(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[339],{10549:(e,t,r)=>{"use strict";r.d(t,{$m:()=>d,As:()=>l,nD:()=>i,ub:()=>c});var a=r(95155);r(12115);var n=r(23478),s=r(66474),o=r(59434);function i(e){let{...t}=e;return(0,a.jsx)(n.bL,{"data-slot":"accordion",...t})}function l(e){let{className:t,...r}=e;return(0,a.jsx)(n.q7,{"data-slot":"accordion-item",className:(0,o.cn)("border-b last:border-b-0",t),...r})}function d(e){let{className:t,children:r,...i}=e;return(0,a.jsx)(n.Y9,{className:"flex",children:(0,a.jsxs)(n.l9,{"data-slot":"accordion-trigger",className:(0,o.cn)("focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180",t),...i,children:[r,(0,a.jsx)(s.A,{className:"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200"})]})})}function c(e){let{className:t,children:r,...s}=e;return(0,a.jsx)(n.UC,{"data-slot":"accordion-content",className:"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm",...s,children:(0,a.jsx)("div",{className:(0,o.cn)("pt-0 pb-4",t),children:r})})}},15147:(e,t,r)=>{Promise.resolve().then(r.bind(r,52619))},16183:(e,t,r)=>{"use strict";r.d(t,{Er:()=>l,_1:()=>i,s9:()=>o});var a=r(99090);let n=["subscription"],s=["usage"],o=(0,a.DY)({all:n,details:()=>[...n,"details"]}),i=(0,a.DY)({all:["models"],available:["models","available"]}),l=(0,a.DY)({all:s,logs:(e,t)=>[...s,"logs",{page:e,itemsPerPage:t}]})},26126:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var a=r(95155);r(12115);var n=r(99708),s=r(74466),o=r(59434);let i=(0,s.F)("inline-flex items-center justify-center rounded-lg border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground",new:"text-purple-600 dark:text-purple-300 bg-purple-600/30 dark:bg-purple-600/30",beta:"text-blue-600 dark:text-blue-300 bg-blue-600/30 dark:bg-blue-600/30",highlight:"text-green-800 dark:text-green-300 bg-green-600/30 dark:bg-green-600/30"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,asChild:s=!1,...l}=e,d=s?n.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,o.cn)(i({variant:r}),t),...l})}},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,r:()=>i});var a=r(95155);r(12115);var n=r(99708),s=r(74466),o=r(59434);let i=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",node_outline:"bg-transparent border border-primary/10",node_secondary:"px-0 bg-transparent hover:opacity-60",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-lg gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-lg px-6 has-[>svg]:px-4",icon:"size-9",node_secondary:"px-0"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:s,asChild:l=!1,...d}=e,c=l?n.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,o.cn)(i({variant:r,size:s,className:t})),...d})}},52619:(e,t,r)=>{"use strict";r.d(t,{default:()=>p});var a=r(95155),n=r(12115),s=r(66695),o=r(85127),i=r(10549),l=r(26126),d=r(68856),c=r(30285),u=r(33786),g=r(51154),m=r(6874),h=r.n(m),b=r(33096),x=r(95557);function p(e){let{accountId:t}=e,[r,m]=(0,n.useState)(0),[p,f]=(0,n.useState)([]),[v,j]=(0,n.useState)(!0),{data:y,isLoading:k,error:w,refetch:N}=(0,x._o)(r,1e3);(0,n.useEffect)(()=>{y&&(0===r?f(y.logs||[]):f(e=>[...e,...y.logs||[]]),j(y.has_more||!1))},[y,r]);let T=e=>"string"==typeof e||0===e?"string"==typeof e?e:"$0.0000":"$".concat(e.toFixed(4)),_=e=>new Date(e).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),C=(e,t)=>{let r="/projects/".concat(t,"/thread/").concat(e);window.open(r,"_blank")};if(k&&0===r)return(0,a.jsxs)(s.Zp,{children:[(0,a.jsxs)(s.aR,{children:[(0,a.jsx)(s.ZB,{children:"Usage Logs"}),(0,a.jsx)(s.BT,{children:"Loading your token usage history..."})]}),(0,a.jsx)(s.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:Array.from({length:5}).map((e,t)=>(0,a.jsx)(d.Skeleton,{className:"h-16 w-full"},t))})})]});if(w)return(0,a.jsxs)(s.Zp,{children:[(0,a.jsx)(s.aR,{children:(0,a.jsx)(s.ZB,{children:"Usage Logs"})}),(0,a.jsx)(s.Wu,{children:(0,a.jsx)("div",{className:"p-4 bg-destructive/10 border border-destructive/20 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-destructive",children:["Error: ",w.message||"Failed to load usage logs"]})})})]});if(null==y?void 0:y.message)return(0,a.jsxs)(s.Zp,{children:[(0,a.jsx)(s.aR,{children:(0,a.jsx)(s.ZB,{children:"Usage Logs"})}),(0,a.jsx)(s.Wu,{children:(0,a.jsx)("div",{className:"p-4 bg-muted/30 border border-border rounded-lg text-center",children:(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:y.message})})})]});let A=Object.values(p.reduce((e,t)=>{let r=new Date(t.created_at).toDateString();return e[r]||(e[r]={date:r,logs:[],totalTokens:0,totalCost:0,requestCount:0,models:[]}),e[r].logs.push(t),e[r].totalTokens+=t.total_tokens,e[r].totalCost+="number"==typeof t.estimated_cost?t.estimated_cost:0,e[r].requestCount+=1,e[r].models.includes(t.content.model)||e[r].models.push(t.content.model),e},{})).sort((e,t)=>new Date(t.date).getTime()-new Date(e.date).getTime());return p.reduce((e,t)=>e+("number"==typeof t.estimated_cost?t.estimated_cost:0),0),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)(s.Zp,{children:[(0,a.jsxs)(s.aR,{children:[(0,a.jsx)(s.ZB,{children:"Daily Usage Logs"}),(0,a.jsx)(s.BT,{children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:["Your token usage organized by day, sorted by most recent."," ",(0,a.jsx)(c.$,{variant:"outline",asChild:!0,className:"text-sm ml-4",children:(0,a.jsxs)(h(),{href:"/model-pricing",children:["View Model Pricing ",(0,a.jsx)(b.Ngv,{className:"w-4 h-4"})]})})]})})]}),(0,a.jsx)(s.Wu,{children:0===A.length?(0,a.jsx)("div",{className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"No usage logs found."})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.nD,{type:"single",collapsible:!0,className:"w-full",children:A.map(e=>(0,a.jsxs)(i.As,{value:e.date,children:[(0,a.jsx)(i.$m,{className:"hover:no-underline",children:(0,a.jsxs)("div",{className:"flex justify-between items-center w-full mr-4",children:[(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("div",{className:"font-semibold",children:_(e.date)}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground",children:[e.requestCount," request",1!==e.requestCount?"s":""," •"," ",e.models.join(", ")]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("div",{className:"font-mono font-semibold",children:T(e.totalCost)}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground font-mono",children:[e.totalTokens.toLocaleString()," tokens"]})]})]})}),(0,a.jsx)(i.ub,{children:(0,a.jsx)("div",{className:"rounded-md border mt-4",children:(0,a.jsxs)(o.Table,{children:[(0,a.jsx)(o.A0,{children:(0,a.jsxs)(o.TableRow,{children:[(0,a.jsx)(o.nd,{children:"Time"}),(0,a.jsx)(o.nd,{children:"Model"}),(0,a.jsx)(o.nd,{className:"text-right",children:"Tokens"}),(0,a.jsx)(o.nd,{className:"text-right",children:"Cost"}),(0,a.jsx)(o.nd,{className:"text-center",children:"Thread"})]})}),(0,a.jsx)(o.TableBody,{children:e.logs.map(e=>(0,a.jsxs)(o.TableRow,{children:[(0,a.jsx)(o.TableCell,{className:"font-mono text-sm",children:new Date(e.created_at).toLocaleTimeString()}),(0,a.jsx)(o.TableCell,{children:(0,a.jsx)(l.E,{className:"font-mono text-xs",children:e.content.model})}),(0,a.jsxs)(o.TableCell,{className:"text-right font-mono font-medium text-sm",children:[e.content.usage.prompt_tokens.toLocaleString()," ","->"," ",e.content.usage.completion_tokens.toLocaleString()]}),(0,a.jsx)(o.TableCell,{className:"text-right font-mono font-medium text-sm",children:T(e.estimated_cost)}),(0,a.jsx)(o.TableCell,{className:"text-center",children:(0,a.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>C(e.thread_id,e.project_id),className:"h-8 w-8 p-0",children:(0,a.jsx)(u.A,{className:"h-4 w-4"})})})]},e.message_id))})]})})})]},e.date))}),v&&(0,a.jsx)("div",{className:"flex justify-center pt-6",children:(0,a.jsx)(c.$,{onClick:()=>{m(r+1)},disabled:k,variant:"outline",children:k?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Loading..."]}):"Load More"})})]})})]})})}},59434:(e,t,r)=>{"use strict";r.d(t,{$3:()=>l,Hz:()=>i,W5:()=>d,cn:()=>o});var a=r(52596),n=r(81949),s=r(39688);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}let i=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"rgba(180, 180, 180)";if(!e)return t;try{if("string"==typeof e&&e.startsWith("var(")){let t=document.createElement("div");t.style.color=e,document.body.appendChild(t);let r=window.getComputedStyle(t).color;return document.body.removeChild(t),n.formatRGBA(n.parse(r))}return n.formatRGBA(n.parse(e))}catch(e){return console.error("Color parsing failed:",e),t}},l=(e,t)=>e.startsWith("rgb")?n.formatRGBA(n.alpha(n.parse(e),t)):e;function d(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return e.length<=t?e:e.slice(0,t)+"..."}},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>i,Zp:()=>s,aR:()=>o,wL:()=>c});var a=r(95155);r(12115);var n=r(59434);function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r})}},68856:(e,t,r)=>{"use strict";r.d(t,{Skeleton:()=>i});var a=r(95155),n=r(11518),s=r.n(n),o=r(59434);function i(e){let{className:t,...r}=e;return(0,a.jsxs)("div",{...r,className:"jsx-1e687dde282a8b11 "+(r&&null!=r.className&&r.className||(0,o.cn)("relative overflow-hidden rounded-md","bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10","background-animate",t)||""),children:[(0,a.jsx)("div",{className:"jsx-1e687dde282a8b11 shimmer-wrapper",children:(0,a.jsx)("div",{className:"jsx-1e687dde282a8b11 shimmer"})}),(0,a.jsx)(s(),{id:"1e687dde282a8b11",children:".background-animate.jsx-1e687dde282a8b11{-webkit-background-size:200%200%;-moz-background-size:200%200%;-o-background-size:200%200%;background-size:200%200%;-webkit-animation:gradientAnimation 1s ease infinite;-moz-animation:gradientAnimation 1s ease infinite;-o-animation:gradientAnimation 1s ease infinite;animation:gradientAnimation 1s ease infinite}@-webkit-keyframes gradientAnimation{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}@-moz-keyframes gradientAnimation{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}@-o-keyframes gradientAnimation{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}@keyframes gradientAnimation{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}.shimmer-wrapper.jsx-1e687dde282a8b11{position:absolute;top:0;left:0;width:100%;height:100%;overflow:hidden}.shimmer.jsx-1e687dde282a8b11{width:50%;height:100%;background:-webkit-linear-gradient(left,rgba(255,255,255,0)0%,rgba(255,255,255,.3)50%,rgba(255,255,255,0)100%);background:-moz-linear-gradient(left,rgba(255,255,255,0)0%,rgba(255,255,255,.3)50%,rgba(255,255,255,0)100%);background:-o-linear-gradient(left,rgba(255,255,255,0)0%,rgba(255,255,255,.3)50%,rgba(255,255,255,0)100%);background:linear-gradient(90deg,rgba(255,255,255,0)0%,rgba(255,255,255,.3)50%,rgba(255,255,255,0)100%);-webkit-animation:shimmerAnimation 1s infinite;-moz-animation:shimmerAnimation 1s infinite;-o-animation:shimmerAnimation 1s infinite;animation:shimmerAnimation 1s infinite;position:absolute;top:0;left:-150%}@-webkit-keyframes shimmerAnimation{to{left:150%}}@-moz-keyframes shimmerAnimation{to{left:150%}}@-o-keyframes shimmerAnimation{to{left:150%}}@keyframes shimmerAnimation{to{left:150%}}"})]})}r(12115)},85127:(e,t,r)=>{"use strict";r.d(t,{A0:()=>o,Table:()=>s,TableBody:()=>i,TableCell:()=>c,TableRow:()=>l,nd:()=>d});var a=r(95155);r(12115);var n=r(59434);function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,n.cn)("w-full caption-bottom text-sm",t),...r})})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,n.cn)("[&_tr]:border-b",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,n.cn)("[&_tr:last-child]:border-0",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,n.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,n.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,n.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...r})}},90697:(e,t,r)=>{"use strict";r.d(t,{Hv:()=>l,mI:()=>i});var a=r(52643),n=r(33356);let s="http://localhost:8000/api",o={async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{showErrors:r=!0,errorContext:s,timeout:o=3e4,...i}=t;try{let t,l=new AbortController,d=setTimeout(()=>l.abort(),o),c=(0,a.U)(),{data:{session:u}}=await c.auth.getSession(),g={"Content-Type":"application/json",...i.headers};(null==u?void 0:u.access_token)&&(g.Authorization="Bearer ".concat(u.access_token));let m=await fetch(e,{...i,headers:g,signal:l.signal});if(clearTimeout(d),!m.ok){let e=Error("HTTP ".concat(m.status,": ").concat(m.statusText));e.status=m.status,e.response=m;try{let t=await m.json();e.details=t,t.message&&(e.message=t.message)}catch(e){}return r&&(0,n.hS)(e,s),{error:e,success:!1}}let h=m.headers.get("content-type");return{data:(null==h?void 0:h.includes("application/json"))?await m.json():(null==h?void 0:h.includes("text/"))?await m.text():await m.blob(),success:!0}}catch(t){let e=t instanceof Error?t:Error(String(t));return"AbortError"===t.name&&(e.message="Request timeout",e.code="TIMEOUT"),r&&(0,n.nQ)(e,s),{error:e,success:!1}}},get:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return o.request(e,{...t,method:"GET"})},post:async function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return o.request(e,{...r,method:"POST",body:t?JSON.stringify(t):void 0})},put:async function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return o.request(e,{...r,method:"PUT",body:t?JSON.stringify(t):void 0})},patch:async function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return o.request(e,{...r,method:"PATCH",body:t?JSON.stringify(t):void 0})},delete:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return o.request(e,{...t,method:"DELETE"})},upload:async function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{headers:a,...n}=r,s={...a};return delete s["Content-Type"],o.request(e,{...n,method:"POST",body:t,headers:s})}},i={async execute(e,t){try{let{data:r,error:a}=await e();if(a){let e=Error(a.message||"Database error");return e.code=a.code,e.details=a,(0,n.hS)(e,t),{error:e,success:!1}}return{data:r,success:!0}}catch(r){let e=r instanceof Error?r:Error(String(r));return(0,n.hS)(e,t),{error:e,success:!1}}}},l={get:(e,t)=>o.get("".concat(s).concat(e),t),post:(e,t,r)=>o.post("".concat(s).concat(e),t,r),put:(e,t,r)=>o.put("".concat(s).concat(e),t,r),patch:(e,t,r)=>o.patch("".concat(s).concat(e),t,r),delete:(e,t)=>o.delete("".concat(s).concat(e),t),upload:(e,t,r)=>o.upload("".concat(s).concat(e),t,r)}},95557:(e,t,r)=>{"use strict";r.d(t,{$x:()=>l,_o:()=>d});var a=r(99090),n=r(25731);r(52643);var s=r(90697);r(33356);let o={getSubscription:async()=>(await s.Hv.get("/billing/subscription",{errorContext:{operation:"load subscription",resource:"billing information"}})).data||null,checkStatus:async()=>(await s.Hv.get("/billing/status",{errorContext:{operation:"check billing status",resource:"account status"}})).data||null,createCheckoutSession:async e=>(await s.Hv.post("/billing/create-checkout-session",e,{errorContext:{operation:"create checkout session",resource:"billing"}})).data||null,createPortalSession:async e=>(await s.Hv.post("/billing/create-portal-session",e,{errorContext:{operation:"create portal session",resource:"billing portal"}})).data||null,getAvailableModels:async()=>(await s.Hv.get("/billing/available-models",{errorContext:{operation:"load available models",resource:"AI models"}})).data||null,async getUsageLogs(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3;return(await s.Hv.get("/billing/usage-logs?page=".concat(e,"&items_per_page=").concat(t),{errorContext:{operation:"load usage logs",resource:"usage history"}})).data||null}};var i=r(16183);let l=(0,a.GQ)(i._1.available,n.cL,{staleTime:6e5,refetchOnWindowFocus:!1});(0,a.GQ)(["billing","status"],n.Et,{staleTime:12e4,refetchOnWindowFocus:!0}),(0,a.Lx)(e=>(0,n.fw)(e),{onSuccess:e=>{e.url&&(window.location.href=e.url)},errorContext:{operation:"create checkout session",resource:"billing"}});let d=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3;return(0,a.GQ)(i.Er.logs(e,t),()=>o.getUsageLogs(e,t),{staleTime:3e4,refetchOnMount:!0,refetchOnWindowFocus:!1})()}},99090:(e,t,r)=>{"use strict";r.d(t,{DY:()=>o,GQ:()=>i,Lx:()=>l});var a=r(28755),n=r(5041),s=r(33356);let o=e=>e;function i(e,t,r){return n=>(0,a.I)({queryKey:e,queryFn:t,...r,...n})}function l(e,t){return r=>{let{errorContext:a,...o}=t||{},{errorContext:i,...l}=r||{};return(0,n.n)({mutationFn:e,onError:(e,t,r)=>{var n,d;(null==l?void 0:l.onError)||(null==o?void 0:o.onError)||(0,s.hS)(e,i||a),null==o||null==(n=o.onError)||n.call(o,e,t,r),null==l||null==(d=l.onError)||d.call(l,e,t,r)},...o,...l})}}}},e=>{var t=t=>e(e.s=t);e.O(0,[5105,2969,1935,6671,3860,6874,1171,8341,6165,5952,6686,8441,1684,7358],()=>t(15147)),_N_E=e.O()}]);