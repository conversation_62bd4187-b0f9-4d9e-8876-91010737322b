(()=>{var e={};e.id=8660,e.ids=[8660],e.modules={2507:(e,r,t)=>{"use strict";t.d(r,{U:()=>i});var n=t(67218);t(79130);var s=t(45934),o=t(44999),a=t(17478);let i=async()=>{let e=await (0,o.cookies)(),r="";return r&&!r.startsWith("http")&&(r=`http://${r}`),(0,s.createServerClient)(r,"",{cookies:{getAll:()=>e.getAll(),setAll(r){try{r.forEach(({name:r,value:t,options:n})=>e.set({name:r,value:t,...n}))}catch(e){}}}})};(0,a.D)([i]),(0,n.A)(i,"7f8bed79c8654f95685745e61906af37f96a086236",null)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4536:(e,r,t)=>{let{createProxy:n}=t(39844);e.exports=n("C:\\Users\\<USER>\\suna\\frontend\\node_modules\\next\\dist\\client\\app-dir\\link.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},4992:(e,r,t)=>{Promise.resolve().then(t.bind(t,62478))},5527:(e,r,t)=>{Promise.resolve().then(t.bind(t,34806))},6211:(e,r,t)=>{"use strict";t.d(r,{A0:()=>a,Table:()=>o,TableBody:()=>i,TableCell:()=>c,TableRow:()=>l,nd:()=>d});var n=t(60687);t(43210);var s=t(4780);function o({className:e,...r}){return(0,n.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,n.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm",e),...r})})}function a({className:e,...r}){return(0,n.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",e),...r})}function i({className:e,...r}){return(0,n.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",e),...r})}function l({className:e,...r}){return(0,n.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...r})}function d({className:e,...r}){return(0,n.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...r})}function c({className:e,...r}){return(0,n.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...r})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,r,t)=>{"use strict";t.d(r,{cn:()=>o});var n=t(75986),s=t(8974);function o(...e){return(0,s.QP)((0,n.$)(e))}},11576:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l});var n=t(60687),s=t(35950),o=t(85814),a=t.n(o),i=t(16189);function l({children:e}){let r=(0,i.usePathname)();return(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)("div",{className:"space-y-6 w-full",children:[(0,n.jsx)(s.w,{className:"border-subtle dark:border-white/10"}),(0,n.jsxs)("div",{className:"flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0 w-full max-w-7xl mx-auto px-4",children:[(0,n.jsx)("aside",{className:"lg:w-1/4 p-1",children:(0,n.jsx)("nav",{className:"flex flex-col space-y-1",children:[{name:"Billing",href:"/settings/billing"},{name:"Usage Logs",href:"/settings/usage-logs"}].map(e=>(0,n.jsx)(a(),{href:e.href,className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${r===e.href?"bg-accent text-accent-foreground":"text-muted-foreground hover:bg-accent/50 hover:text-accent-foreground"}`,children:e.name},e.href))})}),(0,n.jsx)("div",{className:"flex-1 bg-card-bg dark:bg-background-secondary p-6 rounded-2xl border border-subtle dark:border-white/10 shadow-custom",children:e})]})]})})}},11997:e=>{"use strict";e.exports=require("punycode")},16745:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"7f8bed79c8654f95685745e61906af37f96a086236":()=>n.U});var n=t(2507)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23318:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>l});var n=t(65239),s=t(48088),o=t(31369),a=t(30893),i={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>a[e]);t.d(r,i);let l={children:["",{children:["(dashboard)",{children:["(personalAccount)",{children:["settings",{children:["teams",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,53748)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\settings\\teams\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,34806)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\settings\\layout.tsx"]}]},{loading:[()=>Promise.resolve().then(t.bind(t,62478)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,33532)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,88524))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,93595)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,88524))).default(e)],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\settings\\teams\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/(personalAccount)/settings/teams/page",pathname:"/settings/teams",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33252:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});var n=t(60687);function s(){return(0,n.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,n.jsx)("div",{className:"w-12 h-12 border-4 border-primary rounded-full border-t-transparent animate-spin"})})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34806:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});let n=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\(personalAccount)\\\\settings\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\settings\\layout.tsx","default")},41859:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,4536,23)),Promise.resolve().then(t.bind(t,80401))},51455:e=>{"use strict";e.exports=require("node:fs/promises")},53748:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>P});var n=t(37413),s=t(61120),o=t(10974);function a({className:e,...r}){return(0,n.jsx)("div",{"data-slot":"card",className:(0,o.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...r})}function i({className:e,...r}){return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,o.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...r})}function l({className:e,...r}){return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,o.cn)("leading-none font-semibold",e),...r})}function d({className:e,...r}){return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,o.cn)("text-muted-foreground text-sm",e),...r})}function c({className:e,...r}){return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,o.cn)("px-6",e),...r})}var u=t(2507),p=t(80401);function b(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}var f=function(e){let r=function(e){let r=s.forwardRef((e,r)=>{let{children:t,...n}=e;if(s.isValidElement(t)){var o;let e,a,i=(o=t,(a=(e=Object.getOwnPropertyDescriptor(o.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.ref:(a=(e=Object.getOwnPropertyDescriptor(o,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?o.props.ref:o.props.ref||o.ref),l=function(e,r){let t={...r};for(let n in r){let s=e[n],o=r[n];/^on[A-Z]/.test(n)?s&&o?t[n]=(...e)=>{let r=o(...e);return s(...e),r}:s&&(t[n]=s):"style"===n?t[n]={...s,...o}:"className"===n&&(t[n]=[s,o].filter(Boolean).join(" "))}return{...e,...t}}(n,t.props);return t.type!==s.Fragment&&(l.ref=r?function(...e){return r=>{let t=!1,n=e.map(e=>{let n=b(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():b(e[r],null)}}}}(r,i):i),s.cloneElement(t,l)}return s.Children.count(t)>1?s.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=s.forwardRef((e,t)=>{let{children:o,...a}=e,i=s.Children.toArray(o),l=i.find(m);if(l){let e=l.props.children,o=i.map(r=>r!==l?r:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(r,{...a,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,o):null})}return(0,n.jsx)(r,{...a,ref:t,children:o})});return t.displayName=`${e}.Slot`,t}("Slot"),h=Symbol("radix.slottable");function m(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===h}var x=t(75986);let v=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,g=x.$,y=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return g(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:s,defaultVariants:o}=r,a=Object.keys(s).map(e=>{let r=null==t?void 0:t[e],n=null==o?void 0:o[e];if(null===r)return null;let a=v(r)||v(n);return s[e][a]}),i=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return g(e,a,null==r||null==(n=r.compoundVariants)?void 0:n.reduce((e,r)=>{let{class:t,className:n,...s}=r;return Object.entries(s).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...o,...i}[r]):({...o,...i})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)},w=y("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",node_outline:"bg-transparent border border-primary/10",node_secondary:"px-0 bg-transparent hover:opacity-60",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-lg gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-lg px-6 has-[>svg]:px-4",icon:"size-9",node_secondary:"px-0"}},defaultVariants:{variant:"default",size:"default"}});function C({className:e,variant:r,size:t,asChild:s=!1,...a}){return(0,n.jsx)(s?f:"button",{"data-slot":"button",className:(0,o.cn)(w({variant:r,size:t,className:e})),...a})}var j=t(4536),k=t.n(j);let T=y("inline-flex items-center justify-center rounded-lg border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground",new:"text-purple-600 dark:text-purple-300 bg-purple-600/30 dark:bg-purple-600/30",beta:"text-blue-600 dark:text-blue-300 bg-blue-600/30 dark:bg-blue-600/30",highlight:"text-green-800 dark:text-green-300 bg-green-600/30 dark:bg-green-600/30"}},defaultVariants:{variant:"default"}});function N({className:e,variant:r,asChild:t=!1,...s}){return(0,n.jsx)(t?f:"span",{"data-slot":"badge",className:(0,o.cn)(T({variant:r}),e),...s})}async function _(){let e=await (0,u.U)(),{data:r}=await e.rpc("get_accounts"),t=r?.filter(e=>!1===e.personal_account);return(0,n.jsxs)(a,{className:"border-subtle dark:border-white/10 bg-white dark:bg-background-secondary shadow-none rounded-xl",children:[(0,n.jsxs)(i,{className:"pb-3",children:[(0,n.jsx)(l,{className:"text-base text-card-title",children:"Your Teams"}),(0,n.jsx)(d,{className:"text-foreground/70",children:"Teams you belong to or own"})]}),(0,n.jsx)(c,{children:(0,n.jsx)(p.Table,{children:(0,n.jsx)(p.TableBody,{children:t.map(e=>(0,n.jsxs)(p.TableRow,{className:"hover:bg-hover-bg border-subtle dark:border-white/10",children:[(0,n.jsx)(p.TableCell,{children:(0,n.jsxs)("div",{className:"flex items-center gap-x-2",children:[(0,n.jsx)("span",{className:"font-medium text-card-title",children:e.name}),(0,n.jsx)(N,{variant:"owner"===e.account_role?"default":"outline",className:"owner"===e.account_role?"bg-primary hover:bg-primary/90":"text-foreground/70 border-subtle dark:border-white/10",children:e.is_primary_owner?"Primary Owner":e.account_role})]})}),(0,n.jsx)(p.TableCell,{className:"text-right",children:(0,n.jsx)(C,{variant:"outline",asChild:!0,className:"rounded-lg h-9 border-subtle dark:border-white/10 hover:bg-hover-bg dark:hover:bg-hover-bg-dark",children:(0,n.jsx)(k(),{href:`/${e.slug}`,children:"View"})})})]},e.account_id))})})})]})}async function P(){return(0,n.jsx)("div",{children:(0,n.jsx)(_,{})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},55931:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,85814,23)),Promise.resolve().then(t.bind(t,6211))},57975:e=>{"use strict";e.exports=require("node:util")},58671:(e,r,t)=>{Promise.resolve().then(t.bind(t,11576))},62478:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});let n=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\(personalAccount)\\\\loading.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\loading.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68544:(e,r,t)=>{Promise.resolve().then(t.bind(t,33252))},70400:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"60120624b62a67d0046abca062aae687cbb5ebc44a":()=>n.vI,"602190608cb4aada86562e5e5997e6bb44fbe95e4e":()=>n.$w,"60836a64bf90333e8dead87703a0c5a32e95fa0f8f":()=>n.gj});var n=t(67834)},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80401:(e,r,t)=>{"use strict";t.d(r,{Table:()=>s,TableBody:()=>o,TableCell:()=>i,TableRow:()=>a});var n=t(12907);let s=(0,n.registerClientReference)(function(){throw Error("Attempted to call Table() from the server but Table is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\table.tsx","Table");(0,n.registerClientReference)(function(){throw Error("Attempted to call TableHeader() from the server but TableHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\table.tsx","TableHeader");let o=(0,n.registerClientReference)(function(){throw Error("Attempted to call TableBody() from the server but TableBody is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\table.tsx","TableBody");(0,n.registerClientReference)(function(){throw Error("Attempted to call TableFooter() from the server but TableFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\table.tsx","TableFooter"),(0,n.registerClientReference)(function(){throw Error("Attempted to call TableHead() from the server but TableHead is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\table.tsx","TableHead");let a=(0,n.registerClientReference)(function(){throw Error("Attempted to call TableRow() from the server but TableRow is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\table.tsx","TableRow"),i=(0,n.registerClientReference)(function(){throw Error("Attempted to call TableCell() from the server but TableCell is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\table.tsx","TableCell");(0,n.registerClientReference)(function(){throw Error("Attempted to call TableCaption() from the server but TableCaption is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\table.tsx","TableCaption")},81630:e=>{"use strict";e.exports=require("http")},84297:e=>{"use strict";e.exports=require("async_hooks")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[7719,5193,4267,7096,1265,3530,7156,7976,4257,4017,3667,8188,3806,1841],()=>t(23318));module.exports=n})();