"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2566,7351,9613],{6654:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return s}});let n=r(12115);function s(e,t){let r=(0,n.useRef)(null),s=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=s.current;t&&(s.current=null,t())}else e&&(r.current=i(e,n)),t&&(s.current=i(t,n))},[e,t])}function i(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},17649:(e,t,r)=>{r.d(t,{UC:()=>_,VY:()=>B,ZD:()=>H,ZL:()=>P,bL:()=>L,hE:()=>Q,hJ:()=>N,l9:()=>D,rc:()=>q});var n=r(12115),s=r(46081),i=r(6101),o=r(15452),l=r(85185),a=r(99708),u=r(95155),c="AlertDialog",[d,h]=(0,s.A)(c,[o.Hs]),p=(0,o.Hs)(),f=e=>{let{__scopeAlertDialog:t,...r}=e,n=p(t);return(0,u.jsx)(o.bL,{...n,...r,modal:!0})};f.displayName=c;var y=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,s=p(r);return(0,u.jsx)(o.l9,{...s,...n,ref:t})});y.displayName="AlertDialogTrigger";var v=e=>{let{__scopeAlertDialog:t,...r}=e,n=p(t);return(0,u.jsx)(o.ZL,{...n,...r})};v.displayName="AlertDialogPortal";var b=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,s=p(r);return(0,u.jsx)(o.hJ,{...s,...n,ref:t})});b.displayName="AlertDialogOverlay";var x="AlertDialogContent",[g,m]=d(x),w=(0,a.Dc)("AlertDialogContent"),k=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:s,...a}=e,c=p(r),d=n.useRef(null),h=(0,i.s)(t,d),f=n.useRef(null);return(0,u.jsx)(o.G$,{contentName:x,titleName:C,docsSlug:"alert-dialog",children:(0,u.jsx)(g,{scope:r,cancelRef:f,children:(0,u.jsxs)(o.UC,{role:"alertdialog",...c,...a,ref:h,onOpenAutoFocus:(0,l.m)(a.onOpenAutoFocus,e=>{var t;e.preventDefault(),null==(t=f.current)||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,u.jsx)(w,{children:s}),(0,u.jsx)(T,{contentRef:d})]})})})});k.displayName=x;var C="AlertDialogTitle",R=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,s=p(r);return(0,u.jsx)(o.hE,{...s,...n,ref:t})});R.displayName=C;var E="AlertDialogDescription",j=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,s=p(r);return(0,u.jsx)(o.VY,{...s,...n,ref:t})});j.displayName=E;var A=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,s=p(r);return(0,u.jsx)(o.bm,{...s,...n,ref:t})});A.displayName="AlertDialogAction";var M="AlertDialogCancel",O=n.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,{cancelRef:s}=m(M,r),l=p(r),a=(0,i.s)(t,s);return(0,u.jsx)(o.bm,{...l,...n,ref:a})});O.displayName=M;var T=e=>{let{contentRef:t}=e,r="`".concat(x,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(x,"` by passing a `").concat(E,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(x,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return n.useEffect(()=>{var e;document.getElementById(null==(e=t.current)?void 0:e.getAttribute("aria-describedby"))||console.warn(r)},[r,t]),null},L=f,D=y,P=v,N=b,_=k,q=A,H=O,Q=R,B=j},22432:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},25657:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},38164:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},40968:(e,t,r)=>{r.d(t,{b:()=>l});var n=r(12115),s=r(63655),i=r(95155),o=n.forwardRef((e,t)=>(0,i.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var l=o},53904:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},66516:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},71610:(e,t,r)=>{r.d(t,{E:()=>y});var n=r(12115),s=r(7165),i=r(76347),o=r(25910),l=r(52020);function a(e,t){return e.filter(e=>!t.includes(e))}var u=class extends o.Q{#e;#t;#r;#n;#s;#i;#o;#l;#a=[];constructor(e,t,r){super(),this.#e=e,this.#n=r,this.#r=[],this.#s=[],this.#t=[],this.setQueries(t)}onSubscribe(){1===this.listeners.size&&this.#s.forEach(e=>{e.subscribe(t=>{this.#u(e,t)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#s.forEach(e=>{e.destroy()})}setQueries(e,t){this.#r=e,this.#n=t,s.jG.batch(()=>{let e=this.#s,t=this.#c(this.#r);this.#a=t,t.forEach(e=>e.observer.setOptions(e.defaultedQueryOptions));let r=t.map(e=>e.observer),n=r.map(e=>e.getCurrentResult()),s=r.some((t,r)=>t!==e[r]);(e.length!==r.length||s)&&(this.#s=r,this.#t=n,this.hasListeners()&&(a(e,r).forEach(e=>{e.destroy()}),a(r,e).forEach(e=>{e.subscribe(t=>{this.#u(e,t)})}),this.#d()))})}getCurrentResult(){return this.#t}getQueries(){return this.#s.map(e=>e.getCurrentQuery())}getObservers(){return this.#s}getOptimisticResult(e,t){let r=this.#c(e),n=r.map(e=>e.observer.getOptimisticResult(e.defaultedQueryOptions));return[n,e=>this.#h(e??n,t),()=>this.#p(n,r)]}#p(e,t){return t.map((r,n)=>{let s=e[n];return r.defaultedQueryOptions.notifyOnChangeProps?s:r.observer.trackResult(s,e=>{t.forEach(t=>{t.observer.trackProp(e)})})})}#h(e,t){return t?(this.#i&&this.#t===this.#l&&t===this.#o||(this.#o=t,this.#l=this.#t,this.#i=(0,l.BH)(this.#i,t(e))),this.#i):e}#c(e){let t=new Map(this.#s.map(e=>[e.options.queryHash,e])),r=[];return e.forEach(e=>{let n=this.#e.defaultQueryOptions(e),s=t.get(n.queryHash);s?r.push({defaultedQueryOptions:n,observer:s}):r.push({defaultedQueryOptions:n,observer:new i.$(this.#e,n)})}),r}#u(e,t){let r=this.#s.indexOf(e);-1!==r&&(this.#t=function(e,t,r){let n=e.slice(0);return n[t]=r,n}(this.#t,r,t),this.#d())}#d(){if(this.hasListeners()){let e=this.#i,t=this.#p(this.#t,this.#a);e!==this.#h(t,this.#n?.combine)&&s.jG.batch(()=>{this.listeners.forEach(e=>{e(this.#t)})})}}},c=r(26715),d=r(61581),h=r(80382),p=r(22450),f=r(4791);function y(e,t){let{queries:r,...o}=e,a=(0,c.jE)(t),y=(0,d.w)(),v=(0,h.h)(),b=n.useMemo(()=>r.map(e=>{let t=a.defaultQueryOptions(e);return t._optimisticResults=y?"isRestoring":"optimistic",t}),[r,a,y]);b.forEach(e=>{(0,f.jv)(e),(0,p.LJ)(e,v)}),(0,p.wZ)(v);let[x]=n.useState(()=>new u(a,b,o)),[g,m,w]=x.getOptimisticResult(b,o.combine),k=!y&&!1!==o.subscribed;n.useSyncExternalStore(n.useCallback(e=>k?x.subscribe(s.jG.batchCalls(e)):l.lQ,[x,k]),()=>x.getCurrentResult(),()=>x.getCurrentResult()),n.useEffect(()=>{x.setQueries(b,o)},[b,o,x]);let C=g.some((e,t)=>(0,f.EU)(b[t],e))?g.flatMap((e,t)=>{let r=b[t];if(r){let t=new i.$(a,r);if((0,f.EU)(r,e))return(0,f.iL)(r,t,v);(0,f.nE)(e,y)&&(0,f.iL)(r,t,v)}return[]}):[];if(C.length>0)throw Promise.all(C);let R=g.find((e,t)=>{let r=b[t];return r&&(0,p.$1)({result:e,errorResetBoundary:v,throwOnError:r.throwOnError,query:a.getQueryCache().get(r.queryHash),suspense:r.suspense})});if(null==R?void 0:R.error)throw R.error;return m(w())}},74783:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},77639:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Link2Off",[["path",{d:"M9 17H7A5 5 0 0 1 7 7",key:"10o201"}],["path",{d:"M15 7h2a5 5 0 0 1 4 8",key:"1d3206"}],["line",{x1:"8",x2:"12",y1:"12",y2:"12",key:"rvw6j4"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},85339:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},87004:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("PanelRightOpen",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M15 3v18",key:"14nvp0"}],["path",{d:"m10 15-3-3 3-3",key:"1pgupc"}]])},87489:(e,t,r)=>{r.d(t,{b:()=>u});var n=r(12115),s=r(63655),i=r(95155),o="horizontal",l=["horizontal","vertical"],a=n.forwardRef((e,t)=>{var r;let{decorative:n,orientation:a=o,...u}=e,c=(r=a,l.includes(r))?a:o;return(0,i.jsx)(s.sG.div,{"data-orientation":c,...n?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...u,ref:t})});a.displayName="Separator";var u=a},89613:(e,t,r)=>{r.d(t,{Bc:()=>R,Kq:()=>G,UC:()=>$,ZL:()=>Y,bL:()=>F,i3:()=>J,k$:()=>T,l9:()=>Z});var n=r(12115),s=r(85185),i=r(6101),o=r(46081),l=r(19178),a=r(61285),u=r(35152),c=r(34378),d=r(28905),h=r(63655),p=r(99708),f=r(5845),y=r(2564),v=r(95155),[b,x]=(0,o.A)("Tooltip",[u.Bk]),g=(0,u.Bk)(),m="TooltipProvider",w="tooltip.open",[k,C]=b(m),R=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:s=300,disableHoverableContent:i=!1,children:o}=e,l=n.useRef(!0),a=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,v.jsx)(k,{scope:t,isOpenDelayedRef:l,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),l.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>l.current=!0,s)},[s]),isPointerInTransitRef:a,onPointerInTransitChange:n.useCallback(e=>{a.current=e},[]),disableHoverableContent:i,children:o})};R.displayName=m;var E="Tooltip",[j,A]=b(E),M=e=>{let{__scopeTooltip:t,children:r,open:s,defaultOpen:i,onOpenChange:o,disableHoverableContent:l,delayDuration:c}=e,d=C(E,e.__scopeTooltip),h=g(t),[p,y]=n.useState(null),b=(0,a.B)(),x=n.useRef(0),m=null!=l?l:d.disableHoverableContent,k=null!=c?c:d.delayDuration,R=n.useRef(!1),[A,M]=(0,f.i)({prop:s,defaultProp:null!=i&&i,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(w))):d.onClose(),null==o||o(e)},caller:E}),O=n.useMemo(()=>A?R.current?"delayed-open":"instant-open":"closed",[A]),T=n.useCallback(()=>{window.clearTimeout(x.current),x.current=0,R.current=!1,M(!0)},[M]),L=n.useCallback(()=>{window.clearTimeout(x.current),x.current=0,M(!1)},[M]),D=n.useCallback(()=>{window.clearTimeout(x.current),x.current=window.setTimeout(()=>{R.current=!0,M(!0),x.current=0},k)},[k,M]);return n.useEffect(()=>()=>{x.current&&(window.clearTimeout(x.current),x.current=0)},[]),(0,v.jsx)(u.bL,{...h,children:(0,v.jsx)(j,{scope:t,contentId:b,open:A,stateAttribute:O,trigger:p,onTriggerChange:y,onTriggerEnter:n.useCallback(()=>{d.isOpenDelayedRef.current?D():T()},[d.isOpenDelayedRef,D,T]),onTriggerLeave:n.useCallback(()=>{m?L():(window.clearTimeout(x.current),x.current=0)},[L,m]),onOpen:T,onClose:L,disableHoverableContent:m,children:r})})};M.displayName=E;var O="TooltipTrigger",T=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...o}=e,l=A(O,r),a=C(O,r),c=g(r),d=n.useRef(null),p=(0,i.s)(t,d,l.onTriggerChange),f=n.useRef(!1),y=n.useRef(!1),b=n.useCallback(()=>f.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",b),[b]),(0,v.jsx)(u.Mz,{asChild:!0,...c,children:(0,v.jsx)(h.sG.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...o,ref:p,onPointerMove:(0,s.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(y.current||a.isPointerInTransitRef.current||(l.onTriggerEnter(),y.current=!0))}),onPointerLeave:(0,s.m)(e.onPointerLeave,()=>{l.onTriggerLeave(),y.current=!1}),onPointerDown:(0,s.m)(e.onPointerDown,()=>{l.open&&l.onClose(),f.current=!0,document.addEventListener("pointerup",b,{once:!0})}),onFocus:(0,s.m)(e.onFocus,()=>{f.current||l.onOpen()}),onBlur:(0,s.m)(e.onBlur,l.onClose),onClick:(0,s.m)(e.onClick,l.onClose)})})});T.displayName=O;var L="TooltipPortal",[D,P]=b(L,{forceMount:void 0}),N=e=>{let{__scopeTooltip:t,forceMount:r,children:n,container:s}=e,i=A(L,t);return(0,v.jsx)(D,{scope:t,forceMount:r,children:(0,v.jsx)(d.C,{present:r||i.open,children:(0,v.jsx)(c.Z,{asChild:!0,container:s,children:n})})})};N.displayName=L;var _="TooltipContent",q=n.forwardRef((e,t)=>{let r=P(_,e.__scopeTooltip),{forceMount:n=r.forceMount,side:s="top",...i}=e,o=A(_,e.__scopeTooltip);return(0,v.jsx)(d.C,{present:n||o.open,children:o.disableHoverableContent?(0,v.jsx)(I,{side:s,...i,ref:t}):(0,v.jsx)(H,{side:s,...i,ref:t})})}),H=n.forwardRef((e,t)=>{let r=A(_,e.__scopeTooltip),s=C(_,e.__scopeTooltip),o=n.useRef(null),l=(0,i.s)(t,o),[a,u]=n.useState(null),{trigger:c,onClose:d}=r,h=o.current,{onPointerInTransitChange:p}=s,f=n.useCallback(()=>{u(null),p(!1)},[p]),y=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},s=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),s=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(r,n,s,i)){case i:return"left";case s:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,s),...function(e){let{top:t,right:r,bottom:n,left:s}=e;return[{x:s,y:t},{x:r,y:t},{x:r,y:n},{x:s,y:n}]}(t.getBoundingClientRect())])),p(!0)},[p]);return n.useEffect(()=>()=>f(),[f]),n.useEffect(()=>{if(c&&h){let e=e=>y(e,h),t=e=>y(e,c);return c.addEventListener("pointerleave",e),h.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),h.removeEventListener("pointerleave",t)}}},[c,h,y,f]),n.useEffect(()=>{if(a){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=(null==c?void 0:c.contains(t))||(null==h?void 0:h.contains(t)),s=!function(e,t){let{x:r,y:n}=e,s=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let o=t[e],l=t[i],a=o.x,u=o.y,c=l.x,d=l.y;u>n!=d>n&&r<(c-a)*(n-u)/(d-u)+a&&(s=!s)}return s}(r,a);n?f():s&&(f(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,h,a,d,f]),(0,v.jsx)(I,{...e,ref:l})}),[Q,B]=b(E,{isInside:!1}),S=(0,p.Dc)("TooltipContent"),I=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:s,"aria-label":i,onEscapeKeyDown:o,onPointerDownOutside:a,...c}=e,d=A(_,r),h=g(r),{onClose:p}=d;return n.useEffect(()=>(document.addEventListener(w,p),()=>document.removeEventListener(w,p)),[p]),n.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&p()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,p]),(0,v.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:o,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:p,children:(0,v.jsxs)(u.UC,{"data-state":d.stateAttribute,...h,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,v.jsx)(S,{children:s}),(0,v.jsx)(Q,{scope:r,isInside:!0,children:(0,v.jsx)(y.bL,{id:d.contentId,role:"tooltip",children:i||s})})]})})});q.displayName=_;var U="TooltipArrow",z=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,s=g(r);return B(U,r).isInside?null:(0,v.jsx)(u.i3,{...s,...n,ref:t})});z.displayName=U;var G=R,F=M,Z=T,Y=N,$=q,J=z},99245:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Book",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]])}}]);