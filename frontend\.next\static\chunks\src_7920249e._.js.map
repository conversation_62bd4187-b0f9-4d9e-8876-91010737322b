{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\r\nimport * as Color from 'color-bits';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\n// Helper function to convert any CSS color to rgba\r\nexport const getRGBA = (\r\n  cssColor: React.CSSProperties['color'],\r\n  fallback: string = 'rgba(180, 180, 180)',\r\n): string => {\r\n  if (typeof window === 'undefined') return fallback;\r\n  if (!cssColor) return fallback;\r\n\r\n  try {\r\n    // Handle CSS variables\r\n    if (typeof cssColor === 'string' && cssColor.startsWith('var(')) {\r\n      const element = document.createElement('div');\r\n      element.style.color = cssColor;\r\n      document.body.appendChild(element);\r\n      const computedColor = window.getComputedStyle(element).color;\r\n      document.body.removeChild(element);\r\n      return Color.formatRGBA(Color.parse(computedColor));\r\n    }\r\n\r\n    return Color.formatRGBA(Color.parse(cssColor));\r\n  } catch (e) {\r\n    console.error('Color parsing failed:', e);\r\n    return fallback;\r\n  }\r\n};\r\n\r\n// Helper function to add opacity to an RGB color string\r\nexport const colorWithOpacity = (color: string, opacity: number): string => {\r\n  if (!color.startsWith('rgb')) return color;\r\n  return Color.formatRGBA(Color.alpha(Color.parse(color), opacity));\r\n};\r\n\r\n// Tremor Raw focusInput [v0.0.1]\r\n\r\nexport const focusInput = [\r\n  // base\r\n  'focus:ring-2',\r\n  // ring color\r\n  'focus:ring-blue-200 focus:dark:ring-blue-700/30',\r\n  // border color\r\n  'focus:border-blue-500 focus:dark:border-blue-700',\r\n];\r\n\r\n// Tremor Raw focusRing [v0.0.1]\r\n\r\nexport const focusRing = [\r\n  // base\r\n  'outline outline-offset-2 outline-0 focus-visible:outline-2',\r\n  // outline color\r\n  'outline-blue-500 dark:outline-blue-500',\r\n];\r\n\r\n// Tremor Raw hasErrorInput [v0.0.1]\r\n\r\nexport const hasErrorInput = [\r\n  // base\r\n  'ring-2',\r\n  // border color\r\n  'border-red-500 dark:border-red-700',\r\n  // ring color\r\n  'ring-red-200 dark:ring-red-700/30',\r\n];\r\n\r\nexport function truncateString(str: string, maxLength = 50) {\r\n  if (str.length <= maxLength) return str;\r\n  return str.slice(0, maxLength) + '...';\r\n}\r\n\r\n\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,UAAU,CACrB,UACA,WAAmB,qBAAqB;IAExC,uCAAmC;;IAAe;IAClD,IAAI,CAAC,UAAU,OAAO;IAEtB,IAAI;QACF,uBAAuB;QACvB,IAAI,OAAO,aAAa,YAAY,SAAS,UAAU,CAAC,SAAS;YAC/D,MAAM,UAAU,SAAS,aAAa,CAAC;YACvC,QAAQ,KAAK,CAAC,KAAK,GAAG;YACtB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,MAAM,gBAAgB,OAAO,gBAAgB,CAAC,SAAS,KAAK;YAC5D,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO,CAAA,GAAA,kJAAA,CAAA,aAAgB,AAAD,EAAE,CAAA,GAAA,kJAAA,CAAA,QAAW,AAAD,EAAE;QACtC;QAEA,OAAO,CAAA,GAAA,kJAAA,CAAA,aAAgB,AAAD,EAAE,CAAA,GAAA,kJAAA,CAAA,QAAW,AAAD,EAAE;IACtC,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;IACT;AACF;AAGO,MAAM,mBAAmB,CAAC,OAAe;IAC9C,IAAI,CAAC,MAAM,UAAU,CAAC,QAAQ,OAAO;IACrC,OAAO,CAAA,GAAA,kJAAA,CAAA,aAAgB,AAAD,EAAE,CAAA,GAAA,kJAAA,CAAA,QAAW,AAAD,EAAE,CAAA,GAAA,kJAAA,CAAA,QAAW,AAAD,EAAE,QAAQ;AAC1D;AAIO,MAAM,aAAa;IACxB,OAAO;IACP;IACA,aAAa;IACb;IACA,eAAe;IACf;CACD;AAIM,MAAM,YAAY;IACvB,OAAO;IACP;IACA,gBAAgB;IAChB;CACD;AAIM,MAAM,gBAAgB;IAC3B,OAAO;IACP;IACA,eAAe;IACf;IACA,aAAa;IACb;CACD;AAEM,SAAS,eAAe,GAAW,EAAE,YAAY,EAAE;IACxD,IAAI,IAAI,MAAM,IAAI,WAAW,OAAO;IACpC,OAAO,IAAI,KAAK,CAAC,GAAG,aAAa;AACnC", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>/flickering-grid.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { cn, colorWithOpacity, getRGBA } from '@/lib/utils';\r\nimport React, {\r\n  useCallback,\r\n  useEffect,\r\n  useMemo,\r\n  useRef,\r\n  useState,\r\n} from 'react';\r\n\r\ninterface FlickeringGridProps extends React.HTMLAttributes<HTMLDivElement> {\r\n  squareSize?: number;\r\n  gridGap?: number;\r\n  flickerChance?: number;\r\n  color?: string; // Can be any valid CSS color including hex, rgb, rgba, hsl, var(--color)\r\n  width?: number;\r\n  height?: number;\r\n  className?: string;\r\n  maxOpacity?: number;\r\n  text?: string;\r\n  textColor?: string;\r\n  fontSize?: number;\r\n  fontWeight?: number | string;\r\n}\r\n\r\nexport const FlickeringGrid: React.FC<FlickeringGridProps> = ({\r\n  squareSize = 3,\r\n  gridGap = 3,\r\n  flickerChance = 0.2,\r\n  color = '#B4B4B4',\r\n  width,\r\n  height,\r\n  className,\r\n  maxOpacity = 0.15,\r\n  text = '',\r\n  fontSize = 140,\r\n  fontWeight = 600,\r\n  ...props\r\n}) => {\r\n  const canvasRef = useRef<HTMLCanvasElement>(null);\r\n  const containerRef = useRef<HTMLDivElement>(null);\r\n  const animationRef = useRef<number>(0);\r\n  const lastRenderTimeRef = useRef<number>(0);\r\n  const lastResizeTimeRef = useRef<number>(0);\r\n  const gridParamsRef = useRef<any>(null);\r\n  const [isInView, setIsInView] = useState(false);\r\n  const [canvasSize, setCanvasSize] = useState({ width: 0, height: 0 });\r\n\r\n  // Throttle rendering to improve performance - adjust ms as needed\r\n  const FRAME_THROTTLE = 50; // Only render every ~50ms (20fps instead of 60fps)\r\n  const RESIZE_THROTTLE = 200; // Throttle resize events\r\n\r\n  // Convert any CSS color to rgba for optimal canvas performance\r\n  const memoizedColor = useMemo(() => {\r\n    return getRGBA(color);\r\n  }, [color]);\r\n\r\n  const drawGrid = useCallback(\r\n    (\r\n      ctx: CanvasRenderingContext2D,\r\n      width: number,\r\n      height: number,\r\n      cols: number,\r\n      rows: number,\r\n      squares: Float32Array,\r\n      dpr: number,\r\n    ) => {\r\n      ctx.clearRect(0, 0, width, height);\r\n\r\n      // Create a separate canvas for the text mask if needed\r\n      let maskCanvas: HTMLCanvasElement | null = null;\r\n      let maskCtx: CanvasRenderingContext2D | null = null;\r\n\r\n      if (text) {\r\n        maskCanvas = document.createElement('canvas');\r\n        maskCanvas.width = width;\r\n        maskCanvas.height = height;\r\n        maskCtx = maskCanvas.getContext('2d', { willReadFrequently: true });\r\n\r\n        if (maskCtx) {\r\n          // Draw text on mask canvas\r\n          maskCtx.save();\r\n          maskCtx.scale(dpr, dpr);\r\n          maskCtx.fillStyle = 'white';\r\n          maskCtx.font = `${fontWeight} ${fontSize}px \"Geist\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif`;\r\n          maskCtx.textAlign = 'center';\r\n          maskCtx.textBaseline = 'middle';\r\n          maskCtx.fillText(text, width / (2 * dpr), height / (2 * dpr));\r\n          maskCtx.restore();\r\n        }\r\n      }\r\n\r\n      // Batch squares by opacity for better performance\r\n      const opacityMap = new Map<number, { x: number; y: number }[]>();\r\n\r\n      for (let i = 0; i < cols; i++) {\r\n        for (let j = 0; j < rows; j++) {\r\n          const x = i * (squareSize + gridGap) * dpr;\r\n          const y = j * (squareSize + gridGap) * dpr;\r\n          const squareWidth = squareSize * dpr;\r\n          const squareHeight = squareSize * dpr;\r\n\r\n          let hasText = false;\r\n\r\n          if (maskCtx && maskCanvas) {\r\n            const maskData = maskCtx.getImageData(\r\n              x,\r\n              y,\r\n              squareWidth,\r\n              squareHeight,\r\n            ).data;\r\n\r\n            hasText = maskData.some(\r\n              (value, index) => index % 4 === 0 && value > 0,\r\n            );\r\n          }\r\n\r\n          const opacity = squares[i * rows + j];\r\n          const finalOpacity = hasText\r\n            ? Math.min(1, opacity * 3 + 0.4)\r\n            : opacity;\r\n\r\n          // Round opacity to 2 decimal places for batching\r\n          const roundedOpacity = Math.round(finalOpacity * 100) / 100;\r\n\r\n          if (!opacityMap.has(roundedOpacity)) {\r\n            opacityMap.set(roundedOpacity, []);\r\n          }\r\n\r\n          opacityMap.get(roundedOpacity)?.push({ x, y });\r\n        }\r\n      }\r\n\r\n      // Draw squares by opacity batch\r\n      for (const [opacity, squares] of opacityMap.entries()) {\r\n        ctx.fillStyle = colorWithOpacity(memoizedColor, opacity);\r\n\r\n        for (const { x, y } of squares) {\r\n          ctx.fillRect(x, y, squareSize * dpr, squareSize * dpr);\r\n        }\r\n      }\r\n    },\r\n    [memoizedColor, squareSize, gridGap, text, fontSize, fontWeight],\r\n  );\r\n\r\n  const setupCanvas = useCallback(\r\n    (canvas: HTMLCanvasElement, width: number, height: number) => {\r\n      const dpr = window.devicePixelRatio || 1;\r\n      canvas.width = width * dpr;\r\n      canvas.height = height * dpr;\r\n      canvas.style.width = `${width}px`;\r\n      canvas.style.height = `${height}px`;\r\n      const cols = Math.ceil(width / (squareSize + gridGap));\r\n      const rows = Math.ceil(height / (squareSize + gridGap));\r\n\r\n      // Check if we should preserve the existing grid state\r\n      if (\r\n        gridParamsRef.current &&\r\n        gridParamsRef.current.cols === cols &&\r\n        gridParamsRef.current.rows === rows\r\n      ) {\r\n        // Use existing squares array to maintain state\r\n        return {\r\n          cols,\r\n          rows,\r\n          squares: gridParamsRef.current.squares,\r\n          dpr,\r\n        };\r\n      }\r\n\r\n      // Create new squares array only if needed\r\n      const squares = new Float32Array(cols * rows);\r\n      for (let i = 0; i < squares.length; i++) {\r\n        squares[i] = Math.random() * maxOpacity;\r\n      }\r\n\r\n      return { cols, rows, squares, dpr };\r\n    },\r\n    [squareSize, gridGap, maxOpacity],\r\n  );\r\n\r\n  const updateSquares = useCallback(\r\n    (squares: Float32Array, deltaTime: number) => {\r\n      // Only update if flickerChance is greater than 0\r\n      if (flickerChance <= 0) return;\r\n\r\n      for (let i = 0; i < squares.length; i++) {\r\n        if (Math.random() < flickerChance * deltaTime) {\r\n          squares[i] = Math.random() * maxOpacity;\r\n        }\r\n      }\r\n    },\r\n    [flickerChance, maxOpacity],\r\n  );\r\n\r\n  useEffect(() => {\r\n    const canvas = canvasRef.current;\r\n    const container = containerRef.current;\r\n    if (!canvas || !container) return;\r\n\r\n    const ctx = canvas.getContext('2d', { alpha: true });\r\n    if (!ctx) return;\r\n\r\n    const updateCanvasSize = () => {\r\n      const now = performance.now();\r\n      if (now - lastResizeTimeRef.current < RESIZE_THROTTLE) return;\r\n\r\n      lastResizeTimeRef.current = now;\r\n      const newWidth = width || container.clientWidth;\r\n      const newHeight = height || container.clientHeight;\r\n\r\n      // Only update if size changed to prevent unnecessary redraws\r\n      if (canvasSize.width !== newWidth || canvasSize.height !== newHeight) {\r\n        setCanvasSize({ width: newWidth, height: newHeight });\r\n\r\n        // Don't recreate grid if sizes are similar (within 10px)\r\n        const shouldPreserveGrid =\r\n          gridParamsRef.current &&\r\n          Math.abs(\r\n            gridParamsRef.current.cols * (squareSize + gridGap) - newWidth,\r\n          ) < 10 &&\r\n          Math.abs(\r\n            gridParamsRef.current.rows * (squareSize + gridGap) - newHeight,\r\n          ) < 10;\r\n\r\n        if (!shouldPreserveGrid) {\r\n          gridParamsRef.current = setupCanvas(canvas, newWidth, newHeight);\r\n        } else {\r\n          // Just update canvas dimensions without recreating grid\r\n          const dpr = window.devicePixelRatio || 1;\r\n          canvas.width = newWidth * dpr;\r\n          canvas.height = newHeight * dpr;\r\n          canvas.style.width = `${newWidth}px`;\r\n          canvas.style.height = `${newHeight}px`;\r\n        }\r\n      }\r\n    };\r\n\r\n    // Initialize canvas size and grid params if needed\r\n    if (!gridParamsRef.current) {\r\n      updateCanvasSize();\r\n    }\r\n\r\n    let lastTime = 0;\r\n    const animate = (time: number) => {\r\n      if (!isInView) {\r\n        animationRef.current = requestAnimationFrame(animate);\r\n        return;\r\n      }\r\n\r\n      // Throttle to improve performance\r\n      if (time - lastRenderTimeRef.current < FRAME_THROTTLE) {\r\n        animationRef.current = requestAnimationFrame(animate);\r\n        return;\r\n      }\r\n\r\n      // Safety check\r\n      if (!gridParamsRef.current || !gridParamsRef.current.squares) {\r\n        updateCanvasSize();\r\n        animationRef.current = requestAnimationFrame(animate);\r\n        return;\r\n      }\r\n\r\n      lastRenderTimeRef.current = time;\r\n      const deltaTime = (time - lastTime) / 1000;\r\n      lastTime = time;\r\n\r\n      updateSquares(gridParamsRef.current.squares, deltaTime);\r\n      drawGrid(\r\n        ctx,\r\n        canvas.width,\r\n        canvas.height,\r\n        gridParamsRef.current.cols,\r\n        gridParamsRef.current.rows,\r\n        gridParamsRef.current.squares,\r\n        gridParamsRef.current.dpr,\r\n      );\r\n      animationRef.current = requestAnimationFrame(animate);\r\n    };\r\n\r\n    // Use a gentle resize observer that doesn't completely redraw everything\r\n    const resizeObserver = new ResizeObserver(() => {\r\n      const now = performance.now();\r\n      if (now - lastResizeTimeRef.current < RESIZE_THROTTLE) return;\r\n\r\n      const newWidth = width || container.clientWidth;\r\n      const newHeight = height || container.clientHeight;\r\n\r\n      // Only update if dimensions actually changed significantly (at least 5px difference)\r\n      if (\r\n        Math.abs(canvasSize.width - newWidth) > 5 ||\r\n        Math.abs(canvasSize.height - newHeight) > 5\r\n      ) {\r\n        updateCanvasSize();\r\n      }\r\n    });\r\n\r\n    resizeObserver.observe(container);\r\n\r\n    const intersectionObserver = new IntersectionObserver(\r\n      ([entry]) => {\r\n        setIsInView(entry.isIntersecting);\r\n      },\r\n      { threshold: 0.1, rootMargin: '50px' }, // Only activate when 10% visible with margin\r\n    );\r\n\r\n    intersectionObserver.observe(canvas);\r\n\r\n    // Start animation if in view\r\n    if (isInView) {\r\n      animationRef.current = requestAnimationFrame(animate);\r\n    }\r\n\r\n    return () => {\r\n      cancelAnimationFrame(animationRef.current);\r\n      resizeObserver.disconnect();\r\n      intersectionObserver.disconnect();\r\n    };\r\n  }, [\r\n    setupCanvas,\r\n    updateSquares,\r\n    drawGrid,\r\n    width,\r\n    height,\r\n    isInView,\r\n    squareSize,\r\n    gridGap,\r\n  ]);\r\n\r\n  return (\r\n    <div\r\n      ref={containerRef}\r\n      className={cn(`h-full w-full ${className}`)}\r\n      {...props}\r\n    >\r\n      <canvas\r\n        ref={canvasRef}\r\n        className=\"pointer-events-none\"\r\n        style={{\r\n          width: canvasSize.width,\r\n          height: canvasSize.height,\r\n        }}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AA0BO,MAAM,iBAAgD,CAAC,EAC5D,aAAa,CAAC,EACd,UAAU,CAAC,EACX,gBAAgB,GAAG,EACnB,QAAQ,SAAS,EACjB,KAAK,EACL,MAAM,EACN,SAAS,EACT,aAAa,IAAI,EACjB,OAAO,EAAE,EACT,WAAW,GAAG,EACd,aAAa,GAAG,EAChB,GAAG,OACJ;;IACC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU;IACpC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU;IACzC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU;IACzC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAO;IAClC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,OAAO;QAAG,QAAQ;IAAE;IAEnE,kEAAkE;IAClE,MAAM,iBAAiB,IAAI,mDAAmD;IAC9E,MAAM,kBAAkB,KAAK,yBAAyB;IAEtD,+DAA+D;IAC/D,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iDAAE;YAC5B,OAAO,CAAA,GAAA,sHAAA,CAAA,UAAO,AAAD,EAAE;QACjB;gDAAG;QAAC;KAAM;IAEV,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDACzB,CACE,KACA,OACA,QACA,MACA,MACA,SACA;YAEA,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO;YAE3B,uDAAuD;YACvD,IAAI,aAAuC;YAC3C,IAAI,UAA2C;YAE/C,IAAI,MAAM;gBACR,aAAa,SAAS,aAAa,CAAC;gBACpC,WAAW,KAAK,GAAG;gBACnB,WAAW,MAAM,GAAG;gBACpB,UAAU,WAAW,UAAU,CAAC,MAAM;oBAAE,oBAAoB;gBAAK;gBAEjE,IAAI,SAAS;oBACX,2BAA2B;oBAC3B,QAAQ,IAAI;oBACZ,QAAQ,KAAK,CAAC,KAAK;oBACnB,QAAQ,SAAS,GAAG;oBACpB,QAAQ,IAAI,GAAG,GAAG,WAAW,CAAC,EAAE,SAAS,6EAA6E,CAAC;oBACvH,QAAQ,SAAS,GAAG;oBACpB,QAAQ,YAAY,GAAG;oBACvB,QAAQ,QAAQ,CAAC,MAAM,QAAQ,CAAC,IAAI,GAAG,GAAG,SAAS,CAAC,IAAI,GAAG;oBAC3D,QAAQ,OAAO;gBACjB;YACF;YAEA,kDAAkD;YAClD,MAAM,aAAa,IAAI;YAEvB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;gBAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;oBAC7B,MAAM,IAAI,IAAI,CAAC,aAAa,OAAO,IAAI;oBACvC,MAAM,IAAI,IAAI,CAAC,aAAa,OAAO,IAAI;oBACvC,MAAM,cAAc,aAAa;oBACjC,MAAM,eAAe,aAAa;oBAElC,IAAI,UAAU;oBAEd,IAAI,WAAW,YAAY;wBACzB,MAAM,WAAW,QAAQ,YAAY,CACnC,GACA,GACA,aACA,cACA,IAAI;wBAEN,UAAU,SAAS,IAAI;oEACrB,CAAC,OAAO,QAAU,QAAQ,MAAM,KAAK,QAAQ;;oBAEjD;oBAEA,MAAM,UAAU,OAAO,CAAC,IAAI,OAAO,EAAE;oBACrC,MAAM,eAAe,UACjB,KAAK,GAAG,CAAC,GAAG,UAAU,IAAI,OAC1B;oBAEJ,iDAAiD;oBACjD,MAAM,iBAAiB,KAAK,KAAK,CAAC,eAAe,OAAO;oBAExD,IAAI,CAAC,WAAW,GAAG,CAAC,iBAAiB;wBACnC,WAAW,GAAG,CAAC,gBAAgB,EAAE;oBACnC;oBAEA,WAAW,GAAG,CAAC,iBAAiB,KAAK;wBAAE;wBAAG;oBAAE;gBAC9C;YACF;YAEA,gCAAgC;YAChC,KAAK,MAAM,CAAC,SAAS,QAAQ,IAAI,WAAW,OAAO,GAAI;gBACrD,IAAI,SAAS,GAAG,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,eAAe;gBAEhD,KAAK,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,QAAS;oBAC9B,IAAI,QAAQ,CAAC,GAAG,GAAG,aAAa,KAAK,aAAa;gBACpD;YACF;QACF;+CACA;QAAC;QAAe;QAAY;QAAS;QAAM;QAAU;KAAW;IAGlE,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAC5B,CAAC,QAA2B,OAAe;YACzC,MAAM,MAAM,OAAO,gBAAgB,IAAI;YACvC,OAAO,KAAK,GAAG,QAAQ;YACvB,OAAO,MAAM,GAAG,SAAS;YACzB,OAAO,KAAK,CAAC,KAAK,GAAG,GAAG,MAAM,EAAE,CAAC;YACjC,OAAO,KAAK,CAAC,MAAM,GAAG,GAAG,OAAO,EAAE,CAAC;YACnC,MAAM,OAAO,KAAK,IAAI,CAAC,QAAQ,CAAC,aAAa,OAAO;YACpD,MAAM,OAAO,KAAK,IAAI,CAAC,SAAS,CAAC,aAAa,OAAO;YAErD,sDAAsD;YACtD,IACE,cAAc,OAAO,IACrB,cAAc,OAAO,CAAC,IAAI,KAAK,QAC/B,cAAc,OAAO,CAAC,IAAI,KAAK,MAC/B;gBACA,+CAA+C;gBAC/C,OAAO;oBACL;oBACA;oBACA,SAAS,cAAc,OAAO,CAAC,OAAO;oBACtC;gBACF;YACF;YAEA,0CAA0C;YAC1C,MAAM,UAAU,IAAI,aAAa,OAAO;YACxC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;gBACvC,OAAO,CAAC,EAAE,GAAG,KAAK,MAAM,KAAK;YAC/B;YAEA,OAAO;gBAAE;gBAAM;gBAAM;gBAAS;YAAI;QACpC;kDACA;QAAC;QAAY;QAAS;KAAW;IAGnC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAC9B,CAAC,SAAuB;YACtB,iDAAiD;YACjD,IAAI,iBAAiB,GAAG;YAExB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;gBACvC,IAAI,KAAK,MAAM,KAAK,gBAAgB,WAAW;oBAC7C,OAAO,CAAC,EAAE,GAAG,KAAK,MAAM,KAAK;gBAC/B;YACF;QACF;oDACA;QAAC;QAAe;KAAW;IAG7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,SAAS,UAAU,OAAO;YAChC,MAAM,YAAY,aAAa,OAAO;YACtC,IAAI,CAAC,UAAU,CAAC,WAAW;YAE3B,MAAM,MAAM,OAAO,UAAU,CAAC,MAAM;gBAAE,OAAO;YAAK;YAClD,IAAI,CAAC,KAAK;YAEV,MAAM;6DAAmB;oBACvB,MAAM,MAAM,YAAY,GAAG;oBAC3B,IAAI,MAAM,kBAAkB,OAAO,GAAG,iBAAiB;oBAEvD,kBAAkB,OAAO,GAAG;oBAC5B,MAAM,WAAW,SAAS,UAAU,WAAW;oBAC/C,MAAM,YAAY,UAAU,UAAU,YAAY;oBAElD,6DAA6D;oBAC7D,IAAI,WAAW,KAAK,KAAK,YAAY,WAAW,MAAM,KAAK,WAAW;wBACpE,cAAc;4BAAE,OAAO;4BAAU,QAAQ;wBAAU;wBAEnD,yDAAyD;wBACzD,MAAM,qBACJ,cAAc,OAAO,IACrB,KAAK,GAAG,CACN,cAAc,OAAO,CAAC,IAAI,GAAG,CAAC,aAAa,OAAO,IAAI,YACpD,MACJ,KAAK,GAAG,CACN,cAAc,OAAO,CAAC,IAAI,GAAG,CAAC,aAAa,OAAO,IAAI,aACpD;wBAEN,IAAI,CAAC,oBAAoB;4BACvB,cAAc,OAAO,GAAG,YAAY,QAAQ,UAAU;wBACxD,OAAO;4BACL,wDAAwD;4BACxD,MAAM,MAAM,OAAO,gBAAgB,IAAI;4BACvC,OAAO,KAAK,GAAG,WAAW;4BAC1B,OAAO,MAAM,GAAG,YAAY;4BAC5B,OAAO,KAAK,CAAC,KAAK,GAAG,GAAG,SAAS,EAAE,CAAC;4BACpC,OAAO,KAAK,CAAC,MAAM,GAAG,GAAG,UAAU,EAAE,CAAC;wBACxC;oBACF;gBACF;;YAEA,mDAAmD;YACnD,IAAI,CAAC,cAAc,OAAO,EAAE;gBAC1B;YACF;YAEA,IAAI,WAAW;YACf,MAAM;oDAAU,CAAC;oBACf,IAAI,CAAC,UAAU;wBACb,aAAa,OAAO,GAAG,sBAAsB;wBAC7C;oBACF;oBAEA,kCAAkC;oBAClC,IAAI,OAAO,kBAAkB,OAAO,GAAG,gBAAgB;wBACrD,aAAa,OAAO,GAAG,sBAAsB;wBAC7C;oBACF;oBAEA,eAAe;oBACf,IAAI,CAAC,cAAc,OAAO,IAAI,CAAC,cAAc,OAAO,CAAC,OAAO,EAAE;wBAC5D;wBACA,aAAa,OAAO,GAAG,sBAAsB;wBAC7C;oBACF;oBAEA,kBAAkB,OAAO,GAAG;oBAC5B,MAAM,YAAY,CAAC,OAAO,QAAQ,IAAI;oBACtC,WAAW;oBAEX,cAAc,cAAc,OAAO,CAAC,OAAO,EAAE;oBAC7C,SACE,KACA,OAAO,KAAK,EACZ,OAAO,MAAM,EACb,cAAc,OAAO,CAAC,IAAI,EAC1B,cAAc,OAAO,CAAC,IAAI,EAC1B,cAAc,OAAO,CAAC,OAAO,EAC7B,cAAc,OAAO,CAAC,GAAG;oBAE3B,aAAa,OAAO,GAAG,sBAAsB;gBAC/C;;YAEA,yEAAyE;YACzE,MAAM,iBAAiB,IAAI;4CAAe;oBACxC,MAAM,MAAM,YAAY,GAAG;oBAC3B,IAAI,MAAM,kBAAkB,OAAO,GAAG,iBAAiB;oBAEvD,MAAM,WAAW,SAAS,UAAU,WAAW;oBAC/C,MAAM,YAAY,UAAU,UAAU,YAAY;oBAElD,qFAAqF;oBACrF,IACE,KAAK,GAAG,CAAC,WAAW,KAAK,GAAG,YAAY,KACxC,KAAK,GAAG,CAAC,WAAW,MAAM,GAAG,aAAa,GAC1C;wBACA;oBACF;gBACF;;YAEA,eAAe,OAAO,CAAC;YAEvB,MAAM,uBAAuB,IAAI;4CAC/B,CAAC,CAAC,MAAM;oBACN,YAAY,MAAM,cAAc;gBAClC;2CACA;gBAAE,WAAW;gBAAK,YAAY;YAAO;YAGvC,qBAAqB,OAAO,CAAC;YAE7B,6BAA6B;YAC7B,IAAI,UAAU;gBACZ,aAAa,OAAO,GAAG,sBAAsB;YAC/C;YAEA;4CAAO;oBACL,qBAAqB,aAAa,OAAO;oBACzC,eAAe,UAAU;oBACzB,qBAAqB,UAAU;gBACjC;;QACF;mCAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAC,cAAc,EAAE,WAAW;QACzC,GAAG,KAAK;kBAET,cAAA,6LAAC;YACC,KAAK;YACL,WAAU;YACV,OAAO;gBACL,OAAO,WAAW,KAAK;gBACvB,QAAQ,WAAW,MAAM;YAC3B;;;;;;;;;;;AAIR;GAhUa;KAAA", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/use-media-query.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\n\r\nexport function useMediaQuery(query: string): boolean {\r\n  const [matches, setMatches] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const media = window.matchMedia(query);\r\n    if (media.matches !== matches) {\r\n      setMatches(media.matches);\r\n    }\r\n\r\n    const listener = () => setMatches(media.matches);\r\n    media.addEventListener('change', listener);\r\n\r\n    return () => media.removeEventListener('change', listener);\r\n  }, [matches, query]);\r\n\r\n  return matches;\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAIO,SAAS,cAAc,KAAa;;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,QAAQ,OAAO,UAAU,CAAC;YAChC,IAAI,MAAM,OAAO,KAAK,SAAS;gBAC7B,WAAW,MAAM,OAAO;YAC1B;YAEA,MAAM;oDAAW,IAAM,WAAW,MAAM,OAAO;;YAC/C,MAAM,gBAAgB,CAAC,UAAU;YAEjC;2CAAO,IAAM,MAAM,mBAAmB,CAAC,UAAU;;QACnD;kCAAG;QAAC;QAAS;KAAM;IAEnB,OAAO;AACT;GAhBgB", "debugId": null}}, {"offset": {"line": 426, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/app/not-found.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport Link from 'next/link';\r\nimport { useEffect, useState, useRef } from 'react';\r\nimport { ArrowLeft } from 'lucide-react';\r\nimport { useScroll } from 'motion/react';\r\nimport { FlickeringGrid } from '@/components/home/<USER>/flickering-grid';\r\nimport { useMediaQuery } from '@/hooks/use-media-query';\r\n\r\nexport default function NotFound() {\r\n  const tablet = useMediaQuery('(max-width: 1024px)');\r\n  const [mounted, setMounted] = useState(false);\r\n  const [isScrolling, setIsScrolling] = useState(false);\r\n  const scrollTimeout = useRef<NodeJS.Timeout | null>(null);\r\n  const { scrollY } = useScroll();\r\n\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  // Detect when scrolling is active to reduce animation complexity\r\n  useEffect(() => {\r\n    const unsubscribe = scrollY.on('change', () => {\r\n      setIsScrolling(true);\r\n\r\n      // Clear any existing timeout\r\n      if (scrollTimeout.current) {\r\n        clearTimeout(scrollTimeout.current);\r\n      }\r\n\r\n      // Set a new timeout\r\n      scrollTimeout.current = setTimeout(() => {\r\n        setIsScrolling(false);\r\n      }, 300); // Wait 300ms after scroll stops\r\n    });\r\n\r\n    return () => {\r\n      unsubscribe();\r\n      if (scrollTimeout.current) {\r\n        clearTimeout(scrollTimeout.current);\r\n      }\r\n    };\r\n  }, [scrollY]);\r\n\r\n  return (\r\n    <section className=\"w-full relative overflow-hidden min-h-screen flex items-center justify-center\">\r\n      <div className=\"relative flex flex-col items-center w-full px-6\">\r\n        {/* Left side flickering grid with gradient fades */}\r\n        <div className=\"absolute left-0 top-0 h-full w-1/3 -z-10 overflow-hidden\">\r\n          {/* Horizontal fade from left to right */}\r\n          <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-background z-10\" />\r\n\r\n          {/* Vertical fade from top */}\r\n          <div className=\"absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10\" />\r\n\r\n          {/* Vertical fade to bottom */}\r\n          <div className=\"absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10\" />\r\n\r\n          <FlickeringGrid\r\n            className=\"h-full w-full\"\r\n            squareSize={mounted && tablet ? 2 : 2.5}\r\n            gridGap={mounted && tablet ? 2 : 2.5}\r\n            color=\"var(--secondary)\"\r\n            maxOpacity={0.4}\r\n            flickerChance={isScrolling ? 0.01 : 0.03}\r\n          />\r\n        </div>\r\n\r\n        {/* Right side flickering grid with gradient fades */}\r\n        <div className=\"absolute right-0 top-0 h-full w-1/3 -z-10 overflow-hidden\">\r\n          {/* Horizontal fade from right to left */}\r\n          <div className=\"absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-background z-10\" />\r\n\r\n          {/* Vertical fade from top */}\r\n          <div className=\"absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10\" />\r\n\r\n          {/* Vertical fade to bottom */}\r\n          <div className=\"absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10\" />\r\n\r\n          <FlickeringGrid\r\n            className=\"h-full w-full\"\r\n            squareSize={mounted && tablet ? 2 : 2.5}\r\n            gridGap={mounted && tablet ? 2 : 2.5}\r\n            color=\"var(--secondary)\"\r\n            maxOpacity={0.4}\r\n            flickerChance={isScrolling ? 0.01 : 0.03}\r\n          />\r\n        </div>\r\n\r\n        {/* Center content background with rounded bottom */}\r\n        <div className=\"absolute inset-x-1/4 top-0 h-full -z-20 bg-background rounded-b-xl\"></div>\r\n\r\n        <div className=\"relative z-10 max-w-3xl mx-auto h-full w-full flex flex-col gap-10 items-center justify-center\">\r\n          <div className=\"inline-flex h-10 w-fit items-center justify-center gap-2 rounded-full bg-secondary/10 text-secondary px-4\">\r\n            <span className=\"text-sm font-medium\">404 Error</span>\r\n          </div>\r\n\r\n          <div className=\"flex flex-col items-center justify-center gap-5\">\r\n            <h1 className=\"text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-medium tracking-tighter text-balance text-center text-primary\">\r\n              Page not found\r\n            </h1>\r\n            <p className=\"text-base md:text-lg text-center text-muted-foreground font-medium text-balance leading-relaxed tracking-tight\">\r\n              The page you're looking for doesn't exist or has been moved.\r\n            </p>\r\n          </div>\r\n          <div className=\"flex items-center w-full max-w-xl gap-2 flex-wrap justify-center\">\r\n            <Link\r\n              href=\"/\"\r\n              className=\"inline-flex h-12 md:h-14 items-center justify-center gap-2 rounded-full bg-primary text-white px-6 shadow-md hover:bg-primary/90 transition-all duration-200\"\r\n            >\r\n              <ArrowLeft className=\"size-4 md:size-5 dark:text-black\" />\r\n              <span className=\"font-medium dark:text-black\">Return Home</span>\r\n            </Link>\r\n          </div>\r\n\r\n          {/* Subtle glow effect */}\r\n          <div className=\"absolute -bottom-4 inset-x-0 h-6 bg-secondary/20 blur-xl rounded-full -z-10 opacity-70\"></div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAAE;IAC7B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IACpD,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,oMAAA,CAAA,YAAS,AAAD;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,WAAW;QACb;6BAAG,EAAE;IAEL,iEAAiE;IACjE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,cAAc,QAAQ,EAAE,CAAC;kDAAU;oBACvC,eAAe;oBAEf,6BAA6B;oBAC7B,IAAI,cAAc,OAAO,EAAE;wBACzB,aAAa,cAAc,OAAO;oBACpC;oBAEA,oBAAoB;oBACpB,cAAc,OAAO,GAAG;0DAAW;4BACjC,eAAe;wBACjB;yDAAG,MAAM,gCAAgC;gBAC3C;;YAEA;sCAAO;oBACL;oBACA,IAAI,cAAc,OAAO,EAAE;wBACzB,aAAa,cAAc,OAAO;oBACpC;gBACF;;QACF;6BAAG;QAAC;KAAQ;IAEZ,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;;;;;;sCAEf,6LAAC,yJAAA,CAAA,iBAAc;4BACb,WAAU;4BACV,YAAY,WAAW,SAAS,IAAI;4BACpC,SAAS,WAAW,SAAS,IAAI;4BACjC,OAAM;4BACN,YAAY;4BACZ,eAAe,cAAc,OAAO;;;;;;;;;;;;8BAKxC,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;;;;;;sCAEf,6LAAC,yJAAA,CAAA,iBAAc;4BACb,WAAU;4BACV,YAAY,WAAW,SAAS,IAAI;4BACpC,SAAS,WAAW,SAAS,IAAI;4BACjC,OAAM;4BACN,YAAY;4BACZ,eAAe,cAAc,OAAO;;;;;;;;;;;;8BAKxC,6LAAC;oBAAI,WAAU;;;;;;8BAEf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CAAsB;;;;;;;;;;;sCAGxC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAkH;;;;;;8CAGhI,6LAAC;oCAAE,WAAU;8CAAiH;;;;;;;;;;;;sCAIhI,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,6LAAC;wCAAK,WAAU;kDAA8B;;;;;;;;;;;;;;;;;sCAKlD,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKzB;GAhHwB;;QACP,wIAAA,CAAA,gBAAa;QAIR,oMAAA,CAAA,YAAS;;;KALP", "debugId": null}}]}