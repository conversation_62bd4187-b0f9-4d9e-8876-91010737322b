(()=>{var e={};e.id=1008,e.ids=[1008],e.modules={60:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("ArrowUpRight",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]])},490:(e,t,r)=>{"use strict";r.d(t,{R:()=>i});var s=r(60687),a=r(30474),n=r(10218),l=r(43210);function i({size:e=24}){let{theme:t,systemTheme:r}=(0,n.D)(),[i,o]=(0,l.useState)(!1);return(0,s.jsx)(a.default,{src:"/kortix-symbol.svg",alt:"Kortix",width:e,height:e,className:`${i&&("dark"===t||"system"===t&&"dark"===r)?"invert":""} flex-shrink-0`})}},1708:e=>{"use strict";e.exports=require("node:process")},2507:(e,t,r)=>{"use strict";r.d(t,{U:()=>i});var s=r(67218);r(79130);var a=r(45934),n=r(44999),l=r(17478);let i=async()=>{let e=await (0,n.cookies)(),t="";return t&&!t.startsWith("http")&&(t=`http://${t}`),(0,a.createServerClient)(t,"",{cookies:{getAll:()=>e.getAll(),setAll(t){try{t.forEach(({name:t,value:r,options:s})=>e.set({name:t,value:r,...s}))}catch(e){}}}})};(0,l.D)([i]),(0,s.A)(i,"7f8bed79c8654f95685745e61906af37f96a086236",null)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3589:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},8730:(e,t,r)=>{"use strict";r.d(t,{DX:()=>i,Dc:()=>c,TL:()=>l});var s=r(43210),a=r(98599),n=r(60687);function l(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...n}=e;if(s.isValidElement(r)){var l;let e,i,o=(l=r,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),c=function(e,t){let r={...t};for(let s in t){let a=e[s],n=t[s];/^on[A-Z]/.test(s)?a&&n?r[s]=(...e)=>{let t=n(...e);return a(...e),t}:a&&(r[s]=a):"style"===s?r[s]={...a,...n}:"className"===s&&(r[s]=[a,n].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==s.Fragment&&(c.ref=t?(0,a.t)(t,o):o),s.cloneElement(r,c)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:a,...l}=e,i=s.Children.toArray(a),o=i.find(d);if(o){let e=o.props.children,a=i.map(t=>t!==o?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...l,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,a):null})}return(0,n.jsx)(t,{...l,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var i=l("Slot"),o=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,n.jsx)(n.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=o,t}function d(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},16745:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"7f8bed79c8654f95685745e61906af37f96a086236":()=>s.U});var s=r(2507)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21180:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\share\\\\[threadId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\share\\[threadId]\\page.tsx","default")},22816:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{o:()=>h});var a=r(60687),n=r(4780),l=r(71527),i=r(43210),o=r(35056),c=r(85629),d=r(99592),u=e([d]);d=(u.then?(await u)():u)[0];let m={code:function({className:e,children:t,...r}){if(!r.node?.position?.start.line||r.node?.position?.start.line===r.node?.position?.end.line)return(0,a.jsx)("span",{className:(0,n.cn)("bg-primary-foreground dark:bg-zinc-800 dark:border dark:border-zinc-700 rounded-sm px-1 font-mono text-sm",e),...r,children:t});let s=function(e){if(!e)return"plaintext";let t=e.match(/language-(\w+)/);return t?t[1]:"plaintext"}(e);return(0,a.jsx)(d.NG,{className:"rounded-md overflow-hidden my-4 border border-zinc-200 dark:border-zinc-800 max-w-full min-w-0 w-full",children:(0,a.jsx)(d.sd,{code:t,language:s,className:"text-sm"})})},pre:function({children:e}){return(0,a.jsx)(a.Fragment,{children:e})},ul:function({children:e,...t}){return(0,a.jsx)("ul",{className:"list-disc pl-5 my-2",...t,children:e})},ol:function({children:e,...t}){return(0,a.jsx)("ol",{className:"list-decimal pl-5 my-2",...t,children:e})},li:function({children:e,...t}){return(0,a.jsx)("li",{className:"my-1",...t,children:e})},h1:function({children:e,...t}){return(0,a.jsx)("h1",{className:"text-2xl font-bold my-3",...t,children:e})},h2:function({children:e,...t}){return(0,a.jsx)("h2",{className:"text-xl font-bold my-2",...t,children:e})},h3:function({children:e,...t}){return(0,a.jsx)("h3",{className:"text-lg font-bold my-2",...t,children:e})},blockquote:function({children:e,...t}){return(0,a.jsx)("blockquote",{className:"border-l-4 border-muted pl-4 italic my-2 dark:text-zinc-400 dark:border-zinc-600",...t,children:e})},a:function({children:e,href:t,...r}){return(0,a.jsx)("a",{href:t,className:"text-primary hover:underline dark:text-blue-400",target:"_blank",rel:"noopener noreferrer",...r,children:e})},table:function({children:e,...t}){return(0,a.jsx)("table",{className:"w-full border-collapse my-3 text-sm",...t,children:e})},th:function({children:e,...t}){return(0,a.jsx)("th",{className:"border border-slate-300 dark:border-zinc-700 px-3 py-2 text-left font-semibold bg-slate-100 dark:bg-zinc-800",...t,children:e})},td:function({children:e,...t}){return(0,a.jsx)("td",{className:"border border-slate-300 dark:border-zinc-700 px-3 py-2",...t,children:e})}},p=(0,i.memo)(function({content:e,components:t=m}){return(0,a.jsx)(o.oz,{remarkPlugins:[c.A],components:t,children:e})},function(e,t){return e.content===t.content});p.displayName="MemoizedMarkdownBlock";let h=(0,i.memo)(function({children:e,id:t,className:r,components:s=m}){let o=(0,i.useId)(),c=t??o,d=(0,i.useMemo)(()=>l.xI.lexer(e).map(e=>e.raw),[e]);return(0,a.jsx)("div",{className:(0,n.cn)("prose-code:before:hidden prose-code:after:hidden",r),children:d.map((e,t)=>(0,a.jsx)(p,{content:e,components:s},`${c}-block-${t}`))})});h.displayName="Markdown",s()}catch(e){s(e)}})},24224:(e,t,r)=>{"use strict";r.d(t,{F:()=>l});var s=r(49384);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,n=s.$,l=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return n(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:i}=t,o=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],s=null==i?void 0:i[e];if(null===t)return null;let n=a(t)||a(s);return l[e][n]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return n(e,o,null==t||null==(s=t.compoundVariants)?void 0:s.reduce((e,t)=>{let{class:r,className:s,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...c}[t]):({...i,...c})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33804:(e,t,r)=>{Promise.resolve().then(r.bind(r,58978))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},43649:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},46956:(e,t,r)=>{Promise.resolve().then(r.bind(r,21180))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56527:e=>{"use strict";e.exports=import("shiki")},57800:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},58978:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>g});var a=r(60687),n=r(43210),l=r.n(n),i=r(16189);r(62185);var o=r(52581),c=r(7330),d=r(19477),u=r(96260),m=r(78546),p=r(36990),h=r(26408),x=r(83614);r(75330);var f=e([d,u]);function g({params:e}){let t=l().use(e).threadId,r=(0,i.useRouter)(),[s,f]=(0,n.useState)([]),[g,b]=(0,n.useState)(!0),[v,w]=(0,n.useState)(null),[y,k]=(0,n.useState)(null),[j,N]=(0,n.useState)("idle"),[C,S]=(0,n.useState)(!1),[_,T]=(0,n.useState)([]),[A,P]=(0,n.useState)(0),[R,I]=(0,n.useState)(!1),[$,M]=(0,n.useState)(!1),[E,z]=(0,n.useState)(0),[q,D]=(0,n.useState)(""),[G,O]=(0,n.useState)(null),[U,F]=l().useState(void 0),V=(0,n.useRef)(null);(0,n.useRef)(null),(0,n.useRef)(null);let[W,K]=(0,n.useState)(!1),[L,H]=(0,n.useState)(!1);(0,n.useRef)(!1);let[Z,B]=(0,n.useState)(null),[X,J]=(0,n.useState)(null),[Q,Y]=(0,n.useState)(!1),[ee,et]=(0,n.useState)(""),[er,es]=(0,n.useState)(null),ea=(0,n.useRef)(!1);(0,n.useRef)(!1),(0,n.useRef)(!1);let[en,el]=(0,n.useState)(""),ei=(0,n.useRef)(!1),eo=(0,n.useCallback)(()=>{S(e=>!e)},[]),ec=(0,n.useCallback)(e=>{P(e),console.log(`Tool panel manually set to index ${e}`)},[]),ed=(0,n.useCallback)(e=>{console.log(`[STREAM HANDLER] Received message: ID=${e.message_id}, Type=${e.type}`),e.message_id||console.warn(`[STREAM HANDLER] Received message is missing ID: Type=${e.type}, Content=${e.content?.substring(0,50)}...`),f(t=>t.some(t=>t.message_id===e.message_id)?t.map(t=>t.message_id===e.message_id?e:t):[...t,e]),"tool"===e.type&&I(!1)},[]),eu=(0,n.useCallback)(e=>{switch(console.log(`[PAGE] Hook status changed: ${e}`),e){case"idle":case"completed":case"stopped":case"agent_not_running":N("idle"),k(null),I(!1);break;case"connecting":N("connecting");break;case"streaming":N("running");break;case"error":N("error"),setTimeout(()=>{N("idle"),k(null)},3e3)}},[t]),em=(0,n.useCallback)(e=>{console.error(`[PAGE] Stream hook error: ${e}`),o.oR.error(e,{duration:15e3})},[]),ep=(0,n.useCallback)(()=>{console.log(`[PAGE] Stream hook closed with final status: ${j}`)},[j]);(0,n.useCallback)(e=>{if(!e)return;let t=e.name||e.xml_tag_name||"Unknown Tool",r=t.replace(/_/g,"-").toLowerCase();if(console.log("[STREAM] Received tool call:",r,"(raw:",t,")"),ei.current)return;let s=e.arguments||"",a=s;if(r.includes("command")&&!s.includes("<execute-command>"))a=`<execute-command>${s}</execute-command>`;else if(r.includes("file")||"create-file"===r||"delete-file"===r||"full-file-rewrite"===r){let e=["create-file","delete-file","full-file-rewrite"].find(e=>r===e);if(e)if(s.includes(`<${e}>`)||s.includes("file_path="))a=s;else{let t=s.trim();a=t&&!t.startsWith("<")?`<${e} file_path="${t}">`:`<${e}>${s}</${e}>`}}let n={assistantCall:{name:r,content:a,timestamp:new Date().toISOString()},toolResult:{content:"STREAMING",isSuccess:!0,timestamp:new Date().toISOString()}};T(e=>e.length>0&&e[0].assistantCall.name===r?[{...e[0],assistantCall:{...e[0].assistantCall,content:a}}]:[n]),P(0),S(!0)},[]);let{status:eh,toolCall:ex,error:ef,agentRunId:eg,startStreaming:eb,stopStreaming:ev}=(0,h.Z)({onMessage:ed,onStatusChange:eu,onError:em,onClose:ep},t,f),ew=(0,n.useCallback)((e,t)=>{if(!e){console.warn("Clicked assistant message ID is null. Cannot open side panel."),o.oR.warning("Cannot view details: Assistant message ID is missing.");return}ei.current=!1,console.log("[PAGE] Tool Click Triggered. Assistant Message ID:",e,"Tool Name:",t);let r=_.findIndex(t=>{if(!t.toolResult?.content||"STREAMING"===t.toolResult.content)return!1;let r=s.find(t=>t.message_id===e&&"assistant"===t.type);if(!r)return!1;let a=s.find(e=>{if("tool"!==e.type||!e.metadata)return!1;try{return(0,p.jD)(e.metadata,{}).assistant_message_id===r.message_id}catch{return!1}});return t.assistantCall?.content===r.content&&t.toolResult?.content===a?.content});-1!==r?(console.log(`[PAGE] Found tool call at index ${r} for assistant message ${e}`),F(r),P(r),S(!0),setTimeout(()=>F(void 0),100)):(console.warn(`[PAGE] Could not find matching tool call in toolCalls array for assistant message ID: ${e}`),o.oR.info("Could not find details for this tool call."))},[s,_]),ey=(0,n.useCallback)((e,t)=>{e?es(e):es(null),Y(!0)},[]),{playbackState:ek,renderHeader:ej,renderFloatingControls:eN,renderWelcomeOverlay:eC,togglePlayback:eS,resetPlayback:e_,skipToEnd:eT}=(0,m.I)({messages:s,isSidePanelOpen:C,onToggleSidePanel:eo,toolCalls:_,setCurrentToolIndex:P,onFileViewerOpen:ey,projectName:ee||"Shared Conversation"});return((0,n.useCallback)((e="smooth")=>{!L&&V.current&&V.current.scrollIntoView({behavior:e})},[L]),g&&!ea.current)?(0,a.jsx)(x.y,{isSidePanelOpen:C,showHeader:!0}):v?(0,a.jsx)("div",{className:"flex h-screen",children:(0,a.jsxs)("div",{className:`flex flex-col flex-1 overflow-hidden transition-all duration-200 ease-in-out ${C?"mr-[90%] sm:mr-[450px] md:mr-[500px] lg:mr-[550px] xl:mr-[650px]":""}`,children:[(0,a.jsx)("div",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 relative z-[100]",children:(0,a.jsx)("div",{className:"flex h-14 items-center gap-4 px-4",children:(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsx)("span",{className:"text-foreground font-medium",children:"Shared Conversation"})})})}),(0,a.jsx)("div",{className:"flex flex-1 items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"flex w-full max-w-md flex-col items-center gap-4 rounded-lg border bg-card p-6 text-center",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold text-destructive",children:"Error"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:v}),(0,a.jsx)("button",{className:"rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground",onClick:()=>r.push("/"),children:"Back to Home"})]})})]})}):(0,a.jsxs)("div",{className:"flex h-screen",children:[(0,a.jsxs)("div",{className:`flex flex-col flex-1 overflow-hidden transition-all duration-200 ease-in-out ${C?"mr-[90%] sm:mr-[450px] md:mr-[500px] lg:mr-[550px] xl:mr-[650px]":""}`,children:[ej(),(0,a.jsx)(u.u9,{messages:s,agentStatus:j,handleToolClick:ew,handleOpenFileViewer:ey,readOnly:!0,visibleMessages:ek.visibleMessages,streamingText:ek.streamingText,isStreamingText:ek.isStreamingText,currentToolCall:ek.currentToolCall,sandboxId:X||"",project:Z}),eC(),eN()]}),(0,a.jsx)(d.s,{isOpen:C,onClose:()=>{S(!1),ei.current=!0},toolCalls:_,messages:s,agentStatus:"idle",currentIndex:A,onNavigate:ec,externalNavigateToIndex:U,project:Z,onFileClick:ey}),(0,a.jsx)(c.S,{open:Q,onOpenChange:Y,sandboxId:X||"",initialFilePath:er,project:Z})]})}[d,u]=f.then?(await f)():f,s()}catch(e){s(e)}})},61642:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.default,__next_app__:()=>d,pages:()=>c,routeModule:()=>u,tree:()=>o});var s=r(65239),a=r(48088),n=r(31369),l=r(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(t,i);let o={children:["",{children:["share",{children:["[threadId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21180)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\share\\[threadId]\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,99600)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\share\\[threadId]\\layout.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,88524))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,93595)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,88524))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\suna\\frontend\\src\\app\\share\\[threadId]\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/share/[threadId]/page",pathname:"/share/[threadId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67969:(e,t,r)=>{"use strict";function s(e,[t,r]){return Math.min(r,Math.max(t,e))}r.d(t,{q:()=>s})},73136:e=>{"use strict";e.exports=require("node:url")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},78272:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},78546:(e,t,r)=>{"use strict";r.d(t,{I:()=>h});var s=r(60687),a=r(43210),n=r(29523),l=r(10022),i=r(36058),o=r(97840),c=r(89422),d=r(96882),u=r(85814),m=r.n(u);let p=new Set(["execute-command","create-file","delete-file","full-file-rewrite","str-replace","browser-click-element","browser-close-tab","browser-drag-drop","browser-get-dropdown-options","browser-go-back","browser-input-text","browser-navigate-to","browser-scroll-down","browser-scroll-to-text","browser-scroll-up","browser-select-dropdown-option","browser-send-keys","browser-switch-tab","browser-wait","deploy","ask","complete","crawl-webpage","web-search"]),h=({messages:e,isSidePanelOpen:t,onToggleSidePanel:r,toolCalls:u,setCurrentToolIndex:h,onFileViewerOpen:x,projectName:f="Shared Conversation"})=>{let[g,b]=(0,a.useState)({isPlaying:!1,currentMessageIndex:0,visibleMessages:[],streamingText:"",isStreamingText:!1,currentToolCall:null,toolPlaybackIndex:-1}),{isPlaying:v,currentMessageIndex:w,visibleMessages:y,streamingText:k,isStreamingText:j,currentToolCall:N,toolPlaybackIndex:C}=g,S=(0,a.useCallback)(e=>{b(t=>({...t,...e}))},[]),_=(0,a.useCallback)(()=>{S({isPlaying:!v}),v||t||r()},[v,t,r]),T=(0,a.useCallback)(()=>{S({isPlaying:!1,currentMessageIndex:0,visibleMessages:[],streamingText:"",isStreamingText:!1,currentToolCall:null,toolPlaybackIndex:-1})},[S]),A=(0,a.useCallback)(()=>{S({isPlaying:!1,currentMessageIndex:e.length,visibleMessages:e,streamingText:"",isStreamingText:!1,currentToolCall:null,toolPlaybackIndex:u.length-1}),u.length>0&&(h(u.length-1),t||r())},[e,u,t,r,h,S]),P=(0,a.useCallback)((s,a)=>{let n;if(!s||!v)return a(),()=>{};S({isStreamingText:!0,streamingText:""});let l=/<([a-zA-Z\-_]+)(?:\s+[^>]*)?>(?:[\s\S]*?)<\/\1>|<([a-zA-Z\-_]+)(?:\s+[^>]*)?\/>/g,i=[],o=0;for(;null!==(n=l.exec(s));){n.index>o&&i.push({text:s.substring(o,n.index),isTool:!1});let e=n[1]||n[2];i.push({text:n[0],isTool:!0,toolName:e}),o=l.lastIndex}o<s.length&&i.push({text:s.substring(o),isTool:!1});let c=0,d=0,u="",m=!1,x=()=>{if(!v||m)return void setTimeout(x,100);if(d>=i.length){S({isStreamingText:!1});let t=e[w],r=y[y.length-1];r?.message_id===t.message_id?S({visibleMessages:[...y.slice(0,-1),t]}):S({visibleMessages:[...y,t]}),a();return}let s=i[d];if(s.isTool&&0===c&&s.toolName&&p.has(s.toolName)){S({currentToolCall:{name:s.toolName,arguments:s.text,xml_tag_name:s.toolName},toolPlaybackIndex:C+1}),t||r(),h(C+1),m=!0,setTimeout(()=>{m=!1,S({currentToolCall:null}),d++,c=0,x()},500);return}if(c<s.text.length){let e=5,t=s.text[c];e=".!?,;:".includes(t)?5+100*Math.random()+50:5+5*Math.random(),S({streamingText:u+=s.text[c]}),c++,setTimeout(x,e)}else d++,c=0,x()};return x(),()=>{S({isStreamingText:!1,streamingText:""}),m=!0}},[v,e,w,C,h,t,r,S,y]);(0,a.useEffect)(()=>{let t,r;if(v&&0!==e.length)return t=setTimeout(async()=>{if(w>=e.length)return void S({isPlaying:!1});let t=e[w];if(console.log(`Playing message ${w}:`,t.type,t.message_id),"assistant"===t.type)try{let e=t.content;try{let t=JSON.parse(e);t.content&&(e=t.content)}catch(e){}await new Promise(t=>{r=P(e,t)})}catch(e){console.error("Error streaming message:",e)}else S({visibleMessages:[...y,t]}),await new Promise(e=>setTimeout(e,500));S({currentMessageIndex:w+1})},500),()=>{clearTimeout(t),r&&r()}},[v,w,e,P,S,y]);let R=t?"left-1/2 -translate-x-1/4 sm:left-[calc(50%-225px)] md:left-[calc(50%-250px)] lg:left-[calc(50%-275px)] xl:left-[calc(50%-325px)]":"left-1/2 -translate-x-1/2",I=(0,a.useCallback)(()=>(0,s.jsx)("div",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 relative z-[50]",children:(0,s.jsxs)("div",{className:"flex h-14 items-center gap-4 px-4",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-md overflow-hidden bg-primary/10",children:(0,s.jsx)(m(),{href:"/",children:(0,s.jsx)("img",{src:"/kortix-symbol.svg",alt:"Kortix",width:16,height:16,className:"object-contain"})})}),(0,s.jsx)("h1",{children:(0,s.jsx)("span",{className:"font-medium text-foreground",children:f})})]})}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(n.$,{variant:"ghost",size:"icon",onClick:x,className:"h-8 w-8","aria-label":"View Files",children:(0,s.jsx)(l.A,{className:"h-4 w-4"})}),(0,s.jsx)(n.$,{variant:"ghost",size:"icon",onClick:_,className:"h-8 w-8","aria-label":v?"Pause Replay":"Play Replay",children:v?(0,s.jsx)(i.A,{className:"h-4 w-4"}):(0,s.jsx)(o.A,{className:"h-4 w-4"})}),(0,s.jsx)(n.$,{variant:"ghost",size:"icon",onClick:T,className:"h-8 w-8","aria-label":"Restart Replay",children:(0,s.jsx)(c.A,{className:"h-4 w-4 rotate-90"})}),(0,s.jsx)(n.$,{variant:"ghost",size:"icon",onClick:r,className:`h-8 w-8 ${t?"text-primary":""}`,"aria-label":"Toggle Tool Panel",children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})]})]})}),[v,t,x,r,f,T,_]);return{playbackState:g,updatePlaybackState:S,renderHeader:I,renderFloatingControls:(0,a.useCallback)(()=>(0,s.jsx)(s.Fragment,{children:e.length>0&&(0,s.jsx)("div",{className:`fixed bottom-4 z-10 transform bg-background/90 backdrop-blur rounded-full border shadow-md px-3 py-1.5 transition-all duration-200 ${R}`,children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(n.$,{variant:"ghost",size:"icon",onClick:_,className:"h-8 w-8",children:v?(0,s.jsx)(i.A,{className:"h-4 w-4"}):(0,s.jsx)(o.A,{className:"h-4 w-4"})}),(0,s.jsx)("div",{className:"flex items-center text-xs text-muted-foreground",children:(0,s.jsxs)("span",{children:[Math.min(w+ +!j,e.length),"/",e.length]})}),(0,s.jsx)(n.$,{variant:"ghost",size:"icon",onClick:T,className:"h-8 w-8",children:(0,s.jsx)(c.A,{className:"h-4 w-4 rotate-90"})}),(0,s.jsx)(n.$,{variant:"ghost",size:"sm",onClick:A,className:"text-xs",children:"Skip to end"})]})})}),[R,w,v,j,e.length,T,A,_]),renderWelcomeOverlay:(0,a.useCallback)(()=>(0,s.jsx)(s.Fragment,{children:0===y.length&&!k&&!N&&(0,s.jsxs)("div",{className:"fixed inset-0 flex flex-col items-center justify-center",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent dark:from-black/90 dark:via-black/50 dark:to-transparent"}),(0,s.jsxs)("div",{className:"text-center max-w-md mx-auto relative z-10 px-4",children:[(0,s.jsx)("div",{className:"rounded-full bg-primary/10 backdrop-blur-sm w-12 h-12 mx-auto flex items-center justify-center mb-4",children:(0,s.jsx)(o.A,{className:"h-5 w-5 text-primary"})}),(0,s.jsx)("h3",{className:"text-lg font-medium mb-2 text-white",children:"Watch this agent in action"}),(0,s.jsx)("p",{className:"text-sm text-white/80 mb-4",children:"This is a shared view-only agent run. Click play to replay the entire conversation with realistic timing."}),(0,s.jsxs)(n.$,{onClick:_,className:"flex items-center mx-auto bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white border-white/20",size:"lg",variant:"outline",children:[(0,s.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Start Playback"]})]})]})}),[N,k,_,y.length]),togglePlayback:_,resetPlayback:T,skipToEnd:A}}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82120:(e,t,r)=>{"use strict";r.d(t,{a:()=>a});var s=r(43210);function a(){let[e,t]=s.useState(void 0);return s.useEffect(()=>{let e=window.matchMedia("(max-width: 767px)"),r=()=>{t(window.innerWidth<768)};return e.addEventListener("change",r),t(window.innerWidth<768),()=>e.removeEventListener("change",r)},[]),!!e}},83721:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(43210);function a(e){let t=s.useRef({value:e,previous:e});return s.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},98599:(e,t,r)=>{"use strict";r.d(t,{s:()=>l,t:()=>n});var s=r(43210);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function n(...e){return t=>{let r=!1,s=e.map(e=>{let s=a(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():a(e[t],null)}}}}function l(...e){return s.useCallback(n(...e),e)}},99592:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{NG:()=>d,sd:()=>u});var a=r(60687),n=r(4780),l=r(43210),i=r(56527),o=r(10218),c=e([i]);function d({children:e,className:t,...r}){return(0,a.jsx)("div",{className:(0,n.cn)("w-px flex-grow min-w-0 overflow-hidden flex",t),...r,children:e})}function u({code:e,language:t="tsx",theme:r,className:s,...i}){let{resolvedTheme:c}=(0,o.D)(),[d,u]=(0,l.useState)(null),m=(0,n.cn)("[&_pre]:!bg-background/95 [&_pre]:rounded-lg [&_pre]:p-4 [&_pre]:!overflow-x-auto [&_pre]:!w-px [&_pre]:!flex-grow [&_pre]:!min-w-0 [&_pre]:!box-border [&_.shiki]:!overflow-x-auto [&_.shiki]:!w-px [&_.shiki]:!flex-grow [&_.shiki]:!min-w-0 [&_code]:!min-w-0 [&_code]:!whitespace-pre","w-px flex-grow min-w-0 overflow-hidden flex w-full",s);return d?(0,a.jsx)("div",{className:m,dangerouslySetInnerHTML:{__html:d},...i}):(0,a.jsx)("div",{className:m,...i,children:(0,a.jsx)("pre",{className:"!overflow-x-auto !w-px !flex-grow !min-w-0 !box-border",children:(0,a.jsx)("code",{children:e})})})}i=(c.then?(await c)():c)[0],s()}catch(e){s(e)}})},99600:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,generateMetadata:()=>i});var s=r(37413),a=r(2507);let n=async e=>{let t=await (0,a.U)(),{data:r,error:s}=await t.from("threads").select("*").eq("thread_id",e).single();if(s)throw s;return r},l=async e=>{let t=await (0,a.U)();try{let{data:r,error:s}=await t.from("projects").select("*").eq("project_id",e).single();if(console.log("Raw project data from database:",r),s){if("PGRST116"===s.code)throw Error(`Project not found or not accessible: ${e}`);throw s}return console.log("Raw project data from database:",r),{id:r.project_id,name:r.name||"",description:r.description||"",account_id:r.account_id,is_public:r.is_public||!1,created_at:r.created_at,sandbox:r.sandbox||{id:"",pass:"",vnc_preview:"",sandbox_url:""}}}catch(t){throw console.error(`Error fetching project ${e}:`,t),t}};async function i({params:e}){let{threadId:t}=await e,r={title:"Shared Conversation | Kortix Suna",description:"Replay this Agent conversation on Kortix Suna",alternates:{canonical:`http://localhost:3000/share/${t}`},openGraph:{title:"Shared Conversation | Kortix Suna",description:"Replay this Agent conversation on Kortix Suna",images:["http://localhost:3000/share-page/og-fallback.png"]}};try{let e=await n(t),s=await l(e.project_id);if(!e||!s)return r;let a=s.name||"Shared Conversation | Kortix Suna",i=s.description||"Replay this Agent conversation on Kortix Suna",o="http://localhost:3000/share-page/og-fallback.png";return{title:a,description:i,alternates:{canonical:`http://localhost:3000/share/${t}`},openGraph:{title:a,description:i,images:[o]},twitter:{title:a,description:i,images:o,card:"summary_large_image"}}}catch(e){return r}}async function o({children:e}){return(0,s.jsx)(s.Fragment,{children:e})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,5193,4267,1265,3530,6914,4257,5811,3175,3667,8188,9490,8607,846],()=>r(61642));module.exports=s})();