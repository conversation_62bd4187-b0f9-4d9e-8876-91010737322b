"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6955],{12969:(e,t,r)=>{r.d(t,{T:()=>x,w:()=>u});var s=r(95155),l=r(12115),a=r(25789),n=r(93836),i=r(41151),o=r(5710),c=r(66424),d=r(59434),m=r(34513);let u=e=>e?e.replace(/\\u([0-9a-fA-F]{4})/g,(e,t)=>String.fromCharCode(parseInt(t,16))).replace(/\\u([0-9a-fA-F]{8})/g,(e,t)=>String.fromCharCode(parseInt(t.substring(0,4),16),parseInt(t.substring(4,8),16))):"",x=(0,l.forwardRef)((e,t)=>{let{content:r,className:l}=e,x=u(r);return(0,s.jsx)(c.F,{className:(0,d.cn)("w-full h-full rounded-md relative",l),children:(0,s.jsx)("div",{className:"p-4 markdown prose prose-sm dark:prose-invert max-w-none",ref:t,children:(0,s.jsx)(a.oz,{remarkPlugins:[n.A],rehypePlugins:[i.A,o.A],components:{code(e){let{className:t,children:r,...l}=e,a=/language-(\w+)/.exec(t||"");return t&&a?(0,s.jsx)(m.$,{content:String(r).replace(/\n$/,""),language:a?a[1]:""}):(0,s.jsx)("code",{className:t,...l,children:r})},h1:e=>{let{node:t,...r}=e;return(0,s.jsx)("h1",{className:"text-2xl font-bold my-4",...r})},h2:e=>{let{node:t,...r}=e;return(0,s.jsx)("h2",{className:"text-xl font-bold my-3",...r})},h3:e=>{let{node:t,...r}=e;return(0,s.jsx)("h3",{className:"text-lg font-bold my-2",...r})},a:e=>{let{node:t,...r}=e;return(0,s.jsx)("a",{className:"text-primary hover:underline",...r})},p:e=>{let{node:t,...r}=e;return(0,s.jsx)("p",{className:"my-2 font-sans cjk-text",...r})},ul:e=>{let{node:t,...r}=e;return(0,s.jsx)("ul",{className:"list-disc pl-5 my-2",...r})},ol:e=>{let{node:t,...r}=e;return(0,s.jsx)("ol",{className:"list-decimal pl-5 my-2",...r})},li:e=>{let{node:t,...r}=e;return(0,s.jsx)("li",{className:"my-1",...r})},blockquote:e=>{let{node:t,...r}=e;return(0,s.jsx)("blockquote",{className:"border-l-4 border-muted pl-4 italic my-2",...r})},img:e=>{let{node:t,...r}=e;return(0,s.jsx)("img",{className:"max-w-full h-auto rounded-md my-2",...r,alt:r.alt||""})},pre:e=>{let{node:t,...r}=e;return(0,s.jsx)("pre",{className:"p-0 my-2 bg-transparent",...r})},table:e=>{let{node:t,...r}=e;return(0,s.jsx)("table",{className:"w-full border-collapse my-3 text-sm",...r})},th:e=>{let{node:t,...r}=e;return(0,s.jsx)("th",{className:"border border-slate-300 dark:border-zinc-700 px-3 py-2 text-left font-semibold bg-slate-100 dark:bg-zinc-800",...r})},td:e=>{let{node:t,...r}=e;return(0,s.jsx)("td",{className:"border border-slate-300 dark:border-zinc-700 px-3 py-2 cjk-text",...r})}},children:x})})})});x.displayName="MarkdownRenderer"},19363:(e,t,r)=>{r.d(t,{s:()=>t2});var s=r(95155),l=r(18068),a=r(12115),n=r(54073),i=r(59434);function o(e){let{className:t,defaultValue:r,value:l,min:o=0,max:c=100,...d}=e,m=a.useMemo(()=>Array.isArray(l)?l:Array.isArray(r)?r:[o,c],[l,r,o,c]);return(0,s.jsxs)(n.bL,{"data-slot":"slider",defaultValue:r,value:l,min:o,max:c,className:(0,i.cn)("relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",t),...d,children:[(0,s.jsx)(n.CC,{"data-slot":"slider-track",className:(0,i.cn)("bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5"),children:(0,s.jsx)(n.Q6,{"data-slot":"slider-range",className:(0,i.cn)("bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full")})}),Array.from({length:m.length},(e,t)=>(0,s.jsx)(n.zi,{"data-slot":"slider-thumb",className:"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50"},t))]})}var c=r(68856),d=r(74311),m=r(54416),u=r(2645),x=r(66538),h=r(42355),p=r(13052),f=r(40416),g=r(30285),b=r(48313),j=r(40646),v=r(1243),N=r(14186),w=r(62332),k=r(48587),y=r(65112),z=r(42118),S=r(57434),C=r(84209),A=r(73032),E=r(45517),_=r(75761),R=r(14395),L=r(99890),F=r(99856);function T(e){try{let t=JSON.parse(e);if("object"==typeof t)return I(t)}catch(e){}let t=e.match(/<\/?([\w-]+)>/),r=t?t[1]:"unknown",s=!0;if(e.includes("ToolResult")){let t=e.match(/success\s*=\s*(True|False|true|false)/i);t&&(s="true"===t[1].toLowerCase())}return{toolName:r.replace(/_/g,"-"),functionName:r.replace(/-/g,"_"),toolOutput:e,isSuccess:s}}function I(e){var t,r,s,l,a,n,i;if("tool_execution"in e&&"object"==typeof e.tool_execution){let l=e.tool_execution,a=l.function_name||"unknown",n=l.xml_tag_name||"";return{toolName:(n||a).replace(/_/g,"-"),functionName:a,xmlTagName:n||void 0,toolOutput:(null==(t=l.result)?void 0:t.output)||"",isSuccess:(null==(r=l.result)?void 0:r.success)!==!1,arguments:l.arguments,timestamp:null==(s=l.execution_details)?void 0:s.timestamp,toolCallId:l.tool_call_id,summary:e.summary}}if("role"in e&&"content"in e&&"object"==typeof e.content){let t=e.content;if("tool_execution"in t&&"object"==typeof t.tool_execution)return I(t);if("tool_name"in t||"xml_tag_name"in t){let e=(t.tool_name||t.xml_tag_name||"unknown").replace(/_/g,"-");return{toolName:e,functionName:e.replace(/-/g,"_"),toolOutput:(null==(l=t.result)?void 0:l.output)||"",isSuccess:(null==(a=t.result)?void 0:a.success)!==!1}}}if("role"in e&&"content"in e&&"string"==typeof e.content)return T(e.content);if("tool_name"in e||"xml_tag_name"in e){let t=(e.tool_name||e.xml_tag_name||"unknown").replace(/_/g,"-");return{toolName:t,functionName:t.replace(/-/g,"_"),toolOutput:(null==(n=e.result)?void 0:n.output)||"",isSuccess:(null==(i=e.result)?void 0:i.success)!==!1}}return null}function P(e){if(!e)return"";try{let t=new Date(e);return isNaN(t.getTime())?"Invalid date":t.toLocaleString()}catch(e){return"Invalid date"}}function O(e){let t=e.toLowerCase(),r={"execute-command":"Execute Command","check-command-output":"Check Command Output","str-replace":"String Replace","create-file":"Create File","full-file-rewrite":"Rewrite File","delete-file":"Delete File","web-search":"Web Search","crawl-webpage":"Web Crawl","scrape-webpage":"Web Scrape","browser-navigate":"Browser Navigate","browser-click":"Browser Click","browser-extract":"Browser Extract","browser-fill":"Browser Fill","browser-wait":"Browser Wait","see-image":"View Image",ask:"Ask",complete:"Task Complete","execute-data-provider-call":"Data Provider Call","get-data-provider-endpoints":"Data Endpoints",deploy:"Deploy","generic-tool":"Tool",default:"Tool"};if(r[t])return r[t];if(t.startsWith("browser-")){let e=t.replace("browser-","").replace(/-/g," ");return"Browser "+e.charAt(0).toUpperCase()+e.slice(1)}return e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")}function W(e){let t=G(e);if(!t)return null;let r=t.match(/<execute-command[^>]*>([\s\S]*?)<\/execute-command>/);if(r)return r[1].trim();try{let e=JSON.parse(t);if(e.tool_calls&&Array.isArray(e.tool_calls)){var s;let t=e.tool_calls.find(e=>{var t,r;return(null==(t=e.function)?void 0:t.name)==="execute-command"||(null==(r=e.function)?void 0:r.name)==="execute_command"});if(t&&(null==(s=t.function)?void 0:s.arguments))try{let e="string"==typeof t.function.arguments?JSON.parse(t.function.arguments):t.function.arguments;if(e.command)return e.command}catch(e){}}}catch(e){}return t.includes("<execute-command")||t.includes("</execute-command>")||t.startsWith("{")||t.startsWith("<")||t.includes("ToolResult")||t.includes("No command")?(console.log("extractCommand: Could not extract command from content:",t.substring(0,200)),null):t.trim()}function D(e){let t=G(e);if(!t)return null;let r=t.match(/<check-command-output[^>]*session_name=["']([^"']+)["']/);if(r)return r[1].trim();try{let e=JSON.parse(t);if(e.tool_calls&&Array.isArray(e.tool_calls)){var s;let t=e.tool_calls.find(e=>{var t,r;return(null==(t=e.function)?void 0:t.name)==="check-command-output"||(null==(r=e.function)?void 0:r.name)==="check_command_output"});if(t&&(null==(s=t.function)?void 0:s.arguments))try{let e="string"==typeof t.function.arguments?JSON.parse(t.function.arguments):t.function.arguments;if(e.session_name)return e.session_name}catch(e){}}}catch(e){}let l=t.match(/session_name=["']([^"']+)["']/);return l?l[1].trim():null}function U(e){let t=G(e);if(!t)return null;try{let e=JSON.parse(t);if(e.content){if((0,F.tm)(e.content)){let t=(0,F.CG)(e.content);if(t.length>0&&t[0].parameters.file_path)return V(t[0].parameters.file_path)}let t=e.content.match(/file_path=["']([^"']+)["']/);if(t)return V(t[1])}}catch(e){}if((0,F.tm)(t)){let e=(0,F.CG)(t);if(e.length>0&&e[0].parameters.file_path)return V(e[0].parameters.file_path)}let r=t.match(/file_path=["']([^"']+)["']/);if(r)return V(r[1]);if("string"==typeof e&&e.startsWith('"{')&&e.endsWith('}"'))try{let t=JSON.parse(e),r=JSON.parse(t);if(r&&"object"==typeof r){if(r.file_path)return V(r.file_path);if(r.arguments&&r.arguments.file_path)return V(r.arguments.file_path)}}catch(e){}if("object"==typeof e&&null!==e)try{if("content"in e&&"string"==typeof e.content){let t=e.content.match(/<(?:create-file|delete-file|full-file-rewrite|str-replace)[^>]*\s+file_path=["']([\s\S]*?)["']/i)||e.content.match(/<delete[^>]*\s+file_path=["']([\s\S]*?)["']/i)||e.content.match(/<delete-file[^>]*>([^<]+)<\/delete-file>/i)||e.content.match(/<(?:create-file|delete-file|full-file-rewrite)\s+file_path=["']([^"']+)/i);if(t)return V(t[1])}if("file_path"in e)return V(e.file_path);if("arguments"in e&&e.arguments&&"object"==typeof e.arguments){let t=e.arguments;if(t.file_path)return V(t.file_path)}}catch(e){}try{let e=JSON.parse(t);if(e.file_path)return V(e.file_path);if(e.arguments&&e.arguments.file_path)return V(e.arguments.file_path)}catch(e){}let s=t.match(/file_path=["']([\s\S]*?)["']/i)||t.match(/target_file=["']([\s\S]*?)["']/i)||t.match(/path=["']([\s\S]*?)["']/i);if(s)return V(s[1].trim());let l=t.match(/<(?:create-file|delete-file|full-file-rewrite|str-replace)[^>]*\s+file_path=["']([\s\S]*?)["']/i)||t.match(/<delete[^>]*\s+file_path=["']([\s\S]*?)["']/i)||t.match(/<delete-file[^>]*>([^<]+)<\/delete-file>/i)||t.match(/<(?:create-file|delete-file|full-file-rewrite)\s+file_path=["']([^"']+)/i);if(l)return V(l[1]);if(t.toLowerCase().includes("delete")||t.includes("delete-file")){let e=t.match(/(?:delete|remove|deleting)\s+(?:file|the file)?:?\s+["']?([\w\-./\\]+\.\w+)["']?/i);if(e)return V(e[1]);let r=t.match(/["']?([\w\-./\\]+\.\w+)["']?/);if(r)return V(r[1])}return null}function V(e){return e?e.replace(/\\n/g,"\n").replace(/\\t/g,"	").replace(/\\r/g,"").replace(/\\\\/g,"\\").replace(/\\"/g,'"').replace(/\\'/g,"'").split("\n")[0].trim():e}function J(e){let t=G(e);if(!t)return{oldStr:null,newStr:null};let r=t.match(/<str-replace[^>]*>([\s\S]*?)<\/str-replace>/);if(r){let e=r[1],t=e.match(/<old_str>([\s\S]*?)<\/old_str>/),s=e.match(/<new_str>([\s\S]*?)<\/new_str>/);return{oldStr:t?t[1]:null,newStr:s?s[1]:null}}let s=t.match(/<old_str>([\s\S]*?)<\/old_str>/),l=t.match(/<new_str>([\s\S]*?)<\/new_str>/);return{oldStr:s?s[1]:null,newStr:l?l[1]:null}}function M(e){if(!e)return"";if("object"==typeof e)return JSON.stringify(e,null,2);let t=e.trim();if(t.startsWith("{")&&t.endsWith("}")||t.startsWith("[")&&t.endsWith("]"))try{let t=JSON.parse(e);return JSON.stringify(t,null,2)}catch(e){}return e.replace(/\\n/g,"\n").replace(/\\t/g,"	").replace(/\\r/g,"").replace(/\\\\/g,"\\").replace(/\\"/g,'"').replace(/\\'/g,"'")}function B(e){let t=G(e);if(!t)return null;let r=t.match(/ToolResult\(.*?output='([\s\S]*?)'.*?\)/);if(r)try{let e=JSON.parse(r[1]);if(e.query&&"string"==typeof e.query)return e.query}catch(e){}let s=t;try{let e=JSON.parse(t);if(e.query&&"string"==typeof e.query)return e.query;if("string"==typeof e.content){if(s=e.content,"string"==typeof e.query)return e.query;if("object"==typeof e.arguments&&null!==e.arguments&&"string"==typeof e.arguments.query)return e.arguments.query;if(Array.isArray(e.tool_calls)&&e.tool_calls.length>0){let t=e.tool_calls[0];if("object"==typeof t.arguments&&null!==t.arguments&&"string"==typeof t.arguments.query)return t.arguments.query;if("string"==typeof t.arguments)try{let e=JSON.parse(t.arguments);if("string"==typeof e.query)return e.query}catch(e){}}}}catch(e){}let l=s.match(/<web-search[^>]*\s+query=[\"']([^\"']*)["'][^>]*>/i);if(l&&l[1])return l[1].trim();let a=s.match(/query=[\"']([\s\S]*?)["']/i);return a&&a[1]?a[1].split(/[\"']/)[0].trim():null}function $(e){let t,r=[];try{let t=JSON.parse(e);if(Array.isArray(t))return t.map(e=>({title:e.title||"",url:e.url||"",snippet:e.content||e.snippet||""}));if(t.results&&Array.isArray(t.results))return t.results.map(e=>({title:e.title||"",url:e.url||"",snippet:e.content||""}))}catch(e){}let s=/\{\s*"title"\s*:\s*"([^"]+)"\s*,\s*"url"\s*:\s*"(https?:\/\/[^"]+)"\s*(?:,\s*"content"\s*:\s*"([^"]*)")?\s*\}/g;for(;null!==(t=s.exec(e));){let e=t[1],s=t[2],l=t[3]||"";s&&e&&!r.some(e=>e.url===s)&&r.push({title:e,url:s,snippet:l})}if(0===r.length){let t,s=/https?:\/\/[^\s"<]+/g;for(;null!==(t=s.exec(e));){let s=t[0],l=s.indexOf("://"),a=-1!==l?l+3:0,n=s.indexOf("/n",a),i=s.indexOf("\\n",a),o=-1;for(-1!==n&&-1!==i?o=Math.min(n,i):-1!==n?o=n:-1!==i&&(o=i),-1!==o&&(s=s.substring(0,o)),s=s.replace(/<\/?url>$/,"").replace(/<\/?content>$/,"").replace(/%3C$/,"");/[);.,\/]$/.test(s);)s=s.slice(0,-1);try{s=decodeURIComponent(decodeURIComponent(s))}catch(e){try{s=decodeURIComponent(s)}catch(e){console.warn("Failed to decode URL component:",s,e)}}s=(s=s.replace(/\u2026$/,"")).replace(/<\/?url>$/,"").replace(/<\/?content>$/,"");let c=t.index,d=e.substring(Math.max(0,c-100),c+s.length+200),m=d.match(/title"?\s*:\s*"([^"]+)"/i)||d.match(/Title[:\s]+([^\n<]+)/i)||d.match(/\"(.*?)\"[\s\n]*?https?:\/\//),u=q(s);m&&m[1].trim()&&(u=m[1].trim()),s&&!r.some(e=>e.url===s)&&r.push({title:u,url:s})}}return r}function q(e){try{let t=new URL(e);return t.hostname.replace("www.","")+("/"!==t.pathname?t.pathname:"")}catch(t){return e}}function Z(e){let t=G(e);if(!t)return[];try{let e,r=t.match(/"results":\s*\[([^\]]*(?:\[[^\]]*\][^\]]*)*)\]/);if(r)try{let e="["+r[1]+"]",t=JSON.parse(e);if(Array.isArray(t))return t.map(e=>({title:e.title||"",url:e.url||"",snippet:e.content||""}))}catch(e){console.warn("Failed to parse results array:",e)}let s=/\{\s*"url":\s*"([^"]+)"\s*,\s*"title":\s*"([^"]+)"\s*,\s*"content":\s*"([^"]*)"[^}]*\}/g,l=[];for(;null!==(e=s.exec(t));)l.push({url:e[1],title:e[2],snippet:e[3]});if(l.length>0)return l;let a=JSON.parse(t);if(a.results&&Array.isArray(a.results))return a.results.map(e=>({title:e.title||"",url:e.url||"",snippet:e.content||""}));if(a.content&&"string"==typeof a.content){let e=a.content.match(/<tool_result[^>]*>\s*<web-search[^>]*>([\s\S]*?)<\/web-search>\s*<\/tool_result>/);if(e)try{return JSON.parse(e[1])}catch(t){return $(e[1])}let t=a.content.match(/\[\s*{[\s\S]*}\s*\]/);if(t)try{return JSON.parse(t[0])}catch(e){}return $(a.content)}}catch(e){}return $(t)}function G(e){if(!e)return null;if("string"==typeof e){if(e.startsWith('"{')&&e.endsWith('}"'))try{let t=JSON.parse(e),r=JSON.parse(t);if(r&&"object"==typeof r&&"content"in r)return r.content;return JSON.stringify(r)}catch(e){}return e}if("object"==typeof e&&null!==e)try{if("content"in e&&"string"==typeof e.content)return e.content;if("content"in e&&"object"==typeof e.content&&null!==e.content){if("content"in e.content&&"string"==typeof e.content.content)return e.content.content;return JSON.stringify(e.content)}if("role"in e&&"content"in e&&"string"==typeof e.content)return e.content;else if("role"in e&&"content"in e&&"object"==typeof e.content&&null!==e.content){if("content"in e.content&&"string"==typeof e.content.content)return e.content.content;return JSON.stringify(e.content)}else{let t=JSON.stringify(e);return t.includes("<")||t.includes("file_path")||t.includes("command"),t}}catch(t){console.error("Error in normalizeContentToString:",t,"Content:",e)}return null}let H=e=>{var t;let r=null==(t=e.split(".").pop())?void 0:t.toLowerCase();switch(r){case"js":case"jsx":case"ts":case"tsx":return{icon:w.A,color:"text-yellow-500 dark:text-yellow-400",bgColor:"bg-gradient-to-br from-yellow-500/20 to-yellow-600/10 border border-yellow-500/20"};case"py":return{icon:w.A,color:"text-blue-500 dark:text-blue-400",bgColor:"bg-gradient-to-br from-blue-500/20 to-blue-600/10 border border-blue-500/20"};case"html":case"css":case"scss":return{icon:w.A,color:"text-orange-500 dark:text-orange-400",bgColor:"bg-gradient-to-br from-orange-500/20 to-orange-600/10 border border-orange-500/20"};case"json":return{icon:k.A,color:"text-green-500 dark:text-green-400",bgColor:"bg-gradient-to-br from-green-500/20 to-green-600/10 border border-green-500/20"};case"csv":return{icon:y.A,color:"text-emerald-500 dark:text-emerald-400",bgColor:"bg-gradient-to-br from-emerald-500/20 to-emerald-600/10 border border-emerald-500/20"};case"xml":case"yaml":case"yml":return{icon:w.A,color:"text-purple-500 dark:text-purple-400",bgColor:"bg-gradient-to-br from-purple-500/20 to-purple-600/10 border border-purple-500/20"};case"jpg":case"jpeg":case"png":case"gif":case"svg":case"webp":return{icon:z.A,color:"text-pink-500 dark:text-pink-400",bgColor:"bg-gradient-to-br from-pink-500/20 to-pink-600/10 border border-pink-500/20"};case"md":case"mdx":return{icon:S.A,color:"text-slate-500 dark:text-slate-400",bgColor:"bg-gradient-to-br from-slate-500/20 to-slate-600/10 border border-slate-500/20"};case"txt":return{icon:S.A,color:"text-zinc-500 dark:text-zinc-400",bgColor:"bg-gradient-to-br from-zinc-500/20 to-zinc-600/10 border border-zinc-500/20"};case"pdf":return{icon:C.A,color:"text-red-500 dark:text-red-400",bgColor:"bg-gradient-to-br from-red-500/20 to-red-600/10 border border-red-500/20"};case"mp4":case"avi":case"mov":return{icon:A.A,color:"text-indigo-500 dark:text-indigo-400",bgColor:"bg-gradient-to-br from-indigo-500/20 to-indigo-600/10 border border-indigo-500/20"};case"mp3":case"wav":case"ogg":return{icon:E.A,color:"text-teal-500 dark:text-teal-400",bgColor:"bg-gradient-to-br from-teal-500/20 to-teal-600/10 border border-teal-500/20"};case"zip":case"tar":case"gz":case"rar":return{icon:_.A,color:"text-amber-500 dark:text-amber-400",bgColor:"bg-gradient-to-br from-amber-500/20 to-amber-600/10 border border-amber-500/20"};default:if(!r||e.includes("/"))return{icon:R.A,color:"text-blue-500 dark:text-blue-400",bgColor:"bg-gradient-to-br from-blue-500/20 to-blue-600/10 border border-blue-500/20"};return{icon:L.A,color:"text-zinc-500 dark:text-zinc-400",bgColor:"bg-gradient-to-br from-zinc-500/20 to-zinc-600/10 border border-zinc-500/20"}}};function X(e){let t=function(e){try{if("string"==typeof e)return T(e);if("object"==typeof e&&null!==e)return I(e);return null}catch(e){return console.error("Error parsing tool result:",e),null}}(e);if(t){let e=t.arguments||{};return{toolResult:t,arguments:e,filePath:e.file_path||e.path||null,fileContent:e.file_contents||e.content||null,command:e.command||null,url:e.url||null,query:e.query||null}}return{toolResult:null,arguments:{},filePath:null,fileContent:null,command:null,url:null,query:null}}var Y=r(66695),Q=r(26126),K=r(66424),ee=r(51154),et=r(55863);function er(e){let{className:t,value:r,...l}=e;return(0,s.jsx)(et.bL,{"data-slot":"progress",className:(0,i.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",t),...l,children:(0,s.jsx)(et.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:"translateX(-".concat(100-(r||0),"%)")}})})}function es(e){let{icon:t=ee.A,iconColor:r="text-purple-500 dark:text-purple-400",bgColor:l="bg-gradient-to-b from-purple-100 to-purple-50 shadow-inner dark:from-purple-800/40 dark:to-purple-900/60 dark:shadow-purple-950/20",title:n,subtitle:o,filePath:c,showProgress:d=!0,progressText:m,autoProgress:u=!0,initialProgress:x=0}=e,[h,p]=(0,a.useState)(x);return(0,a.useEffect)(()=>{if(d&&u){p(0);let e=setInterval(()=>{p(t=>t>=95?(clearInterval(e),t):t+10*Math.random()+5)},500);return()=>clearInterval(e)}},[d,u]),(0,s.jsx)("div",{className:"flex flex-col items-center justify-center h-[calc(100vh-15rem)] overflow-hidden scrollbar-hide py-12 px-6",children:(0,s.jsxs)("div",{className:"text-center w-full max-w-sm",children:[(0,s.jsx)("div",{className:(0,i.cn)("w-16 h-16 rounded-full mx-auto mb-6 flex items-center justify-center",l),children:(0,s.jsx)(t,{className:(0,i.cn)("h-8 w-8",r,t===ee.A&&"animate-spin")})}),(0,s.jsx)("h3",{className:"text-xl font-semibold mb-4 text-zinc-900 dark:text-zinc-100",children:n}),c&&(0,s.jsx)("div",{className:"bg-zinc-50 dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-lg p-4 w-full text-center mb-6 shadow-sm",children:(0,s.jsx)("code",{className:"text-sm font-mono text-zinc-700 dark:text-zinc-300 break-all",children:c})}),d&&(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(er,{value:Math.min(h,100),className:"w-full h-1"}),(0,s.jsxs)("div",{className:"flex justify-between items-center text-xs text-zinc-500 dark:text-zinc-400",children:[(0,s.jsx)("span",{children:m||"Processing..."}),(0,s.jsxs)("span",{className:"font-mono",children:[Math.round(Math.min(h,100)),"%"]})]})]}),o&&(0,s.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400 mt-4",children:o})]})})}function el(e){let{name:t="generic-tool",assistantContent:r,toolContent:l,assistantTimestamp:n,toolTimestamp:i,isSuccess:o=!0,isStreaming:c=!1}=e,d=O(t),m=e=>{if(!e)return null;let{toolResult:t}=X(e);if(t){let e={tool:t.xmlTagName||t.functionName};return t.arguments&&Object.keys(t.arguments).length>0&&(e.parameters=t.arguments),t.toolOutput&&(e.output=t.toolOutput),void 0!==t.isSuccess&&(e.success=t.isSuccess),JSON.stringify(e,null,2)}if("object"==typeof e){if("tool_name"in e||"xml_tag_name"in e){let t={tool:e.tool_name||e.xml_tag_name||"unknown"};return e.parameters&&Object.keys(e.parameters).length>0&&(t.parameters=e.parameters),e.result&&(t.result=e.result),JSON.stringify(t,null,2)}if("content"in e&&"object"==typeof e.content){let t=e.content;if("tool_name"in t||"xml_tag_name"in t){let e={tool:t.tool_name||t.xml_tag_name||"unknown"};return t.parameters&&Object.keys(t.parameters).length>0&&(e.parameters=t.parameters),t.result&&(e.result=t.result),JSON.stringify(e,null,2)}}return e.content&&"string"==typeof e.content?e.content:JSON.stringify(e,null,2)}if("string"==typeof e)try{let t=JSON.parse(e);return JSON.stringify(t,null,2)}catch(t){return e}return String(e)},u=a.useMemo(()=>m(r),[r]),x=a.useMemo(()=>m(l),[l]);return(0,s.jsxs)(Y.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,s.jsx)(Y.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,s.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"relative p-2 rounded-lg bg-gradient-to-br from-orange-500/20 to-orange-600/10 border border-orange-500/20",children:(0,s.jsx)(b.A,{className:"w-5 h-5 text-orange-500 dark:text-orange-400"})}),(0,s.jsx)("div",{children:(0,s.jsx)(Y.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:d})})]}),!c&&(0,s.jsxs)(Q.E,{variant:"secondary",className:o?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[o?(0,s.jsx)(j.A,{className:"h-3.5 w-3.5"}):(0,s.jsx)(v.A,{className:"h-3.5 w-3.5"}),o?"Tool executed successfully":"Tool execution failed"]})]})}),(0,s.jsx)(Y.Wu,{className:"p-0 h-full flex-1 overflow-hidden relative",children:c?(0,s.jsx)(es,{icon:b.A,iconColor:"text-orange-500 dark:text-orange-400",bgColor:"bg-gradient-to-b from-orange-100 to-orange-50 shadow-inner dark:from-orange-800/40 dark:to-orange-900/60 dark:shadow-orange-950/20",title:"Executing tool",filePath:t,showProgress:!0}):u||x?(0,s.jsx)(K.F,{className:"h-full w-full",children:(0,s.jsxs)("div",{className:"p-4 space-y-4",children:[u&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"text-sm font-medium text-zinc-700 dark:text-zinc-300 flex items-center",children:[(0,s.jsx)(b.A,{className:"h-4 w-4 mr-2 text-zinc-500 dark:text-zinc-400"}),"Input"]}),(0,s.jsx)("div",{className:"border-muted bg-muted/20 rounded-lg overflow-hidden border",children:(0,s.jsx)("div",{className:"p-4",children:(0,s.jsx)("pre",{className:"text-xs text-zinc-700 dark:text-zinc-300 whitespace-pre-wrap break-words font-mono",children:u})})})]}),x&&(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"text-sm font-medium text-zinc-700 dark:text-zinc-300 flex items-center",children:[(0,s.jsx)(b.A,{className:"h-4 w-4 mr-2 text-zinc-500 dark:text-zinc-400"}),"Output"]}),(0,s.jsx)("div",{className:"border-muted bg-muted/20 rounded-lg overflow-hidden border",children:(0,s.jsx)("div",{className:"p-4",children:(0,s.jsx)("pre",{className:"text-xs text-zinc-700 dark:text-zinc-300 whitespace-pre-wrap break-words font-mono",children:x})})})]})]})}):(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:[(0,s.jsx)("div",{className:"w-20 h-20 rounded-full flex items-center justify-center mb-6 bg-gradient-to-b from-zinc-100 to-zinc-50 shadow-inner dark:from-zinc-800/40 dark:to-zinc-900/60",children:(0,s.jsx)(b.A,{className:"h-10 w-10 text-zinc-400 dark:text-zinc-600"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold mb-2 text-zinc-900 dark:text-zinc-100",children:"No Content Available"}),(0,s.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400 text-center max-w-md",children:"This tool execution did not produce any input or output content to display."})]})}),(0,s.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,s.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:!c&&(u||x)&&(0,s.jsxs)(Q.E,{variant:"outline",className:"h-6 py-0.5 bg-zinc-50 dark:bg-zinc-900",children:[(0,s.jsx)(b.A,{className:"h-3 w-3"}),"Tool"]})}),(0,s.jsxs)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400 flex items-center gap-2",children:[(0,s.jsx)(N.A,{className:"h-3.5 w-3.5"}),i&&!c?P(i):n?P(n):""]})]})]})}var ea=r(66611),en=r(33786),ei=r(34869);let eo=e=>{let{className:t=""}=e;return(0,s.jsx)("div",{className:"relative flex items-center justify-center h-full w-full overflow-hidden ".concat(t),children:(0,s.jsxs)("div",{className:"h-[60%] flex flex-col gap-6 aspect-square rounded-xl items-center justify-center",children:[(0,s.jsx)(c.Skeleton,{className:"w-full h-full rounded-lg"}),(0,s.jsx)(c.Skeleton,{className:"w-full h-14 rounded-lg"}),(0,s.jsx)(c.Skeleton,{className:"w-full h-14 rounded-lg"})]})})};function ec(e){var t,r;let n,{name:i="browser-operation",assistantContent:o,toolContent:c,assistantTimestamp:d,toolTimestamp:m,isSuccess:u=!0,isStreaming:h=!1,project:p,agentStatus:f="idle",messages:b=[],currentIndex:N=0,totalCalls:w=1}=e,k=X(o),y=X(c),z=null;k.toolResult?z=k.url:y.toolResult&&(z=y.url),z||(z=function(e){let t=G(e);if(!t)return null;let r=t.match(/url=["'](https?:\/\/[^"']+)["']/);return r?r[1]:null}(o));let S=function(e){if(!e)return"Browser Operation";let t=e.replace("browser-","").replace(/-/g," ");return t.charAt(0).toUpperCase()+t.slice(1)}(i),C=O(i),A=null,E=null,[_,R]=a.useState(!0),[L,F]=a.useState(!1);try{let e=(0,l.jD)(c,{}),t=(null==e?void 0:e.content)||c;if(t&&"string"==typeof t){let e=t.match(/ToolResult\([^)]*output='([\s\S]*?)'(?:\s*,|\s*\))/);if(e){let t=e[1];try{let e=t.replace(/\\n/g,"\n").replace(/\\"/g,'"').replace(/\\u([0-9a-fA-F]{4})/g,(e,t)=>String.fromCharCode(parseInt(t,16))),r=JSON.parse(e);r.image_url&&(A=r.image_url),r.message_id&&(n=r.message_id)}catch(e){}}if(!A){let e=t.match(/"image_url":\s*"([^"]+)"/);e&&(A=e[1])}if(!n){let e=t.match(/"message_id":\s*"([^"]+)"/);e&&(n=e[1])}if(!n&&!A){let e=t.match(/\boutput='(.*?)'(?=\s*\))/),r=e?e[1]:null;if(r){let e=r.replace(/\\n/g,"\n").replace(/\\"/g,'"'),t=(0,l.jD)(e,{});n=null==t?void 0:t.message_id,A=(null==t?void 0:t.image_url)||null}}}else t&&"object"==typeof t&&(A=t&&"tool_execution"in t&&"result"in t.tool_execution&&"output"in t.tool_execution.result&&"image_url"in t.tool_execution.result.output&&"string"==typeof t.tool_execution.result.output.image_url?t.tool_execution.result.output.image_url:null)}catch(e){}if(!A&&!E&&n&&b.length>0){let e=b.find(e=>"browser_state"===e.type&&e.message_id===n);if(e){let t=(0,l.jD)(e.content,{});E=(null==t?void 0:t.screenshot_base64)||null,A=(null==t?void 0:t.image_url)||null}}let T=(null==p||null==(t=p.sandbox)?void 0:t.vnc_preview)?"".concat(p.sandbox.vnc_preview,"/vnc_lite.html?password=").concat(null==p||null==(r=p.sandbox)?void 0:r.pass,"&autoconnect=true&scale=local&width=1024&height=768"):void 0,I=h||"running"===f,W=(0,a.useMemo)(()=>T?(0,s.jsx)("iframe",{src:T,title:"Browser preview",className:"w-full h-full border-0 min-h-[600px]",style:{width:"100%",height:"100%",minHeight:"600px"}}):null,[T]),[D,U]=a.useState(100);a.useEffect(()=>{if(I){U(0);let e=setInterval(()=>{U(t=>t>=95?(clearInterval(e),t):t+2)},500);return()=>clearInterval(e)}U(100)},[I]),a.useEffect(()=>{(A||E)&&(R(!0),F(!1))},[A,E]);let V=()=>{R(!1),F(!1)},J=()=>{R(!1),F(!0)};return(0,s.jsxs)(Y.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,s.jsx)(Y.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,s.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"relative p-2 rounded-lg bg-gradient-to-br from-purple-500/20 to-purple-600/10 border border-purple-500/20",children:(0,s.jsx)(ea.A,{className:"w-5 h-5 text-purple-500 dark:text-purple-400"})}),(0,s.jsx)("div",{children:(0,s.jsx)(Y.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:C})})]}),!I&&(0,s.jsxs)(Q.E,{variant:"secondary",className:u?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[u?(0,s.jsx)(j.A,{className:"h-3.5 w-3.5 mr-1"}):(0,s.jsx)(v.A,{className:"h-3.5 w-3.5 mr-1"}),u?"Browser action completed":"Browser action failed"]}),I&&(0,s.jsxs)(Q.E,{className:"bg-gradient-to-b from-blue-200 to-blue-100 text-blue-700 dark:from-blue-800/50 dark:to-blue-900/60 dark:text-blue-300",children:[(0,s.jsx)(x.A,{className:"h-3.5 w-3.5 animate-spin"}),"Executing browser action"]})]})}),(0,s.jsx)(Y.Wu,{className:"p-0 flex-1 overflow-hidden relative",style:{height:"calc(100vh - 150px)",minHeight:"600px"},children:(0,s.jsx)("div",{className:"flex-1 flex h-full items-stretch bg-white dark:bg-black",children:N===w-1?I&&W?(0,s.jsx)("div",{className:"flex flex-col items-center justify-center w-full h-full min-h-[600px]",style:{minHeight:"600px"},children:(0,s.jsxs)("div",{className:"relative w-full h-full min-h-[600px]",style:{minHeight:"600px"},children:[W,(0,s.jsx)("div",{className:"absolute top-4 right-4 z-10",children:(0,s.jsxs)(Q.E,{className:"bg-blue-500/90 text-white border-none shadow-lg animate-pulse",children:[(0,s.jsx)(x.A,{className:"h-3 w-3 animate-spin"}),S," in progress"]})})]})}):A||E?A?(0,s.jsxs)("div",{className:"flex items-center justify-center w-full h-full min-h-[600px] relative p-4",style:{minHeight:"600px"},children:[_&&(0,s.jsx)(eo,{}),(0,s.jsx)(Y.Zp,{className:"p-0 overflow-hidden border ".concat(_?"hidden":"block"),children:(0,s.jsx)("img",{src:A,alt:"Browser Screenshot",className:"max-w-full max-h-full object-contain",onLoad:V,onError:J})}),L&&!_&&(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-zinc-50 dark:bg-zinc-900",children:(0,s.jsxs)("div",{className:"text-center text-zinc-500 dark:text-zinc-400",children:[(0,s.jsx)(v.A,{className:"h-8 w-8 mx-auto mb-2"}),(0,s.jsx)("p",{children:"Failed to load screenshot"})]})})]}):E?(0,s.jsxs)("div",{className:"flex items-center justify-center w-full h-full min-h-[600px] relative p-4",style:{minHeight:"600px"},children:[_&&(0,s.jsx)(eo,{}),(0,s.jsx)(Y.Zp,{className:"overflow-hidden border ".concat(_?"hidden":"block"),children:(0,s.jsx)("img",{src:"data:image/jpeg;base64,".concat(E),alt:"Browser Screenshot",className:"max-w-full max-h-full object-contain",onLoad:V,onError:J})}),L&&!_&&(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-zinc-50 dark:bg-zinc-900",children:(0,s.jsxs)("div",{className:"text-center text-zinc-500 dark:text-zinc-400",children:[(0,s.jsx)(v.A,{className:"h-8 w-8 mx-auto mb-2"}),(0,s.jsx)("p",{children:"Failed to load screenshot"})]})})]}):null:W?(0,s.jsx)("div",{className:"flex flex-col items-center justify-center w-full h-full min-h-[600px]",style:{minHeight:"600px"},children:W}):(0,s.jsxs)("div",{className:"p-8 flex flex-col items-center justify-center w-full bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900 text-zinc-700 dark:text-zinc-400",children:[(0,s.jsx)("div",{className:"w-20 h-20 rounded-full flex items-center justify-center mb-6 bg-gradient-to-b from-purple-100 to-purple-50 shadow-inner dark:from-purple-800/40 dark:to-purple-900/60",children:(0,s.jsx)(ea.A,{className:"h-10 w-10 text-purple-400 dark:text-purple-600"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold mb-2 text-zinc-900 dark:text-zinc-100",children:"Browser preview not available"}),z&&(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)(g.$,{variant:"outline",size:"sm",className:"bg-white dark:bg-zinc-900 border-zinc-200 dark:border-zinc-700 shadow-sm hover:shadow-md transition-shadow",asChild:!0,children:(0,s.jsxs)("a",{href:z,target:"_blank",rel:"noopener noreferrer",children:[(0,s.jsx)(en.A,{className:"h-3.5 w-3.5 mr-2"}),"Visit URL"]})})})]}):A||E?(0,s.jsxs)("div",{className:"flex items-center justify-center w-full h-full overflow-auto relative p-4",children:[_&&(0,s.jsx)(eo,{}),(0,s.jsx)(Y.Zp,{className:"p-0 overflow-hidden border ".concat(_?"hidden":"block"),children:A?(0,s.jsx)("img",{src:A,alt:"Browser Screenshot",className:"max-w-full max-h-full object-contain",onLoad:V,onError:J}):(0,s.jsx)("img",{src:"data:image/jpeg;base64,".concat(E),alt:"Browser Screenshot",className:"max-w-full max-h-full object-contain",onLoad:V,onError:J})}),L&&!_&&(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-zinc-50 dark:bg-zinc-900",children:(0,s.jsxs)("div",{className:"text-center text-zinc-500 dark:text-zinc-400",children:[(0,s.jsx)(v.A,{className:"h-8 w-8 mx-auto mb-2"}),(0,s.jsx)("p",{children:"Failed to load screenshot"})]})})]}):(0,s.jsxs)("div",{className:"p-8 h-full flex flex-col items-center justify-center w-full bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900 text-zinc-700 dark:text-zinc-400",children:[(0,s.jsx)("div",{className:"w-20 h-20 rounded-full flex items-center justify-center mb-6 bg-gradient-to-b from-zinc-100 to-zinc-50 shadow-inner dark:from-zinc-800/40 dark:to-zinc-900/60",children:(0,s.jsx)(ea.A,{className:"h-10 w-10 text-zinc-400 dark:text-zinc-600"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold mb-2 text-zinc-900 dark:text-zinc-100",children:"No Browser State Available"}),(0,s.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400",children:"Browser state image not found for this action"})]})})}),(0,s.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,s.jsxs)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:[!I&&(0,s.jsxs)(Q.E,{className:"h-6 py-0.5",children:[(0,s.jsx)(ei.A,{className:"h-3 w-3"}),S]}),z&&(0,s.jsx)("span",{className:"text-xs truncate max-w-[200px] hidden sm:inline-block",children:z})]}),(0,s.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:m&&!I?P(m):d?P(d):""})]})]})}var ed=r(41684),em=r(51362);let eu=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){}return e},ex=e=>{let t=eu(e);if(!t||"object"!=typeof t)return{command:null,output:null,exitCode:null,sessionName:null,cwd:null,completed:null,success:void 0,timestamp:void 0};if("tool_execution"in t&&"object"==typeof t.tool_execution){var r,s,l;let e=t.tool_execution,a=e.arguments||{},n=null==(r=e.result)?void 0:r.output;if("string"==typeof n)try{n=JSON.parse(n)}catch(e){}n=n||{};let i={command:a.command||null,output:(null==n?void 0:n.output)||null,exitCode:(null==n?void 0:n.exit_code)||null,sessionName:a.session_name||(null==n?void 0:n.session_name)||null,cwd:(null==n?void 0:n.cwd)||null,completed:(null==n?void 0:n.completed)||null,success:null==(s=e.result)?void 0:s.success,timestamp:null==(l=e.execution_details)?void 0:l.timestamp};return console.log("CommandToolView: Extracted from new format:",{command:i.command,hasOutput:!!i.output,exitCode:i.exitCode,sessionName:i.sessionName,success:i.success}),i}return"role"in t&&"content"in t?ex(t.content):{command:null,output:null,exitCode:null,sessionName:null,cwd:null,completed:null,success:void 0,timestamp:void 0}},eh=e=>{let t=X(e);if(t.toolResult){let e=t.arguments||{};return console.log("CommandToolView: Extracted from legacy format (extractToolData):",{command:t.command||e.command,hasOutput:!!t.toolResult.toolOutput}),{command:t.command||e.command||null,output:t.toolResult.toolOutput||null,exitCode:null,sessionName:e.session_name||null,cwd:null,completed:null}}let r=W(e);return console.log("CommandToolView: Extracted from legacy format (fallback):",{command:r}),{command:r,output:null,exitCode:null,sessionName:null,cwd:null,completed:null}};function ep(e,t,r,s,l){let a=null,n=null,i=null,o=null,c=null,d=null,m=r,u=s,x=l,h=ex(e),p=ex(t);if(console.log("CommandToolView: Format detection results:",{assistantNewFormat:{hasCommand:!!h.command,hasOutput:!!h.output,sessionName:h.sessionName},toolNewFormat:{hasCommand:!!p.command,hasOutput:!!p.output,sessionName:p.sessionName}}),h.command||h.output)a=h.command,n=h.output,i=h.exitCode,o=h.sessionName,c=h.cwd,d=h.completed,void 0!==h.success&&(m=h.success),h.timestamp&&(x=h.timestamp),console.log("CommandToolView: Using assistant new format data");else if(p.command||p.output)a=p.command,n=p.output,i=p.exitCode,o=p.sessionName,c=p.cwd,d=p.completed,void 0!==p.success&&(m=p.success),p.timestamp&&(u=p.timestamp),console.log("CommandToolView: Using tool new format data");else{let r=eh(e),s=eh(t);a=r.command||s.command,console.log("CommandToolView: Using legacy format data:",{command:a,hasOutput:!!(n=r.output||s.output),sessionName:o=r.sessionName||s.sessionName})}if(!a){var f,g,b;let r=W(e)||W(t);a=(null==r||null==(b=r.replace(/^suna@computer:~\$\s*/g,""))||null==(g=b.replace(/\\n/g,""))||null==(f=g.replace(/\n/g,""))?void 0:f.trim())||null}if(!n&&t&&(n=function(e){let t=G(e);if(!t)return null;try{let e=JSON.parse(t);if(e.output&&"string"==typeof e.output)return e.output;if(e.content&&"string"==typeof e.content){let t=e.content.match(/<tool_result>\s*<(?:execute-command|check-command-output)>([\s\S]*?)<\/(?:execute-command|check-command-output)>\s*<\/tool_result>/);if(t)return t[1].trim();let r=e.content.match(/ToolResult\(.*?output='([\s\S]*?)'.*?\)/);if(r)return r[1];return e.content}if("string"==typeof e)return e}catch(s){let e=t.match(/<tool_result>\s*<(?:execute-command|check-command-output)>([\s\S]*?)<\/(?:execute-command|check-command-output)>\s*<\/tool_result>/);if(e)return e[1].trim();let r=t.match(/ToolResult\(.*?output='([\s\S]*?)'.*?\)/);if(r)return r[1];t.startsWith("<")||t.includes("ToolResult")}return t}(t)),null===i&&t&&(i=function(e){let t=G(e);if(!t)return null;try{let e=t.match(/exit_code=(\d+)/);if(e&&e[1])return parseInt(e[1],10);return 0}catch(e){return null}}(t)),o||(o=D(e)||D(t)),n&&"string"==typeof n&&n.includes("exit_code=")&&null===i){let e=n.match(/exit_code=(\d+)/);e&&(i=parseInt(e[1],10))}return{command:a,output:n,exitCode:i,sessionName:o,cwd:c,completed:d,actualIsSuccess:m,actualToolTimestamp:u,actualAssistantTimestamp:x}}let ef=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){}return e},eg=e=>{let t=ef(e);if(!t||"object"!=typeof t)return{port:null,url:null,message:null,success:void 0,timestamp:void 0};if("tool_execution"in t&&"object"==typeof t.tool_execution){var r,s,l;let e=t.tool_execution,a=e.arguments||{},n=null==(r=e.result)?void 0:r.output;if("string"==typeof n)try{n=JSON.parse(n)}catch(e){}let i={port:a.port?parseInt(a.port,10):(null==n?void 0:n.port)?parseInt(n.port,10):null,url:(null==n?void 0:n.url)||null,message:(null==n?void 0:n.message)||t.summary||null,success:null==(s=e.result)?void 0:s.success,timestamp:null==(l=e.execution_details)?void 0:l.timestamp};return console.log("ExposePortToolView: Extracted from new format:",{port:i.port,hasUrl:!!i.url,hasMessage:!!i.message,success:i.success}),i}return"role"in t&&"content"in t?eg(t.content):{port:null,url:null,message:null,success:void 0,timestamp:void 0}},eb=e=>{let t=G(e);if(!t)return null;try{let e=t.match(/<expose-port>\s*(\d+)\s*<\/expose-port>/);return e?parseInt(e[1],10):null}catch(e){return console.error("Failed to extract port number:",e),null}},ej=e=>{let t=X(e);if(t.toolResult&&t.arguments)return console.log("ExposePortToolView: Extracted from legacy format (extractToolData):",{port:t.arguments.port}),{port:t.arguments.port?parseInt(t.arguments.port,10):null,url:null,message:null};let r=G(e);if(!r)return{port:null,url:null,message:null};try{let e=JSON.parse(r);if(e.url&&e.port)return{port:parseInt(e.port,10),url:e.url,message:e.message||null}}catch(e){}try{let e=r.match(/ToolResult\(success=(?:True|true),\s*output='((?:[^'\\]|\\.)*)'\)/);if(e){let t=e[1];t=t.replace(/\\\\n/g,"\n").replace(/\\\\"/g,'"').replace(/\\n/g,"\n").replace(/\\"/g,'"').replace(/\\'/g,"'").replace(/\\\\/g,"\\");let r=JSON.parse(t);return{port:r.port?parseInt(r.port,10):null,url:r.url||null,message:r.message||null}}let t=r.match(/output='([^']+)'/);if(t){let e=t[1].replace(/\\n/g,"\n").replace(/\\"/g,'"'),r=JSON.parse(e);return{port:r.port?parseInt(r.port,10):null,url:r.url||null,message:r.message||null}}return{port:null,url:null,message:null}}catch(e){return console.error("Failed to parse tool content:",e),console.error("Tool content was:",r),{port:null,url:null,message:null}}};var ev=r(29621),eN=r(92657),ew=r(12969),ek=r(64384),ey=r(66722),ez=r(63496),eS=r(17313),eC=r(49992),eA=r(64320),eE=r(62525),e_=r(64261);let eR=e=>{var t;return({html:"html",htm:"html",css:"css",scss:"scss",sass:"scss",less:"less",js:"javascript",jsx:"jsx",ts:"typescript",tsx:"tsx",json:"json",jsonc:"json",xml:"xml",yml:"yaml",yaml:"yaml",toml:"toml",ini:"ini",env:"bash",gitignore:"bash",dockerignore:"bash",py:"python",rb:"ruby",php:"php",go:"go",java:"java",kt:"kotlin",c:"c",cpp:"cpp",h:"c",hpp:"cpp",cs:"csharp",swift:"swift",rs:"rust",sh:"bash",bash:"bash",zsh:"bash",ps1:"powershell",bat:"batch",cmd:"batch",svg:"svg",tex:"latex",graphql:"graphql",gql:"graphql"})[(null==(t=e.split(".").pop())?void 0:t.toLowerCase())||""]||"text"},eL=(e,t)=>{if(e){if(e.includes("create"))return"create";if(e.includes("rewrite"))return"rewrite";if(e.includes("delete"))return"delete"}if(!t)return"create";let r="string"==typeof t?t:JSON.stringify(t);return!r||r.includes("<create-file>")?"create":r.includes("<full-file-rewrite>")?"rewrite":r.includes("delete-file")||r.includes("<delete>")?"delete":r.toLowerCase().includes("create file")?"create":r.toLowerCase().includes("rewrite file")?"rewrite":r.toLowerCase().includes("delete file")?"delete":"create"},eF=()=>({create:{icon:eC.A,color:"text-emerald-600 dark:text-emerald-400",successMessage:"File created successfully",progressMessage:"Creating file...",bgColor:"bg-gradient-to-b from-emerald-100 to-emerald-50 shadow-inner dark:from-emerald-800/40 dark:to-emerald-900/60 dark:shadow-emerald-950/20",gradientBg:"bg-gradient-to-br from-emerald-500/20 to-emerald-600/10",borderColor:"border-emerald-500/20",badgeColor:"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 shadow-sm dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300",hoverColor:"hover:bg-neutral-200 dark:hover:bg-neutral-800"},rewrite:{icon:eA.A,color:"text-blue-600 dark:text-blue-400",successMessage:"File rewritten successfully",progressMessage:"Rewriting file...",bgColor:"bg-gradient-to-b from-blue-100 to-blue-50 shadow-inner dark:from-blue-800/40 dark:to-blue-900/60 dark:shadow-blue-950/20",gradientBg:"bg-gradient-to-br from-blue-500/20 to-blue-600/10",borderColor:"border-blue-500/20",badgeColor:"bg-gradient-to-b from-blue-200 to-blue-100 text-blue-700 shadow-sm dark:from-blue-800/50 dark:to-blue-900/60 dark:text-blue-300",hoverColor:"hover:bg-neutral-200 dark:hover:bg-neutral-800"},delete:{icon:eE.A,color:"text-rose-600 dark:text-rose-400",successMessage:"File deleted successfully",progressMessage:"Deleting file...",bgColor:"bg-gradient-to-b from-rose-100 to-rose-50 shadow-inner dark:from-rose-800/40 dark:to-rose-900/60 dark:shadow-rose-950/20",gradientBg:"bg-gradient-to-br from-rose-500/20 to-rose-600/10",borderColor:"border-rose-500/20",badgeColor:"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 shadow-sm dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",hoverColor:"hover:bg-neutral-200 dark:hover:bg-neutral-800"}}),eT=e=>e.endsWith(".md")?w.A:e.endsWith(".csv")?e_.A:e.endsWith(".html")?w.A:L.A,eI=e=>e?e.trim().replace(/\\n/g,"\n").split("\n")[0]:null,eP=e=>e?e.split("/").pop()||e:"",eO=e=>{var t;return(null==(t=e.split(".").pop())?void 0:t.toLowerCase())||""},eW={markdown:e=>"md"===e,html:e=>"html"===e||"htm"===e,csv:e=>"csv"===e},eD=e=>"text"!==e,eU=e=>e?e.replace(/\\n/g,"\n").split("\n"):[];function eV(e){var t;let{assistantContent:r,toolContent:l,assistantTimestamp:a,toolTimestamp:n,isSuccess:o=!0,isStreaming:c=!1,name:d,project:m}=e,{resolvedTheme:u}=(0,em.D)(),x=eL(d,r),h=eF()[x],p=h.icon,f=null,b=null,j=X(r),v=X(l);j.toolResult?(f=j.filePath,b=j.fileContent):v.toolResult&&(f=v.filePath,b=v.fileContent),f||(f=U(r)),b||"delete"===x||(b=c?function(e,t){let r=G(e);if(!r)return null;let s="create-file"===t?"create-file":"full-file-rewrite";if("object"==typeof e&&null!==e)try{if("content"in e&&"string"==typeof e.content){let t=e.content.match(RegExp("<".concat(s,"[^>]*>"),"i"));if(t){let r=e.content.indexOf(t[0])+t[0].length,l=e.content.substring(r),a=l.match(RegExp("<\\/".concat(s,">"),"i"));if(a)return M(l.substring(0,a.index));return M(l)}}}catch(e){}let l=r.match(RegExp("<".concat(s,"[^>]*>"),"i"));if(l){let e=r.indexOf(l[0])+l[0].length,t=r.substring(e),a=t.match(RegExp("<\\/".concat(s,">"),"i"));return a?M(t.substring(0,a.index)):M(t)}return null}(r,"create"===x?"create-file":"full-file-rewrite")||"":function(e,t){let r=G(e);if(!r)return null;try{let e=JSON.parse(r);if(e.content){if((0,F.tm)(e.content)){let t=(0,F.CG)(e.content);if(t.length>0&&t[0].parameters.file_contents)return M(t[0].parameters.file_contents)}let r="create-file"===t?"create-file":"full-file-rewrite",s=e.content.match(RegExp("<".concat(r,"[^>]*>([\\s\\S]*?)<\\/").concat(r,">"),"i"));if(s)return M(s[1])}}catch(e){}if((0,F.tm)(r)){let e=(0,F.CG)(r);if(e.length>0&&e[0].parameters.file_contents)return M(e[0].parameters.file_contents)}let s="create-file"===t?"create-file":"full-file-rewrite",l=r.match(RegExp("<".concat(s,"[^>]*>([\\s\\S]*?)<\\/").concat(s,">"),"i"));return l?M(l[1]):null}(r,"create"===x?"create-file":"full-file-rewrite"));let N=O(d||"file-".concat(x)),w=eI(f),k=eP(w),y=eO(k),z=eW.markdown(y),S=eW.html(y),C=eW.csv(y),A=eR(k),E=eD(A),_=eU(b),R=S&&(null==m||null==(t=m.sandbox)?void 0:t.sandbox_url)&&w?(0,ez.i)(m.sandbox.sandbox_url,w):void 0,L=eT(k);return c||w||b?(0,s.jsx)(Y.Zp,{className:"flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:(0,s.jsxs)(eS.tU,{defaultValue:"preview",className:"w-full h-full",children:[(0,s.jsx)(Y.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2 mb-0",children:(0,s.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:(0,i.cn)("relative p-2 rounded-lg border",h.gradientBg,h.borderColor),children:(0,s.jsx)(p,{className:(0,i.cn)("h-5 w-5",h.color)})}),(0,s.jsx)("div",{children:(0,s.jsx)(Y.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:N})})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[S&&R&&!c&&(0,s.jsx)(g.$,{variant:"outline",size:"sm",className:"h-8 text-xs bg-white dark:bg-zinc-900 hover:bg-zinc-100 dark:hover:bg-zinc-800",asChild:!0,children:(0,s.jsxs)("a",{href:R,target:"_blank",rel:"noopener noreferrer",children:[(0,s.jsx)(en.A,{className:"h-3.5 w-3.5 mr-1.5"}),"Open in Browser"]})}),(0,s.jsxs)(eS.j7,{className:"-mr-2 h-7 bg-zinc-100/70 dark:bg-zinc-800/70 rounded-lg",children:[(0,s.jsxs)(eS.Xi,{value:"code",className:"rounded-md data-[state=active]:bg-white dark:data-[state=active]:bg-zinc-900 data-[state=active]:text-primary",children:[(0,s.jsx)(ev.A,{className:"h-4 w-4"}),"Source"]}),(0,s.jsxs)(eS.Xi,{value:"preview",className:"rounded-md data-[state=active]:bg-white dark:data-[state=active]:bg-zinc-900 data-[state=active]:text-primary",children:[(0,s.jsx)(eN.A,{className:"h-4 w-4"}),"Preview"]})]})]})]})}),(0,s.jsxs)(Y.Wu,{className:"p-0 -my-2 h-full flex-1 overflow-hidden relative",children:[(0,s.jsx)(eS.av,{value:"code",className:"flex-1 h-full mt-0 p-0 overflow-hidden",children:(0,s.jsx)(K.F,{className:"h-screen w-full min-h-0",children:c&&!b?(0,s.jsx)(es,{icon:p,iconColor:h.color,bgColor:h.bgColor,title:h.progressMessage,filePath:w||"Processing file...",subtitle:"Please wait while the file is being processed",showProgress:!1}):"delete"===x?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6",children:[(0,s.jsx)("div",{className:(0,i.cn)("w-20 h-20 rounded-full flex items-center justify-center mb-6",h.bgColor),children:(0,s.jsx)(p,{className:(0,i.cn)("h-10 w-10",h.color)})}),(0,s.jsx)("h3",{className:"text-xl font-semibold mb-6 text-zinc-900 dark:text-zinc-100",children:"Delete Operation"}),(0,s.jsx)("div",{className:"bg-zinc-50 dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-lg p-4 w-full max-w-md text-center",children:(0,s.jsx)("code",{className:"text-sm font-mono text-zinc-700 dark:text-zinc-300 break-all",children:w||"Unknown file path"})})]}):b?E?(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-12 border-r border-zinc-200 dark:border-zinc-800 z-10 flex flex-col bg-zinc-50 dark:bg-zinc-900",children:_.map((e,t)=>(0,s.jsx)("div",{className:"h-6 text-right pr-3 text-xs font-mono text-zinc-500 dark:text-zinc-500 select-none",children:t+1},t))}),(0,s.jsx)("div",{className:"pl-12",children:(0,s.jsx)(ey.sd,{code:(0,ew.w)(b),language:A,className:"text-xs"})})]}):(0,s.jsxs)("div",{className:"min-w-full table",children:[_.map((e,t)=>(0,s.jsxs)("div",{className:(0,i.cn)("table-row transition-colors",h.hoverColor),children:[(0,s.jsx)("div",{className:"table-cell text-right pr-3 pl-6 py-0.5 text-xs font-mono text-zinc-500 dark:text-zinc-500 select-none w-12 border-r border-zinc-200 dark:border-zinc-800 bg-zinc-50 dark:bg-zinc-900",children:t+1}),(0,s.jsx)("div",{className:"table-cell pl-3 py-0.5 pr-4 text-xs font-mono whitespace-pre-wrap text-zinc-800 dark:text-zinc-300",children:(0,ew.w)(e)||" "})]},t)),(0,s.jsx)("div",{className:"table-row h-4"})]}):(0,s.jsx)("div",{className:"flex items-center justify-center h-full p-12",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(L,{className:"h-12 w-12 mx-auto mb-4 text-zinc-400"}),(0,s.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400",children:"No source code to display"})]})})})}),(0,s.jsx)(eS.av,{value:"preview",className:"w-full flex-1 h-full mt-0 p-0 overflow-hidden",children:(0,s.jsxs)(K.F,{className:"h-full w-full min-h-0",children:[c&&!b?(0,s.jsx)(es,{icon:p,iconColor:h.color,bgColor:h.bgColor,title:h.progressMessage,filePath:w||"Processing file...",subtitle:"Please wait while the file is being processed",showProgress:!1}):"delete"===x?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:[(0,s.jsx)("div",{className:(0,i.cn)("w-20 h-20 rounded-full flex items-center justify-center mb-6",h.bgColor),children:(0,s.jsx)(p,{className:(0,i.cn)("h-10 w-10",h.color)})}),(0,s.jsx)("h3",{className:"text-xl font-semibold mb-6 text-zinc-900 dark:text-zinc-100",children:"File Deleted"}),(0,s.jsx)("div",{className:"bg-zinc-50 dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-lg p-4 w-full max-w-md text-center mb-4 shadow-sm",children:(0,s.jsx)("code",{className:"text-sm font-mono text-zinc-700 dark:text-zinc-300 break-all",children:w||"Unknown file path"})}),(0,s.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400",children:"This file has been permanently removed"})]}):b?S&&R?(0,s.jsx)("div",{className:"flex flex-col h-[calc(100vh-16rem)]",children:(0,s.jsx)("iframe",{src:R,title:"HTML Preview of ".concat(k),className:"flex-grow border-0",sandbox:"allow-same-origin allow-scripts"})}):z?(0,s.jsx)("div",{className:"p-1 py-0 prose dark:prose-invert prose-zinc max-w-none",children:(0,s.jsx)(ew.T,{content:(0,ew.w)(b)})}):C?(0,s.jsx)("div",{className:"h-full w-full p-4",children:(0,s.jsx)("div",{className:"h-[calc(100vh-17rem)] w-full bg-muted/20 border rounded-xl overflow-auto",children:(0,s.jsx)(ek.W,{content:(0,ew.w)(b)})})}):(0,s.jsx)("div",{className:"p-4",children:(0,s.jsx)("div",{className:"w-full h-full bg-muted/20 border rounded-xl px-4 py-2 pb-6",children:(0,s.jsx)("pre",{className:"text-sm font-mono text-zinc-800 dark:text-zinc-300 whitespace-pre-wrap break-words",children:(0,ew.w)(b)})})}):(0,s.jsx)("div",{className:"flex items-center justify-center h-full p-12",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(L,{className:"h-12 w-12 mx-auto mb-4 text-zinc-400"}),(0,s.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400",children:"No content to preview"})]})}),c&&b&&(0,s.jsx)("div",{className:"sticky bottom-4 right-4 float-right mr-4 mb-4",children:(0,s.jsxs)(Q.E,{className:"bg-blue-500/90 text-white border-none shadow-lg animate-pulse",children:[(0,s.jsx)(ee.A,{className:"h-3 w-3 animate-spin mr-1"}),"Streaming..."]})})]})})]}),(0,s.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,s.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:(0,s.jsxs)(Q.E,{variant:"outline",className:"py-0.5 h-6",children:[(0,s.jsx)(L,{className:"h-3 w-3"}),E?A.toUpperCase():y.toUpperCase()||"TEXT"]})}),(0,s.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:n&&!c?P(n):a?P(a):""})]})]})}):(0,s.jsx)(el,{name:d||"file-".concat(x),assistantContent:r,toolContent:l,assistantTimestamp:a,toolTimestamp:n,isSuccess:o,isStreaming:c})}var eJ=r(87712),eM=r(84616),eB=r(81422),e$=r(47863),eq=r(66474),eZ=r(46102);let eG=e=>{if(!e)return{filePath:null,oldStr:null,newStr:null};if("string"==typeof e){let t=e.trim();if(!(t.startsWith("{")||t.startsWith("[")))return console.debug("StrReplaceToolView: String content does not look like JSON, skipping parse"),{filePath:null,oldStr:null,newStr:null};try{console.debug("StrReplaceToolView: Attempting to parse JSON string:",e.substring(0,100)+"...");let t=JSON.parse(e);return console.debug("StrReplaceToolView: Successfully parsed JSON:",t),eG(t)}catch(t){return console.error("StrReplaceToolView: JSON parse error:",t,"Content:",e.substring(0,200)),{filePath:null,oldStr:null,newStr:null}}}if("object"!=typeof e)return{filePath:null,oldStr:null,newStr:null};if("tool_execution"in e&&"object"==typeof e.tool_execution){var t,r,s;let l=e.tool_execution,a=l.arguments||{};return console.debug("StrReplaceToolView: Extracted from new format:",{filePath:a.file_path,oldStr:a.old_str?"".concat(a.old_str.substring(0,50),"..."):null,newStr:a.new_str?"".concat(a.new_str.substring(0,50),"..."):null,success:null==(t=l.result)?void 0:t.success}),{filePath:a.file_path||null,oldStr:a.old_str||null,newStr:a.new_str||null,success:null==(r=l.result)?void 0:r.success,timestamp:null==(s=l.execution_details)?void 0:s.timestamp}}return"role"in e&&"content"in e&&"string"==typeof e.content?(console.debug("StrReplaceToolView: Found role/content structure with string content, parsing..."),eG(e.content)):"role"in e&&"content"in e&&"object"==typeof e.content?(console.debug("StrReplaceToolView: Found role/content structure with object content"),eG(e.content)):{filePath:null,oldStr:null,newStr:null}},eH=(e,t,r,s)=>{let l=t(e);if(l.toolResult){let e=l.arguments||{};return console.debug("StrReplaceToolView: Extracted from legacy format (extractToolData):",{filePath:l.filePath||e.file_path,oldStr:e.old_str?"".concat(e.old_str.substring(0,50),"..."):null,newStr:e.new_str?"".concat(e.new_str.substring(0,50),"..."):null}),{filePath:l.filePath||e.file_path||null,oldStr:e.old_str||null,newStr:e.new_str||null}}let a=r(e),n=s(e);return console.debug("StrReplaceToolView: Extracted from legacy format (fallback):",{filePath:a,oldStr:n.oldStr?"".concat(n.oldStr.substring(0,50),"..."):null,newStr:n.newStr?"".concat(n.newStr.substring(0,50),"..."):null}),{filePath:a,oldStr:n.oldStr,newStr:n.newStr}},eX=e=>e.replace(/\\n/g,"\n"),eY=(e,t)=>{let r=eX(e),s=eX(t),l=r.split("\n"),a=s.split("\n"),n=[],i=Math.max(l.length,a.length);for(let e=0;e<i;e++){let t=e<l.length?l[e]:null,r=e<a.length?a[e]:null;t===r?n.push({type:"unchanged",oldLine:t,newLine:r,lineNumber:e+1}):(null!==t&&n.push({type:"removed",oldLine:t,newLine:null,lineNumber:e+1}),null!==r&&n.push({type:"added",oldLine:null,newLine:r,lineNumber:e+1}))}return n},eQ=(e,t)=>{let r=eX(e),s=eX(t),l=0;for(;l<r.length&&l<s.length&&r[l]===s[l];)l++;let a=r.length,n=s.length;for(;a>l&&n>l&&r[a-1]===s[n-1];)a--,n--;let i=[];return l>0&&i.push({text:r.substring(0,l),type:"unchanged"}),a>l&&i.push({text:r.substring(l,a),type:"removed"}),n>l&&i.push({text:s.substring(l,n),type:"added"}),a<r.length&&i.push({text:r.substring(a),type:"unchanged"}),i},eK=e=>({additions:e.filter(e=>"added"===e.type).length,deletions:e.filter(e=>"removed"===e.type).length}),e0=e=>{let{lineDiff:t}=e;return(0,s.jsx)("div",{className:"bg-white dark:bg-zinc-950 font-mono text-sm overflow-x-auto -mt-2",children:(0,s.jsx)("table",{className:"w-full border-collapse",children:(0,s.jsx)("tbody",{children:t.map((e,t)=>(0,s.jsxs)("tr",{className:(0,i.cn)("hover:bg-zinc-50 dark:hover:bg-zinc-900","removed"===e.type&&"bg-red-50 dark:bg-red-950/30","added"===e.type&&"bg-emerald-50 dark:bg-emerald-950/30"),children:[(0,s.jsx)("td",{className:"w-10 text-right select-none py-0.5 pr-1 pl-4 text-xs text-zinc-500 dark:text-zinc-400 border-r border-zinc-200 dark:border-zinc-800",children:e.lineNumber}),(0,s.jsxs)("td",{className:"pl-2 py-0.5 w-6 select-none",children:["removed"===e.type&&(0,s.jsx)(eJ.A,{className:"h-3.5 w-3.5 text-red-500"}),"added"===e.type&&(0,s.jsx)(eM.A,{className:"h-3.5 w-3.5 text-emerald-500"})]}),(0,s.jsx)("td",{className:"w-full px-3 py-0.5",children:(0,s.jsxs)("div",{className:"overflow-x-auto max-w-full text-xs",children:["removed"===e.type&&(0,s.jsx)("span",{className:"text-red-700 dark:text-red-400",children:e.oldLine}),"added"===e.type&&(0,s.jsx)("span",{className:"text-emerald-700 dark:text-emerald-400",children:e.newLine}),"unchanged"===e.type&&(0,s.jsx)("span",{className:"text-zinc-700 dark:text-zinc-300",children:e.oldLine})]})})]},t))})})})},e1=e=>{let{lineDiff:t}=e;return(0,s.jsx)("div",{className:"bg-white dark:bg-zinc-950 font-mono text-sm overflow-x-auto -my-2",children:(0,s.jsxs)("table",{className:"w-full border-collapse",children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"border-b border-zinc-200 dark:border-zinc-800 text-xs",children:[(0,s.jsx)("th",{className:"p-2 text-left text-zinc-500 dark:text-zinc-400 w-1/2",children:"Removed"}),(0,s.jsx)("th",{className:"p-2 text-left text-zinc-500 dark:text-zinc-400 w-1/2",children:"Added"})]})}),(0,s.jsx)("tbody",{children:t.map((e,t)=>(0,s.jsxs)("tr",{children:[(0,s.jsx)("td",{className:(0,i.cn)("p-2 align-top","removed"===e.type?"bg-red-50 dark:bg-red-950/30 text-red-700 dark:text-red-400":"",null===e.oldLine?"bg-zinc-100 dark:bg-zinc-900":""),children:null!==e.oldLine?(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"w-8 text-right pr-2 select-none text-xs text-zinc-500 dark:text-zinc-400",children:e.lineNumber}),"removed"===e.type&&(0,s.jsx)(eJ.A,{className:"h-3.5 w-3.5 text-red-500 mt-0.5 mr-2 flex-shrink-0"}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsx)("span",{className:"break-all",children:e.oldLine})})]}):null}),(0,s.jsx)("td",{className:(0,i.cn)("p-2 align-top","added"===e.type?"bg-emerald-50 dark:bg-emerald-950/30 text-emerald-700 dark:text-emerald-400":"",null===e.newLine?"bg-zinc-100 dark:bg-zinc-900":""),children:null!==e.newLine?(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:"w-8 text-right pr-2 select-none text-xs text-zinc-500 dark:text-zinc-400",children:e.lineNumber}),"added"===e.type&&(0,s.jsx)(eM.A,{className:"h-3.5 w-3.5 text-emerald-500 mt-0.5 mr-2 flex-shrink-0"}),(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsx)("span",{className:"break-all",children:e.newLine})})]}):null})]},t))})]})})},e5=()=>(0,s.jsx)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:(0,s.jsxs)("div",{className:"text-center w-full max-w-xs",children:[(0,s.jsx)(v.A,{className:"h-16 w-16 mx-auto mb-6 text-amber-500"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-zinc-900 dark:text-zinc-100 mb-2",children:"Invalid String Replacement"}),(0,s.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400",children:"Could not extract the old string and new string from the request."})]})});var e2=r(94870),e4=r(5040),e3=r(5196),e8=r(24357),e9=r(71539),e6=r(69074);let e7=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){}return e},te=e=>{let t=e7(e);if(!t||"object"!=typeof t)return{url:null,urls:null,success:void 0,message:null,files:[],urlCount:0,timestamp:void 0};if("tool_execution"in t&&"object"==typeof t.tool_execution){var r,s,l,a;let e=t.tool_execution,n=e.arguments||{},i=null==(r=e.result)?void 0:r.output;if("string"==typeof i)try{i=JSON.parse(i)}catch(e){}let o=null,c=null;n.urls&&("string"==typeof n.urls?c=(null==(o=n.urls.split(",").map(e=>e.trim()))?void 0:o[0])||null:Array.isArray(n.urls)&&(c=(null==(o=n.urls)?void 0:o[0])||null));let d=[],m=0,u="";if("string"==typeof(null==(s=e.result)?void 0:s.output)){let t=e.result.output;u=t;let r=t.match(/Successfully scraped (?:all )?(\d+) URLs?/);m=r?parseInt(r[1]):0;let s=t.match(/- ([^\n]+\.json)/g);d=s?s.map(e=>e.replace("- ","")):[]}let x={url:c,urls:o,success:null==(l=e.result)?void 0:l.success,message:u||t.summary||null,files:d,urlCount:m,timestamp:null==(a=e.execution_details)?void 0:a.timestamp};return console.log("WebScrapeToolView: Extracted from new format:",{url:x.url,urlCount:x.urlCount,fileCount:x.files.length,success:x.success}),x}return"role"in t&&"content"in t?te(t.content):{url:null,urls:null,success:void 0,message:null,files:[],urlCount:0,timestamp:void 0}},tt=e=>{let t=G(e);if(!t)return null;let r=t.match(/<scrape-webpage[^>]*\s+urls=["']([^"']+)["']/);if(r)return r[1];let s=t.match(/https?:\/\/[^\s<>"]+/);return s?s[0]:null},tr=e=>{let t=G(e);if(!t)return{success:!1,message:"No output received",files:[],urlCount:0};let r=t.match(/output='([^']+)'/),s=r?r[1].replace(/\\n/g,"\n"):t,l=s.match(/Successfully scraped (?:all )?(\d+) URLs?/),a=l?parseInt(l[1]):0,n=s.match(/- ([^\n]+\.json)/g),i=n?n.map(e=>e.replace("- ","")):[];return{success:s.includes("Successfully scraped"),message:s,files:i,urlCount:a}},ts=e=>{let t=X(e);if(t.toolResult&&t.arguments)return console.log("WebScrapeToolView: Extracted from legacy format (extractToolData):",{url:t.url}),{url:t.url||null,urls:t.url?[t.url]:null,success:void 0,message:null,files:[],urlCount:0};let r=G(e);if(!r)return{url:null,urls:null,success:void 0,message:null,files:[],urlCount:0};let s=tt(r),l=tr(r);return console.log("WebScrapeToolView: Extracted from legacy format (manual parsing):",{url:s,fileCount:l.files.length,urlCount:l.urlCount}),{url:s,urls:s?[s]:null,success:l.success,message:l.message,files:l.files,urlCount:l.urlCount}};var tl=r(23562),ta=r(47924),tn=r(27213);let ti=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){}return e},to=e=>{let t=ti(e);if(!t||"object"!=typeof t)return{query:null,results:[],answer:null,images:[],success:void 0,timestamp:void 0};if("tool_execution"in t&&"object"==typeof t.tool_execution){var r,s,l,a;let e=t.tool_execution,n=e.arguments||{},i=null==(r=e.result)?void 0:r.output;if("string"==typeof i)try{i=JSON.parse(i)}catch(e){}return i=i||{},{query:n.query||(null==i?void 0:i.query)||null,results:(null==i||null==(s=i.results)?void 0:s.map(e=>({title:e.title||"",url:e.url||"",snippet:e.content||e.snippet||""})))||[],answer:(null==i?void 0:i.answer)||null,images:(null==i?void 0:i.images)||[],success:null==(l=e.result)?void 0:l.success,timestamp:null==(a=e.execution_details)?void 0:a.timestamp}}return"role"in t&&"content"in t?to(t.content):{query:null,results:[],answer:null,images:[],success:void 0,timestamp:void 0}},tc=e=>{let t=X(e);if(t.toolResult){let e=t.arguments||{};return console.log("WebSearchToolView: Extracted from legacy format (extractToolData):",{query:t.query||e.query,resultsCount:0}),{query:t.query||e.query||null,results:[],answer:null,images:[]}}let r=B(e);return console.log("WebSearchToolView: Extracted from legacy format (fallback):",{query:r,resultsCount:0}),{query:r,results:[],answer:null,images:[]}};var td=r(58009),tm=r(6262),tu=r(54481),tx=r(91788);let th=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){}return e},tp=e=>{let t=th(e);if(!t||"object"!=typeof t)return{filePath:null,description:null,success:void 0,timestamp:void 0,output:void 0};if("tool_execution"in t&&"object"==typeof t.tool_execution){var r,s,l,a;let e=t.tool_execution,n=e.arguments||{},i=null==(r=e.result)?void 0:r.output;if("string"==typeof i)try{i=JSON.parse(i)}catch(e){}let o={filePath:n.file_path||null,description:t.summary||null,success:null==(s=e.result)?void 0:s.success,timestamp:null==(l=e.execution_details)?void 0:l.timestamp,output:"string"==typeof(null==(a=e.result)?void 0:a.output)?e.result.output:null};return console.log("SeeImageToolView: Extracted from new format:",{filePath:o.filePath,hasDescription:!!o.description,success:o.success}),o}return"role"in t&&"content"in t?tp(t.content):{filePath:null,description:null,success:void 0,timestamp:void 0,output:void 0}};function tf(e){return e?e.replace(/\\n/g,"\n").replace(/\\t/g,"	").replace(/\\r/g,"").replace(/\\\\/g,"\\").replace(/\\"/g,'"').replace(/\\'/g,"'").split("\n")[0].trim():e}let tg=e=>{let t=X(e);if(t.toolResult&&t.arguments)return console.log("SeeImageToolView: Extracted from legacy format (extractToolData):",{filePath:t.arguments.file_path}),{filePath:t.arguments.file_path||null,description:null};let r=G(e);if(!r)return{filePath:null,description:null};let s=function(e){let t=G(e);if(!t)return null;console.log("Extracting file path from content:",t);try{let e=JSON.parse(t);if(e.content&&"string"==typeof e.content){let t=e.content,r=t.match(/<see-image\s+file_path=["']([^"']+)["'][^>]*><\/see-image>/i);if(r||(r=t.match(/<see-image[^>]*>([^<]+)<\/see-image>/i)))return tf(r[1])}}catch(e){}let r=t.match(/<see-image\s+file_path=["']([^"']+)["'][^>]*><\/see-image>/i);if(r||(r=t.match(/<see-image[^>]*>([^<]+)<\/see-image>/i)))return tf(r[1]);let s=t.match(/image\s*:\s*["']?([^,"'\s]+\.(jpg|jpeg|png|gif|svg|webp))["']?/i);if(s)return tf(s[1]);let l=t.match(/["']?([^,"'\s]+\.(jpg|jpeg|png|gif|svg|webp))["']?/i);return l?tf(l[1]):(console.log("No file path found in assistant content"),null)}(r),l=function(e){let t=G(e);if(!t)return null;try{let e=JSON.parse(t);if(e.content&&"string"==typeof e.content){let t=e.content.split(/<see-image/i);if(t.length>1)return t[0].trim()}}catch(e){}let r=t.split(/<see-image/i);return r.length>1?r[0].trim():null}(r);return console.log("SeeImageToolView: Extracted from legacy format (manual parsing):",{filePath:s,hasDescription:!!l}),{filePath:s,description:l}};var tb=r(64541);function tj(e){var t;let{src:r,alt:l,filePath:n,className:o}=e,[d,m]=(0,a.useState)(null),[u,x]=(0,a.useState)(!1),[h,p]=(0,a.useState)(0),[f,b]=(0,a.useState)(!1),[j,v]=(0,a.useState)(1),{session:N}=(0,tb.A)();return((0,a.useEffect)(()=>((async()=>{if(r.includes("/sandboxes/")&&r.includes("/files/content"))try{let e=await fetch(r,{headers:{Authorization:"Bearer ".concat(null==N?void 0:N.access_token)}});if(!e.ok)throw Error("Failed to load image: ".concat(e.status," ").concat(e.statusText));let t=await e.blob(),s=URL.createObjectURL(t);m(s)}catch(e){console.error("Error loading authenticated image:",e),x(!0)}else m(r)})(),x(!1),p(0),()=>{d&&d.startsWith("blob:")&&URL.revokeObjectURL(d)}),[r,null==N?void 0:N.access_token]),u)?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center w-full h-64 bg-gradient-to-b from-rose-50 to-rose-100 dark:from-rose-950/30 dark:to-rose-900/20 rounded-lg border border-rose-200 dark:border-rose-800 text-rose-700 dark:text-rose-300 shadow-inner",children:[(0,s.jsx)("div",{className:"bg-white dark:bg-black/30 p-3 rounded-full shadow-md mb-3",children:(0,s.jsx)(td.A,{className:"h-8 w-8 text-rose-500 dark:text-rose-400"})}),(0,s.jsx)("p",{className:"text-sm font-medium",children:"Unable to load image"}),(0,s.jsx)("p",{className:"text-xs text-rose-600/70 dark:text-rose-400/70 mt-1 max-w-xs text-center break-all",children:n})]}):d?(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)("div",{className:(0,i.cn)("overflow-hidden transition-all duration-300 rounded-3xl border bg-card mb-3",f?"cursor-zoom-out":"cursor-zoom-in"),children:(0,s.jsx)("div",{className:"relative flex items-center justify-center",children:(0,s.jsx)("img",{src:d,alt:l,onClick:()=>{b(!f),v(1)},className:(0,i.cn)("max-w-full object-contain transition-all duration-300 ease-in-out",f?"max-h-[80vh]":"max-h-[500px] hover:scale-[1.01]",o),style:{transform:f?"scale(".concat(j,")"):"none"},onError:()=>{h<3?(p(h+1),console.log("Image load failed (attempt ".concat(h+1,"). Trying alternative:"),n),0===h?m(n):1===h?n.startsWith("/")?x(!0):m("/".concat(n)):x(!0)):x(!0)}})})}),(0,s.jsxs)("div",{className:"flex items-center justify-between w-full px-2 py-2 bg-zinc-50 dark:bg-zinc-900 rounded-2xl border border-zinc-200 dark:border-zinc-800",children:[(0,s.jsxs)(Q.E,{variant:"secondary",className:"bg-white/90 dark:bg-black/70 text-zinc-700 dark:text-zinc-300 shadow-sm",children:[(0,s.jsx)(tn.A,{className:"h-3 w-3 mr-1"}),null==(t=n.split(".").pop())?void 0:t.toUpperCase()]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(g.$,{variant:"outline",size:"icon",className:"h-8 w-8 rounded-md bg-white dark:bg-zinc-800",onClick:e=>{e.stopPropagation(),v(e=>Math.max(e-.25,.5))},disabled:j<=.5,children:(0,s.jsx)(tm.A,{className:"h-4 w-4"})}),(0,s.jsxs)("span",{className:"text-xs font-mono px-2 text-zinc-700 dark:text-zinc-300 min-w-12 text-center",children:[Math.round(100*j),"%"]}),(0,s.jsx)(g.$,{variant:"outline",size:"icon",className:"h-8 w-8 rounded-md bg-white dark:bg-zinc-800",onClick:e=>{e.stopPropagation(),v(e=>Math.min(e+.25,3)),f||b(!0)},disabled:j>=3,children:(0,s.jsx)(tu.A,{className:"h-4 w-4"})}),(0,s.jsx)("span",{className:"w-px h-6 bg-zinc-200 dark:bg-zinc-700 mx-2"}),(0,s.jsx)(g.$,{variant:"outline",size:"icon",className:"h-8 w-8 rounded-md bg-white dark:bg-zinc-800",onClick:e=>{if(e.stopPropagation(),!d)return;let t=document.createElement("a");t.href=d,t.download=n.split("/").pop()||"image",document.body.appendChild(t),t.click(),document.body.removeChild(t)},title:"Download image",children:(0,s.jsx)(tx.A,{className:"h-4 w-4"})})]})]})]}):(0,s.jsx)("div",{className:"flex py-8 flex-col items-center justify-center w-full h-64 bg-gradient-to-b from-zinc-50 to-zinc-100 dark:from-zinc-900/50 dark:to-zinc-800/30 rounded-lg border-zinc-200 dark:border-zinc-700/50 shadow-inner",children:(0,s.jsxs)("div",{className:"space-y-2 w-full max-w-md py-8",children:[(0,s.jsx)(c.Skeleton,{className:"h-8 w-8 rounded-full mx-auto"}),(0,s.jsx)(c.Skeleton,{className:"h-4 w-48 mx-auto"}),(0,s.jsx)(c.Skeleton,{className:"h-64 w-full rounded-lg mt-4"}),(0,s.jsxs)("div",{className:"flex justify-center gap-2 mt-4",children:[(0,s.jsx)(c.Skeleton,{className:"h-8 w-8 rounded-full"}),(0,s.jsx)(c.Skeleton,{className:"h-8 w-8 rounded-full"}),(0,s.jsx)(c.Skeleton,{className:"h-8 w-8 rounded-full"})]})]})})}var tv=r(22431),tN=r(27265),tw=r(92138),tk=r(13089),ty=r(50492),tz=r(81497);let tS=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){}return e},tC=e=>{let t=tS(e);if(!t||"object"!=typeof t)return{text:null,attachments:null,status:null,success:void 0,timestamp:void 0};if("tool_execution"in t&&"object"==typeof t.tool_execution){var r,s,l,a;let e=t.tool_execution,n=e.arguments||{},i=null==(r=e.result)?void 0:r.output;if("string"==typeof i)try{i=JSON.parse(i)}catch(e){}let o=null;n.attachments&&("string"==typeof n.attachments?o=n.attachments.split(",").map(e=>e.trim()).filter(e=>e.length>0):Array.isArray(n.attachments)&&(o=n.attachments));let c=null;i&&"object"==typeof i&&i.status&&(c=i.status);let d={text:n.text||null,attachments:o,status:c||t.summary||null,success:null==(s=e.result)?void 0:s.success,timestamp:null==(l=e.execution_details)?void 0:l.timestamp};return console.log("AskToolView: Extracted from new format:",{hasText:!!d.text,attachmentCount:(null==(a=d.attachments)?void 0:a.length)||0,hasStatus:!!d.status,success:d.success}),d}return"role"in t&&"content"in t?tC(t.content):{text:null,attachments:null,status:null,success:void 0,timestamp:void 0}},tA=e=>{let t=X(e);if(t.toolResult&&t.arguments){console.log("AskToolView: Extracted from legacy format (extractToolData):",{hasText:!!t.arguments.text,attachmentCount:t.arguments.attachments?Array.isArray(t.arguments.attachments)?t.arguments.attachments.length:1:0});let e=null;return t.arguments.attachments&&(Array.isArray(t.arguments.attachments)?e=t.arguments.attachments:"string"==typeof t.arguments.attachments&&(e=t.arguments.attachments.split(",").map(e=>e.trim()).filter(e=>e.length>0))),{text:t.arguments.text||null,attachments:e,status:null}}let r=G(e);if(!r)return{text:null,attachments:null,status:null};let s=null,l=r.match(/attachments=["']([^"']*)["']/i);l&&(s=l[1].split(",").map(e=>e.trim()).filter(e=>e.length>0));let a=null,n=r.match(/<ask[^>]*>([^<]*)<\/ask>/i);return n&&(a=n[1].trim()),console.log("AskToolView: Extracted from legacy format (manual parsing):",{hasText:!!a,attachmentCount:(null==s?void 0:s.length)||0}),{text:a,attachments:s,status:null}};var tE=r(84332),t_=r(43453),tR=r(18186),tL=r(53311),tF=r(50142),tT=r(31834),tI=r(17580),tP=r(71366),tO=r(57340),tW=r(86151),tD=r(33109),tU=r(17576),tV=r(381),tJ=r(54213);let tM=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){}return e},tB=e=>{let t=tM(e);if(!t||"object"!=typeof t)return{serviceName:null,route:null,payload:null,endpoints:null,success:void 0,timestamp:void 0,output:void 0};if("tool_execution"in t&&"object"==typeof t.tool_execution){var r,s,l,a;let e=t.tool_execution,n=e.arguments||{},i=null==(r=e.result)?void 0:r.output;if("string"==typeof i)try{i=JSON.parse(i)}catch(e){}let o={serviceName:n.service_name||null,route:n.route||null,payload:n.payload||n,endpoints:i||null,success:null==(s=e.result)?void 0:s.success,timestamp:null==(l=e.execution_details)?void 0:l.timestamp,output:"string"==typeof(null==(a=e.result)?void 0:a.output)?e.result.output:null};return console.log("DataProviderToolView: Extracted from new format:",{serviceName:o.serviceName,route:o.route,hasPayload:!!o.payload,hasEndpoints:!!o.endpoints,success:o.success}),o}return"role"in t&&"content"in t?tB(t.content):{serviceName:null,route:null,payload:null,endpoints:null,success:void 0,timestamp:void 0,output:void 0}},t$=e=>{let t=e.match(/<execute-data-provider-call\b(?=[^>]*\bservice_name="([^"]+)")(?=[^>]*\broute="([^"]+)")[^>]*>/),r=null,s=null;t&&(r=t[1],s=t[2]);let l=e.match(/<execute-data-provider-call\b[^>]*>\s*(\{[\s\S]*?\})\s*<\/execute-data-provider-call>/),a=null;if(l){let e=l[1].trim();e=e.replace(/\\"/g,'"');try{a=JSON.parse(e)}catch(t){console.error("Failed to parse JSON content:",t),console.error("JSON string was:",e),a=e}}return{serviceName:r,route:s,jsonContent:a}},tq=e=>{let t=e.match(/<get-data-provider-endpoints\s+service_name="([^"]+)"\s*>/);return t?t[1]:null},tZ=e=>{let t=X(e);if(t.toolResult&&t.arguments)return console.log("DataProviderToolView: Extracted from legacy format (extractToolData):",{serviceName:t.arguments.service_name,route:t.arguments.route}),{serviceName:t.arguments.service_name||null,route:t.arguments.route||null,payload:t.arguments,endpoints:null};let r=G(e);if(!r)return{serviceName:null,route:null,payload:null,endpoints:null};let s=t$(r);if(s.serviceName||s.route)return console.log("DataProviderToolView: Extracted from legacy format (parseDataProviderCall):",{serviceName:s.serviceName,route:s.route}),{serviceName:s.serviceName,route:s.route,payload:s.jsonContent,endpoints:null};let l=tq(r);return l?(console.log("DataProviderToolView: Extracted service name from legacy format:",l),{serviceName:l,route:null,payload:null,endpoints:null}):{serviceName:null,route:null,payload:null,endpoints:null}},tG={linkedin:{name:"LinkedIn Data Provider",icon:tI.A,color:"from-blue-500 to-blue-600",bgColor:"bg-blue-50 dark:bg-blue-900/20",textColor:"text-blue-700 dark:text-blue-300",borderColor:"border-blue-200 dark:border-blue-800"},twitter:{name:"Twitter Data Provider",icon:tP.A,color:"from-sky-400 to-sky-500",bgColor:"bg-sky-50 dark:bg-sky-900/20",textColor:"text-sky-700 dark:text-sky-300",borderColor:"border-sky-200 dark:border-sky-800"},zillow:{name:"Zillow Data Provider",icon:tO.A,color:"from-emerald-500 to-emerald-600",bgColor:"bg-emerald-50 dark:bg-emerald-900/20",textColor:"text-emerald-700 dark:text-emerald-300",borderColor:"border-emerald-200 dark:border-emerald-800"},amazon:{name:"Amazon Data Provider",icon:tW.A,color:"from-orange-500 to-orange-600",bgColor:"bg-orange-50 dark:bg-orange-900/20",textColor:"text-orange-700 dark:text-orange-300",borderColor:"border-orange-200 dark:border-orange-800"},yahoo_finance:{name:"Yahoo Finance Data Provider",icon:tD.A,color:"from-purple-500 to-purple-600",bgColor:"bg-purple-50 dark:bg-purple-900/20",textColor:"text-purple-700 dark:text-purple-300",borderColor:"border-purple-200 dark:border-purple-800"},active_jobs:{name:"Active Jobs Data Provider",icon:tU.A,color:"from-indigo-500 to-indigo-600",bgColor:"bg-indigo-50 dark:bg-indigo-900/20",textColor:"text-indigo-700 dark:text-indigo-300",borderColor:"border-indigo-200 dark:border-indigo-800"}},tH={linkedin:{name:"LinkedIn Data Provider",icon:tI.A,color:"from-blue-500 to-blue-600",bgColor:"bg-blue-50 dark:bg-blue-900/20",textColor:"text-blue-700 dark:text-blue-300"},twitter:{name:"Twitter Data Provider",icon:tP.A,color:"from-sky-400 to-sky-500",bgColor:"bg-sky-50 dark:bg-sky-900/20",textColor:"text-sky-700 dark:text-sky-300"},zillow:{name:"Zillow Data Provider",icon:tO.A,color:"from-emerald-500 to-emerald-600",bgColor:"bg-emerald-50 dark:bg-emerald-900/20",textColor:"text-emerald-700 dark:text-emerald-300"},amazon:{name:"Amazon Data Provider",icon:tW.A,color:"from-orange-500 to-orange-600",bgColor:"bg-orange-50 dark:bg-orange-900/20",textColor:"text-orange-700 dark:text-orange-300"},yahoo_finance:{name:"Yahoo Finance Data Provider",icon:tD.A,color:"from-purple-500 to-purple-600",bgColor:"bg-purple-50 dark:bg-purple-900/20",textColor:"text-purple-700 dark:text-purple-300"},active_jobs:{name:"Active Jobs Data Provider",icon:tU.A,color:"from-indigo-500 to-indigo-600",bgColor:"bg-indigo-50 dark:bg-indigo-900/20",textColor:"text-indigo-700 dark:text-indigo-300"}};var tX=r(59964);let tY={"browser-navigate-to":ec,"browser-go-back":ec,"browser-wait":ec,"browser-click-element":ec,"browser-input-text":ec,"browser-send-keys":ec,"browser-switch-tab":ec,"browser-close-tab":ec,"browser-scroll-down":ec,"browser-scroll-up":ec,"browser-scroll-to-text":ec,"browser-get-dropdown-options":ec,"browser-select-dropdown-option":ec,"browser-drag-drop":ec,"browser-click-coordinates":ec,"execute-command":function(e){let{name:t="execute-command",assistantContent:r,toolContent:l,assistantTimestamp:n,toolTimestamp:i,isSuccess:o=!0,isStreaming:c=!1}=e,{resolvedTheme:d}=(0,em.D)(),[m,u]=(0,a.useState)(!0),{command:h,output:p,exitCode:f,sessionName:g,cwd:b,completed:w,actualIsSuccess:k,actualToolTimestamp:y,actualAssistantTimestamp:z}=ep(r,l,o,i,n),S="check-command-output"===t?g:h,C="check-command-output"===t?"Session":"Command",A=O(t),E=a.useMemo(()=>{if(!p)return[];let e=p;try{if("string"==typeof p&&(p.trim().startsWith("{")||p.trim().startsWith("{"))){let t=JSON.parse(p);t&&"object"==typeof t&&t.output&&(e=t.output)}}catch(e){}return(e=(e=(e=(e=String(e)).replace(/\\\\/g,"\\")).replace(/\\n/g,"\n").replace(/\\t/g,"	").replace(/\\"/g,'"').replace(/\\'/g,"'")).replace(/\\u([0-9a-fA-F]{4})/g,(e,t)=>String.fromCharCode(parseInt(t,16)))).split("\n")},[p]),_=E.length>10,R=E.slice(0,10),L=m?E:R;return(0,s.jsxs)(Y.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,s.jsx)(Y.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,s.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"relative p-2 rounded-lg bg-gradient-to-br from-purple-500/20 to-purple-600/10 border border-purple-500/20",children:(0,s.jsx)(ed.A,{className:"w-5 h-5 text-purple-500 dark:text-purple-400"})}),(0,s.jsx)("div",{children:(0,s.jsx)(Y.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:A})})]}),!c&&(0,s.jsxs)(Q.E,{variant:"secondary",className:k?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[k?(0,s.jsx)(j.A,{className:"h-3.5 w-3.5 mr-1"}):(0,s.jsx)(v.A,{className:"h-3.5 w-3.5 mr-1"}),k?"check-command-output"===t?"Output retrieved successfully":"Command executed successfully":"check-command-output"===t?"Failed to retrieve output":"Command failed"]})]})}),(0,s.jsx)(Y.Wu,{className:"p-0 h-full flex-1 overflow-hidden relative",children:c?(0,s.jsx)(es,{icon:ed.A,iconColor:"text-purple-500 dark:text-purple-400",bgColor:"bg-gradient-to-b from-purple-100 to-purple-50 shadow-inner dark:from-purple-800/40 dark:to-purple-900/60 dark:shadow-purple-950/20",title:"check-command-output"===t?"Checking command output":"Executing command",filePath:S||"Processing command...",showProgress:!0}):S?(0,s.jsx)(K.F,{className:"h-full w-full",children:(0,s.jsxs)("div",{className:"p-4",children:[p&&(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsxs)("div",{className:"bg-zinc-100 dark:bg-neutral-900 rounded-lg overflow-hidden border border-zinc-200/20",children:[(0,s.jsxs)("div",{className:"bg-zinc-300 dark:bg-neutral-800 flex items-center justify-between dark:border-zinc-700/50",children:[(0,s.jsxs)("div",{className:"bg-zinc-200 w-full dark:bg-zinc-800 px-4 py-2 flex items-center gap-2",children:[(0,s.jsx)(ed.A,{className:"h-4 w-4 text-zinc-600 dark:text-zinc-400"}),(0,s.jsx)("span",{className:"text-sm font-medium text-zinc-700 dark:text-zinc-300",children:"Terminal output"})]}),null!==f&&0!==f&&(0,s.jsxs)(Q.E,{variant:"outline",className:"text-xs h-5 border-red-700/30 text-red-400",children:[(0,s.jsx)(v.A,{className:"h-3 w-3 mr-1"}),"Error"]})]}),(0,s.jsx)("div",{className:"p-4 max-h-96 overflow-auto scrollbar-hide",children:(0,s.jsxs)("pre",{className:"text-xs text-zinc-600 dark:text-zinc-300 font-mono whitespace-pre-wrap break-all overflow-visible",children:[L.map((e,t)=>(0,s.jsx)("div",{className:"py-0.5 bg-transparent",children:e||" "},t)),!m&&_&&(0,s.jsxs)("div",{className:"text-zinc-500 mt-2 border-t border-zinc-700/30 pt-2",children:["+ ",E.length-10," more lines"]})]})})]})}),!p&&!c&&(0,s.jsx)("div",{className:"bg-black rounded-lg overflow-hidden border border-zinc-700/20 shadow-md p-6 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(x.A,{className:"h-8 w-8 text-zinc-500 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-zinc-400 text-sm",children:"No output received"})]})})]})}):(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:[(0,s.jsx)("div",{className:"w-20 h-20 rounded-full flex items-center justify-center mb-6 bg-gradient-to-b from-zinc-100 to-zinc-50 shadow-inner dark:from-zinc-800/40 dark:to-zinc-900/60",children:(0,s.jsx)(ed.A,{className:"h-10 w-10 text-zinc-400 dark:text-zinc-600"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold mb-2 text-zinc-900 dark:text-zinc-100",children:"check-command-output"===t?"No Session Found":"No Command Found"}),(0,s.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400 text-center max-w-md",children:"check-command-output"===t?"No session name was detected. Please provide a valid session name to check.":"No command was detected. Please provide a valid command to execute."})]})}),(0,s.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,s.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:!c&&S&&(0,s.jsxs)(Q.E,{variant:"outline",className:"h-6 py-0.5 bg-zinc-50 dark:bg-zinc-900",children:[(0,s.jsx)(ed.A,{className:"h-3 w-3 mr-1"}),C]})}),(0,s.jsxs)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400 flex items-center gap-2",children:[(0,s.jsx)(N.A,{className:"h-3.5 w-3.5"}),y&&!c?P(y):z?P(z):""]})]})]})},"check-command-output":el,"terminate-command":function(e){let{name:t="terminate-command",assistantContent:r,toolContent:l,assistantTimestamp:n,toolTimestamp:o,isSuccess:c=!0,isStreaming:d=!1}=e,{resolvedTheme:m}=(0,em.D)(),[u,h]=(0,a.useState)(0),[p,f]=(0,a.useState)(!0),{sessionName:g,output:b,actualIsSuccess:w,actualToolTimestamp:k,actualAssistantTimestamp:y}=ep(r,l,c,o,n),z=a.useMemo(()=>{if(g)return g;if(!r)return null;let e=G(r);if(!e)return null;try{let t=JSON.parse(e);if(t.content){let e=t.content.match(/<terminate-command[^>]*session_name=["']([^"']+)["'][^>]*>/);if(e)return e[1].trim()}}catch(r){let t=e.match(/<terminate-command[^>]*session_name=["']([^"']+)["'][^>]*>/);if(t)return t[1].trim()}return null},[r,g]),S=(null==z?void 0:z.trim())||g,C=O(t)||"Terminate Session",A=a.useMemo(()=>{if(!b)return!1;let e=b.toLowerCase();if(e.includes("does not exist"))return!1;if(e.includes("terminated")||e.includes("killed"))return!0;if("string"==typeof l){let e=l.match(/ToolResult\(success=(true|false)/i);if(e)return"true"===e[1].toLowerCase()}return w},[b,w,l]);(0,a.useEffect)(()=>{if(d){let e=setInterval(()=>{h(t=>t>=95?(clearInterval(e),t):t+5)},300);return()=>clearInterval(e)}h(100)},[d]);let E=a.useMemo(()=>{if(!b)return[];let e=b;try{if("string"==typeof b&&(b.trim().startsWith("{")||b.trim().startsWith("{"))){let t=JSON.parse(b);t&&"object"==typeof t&&t.output&&(e=t.output)}}catch(e){}return(e=(e=(e=(e=String(e)).replace(/\\\\/g,"\\")).replace(/\\n/g,"\n").replace(/\\t/g,"	").replace(/\\"/g,'"').replace(/\\'/g,"'")).replace(/\\u([0-9a-fA-F]{4})/g,(e,t)=>String.fromCharCode(parseInt(t,16)))).split("\n")},[b]),_=E.length>10,R=E.slice(0,10),L=p?E:R;return(0,s.jsxs)(Y.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,s.jsx)(Y.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,s.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"relative p-2 rounded-lg bg-gradient-to-br from-red-500/20 to-red-600/10 border border-red-500/20",children:(0,s.jsx)(tv.A,{className:"w-5 h-5 text-red-500 dark:text-red-400"})}),(0,s.jsx)("div",{children:(0,s.jsx)(Y.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:C})})]}),!d&&(0,s.jsxs)(Q.E,{variant:"secondary",className:A?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[A?(0,s.jsx)(j.A,{className:"h-3.5 w-3.5 mr-1"}):(0,s.jsx)(v.A,{className:"h-3.5 w-3.5 mr-1"}),A?"Session terminated":"Termination failed"]})]})}),(0,s.jsx)(Y.Wu,{className:"p-0 h-full flex-1 overflow-hidden relative",children:d?(0,s.jsx)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:(0,s.jsxs)("div",{className:"text-center w-full max-w-xs",children:[(0,s.jsx)("div",{className:"w-16 h-16 rounded-full mx-auto mb-6 flex items-center justify-center bg-gradient-to-b from-red-100 to-red-50 shadow-inner dark:from-red-800/40 dark:to-red-900/60 dark:shadow-red-950/20",children:(0,s.jsx)(ee.A,{className:"h-8 w-8 animate-spin text-red-500 dark:text-red-400"})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-zinc-900 dark:text-zinc-100 mb-2",children:"Terminating session"}),(0,s.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400 mb-6",children:(0,s.jsx)("span",{className:"font-mono text-xs break-all",children:S||"Processing termination..."})}),(0,s.jsx)(er,{value:u,className:"w-full h-1"}),(0,s.jsxs)("p",{className:"text-xs text-zinc-400 dark:text-zinc-500 mt-2",children:[u,"%"]})]})}):S?(0,s.jsx)(K.F,{className:"h-full w-full",children:(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsxs)("div",{className:"mb-4 bg-zinc-100 dark:bg-neutral-900 rounded-lg overflow-hidden border border-zinc-200 dark:border-zinc-800",children:[(0,s.jsxs)("div",{className:"bg-zinc-200 dark:bg-zinc-800 px-4 py-2 flex items-center gap-2",children:[(0,s.jsx)(tN.A,{className:"h-4 w-4 text-zinc-600 dark:text-zinc-400"}),(0,s.jsx)("span",{className:"text-sm font-medium text-zinc-700 dark:text-zinc-300",children:"Session"})]}),(0,s.jsxs)("div",{className:"p-4 font-mono text-sm text-zinc-700 dark:text-zinc-300 flex gap-2",children:[(0,s.jsx)("span",{className:"text-red-500 dark:text-red-400 select-none",children:"●"}),(0,s.jsx)("code",{className:"flex-1 break-all",children:S})]})]}),b&&(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,s.jsxs)("h3",{className:"text-sm font-medium text-zinc-700 dark:text-zinc-300 flex items-center",children:[(0,s.jsx)(tw.A,{className:"h-4 w-4 mr-2 text-zinc-500 dark:text-zinc-400"}),"Result"]}),(0,s.jsx)(Q.E,{className:(0,i.cn)("ml-2",A?"bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-400":"bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400"),children:A?"Success":"Failed"})]}),(0,s.jsxs)("div",{className:"bg-zinc-100 dark:bg-neutral-900 rounded-lg overflow-hidden border border-zinc-200/20",children:[(0,s.jsxs)("div",{className:"bg-zinc-300 dark:bg-neutral-800 flex items-center justify-between dark:border-zinc-700/50",children:[(0,s.jsxs)("div",{className:"bg-zinc-200 w-full dark:bg-zinc-800 px-4 py-2 flex items-center gap-2",children:[(0,s.jsx)(ed.A,{className:"h-4 w-4 text-zinc-600 dark:text-zinc-400"}),(0,s.jsx)("span",{className:"text-sm font-medium text-zinc-700 dark:text-zinc-300",children:"Termination output"})]}),!A&&(0,s.jsxs)(Q.E,{variant:"outline",className:"text-xs h-5 border-red-700/30 text-red-400",children:[(0,s.jsx)(v.A,{className:"h-3 w-3 mr-1"}),"Error"]})]}),(0,s.jsx)("div",{className:"p-4 max-h-96 overflow-auto scrollbar-hide",children:(0,s.jsxs)("pre",{className:"text-xs text-zinc-600 dark:text-zinc-300 font-mono whitespace-pre-wrap break-all overflow-visible",children:[L.map((e,t)=>(0,s.jsx)("div",{className:"py-0.5 bg-transparent",children:e||" "},t)),!p&&_&&(0,s.jsxs)("div",{className:"text-zinc-500 mt-2 border-t border-zinc-700/30 pt-2",children:["+ ",E.length-10," more lines"]})]})})]})]}),!b&&!d&&(0,s.jsx)("div",{className:"bg-black rounded-lg overflow-hidden border border-zinc-700/20 shadow-md p-6 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(x.A,{className:"h-8 w-8 text-zinc-500 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-zinc-400 text-sm",children:"No output received"})]})})]})}):(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:[(0,s.jsx)("div",{className:"w-20 h-20 rounded-full flex items-center justify-center mb-6 bg-gradient-to-b from-zinc-100 to-zinc-50 shadow-inner dark:from-zinc-800/40 dark:to-zinc-900/60",children:(0,s.jsx)(tv.A,{className:"h-10 w-10 text-zinc-400 dark:text-zinc-600"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold mb-2 text-zinc-900 dark:text-zinc-100",children:"No Session Found"}),(0,s.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400 text-center max-w-md",children:"No session name was detected. Please provide a valid session to terminate."})]})}),(0,s.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,s.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:!d&&S&&(0,s.jsxs)(Q.E,{variant:"outline",className:"h-6 py-0.5 bg-zinc-50 dark:bg-zinc-900",children:[(0,s.jsx)(tv.A,{className:"h-3 w-3 mr-1"}),"Terminate"]})}),(0,s.jsxs)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400 flex items-center gap-2",children:[(0,s.jsx)(N.A,{className:"h-3.5 w-3.5"}),k&&!d?P(k):y?P(y):""]})]})]})},"list-commands":el,"create-file":eV,"delete-file":eV,"full-file-rewrite":eV,"read-file":eV,"str-replace":function(e){let{name:t="str-replace",assistantContent:r,toolContent:l,assistantTimestamp:n,toolTimestamp:i,isSuccess:o=!0,isStreaming:c=!1}=e,[d,m]=(0,a.useState)(!0),[u,h]=(0,a.useState)("unified"),p=null,f=null,b=null,N=o,w=i,k=n,y=eG(r),z=eG(l);if(y.filePath||y.oldStr||y.newStr)p=y.filePath,f=y.oldStr,b=y.newStr,void 0!==y.success&&(N=y.success),y.timestamp&&(k=y.timestamp);else if(z.filePath||z.oldStr||z.newStr)p=z.filePath,f=z.oldStr,b=z.newStr,void 0!==z.success&&(N=z.success),z.timestamp&&(w=z.timestamp);else{let e=eH(r,X,U,J),t=eH(l,X,U,J);p=e.filePath||t.filePath,f=e.oldStr||t.oldStr,b=e.newStr||t.newStr}if(p||(p=U(r)||U(l)),!f||!b){let e=J(r),t=J(l);f=f||e.oldStr||t.oldStr,b=b||e.newStr||t.newStr}let S=O(t),C=f&&b?eY(f,b):[];f&&b&&eQ(f,b);let A=eK(C),E=!c&&(!f||!b)&&(r||l);return(0,s.jsxs)(Y.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,s.jsx)(Y.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,s.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"relative p-2 rounded-lg bg-gradient-to-br from-purple-500/20 to-purple-600/10 border border-purple-500/20",children:(0,s.jsx)(eB.A,{className:"w-5 h-5 text-purple-500 dark:text-purple-400"})}),(0,s.jsx)(Y.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:S})]}),!c&&(0,s.jsxs)(Q.E,{variant:"secondary",className:N?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[N?(0,s.jsx)(j.A,{className:"h-3.5 w-3.5 mr-1"}):(0,s.jsx)(v.A,{className:"h-3.5 w-3.5 mr-1"}),N?"Replacement completed":"Replacement failed"]}),c&&(0,s.jsxs)(Q.E,{className:"bg-gradient-to-b from-blue-200 to-blue-100 text-blue-700 dark:from-blue-800/50 dark:to-blue-900/60 dark:text-blue-300",children:[(0,s.jsx)(ee.A,{className:"h-3.5 w-3.5 animate-spin mr-1"}),"Processing replacement"]})]})}),(0,s.jsx)(Y.Wu,{className:"p-0 h-full flex-1 overflow-hidden relative",children:c?(0,s.jsx)(es,{icon:eB.A,iconColor:"text-purple-500 dark:text-purple-400",bgColor:"bg-gradient-to-b from-purple-100 to-purple-50 shadow-inner dark:from-purple-800/40 dark:to-purple-900/60 dark:shadow-purple-950/20",title:"Processing String Replacement",filePath:p||"Processing file...",progressText:"Analyzing text patterns",subtitle:"Please wait while the replacement is being processed"}):E?(0,s.jsx)(e5,{}):(0,s.jsx)(K.F,{className:"h-full w-full",children:(0,s.jsx)("div",{className:"p-4",children:(0,s.jsxs)("div",{className:"bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-lg overflow-hidden mb-4",children:[(0,s.jsxs)("div",{className:"p-3 border-b border-zinc-200 dark:border-zinc-800 bg-accent flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(L.A,{className:"h-4 w-4 mr-2 text-zinc-500 dark:text-zinc-400"}),(0,s.jsx)("code",{className:"text-xs font-mono text-zinc-700 dark:text-zinc-300",children:p||"Unknown file"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)("div",{className:"flex items-center text-xs text-zinc-500 dark:text-zinc-400 gap-3",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(eM.A,{className:"h-3.5 w-3.5 text-emerald-500 mr-1"}),(0,s.jsx)("span",{children:A.additions})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(eJ.A,{className:"h-3.5 w-3.5 text-red-500 mr-1"}),(0,s.jsx)("span",{children:A.deletions})]})]}),(0,s.jsx)(eZ.Bc,{children:(0,s.jsxs)(eZ.m_,{children:[(0,s.jsx)(eZ.k$,{asChild:!0,children:(0,s.jsx)(g.$,{variant:"ghost",size:"sm",className:"h-7 w-7 p-0",onClick:()=>m(!d),children:d?(0,s.jsx)(e$.A,{className:"h-4 w-4"}):(0,s.jsx)(eq.A,{className:"h-4 w-4"})})}),(0,s.jsx)(eZ.ZI,{children:(0,s.jsx)("p",{children:d?"Collapse":"Expand"})})]})})]})]}),d&&(0,s.jsx)("div",{children:(0,s.jsxs)(eS.tU,{value:u,onValueChange:e=>h(e),className:"w-auto",children:[(0,s.jsx)("div",{className:"border-b border-zinc-200 dark:border-zinc-800 bg-zinc-50 dark:bg-zinc-900 p-2 flex justify-end",children:(0,s.jsxs)(eS.j7,{className:"h-7 p-0.5",children:[(0,s.jsx)(eS.Xi,{value:"unified",className:"text-xs h-6 px-2",children:"Unified"}),(0,s.jsx)(eS.Xi,{value:"split",className:"text-xs h-6 px-2",children:"Split"})]})}),(0,s.jsx)(eS.av,{value:"unified",className:"m-0 pb-4",children:(0,s.jsx)(e0,{lineDiff:C})}),(0,s.jsx)(eS.av,{value:"split",className:"m-0",children:(0,s.jsx)(e1,{lineDiff:C})})]})})]})})})}),(0,s.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"h-full flex items-center gap-2 text-xs text-zinc-500 dark:text-zinc-400",children:[!c&&(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[N?(0,s.jsx)(j.A,{className:"h-3.5 w-3.5 text-emerald-500 mr-1"}):(0,s.jsx)(v.A,{className:"h-3.5 w-3.5 text-red-500 mr-1"}),(0,s.jsx)("span",{children:N?"String replacement successful":"String replacement failed"})]}),c&&(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(x.A,{className:"h-3.5 w-3.5 text-blue-500 animate-spin mr-1"}),(0,s.jsx)("span",{children:"Processing replacement..."})]})]}),(0,s.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:w&&!c?P(w):k?P(k):""})]})]})},"web-search":function(e){let{name:t="web-search",assistantContent:r,toolContent:l,assistantTimestamp:n,toolTimestamp:o,isSuccess:c=!0,isStreaming:d=!1}=e,{resolvedTheme:m}=(0,em.D)(),[u,x]=(0,a.useState)({}),{query:h,searchResults:p,answer:f,images:b,actualIsSuccess:w,actualToolTimestamp:k,actualAssistantTimestamp:y}=function(e,t,r,s,l){let a=null,n=[],i=null,o=[],c=r,d=s,m=l,u=to(e),x=to(t);if(console.log("WebSearchToolView: Format detection results:",{assistantNewFormat:{hasQuery:!!u.query,resultsCount:u.results.length,hasAnswer:!!u.answer,imagesCount:u.images.length},toolNewFormat:{hasQuery:!!x.query,resultsCount:x.results.length,hasAnswer:!!x.answer,imagesCount:x.images.length}}),u.query||u.results.length>0)a=u.query,n=u.results,i=u.answer,o=u.images,void 0!==u.success&&(c=u.success),u.timestamp&&(m=u.timestamp),console.log("WebSearchToolView: Using assistant new format data");else if(x.query||x.results.length>0)a=x.query,n=x.results,i=x.answer,o=x.images,void 0!==x.success&&(c=x.success),x.timestamp&&(d=x.timestamp),console.log("WebSearchToolView: Using tool new format data");else{let r=tc(e),s=tc(t);a=r.query||s.query;let l=Z(t);if(n=l,console.log("WebSearchToolView: Using legacy format data:",{query:a,legacyResultsCount:l.length,firstLegacyResult:l[0]}),t)try{let e;(e="string"==typeof t?JSON.parse(t):"object"==typeof t&&null!==t?t:{}).answer&&"string"==typeof e.answer&&(i=e.answer),e.images&&Array.isArray(e.images)&&(o=e.images)}catch(e){}}if(a||(a=B(e)||B(t)),0===n.length){let e=Z(t);n=e,console.log("WebSearchToolView: Fallback extraction results:",e.length)}return console.log("WebSearchToolView: Final extracted data:",{query:a,searchResultsCount:n.length,hasAnswer:!!i,imagesCount:o.length,actualIsSuccess:c,firstResult:n[0]}),{query:a,searchResults:n,answer:i,images:o,actualIsSuccess:c,actualToolTimestamp:d,actualAssistantTimestamp:m}}(r,l,c,o,n),z=O(t),C=e=>{try{let t=new URL(e).hostname;return"https://www.google.com/s2/favicons?domain=".concat(t,"&sz=128")}catch(e){return null}},A=e=>{let{url:t,title:r}=e;return t.includes("news")||t.includes("article")||r.includes("News")?{icon:S.A,label:"Article"}:t.includes("wiki")?{icon:e4.A,label:"Wiki"}:t.includes("blog")?{icon:tl.A,label:"Blog"}:{icon:ei.A,label:"Website"}};return(0,s.jsxs)(Y.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,s.jsx)(Y.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,s.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"relative p-2 rounded-xl bg-gradient-to-br from-blue-500/20 to-blue-600/10 border border-blue-500/20",children:(0,s.jsx)(ta.A,{className:"w-5 h-5 text-blue-500 dark:text-blue-400"})}),(0,s.jsx)("div",{children:(0,s.jsx)(Y.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:z})})]}),!d&&(0,s.jsxs)(Q.E,{variant:"secondary",className:w?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[w?(0,s.jsx)(j.A,{className:"h-3.5 w-3.5"}):(0,s.jsx)(v.A,{className:"h-3.5 w-3.5"}),w?"Search completed successfully":"Search failed"]})]})}),(0,s.jsx)(Y.Wu,{className:"p-0 h-full flex-1 overflow-hidden relative",children:d&&0===p.length&&!f?(0,s.jsx)(es,{icon:ta.A,iconColor:"text-blue-500 dark:text-blue-400",bgColor:"bg-gradient-to-b from-blue-100 to-blue-50 shadow-inner dark:from-blue-800/40 dark:to-blue-900/60 dark:shadow-blue-950/20",title:"Searching the web",filePath:h,showProgress:!0}):p.length>0||f?(0,s.jsx)(K.F,{className:"h-full w-full",children:(0,s.jsxs)("div",{className:"p-4 py-0 my-4",children:[b.length>0&&(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("h3",{className:"text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-3 flex items-center",children:[(0,s.jsx)(tn.A,{className:"h-4 w-4 mr-2 opacity-70"}),"Images"]}),(0,s.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 gap-3 mb-1",children:b.slice(0,6).map((e,t)=>(0,s.jsxs)("a",{href:e,target:"_blank",rel:"noopener noreferrer",className:"group relative overflow-hidden rounded-lg border border-zinc-200 dark:border-zinc-800 bg-zinc-100 dark:bg-zinc-900 hover:border-blue-300 dark:hover:border-blue-700 transition-colors shadow-sm hover:shadow-md",children:[(0,s.jsx)("img",{src:e,alt:"Search result ".concat(t+1),className:"object-cover w-full h-32 group-hover:opacity-90 transition-opacity",onError:e=>{let t=e.target;t.src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23888' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Ccircle cx='8.5' cy='8.5' r='1.5'%3E%3C/circle%3E%3Cpolyline points='21 15 16 10 5 21'%3E%3C/polyline%3E%3C/svg%3E",t.classList.add("p-4")}}),(0,s.jsx)("div",{className:"absolute top-0 right-0 p-1",children:(0,s.jsx)(Q.E,{variant:"secondary",className:"bg-black/60 hover:bg-black/70 text-white border-none shadow-md",children:(0,s.jsx)(en.A,{className:"h-3 w-3"})})})]},t))}),b.length>6&&(0,s.jsxs)(g.$,{variant:"outline",size:"sm",className:"mt-2 text-xs",children:["View ",b.length-6," more images"]})]}),p.length>0&&(0,s.jsxs)("div",{className:"text-sm font-medium text-zinc-800 dark:text-zinc-200 mb-4 flex items-center justify-between",children:[(0,s.jsxs)("span",{children:["Search Results (",p.length,")"]}),(0,s.jsxs)(Q.E,{variant:"outline",className:"text-xs font-normal",children:[(0,s.jsx)(N.A,{className:"h-3 w-3 mr-1.5 opacity-70"}),new Date().toLocaleDateString()]})]}),(0,s.jsx)("div",{className:"space-y-4",children:p.map((e,t)=>{let{icon:r,label:l}=A(e),a=u[t]||!1,n=C(e.url);return(0,s.jsxs)("div",{className:"bg-card border rounded-lg shadow-sm hover:shadow transition-shadow",children:[(0,s.jsx)("div",{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-start gap-3 mb-2",children:[n&&(0,s.jsx)("img",{src:n,alt:"",className:"w-5 h-5 mt-1 rounded",onError:e=>{e.target.style.display="none"}}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("div",{className:"flex items-center gap-2 mb-1",children:(0,s.jsxs)(Q.E,{variant:"outline",className:"text-xs px-2 py-0 h-5 font-normal bg-zinc-50 dark:bg-zinc-800",children:[(0,s.jsx)(r,{className:"h-3 w-3 mr-1 opacity-70"}),l]})}),(0,s.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"text-md font-medium text-blue-600 dark:text-blue-400 hover:underline line-clamp-1 mb-1",children:(0,i.W5)(q(e.title),50)}),(0,s.jsxs)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400 mb-2 flex items-center",children:[(0,s.jsx)(ei.A,{className:"h-3 w-3 mr-1.5 flex-shrink-0 opacity-70"}),(0,i.W5)(q(e.url),70)]})]})]})}),a&&(0,s.jsxs)("div",{className:"bg-zinc-50 px-4 dark:bg-zinc-800/50 border-t border-zinc-200 dark:border-zinc-800 p-3 flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:["Source: ",q(e.url)]}),(0,s.jsx)(g.$,{variant:"outline",size:"sm",className:"h-7 text-xs bg-white dark:bg-zinc-900",asChild:!0,children:(0,s.jsxs)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",children:[(0,s.jsx)(en.A,{className:"h-3 w-3"}),"Visit Site"]})})]})]},t)})})]})}):(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:[(0,s.jsx)("div",{className:"w-20 h-20 rounded-full flex items-center justify-center mb-6 bg-gradient-to-b from-zinc-100 to-zinc-50 shadow-inner dark:from-zinc-800/40 dark:to-zinc-900/60",children:(0,s.jsx)(ta.A,{className:"h-10 w-10 text-zinc-400 dark:text-zinc-600"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold mb-2 text-zinc-900 dark:text-zinc-100",children:"No Results Found"}),(0,s.jsx)("div",{className:"bg-zinc-50 dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-lg p-4 w-full max-w-md text-center mb-4 shadow-sm",children:(0,s.jsx)("code",{className:"text-sm font-mono text-zinc-700 dark:text-zinc-300 break-all",children:h||"Unknown query"})}),(0,s.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400",children:"Try refining your search query for better results"})]})}),(0,s.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,s.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:!d&&p.length>0&&(0,s.jsxs)(Q.E,{variant:"outline",className:"h-6 py-0.5",children:[(0,s.jsx)(ei.A,{className:"h-3 w-3"}),p.length," results"]})}),(0,s.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:k&&!d?P(k):y?P(y):""})]})]})},"crawl-webpage":function(e){let{name:t="crawl-webpage",assistantContent:r,toolContent:l,assistantTimestamp:n,toolTimestamp:o,isSuccess:c=!0,isStreaming:d=!1}=e,{resolvedTheme:m}=(0,em.D)(),[u,x]=(0,a.useState)(0),[h,p]=(0,a.useState)(!1),f=X(r),b=X(l),N=null;f.toolResult?N=f.url:b.toolResult&&(N=b.url),N||(N=function(e){let t=G(e);if(!t)return null;try{let e=JSON.parse(t);if(e.content){let t=e.content.match(/<(?:crawl|scrape)-webpage[^>]*\s+url=["'](https?:\/\/[^"']+)["']/i);if(t)return t[1]}}catch(e){}let r=t.match(/<(?:crawl|scrape)-webpage[^>]*\s+url=["'](https?:\/\/[^"']+)["']/i)||t.match(/url=["'](https?:\/\/[^"']+)["']/i);return r?r[1]:null}(r));let w=function(e){let t=G(e);if(!t)return null;try{let e=JSON.parse(t);if(e.content&&"string"==typeof e.content){let t=e.content.match(/<tool_result[^>]*>\s*<(?:crawl|scrape)-webpage[^>]*>([\s\S]*?)<\/(?:crawl|scrape)-webpage>\s*<\/tool_result>/);if(t)try{let e=t[1],r=e.match(/ToolResult\(.*?output='([\s\S]*?)'.*?\)/);if(r)try{let e=JSON.parse(r[1].replace(/\\\\n/g,"\\n").replace(/\\\\u/g,"\\u"));if(Array.isArray(e)&&e.length>0){let t=e[0];return{title:t.Title||t.title||"",text:t.Text||t.text||t.content||""}}return{title:e.Title||e.title||"",text:e.Text||e.text||e.content||""}}catch(e){return{title:"Webpage Content",text:r[1]}}let s=JSON.parse(e);if(Array.isArray(s)&&s.length>0){let e=s[0];return{title:e.Title||e.title||"",text:e.Text||e.text||e.content||""}}return{title:s.Title||s.title||"",text:s.Text||s.text||s.content||""}}catch(e){return{title:"Webpage Content",text:t[1]}}let r=e.content.match(/ToolResult\(.*?output='([\s\S]*?)'.*?\)/);if(r)try{let e=JSON.parse(r[1].replace(/\\\\n/g,"\\n").replace(/\\\\u/g,"\\u"));if(Array.isArray(e)&&e.length>0){let t=e[0];return{title:t.Title||t.title||"",text:t.Text||t.text||t.content||""}}return{title:e.Title||e.title||"",text:e.Text||e.text||e.content||""}}catch(e){return{title:"Webpage Content",text:r[1]}}}let r=t.match(/<(?:crawl|scrape)-webpage[^>]*>([\s\S]*?)<\/(?:crawl|scrape)-webpage>/);if(r){let e=r[1].match(/ToolResult\(.*?output='([\s\S]*?)'.*?\)/);if(e)try{let t=e[1].replace(/\\\\n/g,"\\n").replace(/\\\\u/g,"\\u"),r=JSON.parse(t);if(Array.isArray(r)&&r.length>0){let e=r[0];return{title:e.Title||e.title||(e.URL?new URL(e.URL).hostname:""),text:e.Text||e.text||e.content||""}}return{title:r.Title||r.title||"",text:r.Text||r.text||r.content||""}}catch(t){return{title:"Webpage Content",text:e[1]}}}if(Array.isArray(e)&&e.length>0){let t=e[0];return{title:t.Title||t.title||"",text:t.Text||t.text||t.content||""}}if("object"==typeof e&&null!==e){if("Title"in e||"title"in e||"Text"in e||"text"in e)return{title:e.Title||e.title||"Webpage Content",text:e.Text||e.text||e.content||""};return{title:"Webpage Content",text:JSON.stringify(e)}}}catch(r){let e=t.match(/ToolResult\(.*?output='([\s\S]*?)'.*?\)/);if(e)try{let t=JSON.parse(e[1].replace(/\\\\n/g,"\\n").replace(/\\\\u/g,"\\u"));if(Array.isArray(t)&&t.length>0){let e=t[0];return{title:e.Title||e.title||"",text:e.Text||e.text||e.content||""}}return{title:t.Title||t.title||"",text:t.Text||t.text||t.content||""}}catch(t){return{title:"Webpage Content",text:e[1]}}if(t)return{title:"Webpage Content",text:t}}return null}(l),k=O(t),y=N?(e=>{try{return new URL(e).hostname.replace("www.","")}catch(t){return e}})(N):"Unknown",z=N?(e=>{try{let t=new URL(e).hostname;return"https://www.google.com/s2/favicons?domain=".concat(t,"&sz=128")}catch(e){return null}})(N):null;(0,a.useEffect)(()=>{if(d){let e=setInterval(()=>{x(t=>t>=95?(clearInterval(e),t):t+5)},300);return()=>clearInterval(e)}x(100)},[d]);let C=async()=>{if(null==w?void 0:w.text)try{await navigator.clipboard.writeText(w.text),p(!0),setTimeout(()=>p(!1),2e3)}catch(e){console.error("Failed to copy:",e)}},A=(null==w?void 0:w.text)?(e=>{let t=e.trim().split(/\s+/).length;return{wordCount:t,charCount:e.length,lineCount:e.split("\n").length}})(w.text):null;return(0,s.jsxs)(Y.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,s.jsx)(Y.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,s.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"relative p-2 rounded-lg bg-gradient-to-br from-primary/20 to-primary/10 border border-primary/20",children:(0,s.jsx)(ei.A,{className:"w-5 h-5 text-primary"})}),(0,s.jsx)("div",{children:(0,s.jsx)(Y.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:k})})]}),!d&&(0,s.jsxs)(Q.E,{variant:"secondary",className:c?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[c?(0,s.jsx)(j.A,{className:"h-3.5 w-3.5"}):(0,s.jsx)(v.A,{className:"h-3.5 w-3.5"}),c?"Crawling completed":"Crawling failed"]})]})}),(0,s.jsx)(Y.Wu,{className:"p-0 h-full flex-1 overflow-hidden relative",children:d?(0,s.jsx)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:(0,s.jsxs)("div",{className:"text-center w-full max-w-xs",children:[(0,s.jsx)("div",{className:"w-16 h-16 rounded-full mx-auto mb-6 flex items-center justify-center bg-gradient-to-br from-primary/20 to-primary/10 border border-primary/20",children:(0,s.jsx)(ee.A,{className:"h-8 w-8 animate-spin text-primary"})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-zinc-900 dark:text-zinc-100 mb-2",children:"Crawling Webpage"}),(0,s.jsxs)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400 mb-6",children:["Fetching content from ",(0,s.jsx)("span",{className:"font-mono text-xs break-all",children:y})]}),(0,s.jsx)(er,{value:u,className:"w-full h-1"}),(0,s.jsxs)("p",{className:"text-xs text-zinc-400 dark:text-zinc-500 mt-2",children:[u,"% complete"]})]})}):N?(0,s.jsx)(K.F,{className:"h-full w-full",children:(0,s.jsxs)("div",{className:"p-4 py-0 my-4",children:[(0,s.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-zinc-800 dark:text-zinc-200",children:[(0,s.jsx)(ei.A,{className:"w-4 h-4 text-zinc-500 dark:text-zinc-400"}),"Source URL"]}),(0,s.jsx)("div",{className:"group relative",children:(0,s.jsxs)("div",{className:"flex items-center gap-3 p-4 bg-zinc-50 dark:bg-zinc-900 hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors rounded-xl border border-zinc-200 dark:border-zinc-800",children:[z&&(0,s.jsx)("img",{src:z,alt:"",className:"w-6 h-6 rounded-md flex-shrink-0",onError:e=>{e.target.style.display="none"}}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"font-mono text-sm text-zinc-900 dark:text-zinc-100 truncate",children:(0,i.W5)(N,70)}),(0,s.jsx)("p",{className:"text-xs text-zinc-500 dark:text-zinc-400 mt-1",children:y})]}),(0,s.jsx)(g.$,{variant:"ghost",size:"sm",className:"opacity-70 group-hover:opacity-100 transition-opacity",asChild:!0,children:(0,s.jsx)("a",{href:N,target:"_blank",rel:"noopener noreferrer",children:(0,s.jsx)(e2.A,{className:"w-4 h-4"})})})]})})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-zinc-800 dark:text-zinc-200",children:[(0,s.jsx)(e4.A,{className:"w-4 h-4 text-zinc-500 dark:text-zinc-400"}),"Extracted Content"]}),A&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(Q.E,{variant:"outline",className:"text-xs",children:[A.wordCount," words"]}),(0,s.jsxs)(Q.E,{variant:"outline",className:"text-xs",children:[A.charCount," chars"]})]})]}),(null==w?void 0:w.text)?(0,s.jsxs)("div",{className:"group relative bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-xl overflow-hidden hover:border-zinc-300 dark:hover:border-zinc-700 transition-all duration-200 hover:shadow-sm",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 bg-zinc-50 dark:bg-zinc-800/50 border-b border-zinc-200 dark:border-zinc-700",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500/20 to-blue-600/10 flex items-center justify-center border border-blue-500/20",children:(0,s.jsx)(S.A,{className:"w-4 h-4 text-blue-600 dark:text-blue-400"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-zinc-900 dark:text-zinc-100",children:"Page Content"}),A&&(0,s.jsxs)("p",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:[A.lineCount," lines"]})]})]}),(0,s.jsx)("div",{className:"flex items-center gap-1",children:(0,s.jsx)(eZ.Bc,{children:(0,s.jsxs)(eZ.m_,{children:[(0,s.jsx)(eZ.k$,{asChild:!0,children:(0,s.jsx)(g.$,{variant:"ghost",size:"sm",className:(0,i.cn)("opacity-70 group-hover:opacity-100 transition-all duration-200",h&&"opacity-100"),onClick:C,children:h?(0,s.jsx)(e3.A,{className:"w-4 h-4 text-green-600"}):(0,s.jsx)(e8.A,{className:"w-4 h-4"})})}),(0,s.jsx)(eZ.ZI,{children:(0,s.jsx)("p",{children:h?"Copied!":"Copy content"})})]})})})]}),(0,s.jsx)("div",{className:"p-4 max-h-96 overflow-auto",children:(0,s.jsx)("pre",{className:"text-xs font-mono text-zinc-800 dark:text-zinc-300 whitespace-pre-wrap leading-relaxed",children:w.text})})]}):(0,s.jsxs)("div",{className:"text-center py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900 rounded-xl border border-zinc-200 dark:border-zinc-800",children:[(0,s.jsx)("div",{className:"w-16 h-16 rounded-full flex items-center justify-center mb-4 bg-gradient-to-b from-zinc-100 to-zinc-50 shadow-inner dark:from-zinc-800/40 dark:to-zinc-900/60 mx-auto",children:(0,s.jsx)(S.A,{className:"h-8 w-8 text-zinc-400 dark:text-zinc-600"})}),(0,s.jsx)("h3",{className:"text-lg font-medium mb-2 text-zinc-900 dark:text-zinc-100",children:"No Content Extracted"}),(0,s.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400 max-w-sm mx-auto",children:"The webpage might be restricted, empty, or require JavaScript to load content"})]})]})]})}):(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:[(0,s.jsx)("div",{className:"w-20 h-20 rounded-full flex items-center justify-center mb-6 bg-gradient-to-b from-zinc-100 to-zinc-50 shadow-inner dark:from-zinc-800/40 dark:to-zinc-900/60",children:(0,s.jsx)(ei.A,{className:"h-10 w-10 text-zinc-400 dark:text-zinc-600"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold mb-2 text-zinc-900 dark:text-zinc-100",children:"No URL Detected"}),(0,s.jsx)("p",{className:"text-zinc-500 dark:text-zinc-400 text-center max-w-sm",children:"Unable to extract a valid URL from the crawling request"})]})}),(0,s.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,s.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:!d&&(null==w?void 0:w.text)&&(0,s.jsxs)(Q.E,{className:"h-6 py-0.5",children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full bg-green-500 mr-1.5"}),"Content extracted"]})}),(0,s.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:o&&!d?P(o):n?P(n):""})]})]})},"scrape-webpage":function(e){let{name:t="scrape-webpage",assistantContent:r,toolContent:l,assistantTimestamp:n,toolTimestamp:o,isSuccess:c=!0,isStreaming:d=!1}=e,{resolvedTheme:m}=(0,em.D)(),[u,x]=(0,a.useState)(0),[h,p]=(0,a.useState)(null),{url:f,files:b,actualIsSuccess:N,actualToolTimestamp:w,actualAssistantTimestamp:k}=function(e,t,r,s,l){let a=null,n=null,i=!1,o=null,c=[],d=0,m=r,u=s,x=l,h=te(e),p=te(t);if(console.log("WebScrapeToolView: Format detection results:",{assistantNewFormat:{hasUrl:!!h.url,fileCount:h.files.length,urlCount:h.urlCount},toolNewFormat:{hasUrl:!!p.url,fileCount:p.files.length,urlCount:p.urlCount}}),h.url||h.files.length>0||h.urlCount>0)a=h.url,n=h.urls,i=h.success||!1,o=h.message,c=h.files,d=h.urlCount,void 0!==h.success&&(m=h.success),h.timestamp&&(x=h.timestamp),console.log("WebScrapeToolView: Using assistant new format data");else if(p.url||p.files.length>0||p.urlCount>0)a=p.url,n=p.urls,i=p.success||!1,o=p.message,c=p.files,d=p.urlCount,void 0!==p.success&&(m=p.success),p.timestamp&&(u=p.timestamp),console.log("WebScrapeToolView: Using tool new format data");else{let r=ts(e),s=ts(t);a=r.url||s.url,n=r.urls||s.urls,i=r.success||s.success||!1,o=r.message||s.message,c=r.files.length>0?r.files:s.files,d=r.urlCount>0?r.urlCount:s.urlCount,console.log("WebScrapeToolView: Using legacy format data:",{url:a,fileCount:c.length,urlCount:d})}return console.log("WebScrapeToolView: Final extracted data:",{url:a,fileCount:c.length,urlCount:d,actualIsSuccess:m}),{url:a,urls:n,success:i,message:o,files:c,urlCount:d,actualIsSuccess:m,actualToolTimestamp:u,actualAssistantTimestamp:x}}(r,l,c,o,n),y=O(t),z=f?(e=>{try{return new URL(e).hostname.replace("www.","")}catch(t){return e}})(f):"Unknown",C=f?(e=>{try{let t=new URL(e).hostname;return"https://www.google.com/s2/favicons?domain=".concat(t,"&sz=128")}catch(e){return null}})(f):null;(0,a.useEffect)(()=>{if(d){let e=setInterval(()=>{x(t=>t>=95?(clearInterval(e),t):t+5)},300);return()=>clearInterval(e)}x(100)},[d]);let A=async e=>{try{await navigator.clipboard.writeText(e),p(e),setTimeout(()=>p(null),2e3)}catch(e){console.error("Failed to copy:",e)}},E=e=>{let t=e.match(/(\d{8}_\d{6})/),r=e.match(/(\w+)_com\.json$/),s=e.split("/").pop()||e;return{timestamp:t?t[1]:"",domain:r?r[1]:"unknown",fileName:s,fullPath:e}};return(0,s.jsxs)(Y.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,s.jsx)(Y.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,s.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"relative p-2 rounded-lg bg-gradient-to-br from-primary/20 to-primary/10 border border-primary/20",children:(0,s.jsx)(ei.A,{className:"w-5 h-5 text-primary"})}),(0,s.jsx)("div",{children:(0,s.jsx)(Y.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:y})})]}),!d&&(0,s.jsxs)(Q.E,{variant:"secondary",className:N?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[N?(0,s.jsx)(j.A,{className:"h-3.5 w-3.5"}):(0,s.jsx)(v.A,{className:"h-3.5 w-3.5"}),N?"Scraping completed":"Scraping failed"]})]})}),(0,s.jsx)(Y.Wu,{className:"p-0 h-full flex-1 overflow-hidden relative",children:d?(0,s.jsx)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:(0,s.jsxs)("div",{className:"text-center w-full max-w-xs",children:[(0,s.jsx)("div",{className:"w-16 h-16 rounded-full mx-auto mb-6 flex items-center justify-center bg-gradient-to-br from-primary/20 to-primary/10 border border-primary/20",children:(0,s.jsx)(ee.A,{className:"h-8 w-8 animate-spin text-primary"})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-zinc-900 dark:text-zinc-100 mb-2",children:"Extracting Content"}),(0,s.jsxs)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400 mb-6",children:["Analyzing and processing ",(0,s.jsx)("span",{className:"font-mono text-xs break-all",children:z})]}),(0,s.jsx)(er,{value:u,className:"w-full h-1"}),(0,s.jsxs)("p",{className:"text-xs text-zinc-400 dark:text-zinc-500 mt-2",children:[u,"% complete"]})]})}):f?(0,s.jsx)(K.F,{className:"h-full w-full",children:(0,s.jsxs)("div",{className:"p-4 py-0 my-4",children:[(0,s.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-zinc-800 dark:text-zinc-200",children:[(0,s.jsx)(ei.A,{className:"w-4 h-4 text-zinc-500 dark:text-zinc-400"}),"Source URL"]}),(0,s.jsx)("div",{className:"group relative",children:(0,s.jsxs)("div",{className:"flex items-center gap-3 p-4 bg-zinc-50 dark:bg-zinc-900 hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors rounded-xl border border-zinc-200 dark:border-zinc-800",children:[C&&(0,s.jsx)("img",{src:C,alt:"",className:"w-6 h-6 rounded-md flex-shrink-0",onError:e=>{e.target.style.display="none"}}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"font-mono text-sm text-zinc-900 dark:text-zinc-100 truncate",children:(0,i.W5)(f,70)}),(0,s.jsx)("p",{className:"text-xs text-zinc-500 dark:text-zinc-400 mt-1",children:z})]}),(0,s.jsx)(g.$,{variant:"ghost",size:"sm",className:"opacity-70 group-hover:opacity-100 transition-opacity",asChild:!0,children:(0,s.jsx)("a",{href:f,target:"_blank",rel:"noopener noreferrer",children:(0,s.jsx)(e2.A,{className:"w-4 h-4"})})})]})})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-zinc-800 dark:text-zinc-200",children:[(0,s.jsx)(e9.A,{className:"w-4 h-4 text-zinc-500 dark:text-zinc-400"}),"Generated Files"]}),(0,s.jsxs)(Q.E,{variant:"outline",className:"gap-1",children:[b.length," file",1!==b.length?"s":""]})]}),b.length>0?(0,s.jsx)("div",{className:"space-y-3",children:b.map((e,t)=>{let r=E(e),l=h===e;return(0,s.jsx)("div",{className:"group relative bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-xl p-4 hover:border-zinc-300 dark:hover:border-zinc-700 transition-all duration-200 hover:shadow-sm",children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-lg bg-gradient-to-br from-green-500/20 to-green-600/10 flex items-center justify-center border border-green-500/20 flex-shrink-0",children:(0,s.jsx)(S.A,{className:"w-5 h-5 text-green-600 dark:text-green-400"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0 space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 flex-wrap",children:[(0,s.jsx)(Q.E,{variant:"outline",className:"text-xs font-normal",children:"JSON"}),r.timestamp&&(0,s.jsxs)(Q.E,{variant:"outline",className:"text-xs font-normal",children:[(0,s.jsx)(e6.A,{className:"w-3 h-3 mr-1"}),r.timestamp.replace("_"," ")]})]}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)("p",{className:"font-mono text-sm text-zinc-900 dark:text-zinc-100 font-medium",children:r.fileName}),(0,s.jsx)("p",{className:"font-mono text-xs text-zinc-500 dark:text-zinc-400 truncate",children:r.fullPath})]})]}),(0,s.jsx)(eZ.Bc,{children:(0,s.jsxs)(eZ.m_,{children:[(0,s.jsx)(eZ.k$,{asChild:!0,children:(0,s.jsx)(g.$,{variant:"ghost",size:"sm",className:(0,i.cn)("opacity-0 group-hover:opacity-100 transition-all duration-200",l&&"opacity-100"),onClick:()=>A(e),children:l?(0,s.jsx)(e3.A,{className:"w-4 h-4 text-green-600"}):(0,s.jsx)(e8.A,{className:"w-4 h-4"})})}),(0,s.jsx)(eZ.ZI,{children:(0,s.jsx)("p",{children:l?"Copied!":"Copy file path"})})]})})]})},t)})}):(0,s.jsxs)("div",{className:"text-center py-8 text-zinc-500 dark:text-zinc-400",children:[(0,s.jsx)(S.A,{className:"w-8 h-8 mx-auto mb-2 opacity-50"}),(0,s.jsx)("p",{className:"text-sm",children:"No files generated"})]})]})]})}):(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:[(0,s.jsx)("div",{className:"w-20 h-20 rounded-full flex items-center justify-center mb-6 bg-gradient-to-b from-zinc-100 to-zinc-50 shadow-inner dark:from-zinc-800/40 dark:to-zinc-900/60",children:(0,s.jsx)(ei.A,{className:"h-10 w-10 text-zinc-400 dark:text-zinc-600"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold mb-2 text-zinc-900 dark:text-zinc-100",children:"No URL Detected"}),(0,s.jsx)("p",{className:"text-zinc-500 dark:text-zinc-400 text-center max-w-sm",children:"Unable to extract a valid URL from the scraping request"})]})}),(0,s.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,s.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:!d&&b.length>0&&(0,s.jsxs)(Q.E,{className:"h-6 py-0.5",children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full bg-green-500 mr-1.5"}),b.length," file",1!==b.length?"s":""," saved"]})}),(0,s.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:w&&!d?P(w):k?P(k):""})]})]})},"execute-data-provider-call":function(e){let{name:t="execute-data-provider-call",assistantContent:r,toolContent:l,assistantTimestamp:a,toolTimestamp:n,isSuccess:o=!0,isStreaming:c=!1}=e,{serviceName:d,route:m,payload:u,output:x,actualIsSuccess:h,actualToolTimestamp:f,actualAssistantTimestamp:g}=function(e,t,r,s,l){var a,n;let i=null,o=null,c=null,d=null,m=r,u=s,x=l,h=tB(e),p=tB(t);if(console.log("DataProviderCallToolView: Format detection results:",{assistantNewFormat:{hasServiceName:!!h.serviceName,hasRoute:!!h.route,hasPayload:!!h.payload},toolNewFormat:{hasServiceName:!!p.serviceName,hasRoute:!!p.route,hasPayload:!!p.payload}}),h.serviceName||h.route)i=h.serviceName,o=h.route,c=h.payload,d=null!=(a=h.output)?a:null,void 0!==h.success&&(m=h.success),h.timestamp&&(x=h.timestamp),console.log("DataProviderCallToolView: Using assistant new format data");else if(p.serviceName||p.route)i=p.serviceName,o=p.route,c=p.payload,d=null!=(n=p.output)?n:null,void 0!==p.success&&(m=p.success),p.timestamp&&(u=p.timestamp),console.log("DataProviderCallToolView: Using tool new format data");else{let r=tZ(e),s=tZ(t);i=r.serviceName||s.serviceName,console.log("DataProviderCallToolView: Using legacy format data:",{serviceName:i,route:o=r.route||s.route,hasPayload:!!(c=r.payload||s.payload)})}return console.log("DataProviderCallToolView: Final extracted data:",{serviceName:i,route:o,hasPayload:!!c,hasOutput:!!d,actualIsSuccess:m}),{serviceName:i,route:o,payload:c,output:d,actualIsSuccess:m,actualToolTimestamp:u,actualAssistantTimestamp:x}}(r,l,o,n,a),b=null==d?void 0:d.toLowerCase(),N=b&&tG[b]?tG[b]:tG.linkedin,w=N.icon;return(0,s.jsxs)(Y.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,s.jsx)(Y.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,s.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"relative p-2 rounded-xl bg-gradient-to-br from-blue-500/20 to-blue-600/10 border border-blue-500/20",children:(0,s.jsx)(ei.A,{className:"w-5 h-5 text-blue-500 dark:text-blue-400"})}),(0,s.jsx)("div",{children:(0,s.jsx)(Y.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:"Data Provider"})})]}),!c&&(0,s.jsxs)(Q.E,{variant:"secondary",className:(0,i.cn)("text-xs font-medium",h?"bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-900/20 dark:text-emerald-300 dark:border-emerald-800":"bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800"),children:[h?(0,s.jsx)(j.A,{className:"h-3 w-3 mr-1"}):(0,s.jsx)(v.A,{className:"h-3 w-3 mr-1"}),h?"Executed":"Failed"]})]})}),(0,s.jsx)(Y.Wu,{className:"p-0 h-full flex-1 overflow-hidden relative",children:c?(0,s.jsx)("div",{className:"flex flex-col items-center justify-center h-full py-8 px-6",children:(0,s.jsxs)("div",{className:"text-center w-full max-w-xs",children:[(0,s.jsx)("div",{className:"w-16 h-16 rounded-xl mx-auto mb-4 flex items-center justify-center bg-zinc-100 dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700",children:(0,s.jsx)(ee.A,{className:"h-8 w-8 animate-spin text-zinc-500 dark:text-zinc-400"})}),(0,s.jsx)("h3",{className:"text-base font-medium text-zinc-900 dark:text-zinc-100 mb-2",children:"Executing call..."}),(0,s.jsxs)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400",children:["Calling ",d||"data provider"]})]})}):(0,s.jsxs)("div",{className:"p-4 space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4 p-4 bg-zinc-50 dark:bg-zinc-900/50 rounded-lg border border-zinc-200 dark:border-zinc-800",children:[(0,s.jsx)("div",{className:(0,i.cn)("w-12 h-12 rounded-lg flex items-center justify-center shadow-sm border-2","bg-gradient-to-br ".concat(N.color),"border-white/20"),children:(0,s.jsx)(w,{className:"h-6 w-6 text-white drop-shadow-sm"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-zinc-900 dark:text-zinc-100",children:N.name}),d&&(0,s.jsxs)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400",children:["Service: ",d]})]}),m&&(0,s.jsx)(Q.E,{variant:"outline",className:"text-xs font-mono",children:m})]}),x&&!h&&(0,s.jsxs)("div",{className:"p-4 bg-red-50 dark:bg-red-900/10 rounded-lg border border-red-200 dark:border-red-800/50",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)(v.A,{className:"h-4 w-4 text-red-600 dark:text-red-400"}),(0,s.jsx)("span",{className:"text-sm font-medium text-red-800 dark:text-red-300",children:"Execution Failed"})]}),(0,s.jsx)("p",{className:"text-xs text-red-700 dark:text-red-300 font-mono",children:x})]}),u&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-zinc-700 dark:text-zinc-300",children:[(0,s.jsx)(tV.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Call Parameters"}),(0,s.jsx)(p.A,{className:"h-3 w-3 text-zinc-400"})]}),(0,s.jsx)("div",{className:"grid gap-3",children:Object.entries(u).map(e=>{let[t,r]=e;return(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800 hover:bg-zinc-50 dark:hover:bg-zinc-800/50 transition-colors",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full bg-zinc-300 dark:bg-zinc-600"}),(0,s.jsx)("code",{className:"text-sm font-mono font-medium text-zinc-900 dark:text-zinc-100",children:t})]}),(0,s.jsx)("span",{className:"text-sm text-zinc-600 dark:text-zinc-400 max-w-xs truncate font-mono",children:"string"==typeof r?'"'.concat(r,'"'):String(r)})]},t)})}),(0,s.jsxs)("details",{className:"group",children:[(0,s.jsxs)("summary",{className:"flex items-center gap-2 text-sm font-medium text-zinc-700 dark:text-zinc-300 cursor-pointer hover:text-zinc-900 dark:hover:text-zinc-100 transition-colors",children:[(0,s.jsx)(ev.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Raw JSON"}),(0,s.jsx)(p.A,{className:"h-3 w-3 text-zinc-400 group-open:rotate-90 transition-transform"})]}),(0,s.jsx)("div",{className:"mt-3 p-4 bg-zinc-900 dark:bg-zinc-950 rounded-lg border border-zinc-200 dark:border-zinc-800",children:(0,s.jsx)("pre",{className:"text-xs font-mono text-emerald-400 dark:text-emerald-300 overflow-x-auto",children:JSON.stringify(u,null,2)})})]})]}),!d&&!m&&!u&&(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,s.jsx)("div",{className:"w-12 h-12 rounded-lg bg-zinc-100 dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 flex items-center justify-center mb-3",children:(0,s.jsx)(tJ.A,{className:"h-6 w-6 text-zinc-400"})}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(ee.A,{className:"h-4 w-4 animate-spin text-zinc-500 dark:text-zinc-400"}),(0,s.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400",children:"Will be populated when the call is executed..."})]})]})]})}),(0,s.jsxs)("div",{className:"px-4 py-2 h-10 bg-zinc-50/50 dark:bg-zinc-900/50 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,s.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:!c&&d&&(0,s.jsxs)(Q.E,{variant:"outline",className:"h-6 py-0.5 text-xs",children:[(0,s.jsx)(w,{className:"h-3 w-3 mr-1"}),d]})}),(0,s.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:f&&!c?P(f):g?P(g):""})]})]})},"get-data-provider-endpoints":function(e){let{assistantContent:t,toolContent:r,assistantTimestamp:l,toolTimestamp:a,isSuccess:n=!0,isStreaming:o=!1}=e,{serviceName:c,endpoints:d,actualIsSuccess:m,actualToolTimestamp:u,actualAssistantTimestamp:x}=function(e,t,r,s,l){let a=null,n=null,i=r,o=s,c=l,d=tB(e),m=tB(t);if(console.log("DataProviderEndpointsToolView: Format detection results:",{assistantNewFormat:{hasServiceName:!!d.serviceName,hasEndpoints:!!d.endpoints},toolNewFormat:{hasServiceName:!!m.serviceName,hasEndpoints:!!m.endpoints}}),d.serviceName||d.endpoints)a=d.serviceName,n=d.endpoints,void 0!==d.success&&(i=d.success),d.timestamp&&(c=d.timestamp),console.log("DataProviderEndpointsToolView: Using assistant new format data");else if(m.serviceName||m.endpoints)a=m.serviceName,n=m.endpoints,void 0!==m.success&&(i=m.success),m.timestamp&&(o=m.timestamp),console.log("DataProviderEndpointsToolView: Using tool new format data");else{let r=tZ(e),s=tZ(t);console.log("DataProviderEndpointsToolView: Using legacy format data:",{serviceName:a=r.serviceName||s.serviceName,hasEndpoints:!!(n=r.endpoints||s.endpoints)}),a||(a=(e=>{let t=G(e),r=tq(t||"");if(r)return r.toLowerCase();if(!t)return"linkedin";let s=t.toLowerCase();return s.includes("linkedin")?"linkedin":s.includes("twitter")?"twitter":s.includes("zillow")?"zillow":s.includes("amazon")?"amazon":s.includes("yahoo")||s.includes("finance")?"yahoo_finance":s.includes("jobs")||s.includes("active")?"active_jobs":"linkedin"})(e||t))}return console.log("DataProviderEndpointsToolView: Final extracted data:",{serviceName:a,hasEndpoints:!!n,actualIsSuccess:i}),{serviceName:a,endpoints:n,actualIsSuccess:i,actualToolTimestamp:o,actualAssistantTimestamp:c}}(t,r,n,a,l),h=c&&tH[c]?tH[c]:tH.linkedin,f=h.icon,g=d&&"object"==typeof d?Object.keys(d).length:0;return(0,s.jsxs)(Y.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,s.jsx)(Y.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,s.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"relative p-2 rounded-xl bg-gradient-to-br from-blue-500/20 to-blue-600/10 border border-blue-500/20",children:(0,s.jsx)(ei.A,{className:"w-5 h-5 text-blue-500 dark:text-blue-400"})}),(0,s.jsx)("div",{children:(0,s.jsx)(Y.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:"Data Provider"})})]}),!o&&(0,s.jsxs)(Q.E,{variant:"secondary",className:(0,i.cn)("text-xs font-medium",m?"bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-900/20 dark:text-emerald-300 dark:border-emerald-800":"bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800"),children:[m?(0,s.jsx)(j.A,{className:"h-3 w-3 mr-1"}):(0,s.jsx)(v.A,{className:"h-3 w-3 mr-1"}),m?"Loaded":"Failed"]})]})}),(0,s.jsx)(Y.Wu,{className:"p-0 h-full flex-1 overflow-hidden relative",children:o?(0,s.jsx)("div",{className:"flex flex-col items-center justify-center h-full py-8 px-6",children:(0,s.jsxs)("div",{className:"text-center w-full max-w-xs",children:[(0,s.jsx)("div",{className:"w-16 h-16 rounded-xl mx-auto mb-4 flex items-center justify-center bg-zinc-100 dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700",children:(0,s.jsx)(ee.A,{className:"h-8 w-8 animate-spin text-zinc-500 dark:text-zinc-400"})}),(0,s.jsx)("h3",{className:"text-base font-medium text-zinc-900 dark:text-zinc-100 mb-2",children:"Loading provider..."}),(0,s.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400",children:"Connecting to data source"})]})}):(0,s.jsxs)("div",{className:"p-4 space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4 p-4 bg-zinc-50 dark:bg-zinc-900/50 rounded-lg border border-zinc-200 dark:border-zinc-800",children:[(0,s.jsx)("div",{className:(0,i.cn)("w-12 h-12 rounded-lg flex items-center justify-center shadow-sm border-2","bg-gradient-to-br ".concat(h.color),"border-white/20"),children:(0,s.jsx)(f,{className:"h-6 w-6 text-white drop-shadow-sm"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-zinc-900 dark:text-zinc-100",children:h.name}),(0,s.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400",children:g>0?"".concat(g," endpoints loaded and ready"):"Endpoints loaded and ready"})]}),(0,s.jsxs)(Q.E,{variant:"secondary",className:(0,i.cn)("text-xs font-medium",m?"bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-900/20 dark:text-emerald-300 dark:border-emerald-800":"bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800"),children:[m?(0,s.jsx)(j.A,{className:"h-3 w-3 mr-1"}):(0,s.jsx)(v.A,{className:"h-3 w-3 mr-1"}),m?"Connected":"Failed"]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-zinc-700 dark:text-zinc-300",children:[(0,s.jsx)(tJ.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Provider Status"}),(0,s.jsx)(p.A,{className:"h-3 w-3 text-zinc-400"})]}),(0,s.jsxs)("div",{className:"grid gap-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full bg-emerald-500"}),(0,s.jsx)("span",{className:"text-sm font-medium text-zinc-900 dark:text-zinc-100",children:"Connection Status"})]}),(0,s.jsxs)(Q.E,{variant:"secondary",className:(0,i.cn)("text-xs font-medium",m?"bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-900/20 dark:text-emerald-300 dark:border-emerald-800":"bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800"),children:[m?(0,s.jsx)(j.A,{className:"h-3 w-3 mr-1"}):(0,s.jsx)(v.A,{className:"h-3 w-3 mr-1"}),m?"Active":"Inactive"]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full bg-blue-500"}),(0,s.jsx)("span",{className:"text-sm font-medium text-zinc-900 dark:text-zinc-100",children:"Endpoints Available"})]}),(0,s.jsx)(Q.E,{variant:"outline",className:"text-xs",children:g>0?"".concat(g," endpoints"):"Ready"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full bg-purple-500"}),(0,s.jsx)("span",{className:"text-sm font-medium text-zinc-900 dark:text-zinc-100",children:"Data Provider"})]}),(0,s.jsx)("span",{className:"text-sm text-zinc-600 dark:text-zinc-400 font-mono",children:c||"linkedin"})]})]}),m&&(0,s.jsxs)("div",{className:"p-4 bg-emerald-50 dark:bg-emerald-900/10 rounded-lg border border-emerald-200 dark:border-emerald-800/50",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)(j.A,{className:"h-4 w-4 text-emerald-600 dark:text-emerald-400/70"}),(0,s.jsx)("span",{className:"text-sm font-medium text-emerald-800 dark:text-emerald-300/70",children:"Provider Ready"})]}),(0,s.jsx)("p",{className:"text-xs text-emerald-700 dark:text-emerald-300/70",children:"Data provider endpoints have been loaded successfully and are ready to process requests."})]})]})]})}),(0,s.jsxs)("div",{className:"px-4 py-2 h-10 bg-zinc-50/50 dark:bg-zinc-900/50 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,s.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:!o&&(0,s.jsxs)(Q.E,{variant:"outline",className:"h-6 py-0.5 text-xs",children:[(0,s.jsx)(f,{className:"h-3 w-3 mr-1"}),h.name.split(" ")[0]]})}),(0,s.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:u&&!o?P(u):x?P(x):""})]})]})},"expose-port":function(e){let{assistantContent:t,toolContent:r,isSuccess:l=!0,isStreaming:a=!1,assistantTimestamp:n,toolTimestamp:i}=e,{port:o,url:c,message:d,actualIsSuccess:m,actualToolTimestamp:x}=function(e,t,r,s,l){let a=null,n=null,i=null,o=r,c=s,d=l,m=eg(e),u=eg(t);if(console.log("ExposePortToolView: Format detection results:",{assistantNewFormat:{hasPort:!!m.port,hasUrl:!!m.url,hasMessage:!!m.message},toolNewFormat:{hasPort:!!u.port,hasUrl:!!u.url,hasMessage:!!u.message}}),m.port||m.url||m.message)a=m.port,n=m.url,i=m.message,void 0!==m.success&&(o=m.success),m.timestamp&&(d=m.timestamp),console.log("ExposePortToolView: Using assistant new format data");else if(u.port||u.url||u.message)a=u.port,n=u.url,i=u.message,void 0!==u.success&&(o=u.success),u.timestamp&&(c=u.timestamp),console.log("ExposePortToolView: Using tool new format data");else{let r=ej(e),s=ej(t);if(a=r.port||s.port,n=r.url||s.url,i=r.message||s.message,!a){let t=eb(e);t&&(a=t)}console.log("ExposePortToolView: Using legacy format data:",{port:a,hasUrl:!!n,hasMessage:!!i})}return console.log("ExposePortToolView: Final extracted data:",{port:a,hasUrl:!!n,hasMessage:!!i,actualIsSuccess:o}),{port:a,url:n,message:i,actualIsSuccess:o,actualToolTimestamp:c,actualAssistantTimestamp:d}}(t,r,l,i,n);return(0,s.jsxs)(Y.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,s.jsx)(Y.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,s.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"relative p-2 rounded-lg bg-gradient-to-br from-green-500/20 to-green-600/10 border border-green-500/20",children:(0,s.jsx)(u.A,{className:"w-5 h-5 text-green-500 dark:text-green-400"})}),(0,s.jsx)("div",{children:(0,s.jsx)(Y.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:"Port Exposure"})})]}),!a&&(0,s.jsxs)(Q.E,{variant:"secondary",className:m?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[m?(0,s.jsx)(j.A,{className:"h-3.5 w-3.5 mr-1"}):(0,s.jsx)(v.A,{className:"h-3.5 w-3.5 mr-1"}),m?"Port exposed successfully":"Failed to expose port"]})]})}),(0,s.jsx)(Y.Wu,{className:"p-0 h-full flex-1 overflow-hidden relative",children:a?(0,s.jsx)(es,{icon:u.A,iconColor:"text-emerald-500 dark:text-emerald-400",bgColor:"bg-gradient-to-b from-emerald-100 to-emerald-50 shadow-inner dark:from-emerald-800/40 dark:to-emerald-900/60 dark:shadow-emerald-950/20",title:"Exposing port",filePath:null==o?void 0:o.toString(),showProgress:!0}):(0,s.jsx)(K.F,{className:"h-full w-full",children:(0,s.jsxs)("div",{className:"p-4 py-0 my-4 space-y-6",children:[c&&(0,s.jsx)("div",{className:"bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-lg shadow-sm overflow-hidden",children:(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsx)("div",{className:"flex items-start gap-3 mb-3",children:(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("h3",{className:"text-sm font-medium text-zinc-800 dark:text-zinc-200 mb-2",children:"Exposed URL"}),(0,s.jsxs)("a",{href:c,target:"_blank",rel:"noopener noreferrer",className:"text-md font-medium text-blue-600 dark:text-blue-400 hover:underline flex items-center gap-2 mb-3",children:[c,(0,s.jsx)(en.A,{className:"flex-shrink-0 h-3.5 w-3.5"})]})]})}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-1.5",children:[(0,s.jsx)("div",{className:"text-xs font-medium text-zinc-500 dark:text-zinc-400",children:"Port Details"}),(0,s.jsx)("div",{className:"flex gap-2 flex-wrap",children:(0,s.jsxs)(Q.E,{variant:"outline",className:"bg-zinc-50 dark:bg-zinc-800 font-mono",children:["Port: ",o]})})]}),d&&(0,s.jsx)("div",{className:"text-sm text-zinc-600 dark:text-zinc-400",children:d}),(0,s.jsxs)("div",{className:"text-xs bg-amber-50 dark:bg-amber-950/30 border border-amber-100 dark:border-amber-900/50 rounded-md p-3 text-amber-600 dark:text-amber-400 flex items-start gap-2",children:[(0,s.jsx)(v.A,{className:"h-4 w-4 flex-shrink-0 mt-0.5"}),(0,s.jsx)("span",{children:"This URL might only be temporarily available and could expire after some time."})]})]})]})}),!o&&!c&&!a&&(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center py-12 px-6",children:[(0,s.jsx)("div",{className:"w-20 h-20 rounded-full flex items-center justify-center mb-6 bg-gradient-to-b from-zinc-100 to-zinc-50 shadow-inner dark:from-zinc-800/40 dark:to-zinc-900/60",children:(0,s.jsx)(u.A,{className:"h-10 w-10 text-zinc-400 dark:text-zinc-600"})}),(0,s.jsx)("h3",{className:"text-xl font-semibold mb-2 text-zinc-900 dark:text-zinc-100",children:"No Port Information"}),(0,s.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400 text-center max-w-md",children:"No port exposure information is available yet. Use the expose-port command to share a local port."})]})]})})}),(0,s.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,s.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:!a&&o&&(0,s.jsxs)(Q.E,{variant:"outline",children:[(0,s.jsx)(u.A,{className:"h-3 w-3 mr-1"}),"Port ",o]})}),(0,s.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:x&&P(x)})]})]})},"see-image":function(e){let{assistantContent:t,toolContent:r,assistantTimestamp:l,toolTimestamp:n,isSuccess:o=!0,isStreaming:d=!1,name:m,project:u}=e,[x,h]=(0,a.useState)(0),{filePath:p,description:f,output:g,actualIsSuccess:b,actualToolTimestamp:N,actualAssistantTimestamp:w}=function(e,t,r,s,l){var a,n;let i=null,o=null,c=null,d=r,m=s,u=l,x=tp(e),h=tp(t);if(console.log("SeeImageToolView: Format detection results:",{assistantNewFormat:{hasFilePath:!!x.filePath,hasDescription:!!x.description},toolNewFormat:{hasFilePath:!!h.filePath,hasDescription:!!h.description}}),x.filePath||x.description)i=x.filePath,o=x.description,c=null!=(a=x.output)?a:null,void 0!==x.success&&(d=x.success),x.timestamp&&(u=x.timestamp),console.log("SeeImageToolView: Using assistant new format data");else if(h.filePath||h.description)i=h.filePath,o=h.description,c=null!=(n=h.output)?n:null,void 0!==h.success&&(d=h.success),h.timestamp&&(m=h.timestamp),console.log("SeeImageToolView: Using tool new format data");else{let r=tg(e),s=tg(t);i=r.filePath||s.filePath,o=r.description||s.description;let l=function(e){let t=G(e);if(!t)return{success:!1,message:"No tool result available"};console.log("Parsing tool result content:",t);try{let e=t;try{let r=JSON.parse(t);r.content&&"string"==typeof r.content&&(e=r.content)}catch(e){}let r=e.match(/<tool_result>\s*<see-image>\s*ToolResult\(([^)]+)\)\s*<\/see-image>\s*<\/tool_result>/);if(r){let e,t=r[1],s=t.includes("success=True"),l=t.match(/output="([^"]+)"/),a=l?l[1]:"";if(s&&a){let t=a.match(/Successfully loaded the image ['"]([^'"]+)['"]/i);t&&t[1]&&(e=t[1],console.log("Found file path in tool result:",e))}return{success:s,message:a,filePath:e}}let s=e.match(/<tool_result>\s*<see-image>\s*([^<]+)<\/see-image>\s*<\/tool_result>/);if(s){let e=s[1],t=e.includes("success=True")||e.includes("Successfully"),r=e.match(/['"]([^'"]+\.(jpg|jpeg|png|gif|webp|svg))['"]/)||e.match(/Successfully loaded the image ['"]([^'"]+)['"]/i),l=r?r[1]:void 0;return console.log("Found file path in direct tool result:",l),{success:t,message:t?"Image loaded successfully":"Failed to load image",filePath:l}}if(e.includes("success=True")||e.includes("Successfully")){let t=e.match(/Successfully loaded the image ['"]([^'"]+)['"]/i),r=t?t[1]:void 0;return{success:!0,message:"Image loaded successfully",filePath:r}}if(e.includes("success=False")||e.includes("Failed"))return{success:!1,message:"Failed to load image"}}catch(e){return console.error("Error parsing tool result:",e),{success:!1,message:"Failed to parse tool result"}}return{success:!0,message:"Image loaded"}}(t);l.filePath&&!i&&(i=l.filePath),l.message&&!c&&(c=l.message),console.log("SeeImageToolView: Using legacy format data:",{filePath:i,hasDescription:!!o,hasOutput:!!c})}return console.log("SeeImageToolView: Final extracted data:",{filePath:i,hasDescription:!!o,hasOutput:!!c,actualIsSuccess:d}),{filePath:i,description:o,output:c,actualIsSuccess:d,actualToolTimestamp:m,actualAssistantTimestamp:u}}(t,r,o,n,l);if(console.log("Final file path:",p),(0,a.useEffect)(()=>{if(d){let e=setInterval(()=>{h(t=>t>=95?(clearInterval(e),t):t+5)},300);return()=>clearInterval(e)}h(100)},[d]),!p)return console.log("No file path found, falling back to GenericToolView"),(0,s.jsx)(el,{name:m||"see-image",assistantContent:t,toolContent:r,assistantTimestamp:l,toolTimestamp:n,isSuccess:o,isStreaming:d});let k={color:"text-blue-500 dark:text-blue-400",bgColor:"bg-gradient-to-b from-blue-100 to-blue-50 shadow-inner dark:from-blue-800/40 dark:to-blue-900/60 dark:shadow-blue-950/20"},y=function(e,t){var r,s;if(!e||"STREAMING"===e)return console.error("Invalid image path:",e),"";let l=e.replace(/^['"](.*)['"]$/,"$1");if(l.startsWith("http"))return l;let a="string"==typeof(null==t?void 0:t.sandbox)?t.sandbox:null==t||null==(r=t.sandbox)?void 0:r.id;if(a){let e=l;return e.startsWith("/workspace")||(e="/workspace/".concat(e.startsWith("/")?e.substring(1):e)),"".concat("http://localhost:8000/api","/sandboxes/").concat(a,"/files/content?path=").concat(encodeURIComponent(e))}if(null==t||null==(s=t.sandbox)?void 0:s.sandbox_url){let e=t.sandbox.sandbox_url.replace(/\/$/,""),r=l;r.startsWith("/workspace")||(r="/workspace/".concat(r.startsWith("/")?r.substring(1):r));let s="".concat(e).concat(r);return console.log("Constructed sandbox URL:",s),s}return console.warn("No sandbox URL or ID available, using path as-is:",l),l}(p,u),z=p.split("/").pop()||p,S=z.split(".").pop()||"",C=["gif","webp"].includes(S.toLowerCase());return(0,s.jsxs)(Y.Zp,{className:"flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,s.jsx)(Y.aR,{className:"h-14 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-b p-2 px-4 space-y-0",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:(0,i.cn)("relative p-2 rounded-xl bg-gradient-to-br from-blue-500/20 to-blue-600/10 border border-blue-500/20 transition-colors",k.bgColor),children:(0,s.jsx)(tn.A,{className:(0,i.cn)("w-5 h-5",k.color)})}),(0,s.jsx)("div",{children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(Y.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:(0,i.W5)(z,25)}),C&&(0,s.jsx)(Q.E,{variant:"outline",className:"ml-2 text-[10px] py-0 px-1.5 h-4 border-amber-300 text-amber-700 dark:border-amber-700 dark:text-amber-400",children:"ANIMATED"})]})})]}),d?(0,s.jsxs)(Q.E,{variant:"secondary",className:"bg-gradient-to-b from-blue-50 to-blue-100 text-blue-700 border border-blue-200/50 dark:from-blue-900/30 dark:to-blue-800/20 dark:text-blue-400 dark:border-blue-800/30 px-2.5 py-1 flex items-center gap-1.5",children:[(0,s.jsx)(ee.A,{className:"h-3.5 w-3.5 animate-spin"}),"Loading image..."]}):(0,s.jsx)(Q.E,{variant:"secondary",className:(0,i.cn)("px-2.5 py-1 transition-colors flex items-center gap-1.5",b?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300"),children:b?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(j.A,{className:"h-3.5 w-3.5"}),"Success"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(v.A,{className:"h-3.5 w-3.5"}),"Failed"]})})]})}),(0,s.jsx)(Y.Wu,{className:"p-0 flex-1 overflow-hidden relative",children:d?(0,s.jsx)("div",{className:"flex flex-col items-center justify-center h-full p-12 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:(0,s.jsxs)("div",{className:"text-center w-full max-w-xs",children:[(0,s.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,s.jsx)(c.Skeleton,{className:"h-12 w-12 rounded-full mx-auto"}),(0,s.jsx)(c.Skeleton,{className:"h-6 w-32 mx-auto"}),(0,s.jsx)(c.Skeleton,{className:"h-4 w-48 mx-auto"})]}),(0,s.jsx)(c.Skeleton,{className:"h-48 w-full rounded-lg mb-6"}),(0,s.jsx)("div",{className:"w-full h-2 bg-zinc-200 dark:bg-zinc-700 rounded-full overflow-hidden",children:(0,s.jsx)("div",{className:"h-full bg-gradient-to-r from-blue-400 to-blue-500 dark:from-blue-600 dark:to-blue-400 rounded-full transition-all duration-300 ease-out",style:{width:"".concat(x,"%")}})}),(0,s.jsxs)("p",{className:"text-xs text-zinc-400 dark:text-zinc-500 mt-2",children:[x,"%"]})]})}):(0,s.jsx)("div",{className:"flex flex-col",children:(0,s.jsx)("div",{className:"relative w-full overflow-hidden p-6 flex items-center justify-center",children:(0,s.jsx)(tj,{src:y,alt:f||z,filePath:p,className:"max-w-full max-h-[500px] object-contain"})})})}),(0,s.jsxs)("div",{className:"h-10 px-4 py-2 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:[(0,s.jsxs)(Q.E,{className:"py-0.5 h-6 bg-gradient-to-b from-blue-50 to-blue-100 text-blue-700 border border-blue-200/50 dark:from-blue-900/30 dark:to-blue-800/20 dark:text-blue-400 dark:border-blue-800/30",children:[(0,s.jsx)(tn.A,{className:"h-3 w-3 mr-1"}),"IMAGE"]}),S&&(0,s.jsx)(Q.E,{variant:"outline",className:"py-0 px-1.5 h-5 text-[10px] uppercase font-medium",children:S})]}),(0,s.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:N&&!d?P(N):w?P(w):""})]})]})},"call-mcp-tool":el,ask:function(e){let{name:t="ask",assistantContent:r,toolContent:l,assistantTimestamp:a,toolTimestamp:n,isSuccess:o=!0,isStreaming:c=!1,onFileClick:d,project:m}=e,{text:u,attachments:x,status:h,actualIsSuccess:p,actualToolTimestamp:f,actualAssistantTimestamp:g}=function(e,t,r,s,l){var a,n;let i=null,o=null,c=null,d=r,m=s,u=l,x=tC(e),h=tC(t);if(console.log("AskToolView: Format detection results:",{assistantNewFormat:{hasText:!!x.text,attachmentCount:(null==(a=x.attachments)?void 0:a.length)||0,hasStatus:!!x.status},toolNewFormat:{hasText:!!h.text,attachmentCount:(null==(n=h.attachments)?void 0:n.length)||0,hasStatus:!!h.status}}),x.text||x.attachments||x.status)i=x.text,o=x.attachments,c=x.status,void 0!==x.success&&(d=x.success),x.timestamp&&(u=x.timestamp),console.log("AskToolView: Using assistant new format data");else if(h.text||h.attachments||h.status)i=h.text,o=h.attachments,c=h.status,void 0!==h.success&&(d=h.success),h.timestamp&&(m=h.timestamp),console.log("AskToolView: Using tool new format data");else{let r=tA(e),s=tA(t);i=r.text||s.text,o=r.attachments||s.attachments,c=r.status||s.status,console.log("AskToolView: Using legacy format data:",{hasText:!!i,attachmentCount:(null==o?void 0:o.length)||0,hasStatus:!!c})}return console.log("AskToolView: Final extracted data:",{hasText:!!i,attachmentCount:(null==o?void 0:o.length)||0,hasStatus:!!c,actualIsSuccess:d}),{text:i,attachments:o,status:c,actualIsSuccess:d,actualToolTimestamp:m,actualAssistantTimestamp:u}}(r,l,o,n,a),b=e=>null!==(e.split("/").pop()||"").match(/\.(jpg|jpeg|png|gif|webp|svg|bmp)$/i),N=e=>{var t;let r=(null==(t=e.split(".").pop())?void 0:t.toLowerCase())||"";return"html"===r||"htm"===r||"md"===r||"markdown"===r||"csv"===r||"tsv"===r},w=O(t)||"Ask User",k=e=>{d&&d(e)};return(0,s.jsxs)(Y.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,s.jsx)(Y.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,s.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"relative p-2 rounded-xl bg-gradient-to-br from-blue-500/20 to-blue-600/10 border border-blue-500/20",children:(0,s.jsx)(tk.A,{className:"w-5 h-5 text-blue-500 dark:text-blue-400"})}),(0,s.jsx)("div",{children:(0,s.jsx)(Y.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:w})})]}),!c&&(0,s.jsxs)(Q.E,{variant:"secondary",className:p?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[p?(0,s.jsx)(j.A,{className:"h-3.5 w-3.5 mr-1"}):(0,s.jsx)(v.A,{className:"h-3.5 w-3.5 mr-1"}),p?"Success":"Failed"]}),c&&(0,s.jsxs)(Q.E,{className:"bg-gradient-to-b from-blue-200 to-blue-100 text-blue-700 dark:from-blue-800/50 dark:to-blue-900/60 dark:text-blue-300",children:[(0,s.jsx)(ee.A,{className:"h-3.5 w-3.5 animate-spin mr-1"}),"Asking user"]})]})}),(0,s.jsx)(Y.Wu,{className:"p-0 flex-1 overflow-hidden relative",children:(0,s.jsx)(K.F,{className:"h-full w-full",children:(0,s.jsx)("div",{className:"p-4 space-y-6",children:x&&x.length>0?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-muted-foreground",children:[(0,s.jsx)(ty.A,{className:"h-4 w-4"}),"Files (",x.length,")"]}),(0,s.jsx)("div",{className:(0,i.cn)("grid gap-3",1===x.length?"grid-cols-1":x.length>4?"grid-cols-1 sm:grid-cols-2 md:grid-cols-3":"grid-cols-1 sm:grid-cols-2"),children:x.sort((e,t)=>{let r=b(e),s=b(t),l=N(e),a=N(t);return r&&!s?-1:!r&&s?1:l&&!a?-1:!l&&a?1:0}).map((e,t)=>{var r;let l=b(e),a=N(e),n=x.length%2==1&&x.length>1&&t===x.length-1;return(0,s.jsx)("div",{className:(0,i.cn)("relative group",l?"flex items-center justify-center h-full":"",a?"w-full":""),style:n||a?{gridColumn:"1 / -1"}:void 0,children:(0,s.jsx)(tE.m,{filepath:e,onClick:k,sandboxId:null==m||null==(r=m.sandbox)?void 0:r.id,showPreview:!0,className:(0,i.cn)("w-full",l?"h-auto min-h-[54px]":a?"min-h-[240px] max-h-[400px] overflow-auto":"h-[54px]"),customStyle:l?{width:"100%",height:"auto","--attachment-height":n?"240px":"180px"}:a||n?{gridColumn:"1 / -1"}:{width:"100%"},collapsed:!1,project:m})},t)})})]}):(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4",children:(0,s.jsx)(tz.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-foreground mb-2",children:"Question Asked"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"No files attached to this question"})]})})})}),(0,s.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,s.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:(0,s.jsxs)(Q.E,{className:"h-6 py-0.5",variant:"outline",children:[(0,s.jsx)(tk.A,{className:"h-3 w-3"}),"User Interaction"]})}),(0,s.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:g?P(g):""})]})]})},complete:function(e){let{name:t="complete",assistantContent:r,toolContent:l,assistantTimestamp:n,toolTimestamp:o,isSuccess:c=!0,isStreaming:d=!1,onFileClick:m}=e,[u,x]=(0,a.useState)({}),[h,p]=(0,a.useState)(0);(0,a.useEffect)(()=>{if(r)try{let e=G(r);if(!e)return;let t=e.replace(/<function_calls>[\s\S]*?<\/function_calls>/g,"").replace(/<invoke name="complete"[\s\S]*?<\/invoke>/g,"").trim(),s=t.match(/<complete[^>]*>([^<]*)<\/complete>/);s?x(e=>({...e,summary:s[1].trim()})):t&&x(e=>({...e,summary:t}));let l=e.match(/attachments=["']([^"']*)["']/i);if(l){let e=l[1].split(",").map(e=>e.trim()).filter(e=>e.length>0);x(t=>({...t,attachments:e}))}let a=t.match(/- ([^\n]+)/g);if(a){let e=a.map(e=>e.replace("- ","").trim());x(t=>({...t,tasksCompleted:e}))}}catch(e){console.error("Error parsing complete content:",e)}},[r]),(0,a.useEffect)(()=>{if(l&&!d)try{let e=G(l);if(!e)return;let t=e.match(/ToolResult\([^)]*output=['"]([^'"]+)['"]/);t?x(e=>({...e,result:t[1]})):x(t=>({...t,result:e}))}catch(e){console.error("Error parsing tool response:",e)}},[l,d]),(0,a.useEffect)(()=>{if(d){let e=setInterval(()=>{p(t=>t>=95?(clearInterval(e),t):t+5)},300);return()=>clearInterval(e)}p(100)},[d]);let f=O(t)||"Task Complete",g=e=>{m&&m(e)};return(0,s.jsxs)(Y.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,s.jsx)(Y.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,s.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"relative p-2 rounded-lg bg-gradient-to-br from-emerald-500/20 to-emerald-600/10 border border-emerald-500/20",children:(0,s.jsx)(t_.A,{className:"w-5 h-5 text-emerald-500 dark:text-emerald-400"})}),(0,s.jsx)("div",{children:(0,s.jsx)(Y.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:f})})]}),!d&&(0,s.jsxs)(Q.E,{variant:"secondary",className:c?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[c?(0,s.jsx)(j.A,{className:"h-3.5 w-3.5 mr-1"}):(0,s.jsx)(v.A,{className:"h-3.5 w-3.5 mr-1"}),c?"Completed":"Failed"]}),d&&(0,s.jsxs)(Q.E,{className:"bg-gradient-to-b from-blue-200 to-blue-100 text-blue-700 dark:from-blue-800/50 dark:to-blue-900/60 dark:text-blue-300",children:[(0,s.jsx)(ee.A,{className:"h-3.5 w-3.5 animate-spin mr-1"}),"Completing"]})]})}),(0,s.jsx)(Y.Wu,{className:"p-0 flex-1 overflow-hidden relative",children:(0,s.jsx)(K.F,{className:"h-full w-full",children:(0,s.jsxs)("div",{className:"p-4 space-y-6",children:[!d&&c&&!u.summary&&!u.tasksCompleted&&!u.attachments&&(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"w-20 h-20 rounded-full bg-gradient-to-br from-emerald-100 to-emerald-200 dark:from-emerald-800/40 dark:to-emerald-900/60 flex items-center justify-center",children:(0,s.jsx)(tR.A,{className:"h-10 w-10 text-emerald-600 dark:text-emerald-400"})}),(0,s.jsx)("div",{className:"absolute -top-1 -right-1",children:(0,s.jsx)(tL.A,{className:"h-5 w-5 text-yellow-500 animate-pulse"})})]})}),u.summary&&(0,s.jsx)("div",{className:"space-y-2",children:(0,s.jsx)("div",{className:"bg-muted/50 rounded-2xl p-4 border border-border",children:(0,s.jsx)(tT.o,{className:"text-sm prose prose-sm dark:prose-invert chat-markdown max-w-none [&>:first-child]:mt-0 prose-headings:mt-3",children:u.summary})})}),u.attachments&&u.attachments.length>0&&(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-muted-foreground",children:[(0,s.jsx)(ty.A,{className:"h-4 w-4"}),"Files (",u.attachments.length,")"]}),(0,s.jsx)("div",{className:"grid grid-cols-1 gap-2",children:u.attachments.map((e,t)=>{let{icon:r,color:l,bgColor:a}=H(e),n=e.split("/").pop()||e,o=e.includes("/")?e.substring(0,e.lastIndexOf("/")):"";return(0,s.jsxs)("button",{onClick:()=>g(e),className:"flex items-center gap-3 p-3 bg-muted/30 rounded-lg border border-border/50 hover:bg-muted/50 transition-colors group cursor-pointer text-left",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:(0,i.cn)("w-10 h-10 rounded-lg bg-gradient-to-br flex items-center justify-center",a),children:(0,s.jsx)(r,{className:(0,i.cn)("h-5 w-5",l)})})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-foreground truncate",children:n}),o&&(0,s.jsx)("p",{className:"text-xs text-muted-foreground truncate",children:o})]}),(0,s.jsx)("div",{className:"flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,s.jsx)(en.A,{className:"h-4 w-4 text-muted-foreground"})})]},t)})})]}),u.tasksCompleted&&u.tasksCompleted.length>0&&(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-muted-foreground",children:[(0,s.jsx)(tF.A,{className:"h-4 w-4"}),"Tasks Completed"]}),(0,s.jsx)("div",{className:"space-y-2",children:u.tasksCompleted.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-muted/30 rounded-lg border border-border/50",children:[(0,s.jsx)("div",{className:"mt-1 flex-shrink-0",children:(0,s.jsx)(j.A,{className:"h-4 w-4 text-emerald-500"})}),(0,s.jsx)("div",{className:"flex-1 min-w-0",children:(0,s.jsx)(tT.o,{className:"text-sm prose prose-sm dark:prose-invert chat-markdown max-w-none [&>:first-child]:mt-0 [&>:last-child]:mb-0",children:e})})]},t))})]}),d&&(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,s.jsx)("span",{className:"text-muted-foreground",children:"Completing task..."}),(0,s.jsxs)("span",{className:"text-muted-foreground text-xs",children:[h,"%"]})]}),(0,s.jsx)(er,{value:h,className:"h-1"})]}),!u.summary&&!u.result&&!u.attachments&&!u.tasksCompleted&&!d&&(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,s.jsx)("div",{className:"w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4",children:(0,s.jsx)(t_.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,s.jsx)("h3",{className:"text-lg font-medium text-foreground mb-2",children:"Task Completed"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"No additional details provided"})]})]})})}),(0,s.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,s.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:(0,s.jsxs)(Q.E,{className:"h-6 py-0.5",variant:"outline",children:[(0,s.jsx)(t_.A,{className:"h-3 w-3 mr-1"}),"Task Completion"]})}),(0,s.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:o&&!d?P(o):n?P(n):""})]})]})},deploy:function(e){let{name:t="deploy",assistantContent:r,toolContent:l,assistantTimestamp:n,toolTimestamp:i,isSuccess:o=!0,isStreaming:c=!1}=e,{name:d,directoryPath:m,deployResult:u,rawContent:x}=function(e,t){let r=null,s=null,l=null,a=null,n=G(e);if(n)try{let e=JSON.parse(n);e.parameters&&(r=e.parameters.name||null,s=e.parameters.directory_path||null)}catch(l){let e=n.match(/name["']\s*:\s*["']([^"']+)["']/),t=n.match(/directory_path["']\s*:\s*["']([^"']+)["']/);e&&(r=e[1]),t&&(s=t[1])}let i=G(t);if(i){a=i;try{let e=JSON.parse(i),t=null;if(e.tool_execution&&e.tool_execution.result?(t=e.tool_execution.result,!r&&e.tool_execution.arguments&&(r=e.tool_execution.arguments.name||null,s=e.tool_execution.arguments.directory_path||null)):e.output&&(t=e),t){var o,c;if((l={message:(null==(o=t.output)?void 0:o.message)||null,output:(null==(c=t.output)?void 0:c.output)||null,success:void 0===t.success||t.success}).output){let e=l.output.match(/https:\/\/[^\s]+\.pages\.dev[^\s]*/);e&&(l.url=e[0])}}}catch(e){l={message:"Deploy completed",output:i,success:!0}}}return{name:r,directoryPath:s,deployResult:l,rawContent:a}}(r,l),h=O(t),p=(null==u?void 0:u.success)!==void 0?u.success:o,f=a.useMemo(()=>{if(!(null==u?void 0:u.output))return[];let e=u.output;return(e=(e=e.replace(/\u001b\[[0-9;]*m/g,"")).replace(/\\n/g,"\n")).split("\n").filter(e=>e.trim().length>0)},[null==u?void 0:u.output]);return(0,s.jsxs)(Y.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,s.jsx)(Y.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,s.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"relative p-2 rounded-lg bg-gradient-to-br from-orange-500/20 to-orange-600/10 border border-orange-500/20",children:(0,s.jsx)(tX.A,{className:"w-5 h-5 text-orange-500 dark:text-orange-400"})}),(0,s.jsx)("div",{children:(0,s.jsx)(Y.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:h})})]}),!c&&(0,s.jsxs)(Q.E,{variant:"secondary",className:p?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[p?(0,s.jsx)(j.A,{className:"h-3.5 w-3.5 mr-1"}):(0,s.jsx)(v.A,{className:"h-3.5 w-3.5 mr-1"}),p?"Deploy successful":"Deploy failed"]})]})}),(0,s.jsx)(Y.Wu,{className:"p-0 h-full flex-1 overflow-hidden relative",children:c?(0,s.jsx)(es,{icon:tX.A,iconColor:"text-orange-500 dark:text-orange-400",bgColor:"bg-gradient-to-b from-orange-100 to-orange-50 shadow-inner dark:from-orange-800/40 dark:to-orange-900/60 dark:shadow-orange-950/20",title:"Deploying website",filePath:d||"Processing deployment...",showProgress:!0}):(0,s.jsx)(K.F,{className:"h-full w-full",children:(0,s.jsx)("div",{className:"p-4",children:p&&u?(0,s.jsxs)("div",{className:"space-y-4",children:[u.url&&(0,s.jsx)("div",{className:"bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-lg shadow-sm",children:(0,s.jsxs)("div",{className:"p-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)("div",{className:"w-6 h-6 rounded bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center",children:(0,s.jsx)(ei.A,{className:"w-3.5 h-3.5 text-emerald-600 dark:text-emerald-400"})}),(0,s.jsx)("span",{className:"text-sm font-medium text-zinc-900 dark:text-zinc-100",children:"Website Deployed"}),(0,s.jsx)(Q.E,{variant:"outline",className:"text-xs h-5 px-1.5 bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800",children:"Live"})]}),(0,s.jsx)("div",{className:"bg-zinc-50 dark:bg-zinc-800 rounded p-2 mb-3",children:(0,s.jsx)("code",{className:"text-xs font-mono text-zinc-700 dark:text-zinc-300 break-all",children:u.url})}),(0,s.jsx)(g.$,{asChild:!0,size:"sm",className:"w-full bg-emerald-600 hover:bg-emerald-700 text-white h-8",children:(0,s.jsxs)("a",{href:u.url,target:"_blank",rel:"noopener noreferrer",children:[(0,s.jsx)(en.A,{className:"h-3.5 w-3.5 mr-2"}),"Open Website"]})})]})}),f.length>0&&(0,s.jsxs)("div",{className:"bg-zinc-100 dark:bg-neutral-900 rounded-lg overflow-hidden border border-zinc-200/20",children:[(0,s.jsxs)("div",{className:"bg-accent px-4 py-2 flex items-center gap-2",children:[(0,s.jsx)(ed.A,{className:"h-4 w-4 text-zinc-600 dark:text-zinc-400"}),(0,s.jsx)("span",{className:"text-sm font-medium text-zinc-700 dark:text-zinc-300",children:"Deployment Log"})]}),(0,s.jsx)("div",{className:"p-4 max-h-96 overflow-auto scrollbar-hide",children:(0,s.jsx)("pre",{className:"text-xs text-zinc-600 dark:text-zinc-300 font-mono whitespace-pre-wrap break-all",children:f.map((e,t)=>(0,s.jsx)("div",{className:"py-0.5",children:e||" "},t))})})]})]}):(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,s.jsx)(v.A,{className:"h-5 w-5 text-red-600"}),(0,s.jsx)("h3",{className:"font-medium text-red-900 dark:text-red-100",children:"Deployment Failed"})]}),(0,s.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300",children:"The deployment encountered an error. Check the logs below for details."})]}),x&&(0,s.jsxs)("div",{className:"bg-zinc-100 dark:bg-neutral-900 rounded-lg overflow-hidden border border-zinc-200/20",children:[(0,s.jsxs)("div",{className:"bg-accent px-4 py-2 flex items-center gap-2",children:[(0,s.jsx)(ed.A,{className:"h-4 w-4 text-zinc-600 dark:text-zinc-400"}),(0,s.jsx)("span",{className:"text-sm font-medium text-zinc-700 dark:text-zinc-300",children:"Error Details"})]}),(0,s.jsx)("div",{className:"p-4 max-h-96 overflow-auto scrollbar-hide",children:(0,s.jsx)("pre",{className:"text-xs text-zinc-600 dark:text-zinc-300 font-mono whitespace-pre-wrap break-all",children:x})})]})]})})})})]})},default:el};class tQ{register(e,t){this.registry[e]=t}registerMany(e){Object.assign(this.registry,e)}get(e){return this.registry[e]||this.registry.default}has(e){return e in this.registry}getToolNames(){return Object.keys(this.registry).filter(e=>"default"!==e)}clear(){this.registry={default:this.registry.default}}constructor(e={}){this.registry={...tY},Object.entries(e).forEach(e=>{let[t,r]=e;void 0!==r&&(this.registry[t]=r)})}}let tK=new tQ;function t0(e){let{name:t="default",...r}=e,l=(0,a.useMemo)(()=>tK.get(t),[t]);return(0,s.jsx)(l,{name:t,...r})}var t1=r(76408),t5=r(60760);function t2(e){var t,r,n,b;let{isOpen:j,onClose:v,toolCalls:N,currentIndex:w,onNavigate:k,messages:y,agentStatus:z,project:S,isLoading:C=!1,externalNavigateToIndex:A,agentName:E,onFileClick:_}=e,[R,L]=a.useState(""),[F,T]=a.useState(0),[I,P]=a.useState("live"),[O,W]=a.useState([]),[D,U]=a.useState(!1),V=(0,f.a)(),J=a.useCallback(()=>{v()},[v]);a.useEffect(()=>{var e,t,r;let s=N.map((e,t)=>({id:"".concat(t,"-").concat(e.assistantCall.timestamp||Date.now()),toolCall:e,index:t,timestamp:Date.now()}));O.length;let l=s.length>O.length;if(W(s),!D&&s.length>0){if(s.filter(e=>{var t;return(null==(t=e.toolCall.toolResult)?void 0:t.content)&&"STREAMING"!==e.toolCall.toolResult.content}).length>0){let t=-1;for(let r=s.length-1;r>=0;r--){let l=s[r];if((null==(e=l.toolCall.toolResult)?void 0:e.content)&&"STREAMING"!==l.toolCall.toolResult.content){t=r;break}}T(Math.max(0,t))}else T(Math.max(0,s.length-1));U(!0)}else if(l&&"live"===I){let e=s[s.length-1];if((null==e||null==(t=e.toolCall.toolResult)?void 0:t.content)==="STREAMING"){let e=-1;for(let t=s.length-1;t>=0;t--){let l=s[t];if((null==(r=l.toolCall.toolResult)?void 0:r.content)&&"STREAMING"!==l.toolCall.toolResult.content){e=t;break}}e>=0?T(e):T(s.length-1)}else T(s.length-1)}},[N,I,O.length,D]),a.useEffect(()=>{j&&!D&&O.length>0&&T(Math.min(w,O.length-1))},[j,w,D,O.length]);let M=Math.min(F,Math.max(0,O.length-1)),B=O[M],$=null==B?void 0:B.toolCall,q=O.length,Z=O.filter(e=>{var t;return(null==(t=e.toolCall.toolResult)?void 0:t.content)&&"STREAMING"!==e.toolCall.toolResult.content}),G=Z.length,H=$,X=M,Y=q,Q=(null==$||null==(t=$.toolResult)?void 0:t.content)==="STREAMING";if(Q&&G>0)H=Z[Z.length-1].toolCall,X=G-1,Y=G;else if(!Q){let e=Z.findIndex(e=>e.id===(null==B?void 0:B.id));e>=0&&(X=e,Y=G)}null==H||null==(r=H.assistantCall)||r.name,(0,l.S8)((null==$||null==(n=$.assistantCall)?void 0:n.name)||"unknown");let K=(null==H||null==(b=H.toolResult)?void 0:b.content)==="STREAMING",ee=!!K||(e=>{var t,r,s,l,a,n,i,o,c,d,m,u,x,h;let p=null==e||null==(t=e.toolResult)?void 0:t.content;if(!p)return null==(o=null==e||null==(r=e.toolResult)?void 0:r.isSuccess)||o;let f=e=>{try{return"string"==typeof e?JSON.parse(e):e}catch(e){return null}},g=f(p);if(!g)return null==(c=null==e||null==(s=e.toolResult)?void 0:s.isSuccess)||c;if(g.content){let e=f(g.content);if((null==e||null==(m=e.tool_execution)||null==(d=m.result)?void 0:d.success)!==void 0)return e.tool_execution.result.success}let b=null!=(x=null!=(u=null==(a=g.tool_execution)||null==(l=a.result)?void 0:l.success)?u:null==(n=g.result)?void 0:n.success)?x:g.success;return void 0!==b?b:null==(h=null==e||null==(i=e.toolResult)?void 0:i.isSuccess)||h})(H),et=a.useCallback(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"internal";if(e<0||e>=q)return;let r=e===q-1;console.log("[INTERNAL_NAV] ".concat(t,": ").concat(F," -> ").concat(e,", mode will be: ").concat(r?"live":"manual")),T(e),r?P("live"):P("manual"),"user_explicit"===t&&k(e)},[F,q,k]),er="live"===I,es=a.useCallback(()=>{if(X>0){let e=Z[X-1];if(e){let t=O.findIndex(t=>t.id===e.id);t>=0&&(P("manual"),et(t,"user_explicit"))}}},[X,Z,O,et]),el=a.useCallback(()=>{if(X<Y-1){let e=X+1,t=Z[e];if(t){let r=O.findIndex(e=>e.id===t.id);r>=0&&(e===Z.length-1?P("live"):P("manual"),et(r,"user_explicit"))}}},[X,Y,Z,O,et]),ea=a.useCallback(()=>{P("live"),et(q-1,"user_explicit")},[q,et]),en=a.useCallback(()=>{P("manual"),et(q-1,"user_explicit")},[q,et]),ei=a.useCallback(()=>{let e="flex items-center justify-center gap-1.5 px-2 py-0.5 rounded-full w-[116px]",t="w-1.5 h-1.5 rounded-full",r="text-xs font-medium";if(er)if("running"===z)return(0,s.jsxs)("div",{className:"".concat(e," bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800"),children:[(0,s.jsx)("div",{className:"".concat(t," bg-green-500 animate-pulse")}),(0,s.jsx)("span",{className:"".concat(r," text-green-700 dark:text-green-400"),children:"Live Updates"})]});else return(0,s.jsxs)("div",{className:"".concat(e," bg-neutral-50 dark:bg-neutral-900/20 border border-neutral-200 dark:border-neutral-800"),children:[(0,s.jsx)("div",{className:"".concat(t," bg-neutral-500")}),(0,s.jsx)("span",{className:"".concat(r," text-neutral-700 dark:text-neutral-400"),children:"Latest Tool"})]});return"running"===z?(0,s.jsxs)("div",{className:"".concat(e," bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors cursor-pointer"),onClick:ea,children:[(0,s.jsx)("div",{className:"".concat(t," bg-green-500 animate-pulse")}),(0,s.jsx)("span",{className:"".concat(r," text-green-700 dark:text-green-400"),children:"Jump to Live"})]}):(0,s.jsxs)("div",{className:"".concat(e," bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors cursor-pointer"),onClick:en,children:[(0,s.jsx)("div",{className:"".concat(t," bg-blue-500")}),(0,s.jsx)("span",{className:"".concat(r," text-blue-700 dark:text-blue-400"),children:"Jump to Latest"})]})},[er,z,ea,en]),eo=a.useCallback(e=>{let[t]=e,r=Z[t];if(r){let e=O.findIndex(e=>e.id===r.id);e>=0&&(t===Z.length-1?P("live"):P("manual"),et(e,"user_explicit"))}},[Z,O,et]);return(a.useEffect(()=>{if(!j)return;let e=e=>{(e.metaKey||e.ctrlKey)&&"i"===e.key&&(e.preventDefault(),J())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[j,J]),a.useEffect(()=>{if(!j)return;let e=e=>{e.detail.expanded&&J()};return window.addEventListener("sidebar-left-toggled",e),()=>window.removeEventListener("sidebar-left-toggled",e)},[j,J]),a.useEffect(()=>{void 0!==A&&A>=0&&A<q&&et(A,"external_click")},[A,q,et]),a.useEffect(()=>{if(!K)return;let e=setInterval(()=>{L(e=>"..."===e?"":e+".")},500);return()=>clearInterval(e)},[K]),j)?C?(0,s.jsx)("div",{className:"fixed inset-0 z-30 pointer-events-none",children:(0,s.jsx)("div",{className:"p-4 h-full flex items-stretch justify-end pointer-events-auto",children:(0,s.jsx)("div",{className:(0,i.cn)("border rounded-2xl flex flex-col shadow-2xl bg-background",V?"w-full":"w-[90%] sm:w-[450px] md:w-[500px] lg:w-[550px] xl:w-[650px]"),children:(0,s.jsx)("div",{className:"flex-1 flex flex-col overflow-hidden",children:(0,s.jsxs)("div",{className:"flex flex-col h-full",children:[(0,s.jsx)("div",{className:"pt-4 pl-4 pr-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{className:"ml-2 flex items-center gap-2",children:(0,s.jsx)("h2",{className:"text-lg font-medium text-zinc-900 dark:text-zinc-100",children:E?"".concat(E,"'s Computer"):"Suna's Computer"})}),(0,s.jsx)(g.$,{variant:"ghost",size:"icon",onClick:J,className:"h-8 w-8",title:"Minimize to floating preview",children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})]})}),(0,s.jsx)("div",{className:"flex-1 p-4 overflow-auto",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(c.Skeleton,{className:"h-8 w-32"}),(0,s.jsx)(c.Skeleton,{className:"h-20 w-full rounded-md"}),(0,s.jsx)(c.Skeleton,{className:"h-40 w-full rounded-md"}),(0,s.jsx)(c.Skeleton,{className:"h-20 w-full rounded-md"})]})})]})})})})}):(0,s.jsx)(t5.N,{mode:"wait",children:j&&(0,s.jsxs)(t1.P.div,{layoutId:"tool-panel-float",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{opacity:{duration:.15},layout:{type:"spring",stiffness:400,damping:35}},className:(0,i.cn)("fixed top-2 right-2 bottom-4 border rounded-3xl flex flex-col z-30",V?"left-2":"w-[40vw] sm:w-[450px] md:w-[500px] lg:w-[550px] xl:w-[645px]"),style:{overflow:"hidden"},children:[(0,s.jsx)("div",{className:"flex-1 flex flex-col overflow-hidden bg-card",children:(()=>{var e,t,r,a;if(!H&&0===O.length)return(0,s.jsxs)("div",{className:"flex flex-col h-full",children:[(0,s.jsx)("div",{className:"pt-4 pl-4 pr-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{className:"ml-2 flex items-center gap-2",children:(0,s.jsx)("h2",{className:"text-lg font-medium text-zinc-900 dark:text-zinc-100",children:E?"".concat(E,"'s Computer"):"Suna's Computer"})}),(0,s.jsx)(g.$,{variant:"ghost",size:"icon",onClick:J,className:"h-8 w-8",children:(0,s.jsx)(m.A,{className:"h-4 w-4"})})]})}),(0,s.jsx)("div",{className:"flex flex-col items-center justify-center flex-1 p-8",children:(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-4 max-w-sm text-center",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-zinc-100 dark:bg-zinc-800 rounded-full flex items-center justify-center",children:(0,s.jsx)(u.A,{className:"h-8 w-8 text-zinc-400 dark:text-zinc-500"})}),(0,s.jsx)("div",{className:"absolute -bottom-1 -right-1 w-6 h-6 bg-zinc-200 dark:bg-zinc-700 rounded-full flex items-center justify-center",children:(0,s.jsx)("div",{className:"w-2 h-2 bg-zinc-400 dark:text-zinc-500 rounded-full"})})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-zinc-900 dark:text-zinc-100",children:"No tool activity"}),(0,s.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400 leading-relaxed",children:"Tool calls and computer interactions will appear here when they're being executed."})]})]})})]});if(!H&&O.length>0){let e=O.find(e=>{var t;return(null==(t=e.toolCall.toolResult)?void 0:t.content)==="STREAMING"});return e&&0===G?(0,s.jsxs)("div",{className:"flex flex-col h-full",children:[(0,s.jsx)("div",{className:"pt-4 pl-4 pr-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{className:"ml-2 flex items-center gap-2",children:(0,s.jsx)("h2",{className:"text-lg font-medium text-zinc-900 dark:text-zinc-100",children:E?"".concat(E,"'s Computer"):"Suna's Computer"})}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)("div",{className:"px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400 flex items-center gap-1.5",children:[(0,s.jsx)(x.A,{className:"h-3 w-3 animate-spin"}),(0,s.jsx)("span",{children:"Running"})]}),(0,s.jsx)(g.$,{variant:"ghost",size:"icon",onClick:J,className:"h-8 w-8 ml-1",children:(0,s.jsx)(m.A,{className:"h-4 w-4"})})]})]})}),(0,s.jsx)("div",{className:"flex flex-col items-center justify-center flex-1 p-8",children:(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-4 max-w-sm text-center",children:[(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("div",{className:"w-16 h-16 bg-blue-50 dark:bg-blue-900/20 rounded-full flex items-center justify-center",children:(0,s.jsx)(x.A,{className:"h-8 w-8 text-blue-500 dark:text-blue-400 animate-spin"})})}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-zinc-900 dark:text-zinc-100",children:"Tool is running"}),(0,s.jsxs)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400 leading-relaxed",children:[(0,l.qR)(e.toolCall.assistantCall.name||"Tool")," is currently executing. Results will appear here when complete."]})]})]})})]}):(0,s.jsxs)("div",{className:"flex flex-col h-full",children:[(0,s.jsx)("div",{className:"pt-4 pl-4 pr-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{className:"ml-2 flex items-center gap-2",children:(0,s.jsx)("h2",{className:"text-lg font-medium text-zinc-900 dark:text-zinc-100",children:E?"".concat(E,"'s Computer"):"Suna's Computer"})}),(0,s.jsx)(g.$,{variant:"ghost",size:"icon",onClick:J,className:"h-8 w-8",children:(0,s.jsx)(m.A,{className:"h-4 w-4"})})]})}),(0,s.jsx)("div",{className:"flex-1 p-4 overflow-auto",children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(c.Skeleton,{className:"h-8 w-32"}),(0,s.jsx)(c.Skeleton,{className:"h-20 w-full rounded-md"})]})})]})}let n=(0,s.jsx)(t0,{name:H.assistantCall.name,assistantContent:H.assistantCall.content,toolContent:null==(e=H.toolResult)?void 0:e.content,assistantTimestamp:H.assistantCall.timestamp,toolTimestamp:null==(t=H.toolResult)?void 0:t.timestamp,isSuccess:ee,isStreaming:K,project:S,messages:y,agentStatus:z,currentIndex:X,totalCalls:Y,onFileClick:_});return(0,s.jsxs)("div",{className:"flex flex-col h-full",children:[(0,s.jsx)(t1.P.div,{layoutId:"tool-panel-content",className:"p-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(t1.P.div,{layoutId:"tool-icon",className:"ml-2 flex items-center gap-2",children:(0,s.jsx)("h2",{className:"text-lg font-medium text-zinc-900 dark:text-zinc-100",children:E?"".concat(E,"'s Computer"):"Suna's Computer"})}),(null==(r=H.toolResult)?void 0:r.content)&&!K&&(0,s.jsx)("div",{className:"flex items-center gap-2",children:(0,s.jsx)(g.$,{variant:"ghost",size:"icon",onClick:J,className:"h-8 w-8 ml-1",title:"Minimize to floating preview",children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})}),K&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)("div",{className:"px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400 flex items-center gap-1.5",children:[(0,s.jsx)(x.A,{className:"h-3 w-3 animate-spin"}),(0,s.jsx)("span",{children:"Running"})]}),(0,s.jsx)(g.$,{variant:"ghost",size:"icon",onClick:J,className:"h-8 w-8 ml-1",title:"Minimize to floating preview",children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})]}),!(null==(a=H.toolResult)?void 0:a.content)&&!K&&(0,s.jsx)(g.$,{variant:"ghost",size:"icon",onClick:J,className:"h-8 w-8",title:"Minimize to floating preview",children:(0,s.jsx)(d.A,{className:"h-4 w-4"})})]})}),(0,s.jsx)("div",{className:"flex-1 overflow-auto scrollbar-thin scrollbar-thumb-zinc-300 dark:scrollbar-thumb-zinc-700 scrollbar-track-transparent",children:n})]})})()}),(Y>1||Q&&G>0)&&(0,s.jsx)("div",{className:(0,i.cn)("border-t border-zinc-200 dark:border-zinc-800 bg-zinc-50 dark:bg-zinc-900",V?"p-2":"px-4 py-2.5"),children:V?(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)(g.$,{variant:"outline",size:"sm",onClick:es,disabled:X<=0,className:"h-8 px-2.5 text-xs",children:[(0,s.jsx)(h.A,{className:"h-3.5 w-3.5 mr-1"}),(0,s.jsx)("span",{children:"Prev"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,s.jsxs)("span",{className:"text-xs text-zinc-600 dark:text-zinc-400 font-medium tabular-nums min-w-[44px]",children:[X+1,"/",Y]}),ei()]}),(0,s.jsxs)(g.$,{variant:"outline",size:"sm",onClick:el,disabled:X>=Y-1,className:"h-8 px-2.5 text-xs",children:[(0,s.jsx)("span",{children:"Next"}),(0,s.jsx)(p.A,{className:"h-3.5 w-3.5 ml-1"})]})]}):(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(g.$,{variant:"ghost",size:"icon",onClick:es,disabled:X<=0,className:"h-7 w-7 text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-200",children:(0,s.jsx)(h.A,{className:"h-4 w-4"})}),(0,s.jsxs)("span",{className:"text-xs text-zinc-600 dark:text-zinc-400 font-medium tabular-nums px-1 min-w-[44px] text-center",children:[X+1,"/",Y]}),(0,s.jsx)(g.$,{variant:"ghost",size:"icon",onClick:el,disabled:X>=Y-1,className:"h-7 w-7 text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-200",children:(0,s.jsx)(p.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("div",{className:"flex-1 relative",children:(0,s.jsx)(o,{min:0,max:Y-1,step:1,value:[X],onValueChange:eo,className:"w-full [&>span:first-child]:h-1.5 [&>span:first-child]:bg-zinc-200 dark:[&>span:first-child]:bg-zinc-800 [&>span:first-child>span]:bg-zinc-500 dark:[&>span:first-child>span]:bg-zinc-400 [&>span:first-child>span]:h-1.5"})}),(0,s.jsx)("div",{className:"flex items-center gap-1.5",children:ei()})]})})]},"sidepanel")}):null}},34513:(e,t,r)=>{r.d(t,{$:()=>h});var s=r(95155),l=r(12115),a=r(40033),n=r(14098),i=r(72781),o=r(59434),c=r(66424),d=r(96976),m=r(51362),u=r(73603);let x={js:i.cg.javascript,jsx:i.cg.jsx,ts:i.cg.typescript,tsx:i.cg.tsx,html:i.cg.html,css:i.cg.css,json:i.cg.json,md:i.cg.markdown,python:i.cg.python,py:i.cg.python,rust:i.cg.rust,go:i.cg.go,java:i.cg.java,c:i.cg.c,cpp:i.cg.cpp,cs:i.cg.csharp,php:i.cg.php,ruby:i.cg.ruby,sh:i.cg.shell,bash:i.cg.shell,sql:i.cg.sql,yaml:i.cg.yaml,yml:i.cg.yaml};function h(e){let{content:t,language:r="",className:i}=e,{resolvedTheme:h}=(0,m.D)(),[p,f]=(0,l.useState)(!1);(0,l.useEffect)(()=>{f(!0)},[]);let g=[...r&&x[r]?[x[r]()]:[],u.Lz.lineWrapping],b=p&&"dark"===h?n.Ts:d._x;return(0,s.jsx)(c.F,{className:(0,o.cn)("w-full h-full",i),children:(0,s.jsx)("div",{className:"w-full",children:(0,s.jsx)(a.Ay,{value:t,theme:b,extensions:g,basicSetup:{lineNumbers:!1,highlightActiveLine:!1,highlightActiveLineGutter:!1,foldGutter:!1},editable:!1,className:"text-sm w-full min-h-full",style:{maxWidth:"100%"},height:"auto"})})})}},51548:(e,t,r)=>{r.d(t,{S:()=>Y});var s=r(95155),l=r(12115),a=r(54165),n=r(30285),i=r(94631),o=r(42355),c=r(13052),d=r(57340),m=r(91788),u=r(57434),x=r(66474),h=r(39022),p=r(29869),f=r(1243),g=r(21380),b=r(99890),j=r(66424),v=r(59434),N=r(12969),w=r(34513),k=r(13824),y=r(55404),z=r(49280);function S(e){let{url:t,className:r}=e,[a,n]=(0,l.useState)(null),[i,o]=(0,l.useState)(1),[c,d]=(0,l.useState)(1);function m(e){o(t=>{let r=t+e;return r>=1&&r<=(a||1)?r:t})}return(0,s.jsxs)("div",{className:(0,v.cn)("flex flex-col w-full h-full",r),children:[(0,s.jsx)("div",{className:"flex-1 overflow-auto rounded-md",children:(0,s.jsx)(y.A,{file:t,onLoadSuccess:function(e){let{numPages:t}=e;n(t)},className:"flex flex-col items-center",children:(0,s.jsx)(z.A,{pageNumber:i,scale:c,renderTextLayer:!0,renderAnnotationLayer:!0})})}),a&&(0,s.jsxs)("div",{className:"flex items-center justify-between p-2 bg-background border-t",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{onClick:function(){d(e=>Math.max(e-.2,.5))},className:"px-2 py-1 bg-muted rounded hover:bg-muted/80",disabled:c<=.5,children:"-"}),(0,s.jsxs)("span",{children:[Math.round(100*c),"%"]}),(0,s.jsx)("button",{onClick:function(){d(e=>Math.min(e+.2,3))},className:"px-2 py-1 bg-muted rounded hover:bg-muted/80",disabled:c>=3,children:"+"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("button",{onClick:function(){m(-1)},className:"px-2 py-1 bg-muted rounded hover:bg-muted/80",disabled:i<=1,children:"Previous"}),(0,s.jsxs)("span",{children:["Page ",i," of ",a]}),(0,s.jsx)("button",{onClick:function(){m(1)},className:"px-2 py-1 bg-muted rounded hover:bg-muted/80",disabled:i>=a,children:"Next"})]})]})]})}r(88851),r(42465),k.EA.workerSrc=new r.U(r(27266)).toString();var C=r(6262),A=r(54481),E=r(32568),_=r(81284),R=r(91981),L=r(74311);function F(e){let{url:t,className:r}=e,[a,i]=(0,l.useState)(1),[o,c]=(0,l.useState)(0),[d,m]=(0,l.useState)(!1),[u,x]=(0,l.useState)({x:0,y:0}),[h,p]=(0,l.useState)({x:0,y:0}),[f,g]=(0,l.useState)(!0),[b,j]=(0,l.useState)(!1),[N,w]=(0,l.useState)(!1),[k,y]=(0,l.useState)(null),z=(0,l.useRef)(null),S=(0,l.useRef)(null),F=(null==t?void 0:t.toLowerCase().endsWith(".svg"))||(null==t?void 0:t.includes("image/svg"));(0,l.useEffect)(()=>{f&&x({x:0,y:0})},[a,f]);let T=()=>{if(j(!0),w(!1),S.current){var e;y({width:S.current.naturalWidth,height:S.current.naturalHeight,type:F?"SVG":(null==(e=t.split(".").pop())?void 0:e.toUpperCase())||"Image"})}},I=()=>{j(!1),w(!0)},P="scale(".concat(a,") rotate(").concat(o,"deg)"),O="translate(".concat(u.x,"px, ").concat(u.y,"px)"),[W,D]=(0,l.useState)(!1);return(0,s.jsxs)("div",{className:(0,v.cn)("flex flex-col w-full h-full",r),children:[(0,s.jsxs)("div",{className:"flex items-center justify-between py-2 px-4 bg-muted/30 border-b mb-2 rounded-t-md",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:()=>{let e=Math.max(a-.25,.5);i(e),.5===e&&g(!0)},title:"Zoom out",disabled:N,children:(0,s.jsx)(C.A,{className:"h-4 w-4"})}),(0,s.jsxs)("span",{className:"text-xs font-medium",children:[Math.round(100*a),"%"]}),(0,s.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:()=>{i(e=>Math.min(e+.25,3)),g(!1)},title:"Zoom in",disabled:N,children:(0,s.jsx)(A.A,{className:"h-4 w-4"})}),(0,s.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:()=>{c(e=>(e+90)%360)},title:"Rotate",disabled:N,children:(0,s.jsx)(E.A,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:()=>D(!W),title:"Image information",children:(0,s.jsx)(_.A,{className:"h-4 w-4"})}),(0,s.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:()=>{f?(i(1),g(!1)):(i(.5),x({x:0,y:0}),g(!0))},title:f?"Actual size":"Fit to screen",disabled:N,children:f?(0,s.jsx)(R.A,{className:"h-4 w-4"}):(0,s.jsx)(L.A,{className:"h-4 w-4"})})]})]}),W&&k&&(0,s.jsxs)("div",{className:"absolute top-16 right-4 z-50 bg-background/80 backdrop-blur-sm p-3 rounded-md shadow-md border border-border text-xs",children:[(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Type:"})," ",k.type]}),(0,s.jsxs)("p",{children:[(0,s.jsx)("strong",{children:"Dimensions:"})," ",k.width," \xd7 ",k.height]})]}),(0,s.jsx)("div",{ref:z,className:"flex-1 overflow-hidden relative bg-grid-pattern rounded-b-md",onMouseDown:e=>{a>.5&&(m(!0),p({x:e.clientX-u.x,y:e.clientY-u.y}))},onMouseMove:e=>{d&&a>.5&&x({x:e.clientX-h.x,y:e.clientY-h.y})},onMouseUp:()=>{m(!1)},onMouseLeave:()=>{m(!1)},style:{cursor:d?"grabbing":a>.5?"grab":"default",backgroundColor:"#f5f5f5",backgroundImage:"linear-gradient(45deg, #e0e0e0 25%, transparent 25%), linear-gradient(-45deg, #e0e0e0 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #e0e0e0 75%), linear-gradient(-45deg, transparent 75%, #e0e0e0 75%)",backgroundSize:"20px 20px",backgroundPosition:"0 0, 0 10px, 10px -10px, -10px 0px"},children:N?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-full p-6 text-center",children:[(0,s.jsx)("p",{className:"text-destructive font-medium mb-2",children:"Failed to load image"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"The image could not be displayed"})]}):(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",style:{transform:f?"none":O,transition:d?"none":"transform 0.1s ease"},children:F?(0,s.jsx)("object",{data:t,type:"image/svg+xml",className:"max-w-full max-h-full",style:{transform:P,transition:"transform 0.2s ease",width:"100%",height:"100%"},children:(0,s.jsx)("img",{ref:S,src:t,alt:"SVG preview",className:"max-w-full max-h-full object-contain",style:{transform:P,transition:"transform 0.2s ease"},draggable:!1,onLoad:T,onError:I})}):(0,s.jsx)("img",{ref:S,src:t,alt:"Image preview",className:"max-w-full max-h-full object-contain",style:{transform:P,transition:"transform 0.2s ease"},draggable:!1,onLoad:T,onError:I})})})]})}function T(e){var t;let{url:r,fileName:l,className:a,onDownload:o,isDownloading:c=!1}=e,d=(null==(t=l.split(".").pop())?void 0:t.toLowerCase())||"";return(0,s.jsx)("div",{className:(0,v.cn)("flex flex-col items-center justify-center p-10",a),children:(0,s.jsxs)("div",{className:"flex flex-col items-center text-center max-w-md",children:[(0,s.jsxs)("div",{className:"relative mb-6",children:[(0,s.jsx)(b.A,{className:"h-24 w-24 text-muted-foreground/50"}),(0,s.jsx)("div",{className:"absolute bottom-1 right-1 bg-background rounded-sm px-1.5 py-0.5 text-xs font-medium text-muted-foreground border",children:d.toUpperCase()})]}),(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:l.split("/").pop()}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mb-6",children:"This binary file cannot be previewed in the browser"}),(0,s.jsxs)(n.$,{variant:"default",className:"min-w-[150px]",onClick:()=>{if(o)o();else if(r){console.log("[BINARY RENDERER] Using fallback download for ".concat(l));let e=document.createElement("a");e.href=r,e.download=l.split("/").pop()||"file",document.body.appendChild(e),e.click(),document.body.removeChild(e)}else console.error("[BINARY RENDERER] No download URL or handler available")},disabled:c,children:[c?(0,s.jsx)(i.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,s.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Download"]})]})})}var I=r(14738),P=r(29621),O=r(33786);function W(e){let{content:t,previewUrl:r,className:a}=e,[i,o]=(0,l.useState)("preview");return(0,s.jsx)("div",{className:(0,v.cn)("w-full h-full flex flex-col",a),children:(0,s.jsxs)("div",{className:"flex-1 min-h-0 relative",children:[(0,s.jsxs)("div",{className:"absolute left-2 top-2 z-10 flex items-center gap-2",children:[(0,s.jsxs)(n.$,{variant:"ghost",size:"sm",className:(0,v.cn)("flex items-center gap-2 bg-background/80 backdrop-blur-sm hover:bg-background/90","preview"===i&&"bg-background/90"),onClick:()=>o("preview"),children:[(0,s.jsx)(I.A,{className:"h-4 w-4"}),"Preview"]}),(0,s.jsxs)(n.$,{variant:"ghost",size:"sm",className:(0,v.cn)("flex items-center gap-2 bg-background/80 backdrop-blur-sm hover:bg-background/90","code"===i&&"bg-background/90"),onClick:()=>o("code"),children:[(0,s.jsx)(P.A,{className:"h-4 w-4"}),"Code"]}),(0,s.jsxs)(n.$,{variant:"ghost",size:"sm",className:"flex items-center gap-2 bg-background/80 backdrop-blur-sm hover:bg-background/90",onClick:()=>window.open(r,"_blank"),children:[(0,s.jsx)(O.A,{className:"h-4 w-4"}),"Open"]})]}),"preview"===i?(0,s.jsx)("div",{className:"absolute inset-0",children:(0,s.jsx)("iframe",{src:r,title:"HTML Preview",className:"w-full h-full border-0",sandbox:"allow-same-origin allow-scripts"})}):(0,s.jsx)("div",{className:"absolute inset-0",children:(0,s.jsx)(w.$,{content:t,language:"html",className:"w-full h-full"})})]})})}var D=r(63496),U=r(64384);function V(e){var t,r,a;let{content:n,binaryUrl:i,fileName:o,className:c,project:d,markdownRef:m,onDownload:u,isDownloading:x}=e,h=function(e){var t;let r=(null==(t=e.split(".").pop())?void 0:t.toLowerCase())||"";if(["md","markdown"].includes(r))return"markdown";if(["js","jsx","ts","tsx","html","css","json","py","python","java","c","cpp","h","cs","go","rs","php","rb","sh","bash","xml","yml","yaml","toml","sql","graphql","swift","kotlin","dart","r","lua","scala","perl","haskell","rust"].includes(r))return"code";if(["png","jpg","jpeg","gif","webp","svg","bmp","ico"].includes(r))return"image";if(["pdf"].includes(r))return"pdf";if(["csv","tsv"].includes(r))return"csv";else if(["txt","log","env","ini"].includes(r))return"text";else return"binary"}(o),p={js:"javascript",jsx:"jsx",ts:"typescript",tsx:"tsx",html:"html",css:"css",json:"json",py:"python",python:"python",java:"java",c:"c",cpp:"cpp",h:"c",cs:"csharp",go:"go",rs:"rust",php:"php",rb:"ruby",sh:"shell",bash:"shell",xml:"xml",yml:"yaml",yaml:"yaml",sql:"sql"}[(null==(a=o.split(".").pop())?void 0:a.toLowerCase())||""]||"",f=o.toLowerCase().endsWith(".html"),g=l.useMemo(()=>{var e;if(f&&n&&!(null==d||null==(e=d.sandbox)?void 0:e.sandbox_url)){let e=new Blob([n],{type:"text/html"});return URL.createObjectURL(e)}},[f,n,null==d||null==(t=d.sandbox)?void 0:t.sandbox_url]),b=f&&(null==d||null==(r=d.sandbox)?void 0:r.sandbox_url)&&o?(0,D.i)(d.sandbox.sandbox_url,o):g;return l.useEffect(()=>()=>{g&&URL.revokeObjectURL(g)},[g]),(0,s.jsx)("div",{className:(0,v.cn)("w-full h-full",c),children:"binary"===h?(0,s.jsx)(T,{url:i||"",fileName:o,onDownload:u,isDownloading:x}):"image"===h&&i?(0,s.jsx)(F,{url:i}):"pdf"===h&&i?(0,s.jsx)(S,{url:i}):"markdown"===h?(0,s.jsx)(N.T,{content:n||"",ref:m}):"csv"===h?(0,s.jsx)(U.W,{content:n||""}):f?(0,s.jsx)(W,{content:n||"",previewUrl:b||"",className:"w-full h-full"}):"code"===h||"text"===h?(0,s.jsx)(w.$,{content:n||"",language:p,className:"w-full h-full"}):(0,s.jsx)("div",{className:"w-full h-full p-4",children:(0,s.jsx)("pre",{className:"text-sm font-mono whitespace-pre-wrap break-words leading-relaxed bg-muted/30 p-4 rounded-lg overflow-auto max-h-full",children:n||""})})})}var J=r(25731),M=r(56671),B=r(52643),$=r(64541),q=r(44838),Z=r(30257),G=r(59311),H=r.n(G),X=r(90066);function Y(e){let{open:t,onOpenChange:r,sandboxId:v,initialFilePath:N,project:w,filePathList:k}=e,y="string"==typeof N?N:null,{session:z}=(0,$.A)(),[S,C]=(0,l.useState)("/workspace"),[A,E]=(0,l.useState)(!0),[_,R]=(0,l.useState)(-1),L=!!(k&&k.length>0);(0,l.useEffect)(()=>{console.log("[FILE VIEWER DEBUG] filePathList changed:",{filePathList:k,length:null==k?void 0:k.length,isFileListMode:L,currentFileIndex:_})},[k,L,_]);let{data:F=[],isLoading:T,error:I,refetch:P}=(0,Z.R2)(v,S,{enabled:t&&!!v,staleTime:3e4}),O=(0,l.useRef)(null),[W,D]=(0,l.useState)(null),[U,G]=(0,l.useState)(null),[Y,Q]=(0,l.useState)(null),[K,ee]=(0,l.useState)(null),[et,er]=(0,l.useState)(null),{data:es,isLoading:el,error:ea}=(0,Z.x2)(v,W||void 0,{enabled:!!W,staleTime:3e5}),[en,ei]=(0,l.useState)(!1),[eo,ec]=(0,l.useState)(!1),ed=(0,l.useRef)(null),[em,eu]=(0,l.useState)(!1),[ex,eh]=(0,l.useState)(w),[ep,ef]=(0,l.useState)(!1),eg=(0,l.useRef)(null),eb=(0,l.useRef)(new Set),[ej,ev]=(0,l.useState)(!1),[eN,ew]=(0,l.useState)(null);(0,l.useEffect)(()=>{w&&eh(w)},[w,v]);let ek=(0,l.useCallback)(e=>"string"==typeof e&&e?e.startsWith("/workspace")?e:"/workspace/".concat(e.replace(/^\//,"")):(console.warn("[FILE VIEWER] normalizePath received non-string or empty value:",e,"Returning '/workspace'"),"/workspace"),[]),ey=(0,l.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"/workspace",t=[],r=0,s=new Set,l=async e=>{if(!s.has(e)){s.add(e);try{for(let s of(console.log("[DOWNLOAD ALL] Exploring directory: ".concat(e)),await (0,J.XY)(v,e)))s.is_dir?await l(s.path):(t.push(s),r+=s.size||0)}catch(t){console.error("[DOWNLOAD ALL] Error exploring directory ".concat(e,":"),t),M.oR.error("Failed to read directory: ".concat(e))}}};return await l(e),console.log("[DOWNLOAD ALL] Discovered ".concat(t.length," files, total size: ").concat(r," bytes")),{files:t,totalSize:r}},[v]),ez=(0,l.useCallback)(async()=>{if((null==z?void 0:z.access_token)&&!ej)try{ev(!0),ew({current:0,total:0,currentFile:"Discovering files..."});let{files:e}=await ey();if(0===e.length)return void M.oR.error("No files found to download");console.log("[DOWNLOAD ALL] Starting download of ".concat(e.length," files"));let t=new(H());ew({current:0,total:e.length,currentFile:"Creating archive..."});for(let r=0;r<e.length;r++){let s=e[r],l=s.path.replace(/^\/workspace\//,"");ew({current:r+1,total:e.length,currentFile:l});try{let a=Z.pH.getContentTypeFromPath(s.path),n="".concat(v,":").concat(s.path,":").concat(a),i=Z.pH.get(n);if(!i){console.log("[DOWNLOAD ALL] Loading file from server: ".concat(s.path));let e=await fetch("".concat("http://localhost:8000/api","/sandboxes/").concat(v,"/files/content?path=").concat(encodeURIComponent(s.path)),{headers:{Authorization:"Bearer ".concat(z.access_token)}});if(!e.ok){console.warn("[DOWNLOAD ALL] Failed to load file: ".concat(s.path," (").concat(e.status,")"));continue}i="blob"===a?await e.blob():"json"===a?JSON.stringify(await e.json(),null,2):await e.text(),Z.pH.set(n,i)}if(i instanceof Blob)t.file(l,i);else if("string"==typeof i)if(i.startsWith("blob:"))try{let e=await fetch(i),r=await e.blob();t.file(l,r)}catch(r){console.warn("[DOWNLOAD ALL] Failed to fetch blob content for: ".concat(s.path),r);let e=await fetch("".concat("http://localhost:8000/api","/sandboxes/").concat(v,"/files/content?path=").concat(encodeURIComponent(s.path)),{headers:{Authorization:"Bearer ".concat(z.access_token)}});if(e.ok){let r=await e.blob();t.file(l,r)}}else t.file(l,i);else t.file(l,JSON.stringify(i,null,2));console.log("[DOWNLOAD ALL] Added to zip: ".concat(l," (").concat(r+1,"/").concat(e.length,")"))}catch(e){console.error("[DOWNLOAD ALL] Error processing file ".concat(s.path,":"),e)}}ew({current:e.length,total:e.length,currentFile:"Generating zip file..."}),console.log("[DOWNLOAD ALL] Generating zip file...");let r=await t.generateAsync({type:"blob",compression:"DEFLATE",compressionOptions:{level:6}}),s=URL.createObjectURL(r),l=document.createElement("a");l.href=s,l.download="workspace-".concat(v,"-").concat(new Date().toISOString().slice(0,10),".zip"),document.body.appendChild(l),l.click(),document.body.removeChild(l),setTimeout(()=>URL.revokeObjectURL(s),1e4),M.oR.success("Downloaded ".concat(e.length," files as zip archive")),console.log("[DOWNLOAD ALL] Successfully created zip with ".concat(e.length," files"))}catch(e){console.error("[DOWNLOAD ALL] Error creating zip:",e),M.oR.error("Failed to create zip archive: ".concat(e instanceof Error?e.message:String(e)))}finally{ev(!1),ew(null)}},[v,null==z?void 0:z.access_token,ej,ey]),eS=e=>e instanceof Blob,eC=(0,l.useCallback)(()=>{console.log("[FILE VIEWER DEBUG] clearSelectedFile called, isFileListMode: ".concat(L)),D(null),G(null),Q(null),ee(null),er(null),L?console.log("[FILE VIEWER DEBUG] Keeping currentFileIndex in clearSelectedFile because in file list mode"):(console.log("[FILE VIEWER DEBUG] Resetting currentFileIndex in clearSelectedFile"),R(-1))},[L]),eA=(0,l.useCallback)(async e=>{var t;if(e.is_dir){let t=ek(e.path);console.log("[FILE VIEWER] Navigating to folder: ".concat(e.path," → ").concat(t)),eC(),C(t);return}if(W===e.path)return void console.log("[FILE VIEWER] File already selected: ".concat(e.path));console.log("[FILE VIEWER] Opening file: ".concat(e.path));let r=Z.pH.isImageFile(e.path),s=Z.pH.isPdfFile(e.path),l=null==(t=e.path.split(".").pop())?void 0:t.toLowerCase(),a=["xlsx","xls","docx","doc","pptx","ppt"].includes(l||"");r?console.log("[FILE VIEWER][IMAGE DEBUG] Opening image file: ".concat(e.path)):s?console.log("[FILE VIEWER] Opening PDF file: ".concat(e.path)):a&&console.log("[FILE VIEWER] Opening Office document: ".concat(e.path," (").concat(l,")")),eC(),D(e.path),L&&(null==k?void 0:k.includes(e.path))?console.log("[FILE VIEWER DEBUG] Keeping currentFileIndex because file is in file list mode"):(console.log("[FILE VIEWER DEBUG] Resetting currentFileIndex because not in file list mode or file not in list"),R(-1))},[W,eC,ek,L,k]);(0,l.useEffect)(()=>{if(t&&v){if(T&&O.current===S)return void console.log("[FILE VIEWER] Already loading ".concat(S,", skipping duplicate load"));O.current=S,console.log("[FILE VIEWER] Starting navigation to: ".concat(S)),console.log("[FILE VIEWER] React Query will handle directory listing for: ".concat(S)),A&&E(!1),I&&(console.error("Failed to load files:",I),M.oR.error("Failed to load files"))}},[t,v,S,A,T,I]);let eE=(0,l.useCallback)(e=>{if(!e.is_dir)return;let t=ek(e.path);console.log("[FILE VIEWER] Navigating to folder: ".concat(e.path," → ").concat(t)),console.log("[FILE VIEWER] Current path before navigation: ".concat(S)),eC(),C(t)},[ek,eC,S]),e_=(0,l.useCallback)(e=>{let t=ek(e);console.log("[FILE VIEWER] Navigating to breadcrumb path: ".concat(e," → ").concat(t)),eC(),C(t)},[ek,eC]),eR=(0,l.useCallback)(()=>{console.log("[FILE VIEWER] Navigating home from:",S),eC(),C("/workspace")},[eC,S]),eL=(0,l.useCallback)(e=>{let t=ek(e).replace(/^\/workspace\/?/,"");if(!t)return[];let r=t.split("/").filter(Boolean),s="/workspace";return r.map((e,t)=>(s="".concat(s,"/").concat(e),{name:e,path:s,isLast:t===r.length-1}))},[ek]);(0,l.useCallback)(e=>{let t=e;t.startsWith("/workspace")||(t="/workspace/".concat(t.startsWith("/")?t.substring(1):t));let r=Z.pH.getContentTypeFromPath(e),s="".concat(v,":").concat(t,":").concat(r);if(console.log("[FILE VIEWER] Checking cache for key: ".concat(s)),Z.pH.has(s)){let e=Z.pH.get(s);return console.log("[FILE VIEWER] Direct cache hit for ".concat(t," (").concat(r,")")),{found:!0,content:e,contentType:r}}return console.log("[FILE VIEWER] Cache miss for key: ".concat(s)),{found:!1,content:null,contentType:r}},[v]);let eF=(0,l.useCallback)(e=>{if(console.log("[FILE VIEWER DEBUG] navigateToFileByIndex called:",{index:e,isFileListMode:L,filePathList:k,filePathListLength:null==k?void 0:k.length}),!L||!k||e<0||e>=k.length)return void console.log("[FILE VIEWER DEBUG] navigateToFileByIndex early return - invalid conditions");let t=k[e];console.log("[FILE VIEWER DEBUG] Setting currentFileIndex to:",e,"for file:",t),R(e),eA({name:t.split("/").pop()||"",path:ek(t),is_dir:!1,size:0,mod_time:new Date().toISOString()})},[L,k,ek,eA]),eT=(0,l.useCallback)(()=>{_>0&&eF(_-1)},[_,eF]),eI=(0,l.useCallback)(()=>{L&&k&&_<k.length-1&&eF(_+1)},[_,L,k,eF]);(0,l.useEffect)(()=>{if(t&&y&&!em){if(console.log("[FILE VIEWER] useEffect[initialFilePath]: Processing initial path: ".concat(y)),L&&k){console.log("[FILE VIEWER DEBUG] Initial file path - file list mode detected:",{isFileListMode:L,filePathList:k,safeInitialFilePath:y,filePathListLength:k.length});let e=ek(y),t=k.findIndex(t=>ek(t)===e);if(console.log("[FILE VIEWER DEBUG] Found index for initial file:",{normalizedInitialPath:e,index:t,foundPath:-1!==t?k[t]:"not found"}),-1!==t){console.log("[FILE VIEWER] File list mode: navigating to index ".concat(t," for ").concat(e)),eF(t),eu(!0);return}}let e=ek(y),t=e.lastIndexOf("/"),r=t>0?e.substring(0,t):"/workspace",s=t>=0?e.substring(t+1):"";if(console.log("[FILE VIEWER] useEffect[initialFilePath]: Normalized Path: ".concat(e,", Directory: ").concat(r,", File: ").concat(s)),S!==r&&(console.log("[FILE VIEWER] useEffect[initialFilePath]: Setting current path to ".concat(r)),C(r)),y){console.log("[FILE VIEWER] Attempting to load initial file directly from cache: ".concat(y));let t={name:s,path:e,is_dir:!1,size:0,mod_time:new Date().toISOString()};console.log("[FILE VIEWER] Opening initial file: ".concat(e)),eA(t)}eu(!0)}else t||(console.log("[FILE VIEWER] useEffect[initialFilePath]: Modal closed, resetting initialPathProcessed flag."),eu(!1))},[t,y,em,ek,S,eA,L,k,eF]),(0,l.useEffect)(()=>{if(W){if(ea)return void er("Failed to load file: ".concat(ea.message));if(null!==es&&!el){var e;console.log("[FILE VIEWER] Received cached content for: ".concat(W));let t=Z.pH.isImageFile(W),r=Z.pH.isPdfFile(W),s=["xlsx","xls","docx","doc","pptx","ppt"].includes((null==(e=W.split(".").pop())?void 0:e.toLowerCase())||"");if(G(es),"string"==typeof es)es.startsWith("blob:")?(console.log("[FILE VIEWER] Setting blob URL from cached content: ".concat(es)),Q(null),ee(es)):t||r||s?(console.warn("[FILE VIEWER] Binary file received as string content, this should not happen: ".concat(W)),Q(null),ee(null),er("Binary file received in incorrect format. Please try refreshing.")):(console.log("[FILE VIEWER] Setting text content for text file: ".concat(W)),Q(es),ee(null));else if(eS(es)){let e=URL.createObjectURL(es);console.log("[FILE VIEWER] Created blob URL: ".concat(e," for ").concat(W)),ee(e),Q(null)}else if("object"==typeof es){let e=JSON.stringify(es,null,2);console.log("[FILE VIEWER] Setting text content for object file: ".concat(W)),Q(e),ee(null)}else console.warn("[FILE VIEWER] Unknown content type for: ".concat(W),typeof es),Q(null),ee(null),er("Unknown content type received.")}}},[W,es,el,ea]),(0,l.useEffect)(()=>()=>{!K||eo||eb.current.has(K)||(console.log("[FILE VIEWER] Revoking blob URL on cleanup: ".concat(K)),URL.revokeObjectURL(K))},[K,eo]),(0,l.useEffect)(()=>{if(!t||!L)return;let e=e=>{"ArrowLeft"===e.key?(e.preventDefault(),eT()):"ArrowRight"===e.key&&(e.preventDefault(),eI())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[t,L,eT,eI]);let eP=(0,l.useCallback)(e=>{e||(console.log("[FILE VIEWER] handleOpenChange: Modal closing, resetting state."),!K||eo||eb.current.has(K)||(console.log("[FILE VIEWER] Manually revoking blob URL on modal close: ".concat(K)),URL.revokeObjectURL(K)),eC(),C("/workspace"),eu(!1),E(!0),R(-1),ev(!1),ew(null)),r(e)},[r,eC,E,K,eo]),eO=(0,l.useCallback)(e=>!!e&&e.toLowerCase().endsWith(".md"),[]),eW=(0,l.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"portrait";if(W&&!ep&&eO(W)){ef(!0);try{if(!eg.current)throw Error("Markdown content not found");let t=window.open("","_blank");if(!t)throw Error("Unable to open print window. Please check if popup blocker is enabled.");window.location.origin;let r=(W.split("/").pop()||"document").replace(/\.md$/,""),s=eg.current.innerHTML,l='\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset="UTF-8">\n          <title>'.concat(r,"</title>\n          <style>\n            @media print {\n              @page { \n                size: ").concat("landscape"===e?"A4 landscape":"A4",";\n                margin: 15mm;\n              }\n              body {\n                -webkit-print-color-adjust: exact;\n                print-color-adjust: exact;\n              }\n            }\n            body {\n              font-family: 'Helvetica', 'Arial', sans-serif;\n              font-size: 12pt;\n              color: #333;\n              line-height: 1.5;\n              padding: 20px;\n              max-width: 100%;\n              margin: 0 auto;\n              background: white;\n            }\n            h1 { font-size: 24pt; margin-top: 20pt; margin-bottom: 12pt; }\n            h2 { font-size: 20pt; margin-top: 18pt; margin-bottom: 10pt; }\n            h3 { font-size: 16pt; margin-top: 16pt; margin-bottom: 8pt; }\n            h4, h5, h6 { font-weight: bold; margin-top: 12pt; margin-bottom: 6pt; }\n            p { margin: 8pt 0; }\n            pre, code {\n              font-family: 'Courier New', monospace;\n              background-color: #f5f5f5;\n              border-radius: 3pt;\n              padding: 2pt 4pt;\n              font-size: 10pt;\n            }\n            pre {\n              padding: 8pt;\n              margin: 8pt 0;\n              overflow-x: auto;\n              white-space: pre-wrap;\n            }\n            code {\n              white-space: pre-wrap;\n            }\n            img {\n              max-width: 100%;\n              height: auto;\n            }\n            a {\n              color: #0066cc;\n              text-decoration: underline;\n            }\n            ul, ol {\n              padding-left: 20pt;\n              margin: 8pt 0;\n            }\n            blockquote {\n              margin: 8pt 0;\n              padding-left: 12pt;\n              border-left: 4pt solid #ddd;\n              color: #666;\n            }\n            table {\n              border-collapse: collapse;\n              width: 100%;\n              margin: 12pt 0;\n            }\n            th, td {\n              border: 1pt solid #ddd;\n              padding: 6pt;\n              text-align: left;\n            }\n            th {\n              background-color: #f5f5f5;\n              font-weight: bold;\n            }\n            /* Syntax highlighting basic styles */\n            .hljs-keyword, .hljs-selector-tag { color: #569cd6; }\n            .hljs-literal, .hljs-number { color: #b5cea8; }\n            .hljs-string { color: #ce9178; }\n            .hljs-comment { color: #6a9955; }\n            .hljs-attribute, .hljs-attr { color: #9cdcfe; }\n            .hljs-function, .hljs-name { color: #dcdcaa; }\n            .hljs-title.class_ { color: #4ec9b0; }\n            .markdown-content pre { background-color: #f8f8f8; }\n          </style>\n        </head>\n        <body>\n          <div class=\"markdown-content\">\n            ").concat(s,"\n          </div>\n          <script>\n            // Remove any complex CSS variables or functions that might cause issues\n            document.querySelectorAll('[style]').forEach(el => {\n              const style = el.getAttribute('style');\n              if (style && (style.includes('oklch') || style.includes('var(--') || style.includes('hsl('))) {\n                // Replace complex color values with simple ones or remove them\n                el.setAttribute('style', style\n                  .replace(/color:.*?(;|$)/g, 'color: #333;')\n                  .replace(/background-color:.*?(;|$)/g, 'background-color: transparent;')\n                );\n              }\n            });\n            \n            // Print automatically when loaded\n            window.onload = () => {\n              setTimeout(() => {\n                window.print();\n                setTimeout(() => window.close(), 500);\n              }, 300);\n            };\n          <\/script>\n        </body>\n        </html>\n      ");t.document.open(),t.document.write(l),t.document.close(),M.oR.success("PDF export initiated. Check your print dialog.")}catch(e){console.error("PDF export failed:",e),M.oR.error("Failed to export PDF: ".concat(e instanceof Error?e.message:String(e)))}finally{ef(!1)}}},[W,ep,eO]),eD=async()=>{if(W&&!eo)try{var e;ec(!0);let t=W.split("/").pop()||"file",r=(null==(e=Z.pH.getMimeTypeFromPath)?void 0:e.call(Z.pH,W))||"application/octet-stream";if(U){let e;if("string"==typeof U)if(U.startsWith("blob:")){let t=await fetch("".concat("http://localhost:8000/api","/sandboxes/").concat(v,"/files/content?path=").concat(encodeURIComponent(W)),{headers:{Authorization:"Bearer ".concat(null==z?void 0:z.access_token)}});if(!t.ok)throw Error("Server error: ".concat(t.status));e=await t.blob()}else e=new Blob([U],{type:r});else e=U instanceof Blob?U:new Blob([JSON.stringify(U)],{type:"application/json"});e.type!==r&&(e=new Blob([e],{type:r})),eU(e,t);return}let s=await fetch("".concat("http://localhost:8000/api","/sandboxes/").concat(v,"/files/content?path=").concat(encodeURIComponent(W)),{headers:{Authorization:"Bearer ".concat(null==z?void 0:z.access_token)}});if(!s.ok)throw Error("Server error: ".concat(s.status));let l=await s.blob(),a=new Blob([l],{type:r});eU(a,t)}catch(e){console.error("[FILE VIEWER] Download error:",e),M.oR.error("Failed to download file: ".concat(e instanceof Error?e.message:String(e)))}finally{ec(!1)}},eU=(e,t)=>{let r=URL.createObjectURL(e),s=document.createElement("a");s.href=r,s.download=t,document.body.appendChild(s),s.click(),document.body.removeChild(s),eb.current.add(r),setTimeout(()=>{URL.revokeObjectURL(r),eb.current.delete(r)},1e4),M.oR.success("Download started")},eV=(0,l.useCallback)(()=>{ed.current&&ed.current.click()},[]),eJ=(0,l.useCallback)(async e=>{if(!e.target.files||0===e.target.files.length)return;let t=e.target.files[0];ei(!0);try{let e=(0,X.L)(t.name),r="".concat(S,"/").concat(e),s=new FormData;s.append("file",t,e),s.append("path",r);let l=(0,B.U)(),{data:{session:a}}=await l.auth.getSession();if(!(null==a?void 0:a.access_token))throw Error("No access token available");let n=await fetch("".concat("http://localhost:8000/api","/sandboxes/").concat(v,"/files"),{method:"POST",headers:{Authorization:"Bearer ".concat(a.access_token)},body:s});if(!n.ok){let e=await n.text();throw Error(e||"Upload failed")}await P(),M.oR.success("Uploaded: ".concat(e))}catch(e){console.error("Upload failed:",e),M.oR.error("Upload failed: ".concat(e instanceof Error?e.message:String(e)))}finally{ei(!1),e.target&&(e.target.value="")}},[S,v,P]);return(0,l.useEffect)(()=>{t&&!k&&R(-1)},[t,k]),(0,s.jsx)(a.lG,{open:t,onOpenChange:eP,children:(0,s.jsxs)(a.Cf,{className:"sm:max-w-[90vw] md:max-w-[1200px] w-[95vw] h-[90vh] max-h-[900px] flex flex-col p-0 gap-0 overflow-hidden",children:[(0,s.jsxs)(a.c7,{className:"px-4 py-2 border-b flex-shrink-0 flex flex-row gap-4 items-center",children:[(0,s.jsx)(a.L3,{className:"text-lg font-semibold",children:"Workspace Files"}),eN&&(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(i.A,{className:"h-3 w-3 animate-spin"}),(0,s.jsx)("span",{children:eN.total>0?"".concat(eN.current,"/").concat(eN.total):"Preparing..."})]}),(0,s.jsx)("span",{className:"max-w-[200px] truncate",children:eN.currentFile})]}),(0,s.jsx)("div",{className:"flex items-center gap-2",children:(console.log("[FILE VIEWER DEBUG] Navigation visibility check:",{isFileListMode:L,selectedFilePath:W,filePathList:k,filePathListLength:null==k?void 0:k.length,currentFileIndex:_,shouldShow:L&&W&&k&&k.length>1&&_>=0}),L&&W&&k&&k.length>1&&_>=0&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:eT,disabled:_<=0,className:"h-8 w-8 p-0",title:"Previous file (←)",children:(0,s.jsx)(o.A,{className:"h-4 w-4"})}),(0,s.jsxs)("div",{className:"text-xs text-muted-foreground px-1",children:[_+1," / ",(null==k?void 0:k.length)||0]}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:eI,disabled:_>=((null==k?void 0:k.length)||0)-1,className:"h-8 w-8 p-0",title:"Next file (→)",children:(0,s.jsx)(c.A,{className:"h-4 w-4"})})]}))})]}),(0,s.jsxs)("div",{className:"px-4 py-2 border-b flex items-center gap-2",children:[(0,s.jsx)(n.$,{variant:"ghost",size:"icon",onClick:eR,className:"h-8 w-8",title:"Go to home directory",children:(0,s.jsx)(d.A,{className:"h-4 w-4"})}),(0,s.jsxs)("div",{className:"flex items-center overflow-x-auto flex-1 min-w-0 scrollbar-hide whitespace-nowrap",children:[(0,s.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-7 px-2 text-sm font-medium min-w-fit flex-shrink-0",onClick:eR,children:"home"}),"/workspace"!==S&&(0,s.jsx)(s.Fragment,{children:eL(S).map(e=>(0,s.jsxs)(l.Fragment,{children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mx-1 text-muted-foreground opacity-50 flex-shrink-0"}),(0,s.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-7 px-2 text-sm font-medium truncate max-w-[200px]",onClick:()=>e_(e.path),children:e.name})]},e.path))}),W&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mx-1 text-muted-foreground opacity-50 flex-shrink-0"}),(0,s.jsx)("div",{className:"flex items-center gap-2",children:(0,s.jsx)("span",{className:"text-sm font-medium truncate",children:W.split("/").pop()})})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 flex-shrink-0",children:[W&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(n.$,{variant:"outline",size:"sm",onClick:eD,disabled:eo||el,className:"h-8 gap-1",children:[eo?(0,s.jsx)(i.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(m.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"hidden sm:inline",children:"Download"})]}),eO(W)&&(0,s.jsxs)(q.rI,{children:[(0,s.jsx)(q.ty,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"outline",size:"sm",disabled:ep||el||null!==et,className:"h-8 gap-1",children:[ep?(0,s.jsx)(i.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(u.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"hidden sm:inline",children:"Export as PDF"}),(0,s.jsx)(x.A,{className:"h-3 w-3 ml-1"})]})}),(0,s.jsxs)(q.SQ,{align:"end",children:[(0,s.jsxs)(q._2,{onClick:()=>eW("portrait"),className:"flex items-center gap-2 cursor-pointer",children:[(0,s.jsx)("span",{className:"rotate-90",children:"⬌"})," Portrait"]}),(0,s.jsxs)(q._2,{onClick:()=>eW("landscape"),className:"flex items-center gap-2 cursor-pointer",children:[(0,s.jsx)("span",{children:"⬌"})," Landscape"]})]})]})]}),!W&&(0,s.jsxs)(s.Fragment,{children:["/workspace"===S&&(0,s.jsxs)(n.$,{variant:"outline",size:"sm",onClick:ez,disabled:ej||T,className:"h-8 gap-1",children:[ej?(0,s.jsx)(i.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(h.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"hidden sm:inline",children:"Download All"})]}),(0,s.jsxs)(n.$,{variant:"outline",size:"sm",onClick:eV,disabled:en,className:"h-8 gap-1",children:[en?(0,s.jsx)(i.A,{className:"h-4 w-4 animate-spin"}):(0,s.jsx)(p.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"hidden sm:inline",children:"Upload"})]})]}),(0,s.jsx)("input",{type:"file",ref:ed,className:"hidden",onChange:eJ,disabled:en})]})]}),(0,s.jsx)("div",{className:"flex-1 overflow-hidden",children:W?(0,s.jsx)("div",{className:"h-full w-full overflow-auto",children:el?(0,s.jsxs)("div",{className:"h-full w-full flex flex-col items-center justify-center",children:[(0,s.jsx)(i.A,{className:"h-8 w-8 animate-spin text-primary mb-3"}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Loading file",W?": ".concat(W.split("/").pop()):"..."]}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground/70 mt-1",children:(()=>{if(!W)return"Preparing...";let e=W;e.startsWith("/workspace")||(e="/workspace/".concat(e.startsWith("/")?e.substring(1):e));let t=Z.pH.getContentTypeFromPath(e);return Z.pH.has("".concat(v,":").concat(e,":").concat(t))?"Using cached version":"Fetching from server"})()})]}):et?(0,s.jsx)("div",{className:"h-full w-full flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"max-w-md p-6 text-center border rounded-lg bg-muted/10",children:[(0,s.jsx)(f.A,{className:"h-10 w-10 text-orange-500 mx-auto mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Error Loading File"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:et}),(0,s.jsxs)("div",{className:"flex justify-center gap-3",children:[(0,s.jsx)(n.$,{onClick:()=>{er(null),eA({path:W,name:W.split("/").pop()||"",is_dir:!1,size:0,mod_time:new Date().toISOString()})},children:"Retry"}),(0,s.jsx)(n.$,{variant:"outline",onClick:()=>{eC()},children:"Back to Files"})]})]})}):(0,s.jsx)("div",{className:"h-full w-full relative",children:(()=>{var e;let t=Z.pH.isImageFile(W),r=Z.pH.isPdfFile(W),l=["xlsx","xls","docx","doc","pptx","ppt"].includes((null==W||null==(e=W.split(".").pop())?void 0:e.toLowerCase())||""),a=t||r||l;return a&&!K?(0,s.jsx)("div",{className:"h-full w-full flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Loading ",r?"PDF":t?"image":"file","..."]})}):(0,s.jsx)(V,{content:a?null:Y,binaryUrl:K,fileName:W,className:"h-full w-full",project:ex,markdownRef:eO(W)?eg:void 0,onDownload:eD,isDownloading:eo},W)})()})}):(0,s.jsx)("div",{className:"h-full w-full",children:T?(0,s.jsx)("div",{className:"h-full w-full flex items-center justify-center",children:(0,s.jsx)(i.A,{className:"h-6 w-6 animate-spin text-primary"})}):0===F.length?(0,s.jsxs)("div",{className:"h-full w-full flex flex-col items-center justify-center",children:[(0,s.jsx)(g.A,{className:"h-12 w-12 mb-2 text-muted-foreground opacity-30"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Directory is empty"})]}):(0,s.jsx)(j.F,{className:"h-full w-full p-2",children:(0,s.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3 p-4",children:F.map(e=>(0,s.jsxs)("button",{className:"flex flex-col items-center p-3 rounded-2xl border hover:bg-muted/50 transition-colors ".concat(W===e.path?"bg-muted border-primary/20":""),onClick:()=>{e.is_dir?(console.log("[FILE VIEWER] Folder clicked: ".concat(e.name,", path: ").concat(e.path)),eE(e)):eA(e)},children:[(0,s.jsx)("div",{className:"w-12 h-12 flex items-center justify-center mb-1",children:e.is_dir?(0,s.jsx)(g.A,{className:"h-9 w-9 text-blue-500"}):(0,s.jsx)(b.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,s.jsx)("span",{className:"text-xs text-center font-medium truncate max-w-full",children:e.name})]},e.path))})})})})]})})}},52300:(e,t,r)=>{r.d(t,{y:()=>n});var s=r(95155),l=r(12115),a=r(68856);function n(e){let{isSidePanelOpen:t=!1,showHeader:r=!0,messageCount:n=3}=e;return(0,s.jsxs)("div",{className:"flex h-screen",children:[(0,s.jsxs)("div",{className:"flex flex-col flex-1 overflow-hidden transition-all duration-200 ease-in-out",children:[r&&(0,s.jsx)("div",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,s.jsxs)("div",{className:"flex h-14 items-center gap-4 px-4",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(a.Skeleton,{className:"h-6 w-6 rounded-full"}),(0,s.jsx)(a.Skeleton,{className:"h-5 w-40"})]})}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(a.Skeleton,{className:"h-8 w-8 rounded-full"}),(0,s.jsx)(a.Skeleton,{className:"h-8 w-8 rounded-full"})]})]})}),(0,s.jsx)("div",{className:"flex-1 overflow-y-auto px-6 py-4 pb-[5.5rem]",children:(0,s.jsxs)("div",{className:"mx-auto max-w-3xl space-y-6",children:[Array.from({length:n}).map((e,t)=>(0,s.jsx)(l.Fragment,{children:t%2==0?(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)("div",{className:"max-w-[85%] rounded-lg bg-primary/10 px-4 py-3",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(a.Skeleton,{className:"h-4 w-48"}),(0,s.jsx)(a.Skeleton,{className:"h-4 w-32"})]})})}):(0,s.jsx)("div",{children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(a.Skeleton,{className:"flex-shrink-0 w-5 h-5 mt-2 rounded-full"}),(0,s.jsx)("div",{className:"flex-1 space-y-2",children:(0,s.jsx)("div",{className:"max-w-[90%] w-full rounded-lg bg-muted px-4 py-3",children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(a.Skeleton,{className:"h-4 w-full max-w-[360px] mb-2"}),(0,s.jsx)(a.Skeleton,{className:"h-4 w-full max-w-[320px] mb-2"}),(0,s.jsx)(a.Skeleton,{className:"h-4 w-full max-w-[290px]"})]}),t%3==1&&(0,s.jsx)("div",{className:"py-1",children:(0,s.jsx)(a.Skeleton,{className:"h-6 w-32 rounded-md"})}),t%3==1&&(0,s.jsxs)("div",{children:[(0,s.jsx)(a.Skeleton,{className:"h-4 w-full max-w-[340px] mb-2"}),(0,s.jsx)(a.Skeleton,{className:"h-4 w-full max-w-[280px]"})]})]})})})]})})},t)),(0,s.jsx)("div",{children:(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)(a.Skeleton,{className:"flex-shrink-0 w-5 h-5 mt-2 rounded-full"}),(0,s.jsx)("div",{className:"flex-1 space-y-2",children:(0,s.jsxs)("div",{className:"flex items-center gap-1.5 py-1",children:[(0,s.jsx)("div",{className:"h-1.5 w-1.5 rounded-full bg-gray-400/50 animate-pulse"}),(0,s.jsx)("div",{className:"h-1.5 w-1.5 rounded-full bg-gray-400/50 animate-pulse delay-150"}),(0,s.jsx)("div",{className:"h-1.5 w-1.5 rounded-full bg-gray-400/50 animate-pulse delay-300"})]})})]})})]})})]}),t&&(0,s.jsx)("div",{className:"hidden sm:block",children:(0,s.jsx)("div",{className:"h-screen w-[450px] border-l",children:(0,s.jsxs)("div",{className:"p-4",children:[(0,s.jsx)(a.Skeleton,{className:"h-8 w-32 mb-4"}),(0,s.jsx)(a.Skeleton,{className:"h-20 w-full rounded-md mb-4"}),(0,s.jsx)(a.Skeleton,{className:"h-40 w-full rounded-md"})]})})})]})}},64384:(e,t,r)=>{r.d(t,{W:()=>b});var s=r(95155),l=r(12115),a=r(62523),n=r(30285),i=r(26126),o=r(60408),c=r.n(o),d=r(59434),m=r(21492),u=r(47863),x=r(66474),h=r(64261),p=r(1482),f=r(47924),g=r(44838);function b(e){let{content:t,className:r}=e,[o,b]=(0,l.useState)(""),[j,v]=(0,l.useState)({column:"",direction:null}),[N,w]=(0,l.useState)(new Set),[k,y]=(0,l.useState)(1),[z]=(0,l.useState)(50),S=function(e){if(!e)return{data:[],headers:[],meta:null};try{let t=c().parse(e,{header:!0,skipEmptyLines:!0,dynamicTyping:!0}),r=[];return t.meta&&t.meta.fields&&(r=t.meta.fields||[]),{headers:r,data:t.data,meta:t.meta}}catch(e){return console.error("Error parsing CSV:",e),{headers:[],data:[],meta:null}}}(t),C=0===S.data.length,A=(0,l.useMemo)(()=>{let e=S.data;return o&&(e=e.filter(e=>Object.values(e).some(e=>String(e).toLowerCase().includes(o.toLowerCase())))),j.column&&j.direction&&(e=[...e].sort((e,t)=>{let r=e[j.column],s=t[j.column];if(null==r&&null==s)return 0;if(null==r)return"asc"===j.direction?-1:1;if(null==s)return"asc"===j.direction?1:-1;if("number"==typeof r&&"number"==typeof s)return"asc"===j.direction?r-s:s-r;let l=String(r).toLowerCase(),a=String(s).toLowerCase();return l<a?"asc"===j.direction?-1:1:l>a?"asc"===j.direction?1:-1:0})),e},[S.data,o,j]),E=Math.ceil(A.length/z),_=(k-1)*z,R=A.slice(_,_+z),L=S.headers.filter(e=>!N.has(e)),F=e=>{v(t=>{if(t.column!==e)return{column:e,direction:"asc"};{let r="asc"===t.direction?"desc":"desc"===t.direction?null:"asc";return{column:r?e:"",direction:r}}})},T=e=>{w(t=>{let r=new Set(t);return r.has(e)?r.delete(e):r.add(e),r})},I=e=>j.column!==e?(0,s.jsx)(m.A,{className:"h-3 w-3 text-muted-foreground"}):"asc"===j.direction?(0,s.jsx)(u.A,{className:"h-3 w-3 text-primary"}):(0,s.jsx)(x.A,{className:"h-3 w-3 text-primary"}),P=e=>null==e?"":"number"==typeof e?e.toLocaleString():"boolean"==typeof e?e?"Yes":"No":String(e),O=e=>"number"==typeof e?"text-right font-mono":"boolean"==typeof e?e?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400":"";return C?(0,s.jsx)("div",{className:(0,d.cn)("w-full h-full flex items-center justify-center",r),children:(0,s.jsxs)("div",{className:"text-center space-y-4",children:[(0,s.jsx)("div",{className:"w-16 h-16 mx-auto rounded-full bg-muted flex items-center justify-center",children:(0,s.jsx)(h.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-foreground",children:"No Data"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"This CSV file appears to be empty or invalid."})]})]})}):(0,s.jsxs)("div",{className:(0,d.cn)("w-full h-full flex flex-col bg-background",r),children:[(0,s.jsxs)("div",{className:"flex-shrink-0 border-b bg-muted/30 p-4 space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(h.A,{className:"h-5 w-5 text-primary"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("h3",{className:"font-medium text-foreground",children:"CSV Data"}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["- ",A.length.toLocaleString()," rows, ",L.length," columns",o&&" (filtered from ".concat(S.data.length.toLocaleString(),")")]})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(i.E,{variant:"outline",className:"text-xs",children:["Page ",k," of ",E]}),(0,s.jsxs)(g.rI,{children:[(0,s.jsx)(g.ty,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-1"}),"Columns"]})}),(0,s.jsxs)(g.SQ,{align:"end",className:"w-48",children:[(0,s.jsx)("div",{className:"px-2 py-1.5 text-sm font-medium",children:"Show/Hide Columns"}),(0,s.jsx)(g.mB,{}),S.headers.map(e=>(0,s.jsx)(g.hO,{checked:!N.has(e),onCheckedChange:()=>T(e),children:e},e))]})]})]})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(a.p,{placeholder:"Search data...",value:o,onChange:e=>{b(e.target.value),y(1)},className:"pl-9"})]})]}),(0,s.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,s.jsx)("div",{className:"w-full h-full overflow-auto scrollbar-thin scrollbar-thumb-zinc-300 dark:scrollbar-thumb-zinc-700 scrollbar-track-transparent",children:(0,s.jsxs)("table",{className:"w-full border-collapse table-fixed",style:{minWidth:"".concat(150*L.length,"px")},children:[(0,s.jsx)("thead",{className:"bg-muted/50 sticky top-0 z-10",children:(0,s.jsx)("tr",{children:L.map((e,t)=>(0,s.jsx)("th",{className:"px-4 py-3 text-left font-medium border-b border-border bg-muted/50 backdrop-blur-sm",style:{width:"150px",minWidth:"150px"},children:(0,s.jsxs)("button",{onClick:()=>F(e),className:"flex items-center gap-2 hover:text-primary transition-colors group w-full text-left",children:[(0,s.jsx)("span",{className:"truncate",children:e}),(0,s.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0",children:I(e)})]})},e))})}),(0,s.jsxs)("tbody",{children:[R.map((e,t)=>(0,s.jsx)("tr",{className:"border-b border-border hover:bg-muted/30 transition-colors",children:L.map((r,l)=>{let a=e[r];return(0,s.jsx)("td",{className:(0,d.cn)("px-4 py-3 text-sm border-r border-border last:border-r-0",O(a)),style:{width:"150px",minWidth:"150px"},children:(0,s.jsx)("div",{className:"truncate",title:String(a||""),children:P(a)})},"".concat(_+t,"-").concat(l))})},_+t)),0===R.length&&o&&(0,s.jsx)("tr",{children:(0,s.jsx)("td",{colSpan:L.length,className:"py-8 text-center text-muted-foreground",children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("p",{children:['No results found for "',o,'"']}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>b(""),children:"Clear search"})]})})})]})]})})}),E>1&&(0,s.jsx)("div",{className:"flex-shrink-0 border-t bg-muted/30 p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Showing ",_+1," to ",Math.min(_+z,A.length)," of ",A.length.toLocaleString()," rows"]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>y(e=>Math.max(1,e-1)),disabled:1===k,children:"Previous"}),(0,s.jsx)("div",{className:"flex items-center gap-1",children:Array.from({length:Math.min(5,E)},(e,t)=>{let r;return r=E<=5||k<=3?t+1:k>=E-2?E-4+t:k-2+t,(0,s.jsx)(n.$,{variant:k===r?"default":"outline",size:"sm",onClick:()=>y(r),className:"w-8 h-8 p-0",children:r},r)})}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>y(e=>Math.min(E,e+1)),disabled:k===E,children:"Next"})]})]})})]})}}}]);