(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@shikijs/langs/dist/hlsl.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const lang = Object.freeze(JSON.parse("{\"displayName\":\"HLSL\",\"name\":\"hlsl\",\"patterns\":[{\"begin\":\"/\\\\*\",\"end\":\"\\\\*/\",\"name\":\"comment.line.block.hlsl\"},{\"begin\":\"//\",\"end\":\"$\",\"name\":\"comment.line.double-slash.hlsl\"},{\"match\":\"\\\\b[0-9]+\\\\.[0-9]*([Ff])?\\\\b\",\"name\":\"constant.numeric.decimal.hlsl\"},{\"match\":\"(\\\\.([0-9]+)([Ff])?)\\\\b\",\"name\":\"constant.numeric.decimal.hlsl\"},{\"match\":\"\\\\b([0-9]+([Ff])?)\\\\b\",\"name\":\"constant.numeric.decimal.hlsl\"},{\"match\":\"\\\\b(0([Xx])\\\\h+)\\\\b\",\"name\":\"constant.numeric.hex.hlsl\"},{\"match\":\"\\\\b(false|true)\\\\b\",\"name\":\"constant.language.hlsl\"},{\"match\":\"^\\\\s*#\\\\s*(define|elif|else|endif|ifdef|ifndef|if|undef|include|line|error|pragma)\",\"name\":\"keyword.preprocessor.hlsl\"},{\"match\":\"\\\\b(break|case|continue|default|discard|do|else|for|if|return|switch|while)\\\\b\",\"name\":\"keyword.control.hlsl\"},{\"match\":\"\\\\b(compile)\\\\b\",\"name\":\"keyword.control.fx.hlsl\"},{\"match\":\"\\\\b(typedef)\\\\b\",\"name\":\"keyword.typealias.hlsl\"},{\"match\":\"\\\\b(bool([1-4](x[1-4])?)?|double([1-4](x[1-4])?)?|dword|float([1-4](x[1-4])?)?|half([1-4](x[1-4])?)?|int([1-4](x[1-4])?)?|matrix|min10float([1-4](x[1-4])?)?|min12int([1-4](x[1-4])?)?|min16float([1-4](x[1-4])?)?|min16int([1-4](x[1-4])?)?|min16uint([1-4](x[1-4])?)?|unsigned|uint([1-4](x[1-4])?)?|vector|void)\\\\b\",\"name\":\"storage.type.basic.hlsl\"},{\"match\":\"\\\\b([A-Z_a-z][0-9A-Z_a-z]*)(?=\\\\s*\\\\()\",\"name\":\"support.function.hlsl\"},{\"match\":\"(?<=:\\\\s?)(?i:BINORMAL[0-9]*|BLENDINDICES[0-9]*|BLENDWEIGHT[0-9]*|COLOR[0-9]*|NORMAL[0-9]*|POSITIONT?|PSIZE[0-9]*|TANGENT[0-9]*|TEXCOORD[0-9]*|FOG|TESSFACTOR[0-9]*|VFACE|VPOS|DEPTH[0-9]*)\\\\b\",\"name\":\"support.variable.semantic.hlsl\"},{\"match\":\"(?<=:\\\\s?)(?i:SV_(?:ClipDistance[0-9]*|CullDistance[0-9]*|Coverage|Depth|DepthGreaterEqual[0-9]*|DepthLessEqual[0-9]*|InstanceID|IsFrontFace|Position|RenderTargetArrayIndex|SampleIndex|StencilRef|Target[0-7]?|VertexID|ViewportArrayIndex))\\\\b\",\"name\":\"support.variable.semantic.sm4.hlsl\"},{\"match\":\"(?<=:\\\\s?)(?i:SV_(?:DispatchThreadID|DomainLocation|GroupID|GroupIndex|GroupThreadID|GSInstanceID|InsideTessFactor|OutputControlPointID|TessFactor))\\\\b\",\"name\":\"support.variable.semantic.sm5.hlsl\"},{\"match\":\"(?<=:\\\\s?)(?i:SV_(?:InnerCoverage|StencilRef))\\\\b\",\"name\":\"support.variable.semantic.sm5_1.hlsl\"},{\"match\":\"\\\\b(column_major|const|export|extern|globallycoherent|groupshared|inline|inout|in|out|precise|row_major|shared|static|uniform|volatile)\\\\b\",\"name\":\"storage.modifier.hlsl\"},{\"match\":\"\\\\b([su]norm)\\\\b\",\"name\":\"storage.modifier.float.hlsl\"},{\"match\":\"\\\\b(packoffset|register)\\\\b\",\"name\":\"storage.modifier.postfix.hlsl\"},{\"match\":\"\\\\b(centroid|linear|nointerpolation|noperspective|sample)\\\\b\",\"name\":\"storage.modifier.interpolation.hlsl\"},{\"match\":\"\\\\b(lineadj|line|point|triangle|triangleadj)\\\\b\",\"name\":\"storage.modifier.geometryshader.hlsl\"},{\"match\":\"\\\\b(string)\\\\b\",\"name\":\"support.type.other.hlsl\"},{\"match\":\"\\\\b(AppendStructuredBuffer|Buffer|ByteAddressBuffer|ConstantBuffer|ConsumeStructuredBuffer|InputPatch|OutputPatch)\\\\b\",\"name\":\"support.type.object.hlsl\"},{\"match\":\"\\\\b(RasterizerOrdered(?:Buffer|ByteAddressBuffer|StructuredBuffer|Texture1D|Texture1DArray|Texture2D|Texture2DArray|Texture3D))\\\\b\",\"name\":\"support.type.object.rasterizerordered.hlsl\"},{\"match\":\"\\\\b(RW(?:Buffer|ByteAddressBuffer|StructuredBuffer|Texture1D|Texture1DArray|Texture2D|Texture2DArray|Texture3D))\\\\b\",\"name\":\"support.type.object.rw.hlsl\"},{\"match\":\"\\\\b((?:Line|Point|Triangle)Stream)\\\\b\",\"name\":\"support.type.object.geometryshader.hlsl\"},{\"match\":\"\\\\b(sampler(?:|1D|2D|3D|CUBE|_state))\\\\b\",\"name\":\"support.type.sampler.legacy.hlsl\"},{\"match\":\"\\\\b(Sampler(?:|Comparison)State)\\\\b\",\"name\":\"support.type.sampler.hlsl\"},{\"match\":\"\\\\b(texture(?:2D|CUBE))\\\\b\",\"name\":\"support.type.texture.legacy.hlsl\"},{\"match\":\"\\\\b(Texture(?:1D|1DArray|2D|2DArray|2DMS|2DMSArray|3D|Cube|CubeArray))\\\\b\",\"name\":\"support.type.texture.hlsl\"},{\"match\":\"\\\\b(cbuffer|class|interface|namespace|struct|tbuffer)\\\\b\",\"name\":\"storage.type.structured.hlsl\"},{\"match\":\"\\\\b(FALSE|TRUE|NULL)\\\\b\",\"name\":\"support.constant.property-value.fx.hlsl\"},{\"match\":\"\\\\b((?:Blend|DepthStencil|Rasterizer)State)\\\\b\",\"name\":\"support.type.fx.hlsl\"},{\"match\":\"\\\\b(technique|Technique|technique10|technique11|pass)\\\\b\",\"name\":\"storage.type.fx.technique.hlsl\"},{\"match\":\"\\\\b(AlphaToCoverageEnable|BlendEnable|SrcBlend|DestBlend|BlendOp|SrcBlendAlpha|DestBlendAlpha|BlendOpAlpha|RenderTargetWriteMask)\\\\b\",\"name\":\"meta.object-literal.key.fx.blendstate.hlsl\"},{\"match\":\"\\\\b(DepthEnable|DepthWriteMask|DepthFunc|StencilEnable|StencilReadMask|StencilWriteMask|FrontFaceStencilFail|FrontFaceStencilZFail|FrontFaceStencilPass|FrontFaceStencilFunc|BackFaceStencilFail|BackFaceStencilZFail|BackFaceStencilPass|BackFaceStencilFunc)\\\\b\",\"name\":\"meta.object-literal.key.fx.depthstencilstate.hlsl\"},{\"match\":\"\\\\b(FillMode|CullMode|FrontCounterClockwise|DepthBias|DepthBiasClamp|SlopeScaleDepthBias|ZClipEnable|ScissorEnable|MultiSampleEnable|AntiAliasedLineEnable)\\\\b\",\"name\":\"meta.object-literal.key.fx.rasterizerstate.hlsl\"},{\"match\":\"\\\\b(Filter|AddressU|AddressV|AddressW|MipLODBias|MaxAnisotropy|ComparisonFunc|BorderColor|MinLOD|MaxLOD)\\\\b\",\"name\":\"meta.object-literal.key.fx.samplerstate.hlsl\"},{\"match\":\"\\\\b(?i:ZERO|ONE|SRC_COLOR|INV_SRC_COLOR|SRC_ALPHA|INV_SRC_ALPHA|DEST_ALPHA|INV_DEST_ALPHA|DEST_COLOR|INV_DEST_COLOR|SRC_ALPHA_SAT|BLEND_FACTOR|INV_BLEND_FACTOR|SRC1_COLOR|INV_SRC1_COLOR|SRC1_ALPHA|INV_SRC1_ALPHA)\\\\b\",\"name\":\"support.constant.property-value.fx.blend.hlsl\"},{\"match\":\"\\\\b(?i:ADD|SUBTRACT|REV_SUBTRACT|MIN|MAX)\\\\b\",\"name\":\"support.constant.property-value.fx.blendop.hlsl\"},{\"match\":\"\\\\b(?i:ALL)\\\\b\",\"name\":\"support.constant.property-value.fx.depthwritemask.hlsl\"},{\"match\":\"\\\\b(?i:NEVER|LESS|EQUAL|LESS_EQUAL|GREATER|NOT_EQUAL|GREATER_EQUAL|ALWAYS)\\\\b\",\"name\":\"support.constant.property-value.fx.comparisonfunc.hlsl\"},{\"match\":\"\\\\b(?i:KEEP|REPLACE|INCR_SAT|DECR_SAT|INVERT|INCR|DECR)\\\\b\",\"name\":\"support.constant.property-value.fx.stencilop.hlsl\"},{\"match\":\"\\\\b(?i:WIREFRAME|SOLID)\\\\b\",\"name\":\"support.constant.property-value.fx.fillmode.hlsl\"},{\"match\":\"\\\\b(?i:NONE|FRONT|BACK)\\\\b\",\"name\":\"support.constant.property-value.fx.cullmode.hlsl\"},{\"match\":\"\\\\b(?i:MIN_MAG_MIP_POINT|MIN_MAG_POINT_MIP_LINEAR|MIN_POINT_MAG_LINEAR_MIP_POINT|MIN_POINT_MAG_MIP_LINEAR|MIN_LINEAR_MAG_MIP_POINT|MIN_LINEAR_MAG_POINT_MIP_LINEAR|MIN_MAG_LINEAR_MIP_POINT|MIN_MAG_MIP_LINEAR|ANISOTROPIC|COMPARISON_MIN_MAG_MIP_POINT|COMPARISON_MIN_MAG_POINT_MIP_LINEAR|COMPARISON_MIN_POINT_MAG_LINEAR_MIP_POINT|COMPARISON_MIN_POINT_MAG_MIP_LINEAR|COMPARISON_MIN_LINEAR_MAG_MIP_POINT|COMPARISON_MIN_LINEAR_MAG_POINT_MIP_LINEAR|COMPARISON_MIN_MAG_LINEAR_MIP_POINT|COMPARISON_MIN_MAG_MIP_LINEAR|COMPARISON_ANISOTROPIC|TEXT_1BIT)\\\\b\",\"name\":\"support.constant.property-value.fx.filter.hlsl\"},{\"match\":\"\\\\b(?i:WRAP|MIRROR|CLAMP|BORDER|MIRROR_ONCE)\\\\b\",\"name\":\"support.constant.property-value.fx.textureaddressmode.hlsl\"},{\"begin\":\"\\\"\",\"end\":\"\\\"\",\"name\":\"string.quoted.double.hlsl\",\"patterns\":[{\"match\":\"\\\\\\\\.\",\"name\":\"constant.character.escape.hlsl\"}]}],\"scopeName\":\"source.hlsl\"}"));
const __TURBOPACK__default__export__ = [
    lang
];
}}),
"[project]/node_modules/@shikijs/langs/dist/shaderlab.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$hlsl$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/hlsl.mjs [app-client] (ecmascript)");
;
const lang = Object.freeze(JSON.parse("{\"displayName\":\"ShaderLab\",\"name\":\"shaderlab\",\"patterns\":[{\"begin\":\"//\",\"end\":\"$\",\"name\":\"comment.line.double-slash.shaderlab\"},{\"match\":\"\\\\b(?i:Range|Float|Int|Color|Vector|2D|3D|Cube|Any)\\\\b\",\"name\":\"support.type.basic.shaderlab\"},{\"include\":\"#numbers\"},{\"match\":\"\\\\b(?i:Shader|Properties|SubShader|Pass|Category)\\\\b\",\"name\":\"storage.type.structure.shaderlab\"},{\"match\":\"\\\\b(?i:Name|Tags|Fallback|CustomEditor|Cull|ZWrite|ZTest|Offset|Blend|BlendOp|ColorMask|AlphaToMask|LOD|Lighting|Stencil|Ref|ReadMask|WriteMask|Comp|CompBack|CompFront|Fail|ZFail|UsePass|GrabPass|Dependency|Material|Diffuse|Ambient|Shininess|Specular|Emission|Fog|Mode|Density|SeparateSpecular|SetTexture|Combine|ConstantColor|Matrix|AlphaTest|ColorMaterial|BindChannels|Bind)\\\\b\",\"name\":\"support.type.propertyname.shaderlab\"},{\"match\":\"\\\\b(?i:Back|Front|On|Off|[ABGR]{1,3}|AmbientAndDiffuse|Emission)\\\\b\",\"name\":\"support.constant.property-value.shaderlab\"},{\"match\":\"\\\\b(?i:Less|Greater|LEqual|GEqual|Equal|NotEqual|Always|Never)\\\\b\",\"name\":\"support.constant.property-value.comparisonfunction.shaderlab\"},{\"match\":\"\\\\b(?i:Keep|Zero|Replace|IncrSat|DecrSat|Invert|IncrWrap|DecrWrap)\\\\b\",\"name\":\"support.constant.property-value.stenciloperation.shaderlab\"},{\"match\":\"\\\\b(?i:Previous|Primary|Texture|Constant|Lerp|Double|Quad|Alpha)\\\\b\",\"name\":\"support.constant.property-value.texturecombiners.shaderlab\"},{\"match\":\"\\\\b(?i:Global|Linear|Exp2?)\\\\b\",\"name\":\"support.constant.property-value.fog.shaderlab\"},{\"match\":\"\\\\b(?i:Vertex|Normal|Tangent|TexCoord0|TexCoord1)\\\\b\",\"name\":\"support.constant.property-value.bindchannels.shaderlab\"},{\"match\":\"\\\\b(?i:Add|Sub|RevSub|Min|Max|LogicalClear|LogicalSet|LogicalCopyInverted|LogicalCopy|LogicalNoop|LogicalInvert|LogicalAnd|LogicalNand|LogicalOr|LogicalNor|LogicalXor|LogicalEquiv|LogicalAndReverse|LogicalAndInverted|LogicalOrReverse|LogicalOrInverted)\\\\b\",\"name\":\"support.constant.property-value.blendoperations.shaderlab\"},{\"match\":\"\\\\b(?i:One|Zero|SrcColor|SrcAlpha|DstColor|DstAlpha|OneMinusSrcColor|OneMinusSrcAlpha|OneMinusDstColor|OneMinusDstAlpha)\\\\b\",\"name\":\"support.constant.property-value.blendfactors.shaderlab\"},{\"match\":\"\\\\[([A-Z_a-z][0-9A-Z_a-z]*)](?!\\\\s*[A-Z_a-z][0-9A-Z_a-z]*\\\\s*\\\\(\\\")\",\"name\":\"support.variable.reference.shaderlab\"},{\"begin\":\"(\\\\[)\",\"end\":\"(])\",\"name\":\"meta.attribute.shaderlab\",\"patterns\":[{\"match\":\"\\\\G([A-Za-z]+)\\\\b\",\"name\":\"support.type.attributename.shaderlab\"},{\"include\":\"#numbers\"}]},{\"match\":\"\\\\b([A-Z_a-z][0-9A-Z_a-z]*)\\\\s*\\\\(\",\"name\":\"support.variable.declaration.shaderlab\"},{\"begin\":\"\\\\b(CG(?:PROGRAM|INCLUDE))\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.other\"}},\"end\":\"\\\\b(ENDCG)\\\\b\",\"endCaptures\":{\"1\":{\"name\":\"keyword.other\"}},\"name\":\"meta.cgblock\",\"patterns\":[{\"include\":\"#hlsl-embedded\"}]},{\"begin\":\"\\\\b(HLSL(?:PROGRAM|INCLUDE))\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.other\"}},\"end\":\"\\\\b(ENDHLSL)\\\\b\",\"endCaptures\":{\"1\":{\"name\":\"keyword.other\"}},\"name\":\"meta.hlslblock\",\"patterns\":[{\"include\":\"#hlsl-embedded\"}]},{\"begin\":\"\\\"\",\"end\":\"\\\"\",\"name\":\"string.quoted.double.shaderlab\"}],\"repository\":{\"hlsl-embedded\":{\"patterns\":[{\"include\":\"source.hlsl\"},{\"match\":\"\\\\b(fixed([1-4](x[1-4])?)?)\\\\b\",\"name\":\"storage.type.basic.shaderlab\"},{\"match\":\"\\\\b(UNITY_MATRIX_MVP?|UNITY_MATRIX_M|UNITY_MATRIX_V|UNITY_MATRIX_P|UNITY_MATRIX_VP|UNITY_MATRIX_T_MV|UNITY_MATRIX_I_V|UNITY_MATRIX_IT_MV|_Object2World|_World2Object|unity_ObjectToWorld|unity_WorldToObject)\\\\b\",\"name\":\"support.variable.transformations.shaderlab\"},{\"match\":\"\\\\b(_WorldSpaceCameraPos|_ProjectionParams|_ScreenParams|_ZBufferParams|unity_OrthoParams|unity_CameraProjection|unity_CameraInvProjection|unity_CameraWorldClipPlanes)\\\\b\",\"name\":\"support.variable.camera.shaderlab\"},{\"match\":\"\\\\b((?:_|_Sin|_Cos|unity_Delta)Time)\\\\b\",\"name\":\"support.variable.time.shaderlab\"},{\"match\":\"\\\\b(_LightColor0|_WorldSpaceLightPos0|_LightMatrix0|unity_4LightPosX0|unity_4LightPosY0|unity_4LightPosZ0|unity_4LightAtten0|unity_LightColor|_LightColor|unity_LightPosition|unity_LightAtten|unity_SpotDirection)\\\\b\",\"name\":\"support.variable.lighting.shaderlab\"},{\"match\":\"\\\\b(unity_AmbientSky|unity_AmbientEquator|unity_AmbientGround|UNITY_LIGHTMODEL_AMBIENT|unity_FogColor|unity_FogParams)\\\\b\",\"name\":\"support.variable.fog.shaderlab\"},{\"match\":\"\\\\b(unity_LODFade)\\\\b\",\"name\":\"support.variable.various.shaderlab\"},{\"match\":\"\\\\b(SHADER_API_(?:D3D9|D3D11|GLCORE|OPENGL|GLES3??|METAL|D3D11_9X|PSSL|XBOXONE|PSP2|WIIU|MOBILE|GLSL))\\\\b\",\"name\":\"support.variable.preprocessor.targetplatform.shaderlab\"},{\"match\":\"\\\\b(SHADER_TARGET)\\\\b\",\"name\":\"support.variable.preprocessor.targetmodel.shaderlab\"},{\"match\":\"\\\\b(UNITY_VERSION)\\\\b\",\"name\":\"support.variable.preprocessor.unityversion.shaderlab\"},{\"match\":\"\\\\b(UNITY_(?:BRANCH|FLATTEN|NO_SCREENSPACE_SHADOWS|NO_LINEAR_COLORSPACE|NO_RGBM|NO_DXT5nm|FRAMEBUFFER_FETCH_AVAILABLE|USE_RGBA_FOR_POINT_SHADOWS|ATTEN_CHANNEL|HALF_TEXEL_OFFSET|UV_STARTS_AT_TOP|MIGHT_NOT_HAVE_DEPTH_Texture|NEAR_CLIP_VALUE|VPOS_TYPE|CAN_COMPILE_TESSELLATION|COMPILER_HLSL|COMPILER_HLSL2GLSL|COMPILER_CG|REVERSED_Z))\\\\b\",\"name\":\"support.variable.preprocessor.platformdifference.shaderlab\"},{\"match\":\"\\\\b(UNITY_PASS_(?:FORWARDBASE|FORWARDADD|DEFERRED|SHADOWCASTER|PREPASSBASE|PREPASSFINAL))\\\\b\",\"name\":\"support.variable.preprocessor.texture2D.shaderlab\"},{\"match\":\"\\\\b(appdata_(?:base|tan|full|img))\\\\b\",\"name\":\"support.class.structures.shaderlab\"},{\"match\":\"\\\\b(SurfaceOutputStandardSpecular|SurfaceOutputStandard|SurfaceOutput|Input)\\\\b\",\"name\":\"support.class.surface.shaderlab\"}]},\"numbers\":{\"patterns\":[{\"match\":\"\\\\b([0-9]+\\\\.?[0-9]*)\\\\b\",\"name\":\"constant.numeric.shaderlab\"}]}},\"scopeName\":\"source.shaderlab\",\"embeddedLangs\":[\"hlsl\"],\"aliases\":[\"shader\"]}"));
const __TURBOPACK__default__export__ = [
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$hlsl$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    lang
];
}}),
}]);

//# sourceMappingURL=node_modules_%40shikijs_langs_dist_db21ec44._.js.map