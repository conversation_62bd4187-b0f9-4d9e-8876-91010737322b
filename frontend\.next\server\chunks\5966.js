"use strict";exports.id=5966,exports.ids=[5966],exports.modules={15079:(e,t,a)=>{a.d(t,{bq:()=>u,eb:()=>g,gC:()=>m,l6:()=>c,yv:()=>d});var s=a(60687);a(43210);var r=a(22670),o=a(78272),n=a(13964),l=a(3589),i=a(4780);function c({...e}){return(0,s.jsx)(r.bL,{"data-slot":"select",...e})}function d({...e}){return(0,s.jsx)(r.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:a,...n}){return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":t,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[a,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(o.A,{className:"size-4 opacity-50"})})]})}function m({className:e,children:t,position:a="popper",...o}){return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...o,children:[(0,s.jsx)(h,{}),(0,s.jsx)(r.LM,{className:(0,i.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(p,{})]})})}function g({className:e,children:t,...a}){return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...a,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:t})]})}function h({className:e,...t}){return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(l.A,{className:"size-4"})})}function p({className:e,...t}){return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(o.A,{className:"size-4"})})}},25098:(e,t,a)=>{a.d(t,{_:()=>r,n:()=>s});let s={sb_shell_tool:{enabled:!0,description:"Execute shell commands in tmux sessions for terminal operations, CLI tools, and system management",icon:"\uD83D\uDCBB",color:"bg-slate-100 dark:bg-slate-800"},sb_files_tool:{enabled:!0,description:"Create, read, update, and delete files in the workspace with comprehensive file management",icon:"\uD83D\uDCC1",color:"bg-blue-100 dark:bg-blue-800/50"},sb_browser_tool:{enabled:!0,description:"Browser automation for web navigation, clicking, form filling, and page interaction",icon:"\uD83C\uDF10",color:"bg-indigo-100 dark:bg-indigo-800/50"},sb_deploy_tool:{enabled:!0,description:"Deploy applications and services with automated deployment capabilities",icon:"\uD83D\uDE80",color:"bg-green-100 dark:bg-green-800/50"},sb_expose_tool:{enabled:!0,description:"Expose services and manage ports for application accessibility",icon:"\uD83D\uDD0C",color:"bg-orange-100 dark:bg-orange-800/20"},web_search_tool:{enabled:!0,description:"Search the web using Tavily API and scrape webpages with Firecrawl for research",icon:"\uD83D\uDD0D",color:"bg-yellow-100 dark:bg-yellow-800/50"},sb_vision_tool:{enabled:!0,description:"Vision and image processing capabilities for visual content analysis",icon:"\uD83D\uDC41️",color:"bg-pink-100 dark:bg-pink-800/50"},data_providers_tool:{enabled:!0,description:"Access to data providers and external APIs (requires RapidAPI key)",icon:"\uD83D\uDD17",color:"bg-cyan-100 dark:bg-cyan-800/50"}},r=e=>({sb_shell_tool:"Terminal",sb_files_tool:"File Manager",sb_browser_tool:"Browser Automation",sb_deploy_tool:"Deploy Tool",sb_expose_tool:"Port Exposure",web_search_tool:"Web Search",sb_vision_tool:"Image Processing",data_providers_tool:"Data Providers"})[e]||e.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase())},39665:(e,t,a)=>{a.d(t,{fJ:()=>g,gc:()=>w,_F:()=>m,h3:()=>p,gX:()=>x,XK:()=>y,jz:()=>b,Ae:()=>f});var s=a(22372),r=a(8693),o=a(52581),n=a(80116),l=a(61739);a(43210);var i=a(16189);let c=["\uD83E\uDD16","\uD83E\uDDE0","\uD83D\uDCA1","\uD83D\uDE80","⚡","\uD83D\uDD2E","\uD83C\uDFAF","\uD83D\uDEE1️","\uD83D\uDD27","\uD83C\uDFA8","\uD83D\uDCCA","\uD83D\uDCC8","\uD83D\uDD0D","\uD83C\uDF1F","✨","\uD83C\uDFAA","\uD83C\uDFAD","\uD83C\uDFA8","\uD83C\uDFAF","\uD83C\uDFB2","\uD83E\uDDE9","\uD83D\uDD2C","\uD83D\uDD2D","\uD83D\uDDFA️","\uD83E\uDDED","⚙️","\uD83D\uDEE0️","\uD83D\uDD29","\uD83D\uDD17","\uD83D\uDCE1","\uD83C\uDF10","\uD83D\uDCBB","\uD83D\uDDA5️","\uD83D\uDCF1","⌨️","\uD83D\uDDB1️","\uD83D\uDCBE","\uD83D\uDCBF","\uD83D\uDCC0","\uD83D\uDDC4️","\uD83D\uDCC2","\uD83D\uDCC1","\uD83D\uDDC2️","\uD83D\uDCCB","\uD83D\uDCCC","\uD83D\uDCCD","\uD83D\uDCCE","\uD83D\uDD87️","\uD83D\uDCCF","\uD83D\uDCD0","✂️","\uD83D\uDD8A️","\uD83D\uDD8B️","✒️","\uD83D\uDD8C️","\uD83D\uDD8D️","\uD83D\uDCDD","✏️","\uD83D\uDD10","\uD83D\uDD12","\uD83D\uDD13","\uD83D\uDD0F","\uD83D\uDD11","\uD83D\uDDDD️","\uD83D\uDD28","⛏️","⚒️","\uD83D\uDEE1️","\uD83C\uDFF9","\uD83C\uDFAF","\uD83C\uDFB0","\uD83C\uDFAE","\uD83D\uDD79️","\uD83C\uDFB2","♠️","♥️","♦️","♣️","\uD83C\uDCCF","\uD83C\uDC04","\uD83C\uDFB4","\uD83C\uDFAD","\uD83D\uDDBC️","\uD83C\uDFA8","\uD83E\uDDF5","\uD83E\uDDF6","\uD83C\uDFB8","\uD83C\uDFB9","\uD83C\uDFBA","\uD83C\uDFBB","\uD83E\uDD41","\uD83C\uDFAC","\uD83C\uDFA4","\uD83C\uDFA7","\uD83C\uDFBC","\uD83C\uDFB5","\uD83C\uDFB6","\uD83C\uDF99️","\uD83C\uDF9A️","\uD83C\uDF9B️","\uD83D\uDCFB","\uD83D\uDCFA","\uD83D\uDCF7","\uD83D\uDCF9","\uD83D\uDCFD️","\uD83C\uDF9E️","\uD83D\uDCDE","☎️","\uD83D\uDCDF","\uD83D\uDCE0","\uD83D\uDC8E","\uD83D\uDC8D","\uD83C\uDFC6","\uD83E\uDD47","\uD83E\uDD48","\uD83E\uDD49","\uD83C\uDFC5","\uD83C\uDF96️","\uD83C\uDFF5️","\uD83C\uDF97️","\uD83C\uDFAB","\uD83C\uDF9F️","\uD83C\uDFAA","\uD83C\uDFAD","\uD83C\uDFA8","\uD83C\uDFAC","\uD83C\uDFA4","\uD83C\uDFA7","\uD83C\uDFBC","\uD83C\uDFB9","\uD83E\uDDBE","\uD83E\uDDBF","\uD83E\uDDB4","\uD83D\uDC41️","\uD83E\uDDE0","\uD83E\uDEC0","\uD83E\uDEC1","\uD83E\uDDB7","\uD83E\uDDB4","\uD83D\uDC40"],d=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FECA57","#FF9FF3","#54A0FF","#48DBFB","#1DD1A1","#00D2D3","#5F27CD","#341F97","#EE5A24","#F368E0","#FF6348","#7BED9F","#70A1FF","#5352ED","#3742FA","#2ED573","#1E90FF","#FF1744","#D500F9","#00E676","#FF6D00","#00B8D4","#6C5CE7","#A29BFE","#FD79A8","#FDCB6E","#6C5CE7","#00B894","#00CEC9","#0984E3","#6C5CE7","#A29BFE","#74B9FF","#81ECEC","#55A3FF","#FD79A8","#FDCB6E","#FF7675","#E17055","#FAB1A0","#74B9FF","#A29BFE","#DFE6E9","#B2BEC3","#636E72"];var u=a(25098);let m=(e={})=>(0,s.GQ)(n._.list(e),()=>(0,l.vt)(e),{staleTime:3e5,gcTime:6e5})(),g=e=>(0,s.GQ)(n._.detail(e),()=>(0,l.WU)(e),{enabled:!!e,staleTime:3e5,gcTime:6e5})(),h=()=>{let e=(0,r.jE)();return(0,s.Lx)(l.Sn,{onSuccess:t=>{e.invalidateQueries({queryKey:n._.lists()}),e.setQueryData(n._.detail(t.agent_id),t),o.oR.success("Agent created successfully")}})()},p=()=>{let e=(0,i.useRouter)(),t=h();return(0,s.Lx)(async e=>{let{avatar:a,avatar_color:s}={avatar:function(){let e=Math.floor(Math.random()*c.length);return c[e]}(),avatar_color:function(){let e=Math.floor(Math.random()*d.length);return d[e]}()},r={name:"New Agent",description:"",system_prompt:"You are a helpful assistant. Provide clear, accurate, and helpful responses to user queries.",avatar:a,avatar_color:s,configured_mcps:[],agentpress_tools:Object.fromEntries(Object.entries(u.n).map(([e,t])=>[e,{enabled:t.enabled,description:t.description}])),is_default:!1};return await t.mutateAsync(r)},{onSuccess:t=>{e.push(`/agents/config/${t.agent_id}`)},onError:e=>{console.error("Error creating agent:",e),o.oR.error("Failed to create agent. Please try again.")}})()},f=()=>{let e=(0,r.jE)();return(0,s.Lx)(({agentId:e,...t})=>(0,l.x4)(e,t),{onSuccess:(t,a)=>{e.setQueryData(n._.detail(a.agentId),t),e.invalidateQueries({queryKey:n._.lists()}),(void 0!==a.configured_mcps||void 0!==a.custom_mcps)&&(e.invalidateQueries({queryKey:["agent-tools",a.agentId]}),e.invalidateQueries({queryKey:["pipedream-tools",a.agentId]}),e.invalidateQueries({queryKey:["custom-mcp-tools",a.agentId]}),e.invalidateQueries({queryKey:["pipedream","available-tools"]}))}})()},x=()=>{let e=(0,r.jE)();return(0,s.Lx)(l.RB,{onSuccess:(t,a)=>{e.removeQueries({queryKey:n._.detail(a)}),e.invalidateQueries({queryKey:n._.lists()}),o.oR.success("Agent deleted successfully")}})()},y=()=>{let e=(0,r.jE)();return{optimisticallyUpdateAgent:(t,a)=>{e.setQueryData(n._.detail(t),e=>e?{...e,...a}:e)},revertOptimisticUpdate:t=>{e.invalidateQueries({queryKey:n._.detail(t)})}}},b=e=>(0,s.GQ)(n._.threadAgent(e),()=>(0,l.qU)(e),{enabled:!!e,staleTime:3e5,gcTime:6e5})(),w=e=>(0,s.GQ)(n._.builderChatHistory(e),()=>(0,l.Lm)(e),{enabled:!!e,retry:1})()},52556:(e,t,a)=>{a.d(t,{R:()=>_});var s=a(60687),r=a(43210),o=a(29523),n=a(89667),l=a(80013),i=a(54987),c=a(44493),d=a(35950),u=a(63503),m=a(15079),g=a(14719),h=a(96474),p=a(41862),f=a(70334),x=a(78122),y=a(45583),b=a(52581),w=a(4780),j=a(55701),v=a(25006);let _=({app:e,open:t,onOpenChange:a,onComplete:_,mode:E="full"})=>{let[N,C]=(0,r.useState)("profile"),[k,F]=(0,r.useState)(""),[A,T]=(0,r.useState)(!1),[S,P]=(0,r.useState)(""),[$,z]=(0,r.useState)(new Set),[D,B]=(0,r.useState)([]),[q,Q]=(0,r.useState)(!1),[R,U]=(0,r.useState)(!1),[K,M]=(0,r.useState)(!1),{data:I,refetch:L}=(0,j.h4)({app_slug:e.name_slug}),G=(0,j.O4)(),H=(0,j.wG)(),O=(0,r.useMemo)(()=>I?.filter(e=>e.is_connected)||[],[I]),Y=(0,r.useMemo)(()=>I?.find(e=>e.profile_id===k),[I,k]);(0,r.useEffect)(()=>{t&&(C("profile"),F(""),T(!1),P(""),z(new Set),B([]))},[t]),(0,r.useEffect)(()=>{t&&1===O.length&&!k&&F(O[0].profile_id)},[t,O,k]);let W=(0,r.useCallback)(async()=>{if(!S.trim())return void b.oR.error("Please enter a profile name");U(!0);try{let t={profile_name:S.trim(),app_slug:e.name_slug,app_name:e.name,is_default:0===O.length},s=await G.mutateAsync(t);await H.mutateAsync({profileId:s.profile_id,app:e.name_slug}),await L(),F(s.profile_id),T(!1),P(""),b.oR.success("Profile created and connected successfully!"),"profile-only"===E?(_(s.profile_id,[],e.name,e.name_slug),a(!1)):J()}catch(e){console.error("Error creating profile:",e)}finally{U(!1)}},[S,e.name_slug,e.name,O.length,G,H,L,E,_,a]),J=(0,r.useCallback)(async()=>{if(k&&Y){Q(!0),C("tools");try{let t=(await v.A.discoverMCPServers(Y.external_user_id,e.name_slug)).find(t=>t.app_slug===e.name_slug);t?.available_tools&&(B(t.available_tools),z(new Set(t.available_tools.map(e=>e.name))))}catch(e){console.error("Error fetching tools:",e),b.oR.error("Failed to load tools")}finally{Q(!1)}}},[k,Y,e.name_slug]),V=(0,r.useCallback)(async()=>{if(!k||0===$.size)return void b.oR.error("Please select at least one tool");M(!0);try{_(k,Array.from($),e.name,e.name_slug),a(!1)}catch(e){console.error("Error completing connection:",e)}finally{M(!1)}},[k,$,_,e.name,e.name_slug,a]),X=(0,r.useCallback)(async()=>{if(!k)return void b.oR.error("Please select a profile");M(!0);try{_(k,[],e.name,e.name_slug),a(!1)}catch(e){console.error("Error completing connection:",e)}finally{M(!1)}},[k,_,e.name,e.name_slug,a]),Z=(0,r.useCallback)(e=>{z(t=>{let a=new Set(t);return a.has(e)?a.delete(e):a.add(e),a})},[]),ee=(0,r.useCallback)(e=>{P(e.target.value)},[]),et=(0,r.useCallback)(e=>{"Enter"===e.key&&W()},[W]),ea=(0,r.useMemo)(()=>(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold",children:["Connect to ",e.name]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"profile-only"===E?"Create a new profile to connect your account":O.length>0?"Select a profile or create a new one to connect different accounts":"Create your first profile to get started"})]}),"profile-only"!==E&&O.length>0&&!A&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.Label,{htmlFor:"profile-select",children:"Select Profile"}),(0,s.jsxs)(m.l6,{value:k,onValueChange:F,children:[(0,s.jsx)(m.bq,{children:(0,s.jsx)(m.yv,{placeholder:"Choose a profile",children:Y&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{children:Y.profile_name}),(0,s.jsx)(g.A,{className:"h-3 w-3 text-green-500"})]})})}),(0,s.jsx)(m.gC,{children:O.map(e=>(0,s.jsx)(m.eb,{value:e.profile_id,children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{children:e.profile_name}),(0,s.jsx)("div",{className:"h-2 w-2 bg-green-500 rounded-full"})]})},e.profile_id))})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(d.w,{className:"flex-1"}),(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"OR"}),(0,s.jsx)(d.w,{className:"flex-1"})]}),(0,s.jsxs)(o.$,{variant:"outline",onClick:()=>T(!0),className:"w-full",children:[(0,s.jsx)(h.A,{className:"h-4 w-4"}),"Create New Profile"]})]}),("profile-only"===E||0===O.length||A)&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.Label,{htmlFor:"profile-name",children:"Profile Name"}),(0,s.jsx)(n.p,{id:"profile-name",placeholder:"e.g., Personal Account, Work Account",value:S,onChange:ee,onKeyDown:et,autoFocus:"profile-only"===E||A})]}),(0,s.jsxs)("div",{className:"flex gap-3",children:["profile-only"!==E&&A&&(0,s.jsx)(o.$,{variant:"outline",onClick:()=>{T(!1),P("")},className:"flex-1",children:"Cancel"}),(0,s.jsx)(o.$,{onClick:W,disabled:!S.trim()||R,className:"profile-only"===E?"w-full":"flex-1",children:R?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Creating..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(h.A,{className:"h-4 w-4"}),"Create & Connect"]})})]})]}),"profile-only"!==E&&k&&!A&&(0,s.jsx)("div",{className:"pt-4 border-t",children:(0,s.jsx)(o.$,{onClick:J,disabled:!k||K,className:"w-full",children:K?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Connecting..."]}):(0,s.jsxs)(s.Fragment,{children:["Continue to Tools",(0,s.jsx)(f.A,{className:"h-4 w-4"})]})})})]}),[e.name,O,A,k,Y,S,R,ee,et,W,J,E,X,K]),es=(0,r.useMemo)(()=>(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{children:(0,s.jsx)("div",{className:"flex items-center gap-2",children:(0,s.jsx)(o.$,{variant:"link",size:"sm",onClick:()=>C("profile"),className:"mb-4 p-0 h-auto font-normal text-muted-foreground hover:text-foreground",children:"← Back to Profile"})})}),q?(0,s.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(p.A,{className:"h-5 w-5 animate-spin"}),(0,s.jsx)("span",{className:"text-sm",children:"Loading tools..."})]})}):0===D.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"text-4xl mb-3",children:"\uD83D\uDD27"}),(0,s.jsx)("h4",{className:"font-medium mb-2",children:"No tools available"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"This app doesn't have any tools available yet."}),(0,s.jsxs)(o.$,{variant:"outline",onClick:J,children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Refresh"]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"text-sm font-medium",children:[$.size," of ",D.length," tools selected"]}),(0,s.jsx)(o.$,{variant:"ghost",size:"sm",onClick:()=>{$.size===D.length?z(new Set):z(new Set(D.map(e=>e.name)))},children:$.size===D.length?"Deselect All":"Select All"})]}),(0,s.jsx)("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:D.map(e=>{let t=$.has(e.name);return(0,s.jsx)(c.Zp,{className:(0,w.cn)("p-0 border cursor-pointer transition-colors",t?"bg-muted/50":"hover:bg-muted/20"),onClick:()=>Z(e.name),children:(0,s.jsx)(c.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,s.jsx)("div",{className:"flex-1 min-w-0",children:(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,s.jsx)("h4",{className:"font-medium text-sm",children:e.name}),t&&(0,s.jsx)(g.A,{className:"h-4 w-4 text-green-500"})]})}),(0,s.jsx)(i.d,{checked:t,onCheckedChange:()=>Z(e.name),onClick:e=>e.stopPropagation()})]})})},e.name)})})]}),(0,s.jsx)("div",{className:"pt-4 border-t",children:(0,s.jsx)(o.$,{onClick:V,disabled:0===$.size||K,className:"w-full",children:K?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Connecting..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(y.A,{className:"h-4 w-4"}),"Connect with ",$.size," Tool",1!==$.size?"s":""]})})})]})]}),[e.name,q,D,$,Z,V,K,J]);return(0,s.jsx)(u.lG,{open:t,onOpenChange:a,children:(0,s.jsxs)(u.Cf,{className:"sm:max-w-lg max-h-[90vh] overflow-y-auto",children:[(0,s.jsxs)(u.c7,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"h-10 w-10 flex-shrink-0 rounded-lg bg-muted flex items-center justify-center",children:e.img_src?(0,s.jsx)("img",{src:e.img_src,alt:e.name,className:"h-6 w-6 object-cover rounded"}):(0,s.jsx)("span",{className:"text-sm font-semibold",children:e.name.charAt(0)})}),(0,s.jsxs)("div",{children:[(0,s.jsx)(u.L3,{className:"text-left",children:e.name}),(0,s.jsx)(u.rr,{className:"text-left",children:"profile-only"===E?"Connect your account to continue with installation":e.description})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:(0,w.cn)("h-2 w-2 rounded-full","profile"===N?"bg-primary":"bg-muted")}),"full"===E&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"h-px bg-muted flex-1"}),(0,s.jsx)("div",{className:(0,w.cn)("h-2 w-2 rounded-full","tools"===N?"bg-primary":"bg-muted")})]})]})]}),(0,s.jsx)("div",{className:"mt-6",children:"profile"===N?ea:es})]})})}},54987:(e,t,a)=>{a.d(t,{d:()=>n});var s=a(60687);a(43210);var r=a(90270),o=a(4780);function n({className:e,...t}){return(0,s.jsx)(r.bL,{"data-slot":"switch",className:(0,o.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(r.zi,{"data-slot":"switch-thumb",className:(0,o.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},55701:(e,t,a)=>{a.d(t,{Lh:()=>u,O4:()=>d,PA:()=>m,h4:()=>c,wG:()=>g});var s=a(43612),r=a(8693),o=a(54050),n=a(25006),l=a(90015),i=a(52581);let c=e=>(0,s.I)({queryKey:l.M.profiles.list(e),queryFn:()=>n.A.getProfiles(e),staleTime:3e5}),d=()=>{let e=(0,r.jE)();return(0,o.n)({mutationFn:e=>n.A.createProfile(e),onSuccess:t=>{e.invalidateQueries({queryKey:l.M.profiles.all()}),i.oR.success(`Profile "${t.profile_name}" created successfully`)},onError:e=>{i.oR.error(e.message||"Failed to create profile")}})},u=()=>{let e=(0,r.jE)();return(0,o.n)({mutationFn:({profileId:e,request:t})=>n.A.updateProfile(e,t),onSuccess:t=>{e.invalidateQueries({queryKey:l.M.profiles.all()}),e.invalidateQueries({queryKey:l.M.profiles.detail(t.profile_id)}),i.oR.success("Profile updated successfully")},onError:e=>{i.oR.error(e.message||"Failed to update profile")}})},m=()=>{let e=(0,r.jE)();return(0,o.n)({mutationFn:e=>n.A.deleteProfile(e),onSuccess:(t,a)=>{e.invalidateQueries({queryKey:l.M.profiles.all()}),e.removeQueries({queryKey:l.M.profiles.detail(a)}),i.oR.success("Profile deleted successfully")},onError:e=>{i.oR.error(e.message||"Failed to delete profile")}})},g=()=>{let e=(0,r.jE)();return(0,o.n)({mutationFn:({profileId:e,app:t})=>n.A.connectProfile(e,t),onSuccess:t=>{if(e.invalidateQueries({queryKey:l.M.profiles.all()}),e.invalidateQueries({queryKey:l.M.profiles.detail(t.profile_id)}),e.invalidateQueries({queryKey:l.M.profiles.connections(t.profile_id)}),t.link){let a=window.open(t.link,"_blank","width=600,height=700");if(a){let s=setInterval(()=>{a.closed&&(clearInterval(s),e.invalidateQueries({queryKey:l.M.profiles.all()}),e.invalidateQueries({queryKey:l.M.profiles.detail(t.profile_id)}),e.invalidateQueries({queryKey:l.M.profiles.connections(t.profile_id)}),i.oR.success("Connection process completed"))},1e3);setTimeout(()=>{clearInterval(s)},3e5)}else i.oR.error("Failed to open connection window. Please check your popup blocker.")}},onError:e=>{i.oR.error(e.message||"Failed to connect profile")}})}},61739:(e,t,a)=>{a.d(t,{Lm:()=>m,RB:()=>d,Sn:()=>i,WU:()=>l,qU:()=>u,vt:()=>n,x4:()=>c});var s=a(79481),r=a(15945);let o="http://localhost:8000/api",n=async(e={})=>{try{if(!await (0,r.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let t=(0,s.U)(),{data:{session:a}}=await t.auth.getSession();if(!a)throw Error("You must be logged in to get agents");let n=new URLSearchParams;e.page&&n.append("page",e.page.toString()),e.limit&&n.append("limit",e.limit.toString()),e.search&&n.append("search",e.search),e.sort_by&&n.append("sort_by",e.sort_by),e.sort_order&&n.append("sort_order",e.sort_order),void 0!==e.has_default&&n.append("has_default",e.has_default.toString()),void 0!==e.has_mcp_tools&&n.append("has_mcp_tools",e.has_mcp_tools.toString()),void 0!==e.has_agentpress_tools&&n.append("has_agentpress_tools",e.has_agentpress_tools.toString()),e.tools&&n.append("tools",e.tools);let l=`${o}/agents${n.toString()?`?${n.toString()}`:""}`,i=await fetch(l,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a.access_token}`}});if(!i.ok){let e=await i.json().catch(()=>({message:"Unknown error"}));throw Error(e.message||`HTTP ${i.status}: ${i.statusText}`)}let c=await i.json();return console.log("[API] Fetched agents:",c.agents?.length||0,"total:",c.pagination?.total||0),c}catch(e){throw console.error("Error fetching agents:",e),e}},l=async e=>{try{if(!await (0,r.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let t=(0,s.U)(),{data:{session:a}}=await t.auth.getSession();if(!a)throw Error("You must be logged in to get agent details");let n=await fetch(`${o}/agents/${e}`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a.access_token}`}});if(!n.ok){let e=await n.json().catch(()=>({message:"Unknown error"}));throw Error(e.message||`HTTP ${n.status}: ${n.statusText}`)}let l=await n.json();return console.log("[API] Fetched agent:",l.agent_id),l}catch(e){throw console.error("Error fetching agent:",e),e}},i=async e=>{try{if(!await (0,r.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let t=(0,s.U)(),{data:{session:a}}=await t.auth.getSession();if(!a)throw Error("You must be logged in to create an agent");let n=await fetch(`${o}/agents`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a.access_token}`},body:JSON.stringify(e)});if(!n.ok){let e=await n.json().catch(()=>({message:"Unknown error"}));throw Error(e.message||`HTTP ${n.status}: ${n.statusText}`)}let l=await n.json();return console.log("[API] Created agent:",l.agent_id),l}catch(e){throw console.error("Error creating agent:",e),e}},c=async(e,t)=>{try{if(!await (0,r.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let a=(0,s.U)(),{data:{session:n}}=await a.auth.getSession();if(!n)throw Error("You must be logged in to update an agent");let l=await fetch(`${o}/agents/${e}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${n.access_token}`},body:JSON.stringify(t)});if(!l.ok){let e=await l.json().catch(()=>({message:"Unknown error"}));throw Error(e.message||`HTTP ${l.status}: {response.statusText}`)}let i=await l.json();return console.log("[API] Updated agent:",i.agent_id),i}catch(e){throw console.error("Error updating agent:",e),e}},d=async e=>{try{if(!await (0,r.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let t=(0,s.U)(),{data:{session:a}}=await t.auth.getSession();if(!a)throw Error("You must be logged in to delete an agent");let n=await fetch(`${o}/agents/${e}`,{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a.access_token}`}});if(!n.ok){let e=await n.json().catch(()=>({message:"Unknown error"}));throw Error(e.message||`HTTP ${n.status}: ${n.statusText}`)}console.log("[API] Deleted agent:",e)}catch(e){throw console.error("Error deleting agent:",e),e}},u=async e=>{try{if(!await (0,r.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let t=(0,s.U)(),{data:{session:a}}=await t.auth.getSession();if(!a)throw Error("You must be logged in to get thread agent");let n=await fetch(`${o}/thread/${e}/agent`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a.access_token}`}});if(!n.ok){let e=await n.json().catch(()=>({message:"Unknown error"}));throw Error(e.message||`HTTP ${n.status}: ${n.statusText}`)}let l=await n.json();return console.log("[API] Fetched thread agent:",e),l}catch(e){throw console.error("Error fetching thread agent:",e),e}},m=async e=>{try{if(!await (0,r.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let t=(0,s.U)(),{data:{session:a}}=await t.auth.getSession();if(!a)throw Error("You must be logged in to get agent builder chat history");let n=await fetch(`${o}/agents/${e}/builder-chat-history`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a.access_token}`}});if(!n.ok){let e=await n.json().catch(()=>({message:"Unknown error"}));throw Error(e.message||`HTTP ${n.status}: ${n.statusText}`)}let l=await n.json();return console.log("[API] Fetched agent builder chat history:",e,l.messages.length),l}catch(e){throw console.error("Error fetching agent builder chat history:",e),e}}},80116:(e,t,a)=>{a.d(t,{_:()=>o});var s=a(22372);let r=["agents"],o=(0,s.DY)({all:r,lists:()=>[...r,"list"],list:e=>[...r,"list",e],details:()=>[...r,"detail"],detail:e=>[...r,"detail",e],threadAgents:()=>[...r,"thread-agent"],threadAgent:e=>[...r,"thread-agent",e],builderChatHistory:e=>[...r,"builderChatHistory",e]})}};