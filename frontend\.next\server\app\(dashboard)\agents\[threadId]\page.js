(()=>{var e={};e.id=6301,e.ids=[6301],e.modules={1256:(e,s,r)=>{Promise.resolve().then(r.bind(r,44607))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39032:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});var t=r(37413);function a({children:e}){return(0,t.jsx)(t.Fragment,{children:e})}},44607:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\agents\\\\[threadId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\[threadId]\\page.tsx","default")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70400:(e,s,r)=>{"use strict";r.r(s),r.d(s,{"60120624b62a67d0046abca062aae687cbb5ebc44a":()=>t.vI,"602190608cb4aada86562e5e5997e6bb44fbe95e4e":()=>t.$w,"60836a64bf90333e8dead87703a0c5a32e95fa0f8f":()=>t.gj});var t=r(67834)},73578:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>o,routeModule:()=>u,tree:()=>l});var t=r(65239),a=r(48088),n=r(31369),d=r(30893),i={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>d[e]);r.d(s,i);let l={children:["",{children:["(dashboard)",{children:["agents",{children:["[threadId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,44607)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\[threadId]\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,39032)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\[threadId]\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,90170)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,33532)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,88524))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,93595)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,88524))).default(e)],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\[threadId]\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/agents/[threadId]/page",pathname:"/agents/[threadId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83614:(e,s,r)=>{"use strict";r.d(s,{y:()=>i});var t=r(60687),a=r(43210),n=r.n(a),d=r(85726);function i({isSidePanelOpen:e=!1,showHeader:s=!0,messageCount:r=3}){return(0,t.jsxs)("div",{className:"flex h-screen",children:[(0,t.jsxs)("div",{className:"flex flex-col flex-1 overflow-hidden transition-all duration-200 ease-in-out",children:[s&&(0,t.jsx)("div",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,t.jsxs)("div",{className:"flex h-14 items-center gap-4 px-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.Skeleton,{className:"h-6 w-6 rounded-full"}),(0,t.jsx)(d.Skeleton,{className:"h-5 w-40"})]})}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.Skeleton,{className:"h-8 w-8 rounded-full"}),(0,t.jsx)(d.Skeleton,{className:"h-8 w-8 rounded-full"})]})]})}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto px-6 py-4 pb-[5.5rem]",children:(0,t.jsxs)("div",{className:"mx-auto max-w-3xl space-y-6",children:[Array.from({length:r}).map((e,s)=>(0,t.jsx)(n().Fragment,{children:s%2==0?(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsx)("div",{className:"max-w-[85%] rounded-lg bg-primary/10 px-4 py-3",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(d.Skeleton,{className:"h-4 w-48"}),(0,t.jsx)(d.Skeleton,{className:"h-4 w-32"})]})})}):(0,t.jsx)("div",{children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(d.Skeleton,{className:"flex-shrink-0 w-5 h-5 mt-2 rounded-full"}),(0,t.jsx)("div",{className:"flex-1 space-y-2",children:(0,t.jsx)("div",{className:"max-w-[90%] w-full rounded-lg bg-muted px-4 py-3",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(d.Skeleton,{className:"h-4 w-full max-w-[360px] mb-2"}),(0,t.jsx)(d.Skeleton,{className:"h-4 w-full max-w-[320px] mb-2"}),(0,t.jsx)(d.Skeleton,{className:"h-4 w-full max-w-[290px]"})]}),s%3==1&&(0,t.jsx)("div",{className:"py-1",children:(0,t.jsx)(d.Skeleton,{className:"h-6 w-32 rounded-md"})}),s%3==1&&(0,t.jsxs)("div",{children:[(0,t.jsx)(d.Skeleton,{className:"h-4 w-full max-w-[340px] mb-2"}),(0,t.jsx)(d.Skeleton,{className:"h-4 w-full max-w-[280px]"})]})]})})})]})})},s)),(0,t.jsx)("div",{children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(d.Skeleton,{className:"flex-shrink-0 w-5 h-5 mt-2 rounded-full"}),(0,t.jsx)("div",{className:"flex-1 space-y-2",children:(0,t.jsxs)("div",{className:"flex items-center gap-1.5 py-1",children:[(0,t.jsx)("div",{className:"h-1.5 w-1.5 rounded-full bg-gray-400/50 animate-pulse"}),(0,t.jsx)("div",{className:"h-1.5 w-1.5 rounded-full bg-gray-400/50 animate-pulse delay-150"}),(0,t.jsx)("div",{className:"h-1.5 w-1.5 rounded-full bg-gray-400/50 animate-pulse delay-300"})]})})]})})]})})]}),e&&(0,t.jsx)("div",{className:"hidden sm:block",children:(0,t.jsx)("div",{className:"h-screen w-[450px] border-l",children:(0,t.jsxs)("div",{className:"p-4",children:[(0,t.jsx)(d.Skeleton,{className:"h-8 w-32 mb-4"}),(0,t.jsx)(d.Skeleton,{className:"h-20 w-full rounded-md mb-4"}),(0,t.jsx)(d.Skeleton,{className:"h-40 w-full rounded-md"})]})})})]})}},83799:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>c});var t=r(60687),a=r(43210),n=r.n(a),d=r(16189),i=r(58214),l=r(83614);function o({threadId:e}){let s=(0,d.useRouter)();return(0,i.K2)(e).isError?(s.replace("/dashboard"),null):(0,t.jsx)(l.y,{isSidePanelOpen:!1})}function c({params:e}){let s=n().use(e);return(0,t.jsx)(o,{threadId:s.threadId})}},84297:e=>{"use strict";e.exports=require("async_hooks")},88104:(e,s,r)=>{Promise.resolve().then(r.bind(r,83799))},90170:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>n,metadata:()=>a});var t=r(37413);let a={title:"Agent Conversation | Kortix Suna",description:"Interactive agent conversation powered by Kortix Suna",openGraph:{title:"Agent Conversation | Kortix Suna",description:"Interactive agent conversation powered by Kortix Suna",type:"website"}};async function n({children:e}){return(0,t.jsx)(t.Fragment,{children:e})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[7719,5193,4267,7096,1265,3530,7156,7976,3667,8188,3806,1841],()=>r(73578));module.exports=t})();