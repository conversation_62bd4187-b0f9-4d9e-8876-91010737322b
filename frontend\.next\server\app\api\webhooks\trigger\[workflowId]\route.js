(()=>{var e={};e.id=6304,e.ids=[6304],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8719:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{isRequestAPICallableInsideAfter:function(){return c},throwForSearchParamsAccessInUseCache:function(){return i},throwWithStaticGenerationBailoutError:function(){return s},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return a}});let o=t(80023),n=t(3295);function s(e,r){throw Object.defineProperty(new o.StaticGenBailoutError(`Route ${e} couldn't be rendered statically because it used ${r}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function a(e,r){throw Object.defineProperty(new o.StaticGenBailoutError(`Route ${e} with \`dynamic = "error"\` couldn't be rendered statically because it used ${r}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function i(e){let r=Object.defineProperty(Error(`Route ${e.route} used "searchParams" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "searchParams" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E634",enumerable:!1,configurable:!0});throw e.invalidUsageError??=r,r}function c(){let e=n.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16582:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>l,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>p});var o={};t.r(o),t.d(o,{GET:()=>u,POST:()=>c});var n=t(96559),s=t(48088),a=t(37719),i=t(32190);async function c(e,{params:r}){try{let{workflowId:t}=await r;console.log(`[Webhook Proxy] Received webhook for workflow: ${t}`);let o=await e.arrayBuffer(),n={};e.headers.forEach((e,r)=>{["host","content-length","transfer-encoding","connection"].includes(r.toLowerCase())||(n[r]=e)});let s=process.env.BACKEND_URL||"http://localhost:8000",a=`${s}/api/webhooks/trigger/${t}`;console.log(`[Webhook Proxy] Backend URL: ${s}`),console.log(`[Webhook Proxy] Target URL: ${a}`),console.log("[Webhook Proxy] Headers to forward:",Object.keys(n)),console.log("[Webhook Proxy] Body size:",o.byteLength);let c=await fetch(a,{method:"POST",headers:{...n,"Content-Type":"application/json"},body:o});console.log(`[Webhook Proxy] Backend response status: ${c.status}`),console.log(`[Webhook Proxy] Backend response ok: ${c.ok}`);let u=await c.text();return console.log("[Webhook Proxy] Backend response data:",u),new i.NextResponse(u,{status:c.status,headers:{"Content-Type":c.headers.get("Content-Type")||"application/json"}})}catch(e){return console.error("[Webhook Proxy] Error occurred:",e),console.error("[Webhook Proxy] Error details:",{name:e instanceof Error?e.name:"Unknown",message:e instanceof Error?e.message:"Unknown error",cause:e instanceof Error?e.cause:void 0}),i.NextResponse.json({error:"Internal server error",details:e instanceof Error?e.message:"Unknown error",timestamp:new Date().toISOString()},{status:500})}}async function u(e,{params:r}){try{let{workflowId:e}=await r,t=process.env.BACKEND_URL||"http://localhost:8000",o=await fetch(`${t}/api/webhooks/test/${e}`,{method:"GET",headers:{"Content-Type":"application/json"}}),n=await o.json();return i.NextResponse.json(n,{status:o.status})}catch(e){return console.error("Webhook test proxy error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/webhooks/trigger/[workflowId]/route",pathname:"/api/webhooks/trigger/[workflowId]",filename:"route",bundlePath:"app/api/webhooks/trigger/[workflowId]/route"},resolvedPagePath:"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\api\\webhooks\\trigger\\[workflowId]\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:d,workUnitAsyncStorage:p,serverHooks:h}=l;function f(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:p})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},43763:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"ReflectAdapter",{enumerable:!0,get:function(){return t}});class t{static get(e,r,t){let o=Reflect.get(e,r,t);return"function"==typeof o?o.bind(e):o}static set(e,r,t,o){return Reflect.set(e,r,t,o)}static has(e,r){return Reflect.has(e,r)}static deleteProperty(e,r){return Reflect.deleteProperty(e,r)}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72609:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{describeHasCheckingStringProperty:function(){return n},describeStringPropertyAccess:function(){return o},wellKnownProperties:function(){return s}});let t=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function o(e,r){return t.test(r)?"`"+e+"."+r+"`":"`"+e+"["+JSON.stringify(r)+"]`"}function n(e,r){let t=JSON.stringify(r);return"`Reflect.has("+e+", "+t+")`, `"+t+" in "+e+"`, or similar"}let s=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},78335:()=>{},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[7719,580],()=>t(16582));module.exports=o})();