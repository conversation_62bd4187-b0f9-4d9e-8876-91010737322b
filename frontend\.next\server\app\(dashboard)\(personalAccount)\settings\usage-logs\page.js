(()=>{var e={};e.id=339,e.ids=[339],e.modules={2507:(e,t,s)=>{"use strict";s.d(t,{U:()=>l});var r=s(67218);s(79130);var n=s(45934),a=s(44999),o=s(17478);let l=async()=>{let e=await (0,a.cookies)(),t="";return t&&!t.startsWith("http")&&(t=`http://${t}`),(0,n.createServerClient)(t,"",{cookies:{getAll:()=>e.getAll(),setAll(t){try{t.forEach(({name:t,value:s,options:r})=>e.set({name:t,value:s,...r}))}catch(e){}}}})};(0,o.D)([l]),(0,r.A)(l,"7f8bed79c8654f95685745e61906af37f96a086236",null)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},4992:(e,t,s)=>{Promise.resolve().then(s.bind(s,62478))},5527:(e,t,s)=>{Promise.resolve().then(s.bind(s,34806))},6211:(e,t,s)=>{"use strict";s.d(t,{A0:()=>o,Table:()=>a,TableBody:()=>l,TableCell:()=>c,TableRow:()=>i,nd:()=>d});var r=s(60687);s(43210);var n=s(4780);function a({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,n.cn)("w-full caption-bottom text-sm",e),...t})})}function o({className:e,...t}){return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,n.cn)("[&_tr]:border-b",e),...t})}function l({className:e,...t}){return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,n.cn)("[&_tr:last-child]:border-0",e),...t})}function i({className:e,...t}){return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,n.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function d({className:e,...t}){return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,n.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,n.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},9881:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(37413),n=s(2507),a=s(75349);async function o(){let e=await (0,n.U)(),{data:t}=await e.rpc("get_personal_account");return(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)(a.default,{accountId:t.account_id})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11576:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(60687),n=s(35950),a=s(85814),o=s.n(a),l=s(16189);function i({children:e}){let t=(0,l.usePathname)();return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"space-y-6 w-full",children:[(0,r.jsx)(n.w,{className:"border-subtle dark:border-white/10"}),(0,r.jsxs)("div",{className:"flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0 w-full max-w-7xl mx-auto px-4",children:[(0,r.jsx)("aside",{className:"lg:w-1/4 p-1",children:(0,r.jsx)("nav",{className:"flex flex-col space-y-1",children:[{name:"Billing",href:"/settings/billing"},{name:"Usage Logs",href:"/settings/usage-logs"}].map(e=>(0,r.jsx)(o(),{href:e.href,className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${t===e.href?"bg-accent text-accent-foreground":"text-muted-foreground hover:bg-accent/50 hover:text-accent-foreground"}`,children:e.name},e.href))})}),(0,r.jsx)("div",{className:"flex-1 bg-card-bg dark:bg-background-secondary p-6 rounded-2xl border border-subtle dark:border-white/10 shadow-custom",children:e})]})]})})}},11997:e=>{"use strict";e.exports=require("punycode")},16745:(e,t,s)=>{"use strict";s.r(t),s.d(t,{"7f8bed79c8654f95685745e61906af37f96a086236":()=>r.U});var r=s(2507)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33252:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(60687);function n(){return(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,r.jsx)("div",{className:"w-12 h-12 border-4 border-primary rounded-full border-t-transparent animate-spin"})})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34806:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\(personalAccount)\\\\settings\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\settings\\layout.tsx","default")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},58671:(e,t,s)=>{Promise.resolve().then(s.bind(s,11576))},62478:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\(personalAccount)\\\\loading.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\loading.tsx","default")},62861:(e,t,s)=>{Promise.resolve().then(s.bind(s,65915))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65915:(e,t,s)=>{"use strict";s.d(t,{default:()=>b});var r=s(60687),n=s(43210),a=s(44493),o=s(6211),l=s(26110),i=s(96834),d=s(85726),c=s(29523),u=s(25334),m=s(41862),x=s(85814),h=s.n(x),p=s(89698),f=s(56003);function b({accountId:e}){let[t,s]=(0,n.useState)(0),[x,b]=(0,n.useState)([]),[g,j]=(0,n.useState)(!0),{data:v,isLoading:y,error:w,refetch:N}=(0,f._o)(t,1e3),C=e=>"string"==typeof e||0===e?"string"==typeof e?e:"$0.0000":`$${e.toFixed(4)}`,k=e=>new Date(e).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),_=(e,t)=>{let s=`/projects/${t}/thread/${e}`;window.open(s,"_blank")};if(y&&0===t)return(0,r.jsxs)(a.Zp,{children:[(0,r.jsxs)(a.aR,{children:[(0,r.jsx)(a.ZB,{children:"Usage Logs"}),(0,r.jsx)(a.BT,{children:"Loading your token usage history..."})]}),(0,r.jsx)(a.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:Array.from({length:5}).map((e,t)=>(0,r.jsx)(d.Skeleton,{className:"h-16 w-full"},t))})})]});if(w)return(0,r.jsxs)(a.Zp,{children:[(0,r.jsx)(a.aR,{children:(0,r.jsx)(a.ZB,{children:"Usage Logs"})}),(0,r.jsx)(a.Wu,{children:(0,r.jsx)("div",{className:"p-4 bg-destructive/10 border border-destructive/20 rounded-lg",children:(0,r.jsxs)("p",{className:"text-sm text-destructive",children:["Error: ",w.message||"Failed to load usage logs"]})})})]});if(v?.message)return(0,r.jsxs)(a.Zp,{children:[(0,r.jsx)(a.aR,{children:(0,r.jsx)(a.ZB,{children:"Usage Logs"})}),(0,r.jsx)(a.Wu,{children:(0,r.jsx)("div",{className:"p-4 bg-muted/30 border border-border rounded-lg text-center",children:(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:v.message})})})]});let P=Object.values(x.reduce((e,t)=>{let s=new Date(t.created_at).toDateString();return e[s]||(e[s]={date:s,logs:[],totalTokens:0,totalCost:0,requestCount:0,models:[]}),e[s].logs.push(t),e[s].totalTokens+=t.total_tokens,e[s].totalCost+="number"==typeof t.estimated_cost?t.estimated_cost:0,e[s].requestCount+=1,e[s].models.includes(t.content.model)||e[s].models.push(t.content.model),e},{})).sort((e,t)=>new Date(t.date).getTime()-new Date(e.date).getTime());return x.reduce((e,t)=>e+("number"==typeof t.estimated_cost?t.estimated_cost:0),0),(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsxs)(a.Zp,{children:[(0,r.jsxs)(a.aR,{children:[(0,r.jsx)(a.ZB,{children:"Daily Usage Logs"}),(0,r.jsx)(a.BT,{children:(0,r.jsxs)("div",{className:"flex justify-between items-center",children:["Your token usage organized by day, sorted by most recent."," ",(0,r.jsx)(c.$,{variant:"outline",asChild:!0,className:"text-sm ml-4",children:(0,r.jsxs)(h(),{href:"/model-pricing",children:["View Model Pricing ",(0,r.jsx)(p.Ngv,{className:"w-4 h-4"})]})})]})})]}),(0,r.jsx)(a.Wu,{children:0===P.length?(0,r.jsx)("div",{className:"text-center py-8",children:(0,r.jsx)("p",{className:"text-muted-foreground",children:"No usage logs found."})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.nD,{type:"single",collapsible:!0,className:"w-full",children:P.map(e=>(0,r.jsxs)(l.As,{value:e.date,children:[(0,r.jsx)(l.$m,{className:"hover:no-underline",children:(0,r.jsxs)("div",{className:"flex justify-between items-center w-full mr-4",children:[(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-semibold",children:k(e.date)}),(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:[e.requestCount," request",1!==e.requestCount?"s":""," •"," ",e.models.join(", ")]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("div",{className:"font-mono font-semibold",children:C(e.totalCost)}),(0,r.jsxs)("div",{className:"text-sm text-muted-foreground font-mono",children:[e.totalTokens.toLocaleString()," tokens"]})]})]})}),(0,r.jsx)(l.ub,{children:(0,r.jsx)("div",{className:"rounded-md border mt-4",children:(0,r.jsxs)(o.Table,{children:[(0,r.jsx)(o.A0,{children:(0,r.jsxs)(o.TableRow,{children:[(0,r.jsx)(o.nd,{children:"Time"}),(0,r.jsx)(o.nd,{children:"Model"}),(0,r.jsx)(o.nd,{className:"text-right",children:"Tokens"}),(0,r.jsx)(o.nd,{className:"text-right",children:"Cost"}),(0,r.jsx)(o.nd,{className:"text-center",children:"Thread"})]})}),(0,r.jsx)(o.TableBody,{children:e.logs.map(e=>(0,r.jsxs)(o.TableRow,{children:[(0,r.jsx)(o.TableCell,{className:"font-mono text-sm",children:new Date(e.created_at).toLocaleTimeString()}),(0,r.jsx)(o.TableCell,{children:(0,r.jsx)(i.E,{className:"font-mono text-xs",children:e.content.model})}),(0,r.jsxs)(o.TableCell,{className:"text-right font-mono font-medium text-sm",children:[e.content.usage.prompt_tokens.toLocaleString()," ","->"," ",e.content.usage.completion_tokens.toLocaleString()]}),(0,r.jsx)(o.TableCell,{className:"text-right font-mono font-medium text-sm",children:C(e.estimated_cost)}),(0,r.jsx)(o.TableCell,{className:"text-center",children:(0,r.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>_(e.thread_id,e.project_id),className:"h-8 w-8 p-0",children:(0,r.jsx)(u.A,{className:"h-4 w-4"})})})]},e.message_id))})]})})})]},e.date))}),g&&(0,r.jsx)("div",{className:"flex justify-center pt-6",children:(0,r.jsx)(c.$,{onClick:()=>{s(t+1)},disabled:y,variant:"outline",children:y?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Loading..."]}):"Load More"})})]})})]})})}},68544:(e,t,s)=>{Promise.resolve().then(s.bind(s,33252))},70400:(e,t,s)=>{"use strict";s.r(t),s.d(t,{"60120624b62a67d0046abca062aae687cbb5ebc44a":()=>r.vI,"602190608cb4aada86562e5e5997e6bb44fbe95e4e":()=>r.$w,"60836a64bf90333e8dead87703a0c5a32e95fa0f8f":()=>r.gj});var r=s(67834)},74075:e=>{"use strict";e.exports=require("zlib")},75349:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\components\\\\billing\\\\usage-logs.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\components\\billing\\usage-logs.tsx","default")},77598:e=>{"use strict";e.exports=require("node:crypto")},78272:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81421:(e,t,s)=>{Promise.resolve().then(s.bind(s,75349))},81630:e=>{"use strict";e.exports=require("http")},84297:e=>{"use strict";e.exports=require("async_hooks")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97870:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>a.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>i});var r=s(65239),n=s(48088),a=s(31369),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let i={children:["",{children:["(dashboard)",{children:["(personalAccount)",{children:["settings",{children:["usage-logs",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,9881)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\settings\\usage-logs\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,34806)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\settings\\layout.tsx"]}]},{loading:[()=>Promise.resolve().then(s.bind(s,62478)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,33532)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(s.bind(s,88524))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,93595)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(s.bind(s,31369)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(s.bind(s,88524))).default(e)],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\settings\\usage-logs\\page.tsx"],c={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(dashboard)/(personalAccount)/settings/usage-logs/page",pathname:"/settings/usage-logs",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})}};var t=require("../../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[7719,5193,4267,7096,1265,3530,7156,7976,4257,9698,3667,8188,3806,1841,6110],()=>s(97870));module.exports=r})();