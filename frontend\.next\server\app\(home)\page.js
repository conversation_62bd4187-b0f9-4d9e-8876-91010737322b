(()=>{var e={};e.id=7813,e.ids=[7813],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6860:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{k:()=>d});var a=r(60687),i=r(14167),n=r(54974),l=r(70334),o=e([n]);function d(){let e=(n.CQ.useCases||[]).filter(e=>e.featured);return(0,a.jsxs)("section",{id:"use-cases",className:"flex flex-col items-center justify-center gap-10 pb-10 w-full relative",children:[(0,a.jsxs)(i.X,{children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-medium tracking-tighter text-center text-balance",children:"See Suna in action"}),(0,a.jsx)("p",{className:"text-muted-foreground text-center text-balance font-medium",children:"Explore real-world examples of how Suna completes complex tasks autonomously"})]}),(0,a.jsxs)("div",{className:"relative w-full h-full",children:[(0,a.jsx)("div",{className:"grid min-[650px]:grid-cols-2 min-[900px]:grid-cols-3 min-[1200px]:grid-cols-4 gap-4 w-full max-w-6xl mx-auto px-6",children:e.map(e=>(0,a.jsxs)("div",{className:"rounded-xl overflow-hidden relative h-fit min-[650px]:h-full flex flex-col md:shadow-[0px_61px_24px_-10px_rgba(0,0,0,0.01),0px_34px_20px_-8px_rgba(0,0,0,0.05),0px_15px_15px_-6px_rgba(0,0,0,0.09),0px_4px_8px_-2px_rgba(0,0,0,0.10),0px_0px_0px_1px_rgba(0,0,0,0.08)] bg-accent",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-4 p-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"rounded-full bg-secondary/10 p-2",children:(0,a.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-secondary",children:e.icon})}),(0,a.jsx)("h3",{className:"text-lg font-medium line-clamp-1",children:e.title})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground leading-relaxed line-clamp-3",children:e.description})]}),(0,a.jsxs)("div",{className:"mt-auto",children:[(0,a.jsx)("hr",{className:"border-border dark:border-white/20 m-0"}),(0,a.jsx)("div",{className:"w-full h-[160px] bg-accent/10",children:(0,a.jsxs)("div",{className:"relative w-full h-full overflow-hidden",children:[(0,a.jsx)("img",{src:e.image||`https://placehold.co/800x400/f5f5f5/666666?text=Suna+${e.title.split(" ").join("+")}`,alt:`Suna ${e.title}`,className:"w-full h-full object-cover"}),(0,a.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 hover:opacity-100 transition-opacity flex items-end justify-start p-4 group",children:(0,a.jsxs)("span",{className:"flex items-center gap-2 text-sm text-white font-medium",children:["Watch replay",(0,a.jsx)(l.A,{className:"size-4 transform group-hover:translate-x-1 transition-transform"})]})})]})})]})]},e.id))}),0===e.length&&(0,a.jsx)("div",{className:"flex flex-col items-center justify-center py-16 text-center",children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"No use cases available yet."})})]})]})}n=(o.then?(await o)():o)[0],s()}catch(e){s(e)}})},8730:(e,t,r)=>{"use strict";r.d(t,{DX:()=>l,Dc:()=>d,TL:()=>n});var s=r(43210),a=r(98599),i=r(60687);function n(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...i}=e;if(s.isValidElement(r)){var n;let e,l,o=(n=r,(l=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.ref:(l=(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?n.props.ref:n.props.ref||n.ref),d=function(e,t){let r={...t};for(let s in t){let a=e[s],i=t[s];/^on[A-Z]/.test(s)?a&&i?r[s]=(...e)=>{let t=i(...e);return a(...e),t}:a&&(r[s]=a):"style"===s?r[s]={...a,...i}:"className"===s&&(r[s]=[a,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==s.Fragment&&(d.ref=t?(0,a.t)(t,o):o),s.cloneElement(r,d)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:a,...n}=e,l=s.Children.toArray(a),o=l.find(c);if(o){let e=o.props.children,a=l.map(t=>t!==o?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...n,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,a):null})}return(0,i.jsx)(t,{...n,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var l=n("Slot"),o=Symbol("radix.slottable");function d(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=o,t}function c(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},12527:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{h:()=>d});var a=r(60687),i=r(54974),n=r(18709),l=r(43210),o=e([i]);let c=(i=(o.then?(await o)():o)[0]).CQ.nav.links;function d(){let e=(0,l.useRef)(null),[t,r]=(0,l.useState)(0),[s,i]=(0,l.useState)(0),[o,d]=(0,l.useState)(!1),[u,x]=(0,l.useState)("hero"),[m,h]=(0,l.useState)(!1),p=(e,t)=>{e.preventDefault();let s=t.href.substring(1),a=document.getElementById(s);if(a){h(!0),x(s);let t=e.currentTarget.parentElement;if(t){let e=t.getBoundingClientRect();r(t.offsetLeft),i(e.width)}let n=a.getBoundingClientRect().top+window.pageYOffset-100;window.scrollTo({top:n,behavior:"smooth"}),setTimeout(()=>{h(!1)},500)}};return(0,a.jsx)("div",{className:"w-full hidden md:block",children:(0,a.jsxs)("ul",{className:"relative mx-auto flex w-fit rounded-full h-11 px-2 items-center justify-center",ref:e,children:[c.map(e=>(0,a.jsx)("li",{className:`z-10 cursor-pointer h-full flex items-center justify-center px-4 py-2 text-sm font-medium transition-colors duration-200 ${u===e.href.substring(1)?"text-primary":"text-primary/60 hover:text-primary"} tracking-tight`,children:(0,a.jsx)("a",{href:e.href,onClick:t=>p(t,e),children:e.name})},e.name)),o&&(0,a.jsx)(n.P.li,{animate:{left:t,width:s},transition:{type:"spring",stiffness:400,damping:30},className:"absolute inset-0 my-1.5 rounded-full bg-accent/60 border border-border"})]})})}s()}catch(e){s(e)}})},15071:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{w:()=>d});var a=r(60687),i=r(54974),n=r(85814),l=r.n(n),o=e([i]);function d(){let{ctaSection:e}=i.CQ;return(0,a.jsx)("section",{id:"cta",className:"flex flex-col items-center justify-center w-full pt-12 pb-12",children:(0,a.jsx)("div",{className:"w-full max-w-6xl mx-auto px-6",children:(0,a.jsx)("div",{className:"h-[400px] md:h-[400px] overflow-hidden shadow-xl w-full border border-border rounded-xl bg-secondary relative z-20",children:(0,a.jsxs)("div",{className:"absolute inset-0 -top-32 md:-top-40 flex flex-col items-center justify-center",children:[(0,a.jsx)("h1",{className:"text-white text-4xl md:text-7xl font-medium tracking-tighter max-w-xs md:max-w-xl text-center",children:e.title}),(0,a.jsxs)("div",{className:"absolute bottom-10 flex flex-col items-center justify-center gap-2",children:[(0,a.jsx)(l(),{href:e.button.href,className:"bg-white text-black font-semibold text-sm h-10 w-fit px-4 rounded-full flex items-center justify-center shadow-md",children:e.button.text}),(0,a.jsx)("span",{className:"text-white text-sm",children:e.subtext})]})]})})})})}i=(o.then?(await o)():o)[0],s()}catch(e){s(e)}})},17977:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\(home)\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(home)\\page.tsx","default")},18072:(e,t,r)=>{Promise.resolve().then(r.bind(r,88047))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23262:(e,t,r)=>{"use strict";r.d(t,{t:()=>m});var s=r(60687),a=r(97840),i=r(11860),n=r(72777),l=r(18709),o=r(43210),d=r(4780);let c={"from-bottom":{initial:{y:"100%",opacity:0},animate:{y:0,opacity:1},exit:{y:"100%",opacity:0}},"from-center":{initial:{scale:.5,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.5,opacity:0}},"from-top":{initial:{y:"-100%",opacity:0},animate:{y:0,opacity:1},exit:{y:"-100%",opacity:0}},"from-left":{initial:{x:"-100%",opacity:0},animate:{x:0,opacity:1},exit:{x:"-100%",opacity:0}},"from-right":{initial:{x:"100%",opacity:0},animate:{x:0,opacity:1},exit:{x:"100%",opacity:0}},fade:{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0}},"top-in-bottom-out":{initial:{y:"-100%",opacity:0},animate:{y:0,opacity:1},exit:{y:"100%",opacity:0}},"left-in-right-out":{initial:{x:"-100%",opacity:0},animate:{x:0,opacity:1},exit:{x:"100%",opacity:0}}};function u({animationStyle:e="from-center",videoSrc:t,thumbnailSrc:r,thumbnailAlt:u="Video thumbnail",className:x}){let[m,h]=(0,o.useState)(!1),p=c[e];return(0,s.jsxs)("div",{className:(0,d.cn)("relative",x),children:[(0,s.jsxs)("div",{className:"group relative cursor-pointer",onClick:()=>h(!0),children:[r?(0,s.jsx)("img",{src:r,alt:u,width:1920,height:1080,className:"w-full transition-all duration-200 ease-out group-hover:brightness-[0.8] isolate"}):(0,s.jsx)("div",{className:"w-full aspect-video bg-background rounded-2xl"}),(0,s.jsx)("div",{className:"absolute isolate inset-0 flex scale-[0.9] items-center justify-center rounded-2xl transition-all duration-200 ease-out group-hover:scale-100",children:(0,s.jsx)("div",{className:"flex size-28 items-center justify-center rounded-full bg-gradient-to-t from-secondary/20 to-[#ACC3F7/15] backdrop-blur-md",children:(0,s.jsx)("div",{className:"relative flex size-20 scale-100 items-center justify-center rounded-full bg-gradient-to-t from-secondary to-white/10 shadow-md transition-all duration-200 ease-out group-hover:scale-[1.2]",children:(0,s.jsx)(a.A,{className:"size-8 scale-100 fill-white text-white transition-transform duration-200 ease-out group-hover:scale-105",style:{filter:"drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06))"}})})})})]}),(0,s.jsx)(n.N,{children:m&&(0,s.jsx)(l.P.div,{initial:{opacity:0},animate:{opacity:1},onClick:()=>h(!1),exit:{opacity:0},className:"fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-md",children:(0,s.jsxs)(l.P.div,{...p,transition:{type:"spring",damping:30,stiffness:300},className:"relative mx-4 aspect-video w-full max-w-4xl md:mx-0",children:[(0,s.jsx)(l.P.button,{className:"absolute cursor-pointer hover:scale-[98%] transition-all duration-200 ease-out -top-16 right-0 rounded-full bg-neutral-900/50 p-2 text-xl text-white ring-1 backdrop-blur-md dark:bg-neutral-100/50 dark:text-black",onClick:()=>h(!1),children:(0,s.jsx)(i.A,{className:"size-5"})}),(0,s.jsx)("div",{className:"relative isolate z-[1] size-full overflow-hidden rounded-2xl border-2 border-white",children:(0,s.jsx)("iframe",{src:(()=>{let e=new URL(t);return e.searchParams.set("autoplay","1"),e.toString()})(),className:"size-full",allowFullScreen:!0,allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"})})]})})})]})}var x=r(14167);function m(){return(0,s.jsxs)("section",{id:"demo",className:"flex flex-col items-center justify-center gap-10 w-full relative",children:[(0,s.jsxs)(x.X,{children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-medium tracking-tighter text-center text-balance pb-1",children:"Watch Intelligence in Motion"}),(0,s.jsx)("p",{className:"text-muted-foreground text-center text-balance font-medium",children:"Watch how Suna executes complex workflows with precision and autonomy"})]}),(0,s.jsx)("div",{className:"relative px-6",children:(0,s.jsxs)("div",{className:"relative w-full max-w-3xl mx-auto shadow-xl rounded-2xl overflow-hidden",children:[(0,s.jsx)(u,{className:"block dark:hidden",animationStyle:"from-center",videoSrc:"https://www.youtube.com/embed/Jnxq0osSg2c?si=k8ddEM8h8lver20s",thumbnailSrc:"/thumbnail-light.png",thumbnailAlt:"Hero Video"}),(0,s.jsx)(u,{className:"hidden dark:block",animationStyle:"from-center",videoSrc:"https://www.youtube.com/embed/Jnxq0osSg2c?si=k8ddEM8h8lver20s",thumbnailSrc:"/thumbnail-dark.png",thumbnailAlt:"Hero Video"})]})})]})}},24224:(e,t,r)=>{"use strict";r.d(t,{F:()=>n});var s=r(49384);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=s.$,n=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:n,defaultVariants:l}=t,o=Object.keys(n).map(e=>{let t=null==r?void 0:r[e],s=null==l?void 0:l[e];if(null===t)return null;let i=a(t)||a(s);return n[e][i]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return i(e,o,null==t||null==(s=t.compoundVariants)?void 0:s.reduce((e,t)=>{let{class:r,className:s,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...d}[t]):({...l,...d})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},26351:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(60687),a=r(43210),i=r(10218),n=r(52581),l=r(75558);function o({returnUrl:e}){let[t,r]=(0,a.useState)(!1),{resolvedTheme:o}=(0,i.D)(),d=(0,a.useCallback)(()=>{sessionStorage.removeItem("isGitHubAuthInProgress"),r(!1)},[]);(0,a.useCallback)(t=>{d(),setTimeout(()=>{window.location.href=t.returnUrl||e||"/dashboard"},100)},[d,e]),(0,a.useCallback)(e=>{d(),n.oR.error(e.message||"GitHub sign-in failed. Please try again.")},[d]);let c=async()=>{if(t)return;let s=null;try{r(!0),e&&sessionStorage.setItem("github-returnUrl",e||"/dashboard");let t=window.open(`${window.location.origin}/auth/github-popup`,"GitHubOAuth","width=500,height=600,scrollbars=yes,resizable=yes,status=yes,location=yes");if(!t)throw Error("Popup was blocked. Please enable popups and try again.");sessionStorage.setItem("isGitHubAuthInProgress","1"),s=setInterval(()=>{t.closed&&(s&&(clearInterval(s),s=null),setTimeout(()=>{sessionStorage.getItem("isGitHubAuthInProgress")&&(d(),n.oR.error("GitHub sign-in was cancelled or not completed."))},500))},1e3)}catch(e){console.error("GitHub sign-in error:",e),s&&clearInterval(s),d(),n.oR.error(e instanceof Error?e.message:"Failed to start GitHub sign-in")}};return(0,s.jsxs)("button",{onClick:c,disabled:t,className:"relative w-full h-12 flex items-center justify-center text-sm font-normal tracking-wide rounded-full bg-background text-foreground border border-border hover:bg-accent/30 transition-all duration-200 disabled:opacity-60 disabled:cursor-not-allowed font-sans","aria-label":t?"Signing in with GitHub...":"Sign in with GitHub",type:"button",children:[(0,s.jsx)("div",{className:"absolute left-0 inset-y-0 flex items-center pl-1 w-10",children:(0,s.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-foreground dark:bg-foreground dark:text-background",children:t?(0,s.jsx)("div",{className:"w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin"}):(0,s.jsx)(l.F.github,{className:"w-5 h-5"})})}),(0,s.jsx)("span",{className:"ml-9 font-light",children:t?"Signing in...":"Continue with GitHub"})]})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28603:(e,t,r)=>{Promise.resolve().then(r.bind(r,36651))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35456:(e,t,r)=>{"use strict";r.d(t,{i:()=>o});var s=r(60687),a=r(43649),i=r(11860),n=r(29523),l=r(16189);function o({message:e,currentUsage:t,limit:r,accountId:o,onDismiss:d,isOpen:c}){let u=(0,l.useRouter)();return c?(0,s.jsx)("div",{className:"fixed bottom-4 right-4 z-[9999]",children:(0,s.jsx)("div",{className:"bg-destructive/15 backdrop-blur-sm border border-destructive/30 rounded-lg p-5 shadow-lg max-w-md",children:(0,s.jsxs)("div",{className:"flex items-start gap-4",children:[(0,s.jsx)("div",{className:"flex-shrink-0 bg-destructive/20 p-2 rounded-full",children:(0,s.jsx)(a.A,{className:"h-5 w-5 text-destructive"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,s.jsx)("h3",{className:"text-sm font-semibold text-destructive",children:"Usage Limit Reached"}),(0,s.jsx)(n.$,{variant:"ghost",size:"icon",onClick:d,className:"h-6 w-6 p-0 text-muted-foreground hover:text-foreground",children:(0,s.jsx)(i.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:e}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:d,className:"text-xs",children:"Dismiss"}),(0,s.jsx)(n.$,{size:"sm",onClick:()=>u.push(`/settings/billing?accountId=${o}`),className:"text-xs bg-destructive hover:bg-destructive/90",children:"Upgrade Plan"})]})]})]})})}):null}},35814:(e,t,r)=>{"use strict";r.d(t,{L:()=>i});var s=r(43210),a=r(5475);function i(){let[e,t]=(0,s.useState)(null);return{billingError:e,handleBillingError:(0,s.useCallback)(e=>{if((0,a.Jn)())return console.log("Running in local development mode - billing checks are disabled"),!1;if(e&&(e.message||e.subscription))return t({message:e.message||"You've reached your monthly usage limit.",currentUsage:e.currentUsage||e.subscription?.current_usage,limit:e.limit||e.subscription?.limit,subscription:e.subscription||{}}),!0;if(402===e.status||e.message&&e.message.includes("Payment Required")){let r=e.data?.detail||{},s=r.subscription||{};return t({message:r.message||"You've reached your monthly usage limit.",currentUsage:s.current_usage,limit:s.limit,subscription:s}),!0}return!1},[]),clearBillingError:(0,s.useCallback)(()=>{t(null)},[])}}},36651:(e,t,r)=>{"use strict";r.d(t,{Navbar:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\navbar.tsx","Navbar")},39219:(e,t,r)=>{Promise.resolve().then(r.bind(r,56269))},41717:(e,t,r)=>{"use strict";r.d(t,{U:()=>x});var s=r(60687);r(43210);var a=r(21134),i=r(363),n=r(10218),l=r(8730),o=r(24224),d=r(4780);let c=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function u({className:e,variant:t,size:r,asChild:a=!1,...i}){let n=a?l.DX:"button";return(0,s.jsx)(n,{"data-slot":"button",className:(0,d.cn)(c({variant:t,size:r,className:e})),...i})}function x(){let{theme:e,setTheme:t}=(0,n.D)();return(0,s.jsxs)(u,{variant:"outline",size:"icon",onClick:()=>t("light"===e?"dark":"light"),className:"cursor-pointer rounded-full h-8 w-8",children:[(0,s.jsx)(a.A,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0 text-primary"}),(0,s.jsx)(i.A,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100 text-primary"}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}},43649:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},51181:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{K:()=>F});var a=r(60687),i=r(54974),n=r(13488),l=r(63977),o=r(43210),d=r(92211),c=r(85814),u=r.n(c),x=r(16189),m=r(58297),h=r(62185),p=r(19321),f=r(58214),g=r(92819),b=r(63503),v=r(35456),j=r(35814),y=r(2190),w=r(5475),N=r(52581),k=r(51601),_=r(26351),C=r(53060),S=r(12392),z=r(22372),A=r(80116),P=r(61739),L=e([i,C]);[i,C]=L.then?(await L)():L;let I=()=>(0,a.jsx)(b.LC,{className:"bg-background/40 backdrop-blur-md"}),E="pendingAgentPrompt";function F(){let{hero:e}=i.CQ,t=(0,l.U)("(max-width: 1024px)"),[r,s]=(0,o.useState)(!1),[c,L]=(0,o.useState)(!1),[F,U]=(0,o.useState)(!1);(0,o.useRef)(null);let{scrollY:G}=(0,d.L)(),[q,R]=(0,o.useState)(""),[B,M]=(0,o.useState)();(0,x.useRouter)();let{user:T,isLoading:H}=(0,m.A)(),{billingError:D,handleBillingError:O,clearBillingError:V}=(0,j.L)(),{data:W}=(0,y.o)(),$=W?.find(e=>e.personal_account),{onOpen:X}=(0,k.h)(),K=(0,p.T)(),[Q,J]=(0,o.useState)(null);(0,f.K2)(Q||"");let Z=(0,o.useRef)(null),{data:Y}=(0,z.GQ)(A._.list({limit:100,sort_by:"name",sort_order:"asc"}),()=>(0,P.vt)({limit:100,sort_by:"name",sort_order:"asc"}),{enabled:!!T&&!H,staleTime:3e5,gcTime:6e5})();Y?.agents;let[ee,et]=(0,o.useState)(!1),er=async(e,t)=>{if((e.trim()||Z.current?.getPendingFiles().length)&&!F){if(!T&&!H){localStorage.setItem(E,e.trim()),et(!0);return}U(!0);try{let r=Z.current?.getPendingFiles()||[];localStorage.removeItem(E);let s=new FormData;s.append("prompt",e),B&&s.append("agent_id",B),r.forEach(e=>{let t=(0,S.L)(e.name);s.append("files",e,t)}),t?.model_name&&s.append("model_name",t.model_name),s.append("enable_thinking",String(t?.enable_thinking??!1)),s.append("reasoning_effort","low"),s.append("stream","true"),s.append("enable_context_manager","false");let a=await K.mutateAsync(s);if(a.thread_id)J(a.thread_id);else throw Error("Agent initiation did not return a thread_id.");Z.current?.clearPendingFiles(),R("")}catch(e){if(e instanceof h.Ey)console.log("Billing error:",e.detail),X("paymentRequiredDialog");else{let t=e instanceof TypeError&&e.message.includes("Failed to fetch");(!(0,w.Jn)()||t)&&N.oR.error(e.message||"Failed to create agent. Please try again.")}}finally{U(!1)}}};return(0,a.jsxs)("section",{id:"hero",className:"w-full relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"relative flex flex-col items-center w-full px-6",children:[(0,a.jsxs)("div",{className:"absolute left-0 top-0 h-[600px] md:h-[800px] w-1/3 -z-10 overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-background z-10"}),(0,a.jsx)("div",{className:"absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10"}),(0,a.jsx)("div",{className:"absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10"}),(0,a.jsx)(n.b,{className:"h-full w-full",squareSize:r&&t?2:2.5,gridGap:r&&t?2:2.5,color:"var(--secondary)",maxOpacity:.4,flickerChance:c?.01:.03})]}),(0,a.jsxs)("div",{className:"absolute right-0 top-0 h-[600px] md:h-[800px] w-1/3 -z-10 overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-background z-10"}),(0,a.jsx)("div",{className:"absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10"}),(0,a.jsx)("div",{className:"absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10"}),(0,a.jsx)(n.b,{className:"h-full w-full",squareSize:r&&t?2:2.5,gridGap:r&&t?2:2.5,color:"var(--secondary)",maxOpacity:.4,flickerChance:c?.01:.03})]}),(0,a.jsx)("div",{className:"absolute inset-x-1/4 top-0 h-[600px] md:h-[800px] -z-20 bg-background rounded-b-xl"}),(0,a.jsxs)("div",{className:"relative z-10 pt-32 max-w-3xl mx-auto h-full w-full flex flex-col gap-10 items-center justify-center",children:[(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center gap-5 pt-16",children:[(0,a.jsxs)("h1",{className:"text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-medium tracking-tighter text-balance text-center",children:[(0,a.jsx)("span",{className:"text-secondary",children:"Suna"}),(0,a.jsx)("span",{className:"text-primary",children:", your AI Employee."})]}),(0,a.jsx)("p",{className:"text-base md:text-lg text-center text-muted-foreground font-medium text-balance leading-relaxed tracking-tight",children:e.description})]}),(0,a.jsx)("div",{className:"flex items-center w-full max-w-4xl gap-2 flex-wrap justify-center",children:(0,a.jsxs)("div",{className:"w-full relative",children:[(0,a.jsx)("div",{className:"relative z-10",children:(0,a.jsx)(C.V,{ref:Z,onSubmit:er,placeholder:"Describe what you need help with...",loading:F,disabled:F,value:q,onChange:R,isLoggedIn:!!T,selectedAgentId:B,onAgentSelect:M,autoFocus:!1})}),(0,a.jsx)("div",{className:"absolute -bottom-4 inset-x-0 h-6 bg-secondary/20 blur-xl rounded-full -z-10 opacity-70"})]})})]})]}),(0,a.jsx)("div",{className:"mb-16 sm:mt-52 max-w-4xl mx-auto"}),(0,a.jsxs)(b.lG,{open:ee,onOpenChange:et,children:[(0,a.jsx)(I,{}),(0,a.jsxs)(b.Cf,{className:"sm:max-w-md rounded-xl bg-background border border-border",children:[(0,a.jsxs)(b.c7,{children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsx)(b.L3,{className:"text-xl font-medium",children:"Sign in to continue"})}),(0,a.jsx)(b.rr,{className:"text-muted-foreground",children:"Sign in or create an account to talk with Suna"})]}),(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)(g.A,{returnUrl:"/dashboard"}),(0,a.jsx)(_.A,{returnUrl:"/dashboard"})]}),(0,a.jsxs)("div",{className:"relative my-6",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-border"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-2 bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] text-muted-foreground",children:"or continue with email"})})]}),(0,a.jsxs)("div",{className:"space-y-4 pt-4",children:[(0,a.jsx)(u(),{href:`/auth?returnUrl=${encodeURIComponent("/dashboard")}`,className:"flex h-12 items-center justify-center w-full text-center rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all shadow-md",onClick:()=>et(!1),children:"Sign in with email"}),(0,a.jsx)(u(),{href:`/auth?mode=signup&returnUrl=${encodeURIComponent("/dashboard")}`,className:"flex h-12 items-center justify-center w-full text-center rounded-full border border-border bg-background hover:bg-accent/20 transition-all",onClick:()=>et(!1),children:"Create new account"})]}),(0,a.jsxs)("div",{className:"mt-4 text-center text-xs text-muted-foreground",children:["By continuing, you agree to our"," ",(0,a.jsx)(u(),{href:"/terms",className:"text-primary hover:underline",children:"Terms of Service"})," ","and"," ",(0,a.jsx)(u(),{href:"/privacy",className:"text-primary hover:underline",children:"Privacy Policy"})]})]})]}),(0,a.jsx)(v.i,{message:D?.message,currentUsage:D?.currentUsage,limit:D?.limit,accountId:$?.account_id,onDismiss:V,isOpen:!!D})]})}s()}catch(e){s(e)}})},53564:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>o});var s=r(65239),a=r(48088),i=r(31369),n=r(30893),l={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let o={children:["",{children:["(home)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,17977)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(home)\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,89282)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(home)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,88524))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,93595)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,88524))).default(e)],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(home)\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(home)/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56269:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{Navbar:()=>y});var a=r(60687),i=r(12527),n=r(41717),l=r(54974),o=r(4780),d=r(11860),c=r(12941),u=r(92211),x=r(18709),m=r(72777),h=r(85814),p=r.n(h),f=r(30474),g=r(43210),b=r(10218),v=r(58297),j=e([i,l]);[i,l]=j.then?(await j)():j;let w="70rem",N={hidden:{opacity:0},visible:{opacity:1},exit:{opacity:0}},k={hidden:{opacity:0,y:100},visible:{opacity:1,y:0,rotate:0,transition:{type:"spring",damping:15,stiffness:200,staggerChildren:.03}},exit:{opacity:0,y:100,transition:{duration:.1}}},_={hidden:{opacity:0},visible:{opacity:1}},C={hidden:{opacity:0},visible:{opacity:1}};function y(){let{scrollY:e}=(0,u.L)(),[t,r]=(0,g.useState)(!1),[s,h]=(0,g.useState)(!1),[j,y]=(0,g.useState)("hero"),{theme:S,resolvedTheme:z}=(0,b.D)(),[A,P]=(0,g.useState)(!1),{user:L}=(0,v.A)(),F=()=>h(e=>!e),I=A&&"dark"===z?"/kortix-logo-white.svg":"/kortix-logo.svg";return(0,a.jsxs)("header",{className:(0,o.cn)("sticky z-50 mx-4 flex justify-center transition-all duration-300 md:mx-0",t?"top-6":"top-4 mx-0"),children:[(0,a.jsx)(x.P.div,{initial:{width:w},animate:{width:t?"800px":w},transition:{duration:.3,ease:[.25,.1,.25,1]},children:(0,a.jsx)("div",{className:(0,o.cn)("mx-auto max-w-7xl rounded-2xl transition-all duration-300  xl:px-0",t?"px-2 border border-border backdrop-blur-lg bg-background/75":"shadow-none px-7"),children:(0,a.jsxs)("div",{className:"flex h-[56px] items-center justify-between p-4",children:[(0,a.jsx)(p(),{href:"/",className:"flex items-center gap-3",children:(0,a.jsx)(f.default,{src:I,alt:"Kortix Logo",width:140,height:22,priority:!0})}),(0,a.jsx)(i.h,{}),(0,a.jsxs)("div",{className:"flex flex-row items-center gap-1 md:gap-3 shrink-0",children:[(0,a.jsx)("div",{className:"flex items-center space-x-3",children:L?(0,a.jsx)(p(),{className:"bg-secondary h-8 hidden md:flex items-center justify-center text-sm font-normal tracking-wide rounded-full text-primary-foreground dark:text-secondary-foreground w-fit px-4 shadow-[inset_0_1px_2px_rgba(255,255,255,0.25),0_3px_3px_-1.5px_rgba(16,24,40,0.06),0_1px_1px_rgba(16,24,40,0.08)] border border-white/[0.12]",href:"/dashboard",children:"Dashboard"}):(0,a.jsx)(p(),{className:"bg-secondary h-8 hidden md:flex items-center justify-center text-sm font-normal tracking-wide rounded-full text-primary-foreground dark:text-secondary-foreground w-fit px-4 shadow-[inset_0_1px_2px_rgba(255,255,255,0.25),0_3px_3px_-1.5px_rgba(16,24,40,0.06),0_1px_1px_rgba(16,24,40,0.08)] border border-white/[0.12]",href:"/auth",children:"Get started"})}),(0,a.jsx)(n.U,{}),(0,a.jsx)("button",{className:"md:hidden border border-border size-8 rounded-md cursor-pointer flex items-center justify-center",onClick:F,children:s?(0,a.jsx)(d.A,{className:"size-5"}):(0,a.jsx)(c.A,{className:"size-5"})})]})]})})}),(0,a.jsx)(m.N,{children:s&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.P.div,{className:"fixed inset-0 bg-black/50 backdrop-blur-sm",initial:"hidden",animate:"visible",exit:"exit",variants:N,transition:{duration:.2},onClick:()=>h(!1)}),(0,a.jsx)(x.P.div,{className:"fixed inset-x-0 w-[95%] mx-auto bottom-3 bg-background border border-border p-4 rounded-xl shadow-lg",initial:"hidden",animate:"visible",exit:"exit",variants:k,children:(0,a.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)(p(),{href:"/",className:"flex items-center gap-3",children:[(0,a.jsx)(f.default,{src:I,alt:"Kortix Logo",width:120,height:22,priority:!0}),(0,a.jsx)("span",{className:"font-medium text-primary text-sm",children:"/ Suna"})]}),(0,a.jsx)("button",{onClick:F,className:"border border-border rounded-md p-1 cursor-pointer",children:(0,a.jsx)(d.A,{className:"size-5"})})]}),(0,a.jsx)(x.P.ul,{className:"flex flex-col text-sm mb-4 border border-border rounded-md",variants:_,children:(0,a.jsx)(m.N,{children:l.CQ.nav.links.map(e=>(0,a.jsx)(x.P.li,{className:"p-2.5 border-b border-border last:border-b-0",variants:C,children:(0,a.jsx)("a",{href:e.href,onClick:t=>{t.preventDefault();let r=document.getElementById(e.href.substring(1));r?.scrollIntoView({behavior:"smooth"}),h(!1)},className:`underline-offset-4 hover:text-primary/80 transition-colors ${j===e.href.substring(1)?"text-primary font-medium":"text-primary/60"}`,children:e.name})},e.id))})}),(0,a.jsxs)("div",{className:"flex flex-col gap-2",children:[L?(0,a.jsx)(p(),{href:"/dashboard",className:"bg-secondary h-8 flex items-center justify-center text-sm font-normal tracking-wide rounded-full text-primary-foreground dark:text-secondary-foreground w-full px-4 shadow-[inset_0_1px_2px_rgba(255,255,255,0.25),0_3px_3px_-1.5px_rgba(16,24,40,0.06),0_1px_1px_rgba(16,24,40,0.08)] border border-white/[0.12] hover:bg-secondary/80 transition-all ease-out active:scale-95",children:"Dashboard"}):(0,a.jsx)(p(),{href:"/auth",className:"bg-secondary h-8 flex items-center justify-center text-sm font-normal tracking-wide rounded-full text-primary-foreground dark:text-secondary-foreground w-full px-4 shadow-[inset_0_1px_2px_rgba(255,255,255,0.25),0_3px_3px_-1.5px_rgba(16,24,40,0.06),0_1px_1px_rgba(16,24,40,0.08)] border border-white/[0.12] hover:bg-secondary/80 transition-all ease-out active:scale-95",children:"Get Started"}),(0,a.jsx)("div",{className:"flex justify-between",children:(0,a.jsx)(n.U,{})})]})]})})]})})]})}s()}catch(e){s(e)}})},56527:e=>{"use strict";e.exports=import("shiki")},57879:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{i:()=>c});var a=r(60687);r(43210);var i=r(63503),n=r(45583),l=r(51601),o=r(56947),d=e([o]);o=(d.then?(await d)():d)[0];let c=()=>{let{isOpen:e,type:t,onClose:r}=(0,l.h)();return(0,a.jsx)(i.lG,{open:e&&"paymentRequiredDialog"===t,onOpenChange:r,children:(0,a.jsxs)(i.Cf,{className:"w-[95vw] max-w-[750px] max-h-[90vh] overflow-hidden flex flex-col p-0",children:[(0,a.jsxs)(i.c7,{className:"px-4 sm:px-6 pt-4 sm:pt-6 flex-shrink-0",children:[(0,a.jsx)(i.L3,{children:"Upgrade Required"}),(0,a.jsx)(i.rr,{children:"You've reached your plan's usage limit. Upgrade to continue enjoying our premium features."})]}),(0,a.jsx)("div",{className:"flex-1 pb-2 overflow-y-auto scrollbar-thin scrollbar-thumb-zinc-300 dark:scrollbar-thumb-zinc-700 scrollbar-track-transparent px-4 sm:px-6 min-h-0",children:(0,a.jsxs)("div",{className:"space-y-4 sm:space-y-6 pb-4",children:[(0,a.jsx)("div",{className:"flex items-start p-3 sm:p-4 bg-destructive/5 border border-destructive/50 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:(0,a.jsx)(n.A,{className:"w-4 h-4 sm:w-5 sm:h-5 text-destructive"})}),(0,a.jsxs)("div",{className:"text-xs sm:text-sm min-w-0",children:[(0,a.jsx)("p",{className:"font-medium text-destructive",children:"Usage Limit Reached"}),(0,a.jsx)("p",{className:"text-destructive break-words",children:"Your current plan has been exhausted for this billing period."})]})]})}),(0,a.jsx)("div",{className:"w-full",children:(0,a.jsx)(o.c,{insideDialog:!0,hideFree:!0,returnUrl:"http://localhost:3000/dashboard",showTitleAndTabs:!1})})]})})]})})};s()}catch(e){s(e)}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},74075:e=>{"use strict";e.exports=require("zlib")},75083:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{V:()=>p});var a=r(60687),i=r(13488),n=r(63977),l=r(54974),o=r(89698),d=r(85814),c=r.n(d),u=r(30474),x=r(10218),m=r(43210),h=e([l]);function p(){let e=(0,n.U)("(max-width: 1024px)"),{theme:t,resolvedTheme:r}=(0,x.D)(),[s,d]=(0,m.useState)(!1);return(0,a.jsxs)("footer",{id:"footer",className:"w-full pb-0",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between p-10 max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex flex-col items-start justify-start gap-y-5 max-w-xs mx-0",children:[(0,a.jsx)(c(),{href:"/",className:"flex items-center gap-2",children:(0,a.jsx)(u.default,{src:s&&"dark"===r?"/kortix-logo-white.svg":"/kortix-logo.svg",alt:"Kortix Logo",width:122,height:22,priority:!0})}),(0,a.jsx)("p",{className:"tracking-tight text-muted-foreground font-medium",children:l.CQ.hero.description}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)("a",{href:"https://github.com/kortix-ai/suna",target:"_blank",rel:"noopener noreferrer","aria-label":"GitHub",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"size-5 text-muted-foreground hover:text-primary transition-colors",children:(0,a.jsx)("path",{fill:"currentColor",d:"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0 1 12 6.844a9.59 9.59 0 0 1 2.504.337c1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.02 10.02 0 0 0 22 12.017C22 6.484 17.522 2 12 2z"})})}),(0,a.jsx)("a",{href:"https://x.com/kortixai",target:"_blank",rel:"noopener noreferrer","aria-label":"X (Twitter)",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"size-5 text-muted-foreground hover:text-primary transition-colors",children:(0,a.jsx)("path",{fill:"currentColor",d:"M18.901 1.153h3.68l-8.04 9.19L24 22.846h-7.406l-5.8-7.584-6.638 7.584H.474l8.6-9.83L0 1.154h7.594l5.243 6.932ZM17.61 20.644h2.039L6.486 3.24H4.298Z"})})}),(0,a.jsx)("a",{href:"https://www.linkedin.com/company/kortix/",target:"_blank",rel:"noopener noreferrer","aria-label":"LinkedIn",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"size-5 text-muted-foreground hover:text-primary transition-colors",children:(0,a.jsx)("path",{fill:"currentColor",d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433a2.062 2.062 0 01-2.063-2.065 2.064 2.064 0 112.063 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})})})]})]}),(0,a.jsx)("div",{className:"pt-5 md:w-1/2",children:(0,a.jsx)("div",{className:"flex flex-col items-start justify-start md:flex-row md:items-center md:justify-between gap-y-5 lg:pl-10",children:l.CQ.footerLinks.map((e,t)=>(0,a.jsxs)("ul",{className:"flex flex-col gap-y-2",children:[(0,a.jsx)("li",{className:"mb-2 text-sm font-semibold text-primary",children:e.title}),e.links.map(e=>(0,a.jsxs)("li",{className:"group inline-flex cursor-pointer items-center justify-start gap-1 text-[15px]/snug text-muted-foreground",children:[(0,a.jsx)(c(),{href:e.url,children:e.title}),(0,a.jsx)("div",{className:"flex size-4 items-center justify-center border border-border rounded translate-x-0 transform opacity-0 transition-all duration-300 ease-out group-hover:translate-x-1 group-hover:opacity-100",children:(0,a.jsx)(o.vKP,{className:"h-4 w-4 "})})]},e.id))]},t))})})]}),(0,a.jsxs)(c(),{href:"https://www.youtube.com/watch?v=nuf5BF1jvjQ",target:"_blank",rel:"noopener noreferrer",className:"block w-full h-48 md:h-64 relative mt-24 z-0 cursor-pointer",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-transparent to-background z-10 from-40%"}),(0,a.jsx)("div",{className:"absolute inset-0 ",children:(0,a.jsx)(i.b,{text:e?"Agents":"Agents Agents Agents",fontSize:e?60:90,className:"h-full w-full",squareSize:2,gridGap:e?2:3,color:"#6B7280",maxOpacity:.3,flickerChance:.1})})]})]})}l=(h.then?(await h)():h)[0],s()}catch(e){s(e)}})},76760:e=>{"use strict";e.exports=require("node:path")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82366:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.d(t,{g:()=>l});var a=r(60687),i=r(57879),n=e([i]);i=(n.then?(await n)():n)[0];let l=()=>(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(i.i,{})});s()}catch(e){s(e)}})},83224:(e,t,r)=>{Promise.resolve().then(r.bind(r,17977))},87463:(e,t,r)=>{"use strict";r.d(t,{X:()=>o});var s=r(60687),a=r(14167),i=r(62157),n=r(85814),l=r.n(n);function o(){return(0,s.jsx)("section",{id:"open-source",className:"flex flex-col items-center justify-center w-full relative pb-18",children:(0,s.jsxs)("div",{className:"w-full max-w-6xl mx-auto px-6",children:[(0,s.jsxs)(a.X,{children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-medium tracking-tighter text-center text-balance pb-1",children:"100% Open Source"}),(0,s.jsx)("p",{className:"text-muted-foreground text-center text-balance font-medium",children:"Suna is fully open source. Join our community and help shape the future of AI."})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 pt-12",children:[(0,s.jsx)("div",{className:"rounded-xl bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] border border-border p-6",children:(0,s.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-primary font-medium",children:[(0,s.jsx)(i.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"kortix-ai/suna"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("h3",{className:"text-2xl font-semibold tracking-tight",children:"The Generalist AI Agent"}),(0,s.jsx)("p",{className:"text-muted-foreground mt-2",children:"Explore, contribute, or fork our repository. Suna is built with transparency and collaboration at its core."})]}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,s.jsx)("span",{className:"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold bg-secondary/10 border-secondary/20 text-secondary",children:"TypeScript"}),(0,s.jsx)("span",{className:"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold bg-secondary/10 border-secondary/20 text-secondary",children:"Python"}),(0,s.jsx)("span",{className:"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold bg-secondary/10 border-secondary/20 text-secondary",children:"Apache 2.0 License"})]}),(0,s.jsxs)(l(),{href:"https://github.com/Kortix-ai/Suna",target:"_blank",rel:"noopener noreferrer",className:"group inline-flex h-10 items-center justify-center gap-2 text-sm font-medium tracking-wide rounded-full text-primary-foreground dark:text-black px-6 shadow-[inset_0_1px_2px_rgba(255,255,255,0.25),0_3px_3px_-1.5px_rgba(16,24,40,0.06),0_1px_1px_rgba(16,24,40,0.08)] bg-primary dark:bg-white hover:bg-primary/90 dark:hover:bg-white/90 transition-all duration-200 w-fit",children:[(0,s.jsx)("span",{children:"View on GitHub"}),(0,s.jsx)("span",{className:"inline-flex items-center justify-center size-5 rounded-full bg-white/20 dark:bg-black/10 group-hover:bg-white/30 dark:group-hover:bg-black/20 transition-colors duration-200",children:(0,s.jsx)("svg",{width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-primary-foreground dark:text-black",children:(0,s.jsx)("path",{d:"M7 17L17 7M17 7H8M17 7V16",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]})]})}),(0,s.jsx)("div",{className:"rounded-xl bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] border border-border p-6",children:(0,s.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,s.jsx)("h3",{className:"text-xl md:text-2xl font-medium tracking-tight",children:"Transparency & Trust"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"We believe AI should be open and accessible to everyone. Our open source approach ensures accountability, innovation, and community collaboration."}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"rounded-full bg-secondary/10 p-2 mt-0.5",children:(0,s.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-secondary",children:[(0,s.jsx)("path",{d:"M9.75 12.75L11.25 14.25L14.25 9.75",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,s.jsx)("path",{d:"M4.75 12C4.75 7.99594 7.99594 4.75 12 4.75C16.0041 4.75 19.25 7.99594 19.25 12C19.25 16.0041 16.0041 19.25 12 19.25C7.99594 19.25 4.75 16.0041 4.75 12Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium",children:"Transparency"}),(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:"Fully auditable codebase"})]})]}),(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"rounded-full bg-secondary/10 p-2 mt-0.5",children:(0,s.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-secondary",children:[(0,s.jsx)("path",{d:"M9.75 12.75L11.25 14.25L14.25 9.75",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,s.jsx)("path",{d:"M4.75 12C4.75 7.99594 7.99594 4.75 12 4.75C16.0041 4.75 19.25 7.99594 19.25 12C19.25 16.0041 16.0041 19.25 12 19.25C7.99594 19.25 4.75 16.0041 4.75 12Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium",children:"Community"}),(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:"Join our developers"})]})]}),(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"rounded-full bg-secondary/10 p-2 mt-0.5",children:(0,s.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-secondary",children:[(0,s.jsx)("path",{d:"M9.75 12.75L11.25 14.25L14.25 9.75",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,s.jsx)("path",{d:"M4.75 12C4.75 7.99594 7.99594 4.75 12 4.75C16.0041 4.75 19.25 7.99594 19.25 12C19.25 16.0041 16.0041 19.25 12 19.25C7.99594 19.25 4.75 16.0041 4.75 12Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium",children:"Apache 2.0"}),(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:"Free to use and modify"})]})]})]})]})})]})]})})}},88047:(e,t,r)=>{"use strict";r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{default:()=>h});var a=r(60687),i=r(15071),n=r(75083),l=r(51181),o=r(87463),d=r(56947),c=r(6860),u=r(82366),x=r(23262),m=e([i,n,l,d,c,u]);function h(){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u.g,{}),(0,a.jsx)("main",{className:"flex flex-col items-center justify-center min-h-screen w-full",children:(0,a.jsxs)("div",{className:"w-full divide-y divide-border",children:[(0,a.jsx)(l.K,{}),(0,a.jsx)(c.k,{}),(0,a.jsx)(o.X,{}),(0,a.jsx)("div",{className:"flex flex-col items-center px-4",children:(0,a.jsx)(d.c,{})}),(0,a.jsx)("div",{className:"pb-10 mx-auto",children:(0,a.jsx)(x.t,{})}),(0,a.jsx)(i.w,{}),(0,a.jsx)(n.V,{})]})})]})}[i,n,l,d,c,u]=m.then?(await m)():m,s()}catch(e){s(e)}})},89282:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(37413),a=r(36651);function i({children:e}){return(0,s.jsxs)("div",{className:"w-full relative",children:[(0,s.jsx)("div",{className:"block w-px h-full border-l border-border fixed top-0 left-6 z-10"}),(0,s.jsx)("div",{className:"block w-px h-full border-r border-border fixed top-0 right-6 z-10"}),(0,s.jsx)(a.Navbar,{}),e]})}},91645:e=>{"use strict";e.exports=require("net")},92819:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(60687),a=r(43210),i=r(72600),n=r(79481),l=r(10218);function o({returnUrl:e}){let t=process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,[r,o]=(0,a.useState)(!1),{resolvedTheme:d}=(0,l.D)();return((0,a.useCallback)(async t=>{try{o(!0);let r=(0,n.U)();console.log("Starting Google sign in process");let{error:s}=await r.auth.signInWithIdToken({provider:"google",token:t.credential});if(s)throw s;console.log("Google sign in successful, preparing redirect to:",e||"/dashboard"),setTimeout(()=>{console.log("Executing redirect now to:",e||"/dashboard"),window.location.href=e||"/dashboard"},500)}catch(e){console.error("Error signing in with Google:",e),o(!1)}},[e]),t)?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{id:"g_id_onload","data-client_id":t,"data-context":"signin","data-ux_mode":"popup","data-auto_prompt":"false","data-itp_support":"true","data-callback":"handleGoogleSignIn"}),(0,s.jsx)("div",{id:"google-signin-button",className:"w-full h-12"}),(0,s.jsx)(i.default,{src:"https://accounts.google.com/gsi/client",strategy:"afterInteractive",onLoad:()=>{if(window.google&&t){let e=document.getElementById("google-signin-button");e&&(window.google.accounts.id.renderButton(e,{type:"standard",theme:"dark"===d?"filled_black":"outline",size:"large",text:"continue_with",shape:"pill",logoAlignment:"left",width:e.offsetWidth}),setTimeout(()=>{let t=e.querySelector('div[role="button"]');t instanceof HTMLElement&&(t.style.borderRadius="9999px",t.style.width="100%",t.style.height="56px",t.style.border="1px solid var(--border)",t.style.background="var(--background)",t.style.transition="all 0.2s")},100))}}})]}):(0,s.jsxs)("button",{disabled:!0,className:"w-full h-12 flex items-center justify-center gap-2 text-sm font-medium tracking-wide rounded-full bg-background border border-border opacity-60 cursor-not-allowed",children:[(0,s.jsxs)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z",fill:"#4285F4"}),(0,s.jsx)("path",{d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z",fill:"#34A853"}),(0,s.jsx)("path",{d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z",fill:"#FBBC05"}),(0,s.jsx)("path",{d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z",fill:"#EA4335"})]}),"Google Sign-In Not Configured"]})}},94735:e=>{"use strict";e.exports=require("events")},98599:(e,t,r)=>{"use strict";r.d(t,{s:()=>n,t:()=>i});var s=r(43210);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,s=e.map(e=>{let s=a(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():a(e[t],null)}}}}function n(...e){return s.useCallback(i(...e),e)}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,5193,4267,1265,3530,7156,4097,6914,9307,5811,9781,9697,9698,3667,8188,3806,5558,6947,5966,1155,9490,3060],()=>r(53564));module.exports=s})();