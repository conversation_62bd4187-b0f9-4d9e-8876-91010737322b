(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9879],{475:(e,t,n)=>{"use strict";let r,i;n.d(t,{Yz:()=>t5});class o extends Error{constructor(e){super(e),this.name="ShikiError"}}var a=n(49509);function l(e,...t){return t.forEach(t=>{for(let n in t)e[n]=t[n]}),e}var s=/\$(\d+)|\${(\d+):\/(downcase|upcase)}/g,c=class{static hasCaptures(e){return null!==e&&(s.lastIndex=0,s.test(e))}static replaceCaptures(e,t,n){return e.replace(s,(e,r,i,o)=>{let a=n[parseInt(r||i,10)];if(!a)return e;{let e=t.substring(a.start,a.end);for(;"."===e[0];)e=e.substring(1);switch(o){case"downcase":return e.toLowerCase();case"upcase":return e.toUpperCase();default:return e}}})}};function u(e,t){return e<t?-1:+(e>t)}function h(e,t){if(null===e&&null===t)return 0;if(!e)return -1;if(!t)return 1;let n=e.length,r=t.length;if(n===r){for(let r=0;r<n;r++){var i,o;let n=(i=e[r],i<(o=t[r])?-1:+(i>o));if(0!==n)return n}return 0}return n-r}function p(e){return!!(/^#[0-9a-f]{6}$/i.test(e)||/^#[0-9a-f]{8}$/i.test(e)||/^#[0-9a-f]{3}$/i.test(e)||/^#[0-9a-f]{4}$/i.test(e))}function d(e){return e.replace(/[\-\\\{\}\*\+\?\|\^\$\.\,\[\]\(\)\#\s]/g,"\\$&")}var f=class{constructor(e){this.fn=e}cache=new Map;get(e){if(this.cache.has(e))return this.cache.get(e);let t=this.fn(e);return this.cache.set(e,t),t}},m=class{constructor(e,t,n){this._colorMap=e,this._defaults=t,this._root=n}static createFromRawTheme(e,t){return this.createFromParsedTheme(function(e){if(!e||!e.settings||!Array.isArray(e.settings))return[];let t=e.settings,n=[],r=0;for(let e=0,i=t.length;e<i;e++){let i,o=t[e];if(!o.settings)continue;if("string"==typeof o.scope){let e=o.scope;i=(e=(e=e.replace(/^[,]+/,"")).replace(/[,]+$/,"")).split(",")}else i=Array.isArray(o.scope)?o.scope:[""];let a=-1;if("string"==typeof o.settings.fontStyle){a=0;let e=o.settings.fontStyle.split(" ");for(let t=0,n=e.length;t<n;t++)switch(e[t]){case"italic":a|=1;break;case"bold":a|=2;break;case"underline":a|=4;break;case"strikethrough":a|=8}}let l=null;"string"==typeof o.settings.foreground&&p(o.settings.foreground)&&(l=o.settings.foreground);let s=null;"string"==typeof o.settings.background&&p(o.settings.background)&&(s=o.settings.background);for(let t=0,o=i.length;t<o;t++){let o=i[t].trim().split(" "),c=o[o.length-1],u=null;o.length>1&&(u=o.slice(0,o.length-1)).reverse(),n[r++]=new y(c,u,e,a,l,s)}}return n}(e),t)}static createFromParsedTheme(e,t){return function(e,t){e.sort((e,t)=>{var n,r;let i=(n=e.scope,n<(r=t.scope)?-1:+(n>r));return 0!==i||0!==(i=h(e.parentScopes,t.parentScopes))?i:e.index-t.index});let n=0,r="#000000",i="#ffffff";for(;e.length>=1&&""===e[0].scope;){let t=e.shift();-1!==t.fontStyle&&(n=t.fontStyle),null!==t.foreground&&(r=t.foreground),null!==t.background&&(i=t.background)}let o=new x(t),a=new b(n,o.getId(r),o.getId(i)),l=new S(new w(0,null,-1,0,0),[]);for(let t=0,n=e.length;t<n;t++){let n=e[t];l.insert(0,n.scope,n.parentScopes,n.fontStyle,o.getId(n.foreground),o.getId(n.background))}return new m(o,a,l)}(e,t)}_cachedMatchRoot=new f(e=>this._root.match(e));getColorMap(){return this._colorMap.getColorMap()}getDefaults(){return this._defaults}match(e){if(null===e)return this._defaults;let t=e.scopeName,n=this._cachedMatchRoot.get(t).find(t=>(function(e,t){if(0===t.length)return!0;for(let i=0;i<t.length;i++){var n,r;let o=t[i],a=!1;if(">"===o){if(i===t.length-1)return!1;o=t[++i],a=!0}for(;e&&(n=e.scopeName,!((r=o)===n||n.startsWith(r)&&"."===n[r.length]));){if(a)return!1;e=e.parent}if(!e)return!1;e=e.parent}return!0})(e.parent,t.parentScopes));return n?new b(n.fontStyle,n.foreground,n.background):null}},g=class e{constructor(e,t){this.parent=e,this.scopeName=t}static push(t,n){for(let r of n)t=new e(t,r);return t}static from(...t){let n=null;for(let r=0;r<t.length;r++)n=new e(n,t[r]);return n}push(t){return new e(this,t)}getSegments(){let e=this,t=[];for(;e;)t.push(e.scopeName),e=e.parent;return t.reverse(),t}toString(){return this.getSegments().join(" ")}extends(e){return this===e||null!==this.parent&&this.parent.extends(e)}getExtensionIfDefined(e){let t=[],n=this;for(;n&&n!==e;)t.push(n.scopeName),n=n.parent;return n===e?t.reverse():void 0}},b=class{constructor(e,t,n){this.fontStyle=e,this.foregroundId=t,this.backgroundId=n}},y=class{constructor(e,t,n,r,i,o){this.scope=e,this.parentScopes=t,this.index=n,this.fontStyle=r,this.foreground=i,this.background=o}},k=(e=>(e[e.NotSet=-1]="NotSet",e[e.None=0]="None",e[e.Italic=1]="Italic",e[e.Bold=2]="Bold",e[e.Underline=4]="Underline",e[e.Strikethrough=8]="Strikethrough",e))(k||{}),x=class{_isFrozen;_lastColorId;_id2color;_color2id;constructor(e){if(this._lastColorId=0,this._id2color=[],this._color2id=Object.create(null),Array.isArray(e)){this._isFrozen=!0;for(let t=0,n=e.length;t<n;t++)this._color2id[e[t]]=t,this._id2color[t]=e[t]}else this._isFrozen=!1}getId(e){if(null===e)return 0;e=e.toUpperCase();let t=this._color2id[e];if(t)return t;if(this._isFrozen)throw Error(`Missing color in color map - ${e}`);return t=++this._lastColorId,this._color2id[e]=t,this._id2color[t]=e,t}getColorMap(){return this._id2color.slice(0)}},v=Object.freeze([]),w=class e{scopeDepth;parentScopes;fontStyle;foreground;background;constructor(e,t,n,r,i){this.scopeDepth=e,this.parentScopes=t||v,this.fontStyle=n,this.foreground=r,this.background=i}clone(){return new e(this.scopeDepth,this.parentScopes,this.fontStyle,this.foreground,this.background)}static cloneArr(e){let t=[];for(let n=0,r=e.length;n<r;n++)t[n]=e[n].clone();return t}acceptOverwrite(e,t,n,r){this.scopeDepth>e?console.log("how did this happen?"):this.scopeDepth=e,-1!==t&&(this.fontStyle=t),0!==n&&(this.foreground=n),0!==r&&(this.background=r)}},S=class e{constructor(e,t=[],n={}){this._mainRule=e,this._children=n,this._rulesWithParentScopes=t}_rulesWithParentScopes;static _cmpBySpecificity(e,t){if(e.scopeDepth!==t.scopeDepth)return t.scopeDepth-e.scopeDepth;let n=0,r=0;for(;">"===e.parentScopes[n]&&n++,">"===t.parentScopes[r]&&r++,!(n>=e.parentScopes.length)&&!(r>=t.parentScopes.length);){let i=t.parentScopes[r].length-e.parentScopes[n].length;if(0!==i)return i;n++,r++}return t.parentScopes.length-e.parentScopes.length}match(t){if(""!==t){let e,n,r=t.indexOf(".");if(-1===r?(e=t,n=""):(e=t.substring(0,r),n=t.substring(r+1)),this._children.hasOwnProperty(e))return this._children[e].match(n)}let n=this._rulesWithParentScopes.concat(this._mainRule);return n.sort(e._cmpBySpecificity),n}insert(t,n,r,i,o,a){let l,s,c;if(""===n)return void this._doInsertHere(t,r,i,o,a);let u=n.indexOf(".");-1===u?(l=n,s=""):(l=n.substring(0,u),s=n.substring(u+1)),this._children.hasOwnProperty(l)?c=this._children[l]:(c=new e(this._mainRule.clone(),w.cloneArr(this._rulesWithParentScopes)),this._children[l]=c),c.insert(t+1,s,r,i,o,a)}_doInsertHere(e,t,n,r,i){if(null===t)return void this._mainRule.acceptOverwrite(e,n,r,i);for(let o=0,a=this._rulesWithParentScopes.length;o<a;o++){let a=this._rulesWithParentScopes[o];if(0===h(a.parentScopes,t))return void a.acceptOverwrite(e,n,r,i)}-1===n&&(n=this._mainRule.fontStyle),0===r&&(r=this._mainRule.foreground),0===i&&(i=this._mainRule.background),this._rulesWithParentScopes.push(new w(e,t,n,r,i))}},_=class e{static toBinaryStr(e){return e.toString(2).padStart(32,"0")}static print(t){let n=e.getLanguageId(t),r=e.getTokenType(t),i=e.getFontStyle(t);console.log({languageId:n,tokenType:r,fontStyle:i,foreground:e.getForeground(t),background:e.getBackground(t)})}static getLanguageId(e){return(255&e)>>>0}static getTokenType(e){return(768&e)>>>8}static containsBalancedBrackets(e){return(1024&e)!=0}static getFontStyle(e){return(30720&e)>>>11}static getForeground(e){return(0xff8000&e)>>>15}static getBackground(e){return(0xff000000&e)>>>24}static set(t,n,r,i,o,a,l){let s=e.getLanguageId(t),c=e.getTokenType(t),u=+!!e.containsBalancedBrackets(t),h=e.getFontStyle(t),p=e.getForeground(t),d=e.getBackground(t);return 0!==n&&(s=n),8!==r&&(c=r),null!==i&&(u=+!!i),-1!==o&&(h=o),0!==a&&(p=a),0!==l&&(d=l),(0|s|c<<8|u<<10|h<<11|p<<15|d<<24)>>>0}};function C(e,t){var n;let r,i,o=[],a=(n=e,i=(r=/([LR]:|[\w\.:][\w\.:\-]*|[\,\|\-\(\)])/g).exec(n),{next:()=>{if(!i)return null;let e=i[0];return i=r.exec(n),e}}),l=a.next();for(;null!==l;){let e=0;if(2===l.length&&":"===l.charAt(1)){switch(l.charAt(0)){case"R":e=1;break;case"L":e=-1;break;default:console.log(`Unknown priority ${l} in scope selector`)}l=a.next()}let t=c();if(o.push({matcher:t,priority:e}),","!==l)break;l=a.next()}return o;function s(){if("-"===l){l=a.next();let e=s();return t=>!!e&&!e(t)}if("("===l){l=a.next();let e=function(){let e=[],t=c();for(;t&&(e.push(t),"|"===l||","===l);){do l=a.next();while("|"===l||","===l);t=c()}return t=>e.some(e=>e(t))}();return")"===l&&(l=a.next()),e}if(P(l)){let e=[];do e.push(l),l=a.next();while(P(l));return n=>t(e,n)}return null}function c(){let e=[],t=s();for(;t;)e.push(t),t=s();return t=>e.every(e=>e(t))}}function P(e){return!!e&&!!e.match(/[\w\.:]+/)}var A=(e=>(e[e.None=0]="None",e[e.NotBeginString=1]="NotBeginString",e[e.NotEndString=2]="NotEndString",e[e.NotBeginPosition=4]="NotBeginPosition",e[e.DebugCall=8]="DebugCall",e))(A||{});function T(e){"function"==typeof e.dispose&&e.dispose()}var N=class{constructor(e){this.scopeName=e}toKey(){return this.scopeName}},E=class{constructor(e,t){this.scopeName=e,this.ruleName=t}toKey(){return`${this.scopeName}#${this.ruleName}`}},L=class{_references=[];_seenReferenceKeys=new Set;get references(){return this._references}visitedRule=new Set;add(e){let t=e.toKey();this._seenReferenceKeys.has(t)||(this._seenReferenceKeys.add(t),this._references.push(e))}},I=class{constructor(e,t){this.repo=e,this.initialScopeName=t,this.seenFullScopeRequests.add(this.initialScopeName),this.Q=[new N(this.initialScopeName)]}seenFullScopeRequests=new Set;seenPartialScopeRequests=new Set;Q;processQueue(){let e=this.Q;this.Q=[];let t=new L;for(let n of e)!function(e,t,n,r){let i=n.lookup(e.scopeName);if(!i){if(e.scopeName===t)throw Error(`No grammar provided for <${t}>`);return}let o=n.lookup(t);e instanceof N?O({baseGrammar:o,selfGrammar:i},r):R(e.ruleName,{baseGrammar:o,selfGrammar:i,repository:i.repository},r);let a=n.injections(e.scopeName);if(a)for(let e of a)r.add(new N(e))}(n,this.initialScopeName,this.repo,t);for(let e of t.references)if(e instanceof N){if(this.seenFullScopeRequests.has(e.scopeName))continue;this.seenFullScopeRequests.add(e.scopeName),this.Q.push(e)}else{if(this.seenFullScopeRequests.has(e.scopeName)||this.seenPartialScopeRequests.has(e.toKey()))continue;this.seenPartialScopeRequests.add(e.toKey()),this.Q.push(e)}}};function R(e,t,n){t.repository&&t.repository[e]&&D([t.repository[e]],t,n)}function O(e,t){e.selfGrammar.patterns&&Array.isArray(e.selfGrammar.patterns)&&D(e.selfGrammar.patterns,{...e,repository:e.selfGrammar.repository},t),e.selfGrammar.injections&&D(Object.values(e.selfGrammar.injections),{...e,repository:e.selfGrammar.repository},t)}function D(e,t,n){for(let r of e){if(n.visitedRule.has(r))continue;n.visitedRule.add(r);let e=r.repository?l({},t.repository,r.repository):t.repository;Array.isArray(r.patterns)&&D(r.patterns,{...t,repository:e},n);let i=r.include;if(!i)continue;let o=$(i);switch(o.kind){case 0:O({...t,selfGrammar:t.baseGrammar},n);break;case 1:O(t,n);break;case 2:R(o.ruleName,{...t,repository:e},n);break;case 3:case 4:let a=o.scopeName===t.selfGrammar.scopeName?t.selfGrammar:o.scopeName===t.baseGrammar.scopeName?t.baseGrammar:void 0;if(a){let r={baseGrammar:t.baseGrammar,selfGrammar:a,repository:e};4===o.kind?R(o.ruleName,r,n):O(r,n)}else 4===o.kind?n.add(new E(o.scopeName,o.ruleName)):n.add(new N(o.scopeName))}}}var M=class{kind=0},z=class{kind=1},B=class{constructor(e){this.ruleName=e}kind=2},F=class{constructor(e){this.scopeName=e}kind=3},j=class{constructor(e,t){this.scopeName=e,this.ruleName=t}kind=4};function $(e){if("$base"===e)return new M;if("$self"===e)return new z;let t=e.indexOf("#");return -1===t?new F(e):0===t?new B(e.substring(1)):new j(e.substring(0,t),e.substring(t+1))}var H=/\\(\d+)/,G=/\\(\d+)/g;Symbol("RuleId");var U=class{$location;id;_nameIsCapturing;_name;_contentNameIsCapturing;_contentName;constructor(e,t,n,r){this.$location=e,this.id=t,this._name=n||null,this._nameIsCapturing=c.hasCaptures(this._name),this._contentName=r||null,this._contentNameIsCapturing=c.hasCaptures(this._contentName)}get debugName(){let e=this.$location?`${function e(t){let n=~t.lastIndexOf("/")||~t.lastIndexOf("\\");return 0===n?t:~n==t.length-1?e(t.substring(0,t.length-1)):t.substr(~n+1)}(this.$location.filename)}:${this.$location.line}`:"unknown";return`${this.constructor.name}#${this.id} @ ${e}`}getName(e,t){return this._nameIsCapturing&&null!==this._name&&null!==e&&null!==t?c.replaceCaptures(this._name,e,t):this._name}getContentName(e,t){return this._contentNameIsCapturing&&null!==this._contentName?c.replaceCaptures(this._contentName,e,t):this._contentName}},q=class extends U{retokenizeCapturedWithRuleId;constructor(e,t,n,r,i){super(e,t,n,r),this.retokenizeCapturedWithRuleId=i}dispose(){}collectPatterns(e,t){throw Error("Not supported!")}compile(e,t){throw Error("Not supported!")}compileAG(e,t,n,r){throw Error("Not supported!")}},W=class extends U{_match;captures;_cachedCompiledPatterns;constructor(e,t,n,r,i){super(e,t,n,null),this._match=new Z(r,this.id),this.captures=i,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugMatchRegExp(){return`${this._match.source}`}collectPatterns(e,t){t.push(this._match)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,n,r){return this._getCachedCompiledPatterns(e).compileAG(e,n,r)}_getCachedCompiledPatterns(e){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new K,this.collectPatterns(e,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}},V=class extends U{hasMissingPatterns;patterns;_cachedCompiledPatterns;constructor(e,t,n,r,i){super(e,t,n,r),this.patterns=i.patterns,this.hasMissingPatterns=i.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}collectPatterns(e,t){for(let n of this.patterns)e.getRule(n).collectPatterns(e,t)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,n,r){return this._getCachedCompiledPatterns(e).compileAG(e,n,r)}_getCachedCompiledPatterns(e){return this._cachedCompiledPatterns||(this._cachedCompiledPatterns=new K,this.collectPatterns(e,this._cachedCompiledPatterns)),this._cachedCompiledPatterns}},Q=class extends U{_begin;beginCaptures;_end;endHasBackReferences;endCaptures;applyEndPatternLast;hasMissingPatterns;patterns;_cachedCompiledPatterns;constructor(e,t,n,r,i,o,a,l,s,c){super(e,t,n,r),this._begin=new Z(i,this.id),this.beginCaptures=o,this._end=new Z(a||"￿",-1),this.endHasBackReferences=this._end.hasBackReferences,this.endCaptures=l,this.applyEndPatternLast=s||!1,this.patterns=c.patterns,this.hasMissingPatterns=c.hasMissingPatterns,this._cachedCompiledPatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugEndRegExp(){return`${this._end.source}`}getEndWithResolvedBackReferences(e,t){return this._end.resolveBackReferences(e,t)}collectPatterns(e,t){t.push(this._begin)}compile(e,t){return this._getCachedCompiledPatterns(e,t).compile(e)}compileAG(e,t,n,r){return this._getCachedCompiledPatterns(e,t).compileAG(e,n,r)}_getCachedCompiledPatterns(e,t){if(!this._cachedCompiledPatterns){for(let t of(this._cachedCompiledPatterns=new K,this.patterns))e.getRule(t).collectPatterns(e,this._cachedCompiledPatterns);this.applyEndPatternLast?this._cachedCompiledPatterns.push(this._end.hasBackReferences?this._end.clone():this._end):this._cachedCompiledPatterns.unshift(this._end.hasBackReferences?this._end.clone():this._end)}return this._end.hasBackReferences&&(this.applyEndPatternLast?this._cachedCompiledPatterns.setSource(this._cachedCompiledPatterns.length()-1,t):this._cachedCompiledPatterns.setSource(0,t)),this._cachedCompiledPatterns}},J=class extends U{_begin;beginCaptures;whileCaptures;_while;whileHasBackReferences;hasMissingPatterns;patterns;_cachedCompiledPatterns;_cachedCompiledWhilePatterns;constructor(e,t,n,r,i,o,a,l,s){super(e,t,n,r),this._begin=new Z(i,this.id),this.beginCaptures=o,this.whileCaptures=l,this._while=new Z(a,-2),this.whileHasBackReferences=this._while.hasBackReferences,this.patterns=s.patterns,this.hasMissingPatterns=s.hasMissingPatterns,this._cachedCompiledPatterns=null,this._cachedCompiledWhilePatterns=null}dispose(){this._cachedCompiledPatterns&&(this._cachedCompiledPatterns.dispose(),this._cachedCompiledPatterns=null),this._cachedCompiledWhilePatterns&&(this._cachedCompiledWhilePatterns.dispose(),this._cachedCompiledWhilePatterns=null)}get debugBeginRegExp(){return`${this._begin.source}`}get debugWhileRegExp(){return`${this._while.source}`}getWhileWithResolvedBackReferences(e,t){return this._while.resolveBackReferences(e,t)}collectPatterns(e,t){t.push(this._begin)}compile(e,t){return this._getCachedCompiledPatterns(e).compile(e)}compileAG(e,t,n,r){return this._getCachedCompiledPatterns(e).compileAG(e,n,r)}_getCachedCompiledPatterns(e){if(!this._cachedCompiledPatterns)for(let t of(this._cachedCompiledPatterns=new K,this.patterns))e.getRule(t).collectPatterns(e,this._cachedCompiledPatterns);return this._cachedCompiledPatterns}compileWhile(e,t){return this._getCachedCompiledWhilePatterns(e,t).compile(e)}compileWhileAG(e,t,n,r){return this._getCachedCompiledWhilePatterns(e,t).compileAG(e,n,r)}_getCachedCompiledWhilePatterns(e,t){return this._cachedCompiledWhilePatterns||(this._cachedCompiledWhilePatterns=new K,this._cachedCompiledWhilePatterns.push(this._while.hasBackReferences?this._while.clone():this._while)),this._while.hasBackReferences&&this._cachedCompiledWhilePatterns.setSource(0,t||"￿"),this._cachedCompiledWhilePatterns}},Y=class e{static createCaptureRule(e,t,n,r,i){return e.registerRule(e=>new q(t,e,n,r,i))}static getCompiledRuleId(t,n,r){return t.id||n.registerRule(i=>{if(t.id=i,t.match)return new W(t.$vscodeTextmateLocation,t.id,t.name,t.match,e._compileCaptures(t.captures,n,r));if(void 0===t.begin){t.repository&&(r=l({},r,t.repository));let i=t.patterns;return void 0===i&&t.include&&(i=[{include:t.include}]),new V(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,e._compilePatterns(i,n,r))}return t.while?new J(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,t.begin,e._compileCaptures(t.beginCaptures||t.captures,n,r),t.while,e._compileCaptures(t.whileCaptures||t.captures,n,r),e._compilePatterns(t.patterns,n,r)):new Q(t.$vscodeTextmateLocation,t.id,t.name,t.contentName,t.begin,e._compileCaptures(t.beginCaptures||t.captures,n,r),t.end,e._compileCaptures(t.endCaptures||t.captures,n,r),t.applyEndPatternLast,e._compilePatterns(t.patterns,n,r))}),t.id}static _compileCaptures(t,n,r){let i=[];if(t){let o=0;for(let e in t){if("$vscodeTextmateLocation"===e)continue;let t=parseInt(e,10);t>o&&(o=t)}for(let e=0;e<=o;e++)i[e]=null;for(let o in t){if("$vscodeTextmateLocation"===o)continue;let a=parseInt(o,10),l=0;t[o].patterns&&(l=e.getCompiledRuleId(t[o],n,r)),i[a]=e.createCaptureRule(n,t[o].$vscodeTextmateLocation,t[o].name,t[o].contentName,l)}}return i}static _compilePatterns(t,n,r){let i=[];if(t)for(let o=0,a=t.length;o<a;o++){let a=t[o],l=-1;if(a.include){let t=$(a.include);switch(t.kind){case 0:case 1:l=e.getCompiledRuleId(r[a.include],n,r);break;case 2:let i=r[t.ruleName];i&&(l=e.getCompiledRuleId(i,n,r));break;case 3:case 4:let o=t.scopeName,s=4===t.kind?t.ruleName:null,c=n.getExternalGrammar(o,r);if(c)if(s){let t=c.repository[s];t&&(l=e.getCompiledRuleId(t,n,c.repository))}else l=e.getCompiledRuleId(c.repository.$self,n,c.repository)}}else l=e.getCompiledRuleId(a,n,r);if(-1!==l){let e=n.getRule(l),t=!1;if((e instanceof V||e instanceof Q||e instanceof J)&&e.hasMissingPatterns&&0===e.patterns.length&&(t=!0),t)continue;i.push(l)}}return{patterns:i,hasMissingPatterns:(t?t.length:0)!==i.length}}},Z=class e{source;ruleId;hasAnchor;hasBackReferences;_anchorCache;constructor(e,t){if(e&&"string"==typeof e){let t=e.length,n=0,r=[],i=!1;for(let o=0;o<t;o++)if("\\"===e.charAt(o)&&o+1<t){let t=e.charAt(o+1);"z"===t?(r.push(e.substring(n,o)),r.push("$(?!\\n)(?<!\\n)"),n=o+2):("A"===t||"G"===t)&&(i=!0),o++}this.hasAnchor=i,0===n?this.source=e:(r.push(e.substring(n,t)),this.source=r.join(""))}else this.hasAnchor=!1,this.source=e;this.hasAnchor?this._anchorCache=this._buildAnchorCache():this._anchorCache=null,this.ruleId=t,"string"==typeof this.source?this.hasBackReferences=H.test(this.source):this.hasBackReferences=!1}clone(){return new e(this.source,this.ruleId)}setSource(e){this.source!==e&&(this.source=e,this.hasAnchor&&(this._anchorCache=this._buildAnchorCache()))}resolveBackReferences(e,t){if("string"!=typeof this.source)throw Error("This method should only be called if the source is a string");let n=t.map(t=>e.substring(t.start,t.end));return G.lastIndex=0,this.source.replace(G,(e,t)=>d(n[parseInt(t,10)]||""))}_buildAnchorCache(){let e,t,n,r;if("string"!=typeof this.source)throw Error("This method should only be called if the source is a string");let i=[],o=[],a=[],l=[];for(e=0,t=this.source.length;e<t;e++)n=this.source.charAt(e),i[e]=n,o[e]=n,a[e]=n,l[e]=n,"\\"===n&&e+1<t&&("A"===(r=this.source.charAt(e+1))?(i[e+1]="￿",o[e+1]="￿",a[e+1]="A",l[e+1]="A"):"G"===r?(i[e+1]="￿",o[e+1]="G",a[e+1]="￿",l[e+1]="G"):(i[e+1]=r,o[e+1]=r,a[e+1]=r,l[e+1]=r),e++);return{A0_G0:i.join(""),A0_G1:o.join(""),A1_G0:a.join(""),A1_G1:l.join("")}}resolveAnchors(e,t){if(!this.hasAnchor||!this._anchorCache||"string"!=typeof this.source)return this.source;if(e)if(t)return this._anchorCache.A1_G1;else return this._anchorCache.A1_G0;return t?this._anchorCache.A0_G1:this._anchorCache.A0_G0}},K=class{_items;_hasAnchors;_cached;_anchorCache;constructor(){this._items=[],this._hasAnchors=!1,this._cached=null,this._anchorCache={A0_G0:null,A0_G1:null,A1_G0:null,A1_G1:null}}dispose(){this._disposeCaches()}_disposeCaches(){this._cached&&(this._cached.dispose(),this._cached=null),this._anchorCache.A0_G0&&(this._anchorCache.A0_G0.dispose(),this._anchorCache.A0_G0=null),this._anchorCache.A0_G1&&(this._anchorCache.A0_G1.dispose(),this._anchorCache.A0_G1=null),this._anchorCache.A1_G0&&(this._anchorCache.A1_G0.dispose(),this._anchorCache.A1_G0=null),this._anchorCache.A1_G1&&(this._anchorCache.A1_G1.dispose(),this._anchorCache.A1_G1=null)}push(e){this._items.push(e),this._hasAnchors=this._hasAnchors||e.hasAnchor}unshift(e){this._items.unshift(e),this._hasAnchors=this._hasAnchors||e.hasAnchor}length(){return this._items.length}setSource(e,t){this._items[e].source!==t&&(this._disposeCaches(),this._items[e].setSource(t))}compile(e){if(!this._cached){let t=this._items.map(e=>e.source);this._cached=new X(e,t,this._items.map(e=>e.ruleId))}return this._cached}compileAG(e,t,n){if(!this._hasAnchors)return this.compile(e);if(t)if(n)return this._anchorCache.A1_G1||(this._anchorCache.A1_G1=this._resolveAnchors(e,t,n)),this._anchorCache.A1_G1;else return this._anchorCache.A1_G0||(this._anchorCache.A1_G0=this._resolveAnchors(e,t,n)),this._anchorCache.A1_G0;return n?(this._anchorCache.A0_G1||(this._anchorCache.A0_G1=this._resolveAnchors(e,t,n)),this._anchorCache.A0_G1):(this._anchorCache.A0_G0||(this._anchorCache.A0_G0=this._resolveAnchors(e,t,n)),this._anchorCache.A0_G0)}_resolveAnchors(e,t,n){return new X(e,this._items.map(e=>e.resolveAnchors(t,n)),this._items.map(e=>e.ruleId))}},X=class{constructor(e,t,n){this.regExps=t,this.rules=n,this.scanner=e.createOnigScanner(t)}scanner;dispose(){"function"==typeof this.scanner.dispose&&this.scanner.dispose()}toString(){let e=[];for(let t=0,n=this.rules.length;t<n;t++)e.push("   - "+this.rules[t]+": "+this.regExps[t]);return e.join("\n")}findNextMatchSync(e,t,n){let r=this.scanner.findNextMatchSync(e,t,n);return r?{ruleId:this.rules[r.index],captureIndices:r.captureIndices}:null}},ee=class{constructor(e,t){this.languageId=e,this.tokenType=t}},et=class e{_defaultAttributes;_embeddedLanguagesMatcher;constructor(e,t){this._defaultAttributes=new ee(e,8),this._embeddedLanguagesMatcher=new en(Object.entries(t||{}))}getDefaultAttributes(){return this._defaultAttributes}getBasicScopeAttributes(t){return null===t?e._NULL_SCOPE_METADATA:this._getBasicScopeAttributes.get(t)}static _NULL_SCOPE_METADATA=new ee(0,0);_getBasicScopeAttributes=new f(e=>new ee(this._scopeToLanguage(e),this._toStandardTokenType(e)));_scopeToLanguage(e){return this._embeddedLanguagesMatcher.match(e)||0}_toStandardTokenType(t){let n=t.match(e.STANDARD_TOKEN_TYPE_REGEXP);if(!n)return 8;switch(n[1]){case"comment":return 1;case"string":return 2;case"regex":return 3;case"meta.embedded":return 0}throw Error("Unexpected match for standard token type!")}static STANDARD_TOKEN_TYPE_REGEXP=/\b(comment|string|regex|meta\.embedded)\b/},en=class{values;scopesRegExp;constructor(e){if(0===e.length)this.values=null,this.scopesRegExp=null;else{this.values=new Map(e);let t=e.map(([e,t])=>d(e));t.sort(),t.reverse(),this.scopesRegExp=RegExp(`^((${t.join(")|(")}))($|\\.)`,"")}}match(e){if(!this.scopesRegExp)return;let t=e.match(this.scopesRegExp);if(t)return this.values.get(t[1])}};void 0!==a&&a.env.VSCODE_TEXTMATE_DEBUG;var er=class{constructor(e,t){this.stack=e,this.stoppedEarly=t}};function ei(e,t,n,r,i,o,a,l){let s=t.content.length,c=!1,u=-1;if(a){let a=function(e,t,n,r,i,o){let a=i.beginRuleCapturedEOL?0:-1,l=[];for(let t=i;t;t=t.pop()){let n=t.getRule(e);n instanceof J&&l.push({rule:n,stack:t})}for(let d=l.pop();d;d=l.pop()){var s,c,u,h,p;let{ruleScanner:l,findOptions:f}=(s=d.rule,c=e,u=d.stack.endRule,h=n,p=r===a,{ruleScanner:s.compileWhileAG(c,u,h,p),findOptions:0}),m=l.findNextMatchSync(t,r,f);if(m){if(-2!==m.ruleId){i=d.stack.pop();break}m.captureIndices&&m.captureIndices.length&&(o.produce(d.stack,m.captureIndices[0].start),ea(e,t,n,d.stack,o,d.rule.whileCaptures,m.captureIndices),o.produce(d.stack,m.captureIndices[0].end),a=m.captureIndices[0].end,m.captureIndices[0].end>r&&(r=m.captureIndices[0].end,n=!1))}else{i=d.stack.pop();break}}return{stack:i,linePos:r,anchorPosition:a,isFirstLine:n}}(e,t,n,r,i,o);i=a.stack,r=a.linePos,n=a.isFirstLine,u=a.anchorPosition}let h=Date.now();for(;!c;){if(0!==l&&Date.now()-h>l)return new er(i,!0);!function(){let a=function(e,t,n,r,i,o){let a=function(e,t,n,r,i,o){let{ruleScanner:a,findOptions:l}=eo(i.getRule(e),e,i.endRule,n,r===o),s=a.findNextMatchSync(t,r,l);return s?{captureIndices:s.captureIndices,matchedRuleId:s.ruleId}:null}(e,t,n,r,i,o),l=e.getInjections();if(0===l.length)return a;let s=function(e,t,n,r,i,o,a){let l,s=Number.MAX_VALUE,c=null,u=0,h=o.contentNameScopesList.getScopeNames();for(let o=0,p=e.length;o<p;o++){let p=e[o];if(!p.matcher(h))continue;let{ruleScanner:d,findOptions:f}=eo(t.getRule(p.ruleId),t,null,r,i===a),m=d.findNextMatchSync(n,i,f);if(!m)continue;let g=m.captureIndices[0].start;if(!(g>=s)&&(s=g,c=m.captureIndices,l=m.ruleId,u=p.priority,s===i))break}return c?{priorityMatch:-1===u,captureIndices:c,matchedRuleId:l}:null}(l,e,t,n,r,i,o);if(!s)return a;if(!a)return s;let c=a.captureIndices[0].start,u=s.captureIndices[0].start;return u<c||s.priorityMatch&&u===c?s:a}(e,t,n,r,i,u);if(!a){o.produce(i,s),c=!0;return}let l=a.captureIndices,h=a.matchedRuleId,p=!!l&&l.length>0&&l[0].end>r;if(-1===h){let a=i.getRule(e);o.produce(i,l[0].start),i=i.withContentNameScopesList(i.nameScopesList),ea(e,t,n,i,o,a.endCaptures,l),o.produce(i,l[0].end);let h=i;if(i=i.parent,u=h.getAnchorPos(),!p&&h.getEnterPos()===r){i=h,o.produce(i,s),c=!0;return}}else{let a=e.getRule(h);o.produce(i,l[0].start);let d=i,f=a.getName(t.content,l),m=i.contentNameScopesList.pushAttributed(f,e);if(i=i.push(h,r,u,l[0].end===s,null,m,m),a instanceof Q){ea(e,t,n,i,o,a.beginCaptures,l),o.produce(i,l[0].end),u=l[0].end;let r=a.getContentName(t.content,l),h=m.pushAttributed(r,e);if(i=i.withContentNameScopesList(h),a.endHasBackReferences&&(i=i.withEndRule(a.getEndWithResolvedBackReferences(t.content,l))),!p&&d.hasSameRuleAs(i)){i=i.pop(),o.produce(i,s),c=!0;return}}else if(a instanceof J){ea(e,t,n,i,o,a.beginCaptures,l),o.produce(i,l[0].end),u=l[0].end;let r=a.getContentName(t.content,l),h=m.pushAttributed(r,e);if(i=i.withContentNameScopesList(h),a.whileHasBackReferences&&(i=i.withEndRule(a.getWhileWithResolvedBackReferences(t.content,l))),!p&&d.hasSameRuleAs(i)){i=i.pop(),o.produce(i,s),c=!0;return}}else if(ea(e,t,n,i,o,a.captures,l),o.produce(i,l[0].end),i=i.pop(),!p){i=i.safePop(),o.produce(i,s),c=!0;return}}l[0].end>r&&(r=l[0].end,n=!1)}()}return new er(i,!1)}function eo(e,t,n,r,i){return{ruleScanner:e.compileAG(t,n,r,i),findOptions:0}}function ea(e,t,n,r,i,o,a){if(0===o.length)return;let l=t.content,s=Math.min(o.length,a.length),c=[],u=a[0].end;for(let t=0;t<s;t++){let s=o[t];if(null===s)continue;let h=a[t];if(0===h.length)continue;if(h.start>u)break;for(;c.length>0&&c[c.length-1].endPos<=h.start;)i.produceFromScopes(c[c.length-1].scopes,c[c.length-1].endPos),c.pop();if(c.length>0?i.produceFromScopes(c[c.length-1].scopes,h.start):i.produce(r,h.start),s.retokenizeCapturedWithRuleId){let t=s.getName(l,a),o=r.contentNameScopesList.pushAttributed(t,e),c=s.getContentName(l,a),u=o.pushAttributed(c,e),p=r.push(s.retokenizeCapturedWithRuleId,h.start,-1,!1,null,o,u),d=e.createOnigString(l.substring(0,h.end));ei(e,d,n&&0===h.start,h.start,p,i,!1,0),T(d);continue}let p=s.getName(l,a);if(null!==p){let t=(c.length>0?c[c.length-1].scopes:r.contentNameScopesList).pushAttributed(p,e);c.push(new el(t,h.end))}}for(;c.length>0;)i.produceFromScopes(c[c.length-1].scopes,c[c.length-1].endPos),c.pop()}var el=class{scopes;endPos;constructor(e,t){this.scopes=e,this.endPos=t}};function es(e,t,n,r,i){let o=C(t,ec),a=Y.getCompiledRuleId(n,r,i.repository);for(let n of o)e.push({debugSelector:t,matcher:n.matcher,ruleId:a,grammar:i,priority:n.priority})}function ec(e,t){if(t.length<e.length)return!1;let n=0;return e.every(e=>{for(let r=n;r<t.length;r++)if(function(e,t){if(!e)return!1;if(e===t)return!0;let n=t.length;return e.length>n&&e.substr(0,n)===t&&"."===e[n]}(t[r],e))return n=r+1,!0;return!1})}var eu=class{constructor(e,t,n,r,i,o,a,l){if(this._rootScopeName=e,this.balancedBracketSelectors=o,this._onigLib=l,this._basicScopeAttributesProvider=new et(n,r),this._rootId=-1,this._lastRuleId=0,this._ruleId2desc=[null],this._includedGrammars={},this._grammarRepository=a,this._grammar=eh(t,null),this._injections=null,this._tokenTypeMatchers=[],i)for(let e of Object.keys(i))for(let t of C(e,ec))this._tokenTypeMatchers.push({matcher:t.matcher,type:i[e]})}_rootId;_lastRuleId;_ruleId2desc;_includedGrammars;_grammarRepository;_grammar;_injections;_basicScopeAttributesProvider;_tokenTypeMatchers;get themeProvider(){return this._grammarRepository}dispose(){for(let e of this._ruleId2desc)e&&e.dispose()}createOnigScanner(e){return this._onigLib.createOnigScanner(e)}createOnigString(e){return this._onigLib.createOnigString(e)}getMetadataForScope(e){return this._basicScopeAttributesProvider.getBasicScopeAttributes(e)}_collectInjections(){let e=[],t=this._rootScopeName,n=({lookup:e=>e===this._rootScopeName?this._grammar:this.getExternalGrammar(e),injections:e=>this._grammarRepository.injections(e)}).lookup(t);if(n){let r=n.injections;if(r)for(let t in r)es(e,t,r[t],this,n);let i=this._grammarRepository.injections(t);i&&i.forEach(t=>{let n=this.getExternalGrammar(t);if(n){let t=n.injectionSelector;t&&es(e,t,n,this,n)}})}return e.sort((e,t)=>e.priority-t.priority),e}getInjections(){return null===this._injections&&(this._injections=this._collectInjections()),this._injections}registerRule(e){let t=++this._lastRuleId,n=e(t);return this._ruleId2desc[t]=n,n}getRule(e){return this._ruleId2desc[e]}getExternalGrammar(e,t){if(this._includedGrammars[e])return this._includedGrammars[e];if(this._grammarRepository){let n=this._grammarRepository.lookup(e);if(n)return this._includedGrammars[e]=eh(n,t&&t.$base),this._includedGrammars[e]}}tokenizeLine(e,t,n=0){let r=this._tokenize(e,t,!1,n);return{tokens:r.lineTokens.getResult(r.ruleStack,r.lineLength),ruleStack:r.ruleStack,stoppedEarly:r.stoppedEarly}}tokenizeLine2(e,t,n=0){let r=this._tokenize(e,t,!0,n);return{tokens:r.lineTokens.getBinaryResult(r.ruleStack,r.lineLength),ruleStack:r.ruleStack,stoppedEarly:r.stoppedEarly}}_tokenize(e,t,n,r){let i;if(-1===this._rootId&&(this._rootId=Y.getCompiledRuleId(this._grammar.repository.$self,this,this._grammar.repository),this.getInjections()),t&&t!==ed.NULL)i=!1,t.reset();else{let e;i=!0;let n=this._basicScopeAttributesProvider.getDefaultAttributes(),r=this.themeProvider.getDefaults(),o=_.set(0,n.languageId,n.tokenType,null,r.fontStyle,r.foregroundId,r.backgroundId),a=this.getRule(this._rootId).getName(null,null);e=a?ep.createRootAndLookUpScopeName(a,o,this):ep.createRoot("unknown",o),t=new ed(null,this._rootId,-1,-1,!1,null,e,e)}e+="\n";let o=this.createOnigString(e),a=o.content.length,l=new em(n,e,this._tokenTypeMatchers,this.balancedBracketSelectors),s=ei(this,o,i,0,t,l,!0,r);return T(o),{lineLength:a,lineTokens:l,ruleStack:s.stack,stoppedEarly:s.stoppedEarly}}};function eh(e,t){return(e=function e(t){return Array.isArray(t)?function(t){let n=[];for(let r=0,i=t.length;r<i;r++)n[r]=e(t[r]);return n}(t):t instanceof RegExp?t:"object"==typeof t?function(t){let n={};for(let r in t)n[r]=e(t[r]);return n}(t):t}(e)).repository=e.repository||{},e.repository.$self={$vscodeTextmateLocation:e.$vscodeTextmateLocation,patterns:e.patterns,name:e.scopeName},e.repository.$base=t||e.repository.$self,e}var ep=class e{constructor(e,t,n){this.parent=e,this.scopePath=t,this.tokenAttributes=n}static fromExtension(t,n){let r=t,i=t?.scopePath??null;for(let t of n)r=new e(r,i=g.push(i,t.scopeNames),t.encodedTokenAttributes);return r}static createRoot(t,n){return new e(null,new g(null,t),n)}static createRootAndLookUpScopeName(t,n,r){let i=r.getMetadataForScope(t),o=new g(null,t),a=r.themeProvider.themeMatch(o),l=e.mergeAttributes(n,i,a);return new e(null,o,l)}get scopeName(){return this.scopePath.scopeName}toString(){return this.getScopeNames().join(" ")}equals(t){return e.equals(this,t)}static equals(e,t){for(;;){if(e===t||!e&&!t)return!0;if(!e||!t||e.scopeName!==t.scopeName||e.tokenAttributes!==t.tokenAttributes)return!1;e=e.parent,t=t.parent}}static mergeAttributes(e,t,n){let r=-1,i=0,o=0;return null!==n&&(r=n.fontStyle,i=n.foregroundId,o=n.backgroundId),_.set(e,t.languageId,t.tokenType,null,r,i,o)}pushAttributed(t,n){if(null===t)return this;if(-1===t.indexOf(" "))return e._pushAttributed(this,t,n);let r=t.split(/ /g),i=this;for(let t of r)i=e._pushAttributed(i,t,n);return i}static _pushAttributed(t,n,r){let i=r.getMetadataForScope(n),o=t.scopePath.push(n),a=r.themeProvider.themeMatch(o),l=e.mergeAttributes(t.tokenAttributes,i,a);return new e(t,o,l)}getScopeNames(){return this.scopePath.getSegments()}getExtensionIfDefined(e){let t=[],n=this;for(;n&&n!==e;)t.push({encodedTokenAttributes:n.tokenAttributes,scopeNames:n.scopePath.getExtensionIfDefined(n.parent?.scopePath??null)}),n=n.parent;return n===e?t.reverse():void 0}},ed=class e{constructor(e,t,n,r,i,o,a,l){this.parent=e,this.ruleId=t,this.beginRuleCapturedEOL=i,this.endRule=o,this.nameScopesList=a,this.contentNameScopesList=l,this.depth=this.parent?this.parent.depth+1:1,this._enterPos=n,this._anchorPos=r}_stackElementBrand=void 0;static NULL=new e(null,0,0,0,!1,null,null,null);_enterPos;_anchorPos;depth;equals(t){return null!==t&&e._equals(this,t)}static _equals(e,t){return e===t||!!this._structuralEquals(e,t)&&ep.equals(e.contentNameScopesList,t.contentNameScopesList)}static _structuralEquals(e,t){for(;;){if(e===t||!e&&!t)return!0;if(!e||!t||e.depth!==t.depth||e.ruleId!==t.ruleId||e.endRule!==t.endRule)return!1;e=e.parent,t=t.parent}}clone(){return this}static _reset(e){for(;e;)e._enterPos=-1,e._anchorPos=-1,e=e.parent}reset(){e._reset(this)}pop(){return this.parent}safePop(){return this.parent?this.parent:this}push(t,n,r,i,o,a,l){return new e(this,t,n,r,i,o,a,l)}getEnterPos(){return this._enterPos}getAnchorPos(){return this._anchorPos}getRule(e){return e.getRule(this.ruleId)}toString(){let e=[];return this._writeString(e,0),"["+e.join(",")+"]"}_writeString(e,t){return this.parent&&(t=this.parent._writeString(e,t)),e[t++]=`(${this.ruleId}, ${this.nameScopesList?.toString()}, ${this.contentNameScopesList?.toString()})`,t}withContentNameScopesList(e){return this.contentNameScopesList===e?this:this.parent.push(this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,this.endRule,this.nameScopesList,e)}withEndRule(t){return this.endRule===t?this:new e(this.parent,this.ruleId,this._enterPos,this._anchorPos,this.beginRuleCapturedEOL,t,this.nameScopesList,this.contentNameScopesList)}hasSameRuleAs(e){let t=this;for(;t&&t._enterPos===e._enterPos;){if(t.ruleId===e.ruleId)return!0;t=t.parent}return!1}toStateStackFrame(){return{ruleId:this.ruleId,beginRuleCapturedEOL:this.beginRuleCapturedEOL,endRule:this.endRule,nameScopesList:this.nameScopesList?.getExtensionIfDefined(this.parent?.nameScopesList??null)??[],contentNameScopesList:this.contentNameScopesList?.getExtensionIfDefined(this.nameScopesList)??[]}}static pushFrame(t,n){let r=ep.fromExtension(t?.nameScopesList??null,n.nameScopesList);return new e(t,n.ruleId,n.enterPos??-1,n.anchorPos??-1,n.beginRuleCapturedEOL,n.endRule,r,ep.fromExtension(r,n.contentNameScopesList))}},ef=class{balancedBracketScopes;unbalancedBracketScopes;allowAny=!1;constructor(e,t){this.balancedBracketScopes=e.flatMap(e=>"*"===e?(this.allowAny=!0,[]):C(e,ec).map(e=>e.matcher)),this.unbalancedBracketScopes=t.flatMap(e=>C(e,ec).map(e=>e.matcher))}get matchesAlways(){return this.allowAny&&0===this.unbalancedBracketScopes.length}get matchesNever(){return 0===this.balancedBracketScopes.length&&!this.allowAny}match(e){for(let t of this.unbalancedBracketScopes)if(t(e))return!1;for(let t of this.balancedBracketScopes)if(t(e))return!0;return this.allowAny}},em=class{constructor(e,t,n,r){this.balancedBracketSelectors=r,this._emitBinaryTokens=e,this._tokenTypeOverrides=n,this._lineText=null,this._tokens=[],this._binaryTokens=[],this._lastTokenEndIndex=0}_emitBinaryTokens;_lineText;_tokens;_binaryTokens;_lastTokenEndIndex;_tokenTypeOverrides;produce(e,t){this.produceFromScopes(e.contentNameScopesList,t)}produceFromScopes(e,t){if(this._lastTokenEndIndex>=t)return;if(this._emitBinaryTokens){let n=e?.tokenAttributes??0,r=!1;if(this.balancedBracketSelectors?.matchesAlways&&(r=!0),this._tokenTypeOverrides.length>0||this.balancedBracketSelectors&&!this.balancedBracketSelectors.matchesAlways&&!this.balancedBracketSelectors.matchesNever){let t=e?.getScopeNames()??[];for(let e of this._tokenTypeOverrides)e.matcher(t)&&(n=_.set(n,0,e.type,null,-1,0,0));this.balancedBracketSelectors&&(r=this.balancedBracketSelectors.match(t))}if(r&&(n=_.set(n,0,8,r,-1,0,0)),this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-1]===n){this._lastTokenEndIndex=t;return}this._binaryTokens.push(this._lastTokenEndIndex),this._binaryTokens.push(n),this._lastTokenEndIndex=t;return}let n=e?.getScopeNames()??[];this._tokens.push({startIndex:this._lastTokenEndIndex,endIndex:t,scopes:n}),this._lastTokenEndIndex=t}getResult(e,t){return this._tokens.length>0&&this._tokens[this._tokens.length-1].startIndex===t-1&&this._tokens.pop(),0===this._tokens.length&&(this._lastTokenEndIndex=-1,this.produce(e,t),this._tokens[this._tokens.length-1].startIndex=0),this._tokens}getBinaryResult(e,t){this._binaryTokens.length>0&&this._binaryTokens[this._binaryTokens.length-2]===t-1&&(this._binaryTokens.pop(),this._binaryTokens.pop()),0===this._binaryTokens.length&&(this._lastTokenEndIndex=-1,this.produce(e,t),this._binaryTokens[this._binaryTokens.length-2]=0);let n=new Uint32Array(this._binaryTokens.length);for(let e=0,t=this._binaryTokens.length;e<t;e++)n[e]=this._binaryTokens[e];return n}},eg=class{constructor(e,t){this._onigLib=t,this._theme=e}_grammars=new Map;_rawGrammars=new Map;_injectionGrammars=new Map;_theme;dispose(){for(let e of this._grammars.values())e.dispose()}setTheme(e){this._theme=e}getColorMap(){return this._theme.getColorMap()}addGrammar(e,t){this._rawGrammars.set(e.scopeName,e),t&&this._injectionGrammars.set(e.scopeName,t)}lookup(e){return this._rawGrammars.get(e)}injections(e){return this._injectionGrammars.get(e)}getDefaults(){return this._theme.getDefaults()}themeMatch(e){return this._theme.match(e)}grammarForScopeName(e,t,n,r,i){if(!this._grammars.has(e)){let o=this._rawGrammars.get(e);if(!o)return null;this._grammars.set(e,new eu(e,o,t,n,r,i,this,this._onigLib))}return this._grammars.get(e)}},eb=class{_options;_syncRegistry;_ensureGrammarCache;constructor(e){this._options=e,this._syncRegistry=new eg(m.createFromRawTheme(e.theme,e.colorMap),e.onigLib),this._ensureGrammarCache=new Map}dispose(){this._syncRegistry.dispose()}setTheme(e,t){this._syncRegistry.setTheme(m.createFromRawTheme(e,t))}getColorMap(){return this._syncRegistry.getColorMap()}loadGrammarWithEmbeddedLanguages(e,t,n){return this.loadGrammarWithConfiguration(e,t,{embeddedLanguages:n})}loadGrammarWithConfiguration(e,t,n){return this._loadGrammar(e,t,n.embeddedLanguages,n.tokenTypes,new ef(n.balancedBracketSelectors||[],n.unbalancedBracketSelectors||[]))}loadGrammar(e){return this._loadGrammar(e,0,null,null,null)}_loadGrammar(e,t,n,r,i){let o=new I(this._syncRegistry,e);for(;o.Q.length>0;)o.Q.map(e=>this._loadSingleGrammar(e.scopeName)),o.processQueue();return this._grammarForScopeName(e,t,n,r,i)}_loadSingleGrammar(e){this._ensureGrammarCache.has(e)||(this._doLoadSingleGrammar(e),this._ensureGrammarCache.set(e,!0))}_doLoadSingleGrammar(e){let t=this._options.loadGrammar(e);if(t){let n="function"==typeof this._options.getInjections?this._options.getInjections(e):void 0;this._syncRegistry.addGrammar(t,n)}}addGrammar(e,t=[],n=0,r=null){return this._syncRegistry.addGrammar(e,t),this._grammarForScopeName(e.scopeName,n,r)}_grammarForScopeName(e,t=0,n=null,r=null,i=null){return this._syncRegistry.grammarForScopeName(e,t,n,r,i)}},ey=ed.NULL,ek=n(8978),ex=n(83846),ev=n(18995);let ew=/["&'<>`]/g,eS=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,e_=/[\x01-\t\v\f\x0E-\x1F\x7F\x81\x8D\x8F\x90\x9D\xA0-\uFFFF]/g,eC=/[|\\{}()[\]^$+*?.]/g,eP=new WeakMap,eA=/[\dA-Fa-f]/,eT=/\d/,eN=["AElig","AMP","Aacute","Acirc","Agrave","Aring","Atilde","Auml","COPY","Ccedil","ETH","Eacute","Ecirc","Egrave","Euml","GT","Iacute","Icirc","Igrave","Iuml","LT","Ntilde","Oacute","Ocirc","Ograve","Oslash","Otilde","Ouml","QUOT","REG","THORN","Uacute","Ucirc","Ugrave","Uuml","Yacute","aacute","acirc","acute","aelig","agrave","amp","aring","atilde","auml","brvbar","ccedil","cedil","cent","copy","curren","deg","divide","eacute","ecirc","egrave","eth","euml","frac12","frac14","frac34","gt","iacute","icirc","iexcl","igrave","iquest","iuml","laquo","lt","macr","micro","middot","nbsp","not","ntilde","oacute","ocirc","ograve","ordf","ordm","oslash","otilde","ouml","para","plusmn","pound","quot","raquo","reg","sect","shy","sup1","sup2","sup3","szlig","thorn","times","uacute","ucirc","ugrave","uml","uuml","yacute","yen","yuml"],eE={nbsp:"\xa0",iexcl:"\xa1",cent:"\xa2",pound:"\xa3",curren:"\xa4",yen:"\xa5",brvbar:"\xa6",sect:"\xa7",uml:"\xa8",copy:"\xa9",ordf:"\xaa",laquo:"\xab",not:"\xac",shy:"\xad",reg:"\xae",macr:"\xaf",deg:"\xb0",plusmn:"\xb1",sup2:"\xb2",sup3:"\xb3",acute:"\xb4",micro:"\xb5",para:"\xb6",middot:"\xb7",cedil:"\xb8",sup1:"\xb9",ordm:"\xba",raquo:"\xbb",frac14:"\xbc",frac12:"\xbd",frac34:"\xbe",iquest:"\xbf",Agrave:"\xc0",Aacute:"\xc1",Acirc:"\xc2",Atilde:"\xc3",Auml:"\xc4",Aring:"\xc5",AElig:"\xc6",Ccedil:"\xc7",Egrave:"\xc8",Eacute:"\xc9",Ecirc:"\xca",Euml:"\xcb",Igrave:"\xcc",Iacute:"\xcd",Icirc:"\xce",Iuml:"\xcf",ETH:"\xd0",Ntilde:"\xd1",Ograve:"\xd2",Oacute:"\xd3",Ocirc:"\xd4",Otilde:"\xd5",Ouml:"\xd6",times:"\xd7",Oslash:"\xd8",Ugrave:"\xd9",Uacute:"\xda",Ucirc:"\xdb",Uuml:"\xdc",Yacute:"\xdd",THORN:"\xde",szlig:"\xdf",agrave:"\xe0",aacute:"\xe1",acirc:"\xe2",atilde:"\xe3",auml:"\xe4",aring:"\xe5",aelig:"\xe6",ccedil:"\xe7",egrave:"\xe8",eacute:"\xe9",ecirc:"\xea",euml:"\xeb",igrave:"\xec",iacute:"\xed",icirc:"\xee",iuml:"\xef",eth:"\xf0",ntilde:"\xf1",ograve:"\xf2",oacute:"\xf3",ocirc:"\xf4",otilde:"\xf5",ouml:"\xf6",divide:"\xf7",oslash:"\xf8",ugrave:"\xf9",uacute:"\xfa",ucirc:"\xfb",uuml:"\xfc",yacute:"\xfd",thorn:"\xfe",yuml:"\xff",fnof:"ƒ",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",bull:"•",hellip:"…",prime:"′",Prime:"″",oline:"‾",frasl:"⁄",weierp:"℘",image:"ℑ",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦",quot:'"',amp:"&",lt:"<",gt:">",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",circ:"ˆ",tilde:"˜",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",permil:"‰",lsaquo:"‹",rsaquo:"›",euro:"€"},eL=["cent","copy","divide","gt","lt","not","para","times"],eI={}.hasOwnProperty,eR={};for(r in eE)eI.call(eE,r)&&(eR[eE[r]]=r);let eO=/[^\dA-Za-z]/;function eD(e,t,n){let r,i=function(e,t,n){let r="&#x"+e.toString(16).toUpperCase();return n&&t&&!eA.test(String.fromCharCode(t))?r:r+";"}(e,t,n.omitOptionalSemicolons);if((n.useNamedReferences||n.useShortestReferences)&&(r=function(e,t,n,r){let i=String.fromCharCode(e);if(eI.call(eR,i)){let e=eR[i],o="&"+e;return n&&eN.includes(e)&&!eL.includes(e)&&(!r||t&&61!==t&&eO.test(String.fromCharCode(t)))?o:o+";"}return""}(e,t,n.omitOptionalSemicolons,n.attribute)),(n.useShortestReferences||!r)&&n.useShortestReferences){let r=function(e,t,n){let r="&#"+String(e);return n&&t&&!eT.test(String.fromCharCode(t))?r:r+";"}(e,t,n.omitOptionalSemicolons);r.length<i.length&&(i=r)}return r&&(!n.useShortestReferences||r.length<i.length)?r:i}function eM(e,t){let n;var r,i=e,o=Object.assign({format:eD},t);if(i=i.replace(o.subset?(r=o.subset,(n=eP.get(r))||(n=function(e){let t=[],n=-1;for(;++n<e.length;)t.push(e[n].replace(eC,"\\$&"));return RegExp("(?:"+t.join("|")+")","g")}(r),eP.set(r,n)),n):ew,a),o.subset||o.escapeOnly)return i;return i.replace(eS,function(e,t,n){return o.format((e.charCodeAt(0)-55296)*1024+e.charCodeAt(1)-56320+65536,n.charCodeAt(t+2),o)}).replace(e_,a);function a(e,t,n){return o.format(e.charCodeAt(0),n.charCodeAt(t+1),o)}}let ez=/^>|^->|<!--|-->|--!>|<!-$/g,eB=[">"],eF=["<",">"];var ej=n(41974),e$=n(55548),eH=n(7887),eG=n(14947),eU=n(23145);let eq=eQ(1),eW=eQ(-1),eV=[];function eQ(e){return function(t,n,r){let i=t?t.children:eV,o=(n||0)+e,a=i[o];if(!r)for(;a&&(0,eU.m)(a);)o+=e,a=i[o];return a}}let eJ={}.hasOwnProperty;function eY(e){return function(t,n,r){return eJ.call(e,t.tagName)&&e[t.tagName](t,n,r)}}let eZ=eY({body:function(e,t,n){let r=eq(n,t);return!r||"comment"!==r.type},caption:eK,colgroup:eK,dd:function(e,t,n){let r=eq(n,t);return!r||"element"===r.type&&("dt"===r.tagName||"dd"===r.tagName)},dt:function(e,t,n){let r=eq(n,t);return!!(r&&"element"===r.type&&("dt"===r.tagName||"dd"===r.tagName))},head:eK,html:function(e,t,n){let r=eq(n,t);return!r||"comment"!==r.type},li:function(e,t,n){let r=eq(n,t);return!r||"element"===r.type&&"li"===r.tagName},optgroup:function(e,t,n){let r=eq(n,t);return!r||"element"===r.type&&"optgroup"===r.tagName},option:function(e,t,n){let r=eq(n,t);return!r||"element"===r.type&&("option"===r.tagName||"optgroup"===r.tagName)},p:function(e,t,n){let r=eq(n,t);return r?"element"===r.type&&("address"===r.tagName||"article"===r.tagName||"aside"===r.tagName||"blockquote"===r.tagName||"details"===r.tagName||"div"===r.tagName||"dl"===r.tagName||"fieldset"===r.tagName||"figcaption"===r.tagName||"figure"===r.tagName||"footer"===r.tagName||"form"===r.tagName||"h1"===r.tagName||"h2"===r.tagName||"h3"===r.tagName||"h4"===r.tagName||"h5"===r.tagName||"h6"===r.tagName||"header"===r.tagName||"hgroup"===r.tagName||"hr"===r.tagName||"main"===r.tagName||"menu"===r.tagName||"nav"===r.tagName||"ol"===r.tagName||"p"===r.tagName||"pre"===r.tagName||"section"===r.tagName||"table"===r.tagName||"ul"===r.tagName):!n||"element"!==n.type||"a"!==n.tagName&&"audio"!==n.tagName&&"del"!==n.tagName&&"ins"!==n.tagName&&"map"!==n.tagName&&"noscript"!==n.tagName&&"video"!==n.tagName},rp:eX,rt:eX,tbody:function(e,t,n){let r=eq(n,t);return!r||"element"===r.type&&("tbody"===r.tagName||"tfoot"===r.tagName)},td:e0,tfoot:function(e,t,n){return!eq(n,t)},th:e0,thead:function(e,t,n){let r=eq(n,t);return!!(r&&"element"===r.type&&("tbody"===r.tagName||"tfoot"===r.tagName))},tr:function(e,t,n){let r=eq(n,t);return!r||"element"===r.type&&"tr"===r.tagName}});function eK(e,t,n){let r=eq(n,t,!0);return!r||"comment"!==r.type&&!("text"===r.type&&(0,eU.m)(r.value.charAt(0)))}function eX(e,t,n){let r=eq(n,t);return!r||"element"===r.type&&("rp"===r.tagName||"rt"===r.tagName)}function e0(e,t,n){let r=eq(n,t);return!r||"element"===r.type&&("td"===r.tagName||"th"===r.tagName)}let e1=eY({body:function(e){let t=eq(e,-1,!0);return!t||"comment"!==t.type&&!("text"===t.type&&(0,eU.m)(t.value.charAt(0)))&&("element"!==t.type||"meta"!==t.tagName&&"link"!==t.tagName&&"script"!==t.tagName&&"style"!==t.tagName&&"template"!==t.tagName)},colgroup:function(e,t,n){let r=eW(n,t),i=eq(e,-1,!0);return!(n&&r&&"element"===r.type&&"colgroup"===r.tagName&&eZ(r,n.children.indexOf(r),n))&&!!(i&&"element"===i.type&&"col"===i.tagName)},head:function(e){let t=new Set;for(let n of e.children)if("element"===n.type&&("base"===n.tagName||"title"===n.tagName)){if(t.has(n.tagName))return!1;t.add(n.tagName)}let n=e.children[0];return!n||"element"===n.type},html:function(e){let t=eq(e,-1);return!t||"comment"!==t.type},tbody:function(e,t,n){let r=eW(n,t),i=eq(e,-1);return!(n&&r&&"element"===r.type&&("thead"===r.tagName||"tbody"===r.tagName)&&eZ(r,n.children.indexOf(r),n))&&!!(i&&"element"===i.type&&"tr"===i.tagName)}}),e2={name:[["	\n\f\r &/=>".split(""),"	\n\f\r \"&'/=>`".split("")],["\0	\n\f\r \"&'/<=>".split(""),"\0	\n\f\r \"&'/<=>`".split("")]],unquoted:[["	\n\f\r &>".split(""),"\0	\n\f\r \"&'<=>`".split("")],["\0	\n\f\r \"&'<=>`".split(""),"\0	\n\f\r \"&'<=>`".split("")]],single:[["&'".split(""),"\"&'`".split("")],["\0&'".split(""),"\0\"&'`".split("")]],double:[['"&'.split(""),"\"&'`".split("")],['\0"&'.split(""),"\0\"&'`".split("")]]},e3=["<","&"];function e4(e,t,n,r){return n&&"element"===n.type&&("script"===n.tagName||"style"===n.tagName)?e.value:eM(e.value,Object.assign({},r.settings.characterReferences,{subset:e3}))}let e5=(0,ev.A)("type",{invalid:function(e){throw Error("Expected node, not `"+e+"`")},unknown:function(e){throw Error("Cannot compile unknown node `"+e.type+"`")},handlers:{comment:function(e,t,n,r){return r.settings.bogusComments?"<?"+eM(e.value,Object.assign({},r.settings.characterReferences,{subset:eB}))+">":"\x3c!--"+e.value.replace(ez,function(e){return eM(e,Object.assign({},r.settings.characterReferences,{subset:eF}))})+"--\x3e"},doctype:function(e,t,n,r){return"<!"+(r.settings.upperDoctype?"DOCTYPE":"doctype")+(r.settings.tightDoctype?"":" ")+"html>"},element:function(e,t,n,r){let i,o=r.schema,a="svg"!==o.space&&r.settings.omitOptionalTags,l="svg"===o.space?r.settings.closeEmptyElements:r.settings.voids.includes(e.tagName.toLowerCase()),s=[];"html"===o.space&&"svg"===e.tagName&&(r.schema=ex.JW);let c=function(e,t){let n,r=[],i=-1;if(t){for(n in t)if(null!==t[n]&&void 0!==t[n]){let i=function(e,t,n){let r,i=(0,eH.I)(e.schema,t),o=e.settings.allowParseErrors&&"html"===e.schema.space?0:1,a=+!e.settings.allowDangerousCharacters,l=e.quote;if(i.overloadedBoolean&&(n===i.attribute||""===n)?n=!0:(i.boolean||i.overloadedBoolean)&&("string"!=typeof n||n===i.attribute||""===n)&&(n=!!n),null==n||!1===n||"number"==typeof n&&Number.isNaN(n))return"";let s=eM(i.attribute,Object.assign({},e.settings.characterReferences,{subset:e2.name[o][a]}));return!0===n||(n=Array.isArray(n)?(i.commaSeparated?e$.A:eG.A)(n,{padLeft:!e.settings.tightCommaSeparatedLists}):String(n),e.settings.collapseEmptyAttributes&&!n)?s:(e.settings.preferUnquoted&&(r=eM(n,Object.assign({},e.settings.characterReferences,{attribute:!0,subset:e2.unquoted[o][a]}))),r!==n&&(e.settings.quoteSmart&&(0,ej.D)(n,l)>(0,ej.D)(n,e.alternative)&&(l=e.alternative),r=l+eM(n,Object.assign({},e.settings.characterReferences,{subset:("'"===l?e2.single:e2.double)[o][a],attribute:!0}))+l),s+(r?"="+r:r))}(e,n,t[n]);i&&r.push(i)}}for(;++i<r.length;){let t=e.settings.tightAttributes?r[i].charAt(r[i].length-1):void 0;i!==r.length-1&&'"'!==t&&"'"!==t&&(r[i]+=" ")}return r.join("")}(r,e.properties),u=r.all("html"===o.space&&"template"===e.tagName?e.content:e);return r.schema=o,u&&(l=!1),!c&&a&&e1(e,t,n)||(s.push("<",e.tagName,c?" "+c:""),l&&("svg"===o.space||r.settings.closeSelfClosing)&&(i=c.charAt(c.length-1),(!r.settings.tightSelfClosing||"/"===i||i&&'"'!==i&&"'"!==i)&&s.push(" "),s.push("/")),s.push(">")),s.push(u),l||a&&eZ(e,t,n)||s.push("</"+e.tagName+">"),s.join("")},raw:function(e,t,n,r){return r.settings.allowDangerousHtml?e.value:e4(e,t,n,r)},root:function(e,t,n,r){return r.all(e)},text:e4}}),e6={},e9={},e7=[];function e8(e,t,n){return e5(e,t,n,this)}function te(e){let t=[],n=e&&e.children||e7,r=-1;for(;++r<n.length;)t[r]=this.one(n[r],r,e);return t.join("")}function tt(e,t){let n="string"==typeof e?{}:{...e.colorReplacements},r="string"==typeof e?e:e.name;for(let[e,i]of Object.entries(t?.colorReplacements||{}))"string"==typeof i?n[e]=i:e===r&&Object.assign(n,i);return n}function tn(e,t){return e&&t?.[e?.toLowerCase()]||e}async function tr(e){return Promise.resolve("function"==typeof e?e():e).then(e=>e.default||e)}function ti(e){return!e||["plaintext","txt","text","plain"].includes(e)}function to(e){return"ansi"===e||ti(e)}function ta(e){return"none"===e}function tl(e,t){if(!t)return e;for(let n of(e.properties||={},e.properties.class||=[],"string"==typeof e.properties.class&&(e.properties.class=e.properties.class.split(/\s+/g)),Array.isArray(e.properties.class)||(e.properties.class=[]),Array.isArray(t)?t:t.split(/\s+/g)))n&&!e.properties.class.includes(n)&&e.properties.class.push(n);return e}function ts(e,t=!1){let n=e.split(/(\r?\n)/g),r=0,i=[];for(let e=0;e<n.length;e+=2){let o=t?n[e]+(n[e+1]||""):n[e];i.push([o,r]),r+=n[e].length,r+=n[e+1]?.length||0}return i}function tc(e){let t={};if(e.color&&(t.color=e.color),e.bgColor&&(t["background-color"]=e.bgColor),e.fontStyle){e.fontStyle&k.Italic&&(t["font-style"]="italic"),e.fontStyle&k.Bold&&(t["font-weight"]="bold");let n=[];e.fontStyle&k.Underline&&n.push("underline"),e.fontStyle&k.Strikethrough&&n.push("line-through"),n.length&&(t["text-decoration"]=n.join(" "))}return t}function tu(e){return"string"==typeof e?e:Object.entries(e).map(([e,t])=>`${e}:${t}`).join(";")}let th=new WeakMap;function tp(e,t){th.set(e,t)}function td(e){return th.get(e)}class tf{_stacks={};lang;get themes(){return Object.keys(this._stacks)}get theme(){return this.themes[0]}get _stack(){return this._stacks[this.theme]}static initial(e,t){return new tf(Object.fromEntries((Array.isArray(t)?t:[t]).map(e=>[e,ey])),e)}constructor(...e){if(2===e.length){let[t,n]=e;this.lang=n,this._stacks=t}else{let[t,n,r]=e;this.lang=n,this._stacks={[r]:t}}}getInternalStack(e=this.theme){return this._stacks[e]}getScopes(e=this.theme){var t=this._stacks[e];let n=[],r=new Set;return!function e(t){if(r.has(t))return;r.add(t);let i=t?.nameScopesList?.scopeName;i&&n.push(i),t.parent&&e(t.parent)}(t),n}toJSON(){return{lang:this.lang,theme:this.theme,themes:this.themes,scopes:this.getScopes()}}}let tm=[function(){let e=new WeakMap;function t(t){if(!e.has(t.meta)){let n=function(e){if("number"==typeof e){if(e<0||e>t.source.length)throw new o(`Invalid decoration offset: ${e}. Code length: ${t.source.length}`);return{...r.indexToPos(e),offset:e}}{let t=r.lines[e.line];if(void 0===t)throw new o(`Invalid decoration position ${JSON.stringify(e)}. Lines length: ${r.lines.length}`);if(e.character<0||e.character>t.length)throw new o(`Invalid decoration position ${JSON.stringify(e)}. Line ${e.line} length: ${t.length}`);return{...e,offset:r.posToIndex(e.line,e.character)}}},r=function(e){let t=ts(e,!0).map(([e])=>e);return{lines:t,indexToPos:function(n){if(n===e.length)return{line:t.length-1,character:t[t.length-1].length};let r=n,i=0;for(let e of t){if(r<e.length)break;r-=e.length,i++}return{line:i,character:r}},posToIndex:function(e,n){let r=0;for(let n=0;n<e;n++)r+=t[n].length;return r+n}}}(t.source),i=(t.options.decorations||[]).map(e=>({...e,start:n(e.start),end:n(e.end)}));(function(e){for(let t=0;t<e.length;t++){let n=e[t];if(n.start.offset>n.end.offset)throw new o(`Invalid decoration range: ${JSON.stringify(n.start)} - ${JSON.stringify(n.end)}`);for(let r=t+1;r<e.length;r++){let t=e[r],i=n.start.offset<=t.start.offset&&t.start.offset<n.end.offset,a=n.start.offset<t.end.offset&&t.end.offset<=n.end.offset,l=t.start.offset<=n.start.offset&&n.start.offset<t.end.offset,s=t.start.offset<n.end.offset&&n.end.offset<=t.end.offset;if(i||a||l||s){if(i&&a||l&&s)continue;throw new o(`Decorations ${JSON.stringify(n.start)} and ${JSON.stringify(t.start)} intersect.`)}}}})(i),e.set(t.meta,{decorations:i,converter:r,source:t.source})}return e.get(t.meta)}return{name:"shiki:decorations",tokens(e){if(this.options.decorations?.length){var n=e,r=t(this).decorations.flatMap(e=>[e.start.offset,e.end.offset]);let i=Array.from(r instanceof Set?r:new Set(r)).sort((e,t)=>e-t);return i.length?n.map(e=>e.flatMap(e=>{let t=i.filter(t=>e.offset<t&&t<e.offset+e.content.length).map(t=>t-e.offset).sort((e,t)=>e-t);if(!t.length)return e;let n=0,r=[];for(let i of t)i>n&&r.push({...e,content:e.content.slice(n,i),offset:e.offset+n}),n=i;return n<e.content.length&&r.push({...e,content:e.content.slice(n),offset:e.offset+n}),r})):n}},code(e){if(!this.options.decorations?.length)return;let n=t(this),r=Array.from(e.children).filter(e=>"element"===e.type&&"span"===e.tagName);if(r.length!==n.converter.lines.length)throw new o(`Number of lines in code element (${r.length}) does not match the number of lines in the source (${n.converter.lines.length}). Failed to apply decorations.`);function i(e,t,n,i){let l=r[e],s="",c=-1,u=-1;if(0===t&&(c=0),0===n&&(u=0),n===Number.POSITIVE_INFINITY&&(u=l.children.length),-1===c||-1===u)for(let e=0;e<l.children.length;e++)s+=function e(t){return"text"===t.type?t.value:"element"===t.type?t.children.map(e).join(""):""}(l.children[e]),-1===c&&s.length===t&&(c=e+1),-1===u&&s.length===n&&(u=e+1);if(-1===c)throw new o(`Failed to find start index for decoration ${JSON.stringify(i.start)}`);if(-1===u)throw new o(`Failed to find end index for decoration ${JSON.stringify(i.end)}`);let h=l.children.slice(c,u);if(i.alwaysWrap||h.length!==l.children.length)if(i.alwaysWrap||1!==h.length||"element"!==h[0].type){let e={type:"element",tagName:"span",properties:{},children:h};a(e,i,"wrapper"),l.children.splice(c,h.length,e)}else a(h[0],i,"token");else a(l,i,"line")}function a(e,t,n){let r=t.properties||{},i=t.transform||(e=>e);return e.tagName=t.tagName||"span",e.properties={...e.properties,...r,class:e.properties.class},t.properties?.class&&tl(e,t.properties.class),e=i(e,n)||e}let l=[];for(let e of n.decorations.sort((e,t)=>t.start.offset-e.start.offset||e.end.offset-t.end.offset)){let{start:t,end:n}=e;if(t.line===n.line)i(t.line,t.character,n.character,e);else if(t.line<n.line){i(t.line,t.character,Number.POSITIVE_INFINITY,e);for(let i=t.line+1;i<n.line;i++)l.unshift(()=>{var t;r[t=i]=a(r[t],e,"line")});i(n.line,0,n.character,e)}}l.forEach(e=>e())}}}()];function tg(e){return[...e.transformers||[],...tm]}var tb=["black","red","green","yellow","blue","magenta","cyan","white","brightBlack","brightRed","brightGreen","brightYellow","brightBlue","brightMagenta","brightCyan","brightWhite"],ty={1:"bold",2:"dim",3:"italic",4:"underline",7:"reverse",8:"hidden",9:"strikethrough"};function tk(e){let t=e.shift();if("2"===t){let t=e.splice(0,3).map(e=>Number.parseInt(e));if(3!==t.length||t.some(e=>Number.isNaN(e)))return;return{type:"rgb",rgb:t}}if("5"===t){let t=e.shift();if(t)return{type:"table",index:Number(t)}}}var tx={black:"#000000",red:"#bb0000",green:"#00bb00",yellow:"#bbbb00",blue:"#0000bb",magenta:"#ff00ff",cyan:"#00bbbb",white:"#eeeeee",brightBlack:"#555555",brightRed:"#ff5555",brightGreen:"#00ff00",brightYellow:"#ffff55",brightBlue:"#5555ff",brightMagenta:"#ff55ff",brightCyan:"#55ffff",brightWhite:"#ffffff"};function tv(e,t,n={}){let{lang:r="text",theme:i=e.getLoadedThemes()[0]}=n;if(ti(r)||ta(i))return ts(t).map(e=>[{content:e[0],offset:e[1]}]);let{theme:a,colorMap:l}=e.setTheme(i);if("ansi"===r)return function(e,t,n){let r,i,o,a=tt(e,n),l=ts(t),s=function(e=tx){let t;function n(e){return`#${e.map(e=>Math.max(0,Math.min(e,255)).toString(16).padStart(2,"0")).join("")}`}return{value:function(r){switch(r.type){case"named":return e[r.name];case"rgb":return n(r.rgb);case"table":var i;return i=r.index,function(){if(t)return t;t=[];for(let n=0;n<tb.length;n++)t.push(e[tb[n]]);let r=[0,95,135,175,215,255];for(let e=0;e<6;e++)for(let i=0;i<6;i++)for(let o=0;o<6;o++)t.push(n([r[e],r[i],r[o]]));let i=8;for(let e=0;e<24;e++,i+=10)t.push(n([i,i,i]));return t}()[i]}}}}(Object.fromEntries(tb.map(t=>[t,e.colors?.[`terminal.ansi${t[0].toUpperCase()}${t.substring(1)}`]]))),c=(r=null,i=null,o=new Set,{parse(e){let t=[],n=0;do{let a=function(e,t){let n=e.indexOf("\x1b",t);if(-1!==n&&"["===e[n+1]){let t=e.indexOf("m",n);if(-1!==t)return{sequence:e.substring(n+2,t).split(";"),startPosition:n,position:t+1}}return{position:e.length}}(e,n),l=a.sequence?e.substring(n,a.startPosition):e.substring(n);if(l.length>0&&t.push({value:l,foreground:r,background:i,decorations:new Set(o)}),a.sequence){let e=function(e){let t=[];for(;e.length>0;){let n=e.shift();if(!n)continue;let r=Number.parseInt(n);if(!Number.isNaN(r))if(0===r)t.push({type:"resetAll"});else if(r<=9)ty[r]&&t.push({type:"setDecoration",value:ty[r]});else if(r<=29){let e=ty[r-20];e&&(t.push({type:"resetDecoration",value:e}),"dim"===e&&t.push({type:"resetDecoration",value:"bold"}))}else if(r<=37)t.push({type:"setForegroundColor",value:{type:"named",name:tb[r-30]}});else if(38===r){let n=tk(e);n&&t.push({type:"setForegroundColor",value:n})}else if(39===r)t.push({type:"resetForegroundColor"});else if(r<=47)t.push({type:"setBackgroundColor",value:{type:"named",name:tb[r-40]}});else if(48===r){let n=tk(e);n&&t.push({type:"setBackgroundColor",value:n})}else 49===r?t.push({type:"resetBackgroundColor"}):53===r?t.push({type:"setDecoration",value:"overline"}):55===r?t.push({type:"resetDecoration",value:"overline"}):r>=90&&r<=97?t.push({type:"setForegroundColor",value:{type:"named",name:tb[r-90+8]}}):r>=100&&r<=107&&t.push({type:"setBackgroundColor",value:{type:"named",name:tb[r-100+8]}})}return t}(a.sequence);for(let t of e)"resetAll"===t.type?(r=null,i=null,o.clear()):"resetForegroundColor"===t.type?r=null:"resetBackgroundColor"===t.type?i=null:"resetDecoration"===t.type&&o.delete(t.value);for(let t of e)"setForegroundColor"===t.type?r=t.value:"setBackgroundColor"===t.type?i=t.value:"setDecoration"===t.type&&o.add(t.value)}n=a.position}while(n<e.length);return t}});return l.map(t=>c.parse(t[0]).map(n=>{let r,i;n.decorations.has("reverse")?(r=n.background?s.value(n.background):e.bg,i=n.foreground?s.value(n.foreground):e.fg):(r=n.foreground?s.value(n.foreground):e.fg,i=n.background?s.value(n.background):void 0),r=tn(r,a),i=tn(i,a),n.decorations.has("dim")&&(r=function(e){let t=e.match(/#([0-9a-f]{3})([0-9a-f]{3})?([0-9a-f]{2})?/);if(t)if(t[3]){let e=Math.round(Number.parseInt(t[3],16)/2).toString(16).padStart(2,"0");return`#${t[1]}${t[2]}${e}`}else if(t[2])return`#${t[1]}${t[2]}80`;else return`#${Array.from(t[1]).map(e=>`${e}${e}`).join("")}80`;let n=e.match(/var\((--[\w-]+-ansi-[\w-]+)\)/);return n?`var(${n[1]}-dim)`:e}(r));let o=k.None;return n.decorations.has("bold")&&(o|=k.Bold),n.decorations.has("italic")&&(o|=k.Italic),n.decorations.has("underline")&&(o|=k.Underline),n.decorations.has("strikethrough")&&(o|=k.Strikethrough),{content:n.value,offset:t[1],color:r,bgColor:i,fontStyle:o}}))}(a,t,n);let s=e.getLanguage(r);if(n.grammarState){if(n.grammarState.lang!==s.name)throw new o(`Grammar state language "${n.grammarState.lang}" does not match highlight language "${s.name}"`);if(!n.grammarState.themes.includes(a.name))throw new o(`Grammar state themes "${n.grammarState.themes}" do not contain highlight theme "${a.name}"`)}var c=t,u=s,h=a,p=l,d=n;let f=tw(c,u,h,p,d),m=new tf(tw(c,u,h,p,d).stateStack,u.name,h.name);return tp(f.tokens,m),f.tokens}function tw(e,t,n,r,i){let a=tt(n,i),{tokenizeMaxLineLength:l=0,tokenizeTimeLimit:s=500}=i,c=ts(e),u=i.grammarState?function(e,t){if(!(e instanceof tf))throw new o("Invalid grammar state");return e.getInternalStack(t)}(i.grammarState,n.name)??ey:null!=i.grammarContextCode?tw(i.grammarContextCode,t,n,r,{...i,grammarState:void 0,grammarContextCode:void 0}).stateStack:ey,h=[],p=[];for(let e=0,o=c.length;e<o;e++){let o,d,[f,m]=c[e];if(""===f){h=[],p.push([]);continue}if(l>0&&f.length>=l){h=[],p.push([{content:f,offset:m,color:"",fontStyle:0}]);continue}i.includeExplanation&&(o=t.tokenizeLine(f,u,s).tokens,d=0);let g=t.tokenizeLine2(f,u,s),b=g.tokens.length/2;for(let e=0;e<b;e++){let t=g.tokens[2*e],l=e+1<b?g.tokens[2*e+2]:f.length;if(t===l)continue;let s=g.tokens[2*e+1],c=tn(r[_.getForeground(s)],a),u=_.getFontStyle(s),p={content:f.substring(t,l),offset:m+t,color:c,fontStyle:u};if(i.includeExplanation){let e=[];if("scopeName"!==i.includeExplanation)for(let t of n.settings){let n;switch(typeof t.scope){case"string":n=t.scope.split(/,/).map(e=>e.trim());break;case"object":n=t.scope;break;default:continue}e.push({settings:t,selectors:n.map(e=>e.split(/ /))})}p.explanation=[];let r=0;for(;t+r<l;){let t=o[d],n=f.substring(t.startIndex,t.endIndex);r+=n.length,p.explanation.push({content:n,scopes:"scopeName"===i.includeExplanation?t.scopes.map(e=>({scopeName:e})):function(e,t){let n=[];for(let r=0,i=t.length;r<i;r++){let i=t[r];n[r]={scopeName:i,themeMatches:function(e,t,n){let r=[];for(let{selectors:i,settings:o}of e)for(let e of i)if(function(e,t,n){if(!tS(e[e.length-1],t))return!1;let r=e.length-2,i=n.length-1;for(;r>=0&&i>=0;)tS(e[r],n[i])&&(r-=1),i-=1;return -1===r}(e,t,n)){r.push(o);break}return r}(e,i,t.slice(0,r))}}return n}(e,t.scopes)}),d+=1}}h.push(p)}p.push(h),h=[],u=g.ruleStack}return{tokens:p,stateStack:u}}function tS(e,t){return e===t||t.substring(0,e.length)===e&&"."===t[e.length]}function t_(e,t,n){let r=Object.entries(n.themes).filter(e=>e[1]).map(e=>({color:e[0],theme:e[1]})),i=r.map(r=>{let i=tv(e,t,{...n,theme:r.theme}),o=td(i);return{tokens:i,state:o,theme:"string"==typeof r.theme?r.theme:r.theme.name}}),o=function(...e){let t=e.map(()=>[]),n=e.length;for(let r=0;r<e[0].length;r++){let i=e.map(e=>e[r]),o=t.map(()=>[]);t.forEach((e,t)=>e.push(o[t]));let a=i.map(()=>0),l=i.map(e=>e[0]);for(;l.every(e=>e);){let e=Math.min(...l.map(e=>e.content.length));for(let t=0;t<n;t++){let n=l[t];n.content.length===e?(o[t].push(n),a[t]+=1,l[t]=i[t][a[t]]):(o[t].push({...n,content:n.content.slice(0,e)}),l[t]={...n,content:n.content.slice(e),offset:n.offset+e})}}}return t}(...i.map(e=>e.tokens)),a=o[0].map((e,t)=>e.map((e,i)=>{let a={content:e.content,variants:{},offset:e.offset};return"includeExplanation"in n&&n.includeExplanation&&(a.explanation=e.explanation),o.forEach((e,n)=>{let{content:o,explanation:l,offset:s,...c}=e[t][i];a.variants[r[n].color]=c}),a})),l=i[0].state?new tf(Object.fromEntries(i.map(e=>[e.theme,e.state?.getInternalStack(e.theme)])),i[0].state.lang):void 0;return l&&tp(a,l),a}function tC(e,t,n){let r,i,a,l,s,c;if("themes"in n){let{defaultColor:u="light",cssVariablePrefix:h="--shiki-"}=n,p=Object.entries(n.themes).filter(e=>e[1]).map(e=>({color:e[0],theme:e[1]})).sort((e,t)=>e.color===u?-1:+(t.color===u));if(0===p.length)throw new o("`themes` option must not be empty");let d=t_(e,t,n);if(c=td(d),u&&!p.find(e=>e.color===u))throw new o(`\`themes\` option must contain the defaultColor key \`${u}\``);let f=p.map(t=>e.getTheme(t.theme)),m=p.map(e=>e.color);a=d.map(e=>e.map(e=>(function(e,t,n,r){let i={content:e.content,explanation:e.explanation,offset:e.offset},o=t.map(t=>tc(e.variants[t])),a=new Set(o.flatMap(e=>Object.keys(e))),l={};return o.forEach((e,i)=>{for(let o of a){let a=e[o]||"inherit";if(0===i&&r)l[o]=a;else{let e="color"===o?"":"background-color"===o?"-bg":`-${o}`;l[n+t[i]+("color"===o?"":e)]=a}}}),i.htmlStyle=l,i})(e,m,h,u))),c&&tp(a,c);let g=p.map(e=>tt(e.theme,n));i=p.map((e,t)=>(0===t&&u?"":`${h+e.color}:`)+(tn(f[t].fg,g[t])||"inherit")).join(";"),r=p.map((e,t)=>(0===t&&u?"":`${h+e.color}-bg:`)+(tn(f[t].bg,g[t])||"inherit")).join(";"),l=`shiki-themes ${f.map(e=>e.name).join(" ")}`,s=u?void 0:[i,r].join(";")}else if("theme"in n){let o=tt(n.theme,n);a=tv(e,t,n);let s=e.getTheme(n.theme);r=tn(s.bg,o),i=tn(s.fg,o),l=s.name,c=td(a)}else throw new o("Invalid options, either `theme` or `themes` must be provided");return{tokens:a,fg:i,bg:r,themeName:l,rootStyle:s,grammarState:c}}function tP(e,t,n,r={meta:{},options:n,codeToHast:(t,n)=>tP(e,t,n),codeToTokens:(t,n)=>tC(e,t,n)}){let i=t;for(let e of tg(n))i=e.preprocess?.call(r,i,n)||i;let{tokens:o,fg:a,bg:l,themeName:s,rootStyle:c,grammarState:u}=tC(e,i,n),{mergeWhitespaces:h=!0,mergeSameStyleTokens:p=!1}=n;!0===h?o=o.map(e=>{let t=[],n="",r=0;return e.forEach((i,o)=>{let a=!(i.fontStyle&&(i.fontStyle&k.Underline||i.fontStyle&k.Strikethrough));a&&i.content.match(/^\s+$/)&&e[o+1]?(r||(r=i.offset),n+=i.content):n?(a?t.push({...i,offset:r,content:n+i.content}):t.push({content:n,offset:r},i),r=0,n=""):t.push(i)}),t}):"never"===h&&(o=o.map(e=>e.flatMap(e=>{if(e.content.match(/^\s+$/))return e;let t=e.content.match(/^(\s*)(.*?)(\s*)$/);if(!t)return e;let[,n,r,i]=t;if(!n&&!i)return e;let o=[{...e,offset:e.offset+n.length,content:r}];return n&&o.unshift({content:n,offset:e.offset}),i&&o.push({content:i,offset:e.offset+n.length+r.length}),o}))),p&&(o=o.map(e=>{let t=[];for(let n of e){if(0===t.length){t.push({...n});continue}let e=t[t.length-1],r=e.htmlStyle||tu(tc(e)),i=n.htmlStyle||tu(tc(n)),o=e.fontStyle&&(e.fontStyle&k.Underline||e.fontStyle&k.Strikethrough),a=n.fontStyle&&(n.fontStyle&k.Underline||n.fontStyle&k.Strikethrough);o||a||r!==i?t.push({...n}):e.content+=n.content}return t}));let d={...r,get source(){return i}};for(let e of tg(n))o=e.tokens?.call(d,o)||o;return function(e,t,n,r=td(e)){let i=tg(t),o=[],a={type:"root",children:[]},{structure:l="classic",tabindex:s="0"}=t,c={type:"element",tagName:"pre",properties:{class:`shiki ${t.themeName||""}`,style:t.rootStyle||`background-color:${t.bg};color:${t.fg}`,...!1!==s&&null!=s?{tabindex:s.toString()}:{},...Object.fromEntries(Array.from(Object.entries(t.meta||{})).filter(([e])=>!e.startsWith("_")))},children:[]},u={type:"element",tagName:"code",properties:{},children:o},h=[],p={...n,structure:l,addClassToHast:tl,get source(){return n.source},get tokens(){return e},get options(){return t},get root(){return a},get pre(){return c},get code(){return u},get lines(){return h}};if(e.forEach((e,t)=>{t&&("inline"===l?a.children.push({type:"element",tagName:"br",properties:{},children:[]}):"classic"===l&&o.push({type:"text",value:"\n"}));let n={type:"element",tagName:"span",properties:{class:"line"},children:[]},r=0;for(let o of e){let e={type:"element",tagName:"span",properties:{...o.htmlAttrs},children:[{type:"text",value:o.content}]},s=tu(o.htmlStyle||tc(o));for(let a of(s&&(e.properties.style=s),i))e=a?.span?.call(p,e,t+1,r,n,o)||e;"inline"===l?a.children.push(e):"classic"===l&&n.children.push(e),r+=o.content.length}if("classic"===l){for(let e of i)n=e?.line?.call(p,n,t+1)||n;h.push(n),o.push(n)}}),"classic"===l){for(let e of i)u=e?.code?.call(p,u)||u;for(let e of(c.children.push(u),i))c=e?.pre?.call(p,c)||c;a.children.push(c)}let d=a;for(let e of i)d=e?.root?.call(p,d)||d;return r&&tp(d,r),d}(o,{...n,fg:a,bg:l,themeName:s,rootStyle:c},d,u)}let tA=function(e,t){let n=t||e6,r=n.quote||'"';if('"'!==r&&"'"!==r)throw Error("Invalid quote `"+r+"`, expected `'` or `\"`");return({one:e8,all:te,settings:{omitOptionalTags:n.omitOptionalTags||!1,allowParseErrors:n.allowParseErrors||!1,allowDangerousCharacters:n.allowDangerousCharacters||!1,quoteSmart:n.quoteSmart||!1,preferUnquoted:n.preferUnquoted||!1,tightAttributes:n.tightAttributes||!1,upperDoctype:n.upperDoctype||!1,tightDoctype:n.tightDoctype||!1,bogusComments:n.bogusComments||!1,tightCommaSeparatedLists:n.tightCommaSeparatedLists||!1,tightSelfClosing:n.tightSelfClosing||!1,collapseEmptyAttributes:n.collapseEmptyAttributes||!1,allowDangerousHtml:n.allowDangerousHtml||!1,voids:n.voids||ek.b,characterReferences:n.characterReferences||e9,closeSelfClosing:n.closeSelfClosing||!1,closeEmptyElements:n.closeEmptyElements||!1},schema:"svg"===n.space?ex.JW:ex.qy,quote:r,alternative:'"'===r?"'":'"'}).one(Array.isArray(e)?{type:"root",children:e}:e,void 0,void 0)},tT={light:"#333333",dark:"#bbbbbb"},tN={light:"#fffffe",dark:"#1e1e1e"},tE="__shiki_resolved";function tL(e){if(e?.[tE])return e;let t={...e};t.tokenColors&&!t.settings&&(t.settings=t.tokenColors,delete t.tokenColors),t.type||="dark",t.colorReplacements={...t.colorReplacements},t.settings||=[];let{bg:n,fg:r}=t;if(!n||!r){let e=t.settings?t.settings.find(e=>!e.name&&!e.scope):void 0;e?.settings?.foreground&&(r=e.settings.foreground),e?.settings?.background&&(n=e.settings.background),!r&&t?.colors?.["editor.foreground"]&&(r=t.colors["editor.foreground"]),!n&&t?.colors?.["editor.background"]&&(n=t.colors["editor.background"]),r||(r="light"===t.type?tT.light:tT.dark),n||(n="light"===t.type?tN.light:tN.dark),t.fg=r,t.bg=n}t.settings[0]&&t.settings[0].settings&&!t.settings[0].scope||t.settings.unshift({settings:{foreground:t.fg,background:t.bg}});let i=0,o=new Map;function a(e){if(o.has(e))return o.get(e);i+=1;let n=`#${i.toString(16).padStart(8,"0").toLowerCase()}`;return t.colorReplacements?.[`#${n}`]?a(e):(o.set(e,n),n)}for(let e of(t.settings=t.settings.map(e=>{let n=e.settings?.foreground&&!e.settings.foreground.startsWith("#"),r=e.settings?.background&&!e.settings.background.startsWith("#");if(!n&&!r)return e;let i={...e,settings:{...e.settings}};if(n){let n=a(e.settings.foreground);t.colorReplacements[n]=e.settings.foreground,i.settings.foreground=n}if(r){let n=a(e.settings.background);t.colorReplacements[n]=e.settings.background,i.settings.background=n}return i}),Object.keys(t.colors||{})))if(("editor.foreground"===e||"editor.background"===e||e.startsWith("terminal.ansi"))&&!t.colors[e]?.startsWith("#")){let n=a(t.colors[e]);t.colorReplacements[n]=t.colors[e],t.colors[e]=n}return Object.defineProperty(t,tE,{enumerable:!1,writable:!1,value:!0}),t}async function tI(e){return Array.from(new Set((await Promise.all(e.filter(e=>!to(e)).map(async e=>await tr(e).then(e=>Array.isArray(e)?e:[e])))).flat()))}async function tR(e){return(await Promise.all(e.map(async e=>ta(e)?null:tL(await tr(e))))).filter(e=>!!e)}class tO extends Error{constructor(e){super(e),this.name="ShikiError"}}class tD extends eb{constructor(e,t,n,r={}){super(e),this._resolver=e,this._themes=t,this._langs=n,this._alias=r,this._themes.map(e=>this.loadTheme(e)),this.loadLanguages(this._langs)}_resolvedThemes=new Map;_resolvedGrammars=new Map;_langMap=new Map;_langGraph=new Map;_textmateThemeCache=new WeakMap;_loadedThemesCache=null;_loadedLanguagesCache=null;getTheme(e){return"string"==typeof e?this._resolvedThemes.get(e):this.loadTheme(e)}loadTheme(e){let t=tL(e);return t.name&&(this._resolvedThemes.set(t.name,t),this._loadedThemesCache=null),t}getLoadedThemes(){return this._loadedThemesCache||(this._loadedThemesCache=[...this._resolvedThemes.keys()]),this._loadedThemesCache}setTheme(e){let t=this._textmateThemeCache.get(e);t||(t=m.createFromRawTheme(e),this._textmateThemeCache.set(e,t)),this._syncRegistry.setTheme(t)}getGrammar(e){if(this._alias[e]){let t=new Set([e]);for(;this._alias[e];){if(e=this._alias[e],t.has(e))throw new tO(`Circular alias \`${Array.from(t).join(" -> ")} -> ${e}\``);t.add(e)}}return this._resolvedGrammars.get(e)}loadLanguage(e){if(this.getGrammar(e.name))return;let t=new Set([...this._langMap.values()].filter(t=>t.embeddedLangsLazy?.includes(e.name)));this._resolver.addLanguage(e);let n={balancedBracketSelectors:e.balancedBracketSelectors||["*"],unbalancedBracketSelectors:e.unbalancedBracketSelectors||[]};this._syncRegistry._rawGrammars.set(e.scopeName,e);let r=this.loadGrammarWithConfiguration(e.scopeName,1,n);if(r.name=e.name,this._resolvedGrammars.set(e.name,r),e.aliases&&e.aliases.forEach(t=>{this._alias[t]=e.name}),this._loadedLanguagesCache=null,t.size)for(let e of t)this._resolvedGrammars.delete(e.name),this._loadedLanguagesCache=null,this._syncRegistry?._injectionGrammars?.delete(e.scopeName),this._syncRegistry?._grammars?.delete(e.scopeName),this.loadLanguage(this._langMap.get(e.name))}dispose(){super.dispose(),this._resolvedThemes.clear(),this._resolvedGrammars.clear(),this._langMap.clear(),this._langGraph.clear(),this._loadedThemesCache=null}loadLanguages(e){for(let t of e)this.resolveEmbeddedLanguages(t);let t=Array.from(this._langGraph.entries()),n=t.filter(([e,t])=>!t);if(n.length){let e=t.filter(([e,t])=>t&&t.embeddedLangs?.some(e=>n.map(([e])=>e).includes(e))).filter(e=>!n.includes(e));throw new tO(`Missing languages ${n.map(([e])=>`\`${e}\``).join(", ")}, required by ${e.map(([e])=>`\`${e}\``).join(", ")}`)}for(let[e,n]of t)this._resolver.addLanguage(n);for(let[e,n]of t)this.loadLanguage(n)}getLoadedLanguages(){return this._loadedLanguagesCache||(this._loadedLanguagesCache=[...new Set([...this._resolvedGrammars.keys(),...Object.keys(this._alias)])]),this._loadedLanguagesCache}resolveEmbeddedLanguages(e){if(this._langMap.set(e.name,e),this._langGraph.set(e.name,e),e.embeddedLangs)for(let t of e.embeddedLangs)this._langGraph.set(t,this._langMap.get(t))}}class tM{_langs=new Map;_scopeToLang=new Map;_injections=new Map;_onigLib;constructor(e,t){this._onigLib={createOnigScanner:t=>e.createScanner(t),createOnigString:t=>e.createString(t)},t.forEach(e=>this.addLanguage(e))}get onigLib(){return this._onigLib}getLangRegistration(e){return this._langs.get(e)}loadGrammar(e){return this._scopeToLang.get(e)}addLanguage(e){this._langs.set(e.name,e),e.aliases&&e.aliases.forEach(t=>{this._langs.set(t,e)}),this._scopeToLang.set(e.scopeName,e),e.injectTo&&e.injectTo.forEach(t=>{this._injections.get(t)||this._injections.set(t,[]),this._injections.get(t).push(e.scopeName)})}getInjections(e){let t=e.split("."),n=[];for(let e=1;e<=t.length;e++){let r=t.slice(0,e).join(".");n=[...n,...this._injections.get(r)||[]]}return n}}let tz=0;async function tB(e){e.engine||function(e,t=3){if(1&&!(t>3))if(1)console.trace(`[SHIKI DEPRECATE]: ${e}`);else throw Error(`[SHIKI DEPRECATE]: ${e}`)}("`engine` option is required. Use `createOnigurumaEngine` or `createJavaScriptRegexEngine` to create an engine.");let[t,n,r]=await Promise.all([tR(e.themes||[]),tI(e.langs||[]),e.engine]);return function(e){let t;tz+=1,!1!==e.warnings&&tz>=10&&tz%10==0&&console.warn(`[Shiki] ${tz} instances have been created. Shiki is supposed to be used as a singleton, consider refactoring your code to cache your highlighter instance; Or call \`highlighter.dispose()\` to release unused instances.`);let n=!1;if(!e.engine)throw new tO("`engine` option is required for synchronous mode");let r=(e.langs||[]).flat(1),i=(e.themes||[]).flat(1).map(tL),o=new tD(new tM(e.engine,r),i,r,e.langAlias);function a(e){if("none"===e)return{bg:"",fg:"",name:"none",settings:[],type:"dark"};c();let t=o.getTheme(e);if(!t)throw new tO(`Theme \`${e}\` not found, you may need to load it first`);return t}function l(...e){c(),o.loadLanguages(e.flat(1))}function s(...e){for(let t of(c(),e.flat(1)))o.loadTheme(t)}function c(){if(n)throw new tO("Shiki instance has been disposed")}function u(){n||(n=!0,o.dispose(),tz-=1)}return{setTheme:function(e){c();let n=a(e);return t!==e&&(o.setTheme(n),t=e),{theme:n,colorMap:o.getColorMap()}},getTheme:a,getLanguage:function(e){c();let t=o.getGrammar("string"==typeof e?e:e.name);if(!t)throw new tO(`Language \`${e}\` not found, you may need to load it first`);return t},getLoadedThemes:function(){return c(),o.getLoadedThemes()},getLoadedLanguages:function(){return c(),o.getLoadedLanguages()},loadLanguage:async function(...e){return l(await tI(e))},loadLanguageSync:l,loadTheme:async function(...e){return c(),s(await tR(e))},loadThemeSync:s,dispose:u,[Symbol.dispose]:u}}({...e,themes:t,langs:n,engine:r})}async function tF(e){let t=await tB(e);return{getLastGrammarState:(...e)=>(function(...e){if(2===e.length)return td(e[1]);let[t,n,r={}]=e,{lang:i="text",theme:a=t.getLoadedThemes()[0]}=r;if(ti(i)||ta(a))throw new o("Plain language does not have grammar state");if("ansi"===i)throw new o("ANSI language does not have grammar state");let{theme:l,colorMap:s}=t.setTheme(a),c=t.getLanguage(i);return new tf(tw(n,c,l,s,r).stateStack,c.name,l.name)})(t,...e),codeToTokensBase:(e,n)=>tv(t,e,n),codeToTokensWithThemes:(e,n)=>t_(t,e,n),codeToTokens:(e,n)=>tC(t,e,n),codeToHast:(e,n)=>tP(t,e,n),codeToHtml:(e,n)=>(function(e,t,n){let r={meta:{},options:n,codeToHast:(t,n)=>tP(e,t,n),codeToTokens:(t,n)=>tC(e,t,n)},i=tA(tP(e,t,n,r));for(let e of tg(n))i=e.postprocess?.call(r,i,n)||i;return i})(t,e,n),getBundledLanguages:()=>({}),getBundledThemes:()=>({}),...t,getInternalContext:()=>t}}let tj=[{id:"abap",name:"ABAP",import:()=>n.e(2363).then(n.bind(n,12363))},{id:"actionscript-3",name:"ActionScript",import:()=>n.e(1818).then(n.bind(n,1818))},{id:"ada",name:"Ada",import:()=>n.e(3290).then(n.bind(n,90909))},{id:"angular-html",name:"Angular HTML",import:()=>Promise.all([n.e(2577),n.e(3640),n.e(1046),n.e(5885)]).then(n.bind(n,85885))},{id:"angular-ts",name:"Angular TypeScript",import:()=>Promise.all([n.e(2577),n.e(1630),n.e(3640),n.e(1046),n.e(8721),n.e(2610)]).then(n.bind(n,91737))},{id:"apache",name:"Apache Conf",import:()=>n.e(627).then(n.bind(n,80627))},{id:"apex",name:"Apex",import:()=>n.e(3177).then(n.bind(n,63177))},{id:"apl",name:"APL",import:()=>Promise.all([n.e(2577),n.e(3640),n.e(1046),n.e(557),n.e(8460)]).then(n.bind(n,48460))},{id:"applescript",name:"AppleScript",import:()=>n.e(3580).then(n.bind(n,73580))},{id:"ara",name:"Ara",import:()=>n.e(8667).then(n.bind(n,48667))},{id:"asciidoc",name:"AsciiDoc",aliases:["adoc"],import:()=>n.e(2944).then(n.bind(n,72944))},{id:"asm",name:"Assembly",import:()=>n.e(1144).then(n.bind(n,71144))},{id:"astro",name:"Astro",import:()=>Promise.all([n.e(2577),n.e(535),n.e(3640),n.e(6624)]).then(n.bind(n,36624))},{id:"awk",name:"AWK",import:()=>n.e(9858).then(n.bind(n,39858))},{id:"ballerina",name:"Ballerina",import:()=>n.e(7781).then(n.bind(n,37781))},{id:"bat",name:"Batch File",aliases:["batch"],import:()=>n.e(6028).then(n.bind(n,16028))},{id:"beancount",name:"Beancount",import:()=>n.e(5906).then(n.bind(n,75906))},{id:"berry",name:"Berry",aliases:["be"],import:()=>n.e(2989).then(n.bind(n,82989))},{id:"bibtex",name:"BibTeX",import:()=>n.e(8075).then(n.bind(n,38075))},{id:"bicep",name:"Bicep",import:()=>n.e(8332).then(n.bind(n,98332))},{id:"blade",name:"Blade",import:()=>Promise.all([n.e(2577),n.e(3640),n.e(1046),n.e(557),n.e(97),n.e(719)]).then(n.bind(n,90719))},{id:"bsl",name:"1C (Enterprise)",aliases:["1c"],import:()=>n.e(9158).then(n.bind(n,49158))},{id:"c",name:"C",import:()=>n.e(1442).then(n.bind(n,1442))},{id:"cadence",name:"Cadence",aliases:["cdc"],import:()=>n.e(5100).then(n.bind(n,95100))},{id:"cairo",name:"Cairo",import:()=>Promise.all([n.e(9927),n.e(8393)]).then(n.bind(n,38393))},{id:"clarity",name:"Clarity",import:()=>n.e(7757).then(n.bind(n,47757))},{id:"clojure",name:"Clojure",aliases:["clj"],import:()=>n.e(4093).then(n.bind(n,94093))},{id:"cmake",name:"CMake",import:()=>n.e(8736).then(n.bind(n,38736))},{id:"cobol",name:"COBOL",import:()=>Promise.all([n.e(2577),n.e(3640),n.e(1046),n.e(557),n.e(1104)]).then(n.bind(n,11104))},{id:"codeowners",name:"CODEOWNERS",import:()=>n.e(4922).then(n.bind(n,84922))},{id:"codeql",name:"CodeQL",aliases:["ql"],import:()=>n.e(8909).then(n.bind(n,98909))},{id:"coffee",name:"CoffeeScript",aliases:["coffeescript"],import:()=>Promise.all([n.e(2577),n.e(4017)]).then(n.bind(n,84017))},{id:"common-lisp",name:"Common Lisp",aliases:["lisp"],import:()=>n.e(1461).then(n.bind(n,41461))},{id:"coq",name:"Coq",import:()=>n.e(5814).then(n.bind(n,15814))},{id:"cpp",name:"C++",aliases:["c++"],import:()=>Promise.all([n.e(6666),n.e(8835),n.e(97),n.e(1442),n.e(9062)]).then(n.bind(n,68474))},{id:"crystal",name:"Crystal",import:()=>Promise.all([n.e(2577),n.e(3640),n.e(1046),n.e(97),n.e(1442),n.e(4038),n.e(3487)]).then(n.bind(n,93487))},{id:"csharp",name:"C#",aliases:["c#","cs"],import:()=>n.e(1264).then(n.bind(n,81264))},{id:"css",name:"CSS",import:()=>n.e(3640).then(n.bind(n,43640))},{id:"csv",name:"CSV",import:()=>n.e(2877).then(n.bind(n,52877))},{id:"cue",name:"CUE",import:()=>n.e(8540).then(n.bind(n,38540))},{id:"cypher",name:"Cypher",aliases:["cql"],import:()=>n.e(3344).then(n.bind(n,23344))},{id:"d",name:"D",import:()=>n.e(7213).then(n.bind(n,57213))},{id:"dart",name:"Dart",import:()=>n.e(438).then(n.bind(n,60438))},{id:"dax",name:"DAX",import:()=>n.e(7474).then(n.bind(n,97474))},{id:"desktop",name:"Desktop",import:()=>n.e(1229).then(n.bind(n,51229))},{id:"diff",name:"Diff",import:()=>n.e(5640).then(n.bind(n,45640))},{id:"docker",name:"Dockerfile",aliases:["dockerfile"],import:()=>n.e(4953).then(n.bind(n,84953))},{id:"dotenv",name:"dotEnv",import:()=>n.e(6467).then(n.bind(n,36467))},{id:"dream-maker",name:"Dream Maker",import:()=>n.e(9835).then(n.bind(n,49835))},{id:"edge",name:"Edge",import:()=>Promise.all([n.e(2577),n.e(535),n.e(3640),n.e(1046),n.e(650)]).then(n.bind(n,30650))},{id:"elixir",name:"Elixir",import:()=>Promise.all([n.e(2577),n.e(3640),n.e(1046),n.e(3040)]).then(n.bind(n,3040))},{id:"elm",name:"Elm",import:()=>Promise.all([n.e(1442),n.e(989)]).then(n.bind(n,20989))},{id:"emacs-lisp",name:"Emacs Lisp",aliases:["elisp"],import:()=>n.e(3879).then(n.bind(n,13243))},{id:"erb",name:"ERB",import:()=>Promise.all([n.e(2577),n.e(535),n.e(9312),n.e(6814),n.e(6666),n.e(8835),n.e(3640),n.e(1046),n.e(557),n.e(97),n.e(1442),n.e(4038),n.e(8254),n.e(634),n.e(6524)]).then(n.bind(n,76524))},{id:"erlang",name:"Erlang",aliases:["erl"],import:()=>n.e(1708).then(n.bind(n,21708))},{id:"fennel",name:"Fennel",import:()=>n.e(3493).then(n.bind(n,83493))},{id:"fish",name:"Fish",import:()=>n.e(8311).then(n.bind(n,8311))},{id:"fluent",name:"Fluent",aliases:["ftl"],import:()=>n.e(5071).then(n.bind(n,15071))},{id:"fortran-fixed-form",name:"Fortran (Fixed Form)",aliases:["f","for","f77"],import:()=>Promise.all([n.e(9837),n.e(9155)]).then(n.bind(n,29155))},{id:"fortran-free-form",name:"Fortran (Free Form)",aliases:["f90","f95","f03","f08","f18"],import:()=>n.e(9837).then(n.bind(n,62218))},{id:"fsharp",name:"F#",aliases:["f#","fs"],import:()=>Promise.all([n.e(6126),n.e(5123)]).then(n.bind(n,65123))},{id:"gdresource",name:"GDResource",import:()=>n.e(1584).then(n.bind(n,21584))},{id:"gdscript",name:"GDScript",import:()=>n.e(4129).then(n.bind(n,44129))},{id:"gdshader",name:"GDShader",import:()=>n.e(5175).then(n.bind(n,25175))},{id:"genie",name:"Genie",import:()=>n.e(6789).then(n.bind(n,76789))},{id:"gherkin",name:"Gherkin",import:()=>n.e(6319).then(n.bind(n,96319))},{id:"git-commit",name:"Git Commit Message",import:()=>n.e(3639).then(n.bind(n,93639))},{id:"git-rebase",name:"Git Rebase Message",import:()=>Promise.all([n.e(4038),n.e(2580)]).then(n.bind(n,22580))},{id:"gleam",name:"Gleam",import:()=>n.e(8345).then(n.bind(n,18345))},{id:"glimmer-js",name:"Glimmer JS",aliases:["gjs"],import:()=>Promise.all([n.e(2577),n.e(535),n.e(3640),n.e(1046),n.e(578)]).then(n.bind(n,40578))},{id:"glimmer-ts",name:"Glimmer TS",aliases:["gts"],import:()=>Promise.all([n.e(2577),n.e(535),n.e(3640),n.e(1046),n.e(2400)]).then(n.bind(n,42400))},{id:"glsl",name:"GLSL",import:()=>Promise.all([n.e(1442),n.e(9558)]).then(n.bind(n,87177))},{id:"gnuplot",name:"Gnuplot",import:()=>n.e(7532).then(n.bind(n,87532))},{id:"go",name:"Go",import:()=>n.e(2827).then(n.bind(n,52827))},{id:"graphql",name:"GraphQL",aliases:["gql"],import:()=>Promise.all([n.e(2577),n.e(535),n.e(9312),n.e(6814),n.e(8254)]).then(n.bind(n,28254))},{id:"groovy",name:"Groovy",import:()=>n.e(9227).then(n.bind(n,79227))},{id:"hack",name:"Hack",import:()=>Promise.all([n.e(2577),n.e(3640),n.e(1046),n.e(97),n.e(2725)]).then(n.bind(n,65106))},{id:"haml",name:"Ruby Haml",import:()=>Promise.all([n.e(2577),n.e(3640),n.e(6519)]).then(n.bind(n,76519))},{id:"handlebars",name:"Handlebars",aliases:["hbs"],import:()=>Promise.all([n.e(2577),n.e(3640),n.e(1046),n.e(7486)]).then(n.bind(n,15105))},{id:"haskell",name:"Haskell",aliases:["hs"],import:()=>n.e(2605).then(n.bind(n,2605))},{id:"haxe",name:"Haxe",import:()=>n.e(8385).then(n.bind(n,68385))},{id:"hcl",name:"HashiCorp HCL",import:()=>n.e(608).then(n.bind(n,20608))},{id:"hjson",name:"Hjson",import:()=>n.e(8019).then(n.bind(n,68019))},{id:"hlsl",name:"HLSL",import:()=>n.e(376).then(n.bind(n,40376))},{id:"html",name:"HTML",import:()=>Promise.all([n.e(2577),n.e(3640),n.e(1046)]).then(n.bind(n,1046))},{id:"html-derivative",name:"HTML (Derivative)",import:()=>Promise.all([n.e(2577),n.e(3640),n.e(1046),n.e(362)]).then(n.bind(n,40362))},{id:"http",name:"HTTP",import:()=>Promise.all([n.e(2577),n.e(535),n.e(9312),n.e(6814),n.e(557),n.e(4038),n.e(8254),n.e(597)]).then(n.bind(n,20597))},{id:"hxml",name:"HXML",import:()=>n.e(4690).then(n.bind(n,4690))},{id:"hy",name:"Hy",import:()=>n.e(220).then(n.bind(n,90220))},{id:"imba",name:"Imba",import:()=>n.e(6184).then(n.bind(n,16184))},{id:"ini",name:"INI",aliases:["properties"],import:()=>n.e(6111).then(n.bind(n,86111))},{id:"java",name:"Java",import:()=>n.e(557).then(n.bind(n,20557))},{id:"javascript",name:"JavaScript",aliases:["js"],import:()=>n.e(2577).then(n.bind(n,77576))},{id:"jinja",name:"Jinja",import:()=>Promise.all([n.e(2577),n.e(3640),n.e(1046),n.e(3727)]).then(n.bind(n,33727))},{id:"jison",name:"Jison",import:()=>Promise.all([n.e(2577),n.e(3144)]).then(n.bind(n,63144))},{id:"json",name:"JSON",import:()=>n.e(1983).then(n.bind(n,41983))},{id:"json5",name:"JSON5",import:()=>n.e(4650).then(n.bind(n,64650))},{id:"jsonc",name:"JSON with Comments",import:()=>n.e(9748).then(n.bind(n,49748))},{id:"jsonl",name:"JSON Lines",import:()=>n.e(1931).then(n.bind(n,91931))},{id:"jsonnet",name:"Jsonnet",import:()=>n.e(3566).then(n.bind(n,53566))},{id:"jssm",name:"JSSM",aliases:["fsl"],import:()=>n.e(5536).then(n.bind(n,55536))},{id:"jsx",name:"JSX",import:()=>n.e(9312).then(n.bind(n,37402))},{id:"julia",name:"Julia",aliases:["jl"],import:()=>Promise.all([n.e(2577),n.e(6666),n.e(8835),n.e(97),n.e(1442),n.e(9927),n.e(3195),n.e(8765)]).then(n.bind(n,68698))},{id:"kotlin",name:"Kotlin",aliases:["kt","kts"],import:()=>n.e(7202).then(n.bind(n,97202))},{id:"kusto",name:"Kusto",aliases:["kql"],import:()=>n.e(9993).then(n.bind(n,99993))},{id:"latex",name:"LaTeX",import:()=>Promise.all([n.e(3195),n.e(4733)]).then(n.bind(n,54733))},{id:"lean",name:"Lean 4",aliases:["lean4"],import:()=>n.e(6897).then(n.bind(n,26897))},{id:"less",name:"Less",import:()=>n.e(7274).then(n.bind(n,87274))},{id:"liquid",name:"Liquid",import:()=>Promise.all([n.e(2577),n.e(3640),n.e(1046),n.e(3349)]).then(n.bind(n,13349))},{id:"llvm",name:"LLVM IR",import:()=>n.e(9106).then(n.bind(n,59106))},{id:"log",name:"Log file",import:()=>n.e(5859).then(n.bind(n,55859))},{id:"logo",name:"Logo",import:()=>n.e(6604).then(n.bind(n,16604))},{id:"lua",name:"Lua",import:()=>Promise.all([n.e(1442),n.e(359)]).then(n.bind(n,90359))},{id:"luau",name:"Luau",import:()=>n.e(274).then(n.bind(n,40274))},{id:"make",name:"Makefile",aliases:["makefile"],import:()=>n.e(9296).then(n.bind(n,36915))},{id:"markdown",name:"Markdown",aliases:["md"],import:()=>n.e(6126).then(n.bind(n,96126))},{id:"marko",name:"Marko",import:()=>Promise.all([n.e(2577),n.e(3640),n.e(8721),n.e(7274),n.e(4719)]).then(n.bind(n,84719))},{id:"matlab",name:"MATLAB",import:()=>n.e(8926).then(n.bind(n,18926))},{id:"mdc",name:"MDC",import:()=>Promise.all([n.e(2577),n.e(3640),n.e(1046),n.e(6126),n.e(8355)]).then(n.bind(n,88355))},{id:"mdx",name:"MDX",import:()=>n.e(72).then(n.bind(n,80072))},{id:"mermaid",name:"Mermaid",aliases:["mmd"],import:()=>n.e(2972).then(n.bind(n,62972))},{id:"mipsasm",name:"MIPS Assembly",aliases:["mips"],import:()=>n.e(7547).then(n.bind(n,17547))},{id:"mojo",name:"Mojo",import:()=>n.e(5808).then(n.bind(n,35808))},{id:"move",name:"Move",import:()=>n.e(166).then(n.bind(n,80166))},{id:"narrat",name:"Narrat Language",aliases:["nar"],import:()=>n.e(1957).then(n.bind(n,11957))},{id:"nextflow",name:"Nextflow",aliases:["nf"],import:()=>n.e(6250).then(n.bind(n,76250))},{id:"nginx",name:"Nginx",import:()=>Promise.all([n.e(1442),n.e(3065)]).then(n.bind(n,63065))},{id:"nim",name:"Nim",import:()=>Promise.all([n.e(2577),n.e(3640),n.e(1046),n.e(557),n.e(1442),n.e(6126),n.e(8529)]).then(n.bind(n,28529))},{id:"nix",name:"Nix",import:()=>n.e(2772).then(n.bind(n,72772))},{id:"nushell",name:"nushell",aliases:["nu"],import:()=>n.e(478).then(n.bind(n,70478))},{id:"objective-c",name:"Objective-C",aliases:["objc"],import:()=>n.e(9920).then(n.bind(n,9920))},{id:"objective-cpp",name:"Objective-C++",import:()=>n.e(4763).then(n.bind(n,49480))},{id:"ocaml",name:"OCaml",import:()=>n.e(5433).then(n.bind(n,25433))},{id:"pascal",name:"Pascal",import:()=>n.e(3361).then(n.bind(n,33361))},{id:"perl",name:"Perl",import:()=>Promise.all([n.e(2577),n.e(3640),n.e(1046),n.e(557),n.e(97),n.e(2686)]).then(n.bind(n,52686))},{id:"php",name:"PHP",import:()=>Promise.all([n.e(2577),n.e(3640),n.e(1046),n.e(557),n.e(97),n.e(9457),n.e(3958)]).then(n.bind(n,79457))},{id:"plsql",name:"PL/SQL",import:()=>n.e(8821).then(n.bind(n,8821))},{id:"po",name:"Gettext PO",aliases:["pot","potx"],import:()=>n.e(5898).then(n.bind(n,85898))},{id:"polar",name:"Polar",import:()=>n.e(1877).then(n.bind(n,81877))},{id:"postcss",name:"PostCSS",import:()=>n.e(8258).then(n.bind(n,48258))},{id:"powerquery",name:"PowerQuery",import:()=>n.e(96).then(n.bind(n,96))},{id:"powershell",name:"PowerShell",aliases:["ps","ps1"],import:()=>n.e(9278).then(n.bind(n,19278))},{id:"prisma",name:"Prisma",import:()=>n.e(4417).then(n.bind(n,64417))},{id:"prolog",name:"Prolog",import:()=>n.e(5900).then(n.bind(n,15900))},{id:"proto",name:"Protocol Buffer 3",aliases:["protobuf"],import:()=>n.e(3043).then(n.bind(n,13043))},{id:"pug",name:"Pug",aliases:["jade"],import:()=>Promise.all([n.e(2577),n.e(3640),n.e(1046),n.e(2357)]).then(n.bind(n,32357))},{id:"puppet",name:"Puppet",import:()=>n.e(2499).then(n.bind(n,62499))},{id:"purescript",name:"PureScript",import:()=>n.e(7022).then(n.bind(n,97022))},{id:"python",name:"Python",aliases:["py"],import:()=>n.e(9927).then(n.bind(n,99927))},{id:"qml",name:"QML",import:()=>Promise.all([n.e(2577),n.e(6299)]).then(n.bind(n,36299))},{id:"qmldir",name:"QML Directory",import:()=>n.e(7406).then(n.bind(n,27406))},{id:"qss",name:"Qt Style Sheets",import:()=>n.e(4462).then(n.bind(n,74462))},{id:"r",name:"R",import:()=>n.e(3195).then(n.bind(n,63195))},{id:"racket",name:"Racket",import:()=>n.e(1539).then(n.bind(n,81539))},{id:"raku",name:"Raku",aliases:["perl6"],import:()=>n.e(5870).then(n.bind(n,85870))},{id:"razor",name:"ASP.NET Razor",import:()=>Promise.all([n.e(2577),n.e(3640),n.e(1046),n.e(1264),n.e(6613)]).then(n.bind(n,6613))},{id:"reg",name:"Windows Registry Script",import:()=>n.e(2291).then(n.bind(n,72291))},{id:"regexp",name:"RegExp",aliases:["regex"],import:()=>n.e(2702).then(n.bind(n,12702))},{id:"rel",name:"Rel",import:()=>n.e(7144).then(n.bind(n,37144))},{id:"riscv",name:"RISC-V",import:()=>n.e(6396).then(n.bind(n,86396))},{id:"rst",name:"reStructuredText",import:()=>Promise.all([n.e(2577),n.e(535),n.e(9312),n.e(6814),n.e(6666),n.e(8835),n.e(3640),n.e(1046),n.e(557),n.e(97),n.e(1442),n.e(4038),n.e(8254),n.e(9927),n.e(634),n.e(4878)]).then(n.bind(n,4878))},{id:"ruby",name:"Ruby",aliases:["rb"],import:()=>Promise.all([n.e(2577),n.e(535),n.e(9312),n.e(6814),n.e(6666),n.e(8835),n.e(3640),n.e(1046),n.e(557),n.e(97),n.e(1442),n.e(4038),n.e(8254),n.e(634)]).then(n.bind(n,19397))},{id:"rust",name:"Rust",aliases:["rs"],import:()=>n.e(7819).then(n.bind(n,7819))},{id:"sas",name:"SAS",import:()=>Promise.all([n.e(97),n.e(7670)]).then(n.bind(n,97670))},{id:"sass",name:"Sass",import:()=>n.e(9687).then(n.bind(n,19687))},{id:"scala",name:"Scala",import:()=>n.e(2439).then(n.bind(n,12439))},{id:"scheme",name:"Scheme",import:()=>n.e(9712).then(n.bind(n,99712))},{id:"scss",name:"SCSS",import:()=>Promise.all([n.e(3640),n.e(8721)]).then(n.bind(n,8721))},{id:"sdbl",name:"1C (Query)",aliases:["1c-query"],import:()=>n.e(7354).then(n.bind(n,87354))},{id:"shaderlab",name:"ShaderLab",aliases:["shader"],import:()=>n.e(8517).then(n.bind(n,78517))},{id:"shellscript",name:"Shell",aliases:["bash","sh","shell","zsh"],import:()=>n.e(4038).then(n.bind(n,64038))},{id:"shellsession",name:"Shell Session",aliases:["console"],import:()=>Promise.all([n.e(4038),n.e(5659)]).then(n.bind(n,55659))},{id:"smalltalk",name:"Smalltalk",import:()=>n.e(2698).then(n.bind(n,42698))},{id:"solidity",name:"Solidity",import:()=>n.e(4906).then(n.bind(n,94906))},{id:"soy",name:"Closure Templates",aliases:["closure-templates"],import:()=>Promise.all([n.e(2577),n.e(3640),n.e(1046),n.e(9470)]).then(n.bind(n,89470))},{id:"sparql",name:"SPARQL",import:()=>n.e(1618).then(n.bind(n,81618))},{id:"splunk",name:"Splunk Query Language",aliases:["spl"],import:()=>n.e(720).then(n.bind(n,80720))},{id:"sql",name:"SQL",import:()=>n.e(97).then(n.bind(n,30097))},{id:"ssh-config",name:"SSH Config",import:()=>n.e(2116).then(n.bind(n,2116))},{id:"stata",name:"Stata",import:()=>Promise.all([n.e(97),n.e(5626)]).then(n.bind(n,75626))},{id:"stylus",name:"Stylus",aliases:["styl"],import:()=>n.e(9167).then(n.bind(n,39167))},{id:"svelte",name:"Svelte",import:()=>Promise.all([n.e(2577),n.e(535),n.e(3640),n.e(3224)]).then(n.bind(n,73224))},{id:"swift",name:"Swift",import:()=>n.e(500).then(n.bind(n,20500))},{id:"system-verilog",name:"SystemVerilog",import:()=>n.e(5887).then(n.bind(n,15887))},{id:"systemd",name:"Systemd Units",import:()=>n.e(5412).then(n.bind(n,95412))},{id:"talonscript",name:"TalonScript",aliases:["talon"],import:()=>n.e(9648).then(n.bind(n,9648))},{id:"tasl",name:"Tasl",import:()=>n.e(1193).then(n.bind(n,61193))},{id:"tcl",name:"Tcl",import:()=>n.e(3748).then(n.bind(n,53748))},{id:"templ",name:"Templ",import:()=>Promise.all([n.e(2577),n.e(3640),n.e(2827),n.e(5551)]).then(n.bind(n,95551))},{id:"terraform",name:"Terraform",aliases:["tf","tfvars"],import:()=>n.e(9797).then(n.bind(n,39797))},{id:"tex",name:"TeX",import:()=>Promise.all([n.e(3195),n.e(5918)]).then(n.bind(n,35918))},{id:"toml",name:"TOML",import:()=>n.e(7481).then(n.bind(n,87481))},{id:"ts-tags",name:"TypeScript with Tags",aliases:["lit"],import:()=>Promise.all([n.e(2577),n.e(535),n.e(3640),n.e(1046),n.e(557),n.e(97),n.e(1442),n.e(9654)]).then(n.bind(n,49654))},{id:"tsv",name:"TSV",import:()=>n.e(1590).then(n.bind(n,71590))},{id:"tsx",name:"TSX",import:()=>n.e(6814).then(n.bind(n,27744))},{id:"turtle",name:"Turtle",import:()=>n.e(4131).then(n.bind(n,94131))},{id:"twig",name:"Twig",import:()=>Promise.all([n.e(2577),n.e(535),n.e(9312),n.e(6814),n.e(6666),n.e(8835),n.e(3640),n.e(1046),n.e(557),n.e(97),n.e(1442),n.e(4038),n.e(8254),n.e(9927),n.e(634),n.e(8721),n.e(9457),n.e(7830)]).then(n.bind(n,57830))},{id:"typescript",name:"TypeScript",aliases:["ts"],import:()=>n.e(535).then(n.bind(n,60312))},{id:"typespec",name:"TypeSpec",aliases:["tsp"],import:()=>n.e(844).then(n.bind(n,20844))},{id:"typst",name:"Typst",aliases:["typ"],import:()=>n.e(8031).then(n.bind(n,70412))},{id:"v",name:"V",import:()=>n.e(9775).then(n.bind(n,59775))},{id:"vala",name:"Vala",import:()=>n.e(7355).then(n.bind(n,7355))},{id:"vb",name:"Visual Basic",aliases:["cmd"],import:()=>n.e(2085).then(n.bind(n,52085))},{id:"verilog",name:"Verilog",import:()=>n.e(7391).then(n.bind(n,87391))},{id:"vhdl",name:"VHDL",import:()=>n.e(2895).then(n.bind(n,72895))},{id:"viml",name:"Vim Script",aliases:["vim","vimscript"],import:()=>n.e(9073).then(n.bind(n,29073))},{id:"vue",name:"Vue",import:()=>Promise.all([n.e(2577),n.e(535),n.e(3640),n.e(1046),n.e(2640)]).then(n.bind(n,12640))},{id:"vue-html",name:"Vue HTML",import:()=>Promise.all([n.e(2577),n.e(535),n.e(3640),n.e(1046),n.e(7979)]).then(n.bind(n,7979))},{id:"vyper",name:"Vyper",aliases:["vy"],import:()=>n.e(5457).then(n.bind(n,45457))},{id:"wasm",name:"WebAssembly",import:()=>n.e(9949).then(n.bind(n,79949))},{id:"wenyan",name:"Wenyan",aliases:["文言"],import:()=>n.e(4283).then(n.bind(n,64283))},{id:"wgsl",name:"WGSL",import:()=>n.e(4958).then(n.bind(n,64958))},{id:"wikitext",name:"Wikitext",aliases:["mediawiki","wiki"],import:()=>n.e(3516).then(n.bind(n,53516))},{id:"wit",name:"WebAssembly Interface Types",import:()=>n.e(6285).then(n.bind(n,6285))},{id:"wolfram",name:"Wolfram",aliases:["wl"],import:()=>n.e(7442).then(n.bind(n,45729))},{id:"xml",name:"XML",import:()=>Promise.all([n.e(557),n.e(1577)]).then(n.bind(n,63958))},{id:"xsl",name:"XSL",import:()=>Promise.all([n.e(557),n.e(2112)]).then(n.bind(n,62112))},{id:"yaml",name:"YAML",aliases:["yml"],import:()=>n.e(3200).then(n.bind(n,53200))},{id:"zenscript",name:"ZenScript",import:()=>n.e(95).then(n.bind(n,80095))},{id:"zig",name:"Zig",import:()=>n.e(2303).then(n.bind(n,2303))}],t$=Object.fromEntries(tj.map(e=>[e.id,e.import])),tH=Object.fromEntries(tj.flatMap(e=>e.aliases?.map(t=>[t,e.import])||[])),tG={...t$,...tH},tU=Object.fromEntries([{id:"andromeeda",displayName:"Andromeeda",type:"dark",import:()=>n.e(646).then(n.bind(n,20646))},{id:"aurora-x",displayName:"Aurora X",type:"dark",import:()=>n.e(125).then(n.bind(n,50125))},{id:"ayu-dark",displayName:"Ayu Dark",type:"dark",import:()=>n.e(3882).then(n.bind(n,23882))},{id:"catppuccin-frappe",displayName:"Catppuccin Frapp\xe9",type:"dark",import:()=>n.e(9973).then(n.bind(n,39973))},{id:"catppuccin-latte",displayName:"Catppuccin Latte",type:"light",import:()=>n.e(5153).then(n.bind(n,95153))},{id:"catppuccin-macchiato",displayName:"Catppuccin Macchiato",type:"dark",import:()=>n.e(7530).then(n.bind(n,57530))},{id:"catppuccin-mocha",displayName:"Catppuccin Mocha",type:"dark",import:()=>n.e(6161).then(n.bind(n,6161))},{id:"dark-plus",displayName:"Dark Plus",type:"dark",import:()=>n.e(5661).then(n.bind(n,45661))},{id:"dracula",displayName:"Dracula Theme",type:"dark",import:()=>n.e(1186).then(n.bind(n,1186))},{id:"dracula-soft",displayName:"Dracula Theme Soft",type:"dark",import:()=>n.e(4209).then(n.bind(n,94209))},{id:"everforest-dark",displayName:"Everforest Dark",type:"dark",import:()=>n.e(4220).then(n.bind(n,64220))},{id:"everforest-light",displayName:"Everforest Light",type:"light",import:()=>n.e(4694).then(n.bind(n,4694))},{id:"github-dark",displayName:"GitHub Dark",type:"dark",import:()=>n.e(5602).then(n.bind(n,5602))},{id:"github-dark-default",displayName:"GitHub Dark Default",type:"dark",import:()=>n.e(4094).then(n.bind(n,44094))},{id:"github-dark-dimmed",displayName:"GitHub Dark Dimmed",type:"dark",import:()=>n.e(8125).then(n.bind(n,98125))},{id:"github-dark-high-contrast",displayName:"GitHub Dark High Contrast",type:"dark",import:()=>n.e(8986).then(n.bind(n,78986))},{id:"github-light",displayName:"GitHub Light",type:"light",import:()=>n.e(6660).then(n.bind(n,6660))},{id:"github-light-default",displayName:"GitHub Light Default",type:"light",import:()=>n.e(8788).then(n.bind(n,8788))},{id:"github-light-high-contrast",displayName:"GitHub Light High Contrast",type:"light",import:()=>n.e(6372).then(n.bind(n,96372))},{id:"gruvbox-dark-hard",displayName:"Gruvbox Dark Hard",type:"dark",import:()=>n.e(7700).then(n.bind(n,87700))},{id:"gruvbox-dark-medium",displayName:"Gruvbox Dark Medium",type:"dark",import:()=>n.e(2598).then(n.bind(n,12598))},{id:"gruvbox-dark-soft",displayName:"Gruvbox Dark Soft",type:"dark",import:()=>n.e(8435).then(n.bind(n,78435))},{id:"gruvbox-light-hard",displayName:"Gruvbox Light Hard",type:"light",import:()=>n.e(7030).then(n.bind(n,67030))},{id:"gruvbox-light-medium",displayName:"Gruvbox Light Medium",type:"light",import:()=>n.e(1468).then(n.bind(n,21468))},{id:"gruvbox-light-soft",displayName:"Gruvbox Light Soft",type:"light",import:()=>n.e(2981).then(n.bind(n,72981))},{id:"houston",displayName:"Houston",type:"dark",import:()=>n.e(1414).then(n.bind(n,31414))},{id:"kanagawa-dragon",displayName:"Kanagawa Dragon",type:"dark",import:()=>n.e(909).then(n.bind(n,20909))},{id:"kanagawa-lotus",displayName:"Kanagawa Lotus",type:"light",import:()=>n.e(2707).then(n.bind(n,82707))},{id:"kanagawa-wave",displayName:"Kanagawa Wave",type:"dark",import:()=>n.e(5211).then(n.bind(n,85211))},{id:"laserwave",displayName:"LaserWave",type:"dark",import:()=>n.e(1338).then(n.bind(n,91338))},{id:"light-plus",displayName:"Light Plus",type:"light",import:()=>n.e(6751).then(n.bind(n,56751))},{id:"material-theme",displayName:"Material Theme",type:"dark",import:()=>n.e(1321).then(n.bind(n,21321))},{id:"material-theme-darker",displayName:"Material Theme Darker",type:"dark",import:()=>n.e(5437).then(n.bind(n,5437))},{id:"material-theme-lighter",displayName:"Material Theme Lighter",type:"light",import:()=>n.e(5523).then(n.bind(n,65523))},{id:"material-theme-ocean",displayName:"Material Theme Ocean",type:"dark",import:()=>n.e(468).then(n.bind(n,468))},{id:"material-theme-palenight",displayName:"Material Theme Palenight",type:"dark",import:()=>n.e(3092).then(n.bind(n,83092))},{id:"min-dark",displayName:"Min Dark",type:"dark",import:()=>n.e(2139).then(n.bind(n,42139))},{id:"min-light",displayName:"Min Light",type:"light",import:()=>n.e(9707).then(n.bind(n,39707))},{id:"monokai",displayName:"Monokai",type:"dark",import:()=>n.e(6362).then(n.bind(n,56362))},{id:"night-owl",displayName:"Night Owl",type:"dark",import:()=>n.e(8955).then(n.bind(n,8955))},{id:"nord",displayName:"Nord",type:"dark",import:()=>n.e(5069).then(n.bind(n,65069))},{id:"one-dark-pro",displayName:"One Dark Pro",type:"dark",import:()=>n.e(1749).then(n.bind(n,31749))},{id:"one-light",displayName:"One Light",type:"light",import:()=>n.e(6919).then(n.bind(n,36919))},{id:"plastic",displayName:"Plastic",type:"dark",import:()=>n.e(32).then(n.bind(n,30032))},{id:"poimandres",displayName:"Poimandres",type:"dark",import:()=>n.e(5348).then(n.bind(n,25348))},{id:"red",displayName:"Red",type:"dark",import:()=>n.e(7673).then(n.bind(n,77673))},{id:"rose-pine",displayName:"Ros\xe9 Pine",type:"dark",import:()=>n.e(7012).then(n.bind(n,7012))},{id:"rose-pine-dawn",displayName:"Ros\xe9 Pine Dawn",type:"light",import:()=>n.e(3301).then(n.bind(n,23301))},{id:"rose-pine-moon",displayName:"Ros\xe9 Pine Moon",type:"dark",import:()=>n.e(288).then(n.bind(n,80288))},{id:"slack-dark",displayName:"Slack Dark",type:"dark",import:()=>n.e(3483).then(n.bind(n,93483))},{id:"slack-ochin",displayName:"Slack Ochin",type:"light",import:()=>n.e(5988).then(n.bind(n,95988))},{id:"snazzy-light",displayName:"Snazzy Light",type:"light",import:()=>n.e(2196).then(n.bind(n,52196))},{id:"solarized-dark",displayName:"Solarized Dark",type:"dark",import:()=>n.e(8004).then(n.bind(n,38004))},{id:"solarized-light",displayName:"Solarized Light",type:"light",import:()=>n.e(5998).then(n.bind(n,55998))},{id:"synthwave-84",displayName:"Synthwave '84",type:"dark",import:()=>n.e(2164).then(n.bind(n,62164))},{id:"tokyo-night",displayName:"Tokyo Night",type:"dark",import:()=>n.e(2015).then(n.bind(n,12015))},{id:"vesper",displayName:"Vesper",type:"dark",import:()=>n.e(9509).then(n.bind(n,29509))},{id:"vitesse-black",displayName:"Vitesse Black",type:"dark",import:()=>n.e(9371).then(n.bind(n,89371))},{id:"vitesse-dark",displayName:"Vitesse Dark",type:"dark",import:()=>n.e(3768).then(n.bind(n,93768))},{id:"vitesse-light",displayName:"Vitesse Light",type:"light",import:()=>n.e(8362).then(n.bind(n,38362))}].map(e=>[e.id,e.import]));var tq=n(44134).hp;class tW extends Error{constructor(e){super(e),this.name="ShikiError"}}function tV(){return"undefined"!=typeof performance?performance.now():Date.now()}let tQ=(e,t)=>e+(t-e%t)%t;async function tJ(e){let t,n,r={};function i(e){n=e,r.HEAPU8=new Uint8Array(e),r.HEAPU32=new Uint32Array(e)}let o="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;function a(e,t){return e?function(e,t,n=1024){let r=t+n,i=t;for(;e[i]&&!(i>=r);)++i;if(i-t>16&&e.buffer&&o)return o.decode(e.subarray(t,i));let a="";for(;t<i;){let n=e[t++];if(!(128&n)){a+=String.fromCharCode(n);continue}let r=63&e[t++];if((224&n)==192){a+=String.fromCharCode((31&n)<<6|r);continue}let i=63&e[t++];if((n=(240&n)==224?(15&n)<<12|r<<6|i:(7&n)<<18|r<<12|i<<6|63&e[t++])<65536)a+=String.fromCharCode(n);else{let e=n-65536;a+=String.fromCharCode(55296|e>>10,56320|1023&e)}}return a}(r.HEAPU8,e,t):""}let l={emscripten_get_now:tV,emscripten_memcpy_big:function(e,t,n){r.HEAPU8.copyWithin(e,t,t+n)},emscripten_resize_heap:function(e){let o=r.HEAPU8.length;if((e>>>=0)>0x80000000)return!1;for(let r=1;r<=4;r*=2){let a=o*(1+.2/r);if(a=Math.min(a,e+0x6000000),function(e){try{return t.grow(e-n.byteLength+65535>>>16),i(t.buffer),1}catch{}}(Math.min(0x80000000,tQ(Math.max(e,a),65536))))return!0}return!1},fd_write:()=>0};async function s(){let n=await e({env:l,wasi_snapshot_preview1:l});i((t=n.memory).buffer),Object.assign(r,n),r.UTF8ToString=a}return await s(),r}var tY=Object.defineProperty,tZ=(e,t,n)=>t in e?tY(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,tK=(e,t,n)=>tZ(e,"symbol"!=typeof t?t+"":t,n);let tX=null;class t0{constructor(e){tK(this,"utf16Length"),tK(this,"utf8Length"),tK(this,"utf16Value"),tK(this,"utf8Value"),tK(this,"utf16OffsetToUtf8"),tK(this,"utf8OffsetToUtf16");let t=e.length,n=t0._utf8ByteLength(e),r=n!==t,i=r?new Uint32Array(t+1):null;r&&(i[t]=n);let o=r?new Uint32Array(n+1):null;r&&(o[n]=t);let a=new Uint8Array(n),l=0;for(let n=0;n<t;n++){let s=e.charCodeAt(n),c=s,u=!1;if(s>=55296&&s<=56319&&n+1<t){let t=e.charCodeAt(n+1);t>=56320&&t<=57343&&(c=(s-55296<<10)+65536|t-56320,u=!0)}r&&(i[n]=l,u&&(i[n+1]=l),c<=127?o[l+0]=n:c<=2047?(o[l+0]=n,o[l+1]=n):c<=65535?(o[l+0]=n,o[l+1]=n,o[l+2]=n):(o[l+0]=n,o[l+1]=n,o[l+2]=n,o[l+3]=n)),c<=127?a[l++]=c:(c<=2047?a[l++]=192|(1984&c)>>>6:(c<=65535?a[l++]=224|(61440&c)>>>12:(a[l++]=240|(1835008&c)>>>18,a[l++]=128|(258048&c)>>>12),a[l++]=128|(4032&c)>>>6),a[l++]=128|(63&c)>>>0),u&&n++}this.utf16Length=t,this.utf8Length=n,this.utf16Value=e,this.utf8Value=a,this.utf16OffsetToUtf8=i,this.utf8OffsetToUtf16=o}static _utf8ByteLength(e){let t=0;for(let n=0,r=e.length;n<r;n++){let i=e.charCodeAt(n),o=i,a=!1;if(i>=55296&&i<=56319&&n+1<r){let t=e.charCodeAt(n+1);t>=56320&&t<=57343&&(o=(i-55296<<10)+65536|t-56320,a=!0)}o<=127?t+=1:o<=2047?t+=2:o<=65535?t+=3:t+=4,a&&n++}return t}createString(e){let t=e.omalloc(this.utf8Length);return e.HEAPU8.set(this.utf8Value,t),t}}let t1=class e{constructor(t){if(tK(this,"id",++e.LAST_ID),tK(this,"_onigBinding"),tK(this,"content"),tK(this,"utf16Length"),tK(this,"utf8Length"),tK(this,"utf16OffsetToUtf8"),tK(this,"utf8OffsetToUtf16"),tK(this,"ptr"),!tX)throw new tW("Must invoke loadWasm first.");this._onigBinding=tX,this.content=t;let n=new t0(t);this.utf16Length=n.utf16Length,this.utf8Length=n.utf8Length,this.utf16OffsetToUtf8=n.utf16OffsetToUtf8,this.utf8OffsetToUtf16=n.utf8OffsetToUtf16,this.utf8Length<1e4&&!e._sharedPtrInUse?(e._sharedPtr||(e._sharedPtr=tX.omalloc(1e4)),e._sharedPtrInUse=!0,tX.HEAPU8.set(n.utf8Value,e._sharedPtr),this.ptr=e._sharedPtr):this.ptr=n.createString(tX)}convertUtf8OffsetToUtf16(e){return this.utf8OffsetToUtf16?e<0?0:e>this.utf8Length?this.utf16Length:this.utf8OffsetToUtf16[e]:e}convertUtf16OffsetToUtf8(e){return this.utf16OffsetToUtf8?e<0?0:e>this.utf16Length?this.utf8Length:this.utf16OffsetToUtf8[e]:e}dispose(){this.ptr===e._sharedPtr?e._sharedPtrInUse=!1:this._onigBinding.ofree(this.ptr)}};tK(t1,"LAST_ID",0),tK(t1,"_sharedPtr",0),tK(t1,"_sharedPtrInUse",!1);class t2{constructor(e){if(tK(this,"_onigBinding"),tK(this,"_ptr"),!tX)throw new tW("Must invoke loadWasm first.");let t=[],n=[];for(let r=0,i=e.length;r<i;r++){let i=new t0(e[r]);t[r]=i.createString(tX),n[r]=i.utf8Length}let r=tX.omalloc(4*e.length);tX.HEAPU32.set(t,r/4);let i=tX.omalloc(4*e.length);tX.HEAPU32.set(n,i/4);let o=tX.createOnigScanner(r,i,e.length);for(let n=0,r=e.length;n<r;n++)tX.ofree(t[n]);tX.ofree(i),tX.ofree(r),0===o&&function(e){throw new tW(e.UTF8ToString(e.getLastOnigError()))}(tX),this._onigBinding=tX,this._ptr=o}dispose(){this._onigBinding.freeOnigScanner(this._ptr)}findNextMatchSync(e,t,n){let r=0;if("number"==typeof n&&(r=n),"string"==typeof e){e=new t1(e);let n=this._findNextMatchSync(e,t,!1,r);return e.dispose(),n}return this._findNextMatchSync(e,t,!1,r)}_findNextMatchSync(e,t,n,r){let i=this._onigBinding,o=i.findNextOnigScannerMatch(this._ptr,e.id,e.ptr,e.utf8Length,e.convertUtf16OffsetToUtf8(t),r);if(0===o)return null;let a=i.HEAPU32,l=o/4,s=a[l++],c=a[l++],u=[];for(let t=0;t<c;t++){let n=e.convertUtf8OffsetToUtf16(a[l++]),r=e.convertUtf8OffsetToUtf16(a[l++]);u[t]={start:n,end:r,length:r-n}}return{index:s,captureIndices:u}}}function t3(e){return t=>WebAssembly.instantiate(e,t)}async function t4(e){return e&&await (i||(i=async function(){tX=await tJ(async t=>{let n=e;if("function"==typeof(n=await n)&&(n=await n(t)),"function"==typeof n&&(n=await n(t)),"function"==typeof n.instantiator)n=await n.instantiator(t);else if("function"==typeof n.default)n=await n.default(t);else{var r,i,o,a;if(void 0!==n.data&&(n=n.data),r=n,"undefined"!=typeof Response&&r instanceof Response){n="function"==typeof WebAssembly.instantiateStreaming?await (o=n,e=>WebAssembly.instantiateStreaming(o,e))(t):await (a=n,async e=>{let t=await a.arrayBuffer();return WebAssembly.instantiate(t,e)})(t)}else(i=n,"undefined"!=typeof ArrayBuffer&&(i instanceof ArrayBuffer||ArrayBuffer.isView(i))||void 0!==tq&&tq.isBuffer?.(i)||"undefined"!=typeof SharedArrayBuffer&&i instanceof SharedArrayBuffer||"undefined"!=typeof Uint32Array&&i instanceof Uint32Array||n instanceof WebAssembly.Module)?n=await t3(n)(t):"default"in n&&n.default instanceof WebAssembly.Module&&(n=await t3(n.default)(t))}return"instance"in n&&(n=n.instance),"exports"in n&&(n=n.exports),n})}())),{createScanner:e=>new t2(e.map(e=>"string"==typeof e?e:e.source)),createString:e=>new t1(e)}}let{codeToHtml:t5}=function(e,t){let n,r=async function(t={}){if(!n)return n=e({...t,themes:t.themes||[],langs:t.langs||[]});{let e=await n;return await Promise.all([e.loadTheme(...t.themes||[]),e.loadLanguage(...t.langs||[])]),e}};async function i(e,n){let i=await r({langs:[n.lang],themes:"theme"in n?[n.theme]:Object.values(n.themes)}),o=await t?.guessEmbeddedLanguages?.(e,n.lang,i);return o&&await i.loadLanguage(...o),i}return{getSingletonHighlighter:e=>r(e),codeToHtml:async(e,t)=>(await i(e,t)).codeToHtml(e,t),codeToHast:async(e,t)=>(await i(e,t)).codeToHast(e,t),codeToTokens:async(e,t)=>(await i(e,t)).codeToTokens(e,t),codeToTokensBase:async(e,t)=>(await i(e,t)).codeToTokensBase(e,t),codeToTokensWithThemes:async(e,t)=>(await i(e,t)).codeToTokensWithThemes(e,t),getLastGrammarState:async(e,t)=>(await r({langs:[t.lang],themes:[t.theme]})).getLastGrammarState(e,t)}}(function(e){let t=e.langs,n=e.themes,r=e.engine;return async function(e){function i(e){if("string"==typeof e){if(to(e))return[];let n=t[e];if(!n)throw new o(`Language \`${e}\` is not included in this bundle. You may want to load it from external source.`);return n}return e}function a(e){if(ta(e))return"none";if("string"==typeof e){let t=n[e];if(!t)throw new o(`Theme \`${e}\` is not included in this bundle. You may want to load it from external source.`);return t}return e}let l=(e.themes??[]).map(e=>a(e)),s=(e.langs??[]).map(e=>i(e)),c=await tF({engine:e.engine??r(),...e,themes:l,langs:s});return{...c,loadLanguage:(...e)=>c.loadLanguage(...e.map(i)),loadTheme:(...e)=>c.loadTheme(...e.map(a)),getBundledLanguages:()=>t,getBundledThemes:()=>n}}}({langs:tG,themes:tU,engine:()=>t4(Promise.all([n.e(4814),n.e(8124)]).then(n.bind(n,38124)))}),{guessEmbeddedLanguages:function(e,t,n){let r=new Set;for(let t of e.matchAll(/lang=["']([\w-]+)["']/g))r.add(t[1]);for(let t of e.matchAll(/(?:```|~~~)([\w-]+)/g))r.add(t[1]);for(let t of e.matchAll(/\\begin\{([\w-]+)\}/g))r.add(t[1]);if(!n)return Array.from(r);let i=n.getBundledLanguages();return Array.from(r).filter(e=>e&&i[e])}})},1922:(e,t,n)=>{"use strict";n.d(t,{dc:()=>o,VG:()=>a});var r=n(17915);let i=[],o=!1;function a(e,t,n,a){let l;"function"==typeof t&&"function"!=typeof n?(a=n,n=t):l=t;let s=(0,r.C)(l),c=a?-1:1;(function e(r,l,u){let h=r&&"object"==typeof r?r:{};if("string"==typeof h.type){let e="string"==typeof h.tagName?h.tagName:"string"==typeof h.name?h.name:void 0;Object.defineProperty(p,"name",{value:"node ("+r.type+(e?"<"+e+">":"")+")"})}return p;function p(){var h;let p,d,f,m=i;if((!t||s(r,l,u[u.length-1]||void 0))&&(m=Array.isArray(h=n(r,u))?h:"number"==typeof h?[!0,h]:null==h?i:[h])[0]===o)return m;if("children"in r&&r.children&&r.children&&"skip"!==m[0])for(d=(a?r.children.length:-1)+c,f=u.concat(r);d>-1&&d<r.children.length;){if((p=e(r.children[d],d,f)())[0]===o)return p;d="number"==typeof p[1]?p[1]:d+c}return m}})(e,void 0,[])()}},4392:(e,t,n)=>{"use strict";n.d(t,{d:()=>i});let r={};function i(e,t){let n=t||r;return o(e,"boolean"!=typeof n.includeImageAlt||n.includeImageAlt,"boolean"!=typeof n.includeHtml||n.includeHtml)}function o(e,t,n){var r;if((r=e)&&"object"==typeof r){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return a(e.children,t,n)}return Array.isArray(e)?a(e,t,n):""}function a(e,t,n){let r=[],i=-1;for(;++i<e.length;)r[i]=o(e[i],t,n);return r.join("")}},7887:(e,t,n)=>{"use strict";n.d(t,{I:()=>c});var r=n(25437),i=n(68581),o=n(59739);let a=/[A-Z]/g,l=/-[a-z]/g,s=/^data[-\w.:]+$/i;function c(e,t){let n=(0,o.S)(t),c=t,p=i.R;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&s.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(l,h);c="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!l.test(e)){let n=e.replace(a,u);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}p=r.E}return new p(c,t)}function u(e){return"-"+e.toLowerCase()}function h(e){return e.charAt(1).toUpperCase()}},8918:(e,t,n)=>{"use strict";n.r(t),n.d(t,{boolean:()=>i,booleanish:()=>o,commaOrSpaceSeparated:()=>u,commaSeparated:()=>c,number:()=>l,overloadedBoolean:()=>a,spaceSeparated:()=>s});let r=0,i=h(),o=h(),a=h(),l=h(),s=h(),c=h(),u=h();function h(){return 2**++r}},8947:(e,t,n)=>{"use strict";function r(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}n.d(t,{xI:()=>ed});var i=r(),o={exec:()=>null};function a(e,t=""){let n="string"==typeof e?e:e.source,r={replace:(e,t)=>{let i="string"==typeof t?t:t.source;return i=i.replace(l.caret,"$1"),n=n.replace(e,i),r},getRegex:()=>new RegExp(n,t)};return r}var l={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:e=>RegExp(`^( {0,3}${e})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:e=>RegExp(`^ {0,${Math.min(3,e-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:e=>RegExp(`^ {0,${Math.min(3,e-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:e=>RegExp(`^ {0,${Math.min(3,e-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:e=>RegExp(`^ {0,${Math.min(3,e-1)}}#`),htmlBeginRegex:e=>RegExp(`^ {0,${Math.min(3,e-1)}}<(?:[a-z].*>|!--)`,"i")},s=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,c=/(?:[*+-]|\d{1,9}[.)])/,u=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,h=a(u).replace(/bull/g,c).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),p=a(u).replace(/bull/g,c).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),d=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,f=/(?!\s*\])(?:\\.|[^\[\]\\])+/,m=a(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",f).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),g=a(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,c).getRegex(),b="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",y=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,k=a("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",y).replace("tag",b).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),x=a(d).replace("hr",s).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",b).getRegex(),v={blockquote:a(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",x).getRegex(),code:/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,def:m,fences:/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,heading:/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,hr:s,html:k,lheading:h,list:g,newline:/^(?:[ \t]*(?:\n|$))+/,paragraph:x,table:o,text:/^[^\n]+/},w=a("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",s).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",b).getRegex(),S={...v,lheading:p,table:w,paragraph:a(d).replace("hr",s).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",w).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",b).getRegex()},_={...v,html:a(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",y).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:o,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:a(d).replace("hr",s).replace("heading"," *#{1,6} *[^\n]").replace("lheading",h).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},C=/^( {2,}|\\)\n(?!\s*$)/,P=/[\p{P}\p{S}]/u,A=/[\s\p{P}\p{S}]/u,T=/[^\s\p{P}\p{S}]/u,N=a(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,A).getRegex(),E=/(?!~)[\p{P}\p{S}]/u,L=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,I=a(L,"u").replace(/punct/g,P).getRegex(),R=a(L,"u").replace(/punct/g,E).getRegex(),O="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",D=a(O,"gu").replace(/notPunctSpace/g,T).replace(/punctSpace/g,A).replace(/punct/g,P).getRegex(),M=a(O,"gu").replace(/notPunctSpace/g,/(?:[^\s\p{P}\p{S}]|~)/u).replace(/punctSpace/g,/(?!~)[\s\p{P}\p{S}]/u).replace(/punct/g,E).getRegex(),z=a("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,T).replace(/punctSpace/g,A).replace(/punct/g,P).getRegex(),B=a(/\\(punct)/,"gu").replace(/punct/g,P).getRegex(),F=a(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),j=a(y).replace("(?:--\x3e|$)","--\x3e").getRegex(),$=a("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",j).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),H=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,G=a(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",H).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),U=a(/^!?\[(label)\]\[(ref)\]/).replace("label",H).replace("ref",f).getRegex(),q=a(/^!?\[(ref)\](?:\[\])?/).replace("ref",f).getRegex(),W=a("reflink|nolink(?!\\()","g").replace("reflink",U).replace("nolink",q).getRegex(),V={_backpedal:o,anyPunctuation:B,autolink:F,blockSkip:/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,br:C,code:/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,del:o,emStrongLDelim:I,emStrongRDelimAst:D,emStrongRDelimUnd:z,escape:/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,link:G,nolink:q,punctuation:N,reflink:U,reflinkSearch:W,tag:$,text:/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,url:o},Q={...V,link:a(/^!?\[(label)\]\((.*?)\)/).replace("label",H).getRegex(),reflink:a(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",H).getRegex()},J={...V,emStrongRDelimAst:M,emStrongLDelim:R,url:a(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Y={...J,br:a(C).replace("{2,}","*").getRegex(),text:a(J.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},Z={normal:v,gfm:S,pedantic:_},K={normal:V,gfm:J,breaks:Y,pedantic:Q},X={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},ee=e=>X[e];function et(e,t){if(t){if(l.escapeTest.test(e))return e.replace(l.escapeReplace,ee)}else if(l.escapeTestNoEncode.test(e))return e.replace(l.escapeReplaceNoEncode,ee);return e}function en(e){try{e=encodeURI(e).replace(l.percentDecode,"%")}catch{return null}return e}function er(e,t){let n=e.replace(l.findPipe,(e,t,n)=>{let r=!1,i=t;for(;--i>=0&&"\\"===n[i];)r=!r;return r?"|":" |"}).split(l.splitPipe),r=0;if(n[0].trim()||n.shift(),n.length>0&&!n.at(-1)?.trim()&&n.pop(),t)if(n.length>t)n.splice(t);else for(;n.length<t;)n.push("");for(;r<n.length;r++)n[r]=n[r].trim().replace(l.slashPipe,"|");return n}function ei(e,t,n){let r=e.length;if(0===r)return"";let i=0;for(;i<r;){let o=e.charAt(r-i-1);if(o!==t||n)if(o!==t&&n)i++;else break;else i++}return e.slice(0,r-i)}function eo(e,t,n,r,i){let o=t.href,a=t.title||null,l=e[1].replace(i.other.outputLinkReplace,"$1");r.state.inLink=!0;let s={type:"!"===e[0].charAt(0)?"image":"link",raw:n,href:o,title:a,text:l,tokens:r.inlineTokens(l)};return r.state.inLink=!1,s}var ea=class{options;rules;lexer;constructor(e){this.options=e||i}space(e){let t=this.rules.block.newline.exec(e);if(t&&t[0].length>0)return{type:"space",raw:t[0]}}code(e){let t=this.rules.block.code.exec(e);if(t){let e=t[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:t[0],codeBlockStyle:"indented",text:this.options.pedantic?e:ei(e,"\n")}}}fences(e){let t=this.rules.block.fences.exec(e);if(t){let e=t[0],n=function(e,t,n){let r=e.match(n.other.indentCodeCompensation);if(null===r)return t;let i=r[1];return t.split("\n").map(e=>{let t=e.match(n.other.beginningSpace);if(null===t)return e;let[r]=t;return r.length>=i.length?e.slice(i.length):e}).join("\n")}(e,t[3]||"",this.rules);return{type:"code",raw:e,lang:t[2]?t[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):t[2],text:n}}}heading(e){let t=this.rules.block.heading.exec(e);if(t){let e=t[2].trim();if(this.rules.other.endingHash.test(e)){let t=ei(e,"#");this.options.pedantic?e=t.trim():(!t||this.rules.other.endingSpaceChar.test(t))&&(e=t.trim())}return{type:"heading",raw:t[0],depth:t[1].length,text:e,tokens:this.lexer.inline(e)}}}hr(e){let t=this.rules.block.hr.exec(e);if(t)return{type:"hr",raw:ei(t[0],"\n")}}blockquote(e){let t=this.rules.block.blockquote.exec(e);if(t){let e=ei(t[0],"\n").split("\n"),n="",r="",i=[];for(;e.length>0;){let t,o=!1,a=[];for(t=0;t<e.length;t++)if(this.rules.other.blockquoteStart.test(e[t]))a.push(e[t]),o=!0;else if(o)break;else a.push(e[t]);e=e.slice(t);let l=a.join("\n"),s=l.replace(this.rules.other.blockquoteSetextReplace,"\n    $1").replace(this.rules.other.blockquoteSetextReplace2,"");n=n?`${n}
${l}`:l,r=r?`${r}
${s}`:s;let c=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(s,i,!0),this.lexer.state.top=c,0===e.length)break;let u=i.at(-1);if(u?.type==="code")break;if(u?.type==="blockquote"){let t=u.raw+"\n"+e.join("\n"),o=this.blockquote(t);i[i.length-1]=o,n=n.substring(0,n.length-u.raw.length)+o.raw,r=r.substring(0,r.length-u.text.length)+o.text;break}if(u?.type==="list"){let t=u.raw+"\n"+e.join("\n"),o=this.list(t);i[i.length-1]=o,n=n.substring(0,n.length-u.raw.length)+o.raw,r=r.substring(0,r.length-u.raw.length)+o.raw,e=t.substring(i.at(-1).raw.length).split("\n");continue}}return{type:"blockquote",raw:n,tokens:i,text:r}}}list(e){let t=this.rules.block.list.exec(e);if(t){let n=t[1].trim(),r=n.length>1,i={type:"list",raw:"",ordered:r,start:r?+n.slice(0,-1):"",loose:!1,items:[]};n=r?`\\d{1,9}\\${n.slice(-1)}`:`\\${n}`,this.options.pedantic&&(n=r?n:"[*+-]");let o=this.rules.other.listItemRegex(n),a=!1;for(;e;){let n,r=!1,l="",s="";if(!(t=o.exec(e))||this.rules.block.hr.test(e))break;l=t[0],e=e.substring(l.length);let c=t[2].split("\n",1)[0].replace(this.rules.other.listReplaceTabs,e=>" ".repeat(3*e.length)),u=e.split("\n",1)[0],h=!c.trim(),p=0;if(this.options.pedantic?(p=2,s=c.trimStart()):h?p=t[1].length+1:(p=(p=t[2].search(this.rules.other.nonSpaceChar))>4?1:p,s=c.slice(p),p+=t[1].length),h&&this.rules.other.blankLine.test(u)&&(l+=u+"\n",e=e.substring(u.length+1),r=!0),!r){let t=this.rules.other.nextBulletRegex(p),n=this.rules.other.hrRegex(p),r=this.rules.other.fencesBeginRegex(p),i=this.rules.other.headingBeginRegex(p),o=this.rules.other.htmlBeginRegex(p);for(;e;){let a,d=e.split("\n",1)[0];if(u=d,a=this.options.pedantic?u=u.replace(this.rules.other.listReplaceNesting,"  "):u.replace(this.rules.other.tabCharGlobal,"    "),r.test(u)||i.test(u)||o.test(u)||t.test(u)||n.test(u))break;if(a.search(this.rules.other.nonSpaceChar)>=p||!u.trim())s+="\n"+a.slice(p);else{if(h||c.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||r.test(c)||i.test(c)||n.test(c))break;s+="\n"+u}h||u.trim()||(h=!0),l+=d+"\n",e=e.substring(d.length+1),c=a.slice(p)}}!i.loose&&(a?i.loose=!0:this.rules.other.doubleBlankLine.test(l)&&(a=!0));let d=null;this.options.gfm&&(d=this.rules.other.listIsTask.exec(s))&&(n="[ ] "!==d[0],s=s.replace(this.rules.other.listReplaceTask,"")),i.items.push({type:"list_item",raw:l,task:!!d,checked:n,loose:!1,text:s,tokens:[]}),i.raw+=l}let l=i.items.at(-1);if(!l)return;l.raw=l.raw.trimEnd(),l.text=l.text.trimEnd(),i.raw=i.raw.trimEnd();for(let e=0;e<i.items.length;e++)if(this.lexer.state.top=!1,i.items[e].tokens=this.lexer.blockTokens(i.items[e].text,[]),!i.loose){let t=i.items[e].tokens.filter(e=>"space"===e.type);i.loose=t.length>0&&t.some(e=>this.rules.other.anyLine.test(e.raw))}if(i.loose)for(let e=0;e<i.items.length;e++)i.items[e].loose=!0;return i}}html(e){let t=this.rules.block.html.exec(e);if(t)return{type:"html",block:!0,raw:t[0],pre:"pre"===t[1]||"script"===t[1]||"style"===t[1],text:t[0]}}def(e){let t=this.rules.block.def.exec(e);if(t){let e=t[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),n=t[2]?t[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",r=t[3]?t[3].substring(1,t[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):t[3];return{type:"def",tag:e,raw:t[0],href:n,title:r}}}table(e){let t=this.rules.block.table.exec(e);if(!t||!this.rules.other.tableDelimiter.test(t[2]))return;let n=er(t[1]),r=t[2].replace(this.rules.other.tableAlignChars,"").split("|"),i=t[3]?.trim()?t[3].replace(this.rules.other.tableRowBlankLine,"").split("\n"):[],o={type:"table",raw:t[0],header:[],align:[],rows:[]};if(n.length===r.length){for(let e of r)this.rules.other.tableAlignRight.test(e)?o.align.push("right"):this.rules.other.tableAlignCenter.test(e)?o.align.push("center"):this.rules.other.tableAlignLeft.test(e)?o.align.push("left"):o.align.push(null);for(let e=0;e<n.length;e++)o.header.push({text:n[e],tokens:this.lexer.inline(n[e]),header:!0,align:o.align[e]});for(let e of i)o.rows.push(er(e,o.header.length).map((e,t)=>({text:e,tokens:this.lexer.inline(e),header:!1,align:o.align[t]})));return o}}lheading(e){let t=this.rules.block.lheading.exec(e);if(t)return{type:"heading",raw:t[0],depth:"="===t[2].charAt(0)?1:2,text:t[1],tokens:this.lexer.inline(t[1])}}paragraph(e){let t=this.rules.block.paragraph.exec(e);if(t){let e="\n"===t[1].charAt(t[1].length-1)?t[1].slice(0,-1):t[1];return{type:"paragraph",raw:t[0],text:e,tokens:this.lexer.inline(e)}}}text(e){let t=this.rules.block.text.exec(e);if(t)return{type:"text",raw:t[0],text:t[0],tokens:this.lexer.inline(t[0])}}escape(e){let t=this.rules.inline.escape.exec(e);if(t)return{type:"escape",raw:t[0],text:t[1]}}tag(e){let t=this.rules.inline.tag.exec(e);if(t)return!this.lexer.state.inLink&&this.rules.other.startATag.test(t[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(t[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(t[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(t[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:t[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:t[0]}}link(e){let t=this.rules.inline.link.exec(e);if(t){let e=t[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(e)){if(!this.rules.other.endAngleBracket.test(e))return;let t=ei(e.slice(0,-1),"\\");if((e.length-t.length)%2==0)return}else{let e=function(e,t){if(-1===e.indexOf(")"))return -1;let n=0;for(let r=0;r<e.length;r++)if("\\"===e[r])r++;else if("("===e[r])n++;else if(e[r]===t[1]&&--n<0)return r;return n>0?-2:-1}(t[2],"()");if(-2===e)return;if(e>-1){let n=(0===t[0].indexOf("!")?5:4)+t[1].length+e;t[2]=t[2].substring(0,e),t[0]=t[0].substring(0,n).trim(),t[3]=""}}let n=t[2],r="";if(this.options.pedantic){let e=this.rules.other.pedanticHrefTitle.exec(n);e&&(n=e[1],r=e[3])}else r=t[3]?t[3].slice(1,-1):"";return n=n.trim(),this.rules.other.startAngleBracket.test(n)&&(n=this.options.pedantic&&!this.rules.other.endAngleBracket.test(e)?n.slice(1):n.slice(1,-1)),eo(t,{href:n?n.replace(this.rules.inline.anyPunctuation,"$1"):n,title:r?r.replace(this.rules.inline.anyPunctuation,"$1"):r},t[0],this.lexer,this.rules)}}reflink(e,t){let n;if((n=this.rules.inline.reflink.exec(e))||(n=this.rules.inline.nolink.exec(e))){let e=t[(n[2]||n[1]).replace(this.rules.other.multipleSpaceGlobal," ").toLowerCase()];if(!e){let e=n[0].charAt(0);return{type:"text",raw:e,text:e}}return eo(n,e,n[0],this.lexer,this.rules)}}emStrong(e,t,n=""){let r=this.rules.inline.emStrongLDelim.exec(e);if(!(!r||r[3]&&n.match(this.rules.other.unicodeAlphaNumeric))&&(!(r[1]||r[2])||!n||this.rules.inline.punctuation.exec(n))){let n=[...r[0]].length-1,i,o,a=n,l=0,s="*"===r[0][0]?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(s.lastIndex=0,t=t.slice(-1*e.length+n);null!=(r=s.exec(t));){if(!(i=r[1]||r[2]||r[3]||r[4]||r[5]||r[6]))continue;if(o=[...i].length,r[3]||r[4]){a+=o;continue}if((r[5]||r[6])&&n%3&&!((n+o)%3)){l+=o;continue}if((a-=o)>0)continue;o=Math.min(o,o+a+l);let t=[...r[0]][0].length,s=e.slice(0,n+r.index+t+o);if(Math.min(n,o)%2){let e=s.slice(1,-1);return{type:"em",raw:s,text:e,tokens:this.lexer.inlineTokens(e)}}let c=s.slice(2,-2);return{type:"strong",raw:s,text:c,tokens:this.lexer.inlineTokens(c)}}}}codespan(e){let t=this.rules.inline.code.exec(e);if(t){let e=t[2].replace(this.rules.other.newLineCharGlobal," "),n=this.rules.other.nonSpaceChar.test(e),r=this.rules.other.startingSpaceChar.test(e)&&this.rules.other.endingSpaceChar.test(e);return n&&r&&(e=e.substring(1,e.length-1)),{type:"codespan",raw:t[0],text:e}}}br(e){let t=this.rules.inline.br.exec(e);if(t)return{type:"br",raw:t[0]}}del(e){let t=this.rules.inline.del.exec(e);if(t)return{type:"del",raw:t[0],text:t[2],tokens:this.lexer.inlineTokens(t[2])}}autolink(e){let t=this.rules.inline.autolink.exec(e);if(t){let e,n;return n="@"===t[2]?"mailto:"+(e=t[1]):e=t[1],{type:"link",raw:t[0],text:e,href:n,tokens:[{type:"text",raw:e,text:e}]}}}url(e){let t;if(t=this.rules.inline.url.exec(e)){let e,n;if("@"===t[2])n="mailto:"+(e=t[0]);else{let r;do r=t[0],t[0]=this.rules.inline._backpedal.exec(t[0])?.[0]??"";while(r!==t[0]);e=t[0],n="www."===t[1]?"http://"+t[0]:t[0]}return{type:"link",raw:t[0],text:e,href:n,tokens:[{type:"text",raw:e,text:e}]}}}inlineText(e){let t=this.rules.inline.text.exec(e);if(t){let e=this.lexer.state.inRawBlock;return{type:"text",raw:t[0],text:t[0],escaped:e}}}},el=class e{tokens;options;state;tokenizer;inlineQueue;constructor(e){this.tokens=[],this.tokens.links=Object.create(null),this.options=e||i,this.options.tokenizer=this.options.tokenizer||new ea,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};let t={other:l,block:Z.normal,inline:K.normal};this.options.pedantic?(t.block=Z.pedantic,t.inline=K.pedantic):this.options.gfm&&(t.block=Z.gfm,this.options.breaks?t.inline=K.breaks:t.inline=K.gfm),this.tokenizer.rules=t}static get rules(){return{block:Z,inline:K}}static lex(t,n){return new e(n).lex(t)}static lexInline(t,n){return new e(n).inlineTokens(t)}lex(e){e=e.replace(l.carriageReturn,"\n"),this.blockTokens(e,this.tokens);for(let e=0;e<this.inlineQueue.length;e++){let t=this.inlineQueue[e];this.inlineTokens(t.src,t.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[],n=!1){for(this.options.pedantic&&(e=e.replace(l.tabCharGlobal,"    ").replace(l.spaceLine,""));e;){let r;if(this.options.extensions?.block?.some(n=>!!(r=n.call({lexer:this},e,t))&&(e=e.substring(r.raw.length),t.push(r),!0)))continue;if(r=this.tokenizer.space(e)){e=e.substring(r.raw.length);let n=t.at(-1);1===r.raw.length&&void 0!==n?n.raw+="\n":t.push(r);continue}if(r=this.tokenizer.code(e)){e=e.substring(r.raw.length);let n=t.at(-1);n?.type==="paragraph"||n?.type==="text"?(n.raw+="\n"+r.raw,n.text+="\n"+r.text,this.inlineQueue.at(-1).src=n.text):t.push(r);continue}if((r=this.tokenizer.fences(e))||(r=this.tokenizer.heading(e))||(r=this.tokenizer.hr(e))||(r=this.tokenizer.blockquote(e))||(r=this.tokenizer.list(e))||(r=this.tokenizer.html(e))){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.def(e)){e=e.substring(r.raw.length);let n=t.at(-1);n?.type==="paragraph"||n?.type==="text"?(n.raw+="\n"+r.raw,n.text+="\n"+r.raw,this.inlineQueue.at(-1).src=n.text):this.tokens.links[r.tag]||(this.tokens.links[r.tag]={href:r.href,title:r.title});continue}if((r=this.tokenizer.table(e))||(r=this.tokenizer.lheading(e))){e=e.substring(r.raw.length),t.push(r);continue}let i=e;if(this.options.extensions?.startBlock){let t,n=1/0,r=e.slice(1);this.options.extensions.startBlock.forEach(e=>{"number"==typeof(t=e.call({lexer:this},r))&&t>=0&&(n=Math.min(n,t))}),n<1/0&&n>=0&&(i=e.substring(0,n+1))}if(this.state.top&&(r=this.tokenizer.paragraph(i))){let o=t.at(-1);n&&o?.type==="paragraph"?(o.raw+="\n"+r.raw,o.text+="\n"+r.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=o.text):t.push(r),n=i.length!==e.length,e=e.substring(r.raw.length);continue}if(r=this.tokenizer.text(e)){e=e.substring(r.raw.length);let n=t.at(-1);n?.type==="text"?(n.raw+="\n"+r.raw,n.text+="\n"+r.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=n.text):t.push(r);continue}if(e){let t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}throw Error(t)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){let n=e,r=null;if(this.tokens.links){let e=Object.keys(this.tokens.links);if(e.length>0)for(;null!=(r=this.tokenizer.rules.inline.reflinkSearch.exec(n));)e.includes(r[0].slice(r[0].lastIndexOf("[")+1,-1))&&(n=n.slice(0,r.index)+"["+"a".repeat(r[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;null!=(r=this.tokenizer.rules.inline.anyPunctuation.exec(n));)n=n.slice(0,r.index)+"++"+n.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;null!=(r=this.tokenizer.rules.inline.blockSkip.exec(n));)n=n.slice(0,r.index)+"["+"a".repeat(r[0].length-2)+"]"+n.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let i=!1,o="";for(;e;){let r;if(i||(o=""),i=!1,this.options.extensions?.inline?.some(n=>!!(r=n.call({lexer:this},e,t))&&(e=e.substring(r.raw.length),t.push(r),!0)))continue;if((r=this.tokenizer.escape(e))||(r=this.tokenizer.tag(e))||(r=this.tokenizer.link(e))){e=e.substring(r.raw.length),t.push(r);continue}if(r=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(r.raw.length);let n=t.at(-1);"text"===r.type&&n?.type==="text"?(n.raw+=r.raw,n.text+=r.text):t.push(r);continue}if((r=this.tokenizer.emStrong(e,n,o))||(r=this.tokenizer.codespan(e))||(r=this.tokenizer.br(e))||(r=this.tokenizer.del(e))||(r=this.tokenizer.autolink(e))||!this.state.inLink&&(r=this.tokenizer.url(e))){e=e.substring(r.raw.length),t.push(r);continue}let a=e;if(this.options.extensions?.startInline){let t,n=1/0,r=e.slice(1);this.options.extensions.startInline.forEach(e=>{"number"==typeof(t=e.call({lexer:this},r))&&t>=0&&(n=Math.min(n,t))}),n<1/0&&n>=0&&(a=e.substring(0,n+1))}if(r=this.tokenizer.inlineText(a)){e=e.substring(r.raw.length),"_"!==r.raw.slice(-1)&&(o=r.raw.slice(-1)),i=!0;let n=t.at(-1);n?.type==="text"?(n.raw+=r.raw,n.text+=r.text):t.push(r);continue}if(e){let t="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(t);break}throw Error(t)}}return t}},es=class{options;parser;constructor(e){this.options=e||i}space(e){return""}code({text:e,lang:t,escaped:n}){let r=(t||"").match(l.notSpaceStart)?.[0],i=e.replace(l.endingNewline,"")+"\n";return r?'<pre><code class="language-'+et(r)+'">'+(n?i:et(i,!0))+"</code></pre>\n":"<pre><code>"+(n?i:et(i,!0))+"</code></pre>\n"}blockquote({tokens:e}){let t=this.parser.parse(e);return`<blockquote>
${t}</blockquote>
`}html({text:e}){return e}heading({tokens:e,depth:t}){return`<h${t}>${this.parser.parseInline(e)}</h${t}>
`}hr(e){return"<hr>\n"}list(e){let t=e.ordered,n=e.start,r="";for(let t=0;t<e.items.length;t++){let n=e.items[t];r+=this.listitem(n)}let i=t?"ol":"ul";return"<"+i+(t&&1!==n?' start="'+n+'"':"")+">\n"+r+"</"+i+">\n"}listitem(e){let t="";if(e.task){let n=this.checkbox({checked:!!e.checked});e.loose?e.tokens[0]?.type==="paragraph"?(e.tokens[0].text=n+" "+e.tokens[0].text,e.tokens[0].tokens&&e.tokens[0].tokens.length>0&&"text"===e.tokens[0].tokens[0].type&&(e.tokens[0].tokens[0].text=n+" "+et(e.tokens[0].tokens[0].text),e.tokens[0].tokens[0].escaped=!0)):e.tokens.unshift({type:"text",raw:n+" ",text:n+" ",escaped:!0}):t+=n+" "}return t+=this.parser.parse(e.tokens,!!e.loose),`<li>${t}</li>
`}checkbox({checked:e}){return"<input "+(e?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:e}){return`<p>${this.parser.parseInline(e)}</p>
`}table(e){let t="",n="";for(let t=0;t<e.header.length;t++)n+=this.tablecell(e.header[t]);t+=this.tablerow({text:n});let r="";for(let t=0;t<e.rows.length;t++){let i=e.rows[t];n="";for(let e=0;e<i.length;e++)n+=this.tablecell(i[e]);r+=this.tablerow({text:n})}return r&&(r=`<tbody>${r}</tbody>`),"<table>\n<thead>\n"+t+"</thead>\n"+r+"</table>\n"}tablerow({text:e}){return`<tr>
${e}</tr>
`}tablecell(e){let t=this.parser.parseInline(e.tokens),n=e.header?"th":"td";return(e.align?`<${n} align="${e.align}">`:`<${n}>`)+t+`</${n}>
`}strong({tokens:e}){return`<strong>${this.parser.parseInline(e)}</strong>`}em({tokens:e}){return`<em>${this.parser.parseInline(e)}</em>`}codespan({text:e}){return`<code>${et(e,!0)}</code>`}br(e){return"<br>"}del({tokens:e}){return`<del>${this.parser.parseInline(e)}</del>`}link({href:e,title:t,tokens:n}){let r=this.parser.parseInline(n),i=en(e);if(null===i)return r;let o='<a href="'+(e=i)+'"';return t&&(o+=' title="'+et(t)+'"'),o+=">"+r+"</a>"}image({href:e,title:t,text:n,tokens:r}){r&&(n=this.parser.parseInline(r,this.parser.textRenderer));let i=en(e);if(null===i)return et(n);e=i;let o=`<img src="${e}" alt="${n}"`;return t&&(o+=` title="${et(t)}"`),o+=">"}text(e){return"tokens"in e&&e.tokens?this.parser.parseInline(e.tokens):"escaped"in e&&e.escaped?e.text:et(e.text)}},ec=class{strong({text:e}){return e}em({text:e}){return e}codespan({text:e}){return e}del({text:e}){return e}html({text:e}){return e}text({text:e}){return e}link({text:e}){return""+e}image({text:e}){return""+e}br(){return""}},eu=class e{options;renderer;textRenderer;constructor(e){this.options=e||i,this.options.renderer=this.options.renderer||new es,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new ec}static parse(t,n){return new e(n).parse(t)}static parseInline(t,n){return new e(n).parseInline(t)}parse(e,t=!0){let n="";for(let r=0;r<e.length;r++){let i=e[r];if(this.options.extensions?.renderers?.[i.type]){let e=this.options.extensions.renderers[i.type].call({parser:this},i);if(!1!==e||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(i.type)){n+=e||"";continue}}switch(i.type){case"space":n+=this.renderer.space(i);continue;case"hr":n+=this.renderer.hr(i);continue;case"heading":n+=this.renderer.heading(i);continue;case"code":n+=this.renderer.code(i);continue;case"table":n+=this.renderer.table(i);continue;case"blockquote":n+=this.renderer.blockquote(i);continue;case"list":n+=this.renderer.list(i);continue;case"html":n+=this.renderer.html(i);continue;case"paragraph":n+=this.renderer.paragraph(i);continue;case"text":{let o=i,a=this.renderer.text(o);for(;r+1<e.length&&"text"===e[r+1].type;)o=e[++r],a+="\n"+this.renderer.text(o);t?n+=this.renderer.paragraph({type:"paragraph",raw:a,text:a,tokens:[{type:"text",raw:a,text:a,escaped:!0}]}):n+=a;continue}default:{let e='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(e),"";throw Error(e)}}}return n}parseInline(e,t=this.renderer){let n="";for(let r=0;r<e.length;r++){let i=e[r];if(this.options.extensions?.renderers?.[i.type]){let e=this.options.extensions.renderers[i.type].call({parser:this},i);if(!1!==e||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(i.type)){n+=e||"";continue}}switch(i.type){case"escape":case"text":n+=t.text(i);break;case"html":n+=t.html(i);break;case"link":n+=t.link(i);break;case"image":n+=t.image(i);break;case"strong":n+=t.strong(i);break;case"em":n+=t.em(i);break;case"codespan":n+=t.codespan(i);break;case"br":n+=t.br(i);break;case"del":n+=t.del(i);break;default:{let e='Token with "'+i.type+'" type was not found.';if(this.options.silent)return console.error(e),"";throw Error(e)}}}return n}},eh=class{options;block;constructor(e){this.options=e||i}static passThroughHooks=new Set(["preprocess","postprocess","processAllTokens"]);preprocess(e){return e}postprocess(e){return e}processAllTokens(e){return e}provideLexer(){return this.block?el.lex:el.lexInline}provideParser(){return this.block?eu.parse:eu.parseInline}},ep=new class{defaults=r();options=this.setOptions;parse=this.parseMarkdown(!0);parseInline=this.parseMarkdown(!1);Parser=eu;Renderer=es;TextRenderer=ec;Lexer=el;Tokenizer=ea;Hooks=eh;constructor(...e){this.use(...e)}walkTokens(e,t){let n=[];for(let r of e)switch(n=n.concat(t.call(this,r)),r.type){case"table":for(let e of r.header)n=n.concat(this.walkTokens(e.tokens,t));for(let e of r.rows)for(let r of e)n=n.concat(this.walkTokens(r.tokens,t));break;case"list":n=n.concat(this.walkTokens(r.items,t));break;default:{let e=r;this.defaults.extensions?.childTokens?.[e.type]?this.defaults.extensions.childTokens[e.type].forEach(r=>{let i=e[r].flat(1/0);n=n.concat(this.walkTokens(i,t))}):e.tokens&&(n=n.concat(this.walkTokens(e.tokens,t)))}}return n}use(...e){let t=this.defaults.extensions||{renderers:{},childTokens:{}};return e.forEach(e=>{let n={...e};if(n.async=this.defaults.async||n.async||!1,e.extensions&&(e.extensions.forEach(e=>{if(!e.name)throw Error("extension name required");if("renderer"in e){let n=t.renderers[e.name];n?t.renderers[e.name]=function(...t){let r=e.renderer.apply(this,t);return!1===r&&(r=n.apply(this,t)),r}:t.renderers[e.name]=e.renderer}if("tokenizer"in e){if(!e.level||"block"!==e.level&&"inline"!==e.level)throw Error("extension level must be 'block' or 'inline'");let n=t[e.level];n?n.unshift(e.tokenizer):t[e.level]=[e.tokenizer],e.start&&("block"===e.level?t.startBlock?t.startBlock.push(e.start):t.startBlock=[e.start]:"inline"===e.level&&(t.startInline?t.startInline.push(e.start):t.startInline=[e.start]))}"childTokens"in e&&e.childTokens&&(t.childTokens[e.name]=e.childTokens)}),n.extensions=t),e.renderer){let t=this.defaults.renderer||new es(this.defaults);for(let n in e.renderer){if(!(n in t))throw Error(`renderer '${n}' does not exist`);if(["options","parser"].includes(n))continue;let r=e.renderer[n],i=t[n];t[n]=(...e)=>{let n=r.apply(t,e);return!1===n&&(n=i.apply(t,e)),n||""}}n.renderer=t}if(e.tokenizer){let t=this.defaults.tokenizer||new ea(this.defaults);for(let n in e.tokenizer){if(!(n in t))throw Error(`tokenizer '${n}' does not exist`);if(["options","rules","lexer"].includes(n))continue;let r=e.tokenizer[n],i=t[n];t[n]=(...e)=>{let n=r.apply(t,e);return!1===n&&(n=i.apply(t,e)),n}}n.tokenizer=t}if(e.hooks){let t=this.defaults.hooks||new eh;for(let n in e.hooks){if(!(n in t))throw Error(`hook '${n}' does not exist`);if(["options","block"].includes(n))continue;let r=e.hooks[n],i=t[n];eh.passThroughHooks.has(n)?t[n]=e=>{if(this.defaults.async)return Promise.resolve(r.call(t,e)).then(e=>i.call(t,e));let n=r.call(t,e);return i.call(t,n)}:t[n]=(...e)=>{let n=r.apply(t,e);return!1===n&&(n=i.apply(t,e)),n}}n.hooks=t}if(e.walkTokens){let t=this.defaults.walkTokens,r=e.walkTokens;n.walkTokens=function(e){let n=[];return n.push(r.call(this,e)),t&&(n=n.concat(t.call(this,e))),n}}this.defaults={...this.defaults,...n}}),this}setOptions(e){return this.defaults={...this.defaults,...e},this}lexer(e,t){return el.lex(e,t??this.defaults)}parser(e,t){return eu.parse(e,t??this.defaults)}parseMarkdown(e){return(t,n)=>{let r={...n},i={...this.defaults,...r},o=this.onError(!!i.silent,!!i.async);if(!0===this.defaults.async&&!1===r.async)return o(Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(null==t)return o(Error("marked(): input parameter is undefined or null"));if("string"!=typeof t)return o(Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));i.hooks&&(i.hooks.options=i,i.hooks.block=e);let a=i.hooks?i.hooks.provideLexer():e?el.lex:el.lexInline,l=i.hooks?i.hooks.provideParser():e?eu.parse:eu.parseInline;if(i.async)return Promise.resolve(i.hooks?i.hooks.preprocess(t):t).then(e=>a(e,i)).then(e=>i.hooks?i.hooks.processAllTokens(e):e).then(e=>i.walkTokens?Promise.all(this.walkTokens(e,i.walkTokens)).then(()=>e):e).then(e=>l(e,i)).then(e=>i.hooks?i.hooks.postprocess(e):e).catch(o);try{i.hooks&&(t=i.hooks.preprocess(t));let e=a(t,i);i.hooks&&(e=i.hooks.processAllTokens(e)),i.walkTokens&&this.walkTokens(e,i.walkTokens);let n=l(e,i);return i.hooks&&(n=i.hooks.postprocess(n)),n}catch(e){return o(e)}}}onError(e,t){return n=>{if(n.message+="\nPlease report this to https://github.com/markedjs/marked.",e){let e="<p>An error occurred:</p><pre>"+et(n.message+"",!0)+"</pre>";return t?Promise.resolve(e):e}if(t)return Promise.reject(n);throw n}}};function ed(e,t){return ep.parse(e,t)}ed.options=ed.setOptions=function(e){return ep.setOptions(e),ed.defaults=ep.defaults,i=ed.defaults,ed},ed.getDefaults=r,ed.defaults=i,ed.use=function(...e){return ep.use(...e),ed.defaults=ep.defaults,i=ed.defaults,ed},ed.walkTokens=function(e,t){return ep.walkTokens(e,t)},ed.parseInline=ep.parseInline,ed.Parser=eu,ed.parser=eu.parse,ed.Renderer=es,ed.TextRenderer=ec,ed.Lexer=el,ed.lexer=el.lex,ed.Tokenizer=ea,ed.Hooks=eh,ed.parse=ed,ed.options,ed.setOptions,ed.use,ed.walkTokens,ed.parseInline,eu.parse,el.lex},8978:(e,t,n)=>{"use strict";n.d(t,{b:()=>r});let r=["area","base","basefont","bgsound","br","col","command","embed","frame","hr","image","img","input","keygen","link","meta","param","source","track","wbr"]},11603:(e,t,n)=>{"use strict";function r(e,t,n,r){let i,o=e.length,a=0;if(t=t<0?-t>o?0:o+t:t>o?o:t,n=n>0?n:0,r.length<1e4)(i=Array.from(r)).unshift(t,n),e.splice(...i);else for(n&&e.splice(t,n);a<r.length;)(i=r.slice(a,a+1e4)).unshift(t,0),e.splice(...i),a+=1e4,t+=1e4}function i(e,t){return e.length>0?(r(e,e.length,0,t),e):t}n.d(t,{V:()=>i,m:()=>r})},12556:(e,t,n)=>{"use strict";n.d(t,{BM:()=>l,CW:()=>r,Ee:()=>h,HP:()=>u,JQ:()=>a,Ny:()=>f,On:()=>p,cx:()=>o,es:()=>d,lV:()=>i,ok:()=>s,ol:()=>c});let r=m(/[A-Za-z]/),i=m(/[\dA-Za-z]/),o=m(/[#-'*+\--9=?A-Z^-~]/);function a(e){return null!==e&&(e<32||127===e)}let l=m(/\d/),s=m(/[\dA-Fa-f]/),c=m(/[!-/:-@[-`{-~]/);function u(e){return null!==e&&e<-2}function h(e){return null!==e&&(e<0||32===e)}function p(e){return -2===e||-1===e||32===e}let d=m(/\p{P}|\p{S}/u),f=m(/\s/);function m(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}},14947:(e,t,n)=>{"use strict";function r(e){let t=String(e||"").trim();return t?t.split(/[ \t\n\r\f]+/g):[]}function i(e){return e.join(" ").trim()}n.d(t,{A:()=>i,q:()=>r})},17915:(e,t,n)=>{"use strict";n.d(t,{C:()=>r});let r=function(e){var t,n;if(null==e)return o;if("function"==typeof e)return i(e);if("object"==typeof e){return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=r(e[n]);return i(function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1})}(e):(t=e,i(function(e){let n;for(n in t)if(e[n]!==t[n])return!1;return!0}))}if("string"==typeof e){return n=e,i(function(e){return e&&e.type===n})}throw Error("Expected function, string, or object as test")};function i(e){return function(t,n,r){return!!(function(e){return null!==e&&"object"==typeof e&&"type"in e}(t)&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function o(){return!0}},18995:(e,t,n)=>{"use strict";n.d(t,{A:()=>i});let r={}.hasOwnProperty;function i(e,t){let n=t||{};function i(t,...n){let o=i.invalid,a=i.handlers;if(t&&r.call(t,e)){let n=String(t[e]);o=r.call(a,n)?a[n]:i.unknown}if(o)return o.call(this,t,...n)}return i.handlers=n.handlers||{},i.invalid=n.invalid,i.unknown=n.unknown,i}},23145:(e,t,n)=>{"use strict";n.d(t,{m:()=>i});let r=/[ \t\n\f\r]/g;function i(e){return"object"==typeof e?"text"===e.type&&o(e.value):o(e)}function o(e){return""===e.replace(r,"")}},25437:(e,t,n)=>{"use strict";n.d(t,{E:()=>a});var r=n(68581),i=n(8918);let o=Object.keys(i);class a extends r.R{constructor(e,t,n,r){let a=-1;if(super(e,t),function(e,t,n){n&&(e[t]=n)}(this,"space",r),"number"==typeof n)for(;++a<o.length;){let e=o[a];!function(e,t,n){n&&(e[t]=n)}(this,o[a],(n&i[e])===i[e])}}}a.prototype.defined=!0},25789:(e,t,n)=>{"use strict";n.d(t,{oz:()=>tM});var r={};n.r(r),n.d(r,{attentionMarkers:()=>eW,contentInitial:()=>ej,disable:()=>eV,document:()=>eF,flow:()=>eH,flowInitial:()=>e$,insideSpan:()=>eq,string:()=>eG,text:()=>eU});var i=n(34093),o=n(55548);let a=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,l=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,s={};function c(e,t){return((t||s).jsx?l:a).test(e)}var u=n(23145),h=n(83846),p=n(7887);let d={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};var f=n(14947),m=n(53724),g=n(70832);function b(e){return e&&"object"==typeof e?"position"in e||"type"in e?k(e.position):"start"in e||"end"in e?k(e):"line"in e||"column"in e?y(e):"":""}function y(e){return x(e&&e.line)+":"+x(e&&e.column)}function k(e){return y(e&&e.start)+"-"+y(e&&e.end)}function x(e){return e&&"number"==typeof e?e:1}class v extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let r="",i={},o=!1;if(t&&(i="line"in t&&"column"in t||"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?r=e:!i.cause&&e&&(o=!0,r=e.message,i.cause=e),!i.ruleId&&!i.source&&"string"==typeof n){let e=n.indexOf(":");-1===e?i.ruleId=n:(i.source=n.slice(0,e),i.ruleId=n.slice(e+1))}if(!i.place&&i.ancestors&&i.ancestors){let e=i.ancestors[i.ancestors.length-1];e&&(i.place=e.position)}let a=i.place&&"start"in i.place?i.place.start:i.place;this.ancestors=i.ancestors||void 0,this.cause=i.cause||void 0,this.column=a?a.column:void 0,this.fatal=void 0,this.file,this.message=r,this.line=a?a.line:void 0,this.name=b(i.place)||"1:1",this.place=i.place||void 0,this.reason=this.message,this.ruleId=i.ruleId||void 0,this.source=i.source||void 0,this.stack=o&&i.cause&&"string"==typeof i.cause.stack?i.cause.stack:"",this.actual,this.expected,this.note,this.url}}v.prototype.file="",v.prototype.name="",v.prototype.reason="",v.prototype.message="",v.prototype.stack="",v.prototype.column=void 0,v.prototype.line=void 0,v.prototype.ancestors=void 0,v.prototype.cause=void 0,v.prototype.fatal=void 0,v.prototype.place=void 0,v.prototype.ruleId=void 0,v.prototype.source=void 0;let w={}.hasOwnProperty,S=new Map,_=/[A-Z]/g,C=new Set(["table","tbody","thead","tfoot","tr"]),P=new Set(["td","th"]),A="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function T(e,t,n){var r;return"element"===t.type?function(e,t,n){let r=e.schema,i=r;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(e.schema=h.JW),e.ancestors.push(t);let a=I(e,t.tagName,!1),l=function(e,t){let n,r,i={};for(r in t.properties)if("children"!==r&&w.call(t.properties,r)){let a=function(e,t,n){let r=(0,p.I)(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?(0,o.A)(n):(0,f.A)(n)),"style"===r.property){let t="object"==typeof n?n:function(e,t){try{return m(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};let t=new v("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:n,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw t.file=e.filePath||void 0,t.url=A+"#cannot-parse-style-attribute",t}}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){let t,n={};for(t in e)w.call(e,t)&&(n[function(e){let t=e.replace(_,O);return"ms-"===t.slice(0,3)&&(t="-"+t),t}(t)]=e[t]);return n}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?d[r.property]||r.property:r.attribute,n]}}(e,r,t.properties[r]);if(a){let[r,o]=a;e.tableCellAlignToStyle&&"align"===r&&"string"==typeof o&&P.has(t.tagName)?n=o:i[r]=o}}return n&&((i.style||(i.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=n),i}(e,t),s=L(e,t);return C.has(t.tagName)&&(s=s.filter(function(e){return"string"!=typeof e||!(0,u.m)(e)})),N(e,l,a,t),E(l,s),e.ancestors.pop(),e.schema=r,e.create(t,a,l,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){let n=t.data.estree.body[0];return(0,i.ok)("ExpressionStatement"===n.type),e.evaluater.evaluateExpression(n.expression)}R(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){let r=e.schema,o=r;"svg"===t.name&&"html"===r.space&&(e.schema=h.JW),e.ancestors.push(t);let a=null===t.name?e.Fragment:I(e,t.name,!0),l=function(e,t){let n={};for(let r of t.attributes)if("mdxJsxExpressionAttribute"===r.type)if(r.data&&r.data.estree&&e.evaluater){let t=r.data.estree.body[0];(0,i.ok)("ExpressionStatement"===t.type);let o=t.expression;(0,i.ok)("ObjectExpression"===o.type);let a=o.properties[0];(0,i.ok)("SpreadElement"===a.type),Object.assign(n,e.evaluater.evaluateExpression(a.argument))}else R(e,t.position);else{let o,a=r.name;if(r.value&&"object"==typeof r.value)if(r.value.data&&r.value.data.estree&&e.evaluater){let t=r.value.data.estree.body[0];(0,i.ok)("ExpressionStatement"===t.type),o=e.evaluater.evaluateExpression(t.expression)}else R(e,t.position);else o=null===r.value||r.value;n[a]=o}return n}(e,t),s=L(e,t);return N(e,l,a,t),E(l,s),e.ancestors.pop(),e.schema=r,e.create(t,a,l,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);R(e,t.position)}(e,t):"root"===t.type?function(e,t,n){let r={};return E(r,L(e,t)),e.create(t,e.Fragment,r,n)}(e,t,n):"text"===t.type?(r=0,t.value):void 0}function N(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function E(e,t){if(t.length>0){let n=t.length>1?t:t[0];n&&(e.children=n)}}function L(e,t){let n=[],r=-1,i=e.passKeys?new Map:S;for(;++r<t.children.length;){let o,a=t.children[r];if(e.passKeys){let e="element"===a.type?a.tagName:"mdxJsxFlowElement"===a.type||"mdxJsxTextElement"===a.type?a.name:void 0;if(e){let t=i.get(e)||0;o=e+"-"+t,i.set(e,t+1)}}let l=T(e,a,o);void 0!==l&&n.push(l)}return n}function I(e,t,n){let r;if(n)if(t.includes(".")){let e,n=t.split("."),o=-1;for(;++o<n.length;){let t=c(n[o])?{type:"Identifier",name:n[o]}:{type:"Literal",value:n[o]};e=e?{type:"MemberExpression",object:e,property:t,computed:!!(o&&"Literal"===t.type),optional:!1}:t}(0,i.ok)(e,"always a result"),r=e}else r=c(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t};else r={type:"Literal",value:t};if("Literal"===r.type){let t=r.value;return w.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(r);R(e)}function R(e,t){let n=new v("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=A+"#cannot-handle-mdx-estrees-without-createevaluater",n}function O(e){return"-"+e.toLowerCase()}let D={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]};var M=n(95155);n(12115);var z=n(4392),B=n(11603);class F{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){let n=null==t?Number.POSITIVE_INFINITY:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){this.setCursor(Math.trunc(e));let r=this.right.splice(this.right.length-(t||0),Number.POSITIVE_INFINITY);return n&&j(this.left,n),r.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),j(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),j(this.right,e.reverse())}setCursor(e){if(e!==this.left.length&&(!(e>this.left.length)||0!==this.right.length)&&(!(e<0)||0!==this.left.length))if(e<this.left.length){let t=this.left.splice(e,Number.POSITIVE_INFINITY);j(this.right,t.reverse())}else{let t=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);j(this.left,t.reverse())}}}function j(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function $(e){let t,n,r,i,o,a,l,s={},c=-1,u=new F(e);for(;++c<u.length;){for(;c in s;)c=s[c];if(t=u.get(c),c&&"chunkFlow"===t[1].type&&"listItemPrefix"===u.get(c-1)[1].type&&((r=0)<(a=t[1]._tokenizer.events).length&&"lineEndingBlank"===a[r][1].type&&(r+=2),r<a.length&&"content"===a[r][1].type))for(;++r<a.length&&"content"!==a[r][1].type;)"chunkText"===a[r][1].type&&(a[r][1]._isInFirstContentOfListItem=!0,r++);if("enter"===t[0])t[1].contentType&&(Object.assign(s,function(e,t){let n,r,i=e.get(t)[1],o=e.get(t)[2],a=t-1,l=[],s=i._tokenizer;!s&&(s=o.parser[i.contentType](i.start),i._contentTypeTextTrailing&&(s._contentTypeTextTrailing=!0));let c=s.events,u=[],h={},p=-1,d=i,f=0,m=0,g=[0];for(;d;){for(;e.get(++a)[1]!==d;);l.push(a),!d._tokenizer&&(n=o.sliceStream(d),d.next||n.push(null),r&&s.defineSkip(d.start),d._isInFirstContentOfListItem&&(s._gfmTasklistFirstContentOfListItem=!0),s.write(n),d._isInFirstContentOfListItem&&(s._gfmTasklistFirstContentOfListItem=void 0)),r=d,d=d.next}for(d=i;++p<c.length;)"exit"===c[p][0]&&"enter"===c[p-1][0]&&c[p][1].type===c[p-1][1].type&&c[p][1].start.line!==c[p][1].end.line&&(m=p+1,g.push(m),d._tokenizer=void 0,d.previous=void 0,d=d.next);for(s.events=[],d?(d._tokenizer=void 0,d.previous=void 0):g.pop(),p=g.length;p--;){let t=c.slice(g[p],g[p+1]),n=l.pop();u.push([n,n+t.length-1]),e.splice(n,2,t)}for(u.reverse(),p=-1;++p<u.length;)h[f+u[p][0]]=f+u[p][1],f+=u[p][1]-u[p][0]-1;return h}(u,c)),c=s[c],l=!0);else if(t[1]._container){for(r=c,n=void 0;r--;)if("lineEnding"===(i=u.get(r))[1].type||"lineEndingBlank"===i[1].type)"enter"===i[0]&&(n&&(u.get(n)[1].type="lineEndingBlank"),i[1].type="lineEnding",n=r);else if("linePrefix"===i[1].type||"listItemIndent"===i[1].type);else break;n&&(t[1].end={...u.get(n)[1].start},(o=u.slice(n,c)).unshift(t),u.splice(n,c-n+1,o))}}return(0,B.m)(e,0,Number.POSITIVE_INFINITY,u.slice(0)),!l}var H=n(69381),G=n(94581),U=n(12556);let q={tokenize:function(e){let t,n=e.attempt(this.parser.constructs.contentInitial,function(t){return null===t?void e.consume(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,G.N)(e,n,"linePrefix"))},function(n){return e.enter("paragraph"),function n(r){let i=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=i),t=i,function t(r){if(null===r){e.exit("chunkText"),e.exit("paragraph"),e.consume(r);return}return(0,U.HP)(r)?(e.consume(r),e.exit("chunkText"),n):(e.consume(r),t)}(r)}(n)});return n}},W={tokenize:function(e){let t,n,r,i=this,o=[],a=0;return l;function l(t){if(a<o.length){let n=o[a];return i.containerState=n[1],e.attempt(n[0].continuation,s,c)(t)}return c(t)}function s(e){if(a++,i.containerState._closeFlow){let n;i.containerState._closeFlow=void 0,t&&b();let r=i.events.length,o=r;for(;o--;)if("exit"===i.events[o][0]&&"chunkFlow"===i.events[o][1].type){n=i.events[o][1].end;break}g(a);let l=r;for(;l<i.events.length;)i.events[l][1].end={...n},l++;return(0,B.m)(i.events,o+1,0,i.events.slice(r)),i.events.length=l,c(e)}return l(e)}function c(n){if(a===o.length){if(!t)return p(n);if(t.currentConstruct&&t.currentConstruct.concrete)return f(n);i.interrupt=!!(t.currentConstruct&&!t._gfmTableDynamicInterruptHack)}return i.containerState={},e.check(V,u,h)(n)}function u(e){return t&&b(),g(a),p(e)}function h(e){return i.parser.lazy[i.now().line]=a!==o.length,r=i.now().offset,f(e)}function p(t){return i.containerState={},e.attempt(V,d,f)(t)}function d(e){return a++,o.push([i.currentConstruct,i.containerState]),p(e)}function f(r){if(null===r){t&&b(),g(0),e.consume(r);return}return t=t||i.parser.flow(i.now()),e.enter("chunkFlow",{_tokenizer:t,contentType:"flow",previous:n}),function t(n){if(null===n){m(e.exit("chunkFlow"),!0),g(0),e.consume(n);return}return(0,U.HP)(n)?(e.consume(n),m(e.exit("chunkFlow")),a=0,i.interrupt=void 0,l):(e.consume(n),t)}(r)}function m(e,o){let l=i.sliceStream(e);if(o&&l.push(null),e.previous=n,n&&(n.next=e),n=e,t.defineSkip(e.start),t.write(l),i.parser.lazy[e.start.line]){let e,n,o=t.events.length;for(;o--;)if(t.events[o][1].start.offset<r&&(!t.events[o][1].end||t.events[o][1].end.offset>r))return;let l=i.events.length,s=l;for(;s--;)if("exit"===i.events[s][0]&&"chunkFlow"===i.events[s][1].type){if(e){n=i.events[s][1].end;break}e=!0}for(g(a),o=l;o<i.events.length;)i.events[o][1].end={...n},o++;(0,B.m)(i.events,s+1,0,i.events.slice(l)),i.events.length=o}}function g(t){let n=o.length;for(;n-- >t;){let t=o[n];i.containerState=t[1],t[0].exit.call(i,e)}o.length=t}function b(){t.write([null]),n=void 0,t=void 0,i.containerState._closeFlow=void 0}}},V={tokenize:function(e,t,n){return(0,G.N)(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}};var Q=n(95333);let J={resolve:function(e){return $(e),e},tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),r(t)};function r(t){return null===t?i(t):(0,U.HP)(t)?e.check(Y,o,i)(t):(e.consume(t),r)}function i(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function o(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,r}}},Y={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,G.N)(e,i,"linePrefix")};function i(i){if(null===i||(0,U.HP)(i))return n(i);let o=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&o&&"linePrefix"===o[1].type&&o[2].sliceSerialize(o[1],!0).length>=4?t(i):e.interrupt(r.parser.constructs.flow,n,t)(i)}}},Z={tokenize:function(e){let t=this,n=e.attempt(Q.B,function(r){return null===r?void e.consume(r):(e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n)},e.attempt(this.parser.constructs.flowInitial,r,(0,G.N)(e,e.attempt(this.parser.constructs.flow,r,e.attempt(J,r)),"linePrefix")));return n;function r(r){return null===r?void e.consume(r):(e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n)}}},K={resolveAll:en()},X=et("string"),ee=et("text");function et(e){return{resolveAll:en("text"===e?er:void 0),tokenize:function(t){let n=this,r=this.parser.constructs[e],i=t.attempt(r,o,a);return o;function o(e){return s(e)?i(e):a(e)}function a(e){return null===e?void t.consume(e):(t.enter("data"),t.consume(e),l)}function l(e){return s(e)?(t.exit("data"),i(e)):(t.consume(e),l)}function s(e){if(null===e)return!0;let t=r[e],i=-1;if(t)for(;++i<t.length;){let e=t[i];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}}function en(e){return function(t,n){let r,i=-1;for(;++i<=t.length;)void 0===r?t[i]&&"data"===t[i][1].type&&(r=i,i++):t[i]&&"data"===t[i][1].type||(i!==r+2&&(t[r][1].end=t[i-1][1].end,t.splice(r+2,i-r-2),i=r+2),r=void 0);return e?e(t,n):t}}function er(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){let r,i=e[n-1][1],o=t.sliceStream(i),a=o.length,l=-1,s=0;for(;a--;){let e=o[a];if("string"==typeof e){for(l=e.length;32===e.charCodeAt(l-1);)s++,l--;if(l)break;l=-1}else if(-2===e)r=!0,s++;else if(-1===e);else{a++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(s=0),s){let o={type:n===e.length||r||s<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:a?l:i.start._bufferIndex+l,_index:i.start._index+a,line:i.end.line,column:i.end.column-s,offset:i.end.offset-s},end:{...i.end}};i.end={...o.start},i.start.offset===i.end.offset?Object.assign(i,o):(e.splice(n,0,["enter",o,t],["exit",o,t]),n+=2)}n++}return e}let ei={name:"thematicBreak",tokenize:function(e,t,n){let r,i=0;return function(o){var a;return e.enter("thematicBreak"),r=a=o,function o(a){return a===r?(e.enter("thematicBreakSequence"),function t(n){return n===r?(e.consume(n),i++,t):(e.exit("thematicBreakSequence"),(0,U.On)(n)?(0,G.N)(e,o,"whitespace")(n):o(n))}(a)):i>=3&&(null===a||(0,U.HP)(a))?(e.exit("thematicBreak"),t(a)):n(a)}(a)}}},eo={continuation:{tokenize:function(e,t,n){let r=this;return r.containerState._closeFlow=void 0,e.check(Q.B,function(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,(0,G.N)(e,t,"listItemIndent",r.containerState.size+1)(n)},function(n){return r.containerState.furtherBlankLines||!(0,U.On)(n)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,i(n)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(el,t,i)(n))});function i(i){return r.containerState._closeFlow=!0,r.interrupt=void 0,(0,G.N)(e,e.attempt(eo,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(i)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,t,n){let r=this,i=r.events[r.events.length-1],o=i&&"linePrefix"===i[1].type?i[2].sliceSerialize(i[1],!0).length:0,a=0;return function(t){let i=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===i?!r.containerState.marker||t===r.containerState.marker:(0,U.BM)(t)){if(r.containerState.type||(r.containerState.type=i,e.enter(i,{_container:!0})),"listUnordered"===i)return e.enter("listItemPrefix"),42===t||45===t?e.check(ei,n,l)(t):l(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),function t(i){return(0,U.BM)(i)&&++a<10?(e.consume(i),t):(!r.interrupt||a<2)&&(r.containerState.marker?i===r.containerState.marker:41===i||46===i)?(e.exit("listItemValue"),l(i)):n(i)}(t)}return n(t)};function l(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(Q.B,r.interrupt?n:s,e.attempt(ea,u,c))}function s(e){return r.containerState.initialBlankLine=!0,o++,u(e)}function c(t){return(0,U.On)(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),u):n(t)}function u(n){return r.containerState.size=o+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}},ea={partial:!0,tokenize:function(e,t,n){let r=this;return(0,G.N)(e,function(e){let i=r.events[r.events.length-1];return!(0,U.On)(e)&&i&&"listItemPrefixWhitespace"===i[1].type?t(e):n(e)},"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},el={partial:!0,tokenize:function(e,t,n){let r=this;return(0,G.N)(e,function(e){let i=r.events[r.events.length-1];return i&&"listItemIndent"===i[1].type&&i[2].sliceSerialize(i[1],!0).length===r.containerState.size?t(e):n(e)},"listItemIndent",r.containerState.size+1)}},es={continuation:{tokenize:function(e,t,n){let r=this;return function(t){return(0,U.On)(t)?(0,G.N)(e,i,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):i(t)};function i(r){return e.attempt(es,t,n)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,t,n){let r=this;return function(t){if(62===t){let n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),i}return n(t)};function i(n){return(0,U.On)(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}};function ec(e,t,n,r,i,o,a,l,s){let c=s||Number.POSITIVE_INFINITY,u=0;return function(t){return 60===t?(e.enter(r),e.enter(i),e.enter(o),e.consume(t),e.exit(o),h):null===t||32===t||41===t||(0,U.JQ)(t)?n(t):(e.enter(r),e.enter(a),e.enter(l),e.enter("chunkString",{contentType:"string"}),f(t))};function h(n){return 62===n?(e.enter(o),e.consume(n),e.exit(o),e.exit(i),e.exit(r),t):(e.enter(l),e.enter("chunkString",{contentType:"string"}),p(n))}function p(t){return 62===t?(e.exit("chunkString"),e.exit(l),h(t)):null===t||60===t||(0,U.HP)(t)?n(t):(e.consume(t),92===t?d:p)}function d(t){return 60===t||62===t||92===t?(e.consume(t),p):p(t)}function f(i){return!u&&(null===i||41===i||(0,U.Ee)(i))?(e.exit("chunkString"),e.exit(l),e.exit(a),e.exit(r),t(i)):u<c&&40===i?(e.consume(i),u++,f):41===i?(e.consume(i),u--,f):null===i||32===i||40===i||(0,U.JQ)(i)?n(i):(e.consume(i),92===i?m:f)}function m(t){return 40===t||41===t||92===t?(e.consume(t),f):f(t)}}function eu(e,t,n,r,i,o){let a,l=this,s=0;return function(t){return e.enter(r),e.enter(i),e.consume(t),e.exit(i),e.enter(o),c};function c(h){return s>999||null===h||91===h||93===h&&!a||94===h&&!s&&"_hiddenFootnoteSupport"in l.parser.constructs?n(h):93===h?(e.exit(o),e.enter(i),e.consume(h),e.exit(i),e.exit(r),t):(0,U.HP)(h)?(e.enter("lineEnding"),e.consume(h),e.exit("lineEnding"),c):(e.enter("chunkString",{contentType:"string"}),u(h))}function u(t){return null===t||91===t||93===t||(0,U.HP)(t)||s++>999?(e.exit("chunkString"),c(t)):(e.consume(t),a||(a=!(0,U.On)(t)),92===t?h:u)}function h(t){return 91===t||92===t||93===t?(e.consume(t),s++,u):u(t)}}function eh(e,t,n,r,i,o){let a;return function(t){return 34===t||39===t||40===t?(e.enter(r),e.enter(i),e.consume(t),e.exit(i),a=40===t?41:t,l):n(t)};function l(n){return n===a?(e.enter(i),e.consume(n),e.exit(i),e.exit(r),t):(e.enter(o),s(n))}function s(t){return t===a?(e.exit(o),l(a)):null===t?n(t):(0,U.HP)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),(0,G.N)(e,s,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),c(t))}function c(t){return t===a||null===t||(0,U.HP)(t)?(e.exit("chunkString"),s(t)):(e.consume(t),92===t?u:c)}function u(t){return t===a||92===t?(e.consume(t),c):c(t)}}function ep(e,t){let n;return function r(i){return(0,U.HP)(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),n=!0,r):(0,U.On)(i)?(0,G.N)(e,r,n?"linePrefix":"lineSuffix")(i):t(i)}}var ed=n(33386);let ef={partial:!0,tokenize:function(e,t,n){return function(t){return(0,U.Ee)(t)?ep(e,r)(t):n(t)};function r(t){return eh(e,i,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function i(t){return(0,U.On)(t)?(0,G.N)(e,o,"whitespace")(t):o(t)}function o(e){return null===e||(0,U.HP)(e)?t(e):n(e)}}},em={name:"codeIndented",tokenize:function(e,t,n){let r=this;return function(t){return e.enter("codeIndented"),(0,G.N)(e,i,"linePrefix",5)(t)};function i(t){let i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?function t(n){return null===n?o(n):(0,U.HP)(n)?e.attempt(eg,t,o)(n):(e.enter("codeFlowValue"),function n(r){return null===r||(0,U.HP)(r)?(e.exit("codeFlowValue"),t(r)):(e.consume(r),n)}(n))}(t):n(t)}function o(n){return e.exit("codeIndented"),t(n)}}},eg={partial:!0,tokenize:function(e,t,n){let r=this;return i;function i(t){return r.parser.lazy[r.now().line]?n(t):(0,U.HP)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):(0,G.N)(e,o,"linePrefix",5)(t)}function o(e){let o=r.events[r.events.length-1];return o&&"linePrefix"===o[1].type&&o[2].sliceSerialize(o[1],!0).length>=4?t(e):(0,U.HP)(e)?i(e):n(e)}}},eb={name:"setextUnderline",resolveTo:function(e,t){let n,r,i,o=e.length;for(;o--;)if("enter"===e[o][0]){if("content"===e[o][1].type){n=o;break}"paragraph"===e[o][1].type&&(r=o)}else"content"===e[o][1].type&&e.splice(o,1),i||"definition"!==e[o][1].type||(i=o);let a={type:"setextHeading",start:{...e[n][1].start},end:{...e[e.length-1][1].end}};return e[r][1].type="setextHeadingText",i?(e.splice(r,0,["enter",a,t]),e.splice(i+1,0,["exit",e[n][1],t]),e[n][1].end={...e[i][1].end}):e[n][1]=a,e.push(["exit",a,t]),e},tokenize:function(e,t,n){let r,i=this;return function(t){var a;let l,s=i.events.length;for(;s--;)if("lineEnding"!==i.events[s][1].type&&"linePrefix"!==i.events[s][1].type&&"content"!==i.events[s][1].type){l="paragraph"===i.events[s][1].type;break}return!i.parser.lazy[i.now().line]&&(i.interrupt||l)?(e.enter("setextHeadingLine"),r=t,a=t,e.enter("setextHeadingLineSequence"),function t(n){return n===r?(e.consume(n),t):(e.exit("setextHeadingLineSequence"),(0,U.On)(n)?(0,G.N)(e,o,"lineSuffix")(n):o(n))}(a)):n(t)};function o(r){return null===r||(0,U.HP)(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}}},ey=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],ek=["pre","script","style","textarea"],ex={partial:!0,tokenize:function(e,t,n){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(Q.B,t,n)}}},ev={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return(0,U.HP)(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i):n(t)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},ew={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return null===t?n(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),i)};function i(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},eS={concrete:!0,name:"codeFenced",tokenize:function(e,t,n){let r,i=this,o={partial:!0,tokenize:function(e,t,n){let o=0;return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),a};function a(t){return e.enter("codeFencedFence"),(0,U.On)(t)?(0,G.N)(e,s,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):s(t)}function s(t){return t===r?(e.enter("codeFencedFenceSequence"),function t(i){return i===r?(o++,e.consume(i),t):o>=l?(e.exit("codeFencedFenceSequence"),(0,U.On)(i)?(0,G.N)(e,c,"whitespace")(i):c(i)):n(i)}(t)):n(t)}function c(r){return null===r||(0,U.HP)(r)?(e.exit("codeFencedFence"),t(r)):n(r)}}},a=0,l=0;return function(t){var o=t;let c=i.events[i.events.length-1];return a=c&&"linePrefix"===c[1].type?c[2].sliceSerialize(c[1],!0).length:0,r=o,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),function t(i){return i===r?(l++,e.consume(i),t):l<3?n(i):(e.exit("codeFencedFenceSequence"),(0,U.On)(i)?(0,G.N)(e,s,"whitespace")(i):s(i))}(o)};function s(o){return null===o||(0,U.HP)(o)?(e.exit("codeFencedFence"),i.interrupt?t(o):e.check(ew,u,f)(o)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||(0,U.HP)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),s(i)):(0,U.On)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),(0,G.N)(e,c,"whitespace")(i)):96===i&&i===r?n(i):(e.consume(i),t)}(o))}function c(t){return null===t||(0,U.HP)(t)?s(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),function t(i){return null===i||(0,U.HP)(i)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),s(i)):96===i&&i===r?n(i):(e.consume(i),t)}(t))}function u(t){return e.attempt(o,f,h)(t)}function h(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),p}function p(t){return a>0&&(0,U.On)(t)?(0,G.N)(e,d,"linePrefix",a+1)(t):d(t)}function d(t){return null===t||(0,U.HP)(t)?e.check(ew,u,f)(t):(e.enter("codeFlowValue"),function t(n){return null===n||(0,U.HP)(n)?(e.exit("codeFlowValue"),d(n)):(e.consume(n),t)}(t))}function f(n){return e.exit("codeFenced"),t(n)}}},e_=document.createElement("i");function eC(e){let t="&"+e+";";e_.innerHTML=t;let n=e_.textContent;return(59!==n.charCodeAt(n.length-1)||"semi"===e)&&n!==t&&n}let eP={name:"characterReference",tokenize:function(e,t,n){let r,i,o=this,a=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),l};function l(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),s):(e.enter("characterReferenceValue"),r=31,i=U.lV,c(t))}function s(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),r=6,i=U.ok,c):(e.enter("characterReferenceValue"),r=7,i=U.BM,c(t))}function c(l){if(59===l&&a){let r=e.exit("characterReferenceValue");return i!==U.lV||eC(o.sliceSerialize(r))?(e.enter("characterReferenceMarker"),e.consume(l),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(l)}return i(l)&&a++<r?(e.consume(l),c):n(l)}}},eA={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),r};function r(r){return(0,U.ol)(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}},eT={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),(0,G.N)(e,t,"linePrefix")}}};var eN=n(91877);let eE={name:"labelEnd",resolveAll:function(e){let t=-1,n=[];for(;++t<e.length;){let r=e[t][1];if(n.push(e[t]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){let e="labelImage"===r.type?4:2;r.type="data",t+=e}}return e.length!==n.length&&(0,B.m)(e,0,e.length,n),e},resolveTo:function(e,t){let n,r,i,o,a=e.length,l=0;for(;a--;)if(n=e[a][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[a][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(i){if("enter"===e[a][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=a,"labelLink"!==n.type)){l=2;break}}else"labelEnd"===n.type&&(i=a);let s={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},c={type:"label",start:{...e[r][1].start},end:{...e[i][1].end}},u={type:"labelText",start:{...e[r+l+2][1].end},end:{...e[i-2][1].start}};return o=[["enter",s,t],["enter",c,t]],o=(0,B.V)(o,e.slice(r+1,r+l+3)),o=(0,B.V)(o,[["enter",u,t]]),o=(0,B.V)(o,(0,eN.W)(t.parser.constructs.insideSpan.null,e.slice(r+l+4,i-3),t)),o=(0,B.V)(o,[["exit",u,t],e[i-2],e[i-1],["exit",c,t]]),o=(0,B.V)(o,e.slice(i+1)),o=(0,B.V)(o,[["exit",s,t]]),(0,B.m)(e,r,e.length,o),e},tokenize:function(e,t,n){let r,i,o=this,a=o.events.length;for(;a--;)if(("labelImage"===o.events[a][1].type||"labelLink"===o.events[a][1].type)&&!o.events[a][1]._balanced){r=o.events[a][1];break}return function(t){return r?r._inactive?u(t):(i=o.parser.defined.includes((0,ed.B)(o.sliceSerialize({start:r.end,end:o.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),l):n(t)};function l(t){return 40===t?e.attempt(eL,c,i?c:u)(t):91===t?e.attempt(eI,c,i?s:u)(t):i?c(t):u(t)}function s(t){return e.attempt(eR,c,u)(t)}function c(e){return t(e)}function u(e){return r._balanced=!0,n(e)}}},eL={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),r};function r(t){return(0,U.Ee)(t)?ep(e,i)(t):i(t)}function i(t){return 41===t?c(t):ec(e,o,a,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function o(t){return(0,U.Ee)(t)?ep(e,l)(t):c(t)}function a(e){return n(e)}function l(t){return 34===t||39===t||40===t?eh(e,s,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):c(t)}function s(t){return(0,U.Ee)(t)?ep(e,c)(t):c(t)}function c(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}},eI={tokenize:function(e,t,n){let r=this;return function(t){return eu.call(r,e,i,o,"reference","referenceMarker","referenceString")(t)};function i(e){return r.parser.defined.includes((0,ed.B)(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function o(e){return n(e)}}},eR={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}},eO={name:"labelStartImage",resolveAll:eE.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),i};function i(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),o):n(t)}function o(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};var eD=n(49535);let eM={name:"attention",resolveAll:function(e,t){let n,r,i,o,a,l,s,c,u=-1;for(;++u<e.length;)if("enter"===e[u][0]&&"attentionSequence"===e[u][1].type&&e[u][1]._close){for(n=u;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[u][1]).charCodeAt(0)){if((e[n][1]._close||e[u][1]._open)&&(e[u][1].end.offset-e[u][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[u][1].end.offset-e[u][1].start.offset)%3))continue;l=e[n][1].end.offset-e[n][1].start.offset>1&&e[u][1].end.offset-e[u][1].start.offset>1?2:1;let h={...e[n][1].end},p={...e[u][1].start};ez(h,-l),ez(p,l),o={type:l>1?"strongSequence":"emphasisSequence",start:h,end:{...e[n][1].end}},a={type:l>1?"strongSequence":"emphasisSequence",start:{...e[u][1].start},end:p},i={type:l>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[u][1].start}},r={type:l>1?"strong":"emphasis",start:{...o.start},end:{...a.end}},e[n][1].end={...o.start},e[u][1].start={...a.end},s=[],e[n][1].end.offset-e[n][1].start.offset&&(s=(0,B.V)(s,[["enter",e[n][1],t],["exit",e[n][1],t]])),s=(0,B.V)(s,[["enter",r,t],["enter",o,t],["exit",o,t],["enter",i,t]]),s=(0,B.V)(s,(0,eN.W)(t.parser.constructs.insideSpan.null,e.slice(n+1,u),t)),s=(0,B.V)(s,[["exit",i,t],["enter",a,t],["exit",a,t],["exit",r,t]]),e[u][1].end.offset-e[u][1].start.offset?(c=2,s=(0,B.V)(s,[["enter",e[u][1],t],["exit",e[u][1],t]])):c=0,(0,B.m)(e,n-1,u-n+3,s),u=n+s.length-c-2;break}}for(u=-1;++u<e.length;)"attentionSequence"===e[u][1].type&&(e[u][1].type="data");return e},tokenize:function(e,t){let n,r=this.parser.constructs.attentionMarkers.null,i=this.previous,o=(0,eD.S)(i);return function(a){return n=a,e.enter("attentionSequence"),function a(l){if(l===n)return e.consume(l),a;let s=e.exit("attentionSequence"),c=(0,eD.S)(l),u=!c||2===c&&o||r.includes(l),h=!o||2===o&&c||r.includes(i);return s._open=!!(42===n?u:u&&(o||!h)),s._close=!!(42===n?h:h&&(c||!u)),t(l)}(a)}}};function ez(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}let eB={name:"labelStartLink",resolveAll:eE.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),i};function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}},eF={42:eo,43:eo,45:eo,48:eo,49:eo,50:eo,51:eo,52:eo,53:eo,54:eo,55:eo,56:eo,57:eo,62:es},ej={91:{name:"definition",tokenize:function(e,t,n){let r,i=this;return function(t){var r;return e.enter("definition"),r=t,eu.call(i,e,o,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(r)};function o(t){return(r=(0,ed.B)(i.sliceSerialize(i.events[i.events.length-1][1]).slice(1,-1)),58===t)?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),a):n(t)}function a(t){return(0,U.Ee)(t)?ep(e,l)(t):l(t)}function l(t){return ec(e,s,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function s(t){return e.attempt(ef,c,c)(t)}function c(t){return(0,U.On)(t)?(0,G.N)(e,u,"whitespace")(t):u(t)}function u(o){return null===o||(0,U.HP)(o)?(e.exit("definition"),i.parser.defined.push(r),t(o)):n(o)}}}},e$={[-2]:em,[-1]:em,32:em},eH={35:{name:"headingAtx",resolve:function(e,t){let n,r,i=e.length-2,o=3;return"whitespace"===e[3][1].type&&(o+=2),i-2>o&&"whitespace"===e[i][1].type&&(i-=2),"atxHeadingSequence"===e[i][1].type&&(o===i-1||i-4>o&&"whitespace"===e[i-2][1].type)&&(i-=o+1===i?2:4),i>o&&(n={type:"atxHeadingText",start:e[o][1].start,end:e[i][1].end},r={type:"chunkText",start:e[o][1].start,end:e[i][1].end,contentType:"text"},(0,B.m)(e,o,i-o+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]])),e},tokenize:function(e,t,n){let r=0;return function(i){var o;return e.enter("atxHeading"),o=i,e.enter("atxHeadingSequence"),function i(o){return 35===o&&r++<6?(e.consume(o),i):null===o||(0,U.Ee)(o)?(e.exit("atxHeadingSequence"),function n(r){return 35===r?(e.enter("atxHeadingSequence"),function t(r){return 35===r?(e.consume(r),t):(e.exit("atxHeadingSequence"),n(r))}(r)):null===r||(0,U.HP)(r)?(e.exit("atxHeading"),t(r)):(0,U.On)(r)?(0,G.N)(e,n,"whitespace")(r):(e.enter("atxHeadingText"),function t(r){return null===r||35===r||(0,U.Ee)(r)?(e.exit("atxHeadingText"),n(r)):(e.consume(r),t)}(r))}(o)):n(o)}(o)}}},42:ei,45:[eb,ei],60:{concrete:!0,name:"htmlFlow",resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););return t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e},tokenize:function(e,t,n){let r,i,o,a,l,s=this;return function(t){var n;return n=t,e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(n),c};function c(a){return 33===a?(e.consume(a),u):47===a?(e.consume(a),i=!0,d):63===a?(e.consume(a),r=3,s.interrupt?t:I):(0,U.CW)(a)?(e.consume(a),o=String.fromCharCode(a),f):n(a)}function u(i){return 45===i?(e.consume(i),r=2,h):91===i?(e.consume(i),r=5,a=0,p):(0,U.CW)(i)?(e.consume(i),r=4,s.interrupt?t:I):n(i)}function h(r){return 45===r?(e.consume(r),s.interrupt?t:I):n(r)}function p(r){let i="CDATA[";return r===i.charCodeAt(a++)?(e.consume(r),a===i.length)?s.interrupt?t:_:p:n(r)}function d(t){return(0,U.CW)(t)?(e.consume(t),o=String.fromCharCode(t),f):n(t)}function f(a){if(null===a||47===a||62===a||(0,U.Ee)(a)){let l=47===a,c=o.toLowerCase();return!l&&!i&&ek.includes(c)?(r=1,s.interrupt?t(a):_(a)):ey.includes(o.toLowerCase())?(r=6,l)?(e.consume(a),m):s.interrupt?t(a):_(a):(r=7,s.interrupt&&!s.parser.lazy[s.now().line]?n(a):i?function t(n){return(0,U.On)(n)?(e.consume(n),t):w(n)}(a):g(a))}return 45===a||(0,U.lV)(a)?(e.consume(a),o+=String.fromCharCode(a),f):n(a)}function m(r){return 62===r?(e.consume(r),s.interrupt?t:_):n(r)}function g(t){return 47===t?(e.consume(t),w):58===t||95===t||(0,U.CW)(t)?(e.consume(t),b):(0,U.On)(t)?(e.consume(t),g):w(t)}function b(t){return 45===t||46===t||58===t||95===t||(0,U.lV)(t)?(e.consume(t),b):y(t)}function y(t){return 61===t?(e.consume(t),k):(0,U.On)(t)?(e.consume(t),y):g(t)}function k(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),l=t,x):(0,U.On)(t)?(e.consume(t),k):function t(n){return null===n||34===n||39===n||47===n||60===n||61===n||62===n||96===n||(0,U.Ee)(n)?y(n):(e.consume(n),t)}(t)}function x(t){return t===l?(e.consume(t),l=null,v):null===t||(0,U.HP)(t)?n(t):(e.consume(t),x)}function v(e){return 47===e||62===e||(0,U.On)(e)?g(e):n(e)}function w(t){return 62===t?(e.consume(t),S):n(t)}function S(t){return null===t||(0,U.HP)(t)?_(t):(0,U.On)(t)?(e.consume(t),S):n(t)}function _(t){return 45===t&&2===r?(e.consume(t),T):60===t&&1===r?(e.consume(t),N):62===t&&4===r?(e.consume(t),R):63===t&&3===r?(e.consume(t),I):93===t&&5===r?(e.consume(t),L):(0,U.HP)(t)&&(6===r||7===r)?(e.exit("htmlFlowData"),e.check(ex,O,C)(t)):null===t||(0,U.HP)(t)?(e.exit("htmlFlowData"),C(t)):(e.consume(t),_)}function C(t){return e.check(ev,P,O)(t)}function P(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),A}function A(t){return null===t||(0,U.HP)(t)?C(t):(e.enter("htmlFlowData"),_(t))}function T(t){return 45===t?(e.consume(t),I):_(t)}function N(t){return 47===t?(e.consume(t),o="",E):_(t)}function E(t){if(62===t){let n=o.toLowerCase();return ek.includes(n)?(e.consume(t),R):_(t)}return(0,U.CW)(t)&&o.length<8?(e.consume(t),o+=String.fromCharCode(t),E):_(t)}function L(t){return 93===t?(e.consume(t),I):_(t)}function I(t){return 62===t?(e.consume(t),R):45===t&&2===r?(e.consume(t),I):_(t)}function R(t){return null===t||(0,U.HP)(t)?(e.exit("htmlFlowData"),O(t)):(e.consume(t),R)}function O(n){return e.exit("htmlFlow"),t(n)}}},61:eb,95:ei,96:eS,126:eS},eG={38:eP,92:eA},eU={[-5]:eT,[-4]:eT,[-3]:eT,33:eO,38:eP,42:eM,60:[{name:"autolink",tokenize:function(e,t,n){let r=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),i};function i(t){return(0,U.CW)(t)?(e.consume(t),o):64===t?n(t):l(t)}function o(t){return 43===t||45===t||46===t||(0,U.lV)(t)?(r=1,function t(n){return 58===n?(e.consume(n),r=0,a):(43===n||45===n||46===n||(0,U.lV)(n))&&r++<32?(e.consume(n),t):(r=0,l(n))}(t)):l(t)}function a(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||(0,U.JQ)(r)?n(r):(e.consume(r),a)}function l(t){return 64===t?(e.consume(t),s):(0,U.cx)(t)?(e.consume(t),l):n(t)}function s(i){return(0,U.lV)(i)?function i(o){return 46===o?(e.consume(o),r=0,s):62===o?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(o),e.exit("autolinkMarker"),e.exit("autolink"),t):function t(o){if((45===o||(0,U.lV)(o))&&r++<63){let n=45===o?t:i;return e.consume(o),n}return n(o)}(o)}(i):n(i)}}},{name:"htmlText",tokenize:function(e,t,n){let r,i,o,a=this;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),l};function l(t){return 33===t?(e.consume(t),s):47===t?(e.consume(t),x):63===t?(e.consume(t),y):(0,U.CW)(t)?(e.consume(t),w):n(t)}function s(t){return 45===t?(e.consume(t),c):91===t?(e.consume(t),i=0,d):(0,U.CW)(t)?(e.consume(t),b):n(t)}function c(t){return 45===t?(e.consume(t),p):n(t)}function u(t){return null===t?n(t):45===t?(e.consume(t),h):(0,U.HP)(t)?(o=u,E(t)):(e.consume(t),u)}function h(t){return 45===t?(e.consume(t),p):u(t)}function p(e){return 62===e?N(e):45===e?h(e):u(e)}function d(t){let r="CDATA[";return t===r.charCodeAt(i++)?(e.consume(t),i===r.length?f:d):n(t)}function f(t){return null===t?n(t):93===t?(e.consume(t),m):(0,U.HP)(t)?(o=f,E(t)):(e.consume(t),f)}function m(t){return 93===t?(e.consume(t),g):f(t)}function g(t){return 62===t?N(t):93===t?(e.consume(t),g):f(t)}function b(t){return null===t||62===t?N(t):(0,U.HP)(t)?(o=b,E(t)):(e.consume(t),b)}function y(t){return null===t?n(t):63===t?(e.consume(t),k):(0,U.HP)(t)?(o=y,E(t)):(e.consume(t),y)}function k(e){return 62===e?N(e):y(e)}function x(t){return(0,U.CW)(t)?(e.consume(t),v):n(t)}function v(t){return 45===t||(0,U.lV)(t)?(e.consume(t),v):function t(n){return(0,U.HP)(n)?(o=t,E(n)):(0,U.On)(n)?(e.consume(n),t):N(n)}(t)}function w(t){return 45===t||(0,U.lV)(t)?(e.consume(t),w):47===t||62===t||(0,U.Ee)(t)?S(t):n(t)}function S(t){return 47===t?(e.consume(t),N):58===t||95===t||(0,U.CW)(t)?(e.consume(t),_):(0,U.HP)(t)?(o=S,E(t)):(0,U.On)(t)?(e.consume(t),S):N(t)}function _(t){return 45===t||46===t||58===t||95===t||(0,U.lV)(t)?(e.consume(t),_):function t(n){return 61===n?(e.consume(n),C):(0,U.HP)(n)?(o=t,E(n)):(0,U.On)(n)?(e.consume(n),t):S(n)}(t)}function C(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),r=t,P):(0,U.HP)(t)?(o=C,E(t)):(0,U.On)(t)?(e.consume(t),C):(e.consume(t),A)}function P(t){return t===r?(e.consume(t),r=void 0,T):null===t?n(t):(0,U.HP)(t)?(o=P,E(t)):(e.consume(t),P)}function A(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||(0,U.Ee)(t)?S(t):(e.consume(t),A)}function T(e){return 47===e||62===e||(0,U.Ee)(e)?S(e):n(e)}function N(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function E(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),L}function L(t){return(0,U.On)(t)?(0,G.N)(e,I,"linePrefix",a.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):I(t)}function I(t){return e.enter("htmlTextData"),o(t)}}}],91:eB,92:[{name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),r};function r(r){return(0,U.HP)(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}},eA],93:eE,95:eM,96:{name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let t,n,r=e.length-4,i=3;if(("lineEnding"===e[3][1].type||"space"===e[i][1].type)&&("lineEnding"===e[r][1].type||"space"===e[r][1].type)){for(t=i;++t<r;)if("codeTextData"===e[t][1].type){e[i][1].type="codeTextPadding",e[r][1].type="codeTextPadding",i+=2,r-=2;break}}for(t=i-1,r++;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):(t===r||"lineEnding"===e[t][1].type)&&(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},tokenize:function(e,t,n){let r,i,o=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),function t(n){return 96===n?(e.consume(n),o++,t):(e.exit("codeTextSequence"),a(n))}(t)};function a(s){return null===s?n(s):32===s?(e.enter("space"),e.consume(s),e.exit("space"),a):96===s?(i=e.enter("codeTextSequence"),r=0,function n(a){return 96===a?(e.consume(a),r++,n):r===o?(e.exit("codeTextSequence"),e.exit("codeText"),t(a)):(i.type="codeTextData",l(a))}(s)):(0,U.HP)(s)?(e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),a):(e.enter("codeTextData"),l(s))}function l(t){return null===t||32===t||96===t||(0,U.HP)(t)?(e.exit("codeTextData"),a(t)):(e.consume(t),l)}}}},eq={null:[eM,K]},eW={null:[42,95]},eV={null:[]},eQ=/[\0\t\n\r]/g;function eJ(e,t){let n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(65535&n)==65535||(65535&n)==65534||n>1114111?"�":String.fromCodePoint(n)}let eY=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function eZ(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){let e=n.charCodeAt(1),t=120===e||88===e;return eJ(n.slice(t?2:1),t?16:10)}return eC(n)||e}let eK={}.hasOwnProperty;function eX(e){return{line:e.line,column:e.column,offset:e.offset}}function e0(e,t){if(e)throw Error("Cannot close `"+e.type+"` ("+b({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+b({start:t.start,end:t.end})+") is open");throw Error("Cannot close document, a token (`"+t.type+"`, "+b({start:t.start,end:t.end})+") is still open")}function e1(e){let t=this;t.parser=function(n){var i,o;let a,l,s,c;return"string"!=typeof(i={...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})&&(o=i,i=void 0),(function(e){let t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:r(y),autolinkProtocol:c,autolinkEmail:c,atxHeading:r(f),blockQuote:r(function(){return{type:"blockquote",children:[]}}),characterEscape:c,characterReference:c,codeFenced:r(d),codeFencedFenceInfo:i,codeFencedFenceMeta:i,codeIndented:r(d,i),codeText:r(function(){return{type:"inlineCode",value:""}},i),codeTextData:c,data:c,codeFlowValue:c,definition:r(function(){return{type:"definition",identifier:"",label:null,title:null,url:""}}),definitionDestinationString:i,definitionLabelString:i,definitionTitleString:i,emphasis:r(function(){return{type:"emphasis",children:[]}}),hardBreakEscape:r(m),hardBreakTrailing:r(m),htmlFlow:r(g,i),htmlFlowData:c,htmlText:r(g,i),htmlTextData:c,image:r(function(){return{type:"image",title:null,url:"",alt:null}}),label:i,link:r(y),listItem:r(function(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}),listItemValue:function(e){this.data.expectingFirstListItemValue&&(this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0)},listOrdered:r(k,function(){this.data.expectingFirstListItemValue=!0}),listUnordered:r(k),paragraph:r(function(){return{type:"paragraph",children:[]}}),reference:function(){this.data.referenceType="collapsed"},referenceString:i,resourceDestinationString:i,resourceTitleString:i,setextHeading:r(f),strong:r(function(){return{type:"strong",children:[]}}),thematicBreak:r(function(){return{type:"thematicBreak"}})},exit:{atxHeading:a(),atxHeadingSequence:function(e){let t=this.stack[this.stack.length-1];t.depth||(t.depth=this.sliceSerialize(e).length)},autolink:a(),autolinkEmail:function(e){u.call(this,e),this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)},autolinkProtocol:function(e){u.call(this,e),this.stack[this.stack.length-1].url=this.sliceSerialize(e)},blockQuote:a(),characterEscapeValue:u,characterReferenceMarkerHexadecimal:p,characterReferenceMarkerNumeric:p,characterReferenceValue:function(e){let t,n=this.sliceSerialize(e),r=this.data.characterReferenceType;r?(t=eJ(n,"characterReferenceMarkerNumeric"===r?10:16),this.data.characterReferenceType=void 0):t=eC(n);let i=this.stack[this.stack.length-1];i.value+=t},characterReference:function(e){this.stack.pop().position.end=eX(e.end)},codeFenced:a(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}),codeFencedFence:function(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)},codeFencedFenceInfo:function(){let e=this.resume();this.stack[this.stack.length-1].lang=e},codeFencedFenceMeta:function(){let e=this.resume();this.stack[this.stack.length-1].meta=e},codeFlowValue:u,codeIndented:a(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}),codeText:a(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),codeTextData:u,data:u,definition:a(),definitionDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},definitionLabelString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,ed.B)(this.sliceSerialize(e)).toLowerCase()},definitionTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},emphasis:a(),hardBreakEscape:a(h),hardBreakTrailing:a(h),htmlFlow:a(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlFlowData:u,htmlText:a(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlTextData:u,image:a(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),label:function(){let e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];this.data.inReference=!0,"link"===n.type?n.children=e.children:n.alt=t},labelText:function(e){let t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=t.replace(eY,eZ),n.identifier=(0,ed.B)(t).toLowerCase()},lineEnding:function(e){let n=this.stack[this.stack.length-1];if(this.data.atHardBreak){n.children[n.children.length-1].position.end=eX(e.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(c.call(this,e),u.call(this,e))},link:a(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),listItem:a(),listOrdered:a(),listUnordered:a(),paragraph:a(),referenceString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=(0,ed.B)(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"},resourceDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},resourceTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},resource:function(){this.data.inReference=void 0},setextHeading:a(function(){this.data.setextHeadingSlurpLineEnding=void 0}),setextHeadingLineSequence:function(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2},setextHeadingText:function(){this.data.setextHeadingSlurpLineEnding=!0},strong:a(),thematicBreak:a()}};!function e(t,n){let r=-1;for(;++r<n.length;){let i=n[r];Array.isArray(i)?e(t,i):function(e,t){let n;for(n in t)if(eK.call(t,n))switch(n){case"canContainEols":{let r=t[n];r&&e[n].push(...r);break}case"transforms":{let r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{let r=t[n];r&&Object.assign(e[n],r)}}}(t,i)}}(t,(e||{}).mdastExtensions||[]);let n={};return function(e){let r={type:"root",children:[]},a={stack:[r],tokenStack:[],config:t,enter:o,exit:l,buffer:i,resume:s,data:n},c=[],u=-1;for(;++u<e.length;)("listOrdered"===e[u][1].type||"listUnordered"===e[u][1].type)&&("enter"===e[u][0]?c.push(u):u=function(e,t,n){let r,i,o,a,l=t-1,s=-1,c=!1;for(;++l<=n;){let t=e[l];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?s++:s--,a=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||a||s||o||(o=l),a=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:a=void 0}if(!s&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===s&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let a=l;for(i=void 0;a--;){let t=e[a];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;i&&(e[i][1].type="lineEndingBlank",c=!0),t[1].type="lineEnding",i=a}else if("linePrefix"===t[1].type||"blockQuotePrefix"===t[1].type||"blockQuotePrefixWhitespace"===t[1].type||"blockQuoteMarker"===t[1].type||"listItemIndent"===t[1].type);else break}o&&(!i||o<i)&&(r._spread=!0),r.end=Object.assign({},i?e[i][1].start:t[1].end),e.splice(i||l,0,["exit",r,t[2]]),l++,n++}if("listItemPrefix"===t[1].type){let i={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=i,e.splice(l,0,["enter",i,t[2]]),l++,n++,o=void 0,a=!0}}}return e[t][1]._spread=c,n}(e,c.pop(),u));for(u=-1;++u<e.length;){let n=t[e[u][0]];eK.call(n,e[u][1].type)&&n[e[u][1].type].call(Object.assign({sliceSerialize:e[u][2].sliceSerialize},a),e[u][1])}if(a.tokenStack.length>0){let e=a.tokenStack[a.tokenStack.length-1];(e[1]||e0).call(a,void 0,e[0])}for(r.position={start:eX(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:eX(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},u=-1;++u<t.transforms.length;)r=t.transforms[u](r)||r;return r};function r(e,t){return function(n){o.call(this,e(n),n),t&&t.call(this,n)}}function i(){this.stack.push({type:"fragment",children:[]})}function o(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:eX(t.start),end:void 0}}function a(e){return function(t){e&&e.call(this,t),l.call(this,t)}}function l(e,t){let n=this.stack.pop(),r=this.tokenStack.pop();if(r)r[0].type!==e.type&&(t?t.call(this,e,r[0]):(r[1]||e0).call(this,e,r[0]));else throw Error("Cannot close `"+e.type+"` ("+b({start:e.start,end:e.end})+"): it’s not open");n.position.end=eX(e.end)}function s(){return(0,z.d)(this.stack.pop())}function c(e){let t=this.stack[this.stack.length-1].children,n=t[t.length-1];n&&"text"===n.type||((n={type:"text",value:""}).position={start:eX(e.start),end:void 0},t.push(n)),this.stack.push(n)}function u(e){let t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=eX(e.end)}function h(){this.data.atHardBreak=!0}function p(e){this.data.characterReferenceType=e.type}function d(){return{type:"code",lang:null,meta:null,value:""}}function f(){return{type:"heading",depth:0,children:[]}}function m(){return{type:"break"}}function g(){return{type:"html",value:""}}function y(){return{type:"link",title:null,url:"",children:[]}}function k(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}})(o)(function(e){for(;!$(e););return e}((function(e){let t={constructs:(0,H.y)([r,...(e||{}).extensions||[]]),content:n(q),defined:[],document:n(W),flow:n(Z),lazy:{},string:n(X),text:n(ee)};return t;function n(e){return function(n){return function(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0},i={},o=[],a=[],l=[],s={attempt:f(function(e,t){m(e,t.from)}),check:f(d),consume:function(e){(0,U.HP)(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,g()):-1!==e&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===a[r._index].length&&(r._bufferIndex=-1,r._index++)),c.previous=e},enter:function(e,t){let n=t||{};return n.type=e,n.start=p(),c.events.push(["enter",n,c]),l.push(n),n},exit:function(e){let t=l.pop();return t.end=p(),c.events.push(["exit",t,c]),t},interrupt:f(d,{interrupt:!0})},c={code:null,containerState:{},defineSkip:function(e){i[e.line]=e.column,g()},events:[],now:p,parser:e,previous:null,sliceSerialize:function(e,t){return function(e,t){let n,r=-1,i=[];for(;++r<e.length;){let o,a=e[r];if("string"==typeof a)o=a;else switch(a){case -5:o="\r";break;case -4:o="\n";break;case -3:o="\r\n";break;case -2:o=t?" ":"	";break;case -1:if(!t&&n)continue;o=" ";break;default:o=String.fromCharCode(a)}n=-2===a,i.push(o)}return i.join("")}(h(e),t)},sliceStream:h,write:function(e){return(a=(0,B.V)(a,e),function(){let e;for(;r._index<a.length;){let n=a[r._index];if("string"==typeof n)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<n.length;){var t;t=n.charCodeAt(r._bufferIndex),u=u(t)}else u=u(n)}}(),null!==a[a.length-1])?[]:(m(t,0),c.events=(0,eN.W)(o,c.events,c),c.events)}},u=t.tokenize.call(c,s);return t.resolveAll&&o.push(t),c;function h(e){return function(e,t){let n,r=t.start._index,i=t.start._bufferIndex,o=t.end._index,a=t.end._bufferIndex;if(r===o)n=[e[r].slice(i,a)];else{if(n=e.slice(r,o),i>-1){let e=n[0];"string"==typeof e?n[0]=e.slice(i):n.shift()}a>0&&n.push(e[o].slice(0,a))}return n}(a,e)}function p(){let{_bufferIndex:e,_index:t,line:n,column:i,offset:o}=r;return{_bufferIndex:e,_index:t,line:n,column:i,offset:o}}function d(e,t){t.restore()}function f(e,t){return function(n,i,o){var a;let u,h,d,f;return Array.isArray(n)?m(n):"tokenize"in n?m([n]):(a=n,function(e){let t=null!==e&&a[e],n=null!==e&&a.null;return m([...Array.isArray(t)?t:t?[t]:[],...Array.isArray(n)?n:n?[n]:[]])(e)});function m(e){return(u=e,h=0,0===e.length)?o:b(e[h])}function b(e){return function(n){return(f=function(){let e=p(),t=c.previous,n=c.currentConstruct,i=c.events.length,o=Array.from(l);return{from:i,restore:function(){r=e,c.previous=t,c.currentConstruct=n,c.events.length=i,l=o,g()}}}(),d=e,e.partial||(c.currentConstruct=e),e.name&&c.parser.constructs.disable.null.includes(e.name))?k(n):e.tokenize.call(t?Object.assign(Object.create(c),t):c,s,y,k)(n)}}function y(t){return e(d,f),i}function k(e){return(f.restore(),++h<u.length)?b(u[h]):o}}}function m(e,t){e.resolveAll&&!o.includes(e)&&o.push(e),e.resolve&&(0,B.m)(c.events,t,c.events.length-t,e.resolve(c.events.slice(t),c)),e.resolveTo&&(c.events=e.resolveTo(c.events,c))}function g(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}(t,e,n)}}})(o).document().write((l=1,s="",c=!0,function(e,t,n){let r,i,o,u,h,p=[];for(e=s+("string"==typeof e?e.toString():new TextDecoder(t||void 0).decode(e)),o=0,s="",c&&(65279===e.charCodeAt(0)&&o++,c=void 0);o<e.length;){if(eQ.lastIndex=o,u=(r=eQ.exec(e))&&void 0!==r.index?r.index:e.length,h=e.charCodeAt(u),!r){s=e.slice(o);break}if(10===h&&o===u&&a)p.push(-3),a=void 0;else switch(a&&(p.push(-5),a=void 0),o<u&&(p.push(e.slice(o,u)),l+=u-o),h){case 0:p.push(65533),l++;break;case 9:for(i=4*Math.ceil(l/4),p.push(-2);l++<i;)p.push(-1);break;case 10:p.push(-4),l=1;break;default:a=!0,l=1}o=u+1}return n&&(a&&p.push(-5),s&&p.push(s),p.push(null)),p})(n,i,!0))))}}var e2=n(66945);function e3(e){let t=[],n=-1,r=0,i=0;for(;++n<e.length;){let o=e.charCodeAt(n),a="";if(37===o&&(0,U.lV)(e.charCodeAt(n+1))&&(0,U.lV)(e.charCodeAt(n+2)))i=2;else if(o<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(o))||(a=String.fromCharCode(o));else if(o>55295&&o<57344){let t=e.charCodeAt(n+1);o<56320&&t>56319&&t<57344?(a=String.fromCharCode(o,t),i=1):a="�"}else a=String.fromCharCode(o);a&&(t.push(e.slice(r,n),encodeURIComponent(a)),r=n+i+1,a=""),i&&(n+=i,i=0)}return t.join("")+e.slice(r)}function e4(e,t){let n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function e5(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}var e6=n(88428);function e9(e,t){let n=t.referenceType,r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];let i=e.all(t),o=i[0];o&&"text"===o.type?o.value="["+o.value:i.unshift({type:"text",value:"["});let a=i[i.length-1];return a&&"text"===a.type?a.value+=r:i.push({type:"text",value:r}),i}function e7(e){let t=e.spread;return null==t?e.children.length>1:t}function e8(e,t,n){let r=0,i=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(i-1);for(;9===t||32===t;)i--,t=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}let te={blockquote:function(e,t){let n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){let n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){let n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let i={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(i.data={meta:t.meta}),e.patch(t,i),i={type:"element",tagName:"pre",properties:{},children:[i=e.applyData(t,i)]},e.patch(t,i),i},delete:function(e,t){let n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){let n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){let n,r="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",i=String(t.identifier).toUpperCase(),o=e3(i.toLowerCase()),a=e.footnoteOrder.indexOf(i),l=e.footnoteCounts.get(i);void 0===l?(l=0,e.footnoteOrder.push(i),n=e.footnoteOrder.length):n=a+1,l+=1,e.footnoteCounts.set(i,l);let s={type:"element",tagName:"a",properties:{href:"#"+r+"fn-"+o,id:r+"fnref-"+o+(l>1?"-"+l:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(n)}]};e.patch(t,s);let c={type:"element",tagName:"sup",properties:{},children:[s]};return e.patch(t,c),e.applyData(t,c)},heading:function(e,t){let n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){let n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return e9(e,t);let i={src:e3(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(i.title=r.title);let o={type:"element",tagName:"img",properties:i,children:[]};return e.patch(t,o),e.applyData(t,o)},image:function(e,t){let n={src:e3(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){let n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);let r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)},linkReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return e9(e,t);let i={href:e3(r.url||"")};null!==r.title&&void 0!==r.title&&(i.title=r.title);let o={type:"element",tagName:"a",properties:i,children:e.all(t)};return e.patch(t,o),e.applyData(t,o)},link:function(e,t){let n={href:e3(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){let r=e.all(t),i=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;let n=e.children,r=-1;for(;!t&&++r<n.length;)t=e7(n[r])}return t}(n):e7(t),o={},a=[];if("boolean"==typeof t.checked){let e,n=r[0];n&&"element"===n.type&&"p"===n.tagName?e=n:(e={type:"element",tagName:"p",properties:{},children:[]},r.unshift(e)),e.children.length>0&&e.children.unshift({type:"text",value:" "}),e.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),o.className=["task-list-item"]}let l=-1;for(;++l<r.length;){let e=r[l];(i||0!==l||"element"!==e.type||"p"!==e.tagName)&&a.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||i?a.push(e):a.push(...e.children)}let s=r[r.length-1];s&&(i||"element"!==s.type||"p"!==s.tagName)&&a.push({type:"text",value:"\n"});let c={type:"element",tagName:"li",properties:o,children:a};return e.patch(t,c),e.applyData(t,c)},list:function(e,t){let n={},r=e.all(t),i=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++i<r.length;){let e=r[i];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let o={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,o),e.applyData(t,o)},paragraph:function(e,t){let n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){let n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){let n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){let n=e.all(t),r=n.shift(),i=[];if(r){let n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),i.push(n)}if(n.length>0){let r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},o=(0,g.PW)(t.children[1]),a=(0,g.Y)(t.children[t.children.length-1]);o&&a&&(r.position={start:o,end:a}),i.push(r)}let o={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(t,o),e.applyData(t,o)},tableCell:function(e,t){let n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){let r=n?n.children:void 0,i=0===(r?r.indexOf(t):1)?"th":"td",o=n&&"table"===n.type?n.align:void 0,a=o?o.length:t.children.length,l=-1,s=[];for(;++l<a;){let n=t.children[l],r={},a=o?o[l]:void 0;a&&(r.align=a);let c={type:"element",tagName:i,properties:r,children:[]};n&&(c.children=e.all(n),e.patch(n,c),c=e.applyData(n,c)),s.push(c)}let c={type:"element",tagName:"tr",properties:{},children:e.wrap(s,!0)};return e.patch(t,c),e.applyData(t,c)},text:function(e,t){let n={type:"text",value:function(e){let t=String(e),n=/\r?\n|\r/g,r=n.exec(t),i=0,o=[];for(;r;)o.push(e8(t.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=n.exec(t);return o.push(e8(t.slice(i),i>0,!1)),o.join("")}(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){let n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:tt,yaml:tt,definition:tt,footnoteDefinition:tt};function tt(){}let tn={}.hasOwnProperty,tr={};function ti(e,t){e.position&&(t.position=(0,g.G1)(e))}function to(e,t){let n=t;if(e&&e.data){let t=e.data.hName,r=e.data.hChildren,i=e.data.hProperties;"string"==typeof t&&("element"===n.type?n.tagName=t:n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}),"element"===n.type&&i&&Object.assign(n.properties,(0,e2.Ay)(i)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function ta(e,t){let n=[],r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function tl(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function ts(e,t){let n=function(e,t){let n=t||tr,r=new Map,i=new Map,o={all:function(e){let t=[];if("children"in e){let n=e.children,r=-1;for(;++r<n.length;){let i=o.one(n[r],e);if(i){if(r&&"break"===n[r-1].type&&(Array.isArray(i)||"text"!==i.type||(i.value=tl(i.value)),!Array.isArray(i)&&"element"===i.type)){let e=i.children[0];e&&"text"===e.type&&(e.value=tl(e.value))}Array.isArray(i)?t.push(...i):t.push(i)}}}return t},applyData:to,definitionById:r,footnoteById:i,footnoteCounts:new Map,footnoteOrder:[],handlers:{...te,...n.handlers},one:function(e,t){let n=e.type,r=o.handlers[n];if(tn.call(o.handlers,n)&&r)return r(o,e,t);if(o.options.passThrough&&o.options.passThrough.includes(n)){if("children"in e){let{children:t,...n}=e,r=(0,e2.Ay)(n);return r.children=o.all(e),r}return(0,e2.Ay)(e)}return(o.options.unknownHandler||function(e,t){let n=t.data||{},r="value"in t&&!(tn.call(n,"hProperties")||tn.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)})(o,e,t)},options:n,patch:ti,wrap:ta};return(0,e6.YR)(e,function(e){if("definition"===e.type||"footnoteDefinition"===e.type){let t="definition"===e.type?r:i,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}}),o}(e,t),r=n.one(e,void 0),o=function(e){let t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||e4,r=e.options.footnoteBackLabel||e5,i=e.options.footnoteLabel||"Footnotes",o=e.options.footnoteLabelTagName||"h2",a=e.options.footnoteLabelProperties||{className:["sr-only"]},l=[],s=-1;for(;++s<e.footnoteOrder.length;){let i=e.footnoteById.get(e.footnoteOrder[s]);if(!i)continue;let o=e.all(i),a=String(i.identifier).toUpperCase(),c=e3(a.toLowerCase()),u=0,h=[],p=e.footnoteCounts.get(a);for(;void 0!==p&&++u<=p;){h.length>0&&h.push({type:"text",value:" "});let e="string"==typeof n?n:n(s,u);"string"==typeof e&&(e={type:"text",value:e}),h.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+c+(u>1?"-"+u:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(s,u),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}let d=o[o.length-1];if(d&&"element"===d.type&&"p"===d.tagName){let e=d.children[d.children.length-1];e&&"text"===e.type?e.value+=" ":d.children.push({type:"text",value:" "}),d.children.push(...h)}else o.push(...h);let f={type:"element",tagName:"li",properties:{id:t+"fn-"+c},children:e.wrap(o,!0)};e.patch(i,f),l.push(f)}if(0!==l.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:o,properties:{...(0,e2.Ay)(a),id:"footnote-label"},children:[{type:"text",value:i}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(l,!0)},{type:"text",value:"\n"}]}}(n),a=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return o&&((0,i.ok)("children"in a),a.children.push({type:"text",value:"\n"},o)),a}function tc(e,t){return e&&"run"in e?async function(n,r){let i=ts(n,{file:r,...t});await e.run(i,r)}:function(n,r){return ts(n,{file:r,...e||t})}}function tu(e){if(e)throw e}var th=n(53360);function tp(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}let td={basename:function(e,t){let n;if(void 0!==t&&"string"!=typeof t)throw TypeError('"ext" argument must be a string');tf(e);let r=0,i=-1,o=e.length;if(void 0===t||0===t.length||t.length>e.length){for(;o--;)if(47===e.codePointAt(o)){if(n){r=o+1;break}}else i<0&&(n=!0,i=o+1);return i<0?"":e.slice(r,i)}if(t===e)return"";let a=-1,l=t.length-1;for(;o--;)if(47===e.codePointAt(o)){if(n){r=o+1;break}}else a<0&&(n=!0,a=o+1),l>-1&&(e.codePointAt(o)===t.codePointAt(l--)?l<0&&(i=o):(l=-1,i=a));return r===i?i=a:i<0&&(i=e.length),e.slice(r,i)},dirname:function(e){let t;if(tf(e),0===e.length)return".";let n=-1,r=e.length;for(;--r;)if(47===e.codePointAt(r)){if(t){n=r;break}}else t||(t=!0);return n<0?47===e.codePointAt(0)?"/":".":1===n&&47===e.codePointAt(0)?"//":e.slice(0,n)},extname:function(e){let t;tf(e);let n=e.length,r=-1,i=0,o=-1,a=0;for(;n--;){let l=e.codePointAt(n);if(47===l){if(t){i=n+1;break}continue}r<0&&(t=!0,r=n+1),46===l?o<0?o=n:1!==a&&(a=1):o>-1&&(a=-1)}return o<0||r<0||0===a||1===a&&o===r-1&&o===i+1?"":e.slice(o,r)},join:function(...e){let t,n=-1;for(;++n<e.length;)tf(e[n]),e[n]&&(t=void 0===t?e[n]:t+"/"+e[n]);return void 0===t?".":function(e){tf(e);let t=47===e.codePointAt(0),n=function(e,t){let n,r,i="",o=0,a=-1,l=0,s=-1;for(;++s<=e.length;){if(s<e.length)n=e.codePointAt(s);else if(47===n)break;else n=47;if(47===n){if(a===s-1||1===l);else if(a!==s-1&&2===l){if(i.length<2||2!==o||46!==i.codePointAt(i.length-1)||46!==i.codePointAt(i.length-2)){if(i.length>2){if((r=i.lastIndexOf("/"))!==i.length-1){r<0?(i="",o=0):o=(i=i.slice(0,r)).length-1-i.lastIndexOf("/"),a=s,l=0;continue}}else if(i.length>0){i="",o=0,a=s,l=0;continue}}t&&(i=i.length>0?i+"/..":"..",o=2)}else i.length>0?i+="/"+e.slice(a+1,s):i=e.slice(a+1,s),o=s-a-1;a=s,l=0}else 46===n&&l>-1?l++:l=-1}return i}(e,!t);return 0!==n.length||t||(n="."),n.length>0&&47===e.codePointAt(e.length-1)&&(n+="/"),t?"/"+n:n}(t)},sep:"/"};function tf(e){if("string"!=typeof e)throw TypeError("Path must be a string. Received "+JSON.stringify(e))}let tm={cwd:function(){return"/"}};function tg(e){return!!(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}let tb=["history","path","basename","stem","extname","dirname"];class ty{constructor(e){let t,n;t=e?tg(e)?{path:e}:"string"==typeof e||function(e){return!!(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(e)?{value:e}:e:{},this.cwd="cwd"in t?"":tm.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<tb.length;){let e=tb[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)tb.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?td.basename(this.path):void 0}set basename(e){tx(e,"basename"),tk(e,"basename"),this.path=td.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?td.dirname(this.path):void 0}set dirname(e){tv(this.basename,"dirname"),this.path=td.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?td.extname(this.path):void 0}set extname(e){if(tk(e,"extname"),tv(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw Error("`extname` must start with `.`");if(e.includes(".",1))throw Error("`extname` cannot contain multiple dots")}this.path=td.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){tg(e)&&(e=function(e){if("string"==typeof e)e=new URL(e);else if(!tg(e)){let t=TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if("file:"!==e.protocol){let e=TypeError("The URL must be of scheme file");throw e.code="ERR_INVALID_URL_SCHEME",e}return function(e){if(""!==e.hostname){let e=TypeError('File URL host must be "localhost" or empty on darwin');throw e.code="ERR_INVALID_FILE_URL_HOST",e}let t=e.pathname,n=-1;for(;++n<t.length;)if(37===t.codePointAt(n)&&50===t.codePointAt(n+1)){let e=t.codePointAt(n+2);if(70===e||102===e){let e=TypeError("File URL path must not include encoded / characters");throw e.code="ERR_INVALID_FILE_URL_PATH",e}}return decodeURIComponent(t)}(e)}(e)),tx(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?td.basename(this.path,this.extname):void 0}set stem(e){tx(e,"stem"),tk(e,"stem"),this.path=td.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){let r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){let r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){let r=new v(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){return void 0===this.value?"":"string"==typeof this.value?this.value:new TextDecoder(e||void 0).decode(this.value)}}function tk(e,t){if(e&&e.includes(td.sep))throw Error("`"+t+"` cannot be a path: did not expect `"+td.sep+"`")}function tx(e,t){if(!e)throw Error("`"+t+"` cannot be empty")}function tv(e,t){if(!e)throw Error("Setting `"+t+"` requires `path` to be set too")}let tw=function(e){let t=this.constructor.prototype,n=t[e],r=function(){return n.apply(r,arguments)};return Object.setPrototypeOf(r,t),r},tS={}.hasOwnProperty;class t_ extends tw{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=function(){let e=[],t={run:function(...t){let n=-1,r=t.pop();if("function"!=typeof r)throw TypeError("Expected function as last argument, not "+r);!function i(o,...a){let l=e[++n],s=-1;if(o)return void r(o);for(;++s<t.length;)(null===a[s]||void 0===a[s])&&(a[s]=t[s]);t=a,l?(function(e,t){let n;return function(...t){let o,a=e.length>t.length;a&&t.push(r);try{o=e.apply(this,t)}catch(e){if(a&&n)throw e;return r(e)}a||(o&&o.then&&"function"==typeof o.then?o.then(i,r):o instanceof Error?r(o):i(o))};function r(e,...i){n||(n=!0,t(e,...i))}function i(e){r(null,e)}})(l,i)(...a):r(null,...a)}(null,...t)},use:function(n){if("function"!=typeof n)throw TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}()}copy(){let e=new t_,t=-1;for(;++t<this.attachers.length;){let n=this.attachers[t];e.use(...n)}return e.data(th(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2==arguments.length?(tT("data",this.frozen),this.namespace[e]=t,this):tS.call(this.namespace,e)&&this.namespace[e]||void 0:e?(tT("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;for(;++this.freezeIndex<this.attachers.length;){let[e,...t]=this.attachers[this.freezeIndex];if(!1===t[0])continue;!0===t[0]&&(t[0]=void 0);let n=e.call(this,...t);"function"==typeof n&&this.transformers.use(n)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();let t=tL(e),n=this.parser||this.Parser;return tP("parse",n),n(String(t),t)}process(e,t){let n=this;return this.freeze(),tP("process",this.parser||this.Parser),tA("process",this.compiler||this.Compiler),t?r(void 0,t):new Promise(r);function r(r,o){let a=tL(e),l=n.parse(a);function s(e,n){e||!n?o(e):r?r(n):((0,i.ok)(t,"`done` is defined if `resolve` is not"),t(void 0,n))}n.run(l,a,function(e,t,r){var i,o;if(e||!t||!r)return s(e);let a=n.stringify(t,r);"string"==typeof(i=a)||(o=i)&&"object"==typeof o&&"byteLength"in o&&"byteOffset"in o?r.value=a:r.result=a,s(e,r)})}}processSync(e){let t,n=!1;return this.freeze(),tP("processSync",this.parser||this.Parser),tA("processSync",this.compiler||this.Compiler),this.process(e,function(e,r){n=!0,tu(e),t=r}),tE("processSync","process",n),(0,i.ok)(t,"we either bailed on an error or have a tree"),t}run(e,t,n){tN(e),this.freeze();let r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?o(void 0,n):new Promise(o);function o(o,a){(0,i.ok)("function"!=typeof t,"`file` can’t be a `done` anymore, we checked");let l=tL(t);r.run(e,l,function(t,r,l){let s=r||e;t?a(t):o?o(s):((0,i.ok)(n,"`done` is defined if `resolve` is not"),n(void 0,s,l))})}}runSync(e,t){let n,r=!1;return this.run(e,t,function(e,t){tu(e),n=t,r=!0}),tE("runSync","run",r),(0,i.ok)(n,"we either bailed on an error or have a tree"),n}stringify(e,t){this.freeze();let n=tL(t),r=this.compiler||this.Compiler;return tA("stringify",r),tN(e),r(e,n)}use(e,...t){let n=this.attachers,r=this.namespace;if(tT("use",this.frozen),null==e);else if("function"==typeof e)a(e,t);else if("object"==typeof e)Array.isArray(e)?o(e):i(e);else throw TypeError("Expected usable value, not `"+e+"`");return this;function i(e){if(!("plugins"in e)&&!("settings"in e))throw Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");o(e.plugins),e.settings&&(r.settings=th(!0,r.settings,e.settings))}function o(e){let t=-1;if(null==e);else if(Array.isArray(e))for(;++t<e.length;){var n=e[t];if("function"==typeof n)a(n,[]);else if("object"==typeof n)if(Array.isArray(n)){let[e,...t]=n;a(e,t)}else i(n);else throw TypeError("Expected usable value, not `"+n+"`")}else throw TypeError("Expected a list of plugins, not `"+e+"`")}function a(e,t){let r=-1,i=-1;for(;++r<n.length;)if(n[r][0]===e){i=r;break}if(-1===i)n.push([e,...t]);else if(t.length>0){let[r,...o]=t,a=n[i][1];tp(a)&&tp(r)&&(r=th(!0,a,r)),n[i]=[e,r,...o]}}}}let tC=new t_().freeze();function tP(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `parser`")}function tA(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `compiler`")}function tT(e,t){if(t)throw Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function tN(e){if(!tp(e)||"string"!=typeof e.type)throw TypeError("Expected node, got `"+e+"`")}function tE(e,t,n){if(!n)throw Error("`"+e+"` finished async. Use `"+t+"` instead")}function tL(e){var t;return(t=e)&&"object"==typeof t&&"message"in t&&"messages"in t?e:new ty(e)}let tI=[],tR={allowDangerousHtml:!0},tO=/^(https?|ircs?|mailto|xmpp)$/i,tD=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function tM(e){let t=function(e){let t=e.rehypePlugins||tI,n=e.remarkPlugins||tI,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...tR}:tR;return tC().use(e1).use(n).use(tc,r).use(t)}(e),n=function(e){let t=e.children||"",n=new ty;return"string"==typeof t?n.value=t:(0,i.HB)("Unexpected value `"+t+"` for `children` prop, expected `string`"),n}(e);return function(e,t){let n=t.allowedElements,r=t.allowElement,o=t.components,a=t.disallowedElements,l=t.skipHtml,s=t.unwrapDisallowed,c=t.urlTransform||tz;for(let e of tD)Object.hasOwn(t,e.from)&&(0,i.HB)("Unexpected `"+e.from+"` prop, "+(e.to?"use `"+e.to+"` instead":"remove it")+" (see <https://github.com/remarkjs/react-markdown/blob/main/changelog.md#"+e.id+"> for more info)");return n&&a&&(0,i.HB)("Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other"),(0,e6.YR)(e,function(e,t,i){if("raw"===e.type&&i&&"number"==typeof t)return l?i.children.splice(t,1):i.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in D)if(Object.hasOwn(D,t)&&Object.hasOwn(e.properties,t)){let n=e.properties[t],r=D[t];(null===r||r.includes(e.tagName))&&(e.properties[t]=c(String(n||""),t,e))}}if("element"===e.type){let o=n?!n.includes(e.tagName):!!a&&a.includes(e.tagName);if(!o&&r&&"number"==typeof t&&(o=!r(e,t,i)),o&&i&&"number"==typeof t)return s&&e.children?i.children.splice(t,1,...e.children):i.children.splice(t,1),t}}),function(e,t){var n,r,i,o,a;let l;if(!t||void 0===t.Fragment)throw TypeError("Expected `Fragment` in options");let s=t.filePath||void 0;if(t.development){if("function"!=typeof t.jsxDEV)throw TypeError("Expected `jsxDEV` in options when `development: true`");n=s,r=t.jsxDEV,l=function(e,t,i,o){let a=Array.isArray(i.children),l=(0,g.PW)(e);return r(t,i,o,a,{columnNumber:l?l.column-1:void 0,fileName:n,lineNumber:l?l.line:void 0},void 0)}}else{if("function"!=typeof t.jsx)throw TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw TypeError("Expected `jsxs` in production options");i=0,o=t.jsx,a=t.jsxs,l=function(e,t,n,r){let i=Array.isArray(n.children)?a:o;return r?i(t,n,r):i(t,n)}}let c={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:l,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:s,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?h.JW:h.qy,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},u=T(c,e,void 0);return u&&"string"!=typeof u?u:c.create(e,c.Fragment,{children:u||void 0},void 0)}(e,{Fragment:M.Fragment,components:o,ignoreInvalidStyle:!0,jsx:M.jsx,jsxs:M.jsxs,passKeys:!0,passNode:!0})}(t.runSync(t.parse(n),n),e)}function tz(e){let t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),i=e.indexOf("/");return -1===t||-1!==i&&t>i||-1!==n&&t>n||-1!==r&&t>r||tO.test(e.slice(0,t))?e:""}},31300:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelCase=void 0;var n=/^--[a-zA-Z0-9_-]+$/,r=/-([a-z])/g,i=/^[^-]+$/,o=/^-(webkit|moz|ms|o|khtml)-/,a=/^-(ms)-/,l=function(e,t){return t.toUpperCase()},s=function(e,t){return"".concat(t,"-")};t.camelCase=function(e,t){var c;return(void 0===t&&(t={}),!(c=e)||i.test(c)||n.test(c))?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(a,s):e.replace(o,s)).replace(r,l))}},33386:(e,t,n)=>{"use strict";function r(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}n.d(t,{B:()=>r})},34093:(e,t,n)=>{"use strict";function r(){}function i(){}n.d(t,{HB:()=>i,ok:()=>r})},36301:e=>{var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,i=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,o=/^:\s*/,a=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,l=/^[;\s]*/,s=/^\s+|\s+$/g;function c(e){return e?e.replace(s,""):""}e.exports=function(e,s){if("string"!=typeof e)throw TypeError("First argument must be a string");if(!e)return[];s=s||{};var u=1,h=1;function p(e){var t=e.match(n);t&&(u+=t.length);var r=e.lastIndexOf("\n");h=~r?e.length-r:h+e.length}function d(){var e={line:u,column:h};return function(t){return t.position=new f(e),b(r),t}}function f(e){this.start=e,this.end={line:u,column:h},this.source=s.source}f.prototype.content=e;var m=[];function g(t){var n=Error(s.source+":"+u+":"+h+": "+t);if(n.reason=t,n.filename=s.source,n.line=u,n.column=h,n.source=e,s.silent)m.push(n);else throw n}function b(t){var n=t.exec(e);if(n){var r=n[0];return p(r),e=e.slice(r.length),n}}function y(e){var t;for(e=e||[];t=k();)!1!==t&&e.push(t);return e}function k(){var t=d();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;""!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,""===e.charAt(n-1))return g("End of comment missing");var r=e.slice(2,n-2);return h+=2,p(r),e=e.slice(n),h+=2,t({type:"comment",comment:r})}}b(r);var x,v=[];for(y(v);x=function(){var e=d(),n=b(i);if(n){if(k(),!b(o))return g("property missing ':'");var r=b(a),s=e({type:"declaration",property:c(n[0].replace(t,"")),value:r?c(r[0].replace(t,"")):""});return b(l),s}}();)!1!==x&&(v.push(x),y(v));return v}},41974:(e,t,n)=>{"use strict";function r(e,t){let n=String(e);if("string"!=typeof t)throw TypeError("Expected character");let r=0,i=n.indexOf(t);for(;-1!==i;)r++,i=n.indexOf(t,i+t.length);return r}n.d(t,{D:()=>r})},49535:(e,t,n)=>{"use strict";n.d(t,{S:()=>i});var r=n(12556);function i(e){return null===e||(0,r.Ee)(e)||(0,r.Ny)(e)?1:(0,r.es)(e)?2:void 0}},51362:(e,t,n)=>{"use strict";n.d(t,{D:()=>c,N:()=>u});var r=n(12115),i=(e,t,n,r,i,o,a,l)=>{let s=document.documentElement,c=["light","dark"];function u(t){var n;(Array.isArray(e)?e:[e]).forEach(e=>{let n="class"===e,r=n&&o?i.map(e=>o[e]||e):i;n?(s.classList.remove(...r),s.classList.add(o&&o[t]?o[t]:t)):s.setAttribute(e,t)}),n=t,l&&c.includes(n)&&(s.style.colorScheme=n)}if(r)u(r);else try{let e=localStorage.getItem(t)||n,r=a&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;u(r)}catch(e){}},o=["light","dark"],a="(prefers-color-scheme: dark)",l=r.createContext(void 0),s={setTheme:e=>{},themes:[]},c=()=>{var e;return null!=(e=r.useContext(l))?e:s},u=e=>r.useContext(l)?r.createElement(r.Fragment,null,e.children):r.createElement(p,{...e}),h=["light","dark"],p=e=>{let{forcedTheme:t,disableTransitionOnChange:n=!1,enableSystem:i=!0,enableColorScheme:s=!0,storageKey:c="theme",themes:u=h,defaultTheme:p=i?"system":"light",attribute:b="data-theme",value:y,children:k,nonce:x,scriptProps:v}=e,[w,S]=r.useState(()=>f(c,p)),[_,C]=r.useState(()=>"system"===w?g():w),P=y?Object.values(y):u,A=r.useCallback(e=>{let t=e;if(!t)return;"system"===e&&i&&(t=g());let r=y?y[t]:t,a=n?m(x):null,l=document.documentElement,c=e=>{"class"===e?(l.classList.remove(...P),r&&l.classList.add(r)):e.startsWith("data-")&&(r?l.setAttribute(e,r):l.removeAttribute(e))};if(Array.isArray(b)?b.forEach(c):c(b),s){let e=o.includes(p)?p:null,n=o.includes(t)?t:e;l.style.colorScheme=n}null==a||a()},[x]),T=r.useCallback(e=>{let t="function"==typeof e?e(w):e;S(t);try{localStorage.setItem(c,t)}catch(e){}},[w]),N=r.useCallback(e=>{C(g(e)),"system"===w&&i&&!t&&A("system")},[w,t]);r.useEffect(()=>{let e=window.matchMedia(a);return e.addListener(N),N(e),()=>e.removeListener(N)},[N]),r.useEffect(()=>{let e=e=>{e.key===c&&(e.newValue?S(e.newValue):T(p))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[T]),r.useEffect(()=>{A(null!=t?t:w)},[t,w]);let E=r.useMemo(()=>({theme:w,setTheme:T,forcedTheme:t,resolvedTheme:"system"===w?_:w,themes:i?[...u,"system"]:u,systemTheme:i?_:void 0}),[w,T,t,_,i,u]);return r.createElement(l.Provider,{value:E},r.createElement(d,{forcedTheme:t,storageKey:c,attribute:b,enableSystem:i,enableColorScheme:s,defaultTheme:p,value:y,themes:u,nonce:x,scriptProps:v}),k)},d=r.memo(e=>{let{forcedTheme:t,storageKey:n,attribute:o,enableSystem:a,enableColorScheme:l,defaultTheme:s,value:c,themes:u,nonce:h,scriptProps:p}=e,d=JSON.stringify([o,n,s,t,u,c,a,l]).slice(1,-1);return r.createElement("script",{...p,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(i.toString(),")(").concat(d,")")}})}),f=(e,t)=>{let n;try{n=localStorage.getItem(e)||void 0}catch(e){}return n||t},m=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},g=e=>(e||(e=window.matchMedia(a)),e.matches?"dark":"light")},53360:e=>{"use strict";var t=Object.prototype.hasOwnProperty,n=Object.prototype.toString,r=Object.defineProperty,i=Object.getOwnPropertyDescriptor,o=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===n.call(e)},a=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var r,i=t.call(e,"constructor"),o=e.constructor&&e.constructor.prototype&&t.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!i&&!o)return!1;for(r in e);return void 0===r||t.call(e,r)},l=function(e,t){r&&"__proto__"===t.name?r(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},s=function(e,n){if("__proto__"===n){if(!t.call(e,n))return;else if(i)return i(e,n).value}return e[n]};e.exports=function e(){var t,n,r,i,c,u,h=arguments[0],p=1,d=arguments.length,f=!1;for("boolean"==typeof h&&(f=h,h=arguments[1]||{},p=2),(null==h||"object"!=typeof h&&"function"!=typeof h)&&(h={});p<d;++p)if(t=arguments[p],null!=t)for(n in t)r=s(h,n),h!==(i=s(t,n))&&(f&&i&&(a(i)||(c=o(i)))?(c?(c=!1,u=r&&o(r)?r:[]):u=r&&a(r)?r:{},l(h,{name:n,newValue:e(f,u,i)})):void 0!==i&&l(h,{name:n,newValue:i}));return h}},53724:function(e,t,n){"use strict";var r=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(n(87924)),i=n(31300);function o(e,t){var n={};return e&&"string"==typeof e&&(0,r.default)(e,function(e,r){e&&r&&(n[(0,i.camelCase)(e,t)]=r)}),n}o.default=o,e.exports=o},55548:(e,t,n)=>{"use strict";function r(e){let t=[],n=String(e||""),r=n.indexOf(","),i=0,o=!1;for(;!o;){-1===r&&(r=n.length,o=!0);let e=n.slice(i,r).trim();(e||!o)&&t.push(e),i=r+1,r=n.indexOf(",",i)}return t}function i(e,t){let n=t||{};return(""===e[e.length-1]?[...e,""]:e).join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}n.d(t,{A:()=>i,q:()=>r})},59739:(e,t,n)=>{"use strict";function r(e){return e.toLowerCase()}n.d(t,{S:()=>r})},66945:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>p});let r="object"==typeof self?self:globalThis,i=(e,t)=>{let n=(t,n)=>(e.set(n,t),t),i=o=>{if(e.has(o))return e.get(o);let[a,l]=t[o];switch(a){case 0:case -1:return n(l,o);case 1:{let e=n([],o);for(let t of l)e.push(i(t));return e}case 2:{let e=n({},o);for(let[t,n]of l)e[i(t)]=i(n);return e}case 3:return n(new Date(l),o);case 4:{let{source:e,flags:t}=l;return n(new RegExp(e,t),o)}case 5:{let e=n(new Map,o);for(let[t,n]of l)e.set(i(t),i(n));return e}case 6:{let e=n(new Set,o);for(let t of l)e.add(i(t));return e}case 7:{let{name:e,message:t}=l;return n(new r[e](t),o)}case 8:return n(BigInt(l),o);case"BigInt":return n(Object(BigInt(l)),o);case"ArrayBuffer":return n(new Uint8Array(l).buffer,l);case"DataView":{let{buffer:e}=new Uint8Array(l);return n(new DataView(e),l)}}return n(new r[a](l),o)};return i},o=e=>i(new Map,e)(0),{toString:a}={},{keys:l}=Object,s=e=>{let t=typeof e;if("object"!==t||!e)return[0,t];let n=a.call(e).slice(8,-1);switch(n){case"Array":return[1,""];case"Object":return[2,""];case"Date":return[3,""];case"RegExp":return[4,""];case"Map":return[5,""];case"Set":return[6,""];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},c=([e,t])=>0===e&&("function"===t||"symbol"===t),u=(e,t,n,r)=>{let i=(e,t)=>{let i=r.push(e)-1;return n.set(t,i),i},o=r=>{if(n.has(r))return n.get(r);let[a,u]=s(r);switch(a){case 0:{let t=r;switch(u){case"bigint":a=8,t=r.toString();break;case"function":case"symbol":if(e)throw TypeError("unable to serialize "+u);t=null;break;case"undefined":return i([-1],r)}return i([a,t],r)}case 1:{if(u){let e=r;return"DataView"===u?e=new Uint8Array(r.buffer):"ArrayBuffer"===u&&(e=new Uint8Array(r)),i([u,[...e]],r)}let e=[],t=i([a,e],r);for(let t of r)e.push(o(t));return t}case 2:{if(u)switch(u){case"BigInt":return i([u,r.toString()],r);case"Boolean":case"Number":case"String":return i([u,r.valueOf()],r)}if(t&&"toJSON"in r)return o(r.toJSON());let n=[],h=i([a,n],r);for(let t of l(r))(e||!c(s(r[t])))&&n.push([o(t),o(r[t])]);return h}case 3:return i([a,r.toISOString()],r);case 4:{let{source:e,flags:t}=r;return i([a,{source:e,flags:t}],r)}case 5:{let t=[],n=i([a,t],r);for(let[n,i]of r)(e||!(c(s(n))||c(s(i))))&&t.push([o(n),o(i)]);return n}case 6:{let t=[],n=i([a,t],r);for(let n of r)(e||!c(s(n)))&&t.push(o(n));return n}}let{message:h}=r;return i([a,{name:u,message:h}],r)};return o},h=(e,{json:t,lossy:n}={})=>{let r=[];return u(!(t||n),!!t,new Map,r)(e),r},p="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?o(h(e,t)):structuredClone(e):(e,t)=>o(h(e,t))},68581:(e,t,n)=>{"use strict";n.d(t,{R:()=>r});class r{constructor(e,t){this.attribute=t,this.property=e}}r.prototype.attribute="",r.prototype.booleanish=!1,r.prototype.boolean=!1,r.prototype.commaOrSpaceSeparated=!1,r.prototype.commaSeparated=!1,r.prototype.defined=!1,r.prototype.mustUseProperty=!1,r.prototype.number=!1,r.prototype.overloadedBoolean=!1,r.prototype.property="",r.prototype.spaceSeparated=!1,r.prototype.space=void 0},69381:(e,t,n)=>{"use strict";n.d(t,{y:()=>o});var r=n(11603);let i={}.hasOwnProperty;function o(e){let t={},n=-1;for(;++n<e.length;)!function(e,t){let n;for(n in t){let o,a=(i.call(e,n)?e[n]:void 0)||(e[n]={}),l=t[n];if(l)for(o in l){i.call(a,o)||(a[o]=[]);let e=l[o];!function(e,t){let n=-1,i=[];for(;++n<t.length;)("after"===t[n].add?e:i).push(t[n]);(0,r.m)(e,0,0,i)}(a[o],Array.isArray(e)?e:e?[e]:[])}}}(t,e[n]);return t}},70832:(e,t,n)=>{"use strict";n.d(t,{G1:()=>a,PW:()=>i,Y:()=>r});let r=o("end"),i=o("start");function o(e){return function(t){let n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function a(e){let t=i(e),n=r(e);if(t&&n)return{start:t,end:n}}},83846:(e,t,n)=>{"use strict";n.d(t,{qy:()=>b,JW:()=>y});class r{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}function i(e,t){let n={},i={};for(let t of e)Object.assign(n,t.property),Object.assign(i,t.normal);return new r(n,i,t)}r.prototype.normal={},r.prototype.property={},r.prototype.space=void 0;var o=n(59739),a=n(25437);function l(e){let t={},n={};for(let[r,i]of Object.entries(e.properties)){let l=new a.E(r,e.transform(e.attributes||{},r),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(l.mustUseProperty=!0),t[r]=l,n[(0,o.S)(r)]=r,n[(0,o.S)(l.attribute)]=r}return new r(t,n,e.space)}var s=n(8918);let c=l({properties:{ariaActiveDescendant:null,ariaAtomic:s.booleanish,ariaAutoComplete:null,ariaBusy:s.booleanish,ariaChecked:s.booleanish,ariaColCount:s.number,ariaColIndex:s.number,ariaColSpan:s.number,ariaControls:s.spaceSeparated,ariaCurrent:null,ariaDescribedBy:s.spaceSeparated,ariaDetails:null,ariaDisabled:s.booleanish,ariaDropEffect:s.spaceSeparated,ariaErrorMessage:null,ariaExpanded:s.booleanish,ariaFlowTo:s.spaceSeparated,ariaGrabbed:s.booleanish,ariaHasPopup:null,ariaHidden:s.booleanish,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:s.spaceSeparated,ariaLevel:s.number,ariaLive:null,ariaModal:s.booleanish,ariaMultiLine:s.booleanish,ariaMultiSelectable:s.booleanish,ariaOrientation:null,ariaOwns:s.spaceSeparated,ariaPlaceholder:null,ariaPosInSet:s.number,ariaPressed:s.booleanish,ariaReadOnly:s.booleanish,ariaRelevant:null,ariaRequired:s.booleanish,ariaRoleDescription:s.spaceSeparated,ariaRowCount:s.number,ariaRowIndex:s.number,ariaRowSpan:s.number,ariaSelected:s.booleanish,ariaSetSize:s.number,ariaSort:null,ariaValueMax:s.number,ariaValueMin:s.number,ariaValueNow:s.number,ariaValueText:null,role:null},transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase()});function u(e,t){return t in e?e[t]:t}function h(e,t){return u(e,t.toLowerCase())}let p=l({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:s.commaSeparated,acceptCharset:s.spaceSeparated,accessKey:s.spaceSeparated,action:null,allow:null,allowFullScreen:s.boolean,allowPaymentRequest:s.boolean,allowUserMedia:s.boolean,alt:null,as:null,async:s.boolean,autoCapitalize:null,autoComplete:s.spaceSeparated,autoFocus:s.boolean,autoPlay:s.boolean,blocking:s.spaceSeparated,capture:null,charSet:null,checked:s.boolean,cite:null,className:s.spaceSeparated,cols:s.number,colSpan:null,content:null,contentEditable:s.booleanish,controls:s.boolean,controlsList:s.spaceSeparated,coords:s.number|s.commaSeparated,crossOrigin:null,data:null,dateTime:null,decoding:null,default:s.boolean,defer:s.boolean,dir:null,dirName:null,disabled:s.boolean,download:s.overloadedBoolean,draggable:s.booleanish,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:s.boolean,formTarget:null,headers:s.spaceSeparated,height:s.number,hidden:s.overloadedBoolean,high:s.number,href:null,hrefLang:null,htmlFor:s.spaceSeparated,httpEquiv:s.spaceSeparated,id:null,imageSizes:null,imageSrcSet:null,inert:s.boolean,inputMode:null,integrity:null,is:null,isMap:s.boolean,itemId:null,itemProp:s.spaceSeparated,itemRef:s.spaceSeparated,itemScope:s.boolean,itemType:s.spaceSeparated,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:s.boolean,low:s.number,manifest:null,max:null,maxLength:s.number,media:null,method:null,min:null,minLength:s.number,multiple:s.boolean,muted:s.boolean,name:null,nonce:null,noModule:s.boolean,noValidate:s.boolean,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:s.boolean,optimum:s.number,pattern:null,ping:s.spaceSeparated,placeholder:null,playsInline:s.boolean,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:s.boolean,referrerPolicy:null,rel:s.spaceSeparated,required:s.boolean,reversed:s.boolean,rows:s.number,rowSpan:s.number,sandbox:s.spaceSeparated,scope:null,scoped:s.boolean,seamless:s.boolean,selected:s.boolean,shadowRootClonable:s.boolean,shadowRootDelegatesFocus:s.boolean,shadowRootMode:null,shape:null,size:s.number,sizes:null,slot:null,span:s.number,spellCheck:s.booleanish,src:null,srcDoc:null,srcLang:null,srcSet:null,start:s.number,step:null,style:null,tabIndex:s.number,target:null,title:null,translate:null,type:null,typeMustMatch:s.boolean,useMap:null,value:s.booleanish,width:s.number,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:s.spaceSeparated,axis:null,background:null,bgColor:null,border:s.number,borderColor:null,bottomMargin:s.number,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:s.boolean,declare:s.boolean,event:null,face:null,frame:null,frameBorder:null,hSpace:s.number,leftMargin:s.number,link:null,longDesc:null,lowSrc:null,marginHeight:s.number,marginWidth:s.number,noResize:s.boolean,noHref:s.boolean,noShade:s.boolean,noWrap:s.boolean,object:null,profile:null,prompt:null,rev:null,rightMargin:s.number,rules:null,scheme:null,scrolling:s.booleanish,standby:null,summary:null,text:null,topMargin:s.number,valueType:null,version:null,vAlign:null,vLink:null,vSpace:s.number,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:s.boolean,disableRemotePlayback:s.boolean,prefix:null,property:null,results:s.number,security:null,unselectable:null},space:"html",transform:h}),d=l({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:s.commaOrSpaceSeparated,accentHeight:s.number,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:s.number,amplitude:s.number,arabicForm:null,ascent:s.number,attributeName:null,attributeType:null,azimuth:s.number,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:s.number,by:null,calcMode:null,capHeight:s.number,className:s.spaceSeparated,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:s.number,diffuseConstant:s.number,direction:null,display:null,dur:null,divisor:s.number,dominantBaseline:null,download:s.boolean,dx:null,dy:null,edgeMode:null,editable:null,elevation:s.number,enableBackground:null,end:null,event:null,exponent:s.number,externalResourcesRequired:null,fill:null,fillOpacity:s.number,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:s.commaSeparated,g2:s.commaSeparated,glyphName:s.commaSeparated,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:s.number,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:s.number,horizOriginX:s.number,horizOriginY:s.number,id:null,ideographic:s.number,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:s.number,k:s.number,k1:s.number,k2:s.number,k3:s.number,k4:s.number,kernelMatrix:s.commaOrSpaceSeparated,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:s.number,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:s.number,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:s.number,overlineThickness:s.number,paintOrder:null,panose1:null,path:null,pathLength:s.number,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:s.spaceSeparated,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:s.number,pointsAtY:s.number,pointsAtZ:s.number,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:s.commaOrSpaceSeparated,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:s.commaOrSpaceSeparated,rev:s.commaOrSpaceSeparated,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:s.commaOrSpaceSeparated,requiredFeatures:s.commaOrSpaceSeparated,requiredFonts:s.commaOrSpaceSeparated,requiredFormats:s.commaOrSpaceSeparated,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:s.number,specularExponent:s.number,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:s.number,strikethroughThickness:s.number,string:null,stroke:null,strokeDashArray:s.commaOrSpaceSeparated,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:s.number,strokeOpacity:s.number,strokeWidth:null,style:null,surfaceScale:s.number,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:s.commaOrSpaceSeparated,tabIndex:s.number,tableValues:null,target:null,targetX:s.number,targetY:s.number,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:s.commaOrSpaceSeparated,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:s.number,underlineThickness:s.number,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:s.number,values:null,vAlphabetic:s.number,vMathematical:s.number,vectorEffect:null,vHanging:s.number,vIdeographic:s.number,version:null,vertAdvY:s.number,vertOriginX:s.number,vertOriginY:s.number,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:s.number,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:u}),f=l({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase()}),m=l({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:h}),g=l({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase()}),b=i([c,p,f,m,g],"html"),y=i([c,d,f,m,g],"svg")},87924:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=null;if(!e||"string"!=typeof e)return n;var r=(0,i.default)(e),o="function"==typeof t;return r.forEach(function(e){if("declaration"===e.type){var r=e.property,i=e.value;o?t(r,i,e):i&&((n=n||{})[r]=i)}}),n};var i=r(n(36301))},88428:(e,t,n)=>{"use strict";n.d(t,{YR:()=>i});var r=n(1922);function i(e,t,n,i){let o,a,l;"function"==typeof t&&"function"!=typeof n?(a=void 0,l=t,o=n):(a=t,l=n,o=i),(0,r.VG)(e,a,function(e,t){let n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return l(e,r,n)},o)}},91877:(e,t,n)=>{"use strict";function r(e,t,n){let r=[],i=-1;for(;++i<e.length;){let o=e[i].resolveAll;o&&!r.includes(o)&&(t=o(t,n),r.push(o))}return t}n.d(t,{W:()=>r})},93836:(e,t,n)=>{"use strict";n.d(t,{A:()=>eF});var r=n(41974),i=n(34093),o=n(12556),a=n(1922),l=n(17915);let s="phrasing",c=["autolink","link","image","label"];function u(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function h(e){this.config.enter.autolinkProtocol.call(this,e)}function p(e){this.config.exit.autolinkProtocol.call(this,e)}function d(e){this.config.exit.data.call(this,e);let t=this.stack[this.stack.length-1];(0,i.ok)("link"===t.type),t.url="http://"+this.sliceSerialize(e)}function f(e){this.config.exit.autolinkEmail.call(this,e)}function m(e){this.exit(e)}function g(e){!function(e,t,n){let r=(0,l.C)((n||{}).ignore||[]),i=function(e){let t=[];if(!Array.isArray(e))throw TypeError("Expected find and replace tuple or list of tuples");let n=!e[0]||Array.isArray(e[0])?e:[e],r=-1;for(;++r<n.length;){var i;let e=n[r];t.push(["string"==typeof(i=e[0])?RegExp(function(e){if("string"!=typeof e)throw TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}(i),"g"):i,function(e){return"function"==typeof e?e:function(){return e}}(e[1])])}return t}(t),o=-1;for(;++o<i.length;)(0,a.VG)(e,"text",s);function s(e,t){let n,a=-1;for(;++a<t.length;){let e=t[a],i=n?n.children:void 0;if(r(e,i?i.indexOf(e):void 0,n))return;n=e}if(n)return function(e,t){let n=t[t.length-1],r=i[o][0],a=i[o][1],l=0,s=n.children.indexOf(e),c=!1,u=[];r.lastIndex=0;let h=r.exec(e.value);for(;h;){let n=h.index,i={index:h.index,input:h.input,stack:[...t,e]},o=a(...h,i);if("string"==typeof o&&(o=o.length>0?{type:"text",value:o}:void 0),!1===o?r.lastIndex=n+1:(l!==n&&u.push({type:"text",value:e.value.slice(l,n)}),Array.isArray(o)?u.push(...o):o&&u.push(o),l=n+h[0].length,c=!0),!r.global)break;h=r.exec(e.value)}return c?(l<e.value.length&&u.push({type:"text",value:e.value.slice(l)}),n.children.splice(s,1,...u)):u=[e],s+u.length}(e,t)}}(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,b],[/(?<=^|\s|\p{P}|\p{S})([-.\w+]+)@([-\w]+(?:\.[-\w]+)+)/gu,y]],{ignore:["link","linkReference"]})}function b(e,t,n,i,o){let a="";if(!k(o)||(/^w/i.test(t)&&(n=t+n,t="",a="http://"),!function(e){let t=e.split(".");return!(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))}(n)))return!1;let l=function(e){let t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let n=t[0],i=n.indexOf(")"),o=(0,r.D)(e,"("),a=(0,r.D)(e,")");for(;-1!==i&&o>a;)e+=n.slice(0,i+1),i=(n=n.slice(i+1)).indexOf(")"),a++;return[e,n]}(n+i);if(!l[0])return!1;let s={type:"link",title:null,url:a+t+l[0],children:[{type:"text",value:t+l[0]}]};return l[1]?[s,{type:"text",value:l[1]}]:s}function y(e,t,n,r){return!(!k(r,!0)||/[-\d_]$/.test(n))&&{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function k(e,t){let n=e.input.charCodeAt(e.index-1);return(0===e.index||(0,o.Ny)(n)||(0,o.es)(n))&&(!t||47!==n)}var x=n(33386);function v(){this.buffer()}function w(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function S(){this.buffer()}function _(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function C(e){let t=this.resume(),n=this.stack[this.stack.length-1];(0,i.ok)("footnoteReference"===n.type),n.identifier=(0,x.B)(this.sliceSerialize(e)).toLowerCase(),n.label=t}function P(e){this.exit(e)}function A(e){let t=this.resume(),n=this.stack[this.stack.length-1];(0,i.ok)("footnoteDefinition"===n.type),n.identifier=(0,x.B)(this.sliceSerialize(e)).toLowerCase(),n.label=t}function T(e){this.exit(e)}function N(e,t,n,r){let i=n.createTracker(r),o=i.move("[^"),a=n.enter("footnoteReference"),l=n.enter("reference");return o+=i.move(n.safe(n.associationId(e),{after:"]",before:o})),l(),a(),o+=i.move("]")}function E(e,t,n){return 0===t?e:L(e,t,n)}function L(e,t,n){return(n?"":"    ")+e}N.peek=function(){return"["};let I=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];function R(e){this.enter({type:"delete",children:[]},e)}function O(e){this.exit(e)}function D(e,t,n,r){let i=n.createTracker(r),o=n.enter("strikethrough"),a=i.move("~~");return a+=n.containerPhrasing(e,{...i.current(),before:a,after:"~"}),a+=i.move("~~"),o(),a}function M(e){return e.length}function z(e){let t="string"==typeof e?e.codePointAt(0):0;return 67===t||99===t?99:76===t||108===t?108:114*(82===t||114===t)}D.peek=function(){return"~"};function B(e,t,n){if("string"==typeof t&&(t=[t]),!t||0===t.length)return n;let r=-1;for(;++r<t.length;)if(e.includes(t[r]))return!0;return!1}function F(e){let t=e.options.quote||'"';if('"'!==t&&"'"!==t)throw Error("Cannot serialize title with `"+t+"` for `options.quote`, expected `\"`, or `'`");return t}function j(e){return"&#x"+e.toString(16).toUpperCase()+";"}var $=n(49535);function H(e,t,n){let r=(0,$.S)(e),i=(0,$.S)(t);return void 0===r?void 0===i?"_"===n?{inside:!0,outside:!0}:{inside:!1,outside:!1}:1===i?{inside:!0,outside:!0}:{inside:!1,outside:!0}:1===r?void 0===i?{inside:!1,outside:!1}:1===i?{inside:!0,outside:!0}:{inside:!1,outside:!1}:void 0===i?{inside:!1,outside:!1}:1===i?{inside:!0,outside:!1}:{inside:!1,outside:!1}}n(88428);var G=n(4392);function U(e,t,n){let r=e.value||"",i="`",o=-1;for(;RegExp("(^|[^`])"+i+"([^`]|$)").test(r);)i+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++o<n.unsafe.length;){let e,t=n.unsafe[o],i=n.compilePattern(t);if(t.atBreak)for(;e=i.exec(r);){let t=e.index;10===r.charCodeAt(t)&&13===r.charCodeAt(t-1)&&t--,r=r.slice(0,t)+" "+r.slice(e.index+1)}}return i+r+i}function q(e,t){let n=(0,G.d)(e);return!!(!t.options.resourceLink&&e.url&&!e.title&&e.children&&1===e.children.length&&"text"===e.children[0].type&&(n===e.url||"mailto:"+n===e.url)&&/^[a-z][a-z+.-]+:/i.test(e.url)&&!/[\0- <>\u007F]/.test(e.url))}U.peek=function(){return"`"};(0,l.C)(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);let W={inlineCode:U,listItem:function(e,t,n,r){let i=function(e){let t=e.options.listItemIndent||"one";if("tab"!==t&&"one"!==t&&"mixed"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}(n),o=n.bulletCurrent||function(e){let t=e.options.bullet||"*";if("*"!==t&&"+"!==t&&"-"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}(n);t&&"list"===t.type&&t.ordered&&(o=("number"==typeof t.start&&t.start>-1?t.start:1)+(!1===n.options.incrementListMarker?0:t.children.indexOf(e))+o);let a=o.length+1;("tab"===i||"mixed"===i&&(t&&"list"===t.type&&t.spread||e.spread))&&(a=4*Math.ceil(a/4));let l=n.createTracker(r);l.move(o+" ".repeat(a-o.length)),l.shift(a);let s=n.enter("listItem"),c=n.indentLines(n.containerFlow(e,l.current()),function(e,t,n){return t?(n?"":" ".repeat(a))+e:(n?o:o+" ".repeat(a-o.length))+e});return s(),c}};function V(e){let t=e._align;(0,i.ok)(t,"expected `_align` on table"),this.enter({type:"table",align:t.map(function(e){return"none"===e?null:e}),children:[]},e),this.data.inTable=!0}function Q(e){this.exit(e),this.data.inTable=void 0}function J(e){this.enter({type:"tableRow",children:[]},e)}function Y(e){this.exit(e)}function Z(e){this.enter({type:"tableCell",children:[]},e)}function K(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,X));let n=this.stack[this.stack.length-1];(0,i.ok)("inlineCode"===n.type),n.value=t,this.exit(e)}function X(e,t){return"|"===t?t:e}function ee(e){let t=this.stack[this.stack.length-2];(0,i.ok)("listItem"===t.type),t.checked="taskListCheckValueChecked"===e.type}function et(e){let t=this.stack[this.stack.length-2];if(t&&"listItem"===t.type&&"boolean"==typeof t.checked){let e=this.stack[this.stack.length-1];(0,i.ok)("paragraph"===e.type);let n=e.children[0];if(n&&"text"===n.type){let r,i=t.children,o=-1;for(;++o<i.length;){let e=i[o];if("paragraph"===e.type){r=e;break}}r===e&&(n.value=n.value.slice(1),0===n.value.length?e.children.shift():e.position&&n.position&&"number"==typeof n.position.start.offset&&(n.position.start.column++,n.position.start.offset++,e.position.start=Object.assign({},n.position.start)))}}this.exit(e)}function en(e,t,n,r){let i=e.children[0],o="boolean"==typeof e.checked&&i&&"paragraph"===i.type,a="["+(e.checked?"x":" ")+"] ",l=n.createTracker(r);o&&l.move(a);let s=W.listItem(e,t,n,{...r,...l.current()});return o&&(s=s.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,function(e){return e+a})),s}var er=n(69381);let ei={tokenize:function(e,t,n){let r=0;return function t(o){return(87===o||119===o)&&r<3?(r++,e.consume(o),t):46===o&&3===r?(e.consume(o),i):n(o)};function i(e){return null===e?n(e):t(e)}},partial:!0},eo={tokenize:function(e,t,n){let r,i,a;return l;function l(t){return 46===t||95===t?e.check(el,c,s)(t):null===t||(0,o.Ee)(t)||(0,o.Ny)(t)||45!==t&&(0,o.es)(t)?c(t):(a=!0,e.consume(t),l)}function s(t){return 95===t?r=!0:(i=r,r=void 0),e.consume(t),l}function c(e){return i||r||!a?n(e):t(e)}},partial:!0},ea={tokenize:function(e,t){let n=0,r=0;return i;function i(l){return 40===l?(n++,e.consume(l),i):41===l&&r<n?a(l):33===l||34===l||38===l||39===l||41===l||42===l||44===l||46===l||58===l||59===l||60===l||63===l||93===l||95===l||126===l?e.check(el,t,a)(l):null===l||(0,o.Ee)(l)||(0,o.Ny)(l)?t(l):(e.consume(l),i)}function a(t){return 41===t&&r++,e.consume(t),i}},partial:!0},el={tokenize:function(e,t,n){return r;function r(l){return 33===l||34===l||39===l||41===l||42===l||44===l||46===l||58===l||59===l||63===l||95===l||126===l?(e.consume(l),r):38===l?(e.consume(l),a):93===l?(e.consume(l),i):60===l||null===l||(0,o.Ee)(l)||(0,o.Ny)(l)?t(l):n(l)}function i(e){return null===e||40===e||91===e||(0,o.Ee)(e)||(0,o.Ny)(e)?t(e):r(e)}function a(t){return(0,o.CW)(t)?function t(i){return 59===i?(e.consume(i),r):(0,o.CW)(i)?(e.consume(i),t):n(i)}(t):n(t)}},partial:!0},es={tokenize:function(e,t,n){return function(t){return e.consume(t),r};function r(e){return(0,o.lV)(e)?n(e):t(e)}},partial:!0},ec={name:"wwwAutolink",tokenize:function(e,t,n){let r=this;return function(t){return 87!==t&&119!==t||!ef.call(r,r.previous)||ey(r.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(ei,e.attempt(eo,e.attempt(ea,i),n),n)(t))};function i(n){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(n)}},previous:ef},eu={name:"protocolAutolink",tokenize:function(e,t,n){let r=this,i="",a=!1;return function(t){return(72===t||104===t)&&em.call(r,r.previous)&&!ey(r.events)?(e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),i+=String.fromCodePoint(t),e.consume(t),l):n(t)};function l(t){if((0,o.CW)(t)&&i.length<5)return i+=String.fromCodePoint(t),e.consume(t),l;if(58===t){let n=i.toLowerCase();if("http"===n||"https"===n)return e.consume(t),s}return n(t)}function s(t){return 47===t?(e.consume(t),a)?c:(a=!0,s):n(t)}function c(t){return null===t||(0,o.JQ)(t)||(0,o.Ee)(t)||(0,o.Ny)(t)||(0,o.es)(t)?n(t):e.attempt(eo,e.attempt(ea,u),n)(t)}function u(n){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(n)}},previous:em},eh={name:"emailAutolink",tokenize:function(e,t,n){let r,i,a=this;return function(t){return!eb(t)||!eg.call(a,a.previous)||ey(a.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),function t(r){return eb(r)?(e.consume(r),t):64===r?(e.consume(r),l):n(r)}(t))};function l(t){return 46===t?e.check(es,c,s)(t):45===t||95===t||(0,o.lV)(t)?(i=!0,e.consume(t),l):c(t)}function s(t){return e.consume(t),r=!0,l}function c(l){return i&&r&&(0,o.CW)(a.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(l)):n(l)}},previous:eg},ep={},ed=48;for(;ed<123;)ep[ed]=eh,58==++ed?ed=65:91===ed&&(ed=97);function ef(e){return null===e||40===e||42===e||95===e||91===e||93===e||126===e||(0,o.Ee)(e)}function em(e){return!(0,o.CW)(e)}function eg(e){return!(47===e||eb(e))}function eb(e){return 43===e||45===e||46===e||95===e||(0,o.lV)(e)}function ey(e){let t=e.length,n=!1;for(;t--;){let r=e[t][1];if(("labelLink"===r.type||"labelImage"===r.type)&&!r._balanced){n=!0;break}if(r._gfmAutolinkLiteralWalkedInto){n=!1;break}}return e.length>0&&!n&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}ep[43]=eh,ep[45]=eh,ep[46]=eh,ep[95]=eh,ep[72]=[eh,eu],ep[104]=[eh,eu],ep[87]=[eh,ec],ep[119]=[eh,ec];var ek=n(95333),ex=n(94581);let ev={tokenize:function(e,t,n){let r=this;return(0,ex.N)(e,function(e){let i=r.events[r.events.length-1];return i&&"gfmFootnoteDefinitionIndent"===i[1].type&&4===i[2].sliceSerialize(i[1],!0).length?t(e):n(e)},"gfmFootnoteDefinitionIndent",5)},partial:!0};function ew(e,t,n){let r,i=this,o=i.events.length,a=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]);for(;o--;){let e=i.events[o][1];if("labelImage"===e.type){r=e;break}if("gfmFootnoteCall"===e.type||"labelLink"===e.type||"label"===e.type||"image"===e.type||"link"===e.type)break}return function(o){if(!r||!r._balanced)return n(o);let l=(0,x.B)(i.sliceSerialize({start:r.end,end:i.now()}));return 94===l.codePointAt(0)&&a.includes(l.slice(1))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(o),e.exit("gfmFootnoteCallLabelMarker"),t(o)):n(o)}}function eS(e,t){let n=e.length;for(;n--;)if("labelImage"===e[n][1].type&&"enter"===e[n][0]){e[n][1];break}e[n+1][1].type="data",e[n+3][1].type="gfmFootnoteCallLabelMarker";let r={type:"gfmFootnoteCall",start:Object.assign({},e[n+3][1].start),end:Object.assign({},e[e.length-1][1].end)},i={type:"gfmFootnoteCallMarker",start:Object.assign({},e[n+3][1].end),end:Object.assign({},e[n+3][1].end)};i.end.column++,i.end.offset++,i.end._bufferIndex++;let o={type:"gfmFootnoteCallString",start:Object.assign({},i.end),end:Object.assign({},e[e.length-1][1].start)},a={type:"chunkString",contentType:"string",start:Object.assign({},o.start),end:Object.assign({},o.end)},l=[e[n+1],e[n+2],["enter",r,t],e[n+3],e[n+4],["enter",i,t],["exit",i,t],["enter",o,t],["enter",a,t],["exit",a,t],["exit",o,t],e[e.length-2],e[e.length-1],["exit",r,t]];return e.splice(n,e.length-n+1,...l),e}function e_(e,t,n){let r,i=this,a=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]),l=0;return function(t){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(t),e.exit("gfmFootnoteCallLabelMarker"),s};function s(t){return 94!==t?n(t):(e.enter("gfmFootnoteCallMarker"),e.consume(t),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",c)}function c(s){if(l>999||93===s&&!r||null===s||91===s||(0,o.Ee)(s))return n(s);if(93===s){e.exit("chunkString");let r=e.exit("gfmFootnoteCallString");return a.includes((0,x.B)(i.sliceSerialize(r)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(s),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):n(s)}return(0,o.Ee)(s)||(r=!0),l++,e.consume(s),92===s?u:c}function u(t){return 91===t||92===t||93===t?(e.consume(t),l++,c):c(t)}}function eC(e,t,n){let r,i,a=this,l=a.parser.gfmFootnotes||(a.parser.gfmFootnotes=[]),s=0;return function(t){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),c};function c(t){return 94===t?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",u):n(t)}function u(t){if(s>999||93===t&&!i||null===t||91===t||(0,o.Ee)(t))return n(t);if(93===t){e.exit("chunkString");let n=e.exit("gfmFootnoteDefinitionLabelString");return r=(0,x.B)(a.sliceSerialize(n)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),p}return(0,o.Ee)(t)||(i=!0),s++,e.consume(t),92===t?h:u}function h(t){return 91===t||92===t||93===t?(e.consume(t),s++,u):u(t)}function p(t){return 58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),l.includes(r)||l.push(r),(0,ex.N)(e,d,"gfmFootnoteDefinitionWhitespace")):n(t)}function d(e){return t(e)}}function eP(e,t,n){return e.check(ek.B,t,e.attempt(ev,t,n))}function eA(e){e.exit("gfmFootnoteDefinition")}var eT=n(11603),eN=n(91877);class eE{constructor(){this.map=[]}add(e,t,n){!function(e,t,n,r){let i=0;if(0!==n||0!==r.length){for(;i<e.map.length;){if(e.map[i][0]===t){e.map[i][1]+=n,e.map[i][2].push(...r);return}i+=1}e.map.push([t,n,r])}}(this,e,t,n)}consume(e){if(this.map.sort(function(e,t){return e[0]-t[0]}),0===this.map.length)return;let t=this.map.length,n=[];for(;t>0;)t-=1,n.push(e.slice(this.map[t][0]+this.map[t][1]),this.map[t][2]),e.length=this.map[t][0];n.push(e.slice()),e.length=0;let r=n.pop();for(;r;){for(let t of r)e.push(t);r=n.pop()}this.map.length=0}}function eL(e,t,n){let r,i=this,a=0,l=0;return function(e){let t=i.events.length-1;for(;t>-1;){let e=i.events[t][1].type;if("lineEnding"===e||"linePrefix"===e)t--;else break}let r=t>-1?i.events[t][1].type:null,o="tableHead"===r||"tableRow"===r?k:s;return o===k&&i.parser.lazy[i.now().line]?n(e):o(e)};function s(t){var n;return e.enter("tableHead"),e.enter("tableRow"),124===(n=t)||(r=!0,l+=1),c(n)}function c(t){return null===t?n(t):(0,o.HP)(t)?l>1?(l=0,i.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),p):n(t):(0,o.On)(t)?(0,ex.N)(e,c,"whitespace")(t):(l+=1,r&&(r=!1,a+=1),124===t)?(e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),r=!0,c):(e.enter("data"),u(t))}function u(t){return null===t||124===t||(0,o.Ee)(t)?(e.exit("data"),c(t)):(e.consume(t),92===t?h:u)}function h(t){return 92===t||124===t?(e.consume(t),u):u(t)}function p(t){return(i.interrupt=!1,i.parser.lazy[i.now().line])?n(t):(e.enter("tableDelimiterRow"),r=!1,(0,o.On)(t))?(0,ex.N)(e,d,"linePrefix",i.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):d(t)}function d(t){return 45===t||58===t?m(t):124===t?(r=!0,e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),f):n(t)}function f(t){return(0,o.On)(t)?(0,ex.N)(e,m,"whitespace")(t):m(t)}function m(t){return 58===t?(l+=1,r=!0,e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),g):45===t?(l+=1,g(t)):null===t||(0,o.HP)(t)?y(t):n(t)}function g(t){return 45===t?(e.enter("tableDelimiterFiller"),function t(n){return 45===n?(e.consume(n),t):58===n?(r=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(n),e.exit("tableDelimiterMarker"),b):(e.exit("tableDelimiterFiller"),b(n))}(t)):n(t)}function b(t){return(0,o.On)(t)?(0,ex.N)(e,y,"whitespace")(t):y(t)}function y(i){if(124===i)return d(i);if(null===i||(0,o.HP)(i))return r&&a===l?(e.exit("tableDelimiterRow"),e.exit("tableHead"),t(i)):n(i);return n(i)}function k(t){return e.enter("tableRow"),x(t)}function x(n){return 124===n?(e.enter("tableCellDivider"),e.consume(n),e.exit("tableCellDivider"),x):null===n||(0,o.HP)(n)?(e.exit("tableRow"),t(n)):(0,o.On)(n)?(0,ex.N)(e,x,"whitespace")(n):(e.enter("data"),v(n))}function v(t){return null===t||124===t||(0,o.Ee)(t)?(e.exit("data"),x(t)):(e.consume(t),92===t?w:v)}function w(t){return 92===t||124===t?(e.consume(t),v):v(t)}}function eI(e,t){let n,r,i,o=-1,a=!0,l=0,s=[0,0,0,0],c=[0,0,0,0],u=!1,h=0,p=new eE;for(;++o<e.length;){let d=e[o],f=d[1];"enter"===d[0]?"tableHead"===f.type?(u=!1,0!==h&&(eO(p,t,h,n,r),r=void 0,h=0),n={type:"table",start:Object.assign({},f.start),end:Object.assign({},f.end)},p.add(o,0,[["enter",n,t]])):"tableRow"===f.type||"tableDelimiterRow"===f.type?(a=!0,i=void 0,s=[0,0,0,0],c=[0,o+1,0,0],u&&(u=!1,r={type:"tableBody",start:Object.assign({},f.start),end:Object.assign({},f.end)},p.add(o,0,[["enter",r,t]])),l="tableDelimiterRow"===f.type?2:r?3:1):l&&("data"===f.type||"tableDelimiterMarker"===f.type||"tableDelimiterFiller"===f.type)?(a=!1,0===c[2]&&(0!==s[1]&&(c[0]=c[1],i=eR(p,t,s,l,void 0,i),s=[0,0,0,0]),c[2]=o)):"tableCellDivider"===f.type&&(a?a=!1:(0!==s[1]&&(c[0]=c[1],i=eR(p,t,s,l,void 0,i)),c=[(s=c)[1],o,0,0])):"tableHead"===f.type?(u=!0,h=o):"tableRow"===f.type||"tableDelimiterRow"===f.type?(h=o,0!==s[1]?(c[0]=c[1],i=eR(p,t,s,l,o,i)):0!==c[1]&&(i=eR(p,t,c,l,o,i)),l=0):l&&("data"===f.type||"tableDelimiterMarker"===f.type||"tableDelimiterFiller"===f.type)&&(c[3]=o)}for(0!==h&&eO(p,t,h,n,r),p.consume(t.events),o=-1;++o<t.events.length;){let e=t.events[o];"enter"===e[0]&&"table"===e[1].type&&(e[1]._align=function(e,t){let n=!1,r=[];for(;t<e.length;){let i=e[t];if(n){if("enter"===i[0])"tableContent"===i[1].type&&r.push("tableDelimiterMarker"===e[t+1][1].type?"left":"none");else if("tableContent"===i[1].type){if("tableDelimiterMarker"===e[t-1][1].type){let e=r.length-1;r[e]="left"===r[e]?"center":"right"}}else if("tableDelimiterRow"===i[1].type)break}else"enter"===i[0]&&"tableDelimiterRow"===i[1].type&&(n=!0);t+=1}return r}(t.events,o))}return e}function eR(e,t,n,r,i,o){0!==n[0]&&(o.end=Object.assign({},eD(t.events,n[0])),e.add(n[0],0,[["exit",o,t]]));let a=eD(t.events,n[1]);if(o={type:1===r?"tableHeader":2===r?"tableDelimiter":"tableData",start:Object.assign({},a),end:Object.assign({},a)},e.add(n[1],0,[["enter",o,t]]),0!==n[2]){let i=eD(t.events,n[2]),o=eD(t.events,n[3]),a={type:"tableContent",start:Object.assign({},i),end:Object.assign({},o)};if(e.add(n[2],0,[["enter",a,t]]),2!==r){let r=t.events[n[2]],i=t.events[n[3]];if(r[1].end=Object.assign({},i[1].end),r[1].type="chunkText",r[1].contentType="text",n[3]>n[2]+1){let t=n[2]+1,r=n[3]-n[2]-1;e.add(t,r,[])}}e.add(n[3]+1,0,[["exit",a,t]])}return void 0!==i&&(o.end=Object.assign({},eD(t.events,i)),e.add(i,0,[["exit",o,t]]),o=void 0),o}function eO(e,t,n,r,i){let o=[],a=eD(t.events,n);i&&(i.end=Object.assign({},a),o.push(["exit",i,t])),r.end=Object.assign({},a),o.push(["exit",r,t]),e.add(n+1,0,o)}function eD(e,t){let n=e[t],r="enter"===n[0]?"start":"end";return n[1][r]}let eM={name:"tasklistCheck",tokenize:function(e,t,n){let r=this;return function(t){return null===r.previous&&r._gfmTasklistFirstContentOfListItem?(e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),i):n(t)};function i(t){return(0,o.Ee)(t)?(e.enter("taskListCheckValueUnchecked"),e.consume(t),e.exit("taskListCheckValueUnchecked"),a):88===t||120===t?(e.enter("taskListCheckValueChecked"),e.consume(t),e.exit("taskListCheckValueChecked"),a):n(t)}function a(t){return 93===t?(e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),l):n(t)}function l(r){return(0,o.HP)(r)?t(r):(0,o.On)(r)?e.check({tokenize:ez},t,n)(r):n(r)}}};function ez(e,t,n){return(0,ex.N)(e,function(e){return null===e?n(e):t(e)},"whitespace")}let eB={};function eF(e){let t,n=e||eB,r=this.data(),i=r.micromarkExtensions||(r.micromarkExtensions=[]),o=r.fromMarkdownExtensions||(r.fromMarkdownExtensions=[]),a=r.toMarkdownExtensions||(r.toMarkdownExtensions=[]);i.push((0,er.y)([{text:ep},{document:{91:{name:"gfmFootnoteDefinition",tokenize:eC,continuation:{tokenize:eP},exit:eA}},text:{91:{name:"gfmFootnoteCall",tokenize:e_},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:ew,resolveTo:eS}}},function(e){let t=(e||{}).singleTilde,n={name:"strikethrough",tokenize:function(e,n,r){let i=this.previous,o=this.events,a=0;return function(l){return 126===i&&"characterEscape"!==o[o.length-1][1].type?r(l):(e.enter("strikethroughSequenceTemporary"),function o(l){let s=(0,$.S)(i);if(126===l)return a>1?r(l):(e.consume(l),a++,o);if(a<2&&!t)return r(l);let c=e.exit("strikethroughSequenceTemporary"),u=(0,$.S)(l);return c._open=!u||2===u&&!!s,c._close=!s||2===s&&!!u,n(l)}(l))}},resolveAll:function(e,t){let n=-1;for(;++n<e.length;)if("enter"===e[n][0]&&"strikethroughSequenceTemporary"===e[n][1].type&&e[n][1]._close){let r=n;for(;r--;)if("exit"===e[r][0]&&"strikethroughSequenceTemporary"===e[r][1].type&&e[r][1]._open&&e[n][1].end.offset-e[n][1].start.offset==e[r][1].end.offset-e[r][1].start.offset){e[n][1].type="strikethroughSequence",e[r][1].type="strikethroughSequence";let i={type:"strikethrough",start:Object.assign({},e[r][1].start),end:Object.assign({},e[n][1].end)},o={type:"strikethroughText",start:Object.assign({},e[r][1].end),end:Object.assign({},e[n][1].start)},a=[["enter",i,t],["enter",e[r][1],t],["exit",e[r][1],t],["enter",o,t]],l=t.parser.constructs.insideSpan.null;l&&(0,eT.m)(a,a.length,0,(0,eN.W)(l,e.slice(r+1,n),t)),(0,eT.m)(a,a.length,0,[["exit",o,t],["enter",e[n][1],t],["exit",e[n][1],t],["exit",i,t]]),(0,eT.m)(e,r-1,n-r+3,a),n=r+a.length-2;break}}for(n=-1;++n<e.length;)"strikethroughSequenceTemporary"===e[n][1].type&&(e[n][1].type="data");return e}};return null==t&&(t=!0),{text:{126:n},insideSpan:{null:[n]},attentionMarkers:{null:[126]}}}(n),{flow:{null:{name:"table",tokenize:eL,resolveAll:eI}}},{text:{91:eM}}])),o.push([{transforms:[g],enter:{literalAutolink:u,literalAutolinkEmail:h,literalAutolinkHttp:h,literalAutolinkWww:h},exit:{literalAutolink:m,literalAutolinkEmail:f,literalAutolinkHttp:p,literalAutolinkWww:d}},{enter:{gfmFootnoteCallString:v,gfmFootnoteCall:w,gfmFootnoteDefinitionLabelString:S,gfmFootnoteDefinition:_},exit:{gfmFootnoteCallString:C,gfmFootnoteCall:P,gfmFootnoteDefinitionLabelString:A,gfmFootnoteDefinition:T}},{canContainEols:["delete"],enter:{strikethrough:R},exit:{strikethrough:O}},{enter:{table:V,tableData:Z,tableHeader:Z,tableRow:J},exit:{codeText:K,table:Q,tableData:Y,tableHeader:Y,tableRow:Y}},{exit:{taskListCheckValueChecked:ee,taskListCheckValueUnchecked:ee,paragraph:et}}]),a.push({extensions:[{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:s,notInConstruct:c},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:s,notInConstruct:c},{character:":",before:"[ps]",after:"\\/",inConstruct:s,notInConstruct:c}]},(t=!1,n&&n.firstLineBlank&&(t=!0),{handlers:{footnoteDefinition:function(e,n,r,i){let o=r.createTracker(i),a=o.move("[^"),l=r.enter("footnoteDefinition"),s=r.enter("label");return a+=o.move(r.safe(r.associationId(e),{before:a,after:"]"})),s(),a+=o.move("]:"),e.children&&e.children.length>0&&(o.shift(4),a+=o.move((t?"\n":" ")+r.indentLines(r.containerFlow(e,o.current()),t?L:E))),l(),a},footnoteReference:N},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]}),{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:I}],handlers:{delete:D}},function(e){let t=e||{},n=t.tableCellPadding,r=t.tablePipeAlign,i=t.stringLength,o=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:"\n",inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:function(e,t,n){let r=W.inlineCode(e,t,n);return n.stack.includes("tableCell")&&(r=r.replace(/\|/g,"\\$&")),r},table:function(e,t,n,r){return l(function(e,t,n){let r=e.children,i=-1,o=[],a=t.enter("table");for(;++i<r.length;)o[i]=s(r[i],t,n);return a(),o}(e,n,r),e.align)},tableCell:a,tableRow:function(e,t,n,r){let i=l([s(e,n,r)]);return i.slice(0,i.indexOf("\n"))}}};function a(e,t,n,r){let i=n.enter("tableCell"),a=n.enter("phrasing"),l=n.containerPhrasing(e,{...r,before:o,after:o});return a(),i(),l}function l(e,t){return function(e,t){let n=t||{},r=(n.align||[]).concat(),i=n.stringLength||M,o=[],a=[],l=[],s=[],c=0,u=-1;for(;++u<e.length;){let t=[],r=[],o=-1;for(e[u].length>c&&(c=e[u].length);++o<e[u].length;){var h;let a=null==(h=e[u][o])?"":String(h);if(!1!==n.alignDelimiters){let e=i(a);r[o]=e,(void 0===s[o]||e>s[o])&&(s[o]=e)}t.push(a)}a[u]=t,l[u]=r}let p=-1;if("object"==typeof r&&"length"in r)for(;++p<c;)o[p]=z(r[p]);else{let e=z(r);for(;++p<c;)o[p]=e}p=-1;let d=[],f=[];for(;++p<c;){let e=o[p],t="",r="";99===e?(t=":",r=":"):108===e?t=":":114===e&&(r=":");let i=!1===n.alignDelimiters?1:Math.max(1,s[p]-t.length-r.length),a=t+"-".repeat(i)+r;!1!==n.alignDelimiters&&((i=t.length+i+r.length)>s[p]&&(s[p]=i),f[p]=i),d[p]=a}a.splice(1,0,d),l.splice(1,0,f),u=-1;let m=[];for(;++u<a.length;){let e=a[u],t=l[u];p=-1;let r=[];for(;++p<c;){let i=e[p]||"",a="",l="";if(!1!==n.alignDelimiters){let e=s[p]-(t[p]||0),n=o[p];114===n?a=" ".repeat(e):99===n?e%2?(a=" ".repeat(e/2+.5),l=" ".repeat(e/2-.5)):l=a=" ".repeat(e/2):l=" ".repeat(e)}!1===n.delimiterStart||p||r.push("|"),!1!==n.padding&&(!1!==n.alignDelimiters||""!==i)&&(!1!==n.delimiterStart||p)&&r.push(" "),!1!==n.alignDelimiters&&r.push(a),r.push(i),!1!==n.alignDelimiters&&r.push(l),!1!==n.padding&&r.push(" "),(!1!==n.delimiterEnd||p!==c-1)&&r.push("|")}m.push(!1===n.delimiterEnd?r.join("").replace(/ +$/,""):r.join(""))}return m.join("\n")}(e,{align:t,alignDelimiters:r,padding:n,stringLength:i})}function s(e,t,n){let r=e.children,i=-1,o=[],l=t.enter("tableRow");for(;++i<r.length;)o[i]=a(r[i],e,t,n);return l(),o}}(n),{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:en}}]})}},94581:(e,t,n)=>{"use strict";n.d(t,{N:()=>i});var r=n(12556);function i(e,t,n,i){let o=i?i-1:Number.POSITIVE_INFINITY,a=0;return function(i){return(0,r.On)(i)?(e.enter(n),function i(l){return(0,r.On)(l)&&a++<o?(e.consume(l),i):(e.exit(n),t(l))}(i)):t(i)}}},95333:(e,t,n)=>{"use strict";n.d(t,{B:()=>o});var r=n(94581),i=n(12556);let o={partial:!0,tokenize:function(e,t,n){return function(t){return(0,i.On)(t)?(0,r.N)(e,o,"linePrefix")(t):o(t)};function o(e){return null===e||(0,i.HP)(e)?t(e):n(e)}}}}}]);