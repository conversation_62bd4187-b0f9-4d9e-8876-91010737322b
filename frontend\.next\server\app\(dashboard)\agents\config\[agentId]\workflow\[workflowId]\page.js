(()=>{var e={};e.id=8844,e.ids=[8844],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},5178:(e,t,r)=>{"use strict";let n;r.r(t),r.d(t,{default:()=>tM});var o=r(60687),a=r(43210),s=r(16189),l=r(50940),i=r(67821),c=r(84027),d=r(8819),u=r(29523),f=r(89667),p=r(80013),m=r(34729),h=r(40988),v=r(52581),g=r(86577),x=r(43612),w=r(79481),b=r(15945);let y=async e=>{let t,r,n;if(!await (0,b.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let o=(0,w.U)(),{data:{session:a}}=await o.auth.getSession();if(!a)throw Error("You must be logged in to get agent tools");let s=await fetch(`http://localhost:8000/api/agents/${e}`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a.access_token}`}});if(!s.ok)throw Error((await s.json().catch(()=>({detail:"Unknown error"}))).detail||`HTTP ${s.status}: ${s.statusText}`);let l=await s.json();l.current_version?(t=l.current_version.agentpress_tools||{},r=l.current_version.configured_mcps||[],n=l.current_version.custom_mcps||[]):(t=l.agentpress_tools||{},r=l.configured_mcps||[],n=l.custom_mcps||[]);let i=[],c={sb_shell_tool:{description:"Execute shell commands in tmux sessions",icon:"\uD83D\uDCBB",color:"bg-slate-100"},sb_files_tool:{description:"Create, read, update, and delete files",icon:"\uD83D\uDCC1",color:"bg-blue-100"},sb_browser_tool:{description:"Browser automation and web navigation",icon:"\uD83C\uDF10",color:"bg-indigo-100"},sb_deploy_tool:{description:"Deploy applications and services",icon:"\uD83D\uDE80",color:"bg-green-100"},sb_expose_tool:{description:"Expose services and manage ports",icon:"\uD83D\uDD0C",color:"bg-orange-100"},web_search_tool:{description:"Search the web using Tavily API",icon:"\uD83D\uDD0D",color:"bg-yellow-100"},sb_vision_tool:{description:"Vision and image processing capabilities",icon:"\uD83D\uDC41️",color:"bg-pink-100"},data_providers_tool:{description:"Access to data providers and external APIs",icon:"\uD83D\uDD17",color:"bg-cyan-100"}};for(let[e,r]of Object.entries(t)){let t=c[e];t&&r&&"object"==typeof r&&"enabled"in r&&i.push({name:e,description:t.description,type:"agentpress",enabled:!!r.enabled,icon:t.icon,color:t.color})}let d=[];for(let e of r)if(e.enabledTools&&Array.isArray(e.enabledTools))for(let t of e.enabledTools)d.push({name:t,description:`${t} from ${e.name}`,type:"mcp",server:e.name,enabled:!0,icon:"\uD83D\uDD27",color:"bg-purple-100"});for(let e of n)if(e.enabledTools&&Array.isArray(e.enabledTools))for(let t of e.enabledTools){let r=e.customType||e.type||"sse";d.push({name:t,description:`${t} from ${e.name} (${r})`,type:"mcp",server:e.name,enabled:!0,icon:"sse"===r?"\uD83C\uDF10":"http"===r?"\uD83D\uDCE1":"⚙️",color:"bg-indigo-100"})}return{agentpress_tools:i,mcp_tools:d}},E=e=>(0,x.I)({queryKey:["agent-tools",e],queryFn:()=>y(e),staleTime:3e5,enabled:!!e});var j=r(43649),C=r(96474),k=r(17971),N=r(13964),_=r(93661),A=r(88233),S=/[\\\/_+.#"@\[\(\{&]/,P=/[\\\/_+.#"@\[\(\{&]/g,I=/[\s-]/,R=/[\s-]/g;function T(e){return e.toLowerCase().replace(R," ")}function $(){return($=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}function O(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(null==e||e(n),!1===r||!n.defaultPrevented)return null==t?void 0:t(n)}}function D(...e){return t=>e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}function L(...e){return(0,a.useCallback)(D(...e),e)}let M=(null==globalThis?void 0:globalThis.document)?a.useLayoutEffect:()=>{},F=a["useId".toString()]||(()=>void 0),q=0;function U(e){let[t,r]=a.useState(F());return M(()=>{e||r(e=>null!=e?e:String(q++))},[e]),e||(t?`radix-${t}`:"")}function z(e){let t=(0,a.useRef)(e);return(0,a.useEffect)(()=>{t.current=e}),(0,a.useMemo)(()=>(...e)=>{var r;return null==(r=t.current)?void 0:r.call(t,...e)},[])}var K=r(51215),W=r.n(K);let B=(0,a.forwardRef)((e,t)=>{let{children:r,...n}=e,o=a.Children.toArray(r),s=o.find(Y);if(s){let e=s.props.children,r=o.map(t=>t!==s?t:a.Children.count(e)>1?a.Children.only(null):(0,a.isValidElement)(e)?e.props.children:null);return(0,a.createElement)(V,$({},n,{ref:t}),(0,a.isValidElement)(e)?(0,a.cloneElement)(e,void 0,r):null)}return(0,a.createElement)(V,$({},n,{ref:t}),r)});B.displayName="Slot";let V=(0,a.forwardRef)((e,t)=>{let{children:r,...n}=e;return(0,a.isValidElement)(r)?(0,a.cloneElement)(r,{...function(e,t){let r={...t};for(let n in t){let o=e[n],a=t[n];/^on[A-Z]/.test(n)?r[n]=(...e)=>{null==a||a(...e),null==o||o(...e)}:"style"===n?r[n]={...o,...a}:"className"===n&&(r[n]=[o,a].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props),ref:D(t,r.ref)}):a.Children.count(r)>1?a.Children.only(null):null});V.displayName="SlotClone";let G=({children:e})=>(0,a.createElement)(a.Fragment,null,e);function Y(e){return(0,a.isValidElement)(e)&&e.type===G}let H=["a","button","div","h2","h3","img","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=(0,a.forwardRef)((e,r)=>{let{asChild:n,...o}=e,s=n?B:t;return(0,a.useEffect)(()=>{window[Symbol.for("radix-ui")]=!0},[]),(0,a.createElement)(s,$({},o,{ref:r}))});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),X="dismissableLayer.update",Z=(0,a.createContext)({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Q=(0,a.forwardRef)((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:o,onPointerDownOutside:s,onFocusOutside:l,onInteractOutside:i,onDismiss:c,...d}=e,u=(0,a.useContext)(Z),[f,p]=(0,a.useState)(null),[,m]=(0,a.useState)({}),h=L(t,e=>p(e)),v=Array.from(u.layers),[g]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),x=v.indexOf(g),w=f?v.indexOf(f):-1,b=u.layersWithOutsidePointerEventsDisabled.size>0,y=w>=x,E=function(e){let t=z(e),r=(0,a.useRef)(!1),n=(0,a.useRef)(()=>{});return(0,a.useEffect)(()=>{let e=e=>{if(e.target&&!r.current){let r={originalEvent:e};function o(){ee("dismissableLayer.pointerDownOutside",t,r,{discrete:!0})}"touch"===e.pointerType?(document.removeEventListener("click",n.current),n.current=o,document.addEventListener("click",n.current,{once:!0})):o()}r.current=!1},o=window.setTimeout(()=>{document.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(o),document.removeEventListener("pointerdown",e),document.removeEventListener("click",n.current)}},[t]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,r=[...u.branches].some(e=>e.contains(t));y&&!r&&(null==s||s(e),null==i||i(e),e.defaultPrevented||null==c||c())}),j=function(e){let t=z(e),r=(0,a.useRef)(!1);return(0,a.useEffect)(()=>{let e=e=>{e.target&&!r.current&&ee("dismissableLayer.focusOutside",t,{originalEvent:e},{discrete:!1})};return document.addEventListener("focusin",e),()=>document.removeEventListener("focusin",e)},[t]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...u.branches].some(e=>e.contains(t))&&(null==l||l(e),null==i||i(e),e.defaultPrevented||null==c||c())});return!function(e){let t=z(e);(0,a.useEffect)(()=>{let e=e=>{"Escape"===e.key&&t(e)};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[t])}(e=>{w===u.layers.size-1&&(null==o||o(e),!e.defaultPrevented&&c&&(e.preventDefault(),c()))}),(0,a.useEffect)(()=>{if(f)return r&&(0===u.layersWithOutsidePointerEventsDisabled.size&&(n=document.body.style.pointerEvents,document.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(f)),u.layers.add(f),J(),()=>{r&&1===u.layersWithOutsidePointerEventsDisabled.size&&(document.body.style.pointerEvents=n)}},[f,r,u]),(0,a.useEffect)(()=>()=>{f&&(u.layers.delete(f),u.layersWithOutsidePointerEventsDisabled.delete(f),J())},[f,u]),(0,a.useEffect)(()=>{let e=()=>m({});return document.addEventListener(X,e),()=>document.removeEventListener(X,e)},[]),(0,a.createElement)(H.div,$({},d,{ref:h,style:{pointerEvents:b?y?"auto":"none":void 0,...e.style},onFocusCapture:O(e.onFocusCapture,j.onFocusCapture),onBlurCapture:O(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:O(e.onPointerDownCapture,E.onPointerDownCapture)}))});function J(){let e=new CustomEvent(X);document.dispatchEvent(e)}function ee(e,t,r,{discrete:n}){let o=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});if(t&&o.addEventListener(e,t,{once:!0}),n)o&&(0,K.flushSync)(()=>o.dispatchEvent(a));else o.dispatchEvent(a)}let et="focusScope.autoFocusOnMount",er="focusScope.autoFocusOnUnmount",en={bubbles:!1,cancelable:!0},eo=(0,a.forwardRef)((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:o,onUnmountAutoFocus:s,...l}=e,[i,c]=(0,a.useState)(null),d=z(o),u=z(s),f=(0,a.useRef)(null),p=L(t,e=>c(e)),m=(0,a.useRef)({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;(0,a.useEffect)(()=>{if(n){function e(e){if(m.paused||!i)return;let t=e.target;i.contains(t)?f.current=t:el(f.current,{select:!0})}function t(e){!m.paused&&i&&(i.contains(e.relatedTarget)||el(f.current,{select:!0}))}return document.addEventListener("focusin",e),document.addEventListener("focusout",t),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t)}}},[n,i,m.paused]),(0,a.useEffect)(()=>{if(i){ei.add(m);let e=document.activeElement;if(!i.contains(e)){let t=new CustomEvent(et,en);i.addEventListener(et,d),i.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(el(n,{select:t}),document.activeElement!==r)return}(ea(i).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&el(i))}return()=>{i.removeEventListener(et,d),setTimeout(()=>{let t=new CustomEvent(er,en);i.addEventListener(er,u),i.dispatchEvent(t),t.defaultPrevented||el(null!=e?e:document.body,{select:!0}),i.removeEventListener(er,u),ei.remove(m)},0)}}},[i,d,u,m]);let h=(0,a.useCallback)(e=>{if(!r&&!n||m.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[n,a]=function(e){let t=ea(e);return[es(t,e),es(t.reverse(),e)]}(t);n&&a?e.shiftKey||o!==a?e.shiftKey&&o===n&&(e.preventDefault(),r&&el(a,{select:!0})):(e.preventDefault(),r&&el(n,{select:!0})):o===t&&e.preventDefault()}},[r,n,m.paused]);return(0,a.createElement)(H.div,$({tabIndex:-1},l,{ref:p,onKeyDown:h}))});function ea(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function es(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function el(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}let ei=function(){let e=[];return{add(t){let r=e[0];t!==r&&(null==r||r.pause()),(e=ec(e,t)).unshift(t)},remove(t){var r;null==(r=(e=ec(e,t))[0])||r.resume()}}}();function ec(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}let ed=(0,a.forwardRef)((e,t)=>{var r;let{container:n=null==globalThis||null==(r=globalThis.document)?void 0:r.body,...o}=e;return n?W().createPortal((0,a.createElement)(H.div,$({},o,{ref:t})),n):null}),eu=e=>{let{present:t,children:r}=e,n=function(e){var t,r;let[n,o]=(0,a.useState)(),s=(0,a.useRef)({}),l=(0,a.useRef)(e),i=(0,a.useRef)("none"),[c,d]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},(0,a.useReducer)((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return(0,a.useEffect)(()=>{let e=ef(s.current);i.current="mounted"===c?e:"none"},[c]),M(()=>{let t=s.current,r=l.current;if(r!==e){let n=i.current,o=ef(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):r&&n!==o?d("ANIMATION_OUT"):d("UNMOUNT"),l.current=e}},[e,d]),M(()=>{if(n){let e=e=>{let t=ef(s.current).includes(e.animationName);e.target===n&&t&&(0,K.flushSync)(()=>d("ANIMATION_END"))},t=e=>{e.target===n&&(i.current=ef(s.current))};return n.addEventListener("animationstart",t),n.addEventListener("animationcancel",e),n.addEventListener("animationend",e),()=>{n.removeEventListener("animationstart",t),n.removeEventListener("animationcancel",e),n.removeEventListener("animationend",e)}}d("ANIMATION_END")},[n,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:(0,a.useCallback)(e=>{e&&(s.current=getComputedStyle(e)),o(e)},[])}}(t),o="function"==typeof r?r({present:n.isPresent}):a.Children.only(r),s=L(n.ref,o.ref);return"function"==typeof r||n.isPresent?(0,a.cloneElement)(o,{ref:s}):null};function ef(e){return(null==e?void 0:e.animationName)||"none"}eu.displayName="Presence";let ep=0;function em(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}var eh=r(4363),ev=r(51550),eg=r(79131),ex=(0,r(14970).f)(),ew=function(){},eb=a.forwardRef(function(e,t){var r=a.useRef(null),n=a.useState({onScrollCapture:ew,onWheelCapture:ew,onTouchMoveCapture:ew}),o=n[0],s=n[1],l=e.forwardProps,i=e.children,c=e.className,d=e.removeScrollBar,u=e.enabled,f=e.shards,p=e.sideCar,m=e.noIsolation,h=e.inert,v=e.allowPinchZoom,g=e.as,x=(0,eh.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as"]),w=(0,eg.S)([r,t]),b=(0,eh.Cl)((0,eh.Cl)({},x),o);return a.createElement(a.Fragment,null,u&&a.createElement(p,{sideCar:ex,removeScrollBar:d,shards:f,noIsolation:m,inert:h,setCallbacks:s,allowPinchZoom:!!v,lockRef:r}),l?a.cloneElement(a.Children.only(i),(0,eh.Cl)((0,eh.Cl)({},b),{ref:w})):a.createElement(void 0===g?"div":g,(0,eh.Cl)({},b,{className:c,ref:w}),i))});eb.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},eb.classNames={fullWidth:ev.pN,zeroRight:ev.Mi};var ey=r(78274),eE=r(49613),ej=r(63289),eC=!1;if("undefined"!=typeof window)try{var ek=Object.defineProperty({},"passive",{get:function(){return eC=!0,!0}});window.addEventListener("test",ek,ek),window.removeEventListener("test",ek,ek)}catch(e){eC=!1}var eN=!!eC&&{passive:!1},e_=function(e){var t=window.getComputedStyle(e);return"hidden"!==t.overflowY&&(t.overflowY!==t.overflowX||"visible"!==t.overflowY)},eA=function(e){var t=window.getComputedStyle(e);return"hidden"!==t.overflowX&&(t.overflowY!==t.overflowX||"visible"!==t.overflowX)},eS=function(e,t){var r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),eP(e,r)){var n=eI(e,r);if(n[1]>n[2])return!0}r=r.parentNode}while(r&&r!==document.body);return!1},eP=function(e,t){return"v"===e?e_(t):eA(t)},eI=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},eR=function(e,t,r,n,o){var a,s=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),l=s*n,i=r.target,c=t.contains(i),d=!1,u=l>0,f=0,p=0;do{var m=eI(e,i),h=m[0],v=m[1]-m[2]-s*h;(h||v)&&eP(e,i)&&(f+=v,p+=h),i=i.parentNode}while(!c&&i!==document.body||c&&(t.contains(i)||t===i));return u&&(o&&0===f||!o&&l>f)?d=!0:!u&&(o&&0===p||!o&&-l>p)&&(d=!0),d},eT=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},e$=function(e){return[e.deltaX,e.deltaY]},eO=function(e){return e&&"current"in e?e.current:e},eD=0,eL=[];let eM=(0,ey.m)(ex,function(e){var t=a.useRef([]),r=a.useRef([0,0]),n=a.useRef(),o=a.useState(eD++)[0],s=a.useState(function(){return(0,ej.T0)()})[0],l=a.useRef(e);a.useEffect(function(){l.current=e},[e]),a.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(0,eh.fX)([e.lockRef.current],(e.shards||[]).map(eO),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var i=a.useCallback(function(e,t){if("touches"in e&&2===e.touches.length)return!l.current.allowPinchZoom;var o,a=eT(e),s=r.current,i="deltaX"in e?e.deltaX:s[0]-a[0],c="deltaY"in e?e.deltaY:s[1]-a[1],d=e.target,u=Math.abs(i)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===u&&"range"===d.type)return!1;var f=eS(u,d);if(!f)return!0;if(f?o=u:(o="v"===u?"h":"v",f=eS(u,d)),!f)return!1;if(!n.current&&"changedTouches"in e&&(i||c)&&(n.current=o),!o)return!0;var p=n.current||o;return eR(p,t,e,"h"===p?i:c,!0)},[]),c=a.useCallback(function(e){if(eL.length&&eL[eL.length-1]===s){var r="deltaY"in e?e$(e):eT(e),n=t.current.filter(function(t){var n;return t.name===e.type&&t.target===e.target&&(n=t.delta,n[0]===r[0]&&n[1]===r[1])})[0];if(n&&n.should)return void e.preventDefault();if(!n){var o=(l.current.shards||[]).map(eO).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?i(e,o[0]):!l.current.noIsolation)&&e.preventDefault()}}},[]),d=a.useCallback(function(e,r,n,o){var a={name:e,delta:r,target:n,should:o};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),u=a.useCallback(function(e){r.current=eT(e),n.current=void 0},[]),f=a.useCallback(function(t){d(t.type,e$(t),t.target,i(t,e.lockRef.current))},[]),p=a.useCallback(function(t){d(t.type,eT(t),t.target,i(t,e.lockRef.current))},[]);a.useEffect(function(){return eL.push(s),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,eN),document.addEventListener("touchmove",c,eN),document.addEventListener("touchstart",u,eN),function(){eL=eL.filter(function(e){return e!==s}),document.removeEventListener("wheel",c,eN),document.removeEventListener("touchmove",c,eN),document.removeEventListener("touchstart",u,eN)}},[]);var m=e.removeScrollBar,h=e.inert;return a.createElement(a.Fragment,null,h?a.createElement(s,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?a.createElement(eE.jp,{gapMode:"margin"}):null)});var eF=a.forwardRef(function(e,t){return a.createElement(eb,(0,eh.Cl)({},e,{ref:t,sideCar:eM}))});eF.classNames=eb.classNames;var eq=r(63376);let eU="Dialog",[ez,eK]=function(e,t=[]){let r=[],n=()=>{let t=r.map(e=>(0,a.createContext)(e));return function(r){let n=(null==r?void 0:r[e])||t;return(0,a.useMemo)(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return n.scopeName=e,[function(t,n){let o=(0,a.createContext)(n),s=r.length;function l(t){let{scope:r,children:n,...l}=t,i=(null==r?void 0:r[e][s])||o,c=(0,a.useMemo)(()=>l,Object.values(l));return(0,a.createElement)(i.Provider,{value:c},n)}return r=[...r,n],l.displayName=t+"Provider",[l,function(r,l){let i=(null==l?void 0:l[e][s])||o,c=(0,a.useContext)(i);if(c)return c;if(void 0!==n)return n;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return(0,a.useMemo)(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(n,...t)]}(eU),[eW,eB]=ez(eU),eV="DialogPortal",[eG,eY]=ez(eV,{forceMount:void 0}),eH="DialogOverlay",eX=(0,a.forwardRef)((e,t)=>{let r=eY(eH,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,s=eB(eH,e.__scopeDialog);return s.modal?(0,a.createElement)(eu,{present:n||s.open},(0,a.createElement)(eZ,$({},o,{ref:t}))):null}),eZ=(0,a.forwardRef)((e,t)=>{let{__scopeDialog:r,...n}=e,o=eB(eH,r);return(0,a.createElement)(eF,{as:B,allowPinchZoom:!0,shards:[o.contentRef]},(0,a.createElement)(H.div,$({"data-state":e4(o.open)},n,{ref:t,style:{pointerEvents:"auto",...n.style}})))}),eQ="DialogContent",eJ=(0,a.forwardRef)((e,t)=>{let r=eY(eQ,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,s=eB(eQ,e.__scopeDialog);return(0,a.createElement)(eu,{present:n||s.open},s.modal?(0,a.createElement)(e0,$({},o,{ref:t})):(0,a.createElement)(e1,$({},o,{ref:t})))}),e0=(0,a.forwardRef)((e,t)=>{let r=eB(eQ,e.__scopeDialog),n=(0,a.useRef)(null),o=L(t,r.contentRef,n);return(0,a.useEffect)(()=>{let e=n.current;if(e)return(0,eq.Eq)(e)},[]),(0,a.createElement)(e2,$({},e,{ref:o,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:O(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:O(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:O(e.onFocusOutside,e=>e.preventDefault())}))}),e1=(0,a.forwardRef)((e,t)=>{let r=eB(eQ,e.__scopeDialog),n=(0,a.useRef)(!1);return(0,a.createElement)(e2,$({},e,{ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,a;null==(o=e.onCloseAutoFocus)||o.call(e,t),t.defaultPrevented||(n.current||null==(a=r.triggerRef.current)||a.focus(),t.preventDefault()),n.current=!1},onInteractOutside:t=>{var o,a;null==(o=e.onInteractOutside)||o.call(e,t),t.defaultPrevented||(n.current=!0);let s=t.target;(null==(a=r.triggerRef.current)?void 0:a.contains(s))&&t.preventDefault()}}))}),e2=(0,a.forwardRef)((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:s,...l}=e,i=eB(eQ,r),c=L(t,(0,a.useRef)(null));return(0,a.useEffect)(()=>{var e,t;let r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=r[0])?e:em()),document.body.insertAdjacentElement("beforeend",null!=(t=r[1])?t:em()),ep++,()=>{1===ep&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),ep--}},[]),(0,a.createElement)(a.Fragment,null,(0,a.createElement)(eo,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:s},(0,a.createElement)(Q,$({role:"dialog",id:i.contentId,"aria-describedby":i.descriptionId,"aria-labelledby":i.titleId,"data-state":e4(i.open)},l,{ref:c,onDismiss:()=>i.onOpenChange(!1)}))),!1)}),e3="DialogTitle";function e4(e){return e?"open":"closed"}let[e9,e8]=function(e,t){let r=(0,a.createContext)(t);function n(e){let{children:t,...n}=e,o=(0,a.useMemo)(()=>n,Object.values(n));return(0,a.createElement)(r.Provider,{value:o},t)}return n.displayName=e+"Provider",[n,function(n){let o=(0,a.useContext)(r);if(o)return o;if(void 0!==t)return t;throw Error(`\`${n}\` must be used within \`${e}\``)}]}("DialogTitleWarning",{contentName:eQ,titleName:e3,docsSlug:"dialog"}),e5=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:o,onOpenChange:s,modal:l=!0}=e,i=(0,a.useRef)(null),c=(0,a.useRef)(null),[d=!1,u]=function({prop:e,defaultProp:t,onChange:r=()=>{}}){let[n,o]=function({defaultProp:e,onChange:t}){let r=(0,a.useState)(e),[n]=r,o=(0,a.useRef)(n),s=z(t);return(0,a.useEffect)(()=>{o.current!==n&&(s(n),o.current=n)},[n,o,s]),r}({defaultProp:t,onChange:r}),s=void 0!==e,l=s?e:n,i=z(r);return[l,(0,a.useCallback)(t=>{if(s){let r="function"==typeof t?t(e):t;r!==e&&i(r)}else o(t)},[s,e,o,i])]}({prop:n,defaultProp:o,onChange:s});return(0,a.createElement)(eW,{scope:t,triggerRef:i,contentRef:c,contentId:U(),titleId:U(),descriptionId:U(),open:d,onOpenChange:u,onOpenToggle:(0,a.useCallback)(()=>u(e=>!e),[u]),modal:l},r)},e7=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:o}=e,s=eB(eV,t);return(0,a.createElement)(eG,{scope:t,forceMount:r},a.Children.map(n,e=>(0,a.createElement)(eu,{present:r||s.open},(0,a.createElement)(ed,{asChild:!0,container:o},e))))};var e6='[cmdk-group=""]',te='[cmdk-group-items=""]',tt='[cmdk-item=""]',tr=`${tt}:not([aria-disabled="true"])`,tn="cmdk-item-select",to="data-value",ta=(e,t)=>(function(e,t){return function e(t,r,n,o,a,s,l){if(s===r.length)return a===t.length?1:.99;var i=`${a},${s}`;if(void 0!==l[i])return l[i];for(var c,d,u,f,p=o.charAt(s),m=n.indexOf(p,a),h=0;m>=0;)(c=e(t,r,n,o,m+1,s+1,l))>h&&(m===a?c*=1:S.test(t.charAt(m-1))?(c*=.8,(u=t.slice(a,m-1).match(P))&&a>0&&(c*=Math.pow(.999,u.length))):I.test(t.charAt(m-1))?(c*=.9,(f=t.slice(a,m-1).match(R))&&a>0&&(c*=Math.pow(.999,f.length))):(c*=.17,a>0&&(c*=Math.pow(.999,m-a))),t.charAt(m)!==r.charAt(s)&&(c*=.9999)),(c<.1&&n.charAt(m-1)===o.charAt(s+1)||o.charAt(s+1)===o.charAt(s)&&n.charAt(m-1)!==o.charAt(s))&&.1*(d=e(t,r,n,o,m+1,s+2,l))>c&&(c=.1*d),c>h&&(h=c),m=n.indexOf(p,m+1);return l[i]=h,h}(e,t,T(e),T(t),0,0,{})})(e,t),ts=a.createContext(void 0),tl=()=>a.useContext(ts),ti=a.createContext(void 0),tc=()=>a.useContext(ti),td=a.createContext(void 0),tu=a.forwardRef((e,t)=>{let r=a.useRef(null),n=ty(()=>{var t,r,n;return{search:"",value:null!=(n=null!=(r=e.value)?r:null==(t=e.defaultValue)?void 0:t.toLowerCase())?n:"",filtered:{count:0,items:new Map,groups:new Set}}}),o=ty(()=>new Set),s=ty(()=>new Map),l=ty(()=>new Map),i=ty(()=>new Set),c=tw(e),{label:d,children:u,value:f,onValueChange:p,filter:m,shouldFilter:h,vimBindings:v=!0,...g}=e,x=a.useId(),w=a.useId(),b=a.useId(),y=tk();tb(()=>{if(void 0!==f){let e=f.trim().toLowerCase();n.current.value=e,y(6,A),E.emit()}},[f]);let E=a.useMemo(()=>({subscribe:e=>(i.current.add(e),()=>i.current.delete(e)),snapshot:()=>n.current,setState:(e,t,r)=>{var o,a,s;if(!Object.is(n.current[e],t)){if(n.current[e]=t,"search"===e)_(),k(),y(1,N);else if("value"===e)if((null==(o=c.current)?void 0:o.value)!==void 0){null==(s=(a=c.current).onValueChange)||s.call(a,null!=t?t:"");return}else r||y(5,A);E.emit()}},emit:()=>{i.current.forEach(e=>e())}}),[]),j=a.useMemo(()=>({value:(e,t)=>{t!==l.current.get(e)&&(l.current.set(e,t),n.current.filtered.items.set(e,C(t)),y(2,()=>{k(),E.emit()}))},item:(e,t)=>(o.current.add(e),t&&(s.current.has(t)?s.current.get(t).add(e):s.current.set(t,new Set([e]))),y(3,()=>{_(),k(),n.current.value||N(),E.emit()}),()=>{l.current.delete(e),o.current.delete(e),n.current.filtered.items.delete(e);let t=S();y(4,()=>{_(),(null==t?void 0:t.getAttribute("id"))===e&&N(),E.emit()})}),group:e=>(s.current.has(e)||s.current.set(e,new Set),()=>{l.current.delete(e),s.current.delete(e)}),filter:()=>c.current.shouldFilter,label:d||e["aria-label"],commandRef:r,listId:x,inputId:b,labelId:w}),[]);function C(e){var t,r;let o=null!=(r=null==(t=c.current)?void 0:t.filter)?r:ta;return e?o(e,n.current.search):0}function k(){if(!r.current||!n.current.search||!1===c.current.shouldFilter)return;let e=n.current.filtered.items,t=[];n.current.filtered.groups.forEach(r=>{let n=s.current.get(r),o=0;n.forEach(t=>{o=Math.max(e.get(t),o)}),t.push([r,o])});let o=r.current.querySelector('[cmdk-list-sizer=""]');P().sort((t,r)=>{var n,o;let a=t.getAttribute(to),s=r.getAttribute(to);return(null!=(n=e.get(s))?n:0)-(null!=(o=e.get(a))?o:0)}).forEach(e=>{let t=e.closest(te);t?t.appendChild(e.parentElement===t?e:e.closest(`${te} > *`)):o.appendChild(e.parentElement===o?e:e.closest(`${te} > *`))}),t.sort((e,t)=>t[1]-e[1]).forEach(e=>{let t=r.current.querySelector(`${e6}[${to}="${e[0]}"]`);null==t||t.parentElement.appendChild(t)})}function N(){let e=P().find(e=>!e.ariaDisabled),t=null==e?void 0:e.getAttribute(to);E.setState("value",t||void 0)}function _(){if(!n.current.search||!1===c.current.shouldFilter){n.current.filtered.count=o.current.size;return}n.current.filtered.groups=new Set;let e=0;for(let t of o.current){let r=C(l.current.get(t));n.current.filtered.items.set(t,r),r>0&&e++}for(let[e,t]of s.current)for(let r of t)if(n.current.filtered.items.get(r)>0){n.current.filtered.groups.add(e);break}n.current.filtered.count=e}function A(){var e,t,r;let n=S();n&&((null==(e=n.parentElement)?void 0:e.firstChild)===n&&(null==(r=null==(t=n.closest(e6))?void 0:t.querySelector('[cmdk-group-heading=""]'))||r.scrollIntoView({block:"nearest"})),n.scrollIntoView({block:"nearest"}))}function S(){var e;return null==(e=r.current)?void 0:e.querySelector(`${tt}[aria-selected="true"]`)}function P(){return Array.from(r.current.querySelectorAll(tr))}function I(e){let t=P()[e];t&&E.setState("value",t.getAttribute(to))}function R(e){var t;let r=S(),n=P(),o=n.findIndex(e=>e===r),a=n[o+e];null!=(t=c.current)&&t.loop&&(a=o+e<0?n[n.length-1]:o+e===n.length?n[0]:n[o+e]),a&&E.setState("value",a.getAttribute(to))}function T(e){let t=S(),r=null==t?void 0:t.closest(e6),n;for(;r&&!n;)n=null==(r=e>0?function(e,t){let r=e.nextElementSibling;for(;r;){if(r.matches(t))return r;r=r.nextElementSibling}}(r,e6):function(e,t){let r=e.previousElementSibling;for(;r;){if(r.matches(t))return r;r=r.previousElementSibling}}(r,e6))?void 0:r.querySelector(tr);n?E.setState("value",n.getAttribute(to)):R(e)}let $=()=>I(P().length-1),O=e=>{e.preventDefault(),e.metaKey?$():e.altKey?T(1):R(1)},D=e=>{e.preventDefault(),e.metaKey?I(0):e.altKey?T(-1):R(-1)};return a.createElement("div",{ref:tE([r,t]),...g,"cmdk-root":"",onKeyDown:e=>{var t;if(null==(t=g.onKeyDown)||t.call(g,e),!e.defaultPrevented)switch(e.key){case"n":case"j":v&&e.ctrlKey&&O(e);break;case"ArrowDown":O(e);break;case"p":case"k":v&&e.ctrlKey&&D(e);break;case"ArrowUp":D(e);break;case"Home":e.preventDefault(),I(0);break;case"End":e.preventDefault(),$();break;case"Enter":if(!e.nativeEvent.isComposing){e.preventDefault();let t=S();if(t){let e=new Event(tn);t.dispatchEvent(e)}}}}},a.createElement("label",{"cmdk-label":"",htmlFor:j.inputId,id:j.labelId,style:tN},d),a.createElement(ti.Provider,{value:E},a.createElement(ts.Provider,{value:j},u)))}),tf=a.forwardRef((e,t)=>{var r,n;let o=a.useId(),s=a.useRef(null),l=a.useContext(td),i=tl(),c=tw(e),d=null!=(n=null==(r=c.current)?void 0:r.forceMount)?n:null==l?void 0:l.forceMount;tb(()=>i.item(o,null==l?void 0:l.id),[]);let u=tC(o,s,[e.value,e.children,s]),f=tc(),p=tj(e=>e.value&&e.value===u.current),m=tj(e=>!!d||!1===i.filter()||!e.search||e.filtered.items.get(o)>0);function h(){var e,t;v(),null==(t=(e=c.current).onSelect)||t.call(e,u.current)}function v(){f.setState("value",u.current,!0)}if(a.useEffect(()=>{let t=s.current;if(!(!t||e.disabled))return t.addEventListener(tn,h),()=>t.removeEventListener(tn,h)},[m,e.onSelect,e.disabled]),!m)return null;let{disabled:g,value:x,onSelect:w,forceMount:b,...y}=e;return a.createElement("div",{ref:tE([s,t]),...y,id:o,"cmdk-item":"",role:"option","aria-disabled":g||void 0,"aria-selected":p||void 0,"data-disabled":g||void 0,"data-selected":p||void 0,onPointerMove:g?void 0:v,onClick:g?void 0:h},e.children)}),tp=a.forwardRef((e,t)=>{let{heading:r,children:n,forceMount:o,...s}=e,l=a.useId(),i=a.useRef(null),c=a.useRef(null),d=a.useId(),u=tl(),f=tj(e=>!!o||!1===u.filter()||!e.search||e.filtered.groups.has(l));tb(()=>u.group(l),[]),tC(l,i,[e.value,e.heading,c]);let p=a.useMemo(()=>({id:l,forceMount:o}),[o]),m=a.createElement(td.Provider,{value:p},n);return a.createElement("div",{ref:tE([i,t]),...s,"cmdk-group":"",role:"presentation",hidden:!f||void 0},r&&a.createElement("div",{ref:c,"cmdk-group-heading":"","aria-hidden":!0,id:d},r),a.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":r?d:void 0},m))}),tm=a.forwardRef((e,t)=>{let{alwaysRender:r,...n}=e,o=a.useRef(null),s=tj(e=>!e.search);return r||s?a.createElement("div",{ref:tE([o,t]),...n,"cmdk-separator":"",role:"separator"}):null}),th=a.forwardRef((e,t)=>{let{onValueChange:r,...n}=e,o=null!=e.value,s=tc(),l=tj(e=>e.search),i=tj(e=>e.value),c=tl(),d=a.useMemo(()=>{var e;let t=null==(e=c.commandRef.current)?void 0:e.querySelector(`${tt}[${to}="${i}"]`);return null==t?void 0:t.getAttribute("id")},[i,c.commandRef]);return a.useEffect(()=>{null!=e.value&&s.setState("search",e.value)},[e.value]),a.createElement("input",{ref:t,...n,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":c.listId,"aria-labelledby":c.labelId,"aria-activedescendant":d,id:c.inputId,type:"text",value:o?e.value:l,onChange:e=>{o||s.setState("search",e.target.value),null==r||r(e.target.value)}})}),tv=a.forwardRef((e,t)=>{let{children:r,...n}=e,o=a.useRef(null),s=a.useRef(null),l=tl();return a.useEffect(()=>{if(s.current&&o.current){let e=s.current,t=o.current,r,n=new ResizeObserver(()=>{r=requestAnimationFrame(()=>{let r=e.offsetHeight;t.style.setProperty("--cmdk-list-height",r.toFixed(1)+"px")})});return n.observe(e),()=>{cancelAnimationFrame(r),n.unobserve(e)}}},[]),a.createElement("div",{ref:tE([o,t]),...n,"cmdk-list":"",role:"listbox","aria-label":"Suggestions",id:l.listId,"aria-labelledby":l.inputId},a.createElement("div",{ref:s,"cmdk-list-sizer":""},r))}),tg=a.forwardRef((e,t)=>{let{open:r,onOpenChange:n,overlayClassName:o,contentClassName:s,container:l,...i}=e;return a.createElement(e5,{open:r,onOpenChange:n},a.createElement(e7,{container:l},a.createElement(eX,{"cmdk-overlay":"",className:o}),a.createElement(eJ,{"aria-label":e.label,"cmdk-dialog":"",className:s},a.createElement(tu,{ref:t,...i}))))}),tx=Object.assign(tu,{List:tv,Item:tf,Input:th,Group:tp,Separator:tm,Dialog:tg,Empty:a.forwardRef((e,t)=>{let r=a.useRef(!0),n=tj(e=>0===e.filtered.count);return a.useEffect(()=>{r.current=!1},[]),r.current||!n?null:a.createElement("div",{ref:t,...e,"cmdk-empty":"",role:"presentation"})}),Loading:a.forwardRef((e,t)=>{let{progress:r,children:n,...o}=e;return a.createElement("div",{ref:t,...o,"cmdk-loading":"",role:"progressbar","aria-valuenow":r,"aria-valuemin":0,"aria-valuemax":100,"aria-label":"Loading..."},a.createElement("div",{"aria-hidden":!0},n))})});function tw(e){let t=a.useRef(e);return tb(()=>{t.current=e}),t}var tb="undefined"==typeof window?a.useEffect:a.useLayoutEffect;function ty(e){let t=a.useRef();return void 0===t.current&&(t.current=e()),t}function tE(e){return t=>{e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}}function tj(e){let t=tc(),r=()=>e(t.snapshot());return a.useSyncExternalStore(t.subscribe,r,r)}function tC(e,t,r){let n=a.useRef(),o=tl();return tb(()=>{var a;let s=(()=>{var e;for(let t of r){if("string"==typeof t)return t.trim().toLowerCase();if("object"==typeof t&&"current"in t)return t.current?null==(e=t.current.textContent)?void 0:e.trim().toLowerCase():n.current}})();o.value(e,s),null==(a=t.current)||a.setAttribute(to,s),n.current=s}),n}var tk=()=>{let[e,t]=a.useState(),r=ty(()=>new Map);return tb(()=>{r.current.forEach(e=>e()),r.current=new Map},[e]),(e,n)=>{r.current.set(e,n),t({})}},tN={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"},t_=r(99270),tA=r(4780);function tS({className:e,...t}){return(0,o.jsx)(tx,{"data-slot":"command",className:(0,tA.cn)("bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md",e),...t})}function tP({className:e,...t}){return(0,o.jsxs)("div",{"data-slot":"command-input-wrapper",className:"flex h-9 items-center gap-2 border-b px-3",children:[(0,o.jsx)(t_.A,{className:"size-4 shrink-0 opacity-50"}),(0,o.jsx)(tx.Input,{"data-slot":"command-input",className:(0,tA.cn)("placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50",e),...t})]})}function tI({className:e,...t}){return(0,o.jsx)(tx.List,{"data-slot":"command-list",className:(0,tA.cn)("max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto",e),...t})}function tR({...e}){return(0,o.jsx)(tx.Empty,{"data-slot":"command-empty",className:"py-6 text-center text-sm",...e})}function tT({className:e,...t}){return(0,o.jsx)(tx.Group,{"data-slot":"command-group",className:(0,tA.cn)("text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium",e),...t})}function t$({className:e,...t}){return(0,o.jsx)(tx.Item,{"data-slot":"command-item",className:(0,tA.cn)("data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}r(63503);let tO=(e,t)=>"agentpress"===t?({sb_shell_tool:"Shell Tool",sb_files_tool:"Files Tool",sb_browser_tool:"Browser Tool",sb_deploy_tool:"Deploy Tool",sb_expose_tool:"Expose Tool",web_search_tool:"Web Search",sb_vision_tool:"Vision Tool",data_providers_tool:"Data Providers"})[e]||e:e.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" ");function tD({steps:e,onStepsChange:t,agentTools:r,isLoadingTools:n}){let[s,l]=(0,a.useState)({}),[i,c]=(0,a.useState)({});e.forEach((e,t)=>{console.log(`Step ${t}:`,{name:e.name,type:e.type,hasChildren:!!e.children,childrenCount:e.children?.length||0,children:e.children?.map(e=>({name:e.name,type:e.type}))})});let d=()=>Math.random().toString(36).substr(2,9),m=(0,a.useCallback)((r,n)=>{let o={id:d(),name:"Step",description:"",type:"instruction",config:{},order:0,enabled:!0},a=e=>{if(!r){if(n){let t=e.findIndex(e=>e.id===n);return[...e.slice(0,t+1),o,...e.slice(t+1)]}return[...e,o]}return e.map(e=>e.id===r?{...e,children:[...e.children||[],o]}:e.children?{...e,children:a(e.children)}:e)};t(a(e))},[e,t]),v=(0,a.useCallback)(r=>{let n={id:d(),name:"If",description:"",type:"condition",config:{},conditions:{type:"if",expression:""},children:[],order:0,enabled:!0,hasIssues:!0},o=e=>{let t=e.findIndex(e=>e.id===r);return -1!==t?[...e.slice(0,t+1),n,...e.slice(t+1)]:e.map(e=>e.children?{...e,children:o(e.children)}:e)};t(o(e))},[e,t]),g=(0,a.useCallback)(r=>{let n={id:d(),name:"Else If",description:"",type:"condition",config:{},conditions:{type:"elseif",expression:""},children:[],order:0,enabled:!0,hasIssues:!0},o=e=>{let t=e.findIndex(e=>e.id===r);return -1!==t?[...e.slice(0,t+1),n,...e.slice(t+1)]:e.map(e=>e.children?{...e,children:o(e.children)}:e)};t(o(e))},[e,t]),x=(0,a.useCallback)(r=>{let n={id:d(),name:"Else",description:"",type:"condition",config:{},conditions:{type:"else"},children:[],order:0,enabled:!0,hasIssues:!1},o=e=>{let t=e.findIndex(e=>e.id===r);return -1!==t?[...e.slice(0,t+1),n,...e.slice(t+1)]:e.map(e=>e.children?{...e,children:o(e.children)}:e)};t(o(e))},[e,t]),w=(0,a.useCallback)((r,n)=>{let o=e=>e.map(e=>{if(e.id===r){let t={...e,...n};return"instruction"===t.type&&t.name&&"New Step"!==t.name||"condition"===t.type&&(t.conditions?.type==="if"||t.conditions?.type==="elseif")&&t.conditions?.expression?t.hasIssues=!1:"condition"===t.type&&t.conditions?.type==="else"&&(t.hasIssues=!1),t}return e.children?{...e,children:o(e.children)}:e});t(o(e))},[e,t]),b=(0,a.useCallback)(r=>{let n=e=>e.filter(e=>e.id!==r).map(e=>e.children?{...e,children:n(e.children)}:e);t(n(e))},[e,t]),y=(0,a.useCallback)((t,r=e,n={value:0})=>{for(let e of r){if(n.value++,e.id===t)return n.value;if(e.children&&e.children.length>0){let r=y(t,e.children,n);if(r>0)return r}}return 0},[e]),E=e=>String.fromCharCode(65+e),S=(e,t)=>{let r=i[t]||e[0]?.id,n=e.find(e=>e.id===r)||e[0],a=e.some(e=>e.conditions?.type==="else"),s=(r,n)=>{if(("Backspace"===r.key||"Delete"===r.key)&&(r.preventDefault(),e.length>1&&(1!==e.length||n.conditions?.type!=="if"))){b(n.id);let r=e.filter(e=>e.id!==n.id);r.length>0&&c(e=>({...e,[t]:r[0].id}))}};return(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[e.map((e,n)=>{let a=E(n),l=e.id===r,i=e.conditions?.type==="if"?"If":e.conditions?.type==="elseif"?"Else If":e.conditions?.type==="else"?"Else":"If";return(0,o.jsxs)("button",{onClick:()=>c(r=>({...r,[t]:e.id})),onKeyDown:t=>s(t,e),tabIndex:0,className:(0,tA.cn)("flex items-center gap-2 px-3 py-2 rounded-md border text-sm font-medium transition-all",l?"bg-primary text-primary-foreground border-primary shadow-sm":"bg-background border-border text-foreground hover:bg-accent hover:text-accent-foreground"),children:[(0,o.jsx)("span",{className:"font-mono text-xs",children:a}),(0,o.jsx)("span",{children:"•"}),(0,o.jsx)("span",{children:i}),e.hasIssues&&(0,o.jsx)(j.A,{className:"h-3 w-3 text-destructive"})]},e.id)}),!a&&(0,o.jsxs)(u.$,{variant:"outline",size:"sm",onClick:()=>g(e[e.length-1].id),className:"h-9 px-3 border-dashed text-xs",children:[(0,o.jsx)(C.A,{className:"h-3 w-3 mr-1"}),"Else If"]}),!a&&(0,o.jsxs)(u.$,{variant:"outline",size:"sm",onClick:()=>x(e[e.length-1].id),className:"h-9 px-3 border-dashed text-xs",children:[(0,o.jsx)(C.A,{className:"h-3 w-3 mr-1"}),"Else"]})]}),n&&(0,o.jsxs)("div",{className:"bg-muted/50 rounded-lg p-4 border",children:[n.conditions?.type==="if"||n.conditions?.type==="elseif"?(0,o.jsxs)("div",{className:"space-y-3",children:[(0,o.jsx)(p.Label,{className:"text-sm font-medium",children:n.conditions?.type==="if"?"Condition":"Else If Condition"}),(0,o.jsx)(f.p,{type:"text",value:n.conditions.expression||"",onChange:e=>w(n.id,{conditions:{...n.conditions,expression:e.target.value}}),placeholder:"e.g., user asks about pricing",className:"w-full bg-transparent text-sm px-3 py-2 rounded-md"})]}):(0,o.jsx)("div",{className:"text-sm text-muted-foreground font-medium",children:"Otherwise (fallback condition)"}),(0,o.jsxs)("div",{className:"mt-4 space-y-3",children:[n.children&&n.children.length>0&&(0,o.jsx)(o.Fragment,{children:n.children.map((e,t)=>P(e,t+1,!0,n.id))}),(0,o.jsx)("div",{className:"flex justify-center pt-2",children:(0,o.jsxs)(u.$,{variant:"outline",size:"sm",onClick:()=>m(n.id),className:"border-dashed text-xs",children:[(0,o.jsx)(C.A,{className:"h-3 w-3"}),"Add step"]})})]})]})]})},P=(e,t,a=!1,i)=>{let c="condition"===e.type,d="sequence"===e.type;return c?null:(0,o.jsx)("div",{className:"group",children:(0,o.jsx)("div",{className:"bg-card rounded-lg border shadow-sm p-4 transition-shadow",children:(0,o.jsxs)("div",{className:"flex items-start gap-4",children:[(0,o.jsxs)("div",{className:"flex items-center gap-2 shrink-0",children:[e.hasIssues&&(0,o.jsx)(j.A,{className:"h-4 w-4 text-destructive"}),(0,o.jsx)("div",{className:"w-6 h-6 rounded-full bg-muted flex items-center justify-center text-sm font-medium text-muted-foreground",children:t})]}),(0,o.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,o.jsx)("div",{className:"flex items-center justify-between mb-3",children:d?(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)("div",{className:"w-5 h-5 rounded bg-primary/10 flex items-center justify-center",children:(0,o.jsx)("div",{className:"w-2 h-2 rounded-full bg-primary"})}),(0,o.jsx)("span",{className:"text-base font-medium",children:e.description})]}):(0,o.jsx)("input",{type:"text",value:e.name+" "+t,onChange:t=>w(e.id,{name:t.target.value}),placeholder:"Step name",className:"w-full bg-transparent border-0 outline-none text-base font-medium placeholder:text-muted-foreground"})}),!d&&void 0!==e.description&&(0,o.jsx)("input",{type:"text",value:e.description,onChange:t=>w(e.id,{description:t.target.value}),placeholder:"Add a description",className:"-mt-2 w-full bg-transparent border-0 outline-none text-sm text-muted-foreground placeholder:text-muted-foreground mb-3"}),!d&&(0,o.jsxs)(h.AM,{open:s[e.id]||!1,onOpenChange:t=>l(r=>({...r,[e.id]:t})),children:[(0,o.jsx)(h.Wv,{asChild:!0,children:(0,o.jsxs)(u.$,{variant:"outline",role:"combobox","aria-expanded":s[e.id]||!1,className:"h-9 w-full justify-between text-sm",children:[e.config.tool_name?(0,o.jsx)("span",{className:"flex items-center gap-2 text-sm",children:(()=>{let t=r?.agentpress_tools.find(t=>t.name===e.config.tool_name);if(t)return(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("span",{children:t.icon||"\uD83D\uDD27"}),(0,o.jsx)("span",{children:tO(t.name,"agentpress")})]});let n=r?.mcp_tools.find(t=>`${t.server}:${t.name}`===e.config.tool_name);return n?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("span",{children:n.icon||"\uD83D\uDD27"}),(0,o.jsx)("span",{children:tO(n.name,"mcp")})]}):e.config.tool_name})()}):(0,o.jsx)("span",{className:"text-muted-foreground",children:"Select tool (optional)"}),(0,o.jsx)(k.A,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),(0,o.jsx)(h.hl,{className:"w-[320px] p-0",align:"start",children:(0,o.jsxs)(tS,{children:[(0,o.jsx)(tP,{placeholder:"Search tools...",className:"h-9"}),(0,o.jsx)(tR,{children:"No tools found."}),(0,o.jsx)(tI,{children:n?(0,o.jsx)(t$,{disabled:!0,children:"Loading tools..."}):r?(0,o.jsxs)(o.Fragment,{children:[r.agentpress_tools.filter(e=>e.enabled).length>0&&(0,o.jsx)(tT,{heading:"Default Tools",children:r.agentpress_tools.filter(e=>e.enabled).map(t=>(0,o.jsxs)(t$,{value:`${tO(t.name,"agentpress")} ${t.name}`,onSelect:()=>{w(e.id,{config:{...e.config,tool_name:t.name}}),l(t=>({...t,[e.id]:!1}))},children:[(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)("span",{children:t.icon||"\uD83D\uDD27"}),(0,o.jsx)("span",{children:tO(t.name,"agentpress")})]}),(0,o.jsx)(N.A,{className:(0,tA.cn)("ml-auto h-4 w-4",e.config.tool_name===t.name?"opacity-100":"opacity-0")})]},t.name))}),r.mcp_tools.length>0&&(0,o.jsx)(tT,{heading:"External Tools",children:r.mcp_tools.map(t=>(0,o.jsxs)(t$,{value:`${tO(t.name,"mcp")} ${t.name} ${t.server||""}`,onSelect:()=>{w(e.id,{config:{...e.config,tool_name:t.server?`${t.server}:${t.name}`:t.name}}),l(t=>({...t,[e.id]:!1}))},children:[(0,o.jsxs)("div",{className:"flex items-center gap-2",children:[(0,o.jsx)("span",{children:t.icon||"\uD83D\uDD27"}),(0,o.jsx)("span",{children:tO(t.name,"mcp")})]}),(0,o.jsx)(N.A,{className:(0,tA.cn)("ml-auto h-4 w-4",e.config.tool_name===(t.server?`${t.server}:${t.name}`:t.name)?"opacity-100":"opacity-0")})]},`${t.server||"default"}-${t.name}`))})]}):(0,o.jsx)(t$,{disabled:!0,children:"Failed to load tools"})})]})})]}),e.children&&e.children.length>0&&(0,o.jsx)("div",{className:"mt-4 space-y-4",children:e.children.map((t,r)=>P(t,r+1,!0,e.id))})]}),(0,o.jsxs)(h.AM,{children:[(0,o.jsx)(h.Wv,{asChild:!0,children:(0,o.jsx)(u.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,o.jsx)(_.A,{className:"h-4 w-4"})})}),(0,o.jsx)(h.hl,{className:"w-48 p-1",align:"end",children:(0,o.jsxs)(u.$,{variant:"ghost",size:"sm",onClick:()=>b(e.id),className:"w-full justify-start text-destructive hover:text-destructive hover:bg-destructive/10",children:[(0,o.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Delete step"]})})]})]})})},e.id)};return(0,o.jsx)("div",{className:"space-y-6 max-w-4xl",children:0===e.length?(0,o.jsxs)("div",{className:"text-center py-16",children:[(0,o.jsx)("div",{className:"w-16 h-16 bg-muted rounded-2xl flex items-center justify-center mx-auto mb-4",children:(0,o.jsx)(C.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,o.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Start building your workflow"}),(0,o.jsx)("p",{className:"text-muted-foreground mb-6 max-w-md mx-auto",children:"Add steps and conditions to create a smart workflow that adapts to different scenarios."}),(0,o.jsxs)(u.$,{onClick:()=>m(),children:[(0,o.jsx)(C.A,{className:"h-4 w-4"}),"Add step"]})]}):(0,o.jsxs)("div",{className:"space-y-6",children:[(()=>{let t=[],r=0,n=0;for(;n<e.length;){let a=e[n];if("condition"===a.type){let a=[];for(;n<e.length&&"condition"===e[n].type;)a.push(e[n]),n++;r++,t.push((0,o.jsx)("div",{className:"bg-card rounded-lg border shadow-sm p-4 transition-shadow",children:(0,o.jsxs)("div",{className:"flex items-start gap-4",children:[(0,o.jsx)("div",{className:"flex items-center gap-2 shrink-0",children:(0,o.jsx)("div",{className:"w-6 h-6 rounded-full bg-muted flex items-center justify-center text-sm font-medium text-muted-foreground",children:r})}),(0,o.jsxs)("div",{className:"flex-1",children:[(0,o.jsx)("div",{className:"text-base font-medium mb-4",children:"Add rule"}),S(a,a[0].id)]})]})},a[0].id))}else r++,t.push(P(a,r,!1)),n++}return t})(),(0,o.jsx)("div",{className:"flex justify-center pt-4",children:(0,o.jsxs)("div",{className:"flex gap-3",children:[(0,o.jsxs)(u.$,{variant:"outline",onClick:()=>m(),className:"border-dashed",children:[(0,o.jsx)(C.A,{className:"h-4 w-4"}),"Add step"]}),(0,o.jsxs)(u.$,{variant:"outline",onClick:()=>v(e[e.length-1]?.id||""),className:"border-dashed",children:[(0,o.jsx)(C.A,{className:"h-4 w-4"}),"Add rule"]})]})})]})})}let tL=e=>{let t=1,r=e=>e.map(e=>{let n={id:e.id,name:e.name,description:e.description,type:e.type,config:e.config,order:t++};return"condition"===e.type&&e.conditions&&(n.conditions=e.conditions),e.children&&e.children.length>0&&(n.children=r(e.children)),n});return r(e)};function tM(){let e=(0,s.useParams)(),t=(0,s.useRouter)(),r=e.agentId,n=e.workflowId,{data:x=[],isLoading:w}=(0,g.X3)(r),b=(0,g.Ci)(),y=(0,g.Vb)(),{data:j,isLoading:C}=E(r),k=!!n,[N,_]=(0,a.useState)(""),[A,S]=(0,a.useState)(""),[P,I]=(0,a.useState)(""),[R,T]=(0,a.useState)(!1),[$,O]=(0,a.useState)([]),[D,L]=(0,a.useState)(!1),[M,F]=(0,a.useState)(k),q=(0,a.useCallback)(async()=>{if(!N.trim())return void v.oR.error("Please enter a workflow name");let e=tL($);try{k?(await y.mutateAsync({agentId:r,workflowId:n,workflow:{name:N,description:A,trigger_phrase:P||void 0,is_default:R,steps:e}}),v.oR.success("Workflow updated successfully")):(await b.mutateAsync({agentId:r,workflow:{name:N,description:A,trigger_phrase:P||void 0,is_default:R,steps:e}}),v.oR.success("Workflow created successfully"))}catch(e){v.oR.error(`Failed to ${k?"update":"create"} workflow`)}},[N,A,P,R,$,r,n,k,b,y,t]);return M||w?(0,o.jsx)("div",{className:"h-screen flex items-center justify-center",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"}),(0,o.jsx)("p",{children:"Loading workflow..."})]})}):(0,o.jsxs)("div",{className:"h-screen flex flex-col bg-background",children:[(0,o.jsxs)("div",{className:"border-b bg-card px-2 py-4 flex items-center justify-between",children:[(0,o.jsxs)("div",{className:"flex items-center gap-4",children:[(0,o.jsx)(u.$,{variant:"ghost",size:"icon",onClick:()=>t.back(),className:"h-8 w-8",children:(0,o.jsx)(l.A,{className:"h-4 w-4"})}),(0,o.jsxs)("div",{className:"flex items-center gap-3",children:[(0,o.jsx)("div",{className:"w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center",children:(0,o.jsx)(i.A,{className:"h-4 w-4 text-primary"})}),(0,o.jsx)("div",{children:(0,o.jsx)("h1",{className:"text-lg font-semibold",children:N||"Untitled Workflow"})}),(0,o.jsxs)(h.AM,{open:D,onOpenChange:L,children:[(0,o.jsx)(h.Wv,{asChild:!0,children:(0,o.jsx)(u.$,{variant:"ghost",size:"icon",className:"h-8 w-8 ml-2",children:(0,o.jsx)(c.A,{className:"h-4 w-4"})})}),(0,o.jsx)(h.hl,{className:"w-96",align:"start",children:(0,o.jsxs)("div",{className:"space-y-4",children:[(0,o.jsx)("div",{children:(0,o.jsx)("h3",{className:"font-medium text-sm mb-3",children:"Workflow Settings"})}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(p.Label,{className:"text-xs font-medium",children:"Name"}),(0,o.jsx)(f.p,{value:N,onChange:e=>_(e.target.value),placeholder:"Enter workflow name",className:"h-8"})]}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)(p.Label,{className:"text-xs font-medium",children:"Description"}),(0,o.jsx)(m.T,{value:A,onChange:e=>S(e.target.value),placeholder:"Describe what this workflow does",rows:3,className:"resize-none text-sm"})]}),(0,o.jsx)(u.$,{onClick:()=>L(!1),className:"w-full h-8",size:"sm",children:"Done"})]})})]})]})]}),(0,o.jsxs)(u.$,{onClick:q,disabled:b.isPending||y.isPending,size:"sm",className:"h-8",children:[(0,o.jsx)(d.A,{className:"h-3.5 w-3.5"}),b.isPending||y.isPending?"Saving...":"Save Workflow"]})]}),(0,o.jsx)("div",{className:"flex-1 overflow-auto",children:(0,o.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,o.jsx)("div",{className:"p-6",children:(0,o.jsx)(tD,{steps:$,onStepsChange:O,agentTools:j,isLoadingTools:C})})})})]})}},8819:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13084:(e,t,r)=>{Promise.resolve().then(r.bind(r,84250))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34729:(e,t,r)=>{"use strict";r.d(t,{T:()=>a});var n=r(60687);r(43210);var o=r(4780);function a({className:e,...t}){return(0,n.jsx)("textarea",{"data-slot":"textarea",className:(0,o.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},36404:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.default,__next_app__:()=>d,pages:()=>c,routeModule:()=>u,tree:()=>i});var n=r(65239),o=r(48088),a=r(31369),s=r(30893),l={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);r.d(t,l);let i={children:["",{children:["(dashboard)",{children:["agents",{children:["config",{children:["[agentId]",{children:["workflow",{children:["[workflowId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,84250)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\config\\[agentId]\\workflow\\[workflowId]\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94992)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\config\\[agentId]\\workflow\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,75140)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\config\\[agentId]\\layout.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,90170)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,33532)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,88524))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,93595)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,88524))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\config\\[agentId]\\workflow\\[workflowId]\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},u=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(dashboard)/agents/config/[agentId]/workflow/[workflowId]/page",pathname:"/agents/config/[agentId]/workflow/[workflowId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},40599:(e,t,r)=>{"use strict";r.d(t,{UC:()=>B,ZL:()=>W,bL:()=>z,l9:()=>K});var n=r(43210),o=r(70569),a=r(98599),s=r(11273),l=r(31355),i=r(1359),c=r(32547),d=r(96963),u=r(55509),f=r(25028),p=r(46059),m=r(14163),h=r(8730),v=r(65551),g=r(63376),x=r(5704),w=r(60687),b="Popover",[y,E]=(0,s.A)(b,[u.Bk]),j=(0,u.Bk)(),[C,k]=y(b),N=e=>{let{__scopePopover:t,children:r,open:o,defaultOpen:a,onOpenChange:s,modal:l=!1}=e,i=j(t),c=n.useRef(null),[f,p]=n.useState(!1),[m,h]=(0,v.i)({prop:o,defaultProp:a??!1,onChange:s,caller:b});return(0,w.jsx)(u.bL,{...i,children:(0,w.jsx)(C,{scope:t,contentId:(0,d.B)(),triggerRef:c,open:m,onOpenChange:h,onOpenToggle:n.useCallback(()=>h(e=>!e),[h]),hasCustomAnchor:f,onCustomAnchorAdd:n.useCallback(()=>p(!0),[]),onCustomAnchorRemove:n.useCallback(()=>p(!1),[]),modal:l,children:r})})};N.displayName=b;var _="PopoverAnchor";n.forwardRef((e,t)=>{let{__scopePopover:r,...o}=e,a=k(_,r),s=j(r),{onCustomAnchorAdd:l,onCustomAnchorRemove:i}=a;return n.useEffect(()=>(l(),()=>i()),[l,i]),(0,w.jsx)(u.Mz,{...s,...o,ref:t})}).displayName=_;var A="PopoverTrigger",S=n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,s=k(A,r),l=j(r),i=(0,a.s)(t,s.triggerRef),c=(0,w.jsx)(m.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":U(s.open),...n,ref:i,onClick:(0,o.m)(e.onClick,s.onOpenToggle)});return s.hasCustomAnchor?c:(0,w.jsx)(u.Mz,{asChild:!0,...l,children:c})});S.displayName=A;var P="PopoverPortal",[I,R]=y(P,{forceMount:void 0}),T=e=>{let{__scopePopover:t,forceMount:r,children:n,container:o}=e,a=k(P,t);return(0,w.jsx)(I,{scope:t,forceMount:r,children:(0,w.jsx)(p.C,{present:r||a.open,children:(0,w.jsx)(f.Z,{asChild:!0,container:o,children:n})})})};T.displayName=P;var $="PopoverContent",O=n.forwardRef((e,t)=>{let r=R($,e.__scopePopover),{forceMount:n=r.forceMount,...o}=e,a=k($,e.__scopePopover);return(0,w.jsx)(p.C,{present:n||a.open,children:a.modal?(0,w.jsx)(L,{...o,ref:t}):(0,w.jsx)(M,{...o,ref:t})})});O.displayName=$;var D=(0,h.TL)("PopoverContent.RemoveScroll"),L=n.forwardRef((e,t)=>{let r=k($,e.__scopePopover),s=n.useRef(null),l=(0,a.s)(t,s),i=n.useRef(!1);return n.useEffect(()=>{let e=s.current;if(e)return(0,g.Eq)(e)},[]),(0,w.jsx)(x.A,{as:D,allowPinchZoom:!0,children:(0,w.jsx)(F,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),i.current||r.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;i.current=2===t.button||r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),M=n.forwardRef((e,t)=>{let r=k($,e.__scopePopover),o=n.useRef(!1),a=n.useRef(!1);return(0,w.jsx)(F,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||r.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),F=n.forwardRef((e,t)=>{let{__scopePopover:r,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:a,disableOutsidePointerEvents:s,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:p,onInteractOutside:m,...h}=e,v=k($,r),g=j(r);return(0,i.Oh)(),(0,w.jsx)(c.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,w.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:s,onInteractOutside:m,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:p,onDismiss:()=>v.onOpenChange(!1),children:(0,w.jsx)(u.UC,{"data-state":U(v.open),role:"dialog",id:v.contentId,...g,...h,ref:t,style:{...h.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),q="PopoverClose";function U(e){return e?"open":"closed"}n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,a=k(q,r);return(0,w.jsx)(m.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})}).displayName=q,n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,o=j(r);return(0,w.jsx)(u.i3,{...o,...n,ref:t})}).displayName="PopoverArrow";var z=N,K=S,W=T,B=O},40988:(e,t,r)=>{"use strict";r.d(t,{AM:()=>s,Wv:()=>l,hl:()=>i});var n=r(60687);r(43210);var o=r(40599),a=r(4780);function s({...e}){return(0,n.jsx)(o.bL,{"data-slot":"popover",...e})}function l({...e}){return(0,n.jsx)(o.l9,{"data-slot":"popover-trigger",...e})}function i({className:e,align:t="center",sideOffset:r=4,...s}){return(0,n.jsx)(o.ZL,{children:(0,n.jsx)(o.UC,{"data-slot":"popover-content",align:t,sideOffset:r,className:(0,a.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",e),...s})})}},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67821:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("GitBranch",[["line",{x1:"6",x2:"6",y1:"3",y2:"15",key:"17qcm7"}],["circle",{cx:"18",cy:"6",r:"3",key:"1h7g24"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["path",{d:"M18 9a9 9 0 0 1-9 9",key:"n2h4wq"}]])},70400:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"60120624b62a67d0046abca062aae687cbb5ebc44a":()=>n.vI,"602190608cb4aada86562e5e5997e6bb44fbe95e4e":()=>n.$w,"60836a64bf90333e8dead87703a0c5a32e95fa0f8f":()=>n.gj});var n=r(67834)},74075:e=>{"use strict";e.exports=require("zlib")},75140:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,metadata:()=>o});var n=r(37413);let o={title:"Create Agent | Kortix Suna",description:"Create an agent",openGraph:{title:"Create Agent | Kortix Suna",description:"Create an agent",type:"website"}};async function a({children:e}){return(0,n.jsx)(n.Fragment,{children:e})}},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84027:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},84250:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\agents\\\\config\\\\[agentId]\\\\workflow\\\\[workflowId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\config\\[agentId]\\workflow\\[workflowId]\\page.tsx","default")},84297:e=>{"use strict";e.exports=require("async_hooks")},86577:(e,t,r)=>{"use strict";r.d(t,{X3:()=>h,Ci:()=>v,Fo:()=>x,AM:()=>w,Vb:()=>g});var n=r(22372),o=r(8693),a=r(52581);let s={all:["agent-workflows"],agent:e=>[...s.all,e],workflow:(e,t)=>[...s.agent(e),t],executions:(e,t)=>[...s.workflow(e,t),"executions"]};var l=r(79481),i=r(15945);let c="http://localhost:8000/api",d=async e=>{try{if(!await (0,i.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let t=(0,l.U)(),{data:{session:r}}=await t.auth.getSession();if(!r)throw Error("You must be logged in to get workflows");let n=await fetch(`${c}/agents/${e}/workflows`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r.access_token}`}});if(!n.ok){let e=await n.json().catch(()=>({detail:"Unknown error"}));throw Error(e.detail||`HTTP ${n.status}: ${n.statusText}`)}let o=await n.json();return console.log("[API] Fetched workflows for agent:",e,o.length),o}catch(e){throw console.error("Error fetching workflows:",e),e}},u=async(e,t)=>{try{if(!await (0,i.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let r=(0,l.U)(),{data:{session:n}}=await r.auth.getSession();if(!n)throw Error("You must be logged in to create a workflow");let o=await fetch(`${c}/agents/${e}/workflows`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${n.access_token}`},body:JSON.stringify(t)});if(!o.ok){let e=await o.json().catch(()=>({detail:"Unknown error"}));throw Error(e.detail||`HTTP ${o.status}: ${o.statusText}`)}let a=await o.json();return console.log("[API] Created workflow:",a.id),a}catch(e){throw console.error("Error creating workflow:",e),e}},f=async(e,t,r)=>{try{if(console.log("[API] Updating workflow:",r),!await (0,i.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let n=(0,l.U)(),{data:{session:o}}=await n.auth.getSession();if(!o)throw Error("You must be logged in to update a workflow");let a=await fetch(`${c}/agents/${e}/workflows/${t}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o.access_token}`},body:JSON.stringify(r)});if(!a.ok){let e=await a.json().catch(()=>({detail:"Unknown error"}));throw Error(e.detail||`HTTP ${a.status}: ${a.statusText}`)}let s=await a.json();return console.log("[API] Updated workflow:",s.id),s}catch(e){throw console.error("Error updating workflow:",e),e}},p=async(e,t)=>{try{if(!await (0,i.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let r=(0,l.U)(),{data:{session:n}}=await r.auth.getSession();if(!n)throw Error("You must be logged in to delete a workflow");let o=await fetch(`${c}/agents/${e}/workflows/${t}`,{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:`Bearer ${n.access_token}`}});if(!o.ok){let e=await o.json().catch(()=>({detail:"Unknown error"}));throw Error(e.detail||`HTTP ${o.status}: ${o.statusText}`)}console.log("[API] Deleted workflow:",t)}catch(e){throw console.error("Error deleting workflow:",e),e}},m=async(e,t,r)=>{try{if(!await (0,i.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let n=(0,l.U)(),{data:{session:o}}=await n.auth.getSession();if(!o)throw Error("You must be logged in to execute a workflow");let a=await fetch(`${c}/agents/${e}/workflows/${t}/execute`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o.access_token}`},body:JSON.stringify(r)});if(!a.ok){let e=await a.json().catch(()=>({detail:"Unknown error"}));throw Error(e.detail||`HTTP ${a.status}: ${a.statusText}`)}let s=await a.json();return console.log("[API] Executed workflow:",t,"execution:",s.execution_id),s}catch(e){throw console.error("Error executing workflow:",e),e}},h=e=>(0,n.GQ)(s.agent(e),()=>d(e),{enabled:!!e,staleTime:3e4})(),v=()=>{let e=(0,o.jE)();return(0,n.Lx)(({agentId:e,workflow:t})=>u(e,t),{onSuccess:(t,r)=>{e.invalidateQueries({queryKey:s.agent(r.agentId)}),a.oR.success("Workflow created successfully")}})()},g=()=>{let e=(0,o.jE)();return(0,n.Lx)(({agentId:e,workflowId:t,workflow:r})=>f(e,t,r),{onSuccess:(t,r)=>{e.invalidateQueries({queryKey:s.agent(r.agentId)}),a.oR.success("Workflow updated successfully")}})()},x=()=>{let e=(0,o.jE)();return(0,n.Lx)(({agentId:e,workflowId:t})=>p(e,t),{onSuccess:(t,r)=>{e.invalidateQueries({queryKey:s.agent(r.agentId)}),a.oR.success("Workflow deleted successfully")}})()},w=()=>{let e=(0,o.jE)();return(0,n.Lx)(({agentId:e,workflowId:t,execution:r})=>m(e,t,r),{onSuccess:(t,r)=>{e.invalidateQueries({queryKey:s.executions(r.agentId,r.workflowId)}),a.oR.success("Workflow execution started")}})()}},90170:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,metadata:()=>o});var n=r(37413);let o={title:"Agent Conversation | Kortix Suna",description:"Interactive agent conversation powered by Kortix Suna",openGraph:{title:"Agent Conversation | Kortix Suna",description:"Interactive agent conversation powered by Kortix Suna",type:"website"}};async function a({children:e}){return(0,n.jsx)(n.Fragment,{children:e})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},94992:(e,t,r)=>{"use strict";function n({children:e}){return e}r.r(t),r.d(t,{default:()=>n})},96487:()=>{},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},99516:(e,t,r)=>{Promise.resolve().then(r.bind(r,5178))}};var t=require("../../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[7719,5193,4267,7096,1265,3530,7156,7976,3667,8188,3806,1841],()=>r(36404));module.exports=n})();