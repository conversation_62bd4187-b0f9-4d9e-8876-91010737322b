{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/agents/config/[agentId]", "regex": "^/agents/config/([^/]+?)(?:/)?$", "routeKeys": {"nxtPagentId": "nxtPagentId"}, "namedRegex": "^/agents/config/(?<nxtPagentId>[^/]+?)(?:/)?$"}, {"page": "/agents/config/[agentId]/workflow/[workflowId]", "regex": "^/agents/config/([^/]+?)/workflow/([^/]+?)(?:/)?$", "routeKeys": {"nxtPagentId": "nxtPagentId", "nxtPworkflowId": "nxtPworkflowId"}, "namedRegex": "^/agents/config/(?<nxtPagentId>[^/]+?)/workflow/(?<nxtPworkflowId>[^/]+?)(?:/)?$"}, {"page": "/agents/[threadId]", "regex": "^/agents/([^/]+?)(?:/)?$", "routeKeys": {"nxtPthreadId": "nxtPthreadId"}, "namedRegex": "^/agents/(?<nxtPthreadId>[^/]+?)(?:/)?$"}, {"page": "/api/integrations/[provider]/callback", "regex": "^/api/integrations/([^/]+?)/callback(?:/)?$", "routeKeys": {"nxtPprovider": "nxtPprovider"}, "namedRegex": "^/api/integrations/(?<nxtPprovider>[^/]+?)/callback(?:/)?$"}, {"page": "/api/webhooks/trigger/[workflowId]", "regex": "^/api/webhooks/trigger/([^/]+?)(?:/)?$", "routeKeys": {"nxtPworkflowId": "nxtPworkflowId"}, "namedRegex": "^/api/webhooks/trigger/(?<nxtPworkflowId>[^/]+?)(?:/)?$"}, {"page": "/projects/[projectId]/thread/[threadId]", "regex": "^/projects/([^/]+?)/thread/([^/]+?)(?:/)?$", "routeKeys": {"nxtPprojectId": "nxtPprojectId", "nxtPthreadId": "nxtPthreadId"}, "namedRegex": "^/projects/(?<nxtPprojectId>[^/]+?)/thread/(?<nxtPthreadId>[^/]+?)(?:/)?$"}, {"page": "/share/[threadId]", "regex": "^/share/([^/]+?)(?:/)?$", "routeKeys": {"nxtPthreadId": "nxtPthreadId"}, "namedRegex": "^/share/(?<nxtPthreadId>[^/]+?)(?:/)?$"}, {"page": "/[accountSlug]", "regex": "^/([^/]+?)(?:/)?$", "routeKeys": {"nxtPaccountSlug": "nxtPaccountSlug"}, "namedRegex": "^/(?<nxtPaccountSlug>[^/]+?)(?:/)?$"}, {"page": "/[accountSlug]/settings", "regex": "^/([^/]+?)/settings(?:/)?$", "routeKeys": {"nxtPaccountSlug": "nxtPaccountSlug"}, "namedRegex": "^/(?<nxtPaccountSlug>[^/]+?)/settings(?:/)?$"}, {"page": "/[accountSlug]/settings/billing", "regex": "^/([^/]+?)/settings/billing(?:/)?$", "routeKeys": {"nxtPaccountSlug": "nxtPaccountSlug"}, "namedRegex": "^/(?<nxtPaccountSlug>[^/]+?)/settings/billing(?:/)?$"}, {"page": "/[accountSlug]/settings/members", "regex": "^/([^/]+?)/settings/members(?:/)?$", "routeKeys": {"nxtPaccountSlug": "nxtPaccountSlug"}, "namedRegex": "^/(?<nxtPaccountSlug>[^/]+?)/settings/members(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/agents", "regex": "^/agents(?:/)?$", "routeKeys": {}, "namedRegex": "^/agents(?:/)?$"}, {"page": "/auth", "regex": "^/auth(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth(?:/)?$"}, {"page": "/auth/callback", "regex": "^/auth/callback(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/callback(?:/)?$"}, {"page": "/auth/github-popup", "regex": "^/auth/github\\-popup(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/github\\-popup(?:/)?$"}, {"page": "/auth/reset-password", "regex": "^/auth/reset\\-password(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/reset\\-password(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/invitation", "regex": "^/invitation(?:/)?$", "routeKeys": {}, "namedRegex": "^/invitation(?:/)?$"}, {"page": "/legal", "regex": "^/legal(?:/)?$", "routeKeys": {}, "namedRegex": "^/legal(?:/)?$"}, {"page": "/model-pricing", "regex": "^/model\\-pricing(?:/)?$", "routeKeys": {}, "namedRegex": "^/model\\-pricing(?:/)?$"}, {"page": "/monitoring", "regex": "^/monitoring(?:/)?$", "routeKeys": {}, "namedRegex": "^/monitoring(?:/)?$"}, {"page": "/opengraph-image", "regex": "^/opengraph\\-image(?:/)?$", "routeKeys": {}, "namedRegex": "^/opengraph\\-image(?:/)?$"}, {"page": "/settings", "regex": "^/settings(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings(?:/)?$"}, {"page": "/settings/billing", "regex": "^/settings/billing(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings/billing(?:/)?$"}, {"page": "/settings/credentials", "regex": "^/settings/credentials(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings/credentials(?:/)?$"}, {"page": "/settings/teams", "regex": "^/settings/teams(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings/teams(?:/)?$"}, {"page": "/settings/usage-logs", "regex": "^/settings/usage\\-logs(?:/)?$", "routeKeys": {}, "namedRegex": "^/settings/usage\\-logs(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}