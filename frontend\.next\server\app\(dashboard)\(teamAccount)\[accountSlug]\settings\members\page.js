(()=>{var e={};e.id=5601,e.ids=[5601],e.modules={36:(e,t,r)=>{"use strict";r.d(t,{U:()=>s});var a=r(6475);let s=(0,a.createServerReference)("7f8bed79c8654f95685745e61906af37f96a086236",a.callServer,void 0,a.findSourceMapURL,"createClient")},348:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var a=r(60687),s=r(43210),n=r.n(s),i=r(35950),o=r(85814),l=r.n(o),d=r(16189);function c({children:e,params:t}){let{accountSlug:r}=n().use(t),s=(0,d.usePathname)(),o=[{name:"Account",href:`/${r}/settings`},{name:"Members",href:`/${r}/settings/members`},{name:"Bill<PERSON>",href:`/${r}/settings/billing`}];return(0,a.jsxs)("div",{className:"space-y-6 w-full",children:[(0,a.jsx)(i.w,{className:"border-subtle dark:border-white/10"}),(0,a.jsxs)("div",{className:"flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0 w-full max-w-6xl mx-auto px-4",children:[(0,a.jsx)("aside",{className:"lg:w-1/4 p-1",children:(0,a.jsx)("nav",{className:"flex flex-col space-y-1",children:o.map(e=>(0,a.jsx)(l(),{href:e.href,className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${s===e.href?"bg-accent text-accent-foreground":"text-muted-foreground hover:bg-accent/50 hover:text-accent-foreground"}`,children:e.name},e.href))})}),(0,a.jsx)("div",{className:"flex-1 bg-card-bg dark:bg-background-secondary p-6 rounded-2xl border border-subtle dark:border-white/10 shadow-custom",children:e})]})]})}},2990:(e,t,r)=>{Promise.resolve().then(r.bind(r,348))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},4690:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\(teamAccount)\\\\[accountSlug]\\\\settings\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\settings\\layout.tsx","default")},5321:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\(teamAccount)\\\\[accountSlug]\\\\settings\\\\members\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\settings\\members\\page.tsx","default")},6211:(e,t,r)=>{"use strict";r.d(t,{A0:()=>i,Table:()=>n,TableBody:()=>o,TableCell:()=>c,TableRow:()=>l,nd:()=>d});var a=r(60687);r(43210);var s=r(4780);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm",e),...t})})}function i({className:e,...t}){return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",e),...t})}function o({className:e,...t}){return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function d({className:e,...t}){return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>u,eb:()=>h,gC:()=>m,l6:()=>d,yv:()=>c});var a=r(60687);r(43210);var s=r(22670),n=r(78272),i=r(13964),o=r(3589),l=r(4780);function d({...e}){return(0,a.jsx)(s.bL,{"data-slot":"select",...e})}function c({...e}){return(0,a.jsx)(s.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:r,...i}){return(0,a.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":t,className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i,children:[r,(0,a.jsx)(s.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function m({className:e,children:t,position:r="popper",...n}){return(0,a.jsx)(s.ZL,{children:(0,a.jsxs)(s.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...n,children:[(0,a.jsx)(x,{}),(0,a.jsx)(s.LM,{className:(0,l.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(f,{})]})})}function h({className:e,children:t,...r}){return(0,a.jsxs)(s.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(s.VF,{children:(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsx)(s.p4,{children:t})]})}function x({className:e,...t}){return(0,a.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(o.A,{className:"size-4"})})}function f({className:e,...t}){return(0,a.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(n.A,{className:"size-4"})})}},16902:(e,t,r)=>{Promise.resolve().then(r.bind(r,4690))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25836:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>V});var a=r(60687),s=r(43210),n=r.n(s),i=r(36),o=r(6211),l=r(96834),d=r(93661),c=r(21342),u=r(29523),m=r(63503),h=r(44774),x=r(80013),f=r(15079),b=r(6475);let p=(0,b.createServerReference)("60a2413f5458bf68ab3375ffb31a2aaeeca9c6e834",b.callServer,void 0,b.findSourceMapURL,"updateTeamMemberRole");var g=r(82978),v=r(16189);let j=[{label:"Owner",value:"owner"},{label:"Member",value:"member"}];function y({accountId:e,teamMember:t,isPrimaryOwner:r}){let[n,i]=(0,s.useState)(t.account_role),o=(0,v.usePathname)();return(0,a.jsxs)("form",{className:"animate-in flex-1 flex flex-col w-full justify-center gap-y-6 text-foreground",children:[(0,a.jsx)("input",{type:"hidden",name:"accountId",value:e}),(0,a.jsx)("input",{type:"hidden",name:"userId",value:t.user_id}),(0,a.jsx)("input",{type:"hidden",name:"returnUrl",value:o}),(0,a.jsxs)("div",{className:"flex flex-col gap-y-2",children:[(0,a.jsx)(x.Label,{htmlFor:"accountRole",children:"Team Role"}),(0,a.jsxs)(f.l6,{value:n,onValueChange:i,name:"accountRole",children:[(0,a.jsx)(f.bq,{children:(0,a.jsx)(f.yv,{placeholder:"Member type"})}),(0,a.jsx)(f.gC,{children:j.map(e=>(0,a.jsx)(f.eb,{value:e.value,children:e.label},e.value))})]})]}),"owner"===n&&r&&(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(g.S,{id:"makePrimaryOwner",name:"makePrimaryOwner"}),(0,a.jsx)("label",{htmlFor:"makePrimaryOwner",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Make this user the primary owner"})]}),(0,a.jsx)(h.SubmitButton,{formAction:p,pendingText:"Updating...",children:"Update Role"})]})}let w=(0,b.createServerReference)("60f4ca11706210f990045d3b7082fd1eb8b74af14c",b.callServer,void 0,b.findSourceMapURL,"removeTeamMember");function k({accountId:e,teamMember:t}){let r=(0,v.usePathname)();return(0,a.jsxs)("form",{className:"animate-in flex-1 flex flex-col w-full justify-center gap-y-6 text-foreground",children:[(0,a.jsx)("input",{type:"hidden",name:"accountId",value:e}),(0,a.jsx)("input",{type:"hidden",name:"userId",value:t.user_id}),(0,a.jsx)("input",{type:"hidden",name:"returnUrl",value:r}),(0,a.jsx)(h.SubmitButton,{variant:"destructive",formAction:w,pendingText:"Removing...",children:"Remove member"})]})}function N({teamMember:e,accountId:t,isPrimaryOwner:r}){let[n,i]=(0,s.useState)(!1),[o,l]=(0,s.useState)(!1);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(c.rI,{children:[(0,a.jsx)(c.ty,{asChild:!0,children:(0,a.jsxs)(u.$,{variant:"ghost",className:"h-8 w-8 p-0 rounded-full hover:bg-hover-bg dark:hover:bg-hover-bg-dark",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 text-foreground/70"}),(0,a.jsx)("span",{className:"sr-only",children:"Open menu"})]})}),(0,a.jsxs)(c.SQ,{className:"min-w-[160px] border-subtle dark:border-white/10 bg-card-bg dark:bg-background-secondary rounded-xl shadow-custom",children:[(0,a.jsx)(c._2,{onSelect:()=>i(!0),className:"rounded-md hover:bg-hover-bg cursor-pointer text-foreground/90",children:"Change role"}),(0,a.jsx)(c._2,{onSelect:()=>l(!0),className:"rounded-md hover:bg-hover-bg cursor-pointer text-red-500 dark:text-red-400",children:"Remove member"})]})]}),(0,a.jsx)(m.lG,{open:n,onOpenChange:i,children:(0,a.jsxs)(m.Cf,{className:"sm:max-w-[425px] border-subtle dark:border-white/10 bg-card-bg dark:bg-background-secondary rounded-2xl shadow-custom",children:[(0,a.jsxs)(m.c7,{children:[(0,a.jsx)(m.L3,{className:"text-card-title",children:"Update team member role"}),(0,a.jsx)(m.rr,{className:"text-foreground/70",children:"Update a member's role in your team"})]}),(0,a.jsx)(y,{teamMember:e,accountId:t,isPrimaryOwner:r})]})}),(0,a.jsx)(m.lG,{open:o,onOpenChange:l,children:(0,a.jsxs)(m.Cf,{className:"sm:max-w-[425px] border-subtle dark:border-white/10 bg-card-bg dark:bg-background-secondary rounded-2xl shadow-custom",children:[(0,a.jsxs)(m.c7,{children:[(0,a.jsx)(m.L3,{className:"text-card-title",children:"Remove team member"}),(0,a.jsx)(m.rr,{className:"text-foreground/70",children:"Are you sure you want to remove this user from the team?"})]}),(0,a.jsx)(k,{teamMember:e,accountId:t})]})})]})}async function _({accountId:e}){let t=await (0,i.U)(),{data:r}=await t.rpc("get_account_members",{account_id:e}),{data:s}=await t.auth.getUser(),n=r?.find(e=>e.user_id===s?.user?.id)?.is_primary_owner;return(0,a.jsx)("div",{children:(0,a.jsx)(o.Table,{children:(0,a.jsx)(o.TableBody,{children:r?.map(t=>(0,a.jsxs)(o.TableRow,{className:"hover:bg-hover-bg border-subtle dark:border-white/10",children:[(0,a.jsx)(o.TableCell,{children:(0,a.jsxs)("div",{className:"flex items-center gap-x-2",children:[(0,a.jsx)("span",{className:"font-medium text-card-title",children:t.name}),(0,a.jsx)(l.E,{variant:"owner"===t.account_role?"default":"outline",className:"owner"===t.account_role?"bg-primary hover:bg-primary/90":"text-foreground/70 border-subtle dark:border-white/10",children:t.is_primary_owner?"Primary Owner":t.account_role})]})}),(0,a.jsx)(o.TableCell,{children:(0,a.jsx)("span",{className:"text-sm text-foreground/70",children:t.email})}),(0,a.jsx)(o.TableCell,{className:"text-right",children:!t.is_primary_owner&&(0,a.jsx)(N,{teamMember:t,accountId:e,isPrimaryOwner:n})})]},t.user_id))})})})}var C=r(44493);let S=(0,b.createServerReference)("602cb633238b6ed821154b0de7624fe865fdf4a20d",b.callServer,void 0,b.findSourceMapURL,"createInvitation");var M=r(51215);let P=[{label:"24 Hour",value:"24_hour"},{label:"One time use",value:"one_time"}],T=[{label:"Owner",value:"owner"},{label:"Member",value:"member"}],R={message:"",token:""};function A({accountId:e}){var t;let[r,s]=(0,M.useFormState)(S,R);return(0,a.jsx)("form",{className:"animate-in flex-1 flex flex-col w-full justify-center gap-y-6 text-foreground",children:r?.token?(0,a.jsx)("div",{className:"text-sm",children:(t=r.token,`http://localhost:3000/invitation?token=${t}`)}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("input",{type:"hidden",name:"accountId",value:e}),(0,a.jsxs)("div",{className:"flex flex-col gap-y-2",children:[(0,a.jsx)(x.Label,{htmlFor:"invitationType",children:"Invitation Type"}),(0,a.jsxs)(f.l6,{defaultValue:"one_time",name:"invitationType",children:[(0,a.jsx)(f.bq,{children:(0,a.jsx)(f.yv,{placeholder:"Invitation type"})}),(0,a.jsx)(f.gC,{children:P.map(e=>(0,a.jsx)(f.eb,{value:e.value,children:e.label},e.value))})]})]}),(0,a.jsxs)("div",{className:"flex flex-col gap-y-2",children:[(0,a.jsx)(x.Label,{htmlFor:"accountRole",children:"Team Role"}),(0,a.jsxs)(f.l6,{defaultValue:"member",name:"accountRole",children:[(0,a.jsx)(f.bq,{children:(0,a.jsx)(f.yv,{placeholder:"Member type"})}),(0,a.jsx)(f.gC,{children:T.map(e=>(0,a.jsx)(f.eb,{value:e.value,children:e.label},e.value))})]})]}),(0,a.jsx)(h.SubmitButton,{formAction:async(e,t)=>s(t),errorMessage:r?.message,pendingText:"Creating...",children:"Create invitation"})]})})}function I({accountId:e}){return(0,a.jsxs)(m.lG,{children:[(0,a.jsx)(m.zM,{asChild:!0,children:(0,a.jsx)(u.$,{variant:"outline",className:"rounded-lg h-9 border-subtle dark:border-white/10 hover:bg-hover-bg dark:hover:bg-hover-bg-dark",children:"Invite Member"})}),(0,a.jsxs)(m.Cf,{className:"sm:max-w-[425px] border-subtle dark:border-white/10 bg-card-bg dark:bg-background-secondary rounded-2xl shadow-custom",children:[(0,a.jsxs)(m.c7,{children:[(0,a.jsx)(m.L3,{className:"text-card-title",children:"Invite Team Member"}),(0,a.jsx)(m.rr,{className:"text-foreground/70",children:"Send an email invitation to join your team"})]}),(0,a.jsx)(A,{accountId:e})]})]})}var U=r(35780),D=r(47138);function q(e,t){let r=(0,D.a)(e),a=(0,D.a)(t),s=r.getTime()-a.getTime();return s<0?-1:s>0?1:s}var z=r(11392),E=r(79186),L=r(46127),O=r(3211),X=r(9903),B=r(79943),G=r(96362);let F=(0,b.createServerReference)("60479f0ba93cf8cd5b7a4f15ad34fc5cac7652b700",b.callServer,void 0,b.findSourceMapURL,"deleteInvitation");function $({invitationId:e}){let[t,r]=(0,s.useState)(!1),n=(0,v.usePathname)();return(0,a.jsxs)(m.lG,{open:t,onOpenChange:r,children:[(0,a.jsx)(m.zM,{asChild:!0,children:(0,a.jsxs)(u.$,{variant:"ghost",className:"h-8 w-8 p-0 rounded-full hover:bg-hover-bg dark:hover:bg-hover-bg-dark",children:[(0,a.jsx)(G.A,{className:"text-red-500 dark:text-red-400 w-4 h-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Delete invitation"})]})}),(0,a.jsxs)(m.Cf,{className:"sm:max-w-[425px] border-subtle dark:border-white/10 bg-card-bg dark:bg-background-secondary rounded-2xl shadow-custom",children:[(0,a.jsxs)(m.c7,{children:[(0,a.jsx)(m.L3,{className:"text-card-title",children:"Delete Invitation"}),(0,a.jsx)(m.rr,{className:"text-foreground/70",children:"Are you sure you want to delete this invitation? This cannot be undone."})]}),(0,a.jsxs)("div",{className:"flex gap-2 justify-end mt-4",children:[(0,a.jsx)(u.$,{variant:"outline",onClick:()=>r(!1),className:"rounded-lg h-9 border-subtle dark:border-white/10 hover:bg-hover-bg dark:hover:bg-hover-bg-dark",children:"Cancel"}),(0,a.jsxs)("form",{children:[(0,a.jsx)("input",{type:"hidden",name:"invitationId",value:e}),(0,a.jsx)("input",{type:"hidden",name:"returnPath",value:n}),(0,a.jsx)(h.SubmitButton,{variant:"destructive",formAction:F,pendingText:"Deleting...",className:"rounded-lg h-9 bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700",children:"Delete"})]})]})]})]})}async function Z({accountId:e}){let t=await (0,i.U)(),{data:r}=await t.rpc("get_account_invitations",{account_id:e});return(0,a.jsxs)(C.Zp,{children:[(0,a.jsx)(C.aR,{children:(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(C.ZB,{children:"Pending Invitations"}),(0,a.jsx)(C.BT,{children:"These are the pending invitations for your team"})]}),(0,a.jsx)(I,{accountId:e})]})}),!!r?.length&&(0,a.jsx)(C.Wu,{children:(0,a.jsx)(o.Table,{children:(0,a.jsx)(o.TableBody,{children:r?.map(e=>{var t,r;return(0,a.jsxs)(o.TableRow,{children:[(0,a.jsx)(o.TableCell,{children:(0,a.jsxs)("div",{className:"flex gap-x-2",children:[(t=e.created_at,r={addSuffix:!0},function(e,t,r){let a,s,n,i=(0,X.q)(),o=r?.locale??i.locale??O.c,l=q(e,t);if(isNaN(l))throw RangeError("Invalid time value");let d=Object.assign({},r,{addSuffix:r?.addSuffix,comparison:l});l>0?(a=(0,D.a)(t),s=(0,D.a)(e)):(a=(0,D.a)(e),s=(0,D.a)(t));let c=function(e,t,r){var a;return(a=void 0,e=>{let t=(a?Math[a]:Math.trunc)(e);return 0===t?0:t})(((0,D.a)(e)-(0,D.a)(t))/1e3)}(s,a),u=Math.round((c-((0,B.G)(s)-(0,B.G)(a))/1e3)/60);if(u<2)if(r?.includeSeconds)if(c<5)return o.formatDistance("lessThanXSeconds",5,d);else if(c<10)return o.formatDistance("lessThanXSeconds",10,d);else if(c<20)return o.formatDistance("lessThanXSeconds",20,d);else if(c<40)return o.formatDistance("halfAMinute",0,d);else if(c<60)return o.formatDistance("lessThanXMinutes",1,d);else return o.formatDistance("xMinutes",1,d);else if(0===u)return o.formatDistance("lessThanXMinutes",1,d);else return o.formatDistance("xMinutes",u,d);if(u<45)return o.formatDistance("xMinutes",u,d);if(u<90)return o.formatDistance("aboutXHours",1,d);if(u<z.F6){let e=Math.round(u/60);return o.formatDistance("aboutXHours",e,d)}if(u<2520)return o.formatDistance("xDays",1,d);else if(u<z.Nw){let e=Math.round(u/z.F6);return o.formatDistance("xDays",e,d)}else if(u<2*z.Nw)return n=Math.round(u/z.Nw),o.formatDistance("aboutXMonths",n,d);if((n=function(e,t){let r,a=(0,D.a)(e),s=(0,D.a)(t),n=q(a,s),i=Math.abs((0,E.U)(a,s));if(i<1)r=0;else{1===a.getMonth()&&a.getDate()>27&&a.setDate(30),a.setMonth(a.getMonth()-n*i);let t=q(a,s)===-n;(function(e){let t=(0,D.a)(e);return+function(e){let t=(0,D.a)(e);return t.setHours(23,59,59,999),t}(t)==+(0,L.p)(t)})((0,D.a)(e))&&1===i&&1===q(e,s)&&(t=!1),r=n*(i-Number(t))}return 0===r?0:r}(s,a))<12){let e=Math.round(u/z.Nw);return o.formatDistance("xMonths",e,d)}{let e=n%12,t=Math.trunc(n/12);return e<3?o.formatDistance("aboutXYears",t,d):e<9?o.formatDistance("overXYears",t,d):o.formatDistance("almostXYears",t+1,d)}}(t,(0,U.w)(t,Date.now()),r)),(0,a.jsx)(l.E,{variant:"24_hour"===e.invitation_type?"default":"outline",children:e.invitation_type}),(0,a.jsx)(l.E,{variant:"owner"===e.account_role?"default":"outline",children:e.account_role})]})}),(0,a.jsx)(o.TableCell,{className:"text-right",children:(0,a.jsx)($,{invitationId:e.invitation_id})})]},e.invitation_id)})})})})]})}var H=r(91821);function V({params:e}){let{accountSlug:t}=n().use(e),[r,s]=n().useState(null),[i,o]=n().useState(!0),[l,d]=n().useState(null);return i?(0,a.jsx)("div",{children:"Loading..."}):l?(0,a.jsxs)(H.Fc,{variant:"destructive",className:"border-red-300 dark:border-red-800 rounded-xl",children:[(0,a.jsx)(H.XL,{children:"Error"}),(0,a.jsx)(H.TN,{children:l})]}):r&&"owner"===r.account_role?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-card-title",children:"Team Members"}),(0,a.jsx)("p",{className:"text-sm text-foreground/70",children:"Manage your team members and invitations."})]}),(0,a.jsxs)(C.Zp,{className:"border-subtle dark:border-white/10 bg-white dark:bg-background-secondary shadow-none",children:[(0,a.jsxs)(C.aR,{children:[(0,a.jsx)(C.ZB,{className:"text-base text-card-title",children:"Invitations"}),(0,a.jsx)(C.BT,{children:"Invite new members to your team."})]}),(0,a.jsx)(C.Wu,{children:(0,a.jsx)(Z,{accountId:r.account_id})})]}),(0,a.jsxs)(C.Zp,{className:"border-subtle dark:border-white/10 bg-white dark:bg-background-secondary shadow-none",children:[(0,a.jsxs)(C.aR,{children:[(0,a.jsx)(C.ZB,{className:"text-base text-card-title",children:"Members"}),(0,a.jsx)(C.BT,{children:"Manage existing team members."})]}),(0,a.jsx)(C.Wu,{children:(0,a.jsx)(_,{accountId:r.account_id})})]})]}):(0,a.jsxs)(H.Fc,{variant:"destructive",className:"border-red-300 dark:border-red-800 rounded-xl",children:[(0,a.jsx)(H.XL,{children:"Access Denied"}),(0,a.jsx)(H.TN,{children:"You do not have permission to access this page."})]})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33842:(e,t,r)=>{Promise.resolve().then(r.bind(r,5321))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63632:(e,t,r)=>{"use strict";r.d(t,{$b:()=>l,Op:()=>o,QA:()=>d});var a=r(91199);r(42087);var s=r(7944),n=r(5876),i=r(90141);async function o(e,t){let r=t.get("invitationType"),a=t.get("accountId"),i=t.get("accountRole"),o=await (0,n.U)(),{data:l,error:d}=await o.rpc("create_invitation",{account_id:a,invitation_type:r,account_role:i});return d?{message:d.message}:((0,s.revalidatePath)("/[accountSlug]/settings/members/page"),{token:l.token})}async function l(e,t){let r=t.get("invitationId"),a=t.get("returnPath"),s=await (0,n.U)(),{error:o}=await s.rpc("delete_invitation",{invitation_id:r});if(o)return{message:o.message};(0,i.redirect)(a)}async function d(e,t){let r=t.get("token"),a=await (0,n.U)(),{error:s,data:o}=await a.rpc("accept_invitation",{lookup_invitation_token:r});if(s)return{message:s.message};(0,i.redirect)(`/${o.slug}`)}(0,r(33331).D)([o,l,d]),(0,a.A)(o,"602cb633238b6ed821154b0de7624fe865fdf4a20d",null),(0,a.A)(l,"60479f0ba93cf8cd5b7a4f15ad34fc5cac7652b700",null),(0,a.A)(d,"60b56431385a17492afc62212a7d703c72e7e723ab",null)},70732:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>l});var a=r(65239),s=r(48088),n=r(31369),i=r(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let l={children:["",{children:["(dashboard)",{children:["(teamAccount)",{children:["[accountSlug]",{children:["settings",{children:["members",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5321)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\settings\\members\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,4690)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\settings\\layout.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,33532)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,88524))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,93595)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,88524))).default(e)],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\settings\\members\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(dashboard)/(teamAccount)/[accountSlug]/settings/members/page",pathname:"/[accountSlug]/settings/members",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80698:(e,t,r)=>{Promise.resolve().then(r.bind(r,25836))},81630:e=>{"use strict";e.exports=require("http")},82978:(e,t,r)=>{"use strict";r.d(t,{S:()=>T});var a=r(60687),s=r(43210),n=r(98599),i=r(11273),o=r(70569),l=r(65551),d=r(83721),c=r(18853),u=r(46059),m=r(14163),h="Checkbox",[x,f]=(0,i.A)(h),[b,p]=x(h);function g(e){let{__scopeCheckbox:t,checked:r,children:n,defaultChecked:i,disabled:o,form:d,name:c,onCheckedChange:u,required:m,value:x="on",internal_do_not_use_render:f}=e,[p,g]=(0,l.i)({prop:r,defaultProp:i??!1,onChange:u,caller:h}),[v,j]=s.useState(null),[y,w]=s.useState(null),k=s.useRef(!1),N=!v||!!d||!!v.closest("form"),_={checked:p,disabled:o,setChecked:g,control:v,setControl:j,name:c,form:d,value:x,hasConsumerStoppedPropagationRef:k,required:m,defaultChecked:!C(i)&&i,isFormControl:N,bubbleInput:y,setBubbleInput:w};return(0,a.jsx)(b,{scope:t,..._,children:"function"==typeof f?f(_):n})}var v="CheckboxTrigger",j=s.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...i},l)=>{let{control:d,value:c,disabled:u,checked:h,required:x,setControl:f,setChecked:b,hasConsumerStoppedPropagationRef:g,isFormControl:j,bubbleInput:y}=p(v,e),w=(0,n.s)(l,f),k=s.useRef(h);return s.useEffect(()=>{let e=d?.form;if(e){let t=()=>b(k.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[d,b]),(0,a.jsx)(m.sG.button,{type:"button",role:"checkbox","aria-checked":C(h)?"mixed":h,"aria-required":x,"data-state":S(h),"data-disabled":u?"":void 0,disabled:u,value:c,...i,ref:w,onKeyDown:(0,o.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,o.m)(r,e=>{b(e=>!!C(e)||!e),y&&j&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})})});j.displayName=v;var y=s.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:s,checked:n,defaultChecked:i,required:o,disabled:l,value:d,onCheckedChange:c,form:u,...m}=e;return(0,a.jsx)(g,{__scopeCheckbox:r,checked:n,defaultChecked:i,disabled:l,required:o,onCheckedChange:c,name:s,form:u,value:d,internal_do_not_use_render:({isFormControl:e})=>(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(j,{...m,ref:t,__scopeCheckbox:r}),e&&(0,a.jsx)(_,{__scopeCheckbox:r})]})})});y.displayName=h;var w="CheckboxIndicator",k=s.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:s,...n}=e,i=p(w,r);return(0,a.jsx)(u.C,{present:s||C(i.checked)||!0===i.checked,children:(0,a.jsx)(m.sG.span,{"data-state":S(i.checked),"data-disabled":i.disabled?"":void 0,...n,ref:t,style:{pointerEvents:"none",...e.style}})})});k.displayName=w;var N="CheckboxBubbleInput",_=s.forwardRef(({__scopeCheckbox:e,...t},r)=>{let{control:i,hasConsumerStoppedPropagationRef:o,checked:l,defaultChecked:u,required:h,disabled:x,name:f,value:b,form:g,bubbleInput:v,setBubbleInput:j}=p(N,e),y=(0,n.s)(r,j),w=(0,d.Z)(l),k=(0,c.X)(i);s.useEffect(()=>{if(!v)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!o.current;if(w!==l&&e){let r=new Event("click",{bubbles:t});v.indeterminate=C(l),e.call(v,!C(l)&&l),v.dispatchEvent(r)}},[v,w,l,o]);let _=s.useRef(!C(l)&&l);return(0,a.jsx)(m.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:u??_.current,required:h,disabled:x,name:f,value:b,form:g,...t,tabIndex:-1,ref:y,style:{...t.style,...k,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function C(e){return"indeterminate"===e}function S(e){return C(e)?"indeterminate":e?"checked":"unchecked"}_.displayName=N;var M=r(13964),P=r(4780);function T({className:e,...t}){return(0,a.jsx)(y,{"data-slot":"checkbox",className:(0,P.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,a.jsx)(k,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,a.jsx)(M.A,{className:"size-3.5"})})})}},84297:e=>{"use strict";e.exports=require("async_hooks")},90435:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"60120624b62a67d0046abca062aae687cbb5ebc44a":()=>a.vI,"602190608cb4aada86562e5e5997e6bb44fbe95e4e":()=>a.$w,"602cb633238b6ed821154b0de7624fe865fdf4a20d":()=>d.Op,"60479f0ba93cf8cd5b7a4f15ad34fc5cac7652b700":()=>d.$b,"60836a64bf90333e8dead87703a0c5a32e95fa0f8f":()=>a.gj,"60a2413f5458bf68ab3375ffb31a2aaeeca9c6e834":()=>l,"60b56431385a17492afc62212a7d703c72e7e723ab":()=>d.QA,"60f4ca11706210f990045d3b7082fd1eb8b74af14c":()=>o,"7f8bed79c8654f95685745e61906af37f96a086236":()=>s.U});var a=r(67834),s=r(5876),n=r(91199);r(42087);var i=r(90141);async function o(e,t){let r=t.get("userId"),a=t.get("accountId"),n=t.get("returnUrl"),o=await (0,s.U)(),{error:l}=await o.rpc("remove_account_member",{user_id:r,account_id:a});if(l)return{message:l.message};(0,i.redirect)(n)}async function l(e,t){let r=t.get("userId"),a=t.get("accountId"),n=t.get("accountRole"),o=t.get("returnUrl"),l=t.get("makePrimaryOwner"),d=await (0,s.U)(),{error:c}=await d.rpc("update_account_user_role",{user_id:r,account_id:a,new_account_role:n,make_primary_owner:l});if(c)return{message:c.message};(0,i.redirect)(o)}(0,r(33331).D)([o,l]),(0,n.A)(o,"60f4ca11706210f990045d3b7082fd1eb8b74af14c",null),(0,n.A)(l,"60a2413f5458bf68ab3375ffb31a2aaeeca9c6e834",null);var d=r(63632)},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[7719,5193,4267,7096,1265,3530,7156,7976,4097,9781,7944,3667,8188,3806,1841],()=>r(70732));module.exports=a})();