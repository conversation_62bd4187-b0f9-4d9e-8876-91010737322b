"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5653],{1673:(t,e,i)=>{i.d(e,{po:()=>r,tn:()=>a,yT:()=>o});var s=i(14916),n=i(82423);let r=t=>1-Math.sin(Math.acos(t)),o=(0,n.G)(r),a=(0,s.V)(r)},3126:(t,e,i)=>{i.d(e,{S:()=>s});let s=t=>!!(t&&t.getVelocity)},7471:(t,e,i)=>{i.d(e,{Q:()=>s});let s=(0,i(12115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},12189:(t,e,i)=>{i.d(e,{q:()=>s});let s={layout:0,mainThread:0,waapi:0}},14905:(t,e,i)=>{i.d(e,{xQ:()=>r});var s=i(12115),n=i(50430);function r(t=!0){let e=(0,s.useContext)(n.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:o,register:a}=e,l=(0,s.useId)();(0,s.useEffect)(()=>{if(t)return a(l)},[t]);let h=(0,s.useCallback)(()=>t&&o&&o(l),[l,o,t]);return!i&&o?[!1,h]:[!0]}},14916:(t,e,i)=>{i.d(e,{V:()=>s});let s=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2},25958:(t,e,i)=>{i.d(e,{i:()=>x});var s=i(38154),n=i(91762),r=i(27470);let o=(0,r.A)(.42,0,1,1),a=(0,r.A)(0,0,.58,1),l=(0,r.A)(.42,0,.58,1),h=t=>Array.isArray(t)&&"number"!=typeof t[0];var u=i(94449),d=i(21116),c=i(94344),p=i(41765),m=i(1673),f=i(99388);let y={linear:d.l,easeIn:o,easeInOut:l,easeOut:a,circIn:m.po,circInOut:m.tn,circOut:m.yT,backIn:p.dg,backInOut:p.ZZ,backOut:p.Sz,anticipate:c.b},v=t=>"string"==typeof t,g=t=>{if((0,f.D)(t)){(0,u.V)(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,s,n]=t;return(0,r.A)(e,i,s,n)}return v(t)?((0,u.V)(void 0!==y[t],`Invalid easing type '${t}'`),y[t]):t};function x({duration:t=300,keyframes:e,times:i,ease:r="easeInOut"}){var o;let a=h(r)?r.map(g):g(r),u={done:!1,value:e[0]},d=(o=i&&i.length===e.length?i:(0,n.Z)(e),o.map(e=>e*t)),c=(0,s.G)(d,e,{ease:Array.isArray(a)?a:e.map(()=>a||l).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(u.value=c(e),u.done=e>=t,u)}}},27470:(t,e,i)=>{i.d(e,{A:()=>r});var s=i(21116);let n=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function r(t,e,i,r){if(t===e&&i===r)return s.l;let o=e=>(function(t,e,i,s,r){let o,a,l=0;do(o=n(a=e+(i-e)/2,s,r)-t)>0?i=a:e=a;while(Math.abs(o)>1e-7&&++l<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:n(o(t),e,r)}},27821:(t,e,i)=>{i.d(e,{o:()=>m});var s=i(91986),n=i(97393),r=i(75538),o=i(95396);let a={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};var l=i(94449),h=i(98167);function u(t,e){return t*Math.sqrt(1-e*e)}let d=["duration","bounce"],c=["stiffness","damping","mass"];function p(t,e){return e.some(e=>void 0!==t[e])}function m(t=a.visualDuration,e=a.bounce){let i,f="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:y,restDelta:v}=f,g=f.keyframes[0],x=f.keyframes[f.keyframes.length-1],T={done:!1,value:g},{stiffness:w,damping:S,mass:P,duration:b,velocity:A,isResolvedFromDuration:V}=function(t){let e={velocity:a.velocity,stiffness:a.stiffness,damping:a.damping,mass:a.mass,isResolvedFromDuration:!1,...t};if(!p(t,c)&&p(t,d))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),s=i*i,n=2*(0,h.q)(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:a.mass,stiffness:s,damping:n}}else{let i=function({duration:t=a.duration,bounce:e=a.bounce,velocity:i=a.velocity,mass:s=a.mass}){let n,o;(0,l.$)(t<=(0,r.f)(a.maxDuration),"Spring duration must be 10 seconds or less");let d=1-e;d=(0,h.q)(a.minDamping,a.maxDamping,d),t=(0,h.q)(a.minDuration,a.maxDuration,(0,r.X)(t)),d<1?(n=e=>{let s=e*d,n=s*t;return .001-(s-i)/u(e,d)*Math.exp(-n)},o=e=>{let s=e*d*t,r=Math.pow(d,2)*Math.pow(e,2)*t,o=Math.exp(-s),a=u(Math.pow(e,2),d);return(s*i+i-r)*o*(-n(e)+.001>0?-1:1)/a}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),o=e=>t*t*(i-e)*Math.exp(-e*t));let c=function(t,e,i){let s=i;for(let i=1;i<12;i++)s-=t(s)/e(s);return s}(n,o,5/t);if(t=(0,r.f)(t),isNaN(c))return{stiffness:a.stiffness,damping:a.damping,duration:t};{let e=Math.pow(c,2)*s;return{stiffness:e,damping:2*d*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:a.mass}).isResolvedFromDuration=!0}return e}({...f,velocity:-(0,r.X)(f.velocity||0)}),M=A||0,k=S/(2*Math.sqrt(w*P)),E=x-g,D=(0,r.X)(Math.sqrt(w/P)),C=5>Math.abs(E);if(y||(y=C?a.restSpeed.granular:a.restSpeed.default),v||(v=C?a.restDelta.granular:a.restDelta.default),k<1){let t=u(D,k);i=e=>x-Math.exp(-k*D*e)*((M+k*D*E)/t*Math.sin(t*e)+E*Math.cos(t*e))}else if(1===k)i=t=>x-Math.exp(-D*t)*(E+(M+D*E)*t);else{let t=D*Math.sqrt(k*k-1);i=e=>{let i=Math.exp(-k*D*e),s=Math.min(t*e,300);return x-i*((M+k*D*E)*Math.sinh(s)+t*E*Math.cosh(s))/t}}let R={calculatedDuration:V&&b||null,next:t=>{let e=i(t);if(V)T.done=t>=b;else{let s=0===t?M:0;k<1&&(s=0===t?(0,r.f)(M):(0,o.Y)(i,t,e));let n=Math.abs(x-e)<=v;T.done=Math.abs(s)<=y&&n}return T.value=T.done?x:e,T},toString:()=>{let t=Math.min((0,n.t)(R),n.Y),e=(0,s.K)(e=>R.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return R}m.applyToOptions=t=>{let e=function(t,e=100,i){let s=i({...t,keyframes:[0,e]}),o=Math.min((0,n.t)(s),n.Y);return{type:"keyframes",ease:t=>s.next(o*t).value/e,duration:(0,r.X)(o)}}(t,100,m);return t.ease=e.ease,t.duration=(0,r.f)(e.duration),t.type="keyframes",t}},41192:(t,e,i)=>{i.d(e,{s:()=>g});var s=i(58840),n=i(12189),r=i(31324),o=i(46182);let a=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>o.Gt.update(e,t),stop:()=>(0,o.WG)(e),now:()=>o.uv.isProcessing?o.uv.timestamp:s.k.now()}};var l=i(86201),h=i(25958),u=i(97393),d=i(85671),c=i(66569),p=i(92959),m=i(49792),f=i(75538),y=i(98167);let v=t=>t/100;class g extends p.q{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==s.k.now()&&this.tick(s.k.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},n.q.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;(0,c.E)(t);let{type:e=h.i,repeat:i=0,repeatDelay:s=0,repeatType:n,velocity:o=0}=t,{keyframes:a}=t,l=e||h.i;l!==h.i&&"number"!=typeof a[0]&&(this.mixKeyframes=(0,m.F)(v,(0,r.j)(a[0],a[1])),a=[0,100]);let d=l({...t,keyframes:a});"mirror"===n&&(this.mirroredGenerator=l({...t,keyframes:[...a].reverse(),velocity:-o})),null===d.calculatedDuration&&(d.calculatedDuration=(0,u.t)(d));let{calculatedDuration:p}=d;this.calculatedDuration=p,this.resolvedDuration=p+s,this.totalDuration=this.resolvedDuration*(i+1)-s,this.generator=d}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:s,mixKeyframes:n,mirroredGenerator:r,resolvedDuration:o,calculatedDuration:a}=this;if(null===this.startTime)return i.next(0);let{delay:h=0,keyframes:u,repeat:c,repeatType:p,repeatDelay:m,type:f,onUpdate:v,finalKeyframe:g}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-s/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let x=this.currentTime-h*(this.playbackSpeed>=0?1:-1),T=this.playbackSpeed>=0?x<0:x>s;this.currentTime=Math.max(x,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=s);let w=this.currentTime,S=i;if(c){let t=Math.min(this.currentTime,s)/o,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,c+1))%2&&("reverse"===p?(i=1-i,m&&(i-=m/o)):"mirror"===p&&(S=r)),w=(0,y.q)(0,1,i)*o}let P=T?{done:!1,value:u[0]}:S.next(w);n&&(P.value=n(P.value));let{done:b}=P;T||null===a||(b=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);let A=null===this.holdTime&&("finished"===this.state||"running"===this.state&&b);return A&&f!==l.B&&(P.value=(0,d.X)(u,this.options,g,this.speed)),v&&v(P.value),A&&this.finish(),P}then(t,e){return this.finished.then(t,e)}get duration(){return(0,f.X)(this.calculatedDuration)}get time(){return(0,f.X)(this.currentTime)}set time(t){t=(0,f.f)(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(s.k.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=(0,f.X)(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=a,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(s.k.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,n.q.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}},41765:(t,e,i)=>{i.d(e,{Sz:()=>o,ZZ:()=>l,dg:()=>a});var s=i(27470),n=i(14916),r=i(82423);let o=(0,s.A)(.33,1.53,.69,.99),a=(0,r.G)(o),l=(0,n.V)(a)},50430:(t,e,i)=>{i.d(e,{t:()=>s});let s=(0,i(12115).createContext)(null)},57728:(t,e,i)=>{i.d(e,{L:()=>s});let s=(0,i(12115).createContext)({})},66569:(t,e,i)=>{i.d(e,{E:()=>a});var s=i(86201),n=i(25958),r=i(27821);let o={decay:s.B,inertia:s.B,tween:n.i,keyframes:n.i,spring:r.o};function a(t){"string"==typeof t.type&&(t.type=o[t.type])}},75538:(t,e,i)=>{i.d(e,{X:()=>n,f:()=>s});let s=t=>1e3*t,n=t=>t/1e3},82423:(t,e,i)=>{i.d(e,{G:()=>s});let s=t=>e=>1-t(1-e)},85671:(t,e,i)=>{i.d(e,{X:()=>n});let s=t=>null!==t;function n(t,{repeat:e,repeatType:i="loop"},r,o=1){let a=t.filter(s),l=o<0||e&&"loop"!==i&&e%2==1?0:a.length-1;return l&&void 0!==r?r:a[l]}},86201:(t,e,i)=>{i.d(e,{B:()=>r});var s=i(27821),n=i(95396);function r({keyframes:t,velocity:e=0,power:i=.8,timeConstant:r=325,bounceDamping:o=10,bounceStiffness:a=500,modifyTarget:l,min:h,max:u,restDelta:d=.5,restSpeed:c}){let p,m,f=t[0],y={done:!1,value:f},v=t=>void 0!==h&&t<h||void 0!==u&&t>u,g=t=>void 0===h?u:void 0===u||Math.abs(h-t)<Math.abs(u-t)?h:u,x=i*e,T=f+x,w=void 0===l?T:l(T);w!==T&&(x=w-f);let S=t=>-x*Math.exp(-t/r),P=t=>w+S(t),b=t=>{let e=S(t),i=P(t);y.done=Math.abs(e)<=d,y.value=y.done?w:i},A=t=>{v(y.value)&&(p=t,m=(0,s.o)({keyframes:[y.value,g(y.value)],velocity:(0,n.Y)(P,t,y.value),damping:o,stiffness:a,restDelta:d,restSpeed:c}))};return A(0),{calculatedDuration:null,next:t=>{let e=!1;return(m||void 0!==p||(e=!0,b(t),A(t)),void 0!==p&&t>=p)?m.next(t-p):(e||b(t),y)}}}},91986:(t,e,i)=>{i.d(e,{K:()=>s});let s=(t,e,i=10)=>{let s="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)s+=t(e/(n-1))+", ";return`linear(${s.substring(0,s.length-2)})`}},92959:(t,e,i)=>{i.d(e,{q:()=>s});class s{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}},94344:(t,e,i)=>{i.d(e,{b:()=>n});var s=i(41765);let n=t=>(t*=2)<1?.5*(0,s.dg)(t):.5*(2-Math.pow(2,-10*(t-1)))},95396:(t,e,i)=>{i.d(e,{Y:()=>n});var s=i(64530);function n(t,e,i){let n=Math.max(e-5,0);return(0,s.f)(i-t(n),e-n)}},95653:(t,e,i)=>{function s(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function n(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function r(t,e,i,s){if("function"==typeof e){let[r,o]=n(s);e=e(void 0!==i?i:t.custom,r,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,o]=n(s);e=e(void 0!==i?i:t.custom,r,o)}return e}function o(t,e,i){let s=t.getProps();return r(s,e,void 0!==i?i:s.custom,t)}i.d(e,{P:()=>no});let a=t=>Array.isArray(t);var l,h,u=i(99967),d=i(3126),c=i(2098);function p(t,e){let i=t.getValue("willChange");if((0,d.S)(i)&&i.add)return i.add(e);if(!i&&c.W.WillChange){let i=new c.W.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let m=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),f="data-"+m("framerAppearId"),y=t=>null!==t,v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],g=new Set(v),x={type:"spring",stiffness:500,damping:25,restSpeed:10},T=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),w={type:"keyframes",duration:.8},S={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},P=(t,{keyframes:e})=>e.length>2?w:g.has(t)?t.startsWith("scale")?T(e[1]):x:S;function b(t,e){return t?.[e]??t?.default??t}var A=i(75538),V=i(46182),M=i(41192),k=i(58840),E=i(85671);let D=t=>180*t/Math.PI,C=t=>j(D(Math.atan2(t[1],t[0]))),R={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:C,rotateZ:C,skewX:t=>D(Math.atan(t[1])),skewY:t=>D(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},j=t=>((t%=360)<0&&(t+=360),t),L=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),B=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),F={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:L,scaleY:B,scale:t=>(L(t)+B(t))/2,rotateX:t=>j(D(Math.atan2(t[6],t[5]))),rotateY:t=>j(D(Math.atan2(-t[2],t[0]))),rotateZ:C,rotate:C,skewX:t=>D(Math.atan(t[4])),skewY:t=>D(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function O(t){return+!!t.includes("scale")}function I(t,e){let i,s;if(!t||"none"===t)return O(e);let n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=F,s=n;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=R,s=e}if(!s)return O(e);let r=i[e],o=s[1].split(",").map(W);return"function"==typeof r?r(o):o[r]}let U=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return I(i,e)};function W(t){return parseFloat(t.trim())}var N=i(25590),$=i(70315);let X=t=>t===N.ai||t===$.px,G=new Set(["x","y","z"]),Y=v.filter(t=>!G.has(t)),q={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>I(e,"x"),y:(t,{transform:e})=>I(e,"y")};q.translateX=q.x,q.translateY=q.y;let K=new Set,z=!1,H=!1,Z=!1;function Q(){if(H){let t=Array.from(K).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return Y.forEach(i=>{let s=t.getValue(i);void 0!==s&&(e.push([i,s.get()]),s.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}H=!1,z=!1,K.forEach(t=>t.complete(Z)),K.clear()}function _(){K.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(H=!0)})}class J{constructor(t,e,i,s,n,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=s,this.element=n,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(K.add(this),z||(z=!0,V.Gt.read(_),V.Gt.resolveKeyframes(Q))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:s}=this;if(null===t[0]){let n=s?.get(),r=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let s=i.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===n&&s.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),K.delete(this)}cancel(){"scheduled"===this.state&&(K.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tt=t=>t.startsWith("--");var te=i(96299),ti=i(92959),ts=i(12189),tn=i(18579);let tr={};var to=i(57482);let ta=function(t,e){let i=(0,to.p)(t);return()=>tr[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing");var tl=i(91986);let th=([t,e,i,s])=>`cubic-bezier(${t}, ${e}, ${i}, ${s})`,tu={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:th([0,.65,.55,1]),circOut:th([.55,0,1,.45]),backIn:th([.31,.01,.66,-.59]),backOut:th([.33,1.53,.69,.99])};var td=i(99388);function tc(t){return"function"==typeof t&&"applyToOptions"in t}var tp=i(94449),tm=i(21116);class tf extends ti.q{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:s,pseudoElement:n,allowFlatten:r=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!n,this.allowFlatten=r,this.options=t,(0,tp.V)("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return tc(t)&&ta()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:s=0,duration:n=300,repeat:r=0,repeatType:o="loop",ease:a="easeOut",times:l}={},h){let u={[e]:i};l&&(u.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?ta()?(0,tl.K)(e,i):"ease-out":(0,td.D)(e)?th(e):Array.isArray(e)?e.map(e=>t(e,i)||tu.easeOut):tu[e]}(a,n);Array.isArray(d)&&(u.easing=d),tn.Q.value&&ts.q.waapi++;let c={delay:s,duration:n,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:r+1,direction:"reverse"===o?"alternate":"normal"};h&&(c.pseudoElement=h);let p=t.animate(u,c);return tn.Q.value&&p.finished.finally(()=>{ts.q.waapi--}),p}(e,i,s,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let t=(0,E.X)(s,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){tt(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){let t=this.animation.effect?.getComputedTiming?.().duration||0;return(0,A.X)(Number(t))}get time(){return(0,A.X)(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=(0,A.f)(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&(0,te.J)())?(this.animation.timeline=t,tm.l):e(this)}}var ty=i(66569),tv=i(94344),tg=i(41765),tx=i(1673);let tT={anticipate:tv.b,backInOut:tg.ZZ,circInOut:tx.tn};class tw extends tf{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in tT&&(t.ease=tT[t.ease])}(t),(0,ty.E)(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:s,element:n,...r}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let o=new M.s({...r,autoplay:!1}),a=(0,A.f)(this.finishedTime??this.time);e.setWithVelocity(o.sample(a-10).value,o.sample(a).value,10),o.stop()}}var tS=i(95233);let tP=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tS.f.test(t)||"0"===t)&&!t.startsWith("url("));var tb=i(22258);let tA=new Set(["opacity","clipPath","filter","transform"]),tV=(0,to.p)(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tM extends ti.q{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:s=0,repeatDelay:n=0,repeatType:r="loop",keyframes:o,name:a,motionValue:l,element:h,...u}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=k.k.now();let d={autoplay:t,delay:e,type:i,repeat:s,repeatDelay:n,repeatType:r,name:a,motionValue:l,element:h,...u},c=h?.KeyframeResolver||J;this.keyframeResolver=new c(o,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),a,l,h),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,s){this.keyframeResolver=void 0;let{name:n,type:r,velocity:o,delay:a,isHandoff:l,onUpdate:h}=i;this.resolvedAt=k.k.now(),!function(t,e,i,s){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],o=tP(n,e),a=tP(r,e);return(0,tp.$)(o===a,`You are trying to animate ${e} from "${n}" to "${r}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${r} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||tc(i))&&s)}(t,n,r,o)&&((c.W.instantAnimations||!a)&&h?.((0,E.X)(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let u={startTime:s?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},d=!l&&function(t){let{motionValue:e,name:i,repeatDelay:s,repeatType:n,damping:r,type:o}=t;if(!(0,tb.s)(e?.owner?.current))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return tV()&&i&&tA.has(i)&&("transform"!==i||!l)&&!a&&!s&&"mirror"!==n&&0!==r&&"inertia"!==o}(u)?new tw({...u,element:u.motionValue.owner.current}):new M.s(u);d.finished.then(()=>this.notifyFinished()).catch(tm.l),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),Z=!0,_(),Q(),Z=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let tk=(t,e,i,s={},n,r)=>o=>{let a=b(s,t)||{},l=a.delay||s.delay||0,{elapsed:h=0}=s;h-=(0,A.f)(l);let u={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-h,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:r?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:s,staggerDirection:n,repeat:r,repeatType:o,repeatDelay:a,from:l,elapsed:h,...u}){return!!Object.keys(u).length}(a)&&Object.assign(u,P(t,u)),u.duration&&(u.duration=(0,A.f)(u.duration)),u.repeatDelay&&(u.repeatDelay=(0,A.f)(u.repeatDelay)),void 0!==u.from&&(u.keyframes[0]=u.from);let d=!1;if(!1!==u.type&&(0!==u.duration||u.repeatDelay)||(u.duration=0,0===u.delay&&(d=!0)),(c.W.instantAnimations||c.W.skipAnimations)&&(d=!0,u.duration=0,u.delay=0),u.allowFlatten=!a.type&&!a.ease,d&&!r&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},s){let n=t.filter(y),r=e&&"loop"!==i&&e%2==1?0:n.length-1;return n[r]}(u.keyframes,a);if(void 0!==t)return void V.Gt.update(()=>{u.onUpdate(t),u.onComplete()})}return a.isSync?new M.s(u):new tM(u)},tE=new Set(["width","height","top","left","right","bottom",...v]);function tD(t,e,{delay:i=0,transitionOverride:s,type:n}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:l,...h}=e;s&&(r=s);let d=[],c=n&&t.animationState&&t.animationState.getState()[n];for(let e in h){let s=t.getValue(e,t.latestValues[e]??null),n=h[e];if(void 0===n||c&&function({protectedKeys:t,needsAnimating:e},i){let s=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,s}(c,e))continue;let o={delay:i,...b(r||{},e)},a=s.get();if(void 0!==a&&!s.isAnimating&&!Array.isArray(n)&&n===a&&!o.velocity)continue;let l=!1;if(window.MotionHandoffAnimation){let i=t.props[f];if(i){let t=window.MotionHandoffAnimation(i,e,V.Gt);null!==t&&(o.startTime=t,l=!0)}}p(t,e),s.start(tk(e,s,n,t.shouldReduceMotion&&tE.has(e)?{type:!1}:o,t,l));let u=s.animation;u&&d.push(u)}return l&&Promise.all(d).then(()=>{V.Gt.update(()=>{l&&function(t,e){let{transitionEnd:i={},transition:s={},...n}=o(t,e)||{};for(let e in n={...n,...i}){var r;let i=a(r=n[e])?r[r.length-1]||0:r;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,u.OQ)(i))}}(t,l)})}),d}function tC(t,e,i={}){let s=o(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:n=t.getDefaultTransition()||{}}=s||{};i.transitionOverride&&(n=i.transitionOverride);let r=s?()=>Promise.all(tD(t,s,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(s=0)=>{let{delayChildren:r=0,staggerChildren:o,staggerDirection:a}=n;return function(t,e,i=0,s=0,n=1,r){let o=[],a=(t.variantChildren.size-1)*s,l=1===n?(t=0)=>t*s:(t=0)=>a-t*s;return Array.from(t.variantChildren).sort(tR).forEach((t,s)=>{t.notify("AnimationStart",e),o.push(tC(t,e,{...r,delay:i+l(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,r+s,o,a,i)}:()=>Promise.resolve(),{when:l}=n;if(!l)return Promise.all([r(),a(i.delay)]);{let[t,e]="beforeChildren"===l?[r,a]:[a,r];return t().then(()=>e())}}function tR(t,e){return t.sortNodePosition(e)}function tj(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}function tL(t){return"string"==typeof t||Array.isArray(t)}let tB=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],tF=["initial",...tB],tO=tF.length,tI=[...tB].reverse(),tU=tB.length;function tW(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function tN(){return{animate:tW(!0),whileInView:tW(),whileHover:tW(),whileTap:tW(),whileDrag:tW(),whileFocus:tW(),exit:tW()}}class t${constructor(t){this.isMounted=!1,this.node=t}update(){}}class tX extends t${constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e))s=Promise.all(e.map(e=>tC(t,e,i)));else if("string"==typeof e)s=tC(t,e,i);else{let n="function"==typeof e?o(t,e,i.custom):e;s=Promise.all(tD(t,n,i))}return s.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=tN(),n=!0,r=e=>(i,s)=>{let n=o(t,s,"exit"===e?t.presenceContext?.custom:void 0);if(n){let{transition:t,transitionEnd:e,...s}=n;i={...i,...s,...e}}return i};function l(l){let{props:h}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<tO;t++){let s=tF[t],n=e.props[s];(tL(n)||!1===n)&&(i[s]=n)}return i}(t.parent)||{},d=[],c=new Set,p={},m=1/0;for(let e=0;e<tU;e++){var f,y;let o=tI[e],v=i[o],g=void 0!==h[o]?h[o]:u[o],x=tL(g),T=o===l?v.isActive:null;!1===T&&(m=e);let w=g===u[o]&&g!==h[o]&&x;if(w&&n&&t.manuallyAnimateOnMount&&(w=!1),v.protectedKeys={...p},!v.isActive&&null===T||!g&&!v.prevProp||s(g)||"boolean"==typeof g)continue;let S=(f=v.prevProp,"string"==typeof(y=g)?y!==f:!!Array.isArray(y)&&!tj(y,f)),P=S||o===l&&v.isActive&&!w&&x||e>m&&x,b=!1,A=Array.isArray(g)?g:[g],V=A.reduce(r(o),{});!1===T&&(V={});let{prevResolvedValues:M={}}=v,k={...M,...V},E=e=>{P=!0,c.has(e)&&(b=!0,c.delete(e)),v.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in k){let e=V[t],i=M[t];if(p.hasOwnProperty(t))continue;let s=!1;(a(e)&&a(i)?tj(e,i):e===i)?void 0!==e&&c.has(t)?E(t):v.protectedKeys[t]=!0:null!=e?E(t):c.add(t)}v.prevProp=g,v.prevResolvedValues=V,v.isActive&&(p={...p,...V}),n&&t.blockInitialAnimation&&(P=!1);let D=!(w&&S)||b;P&&D&&d.push(...A.map(t=>({animation:t,options:{type:o}})))}if(c.size){let e={};if("boolean"!=typeof h.initial){let i=o(t,Array.isArray(h.initial)?h.initial[0]:h.initial);i&&i.transition&&(e.transition=i.transition)}c.forEach(i=>{let s=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=s??null}),d.push({animation:e})}let v=!!d.length;return n&&(!1===h.initial||h.initial===h.animate)&&!t.manuallyAnimateOnMount&&(v=!1),n=!1,v?e(d):Promise.resolve()}return{animateChanges:l,setActive:function(e,s){if(i[e].isActive===s)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,s)),i[e].isActive=s;let n=l(e);for(let t in i)i[t].protectedKeys={};return n},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=tN(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();s(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let tG=0;class tY extends t${constructor(){super(...arguments),this.id=tG++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}function tq(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}let tK=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function tz(t){return{point:{x:t.pageX,y:t.pageY}}}let tH=t=>e=>tK(e)&&t(e,tz(e));function tZ(t,e,i,s){return tq(t,e,tH(i),s)}function tQ({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}var t_=i(50105);function tJ(t){return t.max-t.min}function t0(t,e,i,s=.5){t.origin=s,t.originPoint=(0,t_.k)(e.min,e.max,t.origin),t.scale=tJ(i)/tJ(e),t.translate=(0,t_.k)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function t1(t,e,i,s){t0(t.x,e.x,i.x,s?s.originX:void 0),t0(t.y,e.y,i.y,s?s.originY:void 0)}function t5(t,e,i){t.min=i.min+e.min,t.max=t.min+tJ(e)}function t9(t,e,i){t.min=e.min-i.min,t.max=t.min+tJ(e)}function t2(t,e,i){t9(t.x,e.x,i.x),t9(t.y,e.y,i.y)}let t3=()=>({translate:0,scale:1,origin:0,originPoint:0}),t4=()=>({x:t3(),y:t3()}),t6=()=>({min:0,max:0}),t8=()=>({x:t6(),y:t6()});function t7(t){return[t("x"),t("y")]}function et(t){return void 0===t||1===t}function ee({scale:t,scaleX:e,scaleY:i}){return!et(t)||!et(e)||!et(i)}function ei(t){return ee(t)||es(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function es(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function en(t,e,i,s,n){return void 0!==n&&(t=s+n*(t-s)),s+i*(t-s)+e}function er(t,e=0,i=1,s,n){t.min=en(t.min,e,i,s,n),t.max=en(t.max,e,i,s,n)}function eo(t,{x:e,y:i}){er(t.x,e.translate,e.scale,e.originPoint),er(t.y,i.translate,i.scale,i.originPoint)}function ea(t,e){t.min=t.min+e,t.max=t.max+e}function el(t,e,i,s,n=.5){let r=(0,t_.k)(t.min,t.max,n);er(t,e,i,r,s)}function eh(t,e){el(t.x,e.x,e.scaleX,e.scale,e.originX),el(t.y,e.y,e.scaleY,e.scale,e.originY)}function eu(t,e){return tQ(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}let ed=({current:t})=>t?t.ownerDocument.defaultView:null;function ec(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let ep=(t,e)=>Math.abs(t-e);var em=i(49792);class ef{constructor(t,e,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=eg(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(ep(t.x,e.x)**2+ep(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:s}=t,{timestamp:n}=V.uv;this.history.push({...s,timestamp:n});let{onStart:r,onMove:o}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=ey(e,this.transformPagePoint),V.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=eg("pointercancel"===t.type?this.lastMoveEventInfo:ey(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),s&&s(t,r)},!tK(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=s||window;let r=ey(tz(t),this.transformPagePoint),{point:o}=r,{timestamp:a}=V.uv;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,eg(r,this.history)),this.removeListeners=(0,em.F)(tZ(this.contextWindow,"pointermove",this.handlePointerMove),tZ(this.contextWindow,"pointerup",this.handlePointerUp),tZ(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,V.WG)(this.updatePoint)}}function ey(t,e){return e?{point:e(t.point)}:t}function ev(t,e){return{x:t.x-e.x,y:t.y-e.y}}function eg({point:t},e){return{point:t,delta:ev(t,ex(e)),offset:ev(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,n=ex(t);for(;i>=0&&(s=t[i],!(n.timestamp-s.timestamp>(0,A.f)(.1)));)i--;if(!s)return{x:0,y:0};let r=(0,A.X)(n.timestamp-s.timestamp);if(0===r)return{x:0,y:0};let o={x:(n.x-s.x)/r,y:(n.y-s.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function ex(t){return t[t.length-1]}var eT=i(38865),ew=i(98167);function eS(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function eP(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function eb(t,e,i){return{min:eA(t,e),max:eA(t,i)}}function eA(t,e){return"number"==typeof t?t:t[e]||0}let eV={x:!1,y:!1},eM=new WeakMap;class ek{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=t8(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new ef(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(tz(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:s,onDragStart:n}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(eV[t])return null;else return eV[t]=!0,()=>{eV[t]=!1};return eV.x||eV.y?null:(eV.x=eV.y=!0,()=>{eV.x=eV.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),t7(t=>{let e=this.getAxisMotionValue(t).get()||0;if($.KN.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=tJ(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&V.Gt.postRender(()=>n(t,e)),p(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:n,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>t7(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:ed(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=e;this.startAnimation(s);let{onDragEnd:n}=this.getProps();n&&V.Gt.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!eE(t,s,this.currentDirection))return;let n=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?(0,t_.k)(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?(0,t_.k)(i,t,s.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),n.set(r)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,s=this.constraints;t&&ec(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:s,right:n}){return{x:eS(t.x,i,n),y:eS(t.y,e,s)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:eb(t,"left","right"),y:eb(t,"top","bottom")}}(e),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&t7(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!ec(e))return!1;let s=e.current;(0,tp.V)(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let r=function(t,e,i){let s=eu(t,i),{scroll:n}=e;return n&&(ea(s.x,n.offset.x),ea(s.y,n.offset.y)),s}(s,n.root,this.visualElement.getTransformPagePoint()),o=(t=n.layout.layoutBox,{x:eP(t.x,r.x),y:eP(t.y,r.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=tQ(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:n,dragSnapToOrigin:r,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(t7(o=>{if(!eE(o,e,this.currentDirection))return;let l=a&&a[o]||{};r&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[o]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,h)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return p(this.visualElement,t),i.start(tk(t,i,0,e,this.visualElement,!1))}stopAnimation(){t7(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){t7(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){t7(e=>{let{drag:i}=this.getProps();if(!eE(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,n=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:r}=s.layout.layoutBox[e];n.set(t[e]-(0,t_.k)(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!ec(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};t7(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=tJ(t),n=tJ(e);return n>s?i=(0,eT.q)(e.min,e.max-s,t.min):s>n&&(i=(0,eT.q)(t.min,t.max-n,e.min)),(0,ew.q)(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),t7(e=>{if(!eE(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:r}=this.constraints[e];i.set((0,t_.k)(n,r,s[e]))})}addListeners(){if(!this.visualElement.current)return;eM.set(this.visualElement,this);let t=tZ(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();ec(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),V.Gt.read(e);let n=tq(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(t7(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),s(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:n=!1,dragElastic:r=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:n,dragElastic:r,dragMomentum:o}}}function eE(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class eD extends t${constructor(t){super(t),this.removeGroupControls=tm.l,this.removeListeners=tm.l,this.controls=new ek(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||tm.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let eC=t=>(e,i)=>{t&&V.Gt.postRender(()=>t(e,i))};class eR extends t${constructor(){super(...arguments),this.removePointerDownListener=tm.l}onPointerDown(t){this.session=new ef(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ed(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:eC(t),onStart:eC(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&V.Gt.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=tZ(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var ej=i(95155),eL=i(12115),eB=i(14905),eF=i(57728);let eO=(0,eL.createContext)({}),eI={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function eU(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let eW={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!$.px.test(t))return t;else t=parseFloat(t);let i=eU(t,e.target.x),s=eU(t,e.target.y);return`${i}% ${s}%`}};var eN=i(95255);let e$={},{schedule:eX}=(0,i(66911).I)(queueMicrotask,!1);class eG extends eL.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:n}=t;for(let t in eq)e$[t]=eq[t],(0,eN.j)(t)&&(e$[t].isCSSVariable=!0);n&&(e.group&&e.group.add(n),i&&i.register&&s&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),eI.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:n}=this.props,{projection:r}=i;return r&&(r.isPresent=n,s||t.layoutDependency!==e||void 0===e||t.isPresent!==n?r.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?r.promote():r.relegate()||V.Gt.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),eX.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function eY(t){let[e,i]=(0,eB.xQ)(),s=(0,eL.useContext)(eF.L);return(0,ej.jsx)(eG,{...t,layoutGroup:s,switchLayoutGroup:(0,eL.useContext)(eO),isPresent:e,safeToRemove:i})}let eq={borderRadius:{...eW,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:eW,borderTopRightRadius:eW,borderBottomLeftRadius:eW,borderBottomRightRadius:eW,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=tS.f.parse(t);if(s.length>5)return t;let n=tS.f.createTransformer(t),r=+("number"!=typeof s[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;s[0+r]/=o,s[1+r]/=a;let l=(0,t_.k)(o,a,.5);return"number"==typeof s[2+r]&&(s[2+r]/=l),"number"==typeof s[3+r]&&(s[3+r]/=l),n(s)}}},eK=(t,e)=>t.depth-e.depth;var ez=i(65649);class eH{constructor(){this.children=[],this.isDirty=!1}add(t){(0,ez.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,ez.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(eK),this.isDirty=!1,this.children.forEach(t)}}function eZ(t){return(0,d.S)(t)?t.get():t}let eQ=["TopLeft","TopRight","BottomLeft","BottomRight"],e_=eQ.length,eJ=t=>"string"==typeof t?parseFloat(t):t,e0=t=>"number"==typeof t||$.px.test(t);function e1(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let e5=e2(0,.5,tx.yT),e9=e2(.5,.95,tm.l);function e2(t,e,i){return s=>s<t?0:s>e?1:i((0,eT.q)(t,e,s))}function e3(t,e){t.min=e.min,t.max=e.max}function e4(t,e){e3(t.x,e.x),e3(t.y,e.y)}function e6(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function e8(t,e,i,s,n){return t-=e,t=s+1/i*(t-s),void 0!==n&&(t=s+1/n*(t-s)),t}function e7(t,e,[i,s,n],r,o){!function(t,e=0,i=1,s=.5,n,r=t,o=t){if($.KN.test(e)&&(e=parseFloat(e),e=(0,t_.k)(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=(0,t_.k)(r.min,r.max,s);t===r&&(a-=e),t.min=e8(t.min,e,i,a,n),t.max=e8(t.max,e,i,a,n)}(t,e[i],e[s],e[n],e.scale,r,o)}let it=["x","scaleX","originX"],ie=["y","scaleY","originY"];function ii(t,e,i,s){e7(t.x,e,it,i?i.x:void 0,s?s.x:void 0),e7(t.y,e,ie,i?i.y:void 0,s?s.y:void 0)}function is(t){return 0===t.translate&&1===t.scale}function ir(t){return is(t.x)&&is(t.y)}function io(t,e){return t.min===e.min&&t.max===e.max}function ia(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function il(t,e){return ia(t.x,e.x)&&ia(t.y,e.y)}function ih(t){return tJ(t.x)/tJ(t.y)}function iu(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class id{constructor(){this.members=[]}add(t){(0,ez.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,ez.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}var ic=i(1309),ip=i(83385);let im={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},iy=["","X","Y","Z"],iv={visibility:"hidden"},ig=0;function ix(t,e,i,s){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),s&&(s[t]=0))}function iT({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:n}){return class{constructor(t={},i=e?.()){this.id=ig++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,tn.Q.value&&(im.nodes=im.calculatedTargetDeltas=im.calculatedProjections=0),this.nodes.forEach(iP),this.nodes.forEach(iD),this.nodes.forEach(iC),this.nodes.forEach(ib),tn.Q.addProjectionMetrics&&tn.Q.addProjectionMetrics(im)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new eH)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new ic.v),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=(0,ip.x)(e)&&(!(0,ip.x)(e)||"svg"!==e.tagName),this.instance=e;let{layoutId:i,layout:s,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),t){let i,s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=k.k.now(),s=({timestamp:n})=>{let r=n-i;r>=250&&((0,V.WG)(s),t(r-e))};return V.Gt.setup(s,!0),()=>(0,V.WG)(s)}(s,250),eI.hasAnimatedSinceResize&&(eI.hasAnimatedSinceResize=!1,this.nodes.forEach(iE))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||n.getDefaultTransition()||iO,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=n.getProps(),l=!this.targetLayout||!il(this.targetLayout,s),h=!e&&i;if(this.options.layoutRoot||this.resumeFrom||h||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...b(r,"layout"),onPlay:o,onComplete:a};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,h)}else e||iE(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,V.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(iR),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=i.props[f];if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",V.Gt,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(iV);return}this.isUpdating||this.nodes.forEach(iM),this.isUpdating=!1,this.nodes.forEach(ik),this.nodes.forEach(iw),this.nodes.forEach(iS),this.clearAllSnapshots();let t=k.k.now();V.uv.delta=(0,ew.q)(0,1e3/60,t-V.uv.timestamp),V.uv.timestamp=t,V.uv.isProcessing=!0,V.PP.update.process(V.uv),V.PP.preRender.process(V.uv),V.PP.render.process(V.uv),V.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,eX.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(iA),this.sharedNodes.forEach(ij)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,V.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){V.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||tJ(this.snapshot.measuredBox.x)||tJ(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=t8(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!ir(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,r=s!==this.prevTransformTemplateValue;t&&this.instance&&(e||ei(this.latestValues)||r)&&(n(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),iW((e=s).x),iW(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return t8();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(i$))){let{scroll:t}=this.root;t&&(ea(e.x,t.offset.x),ea(e.y,t.offset.y))}return e}removeElementScroll(t){let e=t8();if(e4(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let s=this.path[i],{scroll:n,options:r}=s;s!==this.root&&n&&r.layoutScroll&&(n.wasRoot&&e4(e,t),ea(e.x,n.offset.x),ea(e.y,n.offset.y))}return e}applyTransform(t,e=!1){let i=t8();e4(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&eh(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),ei(s.latestValues)&&eh(i,s.latestValues)}return ei(this.latestValues)&&eh(i,this.latestValues),i}removeTransform(t){let e=t8();e4(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!ei(i.latestValues))continue;ee(i.latestValues)&&i.updateSnapshot();let s=t8();e4(s,i.measurePageBox()),ii(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return ei(this.latestValues)&&ii(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==V.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:s,layoutId:n}=this.options;if(this.layout&&(s||n)){if(this.resolvedRelativeTargetAt=V.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=t8(),this.relativeTargetOrigin=t8(),t2(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),e4(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=t8(),this.targetWithTransforms=t8()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var r,o,a;this.forceRelativeParentToResolveTarget(),r=this.target,o=this.relativeTarget,a=this.relativeParent.target,t5(r.x,o.x,a.x),t5(r.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):e4(this.target,this.layout.layoutBox),eo(this.target,this.targetDelta)):e4(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=t8(),this.relativeTargetOrigin=t8(),t2(this.relativeTargetOrigin,this.target,t.target),e4(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}tn.Q.value&&im.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||ee(this.parent.latestValues)||es(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===V.uv.timestamp&&(i=!1),i)return;let{layout:s,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(s||n))return;e4(this.layoutCorrected,this.layout.layoutBox);let r=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,s=!1){let n,r,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){r=(n=i[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(s&&n.options.layoutScroll&&n.scroll&&n!==n.root&&eh(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,eo(t,r)),s&&ei(n.latestValues)&&eh(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=t8());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(e6(this.prevProjectionDelta.x,this.projectionDelta.x),e6(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),t1(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===r&&this.treeScale.y===o&&iu(this.projectionDelta.x,this.prevProjectionDelta.x)&&iu(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),tn.Q.value&&im.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=t4(),this.projectionDelta=t4(),this.projectionDeltaWithTransform=t4()}setAnimationOrigin(t,e=!1){let i,s=this.snapshot,n=s?s.latestValues:{},r={...this.latestValues},o=t4();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=t8(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,d=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(iF));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(iL(o.x,t.x,s),iL(o.y,t.y,s),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,c,p,m,f,y;t2(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,y=s,iB(p.x,m.x,f.x,y),iB(p.y,m.y,f.y,y),i&&(h=this.relativeTarget,c=i,io(h.x,c.x)&&io(h.y,c.y))&&(this.isProjectionDirty=!1),i||(i=t8()),e4(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,s,n,r){n?(t.opacity=(0,t_.k)(0,i.opacity??1,e5(s)),t.opacityExit=(0,t_.k)(e.opacity??1,0,e9(s))):r&&(t.opacity=(0,t_.k)(e.opacity??1,i.opacity??1,s));for(let n=0;n<e_;n++){let r=`border${eQ[n]}Radius`,o=e1(e,r),a=e1(i,r);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||e0(o)===e0(a)?(t[r]=Math.max((0,t_.k)(eJ(o),eJ(a),s),0),($.KN.test(a)||$.KN.test(o))&&(t[r]+="%")):t[r]=a)}(e.rotate||i.rotate)&&(t.rotate=(0,t_.k)(e.rotate||0,i.rotate||0,s))}(r,n,this.latestValues,s,d,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&((0,V.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=V.Gt.update(()=>{eI.hasAnimatedSinceResize=!0,ts.q.layout++,this.motionValue||(this.motionValue=(0,u.OQ)(0)),this.currentAnimation=function(t,e,i){let s=(0,d.S)(t)?t:(0,u.OQ)(t);return s.start(tk("",s,e,i)),s.animation}(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{ts.q.layout--},onComplete:()=>{ts.q.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:n}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&iN(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||t8();let e=tJ(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=tJ(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}e4(e,i),eh(e,n),t1(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new id),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&ix("z",t,s,this.animationValues);for(let e=0;e<iy.length;e++)ix(`rotate${iy[e]}`,t,s,this.animationValues),ix(`skew${iy[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return iv;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=eZ(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=eZ(t?.pointerEvents)||""),this.hasProjected&&!ei(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let n=s.animationValues||s.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let s="",n=t.x.translate/e.x,r=t.y.translate/e.y,o=i?.z||0;if((n||r||o)&&(s=`translate3d(${n}px, ${r}px, ${o}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:r,skewX:o,skewY:a}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),n&&(s+=`rotateX(${n}deg) `),r&&(s+=`rotateY(${r}deg) `),o&&(s+=`skewX(${o}deg) `),a&&(s+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(s+=`scale(${a}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),i&&(e.transform=i(n,e.transform));let{x:r,y:o}=this.projectionDelta;for(let t in e.transformOrigin=`${100*r.origin}% ${100*o.origin}% 0`,s.animationValues?e.opacity=s===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:e.opacity=s===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,e$){if(void 0===n[t])continue;let{correct:i,applyTo:r,isCSSVariable:o}=e$[t],a="none"===e.transform?n[t]:i(n[t],s);if(r){let t=r.length;for(let i=0;i<t;i++)e[r[i]]=a}else o?this.options.visualElement.renderState.vars[t]=a:e[t]=a}return this.options.layoutId&&(e.pointerEvents=s===this?eZ(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(iV),this.root.sharedNodes.clear()}}}function iw(t){t.updateLayout()}function iS(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:s}=t.layout,{animationType:n}=t.options,r=e.source!==t.layout.source;"size"===n?t7(t=>{let s=r?e.measuredBox[t]:e.layoutBox[t],n=tJ(s);s.min=i[t].min,s.max=s.min+n}):iN(n,e.layoutBox,i)&&t7(s=>{let n=r?e.measuredBox[s]:e.layoutBox[s],o=tJ(i[s]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+o)});let o=t4();t1(o,i,e.layoutBox);let a=t4();r?t1(a,t.applyTransform(s,!0),e.measuredBox):t1(a,i,e.layoutBox);let l=!ir(o),h=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:n,layout:r}=s;if(n&&r){let o=t8();t2(o,e.layoutBox,n.layoutBox);let a=t8();t2(a,i,r.layoutBox),il(o,a)||(h=!0),s.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function iP(t){tn.Q.value&&im.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function ib(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function iA(t){t.clearSnapshot()}function iV(t){t.clearMeasurements()}function iM(t){t.isLayoutDirty=!1}function ik(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function iE(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function iD(t){t.resolveTargetDelta()}function iC(t){t.calcProjection()}function iR(t){t.resetSkewAndRotation()}function ij(t){t.removeLeadSnapshot()}function iL(t,e,i){t.translate=(0,t_.k)(e.translate,0,i),t.scale=(0,t_.k)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function iB(t,e,i,s){t.min=(0,t_.k)(e.min,i.min,s),t.max=(0,t_.k)(e.max,i.max,s)}function iF(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let iO={duration:.45,ease:[.4,0,.1,1]},iI=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),iU=iI("applewebkit/")&&!iI("chrome/")?Math.round:tm.l;function iW(t){t.min=iU(t.min),t.max=iU(t.max)}function iN(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(ih(e)-ih(i)))}function i$(t){return t!==t.root&&t.scroll?.wasRoot}let iX=iT({attachResizeListener:(t,e)=>tq(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),iG={current:void 0},iY=iT({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!iG.current){let t=new iX({});t.mount(window),t.setOptions({layoutScroll:!0}),iG.current=t}return iG.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});var iq=i(80793);function iK(t,e){let i=(0,iq.K)(t),s=new AbortController;return[i,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function iz(t){return!("touch"===t.pointerType||eV.x||eV.y)}function iH(t,e,i){let{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=s["onHover"+i];n&&V.Gt.postRender(()=>n(e,tz(e)))}class iZ extends t${mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=iK(t,i),o=t=>{if(!iz(t))return;let{target:i}=t,s=e(i,t);if("function"!=typeof s||!i)return;let r=t=>{iz(t)&&(s(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,n)};return s.forEach(t=>{t.addEventListener("pointerenter",o,n)}),r}(t,(t,e)=>(iH(this.node,e,"Start"),t=>iH(this.node,t,"End"))))}unmount(){}}class iQ extends t${constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,em.F)(tq(this.node.current,"focus",()=>this.onFocus()),tq(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let i_=(t,e)=>!!e&&(t===e||i_(t,e.parentElement)),iJ=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),i0=new WeakSet;function i1(t){return e=>{"Enter"===e.key&&t(e)}}function i5(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let i9=(t,e)=>{let i=t.currentTarget;if(!i)return;let s=i1(()=>{if(i0.has(i))return;i5(i,"down");let t=i1(()=>{i5(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>i5(i,"cancel"),e)});i.addEventListener("keydown",s,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),e)};function i2(t){return tK(t)&&!(eV.x||eV.y)}function i3(t,e,i){let{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=s["onTap"+("End"===i?"":i)];n&&V.Gt.postRender(()=>n(e,tz(e)))}class i4 extends t${mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=iK(t,i),o=t=>{let s=t.currentTarget;if(!i2(t))return;i0.add(s);let r=e(s,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),i0.has(s)&&i0.delete(s),i2(t)&&"function"==typeof r&&r(t,{success:e})},a=t=>{o(t,s===window||s===document||i.useGlobalTarget||i_(s,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,n),window.addEventListener("pointercancel",l,n)};return s.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,n),(0,tb.s)(t))&&(t.addEventListener("focus",t=>i9(t,n)),iJ.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r}(t,(t,e)=>(i3(this.node,e,"Start"),(t,{success:e})=>i3(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let i6=new WeakMap,i8=new WeakMap,i7=t=>{let e=i6.get(t.target);e&&e(t)},st=t=>{t.forEach(i7)},se={some:0,all:1};class si extends t${constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:n}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:se[s]};return function(t,e,i){let s=function({root:t,...e}){let i=t||document;i8.has(i)||i8.set(i,{});let s=i8.get(i),n=JSON.stringify(e);return s[n]||(s[n]=new IntersectionObserver(st,{root:t,...e})),s[n]}(e);return i6.set(t,i),s.observe(t),()=>{i6.delete(t),s.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),r=e?i:s;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let ss=(0,eL.createContext)({strict:!1});var sn=i(7471);let sr=(0,eL.createContext)({});function so(t){return s(t.animate)||tF.some(e=>tL(t[e]))}function sa(t){return!!(so(t)||t.variants)}function sl(t){return Array.isArray(t)?t.join(" "):t}var sh=i(42801);let su={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},sd={};for(let t in su)sd[t]={isEnabled:e=>su[t].some(t=>!!e[t])};let sc=Symbol.for("motionComponentSymbol");var sp=i(50430),sm=i(69025);function sf(t,{layout:e,layoutId:i}){return g.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!e$[t]||"opacity"===t)}let sy=(t,e)=>e&&"number"==typeof t?e.transform(t):t,sv={...N.ai,transform:Math.round},sg={rotate:$.uj,rotateX:$.uj,rotateY:$.uj,rotateZ:$.uj,scale:N.hs,scaleX:N.hs,scaleY:N.hs,scaleZ:N.hs,skew:$.uj,skewX:$.uj,skewY:$.uj,distance:$.px,translateX:$.px,translateY:$.px,translateZ:$.px,x:$.px,y:$.px,z:$.px,perspective:$.px,transformPerspective:$.px,opacity:N.X4,originX:$.gQ,originY:$.gQ,originZ:$.px},sx={borderWidth:$.px,borderTopWidth:$.px,borderRightWidth:$.px,borderBottomWidth:$.px,borderLeftWidth:$.px,borderRadius:$.px,radius:$.px,borderTopLeftRadius:$.px,borderTopRightRadius:$.px,borderBottomRightRadius:$.px,borderBottomLeftRadius:$.px,width:$.px,maxWidth:$.px,height:$.px,maxHeight:$.px,top:$.px,right:$.px,bottom:$.px,left:$.px,padding:$.px,paddingTop:$.px,paddingRight:$.px,paddingBottom:$.px,paddingLeft:$.px,margin:$.px,marginTop:$.px,marginRight:$.px,marginBottom:$.px,marginLeft:$.px,backgroundPositionX:$.px,backgroundPositionY:$.px,...sg,zIndex:sv,fillOpacity:N.X4,strokeOpacity:N.X4,numOctaves:sv},sT={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},sw=v.length;function sS(t,e,i){let{style:s,vars:n,transformOrigin:r}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(g.has(t)){o=!0;continue}if((0,eN.j)(t)){n[t]=i;continue}{let e=sy(i,sx[t]);t.startsWith("origin")?(a=!0,r[t]=e):s[t]=e}}if(!e.transform&&(o||i?s.transform=function(t,e,i){let s="",n=!0;for(let r=0;r<sw;r++){let o=v[r],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=sy(a,sx[o]);if(!l){n=!1;let e=sT[o]||o;s+=`${e}(${t}) `}i&&(e[o]=t)}}return s=s.trim(),i?s=i(e,n?"":s):n&&(s="none"),s}(e,t.transform,i):s.transform&&(s.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=r;s.transformOrigin=`${t} ${e} ${i}`}}let sP=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function sb(t,e,i){for(let s in e)(0,d.S)(e[s])||sf(s,i)||(t[s]=e[s])}let sA={offset:"stroke-dashoffset",array:"stroke-dasharray"},sV={offset:"strokeDashoffset",array:"strokeDasharray"};function sM(t,{attrX:e,attrY:i,attrScale:s,pathLength:n,pathSpacing:r=1,pathOffset:o=0,...a},l,h,u){if(sS(t,a,h),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=u?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==s&&(d.scale=s),void 0!==n&&function(t,e,i=1,s=0,n=!0){t.pathLength=1;let r=n?sA:sV;t[r.offset]=$.px.transform(-s);let o=$.px.transform(e),a=$.px.transform(i);t[r.array]=`${o} ${a}`}(d,n,r,o,!1)}let sk=()=>({...sP(),attrs:{}}),sE=t=>"string"==typeof t&&"svg"===t.toLowerCase(),sD=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function sC(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||sD.has(t)}let sR=t=>!sC(t);try{!function(t){t&&(sR=e=>e.startsWith("on")?!sC(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let sj=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function sL(t){if("string"!=typeof t||t.includes("-"));else if(sj.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var sB=i(76168);let sF=t=>(e,i)=>{let n=(0,eL.useContext)(sr),o=(0,eL.useContext)(sp.t),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,o){return{latestValues:function(t,e,i,n){let o={},a=n(t,{});for(let t in a)o[t]=eZ(a[t]);let{initial:l,animate:h}=t,u=so(t),d=sa(t);e&&d&&!u&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===h&&(h=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===l)?h:l;if(p&&"boolean"!=typeof p&&!s(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let s=r(t,e[i]);if(s){let{transitionEnd:t,transition:e,...i}=s;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(o[t]=e)}for(let e in t)o[e]=t[e]}}}return o}(i,n,o,t),renderState:e()}})(t,e,n,o);return i?a():(0,sB.M)(a)};function sO(t,e,i){let{style:s}=t,n={};for(let r in s)((0,d.S)(s[r])||e.style&&(0,d.S)(e.style[r])||sf(r,t)||i?.getValue(r)?.liveStyle!==void 0)&&(n[r]=s[r]);return n}let sI={useVisualState:sF({scrapeMotionValuesFromProps:sO,createRenderState:sP})};function sU(t,e,i){let s=sO(t,e,i);for(let i in t)((0,d.S)(t[i])||(0,d.S)(e[i]))&&(s[-1!==v.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return s}let sW={useVisualState:sF({scrapeMotionValuesFromProps:sU,createRenderState:sk})},sN={current:null},s$={current:!1},sX=new WeakMap,sG=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),sY=t=>/^0[^.\s]+$/u.test(t);var sq=i(92901);let sK=t=>e=>e.test(t),sz=[N.ai,$.px,$.KN,$.uj,$.vw,$.vh,{test:t=>"auto"===t,parse:t=>t}],sH=t=>sz.find(sK(t)),sZ=[...sz,sq.y,tS.f],sQ=t=>sZ.find(sK(t));var s_=i(38459);let sJ=new Set(["brightness","contrast","saturate","opacity"]);function s0(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[s]=i.match(s_.S)||[];if(!s)return t;let n=i.replace(s,""),r=+!!sJ.has(e);return s!==i&&(r*=100),e+"("+r+n+")"}let s1=/\b([a-z-]*)\(.*?\)/gu,s5={...tS.f,getAnimatableNone:t=>{let e=t.match(s1);return e?e.map(s0).join(" "):t}},s9={...sx,color:sq.y,backgroundColor:sq.y,outlineColor:sq.y,fill:sq.y,stroke:sq.y,borderColor:sq.y,borderTopColor:sq.y,borderRightColor:sq.y,borderBottomColor:sq.y,borderLeftColor:sq.y,filter:s5,WebkitFilter:s5},s2=t=>s9[t];function s3(t,e){let i=s2(t);return i!==s5&&(i=tS.f),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let s4=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class s6{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:n,visualState:r},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=J,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=k.k.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,V.Gt.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=r;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=o,this.blockInitialAnimation=!!n,this.isControllingVariants=so(e),this.isVariantNode=sa(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...u}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in u){let e=u[t];void 0!==a[t]&&(0,d.S)(e)&&e.set(a[t],!1)}}mount(t){this.current=t,sX.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),s$.current||function(){if(s$.current=!0,sh.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>sN.current=t.matches;t.addListener(e),e()}else sN.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sN.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),(0,V.WG)(this.notifyUpdate),(0,V.WG)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let s=g.has(t);s&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&V.Gt.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in sd){let e=sd[t];if(!e)continue;let{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):t8()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<s4.length;e++){let i=s4[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){for(let s in e){let n=e[s],r=i[s];if((0,d.S)(n))t.addValue(s,n);else if((0,d.S)(r))t.addValue(s,(0,u.OQ)(n,{owner:t}));else if(r!==n)if(t.hasValue(s)){let e=t.getValue(s);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(s);t.addValue(s,(0,u.OQ)(void 0!==e?e:n,{owner:t}))}}for(let s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,u.OQ)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(sG(i)||sY(i))?i=parseFloat(i):!sQ(i)&&tS.f.test(e)&&(i=s3(t,e)),this.setBaseTarget(t,(0,d.S)(i)?i.get():i)),(0,d.S)(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let s=r(this.props,i,this.presenceContext?.custom);s&&(e=s[t])}if(i&&void 0!==e)return e;let s=this.getBaseTargetFromProps(this.props,t);return void 0===s||(0,d.S)(s)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new ic.v),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}let s8=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,s7=new Set(["auto","none","0"]);class nt extends J{constructor(t,e,i,s,n){super(t,e,i,s,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let s=t[i];if("string"==typeof s&&(s=s.trim(),(0,eN.p)(s))){let n=function t(e,i,s=1){(0,tp.V)(s<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,r]=function(t){let e=s8.exec(t);if(!e)return[,];let[,i,s,n]=e;return[`--${i??s}`,n]}(e);if(!n)return;let o=window.getComputedStyle(i).getPropertyValue(n);if(o){let t=o.trim();return sG(t)?parseFloat(t):t}return(0,eN.p)(r)?t(r,i,s+1):r}(s,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!tE.has(i)||2!==t.length)return;let[s,n]=t,r=sH(s),o=sH(n);if(r!==o)if(X(r)&&X(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else q[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var s;(null===t[e]||("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||sY(s)))&&i.push(e)}i.length&&function(t,e,i){let s,n=0;for(;n<t.length&&!s;){let e=t[n];"string"==typeof e&&!s7.has(e)&&(0,tS.V)(e).values.length&&(s=t[n]),n++}if(s&&i)for(let n of e)t[n]=s3(i,s)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=q[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let s=e[e.length-1];void 0!==s&&t.getValue(i,s).jump(s,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);let n=i.length-1,r=i[n];i[n]=q[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}class ne extends s6{constructor(){super(...arguments),this.KeyframeResolver=nt}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;(0,d.S)(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function ni(t,{style:e,vars:i},s,n){for(let r in Object.assign(t.style,e,n&&n.getProjectionStyles(s)),i)t.style.setProperty(r,i[r])}class ns extends ne{constructor(){super(...arguments),this.type="html",this.renderInstance=ni}readValueFromInstance(t,e){if(g.has(e))return this.projection?.isProjecting?O(e):U(t,e);{let i=window.getComputedStyle(t),s=((0,eN.j)(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return eu(t,e)}build(t,e,i){sS(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return sO(t,e,i)}}let nn=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class nr extends ne{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=t8}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(g.has(e)){let t=s2(e);return t&&t.default||0}return e=nn.has(e)?e:m(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return sU(t,e,i)}build(t,e,i){sM(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,s){for(let i in ni(t,e,void 0,s),e.attrs)t.setAttribute(nn.has(i)?i:m(i),e.attrs[i])}mount(t){this.isSVGTag=sE(t.tagName),super.mount(t)}}let no=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,s)=>"create"===s?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}((l={animation:{Feature:tX},exit:{Feature:tY},inView:{Feature:si},tap:{Feature:i4},focus:{Feature:iQ},hover:{Feature:iZ},pan:{Feature:eR},drag:{Feature:eD,ProjectionNode:iY,MeasureLayout:eY},layout:{ProjectionNode:iY,MeasureLayout:eY}},h=(t,e)=>sL(t)?new nr(e):new ns(e,{allowProjection:t!==eL.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:s,createVisualElement:n,useRender:r,useVisualState:o,Component:a}=t;function l(t,e){var i,s,l;let h,u={...(0,eL.useContext)(sn.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,eL.useContext)(eF.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:d}=u,c=function(t){let{initial:e,animate:i}=function(t,e){if(so(t)){let{initial:e,animate:i}=t;return{initial:!1===e||tL(e)?e:void 0,animate:tL(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,eL.useContext)(sr));return(0,eL.useMemo)(()=>({initial:e,animate:i}),[sl(e),sl(i)])}(t),p=o(t,d);if(!d&&sh.B){s=0,l=0,(0,eL.useContext)(ss).strict;let t=function(t){let{drag:e,layout:i}=sd;if(!e&&!i)return{};let s={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(u);h=t.MeasureLayout,c.visualElement=function(t,e,i,s,n){let{visualElement:r}=(0,eL.useContext)(sr),o=(0,eL.useContext)(ss),a=(0,eL.useContext)(sp.t),l=(0,eL.useContext)(sn.Q).reducedMotion,h=(0,eL.useRef)(null);s=s||o.renderer,!h.current&&s&&(h.current=s(t,{visualState:e,parent:r,props:i,presenceContext:a,blockInitialAnimation:!!a&&!1===a.initial,reducedMotionConfig:l}));let u=h.current,d=(0,eL.useContext)(eO);u&&!u.projection&&n&&("html"===u.type||"svg"===u.type)&&function(t,e,i,s){let{layoutId:n,layout:r,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:r,alwaysMeasureLayout:!!o||a&&ec(a),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:s,crossfade:u,layoutScroll:l,layoutRoot:h})}(h.current,i,n,d);let c=(0,eL.useRef)(!1);(0,eL.useInsertionEffect)(()=>{u&&c.current&&u.update(i,a)});let p=i[f],m=(0,eL.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,sm.E)(()=>{u&&(c.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),eX.render(u.render),m.current&&u.animationState&&u.animationState.animateChanges())}),(0,eL.useEffect)(()=>{u&&(!m.current&&u.animationState&&u.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),u}(a,p,u,n,t.ProjectionNode)}return(0,ej.jsxs)(sr.Provider,{value:c,children:[h&&c.visualElement?(0,ej.jsx)(h,{visualElement:c.visualElement,...u}):null,r(a,t,(i=c.visualElement,(0,eL.useCallback)(t=>{t&&p.onMount&&p.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):ec(e)&&(e.current=t))},[i])),p,d,c.visualElement)]})}s&&function(t){for(let e in t)sd[e]={...sd[e],...t[e]}}(s),l.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!=(i=null!=(e=a.displayName)?e:a.name)?i:"",")"));let h=(0,eL.forwardRef)(l);return h[sc]=a,h}({...sL(t)?sW:sI,preloadedFeatures:l,useRender:function(t=!1){return(e,i,s,{latestValues:n},r)=>{let o=(sL(e)?function(t,e,i,s){let n=(0,eL.useMemo)(()=>{let i=sk();return sM(i,e,sE(s),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};sb(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},s=function(t,e){let i=t.style||{},s={};return sb(s,i,t),Object.assign(s,function({transformTemplate:t},e){return(0,eL.useMemo)(()=>{let i=sP();return sS(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),s}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,n,r,e),a=function(t,e,i){let s={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(sR(n)||!0===i&&sC(n)||!e&&!sC(n)||t.draggable&&n.startsWith("onDrag"))&&(s[n]=t[n]);return s}(i,"string"==typeof e,t),l=e!==eL.Fragment?{...a,...o,ref:s}:{},{children:h}=i,u=(0,eL.useMemo)(()=>(0,d.S)(h)?h.get():h,[h]);return(0,eL.createElement)(e,{...l,children:u})}}(e),createVisualElement:h,Component:t})}))},97393:(t,e,i)=>{i.d(e,{Y:()=>s,t:()=>n});let s=2e4;function n(t){let e=0,i=t.next(e);for(;!i.done&&e<s;)e+=50,i=t.next(e);return e>=s?1/0:e}},99388:(t,e,i)=>{i.d(e,{D:()=>s});let s=t=>Array.isArray(t)&&"number"==typeof t[0]}}]);