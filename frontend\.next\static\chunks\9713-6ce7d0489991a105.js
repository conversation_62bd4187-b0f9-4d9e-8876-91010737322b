"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9713],{17313:(e,t,r)=>{r.d(t,{Xi:()=>o,av:()=>c,j7:()=>n,tU:()=>i});var l=r(95155);r(12115);var s=r(60704),a=r(59434);function i(e){let{className:t,...r}=e;return(0,l.jsx)(s.bL,{"data-slot":"tabs",className:(0,a.cn)("flex flex-col gap-2",t),...r})}function n(e){let{className:t,...r}=e;return(0,l.jsx)(s.B8,{"data-slot":"tabs-list",className:(0,a.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",t),...r})}function o(e){let{className:t,...r}=e;return(0,l.jsx)(s.l9,{"data-slot":"tabs-trigger",className:(0,a.cn)("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...r})}function c(e){let{className:t,...r}=e;return(0,l.jsx)(s.UC,{"data-slot":"tabs-content",className:(0,a.cn)("flex-1 outline-none",t),...r})}},18068:(e,t,r)=>{r.d(t,{D5:()=>k,S8:()=>j,jD:()=>v,qR:()=>_});var l=r(34869),s=r(49992),a=r(4113),i=r(50773),n=r(57434),o=r(41684),c=r(47924),d=r(33786),u=r(19302),p=r(79323),m=r(42337),h=r(29621),g=r(13089),x=r(43453),f=r(32892),b=r(5040),w=r(48313);function v(e,t){if(!e)return t;try{let t=JSON.parse(e);if("string"==typeof t&&(t.startsWith("{")||t.startsWith("[")))try{return JSON.parse(t)}catch(e){}return t}catch(r){if("object"==typeof e)return e;if("string"==typeof e){if("true"===e)return!0;if("false"===e)return!1;if("null"===e)return null;if(!isNaN(Number(e)))return Number(e);if(!e.startsWith("{")&&!e.startsWith("["))return e}return t}}let j=e=>{switch(null==e?void 0:e.toLowerCase()){case"web-browser-takeover":case"browser-navigate-to":case"browser-click-element":case"browser-input-text":case"browser-scroll-down":case"browser-scroll-up":case"browser-click-coordinates":case"browser-send-keys":case"browser-switch-tab":case"browser-go-back":case"browser-close-tab":case"browser-drag-drop":case"browser-get-dropdown-options":case"browser-select-dropdown-option":case"browser-scroll-to-text":case"browser-wait":case"crawl-webpage":case"scrape-webpage":return l.A;case"create-file":return s.A;case"str-replace":return a.A;case"full-file-rewrite":return i.A;case"read-file":return n.A;case"execute-command":case"check-command-output":case"terminate-command":return o.A;case"web-search":return c.A;case"call-data-provider":return d.A;case"get-data-provider-endpoints":case"execute-data-provider-call":return u.A;case"delete-file":return p.A;case"deploy-site":return m.A;case"execute-code":return h.A;case"ask":return g.A;case"complete":return x.A;case"call-mcp-tool":return f.A;default:if(null==e?void 0:e.startsWith("mcp_")){let t=e.split("_");if(t.length>=3){let e=t[1],r=t.slice(2).join("_");if(r.includes("search")||r.includes("web"))return c.A;if(r.includes("research")||r.includes("paper"))return b.A;if("exa"===e)return c.A}return f.A}return console.log("[PAGE] Using default icon for unknown tool type: ".concat(e)),w.A}},k=(e,t)=>{if(!t)return null;try{if(null==e?void 0:e.toLowerCase().startsWith("browser-")){let e=t.match(/url=(?:"|')([^"|']+)(?:"|')/);if(e)return e[1];let r=t.match(/goal=(?:"|')([^"|']+)(?:"|')/);if(r){let e=r[1];return e.length>30?e.substring(0,27)+"...":e}return null}if(t.startsWith("<")&&t.includes(">")){let r=t.match(/<[^>]+\s+([^>]+)>/);if(r&&r[1]){let t=r[1].trim(),l=t.match(/file_path=["']([^"']+)["']/);if(l)return l[1].split("/").pop()||l[1];if((null==e?void 0:e.toLowerCase())==="execute-command"){let e=t.match(/(?:command|cmd)=["']([^"']+)["']/);if(e){let t=e[1];return t.length>30?t.substring(0,27)+"...":t}}}}let r=null;switch(null==e?void 0:e.toLowerCase()){case"create-file":case"full-file-rewrite":case"read-file":case"delete-file":case"str-replace":return(r=t.match(/file_path=(?:"|')([^"|']+)(?:"|')/))?r[1].split("/").pop()||r[1]:null;case"execute-command":if(r=t.match(/command=(?:"|')([^"|']+)(?:"|')/)){let e=r[1];return e.length>30?e.substring(0,27)+"...":e}break;case"web-search":return(r=t.match(/query=(?:"|')([^"|']+)(?:"|')/))?r[1].length>30?r[1].substring(0,27)+"...":r[1]:null;case"call-data-provider":r=t.match(/service_name=(?:"|')([^"|']+)(?:"|')/);let l=t.match(/route=(?:"|')([^"|']+)(?:"|')/);return r&&l?"".concat(r[1],"/").concat(l[1]):r?r[1]:null;case"deploy-site":return(r=t.match(/site_name=(?:"|')([^"|']+)(?:"|')/))?r[1]:null}return null}catch(e){return console.warn("Error parsing tool parameters:",e),null}},N=new Map([["execute-command","Executing Command"],["check-command-output","Checking Command Output"],["terminate-command","Terminating Command"],["list-commands","Listing Commands"],["create-file","Creating File"],["delete-file","Deleting File"],["full-file-rewrite","Rewriting File"],["str-replace","Editing Text"],["str_replace","Editing Text"],["browser-click-element","Clicking Element"],["browser-close-tab","Closing Tab"],["browser-drag-drop","Dragging Element"],["browser-get-dropdown-options","Getting Options"],["browser-go-back","Going Back"],["browser-input-text","Entering Text"],["browser-navigate-to","Navigating to Page"],["browser-scroll-down","Scrolling Down"],["browser-scroll-to-text","Scrolling to Text"],["browser-scroll-up","Scrolling Up"],["browser-select-dropdown-option","Selecting Option"],["browser-click-coordinates","Clicking Coordinates"],["browser-send-keys","Pressing Keys"],["browser-switch-tab","Switching Tab"],["browser-wait","Waiting"],["execute-data-provider-call","Calling data provider"],["execute_data_provider_call","Calling data provider"],["get-data-provider-endpoints","Getting endpoints"],["deploy","Deploying"],["ask","Ask"],["complete","Completing Task"],["crawl-webpage","Crawling Website"],["expose-port","Exposing Port"],["scrape-webpage","Scraping Website"],["web-search","Searching Web"],["see-image","Viewing Image"],["call-mcp-tool","External Tool"],["update-agent","Updating Agent"],["get-current-agent-config","Getting Agent Config"],["search-mcp-servers","Searching MCP Servers"],["get-mcp-server-tools","Getting MCP Server Tools"],["configure-mcp-server","Configuring MCP Server"],["get-popular-mcp-servers","Getting Popular MCP Servers"],["test-mcp-server-connection","Testing MCP Server Connection"],["execute_command","Executing Command"],["check_command_output","Checking Command Output"],["terminate_command","Terminating Command"],["list_commands","Listing Commands"],["create_file","Creating File"],["delete_file","Deleting File"],["full_file_rewrite","Rewriting File"],["str_replace","Editing Text"],["browser_click_element","Clicking Element"],["browser_close_tab","Closing Tab"],["browser_drag_drop","Dragging Element"],["browser_get_dropdown_options","Getting Options"],["browser_go_back","Going Back"],["browser_input_text","Entering Text"],["browser_navigate_to","Navigating to Page"],["browser_scroll_down","Scrolling Down"],["browser_scroll_to_text","Scrolling to Text"],["browser_scroll_up","Scrolling Up"],["browser_select_dropdown_option","Selecting Option"],["browser_click_coordinates","Clicking Coordinates"],["browser_send_keys","Pressing Keys"],["browser_switch_tab","Switching Tab"],["browser_wait","Waiting"],["execute_data_provider_call","Calling data provider"],["get_data_provider_endpoints","Getting endpoints"],["deploy","Deploying"],["ask","Ask"],["complete","Completing Task"],["crawl_webpage","Crawling Website"],["expose_port","Exposing Port"],["scrape_webpage","Scraping Website"],["web_search","Searching Web"],["see_image","Viewing Image"],["call_mcp_tool","External Tool"],["update_agent","Updating Agent"],["get_current_agent_config","Getting Agent Config"],["search_mcp_servers","Searching MCP Servers"],["get_mcp_server_tools","Getting MCP Server Tools"],["configure_mcp_server","Configuring MCP Server"],["get_popular_mcp_servers","Getting Popular MCP Servers"],["test_mcp_server_connection","Testing MCP Server Connection"]]);function C(e,t){let r={exa:"Exa Search",github:"GitHub",notion:"Notion",slack:"Slack",filesystem:"File System",memory:"Memory",anthropic:"Anthropic",openai:"OpenAI",composio:"Composio",langchain:"LangChain",llamaindex:"LlamaIndex"}[e.toLowerCase()]||e.charAt(0).toUpperCase()+e.slice(1),l=t;return l=t.includes("-")?t.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):t.includes("_")?t.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):/[a-z][A-Z]/.test(t)?t.replace(/([a-z])([A-Z])/g,"$1 $2").split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):t.charAt(0).toUpperCase()+t.slice(1),"".concat(r,": ").concat(l)}function _(e){if(e.startsWith("mcp_")){let t=e.split("_");if(t.length>=3)return C(t[1],t.slice(2).join("_"))}if(e.includes("-")&&!N.has(e)){let t=e.split("-");if(t.length>=2)return C(t[0],t.slice(1).join("-"))}return N.get(e)||e}},30257:(e,t,r)=>{r.d(t,{pH:()=>l.pH,R2:()=>l.R2,RP:()=>s,x2:()=>l.x2,np:()=>n.np,OG:()=>l.OG,c:()=>i});var l=r(34986);function s(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return(0,l.x2)(e,t,{enabled:r.enabled,staleTime:r.staleTime})}var a=r(12115);function i(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},[s,i]=a.useState(null),{data:n,isLoading:o,error:c}=(0,l.x2)(e,t,{contentType:"blob",enabled:r.enabled,staleTime:r.staleTime||3e5});return a.useEffect(()=>{if(!(n instanceof Blob))return void i(null);{console.log("[IMAGE CONTENT] Creating blob URL for ".concat(t),{size:n.size,type:n.type});let e=URL.createObjectURL(n);return i(e),()=>{console.log("[IMAGE CONTENT] Cleaning up blob URL for ".concat(t,": ").concat(e)),URL.revokeObjectURL(e),i(null)}}},[n,t]),{data:s,isLoading:o,error:c}}var n=r(88362)},63496:(e,t,r)=>{r.d(t,{i:()=>l});function l(e,t){if(!e||!t)return;let r=t.replace(/^\/workspace\//,"").split("/").map(e=>encodeURIComponent(e)).join("/");return"".concat(e,"/").concat(r)}},66424:(e,t,r)=>{r.d(t,{F:()=>i});var l=r(95155);r(12115);var s=r(47655),a=r(59434);function i(e){let{className:t,children:r,...i}=e;return(0,l.jsxs)(s.bL,{"data-slot":"scroll-area",className:(0,a.cn)("relative",t),...i,children:[(0,l.jsx)(s.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:r}),(0,l.jsx)(n,{}),(0,l.jsx)(s.OK,{})]})}function n(e){let{className:t,orientation:r="vertical",...i}=e;return(0,l.jsx)(s.VM,{"data-slot":"scroll-area-scrollbar",orientation:r,className:(0,a.cn)("flex touch-none p-px transition-colors select-none","vertical"===r&&"h-full w-2.5 border-l border-l-transparent","horizontal"===r&&"h-2.5 flex-col border-t border-t-transparent",t),...i,children:(0,l.jsx)(s.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}},84332:(e,t,r)=>{r.d(t,{m:()=>P,Q:()=>E});var l=r(95155),s=r(12115),a=r(42118),i=r(62332),n=r(57434),o=r(84209),c=r(45517),d=r(73032),u=r(64261),p=r(39022),m=r(54213),h=r(99890),g=r(51154),x=r(33786),f=r(59434),b=r(86618),w=r(66424),v=r(63496);function j(e){var t,r;let{content:a,previewUrl:i,className:n,project:o}=e,[c,d]=(0,s.useState)("preview"),u=(0,s.useMemo)(()=>{var e;if(a&&!(null==o||null==(e=o.sandbox)?void 0:e.sandbox_url)){let e=new Blob([a],{type:"text/html"});return URL.createObjectURL(e)}},[a,null==o||null==(t=o.sandbox)?void 0:t.sandbox_url]),p=(0,s.useMemo)(()=>{try{if(i.includes("/api/sandboxes/")){let e=new URL(i).searchParams.get("path");if(e)return e.split("/").pop()||""}return i.split("/").pop()||""}catch(e){return console.error("Error extracting filename:",e),""}},[i]),m=(0,s.useMemo)(()=>{var e;return(null==o||null==(e=o.sandbox)?void 0:e.sandbox_url)&&p?(0,v.i)(o.sandbox.sandbox_url,p):u||i},[null==o||null==(r=o.sandbox)?void 0:r.sandbox_url,p,u,i]);return(0,s.useEffect)(()=>()=>{u&&URL.revokeObjectURL(u)},[u]),(0,l.jsx)("div",{className:(0,f.cn)("w-full h-full flex flex-col",n),children:(0,l.jsx)("div",{className:"flex-1 min-h-0 relative",children:"preview"===c?(0,l.jsx)("div",{className:"w-full h-full",children:(0,l.jsx)("iframe",{src:m,title:"HTML Preview",className:"w-full h-full border-0",sandbox:"allow-same-origin allow-scripts",style:{background:"white"}})}):(0,l.jsx)(w.F,{className:"w-full h-full",children:(0,l.jsx)("pre",{className:"p-4 overflow-auto",children:(0,l.jsx)("code",{className:"text-sm",children:a})})})})})}var k=r(31834);function N(e){let{content:t,className:r}=e;return(0,l.jsx)("div",{className:(0,f.cn)("w-full h-full overflow-hidden",r),children:(0,l.jsx)(w.F,{className:"w-full h-full",children:(0,l.jsx)("div",{className:"p-4",children:(0,l.jsx)(k.o,{className:"prose prose-sm dark:prose-invert max-w-none [&>:first-child]:mt-0",children:t})})})})}var C=r(60408),_=r.n(C);function y(e){let{content:t,className:r}=e,s=function(e){if(!e)return{data:[],headers:[]};try{let t=_().parse(e,{header:!0,skipEmptyLines:!0}),r=[];return t.meta&&t.meta.fields&&(r=t.meta.fields||[]),{headers:r,data:t.data}}catch(e){return console.error("Error parsing CSV:",e),{headers:[],data:[]}}}(t),a=0===s.data.length;return(0,l.jsx)("div",{className:(0,f.cn)("w-full h-full overflow-hidden",r),children:(0,l.jsx)(w.F,{className:"w-full h-full",children:(0,l.jsx)("div",{className:"p-0",children:(0,l.jsxs)("table",{className:"w-full border-collapse text-sm",children:[(0,l.jsx)("thead",{className:"bg-muted sticky top-0",children:(0,l.jsx)("tr",{children:s.headers.map((e,t)=>(0,l.jsx)("th",{className:"px-3 py-2 text-left font-medium border border-border",children:e},t))})}),(0,l.jsx)("tbody",{children:a?(0,l.jsx)("tr",{children:(0,l.jsx)("td",{colSpan:s.headers.length||1,className:"py-4 text-center text-muted-foreground",children:"No data available"})}):s.data.map((e,t)=>(0,l.jsx)("tr",{className:"border-b border-border hover:bg-muted/50",children:s.headers.map((t,r)=>(0,l.jsx)("td",{className:"px-3 py-2 border border-border",children:e[t]||""},r))},t))})]})})})})}var A=r(30257),S=r(64541);function P(e){var t;let{filepath:r,onClick:b,className:w,sandboxId:v,showPreview:k=!0,localPreviewUrl:C,customStyle:_,collapsed:P=!0,project:E}=e,{session:L}=(0,S.A)(),[U,T]=s.useState(!1),F=r.split("/").pop()||"file",W=(null==(t=F.split(".").pop())?void 0:t.toLowerCase())||"",M=function(e){var t;let r=(null==(t=e.split(".").pop())?void 0:t.toLowerCase())||"";return["png","jpg","jpeg","gif","webp","svg","bmp"].includes(r)?"image":["js","jsx","ts","tsx","html","css","json","py","java","c","cpp"].includes(r)?"code":["txt","log","env"].includes(r)?"text":["md","markdown"].includes(r)?"markdown":"pdf"===r?"pdf":["mp3","wav","ogg","flac"].includes(r)?"audio":["mp4","webm","mov","avi"].includes(r)?"video":["csv","tsv"].includes(r)?"csv":["xls","xlsx"].includes(r)?"spreadsheet":["zip","rar","tar","gz"].includes(r)?"archive":["db","sqlite","sql"].includes(r)?"database":"other"}(F),I=C||(v?function(e,t){if(!e)return t;t.startsWith("/workspace")||(t="/workspace/".concat(t.startsWith("/")?t.substring(1):t));try{t=t.replace(/\\u([0-9a-fA-F]{4})/g,(e,t)=>String.fromCharCode(parseInt(t,16)))}catch(e){console.error("Error processing Unicode escapes in path:",e)}let r=new URL("".concat("http://localhost:8000/api","/sandboxes/").concat(e,"/files/content"));return r.searchParams.append("path",t),r.toString()}(v,r):r),z="code"===M&&W?W.toUpperCase():({image:"Image",code:"Code",text:"Text",markdown:"Markdown",pdf:"PDF",audio:"Audio",video:"Video",spreadsheet:"Spreadsheet",csv:"CSV",archive:"Archive",database:"Database",other:"File"})[M],R=function(e,t){let r=(5*e.length%800+200)*({image:5,video:20,audio:10,code:.5,text:.3,markdown:.3,pdf:8,spreadsheet:3,csv:2,archive:5,database:4,other:1})[t];return r<1024?"".concat(Math.round(r)," B"):r<1048576?"".concat((r/1024).toFixed(1)," KB"):"".concat((r/1048576).toFixed(1)," MB")}(r,M),O={image:a.A,code:i.A,text:n.A,markdown:n.A,pdf:o.A,audio:c.A,video:d.A,spreadsheet:u.A,csv:u.A,archive:p.A,database:m.A,other:h.A}[M],G="image"===M,D="html"===W||"htm"===W||"md"===W||"markdown"===W,B="csv"===W||"tsv"===W,$=(null==_?void 0:_.gridColumn)==="1 / -1"||!!(_&&"--attachment-height"in _),V=(D||B)&&k&&!1===P,{data:q,isLoading:H,error:Z}=(0,A.RP)(V?v:void 0,V?r:void 0),{data:K,isLoading:X,error:J}=(0,A.c)(G&&k&&v?v:void 0,G&&k?r:void 0);s.useEffect(()=>{(Z||J)&&T(!0)},[Z,J]);let Q=()=>{b&&b(r)};if(G&&k){let e=$?_["--attachment-height"]:"54px";return X&&v?(0,l.jsx)("button",{onClick:Q,className:(0,f.cn)("group relative min-h-[54px] min-w-fit rounded-xl cursor-pointer","border border-black/10 dark:border-white/10","bg-black/5 dark:bg-black/20","p-0 overflow-hidden","flex items-center justify-center",$?"w-full":"min-w-[54px]",w),style:{maxWidth:"100%",height:$?e:"auto",..._},title:F,children:(0,l.jsx)(g.A,{className:"h-6 w-6 text-primary animate-spin"})}):J||U?(0,l.jsxs)("button",{onClick:Q,className:(0,f.cn)("group relative min-h-[54px] min-w-fit rounded-xl cursor-pointer","border border-black/10 dark:border-white/10","bg-black/5 dark:bg-black/20","p-0 overflow-hidden","flex flex-col items-center justify-center gap-1",$?"w-full":"inline-block",w),style:{maxWidth:"100%",height:$?e:"auto",..._},title:F,children:[(0,l.jsx)(O,{className:"h-6 w-6 text-red-500 mb-1"}),(0,l.jsx)("div",{className:"text-xs text-red-500",children:"Failed to load image"})]}):(0,l.jsx)("button",{onClick:Q,className:(0,f.cn)("group relative min-h-[54px] rounded-2xl cursor-pointer","border border-black/10 dark:border-white/10","bg-black/5 dark:bg-black/20","p-0 overflow-hidden","flex items-center justify-center",$?"w-full":"inline-block",w),style:{maxWidth:"100%",height:$?e:"auto",..._},title:F,children:(0,l.jsx)("img",{src:v&&(null==L?void 0:L.access_token)?K:I||"",alt:F,className:(0,f.cn)("max-h-full",$?"w-full h-full object-cover":"w-auto"),style:{height:e,objectPosition:"center",objectFit:$?"cover":"contain"},onLoad:()=>{console.log("Image loaded successfully:",F)},onError:e=>{console.error("Image load error for:",F),T(!0),C&&"string"==typeof C&&C.startsWith("blob:")&&(console.log("Falling back to localPreviewUrl for:",F),e.target.src=C)}})})}if(V&&$){let e={html:j,htm:j,md:N,markdown:N,csv:y,tsv:y}[W];return(0,l.jsxs)("div",{className:(0,f.cn)("group relative rounded-xl w-full","border","bg-card","overflow-hidden","h-[300px]","pt-10",w),style:{gridColumn:"1 / -1",width:"100%",..._},onClick:U?Q:void 0,children:[(0,l.jsxs)("div",{className:"h-full w-full relative",children:[!U&&q&&(0,l.jsx)(l.Fragment,{children:e?(0,l.jsx)(e,{content:q,previewUrl:I,className:"h-full w-full",project:E}):(0,l.jsx)("div",{className:"p-4 text-muted-foreground",children:"No preview available for this file type"})}),U&&(0,l.jsxs)("div",{className:"h-full w-full flex flex-col items-center justify-center p-4",children:[(0,l.jsx)("div",{className:"text-red-500 mb-2",children:"Error loading content"}),(0,l.jsx)("div",{className:"text-muted-foreground text-sm text-center mb-2",children:I&&(0,l.jsx)("div",{className:"text-xs max-w-full overflow-hidden truncate opacity-70",children:"Path may need /workspace prefix"})}),(0,l.jsx)("button",{onClick:Q,className:"px-3 py-1.5 bg-primary/10 hover:bg-primary/20 rounded-md text-sm",children:"Open in viewer"})]}),H&&(0,l.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-background/50",children:(0,l.jsx)(g.A,{className:"h-6 w-6 text-primary animate-spin"})}),!q&&!H&&!U&&(0,l.jsxs)("div",{className:"h-full w-full flex flex-col items-center justify-center p-4 pointer-events-none",children:[(0,l.jsx)("div",{className:"text-muted-foreground text-sm mb-2",children:"Preview available"}),(0,l.jsx)("div",{className:"text-muted-foreground text-xs text-center",children:"Click header to open externally"})]})]}),(0,l.jsxs)("div",{className:"absolute top-0 left-0 right-0 bg-accent p-2 z-10 flex items-center justify-between",children:[(0,l.jsx)("div",{className:"text-sm font-medium truncate",children:F}),b&&(0,l.jsx)("button",{onClick:Q,className:"p-1 rounded-full hover:bg-black/10 dark:hover:bg-white/10",children:(0,l.jsx)(x.A,{size:14})})]})]})}let Y={..._};return delete Y.height,delete Y["--attachment-height"],(0,l.jsxs)("button",{onClick:Q,className:(0,f.cn)("group flex rounded-xl transition-all duration-200 min-h-[54px] h-[54px] overflow-hidden cursor-pointer","border border-black/10 dark:border-white/10","bg-sidebar","text-left","pr-7",$?"min-w-[170px] max-w-[300px] w-fit":"min-w-[170px] w-full sm:max-w-[300px] sm:w-fit",w),style:Y,title:F,children:[(0,l.jsx)("div",{className:"relative min-w-[54px] w-[54px] h-full aspect-square flex-shrink-0 bg-black/5 dark:bg-white/5",children:(0,l.jsx)("div",{className:"flex items-center justify-center h-full w-full",children:(0,l.jsx)(O,{className:"h-5 w-5 text-black/60 dark:text-white/60"})})}),(0,l.jsxs)("div",{className:"flex-1 min-w-0 flex flex-col justify-center p-2 pl-3 overflow-hidden",children:[(0,l.jsx)("div",{className:"text-sm font-medium text-foreground truncate max-w-full",children:F}),(0,l.jsxs)("div",{className:"text-xs text-muted-foreground flex items-center gap-1 truncate",children:[(0,l.jsx)("span",{className:"text-black/60 dark:text-white/60 truncate",children:z}),(0,l.jsx)("span",{className:"text-black/40 dark:text-white/40 flex-shrink-0",children:"\xb7"}),(0,l.jsx)("span",{className:"text-black/60 dark:text-white/60 flex-shrink-0",children:R})]})]})]})}function E(e){let{attachments:t,onFileClick:r,className:s,sandboxId:a,showPreviews:i=!0,collapsed:n=!1,project:o}=e;return t&&0!==t.length?(0,l.jsx)(b.X,{files:t,onFileClick:r,className:s,sandboxId:a,showPreviews:i,layout:"grid",gridImageHeight:150,collapsed:n,project:o}):null}},86618:(e,t,r)=>{r.d(t,{X:()=>m});var l=r(95155),s=r(76408),a=r(60760),i=r(54416),n=r(84616),o=r(46102),c=r(84332),d=r(59434),u=r(12115),p=r(54165);function m(e){let{files:t,sandboxId:r,onRemove:m,layout:h="inline",className:g,onFileClick:x,showPreviews:f=!0,maxHeight:b="216px",gridImageHeight:w=180,collapsed:v=!0,project:j}=e,[k,N]=(0,u.useState)(!1),[C,_]=(0,u.useState)(!1);if((0,u.useEffect)(()=>{let e=()=>{_(window.innerWidth<640)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]),!t||0===t.length)return(0,l.jsx)(s.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"inline"===h?"":"mt-4"});let y="string"==typeof t[0]?[...new Set(t)]:t,A=e=>"string"==typeof e?e:e.path,S=e=>{x&&x(e,y.map(e=>A(e)))},P=e=>{if("string"!=typeof e)return r?void 0:e.localUrl},E=e=>{var t;let r=(null==(t=A(e).split(".").pop())?void 0:t.toLowerCase())||"";return"html"===r||"htm"===r||"md"===r||"markdown"===r||"csv"===r||"tsv"===r},L=Math.min(C?2:5,y.length),U=y.length-L;C||1!==U||(L=y.length,U=0);let T=y.slice(0,L).map((e,t)=>{let r=A(e),l=null!==(r.split("/").pop()||"").match(/\.(jpg|jpeg|png|gif|webp|svg|bmp)$/i);return{file:e,path:r,isImage:l,wrapperClassName:C&&!l?"w-full":""}}),F=[...y].sort((e,t)=>{let r=e=>null!==(A(e).split("/").pop()||"").match(/\.(jpg|jpeg|png|gif|webp|svg|bmp)$/i),l=e=>E(e),s=r(e),a=r(t);if(s&&!a)return -1;if(!s&&a)return 1;let i=!v&&l(e);return i===(!v&&l(t))?0:i?1:-1}),W=F.map((e,t)=>{let r=A(e),l=null!==(r.split("/").pop()||"").match(/\.(jpg|jpeg|png|gif|webp|svg|bmp)$/i),s=E(e),a=F.length%2==1&&F.length>1&&t===F.length-1;return{file:e,path:r,isImage:l,isPreviewFile:s,shouldSpanFull:a,wrapperClassName:(0,d.cn)("relative group",l?"flex items-center justify-center h-full":"",s&&!v?"w-full":""),wrapperStyle:a||s&&!v?{gridColumn:"1 / -1"}:void 0}});return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(a.N,{children:(0,l.jsx)(s.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"inline"===h?"pt-1.5 px-1.5 pb-0":"mt-4",children:"grid"===h?(F.length%2==1&&F.length,(0,l.jsx)("div",{className:(0,d.cn)("grid gap-3",1===y.length?"grid-cols-1":y.length>4?"grid-cols-1 sm:grid-cols-2 md:grid-cols-3":"grid-cols-1 sm:grid-cols-2",g),children:W.map((e,t)=>(0,l.jsxs)("div",{className:e.wrapperClassName,style:e.wrapperStyle,children:[(0,l.jsx)(c.m,{filepath:e.path,onClick:S,sandboxId:r,showPreview:f,localPreviewUrl:P(e.file),className:(0,d.cn)("w-full",e.isImage?"h-auto min-h-[54px]":e.isPreviewFile&&!v?"min-h-[240px] max-h-[400px] overflow-auto":"h-[54px]"),customStyle:e.isImage?{width:"100%",height:"auto",...{"--attachment-height":"".concat(e.shouldSpanFull?Math.floor(1.33*w):w,"px")}}:e.isPreviewFile&&!v||e.shouldSpanFull?{gridColumn:"1 / -1"}:{width:"100%"},collapsed:v,project:j}),m&&(0,l.jsx)("div",{className:"absolute -top-1 -right-1 h-5 w-5 rounded-full   bg-black dark:bg-white   border-3 border-sidebar   text-white dark:text-black flex items-center justify-center   z-30 cursor-pointer",onClick:()=>m(t),children:(0,l.jsx)(o.Bc,{children:(0,l.jsxs)(o.m_,{children:[(0,l.jsx)(o.k$,{asChild:!0,children:(0,l.jsx)("div",{className:"flex items-center justify-center w-full h-full",children:(0,l.jsx)(i.A,{size:10,strokeWidth:3})})}),(0,l.jsx)(o.ZI,{side:"top",children:(0,l.jsx)("p",{children:"Remove file"})})]})})})]},t))})):(0,l.jsxs)("div",{className:(0,d.cn)("flex flex-wrap gap-3",g),children:[T.map((e,t)=>(0,l.jsxs)("div",{className:(0,d.cn)("relative group h-[54px]",e.wrapperClassName),children:[(0,l.jsx)(c.m,{filepath:e.path,onClick:S,sandboxId:r,showPreview:f,localPreviewUrl:P(e.file),collapsed:!0}),m&&(0,l.jsx)("div",{className:"absolute -top-1 -right-1 h-5 w-5 rounded-full   bg-black dark:bg-white   border-3 border-sidebar   text-white dark:text-black flex items-center justify-center   z-30 cursor-pointer",onClick:()=>m(t),children:(0,l.jsx)(o.Bc,{children:(0,l.jsxs)(o.m_,{children:[(0,l.jsx)(o.k$,{asChild:!0,children:(0,l.jsx)("div",{className:"flex items-center justify-center w-full h-full",children:(0,l.jsx)(i.A,{size:10,strokeWidth:3})})}),(0,l.jsx)(o.ZI,{side:"top",children:(0,l.jsx)("p",{children:"Remove file"})})]})})})]},t)),U>0&&(0,l.jsx)("button",{onClick:()=>N(!0),className:(0,d.cn)("h-[54px] rounded-xl cursor-pointer","border border-black/10 dark:border-white/10","bg-black/5 dark:bg-black/20","hover:bg-primary/10 dark:hover:bg-primary/20","flex items-center justify-center transition-colors",C?"w-full":"min-w-[120px] w-fit"),title:"".concat(U," more ").concat(1===U?"file":"files"),children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("div",{className:"flex items-center justify-center w-6 h-6 bg-primary/10 rounded-full",children:(0,l.jsx)(n.A,{size:14,className:"text-primary"})}),(0,l.jsxs)("span",{className:"text-sm font-medium",children:[U," more"]})]})})]})})}),(0,l.jsx)(p.lG,{open:k,onOpenChange:N,children:(0,l.jsxs)(p.Cf,{className:"max-w-3xl max-h-[80vh] overflow-y-auto",children:[(0,l.jsx)(p.c7,{className:"mb-1",children:(0,l.jsx)(p.L3,{children:(0,l.jsxs)("span",{children:["All Files (",y.length,")"]})})}),(0,l.jsx)("div",{className:(0,d.cn)("grid gap-3 sm:justify-start justify-center sm:max-w-full max-w-[300px] mx-auto sm:mx-0",1===y.length?"grid-cols-1":y.length>4?"grid-cols-1 sm:grid-cols-2 md:grid-cols-3":"grid-cols-1 sm:grid-cols-2"),children:(()=>{let e=[...y].sort((e,t)=>{let r=e=>null!==(A(e).split("/").pop()||"").match(/\.(jpg|jpeg|png|gif|webp|svg|bmp)$/i),l=r(e),s=r(t);return l&&!s?-1:!l&&s?1:0}),t=e.map(e=>y.findIndex(t=>A(t)===A(e)));return e.map((e,r)=>{let l=t[r],s=A(e),a=null!==(s.split("/").pop()||"").match(/\.(jpg|jpeg|png|gif|webp|svg|bmp)$/i);return{file:e,path:s,isImage:a,originalIndex:l,wrapperClassName:(0,d.cn)("relative group",a?"flex items-center justify-center h-full":""),fileClassName:(0,d.cn)("w-full",a?"h-auto min-h-[54px]":"h-[54px]"),customStyle:a?{width:"100%",height:"auto","--attachment-height":"".concat(w,"px")}:{width:"100%"}}})})().map(e=>(0,l.jsxs)("div",{className:e.wrapperClassName,children:[(0,l.jsx)(c.m,{filepath:e.path,onClick:e=>{S(e),N(!1)},sandboxId:r,showPreview:f,localPreviewUrl:P(e.file),className:e.fileClassName,customStyle:e.customStyle,collapsed:!0,project:j}),m&&(0,l.jsx)("div",{className:"absolute -top-1 -right-1 h-5 w-5 rounded-full   bg-black dark:bg-white   border-3 border-sidebar   text-white dark:text-black flex items-center justify-center   z-30 cursor-pointer",onClick:()=>{m(e.originalIndex),y.length<=1&&N(!1)},children:(0,l.jsx)(o.Bc,{children:(0,l.jsxs)(o.m_,{children:[(0,l.jsx)(o.k$,{asChild:!0,children:(0,l.jsx)("div",{className:"flex items-center justify-center w-full h-full",children:(0,l.jsx)(i.A,{size:10,strokeWidth:3})})}),(0,l.jsx)(o.ZI,{side:"top",children:(0,l.jsx)("p",{children:"Remove file"})})]})})})]},e.originalIndex))})]})})]})}},90066:(e,t,r)=>{r.d(t,{L:()=>l});let l=e=>{try{return e.normalize("NFC")}catch(t){return console.warn("Failed to normalize filename to NFC:",e,t),e}}}}]);