(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9645],{1243:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("Triangle<PERSON>lert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>l,t:()=>i});var n=r(12115);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}function l(...e){return n.useCallback(i(...e),e)}},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(12115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:s=2,absoluteStrokeWidth:o,className:d="",children:u,iconNode:c,...f}=e;return(0,n.createElement)("svg",{ref:t,...l,width:a,height:a,stroke:r,strokeWidth:o?24*Number(s)/Number(a):s,className:i("lucide",d),...f},[...c.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),o=(e,t)=>{let r=(0,n.forwardRef)((r,l)=>{let{className:o,...d}=r;return(0,n.createElement)(s,{ref:l,iconNode:t,className:i("lucide-".concat(a(e)),o),...d})});return r.displayName="".concat(e),r}},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>o,r:()=>s});var n=r(95155);r(12115);var a=r(99708),i=r(74466),l=r(59434);let s=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",node_outline:"bg-transparent border border-primary/10",node_secondary:"px-0 bg-transparent hover:opacity-60",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-lg gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-lg px-6 has-[>svg]:px-4",icon:"size-9",node_secondary:"px-0"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:i,asChild:o=!1,...d}=e,u=o?a.DX:"button";return(0,n.jsx)(u,{"data-slot":"button",className:(0,l.cn)(s({variant:r,size:i,className:t})),...d})}},35706:(e,t,r)=>{"use strict";r.d(t,{SubmitButton:()=>u});var n=r(95155),a=r(47650),i=r(12115),l=r(30285),s=r(55365),o=r(1243);let d={message:""};function u(e){let{children:t,formAction:r,errorMessage:u,pendingText:c="Submitting...",...f}=e,{pending:v,action:p}=(0,a.useFormStatus)(),[g,m]=(0,i.useActionState)(r,d),h=v&&p===m;return(0,n.jsxs)("div",{className:"flex flex-col gap-y-4 w-full",children:[!!(u||(null==g?void 0:g.message))&&(0,n.jsxs)(s.Fc,{variant:"destructive",className:"w-full",children:[(0,n.jsx)(o.A,{className:"h-4 w-4"}),(0,n.jsx)(s.TN,{children:u||(null==g?void 0:g.message)})]}),(0,n.jsx)("div",{children:(0,n.jsx)(l.$,{...f,type:"submit","aria-disabled":v,formAction:m,children:h?c:t})})]})}},40968:(e,t,r)=>{"use strict";r.d(t,{b:()=>s});var n=r(12115),a=r(63655),i=r(95155),l=n.forwardRef((e,t)=>(0,i.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var s=l},55365:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>s,TN:()=>d,XL:()=>o});var n=r(95155);r(12115);var a=r(74466),i=r(59434);let l=(0,a.F)("relative w-full rounded-xl border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function s(e){let{className:t,variant:r,...a}=e;return(0,n.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(l({variant:r}),t),...a})}function o(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"alert-title",className:(0,i.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...r})}function d(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r})}},59434:(e,t,r)=>{"use strict";r.d(t,{$3:()=>o,Hz:()=>s,W5:()=>d,cn:()=>l});var n=r(52596),a=r(81949),i=r(39688);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.QP)((0,n.$)(t))}let s=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"rgba(180, 180, 180)";if(!e)return t;try{if("string"==typeof e&&e.startsWith("var(")){let t=document.createElement("div");t.style.color=e,document.body.appendChild(t);let r=window.getComputedStyle(t).color;return document.body.removeChild(t),a.formatRGBA(a.parse(r))}return a.formatRGBA(a.parse(e))}catch(e){return console.error("Color parsing failed:",e),t}},o=(e,t)=>e.startsWith("rgb")?a.formatRGBA(a.alpha(a.parse(e),t)):e;function d(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return e.length<=t?e:e.slice(0,t)+"..."}},63655:(e,t,r)=>{"use strict";r.d(t,{hO:()=>o,sG:()=>s});var n=r(12115),a=r(47650),i=r(99708),l=r(95155),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),a=n.forwardRef((e,n)=>{let{asChild:a,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(a?r:t,{...i,ref:n})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function o(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>l});var n=r(52596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.$,l=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:s}=t,o=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],n=null==s?void 0:s[e];if(null===t)return null;let i=a(t)||a(n);return l[e][i]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return i(e,o,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...s,...d}[t]):({...s,...d})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},76554:(e,t,r)=>{Promise.resolve().then(r.bind(r,85057)),Promise.resolve().then(r.bind(r,35706))},85057:(e,t,r)=>{"use strict";r.d(t,{Label:()=>l});var n=r(95155);r(12115);var a=r(40968),i=r(59434);function l(e){let{className:t,...r}=e;return(0,n.jsx)(a.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>s,Dc:()=>d,TL:()=>l});var n=r(12115),a=r(6101),i=r(95155);function l(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){var l;let e,s,o=(l=r,(s=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(s=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),d=function(e,t){let r={...t};for(let n in t){let a=e[n],i=t[n];/^on[A-Z]/.test(n)?a&&i?r[n]=(...e)=>{let t=i(...e);return a(...e),t}:a&&(r[n]=a):"style"===n?r[n]={...a,...i}:"className"===n&&(r[n]=[a,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(d.ref=t?(0,a.t)(t,o):o),n.cloneElement(r,d)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:a,...l}=e,s=n.Children.toArray(a),o=s.find(u);if(o){let e=o.props.children,a=s.map(t=>t!==o?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,i.jsx)(t,{...l,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var s=l("Slot"),o=Symbol("radix.slottable");function d(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=o,t}function u(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}}},e=>{var t=t=>e(e.s=t);e.O(0,[2969,8441,1684,7358],()=>t(76554)),_N_E=e.O()}]);