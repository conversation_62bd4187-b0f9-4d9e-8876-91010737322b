(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/utils.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "cn": (()=>cn),
    "colorWithOpacity": (()=>colorWithOpacity),
    "focusInput": (()=>focusInput),
    "focusRing": (()=>focusRing),
    "getRGBA": (()=>getRGBA),
    "hasErrorInput": (()=>hasErrorInput),
    "truncateString": (()=>truncateString)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$color$2d$bits$2f$build$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/color-bits/build/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-client] (ecmascript)");
;
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
const getRGBA = (cssColor, fallback = 'rgba(180, 180, 180)')=>{
    if ("TURBOPACK compile-time falsy", 0) {
        "TURBOPACK unreachable";
    }
    if (!cssColor) return fallback;
    try {
        // Handle CSS variables
        if (typeof cssColor === 'string' && cssColor.startsWith('var(')) {
            const element = document.createElement('div');
            element.style.color = cssColor;
            document.body.appendChild(element);
            const computedColor = window.getComputedStyle(element).color;
            document.body.removeChild(element);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$color$2d$bits$2f$build$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatRGBA"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$color$2d$bits$2f$build$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parse"])(computedColor));
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$color$2d$bits$2f$build$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatRGBA"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$color$2d$bits$2f$build$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parse"])(cssColor));
    } catch (e) {
        console.error('Color parsing failed:', e);
        return fallback;
    }
};
const colorWithOpacity = (color, opacity)=>{
    if (!color.startsWith('rgb')) return color;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$color$2d$bits$2f$build$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatRGBA"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$color$2d$bits$2f$build$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["alpha"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$color$2d$bits$2f$build$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parse"])(color), opacity));
};
const focusInput = [
    // base
    'focus:ring-2',
    // ring color
    'focus:ring-blue-200 focus:dark:ring-blue-700/30',
    // border color
    'focus:border-blue-500 focus:dark:border-blue-700'
];
const focusRing = [
    // base
    'outline outline-offset-2 outline-0 focus-visible:outline-2',
    // outline color
    'outline-blue-500 dark:outline-blue-500'
];
const hasErrorInput = [
    // base
    'ring-2',
    // border color
    'border-red-500 dark:border-red-700',
    // ring color
    'ring-red-200 dark:ring-red-700/30'
];
function truncateString(str, maxLength = 50) {
    if (str.length <= maxLength) return str;
    return str.slice(0, maxLength) + '...';
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/home/<USER>/flickering-grid.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "FlickeringGrid": (()=>FlickeringGrid)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
const FlickeringGrid = ({ squareSize = 3, gridGap = 3, flickerChance = 0.2, color = '#B4B4B4', width, height, className, maxOpacity = 0.15, text = '', fontSize = 140, fontWeight = 600, ...props })=>{
    _s();
    const canvasRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const containerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const animationRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const lastRenderTimeRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const lastResizeTimeRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const gridParamsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [isInView, setIsInView] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [canvasSize, setCanvasSize] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        width: 0,
        height: 0
    });
    // Throttle rendering to improve performance - adjust ms as needed
    const FRAME_THROTTLE = 50; // Only render every ~50ms (20fps instead of 60fps)
    const RESIZE_THROTTLE = 200; // Throttle resize events
    // Convert any CSS color to rgba for optimal canvas performance
    const memoizedColor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "FlickeringGrid.useMemo[memoizedColor]": ()=>{
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getRGBA"])(color);
        }
    }["FlickeringGrid.useMemo[memoizedColor]"], [
        color
    ]);
    const drawGrid = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FlickeringGrid.useCallback[drawGrid]": (ctx, width, height, cols, rows, squares, dpr)=>{
            ctx.clearRect(0, 0, width, height);
            // Create a separate canvas for the text mask if needed
            let maskCanvas = null;
            let maskCtx = null;
            if (text) {
                maskCanvas = document.createElement('canvas');
                maskCanvas.width = width;
                maskCanvas.height = height;
                maskCtx = maskCanvas.getContext('2d', {
                    willReadFrequently: true
                });
                if (maskCtx) {
                    // Draw text on mask canvas
                    maskCtx.save();
                    maskCtx.scale(dpr, dpr);
                    maskCtx.fillStyle = 'white';
                    maskCtx.font = `${fontWeight} ${fontSize}px "Geist", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif`;
                    maskCtx.textAlign = 'center';
                    maskCtx.textBaseline = 'middle';
                    maskCtx.fillText(text, width / (2 * dpr), height / (2 * dpr));
                    maskCtx.restore();
                }
            }
            // Batch squares by opacity for better performance
            const opacityMap = new Map();
            for(let i = 0; i < cols; i++){
                for(let j = 0; j < rows; j++){
                    const x = i * (squareSize + gridGap) * dpr;
                    const y = j * (squareSize + gridGap) * dpr;
                    const squareWidth = squareSize * dpr;
                    const squareHeight = squareSize * dpr;
                    let hasText = false;
                    if (maskCtx && maskCanvas) {
                        const maskData = maskCtx.getImageData(x, y, squareWidth, squareHeight).data;
                        hasText = maskData.some({
                            "FlickeringGrid.useCallback[drawGrid]": (value, index)=>index % 4 === 0 && value > 0
                        }["FlickeringGrid.useCallback[drawGrid]"]);
                    }
                    const opacity = squares[i * rows + j];
                    const finalOpacity = hasText ? Math.min(1, opacity * 3 + 0.4) : opacity;
                    // Round opacity to 2 decimal places for batching
                    const roundedOpacity = Math.round(finalOpacity * 100) / 100;
                    if (!opacityMap.has(roundedOpacity)) {
                        opacityMap.set(roundedOpacity, []);
                    }
                    opacityMap.get(roundedOpacity)?.push({
                        x,
                        y
                    });
                }
            }
            // Draw squares by opacity batch
            for (const [opacity, squares] of opacityMap.entries()){
                ctx.fillStyle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["colorWithOpacity"])(memoizedColor, opacity);
                for (const { x, y } of squares){
                    ctx.fillRect(x, y, squareSize * dpr, squareSize * dpr);
                }
            }
        }
    }["FlickeringGrid.useCallback[drawGrid]"], [
        memoizedColor,
        squareSize,
        gridGap,
        text,
        fontSize,
        fontWeight
    ]);
    const setupCanvas = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FlickeringGrid.useCallback[setupCanvas]": (canvas, width, height)=>{
            const dpr = window.devicePixelRatio || 1;
            canvas.width = width * dpr;
            canvas.height = height * dpr;
            canvas.style.width = `${width}px`;
            canvas.style.height = `${height}px`;
            const cols = Math.ceil(width / (squareSize + gridGap));
            const rows = Math.ceil(height / (squareSize + gridGap));
            // Check if we should preserve the existing grid state
            if (gridParamsRef.current && gridParamsRef.current.cols === cols && gridParamsRef.current.rows === rows) {
                // Use existing squares array to maintain state
                return {
                    cols,
                    rows,
                    squares: gridParamsRef.current.squares,
                    dpr
                };
            }
            // Create new squares array only if needed
            const squares = new Float32Array(cols * rows);
            for(let i = 0; i < squares.length; i++){
                squares[i] = Math.random() * maxOpacity;
            }
            return {
                cols,
                rows,
                squares,
                dpr
            };
        }
    }["FlickeringGrid.useCallback[setupCanvas]"], [
        squareSize,
        gridGap,
        maxOpacity
    ]);
    const updateSquares = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FlickeringGrid.useCallback[updateSquares]": (squares, deltaTime)=>{
            // Only update if flickerChance is greater than 0
            if (flickerChance <= 0) return;
            for(let i = 0; i < squares.length; i++){
                if (Math.random() < flickerChance * deltaTime) {
                    squares[i] = Math.random() * maxOpacity;
                }
            }
        }
    }["FlickeringGrid.useCallback[updateSquares]"], [
        flickerChance,
        maxOpacity
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FlickeringGrid.useEffect": ()=>{
            const canvas = canvasRef.current;
            const container = containerRef.current;
            if (!canvas || !container) return;
            const ctx = canvas.getContext('2d', {
                alpha: true
            });
            if (!ctx) return;
            const updateCanvasSize = {
                "FlickeringGrid.useEffect.updateCanvasSize": ()=>{
                    const now = performance.now();
                    if (now - lastResizeTimeRef.current < RESIZE_THROTTLE) return;
                    lastResizeTimeRef.current = now;
                    const newWidth = width || container.clientWidth;
                    const newHeight = height || container.clientHeight;
                    // Only update if size changed to prevent unnecessary redraws
                    if (canvasSize.width !== newWidth || canvasSize.height !== newHeight) {
                        setCanvasSize({
                            width: newWidth,
                            height: newHeight
                        });
                        // Don't recreate grid if sizes are similar (within 10px)
                        const shouldPreserveGrid = gridParamsRef.current && Math.abs(gridParamsRef.current.cols * (squareSize + gridGap) - newWidth) < 10 && Math.abs(gridParamsRef.current.rows * (squareSize + gridGap) - newHeight) < 10;
                        if (!shouldPreserveGrid) {
                            gridParamsRef.current = setupCanvas(canvas, newWidth, newHeight);
                        } else {
                            // Just update canvas dimensions without recreating grid
                            const dpr = window.devicePixelRatio || 1;
                            canvas.width = newWidth * dpr;
                            canvas.height = newHeight * dpr;
                            canvas.style.width = `${newWidth}px`;
                            canvas.style.height = `${newHeight}px`;
                        }
                    }
                }
            }["FlickeringGrid.useEffect.updateCanvasSize"];
            // Initialize canvas size and grid params if needed
            if (!gridParamsRef.current) {
                updateCanvasSize();
            }
            let lastTime = 0;
            const animate = {
                "FlickeringGrid.useEffect.animate": (time)=>{
                    if (!isInView) {
                        animationRef.current = requestAnimationFrame(animate);
                        return;
                    }
                    // Throttle to improve performance
                    if (time - lastRenderTimeRef.current < FRAME_THROTTLE) {
                        animationRef.current = requestAnimationFrame(animate);
                        return;
                    }
                    // Safety check
                    if (!gridParamsRef.current || !gridParamsRef.current.squares) {
                        updateCanvasSize();
                        animationRef.current = requestAnimationFrame(animate);
                        return;
                    }
                    lastRenderTimeRef.current = time;
                    const deltaTime = (time - lastTime) / 1000;
                    lastTime = time;
                    updateSquares(gridParamsRef.current.squares, deltaTime);
                    drawGrid(ctx, canvas.width, canvas.height, gridParamsRef.current.cols, gridParamsRef.current.rows, gridParamsRef.current.squares, gridParamsRef.current.dpr);
                    animationRef.current = requestAnimationFrame(animate);
                }
            }["FlickeringGrid.useEffect.animate"];
            // Use a gentle resize observer that doesn't completely redraw everything
            const resizeObserver = new ResizeObserver({
                "FlickeringGrid.useEffect": ()=>{
                    const now = performance.now();
                    if (now - lastResizeTimeRef.current < RESIZE_THROTTLE) return;
                    const newWidth = width || container.clientWidth;
                    const newHeight = height || container.clientHeight;
                    // Only update if dimensions actually changed significantly (at least 5px difference)
                    if (Math.abs(canvasSize.width - newWidth) > 5 || Math.abs(canvasSize.height - newHeight) > 5) {
                        updateCanvasSize();
                    }
                }
            }["FlickeringGrid.useEffect"]);
            resizeObserver.observe(container);
            const intersectionObserver = new IntersectionObserver({
                "FlickeringGrid.useEffect": ([entry])=>{
                    setIsInView(entry.isIntersecting);
                }
            }["FlickeringGrid.useEffect"], {
                threshold: 0.1,
                rootMargin: '50px'
            });
            intersectionObserver.observe(canvas);
            // Start animation if in view
            if (isInView) {
                animationRef.current = requestAnimationFrame(animate);
            }
            return ({
                "FlickeringGrid.useEffect": ()=>{
                    cancelAnimationFrame(animationRef.current);
                    resizeObserver.disconnect();
                    intersectionObserver.disconnect();
                }
            })["FlickeringGrid.useEffect"];
        }
    }["FlickeringGrid.useEffect"], [
        setupCanvas,
        updateSquares,
        drawGrid,
        width,
        height,
        isInView,
        squareSize,
        gridGap
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: containerRef,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])(`h-full w-full ${className}`),
        ...props,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("canvas", {
            ref: canvasRef,
            className: "pointer-events-none",
            style: {
                width: canvasSize.width,
                height: canvasSize.height
            }
        }, void 0, false, {
            fileName: "[project]/src/components/home/<USER>/flickering-grid.tsx",
            lineNumber: 337,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/home/<USER>/flickering-grid.tsx",
        lineNumber: 332,
        columnNumber: 5
    }, this);
};
_s(FlickeringGrid, "RzznyT0qgOzewLpTQutDGZgiR0Q=");
_c = FlickeringGrid;
var _c;
__turbopack_context__.k.register(_c, "FlickeringGrid");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/use-media-query.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useMediaQuery": (()=>useMediaQuery)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
function useMediaQuery(query) {
    _s();
    const [matches, setMatches] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useMediaQuery.useEffect": ()=>{
            const media = window.matchMedia(query);
            if (media.matches !== matches) {
                setMatches(media.matches);
            }
            const listener = {
                "useMediaQuery.useEffect.listener": ()=>setMatches(media.matches)
            }["useMediaQuery.useEffect.listener"];
            media.addEventListener('change', listener);
            return ({
                "useMediaQuery.useEffect": ()=>media.removeEventListener('change', listener)
            })["useMediaQuery.useEffect"];
        }
    }["useMediaQuery.useEffect"], [
        matches,
        query
    ]);
    return matches;
}
_s(useMediaQuery, "/aV7jSECvYA0Ea4uAEPK2AzROhs=");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/not-found.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>NotFound)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/arrow-left.js [app-client] (ecmascript) <export default as ArrowLeft>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$motion$2f$dist$2f$es$2f$framer$2d$motion$2f$dist$2f$es$2f$value$2f$use$2d$scroll$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/motion/dist/es/framer-motion/dist/es/value/use-scroll.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$home$2f$ui$2f$flickering$2d$grid$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/home/<USER>/flickering-grid.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$media$2d$query$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/use-media-query.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
function NotFound() {
    _s();
    const tablet = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$media$2d$query$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMediaQuery"])('(max-width: 1024px)');
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isScrolling, setIsScrolling] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const scrollTimeout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const { scrollY } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$motion$2f$dist$2f$es$2f$framer$2d$motion$2f$dist$2f$es$2f$value$2f$use$2d$scroll$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useScroll"])();
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NotFound.useEffect": ()=>{
            setMounted(true);
        }
    }["NotFound.useEffect"], []);
    // Detect when scrolling is active to reduce animation complexity
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "NotFound.useEffect": ()=>{
            const unsubscribe = scrollY.on('change', {
                "NotFound.useEffect.unsubscribe": ()=>{
                    setIsScrolling(true);
                    // Clear any existing timeout
                    if (scrollTimeout.current) {
                        clearTimeout(scrollTimeout.current);
                    }
                    // Set a new timeout
                    scrollTimeout.current = setTimeout({
                        "NotFound.useEffect.unsubscribe": ()=>{
                            setIsScrolling(false);
                        }
                    }["NotFound.useEffect.unsubscribe"], 300); // Wait 300ms after scroll stops
                }
            }["NotFound.useEffect.unsubscribe"]);
            return ({
                "NotFound.useEffect": ()=>{
                    unsubscribe();
                    if (scrollTimeout.current) {
                        clearTimeout(scrollTimeout.current);
                    }
                }
            })["NotFound.useEffect"];
        }
    }["NotFound.useEffect"], [
        scrollY
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("section", {
        className: "w-full relative overflow-hidden min-h-screen flex items-center justify-center",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "relative flex flex-col items-center w-full px-6",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "absolute left-0 top-0 h-full w-1/3 -z-10 overflow-hidden",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-background z-10"
                        }, void 0, false, {
                            fileName: "[project]/src/app/not-found.tsx",
                            lineNumber: 51,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10"
                        }, void 0, false, {
                            fileName: "[project]/src/app/not-found.tsx",
                            lineNumber: 54,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10"
                        }, void 0, false, {
                            fileName: "[project]/src/app/not-found.tsx",
                            lineNumber: 57,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$home$2f$ui$2f$flickering$2d$grid$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FlickeringGrid"], {
                            className: "h-full w-full",
                            squareSize: mounted && tablet ? 2 : 2.5,
                            gridGap: mounted && tablet ? 2 : 2.5,
                            color: "var(--secondary)",
                            maxOpacity: 0.4,
                            flickerChance: isScrolling ? 0.01 : 0.03
                        }, void 0, false, {
                            fileName: "[project]/src/app/not-found.tsx",
                            lineNumber: 59,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/not-found.tsx",
                    lineNumber: 49,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "absolute right-0 top-0 h-full w-1/3 -z-10 overflow-hidden",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-background z-10"
                        }, void 0, false, {
                            fileName: "[project]/src/app/not-found.tsx",
                            lineNumber: 72,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10"
                        }, void 0, false, {
                            fileName: "[project]/src/app/not-found.tsx",
                            lineNumber: 75,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10"
                        }, void 0, false, {
                            fileName: "[project]/src/app/not-found.tsx",
                            lineNumber: 78,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$home$2f$ui$2f$flickering$2d$grid$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FlickeringGrid"], {
                            className: "h-full w-full",
                            squareSize: mounted && tablet ? 2 : 2.5,
                            gridGap: mounted && tablet ? 2 : 2.5,
                            color: "var(--secondary)",
                            maxOpacity: 0.4,
                            flickerChance: isScrolling ? 0.01 : 0.03
                        }, void 0, false, {
                            fileName: "[project]/src/app/not-found.tsx",
                            lineNumber: 80,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/not-found.tsx",
                    lineNumber: 70,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "absolute inset-x-1/4 top-0 h-full -z-20 bg-background rounded-b-xl"
                }, void 0, false, {
                    fileName: "[project]/src/app/not-found.tsx",
                    lineNumber: 91,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative z-10 max-w-3xl mx-auto h-full w-full flex flex-col gap-10 items-center justify-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "inline-flex h-10 w-fit items-center justify-center gap-2 rounded-full bg-secondary/10 text-secondary px-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "text-sm font-medium",
                                children: "404 Error"
                            }, void 0, false, {
                                fileName: "[project]/src/app/not-found.tsx",
                                lineNumber: 95,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/not-found.tsx",
                            lineNumber: 94,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-col items-center justify-center gap-5",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                    className: "text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-medium tracking-tighter text-balance text-center text-primary",
                                    children: "Page not found"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/not-found.tsx",
                                    lineNumber: 99,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-base md:text-lg text-center text-muted-foreground font-medium text-balance leading-relaxed tracking-tight",
                                    children: "The page you're looking for doesn't exist or has been moved."
                                }, void 0, false, {
                                    fileName: "[project]/src/app/not-found.tsx",
                                    lineNumber: 102,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/not-found.tsx",
                            lineNumber: 98,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center w-full max-w-xl gap-2 flex-wrap justify-center",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                href: "/",
                                className: "inline-flex h-12 md:h-14 items-center justify-center gap-2 rounded-full bg-primary text-white px-6 shadow-md hover:bg-primary/90 transition-all duration-200",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$arrow$2d$left$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__ArrowLeft$3e$__["ArrowLeft"], {
                                        className: "size-4 md:size-5 dark:text-black"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/not-found.tsx",
                                        lineNumber: 111,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium dark:text-black",
                                        children: "Return Home"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/not-found.tsx",
                                        lineNumber: 112,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/not-found.tsx",
                                lineNumber: 107,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/not-found.tsx",
                            lineNumber: 106,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute -bottom-4 inset-x-0 h-6 bg-secondary/20 blur-xl rounded-full -z-10 opacity-70"
                        }, void 0, false, {
                            fileName: "[project]/src/app/not-found.tsx",
                            lineNumber: 117,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/not-found.tsx",
                    lineNumber: 93,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/not-found.tsx",
            lineNumber: 47,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/not-found.tsx",
        lineNumber: 46,
        columnNumber: 5
    }, this);
}
_s(NotFound, "EZ2i4QGAaFL884eJcTKriHGhKQs=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$use$2d$media$2d$query$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMediaQuery"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$motion$2f$dist$2f$es$2f$framer$2d$motion$2f$dist$2f$es$2f$value$2f$use$2d$scroll$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useScroll"]
    ];
});
_c = NotFound;
var _c;
__turbopack_context__.k.register(_c, "NotFound");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_7920249e._.js.map