(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[506],{1632:(e,t,a)=>{"use strict";a.r(t),a.d(t,{ComponentMod:()=>k,default:()=>P});var r,i={};a.r(i),a.d(i,{GET:()=>h,revalidate:()=>g,runtime:()=>m});var s={};a.r(s),a.d(s,{patchFetch:()=>S,routeModule:()=>x,serverHooks:()=>y,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>b});var o=a(8429),n=a(9874),c=a(8294),l=a(6567),p=a(4144),f=a(5421),u=a(974),d=a(5356).Buffer;let m="edge",g=3600;async function h(e){let{searchParams:t}=new URL(e.url),a=t.get("title");if(!a)return new u.Rp("Missing title parameter",{status:400});try{let e=await fetch("https://api.orshot.com/v1/studio/render",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${process.env.ORSHOT_API_KEY}`},body:JSON.stringify({templateId:10,modifications:{title:a},response:{type:"binary"}})});if(!e.ok)throw Error(`Orshot API error: ${e.status}`);let t=await e.blob(),r=await t.arrayBuffer(),i=d.from(r);return new u.Rp(i,{status:200,headers:{"Content-Type":"image/png","Cache-Control":"public, max-age=3600, stale-while-revalidate=86400"}})}catch(e){return console.error("OG Image generation error:",e),new u.Rp("Error generating image",{status:500})}}let x=new l.AppRouteRouteModule({definition:{kind:p.A.APP_ROUTE,page:"/api/share-page/og-image/route",pathname:"/api/share-page/og-image",filename:"route",bundlePath:"app/api/share-page/og-image/route"},resolvedPagePath:"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\api\\share-page\\og-image\\route.tsx",nextConfigOutput:"",userland:i}),{workAsyncStorage:v,workUnitAsyncStorage:b,serverHooks:y}=x;function S(){return(0,f.V5)({workAsyncStorage:v,workUnitAsyncStorage:b})}let w=null==(r=self.__RSC_MANIFEST)?void 0:r["/api/share-page/og-image/route"],C=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);w&&C&&(0,n.fQ)({page:"/api/share-page/og-image/route",clientReferenceManifest:w,serverActionsManifest:C,serverModuleMap:(0,o.e)({serverActionsManifest:C})});let k=s,P=c.s.wrap(x,{nextConfig:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[],unoptimized:!1},devIndicators:{position:"bottom-left"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"C:\\Users\\<USER>\\suna\\frontend",experimental:{nodeMiddleware:!1,cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,dynamicOnHover:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:7,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,viewTransition:!1,routerBFCache:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,useCache:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},htmlLimitedBots:"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti",bundlePagesRouterDependencies:!1,configFile:"C:\\Users\\<USER>\\suna\\frontend\\next.config.ts",configFileName:"next.config.ts",turbopack:{root:"C:\\Users\\<USER>\\suna\\frontend"}}})},5356:e=>{"use strict";e.exports=require("node:buffer")},5521:e=>{"use strict";e.exports=require("node:async_hooks")},6487:()=>{},8335:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[580],()=>t(1632));var a=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/api/share-page/og-image/route"]=a}]);
//# sourceMappingURL=route.js.map