(()=>{var e={};e.id=9718,e.ids=[9718],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8719:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isRequestAPICallableInsideAfter:function(){return c},throwForSearchParamsAccessInUseCache:function(){return i},throwWithStaticGenerationBailoutError:function(){return s},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return a}});let n=r(80023),o=r(3295);function s(e,t){throw Object.defineProperty(new n.StaticGenBailoutError(`Route ${e} couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function a(e,t){throw Object.defineProperty(new n.StaticGenBailoutError(`Route ${e} with \`dynamic = "error"\` couldn't be rendered statically because it used ${t}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function i(e){let t=Object.defineProperty(Error(`Route ${e.route} used "searchParams" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "searchParams" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E634",enumerable:!1,configurable:!0});throw e.invalidUsageError??=t,t}function c(){let e=o.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},43763:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ReflectAdapter",{enumerable:!0,get:function(){return r}});class r{static get(e,t,r){let n=Reflect.get(e,t,r);return"function"==typeof n?n.bind(e):n}static set(e,t,r,n){return Reflect.set(e,t,r,n)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72609:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{describeHasCheckingStringProperty:function(){return o},describeStringPropertyAccess:function(){return n},wellKnownProperties:function(){return s}});let r=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function n(e,t){return r.test(t)?"`"+e+"."+t+"`":"`"+e+"["+JSON.stringify(t)+"]`"}function o(e,t){let r=JSON.stringify(t);return"`Reflect.has("+e+", "+r+")`, `"+r+" in "+e+"`, or similar"}let s=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},78335:()=>{},90562:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>h,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>l});var n={};r.r(n),r.d(n,{GET:()=>u,POST:()=>c});var o=r(96559),s=r(48088),a=r(37719),i=r(32190);async function c(e){try{let t=await e.arrayBuffer(),r={};e.headers.forEach((e,t)=>{["host","content-length","transfer-encoding","connection"].includes(t.toLowerCase())||(r[t]=e)});let n=process.env.BACKEND_URL||"http://localhost:8000",o=`${n}/triggers/qstash/webhook`,s=await fetch(o,{method:"POST",headers:{...r,"Content-Type":r["content-type"]||"application/json"},body:t}),a=await s.text();return new i.NextResponse(a,{status:s.status,headers:{"Content-Type":s.headers.get("Content-Type")||"application/json"}})}catch(e){return console.error("[QStash Webhook Proxy] Error:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function u(e){return i.NextResponse.json({status:"ok",service:"qstash-webhook-proxy"})}let d=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/triggers/qstash/webhook/route",pathname:"/api/triggers/qstash/webhook",filename:"route",bundlePath:"app/api/triggers/qstash/webhook/route"},resolvedPagePath:"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\api\\triggers\\qstash\\webhook\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:p,workUnitAsyncStorage:l,serverHooks:h}=d;function f(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:l})}},96487:()=>{}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[7719,580],()=>r(90562));module.exports=n})();