(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5360,7813],{7259:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var s=r(95155),n=r(12115),i=r(63554),a=r(52643),l=r(51362),o=r(49509);function c(e){let{returnUrl:t}=e,r=o.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,[c,d]=(0,n.useState)(!1),{resolvedTheme:u}=(0,l.D)(),m=(0,n.useCallback)(async e=>{try{d(!0);let r=(0,a.U)();console.log("Starting Google sign in process");let{error:s}=await r.auth.signInWithIdToken({provider:"google",token:e.credential});if(s)throw s;console.log("Google sign in successful, preparing redirect to:",t||"/dashboard"),setTimeout(()=>{console.log("Executing redirect now to:",t||"/dashboard"),window.location.href=t||"/dashboard"},500)}catch(e){console.error("Error signing in with Google:",e),d(!1)}},[t]);return((0,n.useEffect)(()=>(window.handleGoogleSignIn=m,window.google&&r&&window.google.accounts.id.initialize({client_id:r,callback:m,use_fedcm:!0,context:"signin",itp_support:!0}),()=>{delete window.handleGoogleSignIn,window.google&&window.google.accounts.id.cancel()}),[r,m]),r)?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{id:"g_id_onload","data-client_id":r,"data-context":"signin","data-ux_mode":"popup","data-auto_prompt":"false","data-itp_support":"true","data-callback":"handleGoogleSignIn"}),(0,s.jsx)("div",{id:"google-signin-button",className:"w-full h-12"}),(0,s.jsx)(i.default,{src:"https://accounts.google.com/gsi/client",strategy:"afterInteractive",onLoad:()=>{if(window.google&&r){let e=document.getElementById("google-signin-button");e&&(window.google.accounts.id.renderButton(e,{type:"standard",theme:"dark"===u?"filled_black":"outline",size:"large",text:"continue_with",shape:"pill",logoAlignment:"left",width:e.offsetWidth}),setTimeout(()=>{let t=e.querySelector('div[role="button"]');t instanceof HTMLElement&&(t.style.borderRadius="9999px",t.style.width="100%",t.style.height="56px",t.style.border="1px solid var(--border)",t.style.background="var(--background)",t.style.transition="all 0.2s")},100))}}})]}):(0,s.jsxs)("button",{disabled:!0,className:"w-full h-12 flex items-center justify-center gap-2 text-sm font-medium tracking-wide rounded-full bg-background border border-border opacity-60 cursor-not-allowed",children:[(0,s.jsxs)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z",fill:"#4285F4"}),(0,s.jsx)("path",{d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z",fill:"#34A853"}),(0,s.jsx)("path",{d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z",fill:"#FBBC05"}),(0,s.jsx)("path",{d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z",fill:"#EA4335"})]}),"Google Sign-In Not Configured"]})}},8518:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>K});var s=r(95155),n=r(68222),i=r(6874),a=r.n(i);function l(){let{ctaSection:e}=n.CQ;return(0,s.jsx)("section",{id:"cta",className:"flex flex-col items-center justify-center w-full pt-12 pb-12",children:(0,s.jsx)("div",{className:"w-full max-w-6xl mx-auto px-6",children:(0,s.jsx)("div",{className:"h-[400px] md:h-[400px] overflow-hidden shadow-xl w-full border border-border rounded-xl bg-secondary relative z-20",children:(0,s.jsxs)("div",{className:"absolute inset-0 -top-32 md:-top-40 flex flex-col items-center justify-center",children:[(0,s.jsx)("h1",{className:"text-white text-4xl md:text-7xl font-medium tracking-tighter max-w-xs md:max-w-xl text-center",children:e.title}),(0,s.jsxs)("div",{className:"absolute bottom-10 flex flex-col items-center justify-center gap-2",children:[(0,s.jsx)(a(),{href:e.button.href,className:"bg-white text-black font-semibold text-sm h-10 w-fit px-4 rounded-full flex items-center justify-center shadow-md",children:e.button.text}),(0,s.jsx)("span",{className:"text-white text-sm",children:e.subtext})]})]})})})})}var o=r(4654),c=r(38095),d=r(33096),u=r(66766),m=r(51362),x=r(12115);function f(){let e=(0,c.U)("(max-width: 1024px)"),{theme:t,resolvedTheme:r}=(0,m.D)(),[i,l]=(0,x.useState)(!1);return(0,x.useEffect)(()=>{l(!0)},[]),(0,s.jsxs)("footer",{id:"footer",className:"w-full pb-0",children:[(0,s.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between p-10 max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex flex-col items-start justify-start gap-y-5 max-w-xs mx-0",children:[(0,s.jsx)(a(),{href:"/",className:"flex items-center gap-2",children:(0,s.jsx)(u.default,{src:i&&"dark"===r?"/kortix-logo-white.svg":"/kortix-logo.svg",alt:"Kortix Logo",width:122,height:22,priority:!0})}),(0,s.jsx)("p",{className:"tracking-tight text-muted-foreground font-medium",children:n.CQ.hero.description}),(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)("a",{href:"https://github.com/kortix-ai/suna",target:"_blank",rel:"noopener noreferrer","aria-label":"GitHub",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"size-5 text-muted-foreground hover:text-primary transition-colors",children:(0,s.jsx)("path",{fill:"currentColor",d:"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0 1 12 6.844a9.59 9.59 0 0 1 2.504.337c1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.02 10.02 0 0 0 22 12.017C22 6.484 17.522 2 12 2z"})})}),(0,s.jsx)("a",{href:"https://x.com/kortixai",target:"_blank",rel:"noopener noreferrer","aria-label":"X (Twitter)",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"size-5 text-muted-foreground hover:text-primary transition-colors",children:(0,s.jsx)("path",{fill:"currentColor",d:"M18.901 1.153h3.68l-8.04 9.19L24 22.846h-7.406l-5.8-7.584-6.638 7.584H.474l8.6-9.83L0 1.154h7.594l5.243 6.932ZM17.61 20.644h2.039L6.486 3.24H4.298Z"})})}),(0,s.jsx)("a",{href:"https://www.linkedin.com/company/kortix/",target:"_blank",rel:"noopener noreferrer","aria-label":"LinkedIn",children:(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"size-5 text-muted-foreground hover:text-primary transition-colors",children:(0,s.jsx)("path",{fill:"currentColor",d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433a2.062 2.062 0 01-2.063-2.065 2.064 2.064 0 112.063 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})})})]})]}),(0,s.jsx)("div",{className:"pt-5 md:w-1/2",children:(0,s.jsx)("div",{className:"flex flex-col items-start justify-start md:flex-row md:items-center md:justify-between gap-y-5 lg:pl-10",children:n.CQ.footerLinks.map((e,t)=>(0,s.jsxs)("ul",{className:"flex flex-col gap-y-2",children:[(0,s.jsx)("li",{className:"mb-2 text-sm font-semibold text-primary",children:e.title}),e.links.map(e=>(0,s.jsxs)("li",{className:"group inline-flex cursor-pointer items-center justify-start gap-1 text-[15px]/snug text-muted-foreground",children:[(0,s.jsx)(a(),{href:e.url,children:e.title}),(0,s.jsx)("div",{className:"flex size-4 items-center justify-center border border-border rounded translate-x-0 transform opacity-0 transition-all duration-300 ease-out group-hover:translate-x-1 group-hover:opacity-100",children:(0,s.jsx)(d.vKP,{className:"h-4 w-4 "})})]},e.id))]},t))})})]}),(0,s.jsxs)(a(),{href:"https://www.youtube.com/watch?v=nuf5BF1jvjQ",target:"_blank",rel:"noopener noreferrer",className:"block w-full h-48 md:h-64 relative mt-24 z-0 cursor-pointer",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-transparent to-background z-10 from-40%"}),(0,s.jsx)("div",{className:"absolute inset-0 ",children:(0,s.jsx)(o.b,{text:e?"Agents":"Agents Agents Agents",fontSize:e?60:90,className:"h-full w-full",squareSize:2,gridGap:e?2:3,color:"#6B7280",maxOpacity:.3,flickerChance:.1})})]})]})}var h=r(95360),g=r(35695),p=r(64541),b=r(25731),v=r(53153),j=r(8168),w=r(7259),y=r(54165),N=r(61446),k=r(82728),_=r(42686),C=r(23843),S=r(56671),L=r(40407),E=r(60643),z=r(82814),A=r(90066),I=r(99090),M=r(58350),O=r(43319);let P=()=>(0,s.jsx)(y.LC,{className:"bg-background/40 backdrop-blur-md"}),H="pendingAgentPrompt";function F(){let{hero:e}=n.CQ,t=(0,c.U)("(max-width: 1024px)"),[r,i]=(0,x.useState)(!1),[l,d]=(0,x.useState)(!1),[u,m]=(0,x.useState)(!1),f=(0,x.useRef)(null),{scrollY:F}=(0,h.L)(),[T,G]=(0,x.useState)(""),[B,W]=(0,x.useState)(),R=(0,g.useRouter)(),{user:U,isLoading:q}=(0,p.A)(),{billingError:D,handleBillingError:Q,clearBillingError:V}=(0,k.L)(),{data:J}=(0,_.o)(),X=null==J?void 0:J.find(e=>e.personal_account),{onOpen:Y}=(0,L.h)(),$=(0,v.T)(),[Z,K]=(0,x.useState)(null),ee=(0,j.K2)(Z||""),et=(0,x.useRef)(null),{data:er}=(0,I.GQ)(M._.list({limit:100,sort_by:"name",sort_order:"asc"}),()=>(0,O.vt)({limit:100,sort_by:"name",sort_order:"asc"}),{enabled:!!U&&!q,staleTime:3e5,gcTime:6e5})();null==er||er.agents;let[es,en]=(0,x.useState)(!1);(0,x.useEffect)(()=>{i(!0)},[]),(0,x.useEffect)(()=>{let e=F.on("change",()=>{d(!0),f.current&&clearTimeout(f.current),f.current=setTimeout(()=>{d(!1)},300)});return()=>{e(),f.current&&clearTimeout(f.current)}},[F]),(0,x.useEffect)(()=>{es&&T.trim()&&localStorage.setItem(H,T.trim())},[es,T]),(0,x.useEffect)(()=>{es&&U&&!q&&(en(!1),R.push("/dashboard"))},[U,q,es,R]),(0,x.useEffect)(()=>{if(ee.data&&Z){let e=ee.data;e.project_id?R.push("/projects/".concat(e.project_id,"/thread/").concat(Z)):R.push("/agents/".concat(Z)),K(null)}},[ee.data,Z,R]);let ei=async(e,t)=>{var r,s,n,i;if((e.trim()||(null==(r=et.current)?void 0:r.getPendingFiles().length))&&!u){if(!U&&!q){localStorage.setItem(H,e.trim()),en(!0);return}m(!0);try{let r=(null==(s=et.current)?void 0:s.getPendingFiles())||[];localStorage.removeItem(H);let a=new FormData;a.append("prompt",e),B&&a.append("agent_id",B),r.forEach(e=>{let t=(0,A.L)(e.name);a.append("files",e,t)}),(null==t?void 0:t.model_name)&&a.append("model_name",t.model_name),a.append("enable_thinking",String(null!=(i=null==t?void 0:t.enable_thinking)&&i)),a.append("reasoning_effort","low"),a.append("stream","true"),a.append("enable_context_manager","false");let l=await $.mutateAsync(a);if(l.thread_id)K(l.thread_id);else throw Error("Agent initiation did not return a thread_id.");null==(n=et.current)||n.clearPendingFiles(),G("")}catch(e){if(e instanceof b.Ey)console.log("Billing error:",e.detail),Y("paymentRequiredDialog");else{let t=e instanceof TypeError&&e.message.includes("Failed to fetch");(!(0,C.Jn)()||t)&&S.oR.error(e.message||"Failed to create agent. Please try again.")}}finally{m(!1)}}};return(0,s.jsxs)("section",{id:"hero",className:"w-full relative overflow-hidden",children:[(0,s.jsxs)("div",{className:"relative flex flex-col items-center w-full px-6",children:[(0,s.jsxs)("div",{className:"absolute left-0 top-0 h-[600px] md:h-[800px] w-1/3 -z-10 overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-background z-10"}),(0,s.jsx)("div",{className:"absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10"}),(0,s.jsx)("div",{className:"absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10"}),(0,s.jsx)(o.b,{className:"h-full w-full",squareSize:r&&t?2:2.5,gridGap:r&&t?2:2.5,color:"var(--secondary)",maxOpacity:.4,flickerChance:l?.01:.03})]}),(0,s.jsxs)("div",{className:"absolute right-0 top-0 h-[600px] md:h-[800px] w-1/3 -z-10 overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-background z-10"}),(0,s.jsx)("div",{className:"absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10"}),(0,s.jsx)("div",{className:"absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10"}),(0,s.jsx)(o.b,{className:"h-full w-full",squareSize:r&&t?2:2.5,gridGap:r&&t?2:2.5,color:"var(--secondary)",maxOpacity:.4,flickerChance:l?.01:.03})]}),(0,s.jsx)("div",{className:"absolute inset-x-1/4 top-0 h-[600px] md:h-[800px] -z-20 bg-background rounded-b-xl"}),(0,s.jsxs)("div",{className:"relative z-10 pt-32 max-w-3xl mx-auto h-full w-full flex flex-col gap-10 items-center justify-center",children:[(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center gap-5 pt-16",children:[(0,s.jsxs)("h1",{className:"text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-medium tracking-tighter text-balance text-center",children:[(0,s.jsx)("span",{className:"text-secondary",children:"Suna"}),(0,s.jsx)("span",{className:"text-primary",children:", your AI Employee."})]}),(0,s.jsx)("p",{className:"text-base md:text-lg text-center text-muted-foreground font-medium text-balance leading-relaxed tracking-tight",children:e.description})]}),(0,s.jsx)("div",{className:"flex items-center w-full max-w-4xl gap-2 flex-wrap justify-center",children:(0,s.jsxs)("div",{className:"w-full relative",children:[(0,s.jsx)("div",{className:"relative z-10",children:(0,s.jsx)(z.V,{ref:et,onSubmit:ei,placeholder:"Describe what you need help with...",loading:u,disabled:u,value:T,onChange:G,isLoggedIn:!!U,selectedAgentId:B,onAgentSelect:W,autoFocus:!1})}),(0,s.jsx)("div",{className:"absolute -bottom-4 inset-x-0 h-6 bg-secondary/20 blur-xl rounded-full -z-10 opacity-70"})]})})]})]}),(0,s.jsx)("div",{className:"mb-16 sm:mt-52 max-w-4xl mx-auto"}),(0,s.jsxs)(y.lG,{open:es,onOpenChange:en,children:[(0,s.jsx)(P,{}),(0,s.jsxs)(y.Cf,{className:"sm:max-w-md rounded-xl bg-background border border-border",children:[(0,s.jsxs)(y.c7,{children:[(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsx)(y.L3,{className:"text-xl font-medium",children:"Sign in to continue"})}),(0,s.jsx)(y.rr,{className:"text-muted-foreground",children:"Sign in or create an account to talk with Suna"})]}),(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsx)(w.A,{returnUrl:"/dashboard"}),(0,s.jsx)(E.A,{returnUrl:"/dashboard"})]}),(0,s.jsxs)("div",{className:"relative my-6",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-border"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] text-muted-foreground",children:"or continue with email"})})]}),(0,s.jsxs)("div",{className:"space-y-4 pt-4",children:[(0,s.jsx)(a(),{href:"/auth?returnUrl=".concat(encodeURIComponent("/dashboard")),className:"flex h-12 items-center justify-center w-full text-center rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all shadow-md",onClick:()=>en(!1),children:"Sign in with email"}),(0,s.jsx)(a(),{href:"/auth?mode=signup&returnUrl=".concat(encodeURIComponent("/dashboard")),className:"flex h-12 items-center justify-center w-full text-center rounded-full border border-border bg-background hover:bg-accent/20 transition-all",onClick:()=>en(!1),children:"Create new account"})]}),(0,s.jsxs)("div",{className:"mt-4 text-center text-xs text-muted-foreground",children:["By continuing, you agree to our"," ",(0,s.jsx)(a(),{href:"/terms",className:"text-primary hover:underline",children:"Terms of Service"})," ","and"," ",(0,s.jsx)(a(),{href:"/privacy",className:"text-primary hover:underline",children:"Privacy Policy"})]})]})]}),(0,s.jsx)(N.i,{message:null==D?void 0:D.message,currentUsage:null==D?void 0:D.currentUsage,limit:null==D?void 0:D.limit,accountId:null==X?void 0:X.account_id,onDismiss:V,isOpen:!!D})]})}var T=r(7601),G=r(59099);function B(){return(0,s.jsx)("section",{id:"open-source",className:"flex flex-col items-center justify-center w-full relative pb-18",children:(0,s.jsxs)("div",{className:"w-full max-w-6xl mx-auto px-6",children:[(0,s.jsxs)(T.X,{children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-medium tracking-tighter text-center text-balance pb-1",children:"100% Open Source"}),(0,s.jsx)("p",{className:"text-muted-foreground text-center text-balance font-medium",children:"Suna is fully open source. Join our community and help shape the future of AI."})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 pt-12",children:[(0,s.jsx)("div",{className:"rounded-xl bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] border border-border p-6",children:(0,s.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 text-primary font-medium",children:[(0,s.jsx)(G.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{children:"kortix-ai/suna"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("h3",{className:"text-2xl font-semibold tracking-tight",children:"The Generalist AI Agent"}),(0,s.jsx)("p",{className:"text-muted-foreground mt-2",children:"Explore, contribute, or fork our repository. Suna is built with transparency and collaboration at its core."})]}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,s.jsx)("span",{className:"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold bg-secondary/10 border-secondary/20 text-secondary",children:"TypeScript"}),(0,s.jsx)("span",{className:"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold bg-secondary/10 border-secondary/20 text-secondary",children:"Python"}),(0,s.jsx)("span",{className:"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold bg-secondary/10 border-secondary/20 text-secondary",children:"Apache 2.0 License"})]}),(0,s.jsxs)(a(),{href:"https://github.com/Kortix-ai/Suna",target:"_blank",rel:"noopener noreferrer",className:"group inline-flex h-10 items-center justify-center gap-2 text-sm font-medium tracking-wide rounded-full text-primary-foreground dark:text-black px-6 shadow-[inset_0_1px_2px_rgba(255,255,255,0.25),0_3px_3px_-1.5px_rgba(16,24,40,0.06),0_1px_1px_rgba(16,24,40,0.08)] bg-primary dark:bg-white hover:bg-primary/90 dark:hover:bg-white/90 transition-all duration-200 w-fit",children:[(0,s.jsx)("span",{children:"View on GitHub"}),(0,s.jsx)("span",{className:"inline-flex items-center justify-center size-5 rounded-full bg-white/20 dark:bg-black/10 group-hover:bg-white/30 dark:group-hover:bg-black/20 transition-colors duration-200",children:(0,s.jsx)("svg",{width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-primary-foreground dark:text-black",children:(0,s.jsx)("path",{d:"M7 17L17 7M17 7H8M17 7V16",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})})]})]})}),(0,s.jsx)("div",{className:"rounded-xl bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] border border-border p-6",children:(0,s.jsxs)("div",{className:"flex flex-col gap-6",children:[(0,s.jsx)("h3",{className:"text-xl md:text-2xl font-medium tracking-tight",children:"Transparency & Trust"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"We believe AI should be open and accessible to everyone. Our open source approach ensures accountability, innovation, and community collaboration."}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"rounded-full bg-secondary/10 p-2 mt-0.5",children:(0,s.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-secondary",children:[(0,s.jsx)("path",{d:"M9.75 12.75L11.25 14.25L14.25 9.75",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,s.jsx)("path",{d:"M4.75 12C4.75 7.99594 7.99594 4.75 12 4.75C16.0041 4.75 19.25 7.99594 19.25 12C19.25 16.0041 16.0041 19.25 12 19.25C7.99594 19.25 4.75 16.0041 4.75 12Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium",children:"Transparency"}),(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:"Fully auditable codebase"})]})]}),(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"rounded-full bg-secondary/10 p-2 mt-0.5",children:(0,s.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-secondary",children:[(0,s.jsx)("path",{d:"M9.75 12.75L11.25 14.25L14.25 9.75",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,s.jsx)("path",{d:"M4.75 12C4.75 7.99594 7.99594 4.75 12 4.75C16.0041 4.75 19.25 7.99594 19.25 12C19.25 16.0041 16.0041 19.25 12 19.25C7.99594 19.25 4.75 16.0041 4.75 12Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium",children:"Community"}),(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:"Join our developers"})]})]}),(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"rounded-full bg-secondary/10 p-2 mt-0.5",children:(0,s.jsxs)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-secondary",children:[(0,s.jsx)("path",{d:"M9.75 12.75L11.25 14.25L14.25 9.75",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),(0,s.jsx)("path",{d:"M4.75 12C4.75 7.99594 7.99594 4.75 12 4.75C16.0041 4.75 19.25 7.99594 19.25 12C19.25 16.0041 16.0041 19.25 12 19.25C7.99594 19.25 4.75 16.0041 4.75 12Z",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium",children:"Apache 2.0"}),(0,s.jsx)("p",{className:"text-muted-foreground text-sm",children:"Free to use and modify"})]})]})]})]})})]})]})})}var W=r(77633),R=r(92138);function U(){let e=(n.CQ.useCases||[]).filter(e=>e.featured);return(0,s.jsxs)("section",{id:"use-cases",className:"flex flex-col items-center justify-center gap-10 pb-10 w-full relative",children:[(0,s.jsxs)(T.X,{children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-medium tracking-tighter text-center text-balance",children:"See Suna in action"}),(0,s.jsx)("p",{className:"text-muted-foreground text-center text-balance font-medium",children:"Explore real-world examples of how Suna completes complex tasks autonomously"})]}),(0,s.jsxs)("div",{className:"relative w-full h-full",children:[(0,s.jsx)("div",{className:"grid min-[650px]:grid-cols-2 min-[900px]:grid-cols-3 min-[1200px]:grid-cols-4 gap-4 w-full max-w-6xl mx-auto px-6",children:e.map(e=>(0,s.jsxs)("div",{className:"rounded-xl overflow-hidden relative h-fit min-[650px]:h-full flex flex-col md:shadow-[0px_61px_24px_-10px_rgba(0,0,0,0.01),0px_34px_20px_-8px_rgba(0,0,0,0.05),0px_15px_15px_-6px_rgba(0,0,0,0.09),0px_4px_8px_-2px_rgba(0,0,0,0.10),0px_0px_0px_1px_rgba(0,0,0,0.08)] bg-accent",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-4 p-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"rounded-full bg-secondary/10 p-2",children:(0,s.jsx)("svg",{width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-secondary",children:e.icon})}),(0,s.jsx)("h3",{className:"text-lg font-medium line-clamp-1",children:e.title})]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground leading-relaxed line-clamp-3",children:e.description})]}),(0,s.jsxs)("div",{className:"mt-auto",children:[(0,s.jsx)("hr",{className:"border-border dark:border-white/20 m-0"}),(0,s.jsx)("div",{className:"w-full h-[160px] bg-accent/10",children:(0,s.jsxs)("div",{className:"relative w-full h-full overflow-hidden",children:[(0,s.jsx)("img",{src:e.image||"https://placehold.co/800x400/f5f5f5/666666?text=Suna+".concat(e.title.split(" ").join("+")),alt:"Suna ".concat(e.title),className:"w-full h-full object-cover"}),(0,s.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 hover:opacity-100 transition-opacity flex items-end justify-start p-4 group",children:(0,s.jsxs)("span",{className:"flex items-center gap-2 text-sm text-white font-medium",children:["Watch replay",(0,s.jsx)(R.A,{className:"size-4 transform group-hover:translate-x-1 transition-transform"})]})})]})})]})]},e.id))}),0===e.length&&(0,s.jsx)("div",{className:"flex flex-col items-center justify-center py-16 text-center",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"No use cases available yet."})})]})]})}var q=r(12262),D=r(85690),Q=r(54416),V=r(45084),J=r(95653),X=r(59434);let Y={"from-bottom":{initial:{y:"100%",opacity:0},animate:{y:0,opacity:1},exit:{y:"100%",opacity:0}},"from-center":{initial:{scale:.5,opacity:0},animate:{scale:1,opacity:1},exit:{scale:.5,opacity:0}},"from-top":{initial:{y:"-100%",opacity:0},animate:{y:0,opacity:1},exit:{y:"-100%",opacity:0}},"from-left":{initial:{x:"-100%",opacity:0},animate:{x:0,opacity:1},exit:{x:"-100%",opacity:0}},"from-right":{initial:{x:"100%",opacity:0},animate:{x:0,opacity:1},exit:{x:"100%",opacity:0}},fade:{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0}},"top-in-bottom-out":{initial:{y:"-100%",opacity:0},animate:{y:0,opacity:1},exit:{y:"100%",opacity:0}},"left-in-right-out":{initial:{x:"-100%",opacity:0},animate:{x:0,opacity:1},exit:{x:"100%",opacity:0}}};function $(e){let{animationStyle:t="from-center",videoSrc:r,thumbnailSrc:n,thumbnailAlt:i="Video thumbnail",className:a}=e,[l,o]=(0,x.useState)(!1),c=Y[t];return(0,s.jsxs)("div",{className:(0,X.cn)("relative",a),children:[(0,s.jsxs)("div",{className:"group relative cursor-pointer",onClick:()=>o(!0),children:[n?(0,s.jsx)("img",{src:n,alt:i,width:1920,height:1080,className:"w-full transition-all duration-200 ease-out group-hover:brightness-[0.8] isolate"}):(0,s.jsx)("div",{className:"w-full aspect-video bg-background rounded-2xl"}),(0,s.jsx)("div",{className:"absolute isolate inset-0 flex scale-[0.9] items-center justify-center rounded-2xl transition-all duration-200 ease-out group-hover:scale-100",children:(0,s.jsx)("div",{className:"flex size-28 items-center justify-center rounded-full bg-gradient-to-t from-secondary/20 to-[#ACC3F7/15] backdrop-blur-md",children:(0,s.jsx)("div",{className:"relative flex size-20 scale-100 items-center justify-center rounded-full bg-gradient-to-t from-secondary to-white/10 shadow-md transition-all duration-200 ease-out group-hover:scale-[1.2]",children:(0,s.jsx)(D.A,{className:"size-8 scale-100 fill-white text-white transition-transform duration-200 ease-out group-hover:scale-105",style:{filter:"drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06))"}})})})})]}),(0,s.jsx)(V.N,{children:l&&(0,s.jsx)(J.P.div,{initial:{opacity:0},animate:{opacity:1},onClick:()=>o(!1),exit:{opacity:0},className:"fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-md",children:(0,s.jsxs)(J.P.div,{...c,transition:{type:"spring",damping:30,stiffness:300},className:"relative mx-4 aspect-video w-full max-w-4xl md:mx-0",children:[(0,s.jsx)(J.P.button,{className:"absolute cursor-pointer hover:scale-[98%] transition-all duration-200 ease-out -top-16 right-0 rounded-full bg-neutral-900/50 p-2 text-xl text-white ring-1 backdrop-blur-md dark:bg-neutral-100/50 dark:text-black",onClick:()=>o(!1),children:(0,s.jsx)(Q.A,{className:"size-5"})}),(0,s.jsx)("div",{className:"relative isolate z-[1] size-full overflow-hidden rounded-2xl border-2 border-white",children:(0,s.jsx)("iframe",{src:(()=>{let e=new URL(r);return e.searchParams.set("autoplay","1"),e.toString()})(),className:"size-full",allowFullScreen:!0,allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"})})]})})})]})}function Z(){return(0,s.jsxs)("section",{id:"demo",className:"flex flex-col items-center justify-center gap-10 w-full relative",children:[(0,s.jsxs)(T.X,{children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-medium tracking-tighter text-center text-balance pb-1",children:"Watch Intelligence in Motion"}),(0,s.jsx)("p",{className:"text-muted-foreground text-center text-balance font-medium",children:"Watch how Suna executes complex workflows with precision and autonomy"})]}),(0,s.jsx)("div",{className:"relative px-6",children:(0,s.jsxs)("div",{className:"relative w-full max-w-3xl mx-auto shadow-xl rounded-2xl overflow-hidden",children:[(0,s.jsx)($,{className:"block dark:hidden",animationStyle:"from-center",videoSrc:"https://www.youtube.com/embed/Jnxq0osSg2c?si=k8ddEM8h8lver20s",thumbnailSrc:"/thumbnail-light.png",thumbnailAlt:"Hero Video"}),(0,s.jsx)($,{className:"hidden dark:block",animationStyle:"from-center",videoSrc:"https://www.youtube.com/embed/Jnxq0osSg2c?si=k8ddEM8h8lver20s",thumbnailSrc:"/thumbnail-dark.png",thumbnailAlt:"Hero Video"})]})})]})}function K(){return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(q.g,{}),(0,s.jsx)("main",{className:"flex flex-col items-center justify-center min-h-screen w-full",children:(0,s.jsxs)("div",{className:"w-full divide-y divide-border",children:[(0,s.jsx)(F,{}),(0,s.jsx)(U,{}),(0,s.jsx)(B,{}),(0,s.jsx)("div",{className:"flex flex-col items-center px-4",children:(0,s.jsx)(W.c,{})}),(0,s.jsx)("div",{className:"pb-10 mx-auto",children:(0,s.jsx)(Z,{})}),(0,s.jsx)(l,{}),(0,s.jsx)(f,{})]})})]})}},12262:(e,t,r)=>{"use strict";r.d(t,{g:()=>c});var s=r(95155);r(12115);var n=r(54165),i=r(71539),a=r(40407),l=r(77633);let o=()=>{let{isOpen:e,type:t,onClose:r}=(0,a.h)();return(0,s.jsx)(n.lG,{open:e&&"paymentRequiredDialog"===t,onOpenChange:r,children:(0,s.jsxs)(n.Cf,{className:"w-[95vw] max-w-[750px] max-h-[90vh] overflow-hidden flex flex-col p-0",children:[(0,s.jsxs)(n.c7,{className:"px-4 sm:px-6 pt-4 sm:pt-6 flex-shrink-0",children:[(0,s.jsx)(n.L3,{children:"Upgrade Required"}),(0,s.jsx)(n.rr,{children:"You've reached your plan's usage limit. Upgrade to continue enjoying our premium features."})]}),(0,s.jsx)("div",{className:"flex-1 pb-2 overflow-y-auto scrollbar-thin scrollbar-thumb-zinc-300 dark:scrollbar-thumb-zinc-700 scrollbar-track-transparent px-4 sm:px-6 min-h-0",children:(0,s.jsxs)("div",{className:"space-y-4 sm:space-y-6 pb-4",children:[(0,s.jsx)("div",{className:"flex items-start p-3 sm:p-4 bg-destructive/5 border border-destructive/50 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:(0,s.jsx)(i.A,{className:"w-4 h-4 sm:w-5 sm:h-5 text-destructive"})}),(0,s.jsxs)("div",{className:"text-xs sm:text-sm min-w-0",children:[(0,s.jsx)("p",{className:"font-medium text-destructive",children:"Usage Limit Reached"}),(0,s.jsx)("p",{className:"text-destructive break-words",children:"Your current plan has been exhausted for this billing period."})]})]})}),(0,s.jsx)("div",{className:"w-full",children:(0,s.jsx)(l.c,{insideDialog:!0,hideFree:!0,returnUrl:"".concat("http://localhost:3000","/dashboard"),showTitleAndTabs:!1})})]})})]})})},c=()=>(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(o,{})})},38095:(e,t,r)=>{"use strict";r.d(t,{U:()=>n});var s=r(12115);function n(e){let[t,r]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{let s=window.matchMedia(e);s.matches!==t&&r(s.matches);let n=()=>r(s.matches);return s.addEventListener("change",n),()=>s.removeEventListener("change",n)},[t,e]),t}},42686:(e,t,r)=>{"use strict";r.d(t,{o:()=>i});var s=r(26072),n=r(52643);let i=e=>{let t=(0,n.U)();return(0,s.Ay)(!!t&&["accounts"],async()=>{let{data:e,error:r}=await t.rpc("get_accounts");if(r)throw Error(r.message);return e},e)}},42714:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return i}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},s=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function n(e){return["async","defer","noModule"].includes(e)}function i(e,t){for(let[i,a]of Object.entries(t)){if(!t.hasOwnProperty(i)||s.includes(i)||void 0===a)continue;let l=r[i]||i.toLowerCase();"SCRIPT"===e.tagName&&n(l)?e[l]=!!a:e.setAttribute(l,String(a)),(!1===a||"SCRIPT"===e.tagName&&n(l)&&(!a||"false"===a))&&(e.setAttribute(l,""),e.removeAttribute(l))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59099:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(19946).A)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},60643:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var s=r(95155),n=r(12115),i=r(51362),a=r(56671),l=r(87962);function o(e){let{returnUrl:t}=e,[r,o]=(0,n.useState)(!1),{resolvedTheme:c}=(0,i.D)(),d=(0,n.useCallback)(()=>{sessionStorage.removeItem("isGitHubAuthInProgress"),o(!1)},[]),u=(0,n.useCallback)(e=>{d(),setTimeout(()=>{window.location.href=e.returnUrl||t||"/dashboard"},100)},[d,t]),m=(0,n.useCallback)(e=>{d(),a.oR.error(e.message||"GitHub sign-in failed. Please try again.")},[d]);(0,n.useEffect)(()=>{let e=e=>{var t;if(e.origin!==window.location.origin)return void console.warn("Rejected message from unauthorized origin:",e.origin);if((null==(t=e.data)?void 0:t.type)&&"string"==typeof e.data.type)switch(e.data.type){case"github-auth-success":u(e.data);break;case"github-auth-error":m(e.data)}};return window.addEventListener("message",e),()=>{window.removeEventListener("message",e)}},[u,m]),(0,n.useEffect)(()=>()=>{d()},[d]);let x=async()=>{if(r)return;let e=null;try{o(!0),t&&sessionStorage.setItem("github-returnUrl",t||"/dashboard");let r=window.open("".concat(window.location.origin,"/auth/github-popup"),"GitHubOAuth","width=500,height=600,scrollbars=yes,resizable=yes,status=yes,location=yes");if(!r)throw Error("Popup was blocked. Please enable popups and try again.");sessionStorage.setItem("isGitHubAuthInProgress","1"),e=setInterval(()=>{r.closed&&(e&&(clearInterval(e),e=null),setTimeout(()=>{sessionStorage.getItem("isGitHubAuthInProgress")&&(d(),a.oR.error("GitHub sign-in was cancelled or not completed."))},500))},1e3)}catch(t){console.error("GitHub sign-in error:",t),e&&clearInterval(e),d(),a.oR.error(t instanceof Error?t.message:"Failed to start GitHub sign-in")}};return(0,s.jsxs)("button",{onClick:x,disabled:r,className:"relative w-full h-12 flex items-center justify-center text-sm font-normal tracking-wide rounded-full bg-background text-foreground border border-border hover:bg-accent/30 transition-all duration-200 disabled:opacity-60 disabled:cursor-not-allowed font-sans","aria-label":r?"Signing in with GitHub...":"Sign in with GitHub",type:"button",children:[(0,s.jsx)("div",{className:"absolute left-0 inset-y-0 flex items-center pl-1 w-10",children:(0,s.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-foreground dark:bg-foreground dark:text-background",children:r?(0,s.jsx)("div",{className:"w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin"}):(0,s.jsx)(l.F.github,{className:"w-5 h-5"})})}),(0,s.jsx)("span",{className:"ml-9 font-light",children:r?"Signing in...":"Continue with GitHub"})]})}},61446:(e,t,r)=>{"use strict";r.d(t,{i:()=>o});var s=r(95155),n=r(1243),i=r(54416),a=r(30285),l=r(35695);function o(e){let{message:t,currentUsage:r,limit:o,accountId:c,onDismiss:d,isOpen:u}=e,m=(0,l.useRouter)();return u?(0,s.jsx)("div",{className:"fixed bottom-4 right-4 z-[9999]",children:(0,s.jsx)("div",{className:"bg-destructive/15 backdrop-blur-sm border border-destructive/30 rounded-lg p-5 shadow-lg max-w-md",children:(0,s.jsxs)("div",{className:"flex items-start gap-4",children:[(0,s.jsx)("div",{className:"flex-shrink-0 bg-destructive/20 p-2 rounded-full",children:(0,s.jsx)(n.A,{className:"h-5 w-5 text-destructive"})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,s.jsx)("h3",{className:"text-sm font-semibold text-destructive",children:"Usage Limit Reached"}),(0,s.jsx)(a.$,{variant:"ghost",size:"icon",onClick:d,className:"h-6 w-6 p-0 text-muted-foreground hover:text-foreground",children:(0,s.jsx)(i.A,{className:"h-4 w-4"})})]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:t}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(a.$,{variant:"outline",size:"sm",onClick:d,className:"text-xs",children:"Dismiss"}),(0,s.jsx)(a.$,{size:"sm",onClick:()=>m.push("/settings/billing?accountId=".concat(c)),className:"text-xs bg-destructive hover:bg-destructive/90",children:"Upgrade Plan"})]})]})]})})}):null}},63554:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n.a});var s=r(69243),n=r.n(s),i={};for(let e in s)"default"!==e&&(i[e]=()=>s[e]);r.d(t,i)},69243:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return b},handleClientScriptLoad:function(){return h},initScriptLoader:function(){return g}});let s=r(38466),n=r(93011),i=r(95155),a=s._(r(47650)),l=n._(r(12115)),o=r(82830),c=r(42714),d=r(92374),u=new Map,m=new Set,x=e=>{if(a.default.preinit)return void e.forEach(e=>{a.default.preinit(e,{as:"style"})});{let t=document.head;e.forEach(e=>{let r=document.createElement("link");r.type="text/css",r.rel="stylesheet",r.href=e,t.appendChild(r)})}},f=e=>{let{src:t,id:r,onLoad:s=()=>{},onReady:n=null,dangerouslySetInnerHTML:i,children:a="",strategy:l="afterInteractive",onError:o,stylesheets:d}=e,f=r||t;if(f&&m.has(f))return;if(u.has(t)){m.add(f),u.get(t).then(s,o);return}let h=()=>{n&&n(),m.add(f)},g=document.createElement("script"),p=new Promise((e,t)=>{g.addEventListener("load",function(t){e(),s&&s.call(this,t),h()}),g.addEventListener("error",function(e){t(e)})}).catch(function(e){o&&o(e)});i?(g.innerHTML=i.__html||"",h()):a?(g.textContent="string"==typeof a?a:Array.isArray(a)?a.join(""):"",h()):t&&(g.src=t,u.set(t,p)),(0,c.setAttributesFromProps)(g,e),"worker"===l&&g.setAttribute("type","text/partytown"),g.setAttribute("data-nscript",l),d&&x(d),document.body.appendChild(g)};function h(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,d.requestIdleCallback)(()=>f(e))}):f(e)}function g(e){e.forEach(h),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");m.add(t)})}function p(e){let{id:t,src:r="",onLoad:s=()=>{},onReady:n=null,strategy:c="afterInteractive",onError:u,stylesheets:x,...h}=e,{updateScripts:g,scripts:p,getIsSsr:b,appDir:v,nonce:j}=(0,l.useContext)(o.HeadManagerContext),w=(0,l.useRef)(!1);(0,l.useEffect)(()=>{let e=t||r;w.current||(n&&e&&m.has(e)&&n(),w.current=!0)},[n,t,r]);let y=(0,l.useRef)(!1);if((0,l.useEffect)(()=>{if(!y.current){if("afterInteractive"===c)f(e);else"lazyOnload"===c&&("complete"===document.readyState?(0,d.requestIdleCallback)(()=>f(e)):window.addEventListener("load",()=>{(0,d.requestIdleCallback)(()=>f(e))}));y.current=!0}},[e,c]),("beforeInteractive"===c||"worker"===c)&&(g?(p[c]=(p[c]||[]).concat([{id:t,src:r,onLoad:s,onReady:n,onError:u,...h}]),g(p)):b&&b()?m.add(t||r):b&&!b()&&f(e)),v){if(x&&x.forEach(e=>{a.default.preinit(e,{as:"style"})}),"beforeInteractive"===c)if(!r)return h.dangerouslySetInnerHTML&&(h.children=h.dangerouslySetInnerHTML.__html,delete h.dangerouslySetInnerHTML),(0,i.jsx)("script",{nonce:j,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...h,id:t}])+")"}});else return a.default.preload(r,h.integrity?{as:"script",integrity:h.integrity,nonce:j,crossOrigin:h.crossOrigin}:{as:"script",nonce:j,crossOrigin:h.crossOrigin}),(0,i.jsx)("script",{nonce:j,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([r,{...h,id:t}])+")"}});"afterInteractive"===c&&r&&a.default.preload(r,h.integrity?{as:"script",integrity:h.integrity,nonce:j,crossOrigin:h.crossOrigin}:{as:"script",nonce:j,crossOrigin:h.crossOrigin})}return null}Object.defineProperty(p,"__nextScript",{value:!0});let b=p;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},76128:(e,t,r)=>{Promise.resolve().then(r.bind(r,8518))},82728:(e,t,r)=>{"use strict";r.d(t,{L:()=>i});var s=r(12115),n=r(23843);function i(){let[e,t]=(0,s.useState)(null);return{billingError:e,handleBillingError:(0,s.useCallback)(e=>{var r,s,i;if((0,n.Jn)())return console.log("Running in local development mode - billing checks are disabled"),!1;if(e&&(e.message||e.subscription))return t({message:e.message||"You've reached your monthly usage limit.",currentUsage:e.currentUsage||(null==(r=e.subscription)?void 0:r.current_usage),limit:e.limit||(null==(s=e.subscription)?void 0:s.limit),subscription:e.subscription||{}}),!0;if(402===e.status||e.message&&e.message.includes("Payment Required")){let r=(null==(i=e.data)?void 0:i.detail)||{},s=r.subscription||{};return t({message:r.message||"You've reached your monthly usage limit.",currentUsage:s.current_usage,limit:s.limit,subscription:s}),!0}return!1},[]),clearBillingError:(0,s.useCallback)(()=>{t(null)},[])}}},92374:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return s},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},s="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95360:(e,t,r)=>{"use strict";let s,n;r.d(t,{L:()=>q});var i=r(12115),a=r(80793),l=r(83385);let o=new WeakMap;function c({target:e,contentRect:t,borderBoxSize:r}){o.get(e)?.forEach(s=>{s({target:e,contentSize:t,get size(){if(r){let{inlineSize:e,blockSize:t}=r[0];return{width:e,height:t}}if((0,l.x)(e)&&"getBBox"in e)return e.getBBox();return{width:e.offsetWidth,height:e.offsetHeight}}})})}function d(e){e.forEach(c)}let u=new Set;var m=r(38865),x=r(64530);let f=()=>({current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}),h=()=>({time:0,x:f(),y:f()}),g={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function p(e,t,r,s){let n=r[t],{length:i,position:a}=g[t],l=n.current,o=r.time;n.current=e[`scroll${a}`],n.scrollLength=e[`scroll${i}`]-e[`client${i}`],n.offset.length=0,n.offset[0]=0,n.offset[1]=n.scrollLength,n.progress=(0,m.q)(0,n.scrollLength,n.current);let c=s-o;n.velocity=c>50?0:(0,x.f)(n.current-l,c)}var b=r(22258);let v={start:0,center:.5,end:1};function j(e,t,r=0){let s=0;if(e in v&&(e=v[e]),"string"==typeof e){let t=parseFloat(e);e.endsWith("px")?s=t:e.endsWith("%")?e=t/100:e.endsWith("vw")?s=t/100*document.documentElement.clientWidth:e.endsWith("vh")?s=t/100*document.documentElement.clientHeight:e=t}return"number"==typeof e&&(s=t*e),r+s}let w=[0,0],y={All:[[0,0],[1,1]]};var N=r(38154),k=r(91762),_=r(98167);let C={x:0,y:0};var S=r(21116),L=r(46182);let E=new WeakMap,z=new WeakMap,A=new WeakMap,I=e=>e===document.scrollingElement?window:e;function M(e,{container:t=document.scrollingElement,...r}={}){if(!t)return S.l;let i=A.get(t);i||(i=new Set,A.set(t,i));let l=function(e,t,r,s={}){return{measure:t=>{!function(e,t=e,r){if(r.x.targetOffset=0,r.y.targetOffset=0,t!==e){let s=t;for(;s&&s!==e;)r.x.targetOffset+=s.offsetLeft,r.y.targetOffset+=s.offsetTop,s=s.offsetParent}r.x.targetLength=t===e?t.scrollWidth:t.clientWidth,r.y.targetLength=t===e?t.scrollHeight:t.clientHeight,r.x.containerLength=e.clientWidth,r.y.containerLength=e.clientHeight}(e,s.target,r),p(e,"x",r,t),p(e,"y",r,t),r.time=t,(s.offset||s.target)&&function(e,t,r){let{offset:s=y.All}=r,{target:n=e,axis:i="y"}=r,a="y"===i?"height":"width",l=n!==e?function(e,t){let r={x:0,y:0},s=e;for(;s&&s!==t;)if((0,b.s)(s))r.x+=s.offsetLeft,r.y+=s.offsetTop,s=s.offsetParent;else if("svg"===s.tagName){let e=s.getBoundingClientRect(),t=(s=s.parentElement).getBoundingClientRect();r.x+=e.left-t.left,r.y+=e.top-t.top}else if(s instanceof SVGGraphicsElement){let{x:e,y:t}=s.getBBox();r.x+=e,r.y+=t;let n=null,i=s.parentNode;for(;!n;)"svg"===i.tagName&&(n=i),i=s.parentNode;s=n}else break;return r}(n,e):C,o=n===e?{width:e.scrollWidth,height:e.scrollHeight}:"getBBox"in n&&"svg"!==n.tagName?n.getBBox():{width:n.clientWidth,height:n.clientHeight},c={width:e.clientWidth,height:e.clientHeight};t[i].offset.length=0;let d=!t[i].interpolate,u=s.length;for(let e=0;e<u;e++){let r=function(e,t,r,s){let n=Array.isArray(e)?e:w,i=0,a=0;return"number"==typeof e?n=[e,e]:"string"==typeof e&&(n=(e=e.trim()).includes(" ")?e.split(" "):[e,v[e]?e:"0"]),(i=j(n[0],r,s))-j(n[1],t)}(s[e],c[a],o[a],l[i]);d||r===t[i].interpolatorOffsets[e]||(d=!0),t[i].offset[e]=r}d&&(t[i].interpolate=(0,N.G)(t[i].offset,(0,k.Z)(s),{clamp:!1}),t[i].interpolatorOffsets=[...t[i].offset]),t[i].progress=(0,_.q)(0,1,t[i].interpolate(t[i].current))}(e,r,s)},notify:()=>t(r)}}(t,e,h(),r);if(i.add(l),!E.has(t)){let e=()=>{for(let e of i)e.measure(L.uv.timestamp);L.Gt.preUpdate(r)},r=()=>{for(let e of i)e.notify()},l=()=>L.Gt.read(e);E.set(t,l);let c=I(t);window.addEventListener("resize",l,{passive:!0}),t!==document.documentElement&&z.set(t,"function"==typeof t?(u.add(t),n||(n=()=>{let e={width:window.innerWidth,height:window.innerHeight},t={target:window,size:e,contentSize:e};u.forEach(e=>e(t))},window.addEventListener("resize",n)),()=>{u.delete(t),!u.size&&n&&(n=void 0)}):function(e,t){s||"undefined"!=typeof ResizeObserver&&(s=new ResizeObserver(d));let r=(0,a.K)(e);return r.forEach(e=>{let r=o.get(e);r||(r=new Set,o.set(e,r)),r.add(t),s?.observe(e)}),()=>{r.forEach(e=>{let r=o.get(e);r?.delete(t),r?.size||s?.unobserve(e)})}}(t,l)),c.addEventListener("scroll",l,{passive:!0}),l()}let c=E.get(t);return L.Gt.read(c,!1,!0),()=>{(0,L.WG)(c);let e=A.get(t);if(!e||(e.delete(l),e.size))return;let r=E.get(t);E.delete(t),r&&(I(t).removeEventListener("scroll",r),z.get(t)?.(),window.removeEventListener("resize",r))}}var O=r(96299);let P=new Map;function H({source:e,container:t,...r}){let{axis:s}=r;e&&(t=e);let n=P.get(t)??new Map;P.set(t,n);let i=r.target??"self",a=n.get(i)??{},l=s+(r.offset??[]).join(",");return a[l]||(a[l]=!r.target&&(0,O.J)()?new ScrollTimeline({source:t,axis:s}):function(e){let t={value:0},r=M(r=>{t.value=100*r[e.axis].progress},e);return{currentTime:t,cancel:r}}({container:t,...r})),a[l]}function F(e,t){let r,s=()=>{let{currentTime:s}=t,n=(null===s?0:s.value)/100;r!==n&&e(n),r=n};return L.Gt.preUpdate(s,!0),()=>(0,L.WG)(s)}var T=r(76168),G=r(69025),B=r(94449),W=r(99967);function R(e,t){(0,B.$)(!!(!t||t.current),`You have defined a ${e} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \`layoutEffect: false\` option.`)}let U=()=>({scrollX:(0,W.OQ)(0),scrollY:(0,W.OQ)(0),scrollXProgress:(0,W.OQ)(0),scrollYProgress:(0,W.OQ)(0)});function q({container:e,target:t,layoutEffect:r=!0,...s}={}){let n=(0,T.M)(U);return(r?G.E:i.useEffect)(()=>(R("target",t),R("container",e),function(e,{axis:t="y",container:r=document.scrollingElement,...s}={}){var n,i;if(!r)return S.l;let a={axis:t,container:r,...s};return"function"==typeof e?(n=e,i=a,2===n.length?M(e=>{n(e[i.axis].progress,e)},i):F(n,H(i))):function(e,t){let r=H(t);return e.attachTimeline({timeline:t.target?void 0:r,observe:e=>(e.pause(),F(t=>{e.time=e.duration*t},r))})}(e,a)}((e,{x:t,y:r})=>{n.scrollX.set(t.current),n.scrollXProgress.set(t.progress),n.scrollY.set(r.current),n.scrollYProgress.set(r.progress)},{...s,container:e?.current||void 0,target:t?.current||void 0})),[e,t,JSON.stringify(s.offset)]),n}}},e=>{var t=t=>e(e.s=t);e.O(0,[2362,6711,5105,2969,1935,6671,3860,6874,1171,8341,7201,5061,6165,9001,7453,2103,9879,5653,6766,9855,2473,6915,2566,9613,539,4612,6072,6686,937,3685,8222,7379,4962,6528,9713,2814,8441,1684,7358],()=>t(76128)),_N_E=e.O()}]);