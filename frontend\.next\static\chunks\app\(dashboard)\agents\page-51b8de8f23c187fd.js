(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[797],{17907:(e,s,a)=>{Promise.resolve().then(a.bind(a,31636))},31636:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>eJ});var t=a(95155),l=a(12115),r=a(56671),n=a(35695),i=a(38198),c=a(28755),o=a(26715),d=a(5041),m=a(52643);let u="http://localhost:8000/api";function x(){let e=(0,o.jE)();return(0,d.n)({mutationFn:async e=>{let s=(0,m.U)(),{data:{session:a}}=await s.auth.getSession();if(!a)throw Error("You must be logged in to unpublish templates");let t=await fetch("".concat(u,"/templates/").concat(e,"/unpublish"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a.access_token)}});if(!t.ok)throw Error((await t.json().catch(()=>({message:"Unknown error"}))).message||"HTTP ".concat(t.status,": ").concat(t.statusText));return t.json()},onSuccess:()=>{e.invalidateQueries({queryKey:["secure-mcp","marketplace-templates"]}),e.invalidateQueries({queryKey:["secure-mcp","my-templates"]})}})}var h=a(17711),p=a(30285),g=a(62523),f=a(85057),j=a(54165),v=a(51154),N=a(40646),y=a(91788),b=a(92138),_=a(59434),w=a(88271),k=a(22346),C=a(55365),A=a(75525),S=a(84616),P=a(381),T=a(59409),q=a(26126),F=a(66695),E=a(69803),R=a(85339),M=a(38564),L=a(95030);let $=function(e){let s=!(arguments.length>1)||void 0===arguments[1]||arguments[1],a=(0,m.U)();return(0,c.I)({queryKey:["mcp-server-details",e],queryFn:async()=>{let{data:{session:s}}=await a.auth.getSession();if(!s)throw Error("No session");let t=await fetch("".concat("http://localhost:8000/api","/mcp/servers/").concat(e),{headers:{Authorization:"Bearer ".concat(s.access_token)}});if(!t.ok)throw Error("Failed to fetch MCP server details");return t.json()},enabled:s&&!!e,staleTime:6e5})},I=e=>{let{open:s,onOpenChange:a,mcpQualifiedName:n,mcpDisplayName:i,onSuccess:c}=e,[o,d]=(0,l.useState)({profile_name:"".concat(i," Profile"),display_name:i,config:{},is_default:!1}),{data:m,isLoading:u}=$(n),x=(0,L.Ak)(),h=()=>{var e,s;let a=null==m||null==(s=m.connections)||null==(e=s[0])?void 0:e.configSchema;return(null==a?void 0:a.required)||[]},N=e=>h().includes(e),y=(e,s)=>{d(a=>({...a,config:{...a.config,[e]:s}}))},b=async()=>{try{let e={mcp_qualified_name:n,profile_name:o.profile_name,display_name:o.display_name,config:o.config,is_default:o.is_default},s=await x.mutateAsync(e);r.oR.success("Credential profile created successfully!");let t={profile_id:s.profile_id||"new-profile",mcp_qualified_name:n,profile_name:o.profile_name,display_name:o.display_name,config_keys:Object.keys(o.config),is_active:!0,is_default:o.is_default,last_used_at:null,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};c(t),a(!1),d({profile_name:"".concat(i," Profile"),display_name:i,config:{},is_default:!1})}catch(e){r.oR.error(e.message||"Failed to create credential profile")}},_=(()=>{var e,s;let a=null==m||null==(s=m.connections)||null==(e=s[0])?void 0:e.configSchema;return(null==a?void 0:a.properties)||{}})(),w=Object.keys(_).length>0;return(0,t.jsx)(j.lG,{open:s,onOpenChange:a,children:(0,t.jsxs)(j.Cf,{className:"max-w-2xl max-h-[85vh] overflow-hidden flex flex-col",children:[(0,t.jsxs)(j.c7,{children:[(0,t.jsxs)(j.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(E.A,{className:"h-5 w-5 text-primary"}),"Create Credential Profile"]}),(0,t.jsxs)(j.rr,{children:["Create a new credential profile for ",(0,t.jsx)("strong",{children:i})]})]}),u?(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(v.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,t.jsx)("span",{children:"Loading server configuration..."})]}):(0,t.jsx)("div",{className:"space-y-6 flex-1 overflow-y-auto",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"grid grid-cols-1 gap-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.Label,{htmlFor:"profile_name",children:"Profile Name *"}),(0,t.jsx)(g.p,{id:"profile_name",value:o.profile_name,onChange:e=>d(s=>({...s,profile_name:e.target.value})),placeholder:"Enter a name for this profile"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"This helps you identify different configurations for the same MCP server"})]})}),w?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-sm font-semibold flex items-center gap-2",children:[(0,t.jsx)(P.A,{className:"h-4 w-4"}),"Connection Settings"]}),Object.entries(_).map(e=>{let[s,a]=e;return(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(f.Label,{htmlFor:s,children:[a.title||s,N(s)&&(0,t.jsx)("span",{className:"text-destructive ml-1",children:"*"})]}),(0,t.jsx)(g.p,{id:s,type:"password"===a.format?"password":"text",placeholder:a.description||"Enter ".concat(s),value:o.config[s]||"",onChange:e=>y(s,e.target.value)}),a.description&&(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:a.description})]},s)})]}):(0,t.jsxs)(C.Fc,{children:[(0,t.jsx)(E.A,{className:"h-4 w-4"}),(0,t.jsx)(C.TN,{children:"This MCP server doesn't require any API credentials to use."})]}),(0,t.jsxs)(C.Fc,{children:[(0,t.jsx)(A.A,{className:"h-4 w-4"}),(0,t.jsx)(C.TN,{children:"Your credentials will be encrypted and stored securely. You can create multiple profiles for the same MCP server to handle different use cases."})]})]})}),(0,t.jsxs)(j.Es,{children:[(0,t.jsx)(p.$,{variant:"outline",onClick:()=>a(!1),children:"Cancel"}),(0,t.jsx)(p.$,{onClick:b,disabled:!o.profile_name.trim()||x.isPending,children:x.isPending?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v.A,{className:"h-4 w-4 animate-spin"}),"Creating..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),"Create Profile"]})})]})]})})};function O(e){let{mcpQualifiedName:s,mcpDisplayName:a,selectedProfileId:n,onProfileSelect:i,disabled:c=!1}=e,[o,d]=(0,l.useState)(!1),{data:m=[],isLoading:u,error:x,refetch:h}=(0,L.Gx)(s),g=(0,L.M4)(),j=m.find(e=>e.profile_id===n),v=async e=>{try{await g.mutateAsync(e)}catch(e){console.error("Failed to set default profile:",e)}};return u?(0,t.jsx)("div",{className:"flex items-center justify-center p-4",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary"})}):x?(0,t.jsx)(F.Zp,{className:"border-destructive/50",children:(0,t.jsx)(F.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 text-destructive",children:[(0,t.jsx)(R.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm",children:"Failed to load credential profiles"})]})})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsx)(f.Label,{className:"text-sm font-medium",children:"Credential Profile"})}),0===m.length?(0,t.jsx)(F.Zp,{className:"border-dashed",children:(0,t.jsxs)(F.Wu,{className:"p-6 text-center",children:[(0,t.jsx)(P.A,{className:"h-8 w-8 mx-auto mb-2 text-muted-foreground"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground mb-3",children:["No credential profiles found for ",a]}),(0,t.jsxs)(p.$,{variant:"outline",size:"sm",onClick:()=>{d(!0)},disabled:c,children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),"Create Profile"]})]})}):(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)(T.l6,{value:n||"",onValueChange:e=>{if(e&&""!==e.trim()){let s=m.find(s=>s.profile_id===e);s?i(e,s):(console.error("Selected profile not found:",e),r.oR.error("Selected profile not found. Please refresh and try again."))}else i(null,null)},disabled:c,children:[(0,t.jsx)(T.bq,{children:(0,t.jsx)(T.yv,{placeholder:"Select a credential profile..."})}),(0,t.jsx)(T.gC,{children:m.map(e=>(0,t.jsx)(T.eb,{value:e.profile_id,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{children:e.profile_name}),e.is_default&&(0,t.jsx)(q.E,{variant:"outline",className:"text-xs",children:"Default"})]})},e.profile_id))})]}),j&&(0,t.jsx)(F.Zp,{className:"bg-muted/30 py-0",children:(0,t.jsx)(F.Wu,{className:"p-3",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("h4",{className:"text-sm font-medium",children:j.profile_name}),j.is_default&&(0,t.jsx)(q.E,{variant:"outline",className:"text-xs",children:"Default"})]}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:j.display_name})]}),!j.is_default&&(0,t.jsx)(p.$,{variant:"ghost",size:"sm",onClick:()=>v(j.profile_id),disabled:g.isPending,children:(0,t.jsx)(M.A,{className:"h-3 w-3"})})]})})})]})]}),(0,t.jsx)(I,{open:o,onOpenChange:d,mcpQualifiedName:s,mcpDisplayName:a,onSuccess:e=>{h(),i(e.profile_id,e),r.oR.success('Profile "'.concat(e.profile_name,'" created and selected!'))}})]})}var D=a(71007),z=a(43453),U=a(54861),K=a(2417),Q=a(80333),Y=a(44838),Z=a(90010),B=a(74575),G=a(44020),W=a(53904),H=a(8804),V=a(62525);let J=e=>{let{appSlug:s,appName:a,onProfileSelect:r,className:n}=e,[i,c]=(0,l.useState)(!1),[o,d]=(0,l.useState)(null),[m,u]=(0,l.useState)(null),[x,h]=(0,l.useState)(""),[N,y]=(0,l.useState)(!1),{data:b,isLoading:_,refetch:w}=(0,K.h4)({app_slug:s}),k=(0,K.O4)(),C=(0,K.Lh)(),A=(0,K.PA)(),T=(0,K.wG)(),E=async()=>{if(!x.trim())return;let e={profile_name:x.trim(),app_slug:s||"",app_name:a||s||"",is_default:N};try{await k.mutateAsync(e),c(!1),h(""),y(!1)}catch(e){}},R=async(e,s)=>{try{await C.mutateAsync({profileId:e.profile_id,request:s}),d(null)}catch(e){}},L=async e=>{try{await A.mutateAsync(e.profile_id),u(null)}catch(e){}},$=async e=>{try{await T.mutateAsync({profileId:e.profile_id,app:s})}catch(e){}};if(_)return(0,t.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,t.jsx)(v.A,{className:"h-6 w-6 animate-spin"})});let I=(null==b?void 0:b.filter(e=>!s||e.app_slug===s))||[];return(0,t.jsxs)("div",{className:n,children:[0===I.length?(0,t.jsx)(F.Zp,{children:(0,t.jsxs)(F.Wu,{className:"text-center py-12",children:[(0,t.jsx)(D.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,t.jsx)("h4",{className:"font-medium mb-2",children:"No credential profiles yet"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground mb-6 max-w-sm mx-auto",children:["Create credential profiles to manage multiple accounts or configurations for ",a||"your apps","."]}),(0,t.jsxs)(p.$,{onClick:()=>c(!0),variant:"outline",children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),"Create First Profile"]})]})}):(0,t.jsxs)("div",{className:"space-y-4",children:[I.map(e=>(0,t.jsx)(F.Zp,{children:(0,t.jsx)(F.Wu,{children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)("h4",{className:"font-medium",children:e.profile_name}),e.is_default&&(0,t.jsxs)(q.E,{variant:"secondary",className:"text-xs",children:[(0,t.jsx)(M.A,{className:"h-3 w-3"}),"Default"]}),e.is_connected?(0,t.jsxs)(q.E,{variant:"default",className:"text-xs",children:[(0,t.jsx)(z.A,{className:"h-3 w-3"}),"Connected"]}):(0,t.jsxs)(q.E,{variant:"outline",className:"text-xs",children:[(0,t.jsx)(U.A,{className:"h-3 w-3"}),"Not Connected"]}),!e.is_active&&(0,t.jsx)(q.E,{variant:"destructive",className:"text-xs",children:"Inactive"})]})}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[!e.is_connected&&(0,t.jsxs)(p.$,{size:"sm",variant:"outline",onClick:()=>$(e),disabled:T.isPending,children:[(0,t.jsx)(B.A,{className:"h-4 w-4"}),"Connect"]}),(0,t.jsxs)(Y.rI,{children:[(0,t.jsx)(Y.ty,{asChild:!0,children:(0,t.jsx)(p.$,{size:"sm",variant:"ghost",children:(0,t.jsx)(G.A,{className:"h-4 w-4"})})}),(0,t.jsxs)(Y.SQ,{align:"end",children:[(0,t.jsxs)(Y._2,{onClick:()=>d(e),children:[(0,t.jsx)(P.A,{className:"h-4 w-4"}),"Edit Profile"]}),e.is_connected&&(0,t.jsxs)(Y._2,{onClick:()=>$(e),children:[(0,t.jsx)(W.A,{className:"h-4 w-4"}),"Reconnect"]}),!e.is_default&&(0,t.jsxs)(Y._2,{onClick:()=>R(e,{is_default:!0}),children:[(0,t.jsx)(M.A,{className:"h-4 w-4"}),"Set as Default"]}),e.is_default&&(0,t.jsxs)(Y._2,{onClick:()=>R(e,{is_default:!1}),children:[(0,t.jsx)(H.A,{className:"h-4 w-4"}),"Remove Default"]}),(0,t.jsxs)(Y._2,{className:"text-destructive",onClick:()=>u(e),children:[(0,t.jsx)(V.A,{className:"h-4 w-4"}),"Delete Profile"]})]})]})]})]})})},e.profile_id)),(0,t.jsx)("div",{className:"w-full rounded-lg h-24 bg-muted border-dashed border-muted flex items-center justify-center",children:(0,t.jsx)(p.$,{onClick:()=>c(!0),size:"sm",variant:"outline",className:"w-full h-full",children:(0,t.jsx)("div",{className:"flex bg-primary/10 items-center justify-center h-10 w-10 rounded-full",children:(0,t.jsx)(S.A,{className:"h-4 w-4"})})})})]}),(0,t.jsx)(j.lG,{open:i,onOpenChange:c,children:(0,t.jsxs)(j.Cf,{className:"max-w-md",children:[(0,t.jsxs)(j.c7,{children:[(0,t.jsx)(j.L3,{children:"Create Credential Profile"}),(0,t.jsxs)(j.rr,{children:["Create a new credential profile for ",a||"your app","."]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.Label,{htmlFor:"profile-name",children:"Profile Name"}),(0,t.jsx)(g.p,{id:"profile-name",placeholder:"e.g., Personal Account, Work Account",value:x,onChange:e=>h(e.target.value)})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(Q.d,{id:"is-default",checked:N,onCheckedChange:y}),(0,t.jsx)(f.Label,{htmlFor:"is-default",children:"Set as default profile"})]})]}),(0,t.jsxs)(j.Es,{children:[(0,t.jsx)(p.$,{variant:"outline",onClick:()=>c(!1),children:"Cancel"}),(0,t.jsx)(p.$,{onClick:E,disabled:!x.trim()||k.isPending,children:k.isPending?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Creating..."]}):"Create Profile"})]})]})}),o&&(0,t.jsx)(j.lG,{open:!!o,onOpenChange:()=>d(null),children:(0,t.jsxs)(j.Cf,{className:"max-w-md",children:[(0,t.jsxs)(j.c7,{children:[(0,t.jsx)(j.L3,{children:"Edit Profile"}),(0,t.jsxs)(j.rr,{children:["Update the settings for ",o.profile_name,"."]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.Label,{htmlFor:"edit-profile-name",children:"Profile Name"}),(0,t.jsx)(g.p,{id:"edit-profile-name",defaultValue:o.profile_name,onChange:e=>{let s=e.target.value;d({...o,profile_name:s})}})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(Q.d,{id:"edit-is-active",checked:o.is_active,onCheckedChange:e=>{d({...o,is_active:e})}}),(0,t.jsx)(f.Label,{htmlFor:"edit-is-active",children:"Active"})]})]}),(0,t.jsxs)(j.Es,{children:[(0,t.jsx)(p.$,{variant:"outline",onClick:()=>d(null),children:"Cancel"}),(0,t.jsx)(p.$,{onClick:()=>R(o,{profile_name:o.profile_name,is_active:o.is_active}),disabled:C.isPending,children:C.isPending?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Saving..."]}):"Save Changes"})]})]})}),(0,t.jsx)(Z.Lt,{open:!!m,onOpenChange:()=>u(null),children:(0,t.jsxs)(Z.EO,{children:[(0,t.jsxs)(Z.wd,{children:[(0,t.jsx)(Z.r7,{children:"Delete Profile"}),(0,t.jsxs)(Z.$v,{children:['Are you sure you want to delete the profile "',null==m?void 0:m.profile_name,'"? This action cannot be undone.']})]}),(0,t.jsxs)(Z.ck,{children:[(0,t.jsx)(Z.Zr,{children:"Cancel"}),(0,t.jsx)(Z.Rx,{onClick:()=>m&&L(m),className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",children:"Delete Profile"})]})]})})]})},X=e=>{let{appSlug:s,appName:a,selectedProfileId:r,onProfileSelect:n,className:i,showCreateOption:c=!0}=e,[o,d]=(0,l.useState)(!1),{data:m,isLoading:u}=(0,K.h4)({app_slug:s,is_active:!0}),x=null==m?void 0:m.find(e=>e.profile_id===r),h=(null==m?void 0:m.filter(e=>e.is_connected))||[];return u?(0,t.jsx)("div",{className:i,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 animate-spin"}),(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Loading profiles..."})]})}):m&&0!==m.length?0===h.length?(0,t.jsxs)("div",{className:i,children:[(0,t.jsx)(F.Zp,{children:(0,t.jsx)(F.Wu,{className:"py-6",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(R.A,{className:"h-8 w-8 text-warning mx-auto mb-3"}),(0,t.jsx)("p",{className:"text-sm font-medium mb-1",children:"No Connected Profiles"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground mb-3",children:["You have profiles but none are connected to ",a]}),(0,t.jsxs)(p.$,{size:"sm",variant:"outline",onClick:()=>d(!0),children:[(0,t.jsx)(P.A,{className:"h-4 w-4 mr-2"}),"Manage Profiles"]})]})})}),o&&(0,t.jsx)(j.lG,{open:o,onOpenChange:d,children:(0,t.jsxs)(j.Cf,{className:"max-w-3xl",children:[(0,t.jsxs)(j.c7,{children:[(0,t.jsxs)(j.L3,{children:["Manage ",a," Profiles"]}),(0,t.jsx)(j.rr,{children:"Connect your profiles to use them with agents."})]}),(0,t.jsx)(J,{appSlug:s,appName:a,onProfileSelect:e=>{n(e.profile_id),d(!1)}})]})})]}):(0,t.jsxs)("div",{className:i,children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(T.l6,{value:r||"",onValueChange:e=>n(e||null),children:[(0,t.jsx)(T.bq,{className:"flex-1",children:(0,t.jsx)(T.yv,{placeholder:"Select a profile",children:x&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{children:x.profile_name}),x.is_connected?(0,t.jsx)(z.A,{className:"h-3 w-3 text-green-500"}):(0,t.jsx)(U.A,{className:"h-3 w-3 text-red-500"})]})})}),(0,t.jsx)(T.gC,{children:h.map(e=>(0,t.jsx)(T.eb,{value:e.profile_id,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{children:e.profile_name}),(0,t.jsx)("div",{className:"text-xs flex items-center gap-2",children:(0,t.jsx)("div",{className:"h-2 w-2 bg-green-500 rounded-full"})})]})},e.profile_id))})]}),c&&(0,t.jsx)(p.$,{size:"icon",variant:"outline",onClick:()=>d(!0),title:"Add new profile",children:(0,t.jsx)(S.A,{className:"h-4 w-4"})})]}),x&&!x.is_connected&&(0,t.jsxs)("p",{className:"text-xs text-destructive flex items-center gap-1",children:[(0,t.jsx)(U.A,{className:"h-3 w-3"}),"This profile is not connected. Please connect it first."]})]}),o&&(0,t.jsx)(j.lG,{open:o,onOpenChange:d,children:(0,t.jsxs)(j.Cf,{className:"max-w-3xl",children:[(0,t.jsxs)(j.c7,{children:[(0,t.jsxs)(j.L3,{children:["Manage ",a," Profiles"]}),(0,t.jsxs)(j.rr,{children:["Create and manage credential profiles for ",a,"."]})]}),(0,t.jsx)(J,{appSlug:s,appName:a,onProfileSelect:e=>{n(e.profile_id),d(!1)}})]})})]}):(0,t.jsxs)("div",{className:i,children:[(0,t.jsx)(F.Zp,{children:(0,t.jsx)(F.Wu,{className:"py-6",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(D.A,{className:"h-8 w-8 text-muted-foreground mx-auto mb-3"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground mb-3",children:["No credential profiles found for ",a]}),c&&(0,t.jsxs)(p.$,{size:"sm",variant:"outline",onClick:()=>d(!0),children:[(0,t.jsx)(S.A,{className:"h-4 w-4 mr-2"}),"Create Profile"]})]})})}),o&&(0,t.jsx)(j.lG,{open:o,onOpenChange:d,children:(0,t.jsxs)(j.Cf,{className:"max-w-3xl",children:[(0,t.jsxs)(j.c7,{children:[(0,t.jsxs)(j.L3,{children:["Manage ",a," Profiles"]}),(0,t.jsxs)(j.rr,{children:["Create and manage credential profiles for ",a,"."]})]}),(0,t.jsx)(J,{appSlug:s,appName:a,onProfileSelect:e=>{n(e.profile_id),d(!1)}})]})})]})};var ee=a(78948);let es=e=>{var s,a,n,i,c,o;let{step:d,selectedProfileId:m,onProfileSelect:u,onComplete:x}=e,[h,j]=(0,l.useState)("select"),[N,y]=(0,l.useState)(!1),[b,_]=(0,l.useState)(""),[w,T]=(0,l.useState)({}),[q,F]=(0,l.useState)(!1),E=(0,L.Ak)(),{data:R}=$(d.qualified_name),{data:M}=(0,K.h4)(),I="pipedream_profile"===d.type,D=(null==R||null==(n=R.connections)||null==(a=n[0])||null==(s=a.configSchema)?void 0:s.properties)||{},z=(null==R||null==(o=R.connections)||null==(c=o[0])||null==(i=c.configSchema)?void 0:i.required)||[],U=null==M?void 0:M.some(e=>e.app_slug===d.app_slug&&e.is_connected);(0,l.useEffect)(()=>{j("select"),y(!1),_(""),T({}),F(!1)},[d.qualified_name]);let Q=(0,l.useMemo)(()=>({id:d.qualified_name,name_slug:d.app_slug||d.qualified_name,name:d.service_name,description:"Connect your ".concat(d.service_name," account to use its tools"),img_src:"",custom_fields_json:"[]",categories:[],featured_weight:0,auth_type:"keys",connect:{allowed_domains:null,base_proxy_target_url:"",proxy_enabled:!1}}),[d.app_slug,d.qualified_name,d.service_name]),Y=(0,l.useCallback)(async()=>{if(!b.trim())return void r.oR.error("Please enter a profile name");y(!0);try{let e={mcp_qualified_name:d.qualified_name,profile_name:b.trim(),display_name:d.service_name,config:w,is_default:!1},s=await E.mutateAsync(e);r.oR.success("Profile created successfully!"),u(d.qualified_name,s.profile_id||"new-profile"),j("select"),_(""),T({}),null==x||x()}catch(e){r.oR.error(e.message||"Failed to create profile")}finally{y(!1)}},[b,w,d.qualified_name,d.service_name,E,u,x]),Z=(0,l.useCallback)((e,s)=>{T(a=>({...a,[e]:s}))},[]),B=(0,l.useCallback)(e=>{_(e.target.value)},[]),G=(0,l.useCallback)(e=>{"Enter"===e.key&&"create"===h&&Y()},[Y,h]),W=e=>z.includes(e),H=(0,l.useMemo)(()=>(0,t.jsx)("div",{className:"space-y-4",children:I?(0,t.jsxs)("div",{className:"space-y-4",children:[U?(0,t.jsx)(X,{appSlug:d.app_slug||"",appName:d.service_name,selectedProfileId:m,onProfileSelect:e=>{u(d.qualified_name,e)}}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(C.Fc,{className:"border-primary/20 bg-primary/5",children:[(0,t.jsx)(A.A,{className:"h-4 w-4"}),(0,t.jsxs)(C.TN,{children:["No connected ",d.service_name," profiles found. Create and connect one to continue."]})]}),(0,t.jsxs)(p.$,{onClick:()=>F(!0),className:"w-full",children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),"Connect ",d.service_name]})]}),U&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(k.w,{className:"flex-1"}),(0,t.jsx)("span",{className:"text-xs text-muted-foreground",children:"OR"}),(0,t.jsx)(k.w,{className:"flex-1"})]}),(0,t.jsxs)(p.$,{variant:"outline",onClick:()=>F(!0),className:"w-full",children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),"Connect Different Account"]})]})]}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(O,{mcpQualifiedName:d.qualified_name,mcpDisplayName:d.service_name,selectedProfileId:m,onProfileSelect:e=>{u(d.qualified_name,e)}}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(k.w,{className:"flex-1"}),(0,t.jsx)("span",{className:"text-xs text-muted-foreground",children:"OR"}),(0,t.jsx)(k.w,{className:"flex-1"})]}),(0,t.jsxs)(p.$,{variant:"outline",onClick:()=>{_("".concat(d.service_name," Profile")),j("create")},className:"w-full",children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),"Create New Profile"]})]})}),[d.service_name,d.qualified_name,d.app_slug,I,m,U,u]),V=(0,l.useMemo)(()=>(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"flex items-center gap-2",children:(0,t.jsx)(p.$,{variant:"link",size:"sm",onClick:()=>j("select"),className:"mb-4 p-0 h-auto font-normal text-muted-foreground hover:text-foreground",children:"← Back to Selection"})}),(0,t.jsxs)("h3",{className:"font-semibold",children:["Create ",d.service_name," Profile"]}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Set up a new credential profile for ",d.service_name]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.Label,{htmlFor:"profile-name",children:"Profile Name"}),(0,t.jsx)(g.p,{id:"profile-name",placeholder:"e.g., Personal Account, Work Account",value:b,onChange:B,onKeyDown:G,autoFocus:!0,className:"h-11"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"This helps you identify different configurations"})]}),Object.keys(D).length>0?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(P.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Connection Settings"})]}),Object.entries(D).map(e=>{let[s,a]=e;return(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(f.Label,{htmlFor:s,children:[a.title||s,W(s)&&(0,t.jsx)("span",{className:"text-destructive ml-1",children:"*"})]}),(0,t.jsx)(g.p,{id:s,type:"password"===a.format?"password":"text",placeholder:a.description||"Enter ".concat(s),value:w[s]||"",onChange:e=>Z(s,e.target.value),onKeyDown:G,className:"h-11"}),a.description&&(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:a.description})]},s)})]}):(0,t.jsxs)(C.Fc,{className:"border-primary/20 bg-primary/5",children:[(0,t.jsx)(A.A,{className:"h-4 w-4"}),(0,t.jsx)(C.TN,{children:"This service doesn't require any credentials to connect."})]})]}),(0,t.jsx)("div",{className:"pt-4 border-t",children:(0,t.jsx)(p.$,{onClick:Y,disabled:!b.trim()||N,className:"w-full",children:N?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v.A,{className:"h-4 w-4 animate-spin"}),"Creating..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),"Create & Continue"]})})})]}),[d.service_name,b,w,D,N,B,G,Z,Y,W]);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"space-y-6",children:"select"===h?H:V}),I&&(0,t.jsx)(ee.R,{app:Q,open:q,onOpenChange:F,mode:"profile-only",onComplete:(e,s,a,t)=>{u(d.qualified_name,e),F(!1),r.oR.success("Connected to ".concat(a," successfully!")),null==x||x()}})]})};var ea=a(25487),et=a(81284);let el=e=>{var s;let{step:a,config:r,onConfigUpdate:n}=e,i=(0,l.useCallback)((e,s)=>{let t={...r,[e]:s};n(a.qualified_name,t)},[r,n,a.qualified_name]);return(0,t.jsxs)("div",{className:"space-y-4",children:[a.custom_type&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(q.E,{variant:"outline",className:"bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800",children:a.custom_type.toUpperCase()}),(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Custom Server"})]}),(0,t.jsxs)(C.Fc,{className:"border-primary/20 bg-primary/5",children:[(0,t.jsx)(ea.A,{className:"h-4 w-4"}),(0,t.jsxs)(C.TN,{children:["Configure your personal ",a.service_name," server connection details."]})]}),(0,t.jsx)("div",{className:"space-y-4",children:null==(s=a.required_fields)?void 0:s.map(e=>(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.Label,{htmlFor:e.key,className:"text-sm font-medium",children:e.label}),(0,t.jsx)(g.p,{id:e.key,type:e.type,placeholder:e.placeholder,value:r[e.key]||"",onChange:s=>i(e.key,s.target.value),className:"h-11"}),e.description&&(0,t.jsxs)("div",{className:"flex items-start gap-2",children:[(0,t.jsx)(et.A,{className:"h-3 w-3 text-muted-foreground mt-0.5 flex-shrink-0"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]})]},e.key))})]})},er=e=>{let{item:s,open:a,onOpenChange:r,onInstall:n,isInstalling:i}=e,[c,o]=(0,l.useState)(0),[d,m]=(0,l.useState)(""),[u,x]=(0,l.useState)({}),[h,k]=(0,l.useState)({}),[C,A]=(0,l.useState)([]),[S,P]=(0,l.useState)(!1),T=(0,l.useCallback)(()=>{if(!(null==s?void 0:s.mcp_requirements))return[];let e=[];return s.mcp_requirements.filter(e=>"pipedream"===e.custom_type).forEach(s=>{e.push({id:s.qualified_name,title:"Connect ".concat(s.display_name),description:"Select an existing ".concat(s.display_name," profile or create a new one"),type:"pipedream_profile",service_name:s.display_name,qualified_name:s.qualified_name,app_slug:s.qualified_name})}),s.mcp_requirements.filter(e=>!e.custom_type).forEach(s=>{e.push({id:s.qualified_name,title:"Connect ".concat(s.display_name),description:"Select or create a credential profile for ".concat(s.display_name),type:"credential_profile",service_name:s.display_name,qualified_name:s.qualified_name})}),s.mcp_requirements.filter(e=>e.custom_type&&"pipedream"!==e.custom_type).forEach(s=>{var a;e.push({id:s.qualified_name,title:"Configure ".concat(s.display_name),description:"Enter your ".concat(s.display_name," server details"),type:"custom_server",service_name:s.display_name,qualified_name:s.qualified_name,custom_type:s.custom_type,required_fields:(null==(a=s.required_config)?void 0:a.map(e=>({key:e,label:"url"===e?"".concat(s.display_name," Server URL"):e,type:"url"===e?"url":"text",placeholder:"url"===e?"https://your-".concat(s.display_name.toLowerCase(),"-server.com"):"Enter your ".concat(e),description:"url"===e?"Your personal ".concat(s.display_name," server endpoint"):void 0})))||[]})}),e},[s]);(0,l.useEffect)(()=>{a&&s&&(o(0),m(s.name),x({}),k({}),P(!0),A(T()),P(!1))},[a,s,T]);let q=(0,l.useCallback)(e=>{m(e.target.value)},[]),F=(0,l.useCallback)((e,s)=>{x(a=>({...a,[e]:s||""}))},[]),E=(0,l.useCallback)((e,s)=>{k(a=>({...a,[e]:s}))},[]),R=(0,l.useCallback)(()=>{if(0===C.length)return!0;if(c>=C.length)return!!d.trim();let e=C[c];switch(e.type){case"credential_profile":case"pipedream_profile":return!!u[e.qualified_name];case"custom_server":var s;let a=h[e.qualified_name]||{};return(null==(s=e.required_fields)?void 0:s.every(e=>{let s=a[e.key];return s&&s.toString().trim().length>0}))||!1;default:return!1}},[c,C,u,h,d]),M=(0,l.useCallback)(()=>{c<C.length&&o(c+1)},[c,C.length]),L=(0,l.useCallback)(()=>{c>0&&o(c-1)},[c]),$=(0,l.useCallback)(async()=>{if(!s||!d.trim())return;let e={...h};C.forEach(s=>{if("pipedream_profile"===s.type){let a=u[s.qualified_name];a&&(e[s.qualified_name]={url:"https://remote.mcp.pipedream.net",headers:{"x-pd-app-slug":s.app_slug},profile_id:a})}}),await n(s,d,u,e)},[s,d,u,h,C,n]),I=C[c],O=c>=C.length,{avatar:D,color:z}=(0,l.useMemo)(()=>s?s.avatar&&s.avatar_color?{avatar:s.avatar,color:s.avatar_color}:(0,w.Z)(s.id):{avatar:"\uD83E\uDD16",color:"#000"},[s]);return s?(0,t.jsx)(j.lG,{open:a,onOpenChange:r,children:(0,t.jsxs)(j.Cf,{className:"sm:max-w-lg max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(j.c7,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"h-12 w-12 flex-shrink-0 rounded-lg flex items-center justify-center",style:{backgroundColor:z,boxShadow:"0 16px 48px -8px ".concat(z,"70, 0 8px 24px -4px ").concat(z,"50")},children:(0,t.jsx)("span",{className:"text-lg",children:D})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)(j.L3,{className:"text-left flex items-center gap-2",children:["Install ",s.name]}),(0,t.jsx)(j.rr,{className:"text-left",children:s.description})]})]}),C.length>0&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[C.map((e,s)=>(0,t.jsx)("div",{className:(0,_.cn)("h-2 w-2 rounded-full",s<=c?"bg-primary":"bg-muted")},s)),(0,t.jsx)("div",{className:"h-px bg-muted flex-1"}),(0,t.jsx)("div",{className:(0,_.cn)("h-2 w-2 rounded-full",O?"bg-primary":"bg-muted")})]})]}),(0,t.jsx)("div",{className:"mt-6",children:(0,t.jsx)(()=>S?(0,t.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(v.A,{className:"h-5 w-5 animate-spin"}),(0,t.jsx)("span",{className:"text-sm",children:"Preparing installation..."})]})}):0===C.length||O?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"h-8 w-8 rounded-full bg-green-100 dark:bg-green-900/20 flex items-center justify-center",children:(0,t.jsx)(N.A,{className:"h-5 w-5 text-green-600 dark:text-green-400"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold",children:"Ready to install!"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Give your agent a name and we'll set everything up."})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(f.Label,{htmlFor:"instance-name",children:"Agent Name"}),(0,t.jsx)(g.p,{id:"instance-name",placeholder:"Enter a name for this agent",value:d,onChange:q,className:"h-11"})]})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"h-4 w-4 rounded-full bg-primary text-primary-foreground text-sm font-semibold flex items-center justify-center",children:c+1}),(0,t.jsx)("h3",{className:"font-semibold",children:I.title})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:I.description})]}),(0,t.jsxs)("div",{children:[("credential_profile"===I.type||"pipedream_profile"===I.type)&&(0,t.jsx)(es,{step:I,selectedProfileId:u[I.qualified_name],onProfileSelect:F,onComplete:()=>{c<C.length-1&&setTimeout(()=>o(c+1),500)}}),"custom_server"===I.type&&(0,t.jsx)(el,{step:I,config:h[I.qualified_name]||{},onConfigUpdate:E})]}),C.length>1&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"flex items-center gap-2",children:C.map((e,s)=>(0,t.jsx)("div",{className:(0,_.cn)("h-1 flex-1 rounded-full transition-colors",s<=c?"bg-primary":"bg-muted")},s))}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Step ",c+1," of ",C.length]})]})]}),{})}),(0,t.jsxs)("div",{className:"flex gap-3 pt-6 border-t",children:[c>0&&(0,t.jsx)(p.$,{variant:"outline",onClick:L,className:"flex-1",children:"Back"}),O||0===C.length?(0,t.jsx)(p.$,{onClick:$,disabled:i||!d.trim(),className:"flex-1",children:i?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v.A,{className:"h-4 w-4 animate-spin"}),"Installing..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y.A,{className:"h-4 w-4"}),"Install Agent"]})}):(0,t.jsxs)(p.$,{onClick:M,disabled:!R(),className:"flex-1",children:["Continue",(0,t.jsx)(b.A,{className:"h-4 w-4"})]})]})]})}):null};var en=a(25657),ei=a(89340);let ec=()=>(0,t.jsxs)(ei.z,{icon:en.A,children:[(0,t.jsx)("span",{className:"text-primary",children:"Custom agents"})," that",(0,t.jsx)("br",{}),(0,t.jsx)("span",{className:"text-muted-foreground",children:"automate"})," your ",(0,t.jsx)("span",{className:"text-muted-foreground",children:"workflows"}),"."]});var eo=a(86151),ed=a(57434),em=a(51362);let eu=e=>{let{value:s,isActive:a,onClick:l,children:r}=e,{theme:n}=(0,em.D)(),i="dark"===n;return(0,t.jsxs)("button",{onClick:l,className:(0,_.cn)("relative flex items-center justify-center gap-2 rounded-2xl px-4 py-2.5 text-sm font-medium transition-all duration-300",i?"hover:bg-white/5":"hover:bg-muted/80",a?i?"text-white":"text-foreground bg-background border border-border/50":i?"text-white/50 hover:text-white/70":"text-muted-foreground hover:text-foreground"),style:a&&i?{background:"linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08))",backdropFilter:"blur(12px)",boxShadow:"\n          0 4px 8px rgba(0, 0, 0, 0.1),\n          0 0 20px rgba(255, 255, 255, 0.1),\n          0 0 40px rgba(255, 255, 255, 0.1),\n          inset 0 1px 0 rgba(255, 255, 255, 0.2)\n        "}:void 0,children:[a&&i&&(0,t.jsx)("div",{className:"absolute inset-0 rounded-2xl opacity-40 blur-sm",style:{background:"linear-gradient(45deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1))",zIndex:-1}}),r]})},ex=e=>{let{tabs:s,activeTab:a,onTabChange:l,className:r}=e,{theme:n}=(0,em.D)(),i="dark"===n;return(0,t.jsx)("div",{className:(0,_.cn)("overflow-hidden grid w-full max-w-lg mx-auto rounded-3xl p-1.5",i?"border-white/5":"border-border/20 bg-muted",r),style:{gridTemplateColumns:"repeat(".concat(s.length,", 1fr)"),...i?{background:"rgba(255, 255, 255, 0.05)",backdropFilter:"blur(20px)"}:{}},children:s.map(e=>{let s=e.icon;return(0,t.jsxs)(eu,{value:e.value,isActive:a===e.value,onClick:()=>l(e.value),children:[(0,t.jsx)(s,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"hidden sm:inline",children:e.label}),e.shortLabel&&(0,t.jsx)("span",{className:"sm:hidden",children:e.shortLabel})]},e.value)})})},eh=[{value:"my-agents",icon:en.A,label:"My Agents"},{value:"marketplace",icon:eo.A,label:"Marketplace",shortLabel:"Market"},{value:"my-templates",icon:ed.A,label:"My Templates",shortLabel:"Templates"}],ep=e=>{let{activeTab:s,onTabChange:a}=e;return(0,t.jsx)(ex,{tabs:eh,activeTab:s,onTabChange:a})};var eg=a(47924);let ef=e=>{let{placeholder:s,value:a,onChange:l,className:r=""}=e;return(0,t.jsxs)("div",{className:"relative flex-1 max-w-2xl ".concat(r),children:[(0,t.jsx)(eg.A,{className:"absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground"}),(0,t.jsx)(g.p,{placeholder:s,value:a,onChange:e=>l(e.target.value),className:"pl-12 h-12 rounded-xl bg-muted/50 border-0 focus:bg-background focus:ring-2 focus:ring-primary/20 transition-all"})]})},ej=e=>{let{hasAgents:s,onCreateAgent:a,onClearFilters:l}=e;return(0,t.jsx)("div",{className:"flex flex-col items-center justify-center py-16 px-4",children:(0,t.jsxs)("div",{className:"flex flex-col items-center text-center max-w-md space-y-6",children:[(0,t.jsx)("div",{className:"rounded-full bg-muted p-6",children:s?(0,t.jsx)(eg.A,{className:"h-12 w-12 text-muted-foreground"}):(0,t.jsx)(en.A,{className:"h-12 w-12 text-muted-foreground"})}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h2",{className:"text-2xl font-semibold text-foreground",children:s?"No agents found":"No agents yet"}),(0,t.jsx)("p",{className:"text-muted-foreground leading-relaxed",children:s?"No agents match your current search and filter criteria. Try adjusting your filters or search terms.":"Create your first agent to start automating tasks with custom instructions and tools. Configure custom AgentPress capabilities to fine tune agent according to your needs."})]}),s?(0,t.jsx)(p.$,{variant:"outline",onClick:l,className:"mt-4",children:"Clear filters"}):(0,t.jsxs)(p.$,{size:"lg",onClick:a,className:"mt-4",children:[(0,t.jsx)(S.A,{className:"h-5 w-5"}),"Create your first agent"]})]})})};var ev=a(2775),eN=a(48313),ey=a(71366),eb=a(91171),e_=a(34869),ew=a(69074),ek=a(92657);let eC=e=>{let{isKortixTeam:s}=e;return s?(0,t.jsxs)(q.E,{variant:"secondary",className:"bg-blue-100 text-blue-700 border-0 dark:bg-blue-950 dark:text-blue-300",children:[(0,t.jsx)(N.A,{className:"h-3 w-3 mr-1"}),"Kortix"]}):(0,t.jsxs)(q.E,{variant:"secondary",className:"bg-green-100 text-green-700 border-0 dark:bg-green-950 dark:text-green-300",children:[(0,t.jsx)(D.A,{className:"h-3 w-3 mr-1"}),"Community"]})},eA=e=>{let{isPublic:s}=e;return s?(0,t.jsxs)(q.E,{variant:"default",className:"bg-green-100 text-green-700 border-0 dark:bg-green-950 dark:text-green-300",children:[(0,t.jsx)(e_.A,{className:"h-3 w-3 mr-1"}),"Public"]}):(0,t.jsxs)(q.E,{variant:"secondary",className:"bg-gray-100 text-gray-700 border-0 dark:bg-gray-800 dark:text-gray-300",children:[(0,t.jsx)(eb.A,{className:"h-3 w-3 mr-1"}),"Private"]})},eS=e=>{let{agent:s}=e;return(0,t.jsxs)("div",{className:"flex gap-1",children:[s.current_version&&(0,t.jsxs)(q.E,{variant:"outline",className:"text-xs",children:[(0,t.jsx)(ev.A,{className:"h-3 w-3 mr-1"}),s.current_version.version_name]}),s.is_public&&(0,t.jsxs)(q.E,{variant:"outline",className:"text-xs",children:[(0,t.jsx)(A.A,{className:"h-3 w-3 mr-1"}),"Published"]})]})},eP=e=>{let{data:s}=e;return(0,t.jsxs)("div",{className:"flex items-center justify-between text-xs text-muted-foreground",children:[(0,t.jsx)("div",{className:"flex items-center gap-1",children:s.is_kortix_team?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y.A,{className:"h-3 w-3"}),(0,t.jsxs)("span",{children:[s.download_count," installs"]})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(D.A,{className:"h-3 w-3"}),(0,t.jsxs)("span",{children:["by ",s.creator_name]})]})}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(ew.A,{className:"h-3 w-3"}),(0,t.jsx)("span",{children:new Date(s.marketplace_published_at||s.created_at).toLocaleDateString()})]})]})},eT=e=>{let{data:s}=e;return(0,t.jsxs)("div",{className:"space-y-1 text-xs text-muted-foreground",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(ew.A,{className:"h-3 w-3"}),(0,t.jsxs)("span",{children:["Created ",new Date(s.created_at).toLocaleDateString()]})]}),s.is_public&&(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(y.A,{className:"h-3 w-3"}),(0,t.jsxs)("span",{children:[s.download_count," downloads"]})]})]})},eq=()=>(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsx)("span",{className:"text-muted-foreground text-xs",children:"By me"})}),eF=e=>{let{onAction:s,isActioning:a,data:l}=e;return(0,t.jsx)(p.$,{onClick:e=>null==s?void 0:s(l,e),disabled:a,className:"w-full",size:"sm",children:a?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v.A,{className:"h-4 w-4 animate-spin "}),"Installing..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y.A,{className:"h-4 w-4 "}),"Install Agent"]})})},eE=e=>{let{data:s,onPrimaryAction:a,onSecondaryAction:l,isActioning:r}=e;return(0,t.jsx)("div",{className:"space-y-2",children:s.is_public?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(p.$,{onClick:e=>null==a?void 0:a(s,e),disabled:r,variant:"outline",className:"w-full",size:"sm",children:r?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v.A,{className:"h-3 w-3 animate-spin "}),"Unpublishing..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(eb.A,{className:"h-3 w-3 "}),"Make Private"]})}),(0,t.jsxs)(p.$,{onClick:e=>null==l?void 0:l(s,e),variant:"ghost",size:"sm",className:"w-full",children:[(0,t.jsx)(ek.A,{className:"h-3 w-3 "}),"View in Marketplace"]})]}):(0,t.jsx)(p.$,{onClick:e=>null==a?void 0:a(s,e),disabled:r,variant:"default",className:"w-full",size:"sm",children:r?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v.A,{className:"h-3 w-3 animate-spin "}),"Publishing..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(e_.A,{className:"h-3 w-3 "}),"Publish to Marketplace"]})})})},eR=e=>{let{avatar:s,color:a}=e;return(0,t.jsxs)("div",{className:"relative h-14 w-14 flex items-center justify-center rounded-2xl",style:{backgroundColor:a},children:[(0,t.jsx)("div",{className:"text-2xl",children:s}),(0,t.jsx)("div",{className:"absolute inset-0 rounded-2xl pointer-events-none opacity-0 dark:opacity-100 transition-opacity",style:{boxShadow:"0 16px 48px -8px ".concat(a,"70, 0 8px 24px -4px ").concat(a,"50")}})]})},eM=e=>{let{tags:s}=e;return s&&0!==s.length?(0,t.jsxs)("div",{className:"flex flex-wrap gap-1",children:[s.slice(0,2).map(e=>(0,t.jsx)(q.E,{variant:"outline",className:"text-xs border-border/50",children:e},e)),s.length>2&&(0,t.jsxs)(q.E,{variant:"outline",className:"text-xs border-border/50",children:["+",s.length-2]})]}):null},eL=e=>{let{mode:s,data:a,styling:l,isActioning:r=!1,onPrimaryAction:n,onSecondaryAction:i,onClick:c}=e,{avatar:o,color:d}=l;return(0,t.jsxs)("div",{className:"group relative bg-card rounded-2xl overflow-hidden shadow-sm transition-all duration-300 border border-border/50 hover:border-primary/20 cursor-pointer",onClick:()=>null==c?void 0:c(a),children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"}),(0,t.jsxs)("div",{className:"relative p-6",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,t.jsx)(eR,{avatar:o,color:d}),(()=>{switch(s){case"marketplace":return(0,t.jsx)(eC,{isKortixTeam:a.is_kortix_team});case"template":return(0,t.jsx)(eA,{isPublic:a.is_public});case"agent":return(0,t.jsx)(eS,{agent:a});default:return null}})()]}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-foreground mb-2 line-clamp-1",children:a.name}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-4 line-clamp-2 min-h-[2.5rem]",children:a.description||"No description available"}),(0,t.jsx)(eM,{tags:a.tags}),(0,t.jsx)("div",{className:"mt-4 mb-4",children:(()=>{switch(s){case"marketplace":return(0,t.jsx)(eP,{data:a});case"template":return(0,t.jsx)(eT,{data:a});case"agent":return(0,t.jsx)(eq,{});default:return null}})()}),(()=>{switch(s){case"marketplace":return(0,t.jsx)(eF,{onAction:n,isActioning:r,data:a});case"template":return(0,t.jsx)(eE,{data:a,onPrimaryAction:n,onSecondaryAction:i,isActioning:r});default:return null}})()]})]})},e$=e=>{let{agent:s,isOpen:a,onClose:l,onCustomize:r,onChat:n,onPublish:i,onUnpublish:c,isPublishing:o,isUnpublishing:d}=e;if(!s)return null;let{avatar:m,color:u}=(e=>e.avatar&&e.avatar_color?{avatar:e.avatar,color:e.avatar_color}:(0,w.Z)(e.agent_id))(s);return(0,t.jsx)(j.lG,{open:a,onOpenChange:l,children:(0,t.jsxs)(j.Cf,{className:"max-w-md p-0 overflow-hidden border-none",children:[(0,t.jsx)(j.L3,{className:"sr-only",children:"Agent actions"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)("div",{className:"h-32 flex items-center justify-center relative bg-gradient-to-br from-opacity-90 to-opacity-100",style:{backgroundColor:u},children:[(0,t.jsx)("div",{className:"text-6xl drop-shadow-sm",children:m}),(0,t.jsx)("div",{className:"absolute top-4 right-4 flex gap-2",children:s.is_default&&(0,t.jsx)(M.A,{className:"h-5 w-5 text-white fill-white drop-shadow-sm"})})]}),(0,t.jsxs)("div",{className:"p-4 space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-foreground",children:s.name}),s.current_version&&(0,t.jsxs)(q.E,{variant:"outline",className:"text-xs",children:[(0,t.jsx)(ev.A,{className:"h-3 w-3 mr-1"}),s.current_version.version_name]}),s.is_public&&(0,t.jsxs)(q.E,{variant:"outline",className:"text-xs",children:[(0,t.jsx)(A.A,{className:"h-3 w-3 mr-1"}),"Published"]})]}),(0,t.jsx)("p",{className:"text-muted-foreground text-sm leading-relaxed",children:function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:120;return!e||e.length<=s?e||"Try out this agent":e.substring(0,s)+"..."}(s.description)})]}),(0,t.jsxs)("div",{className:"flex gap-3 pt-2",children:[(0,t.jsxs)(p.$,{onClick:()=>r(s.agent_id),variant:"outline",className:"flex-1 gap-2",children:[(0,t.jsx)(eN.A,{className:"h-4 w-4"}),"Customize"]}),(0,t.jsxs)(p.$,{onClick:()=>n(s.agent_id),className:"flex-1 gap-2 bg-primary hover:bg-primary/90",children:[(0,t.jsx)(ey.A,{className:"h-4 w-4"}),"Chat"]})]}),(0,t.jsx)("div",{className:"pt-2",children:s.is_public?(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm text-muted-foreground",children:[(0,t.jsx)("span",{children:"Published as secure template"}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(y.A,{className:"h-3 w-3"}),s.download_count||0," downloads"]})]}),(0,t.jsx)(p.$,{onClick:()=>c(s.agent_id),disabled:d,variant:"outline",className:"w-full gap-2",children:d?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"}),"Making Private..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(eb.A,{className:"h-4 w-4"}),"Make Private"]})})]}):(0,t.jsx)(p.$,{onClick:()=>i(s.agent_id),disabled:o,variant:"outline",className:"w-full gap-2",children:o?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"}),"Publishing..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(A.A,{className:"h-4 w-4"}),"Publish as Template"]})})})]})]})]})})},eI=e=>{let{agents:s,onEditAgent:a,onDeleteAgent:i,onToggleDefault:c,deleteAgentMutation:h}=e,[g,f]=(0,l.useState)(null),[j,v]=(0,l.useState)(null),[N,y]=(0,l.useState)(null),b=(0,n.useRouter)(),_=x(),k=function(){let e=(0,o.jE)();return(0,d.n)({mutationFn:async e=>{let s=(0,m.U)(),{data:{session:a}}=await s.auth.getSession();if(!a)throw Error("You must be logged in to create templates");let t=await fetch("".concat(u,"/templates"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a.access_token)},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json().catch(()=>({message:"Unknown error"}))).message||"HTTP ".concat(t.status,": ").concat(t.statusText));return t.json()},onSuccess:()=>{e.invalidateQueries({queryKey:["secure-mcp","marketplace-templates"]}),e.invalidateQueries({queryKey:["secure-mcp","my-templates"]})}})}(),C=e=>{f(e)},A=async e=>{try{v(e),await k.mutateAsync({agent_id:e,make_public:!0,tags:[]}),r.oR.success("Agent published!"),f(null)}catch(e){r.oR.error("Failed to create secure template")}finally{v(null)}},S=async e=>{try{y(e),await _.mutateAsync(e),r.oR.success("Agent made private"),f(null)}catch(e){r.oR.error("Failed to make agent private")}finally{y(null)}},P=e=>e.avatar&&e.avatar_color?{avatar:e.avatar,color:e.avatar_color}:(0,w.Z)(e.agent_id);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:s.map(e=>{let s={...e,id:e.agent_id};return(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)(eL,{mode:"agent",data:s,styling:P(e),onClick:()=>C(e)}),(0,t.jsx)("div",{className:"absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity",children:!e.is_default&&(0,t.jsxs)(Z.Lt,{children:[(0,t.jsx)(Z.tv,{asChild:!0,children:(0,t.jsx)(p.$,{variant:"ghost",size:"sm",className:"h-7 w-7 p-0 hover:bg-destructive/10 hover:text-destructive text-muted-foreground",disabled:h.isPending,title:"Delete agent",onClick:e=>e.stopPropagation(),children:(0,t.jsx)(V.A,{className:"h-3.5 w-3.5"})})}),(0,t.jsxs)(Z.EO,{className:"max-w-md",children:[(0,t.jsxs)(Z.wd,{children:[(0,t.jsx)(Z.r7,{className:"text-xl",children:"Delete Agent"}),(0,t.jsxs)(Z.$v,{children:['Are you sure you want to delete "',e.name,'"? This action cannot be undone.',e.is_public&&(0,t.jsx)("span",{className:"block mt-2 text-amber-600 dark:text-amber-400",children:"Note: This agent is currently published to the marketplace and will be removed from there as well."})]})]}),(0,t.jsxs)(Z.ck,{children:[(0,t.jsx)(Z.Zr,{onClick:e=>e.stopPropagation(),children:"Cancel"}),(0,t.jsx)(Z.Rx,{onClick:s=>{s.stopPropagation(),i(e.agent_id)},disabled:h.isPending,className:"bg-destructive hover:bg-destructive/90 text-white",children:h.isPending?"Deleting...":"Delete"})]})]})]})})]},e.agent_id)})}),(0,t.jsx)(e$,{agent:g,isOpen:!!g,onClose:()=>f(null),onCustomize:e=>{b.push("/agents/config/".concat(e)),f(null)},onChat:e=>{b.push("/dashboard?agent_id=".concat(e)),f(null)},onPublish:A,onUnpublish:S,isPublishing:j===(null==g?void 0:g.agent_id),isUnpublishing:N===(null==g?void 0:g.agent_id)})]})};var eO=a(68856);let eD=e=>{let{viewMode:s}=e;return(0,t.jsx)("div",{className:"grid"===s?"grid gap-6 sm:grid-cols-2 lg:grid-cols-4":"space-y-4",children:Array.from({length:"grid"===s?4:8},(e,s)=>(0,t.jsxs)("div",{className:"bg-neutral-100 dark:bg-sidebar border border-border rounded-2xl overflow-hidden",children:[(0,t.jsx)(eO.Skeleton,{className:"h-50"}),(0,t.jsxs)("div",{className:"p-4 space-y-3",children:[(0,t.jsx)(eO.Skeleton,{className:"h-5 rounded"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eO.Skeleton,{className:"h-4 rounded"}),(0,t.jsx)(eO.Skeleton,{className:"h-4 rounded w-3/4"})]})]})]},s))})};var ez=a(42355),eU=a(5623),eK=a(13052);let eQ=e=>{let{currentPage:s,totalPages:a,onPageChange:r,isLoading:n=!1}=e;if(a<=1)return null;let i=(()=>{let e=[],t=[];for(let t=Math.max(2,s-2);t<=Math.min(a-1,s+2);t++)e.push(t);return s-2>2?t.push(1,"..."):t.push(1),t.push(...e),s+2<a-1?t.push("...",a):a>1&&t.push(a),t})();return(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,t.jsx)(p.$,{variant:"outline",size:"sm",onClick:()=>r(s-1),disabled:s<=1||n,className:"h-8 w-8 p-0",children:(0,t.jsx)(ez.A,{className:"h-4 w-4"})}),i.map((e,a)=>(0,t.jsx)(l.Fragment,{children:"..."===e?(0,t.jsx)("div",{className:"flex h-8 w-8 items-center justify-center",children:(0,t.jsx)(eU.A,{className:"h-4 w-4"})}):(0,t.jsx)(p.$,{variant:s===e?"default":"outline",size:"sm",onClick:()=>r(e),disabled:n,className:"h-8 w-8 p-0",children:e})},a)),(0,t.jsx)(p.$,{variant:"outline",size:"sm",onClick:()=>r(s+1),disabled:s>=a||n,className:"h-8 w-8 p-0",children:(0,t.jsx)(eK.A,{className:"h-4 w-4"})})]})},eY=e=>{let{agentsSearchQuery:s,setAgentsSearchQuery:a,agentsLoading:l,agents:r,agentsPagination:n,viewMode:i,onCreateAgent:c,onEditAgent:o,onDeleteAgent:d,onToggleDefault:m,onClearFilters:u,deleteAgentMutation:x,setAgentsPage:h}=e;return(0,t.jsxs)("div",{className:"space-y-6 mt-8",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between mb-6",children:[(0,t.jsx)(ef,{placeholder:"Search your agents...",value:s,onChange:a}),(0,t.jsxs)(p.$,{onClick:c,className:"rounded-xl",children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),"Create Agent"]})]}),l?(0,t.jsx)(eD,{viewMode:i}):0===r.length?(0,t.jsx)(ej,{hasAgents:((null==n?void 0:n.total)||0)>0,onCreateAgent:c,onClearFilters:u}):(0,t.jsx)(eI,{agents:r,onEditAgent:o,onDeleteAgent:d,onToggleDefault:m,deleteAgentMutation:x}),n&&n.pages>1&&(0,t.jsx)(eQ,{currentPage:n.page,totalPages:n.pages,onPageChange:h,isLoading:l})]})},eZ=e=>{let{title:s,subtitle:a,icon:l=(0,t.jsx)(A.A,{className:"h-5 w-5 text-white"})}=e;return(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"flex h-10 w-10 items-center justify-center rounded-2xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg",children:l}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-foreground",children:s}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:a})]})]})},eB=e=>{let{marketplaceSearchQuery:s,setMarketplaceSearchQuery:a,marketplaceFilter:l,setMarketplaceFilter:r,marketplaceLoading:n,allMarketplaceItems:i,kortixTeamItems:c,communityItems:o,installingItemId:d,onInstallClick:m,getItemStyling:u}=e;return(0,t.jsxs)("div",{className:"space-y-6 mt-8",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[(0,t.jsx)(ef,{placeholder:"Search agents...",value:s,onChange:a}),(0,t.jsxs)(T.l6,{value:l,onValueChange:e=>r(e),children:[(0,t.jsx)(T.bq,{className:"w-[180px] h-12 rounded-xl",children:(0,t.jsx)(T.yv,{placeholder:"Filter agents"})}),(0,t.jsxs)(T.gC,{children:[(0,t.jsx)(T.eb,{value:"all",children:"All Agents"}),(0,t.jsx)(T.eb,{value:"kortix",children:"Kortix Verified"}),(0,t.jsx)(T.eb,{value:"community",children:"Community"})]})]})]}),n?(0,t.jsx)("div",{className:"grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:Array.from({length:8}).map((e,s)=>(0,t.jsxs)("div",{className:"bg-card rounded-2xl overflow-hidden shadow-sm",children:[(0,t.jsx)(eO.Skeleton,{className:"h-48"}),(0,t.jsxs)("div",{className:"p-6 space-y-3",children:[(0,t.jsx)(eO.Skeleton,{className:"h-5 rounded"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eO.Skeleton,{className:"h-4 rounded"}),(0,t.jsx)(eO.Skeleton,{className:"h-4 rounded w-3/4"})]}),(0,t.jsx)(eO.Skeleton,{className:"h-10 rounded-full"})]})]},s))}):0===i.length?(0,t.jsx)("div",{className:"text-center py-12",children:(0,t.jsx)("p",{className:"text-muted-foreground",children:s?"No templates found matching your criteria. Try adjusting your search or filters.":"No agent templates are currently available in the marketplace."})}):(0,t.jsx)("div",{className:"space-y-12",children:"all"===l?(0,t.jsxs)(t.Fragment,{children:[c.length>0&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(eZ,{title:"Verified by Kortix",subtitle:"Official agents, maintained and supported"}),(0,t.jsx)("div",{className:"grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:c.map(e=>(0,t.jsx)(eL,{mode:"marketplace",data:e,styling:u(e),isActioning:d===e.id,onPrimaryAction:m,onClick:()=>m(e)},e.id))})]}),o.length>0&&(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsx)("div",{className:"grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:o.map(e=>(0,t.jsx)(eL,{mode:"marketplace",data:e,styling:u(e),isActioning:d===e.id,onPrimaryAction:m,onClick:()=>m(e)},e.id))})})]}):(0,t.jsx)("div",{className:"grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:i.map(e=>(0,t.jsx)(eL,{mode:"marketplace",data:e,styling:u(e),isActioning:d===e.id,onPrimaryAction:m,onClick:()=>m(e)},e.id))})})]})};var eG=a(1243);let eW=e=>{let{templatesError:s,templatesLoading:a,myTemplates:l,templatesActioningId:r,onPublish:n,onUnpublish:i,onViewInMarketplace:c,onSwitchToMyAgents:o,getTemplateStyling:d}=e;return(0,t.jsx)("div",{className:"space-y-6 mt-8",children:s?(0,t.jsxs)(C.Fc,{variant:"destructive",className:"rounded-2xl",children:[(0,t.jsx)(eG.A,{className:"h-4 w-4"}),(0,t.jsx)(C.TN,{children:"Failed to load your templates. Please try again later."})]}):a?(0,t.jsx)("div",{className:"grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:Array.from({length:8}).map((e,s)=>(0,t.jsxs)("div",{className:"bg-card rounded-2xl overflow-hidden shadow-sm",children:[(0,t.jsx)(eO.Skeleton,{className:"h-48"}),(0,t.jsxs)("div",{className:"p-6 space-y-3",children:[(0,t.jsx)(eO.Skeleton,{className:"h-5 rounded"}),(0,t.jsx)(eO.Skeleton,{className:"h-4 rounded w-3/4"}),(0,t.jsx)("div",{className:"flex gap-2",children:(0,t.jsx)(eO.Skeleton,{className:"h-10 rounded-full flex-1"})})]})]},s))}):(null==l?void 0:l.length)===0?(0,t.jsxs)("div",{className:"text-center py-16",children:[(0,t.jsx)("div",{className:"mx-auto w-20 h-20 bg-gradient-to-br from-primary/20 to-primary/10 rounded-3xl flex items-center justify-center mb-6",children:(0,t.jsx)(e_.A,{className:"h-10 w-10 text-primary"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold mb-3",children:"No templates yet"}),(0,t.jsx)("p",{className:"text-muted-foreground mb-8 max-w-md mx-auto",children:"Create your first secure agent template to share with the community while keeping your credentials safe."}),(0,t.jsxs)(p.$,{onClick:o,size:"lg",children:[(0,t.jsx)(S.A,{className:"h-4 w-4 mr-2"}),"Create Your First Agent"]})]}):(0,t.jsx)("div",{className:"grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:null==l?void 0:l.map(e=>{let s=r===e.template_id;return(0,t.jsx)(eL,{mode:"template",data:e,styling:d(e),isActioning:s,onPrimaryAction:e.is_public?()=>i(e.template_id,e.name):()=>n(e),onSecondaryAction:e.is_public?c:void 0},e.template_id)})})})},eH=e=>{let{publishDialog:s,publishTags:a,templatesActioningId:l,onClose:r,onPublishTagsChange:n,onPublish:i}=e;return(0,t.jsx)(j.lG,{open:!!s,onOpenChange:r,children:(0,t.jsxs)(j.Cf,{className:"sm:max-w-md",children:[(0,t.jsxs)(j.c7,{children:[(0,t.jsx)(j.L3,{children:"Publish Template to Marketplace"}),(0,t.jsxs)(j.rr,{children:['Make "',null==s?void 0:s.templateName,'" available for the community to discover and install.']})]}),(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{children:[(0,t.jsx)(f.Label,{htmlFor:"tags",children:"Tags (optional)"}),(0,t.jsx)(g.p,{id:"tags",placeholder:"automation, productivity, data-analysis",value:a,onChange:e=>n(e.target.value),className:"mt-1"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Separate tags with commas to help users discover your template"})]})}),(0,t.jsxs)(j.Es,{children:[(0,t.jsx)(p.$,{variant:"outline",onClick:r,disabled:!!l,children:"Cancel"}),(0,t.jsx)(p.$,{onClick:i,disabled:!!l,children:l?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(v.A,{className:"h-4 w-4 animate-spin mr-2"}),"Publishing..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(e_.A,{className:"h-4 w-4 mr-2"}),"Publish Template"]})})]})]})})},eV=e=>{let{count:s=6}=e;return(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:Array.from({length:s}).map((e,s)=>(0,t.jsxs)("div",{className:"p-2 bg-neutral-100 dark:bg-sidebar rounded-2xl overflow-hidden group",children:[(0,t.jsx)("div",{className:"h-24 flex items-center justify-center relative bg-gradient-to-br from-opacity-90 to-opacity-100",children:(0,t.jsx)(eO.Skeleton,{className:"h-24 w-full rounded-xl"})}),(0,t.jsxs)("div",{className:"space-y-2 mt-4 mb-4",children:[(0,t.jsx)(eO.Skeleton,{className:"h-6 w-32 rounded"}),(0,t.jsx)(eO.Skeleton,{className:"h-4 w-24 rounded"})]})]},s))})};function eJ(){let{enabled:e,loading:s}=(0,h.u)("custom_agents"),{enabled:a,loading:p}=(0,h.u)("agent_marketplace"),g=(0,n.useRouter)(),f=s||p;(0,l.useEffect)(()=>{f||e||g.replace("/dashboard")},[f,e,g]);let[j,v]=(0,l.useState)("my-agents"),[N,y]=(0,l.useState)(!1),[b,_]=(0,l.useState)(null),[k,C]=(0,l.useState)("grid"),[A,S]=(0,l.useState)(1),[P,T]=(0,l.useState)(""),[q,F]=(0,l.useState)("created_at"),[E,R]=(0,l.useState)("desc"),[M,L]=(0,l.useState)({hasDefaultAgent:!1,hasMcpTools:!1,hasAgentpressTools:!1,selectedTools:[]}),[$,I]=(0,l.useState)(1),[O,D]=(0,l.useState)(""),[z,U]=(0,l.useState)([]),[K,Q]=(0,l.useState)("newest"),[Y,Z]=(0,l.useState)(null),[B,G]=(0,l.useState)(null),[W,H]=(0,l.useState)(!1),[V,J]=(0,l.useState)("all"),[X,ee]=(0,l.useState)(null),[es,ea]=(0,l.useState)(null),[et,el]=(0,l.useState)(""),en=(0,l.useMemo)(()=>{let e={page:A,limit:20,search:P||void 0,sort_by:q,sort_order:E};return M.hasDefaultAgent&&(e.has_default=!0),M.hasMcpTools&&(e.has_mcp_tools=!0),M.hasAgentpressTools&&(e.has_agentpress_tools=!0),M.selectedTools.length>0&&(e.tools=M.selectedTools.join(",")),e},[A,P,q,E,M]),ei=(0,l.useMemo)(()=>({limit:20,offset:($-1)*20,search:O||void 0,tags:z.length>0?z.join(","):void 0}),[$,O,z]),{data:eo,isLoading:ed,error:em,refetch:eu}=(0,i._F)(en),{data:ex,isLoading:eh}=(0,c.I)({queryKey:["secure-mcp","marketplace-templates",ei],queryFn:async()=>{let e=(0,m.U)(),{data:{session:s}}=await e.auth.getSession();if(!s)throw Error("You must be logged in to view marketplace templates");let a=new URLSearchParams;(null==ei?void 0:ei.limit)&&a.set("limit",ei.limit.toString()),(null==ei?void 0:ei.offset)&&a.set("offset",ei.offset.toString()),(null==ei?void 0:ei.search)&&a.set("search",ei.search),(null==ei?void 0:ei.tags)&&a.set("tags",ei.tags);let t=await fetch("".concat(u,"/templates/marketplace?").concat(a),{headers:{Authorization:"Bearer ".concat(s.access_token)}});if(!t.ok)throw Error((await t.json().catch(()=>({message:"Unknown error"}))).message||"HTTP ".concat(t.status,": ").concat(t.statusText));return t.json()}}),{data:eg,isLoading:ef,error:ej}=(0,c.I)({queryKey:["secure-mcp","my-templates"],queryFn:async()=>{let e=(0,m.U)(),{data:{session:s}}=await e.auth.getSession();if(!s)throw Error("You must be logged in to view your templates");let a=await fetch("".concat(u,"/templates/my"),{headers:{Authorization:"Bearer ".concat(s.access_token)}});if(!a.ok)throw Error((await a.json().catch(()=>({message:"Unknown error"}))).message||"HTTP ".concat(a.status,": ").concat(a.statusText));return a.json()}}),ev=(0,i.Ae)(),eN=(0,i.gX)(),ey=(0,i.h3)(),{optimisticallyUpdateAgent:eb,revertOptimisticUpdate:e_}=(0,i.XK)(),ew=function(){let e=(0,o.jE)();return(0,d.n)({mutationFn:async e=>{let s=(0,m.U)(),{data:{session:a}}=await s.auth.getSession();if(!a)throw Error("You must be logged in to install templates");let t=await fetch("".concat(u,"/templates/install"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a.access_token)},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json().catch(()=>({message:"Unknown error"}))).message||"HTTP ".concat(t.status,": ").concat(t.statusText));return t.json()},onSuccess:()=>{e.invalidateQueries({queryKey:["agents"]})}})}(),ek=x(),eC=function(){let e=(0,o.jE)();return(0,d.n)({mutationFn:async e=>{let{template_id:s,tags:a}=e,t=(0,m.U)(),{data:{session:l}}=await t.auth.getSession();if(!l)throw Error("You must be logged in to publish templates");let r=await fetch("".concat(u,"/templates/").concat(s,"/publish"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(l.access_token)},body:JSON.stringify({tags:a})});if(!r.ok)throw Error((await r.json().catch(()=>({message:"Unknown error"}))).message||"HTTP ".concat(r.status,": ").concat(r.statusText));return r.json()},onSuccess:()=>{e.invalidateQueries({queryKey:["secure-mcp","marketplace-templates"]}),e.invalidateQueries({queryKey:["secure-mcp","my-templates"]})}})}(),eA=(null==eo?void 0:eo.agents)||[],eS=null==eo?void 0:eo.pagination,{kortixTeamItems:eP,communityItems:eT}=(0,l.useMemo)(()=>{let e=[],s=[];ex&&ex.forEach(a=>{let t={id:a.template_id,name:a.name,description:a.description,tags:a.tags||[],download_count:a.download_count||0,creator_name:a.creator_name||"Anonymous",created_at:a.created_at,marketplace_published_at:a.marketplace_published_at,avatar:a.avatar,avatar_color:a.avatar_color,template_id:a.template_id,is_kortix_team:a.is_kortix_team,mcp_requirements:a.mcp_requirements,metadata:a.metadata};a.is_kortix_team?e.push(t):s.push(t)});let a=e=>e.sort((e,s)=>{switch(K){case"newest":return new Date(s.marketplace_published_at||s.created_at).getTime()-new Date(e.marketplace_published_at||e.created_at).getTime();case"popular":case"most_downloaded":return s.download_count-e.download_count;case"name":return e.name.localeCompare(s.name);default:return 0}});return{kortixTeamItems:a(e),communityItems:a(s)}},[ex,K]),eq=(0,l.useMemo)(()=>"kortix"===V?eP:"community"===V?eT:[...eP,...eT],[eP,eT,V]),eF=e=>{v(e)};(0,l.useEffect)(()=>{S(1)},[P,q,E,M]),(0,l.useEffect)(()=>{I(1)},[O,z,K]);let eE=async e=>{try{await eN.mutateAsync(e)}catch(e){console.error("Error deleting agent:",e)}},eR=async(e,s)=>{eb(e,{is_default:!s});try{await ev.mutateAsync({agentId:e,is_default:!s})}catch(s){e_(e),console.error("Error updating agent:",s)}},eM=async(e,s,a,t)=>{var l,n,i,c,o,d,m,u,x,h;Z(e.id);try{if(!s||""===s.trim())return void r.oR.error("Please provide a name for the agent");let c=((null==(l=e.mcp_requirements)?void 0:l.filter(e=>!e.custom_type))||[]).filter(e=>!a||!a[e.qualified_name]||""===a[e.qualified_name].trim());if(c.length>0){let e=c.map(e=>e.display_name).join(", ");r.oR.error("Please select credential profiles for: ".concat(e));return}let o=((null==(n=e.mcp_requirements)?void 0:n.filter(e=>e.custom_type&&"pipedream"!==e.custom_type))||[]).filter(e=>!t||!t[e.qualified_name]||e.required_config.some(s=>{var a;return!(null==(a=t[e.qualified_name][s])?void 0:a.trim())}));if(o.length>0){let e=o.map(e=>e.display_name).join(", ");r.oR.error("Please provide all required configuration for: ".concat(e));return}let d=((null==(i=e.mcp_requirements)?void 0:i.filter(e=>"pipedream"===e.custom_type))||[]).filter(e=>!t||!t[e.qualified_name]||!t[e.qualified_name].profile_id);if(d.length>0){let e=d.map(e=>e.display_name).join(", ");r.oR.error("Please select Pipedream profiles for: ".concat(e));return}let m=await ew.mutateAsync({template_id:e.template_id,instance_name:s,profile_mappings:a,custom_mcp_configs:t});if("installed"===m.status)r.oR.success('Agent "'.concat(s,'" installed successfully!')),H(!1),eF("my-agents");else if("configs_required"===m.status)return void r.oR.error("Please provide all required configurations");else return void r.oR.error("Unexpected response from server. Please try again.")}catch(e){console.error("Installation error:",e),(null==(c=e.message)?void 0:c.includes("already in your library"))?r.oR.error("This agent is already in your library"):(null==(o=e.message)?void 0:o.includes("Credential profile not found"))?r.oR.error("One or more selected credential profiles could not be found. Please refresh and try again."):(null==(d=e.message)?void 0:d.includes("Missing credential profile"))?r.oR.error("Please select credential profiles for all required services"):(null==(m=e.message)?void 0:m.includes("Invalid credential profile"))?r.oR.error("One or more selected credential profiles are invalid. Please select valid profiles."):(null==(u=e.message)?void 0:u.includes("inactive"))?r.oR.error("One or more selected credential profiles are inactive. Please select active profiles."):(null==(x=e.message)?void 0:x.includes("Template not found"))?r.oR.error("This agent template is no longer available"):(null==(h=e.message)?void 0:h.includes("Access denied"))?r.oR.error("You do not have permission to install this agent"):r.oR.error(e.message||"Failed to install agent. Please try again.")}finally{Z(null)}},eL=async(e,s)=>{try{ee(e),await ek.mutateAsync(e),r.oR.success("".concat(s," has been unpublished from the marketplace"))}catch(e){r.oR.error(e.message||"Failed to unpublish template")}finally{ee(null)}},e$=async()=>{if(es)try{ee(es.templateId);let e=et.split(",").map(e=>e.trim()).filter(e=>e.length>0);await eC.mutateAsync({template_id:es.templateId,tags:e.length>0?e:void 0}),r.oR.success("".concat(es.templateName," has been published to the marketplace")),ea(null),el("")}catch(e){r.oR.error(e.message||"Failed to publish template")}finally{ee(null)}};return f?(0,t.jsxs)("div",{className:"min-h-screen",children:[(0,t.jsx)("div",{className:"container max-w-7xl mx-auto px-4 py-8",children:(0,t.jsx)(ec,{})}),(0,t.jsx)("div",{className:"sticky top-0 z-50 bg-background/95 backdrop-blur-md border-b border-border/40 shadow-sm",children:(0,t.jsx)("div",{className:"container max-w-7xl mx-auto px-4 py-4",children:(0,t.jsx)(ep,{activeTab:j,onTabChange:eF})})}),(0,t.jsx)("div",{className:"container max-w-7xl mx-auto px-4 py-8",children:(0,t.jsx)(eV,{})})]}):e?(0,t.jsxs)("div",{className:"min-h-screen",children:[(0,t.jsx)("div",{className:"container mx-auto max-w-7xl px-4 py-8",children:(0,t.jsx)(ec,{})}),(0,t.jsxs)("div",{className:"sticky top-0 z-50 relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 backdrop-blur-md",style:{maskImage:"linear-gradient(to bottom, black 0%, black 60%, transparent 100%)",WebkitMaskImage:"linear-gradient(to bottom, black 0%, black 60%, transparent 100%)"}}),(0,t.jsx)("div",{className:"relative bg-gradient-to-b from-background/95 via-background/70 to-transparent",children:(0,t.jsx)("div",{className:"container mx-auto max-w-7xl px-4 py-4",children:(0,t.jsx)(ep,{activeTab:j,onTabChange:eF})})})]}),(0,t.jsxs)("div",{className:"container mx-auto max-w-7xl px-4 py-2",children:[(0,t.jsxs)("div",{className:"w-full",children:["my-agents"===j&&(0,t.jsx)(eY,{agentsSearchQuery:P,setAgentsSearchQuery:T,agentsLoading:ed,agents:eA,agentsPagination:eS,viewMode:k,onCreateAgent:()=>{ey.mutate()},onEditAgent:e=>{_(e),y(!0)},onDeleteAgent:eE,onToggleDefault:eR,onClearFilters:()=>{T(""),L({hasDefaultAgent:!1,hasMcpTools:!1,hasAgentpressTools:!1,selectedTools:[]}),S(1)},deleteAgentMutation:eN,setAgentsPage:S}),"marketplace"===j&&(0,t.jsx)(eB,{marketplaceSearchQuery:O,setMarketplaceSearchQuery:D,marketplaceFilter:V,setMarketplaceFilter:J,marketplaceLoading:eh,allMarketplaceItems:eq,kortixTeamItems:eP,communityItems:eT,installingItemId:Y,onInstallClick:(e,s)=>{s&&s.stopPropagation(),G(e),H(!0)},getItemStyling:e=>e.avatar&&e.avatar_color?{avatar:e.avatar,color:e.avatar_color}:(0,w.Z)(e.id)}),"my-templates"===j&&(0,t.jsx)(eW,{templatesError:ej,templatesLoading:ef,myTemplates:eg,templatesActioningId:X,onPublish:e=>{ea({templateId:e.template_id,templateName:e.name,currentTags:e.tags||[]}),el((e.tags||[]).join(", "))},onUnpublish:eL,onViewInMarketplace:()=>eF("marketplace"),onSwitchToMyAgents:()=>eF("my-agents"),getTemplateStyling:e=>e.avatar&&e.avatar_color?{avatar:e.avatar,color:e.avatar_color}:(0,w.Z)(e.template_id)})]}),(0,t.jsx)(eH,{publishDialog:es,publishTags:et,templatesActioningId:X,onClose:()=>ea(null),onPublishTagsChange:el,onPublish:e$}),(0,t.jsx)(er,{item:B,open:W,onOpenChange:H,onInstall:eM,isInstalling:Y===(null==B?void 0:B.id)})]})]}):null}},88271:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t}),a(95155),a(12115);let t=e=>{let s=["\uD83E\uDD16","\uD83C\uDFAF","⚡","\uD83D\uDE80","\uD83D\uDD2E","\uD83C\uDFA8","\uD83D\uDCCA","\uD83D\uDD27","\uD83D\uDCA1","\uD83C\uDF1F"],a=["#06b6d4","#22c55e","#8b5cf6","#3b82f6","#ec4899","#eab308","#ef4444","#6366f1"],t=e.split("").reduce((e,s)=>e+s.charCodeAt(0),0)%s.length,l=e.split("").reduce((e,s)=>e+s.charCodeAt(0),0)%a.length;return{avatar:s[t],color:a[l]}}},95030:(e,s,a)=>{"use strict";a.d(s,{Ak:()=>x,Gx:()=>u,M4:()=>h});var t=a(28755),l=a(26715),r=a(5041),n=a(56671),i=a(52643);let c="".concat("http://localhost:8000/api","/secure-mcp");async function o(e){let s=(0,i.U)(),{data:{session:a}}=await s.auth.getSession();if(!a)throw Error("You must be logged in to view credential profiles");let t=encodeURIComponent(e),l=await fetch("".concat(c,"/credential-profiles/").concat(t),{headers:{Authorization:"Bearer ".concat(a.access_token)}});if(!l.ok)throw Error((await l.json().catch(()=>({message:"Unknown error"}))).message||"HTTP ".concat(l.status,": ").concat(l.statusText));return l.json()}async function d(e){let s=(0,i.U)(),{data:{session:a}}=await s.auth.getSession();if(!a)throw Error("You must be logged in to create credential profiles");let t=await fetch("".concat(c,"/credential-profiles"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a.access_token)},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json().catch(()=>({message:"Unknown error"}))).message||"HTTP ".concat(t.status,": ").concat(t.statusText));return t.json()}async function m(e){let s=(0,i.U)(),{data:{session:a}}=await s.auth.getSession();if(!a)throw Error("You must be logged in to set default profile");let t=await fetch("".concat(c,"/credential-profiles/").concat(e,"/set-default"),{method:"PUT",headers:{Authorization:"Bearer ".concat(a.access_token)}});if(!t.ok)throw Error((await t.json().catch(()=>({message:"Unknown error"}))).message||"HTTP ".concat(t.status,": ").concat(t.statusText))}function u(e){return(0,t.I)({queryKey:["credential-profiles",e],queryFn:()=>e?o(e):Promise.resolve([]),enabled:!!e,staleTime:3e5})}function x(){let e=(0,l.jE)();return(0,r.n)({mutationFn:d,onSuccess:s=>{e.invalidateQueries({queryKey:["credential-profiles"]}),e.invalidateQueries({queryKey:["credential-profiles",s.mcp_qualified_name]}),n.oR.success("Created credential profile: ".concat(s.profile_name))},onError:e=>{n.oR.error("Failed to create credential profile: ".concat(e.message))}})}function h(){let e=(0,l.jE)();return(0,r.n)({mutationFn:m,onSuccess:()=>{e.invalidateQueries({queryKey:["credential-profiles"]}),n.oR.success("Default profile updated successfully")},onError:e=>{n.oR.error("Failed to set default profile: ".concat(e.message))}})}}},e=>{var s=s=>e(e.s=s);e.O(0,[2969,1935,6671,3860,1171,8341,7201,5061,6165,9001,2103,9855,2473,2566,7052,6686,4962,2880,8441,1684,7358],()=>s(17907)),_N_E=e.O()}]);