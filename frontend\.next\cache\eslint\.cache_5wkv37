[{"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\loading.tsx": "1", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\settings\\billing\\page.tsx": "2", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\settings\\layout.tsx": "3", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\settings\\page.tsx": "4", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\settings\\teams\\page.tsx": "5", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\settings\\usage-logs\\page.tsx": "6", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\page.tsx": "7", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\settings\\billing\\page.tsx": "8", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\settings\\layout.tsx": "9", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\settings\\members\\page.tsx": "10", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\settings\\page.tsx": "11", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\config\\[agentId]\\layout.tsx": "12", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\config\\[agentId]\\page.tsx": "13", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\config\\[agentId]\\workflow\\layout.tsx": "14", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\config\\[agentId]\\workflow\\[workflowId]\\page.tsx": "15", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\layout.tsx": "16", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\page.tsx": "17", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\[threadId]\\layout.tsx": "18", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\[threadId]\\page.tsx": "19", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\[threadId]\\redirect-page.tsx": "20", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\dashboard\\page.tsx": "21", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\layout.tsx": "22", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\model-pricing\\page.tsx": "23", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\[threadId]\\layout.tsx": "24", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\[threadId]\\page.tsx": "25", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\_components\\index.ts": "26", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\_components\\ThreadError.tsx": "27", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\_components\\ThreadLayout.tsx": "28", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\_components\\UpgradeDialog.tsx": "29", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\_hooks\\index.ts": "30", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\_hooks\\useBilling.ts": "31", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\_hooks\\useKeyboardShortcuts.ts": "32", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\_hooks\\useThreadData.ts": "33", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\_hooks\\useToolCalls.ts": "34", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\_types\\index.ts": "35", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\settings\\credentials\\layout.tsx": "36", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\settings\\credentials\\page.tsx": "37", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(home)\\layout.tsx": "38", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(home)\\page.tsx": "39", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\api\\integrations\\[provider]\\callback\\route.ts": "40", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\api\\share-page\\og-image\\route.tsx": "41", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\api\\triggers\\qstash\\webhook\\route.ts": "42", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\api\\webhooks\\trigger\\[workflowId]\\route.ts": "43", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\auth\\actions.ts": "44", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\auth\\callback\\route.ts": "45", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\auth\\github-popup\\page.tsx": "46", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\auth\\page.tsx": "47", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\auth\\reset-password\\page.tsx": "48", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\global-error.tsx": "49", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\invitation\\page.tsx": "50", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\layout.tsx": "51", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\legal\\page.tsx": "52", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\metadata.ts": "53", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\monitoring\\route.ts": "54", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\not-found.tsx": "55", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\opengraph-image.tsx": "56", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\providers.tsx": "57", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\share\\[threadId]\\layout.tsx": "58", "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\share\\[threadId]\\page.tsx": "59", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\agent-builder-chat.tsx": "60", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\agent-config-modal.tsx": "61", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\agent-mcp-configuration.tsx": "62", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\agent-preview.tsx": "63", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\agent-tools-configuration.tsx": "64", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\agents-grid.tsx": "65", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\agents-list.tsx": "66", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\AgentVersionManager.tsx": "67", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\create-agent-dialog.tsx": "68", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\custom-agents-page\\agent-card.tsx": "69", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\custom-agents-page\\header.tsx": "70", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\custom-agents-page\\index.ts": "71", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\custom-agents-page\\loading-skeleton.tsx": "72", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\custom-agents-page\\marketplace-section-header.tsx": "73", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\custom-agents-page\\marketplace-tab.tsx": "74", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\custom-agents-page\\my-agents-tab.tsx": "75", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\custom-agents-page\\my-templates-tab.tsx": "76", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\custom-agents-page\\publish-dialog.tsx": "77", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\custom-agents-page\\search-bar.tsx": "78", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\custom-agents-page\\tabs-navigation.tsx": "79", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\empty-state.tsx": "80", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\installation\\custom-server-step.tsx": "81", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\installation\\streamlined-install-dialog.tsx": "82", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\installation\\streamlined-profile-connector.tsx": "83", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\installation\\types.ts": "84", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\knowledge-base\\agent-knowledge-base-manager.tsx": "85", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\loading-state.tsx": "86", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\mcp\\configured-mcp-list.tsx": "87", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\mcp\\custom-mcp-dialog.tsx": "88", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\mcp\\mcp-configuration-new.tsx": "89", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\mcp\\mcp-server-card.tsx": "90", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\mcp\\tools-manager.tsx": "91", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\mcp\\types.ts": "92", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pagination.tsx": "93", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\agent-pipedream-tools-manager.tsx": "94", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\constants.ts": "95", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\credential-profile-manager.tsx": "96", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\credential-profile-selector.tsx": "97", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\pipedream-connect-button.tsx": "98", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\pipedream-connections-section.tsx": "99", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\pipedream-connector.tsx": "100", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\pipedream-registry.tsx": "101", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\pipedream-tool-selector.tsx": "102", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\pipedream-types.ts": "103", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\types.ts": "104", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\utils.ts": "105", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\_components\\AppCard.tsx": "106", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\_components\\AppsGrid.tsx": "107", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\_components\\CategorySidebar.tsx": "108", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\_components\\ConnectedAppsSection.tsx": "109", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\_components\\EmptyState.tsx": "110", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\_components\\index.ts": "111", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\_components\\PaginationControls.tsx": "112", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\_components\\PipedreamHeader.tsx": "113", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\results-info.tsx": "114", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\search-and-filters.tsx": "115", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\style-picker.tsx": "116", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\tools.ts": "117", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\triggers\\agent-triggers-configuration.tsx": "118", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\triggers\\configured-triggers-list.tsx": "119", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\triggers\\one-click-integrations.tsx": "120", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\triggers\\providers\\schedule-config.tsx": "121", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\triggers\\trigger-browse-dialog.tsx": "122", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\triggers\\trigger-config-dialog.tsx": "123", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\triggers\\types.ts": "124", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\triggers\\utils.tsx": "125", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\workflows\\agent-workflows-configuration.tsx": "126", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\workflows\\conditional-workflow-builder.tsx": "127", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\AuthProvider.tsx": "128", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\accept-team-invitation.tsx": "129", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\account-selector.tsx": "130", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\client-user-account-button.tsx": "131", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\create-team-dialog.tsx": "132", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\create-team-invitation-button.tsx": "133", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\delete-team-invitation-button.tsx": "134", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\delete-team-member-form.tsx": "135", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\edit-personal-account-name.tsx": "136", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\edit-team-member-role-form.tsx": "137", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\edit-team-name.tsx": "138", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\edit-team-slug.tsx": "139", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\manage-team-invitations.tsx": "140", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\manage-team-members.tsx": "141", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\manage-teams.tsx": "142", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\new-invitation-form.tsx": "143", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\new-team-form.tsx": "144", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\team-member-options.tsx": "145", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\user-account-button.tsx": "146", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\billing\\account-billing-status.tsx": "147", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\billing\\billing-modal.tsx": "148", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\billing\\payment-required-dialog.tsx": "149", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\billing\\usage-limit-alert.tsx": "150", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\billing\\usage-logs.tsx": "151", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\dashboard\\dashboard-content.tsx": "152", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\dashboard\\examples.tsx": "153", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\dashboard\\layout-content.tsx": "154", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\dashboard\\maintenance-banner.tsx": "155", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\dashboard\\maintenance-notice.tsx": "156", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\examples\\ErrorHandlingDemo.tsx": "157", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\file-renderers\\binary-renderer.tsx": "158", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\file-renderers\\code-renderer.tsx": "159", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\file-renderers\\csv-renderer.tsx": "160", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\file-renderers\\html-renderer.tsx": "161", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\file-renderers\\image-renderer.tsx": "162", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\file-renderers\\index.tsx": "163", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\file-renderers\\markdown-renderer.tsx": "164", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\file-renderers\\pdf-renderer.tsx": "165", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\GithubSignIn.tsx": "166", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\GoogleSignIn.tsx": "167", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\first-bento-animation.tsx": "168", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\fourth-bento-animation.tsx": "169", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\icons.tsx": "170", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\nav-menu.tsx": "171", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\second-bento-animation.tsx": "172", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\section-header.tsx": "173", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\bento-section.tsx": "174", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\company-showcase.tsx": "175", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\cta-section.tsx": "176", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\faq-section.tsx": "177", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\feature-section.tsx": "178", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\footer-section.tsx": "179", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\growth-section.tsx": "180", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\hero-section.tsx": "181", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\hero-video-section.tsx": "182", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\navbar.tsx": "183", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\open-source-section.tsx": "184", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\pricing-section.tsx": "185", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\quote-section.tsx": "186", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\testimonial-section.tsx": "187", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\use-cases-section.tsx": "188", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\testimonial-scroll.tsx": "189", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\theme-provider.tsx": "190", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\theme-toggle.tsx": "191", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\third-bento-animation.tsx": "192", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\ui\\accordion.tsx": "193", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\ui\\button.tsx": "194", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\ui\\feature-slideshow.tsx": "195", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\ui\\flickering-grid.tsx": "196", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\ui\\globe.tsx": "197", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\ui\\hero-video-dialog.tsx": "198", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\ui\\marquee.tsx": "199", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\ui\\orbiting-circle.tsx": "200", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\ui\\reasoning.tsx": "201", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\ui\\response-stream.tsx": "202", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\maintenance\\maintenance-page.tsx": "203", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\maintenance-alert.tsx": "204", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\page-header.tsx": "205", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\payment\\paywall-dialog.tsx": "206", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\sidebar\\cta.tsx": "207", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\sidebar\\date-picker.tsx": "208", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\sidebar\\kortix-enterprise-modal.tsx": "209", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\sidebar\\kortix-logo.tsx": "210", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\sidebar\\nav-agents.tsx": "211", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\sidebar\\nav-main.tsx": "212", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\sidebar\\nav-user-with-teams.tsx": "213", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\sidebar\\search-search.tsx": "214", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\sidebar\\share-modal.tsx": "215", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\sidebar\\sidebar-left.tsx": "216", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\theme-provider.tsx": "217", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\attachment-group.tsx": "218", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\chat-input\\agent-selector.tsx": "219", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\chat-input\\chat-dropdown.tsx": "220", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\chat-input\\chat-input.tsx": "221", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\chat-input\\custom-model-dialog.tsx": "222", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\chat-input\\file-upload-handler.tsx": "223", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\chat-input\\floating-tool-preview.tsx": "224", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\chat-input\\message-input.tsx": "225", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\chat-input\\model-selector.tsx": "226", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\chat-input\\uploaded-file-display.tsx": "227", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\chat-input\\voice-recorder.tsx": "228", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\chat-input\\_use-model-selection.ts": "229", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\content\\loader.tsx": "230", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\content\\PlaybackControls.tsx": "231", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\content\\ThreadContent.tsx": "232", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\content\\ThreadSkeleton.tsx": "233", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\DeleteConfirmationDialog.tsx": "234", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\file-attachment.tsx": "235", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\file-browser.tsx": "236", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\file-viewer-modal.tsx": "237", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\knowledge-base\\knowledge-base-manager.tsx": "238", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\preview-renderers\\csv-renderer.tsx": "239", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\preview-renderers\\html-renderer.tsx": "240", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\preview-renderers\\index.ts": "241", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\preview-renderers\\markdown-renderer.tsx": "242", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\thread-site-header.tsx": "243", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-call-side-panel.tsx": "244", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\ask-tool\\AskToolView.tsx": "245", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\ask-tool\\_utils.ts": "246", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\BrowserToolView.tsx": "247", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\command-tool\\CommandToolView.tsx": "248", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\command-tool\\TerminateCommandToolView.tsx": "249", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\command-tool\\_utils.ts": "250", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\CompleteToolView.tsx": "251", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\data-provider-tool\\DataProviderEndpointsToolView.tsx": "252", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\data-provider-tool\\ExecuteDataProviderCallToolView.tsx": "253", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\data-provider-tool\\_utils.ts": "254", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\DeployToolView.tsx": "255", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\expose-port-tool\\ExposePortToolView.tsx": "256", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\expose-port-tool\\_utils.ts": "257", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\file-operation\\FileOperationToolView.tsx": "258", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\file-operation\\_utils.ts": "259", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\GenericToolView.tsx": "260", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\mcp-content-renderer.tsx": "261", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\mcp-format-detector.ts": "262", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\mcp-tool\\McpToolView.tsx": "263", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\mcp-tool\\_utils.ts": "264", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\see-image-tool\\SeeImageToolView.tsx": "265", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\see-image-tool\\_utils.ts": "266", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\shared\\ImageLoader.tsx": "267", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\shared\\LoadingState.tsx": "268", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\str-replace\\StrReplaceToolView.tsx": "269", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\str-replace\\_utils.ts": "270", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\tool-result-parser.ts": "271", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\types.ts": "272", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\utils.ts": "273", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\web-scrape-tool\\WebScrapeToolView.tsx": "274", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\web-scrape-tool\\_utils.ts": "275", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\web-search-tool\\WebSearchToolView.tsx": "276", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\web-search-tool\\_utils.ts": "277", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\WebCrawlToolView.tsx": "278", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\wrapper\\index.ts": "279", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\wrapper\\ToolViewRegistry.tsx": "280", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\wrapper\\ToolViewWrapper.tsx": "281", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\xml-parser.ts": "282", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\types.ts": "283", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\utils.ts": "284", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\accordion.tsx": "285", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\alert-dialog.tsx": "286", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\alert.tsx": "287", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\animated-shiny-text.tsx": "288", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\avatar.tsx": "289", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\badge.tsx": "290", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\breadcrumb.tsx": "291", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\button.tsx": "292", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\calendar.tsx": "293", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\card.tsx": "294", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\checkbox.tsx": "295", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\code-block.tsx": "296", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\collapsible.tsx": "297", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\command.tsx": "298", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\data-table.tsx": "299", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\dialog.tsx": "300", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\drawer.tsx": "301", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\dropdown-menu.tsx": "302", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\editable.tsx": "303", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\fancy-tabs.tsx": "304", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\icons\\slack.tsx": "305", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\input.tsx": "306", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\label.tsx": "307", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\markdown.tsx": "308", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\page-header.tsx": "309", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\pixel-art-editor.tsx": "310", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\pixel-avatar.tsx": "311", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\popover.tsx": "312", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\portal.tsx": "313", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\progress.tsx": "314", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\radio-group.tsx": "315", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\ripple.tsx": "316", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\scroll-area.tsx": "317", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\select.tsx": "318", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\separator.tsx": "319", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\sheet.tsx": "320", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\sidebar.tsx": "321", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\skeleton.tsx": "322", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\slider.tsx": "323", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\sonner.tsx": "324", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\status-overlay.tsx": "325", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\submit-button.tsx": "326", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\switch.tsx": "327", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\table.tsx": "328", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\tabs.tsx": "329", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\textarea.tsx": "330", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\tooltip.tsx": "331", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\workflows\\CredentialProfileSelector.tsx": "332", "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\workflows\\MCPConfigurationDialog.tsx": "333", "C:\\Users\\<USER>\\suna\\frontend\\src\\contexts\\BillingContext.tsx": "334", "C:\\Users\\<USER>\\suna\\frontend\\src\\contexts\\DeleteOperationContext.tsx": "335", "C:\\Users\\<USER>\\suna\\frontend\\src\\flags.ts": "336", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\conditional-workflow-types.ts": "337", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\keys.ts": "338", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\use-agent-tools.ts": "339", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\use-agent-workflows.ts": "340", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\use-agents.ts": "341", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\use-custom-mcp-tools.ts": "342", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\use-pipedream-tools.ts": "343", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\useAgentVersions.ts": "344", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\utils.ts": "345", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\workflow-builder.ts": "346", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\workflow-keys.ts": "347", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\workflow-prompt-builder.ts": "348", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\workflow-utils.ts": "349", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\dashboard\\keys.ts": "350", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\dashboard\\use-initiate-agent.ts": "351", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\dashboard\\utils.ts": "352", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\files\\index.ts": "353", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\files\\keys.ts": "354", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\files\\use-file-content.ts": "355", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\files\\use-file-mutations.ts": "356", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\files\\use-file-queries.ts": "357", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\files\\use-image-content.ts": "358", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\files\\use-sandbox-mutations.ts": "359", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\index.ts": "360", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\knowledge-base\\keys.ts": "361", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\knowledge-base\\types.ts": "362", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\knowledge-base\\use-knowledge-base-queries.ts": "363", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\mcp\\use-credential-profiles.ts": "364", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\mcp\\use-mcp-servers.ts": "365", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\pipedream\\index.ts": "366", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\pipedream\\keys.ts": "367", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\pipedream\\mcp-discovery.ts": "368", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\pipedream\\use-pipedream-profiles.ts": "369", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\pipedream\\use-pipedream.ts": "370", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\pipedream\\utils.ts": "371", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\secure-mcp\\use-secure-mcp.ts": "372", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\sidebar\\keys.ts": "373", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\sidebar\\use-project-mutations.ts": "374", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\sidebar\\use-public-projects.ts": "375", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\sidebar\\use-sidebar.ts": "376", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\subscriptions\\keys.ts": "377", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\subscriptions\\use-billing.ts": "378", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\subscriptions\\use-model.ts": "379", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\subscriptions\\use-subscriptions.ts": "380", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\threads\\keys.ts": "381", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\threads\\use-agent-run.ts": "382", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\threads\\use-billing-status.ts": "383", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\threads\\use-messages.ts": "384", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\threads\\use-project.ts": "385", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\threads\\use-thread-mutations.ts": "386", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\threads\\use-thread-queries.ts": "387", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\threads\\use-threads.ts": "388", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\threads\\utils.ts": "389", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\transcription\\use-transcription.ts": "390", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\triggers\\index.ts": "391", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\triggers\\use-agent-triggers.ts": "392", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\triggers\\use-oauth-integrations.ts": "393", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\triggers\\use-trigger-providers.ts": "394", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\usage\\use-health.ts": "395", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\use-accounts.ts": "396", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\use-announcement-store.ts": "397", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\use-cached-file.ts": "398", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\use-file-content.ts": "399", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\use-image-content.ts": "400", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\use-media-query.ts": "401", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\use-mobile.ts": "402", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\use-modal-store.ts": "403", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\use-query.ts": "404", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\useAgentStream.ts": "405", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\useBillingError.ts": "406", "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\useVncPreloader.ts": "407", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\actions\\invitations.ts": "408", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\actions\\members.ts": "409", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\actions\\personal-account.ts": "410", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\actions\\teams.ts": "411", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\actions\\threads.ts": "412", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\api-client.ts": "413", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\api-enhanced.ts": "414", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\api-server.ts": "415", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\api.ts": "416", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\cache-init.ts": "417", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\config.ts": "418", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\edge-flags.ts": "419", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\error-handler.ts": "420", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\feature-flags.ts": "421", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\full-invitation-url.ts": "422", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\home.tsx": "423", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\site.ts": "424", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\supabase\\client.ts": "425", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\supabase\\handle-edge-error.ts": "426", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\supabase\\middleware.ts": "427", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\supabase\\server.ts": "428", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\utils\\dirty-string-parser.ts": "429", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\utils\\get-agent-style.ts": "430", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\utils\\tool-parser.ts": "431", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\utils\\unicode.ts": "432", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\utils\\url.ts": "433", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\utils\\_avatar-generator.ts": "434", "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\utils.ts": "435", "C:\\Users\\<USER>\\suna\\frontend\\src\\providers\\modal-providers.tsx": "436", "C:\\Users\\<USER>\\suna\\frontend\\src\\providers\\react-query-provider.tsx": "437"}, {"size": 328, "mtime": 1752252501307, "results": "438", "hashOfConfig": "439"}, {"size": 592, "mtime": 1752252501309, "results": "440", "hashOfConfig": "439"}, {"size": 1666, "mtime": 1752252501309, "results": "441", "hashOfConfig": "439"}, {"size": 462, "mtime": 1752252501309, "results": "442", "hashOfConfig": "439"}, {"size": 192, "mtime": 1752252501311, "results": "443", "hashOfConfig": "439"}, {"size": 438, "mtime": 1752252501313, "results": "444", "hashOfConfig": "439"}, {"size": 419, "mtime": 1752252501315, "results": "445", "hashOfConfig": "439"}, {"size": 2361, "mtime": 1752252501315, "results": "446", "hashOfConfig": "439"}, {"size": 1832, "mtime": 1752252501315, "results": "447", "hashOfConfig": "439"}, {"size": 3376, "mtime": 1752252501318, "results": "448", "hashOfConfig": "439"}, {"size": 2590, "mtime": 1752252501319, "results": "449", "hashOfConfig": "439"}, {"size": 410, "mtime": 1752252501324, "results": "450", "hashOfConfig": "439"}, {"size": 21290, "mtime": 1752252501325, "results": "451", "hashOfConfig": "439"}, {"size": 134, "mtime": 1752252501329, "results": "452", "hashOfConfig": "439"}, {"size": 14108, "mtime": 1752252501327, "results": "453", "hashOfConfig": "439"}, {"size": 651, "mtime": 1752252501331, "results": "454", "hashOfConfig": "439"}, {"size": 20454, "mtime": 1752252501331, "results": "455", "hashOfConfig": "439"}, {"size": 125, "mtime": 1752252501320, "results": "456", "hashOfConfig": "439"}, {"size": 366, "mtime": 1752252501322, "results": "457", "hashOfConfig": "439"}, {"size": 804, "mtime": 1752252501323, "results": "458", "hashOfConfig": "439"}, {"size": 1146, "mtime": 1752252501333, "results": "459", "hashOfConfig": "439"}, {"size": 694, "mtime": 1752252501333, "results": "460", "hashOfConfig": "439"}, {"size": 18525, "mtime": 1752252501335, "results": "461", "hashOfConfig": "439"}, {"size": 139, "mtime": 1752252501342, "results": "462", "hashOfConfig": "439"}, {"size": 23472, "mtime": 1752252501343, "results": "463", "hashOfConfig": "439"}, {"size": 143, "mtime": 1752252501348, "results": "464", "hashOfConfig": "439"}, {"size": 969, "mtime": 1752252501343, "results": "465", "hashOfConfig": "439"}, {"size": 4386, "mtime": 1752252501345, "results": "466", "hashOfConfig": "439"}, {"size": 3581, "mtime": 1752252501348, "results": "467", "hashOfConfig": "439"}, {"size": 205, "mtime": 1752252501349, "results": "468", "hashOfConfig": "439"}, {"size": 2629, "mtime": 1752252501350, "results": "469", "hashOfConfig": "439"}, {"size": 1671, "mtime": 1752252501351, "results": "470", "hashOfConfig": "439"}, {"size": 6896, "mtime": 1752252501352, "results": "471", "hashOfConfig": "439"}, {"size": 15488, "mtime": 1752252501354, "results": "472", "hashOfConfig": "439"}, {"size": 1042, "mtime": 1752252501356, "results": "473", "hashOfConfig": "439"}, {"size": 459, "mtime": 1752252501358, "results": "474", "hashOfConfig": "439"}, {"size": 1899, "mtime": 1752252501359, "results": "475", "hashOfConfig": "439"}, {"size": 467, "mtime": 1752252501359, "results": "476", "hashOfConfig": "439"}, {"size": 1629, "mtime": 1752252501360, "results": "477", "hashOfConfig": "439"}, {"size": 2667, "mtime": 1752252501363, "results": "478", "hashOfConfig": "439"}, {"size": 1525, "mtime": 1752252501364, "results": "479", "hashOfConfig": "439"}, {"size": 1354, "mtime": 1752252501367, "results": "480", "hashOfConfig": "439"}, {"size": 3109, "mtime": 1752252501368, "results": "481", "hashOfConfig": "439"}, {"size": 5142, "mtime": 1752252501368, "results": "482", "hashOfConfig": "439"}, {"size": 1101, "mtime": 1752252501368, "results": "483", "hashOfConfig": "439"}, {"size": 6177, "mtime": 1752252501370, "results": "484", "hashOfConfig": "439"}, {"size": 24366, "mtime": 1752252501382, "results": "485", "hashOfConfig": "439"}, {"size": 7870, "mtime": 1752252501385, "results": "486", "hashOfConfig": "439"}, {"size": 645, "mtime": 1752252501387, "results": "487", "hashOfConfig": "439"}, {"size": 639, "mtime": 1752252501390, "results": "488", "hashOfConfig": "439"}, {"size": 4866, "mtime": 1752252501390, "results": "489", "hashOfConfig": "439"}, {"size": 52986, "mtime": 1752252501391, "results": "490", "hashOfConfig": "439"}, {"size": 925, "mtime": 1752252501392, "results": "491", "hashOfConfig": "439"}, {"size": 1400, "mtime": 1752252501393, "results": "492", "hashOfConfig": "439"}, {"size": 5287, "mtime": 1752252501393, "results": "493", "hashOfConfig": "439"}, {"size": 1326, "mtime": 1752252501393, "results": "494", "hashOfConfig": "439"}, {"size": 2003, "mtime": 1752252501393, "results": "495", "hashOfConfig": "439"}, {"size": 2002, "mtime": 1752252501397, "results": "496", "hashOfConfig": "439"}, {"size": 28941, "mtime": 1752252501398, "results": "497", "hashOfConfig": "439"}, {"size": 16130, "mtime": 1752252501405, "results": "498", "hashOfConfig": "439"}, {"size": 11878, "mtime": 1752252501408, "results": "499", "hashOfConfig": "439"}, {"size": 1692, "mtime": 1752252501409, "results": "500", "hashOfConfig": "439"}, {"size": 14407, "mtime": 1752252501410, "results": "501", "hashOfConfig": "439"}, {"size": 3956, "mtime": 1752252501410, "results": "502", "hashOfConfig": "439"}, {"size": 12892, "mtime": 1752252501411, "results": "503", "hashOfConfig": "439"}, {"size": 4745, "mtime": 1752252501413, "results": "504", "hashOfConfig": "439"}, {"size": 8608, "mtime": 1752252501405, "results": "505", "hashOfConfig": "439"}, {"size": 13454, "mtime": 1752252501413, "results": "506", "hashOfConfig": "439"}, {"size": 10481, "mtime": 1752252501416, "results": "507", "hashOfConfig": "439"}, {"size": 458, "mtime": 1752252501416, "results": "508", "hashOfConfig": "439"}, {"size": 540, "mtime": 1752252501417, "results": "509", "hashOfConfig": "439"}, {"size": 917, "mtime": 1752252501418, "results": "510", "hashOfConfig": "439"}, {"size": 780, "mtime": 1752252501419, "results": "511", "hashOfConfig": "439"}, {"size": 5832, "mtime": 1752252501419, "results": "512", "hashOfConfig": "439"}, {"size": 2490, "mtime": 1752252501422, "results": "513", "hashOfConfig": "439"}, {"size": 3646, "mtime": 1752252501422, "results": "514", "hashOfConfig": "439"}, {"size": 2610, "mtime": 1752252501423, "results": "515", "hashOfConfig": "439"}, {"size": 835, "mtime": 1752252501423, "results": "516", "hashOfConfig": "439"}, {"size": 844, "mtime": 1752252501425, "results": "517", "hashOfConfig": "439"}, {"size": 1917, "mtime": 1752252501426, "results": "518", "hashOfConfig": "439"}, {"size": 2540, "mtime": 1752252501426, "results": "519", "hashOfConfig": "439"}, {"size": 13983, "mtime": 1752252501432, "results": "520", "hashOfConfig": "439"}, {"size": 12331, "mtime": 1752252501435, "results": "521", "hashOfConfig": "439"}, {"size": 1129, "mtime": 1752252501435, "results": "522", "hashOfConfig": "439"}, {"size": 48559, "mtime": 1752252501438, "results": "523", "hashOfConfig": "439"}, {"size": 1003, "mtime": 1752252501438, "results": "524", "hashOfConfig": "439"}, {"size": 4239, "mtime": 1752252501441, "results": "525", "hashOfConfig": "439"}, {"size": 18473, "mtime": 1752252501441, "results": "526", "hashOfConfig": "439"}, {"size": 8810, "mtime": 1752252501443, "results": "527", "hashOfConfig": "439"}, {"size": 1789, "mtime": 1752252501443, "results": "528", "hashOfConfig": "439"}, {"size": 11253, "mtime": 1752252501443, "results": "529", "hashOfConfig": "439"}, {"size": 413, "mtime": 1752252501445, "results": "530", "hashOfConfig": "439"}, {"size": 2442, "mtime": 1752252501447, "results": "531", "hashOfConfig": "439"}, {"size": 10870, "mtime": 1752252501457, "results": "532", "hashOfConfig": "439"}, {"size": 1143, "mtime": 1752252501457, "results": "533", "hashOfConfig": "439"}, {"size": 14508, "mtime": 1752252501458, "results": "534", "hashOfConfig": "439"}, {"size": 8044, "mtime": 1752252501458, "results": "535", "hashOfConfig": "439"}, {"size": 2232, "mtime": 1752252501458, "results": "536", "hashOfConfig": "439"}, {"size": 26758, "mtime": 1752252501458, "results": "537", "hashOfConfig": "439"}, {"size": 18642, "mtime": 1752252501460, "results": "538", "hashOfConfig": "439"}, {"size": 12620, "mtime": 1752252501461, "results": "539", "hashOfConfig": "439"}, {"size": 8876, "mtime": 1752252501462, "results": "540", "hashOfConfig": "439"}, {"size": 1148, "mtime": 1752252501462, "results": "541", "hashOfConfig": "439"}, {"size": 2950, "mtime": 1752252501464, "results": "542", "hashOfConfig": "439"}, {"size": 3696, "mtime": 1752252501465, "results": "543", "hashOfConfig": "439"}, {"size": 7824, "mtime": 1752252501449, "results": "544", "hashOfConfig": "439"}, {"size": 2732, "mtime": 1752252501450, "results": "545", "hashOfConfig": "439"}, {"size": 2693, "mtime": 1752252501451, "results": "546", "hashOfConfig": "439"}, {"size": 2231, "mtime": 1752252501451, "results": "547", "hashOfConfig": "439"}, {"size": 1352, "mtime": 1752252501452, "results": "548", "hashOfConfig": "439"}, {"size": 353, "mtime": 1752252501455, "results": "549", "hashOfConfig": "439"}, {"size": 1594, "mtime": 1752252501455, "results": "550", "hashOfConfig": "439"}, {"size": 1984, "mtime": 1752252501455, "results": "551", "hashOfConfig": "439"}, {"size": 822, "mtime": 1752252501466, "results": "552", "hashOfConfig": "439"}, {"size": 5901, "mtime": 1752252501467, "results": "553", "hashOfConfig": "439"}, {"size": 32840, "mtime": 1752252501468, "results": "554", "hashOfConfig": "439"}, {"size": 2276, "mtime": 1752252501468, "results": "555", "hashOfConfig": "439"}, {"size": 6430, "mtime": 1752252501471, "results": "556", "hashOfConfig": "439"}, {"size": 6224, "mtime": 1752252501473, "results": "557", "hashOfConfig": "439"}, {"size": 6036, "mtime": 1752252501473, "results": "558", "hashOfConfig": "439"}, {"size": 28942, "mtime": 1752252501475, "results": "559", "hashOfConfig": "439"}, {"size": 8864, "mtime": 1752252501475, "results": "560", "hashOfConfig": "439"}, {"size": 7646, "mtime": 1752252501475, "results": "561", "hashOfConfig": "439"}, {"size": 1706, "mtime": 1752252501477, "results": "562", "hashOfConfig": "439"}, {"size": 1104, "mtime": 1752252501477, "results": "563", "hashOfConfig": "439"}, {"size": 13328, "mtime": 1752252501480, "results": "564", "hashOfConfig": "439"}, {"size": 26947, "mtime": 1752252501482, "results": "565", "hashOfConfig": "439"}, {"size": 2279, "mtime": 1752252501399, "results": "566", "hashOfConfig": "439"}, {"size": 1387, "mtime": 1752252501483, "results": "567", "hashOfConfig": "439"}, {"size": 7582, "mtime": 1752252501483, "results": "568", "hashOfConfig": "439"}, {"size": 3467, "mtime": 1752252501484, "results": "569", "hashOfConfig": "439"}, {"size": 1047, "mtime": 1752252501486, "results": "570", "hashOfConfig": "439"}, {"size": 1256, "mtime": 1752252501489, "results": "571", "hashOfConfig": "439"}, {"size": 2483, "mtime": 1752252501490, "results": "572", "hashOfConfig": "439"}, {"size": 958, "mtime": 1752252501490, "results": "573", "hashOfConfig": "439"}, {"size": 1455, "mtime": 1752252501491, "results": "574", "hashOfConfig": "439"}, {"size": 2440, "mtime": 1752252501491, "results": "575", "hashOfConfig": "439"}, {"size": 1412, "mtime": 1752252501492, "results": "576", "hashOfConfig": "439"}, {"size": 1663, "mtime": 1752252501492, "results": "577", "hashOfConfig": "439"}, {"size": 2770, "mtime": 1752252501492, "results": "578", "hashOfConfig": "439"}, {"size": 2433, "mtime": 1752252501493, "results": "579", "hashOfConfig": "439"}, {"size": 2700, "mtime": 1752252501493, "results": "580", "hashOfConfig": "439"}, {"size": 2824, "mtime": 1752252501494, "results": "581", "hashOfConfig": "439"}, {"size": 4452, "mtime": 1752252501494, "results": "582", "hashOfConfig": "439"}, {"size": 3725, "mtime": 1752252501496, "results": "583", "hashOfConfig": "439"}, {"size": 2155, "mtime": 1752252501498, "results": "584", "hashOfConfig": "439"}, {"size": 7253, "mtime": 1752252501498, "results": "585", "hashOfConfig": "439"}, {"size": 5913, "mtime": 1752252501499, "results": "586", "hashOfConfig": "439"}, {"size": 2540, "mtime": 1752252501501, "results": "587", "hashOfConfig": "439"}, {"size": 2302, "mtime": 1752252501501, "results": "588", "hashOfConfig": "439"}, {"size": 11666, "mtime": 1752252501502, "results": "589", "hashOfConfig": "439"}, {"size": 9303, "mtime": 1752252501502, "results": "590", "hashOfConfig": "439"}, {"size": 9568, "mtime": 1752252501505, "results": "591", "hashOfConfig": "439"}, {"size": 4488, "mtime": 1752252501509, "results": "592", "hashOfConfig": "439"}, {"size": 6645, "mtime": 1752252501509, "results": "593", "hashOfConfig": "439"}, {"size": 3223, "mtime": 1752252501510, "results": "594", "hashOfConfig": "439"}, {"size": 1, "mtime": 1752252501510, "results": "595", "hashOfConfig": "439"}, {"size": 2470, "mtime": 1752252501514, "results": "596", "hashOfConfig": "439"}, {"size": 2513, "mtime": 1752252501514, "results": "597", "hashOfConfig": "439"}, {"size": 17117, "mtime": 1752252501515, "results": "598", "hashOfConfig": "439"}, {"size": 2631, "mtime": 1752252501516, "results": "599", "hashOfConfig": "439"}, {"size": 9044, "mtime": 1752252501516, "results": "600", "hashOfConfig": "439"}, {"size": 5723, "mtime": 1752252501517, "results": "601", "hashOfConfig": "439"}, {"size": 4972, "mtime": 1752252501521, "results": "602", "hashOfConfig": "439"}, {"size": 3320, "mtime": 1752252501521, "results": "603", "hashOfConfig": "439"}, {"size": 5389, "mtime": 1752252501399, "results": "604", "hashOfConfig": "439"}, {"size": 6893, "mtime": 1752252501402, "results": "605", "hashOfConfig": "439"}, {"size": 5187, "mtime": 1752252501525, "results": "606", "hashOfConfig": "439"}, {"size": 8779, "mtime": 1752252501525, "results": "607", "hashOfConfig": "439"}, {"size": 144806, "mtime": 1752252501527, "results": "608", "hashOfConfig": "439"}, {"size": 4541, "mtime": 1752252501532, "results": "609", "hashOfConfig": "439"}, {"size": 1833, "mtime": 1752252501533, "results": "610", "hashOfConfig": "439"}, {"size": 350, "mtime": 1752252501534, "results": "611", "hashOfConfig": "439"}, {"size": 1858, "mtime": 1752252501534, "results": "612", "hashOfConfig": "439"}, {"size": 1791, "mtime": 1752252501535, "results": "613", "hashOfConfig": "439"}, {"size": 1412, "mtime": 1752252501540, "results": "614", "hashOfConfig": "439"}, {"size": 1806, "mtime": 1752252501541, "results": "615", "hashOfConfig": "439"}, {"size": 1042, "mtime": 1752252501541, "results": "616", "hashOfConfig": "439"}, {"size": 7047, "mtime": 1752252501543, "results": "617", "hashOfConfig": "439"}, {"size": 1866, "mtime": 1752252501543, "results": "618", "hashOfConfig": "439"}, {"size": 16784, "mtime": 1752252501544, "results": "619", "hashOfConfig": "439"}, {"size": 1499, "mtime": 1752252501545, "results": "620", "hashOfConfig": "439"}, {"size": 10859, "mtime": 1752252501546, "results": "621", "hashOfConfig": "439"}, {"size": 9235, "mtime": 1752252501548, "results": "622", "hashOfConfig": "439"}, {"size": 26744, "mtime": 1752252501548, "results": "623", "hashOfConfig": "439"}, {"size": 1194, "mtime": 1752252501549, "results": "624", "hashOfConfig": "439"}, {"size": 937, "mtime": 1752252501550, "results": "625", "hashOfConfig": "439"}, {"size": 4439, "mtime": 1752252501550, "results": "626", "hashOfConfig": "439"}, {"size": 2862, "mtime": 1752252501552, "results": "627", "hashOfConfig": "439"}, {"size": 310, "mtime": 1752252501552, "results": "628", "hashOfConfig": "439"}, {"size": 806, "mtime": 1752252501564, "results": "629", "hashOfConfig": "439"}, {"size": 9144, "mtime": 1752252501565, "results": "630", "hashOfConfig": "439"}, {"size": 2130, "mtime": 1752252501566, "results": "631", "hashOfConfig": "439"}, {"size": 2104, "mtime": 1752252501567, "results": "632", "hashOfConfig": "439"}, {"size": 15129, "mtime": 1752252501568, "results": "633", "hashOfConfig": "439"}, {"size": 10666, "mtime": 1752252501569, "results": "634", "hashOfConfig": "439"}, {"size": 4696, "mtime": 1752252501569, "results": "635", "hashOfConfig": "439"}, {"size": 5418, "mtime": 1752252501571, "results": "636", "hashOfConfig": "439"}, {"size": 1745, "mtime": 1752252501573, "results": "637", "hashOfConfig": "439"}, {"size": 3951, "mtime": 1752252501573, "results": "638", "hashOfConfig": "439"}, {"size": 4626, "mtime": 1752252501574, "results": "639", "hashOfConfig": "439"}, {"size": 11108, "mtime": 1752252501575, "results": "640", "hashOfConfig": "439"}, {"size": 5265, "mtime": 1752252501576, "results": "641", "hashOfConfig": "439"}, {"size": 4500, "mtime": 1752252501575, "results": "642", "hashOfConfig": "439"}, {"size": 0, "mtime": 1752252501576, "results": "643", "hashOfConfig": "439"}, {"size": 4199, "mtime": 1752252501577, "results": "644", "hashOfConfig": "439"}, {"size": 1499, "mtime": 1752252501578, "results": "645", "hashOfConfig": "439"}, {"size": 478, "mtime": 1752252501580, "results": "646", "hashOfConfig": "439"}, {"size": 7170, "mtime": 1752252501581, "results": "647", "hashOfConfig": "439"}, {"size": 779, "mtime": 1752252501581, "results": "648", "hashOfConfig": "439"}, {"size": 21245, "mtime": 1752252501583, "results": "649", "hashOfConfig": "439"}, {"size": 718, "mtime": 1752252501584, "results": "650", "hashOfConfig": "439"}, {"size": 11736, "mtime": 1752252501585, "results": "651", "hashOfConfig": "439"}, {"size": 9596, "mtime": 1752252501585, "results": "652", "hashOfConfig": "439"}, {"size": 9568, "mtime": 1752252501586, "results": "653", "hashOfConfig": "439"}, {"size": 7081, "mtime": 1752252501588, "results": "654", "hashOfConfig": "439"}, {"size": 307, "mtime": 1752252501589, "results": "655", "hashOfConfig": "439"}, {"size": 24402, "mtime": 1752252501589, "results": "656", "hashOfConfig": "439"}, {"size": 12692, "mtime": 1752252501592, "results": "657", "hashOfConfig": "439"}, {"size": 4624, "mtime": 1752252501593, "results": "658", "hashOfConfig": "439"}, {"size": 18911, "mtime": 1752252501593, "results": "659", "hashOfConfig": "439"}, {"size": 4387, "mtime": 1752252501596, "results": "660", "hashOfConfig": "439"}, {"size": 9229, "mtime": 1752252501597, "results": "661", "hashOfConfig": "439"}, {"size": 6269, "mtime": 1752252501597, "results": "662", "hashOfConfig": "439"}, {"size": 9386, "mtime": 1752252501599, "results": "663", "hashOfConfig": "439"}, {"size": 30340, "mtime": 1752252501599, "results": "664", "hashOfConfig": "439"}, {"size": 2585, "mtime": 1752252501600, "results": "665", "hashOfConfig": "439"}, {"size": 6660, "mtime": 1752252501601, "results": "666", "hashOfConfig": "439"}, {"size": 13464, "mtime": 1752252501592, "results": "667", "hashOfConfig": "439"}, {"size": 2215, "mtime": 1752252501608, "results": "668", "hashOfConfig": "439"}, {"size": 19030, "mtime": 1752252501603, "results": "669", "hashOfConfig": "439"}, {"size": 70198, "mtime": 1752252501606, "results": "670", "hashOfConfig": "439"}, {"size": 6730, "mtime": 1752252501607, "results": "671", "hashOfConfig": "439"}, {"size": 2073, "mtime": 1752252501589, "results": "672", "hashOfConfig": "439"}, {"size": 22673, "mtime": 1752252501608, "results": "673", "hashOfConfig": "439"}, {"size": 7901, "mtime": 1752252501608, "results": "674", "hashOfConfig": "439"}, {"size": 59672, "mtime": 1752252501610, "results": "675", "hashOfConfig": "439"}, {"size": 20772, "mtime": 1752252501610, "results": "676", "hashOfConfig": "439"}, {"size": 3016, "mtime": 1752252501611, "results": "677", "hashOfConfig": "439"}, {"size": 3510, "mtime": 1752252501614, "results": "678", "hashOfConfig": "439"}, {"size": 104, "mtime": 1752252501615, "results": "679", "hashOfConfig": "439"}, {"size": 927, "mtime": 1752252501616, "results": "680", "hashOfConfig": "439"}, {"size": 10062, "mtime": 1752252501617, "results": "681", "hashOfConfig": "439"}, {"size": 30460, "mtime": 1752252501618, "results": "682", "hashOfConfig": "439"}, {"size": 9124, "mtime": 1752252501626, "results": "683", "hashOfConfig": "439"}, {"size": 7431, "mtime": 1752252501631, "results": "684", "hashOfConfig": "439"}, {"size": 17346, "mtime": 1752252501620, "results": "685", "hashOfConfig": "439"}, {"size": 9929, "mtime": 1752252501632, "results": "686", "hashOfConfig": "439"}, {"size": 13558, "mtime": 1752252501633, "results": "687", "hashOfConfig": "439"}, {"size": 7474, "mtime": 1752252501634, "results": "688", "hashOfConfig": "439"}, {"size": 13506, "mtime": 1752252501622, "results": "689", "hashOfConfig": "439"}, {"size": 11887, "mtime": 1752252501635, "results": "690", "hashOfConfig": "439"}, {"size": 11571, "mtime": 1752252501636, "results": "691", "hashOfConfig": "439"}, {"size": 12211, "mtime": 1752252501638, "results": "692", "hashOfConfig": "439"}, {"size": 14849, "mtime": 1752252501623, "results": "693", "hashOfConfig": "439"}, {"size": 7355, "mtime": 1752252501640, "results": "694", "hashOfConfig": "439"}, {"size": 7791, "mtime": 1752252501641, "results": "695", "hashOfConfig": "439"}, {"size": 14889, "mtime": 1752252501642, "results": "696", "hashOfConfig": "439"}, {"size": 6087, "mtime": 1752252501644, "results": "697", "hashOfConfig": "439"}, {"size": 9429, "mtime": 1752252501624, "results": "698", "hashOfConfig": "439"}, {"size": 14401, "mtime": 1752252501644, "results": "699", "hashOfConfig": "439"}, {"size": 9048, "mtime": 1752252501647, "results": "700", "hashOfConfig": "439"}, {"size": 13121, "mtime": 1752252501648, "results": "701", "hashOfConfig": "439"}, {"size": 9908, "mtime": 1752252501649, "results": "702", "hashOfConfig": "439"}, {"size": 15065, "mtime": 1752252501650, "results": "703", "hashOfConfig": "439"}, {"size": 13540, "mtime": 1752252501650, "results": "704", "hashOfConfig": "439"}, {"size": 589, "mtime": 1752252501652, "results": "705", "hashOfConfig": "439"}, {"size": 2977, "mtime": 1752252501652, "results": "706", "hashOfConfig": "439"}, {"size": 16639, "mtime": 1752252501656, "results": "707", "hashOfConfig": "439"}, {"size": 7108, "mtime": 1752252501656, "results": "708", "hashOfConfig": "439"}, {"size": 5183, "mtime": 1752252501657, "results": "709", "hashOfConfig": "439"}, {"size": 508, "mtime": 1752252501658, "results": "710", "hashOfConfig": "439"}, {"size": 54257, "mtime": 1752252501659, "results": "711", "hashOfConfig": "439"}, {"size": 14551, "mtime": 1752252501660, "results": "712", "hashOfConfig": "439"}, {"size": 9300, "mtime": 1752252501660, "results": "713", "hashOfConfig": "439"}, {"size": 16053, "mtime": 1752252501664, "results": "714", "hashOfConfig": "439"}, {"size": 7222, "mtime": 1752252501664, "results": "715", "hashOfConfig": "439"}, {"size": 15115, "mtime": 1752252501624, "results": "716", "hashOfConfig": "439"}, {"size": 73, "mtime": 1752252501667, "results": "717", "hashOfConfig": "439"}, {"size": 4385, "mtime": 1752252501666, "results": "718", "hashOfConfig": "439"}, {"size": 3398, "mtime": 1752252501666, "results": "719", "hashOfConfig": "439"}, {"size": 4423, "mtime": 1752252501667, "results": "720", "hashOfConfig": "439"}, {"size": 2372, "mtime": 1752252501668, "results": "721", "hashOfConfig": "439"}, {"size": 14901, "mtime": 1752252501668, "results": "722", "hashOfConfig": "439"}, {"size": 2119, "mtime": 1752252501669, "results": "723", "hashOfConfig": "439"}, {"size": 4042, "mtime": 1752252501670, "results": "724", "hashOfConfig": "439"}, {"size": 1691, "mtime": 1752252501672, "results": "725", "hashOfConfig": "439"}, {"size": 985, "mtime": 1752252501673, "results": "726", "hashOfConfig": "439"}, {"size": 1160, "mtime": 1752252501673, "results": "727", "hashOfConfig": "439"}, {"size": 1993, "mtime": 1752252501674, "results": "728", "hashOfConfig": "439"}, {"size": 2481, "mtime": 1752252501675, "results": "729", "hashOfConfig": "439"}, {"size": 2378, "mtime": 1752252501676, "results": "730", "hashOfConfig": "439"}, {"size": 3006, "mtime": 1752252501676, "results": "731", "hashOfConfig": "439"}, {"size": 2094, "mtime": 1752252501677, "results": "732", "hashOfConfig": "439"}, {"size": 1266, "mtime": 1752252501677, "results": "733", "hashOfConfig": "439"}, {"size": 2885, "mtime": 1752252501677, "results": "734", "hashOfConfig": "439"}, {"size": 839, "mtime": 1752252501679, "results": "735", "hashOfConfig": "439"}, {"size": 4857, "mtime": 1752252501679, "results": "736", "hashOfConfig": "439"}, {"size": 2457, "mtime": 1752252501681, "results": "737", "hashOfConfig": "439"}, {"size": 3973, "mtime": 1752252501682, "results": "738", "hashOfConfig": "439"}, {"size": 4204, "mtime": 1752252501682, "results": "739", "hashOfConfig": "439"}, {"size": 8574, "mtime": 1752252501682, "results": "740", "hashOfConfig": "439"}, {"size": 2870, "mtime": 1752252501682, "results": "741", "hashOfConfig": "439"}, {"size": 3144, "mtime": 1752252501684, "results": "742", "hashOfConfig": "439"}, {"size": 1449, "mtime": 1752252501685, "results": "743", "hashOfConfig": "439"}, {"size": 993, "mtime": 1752252501686, "results": "744", "hashOfConfig": "439"}, {"size": 642, "mtime": 1752252501686, "results": "745", "hashOfConfig": "439"}, {"size": 5493, "mtime": 1752252501687, "results": "746", "hashOfConfig": "439"}, {"size": 909, "mtime": 1752252501689, "results": "747", "hashOfConfig": "439"}, {"size": 9659, "mtime": 1752252501690, "results": "748", "hashOfConfig": "439"}, {"size": 12112, "mtime": 1752252501691, "results": "749", "hashOfConfig": "439"}, {"size": 1693, "mtime": 1752252501691, "results": "750", "hashOfConfig": "439"}, {"size": 434, "mtime": 1752252501691, "results": "751", "hashOfConfig": "439"}, {"size": 771, "mtime": 1752252501692, "results": "752", "hashOfConfig": "439"}, {"size": 1520, "mtime": 1752252501692, "results": "753", "hashOfConfig": "439"}, {"size": 1641, "mtime": 1752252501692, "results": "754", "hashOfConfig": "439"}, {"size": 1711, "mtime": 1752252501693, "results": "755", "hashOfConfig": "439"}, {"size": 6461, "mtime": 1752252501693, "results": "756", "hashOfConfig": "439"}, {"size": 739, "mtime": 1752252501694, "results": "757", "hashOfConfig": "439"}, {"size": 4248, "mtime": 1752252501694, "results": "758", "hashOfConfig": "439"}, {"size": 22483, "mtime": 1752252501695, "results": "759", "hashOfConfig": "439"}, {"size": 1714, "mtime": 1752252501697, "results": "760", "hashOfConfig": "439"}, {"size": 2075, "mtime": 1752252501697, "results": "761", "hashOfConfig": "439"}, {"size": 634, "mtime": 1752252501700, "results": "762", "hashOfConfig": "439"}, {"size": 1109, "mtime": 1752252501700, "results": "763", "hashOfConfig": "439"}, {"size": 1472, "mtime": 1752252501700, "results": "764", "hashOfConfig": "439"}, {"size": 1208, "mtime": 1752252501701, "results": "765", "hashOfConfig": "439"}, {"size": 2580, "mtime": 1752252501701, "results": "766", "hashOfConfig": "439"}, {"size": 2046, "mtime": 1752252501701, "results": "767", "hashOfConfig": "439"}, {"size": 782, "mtime": 1752252501702, "results": "768", "hashOfConfig": "439"}, {"size": 1962, "mtime": 1752252501702, "results": "769", "hashOfConfig": "439"}, {"size": 14950, "mtime": 1752252501705, "results": "770", "hashOfConfig": "439"}, {"size": 19694, "mtime": 1752252501705, "results": "771", "hashOfConfig": "439"}, {"size": 2464, "mtime": 1752252501707, "results": "772", "hashOfConfig": "439"}, {"size": 5684, "mtime": 1752252501709, "results": "773", "hashOfConfig": "439"}, {"size": 265, "mtime": 1752252501709, "results": "774", "hashOfConfig": "439"}, {"size": 9234, "mtime": 1752252501709, "results": "775", "hashOfConfig": "439"}, {"size": 707, "mtime": 1752252501709, "results": "776", "hashOfConfig": "439"}, {"size": 5116, "mtime": 1752252501712, "results": "777", "hashOfConfig": "439"}, {"size": 3094, "mtime": 1752252501714, "results": "778", "hashOfConfig": "439"}, {"size": 6521, "mtime": 1752252501714, "results": "779", "hashOfConfig": "439"}, {"size": 2248, "mtime": 1752252501714, "results": "780", "hashOfConfig": "439"}, {"size": 2768, "mtime": 1752252501714, "results": "781", "hashOfConfig": "439"}, {"size": 2015, "mtime": 1752252501716, "results": "782", "hashOfConfig": "439"}, {"size": 19768, "mtime": 1752252501716, "results": "783", "hashOfConfig": "439"}, {"size": 11391, "mtime": 1752252501717, "results": "784", "hashOfConfig": "439"}, {"size": 381, "mtime": 1752252501717, "results": "785", "hashOfConfig": "439"}, {"size": 11072, "mtime": 1752252501718, "results": "786", "hashOfConfig": "439"}, {"size": 11314, "mtime": 1752252501719, "results": "787", "hashOfConfig": "439"}, {"size": 355, "mtime": 1752252501719, "results": "788", "hashOfConfig": "439"}, {"size": 1822, "mtime": 1752252501734, "results": "789", "hashOfConfig": "439"}, {"size": 95, "mtime": 1752252501736, "results": "790", "hashOfConfig": "439"}, {"size": 573, "mtime": 1752252501738, "results": "791", "hashOfConfig": "439"}, {"size": 568, "mtime": 1752252501740, "results": "792", "hashOfConfig": "439"}, {"size": 584, "mtime": 1752252501742, "results": "793", "hashOfConfig": "439"}, {"size": 7887, "mtime": 1752252501747, "results": "794", "hashOfConfig": "439"}, {"size": 12811, "mtime": 1752252501750, "results": "795", "hashOfConfig": "439"}, {"size": 1508, "mtime": 1752252501752, "results": "796", "hashOfConfig": "439"}, {"size": 1033, "mtime": 1752252501756, "results": "797", "hashOfConfig": "439"}, {"size": 900, "mtime": 1752252501758, "results": "798", "hashOfConfig": "439"}, {"size": 915, "mtime": 1752252501761, "results": "799", "hashOfConfig": "439"}, {"size": 1747, "mtime": 1752252501761, "results": "800", "hashOfConfig": "439"}, {"size": 13366, "mtime": 1752252501763, "results": "801", "hashOfConfig": "439"}, {"size": 8038, "mtime": 1752252501765, "results": "802", "hashOfConfig": "439"}, {"size": 4241, "mtime": 1752252501766, "results": "803", "hashOfConfig": "439"}, {"size": 161, "mtime": 1752252501767, "results": "804", "hashOfConfig": "439"}, {"size": 1583, "mtime": 1752252501769, "results": "805", "hashOfConfig": "439"}, {"size": 2650, "mtime": 1752252501772, "results": "806", "hashOfConfig": "439"}, {"size": 4738, "mtime": 1752252501772, "results": "807", "hashOfConfig": "439"}, {"size": 3415, "mtime": 1752252501775, "results": "808", "hashOfConfig": "439"}, {"size": 10158, "mtime": 1752252501775, "results": "809", "hashOfConfig": "439"}, {"size": 13430, "mtime": 1752252501778, "results": "810", "hashOfConfig": "439"}, {"size": 648, "mtime": 1752252501780, "results": "811", "hashOfConfig": "439"}, {"size": 1285, "mtime": 1752252501780, "results": "812", "hashOfConfig": "439"}, {"size": 336, "mtime": 1752252501782, "results": "813", "hashOfConfig": "439"}, {"size": 3794, "mtime": 1752252501782, "results": "814", "hashOfConfig": "439"}, {"size": 659, "mtime": 1752252501783, "results": "815", "hashOfConfig": "439"}, {"size": 1378, "mtime": 1752252501785, "results": "816", "hashOfConfig": "439"}, {"size": 617, "mtime": 1752252501785, "results": "817", "hashOfConfig": "439"}, {"size": 898, "mtime": 1752252501785, "results": "818", "hashOfConfig": "439"}, {"size": 612, "mtime": 1752252501789, "results": "819", "hashOfConfig": "439"}, {"size": 1044, "mtime": 1752252501790, "results": "820", "hashOfConfig": "439"}, {"size": 681, "mtime": 1752252501790, "results": "821", "hashOfConfig": "439"}, {"size": 610, "mtime": 1752252501792, "results": "822", "hashOfConfig": "439"}, {"size": 919, "mtime": 1752252501794, "results": "823", "hashOfConfig": "439"}, {"size": 755, "mtime": 1752252501794, "results": "824", "hashOfConfig": "439"}, {"size": 635, "mtime": 1752252501796, "results": "825", "hashOfConfig": "439"}, {"size": 1300, "mtime": 1752252501798, "results": "826", "hashOfConfig": "439"}, {"size": 12402, "mtime": 1752252501799, "results": "827", "hashOfConfig": "439"}, {"size": 334, "mtime": 1752252501801, "results": "828", "hashOfConfig": "439"}, {"size": 210, "mtime": 1752252501801, "results": "829", "hashOfConfig": "439"}, {"size": 5609, "mtime": 1752252501801, "results": "830", "hashOfConfig": "439"}, {"size": 5184, "mtime": 1752252501803, "results": "831", "hashOfConfig": "439"}, {"size": 1011, "mtime": 1752252501804, "results": "832", "hashOfConfig": "439"}, {"size": 370, "mtime": 1752252501806, "results": "833", "hashOfConfig": "439"}, {"size": 566, "mtime": 1752252501806, "results": "834", "hashOfConfig": "439"}, {"size": 4703, "mtime": 1752252501806, "results": "835", "hashOfConfig": "439"}, {"size": 27437, "mtime": 1752252501808, "results": "836", "hashOfConfig": "439"}, {"size": 1664, "mtime": 1752252501808, "results": "837", "hashOfConfig": "439"}, {"size": 5421, "mtime": 1752252501808, "results": "838", "hashOfConfig": "439"}, {"size": 541, "mtime": 1752252501810, "results": "839", "hashOfConfig": "439"}, {"size": 606, "mtime": 1752252501810, "results": "840", "hashOfConfig": "439"}, {"size": 437, "mtime": 1752252501810, "results": "841", "hashOfConfig": "439"}, {"size": 2291, "mtime": 1752252501812, "results": "842", "hashOfConfig": "439"}, {"size": 23295, "mtime": 1752252501814, "results": "843", "hashOfConfig": "439"}, {"size": 2058, "mtime": 1752252501816, "results": "844", "hashOfConfig": "439"}, {"size": 5176, "mtime": 1752252501816, "results": "845", "hashOfConfig": "439"}, {"size": 1755, "mtime": 1752252501818, "results": "846", "hashOfConfig": "439"}, {"size": 1374, "mtime": 1752252501819, "results": "847", "hashOfConfig": "439"}, {"size": 501, "mtime": 1752252501820, "results": "848", "hashOfConfig": "439"}, {"size": 1403, "mtime": 1752252501822, "results": "849", "hashOfConfig": "439"}, {"size": 2020, "mtime": 1752252501823, "results": "850", "hashOfConfig": "439"}, {"size": 6866, "mtime": 1752252501824, "results": "851", "hashOfConfig": "439"}, {"size": 13648, "mtime": 1752252501824, "results": "852", "hashOfConfig": "439"}, {"size": 3382, "mtime": 1752252501825, "results": "853", "hashOfConfig": "439"}, {"size": 59214, "mtime": 1752252501826, "results": "854", "hashOfConfig": "439"}, {"size": 4617, "mtime": 1752252501826, "results": "855", "hashOfConfig": "439"}, {"size": 6146, "mtime": 1752252501827, "results": "856", "hashOfConfig": "439"}, {"size": 1522, "mtime": 1752252501828, "results": "857", "hashOfConfig": "439"}, {"size": 5189, "mtime": 1752252501830, "results": "858", "hashOfConfig": "439"}, {"size": 9899, "mtime": 1752252501831, "results": "859", "hashOfConfig": "439"}, {"size": 133, "mtime": 1752252501832, "results": "860", "hashOfConfig": "439"}, {"size": 100879, "mtime": 1752252501833, "results": "861", "hashOfConfig": "439"}, {"size": 324, "mtime": 1752252501833, "results": "862", "hashOfConfig": "439"}, {"size": 689, "mtime": 1752252501834, "results": "863", "hashOfConfig": "439"}, {"size": 644, "mtime": 1752252501834, "results": "864", "hashOfConfig": "439"}, {"size": 3115, "mtime": 1752252501835, "results": "865", "hashOfConfig": "439"}, {"size": 1268, "mtime": 1752252501835, "results": "866", "hashOfConfig": "439"}, {"size": 8516, "mtime": 1752252501838, "results": "867", "hashOfConfig": "439"}, {"size": 1052, "mtime": 1752252501839, "results": "868", "hashOfConfig": "439"}, {"size": 10089, "mtime": 1752252501841, "results": "869", "hashOfConfig": "439"}, {"size": 1140, "mtime": 1752252501841, "results": "870", "hashOfConfig": "439"}, {"size": 1038, "mtime": 1752252501842, "results": "871", "hashOfConfig": "439"}, {"size": 3607, "mtime": 1752252501837, "results": "872", "hashOfConfig": "439"}, {"size": 2162, "mtime": 1752252501835, "results": "873", "hashOfConfig": "439"}, {"size": 197, "mtime": 1752252501844, "results": "874", "hashOfConfig": "439"}, {"size": 1795, "mtime": 1752252501844, "results": "875", "hashOfConfig": "439"}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "kulmo4", {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "885", "messages": "886", "suppressedMessages": "887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "888", "messages": "889", "suppressedMessages": "890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "891", "messages": "892", "suppressedMessages": "893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "894", "messages": "895", "suppressedMessages": "896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "897", "messages": "898", "suppressedMessages": "899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "900", "messages": "901", "suppressedMessages": "902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "903", "messages": "904", "suppressedMessages": "905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "906", "messages": "907", "suppressedMessages": "908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "909", "messages": "910", "suppressedMessages": "911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "912", "messages": "913", "suppressedMessages": "914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "915", "messages": "916", "suppressedMessages": "917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "918", "messages": "919", "suppressedMessages": "920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "921", "messages": "922", "suppressedMessages": "923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "924", "messages": "925", "suppressedMessages": "926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "927", "messages": "928", "suppressedMessages": "929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "930", "messages": "931", "suppressedMessages": "932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "933", "messages": "934", "suppressedMessages": "935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "936", "messages": "937", "suppressedMessages": "938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "939", "messages": "940", "suppressedMessages": "941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "954", "messages": "955", "suppressedMessages": "956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "957", "messages": "958", "suppressedMessages": "959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "960", "messages": "961", "suppressedMessages": "962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "963", "messages": "964", "suppressedMessages": "965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "966", "messages": "967", "suppressedMessages": "968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "969", "messages": "970", "suppressedMessages": "971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "972", "messages": "973", "suppressedMessages": "974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "975", "messages": "976", "suppressedMessages": "977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "suppressedMessages": "980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "981", "messages": "982", "suppressedMessages": "983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "984", "messages": "985", "suppressedMessages": "986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "987", "messages": "988", "suppressedMessages": "989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "990", "messages": "991", "suppressedMessages": "992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "993", "messages": "994", "suppressedMessages": "995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "996", "messages": "997", "suppressedMessages": "998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "999", "messages": "1000", "suppressedMessages": "1001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1002", "messages": "1003", "suppressedMessages": "1004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1005", "messages": "1006", "suppressedMessages": "1007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1008", "messages": "1009", "suppressedMessages": "1010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1011", "messages": "1012", "suppressedMessages": "1013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1014", "messages": "1015", "suppressedMessages": "1016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1017", "messages": "1018", "suppressedMessages": "1019", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1020", "messages": "1021", "suppressedMessages": "1022", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1023", "messages": "1024", "suppressedMessages": "1025", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1026", "messages": "1027", "suppressedMessages": "1028", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1029", "messages": "1030", "suppressedMessages": "1031", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1032", "messages": "1033", "suppressedMessages": "1034", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1035", "messages": "1036", "suppressedMessages": "1037", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1038", "messages": "1039", "suppressedMessages": "1040", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1041", "messages": "1042", "suppressedMessages": "1043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1044", "messages": "1045", "suppressedMessages": "1046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1047", "messages": "1048", "suppressedMessages": "1049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1050", "messages": "1051", "suppressedMessages": "1052", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1053", "messages": "1054", "suppressedMessages": "1055", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1056", "messages": "1057", "suppressedMessages": "1058", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1059", "messages": "1060", "suppressedMessages": "1061", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1062", "messages": "1063", "suppressedMessages": "1064", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1065", "messages": "1066", "suppressedMessages": "1067", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1068", "messages": "1069", "suppressedMessages": "1070", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1071", "messages": "1072", "suppressedMessages": "1073", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1074", "messages": "1075", "suppressedMessages": "1076", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1077", "messages": "1078", "suppressedMessages": "1079", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1080", "messages": "1081", "suppressedMessages": "1082", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1083", "messages": "1084", "suppressedMessages": "1085", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1086", "messages": "1087", "suppressedMessages": "1088", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1089", "messages": "1090", "suppressedMessages": "1091", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1092", "messages": "1093", "suppressedMessages": "1094", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1095", "messages": "1096", "suppressedMessages": "1097", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1098", "messages": "1099", "suppressedMessages": "1100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1101", "messages": "1102", "suppressedMessages": "1103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1104", "messages": "1105", "suppressedMessages": "1106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1107", "messages": "1108", "suppressedMessages": "1109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1110", "messages": "1111", "suppressedMessages": "1112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1113", "messages": "1114", "suppressedMessages": "1115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1116", "messages": "1117", "suppressedMessages": "1118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1119", "messages": "1120", "suppressedMessages": "1121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1122", "messages": "1123", "suppressedMessages": "1124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1125", "messages": "1126", "suppressedMessages": "1127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1128", "messages": "1129", "suppressedMessages": "1130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1131", "messages": "1132", "suppressedMessages": "1133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1134", "messages": "1135", "suppressedMessages": "1136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1137", "messages": "1138", "suppressedMessages": "1139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "1140", "messages": "1141", "suppressedMessages": "1142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1143", "messages": "1144", "suppressedMessages": "1145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1146", "messages": "1147", "suppressedMessages": "1148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1149", "messages": "1150", "suppressedMessages": "1151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1152", "messages": "1153", "suppressedMessages": "1154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1155", "messages": "1156", "suppressedMessages": "1157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1158", "messages": "1159", "suppressedMessages": "1160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1161", "messages": "1162", "suppressedMessages": "1163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1164", "messages": "1165", "suppressedMessages": "1166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1167", "messages": "1168", "suppressedMessages": "1169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1170", "messages": "1171", "suppressedMessages": "1172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1173", "messages": "1174", "suppressedMessages": "1175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1176", "messages": "1177", "suppressedMessages": "1178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1179", "messages": "1180", "suppressedMessages": "1181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1182", "messages": "1183", "suppressedMessages": "1184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1185", "messages": "1186", "suppressedMessages": "1187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1188", "messages": "1189", "suppressedMessages": "1190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1191", "messages": "1192", "suppressedMessages": "1193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1194", "messages": "1195", "suppressedMessages": "1196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1197", "messages": "1198", "suppressedMessages": "1199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1200", "messages": "1201", "suppressedMessages": "1202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1203", "messages": "1204", "suppressedMessages": "1205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1206", "messages": "1207", "suppressedMessages": "1208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1209", "messages": "1210", "suppressedMessages": "1211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1212", "messages": "1213", "suppressedMessages": "1214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1215", "messages": "1216", "suppressedMessages": "1217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1218", "messages": "1219", "suppressedMessages": "1220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1221", "messages": "1222", "suppressedMessages": "1223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1224", "messages": "1225", "suppressedMessages": "1226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1227", "messages": "1228", "suppressedMessages": "1229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1230", "messages": "1231", "suppressedMessages": "1232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1233", "messages": "1234", "suppressedMessages": "1235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1236", "messages": "1237", "suppressedMessages": "1238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1239", "messages": "1240", "suppressedMessages": "1241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1242", "messages": "1243", "suppressedMessages": "1244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1245", "messages": "1246", "suppressedMessages": "1247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1248", "messages": "1249", "suppressedMessages": "1250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1251", "messages": "1252", "suppressedMessages": "1253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1254", "messages": "1255", "suppressedMessages": "1256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1257", "messages": "1258", "suppressedMessages": "1259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1260", "messages": "1261", "suppressedMessages": "1262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1263", "messages": "1264", "suppressedMessages": "1265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1266", "messages": "1267", "suppressedMessages": "1268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1269", "messages": "1270", "suppressedMessages": "1271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1272", "messages": "1273", "suppressedMessages": "1274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1275", "messages": "1276", "suppressedMessages": "1277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1278", "messages": "1279", "suppressedMessages": "1280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1281", "messages": "1282", "suppressedMessages": "1283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1284", "messages": "1285", "suppressedMessages": "1286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1287", "messages": "1288", "suppressedMessages": "1289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1290", "messages": "1291", "suppressedMessages": "1292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1293", "messages": "1294", "suppressedMessages": "1295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1296", "messages": "1297", "suppressedMessages": "1298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1299", "messages": "1300", "suppressedMessages": "1301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1302", "messages": "1303", "suppressedMessages": "1304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1305", "messages": "1306", "suppressedMessages": "1307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1308", "messages": "1309", "suppressedMessages": "1310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1311", "messages": "1312", "suppressedMessages": "1313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1314", "messages": "1315", "suppressedMessages": "1316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1317", "messages": "1318", "suppressedMessages": "1319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1320", "messages": "1321", "suppressedMessages": "1322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1323", "messages": "1324", "suppressedMessages": "1325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1326", "messages": "1327", "suppressedMessages": "1328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1329", "messages": "1330", "suppressedMessages": "1331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1332", "messages": "1333", "suppressedMessages": "1334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1335", "messages": "1336", "suppressedMessages": "1337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1338", "messages": "1339", "suppressedMessages": "1340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1341", "messages": "1342", "suppressedMessages": "1343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1344", "messages": "1345", "suppressedMessages": "1346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1347", "messages": "1348", "suppressedMessages": "1349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1350", "messages": "1351", "suppressedMessages": "1352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1353", "messages": "1354", "suppressedMessages": "1355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1356", "messages": "1357", "suppressedMessages": "1358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1359", "messages": "1360", "suppressedMessages": "1361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1362", "messages": "1363", "suppressedMessages": "1364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1365", "messages": "1366", "suppressedMessages": "1367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1368", "messages": "1369", "suppressedMessages": "1370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1371", "messages": "1372", "suppressedMessages": "1373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1374", "messages": "1375", "suppressedMessages": "1376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1377", "messages": "1378", "suppressedMessages": "1379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1380", "messages": "1381", "suppressedMessages": "1382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1383", "messages": "1384", "suppressedMessages": "1385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1386", "messages": "1387", "suppressedMessages": "1388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1389", "messages": "1390", "suppressedMessages": "1391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1392", "messages": "1393", "suppressedMessages": "1394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1395", "messages": "1396", "suppressedMessages": "1397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1398", "messages": "1399", "suppressedMessages": "1400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1401", "messages": "1402", "suppressedMessages": "1403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1404", "messages": "1405", "suppressedMessages": "1406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1407", "messages": "1408", "suppressedMessages": "1409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1410", "messages": "1411", "suppressedMessages": "1412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1413", "messages": "1414", "suppressedMessages": "1415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1416", "messages": "1417", "suppressedMessages": "1418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1419", "messages": "1420", "suppressedMessages": "1421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1422", "messages": "1423", "suppressedMessages": "1424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1425", "messages": "1426", "suppressedMessages": "1427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1428", "messages": "1429", "suppressedMessages": "1430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1431", "messages": "1432", "suppressedMessages": "1433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1434", "messages": "1435", "suppressedMessages": "1436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1437", "messages": "1438", "suppressedMessages": "1439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1440", "messages": "1441", "suppressedMessages": "1442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1443", "messages": "1444", "suppressedMessages": "1445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1446", "messages": "1447", "suppressedMessages": "1448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1449", "messages": "1450", "suppressedMessages": "1451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1452", "messages": "1453", "suppressedMessages": "1454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1455", "messages": "1456", "suppressedMessages": "1457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1458", "messages": "1459", "suppressedMessages": "1460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1461", "messages": "1462", "suppressedMessages": "1463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1464", "messages": "1465", "suppressedMessages": "1466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1467", "messages": "1468", "suppressedMessages": "1469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1470", "messages": "1471", "suppressedMessages": "1472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1473", "messages": "1474", "suppressedMessages": "1475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1476", "messages": "1477", "suppressedMessages": "1478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1479", "messages": "1480", "suppressedMessages": "1481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1482", "messages": "1483", "suppressedMessages": "1484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1485", "messages": "1486", "suppressedMessages": "1487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1488", "messages": "1489", "suppressedMessages": "1490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1491", "messages": "1492", "suppressedMessages": "1493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1494", "messages": "1495", "suppressedMessages": "1496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1497", "messages": "1498", "suppressedMessages": "1499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1500", "messages": "1501", "suppressedMessages": "1502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1503", "messages": "1504", "suppressedMessages": "1505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1506", "messages": "1507", "suppressedMessages": "1508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1509", "messages": "1510", "suppressedMessages": "1511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1512", "messages": "1513", "suppressedMessages": "1514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1515", "messages": "1516", "suppressedMessages": "1517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1518", "messages": "1519", "suppressedMessages": "1520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1521", "messages": "1522", "suppressedMessages": "1523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1524", "messages": "1525", "suppressedMessages": "1526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1527", "messages": "1528", "suppressedMessages": "1529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1530", "messages": "1531", "suppressedMessages": "1532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1533", "messages": "1534", "suppressedMessages": "1535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1536", "messages": "1537", "suppressedMessages": "1538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1539", "messages": "1540", "suppressedMessages": "1541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1542", "messages": "1543", "suppressedMessages": "1544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1545", "messages": "1546", "suppressedMessages": "1547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1548", "messages": "1549", "suppressedMessages": "1550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1551", "messages": "1552", "suppressedMessages": "1553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1554", "messages": "1555", "suppressedMessages": "1556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1557", "messages": "1558", "suppressedMessages": "1559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1560", "messages": "1561", "suppressedMessages": "1562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1563", "messages": "1564", "suppressedMessages": "1565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1566", "messages": "1567", "suppressedMessages": "1568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1569", "messages": "1570", "suppressedMessages": "1571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1572", "messages": "1573", "suppressedMessages": "1574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1575", "messages": "1576", "suppressedMessages": "1577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1578", "messages": "1579", "suppressedMessages": "1580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1581", "messages": "1582", "suppressedMessages": "1583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1584", "messages": "1585", "suppressedMessages": "1586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1587", "messages": "1588", "suppressedMessages": "1589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1590", "messages": "1591", "suppressedMessages": "1592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1593", "messages": "1594", "suppressedMessages": "1595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1596", "messages": "1597", "suppressedMessages": "1598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1599", "messages": "1600", "suppressedMessages": "1601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1602", "messages": "1603", "suppressedMessages": "1604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1605", "messages": "1606", "suppressedMessages": "1607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1608", "messages": "1609", "suppressedMessages": "1610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1611", "messages": "1612", "suppressedMessages": "1613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1614", "messages": "1615", "suppressedMessages": "1616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1617", "messages": "1618", "suppressedMessages": "1619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1620", "messages": "1621", "suppressedMessages": "1622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1623", "messages": "1624", "suppressedMessages": "1625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1626", "messages": "1627", "suppressedMessages": "1628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "1629", "messages": "1630", "suppressedMessages": "1631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1632", "messages": "1633", "suppressedMessages": "1634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1635", "messages": "1636", "suppressedMessages": "1637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1638", "messages": "1639", "suppressedMessages": "1640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1641", "messages": "1642", "suppressedMessages": "1643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1644", "messages": "1645", "suppressedMessages": "1646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1647", "messages": "1648", "suppressedMessages": "1649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1650", "messages": "1651", "suppressedMessages": "1652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1653", "messages": "1654", "suppressedMessages": "1655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1656", "messages": "1657", "suppressedMessages": "1658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1659", "messages": "1660", "suppressedMessages": "1661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1662", "messages": "1663", "suppressedMessages": "1664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1665", "messages": "1666", "suppressedMessages": "1667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1668", "messages": "1669", "suppressedMessages": "1670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1671", "messages": "1672", "suppressedMessages": "1673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1674", "messages": "1675", "suppressedMessages": "1676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1677", "messages": "1678", "suppressedMessages": "1679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1680", "messages": "1681", "suppressedMessages": "1682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1683", "messages": "1684", "suppressedMessages": "1685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1686", "messages": "1687", "suppressedMessages": "1688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1689", "messages": "1690", "suppressedMessages": "1691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1692", "messages": "1693", "suppressedMessages": "1694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1695", "messages": "1696", "suppressedMessages": "1697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1698", "messages": "1699", "suppressedMessages": "1700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1701", "messages": "1702", "suppressedMessages": "1703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1704", "messages": "1705", "suppressedMessages": "1706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1707", "messages": "1708", "suppressedMessages": "1709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1710", "messages": "1711", "suppressedMessages": "1712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1713", "messages": "1714", "suppressedMessages": "1715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1716", "messages": "1717", "suppressedMessages": "1718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1719", "messages": "1720", "suppressedMessages": "1721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1722", "messages": "1723", "suppressedMessages": "1724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1725", "messages": "1726", "suppressedMessages": "1727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1728", "messages": "1729", "suppressedMessages": "1730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1731", "messages": "1732", "suppressedMessages": "1733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1734", "messages": "1735", "suppressedMessages": "1736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1737", "messages": "1738", "suppressedMessages": "1739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1740", "messages": "1741", "suppressedMessages": "1742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1743", "messages": "1744", "suppressedMessages": "1745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1746", "messages": "1747", "suppressedMessages": "1748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1749", "messages": "1750", "suppressedMessages": "1751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1752", "messages": "1753", "suppressedMessages": "1754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1755", "messages": "1756", "suppressedMessages": "1757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1758", "messages": "1759", "suppressedMessages": "1760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1761", "messages": "1762", "suppressedMessages": "1763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1764", "messages": "1765", "suppressedMessages": "1766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1767", "messages": "1768", "suppressedMessages": "1769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1770", "messages": "1771", "suppressedMessages": "1772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1773", "messages": "1774", "suppressedMessages": "1775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1776", "messages": "1777", "suppressedMessages": "1778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1779", "messages": "1780", "suppressedMessages": "1781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1782", "messages": "1783", "suppressedMessages": "1784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1785", "messages": "1786", "suppressedMessages": "1787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1788", "messages": "1789", "suppressedMessages": "1790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1791", "messages": "1792", "suppressedMessages": "1793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1794", "messages": "1795", "suppressedMessages": "1796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1797", "messages": "1798", "suppressedMessages": "1799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "1800", "messages": "1801", "suppressedMessages": "1802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1803", "messages": "1804", "suppressedMessages": "1805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1806", "messages": "1807", "suppressedMessages": "1808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1809", "messages": "1810", "suppressedMessages": "1811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1812", "messages": "1813", "suppressedMessages": "1814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1815", "messages": "1816", "suppressedMessages": "1817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1818", "messages": "1819", "suppressedMessages": "1820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1821", "messages": "1822", "suppressedMessages": "1823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1824", "messages": "1825", "suppressedMessages": "1826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1827", "messages": "1828", "suppressedMessages": "1829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1830", "messages": "1831", "suppressedMessages": "1832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1833", "messages": "1834", "suppressedMessages": "1835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1836", "messages": "1837", "suppressedMessages": "1838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1839", "messages": "1840", "suppressedMessages": "1841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1842", "messages": "1843", "suppressedMessages": "1844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1845", "messages": "1846", "suppressedMessages": "1847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1848", "messages": "1849", "suppressedMessages": "1850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1851", "messages": "1852", "suppressedMessages": "1853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1854", "messages": "1855", "suppressedMessages": "1856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1857", "messages": "1858", "suppressedMessages": "1859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1860", "messages": "1861", "suppressedMessages": "1862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1863", "messages": "1864", "suppressedMessages": "1865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1866", "messages": "1867", "suppressedMessages": "1868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1869", "messages": "1870", "suppressedMessages": "1871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1872", "messages": "1873", "suppressedMessages": "1874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1875", "messages": "1876", "suppressedMessages": "1877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1878", "messages": "1879", "suppressedMessages": "1880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1881", "messages": "1882", "suppressedMessages": "1883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1884", "messages": "1885", "suppressedMessages": "1886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1887", "messages": "1888", "suppressedMessages": "1889", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1890", "messages": "1891", "suppressedMessages": "1892", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1893", "messages": "1894", "suppressedMessages": "1895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1896", "messages": "1897", "suppressedMessages": "1898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1899", "messages": "1900", "suppressedMessages": "1901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1902", "messages": "1903", "suppressedMessages": "1904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1905", "messages": "1906", "suppressedMessages": "1907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1908", "messages": "1909", "suppressedMessages": "1910", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1911", "messages": "1912", "suppressedMessages": "1913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1914", "messages": "1915", "suppressedMessages": "1916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1917", "messages": "1918", "suppressedMessages": "1919", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1920", "messages": "1921", "suppressedMessages": "1922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1923", "messages": "1924", "suppressedMessages": "1925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1926", "messages": "1927", "suppressedMessages": "1928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1929", "messages": "1930", "suppressedMessages": "1931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1932", "messages": "1933", "suppressedMessages": "1934", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1935", "messages": "1936", "suppressedMessages": "1937", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1938", "messages": "1939", "suppressedMessages": "1940", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1941", "messages": "1942", "suppressedMessages": "1943", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1944", "messages": "1945", "suppressedMessages": "1946", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1947", "messages": "1948", "suppressedMessages": "1949", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1950", "messages": "1951", "suppressedMessages": "1952", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1953", "messages": "1954", "suppressedMessages": "1955", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1956", "messages": "1957", "suppressedMessages": "1958", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1959", "messages": "1960", "suppressedMessages": "1961", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1962", "messages": "1963", "suppressedMessages": "1964", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1965", "messages": "1966", "suppressedMessages": "1967", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1968", "messages": "1969", "suppressedMessages": "1970", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1971", "messages": "1972", "suppressedMessages": "1973", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1974", "messages": "1975", "suppressedMessages": "1976", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1977", "messages": "1978", "suppressedMessages": "1979", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1980", "messages": "1981", "suppressedMessages": "1982", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1983", "messages": "1984", "suppressedMessages": "1985", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1986", "messages": "1987", "suppressedMessages": "1988", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1989", "messages": "1990", "suppressedMessages": "1991", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1992", "messages": "1993", "suppressedMessages": "1994", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1995", "messages": "1996", "suppressedMessages": "1997", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1998", "messages": "1999", "suppressedMessages": "2000", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2001", "messages": "2002", "suppressedMessages": "2003", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2004", "messages": "2005", "suppressedMessages": "2006", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2007", "messages": "2008", "suppressedMessages": "2009", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2010", "messages": "2011", "suppressedMessages": "2012", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2013", "messages": "2014", "suppressedMessages": "2015", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2016", "messages": "2017", "suppressedMessages": "2018", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2019", "messages": "2020", "suppressedMessages": "2021", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2022", "messages": "2023", "suppressedMessages": "2024", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2025", "messages": "2026", "suppressedMessages": "2027", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2028", "messages": "2029", "suppressedMessages": "2030", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2031", "messages": "2032", "suppressedMessages": "2033", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2034", "messages": "2035", "suppressedMessages": "2036", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2037", "messages": "2038", "suppressedMessages": "2039", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2040", "messages": "2041", "suppressedMessages": "2042", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2043", "messages": "2044", "suppressedMessages": "2045", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2046", "messages": "2047", "suppressedMessages": "2048", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2049", "messages": "2050", "suppressedMessages": "2051", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2052", "messages": "2053", "suppressedMessages": "2054", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2055", "messages": "2056", "suppressedMessages": "2057", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2058", "messages": "2059", "suppressedMessages": "2060", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2061", "messages": "2062", "suppressedMessages": "2063", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2064", "messages": "2065", "suppressedMessages": "2066", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2067", "messages": "2068", "suppressedMessages": "2069", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2070", "messages": "2071", "suppressedMessages": "2072", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2073", "messages": "2074", "suppressedMessages": "2075", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2076", "messages": "2077", "suppressedMessages": "2078", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2079", "messages": "2080", "suppressedMessages": "2081", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2082", "messages": "2083", "suppressedMessages": "2084", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2085", "messages": "2086", "suppressedMessages": "2087", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2088", "messages": "2089", "suppressedMessages": "2090", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "2091", "messages": "2092", "suppressedMessages": "2093", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2094", "messages": "2095", "suppressedMessages": "2096", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2097", "messages": "2098", "suppressedMessages": "2099", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2100", "messages": "2101", "suppressedMessages": "2102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2103", "messages": "2104", "suppressedMessages": "2105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2106", "messages": "2107", "suppressedMessages": "2108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2109", "messages": "2110", "suppressedMessages": "2111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2112", "messages": "2113", "suppressedMessages": "2114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2115", "messages": "2116", "suppressedMessages": "2117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2118", "messages": "2119", "suppressedMessages": "2120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2121", "messages": "2122", "suppressedMessages": "2123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2124", "messages": "2125", "suppressedMessages": "2126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "2127", "messages": "2128", "suppressedMessages": "2129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2130", "messages": "2131", "suppressedMessages": "2132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2133", "messages": "2134", "suppressedMessages": "2135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2136", "messages": "2137", "suppressedMessages": "2138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2139", "messages": "2140", "suppressedMessages": "2141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2142", "messages": "2143", "suppressedMessages": "2144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2145", "messages": "2146", "suppressedMessages": "2147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2148", "messages": "2149", "suppressedMessages": "2150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2151", "messages": "2152", "suppressedMessages": "2153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2154", "messages": "2155", "suppressedMessages": "2156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2157", "messages": "2158", "suppressedMessages": "2159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2160", "messages": "2161", "suppressedMessages": "2162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "2163", "messages": "2164", "suppressedMessages": "2165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2166", "messages": "2167", "suppressedMessages": "2168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2169", "messages": "2170", "suppressedMessages": "2171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2172", "messages": "2173", "suppressedMessages": "2174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2175", "messages": "2176", "suppressedMessages": "2177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2178", "messages": "2179", "suppressedMessages": "2180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2181", "messages": "2182", "suppressedMessages": "2183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "2184", "messages": "2185", "suppressedMessages": "2186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\loading.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\settings\\billing\\page.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\settings\\layout.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\settings\\page.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\settings\\teams\\page.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\settings\\usage-logs\\page.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\page.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\settings\\billing\\page.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\settings\\layout.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\settings\\members\\page.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\settings\\page.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\config\\[agentId]\\layout.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\config\\[agentId]\\page.tsx", [], ["2187"], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\config\\[agentId]\\workflow\\layout.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\config\\[agentId]\\workflow\\[workflowId]\\page.tsx", ["2188"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\layout.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\page.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\[threadId]\\layout.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\[threadId]\\page.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\[threadId]\\redirect-page.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\layout.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\model-pricing\\page.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\[threadId]\\layout.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\[threadId]\\page.tsx", ["2189", "2190"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\_components\\index.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\_components\\ThreadError.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\_components\\ThreadLayout.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\_components\\UpgradeDialog.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\_hooks\\index.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\_hooks\\useBilling.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\_hooks\\useKeyboardShortcuts.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\_hooks\\useThreadData.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\_hooks\\useToolCalls.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\_types\\index.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\settings\\credentials\\layout.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\settings\\credentials\\page.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(home)\\layout.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(home)\\page.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\api\\integrations\\[provider]\\callback\\route.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\api\\share-page\\og-image\\route.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\api\\triggers\\qstash\\webhook\\route.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\api\\webhooks\\trigger\\[workflowId]\\route.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\auth\\actions.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\auth\\callback\\route.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\auth\\github-popup\\page.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\auth\\page.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\auth\\reset-password\\page.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\global-error.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\invitation\\page.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\legal\\page.tsx", ["2191", "2192"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\metadata.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\monitoring\\route.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\not-found.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\opengraph-image.tsx", ["2193"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\providers.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\share\\[threadId]\\layout.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\share\\[threadId]\\page.tsx", ["2194", "2195"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\agent-builder-chat.tsx", ["2196"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\agent-config-modal.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\agent-mcp-configuration.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\agent-preview.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\agent-tools-configuration.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\agents-grid.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\agents-list.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\AgentVersionManager.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\create-agent-dialog.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\custom-agents-page\\agent-card.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\custom-agents-page\\header.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\custom-agents-page\\index.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\custom-agents-page\\loading-skeleton.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\custom-agents-page\\marketplace-section-header.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\custom-agents-page\\marketplace-tab.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\custom-agents-page\\my-agents-tab.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\custom-agents-page\\my-templates-tab.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\custom-agents-page\\publish-dialog.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\custom-agents-page\\search-bar.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\custom-agents-page\\tabs-navigation.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\empty-state.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\installation\\custom-server-step.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\installation\\streamlined-install-dialog.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\installation\\streamlined-profile-connector.tsx", ["2197", "2198"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\installation\\types.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\knowledge-base\\agent-knowledge-base-manager.tsx", ["2199"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\loading-state.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\mcp\\configured-mcp-list.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\mcp\\custom-mcp-dialog.tsx", ["2200"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\mcp\\mcp-configuration-new.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\mcp\\mcp-server-card.tsx", ["2201"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\mcp\\tools-manager.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\mcp\\types.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pagination.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\agent-pipedream-tools-manager.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\constants.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\credential-profile-manager.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\credential-profile-selector.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\pipedream-connect-button.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\pipedream-connections-section.tsx", ["2202", "2203"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\pipedream-connector.tsx", ["2204", "2205", "2206", "2207"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\pipedream-registry.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\pipedream-tool-selector.tsx", ["2208"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\pipedream-types.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\types.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\utils.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\_components\\AppCard.tsx", ["2209"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\_components\\AppsGrid.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\_components\\CategorySidebar.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\_components\\ConnectedAppsSection.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\_components\\EmptyState.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\_components\\index.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\_components\\PaginationControls.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\pipedream\\_components\\PipedreamHeader.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\results-info.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\search-and-filters.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\style-picker.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\tools.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\triggers\\agent-triggers-configuration.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\triggers\\configured-triggers-list.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\triggers\\one-click-integrations.tsx", ["2210"], ["2211"], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\triggers\\providers\\schedule-config.tsx", ["2212"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\triggers\\trigger-browse-dialog.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\triggers\\trigger-config-dialog.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\triggers\\types.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\triggers\\utils.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\workflows\\agent-workflows-configuration.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\agents\\workflows\\conditional-workflow-builder.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\AuthProvider.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\accept-team-invitation.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\account-selector.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\client-user-account-button.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\create-team-dialog.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\create-team-invitation-button.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\delete-team-invitation-button.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\delete-team-member-form.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\edit-personal-account-name.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\edit-team-member-role-form.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\edit-team-name.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\edit-team-slug.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\manage-team-invitations.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\manage-team-members.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\manage-teams.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\new-invitation-form.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\new-team-form.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\team-member-options.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\basejump\\user-account-button.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\billing\\account-billing-status.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\billing\\billing-modal.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\billing\\payment-required-dialog.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\billing\\usage-limit-alert.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\billing\\usage-logs.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\dashboard\\dashboard-content.tsx", ["2213"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\dashboard\\examples.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\dashboard\\layout-content.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\dashboard\\maintenance-banner.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\dashboard\\maintenance-notice.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\examples\\ErrorHandlingDemo.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\file-renderers\\binary-renderer.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\file-renderers\\code-renderer.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\file-renderers\\csv-renderer.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\file-renderers\\html-renderer.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\file-renderers\\image-renderer.tsx", ["2214", "2215"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\file-renderers\\index.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\file-renderers\\markdown-renderer.tsx", ["2216"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\file-renderers\\pdf-renderer.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\GithubSignIn.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\GoogleSignIn.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\first-bento-animation.tsx", [], ["2217"], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\fourth-bento-animation.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\icons.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\nav-menu.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\second-bento-animation.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\section-header.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\bento-section.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\company-showcase.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\cta-section.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\faq-section.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\feature-section.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\footer-section.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\growth-section.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\hero-section.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\hero-video-section.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\navbar.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\open-source-section.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\pricing-section.tsx", ["2218"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\quote-section.tsx", [], ["2219"], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\testimonial-section.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\sections\\use-cases-section.tsx", ["2220"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\testimonial-scroll.tsx", [], ["2221"], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\theme-provider.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\theme-toggle.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\third-bento-animation.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\ui\\accordion.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\ui\\feature-slideshow.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\ui\\flickering-grid.tsx", ["2222"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\ui\\globe.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\ui\\hero-video-dialog.tsx", [], ["2223"], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\ui\\marquee.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\ui\\orbiting-circle.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\ui\\reasoning.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\home\\ui\\response-stream.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\maintenance\\maintenance-page.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\maintenance-alert.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\page-header.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\payment\\paywall-dialog.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\sidebar\\cta.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\sidebar\\date-picker.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\sidebar\\kortix-enterprise-modal.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\sidebar\\kortix-logo.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\sidebar\\nav-agents.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\sidebar\\nav-main.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\sidebar\\nav-user-with-teams.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\sidebar\\search-search.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\sidebar\\share-modal.tsx", ["2224"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\sidebar\\sidebar-left.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\theme-provider.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\attachment-group.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\chat-input\\agent-selector.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\chat-input\\chat-dropdown.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\chat-input\\chat-input.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\chat-input\\custom-model-dialog.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\chat-input\\file-upload-handler.tsx", ["2225"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\chat-input\\floating-tool-preview.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\chat-input\\message-input.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\chat-input\\model-selector.tsx", ["2226"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\chat-input\\uploaded-file-display.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\chat-input\\voice-recorder.tsx", ["2227"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\chat-input\\_use-model-selection.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\content\\loader.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\content\\PlaybackControls.tsx", ["2228", "2229", "2230"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\content\\ThreadContent.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\content\\ThreadSkeleton.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\DeleteConfirmationDialog.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\file-attachment.tsx", ["2231"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\file-browser.tsx", ["2232"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\file-viewer-modal.tsx", ["2233"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\knowledge-base\\knowledge-base-manager.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\preview-renderers\\csv-renderer.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\preview-renderers\\html-renderer.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\preview-renderers\\index.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\preview-renderers\\markdown-renderer.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\thread-site-header.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-call-side-panel.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\ask-tool\\AskToolView.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\ask-tool\\_utils.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\BrowserToolView.tsx", ["2234", "2235", "2236", "2237"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\command-tool\\CommandToolView.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\command-tool\\TerminateCommandToolView.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\command-tool\\_utils.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\CompleteToolView.tsx", ["2238"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\data-provider-tool\\DataProviderEndpointsToolView.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\data-provider-tool\\ExecuteDataProviderCallToolView.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\data-provider-tool\\_utils.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\DeployToolView.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\expose-port-tool\\ExposePortToolView.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\expose-port-tool\\_utils.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\file-operation\\FileOperationToolView.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\file-operation\\_utils.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\GenericToolView.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\mcp-content-renderer.tsx", ["2239", "2240"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\mcp-format-detector.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\mcp-tool\\McpToolView.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\mcp-tool\\_utils.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\see-image-tool\\SeeImageToolView.tsx", ["2241", "2242"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\see-image-tool\\_utils.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\shared\\ImageLoader.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\shared\\LoadingState.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\str-replace\\StrReplaceToolView.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\str-replace\\_utils.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\tool-result-parser.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\types.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\utils.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\web-scrape-tool\\WebScrapeToolView.tsx", ["2243"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\web-scrape-tool\\_utils.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\web-search-tool\\WebSearchToolView.tsx", ["2244", "2245"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\web-search-tool\\_utils.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\WebCrawlToolView.tsx", ["2246"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\wrapper\\index.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\wrapper\\ToolViewRegistry.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\wrapper\\ToolViewWrapper.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\tool-views\\xml-parser.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\types.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\thread\\utils.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\accordion.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\alert-dialog.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\animated-shiny-text.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\breadcrumb.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\calendar.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\checkbox.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\code-block.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\collapsible.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\command.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\data-table.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\drawer.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\editable.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\fancy-tabs.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\icons\\slack.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\markdown.tsx", ["2247"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\page-header.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\pixel-art-editor.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\pixel-avatar.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\popover.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\portal.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\radio-group.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\ripple.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\scroll-area.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\sheet.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\sidebar.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\skeleton.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\slider.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\sonner.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\status-overlay.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\submit-button.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\workflows\\CredentialProfileSelector.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\components\\workflows\\MCPConfigurationDialog.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\contexts\\BillingContext.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\contexts\\DeleteOperationContext.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\flags.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\conditional-workflow-types.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\keys.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\use-agent-tools.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\use-agent-workflows.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\use-agents.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\use-custom-mcp-tools.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\use-pipedream-tools.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\useAgentVersions.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\utils.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\workflow-builder.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\workflow-keys.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\workflow-prompt-builder.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\agents\\workflow-utils.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\dashboard\\keys.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\dashboard\\use-initiate-agent.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\dashboard\\utils.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\files\\index.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\files\\keys.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\files\\use-file-content.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\files\\use-file-mutations.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\files\\use-file-queries.ts", ["2248"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\files\\use-image-content.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\files\\use-sandbox-mutations.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\index.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\knowledge-base\\keys.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\knowledge-base\\types.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\knowledge-base\\use-knowledge-base-queries.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\mcp\\use-credential-profiles.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\mcp\\use-mcp-servers.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\pipedream\\index.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\pipedream\\keys.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\pipedream\\mcp-discovery.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\pipedream\\use-pipedream-profiles.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\pipedream\\use-pipedream.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\pipedream\\utils.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\secure-mcp\\use-secure-mcp.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\sidebar\\keys.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\sidebar\\use-project-mutations.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\sidebar\\use-public-projects.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\sidebar\\use-sidebar.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\subscriptions\\keys.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\subscriptions\\use-billing.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\subscriptions\\use-model.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\subscriptions\\use-subscriptions.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\threads\\keys.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\threads\\use-agent-run.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\threads\\use-billing-status.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\threads\\use-messages.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\threads\\use-project.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\threads\\use-thread-mutations.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\threads\\use-thread-queries.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\threads\\use-threads.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\threads\\utils.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\transcription\\use-transcription.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\triggers\\index.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\triggers\\use-agent-triggers.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\triggers\\use-oauth-integrations.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\triggers\\use-trigger-providers.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\react-query\\usage\\use-health.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\use-accounts.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\use-announcement-store.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\use-cached-file.ts", ["2249"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\use-file-content.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\use-image-content.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\use-media-query.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\use-mobile.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\use-modal-store.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\use-query.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\useAgentStream.ts", ["2250"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\useBillingError.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\hooks\\useVncPreloader.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\actions\\invitations.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\actions\\members.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\actions\\personal-account.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\actions\\teams.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\actions\\threads.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\api-client.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\api-enhanced.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\api-server.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\api.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\cache-init.ts", ["2251"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\config.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\edge-flags.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\error-handler.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\feature-flags.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\full-invitation-url.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\home.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\site.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\supabase\\client.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\supabase\\handle-edge-error.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\supabase\\middleware.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\supabase\\server.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\utils\\dirty-string-parser.ts", ["2252"], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\utils\\get-agent-style.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\utils\\tool-parser.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\utils\\unicode.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\utils\\url.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\utils\\_avatar-generator.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\providers\\modal-providers.tsx", [], [], "C:\\Users\\<USER>\\suna\\frontend\\src\\providers\\react-query-provider.tsx", [], [], {"ruleId": "2253", "severity": 1, "message": "2254", "line": 215, "column": 9, "nodeType": "2255", "endLine": 251, "endColumn": 4, "suppressions": "2256"}, {"ruleId": "2253", "severity": 1, "message": "2257", "line": 268, "column": 6, "nodeType": "2258", "endLine": 268, "endColumn": 162, "suggestions": "2259"}, {"ruleId": "2253", "severity": 1, "message": "2260", "line": 144, "column": 50, "nodeType": "2258", "endLine": 144, "endColumn": 118, "suggestions": "2261"}, {"ruleId": "2253", "severity": 1, "message": "2262", "line": 337, "column": 5, "nodeType": "2258", "endLine": 337, "endColumn": 176, "suggestions": "2263"}, {"ruleId": "2253", "severity": 1, "message": "2264", "line": 26, "column": 9, "nodeType": "2255", "endLine": 30, "endColumn": 4, "suggestions": "2265"}, {"ruleId": "2253", "severity": 1, "message": "2266", "line": 26, "column": 9, "nodeType": "2255", "endLine": 30, "endColumn": 4, "suggestions": "2267"}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 33, "column": 11, "nodeType": "2270", "endLine": 41, "endColumn": 13}, {"ruleId": "2253", "severity": 1, "message": "2271", "line": 180, "column": 5, "nodeType": "2258", "endLine": 180, "endColumn": 15, "suggestions": "2272"}, {"ruleId": "2273", "severity": 1, "message": "2274", "line": 302, "column": 5, "nodeType": "2275", "messageId": "2276", "endLine": 302, "endColumn": 20}, {"ruleId": "2253", "severity": 1, "message": "2277", "line": 172, "column": 6, "nodeType": "2258", "endLine": 172, "endColumn": 8, "suggestions": "2278"}, {"ruleId": "2253", "severity": 1, "message": "2279", "line": 46, "column": 9, "nodeType": "2255", "endLine": 46, "endColumn": 91}, {"ruleId": "2253", "severity": 1, "message": "2280", "line": 125, "column": 9, "nodeType": "2255", "endLine": 127, "endColumn": 4}, {"ruleId": "2253", "severity": 1, "message": "2281", "line": 336, "column": 6, "nodeType": "2258", "endLine": 336, "endColumn": 8, "suggestions": "2282"}, {"ruleId": "2273", "severity": 1, "message": "2283", "line": 144, "column": 11, "nodeType": "2275", "messageId": "2276", "endLine": 144, "endColumn": 28, "fix": "2284"}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 19, "column": 11, "nodeType": "2270", "endLine": 19, "endColumn": 107}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 346, "column": 15, "nodeType": "2270", "endLine": 355, "endColumn": 17}, {"ruleId": "2253", "severity": 1, "message": "2285", "line": 542, "column": 9, "nodeType": "2255", "endLine": 549, "endColumn": 53}, {"ruleId": "2253", "severity": 1, "message": "2286", "line": 136, "column": 6, "nodeType": "2258", "endLine": 136, "endColumn": 153, "suggestions": "2287"}, {"ruleId": "2253", "severity": 1, "message": "2288", "line": 344, "column": 6, "nodeType": "2258", "endLine": 359, "endColumn": 4, "suggestions": "2289"}, {"ruleId": "2253", "severity": 1, "message": "2290", "line": 475, "column": 6, "nodeType": "2258", "endLine": 484, "endColumn": 4, "suggestions": "2291"}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 493, "column": 17, "nodeType": "2270", "endLine": 497, "endColumn": 19}, {"ruleId": "2253", "severity": 1, "message": "2292", "line": 69, "column": 6, "nodeType": "2258", "endLine": 69, "endColumn": 36, "suggestions": "2293"}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 73, "column": 17, "nodeType": "2270", "endLine": 82, "endColumn": 19}, {"ruleId": "2253", "severity": 1, "message": "2294", "line": 48, "column": 6, "nodeType": "2258", "endLine": 48, "endColumn": 8, "suggestions": "2295"}, {"ruleId": "2296", "severity": 2, "message": "2297", "line": 153, "column": 19, "nodeType": "2298", "messageId": "2299", "endLine": 153, "endColumn": 96, "suppressions": "2300"}, {"ruleId": "2253", "severity": 1, "message": "2301", "line": 158, "column": 6, "nodeType": "2258", "endLine": 158, "endColumn": 138, "suggestions": "2302"}, {"ruleId": "2253", "severity": 1, "message": "2303", "line": 182, "column": 6, "nodeType": "2258", "endLine": 182, "endColumn": 44, "suggestions": "2304"}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 260, "column": 17, "nodeType": "2270", "endLine": 272, "endColumn": 19}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 275, "column": 15, "nodeType": "2270", "endLine": 287, "endColumn": 17}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 106, "column": 15, "nodeType": "2270", "endLine": 110, "endColumn": 17}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 78, "column": 13, "nodeType": "2270", "endLine": 82, "endColumn": 15, "suppressions": "2305"}, {"ruleId": "2253", "severity": 1, "message": "2306", "line": 585, "column": 6, "nodeType": "2258", "endLine": 585, "endColumn": 54, "suggestions": "2307"}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 19, "column": 13, "nodeType": "2270", "endLine": 23, "endColumn": 15, "suppressions": "2308"}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 76, "column": 21, "nodeType": "2270", "endLine": 83, "endColumn": 23}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 37, "column": 7, "nodeType": "2270", "endLine": 37, "endColumn": 67, "suppressions": "2309"}, {"ruleId": "2253", "severity": 1, "message": "2310", "line": 320, "column": 6, "nodeType": "2258", "endLine": 329, "endColumn": 4, "suggestions": "2311"}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 96, "column": 11, "nodeType": "2270", "endLine": 102, "endColumn": 13, "suppressions": "2312"}, {"ruleId": "2253", "severity": 1, "message": "2313", "line": 82, "column": 6, "nodeType": "2258", "endLine": 82, "endColumn": 18, "suggestions": "2314"}, {"ruleId": "2253", "severity": 1, "message": "2315", "line": 218, "column": 8, "nodeType": "2258", "endLine": 218, "endColumn": 10, "suggestions": "2316"}, {"ruleId": "2253", "severity": 1, "message": "2317", "line": 506, "column": 6, "nodeType": "2258", "endLine": 506, "endColumn": 34, "suggestions": "2318"}, {"ruleId": "2253", "severity": 1, "message": "2319", "line": 53, "column": 8, "nodeType": "2258", "endLine": 53, "endColumn": 15, "suggestions": "2320"}, {"ruleId": "2253", "severity": 1, "message": "2321", "line": 112, "column": 6, "nodeType": "2258", "endLine": 112, "endColumn": 53, "suggestions": "2322"}, {"ruleId": "2273", "severity": 1, "message": "2274", "line": 397, "column": 5, "nodeType": "2275", "messageId": "2276", "endLine": 397, "endColumn": 20}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 426, "column": 19, "nodeType": "2270", "endLine": 432, "endColumn": 21}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 312, "column": 17, "nodeType": "2270", "endLine": 367, "endColumn": 19}, {"ruleId": "2253", "severity": 1, "message": "2323", "line": 55, "column": 6, "nodeType": "2258", "endLine": 55, "endColumn": 25, "suggestions": "2324"}, {"ruleId": "2253", "severity": 1, "message": "2325", "line": 794, "column": 71, "nodeType": "2275", "endLine": 794, "endColumn": 78}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 221, "column": 13, "nodeType": "2270", "endLine": 227, "endColumn": 15}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 246, "column": 13, "nodeType": "2270", "endLine": 252, "endColumn": 15}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 365, "column": 21, "nodeType": "2270", "endLine": 371, "endColumn": 23}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 373, "column": 21, "nodeType": "2270", "endLine": 379, "endColumn": 23}, {"ruleId": "2273", "severity": 1, "message": "2326", "line": 59, "column": 13, "nodeType": "2275", "messageId": "2276", "endLine": 59, "endColumn": 25, "fix": "2327"}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 98, "column": 21, "nodeType": "2270", "endLine": 105, "endColumn": 23}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 121, "column": 23, "nodeType": "2270", "endLine": 128, "endColumn": 25}, {"ruleId": "2253", "severity": 1, "message": "2328", "line": 64, "column": 6, "nodeType": "2258", "endLine": 64, "endColumn": 34, "suggestions": "2329"}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 153, "column": 11, "nodeType": "2270", "endLine": 168, "endColumn": 13}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 185, "column": 23, "nodeType": "2270", "endLine": 192, "endColumn": 25}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 141, "column": 25, "nodeType": "2270", "endLine": 150, "endColumn": 27}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 191, "column": 29, "nodeType": "2270", "endLine": 198, "endColumn": 31}, {"ruleId": "2268", "severity": 1, "message": "2269", "line": 194, "column": 23, "nodeType": "2270", "endLine": 201, "endColumn": 25}, {"ruleId": null, "message": "2330", "line": 1, "column": 1, "severity": 1, "nodeType": null, "fix": "2331"}, {"ruleId": "2253", "severity": 1, "message": "2332", "line": 387, "column": 6, "nodeType": "2258", "endLine": 387, "endColumn": 37, "suggestions": "2333"}, {"ruleId": "2253", "severity": 1, "message": "2334", "line": 289, "column": 6, "nodeType": "2258", "endLine": 289, "endColumn": 48, "suggestions": "2335"}, {"ruleId": "2253", "severity": 1, "message": "2336", "line": 392, "column": 5, "nodeType": "2258", "endLine": 400, "endColumn": 6, "suggestions": "2337"}, {"ruleId": "2273", "severity": 1, "message": "2338", "line": 21, "column": 9, "nodeType": "2275", "messageId": "2276", "endLine": 21, "endColumn": 35, "fix": "2339"}, {"ruleId": "2273", "severity": 1, "message": "2340", "line": 136, "column": 11, "nodeType": "2275", "messageId": "2276", "endLine": 136, "endColumn": 17, "fix": "2341"}, "react-hooks/exhaustive-deps", "The 'getSaveStatusBadge' function makes the dependencies of useMemo Hook (at line 446) change on every render. Move it inside the useMemo callback. Alternatively, wrap the definition of 'getSaveStatusBadge' in its own useCallback() Hook.", "VariableDeclarator", ["2342"], "React Hook useCallback has an unnecessary dependency: 'router'. Either exclude it or remove the dependency array.", "ArrayExpression", ["2343"], "React Hook useMemo has a missing dependency: 'project'. Either include it or remove the dependency array.", ["2344"], "React Hook useCallback has a missing dependency: 'selectedAgentId'. Either include it or remove the dependency array.", ["2345"], "The 'updateUrl' function makes the dependencies of useEffect Hook (at line 39) change on every render. To fix this, wrap the definition of 'updateUrl' in its own useCallback() Hook.", ["2346"], "The 'updateUrl' function makes the dependencies of useEffect Hook (at line 44) change on every render. To fix this, wrap the definition of 'updateUrl' in its own useCallback() Hook.", ["2347"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", "React Hook useCallback has an unnecessary dependency: 'threadId'. Either exclude it or remove the dependency array.", ["2348"], "prefer-const", "'playbackTimeout' is never reassigned. Use 'const' instead.", "Identifier", "useConst", "React Hook useCallback has missing dependencies: 'agentId' and 'queryClient'. Either include them or remove the dependency array.", ["2349"], "The 'configProperties' logical expression could make the dependencies of useMemo Hook (at line 316) change on every render. Move it inside the useMemo callback. Alternatively, wrap the initialization of 'configProperties' in its own useMemo() Hook.", "The 'isFieldRequired' function makes the dependencies of useMemo Hook (at line 316) change on every render. Move it inside the useMemo callback. Alternatively, wrap the definition of 'isFieldRequired' in its own useCallback() Hook.", "React Hook useCallback has a missing dependency: 'handleFileUpload'. Either include it or remove the dependency array.", ["2350"], "'configToSave' is never reassigned. Use 'const' instead.", {"range": "2351", "text": "2352"}, "The 'profilesByApp' logical expression could make the dependencies of useMemo Hook (at line 574) change on every render. To fix this, wrap the initialization of 'profilesByApp' in its own useMemo() Hook.", "React Hook useCallback has a missing dependency: 'proceedToTools'. Either include it or remove the dependency array.", ["2353"], "React Hook useMemo has an unnecessary dependency: 'handleProfileOnlyComplete'. Either exclude it or remove the dependency array.", ["2354"], "React Hook useMemo has an unnecessary dependency: 'app.name'. Either exclude it or remove the dependency array.", ["2355"], "React Hook useEffect has a missing dependency: 'fetchTools'. Either include it or remove the dependency array.", ["2356"], "React Hook useEffect has a missing dependency: 'handleCallback'. Either include it or remove the dependency array.", ["2357"], "@typescript-eslint/no-unused-expressions", "Expected an assignment or function call and instead saw an expression.", "ExpressionStatement", "unusedExpression", ["2358"], "React Hook useEffect has missing dependencies: 'config', 'generateCronExpression', and 'onChange'. Either include them or remove the dependency array. If 'onChange' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2359"], "React Hook useEffect has a missing dependency: 'handleSubmit'. Either include it or remove the dependency array.", ["2360"], ["2361"], "React Hook useEffect has a missing dependency: 'getDefaultBillingPeriod'. Either include it or remove the dependency array.", ["2362"], ["2363"], ["2364"], "React Hook useEffect has missing dependencies: 'canvasSize.height' and 'canvasSize.width'. Either include them or remove the dependency array.", ["2365"], ["2366"], "React Hook useEffect has a missing dependency: 'generateShareLink'. Either include it or remove the dependency array.", ["2367"], "React Hook useEffect has a missing dependency: 'setUploadedFiles'. Either include it or remove the dependency array. If 'setUploadedFiles' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["2368"], "React Hook useEffect has a missing dependency: 'isOpen'. Either include it or remove the dependency array.", ["2369"], "React Hook useEffect has a missing dependency: 'stopRecording'. Either include it or remove the dependency array.", ["2370"], "React Hook useCallback has a missing dependency: 'updatePlaybackState'. Either include it or remove the dependency array.", ["2371"], "React Hook useEffect has a missing dependency: 'loadFiles'. Either include it or remove the dependency array.", ["2372"], "The ref value 'activeDownloadUrls.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'activeDownloadUrls.current' to a variable inside the effect, and use that variable in the cleanup function.", "'cleanContent' is never reassigned. Use 'const' instead.", {"range": "2373", "text": "2374"}, "React Hook useEffect has a missing dependency: 'imgSrc'. Either include it or remove the dependency array.", ["2375"], "Unused eslint-disable directive (no problems were reported from '@typescript-eslint/no-explicit-any').", {"range": "2376", "text": "2377"}, "React Hook React.useMemo has a missing dependency: 'options'. Either include it or remove the dependency array.", ["2378"], "React Hook useEffect has missing dependencies: 'getFileContent' and 'localBlobUrl'. Either include them or remove the dependency array.", ["2379"], "React Hook useCallback has unnecessary dependencies: 'setMessages' and 'threadId'. Either exclude them or remove the dependency array.", ["2380"], "'blobUrlsToRevoke' is never reassigned. Use 'const' instead.", {"range": "2381", "text": "2382"}, "'keyTok' is never reassigned. Use 'const' instead.", {"range": "2383", "text": "2384"}, {"kind": "2385", "justification": "2386"}, {"desc": "2387", "fix": "2388"}, {"desc": "2389", "fix": "2390"}, {"desc": "2391", "fix": "2392"}, {"desc": "2393", "fix": "2394"}, {"desc": "2393", "fix": "2395"}, {"desc": "2396", "fix": "2397"}, {"desc": "2398", "fix": "2399"}, {"desc": "2400", "fix": "2401"}, [4693, 4744], "const configToSave: any = { url: configText.trim() };", {"desc": "2402", "fix": "2403"}, {"desc": "2404", "fix": "2405"}, {"desc": "2406", "fix": "2407"}, {"desc": "2408", "fix": "2409"}, {"desc": "2410", "fix": "2411"}, {"kind": "2385", "justification": "2386"}, {"desc": "2412", "fix": "2413"}, {"desc": "2414", "fix": "2415"}, {"kind": "2385", "justification": "2386"}, {"desc": "2416", "fix": "2417"}, {"kind": "2385", "justification": "2386"}, {"kind": "2385", "justification": "2386"}, {"desc": "2418", "fix": "2419"}, {"kind": "2385", "justification": "2386"}, {"desc": "2420", "fix": "2421"}, {"desc": "2422", "fix": "2423"}, {"desc": "2424", "fix": "2425"}, {"desc": "2426", "fix": "2427"}, {"desc": "2428", "fix": "2429"}, {"desc": "2430", "fix": "2431"}, [1535, 1725], "const cleanContent = contentStr\r\n          .replace(/<function_calls>[\\s\\S]*?<\\/function_calls>/g, '')\r\n          .replace(/<invoke name=\"complete\"[\\s\\S]*?<\\/invoke>/g, '')\r\n          .trim();", {"desc": "2432", "fix": "2433"}, [0, 55], " ", {"desc": "2434", "fix": "2435"}, {"desc": "2436", "fix": "2437"}, {"desc": "2438", "fix": "2439"}, [696, 732], "const blobUrlsToRevoke: string[] = [];", [3906, 3934], "const keyTok = this.tz.peek();", "directive", "", "Update the dependencies array to be: [workflowName, workflowDescription, triggerPhrase, isDefault, steps, agentId, workflowId, isEditing, createWorkflowMutation, updateWorkflowMutation]", {"range": "2440", "text": "2441"}, "Update the dependencies array to be: [project]", {"range": "2442", "text": "2443"}, "Update the dependencies array to be: [threadId, setMessages, addUserMessageMutation, startAgentMutation, selectedAgentId, setAgentRunId, messagesQuery, agentRunsQuery, setBillingData, project?.account_id, setShowBillingAlert]", {"range": "2444", "text": "2445"}, "Wrap the definition of 'updateUrl' in its own useCallback() Hook.", {"range": "2446", "text": "2447"}, {"range": "2448", "text": "2447"}, "Update the dependencies array to be: []", {"range": "2449", "text": "2450"}, "Update the dependencies array to be: [agentId, queryClient]", {"range": "2451", "text": "2452"}, "Update the dependencies array to be: [handleFileUpload]", {"range": "2453", "text": "2454"}, "Update the dependencies array to be: [newProfileName, app.name_slug, app.name, connectedProfiles.length, createProfile, connectProfile, refetchProfiles, mode, onComplete, onOpenChange, proceedToTools]", {"range": "2455", "text": "2456"}, "Update the dependencies array to be: [app.name, connectedProfiles, isCreatingProfile, selectedProfileId, selectedProfile, newProfileName, isConnecting, handleProfileNameChange, handleKeyDown, handleCreateProfile, proceedToTools, mode, isCompletingConnection]", {"range": "2457", "text": "2458"}, "Update the dependencies array to be: [isLoadingTools, tools, selectedTools, handleToolToggle, handleComplete, isCompletingConnection, proceedToTools]", {"range": "2459", "text": "2460"}, "Update the dependencies array to be: [profile.profile_id, appSlug, fetchTools]", {"range": "2461", "text": "2462"}, "Update the dependencies array to be: [handleCallback]", {"range": "2463", "text": "2464"}, "Update the dependencies array to be: [scheduleType, selectedPreset, recurringType, selectedWeekdays, selectedMonths, dayOfMonth, scheduleTime, selectedDate, oneTimeTime, generateCronExpression, config, onChange]", {"range": "2465", "text": "2466"}, "Update the dependencies array to be: [autoSubmit, handleSubmit, inputValue, isSubmitting]", {"range": "2467", "text": "2468"}, "Update the dependencies array to be: [isAuthenticated, currentSubscription.price_id, getDefaultBillingPeriod]", {"range": "2469", "text": "2470"}, "Update the dependencies array to be: [setupCanvas, updateSquares, drawGrid, width, height, isInView, squareSize, gridGap, canvasSize.width, canvasSize.height]", {"range": "2471", "text": "2472"}, "Update the dependencies array to be: [generateShareLink, threadData]", {"range": "2473", "text": "2474"}, "Update the dependencies array to be: [setUploadedFiles]", {"range": "2475", "text": "2476"}, "Update the dependencies array to be: [customModels, isOpen, modelOptions]", {"range": "2477", "text": "2478"}, "Update the dependencies array to be: [state, stopRecording]", {"range": "2479", "text": "2480"}, "Update the dependencies array to be: [isPlaying, isSidePanelOpen, onToggleSidePanel, updatePlaybackState]", {"range": "2481", "text": "2482"}, "Update the dependencies array to be: [isOpen, loadFiles, sandboxId]", {"range": "2483", "text": "2484"}, "Update the dependencies array to be: [src, session?.access_token, imgSrc]", {"range": "2485", "text": "2486"}, "Update the dependencies array to be: [query.data, options]", {"range": "2487", "text": "2488"}, "Update the dependencies array to be: [sandboxId, filePath, options.contentType, getFileContent, localBlobUrl]", {"range": "2489", "text": "2490"}, "Update the dependencies array to be: [status, toolCall, callbacks, finalizeStream, updateStatus]", {"range": "2491", "text": "2492"}, [10131, 10287], "[workflowName, workflowDescription, triggerPhrase, isDefault, steps, agentId, workflowId, isEditing, createWorkflowMutation, updateWorkflowMutation]", [5059, 5127], "[project]", [10998, 11169], "[threadId, setMessages, addUserMessageMutation, startAgentMutation, selectedAgentId, setAgentRunId, messagesQuery, agentRunsQuery, setBillingData, project?.account_id, setShowBillingAlert]", [976, 1159], "useCallback((tab: string) => {\r\n    const params = new URLSearchParams(searchParams);\r\n    params.set('tab', tab);\r\n    router.replace(`${pathname}?${params.toString()}`, { scroll: false });\r\n  })", [976, 1159], [5937, 5947], "[]", [7111, 7113], "[agentId, queryClient]", [9186, 9188], "[handleFileUpload]", [4506, 4653], "[newProfileName, app.name_slug, app.name, connectedProfiles.length, createProfile, connectProfile, refetchProfiles, mode, onComplete, onOpenChange, proceedToTools]", [11938, 12271], "[app.name, connectedProfiles, isCreatingProfile, selectedProfileId, selectedProfile, newProfileName, isConnecting, handleProfileNameChange, handleKeyDown, handleCreateProfile, proceedToTools, mode, isCompletingConnection]", [16632, 16806], "[isLoadingTools, tools, selectedTools, handleToolToggle, handleComplete, isCompletingConnection, proceedToTools]", [2333, 2363], "[profile.profile_id, appSlug, fetchTools]", [1501, 1503], "[handleCallback]", [7960, 8092], "[scheduleType, selectedPreset, recurringType, selectedWeekdays, selectedMonths, dayOfMonth, scheduleTime, selectedDate, oneTimeTime, generateCronExpression, config, onChange]", [6486, 6524], "[autoSubmit, handleSubmit, inputValue, isSubmitting]", [22495, 22543], "[isAuthenticated, currentSubscription.price_id, getDefaultBillingPeriod]", [10188, 10318], "[setupCanvas, updateSquares, drawGrid, width, height, isInView, squareSize, gridGap, canvasSize.width, canvasSize.height]", [2632, 2644], "[generateShareLink, threadData]", [7033, 7035], "[setUploadedFiles]", [18039, 18067], "[customModels, isOpen, modelOptions]", [1869, 1876], "[state, stopRecording]", [3144, 3191], "[isPlaying, isSidePanelOpen, onToggleSidePanel, updatePlaybackState]", [1383, 1402], "[isOpen, loadFiles, sandboxId]", [2189, 2217], "[src, session?.access_token, imgSrc]", [12433, 12464], "[query.data, options]", [10404, 10446], "[sandboxId, filePath, options.contentType, getFileContent, localBlobUrl]", [14253, 14392], "[status, toolCall, callbacks, finalizeStream, updateStatus]"]