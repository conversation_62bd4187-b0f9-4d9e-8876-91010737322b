{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/node_modules/%40shikijs/types/dist/index.mjs"], "sourcesContent": ["class ShikiError extends Error {\n  constructor(message) {\n    super(message);\n    this.name = \"ShikiError\";\n  }\n}\n\nexport { ShikiError };\n"], "names": [], "mappings": ";;;AAAA,MAAM,mBAAmB;IACvB,YAAY,OAAO,CAAE;QACnB,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;IACd;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/node_modules/%40shikijs/vscode-textmate/dist/index.js"], "sourcesContent": ["// src/utils.ts\nfunction clone(something) {\n  return doClone(something);\n}\nfunction doClone(something) {\n  if (Array.isArray(something)) {\n    return cloneArray(something);\n  }\n  if (something instanceof RegExp) {\n    return something;\n  }\n  if (typeof something === \"object\") {\n    return cloneObj(something);\n  }\n  return something;\n}\nfunction cloneArray(arr) {\n  let r = [];\n  for (let i = 0, len = arr.length; i < len; i++) {\n    r[i] = doClone(arr[i]);\n  }\n  return r;\n}\nfunction cloneObj(obj) {\n  let r = {};\n  for (let key in obj) {\n    r[key] = doClone(obj[key]);\n  }\n  return r;\n}\nfunction mergeObjects(target, ...sources) {\n  sources.forEach((source) => {\n    for (let key in source) {\n      target[key] = source[key];\n    }\n  });\n  return target;\n}\nfunction basename(path) {\n  const idx = ~path.lastIndexOf(\"/\") || ~path.lastIndexOf(\"\\\\\");\n  if (idx === 0) {\n    return path;\n  } else if (~idx === path.length - 1) {\n    return basename(path.substring(0, path.length - 1));\n  } else {\n    return path.substr(~idx + 1);\n  }\n}\nvar CAPTURING_REGEX_SOURCE = /\\$(\\d+)|\\${(\\d+):\\/(downcase|upcase)}/g;\nvar RegexSource = class {\n  static hasCaptures(regexSource) {\n    if (regexSource === null) {\n      return false;\n    }\n    CAPTURING_REGEX_SOURCE.lastIndex = 0;\n    return CAPTURING_REGEX_SOURCE.test(regexSource);\n  }\n  static replaceCaptures(regexSource, captureSource, captureIndices) {\n    return regexSource.replace(CAPTURING_REGEX_SOURCE, (match, index, commandIndex, command) => {\n      let capture = captureIndices[parseInt(index || commandIndex, 10)];\n      if (capture) {\n        let result = captureSource.substring(capture.start, capture.end);\n        while (result[0] === \".\") {\n          result = result.substring(1);\n        }\n        switch (command) {\n          case \"downcase\":\n            return result.toLowerCase();\n          case \"upcase\":\n            return result.toUpperCase();\n          default:\n            return result;\n        }\n      } else {\n        return match;\n      }\n    });\n  }\n};\nfunction strcmp(a, b) {\n  if (a < b) {\n    return -1;\n  }\n  if (a > b) {\n    return 1;\n  }\n  return 0;\n}\nfunction strArrCmp(a, b) {\n  if (a === null && b === null) {\n    return 0;\n  }\n  if (!a) {\n    return -1;\n  }\n  if (!b) {\n    return 1;\n  }\n  let len1 = a.length;\n  let len2 = b.length;\n  if (len1 === len2) {\n    for (let i = 0; i < len1; i++) {\n      let res = strcmp(a[i], b[i]);\n      if (res !== 0) {\n        return res;\n      }\n    }\n    return 0;\n  }\n  return len1 - len2;\n}\nfunction isValidHexColor(hex) {\n  if (/^#[0-9a-f]{6}$/i.test(hex)) {\n    return true;\n  }\n  if (/^#[0-9a-f]{8}$/i.test(hex)) {\n    return true;\n  }\n  if (/^#[0-9a-f]{3}$/i.test(hex)) {\n    return true;\n  }\n  if (/^#[0-9a-f]{4}$/i.test(hex)) {\n    return true;\n  }\n  return false;\n}\nfunction escapeRegExpCharacters(value) {\n  return value.replace(/[\\-\\\\\\{\\}\\*\\+\\?\\|\\^\\$\\.\\,\\[\\]\\(\\)\\#\\s]/g, \"\\\\$&\");\n}\nvar CachedFn = class {\n  constructor(fn) {\n    this.fn = fn;\n  }\n  cache = /* @__PURE__ */ new Map();\n  get(key) {\n    if (this.cache.has(key)) {\n      return this.cache.get(key);\n    }\n    const value = this.fn(key);\n    this.cache.set(key, value);\n    return value;\n  }\n};\n\n// src/theme.ts\nvar Theme = class {\n  constructor(_colorMap, _defaults, _root) {\n    this._colorMap = _colorMap;\n    this._defaults = _defaults;\n    this._root = _root;\n  }\n  static createFromRawTheme(source, colorMap) {\n    return this.createFromParsedTheme(parseTheme(source), colorMap);\n  }\n  static createFromParsedTheme(source, colorMap) {\n    return resolveParsedThemeRules(source, colorMap);\n  }\n  _cachedMatchRoot = new CachedFn(\n    (scopeName) => this._root.match(scopeName)\n  );\n  getColorMap() {\n    return this._colorMap.getColorMap();\n  }\n  getDefaults() {\n    return this._defaults;\n  }\n  match(scopePath) {\n    if (scopePath === null) {\n      return this._defaults;\n    }\n    const scopeName = scopePath.scopeName;\n    const matchingTrieElements = this._cachedMatchRoot.get(scopeName);\n    const effectiveRule = matchingTrieElements.find(\n      (v) => _scopePathMatchesParentScopes(scopePath.parent, v.parentScopes)\n    );\n    if (!effectiveRule) {\n      return null;\n    }\n    return new StyleAttributes(\n      effectiveRule.fontStyle,\n      effectiveRule.foreground,\n      effectiveRule.background\n    );\n  }\n};\nvar ScopeStack = class _ScopeStack {\n  constructor(parent, scopeName) {\n    this.parent = parent;\n    this.scopeName = scopeName;\n  }\n  static push(path, scopeNames) {\n    for (const name of scopeNames) {\n      path = new _ScopeStack(path, name);\n    }\n    return path;\n  }\n  static from(...segments) {\n    let result = null;\n    for (let i = 0; i < segments.length; i++) {\n      result = new _ScopeStack(result, segments[i]);\n    }\n    return result;\n  }\n  push(scopeName) {\n    return new _ScopeStack(this, scopeName);\n  }\n  getSegments() {\n    let item = this;\n    const result = [];\n    while (item) {\n      result.push(item.scopeName);\n      item = item.parent;\n    }\n    result.reverse();\n    return result;\n  }\n  toString() {\n    return this.getSegments().join(\" \");\n  }\n  extends(other) {\n    if (this === other) {\n      return true;\n    }\n    if (this.parent === null) {\n      return false;\n    }\n    return this.parent.extends(other);\n  }\n  getExtensionIfDefined(base) {\n    const result = [];\n    let item = this;\n    while (item && item !== base) {\n      result.push(item.scopeName);\n      item = item.parent;\n    }\n    return item === base ? result.reverse() : void 0;\n  }\n};\nfunction _scopePathMatchesParentScopes(scopePath, parentScopes) {\n  if (parentScopes.length === 0) {\n    return true;\n  }\n  for (let index = 0; index < parentScopes.length; index++) {\n    let scopePattern = parentScopes[index];\n    let scopeMustMatch = false;\n    if (scopePattern === \">\") {\n      if (index === parentScopes.length - 1) {\n        return false;\n      }\n      scopePattern = parentScopes[++index];\n      scopeMustMatch = true;\n    }\n    while (scopePath) {\n      if (_matchesScope(scopePath.scopeName, scopePattern)) {\n        break;\n      }\n      if (scopeMustMatch) {\n        return false;\n      }\n      scopePath = scopePath.parent;\n    }\n    if (!scopePath) {\n      return false;\n    }\n    scopePath = scopePath.parent;\n  }\n  return true;\n}\nfunction _matchesScope(scopeName, scopePattern) {\n  return scopePattern === scopeName || scopeName.startsWith(scopePattern) && scopeName[scopePattern.length] === \".\";\n}\nvar StyleAttributes = class {\n  constructor(fontStyle, foregroundId, backgroundId) {\n    this.fontStyle = fontStyle;\n    this.foregroundId = foregroundId;\n    this.backgroundId = backgroundId;\n  }\n};\nfunction parseTheme(source) {\n  if (!source) {\n    return [];\n  }\n  if (!source.settings || !Array.isArray(source.settings)) {\n    return [];\n  }\n  let settings = source.settings;\n  let result = [], resultLen = 0;\n  for (let i = 0, len = settings.length; i < len; i++) {\n    let entry = settings[i];\n    if (!entry.settings) {\n      continue;\n    }\n    let scopes;\n    if (typeof entry.scope === \"string\") {\n      let _scope = entry.scope;\n      _scope = _scope.replace(/^[,]+/, \"\");\n      _scope = _scope.replace(/[,]+$/, \"\");\n      scopes = _scope.split(\",\");\n    } else if (Array.isArray(entry.scope)) {\n      scopes = entry.scope;\n    } else {\n      scopes = [\"\"];\n    }\n    let fontStyle = -1 /* NotSet */;\n    if (typeof entry.settings.fontStyle === \"string\") {\n      fontStyle = 0 /* None */;\n      let segments = entry.settings.fontStyle.split(\" \");\n      for (let j = 0, lenJ = segments.length; j < lenJ; j++) {\n        let segment = segments[j];\n        switch (segment) {\n          case \"italic\":\n            fontStyle = fontStyle | 1 /* Italic */;\n            break;\n          case \"bold\":\n            fontStyle = fontStyle | 2 /* Bold */;\n            break;\n          case \"underline\":\n            fontStyle = fontStyle | 4 /* Underline */;\n            break;\n          case \"strikethrough\":\n            fontStyle = fontStyle | 8 /* Strikethrough */;\n            break;\n        }\n      }\n    }\n    let foreground = null;\n    if (typeof entry.settings.foreground === \"string\" && isValidHexColor(entry.settings.foreground)) {\n      foreground = entry.settings.foreground;\n    }\n    let background = null;\n    if (typeof entry.settings.background === \"string\" && isValidHexColor(entry.settings.background)) {\n      background = entry.settings.background;\n    }\n    for (let j = 0, lenJ = scopes.length; j < lenJ; j++) {\n      let _scope = scopes[j].trim();\n      let segments = _scope.split(\" \");\n      let scope = segments[segments.length - 1];\n      let parentScopes = null;\n      if (segments.length > 1) {\n        parentScopes = segments.slice(0, segments.length - 1);\n        parentScopes.reverse();\n      }\n      result[resultLen++] = new ParsedThemeRule(\n        scope,\n        parentScopes,\n        i,\n        fontStyle,\n        foreground,\n        background\n      );\n    }\n  }\n  return result;\n}\nvar ParsedThemeRule = class {\n  constructor(scope, parentScopes, index, fontStyle, foreground, background) {\n    this.scope = scope;\n    this.parentScopes = parentScopes;\n    this.index = index;\n    this.fontStyle = fontStyle;\n    this.foreground = foreground;\n    this.background = background;\n  }\n};\nvar FontStyle = /* @__PURE__ */ ((FontStyle2) => {\n  FontStyle2[FontStyle2[\"NotSet\"] = -1] = \"NotSet\";\n  FontStyle2[FontStyle2[\"None\"] = 0] = \"None\";\n  FontStyle2[FontStyle2[\"Italic\"] = 1] = \"Italic\";\n  FontStyle2[FontStyle2[\"Bold\"] = 2] = \"Bold\";\n  FontStyle2[FontStyle2[\"Underline\"] = 4] = \"Underline\";\n  FontStyle2[FontStyle2[\"Strikethrough\"] = 8] = \"Strikethrough\";\n  return FontStyle2;\n})(FontStyle || {});\nfunction resolveParsedThemeRules(parsedThemeRules, _colorMap) {\n  parsedThemeRules.sort((a, b) => {\n    let r = strcmp(a.scope, b.scope);\n    if (r !== 0) {\n      return r;\n    }\n    r = strArrCmp(a.parentScopes, b.parentScopes);\n    if (r !== 0) {\n      return r;\n    }\n    return a.index - b.index;\n  });\n  let defaultFontStyle = 0 /* None */;\n  let defaultForeground = \"#000000\";\n  let defaultBackground = \"#ffffff\";\n  while (parsedThemeRules.length >= 1 && parsedThemeRules[0].scope === \"\") {\n    let incomingDefaults = parsedThemeRules.shift();\n    if (incomingDefaults.fontStyle !== -1 /* NotSet */) {\n      defaultFontStyle = incomingDefaults.fontStyle;\n    }\n    if (incomingDefaults.foreground !== null) {\n      defaultForeground = incomingDefaults.foreground;\n    }\n    if (incomingDefaults.background !== null) {\n      defaultBackground = incomingDefaults.background;\n    }\n  }\n  let colorMap = new ColorMap(_colorMap);\n  let defaults = new StyleAttributes(defaultFontStyle, colorMap.getId(defaultForeground), colorMap.getId(defaultBackground));\n  let root = new ThemeTrieElement(new ThemeTrieElementRule(0, null, -1 /* NotSet */, 0, 0), []);\n  for (let i = 0, len = parsedThemeRules.length; i < len; i++) {\n    let rule = parsedThemeRules[i];\n    root.insert(0, rule.scope, rule.parentScopes, rule.fontStyle, colorMap.getId(rule.foreground), colorMap.getId(rule.background));\n  }\n  return new Theme(colorMap, defaults, root);\n}\nvar ColorMap = class {\n  _isFrozen;\n  _lastColorId;\n  _id2color;\n  _color2id;\n  constructor(_colorMap) {\n    this._lastColorId = 0;\n    this._id2color = [];\n    this._color2id = /* @__PURE__ */ Object.create(null);\n    if (Array.isArray(_colorMap)) {\n      this._isFrozen = true;\n      for (let i = 0, len = _colorMap.length; i < len; i++) {\n        this._color2id[_colorMap[i]] = i;\n        this._id2color[i] = _colorMap[i];\n      }\n    } else {\n      this._isFrozen = false;\n    }\n  }\n  getId(color) {\n    if (color === null) {\n      return 0;\n    }\n    color = color.toUpperCase();\n    let value = this._color2id[color];\n    if (value) {\n      return value;\n    }\n    if (this._isFrozen) {\n      throw new Error(`Missing color in color map - ${color}`);\n    }\n    value = ++this._lastColorId;\n    this._color2id[color] = value;\n    this._id2color[value] = color;\n    return value;\n  }\n  getColorMap() {\n    return this._id2color.slice(0);\n  }\n};\nvar emptyParentScopes = Object.freeze([]);\nvar ThemeTrieElementRule = class _ThemeTrieElementRule {\n  scopeDepth;\n  parentScopes;\n  fontStyle;\n  foreground;\n  background;\n  constructor(scopeDepth, parentScopes, fontStyle, foreground, background) {\n    this.scopeDepth = scopeDepth;\n    this.parentScopes = parentScopes || emptyParentScopes;\n    this.fontStyle = fontStyle;\n    this.foreground = foreground;\n    this.background = background;\n  }\n  clone() {\n    return new _ThemeTrieElementRule(this.scopeDepth, this.parentScopes, this.fontStyle, this.foreground, this.background);\n  }\n  static cloneArr(arr) {\n    let r = [];\n    for (let i = 0, len = arr.length; i < len; i++) {\n      r[i] = arr[i].clone();\n    }\n    return r;\n  }\n  acceptOverwrite(scopeDepth, fontStyle, foreground, background) {\n    if (this.scopeDepth > scopeDepth) {\n      console.log(\"how did this happen?\");\n    } else {\n      this.scopeDepth = scopeDepth;\n    }\n    if (fontStyle !== -1 /* NotSet */) {\n      this.fontStyle = fontStyle;\n    }\n    if (foreground !== 0) {\n      this.foreground = foreground;\n    }\n    if (background !== 0) {\n      this.background = background;\n    }\n  }\n};\nvar ThemeTrieElement = class _ThemeTrieElement {\n  constructor(_mainRule, rulesWithParentScopes = [], _children = {}) {\n    this._mainRule = _mainRule;\n    this._children = _children;\n    this._rulesWithParentScopes = rulesWithParentScopes;\n  }\n  _rulesWithParentScopes;\n  static _cmpBySpecificity(a, b) {\n    if (a.scopeDepth !== b.scopeDepth) {\n      return b.scopeDepth - a.scopeDepth;\n    }\n    let aParentIndex = 0;\n    let bParentIndex = 0;\n    while (true) {\n      if (a.parentScopes[aParentIndex] === \">\") {\n        aParentIndex++;\n      }\n      if (b.parentScopes[bParentIndex] === \">\") {\n        bParentIndex++;\n      }\n      if (aParentIndex >= a.parentScopes.length || bParentIndex >= b.parentScopes.length) {\n        break;\n      }\n      const parentScopeLengthDiff = b.parentScopes[bParentIndex].length - a.parentScopes[aParentIndex].length;\n      if (parentScopeLengthDiff !== 0) {\n        return parentScopeLengthDiff;\n      }\n      aParentIndex++;\n      bParentIndex++;\n    }\n    return b.parentScopes.length - a.parentScopes.length;\n  }\n  match(scope) {\n    if (scope !== \"\") {\n      let dotIndex = scope.indexOf(\".\");\n      let head;\n      let tail;\n      if (dotIndex === -1) {\n        head = scope;\n        tail = \"\";\n      } else {\n        head = scope.substring(0, dotIndex);\n        tail = scope.substring(dotIndex + 1);\n      }\n      if (this._children.hasOwnProperty(head)) {\n        return this._children[head].match(tail);\n      }\n    }\n    const rules = this._rulesWithParentScopes.concat(this._mainRule);\n    rules.sort(_ThemeTrieElement._cmpBySpecificity);\n    return rules;\n  }\n  insert(scopeDepth, scope, parentScopes, fontStyle, foreground, background) {\n    if (scope === \"\") {\n      this._doInsertHere(scopeDepth, parentScopes, fontStyle, foreground, background);\n      return;\n    }\n    let dotIndex = scope.indexOf(\".\");\n    let head;\n    let tail;\n    if (dotIndex === -1) {\n      head = scope;\n      tail = \"\";\n    } else {\n      head = scope.substring(0, dotIndex);\n      tail = scope.substring(dotIndex + 1);\n    }\n    let child;\n    if (this._children.hasOwnProperty(head)) {\n      child = this._children[head];\n    } else {\n      child = new _ThemeTrieElement(this._mainRule.clone(), ThemeTrieElementRule.cloneArr(this._rulesWithParentScopes));\n      this._children[head] = child;\n    }\n    child.insert(scopeDepth + 1, tail, parentScopes, fontStyle, foreground, background);\n  }\n  _doInsertHere(scopeDepth, parentScopes, fontStyle, foreground, background) {\n    if (parentScopes === null) {\n      this._mainRule.acceptOverwrite(scopeDepth, fontStyle, foreground, background);\n      return;\n    }\n    for (let i = 0, len = this._rulesWithParentScopes.length; i < len; i++) {\n      let rule = this._rulesWithParentScopes[i];\n      if (strArrCmp(rule.parentScopes, parentScopes) === 0) {\n        rule.acceptOverwrite(scopeDepth, fontStyle, foreground, background);\n        return;\n      }\n    }\n    if (fontStyle === -1 /* NotSet */) {\n      fontStyle = this._mainRule.fontStyle;\n    }\n    if (foreground === 0) {\n      foreground = this._mainRule.foreground;\n    }\n    if (background === 0) {\n      background = this._mainRule.background;\n    }\n    this._rulesWithParentScopes.push(new ThemeTrieElementRule(scopeDepth, parentScopes, fontStyle, foreground, background));\n  }\n};\n\n// src/encodedTokenAttributes.ts\nvar EncodedTokenMetadata = class _EncodedTokenMetadata {\n  static toBinaryStr(encodedTokenAttributes) {\n    return encodedTokenAttributes.toString(2).padStart(32, \"0\");\n  }\n  static print(encodedTokenAttributes) {\n    const languageId = _EncodedTokenMetadata.getLanguageId(encodedTokenAttributes);\n    const tokenType = _EncodedTokenMetadata.getTokenType(encodedTokenAttributes);\n    const fontStyle = _EncodedTokenMetadata.getFontStyle(encodedTokenAttributes);\n    const foreground = _EncodedTokenMetadata.getForeground(encodedTokenAttributes);\n    const background = _EncodedTokenMetadata.getBackground(encodedTokenAttributes);\n    console.log({\n      languageId,\n      tokenType,\n      fontStyle,\n      foreground,\n      background\n    });\n  }\n  static getLanguageId(encodedTokenAttributes) {\n    return (encodedTokenAttributes & 255 /* LANGUAGEID_MASK */) >>> 0 /* LANGUAGEID_OFFSET */;\n  }\n  static getTokenType(encodedTokenAttributes) {\n    return (encodedTokenAttributes & 768 /* TOKEN_TYPE_MASK */) >>> 8 /* TOKEN_TYPE_OFFSET */;\n  }\n  static containsBalancedBrackets(encodedTokenAttributes) {\n    return (encodedTokenAttributes & 1024 /* BALANCED_BRACKETS_MASK */) !== 0;\n  }\n  static getFontStyle(encodedTokenAttributes) {\n    return (encodedTokenAttributes & 30720 /* FONT_STYLE_MASK */) >>> 11 /* FONT_STYLE_OFFSET */;\n  }\n  static getForeground(encodedTokenAttributes) {\n    return (encodedTokenAttributes & 16744448 /* FOREGROUND_MASK */) >>> 15 /* FOREGROUND_OFFSET */;\n  }\n  static getBackground(encodedTokenAttributes) {\n    return (encodedTokenAttributes & 4278190080 /* BACKGROUND_MASK */) >>> 24 /* BACKGROUND_OFFSET */;\n  }\n  /**\n   * Updates the fields in `metadata`.\n   * A value of `0`, `NotSet` or `null` indicates that the corresponding field should be left as is.\n   */\n  static set(encodedTokenAttributes, languageId, tokenType, containsBalancedBrackets, fontStyle, foreground, background) {\n    let _languageId = _EncodedTokenMetadata.getLanguageId(encodedTokenAttributes);\n    let _tokenType = _EncodedTokenMetadata.getTokenType(encodedTokenAttributes);\n    let _containsBalancedBracketsBit = _EncodedTokenMetadata.containsBalancedBrackets(encodedTokenAttributes) ? 1 : 0;\n    let _fontStyle = _EncodedTokenMetadata.getFontStyle(encodedTokenAttributes);\n    let _foreground = _EncodedTokenMetadata.getForeground(encodedTokenAttributes);\n    let _background = _EncodedTokenMetadata.getBackground(encodedTokenAttributes);\n    if (languageId !== 0) {\n      _languageId = languageId;\n    }\n    if (tokenType !== 8 /* NotSet */) {\n      _tokenType = fromOptionalTokenType(tokenType);\n    }\n    if (containsBalancedBrackets !== null) {\n      _containsBalancedBracketsBit = containsBalancedBrackets ? 1 : 0;\n    }\n    if (fontStyle !== -1 /* NotSet */) {\n      _fontStyle = fontStyle;\n    }\n    if (foreground !== 0) {\n      _foreground = foreground;\n    }\n    if (background !== 0) {\n      _background = background;\n    }\n    return (_languageId << 0 /* LANGUAGEID_OFFSET */ | _tokenType << 8 /* TOKEN_TYPE_OFFSET */ | _containsBalancedBracketsBit << 10 /* BALANCED_BRACKETS_OFFSET */ | _fontStyle << 11 /* FONT_STYLE_OFFSET */ | _foreground << 15 /* FOREGROUND_OFFSET */ | _background << 24 /* BACKGROUND_OFFSET */) >>> 0;\n  }\n};\nfunction toOptionalTokenType(standardType) {\n  return standardType;\n}\nfunction fromOptionalTokenType(standardType) {\n  return standardType;\n}\n\n// src/matcher.ts\nfunction createMatchers(selector, matchesName) {\n  const results = [];\n  const tokenizer = newTokenizer(selector);\n  let token = tokenizer.next();\n  while (token !== null) {\n    let priority = 0;\n    if (token.length === 2 && token.charAt(1) === \":\") {\n      switch (token.charAt(0)) {\n        case \"R\":\n          priority = 1;\n          break;\n        case \"L\":\n          priority = -1;\n          break;\n        default:\n          console.log(`Unknown priority ${token} in scope selector`);\n      }\n      token = tokenizer.next();\n    }\n    let matcher = parseConjunction();\n    results.push({ matcher, priority });\n    if (token !== \",\") {\n      break;\n    }\n    token = tokenizer.next();\n  }\n  return results;\n  function parseOperand() {\n    if (token === \"-\") {\n      token = tokenizer.next();\n      const expressionToNegate = parseOperand();\n      return (matcherInput) => !!expressionToNegate && !expressionToNegate(matcherInput);\n    }\n    if (token === \"(\") {\n      token = tokenizer.next();\n      const expressionInParents = parseInnerExpression();\n      if (token === \")\") {\n        token = tokenizer.next();\n      }\n      return expressionInParents;\n    }\n    if (isIdentifier(token)) {\n      const identifiers = [];\n      do {\n        identifiers.push(token);\n        token = tokenizer.next();\n      } while (isIdentifier(token));\n      return (matcherInput) => matchesName(identifiers, matcherInput);\n    }\n    return null;\n  }\n  function parseConjunction() {\n    const matchers = [];\n    let matcher = parseOperand();\n    while (matcher) {\n      matchers.push(matcher);\n      matcher = parseOperand();\n    }\n    return (matcherInput) => matchers.every((matcher2) => matcher2(matcherInput));\n  }\n  function parseInnerExpression() {\n    const matchers = [];\n    let matcher = parseConjunction();\n    while (matcher) {\n      matchers.push(matcher);\n      if (token === \"|\" || token === \",\") {\n        do {\n          token = tokenizer.next();\n        } while (token === \"|\" || token === \",\");\n      } else {\n        break;\n      }\n      matcher = parseConjunction();\n    }\n    return (matcherInput) => matchers.some((matcher2) => matcher2(matcherInput));\n  }\n}\nfunction isIdentifier(token) {\n  return !!token && !!token.match(/[\\w\\.:]+/);\n}\nfunction newTokenizer(input) {\n  let regex = /([LR]:|[\\w\\.:][\\w\\.:\\-]*|[\\,\\|\\-\\(\\)])/g;\n  let match = regex.exec(input);\n  return {\n    next: () => {\n      if (!match) {\n        return null;\n      }\n      const res = match[0];\n      match = regex.exec(input);\n      return res;\n    }\n  };\n}\n\n// src/onigLib.ts\nvar FindOption = /* @__PURE__ */ ((FindOption2) => {\n  FindOption2[FindOption2[\"None\"] = 0] = \"None\";\n  FindOption2[FindOption2[\"NotBeginString\"] = 1] = \"NotBeginString\";\n  FindOption2[FindOption2[\"NotEndString\"] = 2] = \"NotEndString\";\n  FindOption2[FindOption2[\"NotBeginPosition\"] = 4] = \"NotBeginPosition\";\n  FindOption2[FindOption2[\"DebugCall\"] = 8] = \"DebugCall\";\n  return FindOption2;\n})(FindOption || {});\nfunction disposeOnigString(str) {\n  if (typeof str.dispose === \"function\") {\n    str.dispose();\n  }\n}\n\n// src/grammar/grammarDependencies.ts\nvar TopLevelRuleReference = class {\n  constructor(scopeName) {\n    this.scopeName = scopeName;\n  }\n  toKey() {\n    return this.scopeName;\n  }\n};\nvar TopLevelRepositoryRuleReference = class {\n  constructor(scopeName, ruleName) {\n    this.scopeName = scopeName;\n    this.ruleName = ruleName;\n  }\n  toKey() {\n    return `${this.scopeName}#${this.ruleName}`;\n  }\n};\nvar ExternalReferenceCollector = class {\n  _references = [];\n  _seenReferenceKeys = /* @__PURE__ */ new Set();\n  get references() {\n    return this._references;\n  }\n  visitedRule = /* @__PURE__ */ new Set();\n  add(reference) {\n    const key = reference.toKey();\n    if (this._seenReferenceKeys.has(key)) {\n      return;\n    }\n    this._seenReferenceKeys.add(key);\n    this._references.push(reference);\n  }\n};\nvar ScopeDependencyProcessor = class {\n  constructor(repo, initialScopeName) {\n    this.repo = repo;\n    this.initialScopeName = initialScopeName;\n    this.seenFullScopeRequests.add(this.initialScopeName);\n    this.Q = [new TopLevelRuleReference(this.initialScopeName)];\n  }\n  seenFullScopeRequests = /* @__PURE__ */ new Set();\n  seenPartialScopeRequests = /* @__PURE__ */ new Set();\n  Q;\n  processQueue() {\n    const q = this.Q;\n    this.Q = [];\n    const deps = new ExternalReferenceCollector();\n    for (const dep of q) {\n      collectReferencesOfReference(dep, this.initialScopeName, this.repo, deps);\n    }\n    for (const dep of deps.references) {\n      if (dep instanceof TopLevelRuleReference) {\n        if (this.seenFullScopeRequests.has(dep.scopeName)) {\n          continue;\n        }\n        this.seenFullScopeRequests.add(dep.scopeName);\n        this.Q.push(dep);\n      } else {\n        if (this.seenFullScopeRequests.has(dep.scopeName)) {\n          continue;\n        }\n        if (this.seenPartialScopeRequests.has(dep.toKey())) {\n          continue;\n        }\n        this.seenPartialScopeRequests.add(dep.toKey());\n        this.Q.push(dep);\n      }\n    }\n  }\n};\nfunction collectReferencesOfReference(reference, baseGrammarScopeName, repo, result) {\n  const selfGrammar = repo.lookup(reference.scopeName);\n  if (!selfGrammar) {\n    if (reference.scopeName === baseGrammarScopeName) {\n      throw new Error(`No grammar provided for <${baseGrammarScopeName}>`);\n    }\n    return;\n  }\n  const baseGrammar = repo.lookup(baseGrammarScopeName);\n  if (reference instanceof TopLevelRuleReference) {\n    collectExternalReferencesInTopLevelRule({ baseGrammar, selfGrammar }, result);\n  } else {\n    collectExternalReferencesInTopLevelRepositoryRule(\n      reference.ruleName,\n      { baseGrammar, selfGrammar, repository: selfGrammar.repository },\n      result\n    );\n  }\n  const injections = repo.injections(reference.scopeName);\n  if (injections) {\n    for (const injection of injections) {\n      result.add(new TopLevelRuleReference(injection));\n    }\n  }\n}\nfunction collectExternalReferencesInTopLevelRepositoryRule(ruleName, context, result) {\n  if (context.repository && context.repository[ruleName]) {\n    const rule = context.repository[ruleName];\n    collectExternalReferencesInRules([rule], context, result);\n  }\n}\nfunction collectExternalReferencesInTopLevelRule(context, result) {\n  if (context.selfGrammar.patterns && Array.isArray(context.selfGrammar.patterns)) {\n    collectExternalReferencesInRules(\n      context.selfGrammar.patterns,\n      { ...context, repository: context.selfGrammar.repository },\n      result\n    );\n  }\n  if (context.selfGrammar.injections) {\n    collectExternalReferencesInRules(\n      Object.values(context.selfGrammar.injections),\n      { ...context, repository: context.selfGrammar.repository },\n      result\n    );\n  }\n}\nfunction collectExternalReferencesInRules(rules, context, result) {\n  for (const rule of rules) {\n    if (result.visitedRule.has(rule)) {\n      continue;\n    }\n    result.visitedRule.add(rule);\n    const patternRepository = rule.repository ? mergeObjects({}, context.repository, rule.repository) : context.repository;\n    if (Array.isArray(rule.patterns)) {\n      collectExternalReferencesInRules(rule.patterns, { ...context, repository: patternRepository }, result);\n    }\n    const include = rule.include;\n    if (!include) {\n      continue;\n    }\n    const reference = parseInclude(include);\n    switch (reference.kind) {\n      case 0 /* Base */:\n        collectExternalReferencesInTopLevelRule({ ...context, selfGrammar: context.baseGrammar }, result);\n        break;\n      case 1 /* Self */:\n        collectExternalReferencesInTopLevelRule(context, result);\n        break;\n      case 2 /* RelativeReference */:\n        collectExternalReferencesInTopLevelRepositoryRule(reference.ruleName, { ...context, repository: patternRepository }, result);\n        break;\n      case 3 /* TopLevelReference */:\n      case 4 /* TopLevelRepositoryReference */:\n        const selfGrammar = reference.scopeName === context.selfGrammar.scopeName ? context.selfGrammar : reference.scopeName === context.baseGrammar.scopeName ? context.baseGrammar : void 0;\n        if (selfGrammar) {\n          const newContext = { baseGrammar: context.baseGrammar, selfGrammar, repository: patternRepository };\n          if (reference.kind === 4 /* TopLevelRepositoryReference */) {\n            collectExternalReferencesInTopLevelRepositoryRule(reference.ruleName, newContext, result);\n          } else {\n            collectExternalReferencesInTopLevelRule(newContext, result);\n          }\n        } else {\n          if (reference.kind === 4 /* TopLevelRepositoryReference */) {\n            result.add(new TopLevelRepositoryRuleReference(reference.scopeName, reference.ruleName));\n          } else {\n            result.add(new TopLevelRuleReference(reference.scopeName));\n          }\n        }\n        break;\n    }\n  }\n}\nvar BaseReference = class {\n  kind = 0 /* Base */;\n};\nvar SelfReference = class {\n  kind = 1 /* Self */;\n};\nvar RelativeReference = class {\n  constructor(ruleName) {\n    this.ruleName = ruleName;\n  }\n  kind = 2 /* RelativeReference */;\n};\nvar TopLevelReference = class {\n  constructor(scopeName) {\n    this.scopeName = scopeName;\n  }\n  kind = 3 /* TopLevelReference */;\n};\nvar TopLevelRepositoryReference = class {\n  constructor(scopeName, ruleName) {\n    this.scopeName = scopeName;\n    this.ruleName = ruleName;\n  }\n  kind = 4 /* TopLevelRepositoryReference */;\n};\nfunction parseInclude(include) {\n  if (include === \"$base\") {\n    return new BaseReference();\n  } else if (include === \"$self\") {\n    return new SelfReference();\n  }\n  const indexOfSharp = include.indexOf(\"#\");\n  if (indexOfSharp === -1) {\n    return new TopLevelReference(include);\n  } else if (indexOfSharp === 0) {\n    return new RelativeReference(include.substring(1));\n  } else {\n    const scopeName = include.substring(0, indexOfSharp);\n    const ruleName = include.substring(indexOfSharp + 1);\n    return new TopLevelRepositoryReference(scopeName, ruleName);\n  }\n}\n\n// src/rule.ts\nvar HAS_BACK_REFERENCES = /\\\\(\\d+)/;\nvar BACK_REFERENCING_END = /\\\\(\\d+)/g;\nvar ruleIdSymbol = Symbol(\"RuleId\");\nvar endRuleId = -1;\nvar whileRuleId = -2;\nfunction ruleIdFromNumber(id) {\n  return id;\n}\nfunction ruleIdToNumber(id) {\n  return id;\n}\nvar Rule = class {\n  $location;\n  id;\n  _nameIsCapturing;\n  _name;\n  _contentNameIsCapturing;\n  _contentName;\n  constructor($location, id, name, contentName) {\n    this.$location = $location;\n    this.id = id;\n    this._name = name || null;\n    this._nameIsCapturing = RegexSource.hasCaptures(this._name);\n    this._contentName = contentName || null;\n    this._contentNameIsCapturing = RegexSource.hasCaptures(this._contentName);\n  }\n  get debugName() {\n    const location = this.$location ? `${basename(this.$location.filename)}:${this.$location.line}` : \"unknown\";\n    return `${this.constructor.name}#${this.id} @ ${location}`;\n  }\n  getName(lineText, captureIndices) {\n    if (!this._nameIsCapturing || this._name === null || lineText === null || captureIndices === null) {\n      return this._name;\n    }\n    return RegexSource.replaceCaptures(this._name, lineText, captureIndices);\n  }\n  getContentName(lineText, captureIndices) {\n    if (!this._contentNameIsCapturing || this._contentName === null) {\n      return this._contentName;\n    }\n    return RegexSource.replaceCaptures(this._contentName, lineText, captureIndices);\n  }\n};\nvar CaptureRule = class extends Rule {\n  retokenizeCapturedWithRuleId;\n  constructor($location, id, name, contentName, retokenizeCapturedWithRuleId) {\n    super($location, id, name, contentName);\n    this.retokenizeCapturedWithRuleId = retokenizeCapturedWithRuleId;\n  }\n  dispose() {\n  }\n  collectPatterns(grammar, out) {\n    throw new Error(\"Not supported!\");\n  }\n  compile(grammar, endRegexSource) {\n    throw new Error(\"Not supported!\");\n  }\n  compileAG(grammar, endRegexSource, allowA, allowG) {\n    throw new Error(\"Not supported!\");\n  }\n};\nvar MatchRule = class extends Rule {\n  _match;\n  captures;\n  _cachedCompiledPatterns;\n  constructor($location, id, name, match, captures) {\n    super($location, id, name, null);\n    this._match = new RegExpSource(match, this.id);\n    this.captures = captures;\n    this._cachedCompiledPatterns = null;\n  }\n  dispose() {\n    if (this._cachedCompiledPatterns) {\n      this._cachedCompiledPatterns.dispose();\n      this._cachedCompiledPatterns = null;\n    }\n  }\n  get debugMatchRegExp() {\n    return `${this._match.source}`;\n  }\n  collectPatterns(grammar, out) {\n    out.push(this._match);\n  }\n  compile(grammar, endRegexSource) {\n    return this._getCachedCompiledPatterns(grammar).compile(grammar);\n  }\n  compileAG(grammar, endRegexSource, allowA, allowG) {\n    return this._getCachedCompiledPatterns(grammar).compileAG(grammar, allowA, allowG);\n  }\n  _getCachedCompiledPatterns(grammar) {\n    if (!this._cachedCompiledPatterns) {\n      this._cachedCompiledPatterns = new RegExpSourceList();\n      this.collectPatterns(grammar, this._cachedCompiledPatterns);\n    }\n    return this._cachedCompiledPatterns;\n  }\n};\nvar IncludeOnlyRule = class extends Rule {\n  hasMissingPatterns;\n  patterns;\n  _cachedCompiledPatterns;\n  constructor($location, id, name, contentName, patterns) {\n    super($location, id, name, contentName);\n    this.patterns = patterns.patterns;\n    this.hasMissingPatterns = patterns.hasMissingPatterns;\n    this._cachedCompiledPatterns = null;\n  }\n  dispose() {\n    if (this._cachedCompiledPatterns) {\n      this._cachedCompiledPatterns.dispose();\n      this._cachedCompiledPatterns = null;\n    }\n  }\n  collectPatterns(grammar, out) {\n    for (const pattern of this.patterns) {\n      const rule = grammar.getRule(pattern);\n      rule.collectPatterns(grammar, out);\n    }\n  }\n  compile(grammar, endRegexSource) {\n    return this._getCachedCompiledPatterns(grammar).compile(grammar);\n  }\n  compileAG(grammar, endRegexSource, allowA, allowG) {\n    return this._getCachedCompiledPatterns(grammar).compileAG(grammar, allowA, allowG);\n  }\n  _getCachedCompiledPatterns(grammar) {\n    if (!this._cachedCompiledPatterns) {\n      this._cachedCompiledPatterns = new RegExpSourceList();\n      this.collectPatterns(grammar, this._cachedCompiledPatterns);\n    }\n    return this._cachedCompiledPatterns;\n  }\n};\nvar BeginEndRule = class extends Rule {\n  _begin;\n  beginCaptures;\n  _end;\n  endHasBackReferences;\n  endCaptures;\n  applyEndPatternLast;\n  hasMissingPatterns;\n  patterns;\n  _cachedCompiledPatterns;\n  constructor($location, id, name, contentName, begin, beginCaptures, end, endCaptures, applyEndPatternLast, patterns) {\n    super($location, id, name, contentName);\n    this._begin = new RegExpSource(begin, this.id);\n    this.beginCaptures = beginCaptures;\n    this._end = new RegExpSource(end ? end : \"\\uFFFF\", -1);\n    this.endHasBackReferences = this._end.hasBackReferences;\n    this.endCaptures = endCaptures;\n    this.applyEndPatternLast = applyEndPatternLast || false;\n    this.patterns = patterns.patterns;\n    this.hasMissingPatterns = patterns.hasMissingPatterns;\n    this._cachedCompiledPatterns = null;\n  }\n  dispose() {\n    if (this._cachedCompiledPatterns) {\n      this._cachedCompiledPatterns.dispose();\n      this._cachedCompiledPatterns = null;\n    }\n  }\n  get debugBeginRegExp() {\n    return `${this._begin.source}`;\n  }\n  get debugEndRegExp() {\n    return `${this._end.source}`;\n  }\n  getEndWithResolvedBackReferences(lineText, captureIndices) {\n    return this._end.resolveBackReferences(lineText, captureIndices);\n  }\n  collectPatterns(grammar, out) {\n    out.push(this._begin);\n  }\n  compile(grammar, endRegexSource) {\n    return this._getCachedCompiledPatterns(grammar, endRegexSource).compile(grammar);\n  }\n  compileAG(grammar, endRegexSource, allowA, allowG) {\n    return this._getCachedCompiledPatterns(grammar, endRegexSource).compileAG(grammar, allowA, allowG);\n  }\n  _getCachedCompiledPatterns(grammar, endRegexSource) {\n    if (!this._cachedCompiledPatterns) {\n      this._cachedCompiledPatterns = new RegExpSourceList();\n      for (const pattern of this.patterns) {\n        const rule = grammar.getRule(pattern);\n        rule.collectPatterns(grammar, this._cachedCompiledPatterns);\n      }\n      if (this.applyEndPatternLast) {\n        this._cachedCompiledPatterns.push(this._end.hasBackReferences ? this._end.clone() : this._end);\n      } else {\n        this._cachedCompiledPatterns.unshift(this._end.hasBackReferences ? this._end.clone() : this._end);\n      }\n    }\n    if (this._end.hasBackReferences) {\n      if (this.applyEndPatternLast) {\n        this._cachedCompiledPatterns.setSource(this._cachedCompiledPatterns.length() - 1, endRegexSource);\n      } else {\n        this._cachedCompiledPatterns.setSource(0, endRegexSource);\n      }\n    }\n    return this._cachedCompiledPatterns;\n  }\n};\nvar BeginWhileRule = class extends Rule {\n  _begin;\n  beginCaptures;\n  whileCaptures;\n  _while;\n  whileHasBackReferences;\n  hasMissingPatterns;\n  patterns;\n  _cachedCompiledPatterns;\n  _cachedCompiledWhilePatterns;\n  constructor($location, id, name, contentName, begin, beginCaptures, _while, whileCaptures, patterns) {\n    super($location, id, name, contentName);\n    this._begin = new RegExpSource(begin, this.id);\n    this.beginCaptures = beginCaptures;\n    this.whileCaptures = whileCaptures;\n    this._while = new RegExpSource(_while, whileRuleId);\n    this.whileHasBackReferences = this._while.hasBackReferences;\n    this.patterns = patterns.patterns;\n    this.hasMissingPatterns = patterns.hasMissingPatterns;\n    this._cachedCompiledPatterns = null;\n    this._cachedCompiledWhilePatterns = null;\n  }\n  dispose() {\n    if (this._cachedCompiledPatterns) {\n      this._cachedCompiledPatterns.dispose();\n      this._cachedCompiledPatterns = null;\n    }\n    if (this._cachedCompiledWhilePatterns) {\n      this._cachedCompiledWhilePatterns.dispose();\n      this._cachedCompiledWhilePatterns = null;\n    }\n  }\n  get debugBeginRegExp() {\n    return `${this._begin.source}`;\n  }\n  get debugWhileRegExp() {\n    return `${this._while.source}`;\n  }\n  getWhileWithResolvedBackReferences(lineText, captureIndices) {\n    return this._while.resolveBackReferences(lineText, captureIndices);\n  }\n  collectPatterns(grammar, out) {\n    out.push(this._begin);\n  }\n  compile(grammar, endRegexSource) {\n    return this._getCachedCompiledPatterns(grammar).compile(grammar);\n  }\n  compileAG(grammar, endRegexSource, allowA, allowG) {\n    return this._getCachedCompiledPatterns(grammar).compileAG(grammar, allowA, allowG);\n  }\n  _getCachedCompiledPatterns(grammar) {\n    if (!this._cachedCompiledPatterns) {\n      this._cachedCompiledPatterns = new RegExpSourceList();\n      for (const pattern of this.patterns) {\n        const rule = grammar.getRule(pattern);\n        rule.collectPatterns(grammar, this._cachedCompiledPatterns);\n      }\n    }\n    return this._cachedCompiledPatterns;\n  }\n  compileWhile(grammar, endRegexSource) {\n    return this._getCachedCompiledWhilePatterns(grammar, endRegexSource).compile(grammar);\n  }\n  compileWhileAG(grammar, endRegexSource, allowA, allowG) {\n    return this._getCachedCompiledWhilePatterns(grammar, endRegexSource).compileAG(grammar, allowA, allowG);\n  }\n  _getCachedCompiledWhilePatterns(grammar, endRegexSource) {\n    if (!this._cachedCompiledWhilePatterns) {\n      this._cachedCompiledWhilePatterns = new RegExpSourceList();\n      this._cachedCompiledWhilePatterns.push(this._while.hasBackReferences ? this._while.clone() : this._while);\n    }\n    if (this._while.hasBackReferences) {\n      this._cachedCompiledWhilePatterns.setSource(0, endRegexSource ? endRegexSource : \"\\uFFFF\");\n    }\n    return this._cachedCompiledWhilePatterns;\n  }\n};\nvar RuleFactory = class _RuleFactory {\n  static createCaptureRule(helper, $location, name, contentName, retokenizeCapturedWithRuleId) {\n    return helper.registerRule((id) => {\n      return new CaptureRule($location, id, name, contentName, retokenizeCapturedWithRuleId);\n    });\n  }\n  static getCompiledRuleId(desc, helper, repository) {\n    if (!desc.id) {\n      helper.registerRule((id) => {\n        desc.id = id;\n        if (desc.match) {\n          return new MatchRule(\n            desc.$vscodeTextmateLocation,\n            desc.id,\n            desc.name,\n            desc.match,\n            _RuleFactory._compileCaptures(desc.captures, helper, repository)\n          );\n        }\n        if (typeof desc.begin === \"undefined\") {\n          if (desc.repository) {\n            repository = mergeObjects({}, repository, desc.repository);\n          }\n          let patterns = desc.patterns;\n          if (typeof patterns === \"undefined\" && desc.include) {\n            patterns = [{ include: desc.include }];\n          }\n          return new IncludeOnlyRule(\n            desc.$vscodeTextmateLocation,\n            desc.id,\n            desc.name,\n            desc.contentName,\n            _RuleFactory._compilePatterns(patterns, helper, repository)\n          );\n        }\n        if (desc.while) {\n          return new BeginWhileRule(\n            desc.$vscodeTextmateLocation,\n            desc.id,\n            desc.name,\n            desc.contentName,\n            desc.begin,\n            _RuleFactory._compileCaptures(desc.beginCaptures || desc.captures, helper, repository),\n            desc.while,\n            _RuleFactory._compileCaptures(desc.whileCaptures || desc.captures, helper, repository),\n            _RuleFactory._compilePatterns(desc.patterns, helper, repository)\n          );\n        }\n        return new BeginEndRule(\n          desc.$vscodeTextmateLocation,\n          desc.id,\n          desc.name,\n          desc.contentName,\n          desc.begin,\n          _RuleFactory._compileCaptures(desc.beginCaptures || desc.captures, helper, repository),\n          desc.end,\n          _RuleFactory._compileCaptures(desc.endCaptures || desc.captures, helper, repository),\n          desc.applyEndPatternLast,\n          _RuleFactory._compilePatterns(desc.patterns, helper, repository)\n        );\n      });\n    }\n    return desc.id;\n  }\n  static _compileCaptures(captures, helper, repository) {\n    let r = [];\n    if (captures) {\n      let maximumCaptureId = 0;\n      for (const captureId in captures) {\n        if (captureId === \"$vscodeTextmateLocation\") {\n          continue;\n        }\n        const numericCaptureId = parseInt(captureId, 10);\n        if (numericCaptureId > maximumCaptureId) {\n          maximumCaptureId = numericCaptureId;\n        }\n      }\n      for (let i = 0; i <= maximumCaptureId; i++) {\n        r[i] = null;\n      }\n      for (const captureId in captures) {\n        if (captureId === \"$vscodeTextmateLocation\") {\n          continue;\n        }\n        const numericCaptureId = parseInt(captureId, 10);\n        let retokenizeCapturedWithRuleId = 0;\n        if (captures[captureId].patterns) {\n          retokenizeCapturedWithRuleId = _RuleFactory.getCompiledRuleId(captures[captureId], helper, repository);\n        }\n        r[numericCaptureId] = _RuleFactory.createCaptureRule(helper, captures[captureId].$vscodeTextmateLocation, captures[captureId].name, captures[captureId].contentName, retokenizeCapturedWithRuleId);\n      }\n    }\n    return r;\n  }\n  static _compilePatterns(patterns, helper, repository) {\n    let r = [];\n    if (patterns) {\n      for (let i = 0, len = patterns.length; i < len; i++) {\n        const pattern = patterns[i];\n        let ruleId = -1;\n        if (pattern.include) {\n          const reference = parseInclude(pattern.include);\n          switch (reference.kind) {\n            case 0 /* Base */:\n            case 1 /* Self */:\n              ruleId = _RuleFactory.getCompiledRuleId(repository[pattern.include], helper, repository);\n              break;\n            case 2 /* RelativeReference */:\n              let localIncludedRule = repository[reference.ruleName];\n              if (localIncludedRule) {\n                ruleId = _RuleFactory.getCompiledRuleId(localIncludedRule, helper, repository);\n              } else {\n              }\n              break;\n            case 3 /* TopLevelReference */:\n            case 4 /* TopLevelRepositoryReference */:\n              const externalGrammarName = reference.scopeName;\n              const externalGrammarInclude = reference.kind === 4 /* TopLevelRepositoryReference */ ? reference.ruleName : null;\n              const externalGrammar = helper.getExternalGrammar(externalGrammarName, repository);\n              if (externalGrammar) {\n                if (externalGrammarInclude) {\n                  let externalIncludedRule = externalGrammar.repository[externalGrammarInclude];\n                  if (externalIncludedRule) {\n                    ruleId = _RuleFactory.getCompiledRuleId(externalIncludedRule, helper, externalGrammar.repository);\n                  } else {\n                  }\n                } else {\n                  ruleId = _RuleFactory.getCompiledRuleId(externalGrammar.repository.$self, helper, externalGrammar.repository);\n                }\n              } else {\n              }\n              break;\n          }\n        } else {\n          ruleId = _RuleFactory.getCompiledRuleId(pattern, helper, repository);\n        }\n        if (ruleId !== -1) {\n          const rule = helper.getRule(ruleId);\n          let skipRule = false;\n          if (rule instanceof IncludeOnlyRule || rule instanceof BeginEndRule || rule instanceof BeginWhileRule) {\n            if (rule.hasMissingPatterns && rule.patterns.length === 0) {\n              skipRule = true;\n            }\n          }\n          if (skipRule) {\n            continue;\n          }\n          r.push(ruleId);\n        }\n      }\n    }\n    return {\n      patterns: r,\n      hasMissingPatterns: (patterns ? patterns.length : 0) !== r.length\n    };\n  }\n};\nvar RegExpSource = class _RegExpSource {\n  source;\n  ruleId;\n  hasAnchor;\n  hasBackReferences;\n  _anchorCache;\n  constructor(regExpSource, ruleId) {\n    if (regExpSource && typeof regExpSource === \"string\") {\n      const len = regExpSource.length;\n      let lastPushedPos = 0;\n      let output = [];\n      let hasAnchor = false;\n      for (let pos = 0; pos < len; pos++) {\n        const ch = regExpSource.charAt(pos);\n        if (ch === \"\\\\\") {\n          if (pos + 1 < len) {\n            const nextCh = regExpSource.charAt(pos + 1);\n            if (nextCh === \"z\") {\n              output.push(regExpSource.substring(lastPushedPos, pos));\n              output.push(\"$(?!\\\\n)(?<!\\\\n)\");\n              lastPushedPos = pos + 2;\n            } else if (nextCh === \"A\" || nextCh === \"G\") {\n              hasAnchor = true;\n            }\n            pos++;\n          }\n        }\n      }\n      this.hasAnchor = hasAnchor;\n      if (lastPushedPos === 0) {\n        this.source = regExpSource;\n      } else {\n        output.push(regExpSource.substring(lastPushedPos, len));\n        this.source = output.join(\"\");\n      }\n    } else {\n      this.hasAnchor = false;\n      this.source = regExpSource;\n    }\n    if (this.hasAnchor) {\n      this._anchorCache = this._buildAnchorCache();\n    } else {\n      this._anchorCache = null;\n    }\n    this.ruleId = ruleId;\n    if (typeof this.source === \"string\") {\n      this.hasBackReferences = HAS_BACK_REFERENCES.test(this.source);\n    } else {\n      this.hasBackReferences = false;\n    }\n  }\n  clone() {\n    return new _RegExpSource(this.source, this.ruleId);\n  }\n  setSource(newSource) {\n    if (this.source === newSource) {\n      return;\n    }\n    this.source = newSource;\n    if (this.hasAnchor) {\n      this._anchorCache = this._buildAnchorCache();\n    }\n  }\n  resolveBackReferences(lineText, captureIndices) {\n    if (typeof this.source !== \"string\") {\n      throw new Error(\"This method should only be called if the source is a string\");\n    }\n    let capturedValues = captureIndices.map((capture) => {\n      return lineText.substring(capture.start, capture.end);\n    });\n    BACK_REFERENCING_END.lastIndex = 0;\n    return this.source.replace(BACK_REFERENCING_END, (match, g1) => {\n      return escapeRegExpCharacters(capturedValues[parseInt(g1, 10)] || \"\");\n    });\n  }\n  _buildAnchorCache() {\n    if (typeof this.source !== \"string\") {\n      throw new Error(\"This method should only be called if the source is a string\");\n    }\n    let A0_G0_result = [];\n    let A0_G1_result = [];\n    let A1_G0_result = [];\n    let A1_G1_result = [];\n    let pos, len, ch, nextCh;\n    for (pos = 0, len = this.source.length; pos < len; pos++) {\n      ch = this.source.charAt(pos);\n      A0_G0_result[pos] = ch;\n      A0_G1_result[pos] = ch;\n      A1_G0_result[pos] = ch;\n      A1_G1_result[pos] = ch;\n      if (ch === \"\\\\\") {\n        if (pos + 1 < len) {\n          nextCh = this.source.charAt(pos + 1);\n          if (nextCh === \"A\") {\n            A0_G0_result[pos + 1] = \"\\uFFFF\";\n            A0_G1_result[pos + 1] = \"\\uFFFF\";\n            A1_G0_result[pos + 1] = \"A\";\n            A1_G1_result[pos + 1] = \"A\";\n          } else if (nextCh === \"G\") {\n            A0_G0_result[pos + 1] = \"\\uFFFF\";\n            A0_G1_result[pos + 1] = \"G\";\n            A1_G0_result[pos + 1] = \"\\uFFFF\";\n            A1_G1_result[pos + 1] = \"G\";\n          } else {\n            A0_G0_result[pos + 1] = nextCh;\n            A0_G1_result[pos + 1] = nextCh;\n            A1_G0_result[pos + 1] = nextCh;\n            A1_G1_result[pos + 1] = nextCh;\n          }\n          pos++;\n        }\n      }\n    }\n    return {\n      A0_G0: A0_G0_result.join(\"\"),\n      A0_G1: A0_G1_result.join(\"\"),\n      A1_G0: A1_G0_result.join(\"\"),\n      A1_G1: A1_G1_result.join(\"\")\n    };\n  }\n  resolveAnchors(allowA, allowG) {\n    if (!this.hasAnchor || !this._anchorCache || typeof this.source !== \"string\") {\n      return this.source;\n    }\n    if (allowA) {\n      if (allowG) {\n        return this._anchorCache.A1_G1;\n      } else {\n        return this._anchorCache.A1_G0;\n      }\n    } else {\n      if (allowG) {\n        return this._anchorCache.A0_G1;\n      } else {\n        return this._anchorCache.A0_G0;\n      }\n    }\n  }\n};\nvar RegExpSourceList = class {\n  _items;\n  _hasAnchors;\n  _cached;\n  _anchorCache;\n  constructor() {\n    this._items = [];\n    this._hasAnchors = false;\n    this._cached = null;\n    this._anchorCache = {\n      A0_G0: null,\n      A0_G1: null,\n      A1_G0: null,\n      A1_G1: null\n    };\n  }\n  dispose() {\n    this._disposeCaches();\n  }\n  _disposeCaches() {\n    if (this._cached) {\n      this._cached.dispose();\n      this._cached = null;\n    }\n    if (this._anchorCache.A0_G0) {\n      this._anchorCache.A0_G0.dispose();\n      this._anchorCache.A0_G0 = null;\n    }\n    if (this._anchorCache.A0_G1) {\n      this._anchorCache.A0_G1.dispose();\n      this._anchorCache.A0_G1 = null;\n    }\n    if (this._anchorCache.A1_G0) {\n      this._anchorCache.A1_G0.dispose();\n      this._anchorCache.A1_G0 = null;\n    }\n    if (this._anchorCache.A1_G1) {\n      this._anchorCache.A1_G1.dispose();\n      this._anchorCache.A1_G1 = null;\n    }\n  }\n  push(item) {\n    this._items.push(item);\n    this._hasAnchors = this._hasAnchors || item.hasAnchor;\n  }\n  unshift(item) {\n    this._items.unshift(item);\n    this._hasAnchors = this._hasAnchors || item.hasAnchor;\n  }\n  length() {\n    return this._items.length;\n  }\n  setSource(index, newSource) {\n    if (this._items[index].source !== newSource) {\n      this._disposeCaches();\n      this._items[index].setSource(newSource);\n    }\n  }\n  compile(onigLib) {\n    if (!this._cached) {\n      let regExps = this._items.map((e) => e.source);\n      this._cached = new CompiledRule(onigLib, regExps, this._items.map((e) => e.ruleId));\n    }\n    return this._cached;\n  }\n  compileAG(onigLib, allowA, allowG) {\n    if (!this._hasAnchors) {\n      return this.compile(onigLib);\n    } else {\n      if (allowA) {\n        if (allowG) {\n          if (!this._anchorCache.A1_G1) {\n            this._anchorCache.A1_G1 = this._resolveAnchors(onigLib, allowA, allowG);\n          }\n          return this._anchorCache.A1_G1;\n        } else {\n          if (!this._anchorCache.A1_G0) {\n            this._anchorCache.A1_G0 = this._resolveAnchors(onigLib, allowA, allowG);\n          }\n          return this._anchorCache.A1_G0;\n        }\n      } else {\n        if (allowG) {\n          if (!this._anchorCache.A0_G1) {\n            this._anchorCache.A0_G1 = this._resolveAnchors(onigLib, allowA, allowG);\n          }\n          return this._anchorCache.A0_G1;\n        } else {\n          if (!this._anchorCache.A0_G0) {\n            this._anchorCache.A0_G0 = this._resolveAnchors(onigLib, allowA, allowG);\n          }\n          return this._anchorCache.A0_G0;\n        }\n      }\n    }\n  }\n  _resolveAnchors(onigLib, allowA, allowG) {\n    let regExps = this._items.map((e) => e.resolveAnchors(allowA, allowG));\n    return new CompiledRule(onigLib, regExps, this._items.map((e) => e.ruleId));\n  }\n};\nvar CompiledRule = class {\n  constructor(onigLib, regExps, rules) {\n    this.regExps = regExps;\n    this.rules = rules;\n    this.scanner = onigLib.createOnigScanner(regExps);\n  }\n  scanner;\n  dispose() {\n    if (typeof this.scanner.dispose === \"function\") {\n      this.scanner.dispose();\n    }\n  }\n  toString() {\n    const r = [];\n    for (let i = 0, len = this.rules.length; i < len; i++) {\n      r.push(\"   - \" + this.rules[i] + \": \" + this.regExps[i]);\n    }\n    return r.join(\"\\n\");\n  }\n  findNextMatchSync(string, startPosition, options) {\n    const result = this.scanner.findNextMatchSync(string, startPosition, options);\n    if (!result) {\n      return null;\n    }\n    return {\n      ruleId: this.rules[result.index],\n      captureIndices: result.captureIndices\n    };\n  }\n};\n\n// src/grammar/basicScopesAttributeProvider.ts\nvar BasicScopeAttributes = class {\n  constructor(languageId, tokenType) {\n    this.languageId = languageId;\n    this.tokenType = tokenType;\n  }\n};\nvar BasicScopeAttributesProvider = class _BasicScopeAttributesProvider {\n  _defaultAttributes;\n  _embeddedLanguagesMatcher;\n  constructor(initialLanguageId, embeddedLanguages) {\n    this._defaultAttributes = new BasicScopeAttributes(initialLanguageId, 8 /* NotSet */);\n    this._embeddedLanguagesMatcher = new ScopeMatcher(Object.entries(embeddedLanguages || {}));\n  }\n  getDefaultAttributes() {\n    return this._defaultAttributes;\n  }\n  getBasicScopeAttributes(scopeName) {\n    if (scopeName === null) {\n      return _BasicScopeAttributesProvider._NULL_SCOPE_METADATA;\n    }\n    return this._getBasicScopeAttributes.get(scopeName);\n  }\n  static _NULL_SCOPE_METADATA = new BasicScopeAttributes(0, 0);\n  _getBasicScopeAttributes = new CachedFn((scopeName) => {\n    const languageId = this._scopeToLanguage(scopeName);\n    const standardTokenType = this._toStandardTokenType(scopeName);\n    return new BasicScopeAttributes(languageId, standardTokenType);\n  });\n  /**\n   * Given a produced TM scope, return the language that token describes or null if unknown.\n   * e.g. source.html => html, source.css.embedded.html => css, punctuation.definition.tag.html => null\n   */\n  _scopeToLanguage(scope) {\n    return this._embeddedLanguagesMatcher.match(scope) || 0;\n  }\n  _toStandardTokenType(scopeName) {\n    const m = scopeName.match(_BasicScopeAttributesProvider.STANDARD_TOKEN_TYPE_REGEXP);\n    if (!m) {\n      return 8 /* NotSet */;\n    }\n    switch (m[1]) {\n      case \"comment\":\n        return 1 /* Comment */;\n      case \"string\":\n        return 2 /* String */;\n      case \"regex\":\n        return 3 /* RegEx */;\n      case \"meta.embedded\":\n        return 0 /* Other */;\n    }\n    throw new Error(\"Unexpected match for standard token type!\");\n  }\n  static STANDARD_TOKEN_TYPE_REGEXP = /\\b(comment|string|regex|meta\\.embedded)\\b/;\n};\nvar ScopeMatcher = class {\n  values;\n  scopesRegExp;\n  constructor(values) {\n    if (values.length === 0) {\n      this.values = null;\n      this.scopesRegExp = null;\n    } else {\n      this.values = new Map(values);\n      const escapedScopes = values.map(\n        ([scopeName, value]) => escapeRegExpCharacters(scopeName)\n      );\n      escapedScopes.sort();\n      escapedScopes.reverse();\n      this.scopesRegExp = new RegExp(\n        `^((${escapedScopes.join(\")|(\")}))($|\\\\.)`,\n        \"\"\n      );\n    }\n  }\n  match(scope) {\n    if (!this.scopesRegExp) {\n      return void 0;\n    }\n    const m = scope.match(this.scopesRegExp);\n    if (!m) {\n      return void 0;\n    }\n    return this.values.get(m[1]);\n  }\n};\n\n// src/debug.ts\nvar DebugFlags = {\n  InDebugMode: typeof process !== \"undefined\" && !!process.env[\"VSCODE_TEXTMATE_DEBUG\"]\n};\nvar UseOnigurumaFindOptions = false;\n\n// src/grammar/tokenizeString.ts\nvar TokenizeStringResult = class {\n  constructor(stack, stoppedEarly) {\n    this.stack = stack;\n    this.stoppedEarly = stoppedEarly;\n  }\n};\nfunction _tokenizeString(grammar, lineText, isFirstLine, linePos, stack, lineTokens, checkWhileConditions, timeLimit) {\n  const lineLength = lineText.content.length;\n  let STOP = false;\n  let anchorPosition = -1;\n  if (checkWhileConditions) {\n    const whileCheckResult = _checkWhileConditions(\n      grammar,\n      lineText,\n      isFirstLine,\n      linePos,\n      stack,\n      lineTokens\n    );\n    stack = whileCheckResult.stack;\n    linePos = whileCheckResult.linePos;\n    isFirstLine = whileCheckResult.isFirstLine;\n    anchorPosition = whileCheckResult.anchorPosition;\n  }\n  const startTime = Date.now();\n  while (!STOP) {\n    if (timeLimit !== 0) {\n      const elapsedTime = Date.now() - startTime;\n      if (elapsedTime > timeLimit) {\n        return new TokenizeStringResult(stack, true);\n      }\n    }\n    scanNext();\n  }\n  return new TokenizeStringResult(stack, false);\n  function scanNext() {\n    if (false) {\n      console.log(\"\");\n      console.log(\n        `@@scanNext ${linePos}: |${lineText.content.substr(linePos).replace(/\\n$/, \"\\\\n\")}|`\n      );\n    }\n    const r = matchRuleOrInjections(\n      grammar,\n      lineText,\n      isFirstLine,\n      linePos,\n      stack,\n      anchorPosition\n    );\n    if (!r) {\n      lineTokens.produce(stack, lineLength);\n      STOP = true;\n      return;\n    }\n    const captureIndices = r.captureIndices;\n    const matchedRuleId = r.matchedRuleId;\n    const hasAdvanced = captureIndices && captureIndices.length > 0 ? captureIndices[0].end > linePos : false;\n    if (matchedRuleId === endRuleId) {\n      const poppedRule = stack.getRule(grammar);\n      if (false) {\n        console.log(\n          \"  popping \" + poppedRule.debugName + \" - \" + poppedRule.debugEndRegExp\n        );\n      }\n      lineTokens.produce(stack, captureIndices[0].start);\n      stack = stack.withContentNameScopesList(stack.nameScopesList);\n      handleCaptures(\n        grammar,\n        lineText,\n        isFirstLine,\n        stack,\n        lineTokens,\n        poppedRule.endCaptures,\n        captureIndices\n      );\n      lineTokens.produce(stack, captureIndices[0].end);\n      const popped = stack;\n      stack = stack.parent;\n      anchorPosition = popped.getAnchorPos();\n      if (!hasAdvanced && popped.getEnterPos() === linePos) {\n        if (false) {\n          console.error(\n            \"[1] - Grammar is in an endless loop - Grammar pushed & popped a rule without advancing\"\n          );\n        }\n        stack = popped;\n        lineTokens.produce(stack, lineLength);\n        STOP = true;\n        return;\n      }\n    } else {\n      const _rule = grammar.getRule(matchedRuleId);\n      lineTokens.produce(stack, captureIndices[0].start);\n      const beforePush = stack;\n      const scopeName = _rule.getName(lineText.content, captureIndices);\n      const nameScopesList = stack.contentNameScopesList.pushAttributed(\n        scopeName,\n        grammar\n      );\n      stack = stack.push(\n        matchedRuleId,\n        linePos,\n        anchorPosition,\n        captureIndices[0].end === lineLength,\n        null,\n        nameScopesList,\n        nameScopesList\n      );\n      if (_rule instanceof BeginEndRule) {\n        const pushedRule = _rule;\n        if (false) {\n          console.log(\n            \"  pushing \" + pushedRule.debugName + \" - \" + pushedRule.debugBeginRegExp\n          );\n        }\n        handleCaptures(\n          grammar,\n          lineText,\n          isFirstLine,\n          stack,\n          lineTokens,\n          pushedRule.beginCaptures,\n          captureIndices\n        );\n        lineTokens.produce(stack, captureIndices[0].end);\n        anchorPosition = captureIndices[0].end;\n        const contentName = pushedRule.getContentName(\n          lineText.content,\n          captureIndices\n        );\n        const contentNameScopesList = nameScopesList.pushAttributed(\n          contentName,\n          grammar\n        );\n        stack = stack.withContentNameScopesList(contentNameScopesList);\n        if (pushedRule.endHasBackReferences) {\n          stack = stack.withEndRule(\n            pushedRule.getEndWithResolvedBackReferences(\n              lineText.content,\n              captureIndices\n            )\n          );\n        }\n        if (!hasAdvanced && beforePush.hasSameRuleAs(stack)) {\n          if (false) {\n            console.error(\n              \"[2] - Grammar is in an endless loop - Grammar pushed the same rule without advancing\"\n            );\n          }\n          stack = stack.pop();\n          lineTokens.produce(stack, lineLength);\n          STOP = true;\n          return;\n        }\n      } else if (_rule instanceof BeginWhileRule) {\n        const pushedRule = _rule;\n        if (false) {\n          console.log(\"  pushing \" + pushedRule.debugName);\n        }\n        handleCaptures(\n          grammar,\n          lineText,\n          isFirstLine,\n          stack,\n          lineTokens,\n          pushedRule.beginCaptures,\n          captureIndices\n        );\n        lineTokens.produce(stack, captureIndices[0].end);\n        anchorPosition = captureIndices[0].end;\n        const contentName = pushedRule.getContentName(\n          lineText.content,\n          captureIndices\n        );\n        const contentNameScopesList = nameScopesList.pushAttributed(\n          contentName,\n          grammar\n        );\n        stack = stack.withContentNameScopesList(contentNameScopesList);\n        if (pushedRule.whileHasBackReferences) {\n          stack = stack.withEndRule(\n            pushedRule.getWhileWithResolvedBackReferences(\n              lineText.content,\n              captureIndices\n            )\n          );\n        }\n        if (!hasAdvanced && beforePush.hasSameRuleAs(stack)) {\n          if (false) {\n            console.error(\n              \"[3] - Grammar is in an endless loop - Grammar pushed the same rule without advancing\"\n            );\n          }\n          stack = stack.pop();\n          lineTokens.produce(stack, lineLength);\n          STOP = true;\n          return;\n        }\n      } else {\n        const matchingRule = _rule;\n        if (false) {\n          console.log(\n            \"  matched \" + matchingRule.debugName + \" - \" + matchingRule.debugMatchRegExp\n          );\n        }\n        handleCaptures(\n          grammar,\n          lineText,\n          isFirstLine,\n          stack,\n          lineTokens,\n          matchingRule.captures,\n          captureIndices\n        );\n        lineTokens.produce(stack, captureIndices[0].end);\n        stack = stack.pop();\n        if (!hasAdvanced) {\n          if (false) {\n            console.error(\n              \"[4] - Grammar is in an endless loop - Grammar is not advancing, nor is it pushing/popping\"\n            );\n          }\n          stack = stack.safePop();\n          lineTokens.produce(stack, lineLength);\n          STOP = true;\n          return;\n        }\n      }\n    }\n    if (captureIndices[0].end > linePos) {\n      linePos = captureIndices[0].end;\n      isFirstLine = false;\n    }\n  }\n}\nfunction _checkWhileConditions(grammar, lineText, isFirstLine, linePos, stack, lineTokens) {\n  let anchorPosition = stack.beginRuleCapturedEOL ? 0 : -1;\n  const whileRules = [];\n  for (let node = stack; node; node = node.pop()) {\n    const nodeRule = node.getRule(grammar);\n    if (nodeRule instanceof BeginWhileRule) {\n      whileRules.push({\n        rule: nodeRule,\n        stack: node\n      });\n    }\n  }\n  for (let whileRule = whileRules.pop(); whileRule; whileRule = whileRules.pop()) {\n    const { ruleScanner, findOptions } = prepareRuleWhileSearch(whileRule.rule, grammar, whileRule.stack.endRule, isFirstLine, linePos === anchorPosition);\n    const r = ruleScanner.findNextMatchSync(lineText, linePos, findOptions);\n    if (false) {\n      console.log(\"  scanning for while rule\");\n      console.log(ruleScanner.toString());\n    }\n    if (r) {\n      const matchedRuleId = r.ruleId;\n      if (matchedRuleId !== whileRuleId) {\n        stack = whileRule.stack.pop();\n        break;\n      }\n      if (r.captureIndices && r.captureIndices.length) {\n        lineTokens.produce(whileRule.stack, r.captureIndices[0].start);\n        handleCaptures(grammar, lineText, isFirstLine, whileRule.stack, lineTokens, whileRule.rule.whileCaptures, r.captureIndices);\n        lineTokens.produce(whileRule.stack, r.captureIndices[0].end);\n        anchorPosition = r.captureIndices[0].end;\n        if (r.captureIndices[0].end > linePos) {\n          linePos = r.captureIndices[0].end;\n          isFirstLine = false;\n        }\n      }\n    } else {\n      if (false) {\n        console.log(\"  popping \" + whileRule.rule.debugName + \" - \" + whileRule.rule.debugWhileRegExp);\n      }\n      stack = whileRule.stack.pop();\n      break;\n    }\n  }\n  return { stack, linePos, anchorPosition, isFirstLine };\n}\nfunction matchRuleOrInjections(grammar, lineText, isFirstLine, linePos, stack, anchorPosition) {\n  const matchResult = matchRule(grammar, lineText, isFirstLine, linePos, stack, anchorPosition);\n  const injections = grammar.getInjections();\n  if (injections.length === 0) {\n    return matchResult;\n  }\n  const injectionResult = matchInjections(injections, grammar, lineText, isFirstLine, linePos, stack, anchorPosition);\n  if (!injectionResult) {\n    return matchResult;\n  }\n  if (!matchResult) {\n    return injectionResult;\n  }\n  const matchResultScore = matchResult.captureIndices[0].start;\n  const injectionResultScore = injectionResult.captureIndices[0].start;\n  if (injectionResultScore < matchResultScore || injectionResult.priorityMatch && injectionResultScore === matchResultScore) {\n    return injectionResult;\n  }\n  return matchResult;\n}\nfunction matchRule(grammar, lineText, isFirstLine, linePos, stack, anchorPosition) {\n  const rule = stack.getRule(grammar);\n  const { ruleScanner, findOptions } = prepareRuleSearch(rule, grammar, stack.endRule, isFirstLine, linePos === anchorPosition);\n  const r = ruleScanner.findNextMatchSync(lineText, linePos, findOptions);\n  if (r) {\n    return {\n      captureIndices: r.captureIndices,\n      matchedRuleId: r.ruleId\n    };\n  }\n  return null;\n}\nfunction matchInjections(injections, grammar, lineText, isFirstLine, linePos, stack, anchorPosition) {\n  let bestMatchRating = Number.MAX_VALUE;\n  let bestMatchCaptureIndices = null;\n  let bestMatchRuleId;\n  let bestMatchResultPriority = 0;\n  const scopes = stack.contentNameScopesList.getScopeNames();\n  for (let i = 0, len = injections.length; i < len; i++) {\n    const injection = injections[i];\n    if (!injection.matcher(scopes)) {\n      continue;\n    }\n    const rule = grammar.getRule(injection.ruleId);\n    const { ruleScanner, findOptions } = prepareRuleSearch(rule, grammar, null, isFirstLine, linePos === anchorPosition);\n    const matchResult = ruleScanner.findNextMatchSync(lineText, linePos, findOptions);\n    if (!matchResult) {\n      continue;\n    }\n    if (false) {\n      console.log(`  matched injection: ${injection.debugSelector}`);\n      console.log(ruleScanner.toString());\n    }\n    const matchRating = matchResult.captureIndices[0].start;\n    if (matchRating >= bestMatchRating) {\n      continue;\n    }\n    bestMatchRating = matchRating;\n    bestMatchCaptureIndices = matchResult.captureIndices;\n    bestMatchRuleId = matchResult.ruleId;\n    bestMatchResultPriority = injection.priority;\n    if (bestMatchRating === linePos) {\n      break;\n    }\n  }\n  if (bestMatchCaptureIndices) {\n    return {\n      priorityMatch: bestMatchResultPriority === -1,\n      captureIndices: bestMatchCaptureIndices,\n      matchedRuleId: bestMatchRuleId\n    };\n  }\n  return null;\n}\nfunction prepareRuleSearch(rule, grammar, endRegexSource, allowA, allowG) {\n  if (UseOnigurumaFindOptions) {\n    const ruleScanner2 = rule.compile(grammar, endRegexSource);\n    const findOptions = getFindOptions(allowA, allowG);\n    return { ruleScanner: ruleScanner2, findOptions };\n  }\n  const ruleScanner = rule.compileAG(grammar, endRegexSource, allowA, allowG);\n  return { ruleScanner, findOptions: 0 /* None */ };\n}\nfunction prepareRuleWhileSearch(rule, grammar, endRegexSource, allowA, allowG) {\n  if (UseOnigurumaFindOptions) {\n    const ruleScanner2 = rule.compileWhile(grammar, endRegexSource);\n    const findOptions = getFindOptions(allowA, allowG);\n    return { ruleScanner: ruleScanner2, findOptions };\n  }\n  const ruleScanner = rule.compileWhileAG(grammar, endRegexSource, allowA, allowG);\n  return { ruleScanner, findOptions: 0 /* None */ };\n}\nfunction getFindOptions(allowA, allowG) {\n  let options = 0 /* None */;\n  if (!allowA) {\n    options |= 1 /* NotBeginString */;\n  }\n  if (!allowG) {\n    options |= 4 /* NotBeginPosition */;\n  }\n  return options;\n}\nfunction handleCaptures(grammar, lineText, isFirstLine, stack, lineTokens, captures, captureIndices) {\n  if (captures.length === 0) {\n    return;\n  }\n  const lineTextContent = lineText.content;\n  const len = Math.min(captures.length, captureIndices.length);\n  const localStack = [];\n  const maxEnd = captureIndices[0].end;\n  for (let i = 0; i < len; i++) {\n    const captureRule = captures[i];\n    if (captureRule === null) {\n      continue;\n    }\n    const captureIndex = captureIndices[i];\n    if (captureIndex.length === 0) {\n      continue;\n    }\n    if (captureIndex.start > maxEnd) {\n      break;\n    }\n    while (localStack.length > 0 && localStack[localStack.length - 1].endPos <= captureIndex.start) {\n      lineTokens.produceFromScopes(localStack[localStack.length - 1].scopes, localStack[localStack.length - 1].endPos);\n      localStack.pop();\n    }\n    if (localStack.length > 0) {\n      lineTokens.produceFromScopes(localStack[localStack.length - 1].scopes, captureIndex.start);\n    } else {\n      lineTokens.produce(stack, captureIndex.start);\n    }\n    if (captureRule.retokenizeCapturedWithRuleId) {\n      const scopeName = captureRule.getName(lineTextContent, captureIndices);\n      const nameScopesList = stack.contentNameScopesList.pushAttributed(scopeName, grammar);\n      const contentName = captureRule.getContentName(lineTextContent, captureIndices);\n      const contentNameScopesList = nameScopesList.pushAttributed(contentName, grammar);\n      const stackClone = stack.push(captureRule.retokenizeCapturedWithRuleId, captureIndex.start, -1, false, null, nameScopesList, contentNameScopesList);\n      const onigSubStr = grammar.createOnigString(lineTextContent.substring(0, captureIndex.end));\n      _tokenizeString(\n        grammar,\n        onigSubStr,\n        isFirstLine && captureIndex.start === 0,\n        captureIndex.start,\n        stackClone,\n        lineTokens,\n        false,\n        /* no time limit */\n        0\n      );\n      disposeOnigString(onigSubStr);\n      continue;\n    }\n    const captureRuleScopeName = captureRule.getName(lineTextContent, captureIndices);\n    if (captureRuleScopeName !== null) {\n      const base = localStack.length > 0 ? localStack[localStack.length - 1].scopes : stack.contentNameScopesList;\n      const captureRuleScopesList = base.pushAttributed(captureRuleScopeName, grammar);\n      localStack.push(new LocalStackElement(captureRuleScopesList, captureIndex.end));\n    }\n  }\n  while (localStack.length > 0) {\n    lineTokens.produceFromScopes(localStack[localStack.length - 1].scopes, localStack[localStack.length - 1].endPos);\n    localStack.pop();\n  }\n}\nvar LocalStackElement = class {\n  scopes;\n  endPos;\n  constructor(scopes, endPos) {\n    this.scopes = scopes;\n    this.endPos = endPos;\n  }\n};\n\n// src/grammar/grammar.ts\nfunction createGrammar(scopeName, grammar, initialLanguage, embeddedLanguages, tokenTypes, balancedBracketSelectors, grammarRepository, onigLib) {\n  return new Grammar(\n    scopeName,\n    grammar,\n    initialLanguage,\n    embeddedLanguages,\n    tokenTypes,\n    balancedBracketSelectors,\n    grammarRepository,\n    onigLib\n  );\n}\nfunction collectInjections(result, selector, rule, ruleFactoryHelper, grammar) {\n  const matchers = createMatchers(selector, nameMatcher);\n  const ruleId = RuleFactory.getCompiledRuleId(rule, ruleFactoryHelper, grammar.repository);\n  for (const matcher of matchers) {\n    result.push({\n      debugSelector: selector,\n      matcher: matcher.matcher,\n      ruleId,\n      grammar,\n      priority: matcher.priority\n    });\n  }\n}\nfunction nameMatcher(identifers, scopes) {\n  if (scopes.length < identifers.length) {\n    return false;\n  }\n  let lastIndex = 0;\n  return identifers.every((identifier) => {\n    for (let i = lastIndex; i < scopes.length; i++) {\n      if (scopesAreMatching(scopes[i], identifier)) {\n        lastIndex = i + 1;\n        return true;\n      }\n    }\n    return false;\n  });\n}\nfunction scopesAreMatching(thisScopeName, scopeName) {\n  if (!thisScopeName) {\n    return false;\n  }\n  if (thisScopeName === scopeName) {\n    return true;\n  }\n  const len = scopeName.length;\n  return thisScopeName.length > len && thisScopeName.substr(0, len) === scopeName && thisScopeName[len] === \".\";\n}\nvar Grammar = class {\n  constructor(_rootScopeName, grammar, initialLanguage, embeddedLanguages, tokenTypes, balancedBracketSelectors, grammarRepository, _onigLib) {\n    this._rootScopeName = _rootScopeName;\n    this.balancedBracketSelectors = balancedBracketSelectors;\n    this._onigLib = _onigLib;\n    this._basicScopeAttributesProvider = new BasicScopeAttributesProvider(\n      initialLanguage,\n      embeddedLanguages\n    );\n    this._rootId = -1;\n    this._lastRuleId = 0;\n    this._ruleId2desc = [null];\n    this._includedGrammars = {};\n    this._grammarRepository = grammarRepository;\n    this._grammar = initGrammar(grammar, null);\n    this._injections = null;\n    this._tokenTypeMatchers = [];\n    if (tokenTypes) {\n      for (const selector of Object.keys(tokenTypes)) {\n        const matchers = createMatchers(selector, nameMatcher);\n        for (const matcher of matchers) {\n          this._tokenTypeMatchers.push({\n            matcher: matcher.matcher,\n            type: tokenTypes[selector]\n          });\n        }\n      }\n    }\n  }\n  _rootId;\n  _lastRuleId;\n  _ruleId2desc;\n  _includedGrammars;\n  _grammarRepository;\n  _grammar;\n  _injections;\n  _basicScopeAttributesProvider;\n  _tokenTypeMatchers;\n  get themeProvider() {\n    return this._grammarRepository;\n  }\n  dispose() {\n    for (const rule of this._ruleId2desc) {\n      if (rule) {\n        rule.dispose();\n      }\n    }\n  }\n  createOnigScanner(sources) {\n    return this._onigLib.createOnigScanner(sources);\n  }\n  createOnigString(sources) {\n    return this._onigLib.createOnigString(sources);\n  }\n  getMetadataForScope(scope) {\n    return this._basicScopeAttributesProvider.getBasicScopeAttributes(scope);\n  }\n  _collectInjections() {\n    const grammarRepository = {\n      lookup: (scopeName2) => {\n        if (scopeName2 === this._rootScopeName) {\n          return this._grammar;\n        }\n        return this.getExternalGrammar(scopeName2);\n      },\n      injections: (scopeName2) => {\n        return this._grammarRepository.injections(scopeName2);\n      }\n    };\n    const result = [];\n    const scopeName = this._rootScopeName;\n    const grammar = grammarRepository.lookup(scopeName);\n    if (grammar) {\n      const rawInjections = grammar.injections;\n      if (rawInjections) {\n        for (let expression in rawInjections) {\n          collectInjections(\n            result,\n            expression,\n            rawInjections[expression],\n            this,\n            grammar\n          );\n        }\n      }\n      const injectionScopeNames = this._grammarRepository.injections(scopeName);\n      if (injectionScopeNames) {\n        injectionScopeNames.forEach((injectionScopeName) => {\n          const injectionGrammar = this.getExternalGrammar(injectionScopeName);\n          if (injectionGrammar) {\n            const selector = injectionGrammar.injectionSelector;\n            if (selector) {\n              collectInjections(\n                result,\n                selector,\n                injectionGrammar,\n                this,\n                injectionGrammar\n              );\n            }\n          }\n        });\n      }\n    }\n    result.sort((i1, i2) => i1.priority - i2.priority);\n    return result;\n  }\n  getInjections() {\n    if (this._injections === null) {\n      this._injections = this._collectInjections();\n    }\n    return this._injections;\n  }\n  registerRule(factory) {\n    const id = ++this._lastRuleId;\n    const result = factory(ruleIdFromNumber(id));\n    this._ruleId2desc[id] = result;\n    return result;\n  }\n  getRule(ruleId) {\n    return this._ruleId2desc[ruleIdToNumber(ruleId)];\n  }\n  getExternalGrammar(scopeName, repository) {\n    if (this._includedGrammars[scopeName]) {\n      return this._includedGrammars[scopeName];\n    } else if (this._grammarRepository) {\n      const rawIncludedGrammar = this._grammarRepository.lookup(scopeName);\n      if (rawIncludedGrammar) {\n        this._includedGrammars[scopeName] = initGrammar(\n          rawIncludedGrammar,\n          repository && repository.$base\n        );\n        return this._includedGrammars[scopeName];\n      }\n    }\n    return void 0;\n  }\n  tokenizeLine(lineText, prevState, timeLimit = 0) {\n    const r = this._tokenize(lineText, prevState, false, timeLimit);\n    return {\n      tokens: r.lineTokens.getResult(r.ruleStack, r.lineLength),\n      ruleStack: r.ruleStack,\n      stoppedEarly: r.stoppedEarly\n    };\n  }\n  tokenizeLine2(lineText, prevState, timeLimit = 0) {\n    const r = this._tokenize(lineText, prevState, true, timeLimit);\n    return {\n      tokens: r.lineTokens.getBinaryResult(r.ruleStack, r.lineLength),\n      ruleStack: r.ruleStack,\n      stoppedEarly: r.stoppedEarly\n    };\n  }\n  _tokenize(lineText, prevState, emitBinaryTokens, timeLimit) {\n    if (this._rootId === -1) {\n      this._rootId = RuleFactory.getCompiledRuleId(\n        this._grammar.repository.$self,\n        this,\n        this._grammar.repository\n      );\n      this.getInjections();\n    }\n    let isFirstLine;\n    if (!prevState || prevState === StateStackImpl.NULL) {\n      isFirstLine = true;\n      const rawDefaultMetadata = this._basicScopeAttributesProvider.getDefaultAttributes();\n      const defaultStyle = this.themeProvider.getDefaults();\n      const defaultMetadata = EncodedTokenMetadata.set(\n        0,\n        rawDefaultMetadata.languageId,\n        rawDefaultMetadata.tokenType,\n        null,\n        defaultStyle.fontStyle,\n        defaultStyle.foregroundId,\n        defaultStyle.backgroundId\n      );\n      const rootScopeName = this.getRule(this._rootId).getName(\n        null,\n        null\n      );\n      let scopeList;\n      if (rootScopeName) {\n        scopeList = AttributedScopeStack.createRootAndLookUpScopeName(\n          rootScopeName,\n          defaultMetadata,\n          this\n        );\n      } else {\n        scopeList = AttributedScopeStack.createRoot(\n          \"unknown\",\n          defaultMetadata\n        );\n      }\n      prevState = new StateStackImpl(\n        null,\n        this._rootId,\n        -1,\n        -1,\n        false,\n        null,\n        scopeList,\n        scopeList\n      );\n    } else {\n      isFirstLine = false;\n      prevState.reset();\n    }\n    lineText = lineText + \"\\n\";\n    const onigLineText = this.createOnigString(lineText);\n    const lineLength = onigLineText.content.length;\n    const lineTokens = new LineTokens(\n      emitBinaryTokens,\n      lineText,\n      this._tokenTypeMatchers,\n      this.balancedBracketSelectors\n    );\n    const r = _tokenizeString(\n      this,\n      onigLineText,\n      isFirstLine,\n      0,\n      prevState,\n      lineTokens,\n      true,\n      timeLimit\n    );\n    disposeOnigString(onigLineText);\n    return {\n      lineLength,\n      lineTokens,\n      ruleStack: r.stack,\n      stoppedEarly: r.stoppedEarly\n    };\n  }\n};\nfunction initGrammar(grammar, base) {\n  grammar = clone(grammar);\n  grammar.repository = grammar.repository || {};\n  grammar.repository.$self = {\n    $vscodeTextmateLocation: grammar.$vscodeTextmateLocation,\n    patterns: grammar.patterns,\n    name: grammar.scopeName\n  };\n  grammar.repository.$base = base || grammar.repository.$self;\n  return grammar;\n}\nvar AttributedScopeStack = class _AttributedScopeStack {\n  /**\n   * Invariant:\n   * ```\n   * if (parent && !scopePath.extends(parent.scopePath)) {\n   * \tthrow new Error();\n   * }\n   * ```\n   */\n  constructor(parent, scopePath, tokenAttributes) {\n    this.parent = parent;\n    this.scopePath = scopePath;\n    this.tokenAttributes = tokenAttributes;\n  }\n  static fromExtension(namesScopeList, contentNameScopesList) {\n    let current = namesScopeList;\n    let scopeNames = namesScopeList?.scopePath ?? null;\n    for (const frame of contentNameScopesList) {\n      scopeNames = ScopeStack.push(scopeNames, frame.scopeNames);\n      current = new _AttributedScopeStack(current, scopeNames, frame.encodedTokenAttributes);\n    }\n    return current;\n  }\n  static createRoot(scopeName, tokenAttributes) {\n    return new _AttributedScopeStack(null, new ScopeStack(null, scopeName), tokenAttributes);\n  }\n  static createRootAndLookUpScopeName(scopeName, tokenAttributes, grammar) {\n    const rawRootMetadata = grammar.getMetadataForScope(scopeName);\n    const scopePath = new ScopeStack(null, scopeName);\n    const rootStyle = grammar.themeProvider.themeMatch(scopePath);\n    const resolvedTokenAttributes = _AttributedScopeStack.mergeAttributes(\n      tokenAttributes,\n      rawRootMetadata,\n      rootStyle\n    );\n    return new _AttributedScopeStack(null, scopePath, resolvedTokenAttributes);\n  }\n  get scopeName() {\n    return this.scopePath.scopeName;\n  }\n  toString() {\n    return this.getScopeNames().join(\" \");\n  }\n  equals(other) {\n    return _AttributedScopeStack.equals(this, other);\n  }\n  static equals(a, b) {\n    do {\n      if (a === b) {\n        return true;\n      }\n      if (!a && !b) {\n        return true;\n      }\n      if (!a || !b) {\n        return false;\n      }\n      if (a.scopeName !== b.scopeName || a.tokenAttributes !== b.tokenAttributes) {\n        return false;\n      }\n      a = a.parent;\n      b = b.parent;\n    } while (true);\n  }\n  static mergeAttributes(existingTokenAttributes, basicScopeAttributes, styleAttributes) {\n    let fontStyle = -1 /* NotSet */;\n    let foreground = 0;\n    let background = 0;\n    if (styleAttributes !== null) {\n      fontStyle = styleAttributes.fontStyle;\n      foreground = styleAttributes.foregroundId;\n      background = styleAttributes.backgroundId;\n    }\n    return EncodedTokenMetadata.set(\n      existingTokenAttributes,\n      basicScopeAttributes.languageId,\n      basicScopeAttributes.tokenType,\n      null,\n      fontStyle,\n      foreground,\n      background\n    );\n  }\n  pushAttributed(scopePath, grammar) {\n    if (scopePath === null) {\n      return this;\n    }\n    if (scopePath.indexOf(\" \") === -1) {\n      return _AttributedScopeStack._pushAttributed(this, scopePath, grammar);\n    }\n    const scopes = scopePath.split(/ /g);\n    let result = this;\n    for (const scope of scopes) {\n      result = _AttributedScopeStack._pushAttributed(result, scope, grammar);\n    }\n    return result;\n  }\n  static _pushAttributed(target, scopeName, grammar) {\n    const rawMetadata = grammar.getMetadataForScope(scopeName);\n    const newPath = target.scopePath.push(scopeName);\n    const scopeThemeMatchResult = grammar.themeProvider.themeMatch(newPath);\n    const metadata = _AttributedScopeStack.mergeAttributes(\n      target.tokenAttributes,\n      rawMetadata,\n      scopeThemeMatchResult\n    );\n    return new _AttributedScopeStack(target, newPath, metadata);\n  }\n  getScopeNames() {\n    return this.scopePath.getSegments();\n  }\n  getExtensionIfDefined(base) {\n    const result = [];\n    let self = this;\n    while (self && self !== base) {\n      result.push({\n        encodedTokenAttributes: self.tokenAttributes,\n        scopeNames: self.scopePath.getExtensionIfDefined(self.parent?.scopePath ?? null)\n      });\n      self = self.parent;\n    }\n    return self === base ? result.reverse() : void 0;\n  }\n};\nvar StateStackImpl = class _StateStackImpl {\n  /**\n   * Invariant:\n   * ```\n   * if (contentNameScopesList !== nameScopesList && contentNameScopesList?.parent !== nameScopesList) {\n   * \tthrow new Error();\n   * }\n   * if (this.parent && !nameScopesList.extends(this.parent.contentNameScopesList)) {\n   * \tthrow new Error();\n   * }\n   * ```\n   */\n  constructor(parent, ruleId, enterPos, anchorPos, beginRuleCapturedEOL, endRule, nameScopesList, contentNameScopesList) {\n    this.parent = parent;\n    this.ruleId = ruleId;\n    this.beginRuleCapturedEOL = beginRuleCapturedEOL;\n    this.endRule = endRule;\n    this.nameScopesList = nameScopesList;\n    this.contentNameScopesList = contentNameScopesList;\n    this.depth = this.parent ? this.parent.depth + 1 : 1;\n    this._enterPos = enterPos;\n    this._anchorPos = anchorPos;\n  }\n  _stackElementBrand = void 0;\n  // TODO remove me\n  static NULL = new _StateStackImpl(\n    null,\n    0,\n    0,\n    0,\n    false,\n    null,\n    null,\n    null\n  );\n  /**\n   * The position on the current line where this state was pushed.\n   * This is relevant only while tokenizing a line, to detect endless loops.\n   * Its value is meaningless across lines.\n   */\n  _enterPos;\n  /**\n   * The captured anchor position when this stack element was pushed.\n   * This is relevant only while tokenizing a line, to restore the anchor position when popping.\n   * Its value is meaningless across lines.\n   */\n  _anchorPos;\n  /**\n   * The depth of the stack.\n   */\n  depth;\n  equals(other) {\n    if (other === null) {\n      return false;\n    }\n    return _StateStackImpl._equals(this, other);\n  }\n  static _equals(a, b) {\n    if (a === b) {\n      return true;\n    }\n    if (!this._structuralEquals(a, b)) {\n      return false;\n    }\n    return AttributedScopeStack.equals(a.contentNameScopesList, b.contentNameScopesList);\n  }\n  /**\n   * A structural equals check. Does not take into account `scopes`.\n   */\n  static _structuralEquals(a, b) {\n    do {\n      if (a === b) {\n        return true;\n      }\n      if (!a && !b) {\n        return true;\n      }\n      if (!a || !b) {\n        return false;\n      }\n      if (a.depth !== b.depth || a.ruleId !== b.ruleId || a.endRule !== b.endRule) {\n        return false;\n      }\n      a = a.parent;\n      b = b.parent;\n    } while (true);\n  }\n  clone() {\n    return this;\n  }\n  static _reset(el) {\n    while (el) {\n      el._enterPos = -1;\n      el._anchorPos = -1;\n      el = el.parent;\n    }\n  }\n  reset() {\n    _StateStackImpl._reset(this);\n  }\n  pop() {\n    return this.parent;\n  }\n  safePop() {\n    if (this.parent) {\n      return this.parent;\n    }\n    return this;\n  }\n  push(ruleId, enterPos, anchorPos, beginRuleCapturedEOL, endRule, nameScopesList, contentNameScopesList) {\n    return new _StateStackImpl(\n      this,\n      ruleId,\n      enterPos,\n      anchorPos,\n      beginRuleCapturedEOL,\n      endRule,\n      nameScopesList,\n      contentNameScopesList\n    );\n  }\n  getEnterPos() {\n    return this._enterPos;\n  }\n  getAnchorPos() {\n    return this._anchorPos;\n  }\n  getRule(grammar) {\n    return grammar.getRule(this.ruleId);\n  }\n  toString() {\n    const r = [];\n    this._writeString(r, 0);\n    return \"[\" + r.join(\",\") + \"]\";\n  }\n  _writeString(res, outIndex) {\n    if (this.parent) {\n      outIndex = this.parent._writeString(res, outIndex);\n    }\n    res[outIndex++] = `(${this.ruleId}, ${this.nameScopesList?.toString()}, ${this.contentNameScopesList?.toString()})`;\n    return outIndex;\n  }\n  withContentNameScopesList(contentNameScopeStack) {\n    if (this.contentNameScopesList === contentNameScopeStack) {\n      return this;\n    }\n    return this.parent.push(\n      this.ruleId,\n      this._enterPos,\n      this._anchorPos,\n      this.beginRuleCapturedEOL,\n      this.endRule,\n      this.nameScopesList,\n      contentNameScopeStack\n    );\n  }\n  withEndRule(endRule) {\n    if (this.endRule === endRule) {\n      return this;\n    }\n    return new _StateStackImpl(\n      this.parent,\n      this.ruleId,\n      this._enterPos,\n      this._anchorPos,\n      this.beginRuleCapturedEOL,\n      endRule,\n      this.nameScopesList,\n      this.contentNameScopesList\n    );\n  }\n  // Used to warn of endless loops\n  hasSameRuleAs(other) {\n    let el = this;\n    while (el && el._enterPos === other._enterPos) {\n      if (el.ruleId === other.ruleId) {\n        return true;\n      }\n      el = el.parent;\n    }\n    return false;\n  }\n  toStateStackFrame() {\n    return {\n      ruleId: ruleIdToNumber(this.ruleId),\n      beginRuleCapturedEOL: this.beginRuleCapturedEOL,\n      endRule: this.endRule,\n      nameScopesList: this.nameScopesList?.getExtensionIfDefined(this.parent?.nameScopesList ?? null) ?? [],\n      contentNameScopesList: this.contentNameScopesList?.getExtensionIfDefined(this.nameScopesList) ?? []\n    };\n  }\n  static pushFrame(self, frame) {\n    const namesScopeList = AttributedScopeStack.fromExtension(self?.nameScopesList ?? null, frame.nameScopesList);\n    return new _StateStackImpl(\n      self,\n      ruleIdFromNumber(frame.ruleId),\n      frame.enterPos ?? -1,\n      frame.anchorPos ?? -1,\n      frame.beginRuleCapturedEOL,\n      frame.endRule,\n      namesScopeList,\n      AttributedScopeStack.fromExtension(namesScopeList, frame.contentNameScopesList)\n    );\n  }\n};\nvar BalancedBracketSelectors = class {\n  balancedBracketScopes;\n  unbalancedBracketScopes;\n  allowAny = false;\n  constructor(balancedBracketScopes, unbalancedBracketScopes) {\n    this.balancedBracketScopes = balancedBracketScopes.flatMap(\n      (selector) => {\n        if (selector === \"*\") {\n          this.allowAny = true;\n          return [];\n        }\n        return createMatchers(selector, nameMatcher).map((m) => m.matcher);\n      }\n    );\n    this.unbalancedBracketScopes = unbalancedBracketScopes.flatMap(\n      (selector) => createMatchers(selector, nameMatcher).map((m) => m.matcher)\n    );\n  }\n  get matchesAlways() {\n    return this.allowAny && this.unbalancedBracketScopes.length === 0;\n  }\n  get matchesNever() {\n    return this.balancedBracketScopes.length === 0 && !this.allowAny;\n  }\n  match(scopes) {\n    for (const excluder of this.unbalancedBracketScopes) {\n      if (excluder(scopes)) {\n        return false;\n      }\n    }\n    for (const includer of this.balancedBracketScopes) {\n      if (includer(scopes)) {\n        return true;\n      }\n    }\n    return this.allowAny;\n  }\n};\nvar LineTokens = class {\n  constructor(emitBinaryTokens, lineText, tokenTypeOverrides, balancedBracketSelectors) {\n    this.balancedBracketSelectors = balancedBracketSelectors;\n    this._emitBinaryTokens = emitBinaryTokens;\n    this._tokenTypeOverrides = tokenTypeOverrides;\n    if (false) {\n      this._lineText = lineText;\n    } else {\n      this._lineText = null;\n    }\n    this._tokens = [];\n    this._binaryTokens = [];\n    this._lastTokenEndIndex = 0;\n  }\n  _emitBinaryTokens;\n  /**\n   * defined only if `false`.\n   */\n  _lineText;\n  /**\n   * used only if `_emitBinaryTokens` is false.\n   */\n  _tokens;\n  /**\n   * used only if `_emitBinaryTokens` is true.\n   */\n  _binaryTokens;\n  _lastTokenEndIndex;\n  _tokenTypeOverrides;\n  produce(stack, endIndex) {\n    this.produceFromScopes(stack.contentNameScopesList, endIndex);\n  }\n  produceFromScopes(scopesList, endIndex) {\n    if (this._lastTokenEndIndex >= endIndex) {\n      return;\n    }\n    if (this._emitBinaryTokens) {\n      let metadata = scopesList?.tokenAttributes ?? 0;\n      let containsBalancedBrackets = false;\n      if (this.balancedBracketSelectors?.matchesAlways) {\n        containsBalancedBrackets = true;\n      }\n      if (this._tokenTypeOverrides.length > 0 || this.balancedBracketSelectors && !this.balancedBracketSelectors.matchesAlways && !this.balancedBracketSelectors.matchesNever) {\n        const scopes2 = scopesList?.getScopeNames() ?? [];\n        for (const tokenType of this._tokenTypeOverrides) {\n          if (tokenType.matcher(scopes2)) {\n            metadata = EncodedTokenMetadata.set(\n              metadata,\n              0,\n              toOptionalTokenType(tokenType.type),\n              null,\n              -1 /* NotSet */,\n              0,\n              0\n            );\n          }\n        }\n        if (this.balancedBracketSelectors) {\n          containsBalancedBrackets = this.balancedBracketSelectors.match(scopes2);\n        }\n      }\n      if (containsBalancedBrackets) {\n        metadata = EncodedTokenMetadata.set(\n          metadata,\n          0,\n          8 /* NotSet */,\n          containsBalancedBrackets,\n          -1 /* NotSet */,\n          0,\n          0\n        );\n      }\n      if (this._binaryTokens.length > 0 && this._binaryTokens[this._binaryTokens.length - 1] === metadata) {\n        this._lastTokenEndIndex = endIndex;\n        return;\n      }\n      this._binaryTokens.push(this._lastTokenEndIndex);\n      this._binaryTokens.push(metadata);\n      this._lastTokenEndIndex = endIndex;\n      return;\n    }\n    const scopes = scopesList?.getScopeNames() ?? [];\n    this._tokens.push({\n      startIndex: this._lastTokenEndIndex,\n      endIndex,\n      // value: lineText.substring(lastTokenEndIndex, endIndex),\n      scopes\n    });\n    this._lastTokenEndIndex = endIndex;\n  }\n  getResult(stack, lineLength) {\n    if (this._tokens.length > 0 && this._tokens[this._tokens.length - 1].startIndex === lineLength - 1) {\n      this._tokens.pop();\n    }\n    if (this._tokens.length === 0) {\n      this._lastTokenEndIndex = -1;\n      this.produce(stack, lineLength);\n      this._tokens[this._tokens.length - 1].startIndex = 0;\n    }\n    return this._tokens;\n  }\n  getBinaryResult(stack, lineLength) {\n    if (this._binaryTokens.length > 0 && this._binaryTokens[this._binaryTokens.length - 2] === lineLength - 1) {\n      this._binaryTokens.pop();\n      this._binaryTokens.pop();\n    }\n    if (this._binaryTokens.length === 0) {\n      this._lastTokenEndIndex = -1;\n      this.produce(stack, lineLength);\n      this._binaryTokens[this._binaryTokens.length - 2] = 0;\n    }\n    const result = new Uint32Array(this._binaryTokens.length);\n    for (let i = 0, len = this._binaryTokens.length; i < len; i++) {\n      result[i] = this._binaryTokens[i];\n    }\n    return result;\n  }\n};\n\n// src/registry.ts\nvar SyncRegistry = class {\n  constructor(theme, _onigLib) {\n    this._onigLib = _onigLib;\n    this._theme = theme;\n  }\n  _grammars = /* @__PURE__ */ new Map();\n  _rawGrammars = /* @__PURE__ */ new Map();\n  _injectionGrammars = /* @__PURE__ */ new Map();\n  _theme;\n  dispose() {\n    for (const grammar of this._grammars.values()) {\n      grammar.dispose();\n    }\n  }\n  setTheme(theme) {\n    this._theme = theme;\n  }\n  getColorMap() {\n    return this._theme.getColorMap();\n  }\n  /**\n   * Add `grammar` to registry and return a list of referenced scope names\n   */\n  addGrammar(grammar, injectionScopeNames) {\n    this._rawGrammars.set(grammar.scopeName, grammar);\n    if (injectionScopeNames) {\n      this._injectionGrammars.set(grammar.scopeName, injectionScopeNames);\n    }\n  }\n  /**\n   * Lookup a raw grammar.\n   */\n  lookup(scopeName) {\n    return this._rawGrammars.get(scopeName);\n  }\n  /**\n   * Returns the injections for the given grammar\n   */\n  injections(targetScope) {\n    return this._injectionGrammars.get(targetScope);\n  }\n  /**\n   * Get the default theme settings\n   */\n  getDefaults() {\n    return this._theme.getDefaults();\n  }\n  /**\n   * Match a scope in the theme.\n   */\n  themeMatch(scopePath) {\n    return this._theme.match(scopePath);\n  }\n  /**\n   * Lookup a grammar.\n   */\n  grammarForScopeName(scopeName, initialLanguage, embeddedLanguages, tokenTypes, balancedBracketSelectors) {\n    if (!this._grammars.has(scopeName)) {\n      let rawGrammar = this._rawGrammars.get(scopeName);\n      if (!rawGrammar) {\n        return null;\n      }\n      this._grammars.set(scopeName, createGrammar(\n        scopeName,\n        rawGrammar,\n        initialLanguage,\n        embeddedLanguages,\n        tokenTypes,\n        balancedBracketSelectors,\n        this,\n        this._onigLib\n      ));\n    }\n    return this._grammars.get(scopeName);\n  }\n};\n\n// src/index.ts\nvar Registry = class {\n  _options;\n  _syncRegistry;\n  _ensureGrammarCache;\n  constructor(options) {\n    this._options = options;\n    this._syncRegistry = new SyncRegistry(\n      Theme.createFromRawTheme(options.theme, options.colorMap),\n      options.onigLib\n    );\n    this._ensureGrammarCache = /* @__PURE__ */ new Map();\n  }\n  dispose() {\n    this._syncRegistry.dispose();\n  }\n  /**\n   * Change the theme. Once called, no previous `ruleStack` should be used anymore.\n   */\n  setTheme(theme, colorMap) {\n    this._syncRegistry.setTheme(Theme.createFromRawTheme(theme, colorMap));\n  }\n  /**\n   * Returns a lookup array for color ids.\n   */\n  getColorMap() {\n    return this._syncRegistry.getColorMap();\n  }\n  /**\n   * Load the grammar for `scopeName` and all referenced included grammars asynchronously.\n   * Please do not use language id 0.\n   */\n  loadGrammarWithEmbeddedLanguages(initialScopeName, initialLanguage, embeddedLanguages) {\n    return this.loadGrammarWithConfiguration(initialScopeName, initialLanguage, { embeddedLanguages });\n  }\n  /**\n   * Load the grammar for `scopeName` and all referenced included grammars asynchronously.\n   * Please do not use language id 0.\n   */\n  loadGrammarWithConfiguration(initialScopeName, initialLanguage, configuration) {\n    return this._loadGrammar(\n      initialScopeName,\n      initialLanguage,\n      configuration.embeddedLanguages,\n      configuration.tokenTypes,\n      new BalancedBracketSelectors(\n        configuration.balancedBracketSelectors || [],\n        configuration.unbalancedBracketSelectors || []\n      )\n    );\n  }\n  /**\n   * Load the grammar for `scopeName` and all referenced included grammars asynchronously.\n   */\n  loadGrammar(initialScopeName) {\n    return this._loadGrammar(initialScopeName, 0, null, null, null);\n  }\n  _loadGrammar(initialScopeName, initialLanguage, embeddedLanguages, tokenTypes, balancedBracketSelectors) {\n    const dependencyProcessor = new ScopeDependencyProcessor(this._syncRegistry, initialScopeName);\n    while (dependencyProcessor.Q.length > 0) {\n      dependencyProcessor.Q.map((request) => this._loadSingleGrammar(request.scopeName));\n      dependencyProcessor.processQueue();\n    }\n    return this._grammarForScopeName(\n      initialScopeName,\n      initialLanguage,\n      embeddedLanguages,\n      tokenTypes,\n      balancedBracketSelectors\n    );\n  }\n  _loadSingleGrammar(scopeName) {\n    if (!this._ensureGrammarCache.has(scopeName)) {\n      this._doLoadSingleGrammar(scopeName);\n      this._ensureGrammarCache.set(scopeName, true);\n    }\n  }\n  _doLoadSingleGrammar(scopeName) {\n    const grammar = this._options.loadGrammar(scopeName);\n    if (grammar) {\n      const injections = typeof this._options.getInjections === \"function\" ? this._options.getInjections(scopeName) : void 0;\n      this._syncRegistry.addGrammar(grammar, injections);\n    }\n  }\n  /**\n   * Adds a rawGrammar.\n   */\n  addGrammar(rawGrammar, injections = [], initialLanguage = 0, embeddedLanguages = null) {\n    this._syncRegistry.addGrammar(rawGrammar, injections);\n    return this._grammarForScopeName(rawGrammar.scopeName, initialLanguage, embeddedLanguages);\n  }\n  /**\n   * Get the grammar for `scopeName`. The grammar must first be created via `loadGrammar` or `addGrammar`.\n   */\n  _grammarForScopeName(scopeName, initialLanguage = 0, embeddedLanguages = null, tokenTypes = null, balancedBracketSelectors = null) {\n    return this._syncRegistry.grammarForScopeName(\n      scopeName,\n      initialLanguage,\n      embeddedLanguages,\n      tokenTypes,\n      balancedBracketSelectors\n    );\n  }\n};\nvar INITIAL = StateStackImpl.NULL;\nexport {\n  EncodedTokenMetadata,\n  FindOption,\n  FontStyle,\n  INITIAL,\n  Registry,\n  Theme,\n  disposeOnigString\n};\n"], "names": [], "mappings": "AAAA,eAAe;;;;;;;;;;AAsvDO;AArvDtB,SAAS,MAAM,SAAS;IACtB,OAAO,QAAQ;AACjB;AACA,SAAS,QAAQ,SAAS;IACxB,IAAI,MAAM,OAAO,CAAC,YAAY;QAC5B,OAAO,WAAW;IACpB;IACA,IAAI,qBAAqB,QAAQ;QAC/B,OAAO;IACT;IACA,IAAI,OAAO,cAAc,UAAU;QACjC,OAAO,SAAS;IAClB;IACA,OAAO;AACT;AACA,SAAS,WAAW,GAAG;IACrB,IAAI,IAAI,EAAE;IACV,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,MAAM,EAAE,IAAI,KAAK,IAAK;QAC9C,CAAC,CAAC,EAAE,GAAG,QAAQ,GAAG,CAAC,EAAE;IACvB;IACA,OAAO;AACT;AACA,SAAS,SAAS,GAAG;IACnB,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,OAAO,IAAK;QACnB,CAAC,CAAC,IAAI,GAAG,QAAQ,GAAG,CAAC,IAAI;IAC3B;IACA,OAAO;AACT;AACA,SAAS,aAAa,MAAM,EAAE,GAAG,OAAO;IACtC,QAAQ,OAAO,CAAC,CAAC;QACf,IAAK,IAAI,OAAO,OAAQ;YACtB,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;QAC3B;IACF;IACA,OAAO;AACT;AACA,SAAS,SAAS,IAAI;IACpB,MAAM,MAAM,CAAC,KAAK,WAAW,CAAC,QAAQ,CAAC,KAAK,WAAW,CAAC;IACxD,IAAI,QAAQ,GAAG;QACb,OAAO;IACT,OAAO,IAAI,CAAC,QAAQ,KAAK,MAAM,GAAG,GAAG;QACnC,OAAO,SAAS,KAAK,SAAS,CAAC,GAAG,KAAK,MAAM,GAAG;IAClD,OAAO;QACL,OAAO,KAAK,MAAM,CAAC,CAAC,MAAM;IAC5B;AACF;AACA,IAAI,yBAAyB;AAC7B,IAAI,cAAc;IAChB,OAAO,YAAY,WAAW,EAAE;QAC9B,IAAI,gBAAgB,MAAM;YACxB,OAAO;QACT;QACA,uBAAuB,SAAS,GAAG;QACnC,OAAO,uBAAuB,IAAI,CAAC;IACrC;IACA,OAAO,gBAAgB,WAAW,EAAE,aAAa,EAAE,cAAc,EAAE;QACjE,OAAO,YAAY,OAAO,CAAC,wBAAwB,CAAC,OAAO,OAAO,cAAc;YAC9E,IAAI,UAAU,cAAc,CAAC,SAAS,SAAS,cAAc,IAAI;YACjE,IAAI,SAAS;gBACX,IAAI,SAAS,cAAc,SAAS,CAAC,QAAQ,KAAK,EAAE,QAAQ,GAAG;gBAC/D,MAAO,MAAM,CAAC,EAAE,KAAK,IAAK;oBACxB,SAAS,OAAO,SAAS,CAAC;gBAC5B;gBACA,OAAQ;oBACN,KAAK;wBACH,OAAO,OAAO,WAAW;oBAC3B,KAAK;wBACH,OAAO,OAAO,WAAW;oBAC3B;wBACE,OAAO;gBACX;YACF,OAAO;gBACL,OAAO;YACT;QACF;IACF;AACF;AACA,SAAS,OAAO,CAAC,EAAE,CAAC;IAClB,IAAI,IAAI,GAAG;QACT,OAAO,CAAC;IACV;IACA,IAAI,IAAI,GAAG;QACT,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS,UAAU,CAAC,EAAE,CAAC;IACrB,IAAI,MAAM,QAAQ,MAAM,MAAM;QAC5B,OAAO;IACT;IACA,IAAI,CAAC,GAAG;QACN,OAAO,CAAC;IACV;IACA,IAAI,CAAC,GAAG;QACN,OAAO;IACT;IACA,IAAI,OAAO,EAAE,MAAM;IACnB,IAAI,OAAO,EAAE,MAAM;IACnB,IAAI,SAAS,MAAM;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;YAC7B,IAAI,MAAM,OAAO,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE;YAC3B,IAAI,QAAQ,GAAG;gBACb,OAAO;YACT;QACF;QACA,OAAO;IACT;IACA,OAAO,OAAO;AAChB;AACA,SAAS,gBAAgB,GAAG;IAC1B,IAAI,kBAAkB,IAAI,CAAC,MAAM;QAC/B,OAAO;IACT;IACA,IAAI,kBAAkB,IAAI,CAAC,MAAM;QAC/B,OAAO;IACT;IACA,IAAI,kBAAkB,IAAI,CAAC,MAAM;QAC/B,OAAO;IACT;IACA,IAAI,kBAAkB,IAAI,CAAC,MAAM;QAC/B,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS,uBAAuB,KAAK;IACnC,OAAO,MAAM,OAAO,CAAC,2CAA2C;AAClE;AACA,IAAI,WAAW;IACb,YAAY,EAAE,CAAE;QACd,IAAI,CAAC,EAAE,GAAG;IACZ;IACA,QAAQ,aAAa,GAAG,IAAI,MAAM;IAClC,IAAI,GAAG,EAAE;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM;YACvB,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QACxB;QACA,MAAM,QAAQ,IAAI,CAAC,EAAE,CAAC;QACtB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;QACpB,OAAO;IACT;AACF;AAEA,eAAe;AACf,IAAI,QAAQ;IACV,YAAY,SAAS,EAAE,SAAS,EAAE,KAAK,CAAE;QACvC,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,KAAK,GAAG;IACf;IACA,OAAO,mBAAmB,MAAM,EAAE,QAAQ,EAAE;QAC1C,OAAO,IAAI,CAAC,qBAAqB,CAAC,WAAW,SAAS;IACxD;IACA,OAAO,sBAAsB,MAAM,EAAE,QAAQ,EAAE;QAC7C,OAAO,wBAAwB,QAAQ;IACzC;IACA,mBAAmB,IAAI,SACrB,CAAC,YAAc,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,YAChC;IACF,cAAc;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW;IACnC;IACA,cAAc;QACZ,OAAO,IAAI,CAAC,SAAS;IACvB;IACA,MAAM,SAAS,EAAE;QACf,IAAI,cAAc,MAAM;YACtB,OAAO,IAAI,CAAC,SAAS;QACvB;QACA,MAAM,YAAY,UAAU,SAAS;QACrC,MAAM,uBAAuB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC;QACvD,MAAM,gBAAgB,qBAAqB,IAAI,CAC7C,CAAC,IAAM,8BAA8B,UAAU,MAAM,EAAE,EAAE,YAAY;QAEvE,IAAI,CAAC,eAAe;YAClB,OAAO;QACT;QACA,OAAO,IAAI,gBACT,cAAc,SAAS,EACvB,cAAc,UAAU,EACxB,cAAc,UAAU;IAE5B;AACF;AACA,IAAI,aAAa,MAAM;IACrB,YAAY,MAAM,EAAE,SAAS,CAAE;QAC7B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,SAAS,GAAG;IACnB;IACA,OAAO,KAAK,IAAI,EAAE,UAAU,EAAE;QAC5B,KAAK,MAAM,QAAQ,WAAY;YAC7B,OAAO,IAAI,YAAY,MAAM;QAC/B;QACA,OAAO;IACT;IACA,OAAO,KAAK,GAAG,QAAQ,EAAE;QACvB,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACxC,SAAS,IAAI,YAAY,QAAQ,QAAQ,CAAC,EAAE;QAC9C;QACA,OAAO;IACT;IACA,KAAK,SAAS,EAAE;QACd,OAAO,IAAI,YAAY,IAAI,EAAE;IAC/B;IACA,cAAc;QACZ,IAAI,OAAO,IAAI;QACf,MAAM,SAAS,EAAE;QACjB,MAAO,KAAM;YACX,OAAO,IAAI,CAAC,KAAK,SAAS;YAC1B,OAAO,KAAK,MAAM;QACpB;QACA,OAAO,OAAO;QACd,OAAO;IACT;IACA,WAAW;QACT,OAAO,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IACjC;IACA,QAAQ,KAAK,EAAE;QACb,IAAI,IAAI,KAAK,OAAO;YAClB,OAAO;QACT;QACA,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM;YACxB,OAAO;QACT;QACA,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;IAC7B;IACA,sBAAsB,IAAI,EAAE;QAC1B,MAAM,SAAS,EAAE;QACjB,IAAI,OAAO,IAAI;QACf,MAAO,QAAQ,SAAS,KAAM;YAC5B,OAAO,IAAI,CAAC,KAAK,SAAS;YAC1B,OAAO,KAAK,MAAM;QACpB;QACA,OAAO,SAAS,OAAO,OAAO,OAAO,KAAK,KAAK;IACjD;AACF;AACA,SAAS,8BAA8B,SAAS,EAAE,YAAY;IAC5D,IAAI,aAAa,MAAM,KAAK,GAAG;QAC7B,OAAO;IACT;IACA,IAAK,IAAI,QAAQ,GAAG,QAAQ,aAAa,MAAM,EAAE,QAAS;QACxD,IAAI,eAAe,YAAY,CAAC,MAAM;QACtC,IAAI,iBAAiB;QACrB,IAAI,iBAAiB,KAAK;YACxB,IAAI,UAAU,aAAa,MAAM,GAAG,GAAG;gBACrC,OAAO;YACT;YACA,eAAe,YAAY,CAAC,EAAE,MAAM;YACpC,iBAAiB;QACnB;QACA,MAAO,UAAW;YAChB,IAAI,cAAc,UAAU,SAAS,EAAE,eAAe;gBACpD;YACF;YACA,IAAI,gBAAgB;gBAClB,OAAO;YACT;YACA,YAAY,UAAU,MAAM;QAC9B;QACA,IAAI,CAAC,WAAW;YACd,OAAO;QACT;QACA,YAAY,UAAU,MAAM;IAC9B;IACA,OAAO;AACT;AACA,SAAS,cAAc,SAAS,EAAE,YAAY;IAC5C,OAAO,iBAAiB,aAAa,UAAU,UAAU,CAAC,iBAAiB,SAAS,CAAC,aAAa,MAAM,CAAC,KAAK;AAChH;AACA,IAAI,kBAAkB;IACpB,YAAY,SAAS,EAAE,YAAY,EAAE,YAAY,CAAE;QACjD,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,YAAY,GAAG;IACtB;AACF;AACA,SAAS,WAAW,MAAM;IACxB,IAAI,CAAC,QAAQ;QACX,OAAO,EAAE;IACX;IACA,IAAI,CAAC,OAAO,QAAQ,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO,QAAQ,GAAG;QACvD,OAAO,EAAE;IACX;IACA,IAAI,WAAW,OAAO,QAAQ;IAC9B,IAAI,SAAS,EAAE,EAAE,YAAY;IAC7B,IAAK,IAAI,IAAI,GAAG,MAAM,SAAS,MAAM,EAAE,IAAI,KAAK,IAAK;QACnD,IAAI,QAAQ,QAAQ,CAAC,EAAE;QACvB,IAAI,CAAC,MAAM,QAAQ,EAAE;YACnB;QACF;QACA,IAAI;QACJ,IAAI,OAAO,MAAM,KAAK,KAAK,UAAU;YACnC,IAAI,SAAS,MAAM,KAAK;YACxB,SAAS,OAAO,OAAO,CAAC,SAAS;YACjC,SAAS,OAAO,OAAO,CAAC,SAAS;YACjC,SAAS,OAAO,KAAK,CAAC;QACxB,OAAO,IAAI,MAAM,OAAO,CAAC,MAAM,KAAK,GAAG;YACrC,SAAS,MAAM,KAAK;QACtB,OAAO;YACL,SAAS;gBAAC;aAAG;QACf;QACA,IAAI,YAAY,CAAC,EAAE,UAAU;QAC7B,IAAI,OAAO,MAAM,QAAQ,CAAC,SAAS,KAAK,UAAU;YAChD,YAAY,EAAE,QAAQ;YACtB,IAAI,WAAW,MAAM,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC;YAC9C,IAAK,IAAI,IAAI,GAAG,OAAO,SAAS,MAAM,EAAE,IAAI,MAAM,IAAK;gBACrD,IAAI,UAAU,QAAQ,CAAC,EAAE;gBACzB,OAAQ;oBACN,KAAK;wBACH,YAAY,YAAY,EAAE,UAAU;wBACpC;oBACF,KAAK;wBACH,YAAY,YAAY,EAAE,QAAQ;wBAClC;oBACF,KAAK;wBACH,YAAY,YAAY,EAAE,aAAa;wBACvC;oBACF,KAAK;wBACH,YAAY,YAAY,EAAE,iBAAiB;wBAC3C;gBACJ;YACF;QACF;QACA,IAAI,aAAa;QACjB,IAAI,OAAO,MAAM,QAAQ,CAAC,UAAU,KAAK,YAAY,gBAAgB,MAAM,QAAQ,CAAC,UAAU,GAAG;YAC/F,aAAa,MAAM,QAAQ,CAAC,UAAU;QACxC;QACA,IAAI,aAAa;QACjB,IAAI,OAAO,MAAM,QAAQ,CAAC,UAAU,KAAK,YAAY,gBAAgB,MAAM,QAAQ,CAAC,UAAU,GAAG;YAC/F,aAAa,MAAM,QAAQ,CAAC,UAAU;QACxC;QACA,IAAK,IAAI,IAAI,GAAG,OAAO,OAAO,MAAM,EAAE,IAAI,MAAM,IAAK;YACnD,IAAI,SAAS,MAAM,CAAC,EAAE,CAAC,IAAI;YAC3B,IAAI,WAAW,OAAO,KAAK,CAAC;YAC5B,IAAI,QAAQ,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;YACzC,IAAI,eAAe;YACnB,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,eAAe,SAAS,KAAK,CAAC,GAAG,SAAS,MAAM,GAAG;gBACnD,aAAa,OAAO;YACtB;YACA,MAAM,CAAC,YAAY,GAAG,IAAI,gBACxB,OACA,cACA,GACA,WACA,YACA;QAEJ;IACF;IACA,OAAO;AACT;AACA,IAAI,kBAAkB;IACpB,YAAY,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAE;QACzE,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,UAAU,GAAG;IACpB;AACF;AACA,IAAI,YAAY,aAAa,GAAG,CAAC,CAAC;IAChC,UAAU,CAAC,UAAU,CAAC,SAAS,GAAG,CAAC,EAAE,GAAG;IACxC,UAAU,CAAC,UAAU,CAAC,OAAO,GAAG,EAAE,GAAG;IACrC,UAAU,CAAC,UAAU,CAAC,SAAS,GAAG,EAAE,GAAG;IACvC,UAAU,CAAC,UAAU,CAAC,OAAO,GAAG,EAAE,GAAG;IACrC,UAAU,CAAC,UAAU,CAAC,YAAY,GAAG,EAAE,GAAG;IAC1C,UAAU,CAAC,UAAU,CAAC,gBAAgB,GAAG,EAAE,GAAG;IAC9C,OAAO;AACT,CAAC,EAAE,aAAa,CAAC;AACjB,SAAS,wBAAwB,gBAAgB,EAAE,SAAS;IAC1D,iBAAiB,IAAI,CAAC,CAAC,GAAG;QACxB,IAAI,IAAI,OAAO,EAAE,KAAK,EAAE,EAAE,KAAK;QAC/B,IAAI,MAAM,GAAG;YACX,OAAO;QACT;QACA,IAAI,UAAU,EAAE,YAAY,EAAE,EAAE,YAAY;QAC5C,IAAI,MAAM,GAAG;YACX,OAAO;QACT;QACA,OAAO,EAAE,KAAK,GAAG,EAAE,KAAK;IAC1B;IACA,IAAI,mBAAmB,EAAE,QAAQ;IACjC,IAAI,oBAAoB;IACxB,IAAI,oBAAoB;IACxB,MAAO,iBAAiB,MAAM,IAAI,KAAK,gBAAgB,CAAC,EAAE,CAAC,KAAK,KAAK,GAAI;QACvE,IAAI,mBAAmB,iBAAiB,KAAK;QAC7C,IAAI,iBAAiB,SAAS,KAAK,CAAC,EAAE,UAAU,KAAI;YAClD,mBAAmB,iBAAiB,SAAS;QAC/C;QACA,IAAI,iBAAiB,UAAU,KAAK,MAAM;YACxC,oBAAoB,iBAAiB,UAAU;QACjD;QACA,IAAI,iBAAiB,UAAU,KAAK,MAAM;YACxC,oBAAoB,iBAAiB,UAAU;QACjD;IACF;IACA,IAAI,WAAW,IAAI,SAAS;IAC5B,IAAI,WAAW,IAAI,gBAAgB,kBAAkB,SAAS,KAAK,CAAC,oBAAoB,SAAS,KAAK,CAAC;IACvG,IAAI,OAAO,IAAI,iBAAiB,IAAI,qBAAqB,GAAG,MAAM,CAAC,EAAE,UAAU,KAAI,GAAG,IAAI,EAAE;IAC5F,IAAK,IAAI,IAAI,GAAG,MAAM,iBAAiB,MAAM,EAAE,IAAI,KAAK,IAAK;QAC3D,IAAI,OAAO,gBAAgB,CAAC,EAAE;QAC9B,KAAK,MAAM,CAAC,GAAG,KAAK,KAAK,EAAE,KAAK,YAAY,EAAE,KAAK,SAAS,EAAE,SAAS,KAAK,CAAC,KAAK,UAAU,GAAG,SAAS,KAAK,CAAC,KAAK,UAAU;IAC/H;IACA,OAAO,IAAI,MAAM,UAAU,UAAU;AACvC;AACA,IAAI,WAAW;IACb,UAAU;IACV,aAAa;IACb,UAAU;IACV,UAAU;IACV,YAAY,SAAS,CAAE;QACrB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,SAAS,GAAG,EAAE;QACnB,IAAI,CAAC,SAAS,GAAG,aAAa,GAAG,OAAO,MAAM,CAAC;QAC/C,IAAI,MAAM,OAAO,CAAC,YAAY;YAC5B,IAAI,CAAC,SAAS,GAAG;YACjB,IAAK,IAAI,IAAI,GAAG,MAAM,UAAU,MAAM,EAAE,IAAI,KAAK,IAAK;gBACpD,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG;gBAC/B,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE;YAClC;QACF,OAAO;YACL,IAAI,CAAC,SAAS,GAAG;QACnB;IACF;IACA,MAAM,KAAK,EAAE;QACX,IAAI,UAAU,MAAM;YAClB,OAAO;QACT;QACA,QAAQ,MAAM,WAAW;QACzB,IAAI,QAAQ,IAAI,CAAC,SAAS,CAAC,MAAM;QACjC,IAAI,OAAO;YACT,OAAO;QACT;QACA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,OAAO;QACzD;QACA,QAAQ,EAAE,IAAI,CAAC,YAAY;QAC3B,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG;QACxB,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG;QACxB,OAAO;IACT;IACA,cAAc;QACZ,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;IAC9B;AACF;AACA,IAAI,oBAAoB,OAAO,MAAM,CAAC,EAAE;AACxC,IAAI,uBAAuB,MAAM;IAC/B,WAAW;IACX,aAAa;IACb,UAAU;IACV,WAAW;IACX,WAAW;IACX,YAAY,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAE;QACvE,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,YAAY,GAAG,gBAAgB;QACpC,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,UAAU,GAAG;IACpB;IACA,QAAQ;QACN,OAAO,IAAI,sBAAsB,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,UAAU;IACvH;IACA,OAAO,SAAS,GAAG,EAAE;QACnB,IAAI,IAAI,EAAE;QACV,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,MAAM,EAAE,IAAI,KAAK,IAAK;YAC9C,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,KAAK;QACrB;QACA,OAAO;IACT;IACA,gBAAgB,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE;QAC7D,IAAI,IAAI,CAAC,UAAU,GAAG,YAAY;YAChC,QAAQ,GAAG,CAAC;QACd,OAAO;YACL,IAAI,CAAC,UAAU,GAAG;QACpB;QACA,IAAI,cAAc,CAAC,EAAE,UAAU,KAAI;YACjC,IAAI,CAAC,SAAS,GAAG;QACnB;QACA,IAAI,eAAe,GAAG;YACpB,IAAI,CAAC,UAAU,GAAG;QACpB;QACA,IAAI,eAAe,GAAG;YACpB,IAAI,CAAC,UAAU,GAAG;QACpB;IACF;AACF;AACA,IAAI,mBAAmB,MAAM;IAC3B,YAAY,SAAS,EAAE,wBAAwB,EAAE,EAAE,YAAY,CAAC,CAAC,CAAE;QACjE,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,sBAAsB,GAAG;IAChC;IACA,uBAAuB;IACvB,OAAO,kBAAkB,CAAC,EAAE,CAAC,EAAE;QAC7B,IAAI,EAAE,UAAU,KAAK,EAAE,UAAU,EAAE;YACjC,OAAO,EAAE,UAAU,GAAG,EAAE,UAAU;QACpC;QACA,IAAI,eAAe;QACnB,IAAI,eAAe;QACnB,MAAO,KAAM;YACX,IAAI,EAAE,YAAY,CAAC,aAAa,KAAK,KAAK;gBACxC;YACF;YACA,IAAI,EAAE,YAAY,CAAC,aAAa,KAAK,KAAK;gBACxC;YACF;YACA,IAAI,gBAAgB,EAAE,YAAY,CAAC,MAAM,IAAI,gBAAgB,EAAE,YAAY,CAAC,MAAM,EAAE;gBAClF;YACF;YACA,MAAM,wBAAwB,EAAE,YAAY,CAAC,aAAa,CAAC,MAAM,GAAG,EAAE,YAAY,CAAC,aAAa,CAAC,MAAM;YACvG,IAAI,0BAA0B,GAAG;gBAC/B,OAAO;YACT;YACA;YACA;QACF;QACA,OAAO,EAAE,YAAY,CAAC,MAAM,GAAG,EAAE,YAAY,CAAC,MAAM;IACtD;IACA,MAAM,KAAK,EAAE;QACX,IAAI,UAAU,IAAI;YAChB,IAAI,WAAW,MAAM,OAAO,CAAC;YAC7B,IAAI;YACJ,IAAI;YACJ,IAAI,aAAa,CAAC,GAAG;gBACnB,OAAO;gBACP,OAAO;YACT,OAAO;gBACL,OAAO,MAAM,SAAS,CAAC,GAAG;gBAC1B,OAAO,MAAM,SAAS,CAAC,WAAW;YACpC;YACA,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO;gBACvC,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC;YACpC;QACF;QACA,MAAM,QAAQ,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS;QAC/D,MAAM,IAAI,CAAC,kBAAkB,iBAAiB;QAC9C,OAAO;IACT;IACA,OAAO,UAAU,EAAE,KAAK,EAAE,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE;QACzE,IAAI,UAAU,IAAI;YAChB,IAAI,CAAC,aAAa,CAAC,YAAY,cAAc,WAAW,YAAY;YACpE;QACF;QACA,IAAI,WAAW,MAAM,OAAO,CAAC;QAC7B,IAAI;QACJ,IAAI;QACJ,IAAI,aAAa,CAAC,GAAG;YACnB,OAAO;YACP,OAAO;QACT,OAAO;YACL,OAAO,MAAM,SAAS,CAAC,GAAG;YAC1B,OAAO,MAAM,SAAS,CAAC,WAAW;QACpC;QACA,IAAI;QACJ,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,OAAO;YACvC,QAAQ,IAAI,CAAC,SAAS,CAAC,KAAK;QAC9B,OAAO;YACL,QAAQ,IAAI,kBAAkB,IAAI,CAAC,SAAS,CAAC,KAAK,IAAI,qBAAqB,QAAQ,CAAC,IAAI,CAAC,sBAAsB;YAC/G,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG;QACzB;QACA,MAAM,MAAM,CAAC,aAAa,GAAG,MAAM,cAAc,WAAW,YAAY;IAC1E;IACA,cAAc,UAAU,EAAE,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE;QACzE,IAAI,iBAAiB,MAAM;YACzB,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,YAAY,WAAW,YAAY;YAClE;QACF;QACA,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,IAAI,KAAK,IAAK;YACtE,IAAI,OAAO,IAAI,CAAC,sBAAsB,CAAC,EAAE;YACzC,IAAI,UAAU,KAAK,YAAY,EAAE,kBAAkB,GAAG;gBACpD,KAAK,eAAe,CAAC,YAAY,WAAW,YAAY;gBACxD;YACF;QACF;QACA,IAAI,cAAc,CAAC,EAAE,UAAU,KAAI;YACjC,YAAY,IAAI,CAAC,SAAS,CAAC,SAAS;QACtC;QACA,IAAI,eAAe,GAAG;YACpB,aAAa,IAAI,CAAC,SAAS,CAAC,UAAU;QACxC;QACA,IAAI,eAAe,GAAG;YACpB,aAAa,IAAI,CAAC,SAAS,CAAC,UAAU;QACxC;QACA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,qBAAqB,YAAY,cAAc,WAAW,YAAY;IAC7G;AACF;AAEA,gCAAgC;AAChC,IAAI,uBAAuB,MAAM;IAC/B,OAAO,YAAY,sBAAsB,EAAE;QACzC,OAAO,uBAAuB,QAAQ,CAAC,GAAG,QAAQ,CAAC,IAAI;IACzD;IACA,OAAO,MAAM,sBAAsB,EAAE;QACnC,MAAM,aAAa,sBAAsB,aAAa,CAAC;QACvD,MAAM,YAAY,sBAAsB,YAAY,CAAC;QACrD,MAAM,YAAY,sBAAsB,YAAY,CAAC;QACrD,MAAM,aAAa,sBAAsB,aAAa,CAAC;QACvD,MAAM,aAAa,sBAAsB,aAAa,CAAC;QACvD,QAAQ,GAAG,CAAC;YACV;YACA;YACA;YACA;YACA;QACF;IACF;IACA,OAAO,cAAc,sBAAsB,EAAE;QAC3C,OAAO,CAAC,yBAAyB,IAAI,mBAAmB,GAAE,MAAM,EAAE,qBAAqB;IACzF;IACA,OAAO,aAAa,sBAAsB,EAAE;QAC1C,OAAO,CAAC,yBAAyB,IAAI,mBAAmB,GAAE,MAAM,EAAE,qBAAqB;IACzF;IACA,OAAO,yBAAyB,sBAAsB,EAAE;QACtD,OAAO,CAAC,yBAAyB,KAAK,0BAA0B,GAAE,MAAM;IAC1E;IACA,OAAO,aAAa,sBAAsB,EAAE;QAC1C,OAAO,CAAC,yBAAyB,MAAM,mBAAmB,GAAE,MAAM,GAAG,qBAAqB;IAC5F;IACA,OAAO,cAAc,sBAAsB,EAAE;QAC3C,OAAO,CAAC,yBAAyB,SAAS,mBAAmB,GAAE,MAAM,GAAG,qBAAqB;IAC/F;IACA,OAAO,cAAc,sBAAsB,EAAE;QAC3C,OAAO,CAAC,yBAAyB,WAAW,mBAAmB,GAAE,MAAM,GAAG,qBAAqB;IACjG;IACA;;;GAGC,GACD,OAAO,IAAI,sBAAsB,EAAE,UAAU,EAAE,SAAS,EAAE,wBAAwB,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE;QACrH,IAAI,cAAc,sBAAsB,aAAa,CAAC;QACtD,IAAI,aAAa,sBAAsB,YAAY,CAAC;QACpD,IAAI,+BAA+B,sBAAsB,wBAAwB,CAAC,0BAA0B,IAAI;QAChH,IAAI,aAAa,sBAAsB,YAAY,CAAC;QACpD,IAAI,cAAc,sBAAsB,aAAa,CAAC;QACtD,IAAI,cAAc,sBAAsB,aAAa,CAAC;QACtD,IAAI,eAAe,GAAG;YACpB,cAAc;QAChB;QACA,IAAI,cAAc,EAAE,UAAU,KAAI;YAChC,aAAa,sBAAsB;QACrC;QACA,IAAI,6BAA6B,MAAM;YACrC,+BAA+B,2BAA2B,IAAI;QAChE;QACA,IAAI,cAAc,CAAC,EAAE,UAAU,KAAI;YACjC,aAAa;QACf;QACA,IAAI,eAAe,GAAG;YACpB,cAAc;QAChB;QACA,IAAI,eAAe,GAAG;YACpB,cAAc;QAChB;QACA,OAAO,CAAC,eAAe,EAAE,qBAAqB,MAAK,cAAc,EAAE,qBAAqB,MAAK,gCAAgC,GAAG,4BAA4B,MAAK,cAAc,GAAG,qBAAqB,MAAK,eAAe,GAAG,qBAAqB,MAAK,eAAe,GAAG,qBAAqB,GAAE,MAAM;IACzS;AACF;AACA,SAAS,oBAAoB,YAAY;IACvC,OAAO;AACT;AACA,SAAS,sBAAsB,YAAY;IACzC,OAAO;AACT;AAEA,iBAAiB;AACjB,SAAS,eAAe,QAAQ,EAAE,WAAW;IAC3C,MAAM,UAAU,EAAE;IAClB,MAAM,YAAY,aAAa;IAC/B,IAAI,QAAQ,UAAU,IAAI;IAC1B,MAAO,UAAU,KAAM;QACrB,IAAI,WAAW;QACf,IAAI,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,CAAC,OAAO,KAAK;YACjD,OAAQ,MAAM,MAAM,CAAC;gBACnB,KAAK;oBACH,WAAW;oBACX;gBACF,KAAK;oBACH,WAAW,CAAC;oBACZ;gBACF;oBACE,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;YAC7D;YACA,QAAQ,UAAU,IAAI;QACxB;QACA,IAAI,UAAU;QACd,QAAQ,IAAI,CAAC;YAAE;YAAS;QAAS;QACjC,IAAI,UAAU,KAAK;YACjB;QACF;QACA,QAAQ,UAAU,IAAI;IACxB;IACA,OAAO;;IACP,SAAS;QACP,IAAI,UAAU,KAAK;YACjB,QAAQ,UAAU,IAAI;YACtB,MAAM,qBAAqB;YAC3B,OAAO,CAAC,eAAiB,CAAC,CAAC,sBAAsB,CAAC,mBAAmB;QACvE;QACA,IAAI,UAAU,KAAK;YACjB,QAAQ,UAAU,IAAI;YACtB,MAAM,sBAAsB;YAC5B,IAAI,UAAU,KAAK;gBACjB,QAAQ,UAAU,IAAI;YACxB;YACA,OAAO;QACT;QACA,IAAI,aAAa,QAAQ;YACvB,MAAM,cAAc,EAAE;YACtB,GAAG;gBACD,YAAY,IAAI,CAAC;gBACjB,QAAQ,UAAU,IAAI;YACxB,QAAS,aAAa,OAAQ;YAC9B,OAAO,CAAC,eAAiB,YAAY,aAAa;QACpD;QACA,OAAO;IACT;IACA,SAAS;QACP,MAAM,WAAW,EAAE;QACnB,IAAI,UAAU;QACd,MAAO,QAAS;YACd,SAAS,IAAI,CAAC;YACd,UAAU;QACZ;QACA,OAAO,CAAC,eAAiB,SAAS,KAAK,CAAC,CAAC,WAAa,SAAS;IACjE;IACA,SAAS;QACP,MAAM,WAAW,EAAE;QACnB,IAAI,UAAU;QACd,MAAO,QAAS;YACd,SAAS,IAAI,CAAC;YACd,IAAI,UAAU,OAAO,UAAU,KAAK;gBAClC,GAAG;oBACD,QAAQ,UAAU,IAAI;gBACxB,QAAS,UAAU,OAAO,UAAU,IAAK;YAC3C,OAAO;gBACL;YACF;YACA,UAAU;QACZ;QACA,OAAO,CAAC,eAAiB,SAAS,IAAI,CAAC,CAAC,WAAa,SAAS;IAChE;AACF;AACA,SAAS,aAAa,KAAK;IACzB,OAAO,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,KAAK,CAAC;AAClC;AACA,SAAS,aAAa,KAAK;IACzB,IAAI,QAAQ;IACZ,IAAI,QAAQ,MAAM,IAAI,CAAC;IACvB,OAAO;QACL,MAAM;YACJ,IAAI,CAAC,OAAO;gBACV,OAAO;YACT;YACA,MAAM,MAAM,KAAK,CAAC,EAAE;YACpB,QAAQ,MAAM,IAAI,CAAC;YACnB,OAAO;QACT;IACF;AACF;AAEA,iBAAiB;AACjB,IAAI,aAAa,aAAa,GAAG,CAAC,CAAC;IACjC,WAAW,CAAC,WAAW,CAAC,OAAO,GAAG,EAAE,GAAG;IACvC,WAAW,CAAC,WAAW,CAAC,iBAAiB,GAAG,EAAE,GAAG;IACjD,WAAW,CAAC,WAAW,CAAC,eAAe,GAAG,EAAE,GAAG;IAC/C,WAAW,CAAC,WAAW,CAAC,mBAAmB,GAAG,EAAE,GAAG;IACnD,WAAW,CAAC,WAAW,CAAC,YAAY,GAAG,EAAE,GAAG;IAC5C,OAAO;AACT,CAAC,EAAE,cAAc,CAAC;AAClB,SAAS,kBAAkB,GAAG;IAC5B,IAAI,OAAO,IAAI,OAAO,KAAK,YAAY;QACrC,IAAI,OAAO;IACb;AACF;AAEA,qCAAqC;AACrC,IAAI,wBAAwB;IAC1B,YAAY,SAAS,CAAE;QACrB,IAAI,CAAC,SAAS,GAAG;IACnB;IACA,QAAQ;QACN,OAAO,IAAI,CAAC,SAAS;IACvB;AACF;AACA,IAAI,kCAAkC;IACpC,YAAY,SAAS,EAAE,QAAQ,CAAE;QAC/B,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,QAAQ,GAAG;IAClB;IACA,QAAQ;QACN,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE;IAC7C;AACF;AACA,IAAI,6BAA6B;IAC/B,cAAc,EAAE,CAAC;IACjB,qBAAqB,aAAa,GAAG,IAAI,MAAM;IAC/C,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,WAAW;IACzB;IACA,cAAc,aAAa,GAAG,IAAI,MAAM;IACxC,IAAI,SAAS,EAAE;QACb,MAAM,MAAM,UAAU,KAAK;QAC3B,IAAI,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,MAAM;YACpC;QACF;QACA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;QAC5B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;IACxB;AACF;AACA,IAAI,2BAA2B;IAC7B,YAAY,IAAI,EAAE,gBAAgB,CAAE;QAClC,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB;QACpD,IAAI,CAAC,CAAC,GAAG;YAAC,IAAI,sBAAsB,IAAI,CAAC,gBAAgB;SAAE;IAC7D;IACA,wBAAwB,aAAa,GAAG,IAAI,MAAM;IAClD,2BAA2B,aAAa,GAAG,IAAI,MAAM;IACrD,EAAE;IACF,eAAe;QACb,MAAM,IAAI,IAAI,CAAC,CAAC;QAChB,IAAI,CAAC,CAAC,GAAG,EAAE;QACX,MAAM,OAAO,IAAI;QACjB,KAAK,MAAM,OAAO,EAAG;YACnB,6BAA6B,KAAK,IAAI,CAAC,gBAAgB,EAAE,IAAI,CAAC,IAAI,EAAE;QACtE;QACA,KAAK,MAAM,OAAO,KAAK,UAAU,CAAE;YACjC,IAAI,eAAe,uBAAuB;gBACxC,IAAI,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,SAAS,GAAG;oBACjD;gBACF;gBACA,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,SAAS;gBAC5C,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;YACd,OAAO;gBACL,IAAI,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,SAAS,GAAG;oBACjD;gBACF;gBACA,IAAI,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,IAAI,KAAK,KAAK;oBAClD;gBACF;gBACA,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC,IAAI,KAAK;gBAC3C,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;YACd;QACF;IACF;AACF;AACA,SAAS,6BAA6B,SAAS,EAAE,oBAAoB,EAAE,IAAI,EAAE,MAAM;IACjF,MAAM,cAAc,KAAK,MAAM,CAAC,UAAU,SAAS;IACnD,IAAI,CAAC,aAAa;QAChB,IAAI,UAAU,SAAS,KAAK,sBAAsB;YAChD,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,qBAAqB,CAAC,CAAC;QACrE;QACA;IACF;IACA,MAAM,cAAc,KAAK,MAAM,CAAC;IAChC,IAAI,qBAAqB,uBAAuB;QAC9C,wCAAwC;YAAE;YAAa;QAAY,GAAG;IACxE,OAAO;QACL,kDACE,UAAU,QAAQ,EAClB;YAAE;YAAa;YAAa,YAAY,YAAY,UAAU;QAAC,GAC/D;IAEJ;IACA,MAAM,aAAa,KAAK,UAAU,CAAC,UAAU,SAAS;IACtD,IAAI,YAAY;QACd,KAAK,MAAM,aAAa,WAAY;YAClC,OAAO,GAAG,CAAC,IAAI,sBAAsB;QACvC;IACF;AACF;AACA,SAAS,kDAAkD,QAAQ,EAAE,OAAO,EAAE,MAAM;IAClF,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,SAAS,EAAE;QACtD,MAAM,OAAO,QAAQ,UAAU,CAAC,SAAS;QACzC,iCAAiC;YAAC;SAAK,EAAE,SAAS;IACpD;AACF;AACA,SAAS,wCAAwC,OAAO,EAAE,MAAM;IAC9D,IAAI,QAAQ,WAAW,CAAC,QAAQ,IAAI,MAAM,OAAO,CAAC,QAAQ,WAAW,CAAC,QAAQ,GAAG;QAC/E,iCACE,QAAQ,WAAW,CAAC,QAAQ,EAC5B;YAAE,GAAG,OAAO;YAAE,YAAY,QAAQ,WAAW,CAAC,UAAU;QAAC,GACzD;IAEJ;IACA,IAAI,QAAQ,WAAW,CAAC,UAAU,EAAE;QAClC,iCACE,OAAO,MAAM,CAAC,QAAQ,WAAW,CAAC,UAAU,GAC5C;YAAE,GAAG,OAAO;YAAE,YAAY,QAAQ,WAAW,CAAC,UAAU;QAAC,GACzD;IAEJ;AACF;AACA,SAAS,iCAAiC,KAAK,EAAE,OAAO,EAAE,MAAM;IAC9D,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,OAAO,WAAW,CAAC,GAAG,CAAC,OAAO;YAChC;QACF;QACA,OAAO,WAAW,CAAC,GAAG,CAAC;QACvB,MAAM,oBAAoB,KAAK,UAAU,GAAG,aAAa,CAAC,GAAG,QAAQ,UAAU,EAAE,KAAK,UAAU,IAAI,QAAQ,UAAU;QACtH,IAAI,MAAM,OAAO,CAAC,KAAK,QAAQ,GAAG;YAChC,iCAAiC,KAAK,QAAQ,EAAE;gBAAE,GAAG,OAAO;gBAAE,YAAY;YAAkB,GAAG;QACjG;QACA,MAAM,UAAU,KAAK,OAAO;QAC5B,IAAI,CAAC,SAAS;YACZ;QACF;QACA,MAAM,YAAY,aAAa;QAC/B,OAAQ,UAAU,IAAI;YACpB,KAAK,EAAE,QAAQ;gBACb,wCAAwC;oBAAE,GAAG,OAAO;oBAAE,aAAa,QAAQ,WAAW;gBAAC,GAAG;gBAC1F;YACF,KAAK,EAAE,QAAQ;gBACb,wCAAwC,SAAS;gBACjD;YACF,KAAK,EAAE,qBAAqB;gBAC1B,kDAAkD,UAAU,QAAQ,EAAE;oBAAE,GAAG,OAAO;oBAAE,YAAY;gBAAkB,GAAG;gBACrH;YACF,KAAK,EAAE,qBAAqB;YAC5B,KAAK,EAAE,+BAA+B;gBACpC,MAAM,cAAc,UAAU,SAAS,KAAK,QAAQ,WAAW,CAAC,SAAS,GAAG,QAAQ,WAAW,GAAG,UAAU,SAAS,KAAK,QAAQ,WAAW,CAAC,SAAS,GAAG,QAAQ,WAAW,GAAG,KAAK;gBACrL,IAAI,aAAa;oBACf,MAAM,aAAa;wBAAE,aAAa,QAAQ,WAAW;wBAAE;wBAAa,YAAY;oBAAkB;oBAClG,IAAI,UAAU,IAAI,KAAK,EAAE,+BAA+B,KAAI;wBAC1D,kDAAkD,UAAU,QAAQ,EAAE,YAAY;oBACpF,OAAO;wBACL,wCAAwC,YAAY;oBACtD;gBACF,OAAO;oBACL,IAAI,UAAU,IAAI,KAAK,EAAE,+BAA+B,KAAI;wBAC1D,OAAO,GAAG,CAAC,IAAI,gCAAgC,UAAU,SAAS,EAAE,UAAU,QAAQ;oBACxF,OAAO;wBACL,OAAO,GAAG,CAAC,IAAI,sBAAsB,UAAU,SAAS;oBAC1D;gBACF;gBACA;QACJ;IACF;AACF;AACA,IAAI,gBAAgB;IAClB,OAAO,EAAE,QAAQ,IAAG;AACtB;AACA,IAAI,gBAAgB;IAClB,OAAO,EAAE,QAAQ,IAAG;AACtB;AACA,IAAI,oBAAoB;IACtB,YAAY,QAAQ,CAAE;QACpB,IAAI,CAAC,QAAQ,GAAG;IAClB;IACA,OAAO,EAAE,qBAAqB,IAAG;AACnC;AACA,IAAI,oBAAoB;IACtB,YAAY,SAAS,CAAE;QACrB,IAAI,CAAC,SAAS,GAAG;IACnB;IACA,OAAO,EAAE,qBAAqB,IAAG;AACnC;AACA,IAAI,8BAA8B;IAChC,YAAY,SAAS,EAAE,QAAQ,CAAE;QAC/B,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,QAAQ,GAAG;IAClB;IACA,OAAO,EAAE,+BAA+B,IAAG;AAC7C;AACA,SAAS,aAAa,OAAO;IAC3B,IAAI,YAAY,SAAS;QACvB,OAAO,IAAI;IACb,OAAO,IAAI,YAAY,SAAS;QAC9B,OAAO,IAAI;IACb;IACA,MAAM,eAAe,QAAQ,OAAO,CAAC;IACrC,IAAI,iBAAiB,CAAC,GAAG;QACvB,OAAO,IAAI,kBAAkB;IAC/B,OAAO,IAAI,iBAAiB,GAAG;QAC7B,OAAO,IAAI,kBAAkB,QAAQ,SAAS,CAAC;IACjD,OAAO;QACL,MAAM,YAAY,QAAQ,SAAS,CAAC,GAAG;QACvC,MAAM,WAAW,QAAQ,SAAS,CAAC,eAAe;QAClD,OAAO,IAAI,4BAA4B,WAAW;IACpD;AACF;AAEA,cAAc;AACd,IAAI,sBAAsB;AAC1B,IAAI,uBAAuB;AAC3B,IAAI,eAAe,OAAO;AAC1B,IAAI,YAAY,CAAC;AACjB,IAAI,cAAc,CAAC;AACnB,SAAS,iBAAiB,EAAE;IAC1B,OAAO;AACT;AACA,SAAS,eAAe,EAAE;IACxB,OAAO;AACT;AACA,IAAI,OAAO;IACT,UAAU;IACV,GAAG;IACH,iBAAiB;IACjB,MAAM;IACN,wBAAwB;IACxB,aAAa;IACb,YAAY,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,WAAW,CAAE;QAC5C,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,KAAK,GAAG,QAAQ;QACrB,IAAI,CAAC,gBAAgB,GAAG,YAAY,WAAW,CAAC,IAAI,CAAC,KAAK;QAC1D,IAAI,CAAC,YAAY,GAAG,eAAe;QACnC,IAAI,CAAC,uBAAuB,GAAG,YAAY,WAAW,CAAC,IAAI,CAAC,YAAY;IAC1E;IACA,IAAI,YAAY;QACd,MAAM,WAAW,IAAI,CAAC,SAAS,GAAG,GAAG,SAAS,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,GAAG;QAClG,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,UAAU;IAC5D;IACA,QAAQ,QAAQ,EAAE,cAAc,EAAE;QAChC,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,KAAK,KAAK,QAAQ,aAAa,QAAQ,mBAAmB,MAAM;YACjG,OAAO,IAAI,CAAC,KAAK;QACnB;QACA,OAAO,YAAY,eAAe,CAAC,IAAI,CAAC,KAAK,EAAE,UAAU;IAC3D;IACA,eAAe,QAAQ,EAAE,cAAc,EAAE;QACvC,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI,IAAI,CAAC,YAAY,KAAK,MAAM;YAC/D,OAAO,IAAI,CAAC,YAAY;QAC1B;QACA,OAAO,YAAY,eAAe,CAAC,IAAI,CAAC,YAAY,EAAE,UAAU;IAClE;AACF;AACA,IAAI,cAAc,cAAc;IAC9B,6BAA6B;IAC7B,YAAY,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,4BAA4B,CAAE;QAC1E,KAAK,CAAC,WAAW,IAAI,MAAM;QAC3B,IAAI,CAAC,4BAA4B,GAAG;IACtC;IACA,UAAU,CACV;IACA,gBAAgB,OAAO,EAAE,GAAG,EAAE;QAC5B,MAAM,IAAI,MAAM;IAClB;IACA,QAAQ,OAAO,EAAE,cAAc,EAAE;QAC/B,MAAM,IAAI,MAAM;IAClB;IACA,UAAU,OAAO,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE;QACjD,MAAM,IAAI,MAAM;IAClB;AACF;AACA,IAAI,YAAY,cAAc;IAC5B,OAAO;IACP,SAAS;IACT,wBAAwB;IACxB,YAAY,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAE;QAChD,KAAK,CAAC,WAAW,IAAI,MAAM;QAC3B,IAAI,CAAC,MAAM,GAAG,IAAI,aAAa,OAAO,IAAI,CAAC,EAAE;QAC7C,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,uBAAuB,GAAG;IACjC;IACA,UAAU;QACR,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAChC,IAAI,CAAC,uBAAuB,CAAC,OAAO;YACpC,IAAI,CAAC,uBAAuB,GAAG;QACjC;IACF;IACA,IAAI,mBAAmB;QACrB,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;IAChC;IACA,gBAAgB,OAAO,EAAE,GAAG,EAAE;QAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM;IACtB;IACA,QAAQ,OAAO,EAAE,cAAc,EAAE;QAC/B,OAAO,IAAI,CAAC,0BAA0B,CAAC,SAAS,OAAO,CAAC;IAC1D;IACA,UAAU,OAAO,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE;QACjD,OAAO,IAAI,CAAC,0BAA0B,CAAC,SAAS,SAAS,CAAC,SAAS,QAAQ;IAC7E;IACA,2BAA2B,OAAO,EAAE;QAClC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACjC,IAAI,CAAC,uBAAuB,GAAG,IAAI;YACnC,IAAI,CAAC,eAAe,CAAC,SAAS,IAAI,CAAC,uBAAuB;QAC5D;QACA,OAAO,IAAI,CAAC,uBAAuB;IACrC;AACF;AACA,IAAI,kBAAkB,cAAc;IAClC,mBAAmB;IACnB,SAAS;IACT,wBAAwB;IACxB,YAAY,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,CAAE;QACtD,KAAK,CAAC,WAAW,IAAI,MAAM;QAC3B,IAAI,CAAC,QAAQ,GAAG,SAAS,QAAQ;QACjC,IAAI,CAAC,kBAAkB,GAAG,SAAS,kBAAkB;QACrD,IAAI,CAAC,uBAAuB,GAAG;IACjC;IACA,UAAU;QACR,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAChC,IAAI,CAAC,uBAAuB,CAAC,OAAO;YACpC,IAAI,CAAC,uBAAuB,GAAG;QACjC;IACF;IACA,gBAAgB,OAAO,EAAE,GAAG,EAAE;QAC5B,KAAK,MAAM,WAAW,IAAI,CAAC,QAAQ,CAAE;YACnC,MAAM,OAAO,QAAQ,OAAO,CAAC;YAC7B,KAAK,eAAe,CAAC,SAAS;QAChC;IACF;IACA,QAAQ,OAAO,EAAE,cAAc,EAAE;QAC/B,OAAO,IAAI,CAAC,0BAA0B,CAAC,SAAS,OAAO,CAAC;IAC1D;IACA,UAAU,OAAO,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE;QACjD,OAAO,IAAI,CAAC,0BAA0B,CAAC,SAAS,SAAS,CAAC,SAAS,QAAQ;IAC7E;IACA,2BAA2B,OAAO,EAAE;QAClC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACjC,IAAI,CAAC,uBAAuB,GAAG,IAAI;YACnC,IAAI,CAAC,eAAe,CAAC,SAAS,IAAI,CAAC,uBAAuB;QAC5D;QACA,OAAO,IAAI,CAAC,uBAAuB;IACrC;AACF;AACA,IAAI,eAAe,cAAc;IAC/B,OAAO;IACP,cAAc;IACd,KAAK;IACL,qBAAqB;IACrB,YAAY;IACZ,oBAAoB;IACpB,mBAAmB;IACnB,SAAS;IACT,wBAAwB;IACxB,YAAY,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,EAAE,WAAW,EAAE,mBAAmB,EAAE,QAAQ,CAAE;QACnH,KAAK,CAAC,WAAW,IAAI,MAAM;QAC3B,IAAI,CAAC,MAAM,GAAG,IAAI,aAAa,OAAO,IAAI,CAAC,EAAE;QAC7C,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,aAAa,MAAM,MAAM,UAAU,CAAC;QACpD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,IAAI,CAAC,iBAAiB;QACvD,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,mBAAmB,GAAG,uBAAuB;QAClD,IAAI,CAAC,QAAQ,GAAG,SAAS,QAAQ;QACjC,IAAI,CAAC,kBAAkB,GAAG,SAAS,kBAAkB;QACrD,IAAI,CAAC,uBAAuB,GAAG;IACjC;IACA,UAAU;QACR,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAChC,IAAI,CAAC,uBAAuB,CAAC,OAAO;YACpC,IAAI,CAAC,uBAAuB,GAAG;QACjC;IACF;IACA,IAAI,mBAAmB;QACrB,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;IAChC;IACA,IAAI,iBAAiB;QACnB,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;IAC9B;IACA,iCAAiC,QAAQ,EAAE,cAAc,EAAE;QACzD,OAAO,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,UAAU;IACnD;IACA,gBAAgB,OAAO,EAAE,GAAG,EAAE;QAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM;IACtB;IACA,QAAQ,OAAO,EAAE,cAAc,EAAE;QAC/B,OAAO,IAAI,CAAC,0BAA0B,CAAC,SAAS,gBAAgB,OAAO,CAAC;IAC1E;IACA,UAAU,OAAO,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE;QACjD,OAAO,IAAI,CAAC,0BAA0B,CAAC,SAAS,gBAAgB,SAAS,CAAC,SAAS,QAAQ;IAC7F;IACA,2BAA2B,OAAO,EAAE,cAAc,EAAE;QAClD,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACjC,IAAI,CAAC,uBAAuB,GAAG,IAAI;YACnC,KAAK,MAAM,WAAW,IAAI,CAAC,QAAQ,CAAE;gBACnC,MAAM,OAAO,QAAQ,OAAO,CAAC;gBAC7B,KAAK,eAAe,CAAC,SAAS,IAAI,CAAC,uBAAuB;YAC5D;YACA,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC5B,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI;YAC/F,OAAO;gBACL,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI;YAClG;QACF;QACA,IAAI,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC/B,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC5B,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,IAAI,CAAC,uBAAuB,CAAC,MAAM,KAAK,GAAG;YACpF,OAAO;gBACL,IAAI,CAAC,uBAAuB,CAAC,SAAS,CAAC,GAAG;YAC5C;QACF;QACA,OAAO,IAAI,CAAC,uBAAuB;IACrC;AACF;AACA,IAAI,iBAAiB,cAAc;IACjC,OAAO;IACP,cAAc;IACd,cAAc;IACd,OAAO;IACP,uBAAuB;IACvB,mBAAmB;IACnB,SAAS;IACT,wBAAwB;IACxB,6BAA6B;IAC7B,YAAY,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,aAAa,EAAE,MAAM,EAAE,aAAa,EAAE,QAAQ,CAAE;QACnG,KAAK,CAAC,WAAW,IAAI,MAAM;QAC3B,IAAI,CAAC,MAAM,GAAG,IAAI,aAAa,OAAO,IAAI,CAAC,EAAE;QAC7C,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,MAAM,GAAG,IAAI,aAAa,QAAQ;QACvC,IAAI,CAAC,sBAAsB,GAAG,IAAI,CAAC,MAAM,CAAC,iBAAiB;QAC3D,IAAI,CAAC,QAAQ,GAAG,SAAS,QAAQ;QACjC,IAAI,CAAC,kBAAkB,GAAG,SAAS,kBAAkB;QACrD,IAAI,CAAC,uBAAuB,GAAG;QAC/B,IAAI,CAAC,4BAA4B,GAAG;IACtC;IACA,UAAU;QACR,IAAI,IAAI,CAAC,uBAAuB,EAAE;YAChC,IAAI,CAAC,uBAAuB,CAAC,OAAO;YACpC,IAAI,CAAC,uBAAuB,GAAG;QACjC;QACA,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACrC,IAAI,CAAC,4BAA4B,CAAC,OAAO;YACzC,IAAI,CAAC,4BAA4B,GAAG;QACtC;IACF;IACA,IAAI,mBAAmB;QACrB,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;IAChC;IACA,IAAI,mBAAmB;QACrB,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;IAChC;IACA,mCAAmC,QAAQ,EAAE,cAAc,EAAE;QAC3D,OAAO,IAAI,CAAC,MAAM,CAAC,qBAAqB,CAAC,UAAU;IACrD;IACA,gBAAgB,OAAO,EAAE,GAAG,EAAE;QAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM;IACtB;IACA,QAAQ,OAAO,EAAE,cAAc,EAAE;QAC/B,OAAO,IAAI,CAAC,0BAA0B,CAAC,SAAS,OAAO,CAAC;IAC1D;IACA,UAAU,OAAO,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE;QACjD,OAAO,IAAI,CAAC,0BAA0B,CAAC,SAAS,SAAS,CAAC,SAAS,QAAQ;IAC7E;IACA,2BAA2B,OAAO,EAAE;QAClC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACjC,IAAI,CAAC,uBAAuB,GAAG,IAAI;YACnC,KAAK,MAAM,WAAW,IAAI,CAAC,QAAQ,CAAE;gBACnC,MAAM,OAAO,QAAQ,OAAO,CAAC;gBAC7B,KAAK,eAAe,CAAC,SAAS,IAAI,CAAC,uBAAuB;YAC5D;QACF;QACA,OAAO,IAAI,CAAC,uBAAuB;IACrC;IACA,aAAa,OAAO,EAAE,cAAc,EAAE;QACpC,OAAO,IAAI,CAAC,+BAA+B,CAAC,SAAS,gBAAgB,OAAO,CAAC;IAC/E;IACA,eAAe,OAAO,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM,EAAE;QACtD,OAAO,IAAI,CAAC,+BAA+B,CAAC,SAAS,gBAAgB,SAAS,CAAC,SAAS,QAAQ;IAClG;IACA,gCAAgC,OAAO,EAAE,cAAc,EAAE;QACvD,IAAI,CAAC,IAAI,CAAC,4BAA4B,EAAE;YACtC,IAAI,CAAC,4BAA4B,GAAG,IAAI;YACxC,IAAI,CAAC,4BAA4B,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC,MAAM;QAC1G;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE;YACjC,IAAI,CAAC,4BAA4B,CAAC,SAAS,CAAC,GAAG,iBAAiB,iBAAiB;QACnF;QACA,OAAO,IAAI,CAAC,4BAA4B;IAC1C;AACF;AACA,IAAI,cAAc,MAAM;IACtB,OAAO,kBAAkB,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,WAAW,EAAE,4BAA4B,EAAE;QAC3F,OAAO,OAAO,YAAY,CAAC,CAAC;YAC1B,OAAO,IAAI,YAAY,WAAW,IAAI,MAAM,aAAa;QAC3D;IACF;IACA,OAAO,kBAAkB,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE;QACjD,IAAI,CAAC,KAAK,EAAE,EAAE;YACZ,OAAO,YAAY,CAAC,CAAC;gBACnB,KAAK,EAAE,GAAG;gBACV,IAAI,KAAK,KAAK,EAAE;oBACd,OAAO,IAAI,UACT,KAAK,uBAAuB,EAC5B,KAAK,EAAE,EACP,KAAK,IAAI,EACT,KAAK,KAAK,EACV,aAAa,gBAAgB,CAAC,KAAK,QAAQ,EAAE,QAAQ;gBAEzD;gBACA,IAAI,OAAO,KAAK,KAAK,KAAK,aAAa;oBACrC,IAAI,KAAK,UAAU,EAAE;wBACnB,aAAa,aAAa,CAAC,GAAG,YAAY,KAAK,UAAU;oBAC3D;oBACA,IAAI,WAAW,KAAK,QAAQ;oBAC5B,IAAI,OAAO,aAAa,eAAe,KAAK,OAAO,EAAE;wBACnD,WAAW;4BAAC;gCAAE,SAAS,KAAK,OAAO;4BAAC;yBAAE;oBACxC;oBACA,OAAO,IAAI,gBACT,KAAK,uBAAuB,EAC5B,KAAK,EAAE,EACP,KAAK,IAAI,EACT,KAAK,WAAW,EAChB,aAAa,gBAAgB,CAAC,UAAU,QAAQ;gBAEpD;gBACA,IAAI,KAAK,KAAK,EAAE;oBACd,OAAO,IAAI,eACT,KAAK,uBAAuB,EAC5B,KAAK,EAAE,EACP,KAAK,IAAI,EACT,KAAK,WAAW,EAChB,KAAK,KAAK,EACV,aAAa,gBAAgB,CAAC,KAAK,aAAa,IAAI,KAAK,QAAQ,EAAE,QAAQ,aAC3E,KAAK,KAAK,EACV,aAAa,gBAAgB,CAAC,KAAK,aAAa,IAAI,KAAK,QAAQ,EAAE,QAAQ,aAC3E,aAAa,gBAAgB,CAAC,KAAK,QAAQ,EAAE,QAAQ;gBAEzD;gBACA,OAAO,IAAI,aACT,KAAK,uBAAuB,EAC5B,KAAK,EAAE,EACP,KAAK,IAAI,EACT,KAAK,WAAW,EAChB,KAAK,KAAK,EACV,aAAa,gBAAgB,CAAC,KAAK,aAAa,IAAI,KAAK,QAAQ,EAAE,QAAQ,aAC3E,KAAK,GAAG,EACR,aAAa,gBAAgB,CAAC,KAAK,WAAW,IAAI,KAAK,QAAQ,EAAE,QAAQ,aACzE,KAAK,mBAAmB,EACxB,aAAa,gBAAgB,CAAC,KAAK,QAAQ,EAAE,QAAQ;YAEzD;QACF;QACA,OAAO,KAAK,EAAE;IAChB;IACA,OAAO,iBAAiB,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE;QACpD,IAAI,IAAI,EAAE;QACV,IAAI,UAAU;YACZ,IAAI,mBAAmB;YACvB,IAAK,MAAM,aAAa,SAAU;gBAChC,IAAI,cAAc,2BAA2B;oBAC3C;gBACF;gBACA,MAAM,mBAAmB,SAAS,WAAW;gBAC7C,IAAI,mBAAmB,kBAAkB;oBACvC,mBAAmB;gBACrB;YACF;YACA,IAAK,IAAI,IAAI,GAAG,KAAK,kBAAkB,IAAK;gBAC1C,CAAC,CAAC,EAAE,GAAG;YACT;YACA,IAAK,MAAM,aAAa,SAAU;gBAChC,IAAI,cAAc,2BAA2B;oBAC3C;gBACF;gBACA,MAAM,mBAAmB,SAAS,WAAW;gBAC7C,IAAI,+BAA+B;gBACnC,IAAI,QAAQ,CAAC,UAAU,CAAC,QAAQ,EAAE;oBAChC,+BAA+B,aAAa,iBAAiB,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ;gBAC7F;gBACA,CAAC,CAAC,iBAAiB,GAAG,aAAa,iBAAiB,CAAC,QAAQ,QAAQ,CAAC,UAAU,CAAC,uBAAuB,EAAE,QAAQ,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,UAAU,CAAC,WAAW,EAAE;YACvK;QACF;QACA,OAAO;IACT;IACA,OAAO,iBAAiB,QAAQ,EAAE,MAAM,EAAE,UAAU,EAAE;QACpD,IAAI,IAAI,EAAE;QACV,IAAI,UAAU;YACZ,IAAK,IAAI,IAAI,GAAG,MAAM,SAAS,MAAM,EAAE,IAAI,KAAK,IAAK;gBACnD,MAAM,UAAU,QAAQ,CAAC,EAAE;gBAC3B,IAAI,SAAS,CAAC;gBACd,IAAI,QAAQ,OAAO,EAAE;oBACnB,MAAM,YAAY,aAAa,QAAQ,OAAO;oBAC9C,OAAQ,UAAU,IAAI;wBACpB,KAAK,EAAE,QAAQ;wBACf,KAAK,EAAE,QAAQ;4BACb,SAAS,aAAa,iBAAiB,CAAC,UAAU,CAAC,QAAQ,OAAO,CAAC,EAAE,QAAQ;4BAC7E;wBACF,KAAK,EAAE,qBAAqB;4BAC1B,IAAI,oBAAoB,UAAU,CAAC,UAAU,QAAQ,CAAC;4BACtD,IAAI,mBAAmB;gCACrB,SAAS,aAAa,iBAAiB,CAAC,mBAAmB,QAAQ;4BACrE,OAAO,CACP;4BACA;wBACF,KAAK,EAAE,qBAAqB;wBAC5B,KAAK,EAAE,+BAA+B;4BACpC,MAAM,sBAAsB,UAAU,SAAS;4BAC/C,MAAM,yBAAyB,UAAU,IAAI,KAAK,EAAE,+BAA+B,MAAK,UAAU,QAAQ,GAAG;4BAC7G,MAAM,kBAAkB,OAAO,kBAAkB,CAAC,qBAAqB;4BACvE,IAAI,iBAAiB;gCACnB,IAAI,wBAAwB;oCAC1B,IAAI,uBAAuB,gBAAgB,UAAU,CAAC,uBAAuB;oCAC7E,IAAI,sBAAsB;wCACxB,SAAS,aAAa,iBAAiB,CAAC,sBAAsB,QAAQ,gBAAgB,UAAU;oCAClG,OAAO,CACP;gCACF,OAAO;oCACL,SAAS,aAAa,iBAAiB,CAAC,gBAAgB,UAAU,CAAC,KAAK,EAAE,QAAQ,gBAAgB,UAAU;gCAC9G;4BACF,OAAO,CACP;4BACA;oBACJ;gBACF,OAAO;oBACL,SAAS,aAAa,iBAAiB,CAAC,SAAS,QAAQ;gBAC3D;gBACA,IAAI,WAAW,CAAC,GAAG;oBACjB,MAAM,OAAO,OAAO,OAAO,CAAC;oBAC5B,IAAI,WAAW;oBACf,IAAI,gBAAgB,mBAAmB,gBAAgB,gBAAgB,gBAAgB,gBAAgB;wBACrG,IAAI,KAAK,kBAAkB,IAAI,KAAK,QAAQ,CAAC,MAAM,KAAK,GAAG;4BACzD,WAAW;wBACb;oBACF;oBACA,IAAI,UAAU;wBACZ;oBACF;oBACA,EAAE,IAAI,CAAC;gBACT;YACF;QACF;QACA,OAAO;YACL,UAAU;YACV,oBAAoB,CAAC,WAAW,SAAS,MAAM,GAAG,CAAC,MAAM,EAAE,MAAM;QACnE;IACF;AACF;AACA,IAAI,eAAe,MAAM;IACvB,OAAO;IACP,OAAO;IACP,UAAU;IACV,kBAAkB;IAClB,aAAa;IACb,YAAY,YAAY,EAAE,MAAM,CAAE;QAChC,IAAI,gBAAgB,OAAO,iBAAiB,UAAU;YACpD,MAAM,MAAM,aAAa,MAAM;YAC/B,IAAI,gBAAgB;YACpB,IAAI,SAAS,EAAE;YACf,IAAI,YAAY;YAChB,IAAK,IAAI,MAAM,GAAG,MAAM,KAAK,MAAO;gBAClC,MAAM,KAAK,aAAa,MAAM,CAAC;gBAC/B,IAAI,OAAO,MAAM;oBACf,IAAI,MAAM,IAAI,KAAK;wBACjB,MAAM,SAAS,aAAa,MAAM,CAAC,MAAM;wBACzC,IAAI,WAAW,KAAK;4BAClB,OAAO,IAAI,CAAC,aAAa,SAAS,CAAC,eAAe;4BAClD,OAAO,IAAI,CAAC;4BACZ,gBAAgB,MAAM;wBACxB,OAAO,IAAI,WAAW,OAAO,WAAW,KAAK;4BAC3C,YAAY;wBACd;wBACA;oBACF;gBACF;YACF;YACA,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,kBAAkB,GAAG;gBACvB,IAAI,CAAC,MAAM,GAAG;YAChB,OAAO;gBACL,OAAO,IAAI,CAAC,aAAa,SAAS,CAAC,eAAe;gBAClD,IAAI,CAAC,MAAM,GAAG,OAAO,IAAI,CAAC;YAC5B;QACF,OAAO;YACL,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,MAAM,GAAG;QAChB;QACA,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,iBAAiB;QAC5C,OAAO;YACL,IAAI,CAAC,YAAY,GAAG;QACtB;QACA,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,UAAU;YACnC,IAAI,CAAC,iBAAiB,GAAG,oBAAoB,IAAI,CAAC,IAAI,CAAC,MAAM;QAC/D,OAAO;YACL,IAAI,CAAC,iBAAiB,GAAG;QAC3B;IACF;IACA,QAAQ;QACN,OAAO,IAAI,cAAc,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM;IACnD;IACA,UAAU,SAAS,EAAE;QACnB,IAAI,IAAI,CAAC,MAAM,KAAK,WAAW;YAC7B;QACF;QACA,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,iBAAiB;QAC5C;IACF;IACA,sBAAsB,QAAQ,EAAE,cAAc,EAAE;QAC9C,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,UAAU;YACnC,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,iBAAiB,eAAe,GAAG,CAAC,CAAC;YACvC,OAAO,SAAS,SAAS,CAAC,QAAQ,KAAK,EAAE,QAAQ,GAAG;QACtD;QACA,qBAAqB,SAAS,GAAG;QACjC,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,sBAAsB,CAAC,OAAO;YACvD,OAAO,uBAAuB,cAAc,CAAC,SAAS,IAAI,IAAI,IAAI;QACpE;IACF;IACA,oBAAoB;QAClB,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,UAAU;YACnC,MAAM,IAAI,MAAM;QAClB;QACA,IAAI,eAAe,EAAE;QACrB,IAAI,eAAe,EAAE;QACrB,IAAI,eAAe,EAAE;QACrB,IAAI,eAAe,EAAE;QACrB,IAAI,KAAK,KAAK,IAAI;QAClB,IAAK,MAAM,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,MAAM,KAAK,MAAO;YACxD,KAAK,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;YACxB,YAAY,CAAC,IAAI,GAAG;YACpB,YAAY,CAAC,IAAI,GAAG;YACpB,YAAY,CAAC,IAAI,GAAG;YACpB,YAAY,CAAC,IAAI,GAAG;YACpB,IAAI,OAAO,MAAM;gBACf,IAAI,MAAM,IAAI,KAAK;oBACjB,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM;oBAClC,IAAI,WAAW,KAAK;wBAClB,YAAY,CAAC,MAAM,EAAE,GAAG;wBACxB,YAAY,CAAC,MAAM,EAAE,GAAG;wBACxB,YAAY,CAAC,MAAM,EAAE,GAAG;wBACxB,YAAY,CAAC,MAAM,EAAE,GAAG;oBAC1B,OAAO,IAAI,WAAW,KAAK;wBACzB,YAAY,CAAC,MAAM,EAAE,GAAG;wBACxB,YAAY,CAAC,MAAM,EAAE,GAAG;wBACxB,YAAY,CAAC,MAAM,EAAE,GAAG;wBACxB,YAAY,CAAC,MAAM,EAAE,GAAG;oBAC1B,OAAO;wBACL,YAAY,CAAC,MAAM,EAAE,GAAG;wBACxB,YAAY,CAAC,MAAM,EAAE,GAAG;wBACxB,YAAY,CAAC,MAAM,EAAE,GAAG;wBACxB,YAAY,CAAC,MAAM,EAAE,GAAG;oBAC1B;oBACA;gBACF;YACF;QACF;QACA,OAAO;YACL,OAAO,aAAa,IAAI,CAAC;YACzB,OAAO,aAAa,IAAI,CAAC;YACzB,OAAO,aAAa,IAAI,CAAC;YACzB,OAAO,aAAa,IAAI,CAAC;QAC3B;IACF;IACA,eAAe,MAAM,EAAE,MAAM,EAAE;QAC7B,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,UAAU;YAC5E,OAAO,IAAI,CAAC,MAAM;QACpB;QACA,IAAI,QAAQ;YACV,IAAI,QAAQ;gBACV,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK;YAChC,OAAO;gBACL,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK;YAChC;QACF,OAAO;YACL,IAAI,QAAQ;gBACV,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK;YAChC,OAAO;gBACL,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK;YAChC;QACF;IACF;AACF;AACA,IAAI,mBAAmB;IACrB,OAAO;IACP,YAAY;IACZ,QAAQ;IACR,aAAa;IACb,aAAc;QACZ,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,YAAY,GAAG;YAClB,OAAO;YACP,OAAO;YACP,OAAO;YACP,OAAO;QACT;IACF;IACA,UAAU;QACR,IAAI,CAAC,cAAc;IACrB;IACA,iBAAiB;QACf,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,OAAO,CAAC,OAAO;YACpB,IAAI,CAAC,OAAO,GAAG;QACjB;QACA,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;YAC3B,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO;YAC/B,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG;QAC5B;QACA,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;YAC3B,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO;YAC/B,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG;QAC5B;QACA,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;YAC3B,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO;YAC/B,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG;QAC5B;QACA,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;YAC3B,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO;YAC/B,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG;QAC5B;IACF;IACA,KAAK,IAAI,EAAE;QACT,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACjB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,KAAK,SAAS;IACvD;IACA,QAAQ,IAAI,EAAE;QACZ,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;QACpB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,IAAI,KAAK,SAAS;IACvD;IACA,SAAS;QACP,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM;IAC3B;IACA,UAAU,KAAK,EAAE,SAAS,EAAE;QAC1B,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,KAAK,WAAW;YAC3C,IAAI,CAAC,cAAc;YACnB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC;QAC/B;IACF;IACA,QAAQ,OAAO,EAAE;QACf,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,UAAU,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAM,EAAE,MAAM;YAC7C,IAAI,CAAC,OAAO,GAAG,IAAI,aAAa,SAAS,SAAS,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAM,EAAE,MAAM;QACnF;QACA,OAAO,IAAI,CAAC,OAAO;IACrB;IACA,UAAU,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE;QACjC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACrB,OAAO,IAAI,CAAC,OAAO,CAAC;QACtB,OAAO;YACL,IAAI,QAAQ;gBACV,IAAI,QAAQ;oBACV,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;wBAC5B,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,QAAQ;oBAClE;oBACA,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK;gBAChC,OAAO;oBACL,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;wBAC5B,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,QAAQ;oBAClE;oBACA,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK;gBAChC;YACF,OAAO;gBACL,IAAI,QAAQ;oBACV,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;wBAC5B,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,QAAQ;oBAClE;oBACA,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK;gBAChC,OAAO;oBACL,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE;wBAC5B,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,QAAQ;oBAClE;oBACA,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK;gBAChC;YACF;QACF;IACF;IACA,gBAAgB,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE;QACvC,IAAI,UAAU,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAM,EAAE,cAAc,CAAC,QAAQ;QAC9D,OAAO,IAAI,aAAa,SAAS,SAAS,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAM,EAAE,MAAM;IAC3E;AACF;AACA,IAAI,eAAe;IACjB,YAAY,OAAO,EAAE,OAAO,EAAE,KAAK,CAAE;QACnC,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,OAAO,GAAG,QAAQ,iBAAiB,CAAC;IAC3C;IACA,QAAQ;IACR,UAAU;QACR,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,KAAK,YAAY;YAC9C,IAAI,CAAC,OAAO,CAAC,OAAO;QACtB;IACF;IACA,WAAW;QACT,MAAM,IAAI,EAAE;QACZ,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,KAAK,IAAK;YACrD,EAAE,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE;QACzD;QACA,OAAO,EAAE,IAAI,CAAC;IAChB;IACA,kBAAkB,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE;QAChD,MAAM,SAAS,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,eAAe;QACrE,IAAI,CAAC,QAAQ;YACX,OAAO;QACT;QACA,OAAO;YACL,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,CAAC;YAChC,gBAAgB,OAAO,cAAc;QACvC;IACF;AACF;AAEA,8CAA8C;AAC9C,IAAI,uBAAuB;IACzB,YAAY,UAAU,EAAE,SAAS,CAAE;QACjC,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,SAAS,GAAG;IACnB;AACF;AACA,IAAI,+BAA+B,MAAM;IACvC,mBAAmB;IACnB,0BAA0B;IAC1B,YAAY,iBAAiB,EAAE,iBAAiB,CAAE;QAChD,IAAI,CAAC,kBAAkB,GAAG,IAAI,qBAAqB,mBAAmB,EAAE,UAAU;QAClF,IAAI,CAAC,yBAAyB,GAAG,IAAI,aAAa,OAAO,OAAO,CAAC,qBAAqB,CAAC;IACzF;IACA,uBAAuB;QACrB,OAAO,IAAI,CAAC,kBAAkB;IAChC;IACA,wBAAwB,SAAS,EAAE;QACjC,IAAI,cAAc,MAAM;YACtB,OAAO,8BAA8B,oBAAoB;QAC3D;QACA,OAAO,IAAI,CAAC,wBAAwB,CAAC,GAAG,CAAC;IAC3C;IACA,OAAO,uBAAuB,IAAI,qBAAqB,GAAG,GAAG;IAC7D,2BAA2B,IAAI,SAAS,CAAC;QACvC,MAAM,aAAa,IAAI,CAAC,gBAAgB,CAAC;QACzC,MAAM,oBAAoB,IAAI,CAAC,oBAAoB,CAAC;QACpD,OAAO,IAAI,qBAAqB,YAAY;IAC9C,GAAG;IACH;;;GAGC,GACD,iBAAiB,KAAK,EAAE;QACtB,OAAO,IAAI,CAAC,yBAAyB,CAAC,KAAK,CAAC,UAAU;IACxD;IACA,qBAAqB,SAAS,EAAE;QAC9B,MAAM,IAAI,UAAU,KAAK,CAAC,8BAA8B,0BAA0B;QAClF,IAAI,CAAC,GAAG;YACN,OAAO,EAAE,UAAU;QACrB;QACA,OAAQ,CAAC,CAAC,EAAE;YACV,KAAK;gBACH,OAAO,EAAE,WAAW;YACtB,KAAK;gBACH,OAAO,EAAE,UAAU;YACrB,KAAK;gBACH,OAAO,EAAE,SAAS;YACpB,KAAK;gBACH,OAAO,EAAE,SAAS;QACtB;QACA,MAAM,IAAI,MAAM;IAClB;IACA,OAAO,6BAA6B,4CAA4C;AAClF;AACA,IAAI,eAAe;IACjB,OAAO;IACP,aAAa;IACb,YAAY,MAAM,CAAE;QAClB,IAAI,OAAO,MAAM,KAAK,GAAG;YACvB,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,YAAY,GAAG;QACtB,OAAO;YACL,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI;YACtB,MAAM,gBAAgB,OAAO,GAAG,CAC9B,CAAC,CAAC,WAAW,MAAM,GAAK,uBAAuB;YAEjD,cAAc,IAAI;YAClB,cAAc,OAAO;YACrB,IAAI,CAAC,YAAY,GAAG,IAAI,OACtB,CAAC,GAAG,EAAE,cAAc,IAAI,CAAC,OAAO,SAAS,CAAC,EAC1C;QAEJ;IACF;IACA,MAAM,KAAK,EAAE;QACX,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACtB,OAAO,KAAK;QACd;QACA,MAAM,IAAI,MAAM,KAAK,CAAC,IAAI,CAAC,YAAY;QACvC,IAAI,CAAC,GAAG;YACN,OAAO,KAAK;QACd;QACA,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;IAC7B;AACF;AAEA,eAAe;AACf,IAAI,aAAa;IACf,aAAa,OAAO,gKAAA,CAAA,UAAO,KAAK,eAAe,CAAC,CAAC,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,wBAAwB;AACvF;AACA,IAAI,0BAA0B;AAE9B,gCAAgC;AAChC,IAAI,uBAAuB;IACzB,YAAY,KAAK,EAAE,YAAY,CAAE;QAC/B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,YAAY,GAAG;IACtB;AACF;AACA,SAAS,gBAAgB,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,oBAAoB,EAAE,SAAS;IAClH,MAAM,aAAa,SAAS,OAAO,CAAC,MAAM;IAC1C,IAAI,OAAO;IACX,IAAI,iBAAiB,CAAC;IACtB,IAAI,sBAAsB;QACxB,MAAM,mBAAmB,sBACvB,SACA,UACA,aACA,SACA,OACA;QAEF,QAAQ,iBAAiB,KAAK;QAC9B,UAAU,iBAAiB,OAAO;QAClC,cAAc,iBAAiB,WAAW;QAC1C,iBAAiB,iBAAiB,cAAc;IAClD;IACA,MAAM,YAAY,KAAK,GAAG;IAC1B,MAAO,CAAC,KAAM;QACZ,IAAI,cAAc,GAAG;YACnB,MAAM,cAAc,KAAK,GAAG,KAAK;YACjC,IAAI,cAAc,WAAW;gBAC3B,OAAO,IAAI,qBAAqB,OAAO;YACzC;QACF;QACA;IACF;IACA,OAAO,IAAI,qBAAqB,OAAO;;IACvC,SAAS;QACP,uCAAW;;QAKX;QACA,MAAM,IAAI,sBACR,SACA,UACA,aACA,SACA,OACA;QAEF,IAAI,CAAC,GAAG;YACN,WAAW,OAAO,CAAC,OAAO;YAC1B,OAAO;YACP;QACF;QACA,MAAM,iBAAiB,EAAE,cAAc;QACvC,MAAM,gBAAgB,EAAE,aAAa;QACrC,MAAM,cAAc,kBAAkB,eAAe,MAAM,GAAG,IAAI,cAAc,CAAC,EAAE,CAAC,GAAG,GAAG,UAAU;QACpG,IAAI,kBAAkB,WAAW;YAC/B,MAAM,aAAa,MAAM,OAAO,CAAC;YACjC,uCAAW;;YAIX;YACA,WAAW,OAAO,CAAC,OAAO,cAAc,CAAC,EAAE,CAAC,KAAK;YACjD,QAAQ,MAAM,yBAAyB,CAAC,MAAM,cAAc;YAC5D,eACE,SACA,UACA,aACA,OACA,YACA,WAAW,WAAW,EACtB;YAEF,WAAW,OAAO,CAAC,OAAO,cAAc,CAAC,EAAE,CAAC,GAAG;YAC/C,MAAM,SAAS;YACf,QAAQ,MAAM,MAAM;YACpB,iBAAiB,OAAO,YAAY;YACpC,IAAI,CAAC,eAAe,OAAO,WAAW,OAAO,SAAS;gBACpD,uCAAW;;gBAIX;gBACA,QAAQ;gBACR,WAAW,OAAO,CAAC,OAAO;gBAC1B,OAAO;gBACP;YACF;QACF,OAAO;YACL,MAAM,QAAQ,QAAQ,OAAO,CAAC;YAC9B,WAAW,OAAO,CAAC,OAAO,cAAc,CAAC,EAAE,CAAC,KAAK;YACjD,MAAM,aAAa;YACnB,MAAM,YAAY,MAAM,OAAO,CAAC,SAAS,OAAO,EAAE;YAClD,MAAM,iBAAiB,MAAM,qBAAqB,CAAC,cAAc,CAC/D,WACA;YAEF,QAAQ,MAAM,IAAI,CAChB,eACA,SACA,gBACA,cAAc,CAAC,EAAE,CAAC,GAAG,KAAK,YAC1B,MACA,gBACA;YAEF,IAAI,iBAAiB,cAAc;gBACjC,MAAM,aAAa;gBACnB,uCAAW;;gBAIX;gBACA,eACE,SACA,UACA,aACA,OACA,YACA,WAAW,aAAa,EACxB;gBAEF,WAAW,OAAO,CAAC,OAAO,cAAc,CAAC,EAAE,CAAC,GAAG;gBAC/C,iBAAiB,cAAc,CAAC,EAAE,CAAC,GAAG;gBACtC,MAAM,cAAc,WAAW,cAAc,CAC3C,SAAS,OAAO,EAChB;gBAEF,MAAM,wBAAwB,eAAe,cAAc,CACzD,aACA;gBAEF,QAAQ,MAAM,yBAAyB,CAAC;gBACxC,IAAI,WAAW,oBAAoB,EAAE;oBACnC,QAAQ,MAAM,WAAW,CACvB,WAAW,gCAAgC,CACzC,SAAS,OAAO,EAChB;gBAGN;gBACA,IAAI,CAAC,eAAe,WAAW,aAAa,CAAC,QAAQ;oBACnD,uCAAW;;oBAIX;oBACA,QAAQ,MAAM,GAAG;oBACjB,WAAW,OAAO,CAAC,OAAO;oBAC1B,OAAO;oBACP;gBACF;YACF,OAAO,IAAI,iBAAiB,gBAAgB;gBAC1C,MAAM,aAAa;gBACnB,uCAAW;;gBAEX;gBACA,eACE,SACA,UACA,aACA,OACA,YACA,WAAW,aAAa,EACxB;gBAEF,WAAW,OAAO,CAAC,OAAO,cAAc,CAAC,EAAE,CAAC,GAAG;gBAC/C,iBAAiB,cAAc,CAAC,EAAE,CAAC,GAAG;gBACtC,MAAM,cAAc,WAAW,cAAc,CAC3C,SAAS,OAAO,EAChB;gBAEF,MAAM,wBAAwB,eAAe,cAAc,CACzD,aACA;gBAEF,QAAQ,MAAM,yBAAyB,CAAC;gBACxC,IAAI,WAAW,sBAAsB,EAAE;oBACrC,QAAQ,MAAM,WAAW,CACvB,WAAW,kCAAkC,CAC3C,SAAS,OAAO,EAChB;gBAGN;gBACA,IAAI,CAAC,eAAe,WAAW,aAAa,CAAC,QAAQ;oBACnD,uCAAW;;oBAIX;oBACA,QAAQ,MAAM,GAAG;oBACjB,WAAW,OAAO,CAAC,OAAO;oBAC1B,OAAO;oBACP;gBACF;YACF,OAAO;gBACL,MAAM,eAAe;gBACrB,uCAAW;;gBAIX;gBACA,eACE,SACA,UACA,aACA,OACA,YACA,aAAa,QAAQ,EACrB;gBAEF,WAAW,OAAO,CAAC,OAAO,cAAc,CAAC,EAAE,CAAC,GAAG;gBAC/C,QAAQ,MAAM,GAAG;gBACjB,IAAI,CAAC,aAAa;oBAChB,uCAAW;;oBAIX;oBACA,QAAQ,MAAM,OAAO;oBACrB,WAAW,OAAO,CAAC,OAAO;oBAC1B,OAAO;oBACP;gBACF;YACF;QACF;QACA,IAAI,cAAc,CAAC,EAAE,CAAC,GAAG,GAAG,SAAS;YACnC,UAAU,cAAc,CAAC,EAAE,CAAC,GAAG;YAC/B,cAAc;QAChB;IACF;AACF;AACA,SAAS,sBAAsB,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU;IACvF,IAAI,iBAAiB,MAAM,oBAAoB,GAAG,IAAI,CAAC;IACvD,MAAM,aAAa,EAAE;IACrB,IAAK,IAAI,OAAO,OAAO,MAAM,OAAO,KAAK,GAAG,GAAI;QAC9C,MAAM,WAAW,KAAK,OAAO,CAAC;QAC9B,IAAI,oBAAoB,gBAAgB;YACtC,WAAW,IAAI,CAAC;gBACd,MAAM;gBACN,OAAO;YACT;QACF;IACF;IACA,IAAK,IAAI,YAAY,WAAW,GAAG,IAAI,WAAW,YAAY,WAAW,GAAG,GAAI;QAC9E,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,uBAAuB,UAAU,IAAI,EAAE,SAAS,UAAU,KAAK,CAAC,OAAO,EAAE,aAAa,YAAY;QACvI,MAAM,IAAI,YAAY,iBAAiB,CAAC,UAAU,SAAS;QAC3D,uCAAW;;QAGX;QACA,IAAI,GAAG;YACL,MAAM,gBAAgB,EAAE,MAAM;YAC9B,IAAI,kBAAkB,aAAa;gBACjC,QAAQ,UAAU,KAAK,CAAC,GAAG;gBAC3B;YACF;YACA,IAAI,EAAE,cAAc,IAAI,EAAE,cAAc,CAAC,MAAM,EAAE;gBAC/C,WAAW,OAAO,CAAC,UAAU,KAAK,EAAE,EAAE,cAAc,CAAC,EAAE,CAAC,KAAK;gBAC7D,eAAe,SAAS,UAAU,aAAa,UAAU,KAAK,EAAE,YAAY,UAAU,IAAI,CAAC,aAAa,EAAE,EAAE,cAAc;gBAC1H,WAAW,OAAO,CAAC,UAAU,KAAK,EAAE,EAAE,cAAc,CAAC,EAAE,CAAC,GAAG;gBAC3D,iBAAiB,EAAE,cAAc,CAAC,EAAE,CAAC,GAAG;gBACxC,IAAI,EAAE,cAAc,CAAC,EAAE,CAAC,GAAG,GAAG,SAAS;oBACrC,UAAU,EAAE,cAAc,CAAC,EAAE,CAAC,GAAG;oBACjC,cAAc;gBAChB;YACF;QACF,OAAO;YACL,uCAAW;;YAEX;YACA,QAAQ,UAAU,KAAK,CAAC,GAAG;YAC3B;QACF;IACF;IACA,OAAO;QAAE;QAAO;QAAS;QAAgB;IAAY;AACvD;AACA,SAAS,sBAAsB,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc;IAC3F,MAAM,cAAc,UAAU,SAAS,UAAU,aAAa,SAAS,OAAO;IAC9E,MAAM,aAAa,QAAQ,aAAa;IACxC,IAAI,WAAW,MAAM,KAAK,GAAG;QAC3B,OAAO;IACT;IACA,MAAM,kBAAkB,gBAAgB,YAAY,SAAS,UAAU,aAAa,SAAS,OAAO;IACpG,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IACA,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IACA,MAAM,mBAAmB,YAAY,cAAc,CAAC,EAAE,CAAC,KAAK;IAC5D,MAAM,uBAAuB,gBAAgB,cAAc,CAAC,EAAE,CAAC,KAAK;IACpE,IAAI,uBAAuB,oBAAoB,gBAAgB,aAAa,IAAI,yBAAyB,kBAAkB;QACzH,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS,UAAU,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc;IAC/E,MAAM,OAAO,MAAM,OAAO,CAAC;IAC3B,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,kBAAkB,MAAM,SAAS,MAAM,OAAO,EAAE,aAAa,YAAY;IAC9G,MAAM,IAAI,YAAY,iBAAiB,CAAC,UAAU,SAAS;IAC3D,IAAI,GAAG;QACL,OAAO;YACL,gBAAgB,EAAE,cAAc;YAChC,eAAe,EAAE,MAAM;QACzB;IACF;IACA,OAAO;AACT;AACA,SAAS,gBAAgB,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,cAAc;IACjG,IAAI,kBAAkB,OAAO,SAAS;IACtC,IAAI,0BAA0B;IAC9B,IAAI;IACJ,IAAI,0BAA0B;IAC9B,MAAM,SAAS,MAAM,qBAAqB,CAAC,aAAa;IACxD,IAAK,IAAI,IAAI,GAAG,MAAM,WAAW,MAAM,EAAE,IAAI,KAAK,IAAK;QACrD,MAAM,YAAY,UAAU,CAAC,EAAE;QAC/B,IAAI,CAAC,UAAU,OAAO,CAAC,SAAS;YAC9B;QACF;QACA,MAAM,OAAO,QAAQ,OAAO,CAAC,UAAU,MAAM;QAC7C,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,kBAAkB,MAAM,SAAS,MAAM,aAAa,YAAY;QACrG,MAAM,cAAc,YAAY,iBAAiB,CAAC,UAAU,SAAS;QACrE,IAAI,CAAC,aAAa;YAChB;QACF;QACA,uCAAW;;QAGX;QACA,MAAM,cAAc,YAAY,cAAc,CAAC,EAAE,CAAC,KAAK;QACvD,IAAI,eAAe,iBAAiB;YAClC;QACF;QACA,kBAAkB;QAClB,0BAA0B,YAAY,cAAc;QACpD,kBAAkB,YAAY,MAAM;QACpC,0BAA0B,UAAU,QAAQ;QAC5C,IAAI,oBAAoB,SAAS;YAC/B;QACF;IACF;IACA,IAAI,yBAAyB;QAC3B,OAAO;YACL,eAAe,4BAA4B,CAAC;YAC5C,gBAAgB;YAChB,eAAe;QACjB;IACF;IACA,OAAO;AACT;AACA,SAAS,kBAAkB,IAAI,EAAE,OAAO,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM;IACtE,uCAA6B;;IAI7B;IACA,MAAM,cAAc,KAAK,SAAS,CAAC,SAAS,gBAAgB,QAAQ;IACpE,OAAO;QAAE;QAAa,aAAa,EAAE,QAAQ;IAAG;AAClD;AACA,SAAS,uBAAuB,IAAI,EAAE,OAAO,EAAE,cAAc,EAAE,MAAM,EAAE,MAAM;IAC3E,uCAA6B;;IAI7B;IACA,MAAM,cAAc,KAAK,cAAc,CAAC,SAAS,gBAAgB,QAAQ;IACzE,OAAO;QAAE;QAAa,aAAa,EAAE,QAAQ;IAAG;AAClD;AACA,SAAS,eAAe,MAAM,EAAE,MAAM;IACpC,IAAI,UAAU,EAAE,QAAQ;IACxB,IAAI,CAAC,QAAQ;QACX,WAAW,EAAE,kBAAkB;IACjC;IACA,IAAI,CAAC,QAAQ;QACX,WAAW,EAAE,oBAAoB;IACnC;IACA,OAAO;AACT;AACA,SAAS,eAAe,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,cAAc;IACjG,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB;IACF;IACA,MAAM,kBAAkB,SAAS,OAAO;IACxC,MAAM,MAAM,KAAK,GAAG,CAAC,SAAS,MAAM,EAAE,eAAe,MAAM;IAC3D,MAAM,aAAa,EAAE;IACrB,MAAM,SAAS,cAAc,CAAC,EAAE,CAAC,GAAG;IACpC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;QAC5B,MAAM,cAAc,QAAQ,CAAC,EAAE;QAC/B,IAAI,gBAAgB,MAAM;YACxB;QACF;QACA,MAAM,eAAe,cAAc,CAAC,EAAE;QACtC,IAAI,aAAa,MAAM,KAAK,GAAG;YAC7B;QACF;QACA,IAAI,aAAa,KAAK,GAAG,QAAQ;YAC/B;QACF;QACA,MAAO,WAAW,MAAM,GAAG,KAAK,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,MAAM,IAAI,aAAa,KAAK,CAAE;YAC9F,WAAW,iBAAiB,CAAC,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,MAAM;YAC/G,WAAW,GAAG;QAChB;QACA,IAAI,WAAW,MAAM,GAAG,GAAG;YACzB,WAAW,iBAAiB,CAAC,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,aAAa,KAAK;QAC3F,OAAO;YACL,WAAW,OAAO,CAAC,OAAO,aAAa,KAAK;QAC9C;QACA,IAAI,YAAY,4BAA4B,EAAE;YAC5C,MAAM,YAAY,YAAY,OAAO,CAAC,iBAAiB;YACvD,MAAM,iBAAiB,MAAM,qBAAqB,CAAC,cAAc,CAAC,WAAW;YAC7E,MAAM,cAAc,YAAY,cAAc,CAAC,iBAAiB;YAChE,MAAM,wBAAwB,eAAe,cAAc,CAAC,aAAa;YACzE,MAAM,aAAa,MAAM,IAAI,CAAC,YAAY,4BAA4B,EAAE,aAAa,KAAK,EAAE,CAAC,GAAG,OAAO,MAAM,gBAAgB;YAC7H,MAAM,aAAa,QAAQ,gBAAgB,CAAC,gBAAgB,SAAS,CAAC,GAAG,aAAa,GAAG;YACzF,gBACE,SACA,YACA,eAAe,aAAa,KAAK,KAAK,GACtC,aAAa,KAAK,EAClB,YACA,YACA,OACA,iBAAiB,GACjB;YAEF,kBAAkB;YAClB;QACF;QACA,MAAM,uBAAuB,YAAY,OAAO,CAAC,iBAAiB;QAClE,IAAI,yBAAyB,MAAM;YACjC,MAAM,OAAO,WAAW,MAAM,GAAG,IAAI,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,MAAM,GAAG,MAAM,qBAAqB;YAC3G,MAAM,wBAAwB,KAAK,cAAc,CAAC,sBAAsB;YACxE,WAAW,IAAI,CAAC,IAAI,kBAAkB,uBAAuB,aAAa,GAAG;QAC/E;IACF;IACA,MAAO,WAAW,MAAM,GAAG,EAAG;QAC5B,WAAW,iBAAiB,CAAC,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,MAAM;QAC/G,WAAW,GAAG;IAChB;AACF;AACA,IAAI,oBAAoB;IACtB,OAAO;IACP,OAAO;IACP,YAAY,MAAM,EAAE,MAAM,CAAE;QAC1B,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG;IAChB;AACF;AAEA,yBAAyB;AACzB,SAAS,cAAc,SAAS,EAAE,OAAO,EAAE,eAAe,EAAE,iBAAiB,EAAE,UAAU,EAAE,wBAAwB,EAAE,iBAAiB,EAAE,OAAO;IAC7I,OAAO,IAAI,QACT,WACA,SACA,iBACA,mBACA,YACA,0BACA,mBACA;AAEJ;AACA,SAAS,kBAAkB,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,iBAAiB,EAAE,OAAO;IAC3E,MAAM,WAAW,eAAe,UAAU;IAC1C,MAAM,SAAS,YAAY,iBAAiB,CAAC,MAAM,mBAAmB,QAAQ,UAAU;IACxF,KAAK,MAAM,WAAW,SAAU;QAC9B,OAAO,IAAI,CAAC;YACV,eAAe;YACf,SAAS,QAAQ,OAAO;YACxB;YACA;YACA,UAAU,QAAQ,QAAQ;QAC5B;IACF;AACF;AACA,SAAS,YAAY,UAAU,EAAE,MAAM;IACrC,IAAI,OAAO,MAAM,GAAG,WAAW,MAAM,EAAE;QACrC,OAAO;IACT;IACA,IAAI,YAAY;IAChB,OAAO,WAAW,KAAK,CAAC,CAAC;QACvB,IAAK,IAAI,IAAI,WAAW,IAAI,OAAO,MAAM,EAAE,IAAK;YAC9C,IAAI,kBAAkB,MAAM,CAAC,EAAE,EAAE,aAAa;gBAC5C,YAAY,IAAI;gBAChB,OAAO;YACT;QACF;QACA,OAAO;IACT;AACF;AACA,SAAS,kBAAkB,aAAa,EAAE,SAAS;IACjD,IAAI,CAAC,eAAe;QAClB,OAAO;IACT;IACA,IAAI,kBAAkB,WAAW;QAC/B,OAAO;IACT;IACA,MAAM,MAAM,UAAU,MAAM;IAC5B,OAAO,cAAc,MAAM,GAAG,OAAO,cAAc,MAAM,CAAC,GAAG,SAAS,aAAa,aAAa,CAAC,IAAI,KAAK;AAC5G;AACA,IAAI,UAAU;IACZ,YAAY,cAAc,EAAE,OAAO,EAAE,eAAe,EAAE,iBAAiB,EAAE,UAAU,EAAE,wBAAwB,EAAE,iBAAiB,EAAE,QAAQ,CAAE;QAC1I,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,wBAAwB,GAAG;QAChC,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,6BAA6B,GAAG,IAAI,6BACvC,iBACA;QAEF,IAAI,CAAC,OAAO,GAAG,CAAC;QAChB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,YAAY,GAAG;YAAC;SAAK;QAC1B,IAAI,CAAC,iBAAiB,GAAG,CAAC;QAC1B,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,QAAQ,GAAG,YAAY,SAAS;QACrC,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,kBAAkB,GAAG,EAAE;QAC5B,IAAI,YAAY;YACd,KAAK,MAAM,YAAY,OAAO,IAAI,CAAC,YAAa;gBAC9C,MAAM,WAAW,eAAe,UAAU;gBAC1C,KAAK,MAAM,WAAW,SAAU;oBAC9B,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;wBAC3B,SAAS,QAAQ,OAAO;wBACxB,MAAM,UAAU,CAAC,SAAS;oBAC5B;gBACF;YACF;QACF;IACF;IACA,QAAQ;IACR,YAAY;IACZ,aAAa;IACb,kBAAkB;IAClB,mBAAmB;IACnB,SAAS;IACT,YAAY;IACZ,8BAA8B;IAC9B,mBAAmB;IACnB,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,kBAAkB;IAChC;IACA,UAAU;QACR,KAAK,MAAM,QAAQ,IAAI,CAAC,YAAY,CAAE;YACpC,IAAI,MAAM;gBACR,KAAK,OAAO;YACd;QACF;IACF;IACA,kBAAkB,OAAO,EAAE;QACzB,OAAO,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC;IACzC;IACA,iBAAiB,OAAO,EAAE;QACxB,OAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC;IACxC;IACA,oBAAoB,KAAK,EAAE;QACzB,OAAO,IAAI,CAAC,6BAA6B,CAAC,uBAAuB,CAAC;IACpE;IACA,qBAAqB;QACnB,MAAM,oBAAoB;YACxB,QAAQ,CAAC;gBACP,IAAI,eAAe,IAAI,CAAC,cAAc,EAAE;oBACtC,OAAO,IAAI,CAAC,QAAQ;gBACtB;gBACA,OAAO,IAAI,CAAC,kBAAkB,CAAC;YACjC;YACA,YAAY,CAAC;gBACX,OAAO,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC;YAC5C;QACF;QACA,MAAM,SAAS,EAAE;QACjB,MAAM,YAAY,IAAI,CAAC,cAAc;QACrC,MAAM,UAAU,kBAAkB,MAAM,CAAC;QACzC,IAAI,SAAS;YACX,MAAM,gBAAgB,QAAQ,UAAU;YACxC,IAAI,eAAe;gBACjB,IAAK,IAAI,cAAc,cAAe;oBACpC,kBACE,QACA,YACA,aAAa,CAAC,WAAW,EACzB,IAAI,EACJ;gBAEJ;YACF;YACA,MAAM,sBAAsB,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC;YAC/D,IAAI,qBAAqB;gBACvB,oBAAoB,OAAO,CAAC,CAAC;oBAC3B,MAAM,mBAAmB,IAAI,CAAC,kBAAkB,CAAC;oBACjD,IAAI,kBAAkB;wBACpB,MAAM,WAAW,iBAAiB,iBAAiB;wBACnD,IAAI,UAAU;4BACZ,kBACE,QACA,UACA,kBACA,IAAI,EACJ;wBAEJ;oBACF;gBACF;YACF;QACF;QACA,OAAO,IAAI,CAAC,CAAC,IAAI,KAAO,GAAG,QAAQ,GAAG,GAAG,QAAQ;QACjD,OAAO;IACT;IACA,gBAAgB;QACd,IAAI,IAAI,CAAC,WAAW,KAAK,MAAM;YAC7B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,kBAAkB;QAC5C;QACA,OAAO,IAAI,CAAC,WAAW;IACzB;IACA,aAAa,OAAO,EAAE;QACpB,MAAM,KAAK,EAAE,IAAI,CAAC,WAAW;QAC7B,MAAM,SAAS,QAAQ,iBAAiB;QACxC,IAAI,CAAC,YAAY,CAAC,GAAG,GAAG;QACxB,OAAO;IACT;IACA,QAAQ,MAAM,EAAE;QACd,OAAO,IAAI,CAAC,YAAY,CAAC,eAAe,QAAQ;IAClD;IACA,mBAAmB,SAAS,EAAE,UAAU,EAAE;QACxC,IAAI,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE;YACrC,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU;QAC1C,OAAO,IAAI,IAAI,CAAC,kBAAkB,EAAE;YAClC,MAAM,qBAAqB,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;YAC1D,IAAI,oBAAoB;gBACtB,IAAI,CAAC,iBAAiB,CAAC,UAAU,GAAG,YAClC,oBACA,cAAc,WAAW,KAAK;gBAEhC,OAAO,IAAI,CAAC,iBAAiB,CAAC,UAAU;YAC1C;QACF;QACA,OAAO,KAAK;IACd;IACA,aAAa,QAAQ,EAAE,SAAS,EAAE,YAAY,CAAC,EAAE;QAC/C,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,WAAW,OAAO;QACrD,OAAO;YACL,QAAQ,EAAE,UAAU,CAAC,SAAS,CAAC,EAAE,SAAS,EAAE,EAAE,UAAU;YACxD,WAAW,EAAE,SAAS;YACtB,cAAc,EAAE,YAAY;QAC9B;IACF;IACA,cAAc,QAAQ,EAAE,SAAS,EAAE,YAAY,CAAC,EAAE;QAChD,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,WAAW,MAAM;QACpD,OAAO;YACL,QAAQ,EAAE,UAAU,CAAC,eAAe,CAAC,EAAE,SAAS,EAAE,EAAE,UAAU;YAC9D,WAAW,EAAE,SAAS;YACtB,cAAc,EAAE,YAAY;QAC9B;IACF;IACA,UAAU,QAAQ,EAAE,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE;QAC1D,IAAI,IAAI,CAAC,OAAO,KAAK,CAAC,GAAG;YACvB,IAAI,CAAC,OAAO,GAAG,YAAY,iBAAiB,CAC1C,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,EAC9B,IAAI,EACJ,IAAI,CAAC,QAAQ,CAAC,UAAU;YAE1B,IAAI,CAAC,aAAa;QACpB;QACA,IAAI;QACJ,IAAI,CAAC,aAAa,cAAc,eAAe,IAAI,EAAE;YACnD,cAAc;YACd,MAAM,qBAAqB,IAAI,CAAC,6BAA6B,CAAC,oBAAoB;YAClF,MAAM,eAAe,IAAI,CAAC,aAAa,CAAC,WAAW;YACnD,MAAM,kBAAkB,qBAAqB,GAAG,CAC9C,GACA,mBAAmB,UAAU,EAC7B,mBAAmB,SAAS,EAC5B,MACA,aAAa,SAAS,EACtB,aAAa,YAAY,EACzB,aAAa,YAAY;YAE3B,MAAM,gBAAgB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CACtD,MACA;YAEF,IAAI;YACJ,IAAI,eAAe;gBACjB,YAAY,qBAAqB,4BAA4B,CAC3D,eACA,iBACA,IAAI;YAER,OAAO;gBACL,YAAY,qBAAqB,UAAU,CACzC,WACA;YAEJ;YACA,YAAY,IAAI,eACd,MACA,IAAI,CAAC,OAAO,EACZ,CAAC,GACD,CAAC,GACD,OACA,MACA,WACA;QAEJ,OAAO;YACL,cAAc;YACd,UAAU,KAAK;QACjB;QACA,WAAW,WAAW;QACtB,MAAM,eAAe,IAAI,CAAC,gBAAgB,CAAC;QAC3C,MAAM,aAAa,aAAa,OAAO,CAAC,MAAM;QAC9C,MAAM,aAAa,IAAI,WACrB,kBACA,UACA,IAAI,CAAC,kBAAkB,EACvB,IAAI,CAAC,wBAAwB;QAE/B,MAAM,IAAI,gBACR,IAAI,EACJ,cACA,aACA,GACA,WACA,YACA,MACA;QAEF,kBAAkB;QAClB,OAAO;YACL;YACA;YACA,WAAW,EAAE,KAAK;YAClB,cAAc,EAAE,YAAY;QAC9B;IACF;AACF;AACA,SAAS,YAAY,OAAO,EAAE,IAAI;IAChC,UAAU,MAAM;IAChB,QAAQ,UAAU,GAAG,QAAQ,UAAU,IAAI,CAAC;IAC5C,QAAQ,UAAU,CAAC,KAAK,GAAG;QACzB,yBAAyB,QAAQ,uBAAuB;QACxD,UAAU,QAAQ,QAAQ;QAC1B,MAAM,QAAQ,SAAS;IACzB;IACA,QAAQ,UAAU,CAAC,KAAK,GAAG,QAAQ,QAAQ,UAAU,CAAC,KAAK;IAC3D,OAAO;AACT;AACA,IAAI,uBAAuB,MAAM;IAC/B;;;;;;;GAOC,GACD,YAAY,MAAM,EAAE,SAAS,EAAE,eAAe,CAAE;QAC9C,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,eAAe,GAAG;IACzB;IACA,OAAO,cAAc,cAAc,EAAE,qBAAqB,EAAE;QAC1D,IAAI,UAAU;QACd,IAAI,aAAa,gBAAgB,aAAa;QAC9C,KAAK,MAAM,SAAS,sBAAuB;YACzC,aAAa,WAAW,IAAI,CAAC,YAAY,MAAM,UAAU;YACzD,UAAU,IAAI,sBAAsB,SAAS,YAAY,MAAM,sBAAsB;QACvF;QACA,OAAO;IACT;IACA,OAAO,WAAW,SAAS,EAAE,eAAe,EAAE;QAC5C,OAAO,IAAI,sBAAsB,MAAM,IAAI,WAAW,MAAM,YAAY;IAC1E;IACA,OAAO,6BAA6B,SAAS,EAAE,eAAe,EAAE,OAAO,EAAE;QACvE,MAAM,kBAAkB,QAAQ,mBAAmB,CAAC;QACpD,MAAM,YAAY,IAAI,WAAW,MAAM;QACvC,MAAM,YAAY,QAAQ,aAAa,CAAC,UAAU,CAAC;QACnD,MAAM,0BAA0B,sBAAsB,eAAe,CACnE,iBACA,iBACA;QAEF,OAAO,IAAI,sBAAsB,MAAM,WAAW;IACpD;IACA,IAAI,YAAY;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,SAAS;IACjC;IACA,WAAW;QACT,OAAO,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IACnC;IACA,OAAO,KAAK,EAAE;QACZ,OAAO,sBAAsB,MAAM,CAAC,IAAI,EAAE;IAC5C;IACA,OAAO,OAAO,CAAC,EAAE,CAAC,EAAE;QAClB,GAAG;YACD,IAAI,MAAM,GAAG;gBACX,OAAO;YACT;YACA,IAAI,CAAC,KAAK,CAAC,GAAG;gBACZ,OAAO;YACT;YACA,IAAI,CAAC,KAAK,CAAC,GAAG;gBACZ,OAAO;YACT;YACA,IAAI,EAAE,SAAS,KAAK,EAAE,SAAS,IAAI,EAAE,eAAe,KAAK,EAAE,eAAe,EAAE;gBAC1E,OAAO;YACT;YACA,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,MAAM;QACd,QAAS,KAAM;IACjB;IACA,OAAO,gBAAgB,uBAAuB,EAAE,oBAAoB,EAAE,eAAe,EAAE;QACrF,IAAI,YAAY,CAAC,EAAE,UAAU;QAC7B,IAAI,aAAa;QACjB,IAAI,aAAa;QACjB,IAAI,oBAAoB,MAAM;YAC5B,YAAY,gBAAgB,SAAS;YACrC,aAAa,gBAAgB,YAAY;YACzC,aAAa,gBAAgB,YAAY;QAC3C;QACA,OAAO,qBAAqB,GAAG,CAC7B,yBACA,qBAAqB,UAAU,EAC/B,qBAAqB,SAAS,EAC9B,MACA,WACA,YACA;IAEJ;IACA,eAAe,SAAS,EAAE,OAAO,EAAE;QACjC,IAAI,cAAc,MAAM;YACtB,OAAO,IAAI;QACb;QACA,IAAI,UAAU,OAAO,CAAC,SAAS,CAAC,GAAG;YACjC,OAAO,sBAAsB,eAAe,CAAC,IAAI,EAAE,WAAW;QAChE;QACA,MAAM,SAAS,UAAU,KAAK,CAAC;QAC/B,IAAI,SAAS,IAAI;QACjB,KAAK,MAAM,SAAS,OAAQ;YAC1B,SAAS,sBAAsB,eAAe,CAAC,QAAQ,OAAO;QAChE;QACA,OAAO;IACT;IACA,OAAO,gBAAgB,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE;QACjD,MAAM,cAAc,QAAQ,mBAAmB,CAAC;QAChD,MAAM,UAAU,OAAO,SAAS,CAAC,IAAI,CAAC;QACtC,MAAM,wBAAwB,QAAQ,aAAa,CAAC,UAAU,CAAC;QAC/D,MAAM,WAAW,sBAAsB,eAAe,CACpD,OAAO,eAAe,EACtB,aACA;QAEF,OAAO,IAAI,sBAAsB,QAAQ,SAAS;IACpD;IACA,gBAAgB;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW;IACnC;IACA,sBAAsB,IAAI,EAAE;QAC1B,MAAM,SAAS,EAAE;QACjB,IAAI,OAAO,IAAI;QACf,MAAO,QAAQ,SAAS,KAAM;YAC5B,OAAO,IAAI,CAAC;gBACV,wBAAwB,KAAK,eAAe;gBAC5C,YAAY,KAAK,SAAS,CAAC,qBAAqB,CAAC,KAAK,MAAM,EAAE,aAAa;YAC7E;YACA,OAAO,KAAK,MAAM;QACpB;QACA,OAAO,SAAS,OAAO,OAAO,OAAO,KAAK,KAAK;IACjD;AACF;AACA,IAAI,iBAAiB,MAAM;IACzB;;;;;;;;;;GAUC,GACD,YAAY,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,oBAAoB,EAAE,OAAO,EAAE,cAAc,EAAE,qBAAqB,CAAE;QACrH,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,oBAAoB,GAAG;QAC5B,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,qBAAqB,GAAG;QAC7B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI;QACnD,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,UAAU,GAAG;IACpB;IACA,qBAAqB,KAAK,EAAE;IAC5B,iBAAiB;IACjB,OAAO,OAAO,IAAI,gBAChB,MACA,GACA,GACA,GACA,OACA,MACA,MACA,MACA;IACF;;;;GAIC,GACD,UAAU;IACV;;;;GAIC,GACD,WAAW;IACX;;GAEC,GACD,MAAM;IACN,OAAO,KAAK,EAAE;QACZ,IAAI,UAAU,MAAM;YAClB,OAAO;QACT;QACA,OAAO,gBAAgB,OAAO,CAAC,IAAI,EAAE;IACvC;IACA,OAAO,QAAQ,CAAC,EAAE,CAAC,EAAE;QACnB,IAAI,MAAM,GAAG;YACX,OAAO;QACT;QACA,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI;YACjC,OAAO;QACT;QACA,OAAO,qBAAqB,MAAM,CAAC,EAAE,qBAAqB,EAAE,EAAE,qBAAqB;IACrF;IACA;;GAEC,GACD,OAAO,kBAAkB,CAAC,EAAE,CAAC,EAAE;QAC7B,GAAG;YACD,IAAI,MAAM,GAAG;gBACX,OAAO;YACT;YACA,IAAI,CAAC,KAAK,CAAC,GAAG;gBACZ,OAAO;YACT;YACA,IAAI,CAAC,KAAK,CAAC,GAAG;gBACZ,OAAO;YACT;YACA,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,IAAI,EAAE,OAAO,KAAK,EAAE,OAAO,EAAE;gBAC3E,OAAO;YACT;YACA,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,MAAM;QACd,QAAS,KAAM;IACjB;IACA,QAAQ;QACN,OAAO,IAAI;IACb;IACA,OAAO,OAAO,EAAE,EAAE;QAChB,MAAO,GAAI;YACT,GAAG,SAAS,GAAG,CAAC;YAChB,GAAG,UAAU,GAAG,CAAC;YACjB,KAAK,GAAG,MAAM;QAChB;IACF;IACA,QAAQ;QACN,gBAAgB,MAAM,CAAC,IAAI;IAC7B;IACA,MAAM;QACJ,OAAO,IAAI,CAAC,MAAM;IACpB;IACA,UAAU;QACR,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,OAAO,IAAI,CAAC,MAAM;QACpB;QACA,OAAO,IAAI;IACb;IACA,KAAK,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,oBAAoB,EAAE,OAAO,EAAE,cAAc,EAAE,qBAAqB,EAAE;QACtG,OAAO,IAAI,gBACT,IAAI,EACJ,QACA,UACA,WACA,sBACA,SACA,gBACA;IAEJ;IACA,cAAc;QACZ,OAAO,IAAI,CAAC,SAAS;IACvB;IACA,eAAe;QACb,OAAO,IAAI,CAAC,UAAU;IACxB;IACA,QAAQ,OAAO,EAAE;QACf,OAAO,QAAQ,OAAO,CAAC,IAAI,CAAC,MAAM;IACpC;IACA,WAAW;QACT,MAAM,IAAI,EAAE;QACZ,IAAI,CAAC,YAAY,CAAC,GAAG;QACrB,OAAO,MAAM,EAAE,IAAI,CAAC,OAAO;IAC7B;IACA,aAAa,GAAG,EAAE,QAAQ,EAAE;QAC1B,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,WAAW,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK;QAC3C;QACA,GAAG,CAAC,WAAW,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAC,cAAc,EAAE,WAAW,EAAE,EAAE,IAAI,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC;QACnH,OAAO;IACT;IACA,0BAA0B,qBAAqB,EAAE;QAC/C,IAAI,IAAI,CAAC,qBAAqB,KAAK,uBAAuB;YACxD,OAAO,IAAI;QACb;QACA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CACrB,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,oBAAoB,EACzB,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,cAAc,EACnB;IAEJ;IACA,YAAY,OAAO,EAAE;QACnB,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS;YAC5B,OAAO,IAAI;QACb;QACA,OAAO,IAAI,gBACT,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,oBAAoB,EACzB,SACA,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,qBAAqB;IAE9B;IACA,gCAAgC;IAChC,cAAc,KAAK,EAAE;QACnB,IAAI,KAAK,IAAI;QACb,MAAO,MAAM,GAAG,SAAS,KAAK,MAAM,SAAS,CAAE;YAC7C,IAAI,GAAG,MAAM,KAAK,MAAM,MAAM,EAAE;gBAC9B,OAAO;YACT;YACA,KAAK,GAAG,MAAM;QAChB;QACA,OAAO;IACT;IACA,oBAAoB;QAClB,OAAO;YACL,QAAQ,eAAe,IAAI,CAAC,MAAM;YAClC,sBAAsB,IAAI,CAAC,oBAAoB;YAC/C,SAAS,IAAI,CAAC,OAAO;YACrB,gBAAgB,IAAI,CAAC,cAAc,EAAE,sBAAsB,IAAI,CAAC,MAAM,EAAE,kBAAkB,SAAS,EAAE;YACrG,uBAAuB,IAAI,CAAC,qBAAqB,EAAE,sBAAsB,IAAI,CAAC,cAAc,KAAK,EAAE;QACrG;IACF;IACA,OAAO,UAAU,IAAI,EAAE,KAAK,EAAE;QAC5B,MAAM,iBAAiB,qBAAqB,aAAa,CAAC,MAAM,kBAAkB,MAAM,MAAM,cAAc;QAC5G,OAAO,IAAI,gBACT,MACA,iBAAiB,MAAM,MAAM,GAC7B,MAAM,QAAQ,IAAI,CAAC,GACnB,MAAM,SAAS,IAAI,CAAC,GACpB,MAAM,oBAAoB,EAC1B,MAAM,OAAO,EACb,gBACA,qBAAqB,aAAa,CAAC,gBAAgB,MAAM,qBAAqB;IAElF;AACF;AACA,IAAI,2BAA2B;IAC7B,sBAAsB;IACtB,wBAAwB;IACxB,WAAW,MAAM;IACjB,YAAY,qBAAqB,EAAE,uBAAuB,CAAE;QAC1D,IAAI,CAAC,qBAAqB,GAAG,sBAAsB,OAAO,CACxD,CAAC;YACC,IAAI,aAAa,KAAK;gBACpB,IAAI,CAAC,QAAQ,GAAG;gBAChB,OAAO,EAAE;YACX;YACA,OAAO,eAAe,UAAU,aAAa,GAAG,CAAC,CAAC,IAAM,EAAE,OAAO;QACnE;QAEF,IAAI,CAAC,uBAAuB,GAAG,wBAAwB,OAAO,CAC5D,CAAC,WAAa,eAAe,UAAU,aAAa,GAAG,CAAC,CAAC,IAAM,EAAE,OAAO;IAE5E;IACA,IAAI,gBAAgB;QAClB,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,uBAAuB,CAAC,MAAM,KAAK;IAClE;IACA,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,qBAAqB,CAAC,MAAM,KAAK,KAAK,CAAC,IAAI,CAAC,QAAQ;IAClE;IACA,MAAM,MAAM,EAAE;QACZ,KAAK,MAAM,YAAY,IAAI,CAAC,uBAAuB,CAAE;YACnD,IAAI,SAAS,SAAS;gBACpB,OAAO;YACT;QACF;QACA,KAAK,MAAM,YAAY,IAAI,CAAC,qBAAqB,CAAE;YACjD,IAAI,SAAS,SAAS;gBACpB,OAAO;YACT;QACF;QACA,OAAO,IAAI,CAAC,QAAQ;IACtB;AACF;AACA,IAAI,aAAa;IACf,YAAY,gBAAgB,EAAE,QAAQ,EAAE,kBAAkB,EAAE,wBAAwB,CAAE;QACpF,IAAI,CAAC,wBAAwB,GAAG;QAChC,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,mBAAmB,GAAG;QAC3B,uCAAW;;QAEX,OAAO;YACL,IAAI,CAAC,SAAS,GAAG;QACnB;QACA,IAAI,CAAC,OAAO,GAAG,EAAE;QACjB,IAAI,CAAC,aAAa,GAAG,EAAE;QACvB,IAAI,CAAC,kBAAkB,GAAG;IAC5B;IACA,kBAAkB;IAClB;;GAEC,GACD,UAAU;IACV;;GAEC,GACD,QAAQ;IACR;;GAEC,GACD,cAAc;IACd,mBAAmB;IACnB,oBAAoB;IACpB,QAAQ,KAAK,EAAE,QAAQ,EAAE;QACvB,IAAI,CAAC,iBAAiB,CAAC,MAAM,qBAAqB,EAAE;IACtD;IACA,kBAAkB,UAAU,EAAE,QAAQ,EAAE;QACtC,IAAI,IAAI,CAAC,kBAAkB,IAAI,UAAU;YACvC;QACF;QACA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,IAAI,WAAW,YAAY,mBAAmB;YAC9C,IAAI,2BAA2B;YAC/B,IAAI,IAAI,CAAC,wBAAwB,EAAE,eAAe;gBAChD,2BAA2B;YAC7B;YACA,IAAI,IAAI,CAAC,mBAAmB,CAAC,MAAM,GAAG,KAAK,IAAI,CAAC,wBAAwB,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,YAAY,EAAE;gBACvK,MAAM,UAAU,YAAY,mBAAmB,EAAE;gBACjD,KAAK,MAAM,aAAa,IAAI,CAAC,mBAAmB,CAAE;oBAChD,IAAI,UAAU,OAAO,CAAC,UAAU;wBAC9B,WAAW,qBAAqB,GAAG,CACjC,UACA,GACA,oBAAoB,UAAU,IAAI,GAClC,MACA,CAAC,EAAE,UAAU,KACb,GACA;oBAEJ;gBACF;gBACA,IAAI,IAAI,CAAC,wBAAwB,EAAE;oBACjC,2BAA2B,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC;gBACjE;YACF;YACA,IAAI,0BAA0B;gBAC5B,WAAW,qBAAqB,GAAG,CACjC,UACA,GACA,EAAE,UAAU,KACZ,0BACA,CAAC,EAAE,UAAU,KACb,GACA;YAEJ;YACA,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,KAAK,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,EAAE,KAAK,UAAU;gBACnG,IAAI,CAAC,kBAAkB,GAAG;gBAC1B;YACF;YACA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB;YAC/C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YACxB,IAAI,CAAC,kBAAkB,GAAG;YAC1B;QACF;QACA,MAAM,SAAS,YAAY,mBAAmB,EAAE;QAChD,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YAChB,YAAY,IAAI,CAAC,kBAAkB;YACnC;YACA,0DAA0D;YAC1D;QACF;QACA,IAAI,CAAC,kBAAkB,GAAG;IAC5B;IACA,UAAU,KAAK,EAAE,UAAU,EAAE;QAC3B,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,KAAK,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,UAAU,KAAK,aAAa,GAAG;YAClG,IAAI,CAAC,OAAO,CAAC,GAAG;QAClB;QACA,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,GAAG;YAC7B,IAAI,CAAC,kBAAkB,GAAG,CAAC;YAC3B,IAAI,CAAC,OAAO,CAAC,OAAO;YACpB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,UAAU,GAAG;QACrD;QACA,OAAO,IAAI,CAAC,OAAO;IACrB;IACA,gBAAgB,KAAK,EAAE,UAAU,EAAE;QACjC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,KAAK,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,EAAE,KAAK,aAAa,GAAG;YACzG,IAAI,CAAC,aAAa,CAAC,GAAG;YACtB,IAAI,CAAC,aAAa,CAAC,GAAG;QACxB;QACA,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,KAAK,GAAG;YACnC,IAAI,CAAC,kBAAkB,GAAG,CAAC;YAC3B,IAAI,CAAC,OAAO,CAAC,OAAO;YACpB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG,EAAE,GAAG;QACtD;QACA,MAAM,SAAS,IAAI,YAAY,IAAI,CAAC,aAAa,CAAC,MAAM;QACxD,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,KAAK,IAAK;YAC7D,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,aAAa,CAAC,EAAE;QACnC;QACA,OAAO;IACT;AACF;AAEA,kBAAkB;AAClB,IAAI,eAAe;IACjB,YAAY,KAAK,EAAE,QAAQ,CAAE;QAC3B,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG;IAChB;IACA,YAAY,aAAa,GAAG,IAAI,MAAM;IACtC,eAAe,aAAa,GAAG,IAAI,MAAM;IACzC,qBAAqB,aAAa,GAAG,IAAI,MAAM;IAC/C,OAAO;IACP,UAAU;QACR,KAAK,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,MAAM,GAAI;YAC7C,QAAQ,OAAO;QACjB;IACF;IACA,SAAS,KAAK,EAAE;QACd,IAAI,CAAC,MAAM,GAAG;IAChB;IACA,cAAc;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW;IAChC;IACA;;GAEC,GACD,WAAW,OAAO,EAAE,mBAAmB,EAAE;QACvC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,SAAS,EAAE;QACzC,IAAI,qBAAqB;YACvB,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,QAAQ,SAAS,EAAE;QACjD;IACF;IACA;;GAEC,GACD,OAAO,SAAS,EAAE;QAChB,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;IAC/B;IACA;;GAEC,GACD,WAAW,WAAW,EAAE;QACtB,OAAO,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;IACrC;IACA;;GAEC,GACD,cAAc;QACZ,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW;IAChC;IACA;;GAEC,GACD,WAAW,SAAS,EAAE;QACpB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;IAC3B;IACA;;GAEC,GACD,oBAAoB,SAAS,EAAE,eAAe,EAAE,iBAAiB,EAAE,UAAU,EAAE,wBAAwB,EAAE;QACvG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,YAAY;YAClC,IAAI,aAAa,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;YACvC,IAAI,CAAC,YAAY;gBACf,OAAO;YACT;YACA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,WAAW,cAC5B,WACA,YACA,iBACA,mBACA,YACA,0BACA,IAAI,EACJ,IAAI,CAAC,QAAQ;QAEjB;QACA,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;IAC5B;AACF;AAEA,eAAe;AACf,IAAI,WAAW;IACb,SAAS;IACT,cAAc;IACd,oBAAoB;IACpB,YAAY,OAAO,CAAE;QACnB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,aAAa,GAAG,IAAI,aACvB,MAAM,kBAAkB,CAAC,QAAQ,KAAK,EAAE,QAAQ,QAAQ,GACxD,QAAQ,OAAO;QAEjB,IAAI,CAAC,mBAAmB,GAAG,aAAa,GAAG,IAAI;IACjD;IACA,UAAU;QACR,IAAI,CAAC,aAAa,CAAC,OAAO;IAC5B;IACA;;GAEC,GACD,SAAS,KAAK,EAAE,QAAQ,EAAE;QACxB,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,MAAM,kBAAkB,CAAC,OAAO;IAC9D;IACA;;GAEC,GACD,cAAc;QACZ,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW;IACvC;IACA;;;GAGC,GACD,iCAAiC,gBAAgB,EAAE,eAAe,EAAE,iBAAiB,EAAE;QACrF,OAAO,IAAI,CAAC,4BAA4B,CAAC,kBAAkB,iBAAiB;YAAE;QAAkB;IAClG;IACA;;;GAGC,GACD,6BAA6B,gBAAgB,EAAE,eAAe,EAAE,aAAa,EAAE;QAC7E,OAAO,IAAI,CAAC,YAAY,CACtB,kBACA,iBACA,cAAc,iBAAiB,EAC/B,cAAc,UAAU,EACxB,IAAI,yBACF,cAAc,wBAAwB,IAAI,EAAE,EAC5C,cAAc,0BAA0B,IAAI,EAAE;IAGpD;IACA;;GAEC,GACD,YAAY,gBAAgB,EAAE;QAC5B,OAAO,IAAI,CAAC,YAAY,CAAC,kBAAkB,GAAG,MAAM,MAAM;IAC5D;IACA,aAAa,gBAAgB,EAAE,eAAe,EAAE,iBAAiB,EAAE,UAAU,EAAE,wBAAwB,EAAE;QACvG,MAAM,sBAAsB,IAAI,yBAAyB,IAAI,CAAC,aAAa,EAAE;QAC7E,MAAO,oBAAoB,CAAC,CAAC,MAAM,GAAG,EAAG;YACvC,oBAAoB,CAAC,CAAC,GAAG,CAAC,CAAC,UAAY,IAAI,CAAC,kBAAkB,CAAC,QAAQ,SAAS;YAChF,oBAAoB,YAAY;QAClC;QACA,OAAO,IAAI,CAAC,oBAAoB,CAC9B,kBACA,iBACA,mBACA,YACA;IAEJ;IACA,mBAAmB,SAAS,EAAE;QAC5B,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,YAAY;YAC5C,IAAI,CAAC,oBAAoB,CAAC;YAC1B,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,WAAW;QAC1C;IACF;IACA,qBAAqB,SAAS,EAAE;QAC9B,MAAM,UAAU,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;QAC1C,IAAI,SAAS;YACX,MAAM,aAAa,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,KAAK,aAAa,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,aAAa,KAAK;YACrH,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,SAAS;QACzC;IACF;IACA;;GAEC,GACD,WAAW,UAAU,EAAE,aAAa,EAAE,EAAE,kBAAkB,CAAC,EAAE,oBAAoB,IAAI,EAAE;QACrF,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,YAAY;QAC1C,OAAO,IAAI,CAAC,oBAAoB,CAAC,WAAW,SAAS,EAAE,iBAAiB;IAC1E;IACA;;GAEC,GACD,qBAAqB,SAAS,EAAE,kBAAkB,CAAC,EAAE,oBAAoB,IAAI,EAAE,aAAa,IAAI,EAAE,2BAA2B,IAAI,EAAE;QACjI,OAAO,IAAI,CAAC,aAAa,CAAC,mBAAmB,CAC3C,WACA,iBACA,mBACA,YACA;IAEJ;AACF;AACA,IAAI,UAAU,eAAe,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2901, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/node_modules/%40shikijs/core/dist/index.mjs"], "sourcesContent": ["import { ShikiError as ShikiError$1 } from '@shikijs/types';\nexport * from '@shikijs/types';\nimport { FontStyle, INITIAL, EncodedTokenMetadata, Registry as Registry$1, Theme } from '@shikijs/vscode-textmate';\nimport { toHtml } from 'hast-util-to-html';\n\nfunction resolveColorReplacements(theme, options) {\n  const replacements = typeof theme === \"string\" ? {} : { ...theme.colorReplacements };\n  const themeName = typeof theme === \"string\" ? theme : theme.name;\n  for (const [key, value] of Object.entries(options?.colorReplacements || {})) {\n    if (typeof value === \"string\")\n      replacements[key] = value;\n    else if (key === themeName)\n      Object.assign(replacements, value);\n  }\n  return replacements;\n}\nfunction applyColorReplacements(color, replacements) {\n  if (!color)\n    return color;\n  return replacements?.[color?.toLowerCase()] || color;\n}\n\nfunction toArray(x) {\n  return Array.isArray(x) ? x : [x];\n}\nasync function normalizeGetter(p) {\n  return Promise.resolve(typeof p === \"function\" ? p() : p).then((r) => r.default || r);\n}\nfunction isPlainLang(lang) {\n  return !lang || [\"plaintext\", \"txt\", \"text\", \"plain\"].includes(lang);\n}\nfunction isSpecialLang(lang) {\n  return lang === \"ansi\" || isPlainLang(lang);\n}\nfunction isNoneTheme(theme) {\n  return theme === \"none\";\n}\nfunction isSpecialTheme(theme) {\n  return isNoneTheme(theme);\n}\n\nfunction addClassToHast(node, className) {\n  if (!className)\n    return node;\n  node.properties ||= {};\n  node.properties.class ||= [];\n  if (typeof node.properties.class === \"string\")\n    node.properties.class = node.properties.class.split(/\\s+/g);\n  if (!Array.isArray(node.properties.class))\n    node.properties.class = [];\n  const targets = Array.isArray(className) ? className : className.split(/\\s+/g);\n  for (const c of targets) {\n    if (c && !node.properties.class.includes(c))\n      node.properties.class.push(c);\n  }\n  return node;\n}\n\nfunction splitLines(code, preserveEnding = false) {\n  const parts = code.split(/(\\r?\\n)/g);\n  let index = 0;\n  const lines = [];\n  for (let i = 0; i < parts.length; i += 2) {\n    const line = preserveEnding ? parts[i] + (parts[i + 1] || \"\") : parts[i];\n    lines.push([line, index]);\n    index += parts[i].length;\n    index += parts[i + 1]?.length || 0;\n  }\n  return lines;\n}\nfunction createPositionConverter(code) {\n  const lines = splitLines(code, true).map(([line]) => line);\n  function indexToPos(index) {\n    if (index === code.length) {\n      return {\n        line: lines.length - 1,\n        character: lines[lines.length - 1].length\n      };\n    }\n    let character = index;\n    let line = 0;\n    for (const lineText of lines) {\n      if (character < lineText.length)\n        break;\n      character -= lineText.length;\n      line++;\n    }\n    return { line, character };\n  }\n  function posToIndex(line, character) {\n    let index = 0;\n    for (let i = 0; i < line; i++)\n      index += lines[i].length;\n    index += character;\n    return index;\n  }\n  return {\n    lines,\n    indexToPos,\n    posToIndex\n  };\n}\nfunction guessEmbeddedLanguages(code, _lang, highlighter) {\n  const langs = /* @__PURE__ */ new Set();\n  for (const match of code.matchAll(/lang=[\"']([\\w-]+)[\"']/g)) {\n    langs.add(match[1]);\n  }\n  for (const match of code.matchAll(/(?:```|~~~)([\\w-]+)/g)) {\n    langs.add(match[1]);\n  }\n  for (const match of code.matchAll(/\\\\begin\\{([\\w-]+)\\}/g)) {\n    langs.add(match[1]);\n  }\n  if (!highlighter)\n    return Array.from(langs);\n  const bundle = highlighter.getBundledLanguages();\n  return Array.from(langs).filter((l) => l && bundle[l]);\n}\n\nfunction splitToken(token, offsets) {\n  let lastOffset = 0;\n  const tokens = [];\n  for (const offset of offsets) {\n    if (offset > lastOffset) {\n      tokens.push({\n        ...token,\n        content: token.content.slice(lastOffset, offset),\n        offset: token.offset + lastOffset\n      });\n    }\n    lastOffset = offset;\n  }\n  if (lastOffset < token.content.length) {\n    tokens.push({\n      ...token,\n      content: token.content.slice(lastOffset),\n      offset: token.offset + lastOffset\n    });\n  }\n  return tokens;\n}\nfunction splitTokens(tokens, breakpoints) {\n  const sorted = Array.from(breakpoints instanceof Set ? breakpoints : new Set(breakpoints)).sort((a, b) => a - b);\n  if (!sorted.length)\n    return tokens;\n  return tokens.map((line) => {\n    return line.flatMap((token) => {\n      const breakpointsInToken = sorted.filter((i) => token.offset < i && i < token.offset + token.content.length).map((i) => i - token.offset).sort((a, b) => a - b);\n      if (!breakpointsInToken.length)\n        return token;\n      return splitToken(token, breakpointsInToken);\n    });\n  });\n}\nfunction flatTokenVariants(merged, variantsOrder, cssVariablePrefix, defaultColor) {\n  const token = {\n    content: merged.content,\n    explanation: merged.explanation,\n    offset: merged.offset\n  };\n  const styles = variantsOrder.map((t) => getTokenStyleObject(merged.variants[t]));\n  const styleKeys = new Set(styles.flatMap((t) => Object.keys(t)));\n  const mergedStyles = {};\n  styles.forEach((cur, idx) => {\n    for (const key of styleKeys) {\n      const value = cur[key] || \"inherit\";\n      if (idx === 0 && defaultColor) {\n        mergedStyles[key] = value;\n      } else {\n        const keyName = key === \"color\" ? \"\" : key === \"background-color\" ? \"-bg\" : `-${key}`;\n        const varKey = cssVariablePrefix + variantsOrder[idx] + (key === \"color\" ? \"\" : keyName);\n        mergedStyles[varKey] = value;\n      }\n    }\n  });\n  token.htmlStyle = mergedStyles;\n  return token;\n}\nfunction getTokenStyleObject(token) {\n  const styles = {};\n  if (token.color)\n    styles.color = token.color;\n  if (token.bgColor)\n    styles[\"background-color\"] = token.bgColor;\n  if (token.fontStyle) {\n    if (token.fontStyle & FontStyle.Italic)\n      styles[\"font-style\"] = \"italic\";\n    if (token.fontStyle & FontStyle.Bold)\n      styles[\"font-weight\"] = \"bold\";\n    const decorations = [];\n    if (token.fontStyle & FontStyle.Underline)\n      decorations.push(\"underline\");\n    if (token.fontStyle & FontStyle.Strikethrough)\n      decorations.push(\"line-through\");\n    if (decorations.length)\n      styles[\"text-decoration\"] = decorations.join(\" \");\n  }\n  return styles;\n}\nfunction stringifyTokenStyle(token) {\n  if (typeof token === \"string\")\n    return token;\n  return Object.entries(token).map(([key, value]) => `${key}:${value}`).join(\";\");\n}\n\nconst _grammarStateMap = /* @__PURE__ */ new WeakMap();\nfunction setLastGrammarStateToMap(keys, state) {\n  _grammarStateMap.set(keys, state);\n}\nfunction getLastGrammarStateFromMap(keys) {\n  return _grammarStateMap.get(keys);\n}\nclass GrammarState {\n  /**\n   * Theme to Stack mapping\n   */\n  _stacks = {};\n  lang;\n  get themes() {\n    return Object.keys(this._stacks);\n  }\n  get theme() {\n    return this.themes[0];\n  }\n  get _stack() {\n    return this._stacks[this.theme];\n  }\n  /**\n   * Static method to create a initial grammar state.\n   */\n  static initial(lang, themes) {\n    return new GrammarState(\n      Object.fromEntries(toArray(themes).map((theme) => [theme, INITIAL])),\n      lang\n    );\n  }\n  constructor(...args) {\n    if (args.length === 2) {\n      const [stacksMap, lang] = args;\n      this.lang = lang;\n      this._stacks = stacksMap;\n    } else {\n      const [stack, lang, theme] = args;\n      this.lang = lang;\n      this._stacks = { [theme]: stack };\n    }\n  }\n  /**\n   * Get the internal stack object.\n   * @internal\n   */\n  getInternalStack(theme = this.theme) {\n    return this._stacks[theme];\n  }\n  getScopes(theme = this.theme) {\n    return getScopes(this._stacks[theme]);\n  }\n  toJSON() {\n    return {\n      lang: this.lang,\n      theme: this.theme,\n      themes: this.themes,\n      scopes: this.getScopes()\n    };\n  }\n}\nfunction getScopes(stack) {\n  const scopes = [];\n  const visited = /* @__PURE__ */ new Set();\n  function pushScope(stack2) {\n    if (visited.has(stack2))\n      return;\n    visited.add(stack2);\n    const name = stack2?.nameScopesList?.scopeName;\n    if (name)\n      scopes.push(name);\n    if (stack2.parent)\n      pushScope(stack2.parent);\n  }\n  pushScope(stack);\n  return scopes;\n}\nfunction getGrammarStack(state, theme) {\n  if (!(state instanceof GrammarState))\n    throw new ShikiError$1(\"Invalid grammar state\");\n  return state.getInternalStack(theme);\n}\n\nfunction transformerDecorations() {\n  const map = /* @__PURE__ */ new WeakMap();\n  function getContext(shiki) {\n    if (!map.has(shiki.meta)) {\n      let normalizePosition = function(p) {\n        if (typeof p === \"number\") {\n          if (p < 0 || p > shiki.source.length)\n            throw new ShikiError$1(`Invalid decoration offset: ${p}. Code length: ${shiki.source.length}`);\n          return {\n            ...converter.indexToPos(p),\n            offset: p\n          };\n        } else {\n          const line = converter.lines[p.line];\n          if (line === void 0)\n            throw new ShikiError$1(`Invalid decoration position ${JSON.stringify(p)}. Lines length: ${converter.lines.length}`);\n          if (p.character < 0 || p.character > line.length)\n            throw new ShikiError$1(`Invalid decoration position ${JSON.stringify(p)}. Line ${p.line} length: ${line.length}`);\n          return {\n            ...p,\n            offset: converter.posToIndex(p.line, p.character)\n          };\n        }\n      };\n      const converter = createPositionConverter(shiki.source);\n      const decorations = (shiki.options.decorations || []).map((d) => ({\n        ...d,\n        start: normalizePosition(d.start),\n        end: normalizePosition(d.end)\n      }));\n      verifyIntersections(decorations);\n      map.set(shiki.meta, {\n        decorations,\n        converter,\n        source: shiki.source\n      });\n    }\n    return map.get(shiki.meta);\n  }\n  return {\n    name: \"shiki:decorations\",\n    tokens(tokens) {\n      if (!this.options.decorations?.length)\n        return;\n      const ctx = getContext(this);\n      const breakpoints = ctx.decorations.flatMap((d) => [d.start.offset, d.end.offset]);\n      const splitted = splitTokens(tokens, breakpoints);\n      return splitted;\n    },\n    code(codeEl) {\n      if (!this.options.decorations?.length)\n        return;\n      const ctx = getContext(this);\n      const lines = Array.from(codeEl.children).filter((i) => i.type === \"element\" && i.tagName === \"span\");\n      if (lines.length !== ctx.converter.lines.length)\n        throw new ShikiError$1(`Number of lines in code element (${lines.length}) does not match the number of lines in the source (${ctx.converter.lines.length}). Failed to apply decorations.`);\n      function applyLineSection(line, start, end, decoration) {\n        const lineEl = lines[line];\n        let text = \"\";\n        let startIndex = -1;\n        let endIndex = -1;\n        if (start === 0)\n          startIndex = 0;\n        if (end === 0)\n          endIndex = 0;\n        if (end === Number.POSITIVE_INFINITY)\n          endIndex = lineEl.children.length;\n        if (startIndex === -1 || endIndex === -1) {\n          for (let i = 0; i < lineEl.children.length; i++) {\n            text += stringify(lineEl.children[i]);\n            if (startIndex === -1 && text.length === start)\n              startIndex = i + 1;\n            if (endIndex === -1 && text.length === end)\n              endIndex = i + 1;\n          }\n        }\n        if (startIndex === -1)\n          throw new ShikiError$1(`Failed to find start index for decoration ${JSON.stringify(decoration.start)}`);\n        if (endIndex === -1)\n          throw new ShikiError$1(`Failed to find end index for decoration ${JSON.stringify(decoration.end)}`);\n        const children = lineEl.children.slice(startIndex, endIndex);\n        if (!decoration.alwaysWrap && children.length === lineEl.children.length) {\n          applyDecoration(lineEl, decoration, \"line\");\n        } else if (!decoration.alwaysWrap && children.length === 1 && children[0].type === \"element\") {\n          applyDecoration(children[0], decoration, \"token\");\n        } else {\n          const wrapper = {\n            type: \"element\",\n            tagName: \"span\",\n            properties: {},\n            children\n          };\n          applyDecoration(wrapper, decoration, \"wrapper\");\n          lineEl.children.splice(startIndex, children.length, wrapper);\n        }\n      }\n      function applyLine(line, decoration) {\n        lines[line] = applyDecoration(lines[line], decoration, \"line\");\n      }\n      function applyDecoration(el, decoration, type) {\n        const properties = decoration.properties || {};\n        const transform = decoration.transform || ((i) => i);\n        el.tagName = decoration.tagName || \"span\";\n        el.properties = {\n          ...el.properties,\n          ...properties,\n          class: el.properties.class\n        };\n        if (decoration.properties?.class)\n          addClassToHast(el, decoration.properties.class);\n        el = transform(el, type) || el;\n        return el;\n      }\n      const lineApplies = [];\n      const sorted = ctx.decorations.sort((a, b) => b.start.offset - a.start.offset || a.end.offset - b.end.offset);\n      for (const decoration of sorted) {\n        const { start, end } = decoration;\n        if (start.line === end.line) {\n          applyLineSection(start.line, start.character, end.character, decoration);\n        } else if (start.line < end.line) {\n          applyLineSection(start.line, start.character, Number.POSITIVE_INFINITY, decoration);\n          for (let i = start.line + 1; i < end.line; i++)\n            lineApplies.unshift(() => applyLine(i, decoration));\n          applyLineSection(end.line, 0, end.character, decoration);\n        }\n      }\n      lineApplies.forEach((i) => i());\n    }\n  };\n}\nfunction verifyIntersections(items) {\n  for (let i = 0; i < items.length; i++) {\n    const foo = items[i];\n    if (foo.start.offset > foo.end.offset)\n      throw new ShikiError$1(`Invalid decoration range: ${JSON.stringify(foo.start)} - ${JSON.stringify(foo.end)}`);\n    for (let j = i + 1; j < items.length; j++) {\n      const bar = items[j];\n      const isFooHasBarStart = foo.start.offset <= bar.start.offset && bar.start.offset < foo.end.offset;\n      const isFooHasBarEnd = foo.start.offset < bar.end.offset && bar.end.offset <= foo.end.offset;\n      const isBarHasFooStart = bar.start.offset <= foo.start.offset && foo.start.offset < bar.end.offset;\n      const isBarHasFooEnd = bar.start.offset < foo.end.offset && foo.end.offset <= bar.end.offset;\n      if (isFooHasBarStart || isFooHasBarEnd || isBarHasFooStart || isBarHasFooEnd) {\n        if (isFooHasBarStart && isFooHasBarEnd)\n          continue;\n        if (isBarHasFooStart && isBarHasFooEnd)\n          continue;\n        throw new ShikiError$1(`Decorations ${JSON.stringify(foo.start)} and ${JSON.stringify(bar.start)} intersect.`);\n      }\n    }\n  }\n}\nfunction stringify(el) {\n  if (el.type === \"text\")\n    return el.value;\n  if (el.type === \"element\")\n    return el.children.map(stringify).join(\"\");\n  return \"\";\n}\n\nconst builtInTransformers = [\n  /* @__PURE__ */ transformerDecorations()\n];\nfunction getTransformers(options) {\n  return [\n    ...options.transformers || [],\n    ...builtInTransformers\n  ];\n}\n\n// src/colors.ts\nvar namedColors = [\n  \"black\",\n  \"red\",\n  \"green\",\n  \"yellow\",\n  \"blue\",\n  \"magenta\",\n  \"cyan\",\n  \"white\",\n  \"brightBlack\",\n  \"brightRed\",\n  \"brightGreen\",\n  \"brightYellow\",\n  \"brightBlue\",\n  \"brightMagenta\",\n  \"brightCyan\",\n  \"brightWhite\"\n];\n\n// src/decorations.ts\nvar decorations = {\n  1: \"bold\",\n  2: \"dim\",\n  3: \"italic\",\n  4: \"underline\",\n  7: \"reverse\",\n  8: \"hidden\",\n  9: \"strikethrough\"\n};\n\n// src/parser.ts\nfunction findSequence(value, position) {\n  const nextEscape = value.indexOf(\"\\x1B\", position);\n  if (nextEscape !== -1) {\n    if (value[nextEscape + 1] === \"[\") {\n      const nextClose = value.indexOf(\"m\", nextEscape);\n      if (nextClose !== -1) {\n        return {\n          sequence: value.substring(nextEscape + 2, nextClose).split(\";\"),\n          startPosition: nextEscape,\n          position: nextClose + 1\n        };\n      }\n    }\n  }\n  return {\n    position: value.length\n  };\n}\nfunction parseColor(sequence) {\n  const colorMode = sequence.shift();\n  if (colorMode === \"2\") {\n    const rgb = sequence.splice(0, 3).map((x) => Number.parseInt(x));\n    if (rgb.length !== 3 || rgb.some((x) => Number.isNaN(x)))\n      return;\n    return {\n      type: \"rgb\",\n      rgb\n    };\n  } else if (colorMode === \"5\") {\n    const index = sequence.shift();\n    if (index) {\n      return { type: \"table\", index: Number(index) };\n    }\n  }\n}\nfunction parseSequence(sequence) {\n  const commands = [];\n  while (sequence.length > 0) {\n    const code = sequence.shift();\n    if (!code)\n      continue;\n    const codeInt = Number.parseInt(code);\n    if (Number.isNaN(codeInt))\n      continue;\n    if (codeInt === 0) {\n      commands.push({ type: \"resetAll\" });\n    } else if (codeInt <= 9) {\n      const decoration = decorations[codeInt];\n      if (decoration) {\n        commands.push({\n          type: \"setDecoration\",\n          value: decorations[codeInt]\n        });\n      }\n    } else if (codeInt <= 29) {\n      const decoration = decorations[codeInt - 20];\n      if (decoration) {\n        commands.push({\n          type: \"resetDecoration\",\n          value: decoration\n        });\n        if (decoration === \"dim\") {\n          commands.push({\n            type: \"resetDecoration\",\n            value: \"bold\"\n          });\n        }\n      }\n    } else if (codeInt <= 37) {\n      commands.push({\n        type: \"setForegroundColor\",\n        value: { type: \"named\", name: namedColors[codeInt - 30] }\n      });\n    } else if (codeInt === 38) {\n      const color = parseColor(sequence);\n      if (color) {\n        commands.push({\n          type: \"setForegroundColor\",\n          value: color\n        });\n      }\n    } else if (codeInt === 39) {\n      commands.push({\n        type: \"resetForegroundColor\"\n      });\n    } else if (codeInt <= 47) {\n      commands.push({\n        type: \"setBackgroundColor\",\n        value: { type: \"named\", name: namedColors[codeInt - 40] }\n      });\n    } else if (codeInt === 48) {\n      const color = parseColor(sequence);\n      if (color) {\n        commands.push({\n          type: \"setBackgroundColor\",\n          value: color\n        });\n      }\n    } else if (codeInt === 49) {\n      commands.push({\n        type: \"resetBackgroundColor\"\n      });\n    } else if (codeInt === 53) {\n      commands.push({\n        type: \"setDecoration\",\n        value: \"overline\"\n      });\n    } else if (codeInt === 55) {\n      commands.push({\n        type: \"resetDecoration\",\n        value: \"overline\"\n      });\n    } else if (codeInt >= 90 && codeInt <= 97) {\n      commands.push({\n        type: \"setForegroundColor\",\n        value: { type: \"named\", name: namedColors[codeInt - 90 + 8] }\n      });\n    } else if (codeInt >= 100 && codeInt <= 107) {\n      commands.push({\n        type: \"setBackgroundColor\",\n        value: { type: \"named\", name: namedColors[codeInt - 100 + 8] }\n      });\n    }\n  }\n  return commands;\n}\nfunction createAnsiSequenceParser() {\n  let foreground = null;\n  let background = null;\n  let decorations2 = /* @__PURE__ */ new Set();\n  return {\n    parse(value) {\n      const tokens = [];\n      let position = 0;\n      do {\n        const findResult = findSequence(value, position);\n        const text = findResult.sequence ? value.substring(position, findResult.startPosition) : value.substring(position);\n        if (text.length > 0) {\n          tokens.push({\n            value: text,\n            foreground,\n            background,\n            decorations: new Set(decorations2)\n          });\n        }\n        if (findResult.sequence) {\n          const commands = parseSequence(findResult.sequence);\n          for (const styleToken of commands) {\n            if (styleToken.type === \"resetAll\") {\n              foreground = null;\n              background = null;\n              decorations2.clear();\n            } else if (styleToken.type === \"resetForegroundColor\") {\n              foreground = null;\n            } else if (styleToken.type === \"resetBackgroundColor\") {\n              background = null;\n            } else if (styleToken.type === \"resetDecoration\") {\n              decorations2.delete(styleToken.value);\n            }\n          }\n          for (const styleToken of commands) {\n            if (styleToken.type === \"setForegroundColor\") {\n              foreground = styleToken.value;\n            } else if (styleToken.type === \"setBackgroundColor\") {\n              background = styleToken.value;\n            } else if (styleToken.type === \"setDecoration\") {\n              decorations2.add(styleToken.value);\n            }\n          }\n        }\n        position = findResult.position;\n      } while (position < value.length);\n      return tokens;\n    }\n  };\n}\n\n// src/palette.ts\nvar defaultNamedColorsMap = {\n  black: \"#000000\",\n  red: \"#bb0000\",\n  green: \"#00bb00\",\n  yellow: \"#bbbb00\",\n  blue: \"#0000bb\",\n  magenta: \"#ff00ff\",\n  cyan: \"#00bbbb\",\n  white: \"#eeeeee\",\n  brightBlack: \"#555555\",\n  brightRed: \"#ff5555\",\n  brightGreen: \"#00ff00\",\n  brightYellow: \"#ffff55\",\n  brightBlue: \"#5555ff\",\n  brightMagenta: \"#ff55ff\",\n  brightCyan: \"#55ffff\",\n  brightWhite: \"#ffffff\"\n};\nfunction createColorPalette(namedColorsMap = defaultNamedColorsMap) {\n  function namedColor(name) {\n    return namedColorsMap[name];\n  }\n  function rgbColor(rgb) {\n    return `#${rgb.map((x) => Math.max(0, Math.min(x, 255)).toString(16).padStart(2, \"0\")).join(\"\")}`;\n  }\n  let colorTable;\n  function getColorTable() {\n    if (colorTable) {\n      return colorTable;\n    }\n    colorTable = [];\n    for (let i = 0; i < namedColors.length; i++) {\n      colorTable.push(namedColor(namedColors[i]));\n    }\n    let levels = [0, 95, 135, 175, 215, 255];\n    for (let r = 0; r < 6; r++) {\n      for (let g = 0; g < 6; g++) {\n        for (let b = 0; b < 6; b++) {\n          colorTable.push(rgbColor([levels[r], levels[g], levels[b]]));\n        }\n      }\n    }\n    let level = 8;\n    for (let i = 0; i < 24; i++, level += 10) {\n      colorTable.push(rgbColor([level, level, level]));\n    }\n    return colorTable;\n  }\n  function tableColor(index) {\n    return getColorTable()[index];\n  }\n  function value(color) {\n    switch (color.type) {\n      case \"named\":\n        return namedColor(color.name);\n      case \"rgb\":\n        return rgbColor(color.rgb);\n      case \"table\":\n        return tableColor(color.index);\n    }\n  }\n  return {\n    value\n  };\n}\n\nfunction tokenizeAnsiWithTheme(theme, fileContents, options) {\n  const colorReplacements = resolveColorReplacements(theme, options);\n  const lines = splitLines(fileContents);\n  const colorPalette = createColorPalette(\n    Object.fromEntries(\n      namedColors.map((name) => [\n        name,\n        theme.colors?.[`terminal.ansi${name[0].toUpperCase()}${name.substring(1)}`]\n      ])\n    )\n  );\n  const parser = createAnsiSequenceParser();\n  return lines.map(\n    (line) => parser.parse(line[0]).map((token) => {\n      let color;\n      let bgColor;\n      if (token.decorations.has(\"reverse\")) {\n        color = token.background ? colorPalette.value(token.background) : theme.bg;\n        bgColor = token.foreground ? colorPalette.value(token.foreground) : theme.fg;\n      } else {\n        color = token.foreground ? colorPalette.value(token.foreground) : theme.fg;\n        bgColor = token.background ? colorPalette.value(token.background) : void 0;\n      }\n      color = applyColorReplacements(color, colorReplacements);\n      bgColor = applyColorReplacements(bgColor, colorReplacements);\n      if (token.decorations.has(\"dim\"))\n        color = dimColor(color);\n      let fontStyle = FontStyle.None;\n      if (token.decorations.has(\"bold\"))\n        fontStyle |= FontStyle.Bold;\n      if (token.decorations.has(\"italic\"))\n        fontStyle |= FontStyle.Italic;\n      if (token.decorations.has(\"underline\"))\n        fontStyle |= FontStyle.Underline;\n      if (token.decorations.has(\"strikethrough\"))\n        fontStyle |= FontStyle.Strikethrough;\n      return {\n        content: token.value,\n        offset: line[1],\n        // TODO: more accurate offset? might need to fork ansi-sequence-parser\n        color,\n        bgColor,\n        fontStyle\n      };\n    })\n  );\n}\nfunction dimColor(color) {\n  const hexMatch = color.match(/#([0-9a-f]{3})([0-9a-f]{3})?([0-9a-f]{2})?/);\n  if (hexMatch) {\n    if (hexMatch[3]) {\n      const alpha = Math.round(Number.parseInt(hexMatch[3], 16) / 2).toString(16).padStart(2, \"0\");\n      return `#${hexMatch[1]}${hexMatch[2]}${alpha}`;\n    } else if (hexMatch[2]) {\n      return `#${hexMatch[1]}${hexMatch[2]}80`;\n    } else {\n      return `#${Array.from(hexMatch[1]).map((x) => `${x}${x}`).join(\"\")}80`;\n    }\n  }\n  const cssVarMatch = color.match(/var\\((--[\\w-]+-ansi-[\\w-]+)\\)/);\n  if (cssVarMatch)\n    return `var(${cssVarMatch[1]}-dim)`;\n  return color;\n}\n\nfunction codeToTokensBase(internal, code, options = {}) {\n  const {\n    lang = \"text\",\n    theme: themeName = internal.getLoadedThemes()[0]\n  } = options;\n  if (isPlainLang(lang) || isNoneTheme(themeName))\n    return splitLines(code).map((line) => [{ content: line[0], offset: line[1] }]);\n  const { theme, colorMap } = internal.setTheme(themeName);\n  if (lang === \"ansi\")\n    return tokenizeAnsiWithTheme(theme, code, options);\n  const _grammar = internal.getLanguage(lang);\n  if (options.grammarState) {\n    if (options.grammarState.lang !== _grammar.name) {\n      throw new ShikiError$1(`Grammar state language \"${options.grammarState.lang}\" does not match highlight language \"${_grammar.name}\"`);\n    }\n    if (!options.grammarState.themes.includes(theme.name)) {\n      throw new ShikiError$1(`Grammar state themes \"${options.grammarState.themes}\" do not contain highlight theme \"${theme.name}\"`);\n    }\n  }\n  return tokenizeWithTheme(code, _grammar, theme, colorMap, options);\n}\nfunction getLastGrammarState(...args) {\n  if (args.length === 2) {\n    return getLastGrammarStateFromMap(args[1]);\n  }\n  const [internal, code, options = {}] = args;\n  const {\n    lang = \"text\",\n    theme: themeName = internal.getLoadedThemes()[0]\n  } = options;\n  if (isPlainLang(lang) || isNoneTheme(themeName))\n    throw new ShikiError$1(\"Plain language does not have grammar state\");\n  if (lang === \"ansi\")\n    throw new ShikiError$1(\"ANSI language does not have grammar state\");\n  const { theme, colorMap } = internal.setTheme(themeName);\n  const _grammar = internal.getLanguage(lang);\n  return new GrammarState(\n    _tokenizeWithTheme(code, _grammar, theme, colorMap, options).stateStack,\n    _grammar.name,\n    theme.name\n  );\n}\nfunction tokenizeWithTheme(code, grammar, theme, colorMap, options) {\n  const result = _tokenizeWithTheme(code, grammar, theme, colorMap, options);\n  const grammarState = new GrammarState(\n    _tokenizeWithTheme(code, grammar, theme, colorMap, options).stateStack,\n    grammar.name,\n    theme.name\n  );\n  setLastGrammarStateToMap(result.tokens, grammarState);\n  return result.tokens;\n}\nfunction _tokenizeWithTheme(code, grammar, theme, colorMap, options) {\n  const colorReplacements = resolveColorReplacements(theme, options);\n  const {\n    tokenizeMaxLineLength = 0,\n    tokenizeTimeLimit = 500\n  } = options;\n  const lines = splitLines(code);\n  let stateStack = options.grammarState ? getGrammarStack(options.grammarState, theme.name) ?? INITIAL : options.grammarContextCode != null ? _tokenizeWithTheme(\n    options.grammarContextCode,\n    grammar,\n    theme,\n    colorMap,\n    {\n      ...options,\n      grammarState: void 0,\n      grammarContextCode: void 0\n    }\n  ).stateStack : INITIAL;\n  let actual = [];\n  const final = [];\n  for (let i = 0, len = lines.length; i < len; i++) {\n    const [line, lineOffset] = lines[i];\n    if (line === \"\") {\n      actual = [];\n      final.push([]);\n      continue;\n    }\n    if (tokenizeMaxLineLength > 0 && line.length >= tokenizeMaxLineLength) {\n      actual = [];\n      final.push([{\n        content: line,\n        offset: lineOffset,\n        color: \"\",\n        fontStyle: 0\n      }]);\n      continue;\n    }\n    let resultWithScopes;\n    let tokensWithScopes;\n    let tokensWithScopesIndex;\n    if (options.includeExplanation) {\n      resultWithScopes = grammar.tokenizeLine(line, stateStack, tokenizeTimeLimit);\n      tokensWithScopes = resultWithScopes.tokens;\n      tokensWithScopesIndex = 0;\n    }\n    const result = grammar.tokenizeLine2(line, stateStack, tokenizeTimeLimit);\n    const tokensLength = result.tokens.length / 2;\n    for (let j = 0; j < tokensLength; j++) {\n      const startIndex = result.tokens[2 * j];\n      const nextStartIndex = j + 1 < tokensLength ? result.tokens[2 * j + 2] : line.length;\n      if (startIndex === nextStartIndex)\n        continue;\n      const metadata = result.tokens[2 * j + 1];\n      const color = applyColorReplacements(\n        colorMap[EncodedTokenMetadata.getForeground(metadata)],\n        colorReplacements\n      );\n      const fontStyle = EncodedTokenMetadata.getFontStyle(metadata);\n      const token = {\n        content: line.substring(startIndex, nextStartIndex),\n        offset: lineOffset + startIndex,\n        color,\n        fontStyle\n      };\n      if (options.includeExplanation) {\n        const themeSettingsSelectors = [];\n        if (options.includeExplanation !== \"scopeName\") {\n          for (const setting of theme.settings) {\n            let selectors;\n            switch (typeof setting.scope) {\n              case \"string\":\n                selectors = setting.scope.split(/,/).map((scope) => scope.trim());\n                break;\n              case \"object\":\n                selectors = setting.scope;\n                break;\n              default:\n                continue;\n            }\n            themeSettingsSelectors.push({\n              settings: setting,\n              selectors: selectors.map((selector) => selector.split(/ /))\n            });\n          }\n        }\n        token.explanation = [];\n        let offset = 0;\n        while (startIndex + offset < nextStartIndex) {\n          const tokenWithScopes = tokensWithScopes[tokensWithScopesIndex];\n          const tokenWithScopesText = line.substring(\n            tokenWithScopes.startIndex,\n            tokenWithScopes.endIndex\n          );\n          offset += tokenWithScopesText.length;\n          token.explanation.push({\n            content: tokenWithScopesText,\n            scopes: options.includeExplanation === \"scopeName\" ? explainThemeScopesNameOnly(\n              tokenWithScopes.scopes\n            ) : explainThemeScopesFull(\n              themeSettingsSelectors,\n              tokenWithScopes.scopes\n            )\n          });\n          tokensWithScopesIndex += 1;\n        }\n      }\n      actual.push(token);\n    }\n    final.push(actual);\n    actual = [];\n    stateStack = result.ruleStack;\n  }\n  return {\n    tokens: final,\n    stateStack\n  };\n}\nfunction explainThemeScopesNameOnly(scopes) {\n  return scopes.map((scope) => ({ scopeName: scope }));\n}\nfunction explainThemeScopesFull(themeSelectors, scopes) {\n  const result = [];\n  for (let i = 0, len = scopes.length; i < len; i++) {\n    const scope = scopes[i];\n    result[i] = {\n      scopeName: scope,\n      themeMatches: explainThemeScope(themeSelectors, scope, scopes.slice(0, i))\n    };\n  }\n  return result;\n}\nfunction matchesOne(selector, scope) {\n  return selector === scope || scope.substring(0, selector.length) === selector && scope[selector.length] === \".\";\n}\nfunction matches(selectors, scope, parentScopes) {\n  if (!matchesOne(selectors[selectors.length - 1], scope))\n    return false;\n  let selectorParentIndex = selectors.length - 2;\n  let parentIndex = parentScopes.length - 1;\n  while (selectorParentIndex >= 0 && parentIndex >= 0) {\n    if (matchesOne(selectors[selectorParentIndex], parentScopes[parentIndex]))\n      selectorParentIndex -= 1;\n    parentIndex -= 1;\n  }\n  if (selectorParentIndex === -1)\n    return true;\n  return false;\n}\nfunction explainThemeScope(themeSettingsSelectors, scope, parentScopes) {\n  const result = [];\n  for (const { selectors, settings } of themeSettingsSelectors) {\n    for (const selectorPieces of selectors) {\n      if (matches(selectorPieces, scope, parentScopes)) {\n        result.push(settings);\n        break;\n      }\n    }\n  }\n  return result;\n}\n\nfunction codeToTokensWithThemes(internal, code, options) {\n  const themes = Object.entries(options.themes).filter((i) => i[1]).map((i) => ({ color: i[0], theme: i[1] }));\n  const themedTokens = themes.map((t) => {\n    const tokens2 = codeToTokensBase(internal, code, {\n      ...options,\n      theme: t.theme\n    });\n    const state = getLastGrammarStateFromMap(tokens2);\n    const theme = typeof t.theme === \"string\" ? t.theme : t.theme.name;\n    return {\n      tokens: tokens2,\n      state,\n      theme\n    };\n  });\n  const tokens = syncThemesTokenization(\n    ...themedTokens.map((i) => i.tokens)\n  );\n  const mergedTokens = tokens[0].map(\n    (line, lineIdx) => line.map((_token, tokenIdx) => {\n      const mergedToken = {\n        content: _token.content,\n        variants: {},\n        offset: _token.offset\n      };\n      if (\"includeExplanation\" in options && options.includeExplanation) {\n        mergedToken.explanation = _token.explanation;\n      }\n      tokens.forEach((t, themeIdx) => {\n        const {\n          content: _,\n          explanation: __,\n          offset: ___,\n          ...styles\n        } = t[lineIdx][tokenIdx];\n        mergedToken.variants[themes[themeIdx].color] = styles;\n      });\n      return mergedToken;\n    })\n  );\n  const mergedGrammarState = themedTokens[0].state ? new GrammarState(\n    Object.fromEntries(themedTokens.map((s) => [s.theme, s.state?.getInternalStack(s.theme)])),\n    themedTokens[0].state.lang\n  ) : void 0;\n  if (mergedGrammarState)\n    setLastGrammarStateToMap(mergedTokens, mergedGrammarState);\n  return mergedTokens;\n}\nfunction syncThemesTokenization(...themes) {\n  const outThemes = themes.map(() => []);\n  const count = themes.length;\n  for (let i = 0; i < themes[0].length; i++) {\n    const lines = themes.map((t) => t[i]);\n    const outLines = outThemes.map(() => []);\n    outThemes.forEach((t, i2) => t.push(outLines[i2]));\n    const indexes = lines.map(() => 0);\n    const current = lines.map((l) => l[0]);\n    while (current.every((t) => t)) {\n      const minLength = Math.min(...current.map((t) => t.content.length));\n      for (let n = 0; n < count; n++) {\n        const token = current[n];\n        if (token.content.length === minLength) {\n          outLines[n].push(token);\n          indexes[n] += 1;\n          current[n] = lines[n][indexes[n]];\n        } else {\n          outLines[n].push({\n            ...token,\n            content: token.content.slice(0, minLength)\n          });\n          current[n] = {\n            ...token,\n            content: token.content.slice(minLength),\n            offset: token.offset + minLength\n          };\n        }\n      }\n    }\n  }\n  return outThemes;\n}\n\nfunction codeToTokens(internal, code, options) {\n  let bg;\n  let fg;\n  let tokens;\n  let themeName;\n  let rootStyle;\n  let grammarState;\n  if (\"themes\" in options) {\n    const {\n      defaultColor = \"light\",\n      cssVariablePrefix = \"--shiki-\"\n    } = options;\n    const themes = Object.entries(options.themes).filter((i) => i[1]).map((i) => ({ color: i[0], theme: i[1] })).sort((a, b) => a.color === defaultColor ? -1 : b.color === defaultColor ? 1 : 0);\n    if (themes.length === 0)\n      throw new ShikiError$1(\"`themes` option must not be empty\");\n    const themeTokens = codeToTokensWithThemes(\n      internal,\n      code,\n      options\n    );\n    grammarState = getLastGrammarStateFromMap(themeTokens);\n    if (defaultColor && !themes.find((t) => t.color === defaultColor))\n      throw new ShikiError$1(`\\`themes\\` option must contain the defaultColor key \\`${defaultColor}\\``);\n    const themeRegs = themes.map((t) => internal.getTheme(t.theme));\n    const themesOrder = themes.map((t) => t.color);\n    tokens = themeTokens.map((line) => line.map((token) => flatTokenVariants(token, themesOrder, cssVariablePrefix, defaultColor)));\n    if (grammarState)\n      setLastGrammarStateToMap(tokens, grammarState);\n    const themeColorReplacements = themes.map((t) => resolveColorReplacements(t.theme, options));\n    fg = themes.map((t, idx) => (idx === 0 && defaultColor ? \"\" : `${cssVariablePrefix + t.color}:`) + (applyColorReplacements(themeRegs[idx].fg, themeColorReplacements[idx]) || \"inherit\")).join(\";\");\n    bg = themes.map((t, idx) => (idx === 0 && defaultColor ? \"\" : `${cssVariablePrefix + t.color}-bg:`) + (applyColorReplacements(themeRegs[idx].bg, themeColorReplacements[idx]) || \"inherit\")).join(\";\");\n    themeName = `shiki-themes ${themeRegs.map((t) => t.name).join(\" \")}`;\n    rootStyle = defaultColor ? void 0 : [fg, bg].join(\";\");\n  } else if (\"theme\" in options) {\n    const colorReplacements = resolveColorReplacements(options.theme, options);\n    tokens = codeToTokensBase(\n      internal,\n      code,\n      options\n    );\n    const _theme = internal.getTheme(options.theme);\n    bg = applyColorReplacements(_theme.bg, colorReplacements);\n    fg = applyColorReplacements(_theme.fg, colorReplacements);\n    themeName = _theme.name;\n    grammarState = getLastGrammarStateFromMap(tokens);\n  } else {\n    throw new ShikiError$1(\"Invalid options, either `theme` or `themes` must be provided\");\n  }\n  return {\n    tokens,\n    fg,\n    bg,\n    themeName,\n    rootStyle,\n    grammarState\n  };\n}\n\nfunction codeToHast(internal, code, options, transformerContext = {\n  meta: {},\n  options,\n  codeToHast: (_code, _options) => codeToHast(internal, _code, _options),\n  codeToTokens: (_code, _options) => codeToTokens(internal, _code, _options)\n}) {\n  let input = code;\n  for (const transformer of getTransformers(options))\n    input = transformer.preprocess?.call(transformerContext, input, options) || input;\n  let {\n    tokens,\n    fg,\n    bg,\n    themeName,\n    rootStyle,\n    grammarState\n  } = codeToTokens(internal, input, options);\n  const {\n    mergeWhitespaces = true,\n    mergeSameStyleTokens = false\n  } = options;\n  if (mergeWhitespaces === true)\n    tokens = mergeWhitespaceTokens(tokens);\n  else if (mergeWhitespaces === \"never\")\n    tokens = splitWhitespaceTokens(tokens);\n  if (mergeSameStyleTokens) {\n    tokens = mergeAdjacentStyledTokens(tokens);\n  }\n  const contextSource = {\n    ...transformerContext,\n    get source() {\n      return input;\n    }\n  };\n  for (const transformer of getTransformers(options))\n    tokens = transformer.tokens?.call(contextSource, tokens) || tokens;\n  return tokensToHast(\n    tokens,\n    {\n      ...options,\n      fg,\n      bg,\n      themeName,\n      rootStyle\n    },\n    contextSource,\n    grammarState\n  );\n}\nfunction tokensToHast(tokens, options, transformerContext, grammarState = getLastGrammarStateFromMap(tokens)) {\n  const transformers = getTransformers(options);\n  const lines = [];\n  const root = {\n    type: \"root\",\n    children: []\n  };\n  const {\n    structure = \"classic\",\n    tabindex = \"0\"\n  } = options;\n  let preNode = {\n    type: \"element\",\n    tagName: \"pre\",\n    properties: {\n      class: `shiki ${options.themeName || \"\"}`,\n      style: options.rootStyle || `background-color:${options.bg};color:${options.fg}`,\n      ...tabindex !== false && tabindex != null ? {\n        tabindex: tabindex.toString()\n      } : {},\n      ...Object.fromEntries(\n        Array.from(\n          Object.entries(options.meta || {})\n        ).filter(([key]) => !key.startsWith(\"_\"))\n      )\n    },\n    children: []\n  };\n  let codeNode = {\n    type: \"element\",\n    tagName: \"code\",\n    properties: {},\n    children: lines\n  };\n  const lineNodes = [];\n  const context = {\n    ...transformerContext,\n    structure,\n    addClassToHast,\n    get source() {\n      return transformerContext.source;\n    },\n    get tokens() {\n      return tokens;\n    },\n    get options() {\n      return options;\n    },\n    get root() {\n      return root;\n    },\n    get pre() {\n      return preNode;\n    },\n    get code() {\n      return codeNode;\n    },\n    get lines() {\n      return lineNodes;\n    }\n  };\n  tokens.forEach((line, idx) => {\n    if (idx) {\n      if (structure === \"inline\")\n        root.children.push({ type: \"element\", tagName: \"br\", properties: {}, children: [] });\n      else if (structure === \"classic\")\n        lines.push({ type: \"text\", value: \"\\n\" });\n    }\n    let lineNode = {\n      type: \"element\",\n      tagName: \"span\",\n      properties: { class: \"line\" },\n      children: []\n    };\n    let col = 0;\n    for (const token of line) {\n      let tokenNode = {\n        type: \"element\",\n        tagName: \"span\",\n        properties: {\n          ...token.htmlAttrs\n        },\n        children: [{ type: \"text\", value: token.content }]\n      };\n      const style = stringifyTokenStyle(token.htmlStyle || getTokenStyleObject(token));\n      if (style)\n        tokenNode.properties.style = style;\n      for (const transformer of transformers)\n        tokenNode = transformer?.span?.call(context, tokenNode, idx + 1, col, lineNode, token) || tokenNode;\n      if (structure === \"inline\")\n        root.children.push(tokenNode);\n      else if (structure === \"classic\")\n        lineNode.children.push(tokenNode);\n      col += token.content.length;\n    }\n    if (structure === \"classic\") {\n      for (const transformer of transformers)\n        lineNode = transformer?.line?.call(context, lineNode, idx + 1) || lineNode;\n      lineNodes.push(lineNode);\n      lines.push(lineNode);\n    }\n  });\n  if (structure === \"classic\") {\n    for (const transformer of transformers)\n      codeNode = transformer?.code?.call(context, codeNode) || codeNode;\n    preNode.children.push(codeNode);\n    for (const transformer of transformers)\n      preNode = transformer?.pre?.call(context, preNode) || preNode;\n    root.children.push(preNode);\n  }\n  let result = root;\n  for (const transformer of transformers)\n    result = transformer?.root?.call(context, result) || result;\n  if (grammarState)\n    setLastGrammarStateToMap(result, grammarState);\n  return result;\n}\nfunction mergeWhitespaceTokens(tokens) {\n  return tokens.map((line) => {\n    const newLine = [];\n    let carryOnContent = \"\";\n    let firstOffset = 0;\n    line.forEach((token, idx) => {\n      const isDecorated = token.fontStyle && (token.fontStyle & FontStyle.Underline || token.fontStyle & FontStyle.Strikethrough);\n      const couldMerge = !isDecorated;\n      if (couldMerge && token.content.match(/^\\s+$/) && line[idx + 1]) {\n        if (!firstOffset)\n          firstOffset = token.offset;\n        carryOnContent += token.content;\n      } else {\n        if (carryOnContent) {\n          if (couldMerge) {\n            newLine.push({\n              ...token,\n              offset: firstOffset,\n              content: carryOnContent + token.content\n            });\n          } else {\n            newLine.push(\n              {\n                content: carryOnContent,\n                offset: firstOffset\n              },\n              token\n            );\n          }\n          firstOffset = 0;\n          carryOnContent = \"\";\n        } else {\n          newLine.push(token);\n        }\n      }\n    });\n    return newLine;\n  });\n}\nfunction splitWhitespaceTokens(tokens) {\n  return tokens.map((line) => {\n    return line.flatMap((token) => {\n      if (token.content.match(/^\\s+$/))\n        return token;\n      const match = token.content.match(/^(\\s*)(.*?)(\\s*)$/);\n      if (!match)\n        return token;\n      const [, leading, content, trailing] = match;\n      if (!leading && !trailing)\n        return token;\n      const expanded = [{\n        ...token,\n        offset: token.offset + leading.length,\n        content\n      }];\n      if (leading) {\n        expanded.unshift({\n          content: leading,\n          offset: token.offset\n        });\n      }\n      if (trailing) {\n        expanded.push({\n          content: trailing,\n          offset: token.offset + leading.length + content.length\n        });\n      }\n      return expanded;\n    });\n  });\n}\nfunction mergeAdjacentStyledTokens(tokens) {\n  return tokens.map((line) => {\n    const newLine = [];\n    for (const token of line) {\n      if (newLine.length === 0) {\n        newLine.push({ ...token });\n        continue;\n      }\n      const prevToken = newLine[newLine.length - 1];\n      const prevStyle = prevToken.htmlStyle || stringifyTokenStyle(getTokenStyleObject(prevToken));\n      const currentStyle = token.htmlStyle || stringifyTokenStyle(getTokenStyleObject(token));\n      const isPrevDecorated = prevToken.fontStyle && (prevToken.fontStyle & FontStyle.Underline || prevToken.fontStyle & FontStyle.Strikethrough);\n      const isDecorated = token.fontStyle && (token.fontStyle & FontStyle.Underline || token.fontStyle & FontStyle.Strikethrough);\n      if (!isPrevDecorated && !isDecorated && prevStyle === currentStyle) {\n        prevToken.content += token.content;\n      } else {\n        newLine.push({ ...token });\n      }\n    }\n    return newLine;\n  });\n}\n\nconst hastToHtml = toHtml;\nfunction codeToHtml(internal, code, options) {\n  const context = {\n    meta: {},\n    options,\n    codeToHast: (_code, _options) => codeToHast(internal, _code, _options),\n    codeToTokens: (_code, _options) => codeToTokens(internal, _code, _options)\n  };\n  let result = hastToHtml(codeToHast(internal, code, options, context));\n  for (const transformer of getTransformers(options))\n    result = transformer.postprocess?.call(context, result, options) || result;\n  return result;\n}\n\nconst VSCODE_FALLBACK_EDITOR_FG = { light: \"#333333\", dark: \"#bbbbbb\" };\nconst VSCODE_FALLBACK_EDITOR_BG = { light: \"#fffffe\", dark: \"#1e1e1e\" };\nconst RESOLVED_KEY = \"__shiki_resolved\";\nfunction normalizeTheme(rawTheme) {\n  if (rawTheme?.[RESOLVED_KEY])\n    return rawTheme;\n  const theme = {\n    ...rawTheme\n  };\n  if (theme.tokenColors && !theme.settings) {\n    theme.settings = theme.tokenColors;\n    delete theme.tokenColors;\n  }\n  theme.type ||= \"dark\";\n  theme.colorReplacements = { ...theme.colorReplacements };\n  theme.settings ||= [];\n  let { bg, fg } = theme;\n  if (!bg || !fg) {\n    const globalSetting = theme.settings ? theme.settings.find((s) => !s.name && !s.scope) : void 0;\n    if (globalSetting?.settings?.foreground)\n      fg = globalSetting.settings.foreground;\n    if (globalSetting?.settings?.background)\n      bg = globalSetting.settings.background;\n    if (!fg && theme?.colors?.[\"editor.foreground\"])\n      fg = theme.colors[\"editor.foreground\"];\n    if (!bg && theme?.colors?.[\"editor.background\"])\n      bg = theme.colors[\"editor.background\"];\n    if (!fg)\n      fg = theme.type === \"light\" ? VSCODE_FALLBACK_EDITOR_FG.light : VSCODE_FALLBACK_EDITOR_FG.dark;\n    if (!bg)\n      bg = theme.type === \"light\" ? VSCODE_FALLBACK_EDITOR_BG.light : VSCODE_FALLBACK_EDITOR_BG.dark;\n    theme.fg = fg;\n    theme.bg = bg;\n  }\n  if (!(theme.settings[0] && theme.settings[0].settings && !theme.settings[0].scope)) {\n    theme.settings.unshift({\n      settings: {\n        foreground: theme.fg,\n        background: theme.bg\n      }\n    });\n  }\n  let replacementCount = 0;\n  const replacementMap = /* @__PURE__ */ new Map();\n  function getReplacementColor(value) {\n    if (replacementMap.has(value))\n      return replacementMap.get(value);\n    replacementCount += 1;\n    const hex = `#${replacementCount.toString(16).padStart(8, \"0\").toLowerCase()}`;\n    if (theme.colorReplacements?.[`#${hex}`])\n      return getReplacementColor(value);\n    replacementMap.set(value, hex);\n    return hex;\n  }\n  theme.settings = theme.settings.map((setting) => {\n    const replaceFg = setting.settings?.foreground && !setting.settings.foreground.startsWith(\"#\");\n    const replaceBg = setting.settings?.background && !setting.settings.background.startsWith(\"#\");\n    if (!replaceFg && !replaceBg)\n      return setting;\n    const clone = {\n      ...setting,\n      settings: {\n        ...setting.settings\n      }\n    };\n    if (replaceFg) {\n      const replacement = getReplacementColor(setting.settings.foreground);\n      theme.colorReplacements[replacement] = setting.settings.foreground;\n      clone.settings.foreground = replacement;\n    }\n    if (replaceBg) {\n      const replacement = getReplacementColor(setting.settings.background);\n      theme.colorReplacements[replacement] = setting.settings.background;\n      clone.settings.background = replacement;\n    }\n    return clone;\n  });\n  for (const key of Object.keys(theme.colors || {})) {\n    if (key === \"editor.foreground\" || key === \"editor.background\" || key.startsWith(\"terminal.ansi\")) {\n      if (!theme.colors[key]?.startsWith(\"#\")) {\n        const replacement = getReplacementColor(theme.colors[key]);\n        theme.colorReplacements[replacement] = theme.colors[key];\n        theme.colors[key] = replacement;\n      }\n    }\n  }\n  Object.defineProperty(theme, RESOLVED_KEY, {\n    enumerable: false,\n    writable: false,\n    value: true\n  });\n  return theme;\n}\n\nasync function resolveLangs(langs) {\n  return Array.from(new Set((await Promise.all(\n    langs.filter((l) => !isSpecialLang(l)).map(async (lang) => await normalizeGetter(lang).then((r) => Array.isArray(r) ? r : [r]))\n  )).flat()));\n}\nasync function resolveThemes(themes) {\n  const resolved = await Promise.all(\n    themes.map(\n      async (theme) => isSpecialTheme(theme) ? null : normalizeTheme(await normalizeGetter(theme))\n    )\n  );\n  return resolved.filter((i) => !!i);\n}\n\nlet _emitDeprecation = 3;\nlet _emitError = false;\nfunction enableDeprecationWarnings(emitDeprecation = true, emitError = false) {\n  _emitDeprecation = emitDeprecation;\n  _emitError = emitError;\n}\nfunction warnDeprecated(message, version = 3) {\n  if (!_emitDeprecation)\n    return;\n  if (typeof _emitDeprecation === \"number\" && version > _emitDeprecation)\n    return;\n  if (_emitError) {\n    throw new Error(`[SHIKI DEPRECATE]: ${message}`);\n  } else {\n    console.trace(`[SHIKI DEPRECATE]: ${message}`);\n  }\n}\n\nclass ShikiError extends Error {\n  constructor(message) {\n    super(message);\n    this.name = \"ShikiError\";\n  }\n}\n\nclass Registry extends Registry$1 {\n  constructor(_resolver, _themes, _langs, _alias = {}) {\n    super(_resolver);\n    this._resolver = _resolver;\n    this._themes = _themes;\n    this._langs = _langs;\n    this._alias = _alias;\n    this._themes.map((t) => this.loadTheme(t));\n    this.loadLanguages(this._langs);\n  }\n  _resolvedThemes = /* @__PURE__ */ new Map();\n  _resolvedGrammars = /* @__PURE__ */ new Map();\n  _langMap = /* @__PURE__ */ new Map();\n  _langGraph = /* @__PURE__ */ new Map();\n  _textmateThemeCache = /* @__PURE__ */ new WeakMap();\n  _loadedThemesCache = null;\n  _loadedLanguagesCache = null;\n  getTheme(theme) {\n    if (typeof theme === \"string\")\n      return this._resolvedThemes.get(theme);\n    else\n      return this.loadTheme(theme);\n  }\n  loadTheme(theme) {\n    const _theme = normalizeTheme(theme);\n    if (_theme.name) {\n      this._resolvedThemes.set(_theme.name, _theme);\n      this._loadedThemesCache = null;\n    }\n    return _theme;\n  }\n  getLoadedThemes() {\n    if (!this._loadedThemesCache)\n      this._loadedThemesCache = [...this._resolvedThemes.keys()];\n    return this._loadedThemesCache;\n  }\n  // Override and re-implement this method to cache the textmate themes as `TextMateTheme.createFromRawTheme`\n  // is expensive. Themes can switch often especially for dual-theme support.\n  //\n  // The parent class also accepts `colorMap` as the second parameter, but since we don't use that,\n  // we omit here so it's easier to cache the themes.\n  setTheme(theme) {\n    let textmateTheme = this._textmateThemeCache.get(theme);\n    if (!textmateTheme) {\n      textmateTheme = Theme.createFromRawTheme(theme);\n      this._textmateThemeCache.set(theme, textmateTheme);\n    }\n    this._syncRegistry.setTheme(textmateTheme);\n  }\n  getGrammar(name) {\n    if (this._alias[name]) {\n      const resolved = /* @__PURE__ */ new Set([name]);\n      while (this._alias[name]) {\n        name = this._alias[name];\n        if (resolved.has(name))\n          throw new ShikiError(`Circular alias \\`${Array.from(resolved).join(\" -> \")} -> ${name}\\``);\n        resolved.add(name);\n      }\n    }\n    return this._resolvedGrammars.get(name);\n  }\n  loadLanguage(lang) {\n    if (this.getGrammar(lang.name))\n      return;\n    const embeddedLazilyBy = new Set(\n      [...this._langMap.values()].filter((i) => i.embeddedLangsLazy?.includes(lang.name))\n    );\n    this._resolver.addLanguage(lang);\n    const grammarConfig = {\n      balancedBracketSelectors: lang.balancedBracketSelectors || [\"*\"],\n      unbalancedBracketSelectors: lang.unbalancedBracketSelectors || []\n    };\n    this._syncRegistry._rawGrammars.set(lang.scopeName, lang);\n    const g = this.loadGrammarWithConfiguration(lang.scopeName, 1, grammarConfig);\n    g.name = lang.name;\n    this._resolvedGrammars.set(lang.name, g);\n    if (lang.aliases) {\n      lang.aliases.forEach((alias) => {\n        this._alias[alias] = lang.name;\n      });\n    }\n    this._loadedLanguagesCache = null;\n    if (embeddedLazilyBy.size) {\n      for (const e of embeddedLazilyBy) {\n        this._resolvedGrammars.delete(e.name);\n        this._loadedLanguagesCache = null;\n        this._syncRegistry?._injectionGrammars?.delete(e.scopeName);\n        this._syncRegistry?._grammars?.delete(e.scopeName);\n        this.loadLanguage(this._langMap.get(e.name));\n      }\n    }\n  }\n  dispose() {\n    super.dispose();\n    this._resolvedThemes.clear();\n    this._resolvedGrammars.clear();\n    this._langMap.clear();\n    this._langGraph.clear();\n    this._loadedThemesCache = null;\n  }\n  loadLanguages(langs) {\n    for (const lang of langs)\n      this.resolveEmbeddedLanguages(lang);\n    const langsGraphArray = Array.from(this._langGraph.entries());\n    const missingLangs = langsGraphArray.filter(([_, lang]) => !lang);\n    if (missingLangs.length) {\n      const dependents = langsGraphArray.filter(([_, lang]) => lang && lang.embeddedLangs?.some((l) => missingLangs.map(([name]) => name).includes(l))).filter((lang) => !missingLangs.includes(lang));\n      throw new ShikiError(`Missing languages ${missingLangs.map(([name]) => `\\`${name}\\``).join(\", \")}, required by ${dependents.map(([name]) => `\\`${name}\\``).join(\", \")}`);\n    }\n    for (const [_, lang] of langsGraphArray)\n      this._resolver.addLanguage(lang);\n    for (const [_, lang] of langsGraphArray)\n      this.loadLanguage(lang);\n  }\n  getLoadedLanguages() {\n    if (!this._loadedLanguagesCache) {\n      this._loadedLanguagesCache = [\n        .../* @__PURE__ */ new Set([...this._resolvedGrammars.keys(), ...Object.keys(this._alias)])\n      ];\n    }\n    return this._loadedLanguagesCache;\n  }\n  resolveEmbeddedLanguages(lang) {\n    this._langMap.set(lang.name, lang);\n    this._langGraph.set(lang.name, lang);\n    if (lang.embeddedLangs) {\n      for (const embeddedLang of lang.embeddedLangs)\n        this._langGraph.set(embeddedLang, this._langMap.get(embeddedLang));\n    }\n  }\n}\n\nclass Resolver {\n  _langs = /* @__PURE__ */ new Map();\n  _scopeToLang = /* @__PURE__ */ new Map();\n  _injections = /* @__PURE__ */ new Map();\n  _onigLib;\n  constructor(engine, langs) {\n    this._onigLib = {\n      createOnigScanner: (patterns) => engine.createScanner(patterns),\n      createOnigString: (s) => engine.createString(s)\n    };\n    langs.forEach((i) => this.addLanguage(i));\n  }\n  get onigLib() {\n    return this._onigLib;\n  }\n  getLangRegistration(langIdOrAlias) {\n    return this._langs.get(langIdOrAlias);\n  }\n  loadGrammar(scopeName) {\n    return this._scopeToLang.get(scopeName);\n  }\n  addLanguage(l) {\n    this._langs.set(l.name, l);\n    if (l.aliases) {\n      l.aliases.forEach((a) => {\n        this._langs.set(a, l);\n      });\n    }\n    this._scopeToLang.set(l.scopeName, l);\n    if (l.injectTo) {\n      l.injectTo.forEach((i) => {\n        if (!this._injections.get(i))\n          this._injections.set(i, []);\n        this._injections.get(i).push(l.scopeName);\n      });\n    }\n  }\n  getInjections(scopeName) {\n    const scopeParts = scopeName.split(\".\");\n    let injections = [];\n    for (let i = 1; i <= scopeParts.length; i++) {\n      const subScopeName = scopeParts.slice(0, i).join(\".\");\n      injections = [...injections, ...this._injections.get(subScopeName) || []];\n    }\n    return injections;\n  }\n}\n\nlet instancesCount = 0;\nfunction createShikiInternalSync(options) {\n  instancesCount += 1;\n  if (options.warnings !== false && instancesCount >= 10 && instancesCount % 10 === 0)\n    console.warn(`[Shiki] ${instancesCount} instances have been created. Shiki is supposed to be used as a singleton, consider refactoring your code to cache your highlighter instance; Or call \\`highlighter.dispose()\\` to release unused instances.`);\n  let isDisposed = false;\n  if (!options.engine)\n    throw new ShikiError(\"`engine` option is required for synchronous mode\");\n  const langs = (options.langs || []).flat(1);\n  const themes = (options.themes || []).flat(1).map(normalizeTheme);\n  const resolver = new Resolver(options.engine, langs);\n  const _registry = new Registry(resolver, themes, langs, options.langAlias);\n  let _lastTheme;\n  function getLanguage(name) {\n    ensureNotDisposed();\n    const _lang = _registry.getGrammar(typeof name === \"string\" ? name : name.name);\n    if (!_lang)\n      throw new ShikiError(`Language \\`${name}\\` not found, you may need to load it first`);\n    return _lang;\n  }\n  function getTheme(name) {\n    if (name === \"none\")\n      return { bg: \"\", fg: \"\", name: \"none\", settings: [], type: \"dark\" };\n    ensureNotDisposed();\n    const _theme = _registry.getTheme(name);\n    if (!_theme)\n      throw new ShikiError(`Theme \\`${name}\\` not found, you may need to load it first`);\n    return _theme;\n  }\n  function setTheme(name) {\n    ensureNotDisposed();\n    const theme = getTheme(name);\n    if (_lastTheme !== name) {\n      _registry.setTheme(theme);\n      _lastTheme = name;\n    }\n    const colorMap = _registry.getColorMap();\n    return {\n      theme,\n      colorMap\n    };\n  }\n  function getLoadedThemes() {\n    ensureNotDisposed();\n    return _registry.getLoadedThemes();\n  }\n  function getLoadedLanguages() {\n    ensureNotDisposed();\n    return _registry.getLoadedLanguages();\n  }\n  function loadLanguageSync(...langs2) {\n    ensureNotDisposed();\n    _registry.loadLanguages(langs2.flat(1));\n  }\n  async function loadLanguage(...langs2) {\n    return loadLanguageSync(await resolveLangs(langs2));\n  }\n  function loadThemeSync(...themes2) {\n    ensureNotDisposed();\n    for (const theme of themes2.flat(1)) {\n      _registry.loadTheme(theme);\n    }\n  }\n  async function loadTheme(...themes2) {\n    ensureNotDisposed();\n    return loadThemeSync(await resolveThemes(themes2));\n  }\n  function ensureNotDisposed() {\n    if (isDisposed)\n      throw new ShikiError(\"Shiki instance has been disposed\");\n  }\n  function dispose() {\n    if (isDisposed)\n      return;\n    isDisposed = true;\n    _registry.dispose();\n    instancesCount -= 1;\n  }\n  return {\n    setTheme,\n    getTheme,\n    getLanguage,\n    getLoadedThemes,\n    getLoadedLanguages,\n    loadLanguage,\n    loadLanguageSync,\n    loadTheme,\n    loadThemeSync,\n    dispose,\n    [Symbol.dispose]: dispose\n  };\n}\n\nasync function createShikiInternal(options) {\n  if (!options.engine) {\n    warnDeprecated(\"`engine` option is required. Use `createOnigurumaEngine` or `createJavaScriptRegexEngine` to create an engine.\");\n  }\n  const [\n    themes,\n    langs,\n    engine\n  ] = await Promise.all([\n    resolveThemes(options.themes || []),\n    resolveLangs(options.langs || []),\n    options.engine\n  ]);\n  return createShikiInternalSync({\n    ...options,\n    themes,\n    langs,\n    engine\n  });\n}\n\nasync function createHighlighterCore(options) {\n  const internal = await createShikiInternal(options);\n  return {\n    getLastGrammarState: (...args) => getLastGrammarState(internal, ...args),\n    codeToTokensBase: (code, options2) => codeToTokensBase(internal, code, options2),\n    codeToTokensWithThemes: (code, options2) => codeToTokensWithThemes(internal, code, options2),\n    codeToTokens: (code, options2) => codeToTokens(internal, code, options2),\n    codeToHast: (code, options2) => codeToHast(internal, code, options2),\n    codeToHtml: (code, options2) => codeToHtml(internal, code, options2),\n    getBundledLanguages: () => ({}),\n    getBundledThemes: () => ({}),\n    ...internal,\n    getInternalContext: () => internal\n  };\n}\nfunction createHighlighterCoreSync(options) {\n  const internal = createShikiInternalSync(options);\n  return {\n    getLastGrammarState: (...args) => getLastGrammarState(internal, ...args),\n    codeToTokensBase: (code, options2) => codeToTokensBase(internal, code, options2),\n    codeToTokensWithThemes: (code, options2) => codeToTokensWithThemes(internal, code, options2),\n    codeToTokens: (code, options2) => codeToTokens(internal, code, options2),\n    codeToHast: (code, options2) => codeToHast(internal, code, options2),\n    codeToHtml: (code, options2) => codeToHtml(internal, code, options2),\n    getBundledLanguages: () => ({}),\n    getBundledThemes: () => ({}),\n    ...internal,\n    getInternalContext: () => internal\n  };\n}\nfunction makeSingletonHighlighterCore(createHighlighter) {\n  let _shiki;\n  async function getSingletonHighlighterCore2(options) {\n    if (!_shiki) {\n      _shiki = createHighlighter({\n        ...options,\n        themes: options.themes || [],\n        langs: options.langs || []\n      });\n      return _shiki;\n    } else {\n      const s = await _shiki;\n      await Promise.all([\n        s.loadTheme(...options.themes || []),\n        s.loadLanguage(...options.langs || [])\n      ]);\n      return s;\n    }\n  }\n  return getSingletonHighlighterCore2;\n}\nconst getSingletonHighlighterCore = /* @__PURE__ */ makeSingletonHighlighterCore(createHighlighterCore);\n\nfunction createdBundledHighlighter(options) {\n  const bundledLanguages = options.langs;\n  const bundledThemes = options.themes;\n  const engine = options.engine;\n  async function createHighlighter(options2) {\n    function resolveLang(lang) {\n      if (typeof lang === \"string\") {\n        if (isSpecialLang(lang))\n          return [];\n        const bundle = bundledLanguages[lang];\n        if (!bundle)\n          throw new ShikiError$1(`Language \\`${lang}\\` is not included in this bundle. You may want to load it from external source.`);\n        return bundle;\n      }\n      return lang;\n    }\n    function resolveTheme(theme) {\n      if (isSpecialTheme(theme))\n        return \"none\";\n      if (typeof theme === \"string\") {\n        const bundle = bundledThemes[theme];\n        if (!bundle)\n          throw new ShikiError$1(`Theme \\`${theme}\\` is not included in this bundle. You may want to load it from external source.`);\n        return bundle;\n      }\n      return theme;\n    }\n    const _themes = (options2.themes ?? []).map((i) => resolveTheme(i));\n    const langs = (options2.langs ?? []).map((i) => resolveLang(i));\n    const core = await createHighlighterCore({\n      engine: options2.engine ?? engine(),\n      ...options2,\n      themes: _themes,\n      langs\n    });\n    return {\n      ...core,\n      loadLanguage(...langs2) {\n        return core.loadLanguage(...langs2.map(resolveLang));\n      },\n      loadTheme(...themes) {\n        return core.loadTheme(...themes.map(resolveTheme));\n      },\n      getBundledLanguages() {\n        return bundledLanguages;\n      },\n      getBundledThemes() {\n        return bundledThemes;\n      }\n    };\n  }\n  return createHighlighter;\n}\nfunction makeSingletonHighlighter(createHighlighter) {\n  let _shiki;\n  async function getSingletonHighlighter(options = {}) {\n    if (!_shiki) {\n      _shiki = createHighlighter({\n        ...options,\n        themes: options.themes || [],\n        langs: options.langs || []\n      });\n      return _shiki;\n    } else {\n      const s = await _shiki;\n      await Promise.all([\n        s.loadTheme(...options.themes || []),\n        s.loadLanguage(...options.langs || [])\n      ]);\n      return s;\n    }\n  }\n  return getSingletonHighlighter;\n}\nfunction createSingletonShorthands(createHighlighter, config) {\n  const getSingletonHighlighter = makeSingletonHighlighter(createHighlighter);\n  async function get(code, options) {\n    const shiki = await getSingletonHighlighter({\n      langs: [options.lang],\n      themes: \"theme\" in options ? [options.theme] : Object.values(options.themes)\n    });\n    const langs = await config?.guessEmbeddedLanguages?.(code, options.lang, shiki);\n    if (langs) {\n      await shiki.loadLanguage(...langs);\n    }\n    return shiki;\n  }\n  return {\n    getSingletonHighlighter(options) {\n      return getSingletonHighlighter(options);\n    },\n    async codeToHtml(code, options) {\n      const shiki = await get(code, options);\n      return shiki.codeToHtml(code, options);\n    },\n    async codeToHast(code, options) {\n      const shiki = await get(code, options);\n      return shiki.codeToHast(code, options);\n    },\n    async codeToTokens(code, options) {\n      const shiki = await get(code, options);\n      return shiki.codeToTokens(code, options);\n    },\n    async codeToTokensBase(code, options) {\n      const shiki = await get(code, options);\n      return shiki.codeToTokensBase(code, options);\n    },\n    async codeToTokensWithThemes(code, options) {\n      const shiki = await get(code, options);\n      return shiki.codeToTokensWithThemes(code, options);\n    },\n    async getLastGrammarState(code, options) {\n      const shiki = await getSingletonHighlighter({\n        langs: [options.lang],\n        themes: [options.theme]\n      });\n      return shiki.getLastGrammarState(code, options);\n    }\n  };\n}\n\nfunction createCssVariablesTheme(options = {}) {\n  const {\n    name = \"css-variables\",\n    variablePrefix = \"--shiki-\",\n    fontStyle = true\n  } = options;\n  const variable = (name2) => {\n    if (options.variableDefaults?.[name2])\n      return `var(${variablePrefix}${name2}, ${options.variableDefaults[name2]})`;\n    return `var(${variablePrefix}${name2})`;\n  };\n  const theme = {\n    name,\n    type: \"dark\",\n    colors: {\n      \"editor.foreground\": variable(\"foreground\"),\n      \"editor.background\": variable(\"background\"),\n      \"terminal.ansiBlack\": variable(\"ansi-black\"),\n      \"terminal.ansiRed\": variable(\"ansi-red\"),\n      \"terminal.ansiGreen\": variable(\"ansi-green\"),\n      \"terminal.ansiYellow\": variable(\"ansi-yellow\"),\n      \"terminal.ansiBlue\": variable(\"ansi-blue\"),\n      \"terminal.ansiMagenta\": variable(\"ansi-magenta\"),\n      \"terminal.ansiCyan\": variable(\"ansi-cyan\"),\n      \"terminal.ansiWhite\": variable(\"ansi-white\"),\n      \"terminal.ansiBrightBlack\": variable(\"ansi-bright-black\"),\n      \"terminal.ansiBrightRed\": variable(\"ansi-bright-red\"),\n      \"terminal.ansiBrightGreen\": variable(\"ansi-bright-green\"),\n      \"terminal.ansiBrightYellow\": variable(\"ansi-bright-yellow\"),\n      \"terminal.ansiBrightBlue\": variable(\"ansi-bright-blue\"),\n      \"terminal.ansiBrightMagenta\": variable(\"ansi-bright-magenta\"),\n      \"terminal.ansiBrightCyan\": variable(\"ansi-bright-cyan\"),\n      \"terminal.ansiBrightWhite\": variable(\"ansi-bright-white\")\n    },\n    tokenColors: [\n      {\n        scope: [\n          \"keyword.operator.accessor\",\n          \"meta.group.braces.round.function.arguments\",\n          \"meta.template.expression\",\n          \"markup.fenced_code meta.embedded.block\"\n        ],\n        settings: {\n          foreground: variable(\"foreground\")\n        }\n      },\n      {\n        scope: \"emphasis\",\n        settings: {\n          fontStyle: \"italic\"\n        }\n      },\n      {\n        scope: [\"strong\", \"markup.heading.markdown\", \"markup.bold.markdown\"],\n        settings: {\n          fontStyle: \"bold\"\n        }\n      },\n      {\n        scope: [\"markup.italic.markdown\"],\n        settings: {\n          fontStyle: \"italic\"\n        }\n      },\n      {\n        scope: \"meta.link.inline.markdown\",\n        settings: {\n          fontStyle: \"underline\",\n          foreground: variable(\"token-link\")\n        }\n      },\n      {\n        scope: [\"string\", \"markup.fenced_code\", \"markup.inline\"],\n        settings: {\n          foreground: variable(\"token-string\")\n        }\n      },\n      {\n        scope: [\"comment\", \"string.quoted.docstring.multi\"],\n        settings: {\n          foreground: variable(\"token-comment\")\n        }\n      },\n      {\n        scope: [\n          \"constant.numeric\",\n          \"constant.language\",\n          \"constant.other.placeholder\",\n          \"constant.character.format.placeholder\",\n          \"variable.language.this\",\n          \"variable.other.object\",\n          \"variable.other.class\",\n          \"variable.other.constant\",\n          \"meta.property-name\",\n          \"meta.property-value\",\n          \"support\"\n        ],\n        settings: {\n          foreground: variable(\"token-constant\")\n        }\n      },\n      {\n        scope: [\n          \"keyword\",\n          \"storage.modifier\",\n          \"storage.type\",\n          \"storage.control.clojure\",\n          \"entity.name.function.clojure\",\n          \"entity.name.tag.yaml\",\n          \"support.function.node\",\n          \"support.type.property-name.json\",\n          \"punctuation.separator.key-value\",\n          \"punctuation.definition.template-expression\"\n        ],\n        settings: {\n          foreground: variable(\"token-keyword\")\n        }\n      },\n      {\n        scope: \"variable.parameter.function\",\n        settings: {\n          foreground: variable(\"token-parameter\")\n        }\n      },\n      {\n        scope: [\n          \"support.function\",\n          \"entity.name.type\",\n          \"entity.other.inherited-class\",\n          \"meta.function-call\",\n          \"meta.instance.constructor\",\n          \"entity.other.attribute-name\",\n          \"entity.name.function\",\n          \"constant.keyword.clojure\"\n        ],\n        settings: {\n          foreground: variable(\"token-function\")\n        }\n      },\n      {\n        scope: [\n          \"entity.name.tag\",\n          \"string.quoted\",\n          \"string.regexp\",\n          \"string.interpolated\",\n          \"string.template\",\n          \"string.unquoted.plain.out.yaml\",\n          \"keyword.other.template\"\n        ],\n        settings: {\n          foreground: variable(\"token-string-expression\")\n        }\n      },\n      {\n        scope: [\n          \"punctuation.definition.arguments\",\n          \"punctuation.definition.dict\",\n          \"punctuation.separator\",\n          \"meta.function-call.arguments\"\n        ],\n        settings: {\n          foreground: variable(\"token-punctuation\")\n        }\n      },\n      {\n        // [Custom] Markdown links\n        scope: [\n          \"markup.underline.link\",\n          \"punctuation.definition.metadata.markdown\"\n        ],\n        settings: {\n          foreground: variable(\"token-link\")\n        }\n      },\n      {\n        // [Custom] Markdown list\n        scope: [\"beginning.punctuation.definition.list.markdown\"],\n        settings: {\n          foreground: variable(\"token-string\")\n        }\n      },\n      {\n        // [Custom] Markdown punctuation definition brackets\n        scope: [\n          \"punctuation.definition.string.begin.markdown\",\n          \"punctuation.definition.string.end.markdown\",\n          \"string.other.link.title.markdown\",\n          \"string.other.link.description.markdown\"\n        ],\n        settings: {\n          foreground: variable(\"token-keyword\")\n        }\n      },\n      {\n        // [Custom] Diff\n        scope: [\n          \"markup.inserted\",\n          \"meta.diff.header.to-file\",\n          \"punctuation.definition.inserted\"\n        ],\n        settings: {\n          foreground: variable(\"token-inserted\")\n        }\n      },\n      {\n        scope: [\n          \"markup.deleted\",\n          \"meta.diff.header.from-file\",\n          \"punctuation.definition.deleted\"\n        ],\n        settings: {\n          foreground: variable(\"token-deleted\")\n        }\n      },\n      {\n        scope: [\n          \"markup.changed\",\n          \"punctuation.definition.changed\"\n        ],\n        settings: {\n          foreground: variable(\"token-changed\")\n        }\n      }\n    ]\n  };\n  if (!fontStyle) {\n    theme.tokenColors = theme.tokenColors?.map((tokenColor) => {\n      if (tokenColor.settings?.fontStyle)\n        delete tokenColor.settings.fontStyle;\n      return tokenColor;\n    });\n  }\n  return theme;\n}\n\nexport { addClassToHast, applyColorReplacements, codeToHast, codeToHtml, codeToTokens, codeToTokensBase, codeToTokensWithThemes, createCssVariablesTheme, createHighlighterCore, createHighlighterCoreSync, createPositionConverter, createShikiInternal, createShikiInternalSync, createSingletonShorthands, createdBundledHighlighter, enableDeprecationWarnings, flatTokenVariants, getSingletonHighlighterCore, getTokenStyleObject, guessEmbeddedLanguages, hastToHtml, isNoneTheme, isPlainLang, isSpecialLang, isSpecialTheme, makeSingletonHighlighter, makeSingletonHighlighterCore, normalizeGetter, normalizeTheme, resolveColorReplacements, splitLines, splitToken, splitTokens, stringifyTokenStyle, toArray, tokenizeAnsiWithTheme, tokenizeWithTheme, tokensToHast, transformerDecorations, warnDeprecated };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAEA;AACA;;;;;AAEA,SAAS,yBAAyB,KAAK,EAAE,OAAO;IAC9C,MAAM,eAAe,OAAO,UAAU,WAAW,CAAC,IAAI;QAAE,GAAG,MAAM,iBAAiB;IAAC;IACnF,MAAM,YAAY,OAAO,UAAU,WAAW,QAAQ,MAAM,IAAI;IAChE,KAAK,MAAM,CAAC,KAAK,MAAM,IAAI,OAAO,OAAO,CAAC,SAAS,qBAAqB,CAAC,GAAI;QAC3E,IAAI,OAAO,UAAU,UACnB,YAAY,CAAC,IAAI,GAAG;aACjB,IAAI,QAAQ,WACf,OAAO,MAAM,CAAC,cAAc;IAChC;IACA,OAAO;AACT;AACA,SAAS,uBAAuB,KAAK,EAAE,YAAY;IACjD,IAAI,CAAC,OACH,OAAO;IACT,OAAO,cAAc,CAAC,OAAO,cAAc,IAAI;AACjD;AAEA,SAAS,QAAQ,CAAC;IAChB,OAAO,MAAM,OAAO,CAAC,KAAK,IAAI;QAAC;KAAE;AACnC;AACA,eAAe,gBAAgB,CAAC;IAC9B,OAAO,QAAQ,OAAO,CAAC,OAAO,MAAM,aAAa,MAAM,GAAG,IAAI,CAAC,CAAC,IAAM,EAAE,OAAO,IAAI;AACrF;AACA,SAAS,YAAY,IAAI;IACvB,OAAO,CAAC,QAAQ;QAAC;QAAa;QAAO;QAAQ;KAAQ,CAAC,QAAQ,CAAC;AACjE;AACA,SAAS,cAAc,IAAI;IACzB,OAAO,SAAS,UAAU,YAAY;AACxC;AACA,SAAS,YAAY,KAAK;IACxB,OAAO,UAAU;AACnB;AACA,SAAS,eAAe,KAAK;IAC3B,OAAO,YAAY;AACrB;AAEA,SAAS,eAAe,IAAI,EAAE,SAAS;IACrC,IAAI,CAAC,WACH,OAAO;IACT,KAAK,UAAU,KAAK,CAAC;IACrB,KAAK,UAAU,CAAC,KAAK,KAAK,EAAE;IAC5B,IAAI,OAAO,KAAK,UAAU,CAAC,KAAK,KAAK,UACnC,KAAK,UAAU,CAAC,KAAK,GAAG,KAAK,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC;IACtD,IAAI,CAAC,MAAM,OAAO,CAAC,KAAK,UAAU,CAAC,KAAK,GACtC,KAAK,UAAU,CAAC,KAAK,GAAG,EAAE;IAC5B,MAAM,UAAU,MAAM,OAAO,CAAC,aAAa,YAAY,UAAU,KAAK,CAAC;IACvE,KAAK,MAAM,KAAK,QAAS;QACvB,IAAI,KAAK,CAAC,KAAK,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,IACvC,KAAK,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC;IAC/B;IACA,OAAO;AACT;AAEA,SAAS,WAAW,IAAI,EAAE,iBAAiB,KAAK;IAC9C,MAAM,QAAQ,KAAK,KAAK,CAAC;IACzB,IAAI,QAAQ;IACZ,MAAM,QAAQ,EAAE;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,EAAG;QACxC,MAAM,OAAO,iBAAiB,KAAK,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,KAAK,CAAC,EAAE;QACxE,MAAM,IAAI,CAAC;YAAC;YAAM;SAAM;QACxB,SAAS,KAAK,CAAC,EAAE,CAAC,MAAM;QACxB,SAAS,KAAK,CAAC,IAAI,EAAE,EAAE,UAAU;IACnC;IACA,OAAO;AACT;AACA,SAAS,wBAAwB,IAAI;IACnC,MAAM,QAAQ,WAAW,MAAM,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,GAAK;IACrD,SAAS,WAAW,KAAK;QACvB,IAAI,UAAU,KAAK,MAAM,EAAE;YACzB,OAAO;gBACL,MAAM,MAAM,MAAM,GAAG;gBACrB,WAAW,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM;YAC3C;QACF;QACA,IAAI,YAAY;QAChB,IAAI,OAAO;QACX,KAAK,MAAM,YAAY,MAAO;YAC5B,IAAI,YAAY,SAAS,MAAM,EAC7B;YACF,aAAa,SAAS,MAAM;YAC5B;QACF;QACA,OAAO;YAAE;YAAM;QAAU;IAC3B;IACA,SAAS,WAAW,IAAI,EAAE,SAAS;QACjC,IAAI,QAAQ;QACZ,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IACxB,SAAS,KAAK,CAAC,EAAE,CAAC,MAAM;QAC1B,SAAS;QACT,OAAO;IACT;IACA,OAAO;QACL;QACA;QACA;IACF;AACF;AACA,SAAS,uBAAuB,IAAI,EAAE,KAAK,EAAE,WAAW;IACtD,MAAM,QAAQ,aAAa,GAAG,IAAI;IAClC,KAAK,MAAM,SAAS,KAAK,QAAQ,CAAC,0BAA2B;QAC3D,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;IACpB;IACA,KAAK,MAAM,SAAS,KAAK,QAAQ,CAAC,wBAAyB;QACzD,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;IACpB;IACA,KAAK,MAAM,SAAS,KAAK,QAAQ,CAAC,wBAAyB;QACzD,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;IACpB;IACA,IAAI,CAAC,aACH,OAAO,MAAM,IAAI,CAAC;IACpB,MAAM,SAAS,YAAY,mBAAmB;IAC9C,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,CAAC,IAAM,KAAK,MAAM,CAAC,EAAE;AACvD;AAEA,SAAS,WAAW,KAAK,EAAE,OAAO;IAChC,IAAI,aAAa;IACjB,MAAM,SAAS,EAAE;IACjB,KAAK,MAAM,UAAU,QAAS;QAC5B,IAAI,SAAS,YAAY;YACvB,OAAO,IAAI,CAAC;gBACV,GAAG,KAAK;gBACR,SAAS,MAAM,OAAO,CAAC,KAAK,CAAC,YAAY;gBACzC,QAAQ,MAAM,MAAM,GAAG;YACzB;QACF;QACA,aAAa;IACf;IACA,IAAI,aAAa,MAAM,OAAO,CAAC,MAAM,EAAE;QACrC,OAAO,IAAI,CAAC;YACV,GAAG,KAAK;YACR,SAAS,MAAM,OAAO,CAAC,KAAK,CAAC;YAC7B,QAAQ,MAAM,MAAM,GAAG;QACzB;IACF;IACA,OAAO;AACT;AACA,SAAS,YAAY,MAAM,EAAE,WAAW;IACtC,MAAM,SAAS,MAAM,IAAI,CAAC,uBAAuB,MAAM,cAAc,IAAI,IAAI,cAAc,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;IAC9G,IAAI,CAAC,OAAO,MAAM,EAChB,OAAO;IACT,OAAO,OAAO,GAAG,CAAC,CAAC;QACjB,OAAO,KAAK,OAAO,CAAC,CAAC;YACnB,MAAM,qBAAqB,OAAO,MAAM,CAAC,CAAC,IAAM,MAAM,MAAM,GAAG,KAAK,IAAI,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC,IAAM,IAAI,MAAM,MAAM,EAAE,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;YAC7J,IAAI,CAAC,mBAAmB,MAAM,EAC5B,OAAO;YACT,OAAO,WAAW,OAAO;QAC3B;IACF;AACF;AACA,SAAS,kBAAkB,MAAM,EAAE,aAAa,EAAE,iBAAiB,EAAE,YAAY;IAC/E,MAAM,QAAQ;QACZ,SAAS,OAAO,OAAO;QACvB,aAAa,OAAO,WAAW;QAC/B,QAAQ,OAAO,MAAM;IACvB;IACA,MAAM,SAAS,cAAc,GAAG,CAAC,CAAC,IAAM,oBAAoB,OAAO,QAAQ,CAAC,EAAE;IAC9E,MAAM,YAAY,IAAI,IAAI,OAAO,OAAO,CAAC,CAAC,IAAM,OAAO,IAAI,CAAC;IAC5D,MAAM,eAAe,CAAC;IACtB,OAAO,OAAO,CAAC,CAAC,KAAK;QACnB,KAAK,MAAM,OAAO,UAAW;YAC3B,MAAM,QAAQ,GAAG,CAAC,IAAI,IAAI;YAC1B,IAAI,QAAQ,KAAK,cAAc;gBAC7B,YAAY,CAAC,IAAI,GAAG;YACtB,OAAO;gBACL,MAAM,UAAU,QAAQ,UAAU,KAAK,QAAQ,qBAAqB,QAAQ,CAAC,CAAC,EAAE,KAAK;gBACrF,MAAM,SAAS,oBAAoB,aAAa,CAAC,IAAI,GAAG,CAAC,QAAQ,UAAU,KAAK,OAAO;gBACvF,YAAY,CAAC,OAAO,GAAG;YACzB;QACF;IACF;IACA,MAAM,SAAS,GAAG;IAClB,OAAO;AACT;AACA,SAAS,oBAAoB,KAAK;IAChC,MAAM,SAAS,CAAC;IAChB,IAAI,MAAM,KAAK,EACb,OAAO,KAAK,GAAG,MAAM,KAAK;IAC5B,IAAI,MAAM,OAAO,EACf,MAAM,CAAC,mBAAmB,GAAG,MAAM,OAAO;IAC5C,IAAI,MAAM,SAAS,EAAE;QACnB,IAAI,MAAM,SAAS,GAAG,mKAAA,CAAA,YAAS,CAAC,MAAM,EACpC,MAAM,CAAC,aAAa,GAAG;QACzB,IAAI,MAAM,SAAS,GAAG,mKAAA,CAAA,YAAS,CAAC,IAAI,EAClC,MAAM,CAAC,cAAc,GAAG;QAC1B,MAAM,cAAc,EAAE;QACtB,IAAI,MAAM,SAAS,GAAG,mKAAA,CAAA,YAAS,CAAC,SAAS,EACvC,YAAY,IAAI,CAAC;QACnB,IAAI,MAAM,SAAS,GAAG,mKAAA,CAAA,YAAS,CAAC,aAAa,EAC3C,YAAY,IAAI,CAAC;QACnB,IAAI,YAAY,MAAM,EACpB,MAAM,CAAC,kBAAkB,GAAG,YAAY,IAAI,CAAC;IACjD;IACA,OAAO;AACT;AACA,SAAS,oBAAoB,KAAK;IAChC,IAAI,OAAO,UAAU,UACnB,OAAO;IACT,OAAO,OAAO,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK,GAAG,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC;AAC7E;AAEA,MAAM,mBAAmB,aAAa,GAAG,IAAI;AAC7C,SAAS,yBAAyB,IAAI,EAAE,KAAK;IAC3C,iBAAiB,GAAG,CAAC,MAAM;AAC7B;AACA,SAAS,2BAA2B,IAAI;IACtC,OAAO,iBAAiB,GAAG,CAAC;AAC9B;AACA,MAAM;IACJ;;GAEC,GACD,UAAU,CAAC,EAAE;IACb,KAAK;IACL,IAAI,SAAS;QACX,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO;IACjC;IACA,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE;IACvB;IACA,IAAI,SAAS;QACX,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;IACjC;IACA;;GAEC,GACD,OAAO,QAAQ,IAAI,EAAE,MAAM,EAAE;QAC3B,OAAO,IAAI,aACT,OAAO,WAAW,CAAC,QAAQ,QAAQ,GAAG,CAAC,CAAC,QAAU;gBAAC;gBAAO,mKAAA,CAAA,UAAO;aAAC,IAClE;IAEJ;IACA,YAAY,GAAG,IAAI,CAAE;QACnB,IAAI,KAAK,MAAM,KAAK,GAAG;YACrB,MAAM,CAAC,WAAW,KAAK,GAAG;YAC1B,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,OAAO,GAAG;QACjB,OAAO;YACL,MAAM,CAAC,OAAO,MAAM,MAAM,GAAG;YAC7B,IAAI,CAAC,IAAI,GAAG;YACZ,IAAI,CAAC,OAAO,GAAG;gBAAE,CAAC,MAAM,EAAE;YAAM;QAClC;IACF;IACA;;;GAGC,GACD,iBAAiB,QAAQ,IAAI,CAAC,KAAK,EAAE;QACnC,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;IAC5B;IACA,UAAU,QAAQ,IAAI,CAAC,KAAK,EAAE;QAC5B,OAAO,UAAU,IAAI,CAAC,OAAO,CAAC,MAAM;IACtC;IACA,SAAS;QACP,OAAO;YACL,MAAM,IAAI,CAAC,IAAI;YACf,OAAO,IAAI,CAAC,KAAK;YACjB,QAAQ,IAAI,CAAC,MAAM;YACnB,QAAQ,IAAI,CAAC,SAAS;QACxB;IACF;AACF;AACA,SAAS,UAAU,KAAK;IACtB,MAAM,SAAS,EAAE;IACjB,MAAM,UAAU,aAAa,GAAG,IAAI;IACpC,SAAS,UAAU,MAAM;QACvB,IAAI,QAAQ,GAAG,CAAC,SACd;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,OAAO,QAAQ,gBAAgB;QACrC,IAAI,MACF,OAAO,IAAI,CAAC;QACd,IAAI,OAAO,MAAM,EACf,UAAU,OAAO,MAAM;IAC3B;IACA,UAAU;IACV,OAAO;AACT;AACA,SAAS,gBAAgB,KAAK,EAAE,KAAK;IACnC,IAAI,CAAC,CAAC,iBAAiB,YAAY,GACjC,MAAM,IAAI,uJAAA,CAAA,aAAY,CAAC;IACzB,OAAO,MAAM,gBAAgB,CAAC;AAChC;AAEA,SAAS;IACP,MAAM,MAAM,aAAa,GAAG,IAAI;IAChC,SAAS,WAAW,KAAK;QACvB,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG;YACxB,IAAI,oBAAoB,SAAS,CAAC;gBAChC,IAAI,OAAO,MAAM,UAAU;oBACzB,IAAI,IAAI,KAAK,IAAI,MAAM,MAAM,CAAC,MAAM,EAClC,MAAM,IAAI,uJAAA,CAAA,aAAY,CAAC,CAAC,2BAA2B,EAAE,EAAE,eAAe,EAAE,MAAM,MAAM,CAAC,MAAM,EAAE;oBAC/F,OAAO;wBACL,GAAG,UAAU,UAAU,CAAC,EAAE;wBAC1B,QAAQ;oBACV;gBACF,OAAO;oBACL,MAAM,OAAO,UAAU,KAAK,CAAC,EAAE,IAAI,CAAC;oBACpC,IAAI,SAAS,KAAK,GAChB,MAAM,IAAI,uJAAA,CAAA,aAAY,CAAC,CAAC,4BAA4B,EAAE,KAAK,SAAS,CAAC,GAAG,gBAAgB,EAAE,UAAU,KAAK,CAAC,MAAM,EAAE;oBACpH,IAAI,EAAE,SAAS,GAAG,KAAK,EAAE,SAAS,GAAG,KAAK,MAAM,EAC9C,MAAM,IAAI,uJAAA,CAAA,aAAY,CAAC,CAAC,4BAA4B,EAAE,KAAK,SAAS,CAAC,GAAG,OAAO,EAAE,EAAE,IAAI,CAAC,SAAS,EAAE,KAAK,MAAM,EAAE;oBAClH,OAAO;wBACL,GAAG,CAAC;wBACJ,QAAQ,UAAU,UAAU,CAAC,EAAE,IAAI,EAAE,EAAE,SAAS;oBAClD;gBACF;YACF;YACA,MAAM,YAAY,wBAAwB,MAAM,MAAM;YACtD,MAAM,cAAc,CAAC,MAAM,OAAO,CAAC,WAAW,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,IAAM,CAAC;oBAChE,GAAG,CAAC;oBACJ,OAAO,kBAAkB,EAAE,KAAK;oBAChC,KAAK,kBAAkB,EAAE,GAAG;gBAC9B,CAAC;YACD,oBAAoB;YACpB,IAAI,GAAG,CAAC,MAAM,IAAI,EAAE;gBAClB;gBACA;gBACA,QAAQ,MAAM,MAAM;YACtB;QACF;QACA,OAAO,IAAI,GAAG,CAAC,MAAM,IAAI;IAC3B;IACA,OAAO;QACL,MAAM;QACN,QAAO,MAAM;YACX,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,QAC7B;YACF,MAAM,MAAM,WAAW,IAAI;YAC3B,MAAM,cAAc,IAAI,WAAW,CAAC,OAAO,CAAC,CAAC,IAAM;oBAAC,EAAE,KAAK,CAAC,MAAM;oBAAE,EAAE,GAAG,CAAC,MAAM;iBAAC;YACjF,MAAM,WAAW,YAAY,QAAQ;YACrC,OAAO;QACT;QACA,MAAK,MAAM;YACT,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,QAC7B;YACF,MAAM,MAAM,WAAW,IAAI;YAC3B,MAAM,QAAQ,MAAM,IAAI,CAAC,OAAO,QAAQ,EAAE,MAAM,CAAC,CAAC,IAAM,EAAE,IAAI,KAAK,aAAa,EAAE,OAAO,KAAK;YAC9F,IAAI,MAAM,MAAM,KAAK,IAAI,SAAS,CAAC,KAAK,CAAC,MAAM,EAC7C,MAAM,IAAI,uJAAA,CAAA,aAAY,CAAC,CAAC,iCAAiC,EAAE,MAAM,MAAM,CAAC,oDAAoD,EAAE,IAAI,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,+BAA+B,CAAC;YAC3L,SAAS,iBAAiB,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,UAAU;gBACpD,MAAM,SAAS,KAAK,CAAC,KAAK;gBAC1B,IAAI,OAAO;gBACX,IAAI,aAAa,CAAC;gBAClB,IAAI,WAAW,CAAC;gBAChB,IAAI,UAAU,GACZ,aAAa;gBACf,IAAI,QAAQ,GACV,WAAW;gBACb,IAAI,QAAQ,OAAO,iBAAiB,EAClC,WAAW,OAAO,QAAQ,CAAC,MAAM;gBACnC,IAAI,eAAe,CAAC,KAAK,aAAa,CAAC,GAAG;oBACxC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,QAAQ,CAAC,MAAM,EAAE,IAAK;wBAC/C,QAAQ,UAAU,OAAO,QAAQ,CAAC,EAAE;wBACpC,IAAI,eAAe,CAAC,KAAK,KAAK,MAAM,KAAK,OACvC,aAAa,IAAI;wBACnB,IAAI,aAAa,CAAC,KAAK,KAAK,MAAM,KAAK,KACrC,WAAW,IAAI;oBACnB;gBACF;gBACA,IAAI,eAAe,CAAC,GAClB,MAAM,IAAI,uJAAA,CAAA,aAAY,CAAC,CAAC,0CAA0C,EAAE,KAAK,SAAS,CAAC,WAAW,KAAK,GAAG;gBACxG,IAAI,aAAa,CAAC,GAChB,MAAM,IAAI,uJAAA,CAAA,aAAY,CAAC,CAAC,wCAAwC,EAAE,KAAK,SAAS,CAAC,WAAW,GAAG,GAAG;gBACpG,MAAM,WAAW,OAAO,QAAQ,CAAC,KAAK,CAAC,YAAY;gBACnD,IAAI,CAAC,WAAW,UAAU,IAAI,SAAS,MAAM,KAAK,OAAO,QAAQ,CAAC,MAAM,EAAE;oBACxE,gBAAgB,QAAQ,YAAY;gBACtC,OAAO,IAAI,CAAC,WAAW,UAAU,IAAI,SAAS,MAAM,KAAK,KAAK,QAAQ,CAAC,EAAE,CAAC,IAAI,KAAK,WAAW;oBAC5F,gBAAgB,QAAQ,CAAC,EAAE,EAAE,YAAY;gBAC3C,OAAO;oBACL,MAAM,UAAU;wBACd,MAAM;wBACN,SAAS;wBACT,YAAY,CAAC;wBACb;oBACF;oBACA,gBAAgB,SAAS,YAAY;oBACrC,OAAO,QAAQ,CAAC,MAAM,CAAC,YAAY,SAAS,MAAM,EAAE;gBACtD;YACF;YACA,SAAS,UAAU,IAAI,EAAE,UAAU;gBACjC,KAAK,CAAC,KAAK,GAAG,gBAAgB,KAAK,CAAC,KAAK,EAAE,YAAY;YACzD;YACA,SAAS,gBAAgB,EAAE,EAAE,UAAU,EAAE,IAAI;gBAC3C,MAAM,aAAa,WAAW,UAAU,IAAI,CAAC;gBAC7C,MAAM,YAAY,WAAW,SAAS,IAAI,CAAC,CAAC,IAAM,CAAC;gBACnD,GAAG,OAAO,GAAG,WAAW,OAAO,IAAI;gBACnC,GAAG,UAAU,GAAG;oBACd,GAAG,GAAG,UAAU;oBAChB,GAAG,UAAU;oBACb,OAAO,GAAG,UAAU,CAAC,KAAK;gBAC5B;gBACA,IAAI,WAAW,UAAU,EAAE,OACzB,eAAe,IAAI,WAAW,UAAU,CAAC,KAAK;gBAChD,KAAK,UAAU,IAAI,SAAS;gBAC5B,OAAO;YACT;YACA,MAAM,cAAc,EAAE;YACtB,MAAM,SAAS,IAAI,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,MAAM,IAAI,EAAE,GAAG,CAAC,MAAM,GAAG,EAAE,GAAG,CAAC,MAAM;YAC5G,KAAK,MAAM,cAAc,OAAQ;gBAC/B,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG;gBACvB,IAAI,MAAM,IAAI,KAAK,IAAI,IAAI,EAAE;oBAC3B,iBAAiB,MAAM,IAAI,EAAE,MAAM,SAAS,EAAE,IAAI,SAAS,EAAE;gBAC/D,OAAO,IAAI,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE;oBAChC,iBAAiB,MAAM,IAAI,EAAE,MAAM,SAAS,EAAE,OAAO,iBAAiB,EAAE;oBACxE,IAAK,IAAI,IAAI,MAAM,IAAI,GAAG,GAAG,IAAI,IAAI,IAAI,EAAE,IACzC,YAAY,OAAO,CAAC,IAAM,UAAU,GAAG;oBACzC,iBAAiB,IAAI,IAAI,EAAE,GAAG,IAAI,SAAS,EAAE;gBAC/C;YACF;YACA,YAAY,OAAO,CAAC,CAAC,IAAM;QAC7B;IACF;AACF;AACA,SAAS,oBAAoB,KAAK;IAChC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,MAAM,MAAM,KAAK,CAAC,EAAE;QACpB,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,MAAM,EACnC,MAAM,IAAI,uJAAA,CAAA,aAAY,CAAC,CAAC,0BAA0B,EAAE,KAAK,SAAS,CAAC,IAAI,KAAK,EAAE,GAAG,EAAE,KAAK,SAAS,CAAC,IAAI,GAAG,GAAG;QAC9G,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACzC,MAAM,MAAM,KAAK,CAAC,EAAE;YACpB,MAAM,mBAAmB,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,MAAM;YAClG,MAAM,iBAAiB,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,MAAM,IAAI,IAAI,GAAG,CAAC,MAAM,IAAI,IAAI,GAAG,CAAC,MAAM;YAC5F,MAAM,mBAAmB,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,MAAM;YAClG,MAAM,iBAAiB,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,GAAG,CAAC,MAAM,IAAI,IAAI,GAAG,CAAC,MAAM,IAAI,IAAI,GAAG,CAAC,MAAM;YAC5F,IAAI,oBAAoB,kBAAkB,oBAAoB,gBAAgB;gBAC5E,IAAI,oBAAoB,gBACtB;gBACF,IAAI,oBAAoB,gBACtB;gBACF,MAAM,IAAI,uJAAA,CAAA,aAAY,CAAC,CAAC,YAAY,EAAE,KAAK,SAAS,CAAC,IAAI,KAAK,EAAE,KAAK,EAAE,KAAK,SAAS,CAAC,IAAI,KAAK,EAAE,WAAW,CAAC;YAC/G;QACF;IACF;AACF;AACA,SAAS,UAAU,EAAE;IACnB,IAAI,GAAG,IAAI,KAAK,QACd,OAAO,GAAG,KAAK;IACjB,IAAI,GAAG,IAAI,KAAK,WACd,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC;IACzC,OAAO;AACT;AAEA,MAAM,sBAAsB;IAC1B,aAAa,GAAG;CACjB;AACD,SAAS,gBAAgB,OAAO;IAC9B,OAAO;WACF,QAAQ,YAAY,IAAI,EAAE;WAC1B;KACJ;AACH;AAEA,gBAAgB;AAChB,IAAI,cAAc;IAChB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,qBAAqB;AACrB,IAAI,cAAc;IAChB,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;AACL;AAEA,gBAAgB;AAChB,SAAS,aAAa,KAAK,EAAE,QAAQ;IACnC,MAAM,aAAa,MAAM,OAAO,CAAC,QAAQ;IACzC,IAAI,eAAe,CAAC,GAAG;QACrB,IAAI,KAAK,CAAC,aAAa,EAAE,KAAK,KAAK;YACjC,MAAM,YAAY,MAAM,OAAO,CAAC,KAAK;YACrC,IAAI,cAAc,CAAC,GAAG;gBACpB,OAAO;oBACL,UAAU,MAAM,SAAS,CAAC,aAAa,GAAG,WAAW,KAAK,CAAC;oBAC3D,eAAe;oBACf,UAAU,YAAY;gBACxB;YACF;QACF;IACF;IACA,OAAO;QACL,UAAU,MAAM,MAAM;IACxB;AACF;AACA,SAAS,WAAW,QAAQ;IAC1B,MAAM,YAAY,SAAS,KAAK;IAChC,IAAI,cAAc,KAAK;QACrB,MAAM,MAAM,SAAS,MAAM,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,IAAM,OAAO,QAAQ,CAAC;QAC7D,IAAI,IAAI,MAAM,KAAK,KAAK,IAAI,IAAI,CAAC,CAAC,IAAM,OAAO,KAAK,CAAC,KACnD;QACF,OAAO;YACL,MAAM;YACN;QACF;IACF,OAAO,IAAI,cAAc,KAAK;QAC5B,MAAM,QAAQ,SAAS,KAAK;QAC5B,IAAI,OAAO;YACT,OAAO;gBAAE,MAAM;gBAAS,OAAO,OAAO;YAAO;QAC/C;IACF;AACF;AACA,SAAS,cAAc,QAAQ;IAC7B,MAAM,WAAW,EAAE;IACnB,MAAO,SAAS,MAAM,GAAG,EAAG;QAC1B,MAAM,OAAO,SAAS,KAAK;QAC3B,IAAI,CAAC,MACH;QACF,MAAM,UAAU,OAAO,QAAQ,CAAC;QAChC,IAAI,OAAO,KAAK,CAAC,UACf;QACF,IAAI,YAAY,GAAG;YACjB,SAAS,IAAI,CAAC;gBAAE,MAAM;YAAW;QACnC,OAAO,IAAI,WAAW,GAAG;YACvB,MAAM,aAAa,WAAW,CAAC,QAAQ;YACvC,IAAI,YAAY;gBACd,SAAS,IAAI,CAAC;oBACZ,MAAM;oBACN,OAAO,WAAW,CAAC,QAAQ;gBAC7B;YACF;QACF,OAAO,IAAI,WAAW,IAAI;YACxB,MAAM,aAAa,WAAW,CAAC,UAAU,GAAG;YAC5C,IAAI,YAAY;gBACd,SAAS,IAAI,CAAC;oBACZ,MAAM;oBACN,OAAO;gBACT;gBACA,IAAI,eAAe,OAAO;oBACxB,SAAS,IAAI,CAAC;wBACZ,MAAM;wBACN,OAAO;oBACT;gBACF;YACF;QACF,OAAO,IAAI,WAAW,IAAI;YACxB,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,OAAO;oBAAE,MAAM;oBAAS,MAAM,WAAW,CAAC,UAAU,GAAG;gBAAC;YAC1D;QACF,OAAO,IAAI,YAAY,IAAI;YACzB,MAAM,QAAQ,WAAW;YACzB,IAAI,OAAO;gBACT,SAAS,IAAI,CAAC;oBACZ,MAAM;oBACN,OAAO;gBACT;YACF;QACF,OAAO,IAAI,YAAY,IAAI;YACzB,SAAS,IAAI,CAAC;gBACZ,MAAM;YACR;QACF,OAAO,IAAI,WAAW,IAAI;YACxB,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,OAAO;oBAAE,MAAM;oBAAS,MAAM,WAAW,CAAC,UAAU,GAAG;gBAAC;YAC1D;QACF,OAAO,IAAI,YAAY,IAAI;YACzB,MAAM,QAAQ,WAAW;YACzB,IAAI,OAAO;gBACT,SAAS,IAAI,CAAC;oBACZ,MAAM;oBACN,OAAO;gBACT;YACF;QACF,OAAO,IAAI,YAAY,IAAI;YACzB,SAAS,IAAI,CAAC;gBACZ,MAAM;YACR;QACF,OAAO,IAAI,YAAY,IAAI;YACzB,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,OAAO;YACT;QACF,OAAO,IAAI,YAAY,IAAI;YACzB,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,OAAO;YACT;QACF,OAAO,IAAI,WAAW,MAAM,WAAW,IAAI;YACzC,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,OAAO;oBAAE,MAAM;oBAAS,MAAM,WAAW,CAAC,UAAU,KAAK,EAAE;gBAAC;YAC9D;QACF,OAAO,IAAI,WAAW,OAAO,WAAW,KAAK;YAC3C,SAAS,IAAI,CAAC;gBACZ,MAAM;gBACN,OAAO;oBAAE,MAAM;oBAAS,MAAM,WAAW,CAAC,UAAU,MAAM,EAAE;gBAAC;YAC/D;QACF;IACF;IACA,OAAO;AACT;AACA,SAAS;IACP,IAAI,aAAa;IACjB,IAAI,aAAa;IACjB,IAAI,eAAe,aAAa,GAAG,IAAI;IACvC,OAAO;QACL,OAAM,KAAK;YACT,MAAM,SAAS,EAAE;YACjB,IAAI,WAAW;YACf,GAAG;gBACD,MAAM,aAAa,aAAa,OAAO;gBACvC,MAAM,OAAO,WAAW,QAAQ,GAAG,MAAM,SAAS,CAAC,UAAU,WAAW,aAAa,IAAI,MAAM,SAAS,CAAC;gBACzG,IAAI,KAAK,MAAM,GAAG,GAAG;oBACnB,OAAO,IAAI,CAAC;wBACV,OAAO;wBACP;wBACA;wBACA,aAAa,IAAI,IAAI;oBACvB;gBACF;gBACA,IAAI,WAAW,QAAQ,EAAE;oBACvB,MAAM,WAAW,cAAc,WAAW,QAAQ;oBAClD,KAAK,MAAM,cAAc,SAAU;wBACjC,IAAI,WAAW,IAAI,KAAK,YAAY;4BAClC,aAAa;4BACb,aAAa;4BACb,aAAa,KAAK;wBACpB,OAAO,IAAI,WAAW,IAAI,KAAK,wBAAwB;4BACrD,aAAa;wBACf,OAAO,IAAI,WAAW,IAAI,KAAK,wBAAwB;4BACrD,aAAa;wBACf,OAAO,IAAI,WAAW,IAAI,KAAK,mBAAmB;4BAChD,aAAa,MAAM,CAAC,WAAW,KAAK;wBACtC;oBACF;oBACA,KAAK,MAAM,cAAc,SAAU;wBACjC,IAAI,WAAW,IAAI,KAAK,sBAAsB;4BAC5C,aAAa,WAAW,KAAK;wBAC/B,OAAO,IAAI,WAAW,IAAI,KAAK,sBAAsB;4BACnD,aAAa,WAAW,KAAK;wBAC/B,OAAO,IAAI,WAAW,IAAI,KAAK,iBAAiB;4BAC9C,aAAa,GAAG,CAAC,WAAW,KAAK;wBACnC;oBACF;gBACF;gBACA,WAAW,WAAW,QAAQ;YAChC,QAAS,WAAW,MAAM,MAAM,CAAE;YAClC,OAAO;QACT;IACF;AACF;AAEA,iBAAiB;AACjB,IAAI,wBAAwB;IAC1B,OAAO;IACP,KAAK;IACL,OAAO;IACP,QAAQ;IACR,MAAM;IACN,SAAS;IACT,MAAM;IACN,OAAO;IACP,aAAa;IACb,WAAW;IACX,aAAa;IACb,cAAc;IACd,YAAY;IACZ,eAAe;IACf,YAAY;IACZ,aAAa;AACf;AACA,SAAS,mBAAmB,iBAAiB,qBAAqB;IAChE,SAAS,WAAW,IAAI;QACtB,OAAO,cAAc,CAAC,KAAK;IAC7B;IACA,SAAS,SAAS,GAAG;QACnB,OAAO,CAAC,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC,IAAM,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,MAAM,IAAI,CAAC,KAAK;IACnG;IACA,IAAI;IACJ,SAAS;QACP,IAAI,YAAY;YACd,OAAO;QACT;QACA,aAAa,EAAE;QACf,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;YAC3C,WAAW,IAAI,CAAC,WAAW,WAAW,CAAC,EAAE;QAC3C;QACA,IAAI,SAAS;YAAC;YAAG;YAAI;YAAK;YAAK;YAAK;SAAI;QACxC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;gBAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;oBAC1B,WAAW,IAAI,CAAC,SAAS;wBAAC,MAAM,CAAC,EAAE;wBAAE,MAAM,CAAC,EAAE;wBAAE,MAAM,CAAC,EAAE;qBAAC;gBAC5D;YACF;QACF;QACA,IAAI,QAAQ;QACZ,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK,SAAS,GAAI;YACxC,WAAW,IAAI,CAAC,SAAS;gBAAC;gBAAO;gBAAO;aAAM;QAChD;QACA,OAAO;IACT;IACA,SAAS,WAAW,KAAK;QACvB,OAAO,eAAe,CAAC,MAAM;IAC/B;IACA,SAAS,MAAM,KAAK;QAClB,OAAQ,MAAM,IAAI;YAChB,KAAK;gBACH,OAAO,WAAW,MAAM,IAAI;YAC9B,KAAK;gBACH,OAAO,SAAS,MAAM,GAAG;YAC3B,KAAK;gBACH,OAAO,WAAW,MAAM,KAAK;QACjC;IACF;IACA,OAAO;QACL;IACF;AACF;AAEA,SAAS,sBAAsB,KAAK,EAAE,YAAY,EAAE,OAAO;IACzD,MAAM,oBAAoB,yBAAyB,OAAO;IAC1D,MAAM,QAAQ,WAAW;IACzB,MAAM,eAAe,mBACnB,OAAO,WAAW,CAChB,YAAY,GAAG,CAAC,CAAC,OAAS;YACxB;YACA,MAAM,MAAM,EAAE,CAAC,CAAC,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,WAAW,KAAK,KAAK,SAAS,CAAC,IAAI,CAAC;SAC5E;IAGL,MAAM,SAAS;IACf,OAAO,MAAM,GAAG,CACd,CAAC,OAAS,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;YACnC,IAAI;YACJ,IAAI;YACJ,IAAI,MAAM,WAAW,CAAC,GAAG,CAAC,YAAY;gBACpC,QAAQ,MAAM,UAAU,GAAG,aAAa,KAAK,CAAC,MAAM,UAAU,IAAI,MAAM,EAAE;gBAC1E,UAAU,MAAM,UAAU,GAAG,aAAa,KAAK,CAAC,MAAM,UAAU,IAAI,MAAM,EAAE;YAC9E,OAAO;gBACL,QAAQ,MAAM,UAAU,GAAG,aAAa,KAAK,CAAC,MAAM,UAAU,IAAI,MAAM,EAAE;gBAC1E,UAAU,MAAM,UAAU,GAAG,aAAa,KAAK,CAAC,MAAM,UAAU,IAAI,KAAK;YAC3E;YACA,QAAQ,uBAAuB,OAAO;YACtC,UAAU,uBAAuB,SAAS;YAC1C,IAAI,MAAM,WAAW,CAAC,GAAG,CAAC,QACxB,QAAQ,SAAS;YACnB,IAAI,YAAY,mKAAA,CAAA,YAAS,CAAC,IAAI;YAC9B,IAAI,MAAM,WAAW,CAAC,GAAG,CAAC,SACxB,aAAa,mKAAA,CAAA,YAAS,CAAC,IAAI;YAC7B,IAAI,MAAM,WAAW,CAAC,GAAG,CAAC,WACxB,aAAa,mKAAA,CAAA,YAAS,CAAC,MAAM;YAC/B,IAAI,MAAM,WAAW,CAAC,GAAG,CAAC,cACxB,aAAa,mKAAA,CAAA,YAAS,CAAC,SAAS;YAClC,IAAI,MAAM,WAAW,CAAC,GAAG,CAAC,kBACxB,aAAa,mKAAA,CAAA,YAAS,CAAC,aAAa;YACtC,OAAO;gBACL,SAAS,MAAM,KAAK;gBACpB,QAAQ,IAAI,CAAC,EAAE;gBACf,sEAAsE;gBACtE;gBACA;gBACA;YACF;QACF;AAEJ;AACA,SAAS,SAAS,KAAK;IACrB,MAAM,WAAW,MAAM,KAAK,CAAC;IAC7B,IAAI,UAAU;QACZ,IAAI,QAAQ,CAAC,EAAE,EAAE;YACf,MAAM,QAAQ,KAAK,KAAK,CAAC,OAAO,QAAQ,CAAC,QAAQ,CAAC,EAAE,EAAE,MAAM,GAAG,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;YACxF,OAAO,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,GAAG,OAAO;QAChD,OAAO,IAAI,QAAQ,CAAC,EAAE,EAAE;YACtB,OAAO,CAAC,CAAC,EAAE,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC;QAC1C,OAAO;YACL,OAAO,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,IAAM,GAAG,IAAI,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;QACxE;IACF;IACA,MAAM,cAAc,MAAM,KAAK,CAAC;IAChC,IAAI,aACF,OAAO,CAAC,IAAI,EAAE,WAAW,CAAC,EAAE,CAAC,KAAK,CAAC;IACrC,OAAO;AACT;AAEA,SAAS,iBAAiB,QAAQ,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;IACpD,MAAM,EACJ,OAAO,MAAM,EACb,OAAO,YAAY,SAAS,eAAe,EAAE,CAAC,EAAE,EACjD,GAAG;IACJ,IAAI,YAAY,SAAS,YAAY,YACnC,OAAO,WAAW,MAAM,GAAG,CAAC,CAAC,OAAS;YAAC;gBAAE,SAAS,IAAI,CAAC,EAAE;gBAAE,QAAQ,IAAI,CAAC,EAAE;YAAC;SAAE;IAC/E,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,SAAS,QAAQ,CAAC;IAC9C,IAAI,SAAS,QACX,OAAO,sBAAsB,OAAO,MAAM;IAC5C,MAAM,WAAW,SAAS,WAAW,CAAC;IACtC,IAAI,QAAQ,YAAY,EAAE;QACxB,IAAI,QAAQ,YAAY,CAAC,IAAI,KAAK,SAAS,IAAI,EAAE;YAC/C,MAAM,IAAI,uJAAA,CAAA,aAAY,CAAC,CAAC,wBAAwB,EAAE,QAAQ,YAAY,CAAC,IAAI,CAAC,qCAAqC,EAAE,SAAS,IAAI,CAAC,CAAC,CAAC;QACrI;QACA,IAAI,CAAC,QAAQ,YAAY,CAAC,MAAM,CAAC,QAAQ,CAAC,MAAM,IAAI,GAAG;YACrD,MAAM,IAAI,uJAAA,CAAA,aAAY,CAAC,CAAC,sBAAsB,EAAE,QAAQ,YAAY,CAAC,MAAM,CAAC,kCAAkC,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC;QAC/H;IACF;IACA,OAAO,kBAAkB,MAAM,UAAU,OAAO,UAAU;AAC5D;AACA,SAAS,oBAAoB,GAAG,IAAI;IAClC,IAAI,KAAK,MAAM,KAAK,GAAG;QACrB,OAAO,2BAA2B,IAAI,CAAC,EAAE;IAC3C;IACA,MAAM,CAAC,UAAU,MAAM,UAAU,CAAC,CAAC,CAAC,GAAG;IACvC,MAAM,EACJ,OAAO,MAAM,EACb,OAAO,YAAY,SAAS,eAAe,EAAE,CAAC,EAAE,EACjD,GAAG;IACJ,IAAI,YAAY,SAAS,YAAY,YACnC,MAAM,IAAI,uJAAA,CAAA,aAAY,CAAC;IACzB,IAAI,SAAS,QACX,MAAM,IAAI,uJAAA,CAAA,aAAY,CAAC;IACzB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,SAAS,QAAQ,CAAC;IAC9C,MAAM,WAAW,SAAS,WAAW,CAAC;IACtC,OAAO,IAAI,aACT,mBAAmB,MAAM,UAAU,OAAO,UAAU,SAAS,UAAU,EACvE,SAAS,IAAI,EACb,MAAM,IAAI;AAEd;AACA,SAAS,kBAAkB,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO;IAChE,MAAM,SAAS,mBAAmB,MAAM,SAAS,OAAO,UAAU;IAClE,MAAM,eAAe,IAAI,aACvB,mBAAmB,MAAM,SAAS,OAAO,UAAU,SAAS,UAAU,EACtE,QAAQ,IAAI,EACZ,MAAM,IAAI;IAEZ,yBAAyB,OAAO,MAAM,EAAE;IACxC,OAAO,OAAO,MAAM;AACtB;AACA,SAAS,mBAAmB,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO;IACjE,MAAM,oBAAoB,yBAAyB,OAAO;IAC1D,MAAM,EACJ,wBAAwB,CAAC,EACzB,oBAAoB,GAAG,EACxB,GAAG;IACJ,MAAM,QAAQ,WAAW;IACzB,IAAI,aAAa,QAAQ,YAAY,GAAG,gBAAgB,QAAQ,YAAY,EAAE,MAAM,IAAI,KAAK,mKAAA,CAAA,UAAO,GAAG,QAAQ,kBAAkB,IAAI,OAAO,mBAC1I,QAAQ,kBAAkB,EAC1B,SACA,OACA,UACA;QACE,GAAG,OAAO;QACV,cAAc,KAAK;QACnB,oBAAoB,KAAK;IAC3B,GACA,UAAU,GAAG,mKAAA,CAAA,UAAO;IACtB,IAAI,SAAS,EAAE;IACf,MAAM,QAAQ,EAAE;IAChB,IAAK,IAAI,IAAI,GAAG,MAAM,MAAM,MAAM,EAAE,IAAI,KAAK,IAAK;QAChD,MAAM,CAAC,MAAM,WAAW,GAAG,KAAK,CAAC,EAAE;QACnC,IAAI,SAAS,IAAI;YACf,SAAS,EAAE;YACX,MAAM,IAAI,CAAC,EAAE;YACb;QACF;QACA,IAAI,wBAAwB,KAAK,KAAK,MAAM,IAAI,uBAAuB;YACrE,SAAS,EAAE;YACX,MAAM,IAAI,CAAC;gBAAC;oBACV,SAAS;oBACT,QAAQ;oBACR,OAAO;oBACP,WAAW;gBACb;aAAE;YACF;QACF;QACA,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,QAAQ,kBAAkB,EAAE;YAC9B,mBAAmB,QAAQ,YAAY,CAAC,MAAM,YAAY;YAC1D,mBAAmB,iBAAiB,MAAM;YAC1C,wBAAwB;QAC1B;QACA,MAAM,SAAS,QAAQ,aAAa,CAAC,MAAM,YAAY;QACvD,MAAM,eAAe,OAAO,MAAM,CAAC,MAAM,GAAG;QAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,IAAK;YACrC,MAAM,aAAa,OAAO,MAAM,CAAC,IAAI,EAAE;YACvC,MAAM,iBAAiB,IAAI,IAAI,eAAe,OAAO,MAAM,CAAC,IAAI,IAAI,EAAE,GAAG,KAAK,MAAM;YACpF,IAAI,eAAe,gBACjB;YACF,MAAM,WAAW,OAAO,MAAM,CAAC,IAAI,IAAI,EAAE;YACzC,MAAM,QAAQ,uBACZ,QAAQ,CAAC,mKAAA,CAAA,uBAAoB,CAAC,aAAa,CAAC,UAAU,EACtD;YAEF,MAAM,YAAY,mKAAA,CAAA,uBAAoB,CAAC,YAAY,CAAC;YACpD,MAAM,QAAQ;gBACZ,SAAS,KAAK,SAAS,CAAC,YAAY;gBACpC,QAAQ,aAAa;gBACrB;gBACA;YACF;YACA,IAAI,QAAQ,kBAAkB,EAAE;gBAC9B,MAAM,yBAAyB,EAAE;gBACjC,IAAI,QAAQ,kBAAkB,KAAK,aAAa;oBAC9C,KAAK,MAAM,WAAW,MAAM,QAAQ,CAAE;wBACpC,IAAI;wBACJ,OAAQ,OAAO,QAAQ,KAAK;4BAC1B,KAAK;gCACH,YAAY,QAAQ,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,QAAU,MAAM,IAAI;gCAC9D;4BACF,KAAK;gCACH,YAAY,QAAQ,KAAK;gCACzB;4BACF;gCACE;wBACJ;wBACA,uBAAuB,IAAI,CAAC;4BAC1B,UAAU;4BACV,WAAW,UAAU,GAAG,CAAC,CAAC,WAAa,SAAS,KAAK,CAAC;wBACxD;oBACF;gBACF;gBACA,MAAM,WAAW,GAAG,EAAE;gBACtB,IAAI,SAAS;gBACb,MAAO,aAAa,SAAS,eAAgB;oBAC3C,MAAM,kBAAkB,gBAAgB,CAAC,sBAAsB;oBAC/D,MAAM,sBAAsB,KAAK,SAAS,CACxC,gBAAgB,UAAU,EAC1B,gBAAgB,QAAQ;oBAE1B,UAAU,oBAAoB,MAAM;oBACpC,MAAM,WAAW,CAAC,IAAI,CAAC;wBACrB,SAAS;wBACT,QAAQ,QAAQ,kBAAkB,KAAK,cAAc,2BACnD,gBAAgB,MAAM,IACpB,uBACF,wBACA,gBAAgB,MAAM;oBAE1B;oBACA,yBAAyB;gBAC3B;YACF;YACA,OAAO,IAAI,CAAC;QACd;QACA,MAAM,IAAI,CAAC;QACX,SAAS,EAAE;QACX,aAAa,OAAO,SAAS;IAC/B;IACA,OAAO;QACL,QAAQ;QACR;IACF;AACF;AACA,SAAS,2BAA2B,MAAM;IACxC,OAAO,OAAO,GAAG,CAAC,CAAC,QAAU,CAAC;YAAE,WAAW;QAAM,CAAC;AACpD;AACA,SAAS,uBAAuB,cAAc,EAAE,MAAM;IACpD,MAAM,SAAS,EAAE;IACjB,IAAK,IAAI,IAAI,GAAG,MAAM,OAAO,MAAM,EAAE,IAAI,KAAK,IAAK;QACjD,MAAM,QAAQ,MAAM,CAAC,EAAE;QACvB,MAAM,CAAC,EAAE,GAAG;YACV,WAAW;YACX,cAAc,kBAAkB,gBAAgB,OAAO,OAAO,KAAK,CAAC,GAAG;QACzE;IACF;IACA,OAAO;AACT;AACA,SAAS,WAAW,QAAQ,EAAE,KAAK;IACjC,OAAO,aAAa,SAAS,MAAM,SAAS,CAAC,GAAG,SAAS,MAAM,MAAM,YAAY,KAAK,CAAC,SAAS,MAAM,CAAC,KAAK;AAC9G;AACA,SAAS,QAAQ,SAAS,EAAE,KAAK,EAAE,YAAY;IAC7C,IAAI,CAAC,WAAW,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE,EAAE,QAC/C,OAAO;IACT,IAAI,sBAAsB,UAAU,MAAM,GAAG;IAC7C,IAAI,cAAc,aAAa,MAAM,GAAG;IACxC,MAAO,uBAAuB,KAAK,eAAe,EAAG;QACnD,IAAI,WAAW,SAAS,CAAC,oBAAoB,EAAE,YAAY,CAAC,YAAY,GACtE,uBAAuB;QACzB,eAAe;IACjB;IACA,IAAI,wBAAwB,CAAC,GAC3B,OAAO;IACT,OAAO;AACT;AACA,SAAS,kBAAkB,sBAAsB,EAAE,KAAK,EAAE,YAAY;IACpE,MAAM,SAAS,EAAE;IACjB,KAAK,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,IAAI,uBAAwB;QAC5D,KAAK,MAAM,kBAAkB,UAAW;YACtC,IAAI,QAAQ,gBAAgB,OAAO,eAAe;gBAChD,OAAO,IAAI,CAAC;gBACZ;YACF;QACF;IACF;IACA,OAAO;AACT;AAEA,SAAS,uBAAuB,QAAQ,EAAE,IAAI,EAAE,OAAO;IACrD,MAAM,SAAS,OAAO,OAAO,CAAC,QAAQ,MAAM,EAAE,MAAM,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,IAAM,CAAC;YAAE,OAAO,CAAC,CAAC,EAAE;YAAE,OAAO,CAAC,CAAC,EAAE;QAAC,CAAC;IAC1G,MAAM,eAAe,OAAO,GAAG,CAAC,CAAC;QAC/B,MAAM,UAAU,iBAAiB,UAAU,MAAM;YAC/C,GAAG,OAAO;YACV,OAAO,EAAE,KAAK;QAChB;QACA,MAAM,QAAQ,2BAA2B;QACzC,MAAM,QAAQ,OAAO,EAAE,KAAK,KAAK,WAAW,EAAE,KAAK,GAAG,EAAE,KAAK,CAAC,IAAI;QAClE,OAAO;YACL,QAAQ;YACR;YACA;QACF;IACF;IACA,MAAM,SAAS,0BACV,aAAa,GAAG,CAAC,CAAC,IAAM,EAAE,MAAM;IAErC,MAAM,eAAe,MAAM,CAAC,EAAE,CAAC,GAAG,CAChC,CAAC,MAAM,UAAY,KAAK,GAAG,CAAC,CAAC,QAAQ;YACnC,MAAM,cAAc;gBAClB,SAAS,OAAO,OAAO;gBACvB,UAAU,CAAC;gBACX,QAAQ,OAAO,MAAM;YACvB;YACA,IAAI,wBAAwB,WAAW,QAAQ,kBAAkB,EAAE;gBACjE,YAAY,WAAW,GAAG,OAAO,WAAW;YAC9C;YACA,OAAO,OAAO,CAAC,CAAC,GAAG;gBACjB,MAAM,EACJ,SAAS,CAAC,EACV,aAAa,EAAE,EACf,QAAQ,GAAG,EACX,GAAG,QACJ,GAAG,CAAC,CAAC,QAAQ,CAAC,SAAS;gBACxB,YAAY,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG;YACjD;YACA,OAAO;QACT;IAEF,MAAM,qBAAqB,YAAY,CAAC,EAAE,CAAC,KAAK,GAAG,IAAI,aACrD,OAAO,WAAW,CAAC,aAAa,GAAG,CAAC,CAAC,IAAM;YAAC,EAAE,KAAK;YAAE,EAAE,KAAK,EAAE,iBAAiB,EAAE,KAAK;SAAE,IACxF,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,IACxB,KAAK;IACT,IAAI,oBACF,yBAAyB,cAAc;IACzC,OAAO;AACT;AACA,SAAS,uBAAuB,GAAG,MAAM;IACvC,MAAM,YAAY,OAAO,GAAG,CAAC,IAAM,EAAE;IACrC,MAAM,QAAQ,OAAO,MAAM;IAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,IAAK;QACzC,MAAM,QAAQ,OAAO,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE;QACpC,MAAM,WAAW,UAAU,GAAG,CAAC,IAAM,EAAE;QACvC,UAAU,OAAO,CAAC,CAAC,GAAG,KAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG;QAChD,MAAM,UAAU,MAAM,GAAG,CAAC,IAAM;QAChC,MAAM,UAAU,MAAM,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE;QACrC,MAAO,QAAQ,KAAK,CAAC,CAAC,IAAM,GAAI;YAC9B,MAAM,YAAY,KAAK,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAC,IAAM,EAAE,OAAO,CAAC,MAAM;YACjE,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC9B,MAAM,QAAQ,OAAO,CAAC,EAAE;gBACxB,IAAI,MAAM,OAAO,CAAC,MAAM,KAAK,WAAW;oBACtC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;oBACjB,OAAO,CAAC,EAAE,IAAI;oBACd,OAAO,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;gBACnC,OAAO;oBACL,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;wBACf,GAAG,KAAK;wBACR,SAAS,MAAM,OAAO,CAAC,KAAK,CAAC,GAAG;oBAClC;oBACA,OAAO,CAAC,EAAE,GAAG;wBACX,GAAG,KAAK;wBACR,SAAS,MAAM,OAAO,CAAC,KAAK,CAAC;wBAC7B,QAAQ,MAAM,MAAM,GAAG;oBACzB;gBACF;YACF;QACF;IACF;IACA,OAAO;AACT;AAEA,SAAS,aAAa,QAAQ,EAAE,IAAI,EAAE,OAAO;IAC3C,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,YAAY,SAAS;QACvB,MAAM,EACJ,eAAe,OAAO,EACtB,oBAAoB,UAAU,EAC/B,GAAG;QACJ,MAAM,SAAS,OAAO,OAAO,CAAC,QAAQ,MAAM,EAAE,MAAM,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,IAAM,CAAC;gBAAE,OAAO,CAAC,CAAC,EAAE;gBAAE,OAAO,CAAC,CAAC,EAAE;YAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,KAAK,eAAe,CAAC,IAAI,EAAE,KAAK,KAAK,eAAe,IAAI;QAC3L,IAAI,OAAO,MAAM,KAAK,GACpB,MAAM,IAAI,uJAAA,CAAA,aAAY,CAAC;QACzB,MAAM,cAAc,uBAClB,UACA,MACA;QAEF,eAAe,2BAA2B;QAC1C,IAAI,gBAAgB,CAAC,OAAO,IAAI,CAAC,CAAC,IAAM,EAAE,KAAK,KAAK,eAClD,MAAM,IAAI,uJAAA,CAAA,aAAY,CAAC,CAAC,sDAAsD,EAAE,aAAa,EAAE,CAAC;QAClG,MAAM,YAAY,OAAO,GAAG,CAAC,CAAC,IAAM,SAAS,QAAQ,CAAC,EAAE,KAAK;QAC7D,MAAM,cAAc,OAAO,GAAG,CAAC,CAAC,IAAM,EAAE,KAAK;QAC7C,SAAS,YAAY,GAAG,CAAC,CAAC,OAAS,KAAK,GAAG,CAAC,CAAC,QAAU,kBAAkB,OAAO,aAAa,mBAAmB;QAChH,IAAI,cACF,yBAAyB,QAAQ;QACnC,MAAM,yBAAyB,OAAO,GAAG,CAAC,CAAC,IAAM,yBAAyB,EAAE,KAAK,EAAE;QACnF,KAAK,OAAO,GAAG,CAAC,CAAC,GAAG,MAAQ,CAAC,QAAQ,KAAK,eAAe,KAAK,GAAG,oBAAoB,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,uBAAuB,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,sBAAsB,CAAC,IAAI,KAAK,SAAS,GAAG,IAAI,CAAC;QAC/L,KAAK,OAAO,GAAG,CAAC,CAAC,GAAG,MAAQ,CAAC,QAAQ,KAAK,eAAe,KAAK,GAAG,oBAAoB,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,sBAAsB,CAAC,IAAI,KAAK,SAAS,GAAG,IAAI,CAAC;QAClM,YAAY,CAAC,aAAa,EAAE,UAAU,GAAG,CAAC,CAAC,IAAM,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM;QACpE,YAAY,eAAe,KAAK,IAAI;YAAC;YAAI;SAAG,CAAC,IAAI,CAAC;IACpD,OAAO,IAAI,WAAW,SAAS;QAC7B,MAAM,oBAAoB,yBAAyB,QAAQ,KAAK,EAAE;QAClE,SAAS,iBACP,UACA,MACA;QAEF,MAAM,SAAS,SAAS,QAAQ,CAAC,QAAQ,KAAK;QAC9C,KAAK,uBAAuB,OAAO,EAAE,EAAE;QACvC,KAAK,uBAAuB,OAAO,EAAE,EAAE;QACvC,YAAY,OAAO,IAAI;QACvB,eAAe,2BAA2B;IAC5C,OAAO;QACL,MAAM,IAAI,uJAAA,CAAA,aAAY,CAAC;IACzB;IACA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAEA,SAAS,WAAW,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,qBAAqB;IAChE,MAAM,CAAC;IACP;IACA,YAAY,CAAC,OAAO,WAAa,WAAW,UAAU,OAAO;IAC7D,cAAc,CAAC,OAAO,WAAa,aAAa,UAAU,OAAO;AACnE,CAAC;IACC,IAAI,QAAQ;IACZ,KAAK,MAAM,eAAe,gBAAgB,SACxC,QAAQ,YAAY,UAAU,EAAE,KAAK,oBAAoB,OAAO,YAAY;IAC9E,IAAI,EACF,MAAM,EACN,EAAE,EACF,EAAE,EACF,SAAS,EACT,SAAS,EACT,YAAY,EACb,GAAG,aAAa,UAAU,OAAO;IAClC,MAAM,EACJ,mBAAmB,IAAI,EACvB,uBAAuB,KAAK,EAC7B,GAAG;IACJ,IAAI,qBAAqB,MACvB,SAAS,sBAAsB;SAC5B,IAAI,qBAAqB,SAC5B,SAAS,sBAAsB;IACjC,IAAI,sBAAsB;QACxB,SAAS,0BAA0B;IACrC;IACA,MAAM,gBAAgB;QACpB,GAAG,kBAAkB;QACrB,IAAI,UAAS;YACX,OAAO;QACT;IACF;IACA,KAAK,MAAM,eAAe,gBAAgB,SACxC,SAAS,YAAY,MAAM,EAAE,KAAK,eAAe,WAAW;IAC9D,OAAO,aACL,QACA;QACE,GAAG,OAAO;QACV;QACA;QACA;QACA;IACF,GACA,eACA;AAEJ;AACA,SAAS,aAAa,MAAM,EAAE,OAAO,EAAE,kBAAkB,EAAE,eAAe,2BAA2B,OAAO;IAC1G,MAAM,eAAe,gBAAgB;IACrC,MAAM,QAAQ,EAAE;IAChB,MAAM,OAAO;QACX,MAAM;QACN,UAAU,EAAE;IACd;IACA,MAAM,EACJ,YAAY,SAAS,EACrB,WAAW,GAAG,EACf,GAAG;IACJ,IAAI,UAAU;QACZ,MAAM;QACN,SAAS;QACT,YAAY;YACV,OAAO,CAAC,MAAM,EAAE,QAAQ,SAAS,IAAI,IAAI;YACzC,OAAO,QAAQ,SAAS,IAAI,CAAC,iBAAiB,EAAE,QAAQ,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,EAAE;YAChF,GAAG,aAAa,SAAS,YAAY,OAAO;gBAC1C,UAAU,SAAS,QAAQ;YAC7B,IAAI,CAAC,CAAC;YACN,GAAG,OAAO,WAAW,CACnB,MAAM,IAAI,CACR,OAAO,OAAO,CAAC,QAAQ,IAAI,IAAI,CAAC,IAChC,MAAM,CAAC,CAAC,CAAC,IAAI,GAAK,CAAC,IAAI,UAAU,CAAC,MACrC;QACH;QACA,UAAU,EAAE;IACd;IACA,IAAI,WAAW;QACb,MAAM;QACN,SAAS;QACT,YAAY,CAAC;QACb,UAAU;IACZ;IACA,MAAM,YAAY,EAAE;IACpB,MAAM,UAAU;QACd,GAAG,kBAAkB;QACrB;QACA;QACA,IAAI,UAAS;YACX,OAAO,mBAAmB,MAAM;QAClC;QACA,IAAI,UAAS;YACX,OAAO;QACT;QACA,IAAI,WAAU;YACZ,OAAO;QACT;QACA,IAAI,QAAO;YACT,OAAO;QACT;QACA,IAAI,OAAM;YACR,OAAO;QACT;QACA,IAAI,QAAO;YACT,OAAO;QACT;QACA,IAAI,SAAQ;YACV,OAAO;QACT;IACF;IACA,OAAO,OAAO,CAAC,CAAC,MAAM;QACpB,IAAI,KAAK;YACP,IAAI,cAAc,UAChB,KAAK,QAAQ,CAAC,IAAI,CAAC;gBAAE,MAAM;gBAAW,SAAS;gBAAM,YAAY,CAAC;gBAAG,UAAU,EAAE;YAAC;iBAC/E,IAAI,cAAc,WACrB,MAAM,IAAI,CAAC;gBAAE,MAAM;gBAAQ,OAAO;YAAK;QAC3C;QACA,IAAI,WAAW;YACb,MAAM;YACN,SAAS;YACT,YAAY;gBAAE,OAAO;YAAO;YAC5B,UAAU,EAAE;QACd;QACA,IAAI,MAAM;QACV,KAAK,MAAM,SAAS,KAAM;YACxB,IAAI,YAAY;gBACd,MAAM;gBACN,SAAS;gBACT,YAAY;oBACV,GAAG,MAAM,SAAS;gBACpB;gBACA,UAAU;oBAAC;wBAAE,MAAM;wBAAQ,OAAO,MAAM,OAAO;oBAAC;iBAAE;YACpD;YACA,MAAM,QAAQ,oBAAoB,MAAM,SAAS,IAAI,oBAAoB;YACzE,IAAI,OACF,UAAU,UAAU,CAAC,KAAK,GAAG;YAC/B,KAAK,MAAM,eAAe,aACxB,YAAY,aAAa,MAAM,KAAK,SAAS,WAAW,MAAM,GAAG,KAAK,UAAU,UAAU;YAC5F,IAAI,cAAc,UAChB,KAAK,QAAQ,CAAC,IAAI,CAAC;iBAChB,IAAI,cAAc,WACrB,SAAS,QAAQ,CAAC,IAAI,CAAC;YACzB,OAAO,MAAM,OAAO,CAAC,MAAM;QAC7B;QACA,IAAI,cAAc,WAAW;YAC3B,KAAK,MAAM,eAAe,aACxB,WAAW,aAAa,MAAM,KAAK,SAAS,UAAU,MAAM,MAAM;YACpE,UAAU,IAAI,CAAC;YACf,MAAM,IAAI,CAAC;QACb;IACF;IACA,IAAI,cAAc,WAAW;QAC3B,KAAK,MAAM,eAAe,aACxB,WAAW,aAAa,MAAM,KAAK,SAAS,aAAa;QAC3D,QAAQ,QAAQ,CAAC,IAAI,CAAC;QACtB,KAAK,MAAM,eAAe,aACxB,UAAU,aAAa,KAAK,KAAK,SAAS,YAAY;QACxD,KAAK,QAAQ,CAAC,IAAI,CAAC;IACrB;IACA,IAAI,SAAS;IACb,KAAK,MAAM,eAAe,aACxB,SAAS,aAAa,MAAM,KAAK,SAAS,WAAW;IACvD,IAAI,cACF,yBAAyB,QAAQ;IACnC,OAAO;AACT;AACA,SAAS,sBAAsB,MAAM;IACnC,OAAO,OAAO,GAAG,CAAC,CAAC;QACjB,MAAM,UAAU,EAAE;QAClB,IAAI,iBAAiB;QACrB,IAAI,cAAc;QAClB,KAAK,OAAO,CAAC,CAAC,OAAO;YACnB,MAAM,cAAc,MAAM,SAAS,IAAI,CAAC,MAAM,SAAS,GAAG,mKAAA,CAAA,YAAS,CAAC,SAAS,IAAI,MAAM,SAAS,GAAG,mKAAA,CAAA,YAAS,CAAC,aAAa;YAC1H,MAAM,aAAa,CAAC;YACpB,IAAI,cAAc,MAAM,OAAO,CAAC,KAAK,CAAC,YAAY,IAAI,CAAC,MAAM,EAAE,EAAE;gBAC/D,IAAI,CAAC,aACH,cAAc,MAAM,MAAM;gBAC5B,kBAAkB,MAAM,OAAO;YACjC,OAAO;gBACL,IAAI,gBAAgB;oBAClB,IAAI,YAAY;wBACd,QAAQ,IAAI,CAAC;4BACX,GAAG,KAAK;4BACR,QAAQ;4BACR,SAAS,iBAAiB,MAAM,OAAO;wBACzC;oBACF,OAAO;wBACL,QAAQ,IAAI,CACV;4BACE,SAAS;4BACT,QAAQ;wBACV,GACA;oBAEJ;oBACA,cAAc;oBACd,iBAAiB;gBACnB,OAAO;oBACL,QAAQ,IAAI,CAAC;gBACf;YACF;QACF;QACA,OAAO;IACT;AACF;AACA,SAAS,sBAAsB,MAAM;IACnC,OAAO,OAAO,GAAG,CAAC,CAAC;QACjB,OAAO,KAAK,OAAO,CAAC,CAAC;YACnB,IAAI,MAAM,OAAO,CAAC,KAAK,CAAC,UACtB,OAAO;YACT,MAAM,QAAQ,MAAM,OAAO,CAAC,KAAK,CAAC;YAClC,IAAI,CAAC,OACH,OAAO;YACT,MAAM,GAAG,SAAS,SAAS,SAAS,GAAG;YACvC,IAAI,CAAC,WAAW,CAAC,UACf,OAAO;YACT,MAAM,WAAW;gBAAC;oBAChB,GAAG,KAAK;oBACR,QAAQ,MAAM,MAAM,GAAG,QAAQ,MAAM;oBACrC;gBACF;aAAE;YACF,IAAI,SAAS;gBACX,SAAS,OAAO,CAAC;oBACf,SAAS;oBACT,QAAQ,MAAM,MAAM;gBACtB;YACF;YACA,IAAI,UAAU;gBACZ,SAAS,IAAI,CAAC;oBACZ,SAAS;oBACT,QAAQ,MAAM,MAAM,GAAG,QAAQ,MAAM,GAAG,QAAQ,MAAM;gBACxD;YACF;YACA,OAAO;QACT;IACF;AACF;AACA,SAAS,0BAA0B,MAAM;IACvC,OAAO,OAAO,GAAG,CAAC,CAAC;QACjB,MAAM,UAAU,EAAE;QAClB,KAAK,MAAM,SAAS,KAAM;YACxB,IAAI,QAAQ,MAAM,KAAK,GAAG;gBACxB,QAAQ,IAAI,CAAC;oBAAE,GAAG,KAAK;gBAAC;gBACxB;YACF;YACA,MAAM,YAAY,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;YAC7C,MAAM,YAAY,UAAU,SAAS,IAAI,oBAAoB,oBAAoB;YACjF,MAAM,eAAe,MAAM,SAAS,IAAI,oBAAoB,oBAAoB;YAChF,MAAM,kBAAkB,UAAU,SAAS,IAAI,CAAC,UAAU,SAAS,GAAG,mKAAA,CAAA,YAAS,CAAC,SAAS,IAAI,UAAU,SAAS,GAAG,mKAAA,CAAA,YAAS,CAAC,aAAa;YAC1I,MAAM,cAAc,MAAM,SAAS,IAAI,CAAC,MAAM,SAAS,GAAG,mKAAA,CAAA,YAAS,CAAC,SAAS,IAAI,MAAM,SAAS,GAAG,mKAAA,CAAA,YAAS,CAAC,aAAa;YAC1H,IAAI,CAAC,mBAAmB,CAAC,eAAe,cAAc,cAAc;gBAClE,UAAU,OAAO,IAAI,MAAM,OAAO;YACpC,OAAO;gBACL,QAAQ,IAAI,CAAC;oBAAE,GAAG,KAAK;gBAAC;YAC1B;QACF;QACA,OAAO;IACT;AACF;AAEA,MAAM,aAAa,6JAAA,CAAA,SAAM;AACzB,SAAS,WAAW,QAAQ,EAAE,IAAI,EAAE,OAAO;IACzC,MAAM,UAAU;QACd,MAAM,CAAC;QACP;QACA,YAAY,CAAC,OAAO,WAAa,WAAW,UAAU,OAAO;QAC7D,cAAc,CAAC,OAAO,WAAa,aAAa,UAAU,OAAO;IACnE;IACA,IAAI,SAAS,WAAW,WAAW,UAAU,MAAM,SAAS;IAC5D,KAAK,MAAM,eAAe,gBAAgB,SACxC,SAAS,YAAY,WAAW,EAAE,KAAK,SAAS,QAAQ,YAAY;IACtE,OAAO;AACT;AAEA,MAAM,4BAA4B;IAAE,OAAO;IAAW,MAAM;AAAU;AACtE,MAAM,4BAA4B;IAAE,OAAO;IAAW,MAAM;AAAU;AACtE,MAAM,eAAe;AACrB,SAAS,eAAe,QAAQ;IAC9B,IAAI,UAAU,CAAC,aAAa,EAC1B,OAAO;IACT,MAAM,QAAQ;QACZ,GAAG,QAAQ;IACb;IACA,IAAI,MAAM,WAAW,IAAI,CAAC,MAAM,QAAQ,EAAE;QACxC,MAAM,QAAQ,GAAG,MAAM,WAAW;QAClC,OAAO,MAAM,WAAW;IAC1B;IACA,MAAM,IAAI,KAAK;IACf,MAAM,iBAAiB,GAAG;QAAE,GAAG,MAAM,iBAAiB;IAAC;IACvD,MAAM,QAAQ,KAAK,EAAE;IACrB,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG;IACjB,IAAI,CAAC,MAAM,CAAC,IAAI;QACd,MAAM,gBAAgB,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAM,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,KAAK;QAC9F,IAAI,eAAe,UAAU,YAC3B,KAAK,cAAc,QAAQ,CAAC,UAAU;QACxC,IAAI,eAAe,UAAU,YAC3B,KAAK,cAAc,QAAQ,CAAC,UAAU;QACxC,IAAI,CAAC,MAAM,OAAO,QAAQ,CAAC,oBAAoB,EAC7C,KAAK,MAAM,MAAM,CAAC,oBAAoB;QACxC,IAAI,CAAC,MAAM,OAAO,QAAQ,CAAC,oBAAoB,EAC7C,KAAK,MAAM,MAAM,CAAC,oBAAoB;QACxC,IAAI,CAAC,IACH,KAAK,MAAM,IAAI,KAAK,UAAU,0BAA0B,KAAK,GAAG,0BAA0B,IAAI;QAChG,IAAI,CAAC,IACH,KAAK,MAAM,IAAI,KAAK,UAAU,0BAA0B,KAAK,GAAG,0BAA0B,IAAI;QAChG,MAAM,EAAE,GAAG;QACX,MAAM,EAAE,GAAG;IACb;IACA,IAAI,CAAC,CAAC,MAAM,QAAQ,CAAC,EAAE,IAAI,MAAM,QAAQ,CAAC,EAAE,CAAC,QAAQ,IAAI,CAAC,MAAM,QAAQ,CAAC,EAAE,CAAC,KAAK,GAAG;QAClF,MAAM,QAAQ,CAAC,OAAO,CAAC;YACrB,UAAU;gBACR,YAAY,MAAM,EAAE;gBACpB,YAAY,MAAM,EAAE;YACtB;QACF;IACF;IACA,IAAI,mBAAmB;IACvB,MAAM,iBAAiB,aAAa,GAAG,IAAI;IAC3C,SAAS,oBAAoB,KAAK;QAChC,IAAI,eAAe,GAAG,CAAC,QACrB,OAAO,eAAe,GAAG,CAAC;QAC5B,oBAAoB;QACpB,MAAM,MAAM,CAAC,CAAC,EAAE,iBAAiB,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,KAAK,WAAW,IAAI;QAC9E,IAAI,MAAM,iBAAiB,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EACtC,OAAO,oBAAoB;QAC7B,eAAe,GAAG,CAAC,OAAO;QAC1B,OAAO;IACT;IACA,MAAM,QAAQ,GAAG,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC;QACnC,MAAM,YAAY,QAAQ,QAAQ,EAAE,cAAc,CAAC,QAAQ,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC;QAC1F,MAAM,YAAY,QAAQ,QAAQ,EAAE,cAAc,CAAC,QAAQ,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC;QAC1F,IAAI,CAAC,aAAa,CAAC,WACjB,OAAO;QACT,MAAM,QAAQ;YACZ,GAAG,OAAO;YACV,UAAU;gBACR,GAAG,QAAQ,QAAQ;YACrB;QACF;QACA,IAAI,WAAW;YACb,MAAM,cAAc,oBAAoB,QAAQ,QAAQ,CAAC,UAAU;YACnE,MAAM,iBAAiB,CAAC,YAAY,GAAG,QAAQ,QAAQ,CAAC,UAAU;YAClE,MAAM,QAAQ,CAAC,UAAU,GAAG;QAC9B;QACA,IAAI,WAAW;YACb,MAAM,cAAc,oBAAoB,QAAQ,QAAQ,CAAC,UAAU;YACnE,MAAM,iBAAiB,CAAC,YAAY,GAAG,QAAQ,QAAQ,CAAC,UAAU;YAClE,MAAM,QAAQ,CAAC,UAAU,GAAG;QAC9B;QACA,OAAO;IACT;IACA,KAAK,MAAM,OAAO,OAAO,IAAI,CAAC,MAAM,MAAM,IAAI,CAAC,GAAI;QACjD,IAAI,QAAQ,uBAAuB,QAAQ,uBAAuB,IAAI,UAAU,CAAC,kBAAkB;YACjG,IAAI,CAAC,MAAM,MAAM,CAAC,IAAI,EAAE,WAAW,MAAM;gBACvC,MAAM,cAAc,oBAAoB,MAAM,MAAM,CAAC,IAAI;gBACzD,MAAM,iBAAiB,CAAC,YAAY,GAAG,MAAM,MAAM,CAAC,IAAI;gBACxD,MAAM,MAAM,CAAC,IAAI,GAAG;YACtB;QACF;IACF;IACA,OAAO,cAAc,CAAC,OAAO,cAAc;QACzC,YAAY;QACZ,UAAU;QACV,OAAO;IACT;IACA,OAAO;AACT;AAEA,eAAe,aAAa,KAAK;IAC/B,OAAO,MAAM,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,QAAQ,GAAG,CAC1C,MAAM,MAAM,CAAC,CAAC,IAAM,CAAC,cAAc,IAAI,GAAG,CAAC,OAAO,OAAS,MAAM,gBAAgB,MAAM,IAAI,CAAC,CAAC,IAAM,MAAM,OAAO,CAAC,KAAK,IAAI;gBAAC;aAAE,GAC9H,EAAE,IAAI;AACT;AACA,eAAe,cAAc,MAAM;IACjC,MAAM,WAAW,MAAM,QAAQ,GAAG,CAChC,OAAO,GAAG,CACR,OAAO,QAAU,eAAe,SAAS,OAAO,eAAe,MAAM,gBAAgB;IAGzF,OAAO,SAAS,MAAM,CAAC,CAAC,IAAM,CAAC,CAAC;AAClC;AAEA,IAAI,mBAAmB;AACvB,IAAI,aAAa;AACjB,SAAS,0BAA0B,kBAAkB,IAAI,EAAE,YAAY,KAAK;IAC1E,mBAAmB;IACnB,aAAa;AACf;AACA,SAAS,eAAe,OAAO,EAAE,UAAU,CAAC;IAC1C,IAAI,CAAC,kBACH;IACF,IAAI,OAAO,qBAAqB,YAAY,UAAU,kBACpD;IACF,IAAI,YAAY;QACd,MAAM,IAAI,MAAM,CAAC,mBAAmB,EAAE,SAAS;IACjD,OAAO;QACL,QAAQ,KAAK,CAAC,CAAC,mBAAmB,EAAE,SAAS;IAC/C;AACF;AAEA,MAAM,mBAAmB;IACvB,YAAY,OAAO,CAAE;QACnB,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEA,MAAM,iBAAiB,mKAAA,CAAA,WAAU;IAC/B,YAAY,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC,CAAE;QACnD,KAAK,CAAC;QACN,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAM,IAAI,CAAC,SAAS,CAAC;QACvC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM;IAChC;IACA,kBAAkB,aAAa,GAAG,IAAI,MAAM;IAC5C,oBAAoB,aAAa,GAAG,IAAI,MAAM;IAC9C,WAAW,aAAa,GAAG,IAAI,MAAM;IACrC,aAAa,aAAa,GAAG,IAAI,MAAM;IACvC,sBAAsB,aAAa,GAAG,IAAI,UAAU;IACpD,qBAAqB,KAAK;IAC1B,wBAAwB,KAAK;IAC7B,SAAS,KAAK,EAAE;QACd,IAAI,OAAO,UAAU,UACnB,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;aAEhC,OAAO,IAAI,CAAC,SAAS,CAAC;IAC1B;IACA,UAAU,KAAK,EAAE;QACf,MAAM,SAAS,eAAe;QAC9B,IAAI,OAAO,IAAI,EAAE;YACf,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,IAAI,EAAE;YACtC,IAAI,CAAC,kBAAkB,GAAG;QAC5B;QACA,OAAO;IACT;IACA,kBAAkB;QAChB,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAC1B,IAAI,CAAC,kBAAkB,GAAG;eAAI,IAAI,CAAC,eAAe,CAAC,IAAI;SAAG;QAC5D,OAAO,IAAI,CAAC,kBAAkB;IAChC;IACA,2GAA2G;IAC3G,2EAA2E;IAC3E,EAAE;IACF,iGAAiG;IACjG,mDAAmD;IACnD,SAAS,KAAK,EAAE;QACd,IAAI,gBAAgB,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC;QACjD,IAAI,CAAC,eAAe;YAClB,gBAAgB,mKAAA,CAAA,QAAK,CAAC,kBAAkB,CAAC;YACzC,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,OAAO;QACtC;QACA,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC;IAC9B;IACA,WAAW,IAAI,EAAE;QACf,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YACrB,MAAM,WAAW,aAAa,GAAG,IAAI,IAAI;gBAAC;aAAK;YAC/C,MAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAE;gBACxB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;gBACxB,IAAI,SAAS,GAAG,CAAC,OACf,MAAM,IAAI,WAAW,CAAC,iBAAiB,EAAE,MAAM,IAAI,CAAC,UAAU,IAAI,CAAC,QAAQ,IAAI,EAAE,KAAK,EAAE,CAAC;gBAC3F,SAAS,GAAG,CAAC;YACf;QACF;QACA,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC;IACpC;IACA,aAAa,IAAI,EAAE;QACjB,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI,GAC3B;QACF,MAAM,mBAAmB,IAAI,IAC3B;eAAI,IAAI,CAAC,QAAQ,CAAC,MAAM;SAAG,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,iBAAiB,EAAE,SAAS,KAAK,IAAI;QAEnF,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;QAC3B,MAAM,gBAAgB;YACpB,0BAA0B,KAAK,wBAAwB,IAAI;gBAAC;aAAI;YAChE,4BAA4B,KAAK,0BAA0B,IAAI,EAAE;QACnE;QACA,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;QACpD,MAAM,IAAI,IAAI,CAAC,4BAA4B,CAAC,KAAK,SAAS,EAAE,GAAG;QAC/D,EAAE,IAAI,GAAG,KAAK,IAAI;QAClB,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;QACtC,IAAI,KAAK,OAAO,EAAE;YAChB,KAAK,OAAO,CAAC,OAAO,CAAC,CAAC;gBACpB,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,IAAI;YAChC;QACF;QACA,IAAI,CAAC,qBAAqB,GAAG;QAC7B,IAAI,iBAAiB,IAAI,EAAE;YACzB,KAAK,MAAM,KAAK,iBAAkB;gBAChC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,EAAE,IAAI;gBACpC,IAAI,CAAC,qBAAqB,GAAG;gBAC7B,IAAI,CAAC,aAAa,EAAE,oBAAoB,OAAO,EAAE,SAAS;gBAC1D,IAAI,CAAC,aAAa,EAAE,WAAW,OAAO,EAAE,SAAS;gBACjD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,IAAI;YAC5C;QACF;IACF;IACA,UAAU;QACR,KAAK,CAAC;QACN,IAAI,CAAC,eAAe,CAAC,KAAK;QAC1B,IAAI,CAAC,iBAAiB,CAAC,KAAK;QAC5B,IAAI,CAAC,QAAQ,CAAC,KAAK;QACnB,IAAI,CAAC,UAAU,CAAC,KAAK;QACrB,IAAI,CAAC,kBAAkB,GAAG;IAC5B;IACA,cAAc,KAAK,EAAE;QACnB,KAAK,MAAM,QAAQ,MACjB,IAAI,CAAC,wBAAwB,CAAC;QAChC,MAAM,kBAAkB,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO;QAC1D,MAAM,eAAe,gBAAgB,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,GAAK,CAAC;QAC5D,IAAI,aAAa,MAAM,EAAE;YACvB,MAAM,aAAa,gBAAgB,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,GAAK,QAAQ,KAAK,aAAa,EAAE,KAAK,CAAC,IAAM,aAAa,GAAG,CAAC,CAAC,CAAC,KAAK,GAAK,MAAM,QAAQ,CAAC,KAAK,MAAM,CAAC,CAAC,OAAS,CAAC,aAAa,QAAQ,CAAC;YAC1L,MAAM,IAAI,WAAW,CAAC,kBAAkB,EAAE,aAAa,GAAG,CAAC,CAAC,CAAC,KAAK,GAAK,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,MAAM,cAAc,EAAE,WAAW,GAAG,CAAC,CAAC,CAAC,KAAK,GAAK,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,CAAC,OAAO;QACzK;QACA,KAAK,MAAM,CAAC,GAAG,KAAK,IAAI,gBACtB,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;QAC7B,KAAK,MAAM,CAAC,GAAG,KAAK,IAAI,gBACtB,IAAI,CAAC,YAAY,CAAC;IACtB;IACA,qBAAqB;QACnB,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YAC/B,IAAI,CAAC,qBAAqB,GAAG;mBACxB,aAAa,GAAG,IAAI,IAAI;uBAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI;uBAAO,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM;iBAAE;aAC3F;QACH;QACA,OAAO,IAAI,CAAC,qBAAqB;IACnC;IACA,yBAAyB,IAAI,EAAE;QAC7B,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;QAC7B,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;QAC/B,IAAI,KAAK,aAAa,EAAE;YACtB,KAAK,MAAM,gBAAgB,KAAK,aAAa,CAC3C,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,cAAc,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;QACxD;IACF;AACF;AAEA,MAAM;IACJ,SAAS,aAAa,GAAG,IAAI,MAAM;IACnC,eAAe,aAAa,GAAG,IAAI,MAAM;IACzC,cAAc,aAAa,GAAG,IAAI,MAAM;IACxC,SAAS;IACT,YAAY,MAAM,EAAE,KAAK,CAAE;QACzB,IAAI,CAAC,QAAQ,GAAG;YACd,mBAAmB,CAAC,WAAa,OAAO,aAAa,CAAC;YACtD,kBAAkB,CAAC,IAAM,OAAO,YAAY,CAAC;QAC/C;QACA,MAAM,OAAO,CAAC,CAAC,IAAM,IAAI,CAAC,WAAW,CAAC;IACxC;IACA,IAAI,UAAU;QACZ,OAAO,IAAI,CAAC,QAAQ;IACtB;IACA,oBAAoB,aAAa,EAAE;QACjC,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;IACzB;IACA,YAAY,SAAS,EAAE;QACrB,OAAO,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;IAC/B;IACA,YAAY,CAAC,EAAE;QACb,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,IAAI,EAAE;QACxB,IAAI,EAAE,OAAO,EAAE;YACb,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC;gBACjB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG;YACrB;QACF;QACA,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE,SAAS,EAAE;QACnC,IAAI,EAAE,QAAQ,EAAE;YACd,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;gBAClB,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IACxB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE;gBAC5B,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,SAAS;YAC1C;QACF;IACF;IACA,cAAc,SAAS,EAAE;QACvB,MAAM,aAAa,UAAU,KAAK,CAAC;QACnC,IAAI,aAAa,EAAE;QACnB,IAAK,IAAI,IAAI,GAAG,KAAK,WAAW,MAAM,EAAE,IAAK;YAC3C,MAAM,eAAe,WAAW,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC;YACjD,aAAa;mBAAI;mBAAe,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,iBAAiB,EAAE;aAAC;QAC3E;QACA,OAAO;IACT;AACF;AAEA,IAAI,iBAAiB;AACrB,SAAS,wBAAwB,OAAO;IACtC,kBAAkB;IAClB,IAAI,QAAQ,QAAQ,KAAK,SAAS,kBAAkB,MAAM,iBAAiB,OAAO,GAChF,QAAQ,IAAI,CAAC,CAAC,QAAQ,EAAE,eAAe,4MAA4M,CAAC;IACtP,IAAI,aAAa;IACjB,IAAI,CAAC,QAAQ,MAAM,EACjB,MAAM,IAAI,WAAW;IACvB,MAAM,QAAQ,CAAC,QAAQ,KAAK,IAAI,EAAE,EAAE,IAAI,CAAC;IACzC,MAAM,SAAS,CAAC,QAAQ,MAAM,IAAI,EAAE,EAAE,IAAI,CAAC,GAAG,GAAG,CAAC;IAClD,MAAM,WAAW,IAAI,SAAS,QAAQ,MAAM,EAAE;IAC9C,MAAM,YAAY,IAAI,SAAS,UAAU,QAAQ,OAAO,QAAQ,SAAS;IACzE,IAAI;IACJ,SAAS,YAAY,IAAI;QACvB;QACA,MAAM,QAAQ,UAAU,UAAU,CAAC,OAAO,SAAS,WAAW,OAAO,KAAK,IAAI;QAC9E,IAAI,CAAC,OACH,MAAM,IAAI,WAAW,CAAC,WAAW,EAAE,KAAK,2CAA2C,CAAC;QACtF,OAAO;IACT;IACA,SAAS,SAAS,IAAI;QACpB,IAAI,SAAS,QACX,OAAO;YAAE,IAAI;YAAI,IAAI;YAAI,MAAM;YAAQ,UAAU,EAAE;YAAE,MAAM;QAAO;QACpE;QACA,MAAM,SAAS,UAAU,QAAQ,CAAC;QAClC,IAAI,CAAC,QACH,MAAM,IAAI,WAAW,CAAC,QAAQ,EAAE,KAAK,2CAA2C,CAAC;QACnF,OAAO;IACT;IACA,SAAS,SAAS,IAAI;QACpB;QACA,MAAM,QAAQ,SAAS;QACvB,IAAI,eAAe,MAAM;YACvB,UAAU,QAAQ,CAAC;YACnB,aAAa;QACf;QACA,MAAM,WAAW,UAAU,WAAW;QACtC,OAAO;YACL;YACA;QACF;IACF;IACA,SAAS;QACP;QACA,OAAO,UAAU,eAAe;IAClC;IACA,SAAS;QACP;QACA,OAAO,UAAU,kBAAkB;IACrC;IACA,SAAS,iBAAiB,GAAG,MAAM;QACjC;QACA,UAAU,aAAa,CAAC,OAAO,IAAI,CAAC;IACtC;IACA,eAAe,aAAa,GAAG,MAAM;QACnC,OAAO,iBAAiB,MAAM,aAAa;IAC7C;IACA,SAAS,cAAc,GAAG,OAAO;QAC/B;QACA,KAAK,MAAM,SAAS,QAAQ,IAAI,CAAC,GAAI;YACnC,UAAU,SAAS,CAAC;QACtB;IACF;IACA,eAAe,UAAU,GAAG,OAAO;QACjC;QACA,OAAO,cAAc,MAAM,cAAc;IAC3C;IACA,SAAS;QACP,IAAI,YACF,MAAM,IAAI,WAAW;IACzB;IACA,SAAS;QACP,IAAI,YACF;QACF,aAAa;QACb,UAAU,OAAO;QACjB,kBAAkB;IACpB;IACA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,CAAC,OAAO,OAAO,CAAC,EAAE;IACpB;AACF;AAEA,eAAe,oBAAoB,OAAO;IACxC,IAAI,CAAC,QAAQ,MAAM,EAAE;QACnB,eAAe;IACjB;IACA,MAAM,CACJ,QACA,OACA,OACD,GAAG,MAAM,QAAQ,GAAG,CAAC;QACpB,cAAc,QAAQ,MAAM,IAAI,EAAE;QAClC,aAAa,QAAQ,KAAK,IAAI,EAAE;QAChC,QAAQ,MAAM;KACf;IACD,OAAO,wBAAwB;QAC7B,GAAG,OAAO;QACV;QACA;QACA;IACF;AACF;AAEA,eAAe,sBAAsB,OAAO;IAC1C,MAAM,WAAW,MAAM,oBAAoB;IAC3C,OAAO;QACL,qBAAqB,CAAC,GAAG,OAAS,oBAAoB,aAAa;QACnE,kBAAkB,CAAC,MAAM,WAAa,iBAAiB,UAAU,MAAM;QACvE,wBAAwB,CAAC,MAAM,WAAa,uBAAuB,UAAU,MAAM;QACnF,cAAc,CAAC,MAAM,WAAa,aAAa,UAAU,MAAM;QAC/D,YAAY,CAAC,MAAM,WAAa,WAAW,UAAU,MAAM;QAC3D,YAAY,CAAC,MAAM,WAAa,WAAW,UAAU,MAAM;QAC3D,qBAAqB,IAAM,CAAC,CAAC,CAAC;QAC9B,kBAAkB,IAAM,CAAC,CAAC,CAAC;QAC3B,GAAG,QAAQ;QACX,oBAAoB,IAAM;IAC5B;AACF;AACA,SAAS,0BAA0B,OAAO;IACxC,MAAM,WAAW,wBAAwB;IACzC,OAAO;QACL,qBAAqB,CAAC,GAAG,OAAS,oBAAoB,aAAa;QACnE,kBAAkB,CAAC,MAAM,WAAa,iBAAiB,UAAU,MAAM;QACvE,wBAAwB,CAAC,MAAM,WAAa,uBAAuB,UAAU,MAAM;QACnF,cAAc,CAAC,MAAM,WAAa,aAAa,UAAU,MAAM;QAC/D,YAAY,CAAC,MAAM,WAAa,WAAW,UAAU,MAAM;QAC3D,YAAY,CAAC,MAAM,WAAa,WAAW,UAAU,MAAM;QAC3D,qBAAqB,IAAM,CAAC,CAAC,CAAC;QAC9B,kBAAkB,IAAM,CAAC,CAAC,CAAC;QAC3B,GAAG,QAAQ;QACX,oBAAoB,IAAM;IAC5B;AACF;AACA,SAAS,6BAA6B,iBAAiB;IACrD,IAAI;IACJ,eAAe,6BAA6B,OAAO;QACjD,IAAI,CAAC,QAAQ;YACX,SAAS,kBAAkB;gBACzB,GAAG,OAAO;gBACV,QAAQ,QAAQ,MAAM,IAAI,EAAE;gBAC5B,OAAO,QAAQ,KAAK,IAAI,EAAE;YAC5B;YACA,OAAO;QACT,OAAO;YACL,MAAM,IAAI,MAAM;YAChB,MAAM,QAAQ,GAAG,CAAC;gBAChB,EAAE,SAAS,IAAI,QAAQ,MAAM,IAAI,EAAE;gBACnC,EAAE,YAAY,IAAI,QAAQ,KAAK,IAAI,EAAE;aACtC;YACD,OAAO;QACT;IACF;IACA,OAAO;AACT;AACA,MAAM,8BAA8B,aAAa,GAAG,6BAA6B;AAEjF,SAAS,0BAA0B,OAAO;IACxC,MAAM,mBAAmB,QAAQ,KAAK;IACtC,MAAM,gBAAgB,QAAQ,MAAM;IACpC,MAAM,SAAS,QAAQ,MAAM;IAC7B,eAAe,kBAAkB,QAAQ;QACvC,SAAS,YAAY,IAAI;YACvB,IAAI,OAAO,SAAS,UAAU;gBAC5B,IAAI,cAAc,OAChB,OAAO,EAAE;gBACX,MAAM,SAAS,gBAAgB,CAAC,KAAK;gBACrC,IAAI,CAAC,QACH,MAAM,IAAI,uJAAA,CAAA,aAAY,CAAC,CAAC,WAAW,EAAE,KAAK,gFAAgF,CAAC;gBAC7H,OAAO;YACT;YACA,OAAO;QACT;QACA,SAAS,aAAa,KAAK;YACzB,IAAI,eAAe,QACjB,OAAO;YACT,IAAI,OAAO,UAAU,UAAU;gBAC7B,MAAM,SAAS,aAAa,CAAC,MAAM;gBACnC,IAAI,CAAC,QACH,MAAM,IAAI,uJAAA,CAAA,aAAY,CAAC,CAAC,QAAQ,EAAE,MAAM,gFAAgF,CAAC;gBAC3H,OAAO;YACT;YACA,OAAO;QACT;QACA,MAAM,UAAU,CAAC,SAAS,MAAM,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,IAAM,aAAa;QAChE,MAAM,QAAQ,CAAC,SAAS,KAAK,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,IAAM,YAAY;QAC5D,MAAM,OAAO,MAAM,sBAAsB;YACvC,QAAQ,SAAS,MAAM,IAAI;YAC3B,GAAG,QAAQ;YACX,QAAQ;YACR;QACF;QACA,OAAO;YACL,GAAG,IAAI;YACP,cAAa,GAAG,MAAM;gBACpB,OAAO,KAAK,YAAY,IAAI,OAAO,GAAG,CAAC;YACzC;YACA,WAAU,GAAG,MAAM;gBACjB,OAAO,KAAK,SAAS,IAAI,OAAO,GAAG,CAAC;YACtC;YACA;gBACE,OAAO;YACT;YACA;gBACE,OAAO;YACT;QACF;IACF;IACA,OAAO;AACT;AACA,SAAS,yBAAyB,iBAAiB;IACjD,IAAI;IACJ,eAAe,wBAAwB,UAAU,CAAC,CAAC;QACjD,IAAI,CAAC,QAAQ;YACX,SAAS,kBAAkB;gBACzB,GAAG,OAAO;gBACV,QAAQ,QAAQ,MAAM,IAAI,EAAE;gBAC5B,OAAO,QAAQ,KAAK,IAAI,EAAE;YAC5B;YACA,OAAO;QACT,OAAO;YACL,MAAM,IAAI,MAAM;YAChB,MAAM,QAAQ,GAAG,CAAC;gBAChB,EAAE,SAAS,IAAI,QAAQ,MAAM,IAAI,EAAE;gBACnC,EAAE,YAAY,IAAI,QAAQ,KAAK,IAAI,EAAE;aACtC;YACD,OAAO;QACT;IACF;IACA,OAAO;AACT;AACA,SAAS,0BAA0B,iBAAiB,EAAE,MAAM;IAC1D,MAAM,0BAA0B,yBAAyB;IACzD,eAAe,IAAI,IAAI,EAAE,OAAO;QAC9B,MAAM,QAAQ,MAAM,wBAAwB;YAC1C,OAAO;gBAAC,QAAQ,IAAI;aAAC;YACrB,QAAQ,WAAW,UAAU;gBAAC,QAAQ,KAAK;aAAC,GAAG,OAAO,MAAM,CAAC,QAAQ,MAAM;QAC7E;QACA,MAAM,QAAQ,MAAM,QAAQ,yBAAyB,MAAM,QAAQ,IAAI,EAAE;QACzE,IAAI,OAAO;YACT,MAAM,MAAM,YAAY,IAAI;QAC9B;QACA,OAAO;IACT;IACA,OAAO;QACL,yBAAwB,OAAO;YAC7B,OAAO,wBAAwB;QACjC;QACA,MAAM,YAAW,IAAI,EAAE,OAAO;YAC5B,MAAM,QAAQ,MAAM,IAAI,MAAM;YAC9B,OAAO,MAAM,UAAU,CAAC,MAAM;QAChC;QACA,MAAM,YAAW,IAAI,EAAE,OAAO;YAC5B,MAAM,QAAQ,MAAM,IAAI,MAAM;YAC9B,OAAO,MAAM,UAAU,CAAC,MAAM;QAChC;QACA,MAAM,cAAa,IAAI,EAAE,OAAO;YAC9B,MAAM,QAAQ,MAAM,IAAI,MAAM;YAC9B,OAAO,MAAM,YAAY,CAAC,MAAM;QAClC;QACA,MAAM,kBAAiB,IAAI,EAAE,OAAO;YAClC,MAAM,QAAQ,MAAM,IAAI,MAAM;YAC9B,OAAO,MAAM,gBAAgB,CAAC,MAAM;QACtC;QACA,MAAM,wBAAuB,IAAI,EAAE,OAAO;YACxC,MAAM,QAAQ,MAAM,IAAI,MAAM;YAC9B,OAAO,MAAM,sBAAsB,CAAC,MAAM;QAC5C;QACA,MAAM,qBAAoB,IAAI,EAAE,OAAO;YACrC,MAAM,QAAQ,MAAM,wBAAwB;gBAC1C,OAAO;oBAAC,QAAQ,IAAI;iBAAC;gBACrB,QAAQ;oBAAC,QAAQ,KAAK;iBAAC;YACzB;YACA,OAAO,MAAM,mBAAmB,CAAC,MAAM;QACzC;IACF;AACF;AAEA,SAAS,wBAAwB,UAAU,CAAC,CAAC;IAC3C,MAAM,EACJ,OAAO,eAAe,EACtB,iBAAiB,UAAU,EAC3B,YAAY,IAAI,EACjB,GAAG;IACJ,MAAM,WAAW,CAAC;QAChB,IAAI,QAAQ,gBAAgB,EAAE,CAAC,MAAM,EACnC,OAAO,CAAC,IAAI,EAAE,iBAAiB,MAAM,EAAE,EAAE,QAAQ,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;QAC7E,OAAO,CAAC,IAAI,EAAE,iBAAiB,MAAM,CAAC,CAAC;IACzC;IACA,MAAM,QAAQ;QACZ;QACA,MAAM;QACN,QAAQ;YACN,qBAAqB,SAAS;YAC9B,qBAAqB,SAAS;YAC9B,sBAAsB,SAAS;YAC/B,oBAAoB,SAAS;YAC7B,sBAAsB,SAAS;YAC/B,uBAAuB,SAAS;YAChC,qBAAqB,SAAS;YAC9B,wBAAwB,SAAS;YACjC,qBAAqB,SAAS;YAC9B,sBAAsB,SAAS;YAC/B,4BAA4B,SAAS;YACrC,0BAA0B,SAAS;YACnC,4BAA4B,SAAS;YACrC,6BAA6B,SAAS;YACtC,2BAA2B,SAAS;YACpC,8BAA8B,SAAS;YACvC,2BAA2B,SAAS;YACpC,4BAA4B,SAAS;QACvC;QACA,aAAa;YACX;gBACE,OAAO;oBACL;oBACA;oBACA;oBACA;iBACD;gBACD,UAAU;oBACR,YAAY,SAAS;gBACvB;YACF;YACA;gBACE,OAAO;gBACP,UAAU;oBACR,WAAW;gBACb;YACF;YACA;gBACE,OAAO;oBAAC;oBAAU;oBAA2B;iBAAuB;gBACpE,UAAU;oBACR,WAAW;gBACb;YACF;YACA;gBACE,OAAO;oBAAC;iBAAyB;gBACjC,UAAU;oBACR,WAAW;gBACb;YACF;YACA;gBACE,OAAO;gBACP,UAAU;oBACR,WAAW;oBACX,YAAY,SAAS;gBACvB;YACF;YACA;gBACE,OAAO;oBAAC;oBAAU;oBAAsB;iBAAgB;gBACxD,UAAU;oBACR,YAAY,SAAS;gBACvB;YACF;YACA;gBACE,OAAO;oBAAC;oBAAW;iBAAgC;gBACnD,UAAU;oBACR,YAAY,SAAS;gBACvB;YACF;YACA;gBACE,OAAO;oBACL;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,UAAU;oBACR,YAAY,SAAS;gBACvB;YACF;YACA;gBACE,OAAO;oBACL;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,UAAU;oBACR,YAAY,SAAS;gBACvB;YACF;YACA;gBACE,OAAO;gBACP,UAAU;oBACR,YAAY,SAAS;gBACvB;YACF;YACA;gBACE,OAAO;oBACL;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,UAAU;oBACR,YAAY,SAAS;gBACvB;YACF;YACA;gBACE,OAAO;oBACL;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,UAAU;oBACR,YAAY,SAAS;gBACvB;YACF;YACA;gBACE,OAAO;oBACL;oBACA;oBACA;oBACA;iBACD;gBACD,UAAU;oBACR,YAAY,SAAS;gBACvB;YACF;YACA;gBACE,0BAA0B;gBAC1B,OAAO;oBACL;oBACA;iBACD;gBACD,UAAU;oBACR,YAAY,SAAS;gBACvB;YACF;YACA;gBACE,yBAAyB;gBACzB,OAAO;oBAAC;iBAAiD;gBACzD,UAAU;oBACR,YAAY,SAAS;gBACvB;YACF;YACA;gBACE,oDAAoD;gBACpD,OAAO;oBACL;oBACA;oBACA;oBACA;iBACD;gBACD,UAAU;oBACR,YAAY,SAAS;gBACvB;YACF;YACA;gBACE,gBAAgB;gBAChB,OAAO;oBACL;oBACA;oBACA;iBACD;gBACD,UAAU;oBACR,YAAY,SAAS;gBACvB;YACF;YACA;gBACE,OAAO;oBACL;oBACA;oBACA;iBACD;gBACD,UAAU;oBACR,YAAY,SAAS;gBACvB;YACF;YACA;gBACE,OAAO;oBACL;oBACA;iBACD;gBACD,UAAU;oBACR,YAAY,SAAS;gBACvB;YACF;SACD;IACH;IACA,IAAI,CAAC,WAAW;QACd,MAAM,WAAW,GAAG,MAAM,WAAW,EAAE,IAAI,CAAC;YAC1C,IAAI,WAAW,QAAQ,EAAE,WACvB,OAAO,WAAW,QAAQ,CAAC,SAAS;YACtC,OAAO;QACT;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5104, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/node_modules/%40shikijs/engine-oniguruma/dist/index.mjs"], "sourcesContent": ["class ShikiError extends Error {\n  constructor(message) {\n    super(message);\n    this.name = \"<PERSON><PERSON>Error\";\n  }\n}\n\nfunction getHeapMax() {\n  return 2147483648;\n}\nfunction _emscripten_get_now() {\n  return typeof performance !== \"undefined\" ? performance.now() : Date.now();\n}\nconst alignUp = (x, multiple) => x + (multiple - x % multiple) % multiple;\nasync function main(init) {\n  let wasmMemory;\n  let buffer;\n  const binding = {};\n  function updateGlobalBufferAndViews(buf) {\n    buffer = buf;\n    binding.HEAPU8 = new Uint8Array(buf);\n    binding.HEAPU32 = new Uint32Array(buf);\n  }\n  function _emscripten_memcpy_big(dest, src, num) {\n    binding.HEAPU8.copyWithin(dest, src, src + num);\n  }\n  function emscripten_realloc_buffer(size) {\n    try {\n      wasmMemory.grow(size - buffer.byteLength + 65535 >>> 16);\n      updateGlobalBufferAndViews(wasmMemory.buffer);\n      return 1;\n    } catch {\n    }\n  }\n  function _emscripten_resize_heap(requestedSize) {\n    const oldSize = binding.HEAPU8.length;\n    requestedSize = requestedSize >>> 0;\n    const maxHeapSize = getHeapMax();\n    if (requestedSize > maxHeapSize)\n      return false;\n    for (let cutDown = 1; cutDown <= 4; cutDown *= 2) {\n      let overGrownHeapSize = oldSize * (1 + 0.2 / cutDown);\n      overGrownHeapSize = Math.min(overGrownHeapSize, requestedSize + 100663296);\n      const newSize = Math.min(maxHeapSize, alignUp(Math.max(requestedSize, overGrownHeapSize), 65536));\n      const replacement = emscripten_realloc_buffer(newSize);\n      if (replacement)\n        return true;\n    }\n    return false;\n  }\n  const UTF8Decoder = typeof TextDecoder != \"undefined\" ? new TextDecoder(\"utf8\") : void 0;\n  function UTF8ArrayToString(heapOrArray, idx, maxBytesToRead = 1024) {\n    const endIdx = idx + maxBytesToRead;\n    let endPtr = idx;\n    while (heapOrArray[endPtr] && !(endPtr >= endIdx)) ++endPtr;\n    if (endPtr - idx > 16 && heapOrArray.buffer && UTF8Decoder) {\n      return UTF8Decoder.decode(heapOrArray.subarray(idx, endPtr));\n    }\n    let str = \"\";\n    while (idx < endPtr) {\n      let u0 = heapOrArray[idx++];\n      if (!(u0 & 128)) {\n        str += String.fromCharCode(u0);\n        continue;\n      }\n      const u1 = heapOrArray[idx++] & 63;\n      if ((u0 & 224) === 192) {\n        str += String.fromCharCode((u0 & 31) << 6 | u1);\n        continue;\n      }\n      const u2 = heapOrArray[idx++] & 63;\n      if ((u0 & 240) === 224) {\n        u0 = (u0 & 15) << 12 | u1 << 6 | u2;\n      } else {\n        u0 = (u0 & 7) << 18 | u1 << 12 | u2 << 6 | heapOrArray[idx++] & 63;\n      }\n      if (u0 < 65536) {\n        str += String.fromCharCode(u0);\n      } else {\n        const ch = u0 - 65536;\n        str += String.fromCharCode(55296 | ch >> 10, 56320 | ch & 1023);\n      }\n    }\n    return str;\n  }\n  function UTF8ToString(ptr, maxBytesToRead) {\n    return ptr ? UTF8ArrayToString(binding.HEAPU8, ptr, maxBytesToRead) : \"\";\n  }\n  const asmLibraryArg = {\n    emscripten_get_now: _emscripten_get_now,\n    emscripten_memcpy_big: _emscripten_memcpy_big,\n    emscripten_resize_heap: _emscripten_resize_heap,\n    fd_write: () => 0\n  };\n  async function createWasm() {\n    const info = {\n      env: asmLibraryArg,\n      wasi_snapshot_preview1: asmLibraryArg\n    };\n    const exports = await init(info);\n    wasmMemory = exports.memory;\n    updateGlobalBufferAndViews(wasmMemory.buffer);\n    Object.assign(binding, exports);\n    binding.UTF8ToString = UTF8ToString;\n  }\n  await createWasm();\n  return binding;\n}\n\nvar __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __publicField = (obj, key, value) => __defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\nlet onigBinding = null;\nfunction throwLastOnigError(onigBinding2) {\n  throw new ShikiError(onigBinding2.UTF8ToString(onigBinding2.getLastOnigError()));\n}\nclass UtfString {\n  constructor(str) {\n    __publicField(this, \"utf16Length\");\n    __publicField(this, \"utf8Length\");\n    __publicField(this, \"utf16Value\");\n    __publicField(this, \"utf8Value\");\n    __publicField(this, \"utf16OffsetToUtf8\");\n    __publicField(this, \"utf8OffsetToUtf16\");\n    const utf16Length = str.length;\n    const utf8Length = UtfString._utf8ByteLength(str);\n    const computeIndicesMapping = utf8Length !== utf16Length;\n    const utf16OffsetToUtf8 = computeIndicesMapping ? new Uint32Array(utf16Length + 1) : null;\n    if (computeIndicesMapping)\n      utf16OffsetToUtf8[utf16Length] = utf8Length;\n    const utf8OffsetToUtf16 = computeIndicesMapping ? new Uint32Array(utf8Length + 1) : null;\n    if (computeIndicesMapping)\n      utf8OffsetToUtf16[utf8Length] = utf16Length;\n    const utf8Value = new Uint8Array(utf8Length);\n    let i8 = 0;\n    for (let i16 = 0; i16 < utf16Length; i16++) {\n      const charCode = str.charCodeAt(i16);\n      let codePoint = charCode;\n      let wasSurrogatePair = false;\n      if (charCode >= 55296 && charCode <= 56319) {\n        if (i16 + 1 < utf16Length) {\n          const nextCharCode = str.charCodeAt(i16 + 1);\n          if (nextCharCode >= 56320 && nextCharCode <= 57343) {\n            codePoint = (charCode - 55296 << 10) + 65536 | nextCharCode - 56320;\n            wasSurrogatePair = true;\n          }\n        }\n      }\n      if (computeIndicesMapping) {\n        utf16OffsetToUtf8[i16] = i8;\n        if (wasSurrogatePair)\n          utf16OffsetToUtf8[i16 + 1] = i8;\n        if (codePoint <= 127) {\n          utf8OffsetToUtf16[i8 + 0] = i16;\n        } else if (codePoint <= 2047) {\n          utf8OffsetToUtf16[i8 + 0] = i16;\n          utf8OffsetToUtf16[i8 + 1] = i16;\n        } else if (codePoint <= 65535) {\n          utf8OffsetToUtf16[i8 + 0] = i16;\n          utf8OffsetToUtf16[i8 + 1] = i16;\n          utf8OffsetToUtf16[i8 + 2] = i16;\n        } else {\n          utf8OffsetToUtf16[i8 + 0] = i16;\n          utf8OffsetToUtf16[i8 + 1] = i16;\n          utf8OffsetToUtf16[i8 + 2] = i16;\n          utf8OffsetToUtf16[i8 + 3] = i16;\n        }\n      }\n      if (codePoint <= 127) {\n        utf8Value[i8++] = codePoint;\n      } else if (codePoint <= 2047) {\n        utf8Value[i8++] = 192 | (codePoint & 1984) >>> 6;\n        utf8Value[i8++] = 128 | (codePoint & 63) >>> 0;\n      } else if (codePoint <= 65535) {\n        utf8Value[i8++] = 224 | (codePoint & 61440) >>> 12;\n        utf8Value[i8++] = 128 | (codePoint & 4032) >>> 6;\n        utf8Value[i8++] = 128 | (codePoint & 63) >>> 0;\n      } else {\n        utf8Value[i8++] = 240 | (codePoint & 1835008) >>> 18;\n        utf8Value[i8++] = 128 | (codePoint & 258048) >>> 12;\n        utf8Value[i8++] = 128 | (codePoint & 4032) >>> 6;\n        utf8Value[i8++] = 128 | (codePoint & 63) >>> 0;\n      }\n      if (wasSurrogatePair)\n        i16++;\n    }\n    this.utf16Length = utf16Length;\n    this.utf8Length = utf8Length;\n    this.utf16Value = str;\n    this.utf8Value = utf8Value;\n    this.utf16OffsetToUtf8 = utf16OffsetToUtf8;\n    this.utf8OffsetToUtf16 = utf8OffsetToUtf16;\n  }\n  static _utf8ByteLength(str) {\n    let result = 0;\n    for (let i = 0, len = str.length; i < len; i++) {\n      const charCode = str.charCodeAt(i);\n      let codepoint = charCode;\n      let wasSurrogatePair = false;\n      if (charCode >= 55296 && charCode <= 56319) {\n        if (i + 1 < len) {\n          const nextCharCode = str.charCodeAt(i + 1);\n          if (nextCharCode >= 56320 && nextCharCode <= 57343) {\n            codepoint = (charCode - 55296 << 10) + 65536 | nextCharCode - 56320;\n            wasSurrogatePair = true;\n          }\n        }\n      }\n      if (codepoint <= 127)\n        result += 1;\n      else if (codepoint <= 2047)\n        result += 2;\n      else if (codepoint <= 65535)\n        result += 3;\n      else\n        result += 4;\n      if (wasSurrogatePair)\n        i++;\n    }\n    return result;\n  }\n  createString(onigBinding2) {\n    const result = onigBinding2.omalloc(this.utf8Length);\n    onigBinding2.HEAPU8.set(this.utf8Value, result);\n    return result;\n  }\n}\nconst _OnigString = class _OnigString {\n  constructor(str) {\n    __publicField(this, \"id\", ++_OnigString.LAST_ID);\n    __publicField(this, \"_onigBinding\");\n    __publicField(this, \"content\");\n    __publicField(this, \"utf16Length\");\n    __publicField(this, \"utf8Length\");\n    __publicField(this, \"utf16OffsetToUtf8\");\n    __publicField(this, \"utf8OffsetToUtf16\");\n    __publicField(this, \"ptr\");\n    if (!onigBinding)\n      throw new ShikiError(\"Must invoke loadWasm first.\");\n    this._onigBinding = onigBinding;\n    this.content = str;\n    const utfString = new UtfString(str);\n    this.utf16Length = utfString.utf16Length;\n    this.utf8Length = utfString.utf8Length;\n    this.utf16OffsetToUtf8 = utfString.utf16OffsetToUtf8;\n    this.utf8OffsetToUtf16 = utfString.utf8OffsetToUtf16;\n    if (this.utf8Length < 1e4 && !_OnigString._sharedPtrInUse) {\n      if (!_OnigString._sharedPtr)\n        _OnigString._sharedPtr = onigBinding.omalloc(1e4);\n      _OnigString._sharedPtrInUse = true;\n      onigBinding.HEAPU8.set(utfString.utf8Value, _OnigString._sharedPtr);\n      this.ptr = _OnigString._sharedPtr;\n    } else {\n      this.ptr = utfString.createString(onigBinding);\n    }\n  }\n  convertUtf8OffsetToUtf16(utf8Offset) {\n    if (this.utf8OffsetToUtf16) {\n      if (utf8Offset < 0)\n        return 0;\n      if (utf8Offset > this.utf8Length)\n        return this.utf16Length;\n      return this.utf8OffsetToUtf16[utf8Offset];\n    }\n    return utf8Offset;\n  }\n  convertUtf16OffsetToUtf8(utf16Offset) {\n    if (this.utf16OffsetToUtf8) {\n      if (utf16Offset < 0)\n        return 0;\n      if (utf16Offset > this.utf16Length)\n        return this.utf8Length;\n      return this.utf16OffsetToUtf8[utf16Offset];\n    }\n    return utf16Offset;\n  }\n  dispose() {\n    if (this.ptr === _OnigString._sharedPtr)\n      _OnigString._sharedPtrInUse = false;\n    else\n      this._onigBinding.ofree(this.ptr);\n  }\n};\n__publicField(_OnigString, \"LAST_ID\", 0);\n__publicField(_OnigString, \"_sharedPtr\", 0);\n// a pointer to a string of 10000 bytes\n__publicField(_OnigString, \"_sharedPtrInUse\", false);\nlet OnigString = _OnigString;\nclass OnigScanner {\n  constructor(patterns) {\n    __publicField(this, \"_onigBinding\");\n    __publicField(this, \"_ptr\");\n    if (!onigBinding)\n      throw new ShikiError(\"Must invoke loadWasm first.\");\n    const strPtrsArr = [];\n    const strLenArr = [];\n    for (let i = 0, len = patterns.length; i < len; i++) {\n      const utfString = new UtfString(patterns[i]);\n      strPtrsArr[i] = utfString.createString(onigBinding);\n      strLenArr[i] = utfString.utf8Length;\n    }\n    const strPtrsPtr = onigBinding.omalloc(4 * patterns.length);\n    onigBinding.HEAPU32.set(strPtrsArr, strPtrsPtr / 4);\n    const strLenPtr = onigBinding.omalloc(4 * patterns.length);\n    onigBinding.HEAPU32.set(strLenArr, strLenPtr / 4);\n    const scannerPtr = onigBinding.createOnigScanner(strPtrsPtr, strLenPtr, patterns.length);\n    for (let i = 0, len = patterns.length; i < len; i++)\n      onigBinding.ofree(strPtrsArr[i]);\n    onigBinding.ofree(strLenPtr);\n    onigBinding.ofree(strPtrsPtr);\n    if (scannerPtr === 0)\n      throwLastOnigError(onigBinding);\n    this._onigBinding = onigBinding;\n    this._ptr = scannerPtr;\n  }\n  dispose() {\n    this._onigBinding.freeOnigScanner(this._ptr);\n  }\n  findNextMatchSync(string, startPosition, arg) {\n    let options = 0 /* None */;\n    if (typeof arg === \"number\") {\n      options = arg;\n    }\n    if (typeof string === \"string\") {\n      string = new OnigString(string);\n      const result = this._findNextMatchSync(string, startPosition, false, options);\n      string.dispose();\n      return result;\n    }\n    return this._findNextMatchSync(string, startPosition, false, options);\n  }\n  _findNextMatchSync(string, startPosition, debugCall, options) {\n    const onigBinding2 = this._onigBinding;\n    const resultPtr = onigBinding2.findNextOnigScannerMatch(this._ptr, string.id, string.ptr, string.utf8Length, string.convertUtf16OffsetToUtf8(startPosition), options);\n    if (resultPtr === 0) {\n      return null;\n    }\n    const HEAPU32 = onigBinding2.HEAPU32;\n    let offset = resultPtr / 4;\n    const index = HEAPU32[offset++];\n    const count = HEAPU32[offset++];\n    const captureIndices = [];\n    for (let i = 0; i < count; i++) {\n      const beg = string.convertUtf8OffsetToUtf16(HEAPU32[offset++]);\n      const end = string.convertUtf8OffsetToUtf16(HEAPU32[offset++]);\n      captureIndices[i] = {\n        start: beg,\n        end,\n        length: end - beg\n      };\n    }\n    return {\n      index,\n      captureIndices\n    };\n  }\n}\nfunction isInstantiatorOptionsObject(dataOrOptions) {\n  return typeof dataOrOptions.instantiator === \"function\";\n}\nfunction isInstantiatorModule(dataOrOptions) {\n  return typeof dataOrOptions.default === \"function\";\n}\nfunction isDataOptionsObject(dataOrOptions) {\n  return typeof dataOrOptions.data !== \"undefined\";\n}\nfunction isResponse(dataOrOptions) {\n  return typeof Response !== \"undefined\" && dataOrOptions instanceof Response;\n}\nfunction isArrayBuffer(data) {\n  return typeof ArrayBuffer !== \"undefined\" && (data instanceof ArrayBuffer || ArrayBuffer.isView(data)) || typeof Buffer !== \"undefined\" && Buffer.isBuffer?.(data) || typeof SharedArrayBuffer !== \"undefined\" && data instanceof SharedArrayBuffer || typeof Uint32Array !== \"undefined\" && data instanceof Uint32Array;\n}\nlet initPromise;\nfunction loadWasm(options) {\n  if (initPromise)\n    return initPromise;\n  async function _load() {\n    onigBinding = await main(async (info) => {\n      let instance = options;\n      instance = await instance;\n      if (typeof instance === \"function\")\n        instance = await instance(info);\n      if (typeof instance === \"function\")\n        instance = await instance(info);\n      if (isInstantiatorOptionsObject(instance)) {\n        instance = await instance.instantiator(info);\n      } else if (isInstantiatorModule(instance)) {\n        instance = await instance.default(info);\n      } else {\n        if (isDataOptionsObject(instance))\n          instance = instance.data;\n        if (isResponse(instance)) {\n          if (typeof WebAssembly.instantiateStreaming === \"function\")\n            instance = await _makeResponseStreamingLoader(instance)(info);\n          else\n            instance = await _makeResponseNonStreamingLoader(instance)(info);\n        } else if (isArrayBuffer(instance)) {\n          instance = await _makeArrayBufferLoader(instance)(info);\n        } else if (instance instanceof WebAssembly.Module) {\n          instance = await _makeArrayBufferLoader(instance)(info);\n        } else if (\"default\" in instance && instance.default instanceof WebAssembly.Module) {\n          instance = await _makeArrayBufferLoader(instance.default)(info);\n        }\n      }\n      if (\"instance\" in instance)\n        instance = instance.instance;\n      if (\"exports\" in instance)\n        instance = instance.exports;\n      return instance;\n    });\n  }\n  initPromise = _load();\n  return initPromise;\n}\nfunction _makeArrayBufferLoader(data) {\n  return (importObject) => WebAssembly.instantiate(data, importObject);\n}\nfunction _makeResponseStreamingLoader(data) {\n  return (importObject) => WebAssembly.instantiateStreaming(data, importObject);\n}\nfunction _makeResponseNonStreamingLoader(data) {\n  return async (importObject) => {\n    const arrayBuffer = await data.arrayBuffer();\n    return WebAssembly.instantiate(arrayBuffer, importObject);\n  };\n}\n\nlet _defaultWasmLoader;\nfunction setDefaultWasmLoader(_loader) {\n  _defaultWasmLoader = _loader;\n}\nfunction getDefaultWasmLoader() {\n  return _defaultWasmLoader;\n}\nasync function createOnigurumaEngine(options) {\n  if (options)\n    await loadWasm(options);\n  return {\n    createScanner(patterns) {\n      return new OnigScanner(patterns.map((p) => typeof p === \"string\" ? p : p.source));\n    },\n    createString(s) {\n      return new OnigString(s);\n    }\n  };\n}\n\nexport { createOnigurumaEngine, getDefaultWasmLoader, loadWasm, setDefaultWasmLoader };\n"], "names": [], "mappings": ";;;;;;AAkXmH;AAlXnH,MAAM,mBAAmB;IACvB,YAAY,OAAO,CAAE;QACnB,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;IACd;AACF;AAEA,SAAS;IACP,OAAO;AACT;AACA,SAAS;IACP,OAAO,OAAO,gBAAgB,cAAc,YAAY,GAAG,KAAK,KAAK,GAAG;AAC1E;AACA,MAAM,UAAU,CAAC,GAAG,WAAa,IAAI,CAAC,WAAW,IAAI,QAAQ,IAAI;AACjE,eAAe,KAAK,IAAI;IACtB,IAAI;IACJ,IAAI;IACJ,MAAM,UAAU,CAAC;IACjB,SAAS,2BAA2B,GAAG;QACrC,SAAS;QACT,QAAQ,MAAM,GAAG,IAAI,WAAW;QAChC,QAAQ,OAAO,GAAG,IAAI,YAAY;IACpC;IACA,SAAS,uBAAuB,IAAI,EAAE,GAAG,EAAE,GAAG;QAC5C,QAAQ,MAAM,CAAC,UAAU,CAAC,MAAM,KAAK,MAAM;IAC7C;IACA,SAAS,0BAA0B,IAAI;QACrC,IAAI;YACF,WAAW,IAAI,CAAC,OAAO,OAAO,UAAU,GAAG,UAAU;YACrD,2BAA2B,WAAW,MAAM;YAC5C,OAAO;QACT,EAAE,OAAM,CACR;IACF;IACA,SAAS,wBAAwB,aAAa;QAC5C,MAAM,UAAU,QAAQ,MAAM,CAAC,MAAM;QACrC,gBAAgB,kBAAkB;QAClC,MAAM,cAAc;QACpB,IAAI,gBAAgB,aAClB,OAAO;QACT,IAAK,IAAI,UAAU,GAAG,WAAW,GAAG,WAAW,EAAG;YAChD,IAAI,oBAAoB,UAAU,CAAC,IAAI,MAAM,OAAO;YACpD,oBAAoB,KAAK,GAAG,CAAC,mBAAmB,gBAAgB;YAChE,MAAM,UAAU,KAAK,GAAG,CAAC,aAAa,QAAQ,KAAK,GAAG,CAAC,eAAe,oBAAoB;YAC1F,MAAM,cAAc,0BAA0B;YAC9C,IAAI,aACF,OAAO;QACX;QACA,OAAO;IACT;IACA,MAAM,cAAc,OAAO,eAAe,cAAc,IAAI,YAAY,UAAU,KAAK;IACvF,SAAS,kBAAkB,WAAW,EAAE,GAAG,EAAE,iBAAiB,IAAI;QAChE,MAAM,SAAS,MAAM;QACrB,IAAI,SAAS;QACb,MAAO,WAAW,CAAC,OAAO,IAAI,CAAC,CAAC,UAAU,MAAM,EAAG,EAAE;QACrD,IAAI,SAAS,MAAM,MAAM,YAAY,MAAM,IAAI,aAAa;YAC1D,OAAO,YAAY,MAAM,CAAC,YAAY,QAAQ,CAAC,KAAK;QACtD;QACA,IAAI,MAAM;QACV,MAAO,MAAM,OAAQ;YACnB,IAAI,KAAK,WAAW,CAAC,MAAM;YAC3B,IAAI,CAAC,CAAC,KAAK,GAAG,GAAG;gBACf,OAAO,OAAO,YAAY,CAAC;gBAC3B;YACF;YACA,MAAM,KAAK,WAAW,CAAC,MAAM,GAAG;YAChC,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;gBACtB,OAAO,OAAO,YAAY,CAAC,CAAC,KAAK,EAAE,KAAK,IAAI;gBAC5C;YACF;YACA,MAAM,KAAK,WAAW,CAAC,MAAM,GAAG;YAChC,IAAI,CAAC,KAAK,GAAG,MAAM,KAAK;gBACtB,KAAK,CAAC,KAAK,EAAE,KAAK,KAAK,MAAM,IAAI;YACnC,OAAO;gBACL,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK,MAAM,KAAK,MAAM,IAAI,WAAW,CAAC,MAAM,GAAG;YAClE;YACA,IAAI,KAAK,OAAO;gBACd,OAAO,OAAO,YAAY,CAAC;YAC7B,OAAO;gBACL,MAAM,KAAK,KAAK;gBAChB,OAAO,OAAO,YAAY,CAAC,QAAQ,MAAM,IAAI,QAAQ,KAAK;YAC5D;QACF;QACA,OAAO;IACT;IACA,SAAS,aAAa,GAAG,EAAE,cAAc;QACvC,OAAO,MAAM,kBAAkB,QAAQ,MAAM,EAAE,KAAK,kBAAkB;IACxE;IACA,MAAM,gBAAgB;QACpB,oBAAoB;QACpB,uBAAuB;QACvB,wBAAwB;QACxB,UAAU,IAAM;IAClB;IACA,eAAe;QACb,MAAM,OAAO;YACX,KAAK;YACL,wBAAwB;QAC1B;QACA,MAAM,UAAU,MAAM,KAAK;QAC3B,aAAa,QAAQ,MAAM;QAC3B,2BAA2B,WAAW,MAAM;QAC5C,OAAO,MAAM,CAAC,SAAS;QACvB,QAAQ,YAAY,GAAG;IACzB;IACA,MAAM;IACN,OAAO;AACT;AAEA,IAAI,YAAY,OAAO,cAAc;AACrC,IAAI,kBAAkB,CAAC,KAAK,KAAK,QAAU,OAAO,MAAM,UAAU,KAAK,KAAK;QAAE,YAAY;QAAM,cAAc;QAAM,UAAU;QAAM;IAAM,KAAK,GAAG,CAAC,IAAI,GAAG;AAC1J,IAAI,gBAAgB,CAAC,KAAK,KAAK,QAAU,gBAAgB,KAAK,OAAO,QAAQ,WAAW,MAAM,KAAK,KAAK;AACxG,IAAI,cAAc;AAClB,SAAS,mBAAmB,YAAY;IACtC,MAAM,IAAI,WAAW,aAAa,YAAY,CAAC,aAAa,gBAAgB;AAC9E;AACA,MAAM;IACJ,YAAY,GAAG,CAAE;QACf,cAAc,IAAI,EAAE;QACpB,cAAc,IAAI,EAAE;QACpB,cAAc,IAAI,EAAE;QACpB,cAAc,IAAI,EAAE;QACpB,cAAc,IAAI,EAAE;QACpB,cAAc,IAAI,EAAE;QACpB,MAAM,cAAc,IAAI,MAAM;QAC9B,MAAM,aAAa,UAAU,eAAe,CAAC;QAC7C,MAAM,wBAAwB,eAAe;QAC7C,MAAM,oBAAoB,wBAAwB,IAAI,YAAY,cAAc,KAAK;QACrF,IAAI,uBACF,iBAAiB,CAAC,YAAY,GAAG;QACnC,MAAM,oBAAoB,wBAAwB,IAAI,YAAY,aAAa,KAAK;QACpF,IAAI,uBACF,iBAAiB,CAAC,WAAW,GAAG;QAClC,MAAM,YAAY,IAAI,WAAW;QACjC,IAAI,KAAK;QACT,IAAK,IAAI,MAAM,GAAG,MAAM,aAAa,MAAO;YAC1C,MAAM,WAAW,IAAI,UAAU,CAAC;YAChC,IAAI,YAAY;YAChB,IAAI,mBAAmB;YACvB,IAAI,YAAY,SAAS,YAAY,OAAO;gBAC1C,IAAI,MAAM,IAAI,aAAa;oBACzB,MAAM,eAAe,IAAI,UAAU,CAAC,MAAM;oBAC1C,IAAI,gBAAgB,SAAS,gBAAgB,OAAO;wBAClD,YAAY,CAAC,WAAW,SAAS,EAAE,IAAI,QAAQ,eAAe;wBAC9D,mBAAmB;oBACrB;gBACF;YACF;YACA,IAAI,uBAAuB;gBACzB,iBAAiB,CAAC,IAAI,GAAG;gBACzB,IAAI,kBACF,iBAAiB,CAAC,MAAM,EAAE,GAAG;gBAC/B,IAAI,aAAa,KAAK;oBACpB,iBAAiB,CAAC,KAAK,EAAE,GAAG;gBAC9B,OAAO,IAAI,aAAa,MAAM;oBAC5B,iBAAiB,CAAC,KAAK,EAAE,GAAG;oBAC5B,iBAAiB,CAAC,KAAK,EAAE,GAAG;gBAC9B,OAAO,IAAI,aAAa,OAAO;oBAC7B,iBAAiB,CAAC,KAAK,EAAE,GAAG;oBAC5B,iBAAiB,CAAC,KAAK,EAAE,GAAG;oBAC5B,iBAAiB,CAAC,KAAK,EAAE,GAAG;gBAC9B,OAAO;oBACL,iBAAiB,CAAC,KAAK,EAAE,GAAG;oBAC5B,iBAAiB,CAAC,KAAK,EAAE,GAAG;oBAC5B,iBAAiB,CAAC,KAAK,EAAE,GAAG;oBAC5B,iBAAiB,CAAC,KAAK,EAAE,GAAG;gBAC9B;YACF;YACA,IAAI,aAAa,KAAK;gBACpB,SAAS,CAAC,KAAK,GAAG;YACpB,OAAO,IAAI,aAAa,MAAM;gBAC5B,SAAS,CAAC,KAAK,GAAG,MAAM,CAAC,YAAY,IAAI,MAAM;gBAC/C,SAAS,CAAC,KAAK,GAAG,MAAM,CAAC,YAAY,EAAE,MAAM;YAC/C,OAAO,IAAI,aAAa,OAAO;gBAC7B,SAAS,CAAC,KAAK,GAAG,MAAM,CAAC,YAAY,KAAK,MAAM;gBAChD,SAAS,CAAC,KAAK,GAAG,MAAM,CAAC,YAAY,IAAI,MAAM;gBAC/C,SAAS,CAAC,KAAK,GAAG,MAAM,CAAC,YAAY,EAAE,MAAM;YAC/C,OAAO;gBACL,SAAS,CAAC,KAAK,GAAG,MAAM,CAAC,YAAY,OAAO,MAAM;gBAClD,SAAS,CAAC,KAAK,GAAG,MAAM,CAAC,YAAY,MAAM,MAAM;gBACjD,SAAS,CAAC,KAAK,GAAG,MAAM,CAAC,YAAY,IAAI,MAAM;gBAC/C,SAAS,CAAC,KAAK,GAAG,MAAM,CAAC,YAAY,EAAE,MAAM;YAC/C;YACA,IAAI,kBACF;QACJ;QACA,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,iBAAiB,GAAG;IAC3B;IACA,OAAO,gBAAgB,GAAG,EAAE;QAC1B,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,MAAM,EAAE,IAAI,KAAK,IAAK;YAC9C,MAAM,WAAW,IAAI,UAAU,CAAC;YAChC,IAAI,YAAY;YAChB,IAAI,mBAAmB;YACvB,IAAI,YAAY,SAAS,YAAY,OAAO;gBAC1C,IAAI,IAAI,IAAI,KAAK;oBACf,MAAM,eAAe,IAAI,UAAU,CAAC,IAAI;oBACxC,IAAI,gBAAgB,SAAS,gBAAgB,OAAO;wBAClD,YAAY,CAAC,WAAW,SAAS,EAAE,IAAI,QAAQ,eAAe;wBAC9D,mBAAmB;oBACrB;gBACF;YACF;YACA,IAAI,aAAa,KACf,UAAU;iBACP,IAAI,aAAa,MACpB,UAAU;iBACP,IAAI,aAAa,OACpB,UAAU;iBAEV,UAAU;YACZ,IAAI,kBACF;QACJ;QACA,OAAO;IACT;IACA,aAAa,YAAY,EAAE;QACzB,MAAM,SAAS,aAAa,OAAO,CAAC,IAAI,CAAC,UAAU;QACnD,aAAa,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE;QACxC,OAAO;IACT;AACF;AACA,MAAM,cAAc,MAAM;IACxB,YAAY,GAAG,CAAE;QACf,cAAc,IAAI,EAAE,MAAM,EAAE,YAAY,OAAO;QAC/C,cAAc,IAAI,EAAE;QACpB,cAAc,IAAI,EAAE;QACpB,cAAc,IAAI,EAAE;QACpB,cAAc,IAAI,EAAE;QACpB,cAAc,IAAI,EAAE;QACpB,cAAc,IAAI,EAAE;QACpB,cAAc,IAAI,EAAE;QACpB,IAAI,CAAC,aACH,MAAM,IAAI,WAAW;QACvB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,OAAO,GAAG;QACf,MAAM,YAAY,IAAI,UAAU;QAChC,IAAI,CAAC,WAAW,GAAG,UAAU,WAAW;QACxC,IAAI,CAAC,UAAU,GAAG,UAAU,UAAU;QACtC,IAAI,CAAC,iBAAiB,GAAG,UAAU,iBAAiB;QACpD,IAAI,CAAC,iBAAiB,GAAG,UAAU,iBAAiB;QACpD,IAAI,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,YAAY,eAAe,EAAE;YACzD,IAAI,CAAC,YAAY,UAAU,EACzB,YAAY,UAAU,GAAG,YAAY,OAAO,CAAC;YAC/C,YAAY,eAAe,GAAG;YAC9B,YAAY,MAAM,CAAC,GAAG,CAAC,UAAU,SAAS,EAAE,YAAY,UAAU;YAClE,IAAI,CAAC,GAAG,GAAG,YAAY,UAAU;QACnC,OAAO;YACL,IAAI,CAAC,GAAG,GAAG,UAAU,YAAY,CAAC;QACpC;IACF;IACA,yBAAyB,UAAU,EAAE;QACnC,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,IAAI,aAAa,GACf,OAAO;YACT,IAAI,aAAa,IAAI,CAAC,UAAU,EAC9B,OAAO,IAAI,CAAC,WAAW;YACzB,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAW;QAC3C;QACA,OAAO;IACT;IACA,yBAAyB,WAAW,EAAE;QACpC,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC1B,IAAI,cAAc,GAChB,OAAO;YACT,IAAI,cAAc,IAAI,CAAC,WAAW,EAChC,OAAO,IAAI,CAAC,UAAU;YACxB,OAAO,IAAI,CAAC,iBAAiB,CAAC,YAAY;QAC5C;QACA,OAAO;IACT;IACA,UAAU;QACR,IAAI,IAAI,CAAC,GAAG,KAAK,YAAY,UAAU,EACrC,YAAY,eAAe,GAAG;aAE9B,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG;IACpC;AACF;AACA,cAAc,aAAa,WAAW;AACtC,cAAc,aAAa,cAAc;AACzC,uCAAuC;AACvC,cAAc,aAAa,mBAAmB;AAC9C,IAAI,aAAa;AACjB,MAAM;IACJ,YAAY,QAAQ,CAAE;QACpB,cAAc,IAAI,EAAE;QACpB,cAAc,IAAI,EAAE;QACpB,IAAI,CAAC,aACH,MAAM,IAAI,WAAW;QACvB,MAAM,aAAa,EAAE;QACrB,MAAM,YAAY,EAAE;QACpB,IAAK,IAAI,IAAI,GAAG,MAAM,SAAS,MAAM,EAAE,IAAI,KAAK,IAAK;YACnD,MAAM,YAAY,IAAI,UAAU,QAAQ,CAAC,EAAE;YAC3C,UAAU,CAAC,EAAE,GAAG,UAAU,YAAY,CAAC;YACvC,SAAS,CAAC,EAAE,GAAG,UAAU,UAAU;QACrC;QACA,MAAM,aAAa,YAAY,OAAO,CAAC,IAAI,SAAS,MAAM;QAC1D,YAAY,OAAO,CAAC,GAAG,CAAC,YAAY,aAAa;QACjD,MAAM,YAAY,YAAY,OAAO,CAAC,IAAI,SAAS,MAAM;QACzD,YAAY,OAAO,CAAC,GAAG,CAAC,WAAW,YAAY;QAC/C,MAAM,aAAa,YAAY,iBAAiB,CAAC,YAAY,WAAW,SAAS,MAAM;QACvF,IAAK,IAAI,IAAI,GAAG,MAAM,SAAS,MAAM,EAAE,IAAI,KAAK,IAC9C,YAAY,KAAK,CAAC,UAAU,CAAC,EAAE;QACjC,YAAY,KAAK,CAAC;QAClB,YAAY,KAAK,CAAC;QAClB,IAAI,eAAe,GACjB,mBAAmB;QACrB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,IAAI,GAAG;IACd;IACA,UAAU;QACR,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI;IAC7C;IACA,kBAAkB,MAAM,EAAE,aAAa,EAAE,GAAG,EAAE;QAC5C,IAAI,UAAU,EAAE,QAAQ;QACxB,IAAI,OAAO,QAAQ,UAAU;YAC3B,UAAU;QACZ;QACA,IAAI,OAAO,WAAW,UAAU;YAC9B,SAAS,IAAI,WAAW;YACxB,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC,QAAQ,eAAe,OAAO;YACrE,OAAO,OAAO;YACd,OAAO;QACT;QACA,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,eAAe,OAAO;IAC/D;IACA,mBAAmB,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,OAAO,EAAE;QAC5D,MAAM,eAAe,IAAI,CAAC,YAAY;QACtC,MAAM,YAAY,aAAa,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE,OAAO,GAAG,EAAE,OAAO,UAAU,EAAE,OAAO,wBAAwB,CAAC,gBAAgB;QAC7J,IAAI,cAAc,GAAG;YACnB,OAAO;QACT;QACA,MAAM,UAAU,aAAa,OAAO;QACpC,IAAI,SAAS,YAAY;QACzB,MAAM,QAAQ,OAAO,CAAC,SAAS;QAC/B,MAAM,QAAQ,OAAO,CAAC,SAAS;QAC/B,MAAM,iBAAiB,EAAE;QACzB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC9B,MAAM,MAAM,OAAO,wBAAwB,CAAC,OAAO,CAAC,SAAS;YAC7D,MAAM,MAAM,OAAO,wBAAwB,CAAC,OAAO,CAAC,SAAS;YAC7D,cAAc,CAAC,EAAE,GAAG;gBAClB,OAAO;gBACP;gBACA,QAAQ,MAAM;YAChB;QACF;QACA,OAAO;YACL;YACA;QACF;IACF;AACF;AACA,SAAS,4BAA4B,aAAa;IAChD,OAAO,OAAO,cAAc,YAAY,KAAK;AAC/C;AACA,SAAS,qBAAqB,aAAa;IACzC,OAAO,OAAO,cAAc,OAAO,KAAK;AAC1C;AACA,SAAS,oBAAoB,aAAa;IACxC,OAAO,OAAO,cAAc,IAAI,KAAK;AACvC;AACA,SAAS,WAAW,aAAa;IAC/B,OAAO,OAAO,aAAa,eAAe,yBAAyB;AACrE;AACA,SAAS,cAAc,IAAI;IACzB,OAAO,OAAO,gBAAgB,eAAe,CAAC,gBAAgB,eAAe,YAAY,MAAM,CAAC,KAAK,KAAK,OAAO,8JAAA,CAAA,SAAM,KAAK,eAAe,8JAAA,CAAA,SAAM,CAAC,QAAQ,GAAG,SAAS,OAAO,sBAAsB,eAAe,gBAAgB,qBAAqB,OAAO,gBAAgB,eAAe,gBAAgB;AAC/S;AACA,IAAI;AACJ,SAAS,SAAS,OAAO;IACvB,IAAI,aACF,OAAO;IACT,eAAe;QACb,cAAc,MAAM,KAAK,OAAO;YAC9B,IAAI,WAAW;YACf,WAAW,MAAM;YACjB,IAAI,OAAO,aAAa,YACtB,WAAW,MAAM,SAAS;YAC5B,IAAI,OAAO,aAAa,YACtB,WAAW,MAAM,SAAS;YAC5B,IAAI,4BAA4B,WAAW;gBACzC,WAAW,MAAM,SAAS,YAAY,CAAC;YACzC,OAAO,IAAI,qBAAqB,WAAW;gBACzC,WAAW,MAAM,SAAS,OAAO,CAAC;YACpC,OAAO;gBACL,IAAI,oBAAoB,WACtB,WAAW,SAAS,IAAI;gBAC1B,IAAI,WAAW,WAAW;oBACxB,IAAI,OAAO,YAAY,oBAAoB,KAAK,YAC9C,WAAW,MAAM,6BAA6B,UAAU;yBAExD,WAAW,MAAM,gCAAgC,UAAU;gBAC/D,OAAO,IAAI,cAAc,WAAW;oBAClC,WAAW,MAAM,uBAAuB,UAAU;gBACpD,OAAO,IAAI,oBAAoB,YAAY,MAAM,EAAE;oBACjD,WAAW,MAAM,uBAAuB,UAAU;gBACpD,OAAO,IAAI,aAAa,YAAY,SAAS,OAAO,YAAY,YAAY,MAAM,EAAE;oBAClF,WAAW,MAAM,uBAAuB,SAAS,OAAO,EAAE;gBAC5D;YACF;YACA,IAAI,cAAc,UAChB,WAAW,SAAS,QAAQ;YAC9B,IAAI,aAAa,UACf,WAAW,SAAS,OAAO;YAC7B,OAAO;QACT;IACF;IACA,cAAc;IACd,OAAO;AACT;AACA,SAAS,uBAAuB,IAAI;IAClC,OAAO,CAAC,eAAiB,YAAY,WAAW,CAAC,MAAM;AACzD;AACA,SAAS,6BAA6B,IAAI;IACxC,OAAO,CAAC,eAAiB,YAAY,oBAAoB,CAAC,MAAM;AAClE;AACA,SAAS,gCAAgC,IAAI;IAC3C,OAAO,OAAO;QACZ,MAAM,cAAc,MAAM,KAAK,WAAW;QAC1C,OAAO,YAAY,WAAW,CAAC,aAAa;IAC9C;AACF;AAEA,IAAI;AACJ,SAAS,qBAAqB,OAAO;IACnC,qBAAqB;AACvB;AACA,SAAS;IACP,OAAO;AACT;AACA,eAAe,sBAAsB,OAAO;IAC1C,IAAI,SACF,MAAM,SAAS;IACjB,OAAO;QACL,eAAc,QAAQ;YACpB,OAAO,IAAI,YAAY,SAAS,GAAG,CAAC,CAAC,IAAM,OAAO,MAAM,WAAW,IAAI,EAAE,MAAM;QACjF;QACA,cAAa,CAAC;YACZ,OAAO,IAAI,WAAW;QACxB;IACF;AACF", "ignoreList": [0], "debugId": null}}]}