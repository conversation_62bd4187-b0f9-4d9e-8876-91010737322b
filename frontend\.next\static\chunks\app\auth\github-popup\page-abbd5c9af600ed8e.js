(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9190],{14998:(e,t,r)=>{Promise.resolve().then(r.bind(r,37288))},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var o=r(12115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,o.forwardRef)((e,t)=>{let{color:r="currentColor",size:s=24,strokeWidth:a=2,absoluteStrokeWidth:c,className:l="",children:u,iconNode:d,...h}=e;return(0,o.createElement)("svg",{ref:t,...i,width:s,height:s,stroke:r,strokeWidth:c?24*Number(a)/Number(s):a,className:n("lucide",l),...h},[...d.map(e=>{let[t,r]=e;return(0,o.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),c=(e,t)=>{let r=(0,o.forwardRef)((r,i)=>{let{className:c,...l}=r;return(0,o.createElement)(a,{ref:i,iconNode:t,className:n("lucide-".concat(s(e)),c),...l})});return r.displayName="".concat(e),r}},37288:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var o=r(95155),s=r(12115),n=r(52643),i=r(51154);function a(){let[e,t]=(0,s.useState)("loading"),[r,a]=(0,s.useState)("");return(0,s.useEffect)(()=>{let e=(0,n.U)(),r=sessionStorage.getItem("github-returnUrl")||"/dashboard",o=e=>{try{window.opener&&!window.opener.closed&&window.opener.postMessage(e,window.location.origin)}catch(e){console.error("Failed to post message to opener:",e)}},s=()=>{t("processing"),o({type:"github-auth-success",returnUrl:r}),setTimeout(()=>{window.close()},500)},i=e=>{t("error"),a(e),o({type:"github-auth-error",message:e}),setTimeout(()=>{window.close()},2e3)},c=async()=>{try{let r=new URLSearchParams(window.location.search),o=r.has("code");if(r.has("error")){let e=r.get("error"),t=r.get("error_description");throw Error(t||e||"GitHub OAuth error")}if(o){t("processing");try{await new Promise(e=>setTimeout(e,1e3));let{data:{session:t},error:r}=await e.auth.getSession();if(r)throw r;if(null==t?void 0:t.user)return void s();let{data:{subscription:o}}=e.auth.onAuthStateChange(async(e,t)=>{"SIGNED_IN"===e&&(null==t?void 0:t.user)?(o.unsubscribe(),s()):"SIGNED_OUT"===e&&(o.unsubscribe(),i("Authentication failed - please try again"))});setTimeout(()=>{o.unsubscribe(),i("Authentication timeout - please try again")},1e4)}catch(e){console.error("Auth processing error:",e),i(e.message||"Authentication failed")}}else{t("loading");let{error:r}=await e.auth.signInWithOAuth({provider:"github",options:{redirectTo:"".concat(window.location.origin,"/auth/github-popup"),queryParams:{access_type:"online",prompt:"select_account"}}});if(r)throw r}}catch(e){console.error("OAuth error:",e),i(e.message||"Failed to authenticate with GitHub")}},l=()=>{sessionStorage.removeItem("github-returnUrl")};return window.addEventListener("beforeunload",l),c(),()=>{window.removeEventListener("beforeunload",l)}},[]),(0,o.jsx)("main",{className:"flex flex-col items-center justify-center h-screen bg-background p-8",children:(0,o.jsxs)("div",{className:"flex flex-col items-center gap-4 text-center max-w-sm",children:["error"!==e&&(0,o.jsx)(i.A,{className:"h-8 w-8 animate-spin text-primary"}),(0,o.jsxs)("div",{className:"space-y-2",children:[(0,o.jsx)("h1",{className:"text-lg font-medium",children:"GitHub Sign-In"}),(0,o.jsx)("p",{className:"text-sm ".concat((()=>{switch(e){case"error":return"text-red-500";case"processing":return"text-green-500";default:return"text-muted-foreground"}})()),children:(()=>{switch(e){case"loading":return"Starting GitHub authentication...";case"processing":return"Completing sign-in...";case"error":return r||"Authentication failed";default:return"Processing..."}})()})]}),"error"===e&&(0,o.jsx)("button",{onClick:()=>window.close(),className:"mt-4 px-4 py-2 text-sm bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors",children:"Close"})]})})}},51154:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});let o=(0,r(19946).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},52643:(e,t,r)=>{"use strict";r.d(t,{U:()=>s});var o=r(81935);let s=()=>{let e="";return e&&!e.startsWith("http")&&(e="http://".concat(e)),(0,o.createBrowserClient)(e,"")}}},e=>{var t=t=>e(e.s=t);e.O(0,[1935,8441,1684,7358],()=>t(14998)),_N_E=e.O()}]);