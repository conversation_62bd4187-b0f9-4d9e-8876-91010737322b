(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[539],{381:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1243:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1482:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},4113:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("FileSearch",[["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M4.268 21a2 2 0 0 0 1.727 1H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v3",key:"ms7g94"}],["path",{d:"m9 18-1.5-1.5",key:"1j6qii"}],["circle",{cx:"5",cy:"14",r:"3",key:"ufru5t"}]])},5040:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},6983:(t,e,i)=>{"use strict";function r(t){return"object"==typeof t&&null!==t}i.d(e,{G:()=>r})},13052:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},13089:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("MessageCircleQuestion",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},14186:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},19302:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Network",[["rect",{x:"16",y:"16",width:"6",height:"6",rx:"1",key:"4q2zg0"}],["rect",{x:"2",y:"16",width:"6",height:"6",rx:"1",key:"8cvhb9"}],["rect",{x:"9",y:"2",width:"6",height:"6",rx:"1",key:"1egb70"}],["path",{d:"M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3",key:"1jsf9p"}],["path",{d:"M12 12V8",key:"2874zd"}]])},24357:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},27351:(t,e,i)=>{"use strict";i.d(e,{s:()=>n});var r=i(6983);function n(t){return(0,r.G)(t)&&"offsetHeight"in t}},29621:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},29869:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},32082:(t,e,i)=>{"use strict";i.d(e,{xQ:()=>s});var r=i(12115),n=i(80845);function s(t=!0){let e=(0,r.useContext)(n.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:a,register:o}=e,l=(0,r.useId)();(0,r.useEffect)(()=>{if(t)return o(l)},[t]);let h=(0,r.useCallback)(()=>t&&a&&a(l),[l,a,t]);return!i&&a?[!1,h]:[!0]}},32892:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Plug",[["path",{d:"M12 22v-5",key:"1ega77"}],["path",{d:"M9 8V2",key:"14iosj"}],["path",{d:"M15 8V2",key:"18g5xt"}],["path",{d:"M18 8v5a4 4 0 0 1-4 4h-4a4 4 0 0 1-4-4V8Z",key:"osxo6l"}]])},33109:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},33786:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},34869:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},39022:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]])},40646:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},41684:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Terminal",[["polyline",{points:"4 17 10 11 4 5",key:"akl6gq"}],["line",{x1:"12",x2:"20",y1:"19",y2:"19",key:"q2wloq"}]])},42118:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("FileImage",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["circle",{cx:"10",cy:"12",r:"2",key:"737tya"}],["path",{d:"m20 17-1.296-1.296a2.41 2.41 0 0 0-3.408 0L9 22",key:"wt3hpn"}]])},42337:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("CloudUpload",[["path",{d:"M12 13v8",key:"1l5pq0"}],["path",{d:"M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242",key:"1pljnt"}],["path",{d:"m8 17 4-4 4 4",key:"1quai1"}]])},42355:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},43453:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},45517:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("FileAudio",[["path",{d:"M17.5 22h.5a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v3",key:"rslqgf"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M2 19a2 2 0 1 1 4 0v1a2 2 0 1 1-4 0v-4a6 6 0 0 1 12 0v4a2 2 0 1 1-4 0v-1a2 2 0 1 1 4 0",key:"9f7x3i"}]])},47655:(t,e,i)=>{"use strict";i.d(e,{LM:()=>$,OK:()=>X,VM:()=>k,bL:()=>Y,lr:()=>O});var r=i(12115),n=i(63655),s=i(28905),a=i(46081),o=i(6101),l=i(39033),h=i(94315),u=i(52712),d=i(89367),c=i(85185),f=i(95155),p="ScrollArea",[m,g]=(0,a.A)(p),[y,v]=m(p),b=r.forwardRef((t,e)=>{let{__scopeScrollArea:i,type:s="hover",dir:a,scrollHideDelay:l=600,...u}=t,[d,c]=r.useState(null),[p,m]=r.useState(null),[g,v]=r.useState(null),[b,_]=r.useState(null),[w,x]=r.useState(null),[k,S]=r.useState(0),[A,E]=r.useState(0),[T,C]=r.useState(!1),[P,R]=r.useState(!1),M=(0,o.s)(e,t=>c(t)),D=(0,h.jH)(a);return(0,f.jsx)(y,{scope:i,type:s,dir:D,scrollHideDelay:l,scrollArea:d,viewport:p,onViewportChange:m,content:g,onContentChange:v,scrollbarX:b,onScrollbarXChange:_,scrollbarXEnabled:T,onScrollbarXEnabledChange:C,scrollbarY:w,onScrollbarYChange:x,scrollbarYEnabled:P,onScrollbarYEnabledChange:R,onCornerWidthChange:S,onCornerHeightChange:E,children:(0,f.jsx)(n.sG.div,{dir:D,...u,ref:M,style:{position:"relative","--radix-scroll-area-corner-width":k+"px","--radix-scroll-area-corner-height":A+"px",...t.style}})})});b.displayName=p;var _="ScrollAreaViewport",w=r.forwardRef((t,e)=>{let{__scopeScrollArea:i,children:s,nonce:a,...l}=t,h=v(_,i),u=r.useRef(null),d=(0,o.s)(e,u,h.onViewportChange);return(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,f.jsx)(n.sG.div,{"data-radix-scroll-area-viewport":"",...l,ref:d,style:{overflowX:h.scrollbarXEnabled?"scroll":"hidden",overflowY:h.scrollbarYEnabled?"scroll":"hidden",...t.style},children:(0,f.jsx)("div",{ref:h.onContentChange,style:{minWidth:"100%",display:"table"},children:s})})]})});w.displayName=_;var x="ScrollAreaScrollbar",k=r.forwardRef((t,e)=>{let{forceMount:i,...n}=t,s=v(x,t.__scopeScrollArea),{onScrollbarXEnabledChange:a,onScrollbarYEnabledChange:o}=s,l="horizontal"===t.orientation;return r.useEffect(()=>(l?a(!0):o(!0),()=>{l?a(!1):o(!1)}),[l,a,o]),"hover"===s.type?(0,f.jsx)(S,{...n,ref:e,forceMount:i}):"scroll"===s.type?(0,f.jsx)(A,{...n,ref:e,forceMount:i}):"auto"===s.type?(0,f.jsx)(E,{...n,ref:e,forceMount:i}):"always"===s.type?(0,f.jsx)(T,{...n,ref:e}):null});k.displayName=x;var S=r.forwardRef((t,e)=>{let{forceMount:i,...n}=t,a=v(x,t.__scopeScrollArea),[o,l]=r.useState(!1);return r.useEffect(()=>{let t=a.scrollArea,e=0;if(t){let i=()=>{window.clearTimeout(e),l(!0)},r=()=>{e=window.setTimeout(()=>l(!1),a.scrollHideDelay)};return t.addEventListener("pointerenter",i),t.addEventListener("pointerleave",r),()=>{window.clearTimeout(e),t.removeEventListener("pointerenter",i),t.removeEventListener("pointerleave",r)}}},[a.scrollArea,a.scrollHideDelay]),(0,f.jsx)(s.C,{present:i||o,children:(0,f.jsx)(E,{"data-state":o?"visible":"hidden",...n,ref:e})})}),A=r.forwardRef((t,e)=>{var i,n;let{forceMount:a,...o}=t,l=v(x,t.__scopeScrollArea),h="horizontal"===t.orientation,u=Z(()=>p("SCROLL_END"),100),[d,p]=(i="hidden",n={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},r.useReducer((t,e)=>{let i=n[t][e];return null!=i?i:t},i));return r.useEffect(()=>{if("idle"===d){let t=window.setTimeout(()=>p("HIDE"),l.scrollHideDelay);return()=>window.clearTimeout(t)}},[d,l.scrollHideDelay,p]),r.useEffect(()=>{let t=l.viewport,e=h?"scrollLeft":"scrollTop";if(t){let i=t[e],r=()=>{let r=t[e];i!==r&&(p("SCROLL"),u()),i=r};return t.addEventListener("scroll",r),()=>t.removeEventListener("scroll",r)}},[l.viewport,h,p,u]),(0,f.jsx)(s.C,{present:a||"hidden"!==d,children:(0,f.jsx)(T,{"data-state":"hidden"===d?"hidden":"visible",...o,ref:e,onPointerEnter:(0,c.m)(t.onPointerEnter,()=>p("POINTER_ENTER")),onPointerLeave:(0,c.m)(t.onPointerLeave,()=>p("POINTER_LEAVE"))})})}),E=r.forwardRef((t,e)=>{let i=v(x,t.__scopeScrollArea),{forceMount:n,...a}=t,[o,l]=r.useState(!1),h="horizontal"===t.orientation,u=Z(()=>{if(i.viewport){let t=i.viewport.offsetWidth<i.viewport.scrollWidth,e=i.viewport.offsetHeight<i.viewport.scrollHeight;l(h?t:e)}},10);return q(i.viewport,u),q(i.content,u),(0,f.jsx)(s.C,{present:n||o,children:(0,f.jsx)(T,{"data-state":o?"visible":"hidden",...a,ref:e})})}),T=r.forwardRef((t,e)=>{let{orientation:i="vertical",...n}=t,s=v(x,t.__scopeScrollArea),a=r.useRef(null),o=r.useRef(0),[l,h]=r.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=V(l.viewport,l.content),d={...n,sizes:l,onSizesChange:h,hasThumb:!!(u>0&&u<1),onThumbChange:t=>a.current=t,onThumbPointerUp:()=>o.current=0,onThumbPointerDown:t=>o.current=t};function c(t,e){return function(t,e,i){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",n=U(i),s=e||n/2,a=i.scrollbar.paddingStart+s,o=i.scrollbar.size-i.scrollbar.paddingEnd-(n-s),l=i.content-i.viewport;return W([a,o],"ltr"===r?[0,l]:[-1*l,0])(t)}(t,o.current,l,e)}return"horizontal"===i?(0,f.jsx)(C,{...d,ref:e,onThumbPositionChange:()=>{if(s.viewport&&a.current){let t=N(s.viewport.scrollLeft,l,s.dir);a.current.style.transform="translate3d(".concat(t,"px, 0, 0)")}},onWheelScroll:t=>{s.viewport&&(s.viewport.scrollLeft=t)},onDragScroll:t=>{s.viewport&&(s.viewport.scrollLeft=c(t,s.dir))}}):"vertical"===i?(0,f.jsx)(P,{...d,ref:e,onThumbPositionChange:()=>{if(s.viewport&&a.current){let t=N(s.viewport.scrollTop,l);a.current.style.transform="translate3d(0, ".concat(t,"px, 0)")}},onWheelScroll:t=>{s.viewport&&(s.viewport.scrollTop=t)},onDragScroll:t=>{s.viewport&&(s.viewport.scrollTop=c(t))}}):null}),C=r.forwardRef((t,e)=>{let{sizes:i,onSizesChange:n,...s}=t,a=v(x,t.__scopeScrollArea),[l,h]=r.useState(),u=r.useRef(null),d=(0,o.s)(e,u,a.onScrollbarXChange);return r.useEffect(()=>{u.current&&h(getComputedStyle(u.current))},[u]),(0,f.jsx)(D,{"data-orientation":"horizontal",...s,ref:d,sizes:i,style:{bottom:0,left:"rtl"===a.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===a.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":U(i)+"px",...t.style},onThumbPointerDown:e=>t.onThumbPointerDown(e.x),onDragScroll:e=>t.onDragScroll(e.x),onWheelScroll:(e,i)=>{if(a.viewport){let r=a.viewport.scrollLeft+e.deltaX;t.onWheelScroll(r),function(t,e){return t>0&&t<e}(r,i)&&e.preventDefault()}},onResize:()=>{u.current&&a.viewport&&l&&n({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:u.current.clientWidth,paddingStart:B(l.paddingLeft),paddingEnd:B(l.paddingRight)}})}})}),P=r.forwardRef((t,e)=>{let{sizes:i,onSizesChange:n,...s}=t,a=v(x,t.__scopeScrollArea),[l,h]=r.useState(),u=r.useRef(null),d=(0,o.s)(e,u,a.onScrollbarYChange);return r.useEffect(()=>{u.current&&h(getComputedStyle(u.current))},[u]),(0,f.jsx)(D,{"data-orientation":"vertical",...s,ref:d,sizes:i,style:{top:0,right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":U(i)+"px",...t.style},onThumbPointerDown:e=>t.onThumbPointerDown(e.y),onDragScroll:e=>t.onDragScroll(e.y),onWheelScroll:(e,i)=>{if(a.viewport){let r=a.viewport.scrollTop+e.deltaY;t.onWheelScroll(r),function(t,e){return t>0&&t<e}(r,i)&&e.preventDefault()}},onResize:()=>{u.current&&a.viewport&&l&&n({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:u.current.clientHeight,paddingStart:B(l.paddingTop),paddingEnd:B(l.paddingBottom)}})}})}),[R,M]=m(x),D=r.forwardRef((t,e)=>{let{__scopeScrollArea:i,sizes:s,hasThumb:a,onThumbChange:h,onThumbPointerUp:u,onThumbPointerDown:d,onThumbPositionChange:p,onDragScroll:m,onWheelScroll:g,onResize:y,...b}=t,_=v(x,i),[w,k]=r.useState(null),S=(0,o.s)(e,t=>k(t)),A=r.useRef(null),E=r.useRef(""),T=_.viewport,C=s.content-s.viewport,P=(0,l.c)(g),M=(0,l.c)(p),D=Z(y,10);function z(t){A.current&&m({x:t.clientX-A.current.left,y:t.clientY-A.current.top})}return r.useEffect(()=>{let t=t=>{let e=t.target;(null==w?void 0:w.contains(e))&&P(t,C)};return document.addEventListener("wheel",t,{passive:!1}),()=>document.removeEventListener("wheel",t,{passive:!1})},[T,w,C,P]),r.useEffect(M,[s,M]),q(w,D),q(_.content,D),(0,f.jsx)(R,{scope:i,scrollbar:w,hasThumb:a,onThumbChange:(0,l.c)(h),onThumbPointerUp:(0,l.c)(u),onThumbPositionChange:M,onThumbPointerDown:(0,l.c)(d),children:(0,f.jsx)(n.sG.div,{...b,ref:S,style:{position:"absolute",...b.style},onPointerDown:(0,c.m)(t.onPointerDown,t=>{0===t.button&&(t.target.setPointerCapture(t.pointerId),A.current=w.getBoundingClientRect(),E.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",_.viewport&&(_.viewport.style.scrollBehavior="auto"),z(t))}),onPointerMove:(0,c.m)(t.onPointerMove,z),onPointerUp:(0,c.m)(t.onPointerUp,t=>{let e=t.target;e.hasPointerCapture(t.pointerId)&&e.releasePointerCapture(t.pointerId),document.body.style.webkitUserSelect=E.current,_.viewport&&(_.viewport.style.scrollBehavior=""),A.current=null})})})}),z="ScrollAreaThumb",O=r.forwardRef((t,e)=>{let{forceMount:i,...r}=t,n=M(z,t.__scopeScrollArea);return(0,f.jsx)(s.C,{present:i||n.hasThumb,children:(0,f.jsx)(I,{ref:e,...r})})}),I=r.forwardRef((t,e)=>{let{__scopeScrollArea:i,style:s,...a}=t,l=v(z,i),h=M(z,i),{onThumbPositionChange:u}=h,d=(0,o.s)(e,t=>h.onThumbChange(t)),p=r.useRef(void 0),m=Z(()=>{p.current&&(p.current(),p.current=void 0)},100);return r.useEffect(()=>{let t=l.viewport;if(t){let e=()=>{m(),p.current||(p.current=H(t,u),u())};return u(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[l.viewport,m,u]),(0,f.jsx)(n.sG.div,{"data-state":h.hasThumb?"visible":"hidden",...a,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...s},onPointerDownCapture:(0,c.m)(t.onPointerDownCapture,t=>{let e=t.target.getBoundingClientRect(),i=t.clientX-e.left,r=t.clientY-e.top;h.onThumbPointerDown({x:i,y:r})}),onPointerUp:(0,c.m)(t.onPointerUp,h.onThumbPointerUp)})});O.displayName=z;var L="ScrollAreaCorner",F=r.forwardRef((t,e)=>{let i=v(L,t.__scopeScrollArea),r=!!(i.scrollbarX&&i.scrollbarY);return"scroll"!==i.type&&r?(0,f.jsx)(j,{...t,ref:e}):null});F.displayName=L;var j=r.forwardRef((t,e)=>{let{__scopeScrollArea:i,...s}=t,a=v(L,i),[o,l]=r.useState(0),[h,u]=r.useState(0),d=!!(o&&h);return q(a.scrollbarX,()=>{var t;let e=(null==(t=a.scrollbarX)?void 0:t.offsetHeight)||0;a.onCornerHeightChange(e),u(e)}),q(a.scrollbarY,()=>{var t;let e=(null==(t=a.scrollbarY)?void 0:t.offsetWidth)||0;a.onCornerWidthChange(e),l(e)}),d?(0,f.jsx)(n.sG.div,{...s,ref:e,style:{width:o,height:h,position:"absolute",right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:0,...t.style}}):null});function B(t){return t?parseInt(t,10):0}function V(t,e){let i=t/e;return isNaN(i)?0:i}function U(t){let e=V(t.viewport,t.content),i=t.scrollbar.paddingStart+t.scrollbar.paddingEnd;return Math.max((t.scrollbar.size-i)*e,18)}function N(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",r=U(e),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd,s=e.scrollbar.size-n,a=e.content-e.viewport,o=(0,d.q)(t,"ltr"===i?[0,a]:[-1*a,0]);return W([0,a],[0,s-r])(o)}function W(t,e){return i=>{if(t[0]===t[1]||e[0]===e[1])return e[0];let r=(e[1]-e[0])/(t[1]-t[0]);return e[0]+r*(i-t[0])}}var H=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},i={left:t.scrollLeft,top:t.scrollTop},r=0;return!function n(){let s={left:t.scrollLeft,top:t.scrollTop},a=i.left!==s.left,o=i.top!==s.top;(a||o)&&e(),i=s,r=window.requestAnimationFrame(n)}(),()=>window.cancelAnimationFrame(r)};function Z(t,e){let i=(0,l.c)(t),n=r.useRef(0);return r.useEffect(()=>()=>window.clearTimeout(n.current),[]),r.useCallback(()=>{window.clearTimeout(n.current),n.current=window.setTimeout(i,e)},[i,e])}function q(t,e){let i=(0,l.c)(e);(0,u.N)(()=>{let e=0;if(t){let r=new ResizeObserver(()=>{cancelAnimationFrame(e),e=window.requestAnimationFrame(i)});return r.observe(t),()=>{window.cancelAnimationFrame(e),r.unobserve(t)}}},[t,i])}var Y=b,$=w,X=F},47924:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},48313:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]])},49992:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("FilePen",[["path",{d:"M12.5 22H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v9.5",key:"1couwa"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M13.378 15.626a1 1 0 1 0-3.004-3.004l-5.01 5.012a2 2 0 0 0-.506.854l-.837 2.87a.5.5 0 0 0 .62.62l2.87-.837a2 2 0 0 0 .854-.506z",key:"1y4qbx"}]])},50492:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Paperclip",[["path",{d:"M13.234 20.252 21 12.3",key:"1cbrk9"}],["path",{d:"m16 6-8.414 8.586a2 2 0 0 0 0 2.828 2 2 0 0 0 2.828 0l8.414-8.586a4 4 0 0 0 0-5.656 4 4 0 0 0-5.656 0l-8.415 8.585a6 6 0 1 0 8.486 8.486",key:"1pkts6"}]])},50773:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("FilePlus",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M9 15h6",key:"cctwl0"}],["path",{d:"M12 18v-6",key:"17g6i2"}]])},51508:(t,e,i)=>{"use strict";i.d(e,{Q:()=>r});let r=(0,i(12115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},53311:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},54213:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},57434:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},59311:(t,e,i)=>{var r=i(44134).hp,n=i(49509);t.exports=(function t(e,i,r){function n(a,o){if(!i[a]){if(!e[a]){if(s)return s(a,!0);var l=Error("Cannot find module '"+a+"'");throw l.code="MODULE_NOT_FOUND",l}var h=i[a]={exports:{}};e[a][0].call(h.exports,function(t){return n(e[a][1][t]||t)},h,h.exports,t,e,i,r)}return i[a].exports}for(var s=void 0,a=0;a<r.length;a++)n(r[a]);return n})({1:[function(t,e,i){"use strict";var r=t("./utils"),n=t("./support"),s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";i.encode=function(t){for(var e,i,n,a,o,l,h,u=[],d=0,c=t.length,f=c,p="string"!==r.getTypeOf(t);d<t.length;)f=c-d,n=p?(e=t[d++],i=d<c?t[d++]:0,d<c?t[d++]:0):(e=t.charCodeAt(d++),i=d<c?t.charCodeAt(d++):0,d<c?t.charCodeAt(d++):0),a=e>>2,o=(3&e)<<4|i>>4,l=1<f?(15&i)<<2|n>>6:64,h=2<f?63&n:64,u.push(s.charAt(a)+s.charAt(o)+s.charAt(l)+s.charAt(h));return u.join("")},i.decode=function(t){var e,i,r,a,o,l,h=0,u=0,d="data:";if(t.substr(0,d.length)===d)throw Error("Invalid base64 input, it looks like a data url.");var c,f=3*(t=t.replace(/[^A-Za-z0-9+/=]/g,"")).length/4;if(t.charAt(t.length-1)===s.charAt(64)&&f--,t.charAt(t.length-2)===s.charAt(64)&&f--,f%1!=0)throw Error("Invalid base64 input, bad content length.");for(c=n.uint8array?new Uint8Array(0|f):Array(0|f);h<t.length;)e=s.indexOf(t.charAt(h++))<<2|(a=s.indexOf(t.charAt(h++)))>>4,i=(15&a)<<4|(o=s.indexOf(t.charAt(h++)))>>2,r=(3&o)<<6|(l=s.indexOf(t.charAt(h++))),c[u++]=e,64!==o&&(c[u++]=i),64!==l&&(c[u++]=r);return c}},{"./support":30,"./utils":32}],2:[function(t,e,i){"use strict";var r=t("./external"),n=t("./stream/DataWorker"),s=t("./stream/Crc32Probe"),a=t("./stream/DataLengthProbe");function o(t,e,i,r,n){this.compressedSize=t,this.uncompressedSize=e,this.crc32=i,this.compression=r,this.compressedContent=n}o.prototype={getContentWorker:function(){var t=new n(r.Promise.resolve(this.compressedContent)).pipe(this.compression.uncompressWorker()).pipe(new a("data_length")),e=this;return t.on("end",function(){if(this.streamInfo.data_length!==e.uncompressedSize)throw Error("Bug : uncompressed data size mismatch")}),t},getCompressedWorker:function(){return new n(r.Promise.resolve(this.compressedContent)).withStreamInfo("compressedSize",this.compressedSize).withStreamInfo("uncompressedSize",this.uncompressedSize).withStreamInfo("crc32",this.crc32).withStreamInfo("compression",this.compression)}},o.createWorkerFrom=function(t,e,i){return t.pipe(new s).pipe(new a("uncompressedSize")).pipe(e.compressWorker(i)).pipe(new a("compressedSize")).withStreamInfo("compression",e)},e.exports=o},{"./external":6,"./stream/Crc32Probe":25,"./stream/DataLengthProbe":26,"./stream/DataWorker":27}],3:[function(t,e,i){"use strict";var r=t("./stream/GenericWorker");i.STORE={magic:"\0\0",compressWorker:function(){return new r("STORE compression")},uncompressWorker:function(){return new r("STORE decompression")}},i.DEFLATE=t("./flate")},{"./flate":7,"./stream/GenericWorker":28}],4:[function(t,e,i){"use strict";var r=t("./utils"),n=function(){for(var t,e=[],i=0;i<256;i++){t=i;for(var r=0;r<8;r++)t=1&t?0xedb88320^t>>>1:t>>>1;e[i]=t}return e}();e.exports=function(t,e){return void 0!==t&&t.length?"string"!==r.getTypeOf(t)?function(t,e,i,r){var s=0+i;t^=-1;for(var a=0;a<s;a++)t=t>>>8^n[255&(t^e[a])];return -1^t}(0|e,t,t.length,0):function(t,e,i,r){var s=0+i;t^=-1;for(var a=0;a<s;a++)t=t>>>8^n[255&(t^e.charCodeAt(a))];return -1^t}(0|e,t,t.length,0):0}},{"./utils":32}],5:[function(t,e,i){"use strict";i.base64=!1,i.binary=!1,i.dir=!1,i.createFolders=!0,i.date=null,i.compression=null,i.compressionOptions=null,i.comment=null,i.unixPermissions=null,i.dosPermissions=null},{}],6:[function(t,e,i){"use strict";var r=null;e.exports={Promise:"undefined"!=typeof Promise?Promise:t("lie")}},{lie:37}],7:[function(t,e,i){"use strict";var r="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Uint32Array,n=t("pako"),s=t("./utils"),a=t("./stream/GenericWorker"),o=r?"uint8array":"array";function l(t,e){a.call(this,"FlateWorker/"+t),this._pako=null,this._pakoAction=t,this._pakoOptions=e,this.meta={}}i.magic="\b\0",s.inherits(l,a),l.prototype.processChunk=function(t){this.meta=t.meta,null===this._pako&&this._createPako(),this._pako.push(s.transformTo(o,t.data),!1)},l.prototype.flush=function(){a.prototype.flush.call(this),null===this._pako&&this._createPako(),this._pako.push([],!0)},l.prototype.cleanUp=function(){a.prototype.cleanUp.call(this),this._pako=null},l.prototype._createPako=function(){this._pako=new n[this._pakoAction]({raw:!0,level:this._pakoOptions.level||-1});var t=this;this._pako.onData=function(e){t.push({data:e,meta:t.meta})}},i.compressWorker=function(t){return new l("Deflate",t)},i.uncompressWorker=function(){return new l("Inflate",{})}},{"./stream/GenericWorker":28,"./utils":32,pako:38}],8:[function(t,e,i){"use strict";function r(t,e){var i,r="";for(i=0;i<e;i++)r+=String.fromCharCode(255&t),t>>>=8;return r}function n(t,e,i,n,a,u){var d,c,f,p,m=t.file,g=t.compression,y=u!==o.utf8encode,v=s.transformTo("string",u(m.name)),b=s.transformTo("string",o.utf8encode(m.name)),_=m.comment,w=s.transformTo("string",u(_)),x=s.transformTo("string",o.utf8encode(_)),k=b.length!==m.name.length,S=x.length!==_.length,A="",E="",T="",C=m.dir,P=m.date,R={crc32:0,compressedSize:0,uncompressedSize:0};e&&!i||(R.crc32=t.crc32,R.compressedSize=t.compressedSize,R.uncompressedSize=t.uncompressedSize);var M=0;e&&(M|=8),!y&&(k||S)&&(M|=2048);var D=0,z=0;C&&(D|=16),"UNIX"===a?(z=798,D|=(d=m.unixPermissions,c=d,d||(c=C?16893:33204),(65535&c)<<16)):(z=20,D|=63&(m.dosPermissions||0)),f=(P.getUTCHours()<<6|P.getUTCMinutes())<<5|P.getUTCSeconds()/2,p=(P.getUTCFullYear()-1980<<4|P.getUTCMonth()+1)<<5|P.getUTCDate(),k&&(E=r(1,1)+r(l(v),4)+b,A+="up"+r(E.length,2)+E),S&&(T=r(1,1)+r(l(w),4)+x,A+="uc"+r(T.length,2)+T);var O="";return O+="\n\0",O+=r(M,2),O+=g.magic,O+=r(f,2),O+=r(p,2),O+=r(R.crc32,4),O+=r(R.compressedSize,4),O+=r(R.uncompressedSize,4),O+=r(v.length,2),O+=r(A.length,2),{fileRecord:h.LOCAL_FILE_HEADER+O+v+A,dirRecord:h.CENTRAL_FILE_HEADER+r(z,2)+O+r(w.length,2)+"\0\0\0\0"+r(D,4)+r(n,4)+v+A+w}}var s=t("../utils"),a=t("../stream/GenericWorker"),o=t("../utf8"),l=t("../crc32"),h=t("../signature");function u(t,e,i,r){a.call(this,"ZipFileWorker"),this.bytesWritten=0,this.zipComment=e,this.zipPlatform=i,this.encodeFileName=r,this.streamFiles=t,this.accumulate=!1,this.contentBuffer=[],this.dirRecords=[],this.currentSourceOffset=0,this.entriesCount=0,this.currentFile=null,this._sources=[]}s.inherits(u,a),u.prototype.push=function(t){var e=t.meta.percent||0,i=this.entriesCount,r=this._sources.length;this.accumulate?this.contentBuffer.push(t):(this.bytesWritten+=t.data.length,a.prototype.push.call(this,{data:t.data,meta:{currentFile:this.currentFile,percent:i?(e+100*(i-r-1))/i:100}}))},u.prototype.openedSource=function(t){this.currentSourceOffset=this.bytesWritten,this.currentFile=t.file.name;var e=this.streamFiles&&!t.file.dir;if(e){var i=n(t,e,!1,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);this.push({data:i.fileRecord,meta:{percent:0}})}else this.accumulate=!0},u.prototype.closedSource=function(t){this.accumulate=!1;var e=this.streamFiles&&!t.file.dir,i=n(t,e,!0,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);if(this.dirRecords.push(i.dirRecord),e)this.push({data:h.DATA_DESCRIPTOR+r(t.crc32,4)+r(t.compressedSize,4)+r(t.uncompressedSize,4),meta:{percent:100}});else for(this.push({data:i.fileRecord,meta:{percent:0}});this.contentBuffer.length;)this.push(this.contentBuffer.shift());this.currentFile=null},u.prototype.flush=function(){for(var t,e,i,n,a=this.bytesWritten,o=0;o<this.dirRecords.length;o++)this.push({data:this.dirRecords[o],meta:{percent:100}});var l=this.bytesWritten-a,u=(t=this.dirRecords.length,e=this.zipComment,i=this.encodeFileName,n=s.transformTo("string",i(e)),h.CENTRAL_DIRECTORY_END+"\0\0\0\0"+r(t,2)+r(t,2)+r(l,4)+r(a,4)+r(n.length,2)+n);this.push({data:u,meta:{percent:100}})},u.prototype.prepareNextSource=function(){this.previous=this._sources.shift(),this.openedSource(this.previous.streamInfo),this.isPaused?this.previous.pause():this.previous.resume()},u.prototype.registerPrevious=function(t){this._sources.push(t);var e=this;return t.on("data",function(t){e.processChunk(t)}),t.on("end",function(){e.closedSource(e.previous.streamInfo),e._sources.length?e.prepareNextSource():e.end()}),t.on("error",function(t){e.error(t)}),this},u.prototype.resume=function(){return!!a.prototype.resume.call(this)&&(!this.previous&&this._sources.length?(this.prepareNextSource(),!0):this.previous||this._sources.length||this.generatedError?void 0:(this.end(),!0))},u.prototype.error=function(t){var e=this._sources;if(!a.prototype.error.call(this,t))return!1;for(var i=0;i<e.length;i++)try{e[i].error(t)}catch(t){}return!0},u.prototype.lock=function(){a.prototype.lock.call(this);for(var t=this._sources,e=0;e<t.length;e++)t[e].lock()},e.exports=u},{"../crc32":4,"../signature":23,"../stream/GenericWorker":28,"../utf8":31,"../utils":32}],9:[function(t,e,i){"use strict";var r=t("../compressions"),n=t("./ZipFileWorker");i.generateWorker=function(t,e,i){var s=new n(e.streamFiles,i,e.platform,e.encodeFileName),a=0;try{t.forEach(function(t,i){a++;var n=function(t,e){var i=t||e,n=r[i];if(!n)throw Error(i+" is not a valid compression method !");return n}(i.options.compression,e.compression),o=i.options.compressionOptions||e.compressionOptions||{},l=i.dir,h=i.date;i._compressWorker(n,o).withStreamInfo("file",{name:t,dir:l,date:h,comment:i.comment||"",unixPermissions:i.unixPermissions,dosPermissions:i.dosPermissions}).pipe(s)}),s.entriesCount=a}catch(t){s.error(t)}return s}},{"../compressions":3,"./ZipFileWorker":8}],10:[function(t,e,i){"use strict";function r(){if(!(this instanceof r))return new r;if(arguments.length)throw Error("The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.");this.files=Object.create(null),this.comment=null,this.root="",this.clone=function(){var t=new r;for(var e in this)"function"!=typeof this[e]&&(t[e]=this[e]);return t}}(r.prototype=t("./object")).loadAsync=t("./load"),r.support=t("./support"),r.defaults=t("./defaults"),r.version="3.10.1",r.loadAsync=function(t,e){return(new r).loadAsync(t,e)},r.external=t("./external"),e.exports=r},{"./defaults":5,"./external":6,"./load":11,"./object":15,"./support":30}],11:[function(t,e,i){"use strict";var r=t("./utils"),n=t("./external"),s=t("./utf8"),a=t("./zipEntries"),o=t("./stream/Crc32Probe"),l=t("./nodejsUtils");e.exports=function(t,e){var i=this;return e=r.extend(e||{},{base64:!1,checkCRC32:!1,optimizedBinaryString:!1,createFolders:!1,decodeFileName:s.utf8decode}),l.isNode&&l.isStream(t)?n.Promise.reject(Error("JSZip can't accept a stream when loading a zip file.")):r.prepareContent("the loaded zip file",t,!0,e.optimizedBinaryString,e.base64).then(function(t){var i=new a(e);return i.load(t),i}).then(function(t){var i=[n.Promise.resolve(t)],r=t.files;if(e.checkCRC32)for(var s=0;s<r.length;s++)i.push(function(t){return new n.Promise(function(e,i){var r=t.decompressed.getContentWorker().pipe(new o);r.on("error",function(t){i(t)}).on("end",function(){r.streamInfo.crc32!==t.decompressed.crc32?i(Error("Corrupted zip : CRC32 mismatch")):e()}).resume()})}(r[s]));return n.Promise.all(i)}).then(function(t){for(var n=t.shift(),s=n.files,a=0;a<s.length;a++){var o=s[a],l=o.fileNameStr,h=r.resolve(o.fileNameStr);i.file(h,o.decompressed,{binary:!0,optimizedBinaryString:!0,date:o.date,dir:o.dir,comment:o.fileCommentStr.length?o.fileCommentStr:null,unixPermissions:o.unixPermissions,dosPermissions:o.dosPermissions,createFolders:e.createFolders}),o.dir||(i.file(h).unsafeOriginalName=l)}return n.zipComment.length&&(i.comment=n.zipComment),i})}},{"./external":6,"./nodejsUtils":14,"./stream/Crc32Probe":25,"./utf8":31,"./utils":32,"./zipEntries":33}],12:[function(t,e,i){"use strict";var r=t("../utils"),n=t("../stream/GenericWorker");function s(t,e){n.call(this,"Nodejs stream input adapter for "+t),this._upstreamEnded=!1,this._bindStream(e)}r.inherits(s,n),s.prototype._bindStream=function(t){var e=this;(this._stream=t).pause(),t.on("data",function(t){e.push({data:t,meta:{percent:0}})}).on("error",function(t){e.isPaused?this.generatedError=t:e.error(t)}).on("end",function(){e.isPaused?e._upstreamEnded=!0:e.end()})},s.prototype.pause=function(){return!!n.prototype.pause.call(this)&&(this._stream.pause(),!0)},s.prototype.resume=function(){return!!n.prototype.resume.call(this)&&(this._upstreamEnded?this.end():this._stream.resume(),!0)},e.exports=s},{"../stream/GenericWorker":28,"../utils":32}],13:[function(t,e,i){"use strict";var r=t("readable-stream").Readable;function n(t,e,i){r.call(this,e),this._helper=t;var n=this;t.on("data",function(t,e){n.push(t)||n._helper.pause(),i&&i(e)}).on("error",function(t){n.emit("error",t)}).on("end",function(){n.push(null)})}t("../utils").inherits(n,r),n.prototype._read=function(){this._helper.resume()},e.exports=n},{"../utils":32,"readable-stream":16}],14:[function(t,e,i){"use strict";e.exports={isNode:void 0!==r,newBufferFrom:function(t,e){if(r.from&&r.from!==Uint8Array.from)return r.from(t,e);if("number"==typeof t)throw Error('The "data" argument must not be a number');return new r(t,e)},allocBuffer:function(t){if(r.alloc)return r.alloc(t);var e=new r(t);return e.fill(0),e},isBuffer:function(t){return r.isBuffer(t)},isStream:function(t){return t&&"function"==typeof t.on&&"function"==typeof t.pause&&"function"==typeof t.resume}}},{}],15:[function(t,e,i){"use strict";function r(t,e,i){var r,n=s.getTypeOf(e),o=s.extend(i||{},l);o.date=o.date||new Date,null!==o.compression&&(o.compression=o.compression.toUpperCase()),"string"==typeof o.unixPermissions&&(o.unixPermissions=parseInt(o.unixPermissions,8)),o.unixPermissions&&16384&o.unixPermissions&&(o.dir=!0),o.dosPermissions&&16&o.dosPermissions&&(o.dir=!0),o.dir&&(t=m(t)),o.createFolders&&(r=p(t))&&g.call(this,r,!0);var d="string"===n&&!1===o.binary&&!1===o.base64;i&&void 0!==i.binary||(o.binary=!d),(e instanceof h&&0===e.uncompressedSize||o.dir||!e||0===e.length)&&(o.base64=!1,o.binary=!0,e="",o.compression="STORE",n="string");var y=null;y=e instanceof h||e instanceof a?e:c.isNode&&c.isStream(e)?new f(t,e):s.prepareContent(t,e,o.binary,o.optimizedBinaryString,o.base64);var v=new u(t,y,o);this.files[t]=v}var n=t("./utf8"),s=t("./utils"),a=t("./stream/GenericWorker"),o=t("./stream/StreamHelper"),l=t("./defaults"),h=t("./compressedObject"),u=t("./zipObject"),d=t("./generate"),c=t("./nodejsUtils"),f=t("./nodejs/NodejsStreamInputAdapter"),p=function(t){"/"===t.slice(-1)&&(t=t.substring(0,t.length-1));var e=t.lastIndexOf("/");return 0<e?t.substring(0,e):""},m=function(t){return"/"!==t.slice(-1)&&(t+="/"),t},g=function(t,e){return e=void 0!==e?e:l.createFolders,t=m(t),this.files[t]||r.call(this,t,null,{dir:!0,createFolders:e}),this.files[t]};function y(t){return"[object RegExp]"===Object.prototype.toString.call(t)}e.exports={load:function(){throw Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},forEach:function(t){var e,i,r;for(e in this.files)r=this.files[e],(i=e.slice(this.root.length,e.length))&&e.slice(0,this.root.length)===this.root&&t(i,r)},filter:function(t){var e=[];return this.forEach(function(i,r){t(i,r)&&e.push(r)}),e},file:function(t,e,i){if(1!=arguments.length)return t=this.root+t,r.call(this,t,e,i),this;if(y(t)){var n=t;return this.filter(function(t,e){return!e.dir&&n.test(t)})}var s=this.files[this.root+t];return s&&!s.dir?s:null},folder:function(t){if(!t)return this;if(y(t))return this.filter(function(e,i){return i.dir&&t.test(e)});var e=this.root+t,i=g.call(this,e),r=this.clone();return r.root=i.name,r},remove:function(t){t=this.root+t;var e=this.files[t];if(e||("/"!==t.slice(-1)&&(t+="/"),e=this.files[t]),e&&!e.dir)delete this.files[t];else for(var i=this.filter(function(e,i){return i.name.slice(0,t.length)===t}),r=0;r<i.length;r++)delete this.files[i[r].name];return this},generate:function(){throw Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},generateInternalStream:function(t){var e,i={};try{if((i=s.extend(t||{},{streamFiles:!1,compression:"STORE",compressionOptions:null,type:"",platform:"DOS",comment:null,mimeType:"application/zip",encodeFileName:n.utf8encode})).type=i.type.toLowerCase(),i.compression=i.compression.toUpperCase(),"binarystring"===i.type&&(i.type="string"),!i.type)throw Error("No output type specified.");s.checkSupport(i.type),"darwin"!==i.platform&&"freebsd"!==i.platform&&"linux"!==i.platform&&"sunos"!==i.platform||(i.platform="UNIX"),"win32"===i.platform&&(i.platform="DOS");var r=i.comment||this.comment||"";e=d.generateWorker(this,i,r)}catch(t){(e=new a("error")).error(t)}return new o(e,i.type||"string",i.mimeType)},generateAsync:function(t,e){return this.generateInternalStream(t).accumulate(e)},generateNodeStream:function(t,e){return(t=t||{}).type||(t.type="nodebuffer"),this.generateInternalStream(t).toNodejsStream(e)}}},{"./compressedObject":2,"./defaults":5,"./generate":9,"./nodejs/NodejsStreamInputAdapter":12,"./nodejsUtils":14,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31,"./utils":32,"./zipObject":35}],16:[function(t,e,i){"use strict";e.exports=t("stream")},{stream:void 0}],17:[function(t,e,i){"use strict";var r=t("./DataReader");function n(t){r.call(this,t);for(var e=0;e<this.data.length;e++)t[e]=255&t[e]}t("../utils").inherits(n,r),n.prototype.byteAt=function(t){return this.data[this.zero+t]},n.prototype.lastIndexOfSignature=function(t){for(var e=t.charCodeAt(0),i=t.charCodeAt(1),r=t.charCodeAt(2),n=t.charCodeAt(3),s=this.length-4;0<=s;--s)if(this.data[s]===e&&this.data[s+1]===i&&this.data[s+2]===r&&this.data[s+3]===n)return s-this.zero;return -1},n.prototype.readAndCheckSignature=function(t){var e=t.charCodeAt(0),i=t.charCodeAt(1),r=t.charCodeAt(2),n=t.charCodeAt(3),s=this.readData(4);return e===s[0]&&i===s[1]&&r===s[2]&&n===s[3]},n.prototype.readData=function(t){if(this.checkOffset(t),0===t)return[];var e=this.data.slice(this.zero+this.index,this.zero+this.index+t);return this.index+=t,e},e.exports=n},{"../utils":32,"./DataReader":18}],18:[function(t,e,i){"use strict";var r=t("../utils");function n(t){this.data=t,this.length=t.length,this.index=0,this.zero=0}n.prototype={checkOffset:function(t){this.checkIndex(this.index+t)},checkIndex:function(t){if(this.length<this.zero+t||t<0)throw Error("End of data reached (data length = "+this.length+", asked index = "+t+"). Corrupted zip ?")},setIndex:function(t){this.checkIndex(t),this.index=t},skip:function(t){this.setIndex(this.index+t)},byteAt:function(){},readInt:function(t){var e,i=0;for(this.checkOffset(t),e=this.index+t-1;e>=this.index;e--)i=(i<<8)+this.byteAt(e);return this.index+=t,i},readString:function(t){return r.transformTo("string",this.readData(t))},readData:function(){},lastIndexOfSignature:function(){},readAndCheckSignature:function(){},readDate:function(){var t=this.readInt(4);return new Date(Date.UTC(1980+(t>>25&127),(t>>21&15)-1,t>>16&31,t>>11&31,t>>5&63,(31&t)<<1))}},e.exports=n},{"../utils":32}],19:[function(t,e,i){"use strict";var r=t("./Uint8ArrayReader");function n(t){r.call(this,t)}t("../utils").inherits(n,r),n.prototype.readData=function(t){this.checkOffset(t);var e=this.data.slice(this.zero+this.index,this.zero+this.index+t);return this.index+=t,e},e.exports=n},{"../utils":32,"./Uint8ArrayReader":21}],20:[function(t,e,i){"use strict";var r=t("./DataReader");function n(t){r.call(this,t)}t("../utils").inherits(n,r),n.prototype.byteAt=function(t){return this.data.charCodeAt(this.zero+t)},n.prototype.lastIndexOfSignature=function(t){return this.data.lastIndexOf(t)-this.zero},n.prototype.readAndCheckSignature=function(t){return t===this.readData(4)},n.prototype.readData=function(t){this.checkOffset(t);var e=this.data.slice(this.zero+this.index,this.zero+this.index+t);return this.index+=t,e},e.exports=n},{"../utils":32,"./DataReader":18}],21:[function(t,e,i){"use strict";var r=t("./ArrayReader");function n(t){r.call(this,t)}t("../utils").inherits(n,r),n.prototype.readData=function(t){if(this.checkOffset(t),0===t)return new Uint8Array(0);var e=this.data.subarray(this.zero+this.index,this.zero+this.index+t);return this.index+=t,e},e.exports=n},{"../utils":32,"./ArrayReader":17}],22:[function(t,e,i){"use strict";var r=t("../utils"),n=t("../support"),s=t("./ArrayReader"),a=t("./StringReader"),o=t("./NodeBufferReader"),l=t("./Uint8ArrayReader");e.exports=function(t){var e=r.getTypeOf(t);return r.checkSupport(e),"string"!==e||n.uint8array?"nodebuffer"===e?new o(t):n.uint8array?new l(r.transformTo("uint8array",t)):new s(r.transformTo("array",t)):new a(t)}},{"../support":30,"../utils":32,"./ArrayReader":17,"./NodeBufferReader":19,"./StringReader":20,"./Uint8ArrayReader":21}],23:[function(t,e,i){"use strict";i.LOCAL_FILE_HEADER="PK\x03\x04",i.CENTRAL_FILE_HEADER="PK\x01\x02",i.CENTRAL_DIRECTORY_END="PK\x05\x06",i.ZIP64_CENTRAL_DIRECTORY_LOCATOR="PK\x06\x07",i.ZIP64_CENTRAL_DIRECTORY_END="PK\x06\x06",i.DATA_DESCRIPTOR="PK\x07\b"},{}],24:[function(t,e,i){"use strict";var r=t("./GenericWorker"),n=t("../utils");function s(t){r.call(this,"ConvertWorker to "+t),this.destType=t}n.inherits(s,r),s.prototype.processChunk=function(t){this.push({data:n.transformTo(this.destType,t.data),meta:t.meta})},e.exports=s},{"../utils":32,"./GenericWorker":28}],25:[function(t,e,i){"use strict";var r=t("./GenericWorker"),n=t("../crc32");function s(){r.call(this,"Crc32Probe"),this.withStreamInfo("crc32",0)}t("../utils").inherits(s,r),s.prototype.processChunk=function(t){this.streamInfo.crc32=n(t.data,this.streamInfo.crc32||0),this.push(t)},e.exports=s},{"../crc32":4,"../utils":32,"./GenericWorker":28}],26:[function(t,e,i){"use strict";var r=t("../utils"),n=t("./GenericWorker");function s(t){n.call(this,"DataLengthProbe for "+t),this.propName=t,this.withStreamInfo(t,0)}r.inherits(s,n),s.prototype.processChunk=function(t){if(t){var e=this.streamInfo[this.propName]||0;this.streamInfo[this.propName]=e+t.data.length}n.prototype.processChunk.call(this,t)},e.exports=s},{"../utils":32,"./GenericWorker":28}],27:[function(t,e,i){"use strict";var r=t("../utils"),n=t("./GenericWorker");function s(t){n.call(this,"DataWorker");var e=this;this.dataIsReady=!1,this.index=0,this.max=0,this.data=null,this.type="",this._tickScheduled=!1,t.then(function(t){e.dataIsReady=!0,e.data=t,e.max=t&&t.length||0,e.type=r.getTypeOf(t),e.isPaused||e._tickAndRepeat()},function(t){e.error(t)})}r.inherits(s,n),s.prototype.cleanUp=function(){n.prototype.cleanUp.call(this),this.data=null},s.prototype.resume=function(){return!!n.prototype.resume.call(this)&&(!this._tickScheduled&&this.dataIsReady&&(this._tickScheduled=!0,r.delay(this._tickAndRepeat,[],this)),!0)},s.prototype._tickAndRepeat=function(){this._tickScheduled=!1,this.isPaused||this.isFinished||(this._tick(),this.isFinished||(r.delay(this._tickAndRepeat,[],this),this._tickScheduled=!0))},s.prototype._tick=function(){if(this.isPaused||this.isFinished)return!1;var t=null,e=Math.min(this.max,this.index+16384);if(this.index>=this.max)return this.end();switch(this.type){case"string":t=this.data.substring(this.index,e);break;case"uint8array":t=this.data.subarray(this.index,e);break;case"array":case"nodebuffer":t=this.data.slice(this.index,e)}return this.index=e,this.push({data:t,meta:{percent:this.max?this.index/this.max*100:0}})},e.exports=s},{"../utils":32,"./GenericWorker":28}],28:[function(t,e,i){"use strict";function r(t){this.name=t||"default",this.streamInfo={},this.generatedError=null,this.extraStreamInfo={},this.isPaused=!0,this.isFinished=!1,this.isLocked=!1,this._listeners={data:[],end:[],error:[]},this.previous=null}r.prototype={push:function(t){this.emit("data",t)},end:function(){if(this.isFinished)return!1;this.flush();try{this.emit("end"),this.cleanUp(),this.isFinished=!0}catch(t){this.emit("error",t)}return!0},error:function(t){return!this.isFinished&&(this.isPaused?this.generatedError=t:(this.isFinished=!0,this.emit("error",t),this.previous&&this.previous.error(t),this.cleanUp()),!0)},on:function(t,e){return this._listeners[t].push(e),this},cleanUp:function(){this.streamInfo=this.generatedError=this.extraStreamInfo=null,this._listeners=[]},emit:function(t,e){if(this._listeners[t])for(var i=0;i<this._listeners[t].length;i++)this._listeners[t][i].call(this,e)},pipe:function(t){return t.registerPrevious(this)},registerPrevious:function(t){if(this.isLocked)throw Error("The stream '"+this+"' has already been used.");this.streamInfo=t.streamInfo,this.mergeStreamInfo(),this.previous=t;var e=this;return t.on("data",function(t){e.processChunk(t)}),t.on("end",function(){e.end()}),t.on("error",function(t){e.error(t)}),this},pause:function(){return!this.isPaused&&!this.isFinished&&(this.isPaused=!0,this.previous&&this.previous.pause(),!0)},resume:function(){if(!this.isPaused||this.isFinished)return!1;var t=this.isPaused=!1;return this.generatedError&&(this.error(this.generatedError),t=!0),this.previous&&this.previous.resume(),!t},flush:function(){},processChunk:function(t){this.push(t)},withStreamInfo:function(t,e){return this.extraStreamInfo[t]=e,this.mergeStreamInfo(),this},mergeStreamInfo:function(){for(var t in this.extraStreamInfo)Object.prototype.hasOwnProperty.call(this.extraStreamInfo,t)&&(this.streamInfo[t]=this.extraStreamInfo[t])},lock:function(){if(this.isLocked)throw Error("The stream '"+this+"' has already been used.");this.isLocked=!0,this.previous&&this.previous.lock()},toString:function(){var t="Worker "+this.name;return this.previous?this.previous+" -> "+t:t}},e.exports=r},{}],29:[function(t,e,i){"use strict";var n=t("../utils"),s=t("./ConvertWorker"),a=t("./GenericWorker"),o=t("../base64"),l=t("../support"),h=t("../external"),u=null;if(l.nodestream)try{u=t("../nodejs/NodejsStreamOutputAdapter")}catch(t){}function d(t,e,i){var r=e;switch(e){case"blob":case"arraybuffer":r="uint8array";break;case"base64":r="string"}try{this._internalType=r,this._outputType=e,this._mimeType=i,n.checkSupport(r),this._worker=t.pipe(new s(r)),t.lock()}catch(t){this._worker=new a("error"),this._worker.error(t)}}d.prototype={accumulate:function(t){var e;return e=this,new h.Promise(function(i,s){var a=[],l=e._internalType,h=e._outputType,u=e._mimeType;e.on("data",function(e,i){a.push(e),t&&t(i)}).on("error",function(t){a=[],s(t)}).on("end",function(){try{var t=function(t,e,i){switch(t){case"blob":return n.newBlob(n.transformTo("arraybuffer",e),i);case"base64":return o.encode(e);default:return n.transformTo(t,e)}}(h,function(t,e){var i,n=0,s=null,a=0;for(i=0;i<e.length;i++)a+=e[i].length;switch(t){case"string":return e.join("");case"array":return Array.prototype.concat.apply([],e);case"uint8array":for(s=new Uint8Array(a),i=0;i<e.length;i++)s.set(e[i],n),n+=e[i].length;return s;case"nodebuffer":return r.concat(e);default:throw Error("concat : unsupported type '"+t+"'")}}(l,a),u);i(t)}catch(t){s(t)}a=[]}).resume()})},on:function(t,e){var i=this;return"data"===t?this._worker.on(t,function(t){e.call(i,t.data,t.meta)}):this._worker.on(t,function(){n.delay(e,arguments,i)}),this},resume:function(){return n.delay(this._worker.resume,[],this._worker),this},pause:function(){return this._worker.pause(),this},toNodejsStream:function(t){if(n.checkSupport("nodestream"),"nodebuffer"!==this._outputType)throw Error(this._outputType+" is not supported by this method");return new u(this,{objectMode:"nodebuffer"!==this._outputType},t)}},e.exports=d},{"../base64":1,"../external":6,"../nodejs/NodejsStreamOutputAdapter":13,"../support":30,"../utils":32,"./ConvertWorker":24,"./GenericWorker":28}],30:[function(t,e,i){"use strict";if(i.base64=!0,i.array=!0,i.string=!0,i.arraybuffer="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array,i.nodebuffer=void 0!==r,i.uint8array="undefined"!=typeof Uint8Array,"undefined"==typeof ArrayBuffer)i.blob=!1;else{var n=new ArrayBuffer(0);try{i.blob=0===new Blob([n],{type:"application/zip"}).size}catch(t){try{var s=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);s.append(n),i.blob=0===s.getBlob("application/zip").size}catch(t){i.blob=!1}}}try{i.nodestream=!!t("readable-stream").Readable}catch(t){i.nodestream=!1}},{"readable-stream":16}],31:[function(t,e,i){"use strict";for(var r=t("./utils"),n=t("./support"),s=t("./nodejsUtils"),a=t("./stream/GenericWorker"),o=Array(256),l=0;l<256;l++)o[l]=252<=l?6:248<=l?5:240<=l?4:224<=l?3:192<=l?2:1;function h(){a.call(this,"utf-8 decode"),this.leftOver=null}function u(){a.call(this,"utf-8 encode")}o[254]=o[254]=1,i.utf8encode=function(t){return n.nodebuffer?s.newBufferFrom(t,"utf-8"):function(t){var e,i,r,s,a,o=t.length,l=0;for(s=0;s<o;s++)55296==(64512&(i=t.charCodeAt(s)))&&s+1<o&&56320==(64512&(r=t.charCodeAt(s+1)))&&(i=65536+(i-55296<<10)+(r-56320),s++),l+=i<128?1:i<2048?2:i<65536?3:4;for(e=n.uint8array?new Uint8Array(l):Array(l),s=a=0;a<l;s++)55296==(64512&(i=t.charCodeAt(s)))&&s+1<o&&56320==(64512&(r=t.charCodeAt(s+1)))&&(i=65536+(i-55296<<10)+(r-56320),s++),i<128?e[a++]=i:(i<2048?e[a++]=192|i>>>6:(i<65536?e[a++]=224|i>>>12:(e[a++]=240|i>>>18,e[a++]=128|i>>>12&63),e[a++]=128|i>>>6&63),e[a++]=128|63&i);return e}(t)},i.utf8decode=function(t){return n.nodebuffer?r.transformTo("nodebuffer",t).toString("utf-8"):function(t){var e,i,n,s,a=t.length,l=Array(2*a);for(e=i=0;e<a;)if((n=t[e++])<128)l[i++]=n;else if(4<(s=o[n]))l[i++]=65533,e+=s-1;else{for(n&=2===s?31:3===s?15:7;1<s&&e<a;)n=n<<6|63&t[e++],s--;1<s?l[i++]=65533:n<65536?l[i++]=n:(n-=65536,l[i++]=55296|n>>10&1023,l[i++]=56320|1023&n)}return l.length!==i&&(l.subarray?l=l.subarray(0,i):l.length=i),r.applyFromCharCode(l)}(t=r.transformTo(n.uint8array?"uint8array":"array",t))},r.inherits(h,a),h.prototype.processChunk=function(t){var e=r.transformTo(n.uint8array?"uint8array":"array",t.data);if(this.leftOver&&this.leftOver.length){if(n.uint8array){var s=e;(e=new Uint8Array(s.length+this.leftOver.length)).set(this.leftOver,0),e.set(s,this.leftOver.length)}else e=this.leftOver.concat(e);this.leftOver=null}var a=function(t,e){var i;for((e=e||t.length)>t.length&&(e=t.length),i=e-1;0<=i&&128==(192&t[i]);)i--;return i<0||0===i?e:i+o[t[i]]>e?i:e}(e),l=e;a!==e.length&&(n.uint8array?(l=e.subarray(0,a),this.leftOver=e.subarray(a,e.length)):(l=e.slice(0,a),this.leftOver=e.slice(a,e.length))),this.push({data:i.utf8decode(l),meta:t.meta})},h.prototype.flush=function(){this.leftOver&&this.leftOver.length&&(this.push({data:i.utf8decode(this.leftOver),meta:{}}),this.leftOver=null)},i.Utf8DecodeWorker=h,r.inherits(u,a),u.prototype.processChunk=function(t){this.push({data:i.utf8encode(t.data),meta:t.meta})},i.Utf8EncodeWorker=u},{"./nodejsUtils":14,"./stream/GenericWorker":28,"./support":30,"./utils":32}],32:[function(t,e,i){"use strict";var r=t("./support"),n=t("./base64"),s=t("./nodejsUtils"),a=t("./external");function o(t){return t}function l(t,e){for(var i=0;i<t.length;++i)e[i]=255&t.charCodeAt(i);return e}t("setimmediate"),i.newBlob=function(t,e){i.checkSupport("blob");try{return new Blob([t],{type:e})}catch(i){try{var r=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);return r.append(t),r.getBlob(e)}catch(t){throw Error("Bug : can't construct the Blob.")}}};var h={stringifyByChunk:function(t,e,i){var r=[],n=0,s=t.length;if(s<=i)return String.fromCharCode.apply(null,t);for(;n<s;)"array"===e||"nodebuffer"===e?r.push(String.fromCharCode.apply(null,t.slice(n,Math.min(n+i,s)))):r.push(String.fromCharCode.apply(null,t.subarray(n,Math.min(n+i,s)))),n+=i;return r.join("")},stringifyByChar:function(t){for(var e="",i=0;i<t.length;i++)e+=String.fromCharCode(t[i]);return e},applyCanBeUsed:{uint8array:function(){try{return r.uint8array&&1===String.fromCharCode.apply(null,new Uint8Array(1)).length}catch(t){return!1}}(),nodebuffer:function(){try{return r.nodebuffer&&1===String.fromCharCode.apply(null,s.allocBuffer(1)).length}catch(t){return!1}}()}};function u(t){var e=65536,r=i.getTypeOf(t),n=!0;if("uint8array"===r?n=h.applyCanBeUsed.uint8array:"nodebuffer"===r&&(n=h.applyCanBeUsed.nodebuffer),n)for(;1<e;)try{return h.stringifyByChunk(t,r,e)}catch(t){e=Math.floor(e/2)}return h.stringifyByChar(t)}function d(t,e){for(var i=0;i<t.length;i++)e[i]=t[i];return e}i.applyFromCharCode=u;var c={};c.string={string:o,array:function(t){return l(t,Array(t.length))},arraybuffer:function(t){return c.string.uint8array(t).buffer},uint8array:function(t){return l(t,new Uint8Array(t.length))},nodebuffer:function(t){return l(t,s.allocBuffer(t.length))}},c.array={string:u,array:o,arraybuffer:function(t){return new Uint8Array(t).buffer},uint8array:function(t){return new Uint8Array(t)},nodebuffer:function(t){return s.newBufferFrom(t)}},c.arraybuffer={string:function(t){return u(new Uint8Array(t))},array:function(t){return d(new Uint8Array(t),Array(t.byteLength))},arraybuffer:o,uint8array:function(t){return new Uint8Array(t)},nodebuffer:function(t){return s.newBufferFrom(new Uint8Array(t))}},c.uint8array={string:u,array:function(t){return d(t,Array(t.length))},arraybuffer:function(t){return t.buffer},uint8array:o,nodebuffer:function(t){return s.newBufferFrom(t)}},c.nodebuffer={string:u,array:function(t){return d(t,Array(t.length))},arraybuffer:function(t){return c.nodebuffer.uint8array(t).buffer},uint8array:function(t){return d(t,new Uint8Array(t.length))},nodebuffer:o},i.transformTo=function(t,e){return(e=e||"",t)?(i.checkSupport(t),c[i.getTypeOf(e)][t](e)):e},i.resolve=function(t){for(var e=t.split("/"),i=[],r=0;r<e.length;r++){var n=e[r];"."===n||""===n&&0!==r&&r!==e.length-1||(".."===n?i.pop():i.push(n))}return i.join("/")},i.getTypeOf=function(t){return"string"==typeof t?"string":"[object Array]"===Object.prototype.toString.call(t)?"array":r.nodebuffer&&s.isBuffer(t)?"nodebuffer":r.uint8array&&t instanceof Uint8Array?"uint8array":r.arraybuffer&&t instanceof ArrayBuffer?"arraybuffer":void 0},i.checkSupport=function(t){if(!r[t.toLowerCase()])throw Error(t+" is not supported by this platform")},i.MAX_VALUE_16BITS=65535,i.MAX_VALUE_32BITS=-1,i.pretty=function(t){var e,i,r="";for(i=0;i<(t||"").length;i++)r+="\\x"+((e=t.charCodeAt(i))<16?"0":"")+e.toString(16).toUpperCase();return r},i.delay=function(t,e,i){setImmediate(function(){t.apply(i||null,e||[])})},i.inherits=function(t,e){function i(){}i.prototype=e.prototype,t.prototype=new i},i.extend=function(){var t,e,i={};for(t=0;t<arguments.length;t++)for(e in arguments[t])Object.prototype.hasOwnProperty.call(arguments[t],e)&&void 0===i[e]&&(i[e]=arguments[t][e]);return i},i.prepareContent=function(t,e,s,o,h){return a.Promise.resolve(e).then(function(t){return r.blob&&(t instanceof Blob||-1!==["[object File]","[object Blob]"].indexOf(Object.prototype.toString.call(t)))&&"undefined"!=typeof FileReader?new a.Promise(function(e,i){var r=new FileReader;r.onload=function(t){e(t.target.result)},r.onerror=function(t){i(t.target.error)},r.readAsArrayBuffer(t)}):t}).then(function(e){var u,d=i.getTypeOf(e);return d?("arraybuffer"===d?e=i.transformTo("uint8array",e):"string"===d&&(h?e=n.decode(e):s&&!0!==o&&(e=l(u=e,r.uint8array?new Uint8Array(u.length):Array(u.length)))),e):a.Promise.reject(Error("Can't read the data of '"+t+"'. Is it in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?"))})}},{"./base64":1,"./external":6,"./nodejsUtils":14,"./support":30,setimmediate:54}],33:[function(t,e,i){"use strict";var r=t("./reader/readerFor"),n=t("./utils"),s=t("./signature"),a=t("./zipEntry"),o=t("./support");function l(t){this.files=[],this.loadOptions=t}l.prototype={checkSignature:function(t){if(!this.reader.readAndCheckSignature(t)){this.reader.index-=4;var e=this.reader.readString(4);throw Error("Corrupted zip or bug: unexpected signature ("+n.pretty(e)+", expected "+n.pretty(t)+")")}},isSignature:function(t,e){var i=this.reader.index;this.reader.setIndex(t);var r=this.reader.readString(4)===e;return this.reader.setIndex(i),r},readBlockEndOfCentral:function(){this.diskNumber=this.reader.readInt(2),this.diskWithCentralDirStart=this.reader.readInt(2),this.centralDirRecordsOnThisDisk=this.reader.readInt(2),this.centralDirRecords=this.reader.readInt(2),this.centralDirSize=this.reader.readInt(4),this.centralDirOffset=this.reader.readInt(4),this.zipCommentLength=this.reader.readInt(2);var t=this.reader.readData(this.zipCommentLength),e=o.uint8array?"uint8array":"array",i=n.transformTo(e,t);this.zipComment=this.loadOptions.decodeFileName(i)},readBlockZip64EndOfCentral:function(){this.zip64EndOfCentralSize=this.reader.readInt(8),this.reader.skip(4),this.diskNumber=this.reader.readInt(4),this.diskWithCentralDirStart=this.reader.readInt(4),this.centralDirRecordsOnThisDisk=this.reader.readInt(8),this.centralDirRecords=this.reader.readInt(8),this.centralDirSize=this.reader.readInt(8),this.centralDirOffset=this.reader.readInt(8),this.zip64ExtensibleData={};for(var t,e,i,r=this.zip64EndOfCentralSize-44;0<r;)t=this.reader.readInt(2),e=this.reader.readInt(4),i=this.reader.readData(e),this.zip64ExtensibleData[t]={id:t,length:e,value:i}},readBlockZip64EndOfCentralLocator:function(){if(this.diskWithZip64CentralDirStart=this.reader.readInt(4),this.relativeOffsetEndOfZip64CentralDir=this.reader.readInt(8),this.disksCount=this.reader.readInt(4),1<this.disksCount)throw Error("Multi-volumes zip are not supported")},readLocalFiles:function(){var t,e;for(t=0;t<this.files.length;t++)e=this.files[t],this.reader.setIndex(e.localHeaderOffset),this.checkSignature(s.LOCAL_FILE_HEADER),e.readLocalPart(this.reader),e.handleUTF8(),e.processAttributes()},readCentralDir:function(){var t;for(this.reader.setIndex(this.centralDirOffset);this.reader.readAndCheckSignature(s.CENTRAL_FILE_HEADER);)(t=new a({zip64:this.zip64},this.loadOptions)).readCentralPart(this.reader),this.files.push(t);if(this.centralDirRecords!==this.files.length&&0!==this.centralDirRecords&&0===this.files.length)throw Error("Corrupted zip or bug: expected "+this.centralDirRecords+" records in central dir, got "+this.files.length)},readEndOfCentral:function(){var t=this.reader.lastIndexOfSignature(s.CENTRAL_DIRECTORY_END);if(t<0)throw this.isSignature(0,s.LOCAL_FILE_HEADER)?Error("Corrupted zip: can't find end of central directory"):Error("Can't find end of central directory : is this a zip file ? If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html");this.reader.setIndex(t);var e=t;if(this.checkSignature(s.CENTRAL_DIRECTORY_END),this.readBlockEndOfCentral(),this.diskNumber===n.MAX_VALUE_16BITS||this.diskWithCentralDirStart===n.MAX_VALUE_16BITS||this.centralDirRecordsOnThisDisk===n.MAX_VALUE_16BITS||this.centralDirRecords===n.MAX_VALUE_16BITS||this.centralDirSize===n.MAX_VALUE_32BITS||this.centralDirOffset===n.MAX_VALUE_32BITS){if(this.zip64=!0,(t=this.reader.lastIndexOfSignature(s.ZIP64_CENTRAL_DIRECTORY_LOCATOR))<0)throw Error("Corrupted zip: can't find the ZIP64 end of central directory locator");if(this.reader.setIndex(t),this.checkSignature(s.ZIP64_CENTRAL_DIRECTORY_LOCATOR),this.readBlockZip64EndOfCentralLocator(),!this.isSignature(this.relativeOffsetEndOfZip64CentralDir,s.ZIP64_CENTRAL_DIRECTORY_END)&&(this.relativeOffsetEndOfZip64CentralDir=this.reader.lastIndexOfSignature(s.ZIP64_CENTRAL_DIRECTORY_END),this.relativeOffsetEndOfZip64CentralDir<0))throw Error("Corrupted zip: can't find the ZIP64 end of central directory");this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir),this.checkSignature(s.ZIP64_CENTRAL_DIRECTORY_END),this.readBlockZip64EndOfCentral()}var i=this.centralDirOffset+this.centralDirSize;this.zip64&&(i+=20,i+=12+this.zip64EndOfCentralSize);var r=e-i;if(0<r)this.isSignature(e,s.CENTRAL_FILE_HEADER)||(this.reader.zero=r);else if(r<0)throw Error("Corrupted zip: missing "+Math.abs(r)+" bytes.")},prepareReader:function(t){this.reader=r(t)},load:function(t){this.prepareReader(t),this.readEndOfCentral(),this.readCentralDir(),this.readLocalFiles()}},e.exports=l},{"./reader/readerFor":22,"./signature":23,"./support":30,"./utils":32,"./zipEntry":34}],34:[function(t,e,i){"use strict";var r=t("./reader/readerFor"),n=t("./utils"),s=t("./compressedObject"),a=t("./crc32"),o=t("./utf8"),l=t("./compressions"),h=t("./support");function u(t,e){this.options=t,this.loadOptions=e}u.prototype={isEncrypted:function(){return 1==(1&this.bitFlag)},useUTF8:function(){return 2048==(2048&this.bitFlag)},readLocalPart:function(t){var e,i;if(t.skip(22),this.fileNameLength=t.readInt(2),i=t.readInt(2),this.fileName=t.readData(this.fileNameLength),t.skip(i),-1===this.compressedSize||-1===this.uncompressedSize)throw Error("Bug or corrupted zip : didn't get enough information from the central directory (compressedSize === -1 || uncompressedSize === -1)");if(null===(e=function(t){for(var e in l)if(Object.prototype.hasOwnProperty.call(l,e)&&l[e].magic===t)return l[e];return null}(this.compressionMethod)))throw Error("Corrupted zip : compression "+n.pretty(this.compressionMethod)+" unknown (inner file : "+n.transformTo("string",this.fileName)+")");this.decompressed=new s(this.compressedSize,this.uncompressedSize,this.crc32,e,t.readData(this.compressedSize))},readCentralPart:function(t){this.versionMadeBy=t.readInt(2),t.skip(2),this.bitFlag=t.readInt(2),this.compressionMethod=t.readString(2),this.date=t.readDate(),this.crc32=t.readInt(4),this.compressedSize=t.readInt(4),this.uncompressedSize=t.readInt(4);var e=t.readInt(2);if(this.extraFieldsLength=t.readInt(2),this.fileCommentLength=t.readInt(2),this.diskNumberStart=t.readInt(2),this.internalFileAttributes=t.readInt(2),this.externalFileAttributes=t.readInt(4),this.localHeaderOffset=t.readInt(4),this.isEncrypted())throw Error("Encrypted zip are not supported");t.skip(e),this.readExtraFields(t),this.parseZIP64ExtraField(t),this.fileComment=t.readData(this.fileCommentLength)},processAttributes:function(){this.unixPermissions=null,this.dosPermissions=null;var t=this.versionMadeBy>>8;this.dir=!!(16&this.externalFileAttributes),0==t&&(this.dosPermissions=63&this.externalFileAttributes),3==t&&(this.unixPermissions=this.externalFileAttributes>>16&65535),this.dir||"/"!==this.fileNameStr.slice(-1)||(this.dir=!0)},parseZIP64ExtraField:function(){if(this.extraFields[1]){var t=r(this.extraFields[1].value);this.uncompressedSize===n.MAX_VALUE_32BITS&&(this.uncompressedSize=t.readInt(8)),this.compressedSize===n.MAX_VALUE_32BITS&&(this.compressedSize=t.readInt(8)),this.localHeaderOffset===n.MAX_VALUE_32BITS&&(this.localHeaderOffset=t.readInt(8)),this.diskNumberStart===n.MAX_VALUE_32BITS&&(this.diskNumberStart=t.readInt(4))}},readExtraFields:function(t){var e,i,r,n=t.index+this.extraFieldsLength;for(this.extraFields||(this.extraFields={});t.index+4<n;)e=t.readInt(2),i=t.readInt(2),r=t.readData(i),this.extraFields[e]={id:e,length:i,value:r};t.setIndex(n)},handleUTF8:function(){var t=h.uint8array?"uint8array":"array";if(this.useUTF8())this.fileNameStr=o.utf8decode(this.fileName),this.fileCommentStr=o.utf8decode(this.fileComment);else{var e=this.findExtraFieldUnicodePath();if(null!==e)this.fileNameStr=e;else{var i=n.transformTo(t,this.fileName);this.fileNameStr=this.loadOptions.decodeFileName(i)}var r=this.findExtraFieldUnicodeComment();if(null!==r)this.fileCommentStr=r;else{var s=n.transformTo(t,this.fileComment);this.fileCommentStr=this.loadOptions.decodeFileName(s)}}},findExtraFieldUnicodePath:function(){var t=this.extraFields[28789];if(t){var e=r(t.value);return 1!==e.readInt(1)||a(this.fileName)!==e.readInt(4)?null:o.utf8decode(e.readData(t.length-5))}return null},findExtraFieldUnicodeComment:function(){var t=this.extraFields[25461];if(t){var e=r(t.value);return 1!==e.readInt(1)||a(this.fileComment)!==e.readInt(4)?null:o.utf8decode(e.readData(t.length-5))}return null}},e.exports=u},{"./compressedObject":2,"./compressions":3,"./crc32":4,"./reader/readerFor":22,"./support":30,"./utf8":31,"./utils":32}],35:[function(t,e,i){"use strict";function r(t,e,i){this.name=t,this.dir=i.dir,this.date=i.date,this.comment=i.comment,this.unixPermissions=i.unixPermissions,this.dosPermissions=i.dosPermissions,this._data=e,this._dataBinary=i.binary,this.options={compression:i.compression,compressionOptions:i.compressionOptions}}var n=t("./stream/StreamHelper"),s=t("./stream/DataWorker"),a=t("./utf8"),o=t("./compressedObject"),l=t("./stream/GenericWorker");r.prototype={internalStream:function(t){var e=null,i="string";try{if(!t)throw Error("No output type specified.");var r="string"===(i=t.toLowerCase())||"text"===i;"binarystring"!==i&&"text"!==i||(i="string"),e=this._decompressWorker();var s=!this._dataBinary;s&&!r&&(e=e.pipe(new a.Utf8EncodeWorker)),!s&&r&&(e=e.pipe(new a.Utf8DecodeWorker))}catch(t){(e=new l("error")).error(t)}return new n(e,i,"")},async:function(t,e){return this.internalStream(t).accumulate(e)},nodeStream:function(t,e){return this.internalStream(t||"nodebuffer").toNodejsStream(e)},_compressWorker:function(t,e){if(this._data instanceof o&&this._data.compression.magic===t.magic)return this._data.getCompressedWorker();var i=this._decompressWorker();return this._dataBinary||(i=i.pipe(new a.Utf8EncodeWorker)),o.createWorkerFrom(i,t,e)},_decompressWorker:function(){return this._data instanceof o?this._data.getContentWorker():this._data instanceof l?this._data:new s(this._data)}};for(var h=["asText","asBinary","asNodeBuffer","asUint8Array","asArrayBuffer"],u=function(){throw Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},d=0;d<h.length;d++)r.prototype[h[d]]=u;e.exports=r},{"./compressedObject":2,"./stream/DataWorker":27,"./stream/GenericWorker":28,"./stream/StreamHelper":29,"./utf8":31}],36:[function(t,e,r){(function(t){"use strict";var i,r,n=t.MutationObserver||t.WebKitMutationObserver;if(n){var s=0,a=new n(u),o=t.document.createTextNode("");a.observe(o,{characterData:!0}),i=function(){o.data=s=++s%2}}else if(t.setImmediate||void 0===t.MessageChannel)i="document"in t&&"onreadystatechange"in t.document.createElement("script")?function(){var e=t.document.createElement("script");e.onreadystatechange=function(){u(),e.onreadystatechange=null,e.parentNode.removeChild(e),e=null},t.document.documentElement.appendChild(e)}:function(){setTimeout(u,0)};else{var l=new t.MessageChannel;l.port1.onmessage=u,i=function(){l.port2.postMessage(0)}}var h=[];function u(){var t,e;r=!0;for(var i=h.length;i;){for(e=h,h=[],t=-1;++t<i;)e[t]();i=h.length}r=!1}e.exports=function(t){1!==h.push(t)||r||i()}}).call(this,void 0!==i.g?i.g:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],37:[function(t,e,i){"use strict";var r=t("immediate");function n(){}var s={},a=["REJECTED"],o=["FULFILLED"],l=["PENDING"];function h(t){if("function"!=typeof t)throw TypeError("resolver must be a function");this.state=l,this.queue=[],this.outcome=void 0,t!==n&&f(this,t)}function u(t,e,i){this.promise=t,"function"==typeof e&&(this.onFulfilled=e,this.callFulfilled=this.otherCallFulfilled),"function"==typeof i&&(this.onRejected=i,this.callRejected=this.otherCallRejected)}function d(t,e,i){r(function(){var r;try{r=e(i)}catch(e){return s.reject(t,e)}r===t?s.reject(t,TypeError("Cannot resolve promise with itself")):s.resolve(t,r)})}function c(t){var e=t&&t.then;if(t&&("object"==typeof t||"function"==typeof t)&&"function"==typeof e)return function(){e.apply(t,arguments)}}function f(t,e){var i=!1;function r(e){i||(i=!0,s.reject(t,e))}function n(e){i||(i=!0,s.resolve(t,e))}var a=p(function(){e(n,r)});"error"===a.status&&r(a.value)}function p(t,e){var i={};try{i.value=t(e),i.status="success"}catch(t){i.status="error",i.value=t}return i}(e.exports=h).prototype.finally=function(t){if("function"!=typeof t)return this;var e=this.constructor;return this.then(function(i){return e.resolve(t()).then(function(){return i})},function(i){return e.resolve(t()).then(function(){throw i})})},h.prototype.catch=function(t){return this.then(null,t)},h.prototype.then=function(t,e){if("function"!=typeof t&&this.state===o||"function"!=typeof e&&this.state===a)return this;var i=new this.constructor(n);return this.state!==l?d(i,this.state===o?t:e,this.outcome):this.queue.push(new u(i,t,e)),i},u.prototype.callFulfilled=function(t){s.resolve(this.promise,t)},u.prototype.otherCallFulfilled=function(t){d(this.promise,this.onFulfilled,t)},u.prototype.callRejected=function(t){s.reject(this.promise,t)},u.prototype.otherCallRejected=function(t){d(this.promise,this.onRejected,t)},s.resolve=function(t,e){var i=p(c,e);if("error"===i.status)return s.reject(t,i.value);var r=i.value;if(r)f(t,r);else{t.state=o,t.outcome=e;for(var n=-1,a=t.queue.length;++n<a;)t.queue[n].callFulfilled(e)}return t},s.reject=function(t,e){t.state=a,t.outcome=e;for(var i=-1,r=t.queue.length;++i<r;)t.queue[i].callRejected(e);return t},h.resolve=function(t){return t instanceof this?t:s.resolve(new this(n),t)},h.reject=function(t){var e=new this(n);return s.reject(e,t)},h.all=function(t){var e=this;if("[object Array]"!==Object.prototype.toString.call(t))return this.reject(TypeError("must be an array"));var i=t.length,r=!1;if(!i)return this.resolve([]);for(var a=Array(i),o=0,l=-1,h=new this(n);++l<i;)!function(t,n){e.resolve(t).then(function(t){a[n]=t,++o!==i||r||(r=!0,s.resolve(h,a))},function(t){r||(r=!0,s.reject(h,t))})}(t[l],l);return h},h.race=function(t){if("[object Array]"!==Object.prototype.toString.call(t))return this.reject(TypeError("must be an array"));var e,i=t.length,r=!1;if(!i)return this.resolve([]);for(var a=-1,o=new this(n);++a<i;)e=t[a],this.resolve(e).then(function(t){r||(r=!0,s.resolve(o,t))},function(t){r||(r=!0,s.reject(o,t))});return o}},{immediate:36}],38:[function(t,e,i){"use strict";var r={};(0,t("./lib/utils/common").assign)(r,t("./lib/deflate"),t("./lib/inflate"),t("./lib/zlib/constants")),e.exports=r},{"./lib/deflate":39,"./lib/inflate":40,"./lib/utils/common":41,"./lib/zlib/constants":44}],39:[function(t,e,i){"use strict";var r=t("./zlib/deflate"),n=t("./utils/common"),s=t("./utils/strings"),a=t("./zlib/messages"),o=t("./zlib/zstream"),l=Object.prototype.toString;function h(t){if(!(this instanceof h))return new h(t);this.options=n.assign({level:-1,method:8,chunkSize:16384,windowBits:15,memLevel:8,strategy:0,to:""},t||{});var e,i=this.options;i.raw&&0<i.windowBits?i.windowBits=-i.windowBits:i.gzip&&0<i.windowBits&&i.windowBits<16&&(i.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new o,this.strm.avail_out=0;var u=r.deflateInit2(this.strm,i.level,i.method,i.windowBits,i.memLevel,i.strategy);if(0!==u)throw Error(a[u]);if(i.header&&r.deflateSetHeader(this.strm,i.header),i.dictionary){if(e="string"==typeof i.dictionary?s.string2buf(i.dictionary):"[object ArrayBuffer]"===l.call(i.dictionary)?new Uint8Array(i.dictionary):i.dictionary,0!==(u=r.deflateSetDictionary(this.strm,e)))throw Error(a[u]);this._dict_set=!0}}function u(t,e){var i=new h(e);if(i.push(t,!0),i.err)throw i.msg||a[i.err];return i.result}h.prototype.push=function(t,e){var i,a,o=this.strm,h=this.options.chunkSize;if(this.ended)return!1;a=e===~~e?e:4*(!0===e),"string"==typeof t?o.input=s.string2buf(t):"[object ArrayBuffer]"===l.call(t)?o.input=new Uint8Array(t):o.input=t,o.next_in=0,o.avail_in=o.input.length;do{if(0===o.avail_out&&(o.output=new n.Buf8(h),o.next_out=0,o.avail_out=h),1!==(i=r.deflate(o,a))&&0!==i)return this.onEnd(i),this.ended=!0,!1;0!==o.avail_out&&(0!==o.avail_in||4!==a&&2!==a)||("string"===this.options.to?this.onData(s.buf2binstring(n.shrinkBuf(o.output,o.next_out))):this.onData(n.shrinkBuf(o.output,o.next_out)))}while((0<o.avail_in||0===o.avail_out)&&1!==i);return 4===a?(i=r.deflateEnd(this.strm),this.onEnd(i),this.ended=!0,0===i):2!==a||(this.onEnd(0),o.avail_out=0,!0)},h.prototype.onData=function(t){this.chunks.push(t)},h.prototype.onEnd=function(t){0===t&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=n.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},i.Deflate=h,i.deflate=u,i.deflateRaw=function(t,e){return(e=e||{}).raw=!0,u(t,e)},i.gzip=function(t,e){return(e=e||{}).gzip=!0,u(t,e)}},{"./utils/common":41,"./utils/strings":42,"./zlib/deflate":46,"./zlib/messages":51,"./zlib/zstream":53}],40:[function(t,e,i){"use strict";var r=t("./zlib/inflate"),n=t("./utils/common"),s=t("./utils/strings"),a=t("./zlib/constants"),o=t("./zlib/messages"),l=t("./zlib/zstream"),h=t("./zlib/gzheader"),u=Object.prototype.toString;function d(t){if(!(this instanceof d))return new d(t);this.options=n.assign({chunkSize:16384,windowBits:0,to:""},t||{});var e=this.options;e.raw&&0<=e.windowBits&&e.windowBits<16&&(e.windowBits=-e.windowBits,0===e.windowBits&&(e.windowBits=-15)),!(0<=e.windowBits&&e.windowBits<16)||t&&t.windowBits||(e.windowBits+=32),15<e.windowBits&&e.windowBits<48&&0==(15&e.windowBits)&&(e.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new l,this.strm.avail_out=0;var i=r.inflateInit2(this.strm,e.windowBits);if(i!==a.Z_OK)throw Error(o[i]);this.header=new h,r.inflateGetHeader(this.strm,this.header)}function c(t,e){var i=new d(e);if(i.push(t,!0),i.err)throw i.msg||o[i.err];return i.result}d.prototype.push=function(t,e){var i,o,l,h,d,c,f=this.strm,p=this.options.chunkSize,m=this.options.dictionary,g=!1;if(this.ended)return!1;o=e===~~e?e:!0===e?a.Z_FINISH:a.Z_NO_FLUSH,"string"==typeof t?f.input=s.binstring2buf(t):"[object ArrayBuffer]"===u.call(t)?f.input=new Uint8Array(t):f.input=t,f.next_in=0,f.avail_in=f.input.length;do{if(0===f.avail_out&&(f.output=new n.Buf8(p),f.next_out=0,f.avail_out=p),(i=r.inflate(f,a.Z_NO_FLUSH))===a.Z_NEED_DICT&&m&&(c="string"==typeof m?s.string2buf(m):"[object ArrayBuffer]"===u.call(m)?new Uint8Array(m):m,i=r.inflateSetDictionary(this.strm,c)),i===a.Z_BUF_ERROR&&!0===g&&(i=a.Z_OK,g=!1),i!==a.Z_STREAM_END&&i!==a.Z_OK)return this.onEnd(i),this.ended=!0,!1;f.next_out&&(0!==f.avail_out&&i!==a.Z_STREAM_END&&(0!==f.avail_in||o!==a.Z_FINISH&&o!==a.Z_SYNC_FLUSH)||("string"===this.options.to?(l=s.utf8border(f.output,f.next_out),h=f.next_out-l,d=s.buf2string(f.output,l),f.next_out=h,f.avail_out=p-h,h&&n.arraySet(f.output,f.output,l,h,0),this.onData(d)):this.onData(n.shrinkBuf(f.output,f.next_out)))),0===f.avail_in&&0===f.avail_out&&(g=!0)}while((0<f.avail_in||0===f.avail_out)&&i!==a.Z_STREAM_END);return i===a.Z_STREAM_END&&(o=a.Z_FINISH),o===a.Z_FINISH?(i=r.inflateEnd(this.strm),this.onEnd(i),this.ended=!0,i===a.Z_OK):o!==a.Z_SYNC_FLUSH||(this.onEnd(a.Z_OK),f.avail_out=0,!0)},d.prototype.onData=function(t){this.chunks.push(t)},d.prototype.onEnd=function(t){t===a.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=n.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},i.Inflate=d,i.inflate=c,i.inflateRaw=function(t,e){return(e=e||{}).raw=!0,c(t,e)},i.ungzip=c},{"./utils/common":41,"./utils/strings":42,"./zlib/constants":44,"./zlib/gzheader":47,"./zlib/inflate":49,"./zlib/messages":51,"./zlib/zstream":53}],41:[function(t,e,i){"use strict";var r="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;i.assign=function(t){for(var e=Array.prototype.slice.call(arguments,1);e.length;){var i=e.shift();if(i){if("object"!=typeof i)throw TypeError(i+"must be non-object");for(var r in i)i.hasOwnProperty(r)&&(t[r]=i[r])}}return t},i.shrinkBuf=function(t,e){return t.length===e?t:t.subarray?t.subarray(0,e):(t.length=e,t)};var n={arraySet:function(t,e,i,r,n){if(e.subarray&&t.subarray)t.set(e.subarray(i,i+r),n);else for(var s=0;s<r;s++)t[n+s]=e[i+s]},flattenChunks:function(t){var e,i,r,n,s,a;for(e=r=0,i=t.length;e<i;e++)r+=t[e].length;for(a=new Uint8Array(r),e=n=0,i=t.length;e<i;e++)s=t[e],a.set(s,n),n+=s.length;return a}},s={arraySet:function(t,e,i,r,n){for(var s=0;s<r;s++)t[n+s]=e[i+s]},flattenChunks:function(t){return[].concat.apply([],t)}};i.setTyped=function(t){t?(i.Buf8=Uint8Array,i.Buf16=Uint16Array,i.Buf32=Int32Array,i.assign(i,n)):(i.Buf8=Array,i.Buf16=Array,i.Buf32=Array,i.assign(i,s))},i.setTyped(r)},{}],42:[function(t,e,i){"use strict";var r=t("./common"),n=!0,s=!0;try{String.fromCharCode.apply(null,[0])}catch(t){n=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(t){s=!1}for(var a=new r.Buf8(256),o=0;o<256;o++)a[o]=252<=o?6:248<=o?5:240<=o?4:224<=o?3:192<=o?2:1;function l(t,e){if(e<65537&&(t.subarray&&s||!t.subarray&&n))return String.fromCharCode.apply(null,r.shrinkBuf(t,e));for(var i="",a=0;a<e;a++)i+=String.fromCharCode(t[a]);return i}a[254]=a[254]=1,i.string2buf=function(t){var e,i,n,s,a,o=t.length,l=0;for(s=0;s<o;s++)55296==(64512&(i=t.charCodeAt(s)))&&s+1<o&&56320==(64512&(n=t.charCodeAt(s+1)))&&(i=65536+(i-55296<<10)+(n-56320),s++),l+=i<128?1:i<2048?2:i<65536?3:4;for(e=new r.Buf8(l),s=a=0;a<l;s++)55296==(64512&(i=t.charCodeAt(s)))&&s+1<o&&56320==(64512&(n=t.charCodeAt(s+1)))&&(i=65536+(i-55296<<10)+(n-56320),s++),i<128?e[a++]=i:(i<2048?e[a++]=192|i>>>6:(i<65536?e[a++]=224|i>>>12:(e[a++]=240|i>>>18,e[a++]=128|i>>>12&63),e[a++]=128|i>>>6&63),e[a++]=128|63&i);return e},i.buf2binstring=function(t){return l(t,t.length)},i.binstring2buf=function(t){for(var e=new r.Buf8(t.length),i=0,n=e.length;i<n;i++)e[i]=t.charCodeAt(i);return e},i.buf2string=function(t,e){var i,r,n,s,o=e||t.length,h=Array(2*o);for(i=r=0;i<o;)if((n=t[i++])<128)h[r++]=n;else if(4<(s=a[n]))h[r++]=65533,i+=s-1;else{for(n&=2===s?31:3===s?15:7;1<s&&i<o;)n=n<<6|63&t[i++],s--;1<s?h[r++]=65533:n<65536?h[r++]=n:(n-=65536,h[r++]=55296|n>>10&1023,h[r++]=56320|1023&n)}return l(h,r)},i.utf8border=function(t,e){var i;for((e=e||t.length)>t.length&&(e=t.length),i=e-1;0<=i&&128==(192&t[i]);)i--;return i<0||0===i?e:i+a[t[i]]>e?i:e}},{"./common":41}],43:[function(t,e,i){"use strict";e.exports=function(t,e,i,r){for(var n=65535&t,s=t>>>16&65535,a=0;0!==i;){for(i-=a=2e3<i?2e3:i;s=s+(n=n+e[r++]|0)|0,--a;);n%=65521,s%=65521}return n|s<<16}},{}],44:[function(t,e,i){"use strict";e.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},{}],45:[function(t,e,i){"use strict";var r=function(){for(var t,e=[],i=0;i<256;i++){t=i;for(var r=0;r<8;r++)t=1&t?0xedb88320^t>>>1:t>>>1;e[i]=t}return e}();e.exports=function(t,e,i,n){var s=n+i;t^=-1;for(var a=n;a<s;a++)t=t>>>8^r[255&(t^e[a])];return -1^t}},{}],46:[function(t,e,i){"use strict";var r,n=t("../utils/common"),s=t("./trees"),a=t("./adler32"),o=t("./crc32"),l=t("./messages");function h(t,e){return t.msg=l[e],e}function u(t){return(t<<1)-9*(4<t)}function d(t){for(var e=t.length;0<=--e;)t[e]=0}function c(t){var e=t.state,i=e.pending;i>t.avail_out&&(i=t.avail_out),0!==i&&(n.arraySet(t.output,e.pending_buf,e.pending_out,i,t.next_out),t.next_out+=i,e.pending_out+=i,t.total_out+=i,t.avail_out-=i,e.pending-=i,0===e.pending&&(e.pending_out=0))}function f(t,e){s._tr_flush_block(t,0<=t.block_start?t.block_start:-1,t.strstart-t.block_start,e),t.block_start=t.strstart,c(t.strm)}function p(t,e){t.pending_buf[t.pending++]=e}function m(t,e){t.pending_buf[t.pending++]=e>>>8&255,t.pending_buf[t.pending++]=255&e}function g(t,e){var i,r,n=t.max_chain_length,s=t.strstart,a=t.prev_length,o=t.nice_match,l=t.strstart>t.w_size-262?t.strstart-(t.w_size-262):0,h=t.window,u=t.w_mask,d=t.prev,c=t.strstart+258,f=h[s+a-1],p=h[s+a];t.prev_length>=t.good_match&&(n>>=2),o>t.lookahead&&(o=t.lookahead);do if(h[(i=e)+a]===p&&h[i+a-1]===f&&h[i]===h[s]&&h[++i]===h[s+1]){s+=2,i++;do;while(h[++s]===h[++i]&&h[++s]===h[++i]&&h[++s]===h[++i]&&h[++s]===h[++i]&&h[++s]===h[++i]&&h[++s]===h[++i]&&h[++s]===h[++i]&&h[++s]===h[++i]&&s<c);if(r=258-(c-s),s=c-258,a<r){if(t.match_start=e,o<=(a=r))break;f=h[s+a-1],p=h[s+a]}}while((e=d[e&u])>l&&0!=--n);return a<=t.lookahead?a:t.lookahead}function y(t){var e,i,r,s,l,h,u,d,c,f,p=t.w_size;do{if(s=t.window_size-t.lookahead-t.strstart,t.strstart>=p+(p-262)){for(n.arraySet(t.window,t.window,p,p,0),t.match_start-=p,t.strstart-=p,t.block_start-=p,e=i=t.hash_size;r=t.head[--e],t.head[e]=p<=r?r-p:0,--i;);for(e=i=p;r=t.prev[--e],t.prev[e]=p<=r?r-p:0,--i;);s+=p}if(0===t.strm.avail_in)break;if(h=t.strm,u=t.window,d=t.strstart+t.lookahead,f=void 0,(c=s)<(f=h.avail_in)&&(f=c),i=0===f?0:(h.avail_in-=f,n.arraySet(u,h.input,h.next_in,f,d),1===h.state.wrap?h.adler=a(h.adler,u,f,d):2===h.state.wrap&&(h.adler=o(h.adler,u,f,d)),h.next_in+=f,h.total_in+=f,f),t.lookahead+=i,t.lookahead+t.insert>=3)for(l=t.strstart-t.insert,t.ins_h=t.window[l],t.ins_h=(t.ins_h<<t.hash_shift^t.window[l+1])&t.hash_mask;t.insert&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[l+3-1])&t.hash_mask,t.prev[l&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=l,l++,t.insert--,!(t.lookahead+t.insert<3)););}while(t.lookahead<262&&0!==t.strm.avail_in)}function v(t,e){for(var i,r;;){if(t.lookahead<262){if(y(t),t.lookahead<262&&0===e)return 1;if(0===t.lookahead)break}if(i=0,t.lookahead>=3&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,i=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!==i&&t.strstart-i<=t.w_size-262&&(t.match_length=g(t,i)),t.match_length>=3)if(r=s._tr_tally(t,t.strstart-t.match_start,t.match_length-3),t.lookahead-=t.match_length,t.match_length<=t.max_lazy_match&&t.lookahead>=3){for(t.match_length--;t.strstart++,t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,i=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart,0!=--t.match_length;);t.strstart++}else t.strstart+=t.match_length,t.match_length=0,t.ins_h=t.window[t.strstart],t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+1])&t.hash_mask;else r=s._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++;if(r&&(f(t,!1),0===t.strm.avail_out))return 1}return t.insert=t.strstart<2?t.strstart:2,4===e?(f(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(f(t,!1),0===t.strm.avail_out)?1:2}function b(t,e){for(var i,r,n;;){if(t.lookahead<262){if(y(t),t.lookahead<262&&0===e)return 1;if(0===t.lookahead)break}if(i=0,t.lookahead>=3&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,i=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),t.prev_length=t.match_length,t.prev_match=t.match_start,t.match_length=2,0!==i&&t.prev_length<t.max_lazy_match&&t.strstart-i<=t.w_size-262&&(t.match_length=g(t,i),t.match_length<=5&&(1===t.strategy||3===t.match_length&&4096<t.strstart-t.match_start)&&(t.match_length=2)),t.prev_length>=3&&t.match_length<=t.prev_length){for(n=t.strstart+t.lookahead-3,r=s._tr_tally(t,t.strstart-1-t.prev_match,t.prev_length-3),t.lookahead-=t.prev_length-1,t.prev_length-=2;++t.strstart<=n&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,i=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!=--t.prev_length;);if(t.match_available=0,t.match_length=2,t.strstart++,r&&(f(t,!1),0===t.strm.avail_out))return 1}else if(t.match_available){if((r=s._tr_tally(t,0,t.window[t.strstart-1]))&&f(t,!1),t.strstart++,t.lookahead--,0===t.strm.avail_out)return 1}else t.match_available=1,t.strstart++,t.lookahead--}return t.match_available&&(r=s._tr_tally(t,0,t.window[t.strstart-1]),t.match_available=0),t.insert=t.strstart<2?t.strstart:2,4===e?(f(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(f(t,!1),0===t.strm.avail_out)?1:2}function _(t,e,i,r,n){this.good_length=t,this.max_lazy=e,this.nice_length=i,this.max_chain=r,this.func=n}function w(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=8,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new n.Buf16(1146),this.dyn_dtree=new n.Buf16(122),this.bl_tree=new n.Buf16(78),d(this.dyn_ltree),d(this.dyn_dtree),d(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new n.Buf16(16),this.heap=new n.Buf16(573),d(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new n.Buf16(573),d(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function x(t){var e;return t&&t.state?(t.total_in=t.total_out=0,t.data_type=2,(e=t.state).pending=0,e.pending_out=0,e.wrap<0&&(e.wrap=-e.wrap),e.status=e.wrap?42:113,t.adler=+(2!==e.wrap),e.last_flush=0,s._tr_init(e),0):h(t,-2)}function k(t){var e,i=x(t);return 0===i&&((e=t.state).window_size=2*e.w_size,d(e.head),e.max_lazy_match=r[e.level].max_lazy,e.good_match=r[e.level].good_length,e.nice_match=r[e.level].nice_length,e.max_chain_length=r[e.level].max_chain,e.strstart=0,e.block_start=0,e.lookahead=0,e.insert=0,e.match_length=e.prev_length=2,e.match_available=0,e.ins_h=0),i}function S(t,e,i,r,s,a){if(!t)return -2;var o=1;if(-1===e&&(e=6),r<0?(o=0,r=-r):15<r&&(o=2,r-=16),s<1||9<s||8!==i||r<8||15<r||e<0||9<e||a<0||4<a)return h(t,-2);8===r&&(r=9);var l=new w;return(t.state=l).strm=t,l.wrap=o,l.gzhead=null,l.w_bits=r,l.w_size=1<<l.w_bits,l.w_mask=l.w_size-1,l.hash_bits=s+7,l.hash_size=1<<l.hash_bits,l.hash_mask=l.hash_size-1,l.hash_shift=~~((l.hash_bits+3-1)/3),l.window=new n.Buf8(2*l.w_size),l.head=new n.Buf16(l.hash_size),l.prev=new n.Buf16(l.w_size),l.lit_bufsize=1<<s+6,l.pending_buf_size=4*l.lit_bufsize,l.pending_buf=new n.Buf8(l.pending_buf_size),l.d_buf=+l.lit_bufsize,l.l_buf=3*l.lit_bufsize,l.level=e,l.strategy=a,l.method=i,k(t)}r=[new _(0,0,0,0,function(t,e){var i=65535;for(65535>t.pending_buf_size-5&&(i=t.pending_buf_size-5);;){if(t.lookahead<=1){if(y(t),0===t.lookahead&&0===e)return 1;if(0===t.lookahead)break}t.strstart+=t.lookahead,t.lookahead=0;var r=t.block_start+i;if((0===t.strstart||t.strstart>=r)&&(t.lookahead=t.strstart-r,t.strstart=r,f(t,!1),0===t.strm.avail_out)||t.strstart-t.block_start>=t.w_size-262&&(f(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(f(t,!0),0===t.strm.avail_out?3:4):(t.strstart>t.block_start&&(f(t,!1),t.strm.avail_out),1)}),new _(4,4,8,4,v),new _(4,5,16,8,v),new _(4,6,32,32,v),new _(4,4,16,16,b),new _(8,16,32,32,b),new _(8,16,128,128,b),new _(8,32,128,256,b),new _(32,128,258,1024,b),new _(32,258,258,4096,b)],i.deflateInit=function(t,e){return S(t,e,8,15,8,0)},i.deflateInit2=S,i.deflateReset=k,i.deflateResetKeep=x,i.deflateSetHeader=function(t,e){return t&&t.state?2!==t.state.wrap?-2:(t.state.gzhead=e,0):-2},i.deflate=function(t,e){var i,n,a,l;if(!t||!t.state||5<e||e<0)return t?h(t,-2):-2;if(n=t.state,!t.output||!t.input&&0!==t.avail_in||666===n.status&&4!==e)return h(t,0===t.avail_out?-5:-2);if(n.strm=t,i=n.last_flush,n.last_flush=e,42===n.status)if(2===n.wrap)t.adler=0,p(n,31),p(n,139),p(n,8),n.gzhead?(p(n,+!!n.gzhead.text+2*!!n.gzhead.hcrc+4*!!n.gzhead.extra+8*!!n.gzhead.name+16*!!n.gzhead.comment),p(n,255&n.gzhead.time),p(n,n.gzhead.time>>8&255),p(n,n.gzhead.time>>16&255),p(n,n.gzhead.time>>24&255),p(n,9===n.level?2:4*(2<=n.strategy||n.level<2)),p(n,255&n.gzhead.os),n.gzhead.extra&&n.gzhead.extra.length&&(p(n,255&n.gzhead.extra.length),p(n,n.gzhead.extra.length>>8&255)),n.gzhead.hcrc&&(t.adler=o(t.adler,n.pending_buf,n.pending,0)),n.gzindex=0,n.status=69):(p(n,0),p(n,0),p(n,0),p(n,0),p(n,0),p(n,9===n.level?2:4*(2<=n.strategy||n.level<2)),p(n,3),n.status=113);else{var g=8+(n.w_bits-8<<4)<<8;g|=(2<=n.strategy||n.level<2?0:n.level<6?1:6===n.level?2:3)<<6,0!==n.strstart&&(g|=32),g+=31-g%31,n.status=113,m(n,g),0!==n.strstart&&(m(n,t.adler>>>16),m(n,65535&t.adler)),t.adler=1}if(69===n.status)if(n.gzhead.extra){for(a=n.pending;n.gzindex<(65535&n.gzhead.extra.length)&&(n.pending!==n.pending_buf_size||(n.gzhead.hcrc&&n.pending>a&&(t.adler=o(t.adler,n.pending_buf,n.pending-a,a)),c(t),a=n.pending,n.pending!==n.pending_buf_size));)p(n,255&n.gzhead.extra[n.gzindex]),n.gzindex++;n.gzhead.hcrc&&n.pending>a&&(t.adler=o(t.adler,n.pending_buf,n.pending-a,a)),n.gzindex===n.gzhead.extra.length&&(n.gzindex=0,n.status=73)}else n.status=73;if(73===n.status)if(n.gzhead.name){a=n.pending;do{if(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>a&&(t.adler=o(t.adler,n.pending_buf,n.pending-a,a)),c(t),a=n.pending,n.pending===n.pending_buf_size)){l=1;break}l=n.gzindex<n.gzhead.name.length?255&n.gzhead.name.charCodeAt(n.gzindex++):0,p(n,l)}while(0!==l);n.gzhead.hcrc&&n.pending>a&&(t.adler=o(t.adler,n.pending_buf,n.pending-a,a)),0===l&&(n.gzindex=0,n.status=91)}else n.status=91;if(91===n.status)if(n.gzhead.comment){a=n.pending;do{if(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>a&&(t.adler=o(t.adler,n.pending_buf,n.pending-a,a)),c(t),a=n.pending,n.pending===n.pending_buf_size)){l=1;break}l=n.gzindex<n.gzhead.comment.length?255&n.gzhead.comment.charCodeAt(n.gzindex++):0,p(n,l)}while(0!==l);n.gzhead.hcrc&&n.pending>a&&(t.adler=o(t.adler,n.pending_buf,n.pending-a,a)),0===l&&(n.status=103)}else n.status=103;if(103===n.status&&(n.gzhead.hcrc?(n.pending+2>n.pending_buf_size&&c(t),n.pending+2<=n.pending_buf_size&&(p(n,255&t.adler),p(n,t.adler>>8&255),t.adler=0,n.status=113)):n.status=113),0!==n.pending){if(c(t),0===t.avail_out)return n.last_flush=-1,0}else if(0===t.avail_in&&u(e)<=u(i)&&4!==e)return h(t,-5);if(666===n.status&&0!==t.avail_in)return h(t,-5);if(0!==t.avail_in||0!==n.lookahead||0!==e&&666!==n.status){var v=2===n.strategy?function(t,e){for(var i;;){if(0===t.lookahead&&(y(t),0===t.lookahead)){if(0===e)return 1;break}if(t.match_length=0,i=s._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++,i&&(f(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(f(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(f(t,!1),0===t.strm.avail_out)?1:2}(n,e):3===n.strategy?function(t,e){for(var i,r,n,a,o=t.window;;){if(t.lookahead<=258){if(y(t),t.lookahead<=258&&0===e)return 1;if(0===t.lookahead)break}if(t.match_length=0,t.lookahead>=3&&0<t.strstart&&(r=o[n=t.strstart-1])===o[++n]&&r===o[++n]&&r===o[++n]){a=t.strstart+258;do;while(r===o[++n]&&r===o[++n]&&r===o[++n]&&r===o[++n]&&r===o[++n]&&r===o[++n]&&r===o[++n]&&r===o[++n]&&n<a);t.match_length=258-(a-n),t.match_length>t.lookahead&&(t.match_length=t.lookahead)}if(t.match_length>=3?(i=s._tr_tally(t,1,t.match_length-3),t.lookahead-=t.match_length,t.strstart+=t.match_length,t.match_length=0):(i=s._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++),i&&(f(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(f(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(f(t,!1),0===t.strm.avail_out)?1:2}(n,e):r[n.level].func(n,e);if(3!==v&&4!==v||(n.status=666),1===v||3===v)return 0===t.avail_out&&(n.last_flush=-1),0;if(2===v&&(1===e?s._tr_align(n):5!==e&&(s._tr_stored_block(n,0,0,!1),3===e&&(d(n.head),0===n.lookahead&&(n.strstart=0,n.block_start=0,n.insert=0))),c(t),0===t.avail_out))return n.last_flush=-1,0}return 4!==e?0:n.wrap<=0?1:(2===n.wrap?(p(n,255&t.adler),p(n,t.adler>>8&255),p(n,t.adler>>16&255),p(n,t.adler>>24&255),p(n,255&t.total_in),p(n,t.total_in>>8&255),p(n,t.total_in>>16&255),p(n,t.total_in>>24&255)):(m(n,t.adler>>>16),m(n,65535&t.adler)),c(t),0<n.wrap&&(n.wrap=-n.wrap),+(0===n.pending))},i.deflateEnd=function(t){var e;return t&&t.state?42!==(e=t.state.status)&&69!==e&&73!==e&&91!==e&&103!==e&&113!==e&&666!==e?h(t,-2):(t.state=null,113===e?h(t,-3):0):-2},i.deflateSetDictionary=function(t,e){var i,r,s,o,l,h,u,c,f=e.length;if(!t||!t.state||2===(o=(i=t.state).wrap)||1===o&&42!==i.status||i.lookahead)return -2;for(1===o&&(t.adler=a(t.adler,e,f,0)),i.wrap=0,f>=i.w_size&&(0===o&&(d(i.head),i.strstart=0,i.block_start=0,i.insert=0),c=new n.Buf8(i.w_size),n.arraySet(c,e,f-i.w_size,i.w_size,0),e=c,f=i.w_size),l=t.avail_in,h=t.next_in,u=t.input,t.avail_in=f,t.next_in=0,t.input=e,y(i);i.lookahead>=3;){for(r=i.strstart,s=i.lookahead-2;i.ins_h=(i.ins_h<<i.hash_shift^i.window[r+3-1])&i.hash_mask,i.prev[r&i.w_mask]=i.head[i.ins_h],i.head[i.ins_h]=r,r++,--s;);i.strstart=r,i.lookahead=2,y(i)}return i.strstart+=i.lookahead,i.block_start=i.strstart,i.insert=i.lookahead,i.lookahead=0,i.match_length=i.prev_length=2,i.match_available=0,t.next_in=h,t.input=u,t.avail_in=l,i.wrap=o,0},i.deflateInfo="pako deflate (from Nodeca project)"},{"../utils/common":41,"./adler32":43,"./crc32":45,"./messages":51,"./trees":52}],47:[function(t,e,i){"use strict";e.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}},{}],48:[function(t,e,i){"use strict";e.exports=function(t,e){var i,r,n,s,a,o,l,h,u,d,c,f,p,m,g,y,v,b,_,w,x,k,S,A,E;i=t.state,r=t.next_in,A=t.input,n=r+(t.avail_in-5),s=t.next_out,E=t.output,a=s-(e-t.avail_out),o=s+(t.avail_out-257),l=i.dmax,h=i.wsize,u=i.whave,d=i.wnext,c=i.window,f=i.hold,p=i.bits,m=i.lencode,g=i.distcode,y=(1<<i.lenbits)-1,v=(1<<i.distbits)-1;t:do for(p<15&&(f+=A[r++]<<p,p+=8,f+=A[r++]<<p,p+=8),b=m[f&y];;){if(f>>>=_=b>>>24,p-=_,0==(_=b>>>16&255))E[s++]=65535&b;else{if(!(16&_)){if(0==(64&_)){b=m[(65535&b)+(f&(1<<_)-1)];continue}if(32&_){i.mode=12;break t}t.msg="invalid literal/length code",i.mode=30;break t}for(w=65535&b,(_&=15)&&(p<_&&(f+=A[r++]<<p,p+=8),w+=f&(1<<_)-1,f>>>=_,p-=_),p<15&&(f+=A[r++]<<p,p+=8,f+=A[r++]<<p,p+=8),b=g[f&v];;){if(f>>>=_=b>>>24,p-=_,!(16&(_=b>>>16&255))){if(0==(64&_)){b=g[(65535&b)+(f&(1<<_)-1)];continue}t.msg="invalid distance code",i.mode=30;break t}if(x=65535&b,p<(_&=15)&&(f+=A[r++]<<p,(p+=8)<_&&(f+=A[r++]<<p,p+=8)),l<(x+=f&(1<<_)-1)){t.msg="invalid distance too far back",i.mode=30;break t}if(f>>>=_,p-=_,(_=s-a)<x){if(u<(_=x-_)&&i.sane){t.msg="invalid distance too far back",i.mode=30;break t}if(S=c,(k=0)===d){if(k+=h-_,_<w){for(w-=_;E[s++]=c[k++],--_;);k=s-x,S=E}}else if(d<_){if(k+=h+d-_,(_-=d)<w){for(w-=_;E[s++]=c[k++],--_;);if(k=0,d<w){for(w-=_=d;E[s++]=c[k++],--_;);k=s-x,S=E}}}else if(k+=d-_,_<w){for(w-=_;E[s++]=c[k++],--_;);k=s-x,S=E}for(;2<w;)E[s++]=S[k++],E[s++]=S[k++],E[s++]=S[k++],w-=3;w&&(E[s++]=S[k++],1<w&&(E[s++]=S[k++]))}else{for(k=s-x;E[s++]=E[k++],E[s++]=E[k++],E[s++]=E[k++],2<(w-=3););w&&(E[s++]=E[k++],1<w&&(E[s++]=E[k++]))}break}}break}while(r<n&&s<o);r-=w=p>>3,f&=(1<<(p-=w<<3))-1,t.next_in=r,t.next_out=s,t.avail_in=r<n?n-r+5:5-(r-n),t.avail_out=s<o?o-s+257:257-(s-o),i.hold=f,i.bits=p}},{}],49:[function(t,e,i){"use strict";var r=t("../utils/common"),n=t("./adler32"),s=t("./crc32"),a=t("./inffast"),o=t("./inftrees");function l(t){return(t>>>24&255)+(t>>>8&65280)+((65280&t)<<8)+((255&t)<<24)}function h(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new r.Buf16(320),this.work=new r.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function u(t){var e;return t&&t.state?(e=t.state,t.total_in=t.total_out=e.total=0,t.msg="",e.wrap&&(t.adler=1&e.wrap),e.mode=1,e.last=0,e.havedict=0,e.dmax=32768,e.head=null,e.hold=0,e.bits=0,e.lencode=e.lendyn=new r.Buf32(852),e.distcode=e.distdyn=new r.Buf32(592),e.sane=1,e.back=-1,0):-2}function d(t){var e;return t&&t.state?((e=t.state).wsize=0,e.whave=0,e.wnext=0,u(t)):-2}function c(t,e){var i,r;return t&&t.state?(r=t.state,e<0?(i=0,e=-e):(i=1+(e>>4),e<48&&(e&=15)),e&&(e<8||15<e)?-2:(null!==r.window&&r.wbits!==e&&(r.window=null),r.wrap=i,r.wbits=e,d(t))):-2}function f(t,e){var i;return t?((t.state=new h).window=null,0!==(i=c(t,e))&&(t.state=null),i):-2}var p,m,g=!0;function y(t,e,i,n){var s,a=t.state;return null===a.window&&(a.wsize=1<<a.wbits,a.wnext=0,a.whave=0,a.window=new r.Buf8(a.wsize)),n>=a.wsize?(r.arraySet(a.window,e,i-a.wsize,a.wsize,0),a.wnext=0,a.whave=a.wsize):(n<(s=a.wsize-a.wnext)&&(s=n),r.arraySet(a.window,e,i-n,s,a.wnext),(n-=s)?(r.arraySet(a.window,e,i-n,n,0),a.wnext=n,a.whave=a.wsize):(a.wnext+=s,a.wnext===a.wsize&&(a.wnext=0),a.whave<a.wsize&&(a.whave+=s))),0}i.inflateReset=d,i.inflateReset2=c,i.inflateResetKeep=u,i.inflateInit=function(t){return f(t,15)},i.inflateInit2=f,i.inflate=function(t,e){var i,h,u,d,c,f,v,b,_,w,x,k,S,A,E,T,C,P,R,M,D,z,O,I,L=0,F=new r.Buf8(4),j=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!t||!t.state||!t.output||!t.input&&0!==t.avail_in)return -2;12===(i=t.state).mode&&(i.mode=13),c=t.next_out,u=t.output,v=t.avail_out,d=t.next_in,h=t.input,f=t.avail_in,b=i.hold,_=i.bits,w=f,x=v,z=0;t:for(;;)switch(i.mode){case 1:if(0===i.wrap){i.mode=13;break}for(;_<16;){if(0===f)break t;f--,b+=h[d++]<<_,_+=8}if(2&i.wrap&&35615===b){F[i.check=0]=255&b,F[1]=b>>>8&255,i.check=s(i.check,F,2,0),_=b=0,i.mode=2;break}if(i.flags=0,i.head&&(i.head.done=!1),!(1&i.wrap)||(((255&b)<<8)+(b>>8))%31){t.msg="incorrect header check",i.mode=30;break}if(8!=(15&b)){t.msg="unknown compression method",i.mode=30;break}if(_-=4,D=8+(15&(b>>>=4)),0===i.wbits)i.wbits=D;else if(D>i.wbits){t.msg="invalid window size",i.mode=30;break}i.dmax=1<<D,t.adler=i.check=1,i.mode=512&b?10:12,_=b=0;break;case 2:for(;_<16;){if(0===f)break t;f--,b+=h[d++]<<_,_+=8}if(i.flags=b,8!=(255&i.flags)){t.msg="unknown compression method",i.mode=30;break}if(57344&i.flags){t.msg="unknown header flags set",i.mode=30;break}i.head&&(i.head.text=b>>8&1),512&i.flags&&(F[0]=255&b,F[1]=b>>>8&255,i.check=s(i.check,F,2,0)),_=b=0,i.mode=3;case 3:for(;_<32;){if(0===f)break t;f--,b+=h[d++]<<_,_+=8}i.head&&(i.head.time=b),512&i.flags&&(F[0]=255&b,F[1]=b>>>8&255,F[2]=b>>>16&255,F[3]=b>>>24&255,i.check=s(i.check,F,4,0)),_=b=0,i.mode=4;case 4:for(;_<16;){if(0===f)break t;f--,b+=h[d++]<<_,_+=8}i.head&&(i.head.xflags=255&b,i.head.os=b>>8),512&i.flags&&(F[0]=255&b,F[1]=b>>>8&255,i.check=s(i.check,F,2,0)),_=b=0,i.mode=5;case 5:if(1024&i.flags){for(;_<16;){if(0===f)break t;f--,b+=h[d++]<<_,_+=8}i.length=b,i.head&&(i.head.extra_len=b),512&i.flags&&(F[0]=255&b,F[1]=b>>>8&255,i.check=s(i.check,F,2,0)),_=b=0}else i.head&&(i.head.extra=null);i.mode=6;case 6:if(1024&i.flags&&(f<(k=i.length)&&(k=f),k&&(i.head&&(D=i.head.extra_len-i.length,i.head.extra||(i.head.extra=Array(i.head.extra_len)),r.arraySet(i.head.extra,h,d,k,D)),512&i.flags&&(i.check=s(i.check,h,k,d)),f-=k,d+=k,i.length-=k),i.length))break t;i.length=0,i.mode=7;case 7:if(2048&i.flags){if(0===f)break t;for(k=0;D=h[d+k++],i.head&&D&&i.length<65536&&(i.head.name+=String.fromCharCode(D)),D&&k<f;);if(512&i.flags&&(i.check=s(i.check,h,k,d)),f-=k,d+=k,D)break t}else i.head&&(i.head.name=null);i.length=0,i.mode=8;case 8:if(4096&i.flags){if(0===f)break t;for(k=0;D=h[d+k++],i.head&&D&&i.length<65536&&(i.head.comment+=String.fromCharCode(D)),D&&k<f;);if(512&i.flags&&(i.check=s(i.check,h,k,d)),f-=k,d+=k,D)break t}else i.head&&(i.head.comment=null);i.mode=9;case 9:if(512&i.flags){for(;_<16;){if(0===f)break t;f--,b+=h[d++]<<_,_+=8}if(b!==(65535&i.check)){t.msg="header crc mismatch",i.mode=30;break}_=b=0}i.head&&(i.head.hcrc=i.flags>>9&1,i.head.done=!0),t.adler=i.check=0,i.mode=12;break;case 10:for(;_<32;){if(0===f)break t;f--,b+=h[d++]<<_,_+=8}t.adler=i.check=l(b),_=b=0,i.mode=11;case 11:if(0===i.havedict)return t.next_out=c,t.avail_out=v,t.next_in=d,t.avail_in=f,i.hold=b,i.bits=_,2;t.adler=i.check=1,i.mode=12;case 12:if(5===e||6===e)break t;case 13:if(i.last){b>>>=7&_,_-=7&_,i.mode=27;break}for(;_<3;){if(0===f)break t;f--,b+=h[d++]<<_,_+=8}switch(i.last=1&b,_-=1,3&(b>>>=1)){case 0:i.mode=14;break;case 1:if(function(t){if(g){var e;for(p=new r.Buf32(512),m=new r.Buf32(32),e=0;e<144;)t.lens[e++]=8;for(;e<256;)t.lens[e++]=9;for(;e<280;)t.lens[e++]=7;for(;e<288;)t.lens[e++]=8;for(o(1,t.lens,0,288,p,0,t.work,{bits:9}),e=0;e<32;)t.lens[e++]=5;o(2,t.lens,0,32,m,0,t.work,{bits:5}),g=!1}t.lencode=p,t.lenbits=9,t.distcode=m,t.distbits=5}(i),i.mode=20,6!==e)break;b>>>=2,_-=2;break t;case 2:i.mode=17;break;case 3:t.msg="invalid block type",i.mode=30}b>>>=2,_-=2;break;case 14:for(b>>>=7&_,_-=7&_;_<32;){if(0===f)break t;f--,b+=h[d++]<<_,_+=8}if((65535&b)!=(b>>>16^65535)){t.msg="invalid stored block lengths",i.mode=30;break}if(i.length=65535&b,_=b=0,i.mode=15,6===e)break t;case 15:i.mode=16;case 16:if(k=i.length){if(f<k&&(k=f),v<k&&(k=v),0===k)break t;r.arraySet(u,h,d,k,c),f-=k,d+=k,v-=k,c+=k,i.length-=k;break}i.mode=12;break;case 17:for(;_<14;){if(0===f)break t;f--,b+=h[d++]<<_,_+=8}if(i.nlen=257+(31&b),_-=5,i.ndist=1+(31&(b>>>=5)),_-=5,i.ncode=4+(15&(b>>>=5)),b>>>=4,_-=4,286<i.nlen||30<i.ndist){t.msg="too many length or distance symbols",i.mode=30;break}i.have=0,i.mode=18;case 18:for(;i.have<i.ncode;){for(;_<3;){if(0===f)break t;f--,b+=h[d++]<<_,_+=8}i.lens[j[i.have++]]=7&b,b>>>=3,_-=3}for(;i.have<19;)i.lens[j[i.have++]]=0;if(i.lencode=i.lendyn,i.lenbits=7,O={bits:i.lenbits},z=o(0,i.lens,0,19,i.lencode,0,i.work,O),i.lenbits=O.bits,z){t.msg="invalid code lengths set",i.mode=30;break}i.have=0,i.mode=19;case 19:for(;i.have<i.nlen+i.ndist;){for(;T=(L=i.lencode[b&(1<<i.lenbits)-1])>>>16&255,C=65535&L,!((E=L>>>24)<=_);){if(0===f)break t;f--,b+=h[d++]<<_,_+=8}if(C<16)b>>>=E,_-=E,i.lens[i.have++]=C;else{if(16===C){for(I=E+2;_<I;){if(0===f)break t;f--,b+=h[d++]<<_,_+=8}if(b>>>=E,_-=E,0===i.have){t.msg="invalid bit length repeat",i.mode=30;break}D=i.lens[i.have-1],k=3+(3&b),b>>>=2,_-=2}else if(17===C){for(I=E+3;_<I;){if(0===f)break t;f--,b+=h[d++]<<_,_+=8}_-=E,D=0,k=3+(7&(b>>>=E)),b>>>=3,_-=3}else{for(I=E+7;_<I;){if(0===f)break t;f--,b+=h[d++]<<_,_+=8}_-=E,D=0,k=11+(127&(b>>>=E)),b>>>=7,_-=7}if(i.have+k>i.nlen+i.ndist){t.msg="invalid bit length repeat",i.mode=30;break}for(;k--;)i.lens[i.have++]=D}}if(30===i.mode)break;if(0===i.lens[256]){t.msg="invalid code -- missing end-of-block",i.mode=30;break}if(i.lenbits=9,O={bits:i.lenbits},z=o(1,i.lens,0,i.nlen,i.lencode,0,i.work,O),i.lenbits=O.bits,z){t.msg="invalid literal/lengths set",i.mode=30;break}if(i.distbits=6,i.distcode=i.distdyn,O={bits:i.distbits},z=o(2,i.lens,i.nlen,i.ndist,i.distcode,0,i.work,O),i.distbits=O.bits,z){t.msg="invalid distances set",i.mode=30;break}if(i.mode=20,6===e)break t;case 20:i.mode=21;case 21:if(6<=f&&258<=v){t.next_out=c,t.avail_out=v,t.next_in=d,t.avail_in=f,i.hold=b,i.bits=_,a(t,x),c=t.next_out,u=t.output,v=t.avail_out,d=t.next_in,h=t.input,f=t.avail_in,b=i.hold,_=i.bits,12===i.mode&&(i.back=-1);break}for(i.back=0;T=(L=i.lencode[b&(1<<i.lenbits)-1])>>>16&255,C=65535&L,!((E=L>>>24)<=_);){if(0===f)break t;f--,b+=h[d++]<<_,_+=8}if(T&&0==(240&T)){for(P=E,R=T,M=C;T=(L=i.lencode[M+((b&(1<<P+R)-1)>>P)])>>>16&255,C=65535&L,!(P+(E=L>>>24)<=_);){if(0===f)break t;f--,b+=h[d++]<<_,_+=8}b>>>=P,_-=P,i.back+=P}if(b>>>=E,_-=E,i.back+=E,i.length=C,0===T){i.mode=26;break}if(32&T){i.back=-1,i.mode=12;break}if(64&T){t.msg="invalid literal/length code",i.mode=30;break}i.extra=15&T,i.mode=22;case 22:if(i.extra){for(I=i.extra;_<I;){if(0===f)break t;f--,b+=h[d++]<<_,_+=8}i.length+=b&(1<<i.extra)-1,b>>>=i.extra,_-=i.extra,i.back+=i.extra}i.was=i.length,i.mode=23;case 23:for(;T=(L=i.distcode[b&(1<<i.distbits)-1])>>>16&255,C=65535&L,!((E=L>>>24)<=_);){if(0===f)break t;f--,b+=h[d++]<<_,_+=8}if(0==(240&T)){for(P=E,R=T,M=C;T=(L=i.distcode[M+((b&(1<<P+R)-1)>>P)])>>>16&255,C=65535&L,!(P+(E=L>>>24)<=_);){if(0===f)break t;f--,b+=h[d++]<<_,_+=8}b>>>=P,_-=P,i.back+=P}if(b>>>=E,_-=E,i.back+=E,64&T){t.msg="invalid distance code",i.mode=30;break}i.offset=C,i.extra=15&T,i.mode=24;case 24:if(i.extra){for(I=i.extra;_<I;){if(0===f)break t;f--,b+=h[d++]<<_,_+=8}i.offset+=b&(1<<i.extra)-1,b>>>=i.extra,_-=i.extra,i.back+=i.extra}if(i.offset>i.dmax){t.msg="invalid distance too far back",i.mode=30;break}i.mode=25;case 25:if(0===v)break t;if(k=x-v,i.offset>k){if((k=i.offset-k)>i.whave&&i.sane){t.msg="invalid distance too far back",i.mode=30;break}S=k>i.wnext?(k-=i.wnext,i.wsize-k):i.wnext-k,k>i.length&&(k=i.length),A=i.window}else A=u,S=c-i.offset,k=i.length;for(v<k&&(k=v),v-=k,i.length-=k;u[c++]=A[S++],--k;);0===i.length&&(i.mode=21);break;case 26:if(0===v)break t;u[c++]=i.length,v--,i.mode=21;break;case 27:if(i.wrap){for(;_<32;){if(0===f)break t;f--,b|=h[d++]<<_,_+=8}if(x-=v,t.total_out+=x,i.total+=x,x&&(t.adler=i.check=i.flags?s(i.check,u,x,c-x):n(i.check,u,x,c-x)),x=v,(i.flags?b:l(b))!==i.check){t.msg="incorrect data check",i.mode=30;break}_=b=0}i.mode=28;case 28:if(i.wrap&&i.flags){for(;_<32;){if(0===f)break t;f--,b+=h[d++]<<_,_+=8}if(b!==(0|i.total)){t.msg="incorrect length check",i.mode=30;break}_=b=0}i.mode=29;case 29:z=1;break t;case 30:z=-3;break t;case 31:return -4;default:return -2}return t.next_out=c,t.avail_out=v,t.next_in=d,t.avail_in=f,i.hold=b,i.bits=_,(i.wsize||x!==t.avail_out&&i.mode<30&&(i.mode<27||4!==e))&&y(t,t.output,t.next_out,x-t.avail_out)?(i.mode=31,-4):(w-=t.avail_in,x-=t.avail_out,t.total_in+=w,t.total_out+=x,i.total+=x,i.wrap&&x&&(t.adler=i.check=i.flags?s(i.check,u,x,t.next_out-x):n(i.check,u,x,t.next_out-x)),t.data_type=i.bits+64*!!i.last+128*(12===i.mode)+256*(20===i.mode||15===i.mode),(0==w&&0===x||4===e)&&0===z&&(z=-5),z)},i.inflateEnd=function(t){if(!t||!t.state)return -2;var e=t.state;return e.window&&(e.window=null),t.state=null,0},i.inflateGetHeader=function(t,e){var i;return t&&t.state?0==(2&(i=t.state).wrap)?-2:((i.head=e).done=!1,0):-2},i.inflateSetDictionary=function(t,e){var i,r=e.length;return t&&t.state?0!==(i=t.state).wrap&&11!==i.mode?-2:11===i.mode&&n(1,e,r,0)!==i.check?-3:y(t,e,r,r)?(i.mode=31,-4):(i.havedict=1,0):-2},i.inflateInfo="pako inflate (from Nodeca project)"},{"../utils/common":41,"./adler32":43,"./crc32":45,"./inffast":48,"./inftrees":50}],50:[function(t,e,i){"use strict";var r=t("../utils/common"),n=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],s=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],a=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],o=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];e.exports=function(t,e,i,l,h,u,d,c){var f,p,m,g,y,v,b,_,w,x=c.bits,k=0,S=0,A=0,E=0,T=0,C=0,P=0,R=0,M=0,D=0,z=null,O=0,I=new r.Buf16(16),L=new r.Buf16(16),F=null,j=0;for(k=0;k<=15;k++)I[k]=0;for(S=0;S<l;S++)I[e[i+S]]++;for(T=x,E=15;1<=E&&0===I[E];E--);if(E<T&&(T=E),0===E)return h[u++]=0x1400000,h[u++]=0x1400000,c.bits=1,0;for(A=1;A<E&&0===I[A];A++);for(T<A&&(T=A),k=R=1;k<=15;k++)if(R<<=1,(R-=I[k])<0)return -1;if(0<R&&(0===t||1!==E))return -1;for(L[1]=0,k=1;k<15;k++)L[k+1]=L[k]+I[k];for(S=0;S<l;S++)0!==e[i+S]&&(d[L[e[i+S]]++]=S);if(v=0===t?(z=F=d,19):1===t?(z=n,O-=257,F=s,j-=257,256):(z=a,F=o,-1),k=A,y=u,P=S=D=0,m=-1,g=(M=1<<(C=T))-1,1===t&&852<M||2===t&&592<M)return 1;for(;;){for(b=k-P,w=d[S]<v?(_=0,d[S]):d[S]>v?(_=F[j+d[S]],z[O+d[S]]):(_=96,0),f=1<<k-P,A=p=1<<C;h[y+(D>>P)+(p-=f)]=b<<24|_<<16|w,0!==p;);for(f=1<<k-1;D&f;)f>>=1;if(0!==f?(D&=f-1,D+=f):D=0,S++,0==--I[k]){if(k===E)break;k=e[i+d[S]]}if(T<k&&(D&g)!==m){for(0===P&&(P=T),y+=A,R=1<<(C=k-P);C+P<E&&!((R-=I[C+P])<=0);)C++,R<<=1;if(M+=1<<C,1===t&&852<M||2===t&&592<M)return 1;h[m=D&g]=T<<24|C<<16|y-u}}return 0!==D&&(h[y+D]=k-P<<24|4194304),c.bits=T,0}},{"../utils/common":41}],51:[function(t,e,i){"use strict";e.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},{}],52:[function(t,e,i){"use strict";var r=t("../utils/common");function n(t){for(var e=t.length;0<=--e;)t[e]=0}var s=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],a=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],l=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],h=Array(576);n(h);var u=Array(60);n(u);var d=Array(512);n(d);var c=Array(256);n(c);var f=Array(29);n(f);var p,m,g,y=Array(30);function v(t,e,i,r,n){this.static_tree=t,this.extra_bits=e,this.extra_base=i,this.elems=r,this.max_length=n,this.has_stree=t&&t.length}function b(t,e){this.dyn_tree=t,this.max_code=0,this.stat_desc=e}function _(t){return t<256?d[t]:d[256+(t>>>7)]}function w(t,e){t.pending_buf[t.pending++]=255&e,t.pending_buf[t.pending++]=e>>>8&255}function x(t,e,i){t.bi_valid>16-i?(t.bi_buf|=e<<t.bi_valid&65535,w(t,t.bi_buf),t.bi_buf=e>>16-t.bi_valid,t.bi_valid+=i-16):(t.bi_buf|=e<<t.bi_valid&65535,t.bi_valid+=i)}function k(t,e,i){x(t,i[2*e],i[2*e+1])}function S(t,e){for(var i=0;i|=1&t,t>>>=1,i<<=1,0<--e;);return i>>>1}function A(t,e,i){var r,n,s=Array(16),a=0;for(r=1;r<=15;r++)s[r]=a=a+i[r-1]<<1;for(n=0;n<=e;n++){var o=t[2*n+1];0!==o&&(t[2*n]=S(s[o]++,o))}}function E(t){var e;for(e=0;e<286;e++)t.dyn_ltree[2*e]=0;for(e=0;e<30;e++)t.dyn_dtree[2*e]=0;for(e=0;e<19;e++)t.bl_tree[2*e]=0;t.dyn_ltree[512]=1,t.opt_len=t.static_len=0,t.last_lit=t.matches=0}function T(t){8<t.bi_valid?w(t,t.bi_buf):0<t.bi_valid&&(t.pending_buf[t.pending++]=t.bi_buf),t.bi_buf=0,t.bi_valid=0}function C(t,e,i,r){var n=2*e,s=2*i;return t[n]<t[s]||t[n]===t[s]&&r[e]<=r[i]}function P(t,e,i){for(var r=t.heap[i],n=i<<1;n<=t.heap_len&&(n<t.heap_len&&C(e,t.heap[n+1],t.heap[n],t.depth)&&n++,!C(e,r,t.heap[n],t.depth));)t.heap[i]=t.heap[n],i=n,n<<=1;t.heap[i]=r}function R(t,e,i){var r,n,o,l,h=0;if(0!==t.last_lit)for(;r=t.pending_buf[t.d_buf+2*h]<<8|t.pending_buf[t.d_buf+2*h+1],n=t.pending_buf[t.l_buf+h],h++,0===r?k(t,n,e):(k(t,(o=c[n])+256+1,e),0!==(l=s[o])&&x(t,n-=f[o],l),k(t,o=_(--r),i),0!==(l=a[o])&&x(t,r-=y[o],l)),h<t.last_lit;);k(t,256,e)}function M(t,e){var i,r,n,s=e.dyn_tree,a=e.stat_desc.static_tree,o=e.stat_desc.has_stree,l=e.stat_desc.elems,h=-1;for(t.heap_len=0,t.heap_max=573,i=0;i<l;i++)0!==s[2*i]?(t.heap[++t.heap_len]=h=i,t.depth[i]=0):s[2*i+1]=0;for(;t.heap_len<2;)s[2*(n=t.heap[++t.heap_len]=h<2?++h:0)]=1,t.depth[n]=0,t.opt_len--,o&&(t.static_len-=a[2*n+1]);for(e.max_code=h,i=t.heap_len>>1;1<=i;i--)P(t,s,i);for(n=l;i=t.heap[1],t.heap[1]=t.heap[t.heap_len--],P(t,s,1),r=t.heap[1],t.heap[--t.heap_max]=i,t.heap[--t.heap_max]=r,s[2*n]=s[2*i]+s[2*r],t.depth[n]=(t.depth[i]>=t.depth[r]?t.depth[i]:t.depth[r])+1,s[2*i+1]=s[2*r+1]=n,t.heap[1]=n++,P(t,s,1),2<=t.heap_len;);t.heap[--t.heap_max]=t.heap[1],function(t,e){var i,r,n,s,a,o,l=e.dyn_tree,h=e.max_code,u=e.stat_desc.static_tree,d=e.stat_desc.has_stree,c=e.stat_desc.extra_bits,f=e.stat_desc.extra_base,p=e.stat_desc.max_length,m=0;for(s=0;s<=15;s++)t.bl_count[s]=0;for(l[2*t.heap[t.heap_max]+1]=0,i=t.heap_max+1;i<573;i++)p<(s=l[2*l[2*(r=t.heap[i])+1]+1]+1)&&(s=p,m++),l[2*r+1]=s,h<r||(t.bl_count[s]++,a=0,f<=r&&(a=c[r-f]),o=l[2*r],t.opt_len+=o*(s+a),d&&(t.static_len+=o*(u[2*r+1]+a)));if(0!==m){do{for(s=p-1;0===t.bl_count[s];)s--;t.bl_count[s]--,t.bl_count[s+1]+=2,t.bl_count[p]--,m-=2}while(0<m);for(s=p;0!==s;s--)for(r=t.bl_count[s];0!==r;)h<(n=t.heap[--i])||(l[2*n+1]!==s&&(t.opt_len+=(s-l[2*n+1])*l[2*n],l[2*n+1]=s),r--)}}(t,e),A(s,h,t.bl_count)}function D(t,e,i){var r,n,s=-1,a=e[1],o=0,l=7,h=4;for(0===a&&(l=138,h=3),e[2*(i+1)+1]=65535,r=0;r<=i;r++)n=a,a=e[2*(r+1)+1],++o<l&&n===a||(o<h?t.bl_tree[2*n]+=o:0!==n?(n!==s&&t.bl_tree[2*n]++,t.bl_tree[32]++):o<=10?t.bl_tree[34]++:t.bl_tree[36]++,s=n,h=(o=0)===a?(l=138,3):n===a?(l=6,3):(l=7,4))}function z(t,e,i){var r,n,s=-1,a=e[1],o=0,l=7,h=4;for(0===a&&(l=138,h=3),r=0;r<=i;r++)if(n=a,a=e[2*(r+1)+1],!(++o<l&&n===a)){if(o<h)for(;k(t,n,t.bl_tree),0!=--o;);else 0!==n?(n!==s&&(k(t,n,t.bl_tree),o--),k(t,16,t.bl_tree),x(t,o-3,2)):o<=10?(k(t,17,t.bl_tree),x(t,o-3,3)):(k(t,18,t.bl_tree),x(t,o-11,7));s=n,h=(o=0)===a?(l=138,3):n===a?(l=6,3):(l=7,4)}}n(y);var O=!1;function I(t,e,i,n){x(t,0+ +!!n,3),T(t),w(t,i),w(t,~i),r.arraySet(t.pending_buf,t.window,e,i,t.pending),t.pending+=i}i._tr_init=function(t){O||(function(){var t,e,i,r,n,l=Array(16);for(r=i=0;r<28;r++)for(f[r]=i,t=0;t<1<<s[r];t++)c[i++]=r;for(c[i-1]=r,r=n=0;r<16;r++)for(y[r]=n,t=0;t<1<<a[r];t++)d[n++]=r;for(n>>=7;r<30;r++)for(y[r]=n<<7,t=0;t<1<<a[r]-7;t++)d[256+n++]=r;for(e=0;e<=15;e++)l[e]=0;for(t=0;t<=143;)h[2*t+1]=8,t++,l[8]++;for(;t<=255;)h[2*t+1]=9,t++,l[9]++;for(;t<=279;)h[2*t+1]=7,t++,l[7]++;for(;t<=287;)h[2*t+1]=8,t++,l[8]++;for(A(h,287,l),t=0;t<30;t++)u[2*t+1]=5,u[2*t]=S(t,5);p=new v(h,s,257,286,15),m=new v(u,a,0,30,15),g=new v([],o,0,19,7)}(),O=!0),t.l_desc=new b(t.dyn_ltree,p),t.d_desc=new b(t.dyn_dtree,m),t.bl_desc=new b(t.bl_tree,g),t.bi_buf=0,t.bi_valid=0,E(t)},i._tr_stored_block=I,i._tr_flush_block=function(t,e,i,r){var n,s,a=0;0<t.level?(2===t.strm.data_type&&(t.strm.data_type=function(t){var e,i=0xf3ffc07f;for(e=0;e<=31;e++,i>>>=1)if(1&i&&0!==t.dyn_ltree[2*e])return 0;if(0!==t.dyn_ltree[18]||0!==t.dyn_ltree[20]||0!==t.dyn_ltree[26])return 1;for(e=32;e<256;e++)if(0!==t.dyn_ltree[2*e])return 1;return 0}(t)),M(t,t.l_desc),M(t,t.d_desc),a=function(t){var e;for(D(t,t.dyn_ltree,t.l_desc.max_code),D(t,t.dyn_dtree,t.d_desc.max_code),M(t,t.bl_desc),e=18;3<=e&&0===t.bl_tree[2*l[e]+1];e--);return t.opt_len+=3*(e+1)+5+5+4,e}(t),n=t.opt_len+3+7>>>3,(s=t.static_len+3+7>>>3)<=n&&(n=s)):n=s=i+5,i+4<=n&&-1!==e?I(t,e,i,r):4===t.strategy||s===n?(x(t,2+ +!!r,3),R(t,h,u)):(x(t,4+ +!!r,3),function(t,e,i,r){var n;for(x(t,e-257,5),x(t,i-1,5),x(t,r-4,4),n=0;n<r;n++)x(t,t.bl_tree[2*l[n]+1],3);z(t,t.dyn_ltree,e-1),z(t,t.dyn_dtree,i-1)}(t,t.l_desc.max_code+1,t.d_desc.max_code+1,a+1),R(t,t.dyn_ltree,t.dyn_dtree)),E(t),r&&T(t)},i._tr_tally=function(t,e,i){return t.pending_buf[t.d_buf+2*t.last_lit]=e>>>8&255,t.pending_buf[t.d_buf+2*t.last_lit+1]=255&e,t.pending_buf[t.l_buf+t.last_lit]=255&i,t.last_lit++,0===e?t.dyn_ltree[2*i]++:(t.matches++,e--,t.dyn_ltree[2*(c[i]+256+1)]++,t.dyn_dtree[2*_(e)]++),t.last_lit===t.lit_bufsize-1},i._tr_align=function(t){x(t,2,3),k(t,256,h),16===t.bi_valid?(w(t,t.bi_buf),t.bi_buf=0,t.bi_valid=0):8<=t.bi_valid&&(t.pending_buf[t.pending++]=255&t.bi_buf,t.bi_buf>>=8,t.bi_valid-=8)}},{"../utils/common":41}],53:[function(t,e,i){"use strict";e.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},{}],54:[function(t,e,r){(function(t){!function(t,e){"use strict";if(!t.setImmediate){var i,r,s,a,o=1,l={},h=!1,u=t.document,d=Object.getPrototypeOf&&Object.getPrototypeOf(t);d=d&&d.setTimeout?d:t,i="[object process]"===({}).toString.call(t.process)?function(t){n.nextTick(function(){f(t)})}:!function(){if(t.postMessage&&!t.importScripts){var e=!0,i=t.onmessage;return t.onmessage=function(){e=!1},t.postMessage("","*"),t.onmessage=i,e}}()?t.MessageChannel?((s=new MessageChannel).port1.onmessage=function(t){f(t.data)},function(t){s.port2.postMessage(t)}):u&&"onreadystatechange"in u.createElement("script")?(r=u.documentElement,function(t){var e=u.createElement("script");e.onreadystatechange=function(){f(t),e.onreadystatechange=null,r.removeChild(e),e=null},r.appendChild(e)}):function(t){setTimeout(f,0,t)}:(a="setImmediate$"+Math.random()+"$",t.addEventListener?t.addEventListener("message",p,!1):t.attachEvent("onmessage",p),function(e){t.postMessage(a+e,"*")}),d.setImmediate=function(t){"function"!=typeof t&&(t=Function(""+t));for(var e=Array(arguments.length-1),r=0;r<e.length;r++)e[r]=arguments[r+1];var n={callback:t,args:e};return l[o]=n,i(o),o++},d.clearImmediate=c}function c(t){delete l[t]}function f(t){if(h)setTimeout(f,0,t);else{var i=l[t];if(i){h=!0;try{var r=i.callback,n=i.args;switch(n.length){case 0:r();break;case 1:r(n[0]);break;case 2:r(n[0],n[1]);break;case 3:r(n[0],n[1],n[2]);break;default:r.apply(e,n)}}finally{c(t),h=!1}}}}function p(e){e.source===t&&"string"==typeof e.data&&0===e.data.indexOf(a)&&f(+e.data.slice(a.length))}}("undefined"==typeof self?void 0===t?this:t:self)}).call(this,void 0!==i.g?i.g:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}]},{},[10])(10)},59964:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]])},60408:function(t,e){var i,r;void 0===(r="function"==typeof(i=function t(){var e,i="undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==i?i:{},r=!i.document&&!!i.postMessage,n=i.IS_PAPA_WORKER||!1,s={},a=0,o={};function l(t){this._handle=null,this._finished=!1,this._completed=!1,this._halted=!1,this._input=null,this._baseIndex=0,this._partialLine="",this._rowCount=0,this._start=0,this._nextChunk=null,this.isFirstChunk=!0,this._completeResults={data:[],errors:[],meta:{}},(function(t){var e=b(t);e.chunkSize=parseInt(e.chunkSize),t.step||t.chunk||(e.chunkSize=null),this._handle=new f(e),(this._handle.streamer=this)._config=e}).call(this,t),this.parseChunk=function(t,e){var r=parseInt(this._config.skipFirstNLines)||0;if(this.isFirstChunk&&0<r){let e=this._config.newline;e||(s=this._config.quoteChar||'"',e=this._handle.guessLineEndings(t,s)),t=[...t.split(e).slice(r)].join(e)}this.isFirstChunk&&w(this._config.beforeFirstChunk)&&void 0!==(s=this._config.beforeFirstChunk(t))&&(t=s),this.isFirstChunk=!1,this._halted=!1;var r=this._partialLine+t,s=(this._partialLine="",this._handle.parse(r,this._baseIndex,!this._finished));if(!this._handle.paused()&&!this._handle.aborted()){if(t=s.meta.cursor,this._finished||(this._partialLine=r.substring(t-this._baseIndex),this._baseIndex=t),s&&s.data&&(this._rowCount+=s.data.length),r=this._finished||this._config.preview&&this._rowCount>=this._config.preview,n)i.postMessage({results:s,workerId:o.WORKER_ID,finished:r});else if(w(this._config.chunk)&&!e){if(this._config.chunk(s,this._handle),this._handle.paused()||this._handle.aborted())return void(this._halted=!0);this._completeResults=s=void 0}return this._config.step||this._config.chunk||(this._completeResults.data=this._completeResults.data.concat(s.data),this._completeResults.errors=this._completeResults.errors.concat(s.errors),this._completeResults.meta=s.meta),this._completed||!r||!w(this._config.complete)||s&&s.meta.aborted||(this._config.complete(this._completeResults,this._input),this._completed=!0),r||s&&s.meta.paused||this._nextChunk(),s}this._halted=!0},this._sendError=function(t){w(this._config.error)?this._config.error(t):n&&this._config.error&&i.postMessage({workerId:o.WORKER_ID,error:t,finished:!1})}}function h(t){var e;(t=t||{}).chunkSize||(t.chunkSize=o.RemoteChunkSize),l.call(this,t),this._nextChunk=r?function(){this._readChunk(),this._chunkLoaded()}:function(){this._readChunk()},this.stream=function(t){this._input=t,this._nextChunk()},this._readChunk=function(){if(this._finished)this._chunkLoaded();else{if(e=new XMLHttpRequest,this._config.withCredentials&&(e.withCredentials=this._config.withCredentials),r||(e.onload=_(this._chunkLoaded,this),e.onerror=_(this._chunkError,this)),e.open(this._config.downloadRequestBody?"POST":"GET",this._input,!r),this._config.downloadRequestHeaders){var t,i,n=this._config.downloadRequestHeaders;for(i in n)e.setRequestHeader(i,n[i])}this._config.chunkSize&&(t=this._start+this._config.chunkSize-1,e.setRequestHeader("Range","bytes="+this._start+"-"+t));try{e.send(this._config.downloadRequestBody)}catch(t){this._chunkError(t.message)}r&&0===e.status&&this._chunkError()}},this._chunkLoaded=function(){let t;4===e.readyState&&(e.status<200||400<=e.status?this._chunkError():(this._start+=this._config.chunkSize||e.responseText.length,this._finished=!this._config.chunkSize||this._start>=(null!==(t=(t=e).getResponseHeader("Content-Range"))?parseInt(t.substring(t.lastIndexOf("/")+1)):-1),this.parseChunk(e.responseText)))},this._chunkError=function(t){t=e.statusText||t,this._sendError(Error(t))}}function u(t){(t=t||{}).chunkSize||(t.chunkSize=o.LocalChunkSize),l.call(this,t);var e,i,r="undefined"!=typeof FileReader;this.stream=function(t){this._input=t,i=t.slice||t.webkitSlice||t.mozSlice,r?((e=new FileReader).onload=_(this._chunkLoaded,this),e.onerror=_(this._chunkError,this)):e=new FileReaderSync,this._nextChunk()},this._nextChunk=function(){this._finished||this._config.preview&&!(this._rowCount<this._config.preview)||this._readChunk()},this._readChunk=function(){var t=this._input,n=(this._config.chunkSize&&(n=Math.min(this._start+this._config.chunkSize,this._input.size),t=i.call(t,this._start,n)),e.readAsText(t,this._config.encoding));r||this._chunkLoaded({target:{result:n}})},this._chunkLoaded=function(t){this._start+=this._config.chunkSize,this._finished=!this._config.chunkSize||this._start>=this._input.size,this.parseChunk(t.target.result)},this._chunkError=function(){this._sendError(e.error)}}function d(t){var e;l.call(this,t=t||{}),this.stream=function(t){return e=t,this._nextChunk()},this._nextChunk=function(){var t,i;if(!this._finished)return e=(t=this._config.chunkSize)?(i=e.substring(0,t),e.substring(t)):(i=e,""),this._finished=!e,this.parseChunk(i)}}function c(t){l.call(this,t=t||{});var e=[],i=!0,r=!1;this.pause=function(){l.prototype.pause.apply(this,arguments),this._input.pause()},this.resume=function(){l.prototype.resume.apply(this,arguments),this._input.resume()},this.stream=function(t){this._input=t,this._input.on("data",this._streamData),this._input.on("end",this._streamEnd),this._input.on("error",this._streamError)},this._checkIsFinished=function(){r&&1===e.length&&(this._finished=!0)},this._nextChunk=function(){this._checkIsFinished(),e.length?this.parseChunk(e.shift()):i=!0},this._streamData=_(function(t){try{e.push("string"==typeof t?t:t.toString(this._config.encoding)),i&&(i=!1,this._checkIsFinished(),this.parseChunk(e.shift()))}catch(t){this._streamError(t)}},this),this._streamError=_(function(t){this._streamCleanUp(),this._sendError(t)},this),this._streamEnd=_(function(){this._streamCleanUp(),r=!0,this._streamData("")},this),this._streamCleanUp=_(function(){this._input.removeListener("data",this._streamData),this._input.removeListener("end",this._streamEnd),this._input.removeListener("error",this._streamError)},this)}function f(t){var e,i,r,n,s=/^\s*-?(\d+\.?|\.\d+|\d+\.\d+)([eE][-+]?\d+)?\s*$/,a=/^((\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z))|(\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d([+-][0-2]\d:[0-5]\d|Z)))$/,l=this,h=0,u=0,d=!1,c=!1,f=[],g={data:[],errors:[],meta:{}};function y(e){return"greedy"===t.skipEmptyLines?""===e.join("").trim():1===e.length&&0===e[0].length}function v(){if(g&&r&&(x("Delimiter","UndetectableDelimiter","Unable to auto-detect delimiting character; defaulted to '"+o.DefaultDelimiter+"'"),r=!1),t.skipEmptyLines&&(g.data=g.data.filter(function(t){return!y(t)})),_()){if(g)if(Array.isArray(g.data[0])){for(var e,i=0;_()&&i<g.data.length;i++)g.data[i].forEach(n);g.data.splice(0,1)}else g.data.forEach(n);function n(e,i){w(t.transformHeader)&&(e=t.transformHeader(e,i)),f.push(e)}}function l(e,i){for(var r=t.header?{}:[],n=0;n<e.length;n++){var o=n,l=e[n],l=((e,i)=>(t.dynamicTypingFunction&&void 0===t.dynamicTyping[e]&&(t.dynamicTyping[e]=t.dynamicTypingFunction(e)),!0===(t.dynamicTyping[e]||t.dynamicTyping))?"true"===i||"TRUE"===i||"false"!==i&&"FALSE"!==i&&((t=>{if(s.test(t)&&-0x20000000000000<(t=parseFloat(t))&&t<0x20000000000000)return 1})(i)?parseFloat(i):a.test(i)?new Date(i):""===i?null:i):i)(o=t.header?n>=f.length?"__parsed_extra":f[n]:o,l=t.transform?t.transform(l,o):l);"__parsed_extra"===o?(r[o]=r[o]||[],r[o].push(l)):r[o]=l}return t.header&&(n>f.length?x("FieldMismatch","TooManyFields","Too many fields: expected "+f.length+" fields but parsed "+n,u+i):n<f.length&&x("FieldMismatch","TooFewFields","Too few fields: expected "+f.length+" fields but parsed "+n,u+i)),r}g&&(t.header||t.dynamicTyping||t.transform)&&(e=1,!g.data.length||Array.isArray(g.data[0])?(g.data=g.data.map(l),e=g.data.length):g.data=l(g.data,0),t.header&&g.meta&&(g.meta.fields=f),u+=e)}function _(){return t.header&&0===f.length}function x(t,e,i,r){t={type:t,code:e,message:i},void 0!==r&&(t.row=r),g.errors.push(t)}w(t.step)&&(n=t.step,t.step=function(e){g=e,_()?v():(v(),0!==g.data.length&&(h+=e.data.length,t.preview&&h>t.preview?i.abort():(g.data=g.data[0],n(g,l))))}),this.parse=function(n,s,a){var l=t.quoteChar||'"',l=(t.newline||(t.newline=this.guessLineEndings(n,l)),r=!1,t.delimiter?w(t.delimiter)&&(t.delimiter=t.delimiter(n),g.meta.delimiter=t.delimiter):((l=((e,i,r,n,s)=>{var a,l,h,u;s=s||[",","	","|",";",o.RECORD_SEP,o.UNIT_SEP];for(var d=0;d<s.length;d++){for(var c,f=s[d],p=0,g=0,v=0,b=(h=void 0,new m({comments:n,delimiter:f,newline:i,preview:10}).parse(e)),_=0;_<b.data.length;_++)r&&y(b.data[_])?v++:(g+=c=b.data[_].length,void 0===h?h=c:0<c&&(p+=Math.abs(c-h),h=c));0<b.data.length&&(g/=b.data.length-v),(void 0===l||p<=l)&&(void 0===u||u<g)&&1.99<g&&(l=p,a=f,u=g)}return{successful:!!(t.delimiter=a),bestDelimiter:a}})(n,t.newline,t.skipEmptyLines,t.comments,t.delimitersToGuess)).successful?t.delimiter=l.bestDelimiter:(r=!0,t.delimiter=o.DefaultDelimiter),g.meta.delimiter=t.delimiter),b(t));return t.preview&&t.header&&l.preview++,e=n,g=(i=new m(l)).parse(e,s,a),v(),d?{meta:{paused:!0}}:g||{meta:{paused:!1}}},this.paused=function(){return d},this.pause=function(){d=!0,i.abort(),e=w(t.chunk)?"":e.substring(i.getCharIndex())},this.resume=function(){l.streamer._halted?(d=!1,l.streamer.parseChunk(e,!0)):setTimeout(l.resume,3)},this.aborted=function(){return c},this.abort=function(){c=!0,i.abort(),g.meta.aborted=!0,w(t.complete)&&t.complete(g),e=""},this.guessLineEndings=function(t,e){t=t.substring(0,1048576);var e=RegExp(p(e)+"([^]*?)"+p(e),"gm"),i=(t=t.replace(e,"")).split("\r"),e=t.split("\n"),t=1<e.length&&e[0].length<i[0].length;if(1===i.length||t)return"\n";for(var r=0,n=0;n<i.length;n++)"\n"===i[n][0]&&r++;return r>=i.length/2?"\r\n":"\r"}}function p(t){return t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}function m(t){var e=(t=t||{}).delimiter,i=t.newline,r=t.comments,n=t.step,s=t.preview,a=t.fastMode,l=null,h=!1,u=null==t.quoteChar?'"':t.quoteChar,d=u;if(void 0!==t.escapeChar&&(d=t.escapeChar),("string"!=typeof e||-1<o.BAD_DELIMITERS.indexOf(e))&&(e=","),r===e)throw Error("Comment character same as delimiter");!0===r?r="#":("string"!=typeof r||-1<o.BAD_DELIMITERS.indexOf(r))&&(r=!1),"\n"!==i&&"\r"!==i&&"\r\n"!==i&&(i="\n");var c=0,f=!1;this.parse=function(o,m,g){if("string"!=typeof o)throw Error("Input must be a string");var y=o.length,v=e.length,b=i.length,_=r.length,x=w(n),k=[],S=[],A=[],E=c=0;if(!o)return j();if(a||!1!==a&&-1===o.indexOf(u)){for(var T=o.split(i),C=0;C<T.length;C++){if(A=T[C],c+=A.length,C!==T.length-1)c+=i.length;else if(g)break;if(!r||A.substring(0,_)!==r){if(x){if(k=[],O(A.split(e)),B(),f)return j()}else O(A.split(e));if(s&&s<=C)return k=k.slice(0,s),j(!0)}}return j()}for(var P=o.indexOf(e,c),R=o.indexOf(i,c),M=RegExp(p(d)+p(u),"g"),D=o.indexOf(u,c);;)if(o[c]===u)for(D=c,c++;;){if(-1===(D=o.indexOf(u,D+1)))return g||S.push({type:"Quotes",code:"MissingQuotes",message:"Quoted field unterminated",row:k.length,index:c}),L();if(D===y-1)return L(o.substring(c,D).replace(M,u));if(u===d&&o[D+1]===d)D++;else if(u===d||0===D||o[D-1]!==d){-1!==P&&P<D+1&&(P=o.indexOf(e,D+1));var z=I(-1===(R=-1!==R&&R<D+1?o.indexOf(i,D+1):R)?P:Math.min(P,R));if(o.substr(D+1+z,v)===e){A.push(o.substring(c,D).replace(M,u)),o[c=D+1+z+v]!==u&&(D=o.indexOf(u,c)),P=o.indexOf(e,c),R=o.indexOf(i,c);break}if(z=I(R),o.substring(D+1+z,D+1+z+b)===i){if(A.push(o.substring(c,D).replace(M,u)),F(D+1+z+b),P=o.indexOf(e,c),D=o.indexOf(u,c),x&&(B(),f))return j();if(s&&k.length>=s)return j(!0);break}S.push({type:"Quotes",code:"InvalidQuotes",message:"Trailing quote on quoted field is malformed",row:k.length,index:c}),D++}}else if(r&&0===A.length&&o.substring(c,c+_)===r){if(-1===R)return j();c=R+b,R=o.indexOf(i,c),P=o.indexOf(e,c)}else if(-1!==P&&(P<R||-1===R))A.push(o.substring(c,P)),c=P+v,P=o.indexOf(e,c);else{if(-1===R)break;if(A.push(o.substring(c,R)),F(R+b),x&&(B(),f))return j();if(s&&k.length>=s)return j(!0)}return L();function O(t){k.push(t),E=c}function I(t){return -1!==t&&(t=o.substring(D+1,t))&&""===t.trim()?t.length:0}function L(t){return g||(void 0===t&&(t=o.substring(c)),A.push(t),c=y,O(A),x&&B()),j()}function F(t){c=t,O(A),A=[],R=o.indexOf(i,c)}function j(r){if(t.header&&!m&&k.length&&!h){var n=k[0],s=Object.create(null),a=new Set(n);let e=!1;for(let i=0;i<n.length;i++){let r=n[i];if(s[r=w(t.transformHeader)?t.transformHeader(r,i):r]){let t,o=s[r];for(;t=r+"_"+o,o++,a.has(t););a.add(t),n[i]=t,s[r]++,e=!0,(l=null===l?{}:l)[t]=r}else s[r]=1,n[i]=r;a.add(r)}e&&console.warn("Duplicate headers found and renamed."),h=!0}return{data:k,errors:S,meta:{delimiter:e,linebreak:i,aborted:f,truncated:!!r,cursor:E+(m||0),renamedHeaders:l}}}function B(){n(j()),k=[],S=[]}},this.abort=function(){f=!0},this.getCharIndex=function(){return c}}function g(t){var e=t.data,i=s[e.workerId],r=!1;if(e.error)i.userError(e.error,e.file);else if(e.results&&e.results.data){var n={abort:function(){r=!0,y(e.workerId,{data:[],errors:[],meta:{aborted:!0}})},pause:v,resume:v};if(w(i.userStep)){for(var a=0;a<e.results.data.length&&(i.userStep({data:e.results.data[a],errors:e.results.errors,meta:e.results.meta},n),!r);a++);delete e.results}else w(i.userChunk)&&(i.userChunk(e.results,n,e.file),delete e.results)}e.finished&&!r&&y(e.workerId,e.results)}function y(t,e){var i=s[t];w(i.userComplete)&&i.userComplete(e),i.terminate(),delete s[t]}function v(){throw Error("Not implemented.")}function b(t){if("object"!=typeof t||null===t)return t;var e,i=Array.isArray(t)?[]:{};for(e in t)i[e]=b(t[e]);return i}function _(t,e){return function(){t.apply(e,arguments)}}function w(t){return"function"==typeof t}return o.parse=function(e,r){var n,l,f,p=(r=r||{}).dynamicTyping||!1;if(w(p)&&(r.dynamicTypingFunction=p,p={}),r.dynamicTyping=p,r.transform=!!w(r.transform)&&r.transform,!r.worker||!o.WORKERS_SUPPORTED){let t;return p=null,o.NODE_STREAM_INPUT,"string"==typeof e?(e=65279!==(t=e).charCodeAt(0)?t:t.slice(1),p=new(r.download?h:d)(r)):!0===e.readable&&w(e.read)&&w(e.on)?p=new c(r):(i.File&&e instanceof File||e instanceof Object)&&(p=new u(r)),p.stream(e)}(p=!!o.WORKERS_SUPPORTED&&(l=i.URL||i.webkitURL||null,f=t.toString(),n=o.BLOB_URL||(o.BLOB_URL=l.createObjectURL(new Blob(["var global = (function() { if (typeof self !== 'undefined') { return self; } if (typeof window !== 'undefined') { return window; } if (typeof global !== 'undefined') { return global; } return {}; })(); global.IS_PAPA_WORKER=true; ","(",f,")();"],{type:"text/javascript"}))),(n=new i.Worker(n)).onmessage=g,n.id=a++,s[n.id]=n)).userStep=r.step,p.userChunk=r.chunk,p.userComplete=r.complete,p.userError=r.error,r.step=w(r.step),r.chunk=w(r.chunk),r.complete=w(r.complete),r.error=w(r.error),delete r.worker,p.postMessage({input:e,config:r,workerId:p.id})},o.unparse=function(t,e){var i=!1,r=!0,n=",",s="\r\n",a='"',l=a+a,h=!1,u=null,d=!1,c=((()=>{if("object"==typeof e){if("string"!=typeof e.delimiter||o.BAD_DELIMITERS.filter(function(t){return -1!==e.delimiter.indexOf(t)}).length||(n=e.delimiter),("boolean"==typeof e.quotes||"function"==typeof e.quotes||Array.isArray(e.quotes))&&(i=e.quotes),"boolean"!=typeof e.skipEmptyLines&&"string"!=typeof e.skipEmptyLines||(h=e.skipEmptyLines),"string"==typeof e.newline&&(s=e.newline),"string"==typeof e.quoteChar&&(a=e.quoteChar),"boolean"==typeof e.header&&(r=e.header),Array.isArray(e.columns)){if(0===e.columns.length)throw Error("Option columns is empty");u=e.columns}void 0!==e.escapeChar&&(l=e.escapeChar+a),e.escapeFormulae instanceof RegExp?d=e.escapeFormulae:"boolean"==typeof e.escapeFormulae&&e.escapeFormulae&&(d=/^[=+\-@\t\r].*$/)}})(),RegExp(p(a),"g"));if("string"==typeof t&&(t=JSON.parse(t)),Array.isArray(t)){if(!t.length||Array.isArray(t[0]))return f(null,t,h);if("object"==typeof t[0])return f(u||Object.keys(t[0]),t,h)}else if("object"==typeof t)return"string"==typeof t.data&&(t.data=JSON.parse(t.data)),Array.isArray(t.data)&&(t.fields||(t.fields=t.meta&&t.meta.fields||u),t.fields||(t.fields=Array.isArray(t.data[0])?t.fields:"object"==typeof t.data[0]?Object.keys(t.data[0]):[]),Array.isArray(t.data[0])||"object"==typeof t.data[0]||(t.data=[t.data])),f(t.fields||[],t.data||[],h);throw Error("Unable to serialize unrecognized input");function f(t,e,i){var a="",o=("string"==typeof t&&(t=JSON.parse(t)),"string"==typeof e&&(e=JSON.parse(e)),Array.isArray(t)&&0<t.length),l=!Array.isArray(e[0]);if(o&&r){for(var h=0;h<t.length;h++)0<h&&(a+=n),a+=m(t[h],h);0<e.length&&(a+=s)}for(var u=0;u<e.length;u++){var d=(o?t:e[u]).length,c=!1,f=o?0===Object.keys(e[u]).length:0===e[u].length;if(i&&!o&&(c="greedy"===i?""===e[u].join("").trim():1===e[u].length&&0===e[u][0].length),"greedy"===i&&o){for(var p=[],g=0;g<d;g++){var y=l?t[g]:g;p.push(e[u][y])}c=""===p.join("").trim()}if(!c){for(var v=0;v<d;v++){0<v&&!f&&(a+=n);var b=o&&l?t[v]:v;a+=m(e[u][b],v)}u<e.length-1&&(!i||0<d&&!f)&&(a+=s)}}return a}function m(t,e){var r,s;return null==t?"":t.constructor===Date?JSON.stringify(t).slice(1,25):(s=!1,d&&"string"==typeof t&&d.test(t)&&(t="'"+t,s=!0),r=t.toString().replace(c,l),(s=s||!0===i||"function"==typeof i&&i(t,e)||Array.isArray(i)&&i[e]||((t,e)=>{for(var i=0;i<e.length;i++)if(-1<t.indexOf(e[i]))return!0;return!1})(r,o.BAD_DELIMITERS)||-1<r.indexOf(n)||" "===r.charAt(0)||" "===r.charAt(r.length-1))?a+r+a:r)}},o.RECORD_SEP="\x1e",o.UNIT_SEP="\x1f",o.BYTE_ORDER_MARK="\uFEFF",o.BAD_DELIMITERS=["\r","\n",'"',o.BYTE_ORDER_MARK],o.WORKERS_SUPPORTED=!r&&!!i.Worker,o.NODE_STREAM_INPUT=1,o.LocalChunkSize=0xa00000,o.RemoteChunkSize=5242880,o.DefaultDelimiter=",",o.Parser=m,o.ParserHandle=f,o.NetworkStreamer=h,o.FileStreamer=u,o.StringStreamer=d,o.ReadableStreamStreamer=c,i.jQuery&&((e=i.jQuery).fn.parse=function(t){var r=t.config||{},n=[];return this.each(function(t){if(!("INPUT"===e(this).prop("tagName").toUpperCase()&&"file"===e(this).attr("type").toLowerCase()&&i.FileReader)||!this.files||0===this.files.length)return!0;for(var s=0;s<this.files.length;s++)n.push({file:this.files[s],inputElem:this,instanceConfig:e.extend({},r)})}),s(),this;function s(){if(0===n.length)w(t.complete)&&t.complete();else{var i,r,s,l,h=n[0];if(w(t.before)){var u=t.before(h.file,h.inputElem);if("object"==typeof u){if("abort"===u.action)return i="AbortError",r=h.file,s=h.inputElem,l=u.reason,void(w(t.error)&&t.error({name:i},r,s,l));if("skip"===u.action)return void a();"object"==typeof u.config&&(h.instanceConfig=e.extend(h.instanceConfig,u.config))}else if("skip"===u)return void a()}var d=h.instanceConfig.complete;h.instanceConfig.complete=function(t){w(d)&&d(t,h.file,h.inputElem),a()},o.parse(h.file,h.instanceConfig)}}function a(){n.splice(0,1),s()}}),n&&(i.onmessage=function(t){t=t.data,void 0===o.WORKER_ID&&t&&(o.WORKER_ID=t.workerId),"string"==typeof t.input?i.postMessage({workerId:o.WORKER_ID,results:o.parse(t.input,t.config),finished:!0}):(i.File&&t.input instanceof File||t.input instanceof Object)&&(t=o.parse(t.input,t.config))&&i.postMessage({workerId:o.WORKER_ID,results:t,finished:!0})}),(h.prototype=Object.create(l.prototype)).constructor=h,(u.prototype=Object.create(l.prototype)).constructor=u,(d.prototype=Object.create(d.prototype)).constructor=d,(c.prototype=Object.create(l.prototype)).constructor=c,o})?i.apply(e,[]):i)||(t.exports=r)},60704:(t,e,i)=>{"use strict";i.d(e,{B8:()=>P,UC:()=>M,bL:()=>C,l9:()=>R});var r=i(12115),n=i(85185),s=i(46081),a=i(89196),o=i(28905),l=i(63655),h=i(94315),u=i(5845),d=i(61285),c=i(95155),f="Tabs",[p,m]=(0,s.A)(f,[a.RG]),g=(0,a.RG)(),[y,v]=p(f),b=r.forwardRef((t,e)=>{let{__scopeTabs:i,value:r,onValueChange:n,defaultValue:s,orientation:a="horizontal",dir:o,activationMode:p="automatic",...m}=t,g=(0,h.jH)(o),[v,b]=(0,u.i)({prop:r,onChange:n,defaultProp:null!=s?s:"",caller:f});return(0,c.jsx)(y,{scope:i,baseId:(0,d.B)(),value:v,onValueChange:b,orientation:a,dir:g,activationMode:p,children:(0,c.jsx)(l.sG.div,{dir:g,"data-orientation":a,...m,ref:e})})});b.displayName=f;var _="TabsList",w=r.forwardRef((t,e)=>{let{__scopeTabs:i,loop:r=!0,...n}=t,s=v(_,i),o=g(i);return(0,c.jsx)(a.bL,{asChild:!0,...o,orientation:s.orientation,dir:s.dir,loop:r,children:(0,c.jsx)(l.sG.div,{role:"tablist","aria-orientation":s.orientation,...n,ref:e})})});w.displayName=_;var x="TabsTrigger",k=r.forwardRef((t,e)=>{let{__scopeTabs:i,value:r,disabled:s=!1,...o}=t,h=v(x,i),u=g(i),d=E(h.baseId,r),f=T(h.baseId,r),p=r===h.value;return(0,c.jsx)(a.q7,{asChild:!0,...u,focusable:!s,active:p,children:(0,c.jsx)(l.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":f,"data-state":p?"active":"inactive","data-disabled":s?"":void 0,disabled:s,id:d,...o,ref:e,onMouseDown:(0,n.m)(t.onMouseDown,t=>{s||0!==t.button||!1!==t.ctrlKey?t.preventDefault():h.onValueChange(r)}),onKeyDown:(0,n.m)(t.onKeyDown,t=>{[" ","Enter"].includes(t.key)&&h.onValueChange(r)}),onFocus:(0,n.m)(t.onFocus,()=>{let t="manual"!==h.activationMode;p||s||!t||h.onValueChange(r)})})})});k.displayName=x;var S="TabsContent",A=r.forwardRef((t,e)=>{let{__scopeTabs:i,value:n,forceMount:s,children:a,...h}=t,u=v(S,i),d=E(u.baseId,n),f=T(u.baseId,n),p=n===u.value,m=r.useRef(p);return r.useEffect(()=>{let t=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(t)},[]),(0,c.jsx)(o.C,{present:s||p,children:i=>{let{present:r}=i;return(0,c.jsx)(l.sG.div,{"data-state":p?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":d,hidden:!r,id:f,tabIndex:0,...h,ref:e,style:{...t.style,animationDuration:m.current?"0s":void 0},children:r&&a})}})});function E(t,e){return"".concat(t,"-trigger-").concat(e)}function T(t,e){return"".concat(t,"-content-").concat(e)}A.displayName=S;var C=b,P=w,R=k,M=A},60760:(t,e,i)=>{"use strict";i.d(e,{N:()=>v});var r=i(95155),n=i(12115),s=i(90869),a=i(82885),o=i(97494),l=i(80845),h=i(27351),u=i(51508);class d extends n.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,i=(0,h.s)(t)&&t.offsetWidth||0,r=this.props.sizeRef.current;r.height=e.offsetHeight||0,r.width=e.offsetWidth||0,r.top=e.offsetTop,r.left=e.offsetLeft,r.right=i-r.width-r.left}return null}componentDidUpdate(){}render(){return this.props.children}}function c(t){let{children:e,isPresent:i,anchorX:s}=t,a=(0,n.useId)(),o=(0,n.useRef)(null),l=(0,n.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:h}=(0,n.useContext)(u.Q);return(0,n.useInsertionEffect)(()=>{let{width:t,height:e,top:r,left:n,right:u}=l.current;if(i||!o.current||!t||!e)return;o.current.dataset.motionPopId=a;let d=document.createElement("style");return h&&(d.nonce=h),document.head.appendChild(d),d.sheet&&d.sheet.insertRule('\n          [data-motion-pop-id="'.concat(a,'"] {\n            position: absolute !important;\n            width: ').concat(t,"px !important;\n            height: ").concat(e,"px !important;\n            ").concat("left"===s?"left: ".concat(n):"right: ".concat(u),"px !important;\n            top: ").concat(r,"px !important;\n          }\n        ")),()=>{document.head.contains(d)&&document.head.removeChild(d)}},[i]),(0,r.jsx)(d,{isPresent:i,childRef:o,sizeRef:l,children:n.cloneElement(e,{ref:o})})}let f=t=>{let{children:e,initial:i,isPresent:s,onExitComplete:o,custom:h,presenceAffectsLayout:u,mode:d,anchorX:f}=t,m=(0,a.M)(p),g=(0,n.useId)(),y=!0,v=(0,n.useMemo)(()=>(y=!1,{id:g,initial:i,isPresent:s,custom:h,onExitComplete:t=>{for(let e of(m.set(t,!0),m.values()))if(!e)return;o&&o()},register:t=>(m.set(t,!1),()=>m.delete(t))}),[s,m,o]);return u&&y&&(v={...v}),(0,n.useMemo)(()=>{m.forEach((t,e)=>m.set(e,!1))},[s]),n.useEffect(()=>{s||m.size||!o||o()},[s]),"popLayout"===d&&(e=(0,r.jsx)(c,{isPresent:s,anchorX:f,children:e})),(0,r.jsx)(l.t.Provider,{value:v,children:e})};function p(){return new Map}var m=i(32082);let g=t=>t.key||"";function y(t){let e=[];return n.Children.forEach(t,t=>{(0,n.isValidElement)(t)&&e.push(t)}),e}let v=t=>{let{children:e,custom:i,initial:l=!0,onExitComplete:h,presenceAffectsLayout:u=!0,mode:d="sync",propagate:c=!1,anchorX:p="left"}=t,[v,b]=(0,m.xQ)(c),_=(0,n.useMemo)(()=>y(e),[e]),w=c&&!v?[]:_.map(g),x=(0,n.useRef)(!0),k=(0,n.useRef)(_),S=(0,a.M)(()=>new Map),[A,E]=(0,n.useState)(_),[T,C]=(0,n.useState)(_);(0,o.E)(()=>{x.current=!1,k.current=_;for(let t=0;t<T.length;t++){let e=g(T[t]);w.includes(e)?S.delete(e):!0!==S.get(e)&&S.set(e,!1)}},[T,w.length,w.join("-")]);let P=[];if(_!==A){let t=[..._];for(let e=0;e<T.length;e++){let i=T[e],r=g(i);w.includes(r)||(t.splice(e,0,i),P.push(i))}return"wait"===d&&P.length&&(t=P),C(y(t)),E(_),null}let{forceRender:R}=(0,n.useContext)(s.L);return(0,r.jsx)(r.Fragment,{children:T.map(t=>{let e=g(t),n=(!c||!!v)&&(_===T||w.includes(e));return(0,r.jsx)(f,{isPresent:n,initial:(!x.current||!!l)&&void 0,custom:i,presenceAffectsLayout:u,mode:d,onExitComplete:n?void 0:()=>{if(!S.has(e))return;S.set(e,!0);let t=!0;S.forEach(e=>{e||(t=!1)}),t&&(null==R||R(),C(k.current),c&&(null==b||b()),h&&h())},anchorX:p,children:t},e)})})}},62332:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("FileCode",[["path",{d:"M10 12.5 8 15l2 2.5",key:"1tg20x"}],["path",{d:"m14 12.5 2 2.5-2 2.5",key:"yinavb"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7z",key:"1mlx9k"}]])},64261:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]])},66538:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("CircleDashed",[["path",{d:"M10.1 2.182a10 10 0 0 1 3.8 0",key:"5ilxe3"}],["path",{d:"M13.9 21.818a10 10 0 0 1-3.8 0",key:"11zvb9"}],["path",{d:"M17.609 3.721a10 10 0 0 1 2.69 2.7",key:"1iw5b2"}],["path",{d:"M2.182 13.9a10 10 0 0 1 0-3.8",key:"c0bmvh"}],["path",{d:"M20.279 17.609a10 10 0 0 1-2.7 2.69",key:"1ruxm7"}],["path",{d:"M21.818 10.1a10 10 0 0 1 0 3.8",key:"qkgqxc"}],["path",{d:"M3.721 6.391a10 10 0 0 1 2.7-2.69",key:"1mcia2"}],["path",{d:"M6.391 20.279a10 10 0 0 1-2.69-2.7",key:"1fvljs"}]])},68972:(t,e,i)=>{"use strict";i.d(e,{B:()=>r});let r="undefined"!=typeof window},69074:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},71539:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},73032:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("FileVideo",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m10 11 5 3-5 3v-6Z",key:"7ntvm4"}]])},76408:(t,e,i)=>{"use strict";let r;function n(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function s(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function a(t,e,i,r){if("function"==typeof e){let[n,a]=s(r);e=e(void 0!==i?i:t.custom,n,a)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,a]=s(r);e=e(void 0!==i?i:t.custom,n,a)}return e}function o(t,e,i){let r=t.getProps();return a(r,e,void 0!==i?i:r.custom,t)}function l(t,e){return t?.[e]??t?.default??t}i.d(e,{P:()=>sT});let h=t=>t,u={},d=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],c={value:null,addProjectionMetrics:null};function f(t,e){let i=!1,r=!0,n={delta:0,timestamp:0,isProcessing:!1},s=()=>i=!0,a=d.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,r=new Set,n=!1,s=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function h(e){a.has(e)&&(u.schedule(e),t()),l++,e(o)}let u={schedule:(t,e=!1,s=!1)=>{let o=s&&n?i:r;return e&&a.add(t),o.has(t)||o.add(t),t},cancel:t=>{r.delete(t),a.delete(t)},process:t=>{if(o=t,n){s=!0;return}n=!0,[i,r]=[r,i],i.forEach(h),e&&c.value&&c.value.frameloop[e].push(l),l=0,i.clear(),n=!1,s&&(s=!1,u.process(t))}};return u}(s,e?i:void 0),t),{}),{setup:o,read:l,resolveKeyframes:h,preUpdate:f,update:p,preRender:m,render:g,postRender:y}=a,v=()=>{let s=u.useManualTiming?n.timestamp:performance.now();i=!1,u.useManualTiming||(n.delta=r?1e3/60:Math.max(Math.min(s-n.timestamp,40),1)),n.timestamp=s,n.isProcessing=!0,o.process(n),l.process(n),h.process(n),f.process(n),p.process(n),m.process(n),g.process(n),y.process(n),n.isProcessing=!1,i&&e&&(r=!1,t(v))},b=()=>{i=!0,r=!0,n.isProcessing||t(v)};return{schedule:d.reduce((t,e)=>{let r=a[e];return t[e]=(t,e=!1,n=!1)=>(i||b(),r.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<d.length;e++)a[d[e]].cancel(t)},state:n,steps:a}}let{schedule:p,cancel:m,state:g,steps:y}=f("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:h,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],b=new Set(v),_=new Set(["width","height","top","left","right","bottom",...v]);function w(t,e){-1===t.indexOf(e)&&t.push(e)}function x(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class k{constructor(){this.subscriptions=[]}add(t){return w(this.subscriptions,t),()=>x(this.subscriptions,t)}notify(t,e,i){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](t,e,i);else for(let n=0;n<r;n++){let r=this.subscriptions[n];r&&r(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function S(){r=void 0}let A={now:()=>(void 0===r&&A.set(g.isProcessing||u.useManualTiming?g.timestamp:performance.now()),r),set:t=>{r=t,queueMicrotask(S)}},E=t=>!isNaN(parseFloat(t)),T={current:void 0};class C{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=A.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=A.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=E(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new k);let i=this.events[t].add(e);return"change"===t?()=>{i(),p.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return T.current&&T.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=A.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function P(t,e){return new C(t,e)}let R=t=>Array.isArray(t),M=t=>!!(t&&t.getVelocity);function D(t,e){let i=t.getValue("willChange");if(M(i)&&i.add)return i.add(e);if(!i&&u.WillChange){let i=new u.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let z=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),O="data-"+z("framerAppearId"),I=(t,e)=>i=>e(t(i)),L=(...t)=>t.reduce(I),F=(t,e,i)=>i>e?e:i<t?t:i,j=t=>1e3*t,B=t=>t/1e3,V={layout:0,mainThread:0,waapi:0},U=()=>{},N=()=>{},W=t=>e=>"string"==typeof e&&e.startsWith(t),H=W("--"),Z=W("var(--"),q=t=>!!Z(t)&&Y.test(t.split("/*")[0].trim()),Y=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,$={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},X={...$,transform:t=>F(0,1,t)},K={...$,default:1},G=t=>Math.round(1e5*t)/1e5,Q=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,J=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tt=(t,e)=>i=>!!("string"==typeof i&&J.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),te=(t,e,i)=>r=>{if("string"!=typeof r)return r;let[n,s,a,o]=r.match(Q);return{[t]:parseFloat(n),[e]:parseFloat(s),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},ti=t=>F(0,255,t),tr={...$,transform:t=>Math.round(ti(t))},tn={test:tt("rgb","red"),parse:te("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:r=1})=>"rgba("+tr.transform(t)+", "+tr.transform(e)+", "+tr.transform(i)+", "+G(X.transform(r))+")"},ts={test:tt("#"),parse:function(t){let e="",i="",r="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),r=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),r=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,r+=r,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(r,16),alpha:n?parseInt(n,16)/255:1}},transform:tn.transform},ta=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),to=ta("deg"),tl=ta("%"),th=ta("px"),tu=ta("vh"),td=ta("vw"),tc={...tl,parse:t=>tl.parse(t)/100,transform:t=>tl.transform(100*t)},tf={test:tt("hsl","hue"),parse:te("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:r=1})=>"hsla("+Math.round(t)+", "+tl.transform(G(e))+", "+tl.transform(G(i))+", "+G(X.transform(r))+")"},tp={test:t=>tn.test(t)||ts.test(t)||tf.test(t),parse:t=>tn.test(t)?tn.parse(t):tf.test(t)?tf.parse(t):ts.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tn.transform(t):tf.transform(t)},tm=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tg="number",ty="color",tv=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tb(t){let e=t.toString(),i=[],r={color:[],number:[],var:[]},n=[],s=0,a=e.replace(tv,t=>(tp.test(t)?(r.color.push(s),n.push(ty),i.push(tp.parse(t))):t.startsWith("var(")?(r.var.push(s),n.push("var"),i.push(t)):(r.number.push(s),n.push(tg),i.push(parseFloat(t))),++s,"${}")).split("${}");return{values:i,split:a,indexes:r,types:n}}function t_(t){return tb(t).values}function tw(t){let{split:e,types:i}=tb(t),r=e.length;return t=>{let n="";for(let s=0;s<r;s++)if(n+=e[s],void 0!==t[s]){let e=i[s];e===tg?n+=G(t[s]):e===ty?n+=tp.transform(t[s]):n+=t[s]}return n}}let tx=t=>"number"==typeof t?0:t,tk={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(Q)?.length||0)+(t.match(tm)?.length||0)>0},parse:t_,createTransformer:tw,getAnimatableNone:function(t){let e=t_(t);return tw(t)(e.map(tx))}};function tS(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tA(t,e){return i=>i>0?e:t}let tE=(t,e,i)=>t+(e-t)*i,tT=(t,e,i)=>{let r=t*t,n=i*(e*e-r)+r;return n<0?0:Math.sqrt(n)},tC=[ts,tn,tf],tP=t=>tC.find(e=>e.test(t));function tR(t){let e=tP(t);if(U(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tf&&(i=function({hue:t,saturation:e,lightness:i,alpha:r}){t/=360,i/=100;let n=0,s=0,a=0;if(e/=100){let r=i<.5?i*(1+e):i+e-i*e,o=2*i-r;n=tS(o,r,t+1/3),s=tS(o,r,t),a=tS(o,r,t-1/3)}else n=s=a=i;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*a),alpha:r}}(i)),i}let tM=(t,e)=>{let i=tR(t),r=tR(e);if(!i||!r)return tA(t,e);let n={...i};return t=>(n.red=tT(i.red,r.red,t),n.green=tT(i.green,r.green,t),n.blue=tT(i.blue,r.blue,t),n.alpha=tE(i.alpha,r.alpha,t),tn.transform(n))},tD=new Set(["none","hidden"]);function tz(t,e){return i=>tE(t,e,i)}function tO(t){return"number"==typeof t?tz:"string"==typeof t?q(t)?tA:tp.test(t)?tM:tF:Array.isArray(t)?tI:"object"==typeof t?tp.test(t)?tM:tL:tA}function tI(t,e){let i=[...t],r=i.length,n=t.map((t,i)=>tO(t)(t,e[i]));return t=>{for(let e=0;e<r;e++)i[e]=n[e](t);return i}}function tL(t,e){let i={...t,...e},r={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(r[n]=tO(t[n])(t[n],e[n]));return t=>{for(let e in r)i[e]=r[e](t);return i}}let tF=(t,e)=>{let i=tk.createTransformer(e),r=tb(t),n=tb(e);return r.indexes.var.length===n.indexes.var.length&&r.indexes.color.length===n.indexes.color.length&&r.indexes.number.length>=n.indexes.number.length?tD.has(t)&&!n.values.length||tD.has(e)&&!r.values.length?function(t,e){return tD.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):L(tI(function(t,e){let i=[],r={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let s=e.types[n],a=t.indexes[s][r[s]],o=t.values[a]??0;i[n]=o,r[s]++}return i}(r,n),n.values),i):(U(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tA(t,e))};function tj(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tE(t,e,i):tO(t)(t,e)}let tB=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>p.update(e,t),stop:()=>m(e),now:()=>g.isProcessing?g.timestamp:A.now()}},tV=(t,e,i=10)=>{let r="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)r+=t(e/(n-1))+", ";return`linear(${r.substring(0,r.length-2)})`};function tU(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function tN(t,e,i){var r,n;let s=Math.max(e-5,0);return r=i-t(s),(n=e-s)?1e3/n*r:0}let tW={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tH(t,e){return t*Math.sqrt(1-e*e)}let tZ=["duration","bounce"],tq=["stiffness","damping","mass"];function tY(t,e){return e.some(e=>void 0!==t[e])}function t$(t=tW.visualDuration,e=tW.bounce){let i,r="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:s}=r,a=r.keyframes[0],o=r.keyframes[r.keyframes.length-1],l={done:!1,value:a},{stiffness:h,damping:u,mass:d,duration:c,velocity:f,isResolvedFromDuration:p}=function(t){let e={velocity:tW.velocity,stiffness:tW.stiffness,damping:tW.damping,mass:tW.mass,isResolvedFromDuration:!1,...t};if(!tY(t,tq)&&tY(t,tZ))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),r=i*i,n=2*F(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:tW.mass,stiffness:r,damping:n}}else{let i=function({duration:t=tW.duration,bounce:e=tW.bounce,velocity:i=tW.velocity,mass:r=tW.mass}){let n,s;U(t<=j(tW.maxDuration),"Spring duration must be 10 seconds or less");let a=1-e;a=F(tW.minDamping,tW.maxDamping,a),t=F(tW.minDuration,tW.maxDuration,B(t)),a<1?(n=e=>{let r=e*a,n=r*t;return .001-(r-i)/tH(e,a)*Math.exp(-n)},s=e=>{let r=e*a*t,s=Math.pow(a,2)*Math.pow(e,2)*t,o=Math.exp(-r),l=tH(Math.pow(e,2),a);return(r*i+i-s)*o*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),s=e=>t*t*(i-e)*Math.exp(-e*t));let o=function(t,e,i){let r=i;for(let i=1;i<12;i++)r-=t(r)/e(r);return r}(n,s,5/t);if(t=j(t),isNaN(o))return{stiffness:tW.stiffness,damping:tW.damping,duration:t};{let e=Math.pow(o,2)*r;return{stiffness:e,damping:2*a*Math.sqrt(r*e),duration:t}}}(t);(e={...e,...i,mass:tW.mass}).isResolvedFromDuration=!0}return e}({...r,velocity:-B(r.velocity||0)}),m=f||0,g=u/(2*Math.sqrt(h*d)),y=o-a,v=B(Math.sqrt(h/d)),b=5>Math.abs(y);if(n||(n=b?tW.restSpeed.granular:tW.restSpeed.default),s||(s=b?tW.restDelta.granular:tW.restDelta.default),g<1){let t=tH(v,g);i=e=>o-Math.exp(-g*v*e)*((m+g*v*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>o-Math.exp(-v*t)*(y+(m+v*y)*t);else{let t=v*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*v*e),r=Math.min(t*e,300);return o-i*((m+g*v*y)*Math.sinh(r)+t*y*Math.cosh(r))/t}}let _={calculatedDuration:p&&c||null,next:t=>{let e=i(t);if(p)l.done=t>=c;else{let r=0===t?m:0;g<1&&(r=0===t?j(m):tN(i,t,e));let a=Math.abs(o-e)<=s;l.done=Math.abs(r)<=n&&a}return l.value=l.done?o:e,l},toString:()=>{let t=Math.min(tU(_),2e4),e=tV(e=>_.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return _}function tX({keyframes:t,velocity:e=0,power:i=.8,timeConstant:r=325,bounceDamping:n=10,bounceStiffness:s=500,modifyTarget:a,min:o,max:l,restDelta:h=.5,restSpeed:u}){let d,c,f=t[0],p={done:!1,value:f},m=t=>void 0!==o&&t<o||void 0!==l&&t>l,g=t=>void 0===o?l:void 0===l||Math.abs(o-t)<Math.abs(l-t)?o:l,y=i*e,v=f+y,b=void 0===a?v:a(v);b!==v&&(y=b-f);let _=t=>-y*Math.exp(-t/r),w=t=>b+_(t),x=t=>{let e=_(t),i=w(t);p.done=Math.abs(e)<=h,p.value=p.done?b:i},k=t=>{m(p.value)&&(d=t,c=t$({keyframes:[p.value,g(p.value)],velocity:tN(w,t,p.value),damping:n,stiffness:s,restDelta:h,restSpeed:u}))};return k(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,x(t),k(t)),void 0!==d&&t>=d)?c.next(t-d):(e||x(t),p)}}}t$.applyToOptions=t=>{let e=function(t,e=100,i){let r=i({...t,keyframes:[0,e]}),n=Math.min(tU(r),2e4);return{type:"keyframes",ease:t=>r.next(n*t).value/e,duration:B(n)}}(t,100,t$);return t.ease=e.ease,t.duration=j(e.duration),t.type="keyframes",t};let tK=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tG(t,e,i,r){if(t===e&&i===r)return h;let n=e=>(function(t,e,i,r,n){let s,a,o=0;do(s=tK(a=e+(i-e)/2,r,n)-t)>0?i=a:e=a;while(Math.abs(s)>1e-7&&++o<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:tK(n(t),e,r)}let tQ=tG(.42,0,1,1),tJ=tG(0,0,.58,1),t0=tG(.42,0,.58,1),t1=t=>Array.isArray(t)&&"number"!=typeof t[0],t2=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t5=t=>e=>1-t(1-e),t3=tG(.33,1.53,.69,.99),t6=t5(t3),t4=t2(t6),t8=t=>(t*=2)<1?.5*t6(t):.5*(2-Math.pow(2,-10*(t-1))),t9=t=>1-Math.sin(Math.acos(t)),t7=t5(t9),et=t2(t9),ee=t=>Array.isArray(t)&&"number"==typeof t[0],ei={linear:h,easeIn:tQ,easeInOut:t0,easeOut:tJ,circIn:t9,circInOut:et,circOut:t7,backIn:t6,backInOut:t4,backOut:t3,anticipate:t8},er=t=>"string"==typeof t,en=t=>{if(ee(t)){N(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,r,n]=t;return tG(e,i,r,n)}return er(t)?(N(void 0!==ei[t],`Invalid easing type '${t}'`),ei[t]):t},es=(t,e,i)=>{let r=e-t;return 0===r?1:(i-t)/r};function ea({duration:t=300,keyframes:e,times:i,ease:r="easeInOut"}){var n;let s=t1(r)?r.map(en):en(r),a={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:r,mixer:n}={}){let s=t.length;if(N(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];if(2===s&&e[0]===e[1])return()=>e[1];let a=t[0]===t[1];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let o=function(t,e,i){let r=[],n=i||u.mix||tj,s=t.length-1;for(let i=0;i<s;i++){let s=n(t[i],t[i+1]);e&&(s=L(Array.isArray(e)?e[i]||h:e,s)),r.push(s)}return r}(e,r,n),l=o.length,d=i=>{if(a&&i<t[0])return e[0];let r=0;if(l>1)for(;r<t.length-2&&!(i<t[r+1]);r++);let n=es(t[r],t[r+1],i);return o[r](n)};return i?e=>d(F(t[0],t[s-1],e)):d}((n=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let r=1;r<=e;r++){let n=es(0,e,r);t.push(tE(i,1,n))}}(e,t.length-1),e}(e),n.map(e=>e*t)),e,{ease:Array.isArray(s)?s:e.map(()=>s||t0).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(a.value=o(e),a.done=e>=t,a)}}let eo=t=>null!==t;function el(t,{repeat:e,repeatType:i="loop"},r,n=1){let s=t.filter(eo),a=n<0||e&&"loop"!==i&&e%2==1?0:s.length-1;return a&&void 0!==r?r:s[a]}let eh={decay:tX,inertia:tX,tween:ea,keyframes:ea,spring:t$};function eu(t){"string"==typeof t.type&&(t.type=eh[t.type])}class ed{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let ec=t=>t/100;class ef extends ed{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==A.now()&&this.tick(A.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},V.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;eu(t);let{type:e=ea,repeat:i=0,repeatDelay:r=0,repeatType:n,velocity:s=0}=t,{keyframes:a}=t,o=e||ea;o!==ea&&"number"!=typeof a[0]&&(this.mixKeyframes=L(ec,tj(a[0],a[1])),a=[0,100]);let l=o({...t,keyframes:a});"mirror"===n&&(this.mirroredGenerator=o({...t,keyframes:[...a].reverse(),velocity:-s})),null===l.calculatedDuration&&(l.calculatedDuration=tU(l));let{calculatedDuration:h}=l;this.calculatedDuration=h,this.resolvedDuration=h+r,this.totalDuration=this.resolvedDuration*(i+1)-r,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:r,mixKeyframes:n,mirroredGenerator:s,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:h,repeat:u,repeatType:d,repeatDelay:c,type:f,onUpdate:p,finalKeyframe:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-r/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let g=this.currentTime-l*(this.playbackSpeed>=0?1:-1),y=this.playbackSpeed>=0?g<0:g>r;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=r);let v=this.currentTime,b=i;if(u){let t=Math.min(this.currentTime,r)/a,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,u+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/a)):"mirror"===d&&(b=s)),v=F(0,1,i)*a}let _=y?{done:!1,value:h[0]}:b.next(v);n&&(_.value=n(_.value));let{done:w}=_;y||null===o||(w=this.playbackSpeed>=0?this.currentTime>=r:this.currentTime<=0);let x=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return x&&f!==tX&&(_.value=el(h,this.options,m,this.speed)),p&&p(_.value),x&&this.finish(),_}then(t,e){return this.finished.then(t,e)}get duration(){return B(this.calculatedDuration)}get time(){return B(this.currentTime)}set time(t){t=j(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(A.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=B(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tB,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(A.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,V.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let ep=t=>180*t/Math.PI,em=t=>ey(ep(Math.atan2(t[1],t[0]))),eg={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:em,rotateZ:em,skewX:t=>ep(Math.atan(t[1])),skewY:t=>ep(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},ey=t=>((t%=360)<0&&(t+=360),t),ev=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),eb=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),e_={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ev,scaleY:eb,scale:t=>(ev(t)+eb(t))/2,rotateX:t=>ey(ep(Math.atan2(t[6],t[5]))),rotateY:t=>ey(ep(Math.atan2(-t[2],t[0]))),rotateZ:em,rotate:em,skewX:t=>ep(Math.atan(t[4])),skewY:t=>ep(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function ew(t){return+!!t.includes("scale")}function ex(t,e){let i,r;if(!t||"none"===t)return ew(e);let n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=e_,r=n;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=eg,r=e}if(!r)return ew(e);let s=i[e],a=r[1].split(",").map(eS);return"function"==typeof s?s(a):a[s]}let ek=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return ex(i,e)};function eS(t){return parseFloat(t.trim())}let eA=t=>t===$||t===th,eE=new Set(["x","y","z"]),eT=v.filter(t=>!eE.has(t)),eC={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>ex(e,"x"),y:(t,{transform:e})=>ex(e,"y")};eC.translateX=eC.x,eC.translateY=eC.y;let eP=new Set,eR=!1,eM=!1,eD=!1;function ez(){if(eM){let t=Array.from(eP).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return eT.forEach(i=>{let r=t.getValue(i);void 0!==r&&(e.push([i,r.get()]),r.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eM=!1,eR=!1,eP.forEach(t=>t.complete(eD)),eP.clear()}function eO(){eP.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eM=!0)})}class eI{constructor(t,e,i,r,n,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=r,this.element=n,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(eP.add(this),eR||(eR=!0,p.read(eO),p.resolveKeyframes(ez))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:r}=this;if(null===t[0]){let n=r?.get(),s=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let r=i.readValue(e,s);null!=r&&(t[0]=r)}void 0===t[0]&&(t[0]=s),r&&void 0===n&&r.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eP.delete(this)}cancel(){"scheduled"===this.state&&(eP.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eL=t=>t.startsWith("--");function eF(t){let e;return()=>(void 0===e&&(e=t()),e)}let ej=eF(()=>void 0!==window.ScrollTimeline),eB={},eV=function(t,e){let i=eF(t);return()=>eB[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),eU=([t,e,i,r])=>`cubic-bezier(${t}, ${e}, ${i}, ${r})`,eN={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:eU([0,.65,.55,1]),circOut:eU([.55,0,1,.45]),backIn:eU([.31,.01,.66,-.59]),backOut:eU([.33,1.53,.69,.99])};function eW(t){return"function"==typeof t&&"applyToOptions"in t}class eH extends ed{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:r,pseudoElement:n,allowFlatten:s=!1,finalKeyframe:a,onComplete:o}=t;this.isPseudoElement=!!n,this.allowFlatten=s,this.options=t,N("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return eW(t)&&eV()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:r=0,duration:n=300,repeat:s=0,repeatType:a="loop",ease:o="easeOut",times:l}={},h){let u={[e]:i};l&&(u.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?eV()?tV(e,i):"ease-out":ee(e)?eU(e):Array.isArray(e)?e.map(e=>t(e,i)||eN.easeOut):eN[e]}(o,n);Array.isArray(d)&&(u.easing=d),c.value&&V.waapi++;let f={delay:r,duration:n,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:s+1,direction:"reverse"===a?"alternate":"normal"};h&&(f.pseudoElement=h);let p=t.animate(u,f);return c.value&&p.finished.finally(()=>{V.waapi--}),p}(e,i,r,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let t=el(r,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){eL(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return B(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return B(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=j(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&ej())?(this.animation.timeline=t,h):e(this)}}let eZ={anticipate:t8,backInOut:t4,circInOut:et};class eq extends eH{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eZ&&(t.ease=eZ[t.ease])}(t),eu(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:r,element:n,...s}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let a=new ef({...s,autoplay:!1}),o=j(this.finishedTime??this.time);e.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}let eY=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tk.test(t)||"0"===t)&&!t.startsWith("url("));var e$,eX,eK=i(27351);let eG=new Set(["opacity","clipPath","filter","transform"]),eQ=eF(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eJ extends ed{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:r=0,repeatDelay:n=0,repeatType:s="loop",keyframes:a,name:o,motionValue:l,element:h,...u}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=A.now();let d={autoplay:t,delay:e,type:i,repeat:r,repeatDelay:n,repeatType:s,name:o,motionValue:l,element:h,...u},c=h?.KeyframeResolver||eI;this.keyframeResolver=new c(a,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),o,l,h),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,r){this.keyframeResolver=void 0;let{name:n,type:s,velocity:a,delay:o,isHandoff:l,onUpdate:d}=i;this.resolvedAt=A.now(),!function(t,e,i,r){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],a=eY(n,e),o=eY(s,e);return U(a===o,`You are trying to animate ${e} from "${n}" to "${s}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${s} via the \`style\` property.`),!!a&&!!o&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||eW(i))&&r)}(t,n,s,a)&&((u.instantAnimations||!o)&&d?.(el(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let c={startTime:r?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},f=!l&&function(t){let{motionValue:e,name:i,repeatDelay:r,repeatType:n,damping:s,type:a}=t;if(!(0,eK.s)(e?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return eQ()&&i&&eG.has(i)&&("transform"!==i||!l)&&!o&&!r&&"mirror"!==n&&0!==s&&"inertia"!==a}(c)?new eq({...c,element:c.motionValue.owner.current}):new ef(c);f.finished.then(()=>this.notifyFinished()).catch(h),this.pendingTimeline&&(this.stopTimeline=f.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=f}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eD=!0,eO(),ez(),eD=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let e0=t=>null!==t,e1={type:"spring",stiffness:500,damping:25,restSpeed:10},e2=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e5={type:"keyframes",duration:.8},e3={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e6=(t,{keyframes:e})=>e.length>2?e5:b.has(t)?t.startsWith("scale")?e2(e[1]):e1:e3,e4=(t,e,i,r={},n,s)=>a=>{let o=l(r,t)||{},h=o.delay||r.delay||0,{elapsed:d=0}=r;d-=j(h);let c={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...o,delay:-d,onUpdate:t=>{e.set(t),o.onUpdate&&o.onUpdate(t)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:t,motionValue:e,element:s?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:r,staggerDirection:n,repeat:s,repeatType:a,repeatDelay:o,from:l,elapsed:h,...u}){return!!Object.keys(u).length}(o)&&Object.assign(c,e6(t,c)),c.duration&&(c.duration=j(c.duration)),c.repeatDelay&&(c.repeatDelay=j(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let f=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0===c.delay&&(f=!0)),(u.instantAnimations||u.skipAnimations)&&(f=!0,c.duration=0,c.delay=0),c.allowFlatten=!o.type&&!o.ease,f&&!s&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},r){let n=t.filter(e0),s=e&&"loop"!==i&&e%2==1?0:n.length-1;return n[s]}(c.keyframes,o);if(void 0!==t)return void p.update(()=>{c.onUpdate(t),c.onComplete()})}return o.isSync?new ef(c):new eJ(c)};function e8(t,e,{delay:i=0,transitionOverride:r,type:n}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:a,...h}=e;r&&(s=r);let u=[],d=n&&t.animationState&&t.animationState.getState()[n];for(let e in h){let r=t.getValue(e,t.latestValues[e]??null),n=h[e];if(void 0===n||d&&function({protectedKeys:t,needsAnimating:e},i){let r=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,r}(d,e))continue;let a={delay:i,...l(s||{},e)},o=r.get();if(void 0!==o&&!r.isAnimating&&!Array.isArray(n)&&n===o&&!a.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){let i=t.props[O];if(i){let t=window.MotionHandoffAnimation(i,e,p);null!==t&&(a.startTime=t,c=!0)}}D(t,e),r.start(e4(e,r,n,t.shouldReduceMotion&&_.has(e)?{type:!1}:a,t,c));let f=r.animation;f&&u.push(f)}return a&&Promise.all(u).then(()=>{p.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:r={},...n}=o(t,e)||{};for(let e in n={...n,...i}){var s;let i=R(s=n[e])?s[s.length-1]||0:s;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,P(i))}}(t,a)})}),u}function e9(t,e,i={}){let r=o(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:n=t.getDefaultTransition()||{}}=r||{};i.transitionOverride&&(n=i.transitionOverride);let s=r?()=>Promise.all(e8(t,r,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(r=0)=>{let{delayChildren:s=0,staggerChildren:a,staggerDirection:o}=n;return function(t,e,i=0,r=0,n=1,s){let a=[],o=(t.variantChildren.size-1)*r,l=1===n?(t=0)=>t*r:(t=0)=>o-t*r;return Array.from(t.variantChildren).sort(e7).forEach((t,r)=>{t.notify("AnimationStart",e),a.push(e9(t,e,{...s,delay:i+l(r)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,s+r,a,o,i)}:()=>Promise.resolve(),{when:l}=n;if(!l)return Promise.all([s(),a(i.delay)]);{let[t,e]="beforeChildren"===l?[s,a]:[a,s];return t().then(()=>e())}}function e7(t,e){return t.sortNodePosition(e)}function it(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let r=0;r<i;r++)if(e[r]!==t[r])return!1;return!0}function ie(t){return"string"==typeof t||Array.isArray(t)}let ii=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ir=["initial",...ii],is=ir.length,ia=[...ii].reverse(),io=ii.length;function il(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function ih(){return{animate:il(!0),whileInView:il(),whileHover:il(),whileTap:il(),whileDrag:il(),whileFocus:il(),exit:il()}}class iu{constructor(t){this.isMounted=!1,this.node=t}update(){}}class id extends iu{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let r;if(t.notify("AnimationStart",e),Array.isArray(e))r=Promise.all(e.map(e=>e9(t,e,i)));else if("string"==typeof e)r=e9(t,e,i);else{let n="function"==typeof e?o(t,e,i.custom):e;r=Promise.all(e8(t,n,i))}return r.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=ih(),r=!0,s=e=>(i,r)=>{let n=o(t,r,"exit"===e?t.presenceContext?.custom:void 0);if(n){let{transition:t,transitionEnd:e,...r}=n;i={...i,...r,...e}}return i};function a(a){let{props:l}=t,h=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<is;t++){let r=ir[t],n=e.props[r];(ie(n)||!1===n)&&(i[r]=n)}return i}(t.parent)||{},u=[],d=new Set,c={},f=1/0;for(let e=0;e<io;e++){var p,m;let o=ia[e],g=i[o],y=void 0!==l[o]?l[o]:h[o],v=ie(y),b=o===a?g.isActive:null;!1===b&&(f=e);let _=y===h[o]&&y!==l[o]&&v;if(_&&r&&t.manuallyAnimateOnMount&&(_=!1),g.protectedKeys={...c},!g.isActive&&null===b||!y&&!g.prevProp||n(y)||"boolean"==typeof y)continue;let w=(p=g.prevProp,"string"==typeof(m=y)?m!==p:!!Array.isArray(m)&&!it(m,p)),x=w||o===a&&g.isActive&&!_&&v||e>f&&v,k=!1,S=Array.isArray(y)?y:[y],A=S.reduce(s(o),{});!1===b&&(A={});let{prevResolvedValues:E={}}=g,T={...E,...A},C=e=>{x=!0,d.has(e)&&(k=!0,d.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in T){let e=A[t],i=E[t];if(c.hasOwnProperty(t))continue;let r=!1;(R(e)&&R(i)?it(e,i):e===i)?void 0!==e&&d.has(t)?C(t):g.protectedKeys[t]=!0:null!=e?C(t):d.add(t)}g.prevProp=y,g.prevResolvedValues=A,g.isActive&&(c={...c,...A}),r&&t.blockInitialAnimation&&(x=!1);let P=!(_&&w)||k;x&&P&&u.push(...S.map(t=>({animation:t,options:{type:o}})))}if(d.size){let e={};if("boolean"!=typeof l.initial){let i=o(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}d.forEach(i=>{let r=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=r??null}),u.push({animation:e})}let g=!!u.length;return r&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(g=!1),r=!1,g?e(u):Promise.resolve()}return{animateChanges:a,setActive:function(e,r){if(i[e].isActive===r)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,r)),i[e].isActive=r;let n=a(e);for(let t in i)i[t].protectedKeys={};return n},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=ih(),r=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();n(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let ic=0;class ip extends iu{constructor(){super(...arguments),this.id=ic++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let r=this.node.animationState.setActive("exit",!t);e&&!t&&r.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let im={x:!1,y:!1};function ig(t,e,i,r={passive:!0}){return t.addEventListener(e,i,r),()=>t.removeEventListener(e,i)}let iy=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function iv(t){return{point:{x:t.pageX,y:t.pageY}}}let ib=t=>e=>iy(e)&&t(e,iv(e));function i_(t,e,i,r){return ig(t,e,ib(i),r)}function iw({top:t,left:e,right:i,bottom:r}){return{x:{min:e,max:i},y:{min:t,max:r}}}function ix(t){return t.max-t.min}function ik(t,e,i,r=.5){t.origin=r,t.originPoint=tE(e.min,e.max,t.origin),t.scale=ix(i)/ix(e),t.translate=tE(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iS(t,e,i,r){ik(t.x,e.x,i.x,r?r.originX:void 0),ik(t.y,e.y,i.y,r?r.originY:void 0)}function iA(t,e,i){t.min=i.min+e.min,t.max=t.min+ix(e)}function iE(t,e,i){t.min=e.min-i.min,t.max=t.min+ix(e)}function iT(t,e,i){iE(t.x,e.x,i.x),iE(t.y,e.y,i.y)}let iC=()=>({translate:0,scale:1,origin:0,originPoint:0}),iP=()=>({x:iC(),y:iC()}),iR=()=>({min:0,max:0}),iM=()=>({x:iR(),y:iR()});function iD(t){return[t("x"),t("y")]}function iz(t){return void 0===t||1===t}function iO({scale:t,scaleX:e,scaleY:i}){return!iz(t)||!iz(e)||!iz(i)}function iI(t){return iO(t)||iL(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iL(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iF(t,e,i,r,n){return void 0!==n&&(t=r+n*(t-r)),r+i*(t-r)+e}function ij(t,e=0,i=1,r,n){t.min=iF(t.min,e,i,r,n),t.max=iF(t.max,e,i,r,n)}function iB(t,{x:e,y:i}){ij(t.x,e.translate,e.scale,e.originPoint),ij(t.y,i.translate,i.scale,i.originPoint)}function iV(t,e){t.min=t.min+e,t.max=t.max+e}function iU(t,e,i,r,n=.5){let s=tE(t.min,t.max,n);ij(t,e,i,s,r)}function iN(t,e){iU(t.x,e.x,e.scaleX,e.scale,e.originX),iU(t.y,e.y,e.scaleY,e.scale,e.originY)}function iW(t,e){return iw(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:r.y,right:r.x}}(t.getBoundingClientRect(),e))}let iH=({current:t})=>t?t.ownerDocument.defaultView:null;function iZ(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iq=(t,e)=>Math.abs(t-e);class iY{constructor(t,e,{transformPagePoint:i,contextWindow:r,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=iK(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iq(t.x,e.x)**2+iq(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:r}=t,{timestamp:n}=g;this.history.push({...r,timestamp:n});let{onStart:s,onMove:a}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=i$(e,this.transformPagePoint),p.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:r,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=iK("pointercancel"===t.type?this.lastMoveEventInfo:i$(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),r&&r(t,s)},!iy(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=r||window;let s=i$(iv(t),this.transformPagePoint),{point:a}=s,{timestamp:o}=g;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=e;l&&l(t,iK(s,this.history)),this.removeListeners=L(i_(this.contextWindow,"pointermove",this.handlePointerMove),i_(this.contextWindow,"pointerup",this.handlePointerUp),i_(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),m(this.updatePoint)}}function i$(t,e){return e?{point:e(t.point)}:t}function iX(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iK({point:t},e){return{point:t,delta:iX(t,iG(e)),offset:iX(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,r=null,n=iG(t);for(;i>=0&&(r=t[i],!(n.timestamp-r.timestamp>j(.1)));)i--;if(!r)return{x:0,y:0};let s=B(n.timestamp-r.timestamp);if(0===s)return{x:0,y:0};let a={x:(n.x-r.x)/s,y:(n.y-r.y)/s};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function iG(t){return t[t.length-1]}function iQ(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iJ(t,e){let i=e.min-t.min,r=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,r]=[r,i]),{min:i,max:r}}function i0(t,e,i){return{min:i1(t,e),max:i1(t,i)}}function i1(t,e){return"number"==typeof t?t:t[e]||0}let i2=new WeakMap;class i5{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iM(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new iY(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iv(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:r,onDragStart:n}=this.getProps();if(i&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(im[t])return null;else return im[t]=!0,()=>{im[t]=!1};return im.x||im.y?null:(im.x=im.y=!0,()=>{im.x=im.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iD(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tl.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let r=i.layout.layoutBox[t];r&&(e=ix(r)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&p.postRender(()=>n(t,e)),D(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:r,onDirectionLock:n,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=e;if(r&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(a),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,a),this.updateAxis("y",e.point,a),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iD(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:iH(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:r}=e;this.startAnimation(r);let{onDragEnd:n}=this.getProps();n&&p.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:r}=this.getProps();if(!i||!i3(t,r,this.currentDirection))return;let n=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},r){return void 0!==e&&t<e?t=r?tE(e,t,r.min):Math.max(t,e):void 0!==i&&t>i&&(t=r?tE(i,t,r.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),n.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,r=this.constraints;t&&iZ(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:r,right:n}){return{x:iQ(t.x,i,n),y:iQ(t.y,e,r)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:i0(t,"left","right"),y:i0(t,"top","bottom")}}(e),r!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iD(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iZ(e))return!1;let r=e.current;N(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(t,e,i){let r=iW(t,i),{scroll:n}=e;return n&&(iV(r.x,n.offset.x),iV(r.y,n.offset.y)),r}(r,n.root,this.visualElement.getTransformPagePoint()),a=(t=n.layout.layoutBox,{x:iJ(t.x,s.x),y:iJ(t.y,s.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(a));this.hasMutatedConstraints=!!t,t&&(a=iw(t))}return a}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:r,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(iD(a=>{if(!i3(a,e,this.currentDirection))return;let l=o&&o[a]||{};s&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[a]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(a,h)})).then(a)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return D(this.visualElement,t),i.start(e4(t,i,0,e,this.visualElement,!1))}stopAnimation(){iD(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iD(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iD(e=>{let{drag:i}=this.getProps();if(!i3(e,i,this.currentDirection))return;let{projection:r}=this.visualElement,n=this.getAxisMotionValue(e);if(r&&r.layout){let{min:i,max:s}=r.layout.layoutBox[e];n.set(t[e]-tE(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iZ(e)||!i||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};iD(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();r[t]=function(t,e){let i=.5,r=ix(t),n=ix(e);return n>r?i=es(e.min,e.max-r,t.min):r>n&&(i=es(t.min,t.max-n,e.min)),F(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iD(e=>{if(!i3(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:s}=this.constraints[e];i.set(tE(n,s,r[e]))})}addListeners(){if(!this.visualElement.current)return;i2.set(this.visualElement,this);let t=i_(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iZ(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,r=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),p.read(e);let n=ig(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iD(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),r(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:r=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:a=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:r,dragConstraints:n,dragElastic:s,dragMomentum:a}}}function i3(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class i6 extends iu{constructor(t){super(t),this.removeGroupControls=h,this.removeListeners=h,this.controls=new i5(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||h}unmount(){this.removeGroupControls(),this.removeListeners()}}let i4=t=>(e,i)=>{t&&p.postRender(()=>t(e,i))};class i8 extends iu{constructor(){super(...arguments),this.removePointerDownListener=h}onPointerDown(t){this.session=new iY(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iH(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:r}=this.node.getProps();return{onSessionStart:i4(t),onStart:i4(e),onMove:i,onEnd:(t,e)=>{delete this.session,r&&p.postRender(()=>r(t,e))}}}mount(){this.removePointerDownListener=i_(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var i9=i(95155);let{schedule:i7}=f(queueMicrotask,!1);var rt=i(12115),re=i(32082),ri=i(90869);let rr=(0,rt.createContext)({}),rn={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rs(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let ra={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!th.test(t))return t;else t=parseFloat(t);let i=rs(t,e.target.x),r=rs(t,e.target.y);return`${i}% ${r}%`}},ro={};class rl extends rt.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:r}=this.props,{projection:n}=t;for(let t in ru)ro[t]=ru[t],H(t)&&(ro[t].isCSSVariable=!0);n&&(e.group&&e.group.add(n),i&&i.register&&r&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),rn.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:r,isPresent:n}=this.props,{projection:s}=i;return s&&(s.isPresent=n,r||t.layoutDependency!==e||void 0===e||t.isPresent!==n?s.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?s.promote():s.relegate()||p.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),i7.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:r}=t;r&&(r.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(r),i&&i.deregister&&i.deregister(r))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function rh(t){let[e,i]=(0,re.xQ)(),r=(0,rt.useContext)(ri.L);return(0,i9.jsx)(rl,{...t,layoutGroup:r,switchLayoutGroup:(0,rt.useContext)(rr),isPresent:e,safeToRemove:i})}let ru={borderRadius:{...ra,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ra,borderTopRightRadius:ra,borderBottomLeftRadius:ra,borderBottomRightRadius:ra,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let r=tk.parse(t);if(r.length>5)return t;let n=tk.createTransformer(t),s=+("number"!=typeof r[0]),a=i.x.scale*e.x,o=i.y.scale*e.y;r[0+s]/=a,r[1+s]/=o;let l=tE(a,o,.5);return"number"==typeof r[2+s]&&(r[2+s]/=l),"number"==typeof r[3+s]&&(r[3+s]/=l),n(r)}}};var rd=i(6983);function rc(t){return(0,rd.G)(t)&&"ownerSVGElement"in t}let rf=(t,e)=>t.depth-e.depth;class rp{constructor(){this.children=[],this.isDirty=!1}add(t){w(this.children,t),this.isDirty=!0}remove(t){x(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(rf),this.isDirty=!1,this.children.forEach(t)}}function rm(t){return M(t)?t.get():t}let rg=["TopLeft","TopRight","BottomLeft","BottomRight"],ry=rg.length,rv=t=>"string"==typeof t?parseFloat(t):t,rb=t=>"number"==typeof t||th.test(t);function r_(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let rw=rk(0,.5,t7),rx=rk(.5,.95,h);function rk(t,e,i){return r=>r<t?0:r>e?1:i(es(t,e,r))}function rS(t,e){t.min=e.min,t.max=e.max}function rA(t,e){rS(t.x,e.x),rS(t.y,e.y)}function rE(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function rT(t,e,i,r,n){return t-=e,t=r+1/i*(t-r),void 0!==n&&(t=r+1/n*(t-r)),t}function rC(t,e,[i,r,n],s,a){!function(t,e=0,i=1,r=.5,n,s=t,a=t){if(tl.test(e)&&(e=parseFloat(e),e=tE(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let o=tE(s.min,s.max,r);t===s&&(o-=e),t.min=rT(t.min,e,i,o,n),t.max=rT(t.max,e,i,o,n)}(t,e[i],e[r],e[n],e.scale,s,a)}let rP=["x","scaleX","originX"],rR=["y","scaleY","originY"];function rM(t,e,i,r){rC(t.x,e,rP,i?i.x:void 0,r?r.x:void 0),rC(t.y,e,rR,i?i.y:void 0,r?r.y:void 0)}function rD(t){return 0===t.translate&&1===t.scale}function rz(t){return rD(t.x)&&rD(t.y)}function rO(t,e){return t.min===e.min&&t.max===e.max}function rI(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function rL(t,e){return rI(t.x,e.x)&&rI(t.y,e.y)}function rF(t){return ix(t.x)/ix(t.y)}function rj(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class rB{constructor(){this.members=[]}add(t){w(this.members,t),t.scheduleRender()}remove(t){if(x(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:r}=t.options;!1===r&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rV={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},rU=["","X","Y","Z"],rN={visibility:"hidden"},rW=0;function rH(t,e,i,r){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),r&&(r[t]=0))}function rZ({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:r,resetTransform:n}){return class{constructor(t={},i=e?.()){this.id=rW++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,c.value&&(rV.nodes=rV.calculatedTargetDeltas=rV.calculatedProjections=0),this.nodes.forEach(r$),this.nodes.forEach(r1),this.nodes.forEach(r2),this.nodes.forEach(rX),c.addProjectionMetrics&&c.addProjectionMetrics(rV)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new rp)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new k),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=rc(e)&&!(rc(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:r,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(r||i)&&(this.isLayoutDirty=!0),t){let i,r=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=A.now(),r=({timestamp:n})=>{let s=n-i;s>=250&&(m(r),t(s-e))};return p.setup(r,!0),()=>m(r)}(r,250),rn.hasAnimatedSinceResize&&(rn.hasAnimatedSinceResize=!1,this.nodes.forEach(r0))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||r)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||n.getDefaultTransition()||r9,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=n.getProps(),h=!this.targetLayout||!rL(this.targetLayout,r),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...l(s,"layout"),onPlay:a,onComplete:o};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||r0(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),m(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(r5),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let r=i.props[O];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(r,"transform",p,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(rG);return}this.isUpdating||this.nodes.forEach(rQ),this.isUpdating=!1,this.nodes.forEach(rJ),this.nodes.forEach(rq),this.nodes.forEach(rY),this.clearAllSnapshots();let t=A.now();g.delta=F(0,1e3/60,t-g.timestamp),g.timestamp=t,g.isProcessing=!0,y.update.process(g),y.preRender.process(g),y.render.process(g),g.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,i7.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(rK),this.sharedNodes.forEach(r3)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,p.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){p.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||ix(this.snapshot.measuredBox.x)||ix(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iM(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=r(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!rz(this.projectionDelta),i=this.getTransformTemplate(),r=i?i(this.latestValues,""):void 0,s=r!==this.prevTransformTemplateValue;t&&this.instance&&(e||iI(this.latestValues)||s)&&(n(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),r=this.removeElementScroll(i);return t&&(r=this.removeTransform(r)),ne((e=r).x),ne(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iM();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(nr))){let{scroll:t}=this.root;t&&(iV(e.x,t.offset.x),iV(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iM();if(rA(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let r=this.path[i],{scroll:n,options:s}=r;r!==this.root&&n&&s.layoutScroll&&(n.wasRoot&&rA(e,t),iV(e.x,n.offset.x),iV(e.y,n.offset.y))}return e}applyTransform(t,e=!1){let i=iM();rA(i,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];!e&&r.options.layoutScroll&&r.scroll&&r!==r.root&&iN(i,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),iI(r.latestValues)&&iN(i,r.latestValues)}return iI(this.latestValues)&&iN(i,this.latestValues),i}removeTransform(t){let e=iM();rA(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iI(i.latestValues))continue;iO(i.latestValues)&&i.updateSnapshot();let r=iM();rA(r,i.measurePageBox()),rM(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,r)}return iI(this.latestValues)&&rM(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==g.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:r,layoutId:n}=this.options;if(this.layout&&(r||n)){if(this.resolvedRelativeTargetAt=g.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iM(),this.relativeTargetOrigin=iM(),iT(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),rA(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iM(),this.targetWithTransforms=iM()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,a,o;this.forceRelativeParentToResolveTarget(),s=this.target,a=this.relativeTarget,o=this.relativeParent.target,iA(s.x,a.x,o.x),iA(s.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rA(this.target,this.layout.layoutBox),iB(this.target,this.targetDelta)):rA(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iM(),this.relativeTargetOrigin=iM(),iT(this.relativeTargetOrigin,this.target,t.target),rA(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}c.value&&rV.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||iO(this.parent.latestValues)||iL(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===g.timestamp&&(i=!1),i)return;let{layout:r,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(r||n))return;rA(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,r=!1){let n,s,a=i.length;if(a){e.x=e.y=1;for(let o=0;o<a;o++){s=(n=i[o]).projectionDelta;let{visualElement:a}=n.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(r&&n.options.layoutScroll&&n.scroll&&n!==n.root&&iN(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,iB(t,s)),r&&iI(n.latestValues)&&iN(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iM());let{target:o}=t;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rE(this.prevProjectionDelta.x,this.projectionDelta.x),rE(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iS(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===s&&this.treeScale.y===a&&rj(this.projectionDelta.x,this.prevProjectionDelta.x)&&rj(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),c.value&&rV.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iP(),this.projectionDelta=iP(),this.projectionDeltaWithTransform=iP()}setAnimationOrigin(t,e=!1){let i,r=this.snapshot,n=r?r.latestValues:{},s={...this.latestValues},a=iP();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let o=iM(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,d=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(r8));this.animationProgress=0,this.mixTargetDelta=e=>{let r=e/1e3;if(r6(a.x,t.x,r),r6(a.y,t.y,r),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,c,f,p,m,g;iT(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),f=this.relativeTarget,p=this.relativeTargetOrigin,m=o,g=r,r4(f.x,p.x,m.x,g),r4(f.y,p.y,m.y,g),i&&(h=this.relativeTarget,c=i,rO(h.x,c.x)&&rO(h.y,c.y))&&(this.isProjectionDirty=!1),i||(i=iM()),rA(i,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,i,r,n,s){n?(t.opacity=tE(0,i.opacity??1,rw(r)),t.opacityExit=tE(e.opacity??1,0,rx(r))):s&&(t.opacity=tE(e.opacity??1,i.opacity??1,r));for(let n=0;n<ry;n++){let s=`border${rg[n]}Radius`,a=r_(e,s),o=r_(i,s);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||rb(a)===rb(o)?(t[s]=Math.max(tE(rv(a),rv(o),r),0),(tl.test(o)||tl.test(a))&&(t[s]+="%")):t[s]=o)}(e.rotate||i.rotate)&&(t.rotate=tE(e.rotate||0,i.rotate||0,r))}(s,n,this.latestValues,r,d,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(m(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=p.update(()=>{rn.hasAnimatedSinceResize=!0,V.layout++,this.motionValue||(this.motionValue=P(0)),this.currentAnimation=function(t,e,i){let r=M(t)?t:P(t);return r.start(e4("",r,e,i)),r.animation}(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{V.layout--},onComplete:()=>{V.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:r,latestValues:n}=t;if(e&&i&&r){if(this!==t&&this.layout&&r&&ni(this.options.animationType,this.layout.layoutBox,r.layoutBox)){i=this.target||iM();let e=ix(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let r=ix(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+r}rA(e,i),iN(e,n),iS(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new rB),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let r=this.getStack();r&&r.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let r={};i.z&&rH("z",t,r,this.animationValues);for(let e=0;e<rU.length;e++)rH(`rotate${rU[e]}`,t,r,this.animationValues),rH(`skew${rU[e]}`,t,r,this.animationValues);for(let e in t.render(),r)t.setStaticValue(e,r[e]),this.animationValues&&(this.animationValues[e]=r[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return rN;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=rm(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=rm(t?.pointerEvents)||""),this.hasProjected&&!iI(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let n=r.animationValues||r.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let r="",n=t.x.translate/e.x,s=t.y.translate/e.y,a=i?.z||0;if((n||s||a)&&(r=`translate3d(${n}px, ${s}px, ${a}px) `),(1!==e.x||1!==e.y)&&(r+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:s,skewX:a,skewY:o}=i;t&&(r=`perspective(${t}px) ${r}`),e&&(r+=`rotate(${e}deg) `),n&&(r+=`rotateX(${n}deg) `),s&&(r+=`rotateY(${s}deg) `),a&&(r+=`skewX(${a}deg) `),o&&(r+=`skewY(${o}deg) `)}let o=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==o||1!==l)&&(r+=`scale(${o}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),i&&(e.transform=i(n,e.transform));let{x:s,y:a}=this.projectionDelta;for(let t in e.transformOrigin=`${100*s.origin}% ${100*a.origin}% 0`,r.animationValues?e.opacity=r===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:e.opacity=r===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,ro){if(void 0===n[t])continue;let{correct:i,applyTo:s,isCSSVariable:a}=ro[t],o="none"===e.transform?n[t]:i(n[t],r);if(s){let t=s.length;for(let i=0;i<t;i++)e[s[i]]=o}else a?this.options.visualElement.renderState.vars[t]=o:e[t]=o}return this.options.layoutId&&(e.pointerEvents=r===this?rm(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(rG),this.root.sharedNodes.clear()}}}function rq(t){t.updateLayout()}function rY(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:r}=t.layout,{animationType:n}=t.options,s=e.source!==t.layout.source;"size"===n?iD(t=>{let r=s?e.measuredBox[t]:e.layoutBox[t],n=ix(r);r.min=i[t].min,r.max=r.min+n}):ni(n,e.layoutBox,i)&&iD(r=>{let n=s?e.measuredBox[r]:e.layoutBox[r],a=ix(i[r]);n.max=n.min+a,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[r].max=t.relativeTarget[r].min+a)});let a=iP();iS(a,i,e.layoutBox);let o=iP();s?iS(o,t.applyTransform(r,!0),e.measuredBox):iS(o,i,e.layoutBox);let l=!rz(a),h=!1;if(!t.resumeFrom){let r=t.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:n,layout:s}=r;if(n&&s){let a=iM();iT(a,e.layoutBox,n.layoutBox);let o=iM();iT(o,i,s.layoutBox),rL(a,o)||(h=!0),r.options.layoutRoot&&(t.relativeTarget=o,t.relativeTargetOrigin=a,t.relativeParent=r)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function r$(t){c.value&&rV.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function rX(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function rK(t){t.clearSnapshot()}function rG(t){t.clearMeasurements()}function rQ(t){t.isLayoutDirty=!1}function rJ(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function r0(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function r1(t){t.resolveTargetDelta()}function r2(t){t.calcProjection()}function r5(t){t.resetSkewAndRotation()}function r3(t){t.removeLeadSnapshot()}function r6(t,e,i){t.translate=tE(e.translate,0,i),t.scale=tE(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function r4(t,e,i,r){t.min=tE(e.min,i.min,r),t.max=tE(e.max,i.max,r)}function r8(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let r9={duration:.45,ease:[.4,0,.1,1]},r7=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),nt=r7("applewebkit/")&&!r7("chrome/")?Math.round:h;function ne(t){t.min=nt(t.min),t.max=nt(t.max)}function ni(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(rF(e)-rF(i)))}function nr(t){return t!==t.root&&t.scroll?.wasRoot}let nn=rZ({attachResizeListener:(t,e)=>ig(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ns={current:void 0},na=rZ({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!ns.current){let t=new nn({});t.mount(window),t.setOptions({layoutScroll:!0}),ns.current=t}return ns.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function no(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),r=new AbortController;return[i,{passive:!0,...e,signal:r.signal},()=>r.abort()]}function nl(t){return!("touch"===t.pointerType||im.x||im.y)}function nh(t,e,i){let{props:r}=t;t.animationState&&r.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=r["onHover"+i];n&&p.postRender(()=>n(e,iv(e)))}class nu extends iu{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=no(t,i),a=t=>{if(!nl(t))return;let{target:i}=t,r=e(i,t);if("function"!=typeof r||!i)return;let s=t=>{nl(t)&&(r(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,n)};return r.forEach(t=>{t.addEventListener("pointerenter",a,n)}),s}(t,(t,e)=>(nh(this.node,e,"Start"),t=>nh(this.node,t,"End"))))}unmount(){}}class nd extends iu{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=L(ig(this.node.current,"focus",()=>this.onFocus()),ig(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let nc=(t,e)=>!!e&&(t===e||nc(t,e.parentElement)),nf=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),np=new WeakSet;function nm(t){return e=>{"Enter"===e.key&&t(e)}}function ng(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let ny=(t,e)=>{let i=t.currentTarget;if(!i)return;let r=nm(()=>{if(np.has(i))return;ng(i,"down");let t=nm(()=>{ng(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>ng(i,"cancel"),e)});i.addEventListener("keydown",r,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",r),e)};function nv(t){return iy(t)&&!(im.x||im.y)}function nb(t,e,i){let{props:r}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&r.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=r["onTap"+("End"===i?"":i)];n&&p.postRender(()=>n(e,iv(e)))}class n_ extends iu{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[r,n,s]=no(t,i),a=t=>{let r=t.currentTarget;if(!nv(t))return;np.add(r);let s=e(r,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),np.has(r)&&np.delete(r),nv(t)&&"function"==typeof s&&s(t,{success:e})},o=t=>{a(t,r===window||r===document||i.useGlobalTarget||nc(r,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,n),window.addEventListener("pointercancel",l,n)};return r.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",a,n),(0,eK.s)(t))&&(t.addEventListener("focus",t=>ny(t,n)),nf.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(nb(this.node,e,"Start"),(t,{success:e})=>nb(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let nw=new WeakMap,nx=new WeakMap,nk=t=>{let e=nw.get(t.target);e&&e(t)},nS=t=>{t.forEach(nk)},nA={some:0,all:1};class nE extends iu{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:r="some",once:n}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof r?r:nA[r]};return function(t,e,i){let r=function({root:t,...e}){let i=t||document;nx.has(i)||nx.set(i,{});let r=nx.get(i),n=JSON.stringify(e);return r[n]||(r[n]=new IntersectionObserver(nS,{root:t,...e})),r[n]}(e);return nw.set(t,i),r.observe(t),()=>{nw.delete(t),r.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:r}=this.node.getProps(),s=e?i:r;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let nT=(0,rt.createContext)({strict:!1});var nC=i(51508);let nP=(0,rt.createContext)({});function nR(t){return n(t.animate)||ir.some(e=>ie(t[e]))}function nM(t){return!!(nR(t)||t.variants)}function nD(t){return Array.isArray(t)?t.join(" "):t}var nz=i(68972);let nO={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nI={};for(let t in nO)nI[t]={isEnabled:e=>nO[t].some(t=>!!e[t])};let nL=Symbol.for("motionComponentSymbol");var nF=i(80845),nj=i(97494);function nB(t,{layout:e,layoutId:i}){return b.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!ro[t]||"opacity"===t)}let nV=(t,e)=>e&&"number"==typeof t?e.transform(t):t,nU={...$,transform:Math.round},nN={borderWidth:th,borderTopWidth:th,borderRightWidth:th,borderBottomWidth:th,borderLeftWidth:th,borderRadius:th,radius:th,borderTopLeftRadius:th,borderTopRightRadius:th,borderBottomRightRadius:th,borderBottomLeftRadius:th,width:th,maxWidth:th,height:th,maxHeight:th,top:th,right:th,bottom:th,left:th,padding:th,paddingTop:th,paddingRight:th,paddingBottom:th,paddingLeft:th,margin:th,marginTop:th,marginRight:th,marginBottom:th,marginLeft:th,backgroundPositionX:th,backgroundPositionY:th,rotate:to,rotateX:to,rotateY:to,rotateZ:to,scale:K,scaleX:K,scaleY:K,scaleZ:K,skew:to,skewX:to,skewY:to,distance:th,translateX:th,translateY:th,translateZ:th,x:th,y:th,z:th,perspective:th,transformPerspective:th,opacity:X,originX:tc,originY:tc,originZ:th,zIndex:nU,fillOpacity:X,strokeOpacity:X,numOctaves:nU},nW={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nH=v.length;function nZ(t,e,i){let{style:r,vars:n,transformOrigin:s}=t,a=!1,o=!1;for(let t in e){let i=e[t];if(b.has(t)){a=!0;continue}if(H(t)){n[t]=i;continue}{let e=nV(i,nN[t]);t.startsWith("origin")?(o=!0,s[t]=e):r[t]=e}}if(!e.transform&&(a||i?r.transform=function(t,e,i){let r="",n=!0;for(let s=0;s<nH;s++){let a=v[s],o=t[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let t=nV(o,nN[a]);if(!l){n=!1;let e=nW[a]||a;r+=`${e}(${t}) `}i&&(e[a]=t)}}return r=r.trim(),i?r=i(e,n?"":r):n&&(r="none"),r}(e,t.transform,i):r.transform&&(r.transform="none")),o){let{originX:t="50%",originY:e="50%",originZ:i=0}=s;r.transformOrigin=`${t} ${e} ${i}`}}let nq=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function nY(t,e,i){for(let r in e)M(e[r])||nB(r,i)||(t[r]=e[r])}let n$={offset:"stroke-dashoffset",array:"stroke-dasharray"},nX={offset:"strokeDashoffset",array:"strokeDasharray"};function nK(t,{attrX:e,attrY:i,attrScale:r,pathLength:n,pathSpacing:s=1,pathOffset:a=0,...o},l,h,u){if(nZ(t,o,h),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=u?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==r&&(d.scale=r),void 0!==n&&function(t,e,i=1,r=0,n=!0){t.pathLength=1;let s=n?n$:nX;t[s.offset]=th.transform(-r);let a=th.transform(e),o=th.transform(i);t[s.array]=`${a} ${o}`}(d,n,s,a,!1)}let nG=()=>({...nq(),attrs:{}}),nQ=t=>"string"==typeof t&&"svg"===t.toLowerCase(),nJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function n0(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||nJ.has(t)}let n1=t=>!n0(t);try{!function(t){t&&(n1=e=>e.startsWith("on")?!n0(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let n2=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function n5(t){if("string"!=typeof t||t.includes("-"));else if(n2.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var n3=i(82885);let n6=t=>(e,i)=>{let r=(0,rt.useContext)(nP),s=(0,rt.useContext)(nF.t),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,r,s){return{latestValues:function(t,e,i,r){let s={},o=r(t,{});for(let t in o)s[t]=rm(o[t]);let{initial:l,animate:h}=t,u=nR(t),d=nM(t);e&&d&&!u&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===h&&(h=e.animate));let c=!!i&&!1===i.initial,f=(c=c||!1===l)?h:l;if(f&&"boolean"!=typeof f&&!n(f)){let e=Array.isArray(f)?f:[f];for(let i=0;i<e.length;i++){let r=a(t,e[i]);if(r){let{transitionEnd:t,transition:e,...i}=r;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(s[t]=e)}for(let e in t)s[e]=t[e]}}}return s}(i,r,s,t),renderState:e()}})(t,e,r,s);return i?o():(0,n3.M)(o)};function n4(t,e,i){let{style:r}=t,n={};for(let s in r)(M(r[s])||e.style&&M(e.style[s])||nB(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(n[s]=r[s]);return n}let n8={useVisualState:n6({scrapeMotionValuesFromProps:n4,createRenderState:nq})};function n9(t,e,i){let r=n4(t,e,i);for(let i in t)(M(t[i])||M(e[i]))&&(r[-1!==v.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return r}let n7={useVisualState:n6({scrapeMotionValuesFromProps:n9,createRenderState:nG})},st=t=>e=>e.test(t),se=[$,th,tl,to,td,tu,{test:t=>"auto"===t,parse:t=>t}],si=t=>se.find(st(t)),sr=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),sn=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,ss=t=>/^0[^.\s]+$/u.test(t),sa=new Set(["brightness","contrast","saturate","opacity"]);function so(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[r]=i.match(Q)||[];if(!r)return t;let n=i.replace(r,""),s=+!!sa.has(e);return r!==i&&(s*=100),e+"("+s+n+")"}let sl=/\b([a-z-]*)\(.*?\)/gu,sh={...tk,getAnimatableNone:t=>{let e=t.match(sl);return e?e.map(so).join(" "):t}},su={...nN,color:tp,backgroundColor:tp,outlineColor:tp,fill:tp,stroke:tp,borderColor:tp,borderTopColor:tp,borderRightColor:tp,borderBottomColor:tp,borderLeftColor:tp,filter:sh,WebkitFilter:sh},sd=t=>su[t];function sc(t,e){let i=sd(t);return i!==sh&&(i=tk),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let sf=new Set(["auto","none","0"]);class sp extends eI{constructor(t,e,i,r,n){super(t,e,i,r,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let r=t[i];if("string"==typeof r&&q(r=r.trim())){let n=function t(e,i,r=1){N(r<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,s]=function(t){let e=sn.exec(t);if(!e)return[,];let[,i,r,n]=e;return[`--${i??r}`,n]}(e);if(!n)return;let a=window.getComputedStyle(i).getPropertyValue(n);if(a){let t=a.trim();return sr(t)?parseFloat(t):t}return q(s)?t(s,i,r+1):s}(r,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!_.has(i)||2!==t.length)return;let[r,n]=t,s=si(r),a=si(n);if(s!==a)if(eA(s)&&eA(a))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else eC[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var r;(null===t[e]||("number"==typeof(r=t[e])?0===r:null===r||"none"===r||"0"===r||ss(r)))&&i.push(e)}i.length&&function(t,e,i){let r,n=0;for(;n<t.length&&!r;){let e=t[n];"string"==typeof e&&!sf.has(e)&&tb(e).values.length&&(r=t[n]),n++}if(r&&i)for(let n of e)t[n]=sc(i,r)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eC[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let r=e[e.length-1];void 0!==r&&t.getValue(i,r).jump(r,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let r=t.getValue(e);r&&r.jump(this.measuredOrigin,!1);let n=i.length-1,s=i[n];i[n]=eC[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let sm=[...se,tp,tk],sg=t=>sm.find(st(t)),sy={current:null},sv={current:!1},sb=new WeakMap,s_=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sw{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:r,blockInitialAnimation:n,visualState:s},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eI,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=A.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,p.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=s;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=r,this.options=a,this.blockInitialAnimation=!!n,this.isControllingVariants=nR(e),this.isVariantNode=nM(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...u}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in u){let e=u[t];void 0!==o[t]&&M(e)&&e.set(o[t],!1)}}mount(t){this.current=t,sb.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),sv.current||function(){if(sv.current=!0,nz.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>sy.current=t.matches;t.addListener(e),e()}else sy.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||sy.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),m(this.notifyUpdate),m(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let r=b.has(t);r&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&p.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in nI){let e=nI[t];if(!e)continue;let{isEnabled:i,Feature:r}=e;if(!this.features[t]&&r&&i(this.props)&&(this.features[t]=new r(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iM()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<s_.length;e++){let i=s_[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let r=t["on"+i];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=function(t,e,i){for(let r in e){let n=e[r],s=i[r];if(M(n))t.addValue(r,n);else if(M(s))t.addValue(r,P(n,{owner:t}));else if(s!==n)if(t.hasValue(r)){let e=t.getValue(r);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(r);t.addValue(r,P(void 0!==e?e:n,{owner:t}))}}for(let r in i)void 0===e[r]&&t.removeValue(r);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=P(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(sr(i)||ss(i))?i=parseFloat(i):!sg(i)&&tk.test(e)&&(i=sc(t,e)),this.setBaseTarget(t,M(i)?i.get():i)),M(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let r=a(this.props,i,this.presenceContext?.custom);r&&(e=r[t])}if(i&&void 0!==e)return e;let r=this.getBaseTargetFromProps(this.props,t);return void 0===r||M(r)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:r}on(t,e){return this.events[t]||(this.events[t]=new k),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class sx extends sw{constructor(){super(...arguments),this.KeyframeResolver=sp}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;M(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function sk(t,{style:e,vars:i},r,n){for(let s in Object.assign(t.style,e,n&&n.getProjectionStyles(r)),i)t.style.setProperty(s,i[s])}class sS extends sx{constructor(){super(...arguments),this.type="html",this.renderInstance=sk}readValueFromInstance(t,e){if(b.has(e))return this.projection?.isProjecting?ew(e):ek(t,e);{let i=window.getComputedStyle(t),r=(H(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(t,{transformPagePoint:e}){return iW(t,e)}build(t,e,i){nZ(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return n4(t,e,i)}}let sA=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sE extends sx{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iM}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(b.has(e)){let t=sd(e);return t&&t.default||0}return e=sA.has(e)?e:z(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return n9(t,e,i)}build(t,e,i){nK(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,r){for(let i in sk(t,e,void 0,r),e.attrs)t.setAttribute(sA.has(i)?i:z(i),e.attrs[i])}mount(t){this.isSVGTag=nQ(t.tagName),super.mount(t)}}let sT=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,r)=>"create"===r?t:(e.has(r)||e.set(r,t(r)),e.get(r))})}((e$={animation:{Feature:id},exit:{Feature:ip},inView:{Feature:nE},tap:{Feature:n_},focus:{Feature:nd},hover:{Feature:nu},pan:{Feature:i8},drag:{Feature:i6,ProjectionNode:na,MeasureLayout:rh},layout:{ProjectionNode:na,MeasureLayout:rh}},eX=(t,e)=>n5(t)?new sE(e):new sS(e,{allowProjection:t!==rt.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:r,createVisualElement:n,useRender:s,useVisualState:a,Component:o}=t;function l(t,e){var i,r,l;let h,u={...(0,rt.useContext)(nC.Q),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,rt.useContext)(ri.L).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:d}=u,c=function(t){let{initial:e,animate:i}=function(t,e){if(nR(t)){let{initial:e,animate:i}=t;return{initial:!1===e||ie(e)?e:void 0,animate:ie(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,rt.useContext)(nP));return(0,rt.useMemo)(()=>({initial:e,animate:i}),[nD(e),nD(i)])}(t),f=a(t,d);if(!d&&nz.B){r=0,l=0,(0,rt.useContext)(nT).strict;let t=function(t){let{drag:e,layout:i}=nI;if(!e&&!i)return{};let r={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(u);h=t.MeasureLayout,c.visualElement=function(t,e,i,r,n){let{visualElement:s}=(0,rt.useContext)(nP),a=(0,rt.useContext)(nT),o=(0,rt.useContext)(nF.t),l=(0,rt.useContext)(nC.Q).reducedMotion,h=(0,rt.useRef)(null);r=r||a.renderer,!h.current&&r&&(h.current=r(t,{visualState:e,parent:s,props:i,presenceContext:o,blockInitialAnimation:!!o&&!1===o.initial,reducedMotionConfig:l}));let u=h.current,d=(0,rt.useContext)(rr);u&&!u.projection&&n&&("html"===u.type||"svg"===u.type)&&function(t,e,i,r){let{layoutId:n,layout:s,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:s,alwaysMeasureLayout:!!a||o&&iZ(o),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:r,crossfade:u,layoutScroll:l,layoutRoot:h})}(h.current,i,n,d);let c=(0,rt.useRef)(!1);(0,rt.useInsertionEffect)(()=>{u&&c.current&&u.update(i,o)});let f=i[O],p=(0,rt.useRef)(!!f&&!window.MotionHandoffIsComplete?.(f)&&window.MotionHasOptimisedAnimation?.(f));return(0,nj.E)(()=>{u&&(c.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),i7.render(u.render),p.current&&u.animationState&&u.animationState.animateChanges())}),(0,rt.useEffect)(()=>{u&&(!p.current&&u.animationState&&u.animationState.animateChanges(),p.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(f)}),p.current=!1))}),u}(o,f,u,n,t.ProjectionNode)}return(0,i9.jsxs)(nP.Provider,{value:c,children:[h&&c.visualElement?(0,i9.jsx)(h,{visualElement:c.visualElement,...u}):null,s(o,t,(i=c.visualElement,(0,rt.useCallback)(t=>{t&&f.onMount&&f.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):iZ(e)&&(e.current=t))},[i])),f,d,c.visualElement)]})}r&&function(t){for(let e in t)nI[e]={...nI[e],...t[e]}}(r),l.displayName="motion.".concat("string"==typeof o?o:"create(".concat(null!=(i=null!=(e=o.displayName)?e:o.name)?i:"",")"));let h=(0,rt.forwardRef)(l);return h[nL]=o,h}({...n5(t)?n7:n8,preloadedFeatures:e$,useRender:function(t=!1){return(e,i,r,{latestValues:n},s)=>{let a=(n5(e)?function(t,e,i,r){let n=(0,rt.useMemo)(()=>{let i=nG();return nK(i,e,nQ(r),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};nY(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},r=function(t,e){let i=t.style||{},r={};return nY(r,i,t),Object.assign(r,function({transformTemplate:t},e){return(0,rt.useMemo)(()=>{let i=nq();return nZ(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),r}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=r,i})(i,n,s,e),o=function(t,e,i){let r={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(n1(n)||!0===i&&n0(n)||!e&&!n0(n)||t.draggable&&n.startsWith("onDrag"))&&(r[n]=t[n]);return r}(i,"string"==typeof e,t),l=e!==rt.Fragment?{...o,...a,ref:r}:{},{children:h}=i,u=(0,rt.useMemo)(()=>M(h)?h.get():h,[h]);return(0,rt.createElement)(e,{...l,children:u})}}(e),createVisualElement:eX,Component:t})}))},79323:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("FileX",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m14.5 12.5-5 5",key:"b62r18"}],["path",{d:"m9.5 12.5 5 5",key:"1rk7el"}]])},80845:(t,e,i)=>{"use strict";i.d(e,{t:()=>r});let r=(0,i(12115).createContext)(null)},82178:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]])},82885:(t,e,i)=>{"use strict";i.d(e,{M:()=>n});var r=i(12115);function n(t){let e=(0,r.useRef)(null);return null===e.current&&(e.current=t()),e.current}},84209:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("FileType",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M9 13v-1h6v1",key:"1bb014"}],["path",{d:"M12 12v6",key:"3ahymv"}],["path",{d:"M11 18h2",key:"12mj7e"}]])},85690:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},90869:(t,e,i)=>{"use strict";i.d(e,{L:()=>r});let r=(0,i(12115).createContext)({})},91981:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Maximize2",[["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["polyline",{points:"9 21 3 21 3 15",key:"1avn1i"}],["line",{x1:"21",x2:"14",y1:"3",y2:"10",key:"ota7mn"}],["line",{x1:"3",x2:"10",y1:"21",y2:"14",key:"1atl0r"}]])},92138:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},92657:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},97494:(t,e,i)=>{"use strict";i.d(e,{E:()=>n});var r=i(12115);let n=i(68972).B?r.useLayoutEffect:r.useEffect},99890:(t,e,i)=>{"use strict";i.d(e,{A:()=>r});let r=(0,i(19946).A)("File",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]])}}]);