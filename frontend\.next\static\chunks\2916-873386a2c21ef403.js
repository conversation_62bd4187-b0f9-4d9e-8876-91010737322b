(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2916],{26259:(e,t,a)=>{"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0}),t.GoogleAnalytics=function(e){let{gaId:t,debugMode:a,dataLayerName:o="dataLayer",nonce:u}=e;return void 0===n&&(n=o),(0,s.useEffect)(()=>{performance.mark("mark_feature_usage",{detail:{feature:"next-third-parties-ga"}})},[]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.default,{id:"_next-ga-init",dangerouslySetInnerHTML:{__html:"\n          window['".concat(o,"'] = window['").concat(o,"'] || [];\n          function gtag(){window['").concat(o,"'].push(arguments);}\n          gtag('js', new Date());\n\n          gtag('config', '").concat(t,"' ").concat(a?",{ 'debug_mode': true }":"",");")},nonce:u}),(0,r.jsx)(i.default,{id:"_next-ga",src:"https://www.googletagmanager.com/gtag/js?id=".concat(t),nonce:u})]})},t.sendGAEvent=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];if(void 0===n)return void console.warn("@next/third-parties: GA has not been initialized");window[n]?window[n].push(arguments):console.warn("@next/third-parties: GA dataLayer ".concat(n," does not exist"))};let r=a(95155),s=a(12115),i=function(e){return e&&e.__esModule?e:{default:e}}(a(63554))},27735:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},31828:(e,t,a)=>{"use strict";a.d(t,{Analytics:()=>c});var n=a(12115),r=a(49509),s=()=>{window.va||(window.va=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];(window.vaq=window.vaq||[]).push(t)})};function i(){return"undefined"!=typeof window}function o(){return"production"}function u(){return(i()?window.vam:o())||"production"}function l(){return"development"===u()}function c(e){return(0,n.useEffect)(()=>{var t;e.beforeSend&&(null==(t=window.va)||t.call(window,"beforeSend",e.beforeSend))},[e.beforeSend]),(0,n.useEffect)(()=>{var t;!function(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{debug:!0};if(!i())return;!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"auto";if("auto"===e){window.vam=o();return}window.vam=e}(t.mode),s(),t.beforeSend&&(null==(e=window.va)||e.call(window,"beforeSend",t.beforeSend));let a=t.scriptSrc?t.scriptSrc:l()?"https://va.vercel-scripts.com/v1/script.debug.js":t.basePath?"".concat(t.basePath,"/insights/script.js"):"/_vercel/insights/script.js";if(document.head.querySelector('script[src*="'.concat(a,'"]')))return;let n=document.createElement("script");n.src=a,n.defer=!0,n.dataset.sdkn="@vercel/analytics"+(t.framework?"/".concat(t.framework):""),n.dataset.sdkv="1.5.0",t.disableAutoTrack&&(n.dataset.disableAutoTrack="1"),t.endpoint?n.dataset.endpoint=t.endpoint:t.basePath&&(n.dataset.endpoint="".concat(t.basePath,"/insights")),t.dsn&&(n.dataset.dsn=t.dsn),n.onerror=()=>{let e=l()?"Please check if any ad blockers are enabled and try again.":"Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.";console.log("[Vercel Web Analytics] Failed to load script from ".concat(a,". ").concat(e))},l()&&!1===t.debug&&(n.dataset.debug="false"),document.head.appendChild(n)}({framework:e.framework||"react",basePath:null!=(t=e.basePath)?t:function(){if(void 0!==r&&void 0!==r.env)return r.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH}(),...void 0!==e.route&&{disableAutoTrack:!0},...e})},[]),(0,n.useEffect)(()=>{e.route&&e.path&&function(e){var t;let{route:a,path:n}=e;null==(t=window.va)||t.call(window,"pageview",{route:a,path:n})}({route:e.route,path:e.path})},[e.route,e.path]),null}},35695:(e,t,a)=>{"use strict";var n=a(18999);a.o(n,"redirect")&&a.d(t,{redirect:function(){return n.redirect}}),a.o(n,"useParams")&&a.d(t,{useParams:function(){return n.useParams}}),a.o(n,"usePathname")&&a.d(t,{usePathname:function(){return n.usePathname}}),a.o(n,"useRouter")&&a.d(t,{useRouter:function(){return n.useRouter}}),a.o(n,"useSearchParams")&&a.d(t,{useSearchParams:function(){return n.useSearchParams}})},36549:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){let{html:t,height:a=null,width:s=null,children:i,dataNtpc:o=""}=e;return(0,r.useEffect)(()=>{o&&performance.mark("mark_feature_usage",{detail:{feature:"next-third-parties-".concat(o)}})},[o]),(0,n.jsxs)(n.Fragment,{children:[i,t?(0,n.jsx)("div",{style:{height:null!=a?"".concat(a,"px"):"auto",width:null!=s?"".concat(s,"px"):"auto"},"data-ntpc":o,dangerouslySetInnerHTML:{__html:t}}):null]})};let n=a(95155),r=a(12115)},42714:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return s}});let a={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},n=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function r(e){return["async","defer","noModule"].includes(e)}function s(e,t){for(let[s,i]of Object.entries(t)){if(!t.hasOwnProperty(s)||n.includes(s)||void 0===i)continue;let o=a[s]||s.toLowerCase();"SCRIPT"===e.tagName&&r(o)?e[o]=!!i:e.setAttribute(o,String(i)),(!1===i||"SCRIPT"===e.tagName&&r(o)&&(!i||"false"===i))&&(e.setAttribute(o,""),e.removeAttribute(o))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},48031:(e,t,a)=>{"use strict";a.d(t,{SpeedInsights:()=>f});var n=a(12115),r=a(35695),s=a(49509),i=()=>{window.si||(window.si=function(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];(window.siq=window.siq||[]).push(t)})};function o(){return"development"===function(){return"production"}()}function u(e){return new RegExp("/".concat(e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"(?=[/?#]|$)"))}function l(e){(0,n.useEffect)(()=>{var t;e.beforeSend&&(null==(t=window.si)||t.call(window,"beforeSend",e.beforeSend))},[e.beforeSend]);let t=(0,n.useRef)(null);return(0,n.useEffect)(()=>{if(t.current)e.route&&t.current(e.route);else{var a,n;let r=function(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if("undefined"==typeof window||null===t.route)return null;i();let a=t.scriptSrc?t.scriptSrc:o()?"https://va.vercel-scripts.com/v1/speed-insights/script.debug.js":t.dsn?"https://va.vercel-scripts.com/v1/speed-insights/script.js":t.basePath?"".concat(t.basePath,"/speed-insights/script.js"):"/_vercel/speed-insights/script.js";if(document.head.querySelector('script[src*="'.concat(a,'"]')))return null;t.beforeSend&&(null==(e=window.si)||e.call(window,"beforeSend",t.beforeSend));let n=document.createElement("script");return n.src=a,n.defer=!0,n.dataset.sdkn="@vercel/speed-insights"+(t.framework?"/".concat(t.framework):""),n.dataset.sdkv="1.2.0",t.sampleRate&&(n.dataset.sampleRate=t.sampleRate.toString()),t.route&&(n.dataset.route=t.route),t.endpoint?n.dataset.endpoint=t.endpoint:t.basePath&&(n.dataset.endpoint="".concat(t.basePath,"/speed-insights/vitals")),t.dsn&&(n.dataset.dsn=t.dsn),o()&&!1===t.debug&&(n.dataset.debug="false"),n.onerror=()=>{console.log("[Vercel Speed Insights] Failed to load script from ".concat(a,". Please check if any content blockers are enabled and try again."))},document.head.appendChild(n),{setRoute:e=>{n.dataset.route=null!=e?e:void 0}}}({framework:null!=(a=e.framework)?a:"react",basePath:null!=(n=e.basePath)?n:function(){if(void 0!==s&&void 0!==s.env)return s.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH}(),...e});r&&(t.current=r.setRoute)}},[e.route]),null}var c=()=>{let e=(0,r.useParams)(),t=(0,r.useSearchParams)()||new URLSearchParams,a=(0,r.usePathname)();return e?function(e,t){if(!e||!t)return e;let a=e;try{let e=Object.entries(t);for(let[t,n]of e)if(!Array.isArray(n)){let e=u(n);e.test(a)&&(a=a.replace(e,"/[".concat(t,"]")))}for(let[t,n]of e)if(Array.isArray(n)){let e=u(n.join("/"));e.test(a)&&(a=a.replace(e,"/[...".concat(t,"]")))}return a}catch(t){return e}}(a,Object.keys(e).length?e:Object.fromEntries(t.entries())):null};function d(e){let t=c();return n.createElement(l,{route:t,...e,framework:"next",basePath:function(){if(void 0!==s&&void 0!==s.env)return s.env.NEXT_PUBLIC_VERCEL_OBSERVABILITY_BASEPATH}()})}function f(e){return n.createElement(n.Suspense,{fallback:null},n.createElement(d,{...e}))}},51362:(e,t,a)=>{"use strict";a.d(t,{D:()=>l,N:()=>c});var n=a(12115),r=(e,t,a,n,r,s,i,o)=>{let u=document.documentElement,l=["light","dark"];function c(t){var a;(Array.isArray(e)?e:[e]).forEach(e=>{let a="class"===e,n=a&&s?r.map(e=>s[e]||e):r;a?(u.classList.remove(...n),u.classList.add(s&&s[t]?s[t]:t)):u.setAttribute(e,t)}),a=t,o&&l.includes(a)&&(u.style.colorScheme=a)}if(n)c(n);else try{let e=localStorage.getItem(t)||a,n=i&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(n)}catch(e){}},s=["light","dark"],i="(prefers-color-scheme: dark)",o=n.createContext(void 0),u={setTheme:e=>{},themes:[]},l=()=>{var e;return null!=(e=n.useContext(o))?e:u},c=e=>n.useContext(o)?n.createElement(n.Fragment,null,e.children):n.createElement(f,{...e}),d=["light","dark"],f=e=>{let{forcedTheme:t,disableTransitionOnChange:a=!1,enableSystem:r=!0,enableColorScheme:u=!0,storageKey:l="theme",themes:c=d,defaultTheme:f=r?"system":"light",attribute:g="data-theme",value:v,children:b,nonce:w,scriptProps:_}=e,[P,O]=n.useState(()=>p(l,f)),[S,E]=n.useState(()=>"system"===P?y():P),C=v?Object.values(v):c,A=n.useCallback(e=>{let t=e;if(!t)return;"system"===e&&r&&(t=y());let n=v?v[t]:t,i=a?m(w):null,o=document.documentElement,l=e=>{"class"===e?(o.classList.remove(...C),n&&o.classList.add(n)):e.startsWith("data-")&&(n?o.setAttribute(e,n):o.removeAttribute(e))};if(Array.isArray(g)?g.forEach(l):l(g),u){let e=s.includes(f)?f:null,a=s.includes(t)?t:e;o.style.colorScheme=a}null==i||i()},[w]),q=n.useCallback(e=>{let t="function"==typeof e?e(P):e;O(t);try{localStorage.setItem(l,t)}catch(e){}},[P]),j=n.useCallback(e=>{E(y(e)),"system"===P&&r&&!t&&A("system")},[P,t]);n.useEffect(()=>{let e=window.matchMedia(i);return e.addListener(j),j(e),()=>e.removeListener(j)},[j]),n.useEffect(()=>{let e=e=>{e.key===l&&(e.newValue?O(e.newValue):q(f))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[q]),n.useEffect(()=>{A(null!=t?t:P)},[t,P]);let M=n.useMemo(()=>({theme:P,setTheme:q,forcedTheme:t,resolvedTheme:"system"===P?S:P,themes:r?[...c,"system"]:c,systemTheme:r?S:void 0}),[P,q,t,S,r,c]);return n.createElement(o.Provider,{value:M},n.createElement(h,{forcedTheme:t,storageKey:l,attribute:g,enableSystem:r,enableColorScheme:u,defaultTheme:f,value:v,themes:c,nonce:w,scriptProps:_}),b)},h=n.memo(e=>{let{forcedTheme:t,storageKey:a,attribute:s,enableSystem:i,enableColorScheme:o,defaultTheme:u,value:l,themes:c,nonce:d,scriptProps:f}=e,h=JSON.stringify([s,a,u,t,c,l,i,o]).slice(1,-1);return n.createElement("script",{...f,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(r.toString(),")(").concat(h,")")}})}),p=(e,t)=>{let a;try{a=localStorage.getItem(e)||void 0}catch(e){}return a||t},m=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},y=e=>(e||(e=window.matchMedia(i)),e.matches?"dark":"light")},62093:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},63554:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r.a});var n=a(69243),r=a.n(n),s={};for(let e in n)"default"!==e&&(s[e]=()=>n[e]);a.d(t,s)},69243:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return v},handleClientScriptLoad:function(){return m},initScriptLoader:function(){return y}});let n=a(38466),r=a(93011),s=a(95155),i=n._(a(47650)),o=r._(a(12115)),u=a(82830),l=a(42714),c=a(92374),d=new Map,f=new Set,h=e=>{if(i.default.preinit)return void e.forEach(e=>{i.default.preinit(e,{as:"style"})});{let t=document.head;e.forEach(e=>{let a=document.createElement("link");a.type="text/css",a.rel="stylesheet",a.href=e,t.appendChild(a)})}},p=e=>{let{src:t,id:a,onLoad:n=()=>{},onReady:r=null,dangerouslySetInnerHTML:s,children:i="",strategy:o="afterInteractive",onError:u,stylesheets:c}=e,p=a||t;if(p&&f.has(p))return;if(d.has(t)){f.add(p),d.get(t).then(n,u);return}let m=()=>{r&&r(),f.add(p)},y=document.createElement("script"),g=new Promise((e,t)=>{y.addEventListener("load",function(t){e(),n&&n.call(this,t),m()}),y.addEventListener("error",function(e){t(e)})}).catch(function(e){u&&u(e)});s?(y.innerHTML=s.__html||"",m()):i?(y.textContent="string"==typeof i?i:Array.isArray(i)?i.join(""):"",m()):t&&(y.src=t,d.set(t,g)),(0,l.setAttributesFromProps)(y,e),"worker"===o&&y.setAttribute("type","text/partytown"),y.setAttribute("data-nscript",o),c&&h(c),document.body.appendChild(y)};function m(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>p(e))}):p(e)}function y(e){e.forEach(m),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");f.add(t)})}function g(e){let{id:t,src:a="",onLoad:n=()=>{},onReady:r=null,strategy:l="afterInteractive",onError:d,stylesheets:h,...m}=e,{updateScripts:y,scripts:g,getIsSsr:v,appDir:b,nonce:w}=(0,o.useContext)(u.HeadManagerContext),_=(0,o.useRef)(!1);(0,o.useEffect)(()=>{let e=t||a;_.current||(r&&e&&f.has(e)&&r(),_.current=!0)},[r,t,a]);let P=(0,o.useRef)(!1);if((0,o.useEffect)(()=>{if(!P.current){if("afterInteractive"===l)p(e);else"lazyOnload"===l&&("complete"===document.readyState?(0,c.requestIdleCallback)(()=>p(e)):window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>p(e))}));P.current=!0}},[e,l]),("beforeInteractive"===l||"worker"===l)&&(y?(g[l]=(g[l]||[]).concat([{id:t,src:a,onLoad:n,onReady:r,onError:d,...m}]),y(g)):v&&v()?f.add(t||a):v&&!v()&&p(e)),b){if(h&&h.forEach(e=>{i.default.preinit(e,{as:"style"})}),"beforeInteractive"===l)if(!a)return m.dangerouslySetInnerHTML&&(m.children=m.dangerouslySetInnerHTML.__html,delete m.dangerouslySetInnerHTML),(0,s.jsx)("script",{nonce:w,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...m,id:t}])+")"}});else return i.default.preload(a,m.integrity?{as:"script",integrity:m.integrity,nonce:w,crossOrigin:m.crossOrigin}:{as:"script",nonce:w,crossOrigin:m.crossOrigin}),(0,s.jsx)("script",{nonce:w,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([a,{...m,id:t}])+")"}});"afterInteractive"===l&&a&&i.default.preload(a,m.integrity?{as:"script",integrity:m.integrity,nonce:w,crossOrigin:m.crossOrigin}:{as:"script",nonce:w,crossOrigin:m.crossOrigin})}return null}Object.defineProperty(g,"__nextScript",{value:!0});let v=g;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},81451:(e,t,a)=>{"use strict";a.d(t,{Qv:()=>l,hw:()=>u});var n=a(73504);function r(e){return e}function s(e){return e.state.isPaused}function i(e){return"success"===e.state.status}function o(e){return!0}function u(e,t={}){let a=t.shouldDehydrateMutation??e.getDefaultOptions().dehydrate?.shouldDehydrateMutation??s,n=e.getMutationCache().getAll().flatMap(e=>a(e)?[{mutationKey:e.options.mutationKey,state:e.state,...e.options.scope&&{scope:e.options.scope},...e.meta&&{meta:e.meta}}]:[]),l=t.shouldDehydrateQuery??e.getDefaultOptions().dehydrate?.shouldDehydrateQuery??i,c=t.shouldRedactErrors??e.getDefaultOptions().dehydrate?.shouldRedactErrors??o,d=t.serializeData??e.getDefaultOptions().dehydrate?.serializeData??r;return{mutations:n,queries:e.getQueryCache().getAll().flatMap(e=>l(e)?[{dehydratedAt:Date.now(),state:{...e.state,...void 0!==e.state.data&&{data:d(e.state.data)}},queryKey:e.queryKey,queryHash:e.queryHash,..."pending"===e.state.status&&{promise:e.promise?.then(d).catch(e=>c(e)?Promise.reject(Error("redacted")):Promise.reject(e))},...e.meta&&{meta:e.meta}}]:[])}}function l(e,t,a){if("object"!=typeof t||null===t)return;let s=e.getMutationCache(),i=e.getQueryCache(),o=a?.defaultOptions?.deserializeData??e.getDefaultOptions().hydrate?.deserializeData??r,u=t.mutations||[],l=t.queries||[];u.forEach(({state:t,...n})=>{s.build(e,{...e.getDefaultOptions().hydrate?.mutations,...a?.defaultOptions?.mutations,...n},t)}),l.forEach(({queryKey:t,state:r,queryHash:s,meta:u,promise:l,dehydratedAt:c})=>{let d=l?(0,n.b)(l):void 0,f=void 0===r.data?d?.data:r.data,h=void 0===f?f:o(f),p=i.get(s),m=p?.state.status==="pending",y=p?.state.fetchStatus==="fetching";if(p){let e=d&&void 0!==c&&c>p.state.dataUpdatedAt;if(r.dataUpdatedAt>p.state.dataUpdatedAt||e){let{fetchStatus:e,...t}=r;p.setState({...t,data:h})}}else p=i.build(e,{...e.getDefaultOptions().hydrate?.queries,...a?.defaultOptions?.queries,queryKey:t,queryHash:s,meta:u},{...r,data:h,fetchStatus:"idle",status:void 0!==h?"success":r.status});l&&!m&&!y&&(void 0===c||c>p.state.dataUpdatedAt)&&p.fetch(void 0,{initialPromise:Promise.resolve(l).then(o)})})}},87017:(e,t,a)=>{"use strict";a.d(t,{E:()=>m});var n=a(52020),r=a(39853),s=a(7165),i=a(25910),o=class extends i.Q{constructor(e={}){super(),this.config=e,this.#e=new Map}#e;build(e,t,a){let s=t.queryKey,i=t.queryHash??(0,n.F$)(s,t),o=this.get(i);return o||(o=new r.X({client:e,queryKey:s,queryHash:i,options:e.defaultQueryOptions(t),state:a,defaultOptions:e.getQueryDefaults(s)}),this.add(o)),o}add(e){this.#e.has(e.queryHash)||(this.#e.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#e.get(e.queryHash);t&&(e.destroy(),t===e&&this.#e.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){s.jG.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#e.get(e)}getAll(){return[...this.#e.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,n.MK)(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>(0,n.MK)(e,t)):t}notify(e){s.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){s.jG.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){s.jG.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},u=a(34560),l=class extends i.Q{constructor(e={}){super(),this.config=e,this.#t=new Set,this.#a=new Map,this.#n=0}#t;#a;#n;build(e,t,a){let n=new u.s({mutationCache:this,mutationId:++this.#n,options:e.defaultMutationOptions(t),state:a});return this.add(n),n}add(e){this.#t.add(e);let t=c(e);if("string"==typeof t){let a=this.#a.get(t);a?a.push(e):this.#a.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#t.delete(e)){let t=c(e);if("string"==typeof t){let a=this.#a.get(t);if(a)if(a.length>1){let t=a.indexOf(e);-1!==t&&a.splice(t,1)}else a[0]===e&&this.#a.delete(t)}}this.notify({type:"removed",mutation:e})}canRun(e){let t=c(e);if("string"!=typeof t)return!0;{let a=this.#a.get(t),n=a?.find(e=>"pending"===e.state.status);return!n||n===e}}runNext(e){let t=c(e);if("string"!=typeof t)return Promise.resolve();{let a=this.#a.get(t)?.find(t=>t!==e&&t.state.isPaused);return a?.continue()??Promise.resolve()}}clear(){s.jG.batch(()=>{this.#t.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#t.clear(),this.#a.clear()})}getAll(){return Array.from(this.#t)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,n.nJ)(t,e))}findAll(e={}){return this.getAll().filter(t=>(0,n.nJ)(e,t))}notify(e){s.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return s.jG.batch(()=>Promise.all(e.map(e=>e.continue().catch(n.lQ))))}};function c(e){return e.options.scope?.id}var d=a(50920),f=a(21239);function h(e){return{onFetch:(t,a)=>{let r=t.options,s=t.fetchOptions?.meta?.fetchMore?.direction,i=t.state.data?.pages||[],o=t.state.data?.pageParams||[],u={pages:[],pageParams:[]},l=0,c=async()=>{let a=!1,c=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?a=!0:t.signal.addEventListener("abort",()=>{a=!0}),t.signal)})},d=(0,n.ZM)(t.options,t.fetchOptions),f=async(e,r,s)=>{if(a)return Promise.reject();if(null==r&&e.pages.length)return Promise.resolve(e);let i={client:t.client,queryKey:t.queryKey,pageParam:r,direction:s?"backward":"forward",meta:t.options.meta};c(i);let o=await d(i),{maxPages:u}=t.options,l=s?n.ZZ:n.y9;return{pages:l(e.pages,o,u),pageParams:l(e.pageParams,r,u)}};if(s&&i.length){let e="backward"===s,t={pages:i,pageParams:o},a=(e?function(e,{pages:t,pageParams:a}){return t.length>0?e.getPreviousPageParam?.(t[0],t,a[0],a):void 0}:p)(r,t);u=await f(t,a,e)}else{let t=e??i.length;do{let e=0===l?o[0]??r.initialPageParam:p(r,u);if(l>0&&null==e)break;u=await f(u,e),l++}while(l<t)}return u};t.options.persister?t.fetchFn=()=>t.options.persister?.(c,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},a):t.fetchFn=c}}}function p(e,{pages:t,pageParams:a}){let n=t.length-1;return t.length>0?e.getNextPageParam(t[n],t,a[n],a):void 0}var m=class{#r;#s;#i;#o;#u;#l;#c;#d;constructor(e={}){this.#r=e.queryCache||new o,this.#s=e.mutationCache||new l,this.#i=e.defaultOptions||{},this.#o=new Map,this.#u=new Map,this.#l=0}mount(){this.#l++,1===this.#l&&(this.#c=d.m.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#r.onFocus())}),this.#d=f.t.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#r.onOnline())}))}unmount(){this.#l--,0===this.#l&&(this.#c?.(),this.#c=void 0,this.#d?.(),this.#d=void 0)}isFetching(e){return this.#r.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#s.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#r.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),a=this.#r.build(this,t),r=a.state.data;return void 0===r?this.fetchQuery(e):(e.revalidateIfStale&&a.isStaleByTime((0,n.d2)(t.staleTime,a))&&this.prefetchQuery(t),Promise.resolve(r))}getQueriesData(e){return this.#r.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,a){let r=this.defaultQueryOptions({queryKey:e}),s=this.#r.get(r.queryHash),i=s?.state.data,o=(0,n.Zw)(t,i);if(void 0!==o)return this.#r.build(this,r).setData(o,{...a,manual:!0})}setQueriesData(e,t,a){return s.jG.batch(()=>this.#r.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,a)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#r.get(t.queryHash)?.state}removeQueries(e){let t=this.#r;s.jG.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let a=this.#r;return s.jG.batch(()=>(a.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let a={revert:!0,...t};return Promise.all(s.jG.batch(()=>this.#r.findAll(e).map(e=>e.cancel(a)))).then(n.lQ).catch(n.lQ)}invalidateQueries(e,t={}){return s.jG.batch(()=>(this.#r.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let a={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(s.jG.batch(()=>this.#r.findAll(e).filter(e=>!e.isDisabled()&&!e.isStatic()).map(e=>{let t=e.fetch(void 0,a);return a.throwOnError||(t=t.catch(n.lQ)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(n.lQ)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let a=this.#r.build(this,t);return a.isStaleByTime((0,n.d2)(t.staleTime,a))?a.fetch(t):Promise.resolve(a.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(n.lQ).catch(n.lQ)}fetchInfiniteQuery(e){return e.behavior=h(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(n.lQ).catch(n.lQ)}ensureInfiniteQueryData(e){return e.behavior=h(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return f.t.isOnline()?this.#s.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#r}getMutationCache(){return this.#s}getDefaultOptions(){return this.#i}setDefaultOptions(e){this.#i=e}setQueryDefaults(e,t){this.#o.set((0,n.EN)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#o.values()],a={};return t.forEach(t=>{(0,n.Cp)(e,t.queryKey)&&Object.assign(a,t.defaultOptions)}),a}setMutationDefaults(e,t){this.#u.set((0,n.EN)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#u.values()],a={};return t.forEach(t=>{(0,n.Cp)(e,t.mutationKey)&&Object.assign(a,t.defaultOptions)}),a}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#i.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,n.F$)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===n.hT&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#i.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#r.clear(),this.#s.clear()}}},87573:(e,t,a)=>{"use strict";a.d(t,{f:()=>i});var n=a(12115),r=a(81451),s=a(26715),i=e=>{let{children:t,options:a={},state:i,queryClient:o}=e,u=(0,s.jE)(o),[l,c]=n.useState(),d=n.useRef(a);return d.current=a,n.useMemo(()=>{if(i){if("object"!=typeof i)return;let e=u.getQueryCache(),t=i.queries||[],a=[],n=[];for(let r of t){let t=e.get(r.queryHash);if(t){let e=r.state.dataUpdatedAt>t.state.dataUpdatedAt||r.promise&&"pending"!==t.state.status&&"fetching"!==t.state.fetchStatus&&void 0!==r.dehydratedAt&&r.dehydratedAt>t.state.dataUpdatedAt,a=null==l?void 0:l.find(e=>e.queryHash===r.queryHash);e&&(!a||r.state.dataUpdatedAt>a.state.dataUpdatedAt)&&n.push(r)}else a.push(r)}a.length>0&&(0,r.Qv)(u,{queries:a},d.current),n.length>0&&c(e=>e?[...e,...n]:n)}},[u,l,i]),n.useEffect(()=>{l&&((0,r.Qv)(u,{queries:l},d.current),c(void 0))},[u,l]),t}},92374:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return a}});let a="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96063:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.sendGTMEvent=void 0,t.GoogleTagManager=function(e){let{gtmId:t,gtmScriptUrl:a="https://www.googletagmanager.com/gtm.js",dataLayerName:o="dataLayer",auth:u,preview:l,dataLayer:c,nonce:d}=e;i=o;let f="dataLayer"!==o?"&l=".concat(o):"";return(0,r.useEffect)(()=>{performance.mark("mark_feature_usage",{detail:{feature:"next-third-parties-gtm"}})},[]),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(s.default,{id:"_next-gtm-init",dangerouslySetInnerHTML:{__html:"\n      (function(w,l){\n        w[l]=w[l]||[];\n        w[l].push({'gtm.start': new Date().getTime(),event:'gtm.js'});\n        ".concat(c?"w[l].push(".concat(JSON.stringify(c),")"):"","\n      })(window,'").concat(o,"');")},nonce:d}),(0,n.jsx)(s.default,{id:"_next-gtm","data-ntpc":"GTM",src:"".concat(a,"?id=").concat(t).concat(f).concat(u?"&gtm_auth=".concat(u):"").concat(l?"&gtm_preview=".concat(l,"&gtm_cookies_win=x"):""),nonce:d})]})};let n=a(95155),r=a(12115),s=function(e){return e&&e.__esModule?e:{default:e}}(a(63554)),i="dataLayer";t.sendGTMEvent=(e,t)=>{let a=t||i;window[a]=window[a]||[],window[a].push(e)}}}]);