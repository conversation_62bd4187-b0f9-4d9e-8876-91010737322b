"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6915],{32942:(t,e,i)=>{i.d(e,{d:()=>o});var n=i(12115),s=i(7471),r=i(76168),a=i(99967);function o(t){let e=(0,r.M)(()=>(0,a.OQ)(t)),{isStatic:i}=(0,n.useContext)(s.Q);if(i){let[,i]=(0,n.useState)(t);(0,n.useEffect)(()=>e.on("change",i),[])}return e}},34031:(t,e,i)=>{i.d(e,{z:()=>g});var n=i(12115),s=i(7471),r=i(32942),a=i(76168),o=i(69025),l=i(46182);function h(t,e){let i=(0,r.d)(e()),n=()=>i.set(e());return n(),(0,o.E)(()=>{let e=()=>l.Gt.preRender(n,!1,!0),i=t.map(t=>t.on("change",e));return()=>{i.forEach(t=>t()),(0,l.WG)(n)}}),i}var u=i(99967),c=i(38154);function p(t,e){let i=(0,a.M)(()=>[]);return h(t,()=>{i.length=0;let n=t.length;for(let e=0;e<n;e++)i[e]=t[e].get();return e(i)})}var d=i(41192),f=i(3126);function m(t){return"number"==typeof t?t:parseFloat(t)}function g(t,e={}){let{isStatic:i}=(0,n.useContext)(s.Q),a=()=>(0,f.S)(t)?t.get():t;if(i)return function(t,e,i,n){if("function"==typeof t){u.bt.current=[],t();let e=h(u.bt.current,t);return u.bt.current=void 0,e}let s=function(...t){let e=!Array.isArray(t[0]),i=e?0:-1,n=t[0+i],s=t[1+i],r=t[2+i],a=t[3+i],o=(0,c.G)(s,r,a);return e?o(n):o}(void 0,void 0,void 0);return Array.isArray(t)?p(t,s):p([t],([t])=>s(t))}(a);let o=(0,r.d)(a());return(0,n.useInsertionEffect)(()=>(function(t,e,i){let n,s,r=t.get(),a=null,o=r,h="string"==typeof r?r.replace(/[\d.-]/g,""):void 0,u=()=>{a&&(a.stop(),a=null)},c=()=>{u(),a=new d.s({keyframes:[m(t.get()),m(o)],velocity:t.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...i,onUpdate:n})};return t.attach((e,i)=>(o=e,n=t=>{var e,n;return i((e=t,(n=h)?e+n:e))},l.Gt.postRender(c),t.get()),u),(0,f.S)(e)&&(s=e.on("change",e=>{var i,n;return t.set((i=e,(n=h)?i+n:i))}),t.on("destroy",s)),s})(o,t,e),[o,JSON.stringify(e)]),o}},37619:(t,e,i)=>{let n;i.d(e,{Ay:()=>H});var s,r=i(12115);let a=(t,e,i)=>{let n=document.createElement(t),[s,r]=Array.isArray(e)?[void 0,e]:[e,i];return s&&Object.assign(n,s),null==r||r.forEach(t=>n.appendChild(t)),n},o=(t,e)=>{var i;return"left"===e?t.offsetLeft:((null==(i=t.offsetParent instanceof HTMLElement?t.offsetParent:null)?void 0:i.offsetWidth)??0)-t.offsetWidth-t.offsetLeft},l=t=>t.offsetWidth>0&&t.offsetHeight>0,h=String.raw,u=String.raw,c=(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0})(),p="u">typeof CSS&&CSS.supports&&CSS.supports("line-height","mod(1,1)"),d="u">typeof matchMedia?matchMedia("(prefers-reduced-motion: reduce)"):null,f="--_number-flow-d-opacity",m="--_number-flow-d-width",g="--_number-flow-dx",v="--_number-flow-d",y=(()=>{try{return CSS.registerProperty({name:f,syntax:"<number>",inherits:!1,initialValue:"0"}),CSS.registerProperty({name:g,syntax:"<length>",inherits:!0,initialValue:"0px"}),CSS.registerProperty({name:m,syntax:"<number>",inherits:!1,initialValue:"0"}),CSS.registerProperty({name:v,syntax:"<number>",inherits:!0,initialValue:"0"}),!0}catch{return!1}})(),b="var(--number-flow-char-height, 1em)",w="var(--number-flow-mask-height, 0.25em)",A=`calc(${w} / 2)`,x="var(--number-flow-mask-width, 0.5em)",_=`calc(${x} / var(--scale-x))`,E="#000 0, transparent 71%",C=u`:host{display:inline-block;direction:ltr;white-space:nowrap;isolation:isolate;line-height:${b} !important}.number,.number__inner{display:inline-block;transform-origin:left top}:host([data-will-change]) :is(.number,.number__inner,.section,.digit,.digit__num,.symbol){will-change:transform}.number{--scale-x:calc(1 + var(${m}) / var(--width));transform:translateX(var(${g})) scaleX(var(--scale-x));margin:0 calc(-1 * ${x});position:relative;-webkit-mask-image:linear-gradient(to right,transparent 0,#000 ${_},#000 calc(100% - ${_}),transparent ),linear-gradient(to bottom,transparent 0,#000 ${w},#000 calc(100% - ${w}),transparent 100% ),radial-gradient(at bottom right,${E}),radial-gradient(at bottom left,${E}),radial-gradient(at top left,${E}),radial-gradient(at top right,${E});-webkit-mask-size:100% calc(100% - ${w} * 2),calc(100% - ${_} * 2) 100%,${_} ${w},${_} ${w},${_} ${w},${_} ${w};-webkit-mask-position:center,center,top left,top right,bottom right,bottom left;-webkit-mask-repeat:no-repeat}.number__inner{padding:${A} ${x};transform:scaleX(calc(1 / var(--scale-x))) translateX(calc(-1 * var(${g})))}:host > :not(.number){z-index:5}.section,.symbol{display:inline-block;position:relative;isolation:isolate}.section::after{content:'\200b';display:inline-block}.section--justify-left{transform-origin:center left}.section--justify-right{transform-origin:center right}.section > [inert],.symbol > [inert]{margin:0 !important;position:absolute !important;z-index:-1}.digit{display:inline-block;position:relative;--c:var(--current) + var(${v})}.digit__num,.number .section::after{padding:${A} 0}.digit__num{display:inline-block;--offset-raw:mod(var(--length) + var(--n) - mod(var(--c),var(--length)),var(--length));--offset:calc( var(--offset-raw) - var(--length) * round(down,var(--offset-raw) / (var(--length) / 2),1) );--y:clamp(-100%,var(--offset) * 100%,100%);transform:translateY(var(--y))}.digit__num[inert]{position:absolute;top:0;left:50%;transform:translateX(-50%) translateY(var(--y))}.digit:not(.is-spinning) .digit__num[inert]{display:none}.symbol__value{display:inline-block;mix-blend-mode:plus-lighter;white-space:pre}.section--justify-left .symbol > [inert]{left:0}.section--justify-right .symbol > [inert]{right:0}.animate-presence{opacity:calc(1 + var(${f}))}`,S=HTMLElement,R=u`:host{display:inline-block;direction:ltr;white-space:nowrap;line-height:${b} !important}span{display:inline-block}:host([data-will-change]) span{will-change:transform}.number,.digit{padding:${A} 0}.symbol{white-space:pre}`,M=t=>`<span class="${"integer"===t.type||"fraction"===t.type?"digit":"symbol"}" part="${"integer"===t.type||"fraction"===t.type?`digit ${t.type}-digit`:`symbol ${t.type}`}">${t.value}</span>`,P=(t,e)=>`<span part="${e}">${t.reduce((t,e)=>t+M(e),"")}</span>`,k=p&&c&&y;class U extends S{constructor(){super(),this.created=!1,this.batched=!1;let{animated:t,...e}=this.constructor.defaultProps;this._animated=this.computedAnimated=t,Object.assign(this,e)}get animated(){return this._animated}set animated(t){var e;this.animated!==t&&(this._animated=t,null==(e=this.shadowRoot)||e.getAnimations().forEach(t=>t.finish()))}set data(t){var e;if(null==t)return;let{pre:i,integer:s,fraction:r,post:a,value:o}=t;if(this.created){let n=this._data;this._data=t,this.computedTrend="function"==typeof this.trend?this.trend(n.value,o):this.trend,this.computedAnimated=k&&this._animated&&(!this.respectMotionPreference||!(null!=d&&d.matches))&&l(this),null==(e=this.plugins)||e.forEach(e=>{var i;return null==(i=e.onUpdate)?void 0:i.call(e,t,n,this)}),this.batched||this.willUpdate(),this._pre.update(i),this._num.update({integer:s,fraction:r}),this._post.update(a),this.batched||this.didUpdate()}else{this._data=t,this.attachShadow({mode:"open"});try{this._internals??(this._internals=this.attachInternals()),this._internals.role="img"}catch{}if("u">typeof CSSStyleSheet&&this.shadowRoot.adoptedStyleSheets)n||(n=new CSSStyleSheet).replaceSync(C),this.shadowRoot.adoptedStyleSheets=[n];else{let t=document.createElement("style");t.textContent=C,this.shadowRoot.appendChild(t)}this._pre=new T(this,i,{justify:"right",part:"left"}),this.shadowRoot.appendChild(this._pre.el),this._num=new j(this,s,r),this.shadowRoot.appendChild(this._num.el),this._post=new T(this,a,{justify:"left",part:"right"}),this.shadowRoot.appendChild(this._post.el),this.created=!0}try{this._internals.ariaLabel=t.valueAsString}catch{}}willUpdate(){this._pre.willUpdate(),this._num.willUpdate(),this._post.willUpdate()}didUpdate(){if(!this.computedAnimated)return;this._abortAnimationsFinish?this._abortAnimationsFinish.abort():this.dispatchEvent(new Event("animationsstart")),this._pre.didUpdate(),this._num.didUpdate(),this._post.didUpdate();let t=new AbortController;Promise.all(this.shadowRoot.getAnimations().map(t=>t.finished)).then(()=>{t.signal.aborted||(this.dispatchEvent(new Event("animationsfinish")),this._abortAnimationsFinish=void 0)}),this._abortAnimationsFinish=t}}U.defaultProps={transformTiming:{duration:900,easing:"linear(0,.005,.019,.039,.066,.096,.129,.165,.202,.24,.278,.316,.354,.39,.426,.461,.494,.526,.557,.586,.614,.64,.665,.689,.711,.731,.751,.769,.786,.802,.817,.831,.844,.856,.867,.877,.887,.896,.904,.912,.919,.925,.931,.937,.942,.947,.951,.955,.959,.962,.965,.968,.971,.973,.976,.978,.98,.981,.983,.984,.986,.987,.988,.989,.99,.991,.992,.992,.993,.994,.994,.995,.995,.996,.996,.9963,.9967,.9969,.9972,.9975,.9977,.9979,.9981,.9982,.9984,.9985,.9987,.9988,.9989,1)"},spinTiming:void 0,opacityTiming:{duration:450,easing:"ease-out"},animated:!0,trend:(t,e)=>Math.sign(e-t),respectMotionPreference:!0,plugins:void 0,digits:void 0};class j{constructor(t,e,i,{className:n,...s}={}){this.flow=t,this._integer=new B(t,e,{justify:"right",part:"integer"}),this._fraction=new B(t,i,{justify:"left",part:"fraction"}),this._inner=a("span",{className:"number__inner"},[this._integer.el,this._fraction.el]),this.el=a("span",{...s,part:"number",className:`number ${n??""}`},[this._inner])}willUpdate(){this._prevWidth=this.el.offsetWidth,this._prevLeft=this.el.getBoundingClientRect().left,this._integer.willUpdate(),this._fraction.willUpdate()}update({integer:t,fraction:e}){this._integer.update(t),this._fraction.update(e)}didUpdate(){let t=this.el.getBoundingClientRect();this._integer.didUpdate(),this._fraction.didUpdate();let e=this._prevLeft-t.left,i=this.el.offsetWidth,n=this._prevWidth-i;this.el.style.setProperty("--width",String(i)),this.el.animate({[g]:[`${e}px`,"0px"],[m]:[n,0]},{...this.flow.transformTiming,composite:"accumulate"})}}class z{constructor(t,e,{justify:i,className:n,...s},r){this.flow=t,this.children=new Map,this.onCharRemove=t=>()=>{this.children.delete(t)},this.justify=i;let o=e.map(t=>this.addChar(t).el);this.el=a("span",{...s,className:`section section--justify-${i} ${n??""}`},r?r(o):o)}addChar(t,{startDigitsAtZero:e=!1,...i}={}){let n="integer"===t.type||"fraction"===t.type?new L(this,t.type,e?0:t.value,t.pos,{...i,onRemove:this.onCharRemove(t.key)}):new N(this,t.type,t.value,{...i,onRemove:this.onCharRemove(t.key)});return this.children.set(t.key,n),n}unpop(t){t.el.removeAttribute("inert"),t.el.style.top="",t.el.style[this.justify]=""}pop(t){t.forEach(t=>{t.el.style.top=`${t.el.offsetTop}px`,t.el.style[this.justify]=`${o(t.el,this.justify)}px`}),t.forEach(t=>{t.el.setAttribute("inert",""),t.present=!1})}addNewAndUpdateExisting(t){let e=new Map,i=new Map,n="left"===this.justify,s=n?"prepend":"append";if(function(t,e,{reverse:i=!1}={}){let n=t.length;for(let s=i?n-1:0;i?s>=0:s<n;i?s--:s++)e(t[s],s)}(t,t=>{let n;this.children.has(t.key)?(n=this.children.get(t.key),i.set(t,n),this.unpop(n),n.present=!0):(n=this.addChar(t,{startDigitsAtZero:!0,animateIn:!0}),e.set(t,n)),this.el[s](n.el)},{reverse:n}),this.flow.computedAnimated){let t=this.el.getBoundingClientRect();e.forEach(e=>{e.willUpdate(t)})}e.forEach((t,e)=>{t.update(e.value)}),i.forEach((t,e)=>{t.update(e.value)})}willUpdate(){let t=this.el.getBoundingClientRect();this._prevOffset=t[this.justify],this.children.forEach(e=>e.willUpdate(t))}didUpdate(){let t=this.el.getBoundingClientRect();this.children.forEach(e=>e.didUpdate(t));let e=t[this.justify],i=this._prevOffset-e;i&&this.children.size&&this.el.animate({transform:[`translateX(${i}px)`,"none"]},{...this.flow.transformTiming,composite:"accumulate"})}}class B extends z{update(t){let e=new Map;this.children.forEach((i,n)=>{t.find(t=>t.key===n)||e.set(n,i),this.unpop(i)}),this.addNewAndUpdateExisting(t),e.forEach(t=>{t instanceof L&&t.update(0)}),this.pop(e)}}class T extends z{update(t){let e=new Map;this.children.forEach((i,n)=>{t.find(t=>t.key===n)||e.set(n,i)}),this.pop(e),this.addNewAndUpdateExisting(t)}}class F{constructor(t,e,{onRemove:i,animateIn:n=!1}={}){this.flow=t,this.el=e,this._present=!0,this._remove=()=>{var t;this.el.remove(),null==(t=this._onRemove)||t.call(this)},this.el.classList.add("animate-presence"),this.flow.computedAnimated&&n&&this.el.animate({[f]:[-.9999,0]},{...this.flow.opacityTiming,composite:"accumulate"}),this._onRemove=i}get present(){return this._present}set present(t){if(this._present!==t){if(this._present=t,t?this.el.removeAttribute("inert"):this.el.setAttribute("inert",""),!this.flow.computedAnimated){t||this._remove();return}this.el.style.setProperty("--_number-flow-d-opacity",t?"0":"-.999"),this.el.animate({[f]:t?[-.9999,0]:[.999,0]},{...this.flow.opacityTiming,composite:"accumulate"}),t?this.flow.removeEventListener("animationsfinish",this._remove):this.flow.addEventListener("animationsfinish",this._remove,{once:!0})}}}class I extends F{constructor(t,e,i,n){super(t.flow,i,n),this.section=t,this.value=e,this.el=i}}class L extends I{constructor(t,e,i,n,s){var r,o;let l=((null==(o=null==(r=t.flow.digits)?void 0:r[n])?void 0:o.max)??9)+1,h=Array.from({length:l}).map((t,e)=>{let n=a("span",{className:"digit__num"},[document.createTextNode(String(e))]);return e!==i&&n.setAttribute("inert",""),n.style.setProperty("--n",String(e)),n}),u=a("span",{part:`digit ${e}-digit`,className:"digit"},h);u.style.setProperty("--current",String(i)),u.style.setProperty("--length",String(l)),super(t,i,u,s),this.pos=n,this._onAnimationsFinish=()=>{this.el.classList.remove("is-spinning")},this._numbers=h,this.length=l}willUpdate(t){let e=this.el.getBoundingClientRect();this._prevValue=this.value;let i=e[this.section.justify]-t[this.section.justify],n=e.width/2;this._prevCenter="left"===this.section.justify?i+n:i-n}update(t){this.el.style.setProperty("--current",String(t)),this._numbers.forEach((e,i)=>i===t?e.removeAttribute("inert"):e.setAttribute("inert","")),this.value=t}didUpdate(t){let e=this.el.getBoundingClientRect(),i=e[this.section.justify]-t[this.section.justify],n=e.width/2,s="left"===this.section.justify?i+n:i-n,r=this._prevCenter-s;r&&this.el.animate({transform:[`translateX(${r}px)`,"none"]},{...this.flow.transformTiming,composite:"accumulate"});let a=this.getDelta();a&&(this.el.classList.add("is-spinning"),this.el.animate({[v]:[-a,0]},{...this.flow.spinTiming??this.flow.transformTiming,composite:"accumulate"}),this.flow.addEventListener("animationsfinish",this._onAnimationsFinish,{once:!0}))}getDelta(){var t;if(this.flow.plugins)for(let e of this.flow.plugins){let i=null==(t=e.getDelta)?void 0:t.call(e,this.value,this._prevValue,this);if(null!=i)return i}let e=this.value-this._prevValue,i=this.flow.computedTrend||Math.sign(e);return i<0&&this.value>this._prevValue?this.value-this.length-this._prevValue:i>0&&this.value<this._prevValue?this.length-this._prevValue+this.value:e}}class N extends I{constructor(t,e,i,n){let s=a("span",{className:"symbol__value",textContent:i});super(t,i,a("span",{part:`symbol ${e}`,className:"symbol"},[s]),n),this.type=e,this._children=new Map,this._onChildRemove=t=>()=>{this._children.delete(t)},this._children.set(i,new F(this.flow,s,{onRemove:this._onChildRemove(i)}))}willUpdate(t){if("decimal"===this.type)return;let e=this.el.getBoundingClientRect();this._prevOffset=e[this.section.justify]-t[this.section.justify]}update(t){if(this.value!==t){let e=this._children.get(this.value);e&&(e.present=!1);let i=this._children.get(t);if(i)i.present=!0;else{let e=a("span",{className:"symbol__value",textContent:t});this.el.appendChild(e),this._children.set(t,new F(this.flow,e,{animateIn:!0,onRemove:this._onChildRemove(t)}))}}this.value=t}didUpdate(t){if("decimal"===this.type)return;let e=this.el.getBoundingClientRect()[this.section.justify]-t[this.section.justify],i=this._prevOffset-e;i&&this.el.animate({transform:[`translateX(${i}px)`,"none"]},{...this.flow.transformTiming,composite:"accumulate"})}}let $=parseInt(null==(s=r.version.match(/^(\d+)\./))?void 0:s[1])>=19;class D extends U{attributeChangedCallback(t,e,i){this[t]=JSON.parse(i)}}D.observedAttributes=$?[]:["data","digits"],((t,e)=>{customElements.get(t)!==e&&customElements.define(t,e)})("number-flow-react",D);let O={},V=$?t=>t:JSON.stringify;function Q(t){let{transformTiming:e,spinTiming:i,opacityTiming:n,animated:s,respectMotionPreference:r,trend:a,plugins:o,...l}=t;return[{transformTiming:e,spinTiming:i,opacityTiming:n,animated:s,respectMotionPreference:r,trend:a,plugins:o},l]}class X extends r.Component{updateProperties(t){if(!this.el)return;this.el.batched=!this.props.isolate;let[e]=Q(this.props);Object.entries(e).forEach(t=>{let[e,i]=t;this.el[e]=null!=i?i:D.defaultProps[e]}),(null==t?void 0:t.onAnimationsStart)&&this.el.removeEventListener("animationsstart",t.onAnimationsStart),this.props.onAnimationsStart&&this.el.addEventListener("animationsstart",this.props.onAnimationsStart),(null==t?void 0:t.onAnimationsFinish)&&this.el.removeEventListener("animationsfinish",t.onAnimationsFinish),this.props.onAnimationsFinish&&this.el.addEventListener("animationsfinish",this.props.onAnimationsFinish)}componentDidMount(){this.updateProperties(),$&&this.el&&(this.el.digits=this.props.digits,this.el.data=this.props.data)}getSnapshotBeforeUpdate(t){if(this.updateProperties(t),t.data!==this.props.data){if(this.props.group)return this.props.group.willUpdate(),()=>{var t;return null==(t=this.props.group)?void 0:t.didUpdate()};if(!this.props.isolate){var e;return null==(e=this.el)||e.willUpdate(),()=>{var t;return null==(t=this.el)?void 0:t.didUpdate()}}}return null}componentDidUpdate(t,e,i){null==i||i()}handleRef(t){this.props.innerRef&&(this.props.innerRef.current=t),this.el=t}render(){let[t,{innerRef:e,className:i,data:n,willChange:s,isolate:a,group:o,digits:l,onAnimationsStart:h,onAnimationsFinish:u,...c}]=Q(this.props);return r.createElement("number-flow-react",{ref:this.handleRef,"data-will-change":s?"":void 0,class:i,...c,dangerouslySetInnerHTML:{__html:""},suppressHydrationWarning:!0,digits:V(l),data:V(n)})}constructor(t){super(t),this.handleRef=this.handleRef.bind(this)}}let H=r.forwardRef(function(t,e){let{value:i,locales:n,format:s,prefix:a,suffix:o,...l}=t;r.useImperativeHandle(e,()=>h.current,[]);let h=r.useRef(),u=r.useContext(J);null==u||u.useRegister(h);let c=r.useMemo(()=>n?JSON.stringify(n):"",[n]),p=r.useMemo(()=>s?JSON.stringify(s):"",[s]),d=r.useMemo(()=>{var t,e;return function(t,e,i,n){let s=e.formatToParts(t);i&&s.unshift({type:"prefix",value:i}),n&&s.push({type:"suffix",value:n});let r=[],a=[],o=[],l=[],h={},u=t=>`${t}:${h[t]=(h[t]??-1)+1}`,c="",p=!1,d=!1;for(let t of s){c+=t.value;let e="minusSign"===t.type||"plusSign"===t.type?"sign":t.type;"integer"===e?(p=!0,a.push(...t.value.split("").map(t=>({type:e,value:parseInt(t)})))):"group"===e?a.push({type:e,value:t.value}):"decimal"===e?(d=!0,o.push({type:e,value:t.value,key:u(e)})):"fraction"===e?o.push(...t.value.split("").map(t=>({type:e,value:parseInt(t),key:u(e),pos:-1-h[e]}))):(p||d?l:r).push({type:e,value:t.value,key:u(e)})}let f=[];for(let t=a.length-1;t>=0;t--){let e=a[t];f.unshift("integer"===e.type?{...e,key:u(e.type),pos:h[e.type]}:{...e,key:u(e.type)})}return{pre:r,integer:f,fraction:o,post:l,valueAsString:c,value:"string"==typeof t?parseFloat(t):t}}(i,null!=(e=O[t="".concat(c,":").concat(p)])?e:O[t]=new Intl.NumberFormat(n,s),a,o)},[i,c,p,a,o]);return r.createElement(X,{...l,group:u,data:d,innerRef:h})}),J=r.createContext(void 0)},45084:(t,e,i)=>{i.d(e,{N:()=>y});var n=i(95155),s=i(12115),r=i(57728),a=i(76168),o=i(69025),l=i(50430),h=i(7471),u=i(22258);class c extends s.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=e.offsetParent,i=(0,u.s)(t)&&t.offsetWidth||0,n=this.props.sizeRef.current;n.height=e.offsetHeight||0,n.width=e.offsetWidth||0,n.top=e.offsetTop,n.left=e.offsetLeft,n.right=i-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function p(t){let{children:e,isPresent:i,anchorX:r}=t,a=(0,s.useId)(),o=(0,s.useRef)(null),l=(0,s.useRef)({width:0,height:0,top:0,left:0,right:0}),{nonce:u}=(0,s.useContext)(h.Q);return(0,s.useInsertionEffect)(()=>{let{width:t,height:e,top:n,left:s,right:h}=l.current;if(i||!o.current||!t||!e)return;o.current.dataset.motionPopId=a;let c=document.createElement("style");return u&&(c.nonce=u),document.head.appendChild(c),c.sheet&&c.sheet.insertRule('\n          [data-motion-pop-id="'.concat(a,'"] {\n            position: absolute !important;\n            width: ').concat(t,"px !important;\n            height: ").concat(e,"px !important;\n            ").concat("left"===r?"left: ".concat(s):"right: ".concat(h),"px !important;\n            top: ").concat(n,"px !important;\n          }\n        ")),()=>{document.head.contains(c)&&document.head.removeChild(c)}},[i]),(0,n.jsx)(c,{isPresent:i,childRef:o,sizeRef:l,children:s.cloneElement(e,{ref:o})})}let d=t=>{let{children:e,initial:i,isPresent:r,onExitComplete:o,custom:h,presenceAffectsLayout:u,mode:c,anchorX:d}=t,m=(0,a.M)(f),g=(0,s.useId)(),v=!0,y=(0,s.useMemo)(()=>(v=!1,{id:g,initial:i,isPresent:r,custom:h,onExitComplete:t=>{for(let e of(m.set(t,!0),m.values()))if(!e)return;o&&o()},register:t=>(m.set(t,!1),()=>m.delete(t))}),[r,m,o]);return u&&v&&(y={...y}),(0,s.useMemo)(()=>{m.forEach((t,e)=>m.set(e,!1))},[r]),s.useEffect(()=>{r||m.size||!o||o()},[r]),"popLayout"===c&&(e=(0,n.jsx)(p,{isPresent:r,anchorX:d,children:e})),(0,n.jsx)(l.t.Provider,{value:y,children:e})};function f(){return new Map}var m=i(14905);let g=t=>t.key||"";function v(t){let e=[];return s.Children.forEach(t,t=>{(0,s.isValidElement)(t)&&e.push(t)}),e}let y=t=>{let{children:e,custom:i,initial:l=!0,onExitComplete:h,presenceAffectsLayout:u=!0,mode:c="sync",propagate:p=!1,anchorX:f="left"}=t,[y,b]=(0,m.xQ)(p),w=(0,s.useMemo)(()=>v(e),[e]),A=p&&!y?[]:w.map(g),x=(0,s.useRef)(!0),_=(0,s.useRef)(w),E=(0,a.M)(()=>new Map),[C,S]=(0,s.useState)(w),[R,M]=(0,s.useState)(w);(0,o.E)(()=>{x.current=!1,_.current=w;for(let t=0;t<R.length;t++){let e=g(R[t]);A.includes(e)?E.delete(e):!0!==E.get(e)&&E.set(e,!1)}},[R,A.length,A.join("-")]);let P=[];if(w!==C){let t=[...w];for(let e=0;e<R.length;e++){let i=R[e],n=g(i);A.includes(n)||(t.splice(e,0,i),P.push(i))}return"wait"===c&&P.length&&(t=P),M(v(t)),S(w),null}let{forceRender:k}=(0,s.useContext)(r.L);return(0,n.jsx)(n.Fragment,{children:R.map(t=>{let e=g(t),s=(!p||!!y)&&(w===R||A.includes(e));return(0,n.jsx)(d,{isPresent:s,initial:(!x.current||!!l)&&void 0,custom:i,presenceAffectsLayout:u,mode:c,onExitComplete:s?void 0:()=>{if(!E.has(e))return;E.set(e,!0);let t=!0;E.forEach(e=>{e||(t=!1)}),t&&(null==k||k(),M(_.current),p&&(null==b||b()),h&&h())},anchorX:f,children:t},e)})})}},75214:(t,e,i)=>{i.d(e,{A:()=>C});var n=["x","y","z"],s=function(t){Object.assign(this,{uniforms:{},geometry:{vertices:[{x:0,y:0,z:0}]},mode:0,modifiers:{},attributes:[],multiplier:1,buffers:[]}),Object.assign(this,t),this.prepareProgram(),this.prepareUniforms(),this.prepareAttributes()};s.prototype.compileShader=function(t,e){var i=this.gl.createShader(t);return this.gl.shaderSource(i,e),this.gl.compileShader(i),i},s.prototype.prepareProgram=function(){var t=this.gl,e=this.vertex,i=this.fragment,n=t.createProgram();t.attachShader(n,this.compileShader(35633,e)),t.attachShader(n,this.compileShader(35632,i)),t.linkProgram(n),t.useProgram(n),this.program=n},s.prototype.prepareUniforms=function(){for(var t=Object.keys(this.uniforms),e=0;e<t.length;e+=1){var i=this.gl.getUniformLocation(this.program,t[e]);this.uniforms[t[e]].location=i}},s.prototype.prepareAttributes=function(){void 0!==this.geometry.vertices&&this.attributes.push({name:"aPosition",size:3}),void 0!==this.geometry.normal&&this.attributes.push({name:"aNormal",size:3}),this.attributeKeys=[];for(var t=0;t<this.attributes.length;t+=1)this.attributeKeys.push(this.attributes[t].name),this.prepareAttribute(this.attributes[t])},s.prototype.prepareAttribute=function(t){for(var e=this.geometry,i=this.multiplier,s=e.vertices,r=e.normal,a=new Float32Array(i*s.length*t.size),o=0;o<i;o+=1)for(var l=t.data&&t.data(o,i),h=o*s.length*t.size,u=0;u<s.length;u+=1)for(var c=0;c<t.size;c+=1){var p=this.modifiers[t.name];a[h]=void 0!==p?p(l,u,c,this):"aPosition"===t.name?s[u][n[c]]:"aNormal"===t.name?r[u][n[c]]:l[c],h+=1}this.attributes[this.attributeKeys.indexOf(t.name)].data=a,this.prepareBuffer(this.attributes[this.attributeKeys.indexOf(t.name)])},s.prototype.prepareBuffer=function(t){var e=t.data,i=t.name,n=t.size,s=this.gl.createBuffer();this.gl.bindBuffer(34962,s),this.gl.bufferData(34962,e,35044);var r=this.gl.getAttribLocation(this.program,i);this.gl.enableVertexAttribArray(r),this.gl.vertexAttribPointer(r,n,5126,!1,0,0),this.buffers[this.attributeKeys.indexOf(t.name)]={buffer:s,location:r,size:n}},s.prototype.render=function(t){var e=this,i=this.uniforms,n=this.multiplier,s=this.gl;s.useProgram(this.program);for(var r=0;r<this.buffers.length;r+=1){var a=this.buffers[r],o=a.location,l=a.buffer,h=a.size;s.enableVertexAttribArray(o),s.bindBuffer(34962,l),s.vertexAttribPointer(o,h,5126,!1,0,0)}Object.keys(t).forEach(function(e){i[e].value=t[e].value}),Object.keys(i).forEach(function(t){var n=i[t];e.uniformMap[n.type](n.location,n.value)}),s.drawArrays(this.mode,0,n*this.geometry.vertices.length),this.onRender&&this.onRender(this)},s.prototype.destroy=function(){for(var t=0;t<this.buffers.length;t+=1)this.gl.deleteBuffer(this.buffers[t].buffer);this.gl.deleteProgram(this.program),this.gl=null};var r=function(t){var e=this,i=t||{},n=i.canvas;void 0===n&&(n=document.querySelector("canvas"));var s=i.context;void 0===s&&(s={});var r=i.contextType;void 0===r&&(r="experimental-webgl");var a=i.settings;void 0===a&&(a={});var o=n.getContext(r,Object.assign({alpha:!1,antialias:!1},s));Object.assign(this,{gl:o,canvas:n,uniforms:{},instances:new Map,shouldRender:!0}),Object.assign(this,{devicePixelRatio:1,clearColor:[1,1,1,1],position:{x:0,y:0,z:2},clip:[.001,100]}),Object.assign(this,a),this.uniformMap={float:function(t,e){return o.uniform1f(t,e)},vec2:function(t,e){return o.uniform2fv(t,e)},vec3:function(t,e){return o.uniform3fv(t,e)},vec4:function(t,e){return o.uniform4fv(t,e)},mat2:function(t,e){return o.uniformMatrix2fv(t,!1,e)},mat3:function(t,e){return o.uniformMatrix3fv(t,!1,e)},mat4:function(t,e){return o.uniformMatrix4fv(t,!1,e)}},o.enable(o.DEPTH_TEST),o.depthFunc(o.LEQUAL),!1===o.getContextAttributes().alpha&&(o.clearColor.apply(o,this.clearColor),o.clearDepth(1)),this.onSetup&&this.onSetup(o),window.addEventListener("resize",function(){return e.resize()}),this.resize(),this.render()};r.prototype.resize=function(){var t=this.gl,e=this.canvas,i=this.devicePixelRatio,n=this.position;e.width=e.clientWidth*i,e.height=e.clientHeight*i;var s=t.drawingBufferWidth,r=t.drawingBufferHeight,a=s/r;t.viewport(0,0,s,r);var o=Math.tan(Math.PI/180*22.5),l=[1,0,0,0,0,1,0,0,0,0,1,0,n.x,n.y,-((a<1?1:a)*n.z),1];this.uniforms.uProjectionMatrix={type:"mat4",value:[.5/o,0,0,0,0,a/o*.5,0,0,0,0,-(this.clip[1]+this.clip[0])/(this.clip[1]-this.clip[0]),-1,0,0,-2*this.clip[1]*(this.clip[0]/(this.clip[1]-this.clip[0])),0]},this.uniforms.uViewMatrix={type:"mat4",value:[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1]},this.uniforms.uModelMatrix={type:"mat4",value:l}},r.prototype.toggle=function(t){t!==this.shouldRender&&(this.shouldRender=void 0!==t?t:!this.shouldRender,this.shouldRender&&this.render())},r.prototype.render=function(){var t=this;this.gl.clear(16640),this.instances.forEach(function(e){e.render(t.uniforms)}),this.onRender&&this.onRender(this),this.shouldRender&&requestAnimationFrame(function(){return t.render()})},r.prototype.add=function(t,e){void 0===e&&(e={uniforms:{}}),void 0===e.uniforms&&(e.uniforms={}),Object.assign(e.uniforms,JSON.parse(JSON.stringify(this.uniforms))),Object.assign(e,{gl:this.gl,uniformMap:this.uniformMap});var i=new s(e);return this.instances.set(t,i),i},r.prototype.remove=function(t){var e=this.instances.get(t);void 0!==e&&(e.destroy(),this.instances.delete(t))},r.prototype.destroy=function(){var t=this;this.instances.forEach(function(e,i){e.destroy(),t.instances.delete(i)}),this.toggle(!1)};var a="theta",o="mapSamples",l="mapBrightness",h="baseColor",u="markerColor",c="glowColor",p="markers",d="diffuse",f="devicePixelRatio",m="dark",g="offset",v="scale",y="opacity",b="mapBaseBrightness",w={phi:"A",[a]:"B",[o]:"l",[l]:"E",[h]:"R",[u]:"S",[c]:"y",[d]:"F",[m]:"G",[g]:"x",[v]:"C",[y]:"H",[b]:"I"},{PI:A,sin:x,cos:_}=Math,E=t=>[].concat(...t.map(t=>{let[e,i]=t.location;e=e*A/180,i=i*A/180-A;let n=_(e);return[-n*_(i),x(e),n*x(i),t.size]}),[0,0,0,0]),C=(t,e)=>{let i=(t,i,n)=>({type:t,value:void 0===e[i]?n:e[i]}),n=t.getContext("webgl")?"webgl":"experimental-webgl",s=new r({canvas:t,contextType:n,context:{alpha:!0,stencil:!1,antialias:!0,depth:!1,preserveDrawingBuffer:!1,...e.context},settings:{[f]:e[f]||1,onSetup:t=>{let e=t.RGB,i=t.UNSIGNED_BYTE,n=t.TEXTURE_2D,s=t.createTexture();t.bindTexture(n,s),t.texImage2D(n,0,e,1,1,0,e,i,new Uint8Array([0,0,0,0]));let r=new Image;r.onload=()=>{t.bindTexture(n,s),t.texImage2D(n,0,e,e,i,r),t.generateMipmap(n);let a=t.getParameter(t.CURRENT_PROGRAM),o=t.getUniformLocation(a,"J");t.texParameteri(n,t.TEXTURE_MIN_FILTER,t.NEAREST),t.texParameteri(n,t.TEXTURE_MAG_FILTER,t.NEAREST),t.uniform1i(o,0)},r.src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAQAAAACAAQAAAADMzoqnAAAAAXNSR0IArs4c6QAABA5JREFUeNrV179uHEUAx/Hf3JpbF+E2VASBsmVKTBcpKJs3SMEDcDwBiVJAAewYEBUivIHT0uUBIt0YCovKD0CRjUC4QfHYh8hYXu+P25vZ2Zm9c66gMd/GJ/tz82d3bk8GN4SrByYF2366FNTACIAkivVAAazQdnf3MvAlbNUQfOPAdQDvSAimMWhwy4I2g4SU+Kp04ISLpPBAKLxPyic3O/CCi+Y7rUJbiodcpDOFY7CgxCEXmdYD2EYK2s5lApOx5pEDDYCUwM1XdJUwBV11QQMg59kePSCaPAASQMEL2hwo6TJFgxpg+TgC2ymXPbuvc40awr3D1QCFfbH9kcoqAOkZozpQo0aqAGQRKCog/+tjkgbNFEtg2FffBvBGlSxHoAaAa1u6X4PBAwDiR8FFsrQgeUhfJTSALaB9jy5NCybJPn1SVFiWk7ywN+KzhH1aKAuydhGkbEF4lWohLXDXavlyFgHY7LBnLRdlAP6BS5Cc8RfVDXbkwN/oIvmY+6obbNeBP0JwTuMGu9gTzy1Q4RS/cWpfzszeYwd+CAFrtBW/Hur0gLbJGlD+/OjVwe/drfBxkbbg63dndEDfiEBlAd7ac0BPe1D6Jd8dfbLH+RI0OzseFB5s01/M+gMdAeluLOCAuaUA9Lezo/vSgXoCX9rtEiXnp7Q1W/CNyWcd8DXoS6jH/YZ5vAJEWY2dXFQe2TUgaFaNejCzJ98g6HnlVrsE58sDcYqg+9XY75fPqdoh/kRQWiXKg8MWlJQxUFMPjqnyujhFBE7UxIMjyszk0QwQlFsezImsyvUYYYVED2pk6m0Tg8T04Fwjk2kdAwSACqlM6gRRt3vQYAFGX0Ah7Ebx1H+MDRI5ui0QldH4j7FGcm90XdxD2Jg1AOEAVAKhEFXSn4cKUELurIAKwJ3MArypPscQaLhJFICJ0ohjDySAdH8AhDtCiTuMycH8CXzhH9jUACAO5uMhoAwA5i+T6WAKmmAqnLy80wxHqIPFYpqCwxGaYLt4Dyievg5kEoVEUAhs6pqKgFtDQYOuaXypaWKQfIuwwoGSZgfLsu/XAtI8cGN+h7Cc1A5oLOMhwlIPXuhu48AIvsSBkvtV9wsJRKCyYLfq5lTrQMFd1a262oqBck9K1V0YjQg0iEYYgpS1A9GlXQV5cykwm4A7BzVsxQqo7E+zCegO7Ma7yKgsuOcfKbMBwLC8wvVNYDsANYalEpOAa6zpWjTeMKGwEwC1CiQewJc5EKfgy7GmRAZA4vUVGwE2dPM/g0xuAInE/yG5aZ8ISxWGfYigUVbdyBElTHh2uCwGdfCkOLGgQVBh3Ewp+/QK4CDlR5Ws/Zf7yhCf8pH7vinWAvoVCQ6zz0NX5V/6GkAVV+2/5qsJ/gU8bsxpM8IeAQAAAABJRU5ErkJggg=="}}});return s.add("",{vertex:"attribute vec3 aPosition;uniform mat4 uProjectionMatrix;uniform mat4 uModelMatrix;uniform mat4 uViewMatrix;void main(){gl_Position=uProjectionMatrix*uModelMatrix*uViewMatrix*vec4(aPosition,1.);}",fragment:"precision highp float;uniform vec2 t,x;uniform vec3 R,S,y;uniform vec4 z[64];uniform float A,B,l,C,D,E,F,G,H,I;uniform sampler2D J;float K=1./l;mat3 L(float a,float b){float c=cos(a),d=cos(b),e=sin(a),f=sin(b);return mat3(d,f*e,-f*c,0.,c,e,f,d*-e,d*c);}vec3 w(vec3 c,out float v){c=c.xzy;float p=max(2.,floor(log2(2.236068*l*3.141593*(1.-c.z*c.z))*.72021));vec2 g=floor(pow(1.618034,p)/2.236068*vec2(1.,1.618034)+.5),d=fract((g+1.)*.618034)*6.283185-3.883222,e=-2.*g,f=vec2(atan(c.y,c.x),c.z-1.),q=floor(vec2(e.y*f.x-d.y*(f.y*l+1.),-e.x*f.x+d.x*(f.y*l+1.))/(d.x*e.y-e.x*d.y));float n=3.141593;vec3 r;for(float h=0.;h<4.;h+=1.){vec2 s=vec2(mod(h,2.),floor(h*.5));float j=dot(g,q+s);if(j>l)continue;float a=j,b=0.;if(a>=524288.)a-=524288.,b+=.803894;if(a>=262144.)a-=262144.,b+=.901947;if(a>=131072.)a-=131072.,b+=.950973;if(a>=65536.)a-=65536.,b+=.475487;if(a>=32768.)a-=32768.,b+=.737743;if(a>=16384.)a-=16384.,b+=.868872;if(a>=8192.)a-=8192.,b+=.934436;if(a>=4096.)a-=4096.,b+=.467218;if(a>=2048.)a-=2048.,b+=.733609;if(a>=1024.)a-=1024.,b+=.866804;if(a>=512.)a-=512.,b+=.433402;if(a>=256.)a-=256.,b+=.216701;if(a>=128.)a-=128.,b+=.108351;if(a>=64.)a-=64.,b+=.554175;if(a>=32.)a-=32.,b+=.777088;if(a>=16.)a-=16.,b+=.888544;if(a>=8.)a-=8.,b+=.944272;if(a>=4.)a-=4.,b+=.472136;if(a>=2.)a-=2.,b+=.236068;if(a>=1.)a-=1.,b+=.618034;float k=fract(b)*6.283185,i=1.-2.*j*K,m=sqrt(1.-i*i);vec3 o=vec3(cos(k)*m,sin(k)*m,i);float u=length(c-o);if(u<n)n=u,r=o;}v=n;return r.xzy;}void main(){vec2 b=(gl_FragCoord.xy/t*2.-1.)/C-x*vec2(1.,-1.)/t;b.x*=t.x/t.y;float c=dot(b,b);vec4 M=vec4(0.);float m=0.;if(c<=.64){for(int d=0;d<2;d++){vec4 e=vec4(0.);float a;vec3 u=vec3(0.,0.,1.),f=normalize(vec3(b,sqrt(.64-c)));f.z*=d>0?-1.:1.,u.z*=d>0?-1.:1.;vec3 g=f*L(B,A),h=w(g,a);float n=asin(h.y),i=acos(-h.x/cos(n));i=h.z<0.?-i:i;float N=max(texture2D(J,vec2(i*.5/3.141593,-(n/3.141593+.5))).x,I),O=smoothstep(8e-3,0.,a),j=dot(f,u),v=pow(j,F)*E,o=N*O*v,T=mix((1.-o)*pow(j,.4),o,G)+.1;e+=vec4(R*T,1.);int U=int(D);float p=0.;for(int k=0;k<64;k++){if(k>=U)break;vec4 q=z[k];vec3 r=q.xyz,P=r-g;float s=q.w;if(dot(P,P)>s*s*4.)continue;vec3 V=w(r,a);a=length(V-g),a<s?p+=smoothstep(s*.5,0.,a):0.;}p=min(1.,p*v),e.xyz=mix(e.xyz,S,p),e.xyz+=pow(1.-j,4.)*y,M+=e*(1.+(d>0?-H:H))/2.;}m=pow(dot(normalize(vec3(-b,sqrt(1.-c))),vec3(0.,0.,1.)),4.)*smoothstep(0.,1.,.2/(c-.64));}else{float Q=sqrt(.2/(c-.64));m=smoothstep(.5,1.,Q/(Q+1.));}gl_FragColor=M+vec4(m*y,m);}",uniforms:{t:{type:"vec2",value:[e.width,e.height]},A:i("float","phi"),B:i("float",a),l:i("float",o),E:i("float",l),I:i("float",b),R:i("vec3",h),S:i("vec3",u),F:i("float",d),y:i("vec3",c),G:i("float",m),z:{type:"vec4",value:E(e[p])},D:{type:"float",value:e[p].length},x:i("vec2",g,[0,0]),C:i("float",v,1),H:i("float",y,1)},mode:4,geometry:{vertices:[{x:-100,y:100,z:0},{x:-100,y:-100,z:0},{x:100,y:100,z:0},{x:100,y:-100,z:0},{x:-100,y:-100,z:0},{x:100,y:100,z:0}]},onRender:({uniforms:t})=>{let i={};if(e.onRender){for(let n in i=e.onRender(i)||i,w)void 0!==i[n]&&(t[w[n]].value=i[n]);void 0!==i[p]&&(t.z.value=E(i[p]),t.D.value=i[p].length),i.width&&i.height&&(t.t.value=[i.width,i.height])}}}),s}},79017:(t,e,i)=>{i.d(e,{W:()=>a});var n=i(12115),s=i(80793);let r={some:0,all:1};function a(t,{root:e,margin:i,amount:o,once:l=!1,initial:h=!1}={}){let[u,c]=(0,n.useState)(h);return(0,n.useEffect)(()=>{if(!t.current||l&&u)return;let n={root:e&&e.current||void 0,margin:i,amount:o};return function(t,e,{root:i,margin:n,amount:a="some"}={}){let o=(0,s.K)(t),l=new WeakMap,h=new IntersectionObserver(t=>{t.forEach(t=>{let i=l.get(t.target);if(!!i!==t.isIntersecting)if(t.isIntersecting){let i=e(t.target,t);"function"==typeof i?l.set(t.target,i):h.unobserve(t.target)}else"function"==typeof i&&(i(t),l.delete(t.target))})},{root:i,rootMargin:n,threshold:"number"==typeof a?a:r[a]});return o.forEach(t=>h.observe(t)),()=>h.disconnect()}(t.current,()=>(c(!0),l?void 0:()=>c(!1)),n)},[e,t,i,l,o]),u}}}]);