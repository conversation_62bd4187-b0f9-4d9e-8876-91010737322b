(()=>{var e={};e.id=8443,e.ids=[8443],e.modules={899:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var s=r(60687),i=r(93613),o=r(45583),n=r(24413),a=(r(11437),r(29523)),d=r(44493),l=(r(15079),r(56003)),c=r(41862),u=r(43210),p=r(63641);function m(){let{data:e,isLoading:t,error:r,refetch:m}=(0,l.$x)(),{allModels:x}=(0,p.xE)(),[h,f]=(0,u.useState)("anthropic/claude-sonnet-4-20250514"),[g,b]=(0,u.useState)(!1),v=(0,u.useMemo)(()=>(e?.models?.filter(e=>null!==e.input_cost_per_million_tokens&&void 0!==e.input_cost_per_million_tokens&&null!==e.output_cost_per_million_tokens&&void 0!==e.output_cost_per_million_tokens)||[]).map(e=>({...e,display_name:x.find(t=>t.id===e.short_name)?.label,priority:x.find(t=>t.id===e.short_name)?.priority,requiresSubscription:x.find(t=>t.id===e.short_name)?.requiresSubscription})).sort((e,t)=>e.requiresSubscription!==t.requiresSubscription?e.requiresSubscription?-1:1:(e.priority??0)!==(t.priority??0)?(t.priority??0)-(e.priority??0):(e.display_name??e.id).localeCompare(t.display_name??t.id)),[e?.models,x]);return v.find(e=>e.id===h),t?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,s.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,s.jsx)(c.A,{className:"w-8 h-8 animate-spin text-blue-500"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Loading pricing data..."})]})}):r?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,s.jsxs)("div",{className:"max-w-md text-center space-y-4",children:[(0,s.jsx)(i.A,{className:"w-12 h-12 text-red-500 mx-auto"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-foreground",children:"Pricing Unavailable"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:r instanceof Error?r.message:"Failed to fetch model pricing"})]}),(0,s.jsx)(a.$,{onClick:()=>m(),size:"sm",children:"Try Again"})]})}):(0,s.jsxs)("div",{className:"space-y-8 p-8 max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Token Pricing"}),(0,s.jsx)("p",{className:"text-lg text-muted-foreground max-w-3xl",children:"Understand how tokens work, explore pricing for AI models, and find the right plan for your needs."})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsxs)(d.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(o.A,{className:"w-5 h-5 text-blue-500"}),"Understanding Tokens & Compute"]})}),(0,s.jsx)(d.Wu,{children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"Tokens are the fundamental units that AI models use to process text - the more complex or lengthy your task, the more tokens it requires. Compute usage is measured by both input tokens (your prompts and context) and output tokens (the AI's responses), with different models having varying computational requirements and costs per token."})})]}),(0,s.jsxs)(d.Zp,{children:[(0,s.jsx)(d.aR,{children:(0,s.jsxs)(d.ZB,{className:"flex items-center gap-2",children:[(0,s.jsx)(n.A,{className:"w-5 h-5 text-green-500"}),"How does pricing work?"]})}),(0,s.jsx)(d.Wu,{children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"Usage costs are calculated based on token consumption from AI model interactions. We apply a 50% markup over direct model provider costs to maintain our platform and services. Your total cost depends on the specific model used and the number of tokens processed for both input (prompts, context) and output (generated responses)."})})]}),!1,(0,s.jsxs)(d.Zp,{children:[(0,s.jsxs)(d.aR,{children:[(0,s.jsx)(d.ZB,{children:"Compute Pricing by Model"}),(0,s.jsx)(d.BT,{children:"Detailed pricing information for available AI models. We apply a 50% markup on direct LLM provider costs to maintain our service and generate profit."})]}),(0,s.jsx)(d.Wu,{children:(0,s.jsxs)("div",{className:"bg-card border border-border rounded-lg",children:[(0,s.jsx)("div",{className:"px-6 py-4 border-b border-border",children:(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm font-medium text-muted-foreground",children:[(0,s.jsx)("div",{className:"col-span-1",children:"Model"}),(0,s.jsx)("div",{className:"col-span-1 text-center",children:"Input Cost"}),(0,s.jsx)("div",{className:"col-span-1 text-center",children:"Output Cost"})]})}),(0,s.jsx)("div",{className:"divide-y divide-border",children:v.map((e,t)=>(0,s.jsx)("div",{className:`px-6 py-4 hover:bg-muted/50 transition-colors duration-150 ${h===e.id?"bg-blue-50 dark:bg-blue-950/20 border-l-4 border-l-blue-500":""}`,children:(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4 items-center",children:[(0,s.jsx)("div",{className:"col-span-1",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"}),(0,s.jsx)("div",{className:"min-w-0",children:(0,s.jsx)("div",{className:"font-medium text-foreground truncate",children:e.display_name??e.id})})]})}),(0,s.jsx)("div",{className:"col-span-1 text-center",children:(0,s.jsx)("div",{className:"space-y-1",children:null!==e.input_cost_per_million_tokens&&void 0!==e.input_cost_per_million_tokens?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"font-semibold text-foreground",children:["$",e.input_cost_per_million_tokens.toFixed(2)]}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:"per 1M tokens"})]}):(0,s.jsx)("div",{className:"font-semibold text-muted-foreground",children:"—"})})}),(0,s.jsx)("div",{className:"col-span-1 text-center",children:(0,s.jsx)("div",{className:"space-y-1",children:null!==e.output_cost_per_million_tokens&&void 0!==e.output_cost_per_million_tokens?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"font-semibold text-foreground",children:["$",e.output_cost_per_million_tokens.toFixed(2)]}),(0,s.jsx)("div",{className:"text-xs text-muted-foreground",children:"per 1M tokens"})]}):(0,s.jsx)("div",{className:"font-semibold text-muted-foreground",children:"—"})})})]})},e.id))})]})})]})]})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},5445:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\model-pricing\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\model-pricing\\page.tsx","default")},6126:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.default,__next_app__:()=>c,pages:()=>l,routeModule:()=>u,tree:()=>d});var s=r(65239),i=r(48088),o=r(31369),n=r(30893),a={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>n[e]);r.d(t,a);let d={children:["",{children:["(dashboard)",{children:["model-pricing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5445)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\model-pricing\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,33532)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,88524))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,93595)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,88524))).default(e)],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\model-pricing\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(dashboard)/model-pricing/page",pathname:"/model-pricing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},15079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>u,eb:()=>m,gC:()=>p,l6:()=>l,yv:()=>c});var s=r(60687);r(43210);var i=r(22670),o=r(78272),n=r(13964),a=r(3589),d=r(4780);function l({...e}){return(0,s.jsx)(i.bL,{"data-slot":"select",...e})}function c({...e}){return(0,s.jsx)(i.WT,{"data-slot":"select-value",...e})}function u({className:e,size:t="default",children:r,...n}){return(0,s.jsxs)(i.l9,{"data-slot":"select-trigger","data-size":t,className:(0,d.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...n,children:[r,(0,s.jsx)(i.In,{asChild:!0,children:(0,s.jsx)(o.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:t,position:r="popper",...o}){return(0,s.jsx)(i.ZL,{children:(0,s.jsxs)(i.UC,{"data-slot":"select-content",className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...o,children:[(0,s.jsx)(x,{}),(0,s.jsx)(i.LM,{className:(0,d.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(h,{})]})})}function m({className:e,children:t,...r}){return(0,s.jsxs)(i.q7,{"data-slot":"select-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(i.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(i.p4,{children:t})]})}function x({className:e,...t}){return(0,s.jsx)(i.PP,{"data-slot":"select-scroll-up-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(a.A,{className:"size-4"})})}function h({className:e,...t}){return(0,s.jsx)(i.wn,{"data-slot":"select-scroll-down-button",className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(o.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44261:(e,t,r)=>{Promise.resolve().then(r.bind(r,5445))},45583:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57413:(e,t,r)=>{Promise.resolve().then(r.bind(r,899))},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63641:(e,t,r)=>{"use strict";r.d(t,{Zo:()=>m,un:()=>p,L6:()=>x,ve:()=>u,WY:()=>c,Il:()=>f,uv:()=>g,xE:()=>v});var s=r(88018),i=r(43210),o=r(5475),n=r(22372),a=r(62185),d=r(94828);let l=(0,n.GQ)(d._1.available,a.cL,{staleTime:3e5,refetchOnWindowFocus:!1,retry:2,select:e=>({...e,models:[...e.models].sort((e,t)=>e.display_name.localeCompare(t.display_name))})}),c="suna-preferred-model-v2",u="customModels",p="claude-sonnet-4",m="claude-sonnet-4",x={"claude-sonnet-4":{tier:"free",priority:100,recommended:!0,lowQuality:!1},"gemini-flash-2.5":{tier:"free",priority:70,recommended:!1,lowQuality:!1},qwen3:{tier:"free",priority:60,recommended:!1,lowQuality:!1},"sonnet-3.7":{tier:"premium",priority:99,recommended:!1,lowQuality:!1},"grok-4":{tier:"premium",priority:98,recommended:!1,lowQuality:!1},"google/gemini-2.5-pro":{tier:"premium",priority:97,recommended:!1,lowQuality:!1},"gpt-4.1":{tier:"premium",priority:96,recommended:!1,lowQuality:!1},"sonnet-3.5":{tier:"premium",priority:90,recommended:!1,lowQuality:!1},"gpt-4o":{tier:"premium",priority:88,recommended:!1,lowQuality:!1},"gemini-2.5-flash:thinking":{tier:"premium",priority:84,recommended:!1,lowQuality:!1},"deepseek/deepseek-chat-v3-0324":{tier:"premium",priority:75,recommended:!1,lowQuality:!1}},h=(e,t)=>!!(0,o.Jn)()||"active"===e||!t,f=e=>e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),g=()=>((0,o.Jn)(),[]),b=e=>{try{localStorage.setItem(c,e)}catch(e){console.warn("Failed to save model preference to localStorage:",e)}},v=()=>{let[e,t]=(0,i.useState)(m),[r,n]=(0,i.useState)([]),[a,d]=(0,i.useState)(!1),{data:c}=(0,s.Rs)(),{data:u,isLoading:g}=l({refetchOnMount:!1}),v=c?.status==="active"?"active":"no_subscription",y=()=>{(0,o.Jn)()};(0,i.useEffect)(()=>{y()},[]);let j=(0,i.useMemo)(()=>{let e=[];return e=!u?.models||g?[{id:m,label:"DeepSeek",requiresSubscription:!1,priority:x[m]?.priority||50},{id:p,label:"Sonnet 4",requiresSubscription:!0,priority:x[p]?.priority||100}]:u.models.map(e=>{let t=e.short_name||e.id,r=e.display_name||t;r.includes("/")&&(r=r.split("/").pop()||r),r=r.replace(/-/g," ").split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ");let s=x[t]||{};return{id:t,label:r,requiresSubscription:e?.requires_subscription||"premium"===s.tier||!1,top:s.priority>=90,priority:s.priority||0,lowQuality:s.lowQuality||!1,recommended:s.recommended||!1}}),(0,o.Jn)()&&r.length>0&&(e=[...e,...r.map(e=>({id:e.id,label:e.label||f(e.id),requiresSubscription:!1,top:!1,isCustom:!0,priority:30,lowQuality:!1,recommended:!1}))]),e.sort((e,t)=>e.recommended!==t.recommended?e.recommended?-1:1:e.priority!==t.priority?t.priority-e.priority:e.label.localeCompare(t.label))},[u,g,r]),_=(0,i.useMemo)(()=>(0,o.Jn)()?j:j.filter(e=>h(v,e.requiresSubscription)),[j,v]);(0,i.useEffect)(()=>{},[v,j,g,r,a]);let w=e=>{console.log("handleModelChange called with:",e),(0,o.Jn)()&&y();let s=(0,o.Jn)()&&r.some(t=>t.id===e),i=j.find(t=>t.id===e);if(!i&&!s){console.warn("Model not found in options:",e,j,s,r);let i=(0,o.Jn)()?p:m;t(i),b(i);return}if(!s&&!(0,o.Jn)()&&!h(v,i?.requiresSubscription??!1))return void console.warn("Model not accessible:",e);console.log("Setting selected model and saving to localStorage:",e),t(e),b(e)};return{selectedModel:e,setSelectedModel:e=>{w(e)},subscriptionStatus:v,availableModels:_,allModels:j,customModels:r,getActualModelId:e=>e,refreshCustomModels:y,canAccessModel:e=>{if((0,o.Jn)())return!0;let t=j.find(t=>t.id===e);return!!t&&h(v,t.requiresSubscription)},isSubscriptionRequired:e=>j.find(t=>t.id===e)?.requiresSubscription||!1}}},70400:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"60120624b62a67d0046abca062aae687cbb5ebc44a":()=>s.vI,"602190608cb4aada86562e5e5997e6bb44fbe95e4e":()=>s.$w,"60836a64bf90333e8dead87703a0c5a32e95fa0f8f":()=>s.gj});var s=r(67834)},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84297:e=>{"use strict";e.exports=require("async_hooks")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,5193,4267,7096,1265,3530,7156,7976,4097,3667,8188,3806,1841],()=>r(6126));module.exports=s})();