"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5360],{95360:(e,t,n)=>{let r,i;n.d(t,{L:()=>q});var o=n(12115),l=n(80793),s=n(83385);let f=new WeakMap;function c({target:e,contentRect:t,borderBoxSize:n}){f.get(e)?.forEach(r=>{r({target:e,contentSize:t,get size(){if(n){let{inlineSize:e,blockSize:t}=n[0];return{width:e,height:t}}if((0,s.x)(e)&&"getBBox"in e)return e.getBBox();return{width:e.offsetWidth,height:e.offsetHeight}}})})}function a(e){e.forEach(c)}let g=new Set;var u=n(38865),h=n(64530);let d=()=>({current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}),p=()=>({time:0,x:d(),y:d()}),m={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function v(e,t,n,r){let i=n[t],{length:o,position:l}=m[t],s=i.current,f=n.time;i.current=e[`scroll${l}`],i.scrollLength=e[`scroll${o}`]-e[`client${o}`],i.offset.length=0,i.offset[0]=0,i.offset[1]=i.scrollLength,i.progress=(0,u.q)(0,i.scrollLength,i.current);let c=r-f;i.velocity=c>50?0:(0,h.f)(i.current-s,c)}var w=n(22258);let y={start:0,center:.5,end:1};function E(e,t,n=0){let r=0;if(e in y&&(e=y[e]),"string"==typeof e){let t=parseFloat(e);e.endsWith("px")?r=t:e.endsWith("%")?e=t/100:e.endsWith("vw")?r=t/100*document.documentElement.clientWidth:e.endsWith("vh")?r=t/100*document.documentElement.clientHeight:e=t}return"number"==typeof e&&(r=t*e),n+r}let x=[0,0],W={All:[[0,0],[1,1]]};var L=n(38154),b=n(91762),O=n(98167);let B={x:0,y:0};var z=n(21116),H=n(46182);let k=new WeakMap,G=new WeakMap,S=new WeakMap,N=e=>e===document.scrollingElement?window:e;function M(e,{container:t=document.scrollingElement,...n}={}){if(!t)return z.l;let o=S.get(t);o||(o=new Set,S.set(t,o));let s=function(e,t,n,r={}){return{measure:t=>{!function(e,t=e,n){if(n.x.targetOffset=0,n.y.targetOffset=0,t!==e){let r=t;for(;r&&r!==e;)n.x.targetOffset+=r.offsetLeft,n.y.targetOffset+=r.offsetTop,r=r.offsetParent}n.x.targetLength=t===e?t.scrollWidth:t.clientWidth,n.y.targetLength=t===e?t.scrollHeight:t.clientHeight,n.x.containerLength=e.clientWidth,n.y.containerLength=e.clientHeight}(e,r.target,n),v(e,"x",n,t),v(e,"y",n,t),n.time=t,(r.offset||r.target)&&function(e,t,n){let{offset:r=W.All}=n,{target:i=e,axis:o="y"}=n,l="y"===o?"height":"width",s=i!==e?function(e,t){let n={x:0,y:0},r=e;for(;r&&r!==t;)if((0,w.s)(r))n.x+=r.offsetLeft,n.y+=r.offsetTop,r=r.offsetParent;else if("svg"===r.tagName){let e=r.getBoundingClientRect(),t=(r=r.parentElement).getBoundingClientRect();n.x+=e.left-t.left,n.y+=e.top-t.top}else if(r instanceof SVGGraphicsElement){let{x:e,y:t}=r.getBBox();n.x+=e,n.y+=t;let i=null,o=r.parentNode;for(;!i;)"svg"===o.tagName&&(i=o),o=r.parentNode;r=i}else break;return n}(i,e):B,f=i===e?{width:e.scrollWidth,height:e.scrollHeight}:"getBBox"in i&&"svg"!==i.tagName?i.getBBox():{width:i.clientWidth,height:i.clientHeight},c={width:e.clientWidth,height:e.clientHeight};t[o].offset.length=0;let a=!t[o].interpolate,g=r.length;for(let e=0;e<g;e++){let n=function(e,t,n,r){let i=Array.isArray(e)?e:x,o=0,l=0;return"number"==typeof e?i=[e,e]:"string"==typeof e&&(i=(e=e.trim()).includes(" ")?e.split(" "):[e,y[e]?e:"0"]),(o=E(i[0],n,r))-E(i[1],t)}(r[e],c[l],f[l],s[o]);a||n===t[o].interpolatorOffsets[e]||(a=!0),t[o].offset[e]=n}a&&(t[o].interpolate=(0,L.G)(t[o].offset,(0,b.Z)(r),{clamp:!1}),t[o].interpolatorOffsets=[...t[o].offset]),t[o].progress=(0,O.q)(0,1,t[o].interpolate(t[o].current))}(e,n,r)},notify:()=>t(n)}}(t,e,p(),n);if(o.add(s),!k.has(t)){let e=()=>{for(let e of o)e.measure(H.uv.timestamp);H.Gt.preUpdate(n)},n=()=>{for(let e of o)e.notify()},s=()=>H.Gt.read(e);k.set(t,s);let c=N(t);window.addEventListener("resize",s,{passive:!0}),t!==document.documentElement&&G.set(t,"function"==typeof t?(g.add(t),i||(i=()=>{let e={width:window.innerWidth,height:window.innerHeight},t={target:window,size:e,contentSize:e};g.forEach(e=>e(t))},window.addEventListener("resize",i)),()=>{g.delete(t),!g.size&&i&&(i=void 0)}):function(e,t){r||"undefined"!=typeof ResizeObserver&&(r=new ResizeObserver(a));let n=(0,l.K)(e);return n.forEach(e=>{let n=f.get(e);n||(n=new Set,f.set(e,n)),n.add(t),r?.observe(e)}),()=>{n.forEach(e=>{let n=f.get(e);n?.delete(t),n?.size||r?.unobserve(e)})}}(t,s)),c.addEventListener("scroll",s,{passive:!0}),s()}let c=k.get(t);return H.Gt.read(c,!1,!0),()=>{(0,H.WG)(c);let e=S.get(t);if(!e||(e.delete(s),e.size))return;let n=k.get(t);k.delete(t),n&&(N(t).removeEventListener("scroll",n),G.get(t)?.(),window.removeEventListener("resize",n))}}var P=n(96299);let T=new Map;function Y({source:e,container:t,...n}){let{axis:r}=n;e&&(t=e);let i=T.get(t)??new Map;T.set(t,i);let o=n.target??"self",l=i.get(o)??{},s=r+(n.offset??[]).join(",");return l[s]||(l[s]=!n.target&&(0,P.J)()?new ScrollTimeline({source:t,axis:r}):function(e){let t={value:0},n=M(n=>{t.value=100*n[e.axis].progress},e);return{currentTime:t,cancel:n}}({container:t,...n})),l[s]}function $(e,t){let n,r=()=>{let{currentTime:r}=t,i=(null===r?0:r.value)/100;n!==i&&e(i),n=i};return H.Gt.preUpdate(r,!0),()=>(0,H.WG)(r)}var A=n(76168),C=n(69025),Q=n(94449),R=n(99967);function X(e,t){(0,Q.$)(!!(!t||t.current),`You have defined a ${e} options but the provided ref is not yet hydrated, probably because it's defined higher up the tree. Try calling useScroll() in the same component as the ref, or setting its \`layoutEffect: false\` option.`)}let _=()=>({scrollX:(0,R.OQ)(0),scrollY:(0,R.OQ)(0),scrollXProgress:(0,R.OQ)(0),scrollYProgress:(0,R.OQ)(0)});function q({container:e,target:t,layoutEffect:n=!0,...r}={}){let i=(0,A.M)(_);return(n?C.E:o.useEffect)(()=>(X("target",t),X("container",e),function(e,{axis:t="y",container:n=document.scrollingElement,...r}={}){var i,o;if(!n)return z.l;let l={axis:t,container:n,...r};return"function"==typeof e?(i=e,o=l,2===i.length?M(e=>{i(e[o.axis].progress,e)},o):$(i,Y(o))):function(e,t){let n=Y(t);return e.attachTimeline({timeline:t.target?void 0:n,observe:e=>(e.pause(),$(t=>{e.time=e.duration*t},n))})}(e,l)}((e,{x:t,y:n})=>{i.scrollX.set(t.current),i.scrollXProgress.set(t.progress),i.scrollY.set(n.current),i.scrollYProgress.set(n.progress)},{...r,container:e?.current||void 0,target:t?.current||void 0})),[e,t,JSON.stringify(r.offset)]),i}}}]);