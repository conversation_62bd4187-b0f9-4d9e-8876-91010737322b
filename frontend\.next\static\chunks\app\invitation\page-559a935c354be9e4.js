(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8516],{1243:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("Triangle<PERSON>lert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>s,t:()=>i});var n=r(12115);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}function s(...e){return n.useCallback(i(...e),e)}},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(12115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:l=2,absoluteStrokeWidth:o,className:c="",children:d,iconNode:u,...f}=e;return(0,n.createElement)("svg",{ref:t,...s,width:a,height:a,stroke:r,strokeWidth:o?24*Number(l)/Number(a):l,className:i("lucide",c),...f},[...u.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(d)?d:[d]])}),o=(e,t)=>{let r=(0,n.forwardRef)((r,s)=>{let{className:o,...c}=r;return(0,n.createElement)(l,{ref:s,iconNode:t,className:i("lucide-".concat(a(e)),o),...c})});return r.displayName="".concat(e),r}},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>o,r:()=>l});var n=r(95155);r(12115);var a=r(99708),i=r(74466),s=r(59434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",node_outline:"bg-transparent border border-primary/10",node_secondary:"px-0 bg-transparent hover:opacity-60",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-lg gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-lg px-6 has-[>svg]:px-4",icon:"size-9",node_secondary:"px-0"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:r,size:i,asChild:o=!1,...c}=e,d=o?a.DX:"button";return(0,n.jsx)(d,{"data-slot":"button",className:(0,s.cn)(l({variant:r,size:i,className:t})),...c})}},34477:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{callServer:function(){return n.callServer},createServerReference:function(){return i},findSourceMapURL:function(){return a.findSourceMapURL}});let n=r(53806),a=r(31818),i=r(34979).createServerReference},35695:(e,t,r)=>{"use strict";var n=r(18999);r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}}),r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},35706:(e,t,r)=>{"use strict";r.d(t,{SubmitButton:()=>d});var n=r(95155),a=r(47650),i=r(12115),s=r(30285),l=r(55365),o=r(1243);let c={message:""};function d(e){let{children:t,formAction:r,errorMessage:d,pendingText:u="Submitting...",...f}=e,{pending:v,action:p}=(0,a.useFormStatus)(),[m,g]=(0,i.useActionState)(r,c),h=v&&p===g;return(0,n.jsxs)("div",{className:"flex flex-col gap-y-4 w-full",children:[!!(d||(null==m?void 0:m.message))&&(0,n.jsxs)(l.Fc,{variant:"destructive",className:"w-full",children:[(0,n.jsx)(o.A,{className:"h-4 w-4"}),(0,n.jsx)(l.TN,{children:d||(null==m?void 0:m.message)})]}),(0,n.jsx)("div",{children:(0,n.jsx)(s.$,{...f,type:"submit","aria-disabled":v,formAction:g,children:h?u:t})})]})}},41137:(e,t,r)=>{Promise.resolve().then(r.bind(r,85708))},55365:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>l,TN:()=>c,XL:()=>o});var n=r(95155);r(12115);var a=r(74466),i=r(59434);let s=(0,a.F)("relative w-full rounded-xl border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,...a}=e;return(0,n.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(s({variant:r}),t),...a})}function o(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"alert-title",className:(0,i.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...r})}function c(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r})}},59434:(e,t,r)=>{"use strict";r.d(t,{$3:()=>o,Hz:()=>l,W5:()=>c,cn:()=>s});var n=r(52596),a=r(81949),i=r(39688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.QP)((0,n.$)(t))}let l=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"rgba(180, 180, 180)";if(!e)return t;try{if("string"==typeof e&&e.startsWith("var(")){let t=document.createElement("div");t.style.color=e,document.body.appendChild(t);let r=window.getComputedStyle(t).color;return document.body.removeChild(t),a.formatRGBA(a.parse(r))}return a.formatRGBA(a.parse(e))}catch(e){return console.error("Color parsing failed:",e),t}},o=(e,t)=>e.startsWith("rgb")?a.formatRGBA(a.alpha(a.parse(e),t)):e;function c(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return e.length<=t?e:e.slice(0,t)+"..."}},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>s,wL:()=>d});var n=r(95155);r(12115);var a=r(59434);function i(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function s(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function l(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...r})}function o(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...r})}function c(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,n.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r})}},72305:(e,t,r)=>{"use strict";r.d(t,{U:()=>a});var n=r(34477);let a=(0,n.createServerReference)("7f8bed79c8654f95685745e61906af37f96a086236",n.callServer,void 0,n.findSourceMapURL,"createClient")},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>s});var n=r(52596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=n.$,s=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:s,defaultVariants:l}=t,o=Object.keys(s).map(e=>{let t=null==r?void 0:r[e],n=null==l?void 0:l[e];if(null===t)return null;let i=a(t)||a(n);return s[e][i]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return i(e,o,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...c}[t]):({...l,...c})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},85708:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var n=r(95155),a=r(12115),i=r(34477);let s=(0,i.createServerReference)("60b56431385a17492afc62212a7d703c72e7e723ab",i.callServer,void 0,i.findSourceMapURL,"acceptInvitation");var l=r(72305),o=r(55365),c=r(66695),d=r(35706);async function u(e){let{token:t}=e,r=await (0,l.U)(),{data:a}=await r.rpc("lookup_invitation",{lookup_invitation_token:t});return(0,n.jsx)(c.Zp,{children:(0,n.jsxs)(c.Wu,{className:"p-8 text-center flex flex-col gap-y-8",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{children:"You've been invited to join"}),(0,n.jsx)("h1",{className:"text-xl font-bold",children:a.account_name})]}),a.active?(0,n.jsxs)("form",{children:[(0,n.jsx)("input",{type:"hidden",name:"token",value:t}),(0,n.jsx)(d.SubmitButton,{formAction:s,pendingText:"Accepting invitation...",children:"Accept invitation"})]}):(0,n.jsx)(o.Fc,{variant:"destructive",children:"This invitation has been deactivated. Please contact the account owner for a new invitation."})]})})}var f=r(35695);function v(e){let{searchParams:t}=e,r=a.use(t);return r.token||(0,f.redirect)("/"),(0,n.jsx)("div",{className:"max-w-md mx-auto w-full my-12",children:(0,n.jsx)(u,{token:r.token})})}},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>l,Dc:()=>c,TL:()=>s});var n=r(12115),a=r(6101),i=r(95155);function s(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){var s;let e,l,o=(s=r,(l=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(l=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),c=function(e,t){let r={...t};for(let n in t){let a=e[n],i=t[n];/^on[A-Z]/.test(n)?a&&i?r[n]=(...e)=>{let t=i(...e);return a(...e),t}:a&&(r[n]=a):"style"===n?r[n]={...a,...i}:"className"===n&&(r[n]=[a,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==n.Fragment&&(c.ref=t?(0,a.t)(t,o):o),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:a,...s}=e,l=n.Children.toArray(a),o=l.find(d);if(o){let e=o.props.children,a=l.map(t=>t!==o?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...s,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,a):null})}return(0,i.jsx)(t,{...s,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var l=s("Slot"),o=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=o,t}function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}}},e=>{var t=t=>e(e.s=t);e.O(0,[2969,8441,1684,7358],()=>t(41137)),_N_E=e.O()}]);