"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5039],{1856:(e,t,r)=>{r.d(t,{R:()=>l});var n=r(95155),s=r(66766),a=r(51362),o=r(12115);function l(e){let{size:t=24}=e,{theme:r,systemTheme:l}=(0,a.D)(),[i,c]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{c(!0)},[]),(0,n.jsx)(s.default,{src:"/kortix-symbol.svg",alt:"Kortix",width:t,height:t,className:"".concat(i&&("dark"===r||"system"===r&&"dark"===l)?"invert":""," flex-shrink-0")})}},40416:(e,t,r)=>{r.d(t,{a:()=>s});var n=r(12115);function s(){let[e,t]=n.useState(void 0);return n.useEffect(()=>{let e=window.matchMedia("(max-width: ".concat(767,"px)")),r=()=>{t(window.innerWidth<768)};return e.addEventListener("change",r),t(window.innerWidth<768),()=>e.removeEventListener("change",r)},[]),!!e}},53732:(e,t,r)=>{r.d(t,{Z:()=>i});var n=r(12115),s=r(25731),a=r(56671),o=r(18068);let l=(e,t)=>(e||[]).filter(e=>"status"!==e.type).map(e=>({message_id:e.message_id||null,thread_id:e.thread_id||t,type:e.type||"system",is_llm_message:!!e.is_llm_message,content:e.content||"",metadata:e.metadata||"{}",created_at:e.created_at||new Date().toISOString(),updated_at:e.updated_at||new Date().toISOString(),agent_id:e.agent_id,agents:e.agents}));function i(e,t,r){let[i,c]=(0,n.useState)(null),[d,m]=(0,n.useState)("idle"),[u,p]=(0,n.useState)([]),[g,x]=(0,n.useState)(null),[f,h]=(0,n.useState)(null),b=(0,n.useRef)(null),w=(0,n.useRef)(!0),v=(0,n.useRef)(null),y=(0,n.useRef)(t),j=(0,n.useRef)(r),k=(0,n.useMemo)(()=>u.sort((e,t)=>{var r,n;return(null!=(r=e.sequence)?r:0)-(null!=(n=t.sequence)?n:0)}).reduce((e,t)=>e+t.content,""),[u]);(0,n.useEffect)(()=>{y.current=t},[t]),(0,n.useEffect)(()=>{j.current=r},[r]);let _=e=>{switch(e){case"completed":return"completed";case"stopped":return"stopped";case"failed":return"failed";default:return"error"}},N=(0,n.useCallback)(t=>{if(w.current){var r,n,s;m(t),null==(r=e.onStatusChange)||r.call(e,t),"error"===t&&f&&(null==(n=e.onError)||n.call(e,f)),["completed","stopped","failed","error","agent_not_running"].includes(t)&&(null==(s=e.onClose)||s.call(e,t))}},[e,f]),S=(0,n.useCallback)(function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:i;if(!w.current)return;let r=y.current,n=j.current;console.log("[useAgentStream] Finalizing stream for ".concat(t," on thread ").concat(r," with status: ").concat(e)),b.current&&(b.current(),b.current=null),p([]),x(null),N(e),c(null),v.current=null,r&&["completed","stopped","failed","error","agent_not_running"].includes(e)?(console.log("[useAgentStream] Refetching messages for thread ".concat(r," after finalization with status ").concat(e,".")),(0,s.VL)(r).then(e=>{w.current&&e?(console.log("[useAgentStream] Refetched ".concat(e.length," messages for thread ").concat(r,".")),n(l(e,r))):w.current||console.log("[useAgentStream] Component unmounted before messages could be set after refetch for thread ".concat(r,"."))}).catch(e=>{console.error("[useAgentStream] Error refetching messages for thread ".concat(r," after finalization:"),e),a.oR.error("Failed to refresh messages: ".concat(e.message))})):console.log("[useAgentStream] Skipping message refetch for thread ".concat(r,". Final status: ").concat(e)),t&&("completed"===e||"stopped"===e||"agent_not_running"===e)&&(0,s._L)(t).catch(e=>{console.log("[useAgentStream] Post-finalization status check for ".concat(t," failed (this might be expected if not found): ").concat(e.message))})},[i,N]),A=(0,n.useCallback)(t=>{var r,n,s;if(!w.current)return;window.lastStreamMessage=Date.now();let l=t;if(l.startsWith("data: ")&&(l=l.substring(6).trim()),!l)return;if('{"type": "status", "status": "completed", "message": "Agent run completed successfully"}'===l){console.log("[useAgentStream] Received final completion status message"),S("completed",v.current);return}if(l.includes("Run data not available for streaming")||l.includes("Stream ended with status: completed")){console.log('[useAgentStream] Detected final completion message: "'.concat(l,'", finalizing.')),S("completed",v.current);return}try{let t=JSON.parse(l);if("error"===t.status){console.error("[useAgentStream] Received error status message:",t);let n=t.message||"Unknown error occurred";h(n),a.oR.error(n,{duration:15e3}),null==(r=e.onError)||r.call(e,n);return}}catch(e){}let i=(0,o.jD)(l,null);if(!i)return void console.warn("[useAgentStream] Failed to parse streamed message:",l);let c=(0,o.jD)(i.content,{}),m=(0,o.jD)(i.metadata,{});switch("streaming"!==d&&N("streaming"),i.type){case"assistant":console.log("[useAgentStream] test a:",c.content),console.log("[useAgentStream] test a1:",m),"chunk"===m.stream_status&&c.content?(p(e=>e.concat({sequence:i.sequence,content:c.content})),null==(n=e.onAssistantChunk)||n.call(e,{content:c.content})):"complete"===m.stream_status?(p([]),x(null),i.message_id&&e.onMessage(i)):!m.stream_status&&(null==(s=e.onAssistantStart)||s.call(e),i.message_id&&e.onMessage(i));break;case"tool":x(null),i.message_id&&e.onMessage(i);break;case"status":switch(c.status_type){case"tool_started":x({role:"assistant",status_type:"tool_started",name:c.function_name,arguments:c.arguments,xml_tag_name:c.xml_tag_name,tool_index:c.tool_index});break;case"tool_completed":case"tool_failed":case"tool_error":(null==g?void 0:g.tool_index)===c.tool_index&&x(null);break;case"thread_run_end":console.log("[useAgentStream] Received thread run end status, finalizing.");break;case"finish":console.log("[useAgentStream] Received finish status:",c.finish_reason);break;case"error":console.error("[useAgentStream] Received error status message:",c.message),h(c.message||"Agent run failed"),S("error",v.current)}break;case"user":case"system":i.message_id&&e.onMessage(i);break;default:console.warn("[useAgentStream] Unhandled message type:",i.type)}},[t,r,d,g,e,S,N]),C=(0,n.useCallback)(e=>{if(!w.current)return;let t="Unknown streaming error";if("string"==typeof e?t=e:e instanceof Error?t=e.message:e instanceof Event&&"error"===e.type&&(t="Stream connection error"),console.error("[useAgentStream] Streaming error:",t,e),h(t),a.oR.error(t,{duration:15e3}),!v.current){console.warn("[useAgentStream] Stream error occurred but no agentRunId is active."),S("error");return}},[S]),R=(0,n.useCallback)(()=>{if(!w.current)return;console.log("[useAgentStream] Stream connection closed by server.");let e=v.current;if(!e){console.warn("[useAgentStream] Stream closed but no active agentRunId."),"streaming"===d||"connecting"===d?S("error"):"idle"!==d&&"completed"!==d&&"stopped"!==d&&"agent_not_running"!==d&&S("idle");return}(0,s._L)(e).then(t=>{if(w.current)if(console.log("[useAgentStream] Agent status after stream close for ".concat(e,": ").concat(t.status)),"running"===t.status)console.warn("[useAgentStream] Stream closed for ".concat(e,", but agent is still running. Finalizing with error.")),h("Stream closed unexpectedly while agent was running."),S("error",e),a.oR.warning("Stream disconnected. Agent might still be running.");else{let r=_(t.status);console.log("[useAgentStream] Stream closed for ".concat(e,", agent status is ").concat(t.status,". Finalizing stream as ").concat(r,".")),S(r,e)}}).catch(t=>{if(!w.current)return;let r=t instanceof Error?t.message:String(t);console.error("[useAgentStream] Error checking agent status for ".concat(e," after stream close: ").concat(r)),r.includes("not found")||r.includes("404")||r.includes("does not exist")?(console.log("[useAgentStream] Agent run ".concat(e," not found after stream close. Finalizing.")),S("agent_not_running",e)):S("error",e)})},[d,S]);(0,n.useEffect)(()=>(w.current=!0,()=>{w.current=!1,console.log("[useAgentStream] Unmounting or agentRunId changing. Cleaning up stream."),b.current&&(b.current(),b.current=null),m("idle"),p([]),x(null),h(null),c(null),v.current=null}),[]);let E=(0,n.useCallback)(async e=>{if(w.current){console.log("[useAgentStream] Received request to start streaming for ".concat(e)),b.current&&(console.log("[useAgentStream] Cleaning up existing stream before starting new one."),b.current(),b.current=null),p([]),x(null),h(null),N("connecting"),c(e),v.current=e;try{let t=await (0,s._L)(e);if(!w.current)return;if("running"!==t.status){console.warn("[useAgentStream] Agent run ".concat(e," is not in running state (status: ").concat(t.status,"). Cannot start stream.")),h("Agent run is not running (status: ".concat(t.status,")")),S(_(t.status)||"agent_not_running",e);return}console.log("[useAgentStream] Agent run ".concat(e," confirmed running. Setting up EventSource.")),b.current=(0,s.MA)(e,{onMessage:A,onError:C,onClose:R})}catch(r){if(!w.current)return;let t=r instanceof Error?r.message:String(r);console.error("[useAgentStream] Error initiating stream for ".concat(e,": ").concat(t)),h(t),S(t.includes("not found")||t.includes("404")||t.includes("does not exist")?"agent_not_running":"error",e)}}},[N,S,A,C,R]),z=(0,n.useCallback)(async()=>{if(w.current&&i){console.log("[useAgentStream] Stopping stream for agent run ".concat(i)),S("stopped",i);try{await (0,s.eD)(i),a.oR.success("Agent stopped.")}catch(t){let e=t instanceof Error?t.message:String(t);console.error("[useAgentStream] Error sending stop request for ".concat(i,": ").concat(e)),a.oR.error("Failed to stop agent: ".concat(e))}}},[i,S]);return{status:d,textContent:k,toolCall:g,error:f,agentRunId:i,startStreaming:E,stopStreaming:z}}},67897:(e,t,r)=>{r.d(t,{u9:()=>k});var n=r(95155),s=r(12115),a=r(66538),o=r(58832),l=r(30285),i=r(31834),c=r(84332),d=r(30257),m=r(64541),u=r(18068),p=r(1856),g=r(60760),x=r(76408),f=r(59434);let h=e=>{let{children:t,className:r,shimmerWidth:s=100,...a}=e;return(0,n.jsx)("span",{style:{"--shiny-width":"".concat(s,"px")},className:(0,f.cn)("mx-auto max-w-md text-neutral-600/70 dark:text-neutral-400/70","animate-shiny-text bg-clip-text bg-no-repeat [background-position:0_0] [background-size:var(--shiny-width)_100%] [transition:background-position_1s_cubic-bezier(.6,.6,0,1)_infinite]","bg-gradient-to-r from-transparent via-black/80 via-50% to-transparent  dark:via-white/80",r),...a,children:t})},b=[{id:1,content:"Initializing neural pathways..."},{id:2,content:"Analyzing query complexity..."},{id:3,content:"Assembling cognitive framework..."},{id:4,content:"Orchestrating thought processes..."},{id:5,content:"Synthesizing contextual understanding..."},{id:6,content:"Calibrating response parameters..."},{id:7,content:"Engaging reasoning algorithms..."},{id:8,content:"Processing semantic structures..."},{id:9,content:"Formulating strategic approach..."},{id:10,content:"Optimizing solution pathways..."},{id:11,content:"Harmonizing data streams..."},{id:12,content:"Architecting intelligent response..."},{id:13,content:"Fine-tuning cognitive models..."},{id:14,content:"Weaving narrative threads..."},{id:15,content:"Crystallizing insights..."},{id:16,content:"Preparing comprehensive analysis..."}],w=()=>{let[e,t]=(0,s.useState)(0);return(0,s.useEffect)(()=>{let e=setInterval(()=>{t(e=>e>=b.length-1?0:e+1)},1500);return()=>clearInterval(e)},[]),(0,n.jsxs)("div",{className:"flex py-2 items-center w-full",children:[(0,n.jsx)("div",{children:"✨"}),(0,n.jsx)(g.N,{children:(0,n.jsx)(x.P.div,{initial:{y:20,opacity:0,filter:"blur(8px)"},animate:{y:0,opacity:1,filter:"blur(0px)"},exit:{y:-20,opacity:0,filter:"blur(8px)"},transition:{ease:"easeInOut"},style:{position:"absolute"},className:"ml-7",children:(0,n.jsx)(h,{children:b[e].content})},b[e].id)})]})};var v=r(99856);let y=new Set(["execute-command","create-file","delete-file","full-file-rewrite","str-replace","browser-click-element","browser-close-tab","browser-drag-drop","browser-get-dropdown-options","browser-go-back","browser-input-text","browser-navigate-to","browser-scroll-down","browser-scroll-to-text","browser-scroll-up","browser-select-dropdown-option","browser-send-keys","browser-switch-tab","browser-wait","deploy","ask","complete","crawl-webpage","web-search","see-image","call-mcp-tool","execute_data_provider_call","execute_data_provider_endpoint","execute-data-provider-call","execute-data-provider-endpoint"]);function j(e,t,r,s){return e&&0!==e.length?(0,n.jsx)(c.Q,{attachments:e,onFileClick:t,showPreviews:!0,sandboxId:r,project:s}):null}let k=e=>{let{messages:t,streamingTextContent:r="",streamingToolCall:c,agentStatus:g,handleToolClick:x,handleOpenFileViewer:f,readOnly:h=!1,visibleMessages:b,streamingText:k="",isStreamingText:_=!1,currentToolCall:N,streamHookStatus:S="idle",sandboxId:A,project:C,debugMode:R=!1,isPreviewMode:E=!1,agentName:z="Suna",agentAvatar:F=(0,n.jsx)(p.R,{size:16}),emptyStateComponent:D}=e,q=(0,s.useRef)(null),I=(0,s.useRef)(null),U=(0,s.useRef)(null),[O,T]=(0,s.useState)(!1),[,M]=(0,s.useState)(!1),{session:L}=(0,m.A)(),{preloadFiles:W}=(0,d.OG)(),Z=h&&b?b:t,P=(0,s.useCallback)(function(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"smooth";null==(e=q.current)||e.scrollIntoView({behavior:t})},[]);return s.useEffect(()=>{if(!A)return;let e=[];Z.forEach(t=>{if("user"===t.type)try{let r=("string"==typeof t.content?t.content:"").match(/\[Uploaded File: (.*?)\]/g);r&&r.forEach(t=>{let r=t.match(/\[Uploaded File: (.*?)\]/);r&&r[1]&&e.push(r[1])})}catch(e){console.error("Error parsing message attachments:",e)}}),e.length>0&&(null==L?void 0:L.access_token)&&W(A,e).catch(e=>{console.error("React Query preload failed:",e)})},[Z,A,null==L?void 0:L.access_token,W]),(0,n.jsxs)(n.Fragment,{children:[0!==Z.length||r||c||k||N||"idle"!==g?(0,n.jsxs)("div",{ref:I,className:E?"flex-1 overflow-y-auto scrollbar-thin scrollbar-track-secondary/0 scrollbar-thumb-primary/10 scrollbar-thumb-rounded-full hover:scrollbar-thumb-primary/10 px-6 py-4 pb-72":"flex-1 overflow-y-auto scrollbar-thin scrollbar-track-secondary/0 scrollbar-thumb-primary/10 scrollbar-thumb-rounded-full hover:scrollbar-thumb-primary/10 px-6 py-4 pb-72 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",onScroll:()=>{if(!I.current)return;let{scrollTop:e,scrollHeight:t,clientHeight:r}=I.current,n=t-e-r>100;T(n),M(n)},children:[(0,n.jsx)("div",{className:"mx-auto max-w-3xl md:px-8 min-w-0",children:(0,n.jsxs)("div",{className:"space-y-8 min-w-0",children:[(()=>{let e=[],t=null,s=0;Z.forEach((r,n)=>{let a=r.type,o=r.message_id||"msg-".concat(n);"user"===a?(t&&(e.push(t),t=null),e.push({type:"user",messages:[r],key:o})):"assistant"===a||"tool"===a||"browser_state"===a?t&&"assistant_group"===t.type&&(()=>{if("assistant"===a){let e=t.messages.findLast(e=>"assistant"===e.type);return!e||r.agent_id===e.agent_id}return!0})()?null==t||t.messages.push(r):(t&&e.push(t),s++,t={type:"assistant_group",messages:[r],key:"assistant-group-".concat(s)}):"status"!==a&&t&&(e.push(t),t=null)}),t&&e.push(t);let o=[],l=null;if(e.forEach(e=>{"assistant_group"===e.type?l&&"assistant_group"===l.type?l.messages.push(...e.messages):(l&&o.push(l),l={...e}):(l&&(o.push(l),l=null),o.push(e))}),l&&o.push(l),r){let e=o.at(-1);e&&"user"!==e.type?"assistant_group"===e.type&&"streamingTextContent"!==e.messages[e.messages.length-1].message_id&&e.messages.push({content:r,type:"assistant",message_id:"streamingTextContent",metadata:"streamingTextContent",created_at:new Date().toISOString(),updated_at:new Date().toISOString(),is_llm_message:!0,thread_id:"streamingTextContent",sequence:1/0}):(s++,o.push({type:"assistant_group",messages:[{content:r,type:"assistant",message_id:"streamingTextContent",metadata:"streamingTextContent",created_at:new Date().toISOString(),updated_at:new Date().toISOString(),is_llm_message:!0,thread_id:"streamingTextContent",sequence:1/0}],key:"assistant-group-".concat(s,"-streaming")}))}return o.map((t,s)=>{if("user"===t.type){let e=t.messages[0],r=(()=>{try{return(0,u.jD)(e.content,{content:e.content}).content||e.content}catch(t){return e.content}})();if(R)return(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsx)("div",{className:"flex max-w-[85%] rounded-2xl bg-card px-4 py-3 break-words overflow-hidden",children:(0,n.jsx)("pre",{className:"text-xs font-mono whitespace-pre-wrap overflow-x-auto min-w-0 flex-1",children:e.content})})},t.key);let s=r.match(/\[Uploaded File: (.*?)\]/g),a=s?s.map(e=>{let t=e.match(/\[Uploaded File: (.*?)\]/);return t?t[1]:null}).filter(Boolean):[],o=r.replace(/\[Uploaded File: .*?\]/g,"").trim();return(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsx)("div",{className:"flex max-w-[85%] rounded-3xl rounded-br-lg bg-card border px-4 py-3 break-words overflow-hidden",children:(0,n.jsxs)("div",{className:"space-y-3 min-w-0 flex-1",children:[o&&(0,n.jsx)(i.o,{className:"text-sm prose prose-sm dark:prose-invert chat-markdown max-w-none [&>:first-child]:mt-0 prose-headings:mt-3 break-words overflow-wrap-anywhere",children:o}),j(a,f,A,C)]})})},t.key)}return"assistant_group"===t.type?(0,n.jsx)("div",{ref:s===e.length-1?U:null,children:(0,n.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"rounded-md flex items-center justify-center",children:(()=>{var e;let r=t.messages.find(e=>{var t,r;return"assistant"===e.type&&((null==(t=e.agents)?void 0:t.avatar)||(null==(r=e.agents)?void 0:r.avatar_color))});if(null==r||null==(e=r.agents)?void 0:e.avatar){let e=r.agents.avatar;return(0,n.jsx)("div",{className:"h-4 w-5 flex items-center justify-center rounded text-xs",children:(0,n.jsx)("span",{className:"text-lg",children:e})})}return(0,n.jsx)(p.R,{size:16})})()}),(0,n.jsx)("p",{className:"ml-2 text-sm text-muted-foreground",children:(()=>{var e;let r=t.messages.find(e=>{var t;return"assistant"===e.type&&(null==(t=e.agents)?void 0:t.name)});return(null==r||null==(e=r.agents)?void 0:e.name)?r.agents.name:"Suna"})()})]}),(0,n.jsx)("div",{className:"flex max-w-[90%] text-sm break-words overflow-hidden",children:(0,n.jsxs)("div",{className:"space-y-2 min-w-0 flex-1",children:[(()=>{if(R)return t.messages.map((e,t)=>{let r=e.message_id||"raw-msg-".concat(t);return(0,n.jsxs)("div",{className:"mb-4",children:[(0,n.jsxs)("div",{className:"text-xs font-medium text-muted-foreground mb-1",children:["Type: ",e.type," | ID: ",e.message_id||"no-id"]}),(0,n.jsx)("pre",{className:"text-xs font-mono whitespace-pre-wrap overflow-x-auto p-2 border border-border rounded-md bg-muted/30",children:e.content}),e.metadata&&"{}"!==e.metadata&&(0,n.jsxs)("div",{className:"mt-2",children:[(0,n.jsx)("div",{className:"text-xs font-medium text-muted-foreground mb-1",children:"Metadata:"}),(0,n.jsx)("pre",{className:"text-xs font-mono whitespace-pre-wrap overflow-x-auto p-2 border border-border rounded-md bg-muted/30",children:e.metadata})]})]},r)});let e=new Map;t.messages.forEach(t=>{if("tool"===t.type){var r;let n=(0,u.jD)(t.metadata,{}).assistant_message_id||null;e.has(n)||e.set(n,[]),null==(r=e.get(n))||r.push(t)}});let r=[],s=0;return t.messages.forEach((e,t)=>{if("assistant"===e.type){let a=(0,u.jD)(e.content,{}),o=e.message_id||"submsg-assistant-".concat(t);if(!a.content)return;let l=function(e,t,r,s,a,o,l){if(l)return(0,n.jsx)("pre",{className:"text-xs font-mono whitespace-pre-wrap overflow-x-auto p-2 border border-border rounded-md bg-muted/30 text-foreground",children:e});if((0,v.tm)(e)){let l=[],c=0,d=/<function_calls>([\s\S]*?)<\/function_calls>/gi,m=null;for(;null!==(m=d.exec(e));){if(m.index>c){let t=e.substring(c,m.index);t.trim()&&l.push((0,n.jsx)(i.o,{className:"text-sm prose prose-sm dark:prose-invert chat-markdown max-w-none break-words",children:t},"md-".concat(c)))}(0,v.CG)(m[0]).forEach((e,c)=>{let d=e.functionName.replace(/_/g,"-");if("ask"===d){let t=e.parameters.text||"",r=e.parameters.attachments||[],d=Array.isArray(r)?r:"string"==typeof r?r.split(",").map(e=>e.trim()):[];l.push((0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsx)(i.o,{className:"text-sm prose prose-sm dark:prose-invert chat-markdown max-w-none break-words [&>:first-child]:mt-0 prose-headings:mt-3",children:t}),j(d,s,a,o)]},"ask-".concat(m.index,"-").concat(c)))}else{let s=(0,u.S8)(d),a="";e.parameters.file_path?a=e.parameters.file_path:e.parameters.command?a=e.parameters.command:e.parameters.query?a=e.parameters.query:e.parameters.url&&(a=e.parameters.url),l.push((0,n.jsx)("div",{className:"my-1",children:(0,n.jsxs)("button",{onClick:()=>t(r,d),className:"inline-flex items-center gap-1.5 py-1 px-1 pr-1.5 text-xs text-muted-foreground bg-muted hover:bg-muted/80 rounded-lg transition-colors cursor-pointer border border-neutral-200 dark:border-neutral-700/50",children:[(0,n.jsx)("div",{className:"border-2 bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-neutral-700 dark:to-neutral-800 flex items-center justify-center p-0.5 rounded-sm border-neutral-400/20 dark:border-neutral-600",children:(0,n.jsx)(s,{className:"h-3.5 w-3.5 text-muted-foreground flex-shrink-0"})}),(0,n.jsx)("span",{className:"font-mono text-xs text-foreground",children:(0,u.qR)(d)}),a&&(0,n.jsx)("span",{className:"ml-1 text-muted-foreground truncate max-w-[200px]",title:a,children:a})]})},"tool-".concat(m.index,"-").concat(c)))}}),c=m.index+m[0].length}if(c<e.length){let t=e.substring(c);t.trim()&&l.push((0,n.jsx)(i.o,{className:"text-sm prose prose-sm dark:prose-invert chat-markdown max-w-none break-words",children:t},"md-".concat(c)))}return l.length>0?l:(0,n.jsx)(i.o,{className:"text-sm prose prose-sm dark:prose-invert chat-markdown max-w-none break-words",children:e})}let c=/<(?!inform\b)([a-zA-Z\-_]+)(?:\s+[^>]*)?>(?:[\s\S]*?)<\/\1>|<(?!inform\b)([a-zA-Z\-_]+)(?:\s+[^>]*)?\/>/g,d=0,m=[],p=null;if(!e.match(c))return(0,n.jsx)(i.o,{className:"text-sm prose prose-sm dark:prose-invert chat-markdown max-w-none break-words",children:e});for(;null!==(p=c.exec(e));){if(p.index>d){let t=e.substring(d,p.index);m.push((0,n.jsx)(i.o,{className:"text-sm prose prose-sm dark:prose-invert chat-markdown max-w-none inline-block mr-1 break-words",children:t},"md-".concat(d)))}let l=p[0],g=p[1]||p[2],x="tool-".concat(p.index);if("ask"===g){let e=l.match(/attachments=["']([^"']*)["']/i),t=e?e[1].split(",").map(e=>e.trim()):[],r=l.match(/<ask[^>]*>([\s\S]*?)<\/ask>/i),c=r?r[1]:"";m.push((0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsx)(i.o,{className:"text-sm prose prose-sm dark:prose-invert chat-markdown max-w-none break-words [&>:first-child]:mt-0 prose-headings:mt-3",children:c}),j(t,s,a,o)]},"ask-".concat(p.index)))}else{let e=(0,u.S8)(g),s=(0,u.D5)(g,l);m.push((0,n.jsx)("div",{className:"my-1",children:(0,n.jsxs)("button",{onClick:()=>t(r,g),className:"inline-flex items-center gap-1.5 py-1 px-1 pr-1.5 text-xs text-muted-foreground bg-muted hover:bg-muted/80 rounded-lg transition-colors cursor-pointer border border-neutral-200 dark:border-neutral-700/50",children:[(0,n.jsx)("div",{className:"border-2 bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-neutral-700 dark:to-neutral-800 flex items-center justify-center p-0.5 rounded-sm border-neutral-400/20 dark:border-neutral-600",children:(0,n.jsx)(e,{className:"h-3.5 w-3.5 text-muted-foreground flex-shrink-0"})}),(0,n.jsx)("span",{className:"font-mono text-xs text-foreground",children:(0,u.qR)(g)}),s&&(0,n.jsx)("span",{className:"ml-1 text-muted-foreground truncate max-w-[200px]",title:s,children:s})]})},x))}d=c.lastIndex}return d<e.length&&m.push((0,n.jsx)(i.o,{className:"text-sm prose prose-sm dark:prose-invert chat-markdown max-w-none break-words",children:e.substring(d)},"md-".concat(d))),m}(a.content,x,e.message_id,f,A,C,R);r.push((0,n.jsx)("div",{className:s>0?"mt-4":"",children:(0,n.jsx)("div",{className:"prose prose-sm dark:prose-invert chat-markdown max-w-none [&>:first-child]:mt-0 prose-headings:mt-3 break-words overflow-hidden",children:l})},o)),s++}}),r})(),s===o.length-1&&!h&&("streaming"===S||"connecting"===S)&&(0,n.jsx)("div",{className:"mt-2",children:(()=>{if(R&&r)return(0,n.jsx)("pre",{className:"text-xs font-mono whitespace-pre-wrap overflow-x-auto p-2 border border-border rounded-md bg-muted/30",children:r});let e=null,t=-1;if(r){let n=r.indexOf("<function_calls>");if(-1!==n)e="function_calls",t=n;else for(let n of y){let s="<".concat(n),a=r.indexOf(s);if(-1!==a){e=n,t=a;break}}}let s=r||"",o=e?s.substring(0,t):s,l=("streaming"===S||"connecting"===S)&&!e;return(0,n.jsxs)(n.Fragment,{children:[o&&(0,n.jsx)(i.o,{className:"text-sm prose prose-sm dark:prose-invert chat-markdown max-w-none [&>:first-child]:mt-0 prose-headings:mt-3 break-words overflow-wrap-anywhere",children:o}),l&&(0,n.jsx)("span",{className:"inline-block h-4 w-0.5 bg-primary ml-0.5 -mb-1 animate-pulse"}),e&&"function_calls"!==e&&(0,n.jsx)("div",{className:"mt-2 mb-1",children:(0,n.jsxs)("button",{className:"animate-shimmer inline-flex items-center gap-1.5 py-1 px-1 pr-1.5 text-xs font-medium text-primary bg-muted hover:bg-muted/80 rounded-md transition-colors cursor-pointer border border-primary/20",children:[(0,n.jsx)("div",{className:"border-2 bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-neutral-700 dark:to-neutral-800 flex items-center justify-center p-0.5 rounded-sm border-neutral-400/20 dark:border-neutral-600",children:(0,n.jsx)(a.A,{className:"h-3.5 w-3.5 text-primary flex-shrink-0 animate-spin animation-duration-2000"})}),(0,n.jsx)("span",{className:"font-mono text-xs text-primary",children:(0,u.qR)(e)})]})}),"function_calls"===e&&(0,n.jsx)("div",{className:"mt-2 mb-1",children:(0,n.jsxs)("button",{className:"animate-shimmer inline-flex items-center gap-1.5 py-1 px-1 pr-1.5 text-xs font-medium text-primary bg-muted hover:bg-muted/80 rounded-md transition-colors cursor-pointer border border-primary/20",children:[(0,n.jsx)("div",{className:"border-2 bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-neutral-700 dark:to-neutral-800 flex items-center justify-center p-0.5 rounded-sm border-neutral-400/20 dark:border-neutral-600",children:(0,n.jsx)(a.A,{className:"h-3.5 w-3.5 text-primary flex-shrink-0 animate-spin animation-duration-2000"})}),(0,n.jsx)("span",{className:"font-mono text-xs text-primary",children:(()=>{let e=(0,v.Nf)(r);return e?(0,u.qR)(e):"Using Tool..."})()})]})}),c&&!e&&(0,n.jsx)("div",{className:"mt-2 mb-1",children:(()=>{let e=c.name||c.xml_tag_name||"Tool",t=(0,u.D5)(e,c.arguments||"");return(0,n.jsxs)("button",{className:"animate-shimmer inline-flex items-center gap-1.5 py-1 px-1 pr-1.5 text-xs font-medium text-primary bg-muted hover:bg-muted/80 rounded-md transition-colors cursor-pointer border border-primary/20",children:[(0,n.jsx)("div",{className:"border-2 bg-gradient-to-br from-neutral-200 to-neutral-300 dark:from-neutral-700 dark:to-neutral-800 flex items-center justify-center p-0.5 rounded-sm border-neutral-400/20 dark:border-neutral-600",children:(0,n.jsx)(a.A,{className:"h-3.5 w-3.5 text-primary flex-shrink-0 animate-spin animation-duration-2000"})}),(0,n.jsx)("span",{className:"font-mono text-xs text-primary",children:e}),t&&(0,n.jsx)("span",{className:"ml-1 text-primary/70 truncate max-w-[200px]",title:t,children:t})]})})()})]})})()}),h&&s===o.length-1&&_&&(0,n.jsx)("div",{className:"mt-2",children:(()=>{let e=null,t=-1;if(k){let r=k.indexOf("<function_calls>");if(-1!==r)e="function_calls",t=r;else for(let r of y){let n="<".concat(r),s=k.indexOf(n);if(-1!==s){e=r,t=s;break}}}let r=k||"",s=e?r.substring(0,t):r,o=_&&!e;return(0,n.jsx)(n.Fragment,{children:R&&k?(0,n.jsx)("pre",{className:"text-xs font-mono whitespace-pre-wrap overflow-x-auto p-2 border border-border rounded-md bg-muted/30",children:k}):(0,n.jsxs)(n.Fragment,{children:[s&&(0,n.jsx)(i.o,{className:"text-sm prose prose-sm dark:prose-invert chat-markdown max-w-none [&>:first-child]:mt-0 prose-headings:mt-3 break-words overflow-wrap-anywhere",children:s}),o&&(0,n.jsx)("span",{className:"inline-block h-4 w-0.5 bg-primary ml-0.5 -mb-1 animate-pulse"}),e&&(0,n.jsx)("div",{className:"mt-2 mb-1",children:(0,n.jsxs)("button",{className:"animate-shimmer inline-flex items-center gap-1.5 py-1 px-2.5 text-xs font-medium text-primary bg-primary/10 hover:bg-primary/20 rounded-md transition-colors cursor-pointer border border-primary/20",children:[(0,n.jsx)(a.A,{className:"h-3.5 w-3.5 text-primary flex-shrink-0 animate-spin animation-duration-2000"}),(0,n.jsx)("span",{className:"font-mono text-xs text-primary",children:"function_calls"===e?(()=>{let e=(0,v.Nf)(k);return e?(0,u.qR)(e):"Using Tool..."})():(0,u.qR)(e)})]})})]})})})()})]})})]})},t.key):null})})(),("running"===g||"connecting"===g)&&!r&&!h&&(0===t.length||"user"===t[t.length-1].type)&&(0,n.jsx)("div",{ref:U,className:"w-full h-22 rounded",children:(0,n.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,n.jsxs)("div",{className:"flex items-center",children:[(0,n.jsx)("div",{className:"rounded-md flex items-center justify-center",children:F}),(0,n.jsx)("p",{className:"ml-2 text-sm text-muted-foreground",children:z||"Suna"})]}),(0,n.jsx)("div",{className:"space-y-2 w-full h-12",children:(0,n.jsx)(w,{})})]})}),h&&N&&(0,n.jsx)("div",{ref:U,children:(0,n.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,n.jsxs)("div",{className:"flex justify-start",children:[(0,n.jsx)("div",{className:"rounded-md flex items-center justify-center",children:F}),(0,n.jsx)("p",{className:"ml-2 text-sm text-muted-foreground",children:z||"Suna"})]}),(0,n.jsx)("div",{className:"space-y-2",children:(0,n.jsxs)("div",{className:"animate-shimmer inline-flex items-center gap-1.5 py-1.5 px-3 text-xs font-medium text-primary bg-primary/10 rounded-md border border-primary/20",children:[(0,n.jsx)(a.A,{className:"h-3.5 w-3.5 text-primary flex-shrink-0 animate-spin animation-duration-2000"}),(0,n.jsx)("span",{className:"font-mono text-xs text-primary",children:N.name||"Using Tool"})]})})]})}),h&&b&&0===b.length&&_&&(0,n.jsx)("div",{ref:U,children:(0,n.jsxs)("div",{className:"flex flex-col gap-2",children:[(0,n.jsxs)("div",{className:"flex justify-start",children:[(0,n.jsx)("div",{className:"rounded-md flex items-center justify-center",children:F}),(0,n.jsx)("p",{className:"ml-2 text-sm text-muted-foreground",children:z||"Suna"})]}),(0,n.jsx)("div",{className:"max-w-[90%] px-4 py-3 text-sm",children:(0,n.jsxs)("div",{className:"flex items-center gap-1.5 py-1",children:[(0,n.jsx)("div",{className:"h-1.5 w-1.5 rounded-full bg-primary/50 animate-pulse"}),(0,n.jsx)("div",{className:"h-1.5 w-1.5 rounded-full bg-primary/50 animate-pulse delay-150"}),(0,n.jsx)("div",{className:"h-1.5 w-1.5 rounded-full bg-primary/50 animate-pulse delay-300"})]})})]})})]})}),(0,n.jsx)("div",{ref:q,className:"h-1"})]}):(0,n.jsx)("div",{className:"flex-1 min-h-[60vh] flex items-center justify-center",children:D||(0,n.jsx)("div",{className:"text-center text-muted-foreground",children:h?"No messages to display.":"Send a message to start."})}),O&&(0,n.jsx)(l.$,{variant:"outline",size:"icon",className:"fixed bottom-20 right-6 z-10 h-8 w-8 rounded-full shadow-md",onClick:()=>P("smooth"),children:(0,n.jsx)(o.A,{className:"h-4 w-4"})})]})}},99856:(e,t,r)=>{function n(e){let t,r=[],n=/<function_calls>([\s\S]*?)<\/function_calls>/gi;for(;null!==(t=n.exec(e));){let e,n=t[1],s=/<invoke\s+name=["']([^"']+)["']>([\s\S]*?)<\/invoke>/gi;for(;null!==(e=s.exec(n));){let t,n=e[1].replace(/_/g,"-"),s=e[2],a={},o=/<parameter\s+name=["']([^"']+)["']>([\s\S]*?)<\/parameter>/gi;for(;null!==(t=o.exec(s));){let e=t[1],r=t[2].trim();a[e]=function(e){let t=e.trim();if(t.startsWith("{")||t.startsWith("["))try{return JSON.parse(t)}catch(e){}if("true"===t.toLowerCase())return!0;if("false"===t.toLowerCase())return!1;if(/^-?\d+(\.\d+)?$/.test(t)){let e=parseFloat(t);if(!isNaN(e))return e}return e}(r)}r.push({functionName:n,parameters:a,rawXml:e[0]})}}return r}function s(e){if(a(e)){let t=n(e);if(t.length>0)return t[0].functionName.replace(/_/g,"-")}let t=e.match(/<([a-zA-Z\-_]+)(?:\s+[^>]*)?>(?:[\s\S]*?)<\/\1>|<([a-zA-Z\-_]+)(?:\s+[^>]*)?\/>/);return t?(t[1]||t[2]).replace(/_/g,"-"):null}function a(e){return/<function_calls>[\s\S]*<invoke\s+name=/.test(e)}function o(e){let t=e.match(/<invoke\s+name=["']([^"']+)["']/i);if(t)return l(t[1].replace(/_/g,"-"));let r=e.match(/<([a-zA-Z\-_]+)(?:\s+[^>]*)?>(?!\/)/);return r?l(r[1].replace(/_/g,"-")):null}function l(e){if(e.startsWith("mcp_")){let t=e.split("_");if(t.length>=3){let e=t[1],r=t.slice(2).join("_"),n=e.charAt(0).toUpperCase()+e.slice(1),s=r;return s=r.includes("-")?r.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):r.includes("_")?r.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "):r.charAt(0).toUpperCase()+r.slice(1),"".concat(n,": ").concat(s)}}return e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")}r.d(t,{CG:()=>n,Nf:()=>o,bi:()=>s,tm:()=>a})}}]);