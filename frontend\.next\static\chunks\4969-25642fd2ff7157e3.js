"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4969],{17711:(e,t,a)=>{a.d(t,{Ej:()=>u,h:()=>m,u:()=>p});var n=a(12115),l=a(28755),r=a(71610);let o="http://localhost:8000/api",s=new Map,i=null;class c{static getInstance(){return c.instance||(c.instance=new c),c.instance}async isEnabled(e){try{let t=s.get(e);if(t&&Date.now()-t.timestamp<3e5)return t.value;let a=await fetch("".concat(o,"/feature-flags/").concat(e),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)return console.warn("Failed to fetch feature flag ".concat(e,": ").concat(a.status)),!1;let n=await a.json();return s.set(e,{value:n.enabled,timestamp:Date.now()}),n.enabled}catch(t){return console.error("Error checking feature flag ".concat(e,":"),t),!1}}async getFlagDetails(e){try{let t=await fetch("".concat(o,"/feature-flags/").concat(e),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)return console.warn("Failed to fetch feature flag details for ".concat(e,": ").concat(t.status)),null;return await t.json()}catch(t){return console.error("Error fetching feature flag details for ".concat(e,":"),t),null}}async getAllFlags(){try{if(i&&Date.now()-i.timestamp<3e5)return i.flags;let e=await fetch("".concat(o,"/feature-flags"),{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)return console.warn("Failed to fetch all feature flags: ".concat(e.status)),{};let t=await e.json();return i={flags:t.flags,timestamp:Date.now()},Object.entries(t.flags).forEach(e=>{let[t,a]=e;s.set(t,{value:a,timestamp:Date.now()})}),t.flags}catch(e){return console.error("Error fetching all feature flags:",e),{}}}clearCache(){s.clear(),i=null}async preloadFlags(e){try{let t=e.map(e=>this.isEnabled(e));await Promise.all(t)}catch(e){console.error("Error preloading feature flags:",e)}}constructor(){}}let d=c.getInstance(),u=e=>d.isEnabled(e),f={all:["feature-flags"],flag:e=>[...f.all,"flag",e],flagDetails:e=>[...f.all,"details",e],allFlags:()=>[...f.all,"allFlags"]},g=async e=>{let t=await fetch("".concat(o,"/feature-flags/").concat(e),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to fetch feature flag ".concat(e,": ").concat(t.status));return(await t.json()).enabled},p=(e,t)=>{var a,n,r,o,s;let i=(0,l.I)({queryKey:f.flag(e),queryFn:()=>g(e),staleTime:null!=(a=null==t?void 0:t.staleTime)?a:3e5,gcTime:null!=(n=null==t?void 0:t.gcTime)?n:6e5,refetchOnWindowFocus:null!=(r=null==t?void 0:t.refetchOnWindowFocus)&&r,enabled:null==(o=null==t?void 0:t.enabled)||o,retry:(e,t)=>!(t instanceof Error&&t.message.includes("4"))&&e<3,meta:{errorMessage:"Failed to fetch feature flag: ".concat(e)}});return{enabled:null!=(s=i.data)&&s,loading:i.isLoading,...i}},m=(e,t)=>{var a,l,o;let s=(0,r.E)({queries:e.map(e=>{var a,n,l;return{queryKey:f.flag(e),queryFn:()=>g(e),staleTime:null!=(a=null==t?void 0:t.staleTime)?a:3e5,gcTime:null!=(n=null==t?void 0:t.gcTime)?n:6e5,enabled:null==(l=null==t?void 0:t.enabled)||l,retry:(e,t)=>!t.message.includes("4")&&e<3}})}),i=n.useMemo(()=>{let t={};return e.forEach((e,a)=>{var n;let l=s[a];t[e]=null!=(n=l.data)&&n}),t},[s,e]);return{flags:i,loading:s.some(e=>e.isLoading),error:null!=(o=null==(l=s.find(e=>e.error))||null==(a=l.error)?void 0:a.message)?o:null}}},54165:(e,t,a)=>{a.d(t,{Cf:()=>u,Es:()=>g,L3:()=>p,LC:()=>d,c7:()=>f,lG:()=>s,rr:()=>m,zM:()=>i});var n=a(95155);a(12115);var l=a(15452),r=a(54416),o=a(59434);function s(e){let{...t}=e;return(0,n.jsx)(l.bL,{"data-slot":"dialog",...t})}function i(e){let{...t}=e;return(0,n.jsx)(l.l9,{"data-slot":"dialog-trigger",...t})}function c(e){let{...t}=e;return(0,n.jsx)(l.ZL,{"data-slot":"dialog-portal",...t})}function d(e){let{className:t,...a}=e;return(0,n.jsx)(l.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50 backdrop-blur-xs",t),...a})}function u(e){let{className:t,children:a,...s}=e;return(0,n.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,n.jsx)(d,{}),(0,n.jsxs)(l.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-2xl border p-6 shadow-lg duration-200",t),...s,children:[a,(0,n.jsxs)(l.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,n.jsx)(r.A,{}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function f(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function g(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function p(e){let{className:t,...a}=e;return(0,n.jsx)(l.hE,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg leading-none font-semibold",t),...a})}function m(e){let{className:t,...a}=e;return(0,n.jsx)(l.VY,{"data-slot":"dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",t),...a})}},59434:(e,t,a)=>{a.d(t,{$3:()=>i,Hz:()=>s,W5:()=>c,cn:()=>o});var n=a(52596),l=a(81949),r=a(39688);function o(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,n.$)(t))}let s=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"rgba(180, 180, 180)";if(!e)return t;try{if("string"==typeof e&&e.startsWith("var(")){let t=document.createElement("div");t.style.color=e,document.body.appendChild(t);let a=window.getComputedStyle(t).color;return document.body.removeChild(t),l.formatRGBA(l.parse(a))}return l.formatRGBA(l.parse(e))}catch(e){return console.error("Color parsing failed:",e),t}},i=(e,t)=>e.startsWith("rgb")?l.formatRGBA(l.alpha(l.parse(e),t)):e;function c(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return e.length<=t?e:e.slice(0,t)+"..."}},62523:(e,t,a)=>{a.d(t,{p:()=>r});var n=a(95155);a(12115);var l=a(59434);function r(e){let{className:t,type:a,...r}=e;return(0,n.jsx)("input",{type:a,"data-slot":"input",className:(0,l.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...r})}},85057:(e,t,a)=>{a.d(t,{Label:()=>o});var n=a(95155);a(12115);var l=a(40968),r=a(59434);function o(e){let{className:t,...a}=e;return(0,n.jsx)(l.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}}}]);