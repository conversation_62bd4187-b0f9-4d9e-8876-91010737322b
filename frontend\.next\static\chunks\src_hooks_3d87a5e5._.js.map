{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/use-media-query.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\n\r\nexport function useMediaQuery(query: string): boolean {\r\n  const [matches, setMatches] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const media = window.matchMedia(query);\r\n    if (media.matches !== matches) {\r\n      setMatches(media.matches);\r\n    }\r\n\r\n    const listener = () => setMatches(media.matches);\r\n    media.addEventListener('change', listener);\r\n\r\n    return () => media.removeEventListener('change', listener);\r\n  }, [matches, query]);\r\n\r\n  return matches;\r\n}\r\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAIO,SAAS,cAAc,KAAa;;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,QAAQ,OAAO,UAAU,CAAC;YAChC,IAAI,MAAM,OAAO,KAAK,SAAS;gBAC7B,WAAW,MAAM,OAAO;YAC1B;YAEA,MAAM;oDAAW,IAAM,WAAW,MAAM,OAAO;;YAC/C,MAAM,gBAAgB,CAAC,UAAU;YAEjC;2CAAO,IAAM,MAAM,mBAAmB,CAAC,UAAU;;QACnD;kCAAG;QAAC;QAAS;KAAM;IAEnB,OAAO;AACT;GAhBgB", "debugId": null}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/use-query.ts"], "sourcesContent": ["'use client';\r\n\r\nimport {\r\n  useQuery,\r\n  useMutation,\r\n  UseQueryOptions,\r\n  UseMutationOptions,\r\n  QueryKey,\r\n} from '@tanstack/react-query';\r\nimport { handleApiError, ErrorContext } from '@/lib/error-handler';\r\n\r\ntype QueryKeyValue = readonly unknown[];\r\ntype QueryKeyFunction = (...args: any[]) => QueryKeyValue;\r\ntype QueryKeyItem = QueryKeyValue | QueryKeyFunction;\r\n\r\nexport const createQueryKeys = <T extends Record<string, QueryKeyItem>>(\r\n  keys: T,\r\n): T => keys;\r\n\r\nexport function createQueryHook<\r\n  TData,\r\n  TError = Error,\r\n  TQueryKey extends QueryKey = QueryKey,\r\n>(\r\n  queryKey: TQueryKey,\r\n  queryFn: () => Promise<TData>,\r\n  options?: Omit<\r\n    UseQueryOptions<TData, TError, TData, TQueryKey>,\r\n    'queryKey' | 'queryFn'\r\n  >,\r\n) {\r\n  return (\r\n    customOptions?: Omit<\r\n      UseQueryOptions<TData, TError, TData, TQueryKey>,\r\n      'queryKey' | 'queryFn'\r\n    >,\r\n  ) => {\r\n    return useQuery<TData, TError, TData, TQueryKey>({\r\n      queryKey,\r\n      queryFn,\r\n      ...options,\r\n      ...customOptions,\r\n    });\r\n  };\r\n}\r\n\r\nexport function createMutationHook<\r\n  TData,\r\n  TVariables,\r\n  TError = Error,\r\n  TContext = unknown,\r\n>(\r\n  mutationFn: (variables: TVariables) => Promise<TData>,\r\n  options?: Omit<\r\n    UseMutationOptions<TData, TError, TVariables, TContext>,\r\n    'mutationFn'\r\n  > & {\r\n    errorContext?: ErrorContext;\r\n  },\r\n) {\r\n  return (\r\n    customOptions?: Omit<\r\n      UseMutationOptions<TData, TError, TVariables, TContext>,\r\n      'mutationFn'\r\n    > & {\r\n      errorContext?: ErrorContext;\r\n    },\r\n  ) => {\r\n    const { errorContext: baseErrorContext, ...baseOptions } = options || {};\r\n    const { errorContext: customErrorContext, ...customMutationOptions } = customOptions || {};\r\n    \r\n    return useMutation<TData, TError, TVariables, TContext>({\r\n      mutationFn,\r\n      onError: (error, variables, context) => {\r\n        const errorContext = customErrorContext || baseErrorContext;\r\n        if (!customMutationOptions?.onError && !baseOptions?.onError) {\r\n          handleApiError(error, errorContext);\r\n        }\r\n        baseOptions?.onError?.(error, variables, context);\r\n        customMutationOptions?.onError?.(error, variables, context);\r\n      },\r\n      ...baseOptions,\r\n      ...customMutationOptions,\r\n    });\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AAOA;AATA;;;AAeO,MAAM,kBAAkB,CAC7B,OACM;AAED,SAAS,gBAKd,QAAmB,EACnB,OAA6B,EAC7B,OAGC;;IAED,UAAO,CACL;;QAKA,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAmC;YAC/C;YACA;YACA,GAAG,OAAO;YACV,GAAG,aAAa;QAClB;IACF;;YANS,8KAAA,CAAA,WAAQ;;;AAOnB;AAEO,SAAS,mBAMd,UAAqD,EACrD,OAKC;;IAED,UAAO,CACL;;QAOA,MAAM,EAAE,cAAc,gBAAgB,EAAE,GAAG,aAAa,GAAG,WAAW,CAAC;QACvE,MAAM,EAAE,cAAc,kBAAkB,EAAE,GAAG,uBAAuB,GAAG,iBAAiB,CAAC;QAEzF,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAuC;YACtD;YACA,OAAO;kDAAE,CAAC,OAAO,WAAW;oBAC1B,MAAM,eAAe,sBAAsB;oBAC3C,IAAI,CAAC,uBAAuB,WAAW,CAAC,aAAa,SAAS;wBAC5D,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;oBACxB;oBACA,aAAa,UAAU,OAAO,WAAW;oBACzC,uBAAuB,UAAU,OAAO,WAAW;gBACrD;;YACA,GAAG,WAAW;YACd,GAAG,qBAAqB;QAC1B;IACF;;YAbS,iLAAA,CAAA,cAAW;;;AActB", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/dashboard/keys.ts"], "sourcesContent": ["import { createQueryKeys } from '@/hooks/use-query';\r\n\r\nconst dashboardKeysBase = ['dashboard'] as const;\r\nconst dashboardAgentsBase = ['dashboard', 'agents'] as const;\r\n\r\nexport const dashboardKeys = createQueryKeys({\r\n  all: dashboardKeysBase,\r\n  agents: dashboardAgentsBase,\r\n  initiateAgent: () => [...dashboardAgentsBase, 'initiate'] as const,\r\n});\r\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,oBAAoB;IAAC;CAAY;AACvC,MAAM,sBAAsB;IAAC;IAAa;CAAS;AAE5C,MAAM,gBAAgB,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAAE;IAC3C,KAAK;IACL,QAAQ;IACR,eAAe,IAAM;eAAI;YAAqB;SAAW;AAC3D", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/use-modal-store.ts"], "sourcesContent": ["import { create } from \"zustand\";\r\nexport type ModalType = \"paymentRequiredDialog\"\r\n\r\ninterface ModalStore {\r\n    type: ModalType | null;\r\n    isOpen: boolean;\r\n    onOpen: (type: ModalType) => void;\r\n    onClose: () => void;\r\n}\r\n\r\nexport const useModal = create<ModalStore>((set) => ({\r\n    type: null,\r\n    isOpen: false,\r\n    onOpen: (type) => set({ type, isOpen: true }),\r\n    onClose: () => set({ type: null, isOpen: false }),\r\n}));"], "names": [], "mappings": ";;;AAAA;;AAUO,MAAM,WAAW,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAc,CAAC,MAAQ,CAAC;QACjD,MAAM;QACN,QAAQ;QACR,QAAQ,CAAC,OAAS,IAAI;gBAAE;gBAAM,QAAQ;YAAK;QAC3C,SAAS,IAAM,IAAI;gBAAE,MAAM;gBAAM,QAAQ;YAAM;IACnD,CAAC", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/sidebar/keys.ts"], "sourcesContent": ["import { createQueryKeys } from \"@/hooks/use-query\";\r\n\r\nconst projectKeysBase = ['projects'] as const;\r\nconst threadKeysBase = ['threads'] as const;\r\n\r\nexport const projectKeys = createQueryKeys({\r\n  all: projectKeysBase,\r\n  lists: () => [...projectKeysBase, 'list'] as const,\r\n  details: (projectId: string) => [...projectKeysBase, 'detail', projectId] as const,\r\n  public: () => [...projectKeysBase, 'public'] as const,\r\n});\r\n\r\nexport const threadKeys = createQueryKeys({\r\n  all: threadKeysBase,\r\n  lists: () => [...threadKeysBase, 'list'] as const,\r\n  byProject: (projectId: string) => [...threadKeysBase, 'by-project', projectId] as const,\r\n});"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;IAAC;CAAW;AACpC,MAAM,iBAAiB;IAAC;CAAU;AAE3B,MAAM,cAAc,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAAE;IACzC,KAAK;IACL,OAAO,IAAM;eAAI;YAAiB;SAAO;IACzC,SAAS,CAAC,YAAsB;eAAI;YAAiB;YAAU;SAAU;IACzE,QAAQ,IAAM;eAAI;YAAiB;SAAS;AAC9C;AAEO,MAAM,aAAa,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAAE;IACxC,KAAK;IACL,OAAO,IAAM;eAAI;YAAgB;SAAO;IACxC,WAAW,CAAC,YAAsB;eAAI;YAAgB;YAAc;SAAU;AAChF", "debugId": null}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/dashboard/use-initiate-agent.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { initiateAgent, InitiateAgentResponse } from \"@/lib/api\";\r\nimport { createMutationHook } from \"@/hooks/use-query\";\r\nimport { handleApiSuccess, handleApiError } from \"@/lib/error-handler\";\r\nimport { dashboardKeys } from \"./keys\";\r\nimport { useQueryClient } from \"@tanstack/react-query\";\r\nimport { useModal } from \"@/hooks/use-modal-store\";\r\nimport { projectKeys, threadKeys } from \"../sidebar/keys\";\r\n\r\nexport const useInitiateAgentMutation = createMutationHook<\r\n  InitiateAgentResponse, \r\n  FormData\r\n>(\r\n  initiateAgent,\r\n  {\r\n    errorContext: { operation: 'initiate agent', resource: 'AI assistant' },\r\n    onSuccess: (data) => {\r\n      handleApiSuccess(\"Agent initiated successfully\", \"Your AI assistant is ready to help\");\r\n    },\r\n    onError: (error) => {\r\n      if (error instanceof Error && error.message.toLowerCase().includes(\"payment required\")) {\r\n        return;\r\n      }\r\n      handleApiError(error, { operation: 'initiate agent', resource: 'AI assistant' });\r\n    }\r\n  }\r\n);\r\n\r\nexport const useInitiateAgentWithInvalidation = () => {\r\n  const queryClient = useQueryClient();\r\n  const { onOpen } = useModal();\r\n  return useInitiateAgentMutation({\r\n    onSuccess: (data) => {\r\n      queryClient.invalidateQueries({ queryKey: projectKeys.all });\r\n      queryClient.invalidateQueries({ queryKey: threadKeys.all });\r\n      queryClient.invalidateQueries({ queryKey: dashboardKeys.agents });\r\n    },\r\n    onError: (error) => {\r\n      console.log('Mutation error:', error);\r\n      if (error instanceof Error) {\r\n        const errorMessage = error.message;\r\n        if (errorMessage.toLowerCase().includes(\"payment required\")) {\r\n          console.log('Opening payment required modal');\r\n          onOpen(\"paymentRequiredDialog\");\r\n          return;\r\n        }\r\n      }\r\n    }\r\n  });\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AARA;;;;;;;;AAUO,MAAM,2BAA2B,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EAIvD,oHAAA,CAAA,gBAAa,EACb;IACE,cAAc;QAAE,WAAW;QAAkB,UAAU;IAAe;IACtE,WAAW,CAAC;QACV,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,EAAE,gCAAgC;IACnD;IACA,SAAS,CAAC;QACR,IAAI,iBAAiB,SAAS,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,qBAAqB;YACtF;QACF;QACA,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAAE,WAAW;YAAkB,UAAU;QAAe;IAChF;AACF;AAGK,MAAM,mCAAmC;;IAC9C,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD;IAC1B,OAAO,yBAAyB;QAC9B,SAAS;yEAAE,CAAC;gBACV,YAAY,iBAAiB,CAAC;oBAAE,UAAU,oJAAA,CAAA,cAAW,CAAC,GAAG;gBAAC;gBAC1D,YAAY,iBAAiB,CAAC;oBAAE,UAAU,oJAAA,CAAA,aAAU,CAAC,GAAG;gBAAC;gBACzD,YAAY,iBAAiB,CAAC;oBAAE,UAAU,sJAAA,CAAA,gBAAa,CAAC,MAAM;gBAAC;YACjE;;QACA,OAAO;yEAAE,CAAC;gBACR,QAAQ,GAAG,CAAC,mBAAmB;gBAC/B,IAAI,iBAAiB,OAAO;oBAC1B,MAAM,eAAe,MAAM,OAAO;oBAClC,IAAI,aAAa,WAAW,GAAG,QAAQ,CAAC,qBAAqB;wBAC3D,QAAQ,GAAG,CAAC;wBACZ,OAAO;wBACP;oBACF;gBACF;YACF;;IACF;AACF;GArBa;;QACS,yLAAA,CAAA,iBAAc;QACf,wIAAA,CAAA,WAAQ;QACpB", "debugId": null}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/threads/keys.ts"], "sourcesContent": ["import { createQueryKeys } from \"@/hooks/use-query\";\r\n\r\nexport const threadKeys = createQueryKeys({\r\n  all: ['threads'] as const,\r\n  details: (threadId: string) => ['thread', threadId] as const,\r\n  messages: (threadId: string) => ['thread', threadId, 'messages'] as const,\r\n  project: (projectId: string) => ['project', projectId] as const,\r\n  publicProjects: () => ['public-projects'] as const,\r\n  agentRuns: (threadId: string) => ['thread', threadId, 'agent-runs'] as const,\r\n  billingStatus: ['billing', 'status'] as const,\r\n  byProject: (projectId: string) => ['project', projectId, 'threads'] as const,\r\n});"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,aAAa,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAAE;IACxC,KAAK;QAAC;KAAU;IAChB,SAAS,CAAC,WAAqB;YAAC;YAAU;SAAS;IACnD,UAAU,CAAC,WAAqB;YAAC;YAAU;YAAU;SAAW;IAChE,SAAS,CAAC,YAAsB;YAAC;YAAW;SAAU;IACtD,gBAAgB,IAAM;YAAC;SAAkB;IACzC,WAAW,CAAC,WAAqB;YAAC;YAAU;YAAU;SAAa;IACnE,eAAe;QAAC;QAAW;KAAS;IACpC,WAAW,CAAC,YAAsB;YAAC;YAAW;YAAW;SAAU;AACrE", "debugId": null}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/threads/utils.ts"], "sourcesContent": ["import { createClient } from \"@/lib/supabase/client\";\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_BACKEND_URL;\r\n\r\nexport type Thread = {\r\n    thread_id: string;\r\n    account_id: string | null;\r\n    project_id?: string | null;\r\n    is_public?: boolean;\r\n    created_at: string;\r\n    updated_at: string;\r\n    metadata?: {\r\n      workflow_id?: string;\r\n      workflow_name?: string;\r\n      workflow_run_name?: string;\r\n      is_workflow_execution?: boolean;\r\n      agent_id?: string;\r\n      is_agent_builder?: boolean;\r\n      [key: string]: any;\r\n    };\r\n    [key: string]: any;\r\n  };\r\n  \r\n  export type Project = {\r\n    id: string;\r\n    name: string;\r\n    description: string;\r\n    account_id: string;\r\n    created_at: string;\r\n    updated_at?: string;\r\n    sandbox: {\r\n      vnc_preview?: string;\r\n      sandbox_url?: string;\r\n      id?: string;\r\n      pass?: string;\r\n    };\r\n    is_public?: boolean;\r\n    [key: string]: any;\r\n  };\r\n  \r\n\r\n  export const getThread = async (threadId: string): Promise<Thread> => {\r\n    const supabase = createClient();\r\n    const { data, error } = await supabase\r\n      .from('threads')\r\n      .select('*')\r\n      .eq('thread_id', threadId)\r\n      .single();\r\n  \r\n    if (error) throw error;\r\n  \r\n    return data;\r\n  };\r\n\r\nexport const updateThread = async (\r\n    threadId: string,\r\n    data: Partial<Thread>,\r\n  ): Promise<Thread> => {\r\n    const supabase = createClient();\r\n  \r\n    const updateData = { ...data };\r\n  \r\n    // Update the thread\r\n    const { data: updatedThread, error } = await supabase\r\n      .from('threads')\r\n      .update(updateData)\r\n      .eq('thread_id', threadId)\r\n      .select()\r\n      .single();\r\n  \r\n    if (error) {\r\n      console.error('Error updating thread:', error);\r\n      throw new Error(`Error updating thread: ${error.message}`);\r\n    }\r\n  \r\n    return updatedThread;\r\n  };\r\n\r\nexport const toggleThreadPublicStatus = async (\r\n    threadId: string,\r\n    isPublic: boolean,\r\n  ): Promise<Thread> => {\r\n    return updateThread(threadId, { is_public: isPublic });\r\n};\r\n\r\nconst deleteSandbox = async (sandboxId: string): Promise<void> => {\r\n  try {\r\n    const supabase = createClient();\r\n    const {\r\n      data: { session },\r\n    } = await supabase.auth.getSession();\r\n\r\n    const headers: Record<string, string> = {\r\n      'Content-Type': 'application/json',\r\n    };\r\n\r\n    if (session?.access_token) {\r\n      headers['Authorization'] = `Bearer ${session.access_token}`;\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/sandboxes/${sandboxId}`, {\r\n      method: 'DELETE',\r\n      headers,\r\n    });\r\n\r\n    if (!response.ok) {\r\n      console.warn('Failed to delete sandbox, continuing with thread deletion');\r\n    }\r\n  } catch (error) {\r\n    console.warn('Error deleting sandbox, continuing with thread deletion:', error);\r\n  }\r\n};\r\n\r\nexport const deleteThread = async (threadId: string, sandboxId?: string): Promise<void> => {\r\n    try {\r\n      const supabase = createClient();\r\n\r\n      // If sandbox ID is provided, delete it directly\r\n      if (sandboxId) {\r\n        await deleteSandbox(sandboxId);\r\n      } else {\r\n        // Otherwise, get the thread to find its project and sandbox\r\n        const { data: thread, error: threadError } = await supabase\r\n          .from('threads')\r\n          .select('project_id')\r\n          .eq('thread_id', threadId)\r\n          .single();\r\n\r\n        if (threadError) {\r\n          console.error('Error fetching thread:', threadError);\r\n          throw new Error(`Error fetching thread: ${threadError.message}`);\r\n        }\r\n\r\n        // If thread has a project, get sandbox ID and delete it\r\n        if (thread?.project_id) {\r\n          const { data: project } = await supabase\r\n            .from('projects')\r\n            .select('sandbox')\r\n            .eq('project_id', thread.project_id)\r\n            .single();\r\n\r\n          if (project?.sandbox?.id) {\r\n            await deleteSandbox(project.sandbox.id);\r\n          }\r\n        }\r\n      }\r\n\r\n      console.log(`Deleting all agent runs for thread ${threadId}`);\r\n      const { error: agentRunsError } = await supabase\r\n        .from('agent_runs')\r\n        .delete()\r\n        .eq('thread_id', threadId);\r\n\r\n      if (agentRunsError) {\r\n        console.error('Error deleting agent runs:', agentRunsError);\r\n        throw new Error(`Error deleting agent runs: ${agentRunsError.message}`);\r\n      }\r\n\r\n      console.log(`Deleting all messages for thread ${threadId}`);\r\n      const { error: messagesError } = await supabase\r\n        .from('messages')\r\n        .delete()\r\n        .eq('thread_id', threadId);\r\n\r\n      if (messagesError) {\r\n        console.error('Error deleting messages:', messagesError);\r\n        throw new Error(`Error deleting messages: ${messagesError.message}`);\r\n      }\r\n\r\n      console.log(`Deleting thread ${threadId}`);\r\n      const { error: threadError2 } = await supabase\r\n        .from('threads')\r\n        .delete()\r\n        .eq('thread_id', threadId);\r\n  \r\n      if (threadError2) {\r\n        console.error('Error deleting thread:', threadError2);\r\n        throw new Error(`Error deleting thread: ${threadError2.message}`);\r\n      }\r\n  \r\n      console.log(\r\n        `Thread ${threadId} successfully deleted with all related items`,\r\n      );\r\n    } catch (error) {\r\n      console.error('Error deleting thread and related items:', error);\r\n      throw error;\r\n    }\r\n  };\r\n  \r\n\r\nexport const getPublicProjects = async (): Promise<Project[]> => {\r\n    try {\r\n      const supabase = createClient();\r\n  \r\n      // Query for threads that are marked as public\r\n      const { data: publicThreads, error: threadsError } = await supabase\r\n        .from('threads')\r\n        .select('project_id')\r\n        .eq('is_public', true);\r\n  \r\n      if (threadsError) {\r\n        console.error('Error fetching public threads:', threadsError);\r\n        return [];\r\n      }\r\n  \r\n      // If no public threads found, return empty array\r\n      if (!publicThreads?.length) {\r\n        return [];\r\n      }\r\n  \r\n      // Extract unique project IDs from public threads\r\n      const publicProjectIds = [\r\n        ...new Set(publicThreads.map((thread) => thread.project_id)),\r\n      ].filter(Boolean);\r\n  \r\n      // If no valid project IDs, return empty array\r\n      if (!publicProjectIds.length) {\r\n        return [];\r\n      }\r\n  \r\n      // Get the projects that have public threads\r\n      const { data: projects, error: projectsError } = await supabase\r\n        .from('projects')\r\n        .select('*')\r\n        .in('project_id', publicProjectIds);\r\n  \r\n      if (projectsError) {\r\n        console.error('Error fetching public projects:', projectsError);\r\n        return [];\r\n      }\r\n  \r\n      console.log(\r\n        '[API] Raw public projects from DB:',\r\n        projects?.length,\r\n        projects,\r\n      );\r\n  \r\n      // Map database fields to our Project type\r\n      const mappedProjects: Project[] = (projects || []).map((project) => ({\r\n        id: project.project_id,\r\n        name: project.name || '',\r\n        description: project.description || '',\r\n        account_id: project.account_id,\r\n        created_at: project.created_at,\r\n        updated_at: project.updated_at,\r\n        sandbox: project.sandbox || {\r\n          id: '',\r\n          pass: '',\r\n          vnc_preview: '',\r\n          sandbox_url: '',\r\n        },\r\n        is_public: true, // Mark these as public projects\r\n      }));\r\n  \r\n      console.log(\r\n        '[API] Mapped public projects for frontend:',\r\n        mappedProjects.length,\r\n      );\r\n  \r\n      return mappedProjects;\r\n    } catch (err) {\r\n      console.error('Error fetching public projects:', err);\r\n      return [];\r\n    }\r\n  };\r\n\r\n\r\n\r\n  export const getProject = async (projectId: string): Promise<Project> => {\r\n    const supabase = createClient();\r\n  \r\n    try {\r\n      const { data, error } = await supabase\r\n        .from('projects')\r\n        .select('*')\r\n        .eq('project_id', projectId)\r\n        .single();\r\n  \r\n      if (error) {\r\n        // Handle the specific \"no rows returned\" error from Supabase\r\n        if (error.code === 'PGRST116') {\r\n          throw new Error(`Project not found or not accessible: ${projectId}`);\r\n        }\r\n        throw error;\r\n      }\r\n  \r\n      console.log('Raw project data from database:', data);\r\n  \r\n      // If project has a sandbox, ensure it's started\r\n      if (data.sandbox?.id) {\r\n        // Fire off sandbox activation without blocking\r\n        const ensureSandboxActive = async () => {\r\n          try {\r\n            const {\r\n              data: { session },\r\n            } = await supabase.auth.getSession();\r\n  \r\n            // For public projects, we don't need authentication\r\n            const headers: Record<string, string> = {\r\n              'Content-Type': 'application/json',\r\n            };\r\n  \r\n            if (session?.access_token) {\r\n              headers['Authorization'] = `Bearer ${session.access_token}`;\r\n            }\r\n  \r\n            console.log(`Ensuring sandbox is active for project ${projectId}...`);\r\n            const response = await fetch(\r\n              `${API_URL}/project/${projectId}/sandbox/ensure-active`,\r\n              {\r\n                method: 'POST',\r\n                headers,\r\n              },\r\n            );\r\n  \r\n            if (!response.ok) {\r\n              const errorText = await response\r\n                .text()\r\n                .catch(() => 'No error details available');\r\n              console.warn(\r\n                `Failed to ensure sandbox is active: ${response.status} ${response.statusText}`,\r\n                errorText,\r\n              );\r\n            } else {\r\n              console.log('Sandbox activation successful');\r\n            }\r\n          } catch (sandboxError) {\r\n            console.warn('Failed to ensure sandbox is active:', sandboxError);\r\n          }\r\n        };\r\n  \r\n        // Start the sandbox activation without awaiting\r\n        ensureSandboxActive();\r\n      }\r\n  \r\n      // Map database fields to our Project type\r\n      const mappedProject: Project = {\r\n        id: data.project_id,\r\n        name: data.name || '',\r\n        description: data.description || '',\r\n        account_id: data.account_id,\r\n        is_public: data.is_public || false,\r\n        created_at: data.created_at,\r\n        sandbox: data.sandbox || {\r\n          id: '',\r\n          pass: '',\r\n          vnc_preview: '',\r\n          sandbox_url: '',\r\n        },\r\n      };\r\n  \r\n      // console.log('Mapped project data for frontend:', mappedProject);\r\n  \r\n      return mappedProject;\r\n    } catch (error) {\r\n      console.error(`Error fetching project ${projectId}:`, error);\r\n      throw error;\r\n    }\r\n  };\r\n\r\n\r\n  export const updateProject = async (\r\n    projectId: string,\r\n    data: Partial<Project>,\r\n  ): Promise<Project> => {\r\n    const supabase = createClient();\r\n  \r\n    console.log('Updating project with ID:', projectId);\r\n    console.log('Update data:', data);\r\n  \r\n    // Sanity check to avoid update errors\r\n    if (!projectId || projectId === '') {\r\n      console.error('Attempted to update project with invalid ID:', projectId);\r\n      throw new Error('Cannot update project: Invalid project ID');\r\n    }\r\n  \r\n    const { data: updatedData, error } = await supabase\r\n      .from('projects')\r\n      .update(data)\r\n      .eq('project_id', projectId)\r\n      .select()\r\n      .single();\r\n  \r\n    if (error) {\r\n      console.error('Error updating project:', error);\r\n      throw error;\r\n    }\r\n  \r\n    if (!updatedData) {\r\n      throw new Error('No data returned from update');\r\n    }\r\n  \r\n    // Dispatch a custom event to notify components about the project change\r\n    if (typeof window !== 'undefined') {\r\n      window.dispatchEvent(\r\n        new CustomEvent('project-updated', {\r\n          detail: {\r\n            projectId,\r\n            updatedData: {\r\n              id: updatedData.project_id,\r\n              name: updatedData.name,\r\n              description: updatedData.description,\r\n            },\r\n          },\r\n        }),\r\n      );\r\n    }\r\n  \r\n    // Return formatted project data - use same mapping as getProject\r\n    return {\r\n      id: updatedData.project_id,\r\n      name: updatedData.name,\r\n      description: updatedData.description || '',\r\n      account_id: updatedData.account_id,\r\n      created_at: updatedData.created_at,\r\n      sandbox: updatedData.sandbox || {\r\n        id: '',\r\n        pass: '',\r\n        vnc_preview: '',\r\n        sandbox_url: '',\r\n      },\r\n    };\r\n  };"], "names": [], "mappings": ";;;;;;;;;AAEgB;AAFhB;;AAEA,MAAM;AAuCG,MAAM,YAAY,OAAO;IAC9B,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,UAChB,MAAM;IAET,IAAI,OAAO,MAAM;IAEjB,OAAO;AACT;AAEK,MAAM,eAAe,OACxB,UACA;IAEA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,MAAM,aAAa;QAAE,GAAG,IAAI;IAAC;IAE7B,oBAAoB;IACpB,MAAM,EAAE,MAAM,aAAa,EAAE,KAAK,EAAE,GAAG,MAAM,SAC1C,IAAI,CAAC,WACL,MAAM,CAAC,YACP,EAAE,CAAC,aAAa,UAChB,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;IAC3D;IAEA,OAAO;AACT;AAEK,MAAM,2BAA2B,OACpC,UACA;IAEA,OAAO,aAAa,UAAU;QAAE,WAAW;IAAS;AACxD;AAEA,MAAM,gBAAgB,OAAO;IAC3B,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAElC,MAAM,UAAkC;YACtC,gBAAgB;QAClB;QAEA,IAAI,SAAS,cAAc;YACzB,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;QAC7D;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,WAAW,EAAE,WAAW,EAAE;YAChE,QAAQ;YACR;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,IAAI,CAAC;QACf;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,4DAA4D;IAC3E;AACF;AAEO,MAAM,eAAe,OAAO,UAAkB;IACjD,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAE5B,gDAAgD;QAChD,IAAI,WAAW;YACb,MAAM,cAAc;QACtB,OAAO;YACL,4DAA4D;YAC5D,MAAM,EAAE,MAAM,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,SAChD,IAAI,CAAC,WACL,MAAM,CAAC,cACP,EAAE,CAAC,aAAa,UAChB,MAAM;YAET,IAAI,aAAa;gBACf,QAAQ,KAAK,CAAC,0BAA0B;gBACxC,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,YAAY,OAAO,EAAE;YACjE;YAEA,wDAAwD;YACxD,IAAI,QAAQ,YAAY;gBACtB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,SAC7B,IAAI,CAAC,YACL,MAAM,CAAC,WACP,EAAE,CAAC,cAAc,OAAO,UAAU,EAClC,MAAM;gBAET,IAAI,SAAS,SAAS,IAAI;oBACxB,MAAM,cAAc,QAAQ,OAAO,CAAC,EAAE;gBACxC;YACF;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,UAAU;QAC5D,MAAM,EAAE,OAAO,cAAc,EAAE,GAAG,MAAM,SACrC,IAAI,CAAC,cACL,MAAM,GACN,EAAE,CAAC,aAAa;QAEnB,IAAI,gBAAgB;YAClB,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,eAAe,OAAO,EAAE;QACxE;QAEA,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,UAAU;QAC1D,MAAM,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SACpC,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,aAAa;QAEnB,IAAI,eAAe;YACjB,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,cAAc,OAAO,EAAE;QACrE;QAEA,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,UAAU;QACzC,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,WACL,MAAM,GACN,EAAE,CAAC,aAAa;QAEnB,IAAI,cAAc;YAChB,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,IAAI,MAAM,CAAC,uBAAuB,EAAE,aAAa,OAAO,EAAE;QAClE;QAEA,QAAQ,GAAG,CACT,CAAC,OAAO,EAAE,SAAS,4CAA4C,CAAC;IAEpE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,MAAM;IACR;AACF;AAGK,MAAM,oBAAoB;IAC7B,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAE5B,8CAA8C;QAC9C,MAAM,EAAE,MAAM,aAAa,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACxD,IAAI,CAAC,WACL,MAAM,CAAC,cACP,EAAE,CAAC,aAAa;QAEnB,IAAI,cAAc;YAChB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO,EAAE;QACX;QAEA,iDAAiD;QACjD,IAAI,CAAC,eAAe,QAAQ;YAC1B,OAAO,EAAE;QACX;QAEA,iDAAiD;QACjD,MAAM,mBAAmB;eACpB,IAAI,IAAI,cAAc,GAAG,CAAC,CAAC,SAAW,OAAO,UAAU;SAC3D,CAAC,MAAM,CAAC;QAET,8CAA8C;QAC9C,IAAI,CAAC,iBAAiB,MAAM,EAAE;YAC5B,OAAO,EAAE;QACX;QAEA,4CAA4C;QAC5C,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,cAAc;QAEpB,IAAI,eAAe;YACjB,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO,EAAE;QACX;QAEA,QAAQ,GAAG,CACT,sCACA,UAAU,QACV;QAGF,0CAA0C;QAC1C,MAAM,iBAA4B,CAAC,YAAY,EAAE,EAAE,GAAG,CAAC,CAAC,UAAY,CAAC;gBACnE,IAAI,QAAQ,UAAU;gBACtB,MAAM,QAAQ,IAAI,IAAI;gBACtB,aAAa,QAAQ,WAAW,IAAI;gBACpC,YAAY,QAAQ,UAAU;gBAC9B,YAAY,QAAQ,UAAU;gBAC9B,YAAY,QAAQ,UAAU;gBAC9B,SAAS,QAAQ,OAAO,IAAI;oBAC1B,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,aAAa;gBACf;gBACA,WAAW;YACb,CAAC;QAED,QAAQ,GAAG,CACT,8CACA,eAAe,MAAM;QAGvB,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,EAAE;IACX;AACF;AAIO,MAAM,aAAa,OAAO;IAC/B,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,cAAc,WACjB,MAAM;QAET,IAAI,OAAO;YACT,6DAA6D;YAC7D,IAAI,MAAM,IAAI,KAAK,YAAY;gBAC7B,MAAM,IAAI,MAAM,CAAC,qCAAqC,EAAE,WAAW;YACrE;YACA,MAAM;QACR;QAEA,QAAQ,GAAG,CAAC,mCAAmC;QAE/C,gDAAgD;QAChD,IAAI,KAAK,OAAO,EAAE,IAAI;YACpB,+CAA+C;YAC/C,MAAM,sBAAsB;gBAC1B,IAAI;oBACF,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;oBAElC,oDAAoD;oBACpD,MAAM,UAAkC;wBACtC,gBAAgB;oBAClB;oBAEA,IAAI,SAAS,cAAc;wBACzB,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;oBAC7D;oBAEA,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,UAAU,GAAG,CAAC;oBACpE,MAAM,WAAW,MAAM,MACrB,GAAG,QAAQ,SAAS,EAAE,UAAU,sBAAsB,CAAC,EACvD;wBACE,QAAQ;wBACR;oBACF;oBAGF,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,YAAY,MAAM,SACrB,IAAI,GACJ,KAAK,CAAC,IAAM;wBACf,QAAQ,IAAI,CACV,CAAC,oCAAoC,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,EAC/E;oBAEJ,OAAO;wBACL,QAAQ,GAAG,CAAC;oBACd;gBACF,EAAE,OAAO,cAAc;oBACrB,QAAQ,IAAI,CAAC,uCAAuC;gBACtD;YACF;YAEA,gDAAgD;YAChD;QACF;QAEA,0CAA0C;QAC1C,MAAM,gBAAyB;YAC7B,IAAI,KAAK,UAAU;YACnB,MAAM,KAAK,IAAI,IAAI;YACnB,aAAa,KAAK,WAAW,IAAI;YACjC,YAAY,KAAK,UAAU;YAC3B,WAAW,KAAK,SAAS,IAAI;YAC7B,YAAY,KAAK,UAAU;YAC3B,SAAS,KAAK,OAAO,IAAI;gBACvB,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,aAAa;YACf;QACF;QAEA,mEAAmE;QAEnE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,UAAU,CAAC,CAAC,EAAE;QACtD,MAAM;IACR;AACF;AAGO,MAAM,gBAAgB,OAC3B,WACA;IAEA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,QAAQ,GAAG,CAAC,6BAA6B;IACzC,QAAQ,GAAG,CAAC,gBAAgB;IAE5B,sCAAsC;IACtC,IAAI,CAAC,aAAa,cAAc,IAAI;QAClC,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,MAAM,WAAW,EAAE,KAAK,EAAE,GAAG,MAAM,SACxC,IAAI,CAAC,YACL,MAAM,CAAC,MACP,EAAE,CAAC,cAAc,WACjB,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,2BAA2B;QACzC,MAAM;IACR;IAEA,IAAI,CAAC,aAAa;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,wEAAwE;IACxE,wCAAmC;QACjC,OAAO,aAAa,CAClB,IAAI,YAAY,mBAAmB;YACjC,QAAQ;gBACN;gBACA,aAAa;oBACX,IAAI,YAAY,UAAU;oBAC1B,MAAM,YAAY,IAAI;oBACtB,aAAa,YAAY,WAAW;gBACtC;YACF;QACF;IAEJ;IAEA,iEAAiE;IACjE,OAAO;QACL,IAAI,YAAY,UAAU;QAC1B,MAAM,YAAY,IAAI;QACtB,aAAa,YAAY,WAAW,IAAI;QACxC,YAAY,YAAY,UAAU;QAClC,YAAY,YAAY,UAAU;QAClC,SAAS,YAAY,OAAO,IAAI;YAC9B,IAAI;YACJ,MAAM;YACN,aAAa;YACb,aAAa;QACf;IACF;AACF", "debugId": null}}, {"offset": {"line": 624, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/threads/use-threads.ts"], "sourcesContent": ["import { createMutationHook, create<PERSON><PERSON><PERSON><PERSON><PERSON> } from \"@/hooks/use-query\";\r\nimport { threadKeys } from \"./keys\";\r\nimport { Thread, updateThread, toggleThreadPublicStatus, deleteThread, getThread } from \"./utils\";\r\nimport { getThreads } from \"@/lib/api\";\r\n\r\nexport const useThreadQuery = (threadId: string) =>\r\n  createQueryHook(\r\n    threadKeys.details(threadId),\r\n    () => getThread(threadId),\r\n    {\r\n      enabled: !!threadId,\r\n      retry: 1,\r\n    }\r\n)();\r\n\r\nexport const useToggleThreadPublicStatus = () =>\r\n  createMutationHook(\r\n    ({\r\n      threadId,\r\n      isPublic,\r\n    }: {\r\n      threadId: string;\r\n      isPublic: boolean;\r\n    }) => toggleThreadPublicStatus(threadId, isPublic)\r\n)();\r\n\r\nexport const useUpdateThreadMutation = () =>\r\n  createMutationHook(\r\n    ({\r\n      threadId,\r\n      data,\r\n    }: {\r\n      threadId: string;\r\n      data: Partial<Thread>,\r\n    }) => updateThread(threadId, data)\r\n  )()\r\n\r\nexport const useDeleteThreadMutation = () =>\r\n  createMutationHook(\r\n    ({ threadId }: { threadId: string }) => deleteThread(threadId)\r\n)()\r\n\r\n\r\nexport const useThreadsForProject = (projectId: string) => {\r\n  return createQueryHook(\r\n    threadKeys.byProject(projectId),\r\n    () => getThreads(projectId),\r\n    {\r\n      enabled: !!projectId,\r\n      retry: 1,\r\n    }\r\n  )();\r\n};"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;AAEO,MAAM,iBAAiB,CAAC,WAC7B,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EACZ,oJAAA,CAAA,aAAU,CAAC,OAAO,CAAC,WACnB,IAAM,CAAA,GAAA,qJAAA,CAAA,YAAS,AAAD,EAAE,WAChB;QACE,SAAS,CAAC,CAAC;QACX,OAAO;IACT;AAGG,MAAM,8BAA8B,IACzC,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EACf,CAAC,EACC,QAAQ,EACR,QAAQ,EAIT,GAAK,CAAA,GAAA,qJAAA,CAAA,2BAAwB,AAAD,EAAE,UAAU;AAGtC,MAAM,0BAA0B,IACrC,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EACf,CAAC,EACC,QAAQ,EACR,IAAI,EAIL,GAAK,CAAA,GAAA,qJAAA,CAAA,eAAY,AAAD,EAAE,UAAU;AAG1B,MAAM,0BAA0B,IACrC,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EACf,CAAC,EAAE,QAAQ,EAAwB,GAAK,CAAA,GAAA,qJAAA,CAAA,eAAY,AAAD,EAAE;AAIlD,MAAM,uBAAuB,CAAC;IACnC,OAAO,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EACnB,oJAAA,CAAA,aAAU,CAAC,SAAS,CAAC,YACrB,IAAM,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAE,YACjB;QACE,SAAS,CAAC,CAAC;QACX,OAAO;IACT;AAEJ", "debugId": null}}, {"offset": {"line": 661, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/useBillingError.ts"], "sourcesContent": ["import { useState, useCallback } from 'react';\r\nimport { isLocalMode } from '@/lib/config';\r\n\r\ninterface BillingErrorState {\r\n  message: string;\r\n  currentUsage?: number;\r\n  limit?: number;\r\n  subscription?: {\r\n    price_id?: string;\r\n    plan_name?: string;\r\n    current_usage?: number;\r\n    limit?: number;\r\n  };\r\n}\r\n\r\nexport function useBillingError() {\r\n  const [billingError, setBillingError] = useState<BillingErrorState | null>(\r\n    null,\r\n  );\r\n\r\n  const handleBillingError = useCallback((error: any) => {\r\n    // In local mode, don't process billing errors\r\n    if (isLocalMode()) {\r\n      console.log(\r\n        'Running in local development mode - billing checks are disabled',\r\n      );\r\n      return false;\r\n    }\r\n\r\n    // Case 1: Error is already a formatted billing error detail object\r\n    if (error && (error.message || error.subscription)) {\r\n      setBillingError({\r\n        message: error.message || \"You've reached your monthly usage limit.\",\r\n        currentUsage: error.currentUsage || error.subscription?.current_usage,\r\n        limit: error.limit || error.subscription?.limit,\r\n        subscription: error.subscription || {},\r\n      });\r\n      return true;\r\n    }\r\n\r\n    // Case 2: Error is an HTTP error response\r\n    if (\r\n      error.status === 402 ||\r\n      (error.message && error.message.includes('Payment Required'))\r\n    ) {\r\n      // Try to get details from error.data.detail (common API pattern)\r\n      const errorDetail = error.data?.detail || {};\r\n      const subscription = errorDetail.subscription || {};\r\n\r\n      setBillingError({\r\n        message:\r\n          errorDetail.message || \"You've reached your monthly usage limit.\",\r\n        currentUsage: subscription.current_usage,\r\n        limit: subscription.limit,\r\n        subscription,\r\n      });\r\n      return true;\r\n    }\r\n\r\n    // Not a billing error\r\n    return false;\r\n  }, []);\r\n\r\n  const clearBillingError = useCallback(() => {\r\n    setBillingError(null);\r\n  }, []);\r\n\r\n  return {\r\n    billingError,\r\n    handleBillingError,\r\n    clearBillingError,\r\n  };\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;AAcO,SAAS;;IACd,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAC7C;IAGF,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE,CAAC;YACtC,8CAA8C;YAC9C,IAAI,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,KAAK;gBACjB,QAAQ,GAAG,CACT;gBAEF,OAAO;YACT;YAEA,mEAAmE;YACnE,IAAI,SAAS,CAAC,MAAM,OAAO,IAAI,MAAM,YAAY,GAAG;gBAClD,gBAAgB;oBACd,SAAS,MAAM,OAAO,IAAI;oBAC1B,cAAc,MAAM,YAAY,IAAI,MAAM,YAAY,EAAE;oBACxD,OAAO,MAAM,KAAK,IAAI,MAAM,YAAY,EAAE;oBAC1C,cAAc,MAAM,YAAY,IAAI,CAAC;gBACvC;gBACA,OAAO;YACT;YAEA,0CAA0C;YAC1C,IACE,MAAM,MAAM,KAAK,OAChB,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,qBACzC;gBACA,iEAAiE;gBACjE,MAAM,cAAc,MAAM,IAAI,EAAE,UAAU,CAAC;gBAC3C,MAAM,eAAe,YAAY,YAAY,IAAI,CAAC;gBAElD,gBAAgB;oBACd,SACE,YAAY,OAAO,IAAI;oBACzB,cAAc,aAAa,aAAa;oBACxC,OAAO,aAAa,KAAK;oBACzB;gBACF;gBACA,OAAO;YACT;YAEA,sBAAsB;YACtB,OAAO;QACT;0DAAG,EAAE;IAEL,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACpC,gBAAgB;QAClB;yDAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;IACF;AACF;GAzDgB", "debugId": null}}, {"offset": {"line": 727, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/use-accounts.ts"], "sourcesContent": ["import useSWR, { SWRConfiguration } from 'swr';\r\nimport { createClient } from '@/lib/supabase/client';\r\nimport { GetAccountsResponse } from '@usebasejump/shared';\r\n\r\nexport const useAccounts = (options?: SWRConfiguration) => {\r\n  const supabaseClient = createClient();\r\n  return useSWR<GetAccountsResponse>(\r\n    !!supabaseClient && ['accounts'],\r\n    async () => {\r\n      const { data, error } = await supabaseClient.rpc('get_accounts');\r\n\r\n      if (error) {\r\n        throw new Error(error.message);\r\n      }\r\n\r\n      return data;\r\n    },\r\n    options,\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;AAGO,MAAM,cAAc,CAAC;;IAC1B,MAAM,iBAAiB,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAClC,OAAO,CAAA,GAAA,iKAAA,CAAA,UAAM,AAAD,EACV,CAAC,CAAC,kBAAkB;QAAC;KAAW;8BAChC;YACE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,eAAe,GAAG,CAAC;YAEjD,IAAI,OAAO;gBACT,MAAM,IAAI,MAAM,MAAM,OAAO;YAC/B;YAEA,OAAO;QACT;6BACA;AAEJ;GAfa;;QAEJ,iKAAA,CAAA,UAAM", "debugId": null}}, {"offset": {"line": 764, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/use-cached-file.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\r\nimport { useAuth } from '@/components/AuthProvider';\r\n\r\n// Global cache to persist between component mounts\r\nconst fileCache = new Map<string, {\r\n  content: any; // Could be string content, blob URLs, or other file data\r\n  timestamp: number;\r\n  type: 'content' | 'url' | 'error';\r\n}>();\r\n\r\n// Cache expiration time in milliseconds (default: 5 minutes)\r\nconst CACHE_EXPIRATION = 5 * 60 * 1000;\r\n\r\n// Track in-progress preloads to prevent duplication\r\nconst inProgressPreloads = new Map<string, Promise<any>>();\r\n\r\n/**\r\n * Normalize a file path to ensure consistent caching\r\n * @param path The file path to normalize\r\n * @returns Normalized path starting with /workspace/\r\n */\r\nfunction normalizePath(path: string): string {\r\n  if (!path) return '/workspace';\r\n  \r\n  // Ensure path starts with /workspace\r\n  if (!path.startsWith('/workspace')) {\r\n    path = `/workspace/${path.startsWith('/') ? path.substring(1) : path}`;\r\n  }\r\n  \r\n  // Handle Unicode escape sequences like \\u0308\r\n  try {\r\n    path = path.replace(/\\\\u([0-9a-fA-F]{4})/g, (_, hexCode) => {\r\n      return String.fromCharCode(parseInt(hexCode, 16));\r\n    });\r\n  } catch (e) {\r\n    console.error('Error processing Unicode escapes in path:', e);\r\n  }\r\n  \r\n  return path;\r\n}\r\n\r\n/**\r\n * Generate a consistent cache key for a file\r\n * @param sandboxId The sandbox ID\r\n * @param path The file path (will be normalized)\r\n * @returns A consistent cache key\r\n */\r\nfunction getCacheKey(sandboxId: string, path: string): string {\r\n  const normalizedPath = normalizePath(path);\r\n  return `${sandboxId}:${normalizedPath}`;\r\n}\r\n\r\n/**\r\n * Custom hook that fetches and caches file content with authentication support\r\n * @param sandboxId The sandbox ID to fetch from\r\n * @param filePath The path to the file\r\n * @param options Additional options including cache expiration and content type\r\n * @returns The cached or freshly fetched file content\r\n */\r\nexport function useCachedFile<T = string>(\r\n  sandboxId?: string,\r\n  filePath?: string,\r\n  options: {\r\n    expiration?: number;\r\n    contentType?: 'json' | 'text' | 'blob' | 'arrayBuffer' | 'base64';\r\n    processFn?: (data: any) => T;\r\n  } = {}\r\n) {\r\n  const [data, setData] = useState<T | null>(null);\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<Error | null>(null);\r\n  const { session } = useAuth();\r\n  const [localBlobUrl, setLocalBlobUrl] = useState<string | null>(null);\r\n\r\n  // Calculate cache key from sandbox ID and file path\r\n  const cacheKey = sandboxId && filePath\r\n    ? getCacheKey(sandboxId, filePath)\r\n    : null;\r\n\r\n  // Create a cached fetch function\r\n  const getCachedFile = async (key: string, force = false) => {\r\n    // Check if we have a valid cached version\r\n    const cached = fileCache.get(key);\r\n    const now = Date.now();\r\n    const expiration = options.expiration || CACHE_EXPIRATION;\r\n    \r\n    if (!force && cached && now - cached.timestamp < expiration) {\r\n      console.log(`[FILE CACHE] Returning cached content for ${key}`);\r\n      return cached.content;\r\n    }\r\n\r\n    console.log(`[FILE CACHE] Fetching fresh content for ${key}`);\r\n    // Fetch fresh content if no cache or expired\r\n    setIsLoading(true);\r\n    try {\r\n      // Use normalized path consistently\r\n      const normalizedPath = normalizePath(filePath || '');\r\n      \r\n      const url = new URL(`${process.env.NEXT_PUBLIC_BACKEND_URL}/sandboxes/${sandboxId}/files/content`);\r\n      \r\n      // Properly encode the path parameter for UTF-8 support\r\n      url.searchParams.append('path', normalizedPath);\r\n      \r\n      // Fetch with authentication\r\n      const attemptFetch = async (isRetry: boolean = false): Promise<Response> => {\r\n        const headers: Record<string, string> = {};\r\n        if (session?.access_token) {\r\n          headers['Authorization'] = `Bearer ${session.access_token}`;\r\n        }\r\n        \r\n        const response = await fetch(url.toString(), {\r\n          headers\r\n        });\r\n        \r\n        if (!response.ok) {\r\n          const responseText = await response.text();\r\n          const errorMessage = `Failed to load file: ${response.status} ${response.statusText}`;\r\n          \r\n          // Check if this is a workspace initialization error and we haven't retried yet\r\n          const isWorkspaceNotRunning = responseText.includes('Workspace is not running');\r\n          if (isWorkspaceNotRunning && !isRetry) {\r\n            console.log(`[FILE CACHE] Workspace not ready, retrying in 2s for ${normalizedPath}`);\r\n            await new Promise(resolve => setTimeout(resolve, 2000));\r\n            return attemptFetch(true);\r\n          }\r\n          \r\n          console.error(`[FILE CACHE] Failed response for ${normalizedPath}: Status ${response.status}`);\r\n          throw new Error(errorMessage);\r\n        }\r\n        \r\n        return response;\r\n      };\r\n      \r\n      const response = await attemptFetch();\r\n      \r\n      // Process content based on contentType\r\n      let content;\r\n      let cacheType: 'content' | 'url' | 'error' = 'content';\r\n      \r\n      // Important: Check if this is a binary file that needs special handling\r\n      const isOfficeFile = filePath?.toLowerCase().match(/\\.(xlsx|xls|docx|doc|pptx|ppt)$/);\r\n      const isImageFile = filePath ? FileCache.isImageFile(filePath) : false;\r\n      const isPdfFile = filePath ? FileCache.isPdfFile(filePath) : false;\r\n      const isBinaryFile = isOfficeFile || isImageFile || isPdfFile;\r\n      \r\n      // Create a mutable copy of contentType if needed for binary files\r\n      let effectiveContentType = options.contentType || 'text';\r\n      if (isBinaryFile && effectiveContentType !== 'blob') {\r\n        console.log(`[FILE CACHE] Binary file detected (${filePath}), forcing blob contentType`);\r\n        effectiveContentType = 'blob';\r\n      }\r\n      \r\n      switch (effectiveContentType) {\r\n        case 'json':\r\n          content = await response.json();\r\n          break;\r\n        case 'blob':\r\n          // Get the blob\r\n          const blob = await response.blob();\r\n          \r\n          if (isImageFile || isPdfFile) {\r\n            // For images and PDFs, store the raw blob in cache\r\n            console.log(`[FILE CACHE] Storing raw blob for ${isPdfFile ? 'PDF' : 'image'} in cache (${blob.size} bytes, type: ${blob.type})`);\r\n            \r\n            // Verify the blob is the correct type for PDFs\r\n            if (isPdfFile && !blob.type.includes('pdf') && blob.size > 0) {\r\n              console.warn(`[FILE CACHE] PDF blob has generic MIME type: ${blob.type} - will correct it automatically`);\r\n              \r\n              const firstBytes = await blob.slice(0, 10).text();\r\n              if (firstBytes.startsWith('%PDF')) {\r\n                console.log(`[FILE CACHE] Content appears to be a PDF despite incorrect MIME type, proceeding`);\r\n                \r\n                const correctedBlob = new Blob([await blob.arrayBuffer()], { type: 'application/pdf' });\r\n                \r\n                // Store the corrected blob in cache and return it\r\n                fileCache.set(key, { content: correctedBlob, timestamp: Date.now(), type: 'content' });\r\n                return correctedBlob;\r\n              }\r\n            }\r\n            \r\n            // Store the raw blob in cache and return it\r\n            fileCache.set(key, { content: blob, timestamp: Date.now(), type: 'content' });\r\n            return blob;\r\n          } else {\r\n            // For other binary files, content is the blob\r\n            content = blob;\r\n            cacheType = 'content';\r\n          }\r\n          break;\r\n        case 'arrayBuffer':\r\n          content = await response.arrayBuffer();\r\n          break;\r\n        case 'base64':\r\n          const buffer = await response.arrayBuffer();\r\n          content = btoa(\r\n            new Uint8Array(buffer).reduce((data, byte) => data + String.fromCharCode(byte), '')\r\n          );\r\n          break;\r\n        case 'text':\r\n        default:\r\n          content = await response.text();\r\n          break;\r\n      }\r\n      \r\n      // After the switch, the caching logic should be simplified to handle all cases that fall through\r\n      fileCache.set(key, {\r\n        content,\r\n        timestamp: now,\r\n        type: cacheType\r\n      });\r\n      \r\n      return content;\r\n    } catch (err: any) {\r\n      // Cache the error to prevent repeated failing requests\r\n      fileCache.set(key, {\r\n        content: null,\r\n        timestamp: Date.now(),\r\n        type: 'error'\r\n      });\r\n      \r\n      throw err;\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  // Function to get data from cache first, then network if needed\r\n  const getFileContent = async () => {\r\n    if (!cacheKey) return;\r\n\r\n    const processContent = (content: any) => {\r\n      if (localBlobUrl) {\r\n        URL.revokeObjectURL(localBlobUrl);\r\n      }\r\n\r\n      if (content instanceof Blob) {\r\n        const newUrl = URL.createObjectURL(content);\r\n        setLocalBlobUrl(newUrl);\r\n        setData(newUrl as any);\r\n      } else {\r\n        setLocalBlobUrl(null);\r\n        setData(content);\r\n      }\r\n    };\r\n    \r\n    try {\r\n      // First check if we have cached data\r\n      const cachedItem = fileCache.get(cacheKey);\r\n      if (cachedItem) {\r\n        // Set data from cache immediately\r\n        processContent(cachedItem.content);\r\n        \r\n        // If cache is expired, refresh in background\r\n        if (Date.now() - cachedItem.timestamp > (options.expiration || CACHE_EXPIRATION)) {\r\n          getCachedFile(cacheKey, true)\r\n            .then(freshData => processContent(freshData))\r\n            .catch(err => console.error(\"Background refresh failed:\", err));\r\n        }\r\n      } else {\r\n        // No cache, load fresh\r\n        setIsLoading(true);\r\n        const content = await getCachedFile(cacheKey);\r\n        processContent(content);\r\n      }\r\n    } catch (err: any) {\r\n      setError(err);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (sandboxId && filePath) {\r\n      getFileContent();\r\n    } else {\r\n      // Reset state if we don't have necessary params\r\n      setData(null);\r\n      setIsLoading(false);\r\n      setError(null);\r\n    }\r\n    \r\n    // Clean up the local blob URL when component unmounts\r\n    return () => {\r\n      if (localBlobUrl) {\r\n        URL.revokeObjectURL(localBlobUrl);\r\n        setLocalBlobUrl(null);\r\n      }\r\n    };\r\n  }, [sandboxId, filePath, options.contentType]);\r\n\r\n  // Expose the cache manipulation functions\r\n  return {\r\n    data,\r\n    isLoading,\r\n    error,\r\n    getCachedFile: (key?: string, force = false) => {\r\n      return key ? getCachedFile(key, force) : (cacheKey ? getCachedFile(cacheKey, force) : Promise.resolve(null));\r\n    },\r\n    // Helper function for direct cache access\r\n    getFromCache: (key: string) => fileCache.get(key)?.content || null,\r\n    // Static version for direct imports\r\n    cache: fileCache\r\n  };\r\n}\r\n\r\n// Static functions for direct cache manipulation without hooks\r\nexport const FileCache = {\r\n  get: (key: string) => fileCache.get(key)?.content || null,\r\n  \r\n  set: (key: string, content: any) => {\r\n    if (!key || content === null || content === undefined) return;\r\n    \r\n    fileCache.set(key, {\r\n      content,\r\n      timestamp: Date.now(),\r\n      type: typeof content === 'string' && content.startsWith('blob:') ? 'url' : 'content'\r\n    });\r\n  },\r\n  \r\n  has: (key: string) => fileCache.has(key),\r\n  \r\n  clear: () => fileCache.clear(),\r\n  \r\n  delete: (key: string) => fileCache.delete(key),\r\n  \r\n  // Helper function to determine content type from file extension\r\n  getContentTypeFromPath: (path: string): 'text' | 'blob' | 'json' => {\r\n    if (!path) return 'text';\r\n    \r\n    const ext = path.toLowerCase().split('.').pop() || '';\r\n    \r\n    // Binary file extensions\r\n    if (/^(xlsx|xls|docx|doc|pptx|ppt|pdf|png|jpg|jpeg|gif|bmp|webp|svg|ico|zip|exe|dll|bin|dat|obj|o|so|dylib|mp3|mp4|avi|mov|wmv|flv|wav|ogg)$/.test(ext)) {\r\n      return 'blob';\r\n    }\r\n    \r\n    // JSON files\r\n    if (ext === 'json') return 'json';\r\n    \r\n    // Default to text\r\n    return 'text';\r\n  },\r\n  \r\n  // Helper function to check if a file is an image\r\n  isImageFile: (path: string): boolean => {\r\n    const ext = path.split('.').pop()?.toLowerCase() || '';\r\n    return ['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp', 'bmp', 'ico'].includes(ext);\r\n  },\r\n  \r\n  // Helper function to check if a file is a PDF\r\n  isPdfFile: (path: string): boolean => {\r\n    return path.toLowerCase().endsWith('.pdf');\r\n  },\r\n  \r\n  // Helper function to check if a value is a Blob\r\n  isBlob: (value: any): boolean => {\r\n    return value instanceof Blob;\r\n  },\r\n  \r\n  // Helper function to get the correct content type for a file\r\n  getContentType: (path: string, contentType?: 'text' | 'blob' | 'json'): 'text' | 'blob' | 'json' => {\r\n    return contentType || FileCache.getContentTypeFromPath(path);\r\n  },\r\n  \r\n  // Fix: Rename to avoid duplicate property name error\r\n  getMimeTypeFromPath: (path: string): string => {\r\n    const ext = path.split('.').pop()?.toLowerCase() || '';\r\n    \r\n    // Office documents\r\n    switch (ext) {\r\n      case 'xlsx': return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';\r\n      case 'xls': return 'application/vnd.ms-excel';\r\n      case 'docx': return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';\r\n      case 'doc': return 'application/msword';\r\n      case 'pptx': return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';\r\n      case 'ppt': return 'application/vnd.ms-powerpoint';\r\n      \r\n      // PDF and images\r\n      case 'pdf': return 'application/pdf';\r\n      case 'png': return 'image/png';\r\n      case 'jpg': \r\n      case 'jpeg': return 'image/jpeg';\r\n      case 'gif': return 'image/gif';\r\n      case 'svg': return 'image/svg+xml';\r\n      \r\n      // Archives\r\n      case 'zip': return 'application/zip';\r\n      \r\n      // Default\r\n      default: return 'application/octet-stream';\r\n    }\r\n  },\r\n  \r\n  // Preload files into cache for future use\r\n  preload: async (sandboxId: string, filePaths: string[], token?: string | null) => {\r\n    if (!token) {\r\n      // Try to get token from localStorage if not provided\r\n      try {\r\n        const sessionData = localStorage.getItem('auth');\r\n        if (sessionData) {\r\n          const session = JSON.parse(sessionData);\r\n          token = session.access_token;\r\n        }\r\n      } catch (err) {\r\n        console.error('Failed to get auth token from localStorage:', err);\r\n      }\r\n    }\r\n    \r\n    // Skip preloading if no authentication token available\r\n    if (!token) {\r\n      console.warn('Cannot preload files: No authentication token available');\r\n      return [];\r\n    }\r\n    \r\n    // Deduplicate the file paths\r\n    const uniqueFilePaths = [...new Set(filePaths)];\r\n    \r\n    if (uniqueFilePaths.length < filePaths.length) {\r\n      console.log(`[FILE CACHE] Removed ${filePaths.length - uniqueFilePaths.length} duplicate file paths`);\r\n    }\r\n    \r\n    console.log(`[FILE CACHE] Preloading ${uniqueFilePaths.length} files for sandbox ${sandboxId}`);\r\n    \r\n    // Create an array to track promises for each file\r\n    const preloadPromises = uniqueFilePaths.map(async (path) => {\r\n      // Handle Unicode escape sequences in paths\r\n      path = path.replace(/\\\\u([0-9a-fA-F]{4})/g, (_, hexCode) => {\r\n        return String.fromCharCode(parseInt(hexCode, 16));\r\n      });\r\n      \r\n      const normalizedPath = normalizePath(path);\r\n      const key = getCacheKey(sandboxId, normalizedPath);\r\n      \r\n      // Skip if already cached\r\n      if (fileCache.has(key)) {\r\n        console.log(`[FILE CACHE] Already cached: ${normalizedPath}`);\r\n        return fileCache.get(key)?.content;\r\n      }\r\n      \r\n      // Check if this file is already being preloaded\r\n      const preloadKey = `${sandboxId}:${normalizedPath}`;\r\n      if (inProgressPreloads.has(preloadKey)) {\r\n        console.log(`[FILE CACHE] Preload already in progress for: ${normalizedPath}`);\r\n        return inProgressPreloads.get(preloadKey);\r\n      }\r\n      \r\n      console.log(`[FILE CACHE] Preloading file: ${normalizedPath}`);\r\n      \r\n      // Create a promise for this preload and store it\r\n      const preloadPromise = (async () => {\r\n        try {        \r\n          const url = new URL(`${process.env.NEXT_PUBLIC_BACKEND_URL}/sandboxes/${sandboxId}/files/content`);\r\n          \r\n          // Properly encode the path parameter for UTF-8 support\r\n          url.searchParams.append('path', normalizedPath);\r\n          \r\n          const attemptFetch = async (isRetry: boolean = false): Promise<Response> => {\r\n            const response = await fetch(url.toString(), {\r\n              headers: {\r\n                'Authorization': `Bearer ${token}`\r\n              },\r\n            });\r\n            \r\n            if (!response.ok) {\r\n              const responseText = await response.text();\r\n              const errorMessage = `Failed to preload file: ${response.status}`;\r\n              \r\n              // Check if this is a workspace initialization error and we haven't retried yet\r\n              const isWorkspaceNotRunning = responseText.includes('Workspace is not running');\r\n              if (isWorkspaceNotRunning && !isRetry) {\r\n                console.log(`[FILE CACHE] Workspace not ready during preload, retrying in 2s for ${normalizedPath}`);\r\n                await new Promise(resolve => setTimeout(resolve, 2000));\r\n                return attemptFetch(true);\r\n              }\r\n              \r\n              throw new Error(errorMessage);\r\n            }\r\n            \r\n            return response;\r\n          };\r\n          \r\n          const response = await attemptFetch();\r\n          \r\n          // Determine how to process the content based on file type\r\n          const extension = path.split('.').pop()?.toLowerCase();\r\n          let content;\r\n          let type: 'content' | 'url' = 'content';\r\n          \r\n          // Check if this is a binary file (includes Office documents, PDFs, images)\r\n          const isBinaryFile = ['png', 'jpg', 'jpeg', 'gif', 'pdf', 'mp3', 'mp4', \r\n                               'xlsx', 'xls', 'docx', 'doc', 'pptx', 'ppt', \r\n                               'zip', 'exe', 'bin'].includes(extension || '');\r\n          \r\n          if (isBinaryFile) {\r\n            const blob = await response.blob();\r\n            \r\n            if (FileCache.isImageFile(path)) {\r\n              // For images, store the raw blob\r\n              content = blob;\r\n              type = 'content';\r\n              console.log(`[FILE CACHE] Successfully preloaded image blob: ${normalizedPath} (${blob.size} bytes)`);\r\n            } else if (extension === 'pdf' || ['xlsx', 'xls', 'docx', 'doc', 'pptx', 'ppt'].includes(extension || '')) {\r\n              // For PDFs and Office documents, ensure they're stored as blobs with proper MIME type\r\n              const mimeType = FileCache.getMimeTypeFromPath(path);\r\n              const properBlob = new Blob([blob], { type: mimeType });\r\n              content = properBlob;\r\n              type = 'content';\r\n              console.log(`[FILE CACHE] Successfully preloaded binary blob for ${extension} file: ${normalizedPath} (${blob.size} bytes)`);\r\n            } else {\r\n              // For other binary files, store the URL\r\n              content = URL.createObjectURL(blob);\r\n              type = 'url';\r\n              console.log(`[FILE CACHE] Successfully preloaded blob URL: ${normalizedPath} (${blob.size} bytes)`);\r\n            }\r\n          } \r\n          // Json files\r\n          else if (extension === 'json') {\r\n            content = await response.json();\r\n            console.log(`[FILE CACHE] Successfully preloaded JSON: ${normalizedPath}`);\r\n          } \r\n          // Default to text\r\n          else {\r\n            content = await response.text();\r\n            console.log(`[FILE CACHE] Successfully preloaded text: ${normalizedPath} (${content.length} bytes)`);\r\n          }\r\n          \r\n          fileCache.set(key, {\r\n            content,\r\n            timestamp: Date.now(),\r\n            type\r\n          });\r\n          \r\n          return content;\r\n        } catch (err) {\r\n          console.error(`[FILE CACHE] Failed to preload ${normalizedPath}:`, err);\r\n          fileCache.set(key, {\r\n            content: null,\r\n            timestamp: Date.now(),\r\n            type: 'error'\r\n          });\r\n          return null;\r\n        } finally {\r\n          // Remove from in-progress map when done\r\n          inProgressPreloads.delete(preloadKey);\r\n        }\r\n      })();\r\n      \r\n      // Store the promise in the in-progress map\r\n      inProgressPreloads.set(preloadKey, preloadPromise);\r\n      \r\n      return preloadPromise;\r\n    });\r\n    \r\n    return Promise.all(preloadPromises);\r\n  },\r\n  \r\n  // Helper function to get the correct MIME type for a file\r\n  getMimeType: (path: string): string => {\r\n    // Call our renamed function to avoid duplication\r\n    return FileCache.getMimeTypeFromPath(path);\r\n  },\r\n};\r\n\r\n// Update the getCachedFile function to be simpler and more direct\r\nexport async function getCachedFile(\r\n  sandboxId: string,\r\n  filePath: string,\r\n  options: {\r\n    contentType?: 'json' | 'text' | 'blob';\r\n    force?: boolean;\r\n    token?: string;\r\n  } = {}\r\n): Promise<any> {\r\n  if (!filePath || !sandboxId) return null;\r\n  \r\n  // Normalize path and create cache key\r\n  const normalizedPath = normalizePath(filePath);\r\n  const key = getCacheKey(sandboxId, normalizedPath);\r\n  \r\n  // Determine appropriate content type\r\n  const isBinaryFile = FileCache.getContentTypeFromPath(filePath) === 'blob';\r\n  const effectiveType = isBinaryFile ? 'blob' : (options.contentType || 'text');\r\n  \r\n  // Check cache first unless force refresh requested\r\n  if (!options.force && fileCache.has(key)) {\r\n    const cached = fileCache.get(key);\r\n    if (cached && cached.type !== 'error') return cached.content;\r\n  }\r\n  \r\n  // Fetch fresh content\r\n  try {\r\n    const url = new URL(`${process.env.NEXT_PUBLIC_BACKEND_URL}/sandboxes/${sandboxId}/files/content`);\r\n    url.searchParams.append('path', normalizedPath);\r\n    \r\n    console.log(`[FILE CACHE] Fetching file: ${url.toString()}`);\r\n    \r\n    const attemptFetch = async (isRetry: boolean = false): Promise<Response> => {\r\n      const response = await fetch(url.toString(), {\r\n        headers: {\r\n          'Authorization': `Bearer ${options.token}`\r\n        }\r\n      });\r\n      \r\n      if (!response.ok) {\r\n        const responseText = await response.text();\r\n        const errorMessage = `Failed to load file: ${response.status} ${response.statusText}`;\r\n        \r\n        // Check if this is a workspace initialization error and we haven't retried yet\r\n        const isWorkspaceNotRunning = responseText.includes('Workspace is not running');\r\n        if (isWorkspaceNotRunning && !isRetry) {\r\n          console.log(`[FILE CACHE] Workspace not ready, retrying in 2s for ${normalizedPath}`);\r\n          await new Promise(resolve => setTimeout(resolve, 2000));\r\n          return attemptFetch(true);\r\n        }\r\n        \r\n        console.error(`[FILE CACHE] Failed response for ${normalizedPath}: Status ${response.status}`);\r\n        throw new Error(errorMessage);\r\n      }\r\n      \r\n      return response;\r\n    };\r\n    \r\n    const response = await attemptFetch();\r\n    \r\n    // Process content based on type\r\n    let content;\r\n    \r\n    if (effectiveType === 'json') {\r\n      content = await response.json();\r\n    } else if (effectiveType === 'blob') {\r\n      const blob = await response.blob();\r\n      \r\n      // For binary files, ensure correct MIME type\r\n      const mimeType = FileCache.getMimeType(filePath);\r\n      if (mimeType && mimeType !== blob.type) {\r\n        content = new Blob([blob], { type: mimeType });\r\n      } else {\r\n        content = blob;\r\n      }\r\n      \r\n      // For images and PDFs, return a blob URL for immediate use\r\n      if (FileCache.isImageFile(filePath) || FileCache.isPdfFile(filePath)) {\r\n        // Store the blob in cache\r\n        FileCache.set(key, content);\r\n        \r\n        // Return URL for immediate use\r\n        return URL.createObjectURL(content);\r\n      }\r\n    } else {\r\n      content = await response.text();\r\n    }\r\n    \r\n    // Cache the result\r\n    FileCache.set(key, content);\r\n    return content;\r\n    \r\n  } catch (err) {\r\n    // Cache the error\r\n    FileCache.set(key, null);\r\n    throw err;\r\n  }\r\n}\r\n\r\n// Ensure fetchFileContent correctly handles binary files by fixing the response handling:\r\nexport async function fetchFileContent(\r\n  sandboxId: string,\r\n  filePath: string,\r\n  options: {\r\n    contentType?: 'text' | 'blob' | 'json';\r\n    token: string;\r\n  }\r\n): Promise<string | Blob | any> {\r\n  const { contentType = 'text', token } = options;\r\n  \r\n  // For internal tracking\r\n  const requestId = Math.random().toString(36).substring(2, 9);\r\n  console.log(`[FILE CACHE] Fetching fresh content for ${sandboxId}:${filePath}`);\r\n  \r\n  const attemptFetch = async (isRetry: boolean = false): Promise<string | Blob | any> => {\r\n    try {\r\n      // Prepare the API URL\r\n      const apiUrl = `${process.env.NEXT_PUBLIC_BACKEND_URL}/sandboxes/${sandboxId}/files/content`;\r\n      const url = new URL(apiUrl);\r\n      url.searchParams.append('path', filePath);\r\n      \r\n      // Set up fetch options\r\n      const fetchOptions: RequestInit = {\r\n        method: 'GET',\r\n        headers: {\r\n          Authorization: `Bearer ${token}`,\r\n        },\r\n      };\r\n      \r\n      // Execute fetch\r\n      const response = await fetch(url.toString(), fetchOptions);\r\n      if (!response.ok) {\r\n        const errorText = await response.text();\r\n        throw new Error(`Failed to fetch file content: ${response.status} ${errorText}`);\r\n      }\r\n      \r\n      // CRITICAL: Detect correct response handling based on file type\r\n      // Excel files, PDFs and other binary documents should be handled as blobs\r\n      const extension = filePath.split('.').pop()?.toLowerCase();\r\n      const isBinaryFile = ['xlsx', 'xls', 'docx', 'doc', 'pptx', 'ppt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'zip'].includes(extension || '');\r\n      \r\n      // Handle response based on content type\r\n      if (contentType === 'blob' || isBinaryFile) {\r\n        const blob = await response.blob();\r\n        \r\n        // Set correct MIME type for known file types\r\n        if (extension) {\r\n          const mimeType = FileCache.getMimeType(filePath);\r\n          if (mimeType && mimeType !== blob.type) {\r\n            // Create a new blob with correct type\r\n            return new Blob([blob], { type: mimeType });\r\n          }\r\n        }\r\n        \r\n        return blob;\r\n      } else if (contentType === 'json') {\r\n        return await response.json();\r\n      } else {\r\n        return await response.text();\r\n      }\r\n    } catch (error: any) {\r\n      // Check if this is a workspace initialization error and we haven't retried yet\r\n      const isWorkspaceNotRunning = error.message?.includes('Workspace is not running');\r\n      if (isWorkspaceNotRunning && !isRetry) {\r\n        console.log(`[FILE CACHE] Workspace not ready, retrying in 2s for ${filePath}`);\r\n        await new Promise(resolve => setTimeout(resolve, 2000));\r\n        return attemptFetch(true);\r\n      }\r\n      throw error;\r\n    }\r\n  };\r\n  \r\n  try {\r\n    return await attemptFetch();\r\n  } catch (error) {\r\n    console.error(`[FILE CACHE] Error fetching file content:`, error);\r\n    throw error;\r\n  }\r\n} "], "names": [], "mappings": ";;;;;;AAkG6B;AAlG7B;AACA;;;;AAEA,mDAAmD;AACnD,MAAM,YAAY,IAAI;AAMtB,6DAA6D;AAC7D,MAAM,mBAAmB,IAAI,KAAK;AAElC,oDAAoD;AACpD,MAAM,qBAAqB,IAAI;AAE/B;;;;CAIC,GACD,SAAS,cAAc,IAAY;IACjC,IAAI,CAAC,MAAM,OAAO;IAElB,qCAAqC;IACrC,IAAI,CAAC,KAAK,UAAU,CAAC,eAAe;QAClC,OAAO,CAAC,WAAW,EAAE,KAAK,UAAU,CAAC,OAAO,KAAK,SAAS,CAAC,KAAK,MAAM;IACxE;IAEA,8CAA8C;IAC9C,IAAI;QACF,OAAO,KAAK,OAAO,CAAC,wBAAwB,CAAC,GAAG;YAC9C,OAAO,OAAO,YAAY,CAAC,SAAS,SAAS;QAC/C;IACF,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,6CAA6C;IAC7D;IAEA,OAAO;AACT;AAEA;;;;;CAKC,GACD,SAAS,YAAY,SAAiB,EAAE,IAAY;IAClD,MAAM,iBAAiB,cAAc;IACrC,OAAO,GAAG,UAAU,CAAC,EAAE,gBAAgB;AACzC;AASO,SAAS,cACd,SAAkB,EAClB,QAAiB,EACjB,UAII,CAAC,CAAC;;IAEN,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;IACjD,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,oDAAoD;IACpD,MAAM,WAAW,aAAa,WAC1B,YAAY,WAAW,YACvB;IAEJ,iCAAiC;IACjC,MAAM,gBAAgB,OAAO,KAAa,QAAQ,KAAK;QACrD,0CAA0C;QAC1C,MAAM,SAAS,UAAU,GAAG,CAAC;QAC7B,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,aAAa,QAAQ,UAAU,IAAI;QAEzC,IAAI,CAAC,SAAS,UAAU,MAAM,OAAO,SAAS,GAAG,YAAY;YAC3D,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,KAAK;YAC9D,OAAO,OAAO,OAAO;QACvB;QAEA,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,KAAK;QAC5D,6CAA6C;QAC7C,aAAa;QACb,IAAI;YACF,mCAAmC;YACnC,MAAM,iBAAiB,cAAc,YAAY;YAEjD,MAAM,MAAM,IAAI,IAAI,iEAAuC,WAAW,EAAE,UAAU,cAAc,CAAC;YAEjG,uDAAuD;YACvD,IAAI,YAAY,CAAC,MAAM,CAAC,QAAQ;YAEhC,4BAA4B;YAC5B,MAAM,eAAe,OAAO,UAAmB,KAAK;gBAClD,MAAM,UAAkC,CAAC;gBACzC,IAAI,SAAS,cAAc;oBACzB,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;gBAC7D;gBAEA,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ,IAAI;oBAC3C;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,eAAe,MAAM,SAAS,IAAI;oBACxC,MAAM,eAAe,CAAC,qBAAqB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;oBAErF,+EAA+E;oBAC/E,MAAM,wBAAwB,aAAa,QAAQ,CAAC;oBACpD,IAAI,yBAAyB,CAAC,SAAS;wBACrC,QAAQ,GAAG,CAAC,CAAC,qDAAqD,EAAE,gBAAgB;wBACpF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;wBACjD,OAAO,aAAa;oBACtB;oBAEA,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,eAAe,SAAS,EAAE,SAAS,MAAM,EAAE;oBAC7F,MAAM,IAAI,MAAM;gBAClB;gBAEA,OAAO;YACT;YAEA,MAAM,WAAW,MAAM;YAEvB,uCAAuC;YACvC,IAAI;YACJ,IAAI,YAAyC;YAE7C,wEAAwE;YACxE,MAAM,eAAe,UAAU,cAAc,MAAM;YACnD,MAAM,cAAc,WAAW,UAAU,WAAW,CAAC,YAAY;YACjE,MAAM,YAAY,WAAW,UAAU,SAAS,CAAC,YAAY;YAC7D,MAAM,eAAe,gBAAgB,eAAe;YAEpD,kEAAkE;YAClE,IAAI,uBAAuB,QAAQ,WAAW,IAAI;YAClD,IAAI,gBAAgB,yBAAyB,QAAQ;gBACnD,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,SAAS,2BAA2B,CAAC;gBACvF,uBAAuB;YACzB;YAEA,OAAQ;gBACN,KAAK;oBACH,UAAU,MAAM,SAAS,IAAI;oBAC7B;gBACF,KAAK;oBACH,eAAe;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAEhC,IAAI,eAAe,WAAW;wBAC5B,mDAAmD;wBACnD,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,YAAY,QAAQ,QAAQ,WAAW,EAAE,KAAK,IAAI,CAAC,cAAc,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC;wBAEhI,+CAA+C;wBAC/C,IAAI,aAAa,CAAC,KAAK,IAAI,CAAC,QAAQ,CAAC,UAAU,KAAK,IAAI,GAAG,GAAG;4BAC5D,QAAQ,IAAI,CAAC,CAAC,6CAA6C,EAAE,KAAK,IAAI,CAAC,gCAAgC,CAAC;4BAExG,MAAM,aAAa,MAAM,KAAK,KAAK,CAAC,GAAG,IAAI,IAAI;4BAC/C,IAAI,WAAW,UAAU,CAAC,SAAS;gCACjC,QAAQ,GAAG,CAAC,CAAC,gFAAgF,CAAC;gCAE9F,MAAM,gBAAgB,IAAI,KAAK;oCAAC,MAAM,KAAK,WAAW;iCAAG,EAAE;oCAAE,MAAM;gCAAkB;gCAErF,kDAAkD;gCAClD,UAAU,GAAG,CAAC,KAAK;oCAAE,SAAS;oCAAe,WAAW,KAAK,GAAG;oCAAI,MAAM;gCAAU;gCACpF,OAAO;4BACT;wBACF;wBAEA,4CAA4C;wBAC5C,UAAU,GAAG,CAAC,KAAK;4BAAE,SAAS;4BAAM,WAAW,KAAK,GAAG;4BAAI,MAAM;wBAAU;wBAC3E,OAAO;oBACT,OAAO;wBACL,8CAA8C;wBAC9C,UAAU;wBACV,YAAY;oBACd;oBACA;gBACF,KAAK;oBACH,UAAU,MAAM,SAAS,WAAW;oBACpC;gBACF,KAAK;oBACH,MAAM,SAAS,MAAM,SAAS,WAAW;oBACzC,UAAU,KACR,IAAI,WAAW,QAAQ,MAAM,CAAC,CAAC,MAAM,OAAS,OAAO,OAAO,YAAY,CAAC,OAAO;oBAElF;gBACF,KAAK;gBACL;oBACE,UAAU,MAAM,SAAS,IAAI;oBAC7B;YACJ;YAEA,iGAAiG;YACjG,UAAU,GAAG,CAAC,KAAK;gBACjB;gBACA,WAAW;gBACX,MAAM;YACR;YAEA,OAAO;QACT,EAAE,OAAO,KAAU;YACjB,uDAAuD;YACvD,UAAU,GAAG,CAAC,KAAK;gBACjB,SAAS;gBACT,WAAW,KAAK,GAAG;gBACnB,MAAM;YACR;YAEA,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,gEAAgE;IAChE,MAAM,iBAAiB;QACrB,IAAI,CAAC,UAAU;QAEf,MAAM,iBAAiB,CAAC;YACtB,IAAI,cAAc;gBAChB,IAAI,eAAe,CAAC;YACtB;YAEA,IAAI,mBAAmB,MAAM;gBAC3B,MAAM,SAAS,IAAI,eAAe,CAAC;gBACnC,gBAAgB;gBAChB,QAAQ;YACV,OAAO;gBACL,gBAAgB;gBAChB,QAAQ;YACV;QACF;QAEA,IAAI;YACF,qCAAqC;YACrC,MAAM,aAAa,UAAU,GAAG,CAAC;YACjC,IAAI,YAAY;gBACd,kCAAkC;gBAClC,eAAe,WAAW,OAAO;gBAEjC,6CAA6C;gBAC7C,IAAI,KAAK,GAAG,KAAK,WAAW,SAAS,GAAG,CAAC,QAAQ,UAAU,IAAI,gBAAgB,GAAG;oBAChF,cAAc,UAAU,MACrB,IAAI,CAAC,CAAA,YAAa,eAAe,YACjC,KAAK,CAAC,CAAA,MAAO,QAAQ,KAAK,CAAC,8BAA8B;gBAC9D;YACF,OAAO;gBACL,uBAAuB;gBACvB,aAAa;gBACb,MAAM,UAAU,MAAM,cAAc;gBACpC,eAAe;YACjB;QACF,EAAE,OAAO,KAAU;YACjB,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,aAAa,UAAU;gBACzB;YACF,OAAO;gBACL,gDAAgD;gBAChD,QAAQ;gBACR,aAAa;gBACb,SAAS;YACX;YAEA,sDAAsD;YACtD;2CAAO;oBACL,IAAI,cAAc;wBAChB,IAAI,eAAe,CAAC;wBACpB,gBAAgB;oBAClB;gBACF;;QACF;kCAAG;QAAC;QAAW;QAAU,QAAQ,WAAW;KAAC;IAE7C,0CAA0C;IAC1C,OAAO;QACL;QACA;QACA;QACA,eAAe,CAAC,KAAc,QAAQ,KAAK;YACzC,OAAO,MAAM,cAAc,KAAK,SAAU,WAAW,cAAc,UAAU,SAAS,QAAQ,OAAO,CAAC;QACxG;QACA,0CAA0C;QAC1C,cAAc,CAAC,MAAgB,UAAU,GAAG,CAAC,MAAM,WAAW;QAC9D,oCAAoC;QACpC,OAAO;IACT;AACF;GApPgB;;QAYM,qIAAA,CAAA,UAAO;;;AA2OtB,MAAM,YAAY;IACvB,KAAK,CAAC,MAAgB,UAAU,GAAG,CAAC,MAAM,WAAW;IAErD,KAAK,CAAC,KAAa;QACjB,IAAI,CAAC,OAAO,YAAY,QAAQ,YAAY,WAAW;QAEvD,UAAU,GAAG,CAAC,KAAK;YACjB;YACA,WAAW,KAAK,GAAG;YACnB,MAAM,OAAO,YAAY,YAAY,QAAQ,UAAU,CAAC,WAAW,QAAQ;QAC7E;IACF;IAEA,KAAK,CAAC,MAAgB,UAAU,GAAG,CAAC;IAEpC,OAAO,IAAM,UAAU,KAAK;IAE5B,QAAQ,CAAC,MAAgB,UAAU,MAAM,CAAC;IAE1C,gEAAgE;IAChE,wBAAwB,CAAC;QACvB,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,MAAM,KAAK,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG,MAAM;QAEnD,yBAAyB;QACzB,IAAI,0IAA0I,IAAI,CAAC,MAAM;YACvJ,OAAO;QACT;QAEA,aAAa;QACb,IAAI,QAAQ,QAAQ,OAAO;QAE3B,kBAAkB;QAClB,OAAO;IACT;IAEA,iDAAiD;IACjD,aAAa,CAAC;QACZ,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,IAAI,iBAAiB;QACpD,OAAO;YAAC;YAAO;YAAO;YAAQ;YAAO;YAAO;YAAQ;YAAO;SAAM,CAAC,QAAQ,CAAC;IAC7E;IAEA,8CAA8C;IAC9C,WAAW,CAAC;QACV,OAAO,KAAK,WAAW,GAAG,QAAQ,CAAC;IACrC;IAEA,gDAAgD;IAChD,QAAQ,CAAC;QACP,OAAO,iBAAiB;IAC1B;IAEA,6DAA6D;IAC7D,gBAAgB,CAAC,MAAc;QAC7B,OAAO,eAAe,UAAU,sBAAsB,CAAC;IACzD;IAEA,qDAAqD;IACrD,qBAAqB,CAAC;QACpB,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,IAAI,iBAAiB;QAEpD,mBAAmB;QACnB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAO,OAAO;YAEnB,iBAAiB;YACjB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAO,OAAO;YACnB,KAAK;YACL,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAO,OAAO;YAEnB,WAAW;YACX,KAAK;gBAAO,OAAO;YAEnB,UAAU;YACV;gBAAS,OAAO;QAClB;IACF;IAEA,0CAA0C;IAC1C,SAAS,OAAO,WAAmB,WAAqB;QACtD,IAAI,CAAC,OAAO;YACV,qDAAqD;YACrD,IAAI;gBACF,MAAM,cAAc,aAAa,OAAO,CAAC;gBACzC,IAAI,aAAa;oBACf,MAAM,UAAU,KAAK,KAAK,CAAC;oBAC3B,QAAQ,QAAQ,YAAY;gBAC9B;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,+CAA+C;YAC/D;QACF;QAEA,uDAAuD;QACvD,IAAI,CAAC,OAAO;YACV,QAAQ,IAAI,CAAC;YACb,OAAO,EAAE;QACX;QAEA,6BAA6B;QAC7B,MAAM,kBAAkB;eAAI,IAAI,IAAI;SAAW;QAE/C,IAAI,gBAAgB,MAAM,GAAG,UAAU,MAAM,EAAE;YAC7C,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,UAAU,MAAM,GAAG,gBAAgB,MAAM,CAAC,qBAAqB,CAAC;QACtG;QAEA,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,gBAAgB,MAAM,CAAC,mBAAmB,EAAE,WAAW;QAE9F,kDAAkD;QAClD,MAAM,kBAAkB,gBAAgB,GAAG,CAAC,OAAO;YACjD,2CAA2C;YAC3C,OAAO,KAAK,OAAO,CAAC,wBAAwB,CAAC,GAAG;gBAC9C,OAAO,OAAO,YAAY,CAAC,SAAS,SAAS;YAC/C;YAEA,MAAM,iBAAiB,cAAc;YACrC,MAAM,MAAM,YAAY,WAAW;YAEnC,yBAAyB;YACzB,IAAI,UAAU,GAAG,CAAC,MAAM;gBACtB,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,gBAAgB;gBAC5D,OAAO,UAAU,GAAG,CAAC,MAAM;YAC7B;YAEA,gDAAgD;YAChD,MAAM,aAAa,GAAG,UAAU,CAAC,EAAE,gBAAgB;YACnD,IAAI,mBAAmB,GAAG,CAAC,aAAa;gBACtC,QAAQ,GAAG,CAAC,CAAC,8CAA8C,EAAE,gBAAgB;gBAC7E,OAAO,mBAAmB,GAAG,CAAC;YAChC;YAEA,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,gBAAgB;YAE7D,iDAAiD;YACjD,MAAM,iBAAiB,CAAC;gBACtB,IAAI;oBACF,MAAM,MAAM,IAAI,IAAI,iEAAuC,WAAW,EAAE,UAAU,cAAc,CAAC;oBAEjG,uDAAuD;oBACvD,IAAI,YAAY,CAAC,MAAM,CAAC,QAAQ;oBAEhC,MAAM,eAAe,OAAO,UAAmB,KAAK;wBAClD,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ,IAAI;4BAC3C,SAAS;gCACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;4BACpC;wBACF;wBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;4BAChB,MAAM,eAAe,MAAM,SAAS,IAAI;4BACxC,MAAM,eAAe,CAAC,wBAAwB,EAAE,SAAS,MAAM,EAAE;4BAEjE,+EAA+E;4BAC/E,MAAM,wBAAwB,aAAa,QAAQ,CAAC;4BACpD,IAAI,yBAAyB,CAAC,SAAS;gCACrC,QAAQ,GAAG,CAAC,CAAC,oEAAoE,EAAE,gBAAgB;gCACnG,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gCACjD,OAAO,aAAa;4BACtB;4BAEA,MAAM,IAAI,MAAM;wBAClB;wBAEA,OAAO;oBACT;oBAEA,MAAM,WAAW,MAAM;oBAEvB,0DAA0D;oBAC1D,MAAM,YAAY,KAAK,KAAK,CAAC,KAAK,GAAG,IAAI;oBACzC,IAAI;oBACJ,IAAI,OAA0B;oBAE9B,2EAA2E;oBAC3E,MAAM,eAAe;wBAAC;wBAAO;wBAAO;wBAAQ;wBAAO;wBAAO;wBAAO;wBAC5C;wBAAQ;wBAAO;wBAAQ;wBAAO;wBAAQ;wBACtC;wBAAO;wBAAO;qBAAM,CAAC,QAAQ,CAAC,aAAa;oBAEhE,IAAI,cAAc;wBAChB,MAAM,OAAO,MAAM,SAAS,IAAI;wBAEhC,IAAI,UAAU,WAAW,CAAC,OAAO;4BAC/B,iCAAiC;4BACjC,UAAU;4BACV,OAAO;4BACP,QAAQ,GAAG,CAAC,CAAC,gDAAgD,EAAE,eAAe,EAAE,EAAE,KAAK,IAAI,CAAC,OAAO,CAAC;wBACtG,OAAO,IAAI,cAAc,SAAS;4BAAC;4BAAQ;4BAAO;4BAAQ;4BAAO;4BAAQ;yBAAM,CAAC,QAAQ,CAAC,aAAa,KAAK;4BACzG,sFAAsF;4BACtF,MAAM,WAAW,UAAU,mBAAmB,CAAC;4BAC/C,MAAM,aAAa,IAAI,KAAK;gCAAC;6BAAK,EAAE;gCAAE,MAAM;4BAAS;4BACrD,UAAU;4BACV,OAAO;4BACP,QAAQ,GAAG,CAAC,CAAC,oDAAoD,EAAE,UAAU,OAAO,EAAE,eAAe,EAAE,EAAE,KAAK,IAAI,CAAC,OAAO,CAAC;wBAC7H,OAAO;4BACL,wCAAwC;4BACxC,UAAU,IAAI,eAAe,CAAC;4BAC9B,OAAO;4BACP,QAAQ,GAAG,CAAC,CAAC,8CAA8C,EAAE,eAAe,EAAE,EAAE,KAAK,IAAI,CAAC,OAAO,CAAC;wBACpG;oBACF,OAEK,IAAI,cAAc,QAAQ;wBAC7B,UAAU,MAAM,SAAS,IAAI;wBAC7B,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,gBAAgB;oBAC3E,OAEK;wBACH,UAAU,MAAM,SAAS,IAAI;wBAC7B,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,eAAe,EAAE,EAAE,QAAQ,MAAM,CAAC,OAAO,CAAC;oBACrG;oBAEA,UAAU,GAAG,CAAC,KAAK;wBACjB;wBACA,WAAW,KAAK,GAAG;wBACnB;oBACF;oBAEA,OAAO;gBACT,EAAE,OAAO,KAAK;oBACZ,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,eAAe,CAAC,CAAC,EAAE;oBACnE,UAAU,GAAG,CAAC,KAAK;wBACjB,SAAS;wBACT,WAAW,KAAK,GAAG;wBACnB,MAAM;oBACR;oBACA,OAAO;gBACT,SAAU;oBACR,wCAAwC;oBACxC,mBAAmB,MAAM,CAAC;gBAC5B;YACF,CAAC;YAED,2CAA2C;YAC3C,mBAAmB,GAAG,CAAC,YAAY;YAEnC,OAAO;QACT;QAEA,OAAO,QAAQ,GAAG,CAAC;IACrB;IAEA,0DAA0D;IAC1D,aAAa,CAAC;QACZ,iDAAiD;QACjD,OAAO,UAAU,mBAAmB,CAAC;IACvC;AACF;AAGO,eAAe,cACpB,SAAiB,EACjB,QAAgB,EAChB,UAII,CAAC,CAAC;IAEN,IAAI,CAAC,YAAY,CAAC,WAAW,OAAO;IAEpC,sCAAsC;IACtC,MAAM,iBAAiB,cAAc;IACrC,MAAM,MAAM,YAAY,WAAW;IAEnC,qCAAqC;IACrC,MAAM,eAAe,UAAU,sBAAsB,CAAC,cAAc;IACpE,MAAM,gBAAgB,eAAe,SAAU,QAAQ,WAAW,IAAI;IAEtE,mDAAmD;IACnD,IAAI,CAAC,QAAQ,KAAK,IAAI,UAAU,GAAG,CAAC,MAAM;QACxC,MAAM,SAAS,UAAU,GAAG,CAAC;QAC7B,IAAI,UAAU,OAAO,IAAI,KAAK,SAAS,OAAO,OAAO,OAAO;IAC9D;IAEA,sBAAsB;IACtB,IAAI;QACF,MAAM,MAAM,IAAI,IAAI,iEAAuC,WAAW,EAAE,UAAU,cAAc,CAAC;QACjG,IAAI,YAAY,CAAC,MAAM,CAAC,QAAQ;QAEhC,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,IAAI,QAAQ,IAAI;QAE3D,MAAM,eAAe,OAAO,UAAmB,KAAK;YAClD,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ,IAAI;gBAC3C,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,KAAK,EAAE;gBAC5C;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,eAAe,MAAM,SAAS,IAAI;gBACxC,MAAM,eAAe,CAAC,qBAAqB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;gBAErF,+EAA+E;gBAC/E,MAAM,wBAAwB,aAAa,QAAQ,CAAC;gBACpD,IAAI,yBAAyB,CAAC,SAAS;oBACrC,QAAQ,GAAG,CAAC,CAAC,qDAAqD,EAAE,gBAAgB;oBACpF,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD,OAAO,aAAa;gBACtB;gBAEA,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,eAAe,SAAS,EAAE,SAAS,MAAM,EAAE;gBAC7F,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO;QACT;QAEA,MAAM,WAAW,MAAM;QAEvB,gCAAgC;QAChC,IAAI;QAEJ,IAAI,kBAAkB,QAAQ;YAC5B,UAAU,MAAM,SAAS,IAAI;QAC/B,OAAO,IAAI,kBAAkB,QAAQ;YACnC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,6CAA6C;YAC7C,MAAM,WAAW,UAAU,WAAW,CAAC;YACvC,IAAI,YAAY,aAAa,KAAK,IAAI,EAAE;gBACtC,UAAU,IAAI,KAAK;oBAAC;iBAAK,EAAE;oBAAE,MAAM;gBAAS;YAC9C,OAAO;gBACL,UAAU;YACZ;YAEA,2DAA2D;YAC3D,IAAI,UAAU,WAAW,CAAC,aAAa,UAAU,SAAS,CAAC,WAAW;gBACpE,0BAA0B;gBAC1B,UAAU,GAAG,CAAC,KAAK;gBAEnB,+BAA+B;gBAC/B,OAAO,IAAI,eAAe,CAAC;YAC7B;QACF,OAAO;YACL,UAAU,MAAM,SAAS,IAAI;QAC/B;QAEA,mBAAmB;QACnB,UAAU,GAAG,CAAC,KAAK;QACnB,OAAO;IAET,EAAE,OAAO,KAAK;QACZ,kBAAkB;QAClB,UAAU,GAAG,CAAC,KAAK;QACnB,MAAM;IACR;AACF;AAGO,eAAe,iBACpB,SAAiB,EACjB,QAAgB,EAChB,OAGC;IAED,MAAM,EAAE,cAAc,MAAM,EAAE,KAAK,EAAE,GAAG;IAExC,wBAAwB;IACxB,MAAM,YAAY,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;IAC1D,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,UAAU,CAAC,EAAE,UAAU;IAE9E,MAAM,eAAe,OAAO,UAAmB,KAAK;QAClD,IAAI;YACF,sBAAsB;YACtB,MAAM,SAAS,iEAAuC,WAAW,EAAE,UAAU,cAAc,CAAC;YAC5F,MAAM,MAAM,IAAI,IAAI;YACpB,IAAI,YAAY,CAAC,MAAM,CAAC,QAAQ;YAEhC,uBAAuB;YACvB,MAAM,eAA4B;gBAChC,QAAQ;gBACR,SAAS;oBACP,eAAe,CAAC,OAAO,EAAE,OAAO;gBAClC;YACF;YAEA,gBAAgB;YAChB,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ,IAAI;YAC7C,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,8BAA8B,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,WAAW;YACjF;YAEA,gEAAgE;YAChE,0EAA0E;YAC1E,MAAM,YAAY,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI;YAC7C,MAAM,eAAe;gBAAC;gBAAQ;gBAAO;gBAAQ;gBAAO;gBAAQ;gBAAO;gBAAO;gBAAO;gBAAO;gBAAQ;gBAAO;aAAM,CAAC,QAAQ,CAAC,aAAa;YAEpI,wCAAwC;YACxC,IAAI,gBAAgB,UAAU,cAAc;gBAC1C,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,6CAA6C;gBAC7C,IAAI,WAAW;oBACb,MAAM,WAAW,UAAU,WAAW,CAAC;oBACvC,IAAI,YAAY,aAAa,KAAK,IAAI,EAAE;wBACtC,sCAAsC;wBACtC,OAAO,IAAI,KAAK;4BAAC;yBAAK,EAAE;4BAAE,MAAM;wBAAS;oBAC3C;gBACF;gBAEA,OAAO;YACT,OAAO,IAAI,gBAAgB,QAAQ;gBACjC,OAAO,MAAM,SAAS,IAAI;YAC5B,OAAO;gBACL,OAAO,MAAM,SAAS,IAAI;YAC5B;QACF,EAAE,OAAO,OAAY;YACnB,+EAA+E;YAC/E,MAAM,wBAAwB,MAAM,OAAO,EAAE,SAAS;YACtD,IAAI,yBAAyB,CAAC,SAAS;gBACrC,QAAQ,GAAG,CAAC,CAAC,qDAAqD,EAAE,UAAU;gBAC9E,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD,OAAO,aAAa;YACtB;YACA,MAAM;QACR;IACF;IAEA,IAAI;QACF,OAAO,MAAM;IACf,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,yCAAyC,CAAC,EAAE;QAC3D,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 1454, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/files/use-file-queries.ts"], "sourcesContent": ["import React from 'react';\r\nimport { useQuery, useQueryClient } from '@tanstack/react-query';\r\nimport { useAuth } from '@/components/AuthProvider';\r\nimport { listSandboxFiles, type FileInfo } from '@/lib/api';\r\n\r\n// Re-export FileCache utilities for compatibility\r\nexport { FileCache } from '@/hooks/use-cached-file';\r\n\r\n/**\r\n * Normalize a file path to ensure consistent caching\r\n */\r\nfunction normalizePath(path: string): string {\r\n  if (!path) return '/workspace';\r\n  \r\n  // Ensure path starts with /workspace\r\n  if (!path.startsWith('/workspace')) {\r\n    path = `/workspace/${path.startsWith('/') ? path.substring(1) : path}`;\r\n  }\r\n  \r\n  // Handle Unicode escape sequences\r\n  try {\r\n    path = path.replace(/\\\\u([0-9a-fA-F]{4})/g, (_, hexCode) => {\r\n      return String.fromCharCode(parseInt(hexCode, 16));\r\n    });\r\n  } catch (e) {\r\n    console.error('Error processing Unicode escapes in path:', e);\r\n  }\r\n  \r\n  return path;\r\n}\r\n\r\n/**\r\n * Generate React Query keys for file operations\r\n */\r\nexport const fileQueryKeys = {\r\n  all: ['files'] as const,\r\n  contents: () => [...fileQueryKeys.all, 'content'] as const,\r\n  content: (sandboxId: string, path: string, contentType: string) => \r\n    [...fileQueryKeys.contents(), sandboxId, normalizePath(path), contentType] as const,\r\n  directories: () => [...fileQueryKeys.all, 'directory'] as const,\r\n  directory: (sandboxId: string, path: string) => \r\n    [...fileQueryKeys.directories(), sandboxId, normalizePath(path)] as const,\r\n};\r\n\r\n/**\r\n * Determine content type from file path\r\n */\r\nfunction getContentTypeFromPath(path: string): 'text' | 'blob' | 'json' {\r\n  if (!path) return 'text';\r\n  \r\n  const ext = path.toLowerCase().split('.').pop() || '';\r\n  \r\n  // Binary file extensions\r\n  if (/^(xlsx|xls|docx|doc|pptx|ppt|pdf|png|jpg|jpeg|gif|bmp|webp|svg|ico|zip|exe|dll|bin|dat|obj|o|so|dylib|mp3|mp4|avi|mov|wmv|flv|wav|ogg)$/.test(ext)) {\r\n    return 'blob';\r\n  }\r\n  \r\n  // JSON files\r\n  if (ext === 'json') return 'json';\r\n  \r\n  // Default to text\r\n  return 'text';\r\n}\r\n\r\n/**\r\n * Check if a file is an image\r\n */\r\nfunction isImageFile(path: string): boolean {\r\n  const ext = path.split('.').pop()?.toLowerCase() || '';\r\n  return ['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp', 'bmp', 'ico'].includes(ext);\r\n}\r\n\r\n/**\r\n * Check if a file is a PDF\r\n */\r\nfunction isPdfFile(path: string): boolean {\r\n  return path.toLowerCase().endsWith('.pdf');\r\n}\r\n\r\n/**\r\n * Get MIME type from file path\r\n */\r\nfunction getMimeTypeFromPath(path: string): string {\r\n  const ext = path.split('.').pop()?.toLowerCase() || '';\r\n  \r\n  switch (ext) {\r\n    case 'xlsx': return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';\r\n    case 'xls': return 'application/vnd.ms-excel';\r\n    case 'docx': return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';\r\n    case 'doc': return 'application/msword';\r\n    case 'pptx': return 'application/vnd.openxmlformats-officedocument.presentationml.presentation';\r\n    case 'ppt': return 'application/vnd.ms-powerpoint';\r\n    case 'pdf': return 'application/pdf';\r\n    case 'png': return 'image/png';\r\n    case 'jpg': \r\n    case 'jpeg': return 'image/jpeg';\r\n    case 'gif': return 'image/gif';\r\n    case 'svg': return 'image/svg+xml';\r\n    case 'zip': return 'application/zip';\r\n    default: return 'application/octet-stream';\r\n  }\r\n}\r\n\r\n/**\r\n * Fetch file content with proper error handling and content type detection\r\n */\r\nexport async function fetchFileContent(\r\n  sandboxId: string,\r\n  filePath: string,\r\n  contentType: 'text' | 'blob' | 'json',\r\n  token: string\r\n): Promise<string | Blob | any> {\r\n  const normalizedPath = normalizePath(filePath);\r\n  \r\n  const url = new URL(`${process.env.NEXT_PUBLIC_BACKEND_URL}/sandboxes/${sandboxId}/files/content`);\r\n  url.searchParams.append('path', normalizedPath);\r\n  \r\n  console.log(`[FILE QUERY] Fetching ${contentType} content for: ${normalizedPath}`);\r\n  \r\n  const headers: Record<string, string> = {};\r\n  if (token) {\r\n    headers['Authorization'] = `Bearer ${token}`;\r\n  }\r\n  \r\n  const response = await fetch(url.toString(), {\r\n    headers,\r\n  });\r\n  \r\n  if (!response.ok) {\r\n    const errorText = await response.text();\r\n    throw new Error(`Failed to fetch file: ${response.status} ${errorText}`);\r\n  }\r\n  \r\n  // Handle content based on type\r\n  switch (contentType) {\r\n    case 'json':\r\n      return await response.json();\r\n    case 'blob': {\r\n      const blob = await response.blob();\r\n      \r\n      // Ensure correct MIME type for known file types\r\n      const expectedMimeType = getMimeTypeFromPath(filePath);\r\n      if (expectedMimeType !== blob.type && expectedMimeType !== 'application/octet-stream') {\r\n        console.log(`[FILE QUERY] Correcting MIME type for ${filePath}: ${blob.type} → ${expectedMimeType}`);\r\n        const correctedBlob = new Blob([blob], { type: expectedMimeType });\r\n        \r\n        // Additional validation for images\r\n        if (isImageFile(filePath)) {\r\n          console.log(`[FILE QUERY] Created image blob:`, {\r\n            originalType: blob.type,\r\n            correctedType: correctedBlob.type,\r\n            size: correctedBlob.size,\r\n            filePath\r\n          });\r\n        }\r\n        \r\n        return correctedBlob;\r\n      }\r\n      \r\n      // Log blob details for debugging\r\n      if (isImageFile(filePath)) {\r\n        console.log(`[FILE QUERY] Image blob details:`, {\r\n          type: blob.type,\r\n          size: blob.size,\r\n          filePath\r\n        });\r\n      }\r\n      \r\n      return blob;\r\n    }\r\n    case 'text':\r\n    default:\r\n      return await response.text();\r\n  }\r\n}\r\n\r\n/**\r\n * Legacy compatibility function for getCachedFile\r\n */\r\nexport async function getCachedFile(\r\n  sandboxId: string,\r\n  filePath: string,\r\n  options: {\r\n    contentType?: 'text' | 'blob' | 'json';\r\n    force?: boolean;\r\n    token?: string;\r\n  } = {}\r\n): Promise<any> {\r\n  const normalizedPath = normalizePath(filePath);\r\n  const detectedContentType = getContentTypeFromPath(filePath);\r\n  const effectiveContentType = options.contentType || detectedContentType;\r\n  \r\n  if (!options.token) {\r\n    throw new Error('Authentication token required');\r\n  }\r\n  \r\n  return fetchFileContent(sandboxId, normalizedPath, effectiveContentType, options.token);\r\n}\r\n\r\n/**\r\n * Hook for fetching file content with React Query\r\n * Returns raw content - components create blob URLs as needed\r\n */\r\nexport function useFileContentQuery(\r\n  sandboxId?: string,\r\n  filePath?: string,\r\n  options: {\r\n    contentType?: 'text' | 'blob' | 'json';\r\n    enabled?: boolean;\r\n    staleTime?: number;\r\n    gcTime?: number;\r\n  } = {}\r\n) {\r\n  const { session } = useAuth();\r\n  \r\n  const normalizedPath = filePath ? normalizePath(filePath) : null;\r\n  const detectedContentType = filePath ? getContentTypeFromPath(filePath) : 'text';\r\n  const effectiveContentType = options.contentType || detectedContentType;\r\n  \r\n  const queryResult = useQuery({\r\n    queryKey: sandboxId && normalizedPath ? \r\n      fileQueryKeys.content(sandboxId, normalizedPath, effectiveContentType) : [],\r\n    queryFn: async () => {\r\n      if (!sandboxId || !normalizedPath) {\r\n        throw new Error('Missing required parameters');\r\n      }\r\n      \r\n      return fetchFileContent(sandboxId, normalizedPath, effectiveContentType, session?.access_token || '');\r\n    },\r\n    enabled: Boolean(sandboxId && normalizedPath && (options.enabled !== false)),\r\n    staleTime: options.staleTime || (effectiveContentType === 'blob' ? 5 * 60 * 1000 : 2 * 60 * 1000), // 5min for blobs, 2min for text\r\n    gcTime: options.gcTime || 10 * 60 * 1000, // 10 minutes\r\n    retry: (failureCount, error: any) => {\r\n      // Don't retry on auth errors\r\n      if (error?.message?.includes('401') || error?.message?.includes('403')) {\r\n        return false;\r\n      }\r\n      return failureCount < 3;\r\n    },\r\n  });\r\n  \r\n  const queryClient = useQueryClient();\r\n  \r\n  // Refresh function\r\n  const refreshCache = React.useCallback(async () => {\r\n    if (!sandboxId || !filePath) return null;\r\n    \r\n    const normalizedPath = normalizePath(filePath);\r\n    const queryKey = fileQueryKeys.content(sandboxId, normalizedPath, effectiveContentType);\r\n    \r\n    await queryClient.invalidateQueries({ queryKey });\r\n    const newData = queryClient.getQueryData(queryKey);\r\n    return newData || null;\r\n  }, [sandboxId, filePath, effectiveContentType, queryClient]);\r\n  \r\n  return {\r\n    ...queryResult,\r\n    refreshCache,\r\n    // Legacy compatibility methods\r\n    getCachedFile: () => Promise.resolve(queryResult.data),\r\n    getFromCache: () => queryResult.data,\r\n    cache: new Map(), // Legacy compatibility - empty map\r\n  };\r\n}\r\n\r\n/**\r\n * Hook for fetching directory listings\r\n */\r\nexport function useDirectoryQuery(\r\n  sandboxId?: string,\r\n  directoryPath?: string,\r\n  options: {\r\n    enabled?: boolean;\r\n    staleTime?: number;\r\n  } = {}\r\n) {\r\n  const { session } = useAuth();\r\n  \r\n  const normalizedPath = directoryPath ? normalizePath(directoryPath) : null;\r\n  \r\n  return useQuery({\r\n    queryKey: sandboxId && normalizedPath ? \r\n      fileQueryKeys.directory(sandboxId, normalizedPath) : [],\r\n    queryFn: async (): Promise<FileInfo[]> => {\r\n      if (!sandboxId || !normalizedPath) {\r\n        throw new Error('Missing required parameters');\r\n      }\r\n      \r\n      console.log(`[FILE QUERY] Fetching directory listing for: ${normalizedPath}`);\r\n      return await listSandboxFiles(sandboxId, normalizedPath);\r\n    },\r\n    enabled: Boolean(sandboxId && normalizedPath && (options.enabled !== false)),\r\n    staleTime: options.staleTime || 30 * 1000, // 30 seconds for directory listings\r\n    gcTime: 5 * 60 * 1000, // 5 minutes\r\n    retry: 2,\r\n  });\r\n}\r\n\r\n/**\r\n * Hook for preloading multiple files\r\n */\r\nexport function useFilePreloader() {\r\n  const queryClient = useQueryClient();\r\n  const { session } = useAuth();\r\n  \r\n  const preloadFiles = React.useCallback(async (\r\n    sandboxId: string,\r\n    filePaths: string[]\r\n  ): Promise<void> => {\r\n    if (!session?.access_token) {\r\n      console.warn('Cannot preload files: No authentication token available');\r\n      return;\r\n    }\r\n    \r\n    const uniquePaths = [...new Set(filePaths)];\r\n    console.log(`[FILE QUERY] Preloading ${uniquePaths.length} files for sandbox ${sandboxId}`);\r\n    \r\n    const preloadPromises = uniquePaths.map(async (path) => {\r\n      const normalizedPath = normalizePath(path);\r\n      const contentType = getContentTypeFromPath(path);\r\n      \r\n      // Check if already cached\r\n      const queryKey = fileQueryKeys.content(sandboxId, normalizedPath, contentType);\r\n      const existingData = queryClient.getQueryData(queryKey);\r\n      \r\n      if (existingData) {\r\n        console.log(`[FILE QUERY] Already cached: ${normalizedPath}`);\r\n        return existingData;\r\n      }\r\n      \r\n      // Prefetch the file\r\n      return queryClient.prefetchQuery({\r\n        queryKey,\r\n        queryFn: () => fetchFileContent(sandboxId, normalizedPath, contentType, session.access_token!),\r\n        staleTime: contentType === 'blob' ? 5 * 60 * 1000 : 2 * 60 * 1000,\r\n      });\r\n    });\r\n    \r\n    await Promise.all(preloadPromises);\r\n    console.log(`[FILE QUERY] Completed preloading ${uniquePaths.length} files`);\r\n  }, [queryClient, session?.access_token]);\r\n  \r\n  return { preloadFiles };\r\n}\r\n\r\n/**\r\n * Compatibility hook that mimics the old useCachedFile API\r\n */\r\nexport function useCachedFile<T = string>(\r\n  sandboxId?: string,\r\n  filePath?: string,\r\n  options: {\r\n    expiration?: number;\r\n    contentType?: 'json' | 'text' | 'blob' | 'arrayBuffer' | 'base64';\r\n    processFn?: (data: any) => T;\r\n  } = {}\r\n) {\r\n  // Map old contentType values to new ones\r\n  const mappedContentType = React.useMemo(() => {\r\n    switch (options.contentType) {\r\n      case 'json': return 'json';\r\n      case 'blob':\r\n      case 'arrayBuffer':\r\n      case 'base64': return 'blob';\r\n      case 'text':\r\n      default: return 'text';\r\n    }\r\n  }, [options.contentType]);\r\n  \r\n  const query = useFileContentQuery(sandboxId, filePath, {\r\n    contentType: mappedContentType,\r\n    staleTime: options.expiration,\r\n  });\r\n  \r\n  // Process data if processFn is provided\r\n  const processedData = React.useMemo(() => {\r\n    if (!query.data || !options.processFn) {\r\n      return query.data as T;\r\n    }\r\n    \r\n    try {\r\n      return options.processFn(query.data);\r\n    } catch (error) {\r\n      console.error('Error processing file data:', error);\r\n      return null;\r\n    }\r\n  }, [query.data, options.processFn]);\r\n  \r\n  return {\r\n    data: processedData,\r\n    isLoading: query.isLoading,\r\n    error: query.error,\r\n    refreshCache: query.refreshCache,\r\n    // Legacy compatibility methods\r\n    getCachedFile: () => Promise.resolve(processedData),\r\n    getFromCache: () => processedData,\r\n    cache: new Map(), // Legacy compatibility - empty map\r\n  };\r\n} "], "names": [], "mappings": ";;;;;;;;;AAkHyB;AAlHzB;AACA;AAAA;AACA;AACA;AAEA,kDAAkD;AAClD;;;;;;;AAEA;;CAEC,GACD,SAAS,cAAc,IAAY;IACjC,IAAI,CAAC,MAAM,OAAO;IAElB,qCAAqC;IACrC,IAAI,CAAC,KAAK,UAAU,CAAC,eAAe;QAClC,OAAO,CAAC,WAAW,EAAE,KAAK,UAAU,CAAC,OAAO,KAAK,SAAS,CAAC,KAAK,MAAM;IACxE;IAEA,kCAAkC;IAClC,IAAI;QACF,OAAO,KAAK,OAAO,CAAC,wBAAwB,CAAC,GAAG;YAC9C,OAAO,OAAO,YAAY,CAAC,SAAS,SAAS;QAC/C;IACF,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,6CAA6C;IAC7D;IAEA,OAAO;AACT;AAKO,MAAM,gBAAgB;IAC3B,KAAK;QAAC;KAAQ;IACd,UAAU,IAAM;eAAI,cAAc,GAAG;YAAE;SAAU;IACjD,SAAS,CAAC,WAAmB,MAAc,cACzC;eAAI,cAAc,QAAQ;YAAI;YAAW,cAAc;YAAO;SAAY;IAC5E,aAAa,IAAM;eAAI,cAAc,GAAG;YAAE;SAAY;IACtD,WAAW,CAAC,WAAmB,OAC7B;eAAI,cAAc,WAAW;YAAI;YAAW,cAAc;SAAM;AACpE;AAEA;;CAEC,GACD,SAAS,uBAAuB,IAAY;IAC1C,IAAI,CAAC,MAAM,OAAO;IAElB,MAAM,MAAM,KAAK,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG,MAAM;IAEnD,yBAAyB;IACzB,IAAI,0IAA0I,IAAI,CAAC,MAAM;QACvJ,OAAO;IACT;IAEA,aAAa;IACb,IAAI,QAAQ,QAAQ,OAAO;IAE3B,kBAAkB;IAClB,OAAO;AACT;AAEA;;CAEC,GACD,SAAS,YAAY,IAAY;IAC/B,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,IAAI,iBAAiB;IACpD,OAAO;QAAC;QAAO;QAAO;QAAQ;QAAO;QAAO;QAAQ;QAAO;KAAM,CAAC,QAAQ,CAAC;AAC7E;AAEA;;CAEC,GACD,SAAS,UAAU,IAAY;IAC7B,OAAO,KAAK,WAAW,GAAG,QAAQ,CAAC;AACrC;AAEA;;CAEC,GACD,SAAS,oBAAoB,IAAY;IACvC,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,IAAI,iBAAiB;IAEpD,OAAQ;QACN,KAAK;YAAQ,OAAO;QACpB,KAAK;YAAO,OAAO;QACnB,KAAK;YAAQ,OAAO;QACpB,KAAK;YAAO,OAAO;QACnB,KAAK;YAAQ,OAAO;QACpB,KAAK;YAAO,OAAO;QACnB,KAAK;YAAO,OAAO;QACnB,KAAK;YAAO,OAAO;QACnB,KAAK;QACL,KAAK;YAAQ,OAAO;QACpB,KAAK;YAAO,OAAO;QACnB,KAAK;YAAO,OAAO;QACnB,KAAK;YAAO,OAAO;QACnB;YAAS,OAAO;IAClB;AACF;AAKO,eAAe,iBACpB,SAAiB,EACjB,QAAgB,EAChB,WAAqC,EACrC,KAAa;IAEb,MAAM,iBAAiB,cAAc;IAErC,MAAM,MAAM,IAAI,IAAI,iEAAuC,WAAW,EAAE,UAAU,cAAc,CAAC;IACjG,IAAI,YAAY,CAAC,MAAM,CAAC,QAAQ;IAEhC,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,YAAY,cAAc,EAAE,gBAAgB;IAEjF,MAAM,UAAkC,CAAC;IACzC,IAAI,OAAO;QACT,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IAC9C;IAEA,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ,IAAI;QAC3C;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,WAAW;IACzE;IAEA,+BAA+B;IAC/B,OAAQ;QACN,KAAK;YACH,OAAO,MAAM,SAAS,IAAI;QAC5B,KAAK;YAAQ;gBACX,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,gDAAgD;gBAChD,MAAM,mBAAmB,oBAAoB;gBAC7C,IAAI,qBAAqB,KAAK,IAAI,IAAI,qBAAqB,4BAA4B;oBACrF,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,SAAS,EAAE,EAAE,KAAK,IAAI,CAAC,GAAG,EAAE,kBAAkB;oBACnG,MAAM,gBAAgB,IAAI,KAAK;wBAAC;qBAAK,EAAE;wBAAE,MAAM;oBAAiB;oBAEhE,mCAAmC;oBACnC,IAAI,YAAY,WAAW;wBACzB,QAAQ,GAAG,CAAC,CAAC,gCAAgC,CAAC,EAAE;4BAC9C,cAAc,KAAK,IAAI;4BACvB,eAAe,cAAc,IAAI;4BACjC,MAAM,cAAc,IAAI;4BACxB;wBACF;oBACF;oBAEA,OAAO;gBACT;gBAEA,iCAAiC;gBACjC,IAAI,YAAY,WAAW;oBACzB,QAAQ,GAAG,CAAC,CAAC,gCAAgC,CAAC,EAAE;wBAC9C,MAAM,KAAK,IAAI;wBACf,MAAM,KAAK,IAAI;wBACf;oBACF;gBACF;gBAEA,OAAO;YACT;QACA,KAAK;QACL;YACE,OAAO,MAAM,SAAS,IAAI;IAC9B;AACF;AAKO,eAAe,cACpB,SAAiB,EACjB,QAAgB,EAChB,UAII,CAAC,CAAC;IAEN,MAAM,iBAAiB,cAAc;IACrC,MAAM,sBAAsB,uBAAuB;IACnD,MAAM,uBAAuB,QAAQ,WAAW,IAAI;IAEpD,IAAI,CAAC,QAAQ,KAAK,EAAE;QAClB,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO,iBAAiB,WAAW,gBAAgB,sBAAsB,QAAQ,KAAK;AACxF;AAMO,SAAS,oBACd,SAAkB,EAClB,QAAiB,EACjB,UAKI,CAAC,CAAC;;IAEN,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAE1B,MAAM,iBAAiB,WAAW,cAAc,YAAY;IAC5D,MAAM,sBAAsB,WAAW,uBAAuB,YAAY;IAC1E,MAAM,uBAAuB,QAAQ,WAAW,IAAI;IAEpD,MAAM,cAAc,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QAC3B,UAAU,aAAa,iBACrB,cAAc,OAAO,CAAC,WAAW,gBAAgB,wBAAwB,EAAE;QAC7E,OAAO;yDAAE;gBACP,IAAI,CAAC,aAAa,CAAC,gBAAgB;oBACjC,MAAM,IAAI,MAAM;gBAClB;gBAEA,OAAO,iBAAiB,WAAW,gBAAgB,sBAAsB,SAAS,gBAAgB;YACpG;;QACA,SAAS,QAAQ,aAAa,kBAAmB,QAAQ,OAAO,KAAK;QACrE,WAAW,QAAQ,SAAS,IAAI,CAAC,yBAAyB,SAAS,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI;QAChG,QAAQ,QAAQ,MAAM,IAAI,KAAK,KAAK;QACpC,KAAK;yDAAE,CAAC,cAAc;gBACpB,6BAA6B;gBAC7B,IAAI,OAAO,SAAS,SAAS,UAAU,OAAO,SAAS,SAAS,QAAQ;oBACtE,OAAO;gBACT;gBACA,OAAO,eAAe;YACxB;;IACF;IAEA,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,mBAAmB;IACnB,MAAM,eAAe,6JAAA,CAAA,UAAK,CAAC,WAAW;yDAAC;YACrC,IAAI,CAAC,aAAa,CAAC,UAAU,OAAO;YAEpC,MAAM,iBAAiB,cAAc;YACrC,MAAM,WAAW,cAAc,OAAO,CAAC,WAAW,gBAAgB;YAElE,MAAM,YAAY,iBAAiB,CAAC;gBAAE;YAAS;YAC/C,MAAM,UAAU,YAAY,YAAY,CAAC;YACzC,OAAO,WAAW;QACpB;wDAAG;QAAC;QAAW;QAAU;QAAsB;KAAY;IAE3D,OAAO;QACL,GAAG,WAAW;QACd;QACA,+BAA+B;QAC/B,eAAe,IAAM,QAAQ,OAAO,CAAC,YAAY,IAAI;QACrD,cAAc,IAAM,YAAY,IAAI;QACpC,OAAO,IAAI;IACb;AACF;GA5DgB;;QAUM,qIAAA,CAAA,UAAO;QAMP,8KAAA,CAAA,WAAQ;QAsBR,yLAAA,CAAA,iBAAc;;;AA2B7B,SAAS,kBACd,SAAkB,EAClB,aAAsB,EACtB,UAGI,CAAC,CAAC;;IAEN,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAE1B,MAAM,iBAAiB,gBAAgB,cAAc,iBAAiB;IAEtE,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,aAAa,iBACrB,cAAc,SAAS,CAAC,WAAW,kBAAkB,EAAE;QACzD,OAAO;0CAAE;gBACP,IAAI,CAAC,aAAa,CAAC,gBAAgB;oBACjC,MAAM,IAAI,MAAM;gBAClB;gBAEA,QAAQ,GAAG,CAAC,CAAC,6CAA6C,EAAE,gBAAgB;gBAC5E,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW;YAC3C;;QACA,SAAS,QAAQ,aAAa,kBAAmB,QAAQ,OAAO,KAAK;QACrE,WAAW,QAAQ,SAAS,IAAI,KAAK;QACrC,QAAQ,IAAI,KAAK;QACjB,OAAO;IACT;AACF;IA5BgB;;QAQM,qIAAA,CAAA,UAAO;QAIpB,8KAAA,CAAA,WAAQ;;;AAqBV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAE1B,MAAM,eAAe,6JAAA,CAAA,UAAK,CAAC,WAAW;sDAAC,OACrC,WACA;YAEA,IAAI,CAAC,SAAS,cAAc;gBAC1B,QAAQ,IAAI,CAAC;gBACb;YACF;YAEA,MAAM,cAAc;mBAAI,IAAI,IAAI;aAAW;YAC3C,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,YAAY,MAAM,CAAC,mBAAmB,EAAE,WAAW;YAE1F,MAAM,kBAAkB,YAAY,GAAG;8EAAC,OAAO;oBAC7C,MAAM,iBAAiB,cAAc;oBACrC,MAAM,cAAc,uBAAuB;oBAE3C,0BAA0B;oBAC1B,MAAM,WAAW,cAAc,OAAO,CAAC,WAAW,gBAAgB;oBAClE,MAAM,eAAe,YAAY,YAAY,CAAC;oBAE9C,IAAI,cAAc;wBAChB,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,gBAAgB;wBAC5D,OAAO;oBACT;oBAEA,oBAAoB;oBACpB,OAAO,YAAY,aAAa,CAAC;wBAC/B;wBACA,OAAO;0FAAE,IAAM,iBAAiB,WAAW,gBAAgB,aAAa,QAAQ,YAAY;;wBAC5F,WAAW,gBAAgB,SAAS,IAAI,KAAK,OAAO,IAAI,KAAK;oBAC/D;gBACF;;YAEA,MAAM,QAAQ,GAAG,CAAC;YAClB,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,YAAY,MAAM,CAAC,MAAM,CAAC;QAC7E;qDAAG;QAAC;QAAa,SAAS;KAAa;IAEvC,OAAO;QAAE;IAAa;AACxB;IA1CgB;;QACM,yLAAA,CAAA,iBAAc;QACd,qIAAA,CAAA,UAAO;;;AA6CtB,SAAS,cACd,SAAkB,EAClB,QAAiB,EACjB,UAII,CAAC,CAAC;;IAEN,yCAAyC;IACzC,MAAM,oBAAoB,6JAAA,CAAA,UAAK,CAAC,OAAO;oDAAC;YACtC,OAAQ,QAAQ,WAAW;gBACzB,KAAK;oBAAQ,OAAO;gBACpB,KAAK;gBACL,KAAK;gBACL,KAAK;oBAAU,OAAO;gBACtB,KAAK;gBACL;oBAAS,OAAO;YAClB;QACF;mDAAG;QAAC,QAAQ,WAAW;KAAC;IAExB,MAAM,QAAQ,oBAAoB,WAAW,UAAU;QACrD,aAAa;QACb,WAAW,QAAQ,UAAU;IAC/B;IAEA,wCAAwC;IACxC,MAAM,gBAAgB,6JAAA,CAAA,UAAK,CAAC,OAAO;gDAAC;YAClC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,QAAQ,SAAS,EAAE;gBACrC,OAAO,MAAM,IAAI;YACnB;YAEA,IAAI;gBACF,OAAO,QAAQ,SAAS,CAAC,MAAM,IAAI;YACrC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,OAAO;YACT;QACF;+CAAG;QAAC,MAAM,IAAI;QAAE,QAAQ,SAAS;KAAC;IAElC,OAAO;QACL,MAAM;QACN,WAAW,MAAM,SAAS;QAC1B,OAAO,MAAM,KAAK;QAClB,cAAc,MAAM,YAAY;QAChC,+BAA+B;QAC/B,eAAe,IAAM,QAAQ,OAAO,CAAC;QACrC,cAAc,IAAM;QACpB,OAAO,IAAI;IACb;AACF;IAlDgB;;QAqBA", "debugId": null}}, {"offset": {"line": 1875, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/transcription/use-transcription.ts"], "sourcesContent": ["import { createMutationHook } from '@/hooks/use-query';\r\nimport { transcribeAudio, TranscriptionResponse } from '@/lib/api';\r\n\r\nexport const useTranscription = createMutationHook<\r\n  TranscriptionResponse,\r\n  File\r\n>(\r\n  transcribeAudio,\r\n  {\r\n    errorContext: { operation: 'transcribe audio', resource: 'speech-to-text' },\r\n  }\r\n); "], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,mBAAmB,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EAI/C,oHAAA,CAAA,kBAAe,EACf;IACE,cAAc;QAAE,WAAW;QAAoB,UAAU;IAAiB;AAC5E", "debugId": null}}, {"offset": {"line": 1897, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/subscriptions/keys.ts"], "sourcesContent": ["import { createQueryKeys } from '@/hooks/use-query';\r\n\r\nconst subscriptionKeysBase = ['subscription'] as const;\r\nconst modelKeysBase = ['models'] as const;\r\nconst usageKeysBase = ['usage'] as const;\r\n\r\nexport const subscriptionKeys = createQueryKeys({\r\n  all: subscriptionKeysBase,\r\n  details: () => [...subscriptionKeysBase, 'details'] as const,\r\n});\r\n\r\nexport const modelKeys = createQueryKeys({\r\n  all: modelKeysBase,\r\n  available: ['models', 'available'] as const,\r\n});\r\n\r\nexport const usageKeys = createQueryKeys({\r\n  all: usageKeysBase,\r\n  logs: (page?: number, itemsPerPage?: number) => [...usageKeysBase, 'logs', { page, itemsPerPage }] as const,\r\n});"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,uBAAuB;IAAC;CAAe;AAC7C,MAAM,gBAAgB;IAAC;CAAS;AAChC,MAAM,gBAAgB;IAAC;CAAQ;AAExB,MAAM,mBAAmB,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAAE;IAC9C,KAAK;IACL,SAAS,IAAM;eAAI;YAAsB;SAAU;AACrD;AAEO,MAAM,YAAY,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAAE;IACvC,KAAK;IACL,WAAW;QAAC;QAAU;KAAY;AACpC;AAEO,MAAM,YAAY,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAAE;IACvC,KAAK;IACL,MAAM,CAAC,MAAe,eAA0B;eAAI;YAAe;YAAQ;gBAAE;gBAAM;YAAa;SAAE;AACpG", "debugId": null}}, {"offset": {"line": 1947, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/subscriptions/use-subscriptions.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { createMutationHook, createQueryHook } from '@/hooks/use-query';\r\nimport {\r\n  getSubscription,\r\n  createPortalSession,\r\n  SubscriptionStatus,\r\n} from '@/lib/api';\r\nimport { subscriptionKeys } from './keys';\r\n\r\nexport const useSubscription = createQueryHook(\r\n  subscriptionKeys.details(),\r\n  getSubscription,\r\n  {\r\n    staleTime: 1000 * 60 * 5,\r\n    refetchOnWindowFocus: true,\r\n  },\r\n);\r\n\r\nexport const useCreatePortalSession = createMutationHook(\r\n  (params: { return_url: string }) => createPortalSession(params),\r\n  {\r\n    onSuccess: (data) => {\r\n      if (data?.url) {\r\n        window.location.href = data.url;\r\n      }\r\n    },\r\n  },\r\n);\r\n\r\nexport const isPlan = (\r\n  subscriptionData: SubscriptionStatus | null | undefined,\r\n  planId?: string,\r\n): boolean => {\r\n  if (!subscriptionData) return planId === 'free';\r\n  return subscriptionData.plan_name === planId;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAKA;AARA;;;;AAUO,MAAM,kBAAkB,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAC3C,0JAAA,CAAA,mBAAgB,CAAC,OAAO,IACxB,oHAAA,CAAA,kBAAe,EACf;IACE,WAAW,OAAO,KAAK;IACvB,sBAAsB;AACxB;AAGK,MAAM,yBAAyB,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EACrD,CAAC,SAAmC,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE,SACxD;IACE,WAAW,CAAC;QACV,IAAI,MAAM,KAAK;YACb,OAAO,QAAQ,CAAC,IAAI,GAAG,KAAK,GAAG;QACjC;IACF;AACF;AAGK,MAAM,SAAS,CACpB,kBACA;IAEA,IAAI,CAAC,kBAAkB,OAAO,WAAW;IACzC,OAAO,iBAAiB,SAAS,KAAK;AACxC", "debugId": null}}, {"offset": {"line": 1983, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/subscriptions/use-model.ts"], "sourcesContent": ["import { createQueryHook } from \"@/hooks/use-query\";\r\nimport { AvailableModelsResponse, getAvailableModels } from \"@/lib/api\";\r\nimport { modelKeys } from \"./keys\";\r\n\r\nexport const useAvailableModels = createQueryHook<AvailableModelsResponse, Error>(\r\n    modelKeys.available,\r\n    getAvailableModels,\r\n    {\r\n      staleTime: 5 * 60 * 1000,\r\n      refetchOnWindowFocus: false,\r\n      retry: 2,\r\n      select: (data) => {\r\n        return {\r\n          ...data,\r\n          models: [...data.models].sort((a, b) => \r\n            a.display_name.localeCompare(b.display_name)\r\n          ),\r\n        };\r\n      },\r\n    }\r\n  );"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,MAAM,qBAAqB,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAC5C,0JAAA,CAAA,YAAS,CAAC,SAAS,EACnB,oHAAA,CAAA,qBAAkB,EAClB;IACE,WAAW,IAAI,KAAK;IACpB,sBAAsB;IACtB,OAAO;IACP,QAAQ,CAAC;QACP,OAAO;YACL,GAAG,IAAI;YACP,QAAQ;mBAAI,KAAK,MAAM;aAAC,CAAC,IAAI,CAAC,CAAC,GAAG,IAChC,EAAE,YAAY,CAAC,aAAa,CAAC,EAAE,YAAY;QAE/C;IACF;AACF", "debugId": null}}, {"offset": {"line": 2014, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/agents/keys.ts"], "sourcesContent": ["import { createQuery<PERSON>eys } from \"@/hooks/use-query\";\r\n\r\nconst agentKeysBase = ['agents'] as const;\r\n\r\nexport const agentKeys = createQueryKeys({\r\n  all: agentKeysBase,\r\n  lists: () => [...agentKeysBase, 'list'] as const,\r\n  list: (filters?: Record<string, any>) => [...agentKeysBase, 'list', filters] as const,\r\n  details: () => [...agentKeysBase, 'detail'] as const,\r\n  detail: (id: string) => [...agentKeysBase, 'detail', id] as const,\r\n  threadAgents: () => [...agentKeysBase, 'thread-agent'] as const,\r\n  threadAgent: (threadId: string) => [...agentKeysBase, 'thread-agent', threadId] as const,\r\n  builderChatHistory: (agentId: string) => [...agent<PERSON>eysBase, 'builderChatHistory', agentId] as const,\r\n});"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,gBAAgB;IAAC;CAAS;AAEzB,MAAM,YAAY,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAAE;IACvC,KAAK;IACL,OAAO,IAAM;eAAI;YAAe;SAAO;IACvC,MAAM,CAAC,UAAkC;eAAI;YAAe;YAAQ;SAAQ;IAC5E,SAAS,IAAM;eAAI;YAAe;SAAS;IAC3C,QAAQ,CAAC,KAAe;eAAI;YAAe;YAAU;SAAG;IACxD,cAAc,IAAM;eAAI;YAAe;SAAe;IACtD,aAAa,CAAC,WAAqB;eAAI;YAAe;YAAgB;SAAS;IAC/E,oBAAoB,CAAC,UAAoB;eAAI;YAAe;YAAsB;SAAQ;AAC5F", "debugId": null}}, {"offset": {"line": 2066, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/agents/utils.ts"], "sourcesContent": ["import { createClient } from \"@/lib/supabase/client\";\r\nimport { isFlagEnabled } from \"@/lib/feature-flags\";\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || '';\r\n\r\nexport type Agent = {\r\n  agent_id: string;\r\n  account_id: string;\r\n  name: string;\r\n  description?: string;\r\n  system_prompt: string;\r\n  configured_mcps: Array<{\r\n    name: string;\r\n    config: Record<string, any>;\r\n  }>;\r\n  custom_mcps?: Array<{\r\n    name: string;\r\n    type: 'json' | 'sse';\r\n    config: Record<string, any>;\r\n    enabledTools: string[];\r\n  }>;\r\n  agentpress_tools: Record<string, any>;\r\n  is_default: boolean;\r\n  is_public?: boolean;\r\n  marketplace_published_at?: string;\r\n  download_count?: number;\r\n  tags?: string[];\r\n  created_at: string;\r\n  updated_at: string;\r\n  avatar?: string;\r\n  avatar_color?: string;\r\n};\r\n\r\nexport type PaginationInfo = {\r\n  page: number;\r\n  limit: number;\r\n  total: number;\r\n  pages: number;\r\n};\r\n\r\nexport type AgentsResponse = {\r\n  agents: Agent[];\r\n  pagination: PaginationInfo;\r\n};\r\n\r\nexport type AgentsParams = {\r\n  page?: number;\r\n  limit?: number;\r\n  search?: string;\r\n  sort_by?: string;\r\n  sort_order?: string;\r\n  has_default?: boolean;\r\n  has_mcp_tools?: boolean;\r\n  has_agentpress_tools?: boolean;\r\n  tools?: string;\r\n};\r\n\r\nexport type ThreadAgentResponse = {\r\n  agent: Agent | null;\r\n  source: 'thread' | 'default' | 'none' | 'missing';\r\n  message: string;\r\n};\r\n\r\nexport type AgentCreateRequest = {\r\n  name: string;\r\n  description?: string;\r\n  system_prompt: string;\r\n  configured_mcps?: Array<{\r\n    name: string;\r\n    config: Record<string, any>;\r\n  }>;\r\n  custom_mcps?: Array<{\r\n    name: string;\r\n    type: 'json' | 'sse';\r\n    config: Record<string, any>;\r\n    enabledTools: string[];\r\n  }>;\r\n  agentpress_tools?: Record<string, any>;\r\n  is_default?: boolean;\r\n};\r\n\r\nexport type AgentVersionCreateRequest = {\r\n  system_prompt: string;\r\n  configured_mcps?: Array<{\r\n    name: string;\r\n    config: Record<string, any>;\r\n  }>;\r\n  custom_mcps?: Array<{\r\n    name: string;\r\n    type: 'json' | 'sse';\r\n    config: Record<string, any>;\r\n    enabledTools: string[];\r\n  }>;\r\n  agentpress_tools?: Record<string, any>;\r\n};\r\n\r\nexport type AgentVersion = {\r\n  version_id: string;\r\n  agent_id: string;\r\n  version_number: number;\r\n  version_name: string;\r\n  system_prompt: string;\r\n  configured_mcps: Array<any>;\r\n  custom_mcps: Array<any>;\r\n  agentpress_tools: Record<string, any>;\r\n  is_active: boolean;\r\n  created_at: string;\r\n  updated_at: string;\r\n  created_by?: string;\r\n};\r\n\r\nexport type AgentUpdateRequest = {\r\n  name?: string;\r\n  description?: string;\r\n  system_prompt?: string;\r\n  configured_mcps?: Array<{\r\n    name: string;\r\n    config: Record<string, any>;\r\n  }>;\r\n  custom_mcps?: Array<{\r\n    name: string;\r\n    type: 'json' | 'sse';\r\n    config: Record<string, any>;\r\n    enabledTools: string[];\r\n  }>;\r\n  agentpress_tools?: Record<string, any>;\r\n  is_default?: boolean;\r\n};\r\n\r\nexport const getAgents = async (params: AgentsParams = {}): Promise<AgentsResponse> => {\r\n  try {\r\n    const agentPlaygroundEnabled = await isFlagEnabled('custom_agents');\r\n    if (!agentPlaygroundEnabled) {\r\n      throw new Error('Custom agents is not enabled');\r\n    }\r\n    const supabase = createClient();\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n\r\n    if (!session) {\r\n      throw new Error('You must be logged in to get agents');\r\n    }\r\n\r\n    const queryParams = new URLSearchParams();\r\n    if (params.page) queryParams.append('page', params.page.toString());\r\n    if (params.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params.search) queryParams.append('search', params.search);\r\n    if (params.sort_by) queryParams.append('sort_by', params.sort_by);\r\n    if (params.sort_order) queryParams.append('sort_order', params.sort_order);\r\n    if (params.has_default !== undefined) queryParams.append('has_default', params.has_default.toString());\r\n    if (params.has_mcp_tools !== undefined) queryParams.append('has_mcp_tools', params.has_mcp_tools.toString());\r\n    if (params.has_agentpress_tools !== undefined) queryParams.append('has_agentpress_tools', params.has_agentpress_tools.toString());\r\n    if (params.tools) queryParams.append('tools', params.tools);\r\n\r\n    const url = `${API_URL}/agents${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;\r\n\r\n    const response = await fetch(url, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${session.access_token}`,\r\n      },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));\r\n      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);\r\n    }\r\n\r\n    const result = await response.json();\r\n    console.log('[API] Fetched agents:', result.agents?.length || 0, 'total:', result.pagination?.total || 0);\r\n    return result;\r\n  } catch (err) {\r\n    console.error('Error fetching agents:', err);\r\n    throw err;\r\n  }\r\n};\r\n\r\nexport const getAgent = async (agentId: string): Promise<Agent> => {\r\n  try {\r\n    const agentPlaygroundEnabled = await isFlagEnabled('custom_agents');\r\n    if (!agentPlaygroundEnabled) {\r\n      throw new Error('Custom agents is not enabled');\r\n    }\r\n    const supabase = createClient();\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n\r\n    if (!session) {\r\n      throw new Error('You must be logged in to get agent details');\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/agents/${agentId}`, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${session.access_token}`,\r\n      },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));\r\n      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);\r\n    }\r\n\r\n    const agent = await response.json();\r\n    console.log('[API] Fetched agent:', agent.agent_id);\r\n    return agent;\r\n  } catch (err) {\r\n    console.error('Error fetching agent:', err);\r\n    throw err;\r\n  }\r\n};\r\n\r\nexport const createAgent = async (agentData: AgentCreateRequest): Promise<Agent> => {\r\n  try {\r\n    const agentPlaygroundEnabled = await isFlagEnabled('custom_agents');\r\n    if (!agentPlaygroundEnabled) {\r\n      throw new Error('Custom agents is not enabled');\r\n    }\r\n    const supabase = createClient();\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n\r\n    if (!session) {\r\n      throw new Error('You must be logged in to create an agent');\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/agents`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${session.access_token}`,\r\n      },\r\n      body: JSON.stringify(agentData),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));\r\n      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);\r\n    }\r\n\r\n    const agent = await response.json();\r\n    console.log('[API] Created agent:', agent.agent_id);\r\n    return agent;\r\n  } catch (err) {\r\n    console.error('Error creating agent:', err);\r\n    throw err;\r\n  }\r\n};\r\n\r\nexport const updateAgent = async (agentId: string, agentData: AgentUpdateRequest): Promise<Agent> => {\r\n  try {\r\n    const agentPlaygroundEnabled = await isFlagEnabled('custom_agents');\r\n    if (!agentPlaygroundEnabled) {\r\n      throw new Error('Custom agents is not enabled');\r\n    }\r\n    const supabase = createClient();\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n\r\n    if (!session) {\r\n      throw new Error('You must be logged in to update an agent');\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/agents/${agentId}`, {\r\n      method: 'PUT',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${session.access_token}`,\r\n      },\r\n      body: JSON.stringify(agentData),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));\r\n      throw new Error(errorData.message || `HTTP ${response.status}: {response.statusText}`);\r\n    }\r\n\r\n    const agent = await response.json();\r\n    console.log('[API] Updated agent:', agent.agent_id);\r\n    return agent;\r\n  } catch (err) {\r\n    console.error('Error updating agent:', err);\r\n    throw err;\r\n  }\r\n};\r\n\r\nexport const deleteAgent = async (agentId: string): Promise<void> => {\r\n  try {\r\n    const agentPlaygroundEnabled = await isFlagEnabled('custom_agents');\r\n    if (!agentPlaygroundEnabled) {\r\n      throw new Error('Custom agents is not enabled');\r\n    }\r\n    const supabase = createClient();\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n\r\n    if (!session) {\r\n      throw new Error('You must be logged in to delete an agent');\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/agents/${agentId}`, {\r\n      method: 'DELETE',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${session.access_token}`,\r\n      },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));\r\n      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);\r\n    }\r\n\r\n    console.log('[API] Deleted agent:', agentId);\r\n  } catch (err) {\r\n    console.error('Error deleting agent:', err);\r\n    throw err;\r\n  }\r\n};\r\n\r\nexport const getThreadAgent = async (threadId: string): Promise<ThreadAgentResponse> => {\r\n  try {\r\n    const agentPlaygroundEnabled = await isFlagEnabled('custom_agents');\r\n    if (!agentPlaygroundEnabled) {\r\n      throw new Error('Custom agents is not enabled');\r\n    }\r\n    const supabase = createClient();\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n\r\n    if (!session) {\r\n      throw new Error('You must be logged in to get thread agent');\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/thread/${threadId}/agent`, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${session.access_token}`,\r\n      },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));\r\n      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);\r\n    }\r\n\r\n    const agent = await response.json();\r\n    console.log('[API] Fetched thread agent:', threadId);\r\n    return agent;\r\n  } catch (err) {\r\n    console.error('Error fetching thread agent:', err);\r\n    throw err;\r\n  }\r\n};\r\n\r\nexport const getAgentBuilderChatHistory = async (agentId: string): Promise<{messages: any[], thread_id: string | null}> => {\r\n  try {\r\n    const agentPlaygroundEnabled = await isFlagEnabled('custom_agents');\r\n    if (!agentPlaygroundEnabled) {\r\n      throw new Error('Custom agents is not enabled');\r\n    }\r\n    const supabase = createClient();\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n\r\n    if (!session) {\r\n      throw new Error('You must be logged in to get agent builder chat history');\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/agents/${agentId}/builder-chat-history`, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${session.access_token}`,\r\n      },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));\r\n      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n    console.log('[API] Fetched agent builder chat history:', agentId, data.messages.length);\r\n    return data;\r\n  } catch (err) {\r\n    console.error('Error fetching agent builder chat history:', err);\r\n    throw err;\r\n  }\r\n};\r\n\r\n// Agent Builder Chat Types\r\nexport type AgentBuilderMessage = {\r\n  role: 'user' | 'assistant';\r\n  content: string;\r\n};\r\n\r\nexport type AgentBuilderConfig = {\r\n  name?: string;\r\n  description?: string;\r\n  system_prompt?: string;\r\n  agentpress_tools?: Record<string, { enabled: boolean; description: string }>;\r\n  configured_mcps?: Array<{ name: string; qualifiedName: string; config: any; enabledTools?: string[] }>;\r\n  avatar?: string;\r\n  avatar_color?: string;\r\n};\r\n\r\nexport type AgentBuilderChatRequest = {\r\n  message: string;\r\n  conversation_history: AgentBuilderMessage[];\r\n  agent_id: string;\r\n  partial_config?: AgentBuilderConfig;\r\n};\r\n\r\nexport type AgentBuilderStreamData = {\r\n  type: 'content' | 'config' | 'done' | 'error';\r\n  content?: string;\r\n  config?: AgentBuilderConfig;\r\n  next_step?: string;\r\n  error?: string;\r\n};\r\n\r\nexport const startAgentBuilderChat = async (\r\n  request: AgentBuilderChatRequest,\r\n  onData: (data: AgentBuilderStreamData) => void,\r\n  onComplete: () => void,\r\n  signal?: AbortSignal\r\n): Promise<void> => {\r\n  try {\r\n    const agentPlaygroundEnabled = await isFlagEnabled('custom_agents');\r\n    if (!agentPlaygroundEnabled) {\r\n      throw new Error('Custom agents is not enabled');\r\n    }\r\n    const supabase = createClient();\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n\r\n    if (!session) {\r\n      throw new Error('You must be logged in to use the agent builder');\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/agents/builder/chat/${request.agent_id}`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${session.access_token}`,\r\n      },\r\n      body: JSON.stringify({\r\n        message: request.message,\r\n        conversation_history: request.conversation_history,\r\n        partial_config: request.partial_config\r\n      }),\r\n      signal,\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));\r\n      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);\r\n    }\r\n\r\n    const reader = response.body?.getReader();\r\n    const decoder = new TextDecoder();\r\n\r\n    if (!reader) {\r\n      throw new Error('No response body');\r\n    }\r\n\r\n    while (true) {\r\n      const { done, value } = await reader.read();\r\n      if (done) break;\r\n\r\n      const chunk = decoder.decode(value);\r\n      const lines = chunk.split('\\n');\r\n\r\n      for (const line of lines) {\r\n        if (line.startsWith('data: ')) {\r\n          try {\r\n            const data = JSON.parse(line.slice(6));\r\n            onData(data);\r\n            \r\n            if (data.type === 'done') {\r\n              onComplete();\r\n              return;\r\n            }\r\n          } catch (e) {\r\n            console.error('Error parsing SSE data:', e);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  } catch (err) {\r\n    console.error('Error in agent builder chat:', err);\r\n    throw err;\r\n  }\r\n};\r\n\r\nexport const getAgentVersions = async (agentId: string): Promise<AgentVersion[]> => {\r\n  try {\r\n    const agentPlaygroundEnabled = await isFlagEnabled('custom_agents');\r\n    if (!agentPlaygroundEnabled) {\r\n      throw new Error('Custom agents is not enabled');\r\n    }\r\n    const supabase = createClient();\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n\r\n    if (!session) {\r\n      throw new Error('You must be logged in to get agent versions');\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/agents/${agentId}/versions`, {\r\n      headers: {\r\n        'Authorization': `Bearer ${session.access_token}`,\r\n      },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));\r\n      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);\r\n    }\r\n\r\n    const versions = await response.json();\r\n    console.log('[API] Fetched agent versions:', agentId, versions.length);\r\n    return versions;\r\n  } catch (err) {\r\n    console.error('Error fetching agent versions:', err);\r\n    throw err;\r\n  }\r\n};\r\n\r\nexport const createAgentVersion = async (\r\n  agentId: string,\r\n  data: AgentVersionCreateRequest\r\n): Promise<AgentVersion> => {\r\n  try {\r\n    const agentPlaygroundEnabled = await isFlagEnabled('custom_agents');\r\n    if (!agentPlaygroundEnabled) {\r\n      throw new Error('Custom agents is not enabled');\r\n    }\r\n    const supabase = createClient();\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n\r\n    if (!session) {\r\n      throw new Error('You must be logged in to create agent version');\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/agents/${agentId}/versions`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${session.access_token}`,\r\n      },\r\n      body: JSON.stringify(data),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));\r\n      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);\r\n    }\r\n\r\n    const version = await response.json();\r\n    console.log('[API] Created agent version:', version.version_id);\r\n    return version;\r\n  } catch (err) {\r\n    console.error('Error creating agent version:', err);\r\n    throw err;\r\n  }\r\n};\r\n\r\nexport const activateAgentVersion = async (\r\n  agentId: string,\r\n  versionId: string\r\n): Promise<void> => {\r\n  try {\r\n    const agentPlaygroundEnabled = await isFlagEnabled('custom_agents');\r\n    if (!agentPlaygroundEnabled) {\r\n      throw new Error('Custom agents is not enabled');\r\n    }\r\n    const supabase = createClient();\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n\r\n    if (!session) {\r\n      throw new Error('You must be logged in to activate agent version');\r\n    }\r\n\r\n    const response = await fetch(\r\n      `${API_URL}/agents/${agentId}/versions/${versionId}/activate`,\r\n      {\r\n        method: 'PUT',\r\n        headers: {\r\n          'Authorization': `Bearer ${session.access_token}`,\r\n        },\r\n      }\r\n    );\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));\r\n      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);\r\n    }\r\n\r\n    console.log('[API] Activated agent version:', versionId);\r\n  } catch (err) {\r\n    console.error('Error activating agent version:', err);\r\n    throw err;\r\n  }\r\n};\r\n\r\nexport const getAgentVersion = async (\r\n  agentId: string,\r\n  versionId: string\r\n): Promise<AgentVersion> => {\r\n  try {\r\n    const agentPlaygroundEnabled = await isFlagEnabled('custom_agents');\r\n    if (!agentPlaygroundEnabled) {\r\n      throw new Error('Custom agents is not enabled');\r\n    }\r\n    const supabase = createClient();\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n\r\n    if (!session) {\r\n      throw new Error('You must be logged in to get agent version');\r\n    }\r\n\r\n    const response = await fetch(\r\n      `${API_URL}/agents/${agentId}/versions/${versionId}`,\r\n      {\r\n        headers: {\r\n          'Authorization': `Bearer ${session.access_token}`,\r\n        },\r\n      }\r\n    );\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({ message: 'Unknown error' }));\r\n      throw new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);\r\n    }\r\n\r\n    const version = await response.json();\r\n    console.log('[API] Fetched agent version:', version.version_id);\r\n    return version;\r\n  } catch (err) {\r\n    console.error('Error fetching agent version:', err);\r\n    throw err;\r\n  }\r\n};\r\n  "], "names": [], "mappings": ";;;;;;;;;;;;;;AAGgB;AAHhB;AACA;;;AAEA,MAAM,UAAU,iEAAuC;AA8HhD,MAAM,YAAY,OAAO,SAAuB,CAAC,CAAC;IACvD,IAAI;QACF,MAAM,yBAAyB,MAAM,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE;QACnD,IAAI,CAAC,wBAAwB;YAC3B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAE5D,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,cAAc,IAAI;QACxB,IAAI,OAAO,IAAI,EAAE,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QAChE,IAAI,OAAO,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACnE,IAAI,OAAO,MAAM,EAAE,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC7D,IAAI,OAAO,OAAO,EAAE,YAAY,MAAM,CAAC,WAAW,OAAO,OAAO;QAChE,IAAI,OAAO,UAAU,EAAE,YAAY,MAAM,CAAC,cAAc,OAAO,UAAU;QACzE,IAAI,OAAO,WAAW,KAAK,WAAW,YAAY,MAAM,CAAC,eAAe,OAAO,WAAW,CAAC,QAAQ;QACnG,IAAI,OAAO,aAAa,KAAK,WAAW,YAAY,MAAM,CAAC,iBAAiB,OAAO,aAAa,CAAC,QAAQ;QACzG,IAAI,OAAO,oBAAoB,KAAK,WAAW,YAAY,MAAM,CAAC,wBAAwB,OAAO,oBAAoB,CAAC,QAAQ;QAC9H,IAAI,OAAO,KAAK,EAAE,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK;QAE1D,MAAM,MAAM,GAAG,QAAQ,OAAO,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI;QAE5F,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACnD;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,SAAS;gBAAgB,CAAC;YACjF,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACxF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,yBAAyB,OAAO,MAAM,EAAE,UAAU,GAAG,UAAU,OAAO,UAAU,EAAE,SAAS;QACvG,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,0BAA0B;QACxC,MAAM;IACR;AACF;AAEO,MAAM,WAAW,OAAO;IAC7B,IAAI;QACF,MAAM,yBAAyB,MAAM,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE;QACnD,IAAI,CAAC,wBAAwB;YAC3B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAE5D,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,QAAQ,EAAE,SAAS,EAAE;YAC3D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACnD;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,SAAS;gBAAgB,CAAC;YACjF,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACxF;QAEA,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,QAAQ,GAAG,CAAC,wBAAwB,MAAM,QAAQ;QAClD,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,MAAM,cAAc,OAAO;IAChC,IAAI;QACF,MAAM,yBAAyB,MAAM,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE;QACnD,IAAI,CAAC,wBAAwB;YAC3B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAE5D,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,OAAO,CAAC,EAAE;YAChD,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACnD;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,SAAS;gBAAgB,CAAC;YACjF,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACxF;QAEA,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,QAAQ,GAAG,CAAC,wBAAwB,MAAM,QAAQ;QAClD,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,MAAM,cAAc,OAAO,SAAiB;IACjD,IAAI;QACF,MAAM,yBAAyB,MAAM,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE;QACnD,IAAI,CAAC,wBAAwB;YAC3B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAE5D,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,QAAQ,EAAE,SAAS,EAAE;YAC3D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACnD;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,SAAS;gBAAgB,CAAC;YACjF,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,uBAAuB,CAAC;QACvF;QAEA,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,QAAQ,GAAG,CAAC,wBAAwB,MAAM,QAAQ;QAClD,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,MAAM,cAAc,OAAO;IAChC,IAAI;QACF,MAAM,yBAAyB,MAAM,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE;QACnD,IAAI,CAAC,wBAAwB;YAC3B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAE5D,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,QAAQ,EAAE,SAAS,EAAE;YAC3D,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACnD;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,SAAS;gBAAgB,CAAC;YACjF,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACxF;QAEA,QAAQ,GAAG,CAAC,wBAAwB;IACtC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,yBAAyB;QACvC,MAAM;IACR;AACF;AAEO,MAAM,iBAAiB,OAAO;IACnC,IAAI;QACF,MAAM,yBAAyB,MAAM,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE;QACnD,IAAI,CAAC,wBAAwB;YAC3B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAE5D,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,QAAQ,EAAE,SAAS,MAAM,CAAC,EAAE;YAClE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACnD;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,SAAS;gBAAgB,CAAC;YACjF,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACxF;QAEA,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,QAAQ,GAAG,CAAC,+BAA+B;QAC3C,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAEO,MAAM,6BAA6B,OAAO;IAC/C,IAAI;QACF,MAAM,yBAAyB,MAAM,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE;QACnD,IAAI,CAAC,wBAAwB;YAC3B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAE5D,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,QAAQ,EAAE,QAAQ,qBAAqB,CAAC,EAAE;YAChF,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACnD;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,SAAS;gBAAgB,CAAC;YACjF,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACxF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,6CAA6C,SAAS,KAAK,QAAQ,CAAC,MAAM;QACtF,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,MAAM;IACR;AACF;AAiCO,MAAM,wBAAwB,OACnC,SACA,QACA,YACA;IAEA,IAAI;QACF,MAAM,yBAAyB,MAAM,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE;QACnD,IAAI,CAAC,wBAAwB;YAC3B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAE5D,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,qBAAqB,EAAE,QAAQ,QAAQ,EAAE,EAAE;YACjF,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACnD;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,SAAS,QAAQ,OAAO;gBACxB,sBAAsB,QAAQ,oBAAoB;gBAClD,gBAAgB,QAAQ,cAAc;YACxC;YACA;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,SAAS;gBAAgB,CAAC;YACjF,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACxF;QAEA,MAAM,SAAS,SAAS,IAAI,EAAE;QAC9B,MAAM,UAAU,IAAI;QAEpB,IAAI,CAAC,QAAQ;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,MAAO,KAAM;YACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;YACzC,IAAI,MAAM;YAEV,MAAM,QAAQ,QAAQ,MAAM,CAAC;YAC7B,MAAM,QAAQ,MAAM,KAAK,CAAC;YAE1B,KAAK,MAAM,QAAQ,MAAO;gBACxB,IAAI,KAAK,UAAU,CAAC,WAAW;oBAC7B,IAAI;wBACF,MAAM,OAAO,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC;wBACnC,OAAO;wBAEP,IAAI,KAAK,IAAI,KAAK,QAAQ;4BACxB;4BACA;wBACF;oBACF,EAAE,OAAO,GAAG;wBACV,QAAQ,KAAK,CAAC,2BAA2B;oBAC3C;gBACF;YACF;QACF;IACF,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAEO,MAAM,mBAAmB,OAAO;IACrC,IAAI;QACF,MAAM,yBAAyB,MAAM,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE;QACnD,IAAI,CAAC,wBAAwB;YAC3B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAE5D,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,QAAQ,EAAE,QAAQ,SAAS,CAAC,EAAE;YACpE,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACnD;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,SAAS;gBAAgB,CAAC;YACjF,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACxF;QAEA,MAAM,WAAW,MAAM,SAAS,IAAI;QACpC,QAAQ,GAAG,CAAC,iCAAiC,SAAS,SAAS,MAAM;QACrE,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM;IACR;AACF;AAEO,MAAM,qBAAqB,OAChC,SACA;IAEA,IAAI;QACF,MAAM,yBAAyB,MAAM,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE;QACnD,IAAI,CAAC,wBAAwB;YAC3B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAE5D,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,QAAQ,EAAE,QAAQ,SAAS,CAAC,EAAE;YACpE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACnD;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,SAAS;gBAAgB,CAAC;YACjF,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACxF;QAEA,MAAM,UAAU,MAAM,SAAS,IAAI;QACnC,QAAQ,GAAG,CAAC,gCAAgC,QAAQ,UAAU;QAC9D,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF;AAEO,MAAM,uBAAuB,OAClC,SACA;IAEA,IAAI;QACF,MAAM,yBAAyB,MAAM,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE;QACnD,IAAI,CAAC,wBAAwB;YAC3B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAE5D,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MACrB,GAAG,QAAQ,QAAQ,EAAE,QAAQ,UAAU,EAAE,UAAU,SAAS,CAAC,EAC7D;YACE,QAAQ;YACR,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACnD;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,SAAS;gBAAgB,CAAC;YACjF,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACxF;QAEA,QAAQ,GAAG,CAAC,kCAAkC;IAChD,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAEO,MAAM,kBAAkB,OAC7B,SACA;IAEA,IAAI;QACF,MAAM,yBAAyB,MAAM,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE;QACnD,IAAI,CAAC,wBAAwB;YAC3B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAE5D,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MACrB,GAAG,QAAQ,QAAQ,EAAE,QAAQ,UAAU,EAAE,WAAW,EACpD;YACE,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACnD;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,SAAS;gBAAgB,CAAC;YACjF,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACxF;QAEA,MAAM,UAAU,MAAM,SAAS,IAAI;QACnC,QAAQ,GAAG,CAAC,gCAAgC,QAAQ,UAAU;QAC9D,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 2512, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/agents/use-agents.ts"], "sourcesContent": ["import { createMutationHook, createQ<PERSON>yHook } from '@/hooks/use-query';\r\nimport { useQueryClient } from '@tanstack/react-query';\r\nimport { toast } from 'sonner';\r\nimport { agentKeys } from './keys';\r\nimport { Agent, AgentUpdateRequest, AgentsParams, createAgent, deleteAgent, getAgent, getAgents, getThreadAgent, updateAgent, AgentBuilderChatRequest, AgentBuilderStreamData, startAgentBuilderChat, getAgentBuilderChatHistory } from './utils';\r\nimport { useRef, useCallback } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { generateRandomAvatar } from '@/lib/utils/_avatar-generator';\r\nimport { DEFAULT_AGENTPRESS_TOOLS } from '@/components/agents/tools';\r\n\r\nexport const useAgents = (params: AgentsParams = {}) => {\r\n  return createQueryHook(\r\n    agentKeys.list(params),\r\n    () => getAgents(params),\r\n    {\r\n      staleTime: 5 * 60 * 1000,\r\n      gcTime: 10 * 60 * 1000,\r\n    }\r\n  )();\r\n};\r\n\r\nexport const useAgent = (agentId: string) => {\r\n  return createQueryHook(\r\n    agentKeys.detail(agentId),\r\n    () => getAgent(agentId),\r\n    {\r\n      enabled: !!agentId,\r\n      staleTime: 5 * 60 * 1000,\r\n      gcTime: 10 * 60 * 1000,\r\n    }\r\n  )();\r\n};\r\n\r\nexport const useCreateAgent = () => {\r\n  const queryClient = useQueryClient();\r\n  \r\n  return createMutationHook(\r\n    createAgent,\r\n    {\r\n      onSuccess: (data) => {\r\n        queryClient.invalidateQueries({ queryKey: agentKeys.lists() });\r\n        queryClient.setQueryData(agentKeys.detail(data.agent_id), data);\r\n        \r\n        toast.success('Agent created successfully');\r\n      },\r\n    }\r\n  )();\r\n};\r\n\r\nexport const useCreateNewAgent = () => {\r\n  const router = useRouter();\r\n  const createAgentMutation = useCreateAgent();\r\n  \r\n  return createMutationHook(\r\n    async (_: void) => {\r\n      const { avatar, avatar_color } = generateRandomAvatar();\r\n      \r\n      const defaultAgentData = {\r\n        name: 'New Agent',\r\n        description: '',\r\n        system_prompt: 'You are a helpful assistant. Provide clear, accurate, and helpful responses to user queries.',\r\n        avatar,\r\n        avatar_color,\r\n        configured_mcps: [],\r\n        agentpress_tools: Object.fromEntries(\r\n          Object.entries(DEFAULT_AGENTPRESS_TOOLS).map(([key, value]) => [\r\n            key, \r\n            { enabled: value.enabled, description: value.description }\r\n          ])\r\n        ),\r\n        is_default: false,\r\n      };\r\n\r\n      const newAgent = await createAgentMutation.mutateAsync(defaultAgentData);\r\n      return newAgent;\r\n    },\r\n    {\r\n      onSuccess: (newAgent) => {\r\n        router.push(`/agents/config/${newAgent.agent_id}`);\r\n      },\r\n      onError: (error) => {\r\n        console.error('Error creating agent:', error);\r\n        toast.error('Failed to create agent. Please try again.');\r\n      },\r\n    }\r\n  )();\r\n};\r\n\r\nexport const useUpdateAgent = () => {\r\n  const queryClient = useQueryClient();\r\n  \r\n  return createMutationHook(\r\n    ({ agentId, ...data }: { agentId: string } & AgentUpdateRequest) => \r\n      updateAgent(agentId, data),\r\n    {\r\n      onSuccess: (data, variables) => {\r\n        queryClient.setQueryData(agentKeys.detail(variables.agentId), data);\r\n        queryClient.invalidateQueries({ queryKey: agentKeys.lists() });\r\n        if (variables.configured_mcps !== undefined || variables.custom_mcps !== undefined) {\r\n          queryClient.invalidateQueries({ queryKey: ['agent-tools', variables.agentId] });\r\n          queryClient.invalidateQueries({ queryKey: ['pipedream-tools', variables.agentId] });\r\n          queryClient.invalidateQueries({ queryKey: ['custom-mcp-tools', variables.agentId] });\r\n          queryClient.invalidateQueries({ queryKey: ['pipedream', 'available-tools'] });\r\n        }\r\n      },\r\n    }\r\n  )();\r\n};\r\n\r\nexport const useDeleteAgent = () => {\r\n  const queryClient = useQueryClient();\r\n  \r\n  return createMutationHook(\r\n    deleteAgent,\r\n    {\r\n      onSuccess: (_, agentId) => {\r\n        queryClient.removeQueries({ queryKey: agentKeys.detail(agentId) });\r\n        queryClient.invalidateQueries({ queryKey: agentKeys.lists() });\r\n        toast.success('Agent deleted successfully');\r\n      },\r\n    }\r\n  )();\r\n};\r\n\r\nexport const useOptimisticAgentUpdate = () => {\r\n  const queryClient = useQueryClient();\r\n  \r\n  return {\r\n    optimisticallyUpdateAgent: (agentId: string, updates: Partial<Agent>) => {\r\n      queryClient.setQueryData(\r\n        agentKeys.detail(agentId),\r\n        (oldData: Agent | undefined) => {\r\n          if (!oldData) return oldData;\r\n          return { ...oldData, ...updates };\r\n        }\r\n      );\r\n    },\r\n    \r\n    revertOptimisticUpdate: (agentId: string) => {\r\n      queryClient.invalidateQueries({ queryKey: agentKeys.detail(agentId) });\r\n    },\r\n  };\r\n};\r\n\r\nexport const useThreadAgent = (threadId: string) => {\r\n  return createQueryHook(\r\n    agentKeys.threadAgent(threadId),\r\n    () => getThreadAgent(threadId),\r\n    {\r\n      enabled: !!threadId,\r\n      staleTime: 5 * 60 * 1000,\r\n      gcTime: 10 * 60 * 1000,\r\n    }\r\n  )();\r\n};\r\n\r\nexport const useAgentBuilderChat = () => {\r\n  const abortControllerRef = useRef<AbortController | null>(null);\r\n\r\n  const sendMessage = useCallback(async (\r\n    request: AgentBuilderChatRequest,\r\n    callbacks: {\r\n      onData: (data: AgentBuilderStreamData) => void;\r\n      onComplete: () => void;\r\n      onError?: (error: Error) => void;\r\n    }\r\n  ) => {\r\n    // Cancel any existing request\r\n    if (abortControllerRef.current) {\r\n      abortControllerRef.current.abort();\r\n    }\r\n\r\n    // Create new abort controller\r\n    abortControllerRef.current = new AbortController();\r\n\r\n    try {\r\n      await startAgentBuilderChat(\r\n        request,\r\n        callbacks.onData,\r\n        callbacks.onComplete,\r\n        abortControllerRef.current.signal\r\n      );\r\n    } catch (error) {\r\n      if (error instanceof Error && error.name !== 'AbortError') {\r\n        console.error('Error in agent builder chat:', error);\r\n        callbacks.onError?.(error);\r\n      }\r\n    } finally {\r\n      abortControllerRef.current = null;\r\n    }\r\n  }, []);\r\n\r\n  const cancelStream = useCallback(() => {\r\n    if (abortControllerRef.current) {\r\n      abortControllerRef.current.abort();\r\n      abortControllerRef.current = null;\r\n    }\r\n  }, []);\r\n\r\n  return {\r\n    sendMessage,\r\n    cancelStream,\r\n  };\r\n};\r\n\r\nexport const useAgentBuilderChatHistory = (agentId: string) =>\r\n  createQueryHook(\r\n    agentKeys.builderChatHistory(agentId),\r\n    () => getAgentBuilderChatHistory(agentId),\r\n    {\r\n      enabled: !!agentId,\r\n      retry: 1,\r\n    }\r\n  )();"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEO,MAAM,YAAY,CAAC,SAAuB,CAAC,CAAC;IACjD,OAAO,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EACnB,mJAAA,CAAA,YAAS,CAAC,IAAI,CAAC,SACf,IAAM,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,SAChB;QACE,WAAW,IAAI,KAAK;QACpB,QAAQ,KAAK,KAAK;IACpB;AAEJ;AAEO,MAAM,WAAW,CAAC;IACvB,OAAO,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EACnB,mJAAA,CAAA,YAAS,CAAC,MAAM,CAAC,UACjB,IAAM,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,UACf;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;QACpB,QAAQ,KAAK,KAAK;IACpB;AAEJ;AAEO,MAAM,iBAAiB;;IAC5B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EACtB,oJAAA,CAAA,cAAW,EACX;QACE,WAAW,CAAC;YACV,YAAY,iBAAiB,CAAC;gBAAE,UAAU,mJAAA,CAAA,YAAS,CAAC,KAAK;YAAG;YAC5D,YAAY,YAAY,CAAC,mJAAA,CAAA,YAAS,CAAC,MAAM,CAAC,KAAK,QAAQ,GAAG;YAE1D,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;IACF;AAEJ;GAda;;QACS,yLAAA,CAAA,iBAAc;;;AAe7B,MAAM,oBAAoB;;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,sBAAsB;IAE5B,OAAO,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EACtB,OAAO;QACL,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,uBAAoB,AAAD;QAEpD,MAAM,mBAAmB;YACvB,MAAM;YACN,aAAa;YACb,eAAe;YACf;YACA;YACA,iBAAiB,EAAE;YACnB,kBAAkB,OAAO,WAAW,CAClC,OAAO,OAAO,CAAC,uIAAA,CAAA,2BAAwB,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK;oBAC7D;oBACA;wBAAE,SAAS,MAAM,OAAO;wBAAE,aAAa,MAAM,WAAW;oBAAC;iBAC1D;YAEH,YAAY;QACd;QAEA,MAAM,WAAW,MAAM,oBAAoB,WAAW,CAAC;QACvD,OAAO;IACT,GACA;QACE,WAAW,CAAC;YACV,OAAO,IAAI,CAAC,CAAC,eAAe,EAAE,SAAS,QAAQ,EAAE;QACnD;QACA,SAAS,CAAC;YACR,QAAQ,KAAK,CAAC,yBAAyB;YACvC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;AAEJ;IArCa;;QACI,qIAAA,CAAA,YAAS;QACI;;;AAqCvB,MAAM,iBAAiB;;IAC5B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EACtB,CAAC,EAAE,OAAO,EAAE,GAAG,MAAgD,GAC7D,CAAA,GAAA,oJAAA,CAAA,cAAW,AAAD,EAAE,SAAS,OACvB;QACE,WAAW,CAAC,MAAM;YAChB,YAAY,YAAY,CAAC,mJAAA,CAAA,YAAS,CAAC,MAAM,CAAC,UAAU,OAAO,GAAG;YAC9D,YAAY,iBAAiB,CAAC;gBAAE,UAAU,mJAAA,CAAA,YAAS,CAAC,KAAK;YAAG;YAC5D,IAAI,UAAU,eAAe,KAAK,aAAa,UAAU,WAAW,KAAK,WAAW;gBAClF,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAe,UAAU,OAAO;qBAAC;gBAAC;gBAC7E,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAmB,UAAU,OAAO;qBAAC;gBAAC;gBACjF,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAoB,UAAU,OAAO;qBAAC;gBAAC;gBAClF,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAa;qBAAkB;gBAAC;YAC7E;QACF;IACF;AAEJ;IAnBa;;QACS,yLAAA,CAAA,iBAAc;;;AAoB7B,MAAM,iBAAiB;;IAC5B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EACtB,oJAAA,CAAA,cAAW,EACX;QACE,WAAW,CAAC,GAAG;YACb,YAAY,aAAa,CAAC;gBAAE,UAAU,mJAAA,CAAA,YAAS,CAAC,MAAM,CAAC;YAAS;YAChE,YAAY,iBAAiB,CAAC;gBAAE,UAAU,mJAAA,CAAA,YAAS,CAAC,KAAK;YAAG;YAC5D,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;IACF;AAEJ;IAba;;QACS,yLAAA,CAAA,iBAAc;;;AAc7B,MAAM,2BAA2B;;IACtC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO;QACL,2BAA2B,CAAC,SAAiB;YAC3C,YAAY,YAAY,CACtB,mJAAA,CAAA,YAAS,CAAC,MAAM,CAAC,UACjB,CAAC;gBACC,IAAI,CAAC,SAAS,OAAO;gBACrB,OAAO;oBAAE,GAAG,OAAO;oBAAE,GAAG,OAAO;gBAAC;YAClC;QAEJ;QAEA,wBAAwB,CAAC;YACvB,YAAY,iBAAiB,CAAC;gBAAE,UAAU,mJAAA,CAAA,YAAS,CAAC,MAAM,CAAC;YAAS;QACtE;IACF;AACF;IAlBa;;QACS,yLAAA,CAAA,iBAAc;;;AAmB7B,MAAM,iBAAiB,CAAC;IAC7B,OAAO,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EACnB,mJAAA,CAAA,YAAS,CAAC,WAAW,CAAC,WACtB,IAAM,CAAA,GAAA,oJAAA,CAAA,iBAAc,AAAD,EAAE,WACrB;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;QACpB,QAAQ,KAAK,KAAK;IACpB;AAEJ;AAEO,MAAM,sBAAsB;;IACjC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA0B;IAE1D,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,OAC9B,SACA;YAMA,8BAA8B;YAC9B,IAAI,mBAAmB,OAAO,EAAE;gBAC9B,mBAAmB,OAAO,CAAC,KAAK;YAClC;YAEA,8BAA8B;YAC9B,mBAAmB,OAAO,GAAG,IAAI;YAEjC,IAAI;gBACF,MAAM,CAAA,GAAA,oJAAA,CAAA,wBAAqB,AAAD,EACxB,SACA,UAAU,MAAM,EAChB,UAAU,UAAU,EACpB,mBAAmB,OAAO,CAAC,MAAM;YAErC,EAAE,OAAO,OAAO;gBACd,IAAI,iBAAiB,SAAS,MAAM,IAAI,KAAK,cAAc;oBACzD,QAAQ,KAAK,CAAC,gCAAgC;oBAC9C,UAAU,OAAO,GAAG;gBACtB;YACF,SAAU;gBACR,mBAAmB,OAAO,GAAG;YAC/B;QACF;uDAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YAC/B,IAAI,mBAAmB,OAAO,EAAE;gBAC9B,mBAAmB,OAAO,CAAC,KAAK;gBAChC,mBAAmB,OAAO,GAAG;YAC/B;QACF;wDAAG,EAAE;IAEL,OAAO;QACL;QACA;IACF;AACF;IA/Ca;AAiDN,MAAM,6BAA6B,CAAC,UACzC,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EACZ,mJAAA,CAAA,YAAS,CAAC,kBAAkB,CAAC,UAC7B,IAAM,CAAA,GAAA,oJAAA,CAAA,6BAA0B,AAAD,EAAE,UACjC;QACE,SAAS,CAAC,CAAC;QACX,OAAO;IACT", "debugId": null}}, {"offset": {"line": 2759, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/sidebar/use-sidebar.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { createMutationHook } from \"@/hooks/use-query\";\r\nimport { getProjects, getThreads, Project, Thread } from \"@/lib/api\";\r\nimport { createQueryHook } from '@/hooks/use-query';\r\nimport { threadKeys } from \"./keys\";\r\nimport { projectKeys } from \"./keys\";\r\nimport { deleteThread } from \"../threads/utils\";\r\n\r\nexport const useProjects = createQueryHook(\r\n  projectKeys.lists(),\r\n  async () => {\r\n    const data = await getProjects();\r\n    return data as Project[];\r\n  },\r\n  {\r\n    staleTime: 5 * 60 * 1000,\r\n    refetchOnWindowFocus: false,\r\n  }\r\n);\r\n\r\nexport const useThreads = createQueryHook(\r\n  threadKeys.lists(),\r\n  async () => {\r\n    const data = await getThreads();\r\n    return data as Thread[];\r\n  },\r\n  {\r\n    staleTime: 5 * 60 * 1000,\r\n    refetchOnWindowFocus: false,\r\n  }\r\n);\r\n\r\ninterface DeleteThreadVariables {\r\n  threadId: string;\r\n  sandboxId?: string;\r\n  isNavigateAway?: boolean;\r\n}\r\n\r\nexport const useDeleteThread = createMutationHook(\r\n  async ({ threadId, sandboxId }: DeleteThreadVariables) => {\r\n    return await deleteThread(threadId, sandboxId);\r\n  },\r\n  {\r\n    onSuccess: () => {\r\n    },\r\n  }\r\n);\r\n\r\ninterface DeleteMultipleThreadsVariables {\r\n  threadIds: string[];\r\n  threadSandboxMap?: Record<string, string>;\r\n  onProgress?: (completed: number, total: number) => void;\r\n}\r\n\r\nexport const useDeleteMultipleThreads = createMutationHook(\r\n  async ({ threadIds, threadSandboxMap, onProgress }: DeleteMultipleThreadsVariables) => {\r\n    let completedCount = 0;\r\n    const results = await Promise.all(\r\n      threadIds.map(async (threadId) => {\r\n        try {\r\n          const sandboxId = threadSandboxMap?.[threadId];\r\n          const result = await deleteThread(threadId, sandboxId);\r\n          completedCount++;\r\n          onProgress?.(completedCount, threadIds.length);\r\n          return { success: true, threadId };\r\n        } catch (error) {\r\n          return { success: false, threadId, error };\r\n        }\r\n      })\r\n    );\r\n    \r\n    return {\r\n      successful: results.filter(r => r.success).map(r => r.threadId),\r\n      failed: results.filter(r => !r.success).map(r => r.threadId),\r\n    };\r\n  },\r\n  {\r\n    onSuccess: () => {\r\n    },\r\n  }\r\n);\r\n\r\nexport type ThreadWithProject = {\r\n  threadId: string;\r\n  projectId: string;\r\n  projectName: string;\r\n  url: string;\r\n  updatedAt: string;\r\n};\r\n\r\nexport const processThreadsWithProjects = (\r\n  threads: Thread[],\r\n  projects: Project[]\r\n): ThreadWithProject[] => {\r\n  const projectsById = new Map<string, Project>();\r\n  projects.forEach((project) => {\r\n    projectsById.set(project.id, project);\r\n  });\r\n\r\n  const threadsWithProjects: ThreadWithProject[] = [];\r\n\r\n  for (const thread of threads) {\r\n    const projectId = thread.project_id;\r\n    if (!projectId) continue;\r\n\r\n    const project = projectsById.get(projectId);\r\n    if (!project) {\r\n      console.log(\r\n        `❌ Thread ${thread.thread_id} has project_id=${projectId} but no matching project found`,\r\n      );\r\n      continue;\r\n    }\r\n    let displayName = project.name || 'Unnamed Project';\r\n    if (thread.metadata?.is_workflow_execution && thread.metadata?.workflow_run_name) {\r\n      displayName = thread.metadata.workflow_run_name;\r\n    }\r\n\r\n    threadsWithProjects.push({\r\n      threadId: thread.thread_id,\r\n      projectId: projectId,\r\n      projectName: displayName,\r\n      url: `/projects/${projectId}/thread/${thread.thread_id}`,\r\n      updatedAt:\r\n        thread.updated_at || project.updated_at || new Date().toISOString(),\r\n    });\r\n  }\r\n\r\n  return sortThreads(threadsWithProjects);\r\n};\r\n\r\nexport const sortThreads = (\r\n  threadsList: ThreadWithProject[],\r\n): ThreadWithProject[] => {\r\n  return [...threadsList].sort((a, b) => {\r\n    return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();\r\n  });\r\n};"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AAEA;AAEA;AAPA;;;;;;;AASO,MAAM,cAAc,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EACvC,oJAAA,CAAA,cAAW,CAAC,KAAK,IACjB;IACE,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD;IAC7B,OAAO;AACT,GACA;IACE,WAAW,IAAI,KAAK;IACpB,sBAAsB;AACxB;AAGK,MAAM,aAAa,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EACtC,oJAAA,CAAA,aAAU,CAAC,KAAK,IAChB;IACE,MAAM,OAAO,MAAM,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD;IAC5B,OAAO;AACT,GACA;IACE,WAAW,IAAI,KAAK;IACpB,sBAAsB;AACxB;AASK,MAAM,kBAAkB,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EAC9C,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAyB;IACnD,OAAO,MAAM,CAAA,GAAA,qJAAA,CAAA,eAAY,AAAD,EAAE,UAAU;AACtC,GACA;IACE,WAAW,KACX;AACF;AASK,MAAM,2BAA2B,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EACvD,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,UAAU,EAAkC;IAChF,IAAI,iBAAiB;IACrB,MAAM,UAAU,MAAM,QAAQ,GAAG,CAC/B,UAAU,GAAG,CAAC,OAAO;QACnB,IAAI;YACF,MAAM,YAAY,kBAAkB,CAAC,SAAS;YAC9C,MAAM,SAAS,MAAM,CAAA,GAAA,qJAAA,CAAA,eAAY,AAAD,EAAE,UAAU;YAC5C;YACA,aAAa,gBAAgB,UAAU,MAAM;YAC7C,OAAO;gBAAE,SAAS;gBAAM;YAAS;QACnC,EAAE,OAAO,OAAO;YACd,OAAO;gBAAE,SAAS;gBAAO;gBAAU;YAAM;QAC3C;IACF;IAGF,OAAO;QACL,YAAY,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;QAC9D,QAAQ,QAAQ,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,OAAO,EAAE,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;IAC7D;AACF,GACA;IACE,WAAW,KACX;AACF;AAWK,MAAM,6BAA6B,CACxC,SACA;IAEA,MAAM,eAAe,IAAI;IACzB,SAAS,OAAO,CAAC,CAAC;QAChB,aAAa,GAAG,CAAC,QAAQ,EAAE,EAAE;IAC/B;IAEA,MAAM,sBAA2C,EAAE;IAEnD,KAAK,MAAM,UAAU,QAAS;QAC5B,MAAM,YAAY,OAAO,UAAU;QACnC,IAAI,CAAC,WAAW;QAEhB,MAAM,UAAU,aAAa,GAAG,CAAC;QACjC,IAAI,CAAC,SAAS;YACZ,QAAQ,GAAG,CACT,CAAC,SAAS,EAAE,OAAO,SAAS,CAAC,gBAAgB,EAAE,UAAU,8BAA8B,CAAC;YAE1F;QACF;QACA,IAAI,cAAc,QAAQ,IAAI,IAAI;QAClC,IAAI,OAAO,QAAQ,EAAE,yBAAyB,OAAO,QAAQ,EAAE,mBAAmB;YAChF,cAAc,OAAO,QAAQ,CAAC,iBAAiB;QACjD;QAEA,oBAAoB,IAAI,CAAC;YACvB,UAAU,OAAO,SAAS;YAC1B,WAAW;YACX,aAAa;YACb,KAAK,CAAC,UAAU,EAAE,UAAU,QAAQ,EAAE,OAAO,SAAS,EAAE;YACxD,WACE,OAAO,UAAU,IAAI,QAAQ,UAAU,IAAI,IAAI,OAAO,WAAW;QACrE;IACF;IAEA,OAAO,YAAY;AACrB;AAEO,MAAM,cAAc,CACzB;IAEA,OAAO;WAAI;KAAY,CAAC,IAAI,CAAC,CAAC,GAAG;QAC/B,OAAO,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;IACxE;AACF", "debugId": null}}, {"offset": {"line": 2868, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/sidebar/use-project-mutations.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { createMutationHook } from '@/hooks/use-query';\r\nimport { \r\n  createProject, \r\n  updateProject, \r\n  deleteProject,\r\n  Project \r\n} from '@/lib/api';\r\nimport { toast } from 'sonner';\r\nimport { projectKeys } from './keys';\r\n\r\nexport const useCreateProject = createMutationHook(\r\n  (data: { name: string; description: string; accountId?: string }) => \r\n    createProject(data, data.accountId),\r\n  {\r\n    onSuccess: () => {\r\n      toast.success('Project created successfully');\r\n    },\r\n    errorContext: {\r\n      operation: 'create project',\r\n      resource: 'project'\r\n    }\r\n  }\r\n);\r\n\r\nexport const useUpdateProject = createMutationHook(\r\n  ({ projectId, data }: { projectId: string; data: Partial<Project> }) => \r\n    updateProject(projectId, data),\r\n  {\r\n    onSuccess: () => {\r\n    //   toast.success('Project updated successfully');\r\n    },\r\n    errorContext: {\r\n      operation: 'update project',\r\n      resource: 'project'\r\n    }\r\n  }\r\n);\r\n\r\nexport const useDeleteProject = createMutationHook(\r\n  ({ projectId }: { projectId: string }) => deleteProject(projectId),\r\n  {\r\n    onSuccess: () => {\r\n      toast.success('Project deleted successfully');\r\n    },\r\n    errorContext: {\r\n      operation: 'delete project',\r\n      resource: 'project'\r\n    }\r\n  }\r\n); "], "names": [], "mappings": ";;;;;AAEA;AACA;AAMA;AATA;;;;AAYO,MAAM,mBAAmB,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EAC/C,CAAC,OACC,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,KAAK,SAAS,GACpC;IACE,WAAW;QACT,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IACA,cAAc;QACZ,WAAW;QACX,UAAU;IACZ;AACF;AAGK,MAAM,mBAAmB,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EAC/C,CAAC,EAAE,SAAS,EAAE,IAAI,EAAiD,GACjE,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE,WAAW,OAC3B;IACE,WAAW;IACX,mDAAmD;IACnD;IACA,cAAc;QACZ,WAAW;QACX,UAAU;IACZ;AACF;AAGK,MAAM,mBAAmB,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EAC/C,CAAC,EAAE,SAAS,EAAyB,GAAK,CAAA,GAAA,oHAAA,CAAA,gBAAa,AAAD,EAAE,YACxD;IACE,WAAW;QACT,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IACA,cAAc;QACZ,WAAW;QACX,UAAU;IACZ;AACF", "debugId": null}}, {"offset": {"line": 2916, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/sidebar/use-public-projects.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { createQueryHook } from '@/hooks/use-query';\r\nimport { getPublicProjects } from '@/lib/api';\r\nimport { projectKeys } from './keys';\r\n\r\nexport const usePublicProjects = createQueryHook(\r\n  projectKeys.public(),\r\n  getPublicProjects,\r\n  {\r\n    staleTime: 5 * 60 * 1000,\r\n    refetchOnWindowFocus: false,\r\n  }\r\n); "], "names": [], "mappings": ";;;AAEA;AACA;AACA;AAJA;;;;AAMO,MAAM,oBAAoB,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAC7C,oJAAA,CAAA,cAAW,CAAC,MAAM,IAClB,oHAAA,CAAA,oBAAiB,EACjB;IACE,WAAW,IAAI,KAAK;IACpB,sBAAsB;AACxB", "debugId": null}}, {"offset": {"line": 2939, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/threads/use-thread-queries.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { createQueryHook } from '@/hooks/use-query';\r\nimport { getThreads } from '@/lib/api';\r\nimport { threadKeys } from './keys';\r\n\r\nexport const useThreadsByProject = (projectId?: string) =>\r\n  createQueryHook(\r\n    threadKeys.byProject(projectId || ''),\r\n    () => projectId ? getThreads(projectId) : Promise.resolve([]),\r\n    {\r\n      enabled: !!projectId,\r\n      staleTime: 2 * 60 * 1000, \r\n      refetchOnWindowFocus: false,\r\n    }\r\n  )();\r\n\r\nexport const useAllThreads = createQueryHook(\r\n  threadKeys.all,\r\n  () => getThreads(),\r\n  {\r\n    staleTime: 2 * 60 * 1000, \r\n    refetchOnWindowFocus: false,\r\n  }\r\n); "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;AAMO,MAAM,sBAAsB,CAAC,YAClC,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EACZ,oJAAA,CAAA,aAAU,CAAC,SAAS,CAAC,aAAa,KAClC,IAAM,YAAY,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAE,aAAa,QAAQ,OAAO,CAAC,EAAE,GAC5D;QACE,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;QACpB,sBAAsB;IACxB;AAGG,MAAM,gBAAgB,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EACzC,oJAAA,CAAA,aAAU,CAAC,GAAG,EACd,IAAM,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,KACf;IACE,WAAW,IAAI,KAAK;IACpB,sBAAsB;AACxB", "debugId": null}}, {"offset": {"line": 2968, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/threads/use-project.ts"], "sourcesContent": ["import { createMutationHook, createQ<PERSON><PERSON><PERSON><PERSON> } from \"@/hooks/use-query\";\r\nimport { threadKeys } from \"./keys\";\r\nimport { getProject, getPublicProjects, Project, updateProject } from \"./utils\";\r\n\r\nexport const useProjectQuery = (projectId: string | undefined) =>\r\n  createQueryHook(\r\n    threadKeys.project(projectId || \"\"),\r\n    () =>\r\n      projectId\r\n        ? getProject(projectId)\r\n        : Promise.reject(\"No project ID\"),\r\n    {\r\n      enabled: !!projectId,\r\n      retry: 1,\r\n    }\r\n  )();\r\n\r\nexport const useUpdateProjectMutation = () =>\r\n  createMutationHook(\r\n    ({\r\n      projectId,\r\n      data,\r\n    }: {\r\n      projectId: string;\r\n      data: Partial<Project>;\r\n    }) => updateProject(projectId, data)\r\n  )();\r\n\r\nexport const usePublicProjectsQuery = () =>\r\n    createQueryHook(\r\n      threadKeys.publicProjects(),\r\n      () =>\r\n        getPublicProjects(),\r\n      {\r\n        retry: 1,\r\n      }\r\n    )();"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEO,MAAM,kBAAkB,CAAC,YAC9B,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EACZ,oJAAA,CAAA,aAAU,CAAC,OAAO,CAAC,aAAa,KAChC,IACE,YACI,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE,aACX,QAAQ,MAAM,CAAC,kBACrB;QACE,SAAS,CAAC,CAAC;QACX,OAAO;IACT;AAGG,MAAM,2BAA2B,IACtC,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EACf,CAAC,EACC,SAAS,EACT,IAAI,EAIL,GAAK,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE,WAAW;AAG5B,MAAM,yBAAyB,IAClC,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EACZ,oJAAA,CAAA,aAAU,CAAC,cAAc,IACzB,IACE,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,KAClB;QACE,OAAO;IACT", "debugId": null}}, {"offset": {"line": 2996, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/threads/use-messages.ts"], "sourcesContent": ["import { createMutationHook, create<PERSON><PERSON><PERSON><PERSON><PERSON> } from \"@/hooks/use-query\";\r\nimport { threadKeys } from \"./keys\";\r\nimport { addUserMessage, getMessages } from \"@/lib/api\";\r\n\r\nexport const useMessagesQuery = (threadId: string) =>\r\n  createQueryHook(\r\n    threadKeys.messages(threadId),\r\n    () => getMessages(threadId),\r\n    {\r\n      enabled: !!threadId,\r\n      retry: 1,\r\n    }\r\n  )();\r\n\r\nexport const useAddUserMessageMutation = () =>\r\n  createMutationHook(\r\n    ({\r\n      threadId,\r\n      message,\r\n    }: {\r\n      threadId: string;\r\n      message: string;\r\n    }) => addUserMessage(threadId, message)\r\n  )();\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEO,MAAM,mBAAmB,CAAC,WAC/B,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EACZ,oJAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,WACpB,IAAM,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,EAAE,WAClB;QACE,SAAS,CAAC,CAAC;QACX,OAAO;IACT;AAGG,MAAM,4BAA4B,IACvC,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EACf,CAAC,EACC,QAAQ,EACR,OAAO,EAIR,GAAK,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU", "debugId": null}}, {"offset": {"line": 3020, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/threads/use-agent-run.ts"], "sourcesContent": ["import { createMutationHook, create<PERSON><PERSON><PERSON><PERSON>ook } from \"@/hooks/use-query\";\r\nimport { threadKeys } from \"./keys\";\r\nimport { BillingError, getAgentRuns, startAgent, stopAgent } from \"@/lib/api\";\r\n\r\nexport const useAgentRunsQuery = (threadId: string) =>\r\n  createQueryHook(\r\n    threadKeys.agentRuns(threadId),\r\n    () => getAgentRuns(threadId),\r\n    {\r\n      enabled: !!threadId,\r\n      retry: 1,\r\n    }\r\n  )();\r\n\r\nexport const useStartAgentMutation = () =>\r\n  createMutationHook(\r\n    ({\r\n      threadId,\r\n      options,\r\n    }: {\r\n      threadId: string;\r\n      options?: {\r\n        model_name?: string;\r\n        enable_thinking?: boolean;\r\n        reasoning_effort?: string;\r\n        stream?: boolean;\r\n        agent_id?: string;\r\n      };\r\n    }) => startAgent(threadId, options),\r\n    {\r\n      onError: (error) => {\r\n        if (!(error instanceof BillingError)) {\r\n          throw error;\r\n        }\r\n      },\r\n    }\r\n  )();\r\n\r\nexport const useStopAgentMutation = () =>\r\n  createMutationHook((agentRunId: string) => stopAgent(agentRunId))();\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEO,MAAM,oBAAoB,CAAC,WAChC,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EACZ,oJAAA,CAAA,aAAU,CAAC,SAAS,CAAC,WACrB,IAAM,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE,WACnB;QACE,SAAS,CAAC,CAAC;QACX,OAAO;IACT;AAGG,MAAM,wBAAwB,IACnC,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EACf,CAAC,EACC,QAAQ,EACR,OAAO,EAUR,GAAK,CAAA,GAAA,oHAAA,CAAA,aAAU,AAAD,EAAE,UAAU,UAC3B;QACE,SAAS,CAAC;YACR,IAAI,CAAC,CAAC,iBAAiB,oHAAA,CAAA,eAAY,GAAG;gBACpC,MAAM;YACR;QACF;IACF;AAGG,MAAM,uBAAuB,IAClC,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EAAE,CAAC,aAAuB,CAAA,GAAA,oHAAA,CAAA,YAAS,AAAD,EAAE", "debugId": null}}, {"offset": {"line": 3052, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/threads/use-billing-status.ts"], "sourcesContent": ["import { createQueryHook } from \"@/hooks/use-query\";\r\nimport { threadKeys } from \"./keys\";\r\nimport { checkBillingStatus } from \"@/lib/api\";\r\n\r\nexport const useBillingStatusQuery = (enabled = true) =>\r\n  createQueryHook(\r\n    threadKeys.billingStatus,\r\n    () => checkBillingStatus(),\r\n    {\r\n      enabled,\r\n      retry: 1,\r\n      staleTime: 1000 * 60 * 5,\r\n      gcTime: 1000 * 60 * 10,\r\n      refetchOnWindowFocus: false,\r\n      refetchOnMount: false,\r\n      refetchOnReconnect: false,\r\n      refetchInterval: (query: any) => {\r\n        if (query.state.data && !query.state.data.can_run) {\r\n          return 1000 * 60;\r\n        }\r\n        return false;\r\n      },\r\n    }\r\n  )();\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,MAAM,wBAAwB,CAAC,UAAU,IAAI,GAClD,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EACZ,oJAAA,CAAA,aAAU,CAAC,aAAa,EACxB,IAAM,CAAA,GAAA,oHAAA,CAAA,qBAAkB,AAAD,KACvB;QACE;QACA,OAAO;QACP,WAAW,OAAO,KAAK;QACvB,QAAQ,OAAO,KAAK;QACpB,sBAAsB;QACtB,gBAAgB;QAChB,oBAAoB;QACpB,iBAAiB,CAAC;YAChB,IAAI,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE;gBACjD,OAAO,OAAO;YAChB;YACA,OAAO;QACT;IACF", "debugId": null}}, {"offset": {"line": 3085, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/threads/use-thread-mutations.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { createMutationHook } from '@/hooks/use-query';\r\nimport { \r\n  createThread, \r\n  addUserMessage \r\n} from '@/lib/api';\r\nimport { toast } from 'sonner';\r\n\r\nexport const useCreateThread = createMutationHook(\r\n  ({ projectId }: { projectId: string }) => createThread(projectId),\r\n  {\r\n    onSuccess: () => {\r\n      toast.success('Thread created successfully');\r\n    },\r\n    errorContext: {\r\n      operation: 'create thread',\r\n      resource: 'thread'\r\n    }\r\n  }\r\n);\r\n\r\nexport const useAddUserMessage = createMutationHook(\r\n  ({ threadId, content }: { threadId: string; content: string }) => \r\n    addUserMessage(threadId, content),\r\n  {\r\n    errorContext: {\r\n      operation: 'add message',\r\n      resource: 'message'\r\n    }\r\n  }\r\n); "], "names": [], "mappings": ";;;;AAEA;AACA;AAIA;AAPA;;;;AASO,MAAM,kBAAkB,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EAC9C,CAAC,EAAE,SAAS,EAAyB,GAAK,CAAA,GAAA,oHAAA,CAAA,eAAY,AAAD,EAAE,YACvD;IACE,WAAW;QACT,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IACA,cAAc;QACZ,WAAW;QACX,UAAU;IACZ;AACF;AAGK,MAAM,oBAAoB,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EAChD,CAAC,EAAE,QAAQ,EAAE,OAAO,EAAyC,GAC3D,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,UAC3B;IACE,cAAc;QACZ,WAAW;QACX,UAAU;IACZ;AACF", "debugId": null}}, {"offset": {"line": 3120, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/files/use-file-mutations.ts"], "sourcesContent": ["import { useMutation, useQueryClient } from '@tanstack/react-query';\r\nimport { useAuth } from '@/components/AuthProvider';\r\nimport { fileQueryKeys } from './use-file-queries';\r\nimport { FileCache } from '@/hooks/use-cached-file';\r\nimport { toast } from 'sonner';\r\n// Import the normalizePath function from use-file-queries\r\nfunction normalizePath(path: string): string {\r\n  if (!path) return '/';\r\n  \r\n  // Remove any leading/trailing whitespace\r\n  path = path.trim();\r\n  \r\n  // Ensure path starts with /\r\n  if (!path.startsWith('/')) {\r\n    path = '/' + path;\r\n  }\r\n  \r\n  // Remove duplicate slashes and normalize\r\n  path = path.replace(/\\/+/g, '/');\r\n  \r\n  // Remove trailing slash unless it's the root\r\n  if (path.length > 1 && path.endsWith('/')) {\r\n    path = path.slice(0, -1);\r\n  }\r\n  \r\n  return path;\r\n}\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || '';\r\n\r\n/**\r\n * Hook for uploading files\r\n */\r\nexport function useFileUpload() {\r\n  const { session } = useAuth();\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async ({\r\n      sandboxId,\r\n      file,\r\n      targetPath,\r\n    }: {\r\n      sandboxId: string;\r\n      file: File;\r\n      targetPath: string;\r\n    }) => {\r\n      if (!session?.access_token) {\r\n        throw new Error('No access token available');\r\n      }\r\n\r\n      const formData = new FormData();\r\n      formData.append('file', file);\r\n      formData.append('path', targetPath);\r\n\r\n      const response = await fetch(`${API_URL}/sandboxes/${sandboxId}/files`, {\r\n        method: 'POST',\r\n        headers: {\r\n          Authorization: `Bearer ${session.access_token}`,\r\n        },\r\n        body: formData,\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const error = await response.text();\r\n        throw new Error(error || 'Upload failed');\r\n      }\r\n\r\n      return await response.json();\r\n    },\r\n    onSuccess: (_, variables) => {\r\n      // Invalidate directory listing for the target directory\r\n      const directoryPath = variables.targetPath.substring(0, variables.targetPath.lastIndexOf('/'));\r\n      queryClient.invalidateQueries({\r\n        queryKey: fileQueryKeys.directory(variables.sandboxId, directoryPath),\r\n      });\r\n\r\n      // Also invalidate all file listings to be safe\r\n      queryClient.invalidateQueries({\r\n        queryKey: fileQueryKeys.directories(),\r\n      });\r\n\r\n      toast.success(`Uploaded: ${variables.file.name}`);\r\n    },\r\n    onError: (error) => {\r\n      const message = error instanceof Error ? error.message : String(error);\r\n      toast.error(`Upload failed: ${message}`);\r\n    },\r\n  });\r\n}\r\n\r\n/**\r\n * Hook for deleting files\r\n */\r\nexport function useFileDelete() {\r\n  const { session } = useAuth();\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async ({\r\n      sandboxId,\r\n      filePath,\r\n    }: {\r\n      sandboxId: string;\r\n      filePath: string;\r\n    }) => {\r\n      if (!session?.access_token) {\r\n        throw new Error('No access token available');\r\n      }\r\n\r\n      const response = await fetch(\r\n        `${API_URL}/sandboxes/${sandboxId}/files?path=${encodeURIComponent(filePath)}`,\r\n        {\r\n          method: 'DELETE',\r\n          headers: {\r\n            Authorization: `Bearer ${session.access_token}`,\r\n          },\r\n        }\r\n      );\r\n\r\n      if (!response.ok) {\r\n        const error = await response.text();\r\n        throw new Error(error || 'Delete failed');\r\n      }\r\n\r\n      return await response.json();\r\n    },\r\n    onSuccess: (_, variables) => {\r\n      // Invalidate directory listing for the parent directory\r\n      const directoryPath = variables.filePath.substring(0, variables.filePath.lastIndexOf('/'));\r\n      queryClient.invalidateQueries({\r\n        queryKey: fileQueryKeys.directory(variables.sandboxId, directoryPath),\r\n      });\r\n\r\n      // Invalidate all directory listings to be safe\r\n      queryClient.invalidateQueries({\r\n        queryKey: fileQueryKeys.directories(),\r\n      });\r\n\r\n      // Invalidate all file content queries for this specific file\r\n      // This covers all content types (text, blob, json) for the deleted file\r\n      queryClient.invalidateQueries({\r\n        predicate: (query) => {\r\n          const queryKey = query.queryKey;\r\n          // Check if this is a file content query for our sandbox and file\r\n          return (\r\n            queryKey.length >= 4 &&\r\n            queryKey[0] === 'files' &&\r\n            queryKey[1] === 'content' &&\r\n            queryKey[2] === variables.sandboxId &&\r\n            queryKey[3] === variables.filePath\r\n          );\r\n        },\r\n      });\r\n\r\n      // Also remove the specific queries from cache completely\r\n      ['text', 'blob', 'json'].forEach(contentType => {\r\n        const queryKey = fileQueryKeys.content(variables.sandboxId, variables.filePath, contentType);\r\n        queryClient.removeQueries({ queryKey });\r\n      });\r\n\r\n      // Clean up legacy FileCache entries for this file\r\n      const normalizedPath = normalizePath(variables.filePath);\r\n      const legacyCacheKeys = [\r\n        `${variables.sandboxId}:${normalizedPath}:blob`,\r\n        `${variables.sandboxId}:${normalizedPath}:text`,\r\n        `${variables.sandboxId}:${normalizedPath}:json`,\r\n        `${variables.sandboxId}:${normalizedPath}`,\r\n        // Also try without leading slash for compatibility\r\n        `${variables.sandboxId}:${normalizedPath.substring(1)}:blob`,\r\n        `${variables.sandboxId}:${normalizedPath.substring(1)}:text`,\r\n        `${variables.sandboxId}:${normalizedPath.substring(1)}:json`,\r\n        `${variables.sandboxId}:${normalizedPath.substring(1)}`,\r\n      ];\r\n\r\n      legacyCacheKeys.forEach(key => {\r\n        const cachedEntry = (FileCache as any).cache?.get(key);\r\n        if (cachedEntry) {\r\n          // If it's a blob URL, revoke it before deleting\r\n          if (cachedEntry.type === 'url' && typeof cachedEntry.content === 'string' && cachedEntry.content.startsWith('blob:')) {\r\n            console.log(`[FILE DELETE] Revoking blob URL for deleted file: ${cachedEntry.content}`);\r\n            URL.revokeObjectURL(cachedEntry.content);\r\n          }\r\n          FileCache.delete(key);\r\n        }\r\n      });\r\n    },\r\n    onError: (error) => {\r\n      const message = error instanceof Error ? error.message : String(error);\r\n      toast.error(`Delete failed: ${message}`);\r\n    },\r\n  });\r\n}\r\n\r\n/**\r\n * Hook for creating files\r\n */\r\nexport function useFileCreate() {\r\n  const { session } = useAuth();\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async ({\r\n      sandboxId,\r\n      filePath,\r\n      content,\r\n    }: {\r\n      sandboxId: string;\r\n      filePath: string;\r\n      content: string;\r\n    }) => {\r\n      if (!session?.access_token) {\r\n        throw new Error('No access token available');\r\n      }\r\n\r\n      const response = await fetch(`${API_URL}/sandboxes/${sandboxId}/files`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Authorization': `Bearer ${session.access_token}`,\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify({\r\n          path: filePath,\r\n          content,\r\n        }),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const error = await response.text();\r\n        throw new Error(error || 'Create failed');\r\n      }\r\n\r\n      return await response.json();\r\n    },\r\n    onSuccess: (_, variables) => {\r\n      // Invalidate directory listing for the parent directory\r\n      const directoryPath = variables.filePath.substring(0, variables.filePath.lastIndexOf('/'));\r\n      queryClient.invalidateQueries({\r\n        queryKey: fileQueryKeys.directory(variables.sandboxId, directoryPath),\r\n      });\r\n\r\n      toast.success('File created successfully');\r\n    },\r\n    onError: (error) => {\r\n      const message = error instanceof Error ? error.message : String(error);\r\n      toast.error(`Create failed: ${message}`);\r\n    },\r\n  });\r\n} "], "names": [], "mappings": ";;;;;AA4BgB;AA5BhB;AAAA;AACA;AACA;AAAA;AACA;AACA;;;;;;;AACA,0DAA0D;AAC1D,SAAS,cAAc,IAAY;IACjC,IAAI,CAAC,MAAM,OAAO;IAElB,yCAAyC;IACzC,OAAO,KAAK,IAAI;IAEhB,4BAA4B;IAC5B,IAAI,CAAC,KAAK,UAAU,CAAC,MAAM;QACzB,OAAO,MAAM;IACf;IAEA,yCAAyC;IACzC,OAAO,KAAK,OAAO,CAAC,QAAQ;IAE5B,6CAA6C;IAC7C,IAAI,KAAK,MAAM,GAAG,KAAK,KAAK,QAAQ,CAAC,MAAM;QACzC,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC;IACxB;IAEA,OAAO;AACT;AAEA,MAAM,UAAU,iEAAuC;AAKhD,SAAS;;IACd,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE,OAAO,EACjB,SAAS,EACT,IAAI,EACJ,UAAU,EAKX;gBACC,IAAI,CAAC,SAAS,cAAc;oBAC1B,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,QAAQ;gBACxB,SAAS,MAAM,CAAC,QAAQ;gBAExB,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,WAAW,EAAE,UAAU,MAAM,CAAC,EAAE;oBACtE,QAAQ;oBACR,SAAS;wBACP,eAAe,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;oBACjD;oBACA,MAAM;gBACR;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;oBACjC,MAAM,IAAI,MAAM,SAAS;gBAC3B;gBAEA,OAAO,MAAM,SAAS,IAAI;YAC5B;;QACA,SAAS;yCAAE,CAAC,GAAG;gBACb,wDAAwD;gBACxD,MAAM,gBAAgB,UAAU,UAAU,CAAC,SAAS,CAAC,GAAG,UAAU,UAAU,CAAC,WAAW,CAAC;gBACzF,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,oLAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,UAAU,SAAS,EAAE;gBACzD;gBAEA,+CAA+C;gBAC/C,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,oLAAA,CAAA,gBAAa,CAAC,WAAW;gBACrC;gBAEA,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,UAAU,IAAI,CAAC,IAAI,EAAE;YAClD;;QACA,OAAO;yCAAE,CAAC;gBACR,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;gBAChE,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,SAAS;YACzC;;IACF;AACF;GAxDgB;;QACM,qIAAA,CAAA,UAAO;QACP,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAyDb,SAAS;;IACd,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE,OAAO,EACjB,SAAS,EACT,QAAQ,EAIT;gBACC,IAAI,CAAC,SAAS,cAAc;oBAC1B,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,WAAW,MAAM,MACrB,GAAG,QAAQ,WAAW,EAAE,UAAU,YAAY,EAAE,mBAAmB,WAAW,EAC9E;oBACE,QAAQ;oBACR,SAAS;wBACP,eAAe,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;oBACjD;gBACF;gBAGF,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;oBACjC,MAAM,IAAI,MAAM,SAAS;gBAC3B;gBAEA,OAAO,MAAM,SAAS,IAAI;YAC5B;;QACA,SAAS;yCAAE,CAAC,GAAG;gBACb,wDAAwD;gBACxD,MAAM,gBAAgB,UAAU,QAAQ,CAAC,SAAS,CAAC,GAAG,UAAU,QAAQ,CAAC,WAAW,CAAC;gBACrF,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,oLAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,UAAU,SAAS,EAAE;gBACzD;gBAEA,+CAA+C;gBAC/C,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,oLAAA,CAAA,gBAAa,CAAC,WAAW;gBACrC;gBAEA,6DAA6D;gBAC7D,wEAAwE;gBACxE,YAAY,iBAAiB,CAAC;oBAC5B,SAAS;qDAAE,CAAC;4BACV,MAAM,WAAW,MAAM,QAAQ;4BAC/B,iEAAiE;4BACjE,OACE,SAAS,MAAM,IAAI,KACnB,QAAQ,CAAC,EAAE,KAAK,WAChB,QAAQ,CAAC,EAAE,KAAK,aAChB,QAAQ,CAAC,EAAE,KAAK,UAAU,SAAS,IACnC,QAAQ,CAAC,EAAE,KAAK,UAAU,QAAQ;wBAEtC;;gBACF;gBAEA,yDAAyD;gBACzD;oBAAC;oBAAQ;oBAAQ;iBAAO,CAAC,OAAO;iDAAC,CAAA;wBAC/B,MAAM,WAAW,oLAAA,CAAA,gBAAa,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE,UAAU,QAAQ,EAAE;wBAChF,YAAY,aAAa,CAAC;4BAAE;wBAAS;oBACvC;;gBAEA,kDAAkD;gBAClD,MAAM,iBAAiB,cAAc,UAAU,QAAQ;gBACvD,MAAM,kBAAkB;oBACtB,GAAG,UAAU,SAAS,CAAC,CAAC,EAAE,eAAe,KAAK,CAAC;oBAC/C,GAAG,UAAU,SAAS,CAAC,CAAC,EAAE,eAAe,KAAK,CAAC;oBAC/C,GAAG,UAAU,SAAS,CAAC,CAAC,EAAE,eAAe,KAAK,CAAC;oBAC/C,GAAG,UAAU,SAAS,CAAC,CAAC,EAAE,gBAAgB;oBAC1C,mDAAmD;oBACnD,GAAG,UAAU,SAAS,CAAC,CAAC,EAAE,eAAe,SAAS,CAAC,GAAG,KAAK,CAAC;oBAC5D,GAAG,UAAU,SAAS,CAAC,CAAC,EAAE,eAAe,SAAS,CAAC,GAAG,KAAK,CAAC;oBAC5D,GAAG,UAAU,SAAS,CAAC,CAAC,EAAE,eAAe,SAAS,CAAC,GAAG,KAAK,CAAC;oBAC5D,GAAG,UAAU,SAAS,CAAC,CAAC,EAAE,eAAe,SAAS,CAAC,IAAI;iBACxD;gBAED,gBAAgB,OAAO;iDAAC,CAAA;wBACtB,MAAM,cAAc,AAAC,wIAAA,CAAA,YAAS,CAAS,KAAK,EAAE,IAAI;wBAClD,IAAI,aAAa;4BACf,gDAAgD;4BAChD,IAAI,YAAY,IAAI,KAAK,SAAS,OAAO,YAAY,OAAO,KAAK,YAAY,YAAY,OAAO,CAAC,UAAU,CAAC,UAAU;gCACpH,QAAQ,GAAG,CAAC,CAAC,kDAAkD,EAAE,YAAY,OAAO,EAAE;gCACtF,IAAI,eAAe,CAAC,YAAY,OAAO;4BACzC;4BACA,wIAAA,CAAA,YAAS,CAAC,MAAM,CAAC;wBACnB;oBACF;;YACF;;QACA,OAAO;yCAAE,CAAC;gBACR,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;gBAChE,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,SAAS;YACzC;;IACF;AACF;IAlGgB;;QACM,qIAAA,CAAA,UAAO;QACP,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAmGb,SAAS;;IACd,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;yCAAE,OAAO,EACjB,SAAS,EACT,QAAQ,EACR,OAAO,EAKR;gBACC,IAAI,CAAC,SAAS,cAAc;oBAC1B,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,WAAW,EAAE,UAAU,MAAM,CAAC,EAAE;oBACtE,QAAQ;oBACR,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;wBACjD,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,MAAM;wBACN;oBACF;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;oBACjC,MAAM,IAAI,MAAM,SAAS;gBAC3B;gBAEA,OAAO,MAAM,SAAS,IAAI;YAC5B;;QACA,SAAS;yCAAE,CAAC,GAAG;gBACb,wDAAwD;gBACxD,MAAM,gBAAgB,UAAU,QAAQ,CAAC,SAAS,CAAC,GAAG,UAAU,QAAQ,CAAC,WAAW,CAAC;gBACrF,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,oLAAA,CAAA,gBAAa,CAAC,SAAS,CAAC,UAAU,SAAS,EAAE;gBACzD;gBAEA,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;yCAAE,CAAC;gBACR,MAAM,UAAU,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;gBAChE,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,SAAS;YACzC;;IACF;AACF;IAnDgB;;QACM,qIAAA,CAAA,UAAO;QACP,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 3376, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/files/use-sandbox-mutations.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { createMutationHook } from '@/hooks/use-query';\r\nimport { \r\n  createSandboxFile,\r\n  createSandboxFileJson\r\n} from '@/lib/api';\r\nimport { toast } from 'sonner';\r\n\r\nexport const useCreateSandboxFile = createMutationHook(\r\n  ({ sandboxId, filePath, content }: { \r\n    sandboxId: string; \r\n    filePath: string; \r\n    content: string; \r\n  }) => createSandboxFile(sandboxId, filePath, content),\r\n  {\r\n    onSuccess: () => {\r\n      toast.success('File created successfully');\r\n    },\r\n    errorContext: {\r\n      operation: 'create file',\r\n      resource: 'sandbox file'\r\n    }\r\n  }\r\n);\r\n\r\nexport const useCreateSandboxFileJson = createMutationHook(\r\n  ({ sandboxId, filePath, content }: { \r\n    sandboxId: string; \r\n    filePath: string; \r\n    content: string; \r\n  }) => createSandboxFileJson(sandboxId, filePath, content),\r\n  {\r\n    onSuccess: () => {\r\n      toast.success('File created successfully');\r\n    },\r\n    errorContext: {\r\n      operation: 'create file',\r\n      resource: 'sandbox file'\r\n    }\r\n  }\r\n); "], "names": [], "mappings": ";;;;AAEA;AACA;AAIA;AAPA;;;;AASO,MAAM,uBAAuB,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EACnD,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAI9B,GAAK,CAAA,GAAA,oHAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW,UAAU,UAC7C;IACE,WAAW;QACT,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IACA,cAAc;QACZ,WAAW;QACX,UAAU;IACZ;AACF;AAGK,MAAM,2BAA2B,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EACvD,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAI9B,GAAK,CAAA,GAAA,oHAAA,CAAA,wBAAqB,AAAD,EAAE,WAAW,UAAU,UACjD;IACE,WAAW;QACT,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IACA,cAAc;QACZ,WAAW;QACX,UAAU;IACZ;AACF", "debugId": null}}, {"offset": {"line": 3414, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/subscriptions/use-billing.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { createMutationHook, createQueryHook } from '@/hooks/use-query';\r\nimport {\r\n  createCheckoutSession,\r\n  checkBillingStatus,\r\n  getAvailableModels,\r\n  CreateCheckoutSessionRequest\r\n} from '@/lib/api';\r\nimport { billingApi } from '@/lib/api-enhanced';\r\nimport { modelKeys, usageKeys } from './keys';\r\n\r\nexport const useAvailableModels = createQueryHook(\r\n  modelKeys.available,\r\n  getAvailableModels,\r\n  {\r\n    staleTime: 10 * 60 * 1000,\r\n    refetchOnWindowFocus: false,\r\n  }\r\n);\r\n\r\nexport const useBillingStatus = createQueryHook(\r\n  ['billing', 'status'],\r\n  checkBillingStatus,\r\n  {\r\n    staleTime: 2 * 60 * 1000,\r\n    refetchOnWindowFocus: true,\r\n  }\r\n);\r\n\r\nexport const useCreateCheckoutSession = createMutationHook(\r\n  (request: CreateCheckoutSessionRequest) => createCheckoutSession(request),\r\n  {\r\n    onSuccess: (data) => {\r\n      if (data.url) {\r\n        window.location.href = data.url;\r\n      }\r\n    },\r\n    errorContext: {\r\n      operation: 'create checkout session',\r\n      resource: 'billing'\r\n    }\r\n  }\r\n);\r\n\r\nexport const useUsageLogs = (page: number = 0, itemsPerPage: number = 1000) => \r\n  createQueryHook(\r\n    usageKeys.logs(page, itemsPerPage),\r\n    () => billingApi.getUsageLogs(page, itemsPerPage),\r\n    {\r\n      staleTime: 30 * 1000, // 30 seconds\r\n      refetchOnMount: true,\r\n      refetchOnWindowFocus: false,\r\n    }\r\n  )(); "], "names": [], "mappings": ";;;;;;AAEA;AACA;AAMA;AAAA;AACA;AAVA;;;;;AAYO,MAAM,qBAAqB,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAC9C,0JAAA,CAAA,YAAS,CAAC,SAAS,EACnB,oHAAA,CAAA,qBAAkB,EAClB;IACE,WAAW,KAAK,KAAK;IACrB,sBAAsB;AACxB;AAGK,MAAM,mBAAmB,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAC5C;IAAC;IAAW;CAAS,EACrB,oHAAA,CAAA,qBAAkB,EAClB;IACE,WAAW,IAAI,KAAK;IACpB,sBAAsB;AACxB;AAGK,MAAM,2BAA2B,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EACvD,CAAC,UAA0C,CAAA,GAAA,oHAAA,CAAA,wBAAqB,AAAD,EAAE,UACjE;IACE,WAAW,CAAC;QACV,IAAI,KAAK,GAAG,EAAE;YACZ,OAAO,QAAQ,CAAC,IAAI,GAAG,KAAK,GAAG;QACjC;IACF;IACA,cAAc;QACZ,WAAW;QACX,UAAU;IACZ;AACF;AAGK,MAAM,eAAe,CAAC,OAAe,CAAC,EAAE,eAAuB,IAAI,GACxE,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EACZ,0JAAA,CAAA,YAAS,CAAC,IAAI,CAAC,MAAM,eACrB,IAAM,gJAAA,CAAA,aAAU,CAAC,YAAY,CAAC,MAAM,eACpC;QACE,WAAW,KAAK;QAChB,gBAAgB;QAChB,sBAAsB;IACxB", "debugId": null}}, {"offset": {"line": 3466, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/files/keys.ts"], "sourcesContent": ["import { createQueryKeys } from '@/hooks/use-query';\r\n\r\nconst sandboxKeysBase = ['sandbox'] as const;\r\nconst healthKeysBase = ['health'] as const;\r\n\r\nexport const sandboxKeys = createQueryKeys({\r\n  all: sandboxKeysBase,\r\n  files: (sandboxId: string, path: string) => [...sandboxKeysBase, sandboxId, 'files', path] as const,\r\n  fileContent: (sandboxId: string, path: string) => [...sandboxKeysBase, sandboxId, 'content', path] as const,\r\n});\r\n\r\nexport const healthKeys = createQueryKeys({\r\n  all: healthKeysBase,\r\n  api: () => [...healthKeysBase, 'api'] as const,\r\n}); "], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,kBAAkB;IAAC;CAAU;AACnC,MAAM,iBAAiB;IAAC;CAAS;AAE1B,MAAM,cAAc,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAAE;IACzC,KAAK;IACL,OAAO,CAAC,WAAmB,OAAiB;eAAI;YAAiB;YAAW;YAAS;SAAK;IAC1F,aAAa,CAAC,WAAmB,OAAiB;eAAI;YAAiB;YAAW;YAAW;SAAK;AACpG;AAEO,MAAM,aAAa,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAAE;IACxC,KAAK;IACL,KAAK,IAAM;eAAI;YAAgB;SAAM;AACvC", "debugId": null}}, {"offset": {"line": 3509, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/usage/use-health.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { createQueryHook } from '@/hooks/use-query';\r\nimport { checkApiHealth } from '@/lib/api';\r\nimport { healthKeys } from '../files/keys';\r\n\r\nexport const useApiHealth = createQueryHook(\r\n  healthKeys.api(),\r\n  checkApiHealth,\r\n  {\r\n    staleTime: 30 * 1000,\r\n    refetchInterval: 60 * 1000,\r\n    refetchOnWindowFocus: true,\r\n    retry: 3,\r\n  }\r\n); "], "names": [], "mappings": ";;;AAEA;AACA;AACA;AAJA;;;;AAMO,MAAM,eAAe,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EACxC,kJAAA,CAAA,aAAU,CAAC,GAAG,IACd,oHAAA,CAAA,iBAAc,EACd;IACE,WAAW,KAAK;IAChB,iBAAiB,KAAK;IACtB,sBAAsB;IACtB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3534, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/knowledge-base/keys.ts"], "sourcesContent": ["export const knowledgeBaseKeys = {\r\n    all: ['knowledge-base'] as const,\r\n    threads: () => [...knowledgeBaseKeys.all, 'threads'] as const,\r\n    thread: (threadId: string) => [...knowledgeBaseKeys.threads(), threadId] as const,\r\n    agents: () => [...knowledgeBaseKeys.all, 'agents'] as const,\r\n    agent: (agentId: string) => [...knowledgeBaseKeys.agents(), agentId] as const,\r\n    entry: (entryId: string) => [...knowledgeBaseKeys.all, 'entry', entryId] as const,\r\n    context: (threadId: string) => [...knowledgeBaseKeys.all, 'context', threadId] as const,\r\n    agentContext: (agentId: string) => [...knowledgeBaseKeys.all, 'agent-context', agentId] as const,\r\n    combinedContext: (threadId: string, agentId?: string) => [...knowledgeBaseKeys.all, 'combined-context', threadId, agentId] as const,\r\n    processingJobs: (agentId: string) => [...knowledgeBaseKeys.all, 'processing-jobs', agentId] as const,\r\n  };"], "names": [], "mappings": ";;;AAAO,MAAM,oBAAoB;IAC7B,KAAK;QAAC;KAAiB;IACvB,SAAS,IAAM;eAAI,kBAAkB,GAAG;YAAE;SAAU;IACpD,QAAQ,CAAC,WAAqB;eAAI,kBAAkB,OAAO;YAAI;SAAS;IACxE,QAAQ,IAAM;eAAI,kBAAkB,GAAG;YAAE;SAAS;IAClD,OAAO,CAAC,UAAoB;eAAI,kBAAkB,MAAM;YAAI;SAAQ;IACpE,OAAO,CAAC,UAAoB;eAAI,kBAAkB,GAAG;YAAE;YAAS;SAAQ;IACxE,SAAS,CAAC,WAAqB;eAAI,kBAAkB,GAAG;YAAE;YAAW;SAAS;IAC9E,cAAc,CAAC,UAAoB;eAAI,kBAAkB,GAAG;YAAE;YAAiB;SAAQ;IACvF,iBAAiB,CAAC,UAAkB,UAAqB;eAAI,kBAAkB,GAAG;YAAE;YAAoB;YAAU;SAAQ;IAC1H,gBAAgB,CAAC,UAAoB;eAAI,kBAAkB,GAAG;YAAE;YAAmB;SAAQ;AAC7F", "debugId": null}}, {"offset": {"line": 3593, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/knowledge-base/use-knowledge-base-queries.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\r\nimport { toast } from 'sonner';\r\nimport { createClient } from '@/lib/supabase/client';\r\nimport { knowledgeBaseKeys } from './keys';\r\nimport { \r\n  CreateKnowledgeBaseEntryRequest, \r\n  KnowledgeBaseEntry, \r\n  KnowledgeBaseListResponse, \r\n  UpdateKnowledgeBaseEntryRequest,\r\n  FileUploadRequest,\r\n  GitCloneRequest,\r\n  ProcessingJob,\r\n  ProcessingJobsResponse,\r\n  UploadResponse,\r\n  CloneResponse\r\n} from './types';\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || '';\r\n\r\nconst useAuthHeaders = () => {\r\n  const getHeaders = async () => {\r\n    const supabase = createClient();\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n    \r\n    if (!session?.access_token) {\r\n      throw new Error('No access token available');\r\n    }\r\n    return {\r\n      'Authorization': `Bearer ${session.access_token}`,\r\n      'Content-Type': 'application/json',\r\n    };  \r\n  };\r\n  \r\n  return { getHeaders };\r\n};\r\n\r\nexport function useKnowledgeBaseEntries(threadId: string, includeInactive = false) {\r\n  const { getHeaders } = useAuthHeaders();\r\n  \r\n  return useQuery({\r\n    queryKey: knowledgeBaseKeys.thread(threadId),\r\n    queryFn: async (): Promise<KnowledgeBaseListResponse> => {\r\n      const headers = await getHeaders();\r\n      const url = new URL(`${API_URL}/knowledge-base/threads/${threadId}`);\r\n      url.searchParams.set('include_inactive', includeInactive.toString());\r\n      \r\n      const response = await fetch(url.toString(), { headers });\r\n      \r\n      if (!response.ok) {\r\n        const error = await response.text();\r\n        throw new Error(error || 'Failed to fetch knowledge base entries');\r\n      }\r\n      \r\n      return await response.json();\r\n    },\r\n    enabled: !!threadId,\r\n  });\r\n}\r\n\r\nexport function useKnowledgeBaseEntry(entryId: string) {\r\n  const { getHeaders } = useAuthHeaders();\r\n  \r\n  return useQuery({\r\n    queryKey: knowledgeBaseKeys.entry(entryId),\r\n    queryFn: async (): Promise<KnowledgeBaseEntry> => {\r\n      const headers = await getHeaders();\r\n      const response = await fetch(`${API_URL}/knowledge-base/${entryId}`, { headers });\r\n      \r\n      if (!response.ok) {\r\n        const error = await response.text();\r\n        throw new Error(error || 'Failed to fetch knowledge base entry');\r\n      }\r\n      \r\n      return await response.json();\r\n    },\r\n    enabled: !!entryId,\r\n  });\r\n}\r\n\r\nexport function useKnowledgeBaseContext(threadId: string, maxTokens = 4000) {\r\n  const { getHeaders } = useAuthHeaders();\r\n  \r\n  return useQuery({\r\n    queryKey: knowledgeBaseKeys.context(threadId),\r\n    queryFn: async () => {\r\n      const headers = await getHeaders();\r\n      const url = new URL(`${API_URL}/knowledge-base/threads/${threadId}/context`);\r\n      url.searchParams.set('max_tokens', maxTokens.toString());\r\n      \r\n      const response = await fetch(url.toString(), { headers });\r\n      \r\n      if (!response.ok) {\r\n        const error = await response.text();\r\n        throw new Error(error || 'Failed to fetch knowledge base context');\r\n      }\r\n      \r\n      return await response.json();\r\n    },\r\n    enabled: !!threadId,\r\n  });\r\n}\r\n\r\nexport function useCreateKnowledgeBaseEntry() {\r\n  const queryClient = useQueryClient();\r\n  const { getHeaders } = useAuthHeaders();\r\n  \r\n  return useMutation({\r\n    mutationFn: async ({ threadId, data }: { threadId: string; data: CreateKnowledgeBaseEntryRequest }) => {\r\n      const headers = await getHeaders();\r\n      const response = await fetch(`${API_URL}/knowledge-base/threads/${threadId}`, {\r\n        method: 'POST',\r\n        headers: {\r\n          ...headers,\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(data),\r\n      });\r\n      \r\n      if (!response.ok) {\r\n        const error = await response.text();\r\n        throw new Error(error || 'Failed to create knowledge base entry');\r\n      }\r\n      \r\n      return await response.json();\r\n    },\r\n    onSuccess: (_, { threadId }) => {\r\n      queryClient.invalidateQueries({ queryKey: knowledgeBaseKeys.thread(threadId) });\r\n      queryClient.invalidateQueries({ queryKey: knowledgeBaseKeys.context(threadId) });\r\n      toast.success('Knowledge base entry created successfully');\r\n    },\r\n    onError: (error) => {\r\n      toast.error(`Failed to create knowledge base entry: ${error.message}`);\r\n    },\r\n  });\r\n}\r\n\r\nexport function useUpdateKnowledgeBaseEntry() {\r\n  const queryClient = useQueryClient();\r\n  const { getHeaders } = useAuthHeaders();\r\n  \r\n  return useMutation({\r\n    mutationFn: async ({ entryId, data }: { entryId: string; data: UpdateKnowledgeBaseEntryRequest }) => {\r\n      const headers = await getHeaders();\r\n      const response = await fetch(`${API_URL}/knowledge-base/${entryId}`, {\r\n        method: 'PUT',\r\n        headers: {\r\n          ...headers,\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(data),\r\n      });\r\n      \r\n      if (!response.ok) {\r\n        const error = await response.text();\r\n        throw new Error(error || 'Failed to update knowledge base entry');\r\n      }\r\n      \r\n      return await response.json();\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: knowledgeBaseKeys.all });\r\n      toast.success('Knowledge base entry updated successfully');\r\n    },\r\n    onError: (error) => {\r\n      toast.error(`Failed to update knowledge base entry: ${error.message}`);\r\n    },\r\n  });\r\n}\r\n\r\nexport function useDeleteKnowledgeBaseEntry() {\r\n  const queryClient = useQueryClient();\r\n  const { getHeaders } = useAuthHeaders();\r\n  \r\n  return useMutation({\r\n    mutationFn: async (entryId: string) => {\r\n      const headers = await getHeaders();\r\n      const response = await fetch(`${API_URL}/knowledge-base/${entryId}`, {\r\n        method: 'DELETE',\r\n        headers,\r\n      });\r\n      \r\n      if (!response.ok) {\r\n        const error = await response.text();\r\n        throw new Error(error || 'Failed to delete knowledge base entry');\r\n      }\r\n      \r\n      return await response.json();\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: knowledgeBaseKeys.all });\r\n      toast.success('Knowledge base entry deleted successfully');\r\n    },\r\n    onError: (error) => {\r\n      toast.error(`Failed to delete knowledge base entry: ${error.message}`);\r\n    },\r\n  });\r\n}\r\n\r\nexport function useAgentKnowledgeBaseEntries(agentId: string, includeInactive = false) {\r\n  const { getHeaders } = useAuthHeaders();\r\n  \r\n  return useQuery({\r\n    queryKey: knowledgeBaseKeys.agent(agentId),\r\n    queryFn: async (): Promise<KnowledgeBaseListResponse> => {\r\n      const headers = await getHeaders();\r\n      const url = new URL(`${API_URL}/knowledge-base/agents/${agentId}`);\r\n      url.searchParams.set('include_inactive', includeInactive.toString());\r\n      \r\n      const response = await fetch(url.toString(), { headers });\r\n      \r\n      if (!response.ok) {\r\n        const error = await response.text();\r\n        throw new Error(error || 'Failed to fetch agent knowledge base entries');\r\n      }\r\n      \r\n      return await response.json();\r\n    },\r\n    enabled: !!agentId,\r\n  });\r\n}\r\n\r\nexport function useCreateAgentKnowledgeBaseEntry() {\r\n  const queryClient = useQueryClient();\r\n  const { getHeaders } = useAuthHeaders();\r\n  \r\n  return useMutation({\r\n    mutationFn: async ({ agentId, data }: { agentId: string; data: CreateKnowledgeBaseEntryRequest }) => {\r\n      const headers = await getHeaders();\r\n      const response = await fetch(`${API_URL}/knowledge-base/agents/${agentId}`, {\r\n        method: 'POST',\r\n        headers: {\r\n          ...headers,\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(data),\r\n      });\r\n      \r\n      if (!response.ok) {\r\n        const error = await response.text();\r\n        throw new Error(error || 'Failed to create agent knowledge base entry');\r\n      }\r\n      \r\n      return await response.json();\r\n    },\r\n    onSuccess: (_, { agentId }) => {\r\n      queryClient.invalidateQueries({ queryKey: knowledgeBaseKeys.agent(agentId) });\r\n      queryClient.invalidateQueries({ queryKey: knowledgeBaseKeys.agentContext(agentId) });\r\n      toast.success('Agent knowledge entry created successfully');\r\n    },\r\n    onError: (error) => {\r\n      toast.error(`Failed to create agent knowledge entry: ${error.message}`);\r\n    },\r\n  });\r\n}\r\n\r\nexport function useAgentKnowledgeBaseContext(agentId: string, maxTokens = 4000) {\r\n  const { getHeaders } = useAuthHeaders();\r\n  \r\n  return useQuery({\r\n    queryKey: knowledgeBaseKeys.agentContext(agentId),\r\n    queryFn: async () => {\r\n      const headers = await getHeaders();\r\n      const url = new URL(`${API_URL}/knowledge-base/agents/${agentId}/context`);\r\n      url.searchParams.set('max_tokens', maxTokens.toString());\r\n      \r\n      const response = await fetch(url.toString(), { headers });\r\n      \r\n      if (!response.ok) {\r\n        const error = await response.text();\r\n        throw new Error(error || 'Failed to fetch agent knowledge base context');\r\n      }\r\n      \r\n      return await response.json();\r\n    },\r\n    enabled: !!agentId,\r\n  });\r\n}\r\n\r\nexport function useCombinedKnowledgeBaseContext(threadId: string, agentId?: string, maxTokens = 4000) {\r\n  const { getHeaders } = useAuthHeaders();\r\n  \r\n  return useQuery({\r\n    queryKey: knowledgeBaseKeys.combinedContext(threadId, agentId),\r\n    queryFn: async () => {\r\n      const headers = await getHeaders();\r\n      const url = new URL(`${API_URL}/knowledge-base/threads/${threadId}/combined-context`);\r\n      url.searchParams.set('max_tokens', maxTokens.toString());\r\n      if (agentId) {\r\n        url.searchParams.set('agent_id', agentId);\r\n      }\r\n      \r\n      const response = await fetch(url.toString(), { headers });\r\n      \r\n      if (!response.ok) {\r\n        const error = await response.text();\r\n        throw new Error(error || 'Failed to fetch combined knowledge base context');\r\n      }\r\n      \r\n      return await response.json();\r\n    },\r\n    enabled: !!threadId,\r\n  });\r\n}\r\n\r\n// New hooks for file upload and git clone operations\r\nexport function useUploadAgentFiles() {\r\n  const queryClient = useQueryClient();\r\n  const { getHeaders } = useAuthHeaders();\r\n  \r\n  return useMutation({\r\n    mutationFn: async ({ agentId, file }: FileUploadRequest): Promise<UploadResponse> => {\r\n      const supabase = createClient();\r\n      const { data: { session } } = await supabase.auth.getSession();\r\n      \r\n      if (!session?.access_token) {\r\n        throw new Error('No access token available');\r\n      }\r\n\r\n      const formData = new FormData();\r\n      formData.append('file', file);\r\n\r\n      const response = await fetch(`${API_URL}/knowledge-base/agents/${agentId}/upload-file`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Authorization': `Bearer ${session.access_token}`,\r\n        },\r\n        body: formData,\r\n      });\r\n      \r\n      if (!response.ok) {\r\n        const error = await response.text();\r\n        throw new Error(error || 'Failed to upload file');\r\n      }\r\n      \r\n      return await response.json();\r\n    },\r\n    onSuccess: (data, { agentId }) => {\r\n      queryClient.invalidateQueries({ queryKey: knowledgeBaseKeys.agent(agentId) });\r\n      queryClient.invalidateQueries({ queryKey: knowledgeBaseKeys.processingJobs(agentId) });\r\n      toast.success('File uploaded successfully. Processing in background.');\r\n    },\r\n    onError: (error) => {\r\n      toast.error(`Failed to upload file: ${error.message}`);\r\n    },\r\n  });\r\n}\r\n\r\nexport function useCloneGitRepository() {\r\n  const queryClient = useQueryClient();\r\n  const { getHeaders } = useAuthHeaders();\r\n  \r\n  return useMutation({\r\n    mutationFn: async ({ agentId, git_url, branch = 'main' }: GitCloneRequest): Promise<CloneResponse> => {\r\n      const headers = await getHeaders();\r\n      const response = await fetch(`${API_URL}/knowledge-base/agents/${agentId}/clone-git-repo`, {\r\n        method: 'POST',\r\n        headers,\r\n        body: JSON.stringify({ git_url, branch }),\r\n      });\r\n      \r\n      if (!response.ok) {\r\n        const error = await response.text();\r\n        throw new Error(error || 'Failed to clone repository');\r\n      }\r\n      \r\n      return await response.json();\r\n    },\r\n    onSuccess: (data, { agentId }) => {\r\n      queryClient.invalidateQueries({ queryKey: knowledgeBaseKeys.agent(agentId) });\r\n      queryClient.invalidateQueries({ queryKey: knowledgeBaseKeys.processingJobs(agentId) });\r\n      toast.success('Repository cloning started. Processing in background.');\r\n    },\r\n    onError: (error) => {\r\n      toast.error(`Failed to clone repository: ${error.message}`);\r\n    },\r\n  });\r\n}\r\n\r\nexport function useAgentProcessingJobs(agentId: string) {\r\n  const { getHeaders } = useAuthHeaders();\r\n  \r\n  return useQuery({\r\n    queryKey: knowledgeBaseKeys.processingJobs(agentId),\r\n    queryFn: async (): Promise<ProcessingJobsResponse> => {\r\n      const headers = await getHeaders();\r\n      const response = await fetch(`${API_URL}/knowledge-base/agents/${agentId}/processing-jobs`, { headers });\r\n      \r\n      if (!response.ok) {\r\n        const error = await response.text();\r\n        throw new Error(error || 'Failed to fetch processing jobs');\r\n      }\r\n      \r\n      return await response.json();\r\n    },\r\n    enabled: !!agentId,\r\n    refetchInterval: 5000,\r\n  });\r\n} \r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAiBgB;AAjBhB;AAAA;AAAA;AACA;AACA;AACA;;;;;;AAcA,MAAM,UAAU,iEAAuC;AAEvD,MAAM,iBAAiB;IACrB,MAAM,aAAa;QACjB,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAE5D,IAAI,CAAC,SAAS,cAAc;YAC1B,MAAM,IAAI,MAAM;QAClB;QACA,OAAO;YACL,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACjD,gBAAgB;QAClB;IACF;IAEA,OAAO;QAAE;IAAW;AACtB;AAEO,SAAS,wBAAwB,QAAgB,EAAE,kBAAkB,KAAK;;IAC/E,MAAM,EAAE,UAAU,EAAE,GAAG;IAEvB,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,8JAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC;QACnC,OAAO;gDAAE;gBACP,MAAM,UAAU,MAAM;gBACtB,MAAM,MAAM,IAAI,IAAI,GAAG,QAAQ,wBAAwB,EAAE,UAAU;gBACnE,IAAI,YAAY,CAAC,GAAG,CAAC,oBAAoB,gBAAgB,QAAQ;gBAEjE,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ,IAAI;oBAAE;gBAAQ;gBAEvD,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;oBACjC,MAAM,IAAI,MAAM,SAAS;gBAC3B;gBAEA,OAAO,MAAM,SAAS,IAAI;YAC5B;;QACA,SAAS,CAAC,CAAC;IACb;AACF;GArBgB;;QACS;QAEhB,8KAAA,CAAA,WAAQ;;;AAoBV,SAAS,sBAAsB,OAAe;;IACnD,MAAM,EAAE,UAAU,EAAE,GAAG;IAEvB,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,8JAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC;QAClC,OAAO;8CAAE;gBACP,MAAM,UAAU,MAAM;gBACtB,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,gBAAgB,EAAE,SAAS,EAAE;oBAAE;gBAAQ;gBAE/E,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;oBACjC,MAAM,IAAI,MAAM,SAAS;gBAC3B;gBAEA,OAAO,MAAM,SAAS,IAAI;YAC5B;;QACA,SAAS,CAAC,CAAC;IACb;AACF;IAlBgB;;QACS;QAEhB,8KAAA,CAAA,WAAQ;;;AAiBV,SAAS,wBAAwB,QAAgB,EAAE,YAAY,IAAI;;IACxE,MAAM,EAAE,UAAU,EAAE,GAAG;IAEvB,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,8JAAA,CAAA,oBAAiB,CAAC,OAAO,CAAC;QACpC,OAAO;gDAAE;gBACP,MAAM,UAAU,MAAM;gBACtB,MAAM,MAAM,IAAI,IAAI,GAAG,QAAQ,wBAAwB,EAAE,SAAS,QAAQ,CAAC;gBAC3E,IAAI,YAAY,CAAC,GAAG,CAAC,cAAc,UAAU,QAAQ;gBAErD,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ,IAAI;oBAAE;gBAAQ;gBAEvD,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;oBACjC,MAAM,IAAI,MAAM,SAAS;gBAC3B;gBAEA,OAAO,MAAM,SAAS,IAAI;YAC5B;;QACA,SAAS,CAAC,CAAC;IACb;AACF;IArBgB;;QACS;QAEhB,8KAAA,CAAA,WAAQ;;;AAoBV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,UAAU,EAAE,GAAG;IAEvB,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;uDAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAA+D;gBAChG,MAAM,UAAU,MAAM;gBACtB,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,wBAAwB,EAAE,UAAU,EAAE;oBAC5E,QAAQ;oBACR,SAAS;wBACP,GAAG,OAAO;wBACV,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;oBACjC,MAAM,IAAI,MAAM,SAAS;gBAC3B;gBAEA,OAAO,MAAM,SAAS,IAAI;YAC5B;;QACA,SAAS;uDAAE,CAAC,GAAG,EAAE,QAAQ,EAAE;gBACzB,YAAY,iBAAiB,CAAC;oBAAE,UAAU,8JAAA,CAAA,oBAAiB,CAAC,MAAM,CAAC;gBAAU;gBAC7E,YAAY,iBAAiB,CAAC;oBAAE,UAAU,8JAAA,CAAA,oBAAiB,CAAC,OAAO,CAAC;gBAAU;gBAC9E,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;uDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,uCAAuC,EAAE,MAAM,OAAO,EAAE;YACvE;;IACF;AACF;IAhCgB;;QACM,yLAAA,CAAA,iBAAc;QACX;QAEhB,iLAAA,CAAA,cAAW;;;AA8Bb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,UAAU,EAAE,GAAG;IAEvB,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;uDAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAA8D;gBAC9F,MAAM,UAAU,MAAM;gBACtB,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,gBAAgB,EAAE,SAAS,EAAE;oBACnE,QAAQ;oBACR,SAAS;wBACP,GAAG,OAAO;wBACV,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;oBACjC,MAAM,IAAI,MAAM,SAAS;gBAC3B;gBAEA,OAAO,MAAM,SAAS,IAAI;YAC5B;;QACA,SAAS;uDAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,8JAAA,CAAA,oBAAiB,CAAC,GAAG;gBAAC;gBAChE,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;uDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,uCAAuC,EAAE,MAAM,OAAO,EAAE;YACvE;;IACF;AACF;IA/BgB;;QACM,yLAAA,CAAA,iBAAc;QACX;QAEhB,iLAAA,CAAA,cAAW;;;AA6Bb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,UAAU,EAAE,GAAG;IAEvB,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;uDAAE,OAAO;gBACjB,MAAM,UAAU,MAAM;gBACtB,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,gBAAgB,EAAE,SAAS,EAAE;oBACnE,QAAQ;oBACR;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;oBACjC,MAAM,IAAI,MAAM,SAAS;gBAC3B;gBAEA,OAAO,MAAM,SAAS,IAAI;YAC5B;;QACA,SAAS;uDAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU,8JAAA,CAAA,oBAAiB,CAAC,GAAG;gBAAC;gBAChE,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;uDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,uCAAuC,EAAE,MAAM,OAAO,EAAE;YACvE;;IACF;AACF;IA3BgB;;QACM,yLAAA,CAAA,iBAAc;QACX;QAEhB,iLAAA,CAAA,cAAW;;;AAyBb,SAAS,6BAA6B,OAAe,EAAE,kBAAkB,KAAK;;IACnF,MAAM,EAAE,UAAU,EAAE,GAAG;IAEvB,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,8JAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC;QAClC,OAAO;qDAAE;gBACP,MAAM,UAAU,MAAM;gBACtB,MAAM,MAAM,IAAI,IAAI,GAAG,QAAQ,uBAAuB,EAAE,SAAS;gBACjE,IAAI,YAAY,CAAC,GAAG,CAAC,oBAAoB,gBAAgB,QAAQ;gBAEjE,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ,IAAI;oBAAE;gBAAQ;gBAEvD,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;oBACjC,MAAM,IAAI,MAAM,SAAS;gBAC3B;gBAEA,OAAO,MAAM,SAAS,IAAI;YAC5B;;QACA,SAAS,CAAC,CAAC;IACb;AACF;IArBgB;;QACS;QAEhB,8KAAA,CAAA,WAAQ;;;AAoBV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,UAAU,EAAE,GAAG;IAEvB,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4DAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAA8D;gBAC9F,MAAM,UAAU,MAAM;gBACtB,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,uBAAuB,EAAE,SAAS,EAAE;oBAC1E,QAAQ;oBACR,SAAS;wBACP,GAAG,OAAO;wBACV,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;oBACjC,MAAM,IAAI,MAAM,SAAS;gBAC3B;gBAEA,OAAO,MAAM,SAAS,IAAI;YAC5B;;QACA,SAAS;4DAAE,CAAC,GAAG,EAAE,OAAO,EAAE;gBACxB,YAAY,iBAAiB,CAAC;oBAAE,UAAU,8JAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC;gBAAS;gBAC3E,YAAY,iBAAiB,CAAC;oBAAE,UAAU,8JAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC;gBAAS;gBAClF,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;4DAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,wCAAwC,EAAE,MAAM,OAAO,EAAE;YACxE;;IACF;AACF;IAhCgB;;QACM,yLAAA,CAAA,iBAAc;QACX;QAEhB,iLAAA,CAAA,cAAW;;;AA8Bb,SAAS,6BAA6B,OAAe,EAAE,YAAY,IAAI;;IAC5E,MAAM,EAAE,UAAU,EAAE,GAAG;IAEvB,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,8JAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC;QACzC,OAAO;qDAAE;gBACP,MAAM,UAAU,MAAM;gBACtB,MAAM,MAAM,IAAI,IAAI,GAAG,QAAQ,uBAAuB,EAAE,QAAQ,QAAQ,CAAC;gBACzE,IAAI,YAAY,CAAC,GAAG,CAAC,cAAc,UAAU,QAAQ;gBAErD,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ,IAAI;oBAAE;gBAAQ;gBAEvD,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;oBACjC,MAAM,IAAI,MAAM,SAAS;gBAC3B;gBAEA,OAAO,MAAM,SAAS,IAAI;YAC5B;;QACA,SAAS,CAAC,CAAC;IACb;AACF;IArBgB;;QACS;QAEhB,8KAAA,CAAA,WAAQ;;;AAoBV,SAAS,gCAAgC,QAAgB,EAAE,OAAgB,EAAE,YAAY,IAAI;;IAClG,MAAM,EAAE,UAAU,EAAE,GAAG;IAEvB,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,8JAAA,CAAA,oBAAiB,CAAC,eAAe,CAAC,UAAU;QACtD,OAAO;wDAAE;gBACP,MAAM,UAAU,MAAM;gBACtB,MAAM,MAAM,IAAI,IAAI,GAAG,QAAQ,wBAAwB,EAAE,SAAS,iBAAiB,CAAC;gBACpF,IAAI,YAAY,CAAC,GAAG,CAAC,cAAc,UAAU,QAAQ;gBACrD,IAAI,SAAS;oBACX,IAAI,YAAY,CAAC,GAAG,CAAC,YAAY;gBACnC;gBAEA,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ,IAAI;oBAAE;gBAAQ;gBAEvD,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;oBACjC,MAAM,IAAI,MAAM,SAAS;gBAC3B;gBAEA,OAAO,MAAM,SAAS,IAAI;YAC5B;;QACA,SAAS,CAAC,CAAC;IACb;AACF;IAxBgB;;QACS;QAEhB,8KAAA,CAAA,WAAQ;;;AAwBV,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,UAAU,EAAE,GAAG;IAEvB,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;+CAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAqB;gBACrD,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;gBAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;gBAE5D,IAAI,CAAC,SAAS,cAAc;oBAC1B,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,WAAW,IAAI;gBACrB,SAAS,MAAM,CAAC,QAAQ;gBAExB,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,uBAAuB,EAAE,QAAQ,YAAY,CAAC,EAAE;oBACtF,QAAQ;oBACR,SAAS;wBACP,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;oBACnD;oBACA,MAAM;gBACR;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;oBACjC,MAAM,IAAI,MAAM,SAAS;gBAC3B;gBAEA,OAAO,MAAM,SAAS,IAAI;YAC5B;;QACA,SAAS;+CAAE,CAAC,MAAM,EAAE,OAAO,EAAE;gBAC3B,YAAY,iBAAiB,CAAC;oBAAE,UAAU,8JAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC;gBAAS;gBAC3E,YAAY,iBAAiB,CAAC;oBAAE,UAAU,8JAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC;gBAAS;gBACpF,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;+CAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,uBAAuB,EAAE,MAAM,OAAO,EAAE;YACvD;;IACF;AACF;KAxCgB;;QACM,yLAAA,CAAA,iBAAc;QACX;QAEhB,iLAAA,CAAA,cAAW;;;AAsCb,SAAS;;IACd,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,UAAU,EAAE,GAAG;IAEvB,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;iDAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,MAAM,EAAmB;gBACvE,MAAM,UAAU,MAAM;gBACtB,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,uBAAuB,EAAE,QAAQ,eAAe,CAAC,EAAE;oBACzF,QAAQ;oBACR;oBACA,MAAM,KAAK,SAAS,CAAC;wBAAE;wBAAS;oBAAO;gBACzC;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;oBACjC,MAAM,IAAI,MAAM,SAAS;gBAC3B;gBAEA,OAAO,MAAM,SAAS,IAAI;YAC5B;;QACA,SAAS;iDAAE,CAAC,MAAM,EAAE,OAAO,EAAE;gBAC3B,YAAY,iBAAiB,CAAC;oBAAE,UAAU,8JAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC;gBAAS;gBAC3E,YAAY,iBAAiB,CAAC;oBAAE,UAAU,8JAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC;gBAAS;gBACpF,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;iDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,4BAA4B,EAAE,MAAM,OAAO,EAAE;YAC5D;;IACF;AACF;KA7BgB;;QACM,yLAAA,CAAA,iBAAc;QACX;QAEhB,iLAAA,CAAA,cAAW;;;AA2Bb,SAAS,uBAAuB,OAAe;;IACpD,MAAM,EAAE,UAAU,EAAE,GAAG;IAEvB,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,8JAAA,CAAA,oBAAiB,CAAC,cAAc,CAAC;QAC3C,OAAO;+CAAE;gBACP,MAAM,UAAU,MAAM;gBACtB,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,uBAAuB,EAAE,QAAQ,gBAAgB,CAAC,EAAE;oBAAE;gBAAQ;gBAEtG,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;oBACjC,MAAM,IAAI,MAAM,SAAS;gBAC3B;gBAEA,OAAO,MAAM,SAAS,IAAI;YAC5B;;QACA,SAAS,CAAC,CAAC;QACX,iBAAiB;IACnB;AACF;KAnBgB;;QACS;QAEhB,8KAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 4132, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/triggers/use-trigger-providers.ts"], "sourcesContent": ["import { useQuery } from '@tanstack/react-query';\r\nimport { TriggerProvider } from '@/components/agents/triggers/types';\r\nimport { createClient } from '@/lib/supabase/client';\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_BACKEND_URL;\r\n\r\nconst fetchTriggerProviders = async (): Promise<TriggerProvider[]> => {\r\n  const supabase = createClient();\r\n  const { data: { session } } = await supabase.auth.getSession();\r\n  if (!session) {\r\n    throw new Error('You must be logged in to create a trigger');\r\n  }\r\n  const response = await fetch(`${API_URL}/triggers/providers`, {\r\n    headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${session.access_token}` },\r\n  });\r\n  if (!response.ok) {\r\n    throw new Error('Failed to fetch trigger providers');\r\n  }\r\n  return response.json();\r\n};\r\n\r\nexport const useTriggerProviders = () => {\r\n  return useQuery({\r\n    queryKey: ['trigger-providers'],\r\n    queryFn: fetchTriggerProviders,\r\n    staleTime: 5 * 60 * 1000,\r\n    gcTime: 10 * 60 * 1000,\r\n  });\r\n}; "], "names": [], "mappings": ";;;AAIgB;AAJhB;AAEA;;;;AAEA,MAAM;AAEN,MAAM,wBAAwB;IAC5B,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;IAC5D,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,mBAAmB,CAAC,EAAE;QAC5D,SAAS;YAAE,gBAAgB;YAAoB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;QAAC;IACnG;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO,SAAS,IAAI;AACtB;AAEO,MAAM,sBAAsB;;IACjC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;SAAoB;QAC/B,SAAS;QACT,WAAW,IAAI,KAAK;QACpB,QAAQ,KAAK,KAAK;IACpB;AACF;GAPa;;QACJ,8KAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 4184, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/triggers/use-agent-triggers.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\r\nimport { TriggerConfiguration } from '@/components/agents/triggers/types';\r\nimport { createClient } from '@/lib/supabase/client';\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_BACKEND_URL;\r\n\r\nconst fetchAgentTriggers = async (agentId: string): Promise<TriggerConfiguration[]> => {\r\n    const supabase = createClient();\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n    if (!session) {\r\n        throw new Error('You must be logged in to create a trigger');\r\n    }\r\n    const response = await fetch(`${API_URL}/triggers/agents/${agentId}/triggers`, {\r\n    headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${session.access_token}` },\r\n  });\r\n  if (!response.ok) {\r\n    throw new Error('Failed to fetch agent triggers');\r\n  }\r\n  return response.json();\r\n};\r\n\r\nconst createTrigger = async (data: {\r\n  agentId: string;\r\n  provider_id: string;\r\n  name: string;\r\n  description?: string;\r\n  config: Record<string, any>;\r\n}): Promise<TriggerConfiguration> => {\r\n    const supabase = createClient();\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n    if (!session) {\r\n        throw new Error('You must be logged in to create a trigger');\r\n    }\r\n    const response = await fetch(`${API_URL}/triggers/agents/${data.agentId}/triggers`, {\r\n    method: 'POST',\r\n    headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${session.access_token}` },\r\n    body: JSON.stringify({\r\n      provider_id: data.provider_id,\r\n      name: data.name,\r\n      description: data.description,\r\n      config: data.config,\r\n    }),\r\n  });\r\n  \r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.detail || 'Failed to create trigger');\r\n  }\r\n  \r\n  return response.json();\r\n};\r\n\r\nconst updateTrigger = async (data: {\r\n  triggerId: string;\r\n  name?: string;\r\n  description?: string;\r\n  config?: Record<string, any>;\r\n  is_active?: boolean;\r\n}): Promise<TriggerConfiguration> => {\r\n    const supabase = createClient();\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n    if (!session) {\r\n        throw new Error('You must be logged in to create a trigger');\r\n    }\r\n    const response = await fetch(`${API_URL}/triggers/${data.triggerId}`, {\r\n    method: 'PUT',\r\n    headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${session.access_token}` },\r\n    body: JSON.stringify({\r\n      name: data.name,\r\n      description: data.description,\r\n      config: data.config,\r\n      is_active: data.is_active,\r\n    }),\r\n  });\r\n  \r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.detail || 'Failed to update trigger');\r\n  }\r\n  \r\n  return response.json();\r\n};\r\n\r\nconst deleteTrigger = async (triggerId: string): Promise<void> => {\r\n  const supabase = createClient();\r\n  const { data: { session } } = await supabase.auth.getSession();\r\n  if (!session) {\r\n    throw new Error('You must be logged in to create a trigger');\r\n  }\r\n  const response = await fetch(`${API_URL}/triggers/${triggerId}`, {\r\n    method: 'DELETE',\r\n    headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${session.access_token}` },\r\n  });\r\n  \r\n  if (!response.ok) {\r\n    const error = await response.json();\r\n    throw new Error(error.detail || 'Failed to delete trigger');\r\n  }\r\n};\r\n\r\nexport const useAgentTriggers = (agentId: string) => {\r\n  return useQuery({\r\n    queryKey: ['agent-triggers', agentId],\r\n    queryFn: () => fetchAgentTriggers(agentId),\r\n    enabled: !!agentId,\r\n    staleTime: 2 * 60 * 1000,\r\n  });\r\n};\r\n\r\nexport const useCreateTrigger = () => {\r\n  const queryClient = useQueryClient();\r\n  \r\n  return useMutation({\r\n    mutationFn: createTrigger,\r\n    onSuccess: (newTrigger) => {\r\n      queryClient.setQueryData(\r\n        ['agent-triggers', newTrigger.agent_id],\r\n        (old: TriggerConfiguration[] | undefined) => {\r\n          return old ? [...old, newTrigger] : [newTrigger];\r\n        }\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\nexport const useUpdateTrigger = () => {\r\n  const queryClient = useQueryClient();\r\n  \r\n  return useMutation({\r\n    mutationFn: updateTrigger,\r\n    onSuccess: (updatedTrigger) => {\r\n      queryClient.setQueryData(\r\n        ['agent-triggers', updatedTrigger.agent_id],\r\n        (old: TriggerConfiguration[] | undefined) => {\r\n          if (!old) return [updatedTrigger];\r\n          return old.map(trigger => \r\n            trigger.trigger_id === updatedTrigger.trigger_id ? updatedTrigger : trigger\r\n          );\r\n        }\r\n      );\r\n    },\r\n  });\r\n};\r\n\r\nexport const useDeleteTrigger = () => {\r\n  const queryClient = useQueryClient();\r\n  \r\n  return useMutation({\r\n    mutationFn: deleteTrigger,\r\n    onSuccess: (_, triggerId) => {\r\n      queryClient.invalidateQueries({ queryKey: ['agent-triggers'] });\r\n    },\r\n  });\r\n};\r\n\r\nexport const useToggleTrigger = () => {\r\n  const queryClient = useQueryClient();\r\n  \r\n  return useMutation({\r\n    mutationFn: async (data: { triggerId: string; isActive: boolean }) => {\r\n      return updateTrigger({\r\n        triggerId: data.triggerId,\r\n        is_active: data.isActive,\r\n      });\r\n    },\r\n    onSuccess: (updatedTrigger) => {\r\n      queryClient.setQueryData(\r\n        ['agent-triggers', updatedTrigger.agent_id],\r\n        (old: TriggerConfiguration[] | undefined) => {\r\n          if (!old) return [updatedTrigger];\r\n          return old.map(trigger => \r\n            trigger.trigger_id === updatedTrigger.trigger_id ? updatedTrigger : trigger\r\n          );\r\n        }\r\n      );\r\n    },\r\n  });\r\n}; "], "names": [], "mappings": ";;;;;;;AAIgB;AAJhB;AAAA;AAAA;AAEA;;;;AAEA,MAAM;AAEN,MAAM,qBAAqB,OAAO;IAC9B,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;IAC5D,IAAI,CAAC,SAAS;QACV,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,iBAAiB,EAAE,QAAQ,SAAS,CAAC,EAAE;QAC/E,SAAS;YAAE,gBAAgB;YAAoB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;QAAC;IACnG;IACA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO,SAAS,IAAI;AACtB;AAEA,MAAM,gBAAgB,OAAO;IAOzB,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;IAC5D,IAAI,CAAC,SAAS;QACV,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,iBAAiB,EAAE,KAAK,OAAO,CAAC,SAAS,CAAC,EAAE;QACpF,QAAQ;QACR,SAAS;YAAE,gBAAgB;YAAoB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;QAAC;QACjG,MAAM,KAAK,SAAS,CAAC;YACnB,aAAa,KAAK,WAAW;YAC7B,MAAM,KAAK,IAAI;YACf,aAAa,KAAK,WAAW;YAC7B,QAAQ,KAAK,MAAM;QACrB;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,MAAM,IAAI;IAClC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEA,MAAM,gBAAgB,OAAO;IAOzB,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;IAC5D,IAAI,CAAC,SAAS;QACV,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,UAAU,EAAE,KAAK,SAAS,EAAE,EAAE;QACtE,QAAQ;QACR,SAAS;YAAE,gBAAgB;YAAoB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;QAAC;QACjG,MAAM,KAAK,SAAS,CAAC;YACnB,MAAM,KAAK,IAAI;YACf,aAAa,KAAK,WAAW;YAC7B,QAAQ,KAAK,MAAM;YACnB,WAAW,KAAK,SAAS;QAC3B;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,MAAM,IAAI;IAClC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEA,MAAM,gBAAgB,OAAO;IAC3B,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;IAC5D,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,UAAU,EAAE,WAAW,EAAE;QAC/D,QAAQ;QACR,SAAS;YAAE,gBAAgB;YAAoB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;QAAC;IACnG;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,MAAM,IAAI,MAAM,MAAM,MAAM,IAAI;IAClC;AACF;AAEO,MAAM,mBAAmB,CAAC;;IAC/B,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAkB;SAAQ;QACrC,OAAO;yCAAE,IAAM,mBAAmB;;QAClC,SAAS,CAAC,CAAC;QACX,WAAW,IAAI,KAAK;IACtB;AACF;GAPa;;QACJ,8KAAA,CAAA,WAAQ;;;AAQV,MAAM,mBAAmB;;IAC9B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY;QACZ,SAAS;4CAAE,CAAC;gBACV,YAAY,YAAY,CACtB;oBAAC;oBAAkB,WAAW,QAAQ;iBAAC;oDACvC,CAAC;wBACC,OAAO,MAAM;+BAAI;4BAAK;yBAAW,GAAG;4BAAC;yBAAW;oBAClD;;YAEJ;;IACF;AACF;IAda;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAab,MAAM,mBAAmB;;IAC9B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY;QACZ,SAAS;4CAAE,CAAC;gBACV,YAAY,YAAY,CACtB;oBAAC;oBAAkB,eAAe,QAAQ;iBAAC;oDAC3C,CAAC;wBACC,IAAI,CAAC,KAAK,OAAO;4BAAC;yBAAe;wBACjC,OAAO,IAAI,GAAG;4DAAC,CAAA,UACb,QAAQ,UAAU,KAAK,eAAe,UAAU,GAAG,iBAAiB;;oBAExE;;YAEJ;;IACF;AACF;IAjBa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAgBb,MAAM,mBAAmB;;IAC9B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY;QACZ,SAAS;4CAAE,CAAC,GAAG;gBACb,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAiB;gBAAC;YAC/D;;IACF;AACF;IATa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAQb,MAAM,mBAAmB;;IAC9B,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;4CAAE,OAAO;gBACjB,OAAO,cAAc;oBACnB,WAAW,KAAK,SAAS;oBACzB,WAAW,KAAK,QAAQ;gBAC1B;YACF;;QACA,SAAS;4CAAE,CAAC;gBACV,YAAY,YAAY,CACtB;oBAAC;oBAAkB,eAAe,QAAQ;iBAAC;oDAC3C,CAAC;wBACC,IAAI,CAAC,KAAK,OAAO;4BAAC;yBAAe;wBACjC,OAAO,IAAI,GAAG;4DAAC,CAAA,UACb,QAAQ,UAAU,KAAK,eAAe,UAAU,GAAG,iBAAiB;;oBAExE;;YAEJ;;IACF;AACF;IAtBa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 4432, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/triggers/index.ts"], "sourcesContent": ["export { useTriggerProviders } from './use-trigger-providers';\r\nexport { \r\n  useAgentTriggers,\r\n  useCreateTrigger,\r\n  useUpdateTrigger,\r\n  useDeleteTrigger,\r\n  useToggleTrigger\r\n} from './use-agent-triggers'; "], "names": [], "mappings": ";AAAA;AACA", "debugId": null}}, {"offset": {"line": 4456, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/pipedream/keys.ts"], "sourcesContent": ["export const pipedreamKeys = {\r\n  all: ['pipedream'] as const,\r\n  connections: () => [...pipedreamKeys.all, 'connections'] as const,\r\n  connectionToken: (app?: string) => [...pipedreamKeys.all, 'connection-token', app || 'default'] as const,\r\n  health: () => [...pipedreamKeys.all, 'health'] as const,\r\n  config: () => [...pipedreamKeys.all, 'config'] as const,\r\n  workflows: () => [...pipedreamKeys.all, 'workflows'] as const,\r\n  workflowRuns: (workflowId: string) => [...pipedreamKeys.all, 'workflow-runs', workflowId] as const,\r\n  apps: (page: number, search?: string, category?: string) => [...pipedreamKeys.all, 'apps', page, search || '', category || ''] as const,\r\n  appsSearch: (query: string, page: number, category?: string) => [...pipedreamKeys.all, 'apps', 'search', query, page, category || ''] as const,\r\n  availableTools: () => [...pipedreamKeys.all, 'available-tools'] as const,\r\n  mcpDiscovery: (options?: { app_slug?: string; oauth_app_id?: string; custom?: boolean }) => \r\n    [...pipedreamKeys.all, 'mcp-discovery', options?.app_slug, options?.oauth_app_id, options?.custom] as const,\r\n  \r\n  profiles: {\r\n    all: () => [...pipedreamKeys.all, 'profiles'] as const,\r\n    list: (params?: { app_slug?: string; is_active?: boolean }) => \r\n      [...pipedreamKeys.profiles.all(), 'list', params?.app_slug || '', params?.is_active ?? ''] as const,\r\n    detail: (profileId: string) => [...pipedreamKeys.profiles.all(), 'detail', profileId] as const,\r\n    connections: (profileId: string) => [...pipedreamKeys.profiles.all(), 'connections', profileId] as const,\r\n  }\r\n}; "], "names": [], "mappings": ";;;AAAO,MAAM,gBAAgB;IAC3B,KAAK;QAAC;KAAY;IAClB,aAAa,IAAM;eAAI,cAAc,GAAG;YAAE;SAAc;IACxD,iBAAiB,CAAC,MAAiB;eAAI,cAAc,GAAG;YAAE;YAAoB,OAAO;SAAU;IAC/F,QAAQ,IAAM;eAAI,cAAc,GAAG;YAAE;SAAS;IAC9C,QAAQ,IAAM;eAAI,cAAc,GAAG;YAAE;SAAS;IAC9C,WAAW,IAAM;eAAI,cAAc,GAAG;YAAE;SAAY;IACpD,cAAc,CAAC,aAAuB;eAAI,cAAc,GAAG;YAAE;YAAiB;SAAW;IACzF,MAAM,CAAC,MAAc,QAAiB,WAAsB;eAAI,cAAc,GAAG;YAAE;YAAQ;YAAM,UAAU;YAAI,YAAY;SAAG;IAC9H,YAAY,CAAC,OAAe,MAAc,WAAsB;eAAI,cAAc,GAAG;YAAE;YAAQ;YAAU;YAAO;YAAM,YAAY;SAAG;IACrI,gBAAgB,IAAM;eAAI,cAAc,GAAG;YAAE;SAAkB;IAC/D,cAAc,CAAC,UACb;eAAI,cAAc,GAAG;YAAE;YAAiB,SAAS;YAAU,SAAS;YAAc,SAAS;SAAO;IAEpG,UAAU;QACR,KAAK,IAAM;mBAAI,cAAc,GAAG;gBAAE;aAAW;QAC7C,MAAM,CAAC,SACL;mBAAI,cAAc,QAAQ,CAAC,GAAG;gBAAI;gBAAQ,QAAQ,YAAY;gBAAI,QAAQ,aAAa;aAAG;QAC5F,QAAQ,CAAC,YAAsB;mBAAI,cAAc,QAAQ,CAAC,GAAG;gBAAI;gBAAU;aAAU;QACrF,aAAa,CAAC,YAAsB;mBAAI,cAAc,QAAQ,CAAC,GAAG;gBAAI;gBAAe;aAAU;IACjG;AACF", "debugId": null}}, {"offset": {"line": 4547, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/pipedream/mcp-discovery.ts"], "sourcesContent": ["import { useMutation, useQuery } from '@tanstack/react-query';\r\nimport { backendApi } from '@/lib/api-client';\r\nimport { pipedreamKeys } from './keys';\r\n\r\nexport interface MCPDiscoveryRequest {\r\n  app_slug?: string;\r\n  oauth_app_id?: string;\r\n}\r\n\r\nexport interface MCPConnectionRequest {\r\n  app_slug: string;\r\n  oauth_app_id?: string;\r\n}\r\n\r\nexport interface MCPServerInfo {\r\n  app_slug: string;\r\n  app_name: string;\r\n  external_user_id: string;\r\n  oauth_app_id?: string;\r\n  server_url: string;\r\n  project_id: string;\r\n  environment: string;\r\n  available_tools: Array<{\r\n    name: string;\r\n    description: string;\r\n    inputSchema: any;\r\n  }>;\r\n  status: 'connected' | 'error';\r\n  error?: string;\r\n}\r\n\r\nexport interface MCPDiscoveryResponse {\r\n  success: boolean;\r\n  mcp_servers: MCPServerInfo[];\r\n  count: number;\r\n  error?: string;\r\n}\r\n\r\nexport interface MCPConnectionResponse {\r\n  success: boolean;\r\n  mcp_config?: MCPServerInfo;\r\n  error?: string;\r\n}\r\n\r\nexport const usePipedreamMCPDiscovery = (\r\n  options: MCPDiscoveryRequest = {},\r\n  enabled: boolean = true\r\n) => {\r\n  return useQuery({\r\n    queryKey: pipedreamKeys.mcpDiscovery(options),\r\n    queryFn: async (): Promise<MCPDiscoveryResponse> => {\r\n      const response = await backendApi.post('/pipedream/mcp/discover', options);\r\n      return response.data;\r\n    },\r\n    enabled,\r\n    staleTime: 10 * 60 * 1000, // 10 minutes\r\n    gcTime: 15 * 60 * 1000, // 15 minutes cache time\r\n    refetchOnWindowFocus: false, // Prevent refetch on window focus\r\n    refetchOnMount: false, // Only refetch if data is stale\r\n    retry: 2, // Limit retries\r\n  });\r\n};\r\n\r\nexport const usePipedreamMCPConnection = () => {\r\n  return useMutation({\r\n    mutationFn: async (request: MCPConnectionRequest): Promise<MCPConnectionResponse> => {\r\n      const response = await backendApi.post('/pipedream/mcp/connect', request);\r\n      return response.data;\r\n    },\r\n  });\r\n};\r\n\r\nexport const usePipedreamMCPDiscoveryForApp = (\r\n  app_slug: string,\r\n  oauth_app_id?: string,\r\n  enabled: boolean = true\r\n) => {\r\n  return usePipedreamMCPDiscovery(\r\n    { app_slug, oauth_app_id },\r\n    enabled && !!app_slug\r\n  );\r\n};\r\n\r\nexport const usePipedreamMCPServers = (enabled: boolean = true) => {\r\n  return usePipedreamMCPDiscovery({}, enabled);\r\n};\r\n\r\nexport const usePipedreamCustomMCPDiscovery = () => {\r\n  return useQuery({\r\n    queryKey: pipedreamKeys.mcpDiscovery({ custom: true }),\r\n    queryFn: async () => {\r\n      const response = await backendApi.post('/pipedream/mcp/discover-custom');\r\n      return response.data;\r\n    },\r\n    enabled: true,\r\n    staleTime: 5 * 60 * 1000,\r\n    gcTime: 10 * 60 * 1000,\r\n  });\r\n}; "], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AACA;AACA;;;;;AA0CO,MAAM,2BAA2B,CACtC,UAA+B,CAAC,CAAC,EACjC,UAAmB,IAAI;;IAEvB,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,sJAAA,CAAA,gBAAa,CAAC,YAAY,CAAC;QACrC,OAAO;iDAAE;gBACP,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC,2BAA2B;gBAClE,OAAO,SAAS,IAAI;YACtB;;QACA;QACA,WAAW,KAAK,KAAK;QACrB,QAAQ,KAAK,KAAK;QAClB,sBAAsB;QACtB,gBAAgB;QAChB,OAAO;IACT;AACF;GAjBa;;QAIJ,8KAAA,CAAA,WAAQ;;;AAeV,MAAM,4BAA4B;;IACvC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;qDAAE,OAAO;gBACjB,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC,0BAA0B;gBACjE,OAAO,SAAS,IAAI;YACtB;;IACF;AACF;IAPa;;QACJ,iLAAA,CAAA,cAAW;;;AAQb,MAAM,iCAAiC,CAC5C,UACA,cACA,UAAmB,IAAI;;IAEvB,OAAO,yBACL;QAAE;QAAU;IAAa,GACzB,WAAW,CAAC,CAAC;AAEjB;IATa;;QAKJ;;;AAMF,MAAM,yBAAyB,CAAC,UAAmB,IAAI;;IAC5D,OAAO,yBAAyB,CAAC,GAAG;AACtC;IAFa;;QACJ;;;AAGF,MAAM,iCAAiC;;IAC5C,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,sJAAA,CAAA,gBAAa,CAAC,YAAY,CAAC;YAAE,QAAQ;QAAK;QACpD,OAAO;uDAAE;gBACP,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC;gBACvC,OAAO,SAAS,IAAI;YACtB;;QACA,SAAS;QACT,WAAW,IAAI,KAAK;QACpB,QAAQ,KAAK,KAAK;IACpB;AACF;IAXa;;QACJ,8KAAA,CAAA,WAAQ", "debugId": null}}, {"offset": {"line": 4653, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/pipedream/utils.ts"], "sourcesContent": ["import { createQ<PERSON><PERSON><PERSON><PERSON> } from '@/hooks/use-query';\r\nimport { backendApi } from '@/lib/api-client';\r\nimport { pipedreamKeys } from './keys';\r\nimport type {\r\n  PipedreamProfile,\r\n  CreateProfileRequest,\r\n  UpdateProfileRequest,\r\n  ProfileConnectionResponse,\r\n  ProfileConnectionsResponse\r\n} from '@/components/agents/pipedream/pipedream-types';\r\n\r\nexport interface CreateConnectionTokenRequest {\r\n  app?: string;\r\n}\r\n\r\nexport interface ConnectionTokenResponse {\r\n  success: boolean;\r\n  link?: string;\r\n  token?: string;\r\n  external_user_id: string;\r\n  app?: string;\r\n  expires_at?: string;\r\n  error?: string;\r\n}\r\n\r\nexport interface ConnectionResponse {\r\n  success: boolean;\r\n  connections: Connection[];\r\n  count: number;\r\n  error?: string;\r\n}\r\n\r\nexport interface Connection {\r\n  id: string;\r\n  app: string;\r\n  name: string;\r\n  status: 'connected' | 'disconnected' | 'error';\r\n  created_at: string;\r\n  updated_at: string;\r\n  [key: string]: any;\r\n}\r\n\r\nexport interface PipedreamHealthCheckResponse {\r\n  status: 'healthy' | 'unhealthy';\r\n  project_id: string;\r\n  environment: string;\r\n  has_access_token: boolean;\r\n  error?: string;\r\n}\r\n\r\nexport interface TriggerWorkflowRequest {\r\n  workflow_id: string;\r\n  payload: Record<string, any>;\r\n}\r\n\r\nexport interface TriggerWorkflowResponse {\r\n  success: boolean;\r\n  workflow_id: string;\r\n  run_id?: string;\r\n  status?: string;\r\n  error?: string;\r\n}\r\n\r\nexport interface WorkflowRun {\r\n  id: string;\r\n  workflow_id: string;\r\n  status: 'running' | 'completed' | 'failed' | 'cancelled';\r\n  started_at: string;\r\n  completed_at?: string;\r\n  error?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nexport interface PipedreamConfigStatus {\r\n  success: boolean;\r\n  project_id: string;\r\n  environment: string;\r\n  has_client_id: boolean;\r\n  has_client_secret: boolean;\r\n  base_url: string;\r\n}\r\n\r\nexport interface PipedreamApp {\r\n  id: string;\r\n  name: string;\r\n  name_slug: string;\r\n  auth_type: string;\r\n  description: string;\r\n  img_src: string;\r\n  custom_fields_json: string;\r\n  categories: string[];\r\n  featured_weight: number;\r\n  connect: {\r\n    allowed_domains: string[] | null;\r\n    base_proxy_target_url: string;\r\n    proxy_enabled: boolean;\r\n  };\r\n}\r\n\r\nexport interface PipedreamAppResponse {\r\n  success: boolean;\r\n  apps: PipedreamApp[];\r\n  page_info: {\r\n    total_count: number;\r\n    count: number;\r\n    has_more: boolean;\r\n    start_cursor?: string;\r\n    end_cursor?: string;\r\n  };\r\n  total_count: number;\r\n  error?: string;\r\n  search_query?: string;\r\n}\r\n\r\nexport interface PipedreamTool {\r\n  name: string;\r\n  description: string;\r\n  inputSchema: any;\r\n}\r\n\r\nexport interface PipedreamAppWithTools {\r\n  app_name: string;\r\n  app_slug: string;\r\n  tools: PipedreamTool[];\r\n  tool_count: number;\r\n}\r\n\r\nexport interface PipedreamToolsResponse {\r\n  success: boolean;\r\n  apps: PipedreamAppWithTools[];\r\n  total_apps: number;\r\n  total_tools: number;\r\n  user_id?: string;\r\n  timestamp?: number;\r\n  error?: string;\r\n}\r\n\r\nexport const usePipedreamConnections = createQueryHook(\r\n  pipedreamKeys.connections(),\r\n  async (): Promise<ConnectionResponse> => {\r\n    return await pipedreamApi.getConnections();\r\n  },\r\n  {\r\n    staleTime: 10 * 60 * 1000,\r\n    gcTime: 15 * 60 * 1000,\r\n    refetchOnWindowFocus: false,\r\n    refetchOnMount: false,\r\n    refetchInterval: false,\r\n  }\r\n);\r\n\r\nexport const pipedreamApi = {\r\n  async createConnectionToken(request: CreateConnectionTokenRequest = {}): Promise<ConnectionTokenResponse> {\r\n    const result = await backendApi.post<ConnectionTokenResponse>(\r\n      '/pipedream/connection-token',\r\n      request,\r\n      {\r\n        errorContext: { operation: 'create connection token', resource: 'Pipedream connection' },\r\n      }\r\n    );\r\n\r\n    if (!result.success) {\r\n      throw new Error(result.error?.message || 'Failed to create connection token');\r\n    }\r\n\r\n    return result.data!;\r\n  },\r\n\r\n  async getConnections(): Promise<ConnectionResponse> {\r\n    const result = await backendApi.get<ConnectionResponse>(\r\n      '/pipedream/connections',\r\n      {\r\n        errorContext: { operation: 'load connections', resource: 'Pipedream connections' },\r\n      }\r\n    );\r\n\r\n    if (!result.success) {\r\n      throw new Error(result.error?.message || 'Failed to get connections');\r\n    }\r\n\r\n    return result.data!;\r\n  },\r\n\r\n  async getApps(after?: string, search?: string): Promise<PipedreamAppResponse> {\r\n    const params = new URLSearchParams();\r\n    \r\n    if (after) {\r\n      params.append('after', after);\r\n    }\r\n    \r\n    if (search) {\r\n      params.append('q', search);\r\n    }\r\n    \r\n    const result = await backendApi.get<PipedreamAppResponse>(\r\n      `/pipedream/apps${params.toString() ? `?${params.toString()}` : ''}`,\r\n      {\r\n        errorContext: { operation: 'load apps', resource: 'Pipedream apps' },\r\n      }\r\n    );\r\n\r\n    if (!result.success) {\r\n      throw new Error(result.error?.message || 'Failed to get apps');\r\n    }\r\n    const data = result.data!;\r\n    if (!data.success && data.error) {\r\n      throw new Error(data.error);\r\n    }\r\n    return data;\r\n  },\r\n\r\n  async getAvailableTools(): Promise<PipedreamToolsResponse> {\r\n    const result = await backendApi.get<PipedreamToolsResponse>(\r\n      '/pipedream/mcp/available-tools',\r\n      {\r\n        errorContext: { operation: 'load available tools', resource: 'Pipedream tools' },\r\n      }\r\n    );\r\n    if (!result.success) {\r\n      throw new Error(result.error?.message || 'Failed to get available tools');\r\n    }\r\n\r\n    return result.data!;\r\n  },\r\n\r\n  async healthCheck(): Promise<PipedreamHealthCheckResponse> {\r\n    const result = await backendApi.get<PipedreamHealthCheckResponse>(\r\n      '/pipedream/health',\r\n      {\r\n        errorContext: { operation: 'health check', resource: 'Pipedream service' },\r\n      }\r\n    );\r\n\r\n    if (!result.success) {\r\n      throw new Error(result.error?.message || 'Health check failed');\r\n    }\r\n\r\n    return result.data!;\r\n  },\r\n\r\n  async discoverMCPServers(externalUserId: string, appSlug?: string): Promise<any> {\r\n    const request = {\r\n      external_user_id: externalUserId,\r\n      app_slug: appSlug\r\n    };\r\n    \r\n    const result = await backendApi.post<any>(\r\n      '/pipedream/mcp/discover-profile',\r\n      request,\r\n      {\r\n        errorContext: { operation: 'discover MCP servers', resource: 'Pipedream MCP' },\r\n      }\r\n    );\r\n\r\n    if (!result.success) {\r\n      throw new Error(result.error?.message || 'Failed to discover MCP servers');\r\n    }\r\n\r\n    return result.data?.mcp_servers || [];\r\n  },\r\n\r\n  // Credential Profile Methods\r\n  async createProfile(request: CreateProfileRequest): Promise<PipedreamProfile> {\r\n    const result = await backendApi.post<PipedreamProfile>(\r\n      '/pipedream/profiles',\r\n      request,\r\n      {\r\n        errorContext: { operation: 'create profile', resource: 'Pipedream credential profile' },\r\n      }\r\n    );\r\n\r\n    if (!result.success) {\r\n      throw new Error(result.error?.message || 'Failed to create profile');\r\n    }\r\n\r\n    return result.data!;\r\n  },\r\n\r\n  async getProfiles(params?: { app_slug?: string; is_active?: boolean }): Promise<PipedreamProfile[]> {\r\n    const queryParams = new URLSearchParams();\r\n    if (params?.app_slug) queryParams.append('app_slug', params.app_slug);\r\n    if (params?.is_active !== undefined) queryParams.append('is_active', params.is_active.toString());\r\n\r\n    const result = await backendApi.get<PipedreamProfile[]>(\r\n      `/pipedream/profiles${queryParams.toString() ? `?${queryParams.toString()}` : ''}`,\r\n      {\r\n        errorContext: { operation: 'get profiles', resource: 'Pipedream credential profiles' },\r\n      }\r\n    );\r\n\r\n    if (!result.success) {\r\n      throw new Error(result.error?.message || 'Failed to get profiles');\r\n    }\r\n\r\n    return result.data!;\r\n  },\r\n\r\n  async getProfile(profileId: string): Promise<PipedreamProfile> {\r\n    const result = await backendApi.get<PipedreamProfile>(\r\n      `/pipedream/profiles/${profileId}`,\r\n      {\r\n        errorContext: { operation: 'get profile', resource: 'Pipedream credential profile' },\r\n      }\r\n    );\r\n\r\n    if (!result.success) {\r\n      throw new Error(result.error?.message || 'Failed to get profile');\r\n    }\r\n\r\n    return result.data!;\r\n  },\r\n\r\n  async updateProfile(profileId: string, request: UpdateProfileRequest): Promise<PipedreamProfile> {\r\n    const result = await backendApi.put<PipedreamProfile>(\r\n      `/pipedream/profiles/${profileId}`,\r\n      request,\r\n      {\r\n        errorContext: { operation: 'update profile', resource: 'Pipedream credential profile' },\r\n      }\r\n    );\r\n\r\n    if (!result.success) {\r\n      throw new Error(result.error?.message || 'Failed to update profile');\r\n    }\r\n\r\n    return result.data!;\r\n  },\r\n\r\n  async deleteProfile(profileId: string): Promise<void> {\r\n    const result = await backendApi.delete(\r\n      `/pipedream/profiles/${profileId}`,\r\n      {\r\n        errorContext: { operation: 'delete profile', resource: 'Pipedream credential profile' },\r\n      }\r\n    );\r\n\r\n    if (!result.success) {\r\n      throw new Error(result.error?.message || 'Failed to delete profile');\r\n    }\r\n  },\r\n\r\n  async connectProfile(profileId: string, app?: string): Promise<ProfileConnectionResponse> {\r\n    const queryParams = app ? `?app=${encodeURIComponent(app)}` : '';\r\n    const result = await backendApi.post<ProfileConnectionResponse>(\r\n      `/pipedream/profiles/${profileId}/connect${queryParams}`,\r\n      {},\r\n      {\r\n        errorContext: { operation: 'connect profile', resource: 'Pipedream credential profile' },\r\n      }\r\n    );\r\n\r\n    if (!result.success) {\r\n      throw new Error(result.error?.message || 'Failed to connect profile');\r\n    }\r\n\r\n    return result.data!;\r\n  },\r\n\r\n  async getProfileConnections(profileId: string): Promise<ProfileConnectionsResponse> {\r\n    const result = await backendApi.get<ProfileConnectionsResponse>(\r\n      `/pipedream/profiles/${profileId}/connections`,\r\n      {\r\n        errorContext: { operation: 'get profile connections', resource: 'Pipedream profile connections' },\r\n      }\r\n    );\r\n\r\n    if (!result.success) {\r\n      throw new Error(result.error?.message || 'Failed to get profile connections');\r\n    }\r\n\r\n    return result.data!;\r\n  },\r\n}; "], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAuIO,MAAM,0BAA0B,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EACnD,sJAAA,CAAA,gBAAa,CAAC,WAAW,IACzB;IACE,OAAO,MAAM,aAAa,cAAc;AAC1C,GACA;IACE,WAAW,KAAK,KAAK;IACrB,QAAQ,KAAK,KAAK;IAClB,sBAAsB;IACtB,gBAAgB;IAChB,iBAAiB;AACnB;AAGK,MAAM,eAAe;IAC1B,MAAM,uBAAsB,UAAwC,CAAC,CAAC;QACpE,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAClC,+BACA,SACA;YACE,cAAc;gBAAE,WAAW;gBAA2B,UAAU;YAAuB;QACzF;QAGF,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,WAAW;QAC3C;QAEA,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM;QACJ,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CACjC,0BACA;YACE,cAAc;gBAAE,WAAW;gBAAoB,UAAU;YAAwB;QACnF;QAGF,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,WAAW;QAC3C;QAEA,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,SAAQ,KAAc,EAAE,MAAe;QAC3C,MAAM,SAAS,IAAI;QAEnB,IAAI,OAAO;YACT,OAAO,MAAM,CAAC,SAAS;QACzB;QAEA,IAAI,QAAQ;YACV,OAAO,MAAM,CAAC,KAAK;QACrB;QAEA,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CACjC,CAAC,eAAe,EAAE,OAAO,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI,GAAG,IAAI,EACpE;YACE,cAAc;gBAAE,WAAW;gBAAa,UAAU;YAAiB;QACrE;QAGF,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,WAAW;QAC3C;QACA,MAAM,OAAO,OAAO,IAAI;QACxB,IAAI,CAAC,KAAK,OAAO,IAAI,KAAK,KAAK,EAAE;YAC/B,MAAM,IAAI,MAAM,KAAK,KAAK;QAC5B;QACA,OAAO;IACT;IAEA,MAAM;QACJ,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CACjC,kCACA;YACE,cAAc;gBAAE,WAAW;gBAAwB,UAAU;YAAkB;QACjF;QAEF,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,WAAW;QAC3C;QAEA,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM;QACJ,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CACjC,qBACA;YACE,cAAc;gBAAE,WAAW;gBAAgB,UAAU;YAAoB;QAC3E;QAGF,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,WAAW;QAC3C;QAEA,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,oBAAmB,cAAsB,EAAE,OAAgB;QAC/D,MAAM,UAAU;YACd,kBAAkB;YAClB,UAAU;QACZ;QAEA,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAClC,mCACA,SACA;YACE,cAAc;gBAAE,WAAW;gBAAwB,UAAU;YAAgB;QAC/E;QAGF,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,WAAW;QAC3C;QAEA,OAAO,OAAO,IAAI,EAAE,eAAe,EAAE;IACvC;IAEA,6BAA6B;IAC7B,MAAM,eAAc,OAA6B;QAC/C,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAClC,uBACA,SACA;YACE,cAAc;gBAAE,WAAW;gBAAkB,UAAU;YAA+B;QACxF;QAGF,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,WAAW;QAC3C;QAEA,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,aAAY,MAAmD;QACnE,MAAM,cAAc,IAAI;QACxB,IAAI,QAAQ,UAAU,YAAY,MAAM,CAAC,YAAY,OAAO,QAAQ;QACpE,IAAI,QAAQ,cAAc,WAAW,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS,CAAC,QAAQ;QAE9F,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CACjC,CAAC,mBAAmB,EAAE,YAAY,QAAQ,KAAK,CAAC,CAAC,EAAE,YAAY,QAAQ,IAAI,GAAG,IAAI,EAClF;YACE,cAAc;gBAAE,WAAW;gBAAgB,UAAU;YAAgC;QACvF;QAGF,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,WAAW;QAC3C;QAEA,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,YAAW,SAAiB;QAChC,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CACjC,CAAC,oBAAoB,EAAE,WAAW,EAClC;YACE,cAAc;gBAAE,WAAW;gBAAe,UAAU;YAA+B;QACrF;QAGF,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,WAAW;QAC3C;QAEA,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,eAAc,SAAiB,EAAE,OAA6B;QAClE,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CACjC,CAAC,oBAAoB,EAAE,WAAW,EAClC,SACA;YACE,cAAc;gBAAE,WAAW;gBAAkB,UAAU;YAA+B;QACxF;QAGF,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,WAAW;QAC3C;QAEA,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,eAAc,SAAiB;QACnC,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,MAAM,CACpC,CAAC,oBAAoB,EAAE,WAAW,EAClC;YACE,cAAc;gBAAE,WAAW;gBAAkB,UAAU;YAA+B;QACxF;QAGF,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,WAAW;QAC3C;IACF;IAEA,MAAM,gBAAe,SAAiB,EAAE,GAAY;QAClD,MAAM,cAAc,MAAM,CAAC,KAAK,EAAE,mBAAmB,MAAM,GAAG;QAC9D,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAClC,CAAC,oBAAoB,EAAE,UAAU,QAAQ,EAAE,aAAa,EACxD,CAAC,GACD;YACE,cAAc;gBAAE,WAAW;gBAAmB,UAAU;YAA+B;QACzF;QAGF,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,WAAW;QAC3C;QAEA,OAAO,OAAO,IAAI;IACpB;IAEA,MAAM,uBAAsB,SAAiB;QAC3C,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CACjC,CAAC,oBAAoB,EAAE,UAAU,YAAY,CAAC,EAC9C;YACE,cAAc;gBAAE,WAAW;gBAA2B,UAAU;YAAgC;QAClG;QAGF,IAAI,CAAC,OAAO,OAAO,EAAE;YACnB,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,WAAW;QAC3C;QAEA,OAAO,OAAO,IAAI;IACpB;AACF", "debugId": null}}, {"offset": {"line": 4858, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/pipedream/use-pipedream.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';\r\nimport { createQueryHook } from '@/hooks/use-query';\r\nimport { backendApi } from '@/lib/api-client';\r\nimport { \r\n  pipedreamApi, \r\n  type CreateConnectionTokenRequest,\r\n  type ConnectionTokenResponse,\r\n  type ConnectionResponse,\r\n  type PipedreamAppResponse,\r\n  type PipedreamToolsResponse,\r\n} from './utils';\r\nimport { pipedreamKeys } from './keys';\r\n\r\nexport const useCreateConnectionToken = () => {\r\n  const queryClient = useQueryClient();\r\n  return useMutation({\r\n    mutationFn: async (request: CreateConnectionTokenRequest): Promise<ConnectionTokenResponse> => {\r\n      return await pipedreamApi.createConnectionToken(request);\r\n    },\r\n    onSuccess: (data, variables) => {\r\n      queryClient.invalidateQueries({ queryKey: pipedreamKeys.connections() });\r\n      queryClient.setQueryData(pipedreamKeys.connectionToken(variables.app), data);\r\n    },\r\n    onError: (error) => {\r\n      console.error('Failed to create connection token:', error);\r\n    },\r\n  });\r\n};\r\n\r\nexport const useRefreshConnections = () => {\r\n  const queryClient = useQueryClient();\r\n  return useMutation({\r\n    mutationFn: async (): Promise<ConnectionResponse> => {\r\n      return await pipedreamApi.getConnections();\r\n    },\r\n    onSuccess: (data) => {\r\n      queryClient.setQueryData(pipedreamKeys.connections(), data);\r\n    },\r\n    onError: (error) => {\r\n      console.error('Failed to refresh connections:', error);\r\n    },\r\n  });\r\n};\r\n\r\nexport const useInvalidatePipedreamQueries = () => {\r\n  const queryClient = useQueryClient();\r\n  return () => {\r\n    queryClient.invalidateQueries({ queryKey: pipedreamKeys.all });\r\n  };\r\n}; \r\n\r\nexport const usePipedreamApps = (after?: string, search?: string) => {\r\n  return useQuery({\r\n    queryKey: ['pipedream', 'apps', after, search],\r\n    queryFn: async (): Promise<PipedreamAppResponse> => {\r\n      return await pipedreamApi.getApps(after, search);\r\n    },\r\n    staleTime: 5 * 60 * 1000, \r\n    retry: 2,\r\n  });\r\n};\r\n\r\nexport const usePipedreamAvailableTools = createQueryHook(\r\n  pipedreamKeys.availableTools(),\r\n  async (forceRefresh: boolean = false): Promise<PipedreamToolsResponse> => {\r\n    const params = new URLSearchParams();\r\n    if (forceRefresh) {\r\n      params.append('force_refresh', 'true');\r\n    }\r\n    \r\n    const url = `/pipedream/mcp/available-tools${params.toString() ? `?${params.toString()}` : ''}`;\r\n    const result = await backendApi.get<PipedreamToolsResponse>(url, {\r\n      errorContext: { operation: 'load available tools', resource: 'Pipedream tools' },\r\n    });\r\n    if (result.success && result.data) {\r\n      if (result.data.success) {\r\n        return result.data;\r\n      } else {\r\n        throw new Error(result.data.error || 'Failed to get available tools');\r\n      }\r\n    } else {\r\n      throw new Error(result.error?.message || 'Failed to get available tools');\r\n    }\r\n  },\r\n  {\r\n    staleTime: 5 * 60 * 1000,\r\n    gcTime: 10 * 60 * 1000,\r\n    refetchOnWindowFocus: false,\r\n    refetchOnMount: true,\r\n    retry: (failureCount, error) => {\r\n      if (failureCount < 2) {\r\n        const errorMessage = error?.message?.toLowerCase() || '';\r\n        return !errorMessage.includes('unauthorized') && !errorMessage.includes('forbidden');\r\n      }\r\n      return false;\r\n    },\r\n    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),\r\n  }\r\n); \r\n"], "names": [], "mappings": ";;;;;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AAQA;;AAbA;;;;;;AAeO,MAAM,2BAA2B;;IACtC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;oDAAE,OAAO;gBACjB,OAAO,MAAM,uJAAA,CAAA,eAAY,CAAC,qBAAqB,CAAC;YAClD;;QACA,SAAS;oDAAE,CAAC,MAAM;gBAChB,YAAY,iBAAiB,CAAC;oBAAE,UAAU,sJAAA,CAAA,gBAAa,CAAC,WAAW;gBAAG;gBACtE,YAAY,YAAY,CAAC,sJAAA,CAAA,gBAAa,CAAC,eAAe,CAAC,UAAU,GAAG,GAAG;YACzE;;QACA,OAAO;oDAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,sCAAsC;YACtD;;IACF;AACF;GAda;;QACS,yLAAA,CAAA,iBAAc;QAC3B,iLAAA,CAAA,cAAW;;;AAcb,MAAM,wBAAwB;;IACnC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;iDAAE;gBACV,OAAO,MAAM,uJAAA,CAAA,eAAY,CAAC,cAAc;YAC1C;;QACA,SAAS;iDAAE,CAAC;gBACV,YAAY,YAAY,CAAC,sJAAA,CAAA,gBAAa,CAAC,WAAW,IAAI;YACxD;;QACA,OAAO;iDAAE,CAAC;gBACR,QAAQ,KAAK,CAAC,kCAAkC;YAClD;;IACF;AACF;IAba;;QACS,yLAAA,CAAA,iBAAc;QAC3B,iLAAA,CAAA,cAAW;;;AAab,MAAM,gCAAgC;;IAC3C,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IACjC,OAAO;QACL,YAAY,iBAAiB,CAAC;YAAE,UAAU,sJAAA,CAAA,gBAAa,CAAC,GAAG;QAAC;IAC9D;AACF;IALa;;QACS,yLAAA,CAAA,iBAAc;;;AAM7B,MAAM,mBAAmB,CAAC,OAAgB;;IAC/C,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAa;YAAQ;YAAO;SAAO;QAC9C,OAAO;yCAAE;gBACP,OAAO,MAAM,uJAAA,CAAA,eAAY,CAAC,OAAO,CAAC,OAAO;YAC3C;;QACA,WAAW,IAAI,KAAK;QACpB,OAAO;IACT;AACF;IATa;;QACJ,8KAAA,CAAA,WAAQ;;;AAUV,MAAM,6BAA6B,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EACtD,sJAAA,CAAA,gBAAa,CAAC,cAAc,IAC5B,OAAO,eAAwB,KAAK;IAClC,MAAM,SAAS,IAAI;IACnB,IAAI,cAAc;QAChB,OAAO,MAAM,CAAC,iBAAiB;IACjC;IAEA,MAAM,MAAM,CAAC,8BAA8B,EAAE,OAAO,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI,GAAG,IAAI;IAC/F,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAyB,KAAK;QAC/D,cAAc;YAAE,WAAW;YAAwB,UAAU;QAAkB;IACjF;IACA,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;QACjC,IAAI,OAAO,IAAI,CAAC,OAAO,EAAE;YACvB,OAAO,OAAO,IAAI;QACpB,OAAO;YACL,MAAM,IAAI,MAAM,OAAO,IAAI,CAAC,KAAK,IAAI;QACvC;IACF,OAAO;QACL,MAAM,IAAI,MAAM,OAAO,KAAK,EAAE,WAAW;IAC3C;AACF,GACA;IACE,WAAW,IAAI,KAAK;IACpB,QAAQ,KAAK,KAAK;IAClB,sBAAsB;IACtB,gBAAgB;IAChB,OAAO,CAAC,cAAc;QACpB,IAAI,eAAe,GAAG;YACpB,MAAM,eAAe,OAAO,SAAS,iBAAiB;YACtD,OAAO,CAAC,aAAa,QAAQ,CAAC,mBAAmB,CAAC,aAAa,QAAQ,CAAC;QAC1E;QACA,OAAO;IACT;IACA,YAAY,CAAC,eAAiB,KAAK,GAAG,CAAC,OAAO,KAAK,cAAc;AACnE", "debugId": null}}, {"offset": {"line": 5017, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/pipedream/index.ts"], "sourcesContent": ["export * from './mcp-discovery';\r\nexport * from './utils';\r\nexport * from './keys';\r\nexport * from './use-pipedream';\r\nexport { pipedreamKeys } from './keys'; \r\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 5048, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/index.ts"], "sourcesContent": ["export * from './sidebar/use-sidebar';\r\nexport * from './sidebar/use-project-mutations';\r\nexport * from './sidebar/use-public-projects';\r\n\r\nexport * from './threads/use-threads';\r\nexport * from './threads/use-thread-queries';\r\nexport * from './threads/use-project';\r\nexport * from './threads/use-messages';\r\nexport * from './threads/use-agent-run';\r\nexport * from './threads/use-billing-status';\r\nexport * from './threads/use-thread-mutations';\r\n\r\nexport * from './files/use-file-queries';\r\nexport * from './files/use-file-mutations';\r\nexport * from './files/use-sandbox-mutations';\r\n\r\nexport * from './subscriptions/use-subscriptions';\r\nexport * from './subscriptions/use-billing';\r\n\r\nexport * from './dashboard/use-initiate-agent';\r\n\r\nexport * from './usage/use-health';\r\n\r\nexport * from './knowledge-base/use-knowledge-base-queries';\r\n\r\nexport * from './triggers';\r\n\r\nexport * from './pipedream'; "], "names": [], "mappings": ";AAAA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AAEA;AAEA;AAEA;AAEA;AAEA", "debugId": null}}, {"offset": {"line": 5126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/files/use-file-content.ts"], "sourcesContent": ["import { useFileContentQuery } from './use-file-queries';\r\n\r\n/**\r\n * Hook for fetching file content with React Query\r\n * Replaces the existing useFileContent hook\r\n * Now auto-detects content type for proper caching consistency\r\n */\r\nexport function useFileContent(\r\n  sandboxId?: string,\r\n  filePath?: string,\r\n  options: {\r\n    enabled?: boolean;\r\n    staleTime?: number;\r\n  } = {}\r\n) {\r\n  return useFileContentQuery(sandboxId, filePath, {\r\n    // Auto-detect content type for consistency across all hooks\r\n    enabled: options.enabled,\r\n    staleTime: options.staleTime,\r\n  });\r\n} "], "names": [], "mappings": ";;;AAAA;AAAA;;;AAOO,SAAS,eACd,SAAkB,EAClB,QAAiB,EACjB,UAGI,CAAC,CAAC;;IAEN,OAAO,CAAA,GAAA,oLAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW,UAAU;QAC9C,4DAA4D;QAC5D,SAAS,QAAQ,OAAO;QACxB,WAAW,QAAQ,SAAS;IAC9B;AACF;GAbgB;;QAQP,oLAAA,CAAA,sBAAmB", "debugId": null}}, {"offset": {"line": 5155, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/files/use-image-content.ts"], "sourcesContent": ["import React from 'react';\r\nimport { useFileContentQuery } from './use-file-queries';\r\n\r\n/**\r\n * Hook for fetching image content and creating blob URLs\r\n * Simplified to avoid reference counting issues in React StrictMode\r\n */\r\nexport function useImageContent(\r\n  sandboxId?: string,\r\n  filePath?: string,\r\n  options: {\r\n    enabled?: boolean;\r\n    staleTime?: number;\r\n  } = {}\r\n) {\r\n  const [blobUrl, setBlobUrl] = React.useState<string | null>(null);\r\n\r\n  // Get the blob data from React Query cache\r\n  const {\r\n    data: blobData,\r\n    isLoading,\r\n    error,\r\n  } = useFileContentQuery(sandboxId, filePath, {\r\n    contentType: 'blob',\r\n    enabled: options.enabled,\r\n    staleTime: options.staleTime || 5 * 60 * 1000, // 5 minutes default\r\n  });\r\n\r\n  // Create blob URL when we have blob data and clean up properly\r\n  React.useEffect(() => {\r\n    if (blobData instanceof Blob) {\r\n      console.log(`[IMAGE CONTENT] Creating blob URL for ${filePath}`, {\r\n        size: blobData.size,\r\n        type: blobData.type\r\n      });\r\n      \r\n      const url = URL.createObjectURL(blobData);\r\n      setBlobUrl(url);\r\n      \r\n      // Cleanup function to revoke the blob URL\r\n      return () => {\r\n        console.log(`[IMAGE CONTENT] Cleaning up blob URL for ${filePath}: ${url}`);\r\n        URL.revokeObjectURL(url);\r\n        setBlobUrl(null);\r\n      };\r\n    } else {\r\n      setBlobUrl(null);\r\n      return;\r\n    }\r\n  }, [blobData, filePath]);\r\n\r\n  return {\r\n    data: blobUrl,\r\n    isLoading,\r\n    error,\r\n  };\r\n} "], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;;AAMO,SAAS,gBACd,SAAkB,EAClB,QAAiB,EACjB,UAGI,CAAC,CAAC;;IAEN,MAAM,CAAC,SAAS,WAAW,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAgB;IAE5D,2CAA2C;IAC3C,MAAM,EACJ,MAAM,QAAQ,EACd,SAAS,EACT,KAAK,EACN,GAAG,CAAA,GAAA,oLAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW,UAAU;QAC3C,aAAa;QACb,SAAS,QAAQ,OAAO;QACxB,WAAW,QAAQ,SAAS,IAAI,IAAI,KAAK;IAC3C;IAEA,+DAA+D;IAC/D,6JAAA,CAAA,UAAK,CAAC,SAAS;qCAAC;YACd,IAAI,oBAAoB,MAAM;gBAC5B,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,UAAU,EAAE;oBAC/D,MAAM,SAAS,IAAI;oBACnB,MAAM,SAAS,IAAI;gBACrB;gBAEA,MAAM,MAAM,IAAI,eAAe,CAAC;gBAChC,WAAW;gBAEX,0CAA0C;gBAC1C;iDAAO;wBACL,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,SAAS,EAAE,EAAE,KAAK;wBAC1E,IAAI,eAAe,CAAC;wBACpB,WAAW;oBACb;;YACF,OAAO;gBACL,WAAW;gBACX;YACF;QACF;oCAAG;QAAC;QAAU;KAAS;IAEvB,OAAO;QACL,MAAM;QACN;QACA;IACF;AACF;GAjDgB;;QAeV,oLAAA,CAAA,sBAAmB", "debugId": null}}, {"offset": {"line": 5220, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/files/index.ts"], "sourcesContent": ["// Core React Query file hooks\r\nexport {\r\n  useFileContentQuery,\r\n  useDirectoryQuery,\r\n  useFilePreloader,\r\n  useCachedFile,\r\n  fileQueryKeys,\r\n  FileCache,\r\n} from './use-file-queries';\r\n\r\n// Specialized content hooks\r\nexport { useFileContent } from './use-file-content';\r\nexport { useImageContent } from './use-image-content';\r\n\r\n// File mutation hooks\r\nexport {\r\n  useFileUpload,\r\n  useFileDelete,\r\n  useFileCreate,\r\n} from './use-file-mutations';\r\n\r\n// Utility functions for compatibility\r\nexport {\r\n  getCachedFile,\r\n  fetchFileContent,\r\n} from './use-file-queries'; "], "names": [], "mappings": "AAAA,8BAA8B;;AAC9B;AASA,4BAA4B;AAC5B;AACA;AAEA,sBAAsB;AACtB", "debugId": null}}, {"offset": {"line": 5254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/agents/workflow-keys.ts"], "sourcesContent": ["export const workflowKeys = {\r\n  all: ['agent-workflows'] as const,\r\n  agent: (agentId: string) => [...workflowKeys.all, agentId] as const,\r\n  workflow: (agentId: string, workflowId: string) => [...workflowKeys.agent(agentId), workflowId] as const,\r\n  executions: (agentId: string, workflowId: string) => [...workflowKeys.workflow(agentId, workflowId), 'executions'] as const,\r\n}; "], "names": [], "mappings": ";;;AAAO,MAAM,eAAe;IAC1B,KAAK;QAAC;KAAkB;IACxB,OAAO,CAAC,UAAoB;eAAI,aAAa,GAAG;YAAE;SAAQ;IAC1D,UAAU,CAAC,SAAiB,aAAuB;eAAI,aAAa,KAAK,CAAC;YAAU;SAAW;IAC/F,YAAY,CAAC,SAAiB,aAAuB;eAAI,aAAa,QAAQ,CAAC,SAAS;YAAa;SAAa;AACpH", "debugId": null}}, {"offset": {"line": 5283, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/agents/workflow-utils.ts"], "sourcesContent": ["import { createClient } from \"@/lib/supabase/client\";\r\nimport { isFlagEnabled } from \"@/lib/feature-flags\";\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || '';\r\n\r\nexport interface WorkflowStep {\r\n  id: string;\r\n  name: string;\r\n  description?: string;\r\n  type: string;\r\n  config: Record<string, any>;\r\n  conditions?: Record<string, any>;\r\n  order: number;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface AgentWorkflow {\r\n  id: string;\r\n  agent_id: string;\r\n  name: string;\r\n  description?: string;\r\n  status: 'draft' | 'active' | 'paused' | 'archived';\r\n  trigger_phrase?: string;\r\n  is_default: boolean;\r\n  steps: WorkflowStep[];\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\nexport interface WorkflowExecution {\r\n  id: string;\r\n  workflow_id: string;\r\n  agent_id: string;\r\n  thread_id?: string;\r\n  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';\r\n  started_at: string;\r\n  completed_at?: string;\r\n  duration_seconds?: number;\r\n  triggered_by: string;\r\n  input_data?: Record<string, any>;\r\n  output_data?: Record<string, any>;\r\n  error_message?: string;\r\n  created_at: string;\r\n}\r\n\r\nexport interface CreateWorkflowRequest {\r\n  name: string;\r\n  description?: string;\r\n  trigger_phrase?: string;\r\n  is_default?: boolean;\r\n  steps: Array<{\r\n    name: string;\r\n    description?: string;\r\n    type?: string;\r\n    config?: Record<string, any>;\r\n    conditions?: Record<string, any>;\r\n    order: number;\r\n  }>;\r\n}\r\n\r\nexport interface UpdateWorkflowRequest {\r\n  name?: string;\r\n  description?: string;\r\n  trigger_phrase?: string;\r\n  is_default?: boolean;\r\n  status?: 'draft' | 'active' | 'paused' | 'archived';\r\n  steps?: Array<{\r\n    name: string;\r\n    description?: string;\r\n    type?: string; // Optional, defaults to 'instruction'\r\n    config?: Record<string, any>; // Contains optional tool_name and settings\r\n    conditions?: Record<string, any>;\r\n    order: number;\r\n  }>;\r\n}\r\n\r\nexport interface ExecuteWorkflowRequest {\r\n  input_data?: Record<string, any>;\r\n  thread_id?: string;\r\n}\r\n\r\nexport interface LLMWorkflowStep {\r\n  step: string;\r\n  description?: string;\r\n  tool?: string;\r\n  condition?: string;\r\n  then?: LLMWorkflowStep[];\r\n}\r\n\r\nexport interface LLMWorkflowFormat {\r\n  workflow: string;\r\n  description?: string;\r\n  steps: LLMWorkflowStep[];\r\n}\r\n\r\nexport const convertWorkflowToLLMFormat = (workflow: AgentWorkflow): LLMWorkflowFormat => {\r\n  const convertSteps = (steps: any[]): LLMWorkflowStep[] => {\r\n    return steps.map(step => {\r\n      const llmStep: LLMWorkflowStep = {\r\n        step: step.name,\r\n      };\r\n\r\n      if (step.description) {\r\n        llmStep.description = step.description;\r\n      }\r\n\r\n      if (step.config?.tool_name) {\r\n        llmStep.tool = step.config.tool_name;\r\n      }\r\n\r\n      if (step.type === 'condition' && step.conditions) {\r\n        if (step.conditions.type === 'if' && step.conditions.expression) {\r\n          llmStep.condition = step.conditions.expression;\r\n        } else if (step.conditions.type === 'else') {\r\n          llmStep.condition = 'else';\r\n        }\r\n      }\r\n\r\n      if (step.steps && step.steps.length > 0) {\r\n        llmStep.then = convertSteps(step.steps);\r\n      }\r\n\r\n      return llmStep;\r\n    });\r\n  };\r\n\r\n  const llmFormat: LLMWorkflowFormat = {\r\n    workflow: workflow.name,\r\n    steps: convertSteps(workflow.steps)\r\n  };\r\n\r\n  if (workflow.description) {\r\n    llmFormat.description = workflow.description;\r\n  }\r\n\r\n  return llmFormat;\r\n};\r\n\r\nexport const generateLLMWorkflowPrompt = (workflow: AgentWorkflow): string => {\r\n  const llmFormat = convertWorkflowToLLMFormat(workflow);\r\n  return JSON.stringify(llmFormat, null, 2);\r\n};\r\n\r\nexport const getAgentWorkflows = async (agentId: string): Promise<AgentWorkflow[]> => {\r\n  try {\r\n    const agentPlaygroundEnabled = await isFlagEnabled('custom_agents');\r\n    if (!agentPlaygroundEnabled) {\r\n      throw new Error('Custom agents is not enabled');\r\n    }\r\n    const supabase = createClient();\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n\r\n    if (!session) {\r\n      throw new Error('You must be logged in to get workflows');\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/agents/${agentId}/workflows`, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${session.access_token}`,\r\n      },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({ detail: 'Unknown error' }));\r\n      throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);\r\n    }\r\n\r\n    const workflows = await response.json();\r\n    console.log('[API] Fetched workflows for agent:', agentId, workflows.length);\r\n    return workflows;\r\n  } catch (err) {\r\n    console.error('Error fetching workflows:', err);\r\n    throw err;\r\n  }\r\n};\r\n\r\nexport const createAgentWorkflow = async (agentId: string, workflow: CreateWorkflowRequest): Promise<AgentWorkflow> => {\r\n  try {\r\n    const agentPlaygroundEnabled = await isFlagEnabled('custom_agents');\r\n    if (!agentPlaygroundEnabled) {\r\n      throw new Error('Custom agents is not enabled');\r\n    }\r\n    const supabase = createClient();\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n\r\n    if (!session) {\r\n      throw new Error('You must be logged in to create a workflow');\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/agents/${agentId}/workflows`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${session.access_token}`,\r\n      },\r\n      body: JSON.stringify(workflow),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({ detail: 'Unknown error' }));\r\n      throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);\r\n    }\r\n\r\n    const result = await response.json();\r\n    console.log('[API] Created workflow:', result.id);\r\n    return result;\r\n  } catch (err) {\r\n    console.error('Error creating workflow:', err);\r\n    throw err;\r\n  }\r\n};\r\n\r\nexport const updateAgentWorkflow = async (\r\n  agentId: string, \r\n  workflowId: string, \r\n  workflow: UpdateWorkflowRequest\r\n): Promise<AgentWorkflow> => {\r\n  try {\r\n    console.log('[API] Updating workflow:', workflow);\r\n    const agentPlaygroundEnabled = await isFlagEnabled('custom_agents');\r\n    if (!agentPlaygroundEnabled) {\r\n      throw new Error('Custom agents is not enabled');\r\n    }\r\n    const supabase = createClient();\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n\r\n    if (!session) {\r\n      throw new Error('You must be logged in to update a workflow');\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/agents/${agentId}/workflows/${workflowId}`, {\r\n      method: 'PUT',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${session.access_token}`,\r\n      },\r\n      body: JSON.stringify(workflow),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({ detail: 'Unknown error' }));\r\n      throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);\r\n    }\r\n\r\n    const result = await response.json();\r\n    console.log('[API] Updated workflow:', result.id);\r\n    return result;\r\n  } catch (err) {\r\n    console.error('Error updating workflow:', err);\r\n    throw err;\r\n  }\r\n};\r\n\r\nexport const deleteAgentWorkflow = async (agentId: string, workflowId: string): Promise<void> => {\r\n  try {\r\n    const agentPlaygroundEnabled = await isFlagEnabled('custom_agents');\r\n    if (!agentPlaygroundEnabled) {\r\n      throw new Error('Custom agents is not enabled');\r\n    }\r\n    const supabase = createClient();\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n\r\n    if (!session) {\r\n      throw new Error('You must be logged in to delete a workflow');\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/agents/${agentId}/workflows/${workflowId}`, {\r\n      method: 'DELETE',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${session.access_token}`,\r\n      },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({ detail: 'Unknown error' }));\r\n      throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);\r\n    }\r\n\r\n    console.log('[API] Deleted workflow:', workflowId);\r\n  } catch (err) {\r\n    console.error('Error deleting workflow:', err);\r\n    throw err;\r\n  }\r\n};\r\n\r\nexport const executeWorkflow = async (\r\n  agentId: string, \r\n  workflowId: string, \r\n  execution: ExecuteWorkflowRequest\r\n): Promise<{ \r\n  execution_id: string; \r\n  thread_id?: string; \r\n  agent_run_id?: string; \r\n  status: string; \r\n  message?: string; \r\n}> => {\r\n  try {\r\n    const agentPlaygroundEnabled = await isFlagEnabled('custom_agents');\r\n    if (!agentPlaygroundEnabled) {\r\n      throw new Error('Custom agents is not enabled');\r\n    }\r\n    const supabase = createClient();\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n\r\n    if (!session) {\r\n      throw new Error('You must be logged in to execute a workflow');\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/agents/${agentId}/workflows/${workflowId}/execute`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${session.access_token}`,\r\n      },\r\n      body: JSON.stringify(execution),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({ detail: 'Unknown error' }));\r\n      throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);\r\n    }\r\n\r\n    const result = await response.json();\r\n    console.log('[API] Executed workflow:', workflowId, 'execution:', result.execution_id);\r\n    return result;\r\n  } catch (err) {\r\n    console.error('Error executing workflow:', err);\r\n    throw err;\r\n  }\r\n};\r\n\r\nexport const getWorkflowExecutions = async (\r\n  agentId: string, \r\n  workflowId: string, \r\n  limit: number = 20\r\n): Promise<WorkflowExecution[]> => {\r\n  try {\r\n    const agentPlaygroundEnabled = await isFlagEnabled('custom_agents');\r\n    if (!agentPlaygroundEnabled) {\r\n      throw new Error('Custom agents is not enabled');\r\n    }\r\n    const supabase = createClient();\r\n    const { data: { session } } = await supabase.auth.getSession();\r\n\r\n    if (!session) {\r\n      throw new Error('You must be logged in to get workflow executions');\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/agents/${agentId}/workflows/${workflowId}/executions?limit=${limit}`, {\r\n      method: 'GET',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        'Authorization': `Bearer ${session.access_token}`,\r\n      },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorData = await response.json().catch(() => ({ detail: 'Unknown error' }));\r\n      throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);\r\n    }\r\n\r\n    const executions = await response.json();\r\n    console.log('[API] Fetched executions for workflow:', workflowId, executions.length);\r\n    return executions;\r\n  } catch (err) {\r\n    console.error('Error fetching workflow executions:', err);\r\n    throw err;\r\n  }\r\n}; "], "names": [], "mappings": ";;;;;;;;;;AAGgB;AAHhB;AACA;;;AAEA,MAAM,UAAU,iEAAuC;AA6FhD,MAAM,6BAA6B,CAAC;IACzC,MAAM,eAAe,CAAC;QACpB,OAAO,MAAM,GAAG,CAAC,CAAA;YACf,MAAM,UAA2B;gBAC/B,MAAM,KAAK,IAAI;YACjB;YAEA,IAAI,KAAK,WAAW,EAAE;gBACpB,QAAQ,WAAW,GAAG,KAAK,WAAW;YACxC;YAEA,IAAI,KAAK,MAAM,EAAE,WAAW;gBAC1B,QAAQ,IAAI,GAAG,KAAK,MAAM,CAAC,SAAS;YACtC;YAEA,IAAI,KAAK,IAAI,KAAK,eAAe,KAAK,UAAU,EAAE;gBAChD,IAAI,KAAK,UAAU,CAAC,IAAI,KAAK,QAAQ,KAAK,UAAU,CAAC,UAAU,EAAE;oBAC/D,QAAQ,SAAS,GAAG,KAAK,UAAU,CAAC,UAAU;gBAChD,OAAO,IAAI,KAAK,UAAU,CAAC,IAAI,KAAK,QAAQ;oBAC1C,QAAQ,SAAS,GAAG;gBACtB;YACF;YAEA,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG;gBACvC,QAAQ,IAAI,GAAG,aAAa,KAAK,KAAK;YACxC;YAEA,OAAO;QACT;IACF;IAEA,MAAM,YAA+B;QACnC,UAAU,SAAS,IAAI;QACvB,OAAO,aAAa,SAAS,KAAK;IACpC;IAEA,IAAI,SAAS,WAAW,EAAE;QACxB,UAAU,WAAW,GAAG,SAAS,WAAW;IAC9C;IAEA,OAAO;AACT;AAEO,MAAM,4BAA4B,CAAC;IACxC,MAAM,YAAY,2BAA2B;IAC7C,OAAO,KAAK,SAAS,CAAC,WAAW,MAAM;AACzC;AAEO,MAAM,oBAAoB,OAAO;IACtC,IAAI;QACF,MAAM,yBAAyB,MAAM,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE;QACnD,IAAI,CAAC,wBAAwB;YAC3B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAE5D,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,QAAQ,EAAE,QAAQ,UAAU,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACnD;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,QAAQ;gBAAgB,CAAC;YAChF,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACvF;QAEA,MAAM,YAAY,MAAM,SAAS,IAAI;QACrC,QAAQ,GAAG,CAAC,sCAAsC,SAAS,UAAU,MAAM;QAC3E,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAEO,MAAM,sBAAsB,OAAO,SAAiB;IACzD,IAAI;QACF,MAAM,yBAAyB,MAAM,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE;QACnD,IAAI,CAAC,wBAAwB;YAC3B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAE5D,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,QAAQ,EAAE,QAAQ,UAAU,CAAC,EAAE;YACrE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACnD;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,QAAQ;gBAAgB,CAAC;YAChF,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACvF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,2BAA2B,OAAO,EAAE;QAChD,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,MAAM,sBAAsB,OACjC,SACA,YACA;IAEA,IAAI;QACF,QAAQ,GAAG,CAAC,4BAA4B;QACxC,MAAM,yBAAyB,MAAM,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE;QACnD,IAAI,CAAC,wBAAwB;YAC3B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAE5D,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,QAAQ,EAAE,QAAQ,WAAW,EAAE,YAAY,EAAE;YACnF,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACnD;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,QAAQ;gBAAgB,CAAC;YAChF,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACvF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,2BAA2B,OAAO,EAAE;QAChD,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,MAAM,sBAAsB,OAAO,SAAiB;IACzD,IAAI;QACF,MAAM,yBAAyB,MAAM,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE;QACnD,IAAI,CAAC,wBAAwB;YAC3B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAE5D,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,QAAQ,EAAE,QAAQ,WAAW,EAAE,YAAY,EAAE;YACnF,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACnD;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,QAAQ;gBAAgB,CAAC;YAChF,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACvF;QAEA,QAAQ,GAAG,CAAC,2BAA2B;IACzC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,MAAM;IACR;AACF;AAEO,MAAM,kBAAkB,OAC7B,SACA,YACA;IAQA,IAAI;QACF,MAAM,yBAAyB,MAAM,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE;QACnD,IAAI,CAAC,wBAAwB;YAC3B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAE5D,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,QAAQ,EAAE,QAAQ,WAAW,EAAE,WAAW,QAAQ,CAAC,EAAE;YAC3F,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACnD;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,QAAQ;gBAAgB,CAAC;YAChF,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACvF;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,QAAQ,GAAG,CAAC,4BAA4B,YAAY,cAAc,OAAO,YAAY;QACrF,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,MAAM;IACR;AACF;AAEO,MAAM,wBAAwB,OACnC,SACA,YACA,QAAgB,EAAE;IAElB,IAAI;QACF,MAAM,yBAAyB,MAAM,CAAA,GAAA,iIAAA,CAAA,gBAAa,AAAD,EAAE;QACnD,IAAI,CAAC,wBAAwB;YAC3B,MAAM,IAAI,MAAM;QAClB;QACA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAE5D,IAAI,CAAC,SAAS;YACZ,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,QAAQ,EAAE,QAAQ,WAAW,EAAE,WAAW,kBAAkB,EAAE,OAAO,EAAE;YAC7G,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACnD;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;oBAAE,QAAQ;gBAAgB,CAAC;YAChF,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;QACvF;QAEA,MAAM,aAAa,MAAM,SAAS,IAAI;QACtC,QAAQ,GAAG,CAAC,0CAA0C,YAAY,WAAW,MAAM;QACnF,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 5540, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/agents/use-agent-workflows.ts"], "sourcesContent": ["import { createMuta<PERSON>Hook, create<PERSON><PERSON><PERSON><PERSON>ook } from '@/hooks/use-query';\r\nimport { useQueryClient } from '@tanstack/react-query';\r\nimport { toast } from 'sonner';\r\nimport { workflowKeys } from './workflow-keys';\r\nimport { \r\n  CreateWorkflowRequest, \r\n  UpdateWorkflowRequest, \r\n  ExecuteWorkflowRequest,\r\n  getAgentWorkflows,\r\n  createAgentWorkflow,\r\n  updateAgentWorkflow,\r\n  deleteAgentWorkflow,\r\n  executeWorkflow,\r\n  getWorkflowExecutions\r\n} from './workflow-utils';\r\n\r\n\r\nexport const useAgentWorkflows = (agentId: string) => {\r\n  return createQueryHook(\r\n    workflowKeys.agent(agentId),\r\n    () => getAgentWorkflows(agentId),\r\n    {\r\n      enabled: !!agentId,\r\n      staleTime: 30000,\r\n    }\r\n  )();\r\n};\r\n\r\nexport const useCreateAgentWorkflow = () => {\r\n  const queryClient = useQueryClient();\r\n  \r\n  return createMutationHook(\r\n    ({ agentId, workflow }: { agentId: string; workflow: CreateWorkflowRequest }) => \r\n      createAgentWorkflow(agentId, workflow),\r\n    {\r\n      onSuccess: (data, variables) => {\r\n        queryClient.invalidateQueries({ queryKey: workflowKeys.agent(variables.agentId) });\r\n        toast.success('Workflow created successfully');\r\n      },\r\n    }\r\n  )();\r\n};\r\n\r\nexport const useUpdateAgentWorkflow = () => {\r\n  const queryClient = useQueryClient();\r\n  \r\n  return createMutationHook(\r\n    ({ agentId, workflowId, workflow }: { agentId: string; workflowId: string; workflow: UpdateWorkflowRequest }) => \r\n      updateAgentWorkflow(agentId, workflowId, workflow),\r\n    {\r\n      onSuccess: (data, variables) => {\r\n        queryClient.invalidateQueries({ queryKey: workflowKeys.agent(variables.agentId) });\r\n        toast.success('Workflow updated successfully');\r\n      },\r\n    }\r\n  )();\r\n};\r\n\r\nexport const useDeleteAgentWorkflow = () => {\r\n  const queryClient = useQueryClient();\r\n  \r\n  return createMutationHook(\r\n    ({ agentId, workflowId }: { agentId: string; workflowId: string }) => \r\n      deleteAgentWorkflow(agentId, workflowId),\r\n    {\r\n      onSuccess: (_, variables) => {\r\n        queryClient.invalidateQueries({ queryKey: workflowKeys.agent(variables.agentId) });\r\n        toast.success('Workflow deleted successfully');\r\n      },\r\n    }\r\n  )();\r\n};\r\n\r\nexport const useExecuteWorkflow = () => {\r\n  const queryClient = useQueryClient();\r\n  \r\n  return createMutationHook(\r\n    ({ agentId, workflowId, execution }: { agentId: string; workflowId: string; execution: ExecuteWorkflowRequest }) => \r\n      executeWorkflow(agentId, workflowId, execution),\r\n    {\r\n      onSuccess: (_, variables) => {\r\n        queryClient.invalidateQueries({ queryKey: workflowKeys.executions(variables.agentId, variables.workflowId) });\r\n        toast.success('Workflow execution started');\r\n      },\r\n    }\r\n  )();\r\n};\r\n\r\nexport const useWorkflowExecutions = (agentId: string, workflowId: string, limit: number = 20) => {\r\n  return createQueryHook(\r\n    workflowKeys.executions(agentId, workflowId),\r\n    () => getWorkflowExecutions(agentId, workflowId, limit),\r\n    {\r\n      enabled: !!agentId && !!workflowId,\r\n      staleTime: 10000, // 10 seconds\r\n    }\r\n  )();\r\n}; "], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAaO,MAAM,oBAAoB,CAAC;IAChC,OAAO,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EACnB,+JAAA,CAAA,eAAY,CAAC,KAAK,CAAC,UACnB,IAAM,CAAA,GAAA,gKAAA,CAAA,oBAAiB,AAAD,EAAE,UACxB;QACE,SAAS,CAAC,CAAC;QACX,WAAW;IACb;AAEJ;AAEO,MAAM,yBAAyB;;IACpC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EACtB,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAwD,GAC1E,CAAA,GAAA,gKAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,WAC/B;QACE,WAAW,CAAC,MAAM;YAChB,YAAY,iBAAiB,CAAC;gBAAE,UAAU,+JAAA,CAAA,eAAY,CAAC,KAAK,CAAC,UAAU,OAAO;YAAE;YAChF,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;IACF;AAEJ;GAba;;QACS,yLAAA,CAAA,iBAAc;;;AAc7B,MAAM,yBAAyB;;IACpC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EACtB,CAAC,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAA4E,GAC1G,CAAA,GAAA,gKAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,YAAY,WAC3C;QACE,WAAW,CAAC,MAAM;YAChB,YAAY,iBAAiB,CAAC;gBAAE,UAAU,+JAAA,CAAA,eAAY,CAAC,KAAK,CAAC,UAAU,OAAO;YAAE;YAChF,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;IACF;AAEJ;IAba;;QACS,yLAAA,CAAA,iBAAc;;;AAc7B,MAAM,yBAAyB;;IACpC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EACtB,CAAC,EAAE,OAAO,EAAE,UAAU,EAA2C,GAC/D,CAAA,GAAA,gKAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,aAC/B;QACE,WAAW,CAAC,GAAG;YACb,YAAY,iBAAiB,CAAC;gBAAE,UAAU,+JAAA,CAAA,eAAY,CAAC,KAAK,CAAC,UAAU,OAAO;YAAE;YAChF,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;IACF;AAEJ;IAba;;QACS,yLAAA,CAAA,iBAAc;;;AAc7B,MAAM,qBAAqB;;IAChC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,+HAAA,CAAA,qBAAkB,AAAD,EACtB,CAAC,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAA8E,GAC7G,CAAA,GAAA,gKAAA,CAAA,kBAAe,AAAD,EAAE,SAAS,YAAY,YACvC;QACE,WAAW,CAAC,GAAG;YACb,YAAY,iBAAiB,CAAC;gBAAE,UAAU,+JAAA,CAAA,eAAY,CAAC,UAAU,CAAC,UAAU,OAAO,EAAE,UAAU,UAAU;YAAE;YAC3G,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB;IACF;AAEJ;IAba;;QACS,yLAAA,CAAA,iBAAc;;;AAc7B,MAAM,wBAAwB,CAAC,SAAiB,YAAoB,QAAgB,EAAE;IAC3F,OAAO,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EACnB,+JAAA,CAAA,eAAY,CAAC,UAAU,CAAC,SAAS,aACjC,IAAM,CAAA,GAAA,gKAAA,CAAA,wBAAqB,AAAD,EAAE,SAAS,YAAY,QACjD;QACE,SAAS,CAAC,CAAC,WAAW,CAAC,CAAC;QACxB,WAAW;IACb;AAEJ", "debugId": null}}, {"offset": {"line": 5648, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/triggers/use-oauth-integrations.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\r\nimport { createClient } from '@/lib/supabase/client';\r\nimport { toast } from 'sonner';\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_BACKEND_URL;\r\n\r\ninterface OAuthIntegration {\r\n  trigger_id: string;\r\n  provider: string;\r\n  name: string;\r\n  is_active: boolean;\r\n  workspace_name?: string;\r\n  bot_name?: string;\r\n  installed_at: string;\r\n  created_at: string;\r\n}\r\n\r\ninterface OAuthIntegrationStatus {\r\n  agent_id: string;\r\n  integrations: OAuthIntegration[];\r\n}\r\n\r\ninterface OAuthInstallRequest {\r\n  agent_id: string;\r\n  provider: 'slack' | 'discord' | 'teams';\r\n}\r\n\r\ninterface OAuthInstallResponse {\r\n  install_url: string;\r\n  provider: string;\r\n}\r\n\r\nconst getAccessToken = async () => {\r\n  const supabase = createClient();\r\n  const { data: { session } } = await supabase.auth.getSession();\r\n  if (!session) {\r\n    throw new Error('You must be logged in to manage integrations');\r\n  }\r\n  return session.access_token;\r\n};\r\n\r\nconst initiateOAuthInstall = async (request: OAuthInstallRequest): Promise<OAuthInstallResponse> => {\r\n  const accessToken = await getAccessToken();\r\n  const response = await fetch(`${API_URL}/integrations/install`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      'Authorization': `Bearer ${accessToken}`,\r\n    },\r\n    body: JSON.stringify(request),\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const errorData = await response.json().catch(() => ({}));\r\n    throw new Error(errorData.detail || 'Failed to initiate installation');\r\n  }\r\n\r\n  return response.json();\r\n};\r\n\r\nconst uninstallOAuthIntegration = async (triggerId: string): Promise<void> => {\r\n  const accessToken = await getAccessToken();\r\n  const response = await fetch(`${API_URL}/integrations/uninstall/${triggerId}`, {\r\n    method: 'DELETE',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      'Authorization': `Bearer ${accessToken}`,\r\n    },\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const errorData = await response.json().catch(() => ({}));\r\n    throw new Error(errorData.detail || 'Failed to uninstall integration');\r\n  }\r\n};\r\n\r\nexport const useInstallOAuthIntegration = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: initiateOAuthInstall,\r\n    onSuccess: (data, variables) => {\r\n      sessionStorage.setItem('oauth_agent_id', variables.agent_id);\r\n      sessionStorage.setItem('oauth_provider', variables.provider);\r\n      window.location.href = data.install_url;\r\n    },\r\n    onError: (error: Error) => {\r\n      toast.error(error.message || 'Failed to start OAuth installation');\r\n    },\r\n  });\r\n};\r\n\r\nexport const useUninstallOAuthIntegration = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: uninstallOAuthIntegration,\r\n    onSuccess: (_, triggerId) => {\r\n      toast.success('Integration uninstalled successfully');\r\n      queryClient.invalidateQueries({ queryKey: ['oauth-integrations'] });\r\n      queryClient.invalidateQueries({ queryKey: ['agent-triggers'] });\r\n    },\r\n    onError: (error: Error) => {\r\n      toast.error(error.message || 'Failed to uninstall integration');\r\n    },\r\n  });\r\n};\r\n\r\nexport const useOAuthCallbackHandler = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  const handleCallback = () => {\r\n    const urlParams = new URLSearchParams(window.location.search);\r\n    const agentId = sessionStorage.getItem('oauth_agent_id');\r\n    const provider = sessionStorage.getItem('oauth_provider');\r\n    \r\n    const slackSuccess = urlParams.get('slack_success');\r\n    const discordSuccess = urlParams.get('discord_success');\r\n    const teamsSuccess = urlParams.get('teams_success');\r\n    const triggerId = urlParams.get('trigger_id');\r\n    const workspaceName = urlParams.get('workspace');\r\n    const botName = urlParams.get('bot_name');\r\n\r\n    const slackError = urlParams.get('slack_error');\r\n    const discordError = urlParams.get('discord_error');\r\n    const teamsError = urlParams.get('teams_error');\r\n\r\n    if (slackSuccess || discordSuccess || teamsSuccess) {\r\n      const providerName = slackSuccess ? 'Slack' : discordSuccess ? 'Discord' : 'Teams';\r\n      toast.success(`${providerName} integration installed successfully!`);\r\n      \r\n      if (agentId) {\r\n        queryClient.invalidateQueries({ queryKey: ['oauth-integrations', agentId] });\r\n      }\r\n\r\n      sessionStorage.removeItem('oauth_agent_id');\r\n      sessionStorage.removeItem('oauth_provider');\r\n      \r\n      const newUrl = window.location.pathname;\r\n      window.history.replaceState({}, document.title, newUrl);\r\n    } else if (slackError || discordError || teamsError) {\r\n      const error = slackError || discordError || teamsError;\r\n      const providerName = slackError ? 'Slack' : discordError ? 'Discord' : 'Teams';\r\n      toast.error(`Failed to install ${providerName} integration: ${error}`);\r\n      \r\n      sessionStorage.removeItem('oauth_agent_id');\r\n      sessionStorage.removeItem('oauth_provider');\r\n      \r\n      const newUrl = window.location.pathname;\r\n      window.history.replaceState({}, document.title, newUrl);\r\n    }\r\n  };\r\n\r\n  return { handleCallback };\r\n}; "], "names": [], "mappings": ";;;;;AAIgB;AAJhB;AAAA;AACA;AACA;;;;;AAEA,MAAM;AA4BN,MAAM,iBAAiB;IACrB,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;IAC5D,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO,QAAQ,YAAY;AAC7B;AAEA,MAAM,uBAAuB,OAAO;IAClC,MAAM,cAAc,MAAM;IAC1B,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,qBAAqB,CAAC,EAAE;QAC9D,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,iBAAiB,CAAC,OAAO,EAAE,aAAa;QAC1C;QACA,MAAM,KAAK,SAAS,CAAC;IACvB;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;QACvD,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI;IACtC;IAEA,OAAO,SAAS,IAAI;AACtB;AAEA,MAAM,4BAA4B,OAAO;IACvC,MAAM,cAAc,MAAM;IAC1B,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,wBAAwB,EAAE,WAAW,EAAE;QAC7E,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,iBAAiB,CAAC,OAAO,EAAE,aAAa;QAC1C;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;QACvD,MAAM,IAAI,MAAM,UAAU,MAAM,IAAI;IACtC;AACF;AAEO,MAAM,6BAA6B;;IACxC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY;QACZ,SAAS;sDAAE,CAAC,MAAM;gBAChB,eAAe,OAAO,CAAC,kBAAkB,UAAU,QAAQ;gBAC3D,eAAe,OAAO,CAAC,kBAAkB,UAAU,QAAQ;gBAC3D,OAAO,QAAQ,CAAC,IAAI,GAAG,KAAK,WAAW;YACzC;;QACA,OAAO;sDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B;;IACF;AACF;GAda;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAab,MAAM,+BAA+B;;IAC1C,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,YAAY;QACZ,SAAS;wDAAE,CAAC,GAAG;gBACb,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAqB;gBAAC;gBACjE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAiB;gBAAC;YAC/D;;QACA,OAAO;wDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B;;IACF;AACF;IAda;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAab,MAAM,0BAA0B;;IACrC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,iBAAiB;QACrB,MAAM,YAAY,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;QAC5D,MAAM,UAAU,eAAe,OAAO,CAAC;QACvC,MAAM,WAAW,eAAe,OAAO,CAAC;QAExC,MAAM,eAAe,UAAU,GAAG,CAAC;QACnC,MAAM,iBAAiB,UAAU,GAAG,CAAC;QACrC,MAAM,eAAe,UAAU,GAAG,CAAC;QACnC,MAAM,YAAY,UAAU,GAAG,CAAC;QAChC,MAAM,gBAAgB,UAAU,GAAG,CAAC;QACpC,MAAM,UAAU,UAAU,GAAG,CAAC;QAE9B,MAAM,aAAa,UAAU,GAAG,CAAC;QACjC,MAAM,eAAe,UAAU,GAAG,CAAC;QACnC,MAAM,aAAa,UAAU,GAAG,CAAC;QAEjC,IAAI,gBAAgB,kBAAkB,cAAc;YAClD,MAAM,eAAe,eAAe,UAAU,iBAAiB,YAAY;YAC3E,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,GAAG,aAAa,oCAAoC,CAAC;YAEnE,IAAI,SAAS;gBACX,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAsB;qBAAQ;gBAAC;YAC5E;YAEA,eAAe,UAAU,CAAC;YAC1B,eAAe,UAAU,CAAC;YAE1B,MAAM,SAAS,OAAO,QAAQ,CAAC,QAAQ;YACvC,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,SAAS,KAAK,EAAE;QAClD,OAAO,IAAI,cAAc,gBAAgB,YAAY;YACnD,MAAM,QAAQ,cAAc,gBAAgB;YAC5C,MAAM,eAAe,aAAa,UAAU,eAAe,YAAY;YACvE,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,CAAC,kBAAkB,EAAE,aAAa,cAAc,EAAE,OAAO;YAErE,eAAe,UAAU,CAAC;YAC1B,eAAe,UAAU,CAAC;YAE1B,MAAM,SAAS,OAAO,QAAQ,CAAC,QAAQ;YACvC,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,SAAS,KAAK,EAAE;QAClD;IACF;IAEA,OAAO;QAAE;IAAe;AAC1B;IA9Ca;;QACS,yLAAA,CAAA,iBAAc", "debugId": null}}, {"offset": {"line": 5818, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/pipedream/use-pipedream-profiles.ts"], "sourcesContent": ["import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';\r\nimport { pipedreamApi } from './utils';\r\nimport { pipedreamKeys } from './keys';\r\nimport type {\r\n  PipedreamProfile,\r\n  CreateProfileRequest,\r\n  UpdateProfileRequest,\r\n} from '@/components/agents/pipedream/pipedream-types';\r\nimport { toast } from 'sonner';\r\n\r\nexport const usePipedreamProfiles = (params?: { app_slug?: string; is_active?: boolean }) => {\r\n  return useQuery({\r\n    queryKey: pipedreamKeys.profiles.list(params),\r\n    queryFn: () => pipedreamApi.getProfiles(params),\r\n    staleTime: 5 * 60 * 1000,\r\n  });\r\n};\r\n\r\n// Hook to get a single profile\r\nexport const usePipedreamProfile = (profileId: string, enabled = true) => {\r\n  return useQuery({\r\n    queryKey: pipedreamKeys.profiles.detail(profileId),\r\n    queryFn: () => pipedreamApi.getProfile(profileId),\r\n    enabled: enabled && !!profileId,\r\n    staleTime: 5 * 60 * 1000,\r\n  });\r\n};\r\n\r\n// Hook to get profile connections\r\nexport const usePipedreamProfileConnections = (profileId: string, enabled = true) => {\r\n  return useQuery({\r\n    queryKey: pipedreamKeys.profiles.connections(profileId),\r\n    queryFn: () => pipedreamApi.getProfileConnections(profileId),\r\n    enabled: enabled && !!profileId,\r\n    staleTime: 30 * 1000, // 30 seconds\r\n  });\r\n};\r\n\r\n// Hook to create a profile\r\nexport const useCreatePipedreamProfile = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (request: CreateProfileRequest) => pipedreamApi.createProfile(request),\r\n    onSuccess: (data) => {\r\n      queryClient.invalidateQueries({ queryKey: pipedreamKeys.profiles.all() });\r\n      toast.success(`Profile \"${data.profile_name}\" created successfully`);\r\n    },\r\n    onError: (error: Error) => {\r\n      toast.error(error.message || 'Failed to create profile');\r\n    },\r\n  });\r\n};\r\n\r\n// Hook to update a profile\r\nexport const useUpdatePipedreamProfile = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: ({ profileId, request }: { profileId: string; request: UpdateProfileRequest }) =>\r\n      pipedreamApi.updateProfile(profileId, request),\r\n    onSuccess: (data) => {\r\n      queryClient.invalidateQueries({ queryKey: pipedreamKeys.profiles.all() });\r\n      queryClient.invalidateQueries({ queryKey: pipedreamKeys.profiles.detail(data.profile_id) });\r\n      toast.success('Profile updated successfully');\r\n    },\r\n    onError: (error: Error) => {\r\n      toast.error(error.message || 'Failed to update profile');\r\n    },\r\n  });\r\n};\r\n\r\nexport const useDeletePipedreamProfile = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: (profileId: string) => pipedreamApi.deleteProfile(profileId),\r\n    onSuccess: (_, profileId) => {\r\n      queryClient.invalidateQueries({ queryKey: pipedreamKeys.profiles.all() });\r\n      queryClient.removeQueries({ queryKey: pipedreamKeys.profiles.detail(profileId) });\r\n      toast.success('Profile deleted successfully');\r\n    },\r\n    onError: (error: Error) => {\r\n      toast.error(error.message || 'Failed to delete profile');\r\n    },\r\n  });\r\n};\r\n\r\n\r\nexport const useConnectPipedreamProfile = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: ({ profileId, app }: { profileId: string; app?: string }) =>\r\n      pipedreamApi.connectProfile(profileId, app),\r\n    onSuccess: (data) => {\r\n      queryClient.invalidateQueries({ queryKey: pipedreamKeys.profiles.all() });\r\n      queryClient.invalidateQueries({ queryKey: pipedreamKeys.profiles.detail(data.profile_id) });\r\n      queryClient.invalidateQueries({ queryKey: pipedreamKeys.profiles.connections(data.profile_id) });\r\n      if (data.link) {\r\n        const connectWindow = window.open(data.link, '_blank', 'width=600,height=700');\r\n        if (connectWindow) {\r\n          const checkClosed = setInterval(() => {\r\n            if (connectWindow.closed) {\r\n              clearInterval(checkClosed);\r\n              queryClient.invalidateQueries({ queryKey: pipedreamKeys.profiles.all() });\r\n              queryClient.invalidateQueries({ queryKey: pipedreamKeys.profiles.detail(data.profile_id) });\r\n              queryClient.invalidateQueries({ queryKey: pipedreamKeys.profiles.connections(data.profile_id) });\r\n              toast.success('Connection process completed');\r\n            }\r\n          }, 1000);\r\n          setTimeout(() => {\r\n            clearInterval(checkClosed);\r\n          }, 5 * 60 * 1000);\r\n        } else {\r\n          toast.error('Failed to open connection window. Please check your popup blocker.');\r\n        }\r\n      }\r\n    },\r\n    onError: (error: Error) => {\r\n      toast.error(error.message || 'Failed to connect profile');\r\n    },\r\n  });\r\n}; "], "names": [], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA;AACA;AACA;AAMA;;;;;;AAEO,MAAM,uBAAuB,CAAC;;IACnC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,sJAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,IAAI,CAAC;QACtC,OAAO;6CAAE,IAAM,uJAAA,CAAA,eAAY,CAAC,WAAW,CAAC;;QACxC,WAAW,IAAI,KAAK;IACtB;AACF;GANa;;QACJ,8KAAA,CAAA,WAAQ;;;AAQV,MAAM,sBAAsB,CAAC,WAAmB,UAAU,IAAI;;IACnE,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,sJAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,MAAM,CAAC;QACxC,OAAO;4CAAE,IAAM,uJAAA,CAAA,eAAY,CAAC,UAAU,CAAC;;QACvC,SAAS,WAAW,CAAC,CAAC;QACtB,WAAW,IAAI,KAAK;IACtB;AACF;IAPa;;QACJ,8KAAA,CAAA,WAAQ;;;AASV,MAAM,iCAAiC,CAAC,WAAmB,UAAU,IAAI;;IAC9E,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,sJAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,WAAW,CAAC;QAC7C,OAAO;uDAAE,IAAM,uJAAA,CAAA,eAAY,CAAC,qBAAqB,CAAC;;QAClD,SAAS,WAAW,CAAC,CAAC;QACtB,WAAW,KAAK;IAClB;AACF;IAPa;;QACJ,8KAAA,CAAA,WAAQ;;;AASV,MAAM,4BAA4B;;IACvC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;qDAAE,CAAC,UAAkC,uJAAA,CAAA,eAAY,CAAC,aAAa,CAAC;;QAC1E,SAAS;qDAAE,CAAC;gBACV,YAAY,iBAAiB,CAAC;oBAAE,UAAU,sJAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,GAAG;gBAAG;gBACvE,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,SAAS,EAAE,KAAK,YAAY,CAAC,sBAAsB,CAAC;YACrE;;QACA,OAAO;qDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B;;IACF;AACF;IAba;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAab,MAAM,4BAA4B;;IACvC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;qDAAE,CAAC,EAAE,SAAS,EAAE,OAAO,EAAwD,GACvF,uJAAA,CAAA,eAAY,CAAC,aAAa,CAAC,WAAW;;QACxC,SAAS;qDAAE,CAAC;gBACV,YAAY,iBAAiB,CAAC;oBAAE,UAAU,sJAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,GAAG;gBAAG;gBACvE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,sJAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,UAAU;gBAAE;gBACzF,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;qDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B;;IACF;AACF;IAfa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAcb,MAAM,4BAA4B;;IACvC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;qDAAE,CAAC,YAAsB,uJAAA,CAAA,eAAY,CAAC,aAAa,CAAC;;QAC9D,SAAS;qDAAE,CAAC,GAAG;gBACb,YAAY,iBAAiB,CAAC;oBAAE,UAAU,sJAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,GAAG;gBAAG;gBACvE,YAAY,aAAa,CAAC;oBAAE,UAAU,sJAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAAW;gBAC/E,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;;QACA,OAAO;qDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B;;IACF;AACF;IAda;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAcb,MAAM,6BAA6B;;IACxC,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;sDAAE,CAAC,EAAE,SAAS,EAAE,GAAG,EAAuC,GAClE,uJAAA,CAAA,eAAY,CAAC,cAAc,CAAC,WAAW;;QACzC,SAAS;sDAAE,CAAC;gBACV,YAAY,iBAAiB,CAAC;oBAAE,UAAU,sJAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,GAAG;gBAAG;gBACvE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,sJAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,UAAU;gBAAE;gBACzF,YAAY,iBAAiB,CAAC;oBAAE,UAAU,sJAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,UAAU;gBAAE;gBAC9F,IAAI,KAAK,IAAI,EAAE;oBACb,MAAM,gBAAgB,OAAO,IAAI,CAAC,KAAK,IAAI,EAAE,UAAU;oBACvD,IAAI,eAAe;wBACjB,MAAM,cAAc;kFAAY;gCAC9B,IAAI,cAAc,MAAM,EAAE;oCACxB,cAAc;oCACd,YAAY,iBAAiB,CAAC;wCAAE,UAAU,sJAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,GAAG;oCAAG;oCACvE,YAAY,iBAAiB,CAAC;wCAAE,UAAU,sJAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,UAAU;oCAAE;oCACzF,YAAY,iBAAiB,CAAC;wCAAE,UAAU,sJAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,UAAU;oCAAE;oCAC9F,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gCAChB;4BACF;iFAAG;wBACH;sEAAW;gCACT,cAAc;4BAChB;qEAAG,IAAI,KAAK;oBACd,OAAO;wBACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACd;gBACF;YACF;;QACA,OAAO;sDAAE,CAAC;gBACR,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC/B;;IACF;AACF;IAlCa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW", "debugId": null}}, {"offset": {"line": 6046, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/agents/use-pipedream-tools.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\r\nimport { toast } from 'sonner';\r\nimport { backendApi } from '@/lib/api-client';\r\nimport { agentKeys } from './keys';\r\n\r\nexport interface PipedreamTool {\r\n  name: string;\r\n  description: string;\r\n  enabled: boolean;\r\n}\r\n\r\nexport interface PipedreamToolsResponse {\r\n  profile_id: string;\r\n  app_name: string;\r\n  profile_name: string;\r\n  tools: PipedreamTool[];\r\n  has_mcp_config: boolean;\r\n}\r\n\r\nexport interface UpdatePipedreamToolsRequest {\r\n  enabled_tools: string[];\r\n}\r\n\r\nexport const usePipedreamToolsForAgent = (agentId: string, profileId: string) => {\r\n  return useQuery({\r\n    queryKey: ['pipedream-tools', agentId, profileId],\r\n    queryFn: async (): Promise<PipedreamToolsResponse> => {\r\n      const response = await backendApi.get(`/agents/${agentId}/pipedream-tools/${profileId}`);\r\n      return response.data;\r\n    },\r\n    enabled: !!agentId && !!profileId,\r\n    staleTime: 5 * 60 * 1000,\r\n    retry: (failureCount, error: any) => {\r\n      if (error?.response?.status === 404 || error?.response?.status === 400) {\r\n        return false;\r\n      }\r\n      return failureCount < 2;\r\n    },\r\n  });\r\n};\r\n\r\nexport const useUpdatePipedreamToolsForAgent = () => {\r\n  const queryClient = useQueryClient();\r\n\r\n  return useMutation({\r\n    mutationFn: async ({\r\n      agentId,\r\n      profileId,\r\n      enabledTools,\r\n    }: {\r\n      agentId: string;\r\n      profileId: string;\r\n      enabledTools: string[];\r\n    }) => {\r\n      const response = await backendApi.put(\r\n        `/agents/${agentId}/pipedream-tools/${profileId}`,\r\n        { enabled_tools: enabledTools }\r\n      );\r\n      return response.data;\r\n    },\r\n    onSuccess: (data, variables) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: ['pipedream-tools', variables.agentId, variables.profileId],\r\n      });\r\n      queryClient.invalidateQueries({\r\n        queryKey: agentKeys.detail(variables.agentId),\r\n      });\r\n      queryClient.invalidateQueries({\r\n        queryKey: ['agent-tools', variables.agentId],\r\n      });\r\n    },\r\n    onError: (error: any) => {\r\n      const message = error?.response?.data?.detail || 'Failed to update Pipedream tools';\r\n      toast.error(message);\r\n    },\r\n  });\r\n};\r\n\r\n\r\nexport const usePipedreamToolsData = (agentId: string, profileId: string) => {\r\n  const { data, isLoading, error, refetch } = usePipedreamToolsForAgent(agentId, profileId);\r\n  const updateMutation = useUpdatePipedreamToolsForAgent();\r\n\r\n  const handleUpdateTools = (enabledTools: string[]) => {\r\n    updateMutation.mutate({ agentId, profileId, enabledTools });\r\n  };\r\n\r\n  return {\r\n    data,\r\n    isLoading,\r\n    error,\r\n    refetch,\r\n    handleUpdateTools,\r\n    isUpdating: updateMutation.isPending,\r\n  };\r\n}; "], "names": [], "mappings": ";;;;;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;AAoBO,MAAM,4BAA4B,CAAC,SAAiB;;IACzD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU;YAAC;YAAmB;YAAS;SAAU;QACjD,OAAO;kDAAE;gBACP,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,iBAAiB,EAAE,WAAW;gBACvF,OAAO,SAAS,IAAI;YACtB;;QACA,SAAS,CAAC,CAAC,WAAW,CAAC,CAAC;QACxB,WAAW,IAAI,KAAK;QACpB,KAAK;kDAAE,CAAC,cAAc;gBACpB,IAAI,OAAO,UAAU,WAAW,OAAO,OAAO,UAAU,WAAW,KAAK;oBACtE,OAAO;gBACT;gBACA,OAAO,eAAe;YACxB;;IACF;AACF;GAhBa;;QACJ,8KAAA,CAAA,WAAQ;;;AAiBV,MAAM,kCAAkC;;IAC7C,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACjB,UAAU;2DAAE,OAAO,EACjB,OAAO,EACP,SAAS,EACT,YAAY,EAKb;gBACC,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CACnC,CAAC,QAAQ,EAAE,QAAQ,iBAAiB,EAAE,WAAW,EACjD;oBAAE,eAAe;gBAAa;gBAEhC,OAAO,SAAS,IAAI;YACtB;;QACA,SAAS;2DAAE,CAAC,MAAM;gBAChB,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC;wBAAmB,UAAU,OAAO;wBAAE,UAAU,SAAS;qBAAC;gBACvE;gBACA,YAAY,iBAAiB,CAAC;oBAC5B,UAAU,mJAAA,CAAA,YAAS,CAAC,MAAM,CAAC,UAAU,OAAO;gBAC9C;gBACA,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC;wBAAe,UAAU,OAAO;qBAAC;gBAC9C;YACF;;QACA,OAAO;2DAAE,CAAC;gBACR,MAAM,UAAU,OAAO,UAAU,MAAM,UAAU;gBACjD,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;;IACF;AACF;IAnCa;;QACS,yLAAA,CAAA,iBAAc;QAE3B,iLAAA,CAAA,cAAW;;;AAmCb,MAAM,wBAAwB,CAAC,SAAiB;;IACrD,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,0BAA0B,SAAS;IAC/E,MAAM,iBAAiB;IAEvB,MAAM,oBAAoB,CAAC;QACzB,eAAe,MAAM,CAAC;YAAE;YAAS;YAAW;QAAa;IAC3D;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA,YAAY,eAAe,SAAS;IACtC;AACF;IAhBa;;QACiC;QACrB", "debugId": null}}, {"offset": {"line": 6174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/hooks/react-query/agents/use-custom-mcp-tools.ts"], "sourcesContent": ["import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';\r\nimport { backendApi } from '@/lib/api-client';\r\nimport { agentKeys } from './keys';\r\n\r\nexport interface CustomMCPTool {\r\n  name: string;\r\n  description?: string;\r\n  enabled: boolean;\r\n}\r\n\r\nexport interface CustomMCPToolsResponse {\r\n  tools: CustomMCPTool[];\r\n  has_mcp_config: boolean;\r\n}\r\n\r\nexport const useCustomMCPToolsData = (agentId: string, mcpConfig: any) => {\r\n  const queryClient = useQueryClient();\r\n  \r\n  const { data, isLoading, error, refetch } = useQuery<CustomMCPToolsResponse>({\r\n    queryKey: ['custom-mcp-tools', agentId, mcpConfig?.url],\r\n    queryFn: async () => {\r\n      const response = await backendApi.get(`/agents/${agentId}/custom-mcp-tools`, {\r\n        headers: {\r\n          'X-MCP-URL': mcpConfig.url,\r\n          'X-MCP-Type': mcpConfig.type || 'sse',\r\n          ...(mcpConfig.headers ? { 'X-MCP-Headers': JSON.stringify(mcpConfig.headers) } : {})\r\n        }\r\n      });\r\n      if (!response.success) {\r\n        throw new Error(response.error?.message || 'Failed to fetch custom MCP tools');\r\n      }\r\n      return response.data;\r\n    },\r\n    enabled: !!agentId && !!mcpConfig?.url,\r\n    staleTime: 5 * 60 * 1000,\r\n  });\r\n\r\n  const updateToolsMutation = useMutation({\r\n    mutationFn: async (enabledTools: string[]) => {\r\n      const response = await backendApi.post(`/agents/${agentId}/custom-mcp-tools`, {\r\n        url: mcpConfig.url,\r\n        type: mcpConfig.type || 'sse',\r\n        enabled_tools: enabledTools,\r\n      });\r\n      if (!response.success) {\r\n        throw new Error(response.error?.message || 'Failed to update custom MCP tools');\r\n      }\r\n      return response.data;\r\n    },\r\n    onSuccess: () => {\r\n      queryClient.invalidateQueries({ queryKey: ['custom-mcp-tools', agentId] });\r\n      queryClient.invalidateQueries({ queryKey: agentKeys.detail(agentId) });\r\n      queryClient.invalidateQueries({ queryKey: ['agent-tools', agentId] });\r\n    },\r\n  });\r\n\r\n  const handleUpdateTools = (enabledTools: string[]) => {\r\n    return updateToolsMutation.mutate(enabledTools);\r\n  };\r\n  return {\r\n    data,\r\n    isLoading,\r\n    error,\r\n    refetch,\r\n    handleUpdateTools,\r\n    isUpdating: updateToolsMutation.isPending,\r\n  };\r\n}; "], "names": [], "mappings": ";;;AAAA;AAAA;AAAA;AACA;AACA;;;;;AAaO,MAAM,wBAAwB,CAAC,SAAiB;;IACrD,MAAM,cAAc,CAAA,GAAA,yLAAA,CAAA,iBAAc,AAAD;IAEjC,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAA0B;QAC3E,UAAU;YAAC;YAAoB;YAAS,WAAW;SAAI;QACvD,OAAO;8CAAE;gBACP,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,iBAAiB,CAAC,EAAE;oBAC3E,SAAS;wBACP,aAAa,UAAU,GAAG;wBAC1B,cAAc,UAAU,IAAI,IAAI;wBAChC,GAAI,UAAU,OAAO,GAAG;4BAAE,iBAAiB,KAAK,SAAS,CAAC,UAAU,OAAO;wBAAE,IAAI,CAAC,CAAC;oBACrF;gBACF;gBACA,IAAI,CAAC,SAAS,OAAO,EAAE;oBACrB,MAAM,IAAI,MAAM,SAAS,KAAK,EAAE,WAAW;gBAC7C;gBACA,OAAO,SAAS,IAAI;YACtB;;QACA,SAAS,CAAC,CAAC,WAAW,CAAC,CAAC,WAAW;QACnC,WAAW,IAAI,KAAK;IACtB;IAEA,MAAM,sBAAsB,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACtC,UAAU;sEAAE,OAAO;gBACjB,MAAM,WAAW,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE,QAAQ,iBAAiB,CAAC,EAAE;oBAC5E,KAAK,UAAU,GAAG;oBAClB,MAAM,UAAU,IAAI,IAAI;oBACxB,eAAe;gBACjB;gBACA,IAAI,CAAC,SAAS,OAAO,EAAE;oBACrB,MAAM,IAAI,MAAM,SAAS,KAAK,EAAE,WAAW;gBAC7C;gBACA,OAAO,SAAS,IAAI;YACtB;;QACA,SAAS;sEAAE;gBACT,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAoB;qBAAQ;gBAAC;gBACxE,YAAY,iBAAiB,CAAC;oBAAE,UAAU,mJAAA,CAAA,YAAS,CAAC,MAAM,CAAC;gBAAS;gBACpE,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAe;qBAAQ;gBAAC;YACrE;;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAO,oBAAoB,MAAM,CAAC;IACpC;IACA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA,YAAY,oBAAoB,SAAS;IAC3C;AACF;GApDa;;QACS,yLAAA,CAAA,iBAAc;QAEU,8KAAA,CAAA,WAAQ;QAmBxB,iLAAA,CAAA,cAAW", "debugId": null}}]}