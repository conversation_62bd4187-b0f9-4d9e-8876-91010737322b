"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6528],{26528:(e,s,a)=>{a.d(s,{Z:()=>G});var t=a(95155),l=a(12115),n=a(30285),r=a(54416),i=a(25657),o=a(26104),c=a(2417),d=a(38198),m=a(78948),u=a(71319),x=a(56671),p=a(59434),h=a(26715),g=a(66695),f=a(26126),j=a(44838),v=a(66474),N=a(381),y=a(84616),b=a(71539);let w={All:"\uD83C\uDF1F",Communication:"\uD83D\uDCAC","Artificial Intelligence (AI)":"\uD83E\uDD16","Social Media":"\uD83D\uDCF1",CRM:"\uD83D\uDC65",Marketing:"\uD83D\uDCC8",Analytics:"\uD83D\uDCCA",Commerce:"\uD83D\uDCCA",Databases:"\uD83D\uDDC4️","File Storage":"\uD83D\uDDC2️","Help Desk & Support":"\uD83C\uDFA7","Infrastructure & Cloud":"\uD83C\uDF10","E-commerce":"\uD83D\uDED2","Developer Tools":"\uD83D\uDD27","Web & App Development":"\uD83C\uDF10","Business Management":"\uD83D\uDCBC",Productivity:"⚡",Finance:"\uD83D\uDCB0",Email:"\uD83D\uDCE7","Project Management":"\uD83D\uDCCB",Storage:"\uD83D\uDCBE","AI/ML":"\uD83E\uDD16","Data & Databases":"\uD83D\uDDC4️",Video:"\uD83C\uDFA5",Calendar:"\uD83D\uDCC5",Forms:"\uD83D\uDCDD",Security:"\uD83D\uDD12",HR:"\uD83D\uDC54",Sales:"\uD83D\uDCBC",Support:"\uD83C\uDFA7",Design:"\uD83C\uDFA8","Business Intelligence":"\uD83D\uDCC8",Automation:"\uD83D\uDD04",News:"\uD83D\uDCF0",Weather:"\uD83C\uDF24️",Travel:"✈️",Education:"\uD83C\uDF93",Health:"\uD83C\uDFE5"},C={FIRST_PAGE:"FIRST_PAGE"},A=e=>{let s=new Set;return e.forEach(e=>{e.categories.forEach(e=>s.add(e))}),["All",...Array.from(s).sort()]},_=(e,s)=>"All"===s?e:e.filter(e=>e.categories.includes(s)),k=(e,s)=>Object.entries(e.reduce((e,s)=>{let a=s.app_slug;return e[a]||(e[a]=[]),e[a].push(s),e},{})).map(e=>{var a,t,l;let[n,r]=e,i=r[0],o=s.find(e=>e.name_slug===i.app_slug||e.name.toLowerCase()===i.app_name.toLowerCase());return{id:"app_".concat(n),name:i.app_name,name_slug:i.app_slug,auth_type:"keys",description:"Access your ".concat(i.app_name," workspace and tools"),img_src:(null==o?void 0:o.img_src)||"",custom_fields_json:(null==o?void 0:o.custom_fields_json)||"[]",categories:(null==o?void 0:o.categories)||[],featured_weight:0,connect:{allowed_domains:(null==o||null==(a=o.connect)?void 0:a.allowed_domains)||null,base_proxy_target_url:(null==o||null==(t=o.connect)?void 0:t.base_proxy_target_url)||"",proxy_enabled:(null==o||null==(l=o.connect)?void 0:l.proxy_enabled)||!1},connectedProfiles:r,profileCount:r.length}}),S=(e,s,a)=>{if(!e||!s||!a)return[];let t=(e.custom_mcps||[]).filter(e=>{var s,a,t;return(null==(s=e.config)?void 0:s.profile_id)&&(null==(t=e.config)||null==(a=t.url)?void 0:a.includes("pipedream"))}),l=t.map(e=>{var s;return null==(s=e.config)?void 0:s.profile_id}).filter(Boolean);return s.filter(e=>l.includes(e.profile_id)).map(e=>{var s;let a=t.find(s=>{var a;return(null==(a=s.config)?void 0:a.profile_id)===e.profile_id});return{...e,enabledTools:(null==a?void 0:a.enabledTools)||[],toolsCount:(null==a||null==(s=a.enabledTools)?void 0:s.length)||0}})},P=e=>w[e]||"\uD83D\uDD27",E=e=>{let{app:s,compact:a=!1,mode:r="full",currentAgentId:o,agentName:d,agentPipedreamProfiles:m=[],onAppSelected:u,onConnectApp:x,onConfigureTools:h,handleCategorySelect:w}=e,{data:C}=(0,c.h4)(),A=(0,l.useMemo)(()=>(null==C?void 0:C.filter(e=>e.app_slug===s.name_slug&&e.is_active))||[],[C,s.name_slug]),_=(0,l.useMemo)(()=>"connectedProfiles"in s&&s.connectedProfiles?s.connectedProfiles:A.filter(e=>e.is_connected),[A,s]),k=(0,l.useMemo)(()=>o?m.filter(e=>e.app_slug===s.name_slug):[],[s.name_slug,o,m]),S=e=>{e.stopPropagation(),"simple"===r?null==u||u({app_slug:s.name_slug,app_name:s.name}):null==x||x(s)},E=e=>{null==h||h(e)},I=(e,s)=>{e.stopPropagation(),null==w||w(s)},T=k.reduce((e,s)=>e+(s.toolsCount||0),0);return(0,t.jsx)(g.Zp,{className:"group h-full",children:(0,t.jsxs)(g.Wu,{className:"h-full flex flex-col",children:[(0,t.jsxs)("div",{className:"flex items-start gap-3 mb-3",children:[(0,t.jsxs)("div",{className:"flex-shrink-0 relative",children:[(0,t.jsxs)("div",{className:"h-8 w-8 rounded-lg bg-muted flex items-center justify-center text-primary font-semibold overflow-hidden",children:[s.img_src?(0,t.jsx)("img",{src:s.img_src,alt:s.name,className:"w-full h-full object-cover",onError:e=>{var s;let a=e.target;a.style.display="none",null==(s=a.nextElementSibling)||s.classList.remove("hidden")}}):null,(0,t.jsx)("span",{className:(0,p.cn)("font-semibold text-sm",s.img_src?"hidden":"block"),children:s.name.charAt(0).toUpperCase()})]}),_.length>0&&(0,t.jsx)("div",{className:"absolute -top-1 -right-1 h-3 w-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800"})]}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("h3",{className:"font-medium text-sm text-foreground mb-1 truncate",children:s.name}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground line-clamp-2 leading-tight",children:s.description})]})]}),!a&&s.categories.length>0&&(0,t.jsxs)("div",{className:"flex flex-wrap gap-1 mb-3",children:[s.categories.slice(0,2).map(e=>(0,t.jsxs)(f.E,{variant:"outline",className:"text-xs px-2 py-0.5 cursor-pointer hover:bg-muted/50 transition-colors",onClick:s=>I(s,e),children:[P(e)," ",e]},e)),s.categories.length>2&&(0,t.jsxs)(f.E,{variant:"outline",className:"text-xs px-2 py-0.5",children:["+",s.categories.length-2]})]}),k.length>0&&(0,t.jsx)("div",{className:"mb-3",children:(0,t.jsxs)(j.rI,{children:[(0,t.jsx)(j.ty,{asChild:!0,children:(0,t.jsxs)(n.$,{variant:"outline",className:"h-8 w-full justify-between text-xs",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(i.A,{className:"h-3 w-3 text-primary"}),(0,t.jsxs)("span",{className:"text-xs font-medium text-foreground",children:[k.length," ",1===k.length?"Profile":"Profiles"," • ",T," tools"]})]}),(0,t.jsx)(v.A,{className:"h-3 w-3 text-muted-foreground"})]})}),(0,t.jsx)(j.SQ,{align:"start",className:"w-full",children:k.map(e=>(0,t.jsx)(j._2,{onClick:()=>E(e),className:"cursor-pointer",children:(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(N.A,{className:"h-3 w-3"}),(0,t.jsx)("span",{className:"font-medium",children:e.profile_name})]}),(0,t.jsxs)(f.E,{variant:"outline",className:"text-xs px-1 py-0",children:[e.toolsCount," tools"]})]})},e.profile_id))})]})}),(0,t.jsx)("div",{className:"flex-1"}),(0,t.jsx)("div",{className:"flex items-center gap-2 mt-auto",children:"simple"===r?(0,t.jsxs)(n.$,{size:"sm",onClick:S,className:"flex-1",children:[(0,t.jsx)(y.A,{className:"h-3 w-3"}),"Connect"]}):"profile-only"===r?(0,t.jsxs)(n.$,{size:"sm",onClick:S,variant:_.length>0?"outline":"default",className:"flex-1",children:[(0,t.jsx)(y.A,{className:"h-3 w-3"}),_.length>0?"Add Profile":"Connect"]}):(0,t.jsx)(n.$,{size:"sm",onClick:S,className:(0,p.cn)("flex-1",_.length>0&&"bg-purple-600 hover:bg-purple-700"),children:_.length>0?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(b.A,{className:"h-3 w-3"}),"Add Tools"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y.A,{className:"h-3 w-3"}),"Connect"]})})})]})})};var I=a(13052);let T=e=>{let{isCollapsed:s,onToggle:a,categories:l,selectedCategory:r,onCategorySelect:i,allApps:o}=e;return(0,t.jsxs)("div",{className:"border-r bg-sidebar flex-shrink-0 sticky top-0 h-[calc(100vh-12vh)] overflow-hidden",children:[(0,t.jsx)("div",{className:"p-3 border-b flex-shrink-0",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"flex items-center gap-2 min-w-0",children:(0,t.jsx)("div",{className:(0,p.cn)("overflow-hidden transition-all duration-300 ease-in-out",s?"w-0 opacity-0":"w-auto opacity-100"),children:(0,t.jsx)("h3",{className:"font-semibold text-sm whitespace-nowrap",children:"Categories"})})}),(0,t.jsx)(n.$,{variant:"ghost",size:"icon",onClick:a,className:"h-7 w-7",children:(0,t.jsx)(I.A,{className:(0,p.cn)("h-3 w-3 transition-transform duration-300 ease-in-out",s?"rotate-0":"rotate-180")})})]})}),(0,t.jsx)("div",{className:"p-2 space-y-0.5 flex-1 overflow-y-auto",children:l.map(e=>{let a=r===e,l=P(e);return(0,t.jsxs)("button",{onClick:()=>i(e),className:(0,p.cn)("w-full flex items-center gap-2 px-2 py-1.5 rounded-md text-left transition-all duration-200 overflow-hidden",a?"bg-primary/10 text-primary":"hover:bg-primary/5"),title:s?e:void 0,children:[(0,t.jsx)("span",{className:"text-sm flex-shrink-0",children:l}),(0,t.jsx)("div",{className:(0,p.cn)("flex items-center justify-between flex-1 min-w-0 overflow-hidden transition-all duration-300 ease-in-out",s?"w-0 opacity-0":"w-auto opacity-100"),children:(0,t.jsx)("span",{className:"text-sm truncate whitespace-nowrap",children:e})})]},e)})})]})};var F=a(62523),M=a(53311),D=a(47924),L=a(48470);let $=e=>{let{search:s,onSearchChange:a,showAgentSelector:l,currentAgentId:n,onAgentChange:r}=e;return(0,t.jsxs)("div",{className:"absolute top-0 left-0 right-0 z-10 border-b px-4 py-3",children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsx)("div",{className:"flex items-center gap-3",children:(0,t.jsx)("div",{children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("h1",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"Integrations"}),(0,t.jsxs)(f.E,{variant:"secondary",className:"bg-blue-50 text-blue-700 border-blue-200 dark:border-blue-900 dark:bg-blue-900/20 dark:text-blue-400 text-xs",children:[(0,t.jsx)(M.A,{className:"h-3 w-3"}),"New"]})]})})})}),(0,t.jsxs)("div",{className:"mt-3 flex items-center gap-3",children:[(0,t.jsxs)("div",{className:"relative max-w-md",children:[(0,t.jsx)(D.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(F.p,{placeholder:"Search 2700+ apps...",value:s,onChange:e=>a(e.target.value),className:"pl-10 h-9 focus:border-primary/50 focus:ring-primary/20 rounded-lg text-sm"})]}),l&&(0,t.jsx)("div",{className:"flex items-center gap-2",children:(0,t.jsx)(L.b,{selectedAgentId:n,onAgentSelect:r})})]})]})};var z=a(71007);let R=e=>{let{connectedApps:s,showAgentSelector:a,currentAgentId:l,agent:n,agentPipedreamProfiles:r=[],mode:i="full",onAppSelected:o,onConnectApp:c,onConfigureTools:d,onCategorySelect:m}=e;return(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,t.jsx)(z.A,{className:"h-4 w-4 text-green-600 dark:text-green-400"}),(0,t.jsx)("h2",{className:"text-md font-semibold text-gray-900 dark:text-white",children:a&&l&&n?"Available Connections":"profile-only"===i?"Connected Accounts":"My Connections"}),(0,t.jsxs)(f.E,{variant:"secondary",className:"bg-green-50 text-green-700 border-green-200 dark:border-green-900 dark:bg-green-900/20 dark:text-green-400 text-xs",children:[s.length," connected"]}),a&&l&&r.length>0&&(0,t.jsxs)(f.E,{variant:"secondary",className:"bg-primary/10 text-primary border-primary/20 text-xs",children:[r.length," in use"]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-3",children:s.map(e=>(0,t.jsx)(E,{app:e,mode:i,currentAgentId:l,agentName:null==n?void 0:n.name,agentPipedreamProfiles:r,onAppSelected:o,onConnectApp:c,onConfigureTools:d,handleCategorySelect:m},"".concat(e.id,"-").concat(l||"default")))})]})};var q=a(33109),K=a(38564),O=a(51154);let Q=e=>{let{apps:s,selectedCategory:a,mode:l="full",isLoading:n,currentAgentId:r,agent:i,agentPipedreamProfiles:o=[],onAppSelected:c,onConnectApp:d,onConfigureTools:m,onCategorySelect:u}=e;return n?(0,t.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(O.A,{className:"h-4 w-4 animate-spin text-primary"}),(0,t.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Loading integrations..."})]})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"mb-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:["All"===a?(0,t.jsx)(q.A,{className:"h-4 w-4 text-orange-600 dark:text-orange-400"}):(0,t.jsx)("span",{className:"text-lg",children:P(a)}),(0,t.jsx)("h2",{className:"text-md font-medium text-gray-900 dark:text-white",children:"All"===a?"profile-only"===l?"Available Apps":"Popular":a}),"All"===a?(0,t.jsxs)(f.E,{variant:"secondary",className:"bg-orange-50 text-orange-700 border-orange-200 dark:border-orange-900 dark:bg-orange-900/20 dark:text-orange-400 text-xs",children:[(0,t.jsx)(K.A,{className:"h-3 w-3 mr-1"}),"profile-only"===l?"Connect":"Recommended"]}):null]})}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:s.map(e=>(0,t.jsx)(E,{app:e,mode:l,currentAgentId:r,agentName:null==i?void 0:i.name,agentPipedreamProfiles:o,onAppSelected:c,onConnectApp:d,onConfigureTools:m,handleCategorySelect:u},"".concat(e.id,"-").concat(r||"default")))})]})};var U=a(1482);let H=e=>{let{selectedCategory:s,mode:a="full",onClearFilters:l}=e;return(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"text-4xl mb-3",children:"\uD83D\uDD0D"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No integrations found"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4 max-w-md mx-auto",children:"All"!==s?"No ".concat("profile-only"===a?"apps":"integrations",' found in "').concat(s,'" category. Try a different category or search term.'):"profile-only"===a?"Try adjusting your search criteria or browse available apps.":"Try adjusting your search criteria or browse our popular integrations."}),(0,t.jsxs)(n.$,{onClick:l,variant:"outline",size:"sm",className:"bg-primary hover:bg-primary/90 text-white",children:[(0,t.jsx)(U.A,{className:"h-4 w-4 mr-2"}),"Clear Filters"]})]})};var B=a(42355);let Z=e=>{let{isLoading:s,paginationHistory:a,hasMore:l,onPrevPage:r,onNextPage:i}=e;return(0,t.jsx)("div",{className:"absolute bottom-0 left-0 right-0 z-10 border-t px-4 py-3 bg-background",children:(0,t.jsx)("div",{className:"flex items-center justify-end gap-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(n.$,{onClick:r,disabled:s||0===a.length,variant:"outline",size:"sm",className:"h-9 px-3",children:(0,t.jsx)(B.A,{className:"h-4 w-4"})}),(0,t.jsx)("div",{className:"flex flex-col items-center gap-1 px-4 py-2 text-sm rounded-lg border",children:(0,t.jsxs)("div",{className:"font-medium text-gray-900 dark:text-white",children:["Page ",a.length+1]})}),(0,t.jsx)(n.$,{onClick:i,disabled:s||!l,variant:"outline",size:"sm",className:"h-9 px-3",children:s?(0,t.jsx)(O.A,{className:"h-4 w-4 animate-spin"}):(0,t.jsx)(I.A,{className:"h-4 w-4"})})]})})})},G=e=>{var s,a;let{onToolsSelected:g,onAppSelected:f,mode:j="full",onClose:v,showAgentSelector:N=!1,selectedAgentId:y,onAgentChange:b}=e,[w,P]=(0,l.useState)(""),[E,I]=(0,l.useState)("All"),[F,M]=(0,l.useState)(void 0),[D,L]=(0,l.useState)([]),[z,q]=(0,l.useState)(!1),[K,O]=(0,l.useState)(null),[U,B]=(0,l.useState)(!1),[G,W]=(0,l.useState)(!1),[X,J]=(0,l.useState)(null),[V,Y]=(0,l.useState)(y),ee=(0,h.jE)(),{data:es,isLoading:ea,error:et,refetch:el}=(0,o.Dy)(F,w),{data:en}=(0,c.h4)(),er=null!=y?y:V,{data:ei}=(0,d.fJ)(er||""),{data:eo}=(0,o.Dy)(void 0,"");l.useEffect(()=>{Y(y)},[y]);let ec=(0,l.useMemo)(()=>(null==eo?void 0:eo.apps)||[],[null==eo?void 0:eo.apps]),ed=(0,l.useMemo)(()=>S(ei,en,er),[ei,en,er]),em=(0,l.useMemo)(()=>A(ec),[ec]),eu=(0,l.useMemo)(()=>(null==en?void 0:en.filter(e=>e.is_connected))||[],[en]),ex=(0,l.useMemo)(()=>{if(!es||"All"===E)return es;let e=_(es.apps,E);return{...es,apps:e,page_info:{...es.page_info,count:e.length}}},[es,E]),ep=(0,l.useMemo)(()=>k(eu,ec),[eu,ec]),eh=e=>{I(e),M(void 0),L([])},eg=()=>{M(void 0),L([])},ef=e=>{O(e),q(!0),null==v||v()},ej=e=>{if(!er)return void x.oR.error("Please select an agent first");J({profileId:e.profile_id,appName:e.app_name,profileName:e.profile_name}),W(!0)};return et?(0,t.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"text-red-500 mb-4",children:[(0,t.jsx)(r.A,{className:"h-12 w-12 mx-auto mb-2"}),(0,t.jsx)("p",{className:"text-lg font-semibold",children:"Failed to load integrations"})]}),(0,t.jsx)(n.$,{onClick:()=>el(),className:"bg-primary hover:bg-primary/90",children:"Try Again"})]})}):(0,t.jsxs)("div",{className:"h-full flex",children:[(0,t.jsx)("div",{className:(0,p.cn)("transition-all duration-300 ease-in-out",U?"w-12":"w-62"),children:(0,t.jsx)(T,{isCollapsed:U,onToggle:()=>B(!U),categories:em,selectedCategory:E,onCategorySelect:eh,allApps:ec})}),(0,t.jsxs)("div",{className:"flex-1 relative",children:[(0,t.jsx)($,{search:w,onSearchChange:e=>{P(e),M(void 0),L([])},showAgentSelector:N,currentAgentId:er,onAgentChange:e=>{b?b(e):Y(e),e!==er&&(ee.invalidateQueries({queryKey:["agent"]}),e&&ee.invalidateQueries({queryKey:["agent",e]}))}}),(0,t.jsx)("div",{className:"absolute inset-0 pt-[100px] pb-[60px]",children:(0,t.jsx)("div",{className:"h-full overflow-y-auto p-4",children:(0,t.jsxs)("div",{className:"max-w-6xl mx-auto",children:[N&&!er&&(0,t.jsxs)("div",{className:"mb-6 text-center py-8 px-6 bg-muted/30 rounded-xl border-2 border-dashed border-border",children:[(0,t.jsx)("div",{className:"mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-4",children:(0,t.jsx)(i.A,{className:"h-6 w-6 text-muted-foreground"})}),(0,t.jsx)("h4",{className:"text-sm font-medium text-foreground mb-2",children:"Select an agent to get started"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-4 max-w-sm mx-auto",children:"Choose an agent from the dropdown above to view and manage its Pipedream integrations"})]}),ep.length>0&&(!N||er)&&(0,t.jsx)(R,{connectedApps:ep,showAgentSelector:N,currentAgentId:er,agent:ei,agentPipedreamProfiles:ed,mode:j,onAppSelected:f,onConnectApp:ef,onConfigureTools:ej,onCategorySelect:eh}),(!N||er)&&(0,t.jsx)(t.Fragment,{children:(null==ex?void 0:ex.apps)&&ex.apps.length>0?(0,t.jsx)(Q,{apps:ex.apps,selectedCategory:E,mode:j,isLoading:ea,currentAgentId:er,agent:ei,agentPipedreamProfiles:ed,onAppSelected:f,onConnectApp:ef,onConfigureTools:ej,onCategorySelect:eh}):ea?(0,t.jsx)(Q,{apps:[],selectedCategory:E,mode:j,isLoading:ea,currentAgentId:er,agent:ei,agentPipedreamProfiles:ed,onAppSelected:f,onConnectApp:ef,onConfigureTools:ej,onCategorySelect:eh}):(0,t.jsx)(H,{selectedCategory:E,mode:j,onClearFilters:()=>{P(""),I("All"),eg()}})})]})})}),(null==ex?void 0:ex.apps)&&ex.apps.length>0&&(D.length>0||(null==es||null==(s=es.page_info)?void 0:s.has_more))&&(!N||er)&&(0,t.jsx)(Z,{isLoading:ea,paginationHistory:D,hasMore:(null==es||null==(a=es.page_info)?void 0:a.has_more)||!1,onPrevPage:()=>{if(D.length>0){let e=[...D],s=e.pop();L(e),s===C.FIRST_PAGE?M(void 0):M(s)}},onNextPage:()=>{var e;(null==es||null==(e=es.page_info)?void 0:e.end_cursor)&&(F?L(e=>[...e,F]):L(e=>[...e,C.FIRST_PAGE]),M(es.page_info.end_cursor))}})]}),K&&(0,t.jsx)(m.R,{app:K,open:z,onOpenChange:q,onComplete:(e,s,a,t)=>{g&&(g(e,s,a,t),x.oR.success("Added ".concat(s.length," tools from ").concat(a,"!")))},mode:"profile-only"===j?"profile-only":"full"}),X&&er&&(0,t.jsx)(u.n,{mode:"pipedream",agentId:er,profileId:X.profileId,appName:X.appName,profileName:X.profileName,open:G,onOpenChange:e=>{W(e),e||J(null)},onToolsUpdate:e=>{ee.invalidateQueries({queryKey:["agent",er]})}})]})}},46102:(e,s,a)=>{a.d(s,{Bc:()=>r,ZI:()=>c,k$:()=>o,m_:()=>i});var t=a(95155);a(12115);var l=a(89613),n=a(59434);function r(e){let{delayDuration:s=0,...a}=e;return(0,t.jsx)(l.Kq,{"data-slot":"tooltip-provider",delayDuration:s,...a})}function i(e){let{...s}=e;return(0,t.jsx)(r,{children:(0,t.jsx)(l.bL,{"data-slot":"tooltip",...s})})}function o(e){let{...s}=e;return(0,t.jsx)(l.l9,{"data-slot":"tooltip-trigger",...s})}function c(e){let{className:s,sideOffset:a=0,children:r,...i}=e;return(0,t.jsx)(l.ZL,{children:(0,t.jsxs)(l.UC,{"data-slot":"tooltip-content",sideOffset:a,className:(0,n.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",s),...i,children:[r,(0,t.jsx)(l.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},48470:(e,s,a)=>{a.d(s,{b:()=>v});var t=a(95155),l=a(12115),n=a(25657),r=a(381),i=a(5196),o=a(66474),c=a(47924),d=a(84616),m=a(66766),u=a(30285),x=a(44838),p=a(46102),h=a(38198),g=a(35695),f=a(59434);let j=[],v=e=>{let{selectedAgentId:s,onAgentSelect:a,disabled:v=!1}=e,[N,y]=(0,l.useState)(!1),[b,w]=(0,l.useState)(""),[C,A]=(0,l.useState)(-1),_=(0,l.useRef)(null),k=(0,g.useRouter)(),{data:S,isLoading:P}=(0,h._F)(),E=(null==S?void 0:S.agents)||[],I=(0,h.h3)(),T=[{id:void 0,name:"Suna",description:"Your personal AI assistant",type:"default",icon:(0,t.jsx)(m.default,{src:"/kortix-symbol.svg",alt:"Suna",width:16,height:16,className:"h-4 w-4 dark:invert"})},...j.map(e=>({...e,type:"predefined"})),...E.map(e=>({...e,id:e.agent_id,type:"custom",icon:e.avatar||(0,t.jsx)(n.A,{className:"h-4 w-4"})}))],F=T.filter(e=>{var s;return e.name.toLowerCase().includes(b.toLowerCase())||(null==(s=e.description)?void 0:s.toLowerCase().includes(b.toLowerCase()))});(0,l.useEffect)(()=>{N&&_.current?setTimeout(()=>{var e;null==(e=_.current)||e.focus()},50):(w(""),A(-1))},[N]);let M=e=>{console.log("Agent selected:",void 0===e?"Suna (default)":e),null==a||a(e),y(!1)},D=(e,s)=>{s.stopPropagation(),y(!1),k.push("/agents/config/".concat(e))},L=(e,a)=>{let l=e.id===s,n="custom"===e.type&&e.id;return(0,t.jsx)(p.Bc,{children:(0,t.jsxs)(p.m_,{children:[(0,t.jsx)(p.k$,{asChild:!0,children:(0,t.jsxs)(x._2,{className:(0,f.cn)("flex items-center rounded-xl gap-3 px-4 py-2.5 cursor-pointer hover:bg-accent/40 transition-colors duration-200 group",a===C&&"bg-accent/40"),onClick:()=>M(e.id),onMouseEnter:()=>A(a),children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:e.icon}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("div",{className:"flex items-center gap-2",children:(0,t.jsx)("span",{className:"font-medium text-sm text-foreground/90 truncate",children:e.name})}),(0,t.jsx)("span",{className:"text-xs text-muted-foreground/80 truncate leading-relaxed",children:(0,f.W5)(e.description,30)})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 flex-shrink-0",children:[n&&(0,t.jsx)(u.$,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0 hover:bg-muted/60 rounded-full opacity-0 group-hover:opacity-70 transition-opacity duration-200",onClick:s=>D(e.id,s),children:(0,t.jsx)(r.A,{className:"h-3 w-3"})}),l&&(0,t.jsx)("div",{className:"h-6 w-6 rounded-full bg-blue-500/10 flex items-center justify-center",children:(0,t.jsx)(i.A,{className:"h-3 w-3 text-blue-600/80"})})]})]})}),(0,t.jsx)(p.ZI,{side:"left",className:"text-xs max-w-xs",children:(0,t.jsx)("p",{className:"truncate",children:(0,f.W5)(e.description,35)})})]})},e.id||"default")},$=(()=>{let e=T.find(e=>e.id===s);if(e)return console.log("Selected agent found:",e.name,"with ID:",e.id),{name:e.name,icon:e.icon};void 0!==s&&console.warn("Agent with ID",s,"not found, falling back to Suna");let a=T[0];return console.log("Using default agent:",a.name),{name:a.name,icon:a.icon}})();return(0,t.jsx)(t.Fragment,{children:(0,t.jsxs)(x.rI,{open:N,onOpenChange:y,children:[(0,t.jsx)(p.Bc,{children:(0,t.jsxs)(p.m_,{children:[(0,t.jsx)(p.k$,{asChild:!0,children:(0,t.jsx)(x.ty,{asChild:!0,children:(0,t.jsx)(u.$,{variant:"ghost",size:"sm",className:(0,f.cn)("px-2.5 py-1.5 text-sm font-normal hover:bg-accent/40 transition-all duration-200 rounded-xl","focus:ring-1 focus:ring-ring focus:ring-offset-1 focus:outline-none",N&&"bg-accent/40"),disabled:v,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:$.icon}),(0,t.jsx)("span",{className:"hidden sm:inline-block truncate max-w-[80px] font-normal",children:$.name}),(0,t.jsx)(o.A,{size:12,className:(0,f.cn)("opacity-50 transition-transform duration-200",N&&"rotate-180")})]})})})}),(0,t.jsx)(p.ZI,{children:(0,t.jsx)("p",{children:"Select Agent"})})]})}),(0,t.jsxs)(x.SQ,{align:"end",className:"w-88 p-0 border-0 shadow-md bg-card/98 backdrop-blur-sm",sideOffset:6,style:{borderRadius:"20px"},children:[(0,t.jsx)("div",{className:"p-4 pb-3",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(c.A,{className:"absolute left-3 top-2.5 h-3.5 w-3.5 text-muted-foreground/60"}),(0,t.jsx)("input",{ref:_,type:"text",placeholder:"Search agents...",value:b,onChange:e=>w(e.target.value),onKeyDown:e=>{if(e.stopPropagation(),"ArrowDown"===e.key)e.preventDefault(),A(e=>e<F.length-1?e+1:0);else if("ArrowUp"===e.key)e.preventDefault(),A(e=>e>0?e-1:F.length-1);else if("Enter"===e.key&&C>=0){e.preventDefault();let s=F[C];s&&M(s.id)}},className:(0,f.cn)("w-full pl-10 pr-3 py-2 text-sm bg-muted/40 border-0 rounded-xl","focus:outline-none focus:ring-1 focus:ring-ring focus:ring-offset-0 focus:bg-muted/60","placeholder:text-muted-foreground/60 transition-all duration-200")})]})}),(0,t.jsx)("div",{className:"max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-muted-foreground/20 scrollbar-track-transparent px-1.5",children:P?(0,t.jsx)("div",{className:"px-4 py-6 text-sm text-muted-foreground/70 text-center",children:(0,t.jsx)("div",{className:"animate-pulse",children:"Loading agents..."})}):0===F.length?(0,t.jsxs)("div",{className:"px-4 py-6 text-sm text-muted-foreground/70 text-center",children:[(0,t.jsx)(c.A,{className:"h-6 w-6 mx-auto mb-2 opacity-40"}),(0,t.jsx)("p",{children:"No agents found"}),(0,t.jsx)("p",{className:"text-xs mt-1 opacity-60",children:"Try adjusting your search"})]}):(0,t.jsx)("div",{className:"space-y-0.5",children:F.map((e,s)=>L(e,s))})}),(0,t.jsx)("div",{className:"p-4 pt-3 border-t border-border/40",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,t.jsxs)(u.$,{variant:"ghost",size:"sm",onClick:()=>{y(!1),k.push("/agents")},className:"text-xs flex items-center gap-2 rounded-xl hover:bg-accent/40 transition-all duration-200 text-muted-foreground hover:text-foreground px-4 py-2",children:[(0,t.jsx)(c.A,{className:"h-3.5 w-3.5"}),"Explore All Agents"]}),(0,t.jsx)("div",{className:"w-px h-4 bg-border/60"}),(0,t.jsxs)(u.$,{variant:"ghost",size:"sm",onClick:()=>{y(!1),I.mutate()},className:"text-xs flex items-center gap-2 rounded-xl hover:bg-accent/40 transition-all duration-200 text-muted-foreground hover:text-foreground px-4 py-2",children:[(0,t.jsx)(d.A,{className:"h-3.5 w-3.5"}),"Create Agent"]})]})})]})]})})}},71319:(e,s,a)=>{a.d(s,{n:()=>P});var t=a(95155),l=a(12115),n=a(30285),r=a(80333),i=a(66695),o=a(26126),c=a(55365),d=a(54165),m=a(54861),u=a(53904),x=a(71539),p=a(51154),h=a(81284),g=a(43453),f=a(4229),j=a(59434),v=a(28755),N=a(26715),y=a(5041),b=a(56671),w=a(90697),C=a(58350);let A=(e,s)=>(0,v.I)({queryKey:["pipedream-tools",e,s],queryFn:async()=>(await w.Hv.get("/agents/".concat(e,"/pipedream-tools/").concat(s))).data,enabled:!!e&&!!s,staleTime:3e5,retry:(e,s)=>{var a,t;return(null==s||null==(a=s.response)?void 0:a.status)!==404&&(null==s||null==(t=s.response)?void 0:t.status)!==400&&e<2}}),_=()=>{let e=(0,N.jE)();return(0,y.n)({mutationFn:async e=>{let{agentId:s,profileId:a,enabledTools:t}=e;return(await w.Hv.put("/agents/".concat(s,"/pipedream-tools/").concat(a),{enabled_tools:t})).data},onSuccess:(s,a)=>{e.invalidateQueries({queryKey:["pipedream-tools",a.agentId,a.profileId]}),e.invalidateQueries({queryKey:C._.detail(a.agentId)}),e.invalidateQueries({queryKey:["agent-tools",a.agentId]})},onError:e=>{var s,a;let t=(null==e||null==(a=e.response)||null==(s=a.data)?void 0:s.detail)||"Failed to update Pipedream tools";b.oR.error(t)}})},k=(e,s)=>{let{data:a,isLoading:t,error:l,refetch:n}=A(e,s),r=_();return{data:a,isLoading:t,error:l,refetch:n,handleUpdateTools:a=>{r.mutate({agentId:e,profileId:s,enabledTools:a})},isUpdating:r.isPending}},S=(e,s)=>{let a=(0,N.jE)(),{data:t,isLoading:l,error:n,refetch:r}=(0,v.I)({queryKey:["custom-mcp-tools",e,null==s?void 0:s.url],queryFn:async()=>{let a=await w.Hv.get("/agents/".concat(e,"/custom-mcp-tools"),{headers:{"X-MCP-URL":s.url,"X-MCP-Type":s.type||"sse",...s.headers?{"X-MCP-Headers":JSON.stringify(s.headers)}:{}}});if(!a.success){var t;throw Error((null==(t=a.error)?void 0:t.message)||"Failed to fetch custom MCP tools")}return a.data},enabled:!!e&&!!(null==s?void 0:s.url),staleTime:3e5}),i=(0,y.n)({mutationFn:async a=>{let t=await w.Hv.post("/agents/".concat(e,"/custom-mcp-tools"),{url:s.url,type:s.type||"sse",enabled_tools:a});if(!t.success){var l;throw Error((null==(l=t.error)?void 0:l.message)||"Failed to update custom MCP tools")}return t.data},onSuccess:()=>{a.invalidateQueries({queryKey:["custom-mcp-tools",e]}),a.invalidateQueries({queryKey:C._.detail(e)}),a.invalidateQueries({queryKey:["agent-tools",e]})}});return{data:t,isLoading:l,error:n,refetch:r,handleUpdateTools:e=>i.mutate(e),isUpdating:i.isPending}},P=e=>{var s,a,v;let{agentId:N,open:y,onOpenChange:b,onToolsUpdate:w,mode:C}=e,A=k("pipedream"===C?N:"","pipedream"===C?e.profileId:""),_=S("custom"===C?N:"","custom"===C?e.mcpConfig:null),{data:P,isLoading:E,error:I,handleUpdateTools:T,isUpdating:F,refetch:M}="pipedream"===C?A:_,[D,L]=(0,l.useState)({}),[$,z]=(0,l.useState)(!1);l.useEffect(()=>{if(null==P?void 0:P.tools){let e={};P.tools.forEach(s=>{e[s.name]=s.enabled}),L(e),z(!1)}},[P]);let R=(0,l.useMemo)(()=>Object.values(D).filter(Boolean).length,[D]),q=(null==P||null==(s=P.tools)?void 0:s.length)||0,K="pipedream"===C?e.appName:e.mcpName,O="pipedream"===C?e.profileName||"Profile":"Server",Q=e=>{L(s=>{let a=!s[e],t={...s,[e]:a},l={};if(null==P?void 0:P.tools)for(let e of P.tools)l[e.name]=e.enabled;return z(Object.keys(t).some(e=>t[e]!==l[e])),t})};return I?(0,t.jsx)(d.lG,{open:y,onOpenChange:b,children:(0,t.jsxs)(d.Cf,{className:"sm:max-w-lg",children:[(0,t.jsxs)(d.c7,{children:[(0,t.jsxs)(d.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(m.A,{className:"h-5 w-5 text-destructive"}),"Error Loading Tools"]}),(0,t.jsxs)(d.rr,{children:["Failed to load ",K," tools"]})]}),(0,t.jsxs)(c.Fc,{variant:"destructive",children:[(0,t.jsx)(m.A,{className:"h-4 w-4"}),(0,t.jsx)(c.TN,{children:(null==I?void 0:I.message)||"An unexpected error occurred while loading tools."})]}),(0,t.jsxs)(d.Es,{children:[(0,t.jsx)(n.$,{variant:"outline",onClick:()=>b(!1),children:"Close"}),(0,t.jsxs)(n.$,{onClick:()=>M(),children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Retry"]})]})]})}):(0,t.jsx)(d.lG,{open:y,onOpenChange:b,children:(0,t.jsxs)(d.Cf,{className:"sm:max-w-2xl max-h-[80vh] overflow-hidden flex flex-col",children:[(0,t.jsxs)(d.c7,{children:[(0,t.jsxs)(d.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 text-primary"}),"Configure ",K," Tools"]}),(0,t.jsxs)(d.rr,{children:["Choose which ",K," tools are available to your agent"]})]}),(0,t.jsx)("div",{className:"flex-1 overflow-hidden flex flex-col",children:E?(0,t.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(p.A,{className:"h-5 w-5 animate-spin"}),(0,t.jsx)("span",{children:"Loading available tools..."})]})}):(null==P||null==(a=P.tools)?void 0:a.length)?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"flex items-center justify-between pb-4",children:[(0,t.jsx)("div",{className:"flex items-center gap-3",children:(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)("span",{className:"text-sm font-medium",children:[R," of ",q," tools enabled"]}),$&&(0,t.jsx)(o.E,{className:"text-xs bg-primary/10 text-primary",children:"Unsaved changes"})]}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:[O,": ","pipedream"===C?e.profileName:K]})]})}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>{if(!(null==P?void 0:P.tools))return;let e=P.tools.every(e=>!!D[e.name]),s={};P.tools.forEach(a=>{s[a.name]=!e}),L(s),z(!0)},disabled:F,children:P.tools.every(e=>D[e.name])?"Deselect All":"Select All"})]}),(0,t.jsx)("div",{className:"flex-1 overflow-y-auto space-y-3",children:P.tools.map(e=>(0,t.jsx)(i.Zp,{className:(0,j.cn)("transition-colors cursor-pointer",D[e.name]?"bg-muted/50 border-primary/40":"hover:bg-muted/20"),onClick:()=>Q(e.name),children:(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,t.jsx)("div",{className:"flex-1 min-w-0",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("h4",{className:"font-medium text-sm",children:e.name}),D[e.name]&&(0,t.jsx)(g.A,{className:"h-4 w-4 text-green-500"})]})}),(0,t.jsx)(r.d,{checked:D[e.name]||!1,onCheckedChange:()=>Q(e.name),onClick:e=>e.stopPropagation(),disabled:F})]})})},e.name))})]}):(0,t.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(h.A,{className:"h-8 w-8 text-muted-foreground mx-auto mb-2"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["No tools available for this ",K," ","pipedream"===C?"profile":"server"]})]})})}),(0,t.jsx)(d.Es,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsx)("div",{className:"flex items-center gap-2",children:!(null==P?void 0:P.has_mcp_config)&&(null==P||null==(v=P.tools)?void 0:v.length)>0&&(0,t.jsxs)(c.Fc,{className:"p-2",children:[(0,t.jsx)(h.A,{className:"h-3 w-3"}),(0,t.jsxs)(c.TN,{className:"text-xs",children:["This will ","pipedream"===C?"create a new":"update the"," MCP configuration for your agent"]})]})}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(n.$,{variant:"outline",onClick:$?()=>{if(null==P?void 0:P.tools){let e={};P.tools.forEach(s=>{e[s.name]=s.enabled}),L(e),z(!1)}}:()=>b(!1),disabled:F,children:$?"Cancel":"Close"}),$&&(0,t.jsx)(n.$,{onClick:()=>{let e=Object.entries(D).filter(e=>{let[s,a]=e;return a}).map(e=>{let[s]=e;return s});T(e),z(!1),w&&w(e)},disabled:F,children:F?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Saving..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),"Save Changes"]})})]})]})})]})})}}}]);