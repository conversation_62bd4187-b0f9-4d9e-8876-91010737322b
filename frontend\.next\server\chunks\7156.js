"use strict";exports.id=7156,exports.ids=[7156],exports.modules={363:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(62688).A)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},2822:(t,e,i)=>{i.d(e,{L:()=>n});let n=(0,i(43210).createContext)({})},4486:(t,e,i)=>{i.d(e,{Y:()=>s});var n=i(38932);function s(t,e,i){let s=Math.max(e-5,0);return(0,n.f)(i-t(s),e-s)}},5077:(t,e,i)=>{let n;i.d(e,{Ay:()=>tn});var s=i(43210),r=i(57379),a=Object.prototype.hasOwnProperty;let o=new WeakMap,l=()=>{},u=l(),h=Object,d=t=>t===u,c=t=>"function"==typeof t,p=(t,e)=>({...t,...e}),m=t=>c(t.then),f={},y={},g="undefined",v=typeof document!=g,x=!1,b=()=>!1,w=(t,e)=>{let i=o.get(t);return[()=>!d(e)&&t.get(e)||f,n=>{if(!d(e)){let s=t.get(e);e in y||(y[e]=s),i[5](e,p(s,n),s||f)}},i[6],()=>!d(e)&&e in y?y[e]:!d(e)&&t.get(e)||f]},T=!0,[S,P]=[l,l],A={initFocus:t=>(v&&document.addEventListener("visibilitychange",t),S("focus",t),()=>{v&&document.removeEventListener("visibilitychange",t),P("focus",t)}),initReconnect:t=>{let e=()=>{T=!0,t()},i=()=>{T=!1};return S("online",e),S("offline",i),()=>{P("online",e),P("offline",i)}}},k=!s.useId,M=!0,E=t=>b()?window.requestAnimationFrame(t):setTimeout(t,1),V=M?s.useEffect:s.useLayoutEffect,D="undefined"!=typeof navigator&&navigator.connection,R=!M&&D&&(["slow-2g","2g"].includes(D.effectiveType)||D.saveData),C=new WeakMap,j=(t,e)=>h.prototype.toString.call(t)===`[object ${e}]`,L=0,O=t=>{let e,i,n=typeof t,s=j(t,"Date"),r=j(t,"RegExp"),a=j(t,"Object");if(h(t)!==t||s||r)e=s?t.toJSON():"symbol"==n?t.toString():"string"==n?JSON.stringify(t):""+t;else{if(e=C.get(t))return e;if(e=++L+"~",C.set(t,e),Array.isArray(t)){for(i=0,e="@";i<t.length;i++)e+=O(t[i])+",";C.set(t,e)}if(a){e="#";let n=h.keys(t).sort();for(;!d(i=n.pop());)d(t[i])||(e+=i+":"+O(t[i])+",");C.set(t,e)}}return e},B=t=>{if(c(t))try{t=t()}catch(e){t=""}let e=t;return[t="string"==typeof t?t:(Array.isArray(t)?t.length:t)?O(t):"",e]},F=0,I=()=>++F;async function U(...t){let[e,i,n,s]=t,r=p({populateCache:!0,throwOnError:!0},"boolean"==typeof s?{revalidate:s}:s||{}),a=r.populateCache,l=r.rollbackOnError,h=r.optimisticData,f=t=>"function"==typeof l?l(t):!1!==l,y=r.throwOnError;if(c(i)){let t=[];for(let n of e.keys())!/^\$(inf|sub)\$/.test(n)&&i(e.get(n)._k)&&t.push(n);return Promise.all(t.map(g))}return g(i);async function g(i){let s,[l]=B(i);if(!l)return;let[p,g]=w(e,l),[v,x,b,T]=o.get(e),S=()=>{let t=v[l];return(c(r.revalidate)?r.revalidate(p().data,i):!1!==r.revalidate)&&(delete b[l],delete T[l],t&&t[0])?t[0](2).then(()=>p().data):p().data};if(t.length<3)return S();let P=n,A=I();x[l]=[A,0];let k=!d(h),M=p(),E=M.data,V=M._c,D=d(V)?E:V;if(k&&g({data:h=c(h)?h(D,E):h,_c:D}),c(P))try{P=P(D)}catch(t){s=t}if(P&&m(P)){if(P=await P.catch(t=>{s=t}),A!==x[l][0]){if(s)throw s;return P}s&&k&&f(s)&&(a=!0,g({data:D,_c:u}))}if(a&&!s&&(c(a)?g({data:a(P,D),error:u,_c:u}):g({data:P,error:u,_c:u})),x[l][1]=I(),Promise.resolve(S()).then(()=>{g({_c:u})}),s){if(y)throw s;return}return P}}let N=(t,e)=>{for(let i in t)t[i][0]&&t[i][0](e)},$=(t,e)=>{if(!o.has(t)){let i=p(A,e),n=Object.create(null),s=U.bind(u,t),r=l,a=Object.create(null),h=(t,e)=>{let i=a[t]||[];return a[t]=i,i.push(e),()=>i.splice(i.indexOf(e),1)},d=(e,i,n)=>{t.set(e,i);let s=a[e];if(s)for(let t of s)t(i,n)},c=()=>{if(!o.has(t)&&(o.set(t,[n,Object.create(null),Object.create(null),Object.create(null),s,d,h]),!M)){let e=i.initFocus(setTimeout.bind(u,N.bind(u,n,0))),s=i.initReconnect(setTimeout.bind(u,N.bind(u,n,1)));r=()=>{e&&e(),s&&s(),o.delete(t)}}};return c(),[t,s,c,r]}return[t,o.get(t)[4]]},[W,G]=$(new Map),q=p({onLoadingSlow:l,onSuccess:l,onError:l,onErrorRetry:(t,e,i,n,s)=>{let r=i.errorRetryCount,a=s.retryCount,o=~~((Math.random()+.5)*(1<<(a<8?a:8)))*i.errorRetryInterval;(d(r)||!(a>r))&&setTimeout(n,o,s)},onDiscarded:l,revalidateOnFocus:!0,revalidateOnReconnect:!0,revalidateIfStale:!0,shouldRetryOnError:!0,errorRetryInterval:R?1e4:5e3,focusThrottleInterval:5e3,dedupingInterval:2e3,loadingTimeout:R?5e3:3e3,compare:function t(e,i){var n,s;if(e===i)return!0;if(e&&i&&(n=e.constructor)===i.constructor){if(n===Date)return e.getTime()===i.getTime();if(n===RegExp)return e.toString()===i.toString();if(n===Array){if((s=e.length)===i.length)for(;s--&&t(e[s],i[s]););return -1===s}if(!n||"object"==typeof e){for(n in s=0,e)if(a.call(e,n)&&++s&&!a.call(i,n)||!(n in i)||!t(e[n],i[n]))return!1;return Object.keys(i).length===s}}return e!=e&&i!=i},isPaused:()=>!1,cache:W,mutate:G,fallback:{}},{isOnline:()=>T,isVisible:()=>{let t=v&&document.visibilityState;return d(t)||"hidden"!==t}}),X=(t,e)=>{let i=p(t,e);if(e){let{use:n,fallback:s}=t,{use:r,fallback:a}=e;n&&r&&(i.use=n.concat(r)),s&&a&&(i.fallback=p(s,a))}return i},Y=(0,s.createContext)({}),z=!1,H=z?window.__SWR_DEVTOOLS_USE__:[],K=t=>c(t[1])?[t[0],t[1],t[2]||{}]:[t[0],null,(null===t[1]?t[2]:t[1])||{}],Q=()=>p(q,(0,s.useContext)(Y)),_=H.concat(t=>(e,i,n)=>{let s=i&&((...t)=>{let[n]=B(e),[,,,s]=o.get(W);if(n.startsWith("$inf$"))return i(...t);let r=s[n];return d(r)?i(...t):(delete s[n],r)});return t(e,s,n)}),Z=(t,e,i)=>{let n=e[t]||(e[t]=[]);return n.push(i),()=>{let t=n.indexOf(i);t>=0&&(n[t]=n[n.length-1],n.pop())}};z&&(window.__SWR_DEVTOOLS_REACT__=s);let J=()=>{},tt=J();new WeakMap;let te=s.use||(t=>{switch(t.status){case"pending":throw t;case"fulfilled":return t.value;case"rejected":throw t.reason;default:throw t.status="pending",t.then(e=>{t.status="fulfilled",t.value=e},e=>{t.status="rejected",t.reason=e}),t}}),ti={dedupe:!0};h.defineProperty(t=>{let{value:e}=t,i=(0,s.useContext)(Y),n=c(e),r=(0,s.useMemo)(()=>n?e(i):e,[n,i,e]),a=(0,s.useMemo)(()=>n?r:X(i,r),[n,i,r]),o=r&&r.provider,l=(0,s.useRef)(u);o&&!l.current&&(l.current=$(o(a.cache||W),r));let h=l.current;return h&&(a.cache=h[0],a.mutate=h[1]),V(()=>{if(h)return h[2]&&h[2](),h[3]},[]),(0,s.createElement)(Y.Provider,p(t,{value:a}))},"defaultValue",{value:q});let tn=(n=(t,e,i)=>{let{cache:n,compare:a,suspense:l,fallbackData:h,revalidateOnMount:f,revalidateIfStale:y,refreshInterval:g,refreshWhenHidden:v,refreshWhenOffline:x,keepPreviousData:b}=i,[T,S,P,A]=o.get(n),[D,R]=B(t),C=(0,s.useRef)(!1),j=(0,s.useRef)(!1),L=(0,s.useRef)(D),O=(0,s.useRef)(e),F=(0,s.useRef)(i),N=()=>F.current,$=()=>N().isVisible()&&N().isOnline(),[W,G,q,X]=w(n,D),Y=(0,s.useRef)({}).current,z=d(h)?d(i.fallback)?u:i.fallback[D]:h,H=(t,e)=>{for(let i in Y)if("data"===i){if(!a(t[i],e[i])&&(!d(t[i])||!a(ta,e[i])))return!1}else if(e[i]!==t[i])return!1;return!0},K=(0,s.useMemo)(()=>{let t=!!D&&!!e&&(d(f)?!N().isPaused()&&!l&&!1!==y:f),i=e=>{let i=p(e);return(delete i._k,t)?{isValidating:!0,isLoading:!0,...i}:i},n=W(),s=X(),r=i(n),a=n===s?r:i(s),o=r;return[()=>{let t=i(W());return H(t,o)?(o.data=t.data,o.isLoading=t.isLoading,o.isValidating=t.isValidating,o.error=t.error,o):(o=t,t)},()=>a]},[n,D]),Q=(0,r.useSyncExternalStore)((0,s.useCallback)(t=>q(D,(e,i)=>{H(i,e)||t()}),[n,D]),K[0],K[1]),_=!C.current,J=T[D]&&T[D].length>0,tt=Q.data,tn=d(tt)?z&&m(z)?te(z):z:tt,ts=Q.error,tr=(0,s.useRef)(tn),ta=b?d(tt)?d(tr.current)?tn:tr.current:tt:tn,to=(!J||!!d(ts))&&(_&&!d(f)?f:!N().isPaused()&&(l?!d(tn)&&y:d(tn)||y)),tl=!!(D&&e&&_&&to),tu=d(Q.isValidating)?tl:Q.isValidating,th=d(Q.isLoading)?tl:Q.isLoading,td=(0,s.useCallback)(async t=>{let e,n,s=O.current;if(!D||!s||j.current||N().isPaused())return!1;let r=!0,o=t||{},l=!P[D]||!o.dedupe,h=()=>k?!j.current&&D===L.current&&C.current:D===L.current,p={isValidating:!1,isLoading:!1},m=()=>{G(p)},f=()=>{let t=P[D];t&&t[1]===n&&delete P[D]},y={isValidating:!0};d(W().data)&&(y.isLoading=!0);try{if(l&&(G(y),i.loadingTimeout&&d(W().data)&&setTimeout(()=>{r&&h()&&N().onLoadingSlow(D,i)},i.loadingTimeout),P[D]=[s(R),I()]),[e,n]=P[D],e=await e,l&&setTimeout(f,i.dedupingInterval),!P[D]||P[D][1]!==n)return l&&h()&&N().onDiscarded(D),!1;p.error=u;let t=S[D];if(!d(t)&&(n<=t[0]||n<=t[1]||0===t[1]))return m(),l&&h()&&N().onDiscarded(D),!1;let o=W().data;p.data=a(o,e)?o:e,l&&h()&&N().onSuccess(e,D,i)}catch(i){f();let t=N(),{shouldRetryOnError:e}=t;!t.isPaused()&&(p.error=i,l&&h()&&(t.onError(i,D,t),(!0===e||c(e)&&e(i))&&(!N().revalidateOnFocus||!N().revalidateOnReconnect||$())&&t.onErrorRetry(i,D,t,t=>{let e=T[D];e&&e[0]&&e[0](3,t)},{retryCount:(o.retryCount||0)+1,dedupe:!0})))}return r=!1,m(),!0},[D,n]),tc=(0,s.useCallback)((...t)=>U(n,L.current,...t),[]);if(V(()=>{O.current=e,F.current=i,d(tt)||(tr.current=tt)}),V(()=>{if(!D)return;let t=td.bind(u,ti),e=0;N().revalidateOnFocus&&(e=Date.now()+N().focusThrottleInterval);let i=Z(D,T,(i,n={})=>{if(0==i){let i=Date.now();N().revalidateOnFocus&&i>e&&$()&&(e=i+N().focusThrottleInterval,t())}else if(1==i)N().revalidateOnReconnect&&$()&&t();else if(2==i)return td();else if(3==i)return td(n)});return j.current=!1,L.current=D,C.current=!0,G({_k:R}),to&&(d(tn)||M?t():E(t)),()=>{j.current=!0,i()}},[D]),V(()=>{let t;function e(){let e=c(g)?g(W().data):g;e&&-1!==t&&(t=setTimeout(i,e))}function i(){!W().error&&(v||N().isVisible())&&(x||N().isOnline())?td(ti).then(e):e()}return e(),()=>{t&&(clearTimeout(t),t=-1)}},[g,v,x,D]),(0,s.useDebugValue)(ta),l&&d(tn)&&D){if(!k&&M)throw Error("Fallback data is required when using Suspense in SSR.");O.current=e,F.current=i,j.current=!1;let t=A[D];if(d(t)||te(tc(t)),d(ts)){let t=td(ti);d(ta)||(t.status="fulfilled",t.value=!0),te(t)}else throw ts}return{mutate:tc,get data(){return Y.data=!0,ta},get error(){return Y.error=!0,ts},get isValidating(){return Y.isValidating=!0,tu},get isLoading(){return Y.isLoading=!0,th}}},function(...t){let e=Q(),[i,s,r]=K(t),a=X(e,r),o=n,{use:l}=a,u=(l||[]).concat(_);for(let t=u.length;t--;)o=u[t](o);return o(i,s||a.fetcher||null,a)})},6402:(t,e,i)=>{i.d(e,{s:()=>v});var n=i(18234),s=i(99275),r=i(64008),a=i(81480);let o=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>a.Gt.update(e,t),stop:()=>(0,a.WG)(e),now:()=>a.uv.isProcessing?a.uv.timestamp:n.k.now()}};var l=i(55893),u=i(94117),h=i(44257),d=i(35797),c=i(61611),p=i(33081),m=i(98148),f=i(99848),y=i(54581);let g=t=>t/100;class v extends p.q{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==n.k.now()&&this.tick(n.k.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},s.q.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;(0,c.E)(t);let{type:e=u.i,repeat:i=0,repeatDelay:n=0,repeatType:s,velocity:a=0}=t,{keyframes:o}=t,l=e||u.i;l!==u.i&&"number"!=typeof o[0]&&(this.mixKeyframes=(0,m.F)(g,(0,r.j)(o[0],o[1])),o=[0,100]);let d=l({...t,keyframes:o});"mirror"===s&&(this.mirroredGenerator=l({...t,keyframes:[...o].reverse(),velocity:-a})),null===d.calculatedDuration&&(d.calculatedDuration=(0,h.t)(d));let{calculatedDuration:p}=d;this.calculatedDuration=p,this.resolvedDuration=p+n,this.totalDuration=this.resolvedDuration*(i+1)-n,this.generator=d}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:s,mirroredGenerator:r,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:u=0,keyframes:h,repeat:c,repeatType:p,repeatDelay:m,type:f,onUpdate:g,finalKeyframe:v}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let x=this.currentTime-u*(this.playbackSpeed>=0?1:-1),b=this.playbackSpeed>=0?x<0:x>n;this.currentTime=Math.max(x,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let w=this.currentTime,T=i;if(c){let t=Math.min(this.currentTime,n)/a,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,c+1))%2&&("reverse"===p?(i=1-i,m&&(i-=m/a)):"mirror"===p&&(T=r)),w=(0,y.q)(0,1,i)*a}let S=b?{done:!1,value:h[0]}:T.next(w);s&&(S.value=s(S.value));let{done:P}=S;b||null===o||(P=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let A=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return A&&f!==l.B&&(S.value=(0,d.X)(h,this.options,v,this.speed)),g&&g(S.value),A&&this.finish(),S}then(t,e){return this.finished.then(t,e)}get duration(){return(0,f.X)(this.calculatedDuration)}get time(){return(0,f.X)(this.currentTime)}set time(t){t=(0,f.f)(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(n.k.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=(0,f.X)(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=o,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(n.k.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,s.q.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}},12179:(t,e,i)=>{i.d(e,{o:()=>m});var n=i(51002),s=i(44257),r=i(99848),a=i(4486);let o={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};var l=i(537),u=i(54581);function h(t,e){return t*Math.sqrt(1-e*e)}let d=["duration","bounce"],c=["stiffness","damping","mass"];function p(t,e){return e.some(e=>void 0!==t[e])}function m(t=o.visualDuration,e=o.bounce){let i,f="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:y,restDelta:g}=f,v=f.keyframes[0],x=f.keyframes[f.keyframes.length-1],b={done:!1,value:v},{stiffness:w,damping:T,mass:S,duration:P,velocity:A,isResolvedFromDuration:k}=function(t){let e={velocity:o.velocity,stiffness:o.stiffness,damping:o.damping,mass:o.mass,isResolvedFromDuration:!1,...t};if(!p(t,c)&&p(t,d))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,s=2*(0,u.q)(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:o.mass,stiffness:n,damping:s}}else{let i=function({duration:t=o.duration,bounce:e=o.bounce,velocity:i=o.velocity,mass:n=o.mass}){let s,a;(0,l.$)(t<=(0,r.f)(o.maxDuration),"Spring duration must be 10 seconds or less");let d=1-e;d=(0,u.q)(o.minDamping,o.maxDamping,d),t=(0,u.q)(o.minDuration,o.maxDuration,(0,r.X)(t)),d<1?(s=e=>{let n=e*d,s=n*t;return .001-(n-i)/h(e,d)*Math.exp(-s)},a=e=>{let n=e*d*t,r=Math.pow(d,2)*Math.pow(e,2)*t,a=Math.exp(-n),o=h(Math.pow(e,2),d);return(n*i+i-r)*a*(-s(e)+.001>0?-1:1)/o}):(s=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),a=e=>t*t*(i-e)*Math.exp(-e*t));let c=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(s,a,5/t);if(t=(0,r.f)(t),isNaN(c))return{stiffness:o.stiffness,damping:o.damping,duration:t};{let e=Math.pow(c,2)*n;return{stiffness:e,damping:2*d*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:o.mass}).isResolvedFromDuration=!0}return e}({...f,velocity:-(0,r.X)(f.velocity||0)}),M=A||0,E=T/(2*Math.sqrt(w*S)),V=x-v,D=(0,r.X)(Math.sqrt(w/S)),R=5>Math.abs(V);if(y||(y=R?o.restSpeed.granular:o.restSpeed.default),g||(g=R?o.restDelta.granular:o.restDelta.default),E<1){let t=h(D,E);i=e=>x-Math.exp(-E*D*e)*((M+E*D*V)/t*Math.sin(t*e)+V*Math.cos(t*e))}else if(1===E)i=t=>x-Math.exp(-D*t)*(V+(M+D*V)*t);else{let t=D*Math.sqrt(E*E-1);i=e=>{let i=Math.exp(-E*D*e),n=Math.min(t*e,300);return x-i*((M+E*D*V)*Math.sinh(n)+t*V*Math.cosh(n))/t}}let C={calculatedDuration:k&&P||null,next:t=>{let e=i(t);if(k)b.done=t>=P;else{let n=0===t?M:0;E<1&&(n=0===t?(0,r.f)(M):(0,a.Y)(i,t,e));let s=Math.abs(x-e)<=g;b.done=Math.abs(n)<=y&&s}return b.value=b.done?x:e,b},toString:()=>{let t=Math.min((0,s.t)(C),s.Y),e=(0,n.K)(e=>C.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return C}m.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),a=Math.min((0,s.t)(n),s.Y);return{type:"keyframes",ease:t=>n.next(a*t).value/e,duration:(0,r.X)(a)}}(t,100,m);return t.ease=e.ease,t.duration=(0,r.f)(e.duration),t.type="keyframes",t}},12941:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(62688).A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},18709:(t,e,i)=>{function n(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function s(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function r(t,e,i,n){if("function"==typeof e){let[r,a]=s(n);e=e(void 0!==i?i:t.custom,r,a)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[r,a]=s(n);e=e(void 0!==i?i:t.custom,r,a)}return e}function a(t,e,i){let n=t.getProps();return r(n,e,void 0!==i?i:n.custom,t)}i.d(e,{P:()=>sa});let o=t=>Array.isArray(t);var l,u,h=i(58745),d=i(48344),c=i(91628);function p(t,e){let i=t.getValue("willChange");if((0,d.S)(i)&&i.add)return i.add(e);if(!i&&c.W.WillChange){let i=new c.W.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let m=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),f="data-"+m("framerAppearId"),y=t=>null!==t,g=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],v=new Set(g),x={type:"spring",stiffness:500,damping:25,restSpeed:10},b=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),w={type:"keyframes",duration:.8},T={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},S=(t,{keyframes:e})=>e.length>2?w:v.has(t)?t.startsWith("scale")?b(e[1]):x:T;function P(t,e){return t?.[e]??t?.default??t}var A=i(99848),k=i(81480),M=i(6402),E=i(18234),V=i(35797);let D=t=>180*t/Math.PI,R=t=>j(D(Math.atan2(t[1],t[0]))),C={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:R,rotateZ:R,skewX:t=>D(Math.atan(t[1])),skewY:t=>D(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},j=t=>((t%=360)<0&&(t+=360),t),L=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),O=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),B={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:L,scaleY:O,scale:t=>(L(t)+O(t))/2,rotateX:t=>j(D(Math.atan2(t[6],t[5]))),rotateY:t=>j(D(Math.atan2(-t[2],t[0]))),rotateZ:R,rotate:R,skewX:t=>D(Math.atan(t[4])),skewY:t=>D(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function F(t){return+!!t.includes("scale")}function I(t,e){let i,n;if(!t||"none"===t)return F(e);let s=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(s)i=B,n=s;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=C,n=e}if(!n)return F(e);let r=i[e],a=n[1].split(",").map(N);return"function"==typeof r?r(a):a[r]}let U=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return I(i,e)};function N(t){return parseFloat(t.trim())}var $=i(79584),W=i(53445);let G=t=>t===$.ai||t===W.px,q=new Set(["x","y","z"]),X=g.filter(t=>!q.has(t)),Y={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>I(e,"x"),y:(t,{transform:e})=>I(e,"y")};Y.translateX=Y.x,Y.translateY=Y.y;let z=new Set,H=!1,K=!1,Q=!1;function _(){if(K){let t=Array.from(z).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return X.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}K=!1,H=!1,z.forEach(t=>t.complete(Q)),z.clear()}function Z(){z.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(K=!0)})}class J{constructor(t,e,i,n,s,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=s,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(z.add(this),H||(H=!0,k.Gt.read(Z),k.Gt.resolveKeyframes(_))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let s=n?.get(),r=t[t.length-1];if(void 0!==s)t[0]=s;else if(i&&e){let n=i.readValue(e,r);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=r),n&&void 0===s&&n.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),z.delete(this)}cancel(){"scheduled"===this.state&&(z.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let tt=t=>t.startsWith("--");var te=i(84671),ti=i(33081),tn=i(99275),ts=i(53903);let tr={};var ta=i(44898);let to=function(t,e){let i=(0,ta.p)(t);return()=>tr[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing");var tl=i(51002);let tu=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,th={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:tu([0,.65,.55,1]),circOut:tu([.55,0,1,.45]),backIn:tu([.31,.01,.66,-.59]),backOut:tu([.33,1.53,.69,.99])};var td=i(70226);function tc(t){return"function"==typeof t&&"applyToOptions"in t}var tp=i(537),tm=i(61452);class tf extends ti.q{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:s,allowFlatten:r=!1,finalKeyframe:a,onComplete:o}=t;this.isPseudoElement=!!s,this.allowFlatten=r,this.options=t,(0,tp.V)("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return tc(t)&&to()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:s=300,repeat:r=0,repeatType:a="loop",ease:o="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?to()?(0,tl.K)(e,i):"ease-out":(0,td.D)(e)?tu(e):Array.isArray(e)?e.map(e=>t(e,i)||th.easeOut):th[e]}(o,s);Array.isArray(d)&&(h.easing=d),ts.Q.value&&tn.q.waapi++;let c={delay:n,duration:s,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:r+1,direction:"reverse"===a?"alternate":"normal"};u&&(c.pseudoElement=u);let p=t.animate(h,c);return ts.Q.value&&p.finished.finally(()=>{tn.q.waapi--}),p}(e,i,n,l,s),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!s){let t=(0,V.X)(n,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){tt(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){let t=this.animation.effect?.getComputedTiming?.().duration||0;return(0,A.X)(Number(t))}get time(){return(0,A.X)(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=(0,A.f)(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&(0,te.J)())?(this.animation.timeline=t,tm.l):e(this)}}var ty=i(61611),tg=i(82518),tv=i(91215),tx=i(95299);let tb={anticipate:tg.b,backInOut:tv.ZZ,circInOut:tx.tn};class tw extends tf{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in tb&&(t.ease=tb[t.ease])}(t),(0,ty.E)(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:s,...r}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let a=new M.s({...r,autoplay:!1}),o=(0,A.f)(this.finishedTime??this.time);e.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}var tT=i(36237);let tS=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tT.f.test(t)||"0"===t)&&!t.startsWith("url("));var tP=i(83360);let tA=new Set(["opacity","clipPath","filter","transform"]),tk=(0,ta.p)(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class tM extends ti.q{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:s=0,repeatType:r="loop",keyframes:a,name:o,motionValue:l,element:u,...h}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=E.k.now();let d={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:s,repeatType:r,name:o,motionValue:l,element:u,...h},c=u?.KeyframeResolver||J;this.keyframeResolver=new c(a,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),o,l,u),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:s,type:r,velocity:a,delay:o,isHandoff:l,onUpdate:u}=i;this.resolvedAt=E.k.now(),!function(t,e,i,n){let s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],a=tS(s,e),o=tS(r,e);return(0,tp.$)(a===o,`You are trying to animate ${e} from "${s}" to "${r}". ${s} is not an animatable value - to enable this animation set ${s} to a value animatable to ${r} via the \`style\` property.`),!!a&&!!o&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||tc(i))&&n)}(t,s,r,a)&&((c.W.instantAnimations||!o)&&u?.((0,V.X)(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let h={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},d=!l&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:s,damping:r,type:a}=t;if(!(0,tP.s)(e?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return tk()&&i&&tA.has(i)&&("transform"!==i||!l)&&!o&&!n&&"mirror"!==s&&0!==r&&"inertia"!==a}(h)?new tw({...h,element:h.motionValue.owner.current}):new M.s(h);d.finished.then(()=>this.notifyFinished()).catch(tm.l),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),Q=!0,Z(),_(),Q=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let tE=(t,e,i,n={},s,r)=>a=>{let o=P(n,t)||{},l=o.delay||n.delay||0,{elapsed:u=0}=n;u-=(0,A.f)(l);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...o,delay:-u,onUpdate:t=>{e.set(t),o.onUpdate&&o.onUpdate(t)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:t,motionValue:e,element:r?void 0:s};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:s,repeat:r,repeatType:a,repeatDelay:o,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(o)&&Object.assign(h,S(t,h)),h.duration&&(h.duration=(0,A.f)(h.duration)),h.repeatDelay&&(h.repeatDelay=(0,A.f)(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let d=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0===h.delay&&(d=!0)),(c.W.instantAnimations||c.W.skipAnimations)&&(d=!0,h.duration=0,h.delay=0),h.allowFlatten=!o.type&&!o.ease,d&&!r&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},n){let s=t.filter(y),r=e&&"loop"!==i&&e%2==1?0:s.length-1;return s[r]}(h.keyframes,o);if(void 0!==t)return void k.Gt.update(()=>{h.onUpdate(t),h.onComplete()})}return o.isSync?new M.s(h):new tM(h)},tV=new Set(["width","height","top","left","right","bottom",...g]);function tD(t,e,{delay:i=0,transitionOverride:n,type:s}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:l,...u}=e;n&&(r=n);let d=[],c=s&&t.animationState&&t.animationState.getState()[s];for(let e in u){let n=t.getValue(e,t.latestValues[e]??null),s=u[e];if(void 0===s||c&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(c,e))continue;let a={delay:i,...P(r||{},e)},o=n.get();if(void 0!==o&&!n.isAnimating&&!Array.isArray(s)&&s===o&&!a.velocity)continue;let l=!1;if(window.MotionHandoffAnimation){let i=t.props[f];if(i){let t=window.MotionHandoffAnimation(i,e,k.Gt);null!==t&&(a.startTime=t,l=!0)}}p(t,e),n.start(tE(e,n,s,t.shouldReduceMotion&&tV.has(e)?{type:!1}:a,t,l));let h=n.animation;h&&d.push(h)}return l&&Promise.all(d).then(()=>{k.Gt.update(()=>{l&&function(t,e){let{transitionEnd:i={},transition:n={},...s}=a(t,e)||{};for(let e in s={...s,...i}){var r;let i=o(r=s[e])?r[r.length-1]||0:r;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,h.OQ)(i))}}(t,l)})}),d}function tR(t,e,i={}){let n=a(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:s=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(s=i.transitionOverride);let r=n?()=>Promise.all(tD(t,n,i)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:r=0,staggerChildren:a,staggerDirection:o}=s;return function(t,e,i=0,n=0,s=1,r){let a=[],o=(t.variantChildren.size-1)*n,l=1===s?(t=0)=>t*n:(t=0)=>o-t*n;return Array.from(t.variantChildren).sort(tC).forEach((t,n)=>{t.notify("AnimationStart",e),a.push(tR(t,e,{...r,delay:i+l(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,r+n,a,o,i)}:()=>Promise.resolve(),{when:l}=s;if(!l)return Promise.all([r(),o(i.delay)]);{let[t,e]="beforeChildren"===l?[r,o]:[o,r];return t().then(()=>e())}}function tC(t,e){return t.sortNodePosition(e)}function tj(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}function tL(t){return"string"==typeof t||Array.isArray(t)}let tO=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],tB=["initial",...tO],tF=tB.length,tI=[...tO].reverse(),tU=tO.length;function tN(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function t$(){return{animate:tN(!0),whileInView:tN(),whileHover:tN(),whileTap:tN(),whileDrag:tN(),whileFocus:tN(),exit:tN()}}class tW{constructor(t){this.isMounted=!1,this.node=t}update(){}}class tG extends tW{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>tR(t,e,i)));else if("string"==typeof e)n=tR(t,e,i);else{let s="function"==typeof e?a(t,e,i.custom):e;n=Promise.all(tD(t,s,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=t$(),s=!0,r=e=>(i,n)=>{let s=a(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(s){let{transition:t,transitionEnd:e,...n}=s;i={...i,...n,...e}}return i};function l(l){let{props:u}=t,h=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<tF;t++){let n=tB[t],s=e.props[n];(tL(s)||!1===s)&&(i[n]=s)}return i}(t.parent)||{},d=[],c=new Set,p={},m=1/0;for(let e=0;e<tU;e++){var f,y;let a=tI[e],g=i[a],v=void 0!==u[a]?u[a]:h[a],x=tL(v),b=a===l?g.isActive:null;!1===b&&(m=e);let w=v===h[a]&&v!==u[a]&&x;if(w&&s&&t.manuallyAnimateOnMount&&(w=!1),g.protectedKeys={...p},!g.isActive&&null===b||!v&&!g.prevProp||n(v)||"boolean"==typeof v)continue;let T=(f=g.prevProp,"string"==typeof(y=v)?y!==f:!!Array.isArray(y)&&!tj(y,f)),S=T||a===l&&g.isActive&&!w&&x||e>m&&x,P=!1,A=Array.isArray(v)?v:[v],k=A.reduce(r(a),{});!1===b&&(k={});let{prevResolvedValues:M={}}=g,E={...M,...k},V=e=>{S=!0,c.has(e)&&(P=!0,c.delete(e)),g.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in E){let e=k[t],i=M[t];if(p.hasOwnProperty(t))continue;let n=!1;(o(e)&&o(i)?tj(e,i):e===i)?void 0!==e&&c.has(t)?V(t):g.protectedKeys[t]=!0:null!=e?V(t):c.add(t)}g.prevProp=v,g.prevResolvedValues=k,g.isActive&&(p={...p,...k}),s&&t.blockInitialAnimation&&(S=!1);let D=!(w&&T)||P;S&&D&&d.push(...A.map(t=>({animation:t,options:{type:a}})))}if(c.size){let e={};if("boolean"!=typeof u.initial){let i=a(t,Array.isArray(u.initial)?u.initial[0]:u.initial);i&&i.transition&&(e.transition=i.transition)}c.forEach(i=>{let n=t.getBaseTarget(i),s=t.getValue(i);s&&(s.liveStyle=!0),e[i]=n??null}),d.push({animation:e})}let g=!!d.length;return s&&(!1===u.initial||u.initial===u.animate)&&!t.manuallyAnimateOnMount&&(g=!1),s=!1,g?e(d):Promise.resolve()}return{animateChanges:l,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let s=l(e);for(let t in i)i[t].protectedKeys={};return s},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=t$(),s=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();n(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let tq=0;class tX extends tW{constructor(){super(...arguments),this.id=tq++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}function tY(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let tz=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function tH(t){return{point:{x:t.pageX,y:t.pageY}}}let tK=t=>e=>tz(e)&&t(e,tH(e));function tQ(t,e,i,n){return tY(t,e,tK(i),n)}function t_({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}var tZ=i(20521);function tJ(t){return t.max-t.min}function t0(t,e,i,n=.5){t.origin=n,t.originPoint=(0,tZ.k)(e.min,e.max,t.origin),t.scale=tJ(i)/tJ(e),t.translate=(0,tZ.k)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function t1(t,e,i,n){t0(t.x,e.x,i.x,n?n.originX:void 0),t0(t.y,e.y,i.y,n?n.originY:void 0)}function t2(t,e,i){t.min=i.min+e.min,t.max=t.min+tJ(e)}function t5(t,e,i){t.min=e.min-i.min,t.max=t.min+tJ(e)}function t3(t,e,i){t5(t.x,e.x,i.x),t5(t.y,e.y,i.y)}let t9=()=>({translate:0,scale:1,origin:0,originPoint:0}),t4=()=>({x:t9(),y:t9()}),t8=()=>({min:0,max:0}),t6=()=>({x:t8(),y:t8()});function t7(t){return[t("x"),t("y")]}function et(t){return void 0===t||1===t}function ee({scale:t,scaleX:e,scaleY:i}){return!et(t)||!et(e)||!et(i)}function ei(t){return ee(t)||en(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function en(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function es(t,e,i,n,s){return void 0!==s&&(t=n+s*(t-n)),n+i*(t-n)+e}function er(t,e=0,i=1,n,s){t.min=es(t.min,e,i,n,s),t.max=es(t.max,e,i,n,s)}function ea(t,{x:e,y:i}){er(t.x,e.translate,e.scale,e.originPoint),er(t.y,i.translate,i.scale,i.originPoint)}function eo(t,e){t.min=t.min+e,t.max=t.max+e}function el(t,e,i,n,s=.5){let r=(0,tZ.k)(t.min,t.max,s);er(t,e,i,r,n)}function eu(t,e){el(t.x,e.x,e.scaleX,e.scale,e.originX),el(t.y,e.y,e.scaleY,e.scale,e.originY)}function eh(t,e){return t_(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let ed=({current:t})=>t?t.ownerDocument.defaultView:null;function ec(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let ep=(t,e)=>Math.abs(t-e);var em=i(98148);class ef{constructor(t,e,{transformPagePoint:i,contextWindow:n,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=ev(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(ep(t.x,e.x)**2+ep(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:n}=t,{timestamp:s}=k.uv;this.history.push({...n,timestamp:s});let{onStart:r,onMove:a}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=ey(e,this.transformPagePoint),k.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=ev("pointercancel"===t.type?this.lastMoveEventInfo:ey(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),n&&n(t,r)},!tz(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=i,this.contextWindow=n||window;let r=ey(tH(t),this.transformPagePoint),{point:a}=r,{timestamp:o}=k.uv;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=e;l&&l(t,ev(r,this.history)),this.removeListeners=(0,em.F)(tQ(this.contextWindow,"pointermove",this.handlePointerMove),tQ(this.contextWindow,"pointerup",this.handlePointerUp),tQ(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,k.WG)(this.updatePoint)}}function ey(t,e){return e?{point:e(t.point)}:t}function eg(t,e){return{x:t.x-e.x,y:t.y-e.y}}function ev({point:t},e){return{point:t,delta:eg(t,ex(e)),offset:eg(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,s=ex(t);for(;i>=0&&(n=t[i],!(s.timestamp-n.timestamp>(0,A.f)(.1)));)i--;if(!n)return{x:0,y:0};let r=(0,A.X)(s.timestamp-n.timestamp);if(0===r)return{x:0,y:0};let a={x:(s.x-n.x)/r,y:(s.y-n.y)/r};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function ex(t){return t[t.length-1]}var eb=i(62321),ew=i(54581);function eT(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function eS(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function eP(t,e,i){return{min:eA(t,e),max:eA(t,i)}}function eA(t,e){return"number"==typeof t?t:t[e]||0}let ek={x:!1,y:!1},eM=new WeakMap;class eE{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=t6(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new ef(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(tH(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:s}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(ek[t])return null;else return ek[t]=!0,()=>{ek[t]=!1};return ek.x||ek.y?null:(ek.x=ek.y=!0,()=>{ek.x=ek.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),t7(t=>{let e=this.getAxisMotionValue(t).get()||0;if(W.KN.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=tJ(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),s&&k.Gt.postRender(()=>s(t,e)),p(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:s,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(a),null!==this.currentDirection&&s&&s(this.currentDirection);return}this.updateAxis("x",e.point,a),this.updateAxis("y",e.point,a),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>t7(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:ed(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:n}=e;this.startAnimation(n);let{onDragEnd:s}=this.getProps();s&&k.Gt.postRender(()=>s(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!eV(t,n,this.currentDirection))return;let s=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?(0,tZ.k)(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?(0,tZ.k)(i,t,n.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),s.set(r)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&ec(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:s}){return{x:eT(t.x,i,s),y:eT(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:eP(t,"left","right"),y:eP(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&t7(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!ec(e))return!1;let n=e.current;(0,tp.V)(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:s}=this.visualElement;if(!s||!s.layout)return!1;let r=function(t,e,i){let n=eh(t,i),{scroll:s}=e;return s&&(eo(n.x,s.offset.x),eo(n.y,s.offset.y)),n}(n,s.root,this.visualElement.getTransformPagePoint()),a=(t=s.layout.layoutBox,{x:eS(t.x,r.x),y:eS(t.y,r.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(a));this.hasMutatedConstraints=!!t,t&&(a=t_(t))}return a}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:s,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(t7(a=>{if(!eV(a,e,this.currentDirection))return;let l=o&&o[a]||{};r&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[a]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(a,u)})).then(a)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return p(this.visualElement,t),i.start(tE(t,i,0,e,this.visualElement,!1))}stopAnimation(){t7(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){t7(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){t7(e=>{let{drag:i}=this.getProps();if(!eV(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,s=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:r}=n.layout.layoutBox[e];s.set(t[e]-(0,tZ.k)(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!ec(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};t7(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=tJ(t),s=tJ(e);return s>n?i=(0,eb.q)(e.min,e.max-n,t.min):n>s&&(i=(0,eb.q)(t.min,t.max-s,e.min)),(0,ew.q)(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),t7(e=>{if(!eV(e,t,null))return;let i=this.getAxisMotionValue(e),{min:s,max:r}=this.constraints[e];i.set((0,tZ.k)(s,r,n[e]))})}addListeners(){if(!this.visualElement.current)return;eM.set(this.visualElement,this);let t=tQ(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();ec(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),k.Gt.read(e);let s=tY(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(t7(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),n(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:s=!1,dragElastic:r=.35,dragMomentum:a=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:s,dragElastic:r,dragMomentum:a}}}function eV(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class eD extends tW{constructor(t){super(t),this.removeGroupControls=tm.l,this.removeListeners=tm.l,this.controls=new eE(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||tm.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let eR=t=>(e,i)=>{t&&k.Gt.postRender(()=>t(e,i))};class eC extends tW{constructor(){super(...arguments),this.removePointerDownListener=tm.l}onPointerDown(t){this.session=new ef(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ed(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:eR(t),onStart:eR(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&k.Gt.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=tQ(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var ej=i(60687),eL=i(43210),eO=i(93905),eB=i(2822);let eF=(0,eL.createContext)({}),eI={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function eU(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let eN={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!W.px.test(t))return t;else t=parseFloat(t);let i=eU(t,e.target.x),n=eU(t,e.target.y);return`${i}% ${n}%`}};var e$=i(27125);let eW={},{schedule:eG}=(0,i(47490).I)(queueMicrotask,!1);class eq extends eL.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:s}=t;for(let t in eY)eW[t]=eY[t],(0,e$.j)(t)&&(eW[t].isCSSVariable=!0);s&&(e.group&&e.group.add(s),i&&i.register&&n&&i.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),eI.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:s}=this.props,{projection:r}=i;return r&&(r.isPresent=s,n||t.layoutDependency!==e||void 0===e||t.isPresent!==s?r.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?r.promote():r.relegate()||k.Gt.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),eG.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function eX(t){let[e,i]=(0,eO.xQ)(),n=(0,eL.useContext)(eB.L);return(0,ej.jsx)(eq,{...t,layoutGroup:n,switchLayoutGroup:(0,eL.useContext)(eF),isPresent:e,safeToRemove:i})}let eY={borderRadius:{...eN,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:eN,borderTopRightRadius:eN,borderBottomLeftRadius:eN,borderBottomRightRadius:eN,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=tT.f.parse(t);if(n.length>5)return t;let s=tT.f.createTransformer(t),r=+("number"!=typeof n[0]),a=i.x.scale*e.x,o=i.y.scale*e.y;n[0+r]/=a,n[1+r]/=o;let l=(0,tZ.k)(a,o,.5);return"number"==typeof n[2+r]&&(n[2+r]/=l),"number"==typeof n[3+r]&&(n[3+r]/=l),s(n)}}},ez=(t,e)=>t.depth-e.depth;var eH=i(18691);class eK{constructor(){this.children=[],this.isDirty=!1}add(t){(0,eH.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,eH.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(ez),this.isDirty=!1,this.children.forEach(t)}}function eQ(t){return(0,d.S)(t)?t.get():t}let e_=["TopLeft","TopRight","BottomLeft","BottomRight"],eZ=e_.length,eJ=t=>"string"==typeof t?parseFloat(t):t,e0=t=>"number"==typeof t||W.px.test(t);function e1(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let e2=e3(0,.5,tx.yT),e5=e3(.5,.95,tm.l);function e3(t,e,i){return n=>n<t?0:n>e?1:i((0,eb.q)(t,e,n))}function e9(t,e){t.min=e.min,t.max=e.max}function e4(t,e){e9(t.x,e.x),e9(t.y,e.y)}function e8(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function e6(t,e,i,n,s){return t-=e,t=n+1/i*(t-n),void 0!==s&&(t=n+1/s*(t-n)),t}function e7(t,e,[i,n,s],r,a){!function(t,e=0,i=1,n=.5,s,r=t,a=t){if(W.KN.test(e)&&(e=parseFloat(e),e=(0,tZ.k)(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let o=(0,tZ.k)(r.min,r.max,n);t===r&&(o-=e),t.min=e6(t.min,e,i,o,s),t.max=e6(t.max,e,i,o,s)}(t,e[i],e[n],e[s],e.scale,r,a)}let it=["x","scaleX","originX"],ie=["y","scaleY","originY"];function ii(t,e,i,n){e7(t.x,e,it,i?i.x:void 0,n?n.x:void 0),e7(t.y,e,ie,i?i.y:void 0,n?n.y:void 0)}function is(t){return 0===t.translate&&1===t.scale}function ir(t){return is(t.x)&&is(t.y)}function ia(t,e){return t.min===e.min&&t.max===e.max}function io(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function il(t,e){return io(t.x,e.x)&&io(t.y,e.y)}function iu(t){return tJ(t.x)/tJ(t.y)}function ih(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class id{constructor(){this.members=[]}add(t){(0,eH.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,eH.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}var ic=i(10001),ip=i(2857);let im={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},iy=["","X","Y","Z"],ig={visibility:"hidden"},iv=0;function ix(t,e,i,n){let{latestValues:s}=e;s[t]&&(i[t]=s[t],e.setStaticValue(t,0),n&&(n[t]=0))}function ib({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:s}){return class{constructor(t={},i=e?.()){this.id=iv++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,ts.Q.value&&(im.nodes=im.calculatedTargetDeltas=im.calculatedProjections=0),this.nodes.forEach(iS),this.nodes.forEach(iD),this.nodes.forEach(iR),this.nodes.forEach(iP),ts.Q.addProjectionMetrics&&ts.Q.addProjectionMetrics(im)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new eK)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new ic.v),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=(0,ip.x)(e)&&(!(0,ip.x)(e)||"svg"!==e.tagName),this.instance=e;let{layoutId:i,layout:n,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),t){let i,n=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=E.k.now(),n=({timestamp:s})=>{let r=s-i;r>=250&&((0,k.WG)(n),t(r-e))};return k.Gt.setup(n,!0),()=>(0,k.WG)(n)}(n,250),eI.hasAnimatedSinceResize&&(eI.hasAnimatedSinceResize=!1,this.nodes.forEach(iV))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&s&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||s.getDefaultTransition()||iF,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=s.getProps(),l=!this.targetLayout||!il(this.targetLayout,n),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...P(r,"layout"),onPlay:a,onComplete:o};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||iV(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,k.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(iC),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[f];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",k.Gt,!(t||i))}let{parent:s}=e;s&&!s.hasCheckedOptimisedAppear&&t(s)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(ik);return}this.isUpdating||this.nodes.forEach(iM),this.isUpdating=!1,this.nodes.forEach(iE),this.nodes.forEach(iw),this.nodes.forEach(iT),this.clearAllSnapshots();let t=E.k.now();k.uv.delta=(0,ew.q)(0,1e3/60,t-k.uv.timestamp),k.uv.timestamp=t,k.uv.isProcessing=!0,k.PP.update.process(k.uv),k.PP.preRender.process(k.uv),k.PP.render.process(k.uv),k.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,eG.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(iA),this.sharedNodes.forEach(ij)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,k.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){k.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||tJ(this.snapshot.measuredBox.x)||tJ(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=t6(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!ir(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,r=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||ei(this.latestValues)||r)&&(s(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),iN((e=n).x),iN(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return t6();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(iW))){let{scroll:t}=this.root;t&&(eo(e.x,t.offset.x),eo(e.y,t.offset.y))}return e}removeElementScroll(t){let e=t6();if(e4(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:s,options:r}=n;n!==this.root&&s&&r.layoutScroll&&(s.wasRoot&&e4(e,t),eo(e.x,s.offset.x),eo(e.y,s.offset.y))}return e}applyTransform(t,e=!1){let i=t6();e4(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&eu(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),ei(n.latestValues)&&eu(i,n.latestValues)}return ei(this.latestValues)&&eu(i,this.latestValues),i}removeTransform(t){let e=t6();e4(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!ei(i.latestValues))continue;ee(i.latestValues)&&i.updateSnapshot();let n=t6();e4(n,i.measurePageBox()),ii(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return ei(this.latestValues)&&ii(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==k.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:s}=this.options;if(this.layout&&(n||s)){if(this.resolvedRelativeTargetAt=k.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=t6(),this.relativeTargetOrigin=t6(),t3(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),e4(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=t6(),this.targetWithTransforms=t6()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var r,a,o;this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,o=this.relativeParent.target,t2(r.x,a.x,o.x),t2(r.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):e4(this.target,this.layout.layoutBox),ea(this.target,this.targetDelta)):e4(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=t6(),this.relativeTargetOrigin=t6(),t3(this.relativeTargetOrigin,this.target,t.target),e4(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}ts.Q.value&&im.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||ee(this.parent.latestValues)||en(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===k.uv.timestamp&&(i=!1),i)return;let{layout:n,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||s))return;e4(this.layoutCorrected,this.layout.layoutBox);let r=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,n=!1){let s,r,a=i.length;if(a){e.x=e.y=1;for(let o=0;o<a;o++){r=(s=i[o]).projectionDelta;let{visualElement:a}=s.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(n&&s.options.layoutScroll&&s.scroll&&s!==s.root&&eu(t,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,ea(t,r)),n&&ei(s.latestValues)&&eu(t,s.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=t6());let{target:o}=t;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(e8(this.prevProjectionDelta.x,this.projectionDelta.x),e8(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),t1(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===r&&this.treeScale.y===a&&ih(this.projectionDelta.x,this.prevProjectionDelta.x)&&ih(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),ts.Q.value&&im.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=t4(),this.projectionDelta=t4(),this.projectionDeltaWithTransform=t4()}setAnimationOrigin(t,e=!1){let i,n=this.snapshot,s=n?n.latestValues:{},r={...this.latestValues},a=t4();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let o=t6(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(iB));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(iL(a.x,t.x,n),iL(a.y,t.y,n),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,y;t3(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,y=n,iO(p.x,m.x,f.x,y),iO(p.y,m.y,f.y,y),i&&(u=this.relativeTarget,c=i,ia(u.x,c.x)&&ia(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=t6()),e4(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,n,s,r){s?(t.opacity=(0,tZ.k)(0,i.opacity??1,e2(n)),t.opacityExit=(0,tZ.k)(e.opacity??1,0,e5(n))):r&&(t.opacity=(0,tZ.k)(e.opacity??1,i.opacity??1,n));for(let s=0;s<eZ;s++){let r=`border${e_[s]}Radius`,a=e1(e,r),o=e1(i,r);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||e0(a)===e0(o)?(t[r]=Math.max((0,tZ.k)(eJ(a),eJ(o),n),0),(W.KN.test(o)||W.KN.test(a))&&(t[r]+="%")):t[r]=o)}(e.rotate||i.rotate)&&(t.rotate=(0,tZ.k)(e.rotate||0,i.rotate||0,n))}(r,s,this.latestValues,n,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&((0,k.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=k.Gt.update(()=>{eI.hasAnimatedSinceResize=!0,tn.q.layout++,this.motionValue||(this.motionValue=(0,h.OQ)(0)),this.currentAnimation=function(t,e,i){let n=(0,d.S)(t)?t:(0,h.OQ)(t);return n.start(tE("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{tn.q.layout--},onComplete:()=>{tn.q.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:s}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&i$(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||t6();let e=tJ(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=tJ(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}e4(e,i),eu(e,s),t1(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new id),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&ix("z",t,n,this.animationValues);for(let e=0;e<iy.length;e++)ix(`rotate${iy[e]}`,t,n,this.animationValues),ix(`skew${iy[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return ig;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=eQ(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=eQ(t?.pointerEvents)||""),this.hasProjected&&!ei(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let s=n.animationValues||n.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let n="",s=t.x.translate/e.x,r=t.y.translate/e.y,a=i?.z||0;if((s||r||a)&&(n=`translate3d(${s}px, ${r}px, ${a}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:s,rotateY:r,skewX:a,skewY:o}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),s&&(n+=`rotateX(${s}deg) `),r&&(n+=`rotateY(${r}deg) `),a&&(n+=`skewX(${a}deg) `),o&&(n+=`skewY(${o}deg) `)}let o=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==o||1!==l)&&(n+=`scale(${o}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,s),i&&(e.transform=i(s,e.transform));let{x:r,y:a}=this.projectionDelta;for(let t in e.transformOrigin=`${100*r.origin}% ${100*a.origin}% 0`,n.animationValues?e.opacity=n===this?s.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:s.opacityExit:e.opacity=n===this?void 0!==s.opacity?s.opacity:"":void 0!==s.opacityExit?s.opacityExit:0,eW){if(void 0===s[t])continue;let{correct:i,applyTo:r,isCSSVariable:a}=eW[t],o="none"===e.transform?s[t]:i(s[t],n);if(r){let t=r.length;for(let i=0;i<t;i++)e[r[i]]=o}else a?this.options.visualElement.renderState.vars[t]=o:e[t]=o}return this.options.layoutId&&(e.pointerEvents=n===this?eQ(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(ik),this.root.sharedNodes.clear()}}}function iw(t){t.updateLayout()}function iT(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:s}=t.options,r=e.source!==t.layout.source;"size"===s?t7(t=>{let n=r?e.measuredBox[t]:e.layoutBox[t],s=tJ(n);n.min=i[t].min,n.max=n.min+s}):i$(s,e.layoutBox,i)&&t7(n=>{let s=r?e.measuredBox[n]:e.layoutBox[n],a=tJ(i[n]);s.max=s.min+a,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+a)});let a=t4();t1(a,i,e.layoutBox);let o=t4();r?t1(o,t.applyTransform(n,!0),e.measuredBox):t1(o,i,e.layoutBox);let l=!ir(a),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:s,layout:r}=n;if(s&&r){let a=t6();t3(a,e.layoutBox,s.layoutBox);let o=t6();t3(o,i,r.layoutBox),il(a,o)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=o,t.relativeTargetOrigin=a,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function iS(t){ts.Q.value&&im.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function iP(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function iA(t){t.clearSnapshot()}function ik(t){t.clearMeasurements()}function iM(t){t.isLayoutDirty=!1}function iE(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function iV(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function iD(t){t.resolveTargetDelta()}function iR(t){t.calcProjection()}function iC(t){t.resetSkewAndRotation()}function ij(t){t.removeLeadSnapshot()}function iL(t,e,i){t.translate=(0,tZ.k)(e.translate,0,i),t.scale=(0,tZ.k)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function iO(t,e,i,n){t.min=(0,tZ.k)(e.min,i.min,n),t.max=(0,tZ.k)(e.max,i.max,n)}function iB(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let iF={duration:.45,ease:[.4,0,.1,1]},iI=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),iU=iI("applewebkit/")&&!iI("chrome/")?Math.round:tm.l;function iN(t){t.min=iU(t.min),t.max=iU(t.max)}function i$(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(iu(e)-iu(i)))}function iW(t){return t!==t.root&&t.scroll?.wasRoot}let iG=ib({attachResizeListener:(t,e)=>tY(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),iq={current:void 0},iX=ib({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!iq.current){let t=new iG({});t.mount(window),t.setOptions({layoutScroll:!0}),iq.current=t}return iq.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});var iY=i(45653);function iz(t,e){let i=(0,iY.K)(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function iH(t){return!("touch"===t.pointerType||ek.x||ek.y)}function iK(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let s=n["onHover"+i];s&&k.Gt.postRender(()=>s(e,tH(e)))}class iQ extends tW{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,s,r]=iz(t,i),a=t=>{if(!iH(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let r=t=>{iH(t)&&(n(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,s)};return n.forEach(t=>{t.addEventListener("pointerenter",a,s)}),r}(t,(t,e)=>(iK(this.node,e,"Start"),t=>iK(this.node,t,"End"))))}unmount(){}}class i_ extends tW{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,em.F)(tY(this.node.current,"focus",()=>this.onFocus()),tY(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let iZ=(t,e)=>!!e&&(t===e||iZ(t,e.parentElement)),iJ=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),i0=new WeakSet;function i1(t){return e=>{"Enter"===e.key&&t(e)}}function i2(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let i5=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=i1(()=>{if(i0.has(i))return;i2(i,"down");let t=i1(()=>{i2(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>i2(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function i3(t){return tz(t)&&!(ek.x||ek.y)}function i9(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let s=n["onTap"+("End"===i?"":i)];s&&k.Gt.postRender(()=>s(e,tH(e)))}class i4 extends tW{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,s,r]=iz(t,i),a=t=>{let n=t.currentTarget;if(!i3(t))return;i0.add(n);let r=e(n,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),i0.has(n)&&i0.delete(n),i3(t)&&"function"==typeof r&&r(t,{success:e})},o=t=>{a(t,n===window||n===document||i.useGlobalTarget||iZ(n,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,s),window.addEventListener("pointercancel",l,s)};return n.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",a,s),(0,tP.s)(t))&&(t.addEventListener("focus",t=>i5(t,s)),iJ.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r}(t,(t,e)=>(i9(this.node,e,"Start"),(t,{success:e})=>i9(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let i8=new WeakMap,i6=new WeakMap,i7=t=>{let e=i8.get(t.target);e&&e(t)},nt=t=>{t.forEach(i7)},ne={some:0,all:1};class ni extends tW{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:s}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:ne[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;i6.has(i)||i6.set(i,{});let n=i6.get(i),s=JSON.stringify(e);return n[s]||(n[s]=new IntersectionObserver(nt,{root:t,...e})),n[s]}(e);return i8.set(t,i),n.observe(t),()=>{i8.delete(t),n.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,s&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),r=e?i:n;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let nn=(0,eL.createContext)({strict:!1});var ns=i(56923);let nr=(0,eL.createContext)({});function na(t){return n(t.animate)||tB.some(e=>tL(t[e]))}function no(t){return!!(na(t)||t.variants)}function nl(t){return Array.isArray(t)?t.join(" "):t}var nu=i(49567);let nh={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nd={};for(let t in nh)nd[t]={isEnabled:e=>nh[t].some(t=>!!e[t])};let nc=Symbol.for("motionComponentSymbol");var np=i(48618),nm=i(32097);function nf(t,{layout:e,layoutId:i}){return v.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!eW[t]||"opacity"===t)}let ny=(t,e)=>e&&"number"==typeof t?e.transform(t):t,ng={...$.ai,transform:Math.round},nv={rotate:W.uj,rotateX:W.uj,rotateY:W.uj,rotateZ:W.uj,scale:$.hs,scaleX:$.hs,scaleY:$.hs,scaleZ:$.hs,skew:W.uj,skewX:W.uj,skewY:W.uj,distance:W.px,translateX:W.px,translateY:W.px,translateZ:W.px,x:W.px,y:W.px,z:W.px,perspective:W.px,transformPerspective:W.px,opacity:$.X4,originX:W.gQ,originY:W.gQ,originZ:W.px},nx={borderWidth:W.px,borderTopWidth:W.px,borderRightWidth:W.px,borderBottomWidth:W.px,borderLeftWidth:W.px,borderRadius:W.px,radius:W.px,borderTopLeftRadius:W.px,borderTopRightRadius:W.px,borderBottomRightRadius:W.px,borderBottomLeftRadius:W.px,width:W.px,maxWidth:W.px,height:W.px,maxHeight:W.px,top:W.px,right:W.px,bottom:W.px,left:W.px,padding:W.px,paddingTop:W.px,paddingRight:W.px,paddingBottom:W.px,paddingLeft:W.px,margin:W.px,marginTop:W.px,marginRight:W.px,marginBottom:W.px,marginLeft:W.px,backgroundPositionX:W.px,backgroundPositionY:W.px,...nv,zIndex:ng,fillOpacity:$.X4,strokeOpacity:$.X4,numOctaves:ng},nb={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nw=g.length;function nT(t,e,i){let{style:n,vars:s,transformOrigin:r}=t,a=!1,o=!1;for(let t in e){let i=e[t];if(v.has(t)){a=!0;continue}if((0,e$.j)(t)){s[t]=i;continue}{let e=ny(i,nx[t]);t.startsWith("origin")?(o=!0,r[t]=e):n[t]=e}}if(!e.transform&&(a||i?n.transform=function(t,e,i){let n="",s=!0;for(let r=0;r<nw;r++){let a=g[r],o=t[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let t=ny(o,nx[a]);if(!l){s=!1;let e=nb[a]||a;n+=`${e}(${t}) `}i&&(e[a]=t)}}return n=n.trim(),i?n=i(e,s?"":n):s&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),o){let{originX:t="50%",originY:e="50%",originZ:i=0}=r;n.transformOrigin=`${t} ${e} ${i}`}}let nS=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function nP(t,e,i){for(let n in e)(0,d.S)(e[n])||nf(n,i)||(t[n]=e[n])}let nA={offset:"stroke-dashoffset",array:"stroke-dasharray"},nk={offset:"strokeDashoffset",array:"strokeDasharray"};function nM(t,{attrX:e,attrY:i,attrScale:n,pathLength:s,pathSpacing:r=1,pathOffset:a=0,...o},l,u,h){if(nT(t,o,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=h?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==n&&(d.scale=n),void 0!==s&&function(t,e,i=1,n=0,s=!0){t.pathLength=1;let r=s?nA:nk;t[r.offset]=W.px.transform(-n);let a=W.px.transform(e),o=W.px.transform(i);t[r.array]=`${a} ${o}`}(d,s,r,a,!1)}let nE=()=>({...nS(),attrs:{}}),nV=t=>"string"==typeof t&&"svg"===t.toLowerCase(),nD=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function nR(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||nD.has(t)}let nC=t=>!nR(t);try{!function(t){t&&(nC=e=>e.startsWith("on")?!nR(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let nj=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function nL(t){if("string"!=typeof t||t.includes("-"));else if(nj.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var nO=i(53906);let nB=t=>(e,i)=>{let s=(0,eL.useContext)(nr),a=(0,eL.useContext)(np.t),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,s,a){return{latestValues:function(t,e,i,s){let a={},o=s(t,{});for(let t in o)a[t]=eQ(o[t]);let{initial:l,animate:u}=t,h=na(t),d=no(t);e&&d&&!h&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!n(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let n=r(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(a[t]=e)}for(let e in t)a[e]=t[e]}}}return a}(i,s,a,t),renderState:e()}})(t,e,s,a);return i?o():(0,nO.M)(o)};function nF(t,e,i){let{style:n}=t,s={};for(let r in n)((0,d.S)(n[r])||e.style&&(0,d.S)(e.style[r])||nf(r,t)||i?.getValue(r)?.liveStyle!==void 0)&&(s[r]=n[r]);return s}let nI={useVisualState:nB({scrapeMotionValuesFromProps:nF,createRenderState:nS})};function nU(t,e,i){let n=nF(t,e,i);for(let i in t)((0,d.S)(t[i])||(0,d.S)(e[i]))&&(n[-1!==g.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}let nN={useVisualState:nB({scrapeMotionValuesFromProps:nU,createRenderState:nE})},n$={current:null},nW={current:!1},nG=new WeakMap,nq=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),nX=t=>/^0[^.\s]+$/u.test(t);var nY=i(93487);let nz=t=>e=>e.test(t),nH=[$.ai,W.px,W.KN,W.uj,W.vw,W.vh,{test:t=>"auto"===t,parse:t=>t}],nK=t=>nH.find(nz(t)),nQ=[...nH,nY.y,tT.f],n_=t=>nQ.find(nz(t));var nZ=i(9497);let nJ=new Set(["brightness","contrast","saturate","opacity"]);function n0(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(nZ.S)||[];if(!n)return t;let s=i.replace(n,""),r=+!!nJ.has(e);return n!==i&&(r*=100),e+"("+r+s+")"}let n1=/\b([a-z-]*)\(.*?\)/gu,n2={...tT.f,getAnimatableNone:t=>{let e=t.match(n1);return e?e.map(n0).join(" "):t}},n5={...nx,color:nY.y,backgroundColor:nY.y,outlineColor:nY.y,fill:nY.y,stroke:nY.y,borderColor:nY.y,borderTopColor:nY.y,borderRightColor:nY.y,borderBottomColor:nY.y,borderLeftColor:nY.y,filter:n2,WebkitFilter:n2},n3=t=>n5[t];function n9(t,e){let i=n3(t);return i!==n2&&(i=tT.f),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let n4=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class n8{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:s,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=J,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=E.k.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,k.Gt.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=r;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=a,this.blockInitialAnimation=!!s,this.isControllingVariants=na(e),this.isVariantNode=no(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==o[t]&&(0,d.S)(e)&&e.set(o[t],!1)}}mount(t){this.current=t,nG.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),nW.current||function(){if(nW.current=!0,nu.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>n$.current=t.matches;t.addListener(e),e()}else n$.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||n$.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),(0,k.WG)(this.notifyUpdate),(0,k.WG)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=v.has(t);n&&this.onBindTransform&&this.onBindTransform();let s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&k.Gt.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in nd){let e=nd[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):t6()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<n4.length;e++){let i=n4[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let s=e[n],r=i[n];if((0,d.S)(s))t.addValue(n,s);else if((0,d.S)(r))t.addValue(n,(0,h.OQ)(s,{owner:t}));else if(r!==s)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{let e=t.getStaticValue(n);t.addValue(n,(0,h.OQ)(void 0!==e?e:s,{owner:t}))}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,h.OQ)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(nq(i)||nX(i))?i=parseFloat(i):!n_(i)&&tT.f.test(e)&&(i=n9(t,e)),this.setBaseTarget(t,(0,d.S)(i)?i.get():i)),(0,d.S)(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=r(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||(0,d.S)(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new ic.v),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}let n6=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,n7=new Set(["auto","none","0"]);class st extends J{constructor(t,e,i,n,s){super(t,e,i,n,s,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&(n=n.trim(),(0,e$.p)(n))){let s=function t(e,i,n=1){(0,tp.V)(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[s,r]=function(t){let e=n6.exec(t);if(!e)return[,];let[,i,n,s]=e;return[`--${i??n}`,s]}(e);if(!s)return;let a=window.getComputedStyle(i).getPropertyValue(s);if(a){let t=a.trim();return nq(t)?parseFloat(t):t}return(0,e$.p)(r)?t(r,i,n+1):r}(n,e.current);void 0!==s&&(t[i]=s),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!tV.has(i)||2!==t.length)return;let[n,s]=t,r=nK(n),a=nK(s);if(r!==a)if(G(r)&&G(a))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else Y[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||nX(n)))&&i.push(e)}i.length&&function(t,e,i){let n,s=0;for(;s<t.length&&!n;){let e=t[s];"string"==typeof e&&!n7.has(e)&&(0,tT.V)(e).values.length&&(n=t[s]),s++}if(n&&i)for(let s of e)t[s]=n9(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=Y[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let s=i.length-1,r=i[s];i[s]=Y[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}class se extends n8{constructor(){super(...arguments),this.KeyframeResolver=st}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;(0,d.S)(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function si(t,{style:e,vars:i},n,s){for(let r in Object.assign(t.style,e,s&&s.getProjectionStyles(n)),i)t.style.setProperty(r,i[r])}class sn extends se{constructor(){super(...arguments),this.type="html",this.renderInstance=si}readValueFromInstance(t,e){if(v.has(e))return this.projection?.isProjecting?F(e):U(t,e);{let i=window.getComputedStyle(t),n=((0,e$.j)(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return eh(t,e)}build(t,e,i){nT(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return nF(t,e,i)}}let ss=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class sr extends se{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=t6}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(v.has(e)){let t=n3(e);return t&&t.default||0}return e=ss.has(e)?e:m(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return nU(t,e,i)}build(t,e,i){nM(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){for(let i in si(t,e,void 0,n),e.attrs)t.setAttribute(ss.has(i)?i:m(i),e.attrs[i])}mount(t){this.isSVGTag=nV(t.tagName),super.mount(t)}}let sa=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((l={animation:{Feature:tG},exit:{Feature:tX},inView:{Feature:ni},tap:{Feature:i4},focus:{Feature:i_},hover:{Feature:iQ},pan:{Feature:eC},drag:{Feature:eD,ProjectionNode:iX,MeasureLayout:eX},layout:{ProjectionNode:iX,MeasureLayout:eX}},u=(t,e)=>nL(t)?new sr(e):new sn(e,{allowProjection:t!==eL.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function({preloadedFeatures:t,createVisualElement:e,useRender:i,useVisualState:n,Component:s}){function r(t,r){var a,o,l;let u,h={...(0,eL.useContext)(ns.Q),...t,layoutId:function({layoutId:t}){let e=(0,eL.useContext)(eB.L).id;return e&&void 0!==t?e+"-"+t:t}(t)},{isStatic:d}=h,c=function(t){let{initial:e,animate:i}=function(t,e){if(na(t)){let{initial:e,animate:i}=t;return{initial:!1===e||tL(e)?e:void 0,animate:tL(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,eL.useContext)(nr));return(0,eL.useMemo)(()=>({initial:e,animate:i}),[nl(e),nl(i)])}(t),p=n(t,d);if(!d&&nu.B){o=0,l=0,(0,eL.useContext)(nn).strict;let t=function(t){let{drag:e,layout:i}=nd;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:e?.isEnabled(t)||i?.isEnabled(t)?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(h);u=t.MeasureLayout,c.visualElement=function(t,e,i,n,s){let{visualElement:r}=(0,eL.useContext)(nr),a=(0,eL.useContext)(nn),o=(0,eL.useContext)(np.t),l=(0,eL.useContext)(ns.Q).reducedMotion,u=(0,eL.useRef)(null);n=n||a.renderer,!u.current&&n&&(u.current=n(t,{visualState:e,parent:r,props:i,presenceContext:o,blockInitialAnimation:!!o&&!1===o.initial,reducedMotionConfig:l}));let h=u.current,d=(0,eL.useContext)(eF);h&&!h.projection&&s&&("html"===h.type||"svg"===h.type)&&function(t,e,i,n){let{layoutId:s,layout:r,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:s,layout:r,alwaysMeasureLayout:!!a||o&&ec(o),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:n,crossfade:h,layoutScroll:l,layoutRoot:u})}(u.current,i,s,d);let c=(0,eL.useRef)(!1);(0,eL.useInsertionEffect)(()=>{h&&c.current&&h.update(i,o)});let p=i[f],m=(0,eL.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return(0,nm.E)(()=>{h&&(c.current=!0,window.MotionIsMounted=!0,h.updateFeatures(),eG.render(h.render),m.current&&h.animationState&&h.animationState.animateChanges())}),(0,eL.useEffect)(()=>{h&&(!m.current&&h.animationState&&h.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),h}(s,p,h,e,t.ProjectionNode)}return(0,ej.jsxs)(nr.Provider,{value:c,children:[u&&c.visualElement?(0,ej.jsx)(u,{visualElement:c.visualElement,...h}):null,i(s,t,(a=c.visualElement,(0,eL.useCallback)(t=>{t&&p.onMount&&p.onMount(t),a&&(t?a.mount(t):a.unmount()),r&&("function"==typeof r?r(t):ec(r)&&(r.current=t))},[a])),p,d,c.visualElement)]})}t&&function(t){for(let e in t)nd[e]={...nd[e],...t[e]}}(t),r.displayName=`motion.${"string"==typeof s?s:`create(${s.displayName??s.name??""})`}`;let a=(0,eL.forwardRef)(r);return a[nc]=s,a}({...nL(t)?nN:nI,preloadedFeatures:l,useRender:function(t=!1){return(e,i,n,{latestValues:s},r)=>{let a=(nL(e)?function(t,e,i,n){let s=(0,eL.useMemo)(()=>{let i=nE();return nM(i,e,nV(n),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};nP(e,t.style,t),s.style={...e,...s.style}}return s}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return nP(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,eL.useMemo)(()=>{let i=nS();return nT(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,s,r,e),o=function(t,e,i){let n={};for(let s in t)("values"!==s||"object"!=typeof t.values)&&(nC(s)||!0===i&&nR(s)||!e&&!nR(s)||t.draggable&&s.startsWith("onDrag"))&&(n[s]=t[s]);return n}(i,"string"==typeof e,t),l=e!==eL.Fragment?{...o,...a,ref:n}:{},{children:u}=i,h=(0,eL.useMemo)(()=>(0,d.S)(u)?u.get():u,[u]);return(0,eL.createElement)(e,{...l,children:h})}}(e),createVisualElement:u,Component:t})}))},21134:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(62688).A)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},26787:(t,e,i)=>{i.d(e,{v:()=>l});var n=i(43210);let s=t=>{let e,i=new Set,n=(t,n)=>{let s="function"==typeof t?t(e):t;if(!Object.is(s,e)){let t=e;e=(null!=n?n:"object"!=typeof s||null===s)?s:Object.assign({},e,s),i.forEach(i=>i(e,t))}},s=()=>e,r={setState:n,getState:s,getInitialState:()=>a,subscribe:t=>(i.add(t),()=>i.delete(t))},a=e=t(n,s,r);return r},r=t=>t?s(t):s,a=t=>t,o=t=>{let e=r(t),i=t=>(function(t,e=a){let i=n.useSyncExternalStore(t.subscribe,()=>e(t.getState()),()=>e(t.getInitialState()));return n.useDebugValue(i),i})(e,t);return Object.assign(i,e),i},l=t=>t?o(t):o},33081:(t,e,i)=>{i.d(e,{q:()=>n});class n{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}},33860:(t,e,i)=>{i.d(e,{A:()=>r});var n=i(61452);let s=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function r(t,e,i,r){if(t===e&&i===r)return n.l;let a=e=>(function(t,e,i,n,r){let a,o,l=0;do(a=s(o=e+(i-e)/2,n,r)-t)>0?i=o:e=o;while(Math.abs(a)>1e-7&&++l<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:s(a(t),e,r)}},35797:(t,e,i)=>{i.d(e,{X:()=>s});let n=t=>null!==t;function s(t,{repeat:e,repeatType:i="loop"},r,a=1){let o=t.filter(n),l=a<0||e&&"loop"!==i&&e%2==1?0:o.length-1;return l&&void 0!==r?r:o[l]}},44257:(t,e,i)=>{i.d(e,{Y:()=>n,t:()=>s});let n=2e4;function s(t){let e=0,i=t.next(e);for(;!i.done&&e<n;)e+=50,i=t.next(e);return e>=n?1/0:e}},48344:(t,e,i)=>{i.d(e,{S:()=>n});let n=t=>!!(t&&t.getVelocity)},48618:(t,e,i)=>{i.d(e,{t:()=>n});let n=(0,i(43210).createContext)(null)},51002:(t,e,i)=>{i.d(e,{K:()=>n});let n=(t,e,i=10)=>{let n="",s=Math.max(Math.round(e/i),2);for(let e=0;e<s;e++)n+=t(e/(s-1))+", ";return`linear(${n.substring(0,n.length-2)})`}},53332:(t,e,i)=>{var n=i(43210),s="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},r=n.useState,a=n.useEffect,o=n.useLayoutEffect,l=n.useDebugValue;function u(t){var e=t.getSnapshot;t=t.value;try{var i=e();return!s(t,i)}catch(t){return!0}}var h="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(t,e){return e()}:function(t,e){var i=e(),n=r({inst:{value:i,getSnapshot:e}}),s=n[0].inst,h=n[1];return o(function(){s.value=i,s.getSnapshot=e,u(s)&&h({inst:s})},[t,i,e]),a(function(){return u(s)&&h({inst:s}),t(function(){u(s)&&h({inst:s})})},[t]),l(i),i};e.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:h},55893:(t,e,i)=>{i.d(e,{B:()=>r});var n=i(12179),s=i(4486);function r({keyframes:t,velocity:e=0,power:i=.8,timeConstant:r=325,bounceDamping:a=10,bounceStiffness:o=500,modifyTarget:l,min:u,max:h,restDelta:d=.5,restSpeed:c}){let p,m,f=t[0],y={done:!1,value:f},g=t=>void 0!==u&&t<u||void 0!==h&&t>h,v=t=>void 0===u?h:void 0===h||Math.abs(u-t)<Math.abs(h-t)?u:h,x=i*e,b=f+x,w=void 0===l?b:l(b);w!==b&&(x=w-f);let T=t=>-x*Math.exp(-t/r),S=t=>w+T(t),P=t=>{let e=T(t),i=S(t);y.done=Math.abs(e)<=d,y.value=y.done?w:i},A=t=>{g(y.value)&&(p=t,m=(0,n.o)({keyframes:[y.value,v(y.value)],velocity:(0,s.Y)(S,t,y.value),damping:a,stiffness:o,restDelta:d,restSpeed:c}))};return A(0),{calculatedDuration:null,next:t=>{let e=!1;return(m||void 0!==p||(e=!0,P(t),A(t)),void 0!==p&&t>=p)?m.next(t-p):(e||P(t),y)}}}},56923:(t,e,i)=>{i.d(e,{Q:()=>n});let n=(0,i(43210).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},57379:(t,e,i)=>{t.exports=i(53332)},61611:(t,e,i)=>{i.d(e,{E:()=>o});var n=i(55893),s=i(94117),r=i(12179);let a={decay:n.B,inertia:n.B,tween:s.i,keyframes:s.i,spring:r.o};function o(t){"string"==typeof t.type&&(t.type=a[t.type])}},62157:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(62688).A)("Github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},62369:(t,e,i)=>{i.d(e,{b:()=>u});var n=i(43210),s=i(14163),r=i(60687),a="horizontal",o=["horizontal","vertical"],l=n.forwardRef((t,e)=>{var i;let{decorative:n,orientation:l=a,...u}=t,h=(i=l,o.includes(i))?l:a;return(0,r.jsx)(s.sG.div,{"data-orientation":h,...n?{role:"none"}:{"aria-orientation":"vertical"===h?h:void 0,role:"separator"},...u,ref:e})});l.displayName="Separator";var u=l},70226:(t,e,i)=>{i.d(e,{D:()=>n});let n=t=>Array.isArray(t)&&"number"==typeof t[0]},72851:(t,e,i)=>{i.d(e,{G:()=>n});let n=t=>e=>1-t(1-e)},78122:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(62688).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},78148:(t,e,i)=>{i.d(e,{b:()=>o});var n=i(43210),s=i(14163),r=i(60687),a=n.forwardRef((t,e)=>(0,r.jsx)(s.sG.label,{...t,ref:e,onMouseDown:e=>{e.target.closest("button, input, select, textarea")||(t.onMouseDown?.(e),!e.defaultPrevented&&e.detail>1&&e.preventDefault())}}));a.displayName="Label";var o=a},82518:(t,e,i)=>{i.d(e,{b:()=>s});var n=i(91215);let s=t=>(t*=2)<1?.5*(0,n.dg)(t):.5*(2-Math.pow(2,-10*(t-1)))},82550:(t,e,i)=>{i.d(e,{V:()=>n});let n=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2},83753:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(62688).A)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},91215:(t,e,i)=>{i.d(e,{Sz:()=>a,ZZ:()=>l,dg:()=>o});var n=i(33860),s=i(82550),r=i(72851);let a=(0,n.A)(.33,1.53,.69,.99),o=(0,r.G)(a),l=(0,s.V)(o)},93425:(t,e,i)=>{i.d(e,{E:()=>f});var n=i(43210),s=i(33465),r=i(5563),a=i(35536),o=i(31212);function l(t,e){return t.filter(t=>!e.includes(t))}var u=class extends a.Q{#t;#e;#i;#n;#s;#r;#a;#o;#l=[];constructor(t,e,i){super(),this.#t=t,this.#n=i,this.#i=[],this.#s=[],this.#e=[],this.setQueries(e)}onSubscribe(){1===this.listeners.size&&this.#s.forEach(t=>{t.subscribe(e=>{this.#u(t,e)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#s.forEach(t=>{t.destroy()})}setQueries(t,e){this.#i=t,this.#n=e,s.jG.batch(()=>{let t=this.#s,e=this.#h(this.#i);this.#l=e,e.forEach(t=>t.observer.setOptions(t.defaultedQueryOptions));let i=e.map(t=>t.observer),n=i.map(t=>t.getCurrentResult()),s=i.some((e,i)=>e!==t[i]);(t.length!==i.length||s)&&(this.#s=i,this.#e=n,this.hasListeners()&&(l(t,i).forEach(t=>{t.destroy()}),l(i,t).forEach(t=>{t.subscribe(e=>{this.#u(t,e)})}),this.#d()))})}getCurrentResult(){return this.#e}getQueries(){return this.#s.map(t=>t.getCurrentQuery())}getObservers(){return this.#s}getOptimisticResult(t,e){let i=this.#h(t),n=i.map(t=>t.observer.getOptimisticResult(t.defaultedQueryOptions));return[n,t=>this.#c(t??n,e),()=>this.#p(n,i)]}#p(t,e){return e.map((i,n)=>{let s=t[n];return i.defaultedQueryOptions.notifyOnChangeProps?s:i.observer.trackResult(s,t=>{e.forEach(e=>{e.observer.trackProp(t)})})})}#c(t,e){return e?(this.#r&&this.#e===this.#o&&e===this.#a||(this.#a=e,this.#o=this.#e,this.#r=(0,o.BH)(this.#r,e(t))),this.#r):t}#h(t){let e=new Map(this.#s.map(t=>[t.options.queryHash,t])),i=[];return t.forEach(t=>{let n=this.#t.defaultQueryOptions(t),s=e.get(n.queryHash);s?i.push({defaultedQueryOptions:n,observer:s}):i.push({defaultedQueryOptions:n,observer:new r.$(this.#t,n)})}),i}#u(t,e){let i=this.#s.indexOf(t);-1!==i&&(this.#e=function(t,e,i){let n=t.slice(0);return n[e]=i,n}(this.#e,i,e),this.#d())}#d(){if(this.hasListeners()){let t=this.#r,e=this.#p(this.#e,this.#l);t!==this.#c(e,this.#n?.combine)&&s.jG.batch(()=>{this.listeners.forEach(t=>{t(this.#e)})})}}},h=i(8693),d=i(24903),c=i(18228),p=i(16142),m=i(76935);function f({queries:t,...e},i){let a=(0,h.jE)(i),l=(0,d.w)(),f=(0,c.h)(),y=n.useMemo(()=>t.map(t=>{let e=a.defaultQueryOptions(t);return e._optimisticResults=l?"isRestoring":"optimistic",e}),[t,a,l]);y.forEach(t=>{(0,m.jv)(t),(0,p.LJ)(t,f)}),(0,p.wZ)(f);let[g]=n.useState(()=>new u(a,y,e)),[v,x,b]=g.getOptimisticResult(y,e.combine),w=!l&&!1!==e.subscribed;n.useSyncExternalStore(n.useCallback(t=>w?g.subscribe(s.jG.batchCalls(t)):o.lQ,[g,w]),()=>g.getCurrentResult(),()=>g.getCurrentResult()),n.useEffect(()=>{g.setQueries(y,e)},[y,e,g]);let T=v.some((t,e)=>(0,m.EU)(y[e],t))?v.flatMap((t,e)=>{let i=y[e];if(i){let e=new r.$(a,i);if((0,m.EU)(i,t))return(0,m.iL)(i,e,f);(0,m.nE)(t,l)&&(0,m.iL)(i,e,f)}return[]}):[];if(T.length>0)throw Promise.all(T);let S=v.find((t,e)=>{let i=y[e];return i&&(0,p.$1)({result:t,errorResetBoundary:f,throwOnError:i.throwOnError,query:a.getQueryCache().get(i.queryHash),suspense:i.suspense})});if(S?.error)throw S.error;return x(b())}},93613:(t,e,i)=>{i.d(e,{A:()=>n});let n=(0,i(62688).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},93905:(t,e,i)=>{i.d(e,{xQ:()=>r});var n=i(43210),s=i(48618);function r(t=!0){let e=(0,n.useContext)(s.t);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:a,register:o}=e,l=(0,n.useId)();(0,n.useEffect)(()=>{if(t)return o(l)},[t]);let u=(0,n.useCallback)(()=>t&&a&&a(l),[l,a,t]);return!i&&a?[!1,u]:[!0]}},94117:(t,e,i)=>{i.d(e,{i:()=>x});var n=i(43636),s=i(77439),r=i(33860);let a=(0,r.A)(.42,0,1,1),o=(0,r.A)(0,0,.58,1),l=(0,r.A)(.42,0,.58,1),u=t=>Array.isArray(t)&&"number"!=typeof t[0];var h=i(537),d=i(61452),c=i(82518),p=i(91215),m=i(95299),f=i(70226);let y={linear:d.l,easeIn:a,easeInOut:l,easeOut:o,circIn:m.po,circInOut:m.tn,circOut:m.yT,backIn:p.dg,backInOut:p.ZZ,backOut:p.Sz,anticipate:c.b},g=t=>"string"==typeof t,v=t=>{if((0,f.D)(t)){(0,h.V)(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,s]=t;return(0,r.A)(e,i,n,s)}return g(t)?((0,h.V)(void 0!==y[t],`Invalid easing type '${t}'`),y[t]):t};function x({duration:t=300,keyframes:e,times:i,ease:r="easeInOut"}){var a;let o=u(r)?r.map(v):v(r),h={done:!1,value:e[0]},d=(a=i&&i.length===e.length?i:(0,s.Z)(e),a.map(e=>e*t)),c=(0,n.G)(d,e,{ease:Array.isArray(o)?o:e.map(()=>o||l).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(h.value=c(e),h.done=e>=t,h)}}},95299:(t,e,i)=>{i.d(e,{po:()=>r,tn:()=>o,yT:()=>a});var n=i(82550),s=i(72851);let r=t=>1-Math.sin(Math.acos(t)),a=(0,s.G)(r),o=(0,n.V)(r)},97895:(t,e,i)=>{i.d(e,{UC:()=>O,VY:()=>U,ZD:()=>F,ZL:()=>j,bL:()=>R,hE:()=>I,hJ:()=>L,l9:()=>C,rc:()=>B});var n=i(43210),s=i(11273),r=i(98599),a=i(26134),o=i(70569),l=i(8730),u=i(60687),h="AlertDialog",[d,c]=(0,s.A)(h,[a.Hs]),p=(0,a.Hs)(),m=t=>{let{__scopeAlertDialog:e,...i}=t,n=p(e);return(0,u.jsx)(a.bL,{...n,...i,modal:!0})};m.displayName=h;var f=n.forwardRef((t,e)=>{let{__scopeAlertDialog:i,...n}=t,s=p(i);return(0,u.jsx)(a.l9,{...s,...n,ref:e})});f.displayName="AlertDialogTrigger";var y=t=>{let{__scopeAlertDialog:e,...i}=t,n=p(e);return(0,u.jsx)(a.ZL,{...n,...i})};y.displayName="AlertDialogPortal";var g=n.forwardRef((t,e)=>{let{__scopeAlertDialog:i,...n}=t,s=p(i);return(0,u.jsx)(a.hJ,{...s,...n,ref:e})});g.displayName="AlertDialogOverlay";var v="AlertDialogContent",[x,b]=d(v),w=(0,l.Dc)("AlertDialogContent"),T=n.forwardRef((t,e)=>{let{__scopeAlertDialog:i,children:s,...l}=t,h=p(i),d=n.useRef(null),c=(0,r.s)(e,d),m=n.useRef(null);return(0,u.jsx)(a.G$,{contentName:v,titleName:S,docsSlug:"alert-dialog",children:(0,u.jsx)(x,{scope:i,cancelRef:m,children:(0,u.jsxs)(a.UC,{role:"alertdialog",...h,...l,ref:c,onOpenAutoFocus:(0,o.m)(l.onOpenAutoFocus,t=>{t.preventDefault(),m.current?.focus({preventScroll:!0})}),onPointerDownOutside:t=>t.preventDefault(),onInteractOutside:t=>t.preventDefault(),children:[(0,u.jsx)(w,{children:s}),(0,u.jsx)(D,{contentRef:d})]})})})});T.displayName=v;var S="AlertDialogTitle",P=n.forwardRef((t,e)=>{let{__scopeAlertDialog:i,...n}=t,s=p(i);return(0,u.jsx)(a.hE,{...s,...n,ref:e})});P.displayName=S;var A="AlertDialogDescription",k=n.forwardRef((t,e)=>{let{__scopeAlertDialog:i,...n}=t,s=p(i);return(0,u.jsx)(a.VY,{...s,...n,ref:e})});k.displayName=A;var M=n.forwardRef((t,e)=>{let{__scopeAlertDialog:i,...n}=t,s=p(i);return(0,u.jsx)(a.bm,{...s,...n,ref:e})});M.displayName="AlertDialogAction";var E="AlertDialogCancel",V=n.forwardRef((t,e)=>{let{__scopeAlertDialog:i,...n}=t,{cancelRef:s}=b(E,i),o=p(i),l=(0,r.s)(e,s);return(0,u.jsx)(a.bm,{...o,...n,ref:l})});V.displayName=E;var D=({contentRef:t})=>{let e=`\`${v}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${v}\` by passing a \`${A}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${v}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return n.useEffect(()=>{document.getElementById(t.current?.getAttribute("aria-describedby"))||console.warn(e)},[e,t]),null},R=m,C=f,j=y,L=g,O=T,B=M,F=V,I=P,U=k},99275:(t,e,i)=>{i.d(e,{q:()=>n});let n={layout:0,mainThread:0,waapi:0}},99848:(t,e,i)=>{i.d(e,{X:()=>s,f:()=>n});let n=t=>1e3*t,s=t=>t/1e3}};