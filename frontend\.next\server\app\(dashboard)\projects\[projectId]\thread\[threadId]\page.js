(()=>{var e={};e.id=7169,e.ids=[7169],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26286:(e,s,t)=>{Promise.resolve().then(t.bind(t,37861))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35456:(e,s,t)=>{"use strict";t.d(s,{i:()=>o});var a=t(60687),r=t(43649),n=t(11860),l=t(29523),i=t(16189);function o({message:e,currentUsage:s,limit:t,accountId:o,onDismiss:c,isOpen:d}){let u=(0,i.useRouter)();return d?(0,a.jsx)("div",{className:"fixed bottom-4 right-4 z-[9999]",children:(0,a.jsx)("div",{className:"bg-destructive/15 backdrop-blur-sm border border-destructive/30 rounded-lg p-5 shadow-lg max-w-md",children:(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"flex-shrink-0 bg-destructive/20 p-2 rounded-full",children:(0,a.jsx)(r.A,{className:"h-5 w-5 text-destructive"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-destructive",children:"Usage Limit Reached"}),(0,a.jsx)(l.$,{variant:"ghost",size:"icon",onClick:c,className:"h-6 w-6 p-0 text-muted-foreground hover:text-foreground",children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:e}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:c,className:"text-xs",children:"Dismiss"}),(0,a.jsx)(l.$,{size:"sm",onClick:()=>u.push(`/settings/billing?accountId=${o}`),className:"text-xs bg-destructive hover:bg-destructive/90",children:"Upgrade Plan"})]})]})]})})}):null}},36262:(e,s,t)=>{"use strict";t.d(s,{H:()=>n});var a=t(60687);t(43210);var r=t(43649);function n({error:e}){return(0,a.jsx)("div",{className:"flex flex-1 items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"flex w-full max-w-md flex-col items-center gap-4 rounded-lg border bg-card p-6 text-center",children:[(0,a.jsx)("div",{className:"rounded-full bg-destructive/10 p-3",children:(0,a.jsx)(r.A,{className:"h-6 w-6 text-destructive"})}),(0,a.jsx)("h2",{className:"text-lg font-semibold text-destructive",children:"Thread Not Found"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:e.includes("JSON object requested, multiple (or no) rows returned")?"This thread either does not exist or you do not have access to it.":e})]})})}},36544:(e,s,t)=>{"use strict";t.d(s,{T:()=>r});var a=t(43210);function r(e){let s=(0,a.useRef)(null),t=(0,a.useRef)(!1),r=(0,a.useRef)(null),n=(0,a.useRef)(0),l=(0,a.useRef)(!1),i=(0,a.useCallback)(e=>{if(l.current||t.current)return;l.current=!0,console.log(`[VNC PRELOADER] Attempt ${n.current+1}/10 - Starting VNC preload:`,e);let a=document.createElement("iframe");a.src=e,a.style.position="absolute",a.style.left="-9999px",a.style.top="-9999px",a.style.width="1024px",a.style.height="768px",a.style.border="0",a.title="VNC Preloader";let o=setTimeout(()=>{if(console.log("[VNC PRELOADER] Load timeout - VNC service likely not ready"),a.parentNode&&a.parentNode.removeChild(a),n.current<10){n.current++,l.current=!1;let s=Math.min(2e3*Math.pow(1.5,n.current-1),15e3);console.log(`[VNC PRELOADER] Retrying in ${s}ms (attempt ${n.current+1}/10)`),r.current=setTimeout(()=>{i(e)},s)}else console.log("[VNC PRELOADER] Max retries reached, giving up on preloading"),l.current=!1},5e3);a.onload=()=>{clearTimeout(o),console.log("[VNC PRELOADER] ✅ VNC iframe preloaded successfully!"),t.current=!0,l.current=!1,s.current=a},a.onerror=()=>{if(clearTimeout(o),console.log("[VNC PRELOADER] VNC iframe failed to load (onerror)"),a.parentNode&&a.parentNode.removeChild(a),n.current<10){n.current++,l.current=!1;let s=Math.min(2e3*Math.pow(1.5,n.current-1),15e3);console.log(`[VNC PRELOADER] Retrying in ${s}ms (attempt ${n.current+1}/10)`),r.current=setTimeout(()=>{i(e)},s)}else console.log("[VNC PRELOADER] Max retries reached, giving up on preloading"),l.current=!1},document.body.appendChild(a),console.log("[VNC PRELOADER] VNC iframe added to DOM, waiting for load...")},[]);return{isPreloaded:t.current,preloadedIframe:s.current}}},37861:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\projects\\\\[projectId]\\\\thread\\\\[threadId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\[threadId]\\page.tsx","default")},45701:(e,s,t)=>{"use strict";t.d(s,{D:()=>J});var a=t(60687),r=t(29523),n=t(12941),l=t(13964),i=t(11860),o=t(62688);let c=(0,o.A)("PanelRightOpen",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M15 3v18",key:"14nvp0"}],["path",{d:"m10 15-3-3 3-3",key:"1pgupc"}]]);var d=t(18179);let u=(0,o.A)("Book",[["path",{d:"M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H19a1 1 0 0 1 1 1v18a1 1 0 0 1-1 1H6.5a1 1 0 0 1 0-5H20",key:"k3hazp"}]]);var m=t(81620),x=t(16189),h=t(52581),p=t(76242),g=t(43210),f=t(89667),j=t(2645),v=t(85726),N=t(82120),w=t(4780),b=t(55492),y=t(19766),C=t(8693),k=t(713),S=t(80013),A=t(34729),_=t(15079),P=t(96834),R=t(11437),E=t(93613),I=t(99270),$=t(96474),D=t(10022),L=t(12597),O=t(48730),T=t(81904),M=t(90131),q=t(13861),F=t(88233),V=t(41862),U=t(21342),z=t(93500),G=t(63503),H=t(48821);let K=[{value:"always",label:"Always Active",icon:R.A,color:"bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800"}],B=()=>(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"relative w-full",children:(0,a.jsx)(v.Skeleton,{className:"h-10 w-full"})}),(0,a.jsx)(v.Skeleton,{className:"h-10 w-32 ml-4"})]}),(0,a.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,a.jsx)("div",{className:"border rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0 space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(v.Skeleton,{className:"h-4 w-4"}),(0,a.jsx)(v.Skeleton,{className:"h-5 w-48"}),(0,a.jsx)(v.Skeleton,{className:"h-5 w-20"})]}),(0,a.jsx)(v.Skeleton,{className:"h-4 w-64"}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(v.Skeleton,{className:"h-4 w-full"}),(0,a.jsx)(v.Skeleton,{className:"h-4 w-3/4"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(v.Skeleton,{className:"h-5 w-24"}),(0,a.jsx)(v.Skeleton,{className:"h-4 w-20"})]}),(0,a.jsx)(v.Skeleton,{className:"h-4 w-16"})]})]}),(0,a.jsx)(v.Skeleton,{className:"h-8 w-8"})]})},e))})]}),W=({threadId:e})=>{let[s,t]=(0,g.useState)({isOpen:!1}),[n,l]=(0,g.useState)(null),[i,o]=(0,g.useState)(""),[c,d]=(0,g.useState)({name:"",description:"",content:"",usage_context:"always"}),{data:u,isLoading:m,error:x}=(0,H.Xe)(e),h=(0,H.v0)(),p=(0,H.kq)(),j=(0,H.QJ)(),v=()=>{d({name:"",description:"",content:"",usage_context:"always"}),t({isOpen:!0})},N=e=>{d({name:e.name,description:e.description||"",content:e.content,usage_context:e.usage_context}),t({entry:e,isOpen:!0})},b=()=>{t({isOpen:!1}),d({name:"",description:"",content:"",usage_context:"always"})},y=async t=>{if(t.preventDefault(),c.name.trim()&&c.content.trim())try{if(s.entry){let e={name:c.name!==s.entry.name?c.name:void 0,description:c.description!==s.entry.description?c.description:void 0,content:c.content!==s.entry.content?c.content:void 0,usage_context:c.usage_context!==s.entry.usage_context?c.usage_context:void 0};Object.values(e).some(e=>void 0!==e)&&await p.mutateAsync({entryId:s.entry.entry_id,data:e})}else await h.mutateAsync({threadId:e,data:c});b()}catch(e){console.error("Error saving knowledge base entry:",e)}},C=async e=>{try{await j.mutateAsync(e),l(null)}catch(e){console.error("Error deleting knowledge base entry:",e)}},k=async e=>{try{await p.mutateAsync({entryId:e.entry_id,data:{is_active:!e.is_active}})}catch(e){console.error("Error toggling entry status:",e)}},R=e=>K.find(s=>s.value===e)||K[0];if(m)return(0,a.jsx)(B,{});if(x)return(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(E.A,{className:"h-8 w-8 text-red-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:"Failed to load knowledge base"})]})});let W=u?.entries||[],Z=W.filter(e=>e.name.toLowerCase().includes(i.toLowerCase())||e.content.toLowerCase().includes(i.toLowerCase())||e.description&&e.description.toLowerCase().includes(i.toLowerCase()));return(0,a.jsxs)("div",{className:"space-y-6",children:[W.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"relative w-full",children:[(0,a.jsx)(I.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(f.p,{placeholder:"Search knowledge entries...",value:i,onChange:e=>o(e.target.value),className:"pl-9"})]}),(0,a.jsxs)(r.$,{onClick:v,className:"gap-2 ml-4",children:[(0,a.jsx)($.A,{className:"h-4 w-4"}),"Add Knowledge"]})]}),(0,a.jsx)("div",{className:"space-y-3",children:0===Z.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(I.A,{className:"h-8 w-8 mx-auto text-muted-foreground/50 mb-2"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No entries match your search"})]}):Z.map(e=>{let s=R(e.usage_context),t=s.icon;return(0,a.jsx)("div",{className:(0,w.cn)("group border rounded-lg p-4 transition-all",e.is_active?"border-border bg-card hover:border-border/80":"border-border/50 bg-muted/30 opacity-70"),children:(0,a.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0 space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(D.A,{className:"h-4 w-4 text-muted-foreground flex-shrink-0"}),(0,a.jsx)("h3",{className:"font-medium truncate",children:e.name}),!e.is_active&&(0,a.jsxs)(P.E,{variant:"secondary",className:"text-xs",children:[(0,a.jsx)(L.A,{className:"h-3 w-3 mr-1"}),"Disabled"]})]}),e.description&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-1",children:e.description}),(0,a.jsx)("p",{className:"text-sm text-foreground/80 line-clamp-2 leading-relaxed",children:e.content}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(P.E,{variant:"outline",className:(0,w.cn)("text-xs gap-1",s.color),children:[(0,a.jsx)(t,{className:"h-3 w-3"}),s.label]}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground flex items-center gap-1",children:[(0,a.jsx)(O.A,{className:"h-3 w-3"}),new Date(e.created_at).toLocaleDateString()]})]}),e.content_tokens&&(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:["~",e.content_tokens.toLocaleString()," tokens"]})]})]}),(0,a.jsxs)(U.rI,{children:[(0,a.jsx)(U.ty,{asChild:!0,children:(0,a.jsx)(r.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,a.jsx)(T.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(U.SQ,{align:"end",className:"w-36",children:[(0,a.jsxs)(U._2,{onClick:()=>N(e),children:[(0,a.jsx)(M.A,{className:"h-4 w-4"}),"Edit"]}),(0,a.jsx)(U._2,{onClick:()=>k(e),children:e.is_active?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(L.A,{className:"h-4 w-4"}),"Disable"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(q.A,{className:"h-4 w-4"}),"Enable"]})}),(0,a.jsx)(U.mB,{}),(0,a.jsxs)(U._2,{onClick:()=>l(e.entry_id),className:"text-destructive focus:bg-destructive/10 focus:text-destructive",children:[(0,a.jsx)(F.A,{className:"h-4 w-4 text-destructive"}),"Delete"]})]})]})]})},e.entry_id)})})]}),0===W.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"mx-auto w-24 h-24 bg-muted/50 rounded-full flex items-center justify-center mb-4",children:(0,a.jsx)(D.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-2",children:"No Knowledge Entries"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-6 max-w-md mx-auto",children:"Add knowledge entries to provide your agent with context, guidelines, and information it should always remember."}),(0,a.jsxs)(r.$,{onClick:v,className:"gap-2",children:[(0,a.jsx)($.A,{className:"h-4 w-4"}),"Create Your First Entry"]})]}),(0,a.jsx)(G.lG,{open:s.isOpen,onOpenChange:b,children:(0,a.jsxs)(G.Cf,{className:"max-w-2xl max-h-[85vh] overflow-hidden flex flex-col",children:[(0,a.jsx)(G.c7,{className:"flex-shrink-0",children:(0,a.jsxs)(G.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(D.A,{className:"h-5 w-5"}),s.entry?"Edit Knowledge Entry":"Add Knowledge Entry"]})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,a.jsxs)("form",{onSubmit:y,className:"space-y-6 p-1",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(S.Label,{htmlFor:"name",className:"text-sm font-medium",children:"Name *"}),(0,a.jsx)(f.p,{id:"name",value:c.name,onChange:e=>d(s=>({...s,name:e.target.value})),placeholder:"e.g., Company Guidelines, API Documentation",required:!0,className:"w-full"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(S.Label,{htmlFor:"usage_context",className:"text-sm font-medium",children:"Usage Context"}),(0,a.jsxs)(_.l6,{value:c.usage_context,onValueChange:e=>d(s=>({...s,usage_context:e})),children:[(0,a.jsx)(_.bq,{children:(0,a.jsx)(_.yv,{})}),(0,a.jsx)(_.gC,{children:K.map(e=>{let s=e.icon;return(0,a.jsx)(_.eb,{value:e.value,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(s,{className:"h-4 w-4"}),(0,a.jsx)("div",{children:(0,a.jsx)("div",{className:"font-medium",children:e.label})})]})},e.value)})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(S.Label,{htmlFor:"description",className:"text-sm font-medium",children:"Description"}),(0,a.jsx)(f.p,{id:"description",value:c.description,onChange:e=>d(s=>({...s,description:e.target.value})),placeholder:"Brief description of this knowledge (optional)"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(S.Label,{htmlFor:"content",className:"text-sm font-medium",children:"Content *"}),(0,a.jsx)(A.T,{id:"content",value:c.content,onChange:e=>d(s=>({...s,content:e.target.value})),placeholder:"Enter the knowledge content that your agent should know...",className:"min-h-[200px] resize-y",required:!0}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Approximately ",Math.ceil(c.content.length/4).toLocaleString()," tokens"]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-3 pt-4 border-t",children:[(0,a.jsx)(r.$,{type:"button",variant:"outline",onClick:b,children:"Cancel"}),(0,a.jsxs)(r.$,{type:"submit",disabled:!c.name.trim()||!c.content.trim()||h.isPending||p.isPending,className:"gap-2",children:[h.isPending||p.isPending?(0,a.jsx)(V.A,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)($.A,{className:"h-4 w-4"}),s.entry?"Save Changes":"Add Knowledge"]})]})]})})]})}),(0,a.jsx)(z.Lt,{open:!!n,onOpenChange:()=>l(null),children:(0,a.jsxs)(z.EO,{children:[(0,a.jsxs)(z.wd,{children:[(0,a.jsxs)(z.r7,{className:"flex items-center gap-2",children:[(0,a.jsx)(E.A,{className:"h-5 w-5 text-destructive"}),"Delete Knowledge Entry"]}),(0,a.jsx)(z.$v,{children:"This will permanently delete this knowledge entry. Your agent will no longer have access to this information."})]}),(0,a.jsxs)(z.ck,{children:[(0,a.jsx)(z.Zr,{children:"Cancel"}),(0,a.jsxs)(z.Rx,{onClick:()=>n&&C(n),className:"bg-destructive hover:bg-destructive/90",disabled:j.isPending,children:[j.isPending?(0,a.jsx)(V.A,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(F.A,{className:"h-4 w-4"}),"Delete Entry"]})]})]})})]})};var Z=t(15945);function J({threadId:e,projectId:s,projectName:t,onViewFiles:o,onToggleSidePanel:S,onProjectRenamed:A,isMobileView:_,debugMode:P}){(0,x.usePathname)();let[R,E]=(0,g.useState)(!1),[I,$]=(0,g.useState)(t),D=(0,g.useRef)(null),[L,O]=(0,g.useState)(!1),[T,M]=(0,g.useState)(!1),q=(0,C.jE)(),{flags:F,loading:V}=(0,Z.h)(["knowledge_base"]),U=F.knowledge_base,z=(0,N.a)()||_,{setOpenMobile:H}=(0,b.cL)(),K=(0,j.sS)(),B=()=>{E(!1),$(t)},J=async()=>{if(""===I.trim()){$(t),E(!1);return}if(I!==t)try{if(!s){h.oR.error("Cannot rename: Project ID is missing"),$(t),E(!1);return}if(await K.mutateAsync({projectId:s,data:{name:I}}))A?.(I),q.invalidateQueries({queryKey:k.X.project(s)});else throw Error("Failed to update project")}catch(s){let e=s instanceof Error?s.message:"Failed to rename project";console.error("Failed to rename project:",e),h.oR.error(e),$(t)}E(!1)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("header",{className:(0,w.cn)("bg-background sticky top-0 flex h-14 shrink-0 items-center gap-2 z-20 w-full",z&&"px-2"),children:[z&&(0,a.jsx)(r.$,{variant:"ghost",size:"icon",onClick:()=>H(!0),className:"h-9 w-9 mr-1","aria-label":"Open sidebar",children:(0,a.jsx)(n.A,{className:"h-4 w-4"})}),(0,a.jsx)("div",{className:"flex flex-1 items-center gap-2 px-3",children:R?(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(f.p,{ref:D,value:I,onChange:e=>$(e.target.value),onKeyDown:e=>{"Enter"===e.key?J():"Escape"===e.key&&B()},onBlur:J,className:"h-8 w-auto min-w-[180px] text-base font-medium",maxLength:50}),(0,a.jsx)(r.$,{variant:"ghost",size:"icon",className:"h-7 w-7",onClick:J,children:(0,a.jsx)(l.A,{className:"h-3.5 w-3.5"})}),(0,a.jsx)(r.$,{variant:"ghost",size:"icon",className:"h-7 w-7",onClick:B,children:(0,a.jsx)(i.A,{className:"h-3.5 w-3.5"})})]}):t&&"Project"!==t?(0,a.jsx)("div",{className:"text-base font-medium text-muted-foreground hover:text-foreground cursor-pointer flex items-center",onClick:()=>{$(t),E(!0),setTimeout(()=>{D.current?.focus(),D.current?.select()},0)},title:"Click to rename project",children:t}):(0,a.jsx)(v.Skeleton,{className:"h-5 w-32"})}),(0,a.jsxs)("div",{className:"flex items-center gap-1 pr-4",children:[P&&(0,a.jsx)("div",{className:"bg-amber-500 text-black text-xs px-2 py-0.5 rounded-md mr-2",children:"Debug"}),z?(0,a.jsx)(r.$,{variant:"ghost",size:"icon",onClick:S,className:"h-9 w-9 cursor-pointer","aria-label":"Toggle computer panel",children:(0,a.jsx)(c,{className:"h-4 w-4"})}):(0,a.jsxs)(p.Bc,{children:[(0,a.jsxs)(p.m_,{children:[(0,a.jsx)(p.k$,{asChild:!0,children:(0,a.jsx)(r.$,{variant:"ghost",size:"icon",onClick:o,className:"h-9 w-9 cursor-pointer",children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})}),(0,a.jsx)(p.ZI,{children:(0,a.jsx)("p",{children:"View Files in Task"})})]}),U&&(0,a.jsxs)(p.m_,{children:[(0,a.jsx)(p.k$,{asChild:!0,children:(0,a.jsx)(r.$,{variant:"ghost",size:"icon",onClick:()=>{M(!0)},className:"h-9 w-9 cursor-pointer",children:(0,a.jsx)(u,{className:"h-4 w-4"})})}),(0,a.jsx)(p.ZI,{children:(0,a.jsx)("p",{children:"Knowledge Base"})})]}),(0,a.jsxs)(p.m_,{children:[(0,a.jsx)(p.k$,{asChild:!0,children:(0,a.jsx)(r.$,{variant:"ghost",size:"icon",onClick:()=>{O(!0)},className:"h-9 w-9 cursor-pointer",children:(0,a.jsx)(m.A,{className:"h-4 w-4"})})}),(0,a.jsx)(p.ZI,{children:(0,a.jsx)("p",{children:"Share Chat"})})]})]})]})]}),(0,a.jsx)(y.Z,{isOpen:L,onClose:()=>O(!1),threadId:e,projectId:s}),(0,a.jsx)(G.lG,{open:T,onOpenChange:M,children:(0,a.jsx)(G.Cf,{className:"max-w-2xl max-h-[90vh] overflow-hidden p-0",children:(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsx)(G.c7,{className:"px-6 py-4",children:(0,a.jsxs)(G.L3,{className:"flex items-center gap-2 text-lg",children:[(0,a.jsx)(u,{className:"h-5 w-5"}),"Knowledge Base"]})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto p-6",children:(0,a.jsx)(W,{threadId:e})})]})})})]})}},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56527:e=>{"use strict";e.exports=import("shiki")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66038:(e,s,t)=>{Promise.resolve().then(t.bind(t,82323))},70400:(e,s,t)=>{"use strict";t.r(s),t.d(s,{"60120624b62a67d0046abca062aae687cbb5ebc44a":()=>a.vI,"602190608cb4aada86562e5e5997e6bb44fbe95e4e":()=>a.$w,"60836a64bf90333e8dead87703a0c5a32e95fa0f8f":()=>a.gj});var a=t(67834)},73136:e=>{"use strict";e.exports=require("node:url")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81120:(e,s,t)=>{"use strict";t.d(s,{v:()=>m});var a=t(60687);t(43210);var r=t(16189),n=t(92363),l=t(78200),i=t(45583),o=t(48730),c=t(56085),d=t(63503),u=t(29523);function m({open:e,onOpenChange:s,onDismiss:t}){let m=(0,r.useRouter)();return(0,a.jsx)(d.lG,{open:e,onOpenChange:s,children:(0,a.jsxs)(d.Cf,{onPointerDownOutside:e=>e.preventDefault(),className:"sm:max-w-md",children:[(0,a.jsxs)(d.c7,{children:[(0,a.jsxs)(d.L3,{className:"flex items-center",children:[(0,a.jsx)(n.A,{className:"h-5 w-5 mr-2 text-primary"}),"Unlock the Full Suna Experience"]}),(0,a.jsx)(d.rr,{children:"You're currently using Suna's free tier with limited capabilities. Upgrade now to access our most powerful AI model."})]}),(0,a.jsxs)("div",{className:"py-4",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-slate-700 dark:text-slate-300 mb-3",children:"Pro Benefits"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"rounded-full bg-secondary/10 p-2 flex-shrink-0 mt-0.5",children:(0,a.jsx)(l.A,{className:"h-4 w-4 text-secondary"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-slate-900 dark:text-slate-100",children:"Advanced AI Models"}),(0,a.jsx)("p",{className:"text-xs text-slate-500 dark:text-slate-400",children:"Get access to advanced models suited for complex tasks"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"rounded-full bg-secondary/10 p-2 flex-shrink-0 mt-0.5",children:(0,a.jsx)(i.A,{className:"h-4 w-4 text-secondary"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-slate-900 dark:text-slate-100",children:"Faster Responses"}),(0,a.jsx)("p",{className:"text-xs text-slate-500 dark:text-slate-400",children:"Get access to faster models that breeze through your tasks"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"rounded-full bg-secondary/10 p-2 flex-shrink-0 mt-0.5",children:(0,a.jsx)(o.A,{className:"h-4 w-4 text-secondary"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-slate-900 dark:text-slate-100",children:"Higher Usage Limits"}),(0,a.jsx)("p",{className:"text-xs text-slate-500 dark:text-slate-400",children:"Enjoy more conversations and longer run durations"})]})]})]})]}),(0,a.jsxs)(d.Es,{className:"flex gap-2",children:[(0,a.jsx)(u.$,{variant:"outline",onClick:t,children:"Maybe Later"}),(0,a.jsxs)(u.$,{onClick:()=>{m.push("/settings/billing"),s(!1),localStorage.setItem("suna_upgrade_dialog_displayed","true")},children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),"Upgrade Now"]})]})]})})}},81630:e=>{"use strict";e.exports=require("http")},82323:(e,s,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.r(s),t.d(s,{default:()=>k});var r=t(60687),n=t(43210),l=t.n(n),i=t(16189),o=t(62185),c=t(52581),d=t(53060),u=t(55492),m=t(26408),x=t(4780),h=t(82120);t(5475);var p=t(96260),g=t(83614),f=t(56367),j=t(92798),v=t(88018),N=t(88369),w=t(92404),b=t(36544),y=t(39665),C=e([d,p,w]);function k({params:e}){let{projectId:s,threadId:t}=l().use(e),a=(0,h.a)();(0,i.useSearchParams)();let[C,k]=(0,n.useState)(""),[S,A]=(0,n.useState)(!1),[_,P]=(0,n.useState)(!1),[R,E]=(0,n.useState)(null),[I,$]=(0,n.useState)(void 0),[D,L]=(0,n.useState)(!1),[O,T]=(0,n.useState)(!1),[M,q]=(0,n.useState)(!1),[F,V]=(0,n.useState)(void 0),U=(0,n.useRef)(null);(0,n.useRef)(null),(0,n.useRef)(null);let[z,G]=(0,n.useState)(!1),[H,K]=(0,n.useState)(!1);(0,n.useRef)(!1),(0,n.useRef)(!1);let{state:B,setOpen:W}=(0,u.cL)(),{messages:Z,setMessages:J,project:Q,sandboxId:X,projectName:Y,agentRunId:ee,setAgentRunId:es,agentStatus:et,setAgentStatus:ea,isLoading:er,error:en,initialLoadCompleted:el,threadQuery:ei,messagesQuery:eo,projectQuery:ec,agentRunsQuery:ed}=(0,N.uK)(t,s),{toolCalls:eu,setToolCalls:em,currentToolIndex:ex,setCurrentToolIndex:eh,isSidePanelOpen:ep,setIsSidePanelOpen:eg,autoOpenedPanel:ef,setAutoOpenedPanel:ej,externalNavIndex:ev,setExternalNavIndex:eN,handleToolClick:ew,handleStreamingToolCall:eb,toggleSidePanel:ey,handleSidePanelNavigate:eC,userClosedPanelRef:ek}=(0,N.$w)(Z,W,et),{showBillingAlert:eS,setShowBillingAlert:eA,billingData:e_,setBillingData:eP,checkBillingLimits:eR,billingStatusQuery:eE}=(0,N.dp)(Q?.account_id,et,el);(0,N.KW)({isSidePanelOpen:ep,setIsSidePanelOpen:eg,leftSidebarState:B,setLeftSidebarOpen:W,userClosedPanelRef:ek});let eI=(0,f.U)(),e$=(0,j.WZ)(),eD=(0,j.CV)(),{data:eL}=(0,y.jz)(t),eO=eL?.agent;ei.data?.metadata?.workflow_id;let{data:eT}=(0,v.Rs)();eT?.status;let eM=(0,n.useMemo)(()=>Q,[Q?.id,Q?.sandbox?.vnc_preview,Q?.sandbox?.pass]);(0,b.T)(eM);let eq=(0,n.useCallback)(e=>{},[]),eF=(e="smooth")=>{U.current?.scrollIntoView({behavior:e})},eV=(0,n.useCallback)(e=>{console.log(`[STREAM HANDLER] Received message: ID=${e.message_id}, Type=${e.type}`),e.message_id||console.warn(`[STREAM HANDLER] Received message is missing ID: Type=${e.type}, Content=${e.content?.substring(0,50)}...`),J(s=>s.some(s=>s.message_id===e.message_id)?s.map(s=>s.message_id===e.message_id?e:s):[...s,e]),"tool"===e.type&&ej(!1)},[J,ej]),eU=(0,n.useCallback)(e=>{switch(console.log(`[PAGE] Hook status changed: ${e}`),e){case"idle":case"completed":case"stopped":case"agent_not_running":case"error":case"failed":ea("idle"),es(null),ej(!1),["completed","stopped","agent_not_running","error","failed"].includes(e)&&eF("smooth");break;case"connecting":ea("connecting");break;case"streaming":ea("running")}},[ea,es,ej]),ez=(0,n.useCallback)(e=>{console.error(`[PAGE] Stream hook error: ${e}`),e.toLowerCase().includes("not found")||e.toLowerCase().includes("agent run is not running")||c.oR.error(`Stream Error: ${e}`)},[]),eG=(0,n.useCallback)(()=>{console.log(`[PAGE] Stream hook closed with final status: ${et}`)},[et]),{status:eH,textContent:eK,toolCall:eB,error:eW,agentRunId:eZ,startStreaming:eJ,stopStreaming:eQ}=(0,m.Z)({onMessage:eV,onStatusChange:eU,onError:ez,onClose:eG},t,J),eX=(0,n.useCallback)(async(e,s)=>{if(!e.trim())return;A(!0);let a={message_id:`temp-${Date.now()}`,thread_id:t,type:"user",is_llm_message:!1,content:e,metadata:"{}",created_at:new Date().toISOString(),updated_at:new Date().toISOString()};J(e=>[...e,a]),k(""),eF("smooth");try{let r=eI.mutateAsync({threadId:t,message:e}),n=e$.mutateAsync({threadId:t,options:{...s,agent_id:F}}),l=await Promise.allSettled([r,n]);if("rejected"===l[0].status){let e=l[0].reason;throw console.error("Failed to send message:",e),Error(`Failed to send message: ${e?.message||e}`)}if("rejected"===l[1].status){let e=l[1].reason;if(console.error("Failed to start agent:",e),e instanceof o.Ey){console.log("Caught BillingError:",e.detail),eP({currentUsage:e.detail.currentUsage,limit:e.detail.limit,message:e.detail.message||"Monthly usage limit reached. Please upgrade.",accountId:Q?.account_id||null}),eA(!0),J(e=>e.filter(e=>e.message_id!==a.message_id));return}throw Error(`Failed to start agent: ${e?.message||e}`)}let i=l[1].value;es(i.agent_run_id),eo.refetch(),ed.refetch()}catch(e){console.error("Error sending message or starting agent:",e),e instanceof o.Ey||c.oR.error(e instanceof Error?e.message:"Operation failed"),J(e=>e.filter(e=>e.message_id!==a.message_id))}finally{A(!1)}},[t,Q?.account_id,eI,e$,eo,ed,J,eP,eA,es]),eY=(0,n.useCallback)(async()=>{if(console.log("[PAGE] Requesting agent stop via hook."),ea("idle"),await eQ(),ee)try{await eD.mutateAsync(ee),ed.refetch()}catch(e){console.error("Error stopping agent:",e)}},[eQ,ee,eD,ed,ea]),e0=(0,n.useCallback)((e,s)=>{e?E(e):E(null),$(s),P(!0)},[]),e1=(0,n.useCallback)((e,s)=>e?(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("div",{className:"text-xs font-medium text-muted-foreground",children:"Assistant Message"}),(0,r.jsx)("div",{className:"rounded-md border bg-muted/50 p-3",children:(0,r.jsx)("div",{className:"text-xs prose prose-xs dark:prose-invert chat-markdown max-w-none",children:e})})]}):null,[]),e4=(0,n.useCallback)((e,s)=>e?(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("div",{className:"text-xs font-medium text-muted-foreground",children:"Tool Result"}),(0,r.jsx)("div",{className:`px-2 py-0.5 rounded-full text-xs ${s?"bg-green-50 text-green-700 dark:bg-green-900 dark:text-green-300":"bg-red-50 text-red-700 dark:bg-red-900 dark:text-red-300"}`,children:s?"Success":"Failed"})]}),(0,r.jsx)("div",{className:"rounded-md border bg-muted/50 p-3",children:(0,r.jsx)("div",{className:"text-xs prose prose-xs dark:prose-invert chat-markdown max-w-none",children:e})})]}):null,[]);return!el||er?(0,r.jsx)(g.y,{isSidePanelOpen:ep}):en?(0,r.jsx)(w.HW,{threadId:t,projectName:Y,projectId:Q?.id||"",project:Q,sandboxId:X,isSidePanelOpen:ep,onToggleSidePanel:ey,onViewFiles:e0,fileViewerOpen:_,setFileViewerOpen:P,fileToView:R,filePathList:I,toolCalls:eu,messages:Z,externalNavIndex:ev,agentStatus:et,currentToolIndex:ex,onSidePanelNavigate:eC,onSidePanelClose:()=>{eg(!1),ek.current=!0,ej(!0)},renderAssistantMessage:e1,renderToolResult:e4,isLoading:!el||er,showBillingAlert:eS,billingData:e_,onDismissBilling:()=>eA(!1),debugMode:O,isMobile:a,initialLoadCompleted:el,agentName:eO&&eO.name,children:(0,r.jsx)(w.HD,{error:en})}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(w.HW,{threadId:t,projectName:Y,projectId:Q?.id||"",project:Q,sandboxId:X,isSidePanelOpen:ep,onToggleSidePanel:ey,onProjectRenamed:eq,onViewFiles:e0,fileViewerOpen:_,setFileViewerOpen:P,fileToView:R,filePathList:I,toolCalls:eu,messages:Z,externalNavIndex:ev,agentStatus:et,currentToolIndex:ex,onSidePanelNavigate:eC,onSidePanelClose:()=>{eg(!1),ek.current=!0,ej(!0)},renderAssistantMessage:e1,renderToolResult:e4,isLoading:!el||er,showBillingAlert:eS,billingData:e_,onDismissBilling:()=>eA(!1),debugMode:O,isMobile:a,initialLoadCompleted:el,agentName:eO&&eO.name,children:[(0,r.jsx)(p.u9,{messages:Z,streamingTextContent:eK,streamingToolCall:eB,agentStatus:et,handleToolClick:ew,handleOpenFileViewer:e0,readOnly:!1,streamHookStatus:eH,sandboxId:X,project:Q,debugMode:O,agentName:eO&&eO.name,agentAvatar:eO&&eO.avatar}),(0,r.jsx)("div",{className:(0,x.cn)("fixed bottom-0 z-10 bg-gradient-to-t from-background via-background/90 to-transparent px-4 pt-8 transition-all duration-200 ease-in-out","expanded"===B?"left-[72px] md:left-[256px]":"left-[72px]",ep?"right-[90%] sm:right-[450px] md:right-[500px] lg:right-[550px] xl:right-[650px]":"right-0",a?"left-0 right-0":""),children:(0,r.jsx)("div",{className:(0,x.cn)("mx-auto",a?"w-full":"max-w-3xl"),children:(0,r.jsx)(d.V,{value:C,onChange:k,onSubmit:eX,placeholder:"Describe what you need help with...",loading:S,disabled:S||"running"===et||"connecting"===et,isAgentRunning:"running"===et||"connecting"===et,onStopAgent:eY,autoFocus:!er,onFileBrowse:e0,sandboxId:X||void 0,messages:Z,agentName:eO&&eO.name,selectedAgentId:F,onAgentSelect:V,toolCalls:eu,toolCallIndex:ex,showToolPreview:!ep&&eu.length>0,onExpandToolPreview:()=>{eg(!0),ek.current=!1}})})})]}),(0,r.jsx)(w.vw,{open:D,onOpenChange:L,onDismiss:()=>{L(!1),localStorage.setItem("suna_upgrade_dialog_displayed","true")}})]})}[d,p,w]=C.then?(await C)():C,a()}catch(e){a(e)}})},83636:(e,s,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.d(s,{H:()=>d});var r=t(60687);t(43210);var n=t(45701),l=t(7330),i=t(19477),o=t(35456),c=e([i]);function d({children:e,threadId:s,projectName:t,projectId:a,project:c,sandboxId:d,isSidePanelOpen:u,onToggleSidePanel:m,onProjectRenamed:x,onViewFiles:h,fileViewerOpen:p,setFileViewerOpen:g,fileToView:f,filePathList:j,toolCalls:v,messages:N,externalNavIndex:w,agentStatus:b,currentToolIndex:y,onSidePanelNavigate:C,onSidePanelClose:k,renderAssistantMessage:S,renderToolResult:A,isLoading:_,showBillingAlert:P,billingData:R,onDismissBilling:E,debugMode:I,isMobile:$,initialLoadCompleted:D,agentName:L}){return(0,r.jsxs)("div",{className:"flex h-screen",children:[I&&(0,r.jsx)("div",{className:"fixed top-16 right-4 bg-amber-500 text-black text-xs px-2 py-1 rounded-md shadow-md z-50",children:"Debug Mode"}),(0,r.jsxs)("div",{className:`flex flex-col flex-1 overflow-hidden transition-all duration-200 ease-in-out ${!D||u?"mr-[90%] sm:mr-[450px] md:mr-[500px] lg:mr-[550px] xl:mr-[650px]":""}`,children:[(0,r.jsx)(n.D,{threadId:s,projectName:t,projectId:a,onViewFiles:h,onToggleSidePanel:m,onProjectRenamed:x,isMobileView:$,debugMode:I}),e]}),(0,r.jsx)(i.s,{isOpen:u&&D,onClose:k,toolCalls:v,messages:N,externalNavigateToIndex:w,agentStatus:b,currentIndex:y,onNavigate:C,project:c||void 0,renderAssistantMessage:S,renderToolResult:A,isLoading:!D||_,onFileClick:h,agentName:L}),d&&(0,r.jsx)(l.S,{open:p,onOpenChange:g,sandboxId:d,initialFilePath:f,project:c||void 0,filePathList:j}),(0,r.jsx)(o.i,{message:R.message,currentUsage:R.currentUsage,limit:R.limit,accountId:R.accountId,onDismiss:E,isOpen:P})]})}i=(c.then?(await c)():c)[0],a()}catch(e){a(e)}})},84297:e=>{"use strict";e.exports=require("async_hooks")},86220:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.default,__next_app__:()=>d,pages:()=>c,routeModule:()=>u,tree:()=>o});var a=t(65239),r=t(48088),n=t(31369),l=t(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);t.d(s,i);let o={children:["",{children:["(dashboard)",{children:["projects",{children:["[projectId]",{children:["thread",{children:["[threadId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,37861)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\[threadId]\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,87774)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\[threadId]\\layout.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,33532)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,88524))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,93595)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,88524))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\projects\\[projectId]\\thread\\[threadId]\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(dashboard)/projects/[projectId]/thread/[threadId]/page",pathname:"/projects/[projectId]/thread/[threadId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},87774:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(37413);function r({children:e}){return(0,a.jsx)(a.Fragment,{children:e})}},88369:(e,s,t)=>{"use strict";t.d(s,{dp:()=>x,KW:()=>h,uK:()=>c,$w:()=>d});var a=t(43210),r=t(52581),n=t(58214),l=t(56367),i=t(87842),o=t(92798);function c(e,s){let[t,r]=(0,a.useState)([]),[c,d]=(0,a.useState)(null),[u,m]=(0,a.useState)(null),[x,h]=(0,a.useState)(""),[p,g]=(0,a.useState)(null),[f,j]=(0,a.useState)("idle"),[v,N]=(0,a.useState)(!0),[w,b]=(0,a.useState)(null),y=(0,a.useRef)(!1);(0,a.useRef)(!1),(0,a.useRef)(!1),(0,a.useRef)(!1);let C=(0,n.K2)(e),k=(0,l.C)(e),S=(0,i.BR)(s),A=(0,o.P3)(e);return{messages:t,setMessages:r,project:c,sandboxId:u,projectName:x,agentRunId:p,setAgentRunId:g,agentStatus:f,setAgentStatus:j,isLoading:v,error:w,initialLoadCompleted:y.current,threadQuery:C,messagesQuery:k,projectQuery:S,agentRunsQuery:A}}function d(e,s,t){let[n,l]=(0,a.useState)([]),[i,o]=(0,a.useState)(0),[c,d]=(0,a.useState)(!1),[u,m]=(0,a.useState)(!1),[x,h]=(0,a.useState)(void 0),p=(0,a.useRef)(!1),g=(0,a.useRef)(!1),f=(0,a.useCallback)(()=>{d(e=>{let t=!e;return t||(p.current=!0),t&&s(!1),t})},[s]),j=(0,a.useCallback)(e=>{o(e),g.current=!0},[]),v=(0,a.useRef)(new Map),N=(0,a.useCallback)((s,t)=>{if(!s){console.warn("Clicked assistant message ID is null. Cannot open side panel."),r.oR.warning("Cannot view details: Assistant message ID is missing.");return}p.current=!1,g.current=!0,console.log("[PAGE] Tool Click Triggered. Assistant Message ID:",s,"Tool Name:",t);let a=v.current.get(s);if(void 0!==a)console.log(`[PAGE] Found tool call at index ${a} for assistant message ${s}`),h(a),o(a),d(!0),setTimeout(()=>h(void 0),100);else{if(console.warn(`[PAGE] Could not find matching tool call in toolCalls array for assistant message ID: ${s}`),e.find(e=>e.message_id===s&&"assistant"===e.type)){let t=e.filter(e=>"assistant"===e.type&&e.message_id).findIndex(e=>e.message_id===s);if(-1!==t&&t<n.length){console.log(`[PAGE] Using fallback: found tool at index ${t}`),h(t),o(t),d(!0),setTimeout(()=>h(void 0),100);return}}r.oR.info("Could not find details for this tool call.")}},[e,n]),w=(0,a.useCallback)(e=>{if(!e)return;let s=e.name||e.xml_tag_name||"Unknown Tool",t=s.replace(/_/g,"-").toLowerCase();if(console.log("[STREAM] Received tool call:",t,"(raw:",s,")"),p.current)return;let a=e.arguments||"",r=a;if(t.includes("command")&&!a.includes("<execute-command>"))r=`<execute-command>${a}</execute-command>`;else if(t.includes("file")||"create-file"===t||"delete-file"===t||"full-file-rewrite"===t){let e=["create-file","delete-file","full-file-rewrite"].find(e=>t===e);if(e)if(a.includes(`<${e}>`)||a.includes("file_path="))r=a;else{let s=a.trim();r=s&&!s.startsWith("<")?`<${e} file_path="${s}">`:`<${e}>${a}</${e}>`}}let i={assistantCall:{name:t,content:r,timestamp:new Date().toISOString()},toolResult:{content:"STREAMING",isSuccess:!0,timestamp:new Date().toISOString()}};l(e=>{let s=e.findIndex(e=>e.toolResult?.content==="STREAMING");if(-1===s||e[s].assistantCall.name!==t)return[...e,i];{let t=[...e];return t[s]={...t[s],assistantCall:{...t[s].assistantCall,content:r}},t}}),g.current||o(e=>n.length+1-1),d(!0)},[n.length]);return{toolCalls:n,setToolCalls:l,currentToolIndex:i,setCurrentToolIndex:o,isSidePanelOpen:c,setIsSidePanelOpen:d,autoOpenedPanel:u,setAutoOpenedPanel:m,externalNavIndex:x,setExternalNavIndex:h,handleToolClick:N,handleStreamingToolCall:w,toggleSidePanel:f,handleSidePanelNavigate:j,userClosedPanelRef:p}}t(36990),t(75330);var u=t(5475),m=t(49499);function x(e,s,t){let[r,n]=(0,a.useState)(!1),[l,i]=(0,a.useState)({});(0,a.useRef)("idle");let o=(0,m.p)(),c=(0,a.useCallback)(async()=>{if((0,u.Jn)())return console.log("Running in local development mode - billing checks are disabled"),!1;try{await o.refetch();let s=o.data;if(s&&!s.can_run)return i({currentUsage:s.subscription?.minutes_limit||0,limit:s.subscription?.minutes_limit||0,message:s.message||"Usage limit reached",accountId:e||null}),n(!0),!0;return!1}catch(e){return console.error("Error checking billing status:",e),!1}},[e,o]);return{showBillingAlert:r,setShowBillingAlert:n,billingData:l,setBillingData:i,checkBillingLimits:c,billingStatusQuery:o}}function h({isSidePanelOpen:e,setIsSidePanelOpen:s,leftSidebarState:t,setLeftSidebarOpen:a,userClosedPanelRef:r}){}},91645:e=>{"use strict";e.exports=require("net")},92404:(e,s,t)=>{"use strict";t.a(e,async(e,a)=>{try{t.d(s,{HD:()=>r.H,HW:()=>l.H,vw:()=>n.v});var r=t(36262),n=t(81120),l=t(83636),i=e([l]);l=(i.then?(await i)():i)[0],a()}catch(e){a(e)}})},94735:e=>{"use strict";e.exports=require("events")}};var s=require("../../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[7719,5193,4267,7096,1265,3530,7156,7976,4097,6914,9307,5811,9781,9697,3175,3667,8188,3806,1841,5558,6947,5966,1155,9490,3060,8607,846],()=>t(86220));module.exports=a})();