"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7453],{1309:(t,e,r)=>{r.d(e,{v:()=>n});var s=r(65649);class n{constructor(){this.subscriptions=[]}add(t){return(0,s.Kq)(this.subscriptions,t),()=>(0,s.Ai)(this.subscriptions,t)}notify(t,e,r){let s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,r);else for(let n=0;n<s;n++){let s=this.subscriptions[n];s&&s(t,e,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},2098:(t,e,r)=>{r.d(e,{W:()=>s});let s={}},9942:(t,e,r)=>{r.d(e,{a:()=>s});let s=t=>Math.round(1e5*t)/1e5},18579:(t,e,r)=>{r.d(e,{Q:()=>s});let s={value:null,addProjectionMetrics:null}},21018:(t,e,r)=>{r.d(e,{$:()=>i,q:()=>a});var s=r(38459);let n=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,i=(t,e)=>r=>!!("string"==typeof r&&n.test(r)&&r.startsWith(t)||e&&null!=r&&Object.prototype.hasOwnProperty.call(r,e)),a=(t,e,r)=>n=>{if("string"!=typeof n)return n;let[i,a,o,u]=n.match(s.S);return{[t]:parseFloat(i),[e]:parseFloat(a),[r]:parseFloat(o),alpha:void 0!==u?parseFloat(u):1}}},21116:(t,e,r)=>{r.d(e,{l:()=>s});let s=t=>t},22258:(t,e,r)=>{r.d(e,{s:()=>n});var s=r(58990);function n(t){return(0,s.G)(t)&&"offsetHeight"in t}},25590:(t,e,r)=>{r.d(e,{X4:()=>i,ai:()=>n,hs:()=>a});var s=r(98167);let n={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},i={...n,transform:t=>(0,s.q)(0,1,t)},a={...n,default:1}},31324:(t,e,r)=>{r.d(e,{j:()=>q});var s=r(95255),n=r(92901),i=r(95233),a=r(78326),o=r(87183);function u(t,e,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?t+(e-t)*6*r:r<.5?e:r<2/3?t+(e-t)*(2/3-r)*6:t}var l=r(97379);function h(t,e){return r=>r>0?e:t}var d=r(50105),p=r(94449);let c=(t,e,r)=>{let s=t*t,n=r*(e*e-s)+s;return n<0?0:Math.sqrt(n)},f=[a.u,l.B,o.V],v=t=>f.find(e=>e.test(t));function m(t){let e=v(t);if((0,p.$)(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let r=e.parse(t);return e===o.V&&(r=function({hue:t,saturation:e,lightness:r,alpha:s}){t/=360,r/=100;let n=0,i=0,a=0;if(e/=100){let s=r<.5?r*(1+e):r+e-r*e,o=2*r-s;n=u(o,s,t+1/3),i=u(o,s,t),a=u(o,s,t-1/3)}else n=i=a=r;return{red:Math.round(255*n),green:Math.round(255*i),blue:Math.round(255*a),alpha:s}}(r)),r}let g=(t,e)=>{let r=m(t),s=m(e);if(!r||!s)return h(t,e);let n={...r};return t=>(n.red=c(r.red,s.red,t),n.green=c(r.green,s.green,t),n.blue=c(r.blue,s.blue,t),n.alpha=(0,d.k)(r.alpha,s.alpha,t),l.B.transform(n))},y=new Set(["none","hidden"]);var b=r(49792);function A(t,e){return r=>(0,d.k)(t,e,r)}function w(t){return"number"==typeof t?A:"string"==typeof t?(0,s.p)(t)?h:n.y.test(t)?g:k:Array.isArray(t)?V:"object"==typeof t?n.y.test(t)?g:F:h}function V(t,e){let r=[...t],s=r.length,n=t.map((t,r)=>w(t)(t,e[r]));return t=>{for(let e=0;e<s;e++)r[e]=n[e](t);return r}}function F(t,e){let r={...t,...e},s={};for(let n in r)void 0!==t[n]&&void 0!==e[n]&&(s[n]=w(t[n])(t[n],e[n]));return t=>{for(let e in s)r[e]=s[e](t);return r}}let k=(t,e)=>{let r=i.f.createTransformer(e),s=(0,i.V)(t),n=(0,i.V)(e);return s.indexes.var.length===n.indexes.var.length&&s.indexes.color.length===n.indexes.color.length&&s.indexes.number.length>=n.indexes.number.length?y.has(t)&&!n.values.length||y.has(e)&&!s.values.length?function(t,e){return y.has(t)?r=>r<=0?t:e:r=>r>=1?e:t}(t,e):(0,b.F)(V(function(t,e){let r=[],s={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let i=e.types[n],a=t.indexes[i][s[i]],o=t.values[a]??0;r[n]=o,s[i]++}return r}(s,n),n.values),r):((0,p.$)(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),h(t,e))};function q(t,e,r){return"number"==typeof t&&"number"==typeof e&&"number"==typeof r?(0,d.k)(t,e,r):w(t)(t,e)}},38154:(t,e,r)=>{r.d(e,{G:()=>h});var s=r(31324),n=r(94449),i=r(98167),a=r(2098),o=r(21116),u=r(49792),l=r(38865);function h(t,e,{clamp:r=!0,ease:d,mixer:p}={}){let c=t.length;if((0,n.V)(c===e.length,"Both input and output ranges must be the same length"),1===c)return()=>e[0];if(2===c&&e[0]===e[1])return()=>e[1];let f=t[0]===t[1];t[0]>t[c-1]&&(t=[...t].reverse(),e=[...e].reverse());let v=function(t,e,r){let n=[],i=r||a.W.mix||s.j,l=t.length-1;for(let r=0;r<l;r++){let s=i(t[r],t[r+1]);if(e){let t=Array.isArray(e)?e[r]||o.l:e;s=(0,u.F)(t,s)}n.push(s)}return n}(e,d,p),m=v.length,g=r=>{if(f&&r<t[0])return e[0];let s=0;if(m>1)for(;s<t.length-2&&!(r<t[s+1]);s++);let n=(0,l.q)(t[s],t[s+1],r);return v[s](n)};return r?e=>g((0,i.q)(t[0],t[c-1],e)):g}},38459:(t,e,r)=>{r.d(e,{S:()=>s});let s=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu},38865:(t,e,r)=>{r.d(e,{q:()=>s});let s=(t,e,r)=>{let s=e-t;return 0===s?1:(r-t)/s}},42801:(t,e,r)=>{r.d(e,{B:()=>s});let s="undefined"!=typeof window},46182:(t,e,r)=>{r.d(e,{Gt:()=>i,PP:()=>u,WG:()=>a,uv:()=>o});var s=r(66911),n=r(21116);let{schedule:i,cancel:a,state:o,steps:u}=(0,s.I)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:n.l,!0)},49792:(t,e,r)=>{r.d(e,{F:()=>n});let s=(t,e)=>r=>e(t(r)),n=(...t)=>t.reduce(s)},50105:(t,e,r)=>{r.d(e,{k:()=>s});let s=(t,e,r)=>t+(e-t)*r},57482:(t,e,r)=>{r.d(e,{p:()=>s});function s(t){let e;return()=>(void 0===e&&(e=t()),e)}},58840:(t,e,r)=>{let s;r.d(e,{k:()=>o});var n=r(46182),i=r(2098);function a(){s=void 0}let o={now:()=>(void 0===s&&o.set(n.uv.isProcessing||i.W.useManualTiming?n.uv.timestamp:performance.now()),s),set:t=>{s=t,queueMicrotask(a)}}},58990:(t,e,r)=>{r.d(e,{G:()=>s});function s(t){return"object"==typeof t&&null!==t}},64530:(t,e,r)=>{r.d(e,{f:()=>s});function s(t,e){return e?1e3/e*t:0}},65649:(t,e,r)=>{function s(t,e){-1===t.indexOf(e)&&t.push(e)}function n(t,e){let r=t.indexOf(e);r>-1&&t.splice(r,1)}r.d(e,{Ai:()=>n,Kq:()=>s})},66911:(t,e,r)=>{r.d(e,{I:()=>a});let s=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];var n=r(18579),i=r(2098);function a(t,e){let r=!1,a=!0,o={delta:0,timestamp:0,isProcessing:!1},u=()=>r=!0,l=s.reduce((t,r)=>(t[r]=function(t,e){let r=new Set,s=new Set,i=!1,a=!1,o=new WeakSet,u={delta:0,timestamp:0,isProcessing:!1},l=0;function h(e){o.has(e)&&(d.schedule(e),t()),l++,e(u)}let d={schedule:(t,e=!1,n=!1)=>{let a=n&&i?r:s;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{s.delete(t),o.delete(t)},process:t=>{if(u=t,i){a=!0;return}i=!0,[r,s]=[s,r],r.forEach(h),e&&n.Q.value&&n.Q.value.frameloop[e].push(l),l=0,r.clear(),i=!1,a&&(a=!1,d.process(t))}};return d}(u,e?r:void 0),t),{}),{setup:h,read:d,resolveKeyframes:p,preUpdate:c,update:f,preRender:v,render:m,postRender:g}=l,y=()=>{let s=i.W.useManualTiming?o.timestamp:performance.now();r=!1,i.W.useManualTiming||(o.delta=a?1e3/60:Math.max(Math.min(s-o.timestamp,40),1)),o.timestamp=s,o.isProcessing=!0,h.process(o),d.process(o),p.process(o),c.process(o),f.process(o),v.process(o),m.process(o),g.process(o),o.isProcessing=!1,r&&e&&(a=!1,t(y))},b=()=>{r=!0,a=!0,o.isProcessing||t(y)};return{schedule:s.reduce((t,e)=>{let s=l[e];return t[e]=(t,e=!1,n=!1)=>(r||b(),s.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<s.length;e++)l[s[e]].cancel(t)},state:o,steps:l}}},69025:(t,e,r)=>{r.d(e,{E:()=>n});var s=r(12115);let n=r(42801).B?s.useLayoutEffect:s.useEffect},70315:(t,e,r)=>{r.d(e,{KN:()=>i,gQ:()=>l,px:()=>a,uj:()=>n,vh:()=>o,vw:()=>u});let s=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),n=s("deg"),i=s("%"),a=s("px"),o=s("vh"),u=s("vw"),l={...i,parse:t=>i.parse(t)/100,transform:t=>i.transform(100*t)}},76168:(t,e,r)=>{r.d(e,{M:()=>n});var s=r(12115);function n(t){let e=(0,s.useRef)(null);return null===e.current&&(e.current=t()),e.current}},78326:(t,e,r)=>{r.d(e,{u:()=>n});var s=r(97379);let n={test:(0,r(21018).$)("#"),parse:function(t){let e="",r="",s="",n="";return t.length>5?(e=t.substring(1,3),r=t.substring(3,5),s=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),r=t.substring(2,3),s=t.substring(3,4),n=t.substring(4,5),e+=e,r+=r,s+=s,n+=n),{red:parseInt(e,16),green:parseInt(r,16),blue:parseInt(s,16),alpha:n?parseInt(n,16)/255:1}},transform:s.B.transform}},80793:(t,e,r)=>{r.d(e,{K:()=>s});function s(t,e,r){if(t instanceof EventTarget)return[t];if("string"==typeof t){let s=document;e&&(s=e.current);let n=r?.[t]??s.querySelectorAll(t);return n?Array.from(n):[]}return Array.from(t)}},83385:(t,e,r)=>{r.d(e,{x:()=>n});var s=r(58990);function n(t){return(0,s.G)(t)&&"ownerSVGElement"in t}},87183:(t,e,r)=>{r.d(e,{V:()=>o});var s=r(25590),n=r(70315),i=r(9942),a=r(21018);let o={test:(0,a.$)("hsl","hue"),parse:(0,a.q)("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:r,alpha:a=1})=>"hsla("+Math.round(t)+", "+n.KN.transform((0,i.a)(e))+", "+n.KN.transform((0,i.a)(r))+", "+(0,i.a)(s.X4.transform(a))+")"}},91762:(t,e,r)=>{r.d(e,{Z:()=>i});var s=r(50105),n=r(38865);function i(t){let e=[0];return!function(t,e){let r=t[t.length-1];for(let i=1;i<=e;i++){let a=(0,n.q)(0,e,i);t.push((0,s.k)(r,1,a))}}(e,t.length-1),e}},92901:(t,e,r)=>{r.d(e,{y:()=>a});var s=r(78326),n=r(87183),i=r(97379);let a={test:t=>i.B.test(t)||s.u.test(t)||n.V.test(t),parse:t=>i.B.test(t)?i.B.parse(t):n.V.test(t)?n.V.parse(t):s.u.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?i.B.transform(t):n.V.transform(t)}},94449:(t,e,r)=>{r.d(e,{$:()=>s,V:()=>n});let s=()=>{},n=()=>{}},95233:(t,e,r)=>{r.d(e,{V:()=>h,f:()=>f});var s=r(92901);let n=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;var i=r(38459),a=r(9942);let o="number",u="color",l=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function h(t){let e=t.toString(),r=[],n={color:[],number:[],var:[]},i=[],a=0,h=e.replace(l,t=>(s.y.test(t)?(n.color.push(a),i.push(u),r.push(s.y.parse(t))):t.startsWith("var(")?(n.var.push(a),i.push("var"),r.push(t)):(n.number.push(a),i.push(o),r.push(parseFloat(t))),++a,"${}")).split("${}");return{values:r,split:h,indexes:n,types:i}}function d(t){return h(t).values}function p(t){let{split:e,types:r}=h(t),n=e.length;return t=>{let i="";for(let l=0;l<n;l++)if(i+=e[l],void 0!==t[l]){let e=r[l];e===o?i+=(0,a.a)(t[l]):e===u?i+=s.y.transform(t[l]):i+=t[l]}return i}}let c=t=>"number"==typeof t?0:t,f={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(i.S)?.length||0)+(t.match(n)?.length||0)>0},parse:d,createTransformer:p,getAnimatableNone:function(t){let e=d(t);return p(t)(e.map(c))}}},95255:(t,e,r)=>{r.d(e,{j:()=>n,p:()=>a});let s=t=>e=>"string"==typeof e&&e.startsWith(t),n=s("--"),i=s("var(--"),a=t=>!!i(t)&&o.test(t.split("/*")[0].trim()),o=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu},96299:(t,e,r)=>{r.d(e,{J:()=>s});let s=(0,r(57482).p)(()=>void 0!==window.ScrollTimeline)},97379:(t,e,r)=>{r.d(e,{B:()=>l});var s=r(25590),n=r(9942),i=r(21018),a=r(98167);let o=t=>(0,a.q)(0,255,t),u={...s.ai,transform:t=>Math.round(o(t))},l={test:(0,i.$)("rgb","red"),parse:(0,i.q)("red","green","blue"),transform:({red:t,green:e,blue:r,alpha:i=1})=>"rgba("+u.transform(t)+", "+u.transform(e)+", "+u.transform(r)+", "+(0,n.a)(s.X4.transform(i))+")"}},98167:(t,e,r)=>{r.d(e,{q:()=>s});let s=(t,e,r)=>r>e?e:r<t?t:r},99967:(t,e,r)=>{r.d(e,{OQ:()=>h,bt:()=>u});var s=r(58840),n=r(46182),i=r(1309),a=r(64530);let o=t=>!isNaN(parseFloat(t)),u={current:void 0};class l{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let r=s.k.now();if(this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=s.k.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=o(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new i.v);let r=this.events[t].add(e);return"change"===t?()=>{r(),n.Gt.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,r){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-r}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return u.current&&u.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let t=s.k.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,a.f)(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function h(t,e){return new l(t,e)}}}]);