(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5601],{26126:(e,t,a)=>{"use strict";a.d(t,{E:()=>o});var r=a(95155);a(12115);var n=a(99708),s=a(74466),d=a(59434);let i=(0,s.F)("inline-flex items-center justify-center rounded-lg border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground",new:"text-purple-600 dark:text-purple-300 bg-purple-600/30 dark:bg-purple-600/30",beta:"text-blue-600 dark:text-blue-300 bg-blue-600/30 dark:bg-blue-600/30",highlight:"text-green-800 dark:text-green-300 bg-green-600/30 dark:bg-green-600/30"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:a,asChild:s=!1,...o}=e,l=s?n.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,d.cn)(i({variant:a}),t),...o})}},28134:(e,t,a)=>{Promise.resolve().then(a.bind(a,96233))},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>o,r:()=>i});var r=a(95155);a(12115);var n=a(99708),s=a(74466),d=a(59434);let i=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",node_outline:"bg-transparent border border-primary/10",node_secondary:"px-0 bg-transparent hover:opacity-60",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-lg gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-lg px-6 has-[>svg]:px-4",icon:"size-9",node_secondary:"px-0"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:s,asChild:o=!1,...l}=e,c=o?n.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,d.cn)(i({variant:a,size:s,className:t})),...l})}},35706:(e,t,a)=>{"use strict";a.d(t,{SubmitButton:()=>c});var r=a(95155),n=a(47650),s=a(12115),d=a(30285),i=a(55365),o=a(1243);let l={message:""};function c(e){let{children:t,formAction:a,errorMessage:c,pendingText:u="Submitting...",...x}=e,{pending:m,action:h}=(0,n.useFormStatus)(),[v,g]=(0,s.useActionState)(a,l),b=m&&h===g;return(0,r.jsxs)("div",{className:"flex flex-col gap-y-4 w-full",children:[!!(c||(null==v?void 0:v.message))&&(0,r.jsxs)(i.Fc,{variant:"destructive",className:"w-full",children:[(0,r.jsx)(o.A,{className:"h-4 w-4"}),(0,r.jsx)(i.TN,{children:c||(null==v?void 0:v.message)})]}),(0,r.jsx)("div",{children:(0,r.jsx)(d.$,{...x,type:"submit","aria-disabled":m,formAction:g,children:b?u:t})})]})}},44838:(e,t,a)=>{"use strict";a.d(t,{I:()=>c,SQ:()=>l,V0:()=>v,_2:()=>u,hO:()=>x,lp:()=>m,mB:()=>h,rI:()=>i,ty:()=>o});var r=a(95155);a(12115);var n=a(48698),s=a(5196),d=a(59434);function i(e){let{...t}=e;return(0,r.jsx)(n.bL,{"data-slot":"dropdown-menu",...t})}function o(e){let{...t}=e;return(0,r.jsx)(n.l9,{"data-slot":"dropdown-menu-trigger",...t})}function l(e){let{className:t,sideOffset:a=4,...s}=e;return(0,r.jsx)(n.ZL,{children:(0,r.jsx)(n.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-xl border p-1 shadow-md",t),...s})})}function c(e){let{...t}=e;return(0,r.jsx)(n.YJ,{"data-slot":"dropdown-menu-group",...t})}function u(e){let{className:t,inset:a,variant:s="default",...i}=e;return(0,r.jsx)(n.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":s,className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i})}function x(e){let{className:t,children:a,checked:i,...o}=e;return(0,r.jsxs)(n.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),checked:i,...o,children:[(0,r.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(n.VF,{children:(0,r.jsx)(s.A,{className:"size-4"})})}),a]})}function m(e){let{className:t,inset:a,...s}=e;return(0,r.jsx)(n.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,d.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...s})}function h(e){let{className:t,...a}=e;return(0,r.jsx)(n.wv,{"data-slot":"dropdown-menu-separator",className:(0,d.cn)("bg-border -mx-1 my-1 h-px",t),...a})}function v(e){let{className:t,...a}=e;return(0,r.jsx)("span",{"data-slot":"dropdown-menu-shortcut",className:(0,d.cn)("text-muted-foreground ml-auto text-xs tracking-widest",t),...a})}},47262:(e,t,a)=>{"use strict";a.d(t,{S:()=>i});var r=a(95155);a(12115);var n=a(76981),s=a(5196),d=a(59434);function i(e){let{className:t,...a}=e;return(0,r.jsx)(n.bL,{"data-slot":"checkbox",className:(0,d.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,r.jsx)(n.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,r.jsx)(s.A,{className:"size-3.5"})})})}},54165:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>u,Es:()=>m,L3:()=>h,LC:()=>c,c7:()=>x,lG:()=>i,rr:()=>v,zM:()=>o});var r=a(95155);a(12115);var n=a(15452),s=a(54416),d=a(59434);function i(e){let{...t}=e;return(0,r.jsx)(n.bL,{"data-slot":"dialog",...t})}function o(e){let{...t}=e;return(0,r.jsx)(n.l9,{"data-slot":"dialog-trigger",...t})}function l(e){let{...t}=e;return(0,r.jsx)(n.ZL,{"data-slot":"dialog-portal",...t})}function c(e){let{className:t,...a}=e;return(0,r.jsx)(n.hJ,{"data-slot":"dialog-overlay",className:(0,d.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50 backdrop-blur-xs",t),...a})}function u(e){let{className:t,children:a,...i}=e;return(0,r.jsxs)(l,{"data-slot":"dialog-portal",children:[(0,r.jsx)(c,{}),(0,r.jsxs)(n.UC,{"data-slot":"dialog-content",className:(0,d.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-2xl border p-6 shadow-lg duration-200",t),...i,children:[a,(0,r.jsxs)(n.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(s.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,d.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function m(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,d.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function h(e){let{className:t,...a}=e;return(0,r.jsx)(n.hE,{"data-slot":"dialog-title",className:(0,d.cn)("text-lg leading-none font-semibold",t),...a})}function v(e){let{className:t,...a}=e;return(0,r.jsx)(n.VY,{"data-slot":"dialog-description",className:(0,d.cn)("text-muted-foreground text-sm",t),...a})}},55365:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>i,TN:()=>l,XL:()=>o});var r=a(95155);a(12115);var n=a(74466),s=a(59434);let d=(0,n.F)("relative w-full rounded-xl border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:a,...n}=e;return(0,r.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,s.cn)(d({variant:a}),t),...n})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"alert-title",className:(0,s.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"alert-description",className:(0,s.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...a})}},59409:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>m,gC:()=>x,l6:()=>l,yv:()=>c});var r=a(95155);a(12115);var n=a(31992),s=a(66474),d=a(5196),i=a(47863),o=a(59434);function l(e){let{...t}=e;return(0,r.jsx)(n.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,r.jsx)(n.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:d,...i}=e;return(0,r.jsxs)(n.l9,{"data-slot":"select-trigger","data-size":a,className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i,children:[d,(0,r.jsx)(n.In,{asChild:!0,children:(0,r.jsx)(s.A,{className:"size-4 opacity-50"})})]})}function x(e){let{className:t,children:a,position:s="popper",...d}=e;return(0,r.jsx)(n.ZL,{children:(0,r.jsxs)(n.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:s,...d,children:[(0,r.jsx)(h,{}),(0,r.jsx)(n.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,r.jsx)(v,{})]})})}function m(e){let{className:t,children:a,...s}=e;return(0,r.jsxs)(n.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...s,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(n.VF,{children:(0,r.jsx)(d.A,{className:"size-4"})})}),(0,r.jsx)(n.p4,{children:a})]})}function h(e){let{className:t,...a}=e;return(0,r.jsx)(n.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(i.A,{className:"size-4"})})}function v(e){let{className:t,...a}=e;return(0,r.jsx)(n.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(s.A,{className:"size-4"})})}},59434:(e,t,a)=>{"use strict";a.d(t,{$3:()=>o,Hz:()=>i,W5:()=>l,cn:()=>d});var r=a(52596),n=a(81949),s=a(39688);function d(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}let i=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"rgba(180, 180, 180)";if(!e)return t;try{if("string"==typeof e&&e.startsWith("var(")){let t=document.createElement("div");t.style.color=e,document.body.appendChild(t);let a=window.getComputedStyle(t).color;return document.body.removeChild(t),n.formatRGBA(n.parse(a))}return n.formatRGBA(n.parse(e))}catch(e){return console.error("Color parsing failed:",e),t}},o=(e,t)=>e.startsWith("rgb")?n.formatRGBA(n.alpha(n.parse(e),t)):e;function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return e.length<=t?e:e.slice(0,t)+"..."}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>l,ZB:()=>i,Zp:()=>s,aR:()=>d,wL:()=>c});var r=a(95155);a(12115);var n=a(59434);function s(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},72305:(e,t,a)=>{"use strict";a.d(t,{U:()=>n});var r=a(34477);let n=(0,r.createServerReference)("7f8bed79c8654f95685745e61906af37f96a086236",r.callServer,void 0,r.findSourceMapURL,"createClient")},85057:(e,t,a)=>{"use strict";a.d(t,{Label:()=>d});var r=a(95155);a(12115);var n=a(40968),s=a(59434);function d(e){let{className:t,...a}=e;return(0,r.jsx)(n.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},85127:(e,t,a)=>{"use strict";a.d(t,{A0:()=>d,Table:()=>s,TableBody:()=>i,TableCell:()=>c,TableRow:()=>o,nd:()=>l});var r=a(95155);a(12115);var n=a(59434);function s(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,n.cn)("w-full caption-bottom text-sm",t),...a})})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,n.cn)("[&_tr]:border-b",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,n.cn)("[&_tr:last-child]:border-0",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,n.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,n.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,n.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}},96233:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>P});var r=a(95155),n=a(12115),s=a(72305),d=a(85127),i=a(26126),o=a(5623),l=a(44838),c=a(30285),u=a(54165),x=a(35706),m=a(85057),h=a(59409),v=a(34477);let g=(0,v.createServerReference)("60a2413f5458bf68ab3375ffb31a2aaeeca9c6e834",v.callServer,void 0,v.findSourceMapURL,"updateTeamMemberRole");var b=a(47262),f=a(35695);let p=[{label:"Owner",value:"owner"},{label:"Member",value:"member"}];function j(e){let{accountId:t,teamMember:a,isPrimaryOwner:s}=e,[d,i]=(0,n.useState)(a.account_role),o=(0,f.usePathname)();return(0,r.jsxs)("form",{className:"animate-in flex-1 flex flex-col w-full justify-center gap-y-6 text-foreground",children:[(0,r.jsx)("input",{type:"hidden",name:"accountId",value:t}),(0,r.jsx)("input",{type:"hidden",name:"userId",value:a.user_id}),(0,r.jsx)("input",{type:"hidden",name:"returnUrl",value:o}),(0,r.jsxs)("div",{className:"flex flex-col gap-y-2",children:[(0,r.jsx)(m.Label,{htmlFor:"accountRole",children:"Team Role"}),(0,r.jsxs)(h.l6,{value:d,onValueChange:i,name:"accountRole",children:[(0,r.jsx)(h.bq,{children:(0,r.jsx)(h.yv,{placeholder:"Member type"})}),(0,r.jsx)(h.gC,{children:p.map(e=>(0,r.jsx)(h.eb,{value:e.value,children:e.label},e.value))})]})]}),"owner"===d&&s&&(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(b.S,{id:"makePrimaryOwner",name:"makePrimaryOwner"}),(0,r.jsx)("label",{htmlFor:"makePrimaryOwner",className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:"Make this user the primary owner"})]}),(0,r.jsx)(x.SubmitButton,{formAction:g,pendingText:"Updating...",children:"Update Role"})]})}let y=(0,v.createServerReference)("60f4ca11706210f990045d3b7082fd1eb8b74af14c",v.callServer,void 0,v.findSourceMapURL,"removeTeamMember");function w(e){let{accountId:t,teamMember:a}=e,n=(0,f.usePathname)();return(0,r.jsxs)("form",{className:"animate-in flex-1 flex flex-col w-full justify-center gap-y-6 text-foreground",children:[(0,r.jsx)("input",{type:"hidden",name:"accountId",value:t}),(0,r.jsx)("input",{type:"hidden",name:"userId",value:a.user_id}),(0,r.jsx)("input",{type:"hidden",name:"returnUrl",value:n}),(0,r.jsx)(x.SubmitButton,{variant:"destructive",formAction:y,pendingText:"Removing...",children:"Remove member"})]})}function N(e){let{teamMember:t,accountId:a,isPrimaryOwner:s}=e,[d,i]=(0,n.useState)(!1),[x,m]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{d&&i(!1)},[t.account_role,d]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(l.rI,{children:[(0,r.jsx)(l.ty,{asChild:!0,children:(0,r.jsxs)(c.$,{variant:"ghost",className:"h-8 w-8 p-0 rounded-full hover:bg-hover-bg dark:hover:bg-hover-bg-dark",children:[(0,r.jsx)(o.A,{className:"h-4 w-4 text-foreground/70"}),(0,r.jsx)("span",{className:"sr-only",children:"Open menu"})]})}),(0,r.jsxs)(l.SQ,{className:"min-w-[160px] border-subtle dark:border-white/10 bg-card-bg dark:bg-background-secondary rounded-xl shadow-custom",children:[(0,r.jsx)(l._2,{onSelect:()=>i(!0),className:"rounded-md hover:bg-hover-bg cursor-pointer text-foreground/90",children:"Change role"}),(0,r.jsx)(l._2,{onSelect:()=>m(!0),className:"rounded-md hover:bg-hover-bg cursor-pointer text-red-500 dark:text-red-400",children:"Remove member"})]})]}),(0,r.jsx)(u.lG,{open:d,onOpenChange:i,children:(0,r.jsxs)(u.Cf,{className:"sm:max-w-[425px] border-subtle dark:border-white/10 bg-card-bg dark:bg-background-secondary rounded-2xl shadow-custom",children:[(0,r.jsxs)(u.c7,{children:[(0,r.jsx)(u.L3,{className:"text-card-title",children:"Update team member role"}),(0,r.jsx)(u.rr,{className:"text-foreground/70",children:"Update a member's role in your team"})]}),(0,r.jsx)(j,{teamMember:t,accountId:a,isPrimaryOwner:s})]})}),(0,r.jsx)(u.lG,{open:x,onOpenChange:m,children:(0,r.jsxs)(u.Cf,{className:"sm:max-w-[425px] border-subtle dark:border-white/10 bg-card-bg dark:bg-background-secondary rounded-2xl shadow-custom",children:[(0,r.jsxs)(u.c7,{children:[(0,r.jsx)(u.L3,{className:"text-card-title",children:"Remove team member"}),(0,r.jsx)(u.rr,{className:"text-foreground/70",children:"Are you sure you want to remove this user from the team?"})]}),(0,r.jsx)(w,{teamMember:t,accountId:a})]})})]})}async function k(e){var t;let{accountId:a}=e,n=await (0,s.U)(),{data:o}=await n.rpc("get_account_members",{account_id:a}),{data:l}=await n.auth.getUser(),c=null==o||null==(t=o.find(e=>{var t;return e.user_id===(null==l||null==(t=l.user)?void 0:t.id)}))?void 0:t.is_primary_owner;return(0,r.jsx)("div",{children:(0,r.jsx)(d.Table,{children:(0,r.jsx)(d.TableBody,{children:null==o?void 0:o.map(e=>(0,r.jsxs)(d.TableRow,{className:"hover:bg-hover-bg border-subtle dark:border-white/10",children:[(0,r.jsx)(d.TableCell,{children:(0,r.jsxs)("div",{className:"flex items-center gap-x-2",children:[(0,r.jsx)("span",{className:"font-medium text-card-title",children:e.name}),(0,r.jsx)(i.E,{variant:"owner"===e.account_role?"default":"outline",className:"owner"===e.account_role?"bg-primary hover:bg-primary/90":"text-foreground/70 border-subtle dark:border-white/10",children:e.is_primary_owner?"Primary Owner":e.account_role})]})}),(0,r.jsx)(d.TableCell,{children:(0,r.jsx)("span",{className:"text-sm text-foreground/70",children:e.email})}),(0,r.jsx)(d.TableCell,{className:"text-right",children:!e.is_primary_owner&&(0,r.jsx)(N,{teamMember:e,accountId:a,isPrimaryOwner:c})})]},e.user_id))})})})}var _=a(66695);let z=(0,v.createServerReference)("602cb633238b6ed821154b0de7624fe865fdf4a20d",v.callServer,void 0,v.findSourceMapURL,"createInvitation");var C=a(47650);let S=[{label:"24 Hour",value:"24_hour"},{label:"One time use",value:"one_time"}],T=[{label:"Owner",value:"owner"},{label:"Member",value:"member"}],R={message:"",token:""};function I(e){var t;let{accountId:a}=e,[n,s]=(0,C.useFormState)(z,R);return(0,r.jsx)("form",{className:"animate-in flex-1 flex flex-col w-full justify-center gap-y-6 text-foreground",children:(null==n?void 0:n.token)?(0,r.jsx)("div",{className:"text-sm",children:(t=n.token,"".concat("http://localhost:3000","/invitation?token=").concat(t))}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("input",{type:"hidden",name:"accountId",value:a}),(0,r.jsxs)("div",{className:"flex flex-col gap-y-2",children:[(0,r.jsx)(m.Label,{htmlFor:"invitationType",children:"Invitation Type"}),(0,r.jsxs)(h.l6,{defaultValue:"one_time",name:"invitationType",children:[(0,r.jsx)(h.bq,{children:(0,r.jsx)(h.yv,{placeholder:"Invitation type"})}),(0,r.jsx)(h.gC,{children:S.map(e=>(0,r.jsx)(h.eb,{value:e.value,children:e.label},e.value))})]})]}),(0,r.jsxs)("div",{className:"flex flex-col gap-y-2",children:[(0,r.jsx)(m.Label,{htmlFor:"accountRole",children:"Team Role"}),(0,r.jsxs)(h.l6,{defaultValue:"member",name:"accountRole",children:[(0,r.jsx)(h.bq,{children:(0,r.jsx)(h.yv,{placeholder:"Member type"})}),(0,r.jsx)(h.gC,{children:T.map(e=>(0,r.jsx)(h.eb,{value:e.value,children:e.label},e.value))})]})]}),(0,r.jsx)(x.SubmitButton,{formAction:async(e,t)=>s(t),errorMessage:null==n?void 0:n.message,pendingText:"Creating...",children:"Create invitation"})]})})}function L(e){let{accountId:t}=e;return(0,r.jsxs)(u.lG,{children:[(0,r.jsx)(u.zM,{asChild:!0,children:(0,r.jsx)(c.$,{variant:"outline",className:"rounded-lg h-9 border-subtle dark:border-white/10 hover:bg-hover-bg dark:hover:bg-hover-bg-dark",children:"Invite Member"})}),(0,r.jsxs)(u.Cf,{className:"sm:max-w-[425px] border-subtle dark:border-white/10 bg-card-bg dark:bg-background-secondary rounded-2xl shadow-custom",children:[(0,r.jsxs)(u.c7,{children:[(0,r.jsx)(u.L3,{className:"text-card-title",children:"Invite Team Member"}),(0,r.jsx)(u.rr,{className:"text-foreground/70",children:"Send an email invitation to join your team"})]}),(0,r.jsx)(I,{accountId:t})]})]})}var M=a(35095),A=a(74126);let B=(0,v.createServerReference)("60479f0ba93cf8cd5b7a4f15ad34fc5cac7652b700",v.callServer,void 0,v.findSourceMapURL,"deleteInvitation");function U(e){let{invitationId:t}=e,[a,s]=(0,n.useState)(!1),d=(0,f.usePathname)();return(0,r.jsxs)(u.lG,{open:a,onOpenChange:s,children:[(0,r.jsx)(u.zM,{asChild:!0,children:(0,r.jsxs)(c.$,{variant:"ghost",className:"h-8 w-8 p-0 rounded-full hover:bg-hover-bg dark:hover:bg-hover-bg-dark",children:[(0,r.jsx)(A.A,{className:"text-red-500 dark:text-red-400 w-4 h-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Delete invitation"})]})}),(0,r.jsxs)(u.Cf,{className:"sm:max-w-[425px] border-subtle dark:border-white/10 bg-card-bg dark:bg-background-secondary rounded-2xl shadow-custom",children:[(0,r.jsxs)(u.c7,{children:[(0,r.jsx)(u.L3,{className:"text-card-title",children:"Delete Invitation"}),(0,r.jsx)(u.rr,{className:"text-foreground/70",children:"Are you sure you want to delete this invitation? This cannot be undone."})]}),(0,r.jsxs)("div",{className:"flex gap-2 justify-end mt-4",children:[(0,r.jsx)(c.$,{variant:"outline",onClick:()=>s(!1),className:"rounded-lg h-9 border-subtle dark:border-white/10 hover:bg-hover-bg dark:hover:bg-hover-bg-dark",children:"Cancel"}),(0,r.jsxs)("form",{children:[(0,r.jsx)("input",{type:"hidden",name:"invitationId",value:t}),(0,r.jsx)("input",{type:"hidden",name:"returnPath",value:d}),(0,r.jsx)(x.SubmitButton,{variant:"destructive",formAction:B,pendingText:"Deleting...",className:"rounded-lg h-9 bg-red-500 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-700",children:"Delete"})]})]})]})]})}async function F(e){let{accountId:t}=e,a=await (0,s.U)(),{data:n}=await a.rpc("get_account_invitations",{account_id:t});return(0,r.jsxs)(_.Zp,{children:[(0,r.jsx)(_.aR,{children:(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(_.ZB,{children:"Pending Invitations"}),(0,r.jsx)(_.BT,{children:"These are the pending invitations for your team"})]}),(0,r.jsx)(L,{accountId:t})]})}),!!(null==n?void 0:n.length)&&(0,r.jsx)(_.Wu,{children:(0,r.jsx)(d.Table,{children:(0,r.jsx)(d.TableBody,{children:null==n?void 0:n.map(e=>(0,r.jsxs)(d.TableRow,{children:[(0,r.jsx)(d.TableCell,{children:(0,r.jsxs)("div",{className:"flex gap-x-2",children:[(0,M.m)(e.created_at,{addSuffix:!0}),(0,r.jsx)(i.E,{variant:"24_hour"===e.invitation_type?"default":"outline",children:e.invitation_type}),(0,r.jsx)(i.E,{variant:"owner"===e.account_role?"default":"outline",children:e.account_role})]})}),(0,r.jsx)(d.TableCell,{className:"text-right",children:(0,r.jsx)(U,{invitationId:e.invitation_id})})]},e.invitation_id))})})})]})}var O=a(55365);function P(e){let{params:t}=e,{accountSlug:a}=n.use(t),[d,i]=n.useState(null),[o,l]=n.useState(!0),[c,u]=n.useState(null);return(n.useEffect(()=>{!async function(){try{let e=await (0,s.U)(),{data:t}=await e.rpc("get_account_by_slug",{slug:a});i(t),l(!1)}catch(e){u("Failed to load team data"),l(!1),console.error(e)}}()},[a]),o)?(0,r.jsx)("div",{children:"Loading..."}):c?(0,r.jsxs)(O.Fc,{variant:"destructive",className:"border-red-300 dark:border-red-800 rounded-xl",children:[(0,r.jsx)(O.XL,{children:"Error"}),(0,r.jsx)(O.TN,{children:c})]}):d&&"owner"===d.account_role?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-card-title",children:"Team Members"}),(0,r.jsx)("p",{className:"text-sm text-foreground/70",children:"Manage your team members and invitations."})]}),(0,r.jsxs)(_.Zp,{className:"border-subtle dark:border-white/10 bg-white dark:bg-background-secondary shadow-none",children:[(0,r.jsxs)(_.aR,{children:[(0,r.jsx)(_.ZB,{className:"text-base text-card-title",children:"Invitations"}),(0,r.jsx)(_.BT,{children:"Invite new members to your team."})]}),(0,r.jsx)(_.Wu,{children:(0,r.jsx)(F,{accountId:d.account_id})})]}),(0,r.jsxs)(_.Zp,{className:"border-subtle dark:border-white/10 bg-white dark:bg-background-secondary shadow-none",children:[(0,r.jsxs)(_.aR,{children:[(0,r.jsx)(_.ZB,{className:"text-base text-card-title",children:"Members"}),(0,r.jsx)(_.BT,{children:"Manage existing team members."})]}),(0,r.jsx)(_.Wu,{children:(0,r.jsx)(k,{accountId:d.account_id})})]})]}):(0,r.jsxs)(O.Fc,{variant:"destructive",className:"border-red-300 dark:border-red-800 rounded-xl",children:[(0,r.jsx)(O.XL,{children:"Access Denied"}),(0,r.jsx)(O.TN,{children:"You do not have permission to access this page."})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2969,8341,7201,5061,6165,9001,2103,9855,7463,8441,1684,7358],()=>t(28134)),_N_E=e.O()}]);