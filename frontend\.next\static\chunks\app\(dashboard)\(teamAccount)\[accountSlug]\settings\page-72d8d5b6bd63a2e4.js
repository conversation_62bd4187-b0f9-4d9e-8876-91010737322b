(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9533],{1243:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a=(0,r(19946).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>s,t:()=>i});var a=r(12115);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,a=e.map(e=>{let a=n(e,t);return r||"function"!=typeof a||(r=!0),a});if(r)return()=>{for(let t=0;t<a.length;t++){let r=a[t];"function"==typeof r?r():n(e[t],null)}}}}function s(...e){return a.useCallback(i(...e),e)}},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var a=r(12115);let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,a.forwardRef)((e,t)=>{let{color:r="currentColor",size:n=24,strokeWidth:l=2,absoluteStrokeWidth:d,className:o="",children:c,iconNode:u,...f}=e;return(0,a.createElement)("svg",{ref:t,...s,width:n,height:n,stroke:r,strokeWidth:d?24*Number(l)/Number(n):l,className:i("lucide",o),...f},[...u.map(e=>{let[t,r]=e;return(0,a.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),d=(e,t)=>{let r=(0,a.forwardRef)((r,s)=>{let{className:d,...o}=r;return(0,a.createElement)(l,{ref:s,iconNode:t,className:i("lucide-".concat(n(e)),d),...o})});return r.displayName="".concat(e),r}},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>d,r:()=>l});var a=r(95155);r(12115);var n=r(99708),i=r(74466),s=r(59434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",node_outline:"bg-transparent border border-primary/10",node_secondary:"px-0 bg-transparent hover:opacity-60",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-lg gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-lg px-6 has-[>svg]:px-4",icon:"size-9",node_secondary:"px-0"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:r,size:i,asChild:d=!1,...o}=e,c=d?n.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,s.cn)(l({variant:r,size:i,className:t})),...o})}},34477:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{callServer:function(){return a.callServer},createServerReference:function(){return i},findSourceMapURL:function(){return n.findSourceMapURL}});let a=r(53806),n=r(31818),i=r(34979).createServerReference},35706:(e,t,r)=>{"use strict";r.d(t,{SubmitButton:()=>c});var a=r(95155),n=r(47650),i=r(12115),s=r(30285),l=r(55365),d=r(1243);let o={message:""};function c(e){let{children:t,formAction:r,errorMessage:c,pendingText:u="Submitting...",...f}=e,{pending:m,action:p}=(0,n.useFormStatus)(),[g,v]=(0,i.useActionState)(r,o),x=m&&p===v;return(0,a.jsxs)("div",{className:"flex flex-col gap-y-4 w-full",children:[!!(c||(null==g?void 0:g.message))&&(0,a.jsxs)(l.Fc,{variant:"destructive",className:"w-full",children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),(0,a.jsx)(l.TN,{children:c||(null==g?void 0:g.message)})]}),(0,a.jsx)("div",{children:(0,a.jsx)(s.$,{...f,type:"submit","aria-disabled":m,formAction:v,children:x?u:t})})]})}},40968:(e,t,r)=>{"use strict";r.d(t,{b:()=>l});var a=r(12115),n=r(63655),i=r(95155),s=a.forwardRef((e,t)=>(0,i.jsx)(n.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));s.displayName="Label";var l=s},50643:(e,t,r)=>{Promise.resolve().then(r.bind(r,84274))},55365:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>l,TN:()=>o,XL:()=>d});var a=r(95155);r(12115);var n=r(74466),i=r(59434);let s=(0,n.F)("relative w-full rounded-xl border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,...n}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(s({variant:r}),t),...n})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-title",className:(0,i.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r})}},59434:(e,t,r)=>{"use strict";r.d(t,{$3:()=>d,Hz:()=>l,W5:()=>o,cn:()=>s});var a=r(52596),n=r(81949),i=r(39688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.QP)((0,a.$)(t))}let l=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"rgba(180, 180, 180)";if(!e)return t;try{if("string"==typeof e&&e.startsWith("var(")){let t=document.createElement("div");t.style.color=e,document.body.appendChild(t);let r=window.getComputedStyle(t).color;return document.body.removeChild(t),n.formatRGBA(n.parse(r))}return n.formatRGBA(n.parse(e))}catch(e){return console.error("Color parsing failed:",e),t}},d=(e,t)=>e.startsWith("rgb")?n.formatRGBA(n.alpha(n.parse(e),t)):e;function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return e.length<=t?e:e.slice(0,t)+"..."}},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var a=r(95155);r(12115);var n=r(59434);function i(e){let{className:t,type:r,...i}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},63655:(e,t,r)=>{"use strict";r.d(t,{hO:()=>d,sG:()=>l});var a=r(12115),n=r(47650),i=r(99708),s=r(95155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,i.TL)(`Primitive.${t}`),n=a.forwardRef((e,a)=>{let{asChild:n,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(n?r:t,{...i,ref:a})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{});function d(e,t){e&&n.flushSync(()=>e.dispatchEvent(t))}},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>o,ZB:()=>l,Zp:()=>i,aR:()=>s,wL:()=>c});var a=r(95155);r(12115);var n=r(59434);function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function s(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r})}},72305:(e,t,r)=>{"use strict";r.d(t,{U:()=>n});var a=r(34477);let n=(0,a.createServerReference)("7f8bed79c8654f95685745e61906af37f96a086236",a.callServer,void 0,a.findSourceMapURL,"createClient")},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>s});var a=r(52596);let n=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=a.$,s=(e,t)=>r=>{var a;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:s,defaultVariants:l}=t,d=Object.keys(s).map(e=>{let t=null==r?void 0:r[e],a=null==l?void 0:l[e];if(null===t)return null;let i=n(t)||n(a);return s[e][i]}),o=r&&Object.entries(r).reduce((e,t)=>{let[r,a]=t;return void 0===a||(e[r]=a),e},{});return i(e,d,null==t||null==(a=t.compoundVariants)?void 0:a.reduce((e,t)=>{let{class:r,className:a,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...o}[t]):({...l,...o})[t]===r})?[...e,r,a]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},84274:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var a=r(95155),n=r(12115),i=r(62523),s=r(35706),l=r(34477);let d=(0,l.createServerReference)("602190608cb4aada86562e5e5997e6bb44fbe95e4e",l.callServer,void 0,l.findSourceMapURL,"editTeamName");var o=r(85057);function c(e){let{account:t}=e;return(0,a.jsxs)("form",{className:"animate-in",children:[(0,a.jsx)("input",{type:"hidden",name:"accountId",value:t.account_id}),(0,a.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-y-2",children:[(0,a.jsx)(o.Label,{htmlFor:"name",className:"text-sm font-medium text-foreground/90",children:"Team Name"}),(0,a.jsx)(i.p,{defaultValue:t.name,name:"name",id:"name",placeholder:"My Team",required:!0,className:"h-10 rounded-lg border-subtle dark:border-white/10 bg-white dark:bg-background-secondary"})]}),(0,a.jsx)("div",{className:"flex justify-end mt-2",children:(0,a.jsx)(s.SubmitButton,{formAction:d,pendingText:"Updating...",className:"rounded-lg bg-primary hover:bg-primary/90 text-white h-10",children:"Save Changes"})})]})]})}let u=(0,l.createServerReference)("60120624b62a67d0046abca062aae687cbb5ebc44a",l.callServer,void 0,l.findSourceMapURL,"editTeamSlug");function f(e){let{account:t}=e;return(0,a.jsxs)("form",{className:"animate-in",children:[(0,a.jsx)("input",{type:"hidden",name:"accountId",value:t.account_id}),(0,a.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,a.jsxs)("div",{className:"flex flex-col gap-y-2",children:[(0,a.jsx)(o.Label,{htmlFor:"slug",className:"text-sm font-medium text-foreground/90",children:"Team URL Identifier"}),(0,a.jsxs)("div",{className:"flex items-center gap-x-2",children:[(0,a.jsx)("span",{className:"text-sm text-foreground/70 whitespace-nowrap",children:"https://your-app.com/"}),(0,a.jsx)(i.p,{defaultValue:t.slug,name:"slug",id:"slug",placeholder:"my-team",required:!0,className:"h-10 rounded-lg border-subtle dark:border-white/10 bg-white dark:bg-background-secondary"})]})]}),(0,a.jsx)("div",{className:"flex justify-end mt-2",children:(0,a.jsx)(s.SubmitButton,{formAction:u,pendingText:"Updating...",className:"rounded-lg bg-primary hover:bg-primary/90 text-white h-10",children:"Save Changes"})})]})]})}var m=r(66695),p=r(72305);function g(e){let{params:t}=e,{accountSlug:r}=n.use(t),[i,s]=n.useState(null),[l,d]=n.useState(!0),[o,u]=n.useState(null);return(n.useEffect(()=>{!async function(){try{let e=await (0,p.U)(),{data:t}=await e.rpc("get_account_by_slug",{slug:r});s(t),d(!1)}catch(e){u("Failed to load account data"),d(!1),console.error(e)}}()},[r]),l)?(0,a.jsx)("div",{children:"Loading..."}):i?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-card-title",children:"Team Settings"}),(0,a.jsx)("p",{className:"text-sm text-foreground/70",children:"Manage your team account details."})]}),(0,a.jsxs)(m.Zp,{className:"border-subtle dark:border-white/10 bg-white dark:bg-background-secondary shadow-none",children:[(0,a.jsxs)(m.aR,{children:[(0,a.jsx)(m.ZB,{className:"text-base text-card-title",children:"Team Name"}),(0,a.jsx)(m.BT,{children:"Update your team name."})]}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(c,{account:i})})]}),(0,a.jsxs)(m.Zp,{className:"border-subtle dark:border-white/10 bg-white dark:bg-background-secondary shadow-none",children:[(0,a.jsxs)(m.aR,{children:[(0,a.jsx)(m.ZB,{className:"text-base text-card-title",children:"Team URL"}),(0,a.jsx)(m.BT,{children:"Update your team URL slug."})]}),(0,a.jsx)(m.Wu,{children:(0,a.jsx)(f,{account:i})})]})]}):(0,a.jsx)("div",{children:"Account not found"})}},85057:(e,t,r)=>{"use strict";r.d(t,{Label:()=>s});var a=r(95155);r(12115);var n=r(40968),i=r(59434);function s(e){let{className:t,...r}=e;return(0,a.jsx)(n.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...r})}},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>l,Dc:()=>o,TL:()=>s});var a=r(12115),n=r(6101),i=r(95155);function s(e){let t=function(e){let t=a.forwardRef((e,t)=>{let{children:r,...i}=e;if(a.isValidElement(r)){var s;let e,l,d=(s=r,(l=(e=Object.getOwnPropertyDescriptor(s.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.ref:(l=(e=Object.getOwnPropertyDescriptor(s,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?s.props.ref:s.props.ref||s.ref),o=function(e,t){let r={...t};for(let a in t){let n=e[a],i=t[a];/^on[A-Z]/.test(a)?n&&i?r[a]=(...e)=>{let t=i(...e);return n(...e),t}:n&&(r[a]=n):"style"===a?r[a]={...n,...i}:"className"===a&&(r[a]=[n,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props);return r.type!==a.Fragment&&(o.ref=t?(0,n.t)(t,d):d),a.cloneElement(r,o)}return a.Children.count(r)>1?a.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=a.forwardRef((e,r)=>{let{children:n,...s}=e,l=a.Children.toArray(n),d=l.find(c);if(d){let e=d.props.children,n=l.map(t=>t!==d?t:a.Children.count(e)>1?a.Children.only(null):a.isValidElement(e)?e.props.children:null);return(0,i.jsx)(t,{...s,ref:r,children:a.isValidElement(e)?a.cloneElement(e,void 0,n):null})}return(0,i.jsx)(t,{...s,ref:r,children:n})});return r.displayName=`${e}.Slot`,r}var l=s("Slot"),d=Symbol("radix.slottable");function o(e){let t=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=d,t}function c(e){return a.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}}},e=>{var t=t=>e(e.s=t);e.O(0,[2969,8441,1684,7358],()=>t(50643)),_N_E=e.O()}]);