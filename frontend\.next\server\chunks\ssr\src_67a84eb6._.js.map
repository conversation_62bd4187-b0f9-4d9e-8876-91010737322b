{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>/navbar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Navbar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/home/<USER>/navbar.tsx <module evaluation>\",\n    \"Navbar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,yEACA", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>/navbar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Navbar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/home/<USER>/navbar.tsx\",\n    \"Navbar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,qDACA", "debugId": null}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/app/%28home%29/layout.tsx"], "sourcesContent": ["import { Navbar } from '@/components/home/<USER>/navbar';\r\n\r\nexport default function HomeLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  return (\r\n    <div className=\"w-full relative\">\r\n      <div className=\"block w-px h-full border-l border-border fixed top-0 left-6 z-10\"></div>\r\n      <div className=\"block w-px h-full border-r border-border fixed top-0 right-6 z-10\"></div>\r\n      <Navbar />\r\n      {children}\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC,gJAAA,CAAA,SAAM;;;;;YACN;;;;;;;AAGP", "debugId": null}}]}