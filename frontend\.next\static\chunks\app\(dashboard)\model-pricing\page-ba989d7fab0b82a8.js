(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8443],{2564:(e,t,r)=>{"use strict";r.d(t,{Qg:()=>n,bL:()=>l});var i=r(12115),s=r(63655),o=r(95155),n=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),a=i.forwardRef((e,t)=>(0,o.jsx)(s.sG.span,{...e,ref:t,style:{...n,...e.style}}));a.displayName="VisuallyHidden";var l=a},10401:(e,t,r)=>{"use strict";r.d(t,{Zo:()=>m,un:()=>p,L6:()=>g,ve:()=>u,WY:()=>d,Il:()=>x,uv:()=>v,xE:()=>y});var i=r(48988),s=r(12115),o=r(23843),n=r(99090),a=r(25731),l=r(16183);let c=(0,n.GQ)(l._1.available,a.cL,{staleTime:3e5,refetchOnWindowFocus:!1,retry:2,select:e=>({...e,models:[...e.models].sort((e,t)=>e.display_name.localeCompare(t.display_name))})}),d="suna-preferred-model-v2",u="customModels",p="claude-sonnet-4",m="claude-sonnet-4",g={"claude-sonnet-4":{tier:"free",priority:100,recommended:!0,lowQuality:!1},"gemini-flash-2.5":{tier:"free",priority:70,recommended:!1,lowQuality:!1},qwen3:{tier:"free",priority:60,recommended:!1,lowQuality:!1},"sonnet-3.7":{tier:"premium",priority:99,recommended:!1,lowQuality:!1},"grok-4":{tier:"premium",priority:98,recommended:!1,lowQuality:!1},"google/gemini-2.5-pro":{tier:"premium",priority:97,recommended:!1,lowQuality:!1},"gpt-4.1":{tier:"premium",priority:96,recommended:!1,lowQuality:!1},"sonnet-3.5":{tier:"premium",priority:90,recommended:!1,lowQuality:!1},"gpt-4o":{tier:"premium",priority:88,recommended:!1,lowQuality:!1},"gemini-2.5-flash:thinking":{tier:"premium",priority:84,recommended:!1,lowQuality:!1},"deepseek/deepseek-chat-v3-0324":{tier:"premium",priority:75,recommended:!1,lowQuality:!1}},h=(e,t)=>!!(0,o.Jn)()||"active"===e||!t,x=e=>e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),v=()=>{if(!(0,o.Jn)())return[];try{let e=localStorage.getItem(u);if(!e)return[];let t=JSON.parse(e);if(!Array.isArray(t))return[];return t.filter(e=>e&&"object"==typeof e&&"string"==typeof e.id&&"string"==typeof e.label)}catch(e){return console.error("Error parsing custom models:",e),[]}},f=e=>{try{localStorage.setItem(d,e)}catch(e){console.warn("Failed to save model preference to localStorage:",e)}},y=()=>{let[e,t]=(0,s.useState)(m),[r,n]=(0,s.useState)([]),[a,l]=(0,s.useState)(!1),{data:u}=(0,i.Rs)(),{data:y,isLoading:b}=c({refetchOnMount:!1}),_=(null==u?void 0:u.status)==="active"?"active":"no_subscription",I=()=>{(0,o.Jn)()&&n(v())};(0,s.useEffect)(()=>{I()},[]);let j=(0,s.useMemo)(()=>{let e=[];if((null==y?void 0:y.models)&&!b)e=y.models.map(e=>{let t=e.short_name||e.id,r=e.display_name||t;r.includes("/")&&(r=r.split("/").pop()||r),r=r.replace(/-/g," ").split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ");let i=g[t]||{};return{id:t,label:r,requiresSubscription:(null==e?void 0:e.requires_subscription)||"premium"===i.tier||!1,top:i.priority>=90,priority:i.priority||0,lowQuality:i.lowQuality||!1,recommended:i.recommended||!1}});else{var t,i;e=[{id:m,label:"DeepSeek",requiresSubscription:!1,priority:(null==(t=g[m])?void 0:t.priority)||50},{id:p,label:"Sonnet 4",requiresSubscription:!0,priority:(null==(i=g[p])?void 0:i.priority)||100}]}return(0,o.Jn)()&&r.length>0&&(e=[...e,...r.map(e=>({id:e.id,label:e.label||x(e.id),requiresSubscription:!1,top:!1,isCustom:!0,priority:30,lowQuality:!1,recommended:!1}))]),e.sort((e,t)=>e.recommended!==t.recommended?e.recommended?-1:1:e.priority!==t.priority?t.priority-e.priority:e.label.localeCompare(t.label))},[y,b,r]),w=(0,s.useMemo)(()=>(0,o.Jn)()?j:j.filter(e=>h(_,e.requiresSubscription)),[j,_]);(0,s.useEffect)(()=>{if(!a){console.log("Initializing model selection from localStorage...");try{let i=localStorage.getItem(d);if(console.log("Saved model from localStorage:",i),i){if(b)return void console.log("Models still loading, waiting...");let s=j.find(e=>e.id===i),n=(0,o.Jn)()&&r.some(e=>e.id===i);if(s||n){var e;if((0,o.Jn)()||h(_,null!=(e=null==s?void 0:s.requiresSubscription)&&e)){console.log("Using saved model:",i),t(i),l(!0);return}console.log("Saved model not accessible, falling back to default")}else console.log("Saved model not found in available models, falling back to default")}let s="active"===_?p:m;console.log("Using default model:",s),t(s),f(s),l(!0)}catch(r){console.warn("Failed to load preferences from localStorage:",r);let e="active"===_?p:m;t(e),f(e),l(!0)}}},[_,j,b,r,a]);let G=e=>{var i;console.log("handleModelChange called with:",e),(0,o.Jn)()&&I();let s=(0,o.Jn)()&&r.some(t=>t.id===e),n=j.find(t=>t.id===e);if(!n&&!s){console.warn("Model not found in options:",e,j,s,r);let i=(0,o.Jn)()?p:m;t(i),f(i);return}if(!s&&!(0,o.Jn)()&&!h(_,null!=(i=null==n?void 0:n.requiresSubscription)&&i))return void console.warn("Model not accessible:",e);console.log("Setting selected model and saving to localStorage:",e),t(e),f(e)};return{selectedModel:e,setSelectedModel:e=>{G(e)},subscriptionStatus:_,availableModels:w,allModels:j,customModels:r,getActualModelId:e=>e,refreshCustomModels:I,canAccessModel:e=>{if((0,o.Jn)())return!0;let t=j.find(t=>t.id===e);return!!t&&h(_,t.requiresSubscription)},isSubscriptionRequired:e=>{var t;return(null==(t=j.find(t=>t.id===e))?void 0:t.requiresSubscription)||!1}}}},16183:(e,t,r)=>{"use strict";r.d(t,{Er:()=>l,_1:()=>a,s9:()=>n});var i=r(99090);let s=["subscription"],o=["usage"],n=(0,i.DY)({all:s,details:()=>[...s,"details"]}),a=(0,i.DY)({all:["models"],available:["models","available"]}),l=(0,i.DY)({all:o,logs:(e,t)=>[...o,"logs",{page:e,itemsPerPage:t}]})},23843:(e,t,r)=>{"use strict";r.d(t,{$W:()=>s,Jn:()=>o});let i=(()=>{let e="local";if(e){if("local"===e)return console.log("Using explicitly set LOCAL environment mode"),"local";else if("staging"===e)return console.log("Using explicitly set STAGING environment mode"),"staging";else if("production"===e)return console.log("Using explicitly set PRODUCTION environment mode"),"production"}return console.log("Defaulting to PRODUCTION environment mode"),"production"})(),s={ENV_MODE:i,IS_LOCAL:"local"===i,SUBSCRIPTION_TIERS:"staging"===i?{FREE:{priceId:"price_1RIGvuG6l1KZGqIrw14abxeL",name:"Free"},TIER_2_20:{priceId:"price_1RIGvuG6l1KZGqIrCRu0E4Gi",name:"2h/$20"},TIER_6_50:{priceId:"price_1RIGvuG6l1KZGqIrvjlz5p5V",name:"6h/$50"},TIER_12_100:{priceId:"price_1RIGvuG6l1KZGqIrT6UfgblC",name:"12h/$100"},TIER_25_200:{priceId:"price_1RIGvuG6l1KZGqIrOVLKlOMj",name:"25h/$200"},TIER_50_400:{priceId:"price_1RIKNgG6l1KZGqIrvsat5PW7",name:"50h/$400"},TIER_125_800:{priceId:"price_1RIKNrG6l1KZGqIrjKT0yGvI",name:"125h/$800"},TIER_200_1000:{priceId:"price_1RIKQ2G6l1KZGqIrum9n8SI7",name:"200h/$1000"},TIER_2_20_YEARLY:{priceId:"price_1ReGogG6l1KZGqIrEyBTmtPk",name:"2h/$204/year"},TIER_6_50_YEARLY:{priceId:"price_1ReGoJG6l1KZGqIr0DJWtoOc",name:"6h/$510/year"},TIER_12_100_YEARLY:{priceId:"price_1ReGnZG6l1KZGqIr0ThLEl5S",name:"12h/$1020/year"},TIER_25_200_YEARLY:{priceId:"price_1ReGmzG6l1KZGqIre31mqoEJ",name:"25h/$2040/year"},TIER_50_400_YEARLY:{priceId:"price_1ReGmgG6l1KZGqIrn5nBc7e5",name:"50h/$4080/year"},TIER_125_800_YEARLY:{priceId:"price_1ReGmMG6l1KZGqIrvE2ycrAX",name:"125h/$8160/year"},TIER_200_1000_YEARLY:{priceId:"price_1ReGlXG6l1KZGqIrlgurP5GU",name:"200h/$10200/year"}}:{FREE:{priceId:"price_1RILb4G6l1KZGqIrK4QLrx9i",name:"Free"},TIER_2_20:{priceId:"price_1RILb4G6l1KZGqIrhomjgDnO",name:"2h/$20"},TIER_6_50:{priceId:"price_1RILb4G6l1KZGqIr5q0sybWn",name:"6h/$50"},TIER_12_100:{priceId:"price_1RILb4G6l1KZGqIr5Y20ZLHm",name:"12h/$100"},TIER_25_200:{priceId:"price_1RILb4G6l1KZGqIrGAD8rNjb",name:"25h/$200"},TIER_50_400:{priceId:"price_1RILb4G6l1KZGqIruNBUMTF1",name:"50h/$400"},TIER_125_800:{priceId:"price_1RILb3G6l1KZGqIrbJA766tN",name:"125h/$800"},TIER_200_1000:{priceId:"price_1RILb3G6l1KZGqIrmauYPOiN",name:"200h/$1000"},TIER_2_20_YEARLY:{priceId:"price_1ReHB5G6l1KZGqIrD70I1xqM",name:"2h/$204/year"},TIER_6_50_YEARLY:{priceId:"price_1ReHAsG6l1KZGqIrlAog487C",name:"6h/$510/year"},TIER_12_100_YEARLY:{priceId:"price_1ReHAWG6l1KZGqIrBHer2PQc",name:"12h/$1020/year"},TIER_25_200_YEARLY:{priceId:"price_1ReH9uG6l1KZGqIrsvMLHViC",name:"25h/$2040/year"},TIER_50_400_YEARLY:{priceId:"price_1ReH9fG6l1KZGqIrsPtu5KIA",name:"50h/$4080/year"},TIER_125_800_YEARLY:{priceId:"price_1ReH9GG6l1KZGqIrfgqaJyat",name:"125h/$8160/year"},TIER_200_1000_YEARLY:{priceId:"price_1ReH8qG6l1KZGqIrK1akY90q",name:"200h/$10200/year"}}},o=()=>s.IS_LOCAL},25487:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(19946).A)("Server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]])},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,r:()=>a});var i=r(95155);r(12115);var s=r(99708),o=r(74466),n=r(59434);let a=(0,o.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",node_outline:"bg-transparent border border-primary/10",node_secondary:"px-0 bg-transparent hover:opacity-60",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-lg gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-lg px-6 has-[>svg]:px-4",icon:"size-9",node_secondary:"px-0"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:o,asChild:l=!1,...c}=e,d=l?s.DX:"button";return(0,i.jsx)(d,{"data-slot":"button",className:(0,n.cn)(a({variant:r,size:o,className:t})),...c})}},34869:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(19946).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},45905:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>m});var i=r(95155),s=r(85339),o=r(71539),n=r(25487),a=(r(34869),r(30285)),l=r(66695),c=(r(59409),r(95557)),d=r(51154),u=r(12115),p=r(10401);function m(){let{data:e,isLoading:t,error:r,refetch:m}=(0,c.$x)(),{allModels:g}=(0,p.xE)(),[h,x]=(0,u.useState)("anthropic/claude-sonnet-4-20250514"),[v,f]=(0,u.useState)(!1),y=(0,u.useMemo)(()=>{var t;return((null==e||null==(t=e.models)?void 0:t.filter(e=>null!==e.input_cost_per_million_tokens&&void 0!==e.input_cost_per_million_tokens&&null!==e.output_cost_per_million_tokens&&void 0!==e.output_cost_per_million_tokens))||[]).map(e=>{var t,r,i;return{...e,display_name:null==(t=g.find(t=>t.id===e.short_name))?void 0:t.label,priority:null==(r=g.find(t=>t.id===e.short_name))?void 0:r.priority,requiresSubscription:null==(i=g.find(t=>t.id===e.short_name))?void 0:i.requiresSubscription}}).sort((e,t)=>{var r,i,s,o,n,a;return e.requiresSubscription!==t.requiresSubscription?e.requiresSubscription?-1:1:(null!=(r=e.priority)?r:0)!==(null!=(i=t.priority)?i:0)?(null!=(s=t.priority)?s:0)-(null!=(o=e.priority)?o:0):(null!=(n=e.display_name)?n:e.id).localeCompare(null!=(a=t.display_name)?a:t.id)})},[null==e?void 0:e.models,g]);return y.find(e=>e.id===h),t?(0,i.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,i.jsxs)("div",{className:"flex flex-col items-center gap-4",children:[(0,i.jsx)(d.A,{className:"w-8 h-8 animate-spin text-blue-500"}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"Loading pricing data..."})]})}):r?(0,i.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,i.jsxs)("div",{className:"max-w-md text-center space-y-4",children:[(0,i.jsx)(s.A,{className:"w-12 h-12 text-red-500 mx-auto"}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)("h3",{className:"text-lg font-semibold text-foreground",children:"Pricing Unavailable"}),(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:r instanceof Error?r.message:"Failed to fetch model pricing"})]}),(0,i.jsx)(a.$,{onClick:()=>m(),size:"sm",children:"Try Again"})]})}):(0,i.jsxs)("div",{className:"space-y-8 p-8 max-w-4xl mx-auto",children:[(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-foreground",children:"Token Pricing"}),(0,i.jsx)("p",{className:"text-lg text-muted-foreground max-w-3xl",children:"Understand how tokens work, explore pricing for AI models, and find the right plan for your needs."})]}),(0,i.jsxs)(l.Zp,{children:[(0,i.jsx)(l.aR,{children:(0,i.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(o.A,{className:"w-5 h-5 text-blue-500"}),"Understanding Tokens & Compute"]})}),(0,i.jsx)(l.Wu,{children:(0,i.jsx)("p",{className:"text-muted-foreground",children:"Tokens are the fundamental units that AI models use to process text - the more complex or lengthy your task, the more tokens it requires. Compute usage is measured by both input tokens (your prompts and context) and output tokens (the AI's responses), with different models having varying computational requirements and costs per token."})})]}),(0,i.jsxs)(l.Zp,{children:[(0,i.jsx)(l.aR,{children:(0,i.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,i.jsx)(n.A,{className:"w-5 h-5 text-green-500"}),"How does pricing work?"]})}),(0,i.jsx)(l.Wu,{children:(0,i.jsx)("p",{className:"text-muted-foreground",children:"Usage costs are calculated based on token consumption from AI model interactions. We apply a 50% markup over direct model provider costs to maintain our platform and services. Your total cost depends on the specific model used and the number of tokens processed for both input (prompts, context) and output (generated responses)."})})]}),!1,(0,i.jsxs)(l.Zp,{children:[(0,i.jsxs)(l.aR,{children:[(0,i.jsx)(l.ZB,{children:"Compute Pricing by Model"}),(0,i.jsx)(l.BT,{children:"Detailed pricing information for available AI models. We apply a 50% markup on direct LLM provider costs to maintain our service and generate profit."})]}),(0,i.jsx)(l.Wu,{children:(0,i.jsxs)("div",{className:"bg-card border border-border rounded-lg",children:[(0,i.jsx)("div",{className:"px-6 py-4 border-b border-border",children:(0,i.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm font-medium text-muted-foreground",children:[(0,i.jsx)("div",{className:"col-span-1",children:"Model"}),(0,i.jsx)("div",{className:"col-span-1 text-center",children:"Input Cost"}),(0,i.jsx)("div",{className:"col-span-1 text-center",children:"Output Cost"})]})}),(0,i.jsx)("div",{className:"divide-y divide-border",children:y.map((e,t)=>{var r;return(0,i.jsx)("div",{className:"px-6 py-4 hover:bg-muted/50 transition-colors duration-150 ".concat(h===e.id?"bg-blue-50 dark:bg-blue-950/20 border-l-4 border-l-blue-500":""),children:(0,i.jsxs)("div",{className:"grid grid-cols-3 gap-4 items-center",children:[(0,i.jsx)("div",{className:"col-span-1",children:(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)("div",{className:"w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"}),(0,i.jsx)("div",{className:"min-w-0",children:(0,i.jsx)("div",{className:"font-medium text-foreground truncate",children:null!=(r=e.display_name)?r:e.id})})]})}),(0,i.jsx)("div",{className:"col-span-1 text-center",children:(0,i.jsx)("div",{className:"space-y-1",children:null!==e.input_cost_per_million_tokens&&void 0!==e.input_cost_per_million_tokens?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"font-semibold text-foreground",children:["$",e.input_cost_per_million_tokens.toFixed(2)]}),(0,i.jsx)("div",{className:"text-xs text-muted-foreground",children:"per 1M tokens"})]}):(0,i.jsx)("div",{className:"font-semibold text-muted-foreground",children:"—"})})}),(0,i.jsx)("div",{className:"col-span-1 text-center",children:(0,i.jsx)("div",{className:"space-y-1",children:null!==e.output_cost_per_million_tokens&&void 0!==e.output_cost_per_million_tokens?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"font-semibold text-foreground",children:["$",e.output_cost_per_million_tokens.toFixed(2)]}),(0,i.jsx)("div",{className:"text-xs text-muted-foreground",children:"per 1M tokens"})]}):(0,i.jsx)("div",{className:"font-semibold text-muted-foreground",children:"—"})})})]})},e.id)})})]})})]})]})}},48988:(e,t,r)=>{"use strict";r.d(t,{Rs:()=>n});var i=r(99090),s=r(25731),o=r(16183);let n=(0,i.GQ)(o.s9.details(),s.uV,{staleTime:3e5,refetchOnWindowFocus:!0});(0,i.Lx)(e=>(0,s.VK)(e),{onSuccess:e=>{(null==e?void 0:e.url)&&(window.location.href=e.url)}})},51154:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(19946).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},54099:(e,t,r)=>{Promise.resolve().then(r.bind(r,45905))},59409:(e,t,r)=>{"use strict";r.d(t,{bq:()=>u,eb:()=>m,gC:()=>p,l6:()=>c,yv:()=>d});var i=r(95155);r(12115);var s=r(31992),o=r(66474),n=r(5196),a=r(47863),l=r(59434);function c(e){let{...t}=e;return(0,i.jsx)(s.bL,{"data-slot":"select",...t})}function d(e){let{...t}=e;return(0,i.jsx)(s.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:r="default",children:n,...a}=e;return(0,i.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":r,className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...a,children:[n,(0,i.jsx)(s.In,{asChild:!0,children:(0,i.jsx)(o.A,{className:"size-4 opacity-50"})})]})}function p(e){let{className:t,children:r,position:o="popper",...n}=e;return(0,i.jsx)(s.ZL,{children:(0,i.jsxs)(s.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===o&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:o,...n,children:[(0,i.jsx)(g,{}),(0,i.jsx)(s.LM,{className:(0,l.cn)("p-1","popper"===o&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:r}),(0,i.jsx)(h,{})]})})}function m(e){let{className:t,children:r,...o}=e;return(0,i.jsxs)(s.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...o,children:[(0,i.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,i.jsx)(s.VF,{children:(0,i.jsx)(n.A,{className:"size-4"})})}),(0,i.jsx)(s.p4,{children:r})]})}function g(e){let{className:t,...r}=e;return(0,i.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,i.jsx)(a.A,{className:"size-4"})})}function h(e){let{className:t,...r}=e;return(0,i.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,i.jsx)(o.A,{className:"size-4"})})}},59434:(e,t,r)=>{"use strict";r.d(t,{$3:()=>l,Hz:()=>a,W5:()=>c,cn:()=>n});var i=r(52596),s=r(81949),o=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,i.$)(t))}let a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"rgba(180, 180, 180)";if(!e)return t;try{if("string"==typeof e&&e.startsWith("var(")){let t=document.createElement("div");t.style.color=e,document.body.appendChild(t);let r=window.getComputedStyle(t).color;return document.body.removeChild(t),s.formatRGBA(s.parse(r))}return s.formatRGBA(s.parse(e))}catch(e){return console.error("Color parsing failed:",e),t}},l=(e,t)=>e.startsWith("rgb")?s.formatRGBA(s.alpha(s.parse(e),t)):e;function c(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return e.length<=t?e:e.slice(0,t)+"..."}},66695:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>a,Zp:()=>o,aR:()=>n,wL:()=>d});var i=r(95155);r(12115);var s=r(59434);function o(e){let{className:t,...r}=e;return(0,i.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...r})}function n(e){let{className:t,...r}=e;return(0,i.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...r})}function a(e){let{className:t,...r}=e;return(0,i.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...r})}function l(e){let{className:t,...r}=e;return(0,i.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...r})}function c(e){let{className:t,...r}=e;return(0,i.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,i.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...r})}},71539:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(19946).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},85339:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(19946).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},90697:(e,t,r)=>{"use strict";r.d(t,{Hv:()=>l,mI:()=>a});var i=r(52643),s=r(33356);let o="http://localhost:8000/api",n={async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{showErrors:r=!0,errorContext:o,timeout:n=3e4,...a}=t;try{let t,l=new AbortController,c=setTimeout(()=>l.abort(),n),d=(0,i.U)(),{data:{session:u}}=await d.auth.getSession(),p={"Content-Type":"application/json",...a.headers};(null==u?void 0:u.access_token)&&(p.Authorization="Bearer ".concat(u.access_token));let m=await fetch(e,{...a,headers:p,signal:l.signal});if(clearTimeout(c),!m.ok){let e=Error("HTTP ".concat(m.status,": ").concat(m.statusText));e.status=m.status,e.response=m;try{let t=await m.json();e.details=t,t.message&&(e.message=t.message)}catch(e){}return r&&(0,s.hS)(e,o),{error:e,success:!1}}let g=m.headers.get("content-type");return{data:(null==g?void 0:g.includes("application/json"))?await m.json():(null==g?void 0:g.includes("text/"))?await m.text():await m.blob(),success:!0}}catch(t){let e=t instanceof Error?t:Error(String(t));return"AbortError"===t.name&&(e.message="Request timeout",e.code="TIMEOUT"),r&&(0,s.nQ)(e,o),{error:e,success:!1}}},get:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n.request(e,{...t,method:"GET"})},post:async function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return n.request(e,{...r,method:"POST",body:t?JSON.stringify(t):void 0})},put:async function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return n.request(e,{...r,method:"PUT",body:t?JSON.stringify(t):void 0})},patch:async function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return n.request(e,{...r,method:"PATCH",body:t?JSON.stringify(t):void 0})},delete:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return n.request(e,{...t,method:"DELETE"})},upload:async function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{headers:i,...s}=r,o={...i};return delete o["Content-Type"],n.request(e,{...s,method:"POST",body:t,headers:o})}},a={async execute(e,t){try{let{data:r,error:i}=await e();if(i){let e=Error(i.message||"Database error");return e.code=i.code,e.details=i,(0,s.hS)(e,t),{error:e,success:!1}}return{data:r,success:!0}}catch(r){let e=r instanceof Error?r:Error(String(r));return(0,s.hS)(e,t),{error:e,success:!1}}}},l={get:(e,t)=>n.get("".concat(o).concat(e),t),post:(e,t,r)=>n.post("".concat(o).concat(e),t,r),put:(e,t,r)=>n.put("".concat(o).concat(e),t,r),patch:(e,t,r)=>n.patch("".concat(o).concat(e),t,r),delete:(e,t)=>n.delete("".concat(o).concat(e),t),upload:(e,t,r)=>n.upload("".concat(o).concat(e),t,r)}},95557:(e,t,r)=>{"use strict";r.d(t,{$x:()=>l,_o:()=>c});var i=r(99090),s=r(25731);r(52643);var o=r(90697);r(33356);let n={getSubscription:async()=>(await o.Hv.get("/billing/subscription",{errorContext:{operation:"load subscription",resource:"billing information"}})).data||null,checkStatus:async()=>(await o.Hv.get("/billing/status",{errorContext:{operation:"check billing status",resource:"account status"}})).data||null,createCheckoutSession:async e=>(await o.Hv.post("/billing/create-checkout-session",e,{errorContext:{operation:"create checkout session",resource:"billing"}})).data||null,createPortalSession:async e=>(await o.Hv.post("/billing/create-portal-session",e,{errorContext:{operation:"create portal session",resource:"billing portal"}})).data||null,getAvailableModels:async()=>(await o.Hv.get("/billing/available-models",{errorContext:{operation:"load available models",resource:"AI models"}})).data||null,async getUsageLogs(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3;return(await o.Hv.get("/billing/usage-logs?page=".concat(e,"&items_per_page=").concat(t),{errorContext:{operation:"load usage logs",resource:"usage history"}})).data||null}};var a=r(16183);let l=(0,i.GQ)(a._1.available,s.cL,{staleTime:6e5,refetchOnWindowFocus:!1});(0,i.GQ)(["billing","status"],s.Et,{staleTime:12e4,refetchOnWindowFocus:!0}),(0,i.Lx)(e=>(0,s.fw)(e),{onSuccess:e=>{e.url&&(window.location.href=e.url)},errorContext:{operation:"create checkout session",resource:"billing"}});let c=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3;return(0,i.GQ)(a.Er.logs(e,t),()=>n.getUsageLogs(e,t),{staleTime:3e4,refetchOnMount:!0,refetchOnWindowFocus:!1})()}},99090:(e,t,r)=>{"use strict";r.d(t,{DY:()=>n,GQ:()=>a,Lx:()=>l});var i=r(28755),s=r(5041),o=r(33356);let n=e=>e;function a(e,t,r){return s=>(0,i.I)({queryKey:e,queryFn:t,...r,...s})}function l(e,t){return r=>{let{errorContext:i,...n}=t||{},{errorContext:a,...l}=r||{};return(0,s.n)({mutationFn:e,onError:(e,t,r)=>{var s,c;(null==l?void 0:l.onError)||(null==n?void 0:n.onError)||(0,o.hS)(e,a||i),null==n||null==(s=n.onError)||s.call(n,e,t,r),null==l||null==(c=l.onError)||c.call(l,e,t,r)},...n,...l})}}}},e=>{var t=t=>e(e.s=t);e.O(0,[2969,1935,6671,3860,1171,8341,7201,5061,6165,9855,6686,8441,1684,7358],()=>t(54099)),_N_E=e.O()}]);