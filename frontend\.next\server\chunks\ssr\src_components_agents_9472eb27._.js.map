{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/tools.ts"], "sourcesContent": ["export const DEFAULT_AGENTPRESS_TOOLS: Record<string, { enabled: boolean; description: string; icon: string; color: string }> = {\r\n    'sb_shell_tool': { enabled: true, description: 'Execute shell commands in tmux sessions for terminal operations, CLI tools, and system management', icon: '💻', color: 'bg-slate-100 dark:bg-slate-800' },\r\n    'sb_files_tool': { enabled: true, description: 'Create, read, update, and delete files in the workspace with comprehensive file management', icon: '📁', color: 'bg-blue-100 dark:bg-blue-800/50' },\r\n    'sb_browser_tool': { enabled: true, description: 'Browser automation for web navigation, clicking, form filling, and page interaction', icon: '🌐', color: 'bg-indigo-100 dark:bg-indigo-800/50' },\r\n    'sb_deploy_tool': { enabled: true, description: 'Deploy applications and services with automated deployment capabilities', icon: '🚀', color: 'bg-green-100 dark:bg-green-800/50' },\r\n    'sb_expose_tool': { enabled: true, description: 'Expose services and manage ports for application accessibility', icon: '🔌', color: 'bg-orange-100 dark:bg-orange-800/20' },\r\n    'web_search_tool': { enabled: true, description: 'Search the web using Tavily API and scrape webpages with Firecrawl for research', icon: '🔍', color: 'bg-yellow-100 dark:bg-yellow-800/50' },\r\n    'sb_vision_tool': { enabled: true, description: 'Vision and image processing capabilities for visual content analysis', icon: '👁️', color: 'bg-pink-100 dark:bg-pink-800/50' },\r\n    'data_providers_tool': { enabled: true, description: 'Access to data providers and external APIs (requires RapidAPI key)', icon: '🔗', color: 'bg-cyan-100 dark:bg-cyan-800/50' },\r\n};\r\n\r\nexport const getToolDisplayName = (toolName: string): string => {\r\n    const displayNames: Record<string, string> = {\r\n      'sb_shell_tool': 'Terminal',\r\n      'sb_files_tool': 'File Manager',\r\n      'sb_browser_tool': 'Browser Automation',\r\n      'sb_deploy_tool': 'Deploy Tool',\r\n      'sb_expose_tool': 'Port Exposure',\r\n      'web_search_tool': 'Web Search',\r\n      'sb_vision_tool': 'Image Processing',\r\n      'data_providers_tool': 'Data Providers',\r\n    };\r\n    \r\n    return displayNames[toolName] || toolName.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase());\r\n  };"], "names": [], "mappings": ";;;;AAAO,MAAM,2BAAmH;IAC5H,iBAAiB;QAAE,SAAS;QAAM,aAAa;QAAqG,MAAM;QAAM,OAAO;IAAiC;IACxM,iBAAiB;QAAE,SAAS;QAAM,aAAa;QAA8F,MAAM;QAAM,OAAO;IAAkC;IAClM,mBAAmB;QAAE,SAAS;QAAM,aAAa;QAAuF,MAAM;QAAM,OAAO;IAAsC;IACjM,kBAAkB;QAAE,SAAS;QAAM,aAAa;QAA2E,MAAM;QAAM,OAAO;IAAoC;IAClL,kBAAkB;QAAE,SAAS;QAAM,aAAa;QAAkE,MAAM;QAAM,OAAO;IAAsC;IAC3K,mBAAmB;QAAE,SAAS;QAAM,aAAa;QAAmF,MAAM;QAAM,OAAO;IAAsC;IAC7L,kBAAkB;QAAE,SAAS;QAAM,aAAa;QAAwE,MAAM;QAAO,OAAO;IAAkC;IAC9K,uBAAuB;QAAE,SAAS;QAAM,aAAa;QAAsE,MAAM;QAAM,OAAO;IAAkC;AACpL;AAEO,MAAM,qBAAqB,CAAC;IAC/B,MAAM,eAAuC;QAC3C,iBAAiB;QACjB,iBAAiB;QACjB,mBAAmB;QACnB,kBAAkB;QAClB,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,uBAAuB;IACzB;IAEA,OAAO,YAAY,CAAC,SAAS,IAAI,SAAS,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;AAClG", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/triggers/utils.tsx"], "sourcesContent": ["import { FaTelegram } from \"react-icons/fa\";\r\nimport { SlackIcon } from \"@/components/ui/icons/slack\";\r\nimport { Webhook, Clock } from \"lucide-react\";\r\nimport { Zap } from \"lucide-react\";\r\n\r\nexport const getTriggerIcon = (triggerType: string) => {\r\n  switch (triggerType) {\r\n    case 'telegram':\r\n      return <FaTelegram className=\"h-5 w-5\" color=\"#0088cc\" />;\r\n    case 'slack':\r\n      return <SlackIcon className=\"h-5 w-5\" />;\r\n    case 'webhook':\r\n      return <Webhook className=\"h-5 w-5\" />;\r\n    case 'schedule':\r\n      return <Clock className=\"h-5 w-5\" color=\"#10b981\" />;\r\n    default:\r\n      return <Zap className=\"h-5 w-5\" />;\r\n  }\r\n};\r\n\r\nexport const getDialogIcon = (triggerType: string) => {\r\n  switch (triggerType) {\r\n    case 'telegram':\r\n      return <FaTelegram className=\"h-6 w-6\" color=\"#0088cc\" />;\r\n    case 'slack':\r\n      return <SlackIcon className=\"h-6 w-6\" />;\r\n    case 'webhook':\r\n      return <Webhook className=\"h-6 w-6\" />;\r\n    case 'schedule':\r\n      return <Clock className=\"h-6 w-6\" color=\"#10b981\" />;\r\n    default:\r\n      return <Zap className=\"h-5 w-5\" />;\r\n  }\r\n};"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAAA;AACA;;;;;;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OAAQ;QACN,KAAK;YACH,qBAAO,8OAAC,8IAAA,CAAA,aAAU;gBAAC,WAAU;gBAAU,OAAM;;;;;;QAC/C,KAAK;YACH,qBAAO,8OAAC,0IAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAC9B,KAAK;YACH,qBAAO,8OAAC,wMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;QAC5B,KAAK;YACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;gBAAU,OAAM;;;;;;QAC1C;YACE,qBAAO,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;IAC1B;AACF;AAEO,MAAM,gBAAgB,CAAC;IAC5B,OAAQ;QACN,KAAK;YACH,qBAAO,8OAAC,8IAAA,CAAA,aAAU;gBAAC,WAAU;gBAAU,OAAM;;;;;;QAC/C,KAAK;YACH,qBAAO,8OAAC,0IAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;QAC9B,KAAK;YACH,qBAAO,8OAAC,wMAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;QAC5B,KAAK;YACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;gBAAU,OAAM;;;;;;QAC1C;YACE,qBAAO,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/triggers/configured-triggers-list.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React from 'react';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Switch } from '@/components/ui/switch';\r\nimport { \r\n  Edit, \r\n  Trash2, \r\n  ExternalLink, \r\n  MessageSquare, \r\n  Webhook, \r\n  Clock, \r\n  Mail,\r\n  Github,\r\n  Gamepad2,\r\n  Activity,\r\n  Copy\r\n} from 'lucide-react';\r\nimport { TriggerConfiguration } from './types';\r\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';\r\nimport { getTriggerIcon } from './utils';\r\n\r\ninterface ConfiguredTriggersListProps {\r\n  triggers: TriggerConfiguration[];\r\n  onEdit: (trigger: TriggerConfiguration) => void;\r\n  onRemove: (trigger: TriggerConfiguration) => void;\r\n  onToggle: (trigger: TriggerConfiguration) => void;\r\n  isLoading?: boolean;\r\n}\r\n\r\nconst copyToClipboard = async (text: string) => {\r\n  try {\r\n    await navigator.clipboard.writeText(text);\r\n  } catch (err) {\r\n    console.error('Failed to copy text: ', err);\r\n  }\r\n};\r\n\r\nexport const ConfiguredTriggersList: React.FC<ConfiguredTriggersListProps> = ({\r\n  triggers,\r\n  onEdit,\r\n  onRemove,\r\n  onToggle,\r\n  isLoading = false,\r\n}) => {\r\n  return (\r\n    <TooltipProvider>\r\n      <div className=\"space-y-2\">\r\n        {triggers.map((trigger) => (\r\n          <div\r\n            key={trigger.trigger_id}\r\n            className=\"flex items-center justify-between p-4 rounded-lg border bg-card hover:bg-muted/50 transition-colors\"\r\n          >\r\n            <div className=\"flex items-center space-x-4 flex-1\">\r\n              <div className=\"p-2 rounded-lg bg-muted border\">\r\n                {getTriggerIcon(trigger.trigger_type)}\r\n              </div>\r\n              \r\n              <div className=\"flex-1 min-w-0\">\r\n                <div className=\"flex items-center space-x-2 mb-1\">\r\n                  <h4 className=\"text-sm font-medium truncate\">\r\n                    {trigger.name}\r\n                  </h4>\r\n                  <Badge \r\n                    variant={trigger.is_active ? \"default\" : \"secondary\"}\r\n                    className=\"text-xs\"\r\n                  >\r\n                    {trigger.is_active ? \"Active\" : \"Inactive\"}\r\n                  </Badge>\r\n                </div>\r\n                \r\n                {trigger.description && (\r\n                  <p className=\"text-xs text-muted-foreground truncate\">\r\n                    {trigger.description}\r\n                  </p>\r\n                )}\r\n                \r\n                {trigger.webhook_url && (\r\n                  <div className=\"flex items-center space-x-2 mt-2\">\r\n                    <code className=\"text-xs bg-muted px-2 py-1 rounded font-mono max-w-xs truncate\">\r\n                      {trigger.webhook_url}\r\n                    </code>\r\n                    <Tooltip>\r\n                      <TooltipTrigger asChild>\r\n                        <Button\r\n                          size=\"sm\"\r\n                          variant=\"ghost\"\r\n                          className=\"h-6 w-6 p-0\"\r\n                          onClick={() => copyToClipboard(trigger.webhook_url!)}\r\n                        >\r\n                          <Copy className=\"h-3 w-3\" />\r\n                        </Button>\r\n                      </TooltipTrigger>\r\n                      <TooltipContent>\r\n                        <p>Copy webhook URL</p>\r\n                      </TooltipContent>\r\n                    </Tooltip>\r\n                    {trigger.webhook_url.startsWith('http') && (\r\n                      <Tooltip>\r\n                        <TooltipTrigger asChild>\r\n                          <Button\r\n                            size=\"sm\"\r\n                            variant=\"ghost\"\r\n                            className=\"h-6 w-6 p-0\"\r\n                            onClick={() => window.open(trigger.webhook_url, '_blank')}\r\n                          >\r\n                            <ExternalLink className=\"h-3 w-3\" />\r\n                          </Button>\r\n                        </TooltipTrigger>\r\n                        <TooltipContent>\r\n                          <p>Open webhook URL</p>\r\n                        </TooltipContent>\r\n                      </Tooltip>\r\n                    )}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"flex items-center space-x-2\">\r\n              <Tooltip>\r\n                <TooltipTrigger asChild>\r\n                  <div className=\"flex items-center\">\r\n                    <Switch\r\n                      checked={trigger.is_active}\r\n                      onCheckedChange={() => onToggle(trigger)}\r\n                      disabled={isLoading}\r\n                    />\r\n                  </div>\r\n                </TooltipTrigger>\r\n                <TooltipContent>\r\n                  <p>{trigger.is_active ? 'Disable' : 'Enable'} trigger</p>\r\n                </TooltipContent>\r\n              </Tooltip>\r\n              \r\n              <Tooltip>\r\n                <TooltipTrigger asChild>\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"ghost\"\r\n                    onClick={() => onEdit(trigger)}\r\n                    className=\"h-8 w-8 p-0\"\r\n                    disabled={isLoading}\r\n                  >\r\n                    <Edit className=\"h-4 w-4\" />\r\n                  </Button>\r\n                </TooltipTrigger>\r\n                <TooltipContent>\r\n                  <p>Edit trigger</p>\r\n                </TooltipContent>\r\n              </Tooltip>\r\n              \r\n              <Tooltip>\r\n                <TooltipTrigger asChild>\r\n                  <Button\r\n                    size=\"sm\"\r\n                    variant=\"ghost\"\r\n                    onClick={() => onRemove(trigger)}\r\n                    className=\"h-8 w-8 p-0 text-destructive hover:text-destructive\"\r\n                    disabled={isLoading}\r\n                  >\r\n                    <Trash2 className=\"h-4 w-4\" />\r\n                  </Button>\r\n                </TooltipTrigger>\r\n                <TooltipContent>\r\n                  <p>Delete trigger</p>\r\n                </TooltipContent>\r\n              </Tooltip>\r\n            </div>\r\n          </div>\r\n        ))}\r\n      </div>\r\n    </TooltipProvider>\r\n  );\r\n}; "], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAcA;AACA;AArBA;;;;;;;;AA+BA,MAAM,kBAAkB,OAAO;IAC7B,IAAI;QACF,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;IACtC,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,yBAAyB;IACzC;AACF;AAEO,MAAM,yBAAgE,CAAC,EAC5E,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,YAAY,KAAK,EAClB;IACC,qBACE,8OAAC,mIAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC;YAAI,WAAU;sBACZ,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;oBAEC,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,YAAY;;;;;;8CAGtC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DACX,QAAQ,IAAI;;;;;;8DAEf,8OAAC,iIAAA,CAAA,QAAK;oDACJ,SAAS,QAAQ,SAAS,GAAG,YAAY;oDACzC,WAAU;8DAET,QAAQ,SAAS,GAAG,WAAW;;;;;;;;;;;;wCAInC,QAAQ,WAAW,kBAClB,8OAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;wCAIvB,QAAQ,WAAW,kBAClB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DACb,QAAQ,WAAW;;;;;;8DAEtB,8OAAC,mIAAA,CAAA,UAAO;;sEACN,8OAAC,mIAAA,CAAA,iBAAc;4DAAC,OAAO;sEACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,WAAU;gEACV,SAAS,IAAM,gBAAgB,QAAQ,WAAW;0EAElD,cAAA,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAGpB,8OAAC,mIAAA,CAAA,iBAAc;sEACb,cAAA,8OAAC;0EAAE;;;;;;;;;;;;;;;;;gDAGN,QAAQ,WAAW,CAAC,UAAU,CAAC,yBAC9B,8OAAC,mIAAA,CAAA,UAAO;;sEACN,8OAAC,mIAAA,CAAA,iBAAc;4DAAC,OAAO;sEACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gEACL,MAAK;gEACL,SAAQ;gEACR,WAAU;gEACV,SAAS,IAAM,OAAO,IAAI,CAAC,QAAQ,WAAW,EAAE;0EAEhD,cAAA,8OAAC,sNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;;;;;;sEAG5B,8OAAC,mIAAA,CAAA,iBAAc;sEACb,cAAA,8OAAC;0EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,mIAAA,CAAA,UAAO;;sDACN,8OAAC,mIAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAS,QAAQ,SAAS;oDAC1B,iBAAiB,IAAM,SAAS;oDAChC,UAAU;;;;;;;;;;;;;;;;sDAIhB,8OAAC,mIAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC;;oDAAG,QAAQ,SAAS,GAAG,YAAY;oDAAS;;;;;;;;;;;;;;;;;;8CAIjD,8OAAC,mIAAA,CAAA,UAAO;;sDACN,8OAAC,mIAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,OAAO;gDACtB,WAAU;gDACV,UAAU;0DAEV,cAAA,8OAAC,2MAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAGpB,8OAAC,mIAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC;0DAAE;;;;;;;;;;;;;;;;;8CAIP,8OAAC,mIAAA,CAAA,UAAO;;sDACN,8OAAC,mIAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,SAAS;gDACxB,WAAU;gDACV,UAAU;0DAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAGtB,8OAAC,mIAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC;0DAAE;;;;;;;;;;;;;;;;;;;;;;;;mBAnHJ,QAAQ,UAAU;;;;;;;;;;;;;;;AA4HnC", "debugId": null}}, {"offset": {"line": 566, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/triggers/providers/schedule-config.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { Card, CardContent, CardDescription, CardHeader } from '@/components/ui/card';\r\nimport { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';\r\nimport { Calendar } from '@/components/ui/calendar';\r\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\r\nimport { Clock, Calendar as CalendarIcon, Info, Zap, Repeat, Timer, Target } from 'lucide-react';\r\nimport { format, startOfDay } from 'date-fns';\r\nimport { cn } from '@/lib/utils';\r\nimport { TriggerProvider, ScheduleTriggerConfig } from '../types';\r\nimport { useAgentWorkflows } from '@/hooks/react-query/agents/use-agent-workflows';\r\n\r\ninterface ScheduleTriggerConfigFormProps {\r\n  provider: TriggerProvider;\r\n  config: ScheduleTriggerConfig;\r\n  onChange: (config: ScheduleTriggerConfig) => void;\r\n  errors: Record<string, string>;\r\n  agentId: string;\r\n}\r\n\r\ntype ScheduleType = 'quick' | 'recurring' | 'advanced' | 'one-time';\r\n\r\ninterface QuickPreset {\r\n  name: string;\r\n  cron: string;\r\n  description: string;\r\n  icon: React.ReactNode;\r\n  category: 'frequent' | 'daily' | 'weekly' | 'monthly';\r\n}\r\n\r\nconst QUICK_PRESETS: QuickPreset[] = [\r\n  { name: 'Every minute', cron: '* * * * *', description: 'Every minute', icon: <Zap className=\"h-4 w-4\" />, category: 'frequent' },\r\n  { name: 'Every 5 minutes', cron: '*/5 * * * *', description: 'Every 5 minutes', icon: <Timer className=\"h-4 w-4\" />, category: 'frequent' },\r\n  { name: 'Every 15 minutes', cron: '*/15 * * * *', description: 'Every 15 minutes', icon: <Timer className=\"h-4 w-4\" />, category: 'frequent' },\r\n  { name: 'Every 30 minutes', cron: '*/30 * * * *', description: 'Every 30 minutes', icon: <Timer className=\"h-4 w-4\" />, category: 'frequent' },\r\n  { name: 'Every hour', cron: '0 * * * *', description: 'At the start of every hour', icon: <Clock className=\"h-4 w-4\" />, category: 'frequent' },\r\n  \r\n  { name: 'Daily at 9 AM', cron: '0 9 * * *', description: 'Every day at 9:00 AM', icon: <Target className=\"h-4 w-4\" />, category: 'daily' },\r\n  { name: 'Daily at 12 PM', cron: '0 12 * * *', description: 'Every day at 12:00 PM', icon: <Target className=\"h-4 w-4\" />, category: 'daily' },\r\n  { name: 'Daily at 6 PM', cron: '0 18 * * *', description: 'Every day at 6:00 PM', icon: <Target className=\"h-4 w-4\" />, category: 'daily' },\r\n  { name: 'Twice daily', cron: '0 9,17 * * *', description: 'Every day at 9 AM and 5 PM', icon: <Repeat className=\"h-4 w-4\" />, category: 'daily' },\r\n  \r\n  { name: 'Weekdays at 9 AM', cron: '0 9 * * 1-5', description: 'Monday-Friday at 9:00 AM', icon: <Target className=\"h-4 w-4\" />, category: 'weekly' },\r\n  { name: 'Monday mornings', cron: '0 9 * * 1', description: 'Every Monday at 9:00 AM', icon: <CalendarIcon className=\"h-4 w-4\" />, category: 'weekly' },\r\n  { name: 'Friday evenings', cron: '0 17 * * 5', description: 'Every Friday at 5:00 PM', icon: <CalendarIcon className=\"h-4 w-4\" />, category: 'weekly' },\r\n  { name: 'Weekend mornings', cron: '0 10 * * 0,6', description: 'Saturday & Sunday at 10:00 AM', icon: <CalendarIcon className=\"h-4 w-4\" />, category: 'weekly' },\r\n  \r\n  { name: 'Monthly on 1st', cron: '0 9 1 * *', description: 'First day of month at 9:00 AM', icon: <CalendarIcon className=\"h-4 w-4\" />, category: 'monthly' },\r\n  { name: 'Monthly on 15th', cron: '0 9 15 * *', description: '15th of month at 9:00 AM', icon: <CalendarIcon className=\"h-4 w-4\" />, category: 'monthly' },\r\n  { name: 'End of month', cron: '0 9 28-31 * *', description: 'Last few days of month at 9:00 AM', icon: <CalendarIcon className=\"h-4 w-4\" />, category: 'monthly' },\r\n];\r\n\r\nconst TIMEZONES = [\r\n  { value: 'UTC', label: 'UTC (Coordinated Universal Time)' },\r\n  { value: 'America/New_York', label: 'Eastern Time (ET)' },\r\n  { value: 'America/Chicago', label: 'Central Time (CT)' },\r\n  { value: 'America/Denver', label: 'Mountain Time (MT)' },\r\n  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },\r\n  { value: 'Europe/London', label: 'Greenwich Mean Time (GMT)' },\r\n  { value: 'Europe/Paris', label: 'Central European Time (CET)' },\r\n  { value: 'Europe/Berlin', label: 'Central European Time (CET)' },\r\n  { value: 'Asia/Tokyo', label: 'Japan Standard Time (JST)' },\r\n  { value: 'Asia/Shanghai', label: 'China Standard Time (CST)' },\r\n  { value: 'Australia/Sydney', label: 'Australian Eastern Time (AET)' },\r\n];\r\n\r\nconst WEEKDAYS = [\r\n  { value: '1', label: 'Monday', short: 'Mon' },\r\n  { value: '2', label: 'Tuesday', short: 'Tue' },\r\n  { value: '3', label: 'Wednesday', short: 'Wed' },\r\n  { value: '4', label: 'Thursday', short: 'Thu' },\r\n  { value: '5', label: 'Friday', short: 'Fri' },\r\n  { value: '6', label: 'Saturday', short: 'Sat' },\r\n  { value: '0', label: 'Sunday', short: 'Sun' },\r\n];\r\n\r\nconst MONTHS = [\r\n  { value: '1', label: 'January' },\r\n  { value: '2', label: 'February' },\r\n  { value: '3', label: 'March' },\r\n  { value: '4', label: 'April' },\r\n  { value: '5', label: 'May' },\r\n  { value: '6', label: 'June' },\r\n  { value: '7', label: 'July' },\r\n  { value: '8', label: 'August' },\r\n  { value: '9', label: 'September' },\r\n  { value: '10', label: 'October' },\r\n  { value: '11', label: 'November' },\r\n  { value: '12', label: 'December' },\r\n];\r\n\r\nexport const ScheduleTriggerConfigForm: React.FC<ScheduleTriggerConfigFormProps> = ({\r\n  provider,\r\n  config,\r\n  onChange,\r\n  errors,\r\n  agentId,\r\n}) => {\r\n  const { data: workflows = [], isLoading: isLoadingWorkflows } = useAgentWorkflows(agentId);\r\n  const [scheduleType, setScheduleType] = useState<ScheduleType>('quick');\r\n  const [selectedPreset, setSelectedPreset] = useState<string>('');\r\n  \r\n  const [recurringType, setRecurringType] = useState<'daily' | 'weekly' | 'monthly'>('daily');\r\n  const [selectedWeekdays, setSelectedWeekdays] = useState<string[]>(['1', '2', '3', '4', '5']);\r\n  const [selectedMonths, setSelectedMonths] = useState<string[]>(['*']);\r\n  const [dayOfMonth, setDayOfMonth] = useState<string>('1');\r\n  const [scheduleTime, setScheduleTime] = useState<{ hour: string; minute: string }>({ hour: '09', minute: '00' });\r\n  \r\n  const [selectedDate, setSelectedDate] = useState<Date>();\r\n  const [oneTimeTime, setOneTimeTime] = useState<{ hour: string; minute: string }>({ hour: '09', minute: '00' });\r\n\r\n\r\n\r\n  const generateCronExpression = () => {\r\n    if (scheduleType === 'quick' && selectedPreset) {\r\n      return selectedPreset;\r\n    }\r\n    if (scheduleType === 'recurring') {\r\n      const { hour, minute } = scheduleTime;\r\n      switch (recurringType) {\r\n        case 'daily':\r\n          return `${minute} ${hour} * * *`;\r\n        case 'weekly':\r\n          const weekdayStr = selectedWeekdays.join(',');\r\n          return `${minute} ${hour} * * ${weekdayStr}`;\r\n        case 'monthly':\r\n          const monthStr = selectedMonths.includes('*') ? '*' : selectedMonths.join(',');\r\n          return `${minute} ${hour} ${dayOfMonth} ${monthStr} *`;\r\n        default:\r\n          return `${minute} ${hour} * * *`;\r\n      }\r\n    }\r\n    if (scheduleType === 'one-time' && selectedDate) {\r\n      const { hour, minute } = oneTimeTime;\r\n      const day = selectedDate.getDate();\r\n      const month = selectedDate.getMonth() + 1;\r\n      const year = selectedDate.getFullYear();\r\n      return `${minute} ${hour} ${day} ${month} *`;\r\n    }\r\n    return config.cron_expression || '';\r\n  };\r\n\r\n  useEffect(() => {\r\n    const newCron = generateCronExpression();\r\n    if (newCron && newCron !== config.cron_expression) {\r\n      onChange({\r\n        ...config,\r\n        cron_expression: newCron,\r\n      });\r\n    }\r\n  }, [scheduleType, selectedPreset, recurringType, selectedWeekdays, selectedMonths, dayOfMonth, scheduleTime, selectedDate, oneTimeTime]);\r\n\r\n  const handlePresetSelect = (preset: QuickPreset) => {\r\n    setSelectedPreset(preset.cron);\r\n    onChange({\r\n      ...config,\r\n      cron_expression: preset.cron,\r\n    });\r\n  };\r\n\r\n  const handleAgentPromptChange = (value: string) => {\r\n    onChange({\r\n      ...config,\r\n      agent_prompt: value,\r\n    });\r\n  };\r\n\r\n  const handleTimezoneChange = (value: string) => {\r\n    onChange({\r\n      ...config,\r\n      timezone: value,\r\n    });\r\n  };\r\n\r\n  const handleExecutionTypeChange = (value: 'agent' | 'workflow') => {\r\n    const newConfig = {\r\n      ...config,\r\n      execution_type: value,\r\n    };\r\n    if (value === 'agent') {\r\n      delete newConfig.workflow_id;\r\n      delete newConfig.workflow_input;\r\n    } else {\r\n      delete newConfig.agent_prompt;\r\n      if (!newConfig.workflow_input) {\r\n        newConfig.workflow_input = { prompt: '' };\r\n      }\r\n    }\r\n    onChange(newConfig);\r\n  };\r\n\r\n  const handleWorkflowChange = (workflowId: string) => {\r\n    if (workflowId.startsWith('__')) {\r\n      return;\r\n    }\r\n    onChange({\r\n      ...config,\r\n      workflow_id: workflowId,\r\n    });\r\n  };\r\n\r\n\r\n\r\n  const handleWeekdayToggle = (weekday: string) => {\r\n    setSelectedWeekdays(prev => \r\n      prev.includes(weekday) \r\n        ? prev.filter(w => w !== weekday)\r\n        : [...prev, weekday].sort()\r\n    );\r\n  };\r\n\r\n  const handleMonthToggle = (month: string) => {\r\n    if (month === '*') {\r\n      setSelectedMonths(['*']);\r\n    } else {\r\n      setSelectedMonths(prev => {\r\n        const filtered = prev.filter(m => m !== '*');\r\n        return filtered.includes(month)\r\n          ? filtered.filter(m => m !== month)\r\n          : [...filtered, month].sort((a, b) => parseInt(a) - parseInt(b));\r\n      });\r\n    }\r\n  };\r\n\r\n  const groupedPresets = QUICK_PRESETS.reduce((acc, preset) => {\r\n    if (!acc[preset.category]) acc[preset.category] = [];\r\n    acc[preset.category].push(preset);\r\n    return acc;\r\n  }, {} as Record<string, QuickPreset[]>);\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <Card className=\"border-none bg-transparent shadow-none p-0\">\r\n        <CardHeader className='p-0 -mt-2'>\r\n          <CardDescription>\r\n            Configure when your agent should be triggered automatically. Choose from quick presets, recurring schedules, or set up advanced cron expressions.\r\n          </CardDescription>\r\n        </CardHeader>\r\n        <CardContent className=\"p-0\">\r\n          <Tabs value={scheduleType} onValueChange={(value) => setScheduleType(value as ScheduleType)} className=\"w-full\">\r\n            <TabsList className=\"grid w-full grid-cols-4\">\r\n              <TabsTrigger value=\"quick\" className=\"flex items-center gap-2\">\r\n                <Zap className=\"h-4 w-4\" />\r\n                Quick\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"recurring\" className=\"flex items-center gap-2\">\r\n                <Repeat className=\"h-4 w-4\" />\r\n                Recurring\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"one-time\" className=\"flex items-center gap-2\">\r\n                <CalendarIcon className=\"h-4 w-4\" />\r\n                One-time\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"advanced\" className=\"flex items-center gap-2\">\r\n                <Target className=\"h-4 w-4\" />\r\n                Advanced\r\n              </TabsTrigger>\r\n            </TabsList>\r\n\r\n            <TabsContent value=\"quick\" className=\"space-y-4 mt-6\">\r\n              <div className=\"space-y-4\">\r\n                {Object.entries(groupedPresets).map(([category, presets]) => (\r\n                  <div key={category}>\r\n                    <h4 className=\"text-sm font-medium mb-3 capitalize\">{category} Schedules</h4>\r\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\r\n                      {presets.map((preset) => (\r\n                        <Card \r\n                          key={preset.cron}\r\n                          className={cn(\r\n                            \"p-0 cursor-pointer transition-colors hover:bg-accent\",\r\n                            selectedPreset === preset.cron && \"ring-2 ring-primary bg-accent\"\r\n                          )}\r\n                          onClick={() => handlePresetSelect(preset)}\r\n                        >\r\n                          <CardContent className=\"p-4\">\r\n                            <div className=\"flex items-center gap-3\">\r\n                              <div className=\"text-primary\">{preset.icon}</div>\r\n                              <div className=\"flex-1\">\r\n                                <div className=\"font-medium text-sm\">{preset.name}</div>\r\n                                <div className=\"text-xs text-muted-foreground\">{preset.description}</div>\r\n                              </div>\r\n                            </div>\r\n                          </CardContent>\r\n                        </Card>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n              </div>\r\n            </TabsContent>\r\n\r\n            <TabsContent value=\"recurring\" className=\"space-y-6 mt-6\">\r\n              <div className=\"space-y-4\">\r\n                <div>\r\n                  <Label className=\"text-sm font-medium mb-3 block\">Schedule Type</Label>\r\n                  <RadioGroup value={recurringType} onValueChange={(value) => setRecurringType(value as any)}>\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <RadioGroupItem value=\"daily\" id=\"daily\" />\r\n                      <Label htmlFor=\"daily\">Daily</Label>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <RadioGroupItem value=\"weekly\" id=\"weekly\" />\r\n                      <Label htmlFor=\"weekly\">Weekly</Label>\r\n                    </div>\r\n                    <div className=\"flex items-center space-x-2\">\r\n                      <RadioGroupItem value=\"monthly\" id=\"monthly\" />\r\n                      <Label htmlFor=\"monthly\">Monthly</Label>\r\n                    </div>\r\n                  </RadioGroup>\r\n                </div>\r\n\r\n                {recurringType === 'weekly' && (\r\n                  <div>\r\n                    <Label className=\"text-sm font-medium mb-3 block\">Days of Week</Label>\r\n                    <div className=\"flex flex-wrap gap-2\">\r\n                      {WEEKDAYS.map((day) => (\r\n                        <Button\r\n                          key={day.value}\r\n                          variant={selectedWeekdays.includes(day.value) ? \"default\" : \"outline\"}\r\n                          size=\"sm\"\r\n                          onClick={() => handleWeekdayToggle(day.value)}\r\n                        >\r\n                          {day.short}\r\n                        </Button>\r\n                      ))}\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                {recurringType === 'monthly' && (\r\n                  <div className=\"space-y-4\">\r\n                    <div>\r\n                      <Label className=\"text-sm font-medium mb-3 block\">Day of Month</Label>\r\n                      <Select value={dayOfMonth} onValueChange={setDayOfMonth}>\r\n                        <SelectTrigger className=\"w-full\">\r\n                          <SelectValue />\r\n                        </SelectTrigger>\r\n                        <SelectContent>\r\n                          {Array.from({ length: 31 }, (_, i) => (\r\n                            <SelectItem key={i + 1} value={(i + 1).toString()}>\r\n                              {i + 1}\r\n                            </SelectItem>\r\n                          ))}\r\n                        </SelectContent>\r\n                      </Select>\r\n                    </div>\r\n                    <div>\r\n                      <Label className=\"text-sm font-medium mb-3 block\">Months</Label>\r\n                      <div className=\"space-y-2\">\r\n                        <Button\r\n                          variant={selectedMonths.includes('*') ? \"default\" : \"outline\"}\r\n                          size=\"sm\"\r\n                          onClick={() => handleMonthToggle('*')}\r\n                        >\r\n                          All Months\r\n                        </Button>\r\n                        <div className=\"grid grid-cols-3 gap-2\">\r\n                          {MONTHS.map((month) => (\r\n                            <Button\r\n                              key={month.value}\r\n                              variant={selectedMonths.includes(month.value) ? \"default\" : \"outline\"}\r\n                              size=\"sm\"\r\n                              onClick={() => handleMonthToggle(month.value)}\r\n                              disabled={selectedMonths.includes('*')}\r\n                            >\r\n                              {month.label.slice(0, 3)}\r\n                            </Button>\r\n                          ))}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                <div>\r\n                  <Label className=\"text-sm font-medium mb-3 block\">Time</Label>\r\n                  <div className=\"flex gap-2 items-center\">\r\n                    <Select value={scheduleTime.hour} onValueChange={(value) => setScheduleTime(prev => ({ ...prev, hour: value }))}>\r\n                      <SelectTrigger className=\"w-20\">\r\n                        <SelectValue />\r\n                      </SelectTrigger>\r\n                      <SelectContent>\r\n                        {Array.from({ length: 24 }, (_, i) => (\r\n                          <SelectItem key={i} value={i.toString().padStart(2, '0')}>\r\n                            {i.toString().padStart(2, '0')}\r\n                          </SelectItem>\r\n                        ))}\r\n                      </SelectContent>\r\n                    </Select>\r\n                    <span>:</span>\r\n                    <Select value={scheduleTime.minute} onValueChange={(value) => setScheduleTime(prev => ({ ...prev, minute: value }))}>\r\n                      <SelectTrigger className=\"w-20\">\r\n                        <SelectValue />\r\n                      </SelectTrigger>\r\n                      <SelectContent>\r\n                        {Array.from({ length: 60 }, (_, i) => (\r\n                          <SelectItem key={i} value={i.toString().padStart(2, '0')}>\r\n                            {i.toString().padStart(2, '0')}\r\n                          </SelectItem>\r\n                        ))}\r\n                      </SelectContent>\r\n                    </Select>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </TabsContent>\r\n\r\n            <TabsContent value=\"one-time\" className=\"space-y-6 mt-6\">\r\n              <div className=\"space-y-4\">\r\n                <div>\r\n                  <Label className=\"text-sm font-medium mb-3 block\">Date</Label>\r\n                  <Popover>\r\n                    <PopoverTrigger asChild>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        className={cn(\r\n                          \"w-full justify-start text-left font-normal\",\r\n                          !selectedDate && \"text-muted-foreground\"\r\n                        )}\r\n                      >\r\n                        <CalendarIcon className=\"h-4 w-4\" />\r\n                        {selectedDate ? format(selectedDate, \"PPP\") : \"Pick a date\"}\r\n                      </Button>\r\n                    </PopoverTrigger>\r\n                    <PopoverContent className=\"w-auto p-0\" align=\"start\">\r\n                      <Calendar\r\n                        mode=\"single\"\r\n                        selected={selectedDate}\r\n                        onSelect={setSelectedDate}\r\n                        disabled={(date) => date < startOfDay(new Date())}\r\n                        initialFocus\r\n                      />\r\n                    </PopoverContent>\r\n                  </Popover>\r\n                </div>\r\n\r\n                <div>\r\n                  <Label className=\"text-sm font-medium mb-3 block\">Time</Label>\r\n                  <div className=\"flex gap-2 items-center\">\r\n                    <Select value={oneTimeTime.hour} onValueChange={(value) => setOneTimeTime(prev => ({ ...prev, hour: value }))}>\r\n                      <SelectTrigger className=\"w-20\">\r\n                        <SelectValue />\r\n                      </SelectTrigger>\r\n                      <SelectContent>\r\n                        {Array.from({ length: 24 }, (_, i) => (\r\n                          <SelectItem key={i} value={i.toString().padStart(2, '0')}>\r\n                            {i.toString().padStart(2, '0')}\r\n                          </SelectItem>\r\n                        ))}\r\n                      </SelectContent>\r\n                    </Select>\r\n                    <span>:</span>\r\n                    <Select value={oneTimeTime.minute} onValueChange={(value) => setOneTimeTime(prev => ({ ...prev, minute: value }))}>\r\n                      <SelectTrigger className=\"w-20\">\r\n                        <SelectValue />\r\n                      </SelectTrigger>\r\n                      <SelectContent>\r\n                        {Array.from({ length: 60 }, (_, i) => (\r\n                          <SelectItem key={i} value={i.toString().padStart(2, '0')}>\r\n                            {i.toString().padStart(2, '0')}\r\n                          </SelectItem>\r\n                        ))}\r\n                      </SelectContent>\r\n                    </Select>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </TabsContent>\r\n\r\n            <TabsContent value=\"advanced\" className=\"space-y-4 mt-6\">\r\n              <div>\r\n                <Label htmlFor=\"cron_expression\" className=\"text-sm font-medium\">\r\n                  Cron Expression *\r\n                </Label>\r\n                <Input\r\n                  id=\"cron_expression\"\r\n                  type=\"text\"\r\n                  value={config.cron_expression || ''}\r\n                  onChange={(e) => onChange({ ...config, cron_expression: e.target.value })}\r\n                  placeholder=\"0 9 * * 1-5\"\r\n                  className={errors.cron_expression ? 'border-destructive' : ''}\r\n                />\r\n                {errors.cron_expression && (\r\n                  <p className=\"text-xs text-destructive mt-1\">{errors.cron_expression}</p>\r\n                )}\r\n                <Card className=\"mt-3 p-0 py-4\">\r\n                  <CardContent>\r\n                    <div className=\"flex items-center gap-2 mb-2\">\r\n                      <Info className=\"h-4 w-4 text-muted-foreground\" />\r\n                      <span className=\"text-sm font-medium\">Cron Format</span>\r\n                    </div>\r\n                    <div className=\"text-sm text-muted-foreground space-y-1\">\r\n                      <div>Format: <code className=\"bg-muted px-1 rounded text-xs\">minute hour day month weekday</code></div>\r\n                      <div>Example: <code className=\"bg-muted px-1 rounded text-xs\">0 9 * * 1-5</code> = Weekdays at 9 AM</div>\r\n                      <div>Use <code className=\"bg-muted px-1 rounded text-xs\">*</code> for any value, <code className=\"bg-muted px-1 rounded text-xs\">*/5</code> for every 5 units</div>\r\n                    </div>\r\n                  </CardContent>\r\n                </Card>\r\n              </div>\r\n            </TabsContent>\r\n          </Tabs>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      <Card className=\"border-none bg-transparent shadow-none p-0\">\r\n        <CardContent className=\"space-y-4 p-0\">\r\n          <div>\r\n            <Label htmlFor=\"timezone\" className=\"text-sm font-medium\">\r\n              Timezone\r\n            </Label>\r\n            <Select value={config.timezone || 'UTC'} onValueChange={handleTimezoneChange}>\r\n              <SelectTrigger>\r\n                <SelectValue placeholder=\"Select timezone\" />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                {TIMEZONES.map((tz) => (\r\n                  <SelectItem key={tz.value} value={tz.value}>\r\n                    {tz.label}\r\n                  </SelectItem>\r\n                ))}\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n          <div>\r\n            <Label className=\"text-sm font-medium mb-3 block\">\r\n              Execution Type *\r\n            </Label>\r\n            <RadioGroup value={config.execution_type || 'agent'} onValueChange={handleExecutionTypeChange}>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <RadioGroupItem value=\"agent\" id=\"execution-agent\" />\r\n                <Label htmlFor=\"execution-agent\">Execute Agent</Label>\r\n              </div>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <RadioGroupItem value=\"workflow\" id=\"execution-workflow\" />\r\n                <Label htmlFor=\"execution-workflow\">Execute Workflow</Label>\r\n              </div>\r\n            </RadioGroup>\r\n            <p className=\"text-xs text-muted-foreground mt-1\">\r\n              Choose whether to execute the agent directly or run a specific workflow.\r\n            </p>\r\n          </div>\r\n          {config.execution_type === 'workflow' ? (\r\n            <div className=\"space-y-4\">\r\n              <div>\r\n                <Label htmlFor=\"workflow_id\" className=\"text-sm font-medium\">\r\n                  Workflow *\r\n                </Label>\r\n                <Select value={config.workflow_id || ''} onValueChange={handleWorkflowChange}>\r\n                  <SelectTrigger className={errors.workflow_id ? 'border-destructive' : ''}>\r\n                    <SelectValue placeholder=\"Select a workflow\" />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    {isLoadingWorkflows ? (\r\n                      <SelectItem value=\"__loading__\" disabled>Loading workflows...</SelectItem>\r\n                    ) : workflows.length === 0 ? (\r\n                      <SelectItem value=\"__no_workflows__\" disabled>No workflows available</SelectItem>\r\n                    ) : (\r\n                      workflows.filter(w => w.status === 'active').map((workflow) => (\r\n                        <SelectItem key={workflow.id} value={workflow.id}>\r\n                          {workflow.name}\r\n                        </SelectItem>\r\n                      ))\r\n                    )}\r\n                  </SelectContent>\r\n                </Select>\r\n                {errors.workflow_id && (\r\n                  <p className=\"text-xs text-destructive mt-1\">{errors.workflow_id}</p>\r\n                )}\r\n                <p className=\"text-xs text-muted-foreground mt-1\">\r\n                  Select the workflow to execute when triggered.\r\n                </p>\r\n              </div>\r\n\r\n              <div>\r\n                <Label htmlFor=\"workflow_input\" className=\"text-sm font-medium\">\r\n                  Instructions for Workflow\r\n                </Label>\r\n                <Textarea\r\n                  id=\"workflow_input\"\r\n                  value={config.workflow_input?.prompt || config.workflow_input?.message || ''}\r\n                  onChange={(e) => {\r\n                    onChange({\r\n                      ...config,\r\n                      workflow_input: { prompt: e.target.value },\r\n                    });\r\n                  }}\r\n                  placeholder=\"Write what you want the workflow to do...\"\r\n                  rows={3}\r\n                  className={errors.workflow_input ? 'border-destructive' : ''}\r\n                />\r\n                {errors.workflow_input && (\r\n                  <p className=\"text-xs text-destructive mt-1\">{errors.workflow_input}</p>\r\n                )}\r\n                <p className=\"text-xs text-muted-foreground mt-1\">\r\n                  Simply describe what you want the workflow to accomplish. The workflow will interpret your instructions naturally.\r\n                </p>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div>\r\n              <Label htmlFor=\"agent_prompt\" className=\"text-sm font-medium\">\r\n                Agent Prompt *\r\n              </Label>\r\n              <Textarea\r\n                id=\"agent_prompt\"\r\n                value={config.agent_prompt || ''}\r\n                onChange={(e) => handleAgentPromptChange(e.target.value)}\r\n                placeholder=\"Enter the prompt that will be sent to your agent when triggered...\"\r\n                rows={4}\r\n                className={errors.agent_prompt ? 'border-destructive' : ''}\r\n              />\r\n              {errors.agent_prompt && (\r\n                <p className=\"text-xs text-destructive mt-1\">{errors.agent_prompt}</p>\r\n              )}\r\n              <p className=\"text-xs text-muted-foreground mt-1\">\r\n                This prompt will be sent to your agent each time the schedule triggers.\r\n              </p>\r\n            </div>\r\n          )}\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAEA;AAjBA;;;;;;;;;;;;;;;;;AAqCA,MAAM,gBAA+B;IACnC;QAAE,MAAM;QAAgB,MAAM;QAAa,aAAa;QAAgB,oBAAM,8OAAC,gMAAA,CAAA,MAAG;YAAC,WAAU;;;;;;QAAc,UAAU;IAAW;IAChI;QAAE,MAAM;QAAmB,MAAM;QAAe,aAAa;QAAmB,oBAAM,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAAc,UAAU;IAAW;IAC1I;QAAE,MAAM;QAAoB,MAAM;QAAgB,aAAa;QAAoB,oBAAM,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAAc,UAAU;IAAW;IAC7I;QAAE,MAAM;QAAoB,MAAM;QAAgB,aAAa;QAAoB,oBAAM,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAAc,UAAU;IAAW;IAC7I;QAAE,MAAM;QAAc,MAAM;QAAa,aAAa;QAA8B,oBAAM,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;;;;;;QAAc,UAAU;IAAW;IAE9I;QAAE,MAAM;QAAiB,MAAM;QAAa,aAAa;QAAwB,oBAAM,8OAAC,sMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAAc,UAAU;IAAQ;IACzI;QAAE,MAAM;QAAkB,MAAM;QAAc,aAAa;QAAyB,oBAAM,8OAAC,sMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAAc,UAAU;IAAQ;IAC5I;QAAE,MAAM;QAAiB,MAAM;QAAc,aAAa;QAAwB,oBAAM,8OAAC,sMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAAc,UAAU;IAAQ;IAC1I;QAAE,MAAM;QAAe,MAAM;QAAgB,aAAa;QAA8B,oBAAM,8OAAC,sMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAAc,UAAU;IAAQ;IAEhJ;QAAE,MAAM;QAAoB,MAAM;QAAe,aAAa;QAA4B,oBAAM,8OAAC,sMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;QAAc,UAAU;IAAS;IACnJ;QAAE,MAAM;QAAmB,MAAM;QAAa,aAAa;QAA2B,oBAAM,8OAAC,0MAAA,CAAA,WAAY;YAAC,WAAU;;;;;;QAAc,UAAU;IAAS;IACrJ;QAAE,MAAM;QAAmB,MAAM;QAAc,aAAa;QAA2B,oBAAM,8OAAC,0MAAA,CAAA,WAAY;YAAC,WAAU;;;;;;QAAc,UAAU;IAAS;IACtJ;QAAE,MAAM;QAAoB,MAAM;QAAgB,aAAa;QAAiC,oBAAM,8OAAC,0MAAA,CAAA,WAAY;YAAC,WAAU;;;;;;QAAc,UAAU;IAAS;IAE/J;QAAE,MAAM;QAAkB,MAAM;QAAa,aAAa;QAAiC,oBAAM,8OAAC,0MAAA,CAAA,WAAY;YAAC,WAAU;;;;;;QAAc,UAAU;IAAU;IAC3J;QAAE,MAAM;QAAmB,MAAM;QAAc,aAAa;QAA4B,oBAAM,8OAAC,0MAAA,CAAA,WAAY;YAAC,WAAU;;;;;;QAAc,UAAU;IAAU;IACxJ;QAAE,MAAM;QAAgB,MAAM;QAAiB,aAAa;QAAqC,oBAAM,8OAAC,0MAAA,CAAA,WAAY;YAAC,WAAU;;;;;;QAAc,UAAU;IAAU;CAClK;AAED,MAAM,YAAY;IAChB;QAAE,OAAO;QAAO,OAAO;IAAmC;IAC1D;QAAE,OAAO;QAAoB,OAAO;IAAoB;IACxD;QAAE,OAAO;QAAmB,OAAO;IAAoB;IACvD;QAAE,OAAO;QAAkB,OAAO;IAAqB;IACvD;QAAE,OAAO;QAAuB,OAAO;IAAoB;IAC3D;QAAE,OAAO;QAAiB,OAAO;IAA4B;IAC7D;QAAE,OAAO;QAAgB,OAAO;IAA8B;IAC9D;QAAE,OAAO;QAAiB,OAAO;IAA8B;IAC/D;QAAE,OAAO;QAAc,OAAO;IAA4B;IAC1D;QAAE,OAAO;QAAiB,OAAO;IAA4B;IAC7D;QAAE,OAAO;QAAoB,OAAO;IAAgC;CACrE;AAED,MAAM,WAAW;IACf;QAAE,OAAO;QAAK,OAAO;QAAU,OAAO;IAAM;IAC5C;QAAE,OAAO;QAAK,OAAO;QAAW,OAAO;IAAM;IAC7C;QAAE,OAAO;QAAK,OAAO;QAAa,OAAO;IAAM;IAC/C;QAAE,OAAO;QAAK,OAAO;QAAY,OAAO;IAAM;IAC9C;QAAE,OAAO;QAAK,OAAO;QAAU,OAAO;IAAM;IAC5C;QAAE,OAAO;QAAK,OAAO;QAAY,OAAO;IAAM;IAC9C;QAAE,OAAO;QAAK,OAAO;QAAU,OAAO;IAAM;CAC7C;AAED,MAAM,SAAS;IACb;QAAE,OAAO;QAAK,OAAO;IAAU;IAC/B;QAAE,OAAO;QAAK,OAAO;IAAW;IAChC;QAAE,OAAO;QAAK,OAAO;IAAQ;IAC7B;QAAE,OAAO;QAAK,OAAO;IAAQ;IAC7B;QAAE,OAAO;QAAK,OAAO;IAAM;IAC3B;QAAE,OAAO;QAAK,OAAO;IAAO;IAC5B;QAAE,OAAO;QAAK,OAAO;IAAO;IAC5B;QAAE,OAAO;QAAK,OAAO;IAAS;IAC9B;QAAE,OAAO;QAAK,OAAO;IAAY;IACjC;QAAE,OAAO;QAAM,OAAO;IAAU;IAChC;QAAE,OAAO;QAAM,OAAO;IAAW;IACjC;QAAE,OAAO;QAAM,OAAO;IAAW;CAClC;AAEM,MAAM,4BAAsE,CAAC,EAClF,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,MAAM,EACN,OAAO,EACR;IACC,MAAM,EAAE,MAAM,YAAY,EAAE,EAAE,WAAW,kBAAkB,EAAE,GAAG,CAAA,GAAA,qKAAA,CAAA,oBAAiB,AAAD,EAAE;IAClF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IAC/D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE7D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkC;IACnF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;QAAK;QAAK;QAAK;QAAK;KAAI;IAC5F,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QAAC;KAAI;IACpE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoC;QAAE,MAAM;QAAM,QAAQ;IAAK;IAE9G,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoC;QAAE,MAAM;QAAM,QAAQ;IAAK;IAI5G,MAAM,yBAAyB;QAC7B,IAAI,iBAAiB,WAAW,gBAAgB;YAC9C,OAAO;QACT;QACA,IAAI,iBAAiB,aAAa;YAChC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG;YACzB,OAAQ;gBACN,KAAK;oBACH,OAAO,GAAG,OAAO,CAAC,EAAE,KAAK,MAAM,CAAC;gBAClC,KAAK;oBACH,MAAM,aAAa,iBAAiB,IAAI,CAAC;oBACzC,OAAO,GAAG,OAAO,CAAC,EAAE,KAAK,KAAK,EAAE,YAAY;gBAC9C,KAAK;oBACH,MAAM,WAAW,eAAe,QAAQ,CAAC,OAAO,MAAM,eAAe,IAAI,CAAC;oBAC1E,OAAO,GAAG,OAAO,CAAC,EAAE,KAAK,CAAC,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,CAAC;gBACxD;oBACE,OAAO,GAAG,OAAO,CAAC,EAAE,KAAK,MAAM,CAAC;YACpC;QACF;QACA,IAAI,iBAAiB,cAAc,cAAc;YAC/C,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG;YACzB,MAAM,MAAM,aAAa,OAAO;YAChC,MAAM,QAAQ,aAAa,QAAQ,KAAK;YACxC,MAAM,OAAO,aAAa,WAAW;YACrC,OAAO,GAAG,OAAO,CAAC,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC;QAC9C;QACA,OAAO,OAAO,eAAe,IAAI;IACnC;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,UAAU;QAChB,IAAI,WAAW,YAAY,OAAO,eAAe,EAAE;YACjD,SAAS;gBACP,GAAG,MAAM;gBACT,iBAAiB;YACnB;QACF;IACF,GAAG;QAAC;QAAc;QAAgB;QAAe;QAAkB;QAAgB;QAAY;QAAc;QAAc;KAAY;IAEvI,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB,OAAO,IAAI;QAC7B,SAAS;YACP,GAAG,MAAM;YACT,iBAAiB,OAAO,IAAI;QAC9B;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,SAAS;YACP,GAAG,MAAM;YACT,cAAc;QAChB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,SAAS;YACP,GAAG,MAAM;YACT,UAAU;QACZ;IACF;IAEA,MAAM,4BAA4B,CAAC;QACjC,MAAM,YAAY;YAChB,GAAG,MAAM;YACT,gBAAgB;QAClB;QACA,IAAI,UAAU,SAAS;YACrB,OAAO,UAAU,WAAW;YAC5B,OAAO,UAAU,cAAc;QACjC,OAAO;YACL,OAAO,UAAU,YAAY;YAC7B,IAAI,CAAC,UAAU,cAAc,EAAE;gBAC7B,UAAU,cAAc,GAAG;oBAAE,QAAQ;gBAAG;YAC1C;QACF;QACA,SAAS;IACX;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,WAAW,UAAU,CAAC,OAAO;YAC/B;QACF;QACA,SAAS;YACP,GAAG,MAAM;YACT,aAAa;QACf;IACF;IAIA,MAAM,sBAAsB,CAAC;QAC3B,oBAAoB,CAAA,OAClB,KAAK,QAAQ,CAAC,WACV,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM,WACvB;mBAAI;gBAAM;aAAQ,CAAC,IAAI;IAE/B;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,UAAU,KAAK;YACjB,kBAAkB;gBAAC;aAAI;QACzB,OAAO;YACL,kBAAkB,CAAA;gBAChB,MAAM,WAAW,KAAK,MAAM,CAAC,CAAA,IAAK,MAAM;gBACxC,OAAO,SAAS,QAAQ,CAAC,SACrB,SAAS,MAAM,CAAC,CAAA,IAAK,MAAM,SAC3B;uBAAI;oBAAU;iBAAM,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,SAAS,KAAK,SAAS;YACjE;QACF;IACF;IAEA,MAAM,iBAAiB,cAAc,MAAM,CAAC,CAAC,KAAK;QAChD,IAAI,CAAC,GAAG,CAAC,OAAO,QAAQ,CAAC,EAAE,GAAG,CAAC,OAAO,QAAQ,CAAC,GAAG,EAAE;QACpD,GAAG,CAAC,OAAO,QAAQ,CAAC,CAAC,IAAI,CAAC;QAC1B,OAAO;IACT,GAAG,CAAC;IAEJ,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;;kCACd,8OAAC,gIAAA,CAAA,aAAU;wBAAC,WAAU;kCACpB,cAAA,8OAAC,gIAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;kCAInB,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,OAAO;4BAAc,eAAe,CAAC,QAAU,gBAAgB;4BAAwB,WAAU;;8CACrG,8OAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAQ,WAAU;;8DACnC,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAG7B,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAY,WAAU;;8DACvC,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAGhC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAW,WAAU;;8DACtC,8OAAC,0MAAA,CAAA,WAAY;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAGtC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAW,WAAU;;8DACtC,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;8CAKlC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAQ,WAAU;8CACnC,cAAA,8OAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC,CAAC,UAAU,QAAQ,iBACtD,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;;4DAAuC;4DAAS;;;;;;;kEAC9D,8OAAC;wDAAI,WAAU;kEACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,gIAAA,CAAA,OAAI;gEAEH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA,mBAAmB,OAAO,IAAI,IAAI;gEAEpC,SAAS,IAAM,mBAAmB;0EAElC,cAAA,8OAAC,gIAAA,CAAA,cAAW;oEAAC,WAAU;8EACrB,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FAAgB,OAAO,IAAI;;;;;;0FAC1C,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAI,WAAU;kGAAuB,OAAO,IAAI;;;;;;kGACjD,8OAAC;wFAAI,WAAU;kGAAiC,OAAO,WAAW;;;;;;;;;;;;;;;;;;;;;;;+DAZnE,OAAO,IAAI;;;;;;;;;;;+CALd;;;;;;;;;;;;;;;8CA6BhB,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAY,WAAU;8CACvC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAiC;;;;;;kEAClD,8OAAC,0IAAA,CAAA,aAAU;wDAAC,OAAO;wDAAe,eAAe,CAAC,QAAU,iBAAiB;;0EAC3E,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0IAAA,CAAA,iBAAc;wEAAC,OAAM;wEAAQ,IAAG;;;;;;kFACjC,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAQ;;;;;;;;;;;;0EAEzB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0IAAA,CAAA,iBAAc;wEAAC,OAAM;wEAAS,IAAG;;;;;;kFAClC,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAS;;;;;;;;;;;;0EAE1B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,0IAAA,CAAA,iBAAc;wEAAC,OAAM;wEAAU,IAAG;;;;;;kFACnC,8OAAC,iIAAA,CAAA,QAAK;wEAAC,SAAQ;kFAAU;;;;;;;;;;;;;;;;;;;;;;;;4CAK9B,kBAAkB,0BACjB,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAiC;;;;;;kEAClD,8OAAC;wDAAI,WAAU;kEACZ,SAAS,GAAG,CAAC,CAAC,oBACb,8OAAC,kIAAA,CAAA,SAAM;gEAEL,SAAS,iBAAiB,QAAQ,CAAC,IAAI,KAAK,IAAI,YAAY;gEAC5D,MAAK;gEACL,SAAS,IAAM,oBAAoB,IAAI,KAAK;0EAE3C,IAAI,KAAK;+DALL,IAAI,KAAK;;;;;;;;;;;;;;;;4CAYvB,kBAAkB,2BACjB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,WAAU;0EAAiC;;;;;;0EAClD,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO;gEAAY,eAAe;;kFACxC,8OAAC,kIAAA,CAAA,gBAAa;wEAAC,WAAU;kFACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAEd,8OAAC,kIAAA,CAAA,gBAAa;kFACX,MAAM,IAAI,CAAC;4EAAE,QAAQ;wEAAG,GAAG,CAAC,GAAG,kBAC9B,8OAAC,kIAAA,CAAA,aAAU;gFAAa,OAAO,CAAC,IAAI,CAAC,EAAE,QAAQ;0FAC5C,IAAI;+EADU,IAAI;;;;;;;;;;;;;;;;;;;;;;kEAO7B,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,WAAU;0EAAiC;;;;;;0EAClD,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kIAAA,CAAA,SAAM;wEACL,SAAS,eAAe,QAAQ,CAAC,OAAO,YAAY;wEACpD,MAAK;wEACL,SAAS,IAAM,kBAAkB;kFAClC;;;;;;kFAGD,8OAAC;wEAAI,WAAU;kFACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC,kIAAA,CAAA,SAAM;gFAEL,SAAS,eAAe,QAAQ,CAAC,MAAM,KAAK,IAAI,YAAY;gFAC5D,MAAK;gFACL,SAAS,IAAM,kBAAkB,MAAM,KAAK;gFAC5C,UAAU,eAAe,QAAQ,CAAC;0FAEjC,MAAM,KAAK,CAAC,KAAK,CAAC,GAAG;+EANjB,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAe9B,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAiC;;;;;;kEAClD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO,aAAa,IAAI;gEAAE,eAAe,CAAC,QAAU,gBAAgB,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,MAAM;wEAAM,CAAC;;kFAC3G,8OAAC,kIAAA,CAAA,gBAAa;wEAAC,WAAU;kFACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAEd,8OAAC,kIAAA,CAAA,gBAAa;kFACX,MAAM,IAAI,CAAC;4EAAE,QAAQ;wEAAG,GAAG,CAAC,GAAG,kBAC9B,8OAAC,kIAAA,CAAA,aAAU;gFAAS,OAAO,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;0FACjD,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;+EADX;;;;;;;;;;;;;;;;0EAMvB,8OAAC;0EAAK;;;;;;0EACN,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO,aAAa,MAAM;gEAAE,eAAe,CAAC,QAAU,gBAAgB,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,QAAQ;wEAAM,CAAC;;kFAC/G,8OAAC,kIAAA,CAAA,gBAAa;wEAAC,WAAU;kFACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAEd,8OAAC,kIAAA,CAAA,gBAAa;kFACX,MAAM,IAAI,CAAC;4EAAE,QAAQ;wEAAG,GAAG,CAAC,GAAG,kBAC9B,8OAAC,kIAAA,CAAA,aAAU;gFAAS,OAAO,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;0FACjD,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;+EADX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAW/B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;8CACtC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAiC;;;;;;kEAClD,8OAAC,mIAAA,CAAA,UAAO;;0EACN,8OAAC,mIAAA,CAAA,iBAAc;gEAAC,OAAO;0EACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8CACA,CAAC,gBAAgB;;sFAGnB,8OAAC,0MAAA,CAAA,WAAY;4EAAC,WAAU;;;;;;wEACvB,eAAe,CAAA,GAAA,sJAAA,CAAA,SAAM,AAAD,EAAE,cAAc,SAAS;;;;;;;;;;;;0EAGlD,8OAAC,mIAAA,CAAA,iBAAc;gEAAC,WAAU;gEAAa,OAAM;0EAC3C,cAAA,8OAAC,oIAAA,CAAA,WAAQ;oEACP,MAAK;oEACL,UAAU;oEACV,UAAU;oEACV,UAAU,CAAC,OAAS,OAAO,CAAA,GAAA,0IAAA,CAAA,aAAU,AAAD,EAAE,IAAI;oEAC1C,YAAY;;;;;;;;;;;;;;;;;;;;;;;0DAMpB,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,WAAU;kEAAiC;;;;;;kEAClD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO,YAAY,IAAI;gEAAE,eAAe,CAAC,QAAU,eAAe,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,MAAM;wEAAM,CAAC;;kFACzG,8OAAC,kIAAA,CAAA,gBAAa;wEAAC,WAAU;kFACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAEd,8OAAC,kIAAA,CAAA,gBAAa;kFACX,MAAM,IAAI,CAAC;4EAAE,QAAQ;wEAAG,GAAG,CAAC,GAAG,kBAC9B,8OAAC,kIAAA,CAAA,aAAU;gFAAS,OAAO,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;0FACjD,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;+EADX;;;;;;;;;;;;;;;;0EAMvB,8OAAC;0EAAK;;;;;;0EACN,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO,YAAY,MAAM;gEAAE,eAAe,CAAC,QAAU,eAAe,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,QAAQ;wEAAM,CAAC;;kFAC7G,8OAAC,kIAAA,CAAA,gBAAa;wEAAC,WAAU;kFACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kFAEd,8OAAC,kIAAA,CAAA,gBAAa;kFACX,MAAM,IAAI,CAAC;4EAAE,QAAQ;wEAAG,GAAG,CAAC,GAAG,kBAC9B,8OAAC,kIAAA,CAAA,aAAU;gFAAS,OAAO,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;0FACjD,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG;+EADX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAW/B,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;8CACtC,cAAA,8OAAC;;0DACC,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAkB,WAAU;0DAAsB;;;;;;0DAGjE,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,MAAK;gDACL,OAAO,OAAO,eAAe,IAAI;gDACjC,UAAU,CAAC,IAAM,SAAS;wDAAE,GAAG,MAAM;wDAAE,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACvE,aAAY;gDACZ,WAAW,OAAO,eAAe,GAAG,uBAAuB;;;;;;4CAE5D,OAAO,eAAe,kBACrB,8OAAC;gDAAE,WAAU;0DAAiC,OAAO,eAAe;;;;;;0DAEtE,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;0DACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;;sEACV,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;8EAChB,8OAAC;oEAAK,WAAU;8EAAsB;;;;;;;;;;;;sEAExC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;wEAAI;sFAAQ,8OAAC;4EAAK,WAAU;sFAAgC;;;;;;;;;;;;8EAC7D,8OAAC;;wEAAI;sFAAS,8OAAC;4EAAK,WAAU;sFAAgC;;;;;;wEAAkB;;;;;;;8EAChF,8OAAC;;wEAAI;sFAAI,8OAAC;4EAAK,WAAU;sFAAgC;;;;;;wEAAQ;sFAAgB,8OAAC;4EAAK,WAAU;sFAAgC;;;;;;wEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU3J,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;;8CACC,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAW,WAAU;8CAAsB;;;;;;8CAG1D,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO,OAAO,QAAQ,IAAI;oCAAO,eAAe;;sDACtD,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,8OAAC,kIAAA,CAAA,gBAAa;sDACX,UAAU,GAAG,CAAC,CAAC,mBACd,8OAAC,kIAAA,CAAA,aAAU;oDAAgB,OAAO,GAAG,KAAK;8DACvC,GAAG,KAAK;mDADM,GAAG,KAAK;;;;;;;;;;;;;;;;;;;;;;sCAOjC,8OAAC;;8CACC,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;8CAAiC;;;;;;8CAGlD,8OAAC,0IAAA,CAAA,aAAU;oCAAC,OAAO,OAAO,cAAc,IAAI;oCAAS,eAAe;;sDAClE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0IAAA,CAAA,iBAAc;oDAAC,OAAM;oDAAQ,IAAG;;;;;;8DACjC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAkB;;;;;;;;;;;;sDAEnC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0IAAA,CAAA,iBAAc;oDAAC,OAAM;oDAAW,IAAG;;;;;;8DACpC,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;8DAAqB;;;;;;;;;;;;;;;;;;8CAGxC,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;wBAInD,OAAO,cAAc,KAAK,2BACzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAc,WAAU;sDAAsB;;;;;;sDAG7D,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO,OAAO,WAAW,IAAI;4CAAI,eAAe;;8DACtD,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAW,OAAO,WAAW,GAAG,uBAAuB;8DACpE,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;8DACX,mCACC,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;wDAAc,QAAQ;kEAAC;;;;;+DACvC,UAAU,MAAM,KAAK,kBACvB,8OAAC,kIAAA,CAAA,aAAU;wDAAC,OAAM;wDAAmB,QAAQ;kEAAC;;;;;+DAE9C,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,GAAG,CAAC,CAAC,yBAChD,8OAAC,kIAAA,CAAA,aAAU;4DAAmB,OAAO,SAAS,EAAE;sEAC7C,SAAS,IAAI;2DADC,SAAS,EAAE;;;;;;;;;;;;;;;;wCAOnC,OAAO,WAAW,kBACjB,8OAAC;4CAAE,WAAU;sDAAiC,OAAO,WAAW;;;;;;sDAElE,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;8CAKpD,8OAAC;;sDACC,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAiB,WAAU;sDAAsB;;;;;;sDAGhE,8OAAC,oIAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO,OAAO,cAAc,EAAE,UAAU,OAAO,cAAc,EAAE,WAAW;4CAC1E,UAAU,CAAC;gDACT,SAAS;oDACP,GAAG,MAAM;oDACT,gBAAgB;wDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oDAAC;gDAC3C;4CACF;4CACA,aAAY;4CACZ,MAAM;4CACN,WAAW,OAAO,cAAc,GAAG,uBAAuB;;;;;;wCAE3D,OAAO,cAAc,kBACpB,8OAAC;4CAAE,WAAU;sDAAiC,OAAO,cAAc;;;;;;sDAErE,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;;;;;;;;;;;;iDAMtD,8OAAC;;8CACC,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAe,WAAU;8CAAsB;;;;;;8CAG9D,8OAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,OAAO,OAAO,YAAY,IAAI;oCAC9B,UAAU,CAAC,IAAM,wBAAwB,EAAE,MAAM,CAAC,KAAK;oCACvD,aAAY;oCACZ,MAAM;oCACN,WAAW,OAAO,YAAY,GAAG,uBAAuB;;;;;;gCAEzD,OAAO,YAAY,kBAClB,8OAAC;oCAAE,WAAU;8CAAiC,OAAO,YAAY;;;;;;8CAEnE,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShE", "debugId": null}}, {"offset": {"line": 2416, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/triggers/trigger-config-dialog.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState, useEffect } from 'react';\r\nimport {\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Switch } from '@/components/ui/switch';\r\nimport { \r\n  Activity,\r\n  Copy,\r\n  ExternalLink,\r\n  Loader2\r\n} from 'lucide-react';\r\nimport { TriggerProvider, TriggerConfiguration, ScheduleTriggerConfig } from './types';\r\nimport { ScheduleTriggerConfigForm } from './providers/schedule-config';\r\nimport { getDialogIcon } from './utils';\r\n\r\n\r\ninterface TriggerConfigDialogProps {\r\n  provider: TriggerProvider;\r\n  existingConfig?: TriggerConfiguration;\r\n  onSave: (config: any) => void;\r\n  onCancel: () => void;\r\n  isLoading?: boolean;\r\n  agentId: string;\r\n}\r\n\r\nexport const TriggerConfigDialog: React.FC<TriggerConfigDialogProps> = ({\r\n  provider,\r\n  existingConfig,\r\n  onSave,\r\n  onCancel,\r\n  isLoading = false,\r\n  agentId,\r\n}) => {\r\n  const [name, setName] = useState(existingConfig?.name || '');\r\n  const [description, setDescription] = useState(existingConfig?.description || '');\r\n  const [isActive, setIsActive] = useState(existingConfig?.is_active ?? true);\r\n  const [config, setConfig] = useState(existingConfig?.config || {});\r\n  const [errors, setErrors] = useState<Record<string, string>>({});\r\n\r\n  useEffect(() => {\r\n    if (!name && !existingConfig) {\r\n      setName(`${provider.name} Trigger`);\r\n    }\r\n  }, [provider, existingConfig, name]);\r\n\r\n  const validateForm = () => {\r\n    const newErrors: Record<string, string> = {};\r\n    if (!name.trim()) {\r\n      newErrors.name = 'Name is required';\r\n    }\r\n    if (provider.provider_id === 'telegram') {\r\n      if (!config.bot_token) {\r\n        newErrors.bot_token = 'Bot token is required';\r\n      }\r\n    } else if (provider.provider_id === 'slack') {\r\n      if (!config.signing_secret) {\r\n        newErrors.signing_secret = 'Signing secret is required';\r\n      }\r\n    } else if (provider.provider_id === 'schedule') {\r\n      if (!config.cron_expression) {\r\n        newErrors.cron_expression = 'Cron expression is required';\r\n      }\r\n      if (config.execution_type === 'workflow') {\r\n        if (!config.workflow_id) {\r\n          newErrors.workflow_id = 'Workflow selection is required';\r\n        }\r\n      } else {\r\n        if (!config.agent_prompt) {\r\n          newErrors.agent_prompt = 'Agent prompt is required';\r\n        }\r\n      }\r\n    }\r\n    setErrors(newErrors);\r\n    return Object.keys(newErrors).length === 0;\r\n  };\r\n\r\n  const handleSave = () => {\r\n    if (validateForm()) {\r\n      onSave({\r\n        name: name.trim(),\r\n        description: description.trim(),\r\n        is_active: isActive,\r\n        config,\r\n      });\r\n    }\r\n  };\r\n\r\n  const renderProviderSpecificConfig = () => {\r\n    switch (provider.provider_id) {\r\n      case 'schedule':\r\n        return (\r\n          <ScheduleTriggerConfigForm\r\n            provider={provider}\r\n            config={config as ScheduleTriggerConfig}\r\n            onChange={setConfig}\r\n            errors={errors}\r\n            agentId={agentId}\r\n          />\r\n        );\r\n      default:\r\n        return (\r\n          <div className=\"text-center py-8 text-muted-foreground\">\r\n            <Activity className=\"h-12 w-12 mx-auto mb-4\" />\r\n            <p>Configuration form for {provider.name} is not yet implemented.</p>\r\n          </div>\r\n        );\r\n    }\r\n  };\r\n\r\n  return (\r\n    <DialogContent className=\"max-w-2xl max-h-[80vh] overflow-hidden flex flex-col\">\r\n      <DialogHeader>\r\n        <DialogTitle className=\"flex items-center space-x-3\">\r\n          <div className=\"p-2 rounded-lg bg-muted border\">\r\n            {getDialogIcon(provider.trigger_type)}\r\n          </div>\r\n          <div>\r\n            <span>Configure {provider.name}</span>\r\n          </div>\r\n        </DialogTitle>\r\n        <DialogDescription>\r\n          {provider.description}\r\n        </DialogDescription>\r\n      </DialogHeader>\r\n      <div className=\"flex-1 overflow-y-auto space-y-6 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100\">\r\n        <div className=\"space-y-4\">\r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor=\"trigger-name\">Name *</Label>\r\n            <Input\r\n              id=\"trigger-name\"\r\n              value={name}\r\n              onChange={(e) => setName(e.target.value)}\r\n              placeholder=\"Enter a name for this trigger\"\r\n              className={errors.name ? 'border-destructive' : ''}\r\n            />\r\n            {errors.name && (\r\n              <p className=\"text-sm text-destructive\">{errors.name}</p>\r\n            )}\r\n          </div>\r\n          \r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor=\"trigger-description\">Description</Label>\r\n            <Textarea\r\n              id=\"trigger-description\"\r\n              value={description}\r\n              onChange={(e) => setDescription(e.target.value)}\r\n              placeholder=\"Optional description for this trigger\"\r\n              rows={2}\r\n            />\r\n          </div>\r\n          \r\n          <div className=\"flex items-center space-x-2\">\r\n            <Switch\r\n              id=\"trigger-active\"\r\n              checked={isActive}\r\n              onCheckedChange={setIsActive}\r\n            />\r\n            <Label htmlFor=\"trigger-active\">\r\n              Enable trigger immediately\r\n            </Label>\r\n          </div>\r\n        </div>\r\n        <div className=\"border-t pt-6\">\r\n          <h3 className=\"text-sm font-medium mb-4\">\r\n            {provider.name} Configuration\r\n          </h3>\r\n          {renderProviderSpecificConfig()}\r\n        </div>\r\n        {provider.webhook_enabled && existingConfig?.webhook_url && (\r\n          <div className=\"border-t pt-6\">\r\n            <h3 className=\"text-sm font-medium mb-4\">Webhook Information</h3>\r\n            <div className=\"space-y-2\">\r\n              <Label>Webhook URL</Label>\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Input\r\n                  value={existingConfig.webhook_url}\r\n                  readOnly\r\n                  className=\"font-mono text-sm\"\r\n                />\r\n                <Button\r\n                  size=\"sm\"\r\n                  variant=\"outline\"\r\n                  onClick={() => navigator.clipboard.writeText(existingConfig.webhook_url!)}\r\n                >\r\n                  <Copy className=\"h-4 w-4\" />\r\n                </Button>\r\n                <Button\r\n                  size=\"sm\"\r\n                  variant=\"outline\"\r\n                  onClick={() => window.open(existingConfig.webhook_url, '_blank')}\r\n                >\r\n                  <ExternalLink className=\"h-4 w-4\" />\r\n                </Button>\r\n              </div>\r\n              <p className=\"text-xs text-muted-foreground\">\r\n                Use this URL to configure the webhook in {provider.name}\r\n              </p>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n      <DialogFooter>\r\n        <Button variant=\"outline\" onClick={onCancel} disabled={isLoading}>\r\n          Cancel\r\n        </Button>\r\n        <Button onClick={handleSave} disabled={isLoading}>\r\n          {isLoading ? (\r\n            <>\r\n              <Loader2 className=\"animate-spin rounded-full h-4 w-4\" />\r\n              {existingConfig ? 'Updating...' : 'Creating...'}\r\n            </>\r\n          ) : (\r\n            `${existingConfig ? 'Update' : 'Create'} Trigger`\r\n          )}\r\n        </Button>\r\n      </DialogFooter>\r\n    </DialogContent>\r\n  );\r\n}; "], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAOA;AACA;AAvBA;;;;;;;;;;;;AAmCO,MAAM,sBAA0D,CAAC,EACtE,QAAQ,EACR,cAAc,EACd,MAAM,EACN,QAAQ,EACR,YAAY,KAAK,EACjB,OAAO,EACR;IACC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,gBAAgB,QAAQ;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,gBAAgB,eAAe;IAC9E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,gBAAgB,aAAa;IACtE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,gBAAgB,UAAU,CAAC;IAChE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,QAAQ,CAAC,gBAAgB;YAC5B,QAAQ,GAAG,SAAS,IAAI,CAAC,QAAQ,CAAC;QACpC;IACF,GAAG;QAAC;QAAU;QAAgB;KAAK;IAEnC,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAC3C,IAAI,CAAC,KAAK,IAAI,IAAI;YAChB,UAAU,IAAI,GAAG;QACnB;QACA,IAAI,SAAS,WAAW,KAAK,YAAY;YACvC,IAAI,CAAC,OAAO,SAAS,EAAE;gBACrB,UAAU,SAAS,GAAG;YACxB;QACF,OAAO,IAAI,SAAS,WAAW,KAAK,SAAS;YAC3C,IAAI,CAAC,OAAO,cAAc,EAAE;gBAC1B,UAAU,cAAc,GAAG;YAC7B;QACF,OAAO,IAAI,SAAS,WAAW,KAAK,YAAY;YAC9C,IAAI,CAAC,OAAO,eAAe,EAAE;gBAC3B,UAAU,eAAe,GAAG;YAC9B;YACA,IAAI,OAAO,cAAc,KAAK,YAAY;gBACxC,IAAI,CAAC,OAAO,WAAW,EAAE;oBACvB,UAAU,WAAW,GAAG;gBAC1B;YACF,OAAO;gBACL,IAAI,CAAC,OAAO,YAAY,EAAE;oBACxB,UAAU,YAAY,GAAG;gBAC3B;YACF;QACF;QACA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,aAAa;QACjB,IAAI,gBAAgB;YAClB,OAAO;gBACL,MAAM,KAAK,IAAI;gBACf,aAAa,YAAY,IAAI;gBAC7B,WAAW;gBACX;YACF;QACF;IACF;IAEA,MAAM,+BAA+B;QACnC,OAAQ,SAAS,WAAW;YAC1B,KAAK;gBACH,qBACE,8OAAC,2KAAA,CAAA,4BAAyB;oBACxB,UAAU;oBACV,QAAQ;oBACR,UAAU;oBACV,QAAQ;oBACR,SAAS;;;;;;YAGf;gBACE,qBACE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC;;gCAAE;gCAAwB,SAAS,IAAI;gCAAC;;;;;;;;;;;;;QAGjD;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,gBAAa;QAAC,WAAU;;0BACvB,8OAAC,kIAAA,CAAA,eAAY;;kCACX,8OAAC,kIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;0CACZ,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,YAAY;;;;;;0CAEtC,8OAAC;0CACC,cAAA,8OAAC;;wCAAK;wCAAW,SAAS,IAAI;;;;;;;;;;;;;;;;;;kCAGlC,8OAAC,kIAAA,CAAA,oBAAiB;kCACf,SAAS,WAAW;;;;;;;;;;;;0BAGzB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAe;;;;;;kDAC9B,8OAAC,iIAAA,CAAA,QAAK;wCACJ,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;wCACvC,aAAY;wCACZ,WAAW,OAAO,IAAI,GAAG,uBAAuB;;;;;;oCAEjD,OAAO,IAAI,kBACV,8OAAC;wCAAE,WAAU;kDAA4B,OAAO,IAAI;;;;;;;;;;;;0CAIxD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAsB;;;;;;kDACrC,8OAAC,oIAAA,CAAA,WAAQ;wCACP,IAAG;wCACH,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,aAAY;wCACZ,MAAM;;;;;;;;;;;;0CAIV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,IAAG;wCACH,SAAS;wCACT,iBAAiB;;;;;;kDAEnB,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;kDAAiB;;;;;;;;;;;;;;;;;;kCAKpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCACX,SAAS,IAAI;oCAAC;;;;;;;4BAEhB;;;;;;;oBAEF,SAAS,eAAe,IAAI,gBAAgB,6BAC3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2B;;;;;;0CACzC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;kDAAC;;;;;;kDACP,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDACJ,OAAO,eAAe,WAAW;gDACjC,QAAQ;gDACR,WAAU;;;;;;0DAEZ,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,UAAU,SAAS,CAAC,SAAS,CAAC,eAAe,WAAW;0DAEvE,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,OAAO,IAAI,CAAC,eAAe,WAAW,EAAE;0DAEvD,cAAA,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAG5B,8OAAC;wCAAE,WAAU;;4CAAgC;4CACD,SAAS,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;0BAMjE,8OAAC,kIAAA,CAAA,eAAY;;kCACX,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;wBAAU,UAAU;kCAAW;;;;;;kCAGlE,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAY,UAAU;kCACpC,0BACC;;8CACE,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAClB,iBAAiB,gBAAgB;;2CAGpC,GAAG,iBAAiB,WAAW,SAAS,QAAQ,CAAC;;;;;;;;;;;;;;;;;;AAM7D", "debugId": null}}, {"offset": {"line": 2869, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/triggers/one-click-integrations.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useEffect, useState } from 'react';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Loader2, AlertCircle, Clock } from 'lucide-react';\r\nimport { TriggerConfigDialog } from './trigger-config-dialog';\r\nimport { TriggerProvider } from './types';\r\nimport { Dialog } from '@/components/ui/dialog';\r\nimport { \r\n  useInstallOAuthIntegration, \r\n  useUninstallOAuthIntegration,\r\n  useOAuthCallbackHandler \r\n} from '@/hooks/react-query/triggers/use-oauth-integrations';\r\nimport { \r\n  useAgentTriggers, \r\n  useCreateTrigger, \r\n  useDeleteTrigger \r\n} from '@/hooks/react-query/triggers';\r\nimport { toast } from 'sonner';\r\n\r\ninterface OneClickIntegrationsProps {\r\n  agentId: string;\r\n}\r\n\r\nconst OAUTH_PROVIDERS = {\r\n  schedule: {\r\n    name: 'Schedule',\r\n    icon: <Clock className=\"h-4 w-4\" color=\"#10b981\" />,\r\n    isOAuth: false\r\n  }\r\n} as const;\r\n\r\ntype ProviderKey = keyof typeof OAUTH_PROVIDERS;\r\n\r\nexport const OneClickIntegrations: React.FC<OneClickIntegrationsProps> = ({\r\n  agentId\r\n}) => {\r\n  const [configuringSchedule, setConfiguringSchedule] = useState(false);\r\n  const { data: triggers = [] } = useAgentTriggers(agentId);\r\n  const installMutation = useInstallOAuthIntegration();\r\n  const uninstallMutation = useUninstallOAuthIntegration();\r\n  const createTriggerMutation = useCreateTrigger();\r\n  const deleteTriggerMutation = useDeleteTrigger();\r\n  const { handleCallback } = useOAuthCallbackHandler();\r\n\r\n  useEffect(() => {\r\n    handleCallback();\r\n  }, []);\r\n\r\n  const handleInstall = async (provider: ProviderKey) => {\r\n    if (provider === 'schedule') {\r\n      setConfiguringSchedule(true);\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      await installMutation.mutateAsync({\r\n        agent_id: agentId,\r\n        provider: provider\r\n      });\r\n    } catch (error) {\r\n      console.error(`Error installing ${provider}:`, error);\r\n    }\r\n  };\r\n\r\n  const handleUninstall = async (provider: ProviderKey, triggerId?: string) => {\r\n    if (provider === 'schedule' && triggerId) {\r\n      try {\r\n        await deleteTriggerMutation.mutateAsync(triggerId);\r\n        toast.success('Schedule trigger removed successfully');\r\n      } catch (error) {\r\n        toast.error('Failed to remove schedule trigger');\r\n        console.error('Error removing schedule trigger:', error);\r\n      }\r\n      return;\r\n    }\r\n    \r\n    try {\r\n      await uninstallMutation.mutateAsync(triggerId!);\r\n    } catch (error) {\r\n      console.error('Error uninstalling integration:', error);\r\n    }\r\n  };\r\n\r\n  const handleScheduleSave = async (config: any) => {\r\n    try {\r\n      await createTriggerMutation.mutateAsync({\r\n        agentId,\r\n        provider_id: 'schedule',\r\n        name: config.name || 'Scheduled Trigger',\r\n        description: config.description || 'Automatically scheduled trigger',\r\n        config: config.config,\r\n      });\r\n      toast.success('Schedule trigger created successfully');\r\n      setConfiguringSchedule(false);\r\n    } catch (error: any) {\r\n      toast.error(error.message || 'Failed to create schedule trigger');\r\n      console.error('Error creating schedule trigger:', error);\r\n    }\r\n  };\r\n\r\n  const getIntegrationForProvider = (provider: ProviderKey) => {\r\n    if (provider === 'schedule') {\r\n      return triggers.find(trigger => trigger.trigger_type === 'schedule');\r\n    }\r\n  };\r\n\r\n  const isProviderInstalled = (provider: ProviderKey) => {\r\n    return !!getIntegrationForProvider(provider);\r\n  };\r\n\r\n  const getTriggerId = (provider: ProviderKey) => {\r\n    const integration = getIntegrationForProvider(provider);\r\n    if (provider === 'schedule') {\r\n      return integration?.trigger_id;\r\n    }\r\n    return integration?.trigger_id;\r\n  };\r\n\r\n  const scheduleProvider: TriggerProvider = {\r\n    provider_id: 'schedule',\r\n    name: 'Schedule',\r\n    description: 'Schedule agent execution using cron expressions',\r\n    trigger_type: 'schedule',\r\n    webhook_enabled: true,\r\n    config_schema: {}\r\n  };\r\n\r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex flex-wrap gap-3\">\r\n        {Object.entries(OAUTH_PROVIDERS).map(([providerId, config]) => {\r\n          const provider = providerId as ProviderKey;\r\n          const isInstalled = isProviderInstalled(provider);\r\n          const isLoading = installMutation.isPending || uninstallMutation.isPending || \r\n                           (provider === 'schedule' && (createTriggerMutation.isPending || deleteTriggerMutation.isPending));\r\n          const triggerId = getTriggerId(provider);\r\n          \r\n          const buttonText = provider === 'schedule' \r\n            ? config.name \r\n            : (isInstalled ? `Disconnect ${config.name}` : `Connect ${config.name}`);\r\n          \r\n          return (\r\n            <Button\r\n              key={providerId}\r\n              variant=\"outline\"\r\n              size='sm'\r\n              onClick={() => {\r\n                if (provider === 'schedule') {\r\n                  handleInstall(provider); \r\n                } else {\r\n                  // eslint-disable-next-line @typescript-eslint/no-unused-expressions\r\n                  isInstalled ? handleUninstall(provider, triggerId) : handleInstall(provider);\r\n                }\r\n              }}\r\n              disabled={isLoading}\r\n              className=\"flex items-center\"\r\n            >\r\n              {isLoading ? (\r\n                <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\r\n              ) : (\r\n                config.icon\r\n              )}\r\n              {buttonText}\r\n            </Button>\r\n          );\r\n        })}\r\n      </div>\r\n      {configuringSchedule && (\r\n        <Dialog open={configuringSchedule} onOpenChange={setConfiguringSchedule}>\r\n          <TriggerConfigDialog\r\n            provider={scheduleProvider}\r\n            existingConfig={null}\r\n            onSave={handleScheduleSave}\r\n            onCancel={() => setConfiguringSchedule(false)}\r\n            isLoading={createTriggerMutation.isPending}\r\n            agentId={agentId}\r\n          />\r\n        </Dialog>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAEA;AACA;AAKA;AAAA;AAKA;AAlBA;;;;;;;;;;AAwBA,MAAM,kBAAkB;IACtB,UAAU;QACR,MAAM;QACN,oBAAM,8OAAC,oMAAA,CAAA,QAAK;YAAC,WAAU;YAAU,OAAM;;;;;;QACvC,SAAS;IACX;AACF;AAIO,MAAM,uBAA4D,CAAC,EACxE,OAAO,EACR;IACC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,EAAE,MAAM,WAAW,EAAE,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,mBAAgB,AAAD,EAAE;IACjD,MAAM,kBAAkB,CAAA,GAAA,0KAAA,CAAA,6BAA0B,AAAD;IACjD,MAAM,oBAAoB,CAAA,GAAA,0KAAA,CAAA,+BAA4B,AAAD;IACrD,MAAM,wBAAwB,CAAA,GAAA,sKAAA,CAAA,mBAAgB,AAAD;IAC7C,MAAM,wBAAwB,CAAA,GAAA,sKAAA,CAAA,mBAAgB,AAAD;IAC7C,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,0KAAA,CAAA,0BAAuB,AAAD;IAEjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB,OAAO;QAC3B,IAAI,aAAa,YAAY;YAC3B,uBAAuB;YACvB;QACF;QAEA,IAAI;YACF,MAAM,gBAAgB,WAAW,CAAC;gBAChC,UAAU;gBACV,UAAU;YACZ;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC,EAAE;QACjD;IACF;IAEA,MAAM,kBAAkB,OAAO,UAAuB;QACpD,IAAI,aAAa,cAAc,WAAW;YACxC,IAAI;gBACF,MAAM,sBAAsB,WAAW,CAAC;gBACxC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,EAAE,OAAO,OAAO;gBACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,QAAQ,KAAK,CAAC,oCAAoC;YACpD;YACA;QACF;QAEA,IAAI;YACF,MAAM,kBAAkB,WAAW,CAAC;QACtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,sBAAsB,WAAW,CAAC;gBACtC;gBACA,aAAa;gBACb,MAAM,OAAO,IAAI,IAAI;gBACrB,aAAa,OAAO,WAAW,IAAI;gBACnC,QAAQ,OAAO,MAAM;YACvB;YACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,uBAAuB;QACzB,EAAE,OAAO,OAAY;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC7B,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,MAAM,4BAA4B,CAAC;QACjC,IAAI,aAAa,YAAY;YAC3B,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,YAAY,KAAK;QAC3D;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAO,CAAC,CAAC,0BAA0B;IACrC;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,cAAc,0BAA0B;QAC9C,IAAI,aAAa,YAAY;YAC3B,OAAO,aAAa;QACtB;QACA,OAAO,aAAa;IACtB;IAEA,MAAM,mBAAoC;QACxC,aAAa;QACb,MAAM;QACN,aAAa;QACb,cAAc;QACd,iBAAiB;QACjB,eAAe,CAAC;IAClB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACZ,OAAO,OAAO,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC,YAAY,OAAO;oBACxD,MAAM,WAAW;oBACjB,MAAM,cAAc,oBAAoB;oBACxC,MAAM,YAAY,gBAAgB,SAAS,IAAI,kBAAkB,SAAS,IACxD,aAAa,cAAc,CAAC,sBAAsB,SAAS,IAAI,sBAAsB,SAAS;oBAChH,MAAM,YAAY,aAAa;oBAE/B,MAAM,aAAa,aAAa,aAC5B,OAAO,IAAI,GACV,cAAc,CAAC,WAAW,EAAE,OAAO,IAAI,EAAE,GAAG,CAAC,QAAQ,EAAE,OAAO,IAAI,EAAE;oBAEzE,qBACE,8OAAC,kIAAA,CAAA,SAAM;wBAEL,SAAQ;wBACR,MAAK;wBACL,SAAS;4BACP,IAAI,aAAa,YAAY;gCAC3B,cAAc;4BAChB,OAAO;gCACL,oEAAoE;gCACpE,cAAc,gBAAgB,UAAU,aAAa,cAAc;4BACrE;wBACF;wBACA,UAAU;wBACV,WAAU;;4BAET,0BACC,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;uCAEnB,OAAO,IAAI;4BAEZ;;uBAnBI;;;;;gBAsBX;;;;;;YAED,qCACC,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAqB,cAAc;0BAC/C,cAAA,8OAAC,uKAAA,CAAA,sBAAmB;oBAClB,UAAU;oBACV,gBAAgB;oBAChB,QAAQ;oBACR,UAAU,IAAM,uBAAuB;oBACvC,WAAW,sBAAsB,SAAS;oBAC1C,SAAS;;;;;;;;;;;;;;;;;AAMrB", "debugId": null}}, {"offset": {"line": 3066, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/triggers/agent-triggers-configuration.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from 'react';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Zap, MessageSquare, Webhook, Plus, Settings } from 'lucide-react';\r\nimport { Dialog } from '@/components/ui/dialog';\r\nimport { ConfiguredTriggersList } from './configured-triggers-list';\r\nimport { TriggerConfigDialog } from './trigger-config-dialog';\r\nimport { TriggerConfiguration, TriggerProvider } from './types';\r\nimport { \r\n  useAgentTriggers, \r\n  useCreateTrigger, \r\n  useUpdateTrigger, \r\n  useDeleteTrigger, \r\n  useToggleTrigger,\r\n  useTriggerProviders \r\n} from '@/hooks/react-query/triggers';\r\nimport { toast } from 'sonner';\r\nimport { OneClickIntegrations } from './one-click-integrations';\r\n\r\ninterface AgentTriggersConfigurationProps {\r\n  agentId: string;\r\n}\r\n\r\n\r\nexport const AgentTriggersConfiguration: React.FC<AgentTriggersConfigurationProps> = ({\r\n  agentId,\r\n}) => {\r\n  const [configuringProvider, setConfiguringProvider] = useState<TriggerProvider | null>(null);\r\n  const [editingTrigger, setEditingTrigger] = useState<TriggerConfiguration | null>(null);\r\n\r\n  const { data: triggers = [], isLoading, error } = useAgentTriggers(agentId);\r\n  const createTriggerMutation = useCreateTrigger();\r\n  const updateTriggerMutation = useUpdateTrigger();\r\n  const deleteTriggerMutation = useDeleteTrigger();\r\n  const toggleTriggerMutation = useToggleTrigger();\r\n\r\n  const handleEditTrigger = (trigger: TriggerConfiguration) => {\r\n    setEditingTrigger(trigger);\r\n    setConfiguringProvider({\r\n      provider_id: trigger.provider_id,\r\n      name: trigger.trigger_type,\r\n      description: '',\r\n      trigger_type: trigger.trigger_type,\r\n      webhook_enabled: !!trigger.webhook_url,\r\n      config_schema: {}\r\n    });\r\n  };\r\n\r\n  const handleRemoveTrigger = async (trigger: TriggerConfiguration) => {\r\n    try {\r\n      await deleteTriggerMutation.mutateAsync(trigger.trigger_id);\r\n      toast.success('Trigger deleted successfully');\r\n    } catch (error) {\r\n      toast.error('Failed to delete trigger');\r\n      console.error('Error deleting trigger:', error);\r\n    }\r\n  };\r\n\r\n  const handleSaveTrigger = async (config: any) => {\r\n    try {\r\n      if (editingTrigger) {\r\n        await updateTriggerMutation.mutateAsync({\r\n          triggerId: editingTrigger.trigger_id,\r\n          name: config.name,\r\n          description: config.description,\r\n          config: config.config,\r\n          is_active: config.is_active,\r\n        });\r\n        toast.success('Trigger updated successfully');\r\n      } else {\r\n        await createTriggerMutation.mutateAsync({\r\n          agentId,\r\n          provider_id: configuringProvider!.provider_id,\r\n          name: config.name,\r\n          description: config.description,\r\n          config: config.config,\r\n        });\r\n        toast.success('Trigger created successfully');\r\n      }\r\n    } catch (error: any) {\r\n      toast.error(error.message || 'Failed to save trigger');\r\n      console.error('Error saving trigger:', error);\r\n    }\r\n    setConfiguringProvider(null);\r\n    setEditingTrigger(null);\r\n  };\r\n\r\n  const handleToggleTrigger = async (trigger: TriggerConfiguration) => {\r\n    try {\r\n      await toggleTriggerMutation.mutateAsync({\r\n        triggerId: trigger.trigger_id,\r\n        isActive: !trigger.is_active,\r\n      });\r\n      toast.success(`Trigger ${!trigger.is_active ? 'enabled' : 'disabled'}`);\r\n    } catch (error) {\r\n      toast.error('Failed to toggle trigger');\r\n      console.error('Error toggling trigger:', error);\r\n    }\r\n  };\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"rounded-xl p-6 border border-destructive/20 bg-destructive/5\">\r\n        <div className=\"flex items-center space-x-3\">\r\n          <div className=\"p-2 bg-destructive/10 rounded-lg\">\r\n            <Zap className=\"h-5 w-5 text-destructive\" />\r\n          </div>\r\n          <div>\r\n            <h3 className=\"text-lg font-semibold text-destructive\">Error Loading Triggers</h3>\r\n            <p className=\"text-sm text-muted-foreground\">\r\n              {error instanceof Error ? error.message : 'Failed to load triggers'}\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"h-full flex flex-col\">\r\n      <div className=\"flex-1 overflow-y-auto space-y-4\">\r\n        <OneClickIntegrations agentId={agentId} />\r\n        \r\n        {triggers.length > 0 && (\r\n          <div className=\"bg-card rounded-xl border border-border overflow-hidden\">\r\n            <div className=\"px-6 py-4 border-b border-border bg-muted/30\">\r\n              <h4 className=\"text-sm font-medium text-foreground flex items-center gap-2\">\r\n                <Settings className=\"h-4 w-4\" />\r\n                Configured Triggers\r\n              </h4>\r\n            </div>\r\n            <div className=\"p-2 divide-y divide-border\">\r\n              <ConfiguredTriggersList\r\n                triggers={triggers}\r\n                onEdit={handleEditTrigger}\r\n                onRemove={handleRemoveTrigger}\r\n                onToggle={handleToggleTrigger}\r\n                isLoading={deleteTriggerMutation.isPending || toggleTriggerMutation.isPending}\r\n              />\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {!isLoading && triggers.length === 0 && (\r\n          <div className=\"text-center py-12 px-6 bg-muted/30 rounded-xl border-2 border-dashed border-border\">\r\n            <div className=\"mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-4 border\">\r\n              <Zap className=\"h-6 w-6 text-muted-foreground\" />\r\n            </div>\r\n            <h4 className=\"text-sm font-semibold text-foreground\">\r\n              No triggers configured\r\n            </h4>\r\n            <p className=\"text-sm text-muted-foreground mb-6 max-w-sm mx-auto\">\r\n              Click on a trigger provider above to get started\r\n            </p>\r\n          </div>\r\n        )}\r\n      </div>\r\n      \r\n      {configuringProvider && (\r\n        <Dialog open={!!configuringProvider} onOpenChange={() => setConfiguringProvider(null)}>\r\n          <TriggerConfigDialog\r\n            provider={configuringProvider}\r\n            existingConfig={editingTrigger}\r\n            onSave={handleSaveTrigger}\r\n            onCancel={() => setConfiguringProvider(null)}\r\n            isLoading={createTriggerMutation.isPending || updateTriggerMutation.isPending}\r\n            agentId={agentId}\r\n          />\r\n        </Dialog>\r\n      )}\r\n    </div>\r\n  );\r\n}; "], "names": [], "mappings": ";;;;AAEA;AAEA;AAAA;AACA;AACA;AACA;AAEA;AAAA;AAQA;AACA;AAlBA;;;;;;;;;;AAyBO,MAAM,6BAAwE,CAAC,EACpF,OAAO,EACR;IACC,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACvF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IAElF,MAAM,EAAE,MAAM,WAAW,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,sKAAA,CAAA,mBAAgB,AAAD,EAAE;IACnE,MAAM,wBAAwB,CAAA,GAAA,sKAAA,CAAA,mBAAgB,AAAD;IAC7C,MAAM,wBAAwB,CAAA,GAAA,sKAAA,CAAA,mBAAgB,AAAD;IAC7C,MAAM,wBAAwB,CAAA,GAAA,sKAAA,CAAA,mBAAgB,AAAD;IAC7C,MAAM,wBAAwB,CAAA,GAAA,sKAAA,CAAA,mBAAgB,AAAD;IAE7C,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,uBAAuB;YACrB,aAAa,QAAQ,WAAW;YAChC,MAAM,QAAQ,YAAY;YAC1B,aAAa;YACb,cAAc,QAAQ,YAAY;YAClC,iBAAiB,CAAC,CAAC,QAAQ,WAAW;YACtC,eAAe,CAAC;QAClB;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,sBAAsB,WAAW,CAAC,QAAQ,UAAU;YAC1D,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,IAAI,gBAAgB;gBAClB,MAAM,sBAAsB,WAAW,CAAC;oBACtC,WAAW,eAAe,UAAU;oBACpC,MAAM,OAAO,IAAI;oBACjB,aAAa,OAAO,WAAW;oBAC/B,QAAQ,OAAO,MAAM;oBACrB,WAAW,OAAO,SAAS;gBAC7B;gBACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB,OAAO;gBACL,MAAM,sBAAsB,WAAW,CAAC;oBACtC;oBACA,aAAa,oBAAqB,WAAW;oBAC7C,MAAM,OAAO,IAAI;oBACjB,aAAa,OAAO,WAAW;oBAC/B,QAAQ,OAAO,MAAM;gBACvB;gBACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;QACF,EAAE,OAAO,OAAY;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,OAAO,IAAI;YAC7B,QAAQ,KAAK,CAAC,yBAAyB;QACzC;QACA,uBAAuB;QACvB,kBAAkB;IACpB;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI;YACF,MAAM,sBAAsB,WAAW,CAAC;gBACtC,WAAW,QAAQ,UAAU;gBAC7B,UAAU,CAAC,QAAQ,SAAS;YAC9B;YACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,CAAC,QAAQ,SAAS,GAAG,YAAY,YAAY;QACxE,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;kCAEjB,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,QAAQ,MAAM,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;;;IAMtD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,wKAAA,CAAA,uBAAoB;wBAAC,SAAS;;;;;;oBAE9B,SAAS,MAAM,GAAG,mBACjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;0CAIpC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0KAAA,CAAA,yBAAsB;oCACrB,UAAU;oCACV,QAAQ;oCACR,UAAU;oCACV,UAAU;oCACV,WAAW,sBAAsB,SAAS,IAAI,sBAAsB,SAAS;;;;;;;;;;;;;;;;;oBAMpF,CAAC,aAAa,SAAS,MAAM,KAAK,mBACjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAEjB,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,8OAAC;gCAAE,WAAU;0CAAsD;;;;;;;;;;;;;;;;;;YAOxE,qCACC,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM,CAAC,CAAC;gBAAqB,cAAc,IAAM,uBAAuB;0BAC9E,cAAA,8OAAC,uKAAA,CAAA,sBAAmB;oBAClB,UAAU;oBACV,gBAAgB;oBAChB,QAAQ;oBACR,UAAU,IAAM,uBAAuB;oBACvC,WAAW,sBAAsB,SAAS,IAAI,sBAAsB,SAAS;oBAC7E,SAAS;;;;;;;;;;;;;;;;;AAMrB", "debugId": null}}, {"offset": {"line": 3356, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/workflows/agent-workflows-configuration.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useCallback } from 'react';\r\nimport { useRouter } from 'next/navigation';\r\nimport { Plus, Play, Pause, AlertCircle, Workflow, Trash2 } from 'lucide-react';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Card } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';\r\nimport { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Tabs, TabsContent } from '@/components/ui/tabs';\r\nimport { Alert, AlertDescription } from '@/components/ui/alert';\r\nimport { toast } from 'sonner';\r\nimport { \r\n  useAgentWorkflows, \r\n  useCreateAgentWorkflow,\r\n  useUpdateAgentWorkflow, \r\n  useDeleteAgentWorkflow, \r\n  useExecuteWorkflow, \r\n} from '@/hooks/react-query/agents/use-agent-workflows';\r\nimport { \r\n  AgentWorkflow\r\n} from '@/hooks/react-query/agents/workflow-utils';\r\n\r\ninterface AgentWorkflowsConfigurationProps {\r\n  agentId: string;\r\n  agentName: string;\r\n}\r\n\r\nexport function AgentWorkflowsConfiguration({ agentId, agentName }: AgentWorkflowsConfigurationProps) {\r\n  const router = useRouter();\r\n\r\n  const { data: workflows = [], isLoading } = useAgentWorkflows(agentId);\r\n  const createWorkflowMutation = useCreateAgentWorkflow();\r\n  const updateWorkflowMutation = useUpdateAgentWorkflow();\r\n  const deleteWorkflowMutation = useDeleteAgentWorkflow();\r\n  const executeWorkflowMutation = useExecuteWorkflow();\r\n\r\n  const [isExecuteDialogOpen, setIsExecuteDialogOpen] = useState(false);\r\n  const [workflowToExecute, setWorkflowToExecute] = useState<AgentWorkflow | null>(null);\r\n  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);\r\n  const [workflowToDelete, setWorkflowToDelete] = useState<AgentWorkflow | null>(null);\r\n  const [activeTab, setActiveTab] = useState('workflows');\r\n\r\n  const [executionInput, setExecutionInput] = useState<string>('');\r\n\r\n  const handleCreateWorkflow = useCallback(async () => {\r\n    try {\r\n      const defaultWorkflow = {\r\n        name: 'Untitled Workflow',\r\n        description: 'A new workflow',\r\n        steps: []\r\n      };\r\n      const newWorkflow = await createWorkflowMutation.mutateAsync({ \r\n        agentId, \r\n        workflow: defaultWorkflow \r\n      });\r\n      router.push(`/agents/config/${agentId}/workflow/${newWorkflow.id}`);\r\n    } catch (error) {\r\n      toast.error('Failed to create workflow');\r\n    }\r\n  }, [agentId, router, createWorkflowMutation]);\r\n\r\n  const handleUpdateWorkflowStatus = useCallback(async (workflowId: string, status: AgentWorkflow['status']) => {\r\n    await updateWorkflowMutation.mutateAsync({ \r\n      agentId, \r\n      workflowId, \r\n      workflow: { status } \r\n    });\r\n  }, [agentId, updateWorkflowMutation]);\r\n\r\n  const handleExecuteWorkflow = useCallback((workflow: AgentWorkflow) => {\r\n    setWorkflowToExecute(workflow);\r\n    setIsExecuteDialogOpen(true);\r\n  }, []);\r\n\r\n  const handleWorkflowClick = useCallback((workflowId: string) => {\r\n    router.push(`/agents/config/${agentId}/workflow/${workflowId}`);\r\n  }, [agentId, router]);\r\n\r\n  const handleDeleteWorkflow = useCallback((workflow: AgentWorkflow) => {\r\n    setWorkflowToDelete(workflow);\r\n    setIsDeleteDialogOpen(true);\r\n  }, []);\r\n\r\n  const handleConfirmDelete = useCallback(async () => {\r\n    if (!workflowToDelete) return;\r\n    \r\n    try {\r\n      await deleteWorkflowMutation.mutateAsync({ agentId, workflowId: workflowToDelete.id });\r\n      toast.success('Workflow deleted successfully');\r\n      setIsDeleteDialogOpen(false);\r\n      setWorkflowToDelete(null);\r\n    } catch (error) {\r\n      toast.error('Failed to delete workflow');\r\n    }\r\n  }, [agentId, workflowToDelete, deleteWorkflowMutation]);\r\n\r\n  const handleConfirmExecution = useCallback(async () => {\r\n    if (!workflowToExecute) return;\r\n    \r\n    try {\r\n      const result = await executeWorkflowMutation.mutateAsync({ \r\n        agentId, \r\n        workflowId: workflowToExecute.id, \r\n        execution: {\r\n          input_data: executionInput.trim() ? { prompt: executionInput } : undefined\r\n        } \r\n      });\r\n      \r\n      setIsExecuteDialogOpen(false);\r\n      setWorkflowToExecute(null);\r\n      setExecutionInput('');\r\n      \r\n      toast.success(\r\n        `${result.message}. Thread ID: ${result.thread_id}`,\r\n        {\r\n          action: result.thread_id ? {\r\n            label: \"View Execution\",\r\n            onClick: () => {\r\n              window.open(`/thread/${result.thread_id}`, '_blank');\r\n            }\r\n          } : undefined,\r\n          duration: 10000\r\n        }\r\n      );\r\n    } catch (error) {\r\n      toast.error('Failed to execute workflow');\r\n    }\r\n  }, [agentId, workflowToExecute, executionInput, executeWorkflowMutation]);\r\n\r\n\r\n\r\n  const getStatusBadge = (status: AgentWorkflow['status']) => {\r\n    const colors = {\r\n      draft: 'text-gray-700 bg-gray-100',\r\n      active: 'text-green-700 bg-green-100',\r\n      paused: 'text-yellow-700 bg-yellow-100',\r\n      archived: 'text-red-700 bg-red-100'\r\n    };\r\n    \r\n    return (\r\n      <Badge className={colors[status]}>\r\n        {status.charAt(0).toUpperCase() + status.slice(1)}\r\n      </Badge>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <div className=\"h-full flex flex-col\">\r\n      <div className=\"flex-shrink-0 mb-4\">\r\n        <Button \r\n          size='sm' \r\n          variant='outline' \r\n          className=\"flex items-center gap-2\" \r\n          onClick={handleCreateWorkflow}\r\n          disabled={createWorkflowMutation.isPending}\r\n        >\r\n          <Plus className=\"h-4 w-4\" />\r\n          {createWorkflowMutation.isPending ? 'Creating...' : 'Create Workflow'}\r\n        </Button>\r\n      </div>\r\n\r\n      <div className=\"flex-1 overflow-y-auto\">\r\n        <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\r\n          <TabsContent value=\"workflows\" className=\"space-y-4\">\r\n            {isLoading ? (\r\n              <div className=\"flex items-center justify-center p-8\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <AlertCircle className=\"h-5 w-5 animate-spin\" />\r\n                  <span>Loading workflows...</span>\r\n                </div>\r\n              </div>\r\n            ) : workflows.length === 0 ? (\r\n              <div className=\"text-center py-12 px-6 bg-muted/30 rounded-xl border-2 border-dashed border-border\">\r\n                <div className=\"mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-4 border\">\r\n                  <Workflow className=\"h-8 w-8 text-muted-foreground\" />\r\n                </div>\r\n                <h3 className=\"text-sm font-semibold mb-2\">No Agent Workflows</h3>\r\n                <p className=\"text-muted-foreground mb-6 max-w-sm mx-auto\">\r\n                  Create workflows to automate tasks and streamline your agent's operations.\r\n                </p>\r\n              </div>\r\n            ) : (\r\n              <div className=\"grid gap-4\">\r\n                {workflows.map((workflow) => (\r\n                  <Card \r\n                    key={workflow.id} \r\n                    className=\"p-4 cursor-pointer hover:opacity-80 transition-colors\"\r\n                    onClick={() => handleWorkflowClick(workflow.id)}\r\n                  >\r\n                    <div className=\"flex items-center justify-between\">\r\n                      <div className=\"flex-1\">\r\n                        <div className=\"flex items-center gap-3\">\r\n                          <h4 className=\"font-semibold\">{workflow.name}</h4>\r\n                          {getStatusBadge(workflow.status)}\r\n                          {workflow.is_default && <Badge variant=\"outline\">Default</Badge>}\r\n                        </div>\r\n                        <p className=\"text-sm text-muted-foreground mt-1\">{workflow.description}</p>\r\n                        {workflow.trigger_phrase && (\r\n                          <p className=\"text-xs text-muted-foreground mt-1\">\r\n                            Trigger: \"{workflow.trigger_phrase}\"\r\n                          </p>\r\n                        )}\r\n                        <div className=\"flex items-center gap-4 mt-2\">\r\n                          <span className=\"text-xs text-muted-foreground\">\r\n                            {workflow.steps.length} steps\r\n                          </span>\r\n                          <span className=\"text-xs text-muted-foreground\">\r\n                            Created {new Date(workflow.created_at).toLocaleDateString()}\r\n                          </span>\r\n                        </div>\r\n                      </div>\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <Button\r\n                          variant=\"ghost\"\r\n                          size=\"sm\"\r\n                          onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            handleExecuteWorkflow(workflow);\r\n                          }}\r\n                          disabled={workflow.status !== 'active' || executeWorkflowMutation.isPending}\r\n                        >\r\n                          <Play className=\"h-4 w-4\" />\r\n                          Execute\r\n                        </Button>\r\n                        <Button\r\n                          variant=\"ghost\"\r\n                          size=\"sm\"\r\n                          onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            handleUpdateWorkflowStatus(\r\n                              workflow.id,\r\n                              workflow.status === 'active' ? 'paused' : 'active'\r\n                            );\r\n                          }}\r\n                          disabled={updateWorkflowMutation.isPending}\r\n                        >\r\n                          {workflow.status === 'active' ? <Pause className=\"h-4 w-4\" /> : <Play className=\"h-4 w-4\" />}\r\n                        </Button>\r\n                        <Button\r\n                          variant=\"ghost\"\r\n                          size=\"sm\"\r\n                          onClick={(e) => {\r\n                            e.stopPropagation();\r\n                            handleDeleteWorkflow(workflow);\r\n                          }}\r\n                          disabled={deleteWorkflowMutation.isPending}\r\n                          className=\"text-red-600 hover:text-red-700 hover:bg-red-50\"\r\n                        >\r\n                          <Trash2 className=\"h-4 w-4\" />\r\n                        </Button>\r\n                      </div>\r\n                    </div>\r\n                  </Card>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </TabsContent>\r\n          </Tabs>\r\n        </div>\r\n      <Dialog open={isExecuteDialogOpen} onOpenChange={setIsExecuteDialogOpen}>\r\n        <DialogContent className=\"max-w-md\">\r\n          <DialogHeader>\r\n            <DialogTitle>Execute Workflow</DialogTitle>\r\n            <DialogDescription>\r\n              Provide input data for \"{workflowToExecute?.name}\" workflow\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          <div className=\"space-y-4\">\r\n            <div className=\"space-y-2\">\r\n              <Label>What would you like the workflow to work on?</Label>\r\n              <Textarea\r\n                value={executionInput}\r\n                onChange={(e) => setExecutionInput(e.target.value)}\r\n                placeholder=\"Enter your request...\"\r\n                rows={4}\r\n                className=\"resize-none\"\r\n                required={true}\r\n              />\r\n            </div>\r\n\r\n            <div className=\"flex items-center justify-between pt-4\">\r\n              <Button \r\n                variant=\"outline\" \r\n                onClick={() => setIsExecuteDialogOpen(false)}\r\n              >\r\n                Cancel\r\n              </Button>\r\n              <Button \r\n                onClick={handleConfirmExecution}\r\n                disabled={executeWorkflowMutation.isPending}\r\n              >\r\n                {executeWorkflowMutation.isPending ? 'Executing...' : 'Execute Workflow'}\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle>Delete Workflow</AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              Are you sure you want to delete workflow {workflowToDelete?.name}? This action cannot be undone.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel>Cancel</AlertDialogCancel>\r\n            <AlertDialogAction \r\n              onClick={handleConfirmDelete}\r\n              className=\"bg-red-600 hover:bg-red-700\"\r\n              disabled={deleteWorkflowMutation.isPending}\r\n            >\r\n              {deleteWorkflowMutation.isPending ? 'Deleting...' : 'Delete'}\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </div>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAfA;;;;;;;;;;;;;;;AA+BO,SAAS,4BAA4B,EAAE,OAAO,EAAE,SAAS,EAAoC;IAClG,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,EAAE,MAAM,YAAY,EAAE,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,qKAAA,CAAA,oBAAiB,AAAD,EAAE;IAC9D,MAAM,yBAAyB,CAAA,GAAA,qKAAA,CAAA,yBAAsB,AAAD;IACpD,MAAM,yBAAyB,CAAA,GAAA,qKAAA,CAAA,yBAAsB,AAAD;IACpD,MAAM,yBAAyB,CAAA,GAAA,qKAAA,CAAA,yBAAsB,AAAD;IACpD,MAAM,0BAA0B,CAAA,GAAA,qKAAA,CAAA,qBAAkB,AAAD;IAEjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IACjF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC/E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE7D,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACvC,IAAI;YACF,MAAM,kBAAkB;gBACtB,MAAM;gBACN,aAAa;gBACb,OAAO,EAAE;YACX;YACA,MAAM,cAAc,MAAM,uBAAuB,WAAW,CAAC;gBAC3D;gBACA,UAAU;YACZ;YACA,OAAO,IAAI,CAAC,CAAC,eAAe,EAAE,QAAQ,UAAU,EAAE,YAAY,EAAE,EAAE;QACpE,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF,GAAG;QAAC;QAAS;QAAQ;KAAuB;IAE5C,MAAM,6BAA6B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,YAAoB;QACxE,MAAM,uBAAuB,WAAW,CAAC;YACvC;YACA;YACA,UAAU;gBAAE;YAAO;QACrB;IACF,GAAG;QAAC;QAAS;KAAuB;IAEpC,MAAM,wBAAwB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACzC,qBAAqB;QACrB,uBAAuB;IACzB,GAAG,EAAE;IAEL,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACvC,OAAO,IAAI,CAAC,CAAC,eAAe,EAAE,QAAQ,UAAU,EAAE,YAAY;IAChE,GAAG;QAAC;QAAS;KAAO;IAEpB,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACxC,oBAAoB;QACpB,sBAAsB;IACxB,GAAG,EAAE;IAEL,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,IAAI,CAAC,kBAAkB;QAEvB,IAAI;YACF,MAAM,uBAAuB,WAAW,CAAC;gBAAE;gBAAS,YAAY,iBAAiB,EAAE;YAAC;YACpF,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,sBAAsB;YACtB,oBAAoB;QACtB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF,GAAG;QAAC;QAAS;QAAkB;KAAuB;IAEtD,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACzC,IAAI,CAAC,mBAAmB;QAExB,IAAI;YACF,MAAM,SAAS,MAAM,wBAAwB,WAAW,CAAC;gBACvD;gBACA,YAAY,kBAAkB,EAAE;gBAChC,WAAW;oBACT,YAAY,eAAe,IAAI,KAAK;wBAAE,QAAQ;oBAAe,IAAI;gBACnE;YACF;YAEA,uBAAuB;YACvB,qBAAqB;YACrB,kBAAkB;YAElB,wIAAA,CAAA,QAAK,CAAC,OAAO,CACX,GAAG,OAAO,OAAO,CAAC,aAAa,EAAE,OAAO,SAAS,EAAE,EACnD;gBACE,QAAQ,OAAO,SAAS,GAAG;oBACzB,OAAO;oBACP,SAAS;wBACP,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,OAAO,SAAS,EAAE,EAAE;oBAC7C;gBACF,IAAI;gBACJ,UAAU;YACZ;QAEJ,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF,GAAG;QAAC;QAAS;QAAmB;QAAgB;KAAwB;IAIxE,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS;YACb,OAAO;YACP,QAAQ;YACR,QAAQ;YACR,UAAU;QACZ;QAEA,qBACE,8OAAC,iIAAA,CAAA,QAAK;YAAC,WAAW,MAAM,CAAC,OAAO;sBAC7B,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;;;;;;IAGrD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBACL,MAAK;oBACL,SAAQ;oBACR,WAAU;oBACV,SAAS;oBACT,UAAU,uBAAuB,SAAS;;sCAE1C,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;wBACf,uBAAuB,SAAS,GAAG,gBAAgB;;;;;;;;;;;;0BAIxD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;oBAAc,WAAU;8BAC7D,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,OAAM;wBAAY,WAAU;kCACtC,0BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;kDAAK;;;;;;;;;;;;;;;;mCAGR,UAAU,MAAM,KAAK,kBACvB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CAA8C;;;;;;;;;;;iDAK7D,8OAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC,gIAAA,CAAA,OAAI;oCAEH,WAAU;oCACV,SAAS,IAAM,oBAAoB,SAAS,EAAE;8CAE9C,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAG,WAAU;0EAAiB,SAAS,IAAI;;;;;;4DAC3C,eAAe,SAAS,MAAM;4DAC9B,SAAS,UAAU,kBAAI,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAU;;;;;;;;;;;;kEAEnD,8OAAC;wDAAE,WAAU;kEAAsC,SAAS,WAAW;;;;;;oDACtE,SAAS,cAAc,kBACtB,8OAAC;wDAAE,WAAU;;4DAAqC;4DACrC,SAAS,cAAc;4DAAC;;;;;;;kEAGvC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;;oEACb,SAAS,KAAK,CAAC,MAAM;oEAAC;;;;;;;0EAEzB,8OAAC;gEAAK,WAAU;;oEAAgC;oEACrC,IAAI,KAAK,SAAS,UAAU,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;0DAI/D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,sBAAsB;wDACxB;wDACA,UAAU,SAAS,MAAM,KAAK,YAAY,wBAAwB,SAAS;;0EAE3E,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAG9B,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,2BACE,SAAS,EAAE,EACX,SAAS,MAAM,KAAK,WAAW,WAAW;wDAE9C;wDACA,UAAU,uBAAuB,SAAS;kEAEzC,SAAS,MAAM,KAAK,yBAAW,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;iFAAe,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;kEAElF,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,qBAAqB;wDACvB;wDACA,UAAU,uBAAuB,SAAS;wDAC1C,WAAU;kEAEV,cAAA,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mCA/DnB,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;0BA0E9B,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAqB,cAAc;0BAC/C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;8CAAC;;;;;;8CACb,8OAAC,kIAAA,CAAA,oBAAiB;;wCAAC;wCACQ,mBAAmB;wCAAK;;;;;;;;;;;;;sCAGrD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;sDAAC;;;;;;sDACP,8OAAC,oIAAA,CAAA,WAAQ;4CACP,OAAO;4CACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;4CACjD,aAAY;4CACZ,MAAM;4CACN,WAAU;4CACV,UAAU;;;;;;;;;;;;8CAId,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,SAAS,IAAM,uBAAuB;sDACvC;;;;;;sDAGD,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,wBAAwB,SAAS;sDAE1C,wBAAwB,SAAS,GAAG,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhE,8OAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM;gBAAoB,cAAc;0BACnD,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,8OAAC,2IAAA,CAAA,yBAAsB;;wCAAC;wCACoB,kBAAkB;wCAAK;;;;;;;;;;;;;sCAGrE,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS;oCACT,WAAU;oCACV,UAAU,uBAAuB,SAAS;8CAEzC,uBAAuB,SAAS,GAAG,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlE", "debugId": null}}, {"offset": {"line": 4017, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/knowledge-base/agent-knowledge-base-manager.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useRef, use<PERSON>allback } from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { \r\n  Plus, \r\n  Edit2, \r\n  Trash2, \r\n  Clock, \r\n  MoreVertical,\r\n  AlertCircle,\r\n  FileText,\r\n  Eye,\r\n  EyeOff,\r\n  Globe,\r\n  Search,\r\n  Loader2,\r\n  Bot,\r\n  Upload,\r\n  GitBranch,\r\n  Archive,\r\n  CheckCircle,\r\n  XCircle,\r\n  RefreshCw,\r\n  File as FileIcon,\r\n  BookO<PERSON>,\r\n  PenTool,\r\n  X\r\n} from 'lucide-react';\r\nimport { \r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from '@/components/ui/dropdown-menu';\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@/components/ui/alert-dialog';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport { \r\n  useAgentKnowledgeBaseEntries,\r\n  useCreateAgentKnowledgeBaseEntry,\r\n  useUpdateKnowledgeBaseEntry,\r\n  useDeleteKnowledgeBaseEntry,\r\n  useUploadAgentFiles,\r\n  useCloneGitRepository,\r\n  useAgentProcessingJobs,\r\n} from '@/hooks/react-query/knowledge-base/use-knowledge-base-queries';\r\nimport { cn, truncateString } from '@/lib/utils';\r\nimport { CreateKnowledgeBaseEntryRequest, KnowledgeBaseEntry, UpdateKnowledgeBaseEntryRequest, ProcessingJob } from '@/hooks/react-query/knowledge-base/types';\r\nimport { toast } from 'sonner';\r\nimport JSZip from 'jszip';\r\n\r\nimport { \r\n  SiJavascript, \r\n  SiTypescript, \r\n  SiPython, \r\n  SiReact, \r\n  SiHtml5, \r\n  SiCss3, \r\n  SiJson,\r\n  SiMarkdown,\r\n  SiYaml,\r\n  SiXml\r\n} from 'react-icons/si';\r\nimport { \r\n  FaFilePdf, \r\n  FaFileWord, \r\n  FaFileExcel, \r\n  FaFileImage, \r\n  FaFileArchive, \r\n  FaFileCode,\r\n  FaFileAlt,\r\n  FaFile\r\n} from 'react-icons/fa';\r\n\r\ninterface AgentKnowledgeBaseManagerProps {\r\n  agentId: string;\r\n  agentName: string;\r\n}\r\n\r\ninterface EditDialogData {\r\n  entry?: KnowledgeBaseEntry;\r\n  isOpen: boolean;\r\n}\r\n\r\ninterface UploadedFile {\r\n  file: File;\r\n  id: string;\r\n  status: 'pending' | 'uploading' | 'success' | 'error' | 'extracting';\r\n  error?: string;\r\n  isFromZip?: boolean;\r\n  zipParentId?: string;\r\n  originalPath?: string;\r\n}\r\n\r\nconst USAGE_CONTEXT_OPTIONS = [\r\n  { \r\n    value: 'always', \r\n    label: 'Always Active', \r\n    icon: Globe,\r\n    color: 'bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800'\r\n  },\r\n] as const;\r\n\r\nconst getFileTypeIcon = (filename: string, mimeType?: string) => {\r\n  const extension = filename.split('.').pop()?.toLowerCase();\r\n  switch (extension) {\r\n    case 'js':\r\n      return SiJavascript;\r\n    case 'ts':\r\n      return SiTypescript;\r\n    case 'jsx':\r\n    case 'tsx':\r\n      return SiReact;\r\n    case 'py':\r\n      return SiPython;\r\n    case 'html':\r\n      return SiHtml5;\r\n    case 'css':\r\n      return SiCss3;\r\n    case 'json':\r\n      return SiJson;\r\n    case 'md':\r\n      return SiMarkdown;\r\n    case 'yaml':\r\n    case 'yml':\r\n      return SiYaml;\r\n    case 'xml':\r\n      return SiXml;\r\n    case 'pdf':\r\n      return FaFilePdf;\r\n    case 'doc':\r\n    case 'docx':\r\n      return FaFileWord;\r\n    case 'xls':\r\n    case 'xlsx':\r\n    case 'csv':\r\n      return FaFileExcel;\r\n    case 'png':\r\n    case 'jpg':\r\n    case 'jpeg':\r\n    case 'gif':\r\n    case 'svg':\r\n    case 'webp':\r\n    case 'ico':\r\n      return FaFileImage;\r\n    case 'zip':\r\n    case 'rar':\r\n    case '7z':\r\n    case 'tar':\r\n    case 'gz':\r\n      return FaFileArchive;\r\n    default:\r\n      if (['java', 'cpp', 'c', 'cs', 'php', 'rb', 'go', 'rs', 'swift', 'kt', 'scala'].includes(extension || '')) {\r\n        return FaFileCode;\r\n      }\r\n      if (['txt', 'rtf', 'log'].includes(extension || '')) {\r\n        return FaFileAlt;\r\n      }\r\n      return FaFile;\r\n  }\r\n};\r\n\r\nconst getFileIconColor = (filename: string) => {\r\n  const extension = filename.split('.').pop()?.toLowerCase();\r\n  \r\n  switch (extension) {\r\n    case 'js':\r\n      return 'text-yellow-500';\r\n    case 'ts':\r\n    case 'tsx':\r\n      return 'text-blue-500';\r\n    case 'jsx':\r\n      return 'text-cyan-500';\r\n    case 'py':\r\n      return 'text-green-600';\r\n    case 'html':\r\n      return 'text-orange-600';\r\n    case 'css':\r\n      return 'text-blue-600';\r\n    case 'json':\r\n      return 'text-yellow-600';\r\n    case 'md':\r\n      return 'text-gray-700 dark:text-gray-300';\r\n    case 'yaml':\r\n    case 'yml':\r\n      return 'text-red-500';\r\n    case 'xml':\r\n      return 'text-orange-500';\r\n    case 'pdf':\r\n      return 'text-red-600';\r\n    case 'doc':\r\n    case 'docx':\r\n      return 'text-blue-700';\r\n    case 'xls':\r\n    case 'xlsx':\r\n    case 'csv':\r\n      return 'text-green-700';\r\n    case 'png':\r\n    case 'jpg':\r\n    case 'jpeg':\r\n    case 'gif':\r\n    case 'svg':\r\n    case 'webp':\r\n    case 'ico':\r\n      return 'text-purple-500';\r\n    case 'zip':\r\n    case 'rar':\r\n    case '7z':\r\n    case 'tar':\r\n    case 'gz':\r\n      return 'text-yellow-700';\r\n    default:\r\n      return 'text-gray-500';\r\n  }\r\n};\r\n\r\nconst getSourceIcon = (sourceType: string, filename?: string) => {\r\n  switch (sourceType) {\r\n    case 'file':\r\n      return filename ? getFileTypeIcon(filename) : FileIcon;\r\n    case 'git_repo':\r\n      return GitBranch;\r\n    case 'zip_extracted':\r\n      return Archive;\r\n    default:\r\n      return FileText;\r\n  }\r\n};\r\n\r\nconst AgentKnowledgeBaseSkeleton = () => (\r\n  <div className=\"space-y-6\">\r\n    <div className=\"flex items-center justify-between\">\r\n      <div className=\"relative w-full\">\r\n        <Skeleton className=\"h-10 w-full\" />\r\n      </div>\r\n      <Skeleton className=\"h-10 w-32 ml-4\" />\r\n    </div>\r\n\r\n    <div className=\"space-y-3\">\r\n      {[1, 2, 3].map((i) => (\r\n        <div key={i} className=\"border rounded-lg p-4\">\r\n          <div className=\"flex items-start justify-between gap-3\">\r\n            <div className=\"flex-1 min-w-0 space-y-2\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <Skeleton className=\"h-4 w-4\" />\r\n                <Skeleton className=\"h-5 w-48\" />\r\n                <Skeleton className=\"h-5 w-20\" />\r\n              </div>\r\n              <Skeleton className=\"h-4 w-64\" />\r\n              <div className=\"space-y-1\">\r\n                <Skeleton className=\"h-4 w-full\" />\r\n                <Skeleton className=\"h-4 w-3/4\" />\r\n              </div>\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center gap-3\">\r\n                  <Skeleton className=\"h-5 w-24\" />\r\n                  <Skeleton className=\"h-4 w-20\" />\r\n                </div>\r\n                <Skeleton className=\"h-4 w-16\" />\r\n              </div>\r\n            </div>\r\n            <Skeleton className=\"h-8 w-8\" />\r\n          </div>\r\n        </div>\r\n      ))}\r\n    </div>\r\n  </div>\r\n);\r\n\r\nexport const AgentKnowledgeBaseManager = ({ agentId, agentName }: AgentKnowledgeBaseManagerProps) => {\r\n  const [editDialog, setEditDialog] = useState<EditDialogData>({ isOpen: false });\r\n  const [deleteEntryId, setDeleteEntryId] = useState<string | null>(null);\r\n  const [searchQuery, setSearchQuery] = useState('');\r\n  const [addDialogOpen, setAddDialogOpen] = useState(false);\r\n  const [addDialogTab, setAddDialogTab] = useState<'manual' | 'files' | 'repo'>('manual');\r\n  const [dragActive, setDragActive] = useState(false);\r\n  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n  \r\n  const [formData, setFormData] = useState<CreateKnowledgeBaseEntryRequest>({\r\n    name: '',\r\n    description: '',\r\n    content: '',\r\n    usage_context: 'always',\r\n  });\r\n\r\n  const { data: knowledgeBase, isLoading, error } = useAgentKnowledgeBaseEntries(agentId);\r\n  const { data: processingJobsData } = useAgentProcessingJobs(agentId);\r\n  const createMutation = useCreateAgentKnowledgeBaseEntry();\r\n  const updateMutation = useUpdateKnowledgeBaseEntry();\r\n  const deleteMutation = useDeleteKnowledgeBaseEntry();\r\n  const uploadMutation = useUploadAgentFiles();\r\n  const cloneMutation = useCloneGitRepository();\r\n\r\n  const handleDrag = useCallback((e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    if (e.type === 'dragenter' || e.type === 'dragover') {\r\n      setDragActive(true);\r\n    } else if (e.type === 'dragleave') {\r\n      setDragActive(false);\r\n    }\r\n  }, []);\r\n\r\n  const handleDrop = useCallback((e: React.DragEvent) => {\r\n    e.preventDefault();\r\n    e.stopPropagation();\r\n    setDragActive(false);\r\n    \r\n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\r\n      handleFileUpload(e.dataTransfer.files);\r\n    }\r\n  }, []);\r\n\r\n  const handleOpenAddDialog = (tab: 'manual' | 'files' | 'repo' = 'manual') => {\r\n    setAddDialogTab(tab);\r\n    setAddDialogOpen(true);\r\n    setFormData({\r\n      name: '',\r\n      description: '',\r\n      content: '',\r\n      usage_context: 'always',\r\n    });\r\n    setUploadedFiles([]);\r\n  };\r\n\r\n  const handleOpenEditDialog = (entry: KnowledgeBaseEntry) => {\r\n    setFormData({\r\n      name: entry.name,\r\n      description: entry.description || '',\r\n      content: entry.content,\r\n      usage_context: entry.usage_context,\r\n    });\r\n    setEditDialog({ entry, isOpen: true });\r\n  };\r\n\r\n  const handleCloseDialog = () => {\r\n    setEditDialog({ isOpen: false });\r\n    setAddDialogOpen(false);\r\n    setFormData({\r\n      name: '',\r\n      description: '',\r\n      content: '',\r\n      usage_context: 'always',\r\n    });\r\n    setUploadedFiles([]);\r\n  };\r\n\r\n  const handleSubmit = async (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    \r\n    if (!formData.name.trim() || !formData.content.trim()) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      if (editDialog.entry) {\r\n        const updateData: UpdateKnowledgeBaseEntryRequest = {\r\n          name: formData.name !== editDialog.entry.name ? formData.name : undefined,\r\n          description: formData.description !== editDialog.entry.description ? formData.description : undefined,\r\n          content: formData.content !== editDialog.entry.content ? formData.content : undefined,\r\n          usage_context: formData.usage_context !== editDialog.entry.usage_context ? formData.usage_context : undefined,\r\n        };\r\n        const hasChanges = Object.values(updateData).some(value => value !== undefined);\r\n        if (hasChanges) {\r\n          await updateMutation.mutateAsync({ entryId: editDialog.entry.entry_id, data: updateData });\r\n        }\r\n      } else {\r\n        await createMutation.mutateAsync({ agentId, data: formData });\r\n      }\r\n      \r\n      handleCloseDialog();\r\n    } catch (error) {\r\n      console.error('Error saving agent knowledge base entry:', error);\r\n    }\r\n  };\r\n\r\n  const handleDelete = async (entryId: string) => {\r\n    try {\r\n      await deleteMutation.mutateAsync(entryId);\r\n      setDeleteEntryId(null);\r\n    } catch (error) {\r\n      console.error('Error deleting agent knowledge base entry:', error);\r\n    }\r\n  };\r\n\r\n  const handleToggleActive = async (entry: KnowledgeBaseEntry) => {\r\n    try {\r\n      await updateMutation.mutateAsync({\r\n        entryId: entry.entry_id,\r\n        data: { is_active: !entry.is_active }\r\n      });\r\n    } catch (error) {\r\n      console.error('Error toggling entry status:', error);\r\n    }\r\n  };\r\n\r\n  const extractZipFile = async (zipFile: File, zipId: string) => {\r\n    try {\r\n      setUploadedFiles(prev => prev.map(f => \r\n        f.id === zipId ? { ...f, status: 'extracting' } : f\r\n      ));\r\n\r\n      const zip = new JSZip();\r\n      const zipContent = await zip.loadAsync(zipFile);\r\n      const extractedFiles: UploadedFile[] = [];\r\n\r\n      for (const [path, file] of Object.entries(zipContent.files)) {\r\n        if (!file.dir && !path.startsWith('__MACOSX/') && !path.includes('/.')) {\r\n          try {\r\n            const blob = await file.async('blob');\r\n            const fileName = path.split('/').pop() || path;\r\n            const extractedFile = new File([blob], fileName);\r\n\r\n            extractedFiles.push({\r\n              file: extractedFile,\r\n              id: Math.random().toString(36).substr(2, 9),\r\n              status: 'pending' as const,\r\n              isFromZip: true,\r\n              zipParentId: zipId,\r\n              originalPath: path\r\n            });\r\n          } catch (error) {\r\n            console.warn(`Failed to extract ${path}:`, error);\r\n          }\r\n        }\r\n      }\r\n\r\n      setUploadedFiles(prev => [\r\n        ...prev.map(f => f.id === zipId ? { ...f, status: 'success' as const } : f),\r\n        ...extractedFiles\r\n      ]);\r\n\r\n      toast.success(`Extracted ${extractedFiles.length} files from ${zipFile.name}`);\r\n    } catch (error) {\r\n      console.error('Error extracting ZIP:', error);\r\n      setUploadedFiles(prev => prev.map(f => \r\n        f.id === zipId ? { \r\n          ...f, \r\n          status: 'error', \r\n          error: 'Failed to extract ZIP file' \r\n        } : f\r\n      ));\r\n      toast.error('Failed to extract ZIP file');\r\n    }\r\n  };\r\n\r\n  const handleFileUpload = async (files: FileList | null) => {\r\n    if (!files || files.length === 0) return;\r\n    \r\n    const newFiles: UploadedFile[] = [];\r\n    \r\n    for (const file of Array.from(files)) {\r\n      const fileId = Math.random().toString(36).substr(2, 9);\r\n      const uploadedFile: UploadedFile = {\r\n        file,\r\n        id: fileId,\r\n        status: 'pending'\r\n      };\r\n      \r\n      newFiles.push(uploadedFile);\r\n      if (file.name.toLowerCase().endsWith('.zip')) {\r\n        setTimeout(() => extractZipFile(file, fileId), 100);\r\n      }\r\n    }\r\n    \r\n    setUploadedFiles(prev => [...prev, ...newFiles]);\r\n    if (!addDialogOpen) {\r\n      setAddDialogTab('files');\r\n      setAddDialogOpen(true);\r\n    }\r\n  };\r\n\r\n  const uploadFiles = async () => {\r\n    const filesToUpload = uploadedFiles.filter(f => \r\n      f.status === 'pending' && \r\n      (f.isFromZip || !f.file.name.toLowerCase().endsWith('.zip'))\r\n    );\r\n    for (const uploadedFile of filesToUpload) {\r\n      try {\r\n        setUploadedFiles(prev => prev.map(f => \r\n          f.id === uploadedFile.id ? { ...f, status: 'uploading' as const } : f\r\n        ));\r\n        \r\n        await uploadMutation.mutateAsync({ agentId, file: uploadedFile.file });\r\n        \r\n        setUploadedFiles(prev => prev.map(f => \r\n          f.id === uploadedFile.id ? { ...f, status: 'success' as const } : f\r\n        ));\r\n      } catch (error) {\r\n        setUploadedFiles(prev => prev.map(f => \r\n          f.id === uploadedFile.id ? { \r\n            ...f, \r\n            status: 'error' as const, \r\n            error: error instanceof Error ? error.message : 'Upload failed' \r\n          } : f\r\n        ));\r\n      }\r\n    }\r\n    \r\n    setTimeout(() => {\r\n      const nonZipFiles = uploadedFiles.filter(f => !f.file.name.toLowerCase().endsWith('.zip') || f.isFromZip);\r\n      if (nonZipFiles.every(f => f.status === 'success')) {\r\n        handleCloseDialog();\r\n      }\r\n    }, 1000);\r\n  };\r\n\r\n  const removeFile = (fileId: string) => {\r\n    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));\r\n  };\r\n\r\n  const getUsageContextConfig = (context: string) => {\r\n    return USAGE_CONTEXT_OPTIONS.find(option => option.value === context) || USAGE_CONTEXT_OPTIONS[0];\r\n  };\r\n\r\n  const getJobStatusIcon = (status: string) => {\r\n    switch (status) {\r\n      case 'completed':\r\n        return CheckCircle;\r\n      case 'failed':\r\n        return XCircle;\r\n      case 'processing':\r\n        return RefreshCw;\r\n      default:\r\n        return Clock;\r\n    }\r\n  };\r\n\r\n  const getJobStatusColor = (status: string) => {\r\n    switch (status) {\r\n      case 'completed':\r\n        return 'text-green-600';\r\n      case 'failed':\r\n        return 'text-red-600';\r\n      case 'processing':\r\n        return 'text-blue-600';\r\n      default:\r\n        return 'text-yellow-600';\r\n    }\r\n  };\r\n\r\n  if (isLoading) {\r\n    return <AgentKnowledgeBaseSkeleton />;\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"flex items-center justify-center py-12\">\r\n        <div className=\"text-center\">\r\n          <AlertCircle className=\"h-8 w-8 text-red-500 mx-auto mb-4\" />\r\n          <p className=\"text-sm text-red-600 dark:text-red-400\">Failed to load agent knowledge base</p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  const entries = knowledgeBase?.entries || [];\r\n  const processingJobs = processingJobsData?.jobs || [];\r\n  const filteredEntries = entries.filter(entry => \r\n    entry.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n    entry.content.toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n    (entry.description && entry.description.toLowerCase().includes(searchQuery.toLowerCase()))\r\n  );\r\n\r\n  return (\r\n    <div \r\n      className=\"space-y-6\"\r\n      onDragEnter={handleDrag}\r\n      onDragLeave={handleDrag}\r\n      onDragOver={handleDrag}\r\n      onDrop={handleDrop}\r\n    >\r\n      {dragActive && (\r\n        <div className=\"fixed inset-0 bg-blue-500/20 backdrop-blur-sm z-50 flex items-center justify-center\">\r\n          <div className=\"bg-white dark:bg-gray-900 rounded-lg p-8 shadow-lg border-2 border-dashed border-blue-500\">\r\n            <Upload className=\"h-12 w-12 text-blue-500 mx-auto mb-4\" />\r\n            <p className=\"text-lg font-medium text-center\">Drop files here to upload</p>\r\n            <p className=\"text-sm text-muted-foreground text-center mt-2\">\r\n              Supports documents, images, code files, and ZIP archives\r\n            </p>\r\n          </div>\r\n        </div>\r\n      )}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"relative flex-1 max-w-md\">\r\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\r\n          <Input\r\n            placeholder=\"Search knowledge entries...\"\r\n            value={searchQuery}\r\n            onChange={(e) => setSearchQuery(e.target.value)}\r\n            className=\"pl-9\"\r\n          />\r\n        </div>\r\n        <Button onClick={() => handleOpenAddDialog()} className=\"gap-2\">\r\n          <Plus className=\"h-4 w-4\" />\r\n          Add Knowledge\r\n        </Button>\r\n      </div>\r\n      {entries.length === 0 ? (\r\n        <div className=\"text-center py-12 px-6 bg-muted/30 rounded-xl border-2 border-dashed border-border\">\r\n          <div className=\"mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-4 border\">\r\n            <Bot className=\"h-8 w-8 text-muted-foreground\" />\r\n          </div>\r\n          <h3 className=\"text-sm font-semibold mb-2\">No Agent Knowledge Entries</h3>\r\n          <p className=\"text-muted-foreground mb-6 max-w-sm mx-auto\">\r\n            Add knowledge entries to provide <span className=\"font-medium\">{agentName}</span> with specialized context, \r\n            guidelines, and information it should always remember.\r\n          </p>\r\n        </div>\r\n      ) : (\r\n        <div className=\"space-y-3\">\r\n          {filteredEntries.length === 0 ? (\r\n            <div className=\"text-center py-8\">\r\n              <Search className=\"h-8 w-8 mx-auto text-muted-foreground/50 mb-2\" />\r\n              <p className=\"text-sm text-muted-foreground\">No entries match your search</p>\r\n            </div>\r\n          ) : (\r\n            filteredEntries.map((entry) => {\r\n              const contextConfig = getUsageContextConfig(entry.usage_context);\r\n              const ContextIcon = contextConfig.icon;\r\n              const SourceIcon = getSourceIcon(entry.source_type || 'manual', entry.source_metadata?.filename);\r\n              \r\n              return (\r\n                <Card\r\n                  key={entry.entry_id}\r\n                  className={cn(\r\n                    \"group transition-all p-0\",\r\n                    entry.is_active \r\n                      ? \"bg-card\" \r\n                      : \"bg-muted/30 opacity-70\"\r\n                  )}\r\n                >\r\n                  <CardContent className=\"p-4\">\r\n                    <div className=\"flex items-start justify-between gap-3\">\r\n                      <div className=\"flex-1 min-w-0 space-y-2\">\r\n                        <div className=\"flex items-center gap-2\">\r\n                          <SourceIcon className=\"h-4 w-4 text-muted-foreground flex-shrink-0\" />\r\n                          <h3 className=\"font-medium truncate\">{entry.name}</h3>\r\n                          {!entry.is_active && (\r\n                            <Badge variant=\"outline\" className=\"text-xs\">\r\n                              <EyeOff className=\"h-3 w-3 mr-1\" />\r\n                              Disabled\r\n                            </Badge>\r\n                          )}\r\n                          {entry.source_type && entry.source_type !== 'manual' && (\r\n                            <Badge variant=\"outline\" className=\"text-xs\">\r\n                              {entry.source_type === 'git_repo' ? 'Git' : \r\n                               entry.source_type === 'zip_extracted' ? 'ZIP' : 'File'}\r\n                            </Badge>\r\n                          )}\r\n                        </div>\r\n                        {entry.description && (\r\n                          <p className=\"text-sm text-muted-foreground line-clamp-1\">\r\n                            {entry.description}\r\n                          </p>\r\n                        )}\r\n                        <p className=\"text-sm text-foreground/80 line-clamp-2 leading-relaxed\">\r\n                          {entry.content}\r\n                        </p>\r\n                        <div className=\"flex items-center justify-between\">\r\n                          <div className=\"flex items-center gap-3\">\r\n                            <Badge variant=\"outline\" className={cn(\"text-xs gap-1\", contextConfig.color)}>\r\n                              <ContextIcon className=\"h-3 w-3\" />\r\n                              {contextConfig.label}\r\n                            </Badge>\r\n                            <span className=\"text-xs text-muted-foreground flex items-center gap-1\">\r\n                              <Clock className=\"h-3 w-3\" />\r\n                              {new Date(entry.created_at).toLocaleDateString()}\r\n                            </span>\r\n                            {entry.file_size && (\r\n                              <span className=\"text-xs text-muted-foreground\">\r\n                                {(entry.file_size / 1024).toFixed(1)}KB\r\n                              </span>\r\n                            )}\r\n                          </div>\r\n                          {entry.content_tokens && (\r\n                            <span className=\"text-xs text-muted-foreground\">\r\n                              ~{entry.content_tokens.toLocaleString()} tokens\r\n                            </span>\r\n                          )}\r\n                        </div>\r\n                      </div>\r\n                      <DropdownMenu>\r\n                        <DropdownMenuTrigger asChild>\r\n                          <Button \r\n                            variant=\"ghost\" \r\n                            size=\"sm\" \r\n                            className=\"h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity\"\r\n                          >\r\n                            <MoreVertical className=\"h-4 w-4\" />\r\n                          </Button>\r\n                        </DropdownMenuTrigger>\r\n                        <DropdownMenuContent align=\"end\" className=\"w-36\">\r\n                          <DropdownMenuItem onClick={() => handleOpenEditDialog(entry)}>\r\n                            <Edit2 className=\"h-4 w-4\" />\r\n                            Edit\r\n                          </DropdownMenuItem>\r\n                          <DropdownMenuItem onClick={() => handleToggleActive(entry)}>\r\n                            {entry.is_active ? (\r\n                              <>\r\n                                <EyeOff className=\"h-4 w-4\" />\r\n                                Disable\r\n                              </>\r\n                            ) : (\r\n                              <>\r\n                                <Eye className=\"h-4 w-4\" />\r\n                                Enable\r\n                              </>\r\n                            )}\r\n                          </DropdownMenuItem>\r\n                          <DropdownMenuSeparator />\r\n                          <DropdownMenuItem \r\n                            onClick={() => setDeleteEntryId(entry.entry_id)}\r\n                            className=\"text-destructive focus:bg-destructive/10 focus:text-destructive\"\r\n                          >\r\n                            <Trash2 className=\"h-4 w-4 text-destructive\" />\r\n                            Delete\r\n                          </DropdownMenuItem>\r\n                        </DropdownMenuContent>\r\n                      </DropdownMenu>\r\n                    </div>\r\n                  </CardContent>\r\n                </Card>\r\n              );\r\n            })\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      {/* Processing Jobs */}\r\n      {processingJobs.length > 0 && (\r\n        <Card>\r\n          <CardHeader>\r\n            <CardTitle className=\"text-sm font-medium flex items-center gap-2\">\r\n              <RefreshCw className=\"h-4 w-4\" />\r\n              Processing Status\r\n            </CardTitle>\r\n          </CardHeader>\r\n          <CardContent className=\"space-y-3\">\r\n            {processingJobs.map((job) => {\r\n              const StatusIcon = getJobStatusIcon(job.status);\r\n              const statusColor = getJobStatusColor(job.status);\r\n              \r\n              return (\r\n                <div key={job.job_id} className=\"flex items-center justify-between p-3 bg-muted/50 rounded-lg\">\r\n                  <div className=\"flex items-center gap-3\">\r\n                    <StatusIcon className={cn(\"h-4 w-4\", statusColor, job.status === 'processing' && 'animate-spin')} />\r\n                    <div>\r\n                      <p className=\"text-sm font-medium\">\r\n                        {job.job_type === 'file_upload' ? 'File Upload' :\r\n                         job.job_type === 'git_clone' ? 'Git Repository' : 'Processing'}\r\n                      </p>\r\n                      <p className=\"text-xs text-muted-foreground\">\r\n                        {job.source_info.filename || job.source_info.git_url || 'Unknown source'}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"text-right\">\r\n                    <Badge variant={job.status === 'completed' ? 'default' : \r\n                                 job.status === 'failed' ? 'destructive' : 'secondary'} className=\"text-xs\">\r\n                      {job.status}\r\n                    </Badge>\r\n                    {job.status === 'completed' && (\r\n                      <p className=\"text-xs text-muted-foreground mt-1\">\r\n                        {job.entries_created} entries created\r\n                      </p>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n              );\r\n            })}\r\n          </CardContent>\r\n        </Card>\r\n      )}\r\n      <input\r\n        ref={fileInputRef}\r\n        type=\"file\"\r\n        multiple\r\n        onChange={(e) => handleFileUpload(e.target.files)}\r\n        className=\"hidden\"\r\n        accept=\".txt,.md,.py,.js,.ts,.html,.css,.json,.yaml,.yml,.xml,.csv,.pdf,.docx,.xlsx,.png,.jpg,.jpeg,.gif,.zip\"\r\n      />\r\n      <Dialog open={addDialogOpen} onOpenChange={setAddDialogOpen}>\r\n        <DialogContent className=\"max-w-4xl max-h-[90vh] overflow-hidden flex flex-col\">\r\n          <DialogHeader className=\"flex-shrink-0\">\r\n            <DialogTitle className=\"flex items-center gap-2\">\r\n              <BookOpen className=\"h-5 w-5 text-blue-600\" />\r\n              Add Knowledge to {agentName}\r\n            </DialogTitle>\r\n          </DialogHeader>\r\n          \r\n          <div className=\"flex-1 overflow-y-auto\">\r\n            <Tabs value={addDialogTab} onValueChange={(value) => setAddDialogTab(value as any)} className=\"w-full\">\r\n              <TabsList className=\"grid w-80 grid-cols-2\">\r\n                <TabsTrigger value=\"manual\" className=\"gap-2\">\r\n                  <PenTool className=\"h-4 w-4\" />\r\n                  Write Knowledge\r\n                </TabsTrigger>\r\n                <TabsTrigger value=\"files\" className=\"gap-2\">\r\n                  <Upload className=\"h-4 w-4\" />\r\n                  Upload Files\r\n                  {uploadedFiles.length > 0 && (\r\n                    <Badge variant=\"outline\" className=\"ml-1\">\r\n                      {uploadedFiles.length}\r\n                    </Badge>\r\n                  )}\r\n                </TabsTrigger>\r\n              </TabsList>\r\n\r\n              <TabsContent value=\"manual\" className=\"space-y-6 mt-6\">\r\n                <form onSubmit={handleSubmit} className=\"space-y-6\">\r\n                  <div className=\"space-y-2\">\r\n                    <Label htmlFor=\"name\" className=\"text-sm font-medium\">Name *</Label>\r\n                    <Input\r\n                      id=\"name\"\r\n                      value={formData.name}\r\n                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\r\n                      placeholder=\"e.g., Coding Standards, Domain Knowledge, API Guidelines\"\r\n                      required\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"space-y-2\">\r\n                    <Label htmlFor=\"usage_context\" className=\"text-sm font-medium\">Usage Context</Label>\r\n                    <Select\r\n                      value={formData.usage_context}\r\n                      onValueChange={(value: 'always' | 'on_request' | 'contextual') => \r\n                        setFormData(prev => ({ ...prev, usage_context: value }))\r\n                      }\r\n                    >\r\n                      <SelectTrigger>\r\n                        <SelectValue />\r\n                      </SelectTrigger>\r\n                      <SelectContent>\r\n                        {USAGE_CONTEXT_OPTIONS.map((option) => {\r\n                          const Icon = option.icon;\r\n                          return (\r\n                            <SelectItem key={option.value} value={option.value}>\r\n                              <div className=\"flex items-center gap-2\">\r\n                                <Icon className=\"h-4 w-4\" />\r\n                                <span>{option.label}</span>\r\n                              </div>\r\n                            </SelectItem>\r\n                          );\r\n                        })}\r\n                      </SelectContent>\r\n                    </Select>\r\n                  </div>\r\n\r\n                  <div className=\"space-y-2\">\r\n                    <Label htmlFor=\"description\" className=\"text-sm font-medium\">Description</Label>\r\n                    <Input\r\n                      id=\"description\"\r\n                      value={formData.description}\r\n                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\r\n                      placeholder=\"Brief description of this knowledge (optional)\"\r\n                    />\r\n                  </div>\r\n\r\n                  <div className=\"space-y-2\">\r\n                    <Label htmlFor=\"content\" className=\"text-sm font-medium\">Content *</Label>\r\n                    <Textarea\r\n                      id=\"content\"\r\n                      value={formData.content}\r\n                      onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}\r\n                      placeholder={`Enter the specialized knowledge that ${agentName} should know...`}\r\n                      className=\"min-h-[200px] resize-y\"\r\n                      required\r\n                    />\r\n                    <div className=\"text-xs text-muted-foreground\">\r\n                      Approximately {Math.ceil(formData.content.length / 4).toLocaleString()} tokens\r\n                    </div>\r\n                  </div>\r\n\r\n                  <div className=\"flex justify-end gap-3 pt-4 border-t\">\r\n                    <Button type=\"button\" variant=\"outline\" onClick={handleCloseDialog}>\r\n                      Cancel\r\n                    </Button>\r\n                    <Button \r\n                      type=\"submit\" \r\n                      disabled={!formData.name.trim() || !formData.content.trim() || createMutation.isPending}\r\n                      className=\"gap-2\"\r\n                    >\r\n                      {createMutation.isPending ? (\r\n                        <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n                      ) : (\r\n                        <Plus className=\"h-4 w-4\" />\r\n                      )}\r\n                      Add Knowledge\r\n                    </Button>\r\n                  </div>\r\n                </form>\r\n              </TabsContent>\r\n\r\n              <TabsContent value=\"files\" className=\"space-y-6 mt-6\">\r\n                <div className=\"space-y-4\">\r\n                  {uploadedFiles.length === 0 && (\r\n                    <div className=\"border-2 border-dashed border-border rounded-lg p-8 text-center\">\r\n                      <Upload className=\"h-12 w-12 mx-auto mb-4 text-muted-foreground\" />\r\n                      <h3 className=\"text-lg font-medium mb-2\">Upload Files</h3>\r\n                      <p className=\"text-sm text-muted-foreground mb-4\">\r\n                        Drag and drop files here or click to browse.<br />\r\n                        Supports: Documents, Code, ZIP archives\r\n                      </p>\r\n                      <Button \r\n                        onClick={() => fileInputRef.current?.click()}\r\n                        variant=\"outline\"\r\n                        className=\"gap-2\"\r\n                      >\r\n                        <Upload className=\"h-4 w-4\" />\r\n                        Choose Files\r\n                      </Button>\r\n                    </div>\r\n                  )}\r\n                  {uploadedFiles.length > 0 && (\r\n                    <div className=\"space-y-6\">\r\n                      {uploadedFiles.filter(f => f.file.name.toLowerCase().endsWith('.zip') && !f.isFromZip).map((zipFile) => {\r\n                        const extractedFiles = uploadedFiles.filter(f => f.zipParentId === zipFile.id);\r\n                        return (\r\n                          <div key={zipFile.id} className=\"space-y-3\">\r\n                            {extractedFiles.length > 0 && (\r\n                              <div>\r\n                                <p className=\"text-sm font-medium text-muted-foreground mb-3\">\r\n                                  Extracted Files ({extractedFiles.length}):\r\n                                </p>\r\n                                <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3\">\r\n                                  {extractedFiles.map((extractedFile) => {\r\n                                    const ExtractedFileIcon = getFileTypeIcon(extractedFile.file.name);\r\n                                    const iconColor = getFileIconColor(extractedFile.file.name);\r\n                                    return (\r\n                                      <div key={extractedFile.id} className=\"group relative p-2 pb-0 rounded-lg border bg-muted flex items-center\">\r\n                                        <div className=\"flex items-center text-center space-y-2\">\r\n                                          <ExtractedFileIcon className={cn(\"h-8 w-8\", iconColor)} />\r\n                                          <div className=\"w-full flex flex-col items-start ml-2\">\r\n                                            <p className=\"text-xs font-medium truncate\" title={extractedFile.file.name}>\r\n                                              {extractedFile.file.name}\r\n                                            </p>\r\n                                            <p className=\"text-xs text-muted-foreground\">\r\n                                              {(extractedFile.file.size / 1024).toFixed(1)}KB\r\n                                            </p>\r\n                                          </div>\r\n                                          <div className=\"absolute top-1 right-1\">\r\n                                            {extractedFile.status === 'uploading' && (\r\n                                              <Loader2 className=\"h-3 w-3 animate-spin text-blue-600\" />\r\n                                            )}\r\n                                            {extractedFile.status === 'success' && (\r\n                                              <CheckCircle className=\"h-3 w-3 text-green-600\" />\r\n                                            )}\r\n                                            {extractedFile.status === 'error' && (\r\n                                              <XCircle className=\"h-3 w-3 text-red-600\" />\r\n                                            )}\r\n                                            {extractedFile.status === 'pending' && (\r\n                                              <Button\r\n                                                variant=\"ghost\"\r\n                                                size=\"sm\"\r\n                                                onClick={() => removeFile(extractedFile.id)}\r\n                                                className=\"h-4 w-4 p-0 opacity-0 group-hover:opacity-100 transition-opacity\"\r\n                                              >\r\n                                                <X className=\"h-3 w-3\" />\r\n                                              </Button>\r\n                                            )}\r\n                                          </div>\r\n                                        </div>\r\n                                      </div>\r\n                                    );\r\n                                  })}\r\n                                </div>\r\n                              </div>\r\n                            )}\r\n                          </div>\r\n                        );\r\n                      })}\r\n                      {uploadedFiles.filter(f => !f.isFromZip && !f.file.name.toLowerCase().endsWith('.zip')).length > 0 && (\r\n                        <div className=\"space-y-3\">\r\n                          <p className=\"text-sm font-medium text-muted-foreground\">\r\n                            Individual Files ({uploadedFiles.filter(f => !f.isFromZip && !f.file.name.toLowerCase().endsWith('.zip')).length}):\r\n                          </p>\r\n                          <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3\">\r\n                            {uploadedFiles.filter(f => !f.isFromZip && !f.file.name.toLowerCase().endsWith('.zip')).map((uploadedFile) => {\r\n                              const FileTypeIcon = getFileTypeIcon(uploadedFile.file.name);\r\n                              const iconColor = getFileIconColor(uploadedFile.file.name);\r\n                              return (\r\n                                <div key={uploadedFile.id} className=\"group relative p-2 pb-0 rounded-lg border bg-muted flex items-center\">\r\n                                  <div className=\"flex items-center text-center space-y-2\">\r\n                                    <FileTypeIcon className={cn(\"h-8 w-8\", iconColor)} />\r\n                                    <div className=\"w-full flex flex-col items-start ml-2\">\r\n                                      <p className=\"text-xs font-medium truncate\" title={uploadedFile.file.name}>\r\n                                        {truncateString(uploadedFile.file.name, 20)}\r\n                                      </p>\r\n                                      <p className=\"text-xs text-muted-foreground\">\r\n                                        {(uploadedFile.file.size / 1024).toFixed(1)}KB\r\n                                      </p>\r\n                                    </div>\r\n                                    <div className=\"absolute top-1 right-1\">\r\n                                      {uploadedFile.status === 'uploading' && (\r\n                                        <Loader2 className=\"h-3 w-3 animate-spin text-blue-600\" />\r\n                                      )}\r\n                                      {uploadedFile.status === 'success' && (\r\n                                        <CheckCircle className=\"h-3 w-3 text-green-600\" />\r\n                                      )}\r\n                                      {uploadedFile.status === 'error' && (\r\n                                        <XCircle className=\"h-3 w-3 text-red-600\" />\r\n                                      )}\r\n                                      {uploadedFile.status === 'pending' && (\r\n                                        <Button\r\n                                          variant=\"ghost\"\r\n                                          size=\"sm\"\r\n                                          onClick={() => removeFile(uploadedFile.id)}\r\n                                          className=\"h-4 w-4 p-0 opacity-0 group-hover:opacity-100 transition-opacity\"\r\n                                        >\r\n                                          <X className=\"h-3 w-3\" />\r\n                                        </Button>\r\n                                      )}\r\n                                    </div>\r\n                                  </div>\r\n                                </div>\r\n                              );\r\n                            })}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  )}\r\n\r\n                  {uploadedFiles.length > 0 && (\r\n                    <div className=\"flex justify-end gap-3 pt-4 border-t\">\r\n                      <Button type=\"button\" variant=\"outline\" onClick={handleCloseDialog}>\r\n                        Cancel\r\n                      </Button>\r\n                      <Button \r\n                        onClick={uploadFiles}\r\n                        disabled={uploadMutation.isPending || uploadedFiles.filter(f => \r\n                          f.status === 'pending' && \r\n                          (f.isFromZip || !f.file.name.toLowerCase().endsWith('.zip'))\r\n                        ).length === 0}\r\n                        className=\"gap-2\"\r\n                      >\r\n                        {uploadMutation.isPending ? (\r\n                          <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n                        ) : (\r\n                          <Upload className=\"h-4 w-4\" />\r\n                        )}\r\n                        Upload Files ({uploadedFiles.filter(f => \r\n                          f.status === 'pending' && \r\n                          (f.isFromZip || !f.file.name.toLowerCase().endsWith('.zip'))\r\n                        ).length})\r\n                      </Button>\r\n                    </div>\r\n                  )}\r\n                </div>\r\n              </TabsContent>\r\n            </Tabs>\r\n          </div>\r\n        </DialogContent>\r\n      </Dialog>\r\n      <Dialog open={editDialog.isOpen} onOpenChange={handleCloseDialog}>\r\n        <DialogContent className=\"max-w-2xl max-h-[85vh] overflow-hidden flex flex-col\">\r\n          <DialogHeader className=\"flex-shrink-0\">\r\n            <DialogTitle className=\"flex items-center gap-2\">\r\n              <Edit2 className=\"h-5 w-5 text-blue-600\" />\r\n              Edit Knowledge Entry\r\n            </DialogTitle>\r\n          </DialogHeader>\r\n          \r\n          <div className=\"flex-1 overflow-y-auto\">\r\n            <form onSubmit={handleSubmit} className=\"space-y-6 p-1\">\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"edit-name\" className=\"text-sm font-medium\">Name *</Label>\r\n                <Input\r\n                  id=\"edit-name\"\r\n                  value={formData.name}\r\n                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}\r\n                  placeholder=\"e.g., Coding Standards, Domain Knowledge, API Guidelines\"\r\n                  required\r\n                />\r\n              </div>\r\n\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"edit-usage_context\" className=\"text-sm font-medium\">Usage Context</Label>\r\n                <Select\r\n                  value={formData.usage_context}\r\n                  onValueChange={(value: 'always' | 'on_request' | 'contextual') => \r\n                    setFormData(prev => ({ ...prev, usage_context: value }))\r\n                  }\r\n                >\r\n                  <SelectTrigger>\r\n                    <SelectValue />\r\n                  </SelectTrigger>\r\n                  <SelectContent>\r\n                    {USAGE_CONTEXT_OPTIONS.map((option) => {\r\n                      const Icon = option.icon;\r\n                      return (\r\n                        <SelectItem key={option.value} value={option.value}>\r\n                          <div className=\"flex items-center gap-2\">\r\n                            <Icon className=\"h-4 w-4\" />\r\n                            <span>{option.label}</span>\r\n                          </div>\r\n                        </SelectItem>\r\n                      );\r\n                    })}\r\n                  </SelectContent>\r\n                </Select>\r\n              </div>\r\n\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"edit-description\" className=\"text-sm font-medium\">Description</Label>\r\n                <Input\r\n                  id=\"edit-description\"\r\n                  value={formData.description}\r\n                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}\r\n                  placeholder=\"Brief description of this knowledge (optional)\"\r\n                />\r\n              </div>\r\n\r\n              <div className=\"space-y-2\">\r\n                <Label htmlFor=\"edit-content\" className=\"text-sm font-medium\">Content *</Label>\r\n                <Textarea\r\n                  id=\"edit-content\"\r\n                  value={formData.content}\r\n                  onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}\r\n                  placeholder={`Enter the specialized knowledge that ${agentName} should know...`}\r\n                  className=\"min-h-[200px] resize-y\"\r\n                  required\r\n                />\r\n                <div className=\"text-xs text-muted-foreground\">\r\n                  Approximately {Math.ceil(formData.content.length / 4).toLocaleString()} tokens\r\n                </div>\r\n              </div>\r\n\r\n              <div className=\"flex justify-end gap-3 pt-4 border-t\">\r\n                <Button type=\"button\" variant=\"outline\" onClick={handleCloseDialog}>\r\n                  Cancel\r\n                </Button>\r\n                <Button \r\n                  type=\"submit\" \r\n                  disabled={!formData.name.trim() || !formData.content.trim() || updateMutation.isPending}\r\n                  className=\"gap-2\"\r\n                >\r\n                  {updateMutation.isPending ? (\r\n                    <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n                  ) : (\r\n                    <Edit2 className=\"h-4 w-4\" />\r\n                  )}\r\n                  Save Changes\r\n                </Button>\r\n              </div>\r\n            </form>\r\n          </div>\r\n        </DialogContent>\r\n      </Dialog>\r\n      <AlertDialog open={!!deleteEntryId} onOpenChange={() => setDeleteEntryId(null)}>\r\n        <AlertDialogContent>\r\n          <AlertDialogHeader>\r\n            <AlertDialogTitle className=\"flex items-center gap-2\">\r\n              <AlertCircle className=\"h-5 w-5 text-destructive\" />\r\n              Delete Knowledge Entry\r\n            </AlertDialogTitle>\r\n            <AlertDialogDescription>\r\n              This will permanently delete this knowledge entry. {agentName} will no longer have access to this information.\r\n            </AlertDialogDescription>\r\n          </AlertDialogHeader>\r\n          <AlertDialogFooter>\r\n            <AlertDialogCancel>Cancel</AlertDialogCancel>\r\n            <AlertDialogAction\r\n              onClick={() => deleteEntryId && handleDelete(deleteEntryId)}\r\n              className=\"bg-destructive hover:bg-destructive/90\"\r\n            >\r\n              Delete Entry\r\n            </AlertDialogAction>\r\n          </AlertDialogFooter>\r\n        </AlertDialogContent>\r\n      </AlertDialog>\r\n    </div>\r\n  );\r\n}; "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAyBA;AAOA;AAUA;AAMA;AASA;AAEA;AACA;AAEA;AAYA;AAtFA;;;;;;;;;;;;;;;;;;;;;;AAqHA,MAAM,wBAAwB;IAC5B;QACE,OAAO;QACP,OAAO;QACP,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;IACT;CACD;AAED,MAAM,kBAAkB,CAAC,UAAkB;IACzC,MAAM,YAAY,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI;IAC7C,OAAQ;QACN,KAAK;YACH,OAAO,8IAAA,CAAA,eAAY;QACrB,KAAK;YACH,OAAO,8IAAA,CAAA,eAAY;QACrB,KAAK;QACL,KAAK;YACH,OAAO,8IAAA,CAAA,UAAO;QAChB,KAAK;YACH,OAAO,8IAAA,CAAA,WAAQ;QACjB,KAAK;YACH,OAAO,8IAAA,CAAA,UAAO;QAChB,KAAK;YACH,OAAO,8IAAA,CAAA,SAAM;QACf,KAAK;YACH,OAAO,8IAAA,CAAA,SAAM;QACf,KAAK;YACH,OAAO,8IAAA,CAAA,aAAU;QACnB,KAAK;QACL,KAAK;YACH,OAAO,8IAAA,CAAA,SAAM;QACf,KAAK;YACH,OAAO,8IAAA,CAAA,QAAK;QACd,KAAK;YACH,OAAO,8IAAA,CAAA,YAAS;QAClB,KAAK;QACL,KAAK;YACH,OAAO,8IAAA,CAAA,aAAU;QACnB,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,8IAAA,CAAA,cAAW;QACpB,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,8IAAA,CAAA,cAAW;QACpB,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO,8IAAA,CAAA,gBAAa;QACtB;YACE,IAAI;gBAAC;gBAAQ;gBAAO;gBAAK;gBAAM;gBAAO;gBAAM;gBAAM;gBAAM;gBAAS;gBAAM;aAAQ,CAAC,QAAQ,CAAC,aAAa,KAAK;gBACzG,OAAO,8IAAA,CAAA,aAAU;YACnB;YACA,IAAI;gBAAC;gBAAO;gBAAO;aAAM,CAAC,QAAQ,CAAC,aAAa,KAAK;gBACnD,OAAO,8IAAA,CAAA,YAAS;YAClB;YACA,OAAO,8IAAA,CAAA,SAAM;IACjB;AACF;AAEA,MAAM,mBAAmB,CAAC;IACxB,MAAM,YAAY,SAAS,KAAK,CAAC,KAAK,GAAG,IAAI;IAE7C,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,MAAM,gBAAgB,CAAC,YAAoB;IACzC,OAAQ;QACN,KAAK;YACH,OAAO,WAAW,gBAAgB,YAAY,kMAAA,CAAA,OAAQ;QACxD,KAAK;YACH,OAAO,gNAAA,CAAA,YAAS;QAClB,KAAK;YACH,OAAO,wMAAA,CAAA,UAAO;QAChB;YACE,OAAO,8MAAA,CAAA,WAAQ;IACnB;AACF;AAEA,MAAM,6BAA6B,kBACjC,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAEtB,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;0BAGtB,8OAAC;gBAAI,WAAU;0BACZ;oBAAC;oBAAG;oBAAG;iBAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;wBAAY,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAEtB,8OAAC,oIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oIAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC,oIAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;;8DAEtB,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;8CAGxB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;uBArBd;;;;;;;;;;;;;;;;AA6BX,MAAM,4BAA4B,CAAC,EAAE,OAAO,EAAE,SAAS,EAAkC;IAC9F,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QAAE,QAAQ;IAAM;IAC7E,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IAC9E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACrE,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;QACxE,MAAM;QACN,aAAa;QACb,SAAS;QACT,eAAe;IACjB;IAEA,MAAM,EAAE,MAAM,aAAa,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,0LAAA,CAAA,+BAA4B,AAAD,EAAE;IAC/E,MAAM,EAAE,MAAM,kBAAkB,EAAE,GAAG,CAAA,GAAA,0LAAA,CAAA,yBAAsB,AAAD,EAAE;IAC5D,MAAM,iBAAiB,CAAA,GAAA,0LAAA,CAAA,mCAAgC,AAAD;IACtD,MAAM,iBAAiB,CAAA,GAAA,0LAAA,CAAA,8BAA2B,AAAD;IACjD,MAAM,iBAAiB,CAAA,GAAA,0LAAA,CAAA,8BAA2B,AAAD;IACjD,MAAM,iBAAiB,CAAA,GAAA,0LAAA,CAAA,sBAAmB,AAAD;IACzC,MAAM,gBAAgB,CAAA,GAAA,0LAAA,CAAA,wBAAqB,AAAD;IAE1C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,IAAI,EAAE,IAAI,KAAK,eAAe,EAAE,IAAI,KAAK,YAAY;YACnD,cAAc;QAChB,OAAO,IAAI,EAAE,IAAI,KAAK,aAAa;YACjC,cAAc;QAChB;IACF,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;QAEd,IAAI,EAAE,YAAY,CAAC,KAAK,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE;YACnD,iBAAiB,EAAE,YAAY,CAAC,KAAK;QACvC;IACF,GAAG,EAAE;IAEL,MAAM,sBAAsB,CAAC,MAAmC,QAAQ;QACtE,gBAAgB;QAChB,iBAAiB;QACjB,YAAY;YACV,MAAM;YACN,aAAa;YACb,SAAS;YACT,eAAe;QACjB;QACA,iBAAiB,EAAE;IACrB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY;YACV,MAAM,MAAM,IAAI;YAChB,aAAa,MAAM,WAAW,IAAI;YAClC,SAAS,MAAM,OAAO;YACtB,eAAe,MAAM,aAAa;QACpC;QACA,cAAc;YAAE;YAAO,QAAQ;QAAK;IACtC;IAEA,MAAM,oBAAoB;QACxB,cAAc;YAAE,QAAQ;QAAM;QAC9B,iBAAiB;QACjB,YAAY;YACV,MAAM;YACN,aAAa;YACb,SAAS;YACT,eAAe;QACjB;QACA,iBAAiB,EAAE;IACrB;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC,SAAS,OAAO,CAAC,IAAI,IAAI;YACrD;QACF;QAEA,IAAI;YACF,IAAI,WAAW,KAAK,EAAE;gBACpB,MAAM,aAA8C;oBAClD,MAAM,SAAS,IAAI,KAAK,WAAW,KAAK,CAAC,IAAI,GAAG,SAAS,IAAI,GAAG;oBAChE,aAAa,SAAS,WAAW,KAAK,WAAW,KAAK,CAAC,WAAW,GAAG,SAAS,WAAW,GAAG;oBAC5F,SAAS,SAAS,OAAO,KAAK,WAAW,KAAK,CAAC,OAAO,GAAG,SAAS,OAAO,GAAG;oBAC5E,eAAe,SAAS,aAAa,KAAK,WAAW,KAAK,CAAC,aAAa,GAAG,SAAS,aAAa,GAAG;gBACtG;gBACA,MAAM,aAAa,OAAO,MAAM,CAAC,YAAY,IAAI,CAAC,CAAA,QAAS,UAAU;gBACrE,IAAI,YAAY;oBACd,MAAM,eAAe,WAAW,CAAC;wBAAE,SAAS,WAAW,KAAK,CAAC,QAAQ;wBAAE,MAAM;oBAAW;gBAC1F;YACF,OAAO;gBACL,MAAM,eAAe,WAAW,CAAC;oBAAE;oBAAS,MAAM;gBAAS;YAC7D;YAEA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;QAC5D;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,eAAe,WAAW,CAAC;YACjC,iBAAiB;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;QAC9D;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,eAAe,WAAW,CAAC;gBAC/B,SAAS,MAAM,QAAQ;gBACvB,MAAM;oBAAE,WAAW,CAAC,MAAM,SAAS;gBAAC;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,MAAM,iBAAiB,OAAO,SAAe;QAC3C,IAAI;YACF,iBAAiB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAChC,EAAE,EAAE,KAAK,QAAQ;wBAAE,GAAG,CAAC;wBAAE,QAAQ;oBAAa,IAAI;YAGpD,MAAM,MAAM,IAAI,qIAAA,CAAA,UAAK;YACrB,MAAM,aAAa,MAAM,IAAI,SAAS,CAAC;YACvC,MAAM,iBAAiC,EAAE;YAEzC,KAAK,MAAM,CAAC,MAAM,KAAK,IAAI,OAAO,OAAO,CAAC,WAAW,KAAK,EAAG;gBAC3D,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,UAAU,CAAC,gBAAgB,CAAC,KAAK,QAAQ,CAAC,OAAO;oBACtE,IAAI;wBACF,MAAM,OAAO,MAAM,KAAK,KAAK,CAAC;wBAC9B,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,GAAG,MAAM;wBAC1C,MAAM,gBAAgB,IAAI,KAAK;4BAAC;yBAAK,EAAE;wBAEvC,eAAe,IAAI,CAAC;4BAClB,MAAM;4BACN,IAAI,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;4BACzC,QAAQ;4BACR,WAAW;4BACX,aAAa;4BACb,cAAc;wBAChB;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,IAAI,CAAC,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC,EAAE;oBAC7C;gBACF;YACF;YAEA,iBAAiB,CAAA,OAAQ;uBACpB,KAAK,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,QAAQ;4BAAE,GAAG,CAAC;4BAAE,QAAQ;wBAAmB,IAAI;uBACtE;iBACJ;YAED,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,UAAU,EAAE,eAAe,MAAM,CAAC,YAAY,EAAE,QAAQ,IAAI,EAAE;QAC/E,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,iBAAiB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAChC,EAAE,EAAE,KAAK,QAAQ;wBACf,GAAG,CAAC;wBACJ,QAAQ;wBACR,OAAO;oBACT,IAAI;YAEN,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAElC,MAAM,WAA2B,EAAE;QAEnC,KAAK,MAAM,QAAQ,MAAM,IAAI,CAAC,OAAQ;YACpC,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YACpD,MAAM,eAA6B;gBACjC;gBACA,IAAI;gBACJ,QAAQ;YACV;YAEA,SAAS,IAAI,CAAC;YACd,IAAI,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS;gBAC5C,WAAW,IAAM,eAAe,MAAM,SAAS;YACjD;QACF;QAEA,iBAAiB,CAAA,OAAQ;mBAAI;mBAAS;aAAS;QAC/C,IAAI,CAAC,eAAe;YAClB,gBAAgB;YAChB,iBAAiB;QACnB;IACF;IAEA,MAAM,cAAc;QAClB,MAAM,gBAAgB,cAAc,MAAM,CAAC,CAAA,IACzC,EAAE,MAAM,KAAK,aACb,CAAC,EAAE,SAAS,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,OAAO;QAE7D,KAAK,MAAM,gBAAgB,cAAe;YACxC,IAAI;gBACF,iBAAiB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAChC,EAAE,EAAE,KAAK,aAAa,EAAE,GAAG;4BAAE,GAAG,CAAC;4BAAE,QAAQ;wBAAqB,IAAI;gBAGtE,MAAM,eAAe,WAAW,CAAC;oBAAE;oBAAS,MAAM,aAAa,IAAI;gBAAC;gBAEpE,iBAAiB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAChC,EAAE,EAAE,KAAK,aAAa,EAAE,GAAG;4BAAE,GAAG,CAAC;4BAAE,QAAQ;wBAAmB,IAAI;YAEtE,EAAE,OAAO,OAAO;gBACd,iBAAiB,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAChC,EAAE,EAAE,KAAK,aAAa,EAAE,GAAG;4BACzB,GAAG,CAAC;4BACJ,QAAQ;4BACR,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;wBAClD,IAAI;YAER;QACF;QAEA,WAAW;YACT,MAAM,cAAc,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,EAAE,SAAS;YACxG,IAAI,YAAY,KAAK,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY;gBAClD;YACF;QACF,GAAG;IACL;IAEA,MAAM,aAAa,CAAC;QAClB,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACrD;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAO,sBAAsB,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK,YAAY,qBAAqB,CAAC,EAAE;IACnG;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBACH,OAAO,2NAAA,CAAA,cAAW;YACpB,KAAK;gBACH,OAAO,4MAAA,CAAA,UAAO;YAChB,KAAK;gBACH,OAAO,gNAAA,CAAA,YAAS;YAClB;gBACE,OAAO,oMAAA,CAAA,QAAK;QAChB;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,WAAW;QACb,qBAAO,8OAAC;;;;;IACV;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;wBAAE,WAAU;kCAAyC;;;;;;;;;;;;;;;;;IAI9D;IAEA,MAAM,UAAU,eAAe,WAAW,EAAE;IAC5C,MAAM,iBAAiB,oBAAoB,QAAQ,EAAE;IACrD,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA,QACrC,MAAM,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACzD,MAAM,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC3D,MAAM,WAAW,IAAI,MAAM,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;IAGxF,qBACE,8OAAC;QACC,WAAU;QACV,aAAa;QACb,aAAa;QACb,YAAY;QACZ,QAAQ;;YAEP,4BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BAAE,WAAU;sCAAkC;;;;;;sCAC/C,8OAAC;4BAAE,WAAU;sCAAiD;;;;;;;;;;;;;;;;;0BAMpE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;;;;;;;kCAGd,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM;wBAAuB,WAAU;;0CACtD,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;YAI/B,QAAQ,MAAM,KAAK,kBAClB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;kCAEjB,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAE,WAAU;;4BAA8C;0CACxB,8OAAC;gCAAK,WAAU;0CAAe;;;;;;4BAAiB;;;;;;;;;;;;qCAKrF,8OAAC;gBAAI,WAAU;0BACZ,gBAAgB,MAAM,KAAK,kBAC1B,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;2BAG/C,gBAAgB,GAAG,CAAC,CAAC;oBACnB,MAAM,gBAAgB,sBAAsB,MAAM,aAAa;oBAC/D,MAAM,cAAc,cAAc,IAAI;oBACtC,MAAM,aAAa,cAAc,MAAM,WAAW,IAAI,UAAU,MAAM,eAAe,EAAE;oBAEvF,qBACE,8OAAC,gIAAA,CAAA,OAAI;wBAEH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4BACA,MAAM,SAAS,GACX,YACA;kCAGN,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAW,WAAU;;;;;;kEACtB,8OAAC;wDAAG,WAAU;kEAAwB,MAAM,IAAI;;;;;;oDAC/C,CAAC,MAAM,SAAS,kBACf,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;;0EACjC,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;oDAItC,MAAM,WAAW,IAAI,MAAM,WAAW,KAAK,0BAC1C,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAChC,MAAM,WAAW,KAAK,aAAa,QACnC,MAAM,WAAW,KAAK,kBAAkB,QAAQ;;;;;;;;;;;;4CAItD,MAAM,WAAW,kBAChB,8OAAC;gDAAE,WAAU;0DACV,MAAM,WAAW;;;;;;0DAGtB,8OAAC;gDAAE,WAAU;0DACV,MAAM,OAAO;;;;;;0DAEhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB,cAAc,KAAK;;kFACzE,8OAAC;wEAAY,WAAU;;;;;;oEACtB,cAAc,KAAK;;;;;;;0EAEtB,8OAAC;gEAAK,WAAU;;kFACd,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAChB,IAAI,KAAK,MAAM,UAAU,EAAE,kBAAkB;;;;;;;4DAE/C,MAAM,SAAS,kBACd,8OAAC;gEAAK,WAAU;;oEACb,CAAC,MAAM,SAAS,GAAG,IAAI,EAAE,OAAO,CAAC;oEAAG;;;;;;;;;;;;;oDAI1C,MAAM,cAAc,kBACnB,8OAAC;wDAAK,WAAU;;4DAAgC;4DAC5C,MAAM,cAAc,CAAC,cAAc;4DAAG;;;;;;;;;;;;;;;;;;;kDAKhD,8OAAC,4IAAA,CAAA,eAAY;;0DACX,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAO;0DAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;8DAEV,cAAA,8OAAC,0NAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;0DAG5B,8OAAC,4IAAA,CAAA,sBAAmB;gDAAC,OAAM;gDAAM,WAAU;;kEACzC,8OAAC,4IAAA,CAAA,mBAAgB;wDAAC,SAAS,IAAM,qBAAqB;;0EACpD,8OAAC,kMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAAY;;;;;;;kEAG/B,8OAAC,4IAAA,CAAA,mBAAgB;wDAAC,SAAS,IAAM,mBAAmB;kEACjD,MAAM,SAAS,iBACd;;8EACE,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAY;;yFAIhC;;8EACE,8OAAC,gMAAA,CAAA,MAAG;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;kEAKjC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kEACtB,8OAAC,4IAAA,CAAA,mBAAgB;wDACf,SAAS,IAAM,iBAAiB,MAAM,QAAQ;wDAC9C,WAAU;;0EAEV,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA3FpD,MAAM,QAAQ;;;;;gBAoGzB;;;;;;YAML,eAAe,MAAM,GAAG,mBACvB,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;kCAIrC,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACpB,eAAe,GAAG,CAAC,CAAC;4BACnB,MAAM,aAAa,iBAAiB,IAAI,MAAM;4BAC9C,MAAM,cAAc,kBAAkB,IAAI,MAAM;4BAEhD,qBACE,8OAAC;gCAAqB,WAAU;;kDAC9B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAW,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,WAAW,aAAa,IAAI,MAAM,KAAK,gBAAgB;;;;;;0DACjF,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEACV,IAAI,QAAQ,KAAK,gBAAgB,gBACjC,IAAI,QAAQ,KAAK,cAAc,mBAAmB;;;;;;kEAErD,8OAAC;wDAAE,WAAU;kEACV,IAAI,WAAW,CAAC,QAAQ,IAAI,IAAI,WAAW,CAAC,OAAO,IAAI;;;;;;;;;;;;;;;;;;kDAI9D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAS,IAAI,MAAM,KAAK,cAAc,YAChC,IAAI,MAAM,KAAK,WAAW,gBAAgB;gDAAa,WAAU;0DAC3E,IAAI,MAAM;;;;;;4CAEZ,IAAI,MAAM,KAAK,6BACd,8OAAC;gDAAE,WAAU;;oDACV,IAAI,eAAe;oDAAC;;;;;;;;;;;;;;+BApBnB,IAAI,MAAM;;;;;wBA0BxB;;;;;;;;;;;;0BAIN,8OAAC;gBACC,KAAK;gBACL,MAAK;gBACL,QAAQ;gBACR,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;gBAChD,WAAU;gBACV,QAAO;;;;;;0BAET,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAe,cAAc;0BACzC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;sCACtB,cAAA,8OAAC,kIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAA0B;oCAC5B;;;;;;;;;;;;sCAItB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,OAAO;gCAAc,eAAe,CAAC,QAAU,gBAAgB;gCAAe,WAAU;;kDAC5F,8OAAC,gIAAA,CAAA,WAAQ;wCAAC,WAAU;;0DAClB,8OAAC,gIAAA,CAAA,cAAW;gDAAC,OAAM;gDAAS,WAAU;;kEACpC,8OAAC,4MAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAY;;;;;;;0DAGjC,8OAAC,gIAAA,CAAA,cAAW;gDAAC,OAAM;gDAAQ,WAAU;;kEACnC,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAY;oDAE7B,cAAc,MAAM,GAAG,mBACtB,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAChC,cAAc,MAAM;;;;;;;;;;;;;;;;;;kDAM7B,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAS,WAAU;kDACpC,cAAA,8OAAC;4CAAK,UAAU;4CAAc,WAAU;;8DACtC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAO,WAAU;sEAAsB;;;;;;sEACtD,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,SAAS,IAAI;4DACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DACvE,aAAY;4DACZ,QAAQ;;;;;;;;;;;;8DAIZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAgB,WAAU;sEAAsB;;;;;;sEAC/D,8OAAC,kIAAA,CAAA,SAAM;4DACL,OAAO,SAAS,aAAa;4DAC7B,eAAe,CAAC,QACd,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,eAAe;oEAAM,CAAC;;8EAGxD,8OAAC,kIAAA,CAAA,gBAAa;8EACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;8EAEd,8OAAC,kIAAA,CAAA,gBAAa;8EACX,sBAAsB,GAAG,CAAC,CAAC;wEAC1B,MAAM,OAAO,OAAO,IAAI;wEACxB,qBACE,8OAAC,kIAAA,CAAA,aAAU;4EAAoB,OAAO,OAAO,KAAK;sFAChD,cAAA,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAK,WAAU;;;;;;kGAChB,8OAAC;kGAAM,OAAO,KAAK;;;;;;;;;;;;2EAHN,OAAO,KAAK;;;;;oEAOjC;;;;;;;;;;;;;;;;;;8DAKN,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAc,WAAU;sEAAsB;;;;;;sEAC7D,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,SAAS,WAAW;4DAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAC9E,aAAY;;;;;;;;;;;;8DAIhB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAAsB;;;;;;sEACzD,8OAAC,oIAAA,CAAA,WAAQ;4DACP,IAAG;4DACH,OAAO,SAAS,OAAO;4DACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAC1E,aAAa,CAAC,qCAAqC,EAAE,UAAU,eAAe,CAAC;4DAC/E,WAAU;4DACV,QAAQ;;;;;;sEAEV,8OAAC;4DAAI,WAAU;;gEAAgC;gEAC9B,KAAK,IAAI,CAAC,SAAS,OAAO,CAAC,MAAM,GAAG,GAAG,cAAc;gEAAG;;;;;;;;;;;;;8DAI3E,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DAAC,MAAK;4DAAS,SAAQ;4DAAU,SAAS;sEAAmB;;;;;;sEAGpE,8OAAC,kIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,UAAU,CAAC,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC,SAAS,OAAO,CAAC,IAAI,MAAM,eAAe,SAAS;4DACvF,WAAU;;gEAET,eAAe,SAAS,iBACvB,8OAAC,iNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;yFAEnB,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAChB;;;;;;;;;;;;;;;;;;;;;;;;kDAOV,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAQ,WAAU;kDACnC,cAAA,8OAAC;4CAAI,WAAU;;gDACZ,cAAc,MAAM,KAAK,mBACxB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAG,WAAU;sEAA2B;;;;;;sEACzC,8OAAC;4DAAE,WAAU;;gEAAqC;8EACJ,8OAAC;;;;;gEAAK;;;;;;;sEAGpD,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS,IAAM,aAAa,OAAO,EAAE;4DACrC,SAAQ;4DACR,WAAU;;8EAEV,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAAY;;;;;;;;;;;;;gDAKnC,cAAc,MAAM,GAAG,mBACtB,8OAAC;oDAAI,WAAU;;wDACZ,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,CAAC,CAAC;4DAC1F,MAAM,iBAAiB,cAAc,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,QAAQ,EAAE;4DAC7E,qBACE,8OAAC;gEAAqB,WAAU;0EAC7B,eAAe,MAAM,GAAG,mBACvB,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;;gFAAiD;gFAC1C,eAAe,MAAM;gFAAC;;;;;;;sFAE1C,8OAAC;4EAAI,WAAU;sFACZ,eAAe,GAAG,CAAC,CAAC;gFACnB,MAAM,oBAAoB,gBAAgB,cAAc,IAAI,CAAC,IAAI;gFACjE,MAAM,YAAY,iBAAiB,cAAc,IAAI,CAAC,IAAI;gFAC1D,qBACE,8OAAC;oFAA2B,WAAU;8FACpC,cAAA,8OAAC;wFAAI,WAAU;;0GACb,8OAAC;gGAAkB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,WAAW;;;;;;0GAC5C,8OAAC;gGAAI,WAAU;;kHACb,8OAAC;wGAAE,WAAU;wGAA+B,OAAO,cAAc,IAAI,CAAC,IAAI;kHACvE,cAAc,IAAI,CAAC,IAAI;;;;;;kHAE1B,8OAAC;wGAAE,WAAU;;4GACV,CAAC,cAAc,IAAI,CAAC,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC;4GAAG;;;;;;;;;;;;;0GAGjD,8OAAC;gGAAI,WAAU;;oGACZ,cAAc,MAAM,KAAK,6BACxB,8OAAC,iNAAA,CAAA,UAAO;wGAAC,WAAU;;;;;;oGAEpB,cAAc,MAAM,KAAK,2BACxB,8OAAC,2NAAA,CAAA,cAAW;wGAAC,WAAU;;;;;;oGAExB,cAAc,MAAM,KAAK,yBACxB,8OAAC,4MAAA,CAAA,UAAO;wGAAC,WAAU;;;;;;oGAEpB,cAAc,MAAM,KAAK,2BACxB,8OAAC,kIAAA,CAAA,SAAM;wGACL,SAAQ;wGACR,MAAK;wGACL,SAAS,IAAM,WAAW,cAAc,EAAE;wGAC1C,WAAU;kHAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4GAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;mFA5Bb,cAAc,EAAE;;;;;4EAmC9B;;;;;;;;;;;;+DA9CE,QAAQ,EAAE;;;;;wDAoDxB;wDACC,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,SAAS,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,MAAM,GAAG,mBAC/F,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAE,WAAU;;wEAA4C;wEACpC,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,SAAS,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,MAAM;wEAAC;;;;;;;8EAEnH,8OAAC;oEAAI,WAAU;8EACZ,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,SAAS,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SAAS,GAAG,CAAC,CAAC;wEAC3F,MAAM,eAAe,gBAAgB,aAAa,IAAI,CAAC,IAAI;wEAC3D,MAAM,YAAY,iBAAiB,aAAa,IAAI,CAAC,IAAI;wEACzD,qBACE,8OAAC;4EAA0B,WAAU;sFACnC,cAAA,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAa,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,WAAW;;;;;;kGACvC,8OAAC;wFAAI,WAAU;;0GACb,8OAAC;gGAAE,WAAU;gGAA+B,OAAO,aAAa,IAAI,CAAC,IAAI;0GACtE,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,IAAI,CAAC,IAAI,EAAE;;;;;;0GAE1C,8OAAC;gGAAE,WAAU;;oGACV,CAAC,aAAa,IAAI,CAAC,IAAI,GAAG,IAAI,EAAE,OAAO,CAAC;oGAAG;;;;;;;;;;;;;kGAGhD,8OAAC;wFAAI,WAAU;;4FACZ,aAAa,MAAM,KAAK,6BACvB,8OAAC,iNAAA,CAAA,UAAO;gGAAC,WAAU;;;;;;4FAEpB,aAAa,MAAM,KAAK,2BACvB,8OAAC,2NAAA,CAAA,cAAW;gGAAC,WAAU;;;;;;4FAExB,aAAa,MAAM,KAAK,yBACvB,8OAAC,4MAAA,CAAA,UAAO;gGAAC,WAAU;;;;;;4FAEpB,aAAa,MAAM,KAAK,2BACvB,8OAAC,kIAAA,CAAA,SAAM;gGACL,SAAQ;gGACR,MAAK;gGACL,SAAS,IAAM,WAAW,aAAa,EAAE;gGACzC,WAAU;0GAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oGAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;2EA5Bb,aAAa,EAAE;;;;;oEAmC7B;;;;;;;;;;;;;;;;;;gDAOT,cAAc,MAAM,GAAG,mBACtB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DAAC,MAAK;4DAAS,SAAQ;4DAAU,SAAS;sEAAmB;;;;;;sEAGpE,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS;4DACT,UAAU,eAAe,SAAS,IAAI,cAAc,MAAM,CAAC,CAAA,IACzD,EAAE,MAAM,KAAK,aACb,CAAC,EAAE,SAAS,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,OAAO,GAC3D,MAAM,KAAK;4DACb,WAAU;;gEAET,eAAe,SAAS,iBACvB,8OAAC,iNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;yFAEnB,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;gEAClB;gEACa,cAAc,MAAM,CAAC,CAAA,IAClC,EAAE,MAAM,KAAK,aACb,CAAC,EAAE,SAAS,IAAI,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,OAAO,GAC3D,MAAM;gEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU3B,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM,WAAW,MAAM;gBAAE,cAAc;0BAC7C,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;sCACtB,cAAA,8OAAC,kIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,kMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAA0B;;;;;;;;;;;;sCAK/C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;0DAAsB;;;;;;0DAC3D,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDACvE,aAAY;gDACZ,QAAQ;;;;;;;;;;;;kDAIZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAqB,WAAU;0DAAsB;;;;;;0DACpE,8OAAC,kIAAA,CAAA,SAAM;gDACL,OAAO,SAAS,aAAa;gDAC7B,eAAe,CAAC,QACd,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,eAAe;wDAAM,CAAC;;kEAGxD,8OAAC,kIAAA,CAAA,gBAAa;kEACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;;;;;;;;;;kEAEd,8OAAC,kIAAA,CAAA,gBAAa;kEACX,sBAAsB,GAAG,CAAC,CAAC;4DAC1B,MAAM,OAAO,OAAO,IAAI;4DACxB,qBACE,8OAAC,kIAAA,CAAA,aAAU;gEAAoB,OAAO,OAAO,KAAK;0EAChD,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;;;;;;sFAChB,8OAAC;sFAAM,OAAO,KAAK;;;;;;;;;;;;+DAHN,OAAO,KAAK;;;;;wDAOjC;;;;;;;;;;;;;;;;;;kDAKN,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAmB,WAAU;0DAAsB;;;;;;0DAClE,8OAAC,iIAAA,CAAA,QAAK;gDACJ,IAAG;gDACH,OAAO,SAAS,WAAW;gDAC3B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,aAAa,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC9E,aAAY;;;;;;;;;;;;kDAIhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAe,WAAU;0DAAsB;;;;;;0DAC9D,8OAAC,oIAAA,CAAA,WAAQ;gDACP,IAAG;gDACH,OAAO,SAAS,OAAO;gDACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,SAAS,EAAE,MAAM,CAAC,KAAK;wDAAC,CAAC;gDAC1E,aAAa,CAAC,qCAAqC,EAAE,UAAU,eAAe,CAAC;gDAC/E,WAAU;gDACV,QAAQ;;;;;;0DAEV,8OAAC;gDAAI,WAAU;;oDAAgC;oDAC9B,KAAK,IAAI,CAAC,SAAS,OAAO,CAAC,MAAM,GAAG,GAAG,cAAc;oDAAG;;;;;;;;;;;;;kDAI3E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAS,SAAQ;gDAAU,SAAS;0DAAmB;;;;;;0DAGpE,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,UAAU,CAAC,SAAS,IAAI,CAAC,IAAI,MAAM,CAAC,SAAS,OAAO,CAAC,IAAI,MAAM,eAAe,SAAS;gDACvF,WAAU;;oDAET,eAAe,SAAS,iBACvB,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;6EAEnB,8OAAC,kMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDACjB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQd,8OAAC,2IAAA,CAAA,cAAW;gBAAC,MAAM,CAAC,CAAC;gBAAe,cAAc,IAAM,iBAAiB;0BACvE,cAAA,8OAAC,2IAAA,CAAA,qBAAkB;;sCACjB,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,mBAAgB;oCAAC,WAAU;;sDAC1B,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;wCAA6B;;;;;;;8CAGtD,8OAAC,2IAAA,CAAA,yBAAsB;;wCAAC;wCAC8B;wCAAU;;;;;;;;;;;;;sCAGlE,8OAAC,2IAAA,CAAA,oBAAiB;;8CAChB,8OAAC,2IAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,8OAAC,2IAAA,CAAA,oBAAiB;oCAChB,SAAS,IAAM,iBAAiB,aAAa;oCAC7C,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 6464, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/agent-tools-configuration.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Search, Settings2 } from 'lucide-react';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Switch } from '@/components/ui/switch';\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { DEFAULT_AGENTPRESS_TOOLS, getToolDisplayName } from './tools';\r\n\r\ninterface AgentToolsConfigurationProps {\r\n  tools: Record<string, { enabled: boolean; description: string }>;\r\n  onToolsChange: (tools: Record<string, { enabled: boolean; description: string }>) => void;\r\n}\r\n\r\nexport const AgentToolsConfiguration = ({ tools, onToolsChange }: AgentToolsConfigurationProps) => {\r\n  const [searchQuery, setSearchQuery] = useState<string>('');\r\n\r\n  const handleToolToggle = (toolName: string, enabled: boolean) => {\r\n    const updatedTools = {\r\n      ...tools,\r\n      [toolName]: {\r\n        ...tools[toolName],\r\n        enabled\r\n      }\r\n    };\r\n    onToolsChange(updatedTools);\r\n  };\r\n\r\n  const getSelectedToolsCount = (): number => {\r\n    return Object.values(tools).filter(tool => tool.enabled).length;\r\n  };\r\n\r\n  const getFilteredTools = (): Array<[string, any]> => {\r\n    let toolEntries = Object.entries(DEFAULT_AGENTPRESS_TOOLS);\r\n    \r\n    if (searchQuery) {\r\n      toolEntries = toolEntries.filter(([toolName, toolInfo]) => \r\n        getToolDisplayName(toolName).toLowerCase().includes(searchQuery.toLowerCase()) ||\r\n        toolInfo.description.toLowerCase().includes(searchQuery.toLowerCase())\r\n      );\r\n    }\r\n    \r\n    return toolEntries;\r\n  };\r\n\r\n  return (\r\n    <div className=\"h-full flex flex-col\">\r\n      <div className=\"flex-shrink-0 mb-4\">\r\n        <div className=\"flex items-center justify-between mb-4\">\r\n          <span className=\"text-sm text-muted-foreground\">\r\n            {getSelectedToolsCount()} selected\r\n          </span>\r\n        </div>\r\n        <div className=\"relative\">\r\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\r\n          <Input\r\n            placeholder=\"Search tools...\"\r\n            value={searchQuery}\r\n            onChange={(e) => setSearchQuery(e.target.value)}\r\n            className=\"pl-10\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"flex-1 overflow-y-auto\">\r\n        <div className=\"gap-4 grid grid-cols-1 md:grid-cols-2\">\r\n          {getFilteredTools().map(([toolName, toolInfo]) => (\r\n            <div \r\n              key={toolName} \r\n              className=\"flex items-center gap-3 p-3 bg-muted/50 rounded-lg border hover:border-border/80 transition-colors\"\r\n            >\r\n              <div className={`w-10 h-10 rounded-lg ${toolInfo.color} flex items-center justify-center flex-shrink-0`}>\r\n                <span className=\"text-lg\">{toolInfo.icon}</span>\r\n              </div>\r\n              <div className=\"flex-1 min-w-0\">\r\n                <div className=\"flex items-center justify-between mb-1\">\r\n                  <h4 className=\"font-medium text-sm\">\r\n                    {getToolDisplayName(toolName)}\r\n                  </h4>\r\n                  <Switch\r\n                    checked={tools[toolName]?.enabled || false}\r\n                    onCheckedChange={(checked) => handleToolToggle(toolName, checked)}\r\n                    className=\"flex-shrink-0\"\r\n                  />\r\n                </div>\r\n                <p className=\"text-xs text-muted-foreground leading-relaxed\">\r\n                  {toolInfo.description}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {getFilteredTools().length === 0 && (\r\n          <div className=\"text-center py-8\">\r\n            <div className=\"text-4xl mb-3\">🔍</div>\r\n            <h3 className=\"text-sm font-medium mb-1\">No tools found</h3>\r\n            <p className=\"text-xs text-muted-foreground\">Try adjusting your search criteria</p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}; "], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAEA;;;;;;;AAOO,MAAM,0BAA0B,CAAC,EAAE,KAAK,EAAE,aAAa,EAAgC;IAC5F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEvD,MAAM,mBAAmB,CAAC,UAAkB;QAC1C,MAAM,eAAe;YACnB,GAAG,KAAK;YACR,CAAC,SAAS,EAAE;gBACV,GAAG,KAAK,CAAC,SAAS;gBAClB;YACF;QACF;QACA,cAAc;IAChB;IAEA,MAAM,wBAAwB;QAC5B,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,EAAE,MAAM;IACjE;IAEA,MAAM,mBAAmB;QACvB,IAAI,cAAc,OAAO,OAAO,CAAC,oIAAA,CAAA,2BAAwB;QAEzD,IAAI,aAAa;YACf,cAAc,YAAY,MAAM,CAAC,CAAC,CAAC,UAAU,SAAS,GACpD,CAAA,GAAA,oIAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC3E,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAEvE;QAEA,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;;gCACb;gCAAwB;;;;;;;;;;;;kCAG7B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;;;;;;;;;;;;;0BAKhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,mBAAmB,GAAG,CAAC,CAAC,CAAC,UAAU,SAAS,iBAC3C,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAI,WAAW,CAAC,qBAAqB,EAAE,SAAS,KAAK,CAAC,+CAA+C,CAAC;kDACrG,cAAA,8OAAC;4CAAK,WAAU;sDAAW,SAAS,IAAI;;;;;;;;;;;kDAE1C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,CAAA,GAAA,oIAAA,CAAA,qBAAkB,AAAD,EAAE;;;;;;kEAEtB,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAS,KAAK,CAAC,SAAS,EAAE,WAAW;wDACrC,iBAAiB,CAAC,UAAY,iBAAiB,UAAU;wDACzD,WAAU;;;;;;;;;;;;0DAGd,8OAAC;gDAAE,WAAU;0DACV,SAAS,WAAW;;;;;;;;;;;;;+BAlBpB;;;;;;;;;;oBAyBV,mBAAmB,MAAM,KAAK,mBAC7B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,8OAAC;gCAAG,WAAU;0CAA2B;;;;;;0CACzC,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;;;;;;;;;;;;;AAMzD", "debugId": null}}, {"offset": {"line": 6686, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/agent-config-modal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';\r\nimport { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';\r\nimport { <PERSON><PERSON> } from '@/components/ui/button';\r\nimport { Settings2, Brain, Database, Zap, Workflow, Bot } from 'lucide-react';\r\nimport { AgentMCPConfiguration } from './agent-mcp-configuration';\r\nimport { AgentTriggersConfiguration } from './triggers/agent-triggers-configuration';\r\nimport { AgentWorkflowsConfiguration } from './workflows/agent-workflows-configuration';\r\nimport { AgentKnowledgeBaseManager } from './knowledge-base/agent-knowledge-base-manager';\r\nimport { AgentToolsConfiguration } from './agent-tools-configuration';\r\nimport { AgentSelector } from '../thread/chat-input/agent-selector';\r\nimport { useAgent, useUpdateAgent } from '@/hooks/react-query/agents/use-agents';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Textarea } from '@/components/ui/textarea';\r\nimport { Label } from '@/components/ui/label';\r\nimport { toast } from 'sonner';\r\nimport { useRouter } from 'next/navigation';\r\n\r\ninterface AgentConfigModalProps {\r\n  isOpen: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  selectedAgentId?: string;\r\n  onAgentSelect?: (agentId: string | undefined) => void;\r\n  initialTab?: string;\r\n}\r\n\r\nexport const AgentConfigModal: React.FC<AgentConfigModalProps> = ({\r\n  isOpen,\r\n  onOpenChange,\r\n  selectedAgentId,\r\n  onAgentSelect,\r\n  initialTab = 'tools'\r\n}) => {\r\n  const [activeTab, setActiveTab] = useState(initialTab);\r\n  const [editingInstructions, setEditingInstructions] = useState(false);\r\n  const [instructionsValue, setInstructionsValue] = useState('');\r\n  const [agentName, setAgentName] = useState('');\r\n  const [agentDescription, setAgentDescription] = useState('');\r\n  \r\n  const { data: agent, isLoading } = useAgent(selectedAgentId || '');\r\n  const updateAgentMutation = useUpdateAgent();\r\n  const router = useRouter();\r\n\r\n  // Update active tab when initialTab changes or modal opens\r\n  React.useEffect(() => {\r\n    if (isOpen && initialTab) {\r\n      setActiveTab(initialTab);\r\n    }\r\n  }, [initialTab, isOpen]);\r\n\r\n  // Update local state when agent data changes\r\n  React.useEffect(() => {\r\n    if (agent) {\r\n      setAgentName(agent.name || '');\r\n      setAgentDescription(agent.description || '');\r\n      setInstructionsValue(agent.system_prompt || '');\r\n    }\r\n  }, [agent]);\r\n\r\n  const handleSaveInstructions = async () => {\r\n    if (!selectedAgentId) return;\r\n    \r\n    try {\r\n      await updateAgentMutation.mutateAsync({\r\n        agentId: selectedAgentId,\r\n        name: agentName,\r\n        description: agentDescription,\r\n        system_prompt: instructionsValue\r\n      });\r\n      toast.success('Agent updated successfully');\r\n      setEditingInstructions(false);\r\n    } catch (error) {\r\n      toast.error('Failed to update agent');\r\n    }\r\n  };\r\n\r\n  const handleToolsChange = async (tools: Record<string, { enabled: boolean; description: string }>) => {\r\n    if (!selectedAgentId) return;\r\n    \r\n    try {\r\n      await updateAgentMutation.mutateAsync({\r\n        agentId: selectedAgentId,\r\n        agentpress_tools: tools\r\n      });\r\n      toast.success('Tools updated successfully');\r\n    } catch (error) {\r\n      toast.error('Failed to update tools');\r\n    }\r\n  };\r\n\r\n  const handleMCPChange = async (mcps: any) => {\r\n    if (!selectedAgentId) return;\r\n    \r\n    try {\r\n      await updateAgentMutation.mutateAsync({\r\n        agentId: selectedAgentId,\r\n        configured_mcps: mcps.configured_mcps || [],\r\n        custom_mcps: mcps.custom_mcps || []\r\n      });\r\n      toast.success('Integrations updated successfully');\r\n    } catch (error) {\r\n      toast.error('Failed to update integrations');\r\n    }\r\n  };\r\n\r\n  const displayName = agent?.name || 'Suna';\r\n\r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"max-w-4xl h-[85vh] p-0 flex flex-col\">\r\n        <DialogHeader className=\"flex-shrink-0 border-b px-6 py-4\">\r\n          <DialogTitle className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center gap-2\">\r\n              <Bot className=\"h-4 w-4\" />\r\n              Agent Configuration\r\n            </div>\r\n            {selectedAgentId && (\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"sm\"\r\n                onClick={() => router.push(`/agents/config/${selectedAgentId}`)}\r\n                className=\"text-xs\"\r\n              >\r\n                <Settings2 className=\"h-3 w-3 mr-1\" />\r\n                Advanced\r\n              </Button>\r\n            )}\r\n          </DialogTitle>\r\n        </DialogHeader>\r\n        \r\n        <div className=\"flex-shrink-0 px-6 py-3 border-b\">\r\n          <AgentSelector\r\n            selectedAgentId={selectedAgentId}\r\n            onAgentSelect={onAgentSelect}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"flex-1 min-h-0 px-6 py-4\">\r\n          <Tabs value={activeTab} onValueChange={setActiveTab} className=\"h-full flex flex-col\">\r\n            <TabsList className=\"grid w-full grid-cols-5 flex-shrink-0 h-9 mb-4\">\r\n              <TabsTrigger value=\"tools\" className=\"text-xs\">\r\n                <Settings2 className=\"h-3 w-3 mr-1\" />\r\n                Tools\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"instructions\" className=\"text-xs\">\r\n                <Brain className=\"h-3 w-3 mr-1\" />\r\n                Instructions\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"knowledge\" className=\"text-xs\">\r\n                <Database className=\"h-3 w-3 mr-1\" />\r\n                Knowledge\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"triggers\" className=\"text-xs\">\r\n                <Zap className=\"h-3 w-3 mr-1\" />\r\n                Triggers\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"workflows\" className=\"text-xs\">\r\n                <Workflow className=\"h-3 w-3 mr-1\" />\r\n                Workflows\r\n              </TabsTrigger>\r\n            </TabsList>\r\n\r\n            <TabsContent value=\"tools\" className=\"flex-1 m-0 mt-0 overflow-y-auto overflow-hidden\">\r\n              <div className=\"h-full\">\r\n                {selectedAgentId ? (\r\n                  <AgentToolsConfiguration\r\n                    tools={agent?.agentpress_tools || {}}\r\n                    onToolsChange={handleToolsChange}\r\n                  />\r\n                ) : (\r\n                  <div className=\"flex items-center justify-center h-32\">\r\n                    <p className=\"text-sm text-muted-foreground\">Select an agent to configure tools</p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </TabsContent>\r\n            \r\n            <TabsContent value=\"instructions\" className=\"flex-1 m-0 mt-0 overflow-y-auto overflow-hidden\">\r\n              <div className=\"h-full flex flex-col\">\r\n                {selectedAgentId ? (\r\n                  <>\r\n                    <div className=\"grid grid-cols-2 gap-4 mb-4\">\r\n                      <div className=\"space-y-2\">\r\n                        <Label htmlFor=\"agent-name\" className=\"text-sm\">Name</Label>\r\n                        <Input\r\n                          id=\"agent-name\"\r\n                          value={agentName}\r\n                          onChange={(e) => setAgentName(e.target.value)}\r\n                          placeholder=\"Agent name\"\r\n                          className=\"h-8\"\r\n                        />\r\n                      </div>\r\n                      <div className=\"space-y-2\">\r\n                        <Label htmlFor=\"agent-description\" className=\"text-sm\">Description</Label>\r\n                        <Input\r\n                          id=\"agent-description\"\r\n                          value={agentDescription}\r\n                          onChange={(e) => setAgentDescription(e.target.value)}\r\n                          placeholder=\"Brief description\"\r\n                          className=\"h-8\"\r\n                        />\r\n                      </div>\r\n                    </div>\r\n                    \r\n                    <div className=\"space-y-2 flex-1 flex flex-col\">\r\n                      <Label htmlFor=\"system-instructions\" className=\"text-sm\">System Instructions</Label>\r\n                      <Textarea\r\n                        id=\"system-instructions\"\r\n                        value={instructionsValue}\r\n                        onChange={(e) => setInstructionsValue(e.target.value)}\r\n                        placeholder=\"Define the agent's role, behavior, and expertise...\"\r\n                        className=\"flex-1 resize-none\"\r\n                      />\r\n                    </div>\r\n                    \r\n                    <div className=\"flex gap-2 pt-4\">\r\n                      <Button\r\n                        onClick={handleSaveInstructions}\r\n                        disabled={updateAgentMutation.isPending}\r\n                        size=\"sm\"\r\n                      >\r\n                        {updateAgentMutation.isPending ? 'Saving...' : 'Save'}\r\n                      </Button>\r\n                      <Button\r\n                        variant=\"outline\"\r\n                        size=\"sm\"\r\n                        onClick={() => {\r\n                          setAgentName(agent?.name || '');\r\n                          setAgentDescription(agent?.description || '');\r\n                          setInstructionsValue(agent?.system_prompt || '');\r\n                        }}\r\n                      >\r\n                        Reset\r\n                      </Button>\r\n                    </div>\r\n                  </>\r\n                ) : (\r\n                  <div className=\"flex items-center justify-center h-32\">\r\n                    <p className=\"text-sm text-muted-foreground\">Select an agent to configure instructions</p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </TabsContent>\r\n\r\n            <TabsContent value=\"knowledge\" className=\"flex-1 m-0 mt-0 overflow-y-auto overflow-hidden\">\r\n              <div className=\"h-full\">\r\n                {selectedAgentId ? (\r\n                  <AgentKnowledgeBaseManager\r\n                    agentId={selectedAgentId}\r\n                    agentName={agentName}\r\n                  />\r\n                ) : (\r\n                  <div className=\"flex items-center justify-center h-32\">\r\n                    <p className=\"text-sm text-muted-foreground\">Select an agent to manage knowledge base</p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </TabsContent>\r\n\r\n            <TabsContent value=\"triggers\" className=\"flex-1 m-0 mt-0 overflow-y-auto overflow-hidden\">\r\n              <div className=\"h-full\">\r\n                {selectedAgentId ? (\r\n                  <AgentTriggersConfiguration agentId={selectedAgentId} />\r\n                ) : (\r\n                  <div className=\"flex items-center justify-center h-32\">\r\n                    <p className=\"text-sm text-muted-foreground\">Select an agent to configure triggers</p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </TabsContent>\r\n\r\n            <TabsContent value=\"workflows\" className=\"flex-1 m-0 mt-0 overflow-y-auto overflow-hidden\">\r\n              <div className=\"h-full\">\r\n                {selectedAgentId ? (\r\n                  <AgentWorkflowsConfiguration\r\n                    agentId={selectedAgentId}\r\n                    agentName={agentName}\r\n                  />\r\n                ) : (\r\n                  <div className=\"flex items-center justify-center h-32\">\r\n                    <p className=\"text-sm text-muted-foreground\">Select an agent to configure workflows</p>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </TabsContent>\r\n          </Tabs>\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}; "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlBA;;;;;;;;;;;;;;;;;;AA4BO,MAAM,mBAAoD,CAAC,EAChE,MAAM,EACN,YAAY,EACZ,eAAe,EACf,aAAa,EACb,aAAa,OAAO,EACrB;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,MAAM,EAAE,MAAM,KAAK,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,WAAQ,AAAD,EAAE,mBAAmB;IAC/D,MAAM,sBAAsB,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD;IACzC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,2DAA2D;IAC3D,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,UAAU,YAAY;YACxB,aAAa;QACf;IACF,GAAG;QAAC;QAAY;KAAO;IAEvB,6CAA6C;IAC7C,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,OAAO;YACT,aAAa,MAAM,IAAI,IAAI;YAC3B,oBAAoB,MAAM,WAAW,IAAI;YACzC,qBAAqB,MAAM,aAAa,IAAI;QAC9C;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,yBAAyB;QAC7B,IAAI,CAAC,iBAAiB;QAEtB,IAAI;YACF,MAAM,oBAAoB,WAAW,CAAC;gBACpC,SAAS;gBACT,MAAM;gBACN,aAAa;gBACb,eAAe;YACjB;YACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,uBAAuB;QACzB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI,CAAC,iBAAiB;QAEtB,IAAI;YACF,MAAM,oBAAoB,WAAW,CAAC;gBACpC,SAAS;gBACT,kBAAkB;YACpB;YACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI,CAAC,iBAAiB;QAEtB,IAAI;YACF,MAAM,oBAAoB,WAAW,CAAC;gBACpC,SAAS;gBACT,iBAAiB,KAAK,eAAe,IAAI,EAAE;gBAC3C,aAAa,KAAK,WAAW,IAAI,EAAE;YACrC;YACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,cAAc,OAAO,QAAQ;IAEnC,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;8BACtB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAY;;;;;;;4BAG5B,iCACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,eAAe,EAAE,iBAAiB;gCAC9D,WAAU;;kDAEV,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;8BAO9C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kKAAA,CAAA,gBAAa;wBACZ,iBAAiB;wBACjB,eAAe;;;;;;;;;;;8BAInB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,OAAO;wBAAW,eAAe;wBAAc,WAAU;;0CAC7D,8OAAC,gIAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAQ,WAAU;;0DACnC,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGxC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAe,WAAU;;0DAC1C,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGpC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAY,WAAU;;0DACvC,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGvC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAW,WAAU;;0DACtC,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGlC,8OAAC,gIAAA,CAAA,cAAW;wCAAC,OAAM;wCAAY,WAAU;;0DACvC,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAKzC,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAQ,WAAU;0CACnC,cAAA,8OAAC;oCAAI,WAAU;8CACZ,gCACC,8OAAC,+JAAA,CAAA,0BAAuB;wCACtB,OAAO,OAAO,oBAAoB,CAAC;wCACnC,eAAe;;;;;6DAGjB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;0CAMrD,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAe,WAAU;0CAC1C,cAAA,8OAAC;oCAAI,WAAU;8CACZ,gCACC;;0DACE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAa,WAAU;0EAAU;;;;;;0EAChD,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,OAAO;gEACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;gEAC5C,aAAY;gEACZ,WAAU;;;;;;;;;;;;kEAGd,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAoB,WAAU;0EAAU;;;;;;0EACvD,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,OAAO;gEACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gEACnD,aAAY;gEACZ,WAAU;;;;;;;;;;;;;;;;;;0DAKhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAsB,WAAU;kEAAU;;;;;;kEACzD,8OAAC,oIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,OAAO;wDACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;wDACpD,aAAY;wDACZ,WAAU;;;;;;;;;;;;0DAId,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAS;wDACT,UAAU,oBAAoB,SAAS;wDACvC,MAAK;kEAEJ,oBAAoB,SAAS,GAAG,cAAc;;;;;;kEAEjD,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;4DACP,aAAa,OAAO,QAAQ;4DAC5B,oBAAoB,OAAO,eAAe;4DAC1C,qBAAqB,OAAO,iBAAiB;wDAC/C;kEACD;;;;;;;;;;;;;qEAML,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;0CAMrD,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAY,WAAU;0CACvC,cAAA,8OAAC;oCAAI,WAAU;8CACZ,gCACC,8OAAC,0LAAA,CAAA,4BAAyB;wCACxB,SAAS;wCACT,WAAW;;;;;6DAGb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;0CAMrD,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAW,WAAU;0CACtC,cAAA,8OAAC;oCAAI,WAAU;8CACZ,gCACC,8OAAC,8KAAA,CAAA,6BAA0B;wCAAC,SAAS;;;;;6DAErC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;0CAMrD,8OAAC,gIAAA,CAAA,cAAW;gCAAC,OAAM;gCAAY,WAAU;0CACvC,cAAA,8OAAC;oCAAI,WAAU;8CACZ,gCACC,8OAAC,gLAAA,CAAA,8BAA2B;wCAC1B,SAAS;wCACT,WAAW;;;;;6DAGb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjE", "debugId": null}}, {"offset": {"line": 7316, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/pipedream/pipedream-connector.tsx"], "sourcesContent": ["import React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Label } from '@/components/ui/label';\r\nimport { Switch } from '@/components/ui/switch';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Separator } from '@/components/ui/separator';\r\nimport { \r\n  Dialog, \r\n  DialogContent, \r\n  DialogHeader, \r\n  DialogTitle, \r\n  DialogDescription \r\n} from '@/components/ui/dialog';\r\nimport { \r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport { \r\n  Loader2, \r\n  Plus, \r\n  CheckCircle2, \r\n  Zap,\r\n  ArrowRight,\r\n  RefreshCw\r\n} from 'lucide-react';\r\nimport { toast } from 'sonner';\r\nimport { cn } from '@/lib/utils';\r\nimport { usePipedreamProfiles, useCreatePipedreamProfile, useConnectPipedreamProfile } from '@/hooks/react-query/pipedream/use-pipedream-profiles';\r\nimport { pipedreamApi } from '@/hooks/react-query/pipedream/utils';\r\nimport type { CreateProfileRequest } from '@/components/agents/pipedream/pipedream-types';\r\nimport type { PipedreamApp } from '@/hooks/react-query/pipedream/utils';\r\n\r\ninterface PipedreamConnectorProps {\r\n  app: PipedreamApp;\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  onComplete: (profileId: string, selectedTools: string[], appName: string, appSlug: string) => void;\r\n  mode?: 'full' | 'profile-only'; // Add mode prop\r\n}\r\n\r\ninterface PipedreamTool {\r\n  name: string;\r\n  description: string;\r\n}\r\n\r\nexport const PipedreamConnector: React.FC<PipedreamConnectorProps> = ({\r\n  app,\r\n  open,\r\n  onOpenChange,\r\n  onComplete,\r\n  mode = 'full'\r\n}) => {\r\n  const [step, setStep] = useState<'profile' | 'tools'>('profile');\r\n  const [selectedProfileId, setSelectedProfileId] = useState<string>('');\r\n  const [isCreatingProfile, setIsCreatingProfile] = useState(false);\r\n  const [newProfileName, setNewProfileName] = useState('');\r\n  const [selectedTools, setSelectedTools] = useState<Set<string>>(new Set());\r\n  const [tools, setTools] = useState<PipedreamTool[]>([]);\r\n  const [isLoadingTools, setIsLoadingTools] = useState(false);\r\n  const [isConnecting, setIsConnecting] = useState(false);\r\n  const [isCompletingConnection, setIsCompletingConnection] = useState(false);\r\n\r\n  const { data: profiles, refetch: refetchProfiles } = usePipedreamProfiles({ app_slug: app.name_slug });\r\n  const createProfile = useCreatePipedreamProfile();\r\n  const connectProfile = useConnectPipedreamProfile();\r\n\r\n  const connectedProfiles = useMemo(() => {\r\n    return profiles?.filter(p => p.is_connected) || [];\r\n  }, [profiles]);\r\n  \r\n  const selectedProfile = useMemo(() => {\r\n    return profiles?.find(p => p.profile_id === selectedProfileId);\r\n  }, [profiles, selectedProfileId]);\r\n\r\n  useEffect(() => {\r\n    if (open) {\r\n      setStep('profile');\r\n      setSelectedProfileId('');\r\n      setIsCreatingProfile(false);\r\n      setNewProfileName('');\r\n      setSelectedTools(new Set());\r\n      setTools([]);\r\n    }\r\n  }, [open]);\r\n\r\n  useEffect(() => {\r\n    if (open && connectedProfiles.length === 1 && !selectedProfileId) {\r\n      setSelectedProfileId(connectedProfiles[0].profile_id);\r\n    }\r\n  }, [open, connectedProfiles, selectedProfileId]);\r\n\r\n  const handleCreateProfile = useCallback(async () => {\r\n    if (!newProfileName.trim()) {\r\n      toast.error('Please enter a profile name');\r\n      return;\r\n    }\r\n\r\n    setIsConnecting(true);\r\n    try {\r\n      const request: CreateProfileRequest = {\r\n        profile_name: newProfileName.trim(),\r\n        app_slug: app.name_slug,\r\n        app_name: app.name,\r\n        is_default: connectedProfiles.length === 0,\r\n      };\r\n\r\n      const newProfile = await createProfile.mutateAsync(request);\r\n      \r\n      // Connect the profile\r\n      await connectProfile.mutateAsync({\r\n        profileId: newProfile.profile_id,\r\n        app: app.name_slug,\r\n      });\r\n\r\n      await refetchProfiles();\r\n      setSelectedProfileId(newProfile.profile_id);\r\n      setIsCreatingProfile(false);\r\n      setNewProfileName('');\r\n      toast.success('Profile created and connected successfully!');\r\n      \r\n      if (mode === 'profile-only') {\r\n        onComplete(newProfile.profile_id, [], app.name, app.name_slug);\r\n        onOpenChange(false);\r\n      } else {\r\n        proceedToTools();\r\n      }\r\n    } catch (error) {\r\n      console.error('Error creating profile:', error);\r\n    } finally {\r\n      setIsConnecting(false);\r\n    }\r\n  }, [newProfileName, app.name_slug, app.name, connectedProfiles.length, createProfile, connectProfile, refetchProfiles, mode, onComplete, onOpenChange]);\r\n\r\n  const proceedToTools = useCallback(async () => {\r\n    if (!selectedProfileId || !selectedProfile) return;\r\n\r\n    setIsLoadingTools(true);\r\n    setStep('tools');\r\n    \r\n    try {\r\n      const servers = await pipedreamApi.discoverMCPServers(selectedProfile.external_user_id, app.name_slug);\r\n      const server = servers.find(s => s.app_slug === app.name_slug);\r\n      \r\n      if (server?.available_tools) {\r\n        setTools(server.available_tools);\r\n        setSelectedTools(new Set(server.available_tools.map(tool => tool.name)));\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching tools:', error);\r\n      toast.error('Failed to load tools');\r\n    } finally {\r\n      setIsLoadingTools(false);\r\n    }\r\n  }, [selectedProfileId, selectedProfile, app.name_slug]);\r\n\r\n  const handleComplete = useCallback(async () => {\r\n    if (!selectedProfileId || selectedTools.size === 0) {\r\n      toast.error('Please select at least one tool');\r\n      return;\r\n    }\r\n    setIsCompletingConnection(true);\r\n    try {\r\n      onComplete(selectedProfileId, Array.from(selectedTools), app.name, app.name_slug);\r\n      onOpenChange(false);\r\n    } catch (error) {\r\n      console.error('Error completing connection:', error);\r\n    } finally {\r\n      setIsCompletingConnection(false);\r\n    }\r\n  }, [selectedProfileId, selectedTools, onComplete, app.name, app.name_slug, onOpenChange]);\r\n\r\n  const handleProfileOnlyComplete = useCallback(async () => {\r\n    if (!selectedProfileId) {\r\n      toast.error('Please select a profile');\r\n      return;\r\n    }\r\n\r\n    setIsCompletingConnection(true);\r\n    try {\r\n      onComplete(selectedProfileId, [], app.name, app.name_slug);\r\n      onOpenChange(false);\r\n    } catch (error) {\r\n      console.error('Error completing connection:', error);\r\n    } finally {\r\n      setIsCompletingConnection(false);\r\n    }\r\n  }, [selectedProfileId, onComplete, app.name, app.name_slug, onOpenChange]);\r\n\r\n  const handleToolToggle = useCallback((toolName: string) => {\r\n    setSelectedTools(prev => {\r\n      const newSelected = new Set(prev);\r\n      if (newSelected.has(toolName)) {\r\n        newSelected.delete(toolName);\r\n      } else {\r\n        newSelected.add(toolName);\r\n      }\r\n      return newSelected;\r\n    });\r\n  }, []);\r\n\r\n  const handleProfileNameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {\r\n    setNewProfileName(e.target.value);\r\n  }, []);\r\n\r\n  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {\r\n    if (e.key === 'Enter') {\r\n      handleCreateProfile();\r\n    }\r\n  }, [handleCreateProfile]);\r\n\r\n  const ProfileStep = useMemo(() => (\r\n    <div className=\"space-y-6\">\r\n      <div>\r\n        <h3 className=\"text-lg font-semibold\">Connect to {app.name}</h3>\r\n        <p className=\"text-sm text-muted-foreground\">\r\n          {mode === 'profile-only' \r\n            ? 'Create a new profile to connect your account'\r\n            : (connectedProfiles.length > 0 \r\n                ? 'Select a profile or create a new one to connect different accounts'\r\n                : 'Create your first profile to get started')\r\n          }\r\n        </p>\r\n      </div>\r\n\r\n      {mode !== 'profile-only' && connectedProfiles.length > 0 && !isCreatingProfile && (\r\n        <div className=\"space-y-4\">\r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor=\"profile-select\">Select Profile</Label>\r\n            <Select value={selectedProfileId} onValueChange={setSelectedProfileId}>\r\n              <SelectTrigger>\r\n                <SelectValue placeholder=\"Choose a profile\">\r\n                  {selectedProfile && (\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <span>{selectedProfile.profile_name}</span>\r\n                      <CheckCircle2 className=\"h-3 w-3 text-green-500\" />\r\n                    </div>\r\n                  )}\r\n                </SelectValue>\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                {connectedProfiles.map((profile) => (\r\n                  <SelectItem key={profile.profile_id} value={profile.profile_id}>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <span>{profile.profile_name}</span>\r\n                      <div className=\"h-2 w-2 bg-green-500 rounded-full\" />\r\n                    </div>\r\n                  </SelectItem>\r\n                ))}\r\n              </SelectContent>\r\n            </Select>\r\n          </div>\r\n\r\n          <div className=\"flex items-center gap-3\">\r\n            <Separator className=\"flex-1\" />\r\n            <span className=\"text-xs text-muted-foreground\">OR</span>\r\n            <Separator className=\"flex-1\" />\r\n          </div>\r\n\r\n          <Button \r\n            variant=\"outline\" \r\n            onClick={() => setIsCreatingProfile(true)}\r\n            className=\"w-full\"\r\n          >\r\n            <Plus className=\"h-4 w-4\" />\r\n            Create New Profile\r\n          </Button>\r\n        </div>\r\n      )}\r\n\r\n      {(mode === 'profile-only' || connectedProfiles.length === 0 || isCreatingProfile) && (\r\n        <div className=\"space-y-4\">\r\n          <div className=\"space-y-2\">\r\n            <Label htmlFor=\"profile-name\">Profile Name</Label>\r\n            <Input\r\n              id=\"profile-name\"\r\n              placeholder=\"e.g., Personal Account, Work Account\"\r\n              value={newProfileName}\r\n              onChange={handleProfileNameChange}\r\n              onKeyDown={handleKeyDown}\r\n              autoFocus={mode === 'profile-only' || isCreatingProfile}\r\n            />\r\n          </div>\r\n\r\n          <div className=\"flex gap-3\">\r\n            {mode !== 'profile-only' && isCreatingProfile && (\r\n              <Button \r\n                variant=\"outline\" \r\n                onClick={() => {\r\n                  setIsCreatingProfile(false);\r\n                  setNewProfileName('');\r\n                }}\r\n                className=\"flex-1\"\r\n              >\r\n                Cancel\r\n              </Button>\r\n            )}\r\n            <Button \r\n              onClick={handleCreateProfile}\r\n              disabled={!newProfileName.trim() || isConnecting}\r\n              className={mode === 'profile-only' ? 'w-full' : 'flex-1'}\r\n            >\r\n              {isConnecting ? (\r\n                <>\r\n                  <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\r\n                  Creating...\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <Plus className=\"h-4 w-4\" />\r\n                  Create & Connect\r\n                </>\r\n              )}\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      )}\r\n\r\n      {mode !== 'profile-only' && selectedProfileId && !isCreatingProfile && (\r\n        <div className=\"pt-4 border-t\">\r\n          <Button \r\n            onClick={proceedToTools}\r\n            disabled={!selectedProfileId || isCompletingConnection}\r\n            className=\"w-full\"\r\n          >\r\n            {isCompletingConnection ? (\r\n              <>\r\n                <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\r\n                Connecting...\r\n              </>\r\n            ) : (\r\n              <>\r\n                Continue to Tools\r\n                <ArrowRight className=\"h-4 w-4\" />\r\n              </>\r\n            )}\r\n          </Button>\r\n        </div>\r\n      )}\r\n    </div>\r\n  ), [\r\n    app.name, \r\n    connectedProfiles, \r\n    isCreatingProfile, \r\n    selectedProfileId, \r\n    selectedProfile, \r\n    newProfileName, \r\n    isConnecting, \r\n    handleProfileNameChange, \r\n    handleKeyDown, \r\n    handleCreateProfile, \r\n    proceedToTools,\r\n    mode,\r\n    handleProfileOnlyComplete,\r\n    isCompletingConnection\r\n  ]);\r\n\r\n  const ToolsStep = useMemo(() => (\r\n    <div className=\"space-y-6\">\r\n      <div>\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button \r\n            variant=\"link\" \r\n            size=\"sm\"\r\n            onClick={() => setStep('profile')}\r\n            className=\"mb-4 p-0 h-auto font-normal text-muted-foreground hover:text-foreground\"\r\n          >\r\n            ← Back to Profile\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {isLoadingTools ? (\r\n        <div className=\"flex items-center justify-center py-8\">\r\n          <div className=\"flex items-center gap-2\">\r\n            <Loader2 className=\"h-5 w-5 animate-spin\" />\r\n            <span className=\"text-sm\">Loading tools...</span>\r\n          </div>\r\n        </div>\r\n      ) : tools.length === 0 ? (\r\n        <div className=\"text-center py-8\">\r\n          <div className=\"text-4xl mb-3\">🔧</div>\r\n          <h4 className=\"font-medium mb-2\">No tools available</h4>\r\n          <p className=\"text-sm text-muted-foreground mb-4\">\r\n            This app doesn't have any tools available yet.\r\n          </p>\r\n          <Button variant=\"outline\" onClick={proceedToTools}>\r\n            <RefreshCw className=\"h-4 w-4 mr-2\" />\r\n            Refresh\r\n          </Button>\r\n        </div>\r\n      ) : (\r\n        <>\r\n          <div className=\"space-y-3\">\r\n            <div className=\"flex items-center justify-between\">\r\n              <span className=\"text-sm font-medium\">\r\n                {selectedTools.size} of {tools.length} tools selected\r\n              </span>\r\n              <Button \r\n                variant=\"ghost\" \r\n                size=\"sm\"\r\n                onClick={() => {\r\n                  if (selectedTools.size === tools.length) {\r\n                    setSelectedTools(new Set());\r\n                  } else {\r\n                    setSelectedTools(new Set(tools.map(tool => tool.name)));\r\n                  }\r\n                }}\r\n              >\r\n                {selectedTools.size === tools.length ? 'Deselect All' : 'Select All'}\r\n              </Button>\r\n            </div>\r\n            \r\n            <div className=\"space-y-2 max-h-64 overflow-y-auto\">\r\n              {tools.map((tool) => {\r\n                const isSelected = selectedTools.has(tool.name);\r\n                \r\n                return (\r\n                  <Card \r\n                    key={tool.name}\r\n                    className={cn(\r\n                      \"p-0 border cursor-pointer transition-colors\",\r\n                      isSelected ? \"bg-muted/50\" : \"hover:bg-muted/20\"\r\n                    )}\r\n                    onClick={() => handleToolToggle(tool.name)}\r\n                  >\r\n                    <CardContent className=\"p-4\">\r\n                      <div className=\"flex items-start justify-between gap-3\">\r\n                        <div className=\"flex-1 min-w-0\">\r\n                          <div className=\"flex items-center gap-2 mb-1\">\r\n                            <h4 className=\"font-medium text-sm\">{tool.name}</h4>\r\n                            {isSelected && (\r\n                              <CheckCircle2 className=\"h-4 w-4 text-green-500\" />\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                        <Switch\r\n                          checked={isSelected}\r\n                          onCheckedChange={() => handleToolToggle(tool.name)}\r\n                          onClick={(e) => e.stopPropagation()}\r\n                        />\r\n                      </div>\r\n                    </CardContent>\r\n                  </Card>\r\n                );\r\n              })}\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"pt-4 border-t\">\r\n            <Button \r\n              onClick={handleComplete}\r\n              disabled={selectedTools.size === 0 || isCompletingConnection}\r\n              className=\"w-full\"\r\n            >\r\n              {isCompletingConnection ? (\r\n                <>\r\n                  <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\r\n                  Connecting...\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <Zap className=\"h-4 w-4\" />\r\n                  Connect with {selectedTools.size} Tool{selectedTools.size !== 1 ? 's' : ''}\r\n                </>\r\n              )}\r\n            </Button>\r\n          </div>\r\n        </>\r\n      )}\r\n    </div>\r\n  ), [\r\n    app.name, \r\n    isLoadingTools, \r\n    tools, \r\n    selectedTools, \r\n    handleToolToggle, \r\n    handleComplete, \r\n    isCompletingConnection, \r\n    proceedToTools\r\n  ]);\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"sm:max-w-lg max-h-[90vh] overflow-y-auto\">\r\n        <DialogHeader className=\"space-y-4\">\r\n          <div className=\"flex items-center gap-3\">\r\n            <div className=\"h-10 w-10 flex-shrink-0 rounded-lg bg-muted flex items-center justify-center\">\r\n              {app.img_src ? (\r\n                <img\r\n                  src={app.img_src}\r\n                  alt={app.name}\r\n                  className=\"h-6 w-6 object-cover rounded\"\r\n                />\r\n              ) : (\r\n                <span className=\"text-sm font-semibold\">{app.name.charAt(0)}</span>\r\n              )}\r\n            </div>\r\n            <div>\r\n              <DialogTitle className=\"text-left\">{app.name}</DialogTitle>\r\n              <DialogDescription className=\"text-left\">\r\n                {mode === 'profile-only' \r\n                  ? 'Connect your account to continue with installation'\r\n                  : app.description\r\n                }\r\n              </DialogDescription>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"flex items-center gap-2\">\r\n            <div className={cn(\r\n              \"h-2 w-2 rounded-full\",\r\n              step === 'profile' ? 'bg-primary' : 'bg-muted'\r\n            )} />\r\n            {mode === 'full' && (\r\n              <>\r\n                <div className=\"h-px bg-muted flex-1\" />\r\n                <div className={cn(\r\n                  \"h-2 w-2 rounded-full\",\r\n                  step === 'tools' ? 'bg-primary' : 'bg-muted'\r\n                )} />\r\n              </>\r\n            )}\r\n          </div>\r\n        </DialogHeader>\r\n\r\n        <div className=\"mt-6\">\r\n          {step === 'profile' ? ProfileStep : ToolsStep}\r\n        </div>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}; "], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AAOA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAiBO,MAAM,qBAAwD,CAAC,EACpE,GAAG,EACH,IAAI,EACJ,YAAY,EACZ,UAAU,EACV,OAAO,MAAM,EACd;IACC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IACtD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACnE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IACpE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACtD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErE,MAAM,EAAE,MAAM,QAAQ,EAAE,SAAS,eAAe,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,uBAAoB,AAAD,EAAE;QAAE,UAAU,IAAI,SAAS;IAAC;IACpG,MAAM,gBAAgB,CAAA,GAAA,2KAAA,CAAA,4BAAyB,AAAD;IAC9C,MAAM,iBAAiB,CAAA,GAAA,2KAAA,CAAA,6BAA0B,AAAD;IAEhD,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAChC,OAAO,UAAU,OAAO,CAAA,IAAK,EAAE,YAAY,KAAK,EAAE;IACpD,GAAG;QAAC;KAAS;IAEb,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC9B,OAAO,UAAU,KAAK,CAAA,IAAK,EAAE,UAAU,KAAK;IAC9C,GAAG;QAAC;QAAU;KAAkB;IAEhC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM;YACR,QAAQ;YACR,qBAAqB;YACrB,qBAAqB;YACrB,kBAAkB;YAClB,iBAAiB,IAAI;YACrB,SAAS,EAAE;QACb;IACF,GAAG;QAAC;KAAK;IAET,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ,kBAAkB,MAAM,KAAK,KAAK,CAAC,mBAAmB;YAChE,qBAAqB,iBAAiB,CAAC,EAAE,CAAC,UAAU;QACtD;IACF,GAAG;QAAC;QAAM;QAAmB;KAAkB;IAE/C,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,IAAI,CAAC,eAAe,IAAI,IAAI;YAC1B,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,gBAAgB;QAChB,IAAI;YACF,MAAM,UAAgC;gBACpC,cAAc,eAAe,IAAI;gBACjC,UAAU,IAAI,SAAS;gBACvB,UAAU,IAAI,IAAI;gBAClB,YAAY,kBAAkB,MAAM,KAAK;YAC3C;YAEA,MAAM,aAAa,MAAM,cAAc,WAAW,CAAC;YAEnD,sBAAsB;YACtB,MAAM,eAAe,WAAW,CAAC;gBAC/B,WAAW,WAAW,UAAU;gBAChC,KAAK,IAAI,SAAS;YACpB;YAEA,MAAM;YACN,qBAAqB,WAAW,UAAU;YAC1C,qBAAqB;YACrB,kBAAkB;YAClB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAEd,IAAI,SAAS,gBAAgB;gBAC3B,WAAW,WAAW,UAAU,EAAE,EAAE,EAAE,IAAI,IAAI,EAAE,IAAI,SAAS;gBAC7D,aAAa;YACf,OAAO;gBACL;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,gBAAgB;QAClB;IACF,GAAG;QAAC;QAAgB,IAAI,SAAS;QAAE,IAAI,IAAI;QAAE,kBAAkB,MAAM;QAAE;QAAe;QAAgB;QAAiB;QAAM;QAAY;KAAa;IAEtJ,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,CAAC,qBAAqB,CAAC,iBAAiB;QAE5C,kBAAkB;QAClB,QAAQ;QAER,IAAI;YACF,MAAM,UAAU,MAAM,oJAAA,CAAA,eAAY,CAAC,kBAAkB,CAAC,gBAAgB,gBAAgB,EAAE,IAAI,SAAS;YACrG,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,IAAI,SAAS;YAE7D,IAAI,QAAQ,iBAAiB;gBAC3B,SAAS,OAAO,eAAe;gBAC/B,iBAAiB,IAAI,IAAI,OAAO,eAAe,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;YACvE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,kBAAkB;QACpB;IACF,GAAG;QAAC;QAAmB;QAAiB,IAAI,SAAS;KAAC;IAEtD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,IAAI,CAAC,qBAAqB,cAAc,IAAI,KAAK,GAAG;YAClD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QACA,0BAA0B;QAC1B,IAAI;YACF,WAAW,mBAAmB,MAAM,IAAI,CAAC,gBAAgB,IAAI,IAAI,EAAE,IAAI,SAAS;YAChF,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,0BAA0B;QAC5B;IACF,GAAG;QAAC;QAAmB;QAAe;QAAY,IAAI,IAAI;QAAE,IAAI,SAAS;QAAE;KAAa;IAExF,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5C,IAAI,CAAC,mBAAmB;YACtB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,0BAA0B;QAC1B,IAAI;YACF,WAAW,mBAAmB,EAAE,EAAE,IAAI,IAAI,EAAE,IAAI,SAAS;YACzD,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,0BAA0B;QAC5B;IACF,GAAG;QAAC;QAAmB;QAAY,IAAI,IAAI;QAAE,IAAI,SAAS;QAAE;KAAa;IAEzE,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,iBAAiB,CAAA;YACf,MAAM,cAAc,IAAI,IAAI;YAC5B,IAAI,YAAY,GAAG,CAAC,WAAW;gBAC7B,YAAY,MAAM,CAAC;YACrB,OAAO;gBACL,YAAY,GAAG,CAAC;YAClB;YACA,OAAO;QACT;IACF,GAAG,EAAE;IAEL,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC3C,kBAAkB,EAAE,MAAM,CAAC,KAAK;IAClC,GAAG,EAAE;IAEL,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACjC,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB;QACF;IACF,GAAG;QAAC;KAAoB;IAExB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,kBAC1B,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;;gCAAwB;gCAAY,IAAI,IAAI;;;;;;;sCAC1D,8OAAC;4BAAE,WAAU;sCACV,SAAS,iBACN,iDACC,kBAAkB,MAAM,GAAG,IACxB,uEACA;;;;;;;;;;;;gBAKX,SAAS,kBAAkB,kBAAkB,MAAM,GAAG,KAAK,CAAC,mCAC3D,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAiB;;;;;;8CAChC,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAmB,eAAe;;sDAC/C,8OAAC,kIAAA,CAAA,gBAAa;sDACZ,cAAA,8OAAC,kIAAA,CAAA,cAAW;gDAAC,aAAY;0DACtB,iCACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAM,gBAAgB,YAAY;;;;;;sEACnC,8OAAC,qNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;sDAKhC,8OAAC,kIAAA,CAAA,gBAAa;sDACX,kBAAkB,GAAG,CAAC,CAAC,wBACtB,8OAAC,kIAAA,CAAA,aAAU;oDAA0B,OAAO,QAAQ,UAAU;8DAC5D,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAM,QAAQ,YAAY;;;;;;0EAC3B,8OAAC;gEAAI,WAAU;;;;;;;;;;;;mDAHF,QAAQ,UAAU;;;;;;;;;;;;;;;;;;;;;;sCAW3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,qIAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;oCAAK,WAAU;8CAAgC;;;;;;8CAChD,8OAAC,qIAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;;sCAGvB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS,IAAM,qBAAqB;4BACpC,WAAU;;8CAEV,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;gBAMjC,CAAC,SAAS,kBAAkB,kBAAkB,MAAM,KAAK,KAAK,iBAAiB,mBAC9E,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;8CAAe;;;;;;8CAC9B,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,aAAY;oCACZ,OAAO;oCACP,UAAU;oCACV,WAAW;oCACX,WAAW,SAAS,kBAAkB;;;;;;;;;;;;sCAI1C,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,kBAAkB,mCAC1B,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;wCACP,qBAAqB;wCACrB,kBAAkB;oCACpB;oCACA,WAAU;8CACX;;;;;;8CAIH,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,CAAC,eAAe,IAAI,MAAM;oCACpC,WAAW,SAAS,iBAAiB,WAAW;8CAE/C,6BACC;;0DACE,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAA8B;;qEAInD;;0DACE,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;gBASvC,SAAS,kBAAkB,qBAAqB,CAAC,mCAChD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU,CAAC,qBAAqB;wBAChC,WAAU;kCAET,uCACC;;8CACE,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;gCAA8B;;yDAInD;;gCAAE;8CAEA,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kBAOjC;QACD,IAAI,IAAI;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,kBACxB,8OAAC;YAAI,WAAU;;8BACb,8OAAC;8BACC,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS,IAAM,QAAQ;4BACvB,WAAU;sCACX;;;;;;;;;;;;;;;;gBAMJ,+BACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;2BAG5B,MAAM,MAAM,KAAK,kBACnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAG,WAAU;sCAAmB;;;;;;sCACjC,8OAAC;4BAAE,WAAU;sCAAqC;;;;;;sCAGlD,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,SAAS;;8CACjC,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;yCAK1C;;sCACE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;;gDACb,cAAc,IAAI;gDAAC;gDAAK,MAAM,MAAM;gDAAC;;;;;;;sDAExC,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;gDACP,IAAI,cAAc,IAAI,KAAK,MAAM,MAAM,EAAE;oDACvC,iBAAiB,IAAI;gDACvB,OAAO;oDACL,iBAAiB,IAAI,IAAI,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;gDACtD;4CACF;sDAEC,cAAc,IAAI,KAAK,MAAM,MAAM,GAAG,iBAAiB;;;;;;;;;;;;8CAI5D,8OAAC;oCAAI,WAAU;8CACZ,MAAM,GAAG,CAAC,CAAC;wCACV,MAAM,aAAa,cAAc,GAAG,CAAC,KAAK,IAAI;wCAE9C,qBACE,8OAAC,gIAAA,CAAA,OAAI;4CAEH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+CACA,aAAa,gBAAgB;4CAE/B,SAAS,IAAM,iBAAiB,KAAK,IAAI;sDAEzC,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAuB,KAAK,IAAI;;;;;;oEAC7C,4BACC,8OAAC,qNAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAI9B,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS;4DACT,iBAAiB,IAAM,iBAAiB,KAAK,IAAI;4DACjD,SAAS,CAAC,IAAM,EAAE,eAAe;;;;;;;;;;;;;;;;;2CApBlC,KAAK,IAAI;;;;;oCA0BpB;;;;;;;;;;;;sCAIJ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,cAAc,IAAI,KAAK,KAAK;gCACtC,WAAU;0CAET,uCACC;;sDACE,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAA8B;;iEAInD;;sDACE,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAAY;wCACb,cAAc,IAAI;wCAAC;wCAAM,cAAc,IAAI,KAAK,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;kBAQrF;QACD,IAAI,IAAI;QACR;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,IAAI,OAAO,iBACV,8OAAC;wCACC,KAAK,IAAI,OAAO;wCAChB,KAAK,IAAI,IAAI;wCACb,WAAU;;;;;6DAGZ,8OAAC;wCAAK,WAAU;kDAAyB,IAAI,IAAI,CAAC,MAAM,CAAC;;;;;;;;;;;8CAG7D,8OAAC;;sDACC,8OAAC,kIAAA,CAAA,cAAW;4CAAC,WAAU;sDAAa,IAAI,IAAI;;;;;;sDAC5C,8OAAC,kIAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAC1B,SAAS,iBACN,uDACA,IAAI,WAAW;;;;;;;;;;;;;;;;;;sCAMzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,wBACA,SAAS,YAAY,eAAe;;;;;;gCAErC,SAAS,wBACR;;sDACE,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,wBACA,SAAS,UAAU,eAAe;;;;;;;;;;;;;;;;;;;;8BAO5C,8OAAC;oBAAI,WAAU;8BACZ,SAAS,YAAY,cAAc;;;;;;;;;;;;;;;;;AAK9C", "debugId": null}}, {"offset": {"line": 8294, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/mcp/tools-manager.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useMemo } from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Switch } from '@/components/ui/switch';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Alert, AlertDescription } from '@/components/ui/alert';\r\nimport { \r\n  Dialog, \r\n  DialogContent, \r\n  DialogHeader, \r\n  DialogTitle, \r\n  DialogDescription,\r\n  DialogFooter\r\n} from '@/components/ui/dialog';\r\nimport { \r\n  Loader2, \r\n  CheckCircle2, \r\n  XCircle, \r\n  Zap, \r\n  Info,\r\n  RefreshCw,\r\n  Save\r\n} from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { usePipedreamToolsData } from '@/hooks/react-query/agents/use-pipedream-tools';\r\nimport { useCustomMCPToolsData } from '@/hooks/react-query/agents/use-custom-mcp-tools';\r\n\r\ninterface BaseToolsManagerProps {\r\n  agentId: string;\r\n  open: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n  onToolsUpdate?: (enabledTools: string[]) => void;\r\n}\r\n\r\ninterface PipedreamToolsManagerProps extends BaseToolsManagerProps {\r\n  mode: 'pipedream';\r\n  profileId: string;\r\n  appName: string;\r\n  profileName?: string;\r\n}\r\n\r\ninterface CustomToolsManagerProps extends BaseToolsManagerProps {\r\n  mode: 'custom';\r\n  mcpConfig: any;\r\n  mcpName: string;\r\n}\r\n\r\ntype ToolsManagerProps = PipedreamToolsManagerProps | CustomToolsManagerProps;\r\n\r\nexport const ToolsManager: React.FC<ToolsManagerProps> = (props) => {\r\n  const { agentId, open, onOpenChange, onToolsUpdate, mode } = props;\r\n\r\n  const pipedreamResult = usePipedreamToolsData(\r\n    mode === 'pipedream' ? agentId : '',\r\n    mode === 'pipedream' ? props.profileId : ''\r\n  );\r\n  \r\n  const customResult = useCustomMCPToolsData(\r\n    mode === 'custom' ? agentId : '',\r\n    mode === 'custom' ? props.mcpConfig : null\r\n  );\r\n\r\n  const result = mode === 'pipedream' ? pipedreamResult : customResult;\r\n  const { data, isLoading, error, handleUpdateTools, isUpdating, refetch } = result;\r\n  \r\n  const [localTools, setLocalTools] = useState<Record<string, boolean>>({});\r\n  const [hasChanges, setHasChanges] = useState(false);\r\n\r\n  React.useEffect(() => {\r\n    if (data?.tools) {\r\n      const toolsMap: Record<string, boolean> = {};\r\n      data.tools.forEach((tool: { name: string; enabled: boolean }) => {\r\n        toolsMap[tool.name] = tool.enabled;\r\n      });\r\n      setLocalTools(toolsMap);\r\n      setHasChanges(false);\r\n    }\r\n  }, [data]);\r\n\r\n  const enabledCount = useMemo(() => {\r\n    return Object.values(localTools).filter(Boolean).length;\r\n  }, [localTools]);\r\n\r\n  const totalCount = data?.tools?.length || 0;\r\n  \r\n  const displayName = mode === 'pipedream' ? props.appName : props.mcpName;\r\n  const contextName = mode === 'pipedream' ? props.profileName || 'Profile' : 'Server';\r\n\r\n  const handleToolToggle = (toolName: string) => {\r\n    setLocalTools(prev => {\r\n      const newValue = !prev[toolName];\r\n      const updated = { ...prev, [toolName]: newValue };\r\n      const serverTools: Record<string, boolean> = {};\r\n      if (data?.tools) {\r\n        for (const tool of data.tools) {\r\n          serverTools[tool.name] = tool.enabled;\r\n        }\r\n      }\r\n      const hasChanges = Object.keys(updated).some(key => updated[key] !== serverTools[key]);\r\n      setHasChanges(hasChanges);\r\n      return updated;\r\n    });\r\n  };\r\n\r\n  const handleSelectAll = () => {\r\n    if (!data?.tools) return;\r\n    const allEnabled = data.tools.every((tool: any) => !!localTools[tool.name]);\r\n    const newState: Record<string, boolean> = {};\r\n    data.tools.forEach((tool: any) => {\r\n      newState[tool.name] = !allEnabled;\r\n    });\r\n    setLocalTools(newState);\r\n    setHasChanges(true);\r\n  };\r\n\r\n  const handleSave = () => {\r\n    const enabledTools = Object.entries(localTools)\r\n      .filter(([_, enabled]) => enabled)\r\n      .map(([name]) => name);\r\n    \r\n    handleUpdateTools(enabledTools);\r\n    setHasChanges(false);\r\n    \r\n    if (onToolsUpdate) {\r\n      onToolsUpdate(enabledTools);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    if (data?.tools) {\r\n      const serverState: Record<string, boolean> = {};\r\n      data.tools.forEach((tool: any) => {\r\n        serverState[tool.name] = tool.enabled;\r\n      });\r\n      setLocalTools(serverState);\r\n      setHasChanges(false);\r\n    }\r\n  };\r\n\r\n  if (error) {\r\n    return (\r\n      <Dialog open={open} onOpenChange={onOpenChange}>\r\n        <DialogContent className=\"sm:max-w-lg\">\r\n          <DialogHeader>\r\n            <DialogTitle className=\"flex items-center gap-2\">\r\n              <XCircle className=\"h-5 w-5 text-destructive\" />\r\n              Error Loading Tools\r\n            </DialogTitle>\r\n            <DialogDescription>\r\n              Failed to load {displayName} tools\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n          \r\n          <Alert variant=\"destructive\">\r\n            <XCircle className=\"h-4 w-4\" />\r\n            <AlertDescription>\r\n              {error?.message || 'An unexpected error occurred while loading tools.'}\r\n            </AlertDescription>\r\n          </Alert>\r\n          \r\n          <DialogFooter>\r\n            <Button variant=\"outline\" onClick={() => onOpenChange(false)}>\r\n              Close\r\n            </Button>\r\n            <Button onClick={() => refetch()}>\r\n              <RefreshCw className=\"h-4 w-4 mr-2\" />\r\n              Retry\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Dialog open={open} onOpenChange={onOpenChange}>\r\n      <DialogContent className=\"sm:max-w-2xl max-h-[80vh] overflow-hidden flex flex-col\">\r\n        <DialogHeader>\r\n          <DialogTitle className=\"flex items-center gap-2\">\r\n            <Zap className=\"h-5 w-5 text-primary\" />\r\n            Configure {displayName} Tools\r\n          </DialogTitle>\r\n          <DialogDescription>\r\n            Choose which {displayName} tools are available to your agent\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n\r\n        <div className=\"flex-1 overflow-hidden flex flex-col\">\r\n          {isLoading ? (\r\n            <div className=\"flex items-center justify-center py-12\">\r\n              <div className=\"flex items-center gap-2\">\r\n                <Loader2 className=\"h-5 w-5 animate-spin\" />\r\n                <span>Loading available tools...</span>\r\n              </div>\r\n            </div>\r\n          ) : !data?.tools?.length ? (\r\n            <div className=\"flex items-center justify-center py-12\">\r\n              <div className=\"text-center\">\r\n                <Info className=\"h-8 w-8 text-muted-foreground mx-auto mb-2\" />\r\n                <p className=\"text-sm text-muted-foreground\">\r\n                  No tools available for this {displayName} {mode === 'pipedream' ? 'profile' : 'server'}\r\n                </p>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <>\r\n              <div className=\"flex items-center justify-between pb-4\">\r\n                <div className=\"flex items-center gap-3\">\r\n                  <div>\r\n                    <div className=\"flex items-center gap-2\">\r\n                      <span className=\"text-sm font-medium\">\r\n                        {enabledCount} of {totalCount} tools enabled\r\n                      </span>\r\n                      {hasChanges && (\r\n                        <Badge className=\"text-xs bg-primary/10 text-primary\">\r\n                          Unsaved changes\r\n                        </Badge>\r\n                      )}\r\n                    </div>\r\n                    <p className=\"text-xs text-muted-foreground\">\r\n                      {contextName}: {mode === 'pipedream' ? props.profileName : displayName}\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n                \r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  onClick={handleSelectAll}\r\n                  disabled={isUpdating}\r\n                >\r\n                  {data.tools.every((tool: any) => localTools[tool.name]) ? 'Deselect All' : 'Select All'}\r\n                </Button>\r\n              </div>\r\n\r\n              <div className=\"flex-1 overflow-y-auto space-y-3\">\r\n                {data.tools.map((tool: any) => (\r\n                  <Card \r\n                    key={tool.name}\r\n                    className={cn(\r\n                      \"transition-colors cursor-pointer\",\r\n                      localTools[tool.name] ? \"bg-muted/50 border-primary/40\" : \"hover:bg-muted/20\"\r\n                    )}\r\n                    onClick={() => handleToolToggle(tool.name)}\r\n                  >\r\n                    <CardContent>\r\n                      <div className=\"flex items-start justify-between gap-3\">\r\n                        <div className=\"flex-1 min-w-0\">\r\n                          <div className=\"flex items-center gap-2\">\r\n                            <h4 className=\"font-medium text-sm\">{tool.name}</h4>\r\n                            {localTools[tool.name] && (\r\n                              <CheckCircle2 className=\"h-4 w-4 text-green-500\" />\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                        <Switch\r\n                          checked={localTools[tool.name] || false}\r\n                          onCheckedChange={() => handleToolToggle(tool.name)}\r\n                          onClick={(e) => e.stopPropagation()}\r\n                          disabled={isUpdating}\r\n                        />\r\n                      </div>\r\n                    </CardContent>\r\n                  </Card>\r\n                ))}\r\n              </div>\r\n            </>\r\n          )}\r\n        </div>\r\n\r\n        <DialogFooter>\r\n          <div className=\"flex items-center justify-between w-full\">\r\n            <div className=\"flex items-center gap-2\">\r\n              {!data?.has_mcp_config && data?.tools?.length > 0 && (\r\n                <Alert className=\"p-2\">\r\n                  <Info className=\"h-3 w-3\" />\r\n                  <AlertDescription className=\"text-xs\">\r\n                    This will {mode === 'pipedream' ? 'create a new' : 'update the'} MCP configuration for your agent\r\n                  </AlertDescription>\r\n                </Alert>\r\n              )}\r\n            </div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <Button\r\n                variant=\"outline\"\r\n                onClick={hasChanges ? handleCancel : () => onOpenChange(false)}\r\n                disabled={isUpdating}\r\n              >\r\n                {hasChanges ? 'Cancel' : 'Close'}\r\n              </Button>\r\n              \r\n              {hasChanges && (\r\n                <Button\r\n                  onClick={handleSave}\r\n                  disabled={isUpdating}\r\n                >\r\n                  {isUpdating ? (\r\n                    <>\r\n                      <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\r\n                      Saving...\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <Save className=\"h-4 w-4\" />\r\n                      Save Changes\r\n                    </>\r\n                  )}\r\n                </Button>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </DialogFooter>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}; "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AACA;AA3BA;;;;;;;;;;;;;AAmDO,MAAM,eAA4C,CAAC;IACxD,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,YAAY,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG;IAE7D,MAAM,kBAAkB,CAAA,GAAA,qKAAA,CAAA,wBAAqB,AAAD,EAC1C,SAAS,cAAc,UAAU,IACjC,SAAS,cAAc,MAAM,SAAS,GAAG;IAG3C,MAAM,eAAe,CAAA,GAAA,yKAAA,CAAA,wBAAqB,AAAD,EACvC,SAAS,WAAW,UAAU,IAC9B,SAAS,WAAW,MAAM,SAAS,GAAG;IAGxC,MAAM,SAAS,SAAS,cAAc,kBAAkB;IACxD,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,iBAAiB,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG;IAE3E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IACvE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,MAAM,OAAO;YACf,MAAM,WAAoC,CAAC;YAC3C,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC;gBAClB,QAAQ,CAAC,KAAK,IAAI,CAAC,GAAG,KAAK,OAAO;YACpC;YACA,cAAc;YACd,cAAc;QAChB;IACF,GAAG;QAAC;KAAK;IAET,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC3B,OAAO,OAAO,MAAM,CAAC,YAAY,MAAM,CAAC,SAAS,MAAM;IACzD,GAAG;QAAC;KAAW;IAEf,MAAM,aAAa,MAAM,OAAO,UAAU;IAE1C,MAAM,cAAc,SAAS,cAAc,MAAM,OAAO,GAAG,MAAM,OAAO;IACxE,MAAM,cAAc,SAAS,cAAc,MAAM,WAAW,IAAI,YAAY;IAE5E,MAAM,mBAAmB,CAAC;QACxB,cAAc,CAAA;YACZ,MAAM,WAAW,CAAC,IAAI,CAAC,SAAS;YAChC,MAAM,UAAU;gBAAE,GAAG,IAAI;gBAAE,CAAC,SAAS,EAAE;YAAS;YAChD,MAAM,cAAuC,CAAC;YAC9C,IAAI,MAAM,OAAO;gBACf,KAAK,MAAM,QAAQ,KAAK,KAAK,CAAE;oBAC7B,WAAW,CAAC,KAAK,IAAI,CAAC,GAAG,KAAK,OAAO;gBACvC;YACF;YACA,MAAM,aAAa,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,CAAA,MAAO,OAAO,CAAC,IAAI,KAAK,WAAW,CAAC,IAAI;YACrF,cAAc;YACd,OAAO;QACT;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,MAAM,OAAO;QAClB,MAAM,aAAa,KAAK,KAAK,CAAC,KAAK,CAAC,CAAC,OAAc,CAAC,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC;QAC1E,MAAM,WAAoC,CAAC;QAC3C,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC;YAClB,QAAQ,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC;QACzB;QACA,cAAc;QACd,cAAc;IAChB;IAEA,MAAM,aAAa;QACjB,MAAM,eAAe,OAAO,OAAO,CAAC,YACjC,MAAM,CAAC,CAAC,CAAC,GAAG,QAAQ,GAAK,SACzB,GAAG,CAAC,CAAC,CAAC,KAAK,GAAK;QAEnB,kBAAkB;QAClB,cAAc;QAEd,IAAI,eAAe;YACjB,cAAc;QAChB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,MAAM,OAAO;YACf,MAAM,cAAuC,CAAC;YAC9C,KAAK,KAAK,CAAC,OAAO,CAAC,CAAC;gBAClB,WAAW,CAAC,KAAK,IAAI,CAAC,GAAG,KAAK,OAAO;YACvC;YACA,cAAc;YACd,cAAc;QAChB;IACF;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC,kIAAA,CAAA,SAAM;YAAC,MAAM;YAAM,cAAc;sBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,8OAAC,kIAAA,CAAA,eAAY;;0CACX,8OAAC,kIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAA6B;;;;;;;0CAGlD,8OAAC,kIAAA,CAAA,oBAAiB;;oCAAC;oCACD;oCAAY;;;;;;;;;;;;;kCAIhC,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;;0CACb,8OAAC,4MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC,iIAAA,CAAA,mBAAgB;0CACd,OAAO,WAAW;;;;;;;;;;;;kCAIvB,8OAAC,kIAAA,CAAA,eAAY;;0CACX,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS,IAAM,aAAa;0CAAQ;;;;;;0CAG9D,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAS,IAAM;;kDACrB,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;IAOlD;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAyB;gCAC7B;gCAAY;;;;;;;sCAEzB,8OAAC,kIAAA,CAAA,oBAAiB;;gCAAC;gCACH;gCAAY;;;;;;;;;;;;;8BAI9B,8OAAC;oBAAI,WAAU;8BACZ,0BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iNAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,8OAAC;8CAAK;;;;;;;;;;;;;;;;+BAGR,CAAC,MAAM,OAAO,uBAChB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAE,WAAU;;wCAAgC;wCACd;wCAAY;wCAAE,SAAS,cAAc,YAAY;;;;;;;;;;;;;;;;;6CAKpF;;0CACE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;;gEACb;gEAAa;gEAAK;gEAAW;;;;;;;wDAE/B,4BACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,WAAU;sEAAqC;;;;;;;;;;;;8DAK1D,8OAAC;oDAAE,WAAU;;wDACV;wDAAY;wDAAG,SAAS,cAAc,MAAM,WAAW,GAAG;;;;;;;;;;;;;;;;;;kDAKjE,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU;kDAET,KAAK,KAAK,CAAC,KAAK,CAAC,CAAC,OAAc,UAAU,CAAC,KAAK,IAAI,CAAC,IAAI,iBAAiB;;;;;;;;;;;;0CAI/E,8OAAC;gCAAI,WAAU;0CACZ,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,qBACf,8OAAC,gIAAA,CAAA,OAAI;wCAEH,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oCACA,UAAU,CAAC,KAAK,IAAI,CAAC,GAAG,kCAAkC;wCAE5D,SAAS,IAAM,iBAAiB,KAAK,IAAI;kDAEzC,cAAA,8OAAC,gIAAA,CAAA,cAAW;sDACV,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAuB,KAAK,IAAI;;;;;;gEAC7C,UAAU,CAAC,KAAK,IAAI,CAAC,kBACpB,8OAAC,qNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;;;;;;;kEAI9B,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAS,UAAU,CAAC,KAAK,IAAI,CAAC,IAAI;wDAClC,iBAAiB,IAAM,iBAAiB,KAAK,IAAI;wDACjD,SAAS,CAAC,IAAM,EAAE,eAAe;wDACjC,UAAU;;;;;;;;;;;;;;;;;uCArBX,KAAK,IAAI;;;;;;;;;;;;;;;;;8BAgC1B,8OAAC,kIAAA,CAAA,eAAY;8BACX,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,CAAC,MAAM,kBAAkB,MAAM,OAAO,SAAS,mBAC9C,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAU;;sDACf,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC,iIAAA,CAAA,mBAAgB;4CAAC,WAAU;;gDAAU;gDACzB,SAAS,cAAc,iBAAiB;gDAAa;;;;;;;;;;;;;;;;;;0CAKxE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,aAAa,eAAe,IAAM,aAAa;wCACxD,UAAU;kDAET,aAAa,WAAW;;;;;;oCAG1B,4BACC,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,UAAU;kDAET,2BACC;;8DACE,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA8B;;yEAInD;;8DACE,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYlD", "debugId": null}}, {"offset": {"line": 8899, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/pipedream/constants.ts"], "sourcesContent": ["export const categoryEmojis: Record<string, string> = {\r\n  'All': '🌟',\r\n  'Communication': '💬',\r\n  'Artificial Intelligence (AI)': '🤖',\r\n  'Social Media': '📱',\r\n  'CRM': '👥',\r\n  'Marketing': '📈',\r\n  'Analytics': '📊',\r\n  'Commerce': '📊',\r\n  'Databases': '🗄️',\r\n  'File Storage': '🗂️',\r\n  'Help Desk & Support': '🎧',\r\n  'Infrastructure & Cloud': '🌐',\r\n  'E-commerce': '🛒',\r\n  'Developer Tools': '🔧',\r\n  'Web & App Development': '🌐',\r\n  'Business Management': '💼',\r\n  'Productivity': '⚡',\r\n  'Finance': '💰',\r\n  'Email': '📧',\r\n  'Project Management': '📋',\r\n  'Storage': '💾',\r\n  'AI/ML': '🤖',\r\n  'Data & Databases': '🗄️',\r\n  'Video': '🎥',\r\n  'Calendar': '📅',\r\n  'Forms': '📝',\r\n  'Security': '🔒',\r\n  'HR': '👔',\r\n  'Sales': '💼',\r\n  'Support': '🎧',\r\n  'Design': '🎨',\r\n  'Business Intelligence': '📈',\r\n  'Automation': '🔄',\r\n  'News': '📰',\r\n  'Weather': '🌤️',\r\n  'Travel': '✈️',\r\n  'Education': '🎓',\r\n  'Health': '🏥',\r\n};\r\n\r\nexport const PAGINATION_CONSTANTS = {\r\n  FIRST_PAGE: 'FIRST_PAGE',\r\n  POPULAR_APPS_COUNT: 6,\r\n} as const; "], "names": [], "mappings": ";;;;AAAO,MAAM,iBAAyC;IACpD,OAAO;IACP,iBAAiB;IACjB,gCAAgC;IAChC,gBAAgB;IAChB,OAAO;IACP,aAAa;IACb,aAAa;IACb,YAAY;IACZ,aAAa;IACb,gBAAgB;IAChB,uBAAuB;IACvB,0BAA0B;IAC1B,cAAc;IACd,mBAAmB;IACnB,yBAAyB;IACzB,uBAAuB;IACvB,gBAAgB;IAChB,WAAW;IACX,SAAS;IACT,sBAAsB;IACtB,WAAW;IACX,SAAS;IACT,oBAAoB;IACpB,SAAS;IACT,YAAY;IACZ,SAAS;IACT,YAAY;IACZ,MAAM;IACN,SAAS;IACT,WAAW;IACX,UAAU;IACV,yBAAyB;IACzB,cAAc;IACd,QAAQ;IACR,WAAW;IACX,UAAU;IACV,aAAa;IACb,UAAU;AACZ;AAEO,MAAM,uBAAuB;IAClC,YAAY;IACZ,oBAAoB;AACtB", "debugId": null}}, {"offset": {"line": 8953, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/pipedream/utils.ts"], "sourcesContent": ["import type { PipedreamApp } from '@/hooks/react-query/pipedream/utils';\r\nimport type { PipedreamProfile } from '@/components/agents/pipedream/pipedream-types';\r\nimport { categoryEmojis, PAGINATION_CONSTANTS } from './constants';\r\nimport type { ConnectedApp } from './types';\r\n\r\nexport const getCategoriesFromApps = (apps: PipedreamApp[]) => {\r\n  const categorySet = new Set<string>();\r\n  apps.forEach((app) => {\r\n    app.categories.forEach(cat => categorySet.add(cat));\r\n  });\r\n  const sortedCategories = Array.from(categorySet).sort();\r\n  return ['All', ...sortedCategories];\r\n};\r\n\r\nexport const getPopularApps = (apps: PipedreamApp[]) => {\r\n  return apps\r\n    .filter((app) => app.featured_weight > 0)\r\n    .sort((a, b) => b.featured_weight - a.featured_weight)\r\n    .slice(0, PAGINATION_CONSTANTS.POPULAR_APPS_COUNT);\r\n};\r\n\r\nexport const filterAppsByCategory = (apps: PipedreamApp[], category: string) => {\r\n  if (category === 'All') return apps;\r\n  return apps.filter(app => app.categories.includes(category));\r\n};\r\n\r\nexport const getAppCategoryCount = (apps: PipedreamApp[], category: string) => {\r\n  return category === 'All' \r\n    ? apps.length \r\n    : apps.filter(app => app.categories.includes(category)).length;\r\n};\r\n\r\nexport const createConnectedAppsFromProfiles = (\r\n  connectedProfiles: PipedreamProfile[],\r\n  allApps: PipedreamApp[]\r\n): ConnectedApp[] => {\r\n  const profilesByApp = connectedProfiles.reduce((acc, profile) => {\r\n    const key = profile.app_slug;\r\n    if (!acc[key]) {\r\n      acc[key] = [];\r\n    }\r\n    acc[key].push(profile);\r\n    return acc;\r\n  }, {} as Record<string, typeof connectedProfiles>);\r\n\r\n  return Object.entries(profilesByApp).map(([appSlug, profiles]) => {\r\n    const firstProfile = profiles[0];\r\n    const registryApp = allApps.find(app => \r\n      app.name_slug === firstProfile.app_slug || \r\n      app.name.toLowerCase() === firstProfile.app_name.toLowerCase()\r\n    );\r\n    \r\n    return {\r\n      id: `app_${appSlug}`,\r\n      name: firstProfile.app_name,\r\n      name_slug: firstProfile.app_slug,\r\n      auth_type: \"keys\",\r\n      description: `Access your ${firstProfile.app_name} workspace and tools`,\r\n      img_src: registryApp?.img_src || \"\",\r\n      custom_fields_json: registryApp?.custom_fields_json || \"[]\",\r\n      categories: registryApp?.categories || [],\r\n      featured_weight: 0,\r\n      connect: {\r\n        allowed_domains: registryApp?.connect?.allowed_domains || null,\r\n        base_proxy_target_url: registryApp?.connect?.base_proxy_target_url || \"\",\r\n        proxy_enabled: registryApp?.connect?.proxy_enabled || false,\r\n      },\r\n      connectedProfiles: profiles,\r\n      profileCount: profiles.length\r\n    } as ConnectedApp;\r\n  });\r\n};\r\n\r\nexport const getAgentPipedreamProfiles = (\r\n  agent: any,\r\n  profiles: PipedreamProfile[],\r\n  currentAgentId?: string\r\n) => {\r\n  if (!agent || !profiles || !currentAgentId) return [];\r\n  \r\n  const customMcps = agent.custom_mcps || [];\r\n  const pipedreamMcps = customMcps.filter((mcp: any) => \r\n    mcp.config?.profile_id && mcp.config?.url?.includes('pipedream')\r\n  );\r\n  \r\n  const profileIds = pipedreamMcps.map((mcp: any) => mcp.config?.profile_id).filter(Boolean);\r\n  const usedProfiles = profiles.filter(profile => \r\n    profileIds.includes(profile.profile_id)\r\n  );\r\n\r\n  return usedProfiles.map(profile => {\r\n    const mcpConfig = pipedreamMcps.find((mcp: any) => mcp.config?.profile_id === profile.profile_id);\r\n    return {\r\n      ...profile,\r\n      enabledTools: mcpConfig?.enabledTools || [],\r\n      toolsCount: mcpConfig?.enabledTools?.length || 0\r\n    };\r\n  });\r\n};\r\n\r\nexport const getCategoryEmoji = (category: string): string => {\r\n  return categoryEmojis[category] || '🔧';\r\n}; "], "names": [], "mappings": ";;;;;;;;;AAEA;;AAGO,MAAM,wBAAwB,CAAC;IACpC,MAAM,cAAc,IAAI;IACxB,KAAK,OAAO,CAAC,CAAC;QACZ,IAAI,UAAU,CAAC,OAAO,CAAC,CAAA,MAAO,YAAY,GAAG,CAAC;IAChD;IACA,MAAM,mBAAmB,MAAM,IAAI,CAAC,aAAa,IAAI;IACrD,OAAO;QAAC;WAAU;KAAiB;AACrC;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,KACJ,MAAM,CAAC,CAAC,MAAQ,IAAI,eAAe,GAAG,GACtC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,eAAe,GAAG,EAAE,eAAe,EACpD,KAAK,CAAC,GAAG,qJAAA,CAAA,uBAAoB,CAAC,kBAAkB;AACrD;AAEO,MAAM,uBAAuB,CAAC,MAAsB;IACzD,IAAI,aAAa,OAAO,OAAO;IAC/B,OAAO,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,QAAQ,CAAC;AACpD;AAEO,MAAM,sBAAsB,CAAC,MAAsB;IACxD,OAAO,aAAa,QAChB,KAAK,MAAM,GACX,KAAK,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,MAAM;AAClE;AAEO,MAAM,kCAAkC,CAC7C,mBACA;IAEA,MAAM,gBAAgB,kBAAkB,MAAM,CAAC,CAAC,KAAK;QACnD,MAAM,MAAM,QAAQ,QAAQ;QAC5B,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE;YACb,GAAG,CAAC,IAAI,GAAG,EAAE;QACf;QACA,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;QACd,OAAO;IACT,GAAG,CAAC;IAEJ,OAAO,OAAO,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,SAAS,SAAS;QAC3D,MAAM,eAAe,QAAQ,CAAC,EAAE;QAChC,MAAM,cAAc,QAAQ,IAAI,CAAC,CAAA,MAC/B,IAAI,SAAS,KAAK,aAAa,QAAQ,IACvC,IAAI,IAAI,CAAC,WAAW,OAAO,aAAa,QAAQ,CAAC,WAAW;QAG9D,OAAO;YACL,IAAI,CAAC,IAAI,EAAE,SAAS;YACpB,MAAM,aAAa,QAAQ;YAC3B,WAAW,aAAa,QAAQ;YAChC,WAAW;YACX,aAAa,CAAC,YAAY,EAAE,aAAa,QAAQ,CAAC,oBAAoB,CAAC;YACvE,SAAS,aAAa,WAAW;YACjC,oBAAoB,aAAa,sBAAsB;YACvD,YAAY,aAAa,cAAc,EAAE;YACzC,iBAAiB;YACjB,SAAS;gBACP,iBAAiB,aAAa,SAAS,mBAAmB;gBAC1D,uBAAuB,aAAa,SAAS,yBAAyB;gBACtE,eAAe,aAAa,SAAS,iBAAiB;YACxD;YACA,mBAAmB;YACnB,cAAc,SAAS,MAAM;QAC/B;IACF;AACF;AAEO,MAAM,4BAA4B,CACvC,OACA,UACA;IAEA,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,gBAAgB,OAAO,EAAE;IAErD,MAAM,aAAa,MAAM,WAAW,IAAI,EAAE;IAC1C,MAAM,gBAAgB,WAAW,MAAM,CAAC,CAAC,MACvC,IAAI,MAAM,EAAE,cAAc,IAAI,MAAM,EAAE,KAAK,SAAS;IAGtD,MAAM,aAAa,cAAc,GAAG,CAAC,CAAC,MAAa,IAAI,MAAM,EAAE,YAAY,MAAM,CAAC;IAClF,MAAM,eAAe,SAAS,MAAM,CAAC,CAAA,UACnC,WAAW,QAAQ,CAAC,QAAQ,UAAU;IAGxC,OAAO,aAAa,GAAG,CAAC,CAAA;QACtB,MAAM,YAAY,cAAc,IAAI,CAAC,CAAC,MAAa,IAAI,MAAM,EAAE,eAAe,QAAQ,UAAU;QAChG,OAAO;YACL,GAAG,OAAO;YACV,cAAc,WAAW,gBAAgB,EAAE;YAC3C,YAAY,WAAW,cAAc,UAAU;QACjD;IACF;AACF;AAEO,MAAM,mBAAmB,CAAC;IAC/B,OAAO,qJAAA,CAAA,iBAAc,CAAC,SAAS,IAAI;AACrC", "debugId": null}}, {"offset": {"line": 9041, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/pipedream/_components/AppCard.tsx"], "sourcesContent": ["import React, { useState, useMemo } from 'react';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Card, CardContent } from '@/components/ui/card';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { \r\n  DropdownMenu, \r\n  DropdownMenuContent, \r\n  DropdownMenuItem, \r\n  DropdownMenuTrigger \r\n} from '@/components/ui/dropdown-menu';\r\nimport { Plus, Settings, Zap, Bot, ChevronDown } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { getCategoryEmoji } from '../utils';\r\nimport type { AppCardProps } from '../types';\r\nimport { usePipedreamProfiles } from '@/hooks/react-query/pipedream/use-pipedream-profiles';\r\n\r\nexport const AppCard: React.FC<AppCardProps> = ({ \r\n  app, \r\n  compact = false, \r\n  mode = 'full',\r\n  currentAgentId,\r\n  agentName,\r\n  agentPipedreamProfiles = [],\r\n  onAppSelected,\r\n  onConnectApp,\r\n  onConfigureTools,\r\n  handleCategorySelect\r\n}) => {\r\n  const { data: profiles } = usePipedreamProfiles();\r\n  const appProfiles = useMemo(() => {\r\n    return profiles?.filter(p => p.app_slug === app.name_slug && p.is_active) || [];\r\n  }, [profiles, app.name_slug]);\r\n\r\n  const connectedProfiles = useMemo(() => {\r\n    if ('connectedProfiles' in app && app.connectedProfiles) {\r\n      return app.connectedProfiles;\r\n    }\r\n    return appProfiles.filter(p => p.is_connected);\r\n  }, [appProfiles, app]);\r\n  \r\n  const agentProfiles = useMemo(() => {\r\n    if (!currentAgentId) return [];\r\n    return agentPipedreamProfiles.filter(p => p.app_slug === app.name_slug);\r\n  }, [app.name_slug, currentAgentId, agentPipedreamProfiles]);\r\n\r\n  const handleConnectClick = (e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n    if (mode === 'simple') {\r\n      onAppSelected?.({ app_slug: app.name_slug, app_name: app.name });\r\n    } else {\r\n      onConnectApp?.(app);\r\n    }\r\n  };\r\n\r\n  const handleConfigureClick = (profile: any) => {\r\n    onConfigureTools?.(profile);\r\n  };\r\n\r\n  const handleCategoryClick = (e: React.MouseEvent, category: string) => {\r\n    e.stopPropagation();\r\n    handleCategorySelect?.(category);\r\n  };\r\n\r\n  const totalToolsCount = agentProfiles.reduce((sum, profile) => sum + (profile.toolsCount || 0), 0);\r\n\r\n  return (\r\n    <Card className=\"group h-full\">\r\n      <CardContent className=\"h-full flex flex-col\">\r\n        <div className=\"flex items-start gap-3 mb-3\">\r\n          <div className=\"flex-shrink-0 relative\">\r\n            <div className=\"h-8 w-8 rounded-lg bg-muted flex items-center justify-center text-primary font-semibold overflow-hidden\">\r\n              {app.img_src ? (\r\n                <img\r\n                  src={app.img_src}\r\n                  alt={app.name}\r\n                  className=\"w-full h-full object-cover\"\r\n                  onError={(e) => {\r\n                    const target = e.target as HTMLImageElement;\r\n                    target.style.display = 'none';\r\n                    target.nextElementSibling?.classList.remove('hidden');\r\n                  }}\r\n                />\r\n              ) : null}\r\n              <span className={cn(\r\n                \"font-semibold text-sm\",\r\n                app.img_src ? \"hidden\" : \"block\"\r\n              )}>\r\n                {app.name.charAt(0).toUpperCase()}\r\n              </span>\r\n            </div>\r\n            {connectedProfiles.length > 0 && (\r\n              <div className=\"absolute -top-1 -right-1 h-3 w-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800\" />\r\n            )}\r\n          </div>\r\n          \r\n          <div className=\"flex-1 min-w-0\">\r\n            <h3 className=\"font-medium text-sm text-foreground mb-1 truncate\">\r\n              {app.name}\r\n            </h3>\r\n            <p className=\"text-xs text-muted-foreground line-clamp-2 leading-tight\">\r\n              {app.description}\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        {!compact && app.categories.length > 0 && (\r\n          <div className=\"flex flex-wrap gap-1 mb-3\">\r\n            {app.categories.slice(0, 2).map((category) => (\r\n              <Badge \r\n                key={category} \r\n                variant=\"outline\" \r\n                className=\"text-xs px-2 py-0.5 cursor-pointer hover:bg-muted/50 transition-colors\"\r\n                onClick={(e) => handleCategoryClick(e, category)}\r\n              >\r\n                {getCategoryEmoji(category)} {category}\r\n              </Badge>\r\n            ))}\r\n            {app.categories.length > 2 && (\r\n              <Badge variant=\"outline\" className=\"text-xs px-2 py-0.5\">\r\n                +{app.categories.length - 2}\r\n              </Badge>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {agentProfiles.length > 0 && (\r\n          <div className=\"mb-3\">\r\n            <DropdownMenu>\r\n              <DropdownMenuTrigger asChild>\r\n                <Button variant=\"outline\" className=\"h-8 w-full justify-between text-xs\">\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Bot className=\"h-3 w-3 text-primary\" />\r\n                    <span className=\"text-xs font-medium text-foreground\">\r\n                      {agentProfiles.length} {agentProfiles.length === 1 ? 'Profile' : 'Profiles'} • {totalToolsCount} tools\r\n                    </span>\r\n                  </div>\r\n                  <ChevronDown className=\"h-3 w-3 text-muted-foreground\" />\r\n                </Button>\r\n              </DropdownMenuTrigger>\r\n              <DropdownMenuContent align=\"start\" className=\"w-full\">\r\n                {agentProfiles.map((profile) => (\r\n                  <DropdownMenuItem \r\n                    key={profile.profile_id} \r\n                    onClick={() => handleConfigureClick(profile)}\r\n                    className=\"cursor-pointer\"\r\n                  >\r\n                    <div className=\"flex items-center justify-between w-full\">\r\n                      <div className=\"flex items-center gap-2\">\r\n                        <Settings className=\"h-3 w-3\" />\r\n                        <span className=\"font-medium\">{profile.profile_name}</span>\r\n                      </div>\r\n                      <Badge variant=\"outline\" className=\"text-xs px-1 py-0\">\r\n                        {profile.toolsCount} tools\r\n                      </Badge>\r\n                    </div>\r\n                  </DropdownMenuItem>\r\n                ))}\r\n              </DropdownMenuContent>\r\n            </DropdownMenu>\r\n          </div>\r\n        )}\r\n\r\n        <div className=\"flex-1\" />\r\n        \r\n        <div className=\"flex items-center gap-2 mt-auto\">\r\n          {mode === 'simple' ? (\r\n            <Button\r\n              size=\"sm\"\r\n              onClick={handleConnectClick}\r\n              className=\"flex-1\"\r\n            >\r\n              <Plus className=\"h-3 w-3\" />\r\n              Connect\r\n            </Button>\r\n          ) : mode === 'profile-only' ? (\r\n            <Button\r\n              size=\"sm\"\r\n              onClick={handleConnectClick}\r\n              variant={connectedProfiles.length > 0 ? \"outline\" : \"default\"}\r\n              className=\"flex-1\"\r\n            >\r\n              <Plus className=\"h-3 w-3\" />\r\n              {connectedProfiles.length > 0 ? 'Add Profile' : 'Connect'}\r\n            </Button>\r\n          ) : (\r\n            <Button\r\n              size=\"sm\"\r\n              onClick={handleConnectClick}\r\n              className={cn(\r\n                \"flex-1\",\r\n                connectedProfiles.length > 0 && \"bg-purple-600 hover:bg-purple-700\"\r\n              )}\r\n            >\r\n              {connectedProfiles.length > 0 ? (\r\n                <>\r\n                  <Zap className=\"h-3 w-3\" />\r\n                  Add Tools\r\n                </>\r\n              ) : (\r\n                <>\r\n                  <Plus className=\"h-3 w-3\" />\r\n                  Connect\r\n                </>\r\n              )}\r\n            </Button>\r\n          )}\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  );\r\n}; "], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AAMA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;;;;;;;;;;;AAEO,MAAM,UAAkC,CAAC,EAC9C,GAAG,EACH,UAAU,KAAK,EACf,OAAO,MAAM,EACb,cAAc,EACd,SAAS,EACT,yBAAyB,EAAE,EAC3B,aAAa,EACb,YAAY,EACZ,gBAAgB,EAChB,oBAAoB,EACrB;IACC,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,uBAAoB,AAAD;IAC9C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC1B,OAAO,UAAU,OAAO,CAAA,IAAK,EAAE,QAAQ,KAAK,IAAI,SAAS,IAAI,EAAE,SAAS,KAAK,EAAE;IACjF,GAAG;QAAC;QAAU,IAAI,SAAS;KAAC;IAE5B,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAChC,IAAI,uBAAuB,OAAO,IAAI,iBAAiB,EAAE;YACvD,OAAO,IAAI,iBAAiB;QAC9B;QACA,OAAO,YAAY,MAAM,CAAC,CAAA,IAAK,EAAE,YAAY;IAC/C,GAAG;QAAC;QAAa;KAAI;IAErB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5B,IAAI,CAAC,gBAAgB,OAAO,EAAE;QAC9B,OAAO,uBAAuB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,IAAI,SAAS;IACxE,GAAG;QAAC,IAAI,SAAS;QAAE;QAAgB;KAAuB;IAE1D,MAAM,qBAAqB,CAAC;QAC1B,EAAE,eAAe;QACjB,IAAI,SAAS,UAAU;YACrB,gBAAgB;gBAAE,UAAU,IAAI,SAAS;gBAAE,UAAU,IAAI,IAAI;YAAC;QAChE,OAAO;YACL,eAAe;QACjB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,mBAAmB;IACrB;IAEA,MAAM,sBAAsB,CAAC,GAAqB;QAChD,EAAE,eAAe;QACjB,uBAAuB;IACzB;IAEA,MAAM,kBAAkB,cAAc,MAAM,CAAC,CAAC,KAAK,UAAY,MAAM,CAAC,QAAQ,UAAU,IAAI,CAAC,GAAG;IAEhG,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;;8BACrB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCACZ,IAAI,OAAO,iBACV,8OAAC;4CACC,KAAK,IAAI,OAAO;4CAChB,KAAK,IAAI,IAAI;4CACb,WAAU;4CACV,SAAS,CAAC;gDACR,MAAM,SAAS,EAAE,MAAM;gDACvB,OAAO,KAAK,CAAC,OAAO,GAAG;gDACvB,OAAO,kBAAkB,EAAE,UAAU,OAAO;4CAC9C;;;;;mDAEA;sDACJ,8OAAC;4CAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,yBACA,IAAI,OAAO,GAAG,WAAW;sDAExB,IAAI,IAAI,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;;gCAGlC,kBAAkB,MAAM,GAAG,mBAC1B,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAInB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,IAAI,IAAI;;;;;;8CAEX,8OAAC;oCAAE,WAAU;8CACV,IAAI,WAAW;;;;;;;;;;;;;;;;;;gBAKrB,CAAC,WAAW,IAAI,UAAU,CAAC,MAAM,GAAG,mBACnC,8OAAC;oBAAI,WAAU;;wBACZ,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,yBAC/B,8OAAC,iIAAA,CAAA,QAAK;gCAEJ,SAAQ;gCACR,WAAU;gCACV,SAAS,CAAC,IAAM,oBAAoB,GAAG;;oCAEtC,CAAA,GAAA,iJAAA,CAAA,mBAAgB,AAAD,EAAE;oCAAU;oCAAE;;+BALzB;;;;;wBAQR,IAAI,UAAU,CAAC,MAAM,GAAG,mBACvB,8OAAC,iIAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAU,WAAU;;gCAAsB;gCACrD,IAAI,UAAU,CAAC,MAAM,GAAG;;;;;;;;;;;;;gBAMjC,cAAc,MAAM,GAAG,mBACtB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,4IAAA,CAAA,eAAY;;0CACX,8OAAC,4IAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,WAAU;;sDAClC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,8OAAC;oDAAK,WAAU;;wDACb,cAAc,MAAM;wDAAC;wDAAE,cAAc,MAAM,KAAK,IAAI,YAAY;wDAAW;wDAAI;wDAAgB;;;;;;;;;;;;;sDAGpG,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAG3B,8OAAC,4IAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAQ,WAAU;0CAC1C,cAAc,GAAG,CAAC,CAAC,wBAClB,8OAAC,4IAAA,CAAA,mBAAgB;wCAEf,SAAS,IAAM,qBAAqB;wCACpC,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAK,WAAU;sEAAe,QAAQ,YAAY;;;;;;;;;;;;8DAErD,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;;wDAChC,QAAQ,UAAU;wDAAC;;;;;;;;;;;;;uCAVnB,QAAQ,UAAU;;;;;;;;;;;;;;;;;;;;;8BAoBnC,8OAAC;oBAAI,WAAU;;;;;;8BAEf,8OAAC;oBAAI,WAAU;8BACZ,SAAS,yBACR,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS;wBACT,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;+BAG5B,SAAS,+BACX,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS;wBACT,SAAS,kBAAkB,MAAM,GAAG,IAAI,YAAY;wBACpD,WAAU;;0CAEV,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BACf,kBAAkB,MAAM,GAAG,IAAI,gBAAgB;;;;;;6CAGlD,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS;wBACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,UACA,kBAAkB,MAAM,GAAG,KAAK;kCAGjC,kBAAkB,MAAM,GAAG,kBAC1B;;8CACE,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;gCAAY;;yDAI7B;;8CACE,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;;;;;;;AAU9C", "debugId": null}}, {"offset": {"line": 9465, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/pipedream/_components/CategorySidebar.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { ChevronRight } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { getCategoryEmoji, getAppCategoryCount } from '../utils';\r\nimport type { CategorySidebarProps } from '../types';\r\n\r\nexport const CategorySidebar: React.FC<CategorySidebarProps> = ({\r\n  isCollapsed,\r\n  onToggle,\r\n  categories,\r\n  selectedCategory,\r\n  onCategorySelect,\r\n  allApps\r\n}) => {\r\n  return (\r\n    <div className=\"border-r bg-sidebar flex-shrink-0 sticky top-0 h-[calc(100vh-12vh)] overflow-hidden\">\r\n      <div className=\"p-3 border-b flex-shrink-0\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <div className=\"flex items-center gap-2 min-w-0\">\r\n            <div className={cn(\r\n              \"overflow-hidden transition-all duration-300 ease-in-out\",\r\n              isCollapsed ? \"w-0 opacity-0\" : \"w-auto opacity-100\"\r\n            )}>\r\n              <h3 className=\"font-semibold text-sm whitespace-nowrap\">Categories</h3>\r\n            </div>\r\n          </div>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"icon\"\r\n            onClick={onToggle}\r\n            className=\"h-7 w-7\"\r\n          >\r\n            <ChevronRight className={cn(\r\n              \"h-3 w-3 transition-transform duration-300 ease-in-out\",\r\n              isCollapsed ? \"rotate-0\" : \"rotate-180\"\r\n            )} />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n      \r\n      <div className=\"p-2 space-y-0.5 flex-1 overflow-y-auto\">\r\n        {categories.map((category) => {\r\n          const isActive = selectedCategory === category;\r\n          const emoji = getCategoryEmoji(category);\r\n          return (\r\n            <button\r\n              key={category}\r\n              onClick={() => onCategorySelect(category)}\r\n              className={cn(\r\n                \"w-full flex items-center gap-2 px-2 py-1.5 rounded-md text-left transition-all duration-200 overflow-hidden\",\r\n                isActive \r\n                  ? \"bg-primary/10 text-primary\" \r\n                  : \"hover:bg-primary/5\"\r\n              )}\r\n              title={isCollapsed ? category : undefined}\r\n            >\r\n              <span className=\"text-sm flex-shrink-0\">{emoji}</span>\r\n              <div className={cn(\r\n                \"flex items-center justify-between flex-1 min-w-0 overflow-hidden transition-all duration-300 ease-in-out\",\r\n                isCollapsed ? \"w-0 opacity-0\" : \"w-auto opacity-100\"\r\n              )}>\r\n                <span className=\"text-sm truncate whitespace-nowrap\">{category}</span>\r\n              </div>\r\n            </button>\r\n          );\r\n        })}\r\n      </div>\r\n    </div>\r\n  );\r\n}; "], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;;;;;;AAGO,MAAM,kBAAkD,CAAC,EAC9D,WAAW,EACX,QAAQ,EACR,UAAU,EACV,gBAAgB,EAChB,gBAAgB,EAChB,OAAO,EACR;IACC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,2DACA,cAAc,kBAAkB;0CAEhC,cAAA,8OAAC;oCAAG,WAAU;8CAA0C;;;;;;;;;;;;;;;;sCAG5D,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACxB,yDACA,cAAc,aAAa;;;;;;;;;;;;;;;;;;;;;;0BAMnC,8OAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,WAAW,qBAAqB;oBACtC,MAAM,QAAQ,CAAA,GAAA,iJAAA,CAAA,mBAAgB,AAAD,EAAE;oBAC/B,qBACE,8OAAC;wBAEC,SAAS,IAAM,iBAAiB;wBAChC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+GACA,WACI,+BACA;wBAEN,OAAO,cAAc,WAAW;;0CAEhC,8OAAC;gCAAK,WAAU;0CAAyB;;;;;;0CACzC,8OAAC;gCAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,4GACA,cAAc,kBAAkB;0CAEhC,cAAA,8OAAC;oCAAK,WAAU;8CAAsC;;;;;;;;;;;;uBAfnD;;;;;gBAmBX;;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 9595, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/pipedream/_components/PipedreamHeader.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Search, Sparkles } from 'lucide-react';\r\nimport { AgentSelector } from '../../../thread/chat-input/agent-selector';\r\nimport type { PipedreamHeaderProps } from '../types';\r\n\r\nexport const PipedreamHeader: React.FC<PipedreamHeaderProps> = ({\r\n  search,\r\n  onSearchChange,\r\n  showAgentSelector,\r\n  currentAgentId,\r\n  onAgentChange\r\n}) => {\r\n  return (\r\n    <div className=\"absolute top-0 left-0 right-0 z-10 border-b px-4 py-3\">\r\n      <div className=\"flex items-center justify-between\">\r\n        <div className=\"flex items-center gap-3\">\r\n          <div>\r\n            <div className=\"flex items-center gap-2\">\r\n              <h1 className=\"text-xl font-semibold text-gray-900 dark:text-white\">\r\n                Integrations\r\n              </h1>\r\n              <Badge variant=\"secondary\" className=\"bg-blue-50 text-blue-700 border-blue-200 dark:border-blue-900 dark:bg-blue-900/20 dark:text-blue-400 text-xs\">\r\n                <Sparkles className=\"h-3 w-3\" />\r\n                New\r\n              </Badge>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"mt-3 flex items-center gap-3\">\r\n        <div className=\"relative max-w-md\">\r\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\" />\r\n          <Input\r\n            placeholder=\"Search 2700+ apps...\"\r\n            value={search}\r\n            onChange={(e) => onSearchChange(e.target.value)}\r\n            className=\"pl-10 h-9 focus:border-primary/50 focus:ring-primary/20 rounded-lg text-sm\"\r\n          />\r\n        </div>\r\n        {showAgentSelector && (\r\n          <div className=\"flex items-center gap-2\">\r\n            <AgentSelector\r\n              selectedAgentId={currentAgentId}\r\n              onAgentSelect={onAgentChange}\r\n            />\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n}; "], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AACA;;;;;;AAGO,MAAM,kBAAkD,CAAC,EAC9D,MAAM,EACN,cAAc,EACd,iBAAiB,EACjB,cAAc,EACd,aAAa,EACd;IACC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;kCACC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;8CAGpE,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;sDACnC,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,WAAU;;;;;;;;;;;;oBAGb,mCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kKAAA,CAAA,gBAAa;4BACZ,iBAAiB;4BACjB,eAAe;;;;;;;;;;;;;;;;;;;;;;;AAO7B", "debugId": null}}, {"offset": {"line": 9731, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/pipedream/_components/ConnectedAppsSection.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { User } from 'lucide-react';\r\nimport type { ConnectedAppsSectionProps } from '../types';\r\nimport { AppCard } from './AppCard';\r\n\r\n\r\nexport const ConnectedAppsSection: React.FC<ConnectedAppsSectionProps> = ({\r\n  connectedApps,\r\n  showAgentSelector,\r\n  currentAgentId,\r\n  agent,\r\n  agentPipedreamProfiles = [],\r\n  mode = 'full',\r\n  onAppSelected,\r\n  onConnectApp,\r\n  onConfigureTools,\r\n  onCategorySelect\r\n}) => {\r\n  const getSectionTitle = () => {\r\n    if (showAgentSelector && currentAgentId && agent) {\r\n      return 'Available Connections';\r\n    }\r\n    return mode === 'profile-only' ? 'Connected Accounts' : 'My Connections';\r\n  };\r\n\r\n  const getUsedProfilesCount = () => {\r\n    return agentPipedreamProfiles.length;\r\n  };\r\n\r\n  return (\r\n    <div className=\"mb-6\">\r\n      <div className=\"flex items-center gap-2 mb-3\">\r\n        <User className=\"h-4 w-4 text-green-600 dark:text-green-400\" />\r\n        <h2 className=\"text-md font-semibold text-gray-900 dark:text-white\">\r\n          {getSectionTitle()}\r\n        </h2>\r\n        <Badge variant=\"secondary\" className=\"bg-green-50 text-green-700 border-green-200 dark:border-green-900 dark:bg-green-900/20 dark:text-green-400 text-xs\">\r\n          {connectedApps.length} connected\r\n        </Badge>\r\n        {showAgentSelector && currentAgentId && getUsedProfilesCount() > 0 && (\r\n          <Badge variant=\"secondary\" className=\"bg-primary/10 text-primary border-primary/20 text-xs\">\r\n            {getUsedProfilesCount()} in use\r\n          </Badge>\r\n        )}\r\n      </div>\r\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-3\">\r\n        {connectedApps.map((app) => (\r\n          <AppCard \r\n            key={`${app.id}-${currentAgentId || 'default'}`} \r\n            app={app}\r\n            mode={mode}\r\n            currentAgentId={currentAgentId}\r\n            agentName={agent?.name}\r\n            agentPipedreamProfiles={agentPipedreamProfiles}\r\n            onAppSelected={onAppSelected}\r\n            onConnectApp={onConnectApp}\r\n            onConfigureTools={onConfigureTools}\r\n            handleCategorySelect={onCategorySelect}\r\n          />\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n}; "], "names": [], "mappings": ";;;;AACA;AACA;AAEA;;;;;AAGO,MAAM,uBAA4D,CAAC,EACxE,aAAa,EACb,iBAAiB,EACjB,cAAc,EACd,KAAK,EACL,yBAAyB,EAAE,EAC3B,OAAO,MAAM,EACb,aAAa,EACb,YAAY,EACZ,gBAAgB,EAChB,gBAAgB,EACjB;IACC,MAAM,kBAAkB;QACtB,IAAI,qBAAqB,kBAAkB,OAAO;YAChD,OAAO;QACT;QACA,OAAO,SAAS,iBAAiB,uBAAuB;IAC1D;IAEA,MAAM,uBAAuB;QAC3B,OAAO,uBAAuB,MAAM;IACtC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;kCAChB,8OAAC;wBAAG,WAAU;kCACX;;;;;;kCAEH,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;;4BAClC,cAAc,MAAM;4BAAC;;;;;;;oBAEvB,qBAAqB,kBAAkB,yBAAyB,mBAC/D,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;;4BAClC;4BAAuB;;;;;;;;;;;;;0BAI9B,8OAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,oBAClB,8OAAC,mKAAA,CAAA,UAAO;wBAEN,KAAK;wBACL,MAAM;wBACN,gBAAgB;wBAChB,WAAW,OAAO;wBAClB,wBAAwB;wBACxB,eAAe;wBACf,cAAc;wBACd,kBAAkB;wBAClB,sBAAsB;uBATjB,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,kBAAkB,WAAW;;;;;;;;;;;;;;;;AAe3D", "debugId": null}}, {"offset": {"line": 9838, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/pipedream/_components/AppsGrid.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Badge } from '@/components/ui/badge';\r\nimport { Loader2, TrendingUp, Star } from 'lucide-react';\r\nimport { AppCard } from './AppCard';\r\nimport { getCategoryEmoji } from '../utils';\r\nimport type { AppsGridProps } from '../types';\r\n\r\nexport const AppsGrid: React.FC<AppsGridProps> = ({\r\n  apps,\r\n  selectedCategory,\r\n  mode = 'full',\r\n  isLoading,\r\n  currentAgentId,\r\n  agent,\r\n  agentPipedreamProfiles = [],\r\n  onAppSelected,\r\n  onConnectApp,\r\n  onConfigureTools,\r\n  onCategorySelect\r\n}) => {\r\n  const getSectionTitle = () => {\r\n    if (selectedCategory === 'All') {\r\n      return mode === 'profile-only' ? 'Available Apps' : 'Popular';\r\n    }\r\n    return selectedCategory;\r\n  };\r\n\r\n  const getSectionIcon = () => {\r\n    if (selectedCategory === 'All') {\r\n      return <TrendingUp className=\"h-4 w-4 text-orange-600 dark:text-orange-400\" />;\r\n    }\r\n    return <span className=\"text-lg\">{getCategoryEmoji(selectedCategory)}</span>;\r\n  };\r\n\r\n  const getSectionBadge = () => {\r\n    if (selectedCategory === 'All') {\r\n      return (\r\n        <Badge variant=\"secondary\" className=\"bg-orange-50 text-orange-700 border-orange-200 dark:border-orange-900 dark:bg-orange-900/20 dark:text-orange-400 text-xs\">\r\n          <Star className=\"h-3 w-3 mr-1\" />\r\n          {mode === 'profile-only' ? 'Connect' : 'Recommended'}\r\n        </Badge>\r\n      );\r\n    }\r\n    return null;\r\n  };\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"flex items-center justify-center py-8\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <Loader2 className=\"h-4 w-4 animate-spin text-primary\" />\r\n          <span className=\"text-sm text-gray-600 dark:text-gray-400\">Loading integrations...</span>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <div className=\"mb-4\">\r\n        <div className=\"flex items-center gap-2 mb-3\">\r\n          {getSectionIcon()}\r\n          <h2 className=\"text-md font-medium text-gray-900 dark:text-white\">\r\n            {getSectionTitle()}\r\n          </h2>\r\n          {getSectionBadge()}\r\n        </div>\r\n      </div>\r\n      \r\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\">\r\n        {apps.map((app) => (\r\n          <AppCard \r\n            key={`${app.id}-${currentAgentId || 'default'}`} \r\n            app={app}\r\n            mode={mode}\r\n            currentAgentId={currentAgentId}\r\n            agentName={agent?.name}\r\n            agentPipedreamProfiles={agentPipedreamProfiles}\r\n            onAppSelected={onAppSelected}\r\n            onConnectApp={onConnectApp}\r\n            onConfigureTools={onConfigureTools}\r\n            handleCategorySelect={onCategorySelect}\r\n          />\r\n        ))}\r\n      </div>\r\n    </>\r\n  );\r\n}; "], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AACA;AACA;;;;;;AAGO,MAAM,WAAoC,CAAC,EAChD,IAAI,EACJ,gBAAgB,EAChB,OAAO,MAAM,EACb,SAAS,EACT,cAAc,EACd,KAAK,EACL,yBAAyB,EAAE,EAC3B,aAAa,EACb,YAAY,EACZ,gBAAgB,EAChB,gBAAgB,EACjB;IACC,MAAM,kBAAkB;QACtB,IAAI,qBAAqB,OAAO;YAC9B,OAAO,SAAS,iBAAiB,mBAAmB;QACtD;QACA,OAAO;IACT;IAEA,MAAM,iBAAiB;QACrB,IAAI,qBAAqB,OAAO;YAC9B,qBAAO,8OAAC,kNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;QAC/B;QACA,qBAAO,8OAAC;YAAK,WAAU;sBAAW,CAAA,GAAA,iJAAA,CAAA,mBAAgB,AAAD,EAAE;;;;;;IACrD;IAEA,MAAM,kBAAkB;QACtB,IAAI,qBAAqB,OAAO;YAC9B,qBACE,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAY,WAAU;;kCACnC,8OAAC,kMAAA,CAAA,OAAI;wBAAC,WAAU;;;;;;oBACf,SAAS,iBAAiB,YAAY;;;;;;;QAG7C;QACA,OAAO;IACT;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAK,WAAU;kCAA2C;;;;;;;;;;;;;;;;;IAInE;IAEA,qBACE;;0BACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBACZ;sCACD,8OAAC;4BAAG,WAAU;sCACX;;;;;;wBAEF;;;;;;;;;;;;0BAIL,8OAAC;gBAAI,WAAU;0BACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC,mKAAA,CAAA,UAAO;wBAEN,KAAK;wBACL,MAAM;wBACN,gBAAgB;wBAChB,WAAW,OAAO;wBAClB,wBAAwB;wBACxB,eAAe;wBACf,cAAc;wBACd,kBAAkB;wBAClB,sBAAsB;uBATjB,GAAG,IAAI,EAAE,CAAC,CAAC,EAAE,kBAAkB,WAAW;;;;;;;;;;;;AAe3D", "debugId": null}}, {"offset": {"line": 9994, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/pipedream/_components/EmptyState.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Filter } from 'lucide-react';\r\nimport type { EmptyStateProps } from '../types';\r\n\r\nexport const EmptyState: React.FC<EmptyStateProps> = ({\r\n  selectedCategory,\r\n  mode = 'full',\r\n  onClearFilters\r\n}) => {\r\n  const getEmptyMessage = () => {\r\n    if (selectedCategory !== 'All') {\r\n      return `No ${mode === 'profile-only' ? 'apps' : 'integrations'} found in \"${selectedCategory}\" category. Try a different category or search term.`;\r\n    }\r\n    return mode === 'profile-only'\r\n      ? \"Try adjusting your search criteria or browse available apps.\"\r\n      : \"Try adjusting your search criteria or browse our popular integrations.\";\r\n  };\r\n\r\n  return (\r\n    <div className=\"text-center py-8\">\r\n      <div className=\"text-4xl mb-3\">🔍</div>\r\n      <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-2\">\r\n        No integrations found\r\n      </h3>\r\n      <p className=\"text-sm text-gray-600 dark:text-gray-400 mb-4 max-w-md mx-auto\">\r\n        {getEmptyMessage()}\r\n      </p>\r\n      <Button\r\n        onClick={onClearFilters}\r\n        variant=\"outline\"\r\n        size=\"sm\"\r\n        className=\"bg-primary hover:bg-primary/90 text-white\"\r\n      >\r\n        <Filter className=\"h-4 w-4 mr-2\" />\r\n        Clear Filters\r\n      </Button>\r\n    </div>\r\n  );\r\n}; "], "names": [], "mappings": ";;;;AACA;AACA;;;;AAGO,MAAM,aAAwC,CAAC,EACpD,gBAAgB,EAChB,OAAO,MAAM,EACb,cAAc,EACf;IACC,MAAM,kBAAkB;QACtB,IAAI,qBAAqB,OAAO;YAC9B,OAAO,CAAC,GAAG,EAAE,SAAS,iBAAiB,SAAS,eAAe,WAAW,EAAE,iBAAiB,oDAAoD,CAAC;QACpJ;QACA,OAAO,SAAS,iBACZ,iEACA;IACN;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BAAgB;;;;;;0BAC/B,8OAAC;gBAAG,WAAU;0BAAyD;;;;;;0BAGvE,8OAAC;gBAAE,WAAU;0BACV;;;;;;0BAEH,8OAAC,kIAAA,CAAA,SAAM;gBACL,SAAS;gBACT,SAAQ;gBACR,MAAK;gBACL,WAAU;;kCAEV,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;oBAAiB;;;;;;;;;;;;;AAK3C", "debugId": null}}, {"offset": {"line": 10070, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/pipedream/_components/PaginationControls.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { ChevronLeft, ChevronRight, Loader2 } from 'lucide-react';\r\nimport type { PaginationControlsProps } from '../types';\r\n\r\nexport const PaginationControls: React.FC<PaginationControlsProps> = ({\r\n  isLoading,\r\n  paginationHistory,\r\n  hasMore,\r\n  onPrevPage,\r\n  onNextPage\r\n}) => {\r\n  return (\r\n    <div className=\"absolute bottom-0 left-0 right-0 z-10 border-t px-4 py-3 bg-background\">\r\n      <div className=\"flex items-center justify-end gap-4\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button\r\n            onClick={onPrevPage}\r\n            disabled={isLoading || paginationHistory.length === 0}\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            className=\"h-9 px-3\"\r\n          >\r\n            <ChevronLeft className=\"h-4 w-4\" />\r\n          </Button>\r\n          \r\n          <div className=\"flex flex-col items-center gap-1 px-4 py-2 text-sm rounded-lg border\">\r\n            <div className=\"font-medium text-gray-900 dark:text-white\">\r\n              Page {paginationHistory.length + 1}\r\n            </div>\r\n          </div>\r\n          <Button\r\n            onClick={onNextPage}\r\n            disabled={isLoading || !hasMore}\r\n            variant=\"outline\"\r\n            size=\"sm\"\r\n            className=\"h-9 px-3\"\r\n          >\r\n            {isLoading ? (\r\n              <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n            ) : (\r\n              <ChevronRight className=\"h-4 w-4\" />\r\n            )}\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}; "], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;;;;AAGO,MAAM,qBAAwD,CAAC,EACpE,SAAS,EACT,iBAAiB,EACjB,OAAO,EACP,UAAU,EACV,UAAU,EACX;IACC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU,aAAa,kBAAkB,MAAM,KAAK;wBACpD,SAAQ;wBACR,MAAK;wBACL,WAAU;kCAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;kCAGzB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;gCAA4C;gCACnD,kBAAkB,MAAM,GAAG;;;;;;;;;;;;kCAGrC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,UAAU,aAAa,CAAC;wBACxB,SAAQ;wBACR,MAAK;wBACL,WAAU;kCAET,0BACC,8OAAC,iNAAA,CAAA,UAAO;4BAAC,WAAU;;;;;iDAEnB,8OAAC,sNAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtC", "debugId": null}}, {"offset": {"line": 10172, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/pipedream/_components/index.ts"], "sourcesContent": ["export { AppCard } from './AppCard';\r\nexport { CategorySidebar } from './CategorySidebar';\r\nexport { PipedreamHeader } from './PipedreamHeader';\r\nexport { ConnectedAppsSection } from './ConnectedAppsSection';\r\nexport { AppsGrid } from './AppsGrid';\r\nexport { EmptyState } from './EmptyState';\r\nexport { PaginationControls } from './PaginationControls'; "], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 10208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/agents/pipedream/pipedream-registry.tsx"], "sourcesContent": ["import React, { useState, useMemo } from 'react';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { X, Bot } from 'lucide-react';\r\nimport { usePipedreamApps } from '@/hooks/react-query/pipedream/use-pipedream';\r\nimport { usePipedreamProfiles } from '@/hooks/react-query/pipedream/use-pipedream-profiles';\r\nimport { useAgent } from '@/hooks/react-query/agents/use-agents';\r\nimport { PipedreamConnector } from './pipedream-connector';\r\nimport { ToolsManager } from '../mcp/tools-manager';\r\nimport { toast } from 'sonner';\r\nimport { cn } from '@/lib/utils';\r\nimport { useQueryClient } from '@tanstack/react-query';\r\nimport type { PipedreamApp } from '@/hooks/react-query/pipedream/utils';\r\nimport {\r\n  CategorySidebar,\r\n  PipedreamHeader,\r\n  ConnectedAppsSection,\r\n  AppsGrid,\r\n  EmptyState,\r\n  PaginationControls\r\n} from './_components';\r\nimport { PAGINATION_CONSTANTS } from './constants';\r\nimport {\r\n  getCategoriesFromApps,\r\n  createConnectedAppsFromProfiles,\r\n  getAgentPipedreamProfiles,\r\n  filterAppsByCategory\r\n} from './utils';\r\nimport type { PipedreamRegistryProps, ConnectedApp } from './types';\r\n\r\nexport const PipedreamRegistry: React.FC<PipedreamRegistryProps> = ({\r\n  onToolsSelected,\r\n  onAppSelected,\r\n  mode = 'full',\r\n  onClose,\r\n  showAgentSelector = false,\r\n  selectedAgentId,\r\n  onAgentChange\r\n}) => {\r\n  const [search, setSearch] = useState('');\r\n  const [selectedCategory, setSelectedCategory] = useState<string>('All');\r\n  const [after, setAfter] = useState<string | undefined>(undefined);\r\n  const [paginationHistory, setPaginationHistory] = useState<string[]>([]);\r\n  const [showStreamlinedConnector, setShowStreamlinedConnector] = useState(false);\r\n  const [selectedAppForConnection, setSelectedAppForConnection] = useState<PipedreamApp | null>(null);\r\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);\r\n  const [showToolsManager, setShowToolsManager] = useState(false);\r\n  const [selectedToolsProfile, setSelectedToolsProfile] = useState<{\r\n    profileId: string;\r\n    appName: string;\r\n    profileName: string;\r\n  } | null>(null);\r\n  \r\n  const [internalSelectedAgentId, setInternalSelectedAgentId] = useState<string | undefined>(selectedAgentId);\r\n\r\n  const queryClient = useQueryClient();\r\n  const { data: appsData, isLoading, error, refetch } = usePipedreamApps(after, search);\r\n  const { data: profiles } = usePipedreamProfiles();\r\n  \r\n  const currentAgentId = selectedAgentId ?? internalSelectedAgentId;\r\n  const { data: agent } = useAgent(currentAgentId || '');\r\n  \r\n  const { data: allAppsData } = usePipedreamApps(undefined, '');\r\n\r\n  React.useEffect(() => {\r\n    setInternalSelectedAgentId(selectedAgentId);\r\n  }, [selectedAgentId]);\r\n\r\n  const handleAgentSelect = (agentId: string | undefined) => {\r\n    if (onAgentChange) {\r\n      onAgentChange(agentId);\r\n    } else {\r\n      setInternalSelectedAgentId(agentId);\r\n    }\r\n    if (agentId !== currentAgentId) {\r\n      queryClient.invalidateQueries({ queryKey: ['agent'] });\r\n      if (agentId) {\r\n        queryClient.invalidateQueries({ queryKey: ['agent', agentId] });\r\n      }\r\n    }\r\n  };\r\n\r\n  const allApps = useMemo(() => {\r\n    return allAppsData?.apps || [];\r\n  }, [allAppsData?.apps]);\r\n\r\n  const agentPipedreamProfiles = useMemo(() => {\r\n    return getAgentPipedreamProfiles(agent, profiles, currentAgentId);\r\n  }, [agent, profiles, currentAgentId]);\r\n\r\n  const categories = useMemo(() => {\r\n    return getCategoriesFromApps(allApps);\r\n  }, [allApps]);\r\n\r\n  const connectedProfiles = useMemo(() => {\r\n    return profiles?.filter(p => p.is_connected) || [];\r\n  }, [profiles]);\r\n\r\n  const filteredAppsData = useMemo(() => {\r\n    if (!appsData) return appsData;\r\n    \r\n    if (selectedCategory === 'All') {\r\n      return appsData;\r\n    }\r\n    \r\n    const filteredApps = filterAppsByCategory(appsData.apps, selectedCategory);\r\n    \r\n    return {\r\n      ...appsData,\r\n      apps: filteredApps,\r\n      page_info: {\r\n        ...appsData.page_info,\r\n        count: filteredApps.length\r\n      }\r\n    };\r\n  }, [appsData, selectedCategory]);\r\n\r\n  const connectedApps: ConnectedApp[] = useMemo(() => {\r\n    return createConnectedAppsFromProfiles(connectedProfiles, allApps);\r\n  }, [connectedProfiles, allApps]);\r\n\r\n  const handleSearch = (value: string) => {\r\n    setSearch(value);\r\n    setAfter(undefined);\r\n    setPaginationHistory([]);\r\n  };\r\n\r\n  const handleCategorySelect = (category: string) => {\r\n    setSelectedCategory(category);\r\n    setAfter(undefined);\r\n    setPaginationHistory([]);\r\n  };\r\n\r\n  const handleNextPage = () => {\r\n    if (appsData?.page_info?.end_cursor) {\r\n      if (after) {\r\n        setPaginationHistory(prev => [...prev, after]);\r\n      } else {\r\n        setPaginationHistory(prev => [...prev, PAGINATION_CONSTANTS.FIRST_PAGE]);\r\n      }\r\n      setAfter(appsData.page_info.end_cursor);\r\n    }\r\n  };\r\n\r\n  const handlePrevPage = () => {\r\n    if (paginationHistory.length > 0) {\r\n      const newHistory = [...paginationHistory];\r\n      const previousCursor = newHistory.pop();\r\n      setPaginationHistory(newHistory);\r\n      \r\n      if (previousCursor === PAGINATION_CONSTANTS.FIRST_PAGE) {\r\n        setAfter(undefined);\r\n      } else {\r\n        setAfter(previousCursor);\r\n      }\r\n    }\r\n  };\r\n\r\n  const resetPagination = () => {\r\n    setAfter(undefined);\r\n    setPaginationHistory([]);\r\n  };\r\n\r\n  const handleConnectionComplete = (profileId: string, selectedTools: string[], appName: string, appSlug: string) => {\r\n    if (onToolsSelected) {\r\n      onToolsSelected(profileId, selectedTools, appName, appSlug);\r\n      toast.success(`Added ${selectedTools.length} tools from ${appName}!`);\r\n    }\r\n  };\r\n\r\n  const handleConnectApp = (app: PipedreamApp) => {\r\n    setSelectedAppForConnection(app);\r\n    setShowStreamlinedConnector(true);\r\n    onClose?.();\r\n  };\r\n\r\n  const handleConfigureTools = (profile: any) => {\r\n    if (!currentAgentId) {\r\n      toast.error('Please select an agent first');\r\n      return;\r\n    }\r\n    setSelectedToolsProfile({\r\n      profileId: profile.profile_id,\r\n      appName: profile.app_name,\r\n      profileName: profile.profile_name\r\n    });\r\n    setShowToolsManager(true);\r\n  };\r\n\r\n  const handleClearFilters = () => {\r\n    setSearch('');\r\n    setSelectedCategory('All');\r\n    resetPagination();\r\n  };\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"flex items-center justify-center h-full\">\r\n        <div className=\"text-center\">\r\n          <div className=\"text-red-500 mb-4\">\r\n            <X className=\"h-12 w-12 mx-auto mb-2\" />\r\n            <p className=\"text-lg font-semibold\">Failed to load integrations</p>\r\n          </div>\r\n          <Button onClick={() => refetch()} className=\"bg-primary hover:bg-primary/90\">\r\n            Try Again\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"h-full flex\">\r\n      <div className={cn(\r\n        \"transition-all duration-300 ease-in-out\",\r\n        sidebarCollapsed ? \"w-12\" : \"w-62\"\r\n      )}>\r\n        <CategorySidebar \r\n          isCollapsed={sidebarCollapsed}\r\n          onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}\r\n          categories={categories}\r\n          selectedCategory={selectedCategory}\r\n          onCategorySelect={handleCategorySelect}\r\n          allApps={allApps}\r\n        />\r\n      </div>\r\n      <div className=\"flex-1 relative\">\r\n        <PipedreamHeader \r\n          search={search}\r\n          onSearchChange={handleSearch}\r\n          showAgentSelector={showAgentSelector}\r\n          currentAgentId={currentAgentId}\r\n          onAgentChange={handleAgentSelect}\r\n        />\r\n        \r\n        <div className=\"absolute inset-0 pt-[100px] pb-[60px]\">\r\n          <div className=\"h-full overflow-y-auto p-4\">\r\n            <div className=\"max-w-6xl mx-auto\">\r\n              {showAgentSelector && !currentAgentId && (\r\n                <div className=\"mb-6 text-center py-8 px-6 bg-muted/30 rounded-xl border-2 border-dashed border-border\">\r\n                  <div className=\"mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-4\">\r\n                    <Bot className=\"h-6 w-6 text-muted-foreground\" />\r\n                  </div>\r\n                  <h4 className=\"text-sm font-medium text-foreground mb-2\">\r\n                    Select an agent to get started\r\n                  </h4>\r\n                  <p className=\"text-sm text-muted-foreground mb-4 max-w-sm mx-auto\">\r\n                    Choose an agent from the dropdown above to view and manage its Pipedream integrations\r\n                  </p>\r\n                </div>\r\n              )}\r\n              \r\n              {connectedApps.length > 0 && (!showAgentSelector || currentAgentId) && (\r\n                <ConnectedAppsSection\r\n                  connectedApps={connectedApps}\r\n                  showAgentSelector={showAgentSelector}\r\n                  currentAgentId={currentAgentId}\r\n                  agent={agent}\r\n                  agentPipedreamProfiles={agentPipedreamProfiles}\r\n                  mode={mode}\r\n                  onAppSelected={onAppSelected}\r\n                  onConnectApp={handleConnectApp}\r\n                  onConfigureTools={handleConfigureTools}\r\n                  onCategorySelect={handleCategorySelect}\r\n                />\r\n              )}\r\n              \r\n              {(!showAgentSelector || currentAgentId) && (\r\n                <>\r\n                  {filteredAppsData?.apps && filteredAppsData.apps.length > 0 ? (\r\n                    <AppsGrid\r\n                      apps={filteredAppsData.apps}\r\n                      selectedCategory={selectedCategory}\r\n                      mode={mode}\r\n                      isLoading={isLoading}\r\n                      currentAgentId={currentAgentId}\r\n                      agent={agent}\r\n                      agentPipedreamProfiles={agentPipedreamProfiles}\r\n                      onAppSelected={onAppSelected}\r\n                      onConnectApp={handleConnectApp}\r\n                      onConfigureTools={handleConfigureTools}\r\n                      onCategorySelect={handleCategorySelect}\r\n                    />\r\n                  ) : !isLoading ? (\r\n                    <EmptyState\r\n                      selectedCategory={selectedCategory}\r\n                      mode={mode}\r\n                      onClearFilters={handleClearFilters}\r\n                    />\r\n                  ) : (\r\n                    <AppsGrid\r\n                      apps={[]}\r\n                      selectedCategory={selectedCategory}\r\n                      mode={mode}\r\n                      isLoading={isLoading}\r\n                      currentAgentId={currentAgentId}\r\n                      agent={agent}\r\n                      agentPipedreamProfiles={agentPipedreamProfiles}\r\n                      onAppSelected={onAppSelected}\r\n                      onConnectApp={handleConnectApp}\r\n                      onConfigureTools={handleConfigureTools}\r\n                      onCategorySelect={handleCategorySelect}\r\n                    />\r\n                  )}\r\n                </>\r\n              )}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        \r\n        {filteredAppsData?.apps && filteredAppsData.apps.length > 0 && (paginationHistory.length > 0 || appsData?.page_info?.has_more) && (!showAgentSelector || currentAgentId) && (\r\n          <PaginationControls\r\n            isLoading={isLoading}\r\n            paginationHistory={paginationHistory}\r\n            hasMore={appsData?.page_info?.has_more || false}\r\n            onPrevPage={handlePrevPage}\r\n            onNextPage={handleNextPage}\r\n          />\r\n        )}\r\n      </div>\r\n      {selectedAppForConnection && (\r\n        <PipedreamConnector\r\n          app={selectedAppForConnection}\r\n          open={showStreamlinedConnector}\r\n          onOpenChange={setShowStreamlinedConnector}\r\n          onComplete={handleConnectionComplete}\r\n          mode={mode === 'profile-only' ? 'profile-only' : 'full'}\r\n        />\r\n      )}\r\n      {selectedToolsProfile && currentAgentId && (\r\n        <ToolsManager\r\n          mode=\"pipedream\"\r\n          agentId={currentAgentId}\r\n          profileId={selectedToolsProfile.profileId}\r\n          appName={selectedToolsProfile.appName}\r\n          profileName={selectedToolsProfile.profileName}\r\n          open={showToolsManager}\r\n          onOpenChange={(open) => {\r\n            setShowToolsManager(open);\r\n            if (!open) {\r\n              setSelectedToolsProfile(null);\r\n            }\r\n          }}\r\n          onToolsUpdate={(enabledTools) => {\r\n            queryClient.invalidateQueries({ queryKey: ['agent', currentAgentId] });\r\n          }}\r\n        />\r\n      )}\r\n    </div>\r\n  );\r\n}; "], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;;;;;;;;;;;;;;;;AAQO,MAAM,oBAAsD,CAAC,EAClE,eAAe,EACf,aAAa,EACb,OAAO,MAAM,EACb,OAAO,EACP,oBAAoB,KAAK,EACzB,eAAe,EACf,aAAa,EACd;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACvD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvE,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzE,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAC9F,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAIrD;IAEV,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAE3F,MAAM,cAAc,CAAA,GAAA,sLAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,EAAE,MAAM,QAAQ,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO;IAC9E,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,uBAAoB,AAAD;IAE9C,MAAM,iBAAiB,mBAAmB;IAC1C,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,WAAQ,AAAD,EAAE,kBAAkB;IAEnD,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW;IAE1D,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,2BAA2B;IAC7B,GAAG;QAAC;KAAgB;IAEpB,MAAM,oBAAoB,CAAC;QACzB,IAAI,eAAe;YACjB,cAAc;QAChB,OAAO;YACL,2BAA2B;QAC7B;QACA,IAAI,YAAY,gBAAgB;YAC9B,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAQ;YAAC;YACpD,IAAI,SAAS;gBACX,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;wBAAS;qBAAQ;gBAAC;YAC/D;QACF;IACF;IAEA,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACtB,OAAO,aAAa,QAAQ,EAAE;IAChC,GAAG;QAAC,aAAa;KAAK;IAEtB,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACrC,OAAO,CAAA,GAAA,iJAAA,CAAA,4BAAyB,AAAD,EAAE,OAAO,UAAU;IACpD,GAAG;QAAC;QAAO;QAAU;KAAe;IAEpC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACzB,OAAO,CAAA,GAAA,iJAAA,CAAA,wBAAqB,AAAD,EAAE;IAC/B,GAAG;QAAC;KAAQ;IAEZ,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAChC,OAAO,UAAU,OAAO,CAAA,IAAK,EAAE,YAAY,KAAK,EAAE;IACpD,GAAG;QAAC;KAAS;IAEb,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,IAAI,CAAC,UAAU,OAAO;QAEtB,IAAI,qBAAqB,OAAO;YAC9B,OAAO;QACT;QAEA,MAAM,eAAe,CAAA,GAAA,iJAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS,IAAI,EAAE;QAEzD,OAAO;YACL,GAAG,QAAQ;YACX,MAAM;YACN,WAAW;gBACT,GAAG,SAAS,SAAS;gBACrB,OAAO,aAAa,MAAM;YAC5B;QACF;IACF,GAAG;QAAC;QAAU;KAAiB;IAE/B,MAAM,gBAAgC,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QAC5C,OAAO,CAAA,GAAA,iJAAA,CAAA,kCAA+B,AAAD,EAAE,mBAAmB;IAC5D,GAAG;QAAC;QAAmB;KAAQ;IAE/B,MAAM,eAAe,CAAC;QACpB,UAAU;QACV,SAAS;QACT,qBAAqB,EAAE;IACzB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;QACpB,SAAS;QACT,qBAAqB,EAAE;IACzB;IAEA,MAAM,iBAAiB;QACrB,IAAI,UAAU,WAAW,YAAY;YACnC,IAAI,OAAO;gBACT,qBAAqB,CAAA,OAAQ;2BAAI;wBAAM;qBAAM;YAC/C,OAAO;gBACL,qBAAqB,CAAA,OAAQ;2BAAI;wBAAM,qJAAA,CAAA,uBAAoB,CAAC,UAAU;qBAAC;YACzE;YACA,SAAS,SAAS,SAAS,CAAC,UAAU;QACxC;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,kBAAkB,MAAM,GAAG,GAAG;YAChC,MAAM,aAAa;mBAAI;aAAkB;YACzC,MAAM,iBAAiB,WAAW,GAAG;YACrC,qBAAqB;YAErB,IAAI,mBAAmB,qJAAA,CAAA,uBAAoB,CAAC,UAAU,EAAE;gBACtD,SAAS;YACX,OAAO;gBACL,SAAS;YACX;QACF;IACF;IAEA,MAAM,kBAAkB;QACtB,SAAS;QACT,qBAAqB,EAAE;IACzB;IAEA,MAAM,2BAA2B,CAAC,WAAmB,eAAyB,SAAiB;QAC7F,IAAI,iBAAiB;YACnB,gBAAgB,WAAW,eAAe,SAAS;YACnD,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,cAAc,MAAM,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QACtE;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,4BAA4B;QAC5B,4BAA4B;QAC5B;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,CAAC,gBAAgB;YACnB,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QACA,wBAAwB;YACtB,WAAW,QAAQ,UAAU;YAC7B,SAAS,QAAQ,QAAQ;YACzB,aAAa,QAAQ,YAAY;QACnC;QACA,oBAAoB;IACtB;IAEA,MAAM,qBAAqB;QACzB,UAAU;QACV,oBAAoB;QACpB;IACF;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM;wBAAW,WAAU;kCAAiC;;;;;;;;;;;;;;;;;IAMrF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,2CACA,mBAAmB,SAAS;0BAE5B,cAAA,8OAAC,2KAAA,CAAA,kBAAe;oBACd,aAAa;oBACb,UAAU,IAAM,oBAAoB,CAAC;oBACrC,YAAY;oBACZ,kBAAkB;oBAClB,kBAAkB;oBAClB,SAAS;;;;;;;;;;;0BAGb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,2KAAA,CAAA,kBAAe;wBACd,QAAQ;wBACR,gBAAgB;wBAChB,mBAAmB;wBACnB,gBAAgB;wBAChB,eAAe;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;oCACZ,qBAAqB,CAAC,gCACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;0DAEjB,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DAGzD,8OAAC;gDAAE,WAAU;0DAAsD;;;;;;;;;;;;oCAMtE,cAAc,MAAM,GAAG,KAAK,CAAC,CAAC,qBAAqB,cAAc,mBAChE,8OAAC,gLAAA,CAAA,uBAAoB;wCACnB,eAAe;wCACf,mBAAmB;wCACnB,gBAAgB;wCAChB,OAAO;wCACP,wBAAwB;wCACxB,MAAM;wCACN,eAAe;wCACf,cAAc;wCACd,kBAAkB;wCAClB,kBAAkB;;;;;;oCAIrB,CAAC,CAAC,qBAAqB,cAAc,mBACpC;kDACG,kBAAkB,QAAQ,iBAAiB,IAAI,CAAC,MAAM,GAAG,kBACxD,8OAAC,oKAAA,CAAA,WAAQ;4CACP,MAAM,iBAAiB,IAAI;4CAC3B,kBAAkB;4CAClB,MAAM;4CACN,WAAW;4CACX,gBAAgB;4CAChB,OAAO;4CACP,wBAAwB;4CACxB,eAAe;4CACf,cAAc;4CACd,kBAAkB;4CAClB,kBAAkB;;;;;mDAElB,CAAC,0BACH,8OAAC,sKAAA,CAAA,aAAU;4CACT,kBAAkB;4CAClB,MAAM;4CACN,gBAAgB;;;;;iEAGlB,8OAAC,oKAAA,CAAA,WAAQ;4CACP,MAAM,EAAE;4CACR,kBAAkB;4CAClB,MAAM;4CACN,WAAW;4CACX,gBAAgB;4CAChB,OAAO;4CACP,wBAAwB;4CACxB,eAAe;4CACf,cAAc;4CACd,kBAAkB;4CAClB,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;oBAS/B,kBAAkB,QAAQ,iBAAiB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC,kBAAkB,MAAM,GAAG,KAAK,UAAU,WAAW,QAAQ,KAAK,CAAC,CAAC,qBAAqB,cAAc,mBACrK,8OAAC,8KAAA,CAAA,qBAAkB;wBACjB,WAAW;wBACX,mBAAmB;wBACnB,SAAS,UAAU,WAAW,YAAY;wBAC1C,YAAY;wBACZ,YAAY;;;;;;;;;;;;YAIjB,0CACC,8OAAC,mKAAA,CAAA,qBAAkB;gBACjB,KAAK;gBACL,MAAM;gBACN,cAAc;gBACd,YAAY;gBACZ,MAAM,SAAS,iBAAiB,iBAAiB;;;;;;YAGpD,wBAAwB,gCACvB,8OAAC,uJAAA,CAAA,eAAY;gBACX,MAAK;gBACL,SAAS;gBACT,WAAW,qBAAqB,SAAS;gBACzC,SAAS,qBAAqB,OAAO;gBACrC,aAAa,qBAAqB,WAAW;gBAC7C,MAAM;gBACN,cAAc,CAAC;oBACb,oBAAoB;oBACpB,IAAI,CAAC,MAAM;wBACT,wBAAwB;oBAC1B;gBACF;gBACA,eAAe,CAAC;oBACd,YAAY,iBAAiB,CAAC;wBAAE,UAAU;4BAAC;4BAAS;yBAAe;oBAAC;gBACtE;;;;;;;;;;;;AAKV", "debugId": null}}]}