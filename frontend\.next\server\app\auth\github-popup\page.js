(()=>{var e={};e.id=9190,e.ids=[9190],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29630:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(60687),n=t(43210);t(79481);var i=t(41862);function a(){let[e,r]=(0,n.useState)("loading"),[t,a]=(0,n.useState)("");return(0,s.jsx)("main",{className:"flex flex-col items-center justify-center h-screen bg-background p-8",children:(0,s.jsxs)("div",{className:"flex flex-col items-center gap-4 text-center max-w-sm",children:["error"!==e&&(0,s.jsx)(i.A,{className:"h-8 w-8 animate-spin text-primary"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("h1",{className:"text-lg font-medium",children:"GitHub Sign-In"}),(0,s.jsx)("p",{className:`text-sm ${(()=>{switch(e){case"error":return"text-red-500";case"processing":return"text-green-500";default:return"text-muted-foreground"}})()}`,children:(()=>{switch(e){case"loading":return"Starting GitHub authentication...";case"processing":return"Completing sign-in...";case"error":return t||"Authentication failed";default:return"Processing..."}})()})]}),"error"===e&&(0,s.jsx)("button",{onClick:()=>window.close(),className:"mt-4 px-4 py-2 text-sm bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors",children:"Close"})]})})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39618:(e,r,t)=>{Promise.resolve().then(t.bind(t,69776))},41862:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57770:(e,r,t)=>{Promise.resolve().then(t.bind(t,29630))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69776:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\auth\\\\github-popup\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\auth\\github-popup\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80108:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.default,__next_app__:()=>d,pages:()=>p,routeModule:()=>l,tree:()=>u});var s=t(65239),n=t(48088),i=t(31369),a=t(30893),o={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>a[e]);t.d(r,o);let u={children:["",{children:["auth",{children:["github-popup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,69776)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\auth\\github-popup\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,88524))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,93595)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,88524))).default(e)],twitter:[],manifest:void 0}}]}.children,p=["C:\\Users\\<USER>\\suna\\frontend\\src\\app\\auth\\github-popup\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},l=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/auth/github-popup/page",pathname:"/auth/github-popup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,5193,4267,3667],()=>t(80108));module.exports=s})();