(()=>{var e={};e.id=8516,e.ids=[8516],e.modules={36:(e,t,r)=>{"use strict";r.d(t,{U:()=>n});var a=r(6475);let n=(0,a.createServerReference)("7f8bed79c8654f95685745e61906af37f96a086236",a.callServer,void 0,a.findSourceMapURL,"createClient")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5876:(e,t,r)=>{"use strict";r.d(t,{U:()=>o});var a=r(91199);r(42087);var n=r(30445),s=r(74208),i=r(33331);let o=async()=>{let e=await (0,s.UL)(),t="";return t&&!t.startsWith("http")&&(t=`http://${t}`),(0,n.createServerClient)(t,"",{cookies:{getAll:()=>e.getAll(),setAll(t){try{t.forEach(({name:t,value:r,options:a})=>e.set({name:t,value:r,...a}))}catch(e){}}}})};(0,i.D)([o]),(0,a.A)(o,"7f8bed79c8654f95685745e61906af37f96a086236",null)},8552:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"602cb633238b6ed821154b0de7624fe865fdf4a20d":()=>a.Op,"60479f0ba93cf8cd5b7a4f15ad34fc5cac7652b700":()=>a.$b,"60b56431385a17492afc62212a7d703c72e7e723ab":()=>a.QA,"7f8bed79c8654f95685745e61906af37f96a086236":()=>n.U});var a=r(63632),n=r(5876)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23894:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.default,__next_app__:()=>l,pages:()=>c,routeModule:()=>u,tree:()=>d});var a=r(65239),n=r(48088),s=r(31369),i=r(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let d={children:["",{children:["invitation",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,58940)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\invitation\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,88524))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,93595)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,88524))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\suna\\frontend\\src\\app\\invitation\\page.tsx"],l={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/invitation/page",pathname:"/invitation",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},25775:(e,t,r)=>{Promise.resolve().then(r.bind(r,58940))},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>d,r:()=>o});var a=r(60687);r(43210);var n=r(8730),s=r(24224),i=r(4780);let o=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",node_outline:"bg-transparent border border-primary/10",node_secondary:"px-0 bg-transparent hover:opacity-60",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-lg gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-lg px-6 has-[>svg]:px-4",icon:"size-9",node_secondary:"px-0"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:r,asChild:s=!1,...d}){let c=s?n.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:r,className:e})),...d})}},33873:e=>{"use strict";e.exports=require("path")},34023:(e,t,r)=>{Promise.resolve().then(r.bind(r,89857))},34631:e=>{"use strict";e.exports=require("tls")},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>s,aR:()=>i,wL:()=>l});var a=r(60687);r(43210);var n=r(4780);function s({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6 [.border-t]:pt-6",e),...t})}},44774:(e,t,r)=>{"use strict";r.d(t,{SubmitButton:()=>l});var a=r(60687),n=r(51215),s=r(43210),i=r(29523),o=r(91821),d=r(43649);let c={message:""};function l({children:e,formAction:t,errorMessage:r,pendingText:l="Submitting...",...u}){let{pending:p,action:v}=(0,n.useFormStatus)(),[f,x]=(0,s.useActionState)(t,c),g=p&&v===x;return(0,a.jsxs)("div",{className:"flex flex-col gap-y-4 w-full",children:[!!(r||f?.message)&&(0,a.jsxs)(o.Fc,{variant:"destructive",className:"w-full",children:[(0,a.jsx)(d.A,{className:"h-4 w-4"}),(0,a.jsx)(o.TN,{children:r||f?.message})]}),(0,a.jsx)("div",{children:(0,a.jsx)(i.$,{...u,type:"submit","aria-disabled":p,formAction:x,children:g?l:e})})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58940:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\invitation\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\invitation\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63632:(e,t,r)=>{"use strict";r.d(t,{$b:()=>d,Op:()=>o,QA:()=>c});var a=r(91199);r(42087);var n=r(7944),s=r(5876),i=r(90141);async function o(e,t){let r=t.get("invitationType"),a=t.get("accountId"),i=t.get("accountRole"),o=await (0,s.U)(),{data:d,error:c}=await o.rpc("create_invitation",{account_id:a,invitation_type:r,account_role:i});return c?{message:c.message}:((0,n.revalidatePath)("/[accountSlug]/settings/members/page"),{token:d.token})}async function d(e,t){let r=t.get("invitationId"),a=t.get("returnPath"),n=await (0,s.U)(),{error:o}=await n.rpc("delete_invitation",{invitation_id:r});if(o)return{message:o.message};(0,i.redirect)(a)}async function c(e,t){let r=t.get("token"),a=await (0,s.U)(),{error:n,data:o}=await a.rpc("accept_invitation",{lookup_invitation_token:r});if(n)return{message:n.message};(0,i.redirect)(`/${o.slug}`)}(0,r(33331).D)([o,d,c]),(0,a.A)(o,"602cb633238b6ed821154b0de7624fe865fdf4a20d",null),(0,a.A)(d,"60479f0ba93cf8cd5b7a4f15ad34fc5cac7652b700",null),(0,a.A)(c,"60b56431385a17492afc62212a7d703c72e7e723ab",null)},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},89857:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>f});var a=r(60687),n=r(43210),s=r.n(n),i=r(6475);let o=(0,i.createServerReference)("60b56431385a17492afc62212a7d703c72e7e723ab",i.callServer,void 0,i.findSourceMapURL,"acceptInvitation");var d=r(36),c=r(91821),l=r(44493),u=r(44774);async function p({token:e}){let t=await (0,d.U)(),{data:r}=await t.rpc("lookup_invitation",{lookup_invitation_token:e});return(0,a.jsx)(l.Zp,{children:(0,a.jsxs)(l.Wu,{className:"p-8 text-center flex flex-col gap-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{children:"You've been invited to join"}),(0,a.jsx)("h1",{className:"text-xl font-bold",children:r.account_name})]}),r.active?(0,a.jsxs)("form",{children:[(0,a.jsx)("input",{type:"hidden",name:"token",value:e}),(0,a.jsx)(u.SubmitButton,{formAction:o,pendingText:"Accepting invitation...",children:"Accept invitation"})]}):(0,a.jsx)(c.Fc,{variant:"destructive",children:"This invitation has been deactivated. Please contact the account owner for a new invitation."})]})})}var v=r(16189);function f({searchParams:e}){let t=s().use(e);return t.token||(0,v.redirect)("/"),(0,a.jsx)("div",{className:"max-w-md mx-auto w-full my-12",children:(0,a.jsx)(p,{token:t.token})})}},91645:e=>{"use strict";e.exports=require("net")},91821:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>o,TN:()=>c,XL:()=>d});var a=r(60687);r(43210);var n=r(24224),s=r(4780);let i=(0,n.F)("relative w-full rounded-xl border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,s.cn)(i({variant:t}),e),...r})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"alert-title",className:(0,s.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,s.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[7719,5193,4267,7096,7944,3667],()=>r(23894));module.exports=a})();