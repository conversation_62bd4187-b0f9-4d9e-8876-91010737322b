"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6686],{25731:(e,t,o)=>{o.d(t,{A3:()=>f,Bp:()=>R,Et:()=>M,Ey:()=>i,GB:()=>_,Ih:()=>y,JQ:()=>u,Kc:()=>T,Ks:()=>B,MA:()=>A,Nw:()=>m,U1:()=>d,VK:()=>I,VL:()=>E,XY:()=>j,_L:()=>v,cF:()=>x,cL:()=>N,eD:()=>b,fG:()=>w,fu:()=>P,fw:()=>U,gA:()=>g,gS:()=>k,m9:()=>S,uV:()=>C,vr:()=>h,xx:()=>p});var r=o(52643),a=o(33356);let n="http://localhost:8000/api",s=new Set,c=new Map;class i extends Error{constructor(e,t,o){super(o||t.message||"Billing Error: ".concat(e)),this.name="BillingError",this.status=e,this.detail=t,Object.setPrototypeOf(this,i.prototype)}}class l extends Error{constructor(e,t){super(e||"No access token available",t),this.name="NoAccessTokenAvailableError"}}let u=async()=>{try{let e=(0,r.U)(),{data:t,error:o}=await e.auth.getUser();if(o)return console.error("Error getting current user:",o),[];if(!t.user)return console.log("[API] No user logged in, returning empty projects array"),[];let{data:a,error:n}=await e.from("projects").select("*").eq("account_id",t.user.id);if(n){if("42501"===n.code&&n.message.includes("has_role_on_account"))return console.error("Permission error: User does not have proper account access"),[];throw n}console.log("[API] Raw projects from DB:",null==a?void 0:a.length,a);let s=(a||[]).map(e=>({id:e.project_id,name:e.name||"",description:e.description||"",account_id:e.account_id,created_at:e.created_at,updated_at:e.updated_at,sandbox:e.sandbox||{id:"",pass:"",vnc_preview:"",sandbox_url:""}}));return console.log("[API] Mapped projects for frontend:",s.length),s}catch(e){return console.error("Error fetching projects:",e),(0,a.hS)(e,{operation:"load projects",resource:"projects"}),[]}},d=async e=>{let t=(0,r.U)();try{var o;let{data:r,error:a}=await t.from("projects").select("*").eq("project_id",e).single();if(a){if("PGRST116"===a.code)throw Error("Project not found or not accessible: ".concat(e));throw a}return console.log("Raw project data from database:",r),(null==(o=r.sandbox)?void 0:o.id)&&(async()=>{try{let{data:{session:o}}=await t.auth.getSession(),r={"Content-Type":"application/json"};(null==o?void 0:o.access_token)&&(r.Authorization="Bearer ".concat(o.access_token)),console.log("Ensuring sandbox is active for project ".concat(e,"..."));let a=await fetch("".concat(n,"/project/").concat(e,"/sandbox/ensure-active"),{method:"POST",headers:r});if(a.ok)console.log("Sandbox activation successful");else{let e=await a.text().catch(()=>"No error details available");console.warn("Failed to ensure sandbox is active: ".concat(a.status," ").concat(a.statusText),e)}}catch(e){console.warn("Failed to ensure sandbox is active:",e)}})(),{id:r.project_id,name:r.name||"",description:r.description||"",account_id:r.account_id,is_public:r.is_public||!1,created_at:r.created_at,sandbox:r.sandbox||{id:"",pass:"",vnc_preview:"",sandbox_url:""}}}catch(t){throw console.error("Error fetching project ".concat(e,":"),t),(0,a.hS)(t,{operation:"load project",resource:"project ".concat(e)}),t}},g=async(e,t)=>{let o=(0,r.U)();if(!t){let{data:e,error:r}=await o.auth.getUser();if(r)throw r;if(!e.user)throw Error("You must be logged in to create a project");t=e.user.id}let{data:n,error:s}=await o.from("projects").insert({name:e.name,description:e.description||null,account_id:t}).select().single();if(s)throw(0,a.hS)(s,{operation:"create project",resource:"project"}),s;return{id:n.project_id,name:n.name,description:n.description||"",account_id:n.account_id,created_at:n.created_at,sandbox:{id:"",pass:"",vnc_preview:""}}},h=async(e,t)=>{let o=(0,r.U)();if(console.log("Updating project with ID:",e),console.log("Update data:",t),!e||""===e)throw console.error("Attempted to update project with invalid ID:",e),Error("Cannot update project: Invalid project ID");let{data:n,error:s}=await o.from("projects").update(t).eq("project_id",e).select().single();if(s)throw console.error("Error updating project:",s),(0,a.hS)(s,{operation:"update project",resource:"project ".concat(e)}),s;if(!n){let t=Error("No data returned from update");throw(0,a.hS)(t,{operation:"update project",resource:"project ".concat(e)}),t}return window.dispatchEvent(new CustomEvent("project-updated",{detail:{projectId:e,updatedData:{id:n.project_id,name:n.name,description:n.description}}})),{id:n.project_id,name:n.name,description:n.description||"",account_id:n.account_id,created_at:n.created_at,sandbox:n.sandbox||{id:"",pass:"",vnc_preview:"",sandbox_url:""}}},p=async e=>{let t=(0,r.U)(),{error:o}=await t.from("projects").delete().eq("project_id",e);if(o)throw(0,a.hS)(o,{operation:"delete project",resource:"project ".concat(e)}),o},f=async e=>{let t=(0,r.U)(),{data:o,error:n}=await t.auth.getUser();if(n)return console.error("Error getting current user:",n),[];if(!o.user)return console.log("[API] No user logged in, returning empty threads array"),[];let s=t.from("threads").select("*");s=s.eq("account_id",o.user.id),e&&(console.log("[API] Filtering threads by project_id:",e),s=s.eq("project_id",e));let{data:c,error:i}=await s;if(i)throw(0,a.hS)(i,{operation:"load threads",resource:e?"threads for project ".concat(e):"threads"}),i;return(c||[]).filter(e=>!(e.metadata||{}).is_agent_builder).map(e=>({thread_id:e.thread_id,account_id:e.account_id,project_id:e.project_id,created_at:e.created_at,updated_at:e.updated_at,metadata:e.metadata}))},w=async e=>{let t=(0,r.U)(),{data:o,error:n}=await t.from("threads").select("*").eq("thread_id",e).single();if(n)throw(0,a.hS)(n,{operation:"load thread",resource:"thread ".concat(e)}),n;return o},m=async e=>{let t=(0,r.U)(),{data:{user:o}}=await t.auth.getUser();if(!o)throw Error("You must be logged in to create a thread");let{data:n,error:s}=await t.from("threads").insert({project_id:e,account_id:o.id}).select().single();if(s)throw(0,a.hS)(s,{operation:"create thread",resource:"thread"}),s;return n},_=async(e,t)=>{let o=(0,r.U)(),{error:n}=await o.from("messages").insert({thread_id:e,type:"user",is_llm_message:!0,content:JSON.stringify({role:"user",content:t})});if(n)throw console.error("Error adding user message:",n),(0,a.hS)(n,{operation:"add message",resource:"message"}),Error("Error adding message: ".concat(n.message))},E=async e=>{let t=(0,r.U)(),o=[],n=0,s=!0;for(;s;){let{data:r,error:c}=await t.from("messages").select("\n        *,\n        agents:agent_id (\n          name,\n          avatar,\n          avatar_color\n        )\n      ").eq("thread_id",e).neq("type","cost").neq("type","summary").order("created_at",{ascending:!0}).range(n,n+1e3-1);if(c)throw console.error("Error fetching messages:",c),(0,a.hS)(c,{operation:"load messages",resource:"messages for thread ".concat(e)}),Error("Error getting messages: ".concat(c.message));r&&r.length>0?(o=o.concat(r),n+=1e3,s=1e3===r.length):s=!1}return console.log("[API] Messages fetched count:",o.length),o},y=async(e,t)=>{try{let o=(0,r.U)(),{data:{session:a}}=await o.auth.getSession();if(!(null==a?void 0:a.access_token))throw new l;if(!n)throw Error("Backend URL is not configured. Set NEXT_PUBLIC_BACKEND_URL in your environment.");console.log("[API] Starting agent for thread ".concat(e," using ").concat(n,"/thread/").concat(e,"/agent/start"));let s={model_name:"claude-3-7-sonnet-latest",enable_thinking:!1,reasoning_effort:"low",stream:!0,agent_id:void 0,...t},c={model_name:s.model_name,enable_thinking:s.enable_thinking,reasoning_effort:s.reasoning_effort,stream:s.stream};s.agent_id&&(c.agent_id=s.agent_id);let u=await fetch("".concat(n,"/thread/").concat(e,"/agent/start"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a.access_token)},body:JSON.stringify(c)});if(!u.ok){if(402===u.status)try{let e=await u.json();console.error("[API] Billing error starting agent (402):",e);let t=(null==e?void 0:e.detail)||{message:"Payment Required"};throw"string"!=typeof t.message&&(t.message="Payment Required"),new i(u.status,t)}catch(e){throw console.error("[API] Could not parse 402 error response body:",e),new i(u.status,{message:"Payment Required"},"Error starting agent: ".concat(u.statusText," (402)"))}let e=await u.text().catch(()=>"No error details available");throw console.error("[API] Error starting agent: ".concat(u.status," ").concat(u.statusText),e),Error("Error starting agent: ".concat(u.statusText," (").concat(u.status,")"))}return await u.json()}catch(e){if(e instanceof i||e instanceof l)throw e;if(console.error("[API] Failed to start agent:",e),e instanceof TypeError&&e.message.includes("Failed to fetch")){let e=Error("Cannot connect to backend server. Please check your internet connection and make sure the backend is running.");throw(0,a.hS)(e,{operation:"start agent",resource:"AI assistant"}),e}throw(0,a.hS)(e,{operation:"start agent",resource:"AI assistant"}),e}},b=async e=>{s.add(e);let t=c.get(e);t&&(console.log("[API] Closing existing stream for ".concat(e," before stopping agent")),t.close(),c.delete(e));let o=(0,r.U)(),{data:{session:i}}=await o.auth.getSession();if(!(null==i?void 0:i.access_token)){let e=new l;throw(0,a.hS)(e,{operation:"stop agent",resource:"AI assistant"}),e}let u=await fetch("".concat(n,"/agent-run/").concat(e,"/stop"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(i.access_token)},cache:"no-store"});if(!u.ok){let e=Error("Error stopping agent: ".concat(u.statusText));throw(0,a.hS)(e,{operation:"stop agent",resource:"AI assistant"}),e}},v=async e=>{if(console.log("[API] Requesting agent status for ".concat(e)),s.has(e))throw console.log("[API] Agent run ".concat(e," is known to be non-running, returning error")),Error("Agent run ".concat(e," is not running"));try{let t=(0,r.U)(),{data:{session:o}}=await t.auth.getSession();if(!(null==o?void 0:o.access_token))throw console.error("[API] No access token available for getAgentStatus"),new l;let a="".concat(n,"/agent-run/").concat(e);console.log("[API] Fetching from: ".concat(a));let c=await fetch(a,{headers:{Authorization:"Bearer ".concat(o.access_token)},cache:"no-store"});if(!c.ok){let t=await c.text().catch(()=>"No error details available");throw console.error("[API] Error getting agent status: ".concat(c.status," ").concat(c.statusText),t),404===c.status&&s.add(e),Error("Error getting agent status: ".concat(c.statusText," (").concat(c.status,")"))}let i=await c.json();return console.log("[API] Successfully got agent status:",i),"running"!==i.status&&s.add(e),i}catch(e){throw console.error("[API] Failed to get agent status:",e),(0,a.hS)(e,{operation:"get agent status",resource:"AI assistant status",silent:!0}),e}},S=async e=>{try{let t=(0,r.U)(),{data:{session:o}}=await t.auth.getSession();if(!(null==o?void 0:o.access_token))throw new l;let a=await fetch("".concat(n,"/thread/").concat(e,"/agent-runs"),{headers:{Authorization:"Bearer ".concat(o.access_token)},cache:"no-store"});if(!a.ok)throw Error("Error getting agent runs: ".concat(a.statusText));return(await a.json()).agent_runs||[]}catch(e){if(e instanceof l)throw e;throw console.error("Failed to get agent runs:",e),(0,a.hS)(e,{operation:"load agent runs",resource:"conversation history"}),e}},A=(e,t)=>{if(console.log("[STREAM] streamAgent called for ".concat(e)),s.has(e))return console.log("[STREAM] Agent run ".concat(e," is known to be non-running, not creating stream")),setTimeout(()=>{t.onError("Agent run ".concat(e," is not running")),t.onClose()},0),()=>{};let o=c.get(e);o&&(console.log("[STREAM] Stream already exists for ".concat(e,", closing it first")),o.close(),c.delete(e));try{return(async()=>{try{let o=await v(e);if("running"!==o.status){console.log("[STREAM] Agent run ".concat(e," is not running (status: ").concat(o.status,"), not creating stream")),s.add(e),t.onError("Agent run ".concat(e," is not running (status: ").concat(o.status,")")),t.onClose();return}}catch(r){console.error("[STREAM] Error verifying agent run ".concat(e,":"),r);let o=r instanceof Error?r.message:String(r);(o.includes("not found")||o.includes("404")||o.includes("does not exist"))&&(console.log("[STREAM] Agent run ".concat(e," not found, not creating stream")),s.add(e)),t.onError(o),t.onClose();return}let o=(0,r.U)(),{data:{session:a}}=await o.auth.getSession();if(!(null==a?void 0:a.access_token)){let e=new l;console.error("[STREAM] No auth token available"),t.onError(e),t.onClose();return}let i=new URL("".concat(n,"/agent-run/").concat(e,"/stream"));i.searchParams.append("token",a.access_token),console.log("[STREAM] Creating EventSource for ".concat(e));let u=new EventSource(i.toString());c.set(e,u),u.onopen=()=>{console.log("[STREAM] Connection opened for ".concat(e))},u.onmessage=o=>{try{let r=o.data;if(r.includes('"type":"ping"'))return;if(console.log("[STREAM] Received data for ".concat(e,": ").concat(r.substring(0,100)).concat(r.length>100?"...":"")),!r||""===r.trim())return void console.debug("[STREAM] Received empty message, skipping");try{let o=JSON.parse(r);if("error"===o.status){console.error("[STREAM] Error status received for ".concat(e,":"),o),t.onError(o.message||"Unknown error occurred");return}}catch(e){}if(r.includes("Agent run")&&r.includes("not found in active runs")){console.log("[STREAM] Agent run ".concat(e," not found in active runs, closing stream")),s.add(e),t.onError("Agent run not found in active runs"),u.close(),c.delete(e),t.onClose();return}if(r.includes('"type":"status"')&&r.includes('"status":"completed"')){console.log("[STREAM] Detected completion status message for ".concat(e)),(r.includes("Run data not available for streaming")||r.includes("Stream ended with status: completed"))&&(console.log("[STREAM] Detected final completion message for ".concat(e,", adding to non-running set")),s.add(e)),t.onMessage(r),u.close(),c.delete(e),t.onClose();return}if(r.includes('"type":"status"')&&r.includes('"status_type":"thread_run_end"')){console.log("[STREAM] Detected thread run end message for ".concat(e)),s.add(e),t.onMessage(r),u.close(),c.delete(e),t.onClose();return}t.onMessage(r)}catch(e){console.error("[STREAM] Error handling message:",e),t.onError(e instanceof Error?e:String(e))}},u.onerror=o=>{console.log("[STREAM] EventSource error for ".concat(e,":"),o),v(e).then(o=>{"running"!==o.status?(console.log("[STREAM] Agent run ".concat(e," is not running after error, closing stream")),s.add(e),u.close(),c.delete(e),t.onClose()):console.log("[STREAM] Agent run ".concat(e," is still running after error, keeping stream open"))}).catch(o=>{console.error("[STREAM] Error checking agent status after stream error:",o);let r=o instanceof Error?o.message:String(o);(r.includes("not found")||r.includes("404")||r.includes("does not exist"))&&(console.log("[STREAM] Agent run ".concat(e," not found after error, closing stream")),s.add(e),u.close(),c.delete(e),t.onClose()),t.onError(r)})}})(),()=>{console.log("[STREAM] Cleanup called for ".concat(e));let t=c.get(e);t&&(console.log("[STREAM] Closing stream for ".concat(e)),t.close(),c.delete(e))}}catch(o){return console.error("[STREAM] Error setting up stream for ".concat(e,":"),o),t.onError(o instanceof Error?o:String(o)),t.onClose(),()=>{}}},k=async(e,t,o)=>{try{let a=(0,r.U)(),{data:{session:s}}=await a.auth.getSession(),c=new FormData;c.append("path",t);let i=new Blob([o],{type:"application/octet-stream"});c.append("file",i,t.split("/").pop()||"file");let l={};(null==s?void 0:s.access_token)&&(l.Authorization="Bearer ".concat(s.access_token));let u=await fetch("".concat(n,"/sandboxes/").concat(e,"/files"),{method:"POST",headers:l,body:c});if(!u.ok){let e=await u.text().catch(()=>"No error details available");throw console.error("Error creating sandbox file: ".concat(u.status," ").concat(u.statusText),e),Error("Error creating sandbox file: ".concat(u.statusText," (").concat(u.status,")"))}return await u.json()}catch(e){throw console.error("Failed to create sandbox file:",e),(0,a.hS)(e,{operation:"create file",resource:"file ".concat(t)}),e}},x=async(e,t,o)=>{try{let a=(0,r.U)(),{data:{session:s}}=await a.auth.getSession(),c={"Content-Type":"application/json"};(null==s?void 0:s.access_token)&&(c.Authorization="Bearer ".concat(s.access_token));let i=await fetch("".concat(n,"/sandboxes/").concat(e,"/files/json"),{method:"POST",headers:c,body:JSON.stringify({path:t,content:o})});if(!i.ok){let e=await i.text().catch(()=>"No error details available");throw console.error("Error creating sandbox file (JSON): ".concat(i.status," ").concat(i.statusText),e),Error("Error creating sandbox file: ".concat(i.statusText," (").concat(i.status,")"))}return await i.json()}catch(e){throw console.error("Failed to create sandbox file with JSON:",e),(0,a.hS)(e,{operation:"create file",resource:"file ".concat(t)}),e}},j=async(e,t)=>{try{let o=(0,r.U)(),{data:{session:a}}=await o.auth.getSession(),s=new URL("".concat(n,"/sandboxes/").concat(e,"/files")),c=function(e){try{return e.replace(/\\u([0-9a-fA-F]{4})/g,(e,t)=>String.fromCharCode(parseInt(t,16)))}catch(t){return console.error("Error processing Unicode escapes in path:",t),e}}(t);s.searchParams.append("path",c);let i={};(null==a?void 0:a.access_token)&&(i.Authorization="Bearer ".concat(a.access_token));let l=await fetch(s.toString(),{headers:i});if(!l.ok){let e=await l.text().catch(()=>"No error details available");throw console.error("Error listing sandbox files: ".concat(l.status," ").concat(l.statusText),e),Error("Error listing sandbox files: ".concat(l.statusText," (").concat(l.status,")"))}return(await l.json()).files||[]}catch(e){throw console.error("Failed to list sandbox files:",e),e}},T=async()=>{try{let e=(0,r.U)(),{data:t,error:o}=await e.from("threads").select("project_id").eq("is_public",!0);if(o)return console.error("Error fetching public threads:",o),[];if(!(null==t?void 0:t.length))return[];let a=[...new Set(t.map(e=>e.project_id))].filter(Boolean);if(!a.length)return[];let{data:n,error:s}=await e.from("projects").select("*").in("project_id",a);if(s)return console.error("Error fetching public projects:",s),[];console.log("[API] Raw public projects from DB:",null==n?void 0:n.length,n);let c=(n||[]).map(e=>({id:e.project_id,name:e.name||"",description:e.description||"",account_id:e.account_id,created_at:e.created_at,updated_at:e.updated_at,sandbox:e.sandbox||{id:"",pass:"",vnc_preview:"",sandbox_url:""},is_public:!0}));return console.log("[API] Mapped public projects for frontend:",c.length),c}catch(e){return console.error("Error fetching public projects:",e),(0,a.hS)(e,{operation:"load public projects",resource:"public projects"}),[]}},P=async e=>{try{let t=(0,r.U)(),{data:{session:o}}=await t.auth.getSession();if(!(null==o?void 0:o.access_token))throw new l;if(!n)throw Error("Backend URL is not configured. Set NEXT_PUBLIC_BACKEND_URL in your environment.");console.log("[API] Initiating agent with files using ".concat(n,"/agent/initiate"));let a=await fetch("".concat(n,"/agent/initiate"),{method:"POST",headers:{Authorization:"Bearer ".concat(o.access_token)},body:e,cache:"no-store"});if(!a.ok){let e=await a.text().catch(()=>"No error details available");if(console.error("[API] Error initiating agent: ".concat(a.status," ").concat(a.statusText),e),402===a.status)throw Error("Payment Required");if(401===a.status)throw Error("Authentication error: Please sign in again");if(a.status>=500)throw Error("Server error: Please try again later");throw Error("Error initiating agent: ".concat(a.statusText," (").concat(a.status,")"))}return await a.json()}catch(e){if(console.error("[API] Failed to initiate agent:",e),e instanceof TypeError&&e.message.includes("Failed to fetch")){let e=Error("Cannot connect to backend server. Please check your internet connection and make sure the backend is running.");throw(0,a.hS)(e,{operation:"initiate agent",resource:"AI assistant"}),e}throw(0,a.hS)(e,{operation:"initiate agent"}),e}},R=async()=>{try{let e=await fetch("".concat(n,"/health"),{cache:"no-store"});if(!e.ok)throw Error("API health check failed: ".concat(e.statusText));return e.json()}catch(e){throw e}},U=async e=>{try{let t=(0,r.U)(),{data:{session:o}}=await t.auth.getSession();if(!(null==o?void 0:o.access_token))throw new l;let a={...e,tolt_referral:window.tolt_referral};console.log("Tolt Referral ID:",a.tolt_referral);let s=await fetch("".concat(n,"/billing/create-checkout-session"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(o.access_token)},body:JSON.stringify(a)});if(!s.ok){let e=await s.text().catch(()=>"No error details available");throw console.error("Error creating checkout session: ".concat(s.status," ").concat(s.statusText),e),Error("Error creating checkout session: ".concat(s.statusText," (").concat(s.status,")"))}let c=await s.json();switch(console.log("Checkout session response:",c),c.status){case"upgraded":case"updated":case"downgrade_scheduled":case"scheduled":case"no_change":return c;case"new":case"checkout_created":if(!c.url)throw Error("No checkout URL provided");return c;default:return console.warn("Unexpected status from createCheckoutSession:",c.status),c}}catch(e){throw console.error("Failed to create checkout session:",e),(0,a.hS)(e,{operation:"create checkout session",resource:"billing"}),e}},I=async e=>{try{let t=(0,r.U)(),{data:{session:o}}=await t.auth.getSession();if(!(null==o?void 0:o.access_token))throw new l;let a=await fetch("".concat(n,"/billing/create-portal-session"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(o.access_token)},body:JSON.stringify(e)});if(!a.ok){let e=await a.text().catch(()=>"No error details available");throw console.error("Error creating portal session: ".concat(a.status," ").concat(a.statusText),e),Error("Error creating portal session: ".concat(a.statusText," (").concat(a.status,")"))}return a.json()}catch(e){throw console.error("Failed to create portal session:",e),(0,a.hS)(e,{operation:"create portal session",resource:"billing portal"}),e}},C=async()=>{try{let e=(0,r.U)(),{data:{session:t}}=await e.auth.getSession();if(!(null==t?void 0:t.access_token))throw new l;let o=await fetch("".concat(n,"/billing/subscription"),{headers:{Authorization:"Bearer ".concat(t.access_token)}});if(!o.ok){let e=await o.text().catch(()=>"No error details available");throw console.error("Error getting subscription: ".concat(o.status," ").concat(o.statusText),e),Error("Error getting subscription: ".concat(o.statusText," (").concat(o.status,")"))}return o.json()}catch(e){if(e instanceof l)throw e;throw console.error("Failed to get subscription:",e),(0,a.hS)(e,{operation:"load subscription",resource:"billing information"}),e}},N=async()=>{try{let e=(0,r.U)(),{data:{session:t}}=await e.auth.getSession();if(!(null==t?void 0:t.access_token))throw new l;let o=await fetch("".concat(n,"/billing/available-models"),{headers:{Authorization:"Bearer ".concat(t.access_token)}});if(!o.ok){let e=await o.text().catch(()=>"No error details available");throw console.error("Error getting available models: ".concat(o.status," ").concat(o.statusText),e),Error("Error getting available models: ".concat(o.statusText," (").concat(o.status,")"))}return o.json()}catch(e){if(e instanceof l)throw e;throw console.error("Failed to get available models:",e),(0,a.hS)(e,{operation:"load available models",resource:"AI models"}),e}},M=async()=>{try{let e=(0,r.U)(),{data:{session:t}}=await e.auth.getSession();if(!(null==t?void 0:t.access_token))throw new l;let o=await fetch("".concat(n,"/billing/check-status"),{headers:{Authorization:"Bearer ".concat(t.access_token)}});if(!o.ok){let e=await o.text().catch(()=>"No error details available");throw console.error("Error checking billing status: ".concat(o.status," ").concat(o.statusText),e),Error("Error checking billing status: ".concat(o.statusText," (").concat(o.status,")"))}return o.json()}catch(e){if(e instanceof l)throw e;throw console.error("Failed to check billing status:",e),e}},B=async e=>{try{let t=(0,r.U)(),{data:{session:o}}=await t.auth.getSession();if(!(null==o?void 0:o.access_token))throw new l;let a=new FormData;a.append("audio_file",e);let s=await fetch("".concat(n,"/transcription"),{method:"POST",headers:{Authorization:"Bearer ".concat(o.access_token)},body:a});if(!s.ok){let e=await s.text().catch(()=>"No error details available");throw console.error("Error transcribing audio: ".concat(s.status," ").concat(s.statusText),e),Error("Error transcribing audio: ".concat(s.statusText," (").concat(s.status,")"))}return s.json()}catch(e){if(e instanceof l)throw e;throw console.error("Failed to transcribe audio:",e),(0,a.hS)(e,{operation:"transcribe audio",resource:"speech-to-text"}),e}}},33356:(e,t,o)=>{o.d(t,{hS:()=>l,nQ:()=>u,uC:()=>d});var r=o(56671),a=o(25731);let n=e=>{switch(e){case 400:return"Invalid request. Please check your input and try again.";case 401:return"Authentication required. Please sign in again.";case 403:return"Access denied. You don't have permission to perform this action.";case 404:return"The requested resource was not found.";case 408:return"Request timeout. Please try again.";case 409:return"Conflict detected. The resource may have been modified by another user.";case 422:return"Invalid data provided. Please check your input.";case 429:return"Too many requests. Please wait a moment and try again.";case 500:return"Server error. Our team has been notified.";case 502:return"Service temporarily unavailable. Please try again in a moment.";case 503:return"Service maintenance in progress. Please try again later.";case 504:return"Request timeout. The server took too long to respond.";default:return"An unexpected error occurred. Please try again."}},s=e=>{if(e instanceof a.Ey){var t;return(null==(t=e.detail)?void 0:t.message)||e.message||"Billing issue detected"}return e instanceof Error?e.message:(null==e?void 0:e.response)?n(e.response.status):(null==e?void 0:e.status)?n(e.status):"string"==typeof e?e:(null==e?void 0:e.message)?e.message:(null==e?void 0:e.error)?"string"==typeof e.error?e.error:e.error.message||"Unknown error":"An unexpected error occurred"},c=(e,t)=>(null==t||!t.silent)&&!(e instanceof a.Ey)&&((null==e?void 0:e.status)!==404||null==t||!t.resource),i=(e,t)=>{var o;if(!(null==t?void 0:t.operation)&&!(null==t?void 0:t.resource))return e;let r=[];t.operation&&r.push("Failed to ".concat(t.operation)),t.resource&&r.push(t.resource);let a=r.join(" ");return e.toLowerCase().includes((null==(o=t.operation)?void 0:o.toLowerCase())||"")?e:"".concat(a,": ").concat(e)},l=(e,t)=>{if(console.error("API Error:",e,t),!c(e,t))return;let o=i(s(e),t);(null==e?void 0:e.status)>=500?r.oR.error(o,{description:"Our team has been notified and is working on a fix.",duration:6e3}):(null==e?void 0:e.status)===401?r.oR.error(o,{description:"Please refresh the page and sign in again.",duration:8e3}):(null==e?void 0:e.status)===403?r.oR.error(o,{description:"Contact support if you believe this is an error.",duration:6e3}):(null==e?void 0:e.status)===429?r.oR.warning(o,{description:"Please wait a moment before trying again.",duration:5e3}):r.oR.error(o,{duration:5e3})},u=(e,t)=>{var o,a,n;(null==e||null==(o=e.message)?void 0:o.includes("fetch"))||(null==e||null==(a=e.message)?void 0:a.includes("network"))||(null==e||null==(n=e.message)?void 0:n.includes("connection"))||(null==e?void 0:e.code)==="NETWORK_ERROR"||!navigator.onLine?r.oR.error("Connection error",{description:"Please check your internet connection and try again.",duration:6e3}):l(e,t)},d=(e,t)=>{r.oR.success(e,{description:t,duration:3e3})}},52643:(e,t,o)=>{o.d(t,{U:()=>a});var r=o(81935);let a=()=>{let e="";return e&&!e.startsWith("http")&&(e="http://".concat(e)),(0,r.createBrowserClient)(e,"")}}}]);