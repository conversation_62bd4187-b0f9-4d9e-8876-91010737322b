(()=>{var e={};e.id=1943,e.ids=[1943],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37553:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\(teamAccount)\\\\[accountSlug]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\page.tsx","default")},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63107:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(16189),n=t(43210),o=t.n(n);function a({params:e}){let{accountSlug:r}=o().use(e);(0,s.redirect)(`/${r}/settings`)}},69820:(e,r,t)=>{Promise.resolve().then(t.bind(t,63107))},70400:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"60120624b62a67d0046abca062aae687cbb5ebc44a":()=>s.vI,"602190608cb4aada86562e5e5997e6bb44fbe95e4e":()=>s.$w,"60836a64bf90333e8dead87703a0c5a32e95fa0f8f":()=>s.gj});var s=t(67834)},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83388:(e,r,t)=>{Promise.resolve().then(t.bind(t,37553))},84297:e=>{"use strict";e.exports=require("async_hooks")},91645:e=>{"use strict";e.exports=require("net")},91742:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>u});var s=t(65239),n=t(48088),o=t(31369),a=t(30893),i={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>a[e]);t.d(r,i);let u={children:["",{children:["(dashboard)",{children:["(teamAccount)",{children:["[accountSlug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,37553)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,33532)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,88524))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,93595)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,88524))).default(e)],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(dashboard)/(teamAccount)/[accountSlug]/page",pathname:"/[accountSlug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,5193,4267,7096,1265,3530,7156,7976,3667,8188,3806,1841],()=>t(91742));module.exports=s})();