"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7354,9158],{49158:(e,t,n)=>{n.r(t),n.d(t,{default:()=>i});var a=n(87354);let s=Object.freeze(JSON.parse('{"displayName":"1C (Enterprise)","fileTypes":["bsl","os"],"name":"bsl","patterns":[{"include":"#basic"},{"include":"#miscellaneous"},{"begin":"(?i:(?<=[^.а-яё\\\\w]|^)(Процедура|Procedure|Функция|Function)\\\\s+([0-9_a-zа-яё]+)\\\\s*(\\\\())","beginCaptures":{"1":{"name":"storage.type.bsl"},"2":{"name":"entity.name.function.bsl"},"3":{"name":"punctuation.bracket.begin.bsl"}},"end":"(?i:(\\\\))\\\\s*((Экспорт|Export)(?=[^.а-яё\\\\w]|$))?)","endCaptures":{"1":{"name":"punctuation.bracket.end.bsl"},"2":{"name":"storage.modifier.bsl"}},"patterns":[{"include":"#annotations"},{"include":"#basic"},{"match":"(=)","name":"keyword.operator.assignment.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(Знач|Val)(?=[^.а-яё\\\\w]|$))","name":"storage.modifier.bsl"},{"match":"(?<=[^.а-яё\\\\w]|^)((?<==)(?i)[0-9_a-zа-яё]+)(?=[^.а-яё\\\\w]|$)","name":"invalid.illegal.bsl"},{"match":"(?<=[^.а-яё\\\\w]|^)((?<==\\\\s)\\\\s*(?i)[0-9_a-zа-яё]+)(?=[^.а-яё\\\\w]|$)","name":"invalid.illegal.bsl"},{"match":"(?i:[0-9_a-zа-яё]+)","name":"variable.parameter.bsl"}]},{"begin":"(?i:(?<=[^.а-яё\\\\w]|^)(Перем|Var)\\\\s+([0-9_a-zа-яё]+)\\\\s*)","beginCaptures":{"1":{"name":"storage.type.var.bsl"},"2":{"name":"variable.bsl"}},"end":"(;)","endCaptures":{"1":{"name":"keyword.operator.bsl"}},"patterns":[{"match":"(,)","name":"keyword.operator.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(Экспорт|Export)(?=[^.а-яё\\\\w]|$))","name":"storage.modifier.bsl"},{"match":"(?i:[0-9_a-zа-яё]+)","name":"variable.bsl"}]},{"begin":"(?i:(?<=;|^)\\\\s*(Если|If))","beginCaptures":{"1":{"name":"keyword.control.conditional.bsl"}},"end":"(?i:(Тогда|Then))","endCaptures":{"1":{"name":"keyword.control.conditional.bsl"}},"name":"meta.conditional.bsl","patterns":[{"include":"#basic"},{"include":"#miscellaneous"}]},{"begin":"(?i:(?<=;|^)\\\\s*([а-яё\\\\w]+))\\\\s*(=)","beginCaptures":{"1":{"name":"variable.assignment.bsl"},"2":{"name":"keyword.operator.assignment.bsl"}},"end":"(?i:(?=(;|Иначе|Конец|Els|End)))","name":"meta.var-single-variable.bsl","patterns":[{"include":"#basic"},{"include":"#miscellaneous"}]},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(КонецПроцедуры|EndProcedure|КонецФункции|EndFunction)(?=[^.а-яё\\\\w]|$))","name":"storage.type.bsl"},{"match":"(?i)#(Использовать|Use)(?=[^.а-яё\\\\w]|$)","name":"keyword.control.import.bsl"},{"match":"(?i)#native","name":"keyword.control.native.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(Прервать|Break|Продолжить|Continue|Возврат|Return)(?=[^.а-яё\\\\w]|$))","name":"keyword.control.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(Если|If|Иначе|Else|ИначеЕсли|ElsIf|Тогда|Then|КонецЕсли|EndIf)(?=[^.а-яё\\\\w]|$))","name":"keyword.control.conditional.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(Попытка|Try|Исключение|Except|КонецПопытки|EndTry|ВызватьИсключение|Raise)(?=[^.а-яё\\\\w]|$))","name":"keyword.control.exception.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(Пока|While|(Для|For)(\\\\s+(Каждого|Each))?|Из|In|По|To|Цикл|Do|КонецЦикла|EndDo)(?=[^.а-яё\\\\w]|$))","name":"keyword.control.repeat.bsl"},{"match":"(?i:&(НаКлиенте((НаСервере(БезКонтекста)?)?)|AtClient((AtServer(NoContext)?)?)|НаСервере(БезКонтекста)?|AtServer(NoContext)?))","name":"storage.modifier.directive.bsl"},{"include":"#annotations"},{"match":"(?i:#(Если|If|ИначеЕсли|ElsIf|Иначе|Else|КонецЕсли|EndIf).*(Тогда|Then)?)","name":"keyword.other.preprocessor.bsl"},{"begin":"(?i)(#(Область|Region))(\\\\s+([а-яё\\\\w]+))?","beginCaptures":{"1":{"name":"keyword.other.section.bsl"},"4":{"name":"entity.name.section.bsl"}},"end":"$"},{"match":"(?i)#(КонецОбласти|EndRegion)","name":"keyword.other.section.bsl"},{"match":"(?i)#(Удаление|Delete)","name":"keyword.other.section.bsl"},{"match":"(?i)#(КонецУдаления|EndDelete)","name":"keyword.other.section.bsl"},{"match":"(?i)#(Вставка|Insert)","name":"keyword.other.section.bsl"},{"match":"(?i)#(КонецВставки|EndInsert)","name":"keyword.other.section.bsl"}],"repository":{"annotations":{"patterns":[{"begin":"(?i)(&([0-9_a-zа-яё]+))\\\\s*(\\\\()","beginCaptures":{"1":{"name":"storage.type.annotation.bsl"},"3":{"name":"punctuation.bracket.begin.bsl"}},"end":"(\\\\))","endCaptures":{"1":{"name":"punctuation.bracket.end.bsl"}},"patterns":[{"include":"#basic"},{"match":"(=)","name":"keyword.operator.assignment.bsl"},{"match":"(?<=[^.а-яё\\\\w]|^)((?<==)(?i)[0-9_a-zа-яё]+)(?=[^.а-яё\\\\w]|$)","name":"invalid.illegal.bsl"},{"match":"(?<=[^.а-яё\\\\w]|^)((?<==\\\\s)\\\\s*(?i)[0-9_a-zа-яё]+)(?=[^.а-яё\\\\w]|$)","name":"invalid.illegal.bsl"},{"match":"(?i)[0-9_a-zа-яё]+","name":"variable.annotation.bsl"}]},{"match":"(?i)(&([0-9_a-zа-яё]+))","name":"storage.type.annotation.bsl"}]},"basic":{"patterns":[{"begin":"//","end":"$","name":"comment.line.double-slash.bsl"},{"begin":"\\"","end":"\\"(?!\\")","name":"string.quoted.double.bsl","patterns":[{"include":"#query"},{"match":"\\"\\"","name":"constant.character.escape.bsl"},{"match":"^(\\\\s*//.*)$","name":"comment.line.double-slash.bsl"}]},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(Неопределено|Undefined|Истина|True|Ложь|False|NULL)(?=[^.а-яё\\\\w]|$))","name":"constant.language.bsl"},{"match":"(?<=[^.а-яё\\\\w]|^)(\\\\d+\\\\.?\\\\d*)(?=[^.а-яё\\\\w]|$)","name":"constant.numeric.bsl"},{"match":"\'((\\\\d{4}[^\'\\\\d]*\\\\d{2}[^\'\\\\d]*\\\\d{2})([^\'\\\\d]*\\\\d{2}[^\'\\\\d]*\\\\d{2}([^\'\\\\d]*\\\\d{2})?)?)\'","name":"constant.other.date.bsl"},{"match":"(,)","name":"keyword.operator.bsl"},{"match":"(\\\\()","name":"punctuation.bracket.begin.bsl"},{"match":"(\\\\))","name":"punctuation.bracket.end.bsl"}]},"miscellaneous":{"patterns":[{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(НЕ|NOT|И|AND|ИЛИ|OR)(?=[^.а-яё\\\\w]|$))","name":"keyword.operator.logical.bsl"},{"match":"<=|>=|[<=>]","name":"keyword.operator.comparison.bsl"},{"match":"([-%*+/])","name":"keyword.operator.arithmetic.bsl"},{"match":"([;?])","name":"keyword.operator.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(Новый|New)(?=[^.а-яё\\\\w]|$))","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(СтрДлина|StrLen|СокрЛ|TrimL|СокрП|TrimR|СокрЛП|TrimAll|Лев|Left|Прав|Right|Сред|Mid|СтрНайти|StrFind|ВРег|Upper|НРег|Lower|ТРег|Title|Символ|Char|КодСимвола|CharCode|ПустаяСтрока|IsBlankString|СтрЗаменить|StrReplace|СтрЧислоСтрок|StrLineCount|СтрПолучитьСтроку|StrGetLine|СтрЧислоВхождений|StrOccurrenceCount|СтрСравнить|StrCompare|СтрНачинаетсяС|StrStartWith|СтрЗаканчиваетсяНа|StrEndsWith|СтрРазделить|StrSplit|СтрСоединить|StrConcat)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(Цел|Int|Окр|Round|ACos|ASin|ATan|Cos|Exp|Log|Log10|Pow|Sin|Sqrt|Tan)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(Год|Year|Месяц|Month|День|Day|Час|Hour|Минута|Minute|Секунда|Second|НачалоГода|BegOfYear|НачалоДня|BegOfDay|НачалоКвартала|BegOfQuarter|НачалоМесяца|BegOfMonth|НачалоМинуты|BegOfMinute|НачалоНедели|BegOfWeek|НачалоЧаса|BegOfHour|КонецГода|EndOfYear|КонецДня|EndOfDay|КонецКвартала|EndOfQuarter|КонецМесяца|EndOfMonth|КонецМинуты|EndOfMinute|КонецНедели|EndOfWeek|КонецЧаса|EndOfHour|НеделяГода|WeekOfYear|ДеньГода|DayOfYear|ДеньНедели|WeekDay|ТекущаяДата|CurrentDate|ДобавитьМесяц|AddMonth)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(Тип|Type|ТипЗнч|TypeOf)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(Булево|Boolean|Число|Number|Строка|String|Дата|Date)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(ПоказатьВопрос|ShowQueryBox|Вопрос|DoQueryBox|ПоказатьПредупреждение|ShowMessageBox|Предупреждение|DoMessageBox|Сообщить|Message|ОчиститьСообщения|ClearMessages|ОповеститьОбИзменении|NotifyChanged|Состояние|Status|Сигнал|Beep|ПоказатьЗначение|ShowValue|ОткрытьЗначение|OpenValue|Оповестить|Notify|ОбработкаПрерыванияПользователя|UserInterruptProcessing|ОткрытьСодержаниеСправки|OpenHelpContent|ОткрытьИндексСправки|OpenHelpIndex|ОткрытьСправку|OpenHelp|ПоказатьИнформациюОбОшибке|ShowErrorInfo|КраткоеПредставлениеОшибки|BriefErrorDescription|ПодробноеПредставлениеОшибки|DetailErrorDescription|ПолучитьФорму|GetForm|ЗакрытьСправку|CloseHelp|ПоказатьОповещениеПользователя|ShowUserNotification|ОткрытьФорму|OpenForm|ОткрытьФормуМодально|OpenFormModal|АктивноеОкно|ActiveWindow|ВыполнитьОбработкуОповещения|ExecuteNotifyProcessing)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(ПоказатьВводЗначения|ShowInputValue|ВвестиЗначение|InputValue|ПоказатьВводЧисла|ShowInputNumber|ВвестиЧисло|InputNumber|ПоказатьВводСтроки|ShowInputString|ВвестиСтроку|InputString|ПоказатьВводДаты|ShowInputDate|ВвестиДату|InputDate)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(Формат|Format|ЧислоПрописью|NumberInWords|НСтр|NStr|ПредставлениеПериода|PeriodPresentation|СтрШаблон|StrTemplate)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(ПолучитьОбщийМакет|GetCommonTemplate|ПолучитьОбщуюФорму|GetCommonForm|ПредопределенноеЗначение|PredefinedValue|ПолучитьПолноеИмяПредопределенногоЗначения|GetPredefinedValueFullName)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(ПолучитьЗаголовокСистемы|GetCaption|ПолучитьСкоростьКлиентскогоСоединения|GetClientConnectionSpeed|ПодключитьОбработчикОжидания|AttachIdleHandler|УстановитьЗаголовокСистемы|SetCaption|ОтключитьОбработчикОжидания|DetachIdleHandler|ИмяКомпьютера|ComputerName|ЗавершитьРаботуСистемы|Exit|ИмяПользователя|UserName|ПрекратитьРаботуСистемы|Terminate|ПолноеИмяПользователя|UserFullName|ЗаблокироватьРаботуПользователя|LockApplication|КаталогПрограммы|BinDir|КаталогВременныхФайлов|TempFilesDir|ПравоДоступа|AccessRight|РольДоступна|IsInRole|ТекущийЯзык|CurrentLanguage|ТекущийКодЛокализации|CurrentLocaleCode|СтрокаСоединенияИнформационнойБазы|InfoBaseConnectionString|ПодключитьОбработчикОповещения|AttachNotificationHandler|ОтключитьОбработчикОповещения|DetachNotificationHandler|ПолучитьСообщенияПользователю|GetUserMessages|ПараметрыДоступа|AccessParameters|ПредставлениеПриложения|ApplicationPresentation|ТекущийЯзыкСистемы|CurrentSystemLanguage|ЗапуститьСистему|RunSystem|ТекущийРежимЗапуска|CurrentRunMode|УстановитьЧасовойПоясСеанса|SetSessionTimeZone|ЧасовойПоясСеанса|SessionTimeZone|ТекущаяДатаСеанса|CurrentSessionDate|УстановитьКраткийЗаголовокПриложения|SetShortApplicationCaption|ПолучитьКраткийЗаголовокПриложения|GetShortApplicationCaption|ПредставлениеПрава|RightPresentation|ВыполнитьПроверкуПравДоступа|VerifyAccessRights|РабочийКаталогДанныхПользователя|UserDataWorkDir|КаталогДокументов|DocumentsDir|ПолучитьИнформациюЭкрановКлиента|GetClientDisplaysInformation|ТекущийВариантОсновногоШрифтаКлиентскогоПриложения|ClientApplicationBaseFontCurrentVariant|ТекущийВариантИнтерфейсаКлиентскогоПриложения|ClientApplicationInterfaceCurrentVariant|УстановитьЗаголовокКлиентскогоПриложения|SetClientApplicationCaption|ПолучитьЗаголовокКлиентскогоПриложения|GetClientApplicationCaption|НачатьПолучениеКаталогаВременныхФайлов|BeginGettingTempFilesDir|НачатьПолучениеКаталогаДокументов|BeginGettingDocumentsDir|НачатьПолучениеРабочегоКаталогаДанныхПользователя|BeginGettingUserDataWorkDir|ПодключитьОбработчикЗапросаНастроекКлиентаЛицензирования|AttachLicensingClientParametersRequestHandler|ОтключитьОбработчикЗапросаНастроекКлиентаЛицензирования|DetachLicensingClientParametersRequestHandler|КаталогБиблиотекиМобильногоУстройства|MobileDeviceLibraryDir)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(ЗначениеВСтрокуВнутр|ValueToStringInternal|ЗначениеИзСтрокиВнутр|ValueFromStringInternal|ЗначениеВФайл|ValueToFile|ЗначениеИзФайла|ValueFromFile)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(КомандаСистемы|System|ЗапуститьПриложение|RunApp|ПолучитьCOMОбъект|GetCOMObject|ПользователиОС|OSUsers|НачатьЗапускПриложения|BeginRunningApplication)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(ПодключитьВнешнююКомпоненту|AttachAddIn|НачатьУстановкуВнешнейКомпоненты|BeginInstallAddIn|УстановитьВнешнююКомпоненту|InstallAddIn|НачатьПодключениеВнешнейКомпоненты|BeginAttachingAddIn)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(КопироватьФайл|FileCopy|ПереместитьФайл|MoveFile|УдалитьФайлы|DeleteFiles|НайтиФайлы|FindFiles|СоздатьКаталог|CreateDirectory|ПолучитьИмяВременногоФайла|GetTempFileName|РазделитьФайл|SplitFile|ОбъединитьФайлы|MergeFiles|ПолучитьФайл|GetFile|НачатьПомещениеФайла|BeginPutFile|ПоместитьФайл|PutFile|ЭтоАдресВременногоХранилища|IsTempStorageURL|УдалитьИзВременногоХранилища|DeleteFromTempStorage|ПолучитьИзВременногоХранилища|GetFromTempStorage|ПоместитьВоВременноеХранилище|PutToTempStorage|ПодключитьРасширениеРаботыСФайлами|AttachFileSystemExtension|НачатьУстановкуРасширенияРаботыСФайлами|BeginInstallFileSystemExtension|УстановитьРасширениеРаботыСФайлами|InstallFileSystemExtension|ПолучитьФайлы|GetFiles|ПоместитьФайлы|PutFiles|ЗапроситьРазрешениеПользователя|RequestUserPermission|ПолучитьМаскуВсеФайлы|GetAllFilesMask|ПолучитьМаскуВсеФайлыКлиента|GetClientAllFilesMask|ПолучитьМаскуВсеФайлыСервера|GetServerAllFilesMask|ПолучитьРазделительПути|GetPathSeparator|ПолучитьРазделительПутиКлиента|GetClientPathSeparator|ПолучитьРазделительПутиСервера|GetServerPathSeparator|НачатьПодключениеРасширенияРаботыСФайлами|BeginAttachingFileSystemExtension|НачатьЗапросРазрешенияПользователя|BeginRequestingUserPermission|НачатьПоискФайлов|BeginFindingFiles|НачатьСозданиеКаталога|BeginCreatingDirectory|НачатьКопированиеФайла|BeginCopyingFile|НачатьПеремещениеФайла|BeginMovingFile|НачатьУдалениеФайлов|BeginDeletingFiles|НачатьПолучениеФайлов|BeginGettingFiles|НачатьПомещениеФайлов|BeginPuttingFiles|НачатьСозданиеДвоичныхДанныхИзФайла|BeginCreateBinaryDataFromFile)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(НачатьТранзакцию|BeginTransaction|ЗафиксироватьТранзакцию|CommitTransaction|ОтменитьТранзакцию|RollbackTransaction|УстановитьМонопольныйРежим|SetExclusiveMode|МонопольныйРежим|ExclusiveMode|ПолучитьОперативнуюОтметкуВремени|GetRealTimeTimestamp|ПолучитьСоединенияИнформационнойБазы|GetInfoBaseConnections|НомерСоединенияИнформационнойБазы|InfoBaseConnectionNumber|КонфигурацияИзменена|ConfigurationChanged|КонфигурацияБазыДанныхИзмененаДинамически|DataBaseConfigurationChangedDynamically|УстановитьВремяОжиданияБлокировкиДанных|SetLockWaitTime|ОбновитьНумерациюОбъектов|RefreshObjectsNumbering|ПолучитьВремяОжиданияБлокировкиДанных|GetLockWaitTime|КодЛокализацииИнформационнойБазы|InfoBaseLocaleCode|УстановитьМинимальнуюДлинуПаролейПользователей|SetUserPasswordMinLength|ПолучитьМинимальнуюДлинуПаролейПользователей|GetUserPasswordMinLength|ИнициализироватьПредопределенныеДанные|InitializePredefinedData|УдалитьДанныеИнформационнойБазы|EraseInfoBaseData|УстановитьПроверкуСложностиПаролейПользователей|SetUserPasswordStrengthCheck|ПолучитьПроверкуСложностиПаролейПользователей|GetUserPasswordStrengthCheck|ПолучитьСтруктуруХраненияБазыДанных|GetDBStorageStructureInfo|УстановитьПривилегированныйРежим|SetPrivilegedMode|ПривилегированныйРежим|PrivilegedMode|ТранзакцияАктивна|TransactionActive|НеобходимостьЗавершенияСоединения|ConnectionStopRequest|НомерСеансаИнформационнойБазы|InfoBaseSessionNumber|ПолучитьСеансыИнформационнойБазы|GetInfoBaseSessions|ЗаблокироватьДанныеДляРедактирования|LockDataForEdit|УстановитьСоединениеСВнешнимИсточникомДанных|ConnectExternalDataSource|РазблокироватьДанныеДляРедактирования|UnlockDataForEdit|РазорватьСоединениеСВнешнимИсточникомДанных|DisconnectExternalDataSource|ПолучитьБлокировкуСеансов|GetSessionsLock|УстановитьБлокировкуСеансов|SetSessionsLock|ОбновитьПовторноИспользуемыеЗначения|RefreshReusableValues|УстановитьБезопасныйРежим|SetSafeMode|БезопасныйРежим|SafeMode|ПолучитьДанныеВыбора|GetChoiceData|УстановитьЧасовойПоясИнформационнойБазы|SetInfoBaseTimeZone|ПолучитьЧасовойПоясИнформационнойБазы|GetInfoBaseTimeZone|ПолучитьОбновлениеКонфигурацииБазыДанных|GetDataBaseConfigurationUpdate|УстановитьБезопасныйРежимРазделенияДанных|SetDataSeparationSafeMode|БезопасныйРежимРазделенияДанных|DataSeparationSafeMode|УстановитьВремяЗасыпанияПассивногоСеанса|SetPassiveSessionHibernateTime|ПолучитьВремяЗасыпанияПассивногоСеанса|GetPassiveSessionHibernateTime|УстановитьВремяЗавершенияСпящегоСеанса|SetHibernateSessionTerminateTime|ПолучитьВремяЗавершенияСпящегоСеанса|GetHibernateSessionTerminateTime|ПолучитьТекущийСеансИнформационнойБазы|GetCurrentInfoBaseSession|ПолучитьИдентификаторКонфигурации|GetConfigurationID|УстановитьНастройкиКлиентаЛицензирования|SetLicensingClientParameters|ПолучитьИмяКлиентаЛицензирования|GetLicensingClientName|ПолучитьДополнительныйПараметрКлиентаЛицензирования|GetLicensingClientAdditionalParameter|ПолучитьОтключениеБезопасногоРежима|GetSafeModeDisabled|УстановитьОтключениеБезопасногоРежима|SetSafeModeDisabled)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(НайтиПомеченныеНаУдаление|FindMarkedForDeletion|НайтиПоСсылкам|FindByRef|УдалитьОбъекты|DeleteObjects|УстановитьОбновлениеПредопределенныхДанныхИнформационнойБазы|SetInfoBasePredefinedDataUpdate|ПолучитьОбновлениеПредопределенныхДанныхИнформационнойБазы|GetInfoBasePredefinedData)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(XMLСтрока|XMLString|XMLЗначение|XMLValue|XMLТип|XMLType|XMLТипЗнч|XMLTypeOf|ИзXMLТипа|FromXMLType|ВозможностьЧтенияXML|CanReadXML|ПолучитьXMLТип|GetXMLType|ПрочитатьXML|ReadXML|ЗаписатьXML|WriteXML|НайтиНедопустимыеСимволыXML|FindDisallowedXMLCharacters|ИмпортМоделиXDTO|ImportXDTOModel|СоздатьФабрикуXDTO|CreateXDTOFactory)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(ЗаписатьJSON|WriteJSON|ПрочитатьJSON|ReadJSON|ПрочитатьДатуJSON|ReadJSONDate|ЗаписатьДатуJSON|WriteJSONDate)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(ЗаписьЖурналаРегистрации|WriteLogEvent|ПолучитьИспользованиеЖурналаРегистрации|GetEventLogUsing|УстановитьИспользованиеЖурналаРегистрации|SetEventLogUsing|ПредставлениеСобытияЖурналаРегистрации|EventLogEventPresentation|ВыгрузитьЖурналРегистрации|UnloadEventLog|ПолучитьЗначенияОтбораЖурналаРегистрации|GetEventLogFilterValues|УстановитьИспользованиеСобытияЖурналаРегистрации|SetEventLogEventUse|ПолучитьИспользованиеСобытияЖурналаРегистрации|GetEventLogEventUse|СкопироватьЖурналРегистрации|CopyEventLog|ОчиститьЖурналРегистрации|ClearEventLog)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(ЗначениеВДанныеФормы|ValueToFormData|ДанныеФормыВЗначение|FormDataToValue|КопироватьДанныеФормы|CopyFormData|УстановитьСоответствиеОбъектаИФормы|SetObjectAndFormConformity|ПолучитьСоответствиеОбъектаИФормы|GetObjectAndFormConformity)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(ПолучитьФункциональнуюОпцию|GetFunctionalOption|ПолучитьФункциональнуюОпциюИнтерфейса|GetInterfaceFunctionalOption|УстановитьПараметрыФункциональныхОпцийИнтерфейса|SetInterfaceFunctionalOptionParameters|ПолучитьПараметрыФункциональныхОпцийИнтерфейса|GetInterfaceFunctionalOptionParameters|ОбновитьИнтерфейс|RefreshInterface)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(УстановитьРасширениеРаботыСКриптографией|InstallCryptoExtension|НачатьУстановкуРасширенияРаботыСКриптографией|BeginInstallCryptoExtension|ПодключитьРасширениеРаботыСКриптографией|AttachCryptoExtension|НачатьПодключениеРасширенияРаботыСКриптографией|BeginAttachingCryptoExtension)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(УстановитьСоставСтандартногоИнтерфейсаOData|SetStandardODataInterfaceContent|ПолучитьСоставСтандартногоИнтерфейсаOData|GetStandardODataInterfaceContent)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(СоединитьБуферыДвоичныхДанных|ConcatBinaryDataBuffers)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(Мин|Min|Макс|Max|ОписаниеОшибки|ErrorDescription|Вычислить|Eval|ИнформацияОбОшибке|ErrorInfo|Base64Значение|Base64Value|Base64Строка|Base64String|ЗаполнитьЗначенияСвойств|FillPropertyValues|ЗначениеЗаполнено|ValueIsFilled|ПолучитьПредставленияНавигационныхСсылок|GetURLsPresentations|НайтиОкноПоНавигационнойСсылке|FindWindowByURL|ПолучитьОкна|GetWindows|ПерейтиПоНавигационнойСсылке|GotoURL|ПолучитьНавигационнуюСсылку|GetURL|ПолучитьДопустимыеКодыЛокализации|GetAvailableLocaleCodes|ПолучитьНавигационнуюСсылкуИнформационнойБазы|GetInfoBaseURL|ПредставлениеКодаЛокализации|LocaleCodePresentation|ПолучитьДопустимыеЧасовыеПояса|GetAvailableTimeZones|ПредставлениеЧасовогоПояса|TimeZonePresentation|ТекущаяУниверсальнаяДата|CurrentUniversalDate|ТекущаяУниверсальнаяДатаВМиллисекундах|CurrentUniversalDateInMilliseconds|МестноеВремя|ToLocalTime|УниверсальноеВремя|ToUniversalTime|ЧасовойПояс|TimeZone|СмещениеЛетнегоВремени|DaylightTimeOffset|СмещениеСтандартногоВремени|StandardTimeOffset|КодироватьСтроку|EncodeString|РаскодироватьСтроку|DecodeString|Найти|Find|ПродолжитьВызов|ProceedWithCall)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(ПередНачаломРаботыСистемы|BeforeStart|ПриНачалеРаботыСистемы|OnStart|ПередЗавершениемРаботыСистемы|BeforeExit|ПриЗавершенииРаботыСистемы|OnExit|ОбработкаВнешнегоСобытия|ExternEventProcessing|УстановкаПараметровСеанса|SessionParametersSetting|ПриИзмененииПараметровЭкрана|OnChangeDisplaySettings)\\\\s*(?=\\\\())","name":"support.function.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(WSСсылки|WSReferences|БиблиотекаКартинок|PictureLib|БиблиотекаМакетовОформленияКомпоновкиДанных|DataCompositionAppearanceTemplateLib|БиблиотекаСтилей|StyleLib|БизнесПроцессы|BusinessProcesses|ВнешниеИсточникиДанных|ExternalDataSources|ВнешниеОбработки|ExternalDataProcessors|ВнешниеОтчеты|ExternalReports|Документы|Documents|ДоставляемыеУведомления|DeliverableNotifications|ЖурналыДокументов|DocumentJournals|Задачи|Tasks|ИнформацияОбИнтернетСоединении|InternetConnectionInformation|ИспользованиеРабочейДаты|WorkingDateUse|ИсторияРаботыПользователя|UserWorkHistory|Константы|Constants|КритерииОтбора|FilterCriteria|Метаданные|Metadata|Обработки|DataProcessors|ОтправкаДоставляемыхУведомлений|DeliverableNotificationSend|Отчеты|Reports|ПараметрыСеанса|SessionParameters|Перечисления|Enums|ПланыВидовРасчета|ChartsOfCalculationTypes|ПланыВидовХарактеристик|ChartsOfCharacteristicTypes|ПланыОбмена|ExchangePlans|ПланыСчетов|ChartsOfAccounts|ПолнотекстовыйПоиск|FullTextSearch|ПользователиИнформационнойБазы|InfoBaseUsers|Последовательности|Sequences|РасширенияКонфигурации|ConfigurationExtensions|РегистрыБухгалтерии|AccountingRegisters|РегистрыНакопления|AccumulationRegisters|РегистрыРасчета|CalculationRegisters|РегистрыСведений|InformationRegisters|РегламентныеЗадания|ScheduledJobs|СериализаторXDTO|XDTOSerializer|Справочники|Catalogs|СредстваГеопозиционирования|LocationTools|СредстваКриптографии|CryptoToolsManager|СредстваМультимедиа|MultimediaTools|СредстваОтображенияРекламы|AdvertisingPresentationTools|СредстваПочты|MailTools|СредстваТелефонии|TelephonyTools|ФабрикаXDTO|XDTOFactory|ФайловыеПотоки|FileStreams|ФоновыеЗадания|BackgroundJobs|ХранилищаНастроек|SettingsStorages|ВстроенныеПокупки|InAppPurchases|ОтображениеРекламы|AdRepresentation|ПанельЗадачОС|OSTaskbar|ПроверкаВстроенныхПокупок|InAppPurchasesValidation)(?=[^а-яё\\\\w]|$))","name":"support.class.bsl"},{"match":"(?i:(?<=[^.а-яё\\\\w]|^)(ГлавныйИнтерфейс|MainInterface|ГлавныйСтиль|MainStyle|ПараметрЗапуска|LaunchParameter|РабочаяДата|WorkingDate|ХранилищеВариантовОтчетов|ReportsVariantsStorage|ХранилищеНастроекДанныхФорм|FormDataSettingsStorage|ХранилищеОбщихНастроек|CommonSettingsStorage|ХранилищеПользовательскихНастроекДинамическихСписков|DynamicListsUserSettingsStorage|ХранилищеПользовательскихНастроекОтчетов|ReportsUserSettingsStorage|ХранилищеСистемныхНастроек|SystemSettingsStorage)(?=[^а-яё\\\\w]|$))","name":"support.variable.bsl"}]},"query":{"begin":"(?i)(?<=[^.а-яё\\\\w]|^)(Выбрать|Select(\\\\s+Разрешенные|\\\\s+Allowed)?(\\\\s+Различные|\\\\s+Distinct)?(\\\\s+Первые|\\\\s+Top)?)(?=[^.а-яё\\\\w]|$)","beginCaptures":{"1":{"name":"keyword.control.sdbl"}},"end":"(?=\\"[^\\"])","patterns":[{"begin":"^\\\\s*//","end":"$","name":"comment.line.double-slash.bsl"},{"match":"(//((\\"\\")|[^\\"])*)","name":"comment.line.double-slash.sdbl"},{"match":"\\"\\"[^\\"]*\\"\\"","name":"string.quoted.double.sdbl"},{"include":"source.sdbl"}]}},"scopeName":"source.bsl","embeddedLangs":["sdbl"],"aliases":["1c"]}')),i=[...a.default,s]},87354:(e,t,n)=>{n.r(t),n.d(t,{default:()=>a});let a=[Object.freeze(JSON.parse('{"displayName":"1C (Query)","fileTypes":["sdbl","query"],"firstLineMatch":"(?i)Выбрать|Select(\\\\s+Разрешенные|\\\\s+Allowed)?(\\\\s+Различные|\\\\s+Distinct)?(\\\\s+Первые|\\\\s+Top)?.*","name":"sdbl","patterns":[{"match":"^(\\\\s*//.*)$","name":"comment.line.double-slash.sdbl"},{"begin":"//","end":"$","name":"comment.line.double-slash.sdbl"},{"begin":"\\"","end":"\\"(?!\\")","name":"string.quoted.double.sdbl","patterns":[{"match":"\\"\\"","name":"constant.character.escape.sdbl"},{"match":"^(\\\\s*//.*)$","name":"comment.line.double-slash.sdbl"}]},{"match":"(?i)(?<=[^.а-яё\\\\w]|^)(Неопределено|Undefined|Истина|True|Ложь|False|NULL)(?=[^.а-яё\\\\w]|$)","name":"constant.language.sdbl"},{"match":"(?<=[^.а-яё\\\\w]|^)(\\\\d+\\\\.?\\\\d*)(?=[^.а-яё\\\\w]|$)","name":"constant.numeric.sdbl"},{"match":"(?i)(?<=[^.а-яё\\\\w]|^)(Выбор|Case|Когда|When|Тогда|Then|Иначе|Else|Конец|End)(?=[^.а-яё\\\\w]|$)","name":"keyword.control.conditional.sdbl"},{"match":"(?i)(?<!КАК\\\\s|AS\\\\s)(?<=[^.а-яё\\\\w]|^)(НЕ|NOT|И|AND|ИЛИ|OR|В\\\\s+ИЕРАРХИИ|IN\\\\s+HIERARCHY|В|In|Между|Between|Есть(\\\\s+НЕ)?\\\\s+NULL|Is(\\\\s+NOT)?\\\\s+NULL|Ссылка|Refs|Подобно|Like)(?=[^.а-яё\\\\w]|$)","name":"keyword.operator.logical.sdbl"},{"match":"<=|>=|[<=>]","name":"keyword.operator.comparison.sdbl"},{"match":"([-%*+/])","name":"keyword.operator.arithmetic.sdbl"},{"match":"([,;])","name":"keyword.operator.sdbl"},{"match":"(?i)(?<=[^.а-яё\\\\w]|^)(Выбрать|Select|Разрешенные|Allowed|Различные|Distinct|Первые|Top|Как|As|ПустаяТаблица|EmptyTable|Поместить|Into|Уничтожить|Drop|Из|From|((Левое|Left|Правое|Right|Полное|Full)\\\\s+(Внешнее\\\\s+|Outer\\\\s+)?Соединение|Join)|((Внутреннее|Inner)\\\\s+Соединение|Join)|Где|Where|(Сгруппировать\\\\s+По(\\\\s+Группирующим\\\\s+Наборам)?)|(Group\\\\s+By(\\\\s+Grouping\\\\s+Set)?)|Имеющие|Having|Объединить(\\\\s+Все)?|Union(\\\\s+All)?|(Упорядочить\\\\s+По)|(Order\\\\s+By)|Автоупорядочивание|Autoorder|Итоги|Totals|По(\\\\s+Общие)?|By(\\\\s+Overall)?|(Только\\\\s+)?Иерархия|(Only\\\\s+)?Hierarchy|Периодами|Periods|Индексировать|Index|Выразить|Cast|Возр|Asc|Убыв|Desc|Для\\\\s+Изменения|(For\\\\s+Update(\\\\s+Of)?)|Спецсимвол|Escape|СгруппированоПо|GroupedBy)(?=[^.а-яё\\\\w]|$)","name":"keyword.control.sdbl"},{"match":"(?i)(?<=[^.а-яё\\\\w]|^)(Значение|Value|ДатаВремя|DateTime|Тип|Type)(?=\\\\()","name":"support.function.sdbl"},{"match":"(?i)(?<=[^.а-яё\\\\w]|^)(Подстрока|Substring|НРег|Lower|ВРег|Upper|Лев|Left|Прав|Right|ДлинаСтроки|StringLength|СтрНайти|StrFind|СтрЗаменить|StrReplace|СокрЛП|TrimAll|СокрЛ|TrimL|СокрП|TrimR)(?=\\\\()","name":"support.function.sdbl"},{"match":"(?i)(?<=[^.а-яё\\\\w]|^)(Год|Year|Квартал|Quarter|Месяц|Month|ДеньГода|DayOfYear|День|Day|Неделя|Week|ДеньНедели|Weekday|Час|Hour|Минута|Minute|Секунда|Second|НачалоПериода|BeginOfPeriod|КонецПериода|EndOfPeriod|ДобавитьКДате|DateAdd|РазностьДат|DateDiff|Полугодие|HalfYear|Декада|TenDays)(?=\\\\()","name":"support.function.sdbl"},{"match":"(?i)(?<=[^.а-яё\\\\w]|^)(ACOS|COS|ASIN|SIN|ATAN|TAN|EXP|POW|LOG|LOG10|Цел|Int|Окр|Round|SQRT)(?=\\\\()","name":"support.function.sdbl"},{"match":"(?i)(?<=[^.а-яё\\\\w]|^)(Сумма|Sum|Среднее|Avg|Минимум|Min|Максимум|Max|Количество|Count)(?=\\\\()","name":"support.function.sdbl"},{"match":"(?i)(?<=[^.а-яё\\\\w]|^)(ЕстьNULL|IsNULL|Представление|Presentation|ПредставлениеСсылки|RefPresentation|ТипЗначения|ValueType|АвтономерЗаписи|RecordAutoNumber|РазмерХранимыхДанных|StoredDataSize|УникальныйИдентификатор|UUID)(?=\\\\()","name":"support.function.sdbl"},{"match":"(?i)(?<=[^.а-яё\\\\w])(Число|Number|Строка|String|Дата|Date|Булево|Boolean)(?=[^.а-яё\\\\w]|$)","name":"support.type.sdbl"},{"match":"(&[а-яё\\\\w]+)","name":"variable.parameter.sdbl"}],"scopeName":"source.sdbl","aliases":["1c-query"]}'))]}}]);