"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7379],{2508:(e,t,r)=>{r.d(t,{A:()=>s});var a=r(99090),o=r(90697),n=r(69947);(0,a.GQ)(n.M.connections(),async()=>await s.getConnections(),{staleTime:6e5,gcTime:9e5,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchInterval:!1});let s={async createConnectionToken(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=await o.Hv.post("/pipedream/connection-token",e,{errorContext:{operation:"create connection token",resource:"Pipedream connection"}});if(!t.success){var r;throw Error((null==(r=t.error)?void 0:r.message)||"Failed to create connection token")}return t.data},async getConnections(){let e=await o.Hv.get("/pipedream/connections",{errorContext:{operation:"load connections",resource:"Pipedream connections"}});if(!e.success){var t;throw Error((null==(t=e.error)?void 0:t.message)||"Failed to get connections")}return e.data},async getApps(e,t){let r=new URLSearchParams;e&&r.append("after",e),t&&r.append("q",t);let a=await o.Hv.get("/pipedream/apps".concat(r.toString()?"?".concat(r.toString()):""),{errorContext:{operation:"load apps",resource:"Pipedream apps"}});if(!a.success){var n;throw Error((null==(n=a.error)?void 0:n.message)||"Failed to get apps")}let s=a.data;if(!s.success&&s.error)throw Error(s.error);return s},async getAvailableTools(){let e=await o.Hv.get("/pipedream/mcp/available-tools",{errorContext:{operation:"load available tools",resource:"Pipedream tools"}});if(!e.success){var t;throw Error((null==(t=e.error)?void 0:t.message)||"Failed to get available tools")}return e.data},async healthCheck(){let e=await o.Hv.get("/pipedream/health",{errorContext:{operation:"health check",resource:"Pipedream service"}});if(!e.success){var t;throw Error((null==(t=e.error)?void 0:t.message)||"Health check failed")}return e.data},async discoverMCPServers(e,t){var r,a;let n=await o.Hv.post("/pipedream/mcp/discover-profile",{external_user_id:e,app_slug:t},{errorContext:{operation:"discover MCP servers",resource:"Pipedream MCP"}});if(!n.success)throw Error((null==(a=n.error)?void 0:a.message)||"Failed to discover MCP servers");return(null==(r=n.data)?void 0:r.mcp_servers)||[]},async createProfile(e){let t=await o.Hv.post("/pipedream/profiles",e,{errorContext:{operation:"create profile",resource:"Pipedream credential profile"}});if(!t.success){var r;throw Error((null==(r=t.error)?void 0:r.message)||"Failed to create profile")}return t.data},async getProfiles(e){let t=new URLSearchParams;(null==e?void 0:e.app_slug)&&t.append("app_slug",e.app_slug),(null==e?void 0:e.is_active)!==void 0&&t.append("is_active",e.is_active.toString());let r=await o.Hv.get("/pipedream/profiles".concat(t.toString()?"?".concat(t.toString()):""),{errorContext:{operation:"get profiles",resource:"Pipedream credential profiles"}});if(!r.success){var a;throw Error((null==(a=r.error)?void 0:a.message)||"Failed to get profiles")}return r.data},async getProfile(e){let t=await o.Hv.get("/pipedream/profiles/".concat(e),{errorContext:{operation:"get profile",resource:"Pipedream credential profile"}});if(!t.success){var r;throw Error((null==(r=t.error)?void 0:r.message)||"Failed to get profile")}return t.data},async updateProfile(e,t){let r=await o.Hv.put("/pipedream/profiles/".concat(e),t,{errorContext:{operation:"update profile",resource:"Pipedream credential profile"}});if(!r.success){var a;throw Error((null==(a=r.error)?void 0:a.message)||"Failed to update profile")}return r.data},async deleteProfile(e){let t=await o.Hv.delete("/pipedream/profiles/".concat(e),{errorContext:{operation:"delete profile",resource:"Pipedream credential profile"}});if(!t.success){var r;throw Error((null==(r=t.error)?void 0:r.message)||"Failed to delete profile")}},async connectProfile(e,t){let r=t?"?app=".concat(encodeURIComponent(t)):"",a=await o.Hv.post("/pipedream/profiles/".concat(e,"/connect").concat(r),{},{errorContext:{operation:"connect profile",resource:"Pipedream credential profile"}});if(!a.success){var n;throw Error((null==(n=a.error)?void 0:n.message)||"Failed to connect profile")}return a.data},async getProfileConnections(e){let t=await o.Hv.get("/pipedream/profiles/".concat(e,"/connections"),{errorContext:{operation:"get profile connections",resource:"Pipedream profile connections"}});if(!t.success){var r;throw Error((null==(r=t.error)?void 0:r.message)||"Failed to get profile connections")}return t.data}}},8168:(e,t,r)=>{r.d(t,{K2:()=>s,eP:()=>i});var a=r(99090),o=r(80861),n=r(97270);r(25731);let s=e=>(0,a.GQ)(o.X.details(e),()=>(0,n.fG)(e),{enabled:!!e,retry:1})(),i=()=>(0,a.Lx)(e=>{let{threadId:t,data:r}=e;return(0,n.e4)(t,r)})()},16183:(e,t,r)=>{r.d(t,{Er:()=>c,_1:()=>i,s9:()=>s});var a=r(99090);let o=["subscription"],n=["usage"],s=(0,a.DY)({all:o,details:()=>[...o,"details"]}),i=(0,a.DY)({all:["models"],available:["models","available"]}),c=(0,a.DY)({all:n,logs:(e,t)=>[...n,"logs",{page:e,itemsPerPage:t}]})},16879:(e,t,r)=>{r.d(t,{p:()=>s});var a=r(99090),o=r(80861),n=r(25731);let s=function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];return(0,a.GQ)(o.X.billingStatus,()=>(0,n.Et)(),{enabled:e,retry:1,staleTime:3e5,gcTime:6e5,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchOnReconnect:!1,refetchInterval:e=>!!e.state.data&&!e.state.data.can_run&&6e4})()}},25490:(e,t,r)=>{r.d(t,{X:()=>i,g:()=>s});var a=r(99090);let o=["projects"],n=["threads"],s=(0,a.DY)({all:o,lists:()=>[...o,"list"],details:e=>[...o,"detail",e],public:()=>[...o,"public"]}),i=(0,a.DY)({all:n,lists:()=>[...n,"list"],byProject:e=>[...n,"by-project",e]})},26104:(e,t,r)=>{r.d(t,{Dy:()=>c});var a=r(28755),o=r(99090),n=r(90697),s=r(2508),i=r(69947);let c=(e,t)=>(0,a.I)({queryKey:["pipedream","apps",e,t],queryFn:async()=>await s.A.getApps(e,t),staleTime:3e5,retry:2});(0,o.GQ)(i.M.availableTools(),async function(){var e;let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],r=new URLSearchParams;t&&r.append("force_refresh","true");let a="/pipedream/mcp/available-tools".concat(r.toString()?"?".concat(r.toString()):""),o=await n.Hv.get(a,{errorContext:{operation:"load available tools",resource:"Pipedream tools"}});if(o.success&&o.data)if(o.data.success)return o.data;else throw Error(o.data.error||"Failed to get available tools");throw Error((null==(e=o.error)?void 0:e.message)||"Failed to get available tools")},{staleTime:3e5,gcTime:6e5,refetchOnWindowFocus:!1,refetchOnMount:!0,retry:(e,t)=>{if(e<2){var r;let e=(null==t||null==(r=t.message)?void 0:r.toLowerCase())||"";return!e.includes("unauthorized")&&!e.includes("forbidden")}return!1},retryDelay:e=>Math.min(1e3*2**e,3e4)})},27379:(e,t,r)=>{r.d(t,{ux:()=>h,Rs:()=>d.Rs,K2:()=>c.K2,sS:()=>s,eP:()=>c.eP}),r(49912);var a=r(99090),o=r(25731),n=r(56671);(0,a.Lx)(e=>(0,o.gA)(e,e.accountId),{onSuccess:()=>{n.oR.success("Project created successfully")},errorContext:{operation:"create project",resource:"project"}});let s=(0,a.Lx)(e=>{let{projectId:t,data:r}=e;return(0,o.vr)(t,r)},{onSuccess:()=>{},errorContext:{operation:"update project",resource:"project"}});(0,a.Lx)(e=>{let{projectId:t}=e;return(0,o.xx)(t)},{onSuccess:()=>{n.oR.success("Project deleted successfully")},errorContext:{operation:"delete project",resource:"project"}});var i=r(25490);(0,a.GQ)(i.g.public(),o.Kc,{staleTime:3e5,refetchOnWindowFocus:!1});var c=r(8168),l=r(80861);(0,a.GQ)(l.X.all,()=>(0,o.A3)(),{staleTime:12e4,refetchOnWindowFocus:!1}),r(79232),r(58883),r(46768),r(16879),(0,a.Lx)(e=>{let{projectId:t}=e;return(0,o.Nw)(t)},{onSuccess:()=>{n.oR.success("Thread created successfully")},errorContext:{operation:"create thread",resource:"thread"}}),(0,a.Lx)(e=>{let{threadId:t,content:r}=e;return(0,o.GB)(t,r)},{errorContext:{operation:"add message",resource:"message"}}),r(34986),r(88362),(0,a.Lx)(e=>{let{sandboxId:t,filePath:r,content:a}=e;return(0,o.gS)(t,r,a)},{onSuccess:()=>{n.oR.success("File created successfully")},errorContext:{operation:"create file",resource:"sandbox file"}}),(0,a.Lx)(e=>{let{sandboxId:t,filePath:r,content:a}=e;return(0,o.cF)(t,r,a)},{onSuccess:()=>{n.oR.success("File created successfully")},errorContext:{operation:"create file",resource:"sandbox file"}});var d=r(48988);r(95557),r(53153);let u=["sandbox"],g=["health"];(0,a.DY)({all:u,files:(e,t)=>[...u,e,"files",t],fileContent:(e,t)=>[...u,e,"content",t]});let p=(0,a.DY)({all:g,api:()=>[...g,"api"]}),h=(0,a.GQ)(p.api(),o.Bp,{staleTime:3e4,refetchInterval:6e4,refetchOnWindowFocus:!0,retry:3});r(69794),r(58332),r(90697),r(69947),r(2508),r(26104)},40407:(e,t,r)=>{r.d(t,{h:()=>a});let a=(0,r(65453).v)(e=>({type:null,isOpen:!1,onOpen:t=>e({type:t,isOpen:!0}),onClose:()=>e({type:null,isOpen:!1})}))},46768:(e,t,r)=>{r.d(t,{CV:()=>c,P3:()=>s,WZ:()=>i});var a=r(99090),o=r(80861),n=r(25731);let s=e=>(0,a.GQ)(o.X.agentRuns(e),()=>(0,n.m9)(e),{enabled:!!e,retry:1})(),i=()=>(0,a.Lx)(e=>{let{threadId:t,options:r}=e;return(0,n.Ih)(t,r)},{onError:e=>{if(!(e instanceof n.Ey))throw e}})(),c=()=>(0,a.Lx)(e=>(0,n.eD)(e))()},48988:(e,t,r)=>{r.d(t,{Rs:()=>s});var a=r(99090),o=r(25731),n=r(16183);let s=(0,a.GQ)(n.s9.details(),o.uV,{staleTime:3e5,refetchOnWindowFocus:!0});(0,a.Lx)(e=>(0,o.VK)(e),{onSuccess:e=>{(null==e?void 0:e.url)&&(window.location.href=e.url)}})},49912:(e,t,r)=>{r.d(t,{$u:()=>l,Ar:()=>d,BI:()=>c,YK:()=>i,vZ:()=>u});var a=r(99090),o=r(25731),n=r(25490),s=r(97270);let i=(0,a.GQ)(n.g.lists(),async()=>await (0,o.JQ)(),{staleTime:3e5,refetchOnWindowFocus:!1}),c=(0,a.GQ)(n.X.lists(),async()=>await (0,o.A3)(),{staleTime:3e5,refetchOnWindowFocus:!1}),l=(0,a.Lx)(async e=>{let{threadId:t,sandboxId:r}=e;return await (0,s.KX)(t,r)},{onSuccess:()=>{}}),d=(0,a.Lx)(async e=>{let{threadIds:t,threadSandboxMap:r,onProgress:a}=e,o=0,n=await Promise.all(t.map(async e=>{try{let n=null==r?void 0:r[e];return await (0,s.KX)(e,n),o++,null==a||a(o,t.length),{success:!0,threadId:e}}catch(t){return{success:!1,threadId:e,error:t}}}));return{successful:n.filter(e=>e.success).map(e=>e.threadId),failed:n.filter(e=>!e.success).map(e=>e.threadId)}},{onSuccess:()=>{}}),u=(e,t)=>{let r=new Map;t.forEach(e=>{r.set(e.id,e)});let a=[];for(let t of e){var o,n;let e=t.project_id;if(!e)continue;let s=r.get(e);if(!s){console.log("❌ Thread ".concat(t.thread_id," has project_id=").concat(e," but no matching project found"));continue}let i=s.name||"Unnamed Project";(null==(o=t.metadata)?void 0:o.is_workflow_execution)&&(null==(n=t.metadata)?void 0:n.workflow_run_name)&&(i=t.metadata.workflow_run_name),a.push({threadId:t.thread_id,projectId:e,projectName:i,url:"/projects/".concat(e,"/thread/").concat(t.thread_id),updatedAt:t.updated_at||s.updated_at||new Date().toISOString()})}return g(a)},g=e=>[...e].sort((e,t)=>new Date(t.updatedAt).getTime()-new Date(e.updatedAt).getTime())},53153:(e,t,r)=>{r.d(t,{T:()=>u,u:()=>g});var a=r(25731),o=r(99090),n=r(33356);let s=["dashboard","agents"],i=(0,o.DY)({all:["dashboard"],agents:s,initiateAgent:()=>[...s,"initiate"]});var c=r(26715),l=r(40407),d=r(25490);let u=(0,o.Lx)(a.fu,{errorContext:{operation:"initiate agent",resource:"AI assistant"},onSuccess:e=>{(0,n.uC)("Agent initiated successfully","Your AI assistant is ready to help")},onError:e=>{e instanceof Error&&e.message.toLowerCase().includes("payment required")||(0,n.hS)(e,{operation:"initiate agent",resource:"AI assistant"})}}),g=()=>{let e=(0,c.jE)(),{onOpen:t}=(0,l.h)();return u({onSuccess:t=>{e.invalidateQueries({queryKey:d.g.all}),e.invalidateQueries({queryKey:d.X.all}),e.invalidateQueries({queryKey:i.agents})},onError:e=>{if(console.log("Mutation error:",e),e instanceof Error&&e.message.toLowerCase().includes("payment required")){console.log("Opening payment required modal"),t("paymentRequiredDialog");return}}})}},58332:(e,t,r)=>{r.d(t,{aC:()=>g,A$:()=>p,Dz:()=>f,ct:()=>w,vg:()=>h});var a=r(52643),o=r(28755),n=r(26715),s=r(5041);let i="http://localhost:8000/api",c=async e=>{let t=(0,a.U)(),{data:{session:r}}=await t.auth.getSession();if(!r)throw Error("You must be logged in to create a trigger");let o=await fetch("".concat(i,"/triggers/agents/").concat(e,"/triggers"),{headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r.access_token)}});if(!o.ok)throw Error("Failed to fetch agent triggers");return o.json()},l=async e=>{let t=(0,a.U)(),{data:{session:r}}=await t.auth.getSession();if(!r)throw Error("You must be logged in to create a trigger");let o=await fetch("".concat(i,"/triggers/agents/").concat(e.agentId,"/triggers"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r.access_token)},body:JSON.stringify({provider_id:e.provider_id,name:e.name,description:e.description,config:e.config})});if(!o.ok)throw Error((await o.json()).detail||"Failed to create trigger");return o.json()},d=async e=>{let t=(0,a.U)(),{data:{session:r}}=await t.auth.getSession();if(!r)throw Error("You must be logged in to create a trigger");let o=await fetch("".concat(i,"/triggers/").concat(e.triggerId),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r.access_token)},body:JSON.stringify({name:e.name,description:e.description,config:e.config,is_active:e.is_active})});if(!o.ok)throw Error((await o.json()).detail||"Failed to update trigger");return o.json()},u=async e=>{let t=(0,a.U)(),{data:{session:r}}=await t.auth.getSession();if(!r)throw Error("You must be logged in to create a trigger");let o=await fetch("".concat(i,"/triggers/").concat(e),{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r.access_token)}});if(!o.ok)throw Error((await o.json()).detail||"Failed to delete trigger")},g=e=>(0,o.I)({queryKey:["agent-triggers",e],queryFn:()=>c(e),enabled:!!e,staleTime:12e4}),p=()=>{let e=(0,n.jE)();return(0,s.n)({mutationFn:l,onSuccess:t=>{e.setQueryData(["agent-triggers",t.agent_id],e=>e?[...e,t]:[t])}})},h=()=>{let e=(0,n.jE)();return(0,s.n)({mutationFn:d,onSuccess:t=>{e.setQueryData(["agent-triggers",t.agent_id],e=>e?e.map(e=>e.trigger_id===t.trigger_id?t:e):[t])}})},f=()=>{let e=(0,n.jE)();return(0,s.n)({mutationFn:u,onSuccess:(t,r)=>{e.invalidateQueries({queryKey:["agent-triggers"]})}})},w=()=>{let e=(0,n.jE)();return(0,s.n)({mutationFn:async e=>d({triggerId:e.triggerId,is_active:e.isActive}),onSuccess:t=>{e.setQueryData(["agent-triggers",t.agent_id],e=>e?e.map(e=>e.trigger_id===t.trigger_id?t:e):[t])}})}},58883:(e,t,r)=>{r.d(t,{C:()=>s,U:()=>i});var a=r(99090),o=r(80861),n=r(25731);let s=e=>(0,a.GQ)(o.X.messages(e),()=>(0,n.VL)(e),{enabled:!!e,retry:1})(),i=()=>(0,a.Lx)(e=>{let{threadId:t,message:r}=e;return(0,n.GB)(t,r)})()},69794:(e,t,r)=>{r.d(t,{Cv:()=>f,ku:()=>m,rW:()=>y,CR:()=>w,v0:()=>g,QJ:()=>h,Xe:()=>u,kq:()=>p,Dv:()=>v});var a=r(28755),o=r(26715),n=r(5041),s=r(56671),i=r(52643);let c={all:["knowledge-base"],threads:()=>[...c.all,"threads"],thread:e=>[...c.threads(),e],agents:()=>[...c.all,"agents"],agent:e=>[...c.agents(),e],entry:e=>[...c.all,"entry",e],context:e=>[...c.all,"context",e],agentContext:e=>[...c.all,"agent-context",e],combinedContext:(e,t)=>[...c.all,"combined-context",e,t],processingJobs:e=>[...c.all,"processing-jobs",e]},l="http://localhost:8000/api",d=()=>({getHeaders:async()=>{let e=(0,i.U)(),{data:{session:t}}=await e.auth.getSession();if(!(null==t?void 0:t.access_token))throw Error("No access token available");return{Authorization:"Bearer ".concat(t.access_token),"Content-Type":"application/json"}}});function u(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],{getHeaders:r}=d();return(0,a.I)({queryKey:c.thread(e),queryFn:async()=>{let a=await r(),o=new URL("".concat(l,"/knowledge-base/threads/").concat(e));o.searchParams.set("include_inactive",t.toString());let n=await fetch(o.toString(),{headers:a});if(!n.ok)throw Error(await n.text()||"Failed to fetch knowledge base entries");return await n.json()},enabled:!!e})}function g(){let e=(0,o.jE)(),{getHeaders:t}=d();return(0,n.n)({mutationFn:async e=>{let{threadId:r,data:a}=e,o=await t(),n=await fetch("".concat(l,"/knowledge-base/threads/").concat(r),{method:"POST",headers:{...o,"Content-Type":"application/json"},body:JSON.stringify(a)});if(!n.ok)throw Error(await n.text()||"Failed to create knowledge base entry");return await n.json()},onSuccess:(t,r)=>{let{threadId:a}=r;e.invalidateQueries({queryKey:c.thread(a)}),e.invalidateQueries({queryKey:c.context(a)}),s.oR.success("Knowledge base entry created successfully")},onError:e=>{s.oR.error("Failed to create knowledge base entry: ".concat(e.message))}})}function p(){let e=(0,o.jE)(),{getHeaders:t}=d();return(0,n.n)({mutationFn:async e=>{let{entryId:r,data:a}=e,o=await t(),n=await fetch("".concat(l,"/knowledge-base/").concat(r),{method:"PUT",headers:{...o,"Content-Type":"application/json"},body:JSON.stringify(a)});if(!n.ok)throw Error(await n.text()||"Failed to update knowledge base entry");return await n.json()},onSuccess:()=>{e.invalidateQueries({queryKey:c.all}),s.oR.success("Knowledge base entry updated successfully")},onError:e=>{s.oR.error("Failed to update knowledge base entry: ".concat(e.message))}})}function h(){let e=(0,o.jE)(),{getHeaders:t}=d();return(0,n.n)({mutationFn:async e=>{let r=await t(),a=await fetch("".concat(l,"/knowledge-base/").concat(e),{method:"DELETE",headers:r});if(!a.ok)throw Error(await a.text()||"Failed to delete knowledge base entry");return await a.json()},onSuccess:()=>{e.invalidateQueries({queryKey:c.all}),s.oR.success("Knowledge base entry deleted successfully")},onError:e=>{s.oR.error("Failed to delete knowledge base entry: ".concat(e.message))}})}function f(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],{getHeaders:r}=d();return(0,a.I)({queryKey:c.agent(e),queryFn:async()=>{let a=await r(),o=new URL("".concat(l,"/knowledge-base/agents/").concat(e));o.searchParams.set("include_inactive",t.toString());let n=await fetch(o.toString(),{headers:a});if(!n.ok)throw Error(await n.text()||"Failed to fetch agent knowledge base entries");return await n.json()},enabled:!!e})}function w(){let e=(0,o.jE)(),{getHeaders:t}=d();return(0,n.n)({mutationFn:async e=>{let{agentId:r,data:a}=e,o=await t(),n=await fetch("".concat(l,"/knowledge-base/agents/").concat(r),{method:"POST",headers:{...o,"Content-Type":"application/json"},body:JSON.stringify(a)});if(!n.ok)throw Error(await n.text()||"Failed to create agent knowledge base entry");return await n.json()},onSuccess:(t,r)=>{let{agentId:a}=r;e.invalidateQueries({queryKey:c.agent(a)}),e.invalidateQueries({queryKey:c.agentContext(a)}),s.oR.success("Agent knowledge entry created successfully")},onError:e=>{s.oR.error("Failed to create agent knowledge entry: ".concat(e.message))}})}function v(){let e=(0,o.jE)(),{getHeaders:t}=d();return(0,n.n)({mutationFn:async e=>{let{agentId:t,file:r}=e,a=(0,i.U)(),{data:{session:o}}=await a.auth.getSession();if(!(null==o?void 0:o.access_token))throw Error("No access token available");let n=new FormData;n.append("file",r);let s=await fetch("".concat(l,"/knowledge-base/agents/").concat(t,"/upload-file"),{method:"POST",headers:{Authorization:"Bearer ".concat(o.access_token)},body:n});if(!s.ok)throw Error(await s.text()||"Failed to upload file");return await s.json()},onSuccess:(t,r)=>{let{agentId:a}=r;e.invalidateQueries({queryKey:c.agent(a)}),e.invalidateQueries({queryKey:c.processingJobs(a)}),s.oR.success("File uploaded successfully. Processing in background.")},onError:e=>{s.oR.error("Failed to upload file: ".concat(e.message))}})}function y(){let e=(0,o.jE)(),{getHeaders:t}=d();return(0,n.n)({mutationFn:async e=>{let{agentId:r,git_url:a,branch:o="main"}=e,n=await t(),s=await fetch("".concat(l,"/knowledge-base/agents/").concat(r,"/clone-git-repo"),{method:"POST",headers:n,body:JSON.stringify({git_url:a,branch:o})});if(!s.ok)throw Error(await s.text()||"Failed to clone repository");return await s.json()},onSuccess:(t,r)=>{let{agentId:a}=r;e.invalidateQueries({queryKey:c.agent(a)}),e.invalidateQueries({queryKey:c.processingJobs(a)}),s.oR.success("Repository cloning started. Processing in background.")},onError:e=>{s.oR.error("Failed to clone repository: ".concat(e.message))}})}function m(e){let{getHeaders:t}=d();return(0,a.I)({queryKey:c.processingJobs(e),queryFn:async()=>{let r=await t(),a=await fetch("".concat(l,"/knowledge-base/agents/").concat(e,"/processing-jobs"),{headers:r});if(!a.ok)throw Error(await a.text()||"Failed to fetch processing jobs");return await a.json()},enabled:!!e,refetchInterval:5e3})}},69947:(e,t,r)=>{r.d(t,{M:()=>a});let a={all:["pipedream"],connections:()=>[...a.all,"connections"],connectionToken:e=>[...a.all,"connection-token",e||"default"],health:()=>[...a.all,"health"],config:()=>[...a.all,"config"],workflows:()=>[...a.all,"workflows"],workflowRuns:e=>[...a.all,"workflow-runs",e],apps:(e,t,r)=>[...a.all,"apps",e,t||"",r||""],appsSearch:(e,t,r)=>[...a.all,"apps","search",e,t,r||""],availableTools:()=>[...a.all,"available-tools"],mcpDiscovery:e=>[...a.all,"mcp-discovery",null==e?void 0:e.app_slug,null==e?void 0:e.oauth_app_id,null==e?void 0:e.custom],profiles:{all:()=>[...a.all,"profiles"],list:e=>{var t;return[...a.profiles.all(),"list",(null==e?void 0:e.app_slug)||"",null!=(t=null==e?void 0:e.is_active)?t:""]},detail:e=>[...a.profiles.all(),"detail",e],connections:e=>[...a.profiles.all(),"connections",e]}}},79232:(e,t,r)=>{r.d(t,{BR:()=>s});var a=r(99090),o=r(80861),n=r(97270);let s=e=>(0,a.GQ)(o.X.project(e||""),()=>e?(0,n.U1)(e):Promise.reject("No project ID"),{enabled:!!e,retry:1})()},80861:(e,t,r)=>{r.d(t,{X:()=>a});let a=(0,r(99090).DY)({all:["threads"],details:e=>["thread",e],messages:e=>["thread",e,"messages"],project:e=>["project",e],publicProjects:()=>["public-projects"],agentRuns:e=>["thread",e,"agent-runs"],billingStatus:["billing","status"],byProject:e=>["project",e,"threads"]})},90697:(e,t,r)=>{r.d(t,{Hv:()=>c,mI:()=>i});var a=r(52643),o=r(33356);let n="http://localhost:8000/api",s={async request(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{showErrors:r=!0,errorContext:n,timeout:s=3e4,...i}=t;try{let t,c=new AbortController,l=setTimeout(()=>c.abort(),s),d=(0,a.U)(),{data:{session:u}}=await d.auth.getSession(),g={"Content-Type":"application/json",...i.headers};(null==u?void 0:u.access_token)&&(g.Authorization="Bearer ".concat(u.access_token));let p=await fetch(e,{...i,headers:g,signal:c.signal});if(clearTimeout(l),!p.ok){let e=Error("HTTP ".concat(p.status,": ").concat(p.statusText));e.status=p.status,e.response=p;try{let t=await p.json();e.details=t,t.message&&(e.message=t.message)}catch(e){}return r&&(0,o.hS)(e,n),{error:e,success:!1}}let h=p.headers.get("content-type");return{data:(null==h?void 0:h.includes("application/json"))?await p.json():(null==h?void 0:h.includes("text/"))?await p.text():await p.blob(),success:!0}}catch(t){let e=t instanceof Error?t:Error(String(t));return"AbortError"===t.name&&(e.message="Request timeout",e.code="TIMEOUT"),r&&(0,o.nQ)(e,n),{error:e,success:!1}}},get:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return s.request(e,{...t,method:"GET"})},post:async function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return s.request(e,{...r,method:"POST",body:t?JSON.stringify(t):void 0})},put:async function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return s.request(e,{...r,method:"PUT",body:t?JSON.stringify(t):void 0})},patch:async function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return s.request(e,{...r,method:"PATCH",body:t?JSON.stringify(t):void 0})},delete:async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return s.request(e,{...t,method:"DELETE"})},upload:async function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{headers:a,...o}=r,n={...a};return delete n["Content-Type"],s.request(e,{...o,method:"POST",body:t,headers:n})}},i={async execute(e,t){try{let{data:r,error:a}=await e();if(a){let e=Error(a.message||"Database error");return e.code=a.code,e.details=a,(0,o.hS)(e,t),{error:e,success:!1}}return{data:r,success:!0}}catch(r){let e=r instanceof Error?r:Error(String(r));return(0,o.hS)(e,t),{error:e,success:!1}}}},c={get:(e,t)=>s.get("".concat(n).concat(e),t),post:(e,t,r)=>s.post("".concat(n).concat(e),t,r),put:(e,t,r)=>s.put("".concat(n).concat(e),t,r),patch:(e,t,r)=>s.patch("".concat(n).concat(e),t,r),delete:(e,t)=>s.delete("".concat(n).concat(e),t),upload:(e,t,r)=>s.upload("".concat(n).concat(e),t,r)}},95557:(e,t,r)=>{r.d(t,{$x:()=>c,_o:()=>l});var a=r(99090),o=r(25731);r(52643);var n=r(90697);r(33356);let s={getSubscription:async()=>(await n.Hv.get("/billing/subscription",{errorContext:{operation:"load subscription",resource:"billing information"}})).data||null,checkStatus:async()=>(await n.Hv.get("/billing/status",{errorContext:{operation:"check billing status",resource:"account status"}})).data||null,createCheckoutSession:async e=>(await n.Hv.post("/billing/create-checkout-session",e,{errorContext:{operation:"create checkout session",resource:"billing"}})).data||null,createPortalSession:async e=>(await n.Hv.post("/billing/create-portal-session",e,{errorContext:{operation:"create portal session",resource:"billing portal"}})).data||null,getAvailableModels:async()=>(await n.Hv.get("/billing/available-models",{errorContext:{operation:"load available models",resource:"AI models"}})).data||null,async getUsageLogs(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3;return(await n.Hv.get("/billing/usage-logs?page=".concat(e,"&items_per_page=").concat(t),{errorContext:{operation:"load usage logs",resource:"usage history"}})).data||null}};var i=r(16183);let c=(0,a.GQ)(i._1.available,o.cL,{staleTime:6e5,refetchOnWindowFocus:!1});(0,a.GQ)(["billing","status"],o.Et,{staleTime:12e4,refetchOnWindowFocus:!0}),(0,a.Lx)(e=>(0,o.fw)(e),{onSuccess:e=>{e.url&&(window.location.href=e.url)},errorContext:{operation:"create checkout session",resource:"billing"}});let l=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3;return(0,a.GQ)(i.Er.logs(e,t),()=>s.getUsageLogs(e,t),{staleTime:3e4,refetchOnMount:!0,refetchOnWindowFocus:!1})()}},97270:(e,t,r)=>{r.d(t,{KX:()=>c,U1:()=>l,e4:()=>s,fG:()=>n});var a=r(52643);let o="http://localhost:8000/api",n=async e=>{let t=(0,a.U)(),{data:r,error:o}=await t.from("threads").select("*").eq("thread_id",e).single();if(o)throw o;return r},s=async(e,t)=>{let r=(0,a.U)(),o={...t},{data:n,error:s}=await r.from("threads").update(o).eq("thread_id",e).select().single();if(s)throw console.error("Error updating thread:",s),Error("Error updating thread: ".concat(s.message));return n},i=async e=>{try{let t=(0,a.U)(),{data:{session:r}}=await t.auth.getSession(),n={"Content-Type":"application/json"};(null==r?void 0:r.access_token)&&(n.Authorization="Bearer ".concat(r.access_token)),(await fetch("".concat(o,"/sandboxes/").concat(e),{method:"DELETE",headers:n})).ok||console.warn("Failed to delete sandbox, continuing with thread deletion")}catch(e){console.warn("Error deleting sandbox, continuing with thread deletion:",e)}},c=async(e,t)=>{try{let o=(0,a.U)();if(t)await i(t);else{let{data:t,error:a}=await o.from("threads").select("project_id").eq("thread_id",e).single();if(a)throw console.error("Error fetching thread:",a),Error("Error fetching thread: ".concat(a.message));if(null==t?void 0:t.project_id){var r;let{data:e}=await o.from("projects").select("sandbox").eq("project_id",t.project_id).single();(null==e||null==(r=e.sandbox)?void 0:r.id)&&await i(e.sandbox.id)}}console.log("Deleting all agent runs for thread ".concat(e));let{error:n}=await o.from("agent_runs").delete().eq("thread_id",e);if(n)throw console.error("Error deleting agent runs:",n),Error("Error deleting agent runs: ".concat(n.message));console.log("Deleting all messages for thread ".concat(e));let{error:s}=await o.from("messages").delete().eq("thread_id",e);if(s)throw console.error("Error deleting messages:",s),Error("Error deleting messages: ".concat(s.message));console.log("Deleting thread ".concat(e));let{error:c}=await o.from("threads").delete().eq("thread_id",e);if(c)throw console.error("Error deleting thread:",c),Error("Error deleting thread: ".concat(c.message));console.log("Thread ".concat(e," successfully deleted with all related items"))}catch(e){throw console.error("Error deleting thread and related items:",e),e}},l=async e=>{let t=(0,a.U)();try{var r;let{data:a,error:n}=await t.from("projects").select("*").eq("project_id",e).single();if(n){if("PGRST116"===n.code)throw Error("Project not found or not accessible: ".concat(e));throw n}return console.log("Raw project data from database:",a),(null==(r=a.sandbox)?void 0:r.id)&&(async()=>{try{let{data:{session:r}}=await t.auth.getSession(),a={"Content-Type":"application/json"};(null==r?void 0:r.access_token)&&(a.Authorization="Bearer ".concat(r.access_token)),console.log("Ensuring sandbox is active for project ".concat(e,"..."));let n=await fetch("".concat(o,"/project/").concat(e,"/sandbox/ensure-active"),{method:"POST",headers:a});if(n.ok)console.log("Sandbox activation successful");else{let e=await n.text().catch(()=>"No error details available");console.warn("Failed to ensure sandbox is active: ".concat(n.status," ").concat(n.statusText),e)}}catch(e){console.warn("Failed to ensure sandbox is active:",e)}})(),{id:a.project_id,name:a.name||"",description:a.description||"",account_id:a.account_id,is_public:a.is_public||!1,created_at:a.created_at,sandbox:a.sandbox||{id:"",pass:"",vnc_preview:"",sandbox_url:""}}}catch(t){throw console.error("Error fetching project ".concat(e,":"),t),t}}},99090:(e,t,r)=>{r.d(t,{DY:()=>s,GQ:()=>i,Lx:()=>c});var a=r(28755),o=r(5041),n=r(33356);let s=e=>e;function i(e,t,r){return o=>(0,a.I)({queryKey:e,queryFn:t,...r,...o})}function c(e,t){return r=>{let{errorContext:a,...s}=t||{},{errorContext:i,...c}=r||{};return(0,o.n)({mutationFn:e,onError:(e,t,r)=>{var o,l;(null==c?void 0:c.onError)||(null==s?void 0:s.onError)||(0,n.hS)(e,i||a),null==s||null==(o=s.onError)||o.call(s,e,t,r),null==c||null==(l=c.onError)||l.call(c,e,t,r)},...s,...c})}}}}]);