"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5106],{381:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1243:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},2775:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("GitBranch",[["line",{x1:"6",x2:"6",y1:"3",y2:"15",key:"17qcm7"}],["circle",{cx:"18",cy:"6",r:"3",key:"1h7g24"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["path",{d:"M18 9a9 9 0 0 1-9 9",key:"n2h4wq"}]])},4229:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},5623:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},10081:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},20547:(e,t,r)=>{r.d(t,{UC:()=>W,ZL:()=>K,bL:()=>z,l9:()=>B});var n=r(12115),l=r(85185),o=r(6101),u=r(46081),a=r(19178),i=r(92293),s=r(25519),c=r(61285),d=r(35152),f=r(34378),v=r(28905),p=r(63655),m=r(99708),h=r(5845),g=r(38168),E=r(58890),b=r(95155),y="Popover",[w,C]=(0,u.A)(y,[d.Bk]),R=(0,d.Bk)(),[k,A]=w(y),x=e=>{let{__scopePopover:t,children:r,open:l,defaultOpen:o,onOpenChange:u,modal:a=!1}=e,i=R(t),s=n.useRef(null),[f,v]=n.useState(!1),[p,m]=(0,h.i)({prop:l,defaultProp:null!=o&&o,onChange:u,caller:y});return(0,b.jsx)(d.bL,{...i,children:(0,b.jsx)(k,{scope:t,contentId:(0,c.B)(),triggerRef:s,open:p,onOpenChange:m,onOpenToggle:n.useCallback(()=>m(e=>!e),[m]),hasCustomAnchor:f,onCustomAnchorAdd:n.useCallback(()=>v(!0),[]),onCustomAnchorRemove:n.useCallback(()=>v(!1),[]),modal:a,children:r})})};x.displayName=y;var O="PopoverAnchor";n.forwardRef((e,t)=>{let{__scopePopover:r,...l}=e,o=A(O,r),u=R(r),{onCustomAnchorAdd:a,onCustomAnchorRemove:i}=o;return n.useEffect(()=>(a(),()=>i()),[a,i]),(0,b.jsx)(d.Mz,{...u,...l,ref:t})}).displayName=O;var S="PopoverTrigger",P=n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,u=A(S,r),a=R(r),i=(0,o.s)(t,u.triggerRef),s=(0,b.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":u.open,"aria-controls":u.contentId,"data-state":$(u.open),...n,ref:i,onClick:(0,l.m)(e.onClick,u.onOpenToggle)});return u.hasCustomAnchor?s:(0,b.jsx)(d.Mz,{asChild:!0,...a,children:s})});P.displayName=S;var M="PopoverPortal",[D,N]=w(M,{forceMount:void 0}),L=e=>{let{__scopePopover:t,forceMount:r,children:n,container:l}=e,o=A(M,t);return(0,b.jsx)(D,{scope:t,forceMount:r,children:(0,b.jsx)(v.C,{present:r||o.open,children:(0,b.jsx)(f.Z,{asChild:!0,container:l,children:n})})})};L.displayName=M;var I="PopoverContent",F=n.forwardRef((e,t)=>{let r=N(I,e.__scopePopover),{forceMount:n=r.forceMount,...l}=e,o=A(I,e.__scopePopover);return(0,b.jsx)(v.C,{present:n||o.open,children:o.modal?(0,b.jsx)(_,{...l,ref:t}):(0,b.jsx)(j,{...l,ref:t})})});F.displayName=I;var T=(0,m.TL)("PopoverContent.RemoveScroll"),_=n.forwardRef((e,t)=>{let r=A(I,e.__scopePopover),u=n.useRef(null),a=(0,o.s)(t,u),i=n.useRef(!1);return n.useEffect(()=>{let e=u.current;if(e)return(0,g.Eq)(e)},[]),(0,b.jsx)(E.A,{as:T,allowPinchZoom:!0,children:(0,b.jsx)(q,{...e,ref:a,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,l.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),i.current||null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,l.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;i.current=2===t.button||r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,l.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),j=n.forwardRef((e,t)=>{let r=A(I,e.__scopePopover),l=n.useRef(!1),o=n.useRef(!1);return(0,b.jsx)(q,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,u;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(l.current||null==(u=r.triggerRef.current)||u.focus(),t.preventDefault()),l.current=!1,o.current=!1},onInteractOutside:t=>{var n,u;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(l.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let a=t.target;(null==(u=r.triggerRef.current)?void 0:u.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),q=n.forwardRef((e,t)=>{let{__scopePopover:r,trapFocus:n,onOpenAutoFocus:l,onCloseAutoFocus:o,disableOutsidePointerEvents:u,onEscapeKeyDown:c,onPointerDownOutside:f,onFocusOutside:v,onInteractOutside:p,...m}=e,h=A(I,r),g=R(r);return(0,i.Oh)(),(0,b.jsx)(s.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:l,onUnmountAutoFocus:o,children:(0,b.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:u,onInteractOutside:p,onEscapeKeyDown:c,onPointerDownOutside:f,onFocusOutside:v,onDismiss:()=>h.onOpenChange(!1),children:(0,b.jsx)(d.UC,{"data-state":$(h.open),role:"dialog",id:h.contentId,...g,...m,ref:t,style:{...m.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),U="PopoverClose";function $(e){return e?"open":"closed"}n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,o=A(U,r);return(0,b.jsx)(p.sG.button,{type:"button",...n,ref:t,onClick:(0,l.m)(e.onClick,()=>o.onOpenChange(!1))})}).displayName=U,n.forwardRef((e,t)=>{let{__scopePopover:r,...n}=e,l=R(r);return(0,b.jsx)(d.i3,{...l,...n,ref:t})}).displayName="PopoverArrow";var z=x,B=P,K=L,W=F},35169:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40968:(e,t,r)=>{r.d(t,{b:()=>a});var n=r(12115),l=r(63655),o=r(95155),u=n.forwardRef((e,t)=>(0,o.jsx)(l.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));u.displayName="Label";var a=u},47924:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},56542:(e,t,r)=>{let n;r.d(t,{uB:()=>e9});var l=/[\\\/_+.#"@\[\(\{&]/,o=/[\\\/_+.#"@\[\(\{&]/g,u=/[\s-]/,a=/[\s-]/g;function i(e){return e.toLowerCase().replace(a," ")}var s=r(79630),c=r(12115),d=r.t(c,2);function f(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(null==e||e(n),!1===r||!n.defaultPrevented)return null==t?void 0:t(n)}}function v(...e){return t=>e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}function p(...e){return(0,c.useCallback)(v(...e),e)}let m=(null==globalThis?void 0:globalThis.document)?c.useLayoutEffect:()=>{},h=d["useId".toString()]||(()=>void 0),g=0;function E(e){let[t,r]=c.useState(h());return m(()=>{e||r(e=>null!=e?e:String(g++))},[e]),e||(t?`radix-${t}`:"")}function b(e){let t=(0,c.useRef)(e);return(0,c.useEffect)(()=>{t.current=e}),(0,c.useMemo)(()=>(...e)=>{var r;return null==(r=t.current)?void 0:r.call(t,...e)},[])}var y=r(47650);let w=(0,c.forwardRef)((e,t)=>{let{children:r,...n}=e,l=c.Children.toArray(r),o=l.find(k);if(o){let e=o.props.children,r=l.map(t=>t!==o?t:c.Children.count(e)>1?c.Children.only(null):(0,c.isValidElement)(e)?e.props.children:null);return(0,c.createElement)(C,(0,s.A)({},n,{ref:t}),(0,c.isValidElement)(e)?(0,c.cloneElement)(e,void 0,r):null)}return(0,c.createElement)(C,(0,s.A)({},n,{ref:t}),r)});w.displayName="Slot";let C=(0,c.forwardRef)((e,t)=>{let{children:r,...n}=e;return(0,c.isValidElement)(r)?(0,c.cloneElement)(r,{...function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?r[n]=(...e)=>{null==o||o(...e),null==l||l(...e)}:"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props),ref:v(t,r.ref)}):c.Children.count(r)>1?c.Children.only(null):null});C.displayName="SlotClone";let R=({children:e})=>(0,c.createElement)(c.Fragment,null,e);function k(e){return(0,c.isValidElement)(e)&&e.type===R}let A=["a","button","div","h2","h3","img","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=(0,c.forwardRef)((e,r)=>{let{asChild:n,...l}=e,o=n?w:t;return(0,c.useEffect)(()=>{window[Symbol.for("radix-ui")]=!0},[]),(0,c.createElement)(o,(0,s.A)({},l,{ref:r}))});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),x="dismissableLayer.update",O=(0,c.createContext)({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),S=(0,c.forwardRef)((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:l,onPointerDownOutside:o,onFocusOutside:u,onInteractOutside:a,onDismiss:i,...d}=e,v=(0,c.useContext)(O),[m,h]=(0,c.useState)(null),[,g]=(0,c.useState)({}),E=p(t,e=>h(e)),y=Array.from(v.layers),[w]=[...v.layersWithOutsidePointerEventsDisabled].slice(-1),C=y.indexOf(w),R=m?y.indexOf(m):-1,k=v.layersWithOutsidePointerEventsDisabled.size>0,S=R>=C,D=function(e){let t=b(e),r=(0,c.useRef)(!1),n=(0,c.useRef)(()=>{});return(0,c.useEffect)(()=>{let e=e=>{if(e.target&&!r.current){let r={originalEvent:e};function l(){M("dismissableLayer.pointerDownOutside",t,r,{discrete:!0})}"touch"===e.pointerType?(document.removeEventListener("click",n.current),n.current=l,document.addEventListener("click",n.current,{once:!0})):l()}r.current=!1},l=window.setTimeout(()=>{document.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(l),document.removeEventListener("pointerdown",e),document.removeEventListener("click",n.current)}},[t]),{onPointerDownCapture:()=>r.current=!0}}(e=>{let t=e.target,r=[...v.branches].some(e=>e.contains(t));S&&!r&&(null==o||o(e),null==a||a(e),e.defaultPrevented||null==i||i())}),N=function(e){let t=b(e),r=(0,c.useRef)(!1);return(0,c.useEffect)(()=>{let e=e=>{e.target&&!r.current&&M("dismissableLayer.focusOutside",t,{originalEvent:e},{discrete:!1})};return document.addEventListener("focusin",e),()=>document.removeEventListener("focusin",e)},[t]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}(e=>{let t=e.target;![...v.branches].some(e=>e.contains(t))&&(null==u||u(e),null==a||a(e),e.defaultPrevented||null==i||i())});return!function(e){let t=b(e);(0,c.useEffect)(()=>{let e=e=>{"Escape"===e.key&&t(e)};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[t])}(e=>{R===v.layers.size-1&&(null==l||l(e),!e.defaultPrevented&&i&&(e.preventDefault(),i()))}),(0,c.useEffect)(()=>{if(m)return r&&(0===v.layersWithOutsidePointerEventsDisabled.size&&(n=document.body.style.pointerEvents,document.body.style.pointerEvents="none"),v.layersWithOutsidePointerEventsDisabled.add(m)),v.layers.add(m),P(),()=>{r&&1===v.layersWithOutsidePointerEventsDisabled.size&&(document.body.style.pointerEvents=n)}},[m,r,v]),(0,c.useEffect)(()=>()=>{m&&(v.layers.delete(m),v.layersWithOutsidePointerEventsDisabled.delete(m),P())},[m,v]),(0,c.useEffect)(()=>{let e=()=>g({});return document.addEventListener(x,e),()=>document.removeEventListener(x,e)},[]),(0,c.createElement)(A.div,(0,s.A)({},d,{ref:E,style:{pointerEvents:k?S?"auto":"none":void 0,...e.style},onFocusCapture:f(e.onFocusCapture,N.onFocusCapture),onBlurCapture:f(e.onBlurCapture,N.onBlurCapture),onPointerDownCapture:f(e.onPointerDownCapture,D.onPointerDownCapture)}))});function P(){let e=new CustomEvent(x);document.dispatchEvent(e)}function M(e,t,r,{discrete:n}){let l=r.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});if(t&&l.addEventListener(e,t,{once:!0}),n)l&&(0,y.flushSync)(()=>l.dispatchEvent(o));else l.dispatchEvent(o)}let D="focusScope.autoFocusOnMount",N="focusScope.autoFocusOnUnmount",L={bubbles:!1,cancelable:!0},I=(0,c.forwardRef)((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:l,onUnmountAutoFocus:o,...u}=e,[a,i]=(0,c.useState)(null),d=b(l),f=b(o),v=(0,c.useRef)(null),m=p(t,e=>i(e)),h=(0,c.useRef)({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;(0,c.useEffect)(()=>{if(n){function e(e){if(h.paused||!a)return;let t=e.target;a.contains(t)?v.current=t:_(v.current,{select:!0})}function t(e){!h.paused&&a&&(a.contains(e.relatedTarget)||_(v.current,{select:!0}))}return document.addEventListener("focusin",e),document.addEventListener("focusout",t),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t)}}},[n,a,h.paused]),(0,c.useEffect)(()=>{if(a){j.add(h);let e=document.activeElement;if(!a.contains(e)){let t=new CustomEvent(D,L);a.addEventListener(D,d),a.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(_(n,{select:t}),document.activeElement!==r)return}(F(a).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&_(a))}return()=>{a.removeEventListener(D,d),setTimeout(()=>{let t=new CustomEvent(N,L);a.addEventListener(N,f),a.dispatchEvent(t),t.defaultPrevented||_(null!=e?e:document.body,{select:!0}),a.removeEventListener(N,f),j.remove(h)},0)}}},[a,d,f,h]);let g=(0,c.useCallback)(e=>{if(!r&&!n||h.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,l=document.activeElement;if(t&&l){let t=e.currentTarget,[n,o]=function(e){let t=F(e);return[T(t,e),T(t.reverse(),e)]}(t);n&&o?e.shiftKey||l!==o?e.shiftKey&&l===n&&(e.preventDefault(),r&&_(o,{select:!0})):(e.preventDefault(),r&&_(n,{select:!0})):l===t&&e.preventDefault()}},[r,n,h.paused]);return(0,c.createElement)(A.div,(0,s.A)({tabIndex:-1},u,{ref:m,onKeyDown:g}))});function F(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function T(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function _(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}let j=function(){let e=[];return{add(t){let r=e[0];t!==r&&(null==r||r.pause()),(e=q(e,t)).unshift(t)},remove(t){var r;null==(r=(e=q(e,t))[0])||r.resume()}}}();function q(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}let U=(0,c.forwardRef)((e,t)=>{var r;let{container:n=null==globalThis||null==(r=globalThis.document)?void 0:r.body,...l}=e;return n?y.createPortal((0,c.createElement)(A.div,(0,s.A)({},l,{ref:t})),n):null}),$=e=>{let{present:t,children:r}=e,n=function(e){var t,r;let[n,l]=(0,c.useState)(),o=(0,c.useRef)({}),u=(0,c.useRef)(e),a=(0,c.useRef)("none"),[i,s]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},(0,c.useReducer)((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return(0,c.useEffect)(()=>{let e=z(o.current);a.current="mounted"===i?e:"none"},[i]),m(()=>{let t=o.current,r=u.current;if(r!==e){let n=a.current,l=z(t);e?s("MOUNT"):"none"===l||(null==t?void 0:t.display)==="none"?s("UNMOUNT"):r&&n!==l?s("ANIMATION_OUT"):s("UNMOUNT"),u.current=e}},[e,s]),m(()=>{if(n){let e=e=>{let t=z(o.current).includes(e.animationName);e.target===n&&t&&(0,y.flushSync)(()=>s("ANIMATION_END"))},t=e=>{e.target===n&&(a.current=z(o.current))};return n.addEventListener("animationstart",t),n.addEventListener("animationcancel",e),n.addEventListener("animationend",e),()=>{n.removeEventListener("animationstart",t),n.removeEventListener("animationcancel",e),n.removeEventListener("animationend",e)}}s("ANIMATION_END")},[n,s]),{isPresent:["mounted","unmountSuspended"].includes(i),ref:(0,c.useCallback)(e=>{e&&(o.current=getComputedStyle(e)),l(e)},[])}}(t),l="function"==typeof r?r({present:n.isPresent}):c.Children.only(r),o=p(n.ref,l.ref);return"function"==typeof r||n.isPresent?(0,c.cloneElement)(l,{ref:o}):null};function z(e){return(null==e?void 0:e.animationName)||"none"}$.displayName="Presence";let B=0;function K(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}var W=r(39249),V=r(56985),Q=r(70464),H=(0,r(37548).f)(),Z=function(){},X=c.forwardRef(function(e,t){var r=c.useRef(null),n=c.useState({onScrollCapture:Z,onWheelCapture:Z,onTouchMoveCapture:Z}),l=n[0],o=n[1],u=e.forwardProps,a=e.children,i=e.className,s=e.removeScrollBar,d=e.enabled,f=e.shards,v=e.sideCar,p=e.noIsolation,m=e.inert,h=e.allowPinchZoom,g=e.as,E=(0,W.Tt)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as"]),b=(0,Q.S)([r,t]),y=(0,W.Cl)((0,W.Cl)({},E),l);return c.createElement(c.Fragment,null,d&&c.createElement(v,{sideCar:H,removeScrollBar:s,shards:f,noIsolation:p,inert:m,setCallbacks:o,allowPinchZoom:!!h,lockRef:r}),u?c.cloneElement(c.Children.only(a),(0,W.Cl)((0,W.Cl)({},y),{ref:b})):c.createElement(void 0===g?"div":g,(0,W.Cl)({},y,{className:i,ref:b}),a))});X.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},X.classNames={fullWidth:V.pN,zeroRight:V.Mi};var Y=r(50514),G=r(21515),J=r(29874),ee=!1;if("undefined"!=typeof window)try{var et=Object.defineProperty({},"passive",{get:function(){return ee=!0,!0}});window.addEventListener("test",et,et),window.removeEventListener("test",et,et)}catch(e){ee=!1}var er=!!ee&&{passive:!1},en=function(e){var t=window.getComputedStyle(e);return"hidden"!==t.overflowY&&(t.overflowY!==t.overflowX||"visible"!==t.overflowY)},el=function(e){var t=window.getComputedStyle(e);return"hidden"!==t.overflowX&&(t.overflowY!==t.overflowX||"visible"!==t.overflowX)},eo=function(e,t){var r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),eu(e,r)){var n=ea(e,r);if(n[1]>n[2])return!0}r=r.parentNode}while(r&&r!==document.body);return!1},eu=function(e,t){return"v"===e?en(t):el(t)},ea=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},ei=function(e,t,r,n,l){var o,u=(o=window.getComputedStyle(t).direction,"h"===e&&"rtl"===o?-1:1),a=u*n,i=r.target,s=t.contains(i),c=!1,d=a>0,f=0,v=0;do{var p=ea(e,i),m=p[0],h=p[1]-p[2]-u*m;(m||h)&&eu(e,i)&&(f+=h,v+=m),i=i.parentNode}while(!s&&i!==document.body||s&&(t.contains(i)||t===i));return d&&(l&&0===f||!l&&a>f)?c=!0:!d&&(l&&0===v||!l&&-a>v)&&(c=!0),c},es=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ec=function(e){return[e.deltaX,e.deltaY]},ed=function(e){return e&&"current"in e?e.current:e},ef=0,ev=[];let ep=(0,Y.m)(H,function(e){var t=c.useRef([]),r=c.useRef([0,0]),n=c.useRef(),l=c.useState(ef++)[0],o=c.useState(function(){return(0,J.T0)()})[0],u=c.useRef(e);c.useEffect(function(){u.current=e},[e]),c.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(l));var t=(0,W.fX)([e.lockRef.current],(e.shards||[]).map(ed),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(l))}),function(){document.body.classList.remove("block-interactivity-".concat(l)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(l))})}}},[e.inert,e.lockRef.current,e.shards]);var a=c.useCallback(function(e,t){if("touches"in e&&2===e.touches.length)return!u.current.allowPinchZoom;var l,o=es(e),a=r.current,i="deltaX"in e?e.deltaX:a[0]-o[0],s="deltaY"in e?e.deltaY:a[1]-o[1],c=e.target,d=Math.abs(i)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var f=eo(d,c);if(!f)return!0;if(f?l=d:(l="v"===d?"h":"v",f=eo(d,c)),!f)return!1;if(!n.current&&"changedTouches"in e&&(i||s)&&(n.current=l),!l)return!0;var v=n.current||l;return ei(v,t,e,"h"===v?i:s,!0)},[]),i=c.useCallback(function(e){if(ev.length&&ev[ev.length-1]===o){var r="deltaY"in e?ec(e):es(e),n=t.current.filter(function(t){var n;return t.name===e.type&&t.target===e.target&&(n=t.delta,n[0]===r[0]&&n[1]===r[1])})[0];if(n&&n.should)return void e.preventDefault();if(!n){var l=(u.current.shards||[]).map(ed).filter(Boolean).filter(function(t){return t.contains(e.target)});(l.length>0?a(e,l[0]):!u.current.noIsolation)&&e.preventDefault()}}},[]),s=c.useCallback(function(e,r,n,l){var o={name:e,delta:r,target:n,should:l};t.current.push(o),setTimeout(function(){t.current=t.current.filter(function(e){return e!==o})},1)},[]),d=c.useCallback(function(e){r.current=es(e),n.current=void 0},[]),f=c.useCallback(function(t){s(t.type,ec(t),t.target,a(t,e.lockRef.current))},[]),v=c.useCallback(function(t){s(t.type,es(t),t.target,a(t,e.lockRef.current))},[]);c.useEffect(function(){return ev.push(o),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:v}),document.addEventListener("wheel",i,er),document.addEventListener("touchmove",i,er),document.addEventListener("touchstart",d,er),function(){ev=ev.filter(function(e){return e!==o}),document.removeEventListener("wheel",i,er),document.removeEventListener("touchmove",i,er),document.removeEventListener("touchstart",d,er)}},[]);var p=e.removeScrollBar,m=e.inert;return c.createElement(c.Fragment,null,m?c.createElement(o,{styles:"\n  .block-interactivity-".concat(l," {pointer-events: none;}\n  .allow-interactivity-").concat(l," {pointer-events: all;}\n")}):null,p?c.createElement(G.jp,{gapMode:"margin"}):null)});var em=c.forwardRef(function(e,t){return c.createElement(X,(0,W.Cl)({},e,{ref:t,sideCar:ep}))});em.classNames=X.classNames;var eh=r(38168);let eg="Dialog",[eE,eb]=function(e,t=[]){let r=[],n=()=>{let t=r.map(e=>(0,c.createContext)(e));return function(r){let n=(null==r?void 0:r[e])||t;return(0,c.useMemo)(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return n.scopeName=e,[function(t,n){let l=(0,c.createContext)(n),o=r.length;function u(t){let{scope:r,children:n,...u}=t,a=(null==r?void 0:r[e][o])||l,i=(0,c.useMemo)(()=>u,Object.values(u));return(0,c.createElement)(a.Provider,{value:i},n)}return r=[...r,n],u.displayName=t+"Provider",[u,function(r,u){let a=(null==u?void 0:u[e][o])||l,i=(0,c.useContext)(a);if(i)return i;if(void 0!==n)return n;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:n})=>{let l=r(e)[`__scope${n}`];return{...t,...l}},{});return(0,c.useMemo)(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(n,...t)]}(eg),[ey,ew]=eE(eg),eC="DialogPortal",[eR,ek]=eE(eC,{forceMount:void 0}),eA="DialogOverlay",ex=(0,c.forwardRef)((e,t)=>{let r=ek(eA,e.__scopeDialog),{forceMount:n=r.forceMount,...l}=e,o=ew(eA,e.__scopeDialog);return o.modal?(0,c.createElement)($,{present:n||o.open},(0,c.createElement)(eO,(0,s.A)({},l,{ref:t}))):null}),eO=(0,c.forwardRef)((e,t)=>{let{__scopeDialog:r,...n}=e,l=ew(eA,r);return(0,c.createElement)(em,{as:w,allowPinchZoom:!0,shards:[l.contentRef]},(0,c.createElement)(A.div,(0,s.A)({"data-state":eI(l.open)},n,{ref:t,style:{pointerEvents:"auto",...n.style}})))}),eS="DialogContent",eP=(0,c.forwardRef)((e,t)=>{let r=ek(eS,e.__scopeDialog),{forceMount:n=r.forceMount,...l}=e,o=ew(eS,e.__scopeDialog);return(0,c.createElement)($,{present:n||o.open},o.modal?(0,c.createElement)(eM,(0,s.A)({},l,{ref:t})):(0,c.createElement)(eD,(0,s.A)({},l,{ref:t})))}),eM=(0,c.forwardRef)((e,t)=>{let r=ew(eS,e.__scopeDialog),n=(0,c.useRef)(null),l=p(t,r.contentRef,n);return(0,c.useEffect)(()=>{let e=n.current;if(e)return(0,eh.Eq)(e)},[]),(0,c.createElement)(eN,(0,s.A)({},e,{ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:f(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:f(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:f(e.onFocusOutside,e=>e.preventDefault())}))}),eD=(0,c.forwardRef)((e,t)=>{let r=ew(eS,e.__scopeDialog),n=(0,c.useRef)(!1);return(0,c.createElement)(eN,(0,s.A)({},e,{ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var l,o;null==(l=e.onCloseAutoFocus)||l.call(e,t),t.defaultPrevented||(n.current||null==(o=r.triggerRef.current)||o.focus(),t.preventDefault()),n.current=!1},onInteractOutside:t=>{var l,o;null==(l=e.onInteractOutside)||l.call(e,t),t.defaultPrevented||(n.current=!0);let u=t.target;(null==(o=r.triggerRef.current)?void 0:o.contains(u))&&t.preventDefault()}}))}),eN=(0,c.forwardRef)((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:l,onCloseAutoFocus:o,...u}=e,a=ew(eS,r),i=p(t,(0,c.useRef)(null));return(0,c.useEffect)(()=>{var e,t;let r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=r[0])?e:K()),document.body.insertAdjacentElement("beforeend",null!=(t=r[1])?t:K()),B++,()=>{1===B&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),B--}},[]),(0,c.createElement)(c.Fragment,null,(0,c.createElement)(I,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:l,onUnmountAutoFocus:o},(0,c.createElement)(S,(0,s.A)({role:"dialog",id:a.contentId,"aria-describedby":a.descriptionId,"aria-labelledby":a.titleId,"data-state":eI(a.open)},u,{ref:i,onDismiss:()=>a.onOpenChange(!1)}))),!1)}),eL="DialogTitle";function eI(e){return e?"open":"closed"}let[eF,eT]=function(e,t){let r=(0,c.createContext)(t);function n(e){let{children:t,...n}=e,l=(0,c.useMemo)(()=>n,Object.values(n));return(0,c.createElement)(r.Provider,{value:l},t)}return n.displayName=e+"Provider",[n,function(n){let l=(0,c.useContext)(r);if(l)return l;if(void 0!==t)return t;throw Error(`\`${n}\` must be used within \`${e}\``)}]}("DialogTitleWarning",{contentName:eS,titleName:eL,docsSlug:"dialog"}),e_=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:l,onOpenChange:o,modal:u=!0}=e,a=(0,c.useRef)(null),i=(0,c.useRef)(null),[s=!1,d]=function({prop:e,defaultProp:t,onChange:r=()=>{}}){let[n,l]=function({defaultProp:e,onChange:t}){let r=(0,c.useState)(e),[n]=r,l=(0,c.useRef)(n),o=b(t);return(0,c.useEffect)(()=>{l.current!==n&&(o(n),l.current=n)},[n,l,o]),r}({defaultProp:t,onChange:r}),o=void 0!==e,u=o?e:n,a=b(r);return[u,(0,c.useCallback)(t=>{if(o){let r="function"==typeof t?t(e):t;r!==e&&a(r)}else l(t)},[o,e,l,a])]}({prop:n,defaultProp:l,onChange:o});return(0,c.createElement)(ey,{scope:t,triggerRef:a,contentRef:i,contentId:E(),titleId:E(),descriptionId:E(),open:s,onOpenChange:d,onOpenToggle:(0,c.useCallback)(()=>d(e=>!e),[d]),modal:u},r)},ej=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:l}=e,o=ew(eC,t);return(0,c.createElement)(eR,{scope:t,forceMount:r},c.Children.map(n,e=>(0,c.createElement)($,{present:r||o.open},(0,c.createElement)(U,{asChild:!0,container:l},e))))};var eq='[cmdk-group=""]',eU='[cmdk-group-items=""]',e$='[cmdk-item=""]',ez=`${e$}:not([aria-disabled="true"])`,eB="cmdk-item-select",eK="data-value",eW=(e,t)=>(function(e,t){return function e(t,r,n,i,s,c,d){if(c===r.length)return s===t.length?1:.99;var f=`${s},${c}`;if(void 0!==d[f])return d[f];for(var v,p,m,h,g=i.charAt(c),E=n.indexOf(g,s),b=0;E>=0;)(v=e(t,r,n,i,E+1,c+1,d))>b&&(E===s?v*=1:l.test(t.charAt(E-1))?(v*=.8,(m=t.slice(s,E-1).match(o))&&s>0&&(v*=Math.pow(.999,m.length))):u.test(t.charAt(E-1))?(v*=.9,(h=t.slice(s,E-1).match(a))&&s>0&&(v*=Math.pow(.999,h.length))):(v*=.17,s>0&&(v*=Math.pow(.999,E-s))),t.charAt(E)!==r.charAt(c)&&(v*=.9999)),(v<.1&&n.charAt(E-1)===i.charAt(c+1)||i.charAt(c+1)===i.charAt(c)&&n.charAt(E-1)!==i.charAt(c))&&.1*(p=e(t,r,n,i,E+1,c+2,d))>v&&(v=.1*p),v>b&&(b=v),E=n.indexOf(g,E+1);return d[f]=b,b}(e,t,i(e),i(t),0,0,{})})(e,t),eV=c.createContext(void 0),eQ=()=>c.useContext(eV),eH=c.createContext(void 0),eZ=()=>c.useContext(eH),eX=c.createContext(void 0),eY=c.forwardRef((e,t)=>{let r=c.useRef(null),n=e4(()=>{var t,r,n;return{search:"",value:null!=(n=null!=(r=e.value)?r:null==(t=e.defaultValue)?void 0:t.toLowerCase())?n:"",filtered:{count:0,items:new Map,groups:new Set}}}),l=e4(()=>new Set),o=e4(()=>new Map),u=e4(()=>new Map),a=e4(()=>new Set),i=e7(e),{label:s,children:d,value:f,onValueChange:v,filter:p,shouldFilter:m,vimBindings:h=!0,...g}=e,E=c.useId(),b=c.useId(),y=c.useId(),w=tt();e3(()=>{if(void 0!==f){let e=f.trim().toLowerCase();n.current.value=e,w(6,S),C.emit()}},[f]);let C=c.useMemo(()=>({subscribe:e=>(a.current.add(e),()=>a.current.delete(e)),snapshot:()=>n.current,setState:(e,t,r)=>{var l,o,u;if(!Object.is(n.current[e],t)){if(n.current[e]=t,"search"===e)O(),A(),w(1,x);else if("value"===e)if((null==(l=i.current)?void 0:l.value)!==void 0){null==(u=(o=i.current).onValueChange)||u.call(o,null!=t?t:"");return}else r||w(5,S);C.emit()}},emit:()=>{a.current.forEach(e=>e())}}),[]),R=c.useMemo(()=>({value:(e,t)=>{t!==u.current.get(e)&&(u.current.set(e,t),n.current.filtered.items.set(e,k(t)),w(2,()=>{A(),C.emit()}))},item:(e,t)=>(l.current.add(e),t&&(o.current.has(t)?o.current.get(t).add(e):o.current.set(t,new Set([e]))),w(3,()=>{O(),A(),n.current.value||x(),C.emit()}),()=>{u.current.delete(e),l.current.delete(e),n.current.filtered.items.delete(e);let t=P();w(4,()=>{O(),(null==t?void 0:t.getAttribute("id"))===e&&x(),C.emit()})}),group:e=>(o.current.has(e)||o.current.set(e,new Set),()=>{u.current.delete(e),o.current.delete(e)}),filter:()=>i.current.shouldFilter,label:s||e["aria-label"],commandRef:r,listId:E,inputId:y,labelId:b}),[]);function k(e){var t,r;let l=null!=(r=null==(t=i.current)?void 0:t.filter)?r:eW;return e?l(e,n.current.search):0}function A(){if(!r.current||!n.current.search||!1===i.current.shouldFilter)return;let e=n.current.filtered.items,t=[];n.current.filtered.groups.forEach(r=>{let n=o.current.get(r),l=0;n.forEach(t=>{l=Math.max(e.get(t),l)}),t.push([r,l])});let l=r.current.querySelector('[cmdk-list-sizer=""]');M().sort((t,r)=>{var n,l;let o=t.getAttribute(eK),u=r.getAttribute(eK);return(null!=(n=e.get(u))?n:0)-(null!=(l=e.get(o))?l:0)}).forEach(e=>{let t=e.closest(eU);t?t.appendChild(e.parentElement===t?e:e.closest(`${eU} > *`)):l.appendChild(e.parentElement===l?e:e.closest(`${eU} > *`))}),t.sort((e,t)=>t[1]-e[1]).forEach(e=>{let t=r.current.querySelector(`${eq}[${eK}="${e[0]}"]`);null==t||t.parentElement.appendChild(t)})}function x(){let e=M().find(e=>!e.ariaDisabled),t=null==e?void 0:e.getAttribute(eK);C.setState("value",t||void 0)}function O(){if(!n.current.search||!1===i.current.shouldFilter){n.current.filtered.count=l.current.size;return}n.current.filtered.groups=new Set;let e=0;for(let t of l.current){let r=k(u.current.get(t));n.current.filtered.items.set(t,r),r>0&&e++}for(let[e,t]of o.current)for(let r of t)if(n.current.filtered.items.get(r)>0){n.current.filtered.groups.add(e);break}n.current.filtered.count=e}function S(){var e,t,r;let n=P();n&&((null==(e=n.parentElement)?void 0:e.firstChild)===n&&(null==(r=null==(t=n.closest(eq))?void 0:t.querySelector('[cmdk-group-heading=""]'))||r.scrollIntoView({block:"nearest"})),n.scrollIntoView({block:"nearest"}))}function P(){var e;return null==(e=r.current)?void 0:e.querySelector(`${e$}[aria-selected="true"]`)}function M(){return Array.from(r.current.querySelectorAll(ez))}function D(e){let t=M()[e];t&&C.setState("value",t.getAttribute(eK))}function N(e){var t;let r=P(),n=M(),l=n.findIndex(e=>e===r),o=n[l+e];null!=(t=i.current)&&t.loop&&(o=l+e<0?n[n.length-1]:l+e===n.length?n[0]:n[l+e]),o&&C.setState("value",o.getAttribute(eK))}function L(e){let t=P(),r=null==t?void 0:t.closest(eq),n;for(;r&&!n;)n=null==(r=e>0?function(e,t){let r=e.nextElementSibling;for(;r;){if(r.matches(t))return r;r=r.nextElementSibling}}(r,eq):function(e,t){let r=e.previousElementSibling;for(;r;){if(r.matches(t))return r;r=r.previousElementSibling}}(r,eq))?void 0:r.querySelector(ez);n?C.setState("value",n.getAttribute(eK)):N(e)}let I=()=>D(M().length-1),F=e=>{e.preventDefault(),e.metaKey?I():e.altKey?L(1):N(1)},T=e=>{e.preventDefault(),e.metaKey?D(0):e.altKey?L(-1):N(-1)};return c.createElement("div",{ref:e6([r,t]),...g,"cmdk-root":"",onKeyDown:e=>{var t;if(null==(t=g.onKeyDown)||t.call(g,e),!e.defaultPrevented)switch(e.key){case"n":case"j":h&&e.ctrlKey&&F(e);break;case"ArrowDown":F(e);break;case"p":case"k":h&&e.ctrlKey&&T(e);break;case"ArrowUp":T(e);break;case"Home":e.preventDefault(),D(0);break;case"End":e.preventDefault(),I();break;case"Enter":if(!e.nativeEvent.isComposing){e.preventDefault();let t=P();if(t){let e=new Event(eB);t.dispatchEvent(e)}}}}},c.createElement("label",{"cmdk-label":"",htmlFor:R.inputId,id:R.labelId,style:tr},s),c.createElement(eH.Provider,{value:C},c.createElement(eV.Provider,{value:R},d)))}),eG=c.forwardRef((e,t)=>{var r,n;let l=c.useId(),o=c.useRef(null),u=c.useContext(eX),a=eQ(),i=e7(e),s=null!=(n=null==(r=i.current)?void 0:r.forceMount)?n:null==u?void 0:u.forceMount;e3(()=>a.item(l,null==u?void 0:u.id),[]);let d=te(l,o,[e.value,e.children,o]),f=eZ(),v=e8(e=>e.value&&e.value===d.current),p=e8(e=>!!s||!1===a.filter()||!e.search||e.filtered.items.get(l)>0);function m(){var e,t;h(),null==(t=(e=i.current).onSelect)||t.call(e,d.current)}function h(){f.setState("value",d.current,!0)}if(c.useEffect(()=>{let t=o.current;if(!(!t||e.disabled))return t.addEventListener(eB,m),()=>t.removeEventListener(eB,m)},[p,e.onSelect,e.disabled]),!p)return null;let{disabled:g,value:E,onSelect:b,forceMount:y,...w}=e;return c.createElement("div",{ref:e6([o,t]),...w,id:l,"cmdk-item":"",role:"option","aria-disabled":g||void 0,"aria-selected":v||void 0,"data-disabled":g||void 0,"data-selected":v||void 0,onPointerMove:g?void 0:h,onClick:g?void 0:m},e.children)}),eJ=c.forwardRef((e,t)=>{let{heading:r,children:n,forceMount:l,...o}=e,u=c.useId(),a=c.useRef(null),i=c.useRef(null),s=c.useId(),d=eQ(),f=e8(e=>!!l||!1===d.filter()||!e.search||e.filtered.groups.has(u));e3(()=>d.group(u),[]),te(u,a,[e.value,e.heading,i]);let v=c.useMemo(()=>({id:u,forceMount:l}),[l]),p=c.createElement(eX.Provider,{value:v},n);return c.createElement("div",{ref:e6([a,t]),...o,"cmdk-group":"",role:"presentation",hidden:!f||void 0},r&&c.createElement("div",{ref:i,"cmdk-group-heading":"","aria-hidden":!0,id:s},r),c.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":r?s:void 0},p))}),e0=c.forwardRef((e,t)=>{let{alwaysRender:r,...n}=e,l=c.useRef(null),o=e8(e=>!e.search);return r||o?c.createElement("div",{ref:e6([l,t]),...n,"cmdk-separator":"",role:"separator"}):null}),e1=c.forwardRef((e,t)=>{let{onValueChange:r,...n}=e,l=null!=e.value,o=eZ(),u=e8(e=>e.search),a=e8(e=>e.value),i=eQ(),s=c.useMemo(()=>{var e;let t=null==(e=i.commandRef.current)?void 0:e.querySelector(`${e$}[${eK}="${a}"]`);return null==t?void 0:t.getAttribute("id")},[a,i.commandRef]);return c.useEffect(()=>{null!=e.value&&o.setState("search",e.value)},[e.value]),c.createElement("input",{ref:t,...n,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":i.listId,"aria-labelledby":i.labelId,"aria-activedescendant":s,id:i.inputId,type:"text",value:l?e.value:u,onChange:e=>{l||o.setState("search",e.target.value),null==r||r(e.target.value)}})}),e2=c.forwardRef((e,t)=>{let{children:r,...n}=e,l=c.useRef(null),o=c.useRef(null),u=eQ();return c.useEffect(()=>{if(o.current&&l.current){let e=o.current,t=l.current,r,n=new ResizeObserver(()=>{r=requestAnimationFrame(()=>{let r=e.offsetHeight;t.style.setProperty("--cmdk-list-height",r.toFixed(1)+"px")})});return n.observe(e),()=>{cancelAnimationFrame(r),n.unobserve(e)}}},[]),c.createElement("div",{ref:e6([l,t]),...n,"cmdk-list":"",role:"listbox","aria-label":"Suggestions",id:u.listId,"aria-labelledby":u.inputId},c.createElement("div",{ref:o,"cmdk-list-sizer":""},r))}),e5=c.forwardRef((e,t)=>{let{open:r,onOpenChange:n,overlayClassName:l,contentClassName:o,container:u,...a}=e;return c.createElement(e_,{open:r,onOpenChange:n},c.createElement(ej,{container:u},c.createElement(ex,{"cmdk-overlay":"",className:l}),c.createElement(eP,{"aria-label":e.label,"cmdk-dialog":"",className:o},c.createElement(eY,{ref:t,...a}))))}),e9=Object.assign(eY,{List:e2,Item:eG,Input:e1,Group:eJ,Separator:e0,Dialog:e5,Empty:c.forwardRef((e,t)=>{let r=c.useRef(!0),n=e8(e=>0===e.filtered.count);return c.useEffect(()=>{r.current=!1},[]),r.current||!n?null:c.createElement("div",{ref:t,...e,"cmdk-empty":"",role:"presentation"})}),Loading:c.forwardRef((e,t)=>{let{progress:r,children:n,...l}=e;return c.createElement("div",{ref:t,...l,"cmdk-loading":"",role:"progressbar","aria-valuenow":r,"aria-valuemin":0,"aria-valuemax":100,"aria-label":"Loading..."},c.createElement("div",{"aria-hidden":!0},n))})});function e7(e){let t=c.useRef(e);return e3(()=>{t.current=e}),t}var e3="undefined"==typeof window?c.useEffect:c.useLayoutEffect;function e4(e){let t=c.useRef();return void 0===t.current&&(t.current=e()),t}function e6(e){return t=>{e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}}function e8(e){let t=eZ(),r=()=>e(t.snapshot());return c.useSyncExternalStore(t.subscribe,r,r)}function te(e,t,r){let n=c.useRef(),l=eQ();return e3(()=>{var o;let u=(()=>{var e;for(let t of r){if("string"==typeof t)return t.trim().toLowerCase();if("object"==typeof t&&"current"in t)return t.current?null==(e=t.current.textContent)?void 0:e.trim().toLowerCase():n.current}})();l.value(e,u),null==(o=t.current)||o.setAttribute(eK,u),n.current=u}),n}var tt=()=>{let[e,t]=c.useState(),r=e4(()=>new Map);return e3(()=>{r.current.forEach(e=>e()),r.current=new Map},[e]),(e,n)=>{r.current.set(e,n),t({})}},tr={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"}},62525:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},71610:(e,t,r)=>{r.d(t,{E:()=>m});var n=r(12115),l=r(7165),o=r(76347),u=r(25910),a=r(52020);function i(e,t){return e.filter(e=>!t.includes(e))}var s=class extends u.Q{#e;#t;#r;#n;#l;#o;#u;#a;#i=[];constructor(e,t,r){super(),this.#e=e,this.#n=r,this.#r=[],this.#l=[],this.#t=[],this.setQueries(t)}onSubscribe(){1===this.listeners.size&&this.#l.forEach(e=>{e.subscribe(t=>{this.#s(e,t)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#l.forEach(e=>{e.destroy()})}setQueries(e,t){this.#r=e,this.#n=t,l.jG.batch(()=>{let e=this.#l,t=this.#c(this.#r);this.#i=t,t.forEach(e=>e.observer.setOptions(e.defaultedQueryOptions));let r=t.map(e=>e.observer),n=r.map(e=>e.getCurrentResult()),l=r.some((t,r)=>t!==e[r]);(e.length!==r.length||l)&&(this.#l=r,this.#t=n,this.hasListeners()&&(i(e,r).forEach(e=>{e.destroy()}),i(r,e).forEach(e=>{e.subscribe(t=>{this.#s(e,t)})}),this.#d()))})}getCurrentResult(){return this.#t}getQueries(){return this.#l.map(e=>e.getCurrentQuery())}getObservers(){return this.#l}getOptimisticResult(e,t){let r=this.#c(e),n=r.map(e=>e.observer.getOptimisticResult(e.defaultedQueryOptions));return[n,e=>this.#f(e??n,t),()=>this.#v(n,r)]}#v(e,t){return t.map((r,n)=>{let l=e[n];return r.defaultedQueryOptions.notifyOnChangeProps?l:r.observer.trackResult(l,e=>{t.forEach(t=>{t.observer.trackProp(e)})})})}#f(e,t){return t?(this.#o&&this.#t===this.#a&&t===this.#u||(this.#u=t,this.#a=this.#t,this.#o=(0,a.BH)(this.#o,t(e))),this.#o):e}#c(e){let t=new Map(this.#l.map(e=>[e.options.queryHash,e])),r=[];return e.forEach(e=>{let n=this.#e.defaultQueryOptions(e),l=t.get(n.queryHash);l?r.push({defaultedQueryOptions:n,observer:l}):r.push({defaultedQueryOptions:n,observer:new o.$(this.#e,n)})}),r}#s(e,t){let r=this.#l.indexOf(e);-1!==r&&(this.#t=function(e,t,r){let n=e.slice(0);return n[t]=r,n}(this.#t,r,t),this.#d())}#d(){if(this.hasListeners()){let e=this.#o,t=this.#v(this.#t,this.#i);e!==this.#f(t,this.#n?.combine)&&l.jG.batch(()=>{this.listeners.forEach(e=>{e(this.#t)})})}}},c=r(26715),d=r(61581),f=r(80382),v=r(22450),p=r(4791);function m(e,t){let{queries:r,...u}=e,i=(0,c.jE)(t),m=(0,d.w)(),h=(0,f.h)(),g=n.useMemo(()=>r.map(e=>{let t=i.defaultQueryOptions(e);return t._optimisticResults=m?"isRestoring":"optimistic",t}),[r,i,m]);g.forEach(e=>{(0,p.jv)(e),(0,v.LJ)(e,h)}),(0,v.wZ)(h);let[E]=n.useState(()=>new s(i,g,u)),[b,y,w]=E.getOptimisticResult(g,u.combine),C=!m&&!1!==u.subscribed;n.useSyncExternalStore(n.useCallback(e=>C?E.subscribe(l.jG.batchCalls(e)):a.lQ,[E,C]),()=>E.getCurrentResult(),()=>E.getCurrentResult()),n.useEffect(()=>{E.setQueries(g,u)},[g,u,E]);let R=b.some((e,t)=>(0,p.EU)(g[t],e))?b.flatMap((e,t)=>{let r=g[t];if(r){let t=new o.$(i,r);if((0,p.EU)(r,e))return(0,p.iL)(r,t,h);(0,p.nE)(e,m)&&(0,p.iL)(r,t,h)}return[]}):[];if(R.length>0)throw Promise.all(R);let k=b.find((e,t)=>{let r=g[t];return r&&(0,v.$1)({result:e,errorResetBoundary:h,throwOnError:r.throwOnError,query:i.getQueryCache().get(r.queryHash),suspense:r.suspense})});if(null==k?void 0:k.error)throw k.error;return y(w())}},79630:(e,t,r)=>{r.d(t,{A:()=>n});function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}},84616:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}}]);