"use strict";exports.id=846,exports.ids=[846],exports.modules={173:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{iv:()=>l.iv}),s(98977);var l=s(36851),a=e([l]);l=(a.then?(await a)():a)[0],r()}catch(e){r(e)}})},7072:(e,t,s)=>{s.d(t,{Bm:()=>g,I3:()=>x,QC:()=>p,QF:()=>d,Rz:()=>u,WP:()=>h,ZL:()=>f,_e:()=>c,np:()=>b,px:()=>m});var r=s(17090),l=s(98152),a=s(88233),n=s(26316),i=s(37911),o=s(78464);let c=e=>({html:"html",htm:"html",css:"css",scss:"scss",sass:"scss",less:"less",js:"javascript",jsx:"jsx",ts:"typescript",tsx:"tsx",json:"json",jsonc:"json",xml:"xml",yml:"yaml",yaml:"yaml",toml:"toml",ini:"ini",env:"bash",gitignore:"bash",dockerignore:"bash",py:"python",rb:"ruby",php:"php",go:"go",java:"java",kt:"kotlin",c:"c",cpp:"cpp",h:"c",hpp:"cpp",cs:"csharp",swift:"swift",rs:"rust",sh:"bash",bash:"bash",zsh:"bash",ps1:"powershell",bat:"batch",cmd:"batch",svg:"svg",tex:"latex",graphql:"graphql",gql:"graphql"})[e.split(".").pop()?.toLowerCase()||""]||"text",d=(e,t)=>{if(e){if(e.includes("create"))return"create";if(e.includes("rewrite"))return"rewrite";if(e.includes("delete"))return"delete"}if(!t)return"create";let s="string"==typeof t?t:JSON.stringify(t);return!s||s.includes("<create-file>")?"create":s.includes("<full-file-rewrite>")?"rewrite":s.includes("delete-file")||s.includes("<delete>")?"delete":s.toLowerCase().includes("create file")?"create":s.toLowerCase().includes("rewrite file")?"rewrite":s.toLowerCase().includes("delete file")?"delete":"create"},m=()=>({create:{icon:r.A,color:"text-emerald-600 dark:text-emerald-400",successMessage:"File created successfully",progressMessage:"Creating file...",bgColor:"bg-gradient-to-b from-emerald-100 to-emerald-50 shadow-inner dark:from-emerald-800/40 dark:to-emerald-900/60 dark:shadow-emerald-950/20",gradientBg:"bg-gradient-to-br from-emerald-500/20 to-emerald-600/10",borderColor:"border-emerald-500/20",badgeColor:"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 shadow-sm dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300",hoverColor:"hover:bg-neutral-200 dark:hover:bg-neutral-800"},rewrite:{icon:l.A,color:"text-blue-600 dark:text-blue-400",successMessage:"File rewritten successfully",progressMessage:"Rewriting file...",bgColor:"bg-gradient-to-b from-blue-100 to-blue-50 shadow-inner dark:from-blue-800/40 dark:to-blue-900/60 dark:shadow-blue-950/20",gradientBg:"bg-gradient-to-br from-blue-500/20 to-blue-600/10",borderColor:"border-blue-500/20",badgeColor:"bg-gradient-to-b from-blue-200 to-blue-100 text-blue-700 shadow-sm dark:from-blue-800/50 dark:to-blue-900/60 dark:text-blue-300",hoverColor:"hover:bg-neutral-200 dark:hover:bg-neutral-800"},delete:{icon:a.A,color:"text-rose-600 dark:text-rose-400",successMessage:"File deleted successfully",progressMessage:"Deleting file...",bgColor:"bg-gradient-to-b from-rose-100 to-rose-50 shadow-inner dark:from-rose-800/40 dark:to-rose-900/60 dark:shadow-rose-950/20",gradientBg:"bg-gradient-to-br from-rose-500/20 to-rose-600/10",borderColor:"border-rose-500/20",badgeColor:"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 shadow-sm dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",hoverColor:"hover:bg-neutral-200 dark:hover:bg-neutral-800"}}),x=e=>e.endsWith(".md")?n.A:e.endsWith(".csv")?i.A:e.endsWith(".html")?n.A:o.A,u=e=>e?e.trim().replace(/\\n/g,"\n").split("\n")[0]:null,h=e=>e?e.split("/").pop()||e:"",p=e=>e.split(".").pop()?.toLowerCase()||"",f={markdown:e=>"md"===e,html:e=>"html"===e||"htm"===e,csv:e=>"csv"===e},g=e=>"text"!==e,b=e=>e?e.replace(/\\n/g,"\n").split("\n"):[]},7330:(e,t,s)=>{s.d(t,{S:()=>Y});var r=s(60687),l=s(43210),a=s.n(l),n=s(63503),i=s(29523),o=s(43125),c=s(47033),d=s(14952),m=s(32192),x=s(31158),u=s(10022),h=s(78272),p=s(77026),f=s(16023),g=s(43649),b=s(82570),j=s(78464),N=s(42692),w=s(4780),v=s(60669),k=s(94833),y=s(73168),z=s(43710),S=s(87641);function C({url:e,className:t}){let[s,a]=(0,l.useState)(null),[n,i]=(0,l.useState)(1),[o,c]=(0,l.useState)(1);function d(e){i(t=>{let r=t+e;return r>=1&&r<=(s||1)?r:t})}return(0,r.jsxs)("div",{className:(0,w.cn)("flex flex-col w-full h-full",t),children:[(0,r.jsx)("div",{className:"flex-1 overflow-auto rounded-md",children:(0,r.jsx)(z.A,{file:e,onLoadSuccess:function({numPages:e}){a(e)},className:"flex flex-col items-center",children:(0,r.jsx)(S.A,{pageNumber:n,scale:o,renderTextLayer:!0,renderAnnotationLayer:!0})})}),s&&(0,r.jsxs)("div",{className:"flex items-center justify-between p-2 bg-background border-t",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:function(){c(e=>Math.max(e-.2,.5))},className:"px-2 py-1 bg-muted rounded hover:bg-muted/80",disabled:o<=.5,children:"-"}),(0,r.jsxs)("span",{children:[Math.round(100*o),"%"]}),(0,r.jsx)("button",{onClick:function(){c(e=>Math.min(e+.2,3))},className:"px-2 py-1 bg-muted rounded hover:bg-muted/80",disabled:o>=3,children:"+"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("button",{onClick:function(){d(-1)},className:"px-2 py-1 bg-muted rounded hover:bg-muted/80",disabled:n<=1,children:"Previous"}),(0,r.jsxs)("span",{children:["Page ",n," of ",s]}),(0,r.jsx)("button",{onClick:function(){d(1)},className:"px-2 py-1 bg-muted rounded hover:bg-muted/80",disabled:n>=s,children:"Next"})]})]})]})}s(45837),s(44719),y.EA.workerSrc=new s.U(s(18148)).toString();var A=s(50004),E=s(16969),_=s(36444),$=s(96882),T=s(79351),R=s(49153);function L({url:e,className:t}){let[s,a]=(0,l.useState)(1),[n,o]=(0,l.useState)(0),[c,d]=(0,l.useState)(!1),[m,x]=(0,l.useState)({x:0,y:0}),[u,h]=(0,l.useState)({x:0,y:0}),[p,f]=(0,l.useState)(!0),[g,b]=(0,l.useState)(!1),[j,N]=(0,l.useState)(!1),[v,k]=(0,l.useState)(null),y=(0,l.useRef)(null),z=(0,l.useRef)(null),S=e?.toLowerCase().endsWith(".svg")||e?.includes("image/svg"),C=()=>{b(!0),N(!1),z.current&&k({width:z.current.naturalWidth,height:z.current.naturalHeight,type:S?"SVG":e.split(".").pop()?.toUpperCase()||"Image"})},L=()=>{b(!1),N(!0)},F=`scale(${s}) rotate(${n}deg)`,P=`translate(${m.x}px, ${m.y}px)`,[D,O]=(0,l.useState)(!1);return(0,r.jsxs)("div",{className:(0,w.cn)("flex flex-col w-full h-full",t),children:[(0,r.jsxs)("div",{className:"flex items-center justify-between py-2 px-4 bg-muted/30 border-b mb-2 rounded-t-md",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(i.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:()=>{let e=Math.max(s-.25,.5);a(e),.5===e&&f(!0)},title:"Zoom out",disabled:j,children:(0,r.jsx)(A.A,{className:"h-4 w-4"})}),(0,r.jsxs)("span",{className:"text-xs font-medium",children:[Math.round(100*s),"%"]}),(0,r.jsx)(i.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:()=>{a(e=>Math.min(e+.25,3)),f(!1)},title:"Zoom in",disabled:j,children:(0,r.jsx)(E.A,{className:"h-4 w-4"})}),(0,r.jsx)(i.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:()=>{o(e=>(e+90)%360)},title:"Rotate",disabled:j,children:(0,r.jsx)(_.A,{className:"h-4 w-4"})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(i.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:()=>O(!D),title:"Image information",children:(0,r.jsx)($.A,{className:"h-4 w-4"})}),(0,r.jsx)(i.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:()=>{p?(a(1),f(!1)):(a(.5),x({x:0,y:0}),f(!0))},title:p?"Actual size":"Fit to screen",disabled:j,children:p?(0,r.jsx)(T.A,{className:"h-4 w-4"}):(0,r.jsx)(R.A,{className:"h-4 w-4"})})]})]}),D&&v&&(0,r.jsxs)("div",{className:"absolute top-16 right-4 z-50 bg-background/80 backdrop-blur-sm p-3 rounded-md shadow-md border border-border text-xs",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Type:"})," ",v.type]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Dimensions:"})," ",v.width," \xd7 ",v.height]})]}),(0,r.jsx)("div",{ref:y,className:"flex-1 overflow-hidden relative bg-grid-pattern rounded-b-md",onMouseDown:e=>{s>.5&&(d(!0),h({x:e.clientX-m.x,y:e.clientY-m.y}))},onMouseMove:e=>{c&&s>.5&&x({x:e.clientX-u.x,y:e.clientY-u.y})},onMouseUp:()=>{d(!1)},onMouseLeave:()=>{d(!1)},style:{cursor:c?"grabbing":s>.5?"grab":"default",backgroundColor:"#f5f5f5",backgroundImage:"linear-gradient(45deg, #e0e0e0 25%, transparent 25%), linear-gradient(-45deg, #e0e0e0 25%, transparent 25%), linear-gradient(45deg, transparent 75%, #e0e0e0 75%), linear-gradient(-45deg, transparent 75%, #e0e0e0 75%)",backgroundSize:"20px 20px",backgroundPosition:"0 0, 0 10px, 10px -10px, -10px 0px"},children:j?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full p-6 text-center",children:[(0,r.jsx)("p",{className:"text-destructive font-medium mb-2",children:"Failed to load image"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"The image could not be displayed"})]}):(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",style:{transform:p?"none":P,transition:c?"none":"transform 0.1s ease"},children:S?(0,r.jsx)("object",{data:e,type:"image/svg+xml",className:"max-w-full max-h-full",style:{transform:F,transition:"transform 0.2s ease",width:"100%",height:"100%"},children:(0,r.jsx)("img",{ref:z,src:e,alt:"SVG preview",className:"max-w-full max-h-full object-contain",style:{transform:F,transition:"transform 0.2s ease"},draggable:!1,onLoad:C,onError:L})}):(0,r.jsx)("img",{ref:z,src:e,alt:"Image preview",className:"max-w-full max-h-full object-contain",style:{transform:F,transition:"transform 0.2s ease"},draggable:!1,onLoad:C,onError:L})})})]})}function F({url:e,fileName:t,className:s,onDownload:l,isDownloading:a=!1}){let n=t.split(".").pop()?.toLowerCase()||"";return(0,r.jsx)("div",{className:(0,w.cn)("flex flex-col items-center justify-center p-10",s),children:(0,r.jsxs)("div",{className:"flex flex-col items-center text-center max-w-md",children:[(0,r.jsxs)("div",{className:"relative mb-6",children:[(0,r.jsx)(j.A,{className:"h-24 w-24 text-muted-foreground/50"}),(0,r.jsx)("div",{className:"absolute bottom-1 right-1 bg-background rounded-sm px-1.5 py-0.5 text-xs font-medium text-muted-foreground border",children:n.toUpperCase()})]}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:t.split("/").pop()}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-6",children:"This binary file cannot be previewed in the browser"}),(0,r.jsxs)(i.$,{variant:"default",className:"min-w-[150px]",onClick:()=>{if(l)l();else if(e){console.log(`[BINARY RENDERER] Using fallback download for ${t}`);let s=document.createElement("a");s.href=e,s.download=t.split("/").pop()||"file",document.body.appendChild(s),s.click(),document.body.removeChild(s)}else console.error("[BINARY RENDERER] No download URL or handler available")},disabled:a,children:[a?(0,r.jsx)(o.A,{className:"h-4 w-4 mr-2 animate-spin"}):(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Download"]})]})})}var P=s(34318),D=s(80375),O=s(25334);function I({content:e,previewUrl:t,className:s}){let[a,n]=(0,l.useState)("preview");return(0,r.jsx)("div",{className:(0,w.cn)("w-full h-full flex flex-col",s),children:(0,r.jsxs)("div",{className:"flex-1 min-h-0 relative",children:[(0,r.jsxs)("div",{className:"absolute left-2 top-2 z-10 flex items-center gap-2",children:[(0,r.jsxs)(i.$,{variant:"ghost",size:"sm",className:(0,w.cn)("flex items-center gap-2 bg-background/80 backdrop-blur-sm hover:bg-background/90","preview"===a&&"bg-background/90"),onClick:()=>n("preview"),children:[(0,r.jsx)(P.A,{className:"h-4 w-4"}),"Preview"]}),(0,r.jsxs)(i.$,{variant:"ghost",size:"sm",className:(0,w.cn)("flex items-center gap-2 bg-background/80 backdrop-blur-sm hover:bg-background/90","code"===a&&"bg-background/90"),onClick:()=>n("code"),children:[(0,r.jsx)(D.A,{className:"h-4 w-4"}),"Code"]}),(0,r.jsxs)(i.$,{variant:"ghost",size:"sm",className:"flex items-center gap-2 bg-background/80 backdrop-blur-sm hover:bg-background/90",onClick:()=>window.open(t,"_blank"),children:[(0,r.jsx)(O.A,{className:"h-4 w-4"}),"Open"]})]}),"preview"===a?(0,r.jsx)("div",{className:"absolute inset-0",children:(0,r.jsx)("iframe",{src:t,title:"HTML Preview",className:"w-full h-full border-0",sandbox:"allow-same-origin allow-scripts"})}):(0,r.jsx)("div",{className:"absolute inset-0",children:(0,r.jsx)(k.$,{content:e,language:"html",className:"w-full h-full"})})]})})}var W=s(67314),U=s(21226);function V({content:e,binaryUrl:t,fileName:s,className:l,project:n,markdownRef:i,onDownload:o,isDownloading:c}){var d;let m=function(e){let t=e.split(".").pop()?.toLowerCase()||"";if(["md","markdown"].includes(t))return"markdown";if(["js","jsx","ts","tsx","html","css","json","py","python","java","c","cpp","h","cs","go","rs","php","rb","sh","bash","xml","yml","yaml","toml","sql","graphql","swift","kotlin","dart","r","lua","scala","perl","haskell","rust"].includes(t))return"code";if(["png","jpg","jpeg","gif","webp","svg","bmp","ico"].includes(t))return"image";if(["pdf"].includes(t))return"pdf";if(["csv","tsv"].includes(t))return"csv";else if(["txt","log","env","ini"].includes(t))return"text";else return"binary"}(s),x=(d=s,({js:"javascript",jsx:"jsx",ts:"typescript",tsx:"tsx",html:"html",css:"css",json:"json",py:"python",python:"python",java:"java",c:"c",cpp:"cpp",h:"c",cs:"csharp",go:"go",rs:"rust",php:"php",rb:"ruby",sh:"shell",bash:"shell",xml:"xml",yml:"yaml",yaml:"yaml",sql:"sql"})[d.split(".").pop()?.toLowerCase()||""]||""),u=s.toLowerCase().endsWith(".html"),h=a().useMemo(()=>{if(u&&e&&!n?.sandbox?.sandbox_url){let t=new Blob([e],{type:"text/html"});return URL.createObjectURL(t)}},[u,e,n?.sandbox?.sandbox_url]),p=u&&n?.sandbox?.sandbox_url&&s?(0,W.i)(n.sandbox.sandbox_url,s):h;return(0,r.jsx)("div",{className:(0,w.cn)("w-full h-full",l),children:"binary"===m?(0,r.jsx)(F,{url:t||"",fileName:s,onDownload:o,isDownloading:c}):"image"===m&&t?(0,r.jsx)(L,{url:t}):"pdf"===m&&t?(0,r.jsx)(C,{url:t}):"markdown"===m?(0,r.jsx)(v.T,{content:e||"",ref:i}):"csv"===m?(0,r.jsx)(U.W,{content:e||""}):u?(0,r.jsx)(I,{content:e||"",previewUrl:p||"",className:"w-full h-full"}):"code"===m||"text"===m?(0,r.jsx)(k.$,{content:e||"",language:x,className:"w-full h-full"}):(0,r.jsx)("div",{className:"w-full h-full p-4",children:(0,r.jsx)("pre",{className:"text-sm font-mono whitespace-pre-wrap break-words leading-relaxed bg-muted/30 p-4 rounded-lg overflow-auto max-h-full",children:e||""})})})}var J=s(62185),B=s(52581),M=s(79481),q=s(58297),Z=s(21342),G=s(24723),H=s(94084),Q=s.n(H),X=s(12392);function Y({open:e,onOpenChange:t,sandboxId:s,initialFilePath:a,project:w,filePathList:v}){let{session:k}=(0,q.A)(),[y,z]=(0,l.useState)("/workspace"),[S,C]=(0,l.useState)(!0),[A,E]=(0,l.useState)(-1),_=!!(v&&v.length>0),{data:$=[],isLoading:T,error:R,refetch:L}=(0,G.R2)(s,y,{enabled:e&&!!s,staleTime:3e4});(0,l.useRef)(null);let[F,P]=(0,l.useState)(null),[D,O]=(0,l.useState)(null),[I,W]=(0,l.useState)(null),[U,H]=(0,l.useState)(null),[Y,K]=(0,l.useState)(null),{data:ee,isLoading:et,error:es}=(0,G.x2)(s,F||void 0,{enabled:!!F,staleTime:3e5}),[er,el]=(0,l.useState)(!1),[ea,en]=(0,l.useState)(!1),ei=(0,l.useRef)(null),[eo,ec]=(0,l.useState)(!1),[ed,em]=(0,l.useState)(w),[ex,eu]=(0,l.useState)(!1),eh=(0,l.useRef)(null),ep=(0,l.useRef)(new Set),[ef,eg]=(0,l.useState)(!1),[eb,ej]=(0,l.useState)(null),eN=(0,l.useCallback)(e=>"string"==typeof e&&e?e.startsWith("/workspace")?e:`/workspace/${e.replace(/^\//,"")}`:(console.warn("[FILE VIEWER] normalizePath received non-string or empty value:",e,"Returning '/workspace'"),"/workspace"),[]),ew=(0,l.useCallback)(async(e="/workspace")=>{let t=[],r=0,l=new Set,a=async e=>{if(!l.has(e)){l.add(e);try{for(let l of(console.log(`[DOWNLOAD ALL] Exploring directory: ${e}`),await (0,J.XY)(s,e)))l.is_dir?await a(l.path):(t.push(l),r+=l.size||0)}catch(t){console.error(`[DOWNLOAD ALL] Error exploring directory ${e}:`,t),B.oR.error(`Failed to read directory: ${e}`)}}};return await a(e),console.log(`[DOWNLOAD ALL] Discovered ${t.length} files, total size: ${r} bytes`),{files:t,totalSize:r}},[s]),ev=(0,l.useCallback)(async()=>{if(k?.access_token&&!ef)try{eg(!0),ej({current:0,total:0,currentFile:"Discovering files..."});let{files:e}=await ew();if(0===e.length)return void B.oR.error("No files found to download");console.log(`[DOWNLOAD ALL] Starting download of ${e.length} files`);let t=new(Q());ej({current:0,total:e.length,currentFile:"Creating archive..."});for(let r=0;r<e.length;r++){let l=e[r],a=l.path.replace(/^\/workspace\//,"");ej({current:r+1,total:e.length,currentFile:a});try{let n=G.pH.getContentTypeFromPath(l.path),i=`${s}:${l.path}:${n}`,o=G.pH.get(i);if(!o){console.log(`[DOWNLOAD ALL] Loading file from server: ${l.path}`);let e=await fetch(`http://localhost:8000/api/sandboxes/${s}/files/content?path=${encodeURIComponent(l.path)}`,{headers:{Authorization:`Bearer ${k.access_token}`}});if(!e.ok){console.warn(`[DOWNLOAD ALL] Failed to load file: ${l.path} (${e.status})`);continue}o="blob"===n?await e.blob():"json"===n?JSON.stringify(await e.json(),null,2):await e.text(),G.pH.set(i,o)}if(o instanceof Blob)t.file(a,o);else if("string"==typeof o)if(o.startsWith("blob:"))try{let e=await fetch(o),s=await e.blob();t.file(a,s)}catch(r){console.warn(`[DOWNLOAD ALL] Failed to fetch blob content for: ${l.path}`,r);let e=await fetch(`http://localhost:8000/api/sandboxes/${s}/files/content?path=${encodeURIComponent(l.path)}`,{headers:{Authorization:`Bearer ${k.access_token}`}});if(e.ok){let s=await e.blob();t.file(a,s)}}else t.file(a,o);else t.file(a,JSON.stringify(o,null,2));console.log(`[DOWNLOAD ALL] Added to zip: ${a} (${r+1}/${e.length})`)}catch(e){console.error(`[DOWNLOAD ALL] Error processing file ${l.path}:`,e)}}ej({current:e.length,total:e.length,currentFile:"Generating zip file..."}),console.log("[DOWNLOAD ALL] Generating zip file...");let r=await t.generateAsync({type:"blob",compression:"DEFLATE",compressionOptions:{level:6}}),l=URL.createObjectURL(r),a=document.createElement("a");a.href=l,a.download=`workspace-${s}-${new Date().toISOString().slice(0,10)}.zip`,document.body.appendChild(a),a.click(),document.body.removeChild(a),setTimeout(()=>URL.revokeObjectURL(l),1e4),B.oR.success(`Downloaded ${e.length} files as zip archive`),console.log(`[DOWNLOAD ALL] Successfully created zip with ${e.length} files`)}catch(e){console.error("[DOWNLOAD ALL] Error creating zip:",e),B.oR.error(`Failed to create zip archive: ${e instanceof Error?e.message:String(e)}`)}finally{eg(!1),ej(null)}},[s,k?.access_token,ef,ew]),ek=(0,l.useCallback)(()=>{console.log(`[FILE VIEWER DEBUG] clearSelectedFile called, isFileListMode: ${_}`),P(null),O(null),W(null),H(null),K(null),_?console.log("[FILE VIEWER DEBUG] Keeping currentFileIndex in clearSelectedFile because in file list mode"):(console.log("[FILE VIEWER DEBUG] Resetting currentFileIndex in clearSelectedFile"),E(-1))},[_]),ey=(0,l.useCallback)(async e=>{if(e.is_dir){let t=eN(e.path);console.log(`[FILE VIEWER] Navigating to folder: ${e.path} → ${t}`),ek(),z(t);return}if(F===e.path)return void console.log(`[FILE VIEWER] File already selected: ${e.path}`);console.log(`[FILE VIEWER] Opening file: ${e.path}`);let t=G.pH.isImageFile(e.path),s=G.pH.isPdfFile(e.path),r=e.path.split(".").pop()?.toLowerCase(),l=["xlsx","xls","docx","doc","pptx","ppt"].includes(r||"");t?console.log(`[FILE VIEWER][IMAGE DEBUG] Opening image file: ${e.path}`):s?console.log(`[FILE VIEWER] Opening PDF file: ${e.path}`):l&&console.log(`[FILE VIEWER] Opening Office document: ${e.path} (${r})`),ek(),P(e.path),_&&v?.includes(e.path)?console.log("[FILE VIEWER DEBUG] Keeping currentFileIndex because file is in file list mode"):(console.log("[FILE VIEWER DEBUG] Resetting currentFileIndex because not in file list mode or file not in list"),E(-1))},[F,ek,eN,_,v]),ez=(0,l.useCallback)(e=>{if(!e.is_dir)return;let t=eN(e.path);console.log(`[FILE VIEWER] Navigating to folder: ${e.path} → ${t}`),console.log(`[FILE VIEWER] Current path before navigation: ${y}`),ek(),z(t)},[eN,ek,y]),eS=(0,l.useCallback)(e=>{let t=eN(e);console.log(`[FILE VIEWER] Navigating to breadcrumb path: ${e} → ${t}`),ek(),z(t)},[eN,ek]),eC=(0,l.useCallback)(()=>{console.log("[FILE VIEWER] Navigating home from:",y),ek(),z("/workspace")},[ek,y]),eA=(0,l.useCallback)(e=>{let t=eN(e).replace(/^\/workspace\/?/,"");if(!t)return[];let s=t.split("/").filter(Boolean),r="/workspace";return s.map((e,t)=>(r=`${r}/${e}`,{name:e,path:r,isLast:t===s.length-1}))},[eN]);(0,l.useCallback)(e=>{let t=e;t.startsWith("/workspace")||(t=`/workspace/${t.startsWith("/")?t.substring(1):t}`);let r=G.pH.getContentTypeFromPath(e),l=`${s}:${t}:${r}`;if(console.log(`[FILE VIEWER] Checking cache for key: ${l}`),G.pH.has(l)){let e=G.pH.get(l);return console.log(`[FILE VIEWER] Direct cache hit for ${t} (${r})`),{found:!0,content:e,contentType:r}}return console.log(`[FILE VIEWER] Cache miss for key: ${l}`),{found:!1,content:null,contentType:r}},[s]);let eE=(0,l.useCallback)(e=>{if(console.log("[FILE VIEWER DEBUG] navigateToFileByIndex called:",{index:e,isFileListMode:_,filePathList:v,filePathListLength:v?.length}),!_||!v||e<0||e>=v.length)return void console.log("[FILE VIEWER DEBUG] navigateToFileByIndex early return - invalid conditions");let t=v[e];console.log("[FILE VIEWER DEBUG] Setting currentFileIndex to:",e,"for file:",t),E(e),ey({name:t.split("/").pop()||"",path:eN(t),is_dir:!1,size:0,mod_time:new Date().toISOString()})},[_,v,eN,ey]),e_=(0,l.useCallback)(()=>{A>0&&eE(A-1)},[A,eE]),e$=(0,l.useCallback)(()=>{_&&v&&A<v.length-1&&eE(A+1)},[A,_,v,eE]),eT=(0,l.useCallback)(e=>{e||(console.log("[FILE VIEWER] handleOpenChange: Modal closing, resetting state."),!U||ea||ep.current.has(U)||(console.log(`[FILE VIEWER] Manually revoking blob URL on modal close: ${U}`),URL.revokeObjectURL(U)),ek(),z("/workspace"),ec(!1),C(!0),E(-1),eg(!1),ej(null)),t(e)},[t,ek,C,U,ea]),eR=(0,l.useCallback)(e=>!!e&&e.toLowerCase().endsWith(".md"),[]),eL=(0,l.useCallback)(async(e="portrait")=>{if(F&&!ex&&eR(F)){eu(!0);try{if(!eh.current)throw Error("Markdown content not found");let t=window.open("","_blank");if(!t)throw Error("Unable to open print window. Please check if popup blocker is enabled.");window.location.origin;let s=(F.split("/").pop()||"document").replace(/\.md$/,""),r=eh.current.innerHTML,l=`
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <title>${s}</title>
          <style>
            @media print {
              @page { 
                size: ${"landscape"===e?"A4 landscape":"A4"};
                margin: 15mm;
              }
              body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
              }
            }
            body {
              font-family: 'Helvetica', 'Arial', sans-serif;
              font-size: 12pt;
              color: #333;
              line-height: 1.5;
              padding: 20px;
              max-width: 100%;
              margin: 0 auto;
              background: white;
            }
            h1 { font-size: 24pt; margin-top: 20pt; margin-bottom: 12pt; }
            h2 { font-size: 20pt; margin-top: 18pt; margin-bottom: 10pt; }
            h3 { font-size: 16pt; margin-top: 16pt; margin-bottom: 8pt; }
            h4, h5, h6 { font-weight: bold; margin-top: 12pt; margin-bottom: 6pt; }
            p { margin: 8pt 0; }
            pre, code {
              font-family: 'Courier New', monospace;
              background-color: #f5f5f5;
              border-radius: 3pt;
              padding: 2pt 4pt;
              font-size: 10pt;
            }
            pre {
              padding: 8pt;
              margin: 8pt 0;
              overflow-x: auto;
              white-space: pre-wrap;
            }
            code {
              white-space: pre-wrap;
            }
            img {
              max-width: 100%;
              height: auto;
            }
            a {
              color: #0066cc;
              text-decoration: underline;
            }
            ul, ol {
              padding-left: 20pt;
              margin: 8pt 0;
            }
            blockquote {
              margin: 8pt 0;
              padding-left: 12pt;
              border-left: 4pt solid #ddd;
              color: #666;
            }
            table {
              border-collapse: collapse;
              width: 100%;
              margin: 12pt 0;
            }
            th, td {
              border: 1pt solid #ddd;
              padding: 6pt;
              text-align: left;
            }
            th {
              background-color: #f5f5f5;
              font-weight: bold;
            }
            /* Syntax highlighting basic styles */
            .hljs-keyword, .hljs-selector-tag { color: #569cd6; }
            .hljs-literal, .hljs-number { color: #b5cea8; }
            .hljs-string { color: #ce9178; }
            .hljs-comment { color: #6a9955; }
            .hljs-attribute, .hljs-attr { color: #9cdcfe; }
            .hljs-function, .hljs-name { color: #dcdcaa; }
            .hljs-title.class_ { color: #4ec9b0; }
            .markdown-content pre { background-color: #f8f8f8; }
          </style>
        </head>
        <body>
          <div class="markdown-content">
            ${r}
          </div>
          <script>
            // Remove any complex CSS variables or functions that might cause issues
            document.querySelectorAll('[style]').forEach(el => {
              const style = el.getAttribute('style');
              if (style && (style.includes('oklch') || style.includes('var(--') || style.includes('hsl('))) {
                // Replace complex color values with simple ones or remove them
                el.setAttribute('style', style
                  .replace(/color:.*?(;|$)/g, 'color: #333;')
                  .replace(/background-color:.*?(;|$)/g, 'background-color: transparent;')
                );
              }
            });
            
            // Print automatically when loaded
            window.onload = () => {
              setTimeout(() => {
                window.print();
                setTimeout(() => window.close(), 500);
              }, 300);
            };
          </script>
        </body>
        </html>
      `;t.document.open(),t.document.write(l),t.document.close(),B.oR.success("PDF export initiated. Check your print dialog.")}catch(e){console.error("PDF export failed:",e),B.oR.error(`Failed to export PDF: ${e instanceof Error?e.message:String(e)}`)}finally{eu(!1)}}},[F,ex,eR]),eF=async()=>{if(F&&!ea)try{en(!0);let e=F.split("/").pop()||"file",t=G.pH.getMimeTypeFromPath?.(F)||"application/octet-stream";if(D){let r;if("string"==typeof D)if(D.startsWith("blob:")){let e=await fetch(`http://localhost:8000/api/sandboxes/${s}/files/content?path=${encodeURIComponent(F)}`,{headers:{Authorization:`Bearer ${k?.access_token}`}});if(!e.ok)throw Error(`Server error: ${e.status}`);r=await e.blob()}else r=new Blob([D],{type:t});else r=D instanceof Blob?D:new Blob([JSON.stringify(D)],{type:"application/json"});r.type!==t&&(r=new Blob([r],{type:t})),eP(r,e);return}let r=await fetch(`http://localhost:8000/api/sandboxes/${s}/files/content?path=${encodeURIComponent(F)}`,{headers:{Authorization:`Bearer ${k?.access_token}`}});if(!r.ok)throw Error(`Server error: ${r.status}`);let l=await r.blob(),a=new Blob([l],{type:t});eP(a,e)}catch(e){console.error("[FILE VIEWER] Download error:",e),B.oR.error(`Failed to download file: ${e instanceof Error?e.message:String(e)}`)}finally{en(!1)}},eP=(e,t)=>{let s=URL.createObjectURL(e),r=document.createElement("a");r.href=s,r.download=t,document.body.appendChild(r),r.click(),document.body.removeChild(r),ep.current.add(s),setTimeout(()=>{URL.revokeObjectURL(s),ep.current.delete(s)},1e4),B.oR.success("Download started")},eD=(0,l.useCallback)(()=>{ei.current&&ei.current.click()},[]),eO=(0,l.useCallback)(async e=>{if(!e.target.files||0===e.target.files.length)return;let t=e.target.files[0];el(!0);try{let e=(0,X.L)(t.name),r=`${y}/${e}`,l=new FormData;l.append("file",t,e),l.append("path",r);let a=(0,M.U)(),{data:{session:n}}=await a.auth.getSession();if(!n?.access_token)throw Error("No access token available");let i=await fetch(`http://localhost:8000/api/sandboxes/${s}/files`,{method:"POST",headers:{Authorization:`Bearer ${n.access_token}`},body:l});if(!i.ok){let e=await i.text();throw Error(e||"Upload failed")}await L(),B.oR.success(`Uploaded: ${e}`)}catch(e){console.error("Upload failed:",e),B.oR.error(`Upload failed: ${e instanceof Error?e.message:String(e)}`)}finally{el(!1),e.target&&(e.target.value="")}},[y,s,L]);return(0,r.jsx)(n.lG,{open:e,onOpenChange:eT,children:(0,r.jsxs)(n.Cf,{className:"sm:max-w-[90vw] md:max-w-[1200px] w-[95vw] h-[90vh] max-h-[900px] flex flex-col p-0 gap-0 overflow-hidden",children:[(0,r.jsxs)(n.c7,{className:"px-4 py-2 border-b flex-shrink-0 flex flex-row gap-4 items-center",children:[(0,r.jsx)(n.L3,{className:"text-lg font-semibold",children:"Workspace Files"}),eb&&(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-muted-foreground",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(o.A,{className:"h-3 w-3 animate-spin"}),(0,r.jsx)("span",{children:eb.total>0?`${eb.current}/${eb.total}`:"Preparing..."})]}),(0,r.jsx)("span",{className:"max-w-[200px] truncate",children:eb.currentFile})]}),(0,r.jsx)("div",{className:"flex items-center gap-2",children:(console.log("[FILE VIEWER DEBUG] Navigation visibility check:",{isFileListMode:_,selectedFilePath:F,filePathList:v,filePathListLength:v?.length,currentFileIndex:A,shouldShow:_&&F&&v&&v.length>1&&A>=0}),_&&F&&v&&v.length>1&&A>=0&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(i.$,{variant:"outline",size:"sm",onClick:e_,disabled:A<=0,className:"h-8 w-8 p-0",title:"Previous file (←)",children:(0,r.jsx)(c.A,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{className:"text-xs text-muted-foreground px-1",children:[A+1," / ",v?.length||0]}),(0,r.jsx)(i.$,{variant:"outline",size:"sm",onClick:e$,disabled:A>=(v?.length||0)-1,className:"h-8 w-8 p-0",title:"Next file (→)",children:(0,r.jsx)(d.A,{className:"h-4 w-4"})})]}))})]}),(0,r.jsxs)("div",{className:"px-4 py-2 border-b flex items-center gap-2",children:[(0,r.jsx)(i.$,{variant:"ghost",size:"icon",onClick:eC,className:"h-8 w-8",title:"Go to home directory",children:(0,r.jsx)(m.A,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{className:"flex items-center overflow-x-auto flex-1 min-w-0 scrollbar-hide whitespace-nowrap",children:[(0,r.jsx)(i.$,{variant:"ghost",size:"sm",className:"h-7 px-2 text-sm font-medium min-w-fit flex-shrink-0",onClick:eC,children:"home"}),"/workspace"!==y&&(0,r.jsx)(r.Fragment,{children:eA(y).map(e=>(0,r.jsxs)(l.Fragment,{children:[(0,r.jsx)(d.A,{className:"h-4 w-4 mx-1 text-muted-foreground opacity-50 flex-shrink-0"}),(0,r.jsx)(i.$,{variant:"ghost",size:"sm",className:"h-7 px-2 text-sm font-medium truncate max-w-[200px]",onClick:()=>eS(e.path),children:e.name})]},e.path))}),F&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.A,{className:"h-4 w-4 mx-1 text-muted-foreground opacity-50 flex-shrink-0"}),(0,r.jsx)("div",{className:"flex items-center gap-2",children:(0,r.jsx)("span",{className:"text-sm font-medium truncate",children:F.split("/").pop()})})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 flex-shrink-0",children:[F&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(i.$,{variant:"outline",size:"sm",onClick:eF,disabled:ea||et,className:"h-8 gap-1",children:[ea?(0,r.jsx)(o.A,{className:"h-4 w-4 animate-spin"}):(0,r.jsx)(x.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Download"})]}),eR(F)&&(0,r.jsxs)(Z.rI,{children:[(0,r.jsx)(Z.ty,{asChild:!0,children:(0,r.jsxs)(i.$,{variant:"outline",size:"sm",disabled:ex||et||null!==Y,className:"h-8 gap-1",children:[ex?(0,r.jsx)(o.A,{className:"h-4 w-4 animate-spin"}):(0,r.jsx)(u.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Export as PDF"}),(0,r.jsx)(h.A,{className:"h-3 w-3 ml-1"})]})}),(0,r.jsxs)(Z.SQ,{align:"end",children:[(0,r.jsxs)(Z._2,{onClick:()=>eL("portrait"),className:"flex items-center gap-2 cursor-pointer",children:[(0,r.jsx)("span",{className:"rotate-90",children:"⬌"})," Portrait"]}),(0,r.jsxs)(Z._2,{onClick:()=>eL("landscape"),className:"flex items-center gap-2 cursor-pointer",children:[(0,r.jsx)("span",{children:"⬌"})," Landscape"]})]})]})]}),!F&&(0,r.jsxs)(r.Fragment,{children:["/workspace"===y&&(0,r.jsxs)(i.$,{variant:"outline",size:"sm",onClick:ev,disabled:ef||T,className:"h-8 gap-1",children:[ef?(0,r.jsx)(o.A,{className:"h-4 w-4 animate-spin"}):(0,r.jsx)(p.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Download All"})]}),(0,r.jsxs)(i.$,{variant:"outline",size:"sm",onClick:eD,disabled:er,className:"h-8 gap-1",children:[er?(0,r.jsx)(o.A,{className:"h-4 w-4 animate-spin"}):(0,r.jsx)(f.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"hidden sm:inline",children:"Upload"})]})]}),(0,r.jsx)("input",{type:"file",ref:ei,className:"hidden",onChange:eO,disabled:er})]})]}),(0,r.jsx)("div",{className:"flex-1 overflow-hidden",children:F?(0,r.jsx)("div",{className:"h-full w-full overflow-auto",children:et?(0,r.jsxs)("div",{className:"h-full w-full flex flex-col items-center justify-center",children:[(0,r.jsx)(o.A,{className:"h-8 w-8 animate-spin text-primary mb-3"}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Loading file",F?`: ${F.split("/").pop()}`:"..."]}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground/70 mt-1",children:(()=>{if(!F)return"Preparing...";let e=F;e.startsWith("/workspace")||(e=`/workspace/${e.startsWith("/")?e.substring(1):e}`);let t=G.pH.getContentTypeFromPath(e);return G.pH.has(`${s}:${e}:${t}`)?"Using cached version":"Fetching from server"})()})]}):Y?(0,r.jsx)("div",{className:"h-full w-full flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"max-w-md p-6 text-center border rounded-lg bg-muted/10",children:[(0,r.jsx)(g.A,{className:"h-10 w-10 text-orange-500 mx-auto mb-4"}),(0,r.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Error Loading File"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:Y}),(0,r.jsxs)("div",{className:"flex justify-center gap-3",children:[(0,r.jsx)(i.$,{onClick:()=>{K(null),ey({path:F,name:F.split("/").pop()||"",is_dir:!1,size:0,mod_time:new Date().toISOString()})},children:"Retry"}),(0,r.jsx)(i.$,{variant:"outline",onClick:()=>{ek()},children:"Back to Files"})]})]})}):(0,r.jsx)("div",{className:"h-full w-full relative",children:(()=>{let e=G.pH.isImageFile(F),t=G.pH.isPdfFile(F),s=["xlsx","xls","docx","doc","pptx","ppt"].includes(F?.split(".").pop()?.toLowerCase()||""),l=e||t||s;return l&&!U?(0,r.jsx)("div",{className:"h-full w-full flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Loading ",t?"PDF":e?"image":"file","..."]})}):(0,r.jsx)(V,{content:l?null:I,binaryUrl:U,fileName:F,className:"h-full w-full",project:ed,markdownRef:eR(F)?eh:void 0,onDownload:eF,isDownloading:ea},F)})()})}):(0,r.jsx)("div",{className:"h-full w-full",children:T?(0,r.jsx)("div",{className:"h-full w-full flex items-center justify-center",children:(0,r.jsx)(o.A,{className:"h-6 w-6 animate-spin text-primary"})}):0===$.length?(0,r.jsxs)("div",{className:"h-full w-full flex flex-col items-center justify-center",children:[(0,r.jsx)(b.A,{className:"h-12 w-12 mb-2 text-muted-foreground opacity-30"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Directory is empty"})]}):(0,r.jsx)(N.F,{className:"h-full w-full p-2",children:(0,r.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-3 p-4",children:$.map(e=>(0,r.jsxs)("button",{className:`flex flex-col items-center p-3 rounded-2xl border hover:bg-muted/50 transition-colors ${F===e.path?"bg-muted border-primary/20":""}`,onClick:()=>{e.is_dir?(console.log(`[FILE VIEWER] Folder clicked: ${e.name}, path: ${e.path}`),ez(e)):ey(e)},children:[(0,r.jsx)("div",{className:"w-12 h-12 flex items-center justify-center mb-1",children:e.is_dir?(0,r.jsx)(b.A,{className:"h-9 w-9 text-blue-500"}):(0,r.jsx)(j.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,r.jsx)("span",{className:"text-xs text-center font-medium truncate max-w-full",children:e.name})]},e.path))})})})})]})})}},8065:(e,t,s)=>{s.d(t,{F:()=>v});var r=s(60687);s(43210);var l=s(41312),a=s(33872),n=s(32192),i=s(71057),o=s(25541),c=s(57800),d=s(11437),m=s(5336),x=s(43649),u=s(41862),h=s(83992),p=s(14952),f=s(14296),g=s(4780),b=s(44493),j=s(96834),N=s(51555);let w={linkedin:{name:"LinkedIn Data Provider",icon:l.A,color:"from-blue-500 to-blue-600",bgColor:"bg-blue-50 dark:bg-blue-900/20",textColor:"text-blue-700 dark:text-blue-300"},twitter:{name:"Twitter Data Provider",icon:a.A,color:"from-sky-400 to-sky-500",bgColor:"bg-sky-50 dark:bg-sky-900/20",textColor:"text-sky-700 dark:text-sky-300"},zillow:{name:"Zillow Data Provider",icon:n.A,color:"from-emerald-500 to-emerald-600",bgColor:"bg-emerald-50 dark:bg-emerald-900/20",textColor:"text-emerald-700 dark:text-emerald-300"},amazon:{name:"Amazon Data Provider",icon:i.A,color:"from-orange-500 to-orange-600",bgColor:"bg-orange-50 dark:bg-orange-900/20",textColor:"text-orange-700 dark:text-orange-300"},yahoo_finance:{name:"Yahoo Finance Data Provider",icon:o.A,color:"from-purple-500 to-purple-600",bgColor:"bg-purple-50 dark:bg-purple-900/20",textColor:"text-purple-700 dark:text-purple-300"},active_jobs:{name:"Active Jobs Data Provider",icon:c.A,color:"from-indigo-500 to-indigo-600",bgColor:"bg-indigo-50 dark:bg-indigo-900/20",textColor:"text-indigo-700 dark:text-indigo-300"}};function v({assistantContent:e,toolContent:t,assistantTimestamp:s,toolTimestamp:l,isSuccess:a=!0,isStreaming:n=!1}){let{serviceName:i,endpoints:o,actualIsSuccess:c,actualToolTimestamp:v,actualAssistantTimestamp:k}=(0,N.$)(e,t,a,l,s),y=i&&w[i]?w[i]:w.linkedin,z=y.icon,S=o&&"object"==typeof o?Object.keys(o).length:0;return(0,r.jsxs)(b.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,r.jsx)(b.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,r.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"relative p-2 rounded-xl bg-gradient-to-br from-blue-500/20 to-blue-600/10 border border-blue-500/20",children:(0,r.jsx)(d.A,{className:"w-5 h-5 text-blue-500 dark:text-blue-400"})}),(0,r.jsx)("div",{children:(0,r.jsx)(b.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:"Data Provider"})})]}),!n&&(0,r.jsxs)(j.E,{variant:"secondary",className:(0,g.cn)("text-xs font-medium",c?"bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-900/20 dark:text-emerald-300 dark:border-emerald-800":"bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800"),children:[c?(0,r.jsx)(m.A,{className:"h-3 w-3 mr-1"}):(0,r.jsx)(x.A,{className:"h-3 w-3 mr-1"}),c?"Loaded":"Failed"]})]})}),(0,r.jsx)(b.Wu,{className:"p-0 h-full flex-1 overflow-hidden relative",children:n?(0,r.jsx)("div",{className:"flex flex-col items-center justify-center h-full py-8 px-6",children:(0,r.jsxs)("div",{className:"text-center w-full max-w-xs",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-xl mx-auto mb-4 flex items-center justify-center bg-zinc-100 dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700",children:(0,r.jsx)(u.A,{className:"h-8 w-8 animate-spin text-zinc-500 dark:text-zinc-400"})}),(0,r.jsx)("h3",{className:"text-base font-medium text-zinc-900 dark:text-zinc-100 mb-2",children:"Loading provider..."}),(0,r.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400",children:"Connecting to data source"})]})}):(0,r.jsxs)("div",{className:"p-4 space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 p-4 bg-zinc-50 dark:bg-zinc-900/50 rounded-lg border border-zinc-200 dark:border-zinc-800",children:[(0,r.jsx)("div",{className:(0,g.cn)("w-12 h-12 rounded-lg flex items-center justify-center shadow-sm border-2",`bg-gradient-to-br ${y.color}`,"border-white/20"),children:(0,r.jsx)(z,{className:"h-6 w-6 text-white drop-shadow-sm"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-zinc-900 dark:text-zinc-100",children:y.name}),(0,r.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400",children:S>0?`${S} endpoints loaded and ready`:"Endpoints loaded and ready"})]}),(0,r.jsxs)(j.E,{variant:"secondary",className:(0,g.cn)("text-xs font-medium",c?"bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-900/20 dark:text-emerald-300 dark:border-emerald-800":"bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800"),children:[c?(0,r.jsx)(m.A,{className:"h-3 w-3 mr-1"}):(0,r.jsx)(x.A,{className:"h-3 w-3 mr-1"}),c?"Connected":"Failed"]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-zinc-700 dark:text-zinc-300",children:[(0,r.jsx)(h.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Provider Status"}),(0,r.jsx)(p.A,{className:"h-3 w-3 text-zinc-400"})]}),(0,r.jsxs)("div",{className:"grid gap-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 rounded-full bg-emerald-500"}),(0,r.jsx)("span",{className:"text-sm font-medium text-zinc-900 dark:text-zinc-100",children:"Connection Status"})]}),(0,r.jsxs)(j.E,{variant:"secondary",className:(0,g.cn)("text-xs font-medium",c?"bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-900/20 dark:text-emerald-300 dark:border-emerald-800":"bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800"),children:[c?(0,r.jsx)(m.A,{className:"h-3 w-3 mr-1"}):(0,r.jsx)(x.A,{className:"h-3 w-3 mr-1"}),c?"Active":"Inactive"]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 rounded-full bg-blue-500"}),(0,r.jsx)("span",{className:"text-sm font-medium text-zinc-900 dark:text-zinc-100",children:"Endpoints Available"})]}),(0,r.jsx)(j.E,{variant:"outline",className:"text-xs",children:S>0?`${S} endpoints`:"Ready"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 rounded-full bg-purple-500"}),(0,r.jsx)("span",{className:"text-sm font-medium text-zinc-900 dark:text-zinc-100",children:"Data Provider"})]}),(0,r.jsx)("span",{className:"text-sm text-zinc-600 dark:text-zinc-400 font-mono",children:i||"linkedin"})]})]}),c&&(0,r.jsxs)("div",{className:"p-4 bg-emerald-50 dark:bg-emerald-900/10 rounded-lg border border-emerald-200 dark:border-emerald-800/50",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 text-emerald-600 dark:text-emerald-400/70"}),(0,r.jsx)("span",{className:"text-sm font-medium text-emerald-800 dark:text-emerald-300/70",children:"Provider Ready"})]}),(0,r.jsx)("p",{className:"text-xs text-emerald-700 dark:text-emerald-300/70",children:"Data provider endpoints have been loaded successfully and are ready to process requests."})]})]})]})}),(0,r.jsxs)("div",{className:"px-4 py-2 h-10 bg-zinc-50/50 dark:bg-zinc-900/50 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,r.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:!n&&(0,r.jsxs)(j.E,{variant:"outline",className:"h-6 py-0.5 text-xs",children:[(0,r.jsx)(z,{className:"h-3 w-3 mr-1"}),y.name.split(" ")[0]]})}),(0,r.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:v&&!n?(0,f.Ey)(v):k?(0,f.Ey)(k):""})]})]})}},11953:(e,t,s)=>{s.d(t,{t:()=>k});var r=s(60687),l=s(43210),a=s(11437),n=s(5336),i=s(43649),o=s(41862),c=s(60),d=s(82080),m=s(10022),x=s(13964),u=s(70615),h=s(14296),p=s(4780),f=s(10218),g=s(44493),b=s(96834),j=s(29523),N=s(23562),w=s(42692),v=s(76242);function k({name:e="crawl-webpage",assistantContent:t,toolContent:s,assistantTimestamp:k,toolTimestamp:y,isSuccess:z=!0,isStreaming:S=!1}){let{resolvedTheme:C}=(0,f.D)(),[A,E]=(0,l.useState)(0),[_,$]=(0,l.useState)(!1),T=(0,h.sD)(t),R=(0,h.sD)(s),L=null;T.toolResult?L=T.url:R.toolResult&&(L=R.url),L||(L=(0,h.m$)(t));let F=(0,h._C)(s),P=(0,h.Bs)(e),D=L?(e=>{try{return new URL(e).hostname.replace("www.","")}catch(t){return e}})(L):"Unknown",O=L?(e=>{try{let t=new URL(e).hostname;return`https://www.google.com/s2/favicons?domain=${t}&sz=128`}catch(e){return null}})(L):null,I=async()=>{if(F?.text)try{await navigator.clipboard.writeText(F.text),$(!0),setTimeout(()=>$(!1),2e3)}catch(e){console.error("Failed to copy:",e)}},W=F?.text?(e=>{let t=e.trim().split(/\s+/).length;return{wordCount:t,charCount:e.length,lineCount:e.split("\n").length}})(F.text):null;return(0,r.jsxs)(g.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,r.jsx)(g.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,r.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"relative p-2 rounded-lg bg-gradient-to-br from-primary/20 to-primary/10 border border-primary/20",children:(0,r.jsx)(a.A,{className:"w-5 h-5 text-primary"})}),(0,r.jsx)("div",{children:(0,r.jsx)(g.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:P})})]}),!S&&(0,r.jsxs)(b.E,{variant:"secondary",className:z?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[z?(0,r.jsx)(n.A,{className:"h-3.5 w-3.5"}):(0,r.jsx)(i.A,{className:"h-3.5 w-3.5"}),z?"Crawling completed":"Crawling failed"]})]})}),(0,r.jsx)(g.Wu,{className:"p-0 h-full flex-1 overflow-hidden relative",children:S?(0,r.jsx)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:(0,r.jsxs)("div",{className:"text-center w-full max-w-xs",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-full mx-auto mb-6 flex items-center justify-center bg-gradient-to-br from-primary/20 to-primary/10 border border-primary/20",children:(0,r.jsx)(o.A,{className:"h-8 w-8 animate-spin text-primary"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-zinc-900 dark:text-zinc-100 mb-2",children:"Crawling Webpage"}),(0,r.jsxs)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400 mb-6",children:["Fetching content from ",(0,r.jsx)("span",{className:"font-mono text-xs break-all",children:D})]}),(0,r.jsx)(N.k,{value:A,className:"w-full h-1"}),(0,r.jsxs)("p",{className:"text-xs text-zinc-400 dark:text-zinc-500 mt-2",children:[A,"% complete"]})]})}):L?(0,r.jsx)(w.F,{className:"h-full w-full",children:(0,r.jsxs)("div",{className:"p-4 py-0 my-4",children:[(0,r.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-zinc-800 dark:text-zinc-200",children:[(0,r.jsx)(a.A,{className:"w-4 h-4 text-zinc-500 dark:text-zinc-400"}),"Source URL"]}),(0,r.jsx)("div",{className:"group relative",children:(0,r.jsxs)("div",{className:"flex items-center gap-3 p-4 bg-zinc-50 dark:bg-zinc-900 hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors rounded-xl border border-zinc-200 dark:border-zinc-800",children:[O&&(0,r.jsx)("img",{src:O,alt:"",className:"w-6 h-6 rounded-md flex-shrink-0",onError:e=>{e.target.style.display="none"}}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"font-mono text-sm text-zinc-900 dark:text-zinc-100 truncate",children:(0,p.W5)(L,70)}),(0,r.jsx)("p",{className:"text-xs text-zinc-500 dark:text-zinc-400 mt-1",children:D})]}),(0,r.jsx)(j.$,{variant:"ghost",size:"sm",className:"opacity-70 group-hover:opacity-100 transition-opacity",asChild:!0,children:(0,r.jsx)("a",{href:L,target:"_blank",rel:"noopener noreferrer",children:(0,r.jsx)(c.A,{className:"w-4 h-4"})})})]})})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-zinc-800 dark:text-zinc-200",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 text-zinc-500 dark:text-zinc-400"}),"Extracted Content"]}),W&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(b.E,{variant:"outline",className:"text-xs",children:[W.wordCount," words"]}),(0,r.jsxs)(b.E,{variant:"outline",className:"text-xs",children:[W.charCount," chars"]})]})]}),F?.text?(0,r.jsxs)("div",{className:"group relative bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-xl overflow-hidden hover:border-zinc-300 dark:hover:border-zinc-700 transition-all duration-200 hover:shadow-sm",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-zinc-50 dark:bg-zinc-800/50 border-b border-zinc-200 dark:border-zinc-700",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500/20 to-blue-600/10 flex items-center justify-center border border-blue-500/20",children:(0,r.jsx)(m.A,{className:"w-4 h-4 text-blue-600 dark:text-blue-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-zinc-900 dark:text-zinc-100",children:"Page Content"}),W&&(0,r.jsxs)("p",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:[W.lineCount," lines"]})]})]}),(0,r.jsx)("div",{className:"flex items-center gap-1",children:(0,r.jsx)(v.Bc,{children:(0,r.jsxs)(v.m_,{children:[(0,r.jsx)(v.k$,{asChild:!0,children:(0,r.jsx)(j.$,{variant:"ghost",size:"sm",className:(0,p.cn)("opacity-70 group-hover:opacity-100 transition-all duration-200",_&&"opacity-100"),onClick:I,children:_?(0,r.jsx)(x.A,{className:"w-4 h-4 text-green-600"}):(0,r.jsx)(u.A,{className:"w-4 h-4"})})}),(0,r.jsx)(v.ZI,{children:(0,r.jsx)("p",{children:_?"Copied!":"Copy content"})})]})})})]}),(0,r.jsx)("div",{className:"p-4 max-h-96 overflow-auto",children:(0,r.jsx)("pre",{className:"text-xs font-mono text-zinc-800 dark:text-zinc-300 whitespace-pre-wrap leading-relaxed",children:F.text})})]}):(0,r.jsxs)("div",{className:"text-center py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900 rounded-xl border border-zinc-200 dark:border-zinc-800",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-full flex items-center justify-center mb-4 bg-gradient-to-b from-zinc-100 to-zinc-50 shadow-inner dark:from-zinc-800/40 dark:to-zinc-900/60 mx-auto",children:(0,r.jsx)(m.A,{className:"h-8 w-8 text-zinc-400 dark:text-zinc-600"})}),(0,r.jsx)("h3",{className:"text-lg font-medium mb-2 text-zinc-900 dark:text-zinc-100",children:"No Content Extracted"}),(0,r.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400 max-w-sm mx-auto",children:"The webpage might be restricted, empty, or require JavaScript to load content"})]})]})]})}):(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:[(0,r.jsx)("div",{className:"w-20 h-20 rounded-full flex items-center justify-center mb-6 bg-gradient-to-b from-zinc-100 to-zinc-50 shadow-inner dark:from-zinc-800/40 dark:to-zinc-900/60",children:(0,r.jsx)(a.A,{className:"h-10 w-10 text-zinc-400 dark:text-zinc-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2 text-zinc-900 dark:text-zinc-100",children:"No URL Detected"}),(0,r.jsx)("p",{className:"text-zinc-500 dark:text-zinc-400 text-center max-w-sm",children:"Unable to extract a valid URL from the crawling request"})]})}),(0,r.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,r.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:!S&&F?.text&&(0,r.jsxs)(b.E,{className:"h-6 py-0.5",children:[(0,r.jsx)("div",{className:"w-2 h-2 rounded-full bg-green-500 mr-1.5"}),"Content extracted"]})}),(0,r.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:y&&!S?(0,h.Ey)(y):k?(0,h.Ey)(k):""})]})]})}},14296:(e,t,s)=>{s.d(t,{TG:()=>T,DS:()=>E,J1:()=>A,Di:()=>j,AQ:()=>w,m$:()=>R,Fl:()=>v,FK:()=>S,pn:()=>k,wi:()=>_,SK:()=>F,Ze:()=>N,yq:()=>z,VU:()=>D,sD:()=>I,_C:()=>L,Ey:()=>g,QL:()=>O,Bs:()=>b,NI:()=>P});var r=s(26316),l=s(41303),a=s(82164),n=s(62140),i=s(10022),o=s(20741),c=s(29446),d=s(96023),m=s(51563),x=s(18179),u=s(78464),h=s(75330);function p(e){try{let t=JSON.parse(e);if("object"==typeof t)return f(t)}catch{}let t=e.match(/<\/?([\w-]+)>/),s=t?t[1]:"unknown",r=!0;if(e.includes("ToolResult")){let t=e.match(/success\s*=\s*(True|False|true|false)/i);t&&(r="true"===t[1].toLowerCase())}return{toolName:s.replace(/_/g,"-"),functionName:s.replace(/-/g,"_"),toolOutput:e,isSuccess:r}}function f(e){if("tool_execution"in e&&"object"==typeof e.tool_execution){let t=e.tool_execution,s=t.function_name||"unknown",r=t.xml_tag_name||"";return{toolName:(r||s).replace(/_/g,"-"),functionName:s,xmlTagName:r||void 0,toolOutput:t.result?.output||"",isSuccess:t.result?.success!==!1,arguments:t.arguments,timestamp:t.execution_details?.timestamp,toolCallId:t.tool_call_id,summary:e.summary}}if("role"in e&&"content"in e&&"object"==typeof e.content){let t=e.content;if("tool_execution"in t&&"object"==typeof t.tool_execution)return f(t);if("tool_name"in t||"xml_tag_name"in t){let e=(t.tool_name||t.xml_tag_name||"unknown").replace(/_/g,"-");return{toolName:e,functionName:e.replace(/-/g,"_"),toolOutput:t.result?.output||"",isSuccess:t.result?.success!==!1}}}if("role"in e&&"content"in e&&"string"==typeof e.content)return p(e.content);if("tool_name"in e||"xml_tag_name"in e){let t=(e.tool_name||e.xml_tag_name||"unknown").replace(/_/g,"-");return{toolName:t,functionName:t.replace(/-/g,"_"),toolOutput:e.result?.output||"",isSuccess:e.result?.success!==!1}}return null}function g(e){if(!e)return"";try{let t=new Date(e);return isNaN(t.getTime())?"Invalid date":t.toLocaleString()}catch(e){return"Invalid date"}}function b(e){let t=e.toLowerCase(),s={"execute-command":"Execute Command","check-command-output":"Check Command Output","str-replace":"String Replace","create-file":"Create File","full-file-rewrite":"Rewrite File","delete-file":"Delete File","web-search":"Web Search","crawl-webpage":"Web Crawl","scrape-webpage":"Web Scrape","browser-navigate":"Browser Navigate","browser-click":"Browser Click","browser-extract":"Browser Extract","browser-fill":"Browser Fill","browser-wait":"Browser Wait","see-image":"View Image",ask:"Ask",complete:"Task Complete","execute-data-provider-call":"Data Provider Call","get-data-provider-endpoints":"Data Endpoints",deploy:"Deploy","generic-tool":"Tool",default:"Tool"};if(s[t])return s[t];if(t.startsWith("browser-")){let e=t.replace("browser-","").replace(/-/g," ");return"Browser "+e.charAt(0).toUpperCase()+e.slice(1)}return e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ")}function j(e){let t=P(e);if(!t)return null;let s=t.match(/<execute-command[^>]*>([\s\S]*?)<\/execute-command>/);if(s)return s[1].trim();try{let e=JSON.parse(t);if(e.tool_calls&&Array.isArray(e.tool_calls)){let t=e.tool_calls.find(e=>e.function?.name==="execute-command"||e.function?.name==="execute_command");if(t&&t.function?.arguments)try{let e="string"==typeof t.function.arguments?JSON.parse(t.function.arguments):t.function.arguments;if(e.command)return e.command}catch(e){}}}catch(e){}return t.includes("<execute-command")||t.includes("</execute-command>")||t.startsWith("{")||t.startsWith("<")||t.includes("ToolResult")||t.includes("No command")?(console.log("extractCommand: Could not extract command from content:",t.substring(0,200)),null):t.trim()}function N(e){let t=P(e);if(!t)return null;let s=t.match(/<check-command-output[^>]*session_name=["']([^"']+)["']/);if(s)return s[1].trim();try{let e=JSON.parse(t);if(e.tool_calls&&Array.isArray(e.tool_calls)){let t=e.tool_calls.find(e=>e.function?.name==="check-command-output"||e.function?.name==="check_command_output");if(t&&t.function?.arguments)try{let e="string"==typeof t.function.arguments?JSON.parse(t.function.arguments):t.function.arguments;if(e.session_name)return e.session_name}catch(e){}}}catch(e){}let r=t.match(/session_name=["']([^"']+)["']/);return r?r[1].trim():null}function w(e){let t=P(e);if(!t)return null;try{let e=JSON.parse(t);if(e.output&&"string"==typeof e.output)return e.output;if(e.content&&"string"==typeof e.content){let t=e.content.match(/<tool_result>\s*<(?:execute-command|check-command-output)>([\s\S]*?)<\/(?:execute-command|check-command-output)>\s*<\/tool_result>/);if(t)return t[1].trim();let s=e.content.match(/ToolResult\(.*?output='([\s\S]*?)'.*?\)/);if(s)return s[1];return e.content}if("string"==typeof e)return e}catch(r){let e=t.match(/<tool_result>\s*<(?:execute-command|check-command-output)>([\s\S]*?)<\/(?:execute-command|check-command-output)>\s*<\/tool_result>/);if(e)return e[1].trim();let s=t.match(/ToolResult\(.*?output='([\s\S]*?)'.*?\)/);if(s)return s[1];t.startsWith("<")||t.includes("ToolResult")}return t}function v(e){let t=P(e);if(!t)return null;try{let e=t.match(/exit_code=(\d+)/);if(e&&e[1])return parseInt(e[1],10);return 0}catch(e){return null}}function k(e){let t=P(e);if(!t)return null;try{let e=JSON.parse(t);if(e.content){if((0,h.tm)(e.content)){let t=(0,h.CG)(e.content);if(t.length>0&&t[0].parameters.file_path)return y(t[0].parameters.file_path)}let t=e.content.match(/file_path=["']([^"']+)["']/);if(t)return y(t[1])}}catch(e){}if((0,h.tm)(t)){let e=(0,h.CG)(t);if(e.length>0&&e[0].parameters.file_path)return y(e[0].parameters.file_path)}let s=t.match(/file_path=["']([^"']+)["']/);if(s)return y(s[1]);if("string"==typeof e&&e.startsWith('"{')&&e.endsWith('}"'))try{let t=JSON.parse(e),s=JSON.parse(t);if(s&&"object"==typeof s){if(s.file_path)return y(s.file_path);if(s.arguments&&s.arguments.file_path)return y(s.arguments.file_path)}}catch(e){}if("object"==typeof e&&null!==e)try{if("content"in e&&"string"==typeof e.content){let t=e.content.match(/<(?:create-file|delete-file|full-file-rewrite|str-replace)[^>]*\s+file_path=["']([\s\S]*?)["']/i)||e.content.match(/<delete[^>]*\s+file_path=["']([\s\S]*?)["']/i)||e.content.match(/<delete-file[^>]*>([^<]+)<\/delete-file>/i)||e.content.match(/<(?:create-file|delete-file|full-file-rewrite)\s+file_path=["']([^"']+)/i);if(t)return y(t[1])}if("file_path"in e)return y(e.file_path);if("arguments"in e&&e.arguments&&"object"==typeof e.arguments){let t=e.arguments;if(t.file_path)return y(t.file_path)}}catch(e){}try{let e=JSON.parse(t);if(e.file_path)return y(e.file_path);if(e.arguments&&e.arguments.file_path)return y(e.arguments.file_path)}catch(e){}let r=t.match(/file_path=["']([\s\S]*?)["']/i)||t.match(/target_file=["']([\s\S]*?)["']/i)||t.match(/path=["']([\s\S]*?)["']/i);if(r)return y(r[1].trim());let l=t.match(/<(?:create-file|delete-file|full-file-rewrite|str-replace)[^>]*\s+file_path=["']([\s\S]*?)["']/i)||t.match(/<delete[^>]*\s+file_path=["']([\s\S]*?)["']/i)||t.match(/<delete-file[^>]*>([^<]+)<\/delete-file>/i)||t.match(/<(?:create-file|delete-file|full-file-rewrite)\s+file_path=["']([^"']+)/i);if(l)return y(l[1]);if(t.toLowerCase().includes("delete")||t.includes("delete-file")){let e=t.match(/(?:delete|remove|deleting)\s+(?:file|the file)?:?\s+["']?([\w\-./\\]+\.\w+)["']?/i);if(e)return y(e[1]);let s=t.match(/["']?([\w\-./\\]+\.\w+)["']?/);if(s)return y(s[1])}return null}function y(e){return e?e.replace(/\\n/g,"\n").replace(/\\t/g,"	").replace(/\\r/g,"").replace(/\\\\/g,"\\").replace(/\\"/g,'"').replace(/\\'/g,"'").split("\n")[0].trim():e}function z(e){let t=P(e);if(!t)return{oldStr:null,newStr:null};let s=t.match(/<str-replace[^>]*>([\s\S]*?)<\/str-replace>/);if(s){let e=s[1],t=e.match(/<old_str>([\s\S]*?)<\/old_str>/),r=e.match(/<new_str>([\s\S]*?)<\/new_str>/);return{oldStr:t?t[1]:null,newStr:r?r[1]:null}}let r=t.match(/<old_str>([\s\S]*?)<\/old_str>/),l=t.match(/<new_str>([\s\S]*?)<\/new_str>/);return{oldStr:r?r[1]:null,newStr:l?l[1]:null}}function S(e,t){let s=P(e);if(!s)return null;try{let e=JSON.parse(s);if(e.content){if((0,h.tm)(e.content)){let t=(0,h.CG)(e.content);if(t.length>0&&t[0].parameters.file_contents)return C(t[0].parameters.file_contents)}let s="create-file"===t?"create-file":"full-file-rewrite",r=e.content.match(RegExp(`<${s}[^>]*>([\\s\\S]*?)<\\/${s}>`,"i"));if(r)return C(r[1])}}catch(e){}if((0,h.tm)(s)){let e=(0,h.CG)(s);if(e.length>0&&e[0].parameters.file_contents)return C(e[0].parameters.file_contents)}let r="create-file"===t?"create-file":"full-file-rewrite",l=s.match(RegExp(`<${r}[^>]*>([\\s\\S]*?)<\\/${r}>`,"i"));return l?C(l[1]):null}function C(e){if(!e)return"";if("object"==typeof e)return JSON.stringify(e,null,2);let t=e.trim();if(t.startsWith("{")&&t.endsWith("}")||t.startsWith("[")&&t.endsWith("]"))try{let t=JSON.parse(e);return JSON.stringify(t,null,2)}catch(e){}return e.replace(/\\n/g,"\n").replace(/\\t/g,"	").replace(/\\r/g,"").replace(/\\\\/g,"\\").replace(/\\"/g,'"').replace(/\\'/g,"'")}function A(e){let t=P(e);if(!t)return null;let s=t.match(/url=["'](https?:\/\/[^"']+)["']/);return s?s[1]:null}function E(e){if(!e)return"Browser Operation";let t=e.replace("browser-","").replace(/-/g," ");return t.charAt(0).toUpperCase()+t.slice(1)}function _(e){let t=P(e);if(!t)return null;let s=t.match(/ToolResult\(.*?output='([\s\S]*?)'.*?\)/);if(s)try{let e=JSON.parse(s[1]);if(e.query&&"string"==typeof e.query)return e.query}catch(e){}let r=t;try{let e=JSON.parse(t);if(e.query&&"string"==typeof e.query)return e.query;if("string"==typeof e.content){if(r=e.content,"string"==typeof e.query)return e.query;if("object"==typeof e.arguments&&null!==e.arguments&&"string"==typeof e.arguments.query)return e.arguments.query;if(Array.isArray(e.tool_calls)&&e.tool_calls.length>0){let t=e.tool_calls[0];if("object"==typeof t.arguments&&null!==t.arguments&&"string"==typeof t.arguments.query)return t.arguments.query;if("string"==typeof t.arguments)try{let e=JSON.parse(t.arguments);if("string"==typeof e.query)return e.query}catch{}}}}catch(e){}let l=r.match(/<web-search[^>]*\s+query=[\"']([^\"']*)["'][^>]*>/i);if(l&&l[1])return l[1].trim();let a=r.match(/query=[\"']([\s\S]*?)["']/i);return a&&a[1]?a[1].split(/[\"']/)[0].trim():null}function $(e){let t,s=[];try{let t=JSON.parse(e);if(Array.isArray(t))return t.map(e=>({title:e.title||"",url:e.url||"",snippet:e.content||e.snippet||""}));if(t.results&&Array.isArray(t.results))return t.results.map(e=>({title:e.title||"",url:e.url||"",snippet:e.content||""}))}catch(e){}let r=/\{\s*"title"\s*:\s*"([^"]+)"\s*,\s*"url"\s*:\s*"(https?:\/\/[^"]+)"\s*(?:,\s*"content"\s*:\s*"([^"]*)")?\s*\}/g;for(;null!==(t=r.exec(e));){let e=t[1],r=t[2],l=t[3]||"";r&&e&&!s.some(e=>e.url===r)&&s.push({title:e,url:r,snippet:l})}if(0===s.length){let t,r=/https?:\/\/[^\s"<]+/g;for(;null!==(t=r.exec(e));){let r=t[0],l=r.indexOf("://"),a=-1!==l?l+3:0,n=r.indexOf("/n",a),i=r.indexOf("\\n",a),o=-1;for(-1!==n&&-1!==i?o=Math.min(n,i):-1!==n?o=n:-1!==i&&(o=i),-1!==o&&(r=r.substring(0,o)),r=r.replace(/<\/?url>$/,"").replace(/<\/?content>$/,"").replace(/%3C$/,"");/[);.,\/]$/.test(r);)r=r.slice(0,-1);try{r=decodeURIComponent(decodeURIComponent(r))}catch(e){try{r=decodeURIComponent(r)}catch(e){console.warn("Failed to decode URL component:",r,e)}}r=(r=r.replace(/\u2026$/,"")).replace(/<\/?url>$/,"").replace(/<\/?content>$/,"");let c=t.index,d=e.substring(Math.max(0,c-100),c+r.length+200),m=d.match(/title"?\s*:\s*"([^"]+)"/i)||d.match(/Title[:\s]+([^\n<]+)/i)||d.match(/\"(.*?)\"[\s\n]*?https?:\/\//),x=T(r);m&&m[1].trim()&&(x=m[1].trim()),r&&!s.some(e=>e.url===r)&&s.push({title:x,url:r})}}return s}function T(e){try{let t=new URL(e);return t.hostname.replace("www.","")+("/"!==t.pathname?t.pathname:"")}catch(t){return e}}function R(e){let t=P(e);if(!t)return null;try{let e=JSON.parse(t);if(e.content){let t=e.content.match(/<(?:crawl|scrape)-webpage[^>]*\s+url=["'](https?:\/\/[^"']+)["']/i);if(t)return t[1]}}catch(e){}let s=t.match(/<(?:crawl|scrape)-webpage[^>]*\s+url=["'](https?:\/\/[^"']+)["']/i)||t.match(/url=["'](https?:\/\/[^"']+)["']/i);return s?s[1]:null}function L(e){let t=P(e);if(!t)return null;try{let e=JSON.parse(t);if(e.content&&"string"==typeof e.content){let t=e.content.match(/<tool_result[^>]*>\s*<(?:crawl|scrape)-webpage[^>]*>([\s\S]*?)<\/(?:crawl|scrape)-webpage>\s*<\/tool_result>/);if(t)try{let e=t[1],s=e.match(/ToolResult\(.*?output='([\s\S]*?)'.*?\)/);if(s)try{let e=JSON.parse(s[1].replace(/\\\\n/g,"\\n").replace(/\\\\u/g,"\\u"));if(Array.isArray(e)&&e.length>0){let t=e[0];return{title:t.Title||t.title||"",text:t.Text||t.text||t.content||""}}return{title:e.Title||e.title||"",text:e.Text||e.text||e.content||""}}catch(e){return{title:"Webpage Content",text:s[1]}}let r=JSON.parse(e);if(Array.isArray(r)&&r.length>0){let e=r[0];return{title:e.Title||e.title||"",text:e.Text||e.text||e.content||""}}return{title:r.Title||r.title||"",text:r.Text||r.text||r.content||""}}catch(e){return{title:"Webpage Content",text:t[1]}}let s=e.content.match(/ToolResult\(.*?output='([\s\S]*?)'.*?\)/);if(s)try{let e=JSON.parse(s[1].replace(/\\\\n/g,"\\n").replace(/\\\\u/g,"\\u"));if(Array.isArray(e)&&e.length>0){let t=e[0];return{title:t.Title||t.title||"",text:t.Text||t.text||t.content||""}}return{title:e.Title||e.title||"",text:e.Text||e.text||e.content||""}}catch(e){return{title:"Webpage Content",text:s[1]}}}let s=t.match(/<(?:crawl|scrape)-webpage[^>]*>([\s\S]*?)<\/(?:crawl|scrape)-webpage>/);if(s){let e=s[1].match(/ToolResult\(.*?output='([\s\S]*?)'.*?\)/);if(e)try{let t=e[1].replace(/\\\\n/g,"\\n").replace(/\\\\u/g,"\\u"),s=JSON.parse(t);if(Array.isArray(s)&&s.length>0){let e=s[0];return{title:e.Title||e.title||(e.URL?new URL(e.URL).hostname:""),text:e.Text||e.text||e.content||""}}return{title:s.Title||s.title||"",text:s.Text||s.text||s.content||""}}catch(t){return{title:"Webpage Content",text:e[1]}}}if(Array.isArray(e)&&e.length>0){let t=e[0];return{title:t.Title||t.title||"",text:t.Text||t.text||t.content||""}}if("object"==typeof e&&null!==e){if("Title"in e||"title"in e||"Text"in e||"text"in e)return{title:e.Title||e.title||"Webpage Content",text:e.Text||e.text||e.content||""};return{title:"Webpage Content",text:JSON.stringify(e)}}}catch(s){let e=t.match(/ToolResult\(.*?output='([\s\S]*?)'.*?\)/);if(e)try{let t=JSON.parse(e[1].replace(/\\\\n/g,"\\n").replace(/\\\\u/g,"\\u"));if(Array.isArray(t)&&t.length>0){let e=t[0];return{title:e.Title||e.title||"",text:e.Text||e.text||e.content||""}}return{title:t.Title||t.title||"",text:t.Text||t.text||t.content||""}}catch(t){return{title:"Webpage Content",text:e[1]}}if(t)return{title:"Webpage Content",text:t}}return null}function F(e){let t=P(e);if(!t)return[];try{let e,s=t.match(/"results":\s*\[([^\]]*(?:\[[^\]]*\][^\]]*)*)\]/);if(s)try{let e="["+s[1]+"]",t=JSON.parse(e);if(Array.isArray(t))return t.map(e=>({title:e.title||"",url:e.url||"",snippet:e.content||""}))}catch(e){console.warn("Failed to parse results array:",e)}let r=/\{\s*"url":\s*"([^"]+)"\s*,\s*"title":\s*"([^"]+)"\s*,\s*"content":\s*"([^"]*)"[^}]*\}/g,l=[];for(;null!==(e=r.exec(t));)l.push({url:e[1],title:e[2],snippet:e[3]});if(l.length>0)return l;let a=JSON.parse(t);if(a.results&&Array.isArray(a.results))return a.results.map(e=>({title:e.title||"",url:e.url||"",snippet:e.content||""}));if(a.content&&"string"==typeof a.content){let e=a.content.match(/<tool_result[^>]*>\s*<web-search[^>]*>([\s\S]*?)<\/web-search>\s*<\/tool_result>/);if(e)try{return JSON.parse(e[1])}catch(t){return $(e[1])}let t=a.content.match(/\[\s*{[\s\S]*}\s*\]/);if(t)try{return JSON.parse(t[0])}catch(e){}return $(a.content)}}catch(e){}return $(t)}function P(e){if(!e)return null;if("string"==typeof e){if(e.startsWith('"{')&&e.endsWith('}"'))try{let t=JSON.parse(e),s=JSON.parse(t);if(s&&"object"==typeof s&&"content"in s)return s.content;return JSON.stringify(s)}catch(e){}return e}if("object"==typeof e&&null!==e)try{if("content"in e&&"string"==typeof e.content)return e.content;if("content"in e&&"object"==typeof e.content&&null!==e.content){if("content"in e.content&&"string"==typeof e.content.content)return e.content.content;return JSON.stringify(e.content)}if("role"in e&&"content"in e&&"string"==typeof e.content)return e.content;else if("role"in e&&"content"in e&&"object"==typeof e.content&&null!==e.content){if("content"in e.content&&"string"==typeof e.content.content)return e.content.content;return JSON.stringify(e.content)}else{let t=JSON.stringify(e);return t.includes("<")||t.includes("file_path")||t.includes("command"),t}}catch(t){console.error("Error in normalizeContentToString:",t,"Content:",e)}return null}function D(e,t){let s=P(e);if(!s)return null;let r="create-file"===t?"create-file":"full-file-rewrite";if("object"==typeof e&&null!==e)try{if("content"in e&&"string"==typeof e.content){let t=e.content.match(RegExp(`<${r}[^>]*>`,"i"));if(t){let s=e.content.indexOf(t[0])+t[0].length,l=e.content.substring(s),a=l.match(RegExp(`<\\/${r}>`,"i"));if(a)return C(l.substring(0,a.index));return C(l)}}}catch(e){}let l=s.match(RegExp(`<${r}[^>]*>`,"i"));if(l){let e=s.indexOf(l[0])+l[0].length,t=s.substring(e),a=t.match(RegExp(`<\\/${r}>`,"i"));return a?C(t.substring(0,a.index)):C(t)}return null}let O=e=>{let t=e.split(".").pop()?.toLowerCase();switch(t){case"js":case"jsx":case"ts":case"tsx":return{icon:r.A,color:"text-yellow-500 dark:text-yellow-400",bgColor:"bg-gradient-to-br from-yellow-500/20 to-yellow-600/10 border border-yellow-500/20"};case"py":return{icon:r.A,color:"text-blue-500 dark:text-blue-400",bgColor:"bg-gradient-to-br from-blue-500/20 to-blue-600/10 border border-blue-500/20"};case"html":case"css":case"scss":return{icon:r.A,color:"text-orange-500 dark:text-orange-400",bgColor:"bg-gradient-to-br from-orange-500/20 to-orange-600/10 border border-orange-500/20"};case"json":return{icon:l.A,color:"text-green-500 dark:text-green-400",bgColor:"bg-gradient-to-br from-green-500/20 to-green-600/10 border border-green-500/20"};case"csv":return{icon:a.A,color:"text-emerald-500 dark:text-emerald-400",bgColor:"bg-gradient-to-br from-emerald-500/20 to-emerald-600/10 border border-emerald-500/20"};case"xml":case"yaml":case"yml":return{icon:r.A,color:"text-purple-500 dark:text-purple-400",bgColor:"bg-gradient-to-br from-purple-500/20 to-purple-600/10 border border-purple-500/20"};case"jpg":case"jpeg":case"png":case"gif":case"svg":case"webp":return{icon:n.A,color:"text-pink-500 dark:text-pink-400",bgColor:"bg-gradient-to-br from-pink-500/20 to-pink-600/10 border border-pink-500/20"};case"md":case"mdx":return{icon:i.A,color:"text-slate-500 dark:text-slate-400",bgColor:"bg-gradient-to-br from-slate-500/20 to-slate-600/10 border border-slate-500/20"};case"txt":return{icon:i.A,color:"text-zinc-500 dark:text-zinc-400",bgColor:"bg-gradient-to-br from-zinc-500/20 to-zinc-600/10 border border-zinc-500/20"};case"pdf":return{icon:o.A,color:"text-red-500 dark:text-red-400",bgColor:"bg-gradient-to-br from-red-500/20 to-red-600/10 border border-red-500/20"};case"mp4":case"avi":case"mov":return{icon:c.A,color:"text-indigo-500 dark:text-indigo-400",bgColor:"bg-gradient-to-br from-indigo-500/20 to-indigo-600/10 border border-indigo-500/20"};case"mp3":case"wav":case"ogg":return{icon:d.A,color:"text-teal-500 dark:text-teal-400",bgColor:"bg-gradient-to-br from-teal-500/20 to-teal-600/10 border border-teal-500/20"};case"zip":case"tar":case"gz":case"rar":return{icon:m.A,color:"text-amber-500 dark:text-amber-400",bgColor:"bg-gradient-to-br from-amber-500/20 to-amber-600/10 border border-amber-500/20"};default:if(!t||e.includes("/"))return{icon:x.A,color:"text-blue-500 dark:text-blue-400",bgColor:"bg-gradient-to-br from-blue-500/20 to-blue-600/10 border border-blue-500/20"};return{icon:u.A,color:"text-zinc-500 dark:text-zinc-400",bgColor:"bg-gradient-to-br from-zinc-500/20 to-zinc-600/10 border border-zinc-500/20"}}};function I(e){let t=function(e){try{if("string"==typeof e)return p(e);if("object"==typeof e&&null!==e)return f(e);return null}catch(e){return console.error("Error parsing tool result:",e),null}}(e);if(t){let e=t.arguments||{};return{toolResult:t,arguments:e,filePath:e.file_path||e.path||null,fileContent:e.file_contents||e.content||null,command:e.command||null,url:e.url||null,query:e.query||null}}return{toolResult:null,arguments:{},filePath:null,fileContent:null,command:null,url:null,query:null}}},14901:(e,t,s)=>{s.d(t,{N:()=>p});var r=s(60687),l=s(43210),a=s.n(l),n=s(82679),i=s(5336),o=s(43649),c=s(48730),d=s(14296),m=s(44493),x=s(96834),u=s(42692),h=s(91394);function p({name:e="generic-tool",assistantContent:t,toolContent:s,assistantTimestamp:l,toolTimestamp:p,isSuccess:f=!0,isStreaming:g=!1}){let b=(0,d.Bs)(e),j=e=>{if(!e)return null;let{toolResult:t}=(0,d.sD)(e);if(t){let e={tool:t.xmlTagName||t.functionName};return t.arguments&&Object.keys(t.arguments).length>0&&(e.parameters=t.arguments),t.toolOutput&&(e.output=t.toolOutput),void 0!==t.isSuccess&&(e.success=t.isSuccess),JSON.stringify(e,null,2)}if("object"==typeof e){if("tool_name"in e||"xml_tag_name"in e){let t={tool:e.tool_name||e.xml_tag_name||"unknown"};return e.parameters&&Object.keys(e.parameters).length>0&&(t.parameters=e.parameters),e.result&&(t.result=e.result),JSON.stringify(t,null,2)}if("content"in e&&"object"==typeof e.content){let t=e.content;if("tool_name"in t||"xml_tag_name"in t){let e={tool:t.tool_name||t.xml_tag_name||"unknown"};return t.parameters&&Object.keys(t.parameters).length>0&&(e.parameters=t.parameters),t.result&&(e.result=t.result),JSON.stringify(e,null,2)}}return e.content&&"string"==typeof e.content?e.content:JSON.stringify(e,null,2)}if("string"==typeof e)try{let t=JSON.parse(e);return JSON.stringify(t,null,2)}catch(t){return e}return String(e)},N=a().useMemo(()=>j(t),[t]),w=a().useMemo(()=>j(s),[s]);return(0,r.jsxs)(m.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,r.jsx)(m.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,r.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"relative p-2 rounded-lg bg-gradient-to-br from-orange-500/20 to-orange-600/10 border border-orange-500/20",children:(0,r.jsx)(n.A,{className:"w-5 h-5 text-orange-500 dark:text-orange-400"})}),(0,r.jsx)("div",{children:(0,r.jsx)(m.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:b})})]}),!g&&(0,r.jsxs)(x.E,{variant:"secondary",className:f?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[f?(0,r.jsx)(i.A,{className:"h-3.5 w-3.5"}):(0,r.jsx)(o.A,{className:"h-3.5 w-3.5"}),f?"Tool executed successfully":"Tool execution failed"]})]})}),(0,r.jsx)(m.Wu,{className:"p-0 h-full flex-1 overflow-hidden relative",children:g?(0,r.jsx)(h.G,{icon:n.A,iconColor:"text-orange-500 dark:text-orange-400",bgColor:"bg-gradient-to-b from-orange-100 to-orange-50 shadow-inner dark:from-orange-800/40 dark:to-orange-900/60 dark:shadow-orange-950/20",title:"Executing tool",filePath:e,showProgress:!0}):N||w?(0,r.jsx)(u.F,{className:"h-full w-full",children:(0,r.jsxs)("div",{className:"p-4 space-y-4",children:[N&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"text-sm font-medium text-zinc-700 dark:text-zinc-300 flex items-center",children:[(0,r.jsx)(n.A,{className:"h-4 w-4 mr-2 text-zinc-500 dark:text-zinc-400"}),"Input"]}),(0,r.jsx)("div",{className:"border-muted bg-muted/20 rounded-lg overflow-hidden border",children:(0,r.jsx)("div",{className:"p-4",children:(0,r.jsx)("pre",{className:"text-xs text-zinc-700 dark:text-zinc-300 whitespace-pre-wrap break-words font-mono",children:N})})})]}),w&&(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"text-sm font-medium text-zinc-700 dark:text-zinc-300 flex items-center",children:[(0,r.jsx)(n.A,{className:"h-4 w-4 mr-2 text-zinc-500 dark:text-zinc-400"}),"Output"]}),(0,r.jsx)("div",{className:"border-muted bg-muted/20 rounded-lg overflow-hidden border",children:(0,r.jsx)("div",{className:"p-4",children:(0,r.jsx)("pre",{className:"text-xs text-zinc-700 dark:text-zinc-300 whitespace-pre-wrap break-words font-mono",children:w})})})]})]})}):(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:[(0,r.jsx)("div",{className:"w-20 h-20 rounded-full flex items-center justify-center mb-6 bg-gradient-to-b from-zinc-100 to-zinc-50 shadow-inner dark:from-zinc-800/40 dark:to-zinc-900/60",children:(0,r.jsx)(n.A,{className:"h-10 w-10 text-zinc-400 dark:text-zinc-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2 text-zinc-900 dark:text-zinc-100",children:"No Content Available"}),(0,r.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400 text-center max-w-md",children:"This tool execution did not produce any input or output content to display."})]})}),(0,r.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,r.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:!g&&(N||w)&&(0,r.jsxs)(x.E,{variant:"outline",className:"h-6 py-0.5 bg-zinc-50 dark:bg-zinc-900",children:[(0,r.jsx)(n.A,{className:"h-3 w-3"}),"Tool"]})}),(0,r.jsxs)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400 flex items-center gap-2",children:[(0,r.jsx)(c.A,{className:"h-3.5 w-3.5"}),p&&!g?(0,d.Ey)(p):l?(0,d.Ey)(l):""]})]})]})}},15353:(e,t,s)=>{s.d(t,{o:()=>S});var r=s(60687),l=s(43210),a=s(10022),n=s(82080),i=s(92834),o=s(11437),c=s(99270),d=s(5336),m=s(43649),x=s(9005),u=s(25334),h=s(48730),p=s(14296),f=s(4780),g=s(10218),b=s(44493),j=s(96834),N=s(29523),w=s(42692),v=s(91394);let k=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){}return e},y=e=>{let t=k(e);if(!t||"object"!=typeof t)return{query:null,results:[],answer:null,images:[],success:void 0,timestamp:void 0};if("tool_execution"in t&&"object"==typeof t.tool_execution){let e=t.tool_execution,s=e.arguments||{},r=e.result?.output;if("string"==typeof r)try{r=JSON.parse(r)}catch(e){}return r=r||{},{query:s.query||r?.query||null,results:r?.results?.map(e=>({title:e.title||"",url:e.url||"",snippet:e.content||e.snippet||""}))||[],answer:r?.answer||null,images:r?.images||[],success:e.result?.success,timestamp:e.execution_details?.timestamp}}return"role"in t&&"content"in t?y(t.content):{query:null,results:[],answer:null,images:[],success:void 0,timestamp:void 0}},z=e=>{let t=(0,p.sD)(e);if(t.toolResult){let e=t.arguments||{};return console.log("WebSearchToolView: Extracted from legacy format (extractToolData):",{query:t.query||e.query,resultsCount:0}),{query:t.query||e.query||null,results:[],answer:null,images:[]}}let s=(0,p.wi)(e);return console.log("WebSearchToolView: Extracted from legacy format (fallback):",{query:s,resultsCount:0}),{query:s,results:[],answer:null,images:[]}};function S({name:e="web-search",assistantContent:t,toolContent:s,assistantTimestamp:k,toolTimestamp:S,isSuccess:C=!0,isStreaming:A=!1}){let{resolvedTheme:E}=(0,g.D)(),[_,$]=(0,l.useState)({}),{query:T,searchResults:R,answer:L,images:F,actualIsSuccess:P,actualToolTimestamp:D,actualAssistantTimestamp:O}=function(e,t,s,r,l){let a=null,n=[],i=null,o=[],c=s,d=r,m=l,x=y(e),u=y(t);if(console.log("WebSearchToolView: Format detection results:",{assistantNewFormat:{hasQuery:!!x.query,resultsCount:x.results.length,hasAnswer:!!x.answer,imagesCount:x.images.length},toolNewFormat:{hasQuery:!!u.query,resultsCount:u.results.length,hasAnswer:!!u.answer,imagesCount:u.images.length}}),x.query||x.results.length>0)a=x.query,n=x.results,i=x.answer,o=x.images,void 0!==x.success&&(c=x.success),x.timestamp&&(m=x.timestamp),console.log("WebSearchToolView: Using assistant new format data");else if(u.query||u.results.length>0)a=u.query,n=u.results,i=u.answer,o=u.images,void 0!==u.success&&(c=u.success),u.timestamp&&(d=u.timestamp),console.log("WebSearchToolView: Using tool new format data");else{let s=z(e),r=z(t);a=s.query||r.query;let l=(0,p.SK)(t);if(n=l,console.log("WebSearchToolView: Using legacy format data:",{query:a,legacyResultsCount:l.length,firstLegacyResult:l[0]}),t)try{let e;(e="string"==typeof t?JSON.parse(t):"object"==typeof t&&null!==t?t:{}).answer&&"string"==typeof e.answer&&(i=e.answer),e.images&&Array.isArray(e.images)&&(o=e.images)}catch(e){}}if(a||(a=(0,p.wi)(e)||(0,p.wi)(t)),0===n.length){let e=(0,p.SK)(t);n=e,console.log("WebSearchToolView: Fallback extraction results:",e.length)}return console.log("WebSearchToolView: Final extracted data:",{query:a,searchResultsCount:n.length,hasAnswer:!!i,imagesCount:o.length,actualIsSuccess:c,firstResult:n[0]}),{query:a,searchResults:n,answer:i,images:o,actualIsSuccess:c,actualToolTimestamp:d,actualAssistantTimestamp:m}}(t,s,C,S,k),I=(0,p.Bs)(e),W=e=>{try{let t=new URL(e).hostname;return`https://www.google.com/s2/favicons?domain=${t}&sz=128`}catch(e){return null}},U=e=>{let{url:t,title:s}=e;return t.includes("news")||t.includes("article")||s.includes("News")?{icon:a.A,label:"Article"}:t.includes("wiki")?{icon:n.A,label:"Wiki"}:t.includes("blog")?{icon:i.A,label:"Blog"}:{icon:o.A,label:"Website"}};return(0,r.jsxs)(b.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,r.jsx)(b.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,r.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"relative p-2 rounded-xl bg-gradient-to-br from-blue-500/20 to-blue-600/10 border border-blue-500/20",children:(0,r.jsx)(c.A,{className:"w-5 h-5 text-blue-500 dark:text-blue-400"})}),(0,r.jsx)("div",{children:(0,r.jsx)(b.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:I})})]}),!A&&(0,r.jsxs)(j.E,{variant:"secondary",className:P?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[P?(0,r.jsx)(d.A,{className:"h-3.5 w-3.5"}):(0,r.jsx)(m.A,{className:"h-3.5 w-3.5"}),P?"Search completed successfully":"Search failed"]})]})}),(0,r.jsx)(b.Wu,{className:"p-0 h-full flex-1 overflow-hidden relative",children:A&&0===R.length&&!L?(0,r.jsx)(v.G,{icon:c.A,iconColor:"text-blue-500 dark:text-blue-400",bgColor:"bg-gradient-to-b from-blue-100 to-blue-50 shadow-inner dark:from-blue-800/40 dark:to-blue-900/60 dark:shadow-blue-950/20",title:"Searching the web",filePath:T,showProgress:!0}):R.length>0||L?(0,r.jsx)(w.F,{className:"h-full w-full",children:(0,r.jsxs)("div",{className:"p-4 py-0 my-4",children:[F.length>0&&(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h3",{className:"text-sm font-medium text-zinc-700 dark:text-zinc-300 mb-3 flex items-center",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2 opacity-70"}),"Images"]}),(0,r.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 gap-3 mb-1",children:F.slice(0,6).map((e,t)=>(0,r.jsxs)("a",{href:e,target:"_blank",rel:"noopener noreferrer",className:"group relative overflow-hidden rounded-lg border border-zinc-200 dark:border-zinc-800 bg-zinc-100 dark:bg-zinc-900 hover:border-blue-300 dark:hover:border-blue-700 transition-colors shadow-sm hover:shadow-md",children:[(0,r.jsx)("img",{src:e,alt:`Search result ${t+1}`,className:"object-cover w-full h-32 group-hover:opacity-90 transition-opacity",onError:e=>{let t=e.target;t.src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23888' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='18' height='18' rx='2' ry='2'%3E%3C/rect%3E%3Ccircle cx='8.5' cy='8.5' r='1.5'%3E%3C/circle%3E%3Cpolyline points='21 15 16 10 5 21'%3E%3C/polyline%3E%3C/svg%3E",t.classList.add("p-4")}}),(0,r.jsx)("div",{className:"absolute top-0 right-0 p-1",children:(0,r.jsx)(j.E,{variant:"secondary",className:"bg-black/60 hover:bg-black/70 text-white border-none shadow-md",children:(0,r.jsx)(u.A,{className:"h-3 w-3"})})})]},t))}),F.length>6&&(0,r.jsxs)(N.$,{variant:"outline",size:"sm",className:"mt-2 text-xs",children:["View ",F.length-6," more images"]})]}),R.length>0&&(0,r.jsxs)("div",{className:"text-sm font-medium text-zinc-800 dark:text-zinc-200 mb-4 flex items-center justify-between",children:[(0,r.jsxs)("span",{children:["Search Results (",R.length,")"]}),(0,r.jsxs)(j.E,{variant:"outline",className:"text-xs font-normal",children:[(0,r.jsx)(h.A,{className:"h-3 w-3 mr-1.5 opacity-70"}),new Date().toLocaleDateString()]})]}),(0,r.jsx)("div",{className:"space-y-4",children:R.map((e,t)=>{let{icon:s,label:l}=U(e),a=_[t]||!1,n=W(e.url);return(0,r.jsxs)("div",{className:"bg-card border rounded-lg shadow-sm hover:shadow transition-shadow",children:[(0,r.jsx)("div",{className:"p-4",children:(0,r.jsxs)("div",{className:"flex items-start gap-3 mb-2",children:[n&&(0,r.jsx)("img",{src:n,alt:"",className:"w-5 h-5 mt-1 rounded",onError:e=>{e.target.style.display="none"}}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("div",{className:"flex items-center gap-2 mb-1",children:(0,r.jsxs)(j.E,{variant:"outline",className:"text-xs px-2 py-0 h-5 font-normal bg-zinc-50 dark:bg-zinc-800",children:[(0,r.jsx)(s,{className:"h-3 w-3 mr-1 opacity-70"}),l]})}),(0,r.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"text-md font-medium text-blue-600 dark:text-blue-400 hover:underline line-clamp-1 mb-1",children:(0,f.W5)((0,p.TG)(e.title),50)}),(0,r.jsxs)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400 mb-2 flex items-center",children:[(0,r.jsx)(o.A,{className:"h-3 w-3 mr-1.5 flex-shrink-0 opacity-70"}),(0,f.W5)((0,p.TG)(e.url),70)]})]})]})}),a&&(0,r.jsxs)("div",{className:"bg-zinc-50 px-4 dark:bg-zinc-800/50 border-t border-zinc-200 dark:border-zinc-800 p-3 flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:["Source: ",(0,p.TG)(e.url)]}),(0,r.jsx)(N.$,{variant:"outline",size:"sm",className:"h-7 text-xs bg-white dark:bg-zinc-900",asChild:!0,children:(0,r.jsxs)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",children:[(0,r.jsx)(u.A,{className:"h-3 w-3"}),"Visit Site"]})})]})]},t)})})]})}):(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:[(0,r.jsx)("div",{className:"w-20 h-20 rounded-full flex items-center justify-center mb-6 bg-gradient-to-b from-zinc-100 to-zinc-50 shadow-inner dark:from-zinc-800/40 dark:to-zinc-900/60",children:(0,r.jsx)(c.A,{className:"h-10 w-10 text-zinc-400 dark:text-zinc-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2 text-zinc-900 dark:text-zinc-100",children:"No Results Found"}),(0,r.jsx)("div",{className:"bg-zinc-50 dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-lg p-4 w-full max-w-md text-center mb-4 shadow-sm",children:(0,r.jsx)("code",{className:"text-sm font-mono text-zinc-700 dark:text-zinc-300 break-all",children:T||"Unknown query"})}),(0,r.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400",children:"Try refining your search query for better results"})]})}),(0,r.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,r.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:!A&&R.length>0&&(0,r.jsxs)(j.E,{variant:"outline",className:"h-6 py-0.5",children:[(0,r.jsx)(o.A,{className:"h-3 w-3"}),R.length," results"]})}),(0,r.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:D&&!A?(0,p.Ey)(D):O?(0,p.Ey)(O):""})]})]})}},16895:(e,t,s)=>{s.d(t,{e:()=>k});var r=s(60687),l=s(43210),a=s.n(l),n=s(39743),i=s(5336),o=s(43649),c=s(41862),d=s(45609),m=s(70334),x=s(24366),u=s(63934),h=s(48730),p=s(14296),f=s(4780),g=s(10218),b=s(44493),j=s(96834),N=s(23562),w=s(42692),v=s(83156);function k({name:e="terminate-command",assistantContent:t,toolContent:s,assistantTimestamp:k,toolTimestamp:y,isSuccess:z=!0,isStreaming:S=!1}){let{resolvedTheme:C}=(0,g.D)(),[A,E]=(0,l.useState)(0),[_,$]=(0,l.useState)(!0),{sessionName:T,output:R,actualIsSuccess:L,actualToolTimestamp:F,actualAssistantTimestamp:P}=(0,v.p)(t,s,z,y,k),D=a().useMemo(()=>{if(T)return T;if(!t)return null;let e=(0,p.NI)(t);if(!e)return null;try{let t=JSON.parse(e);if(t.content){let e=t.content.match(/<terminate-command[^>]*session_name=["']([^"']+)["'][^>]*>/);if(e)return e[1].trim()}}catch(s){let t=e.match(/<terminate-command[^>]*session_name=["']([^"']+)["'][^>]*>/);if(t)return t[1].trim()}return null},[t,T]),O=D?.trim()||T,I=(0,p.Bs)(e)||"Terminate Session",W=a().useMemo(()=>{if(!R)return!1;let e=R.toLowerCase();if(e.includes("does not exist"))return!1;if(e.includes("terminated")||e.includes("killed"))return!0;if("string"==typeof s){let e=s.match(/ToolResult\(success=(true|false)/i);if(e)return"true"===e[1].toLowerCase()}return L},[R,L,s]),U=a().useMemo(()=>{if(!R)return[];let e=R;try{if("string"==typeof R&&(R.trim().startsWith("{")||R.trim().startsWith("{"))){let t=JSON.parse(R);t&&"object"==typeof t&&t.output&&(e=t.output)}}catch(e){}return(e=(e=(e=(e=String(e)).replace(/\\\\/g,"\\")).replace(/\\n/g,"\n").replace(/\\t/g,"	").replace(/\\"/g,'"').replace(/\\'/g,"'")).replace(/\\u([0-9a-fA-F]{4})/g,(e,t)=>String.fromCharCode(parseInt(t,16)))).split("\n")},[R]),V=U.length>10,J=U.slice(0,10),B=_?U:J;return(0,r.jsxs)(b.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,r.jsx)(b.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,r.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"relative p-2 rounded-lg bg-gradient-to-br from-red-500/20 to-red-600/10 border border-red-500/20",children:(0,r.jsx)(n.A,{className:"w-5 h-5 text-red-500 dark:text-red-400"})}),(0,r.jsx)("div",{children:(0,r.jsx)(b.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:I})})]}),!S&&(0,r.jsxs)(j.E,{variant:"secondary",className:W?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[W?(0,r.jsx)(i.A,{className:"h-3.5 w-3.5 mr-1"}):(0,r.jsx)(o.A,{className:"h-3.5 w-3.5 mr-1"}),W?"Session terminated":"Termination failed"]})]})}),(0,r.jsx)(b.Wu,{className:"p-0 h-full flex-1 overflow-hidden relative",children:S?(0,r.jsx)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:(0,r.jsxs)("div",{className:"text-center w-full max-w-xs",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-full mx-auto mb-6 flex items-center justify-center bg-gradient-to-b from-red-100 to-red-50 shadow-inner dark:from-red-800/40 dark:to-red-900/60 dark:shadow-red-950/20",children:(0,r.jsx)(c.A,{className:"h-8 w-8 animate-spin text-red-500 dark:text-red-400"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-zinc-900 dark:text-zinc-100 mb-2",children:"Terminating session"}),(0,r.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400 mb-6",children:(0,r.jsx)("span",{className:"font-mono text-xs break-all",children:O||"Processing termination..."})}),(0,r.jsx)(N.k,{value:A,className:"w-full h-1"}),(0,r.jsxs)("p",{className:"text-xs text-zinc-400 dark:text-zinc-500 mt-2",children:[A,"%"]})]})}):O?(0,r.jsx)(w.F,{className:"h-full w-full",children:(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsxs)("div",{className:"mb-4 bg-zinc-100 dark:bg-neutral-900 rounded-lg overflow-hidden border border-zinc-200 dark:border-zinc-800",children:[(0,r.jsxs)("div",{className:"bg-zinc-200 dark:bg-zinc-800 px-4 py-2 flex items-center gap-2",children:[(0,r.jsx)(d.A,{className:"h-4 w-4 text-zinc-600 dark:text-zinc-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-zinc-700 dark:text-zinc-300",children:"Session"})]}),(0,r.jsxs)("div",{className:"p-4 font-mono text-sm text-zinc-700 dark:text-zinc-300 flex gap-2",children:[(0,r.jsx)("span",{className:"text-red-500 dark:text-red-400 select-none",children:"●"}),(0,r.jsx)("code",{className:"flex-1 break-all",children:O})]})]}),R&&(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("h3",{className:"text-sm font-medium text-zinc-700 dark:text-zinc-300 flex items-center",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2 text-zinc-500 dark:text-zinc-400"}),"Result"]}),(0,r.jsx)(j.E,{className:(0,f.cn)("ml-2",W?"bg-emerald-100 text-emerald-700 dark:bg-emerald-900/30 dark:text-emerald-400":"bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400"),children:W?"Success":"Failed"})]}),(0,r.jsxs)("div",{className:"bg-zinc-100 dark:bg-neutral-900 rounded-lg overflow-hidden border border-zinc-200/20",children:[(0,r.jsxs)("div",{className:"bg-zinc-300 dark:bg-neutral-800 flex items-center justify-between dark:border-zinc-700/50",children:[(0,r.jsxs)("div",{className:"bg-zinc-200 w-full dark:bg-zinc-800 px-4 py-2 flex items-center gap-2",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 text-zinc-600 dark:text-zinc-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-zinc-700 dark:text-zinc-300",children:"Termination output"})]}),!W&&(0,r.jsxs)(j.E,{variant:"outline",className:"text-xs h-5 border-red-700/30 text-red-400",children:[(0,r.jsx)(o.A,{className:"h-3 w-3 mr-1"}),"Error"]})]}),(0,r.jsx)("div",{className:"p-4 max-h-96 overflow-auto scrollbar-hide",children:(0,r.jsxs)("pre",{className:"text-xs text-zinc-600 dark:text-zinc-300 font-mono whitespace-pre-wrap break-all overflow-visible",children:[B.map((e,t)=>(0,r.jsx)("div",{className:"py-0.5 bg-transparent",children:e||" "},t)),!_&&V&&(0,r.jsxs)("div",{className:"text-zinc-500 mt-2 border-t border-zinc-700/30 pt-2",children:["+ ",U.length-10," more lines"]})]})})]})]}),!R&&!S&&(0,r.jsx)("div",{className:"bg-black rounded-lg overflow-hidden border border-zinc-700/20 shadow-md p-6 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(u.A,{className:"h-8 w-8 text-zinc-500 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-zinc-400 text-sm",children:"No output received"})]})})]})}):(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:[(0,r.jsx)("div",{className:"w-20 h-20 rounded-full flex items-center justify-center mb-6 bg-gradient-to-b from-zinc-100 to-zinc-50 shadow-inner dark:from-zinc-800/40 dark:to-zinc-900/60",children:(0,r.jsx)(n.A,{className:"h-10 w-10 text-zinc-400 dark:text-zinc-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2 text-zinc-900 dark:text-zinc-100",children:"No Session Found"}),(0,r.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400 text-center max-w-md",children:"No session name was detected. Please provide a valid session to terminate."})]})}),(0,r.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,r.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:!S&&O&&(0,r.jsxs)(j.E,{variant:"outline",className:"h-6 py-0.5 bg-zinc-50 dark:bg-zinc-900",children:[(0,r.jsx)(n.A,{className:"h-3 w-3 mr-1"}),"Terminate"]})}),(0,r.jsxs)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400 flex items-center gap-2",children:[(0,r.jsx)(h.A,{className:"h-3.5 w-3.5"}),F&&!S?(0,p.Ey)(F):P?(0,p.Ey)(P):""]})]})]})}},18116:(e,t,s)=>{s.d(t,{A:()=>i});var r=s(60687),l=s(43210),a=s(24851),n=s(4780);function i({className:e,defaultValue:t,value:s,min:i=0,max:o=100,...c}){let d=l.useMemo(()=>Array.isArray(s)?s:Array.isArray(t)?t:[i,o],[s,t,i,o]);return(0,r.jsxs)(a.bL,{"data-slot":"slider",defaultValue:t,value:s,min:i,max:o,className:(0,n.cn)("relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",e),...c,children:[(0,r.jsx)(a.CC,{"data-slot":"slider-track",className:(0,n.cn)("bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5"),children:(0,r.jsx)(a.Q6,{"data-slot":"slider-range",className:(0,n.cn)("bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full")})}),Array.from({length:d.length},(e,t)=>(0,r.jsx)(a.zi,{"data-slot":"slider-thumb",className:"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50"},t))]})}},19477:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{s:()=>k});var l=s(60687),a=s(36990),n=s(43210),i=s.n(n),o=s(18116),c=s(85726),d=s(49153),m=s(11860),x=s(36907),u=s(63934),h=s(47033),p=s(14952),f=s(4780),g=s(82120),b=s(29523),j=s(173),N=s(26001),w=s(88920),v=e([j]);function k({isOpen:e,onClose:t,toolCalls:s,currentIndex:r,onNavigate:n,messages:v,agentStatus:k,project:y,isLoading:z=!1,externalNavigateToIndex:S,agentName:C,onFileClick:A}){let[E,_]=i().useState(""),[$,T]=i().useState(0),[R,L]=i().useState("live"),[F,P]=i().useState([]),[D,O]=i().useState(!1),I=(0,g.a)(),W=i().useCallback(()=>{t()},[t]),U=Math.min($,Math.max(0,F.length-1)),V=F[U],J=V?.toolCall,B=F.length,M=F.filter(e=>e.toolCall.toolResult?.content&&"STREAMING"!==e.toolCall.toolResult.content),q=M.length,Z=J,G=U,H=B,Q=J?.toolResult?.content==="STREAMING";if(Q&&q>0)Z=M[M.length-1].toolCall,G=q-1,H=q;else if(!Q){let e=M.findIndex(e=>e.id===V?.id);e>=0&&(G=e,H=q)}Z?.assistantCall?.name,(0,a.S8)(J?.assistantCall?.name||"unknown");let X=Z?.toolResult?.content==="STREAMING",Y=!!X||(e=>{let t=e?.toolResult?.content;if(!t)return e?.toolResult?.isSuccess??!0;let s=e=>{try{return"string"==typeof e?JSON.parse(e):e}catch{return null}},r=s(t);if(!r)return e?.toolResult?.isSuccess??!0;if(r.content){let e=s(r.content);if(e?.tool_execution?.result?.success!==void 0)return e.tool_execution.result.success}let l=r.tool_execution?.result?.success??r.result?.success??r.success;return void 0!==l?l:e?.toolResult?.isSuccess??!0})(Z),K=i().useCallback((e,t="internal")=>{if(e<0||e>=B)return;let s=e===B-1;console.log(`[INTERNAL_NAV] ${t}: ${$} -> ${e}, mode will be: ${s?"live":"manual"}`),T(e),s?L("live"):L("manual"),"user_explicit"===t&&n(e)},[$,B,n]),ee="live"===R,et=i().useCallback(()=>{if(G>0){let e=M[G-1];if(e){let t=F.findIndex(t=>t.id===e.id);t>=0&&(L("manual"),K(t,"user_explicit"))}}},[G,M,F,K]),es=i().useCallback(()=>{if(G<H-1){let e=G+1,t=M[e];if(t){let s=F.findIndex(e=>e.id===t.id);s>=0&&(e===M.length-1?L("live"):L("manual"),K(s,"user_explicit"))}}},[G,H,M,F,K]),er=i().useCallback(()=>{L("live"),K(B-1,"user_explicit")},[B,K]),el=i().useCallback(()=>{L("manual"),K(B-1,"user_explicit")},[B,K]),ea=i().useCallback(()=>{let e="flex items-center justify-center gap-1.5 px-2 py-0.5 rounded-full w-[116px]",t="w-1.5 h-1.5 rounded-full",s="text-xs font-medium";if(ee)if("running"===k)return(0,l.jsxs)("div",{className:`${e} bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800`,children:[(0,l.jsx)("div",{className:`${t} bg-green-500 animate-pulse`}),(0,l.jsx)("span",{className:`${s} text-green-700 dark:text-green-400`,children:"Live Updates"})]});else return(0,l.jsxs)("div",{className:`${e} bg-neutral-50 dark:bg-neutral-900/20 border border-neutral-200 dark:border-neutral-800`,children:[(0,l.jsx)("div",{className:`${t} bg-neutral-500`}),(0,l.jsx)("span",{className:`${s} text-neutral-700 dark:text-neutral-400`,children:"Latest Tool"})]});return"running"===k?(0,l.jsxs)("div",{className:`${e} bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors cursor-pointer`,onClick:er,children:[(0,l.jsx)("div",{className:`${t} bg-green-500 animate-pulse`}),(0,l.jsx)("span",{className:`${s} text-green-700 dark:text-green-400`,children:"Jump to Live"})]}):(0,l.jsxs)("div",{className:`${e} bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors cursor-pointer`,onClick:el,children:[(0,l.jsx)("div",{className:`${t} bg-blue-500`}),(0,l.jsx)("span",{className:`${s} text-blue-700 dark:text-blue-400`,children:"Jump to Latest"})]})},[ee,k,er,el]),en=i().useCallback(([e])=>{let t=M[e];if(t){let s=F.findIndex(e=>e.id===t.id);s>=0&&(e===M.length-1?L("live"):L("manual"),K(s,"user_explicit"))}},[M,F,K]);return e?z?(0,l.jsx)("div",{className:"fixed inset-0 z-30 pointer-events-none",children:(0,l.jsx)("div",{className:"p-4 h-full flex items-stretch justify-end pointer-events-auto",children:(0,l.jsx)("div",{className:(0,f.cn)("border rounded-2xl flex flex-col shadow-2xl bg-background",I?"w-full":"w-[90%] sm:w-[450px] md:w-[500px] lg:w-[550px] xl:w-[650px]"),children:(0,l.jsx)("div",{className:"flex-1 flex flex-col overflow-hidden",children:(0,l.jsxs)("div",{className:"flex flex-col h-full",children:[(0,l.jsx)("div",{className:"pt-4 pl-4 pr-4",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("div",{className:"ml-2 flex items-center gap-2",children:(0,l.jsx)("h2",{className:"text-lg font-medium text-zinc-900 dark:text-zinc-100",children:C?`${C}'s Computer`:"Suna's Computer"})}),(0,l.jsx)(b.$,{variant:"ghost",size:"icon",onClick:W,className:"h-8 w-8",title:"Minimize to floating preview",children:(0,l.jsx)(d.A,{className:"h-4 w-4"})})]})}),(0,l.jsx)("div",{className:"flex-1 p-4 overflow-auto",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)(c.Skeleton,{className:"h-8 w-32"}),(0,l.jsx)(c.Skeleton,{className:"h-20 w-full rounded-md"}),(0,l.jsx)(c.Skeleton,{className:"h-40 w-full rounded-md"}),(0,l.jsx)(c.Skeleton,{className:"h-20 w-full rounded-md"})]})})]})})})})}):(0,l.jsx)(w.N,{mode:"wait",children:e&&(0,l.jsxs)(N.P.div,{layoutId:"tool-panel-float",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{opacity:{duration:.15},layout:{type:"spring",stiffness:400,damping:35}},className:(0,f.cn)("fixed top-2 right-2 bottom-4 border rounded-3xl flex flex-col z-30",I?"left-2":"w-[40vw] sm:w-[450px] md:w-[500px] lg:w-[550px] xl:w-[645px]"),style:{overflow:"hidden"},children:[(0,l.jsx)("div",{className:"flex-1 flex flex-col overflow-hidden bg-card",children:(()=>{if(!Z&&0===F.length)return(0,l.jsxs)("div",{className:"flex flex-col h-full",children:[(0,l.jsx)("div",{className:"pt-4 pl-4 pr-4",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("div",{className:"ml-2 flex items-center gap-2",children:(0,l.jsx)("h2",{className:"text-lg font-medium text-zinc-900 dark:text-zinc-100",children:C?`${C}'s Computer`:"Suna's Computer"})}),(0,l.jsx)(b.$,{variant:"ghost",size:"icon",onClick:W,className:"h-8 w-8",children:(0,l.jsx)(m.A,{className:"h-4 w-4"})})]})}),(0,l.jsx)("div",{className:"flex flex-col items-center justify-center flex-1 p-8",children:(0,l.jsxs)("div",{className:"flex flex-col items-center space-y-4 max-w-sm text-center",children:[(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("div",{className:"w-16 h-16 bg-zinc-100 dark:bg-zinc-800 rounded-full flex items-center justify-center",children:(0,l.jsx)(x.A,{className:"h-8 w-8 text-zinc-400 dark:text-zinc-500"})}),(0,l.jsx)("div",{className:"absolute -bottom-1 -right-1 w-6 h-6 bg-zinc-200 dark:bg-zinc-700 rounded-full flex items-center justify-center",children:(0,l.jsx)("div",{className:"w-2 h-2 bg-zinc-400 dark:text-zinc-500 rounded-full"})})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("h3",{className:"text-lg font-medium text-zinc-900 dark:text-zinc-100",children:"No tool activity"}),(0,l.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400 leading-relaxed",children:"Tool calls and computer interactions will appear here when they're being executed."})]})]})})]});if(!Z&&F.length>0){let e=F.find(e=>e.toolCall.toolResult?.content==="STREAMING");return e&&0===q?(0,l.jsxs)("div",{className:"flex flex-col h-full",children:[(0,l.jsx)("div",{className:"pt-4 pl-4 pr-4",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("div",{className:"ml-2 flex items-center gap-2",children:(0,l.jsx)("h2",{className:"text-lg font-medium text-zinc-900 dark:text-zinc-100",children:C?`${C}'s Computer`:"Suna's Computer"})}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsxs)("div",{className:"px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400 flex items-center gap-1.5",children:[(0,l.jsx)(u.A,{className:"h-3 w-3 animate-spin"}),(0,l.jsx)("span",{children:"Running"})]}),(0,l.jsx)(b.$,{variant:"ghost",size:"icon",onClick:W,className:"h-8 w-8 ml-1",children:(0,l.jsx)(m.A,{className:"h-4 w-4"})})]})]})}),(0,l.jsx)("div",{className:"flex flex-col items-center justify-center flex-1 p-8",children:(0,l.jsxs)("div",{className:"flex flex-col items-center space-y-4 max-w-sm text-center",children:[(0,l.jsx)("div",{className:"relative",children:(0,l.jsx)("div",{className:"w-16 h-16 bg-blue-50 dark:bg-blue-900/20 rounded-full flex items-center justify-center",children:(0,l.jsx)(u.A,{className:"h-8 w-8 text-blue-500 dark:text-blue-400 animate-spin"})})}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("h3",{className:"text-lg font-medium text-zinc-900 dark:text-zinc-100",children:"Tool is running"}),(0,l.jsxs)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400 leading-relaxed",children:[(0,a.qR)(e.toolCall.assistantCall.name||"Tool")," is currently executing. Results will appear here when complete."]})]})]})})]}):(0,l.jsxs)("div",{className:"flex flex-col h-full",children:[(0,l.jsx)("div",{className:"pt-4 pl-4 pr-4",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("div",{className:"ml-2 flex items-center gap-2",children:(0,l.jsx)("h2",{className:"text-lg font-medium text-zinc-900 dark:text-zinc-100",children:C?`${C}'s Computer`:"Suna's Computer"})}),(0,l.jsx)(b.$,{variant:"ghost",size:"icon",onClick:W,className:"h-8 w-8",children:(0,l.jsx)(m.A,{className:"h-4 w-4"})})]})}),(0,l.jsx)("div",{className:"flex-1 p-4 overflow-auto",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)(c.Skeleton,{className:"h-8 w-32"}),(0,l.jsx)(c.Skeleton,{className:"h-20 w-full rounded-md"})]})})]})}let e=(0,l.jsx)(j.iv,{name:Z.assistantCall.name,assistantContent:Z.assistantCall.content,toolContent:Z.toolResult?.content,assistantTimestamp:Z.assistantCall.timestamp,toolTimestamp:Z.toolResult?.timestamp,isSuccess:Y,isStreaming:X,project:y,messages:v,agentStatus:k,currentIndex:G,totalCalls:H,onFileClick:A});return(0,l.jsxs)("div",{className:"flex flex-col h-full",children:[(0,l.jsx)(N.P.div,{layoutId:"tool-panel-content",className:"p-3",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(N.P.div,{layoutId:"tool-icon",className:"ml-2 flex items-center gap-2",children:(0,l.jsx)("h2",{className:"text-lg font-medium text-zinc-900 dark:text-zinc-100",children:C?`${C}'s Computer`:"Suna's Computer"})}),Z.toolResult?.content&&!X&&(0,l.jsx)("div",{className:"flex items-center gap-2",children:(0,l.jsx)(b.$,{variant:"ghost",size:"icon",onClick:W,className:"h-8 w-8 ml-1",title:"Minimize to floating preview",children:(0,l.jsx)(d.A,{className:"h-4 w-4"})})}),X&&(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsxs)("div",{className:"px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400 flex items-center gap-1.5",children:[(0,l.jsx)(u.A,{className:"h-3 w-3 animate-spin"}),(0,l.jsx)("span",{children:"Running"})]}),(0,l.jsx)(b.$,{variant:"ghost",size:"icon",onClick:W,className:"h-8 w-8 ml-1",title:"Minimize to floating preview",children:(0,l.jsx)(d.A,{className:"h-4 w-4"})})]}),!Z.toolResult?.content&&!X&&(0,l.jsx)(b.$,{variant:"ghost",size:"icon",onClick:W,className:"h-8 w-8",title:"Minimize to floating preview",children:(0,l.jsx)(d.A,{className:"h-4 w-4"})})]})}),(0,l.jsx)("div",{className:"flex-1 overflow-auto scrollbar-thin scrollbar-thumb-zinc-300 dark:scrollbar-thumb-zinc-700 scrollbar-track-transparent",children:e})]})})()}),(H>1||Q&&q>0)&&(0,l.jsx)("div",{className:(0,f.cn)("border-t border-zinc-200 dark:border-zinc-800 bg-zinc-50 dark:bg-zinc-900",I?"p-2":"px-4 py-2.5"),children:I?(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)(b.$,{variant:"outline",size:"sm",onClick:et,disabled:G<=0,className:"h-8 px-2.5 text-xs",children:[(0,l.jsx)(h.A,{className:"h-3.5 w-3.5 mr-1"}),(0,l.jsx)("span",{children:"Prev"})]}),(0,l.jsxs)("div",{className:"flex items-center gap-1.5",children:[(0,l.jsxs)("span",{className:"text-xs text-zinc-600 dark:text-zinc-400 font-medium tabular-nums min-w-[44px]",children:[G+1,"/",H]}),ea()]}),(0,l.jsxs)(b.$,{variant:"outline",size:"sm",onClick:es,disabled:G>=H-1,className:"h-8 px-2.5 text-xs",children:[(0,l.jsx)("span",{children:"Next"}),(0,l.jsx)(p.A,{className:"h-3.5 w-3.5 ml-1"})]})]}):(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)(b.$,{variant:"ghost",size:"icon",onClick:et,disabled:G<=0,className:"h-7 w-7 text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-200",children:(0,l.jsx)(h.A,{className:"h-4 w-4"})}),(0,l.jsxs)("span",{className:"text-xs text-zinc-600 dark:text-zinc-400 font-medium tabular-nums px-1 min-w-[44px] text-center",children:[G+1,"/",H]}),(0,l.jsx)(b.$,{variant:"ghost",size:"icon",onClick:es,disabled:G>=H-1,className:"h-7 w-7 text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-200",children:(0,l.jsx)(p.A,{className:"h-4 w-4"})})]}),(0,l.jsx)("div",{className:"flex-1 relative",children:(0,l.jsx)(o.A,{min:0,max:H-1,step:1,value:[G],onValueChange:en,className:"w-full [&>span:first-child]:h-1.5 [&>span:first-child]:bg-zinc-200 dark:[&>span:first-child]:bg-zinc-800 [&>span:first-child>span]:bg-zinc-500 dark:[&>span:first-child>span]:bg-zinc-400 [&>span:first-child>span]:h-1.5"})}),(0,l.jsx)("div",{className:"flex items-center gap-1.5",children:ea()})]})})]},"sidepanel")}):null}j=(v.then?(await v)():v)[0],r()}catch(e){r(e)}})},20244:(e,t,s)=>{s.d(t,{A:()=>y});var r=s(60687);s(43210);var l=s(41312),a=s(33872),n=s(32192),i=s(71057),o=s(25541),c=s(57800),d=s(11437),m=s(5336),x=s(43649),u=s(41862),h=s(84027),p=s(14952),f=s(80375),g=s(83992),b=s(14296),j=s(4780),N=s(44493),w=s(96834),v=s(51555);let k={linkedin:{name:"LinkedIn Data Provider",icon:l.A,color:"from-blue-500 to-blue-600",bgColor:"bg-blue-50 dark:bg-blue-900/20",textColor:"text-blue-700 dark:text-blue-300",borderColor:"border-blue-200 dark:border-blue-800"},twitter:{name:"Twitter Data Provider",icon:a.A,color:"from-sky-400 to-sky-500",bgColor:"bg-sky-50 dark:bg-sky-900/20",textColor:"text-sky-700 dark:text-sky-300",borderColor:"border-sky-200 dark:border-sky-800"},zillow:{name:"Zillow Data Provider",icon:n.A,color:"from-emerald-500 to-emerald-600",bgColor:"bg-emerald-50 dark:bg-emerald-900/20",textColor:"text-emerald-700 dark:text-emerald-300",borderColor:"border-emerald-200 dark:border-emerald-800"},amazon:{name:"Amazon Data Provider",icon:i.A,color:"from-orange-500 to-orange-600",bgColor:"bg-orange-50 dark:bg-orange-900/20",textColor:"text-orange-700 dark:text-orange-300",borderColor:"border-orange-200 dark:border-orange-800"},yahoo_finance:{name:"Yahoo Finance Data Provider",icon:o.A,color:"from-purple-500 to-purple-600",bgColor:"bg-purple-50 dark:bg-purple-900/20",textColor:"text-purple-700 dark:text-purple-300",borderColor:"border-purple-200 dark:border-purple-800"},active_jobs:{name:"Active Jobs Data Provider",icon:c.A,color:"from-indigo-500 to-indigo-600",bgColor:"bg-indigo-50 dark:bg-indigo-900/20",textColor:"text-indigo-700 dark:text-indigo-300",borderColor:"border-indigo-200 dark:border-indigo-800"}};function y({name:e="execute-data-provider-call",assistantContent:t,toolContent:s,assistantTimestamp:l,toolTimestamp:a,isSuccess:n=!0,isStreaming:i=!1}){let{serviceName:o,route:c,payload:y,output:z,actualIsSuccess:S,actualToolTimestamp:C,actualAssistantTimestamp:A}=(0,v.D)(t,s,n,a,l),E=o?.toLowerCase(),_=E&&k[E]?k[E]:k.linkedin,$=_.icon;return(0,r.jsxs)(N.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,r.jsx)(N.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,r.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"relative p-2 rounded-xl bg-gradient-to-br from-blue-500/20 to-blue-600/10 border border-blue-500/20",children:(0,r.jsx)(d.A,{className:"w-5 h-5 text-blue-500 dark:text-blue-400"})}),(0,r.jsx)("div",{children:(0,r.jsx)(N.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:"Data Provider"})})]}),!i&&(0,r.jsxs)(w.E,{variant:"secondary",className:(0,j.cn)("text-xs font-medium",S?"bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-900/20 dark:text-emerald-300 dark:border-emerald-800":"bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800"),children:[S?(0,r.jsx)(m.A,{className:"h-3 w-3 mr-1"}):(0,r.jsx)(x.A,{className:"h-3 w-3 mr-1"}),S?"Executed":"Failed"]})]})}),(0,r.jsx)(N.Wu,{className:"p-0 h-full flex-1 overflow-hidden relative",children:i?(0,r.jsx)("div",{className:"flex flex-col items-center justify-center h-full py-8 px-6",children:(0,r.jsxs)("div",{className:"text-center w-full max-w-xs",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-xl mx-auto mb-4 flex items-center justify-center bg-zinc-100 dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700",children:(0,r.jsx)(u.A,{className:"h-8 w-8 animate-spin text-zinc-500 dark:text-zinc-400"})}),(0,r.jsx)("h3",{className:"text-base font-medium text-zinc-900 dark:text-zinc-100 mb-2",children:"Executing call..."}),(0,r.jsxs)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400",children:["Calling ",o||"data provider"]})]})}):(0,r.jsxs)("div",{className:"p-4 space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 p-4 bg-zinc-50 dark:bg-zinc-900/50 rounded-lg border border-zinc-200 dark:border-zinc-800",children:[(0,r.jsx)("div",{className:(0,j.cn)("w-12 h-12 rounded-lg flex items-center justify-center shadow-sm border-2",`bg-gradient-to-br ${_.color}`,"border-white/20"),children:(0,r.jsx)($,{className:"h-6 w-6 text-white drop-shadow-sm"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-zinc-900 dark:text-zinc-100",children:_.name}),o&&(0,r.jsxs)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400",children:["Service: ",o]})]}),c&&(0,r.jsx)(w.E,{variant:"outline",className:"text-xs font-mono",children:c})]}),z&&!S&&(0,r.jsxs)("div",{className:"p-4 bg-red-50 dark:bg-red-900/10 rounded-lg border border-red-200 dark:border-red-800/50",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 text-red-600 dark:text-red-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-red-800 dark:text-red-300",children:"Execution Failed"})]}),(0,r.jsx)("p",{className:"text-xs text-red-700 dark:text-red-300 font-mono",children:z})]}),y&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-zinc-700 dark:text-zinc-300",children:[(0,r.jsx)(h.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Call Parameters"}),(0,r.jsx)(p.A,{className:"h-3 w-3 text-zinc-400"})]}),(0,r.jsx)("div",{className:"grid gap-3",children:Object.entries(y).map(([e,t])=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white dark:bg-zinc-900 rounded-lg border border-zinc-200 dark:border-zinc-800 hover:bg-zinc-50 dark:hover:bg-zinc-800/50 transition-colors",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"w-2 h-2 rounded-full bg-zinc-300 dark:bg-zinc-600"}),(0,r.jsx)("code",{className:"text-sm font-mono font-medium text-zinc-900 dark:text-zinc-100",children:e})]}),(0,r.jsx)("span",{className:"text-sm text-zinc-600 dark:text-zinc-400 max-w-xs truncate font-mono",children:"string"==typeof t?`"${t}"`:String(t)})]},e))}),(0,r.jsxs)("details",{className:"group",children:[(0,r.jsxs)("summary",{className:"flex items-center gap-2 text-sm font-medium text-zinc-700 dark:text-zinc-300 cursor-pointer hover:text-zinc-900 dark:hover:text-zinc-100 transition-colors",children:[(0,r.jsx)(f.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Raw JSON"}),(0,r.jsx)(p.A,{className:"h-3 w-3 text-zinc-400 group-open:rotate-90 transition-transform"})]}),(0,r.jsx)("div",{className:"mt-3 p-4 bg-zinc-900 dark:bg-zinc-950 rounded-lg border border-zinc-200 dark:border-zinc-800",children:(0,r.jsx)("pre",{className:"text-xs font-mono text-emerald-400 dark:text-emerald-300 overflow-x-auto",children:JSON.stringify(y,null,2)})})]})]}),!o&&!c&&!y&&(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-lg bg-zinc-100 dark:bg-zinc-800 border border-zinc-200 dark:border-zinc-700 flex items-center justify-center mb-3",children:(0,r.jsx)(g.A,{className:"h-6 w-6 text-zinc-400"})}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 animate-spin text-zinc-500 dark:text-zinc-400"}),(0,r.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400",children:"Will be populated when the call is executed..."})]})]})]})}),(0,r.jsxs)("div",{className:"px-4 py-2 h-10 bg-zinc-50/50 dark:bg-zinc-900/50 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,r.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:!i&&o&&(0,r.jsxs)(w.E,{variant:"outline",className:"h-6 py-0.5 text-xs",children:[(0,r.jsx)($,{className:"h-3 w-3 mr-1"}),o]})}),(0,r.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:C&&!i?(0,b.Ey)(C):A?(0,b.Ey)(A):""})]})]})}},21226:(e,t,s)=>{s.d(t,{W:()=>b});var r=s(60687),l=s(43210),a=s(89667),n=s(29523),i=s(96834),o=s(84430),c=s.n(o),d=s(4780),m=s(54220),x=s(3589),u=s(78272),h=s(37911),p=s(98492),f=s(99270),g=s(21342);function b({content:e,className:t}){let[s,o]=(0,l.useState)(""),[b,j]=(0,l.useState)({column:"",direction:null}),[N,w]=(0,l.useState)(new Set),[v,k]=(0,l.useState)(1),[y]=(0,l.useState)(50),z=function(e){if(!e)return{data:[],headers:[],meta:null};try{let t=c().parse(e,{header:!0,skipEmptyLines:!0,dynamicTyping:!0}),s=[];return t.meta&&t.meta.fields&&(s=t.meta.fields||[]),{headers:s,data:t.data,meta:t.meta}}catch(e){return console.error("Error parsing CSV:",e),{headers:[],data:[],meta:null}}}(e),S=0===z.data.length,C=(0,l.useMemo)(()=>{let e=z.data;return s&&(e=e.filter(e=>Object.values(e).some(e=>String(e).toLowerCase().includes(s.toLowerCase())))),b.column&&b.direction&&(e=[...e].sort((e,t)=>{let s=e[b.column],r=t[b.column];if(null==s&&null==r)return 0;if(null==s)return"asc"===b.direction?-1:1;if(null==r)return"asc"===b.direction?1:-1;if("number"==typeof s&&"number"==typeof r)return"asc"===b.direction?s-r:r-s;let l=String(s).toLowerCase(),a=String(r).toLowerCase();return l<a?"asc"===b.direction?-1:1:l>a?"asc"===b.direction?1:-1:0})),e},[z.data,s,b]),A=Math.ceil(C.length/y),E=(v-1)*y,_=C.slice(E,E+y),$=z.headers.filter(e=>!N.has(e)),T=e=>{j(t=>{if(t.column!==e)return{column:e,direction:"asc"};{let s="asc"===t.direction?"desc":"desc"===t.direction?null:"asc";return{column:s?e:"",direction:s}}})},R=e=>{w(t=>{let s=new Set(t);return s.has(e)?s.delete(e):s.add(e),s})},L=e=>b.column!==e?(0,r.jsx)(m.A,{className:"h-3 w-3 text-muted-foreground"}):"asc"===b.direction?(0,r.jsx)(x.A,{className:"h-3 w-3 text-primary"}):(0,r.jsx)(u.A,{className:"h-3 w-3 text-primary"}),F=e=>null==e?"":"number"==typeof e?e.toLocaleString():"boolean"==typeof e?e?"Yes":"No":String(e),P=e=>"number"==typeof e?"text-right font-mono":"boolean"==typeof e?e?"text-green-600 dark:text-green-400":"text-red-600 dark:text-red-400":"";return S?(0,r.jsx)("div",{className:(0,d.cn)("w-full h-full flex items-center justify-center",t),children:(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsx)("div",{className:"w-16 h-16 mx-auto rounded-full bg-muted flex items-center justify-center",children:(0,r.jsx)(h.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-foreground",children:"No Data"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"This CSV file appears to be empty or invalid."})]})]})}):(0,r.jsxs)("div",{className:(0,d.cn)("w-full h-full flex flex-col bg-background",t),children:[(0,r.jsxs)("div",{className:"flex-shrink-0 border-b bg-muted/30 p-4 space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(h.A,{className:"h-5 w-5 text-primary"}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("h3",{className:"font-medium text-foreground",children:"CSV Data"}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:["- ",C.length.toLocaleString()," rows, ",$.length," columns",s&&` (filtered from ${z.data.length.toLocaleString()})`]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(i.E,{variant:"outline",className:"text-xs",children:["Page ",v," of ",A]}),(0,r.jsxs)(g.rI,{children:[(0,r.jsx)(g.ty,{asChild:!0,children:(0,r.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-1"}),"Columns"]})}),(0,r.jsxs)(g.SQ,{align:"end",className:"w-48",children:[(0,r.jsx)("div",{className:"px-2 py-1.5 text-sm font-medium",children:"Show/Hide Columns"}),(0,r.jsx)(g.mB,{}),z.headers.map(e=>(0,r.jsx)(g.hO,{checked:!N.has(e),onCheckedChange:()=>R(e),children:e},e))]})]})]})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,r.jsx)(a.p,{placeholder:"Search data...",value:s,onChange:e=>{o(e.target.value),k(1)},className:"pl-9"})]})]}),(0,r.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,r.jsx)("div",{className:"w-full h-full overflow-auto scrollbar-thin scrollbar-thumb-zinc-300 dark:scrollbar-thumb-zinc-700 scrollbar-track-transparent",children:(0,r.jsxs)("table",{className:"w-full border-collapse table-fixed",style:{minWidth:`${150*$.length}px`},children:[(0,r.jsx)("thead",{className:"bg-muted/50 sticky top-0 z-10",children:(0,r.jsx)("tr",{children:$.map((e,t)=>(0,r.jsx)("th",{className:"px-4 py-3 text-left font-medium border-b border-border bg-muted/50 backdrop-blur-sm",style:{width:"150px",minWidth:"150px"},children:(0,r.jsxs)("button",{onClick:()=>T(e),className:"flex items-center gap-2 hover:text-primary transition-colors group w-full text-left",children:[(0,r.jsx)("span",{className:"truncate",children:e}),(0,r.jsx)("div",{className:"opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0",children:L(e)})]})},e))})}),(0,r.jsxs)("tbody",{children:[_.map((e,t)=>(0,r.jsx)("tr",{className:"border-b border-border hover:bg-muted/30 transition-colors",children:$.map((s,l)=>{let a=e[s];return(0,r.jsx)("td",{className:(0,d.cn)("px-4 py-3 text-sm border-r border-border last:border-r-0",P(a)),style:{width:"150px",minWidth:"150px"},children:(0,r.jsx)("div",{className:"truncate",title:String(a||""),children:F(a)})},`${E+t}-${l}`)})},E+t)),0===_.length&&s&&(0,r.jsx)("tr",{children:(0,r.jsx)("td",{colSpan:$.length,className:"py-8 text-center text-muted-foreground",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("p",{children:['No results found for "',s,'"']}),(0,r.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>o(""),children:"Clear search"})]})})})]})]})})}),A>1&&(0,r.jsx)("div",{className:"flex-shrink-0 border-t bg-muted/30 p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"text-sm text-muted-foreground",children:["Showing ",E+1," to ",Math.min(E+y,C.length)," of ",C.length.toLocaleString()," rows"]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>k(e=>Math.max(1,e-1)),disabled:1===v,children:"Previous"}),(0,r.jsx)("div",{className:"flex items-center gap-1",children:Array.from({length:Math.min(5,A)},(e,t)=>{let s;return s=A<=5||v<=3?t+1:v>=A-2?A-4+t:v-2+t,(0,r.jsx)(n.$,{variant:v===s?"default":"outline",size:"sm",onClick:()=>k(s),className:"w-8 h-8 p-0",children:s},s)})}),(0,r.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>k(e=>Math.min(A,e+1)),disabled:v===A,children:"Next"})]})]})})]})}},22838:(e,t,s)=>{s.d(t,{k:()=>S});var r=s(60687),l=s(43210),a=s(23741),n=s(9005),i=s(50004),o=s(16969),c=s(31158),d=s(5336),m=s(43649),x=s(41862),u=s(14296);let h=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){}return e},p=e=>{let t=h(e);if(!t||"object"!=typeof t)return{filePath:null,description:null,success:void 0,timestamp:void 0,output:void 0};if("tool_execution"in t&&"object"==typeof t.tool_execution){let e=t.tool_execution,s=e.arguments||{},r=e.result?.output;if("string"==typeof r)try{r=JSON.parse(r)}catch(e){}let l={filePath:s.file_path||null,description:t.summary||null,success:e.result?.success,timestamp:e.execution_details?.timestamp,output:"string"==typeof e.result?.output?e.result.output:null};return console.log("SeeImageToolView: Extracted from new format:",{filePath:l.filePath,hasDescription:!!l.description,success:l.success}),l}return"role"in t&&"content"in t?p(t.content):{filePath:null,description:null,success:void 0,timestamp:void 0,output:void 0}};function f(e){return e?e.replace(/\\n/g,"\n").replace(/\\t/g,"	").replace(/\\r/g,"").replace(/\\\\/g,"\\").replace(/\\"/g,'"').replace(/\\'/g,"'").split("\n")[0].trim():e}let g=e=>{let t=(0,u.sD)(e);if(t.toolResult&&t.arguments)return console.log("SeeImageToolView: Extracted from legacy format (extractToolData):",{filePath:t.arguments.file_path}),{filePath:t.arguments.file_path||null,description:null};let s=(0,u.NI)(e);if(!s)return{filePath:null,description:null};let r=function(e){let t=(0,u.NI)(e);if(!t)return null;console.log("Extracting file path from content:",t);try{let e=JSON.parse(t);if(e.content&&"string"==typeof e.content){let t=e.content,s=t.match(/<see-image\s+file_path=["']([^"']+)["'][^>]*><\/see-image>/i);if(s||(s=t.match(/<see-image[^>]*>([^<]+)<\/see-image>/i)))return f(s[1])}}catch(e){}let s=t.match(/<see-image\s+file_path=["']([^"']+)["'][^>]*><\/see-image>/i);if(s||(s=t.match(/<see-image[^>]*>([^<]+)<\/see-image>/i)))return f(s[1]);let r=t.match(/image\s*:\s*["']?([^,"'\s]+\.(jpg|jpeg|png|gif|svg|webp))["']?/i);if(r)return f(r[1]);let l=t.match(/["']?([^,"'\s]+\.(jpg|jpeg|png|gif|svg|webp))["']?/i);return l?f(l[1]):(console.log("No file path found in assistant content"),null)}(s),l=function(e){let t=(0,u.NI)(e);if(!t)return null;try{let e=JSON.parse(t);if(e.content&&"string"==typeof e.content){let t=e.content.split(/<see-image/i);if(t.length>1)return t[0].trim()}}catch(e){}let s=t.split(/<see-image/i);return s.length>1?s[0].trim():null}(s);return console.log("SeeImageToolView: Extracted from legacy format (manual parsing):",{filePath:r,hasDescription:!!l}),{filePath:r,description:l}};var b=s(44493),j=s(96834),N=s(4780),w=s(14901),v=s(58297),k=s(29523),y=s(85726);function z({src:e,alt:t,filePath:s,className:d}){let[m,x]=(0,l.useState)(null),[u,h]=(0,l.useState)(!1),[p,f]=(0,l.useState)(0),[g,b]=(0,l.useState)(!1),[w,z]=(0,l.useState)(1),{session:S}=(0,v.A)();return u?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center w-full h-64 bg-gradient-to-b from-rose-50 to-rose-100 dark:from-rose-950/30 dark:to-rose-900/20 rounded-lg border border-rose-200 dark:border-rose-800 text-rose-700 dark:text-rose-300 shadow-inner",children:[(0,r.jsx)("div",{className:"bg-white dark:bg-black/30 p-3 rounded-full shadow-md mb-3",children:(0,r.jsx)(a.A,{className:"h-8 w-8 text-rose-500 dark:text-rose-400"})}),(0,r.jsx)("p",{className:"text-sm font-medium",children:"Unable to load image"}),(0,r.jsx)("p",{className:"text-xs text-rose-600/70 dark:text-rose-400/70 mt-1 max-w-xs text-center break-all",children:s})]}):m?(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("div",{className:(0,N.cn)("overflow-hidden transition-all duration-300 rounded-3xl border bg-card mb-3",g?"cursor-zoom-out":"cursor-zoom-in"),children:(0,r.jsx)("div",{className:"relative flex items-center justify-center",children:(0,r.jsx)("img",{src:m,alt:t,onClick:()=>{b(!g),z(1)},className:(0,N.cn)("max-w-full object-contain transition-all duration-300 ease-in-out",g?"max-h-[80vh]":"max-h-[500px] hover:scale-[1.01]",d),style:{transform:g?`scale(${w})`:"none"},onError:()=>{p<3?(f(p+1),console.log(`Image load failed (attempt ${p+1}). Trying alternative:`,s),0===p?x(s):1===p?s.startsWith("/")?h(!0):x(`/${s}`):h(!0)):h(!0)}})})}),(0,r.jsxs)("div",{className:"flex items-center justify-between w-full px-2 py-2 bg-zinc-50 dark:bg-zinc-900 rounded-2xl border border-zinc-200 dark:border-zinc-800",children:[(0,r.jsxs)(j.E,{variant:"secondary",className:"bg-white/90 dark:bg-black/70 text-zinc-700 dark:text-zinc-300 shadow-sm",children:[(0,r.jsx)(n.A,{className:"h-3 w-3 mr-1"}),s.split(".").pop()?.toUpperCase()]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(k.$,{variant:"outline",size:"icon",className:"h-8 w-8 rounded-md bg-white dark:bg-zinc-800",onClick:e=>{e.stopPropagation(),z(e=>Math.max(e-.25,.5))},disabled:w<=.5,children:(0,r.jsx)(i.A,{className:"h-4 w-4"})}),(0,r.jsxs)("span",{className:"text-xs font-mono px-2 text-zinc-700 dark:text-zinc-300 min-w-12 text-center",children:[Math.round(100*w),"%"]}),(0,r.jsx)(k.$,{variant:"outline",size:"icon",className:"h-8 w-8 rounded-md bg-white dark:bg-zinc-800",onClick:e=>{e.stopPropagation(),z(e=>Math.min(e+.25,3)),g||b(!0)},disabled:w>=3,children:(0,r.jsx)(o.A,{className:"h-4 w-4"})}),(0,r.jsx)("span",{className:"w-px h-6 bg-zinc-200 dark:bg-zinc-700 mx-2"}),(0,r.jsx)(k.$,{variant:"outline",size:"icon",className:"h-8 w-8 rounded-md bg-white dark:bg-zinc-800",onClick:e=>{if(e.stopPropagation(),!m)return;let t=document.createElement("a");t.href=m,t.download=s.split("/").pop()||"image",document.body.appendChild(t),t.click(),document.body.removeChild(t)},title:"Download image",children:(0,r.jsx)(c.A,{className:"h-4 w-4"})})]})]})]}):(0,r.jsx)("div",{className:"flex py-8 flex-col items-center justify-center w-full h-64 bg-gradient-to-b from-zinc-50 to-zinc-100 dark:from-zinc-900/50 dark:to-zinc-800/30 rounded-lg border-zinc-200 dark:border-zinc-700/50 shadow-inner",children:(0,r.jsxs)("div",{className:"space-y-2 w-full max-w-md py-8",children:[(0,r.jsx)(y.Skeleton,{className:"h-8 w-8 rounded-full mx-auto"}),(0,r.jsx)(y.Skeleton,{className:"h-4 w-48 mx-auto"}),(0,r.jsx)(y.Skeleton,{className:"h-64 w-full rounded-lg mt-4"}),(0,r.jsxs)("div",{className:"flex justify-center gap-2 mt-4",children:[(0,r.jsx)(y.Skeleton,{className:"h-8 w-8 rounded-full"}),(0,r.jsx)(y.Skeleton,{className:"h-8 w-8 rounded-full"}),(0,r.jsx)(y.Skeleton,{className:"h-8 w-8 rounded-full"})]})]})})}function S({assistantContent:e,toolContent:t,assistantTimestamp:s,toolTimestamp:a,isSuccess:i=!0,isStreaming:o=!1,name:c,project:h}){let[f,v]=(0,l.useState)(0),{filePath:k,description:S,output:C,actualIsSuccess:A,actualToolTimestamp:E,actualAssistantTimestamp:_}=function(e,t,s,r,l){let a=null,n=null,i=null,o=s,c=r,d=l,m=p(e),x=p(t);if(console.log("SeeImageToolView: Format detection results:",{assistantNewFormat:{hasFilePath:!!m.filePath,hasDescription:!!m.description},toolNewFormat:{hasFilePath:!!x.filePath,hasDescription:!!x.description}}),m.filePath||m.description)a=m.filePath,n=m.description,i=m.output??null,void 0!==m.success&&(o=m.success),m.timestamp&&(d=m.timestamp),console.log("SeeImageToolView: Using assistant new format data");else if(x.filePath||x.description)a=x.filePath,n=x.description,i=x.output??null,void 0!==x.success&&(o=x.success),x.timestamp&&(c=x.timestamp),console.log("SeeImageToolView: Using tool new format data");else{let s=g(e),r=g(t);a=s.filePath||r.filePath,n=s.description||r.description;let l=function(e){let t=(0,u.NI)(e);if(!t)return{success:!1,message:"No tool result available"};console.log("Parsing tool result content:",t);try{let e=t;try{let s=JSON.parse(t);s.content&&"string"==typeof s.content&&(e=s.content)}catch(e){}let s=e.match(/<tool_result>\s*<see-image>\s*ToolResult\(([^)]+)\)\s*<\/see-image>\s*<\/tool_result>/);if(s){let e,t=s[1],r=t.includes("success=True"),l=t.match(/output="([^"]+)"/),a=l?l[1]:"";if(r&&a){let t=a.match(/Successfully loaded the image ['"]([^'"]+)['"]/i);t&&t[1]&&(e=t[1],console.log("Found file path in tool result:",e))}return{success:r,message:a,filePath:e}}let r=e.match(/<tool_result>\s*<see-image>\s*([^<]+)<\/see-image>\s*<\/tool_result>/);if(r){let e=r[1],t=e.includes("success=True")||e.includes("Successfully"),s=e.match(/['"]([^'"]+\.(jpg|jpeg|png|gif|webp|svg))['"]/)||e.match(/Successfully loaded the image ['"]([^'"]+)['"]/i),l=s?s[1]:void 0;return console.log("Found file path in direct tool result:",l),{success:t,message:t?"Image loaded successfully":"Failed to load image",filePath:l}}if(e.includes("success=True")||e.includes("Successfully")){let t=e.match(/Successfully loaded the image ['"]([^'"]+)['"]/i),s=t?t[1]:void 0;return{success:!0,message:"Image loaded successfully",filePath:s}}if(e.includes("success=False")||e.includes("Failed"))return{success:!1,message:"Failed to load image"}}catch(e){return console.error("Error parsing tool result:",e),{success:!1,message:"Failed to parse tool result"}}return{success:!0,message:"Image loaded"}}(t);l.filePath&&!a&&(a=l.filePath),l.message&&!i&&(i=l.message),console.log("SeeImageToolView: Using legacy format data:",{filePath:a,hasDescription:!!n,hasOutput:!!i})}return console.log("SeeImageToolView: Final extracted data:",{filePath:a,hasDescription:!!n,hasOutput:!!i,actualIsSuccess:o}),{filePath:a,description:n,output:i,actualIsSuccess:o,actualToolTimestamp:c,actualAssistantTimestamp:d}}(e,t,i,a,s);if(console.log("Final file path:",k),!k)return console.log("No file path found, falling back to GenericToolView"),(0,r.jsx)(w.N,{name:c||"see-image",assistantContent:e,toolContent:t,assistantTimestamp:s,toolTimestamp:a,isSuccess:i,isStreaming:o});let $={color:"text-blue-500 dark:text-blue-400",bgColor:"bg-gradient-to-b from-blue-100 to-blue-50 shadow-inner dark:from-blue-800/40 dark:to-blue-900/60 dark:shadow-blue-950/20"},T=function(e,t){if(!e||"STREAMING"===e)return console.error("Invalid image path:",e),"";let s=e.replace(/^['"](.*)['"]$/,"$1");if(s.startsWith("http"))return s;let r="string"==typeof t?.sandbox?t.sandbox:t?.sandbox?.id;if(r){let e=s;return e.startsWith("/workspace")||(e=`/workspace/${e.startsWith("/")?e.substring(1):e}`),`http://localhost:8000/api/sandboxes/${r}/files/content?path=${encodeURIComponent(e)}`}if(t?.sandbox?.sandbox_url){let e=t.sandbox.sandbox_url.replace(/\/$/,""),r=s;r.startsWith("/workspace")||(r=`/workspace/${r.startsWith("/")?r.substring(1):r}`);let l=`${e}${r}`;return console.log("Constructed sandbox URL:",l),l}return console.warn("No sandbox URL or ID available, using path as-is:",s),s}(k,h),R=k.split("/").pop()||k,L=R.split(".").pop()||"",F=["gif","webp"].includes(L.toLowerCase());return(0,r.jsxs)(b.Zp,{className:"flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,r.jsx)(b.aR,{className:"h-14 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-b p-2 px-4 space-y-0",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:(0,N.cn)("relative p-2 rounded-xl bg-gradient-to-br from-blue-500/20 to-blue-600/10 border border-blue-500/20 transition-colors",$.bgColor),children:(0,r.jsx)(n.A,{className:(0,N.cn)("w-5 h-5",$.color)})}),(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(b.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:(0,N.W5)(R,25)}),F&&(0,r.jsx)(j.E,{variant:"outline",className:"ml-2 text-[10px] py-0 px-1.5 h-4 border-amber-300 text-amber-700 dark:border-amber-700 dark:text-amber-400",children:"ANIMATED"})]})})]}),o?(0,r.jsxs)(j.E,{variant:"secondary",className:"bg-gradient-to-b from-blue-50 to-blue-100 text-blue-700 border border-blue-200/50 dark:from-blue-900/30 dark:to-blue-800/20 dark:text-blue-400 dark:border-blue-800/30 px-2.5 py-1 flex items-center gap-1.5",children:[(0,r.jsx)(x.A,{className:"h-3.5 w-3.5 animate-spin"}),"Loading image..."]}):(0,r.jsx)(j.E,{variant:"secondary",className:(0,N.cn)("px-2.5 py-1 transition-colors flex items-center gap-1.5",A?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300"),children:A?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.A,{className:"h-3.5 w-3.5"}),"Success"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.A,{className:"h-3.5 w-3.5"}),"Failed"]})})]})}),(0,r.jsx)(b.Wu,{className:"p-0 flex-1 overflow-hidden relative",children:o?(0,r.jsx)("div",{className:"flex flex-col items-center justify-center h-full p-12 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:(0,r.jsxs)("div",{className:"text-center w-full max-w-xs",children:[(0,r.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,r.jsx)(y.Skeleton,{className:"h-12 w-12 rounded-full mx-auto"}),(0,r.jsx)(y.Skeleton,{className:"h-6 w-32 mx-auto"}),(0,r.jsx)(y.Skeleton,{className:"h-4 w-48 mx-auto"})]}),(0,r.jsx)(y.Skeleton,{className:"h-48 w-full rounded-lg mb-6"}),(0,r.jsx)("div",{className:"w-full h-2 bg-zinc-200 dark:bg-zinc-700 rounded-full overflow-hidden",children:(0,r.jsx)("div",{className:"h-full bg-gradient-to-r from-blue-400 to-blue-500 dark:from-blue-600 dark:to-blue-400 rounded-full transition-all duration-300 ease-out",style:{width:`${f}%`}})}),(0,r.jsxs)("p",{className:"text-xs text-zinc-400 dark:text-zinc-500 mt-2",children:[f,"%"]})]})}):(0,r.jsx)("div",{className:"flex flex-col",children:(0,r.jsx)("div",{className:"relative w-full overflow-hidden p-6 flex items-center justify-center",children:(0,r.jsx)(z,{src:T,alt:S||R,filePath:k,className:"max-w-full max-h-[500px] object-contain"})})})}),(0,r.jsxs)("div",{className:"h-10 px-4 py-2 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:[(0,r.jsxs)(j.E,{className:"py-0.5 h-6 bg-gradient-to-b from-blue-50 to-blue-100 text-blue-700 border border-blue-200/50 dark:from-blue-900/30 dark:to-blue-800/20 dark:text-blue-400 dark:border-blue-800/30",children:[(0,r.jsx)(n.A,{className:"h-3 w-3 mr-1"}),"IMAGE"]}),L&&(0,r.jsx)(j.E,{variant:"outline",className:"py-0 px-1.5 h-5 text-[10px] uppercase font-medium",children:L})]}),(0,r.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:E&&!o?(0,u.Ey)(E):_?(0,u.Ey)(_):""})]})]})}},23562:(e,t,s)=>{s.d(t,{k:()=>n});var r=s(60687);s(43210);var l=s(25177),a=s(4780);function n({className:e,value:t,...s}){return(0,r.jsx)(l.bL,{"data-slot":"progress",className:(0,a.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...s,children:(0,r.jsx)(l.C1,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})})}},24345:(e,t,s)=>{s.d(t,{L:()=>g});var r=s(60687);s(43210);var l=s(36907),a=s(5336),n=s(43649),i=s(25334),o=s(14296);let c=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){}return e},d=e=>{let t=c(e);if(!t||"object"!=typeof t)return{port:null,url:null,message:null,success:void 0,timestamp:void 0};if("tool_execution"in t&&"object"==typeof t.tool_execution){let e=t.tool_execution,s=e.arguments||{},r=e.result?.output;if("string"==typeof r)try{r=JSON.parse(r)}catch(e){}let l={port:s.port?parseInt(s.port,10):r?.port?parseInt(r.port,10):null,url:r?.url||null,message:r?.message||t.summary||null,success:e.result?.success,timestamp:e.execution_details?.timestamp};return console.log("ExposePortToolView: Extracted from new format:",{port:l.port,hasUrl:!!l.url,hasMessage:!!l.message,success:l.success}),l}return"role"in t&&"content"in t?d(t.content):{port:null,url:null,message:null,success:void 0,timestamp:void 0}},m=e=>{let t=(0,o.NI)(e);if(!t)return null;try{let e=t.match(/<expose-port>\s*(\d+)\s*<\/expose-port>/);return e?parseInt(e[1],10):null}catch(e){return console.error("Failed to extract port number:",e),null}},x=e=>{let t=(0,o.sD)(e);if(t.toolResult&&t.arguments)return console.log("ExposePortToolView: Extracted from legacy format (extractToolData):",{port:t.arguments.port}),{port:t.arguments.port?parseInt(t.arguments.port,10):null,url:null,message:null};let s=(0,o.NI)(e);if(!s)return{port:null,url:null,message:null};try{let e=JSON.parse(s);if(e.url&&e.port)return{port:parseInt(e.port,10),url:e.url,message:e.message||null}}catch(e){}try{let e=s.match(/ToolResult\(success=(?:True|true),\s*output='((?:[^'\\]|\\.)*)'\)/);if(e){let t=e[1];t=t.replace(/\\\\n/g,"\n").replace(/\\\\"/g,'"').replace(/\\n/g,"\n").replace(/\\"/g,'"').replace(/\\'/g,"'").replace(/\\\\/g,"\\");let s=JSON.parse(t);return{port:s.port?parseInt(s.port,10):null,url:s.url||null,message:s.message||null}}let t=s.match(/output='([^']+)'/);if(t){let e=t[1].replace(/\\n/g,"\n").replace(/\\"/g,'"'),s=JSON.parse(e);return{port:s.port?parseInt(s.port,10):null,url:s.url||null,message:s.message||null}}return{port:null,url:null,message:null}}catch(e){return console.error("Failed to parse tool content:",e),console.error("Tool content was:",s),{port:null,url:null,message:null}}};var u=s(44493),h=s(96834),p=s(42692),f=s(91394);function g({assistantContent:e,toolContent:t,isSuccess:s=!0,isStreaming:c=!1,assistantTimestamp:g,toolTimestamp:b}){let{port:j,url:N,message:w,actualIsSuccess:v,actualToolTimestamp:k}=function(e,t,s,r,l){let a=null,n=null,i=null,o=s,c=r,u=l,h=d(e),p=d(t);if(console.log("ExposePortToolView: Format detection results:",{assistantNewFormat:{hasPort:!!h.port,hasUrl:!!h.url,hasMessage:!!h.message},toolNewFormat:{hasPort:!!p.port,hasUrl:!!p.url,hasMessage:!!p.message}}),h.port||h.url||h.message)a=h.port,n=h.url,i=h.message,void 0!==h.success&&(o=h.success),h.timestamp&&(u=h.timestamp),console.log("ExposePortToolView: Using assistant new format data");else if(p.port||p.url||p.message)a=p.port,n=p.url,i=p.message,void 0!==p.success&&(o=p.success),p.timestamp&&(c=p.timestamp),console.log("ExposePortToolView: Using tool new format data");else{let s=x(e),r=x(t);if(a=s.port||r.port,n=s.url||r.url,i=s.message||r.message,!a){let t=m(e);t&&(a=t)}console.log("ExposePortToolView: Using legacy format data:",{port:a,hasUrl:!!n,hasMessage:!!i})}return console.log("ExposePortToolView: Final extracted data:",{port:a,hasUrl:!!n,hasMessage:!!i,actualIsSuccess:o}),{port:a,url:n,message:i,actualIsSuccess:o,actualToolTimestamp:c,actualAssistantTimestamp:u}}(e,t,s,b,g);return(0,r.jsxs)(u.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,r.jsx)(u.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,r.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"relative p-2 rounded-lg bg-gradient-to-br from-green-500/20 to-green-600/10 border border-green-500/20",children:(0,r.jsx)(l.A,{className:"w-5 h-5 text-green-500 dark:text-green-400"})}),(0,r.jsx)("div",{children:(0,r.jsx)(u.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:"Port Exposure"})})]}),!c&&(0,r.jsxs)(h.E,{variant:"secondary",className:v?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[v?(0,r.jsx)(a.A,{className:"h-3.5 w-3.5 mr-1"}):(0,r.jsx)(n.A,{className:"h-3.5 w-3.5 mr-1"}),v?"Port exposed successfully":"Failed to expose port"]})]})}),(0,r.jsx)(u.Wu,{className:"p-0 h-full flex-1 overflow-hidden relative",children:c?(0,r.jsx)(f.G,{icon:l.A,iconColor:"text-emerald-500 dark:text-emerald-400",bgColor:"bg-gradient-to-b from-emerald-100 to-emerald-50 shadow-inner dark:from-emerald-800/40 dark:to-emerald-900/60 dark:shadow-emerald-950/20",title:"Exposing port",filePath:j?.toString(),showProgress:!0}):(0,r.jsx)(p.F,{className:"h-full w-full",children:(0,r.jsxs)("div",{className:"p-4 py-0 my-4 space-y-6",children:[N&&(0,r.jsx)("div",{className:"bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-lg shadow-sm overflow-hidden",children:(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsx)("div",{className:"flex items-start gap-3 mb-3",children:(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-zinc-800 dark:text-zinc-200 mb-2",children:"Exposed URL"}),(0,r.jsxs)("a",{href:N,target:"_blank",rel:"noopener noreferrer",className:"text-md font-medium text-blue-600 dark:text-blue-400 hover:underline flex items-center gap-2 mb-3",children:[N,(0,r.jsx)(i.A,{className:"flex-shrink-0 h-3.5 w-3.5"})]})]})}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-1.5",children:[(0,r.jsx)("div",{className:"text-xs font-medium text-zinc-500 dark:text-zinc-400",children:"Port Details"}),(0,r.jsx)("div",{className:"flex gap-2 flex-wrap",children:(0,r.jsxs)(h.E,{variant:"outline",className:"bg-zinc-50 dark:bg-zinc-800 font-mono",children:["Port: ",j]})})]}),w&&(0,r.jsx)("div",{className:"text-sm text-zinc-600 dark:text-zinc-400",children:w}),(0,r.jsxs)("div",{className:"text-xs bg-amber-50 dark:bg-amber-950/30 border border-amber-100 dark:border-amber-900/50 rounded-md p-3 text-amber-600 dark:text-amber-400 flex items-start gap-2",children:[(0,r.jsx)(n.A,{className:"h-4 w-4 flex-shrink-0 mt-0.5"}),(0,r.jsx)("span",{children:"This URL might only be temporarily available and could expire after some time."})]})]})]})}),!j&&!N&&!c&&(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center py-12 px-6",children:[(0,r.jsx)("div",{className:"w-20 h-20 rounded-full flex items-center justify-center mb-6 bg-gradient-to-b from-zinc-100 to-zinc-50 shadow-inner dark:from-zinc-800/40 dark:to-zinc-900/60",children:(0,r.jsx)(l.A,{className:"h-10 w-10 text-zinc-400 dark:text-zinc-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2 text-zinc-900 dark:text-zinc-100",children:"No Port Information"}),(0,r.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400 text-center max-w-md",children:"No port exposure information is available yet. Use the expose-port command to share a local port."})]})]})})}),(0,r.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,r.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:!c&&j&&(0,r.jsxs)(h.E,{variant:"outline",children:[(0,r.jsx)(l.A,{className:"h-3 w-3 mr-1"}),"Port ",j]})}),(0,r.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:k&&(0,o.Ey)(k)})]})]})}},36851:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{iv:()=>k});var l=s(60687),a=s(43210),n=s(14901),i=s(44149),o=s(88806),c=s(24345),d=s(65024),m=s(87028),x=s(11953),u=s(46545),h=s(15353),p=s(22838),f=s(16895),g=s(52050),b=s(59183),j=s(20244),N=s(8065),w=s(84579),v=e([d,g,b]);[d,g,b]=v.then?(await v)():v;let y={"browser-navigate-to":i.y,"browser-go-back":i.y,"browser-wait":i.y,"browser-click-element":i.y,"browser-input-text":i.y,"browser-send-keys":i.y,"browser-switch-tab":i.y,"browser-close-tab":i.y,"browser-scroll-down":i.y,"browser-scroll-up":i.y,"browser-scroll-to-text":i.y,"browser-get-dropdown-options":i.y,"browser-select-dropdown-option":i.y,"browser-drag-drop":i.y,"browser-click-coordinates":i.y,"execute-command":o.l,"check-command-output":n.N,"terminate-command":f.e,"list-commands":n.N,"create-file":d.z,"delete-file":d.z,"full-file-rewrite":d.z,"read-file":d.z,"str-replace":m.t,"web-search":h.o,"crawl-webpage":x.t,"scrape-webpage":u.I,"execute-data-provider-call":j.A,"get-data-provider-endpoints":N.F,"expose-port":c.L,"see-image":p.k,"call-mcp-tool":n.N,ask:g.$,complete:b.Z,deploy:w.r,default:n.N};class z{constructor(e={}){this.registry={...y},Object.entries(e).forEach(([e,t])=>{void 0!==t&&(this.registry[e]=t)})}register(e,t){this.registry[e]=t}registerMany(e){Object.assign(this.registry,e)}get(e){return this.registry[e]||this.registry.default}has(e){return e in this.registry}getToolNames(){return Object.keys(this.registry).filter(e=>"default"!==e)}clear(){this.registry={default:this.registry.default}}}let S=new z;function k({name:e="default",...t}){let s=(0,a.useMemo)(()=>S.get(e),[e]);return(0,l.jsx)(s,{name:e,...t})}r()}catch(e){r(e)}})},38036:(e,t,s)=>{s.d(t,{$:()=>i});var r=s(14296);let l=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){}return e},a=e=>{let t=l(e);if(!t||"object"!=typeof t)return{text:null,attachments:null,status:null,success:void 0,timestamp:void 0};if("tool_execution"in t&&"object"==typeof t.tool_execution){let e=t.tool_execution,s=e.arguments||{},r=e.result?.output;if("string"==typeof r)try{r=JSON.parse(r)}catch(e){}let l=null;s.attachments&&("string"==typeof s.attachments?l=s.attachments.split(",").map(e=>e.trim()).filter(e=>e.length>0):Array.isArray(s.attachments)&&(l=s.attachments));let a=null;r&&"object"==typeof r&&r.status&&(a=r.status);let n={text:s.text||null,attachments:l,status:a||t.summary||null,success:e.result?.success,timestamp:e.execution_details?.timestamp};return console.log("AskToolView: Extracted from new format:",{hasText:!!n.text,attachmentCount:n.attachments?.length||0,hasStatus:!!n.status,success:n.success}),n}return"role"in t&&"content"in t?a(t.content):{text:null,attachments:null,status:null,success:void 0,timestamp:void 0}},n=e=>{let t=(0,r.sD)(e);if(t.toolResult&&t.arguments){console.log("AskToolView: Extracted from legacy format (extractToolData):",{hasText:!!t.arguments.text,attachmentCount:t.arguments.attachments?Array.isArray(t.arguments.attachments)?t.arguments.attachments.length:1:0});let e=null;return t.arguments.attachments&&(Array.isArray(t.arguments.attachments)?e=t.arguments.attachments:"string"==typeof t.arguments.attachments&&(e=t.arguments.attachments.split(",").map(e=>e.trim()).filter(e=>e.length>0))),{text:t.arguments.text||null,attachments:e,status:null}}let s=(0,r.NI)(e);if(!s)return{text:null,attachments:null,status:null};let l=null,a=s.match(/attachments=["']([^"']*)["']/i);a&&(l=a[1].split(",").map(e=>e.trim()).filter(e=>e.length>0));let n=null,i=s.match(/<ask[^>]*>([^<]*)<\/ask>/i);return i&&(n=i[1].trim()),console.log("AskToolView: Extracted from legacy format (manual parsing):",{hasText:!!n,attachmentCount:l?.length||0}),{text:n,attachments:l,status:null}};function i(e,t,s,r,l){let i=null,o=null,c=null,d=s,m=r,x=l,u=a(e),h=a(t);if(console.log("AskToolView: Format detection results:",{assistantNewFormat:{hasText:!!u.text,attachmentCount:u.attachments?.length||0,hasStatus:!!u.status},toolNewFormat:{hasText:!!h.text,attachmentCount:h.attachments?.length||0,hasStatus:!!h.status}}),u.text||u.attachments||u.status)i=u.text,o=u.attachments,c=u.status,void 0!==u.success&&(d=u.success),u.timestamp&&(x=u.timestamp),console.log("AskToolView: Using assistant new format data");else if(h.text||h.attachments||h.status)i=h.text,o=h.attachments,c=h.status,void 0!==h.success&&(d=h.success),h.timestamp&&(m=h.timestamp),console.log("AskToolView: Using tool new format data");else{let s=n(e),r=n(t);i=s.text||r.text,o=s.attachments||r.attachments,c=s.status||r.status,console.log("AskToolView: Using legacy format data:",{hasText:!!i,attachmentCount:o?.length||0,hasStatus:!!c})}return console.log("AskToolView: Final extracted data:",{hasText:!!i,attachmentCount:o?.length||0,hasStatus:!!c,actualIsSuccess:d}),{text:i,attachments:o,status:c,actualIsSuccess:d,actualToolTimestamp:m,actualAssistantTimestamp:x}}},44149:(e,t,s)=>{s.d(t,{y:()=>j});var r=s(60687),l=s(43210),a=s.n(l),n=s(43649),i=s(39233),o=s(5336),c=s(63934),d=s(25334),m=s(11437),x=s(14296),u=s(36990),h=s(44493),p=s(96834),f=s(29523),g=s(85726);let b=({className:e=""})=>(0,r.jsx)("div",{className:`relative flex items-center justify-center h-full w-full overflow-hidden ${e}`,children:(0,r.jsxs)("div",{className:"h-[60%] flex flex-col gap-6 aspect-square rounded-xl items-center justify-center",children:[(0,r.jsx)(g.Skeleton,{className:"w-full h-full rounded-lg"}),(0,r.jsx)(g.Skeleton,{className:"w-full h-14 rounded-lg"}),(0,r.jsx)(g.Skeleton,{className:"w-full h-14 rounded-lg"})]})});function j({name:e="browser-operation",assistantContent:t,toolContent:s,assistantTimestamp:g,toolTimestamp:j,isSuccess:N=!0,isStreaming:w=!1,project:v,agentStatus:k="idle",messages:y=[],currentIndex:z=0,totalCalls:S=1}){let C,A=(0,x.sD)(t),E=(0,x.sD)(s),_=null;A.toolResult?_=A.url:E.toolResult&&(_=E.url),_||(_=(0,x.J1)(t));let $=(0,x.DS)(e),T=(0,x.Bs)(e),R=null,L=null,[F,P]=a().useState(!0),[D,O]=a().useState(!1);try{let e=(0,u.jD)(s,{}),t=e?.content||s;if(t&&"string"==typeof t){let e=t.match(/ToolResult\([^)]*output='([\s\S]*?)'(?:\s*,|\s*\))/);if(e){let t=e[1];try{let e=t.replace(/\\n/g,"\n").replace(/\\"/g,'"').replace(/\\u([0-9a-fA-F]{4})/g,(e,t)=>String.fromCharCode(parseInt(t,16))),s=JSON.parse(e);s.image_url&&(R=s.image_url),s.message_id&&(C=s.message_id)}catch(e){}}if(!R){let e=t.match(/"image_url":\s*"([^"]+)"/);e&&(R=e[1])}if(!C){let e=t.match(/"message_id":\s*"([^"]+)"/);e&&(C=e[1])}if(!C&&!R){let e=t.match(/\boutput='(.*?)'(?=\s*\))/),s=e?e[1]:null;if(s){let e=s.replace(/\\n/g,"\n").replace(/\\"/g,'"'),t=(0,u.jD)(e,{});C=t?.message_id,R=t?.image_url||null}}}else t&&"object"==typeof t&&(R=t&&"tool_execution"in t&&"result"in t.tool_execution&&"output"in t.tool_execution.result&&"image_url"in t.tool_execution.result.output&&"string"==typeof t.tool_execution.result.output.image_url?t.tool_execution.result.output.image_url:null)}catch(e){}if(!R&&!L&&C&&y.length>0){let e=y.find(e=>"browser_state"===e.type&&e.message_id===C);if(e){let t=(0,u.jD)(e.content,{});L=t?.screenshot_base64||null,R=t?.image_url||null}}let I=v?.sandbox?.vnc_preview?`${v.sandbox.vnc_preview}/vnc_lite.html?password=${v?.sandbox?.pass}&autoconnect=true&scale=local&width=1024&height=768`:void 0,W=w||"running"===k,U=(0,l.useMemo)(()=>I?(0,r.jsx)("iframe",{src:I,title:"Browser preview",className:"w-full h-full border-0 min-h-[600px]",style:{width:"100%",height:"100%",minHeight:"600px"}}):null,[I]),[V,J]=a().useState(100),B=()=>{P(!1),O(!1)},M=()=>{P(!1),O(!0)};return(0,r.jsxs)(h.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,r.jsx)(h.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,r.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"relative p-2 rounded-lg bg-gradient-to-br from-purple-500/20 to-purple-600/10 border border-purple-500/20",children:(0,r.jsx)(i.A,{className:"w-5 h-5 text-purple-500 dark:text-purple-400"})}),(0,r.jsx)("div",{children:(0,r.jsx)(h.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:T})})]}),!W&&(0,r.jsxs)(p.E,{variant:"secondary",className:N?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[N?(0,r.jsx)(o.A,{className:"h-3.5 w-3.5 mr-1"}):(0,r.jsx)(n.A,{className:"h-3.5 w-3.5 mr-1"}),N?"Browser action completed":"Browser action failed"]}),W&&(0,r.jsxs)(p.E,{className:"bg-gradient-to-b from-blue-200 to-blue-100 text-blue-700 dark:from-blue-800/50 dark:to-blue-900/60 dark:text-blue-300",children:[(0,r.jsx)(c.A,{className:"h-3.5 w-3.5 animate-spin"}),"Executing browser action"]})]})}),(0,r.jsx)(h.Wu,{className:"p-0 flex-1 overflow-hidden relative",style:{height:"calc(100vh - 150px)",minHeight:"600px"},children:(0,r.jsx)("div",{className:"flex-1 flex h-full items-stretch bg-white dark:bg-black",children:z===S-1?W&&U?(0,r.jsx)("div",{className:"flex flex-col items-center justify-center w-full h-full min-h-[600px]",style:{minHeight:"600px"},children:(0,r.jsxs)("div",{className:"relative w-full h-full min-h-[600px]",style:{minHeight:"600px"},children:[U,(0,r.jsx)("div",{className:"absolute top-4 right-4 z-10",children:(0,r.jsxs)(p.E,{className:"bg-blue-500/90 text-white border-none shadow-lg animate-pulse",children:[(0,r.jsx)(c.A,{className:"h-3 w-3 animate-spin"}),$," in progress"]})})]})}):R||L?R?(0,r.jsxs)("div",{className:"flex items-center justify-center w-full h-full min-h-[600px] relative p-4",style:{minHeight:"600px"},children:[F&&(0,r.jsx)(b,{}),(0,r.jsx)(h.Zp,{className:`p-0 overflow-hidden border ${F?"hidden":"block"}`,children:(0,r.jsx)("img",{src:R,alt:"Browser Screenshot",className:"max-w-full max-h-full object-contain",onLoad:B,onError:M})}),D&&!F&&(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-zinc-50 dark:bg-zinc-900",children:(0,r.jsxs)("div",{className:"text-center text-zinc-500 dark:text-zinc-400",children:[(0,r.jsx)(n.A,{className:"h-8 w-8 mx-auto mb-2"}),(0,r.jsx)("p",{children:"Failed to load screenshot"})]})})]}):L?(0,r.jsxs)("div",{className:"flex items-center justify-center w-full h-full min-h-[600px] relative p-4",style:{minHeight:"600px"},children:[F&&(0,r.jsx)(b,{}),(0,r.jsx)(h.Zp,{className:`overflow-hidden border ${F?"hidden":"block"}`,children:(0,r.jsx)("img",{src:`data:image/jpeg;base64,${L}`,alt:"Browser Screenshot",className:"max-w-full max-h-full object-contain",onLoad:B,onError:M})}),D&&!F&&(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-zinc-50 dark:bg-zinc-900",children:(0,r.jsxs)("div",{className:"text-center text-zinc-500 dark:text-zinc-400",children:[(0,r.jsx)(n.A,{className:"h-8 w-8 mx-auto mb-2"}),(0,r.jsx)("p",{children:"Failed to load screenshot"})]})})]}):null:U?(0,r.jsx)("div",{className:"flex flex-col items-center justify-center w-full h-full min-h-[600px]",style:{minHeight:"600px"},children:U}):(0,r.jsxs)("div",{className:"p-8 flex flex-col items-center justify-center w-full bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900 text-zinc-700 dark:text-zinc-400",children:[(0,r.jsx)("div",{className:"w-20 h-20 rounded-full flex items-center justify-center mb-6 bg-gradient-to-b from-purple-100 to-purple-50 shadow-inner dark:from-purple-800/40 dark:to-purple-900/60",children:(0,r.jsx)(i.A,{className:"h-10 w-10 text-purple-400 dark:text-purple-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2 text-zinc-900 dark:text-zinc-100",children:"Browser preview not available"}),_&&(0,r.jsx)("div",{className:"mt-4",children:(0,r.jsx)(f.$,{variant:"outline",size:"sm",className:"bg-white dark:bg-zinc-900 border-zinc-200 dark:border-zinc-700 shadow-sm hover:shadow-md transition-shadow",asChild:!0,children:(0,r.jsxs)("a",{href:_,target:"_blank",rel:"noopener noreferrer",children:[(0,r.jsx)(d.A,{className:"h-3.5 w-3.5 mr-2"}),"Visit URL"]})})})]}):R||L?(0,r.jsxs)("div",{className:"flex items-center justify-center w-full h-full overflow-auto relative p-4",children:[F&&(0,r.jsx)(b,{}),(0,r.jsx)(h.Zp,{className:`p-0 overflow-hidden border ${F?"hidden":"block"}`,children:R?(0,r.jsx)("img",{src:R,alt:"Browser Screenshot",className:"max-w-full max-h-full object-contain",onLoad:B,onError:M}):(0,r.jsx)("img",{src:`data:image/jpeg;base64,${L}`,alt:"Browser Screenshot",className:"max-w-full max-h-full object-contain",onLoad:B,onError:M})}),D&&!F&&(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center bg-zinc-50 dark:bg-zinc-900",children:(0,r.jsxs)("div",{className:"text-center text-zinc-500 dark:text-zinc-400",children:[(0,r.jsx)(n.A,{className:"h-8 w-8 mx-auto mb-2"}),(0,r.jsx)("p",{children:"Failed to load screenshot"})]})})]}):(0,r.jsxs)("div",{className:"p-8 h-full flex flex-col items-center justify-center w-full bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900 text-zinc-700 dark:text-zinc-400",children:[(0,r.jsx)("div",{className:"w-20 h-20 rounded-full flex items-center justify-center mb-6 bg-gradient-to-b from-zinc-100 to-zinc-50 shadow-inner dark:from-zinc-800/40 dark:to-zinc-900/60",children:(0,r.jsx)(i.A,{className:"h-10 w-10 text-zinc-400 dark:text-zinc-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2 text-zinc-900 dark:text-zinc-100",children:"No Browser State Available"}),(0,r.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400",children:"Browser state image not found for this action"})]})})}),(0,r.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,r.jsxs)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:[!W&&(0,r.jsxs)(p.E,{className:"h-6 py-0.5",children:[(0,r.jsx)(m.A,{className:"h-3 w-3"}),$]}),_&&(0,r.jsx)("span",{className:"text-xs truncate max-w-[200px] hidden sm:inline-block",children:_})]}),(0,r.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:j&&!W?(0,x.Ey)(j):g?(0,x.Ey)(g):""})]})]})}},46545:(e,t,s)=>{s.d(t,{I:()=>E});var r=s(60687),l=s(43210),a=s(11437),n=s(5336),i=s(43649),o=s(41862),c=s(60),d=s(45583),m=s(10022),x=s(40228),u=s(13964),h=s(70615),p=s(14296);let f=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){}return e},g=e=>{let t=f(e);if(!t||"object"!=typeof t)return{url:null,urls:null,success:void 0,message:null,files:[],urlCount:0,timestamp:void 0};if("tool_execution"in t&&"object"==typeof t.tool_execution){let e=t.tool_execution,s=e.arguments||{},r=e.result?.output;if("string"==typeof r)try{r=JSON.parse(r)}catch(e){}let l=null,a=null;s.urls&&("string"==typeof s.urls?(l=s.urls.split(",").map(e=>e.trim()),a=l?.[0]||null):Array.isArray(s.urls)&&(l=s.urls,a=l?.[0]||null));let n=[],i=0,o="";if("string"==typeof e.result?.output){let t=e.result.output;o=t;let s=t.match(/Successfully scraped (?:all )?(\d+) URLs?/);i=s?parseInt(s[1]):0;let r=t.match(/- ([^\n]+\.json)/g);n=r?r.map(e=>e.replace("- ","")):[]}let c={url:a,urls:l,success:e.result?.success,message:o||t.summary||null,files:n,urlCount:i,timestamp:e.execution_details?.timestamp};return console.log("WebScrapeToolView: Extracted from new format:",{url:c.url,urlCount:c.urlCount,fileCount:c.files.length,success:c.success}),c}return"role"in t&&"content"in t?g(t.content):{url:null,urls:null,success:void 0,message:null,files:[],urlCount:0,timestamp:void 0}},b=e=>{let t=(0,p.NI)(e);if(!t)return null;let s=t.match(/<scrape-webpage[^>]*\s+urls=["']([^"']+)["']/);if(s)return s[1];let r=t.match(/https?:\/\/[^\s<>"]+/);return r?r[0]:null},j=e=>{let t=(0,p.NI)(e);if(!t)return{success:!1,message:"No output received",files:[],urlCount:0};let s=t.match(/output='([^']+)'/),r=s?s[1].replace(/\\n/g,"\n"):t,l=r.match(/Successfully scraped (?:all )?(\d+) URLs?/),a=l?parseInt(l[1]):0,n=r.match(/- ([^\n]+\.json)/g),i=n?n.map(e=>e.replace("- ","")):[];return{success:r.includes("Successfully scraped"),message:r,files:i,urlCount:a}},N=e=>{let t=(0,p.sD)(e);if(t.toolResult&&t.arguments)return console.log("WebScrapeToolView: Extracted from legacy format (extractToolData):",{url:t.url}),{url:t.url||null,urls:t.url?[t.url]:null,success:void 0,message:null,files:[],urlCount:0};let s=(0,p.NI)(e);if(!s)return{url:null,urls:null,success:void 0,message:null,files:[],urlCount:0};let r=b(s),l=j(s);return console.log("WebScrapeToolView: Extracted from legacy format (manual parsing):",{url:r,fileCount:l.files.length,urlCount:l.urlCount}),{url:r,urls:r?[r]:null,success:l.success,message:l.message,files:l.files,urlCount:l.urlCount}};var w=s(4780),v=s(10218),k=s(44493),y=s(96834),z=s(29523),S=s(23562),C=s(42692),A=s(76242);function E({name:e="scrape-webpage",assistantContent:t,toolContent:s,assistantTimestamp:f,toolTimestamp:b,isSuccess:j=!0,isStreaming:E=!1}){let{resolvedTheme:_}=(0,v.D)(),[$,T]=(0,l.useState)(0),[R,L]=(0,l.useState)(null),{url:F,files:P,actualIsSuccess:D,actualToolTimestamp:O,actualAssistantTimestamp:I}=function(e,t,s,r,l){let a=null,n=null,i=!1,o=null,c=[],d=0,m=s,x=r,u=l,h=g(e),p=g(t);if(console.log("WebScrapeToolView: Format detection results:",{assistantNewFormat:{hasUrl:!!h.url,fileCount:h.files.length,urlCount:h.urlCount},toolNewFormat:{hasUrl:!!p.url,fileCount:p.files.length,urlCount:p.urlCount}}),h.url||h.files.length>0||h.urlCount>0)a=h.url,n=h.urls,i=h.success||!1,o=h.message,c=h.files,d=h.urlCount,void 0!==h.success&&(m=h.success),h.timestamp&&(u=h.timestamp),console.log("WebScrapeToolView: Using assistant new format data");else if(p.url||p.files.length>0||p.urlCount>0)a=p.url,n=p.urls,i=p.success||!1,o=p.message,c=p.files,d=p.urlCount,void 0!==p.success&&(m=p.success),p.timestamp&&(x=p.timestamp),console.log("WebScrapeToolView: Using tool new format data");else{let s=N(e),r=N(t);a=s.url||r.url,n=s.urls||r.urls,i=s.success||r.success||!1,o=s.message||r.message,c=s.files.length>0?s.files:r.files,d=s.urlCount>0?s.urlCount:r.urlCount,console.log("WebScrapeToolView: Using legacy format data:",{url:a,fileCount:c.length,urlCount:d})}return console.log("WebScrapeToolView: Final extracted data:",{url:a,fileCount:c.length,urlCount:d,actualIsSuccess:m}),{url:a,urls:n,success:i,message:o,files:c,urlCount:d,actualIsSuccess:m,actualToolTimestamp:x,actualAssistantTimestamp:u}}(t,s,j,b,f),W=(0,p.Bs)(e),U=F?(e=>{try{return new URL(e).hostname.replace("www.","")}catch(t){return e}})(F):"Unknown",V=F?(e=>{try{let t=new URL(e).hostname;return`https://www.google.com/s2/favicons?domain=${t}&sz=128`}catch(e){return null}})(F):null,J=async e=>{try{await navigator.clipboard.writeText(e),L(e),setTimeout(()=>L(null),2e3)}catch(e){console.error("Failed to copy:",e)}},B=e=>{let t=e.match(/(\d{8}_\d{6})/),s=e.match(/(\w+)_com\.json$/),r=e.split("/").pop()||e;return{timestamp:t?t[1]:"",domain:s?s[1]:"unknown",fileName:r,fullPath:e}};return(0,r.jsxs)(k.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,r.jsx)(k.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,r.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"relative p-2 rounded-lg bg-gradient-to-br from-primary/20 to-primary/10 border border-primary/20",children:(0,r.jsx)(a.A,{className:"w-5 h-5 text-primary"})}),(0,r.jsx)("div",{children:(0,r.jsx)(k.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:W})})]}),!E&&(0,r.jsxs)(y.E,{variant:"secondary",className:D?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[D?(0,r.jsx)(n.A,{className:"h-3.5 w-3.5"}):(0,r.jsx)(i.A,{className:"h-3.5 w-3.5"}),D?"Scraping completed":"Scraping failed"]})]})}),(0,r.jsx)(k.Wu,{className:"p-0 h-full flex-1 overflow-hidden relative",children:E?(0,r.jsx)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:(0,r.jsxs)("div",{className:"text-center w-full max-w-xs",children:[(0,r.jsx)("div",{className:"w-16 h-16 rounded-full mx-auto mb-6 flex items-center justify-center bg-gradient-to-br from-primary/20 to-primary/10 border border-primary/20",children:(0,r.jsx)(o.A,{className:"h-8 w-8 animate-spin text-primary"})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-zinc-900 dark:text-zinc-100 mb-2",children:"Extracting Content"}),(0,r.jsxs)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400 mb-6",children:["Analyzing and processing ",(0,r.jsx)("span",{className:"font-mono text-xs break-all",children:U})]}),(0,r.jsx)(S.k,{value:$,className:"w-full h-1"}),(0,r.jsxs)("p",{className:"text-xs text-zinc-400 dark:text-zinc-500 mt-2",children:[$,"% complete"]})]})}):F?(0,r.jsx)(C.F,{className:"h-full w-full",children:(0,r.jsxs)("div",{className:"p-4 py-0 my-4",children:[(0,r.jsxs)("div",{className:"space-y-3 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-zinc-800 dark:text-zinc-200",children:[(0,r.jsx)(a.A,{className:"w-4 h-4 text-zinc-500 dark:text-zinc-400"}),"Source URL"]}),(0,r.jsx)("div",{className:"group relative",children:(0,r.jsxs)("div",{className:"flex items-center gap-3 p-4 bg-zinc-50 dark:bg-zinc-900 hover:bg-zinc-100 dark:hover:bg-zinc-800 transition-colors rounded-xl border border-zinc-200 dark:border-zinc-800",children:[V&&(0,r.jsx)("img",{src:V,alt:"",className:"w-6 h-6 rounded-md flex-shrink-0",onError:e=>{e.target.style.display="none"}}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"font-mono text-sm text-zinc-900 dark:text-zinc-100 truncate",children:(0,w.W5)(F,70)}),(0,r.jsx)("p",{className:"text-xs text-zinc-500 dark:text-zinc-400 mt-1",children:U})]}),(0,r.jsx)(z.$,{variant:"ghost",size:"sm",className:"opacity-70 group-hover:opacity-100 transition-opacity",asChild:!0,children:(0,r.jsx)("a",{href:F,target:"_blank",rel:"noopener noreferrer",children:(0,r.jsx)(c.A,{className:"w-4 h-4"})})})]})})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-zinc-800 dark:text-zinc-200",children:[(0,r.jsx)(d.A,{className:"w-4 h-4 text-zinc-500 dark:text-zinc-400"}),"Generated Files"]}),(0,r.jsxs)(y.E,{variant:"outline",className:"gap-1",children:[P.length," file",1!==P.length?"s":""]})]}),P.length>0?(0,r.jsx)("div",{className:"space-y-3",children:P.map((e,t)=>{let s=B(e),l=R===e;return(0,r.jsx)("div",{className:"group relative bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-xl p-4 hover:border-zinc-300 dark:hover:border-zinc-700 transition-all duration-200 hover:shadow-sm",children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)("div",{className:"w-10 h-10 rounded-lg bg-gradient-to-br from-green-500/20 to-green-600/10 flex items-center justify-center border border-green-500/20 flex-shrink-0",children:(0,r.jsx)(m.A,{className:"w-5 h-5 text-green-600 dark:text-green-400"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0 space-y-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 flex-wrap",children:[(0,r.jsx)(y.E,{variant:"outline",className:"text-xs font-normal",children:"JSON"}),s.timestamp&&(0,r.jsxs)(y.E,{variant:"outline",className:"text-xs font-normal",children:[(0,r.jsx)(x.A,{className:"w-3 h-3 mr-1"}),s.timestamp.replace("_"," ")]})]}),(0,r.jsxs)("div",{className:"space-y-1",children:[(0,r.jsx)("p",{className:"font-mono text-sm text-zinc-900 dark:text-zinc-100 font-medium",children:s.fileName}),(0,r.jsx)("p",{className:"font-mono text-xs text-zinc-500 dark:text-zinc-400 truncate",children:s.fullPath})]})]}),(0,r.jsx)(A.Bc,{children:(0,r.jsxs)(A.m_,{children:[(0,r.jsx)(A.k$,{asChild:!0,children:(0,r.jsx)(z.$,{variant:"ghost",size:"sm",className:(0,w.cn)("opacity-0 group-hover:opacity-100 transition-all duration-200",l&&"opacity-100"),onClick:()=>J(e),children:l?(0,r.jsx)(u.A,{className:"w-4 h-4 text-green-600"}):(0,r.jsx)(h.A,{className:"w-4 h-4"})})}),(0,r.jsx)(A.ZI,{children:(0,r.jsx)("p",{children:l?"Copied!":"Copy file path"})})]})})]})},t)})}):(0,r.jsxs)("div",{className:"text-center py-8 text-zinc-500 dark:text-zinc-400",children:[(0,r.jsx)(m.A,{className:"w-8 h-8 mx-auto mb-2 opacity-50"}),(0,r.jsx)("p",{className:"text-sm",children:"No files generated"})]})]})]})}):(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:[(0,r.jsx)("div",{className:"w-20 h-20 rounded-full flex items-center justify-center mb-6 bg-gradient-to-b from-zinc-100 to-zinc-50 shadow-inner dark:from-zinc-800/40 dark:to-zinc-900/60",children:(0,r.jsx)(a.A,{className:"h-10 w-10 text-zinc-400 dark:text-zinc-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2 text-zinc-900 dark:text-zinc-100",children:"No URL Detected"}),(0,r.jsx)("p",{className:"text-zinc-500 dark:text-zinc-400 text-center max-w-sm",children:"Unable to extract a valid URL from the scraping request"})]})}),(0,r.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,r.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:!E&&P.length>0&&(0,r.jsxs)(y.E,{className:"h-6 py-0.5",children:[(0,r.jsx)("div",{className:"w-2 h-2 rounded-full bg-green-500 mr-1.5"}),P.length," file",1!==P.length?"s":""," saved"]})}),(0,r.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:O&&!E?(0,p.Ey)(O):I?(0,p.Ey)(I):""})]})]})}},51555:(e,t,s)=>{s.d(t,{$:()=>d,D:()=>c});var r=s(14296);let l=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){}return e},a=e=>{let t=l(e);if(!t||"object"!=typeof t)return{serviceName:null,route:null,payload:null,endpoints:null,success:void 0,timestamp:void 0,output:void 0};if("tool_execution"in t&&"object"==typeof t.tool_execution){let e=t.tool_execution,s=e.arguments||{},r=e.result?.output;if("string"==typeof r)try{r=JSON.parse(r)}catch(e){}let l={serviceName:s.service_name||null,route:s.route||null,payload:s.payload||s,endpoints:r||null,success:e.result?.success,timestamp:e.execution_details?.timestamp,output:"string"==typeof e.result?.output?e.result.output:null};return console.log("DataProviderToolView: Extracted from new format:",{serviceName:l.serviceName,route:l.route,hasPayload:!!l.payload,hasEndpoints:!!l.endpoints,success:l.success}),l}return"role"in t&&"content"in t?a(t.content):{serviceName:null,route:null,payload:null,endpoints:null,success:void 0,timestamp:void 0,output:void 0}},n=e=>{let t=e.match(/<execute-data-provider-call\b(?=[^>]*\bservice_name="([^"]+)")(?=[^>]*\broute="([^"]+)")[^>]*>/),s=null,r=null;t&&(s=t[1],r=t[2]);let l=e.match(/<execute-data-provider-call\b[^>]*>\s*(\{[\s\S]*?\})\s*<\/execute-data-provider-call>/),a=null;if(l){let e=l[1].trim();e=e.replace(/\\"/g,'"');try{a=JSON.parse(e)}catch(t){console.error("Failed to parse JSON content:",t),console.error("JSON string was:",e),a=e}}return{serviceName:s,route:r,jsonContent:a}},i=e=>{let t=e.match(/<get-data-provider-endpoints\s+service_name="([^"]+)"\s*>/);return t?t[1]:null},o=e=>{let t=(0,r.sD)(e);if(t.toolResult&&t.arguments)return console.log("DataProviderToolView: Extracted from legacy format (extractToolData):",{serviceName:t.arguments.service_name,route:t.arguments.route}),{serviceName:t.arguments.service_name||null,route:t.arguments.route||null,payload:t.arguments,endpoints:null};let s=(0,r.NI)(e);if(!s)return{serviceName:null,route:null,payload:null,endpoints:null};let l=n(s);if(l.serviceName||l.route)return console.log("DataProviderToolView: Extracted from legacy format (parseDataProviderCall):",{serviceName:l.serviceName,route:l.route}),{serviceName:l.serviceName,route:l.route,payload:l.jsonContent,endpoints:null};let a=i(s);return a?(console.log("DataProviderToolView: Extracted service name from legacy format:",a),{serviceName:a,route:null,payload:null,endpoints:null}):{serviceName:null,route:null,payload:null,endpoints:null}};function c(e,t,s,r,l){let n=null,i=null,c=null,d=null,m=s,x=r,u=l,h=a(e),p=a(t);if(console.log("DataProviderCallToolView: Format detection results:",{assistantNewFormat:{hasServiceName:!!h.serviceName,hasRoute:!!h.route,hasPayload:!!h.payload},toolNewFormat:{hasServiceName:!!p.serviceName,hasRoute:!!p.route,hasPayload:!!p.payload}}),h.serviceName||h.route)n=h.serviceName,i=h.route,c=h.payload,d=h.output??null,void 0!==h.success&&(m=h.success),h.timestamp&&(u=h.timestamp),console.log("DataProviderCallToolView: Using assistant new format data");else if(p.serviceName||p.route)n=p.serviceName,i=p.route,c=p.payload,d=p.output??null,void 0!==p.success&&(m=p.success),p.timestamp&&(x=p.timestamp),console.log("DataProviderCallToolView: Using tool new format data");else{let s=o(e),r=o(t);n=s.serviceName||r.serviceName,console.log("DataProviderCallToolView: Using legacy format data:",{serviceName:n,route:i=s.route||r.route,hasPayload:!!(c=s.payload||r.payload)})}return console.log("DataProviderCallToolView: Final extracted data:",{serviceName:n,route:i,hasPayload:!!c,hasOutput:!!d,actualIsSuccess:m}),{serviceName:n,route:i,payload:c,output:d,actualIsSuccess:m,actualToolTimestamp:x,actualAssistantTimestamp:u}}function d(e,t,s,l,n){let c=null,d=null,m=s,x=l,u=n,h=a(e),p=a(t);if(console.log("DataProviderEndpointsToolView: Format detection results:",{assistantNewFormat:{hasServiceName:!!h.serviceName,hasEndpoints:!!h.endpoints},toolNewFormat:{hasServiceName:!!p.serviceName,hasEndpoints:!!p.endpoints}}),h.serviceName||h.endpoints)c=h.serviceName,d=h.endpoints,void 0!==h.success&&(m=h.success),h.timestamp&&(u=h.timestamp),console.log("DataProviderEndpointsToolView: Using assistant new format data");else if(p.serviceName||p.endpoints)c=p.serviceName,d=p.endpoints,void 0!==p.success&&(m=p.success),p.timestamp&&(x=p.timestamp),console.log("DataProviderEndpointsToolView: Using tool new format data");else{let s=o(e),l=o(t);console.log("DataProviderEndpointsToolView: Using legacy format data:",{serviceName:c=s.serviceName||l.serviceName,hasEndpoints:!!(d=s.endpoints||l.endpoints)}),c||(c=(e=>{let t=(0,r.NI)(e),s=i(t||"");if(s)return s.toLowerCase();if(!t)return"linkedin";let l=t.toLowerCase();return l.includes("linkedin")?"linkedin":l.includes("twitter")?"twitter":l.includes("zillow")?"zillow":l.includes("amazon")?"amazon":l.includes("yahoo")||l.includes("finance")?"yahoo_finance":l.includes("jobs")||l.includes("active")?"active_jobs":"linkedin"})(e||t))}return console.log("DataProviderEndpointsToolView: Final extracted data:",{serviceName:c,hasEndpoints:!!d,actualIsSuccess:m}),{serviceName:c,endpoints:d,actualIsSuccess:m,actualToolTimestamp:x,actualAssistantTimestamp:u}}},52050:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{$:()=>j});var l=s(60687);s(43210);var a=s(72341),n=s(5336),i=s(43649),o=s(41862),c=s(3876),d=s(58887),m=s(14296),x=s(38036),u=s(4780),h=s(44493),p=s(96834),f=s(42692),g=s(60329),b=e([g]);function j({name:e="ask",assistantContent:t,toolContent:s,assistantTimestamp:r,toolTimestamp:b,isSuccess:j=!0,isStreaming:N=!1,onFileClick:w,project:v}){let{text:k,attachments:y,status:z,actualIsSuccess:S,actualToolTimestamp:C,actualAssistantTimestamp:A}=(0,x.$)(t,s,j,b,r),E=e=>{let t=e.split("/").pop()||"";return null!==t.match(/\.(jpg|jpeg|png|gif|webp|svg|bmp)$/i)},_=e=>{let t=e.split(".").pop()?.toLowerCase()||"";return"html"===t||"htm"===t||"md"===t||"markdown"===t||"csv"===t||"tsv"===t},$=(0,m.Bs)(e)||"Ask User",T=e=>{w&&w(e)};return(0,l.jsxs)(h.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,l.jsx)(h.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,l.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("div",{className:"relative p-2 rounded-xl bg-gradient-to-br from-blue-500/20 to-blue-600/10 border border-blue-500/20",children:(0,l.jsx)(a.A,{className:"w-5 h-5 text-blue-500 dark:text-blue-400"})}),(0,l.jsx)("div",{children:(0,l.jsx)(h.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:$})})]}),!N&&(0,l.jsxs)(p.E,{variant:"secondary",className:S?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[S?(0,l.jsx)(n.A,{className:"h-3.5 w-3.5 mr-1"}):(0,l.jsx)(i.A,{className:"h-3.5 w-3.5 mr-1"}),S?"Success":"Failed"]}),N&&(0,l.jsxs)(p.E,{className:"bg-gradient-to-b from-blue-200 to-blue-100 text-blue-700 dark:from-blue-800/50 dark:to-blue-900/60 dark:text-blue-300",children:[(0,l.jsx)(o.A,{className:"h-3.5 w-3.5 animate-spin mr-1"}),"Asking user"]})]})}),(0,l.jsx)(h.Wu,{className:"p-0 flex-1 overflow-hidden relative",children:(0,l.jsx)(f.F,{className:"h-full w-full",children:(0,l.jsx)("div",{className:"p-4 space-y-6",children:y&&y.length>0?(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-muted-foreground",children:[(0,l.jsx)(c.A,{className:"h-4 w-4"}),"Files (",y.length,")"]}),(0,l.jsx)("div",{className:(0,u.cn)("grid gap-3",1===y.length?"grid-cols-1":y.length>4?"grid-cols-1 sm:grid-cols-2 md:grid-cols-3":"grid-cols-1 sm:grid-cols-2"),children:y.sort((e,t)=>{let s=E(e),r=E(t),l=_(e),a=_(t);return s&&!r?-1:!s&&r?1:l&&!a?-1:!l&&a?1:0}).map((e,t)=>{let s=E(e),r=_(e),a=y.length%2==1&&y.length>1&&t===y.length-1;return(0,l.jsx)("div",{className:(0,u.cn)("relative group",s?"flex items-center justify-center h-full":"",r?"w-full":""),style:a||r?{gridColumn:"1 / -1"}:void 0,children:(0,l.jsx)(g.m,{filepath:e,onClick:T,sandboxId:v?.sandbox?.id,showPreview:!0,className:(0,u.cn)("w-full",s?"h-auto min-h-[54px]":r?"min-h-[240px] max-h-[400px] overflow-auto":"h-[54px]"),customStyle:s?{width:"100%",height:"auto","--attachment-height":a?"240px":"180px"}:r||a?{gridColumn:"1 / -1"}:{width:"100%"},collapsed:!1,project:v})},t)})})]}):(0,l.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,l.jsx)("div",{className:"w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4",children:(0,l.jsx)(d.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,l.jsx)("h3",{className:"text-lg font-medium text-foreground mb-2",children:"Question Asked"}),(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:"No files attached to this question"})]})})})}),(0,l.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,l.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:(0,l.jsxs)(p.E,{className:"h-6 py-0.5",variant:"outline",children:[(0,l.jsx)(a.A,{className:"h-3 w-3"}),"User Interaction"]})}),(0,l.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:A?(0,m.Ey)(A):""})]})]})}g=(b.then?(await b)():b)[0],r()}catch(e){r(e)}})},59183:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{Z:()=>k});var l=s(60687),a=s(43210),n=s(14719),i=s(5336),o=s(43649),c=s(41862),d=s(44887),m=s(56085),x=s(3876),u=s(25334),h=s(77850),p=s(14296),f=s(4780),g=s(44493),b=s(96834),j=s(42692),N=s(23562),w=s(22816),v=e([w]);function k({name:e="complete",assistantContent:t,toolContent:s,assistantTimestamp:r,toolTimestamp:v,isSuccess:k=!0,isStreaming:y=!1,onFileClick:z}){let[S,C]=(0,a.useState)({}),[A,E]=(0,a.useState)(0),_=(0,p.Bs)(e)||"Task Complete",$=e=>{z&&z(e)};return(0,l.jsxs)(g.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,l.jsx)(g.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,l.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("div",{className:"relative p-2 rounded-lg bg-gradient-to-br from-emerald-500/20 to-emerald-600/10 border border-emerald-500/20",children:(0,l.jsx)(n.A,{className:"w-5 h-5 text-emerald-500 dark:text-emerald-400"})}),(0,l.jsx)("div",{children:(0,l.jsx)(g.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:_})})]}),!y&&(0,l.jsxs)(b.E,{variant:"secondary",className:k?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[k?(0,l.jsx)(i.A,{className:"h-3.5 w-3.5 mr-1"}):(0,l.jsx)(o.A,{className:"h-3.5 w-3.5 mr-1"}),k?"Completed":"Failed"]}),y&&(0,l.jsxs)(b.E,{className:"bg-gradient-to-b from-blue-200 to-blue-100 text-blue-700 dark:from-blue-800/50 dark:to-blue-900/60 dark:text-blue-300",children:[(0,l.jsx)(c.A,{className:"h-3.5 w-3.5 animate-spin mr-1"}),"Completing"]})]})}),(0,l.jsx)(g.Wu,{className:"p-0 flex-1 overflow-hidden relative",children:(0,l.jsx)(j.F,{className:"h-full w-full",children:(0,l.jsxs)("div",{className:"p-4 space-y-6",children:[!y&&k&&!S.summary&&!S.tasksCompleted&&!S.attachments&&(0,l.jsx)("div",{className:"flex justify-center",children:(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("div",{className:"w-20 h-20 rounded-full bg-gradient-to-br from-emerald-100 to-emerald-200 dark:from-emerald-800/40 dark:to-emerald-900/60 flex items-center justify-center",children:(0,l.jsx)(d.A,{className:"h-10 w-10 text-emerald-600 dark:text-emerald-400"})}),(0,l.jsx)("div",{className:"absolute -top-1 -right-1",children:(0,l.jsx)(m.A,{className:"h-5 w-5 text-yellow-500 animate-pulse"})})]})}),S.summary&&(0,l.jsx)("div",{className:"space-y-2",children:(0,l.jsx)("div",{className:"bg-muted/50 rounded-2xl p-4 border border-border",children:(0,l.jsx)(w.o,{className:"text-sm prose prose-sm dark:prose-invert chat-markdown max-w-none [&>:first-child]:mt-0 prose-headings:mt-3",children:S.summary})})}),S.attachments&&S.attachments.length>0&&(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-muted-foreground",children:[(0,l.jsx)(x.A,{className:"h-4 w-4"}),"Files (",S.attachments.length,")"]}),(0,l.jsx)("div",{className:"grid grid-cols-1 gap-2",children:S.attachments.map((e,t)=>{let{icon:s,color:r,bgColor:a}=(0,p.QL)(e),n=e.split("/").pop()||e,i=e.includes("/")?e.substring(0,e.lastIndexOf("/")):"";return(0,l.jsxs)("button",{onClick:()=>$(e),className:"flex items-center gap-3 p-3 bg-muted/30 rounded-lg border border-border/50 hover:bg-muted/50 transition-colors group cursor-pointer text-left",children:[(0,l.jsx)("div",{className:"flex-shrink-0",children:(0,l.jsx)("div",{className:(0,f.cn)("w-10 h-10 rounded-lg bg-gradient-to-br flex items-center justify-center",a),children:(0,l.jsx)(s,{className:(0,f.cn)("h-5 w-5",r)})})}),(0,l.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,l.jsx)("p",{className:"text-sm font-medium text-foreground truncate",children:n}),i&&(0,l.jsx)("p",{className:"text-xs text-muted-foreground truncate",children:i})]}),(0,l.jsx)("div",{className:"flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,l.jsx)(u.A,{className:"h-4 w-4 text-muted-foreground"})})]},t)})})]}),S.tasksCompleted&&S.tasksCompleted.length>0&&(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2 text-sm font-medium text-muted-foreground",children:[(0,l.jsx)(h.A,{className:"h-4 w-4"}),"Tasks Completed"]}),(0,l.jsx)("div",{className:"space-y-2",children:S.tasksCompleted.map((e,t)=>(0,l.jsxs)("div",{className:"flex items-start gap-3 p-3 bg-muted/30 rounded-lg border border-border/50",children:[(0,l.jsx)("div",{className:"mt-1 flex-shrink-0",children:(0,l.jsx)(i.A,{className:"h-4 w-4 text-emerald-500"})}),(0,l.jsx)("div",{className:"flex-1 min-w-0",children:(0,l.jsx)(w.o,{className:"text-sm prose prose-sm dark:prose-invert chat-markdown max-w-none [&>:first-child]:mt-0 [&>:last-child]:mb-0",children:e})})]},t))})]}),y&&(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,l.jsx)("span",{className:"text-muted-foreground",children:"Completing task..."}),(0,l.jsxs)("span",{className:"text-muted-foreground text-xs",children:[A,"%"]})]}),(0,l.jsx)(N.k,{value:A,className:"h-1"})]}),!S.summary&&!S.result&&!S.attachments&&!S.tasksCompleted&&!y&&(0,l.jsxs)("div",{className:"flex flex-col items-center justify-center py-8 text-center",children:[(0,l.jsx)("div",{className:"w-16 h-16 rounded-full bg-muted flex items-center justify-center mb-4",children:(0,l.jsx)(n.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,l.jsx)("h3",{className:"text-lg font-medium text-foreground mb-2",children:"Task Completed"}),(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:"No additional details provided"})]})]})})}),(0,l.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,l.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:(0,l.jsxs)(b.E,{className:"h-6 py-0.5",variant:"outline",children:[(0,l.jsx)(n.A,{className:"h-3 w-3 mr-1"}),"Task Completion"]})}),(0,l.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:v&&!y?(0,p.Ey)(v):r?(0,p.Ey)(r):""})]})]})}w=(v.then?(await v)():v)[0],r()}catch(e){r(e)}})},60669:(e,t,s)=>{s.d(t,{T:()=>u,w:()=>x});var r=s(60687),l=s(43210),a=s(35056),n=s(85629),i=s(3832),o=s(62207),c=s(42692),d=s(4780),m=s(94833);let x=e=>e?e.replace(/\\u([0-9a-fA-F]{4})/g,(e,t)=>String.fromCharCode(parseInt(t,16))).replace(/\\u([0-9a-fA-F]{8})/g,(e,t)=>String.fromCharCode(parseInt(t.substring(0,4),16),parseInt(t.substring(4,8),16))):"",u=(0,l.forwardRef)(({content:e,className:t},s)=>{let l=x(e);return(0,r.jsx)(c.F,{className:(0,d.cn)("w-full h-full rounded-md relative",t),children:(0,r.jsx)("div",{className:"p-4 markdown prose prose-sm dark:prose-invert max-w-none",ref:s,children:(0,r.jsx)(a.oz,{remarkPlugins:[n.A],rehypePlugins:[i.A,o.A],components:{code(e){let{className:t,children:s,...l}=e,a=/language-(\w+)/.exec(t||"");return t&&a?(0,r.jsx)(m.$,{content:String(s).replace(/\n$/,""),language:a?a[1]:""}):(0,r.jsx)("code",{className:t,...l,children:s})},h1:({node:e,...t})=>(0,r.jsx)("h1",{className:"text-2xl font-bold my-4",...t}),h2:({node:e,...t})=>(0,r.jsx)("h2",{className:"text-xl font-bold my-3",...t}),h3:({node:e,...t})=>(0,r.jsx)("h3",{className:"text-lg font-bold my-2",...t}),a:({node:e,...t})=>(0,r.jsx)("a",{className:"text-primary hover:underline",...t}),p:({node:e,...t})=>(0,r.jsx)("p",{className:"my-2 font-sans cjk-text",...t}),ul:({node:e,...t})=>(0,r.jsx)("ul",{className:"list-disc pl-5 my-2",...t}),ol:({node:e,...t})=>(0,r.jsx)("ol",{className:"list-decimal pl-5 my-2",...t}),li:({node:e,...t})=>(0,r.jsx)("li",{className:"my-1",...t}),blockquote:({node:e,...t})=>(0,r.jsx)("blockquote",{className:"border-l-4 border-muted pl-4 italic my-2",...t}),img:({node:e,...t})=>(0,r.jsx)("img",{className:"max-w-full h-auto rounded-md my-2",...t,alt:t.alt||""}),pre:({node:e,...t})=>(0,r.jsx)("pre",{className:"p-0 my-2 bg-transparent",...t}),table:({node:e,...t})=>(0,r.jsx)("table",{className:"w-full border-collapse my-3 text-sm",...t}),th:({node:e,...t})=>(0,r.jsx)("th",{className:"border border-slate-300 dark:border-zinc-700 px-3 py-2 text-left font-semibold bg-slate-100 dark:bg-zinc-800",...t}),td:({node:e,...t})=>(0,r.jsx)("td",{className:"border border-slate-300 dark:border-zinc-700 px-3 py-2 cjk-text",...t})},children:l})})})});u.displayName="MarkdownRenderer"},65024:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.d(t,{z:()=>z});var l=s(60687);s(43210);var a=s(25334),n=s(80375),i=s(13861),o=s(41862),c=s(14296),d=s(60669),m=s(21226),x=s(4780),u=s(10218),h=s(99592),p=s(67314),f=s(44493),g=s(96834),b=s(29523),j=s(85763),N=s(42692),w=s(7072),v=s(14901),k=s(91394),y=e([h]);function z({assistantContent:e,toolContent:t,assistantTimestamp:s,toolTimestamp:r,isSuccess:y=!0,isStreaming:z=!1,name:S,project:C}){let{resolvedTheme:A}=(0,u.D)(),E=(0,w.QF)(S,e),_=(0,w.px)()[E],$=_.icon,T=null,R=null,L=(0,c.sD)(e),F=(0,c.sD)(t);L.toolResult?(T=L.filePath,R=L.fileContent):F.toolResult&&(T=F.filePath,R=F.fileContent),T||(T=(0,c.pn)(e)),R||"delete"===E||(R=z?(0,c.VU)(e,"create"===E?"create-file":"full-file-rewrite")||"":(0,c.FK)(e,"create"===E?"create-file":"full-file-rewrite"));let P=(0,c.Bs)(S||`file-${E}`),D=(0,w.Rz)(T),O=(0,w.WP)(D),I=(0,w.QC)(O),W=w.ZL.markdown(I),U=w.ZL.html(I),V=w.ZL.csv(I),J=(0,w._e)(O),B=(0,w.Bm)(J),M=(0,w.np)(R),q=U&&C?.sandbox?.sandbox_url&&D?(0,p.i)(C.sandbox.sandbox_url,D):void 0,Z=(0,w.I3)(O);return z||D||R?(0,l.jsx)(f.Zp,{className:"flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:(0,l.jsxs)(j.tU,{defaultValue:"preview",className:"w-full h-full",children:[(0,l.jsx)(f.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2 mb-0",children:(0,l.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("div",{className:(0,x.cn)("relative p-2 rounded-lg border",_.gradientBg,_.borderColor),children:(0,l.jsx)($,{className:(0,x.cn)("h-5 w-5",_.color)})}),(0,l.jsx)("div",{children:(0,l.jsx)(f.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:P})})]}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[U&&q&&!z&&(0,l.jsx)(b.$,{variant:"outline",size:"sm",className:"h-8 text-xs bg-white dark:bg-zinc-900 hover:bg-zinc-100 dark:hover:bg-zinc-800",asChild:!0,children:(0,l.jsxs)("a",{href:q,target:"_blank",rel:"noopener noreferrer",children:[(0,l.jsx)(a.A,{className:"h-3.5 w-3.5 mr-1.5"}),"Open in Browser"]})}),(0,l.jsxs)(j.j7,{className:"-mr-2 h-7 bg-zinc-100/70 dark:bg-zinc-800/70 rounded-lg",children:[(0,l.jsxs)(j.Xi,{value:"code",className:"rounded-md data-[state=active]:bg-white dark:data-[state=active]:bg-zinc-900 data-[state=active]:text-primary",children:[(0,l.jsx)(n.A,{className:"h-4 w-4"}),"Source"]}),(0,l.jsxs)(j.Xi,{value:"preview",className:"rounded-md data-[state=active]:bg-white dark:data-[state=active]:bg-zinc-900 data-[state=active]:text-primary",children:[(0,l.jsx)(i.A,{className:"h-4 w-4"}),"Preview"]})]})]})]})}),(0,l.jsxs)(f.Wu,{className:"p-0 -my-2 h-full flex-1 overflow-hidden relative",children:[(0,l.jsx)(j.av,{value:"code",className:"flex-1 h-full mt-0 p-0 overflow-hidden",children:(0,l.jsx)(N.F,{className:"h-screen w-full min-h-0",children:z&&!R?(0,l.jsx)(k.G,{icon:$,iconColor:_.color,bgColor:_.bgColor,title:_.progressMessage,filePath:D||"Processing file...",subtitle:"Please wait while the file is being processed",showProgress:!1}):"delete"===E?(0,l.jsxs)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6",children:[(0,l.jsx)("div",{className:(0,x.cn)("w-20 h-20 rounded-full flex items-center justify-center mb-6",_.bgColor),children:(0,l.jsx)($,{className:(0,x.cn)("h-10 w-10",_.color)})}),(0,l.jsx)("h3",{className:"text-xl font-semibold mb-6 text-zinc-900 dark:text-zinc-100",children:"Delete Operation"}),(0,l.jsx)("div",{className:"bg-zinc-50 dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-lg p-4 w-full max-w-md text-center",children:(0,l.jsx)("code",{className:"text-sm font-mono text-zinc-700 dark:text-zinc-300 break-all",children:D||"Unknown file path"})})]}):R?B?(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-12 border-r border-zinc-200 dark:border-zinc-800 z-10 flex flex-col bg-zinc-50 dark:bg-zinc-900",children:M.map((e,t)=>(0,l.jsx)("div",{className:"h-6 text-right pr-3 text-xs font-mono text-zinc-500 dark:text-zinc-500 select-none",children:t+1},t))}),(0,l.jsx)("div",{className:"pl-12",children:(0,l.jsx)(h.sd,{code:(0,d.w)(R),language:J,className:"text-xs"})})]}):(0,l.jsxs)("div",{className:"min-w-full table",children:[M.map((e,t)=>(0,l.jsxs)("div",{className:(0,x.cn)("table-row transition-colors",_.hoverColor),children:[(0,l.jsx)("div",{className:"table-cell text-right pr-3 pl-6 py-0.5 text-xs font-mono text-zinc-500 dark:text-zinc-500 select-none w-12 border-r border-zinc-200 dark:border-zinc-800 bg-zinc-50 dark:bg-zinc-900",children:t+1}),(0,l.jsx)("div",{className:"table-cell pl-3 py-0.5 pr-4 text-xs font-mono whitespace-pre-wrap text-zinc-800 dark:text-zinc-300",children:(0,d.w)(e)||" "})]},t)),(0,l.jsx)("div",{className:"table-row h-4"})]}):(0,l.jsx)("div",{className:"flex items-center justify-center h-full p-12",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)(Z,{className:"h-12 w-12 mx-auto mb-4 text-zinc-400"}),(0,l.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400",children:"No source code to display"})]})})})}),(0,l.jsx)(j.av,{value:"preview",className:"w-full flex-1 h-full mt-0 p-0 overflow-hidden",children:(0,l.jsxs)(N.F,{className:"h-full w-full min-h-0",children:[z&&!R?(0,l.jsx)(k.G,{icon:$,iconColor:_.color,bgColor:_.bgColor,title:_.progressMessage,filePath:D||"Processing file...",subtitle:"Please wait while the file is being processed",showProgress:!1}):"delete"===E?(0,l.jsxs)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:[(0,l.jsx)("div",{className:(0,x.cn)("w-20 h-20 rounded-full flex items-center justify-center mb-6",_.bgColor),children:(0,l.jsx)($,{className:(0,x.cn)("h-10 w-10",_.color)})}),(0,l.jsx)("h3",{className:"text-xl font-semibold mb-6 text-zinc-900 dark:text-zinc-100",children:"File Deleted"}),(0,l.jsx)("div",{className:"bg-zinc-50 dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-lg p-4 w-full max-w-md text-center mb-4 shadow-sm",children:(0,l.jsx)("code",{className:"text-sm font-mono text-zinc-700 dark:text-zinc-300 break-all",children:D||"Unknown file path"})}),(0,l.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400",children:"This file has been permanently removed"})]}):R?U&&q?(0,l.jsx)("div",{className:"flex flex-col h-[calc(100vh-16rem)]",children:(0,l.jsx)("iframe",{src:q,title:`HTML Preview of ${O}`,className:"flex-grow border-0",sandbox:"allow-same-origin allow-scripts"})}):W?(0,l.jsx)("div",{className:"p-1 py-0 prose dark:prose-invert prose-zinc max-w-none",children:(0,l.jsx)(d.T,{content:(0,d.w)(R)})}):V?(0,l.jsx)("div",{className:"h-full w-full p-4",children:(0,l.jsx)("div",{className:"h-[calc(100vh-17rem)] w-full bg-muted/20 border rounded-xl overflow-auto",children:(0,l.jsx)(m.W,{content:(0,d.w)(R)})})}):(0,l.jsx)("div",{className:"p-4",children:(0,l.jsx)("div",{className:"w-full h-full bg-muted/20 border rounded-xl px-4 py-2 pb-6",children:(0,l.jsx)("pre",{className:"text-sm font-mono text-zinc-800 dark:text-zinc-300 whitespace-pre-wrap break-words",children:(0,d.w)(R)})})}):(0,l.jsx)("div",{className:"flex items-center justify-center h-full p-12",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)(Z,{className:"h-12 w-12 mx-auto mb-4 text-zinc-400"}),(0,l.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400",children:"No content to preview"})]})}),z&&R&&(0,l.jsx)("div",{className:"sticky bottom-4 right-4 float-right mr-4 mb-4",children:(0,l.jsxs)(g.E,{className:"bg-blue-500/90 text-white border-none shadow-lg animate-pulse",children:[(0,l.jsx)(o.A,{className:"h-3 w-3 animate-spin mr-1"}),"Streaming..."]})})]})})]}),(0,l.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,l.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:(0,l.jsxs)(g.E,{variant:"outline",className:"py-0.5 h-6",children:[(0,l.jsx)(Z,{className:"h-3 w-3"}),B?J.toUpperCase():I.toUpperCase()||"TEXT"]})}),(0,l.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:r&&!z?(0,c.Ey)(r):s?(0,c.Ey)(s):""})]})]})}):(0,l.jsx)(v.N,{name:S||`file-${E}`,assistantContent:e,toolContent:t,assistantTimestamp:s,toolTimestamp:r,isSuccess:y,isStreaming:z})}h=(y.then?(await y)():y)[0],r()}catch(e){r(e)}})},83156:(e,t,s)=>{s.d(t,{p:()=>i});var r=s(14296);let l=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){}return e},a=e=>{let t=l(e);if(!t||"object"!=typeof t)return{command:null,output:null,exitCode:null,sessionName:null,cwd:null,completed:null,success:void 0,timestamp:void 0};if("tool_execution"in t&&"object"==typeof t.tool_execution){let e=t.tool_execution,s=e.arguments||{},r=e.result?.output;if("string"==typeof r)try{r=JSON.parse(r)}catch(e){}r=r||{};let l={command:s.command||null,output:r?.output||null,exitCode:r?.exit_code||null,sessionName:s.session_name||r?.session_name||null,cwd:r?.cwd||null,completed:r?.completed||null,success:e.result?.success,timestamp:e.execution_details?.timestamp};return console.log("CommandToolView: Extracted from new format:",{command:l.command,hasOutput:!!l.output,exitCode:l.exitCode,sessionName:l.sessionName,success:l.success}),l}return"role"in t&&"content"in t?a(t.content):{command:null,output:null,exitCode:null,sessionName:null,cwd:null,completed:null,success:void 0,timestamp:void 0}},n=e=>{let t=(0,r.sD)(e);if(t.toolResult){let e=t.arguments||{};return console.log("CommandToolView: Extracted from legacy format (extractToolData):",{command:t.command||e.command,hasOutput:!!t.toolResult.toolOutput}),{command:t.command||e.command||null,output:t.toolResult.toolOutput||null,exitCode:null,sessionName:e.session_name||null,cwd:null,completed:null}}let s=(0,r.Di)(e);return console.log("CommandToolView: Extracted from legacy format (fallback):",{command:s}),{command:s,output:null,exitCode:null,sessionName:null,cwd:null,completed:null}};function i(e,t,s,l,i){let o=null,c=null,d=null,m=null,x=null,u=null,h=s,p=l,f=i,g=a(e),b=a(t);if(console.log("CommandToolView: Format detection results:",{assistantNewFormat:{hasCommand:!!g.command,hasOutput:!!g.output,sessionName:g.sessionName},toolNewFormat:{hasCommand:!!b.command,hasOutput:!!b.output,sessionName:b.sessionName}}),g.command||g.output)o=g.command,c=g.output,d=g.exitCode,m=g.sessionName,x=g.cwd,u=g.completed,void 0!==g.success&&(h=g.success),g.timestamp&&(f=g.timestamp),console.log("CommandToolView: Using assistant new format data");else if(b.command||b.output)o=b.command,c=b.output,d=b.exitCode,m=b.sessionName,x=b.cwd,u=b.completed,void 0!==b.success&&(h=b.success),b.timestamp&&(p=b.timestamp),console.log("CommandToolView: Using tool new format data");else{let s=n(e),r=n(t);o=s.command||r.command,console.log("CommandToolView: Using legacy format data:",{command:o,hasOutput:!!(c=s.output||r.output),sessionName:m=s.sessionName||r.sessionName})}if(!o){let s=(0,r.Di)(e)||(0,r.Di)(t);o=s?.replace(/^suna@computer:~\$\s*/g,"")?.replace(/\\n/g,"")?.replace(/\n/g,"")?.trim()||null}if(!c&&t&&(c=(0,r.AQ)(t)),null===d&&t&&(d=(0,r.Fl)(t)),m||(m=(0,r.Ze)(e)||(0,r.Ze)(t)),c&&"string"==typeof c&&c.includes("exit_code=")&&null===d){let e=c.match(/exit_code=(\d+)/);e&&(d=parseInt(e[1],10))}return{command:o,output:c,exitCode:d,sessionName:m,cwd:x,completed:u,actualIsSuccess:h,actualToolTimestamp:p,actualAssistantTimestamp:f}}},83614:(e,t,s)=>{s.d(t,{y:()=>i});var r=s(60687),l=s(43210),a=s.n(l),n=s(85726);function i({isSidePanelOpen:e=!1,showHeader:t=!0,messageCount:s=3}){return(0,r.jsxs)("div",{className:"flex h-screen",children:[(0,r.jsxs)("div",{className:"flex flex-col flex-1 overflow-hidden transition-all duration-200 ease-in-out",children:[t&&(0,r.jsx)("div",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,r.jsxs)("div",{className:"flex h-14 items-center gap-4 px-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(n.Skeleton,{className:"h-6 w-6 rounded-full"}),(0,r.jsx)(n.Skeleton,{className:"h-5 w-40"})]})}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(n.Skeleton,{className:"h-8 w-8 rounded-full"}),(0,r.jsx)(n.Skeleton,{className:"h-8 w-8 rounded-full"})]})]})}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto px-6 py-4 pb-[5.5rem]",children:(0,r.jsxs)("div",{className:"mx-auto max-w-3xl space-y-6",children:[Array.from({length:s}).map((e,t)=>(0,r.jsx)(a().Fragment,{children:t%2==0?(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)("div",{className:"max-w-[85%] rounded-lg bg-primary/10 px-4 py-3",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(n.Skeleton,{className:"h-4 w-48"}),(0,r.jsx)(n.Skeleton,{className:"h-4 w-32"})]})})}):(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)(n.Skeleton,{className:"flex-shrink-0 w-5 h-5 mt-2 rounded-full"}),(0,r.jsx)("div",{className:"flex-1 space-y-2",children:(0,r.jsx)("div",{className:"max-w-[90%] w-full rounded-lg bg-muted px-4 py-3",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(n.Skeleton,{className:"h-4 w-full max-w-[360px] mb-2"}),(0,r.jsx)(n.Skeleton,{className:"h-4 w-full max-w-[320px] mb-2"}),(0,r.jsx)(n.Skeleton,{className:"h-4 w-full max-w-[290px]"})]}),t%3==1&&(0,r.jsx)("div",{className:"py-1",children:(0,r.jsx)(n.Skeleton,{className:"h-6 w-32 rounded-md"})}),t%3==1&&(0,r.jsxs)("div",{children:[(0,r.jsx)(n.Skeleton,{className:"h-4 w-full max-w-[340px] mb-2"}),(0,r.jsx)(n.Skeleton,{className:"h-4 w-full max-w-[280px]"})]})]})})})]})})},t)),(0,r.jsx)("div",{children:(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[(0,r.jsx)(n.Skeleton,{className:"flex-shrink-0 w-5 h-5 mt-2 rounded-full"}),(0,r.jsx)("div",{className:"flex-1 space-y-2",children:(0,r.jsxs)("div",{className:"flex items-center gap-1.5 py-1",children:[(0,r.jsx)("div",{className:"h-1.5 w-1.5 rounded-full bg-gray-400/50 animate-pulse"}),(0,r.jsx)("div",{className:"h-1.5 w-1.5 rounded-full bg-gray-400/50 animate-pulse delay-150"}),(0,r.jsx)("div",{className:"h-1.5 w-1.5 rounded-full bg-gray-400/50 animate-pulse delay-300"})]})})]})})]})})]}),e&&(0,r.jsx)("div",{className:"hidden sm:block",children:(0,r.jsx)("div",{className:"h-screen w-[450px] border-l",children:(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsx)(n.Skeleton,{className:"h-8 w-32 mb-4"}),(0,r.jsx)(n.Skeleton,{className:"h-20 w-full rounded-md mb-4"}),(0,r.jsx)(n.Skeleton,{className:"h-40 w-full rounded-md"})]})})})]})}},84579:(e,t,s)=>{s.d(t,{r:()=>b});var r=s(60687),l=s(43210),a=s.n(l),n=s(54786),i=s(5336),o=s(43649),c=s(11437),d=s(25334),m=s(24366),x=s(14296),u=s(44493),h=s(96834),p=s(29523),f=s(42692),g=s(91394);function b({name:e="deploy",assistantContent:t,toolContent:s,assistantTimestamp:l,toolTimestamp:b,isSuccess:j=!0,isStreaming:N=!1}){let{name:w,directoryPath:v,deployResult:k,rawContent:y}=function(e,t){let s=null,r=null,l=null,a=null,n=(0,x.NI)(e);if(n)try{let e=JSON.parse(n);e.parameters&&(s=e.parameters.name||null,r=e.parameters.directory_path||null)}catch(l){let e=n.match(/name["']\s*:\s*["']([^"']+)["']/),t=n.match(/directory_path["']\s*:\s*["']([^"']+)["']/);e&&(s=e[1]),t&&(r=t[1])}let i=(0,x.NI)(t);if(i){a=i;try{let e=JSON.parse(i),t=null;if(e.tool_execution&&e.tool_execution.result?(t=e.tool_execution.result,!s&&e.tool_execution.arguments&&(s=e.tool_execution.arguments.name||null,r=e.tool_execution.arguments.directory_path||null)):e.output&&(t=e),t&&(l={message:t.output?.message||null,output:t.output?.output||null,success:void 0===t.success||t.success}).output){let e=l.output.match(/https:\/\/[^\s]+\.pages\.dev[^\s]*/);e&&(l.url=e[0])}}catch(e){l={message:"Deploy completed",output:i,success:!0}}}return{name:s,directoryPath:r,deployResult:l,rawContent:a}}(t,s),z=(0,x.Bs)(e),S=k?.success!==void 0?k.success:j,C=a().useMemo(()=>{if(!k?.output)return[];let e=k.output;return(e=(e=e.replace(/\u001b\[[0-9;]*m/g,"")).replace(/\\n/g,"\n")).split("\n").filter(e=>e.trim().length>0)},[k?.output]);return(0,r.jsxs)(u.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,r.jsx)(u.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,r.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"relative p-2 rounded-lg bg-gradient-to-br from-orange-500/20 to-orange-600/10 border border-orange-500/20",children:(0,r.jsx)(n.A,{className:"w-5 h-5 text-orange-500 dark:text-orange-400"})}),(0,r.jsx)("div",{children:(0,r.jsx)(u.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:z})})]}),!N&&(0,r.jsxs)(h.E,{variant:"secondary",className:S?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[S?(0,r.jsx)(i.A,{className:"h-3.5 w-3.5 mr-1"}):(0,r.jsx)(o.A,{className:"h-3.5 w-3.5 mr-1"}),S?"Deploy successful":"Deploy failed"]})]})}),(0,r.jsx)(u.Wu,{className:"p-0 h-full flex-1 overflow-hidden relative",children:N?(0,r.jsx)(g.G,{icon:n.A,iconColor:"text-orange-500 dark:text-orange-400",bgColor:"bg-gradient-to-b from-orange-100 to-orange-50 shadow-inner dark:from-orange-800/40 dark:to-orange-900/60 dark:shadow-orange-950/20",title:"Deploying website",filePath:w||"Processing deployment...",showProgress:!0}):(0,r.jsx)(f.F,{className:"h-full w-full",children:(0,r.jsx)("div",{className:"p-4",children:S&&k?(0,r.jsxs)("div",{className:"space-y-4",children:[k.url&&(0,r.jsx)("div",{className:"bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-lg shadow-sm",children:(0,r.jsxs)("div",{className:"p-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)("div",{className:"w-6 h-6 rounded bg-emerald-100 dark:bg-emerald-900/30 flex items-center justify-center",children:(0,r.jsx)(c.A,{className:"w-3.5 h-3.5 text-emerald-600 dark:text-emerald-400"})}),(0,r.jsx)("span",{className:"text-sm font-medium text-zinc-900 dark:text-zinc-100",children:"Website Deployed"}),(0,r.jsx)(h.E,{variant:"outline",className:"text-xs h-5 px-1.5 bg-emerald-50 dark:bg-emerald-900/20 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-800",children:"Live"})]}),(0,r.jsx)("div",{className:"bg-zinc-50 dark:bg-zinc-800 rounded p-2 mb-3",children:(0,r.jsx)("code",{className:"text-xs font-mono text-zinc-700 dark:text-zinc-300 break-all",children:k.url})}),(0,r.jsx)(p.$,{asChild:!0,size:"sm",className:"w-full bg-emerald-600 hover:bg-emerald-700 text-white h-8",children:(0,r.jsxs)("a",{href:k.url,target:"_blank",rel:"noopener noreferrer",children:[(0,r.jsx)(d.A,{className:"h-3.5 w-3.5 mr-2"}),"Open Website"]})})]})}),C.length>0&&(0,r.jsxs)("div",{className:"bg-zinc-100 dark:bg-neutral-900 rounded-lg overflow-hidden border border-zinc-200/20",children:[(0,r.jsxs)("div",{className:"bg-accent px-4 py-2 flex items-center gap-2",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 text-zinc-600 dark:text-zinc-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-zinc-700 dark:text-zinc-300",children:"Deployment Log"})]}),(0,r.jsx)("div",{className:"p-4 max-h-96 overflow-auto scrollbar-hide",children:(0,r.jsx)("pre",{className:"text-xs text-zinc-600 dark:text-zinc-300 font-mono whitespace-pre-wrap break-all",children:C.map((e,t)=>(0,r.jsx)("div",{className:"py-0.5",children:e||" "},t))})})]})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsx)(o.A,{className:"h-5 w-5 text-red-600"}),(0,r.jsx)("h3",{className:"font-medium text-red-900 dark:text-red-100",children:"Deployment Failed"})]}),(0,r.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300",children:"The deployment encountered an error. Check the logs below for details."})]}),y&&(0,r.jsxs)("div",{className:"bg-zinc-100 dark:bg-neutral-900 rounded-lg overflow-hidden border border-zinc-200/20",children:[(0,r.jsxs)("div",{className:"bg-accent px-4 py-2 flex items-center gap-2",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 text-zinc-600 dark:text-zinc-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-zinc-700 dark:text-zinc-300",children:"Error Details"})]}),(0,r.jsx)("div",{className:"p-4 max-h-96 overflow-auto scrollbar-hide",children:(0,r.jsx)("pre",{className:"text-xs text-zinc-600 dark:text-zinc-300 font-mono whitespace-pre-wrap break-all",children:y})})]})]})})})})]})}},87028:(e,t,s)=>{s.d(t,{t:()=>R});var r=s(60687),l=s(43210),a=s(5748),n=s(96474),i=s(43649),o=s(29878),c=s(5336),d=s(41862),m=s(78464),x=s(3589),u=s(78272),h=s(63934),p=s(4780),f=s(44493),g=s(96834),b=s(42692),j=s(76242),N=s(29523),w=s(85763);let v=e=>{if(!e)return{filePath:null,oldStr:null,newStr:null};if("string"==typeof e){let t=e.trim();if(!(t.startsWith("{")||t.startsWith("[")))return console.debug("StrReplaceToolView: String content does not look like JSON, skipping parse"),{filePath:null,oldStr:null,newStr:null};try{console.debug("StrReplaceToolView: Attempting to parse JSON string:",e.substring(0,100)+"...");let t=JSON.parse(e);return console.debug("StrReplaceToolView: Successfully parsed JSON:",t),v(t)}catch(t){return console.error("StrReplaceToolView: JSON parse error:",t,"Content:",e.substring(0,200)),{filePath:null,oldStr:null,newStr:null}}}if("object"!=typeof e)return{filePath:null,oldStr:null,newStr:null};if("tool_execution"in e&&"object"==typeof e.tool_execution){let t=e.tool_execution,s=t.arguments||{};return console.debug("StrReplaceToolView: Extracted from new format:",{filePath:s.file_path,oldStr:s.old_str?`${s.old_str.substring(0,50)}...`:null,newStr:s.new_str?`${s.new_str.substring(0,50)}...`:null,success:t.result?.success}),{filePath:s.file_path||null,oldStr:s.old_str||null,newStr:s.new_str||null,success:t.result?.success,timestamp:t.execution_details?.timestamp}}return"role"in e&&"content"in e&&"string"==typeof e.content?(console.debug("StrReplaceToolView: Found role/content structure with string content, parsing..."),v(e.content)):"role"in e&&"content"in e&&"object"==typeof e.content?(console.debug("StrReplaceToolView: Found role/content structure with object content"),v(e.content)):{filePath:null,oldStr:null,newStr:null}},k=(e,t,s,r)=>{let l=t(e);if(l.toolResult){let e=l.arguments||{};return console.debug("StrReplaceToolView: Extracted from legacy format (extractToolData):",{filePath:l.filePath||e.file_path,oldStr:e.old_str?`${e.old_str.substring(0,50)}...`:null,newStr:e.new_str?`${e.new_str.substring(0,50)}...`:null}),{filePath:l.filePath||e.file_path||null,oldStr:e.old_str||null,newStr:e.new_str||null}}let a=s(e),n=r(e);return console.debug("StrReplaceToolView: Extracted from legacy format (fallback):",{filePath:a,oldStr:n.oldStr?`${n.oldStr.substring(0,50)}...`:null,newStr:n.newStr?`${n.newStr.substring(0,50)}...`:null}),{filePath:a,oldStr:n.oldStr,newStr:n.newStr}},y=e=>e.replace(/\\n/g,"\n"),z=(e,t)=>{let s=y(e),r=y(t),l=s.split("\n"),a=r.split("\n"),n=[],i=Math.max(l.length,a.length);for(let e=0;e<i;e++){let t=e<l.length?l[e]:null,s=e<a.length?a[e]:null;t===s?n.push({type:"unchanged",oldLine:t,newLine:s,lineNumber:e+1}):(null!==t&&n.push({type:"removed",oldLine:t,newLine:null,lineNumber:e+1}),null!==s&&n.push({type:"added",oldLine:null,newLine:s,lineNumber:e+1}))}return n},S=(e,t)=>{let s=y(e),r=y(t),l=0;for(;l<s.length&&l<r.length&&s[l]===r[l];)l++;let a=s.length,n=r.length;for(;a>l&&n>l&&s[a-1]===r[n-1];)a--,n--;let i=[];return l>0&&i.push({text:s.substring(0,l),type:"unchanged"}),a>l&&i.push({text:s.substring(l,a),type:"removed"}),n>l&&i.push({text:r.substring(l,n),type:"added"}),a<s.length&&i.push({text:s.substring(a),type:"unchanged"}),i},C=e=>({additions:e.filter(e=>"added"===e.type).length,deletions:e.filter(e=>"removed"===e.type).length});var A=s(14296),E=s(91394);let _=({lineDiff:e})=>(0,r.jsx)("div",{className:"bg-white dark:bg-zinc-950 font-mono text-sm overflow-x-auto -mt-2",children:(0,r.jsx)("table",{className:"w-full border-collapse",children:(0,r.jsx)("tbody",{children:e.map((e,t)=>(0,r.jsxs)("tr",{className:(0,p.cn)("hover:bg-zinc-50 dark:hover:bg-zinc-900","removed"===e.type&&"bg-red-50 dark:bg-red-950/30","added"===e.type&&"bg-emerald-50 dark:bg-emerald-950/30"),children:[(0,r.jsx)("td",{className:"w-10 text-right select-none py-0.5 pr-1 pl-4 text-xs text-zinc-500 dark:text-zinc-400 border-r border-zinc-200 dark:border-zinc-800",children:e.lineNumber}),(0,r.jsxs)("td",{className:"pl-2 py-0.5 w-6 select-none",children:["removed"===e.type&&(0,r.jsx)(a.A,{className:"h-3.5 w-3.5 text-red-500"}),"added"===e.type&&(0,r.jsx)(n.A,{className:"h-3.5 w-3.5 text-emerald-500"})]}),(0,r.jsx)("td",{className:"w-full px-3 py-0.5",children:(0,r.jsxs)("div",{className:"overflow-x-auto max-w-full text-xs",children:["removed"===e.type&&(0,r.jsx)("span",{className:"text-red-700 dark:text-red-400",children:e.oldLine}),"added"===e.type&&(0,r.jsx)("span",{className:"text-emerald-700 dark:text-emerald-400",children:e.newLine}),"unchanged"===e.type&&(0,r.jsx)("span",{className:"text-zinc-700 dark:text-zinc-300",children:e.oldLine})]})})]},t))})})}),$=({lineDiff:e})=>(0,r.jsx)("div",{className:"bg-white dark:bg-zinc-950 font-mono text-sm overflow-x-auto -my-2",children:(0,r.jsxs)("table",{className:"w-full border-collapse",children:[(0,r.jsx)("thead",{children:(0,r.jsxs)("tr",{className:"border-b border-zinc-200 dark:border-zinc-800 text-xs",children:[(0,r.jsx)("th",{className:"p-2 text-left text-zinc-500 dark:text-zinc-400 w-1/2",children:"Removed"}),(0,r.jsx)("th",{className:"p-2 text-left text-zinc-500 dark:text-zinc-400 w-1/2",children:"Added"})]})}),(0,r.jsx)("tbody",{children:e.map((e,t)=>(0,r.jsxs)("tr",{children:[(0,r.jsx)("td",{className:(0,p.cn)("p-2 align-top","removed"===e.type?"bg-red-50 dark:bg-red-950/30 text-red-700 dark:text-red-400":"",null===e.oldLine?"bg-zinc-100 dark:bg-zinc-900":""),children:null!==e.oldLine?(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"w-8 text-right pr-2 select-none text-xs text-zinc-500 dark:text-zinc-400",children:e.lineNumber}),"removed"===e.type&&(0,r.jsx)(a.A,{className:"h-3.5 w-3.5 text-red-500 mt-0.5 mr-2 flex-shrink-0"}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsx)("span",{className:"break-all",children:e.oldLine})})]}):null}),(0,r.jsx)("td",{className:(0,p.cn)("p-2 align-top","added"===e.type?"bg-emerald-50 dark:bg-emerald-950/30 text-emerald-700 dark:text-emerald-400":"",null===e.newLine?"bg-zinc-100 dark:bg-zinc-900":""),children:null!==e.newLine?(0,r.jsxs)("div",{className:"flex",children:[(0,r.jsx)("div",{className:"w-8 text-right pr-2 select-none text-xs text-zinc-500 dark:text-zinc-400",children:e.lineNumber}),"added"===e.type&&(0,r.jsx)(n.A,{className:"h-3.5 w-3.5 text-emerald-500 mt-0.5 mr-2 flex-shrink-0"}),(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsx)("span",{className:"break-all",children:e.newLine})})]}):null})]},t))})]})}),T=()=>(0,r.jsx)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:(0,r.jsxs)("div",{className:"text-center w-full max-w-xs",children:[(0,r.jsx)(i.A,{className:"h-16 w-16 mx-auto mb-6 text-amber-500"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-zinc-900 dark:text-zinc-100 mb-2",children:"Invalid String Replacement"}),(0,r.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400",children:"Could not extract the old string and new string from the request."})]})});function R({name:e="str-replace",assistantContent:t,toolContent:s,assistantTimestamp:p,toolTimestamp:y,isSuccess:R=!0,isStreaming:L=!1}){let[F,P]=(0,l.useState)(!0),[D,O]=(0,l.useState)("unified"),I=null,W=null,U=null,V=R,J=y,B=p,M=v(t),q=v(s);if(M.filePath||M.oldStr||M.newStr)I=M.filePath,W=M.oldStr,U=M.newStr,void 0!==M.success&&(V=M.success),M.timestamp&&(B=M.timestamp);else if(q.filePath||q.oldStr||q.newStr)I=q.filePath,W=q.oldStr,U=q.newStr,void 0!==q.success&&(V=q.success),q.timestamp&&(J=q.timestamp);else{let e=k(t,A.sD,A.pn,A.yq),r=k(s,A.sD,A.pn,A.yq);I=e.filePath||r.filePath,W=e.oldStr||r.oldStr,U=e.newStr||r.newStr}if(I||(I=(0,A.pn)(t)||(0,A.pn)(s)),!W||!U){let e=(0,A.yq)(t),r=(0,A.yq)(s);W=W||e.oldStr||r.oldStr,U=U||e.newStr||r.newStr}let Z=(0,A.Bs)(e),G=W&&U?z(W,U):[];W&&U&&S(W,U);let H=C(G),Q=!L&&(!W||!U)&&(t||s);return(0,r.jsxs)(f.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,r.jsx)(f.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,r.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"relative p-2 rounded-lg bg-gradient-to-br from-purple-500/20 to-purple-600/10 border border-purple-500/20",children:(0,r.jsx)(o.A,{className:"w-5 h-5 text-purple-500 dark:text-purple-400"})}),(0,r.jsx)(f.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:Z})]}),!L&&(0,r.jsxs)(g.E,{variant:"secondary",className:V?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[V?(0,r.jsx)(c.A,{className:"h-3.5 w-3.5 mr-1"}):(0,r.jsx)(i.A,{className:"h-3.5 w-3.5 mr-1"}),V?"Replacement completed":"Replacement failed"]}),L&&(0,r.jsxs)(g.E,{className:"bg-gradient-to-b from-blue-200 to-blue-100 text-blue-700 dark:from-blue-800/50 dark:to-blue-900/60 dark:text-blue-300",children:[(0,r.jsx)(d.A,{className:"h-3.5 w-3.5 animate-spin mr-1"}),"Processing replacement"]})]})}),(0,r.jsx)(f.Wu,{className:"p-0 h-full flex-1 overflow-hidden relative",children:L?(0,r.jsx)(E.G,{icon:o.A,iconColor:"text-purple-500 dark:text-purple-400",bgColor:"bg-gradient-to-b from-purple-100 to-purple-50 shadow-inner dark:from-purple-800/40 dark:to-purple-900/60 dark:shadow-purple-950/20",title:"Processing String Replacement",filePath:I||"Processing file...",progressText:"Analyzing text patterns",subtitle:"Please wait while the replacement is being processed"}):Q?(0,r.jsx)(T,{}):(0,r.jsx)(b.F,{className:"h-full w-full",children:(0,r.jsx)("div",{className:"p-4",children:(0,r.jsxs)("div",{className:"bg-white dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-lg overflow-hidden mb-4",children:[(0,r.jsxs)("div",{className:"p-3 border-b border-zinc-200 dark:border-zinc-800 bg-accent flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2 text-zinc-500 dark:text-zinc-400"}),(0,r.jsx)("code",{className:"text-xs font-mono text-zinc-700 dark:text-zinc-300",children:I||"Unknown file"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("div",{className:"flex items-center text-xs text-zinc-500 dark:text-zinc-400 gap-3",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(n.A,{className:"h-3.5 w-3.5 text-emerald-500 mr-1"}),(0,r.jsx)("span",{children:H.additions})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(a.A,{className:"h-3.5 w-3.5 text-red-500 mr-1"}),(0,r.jsx)("span",{children:H.deletions})]})]}),(0,r.jsx)(j.Bc,{children:(0,r.jsxs)(j.m_,{children:[(0,r.jsx)(j.k$,{asChild:!0,children:(0,r.jsx)(N.$,{variant:"ghost",size:"sm",className:"h-7 w-7 p-0",onClick:()=>P(!F),children:F?(0,r.jsx)(x.A,{className:"h-4 w-4"}):(0,r.jsx)(u.A,{className:"h-4 w-4"})})}),(0,r.jsx)(j.ZI,{children:(0,r.jsx)("p",{children:F?"Collapse":"Expand"})})]})})]})]}),F&&(0,r.jsx)("div",{children:(0,r.jsxs)(w.tU,{value:D,onValueChange:e=>O(e),className:"w-auto",children:[(0,r.jsx)("div",{className:"border-b border-zinc-200 dark:border-zinc-800 bg-zinc-50 dark:bg-zinc-900 p-2 flex justify-end",children:(0,r.jsxs)(w.j7,{className:"h-7 p-0.5",children:[(0,r.jsx)(w.Xi,{value:"unified",className:"text-xs h-6 px-2",children:"Unified"}),(0,r.jsx)(w.Xi,{value:"split",className:"text-xs h-6 px-2",children:"Split"})]})}),(0,r.jsx)(w.av,{value:"unified",className:"m-0 pb-4",children:(0,r.jsx)(_,{lineDiff:G})}),(0,r.jsx)(w.av,{value:"split",className:"m-0",children:(0,r.jsx)($,{lineDiff:G})})]})})]})})})}),(0,r.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center",children:[(0,r.jsxs)("div",{className:"h-full flex items-center gap-2 text-xs text-zinc-500 dark:text-zinc-400",children:[!L&&(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[V?(0,r.jsx)(c.A,{className:"h-3.5 w-3.5 text-emerald-500 mr-1"}):(0,r.jsx)(i.A,{className:"h-3.5 w-3.5 text-red-500 mr-1"}),(0,r.jsx)("span",{children:V?"String replacement successful":"String replacement failed"})]}),L&&(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(h.A,{className:"h-3.5 w-3.5 text-blue-500 animate-spin mr-1"}),(0,r.jsx)("span",{children:"Processing replacement..."})]})]}),(0,r.jsx)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400",children:J&&!L?(0,A.Ey)(J):B?(0,A.Ey)(B):""})]})]})}},88806:(e,t,s)=>{s.d(t,{l:()=>b});var r=s(60687),l=s(43210),a=s.n(l),n=s(24366),i=s(5336),o=s(43649),c=s(63934),d=s(48730),m=s(14296),x=s(10218),u=s(44493),h=s(96834),p=s(42692),f=s(91394),g=s(83156);function b({name:e="execute-command",assistantContent:t,toolContent:s,assistantTimestamp:b,toolTimestamp:j,isSuccess:N=!0,isStreaming:w=!1}){let{resolvedTheme:v}=(0,x.D)(),[k,y]=(0,l.useState)(!0),{command:z,output:S,exitCode:C,sessionName:A,cwd:E,completed:_,actualIsSuccess:$,actualToolTimestamp:T,actualAssistantTimestamp:R}=(0,g.p)(t,s,N,j,b),L="check-command-output"===e?A:z,F="check-command-output"===e?"Session":"Command",P=(0,m.Bs)(e),D=a().useMemo(()=>{if(!S)return[];let e=S;try{if("string"==typeof S&&(S.trim().startsWith("{")||S.trim().startsWith("{"))){let t=JSON.parse(S);t&&"object"==typeof t&&t.output&&(e=t.output)}}catch(e){}return(e=(e=(e=(e=String(e)).replace(/\\\\/g,"\\")).replace(/\\n/g,"\n").replace(/\\t/g,"	").replace(/\\"/g,'"').replace(/\\'/g,"'")).replace(/\\u([0-9a-fA-F]{4})/g,(e,t)=>String.fromCharCode(parseInt(t,16)))).split("\n")},[S]),O=D.length>10,I=D.slice(0,10),W=k?D:I;return(0,r.jsxs)(u.Zp,{className:"gap-0 flex border shadow-none border-t border-b-0 border-x-0 p-0 rounded-none flex-col h-full overflow-hidden bg-card",children:[(0,r.jsx)(u.aR,{className:"h-14 bg-zinc-50/80 dark:bg-zinc-900/80 backdrop-blur-sm border-b p-2 px-4 space-y-2",children:(0,r.jsxs)("div",{className:"flex flex-row items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"relative p-2 rounded-lg bg-gradient-to-br from-purple-500/20 to-purple-600/10 border border-purple-500/20",children:(0,r.jsx)(n.A,{className:"w-5 h-5 text-purple-500 dark:text-purple-400"})}),(0,r.jsx)("div",{children:(0,r.jsx)(u.ZB,{className:"text-base font-medium text-zinc-900 dark:text-zinc-100",children:P})})]}),!w&&(0,r.jsxs)(h.E,{variant:"secondary",className:$?"bg-gradient-to-b from-emerald-200 to-emerald-100 text-emerald-700 dark:from-emerald-800/50 dark:to-emerald-900/60 dark:text-emerald-300":"bg-gradient-to-b from-rose-200 to-rose-100 text-rose-700 dark:from-rose-800/50 dark:to-rose-900/60 dark:text-rose-300",children:[$?(0,r.jsx)(i.A,{className:"h-3.5 w-3.5 mr-1"}):(0,r.jsx)(o.A,{className:"h-3.5 w-3.5 mr-1"}),$?"check-command-output"===e?"Output retrieved successfully":"Command executed successfully":"check-command-output"===e?"Failed to retrieve output":"Command failed"]})]})}),(0,r.jsx)(u.Wu,{className:"p-0 h-full flex-1 overflow-hidden relative",children:w?(0,r.jsx)(f.G,{icon:n.A,iconColor:"text-purple-500 dark:text-purple-400",bgColor:"bg-gradient-to-b from-purple-100 to-purple-50 shadow-inner dark:from-purple-800/40 dark:to-purple-900/60 dark:shadow-purple-950/20",title:"check-command-output"===e?"Checking command output":"Executing command",filePath:L||"Processing command...",showProgress:!0}):L?(0,r.jsx)(p.F,{className:"h-full w-full",children:(0,r.jsxs)("div",{className:"p-4",children:[S&&(0,r.jsx)("div",{className:"mb-4",children:(0,r.jsxs)("div",{className:"bg-zinc-100 dark:bg-neutral-900 rounded-lg overflow-hidden border border-zinc-200/20",children:[(0,r.jsxs)("div",{className:"bg-zinc-300 dark:bg-neutral-800 flex items-center justify-between dark:border-zinc-700/50",children:[(0,r.jsxs)("div",{className:"bg-zinc-200 w-full dark:bg-zinc-800 px-4 py-2 flex items-center gap-2",children:[(0,r.jsx)(n.A,{className:"h-4 w-4 text-zinc-600 dark:text-zinc-400"}),(0,r.jsx)("span",{className:"text-sm font-medium text-zinc-700 dark:text-zinc-300",children:"Terminal output"})]}),null!==C&&0!==C&&(0,r.jsxs)(h.E,{variant:"outline",className:"text-xs h-5 border-red-700/30 text-red-400",children:[(0,r.jsx)(o.A,{className:"h-3 w-3 mr-1"}),"Error"]})]}),(0,r.jsx)("div",{className:"p-4 max-h-96 overflow-auto scrollbar-hide",children:(0,r.jsxs)("pre",{className:"text-xs text-zinc-600 dark:text-zinc-300 font-mono whitespace-pre-wrap break-all overflow-visible",children:[W.map((e,t)=>(0,r.jsx)("div",{className:"py-0.5 bg-transparent",children:e||" "},t)),!k&&O&&(0,r.jsxs)("div",{className:"text-zinc-500 mt-2 border-t border-zinc-700/30 pt-2",children:["+ ",D.length-10," more lines"]})]})})]})}),!S&&!w&&(0,r.jsx)("div",{className:"bg-black rounded-lg overflow-hidden border border-zinc-700/20 shadow-md p-6 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(c.A,{className:"h-8 w-8 text-zinc-500 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-zinc-400 text-sm",children:"No output received"})]})})]})}):(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-full py-12 px-6 bg-gradient-to-b from-white to-zinc-50 dark:from-zinc-950 dark:to-zinc-900",children:[(0,r.jsx)("div",{className:"w-20 h-20 rounded-full flex items-center justify-center mb-6 bg-gradient-to-b from-zinc-100 to-zinc-50 shadow-inner dark:from-zinc-800/40 dark:to-zinc-900/60",children:(0,r.jsx)(n.A,{className:"h-10 w-10 text-zinc-400 dark:text-zinc-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-2 text-zinc-900 dark:text-zinc-100",children:"check-command-output"===e?"No Session Found":"No Command Found"}),(0,r.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400 text-center max-w-md",children:"check-command-output"===e?"No session name was detected. Please provide a valid session name to check.":"No command was detected. Please provide a valid command to execute."})]})}),(0,r.jsxs)("div",{className:"px-4 py-2 h-10 bg-gradient-to-r from-zinc-50/90 to-zinc-100/90 dark:from-zinc-900/90 dark:to-zinc-800/90 backdrop-blur-sm border-t border-zinc-200 dark:border-zinc-800 flex justify-between items-center gap-4",children:[(0,r.jsx)("div",{className:"h-full flex items-center gap-2 text-sm text-zinc-500 dark:text-zinc-400",children:!w&&L&&(0,r.jsxs)(h.E,{variant:"outline",className:"h-6 py-0.5 bg-zinc-50 dark:bg-zinc-900",children:[(0,r.jsx)(n.A,{className:"h-3 w-3 mr-1"}),F]})}),(0,r.jsxs)("div",{className:"text-xs text-zinc-500 dark:text-zinc-400 flex items-center gap-2",children:[(0,r.jsx)(d.A,{className:"h-3.5 w-3.5"}),T&&!w?(0,m.Ey)(T):R?(0,m.Ey)(R):""]})]})]})}},91394:(e,t,s)=>{s.d(t,{G:()=>o});var r=s(60687),l=s(43210),a=s(41862),n=s(4780),i=s(23562);function o({icon:e=a.A,iconColor:t="text-purple-500 dark:text-purple-400",bgColor:s="bg-gradient-to-b from-purple-100 to-purple-50 shadow-inner dark:from-purple-800/40 dark:to-purple-900/60 dark:shadow-purple-950/20",title:o,subtitle:c,filePath:d,showProgress:m=!0,progressText:x,autoProgress:u=!0,initialProgress:h=0}){let[p,f]=(0,l.useState)(h);return(0,r.jsx)("div",{className:"flex flex-col items-center justify-center h-[calc(100vh-15rem)] overflow-hidden scrollbar-hide py-12 px-6",children:(0,r.jsxs)("div",{className:"text-center w-full max-w-sm",children:[(0,r.jsx)("div",{className:(0,n.cn)("w-16 h-16 rounded-full mx-auto mb-6 flex items-center justify-center",s),children:(0,r.jsx)(e,{className:(0,n.cn)("h-8 w-8",t,e===a.A&&"animate-spin")})}),(0,r.jsx)("h3",{className:"text-xl font-semibold mb-4 text-zinc-900 dark:text-zinc-100",children:o}),d&&(0,r.jsx)("div",{className:"bg-zinc-50 dark:bg-zinc-900 border border-zinc-200 dark:border-zinc-800 rounded-lg p-4 w-full text-center mb-6 shadow-sm",children:(0,r.jsx)("code",{className:"text-sm font-mono text-zinc-700 dark:text-zinc-300 break-all",children:d})}),m&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(i.k,{value:Math.min(p,100),className:"w-full h-1"}),(0,r.jsxs)("div",{className:"flex justify-between items-center text-xs text-zinc-500 dark:text-zinc-400",children:[(0,r.jsx)("span",{children:x||"Processing..."}),(0,r.jsxs)("span",{className:"font-mono",children:[Math.round(Math.min(p,100)),"%"]})]})]}),c&&(0,r.jsx)("p",{className:"text-sm text-zinc-500 dark:text-zinc-400 mt-4",children:c})]})})}},94833:(e,t,s)=>{s.d(t,{$:()=>h});var r=s(60687),l=s(43210),a=s(62549),n=s(49083),i=s(79799),o=s(4780),c=s(42692),d=s(96616),m=s(10218),x=s(54129);let u={js:i.cg.javascript,jsx:i.cg.jsx,ts:i.cg.typescript,tsx:i.cg.tsx,html:i.cg.html,css:i.cg.css,json:i.cg.json,md:i.cg.markdown,python:i.cg.python,py:i.cg.python,rust:i.cg.rust,go:i.cg.go,java:i.cg.java,c:i.cg.c,cpp:i.cg.cpp,cs:i.cg.csharp,php:i.cg.php,ruby:i.cg.ruby,sh:i.cg.shell,bash:i.cg.shell,sql:i.cg.sql,yaml:i.cg.yaml,yml:i.cg.yaml};function h({content:e,language:t="",className:s}){let{resolvedTheme:i}=(0,m.D)(),[h,p]=(0,l.useState)(!1),f=[...t&&u[t]?[u[t]()]:[],x.Lz.lineWrapping],g=h&&"dark"===i?n.Ts:d._x;return(0,r.jsx)(c.F,{className:(0,o.cn)("w-full h-full",s),children:(0,r.jsx)("div",{className:"w-full",children:(0,r.jsx)(a.Ay,{value:e,theme:g,extensions:f,basicSetup:{lineNumbers:!1,highlightActiveLine:!1,highlightActiveLineGutter:!1,foldGutter:!1},editable:!1,className:"text-sm w-full min-h-full",style:{maxWidth:"100%"},height:"auto"})})})}},98977:(e,t,s)=>{s(60687),s(43210),s(36990)}};