(()=>{var e={};e.id=9533,e.ids=[9533],e.modules={36:(e,r,t)=>{"use strict";t.d(r,{U:()=>a});var s=t(6475);let a=(0,s.createServerReference)("7f8bed79c8654f95685745e61906af37f96a086236",s.callServer,void 0,s.findSourceMapURL,"createClient")},348:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l});var s=t(60687),a=t(43210),n=t.n(a),o=t(35950),i=t(85814),d=t.n(i),c=t(16189);function l({children:e,params:r}){let{accountSlug:t}=n().use(r),a=(0,c.usePathname)(),i=[{name:"Account",href:`/${t}/settings`},{name:"Members",href:`/${t}/settings/members`},{name:"Bill<PERSON>",href:`/${t}/settings/billing`}];return(0,s.jsxs)("div",{className:"space-y-6 w-full",children:[(0,s.jsx)(o.w,{className:"border-subtle dark:border-white/10"}),(0,s.jsxs)("div",{className:"flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0 w-full max-w-6xl mx-auto px-4",children:[(0,s.jsx)("aside",{className:"lg:w-1/4 p-1",children:(0,s.jsx)("nav",{className:"flex flex-col space-y-1",children:i.map(e=>(0,s.jsx)(d(),{href:e.href,className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${a===e.href?"bg-accent text-accent-foreground":"text-muted-foreground hover:bg-accent/50 hover:text-accent-foreground"}`,children:e.name},e.href))})}),(0,s.jsx)("div",{className:"flex-1 bg-card-bg dark:bg-background-secondary p-6 rounded-2xl border border-subtle dark:border-white/10 shadow-custom",children:e})]})]})}},2990:(e,r,t)=>{Promise.resolve().then(t.bind(t,348))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},4690:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\(teamAccount)\\\\[accountSlug]\\\\settings\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\settings\\layout.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},16902:(e,r,t)=>{Promise.resolve().then(t.bind(t,4690))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30789:(e,r,t)=>{Promise.resolve().then(t.bind(t,73129))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},43941:(e,r,t)=>{Promise.resolve().then(t.bind(t,46873))},46873:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h});var s=t(60687),a=t(43210),n=t.n(a),o=t(89667),i=t(44774),d=t(6475);let c=(0,d.createServerReference)("602190608cb4aada86562e5e5997e6bb44fbe95e4e",d.callServer,void 0,d.findSourceMapURL,"editTeamName");var l=t(80013);function u({account:e}){return(0,s.jsxs)("form",{className:"animate-in",children:[(0,s.jsx)("input",{type:"hidden",name:"accountId",value:e.account_id}),(0,s.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-y-2",children:[(0,s.jsx)(l.Label,{htmlFor:"name",className:"text-sm font-medium text-foreground/90",children:"Team Name"}),(0,s.jsx)(o.p,{defaultValue:e.name,name:"name",id:"name",placeholder:"My Team",required:!0,className:"h-10 rounded-lg border-subtle dark:border-white/10 bg-white dark:bg-background-secondary"})]}),(0,s.jsx)("div",{className:"flex justify-end mt-2",children:(0,s.jsx)(i.SubmitButton,{formAction:c,pendingText:"Updating...",className:"rounded-lg bg-primary hover:bg-primary/90 text-white h-10",children:"Save Changes"})})]})]})}let p=(0,d.createServerReference)("60120624b62a67d0046abca062aae687cbb5ebc44a",d.callServer,void 0,d.findSourceMapURL,"editTeamSlug");function m({account:e}){return(0,s.jsxs)("form",{className:"animate-in",children:[(0,s.jsx)("input",{type:"hidden",name:"accountId",value:e.account_id}),(0,s.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-y-2",children:[(0,s.jsx)(l.Label,{htmlFor:"slug",className:"text-sm font-medium text-foreground/90",children:"Team URL Identifier"}),(0,s.jsxs)("div",{className:"flex items-center gap-x-2",children:[(0,s.jsx)("span",{className:"text-sm text-foreground/70 whitespace-nowrap",children:"https://your-app.com/"}),(0,s.jsx)(o.p,{defaultValue:e.slug,name:"slug",id:"slug",placeholder:"my-team",required:!0,className:"h-10 rounded-lg border-subtle dark:border-white/10 bg-white dark:bg-background-secondary"})]})]}),(0,s.jsx)("div",{className:"flex justify-end mt-2",children:(0,s.jsx)(i.SubmitButton,{formAction:p,pendingText:"Updating...",className:"rounded-lg bg-primary hover:bg-primary/90 text-white h-10",children:"Save Changes"})})]})]})}var x=t(44493);function h({params:e}){let{accountSlug:r}=n().use(e),[t,a]=n().useState(null),[o,i]=n().useState(!0),[d,c]=n().useState(null);return o?(0,s.jsx)("div",{children:"Loading..."}):t?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-card-title",children:"Team Settings"}),(0,s.jsx)("p",{className:"text-sm text-foreground/70",children:"Manage your team account details."})]}),(0,s.jsxs)(x.Zp,{className:"border-subtle dark:border-white/10 bg-white dark:bg-background-secondary shadow-none",children:[(0,s.jsxs)(x.aR,{children:[(0,s.jsx)(x.ZB,{className:"text-base text-card-title",children:"Team Name"}),(0,s.jsx)(x.BT,{children:"Update your team name."})]}),(0,s.jsx)(x.Wu,{children:(0,s.jsx)(u,{account:t})})]}),(0,s.jsxs)(x.Zp,{className:"border-subtle dark:border-white/10 bg-white dark:bg-background-secondary shadow-none",children:[(0,s.jsxs)(x.aR,{children:[(0,s.jsx)(x.ZB,{className:"text-base text-card-title",children:"Team URL"}),(0,s.jsx)(x.BT,{children:"Update your team URL slug."})]}),(0,s.jsx)(x.Wu,{children:(0,s.jsx)(m,{account:t})})]})]}):(0,s.jsx)("div",{children:"Account not found"})}t(36)},48772:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.default,__next_app__:()=>l,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=t(65239),a=t(48088),n=t(31369),o=t(30893),i={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>o[e]);t.d(r,i);let d={children:["",{children:["(dashboard)",{children:["(teamAccount)",{children:["[accountSlug]",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,73129)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\settings\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,4690)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\settings\\layout.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,33532)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,88524))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,93595)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,88524))).default(e)],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\settings\\page.tsx"],l={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/(teamAccount)/[accountSlug]/settings/page",pathname:"/[accountSlug]/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73129:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\(teamAccount)\\\\[accountSlug]\\\\settings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(teamAccount)\\[accountSlug]\\settings\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84297:e=>{"use strict";e.exports=require("async_hooks")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97662:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"60120624b62a67d0046abca062aae687cbb5ebc44a":()=>s.vI,"602190608cb4aada86562e5e5997e6bb44fbe95e4e":()=>s.$w,"60836a64bf90333e8dead87703a0c5a32e95fa0f8f":()=>s.gj,"7f8bed79c8654f95685745e61906af37f96a086236":()=>a.U});var s=t(67834),a=t(5876)}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,5193,4267,7096,1265,3530,7156,7976,3667,8188,3806,1841],()=>t(48772));module.exports=s})();