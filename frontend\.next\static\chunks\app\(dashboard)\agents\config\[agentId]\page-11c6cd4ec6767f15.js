(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2593],{6371:(e,t,a)=>{Promise.resolve().then(a.bind(a,33532))},10549:(e,t,a)=>{"use strict";a.d(t,{$m:()=>d,As:()=>o,nD:()=>i,ub:()=>c});var s=a(95155);a(12115);var r=a(23478),n=a(66474),l=a(59434);function i(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"accordion",...t})}function o(e){let{className:t,...a}=e;return(0,s.jsx)(r.q7,{"data-slot":"accordion-item",className:(0,l.cn)("border-b last:border-b-0",t),...a})}function d(e){let{className:t,children:a,...i}=e;return(0,s.jsx)(r.Y9,{className:"flex",children:(0,s.jsxs)(r.l9,{"data-slot":"accordion-trigger",className:(0,l.cn)("focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180",t),...i,children:[a,(0,s.jsx)(n.A,{className:"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200"})]})})}function c(e){let{className:t,children:a,...n}=e;return(0,s.jsx)(r.UC,{"data-slot":"accordion-content",className:"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm",...n,children:(0,s.jsx)("div",{className:(0,l.cn)("pt-0 pb-4",t),children:a})})}},33532:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>eI});var s=a(95155),r=a(12115),n=a(35695),l=a(14186),i=a(5196),o=a(74783),d=a(92657),c=a(47330),u=a(53311),m=a(71539),g=a(49376),x=a(40081),h=a(51154),f=a(30285),p=a(10549),b=a(26126),v=a(55365),j=a(69474),w=a(59434);function N(e){let{...t}=e;return(0,s.jsx)(j._s.Root,{"data-slot":"drawer",...t})}function y(e){let{...t}=e;return(0,s.jsx)(j._s.Trigger,{"data-slot":"drawer-trigger",...t})}function _(e){let{...t}=e;return(0,s.jsx)(j._s.Portal,{"data-slot":"drawer-portal",...t})}function C(e){let{className:t,...a}=e;return(0,s.jsx)(j._s.Overlay,{"data-slot":"drawer-overlay",className:(0,w.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function S(e){let{className:t,children:a,...r}=e;return(0,s.jsxs)(_,{"data-slot":"drawer-portal",children:[(0,s.jsx)(C,{}),(0,s.jsxs)(j._s.Content,{"data-slot":"drawer-content",className:(0,w.cn)("group/drawer-content bg-background fixed z-50 flex h-auto flex-col","data-[vaul-drawer-direction=top]:inset-x-0 data-[vaul-drawer-direction=top]:top-0 data-[vaul-drawer-direction=top]:mb-24 data-[vaul-drawer-direction=top]:max-h-[80vh] data-[vaul-drawer-direction=top]:rounded-b-lg data-[vaul-drawer-direction=top]:border-b","data-[vaul-drawer-direction=bottom]:inset-x-0 data-[vaul-drawer-direction=bottom]:bottom-0 data-[vaul-drawer-direction=bottom]:mt-24 data-[vaul-drawer-direction=bottom]:max-h-[80vh] data-[vaul-drawer-direction=bottom]:rounded-t-lg data-[vaul-drawer-direction=bottom]:border-t","data-[vaul-drawer-direction=right]:inset-y-0 data-[vaul-drawer-direction=right]:right-0 data-[vaul-drawer-direction=right]:w-3/4 data-[vaul-drawer-direction=right]:border-l data-[vaul-drawer-direction=right]:sm:max-w-sm","data-[vaul-drawer-direction=left]:inset-y-0 data-[vaul-drawer-direction=left]:left-0 data-[vaul-drawer-direction=left]:w-3/4 data-[vaul-drawer-direction=left]:border-r data-[vaul-drawer-direction=left]:sm:max-w-sm",t),...r,children:[(0,s.jsx)("div",{className:"bg-muted mx-auto mt-4 hidden h-2 w-[100px] shrink-0 rounded-full group-data-[vaul-drawer-direction=bottom]/drawer-content:block"}),a]})]})}function k(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"drawer-header",className:(0,w.cn)("flex flex-col gap-1.5 p-4",t),...a})}function E(e){let{className:t,...a}=e;return(0,s.jsx)(j._s.Title,{"data-slot":"drawer-title",className:(0,w.cn)("text-foreground font-semibold",t),...a})}var A=a(17313),T=a(38198),I=a(5937),P=a(25487),R=a(54165),F=a(66695),D=a(69803),O=a(1243),z=a(381),L=a(54416),V=a(95030);let B=e=>{var t,a;let{mcp:r,index:n,onEdit:l,onRemove:i,onConfigureTools:o}=e,{data:d=[]}=(0,V.Gx)(r.qualifiedName),c=r.selectedProfileId||(null==(t=r.config)?void 0:t.profile_id),m=d.find(e=>e.profile_id===c),g=r.config&&Object.keys(r.config).length>0,x=!!c&&!!m,h=!x&&!g&&!r.isCustom;return(0,s.jsx)(F.Zp,{className:"p-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[(0,s.jsx)("div",{className:"w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center flex-shrink-0",children:(0,s.jsx)(u.A,{className:"h-4 w-4 text-primary"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("div",{className:"flex items-center gap-2 mb-1",children:(0,s.jsx)("div",{className:"font-medium text-sm truncate",children:r.name})}),(0,s.jsxs)("div",{className:"flex items-center gap-3 text-xs text-muted-foreground",children:[(0,s.jsxs)("span",{children:[(null==(a=r.enabledTools)?void 0:a.length)||0," tools enabled"]}),x&&(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(D.A,{className:"h-3 w-3 text-green-600"}),(0,s.jsx)("span",{className:"text-green-600 font-medium truncate max-w-24",children:m.profile_name})]}),g&&!x&&(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(D.A,{className:"h-3 w-3 text-green-600"}),(0,s.jsx)("span",{className:"text-green-600 font-medium",children:"Configured"})]}),h&&(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[(0,s.jsx)(O.A,{className:"h-3 w-3 text-amber-600"}),(0,s.jsx)("span",{className:"text-amber-600",children:"Needs config"})]})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 flex-shrink-0",children:[o&&(0,s.jsx)(f.$,{size:"sm",variant:"ghost",onClick:()=>o(n),title:"Configure tools",children:(0,s.jsx)(z.A,{className:"h-4 w-4"})}),(0,s.jsx)(f.$,{size:"sm",variant:"ghost",onClick:()=>i(n),title:"Remove integration",children:(0,s.jsx)(L.A,{className:"h-4 w-4"})})]})]})})},U=e=>{let{configuredMCPs:t,onEdit:a,onRemove:r,onConfigureTools:n}=e;return 0===t.length?null:(0,s.jsx)("div",{className:"space-y-2",children:t.map((e,t)=>(0,s.jsx)(B,{mcp:e,index:t,onEdit:a,onRemove:r,onConfigureTools:n},t))})};var M=a(85057),W=a(30356),$=a(13052),q=a(76517),Z=a(85339),G=a(43453),Y=a(66424),J=a(47262),K=a(52643),H=a(62523);let X=e=>{let{open:t,onOpenChange:a,onSave:n}=e,[l,i]=(0,r.useState)("setup"),[o,d]=(0,r.useState)("sse"),[c,g]=(0,r.useState)(""),[x,p]=(0,r.useState)(""),[b,j]=(0,r.useState)(""),[N,y]=(0,r.useState)(!1),[_,C]=(0,r.useState)(null),[S,k]=(0,r.useState)([]),[E,A]=(0,r.useState)(new Set),[T,I]=(0,r.useState)(null),F=async()=>{y(!0),C(null),k([]);try{let e;if("sse"===o||"http"===o){let t=c.trim();if(!t)throw Error("Please enter the connection URL.");if(!b.trim())throw Error("Please enter a name for this connection.");e={url:t},p(b.trim())}let t=(0,K.U)(),{data:{session:a}}=await t.auth.getSession();if(!a)throw Error("You must be logged in to discover tools");let s=await fetch("".concat("http://localhost:8000/api","/mcp/discover-custom-tools"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a.access_token)},body:JSON.stringify({type:o,config:e})});if(!s.ok){let e=await s.json();throw Error(e.message||"Failed to connect to the service. Please check your configuration.")}let r=await s.json();if(!r.tools||0===r.tools.length)throw Error("No tools found. Please check your configuration.");r.serverName&&p(r.serverName),r.processedConfig&&I(r.processedConfig),k(r.tools),A(new Set(r.tools.map(e=>e.name))),i("tools")}catch(e){C(e.message)}finally{y(!1)}},D=()=>{if(0===S.length||0===E.size)return void C("Please select at least one tool to continue.");if(!x.trim())return void C("Please provide a name for this connection.");try{let e={url:c.trim()};n({name:x,type:o,config:e,enabledTools:Array.from(E),selectedProfileId:void 0}),g(""),j(""),k([]),A(new Set),p(""),I(null),C(null),i("setup"),a(!1)}catch(e){C("Invalid configuration format.")}},O=e=>{let t=new Set(E);t.has(e)?t.delete(e):t.add(e),A(t)},z=()=>{g(""),j(""),k([]),A(new Set),p(""),I(null),C(null),i("setup")};return(0,s.jsx)(R.lG,{open:t,onOpenChange:e=>{a(e),e||z()},children:(0,s.jsxs)(R.Cf,{className:"max-w-4xl max-h-[85vh] overflow-hidden flex flex-col",children:[(0,s.jsxs)(R.c7,{children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center",children:(0,s.jsx)(m.A,{className:"h-4 w-4 text-primary"})}),(0,s.jsx)(R.L3,{children:"Connect New Service"})]}),(0,s.jsx)(R.rr,{children:"setup"===l?"Connect to external services to expand your capabilities with new tools and integrations.":"Choose which tools you'd like to enable from this service connection."}),(0,s.jsxs)("div",{className:"flex items-center gap-2 pt-2",children:[(0,s.jsxs)("div",{className:(0,w.cn)("flex items-center gap-2 text-sm font-medium","setup"===l?"text-primary":"text-muted-foreground"),children:[(0,s.jsx)("div",{className:(0,w.cn)("w-6 h-6 rounded-full flex items-center justify-center text-xs","setup"===l?"bg-primary text-primary-foreground":"bg-muted text-muted-foreground"),children:"1"}),"Setup Connection"]}),(0,s.jsx)($.A,{className:"h-4 w-4 text-muted-foreground"}),(0,s.jsxs)("div",{className:(0,w.cn)("flex items-center gap-2 text-sm font-medium","tools"===l?"text-primary":"text-muted-foreground"),children:[(0,s.jsx)("div",{className:(0,w.cn)("w-6 h-6 rounded-full flex items-center justify-center text-xs","tools"===l?"bg-primary text-primary-foreground":"bg-muted-foreground/20 text-muted-foreground"),children:"2"}),"Select Tools"]})]})]}),(0,s.jsx)("div",{className:"flex-1 overflow-hidden flex flex-col",children:"setup"===l?(0,s.jsxs)("div",{className:"space-y-6 p-1 flex-1",children:[(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(M.Label,{className:"text-base font-medium",children:"How would you like to connect?"}),(0,s.jsxs)(W.z,{value:o,onValueChange:e=>d(e),className:"grid grid-cols-1 gap-3",children:[(0,s.jsxs)("div",{className:(0,w.cn)("flex items-start space-x-3 p-4 rounded-lg border cursor-pointer transition-all hover:bg-muted/50","http"===o?"border-primary bg-primary/5":"border-border"),children:[(0,s.jsx)(W.C,{value:"http",id:"http",className:"mt-1"}),(0,s.jsxs)("div",{className:"flex-1 space-y-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(P.A,{className:"h-4 w-4 text-primary"}),(0,s.jsx)(M.Label,{htmlFor:"http",className:"text-base font-medium cursor-pointer",children:"Streamable HTTP"})]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Standard streamable HTTP connection"})]})]}),(0,s.jsxs)("div",{className:(0,w.cn)("flex items-start space-x-3 p-4 rounded-lg border cursor-pointer transition-all hover:bg-muted/50","sse"===o?"border-primary bg-primary/5":"border-border"),children:[(0,s.jsx)(W.C,{value:"sse",id:"sse",className:"mt-1"}),(0,s.jsxs)("div",{className:"flex-1 space-y-1",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(q.A,{className:"h-4 w-4 text-primary"}),(0,s.jsx)(M.Label,{htmlFor:"sse",className:"text-base font-medium cursor-pointer",children:"SSE (Server-Sent Events)"})]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Real-time connection using Server-Sent Events for streaming updates"})]})]})]})]})}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(M.Label,{htmlFor:"serverName",className:"text-base font-medium",children:"Connection Name"}),(0,s.jsx)("input",{id:"serverName",type:"text",placeholder:"e.g., Gmail, Slack, Customer Support Tools",value:b,onChange:e=>j(e.target.value),className:"w-full px-4 py-3 border border-input bg-background rounded-lg text-base focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Give this connection a memorable name"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(M.Label,{htmlFor:"config",className:"text-base font-medium",children:"Connection URL"}),(0,s.jsx)(H.p,{id:"config",type:"url",placeholder:{http:"https://server.example.com/mcp",sse:"https://mcp.composio.dev/partner/composio/gmail/sse?customerId=YOUR_CUSTOMER_ID"}[o],value:c,onChange:e=>g(e.target.value),className:"w-full px-4 py-3 border border-input bg-muted rounded-lg text-base focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent font-mono"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Paste the complete connection URL provided by your service"})]})]}),_&&(0,s.jsxs)(v.Fc,{variant:"destructive",children:[(0,s.jsx)(Z.A,{className:"h-4 w-4"}),(0,s.jsx)(v.TN,{children:_})]})]}):"tools"===l?(0,s.jsxs)("div",{className:"space-y-6 p-1 flex-1 flex flex-col",children:[(0,s.jsxs)(v.Fc,{className:"border-green-200 bg-green-50 text-green-800",children:[(0,s.jsx)(G.A,{className:"h-5 w-5 text-green-600"}),(0,s.jsxs)("div",{className:"ml-2",children:[(0,s.jsx)("h3",{className:"font-medium text-green-900 mb-1",children:"Connection Successful!"}),(0,s.jsxs)("p",{className:"text-sm text-green-700",children:["Found ",S.length," available tools from ",(0,s.jsx)("strong",{children:x})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4 flex-1 flex flex-col",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-base font-medium",children:"Available Tools"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Select the tools you want to enable"})]}),(0,s.jsx)(f.$,{variant:"outline",size:"sm",onClick:()=>{E.size===S.length?A(new Set):A(new Set(S.map(e=>e.name)))},children:E.size===S.length?"Deselect All":"Select All"})]}),(0,s.jsx)("div",{className:"flex-1 min-h-0",children:(0,s.jsx)(Y.F,{className:"h-[400px] border border-border rounded-lg",children:(0,s.jsx)("div",{className:"space-y-3 p-4",children:S.map(e=>(0,s.jsxs)("div",{className:(0,w.cn)("flex items-start space-x-3 p-4 rounded-lg border transition-all cursor-pointer hover:bg-muted/50",E.has(e.name)?"border-primary bg-primary/5":"border-border"),onClick:()=>O(e.name),children:[(0,s.jsx)(J.S,{id:e.name,checked:E.has(e.name),onCheckedChange:()=>O(e.name),className:"mt-1"}),(0,s.jsxs)("div",{className:"flex-1 space-y-2 min-w-0",children:[(0,s.jsx)(M.Label,{htmlFor:e.name,className:"text-base font-medium cursor-pointer block",children:e.name.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase())}),e.description&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground leading-relaxed",children:e.description})]})]},e.name))})})})]}),_&&(0,s.jsxs)(v.Fc,{variant:"destructive",children:[(0,s.jsx)(Z.A,{className:"h-4 w-4"}),(0,s.jsx)(v.TN,{children:_})]})]}):null}),(0,s.jsx)(R.Es,{className:"flex-shrink-0 pt-4",children:"tools"===l?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(f.$,{variant:"outline",onClick:()=>{"tools"===l&&i("setup"),C(null)},children:"Back"}),(0,s.jsx)(f.$,{variant:"outline",onClick:()=>a(!1),children:"Cancel"}),(0,s.jsxs)(f.$,{onClick:()=>{if(0===E.size)return void C("Please select at least one tool to continue.");C(null),D()},disabled:0===E.size,children:["Add Connection (",E.size," tools)"]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(f.$,{variant:"outline",onClick:()=>a(!1),children:"Cancel"}),(0,s.jsx)(f.$,{onClick:F,disabled:!c.trim()||!b.trim()||N,children:N?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(h.A,{className:"h-5 w-5 animate-spin"}),"Discovering tools..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(u.A,{className:"h-5 w-5"}),"Connect"]})})]})})]})})};var Q=a(26528),ee=a(71319);let et=e=>{var t,a;let{configuredMCPs:n,onConfigurationChange:l,agentId:i}=e,[o,d]=(0,r.useState)(!1),[c,u]=(0,r.useState)(!1),[g,x]=(0,r.useState)(null),[h,p]=(0,r.useState)(!1),[b,v]=(0,r.useState)(!1),[j,w]=(0,r.useState)(null),[N,y]=(0,r.useState)(i);return(0,r.useEffect)(()=>{y(i)},[i]),(0,s.jsxs)("div",{className:"h-full flex flex-col",children:[(0,s.jsxs)("div",{className:"flex-1 overflow-y-auto",children:[0===n.length&&(0,s.jsxs)("div",{className:"text-center py-12 px-6 bg-muted/30 rounded-xl border-2 border-dashed border-border",children:[(0,s.jsx)("div",{className:"mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-4",children:(0,s.jsx)(m.A,{className:"h-6 w-6 text-muted-foreground"})}),(0,s.jsx)("h4",{className:"text-sm font-medium text-foreground mb-2",children:"No integrations configured"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mb-6 max-w-sm mx-auto",children:"Browse the app registry to connect your apps through Pipedream or add custom MCP servers"}),(0,s.jsxs)("div",{className:"flex gap-2 justify-center",children:[(0,s.jsxs)(f.$,{onClick:()=>u(!0),variant:"default",children:[(0,s.jsx)(I.A,{className:"h-4 w-4"}),"Browse Apps"]}),(0,s.jsxs)(f.$,{onClick:()=>d(!0),variant:"outline",children:[(0,s.jsx)(P.A,{className:"h-4 w-4"}),"Custom MCP"]})]})]}),n.length>0&&(0,s.jsx)("div",{className:"space-y-4",children:(0,s.jsxs)("div",{className:"bg-card rounded-xl border border-border overflow-hidden",children:[(0,s.jsx)("div",{className:"px-6 py-4 border-b border-border bg-muted/30",children:(0,s.jsx)("h4",{className:"text-sm font-medium text-foreground",children:"Configured Integrations"})}),(0,s.jsx)("div",{className:"p-2 divide-y divide-border",children:(0,s.jsx)(U,{configuredMCPs:n,onEdit:e=>{n[e].customType,x(e),d(!0)},onRemove:e=>{let t=[...n];t.splice(e,1),l(t)},onConfigureTools:e=>{let t=n[e];if(w(t),"pipedream"===t.customType){var a;t.selectedProfileId||(null==(a=t.config)?void 0:a.profile_id)?p(!0):console.warn("Pipedream MCP has no profile_id:",t)}else v(!0)}})})]})})]}),n.length>0&&(0,s.jsx)("div",{className:"flex-shrink-0 pt-4",children:(0,s.jsxs)("div",{className:"flex gap-2 justify-center",children:[(0,s.jsxs)(f.$,{onClick:()=>u(!0),variant:"default",children:[(0,s.jsx)(I.A,{className:"h-4 w-4"}),"Browse Apps"]}),(0,s.jsxs)(f.$,{onClick:()=>d(!0),variant:"outline",children:[(0,s.jsx)(P.A,{className:"h-4 w-4"}),"Custom MCP"]})]})}),(0,s.jsx)(R.lG,{open:c,onOpenChange:u,children:(0,s.jsxs)(R.Cf,{className:"p-0 max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,s.jsx)(R.c7,{className:"sr-only",children:(0,s.jsx)(R.L3,{children:"Select Integration"})}),(0,s.jsx)(Q.Z,{showAgentSelector:!1,selectedAgentId:N,onAgentChange:e=>{y(e)},onToolsSelected:(e,t,a,s)=>{let r={name:a,qualifiedName:"pipedream_".concat(s,"_").concat(e),config:{url:"https://remote.mcp.pipedream.net",headers:{"x-pd-app-slug":s},profile_id:e},enabledTools:t,isCustom:!0,customType:"pipedream",selectedProfileId:e};l([...n.filter(t=>"pipedream"!==t.customType||t.selectedProfileId!==e),r]),u(!1)}})]})}),(0,s.jsx)(X,{open:o,onOpenChange:d,onSave:e=>{l([...n,{name:e.name,qualifiedName:"custom_".concat(e.type,"_").concat(Date.now()),config:e.config,enabledTools:e.enabledTools,selectedProfileId:e.selectedProfileId,isCustom:!0,customType:e.type}])}}),j&&"pipedream"===j.customType&&(j.selectedProfileId||(null==(t=j.config)?void 0:t.profile_id))&&(0,s.jsx)(ee.n,{mode:"pipedream",agentId:N,profileId:j.selectedProfileId||(null==(a=j.config)?void 0:a.profile_id),appName:j.name,open:h,onOpenChange:p,onToolsUpdate:e=>{j&&(l(n.map(t=>t===j?{...t,enabledTools:e}:t)),p(!1),w(null))}}),j&&"pipedream"!==j.customType&&(0,s.jsx)(ee.n,{mode:"custom",agentId:N,mcpConfig:j.config,mcpName:j.name,open:b,onOpenChange:v,onToolsUpdate:e=>{j&&(l(n.map(t=>t===j?{...t,enabledTools:e}:t)),v(!1),w(null))}})]})},ea=e=>{let{configuredMCPs:t,customMCPs:a,onMCPChange:r,agentId:n}=e,l=[...t||[],...(a||[]).map(e=>({name:e.name,qualifiedName:"custom_".concat(e.type||e.customType,"_").concat(e.name.replace(" ","_").toLowerCase()),config:e.config,enabledTools:e.enabledTools,isCustom:!0,customType:e.type||e.customType}))];return(0,s.jsx)(et,{configuredMCPs:l,onConfigurationChange:e=>{r({configured_mcps:e.filter(e=>!e.isCustom),custom_mcps:e.filter(e=>e.isCustom).map(e=>({name:e.name,type:e.customType,customType:e.customType,config:e.config,enabledTools:e.enabledTools}))})},agentId:n})};var es=a(56671),er=a(58351),en=a(88271),el=a(82814),ei=a(67897),eo=a(53153),ed=a(53732),ec=a(58883),eu=a(46768),em=a(25731),eg=a(90066);let ex=e=>{let{agent:t}=e,[a,n]=(0,r.useState)([]),[l,i]=(0,r.useState)(""),[o,d]=(0,r.useState)(null),[c,u]=(0,r.useState)(null),[m,g]=(0,r.useState)("idle"),[x,h]=(0,r.useState)(!1),[f,p]=(0,r.useState)(!1),v=(0,r.useRef)(null),j=(0,r.useRef)(null),{avatar:w,color:N}=t.avatar&&t.avatar_color?{avatar:t.avatar,color:t.avatar_color}:(0,en.Z)(t.agent_id),y=(0,eo.u)(),_=(0,ec.U)(),C=(0,eu.WZ)(),S=(0,eu.CV)(),k=()=>{var e;null==(e=v.current)||e.scrollIntoView({behavior:"smooth"})};(0,r.useEffect)(()=>{k()},[a]);let E=(0,r.useCallback)(e=>{console.log("[PREVIEW STREAM] Received message: ID=".concat(e.message_id,", Type=").concat(e.type)),n(t=>t.some(t=>t.message_id===e.message_id)?t.map(t=>t.message_id===e.message_id?e:t):[...t,e])},[]),A=(0,r.useCallback)(e=>{switch(console.log("[PREVIEW] Stream status changed: ".concat(e)),e){case"idle":case"completed":case"stopped":case"agent_not_running":case"error":case"failed":g("idle"),u(null);break;case"connecting":g("connecting");break;case"streaming":g("running")}},[]),T=(0,r.useCallback)(e=>{console.error("[PREVIEW] Stream error: ".concat(e)),e.toLowerCase().includes("not found")||e.toLowerCase().includes("agent run is not running")||es.oR.error("Stream Error: ".concat(e))},[]),I=(0,r.useCallback)(()=>{console.log("[PREVIEW] Stream closed")},[]),{status:P,textContent:R,toolCall:F,error:D,agentRunId:O,startStreaming:z,stopStreaming:L}=(0,ed.Z)({onMessage:E,onStatusChange:A,onError:T,onClose:I},o,n);(0,r.useEffect)(()=>{c&&c!==O&&o&&(console.log("[PREVIEW] Starting stream for agentRunId: ".concat(c,", threadId: ").concat(o)),z(c))},[c,z,O,o]),(0,r.useEffect)(()=>{console.log("[PREVIEW] State update:",{threadId:o,agentRunId:c,currentHookRunId:O,agentStatus:m,streamHookStatus:P,messagesCount:a.length,hasStartedConversation:f})},[o,c,O,m,P,a.length,f]),(0,r.useEffect)(()=>{R&&k()},[R]);let V=async(e,a)=>{var s,r,l,o,c,m,g;if(e.trim()||(null==(s=j.current)?void 0:s.getPendingFiles().length)){h(!0),p(!0);try{let s=(null==(r=j.current)?void 0:r.getPendingFiles())||[],x=new FormData;x.append("prompt",e),x.append("agent_id",t.agent_id),s.forEach((e,t)=>{let a=(0,eg.L)(e.name);x.append("files",e,a)}),(null==a?void 0:a.model_name)&&x.append("model_name",a.model_name),x.append("enable_thinking",String(null!=(o=null==a?void 0:a.enable_thinking)&&o)),x.append("reasoning_effort",null!=(c=null==a?void 0:a.reasoning_effort)?c:"low"),x.append("stream",String(null==(m=null==a?void 0:a.stream)||m)),x.append("enable_context_manager",String(null!=(g=null==a?void 0:a.enable_context_manager)&&g)),console.log("[PREVIEW] Initiating agent...");let h=await y.mutateAsync(x);if(console.log("[PREVIEW] Agent initiated:",h),h.thread_id){if(d(h.thread_id),h.agent_run_id)console.log("[PREVIEW] Setting agent run ID:",h.agent_run_id),u(h.agent_run_id);else{console.log("[PREVIEW] No agent_run_id in result, starting agent manually...");try{let e=await C.mutateAsync({threadId:h.thread_id,options:a});console.log("[PREVIEW] Agent started manually:",e),u(e.agent_run_id)}catch(e){console.error("[PREVIEW] Error starting agent manually:",e),es.oR.error("Failed to start agent")}}let t={message_id:"user-".concat(Date.now()),thread_id:h.thread_id,type:"user",is_llm_message:!1,content:e,metadata:"{}",created_at:new Date().toISOString(),updated_at:new Date().toISOString()};n([t])}null==(l=j.current)||l.clearPendingFiles(),i("")}catch(e){console.error("[PREVIEW] Error during initiation:",e),e instanceof em.Ey?es.oR.error("Billing limit reached. Please upgrade your plan."):es.oR.error("Failed to start conversation"),p(!1)}finally{h(!1)}}},B=(0,r.useCallback)(async(e,t)=>{if(!e.trim()||!o)return;h(!0);let a={message_id:"temp-".concat(Date.now()),thread_id:o,type:"user",is_llm_message:!1,content:e,metadata:"{}",created_at:new Date().toISOString(),updated_at:new Date().toISOString()};n(e=>[...e,a]),i("");try{let r=_.mutateAsync({threadId:o,message:e}),l=C.mutateAsync({threadId:o,options:t}),i=await Promise.allSettled([r,l]);if("rejected"===i[0].status){var s;throw Error("Failed to send message: ".concat((null==(s=i[0].reason)?void 0:s.message)||i[0].reason))}if("rejected"===i[1].status){let e=i[1].reason;if(e instanceof em.Ey){es.oR.error("Billing limit reached. Please upgrade your plan."),n(e=>e.filter(e=>e.message_id!==a.message_id));return}throw Error("Failed to start agent: ".concat((null==e?void 0:e.message)||e))}let d=i[1].value;u(d.agent_run_id)}catch(e){console.error("[PREVIEW] Error sending message:",e),es.oR.error(e instanceof Error?e.message:"Operation failed"),n(e=>e.filter(e=>e.message_id!==a.message_id))}finally{h(!1)}},[o,_,C]),U=(0,r.useCallback)(async()=>{if(console.log("[PREVIEW] Stopping agent..."),g("idle"),await L(),c)try{await S.mutateAsync(c)}catch(e){console.error("[PREVIEW] Error stopping agent:",e)}},[L,c,S]),M=(0,r.useCallback)((e,t)=>{console.log("[PREVIEW] Tool clicked:",t),es.oR.info("Tool: ".concat(t," (Preview mode - tool details not available)"))},[]);return(0,s.jsxs)("div",{className:"h-full flex flex-col bg-muted dark:bg-muted/30",children:[(0,s.jsxs)("div",{className:"flex-shrink-0 flex items-center gap-3 p-8",children:[(0,s.jsx)("div",{className:"h-10 w-10 flex items-center justify-center rounded-lg text-lg",style:{backgroundColor:N},children:w}),(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsx)("h3",{className:"font-semibold",children:t.name||"Unnamed Agent"})}),(0,s.jsx)(b.E,{variant:"highlight",className:"text-sm",children:"Preview Mode"})]}),(0,s.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,s.jsxs)("div",{className:"h-full overflow-y-auto scrollbar-hide",children:[(0,s.jsx)(ei.u9,{messages:a,streamingTextContent:R,streamingToolCall:F,agentStatus:m,handleToolClick:M,handleOpenFileViewer:()=>{},streamHookStatus:P,isPreviewMode:!0,agentName:t.name,agentAvatar:w,emptyStateComponent:(0,s.jsxs)("div",{className:"flex flex-col items-center text-center text-muted-foreground/80",children:[(0,s.jsx)("div",{className:"flex w-20 aspect-square items-center justify-center rounded-2xl bg-muted-foreground/10 p-4 mb-4",children:(0,s.jsx)("div",{className:"text-4xl",children:w})}),(0,s.jsxs)("p",{className:"w-[60%] text-2xl mb-3",children:["Start conversation with ",(0,s.jsx)("span",{className:"text-primary/80 font-semibold",children:t.name})]}),(0,s.jsx)("p",{className:"w-[70%] text-sm text-muted-foreground/60",children:"Test your agent's configuration and chat back and forth to see how it performs with your current settings, tools, and knowledge base."})]})}),(0,s.jsx)("div",{ref:v})]})}),(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"p-0 md:p-4 md:px-10",children:(0,s.jsx)(el.V,{ref:j,onSubmit:o?B:V,loading:x,placeholder:"Message ".concat(t.name||"agent","..."),value:l,onChange:i,disabled:x,isAgentRunning:"running"===m||"connecting"===m,onStopAgent:U,agentName:t.name,hideAttachments:!1,bgColor:"bg-muted-foreground/10",selectedAgentId:t.agent_id,onAgentSelect:()=>{es.oR.info("You can only test the agent you are currently configuring")}})})})]})};var eh=a(88539),ef=a(56287);let ep=e=>{let{value:t,onSave:a,className:n="",placeholder:l="Click to edit...",multiline:i=!1,minHeight:o="auto"}=e,[d,c]=(0,r.useState)(!1),[u,m]=(0,r.useState)(t);(0,r.useEffect)(()=>{m(t)},[t]);let g=()=>{a(u),c(!1)},x=()=>{m(t),c(!1)};if(d){let e=i?eh.T:H.p;return(0,s.jsx)("div",{className:"space-y-2",children:(0,s.jsx)(e,{value:u,onChange:e=>m(e.target.value),onKeyDown:e=>{"Enter"!==e.key||i?"Escape"===e.key?x():"Enter"===e.key&&e.metaKey&&i&&g():g()},onBlur:g,autoFocus:!0,className:(0,w.cn)("border-none shadow-none px-0 focus-visible:ring-0 bg-transparent",i?"resize-none":"",i&&o?"min-h-[".concat(o,"]"):"",n),style:{fontSize:"inherit",fontWeight:"inherit",lineHeight:"inherit",...i&&o?{minHeight:o}:{}}})})}return(0,s.jsxs)("div",{className:(0,w.cn)("group bg-transparent cursor-pointer relative rounded px-2 py-1 -mx-2 -my-1 transition-colors",n),onClick:()=>c(!0),children:[(0,s.jsx)("div",{className:(0,w.cn)(t?"":"text-muted-foreground italic",i&&o?"min-h-[".concat(o,"]"):""),style:i&&o?{minHeight:o}:{},children:t||l}),(0,s.jsx)(ef.A,{className:"h-3 w-3 opacity-0 group-hover:opacity-50 absolute top-1 right-1 transition-opacity"})]})};var eb=a(14636),ev=a(22346);let ej={smileys:{name:"Smileys & People",icon:"\uD83D\uDE00",emojis:["\uD83D\uDE00","\uD83D\uDE03","\uD83D\uDE04","\uD83D\uDE01","\uD83D\uDE06","\uD83D\uDE05","\uD83D\uDE02","\uD83E\uDD23","\uD83D\uDE0A","\uD83D\uDE07","\uD83D\uDE42","\uD83D\uDE43","\uD83D\uDE09","\uD83D\uDE0C","\uD83D\uDE0D","\uD83E\uDD70","\uD83D\uDE18","\uD83D\uDE17","\uD83D\uDE19","\uD83D\uDE1A","\uD83D\uDE0B","\uD83D\uDE1B","\uD83D\uDE1D","\uD83D\uDE1C","\uD83E\uDD2A","\uD83E\uDD28","\uD83E\uDDD0","\uD83E\uDD13","\uD83D\uDE0E","\uD83E\uDD29","\uD83E\uDD73","\uD83D\uDE0F","\uD83D\uDE12","\uD83D\uDE1E","\uD83D\uDE14","\uD83D\uDE1F","\uD83D\uDE15","\uD83D\uDE41","☹️","\uD83D\uDE23","\uD83D\uDE16","\uD83D\uDE2B","\uD83D\uDE29","\uD83E\uDD7A","\uD83D\uDE22","\uD83D\uDE2D","\uD83D\uDE24","\uD83D\uDE20","\uD83D\uDE21","\uD83E\uDD2C","\uD83E\uDD2F","\uD83D\uDE33","\uD83E\uDD75","\uD83E\uDD76","\uD83D\uDE31","\uD83D\uDE28","\uD83D\uDE30","\uD83D\uDE25","\uD83D\uDE13","\uD83E\uDD17","\uD83E\uDD14","\uD83E\uDD2D","\uD83E\uDD2B","\uD83E\uDD25","\uD83D\uDE36","\uD83D\uDE10","\uD83D\uDE11","\uD83D\uDE2C","\uD83D\uDE44","\uD83D\uDE2F","\uD83D\uDE26","\uD83D\uDE27","\uD83D\uDE2E","\uD83D\uDE32","\uD83E\uDD71","\uD83D\uDE34","\uD83E\uDD24","\uD83D\uDE2A","\uD83D\uDE35","\uD83E\uDD10","\uD83E\uDD74","\uD83E\uDD22","\uD83E\uDD2E","\uD83E\uDD27","\uD83D\uDE37","\uD83E\uDD12","\uD83E\uDD15","\uD83E\uDD11","\uD83E\uDD20","\uD83D\uDE08","\uD83D\uDC7F","\uD83D\uDC79","\uD83D\uDC7A","\uD83E\uDD21","\uD83D\uDCA9","\uD83D\uDC7B","\uD83D\uDC80","☠️","\uD83D\uDC7D","\uD83D\uDC7E","\uD83E\uDD16","\uD83C\uDF83","\uD83D\uDE3A","\uD83D\uDE38","\uD83D\uDE39","\uD83D\uDE3B","\uD83D\uDE3C","\uD83D\uDE3D","\uD83D\uDE40","\uD83D\uDE3F","\uD83D\uDE3E"]},people:{name:"People & Body",icon:"\uD83D\uDC4B",emojis:["\uD83D\uDC4B","\uD83E\uDD1A","\uD83D\uDD90️","✋","\uD83D\uDD96","\uD83D\uDC4C","\uD83E\uDD0C","\uD83E\uDD0F","✌️","\uD83E\uDD1E","\uD83E\uDD1F","\uD83E\uDD18","\uD83E\uDD19","\uD83D\uDC48","\uD83D\uDC49","\uD83D\uDC46","\uD83D\uDD95","\uD83D\uDC47","☝️","\uD83D\uDC4D","\uD83D\uDC4E","\uD83D\uDC4A","✊","\uD83E\uDD1B","\uD83E\uDD1C","\uD83D\uDC4F","\uD83D\uDE4C","\uD83D\uDC50","\uD83E\uDD32","\uD83E\uDD1D","\uD83D\uDE4F","✍️","\uD83D\uDC85","\uD83E\uDD33","\uD83D\uDCAA","\uD83E\uDDBE","\uD83E\uDDBF","\uD83E\uDDB5","\uD83E\uDDB6","\uD83D\uDC42","\uD83E\uDDBB","\uD83D\uDC43","\uD83E\uDDE0","\uD83E\uDEC0","\uD83E\uDEC1","\uD83E\uDDB7","\uD83E\uDDB4","\uD83D\uDC40","\uD83D\uDC41️","\uD83D\uDC45","\uD83D\uDC44","\uD83D\uDC8B","\uD83E\uDE78","\uD83D\uDC76","\uD83E\uDDD2","\uD83D\uDC66","\uD83D\uDC67","\uD83E\uDDD1","\uD83D\uDC71","\uD83D\uDC68","\uD83E\uDDD4","\uD83D\uDC68‍\uD83E\uDDB0","\uD83D\uDC68‍\uD83E\uDDB1","\uD83D\uDC68‍\uD83E\uDDB3","\uD83D\uDC68‍\uD83E\uDDB2","\uD83D\uDC69","\uD83D\uDC69‍\uD83E\uDDB0","\uD83E\uDDD1‍\uD83E\uDDB0","\uD83D\uDC69‍\uD83E\uDDB1","\uD83E\uDDD1‍\uD83E\uDDB1","\uD83D\uDC69‍\uD83E\uDDB3","\uD83E\uDDD1‍\uD83E\uDDB3","\uD83D\uDC69‍\uD83E\uDDB2","\uD83E\uDDD1‍\uD83E\uDDB2","\uD83D\uDC71‍♀️","\uD83D\uDC71‍♂️","\uD83E\uDDD3","\uD83D\uDC74","\uD83D\uDC75","\uD83D\uDE4D","\uD83D\uDE4D‍♂️","\uD83D\uDE4D‍♀️","\uD83D\uDE4E","\uD83D\uDE4E‍♂️","\uD83D\uDE4E‍♀️","\uD83D\uDE45","\uD83D\uDE45‍♂️","\uD83D\uDE45‍♀️","\uD83D\uDE46","\uD83D\uDE46‍♂️","\uD83D\uDE46‍♀️","\uD83D\uDC81","\uD83D\uDC81‍♂️","\uD83D\uDC81‍♀️","\uD83D\uDE4B","\uD83D\uDE4B‍♂️","\uD83D\uDE4B‍♀️","\uD83E\uDDCF","\uD83E\uDDCF‍♂️","\uD83E\uDDCF‍♀️","\uD83D\uDE47","\uD83D\uDE47‍♂️","\uD83D\uDE47‍♀️","\uD83E\uDD26","\uD83E\uDD26‍♂️","\uD83E\uDD26‍♀️","\uD83E\uDD37","\uD83E\uDD37‍♂️","\uD83E\uDD37‍♀️"]},animals:{name:"Animals & Nature",icon:"\uD83D\uDC36",emojis:["\uD83D\uDC36","\uD83D\uDC31","\uD83D\uDC2D","\uD83D\uDC39","\uD83D\uDC30","\uD83E\uDD8A","\uD83D\uDC3B","\uD83D\uDC3C","\uD83D\uDC3B‍❄️","\uD83D\uDC28","\uD83D\uDC2F","\uD83E\uDD81","\uD83D\uDC2E","\uD83D\uDC37","\uD83D\uDC3D","\uD83D\uDC38","\uD83D\uDC35","\uD83D\uDE48","\uD83D\uDE49","\uD83D\uDE4A","\uD83D\uDC12","\uD83D\uDC14","\uD83D\uDC27","\uD83D\uDC26","\uD83D\uDC24","\uD83D\uDC23","\uD83D\uDC25","\uD83E\uDD86","\uD83E\uDD85","\uD83E\uDD89","\uD83E\uDD87","\uD83D\uDC3A","\uD83D\uDC17","\uD83D\uDC34","\uD83E\uDD84","\uD83D\uDC1D","\uD83D\uDC1B","\uD83E\uDD8B","\uD83D\uDC0C","\uD83D\uDC1E","\uD83D\uDC1C","\uD83E\uDD9F","\uD83E\uDD97","\uD83D\uDD77️","\uD83D\uDD78️","\uD83E\uDD82","\uD83D\uDC22","\uD83D\uDC0D","\uD83E\uDD8E","\uD83E\uDD96","\uD83E\uDD95","\uD83D\uDC19","\uD83E\uDD91","\uD83E\uDD90","\uD83E\uDD9E","\uD83E\uDD80","\uD83D\uDC21","\uD83D\uDC20","\uD83D\uDC1F","\uD83D\uDC2C","\uD83D\uDC33","\uD83D\uDC0B","\uD83E\uDD88","\uD83D\uDC0A","\uD83D\uDC05","\uD83D\uDC06","\uD83E\uDD93","\uD83E\uDD8D","\uD83E\uDDA7","\uD83D\uDC18","\uD83E\uDD9B","\uD83E\uDD8F","\uD83D\uDC2A","\uD83D\uDC2B","\uD83E\uDD92","\uD83E\uDD98","\uD83D\uDC03","\uD83D\uDC02","\uD83D\uDC04","\uD83D\uDC0E","\uD83D\uDC16","\uD83D\uDC0F","\uD83D\uDC11","\uD83E\uDD99","\uD83D\uDC10","\uD83E\uDD8C","\uD83D\uDC15","\uD83D\uDC29","\uD83E\uDDAE","\uD83D\uDC15‍\uD83E\uDDBA","\uD83D\uDC08","\uD83D\uDC08‍⬛","\uD83D\uDC13","\uD83E\uDD83","\uD83E\uDD9A","\uD83E\uDD9C","\uD83E\uDDA2","\uD83E\uDDA9","\uD83D\uDD4A️","\uD83D\uDC07","\uD83E\uDD9D","\uD83E\uDDA8","\uD83E\uDDA1","\uD83E\uDDA6","\uD83E\uDDA5","\uD83D\uDC01","\uD83D\uDC00","\uD83D\uDC3F️","\uD83E\uDD94"]},food:{name:"Food & Drink",icon:"\uD83C\uDF4E",emojis:["\uD83C\uDF4E","\uD83C\uDF4F","\uD83C\uDF4A","\uD83C\uDF4B","\uD83C\uDF4C","\uD83C\uDF49","\uD83C\uDF47","\uD83C\uDF53","\uD83E\uDED0","\uD83C\uDF48","\uD83C\uDF52","\uD83C\uDF51","\uD83E\uDD6D","\uD83C\uDF4D","\uD83E\uDD65","\uD83E\uDD5D","\uD83C\uDF45","\uD83C\uDF46","\uD83E\uDD51","\uD83E\uDD66","\uD83E\uDD6C","\uD83E\uDD52","\uD83C\uDF36️","\uD83E\uDED1","\uD83C\uDF3D","\uD83E\uDD55","\uD83E\uDED2","\uD83E\uDDC4","\uD83E\uDDC5","\uD83E\uDD54","\uD83C\uDF60","\uD83E\uDD50","\uD83E\uDD6F","\uD83C\uDF5E","\uD83E\uDD56","\uD83E\uDD68","\uD83E\uDDC0","\uD83E\uDD5A","\uD83C\uDF73","\uD83E\uDDC8","\uD83E\uDD5E","\uD83E\uDDC7","\uD83E\uDD53","\uD83E\uDD69","\uD83C\uDF57","\uD83C\uDF56","\uD83E\uDDB4","\uD83C\uDF2D","\uD83C\uDF54","\uD83C\uDF5F","\uD83C\uDF55","\uD83E\uDED3","\uD83E\uDD6A","\uD83E\uDD59","\uD83E\uDDC6","\uD83C\uDF2E","\uD83C\uDF2F","\uD83E\uDED4","\uD83E\uDD57","\uD83E\uDD58","\uD83E\uDED5","\uD83E\uDD6B","\uD83C\uDF5D","\uD83C\uDF5C","\uD83C\uDF72","\uD83C\uDF5B","\uD83C\uDF63","\uD83C\uDF71","\uD83E\uDD5F","\uD83E\uDDAA","\uD83C\uDF64","\uD83C\uDF59","\uD83C\uDF5A","\uD83C\uDF58","\uD83C\uDF65","\uD83E\uDD60","\uD83E\uDD6E","\uD83C\uDF62","\uD83C\uDF61","\uD83C\uDF67","\uD83C\uDF68","\uD83C\uDF66","\uD83E\uDD67","\uD83E\uDDC1","\uD83C\uDF70","\uD83C\uDF82","\uD83C\uDF6E","\uD83C\uDF6D","\uD83C\uDF6C","\uD83C\uDF6B","\uD83C\uDF7F","\uD83C\uDF69","\uD83C\uDF6A","\uD83C\uDF30","\uD83E\uDD5C","\uD83C\uDF6F","\uD83E\uDD5B","\uD83C\uDF7C","☕","\uD83E\uDED6","\uD83C\uDF75","\uD83E\uDDC3","\uD83E\uDD64","\uD83E\uDDCB","\uD83C\uDF76","\uD83C\uDF7A","\uD83C\uDF7B","\uD83E\uDD42","\uD83C\uDF77","\uD83E\uDD43","\uD83C\uDF78","\uD83C\uDF79","\uD83E\uDDC9","\uD83C\uDF7E"]},activities:{name:"Activities",icon:"⚽",emojis:["⚽","\uD83C\uDFC0","\uD83C\uDFC8","⚾","\uD83E\uDD4E","\uD83C\uDFBE","\uD83C\uDFD0","\uD83C\uDFC9","\uD83E\uDD4F","\uD83C\uDFB1","\uD83E\uDE80","\uD83C\uDFD3","\uD83C\uDFF8","\uD83C\uDFD2","\uD83C\uDFD1","\uD83E\uDD4D","\uD83C\uDFCF","\uD83E\uDE83","\uD83E\uDD45","⛳","\uD83E\uDE81","\uD83C\uDFF9","\uD83C\uDFA3","\uD83E\uDD3F","\uD83E\uDD4A","\uD83E\uDD4B","\uD83C\uDFBD","\uD83D\uDEF9","\uD83D\uDEF7","⛸️","\uD83E\uDD4C","\uD83C\uDFBF","⛷️","\uD83C\uDFC2","\uD83E\uDE82","\uD83C\uDFCB️","\uD83C\uDFCB️‍♂️","\uD83C\uDFCB️‍♀️","\uD83E\uDD3C","\uD83E\uDD3C‍♂️","\uD83E\uDD3C‍♀️","\uD83E\uDD38","\uD83E\uDD38‍♂️","\uD83E\uDD38‍♀️","⛹️","⛹️‍♂️","⛹️‍♀️","\uD83E\uDD3A","\uD83E\uDD3E","\uD83E\uDD3E‍♂️","\uD83E\uDD3E‍♀️","\uD83C\uDFCC️","\uD83C\uDFCC️‍♂️","\uD83C\uDFCC️‍♀️","\uD83C\uDFC7","\uD83E\uDDD8","\uD83E\uDDD8‍♂️","\uD83E\uDDD8‍♀️","\uD83C\uDFC4","\uD83C\uDFC4‍♂️","\uD83C\uDFC4‍♀️","\uD83C\uDFCA","\uD83C\uDFCA‍♂️","\uD83C\uDFCA‍♀️","\uD83E\uDD3D","\uD83E\uDD3D‍♂️","\uD83E\uDD3D‍♀️","\uD83D\uDEA3","\uD83D\uDEA3‍♂️","\uD83D\uDEA3‍♀️","\uD83E\uDDD7","\uD83E\uDDD7‍♂️","\uD83E\uDDD7‍♀️","\uD83D\uDEB5","\uD83D\uDEB5‍♂️","\uD83D\uDEB5‍♀️","\uD83D\uDEB4","\uD83D\uDEB4‍♂️","\uD83D\uDEB4‍♀️","\uD83C\uDFC6","\uD83E\uDD47","\uD83E\uDD48","\uD83E\uDD49","\uD83C\uDFC5","\uD83C\uDF96️","\uD83C\uDFF5️","\uD83C\uDF97️","\uD83C\uDFAB","\uD83C\uDF9F️","\uD83C\uDFAA","\uD83E\uDD39","\uD83E\uDD39‍♂️","\uD83E\uDD39‍♀️","\uD83C\uDFAD","\uD83E\uDE70","\uD83C\uDFA8","\uD83C\uDFAC","\uD83C\uDFA4","\uD83C\uDFA7","\uD83C\uDFBC","\uD83C\uDFB5","\uD83C\uDFB6","\uD83E\uDD41","\uD83E\uDE98","\uD83C\uDFB9","\uD83C\uDFB7","\uD83C\uDFBA","\uD83E\uDE97","\uD83C\uDFB8","\uD83E\uDE95","\uD83C\uDFBB","\uD83C\uDFB2","♟️","\uD83C\uDFAF","\uD83C\uDFB3","\uD83C\uDFAE","\uD83C\uDFB0","\uD83E\uDDE9"]},travel:{name:"Travel & Places",icon:"\uD83D\uDE97",emojis:["\uD83D\uDE97","\uD83D\uDE95","\uD83D\uDE99","\uD83D\uDE8C","\uD83D\uDE8E","\uD83C\uDFCE️","\uD83D\uDE93","\uD83D\uDE91","\uD83D\uDE92","\uD83D\uDE90","\uD83D\uDEFB","\uD83D\uDE9A","\uD83D\uDE9B","\uD83D\uDE9C","\uD83C\uDFCD️","\uD83D\uDEF5","\uD83D\uDEB2","\uD83D\uDEF4","\uD83D\uDEF9","\uD83D\uDEFC","\uD83D\uDE81","\uD83D\uDEF8","✈️","\uD83D\uDEE9️","\uD83D\uDEEB","\uD83D\uDEEC","\uD83E\uDE82","\uD83D\uDCBA","\uD83D\uDE80","\uD83D\uDEF0️","\uD83D\uDE89","\uD83D\uDE8A","\uD83D\uDE9D","\uD83D\uDE9E","\uD83D\uDE8B","\uD83D\uDE83","\uD83D\uDE8B","\uD83D\uDE9E","\uD83D\uDE9D","\uD83D\uDE84","\uD83D\uDE85","\uD83D\uDE88","\uD83D\uDE82","\uD83D\uDE86","\uD83D\uDE87","\uD83D\uDE8A","\uD83D\uDE89","✈️","\uD83D\uDEEB","\uD83D\uDEEC","\uD83D\uDEE9️","\uD83D\uDCBA","\uD83D\uDEF0️","\uD83D\uDE80","\uD83D\uDEF8","\uD83D\uDE81","\uD83D\uDEF6","⛵","\uD83D\uDEA4","\uD83D\uDEE5️","\uD83D\uDEF3️","⛴️","\uD83D\uDEA2","⚓","⛽","\uD83D\uDEA7","\uD83D\uDEA8","\uD83D\uDEA5","\uD83D\uDEA6","\uD83D\uDED1","\uD83D\uDE8F","\uD83D\uDDFA️","\uD83D\uDDFF","\uD83D\uDDFD","\uD83D\uDDFC","\uD83C\uDFF0","\uD83C\uDFEF","\uD83C\uDFDF️","\uD83C\uDFA1","\uD83C\uDFA2","\uD83C\uDFA0","⛲","⛱️","\uD83C\uDFD6️","\uD83C\uDFDD️","\uD83C\uDFDC️","\uD83C\uDF0B","⛰️","\uD83C\uDFD4️","\uD83D\uDDFB","\uD83C\uDFD5️","⛺","\uD83D\uDED6","\uD83C\uDFE0","\uD83C\uDFE1","\uD83C\uDFD8️","\uD83C\uDFDA️","\uD83C\uDFD7️","\uD83C\uDFED","\uD83C\uDFE2","\uD83C\uDFEC","\uD83C\uDFE3","\uD83C\uDFE4","\uD83C\uDFE5","\uD83C\uDFE6","\uD83C\uDFE8","\uD83C\uDFEA","\uD83C\uDFEB","\uD83C\uDFE9","\uD83D\uDC92","\uD83C\uDFDB️","⛪","\uD83D\uDD4C","\uD83D\uDED5","\uD83D\uDD4D","\uD83D\uDD4B","⛩️","\uD83D\uDEE4️","\uD83D\uDEE3️","\uD83D\uDDFE","\uD83C\uDF91","\uD83C\uDFDE️","\uD83C\uDF05","\uD83C\uDF04","\uD83C\uDF20","\uD83C\uDF87","\uD83C\uDF86","\uD83C\uDF07","\uD83C\uDF06","\uD83C\uDFD9️","\uD83C\uDF03","\uD83C\uDF0C","\uD83C\uDF09","\uD83C\uDF01"]},objects:{name:"Objects",icon:"⌚",emojis:["⌚","\uD83D\uDCF1","\uD83D\uDCF2","\uD83D\uDCBB","⌨️","\uD83D\uDDA5️","\uD83D\uDDA8️","\uD83D\uDDB1️","\uD83D\uDDB2️","\uD83D\uDD79️","\uD83D\uDDDC️","\uD83D\uDCBD","\uD83D\uDCBE","\uD83D\uDCBF","\uD83D\uDCC0","\uD83D\uDCFC","\uD83D\uDCF7","\uD83D\uDCF8","\uD83D\uDCF9","\uD83C\uDFA5","\uD83D\uDCFD️","\uD83C\uDF9E️","\uD83D\uDCDE","☎️","\uD83D\uDCDF","\uD83D\uDCE0","\uD83D\uDCFA","\uD83D\uDCFB","\uD83C\uDF99️","\uD83C\uDF9A️","\uD83C\uDF9B️","\uD83E\uDDED","⏱️","⏲️","⏰","\uD83D\uDD70️","⌛","⏳","\uD83D\uDCE1","\uD83D\uDD0B","\uD83D\uDD0C","\uD83D\uDCA1","\uD83D\uDD26","\uD83D\uDD6F️","\uD83E\uDE94","\uD83E\uDDEF","\uD83D\uDEE2️","\uD83D\uDCB8","\uD83D\uDCB5","\uD83D\uDCB4","\uD83D\uDCB6","\uD83D\uDCB7","\uD83E\uDE99","\uD83D\uDCB0","\uD83D\uDCB3","\uD83D\uDC8E","⚖️","\uD83E\uDE9C","\uD83E\uDDF0","\uD83D\uDD27","\uD83D\uDD28","⚒️","\uD83D\uDEE0️","⛏️","\uD83E\uDE93","\uD83E\uDE9A","\uD83D\uDD29","⚙️","\uD83E\uDEA4","\uD83E\uDDF1","⛓️","\uD83E\uDDF2","\uD83D\uDD2B","\uD83D\uDCA3","\uD83E\uDDE8","\uD83E\uDE93","\uD83D\uDD2A","\uD83D\uDDE1️","⚔️","\uD83D\uDEE1️","\uD83D\uDEAC","⚰️","\uD83E\uDEA6","⚱️","\uD83C\uDFFA","\uD83D\uDD2E","\uD83D\uDCFF","\uD83E\uDDFF","\uD83D\uDC88","⚗️","\uD83D\uDD2D","\uD83D\uDD2C","\uD83D\uDD73️","\uD83E\uDE79","\uD83E\uDE7A","\uD83D\uDC8A","\uD83D\uDC89","\uD83E\uDE78","\uD83E\uDDEC","\uD83E\uDDA0","\uD83E\uDDEB","\uD83E\uDDEA","\uD83C\uDF21️","\uD83E\uDDF9","\uD83E\uDEA3","\uD83E\uDDFD","\uD83E\uDDF4","\uD83D\uDECE️","\uD83D\uDD11","\uD83D\uDDDD️","\uD83D\uDEAA","\uD83E\uDE91","\uD83D\uDECB️","\uD83D\uDECF️","\uD83D\uDECC","\uD83E\uDDF8","\uD83E\uDE86","\uD83D\uDDBC️","\uD83E\uDE9E","\uD83E\uDE9F","\uD83D\uDECD️","\uD83D\uDED2","\uD83C\uDF81","\uD83C\uDF88","\uD83C\uDF8F","\uD83C\uDF80","\uD83E\uDE84","\uD83E\uDE85","\uD83C\uDF8A","\uD83C\uDF89","\uD83C\uDF8E","\uD83C\uDFEE","\uD83C\uDF90","\uD83E\uDDE7","✉️","\uD83D\uDCE9","\uD83D\uDCE8","\uD83D\uDCE7","\uD83D\uDC8C","\uD83D\uDCE5","\uD83D\uDCE4","\uD83D\uDCE6","\uD83C\uDFF7️","\uD83E\uDEA7","\uD83D\uDCEA","\uD83D\uDCEB","\uD83D\uDCEC","\uD83D\uDCED","\uD83D\uDCEE","\uD83D\uDCEF","\uD83D\uDCDC","\uD83D\uDCC3","\uD83D\uDCC4","\uD83D\uDCD1","\uD83E\uDDFE","\uD83D\uDCCA","\uD83D\uDCC8","\uD83D\uDCC9","\uD83D\uDDD2️","\uD83D\uDDD3️","\uD83D\uDCC6","\uD83D\uDCC5","\uD83D\uDDD1️","\uD83D\uDCC7","\uD83D\uDDC3️","\uD83D\uDDF3️","\uD83D\uDDC4️","\uD83D\uDCCB","\uD83D\uDCC1","\uD83D\uDCC2","\uD83D\uDDC2️","\uD83D\uDDDE️","\uD83D\uDCF0","\uD83D\uDCD3","\uD83D\uDCD4","\uD83D\uDCD2","\uD83D\uDCD5","\uD83D\uDCD7","\uD83D\uDCD8","\uD83D\uDCD9","\uD83D\uDCDA","\uD83D\uDCD6","\uD83D\uDD16","\uD83E\uDDF7","\uD83D\uDD17","\uD83D\uDCCE","\uD83D\uDD87️","\uD83D\uDCD0","\uD83D\uDCCF","\uD83E\uDDEE","\uD83D\uDCCC","\uD83D\uDCCD","✂️","\uD83D\uDD8A️","\uD83D\uDD8B️","✒️","\uD83D\uDD8C️","\uD83D\uDD8D️","\uD83D\uDCDD","✏️","\uD83D\uDD0D","\uD83D\uDD0E","\uD83D\uDD0F","\uD83D\uDD10","\uD83D\uDD12","\uD83D\uDD13"]},symbols:{name:"Symbols",icon:"❤️",emojis:["❤️","\uD83E\uDDE1","\uD83D\uDC9B","\uD83D\uDC9A","\uD83D\uDC99","\uD83D\uDC9C","\uD83D\uDDA4","\uD83E\uDD0D","\uD83E\uDD0E","\uD83D\uDC94","❣️","\uD83D\uDC95","\uD83D\uDC9E","\uD83D\uDC93","\uD83D\uDC97","\uD83D\uDC96","\uD83D\uDC98","\uD83D\uDC9D","\uD83D\uDC9F","☮️","✝️","☪️","\uD83D\uDD49️","☸️","✡️","\uD83D\uDD2F","\uD83D\uDD4E","☯️","☦️","\uD83D\uDED0","⛎","♈","♉","♊","♋","♌","♍","♎","♏","♐","♑","♒","♓","\uD83C\uDD94","⚛️","\uD83C\uDE51","☢️","☣️","\uD83D\uDCF4","\uD83D\uDCF3","\uD83C\uDE36","\uD83C\uDE1A","\uD83C\uDE38","\uD83C\uDE3A","\uD83C\uDE37️","✴️","\uD83C\uDD9A","\uD83D\uDCAE","\uD83C\uDE50","㊙️","㊗️","\uD83C\uDE34","\uD83C\uDE35","\uD83C\uDE39","\uD83C\uDE32","\uD83C\uDD70️","\uD83C\uDD71️","\uD83C\uDD8E","\uD83C\uDD91","\uD83C\uDD7E️","\uD83C\uDD98","❌","⭕","\uD83D\uDED1","⛔","\uD83D\uDCDB","\uD83D\uDEAB","\uD83D\uDCAF","\uD83D\uDCA2","♨️","\uD83D\uDEB7","\uD83D\uDEAF","\uD83D\uDEB3","\uD83D\uDEB1","\uD83D\uDD1E","\uD83D\uDCF5","\uD83D\uDEAD","❗","❕","❓","❔","‼️","⁉️","\uD83D\uDD05","\uD83D\uDD06","〽️","⚠️","\uD83D\uDEB8","\uD83D\uDD31","⚜️","\uD83D\uDD30","♻️","✅","\uD83C\uDE2F","\uD83D\uDCB9","❇️","✳️","❎","\uD83C\uDF10","\uD83D\uDCA0","Ⓜ️","\uD83C\uDF00","\uD83D\uDCA4","\uD83C\uDFE7","\uD83D\uDEBE","♿","\uD83C\uDD7F️","\uD83D\uDED7","\uD83C\uDE33","\uD83C\uDE02️","\uD83D\uDEC2","\uD83D\uDEC3","\uD83D\uDEC4","\uD83D\uDEC5","\uD83D\uDEB9","\uD83D\uDEBA","\uD83D\uDEBC","⚧️","\uD83D\uDEBB","\uD83D\uDEAE","\uD83C\uDFA6","\uD83D\uDCF6","\uD83C\uDE01","\uD83D\uDD23","ℹ️","\uD83D\uDD24","\uD83D\uDD21","\uD83D\uDD20","\uD83C\uDD96","\uD83C\uDD97","\uD83C\uDD99","\uD83C\uDD92","\uD83C\uDD95","\uD83C\uDD93","0️⃣","1️⃣","2️⃣","3️⃣","4️⃣","5️⃣","6️⃣","7️⃣","8️⃣","9️⃣","\uD83D\uDD1F","\uD83D\uDD22","#️⃣","*️⃣","⏏️","▶️","⏸️","⏯️","⏹️","⏺️","⏭️","⏮️","⏩","⏪","⏫","⏬","◀️","\uD83D\uDD3C","\uD83D\uDD3D","➡️","⬅️","⬆️","⬇️","↗️","↘️","↙️","↖️","↕️","↔️","↪️","↩️","⤴️","⤵️","\uD83D\uDD00","\uD83D\uDD01","\uD83D\uDD02","\uD83D\uDD04","\uD83D\uDD03","\uD83C\uDFB5","\uD83C\uDFB6","➕","➖","➗","✖️","\uD83D\uDFF0","♾️","\uD83D\uDCB2","\uD83D\uDCB1","™️","\xa9️","\xae️","〰️","➰","➿","\uD83D\uDD1A","\uD83D\uDD19","\uD83D\uDD1B","\uD83D\uDD1D","\uD83D\uDD1C","✔️","☑️","\uD83D\uDD18","\uD83D\uDD34","\uD83D\uDFE0","\uD83D\uDFE1","\uD83D\uDFE2","\uD83D\uDD35","\uD83D\uDFE3","⚫","⚪","\uD83D\uDFE4","\uD83D\uDD3A","\uD83D\uDD3B","\uD83D\uDD38","\uD83D\uDD39","\uD83D\uDD36","\uD83D\uDD37","\uD83D\uDD33","\uD83D\uDD32","▪️","▫️","◾","◽","◼️","◻️","\uD83D\uDFE5","\uD83D\uDFE7","\uD83D\uDFE8","\uD83D\uDFE9","\uD83D\uDFE6","\uD83D\uDFEA","⬛","⬜","\uD83D\uDFEB","\uD83D\uDD08","\uD83D\uDD07","\uD83D\uDD09","\uD83D\uDD0A","\uD83D\uDD14","\uD83D\uDD15","\uD83D\uDCE3","\uD83D\uDCE2","\uD83D\uDC41️‍\uD83D\uDDE8️","\uD83D\uDCAC","\uD83D\uDCAD","\uD83D\uDDEF️","♠️","♣️","♥️","♦️","\uD83C\uDCCF","\uD83C\uDFB4","\uD83C\uDC04","\uD83D\uDD50","\uD83D\uDD51","\uD83D\uDD52","\uD83D\uDD53","\uD83D\uDD54","\uD83D\uDD55","\uD83D\uDD56","\uD83D\uDD57","\uD83D\uDD58","\uD83D\uDD59","\uD83D\uDD5A","\uD83D\uDD5B","\uD83D\uDD5C","\uD83D\uDD5D","\uD83D\uDD5E","\uD83D\uDD5F","\uD83D\uDD60","\uD83D\uDD61","\uD83D\uDD62","\uD83D\uDD63","\uD83D\uDD64","\uD83D\uDD65","\uD83D\uDD66","\uD83D\uDD67"]},flags:{name:"Flags",icon:"\uD83C\uDFC1",emojis:["\uD83C\uDFC1","\uD83D\uDEA9","\uD83C\uDF8C","\uD83C\uDFF4","\uD83C\uDFF3️","\uD83C\uDFF3️‍\uD83C\uDF08","\uD83C\uDFF3️‍⚧️","\uD83C\uDFF4‍☠️","\uD83C\uDDE6\uD83C\uDDE8","\uD83C\uDDE6\uD83C\uDDE9","\uD83C\uDDE6\uD83C\uDDEA","\uD83C\uDDE6\uD83C\uDDEB","\uD83C\uDDE6\uD83C\uDDEC","\uD83C\uDDE6\uD83C\uDDEE","\uD83C\uDDE6\uD83C\uDDF1","\uD83C\uDDE6\uD83C\uDDF2","\uD83C\uDDE6\uD83C\uDDF4","\uD83C\uDDE6\uD83C\uDDF6","\uD83C\uDDE6\uD83C\uDDF7","\uD83C\uDDE6\uD83C\uDDF8","\uD83C\uDDE6\uD83C\uDDF9","\uD83C\uDDE6\uD83C\uDDFA","\uD83C\uDDE6\uD83C\uDDFC","\uD83C\uDDE6\uD83C\uDDFD","\uD83C\uDDE6\uD83C\uDDFF","\uD83C\uDDE7\uD83C\uDDE6","\uD83C\uDDE7\uD83C\uDDE7","\uD83C\uDDE7\uD83C\uDDE9","\uD83C\uDDE7\uD83C\uDDEA","\uD83C\uDDE7\uD83C\uDDEB","\uD83C\uDDE7\uD83C\uDDEC","\uD83C\uDDE7\uD83C\uDDED","\uD83C\uDDE7\uD83C\uDDEE","\uD83C\uDDE7\uD83C\uDDEF","\uD83C\uDDE7\uD83C\uDDF1","\uD83C\uDDE7\uD83C\uDDF2","\uD83C\uDDE7\uD83C\uDDF3","\uD83C\uDDE7\uD83C\uDDF4","\uD83C\uDDE7\uD83C\uDDF6","\uD83C\uDDE7\uD83C\uDDF7","\uD83C\uDDE7\uD83C\uDDF8","\uD83C\uDDE7\uD83C\uDDF9","\uD83C\uDDE7\uD83C\uDDFB","\uD83C\uDDE7\uD83C\uDDFC","\uD83C\uDDE7\uD83C\uDDFE","\uD83C\uDDE7\uD83C\uDDFF","\uD83C\uDDE8\uD83C\uDDE6","\uD83C\uDDE8\uD83C\uDDE8","\uD83C\uDDE8\uD83C\uDDE9","\uD83C\uDDE8\uD83C\uDDEB","\uD83C\uDDE8\uD83C\uDDEC","\uD83C\uDDE8\uD83C\uDDED","\uD83C\uDDE8\uD83C\uDDEE","\uD83C\uDDE8\uD83C\uDDF0","\uD83C\uDDE8\uD83C\uDDF1","\uD83C\uDDE8\uD83C\uDDF2","\uD83C\uDDE8\uD83C\uDDF3","\uD83C\uDDE8\uD83C\uDDF4","\uD83C\uDDE8\uD83C\uDDF5","\uD83C\uDDE8\uD83C\uDDF7","\uD83C\uDDE8\uD83C\uDDFA","\uD83C\uDDE8\uD83C\uDDFB","\uD83C\uDDE8\uD83C\uDDFC","\uD83C\uDDE8\uD83C\uDDFD","\uD83C\uDDE8\uD83C\uDDFE","\uD83C\uDDE8\uD83C\uDDFF","\uD83C\uDDE9\uD83C\uDDEA","\uD83C\uDDE9\uD83C\uDDEC","\uD83C\uDDE9\uD83C\uDDEF","\uD83C\uDDE9\uD83C\uDDF0","\uD83C\uDDE9\uD83C\uDDF2","\uD83C\uDDE9\uD83C\uDDF4","\uD83C\uDDE9\uD83C\uDDFF","\uD83C\uDDEA\uD83C\uDDE6","\uD83C\uDDEA\uD83C\uDDE8","\uD83C\uDDEA\uD83C\uDDEA","\uD83C\uDDEA\uD83C\uDDEC","\uD83C\uDDEA\uD83C\uDDED","\uD83C\uDDEA\uD83C\uDDF7","\uD83C\uDDEA\uD83C\uDDF8","\uD83C\uDDEA\uD83C\uDDF9","\uD83C\uDDEA\uD83C\uDDFA","\uD83C\uDDEB\uD83C\uDDEE","\uD83C\uDDEB\uD83C\uDDEF","\uD83C\uDDEB\uD83C\uDDF0","\uD83C\uDDEB\uD83C\uDDF2","\uD83C\uDDEB\uD83C\uDDF4","\uD83C\uDDEB\uD83C\uDDF7","\uD83C\uDDEC\uD83C\uDDE6","\uD83C\uDDEC\uD83C\uDDE7","\uD83C\uDDEC\uD83C\uDDE9","\uD83C\uDDEC\uD83C\uDDEA","\uD83C\uDDEC\uD83C\uDDEB","\uD83C\uDDEC\uD83C\uDDEC","\uD83C\uDDEC\uD83C\uDDED","\uD83C\uDDEC\uD83C\uDDEE","\uD83C\uDDEC\uD83C\uDDF1","\uD83C\uDDEC\uD83C\uDDF2","\uD83C\uDDEC\uD83C\uDDF3","\uD83C\uDDEC\uD83C\uDDF5","\uD83C\uDDEC\uD83C\uDDF6","\uD83C\uDDEC\uD83C\uDDF7","\uD83C\uDDEC\uD83C\uDDF8","\uD83C\uDDEC\uD83C\uDDF9","\uD83C\uDDEC\uD83C\uDDFA","\uD83C\uDDEC\uD83C\uDDFC","\uD83C\uDDEC\uD83C\uDDFE","\uD83C\uDDED\uD83C\uDDF0","\uD83C\uDDED\uD83C\uDDF2","\uD83C\uDDED\uD83C\uDDF3","\uD83C\uDDED\uD83C\uDDF7","\uD83C\uDDED\uD83C\uDDF9","\uD83C\uDDED\uD83C\uDDFA","\uD83C\uDDEE\uD83C\uDDE8","\uD83C\uDDEE\uD83C\uDDE9","\uD83C\uDDEE\uD83C\uDDEA","\uD83C\uDDEE\uD83C\uDDF1","\uD83C\uDDEE\uD83C\uDDF2","\uD83C\uDDEE\uD83C\uDDF3","\uD83C\uDDEE\uD83C\uDDF4","\uD83C\uDDEE\uD83C\uDDF6","\uD83C\uDDEE\uD83C\uDDF7","\uD83C\uDDEE\uD83C\uDDF8","\uD83C\uDDEE\uD83C\uDDF9","\uD83C\uDDEF\uD83C\uDDEA","\uD83C\uDDEF\uD83C\uDDF2","\uD83C\uDDEF\uD83C\uDDF4","\uD83C\uDDEF\uD83C\uDDF5","\uD83C\uDDF0\uD83C\uDDEA","\uD83C\uDDF0\uD83C\uDDEC","\uD83C\uDDF0\uD83C\uDDED","\uD83C\uDDF0\uD83C\uDDEE","\uD83C\uDDF0\uD83C\uDDF2","\uD83C\uDDF0\uD83C\uDDF3","\uD83C\uDDF0\uD83C\uDDF5","\uD83C\uDDF0\uD83C\uDDF7","\uD83C\uDDF0\uD83C\uDDFC","\uD83C\uDDF0\uD83C\uDDFE","\uD83C\uDDF0\uD83C\uDDFF","\uD83C\uDDF1\uD83C\uDDE6","\uD83C\uDDF1\uD83C\uDDE7","\uD83C\uDDF1\uD83C\uDDE8","\uD83C\uDDF1\uD83C\uDDEE","\uD83C\uDDF1\uD83C\uDDF0","\uD83C\uDDF1\uD83C\uDDF7","\uD83C\uDDF1\uD83C\uDDF8","\uD83C\uDDF1\uD83C\uDDF9","\uD83C\uDDF1\uD83C\uDDFA","\uD83C\uDDF1\uD83C\uDDFB","\uD83C\uDDF1\uD83C\uDDFE","\uD83C\uDDF2\uD83C\uDDE6","\uD83C\uDDF2\uD83C\uDDE8","\uD83C\uDDF2\uD83C\uDDE9","\uD83C\uDDF2\uD83C\uDDEA","\uD83C\uDDF2\uD83C\uDDEB","\uD83C\uDDF2\uD83C\uDDEC","\uD83C\uDDF2\uD83C\uDDED","\uD83C\uDDF2\uD83C\uDDF0","\uD83C\uDDF2\uD83C\uDDF1","\uD83C\uDDF2\uD83C\uDDF2","\uD83C\uDDF2\uD83C\uDDF3","\uD83C\uDDF2\uD83C\uDDF4","\uD83C\uDDF2\uD83C\uDDF5","\uD83C\uDDF2\uD83C\uDDF6","\uD83C\uDDF2\uD83C\uDDF7","\uD83C\uDDF2\uD83C\uDDF8","\uD83C\uDDF2\uD83C\uDDF9","\uD83C\uDDF2\uD83C\uDDFA","\uD83C\uDDF2\uD83C\uDDFB","\uD83C\uDDF2\uD83C\uDDFC","\uD83C\uDDF2\uD83C\uDDFD","\uD83C\uDDF2\uD83C\uDDFE","\uD83C\uDDF2\uD83C\uDDFF","\uD83C\uDDF3\uD83C\uDDE6","\uD83C\uDDF3\uD83C\uDDE8","\uD83C\uDDF3\uD83C\uDDEA","\uD83C\uDDF3\uD83C\uDDEB","\uD83C\uDDF3\uD83C\uDDEC","\uD83C\uDDF3\uD83C\uDDEE","\uD83C\uDDF3\uD83C\uDDF1","\uD83C\uDDF3\uD83C\uDDF4","\uD83C\uDDF3\uD83C\uDDF5","\uD83C\uDDF3\uD83C\uDDF7","\uD83C\uDDF3\uD83C\uDDFA","\uD83C\uDDF3\uD83C\uDDFF","\uD83C\uDDF4\uD83C\uDDF2","\uD83C\uDDF5\uD83C\uDDE6","\uD83C\uDDF5\uD83C\uDDEA","\uD83C\uDDF5\uD83C\uDDEB","\uD83C\uDDF5\uD83C\uDDEC","\uD83C\uDDF5\uD83C\uDDED","\uD83C\uDDF5\uD83C\uDDF0","\uD83C\uDDF5\uD83C\uDDF1","\uD83C\uDDF5\uD83C\uDDF2","\uD83C\uDDF5\uD83C\uDDF3","\uD83C\uDDF5\uD83C\uDDF7","\uD83C\uDDF5\uD83C\uDDF8","\uD83C\uDDF5\uD83C\uDDF9","\uD83C\uDDF5\uD83C\uDDFC","\uD83C\uDDF5\uD83C\uDDFE","\uD83C\uDDF6\uD83C\uDDE6","\uD83C\uDDF7\uD83C\uDDEA","\uD83C\uDDF7\uD83C\uDDF4","\uD83C\uDDF7\uD83C\uDDF8","\uD83C\uDDF7\uD83C\uDDFA","\uD83C\uDDF7\uD83C\uDDFC","\uD83C\uDDF8\uD83C\uDDE6","\uD83C\uDDF8\uD83C\uDDE7","\uD83C\uDDF8\uD83C\uDDE8","\uD83C\uDDF8\uD83C\uDDE9","\uD83C\uDDF8\uD83C\uDDEA","\uD83C\uDDF8\uD83C\uDDEC","\uD83C\uDDF8\uD83C\uDDED","\uD83C\uDDF8\uD83C\uDDEE","\uD83C\uDDF8\uD83C\uDDEF","\uD83C\uDDF8\uD83C\uDDF0","\uD83C\uDDF8\uD83C\uDDF1","\uD83C\uDDF8\uD83C\uDDF2","\uD83C\uDDF8\uD83C\uDDF3","\uD83C\uDDF8\uD83C\uDDF4","\uD83C\uDDF8\uD83C\uDDF7","\uD83C\uDDF8\uD83C\uDDF8","\uD83C\uDDF8\uD83C\uDDF9","\uD83C\uDDF8\uD83C\uDDFB","\uD83C\uDDF8\uD83C\uDDFD","\uD83C\uDDF8\uD83C\uDDFE","\uD83C\uDDF8\uD83C\uDDFF","\uD83C\uDDF9\uD83C\uDDE6","\uD83C\uDDF9\uD83C\uDDE8","\uD83C\uDDF9\uD83C\uDDE9","\uD83C\uDDF9\uD83C\uDDEB","\uD83C\uDDF9\uD83C\uDDEC","\uD83C\uDDF9\uD83C\uDDED","\uD83C\uDDF9\uD83C\uDDEF","\uD83C\uDDF9\uD83C\uDDF0","\uD83C\uDDF9\uD83C\uDDF1","\uD83C\uDDF9\uD83C\uDDF2","\uD83C\uDDF9\uD83C\uDDF3","\uD83C\uDDF9\uD83C\uDDF4","\uD83C\uDDF9\uD83C\uDDF7","\uD83C\uDDF9\uD83C\uDDF9","\uD83C\uDDF9\uD83C\uDDFB","\uD83C\uDDF9\uD83C\uDDFC","\uD83C\uDDF9\uD83C\uDDFF","\uD83C\uDDFA\uD83C\uDDE6","\uD83C\uDDFA\uD83C\uDDEC","\uD83C\uDDFA\uD83C\uDDF2","\uD83C\uDDFA\uD83C\uDDF3","\uD83C\uDDFA\uD83C\uDDF8","\uD83C\uDDFA\uD83C\uDDFE","\uD83C\uDDFA\uD83C\uDDFF","\uD83C\uDDFB\uD83C\uDDE6","\uD83C\uDDFB\uD83C\uDDE8","\uD83C\uDDFB\uD83C\uDDEA","\uD83C\uDDFB\uD83C\uDDEC","\uD83C\uDDFB\uD83C\uDDEE","\uD83C\uDDFB\uD83C\uDDF3","\uD83C\uDDFB\uD83C\uDDFA","\uD83C\uDDFC\uD83C\uDDEB","\uD83C\uDDFC\uD83C\uDDF8","\uD83C\uDDFD\uD83C\uDDF0","\uD83C\uDDFE\uD83C\uDDEA","\uD83C\uDDFE\uD83C\uDDF9","\uD83C\uDDFF\uD83C\uDDE6","\uD83C\uDDFF\uD83C\uDDF2","\uD83C\uDDFF\uD83C\uDDFC","\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67\uDB40\uDC7F","\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74\uDB40\uDC7F","\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73\uDB40\uDC7F"]}},ew=["#ef4444","#f97316","#f59e0b","#eab308","#84cc16","#22c55e","#10b981","#14b8a6","#06b6d4","#0ea5e9","#3b82f6","#6366f1","#8b5cf6","#a855f7","#d946ef","#ec4899","#f43f5e","#64748b","#6b7280","#374151","#111827","#000000","#ffffff","#f8fafc"],eN=e=>{let{children:t,agentId:a,currentEmoji:n,currentColor:l,onStyleChange:i}=e,[o,d]=(0,r.useState)(l||"#3b82f6"),[c,u]=(0,r.useState)(n||"\uD83D\uDE00"),[m,g]=(0,r.useState)(!1),[x,h]=(0,r.useState)(""),[p,b]=(0,r.useState)("smileys");r.useEffect(()=>{l&&d(l)},[l]),r.useEffect(()=>{n&&u(n)},[n]);let v=(0,r.useMemo)(()=>x?Object.values(ej).flatMap(e=>e.emojis).filter(e=>e.includes(x)):ej[p].emojis,[x,p]);return(0,s.jsxs)(eb.AM,{open:m,onOpenChange:g,children:[(0,s.jsx)(eb.Wv,{asChild:!0,children:t}),(0,s.jsx)(eb.hl,{className:"w-96 p-0",align:"start",children:(0,s.jsxs)(F.Zp,{className:"border-0 shadow-none",children:[(0,s.jsxs)(F.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-6 h-6 rounded-full border-2 border-gray-200",style:{backgroundColor:o}}),(0,s.jsx)("span",{className:"font-medium",children:"Color"})]}),(0,s.jsx)("div",{className:"grid grid-cols-8 gap-2",children:ew.map(e=>(0,s.jsx)("button",{className:"w-8 h-8 rounded-lg border-2 transition-all hover:scale-110 ".concat(o===e?"border-gray-900 shadow-md":"border-gray-200 hover:border-gray-300"),style:{backgroundColor:e},onClick:()=>d(e)},e))}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("input",{type:"color",value:o,onChange:e=>d(e.target.value),className:"w-8 h-8 rounded border cursor-pointer"}),(0,s.jsx)("input",{type:"text",value:o,onChange:e=>d(e.target.value),className:"flex-1 px-2 py-1 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"#000000"})]})]}),(0,s.jsx)(ev.w,{}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"text-xl",children:c}),(0,s.jsx)("span",{className:"font-medium",children:"Emoji"})]}),!x&&(0,s.jsxs)(A.tU,{value:p,onValueChange:b,className:"w-full",children:[(0,s.jsx)(A.j7,{className:"grid w-full grid-cols-4 h-auto p-1",children:Object.entries(ej).slice(0,4).map(e=>{let[t,a]=e;return(0,s.jsx)(A.Xi,{value:t,className:"text-xs p-1",children:(0,s.jsx)("span",{className:"text-sm",children:a.icon})},t)})}),(0,s.jsx)(A.j7,{className:"grid w-full grid-cols-4 h-auto p-1 mt-1",children:Object.entries(ej).slice(4,8).map(e=>{let[t,a]=e;return(0,s.jsx)(A.Xi,{value:t,className:"text-xs p-1",children:(0,s.jsx)("span",{className:"text-sm",children:a.icon})},t)})})]}),(0,s.jsx)(Y.F,{className:"h-48 w-full",children:(0,s.jsx)("div",{className:"grid grid-cols-8 gap-1 p-1",children:v.map((e,t)=>(0,s.jsx)("button",{className:"w-8 h-8 text-lg hover:bg-gray-100 rounded transition-colors ".concat(c===e?"bg-blue-100 ring-2 ring-blue-500":""),onClick:()=>u(e),children:e},"".concat(e,"-").concat(t)))})}),x&&0===v.length&&(0,s.jsxs)("div",{className:"text-center text-muted-foreground py-4",children:['No emojis found for "',x,'"']})]}),(0,s.jsx)(ev.w,{})]}),(0,s.jsxs)(F.wL,{className:"flex justify-end gap-2",children:[(0,s.jsx)(f.$,{variant:"outline",onClick:()=>{let e=["\uD83E\uDD16","\uD83C\uDFAF","⚡","\uD83D\uDE80","\uD83D\uDD2E","\uD83C\uDFA8","\uD83D\uDCCA","\uD83D\uDD27","\uD83D\uDCA1","\uD83C\uDF1F"],t=["#ef4444","#f97316","#f59e0b","#eab308","#84cc16","#22c55e","#10b981","#14b8a6","#06b6d4","#0ea5e9","#3b82f6","#6366f1","#8b5cf6","#a855f7"],s=a.split("").reduce((e,t)=>e+t.charCodeAt(0),0)%e.length,r=a.split("").reduce((e,t)=>e+t.charCodeAt(0),0)%t.length;u(e[s]),d(t[r]),h(""),b("smileys")},children:"Reset"}),(0,s.jsx)(f.$,{onClick:()=>{i(c,o),g(!1)},children:"Save"})]})]})})]})};var ey=a(52695),e_=a(46102),eC=a(26715),eS=a(58350);let ek=r.memo(function(e){let{agentId:t,formData:a,handleFieldChange:n,handleStyleChange:l,currentStyle:i}=e,[o,d]=(0,r.useState)(null),[c,u]=(0,r.useState)(null),[m,g]=(0,r.useState)([]),[x,h]=(0,r.useState)(""),[f,p]=(0,r.useState)("idle"),[b,v]=(0,r.useState)("idle"),[j,w]=(0,r.useState)(!1),[N,y]=(0,r.useState)(!1),_=(0,r.useRef)(null),C=(0,r.useRef)(null),S=(0,r.useRef)(0),k=(0,r.useRef)(!1),E=(0,r.useRef)(null);(0,r.useEffect)(()=>(console.log("[AgentBuilderChat] Component mounted"),()=>{console.log("[AgentBuilderChat] Component unmounted")}),[]),(0,r.useEffect)(()=>{null!==E.current&&E.current!==t&&(console.log("[AgentBuilderChat] Agent ID changed, resetting state"),k.current=!1,g([]),d(null),u(null),y(!1),S.current=0),E.current=t},[t]);let A=(0,eo.u)(),I=(0,ec.U)(),P=(0,eu.WZ)(),R=(0,eu.CV)(),F=(0,T.gc)(t),D=(0,eC.jE)(),O=()=>{var e;null==(e=_.current)||e.scrollIntoView({behavior:"smooth"})};(0,r.useEffect)(()=>{m&&m.length>S.current&&O(),S.current=(null==m?void 0:m.length)||0},[m,null==m?void 0:m.length]),(0,r.useEffect)(()=>{if(F.data&&"success"===F.status&&!k.current){console.log("[AgentBuilderChat] Loading chat history for agent:",t);let{messages:e,thread_id:a}=F.data;if(e&&e.length>0){let t=e.filter(e=>"status"!==e.type).map(e=>({message_id:e.message_id||"msg-".concat(Date.now(),"-").concat(Math.random()),thread_id:e.thread_id||a,type:e.type||"system",is_llm_message:!!e.is_llm_message,content:e.content||"",metadata:e.metadata||"{}",created_at:e.created_at||new Date().toISOString(),updated_at:e.updated_at||new Date().toISOString(),sequence:0}));g(t),y(t.length>0),S.current=t.length,a&&d(a)}k.current=!0}else"error"===F.status&&(console.error("[AgentBuilderChat] Error loading chat history:",F.error),k.current=!0)},[F.data,F.status,F.error,t]);let z=(0,r.useCallback)(e=>{g(t=>{if(t||(t=[]),"user"===e.type){let a=t.findIndex(t=>t.message_id.startsWith("temp-user-")&&t.content===e.content&&"user"===t.type);if(-1!==a){console.log("[AGENT BUILDER] Replacing optimistic message with real message");let s=[...t];return s[a]=e,s}}return t.some(t=>t.message_id===e.message_id)?t.map(t=>t.message_id===e.message_id?e:t):[...t,e]})},[]),L=(0,r.useCallback)(e=>{switch(e){case"idle":case"completed":case"stopped":case"agent_not_running":case"error":case"failed":p("idle"),u(null),"completed"===e&&(v("saved"),D.invalidateQueries({queryKey:eS._.lists()}),D.invalidateQueries({queryKey:eS._.detail(t)}),D.invalidateQueries({queryKey:eS._.builderChatHistory(t)}),setTimeout(()=>v("idle"),2e3));break;case"connecting":p("connecting");break;case"streaming":p("running")}},[]),V=(0,r.useCallback)(e=>{e.toLowerCase().includes("not found")||e.toLowerCase().includes("agent run is not running")||es.oR.error("Stream Error: ".concat(e))},[]),B=(0,r.useCallback)(()=>{console.log("[AGENT BUILDER] Stream closed")},[]),{status:U,textContent:M,toolCall:W,error:$,agentRunId:q,startStreaming:Z,stopStreaming:G}=(0,ed.Z)({onMessage:z,onStatusChange:L,onError:V,onClose:B},o,g);(0,r.useEffect)(()=>{c&&c!==q&&o&&Z(c)},[c,Z,q,o]);let Y=async(e,a)=>{var s,r,n,l,i,o,c;if(e.trim()||(null==(s=C.current)?void 0:s.getPendingFiles().length)){w(!0),y(!0),v("saving");try{let s=(null==(r=C.current)?void 0:r.getPendingFiles())||[],m=new FormData;m.append("prompt",e),m.append("is_agent_builder",String(!0)),m.append("target_agent_id",t),s.forEach(e=>{let t=(0,eg.L)(e.name);m.append("files",e,t)}),(null==a?void 0:a.model_name)&&m.append("model_name",a.model_name),m.append("enable_thinking",String(null!=(l=null==a?void 0:a.enable_thinking)&&l)),m.append("reasoning_effort",null!=(i=null==a?void 0:a.reasoning_effort)?i:"low"),m.append("stream",String(null==(o=null==a?void 0:a.stream)||o)),m.append("enable_context_manager",String(null!=(c=null==a?void 0:a.enable_context_manager)&&c));let x=await A.mutateAsync(m);if(x.thread_id){d(x.thread_id),x.agent_run_id&&(console.log("[AGENT BUILDER] Setting agent run ID:",x.agent_run_id),u(x.agent_run_id));let t={message_id:"user-".concat(Date.now()),thread_id:x.thread_id,type:"user",is_llm_message:!1,content:e,metadata:"{}",created_at:new Date().toISOString(),updated_at:new Date().toISOString(),sequence:0};g(e=>[...e,t])}null==(n=C.current)||n.clearPendingFiles(),h("")}catch(e){e instanceof em.Ey?es.oR.error("Billing limit reached. Please upgrade your plan."):es.oR.error("Failed to start agent builder session"),y(!1),v("idle")}finally{w(!1)}}},J=(0,r.useCallback)(async(e,t)=>{if(!e.trim()||!o)return;w(!0),v("saving");let a={message_id:"temp-user-".concat(Date.now(),"-").concat(Math.random()),thread_id:o,type:"user",is_llm_message:!1,content:e,metadata:"{}",created_at:new Date().toISOString(),updated_at:new Date().toISOString(),sequence:m.length};g(e=>[...e,a]),h("");try{let r=I.mutateAsync({threadId:o,message:e}),n=P.mutateAsync({threadId:o,options:t}),l=await Promise.allSettled([r,n]);if("rejected"===l[0].status){var s;throw Error("Failed to send message: ".concat((null==(s=l[0].reason)?void 0:s.message)||l[0].reason))}if("rejected"===l[1].status){let e=l[1].reason;if(e instanceof em.Ey){es.oR.error("Billing limit reached. Please upgrade your plan."),g(e=>e.filter(e=>e.message_id!==a.message_id));return}throw Error("Failed to start agent: ".concat((null==e?void 0:e.message)||e))}let i=l[1].value;u(i.agent_run_id)}catch(e){es.oR.error(e instanceof Error?e.message:"Operation failed"),g(e=>e.map(e=>e.message_id===a.message_id?{...e,message_id:"user-error-".concat(Date.now())}:e)),v("idle")}finally{w(!1)}},[o,null==m?void 0:m.length,I,P]),K=(0,r.useCallback)(async()=>{if(p("idle"),await G(),c)try{await R.mutateAsync(c)}catch(e){console.error("[AGENT BUILDER] Error stopping agent:",e)}},[G,c,R]),H=(0,r.useCallback)(()=>{},[]);return(0,s.jsxs)("div",{className:"flex flex-col h-full",children:[(0,s.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,s.jsxs)("div",{className:"h-full overflow-y-auto scrollbar-hide",children:[(0,s.jsx)(ei.u9,{messages:m||[],streamingTextContent:M,streamingToolCall:W,agentStatus:f,handleToolClick:()=>{},handleOpenFileViewer:H,streamHookStatus:U,agentName:"Agent Builder",agentAvatar:"\uD83E\uDD16",emptyStateComponent:(0,s.jsxs)("div",{className:"mt-6 flex flex-col items-center text-center text-muted-foreground/80",children:[(0,s.jsx)("div",{className:"flex w-20 aspect-square items-center justify-center rounded-2xl bg-muted-foreground/10 p-4 mb-4",children:(0,s.jsx)("div",{className:"text-4xl",children:"\uD83E\uDD16"})}),(0,s.jsxs)("p",{className:"w-[60%] text-2xl",children:["I'm your ",(0,s.jsx)("span",{className:"text-primary/80 font-semibold",children:"Agent Builder"}),". Describe the exact workflows and tasks you want to automate, and I'll configure your agent to handle them."]})]})}),(0,s.jsx)("div",{ref:_})]})}),(0,s.jsx)("div",{className:"flex-shrink-0 md:pb-4 md:px-12 px-4",children:(0,s.jsx)(el.V,{ref:C,onSubmit:o?J:Y,loading:j,placeholder:"Tell me how you'd like to configure your agent...",value:x,onChange:h,disabled:j,isAgentRunning:"running"===f||"connecting"===f,onStopAgent:K,agentName:"Agent Builder",hideAttachments:!0,bgColor:"bg-muted-foreground/10",hideAgentSelection:!0})})]})},(e,t)=>e.agentId===t.agentId&&JSON.stringify(e.formData)===JSON.stringify(t.formData)&&e.currentStyle.avatar===t.currentStyle.avatar&&e.currentStyle.color===t.currentStyle.color&&e.handleFieldChange===t.handleFieldChange&&e.handleStyleChange===t.handleStyleChange);var eE=a(70567),eA=a(40538),eT=a(84779);function eI(){let e=(0,n.useParams)(),t=(0,n.useRouter)(),a=e.agentId,{data:j,isLoading:w,error:_}=(0,T.fJ)(a),C=(0,T.Ae)(),{state:I,setOpen:P,setOpenMobile:R}=(0,ey.cL)(),F=(0,r.useRef)(!1),[D,O]=(0,r.useState)({name:"",description:"",system_prompt:"",agentpress_tools:{},configured_mcps:[],custom_mcps:[],is_default:!1,avatar:"",avatar_color:""}),z=(0,r.useRef)(null),L=(0,r.useRef)(D),[V,B]=(0,r.useState)("idle"),U=(0,r.useRef)(null),[M,W]=(0,r.useState)(!1),[$,q]=(0,r.useState)("agent-builder"),Z=(0,r.useRef)(null);(0,r.useEffect)(()=>{F.current||(P(!1),F.current=!0)},[P]),(0,r.useEffect)(()=>{if(j){let e={name:j.name||"",description:j.description||"",system_prompt:j.system_prompt||"",agentpress_tools:j.agentpress_tools||{},configured_mcps:j.configured_mcps||[],custom_mcps:j.custom_mcps||[],is_default:j.is_default||!1,avatar:j.avatar||"",avatar_color:j.avatar_color||""};O(e),z.current={...e}}},[j]),(0,r.useEffect)(()=>{if(_){let e=_ instanceof Error?_.message:String(_);if(e.includes("Access denied")||e.includes("403")){es.oR.error("You don't have permission to edit this agent"),t.push("/agents");return}}},[_,t]),(0,r.useEffect)(()=>{L.current=D},[D]);let G=(0,r.useCallback)((e,t)=>!t||e.name!==t.name||e.description!==t.description||e.system_prompt!==t.system_prompt||e.is_default!==t.is_default||e.avatar!==t.avatar||e.avatar_color!==t.avatar_color||JSON.stringify(e.agentpress_tools)!==JSON.stringify(t.agentpress_tools)||JSON.stringify(e.configured_mcps)!==JSON.stringify(t.configured_mcps)||JSON.stringify(e.custom_mcps)!==JSON.stringify(t.custom_mcps)||!1,[]),Y=(0,r.useCallback)(async e=>{try{B("saving"),await C.mutateAsync({agentId:a,...e}),z.current={...e},B("saved"),setTimeout(()=>B("idle"),2e3)}catch(e){console.error("Error updating agent:",e),B("error"),es.oR.error("Failed to update agent"),setTimeout(()=>B("idle"),3e3)}},[a,C]),J=(0,r.useCallback)(e=>{U.current&&clearTimeout(U.current),G(e,z.current)&&(U.current=setTimeout(()=>{G(e,z.current)&&Y(e)},500))},[Y,G]),K=(0,r.useCallback)((e,t)=>{let a={...L.current,[e]:t};O(a),J(a)},[J]),H=(0,r.useCallback)(e=>{let t={...L.current,configured_mcps:e.configured_mcps,custom_mcps:e.custom_mcps};O(t),J(t)},[J]),X=(0,r.useCallback)(()=>{Z.current&&Z.current.scrollIntoView({behavior:"smooth",block:"end"})},[]),Q=(0,r.useCallback)((e,t)=>{let a={...L.current,avatar:e,avatar_color:t};O(a),J(a)},[J]),ee=(0,r.useMemo)(()=>D.avatar&&D.avatar_color?{avatar:D.avatar,color:D.avatar_color}:(0,en.Z)(a),[D.avatar,D.avatar_color,a]),et=(0,r.useMemo)(()=>(0,s.jsx)(ek,{agentId:a,formData:D,handleFieldChange:K,handleStyleChange:Q,currentStyle:ee}),[a,D,K,Q,ee]),el=()=>{let e="idle"===V&&!G(D,z.current);switch(V){case"saving":return(0,s.jsxs)(b.E,{variant:"secondary",className:"flex items-center gap-1 text-amber-700 dark:text-amber-300 bg-amber-600/30 hover:bg-amber-700/40",children:[(0,s.jsx)(l.A,{className:"h-3 w-3 animate-pulse"}),"Saving..."]});case"saved":return(0,s.jsxs)(b.E,{variant:"default",className:"flex items-center gap-1 text-green-700 dark:text-green-300 bg-green-600/30 hover:bg-green-700/40",children:[(0,s.jsx)(i.A,{className:"h-3 w-3"}),"Saved"]});case"error":return(0,s.jsx)(b.E,{variant:"destructive",className:"flex items-center gap-1 text-red-700 dark:text-red-300 bg-red-600/30 hover:bg-red-700/40",children:"Error saving"});default:return e?(0,s.jsxs)(b.E,{variant:"default",className:"flex items-center gap-1 text-green-700 dark:text-green-300 bg-green-600/30 hover:bg-green-700/40",children:[(0,s.jsx)(i.A,{className:"h-3 w-3"}),"Saved"]}):(0,s.jsx)(b.E,{variant:"destructive",className:"flex items-center gap-1 text-red-700 dark:text-red-300 bg-red-600/30 hover:bg-red-700/40",children:"Error saving"})}},ei=(0,r.useMemo)(()=>(0,s.jsxs)("div",{className:"h-full flex flex-col",children:[(0,s.jsxs)("div",{className:"md:hidden flex justify-between items-center mb-4 p-4 pb-0",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(e_.m_,{children:[(0,s.jsx)(e_.k$,{asChild:!0,children:(0,s.jsx)("button",{onClick:()=>R(!0),className:"h-8 w-8 flex items-center justify-center rounded-md hover:bg-accent",children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})}),(0,s.jsx)(e_.ZI,{children:"Open menu"})]}),(0,s.jsx)("div",{className:"md:hidden flex justify-center",children:el()})]}),(0,s.jsxs)(N,{open:M,onOpenChange:W,children:[(0,s.jsx)(y,{asChild:!0,children:(0,s.jsxs)(f.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(d.A,{className:"h-4 w-4"}),"Preview"]})}),(0,s.jsxs)(S,{className:"h-[90vh] bg-muted",children:[(0,s.jsx)(k,{children:(0,s.jsx)(E,{children:"Agent Preview"})}),(0,s.jsx)("div",{className:"flex-1 overflow-y-auto px-4 pb-4",children:(0,s.jsx)(ex,{agent:{...j,...D}})})]})]})]}),(0,s.jsxs)(A.tU,{value:$,onValueChange:q,className:"flex-1 flex flex-col overflow-hidden",children:[(0,s.jsx)("div",{className:"w-full flex items-center justify-center flex-shrink-0 px-4 md:px-12 md:mt-10",children:(0,s.jsx)("div",{className:"w-auto flex items-center gap-2",children:(0,s.jsxs)(A.j7,{className:"grid h-auto w-full grid-cols-2 bg-muted-foreground/10",children:[(0,s.jsx)(A.Xi,{value:"agent-builder",className:"w-48 flex items-center gap-1.5 px-2",children:(0,s.jsx)("span",{className:"truncate",children:"Prompt to configure"})}),(0,s.jsx)(A.Xi,{value:"manual",children:"Config"})]})})}),(0,s.jsx)(A.av,{value:"manual",className:"mt-0 flex-1 overflow-y-auto overflow-x-hidden px-4 md:px-12 pb-4 md:pb-12 scrollbar-hide",children:(0,s.jsxs)("div",{className:"max-w-full",children:[(0,s.jsx)("div",{className:"hidden md:flex justify-end mb-4 mt-4",children:el()}),(0,s.jsxs)("div",{className:"flex items-start md:items-center flex-col md:flex-row mt-6",children:[(0,s.jsx)(eN,{agentId:a,currentEmoji:ee.avatar,currentColor:ee.color,onStyleChange:Q,children:(0,s.jsx)("div",{className:"flex-shrink-0 h-12 w-12 md:h-16 md:w-16 flex items-center justify-center rounded-2xl text-xl md:text-2xl cursor-pointer hover:opacity-80 transition-opacity mb-3 md:mb-0",style:{backgroundColor:ee.color},children:ee.avatar})}),(0,s.jsxs)("div",{className:"flex flex-col md:ml-3 w-full min-w-0",children:[(0,s.jsx)(ep,{value:D.name,onSave:e=>K("name",e),className:"text-lg md:text-xl font-semibold bg-transparent",placeholder:"Click to add agent name..."}),(0,s.jsx)(ep,{value:D.description,onSave:e=>K("description",e),className:"text-muted-foreground text-sm md:text-base",placeholder:"Click to add description..."})]})]}),(0,s.jsxs)("div",{className:"flex flex-col mt-6 md:mt-8",children:[(0,s.jsx)("div",{className:"text-sm font-semibold text-muted-foreground mb-2",children:"Instructions"}),(0,s.jsx)(ep,{value:D.system_prompt,onSave:e=>K("system_prompt",e),className:"bg-transparent hover:bg-transparent border-none focus-visible:ring-0 shadow-none text-sm md:text-base",placeholder:"Click to set system instructions...",multiline:!0,minHeight:"150px"})]}),(0,s.jsx)("div",{ref:Z,className:"mt-6 border-t",children:(0,s.jsxs)(p.nD,{type:"multiple",defaultValue:[],className:"space-y-2",onValueChange:X,children:[(0,s.jsxs)(p.As,{value:"tools",className:"border-b",children:[(0,s.jsx)(p.$m,{className:"hover:no-underline text-sm md:text-base",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(c.A,{className:"h-4 w-4"}),"Default Tools"]})}),(0,s.jsx)(p.ub,{className:"pb-4 overflow-x-hidden",children:(0,s.jsx)(er.b,{tools:D.agentpress_tools,onToolsChange:e=>K("agentpress_tools",e)})})]}),(0,s.jsxs)(p.As,{value:"mcp",className:"border-b",children:[(0,s.jsx)(p.$m,{className:"hover:no-underline text-sm md:text-base",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(u.A,{className:"h-4 w-4"}),"Integrations & MCPs",(0,s.jsx)(b.E,{variant:"new",children:"New"})]})}),(0,s.jsx)(p.ub,{className:"pb-4 overflow-x-hidden",children:(0,s.jsx)(ea,{configuredMCPs:D.configured_mcps,customMCPs:D.custom_mcps,onMCPChange:H,agentId:a})})]}),(0,s.jsxs)(p.As,{value:"triggers",className:"border-b",children:[(0,s.jsx)(p.$m,{className:"hover:no-underline text-sm md:text-base",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(m.A,{className:"h-4 w-4"}),"Triggers",(0,s.jsx)(b.E,{variant:"new",children:"New"})]})}),(0,s.jsx)(p.ub,{className:"pb-4 overflow-x-hidden",children:(0,s.jsx)(eE.b,{agentId:a})})]}),(0,s.jsxs)(p.As,{value:"knowledge-base",className:"border-b",children:[(0,s.jsx)(p.$m,{className:"hover:no-underline text-sm md:text-base",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(g.A,{className:"h-4 w-4"}),"Knowledge Base",(0,s.jsx)(b.E,{variant:"new",children:"New"})]})}),(0,s.jsx)(p.ub,{className:"pb-4 overflow-x-hidden",children:(0,s.jsx)(eA.Y,{agentId:a,agentName:D.name||"Agent"})})]}),(0,s.jsxs)(p.As,{value:"workflows",className:"border-b",children:[(0,s.jsx)(p.$m,{className:"hover:no-underline text-sm md:text-base",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(x.A,{className:"h-4 w-4"}),"Workflows",(0,s.jsx)(b.E,{variant:"new",children:"New"})]})}),(0,s.jsx)(p.ub,{className:"pb-4 overflow-x-hidden",children:(0,s.jsx)(eT.E,{agentId:a,agentName:D.name||"Agent"})})]})]})})]})}),(0,s.jsx)(A.av,{value:"agent-builder",className:"mt-0 flex-1 flex flex-col overflow-hidden",children:et})]})]}),[$,a,j,D,ee,M,et,K,Q,R,W,q,X,el,H]);if((0,r.useEffect)(()=>()=>{U.current&&clearTimeout(U.current)},[]),w)return(0,s.jsx)("div",{className:"container mx-auto max-w-7xl px-4 py-8",children:(0,s.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(h.A,{className:"h-6 w-6 animate-spin"}),(0,s.jsx)("span",{children:"Loading agent..."})]})})});if(_||!j){let e=_ instanceof Error?_.message:String(_),t=e.includes("Access denied")||e.includes("403");return(0,s.jsx)("div",{className:"container mx-auto max-w-7xl px-4 py-8",children:(0,s.jsx)("div",{className:"text-center space-y-4",children:t?(0,s.jsx)(v.Fc,{variant:"destructive",children:(0,s.jsx)(v.TN,{children:"You don't have permission to edit this agent. You can only edit agents that you created."})}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Agent not found"}),(0,s.jsx)("p",{className:"text-muted-foreground mb-4",children:"The agent you're looking for doesn't exist."})]})})})}return(0,s.jsx)("div",{className:"h-screen flex flex-col",children:(0,s.jsxs)("div",{className:"flex-1 flex overflow-hidden",children:[(0,s.jsxs)("div",{className:"hidden md:flex w-full h-full",children:[(0,s.jsx)("div",{className:"w-1/2 border-r bg-background h-full flex flex-col",children:ei}),(0,s.jsx)("div",{className:"w-1/2 overflow-y-auto",children:(0,s.jsx)(ex,{agent:{...j,...D}})})]}),(0,s.jsx)("div",{className:"md:hidden w-full h-full flex flex-col",children:ei})]})})}},47262:(e,t,a)=>{"use strict";a.d(t,{S:()=>i});var s=a(95155);a(12115);var r=a(76981),n=a(5196),l=a(59434);function i(e){let{className:t,...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,s.jsx)(r.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(n.A,{className:"size-3.5"})})})}},52695:(e,t,a)=>{"use strict";a.d(t,{Bx:()=>_,Yv:()=>T,CG:()=>A,Cn:()=>I,jj:()=>P,Gh:()=>E,sF:()=>k,wZ:()=>R,Uj:()=>O,FX:()=>F,GB:()=>y,jM:()=>S,x2:()=>C,cL:()=>N});var s=a(95155),r=a(12115),n=a(99708),l=a(74466),i=a(22432),o=a(40416),d=a(59434),c=a(30285);a(62523),a(22346);var u=a(15452),m=a(54416);function g(e){let{...t}=e;return(0,s.jsx)(u.bL,{"data-slot":"sheet",...t})}function x(e){let{...t}=e;return(0,s.jsx)(u.ZL,{"data-slot":"sheet-portal",...t})}function h(e){let{className:t,...a}=e;return(0,s.jsx)(u.hJ,{"data-slot":"sheet-overlay",className:(0,d.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function f(e){let{className:t,children:a,side:r="right",...n}=e;return(0,s.jsxs)(x,{children:[(0,s.jsx)(h,{}),(0,s.jsxs)(u.UC,{"data-slot":"sheet-content",className:(0,d.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===r&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===r&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===r&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===r&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...n,children:[a,(0,s.jsxs)(u.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,s.jsx)(m.A,{className:"size-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function p(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sheet-header",className:(0,d.cn)("flex flex-col gap-1.5 p-4",t),...a})}function b(e){let{className:t,...a}=e;return(0,s.jsx)(u.hE,{"data-slot":"sheet-title",className:(0,d.cn)("text-foreground font-semibold",t),...a})}function v(e){let{className:t,...a}=e;return(0,s.jsx)(u.VY,{"data-slot":"sheet-description",className:(0,d.cn)("text-muted-foreground text-sm",t),...a})}a(68856);var j=a(46102);let w=r.createContext(null);function N(){let e=r.useContext(w);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}function y(e){let{defaultOpen:t=!0,open:a,onOpenChange:n,className:l,style:i,children:c,...u}=e,m=(0,o.a)(),[g,x]=r.useState(!1),[h,f]=r.useState(t),p=null!=a?a:h,b=r.useCallback(e=>{let t="function"==typeof e?e(p):e;n?n(t):f(t),document.cookie="".concat("sidebar_state","=").concat(t,"; path=/; max-age=").concat(604800)},[n,p]),v=r.useCallback(()=>m?x(e=>!e):b(e=>!e),[m,b,x]);r.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),v())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[v]);let N=p?"expanded":"collapsed",y=r.useMemo(()=>({state:N,open:p,setOpen:b,isMobile:m,openMobile:g,setOpenMobile:x,toggleSidebar:v}),[N,p,b,m,g,x,v]);return(0,s.jsx)(w.Provider,{value:y,children:(0,s.jsx)(j.Bc,{delayDuration:0,children:(0,s.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...i},className:(0,d.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",l),...u,children:c})})})}function _(e){let{side:t="left",variant:a="sidebar",collapsible:r="offcanvas",className:n,children:l,...i}=e,{isMobile:o,state:c,openMobile:u,setOpenMobile:m}=N();return"none"===r?(0,s.jsx)("div",{"data-slot":"sidebar",className:(0,d.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",n),...i,children:l}):o?(0,s.jsx)(g,{open:u,onOpenChange:m,...i,children:(0,s.jsxs)(f,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:t,children:[(0,s.jsxs)(p,{className:"sr-only",children:[(0,s.jsx)(b,{children:"Sidebar"}),(0,s.jsx)(v,{children:"Displays the mobile sidebar."})]}),(0,s.jsx)("div",{className:"flex h-full w-full flex-col",children:l})]})}):(0,s.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":c,"data-collapsible":"collapsed"===c?r:"","data-variant":a,"data-side":t,"data-slot":"sidebar",children:[(0,s.jsx)("div",{"data-slot":"sidebar-gap",className:(0,d.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===a||"inset"===a?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,s.jsx)("div",{"data-slot":"sidebar-container",className:(0,d.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===t?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===a||"inset"===a?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",n),...i,children:(0,s.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:l})})]})}function C(e){let{className:t,onClick:a,...r}=e,{toggleSidebar:n}=N();return(0,s.jsxs)(c.$,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:(0,d.cn)("size-7",t),onClick:e=>{null==a||a(e),n()},...r,children:[(0,s.jsx)(i.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function S(e){let{className:t,...a}=e,{toggleSidebar:r}=N();return(0,s.jsx)("button",{"data-sidebar":"rail","data-slot":"sidebar-rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:r,title:"Toggle Sidebar",className:(0,d.cn)("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex","in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",t),...a})}function k(e){let{className:t,...a}=e;return(0,s.jsx)("main",{"data-slot":"sidebar-inset",className:(0,d.cn)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",t),...a})}function E(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:(0,d.cn)("flex flex-col gap-2 p-2",t),...a})}function A(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:(0,d.cn)("flex flex-col gap-2 p-2",t),...a})}function T(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,d.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",t),...a})}function I(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,d.cn)("relative flex w-full min-w-0 flex-col p-2",t),...a})}function P(e){let{className:t,asChild:a=!1,...r}=e,l=a?n.DX:"div";return(0,s.jsx)(l,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,d.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",t),...r})}function R(e){let{className:t,...a}=e;return(0,s.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,d.cn)("flex w-full min-w-0 flex-col gap-1",t),...a})}function F(e){let{className:t,...a}=e;return(0,s.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,d.cn)("group/menu-item relative",t),...a})}let D=(0,l.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function O(e){let{asChild:t=!1,isActive:a=!1,variant:r="default",size:l="default",tooltip:i,className:o,...c}=e,u=t?n.DX:"button",{isMobile:m,state:g}=N(),x=(0,s.jsx)(u,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":l,"data-active":a,className:(0,d.cn)(D({variant:r,size:l}),o),...c});return i?("string"==typeof i&&(i={children:i}),(0,s.jsxs)(j.m_,{children:[(0,s.jsx)(j.k$,{asChild:!0,children:x}),(0,s.jsx)(j.ZI,{side:"right",align:"center",hidden:"collapsed"!==g||m,...i})]})):x}},88271:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s}),a(95155),a(12115);let s=e=>{let t=["\uD83E\uDD16","\uD83C\uDFAF","⚡","\uD83D\uDE80","\uD83D\uDD2E","\uD83C\uDFA8","\uD83D\uDCCA","\uD83D\uDD27","\uD83D\uDCA1","\uD83C\uDF1F"],a=["#06b6d4","#22c55e","#8b5cf6","#3b82f6","#ec4899","#eab308","#ef4444","#6366f1"],s=e.split("").reduce((e,t)=>e+t.charCodeAt(0),0)%t.length,r=e.split("").reduce((e,t)=>e+t.charCodeAt(0),0)%a.length;return{avatar:t[s],color:a[r]}}},95030:(e,t,a)=>{"use strict";a.d(t,{Ak:()=>g,Gx:()=>m,M4:()=>x});var s=a(28755),r=a(26715),n=a(5041),l=a(56671),i=a(52643);let o="".concat("http://localhost:8000/api","/secure-mcp");async function d(e){let t=(0,i.U)(),{data:{session:a}}=await t.auth.getSession();if(!a)throw Error("You must be logged in to view credential profiles");let s=encodeURIComponent(e),r=await fetch("".concat(o,"/credential-profiles/").concat(s),{headers:{Authorization:"Bearer ".concat(a.access_token)}});if(!r.ok)throw Error((await r.json().catch(()=>({message:"Unknown error"}))).message||"HTTP ".concat(r.status,": ").concat(r.statusText));return r.json()}async function c(e){let t=(0,i.U)(),{data:{session:a}}=await t.auth.getSession();if(!a)throw Error("You must be logged in to create credential profiles");let s=await fetch("".concat(o,"/credential-profiles"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a.access_token)},body:JSON.stringify(e)});if(!s.ok)throw Error((await s.json().catch(()=>({message:"Unknown error"}))).message||"HTTP ".concat(s.status,": ").concat(s.statusText));return s.json()}async function u(e){let t=(0,i.U)(),{data:{session:a}}=await t.auth.getSession();if(!a)throw Error("You must be logged in to set default profile");let s=await fetch("".concat(o,"/credential-profiles/").concat(e,"/set-default"),{method:"PUT",headers:{Authorization:"Bearer ".concat(a.access_token)}});if(!s.ok)throw Error((await s.json().catch(()=>({message:"Unknown error"}))).message||"HTTP ".concat(s.status,": ").concat(s.statusText))}function m(e){return(0,s.I)({queryKey:["credential-profiles",e],queryFn:()=>e?d(e):Promise.resolve([]),enabled:!!e,staleTime:3e5})}function g(){let e=(0,r.jE)();return(0,n.n)({mutationFn:c,onSuccess:t=>{e.invalidateQueries({queryKey:["credential-profiles"]}),e.invalidateQueries({queryKey:["credential-profiles",t.mcp_qualified_name]}),l.oR.success("Created credential profile: ".concat(t.profile_name))},onError:e=>{l.oR.error("Failed to create credential profile: ".concat(e.message))}})}function x(){let e=(0,r.jE)();return(0,n.n)({mutationFn:u,onSuccess:()=>{e.invalidateQueries({queryKey:["credential-profiles"]}),l.oR.success("Default profile updated successfully")},onError:e=>{l.oR.error("Failed to set default profile: ".concat(e.message))}})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2362,6711,2969,1935,6671,3860,1171,8341,7201,5061,6165,9001,7453,2103,9879,5653,6766,9855,2473,6915,2566,9613,539,4612,360,6686,937,3685,8222,7379,4962,6528,9713,2814,5039,8441,1684,7358],()=>t(6371)),_N_E=e.O()}]);