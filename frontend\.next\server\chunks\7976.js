"use strict";exports.id=7976,exports.ids=[7976],exports.modules={60:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("ArrowUpRight",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]])},1513:(e,t,r)=>{r.d(t,{A:()=>o,T:()=>s});var a=r(43210),i=r(60687);function n(e="https://app.cal.com/embed/embed.js"){var t;let r,a;return t=window,r=function(e,t){e.q.push(t)},a=t.document,t.Cal=t.Cal||function(){let i=t.Cal,n=arguments;if(i.loaded||(i.ns={},i.q=i.q||[],a.head.appendChild(a.createElement("script")).src=e,i.loaded=!0),"init"===n[0]){let e=function(){r(e,arguments)},t=n[1];e.q=e.q||[],"string"==typeof t?(i.ns[t]=i.ns[t]||e,r(i.ns[t],n),r(i,["initNamespace",t])):r(i,n);return}r(i,n)},window.Cal}n.toString();let o=function(e){let{calLink:t,calOrigin:r,namespace:o="",config:s,initConfig:c={},embedJsUrl:l,...d}=e;if(!t)throw Error("calLink is required");let u=(0,a.useRef)(!1),f=function(e){let[t,r]=(0,a.useState)();return(0,a.useEffect)(()=>{r(()=>n(e))},[]),t}(l),h=(0,a.useRef)(null);return(0,a.useEffect)(()=>{if(!f||u.current||!h.current)return;u.current=!0;let e=h.current;o?(f("init",o,{...c,origin:r}),f.ns[o]("inline",{elementOrSelector:e,calLink:t,config:s})):(f("init",{...c,origin:r}),f("inline",{elementOrSelector:e,calLink:t,config:s}))},[f,t,s,o,r,c]),f?(0,i.jsx)("div",{ref:h,...d}):null};function s(e){let{namespace:t="",embedJsUrl:r}="string"==typeof e?{embedJsUrl:e}:e??{};return new Promise(function e(a){let i=n(r);i("init",t);let o=t?i.ns[t]:i;if(!o)return void setTimeout(()=>{e(a)},50);a(o)})}},11096:(e,t,r)=>{r.d(t,{H4:()=>k,_V:()=>A,bL:()=>S});var a=r(43210),i=r(11273),n=r(13495),o=r(66156),s=r(14163),c=r(57379);function l(){return()=>{}}var d=r(60687),u="Avatar",[f,h]=(0,i.A)(u),[p,y]=f(u),m=a.forwardRef((e,t)=>{let{__scopeAvatar:r,...i}=e,[n,o]=a.useState("idle");return(0,d.jsx)(p,{scope:r,imageLoadingStatus:n,onImageLoadingStatusChange:o,children:(0,d.jsx)(s.sG.span,{...i,ref:t})})});m.displayName=u;var w="AvatarImage",g=a.forwardRef((e,t)=>{let{__scopeAvatar:r,src:i,onLoadingStatusChange:u=()=>{},...f}=e,h=y(w,r),p=function(e,{referrerPolicy:t,crossOrigin:r}){let i=(0,c.useSyncExternalStore)(l,()=>!0,()=>!1),n=a.useRef(null),s=i?(n.current||(n.current=new window.Image),n.current):null,[d,u]=a.useState(()=>b(s,e));return(0,o.N)(()=>{u(b(s,e))},[s,e]),(0,o.N)(()=>{let e=e=>()=>{u(e)};if(!s)return;let a=e("loaded"),i=e("error");return s.addEventListener("load",a),s.addEventListener("error",i),t&&(s.referrerPolicy=t),"string"==typeof r&&(s.crossOrigin=r),()=>{s.removeEventListener("load",a),s.removeEventListener("error",i)}},[s,r,t]),d}(i,f),m=(0,n.c)(e=>{u(e),h.onImageLoadingStatusChange(e)});return(0,o.N)(()=>{"idle"!==p&&m(p)},[p,m]),"loaded"===p?(0,d.jsx)(s.sG.img,{...f,ref:t,src:i}):null});g.displayName=w;var E="AvatarFallback",v=a.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:i,...n}=e,o=y(E,r),[c,l]=a.useState(void 0===i);return a.useEffect(()=>{if(void 0!==i){let e=window.setTimeout(()=>l(!0),i);return()=>window.clearTimeout(e)}},[i]),c&&"loaded"!==o.imageLoadingStatus?(0,d.jsx)(s.sG.span,{...n,ref:t}):null});function b(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}v.displayName=E;var S=m,A=g,k=v},17971:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},24413:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("Server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]])},26277:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("Link2Off",[["path",{d:"M9 17H7A5 5 0 0 1 7 7",key:"10o201"}],["path",{d:"M15 7h2a5 5 0 0 1 4 8",key:"1d3206"}],["line",{x1:"8",x2:"12",y1:"12",y2:"12",key:"rvw6j4"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},31541:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("AtSign",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M16 8v5a3 3 0 0 0 6 0v-1a10 10 0 1 0-4 8",key:"7n84p3"}]])},38252:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("AudioWaveform",[["path",{d:"M2 13a2 2 0 0 0 2-2V7a2 2 0 0 1 4 0v13a2 2 0 0 0 4 0V4a2 2 0 0 1 4 0v13a2 2 0 0 0 4 0v-4a2 2 0 0 1 2-2",key:"57tc96"}]])},40083:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},47342:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},51214:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("PanelLeft",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},51904:(e,t,r)=>{let a;r.d(t,{Jt:()=>tu});var i=r(4573),n=r(77598);let o=(e,t)=>(0,n.createHash)(e).update(t).digest(),s=new TextEncoder,c=new TextDecoder;function l(...e){let t=new Uint8Array(e.reduce((e,{length:t})=>e+t,0)),r=0;for(let a of e)t.set(a,r),r+=a.length;return t}function d(e,t,r){if(t<0||t>=0x100000000)throw RangeError(`value must be >= 0 and <= ${0x100000000-1}. Received ${t}`);e.set([t>>>24,t>>>16,t>>>8,255&t],r)}function u(e){let t=new Uint8Array(4);return d(t,e),t}function f(e){return l(u(e.length),e)}async function h(e,t,r){let a=Math.ceil((t>>3)/32),i=new Uint8Array(32*a);for(let t=0;t<a;t++){let a=new Uint8Array(4+e.length+r.length);a.set(u(t+1)),a.set(e,4),a.set(r,4+e.length),i.set(await o("sha256",a),32*t)}return i.slice(0,t>>3)}let p=e=>i.Buffer.from(e).toString("base64url"),y=e=>new Uint8Array(i.Buffer.from(function(e){let t=e;return t instanceof Uint8Array&&(t=c.decode(t)),t}(e),"base64url"));var m=r(57975);class w extends Error{static code="ERR_JOSE_GENERIC";code="ERR_JOSE_GENERIC";constructor(e,t){super(e,t),this.name=this.constructor.name,Error.captureStackTrace?.(this,this.constructor)}}class g extends w{static code="ERR_JWT_CLAIM_VALIDATION_FAILED";code="ERR_JWT_CLAIM_VALIDATION_FAILED";claim;reason;payload;constructor(e,t,r="unspecified",a="unspecified"){super(e,{cause:{claim:r,reason:a,payload:t}}),this.claim=r,this.reason=a,this.payload=t}}class E extends w{static code="ERR_JWT_EXPIRED";code="ERR_JWT_EXPIRED";claim;reason;payload;constructor(e,t,r="unspecified",a="unspecified"){super(e,{cause:{claim:r,reason:a,payload:t}}),this.claim=r,this.reason=a,this.payload=t}}class v extends w{static code="ERR_JOSE_ALG_NOT_ALLOWED";code="ERR_JOSE_ALG_NOT_ALLOWED"}class b extends w{static code="ERR_JOSE_NOT_SUPPORTED";code="ERR_JOSE_NOT_SUPPORTED"}class S extends w{static code="ERR_JWE_DECRYPTION_FAILED";code="ERR_JWE_DECRYPTION_FAILED";constructor(e="decryption operation failed",t){super(e,t)}}class A extends w{static code="ERR_JWE_INVALID";code="ERR_JWE_INVALID"}class k extends w{static code="ERR_JWS_INVALID";code="ERR_JWS_INVALID"}class _ extends w{static code="ERR_JWT_INVALID";code="ERR_JWT_INVALID"}class R extends w{[Symbol.asyncIterator];static code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";code="ERR_JWKS_MULTIPLE_MATCHING_KEYS";constructor(e="multiple matching keys found in the JSON Web Key Set",t){super(e,t)}}class C extends w{static code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";code="ERR_JWS_SIGNATURE_VERIFICATION_FAILED";constructor(e="signature verification failed",t){super(e,t)}}function P(e){switch(e){case"PS256":case"RS256":case"ES256":case"ES256K":return"sha256";case"PS384":case"RS384":case"ES384":return"sha384";case"PS512":case"RS512":case"ES512":return"sha512";case"Ed25519":case"EdDSA":return;default:throw new b(`alg ${e} is not supported either by JOSE or your javascript runtime`)}}let T=n.webcrypto,O=e=>m.types.isCryptoKey(e),H=e=>m.types.isKeyObject(e);function x(e,t,...r){if((r=r.filter(Boolean)).length>2){let t=r.pop();e+=`one of type ${r.join(", ")}, or ${t}.`}else 2===r.length?e+=`one of type ${r[0]} or ${r[1]}.`:e+=`of type ${r[0]}.`;return null==t?e+=` Received ${t}`:"function"==typeof t&&t.name?e+=` Received function ${t.name}`:"object"==typeof t&&null!=t&&t.constructor?.name&&(e+=` Received an instance of ${t.constructor.name}`),e}let I=(e,...t)=>x("Key must be ",e,...t);function W(e,t,...r){return x(`Key for the ${e} algorithm must be `,t,...r)}let J=e=>H(e)||O(e),K=["KeyObject"];function $(e){if("object"!=typeof e||null===e||"[object Object]"!==Object.prototype.toString.call(e))return!1;if(null===Object.getPrototypeOf(e))return!0;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function U(e){return $(e)&&"string"==typeof e.kty}(globalThis.CryptoKey||T?.CryptoKey)&&K.push("CryptoKey"),new WeakMap;let N=e=>{switch(e){case"prime256v1":return"P-256";case"secp384r1":return"P-384";case"secp521r1":return"P-521";case"secp256k1":return"secp256k1";default:throw new b("Unsupported key curve for this operation")}},j=(e,t)=>{let r;if(O(e))r=n.KeyObject.from(e);else if(H(e))r=e;else if(U(e))return e.crv;else throw TypeError(I(e,...K));if("secret"===r.type)throw TypeError('only "private" or "public" type keys can be used for this operation');switch(r.asymmetricKeyType){case"ed25519":case"ed448":return`Ed${r.asymmetricKeyType.slice(2)}`;case"x25519":case"x448":return`X${r.asymmetricKeyType.slice(1)}`;case"ec":{let e=r.asymmetricKeyDetails.namedCurve;if(t)return e;return N(e)}default:throw TypeError("Invalid asymmetric key type for this operation")}},D=(e,t)=>{let r;try{r=e instanceof n.KeyObject?e.asymmetricKeyDetails?.modulusLength:Buffer.from(e.n,"base64url").byteLength<<3}catch{}if("number"!=typeof r||r<2048)throw TypeError(`${t} requires key modulusLength to be 2048 bits or larger`)},M=new Map([["ES256","P-256"],["ES256K","secp256k1"],["ES384","P-384"],["ES512","P-521"]]);function L(e,t){let r,a,i,o;if(t instanceof n.KeyObject)r=t.asymmetricKeyType,a=t.asymmetricKeyDetails;else switch(i=!0,t.kty){case"RSA":r="rsa";break;case"EC":r="ec";break;case"OKP":if("Ed25519"===t.crv){r="ed25519";break}if("Ed448"===t.crv){r="ed448";break}throw TypeError("Invalid key for this operation, its crv must be Ed25519 or Ed448");default:throw TypeError("Invalid key for this operation, its kty must be RSA, OKP, or EC")}switch(e){case"Ed25519":if("ed25519"!==r)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be ed25519");break;case"EdDSA":if(!["ed25519","ed448"].includes(r))throw TypeError("Invalid key for this operation, its asymmetricKeyType must be ed25519 or ed448");break;case"RS256":case"RS384":case"RS512":if("rsa"!==r)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa");D(t,e);break;case"PS256":case"PS384":case"PS512":if("rsa-pss"===r){let{hashAlgorithm:t,mgf1HashAlgorithm:r,saltLength:i}=a,n=parseInt(e.slice(-3),10);if(void 0!==t&&(t!==`sha${n}`||r!==t))throw TypeError(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of "alg" ${e}`);if(void 0!==i&&i>n>>3)throw TypeError(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of "alg" ${e}`)}else if("rsa"!==r)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa or rsa-pss");D(t,e),o={padding:n.constants.RSA_PKCS1_PSS_PADDING,saltLength:n.constants.RSA_PSS_SALTLEN_DIGEST};break;case"ES256":case"ES256K":case"ES384":case"ES512":{if("ec"!==r)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be ec");let a=j(t),i=M.get(e);if(a!==i)throw TypeError(`Invalid key curve for the algorithm, its curve must be ${i}, got ${a}`);o={dsaEncoding:"ieee-p1363"};break}default:throw new b(`alg ${e} is not supported either by JOSE or your javascript runtime`)}return i?{format:"jwk",key:t,...o}:o?{...o,key:t}:t}function G(e,t="algorithm.name"){return TypeError(`CryptoKey does not support this operation, its ${t} must be ${e}`)}function V(e,t){return e.name===t}function F(e){return parseInt(e.name.slice(4),10)}function B(e,t){if(t.length&&!t.some(t=>e.usages.includes(t))){let e="CryptoKey does not support this operation, its usages must include ";if(t.length>2){let r=t.pop();e+=`one of ${t.join(", ")}, or ${r}.`}else 2===t.length?e+=`one of ${t[0]} or ${t[1]}.`:e+=`${t[0]}.`;throw TypeError(e)}}function q(e,t,...r){switch(t){case"A128GCM":case"A192GCM":case"A256GCM":{if(!V(e.algorithm,"AES-GCM"))throw G("AES-GCM");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw G(r,"algorithm.length");break}case"A128KW":case"A192KW":case"A256KW":{if(!V(e.algorithm,"AES-KW"))throw G("AES-KW");let r=parseInt(t.slice(1,4),10);if(e.algorithm.length!==r)throw G(r,"algorithm.length");break}case"ECDH":switch(e.algorithm.name){case"ECDH":case"X25519":case"X448":break;default:throw G("ECDH, X25519, or X448")}break;case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":if(!V(e.algorithm,"PBKDF2"))throw G("PBKDF2");break;case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":{if(!V(e.algorithm,"RSA-OAEP"))throw G("RSA-OAEP");let r=parseInt(t.slice(9),10)||1;if(F(e.algorithm.hash)!==r)throw G(`SHA-${r}`,"algorithm.hash");break}default:throw TypeError("CryptoKey does not support this operation")}B(e,r)}function z(e,t,r){if(t instanceof Uint8Array){if(!e.startsWith("HS"))throw TypeError(I(t,...K));return(0,n.createSecretKey)(t)}if(t instanceof n.KeyObject)return t;if(O(t))return!function(e,t,...r){switch(t){case"HS256":case"HS384":case"HS512":{if(!V(e.algorithm,"HMAC"))throw G("HMAC");let r=parseInt(t.slice(2),10);if(F(e.algorithm.hash)!==r)throw G(`SHA-${r}`,"algorithm.hash");break}case"RS256":case"RS384":case"RS512":{if(!V(e.algorithm,"RSASSA-PKCS1-v1_5"))throw G("RSASSA-PKCS1-v1_5");let r=parseInt(t.slice(2),10);if(F(e.algorithm.hash)!==r)throw G(`SHA-${r}`,"algorithm.hash");break}case"PS256":case"PS384":case"PS512":{if(!V(e.algorithm,"RSA-PSS"))throw G("RSA-PSS");let r=parseInt(t.slice(2),10);if(F(e.algorithm.hash)!==r)throw G(`SHA-${r}`,"algorithm.hash");break}case"EdDSA":if("Ed25519"!==e.algorithm.name&&"Ed448"!==e.algorithm.name)throw G("Ed25519 or Ed448");break;case"Ed25519":if(!V(e.algorithm,"Ed25519"))throw G("Ed25519");break;case"ES256":case"ES384":case"ES512":{if(!V(e.algorithm,"ECDSA"))throw G("ECDSA");let r=function(e){switch(e){case"ES256":return"P-256";case"ES384":return"P-384";case"ES512":return"P-521";default:throw Error("unreachable")}}(t);if(e.algorithm.namedCurve!==r)throw G(r,"algorithm.namedCurve");break}default:throw TypeError("CryptoKey does not support this operation")}B(e,r)}(t,e,r),n.KeyObject.from(t);if(U(t))return e.startsWith("HS")?(0,n.createSecretKey)(Buffer.from(t.k,"base64url")):t;throw TypeError(I(t,...K,"Uint8Array","JSON Web Key"))}let X=(0,m.promisify)(n.sign),Y=async(e,t,r)=>{let a=z(e,t,"sign");if(e.startsWith("HS")){let t=n.createHmac(function(e){switch(e){case"HS256":return"sha256";case"HS384":return"sha384";case"HS512":return"sha512";default:throw new b(`alg ${e} is not supported either by JOSE or your javascript runtime`)}}(e),a);return t.update(r),t.digest()}return X(P(e),r,L(e,a))},Z=(0,m.promisify)(n.verify),Q=async(e,t,r,a)=>{let i=z(e,t,"verify");if(e.startsWith("HS")){let t=await Y(e,i,a);try{return n.timingSafeEqual(r,t)}catch{return!1}}let o=P(e),s=L(e,i);try{return await Z(o,a,s,r)}catch{return!1}},ee=(...e)=>{let t,r=e.filter(Boolean);if(0===r.length||1===r.length)return!0;for(let e of r){let r=Object.keys(e);if(!t||0===t.size){t=new Set(r);continue}for(let e of r){if(t.has(e))return!1;t.add(e)}}return!0},et=e=>e?.[Symbol.toStringTag],er=(e,t,r)=>{if(void 0!==t.use&&"sig"!==t.use)throw TypeError("Invalid key for this operation, when present its use must be sig");if(void 0!==t.key_ops&&t.key_ops.includes?.(r)!==!0)throw TypeError(`Invalid key for this operation, when present its key_ops must include ${r}`);if(void 0!==t.alg&&t.alg!==e)throw TypeError(`Invalid key for this operation, when present its alg must be ${e}`);return!0},ea=(e,t,r,a)=>{if(!(t instanceof Uint8Array)){if(a&&U(t)){if(function(e){return U(e)&&"oct"===e.kty&&"string"==typeof e.k}(t)&&er(e,t,r))return;throw TypeError('JSON Web Key for symmetric algorithms must have JWK "kty" (Key Type) equal to "oct" and the JWK "k" (Key Value) present')}if(!J(t))throw TypeError(W(e,t,...K,"Uint8Array",a?"JSON Web Key":null));if("secret"!==t.type)throw TypeError(`${et(t)} instances for symmetric algorithms must be of type "secret"`)}},ei=(e,t,r,a)=>{if(a&&U(t))switch(r){case"sign":if(function(e){return"oct"!==e.kty&&"string"==typeof e.d}(t)&&er(e,t,r))return;throw TypeError("JSON Web Key for this operation be a private JWK");case"verify":if(function(e){return"oct"!==e.kty&&void 0===e.d}(t)&&er(e,t,r))return;throw TypeError("JSON Web Key for this operation be a public JWK")}if(!J(t))throw TypeError(W(e,t,...K,a?"JSON Web Key":null));if("secret"===t.type)throw TypeError(`${et(t)} instances for asymmetric algorithms must not be of type "secret"`);if("sign"===r&&"public"===t.type)throw TypeError(`${et(t)} instances for asymmetric algorithm signing must be of type "private"`);if("decrypt"===r&&"public"===t.type)throw TypeError(`${et(t)} instances for asymmetric algorithm decryption must be of type "private"`);if(t.algorithm&&"verify"===r&&"private"===t.type)throw TypeError(`${et(t)} instances for asymmetric algorithm verifying must be of type "public"`);if(t.algorithm&&"encrypt"===r&&"private"===t.type)throw TypeError(`${et(t)} instances for asymmetric algorithm encryption must be of type "public"`)};function en(e,t,r,a){t.startsWith("HS")||"dir"===t||t.startsWith("PBES2")||/^A\d{3}(?:GCM)?KW$/.test(t)?ea(t,r,a,e):ei(t,r,a,e)}let eo=en.bind(void 0,!1),es=en.bind(void 0,!0),ec=function(e,t,r,a,i){let n;if(void 0!==i.crit&&a?.crit===void 0)throw new e('"crit" (Critical) Header Parameter MUST be integrity protected');if(!a||void 0===a.crit)return new Set;if(!Array.isArray(a.crit)||0===a.crit.length||a.crit.some(e=>"string"!=typeof e||0===e.length))throw new e('"crit" (Critical) Header Parameter MUST be an array of non-empty strings when present');for(let o of(n=void 0!==r?new Map([...Object.entries(r),...t.entries()]):t,a.crit)){if(!n.has(o))throw new b(`Extension Header Parameter "${o}" is not recognized`);if(void 0===i[o])throw new e(`Extension Header Parameter "${o}" is missing`);if(n.get(o)&&void 0===a[o])throw new e(`Extension Header Parameter "${o}" MUST be integrity protected`)}return new Set(a.crit)},el=(e,t)=>{if(void 0!==t&&(!Array.isArray(t)||t.some(e=>"string"!=typeof e)))throw TypeError(`"${e}" option must be an array of strings`);if(t)return new Set(t)},ed=e=>e.d?(0,n.createPrivateKey)({format:"jwk",key:e}):(0,n.createPublicKey)({format:"jwk",key:e});async function eu(e,t){if(!$(e))throw TypeError("JWK must be an object");switch(t||=e.alg,e.kty){case"oct":if("string"!=typeof e.k||!e.k)throw TypeError('missing "k" (Key Value) Parameter value');return y(e.k);case"RSA":if("oth"in e&&void 0!==e.oth)throw new b('RSA JWK "oth" (Other Primes Info) Parameter value is not supported');case"EC":case"OKP":return ed({...e,alg:t});default:throw new b('Unsupported "kty" (Key Type) Parameter value')}}async function ef(e,t,r){let a,i;if(!$(e))throw new k("Flattened JWS must be an object");if(void 0===e.protected&&void 0===e.header)throw new k('Flattened JWS must have either of the "protected" or "header" members');if(void 0!==e.protected&&"string"!=typeof e.protected)throw new k("JWS Protected Header incorrect type");if(void 0===e.payload)throw new k("JWS Payload missing");if("string"!=typeof e.signature)throw new k("JWS Signature missing or incorrect type");if(void 0!==e.header&&!$(e.header))throw new k("JWS Unprotected Header incorrect type");let n={};if(e.protected)try{let t=y(e.protected);n=JSON.parse(c.decode(t))}catch{throw new k("JWS Protected Header is invalid")}if(!ee(n,e.header))throw new k("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let o={...n,...e.header},d=ec(k,new Map([["b64",!0]]),r?.crit,n,o),u=!0;if(d.has("b64")&&"boolean"!=typeof(u=n.b64))throw new k('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:f}=o;if("string"!=typeof f||!f)throw new k('JWS "alg" (Algorithm) Header Parameter missing or invalid');let h=r&&el("algorithms",r.algorithms);if(h&&!h.has(f))throw new v('"alg" (Algorithm) Header Parameter value not allowed');if(u){if("string"!=typeof e.payload)throw new k("JWS Payload must be a string")}else if("string"!=typeof e.payload&&!(e.payload instanceof Uint8Array))throw new k("JWS Payload must be a string or an Uint8Array instance");let p=!1;"function"==typeof t?(t=await t(n,e),p=!0,es(f,t,"verify"),U(t)&&(t=await eu(t,f))):es(f,t,"verify");let m=l(s.encode(e.protected??""),s.encode("."),"string"==typeof e.payload?s.encode(e.payload):e.payload);try{a=y(e.signature)}catch{throw new k("Failed to base64url decode the signature")}if(!await Q(f,t,a,m))throw new C;if(u)try{i=y(e.payload)}catch{throw new k("Failed to base64url decode the payload")}else i="string"==typeof e.payload?s.encode(e.payload):e.payload;let w={payload:i};return(void 0!==e.protected&&(w.protectedHeader=n),void 0!==e.header&&(w.unprotectedHeader=e.header),p)?{...w,key:t}:w}async function eh(e,t,r){if(e instanceof Uint8Array&&(e=c.decode(e)),"string"!=typeof e)throw new k("Compact JWS must be a string or Uint8Array");let{0:a,1:i,2:n,length:o}=e.split(".");if(3!==o)throw new k("Invalid Compact JWS");let s=await ef({payload:i,protected:a,signature:n},t,r),l={payload:s.payload,protectedHeader:s.protectedHeader};return"function"==typeof t?{...l,key:s.key}:l}class ep{_payload;_protectedHeader;_unprotectedHeader;constructor(e){if(!(e instanceof Uint8Array))throw TypeError("payload must be an instance of Uint8Array");this._payload=e}setProtectedHeader(e){if(this._protectedHeader)throw TypeError("setProtectedHeader can only be called once");return this._protectedHeader=e,this}setUnprotectedHeader(e){if(this._unprotectedHeader)throw TypeError("setUnprotectedHeader can only be called once");return this._unprotectedHeader=e,this}async sign(e,t){let r;if(!this._protectedHeader&&!this._unprotectedHeader)throw new k("either setProtectedHeader or setUnprotectedHeader must be called before #sign()");if(!ee(this._protectedHeader,this._unprotectedHeader))throw new k("JWS Protected and JWS Unprotected Header Parameter names must be disjoint");let a={...this._protectedHeader,...this._unprotectedHeader},i=ec(k,new Map([["b64",!0]]),t?.crit,this._protectedHeader,a),n=!0;if(i.has("b64")&&"boolean"!=typeof(n=this._protectedHeader.b64))throw new k('The "b64" (base64url-encode payload) Header Parameter must be a boolean');let{alg:o}=a;if("string"!=typeof o||!o)throw new k('JWS "alg" (Algorithm) Header Parameter missing or invalid');es(o,e,"sign");let d=this._payload;n&&(d=s.encode(p(d)));let u=l(r=this._protectedHeader?s.encode(p(JSON.stringify(this._protectedHeader))):s.encode(""),s.encode("."),d),f={signature:p(await Y(o,e,u)),payload:""};return n&&(f.payload=c.decode(d)),this._unprotectedHeader&&(f.header=this._unprotectedHeader),this._protectedHeader&&(f.protected=c.decode(r)),f}}class ey{_flattened;constructor(e){this._flattened=new ep(e)}setProtectedHeader(e){return this._flattened.setProtectedHeader(e),this}async sign(e,t){let r=await this._flattened.sign(e,t);if(void 0===r.payload)throw TypeError("use the flattened module for creating JWS with b64: false");return`${r.protected}.${r.payload}.${r.signature}`}}function em(e,t,{cachePromiseRejection:r=!1}={}){let a,i,n=!1;return function(...o){return n&&t(o,a)||(i=e.apply(this,o),!r&&i.catch&&i.catch(()=>n=!1),n=!0,a=o),i}}var ew=em((e,t)=>eh(e,y(t),{algorithms:["HS256"]}),(e,t)=>e[0]===t[0]&&e[1]===t[1],{cachePromiseRejection:!0});async function eg(e,t,r){var a,i;let{payload:n}=await ew(e,r),[o,s]=n.length===t.length?[n]:(a=n,i=t.length,[a.slice(0,i),a.slice(i)]),c=s?JSON.parse(`[${new TextDecoder().decode(s)}]`):null,l=0;return o.reduce((e,r,a)=>{let i=t[a];if(!i)throw Error(`flags: No flag at index ${a}`);switch(r){case 253:e[i.key]=!1;break;case 254:e[i.key]=!0;break;case 255:e[i.key]=c[l++];break;case 252:e[i.key]=null;break;default:e[i.key]=i.options?.[r]?.value}return e},{})}em((e,t)=>new ey(e).setProtectedHeader({alg:"HS256"}).sign(y(t)),(e,t)=>e[0].length===t[0].length&&e[0].every((e,r)=>t[0][r]===e)&&e[1]===t[1],{cachePromiseRejection:!0});var eE=r(84297);let ev=(e,t)=>{if(t.length<<3!==function(e){switch(e){case"A128GCM":case"A128GCMKW":case"A192GCM":case"A192GCMKW":case"A256GCM":case"A256GCMKW":return 96;case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return 128;default:throw new b(`Unsupported JWE Algorithm: ${e}`)}}(e))throw new A("Invalid Initialization Vector length")},eb=(e,t)=>{let r;switch(e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":r=parseInt(e.slice(-3),10);break;case"A128GCM":case"A192GCM":case"A256GCM":r=parseInt(e.slice(1,4),10);break;default:throw new b(`Content Encryption Algorithm ${e} is not supported either by JOSE or your javascript runtime`)}if(t instanceof Uint8Array){let e=t.byteLength<<3;if(e!==r)throw new A(`Invalid Content Encryption Key length. Expected ${r} bits, got ${e} bits`);return}if(H(t)&&"secret"===t.type){let e=t.symmetricKeySize<<3;if(e!==r)throw new A(`Invalid Content Encryption Key length. Expected ${r} bits, got ${e} bits`);return}throw TypeError("Invalid Content Encryption Key type")},eS=n.timingSafeEqual,eA=e=>(a||=new Set((0,n.getCiphers)())).has(e),ek=(e,t,r,a,i,o)=>{let s;if(O(t))q(t,e,"decrypt"),s=n.KeyObject.from(t);else if(t instanceof Uint8Array||H(t))s=t;else throw TypeError(I(t,...K,"Uint8Array"));if(!a)throw new A("JWE Initialization Vector missing");if(!i)throw new A("JWE Authentication Tag missing");switch(eb(e,s),ev(e,a),e){case"A128CBC-HS256":case"A192CBC-HS384":case"A256CBC-HS512":return function(e,t,r,a,i,o){let s,c,u=parseInt(e.slice(1,4),10);H(t)&&(t=t.export());let f=t.subarray(u>>3),h=t.subarray(0,u>>3),p=parseInt(e.slice(-3),10),y=`aes-${u}-cbc`;if(!eA(y))throw new b(`alg ${e} is not supported by your javascript runtime`);let m=function(e,t,r,a,i,o){let s=l(e,t,r,function(e){let t=Math.floor(e/0x100000000),r=new Uint8Array(8);return d(r,t,0),d(r,e%0x100000000,4),r}(e.length<<3)),c=(0,n.createHmac)(`sha${a}`,i);return c.update(s),c.digest().slice(0,o>>3)}(o,a,r,p,h,u);try{s=eS(i,m)}catch{}if(!s)throw new S;try{let e=(0,n.createDecipheriv)(y,f,a);c=l(e.update(r),e.final())}catch{}if(!c)throw new S;return c}(e,s,r,a,i,o);case"A128GCM":case"A192GCM":case"A256GCM":return function(e,t,r,a,i,o){let s=parseInt(e.slice(1,4),10),c=`aes-${s}-gcm`;if(!eA(c))throw new b(`alg ${e} is not supported by your javascript runtime`);try{let e=(0,n.createDecipheriv)(c,t,a,{authTagLength:16});e.setAuthTag(i),o.byteLength&&e.setAAD(o,{plaintextLength:r.length});let s=e.update(r);return e.final(),s}catch{throw new S}}(e,s,r,a,i,o);default:throw new b("Unsupported JWE Content Encryption Algorithm")}},e_=(e,t,r)=>{let a=parseInt(e.slice(1,4),10),o=`aes${a}-wrap`;if(!eA(o))throw new b(`alg ${e} is not supported either by JOSE or your javascript runtime`);let s=function(e,t,r){if(H(e))return e;if(e instanceof Uint8Array)return(0,n.createSecretKey)(e);if(O(e))return q(e,t,r),n.KeyObject.from(e);throw TypeError(I(e,...K,"Uint8Array"))}(t,e,"unwrapKey");!function(e,t){if(e.symmetricKeySize<<3!==parseInt(t.slice(1,4),10))throw TypeError(`Invalid key size for alg: ${t}`)}(s,e);let c=(0,n.createDecipheriv)(o,s,i.Buffer.alloc(8,166));return l(c.update(r),c.final())};async function eR(e,t,r,a,i=new Uint8Array(0),o=new Uint8Array(0)){let c,d;if(O(e))q(e,"ECDH"),c=n.KeyObject.from(e);else if(H(e))c=e;else throw TypeError(I(e,...K));if(O(t))q(t,"ECDH","deriveBits"),d=n.KeyObject.from(t);else if(H(t))d=t;else throw TypeError(I(t,...K));let p=l(f(s.encode(r)),f(i),f(o),u(a));return h((0,n.diffieHellman)({privateKey:d,publicKey:c}),a,p)}(0,m.promisify)(n.generateKeyPair);let eC=e=>["P-256","P-384","P-521","X25519","X448"].includes(j(e)),eP=(0,m.promisify)(n.pbkdf2),eT=async(e,t,r,a,i)=>{!function(e){if(!(e instanceof Uint8Array)||e.length<8)throw new A("PBES2 Salt Input must be 8 or more octets")}(i);let o=l(s.encode(e),new Uint8Array([0]),i),c=parseInt(e.slice(13,16),10)>>3,d=function(e,t){if(H(e))return e.export();if(e instanceof Uint8Array)return e;if(O(e))return q(e,t,"deriveBits","deriveKey"),n.KeyObject.from(e).export();throw TypeError(I(e,...K,"Uint8Array"))}(t,e),u=await eP(d,o,a,c,`sha${e.slice(8,11)}`);return e_(e.slice(-6),u,r)},eO=(e,t)=>{if("rsa"!==e.asymmetricKeyType)throw TypeError("Invalid key for this operation, its asymmetricKeyType must be rsa");D(e,t)},eH=(0,m.deprecate)(()=>n.constants.RSA_PKCS1_PADDING,'The RSA1_5 "alg" (JWE Algorithm) is deprecated and will be removed in the next major revision.'),ex=e=>{switch(e){case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":return n.constants.RSA_PKCS1_OAEP_PADDING;case"RSA1_5":return eH();default:return}},eI=e=>{switch(e){case"RSA-OAEP":return"sha1";case"RSA-OAEP-256":return"sha256";case"RSA-OAEP-384":return"sha384";case"RSA-OAEP-512":return"sha512";default:return}},eW=(e,t,r)=>{let a=ex(e),i=eI(e),o=function(e,t,...r){if(H(e))return e;if(O(e))return q(e,t,...r),n.KeyObject.from(e);throw TypeError(I(e,...K))}(t,e,"unwrapKey","decrypt");return eO(o,e),(0,n.privateDecrypt)({key:o,oaepHash:i,padding:a},r)},eJ={};function eK(e){switch(e){case"A128GCM":return 128;case"A192GCM":return 192;case"A256GCM":case"A128CBC-HS256":return 256;case"A192CBC-HS384":return 384;case"A256CBC-HS512":return 512;default:throw new b(`Unsupported JWE Algorithm: ${e}`)}}let e$=e=>(0,n.randomFillSync)(new Uint8Array(eK(e)>>3));async function eU(e,t,r,a,i){return ek(e.slice(0,7),t,r,a,i,new Uint8Array(0))}async function eN(e,t,r,a,i){switch(eo(e,t,"decrypt"),t=await eJ.normalizePrivateKey?.(t,e)||t,e){case"dir":if(void 0!==r)throw new A("Encountered unexpected JWE Encrypted Key");return t;case"ECDH-ES":if(void 0!==r)throw new A("Encountered unexpected JWE Encrypted Key");case"ECDH-ES+A128KW":case"ECDH-ES+A192KW":case"ECDH-ES+A256KW":{let i,n;if(!$(a.epk))throw new A('JOSE Header "epk" (Ephemeral Public Key) missing or invalid');if(!eC(t))throw new b("ECDH with the provided key is not allowed or not supported by your javascript runtime");let o=await eu(a.epk,e);if(void 0!==a.apu){if("string"!=typeof a.apu)throw new A('JOSE Header "apu" (Agreement PartyUInfo) invalid');try{i=y(a.apu)}catch{throw new A("Failed to base64url decode the apu")}}if(void 0!==a.apv){if("string"!=typeof a.apv)throw new A('JOSE Header "apv" (Agreement PartyVInfo) invalid');try{n=y(a.apv)}catch{throw new A("Failed to base64url decode the apv")}}let s=await eR(o,t,"ECDH-ES"===e?a.enc:e,"ECDH-ES"===e?eK(a.enc):parseInt(e.slice(-5,-2),10),i,n);if("ECDH-ES"===e)return s;if(void 0===r)throw new A("JWE Encrypted Key missing");return e_(e.slice(-6),s,r)}case"RSA1_5":case"RSA-OAEP":case"RSA-OAEP-256":case"RSA-OAEP-384":case"RSA-OAEP-512":if(void 0===r)throw new A("JWE Encrypted Key missing");return eW(e,t,r);case"PBES2-HS256+A128KW":case"PBES2-HS384+A192KW":case"PBES2-HS512+A256KW":{let n;if(void 0===r)throw new A("JWE Encrypted Key missing");if("number"!=typeof a.p2c)throw new A('JOSE Header "p2c" (PBES2 Count) missing or invalid');let o=i?.maxPBES2Count||1e4;if(a.p2c>o)throw new A('JOSE Header "p2c" (PBES2 Count) out is of acceptable bounds');if("string"!=typeof a.p2s)throw new A('JOSE Header "p2s" (PBES2 Salt) missing or invalid');try{n=y(a.p2s)}catch{throw new A("Failed to base64url decode the p2s")}return eT(e,t,r,a.p2c,n)}case"A128KW":case"A192KW":case"A256KW":if(void 0===r)throw new A("JWE Encrypted Key missing");return e_(e,t,r);case"A128GCMKW":case"A192GCMKW":case"A256GCMKW":{let i,n;if(void 0===r)throw new A("JWE Encrypted Key missing");if("string"!=typeof a.iv)throw new A('JOSE Header "iv" (Initialization Vector) missing or invalid');if("string"!=typeof a.tag)throw new A('JOSE Header "tag" (Authentication Tag) missing or invalid');try{i=y(a.iv)}catch{throw new A("Failed to base64url decode the iv")}try{n=y(a.tag)}catch{throw new A("Failed to base64url decode the tag")}return eU(e,t,r,i,n)}default:throw new b('Invalid or unsupported "alg" (JWE Algorithm) header value')}}async function ej(e,t,r){let a,i,n,o,d,u,f;if(!$(e))throw new A("Flattened JWE must be an object");if(void 0===e.protected&&void 0===e.header&&void 0===e.unprotected)throw new A("JOSE Header missing");if(void 0!==e.iv&&"string"!=typeof e.iv)throw new A("JWE Initialization Vector incorrect type");if("string"!=typeof e.ciphertext)throw new A("JWE Ciphertext missing or incorrect type");if(void 0!==e.tag&&"string"!=typeof e.tag)throw new A("JWE Authentication Tag incorrect type");if(void 0!==e.protected&&"string"!=typeof e.protected)throw new A("JWE Protected Header incorrect type");if(void 0!==e.encrypted_key&&"string"!=typeof e.encrypted_key)throw new A("JWE Encrypted Key incorrect type");if(void 0!==e.aad&&"string"!=typeof e.aad)throw new A("JWE AAD incorrect type");if(void 0!==e.header&&!$(e.header))throw new A("JWE Shared Unprotected Header incorrect type");if(void 0!==e.unprotected&&!$(e.unprotected))throw new A("JWE Per-Recipient Unprotected Header incorrect type");if(e.protected)try{let t=y(e.protected);a=JSON.parse(c.decode(t))}catch{throw new A("JWE Protected Header is invalid")}if(!ee(a,e.header,e.unprotected))throw new A("JWE Protected, JWE Unprotected Header, and JWE Per-Recipient Unprotected Header Parameter names must be disjoint");let h={...a,...e.header,...e.unprotected};if(ec(A,new Map,r?.crit,a,h),void 0!==h.zip)throw new b('JWE "zip" (Compression Algorithm) Header Parameter is not supported.');let{alg:p,enc:m}=h;if("string"!=typeof p||!p)throw new A("missing JWE Algorithm (alg) in JWE Header");if("string"!=typeof m||!m)throw new A("missing JWE Encryption Algorithm (enc) in JWE Header");let w=r&&el("keyManagementAlgorithms",r.keyManagementAlgorithms),g=r&&el("contentEncryptionAlgorithms",r.contentEncryptionAlgorithms);if(w&&!w.has(p)||!w&&p.startsWith("PBES2"))throw new v('"alg" (Algorithm) Header Parameter value not allowed');if(g&&!g.has(m))throw new v('"enc" (Encryption Algorithm) Header Parameter value not allowed');if(void 0!==e.encrypted_key)try{i=y(e.encrypted_key)}catch{throw new A("Failed to base64url decode the encrypted_key")}let E=!1;"function"==typeof t&&(t=await t(a,e),E=!0);try{n=await eN(p,t,i,h,r)}catch(e){if(e instanceof TypeError||e instanceof A||e instanceof b)throw e;n=e$(m)}if(void 0!==e.iv)try{o=y(e.iv)}catch{throw new A("Failed to base64url decode the iv")}if(void 0!==e.tag)try{d=y(e.tag)}catch{throw new A("Failed to base64url decode the tag")}let S=s.encode(e.protected??"");u=void 0!==e.aad?l(S,s.encode("."),s.encode(e.aad)):S;try{f=y(e.ciphertext)}catch{throw new A("Failed to base64url decode the ciphertext")}let k={plaintext:await ek(m,n,f,o,d,u)};if(void 0!==e.protected&&(k.protectedHeader=a),void 0!==e.aad)try{k.additionalAuthenticatedData=y(e.aad)}catch{throw new A("Failed to base64url decode the aad")}return(void 0!==e.unprotected&&(k.sharedUnprotectedHeader=e.unprotected),void 0!==e.header&&(k.unprotectedHeader=e.header),E)?{...k,key:t}:k}async function eD(e,t,r){if(e instanceof Uint8Array&&(e=c.decode(e)),"string"!=typeof e)throw new A("Compact JWE must be a string or Uint8Array");let{0:a,1:i,2:n,3:o,4:s,length:l}=e.split(".");if(5!==l)throw new A("Invalid Compact JWE");let d=await ej({ciphertext:o,iv:n||void 0,protected:a,tag:s||void 0,encrypted_key:i||void 0},t,r),u={plaintext:d.plaintext,protectedHeader:d.protectedHeader};return"function"==typeof t?{...u,key:d.key}:u}let eM=e=>Math.floor(e.getTime()/1e3),eL=/^(\+|\-)? ?(\d+|\d+\.\d+) ?(seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)(?: (ago|from now))?$/i,eG=e=>{let t,r=eL.exec(e);if(!r||r[4]&&r[1])throw TypeError("Invalid time period format");let a=parseFloat(r[2]);switch(r[3].toLowerCase()){case"sec":case"secs":case"second":case"seconds":case"s":t=Math.round(a);break;case"minute":case"minutes":case"min":case"mins":case"m":t=Math.round(60*a);break;case"hour":case"hours":case"hr":case"hrs":case"h":t=Math.round(3600*a);break;case"day":case"days":case"d":t=Math.round(86400*a);break;case"week":case"weeks":case"w":t=Math.round(604800*a);break;default:t=Math.round(0x1e187e0*a)}return"-"===r[1]||"ago"===r[4]?-t:t},eV=e=>e.toLowerCase().replace(/^application\//,""),eF=(e,t)=>"string"==typeof e?t.includes(e):!!Array.isArray(e)&&t.some(Set.prototype.has.bind(new Set(e))),eB=(e,t,r={})=>{let a,i;try{a=JSON.parse(c.decode(t))}catch{}if(!$(a))throw new _("JWT Claims Set must be a top-level JSON object");let{typ:n}=r;if(n&&("string"!=typeof e.typ||eV(e.typ)!==eV(n)))throw new g('unexpected "typ" JWT header value',a,"typ","check_failed");let{requiredClaims:o=[],issuer:s,subject:l,audience:d,maxTokenAge:u}=r,f=[...o];for(let e of(void 0!==u&&f.push("iat"),void 0!==d&&f.push("aud"),void 0!==l&&f.push("sub"),void 0!==s&&f.push("iss"),new Set(f.reverse())))if(!(e in a))throw new g(`missing required "${e}" claim`,a,e,"missing");if(s&&!(Array.isArray(s)?s:[s]).includes(a.iss))throw new g('unexpected "iss" claim value',a,"iss","check_failed");if(l&&a.sub!==l)throw new g('unexpected "sub" claim value',a,"sub","check_failed");if(d&&!eF(a.aud,"string"==typeof d?[d]:d))throw new g('unexpected "aud" claim value',a,"aud","check_failed");switch(typeof r.clockTolerance){case"string":i=eG(r.clockTolerance);break;case"number":i=r.clockTolerance;break;case"undefined":i=0;break;default:throw TypeError("Invalid clockTolerance option type")}let{currentDate:h}=r,p=eM(h||new Date);if((void 0!==a.iat||u)&&"number"!=typeof a.iat)throw new g('"iat" claim must be a number',a,"iat","invalid");if(void 0!==a.nbf){if("number"!=typeof a.nbf)throw new g('"nbf" claim must be a number',a,"nbf","invalid");if(a.nbf>p+i)throw new g('"nbf" claim timestamp check failed',a,"nbf","check_failed")}if(void 0!==a.exp){if("number"!=typeof a.exp)throw new g('"exp" claim must be a number',a,"exp","invalid");if(a.exp<=p-i)throw new E('"exp" claim timestamp check failed',a,"exp","check_failed")}if(u){let e=p-a.iat;if(e-i>("number"==typeof u?u:eG(u)))throw new E('"iat" claim timestamp check failed (too far in the past)',a,"iat","check_failed");if(e<0-i)throw new g('"iat" claim timestamp check failed (it should be in the past)',a,"iat","check_failed")}return a};async function eq(e,t,r){let a=await eD(e,t,r),i=eB(a.protectedHeader,a.plaintext,r),{protectedHeader:n}=a;if(void 0!==n.iss&&n.iss!==i.iss)throw new g('replicated "iss" claim header parameter mismatch',i,"iss","mismatch");if(void 0!==n.sub&&n.sub!==i.sub)throw new g('replicated "sub" claim header parameter mismatch',i,"sub","mismatch");if(void 0!==n.aud&&JSON.stringify(n.aud)!==JSON.stringify(i.aud))throw new g('replicated "aud" claim header parameter mismatch',i,"aud","mismatch");let o={payload:i,protectedHeader:n};return"function"==typeof t?{...o,key:a.key}:o}var ez="3.2.0",eX=Symbol.for("flags:global-trace"),eY=new eE.AsyncLocalStorage;function eZ(e,t){eY.getStore()?.set(e,t)}function eQ(e,t={name:e.name}){return function(...r){let a=function(){let e=Reflect.get(globalThis,eX);return e?.getTracer("flags",ez)}();return a&&("true"===process.env.VERCEL_FLAGS_TRACE_VERBOSE||!1===t.isVerboseTrace)?eY.run(new Map,()=>a.startActiveSpan(t.name,a=>{t.attributes&&a.setAttributes(t.attributes);try{let i=e.apply(this,r);return null!==i&&"object"==typeof i&&"then"in i&&"function"==typeof i.then?i.then(e=>{t.attributesSuccess&&a.setAttributes(t.attributesSuccess(e)),eY.getStore()?.forEach((e,t)=>{a.setAttribute(t,e)}),a.setStatus({code:1}),a.end()}).catch(e=>{t.attributesError&&a.setAttributes(t.attributesError(e)),a.setStatus({code:2,message:e instanceof Error?e.message:void 0}),eY.getStore()?.forEach((e,t)=>{a.setAttribute(t,e)}),a.end()}):(t.attributesSuccess&&a.setAttributes(t.attributesSuccess(i)),eY.getStore()?.forEach((e,t)=>{a.setAttribute(t,e)}),a.setStatus({code:1}),a.end()),i}catch(e){throw t.attributesError&&a.setAttributes(t.attributesError(e)),a.setStatus({code:2,message:e instanceof Error?e.message:void 0}),eY.getStore()?.forEach((e,t)=>{a.setAttribute(t,e)}),a.end(),e}})):e.apply(this,r)}}async function e0(e,t,r){if("string"==typeof e)try{let{payload:a}=await eq(e,y(r));if(!t||t(a))return delete a.iat,delete a.exp,a}catch{}}async function e1(e,t=process?.env?.FLAGS_SECRET){if(!t)throw Error("Missing FLAGS_SECRET");let r=await e0(e,null,t);return r?.c}eQ(async function(e,t=process?.env?.FLAGS_SECRET){if(!e)return!1;if(!t)throw Error("flags: verifyAccess was called without a secret. Please set FLAGS_SECRET environment variable.");return void 0!==await e1(e?.replace(/^Bearer /i,""),t)},{isVerboseTrace:!1,name:"verifyAccess"});var e2=class{static get(e,t,r){let a=Reflect.get(e,t,r);return"function"==typeof a?a.bind(e):a}static set(e,t,r,a){return Reflect.set(e,t,r,a)}static has(e,t){return Reflect.has(e,t)}static deleteProperty(e,t){return Reflect.deleteProperty(e,t)}},e5=class e extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new e}},e4=class e extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,a){if("symbol"==typeof r)return e2.get(t,r,a);let i=r.toLowerCase(),n=Object.keys(e).find(e=>e.toLowerCase()===i);if(void 0!==n)return e2.get(t,n,a)},set(t,r,a,i){if("symbol"==typeof r)return e2.set(t,r,a,i);let n=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===n);return e2.set(t,o??r,a,i)},has(t,r){if("symbol"==typeof r)return e2.has(t,r);let a=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===a);return void 0!==i&&e2.has(t,i)},deleteProperty(t,r){if("symbol"==typeof r)return e2.deleteProperty(t,r);let a=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===a);return void 0===i||e2.deleteProperty(t,i)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return e5.callable;default:return e2.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(t){return t instanceof Headers?t:new e(t)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,a]of this.entries())e.call(t,a,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}},e6=class e extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new e}},e8=class{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return e6.callable;default:return e2.get(e,t,r)}}})}};function e3(e){var t;let r=["path"in e&&e.path&&`Path=${e.path}`,"expires"in e&&(e.expires||0===e.expires)&&`Expires=${("number"==typeof e.expires?new Date(e.expires):e.expires).toUTCString()}`,"maxAge"in e&&"number"==typeof e.maxAge&&`Max-Age=${e.maxAge}`,"domain"in e&&e.domain&&`Domain=${e.domain}`,"secure"in e&&e.secure&&"Secure","httpOnly"in e&&e.httpOnly&&"HttpOnly","sameSite"in e&&e.sameSite&&`SameSite=${e.sameSite}`,"partitioned"in e&&e.partitioned&&"Partitioned","priority"in e&&e.priority&&`Priority=${e.priority}`].filter(Boolean),a=`${e.name}=${encodeURIComponent(null!=(t=e.value)?t:"")}`;return 0===r.length?a:`${a}; ${r.join("; ")}`}Symbol.for("next.mutated.cookies");var e9=class{constructor(e){this._parsed=new Map,this._headers=e;let t=e.get("cookie");if(t)for(let[e,r]of function(e){let t=new Map;for(let r of e.split(/; */)){if(!r)continue;let e=r.indexOf("=");if(-1===e){t.set(r,"true");continue}let[a,i]=[r.slice(0,e),r.slice(e+1)];try{t.set(a,decodeURIComponent(null!=i?i:"true"))}catch{}}return t}(t))this._parsed.set(e,{name:e,value:r})}[Symbol.iterator](){return this._parsed[Symbol.iterator]()}get size(){return this._parsed.size}get(...e){let t="string"==typeof e[0]?e[0]:e[0].name;return this._parsed.get(t)}getAll(...e){var t;let r=Array.from(this._parsed);if(!e.length)return r.map(([e,t])=>t);let a="string"==typeof e[0]?e[0]:null==(t=e[0])?void 0:t.name;return r.filter(([e])=>e===a).map(([e,t])=>t)}has(e){return this._parsed.has(e)}set(...e){let[t,r]=1===e.length?[e[0].name,e[0].value]:e,a=this._parsed;return a.set(t,{name:t,value:r}),this._headers.set("cookie",Array.from(a).map(([e,t])=>e3(t)).join("; ")),this}delete(e){let t=this._parsed,r=Array.isArray(e)?e.map(e=>t.delete(e)):t.delete(e);return this._headers.set("cookie",Array.from(t).map(([e,t])=>e3(t)).join("; ")),r}clear(){return this.delete(Array.from(this._parsed.keys())),this}[Symbol.for("edge-runtime.inspect.custom")](){return`RequestCookies ${JSON.stringify(Object.fromEntries(this._parsed))}`}toString(){return[...this._parsed.values()].map(e=>`${e.name}=${encodeURIComponent(e.value)}`).join("; ")}};Symbol.for("edge-runtime.inspect.custom");var e7=em(e=>e1(e),(e,t)=>e[0]===t[0],{cachePromiseRejection:!0});async function te(e){return"string"==typeof e&&""!==e?await e7(e)??null:null}async function tt(e,t,r=process.env.FLAGS_SECRET){if(!r)throw Error("flags: Can not serialize due to missing secret");return eg(t,e,r)}async function tr(e,t,r,a=process.env.FLAGS_SECRET){if(!a)throw Error("flags: getPrecomputed was called without a secret. Please set FLAGS_SECRET environment variable.");let i=await tt(t,r,a);return Array.isArray(e)?e.map(e=>i[e.key]):i[e.key]}var ta=Symbol.for("react.postpone"),ti=new WeakMap;function tn(e,t,r,a){let i=ti.get(e);if(!i)return void ti.set(e,new Map([[t,new Map([[r,a]])]]));let n=i.get(t);if(!n)return void i.set(t,new Map([[r,a]]));n.set(r,a)}var to=new WeakMap,ts=new WeakMap,tc=new WeakMap,tl=new WeakMap;async function td(e,t,r,a){if(!e)return;if("function"!=typeof e)return e;let i=tl.get(t);if(i)return e(...i);let n=[{headers:r,cookies:a}];return tl.set(t,n),e(...n)}function tu(e){var t;let a=function(t){if("function"==typeof e.decide)return e.decide(t);if("function"==typeof e.adapter?.decide)return e.adapter.decide({key:e.key,...t});throw Error(`flags: No decide function provided for ${e.key}`)},i=function(t){return"function"==typeof e.identify?e.identify(t):"function"==typeof e.adapter?.identify?e.adapter.identify(t):e.identify},n=async function(t){let i,n,o;if(t.request){let e=function(e){let t=to.get(e);if(void 0!==t)return t;let r=new Headers;for(let[t,a]of Object.entries(e))Array.isArray(a)?a.forEach(e=>r.append(t,e)):void 0!==a&&r.append(t,a);return to.set(e,r),r}(t.request.headers);i=function(e){let t=ts.get(e);if(void 0!==t)return t;let r=e4.seal(e);return ts.set(e,r),r}(e),n=function(e){let t=tc.get(e);if(void 0!==t)return t;let r=e8.seal(new e9(e));return tc.set(e,r),r}(e),o=t.request.headers}else{let{headers:e,cookies:t}=await Promise.resolve().then(r.bind(r,44999)),[a,s]=await Promise.all([e(),t()]);i=a,n=s,o=a}let s=await te(n.get("vercel-flag-overrides")?.value),c=await td(t.identify,o,i,n),l=JSON.stringify(c)??"",d=function(e,t,r){let a=ti.get(e)?.get(t);if(a)return a.get(r)}(i,e.key,l);if(void 0!==d)return eZ("method","cached"),await d;if(s&&void 0!==s[e.key]){eZ("method","override");let t=s[e.key];return tn(i,e.key,l,Promise.resolve(t)),!function(e,t,r){let a=Symbol.for("@vercel/request-context"),i=Reflect.get(globalThis,a)?.get();i?.flags?.reportValue(e,t,{sdkVersion:ez,...r})}(e.key,t,{reason:"override"}),t}let u=(async()=>a({defaultValue:e.defaultValue,headers:i,cookies:n,entities:c}))().then(t=>{if(void 0!==t)return t;if(void 0!==e.defaultValue)return e.defaultValue;throw Error(`flags: Flag "${e.key}" must have a defaultValue or a decide function that returns a value`)},t=>{if(function(e){if("object"==typeof e&&null!==e&&"$$typeof"in e&&e.$$typeof===ta)return!0;if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let t=e.digest.split(";")[0];return"NEXT_REDIRECT"===t||"DYNAMIC_SERVER_USAGE"===t||"BAILOUT_TO_CLIENT_SIDE_RENDERING"===t||"NEXT_NOT_FOUND"===t}(t))throw t;if(void 0!==e.defaultValue)return console.warn(`flags: Flag "${e.key}" is falling back to its defaultValue after catching the following error`,t),e.defaultValue;throw console.warn(`flags: Flag "${e.key}" could not be evaluated`),t});tn(i,e.key,l,u);let f=await u;return e.config?.reportValue!==!1&&function(e,t){let r=Symbol.for("@vercel/request-context"),a=Reflect.get(globalThis,r)?.get();a?.flags?.reportValue(e,t,{sdkVersion:ez})}(e.key,f),f},o=e.origin?e.origin:"function"==typeof e.adapter?.origin?e.adapter.origin(e.key):e.adapter?.origin,s=eQ(async(...e)=>{if(eZ("method","decided"),"string"==typeof e[0]&&Array.isArray(e[1])){let[t,r,a]=e;if(t&&r)return eZ("method","precomputed"),tr(s,r,t,a)}if(e[0]&&"object"==typeof e[0]&&"headers"in e[0]){let[t]=e;return n({identify:i,request:t})}return n({identify:i,request:void 0})},{name:"flag",isVerboseTrace:!1,attributes:{key:e.key}});return s.key=e.key,s.defaultValue=e.defaultValue,s.origin=o,s.options=Array.isArray(t=e.options)?t.map(e=>"boolean"==typeof e||"number"==typeof e||"string"==typeof e||null===e?{value:e}:e):t,s.description=e.description,s.identify=i?eQ(i,{isVerboseTrace:!1,name:"identify",attributes:{key:e.key}}):i,s.decide=eQ(a,{isVerboseTrace:!1,name:"decide",attributes:{key:e.key}}),s.run=eQ(n,{isVerboseTrace:!1,name:"run",attributes:{key:e.key}}),s}},57800:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},80445:(e,t,r)=>{r.d(t,{UK:()=>P});var a,i=r(51455),n="@vercel/edge-config",o="1.4.0",s=Symbol.for("@vercel/edge-config:global-trace");function c(e,t={name:e.name}){return function(...r){let a=function(){let e=Reflect.get(globalThis,s);return null==e?void 0:e.getTracer(n,o)}();return a&&("true"===process.env.EDGE_CONFIG_TRACE_VERBOSE||!1===t.isVerboseTrace)?a.startActiveSpan(t.name,a=>{t.attributes&&a.setAttributes(t.attributes);try{let i=e.apply(this,r);return null!==i&&"object"==typeof i&&"then"in i&&"function"==typeof i.then?i.then(e=>{t.attributesSuccess&&a.setAttributes(t.attributesSuccess(e)),a.setStatus({code:1}),a.end()}).catch(e=>{t.attributesError&&a.setAttributes(t.attributesError(e)),a.setStatus({code:2,message:e instanceof Error?e.message:void 0}),a.end()}):(t.attributesSuccess&&a.setAttributes(t.attributesSuccess(i)),a.setStatus({code:1}),a.end()),i}catch(e){throw t.attributesError&&a.setAttributes(t.attributesError(e)),a.setStatus({code:2,message:e instanceof Error?e.message:void 0}),a.end(),e}}):e.apply(this,r)}}var l={UNAUTHORIZED:"@vercel/edge-config: Unauthorized",EDGE_CONFIG_NOT_FOUND:"@vercel/edge-config: Edge Config not found"},d=class extends Error{constructor(e){super(`@vercel/edge-config: Unexpected error due to response with status code ${e.status}`)}};function u(e){if("string"!=typeof e)throw Error("@vercel/edge-config: Expected key to be a string")}function f(e){return""===e.trim()}function h(e){if(!Array.isArray(e)||e.some(e=>"string"!=typeof e))throw Error("@vercel/edge-config: Expected keys to be an array of string")}function p(e){return new Response(e.response,{headers:{...e.headers,Age:String(Math.max(1,Math.floor((Date.now()-e.time)/1e3)))},status:e.status})}c(function(e){return"function"==typeof structuredClone?structuredClone(e):void 0===e?e:JSON.parse(JSON.stringify(e))},{name:"clone"});var y=new Map,m=c(async function(e,t={}){let{headers:r=new Headers,...a}=t,i=r.get("Authorization"),n=`${e},${i||""}`,o=y.get(n);if(o){let{etag:t,response:i}=o,s=new Headers(r);s.set("If-None-Match",t);let c=function(e){if(!e)return null;let t=/stale-if-error=(?<staleIfError>\d+)/i.exec(e);return(null==t?void 0:t.groups)?Number(t.groups.staleIfError):null}(s.get("Cache-Control")),l=await fetch(e,{...a,headers:s}).then(function(e){switch(e.status){case 500:case 502:case 503:case 504:return"number"==typeof c&&o.time<Date.now()+1e3*c?p(o):e;default:return e}},function(e){if("number"==typeof c&&o.time<Date.now()+1e3*c)return p(o);throw e});if(304===l.status)return l.cachedResponseBody=JSON.parse(i),l;let d=l.headers.get("ETag");return l.ok&&d&&y.set(n,{etag:d,response:await l.clone().text(),headers:Object.fromEntries(l.headers.entries()),status:l.status,time:Date.now()}),l}let s=await fetch(e,t),c=s.headers.get("ETag");return s.ok&&c&&y.set(n,{etag:c,response:await s.clone().text(),headers:Object.fromEntries(s.headers.entries()),status:s.status,time:Date.now()}),s},{name:"fetchWithCachedResponse",attributesSuccess:e=>({status:e.status})}),w=new Map,g=c(i.readFile,{name:"readFile"}),E=c(JSON.parse,{name:"JSON.parse"}),v=Symbol.for("privateEdgeConfig"),b=c((e,t)=>{let r=w.get(e);if(r)return r;let a=E(t);return w.set(e,Object.freeze(a)),a},{name:"cached JSON.parse"}),S=c(async function(e){if("vercel"!==e.type||!process.env.AWS_LAMBDA_FUNCTION_NAME)return null;try{let t=await g(`/opt/edge-config/${e.id}.json`,"utf-8");return b(e.id,t)}catch{return null}},{name:"getFileSystemEdgeConfig"}),A=c(async function(e){let t=Reflect.get(globalThis,v);return"object"==typeof t&&"function"==typeof t.get?t.get(e.id):null},{name:"getPrivateEdgeConfig"});function k(e){e.set("x-edge-config-min-updated-at",`${Number.MAX_SAFE_INTEGER}`)}async function _(e,t){return(null==t?void 0:t.consistentRead)?null:await A(e)||await S(e)}async function R(e){await e.arrayBuffer()}var C=c(function(e,t={staleIfError:604800,cache:"no-store"}){var r;let a,i;if(!e)throw Error("@vercel/edge-config: No connection string provided");let s=function(e){try{if(!e.startsWith("edge-config:"))return null;let t=new URLSearchParams(e.slice(12)),r=t.get("id"),a=t.get("token");if(!r||!a)return null;return{type:"vercel",baseUrl:`https://edge-config.vercel.com/${r}`,id:r,version:"1",token:a}}catch{}return null}(e)||function(e){try{let t=new URL(e);if("edge-config.vercel.com"!==t.host||"https:"!==t.protocol||!t.pathname.startsWith("/ecfg"))return null;let r=t.pathname.split("/")[1];if(!r)return null;let a=t.searchParams.get("token");if(!a||""===a)return null;return{type:"vercel",baseUrl:`https://edge-config.vercel.com/${r}`,id:r,version:"1",token:a}}catch{return null}}(e)||function(e){try{let t=new URL(e),r=t.searchParams.get("id"),a=t.searchParams.get("token"),i=t.searchParams.get("version")||"1";if((!r||t.pathname.startsWith("/ecfg_"))&&(r=t.pathname.split("/")[1]||null),!r||!a)return null;return t.search="",{type:"external",baseUrl:t.toString(),id:r,token:a,version:i}}catch{return null}}(e);if(!s)throw Error("@vercel/edge-config: Invalid connection string provided");let p=s.id,y=s.baseUrl,w=s.version,g={Authorization:`Bearer ${s.token}`};"undefined"!=typeof process&&process.env.VERCEL_ENV&&(g["x-edge-config-vercel-env"]=process.env.VERCEL_ENV),"string"==typeof n&&"string"==typeof o&&(g["x-edge-config-sdk"]=`${n}@${o}`),"number"==typeof t.staleIfError&&t.staleIfError>0&&(g["cache-control"]=`stale-if-error=${t.staleIfError}`);let E=t.cache||"no-store",v=(r=!t.disableDevelopmentCache&&!1,a=null,i=null,c(e=>(null==e?void 0:e.consistentRead)||!r?Promise.resolve(null):(i||(i=m(`${s.baseUrl}/items?version=${s.version}`,{headers:new Headers(g),cache:E}).then(async e=>{let t,r=e.headers.get("x-edge-config-digest");if(e.ok)t=await e.json();else if(await R(e),!(t=e.cachedResponseBody))return null;return{digest:r,items:t}})).then(e=>{a=Promise.resolve(e),i=null},()=>{a=null,i=null}),a||(a=i),a),{name:"getInMemoryEdgeConfig"}));return{...{get:c(async function(e,t){let r=await v(t)||await _(s,t);if(u(e),f(e))return;if(r)return Promise.resolve(r.items[e]);let a=new Headers(g);return(null==t?void 0:t.consistentRead)&&k(a),m(`${y}/item/${e}?version=${w}`,{headers:a,cache:E}).then(async e=>{if(e.ok)return e.json();if(await R(e),401===e.status)throw Error(l.UNAUTHORIZED);if(404===e.status){if(e.headers.has("x-edge-config-digest"))return;throw Error(l.EDGE_CONFIG_NOT_FOUND)}if(void 0!==e.cachedResponseBody)return e.cachedResponseBody;throw new d(e)})},{name:"get",isVerboseTrace:!1,attributes:{edgeConfigId:p}}),has:c(async function(e,t){let r=await v(t)||await _(s,t);if(u(e),f(e))return!1;if(r){var a;return Promise.resolve((a=r.items,Object.prototype.hasOwnProperty.call(a,e)))}let i=new Headers(g);return(null==t?void 0:t.consistentRead)&&k(i),fetch(`${y}/item/${e}?version=${w}`,{method:"HEAD",headers:i,cache:E}).then(e=>{if(401===e.status)throw Error(l.UNAUTHORIZED);if(404===e.status){if(e.headers.has("x-edge-config-digest"))return!1;throw Error(l.EDGE_CONFIG_NOT_FOUND)}if(e.ok)return!0;throw new d(e)})},{name:"has",isVerboseTrace:!1,attributes:{edgeConfigId:p}}),getAll:c(async function(e,t){let r=await v(t)||await _(s,t);if(r)return void 0===e?Promise.resolve(r.items):(h(e),Promise.resolve(function(e,t){let r={};return t.forEach(t=>{r[t]=e[t]}),r}(r.items,e)));Array.isArray(e)&&h(e);let a=Array.isArray(e)?new URLSearchParams(e.filter(e=>"string"==typeof e&&!f(e)).map(e=>["key",e])).toString():null;if(""===a)return Promise.resolve({});let i=new Headers(g);return(null==t?void 0:t.consistentRead)&&k(i),m(`${y}/items?version=${w}${null===a?"":`&${a}`}`,{headers:i,cache:E}).then(async e=>{if(e.ok)return e.json();if(await R(e),401===e.status)throw Error(l.UNAUTHORIZED);if(404===e.status)throw Error(l.EDGE_CONFIG_NOT_FOUND);if(void 0!==e.cachedResponseBody)return e.cachedResponseBody;throw new d(e)})},{name:"getAll",isVerboseTrace:!1,attributes:{edgeConfigId:p}}),digest:c(async function(e){let t=await v(e)||await _(s,e);if(t)return Promise.resolve(t.digest);let r=new Headers(g);return(null==e?void 0:e.consistentRead)&&k(r),m(`${y}/digest?version=${w}`,{headers:r,cache:E}).then(async e=>{if(e.ok)return e.json();if(await R(e),void 0!==e.cachedResponseBody)return e.cachedResponseBody;throw new d(e)})},{name:"digest",isVerboseTrace:!1,attributes:{edgeConfigId:p}})},connection:s}},{name:"createClient"}),P=(...e)=>(a||(a=C(process.env.EDGE_CONFIG)),a.getAll(...e))},81620:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},85397:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("Command",[["path",{d:"M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3",key:"11bfej"}]])},85778:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},93661:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(62688).A)("Ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])}};