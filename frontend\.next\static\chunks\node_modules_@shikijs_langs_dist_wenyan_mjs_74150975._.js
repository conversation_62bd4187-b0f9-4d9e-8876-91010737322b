(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@shikijs/langs/dist/wenyan.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const lang = Object.freeze(JSON.parse("{\"displayName\":\"Wenyan\",\"name\":\"wenyan\",\"patterns\":[{\"include\":\"#keywords\"},{\"include\":\"#constants\"},{\"include\":\"#operators\"},{\"include\":\"#symbols\"},{\"include\":\"#expression\"},{\"include\":\"#comment-blocks\"},{\"include\":\"#comment-lines\"}],\"repository\":{\"comment-blocks\":{\"begin\":\"([批注疏]曰)。?(「「|『)\",\"end\":\"(」」|』)\",\"name\":\"comment.block\",\"patterns\":[{\"match\":\"\\\\\\\\.\",\"name\":\"constant.character\"}]},\"comment-lines\":{\"begin\":\"[批注疏]曰\",\"end\":\"$\",\"name\":\"comment.line\",\"patterns\":[{\"match\":\"\\\\\\\\.\",\"name\":\"constant.character\"}]},\"constants\":{\"patterns\":[{\"match\":\"[·〇一七三九二五京億兆八六分十千又四垓埃塵微忽極正毫沙渺溝漠澗百秭穰絲纖萬負載釐零]\",\"name\":\"constant.numeric\"},{\"match\":\"[其陰陽]\",\"name\":\"constant.language\"},{\"begin\":\"「「|『\",\"end\":\"」」|』\",\"name\":\"string.quoted\",\"patterns\":[{\"match\":\"\\\\\\\\.\",\"name\":\"constant.character\"}]}]},\"expression\":{\"patterns\":[{\"include\":\"#variables\"}]},\"keywords\":{\"patterns\":[{\"match\":\"[元列數爻物術言]\",\"name\":\"storage.type\"},{\"match\":\"乃行是術曰|若其不然者|乃歸空無|欲行是術|乃止是遍|若其然者|其物如是|乃得矣|之術也|必先得|是術曰|恆為是|之物也|乃得|是謂|云云|中之|為是|乃止|若非|或若|之長|其餘\",\"name\":\"keyword.control\"},{\"match\":\"或云|蓋謂\",\"name\":\"keyword.control\"},{\"match\":\"中有陽乎|中無陰乎|所餘幾何|不等於|不大於|不小於|等於|大於|小於|[乘以加於減變除]\",\"name\":\"keyword.operator\"},{\"match\":\"不知何禍歟|不復存矣|姑妄行此|如事不諧|名之曰|吾嘗觀|之禍歟|乃作罷|吾有|今有|物之|書之|以施|昔之|是矣|之書|方悟|之義|嗚呼|之禍|[中今取噫夫施曰有豈]\",\"name\":\"keyword.other\"},{\"match\":\"[之也充凡者若遍銜]\",\"name\":\"keyword.control\"}]},\"symbols\":{\"patterns\":[{\"match\":\"[、。]\",\"name\":\"punctuation.separator\"}]},\"variables\":{\"begin\":\"「\",\"end\":\"」\",\"name\":\"variable.other\",\"patterns\":[{\"match\":\"\\\\\\\\.\",\"name\":\"constant.character\"}]}},\"scopeName\":\"source.wenyan\",\"aliases\":[\"文言\"]}"));
const __TURBOPACK__default__export__ = [
    lang
];
}}),
}]);

//# sourceMappingURL=node_modules_%40shikijs_langs_dist_wenyan_mjs_74150975._.js.map