"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2103],{2564:(e,r,n)=>{n.d(r,{Qg:()=>l,bL:()=>i});var t=n(12115),o=n(63655),a=n(95155),l=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),u=t.forwardRef((e,r)=>(0,a.jsx)(o.sG.span,{...e,ref:r,style:{...l,...e.style}}));u.displayName="VisuallyHidden";var i=u},48698:(e,r,n)=>{n.d(r,{H_:()=>e1,UC:()=>eJ,YJ:()=>eQ,q7:()=>e0,VF:()=>e5,JU:()=>e$,ZL:()=>eZ,bL:()=>eW,wv:()=>e8,l9:()=>eY});var t=n(12115),o=n(85185),a=n(6101),l=n(46081),u=n(5845),i=n(63655),s=n(29855),d=n(94315),c=n(19178),p=n(92293),f=n(25519),v=n(61285),m=n(35152),h=n(34378),w=n(28905),g=n(89196),x=n(99708),y=n(39033),b=n(38168),C=n(58890),R=n(95155),M=["Enter"," "],j=["ArrowUp","PageDown","End"],D=["ArrowDown","PageUp","Home",...j],_={ltr:[...M,"ArrowRight"],rtl:[...M,"ArrowLeft"]},k={ltr:["ArrowLeft"],rtl:["ArrowRight"]},I="Menu",[E,P,T]=(0,s.N)(I),[S,F]=(0,l.A)(I,[T,m.Bk,g.RG]),N=(0,m.Bk)(),L=(0,g.RG)(),[A,O]=S(I),[G,K]=S(I),B=e=>{let{__scopeMenu:r,open:n=!1,children:o,dir:a,onOpenChange:l,modal:u=!0}=e,i=N(r),[s,c]=t.useState(null),p=t.useRef(!1),f=(0,y.c)(l),v=(0,d.jH)(a);return t.useEffect(()=>{let e=()=>{p.current=!0,document.addEventListener("pointerdown",r,{capture:!0,once:!0}),document.addEventListener("pointermove",r,{capture:!0,once:!0})},r=()=>p.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",r,{capture:!0}),document.removeEventListener("pointermove",r,{capture:!0})}},[]),(0,R.jsx)(m.bL,{...i,children:(0,R.jsx)(A,{scope:r,open:n,onOpenChange:f,content:s,onContentChange:c,children:(0,R.jsx)(G,{scope:r,onClose:t.useCallback(()=>f(!1),[f]),isUsingKeyboardRef:p,dir:v,modal:u,children:o})})})};B.displayName=I;var U=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=N(n);return(0,R.jsx)(m.Mz,{...o,...t,ref:r})});U.displayName="MenuAnchor";var V="MenuPortal",[H,q]=S(V,{forceMount:void 0}),X=e=>{let{__scopeMenu:r,forceMount:n,children:t,container:o}=e,a=O(V,r);return(0,R.jsx)(H,{scope:r,forceMount:n,children:(0,R.jsx)(w.C,{present:n||a.open,children:(0,R.jsx)(h.Z,{asChild:!0,container:o,children:t})})})};X.displayName=V;var z="MenuContent",[W,Y]=S(z),Z=t.forwardRef((e,r)=>{let n=q(z,e.__scopeMenu),{forceMount:t=n.forceMount,...o}=e,a=O(z,e.__scopeMenu),l=K(z,e.__scopeMenu);return(0,R.jsx)(E.Provider,{scope:e.__scopeMenu,children:(0,R.jsx)(w.C,{present:t||a.open,children:(0,R.jsx)(E.Slot,{scope:e.__scopeMenu,children:l.modal?(0,R.jsx)(J,{...o,ref:r}):(0,R.jsx)(Q,{...o,ref:r})})})})}),J=t.forwardRef((e,r)=>{let n=O(z,e.__scopeMenu),l=t.useRef(null),u=(0,a.s)(r,l);return t.useEffect(()=>{let e=l.current;if(e)return(0,b.Eq)(e)},[]),(0,R.jsx)(ee,{...e,ref:u,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Q=t.forwardRef((e,r)=>{let n=O(z,e.__scopeMenu);return(0,R.jsx)(ee,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),$=(0,x.TL)("MenuContent.ScrollLock"),ee=t.forwardRef((e,r)=>{let{__scopeMenu:n,loop:l=!1,trapFocus:u,onOpenAutoFocus:i,onCloseAutoFocus:s,disableOutsidePointerEvents:d,onEntryFocus:v,onEscapeKeyDown:h,onPointerDownOutside:w,onFocusOutside:x,onInteractOutside:y,onDismiss:b,disableOutsideScroll:M,..._}=e,k=O(z,n),I=K(z,n),E=N(n),T=L(n),S=P(n),[F,A]=t.useState(null),G=t.useRef(null),B=(0,a.s)(r,G,k.onContentChange),U=t.useRef(0),V=t.useRef(""),H=t.useRef(0),q=t.useRef(null),X=t.useRef("right"),Y=t.useRef(0),Z=M?C.A:t.Fragment,J=e=>{var r,n;let t=V.current+e,o=S().filter(e=>!e.disabled),a=document.activeElement,l=null==(r=o.find(e=>e.ref.current===a))?void 0:r.textValue,u=function(e,r,n){var t;let o=r.length>1&&Array.from(r).every(e=>e===r[0])?r[0]:r,a=n?e.indexOf(n):-1,l=(t=Math.max(a,0),e.map((r,n)=>e[(t+n)%e.length]));1===o.length&&(l=l.filter(e=>e!==n));let u=l.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return u!==n?u:void 0}(o.map(e=>e.textValue),t,l),i=null==(n=o.find(e=>e.textValue===u))?void 0:n.ref.current;!function e(r){V.current=r,window.clearTimeout(U.current),""!==r&&(U.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};t.useEffect(()=>()=>window.clearTimeout(U.current),[]),(0,p.Oh)();let Q=t.useCallback(e=>{var r,n;return X.current===(null==(r=q.current)?void 0:r.side)&&function(e,r){return!!r&&function(e,r){let{x:n,y:t}=e,o=!1;for(let e=0,a=r.length-1;e<r.length;a=e++){let l=r[e],u=r[a],i=l.x,s=l.y,d=u.x,c=u.y;s>t!=c>t&&n<(d-i)*(t-s)/(c-s)+i&&(o=!o)}return o}({x:e.clientX,y:e.clientY},r)}(e,null==(n=q.current)?void 0:n.area)},[]);return(0,R.jsx)(W,{scope:n,searchRef:V,onItemEnter:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:t.useCallback(e=>{var r;Q(e)||(null==(r=G.current)||r.focus(),A(null))},[Q]),onTriggerLeave:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:H,onPointerGraceIntentChange:t.useCallback(e=>{q.current=e},[]),children:(0,R.jsx)(Z,{...M?{as:$,allowPinchZoom:!0}:void 0,children:(0,R.jsx)(f.n,{asChild:!0,trapped:u,onMountAutoFocus:(0,o.m)(i,e=>{var r;e.preventDefault(),null==(r=G.current)||r.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,R.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:h,onPointerDownOutside:w,onFocusOutside:x,onInteractOutside:y,onDismiss:b,children:(0,R.jsx)(g.bL,{asChild:!0,...T,dir:I.dir,orientation:"vertical",loop:l,currentTabStopId:F,onCurrentTabStopIdChange:A,onEntryFocus:(0,o.m)(v,e=>{I.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,R.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eD(k.open),"data-radix-menu-content":"",dir:I.dir,...E,..._,ref:B,style:{outline:"none",..._.style},onKeyDown:(0,o.m)(_.onKeyDown,e=>{let r=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!n&&t&&J(e.key));let o=G.current;if(e.target!==o||!D.includes(e.key))return;e.preventDefault();let a=S().filter(e=>!e.disabled).map(e=>e.ref.current);j.includes(e.key)&&a.reverse(),function(e){let r=document.activeElement;for(let n of e)if(n===r||(n.focus(),document.activeElement!==r))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(U.current),V.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,eI(e=>{let r=e.target,n=Y.current!==e.clientX;e.currentTarget.contains(r)&&n&&(X.current=e.clientX>Y.current?"right":"left",Y.current=e.clientX)}))})})})})})})});Z.displayName=z;var er=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,R.jsx)(i.sG.div,{role:"group",...t,ref:r})});er.displayName="MenuGroup";var en=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,R.jsx)(i.sG.div,{...t,ref:r})});en.displayName="MenuLabel";var et="MenuItem",eo="menu.itemSelect",ea=t.forwardRef((e,r)=>{let{disabled:n=!1,onSelect:l,...u}=e,s=t.useRef(null),d=K(et,e.__scopeMenu),c=Y(et,e.__scopeMenu),p=(0,a.s)(r,s),f=t.useRef(!1);return(0,R.jsx)(el,{...u,ref:p,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=s.current;if(!n&&e){let r=new CustomEvent(eo,{bubbles:!0,cancelable:!0});e.addEventListener(eo,e=>null==l?void 0:l(e),{once:!0}),(0,i.hO)(e,r),r.defaultPrevented?f.current=!1:d.onClose()}}),onPointerDown:r=>{var n;null==(n=e.onPointerDown)||n.call(e,r),f.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var r;f.current||null==(r=e.currentTarget)||r.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=""!==c.searchRef.current;n||r&&" "===e.key||M.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ea.displayName=et;var el=t.forwardRef((e,r)=>{let{__scopeMenu:n,disabled:l=!1,textValue:u,...s}=e,d=Y(et,n),c=L(n),p=t.useRef(null),f=(0,a.s)(r,p),[v,m]=t.useState(!1),[h,w]=t.useState("");return t.useEffect(()=>{let e=p.current;if(e){var r;w((null!=(r=e.textContent)?r:"").trim())}},[s.children]),(0,R.jsx)(E.ItemSlot,{scope:n,disabled:l,textValue:null!=u?u:h,children:(0,R.jsx)(g.q7,{asChild:!0,...c,focusable:!l,children:(0,R.jsx)(i.sG.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":l||void 0,"data-disabled":l?"":void 0,...s,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,eI(e=>{l?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eI(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>m(!0)),onBlur:(0,o.m)(e.onBlur,()=>m(!1))})})})}),eu=t.forwardRef((e,r)=>{let{checked:n=!1,onCheckedChange:t,...a}=e;return(0,R.jsx)(em,{scope:e.__scopeMenu,checked:n,children:(0,R.jsx)(ea,{role:"menuitemcheckbox","aria-checked":e_(n)?"mixed":n,...a,ref:r,"data-state":ek(n),onSelect:(0,o.m)(a.onSelect,()=>null==t?void 0:t(!!e_(n)||!n),{checkForDefaultPrevented:!1})})})});eu.displayName="MenuCheckboxItem";var ei="MenuRadioGroup",[es,ed]=S(ei,{value:void 0,onValueChange:()=>{}}),ec=t.forwardRef((e,r)=>{let{value:n,onValueChange:t,...o}=e,a=(0,y.c)(t);return(0,R.jsx)(es,{scope:e.__scopeMenu,value:n,onValueChange:a,children:(0,R.jsx)(er,{...o,ref:r})})});ec.displayName=ei;var ep="MenuRadioItem",ef=t.forwardRef((e,r)=>{let{value:n,...t}=e,a=ed(ep,e.__scopeMenu),l=n===a.value;return(0,R.jsx)(em,{scope:e.__scopeMenu,checked:l,children:(0,R.jsx)(ea,{role:"menuitemradio","aria-checked":l,...t,ref:r,"data-state":ek(l),onSelect:(0,o.m)(t.onSelect,()=>{var e;return null==(e=a.onValueChange)?void 0:e.call(a,n)},{checkForDefaultPrevented:!1})})})});ef.displayName=ep;var ev="MenuItemIndicator",[em,eh]=S(ev,{checked:!1}),ew=t.forwardRef((e,r)=>{let{__scopeMenu:n,forceMount:t,...o}=e,a=eh(ev,n);return(0,R.jsx)(w.C,{present:t||e_(a.checked)||!0===a.checked,children:(0,R.jsx)(i.sG.span,{...o,ref:r,"data-state":ek(a.checked)})})});ew.displayName=ev;var eg=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,R.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...t,ref:r})});eg.displayName="MenuSeparator";var ex=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=N(n);return(0,R.jsx)(m.i3,{...o,...t,ref:r})});ex.displayName="MenuArrow";var[ey,eb]=S("MenuSub"),eC="MenuSubTrigger",eR=t.forwardRef((e,r)=>{let n=O(eC,e.__scopeMenu),l=K(eC,e.__scopeMenu),u=eb(eC,e.__scopeMenu),i=Y(eC,e.__scopeMenu),s=t.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:c}=i,p={__scopeMenu:e.__scopeMenu},f=t.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return t.useEffect(()=>f,[f]),t.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),c(null)}},[d,c]),(0,R.jsx)(U,{asChild:!0,...p,children:(0,R.jsx)(el,{id:u.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":u.contentId,"data-state":eD(n.open),...e,ref:(0,a.t)(r,u.onTriggerChange),onClick:r=>{var t;null==(t=e.onClick)||t.call(e,r),e.disabled||r.defaultPrevented||(r.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,eI(r=>{i.onItemEnter(r),!r.defaultPrevented&&(e.disabled||n.open||s.current||(i.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{n.onOpenChange(!0),f()},100)))})),onPointerLeave:(0,o.m)(e.onPointerLeave,eI(e=>{var r,t;f();let o=null==(r=n.content)?void 0:r.getBoundingClientRect();if(o){let r=null==(t=n.content)?void 0:t.dataset.side,a="right"===r,l=o[a?"left":"right"],u=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:l,y:o.top},{x:u,y:o.top},{x:u,y:o.bottom},{x:l,y:o.bottom}],side:r}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,r=>{let t=""!==i.searchRef.current;if(!e.disabled&&(!t||" "!==r.key)&&_[l.dir].includes(r.key)){var o;n.onOpenChange(!0),null==(o=n.content)||o.focus(),r.preventDefault()}})})})});eR.displayName=eC;var eM="MenuSubContent",ej=t.forwardRef((e,r)=>{let n=q(z,e.__scopeMenu),{forceMount:l=n.forceMount,...u}=e,i=O(z,e.__scopeMenu),s=K(z,e.__scopeMenu),d=eb(eM,e.__scopeMenu),c=t.useRef(null),p=(0,a.s)(r,c);return(0,R.jsx)(E.Provider,{scope:e.__scopeMenu,children:(0,R.jsx)(w.C,{present:l||i.open,children:(0,R.jsx)(E.Slot,{scope:e.__scopeMenu,children:(0,R.jsx)(ee,{id:d.contentId,"aria-labelledby":d.triggerId,...u,ref:p,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var r;s.isUsingKeyboardRef.current&&(null==(r=c.current)||r.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=e.currentTarget.contains(e.target),n=k[s.dir].includes(e.key);if(r&&n){var t;i.onOpenChange(!1),null==(t=d.trigger)||t.focus(),e.preventDefault()}})})})})})});function eD(e){return e?"open":"closed"}function e_(e){return"indeterminate"===e}function ek(e){return e_(e)?"indeterminate":e?"checked":"unchecked"}function eI(e){return r=>"mouse"===r.pointerType?e(r):void 0}ej.displayName=eM;var eE="DropdownMenu",[eP,eT]=(0,l.A)(eE,[F]),eS=F(),[eF,eN]=eP(eE),eL=e=>{let{__scopeDropdownMenu:r,children:n,dir:o,open:a,defaultOpen:l,onOpenChange:i,modal:s=!0}=e,d=eS(r),c=t.useRef(null),[p,f]=(0,u.i)({prop:a,defaultProp:null!=l&&l,onChange:i,caller:eE});return(0,R.jsx)(eF,{scope:r,triggerId:(0,v.B)(),triggerRef:c,contentId:(0,v.B)(),open:p,onOpenChange:f,onOpenToggle:t.useCallback(()=>f(e=>!e),[f]),modal:s,children:(0,R.jsx)(B,{...d,open:p,onOpenChange:f,dir:o,modal:s,children:n})})};eL.displayName=eE;var eA="DropdownMenuTrigger",eO=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,disabled:t=!1,...l}=e,u=eN(eA,n),s=eS(n);return(0,R.jsx)(U,{asChild:!0,...s,children:(0,R.jsx)(i.sG.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...l,ref:(0,a.t)(r,u.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{!t&&0===e.button&&!1===e.ctrlKey&&(u.onOpenToggle(),u.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eO.displayName=eA;var eG=e=>{let{__scopeDropdownMenu:r,...n}=e,t=eS(r);return(0,R.jsx)(X,{...t,...n})};eG.displayName="DropdownMenuPortal";var eK="DropdownMenuContent",eB=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...a}=e,l=eN(eK,n),u=eS(n),i=t.useRef(!1);return(0,R.jsx)(Z,{id:l.contentId,"aria-labelledby":l.triggerId,...u,...a,ref:r,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var r;i.current||null==(r=l.triggerRef.current)||r.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let r=e.detail.originalEvent,n=0===r.button&&!0===r.ctrlKey,t=2===r.button||n;(!l.modal||t)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eB.displayName=eK;var eU=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eS(n);return(0,R.jsx)(er,{...o,...t,ref:r})});eU.displayName="DropdownMenuGroup";var eV=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eS(n);return(0,R.jsx)(en,{...o,...t,ref:r})});eV.displayName="DropdownMenuLabel";var eH=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eS(n);return(0,R.jsx)(ea,{...o,...t,ref:r})});eH.displayName="DropdownMenuItem";var eq=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eS(n);return(0,R.jsx)(eu,{...o,...t,ref:r})});eq.displayName="DropdownMenuCheckboxItem",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eS(n);return(0,R.jsx)(ec,{...o,...t,ref:r})}).displayName="DropdownMenuRadioGroup",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eS(n);return(0,R.jsx)(ef,{...o,...t,ref:r})}).displayName="DropdownMenuRadioItem";var eX=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eS(n);return(0,R.jsx)(ew,{...o,...t,ref:r})});eX.displayName="DropdownMenuItemIndicator";var ez=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eS(n);return(0,R.jsx)(eg,{...o,...t,ref:r})});ez.displayName="DropdownMenuSeparator",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eS(n);return(0,R.jsx)(ex,{...o,...t,ref:r})}).displayName="DropdownMenuArrow",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eS(n);return(0,R.jsx)(eR,{...o,...t,ref:r})}).displayName="DropdownMenuSubTrigger",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eS(n);return(0,R.jsx)(ej,{...o,...t,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var eW=eL,eY=eO,eZ=eG,eJ=eB,eQ=eU,e$=eV,e0=eH,e1=eq,e5=eX,e8=ez},89196:(e,r,n)=>{n.d(r,{RG:()=>b,bL:()=>E,q7:()=>P});var t=n(12115),o=n(85185),a=n(29855),l=n(6101),u=n(46081),i=n(61285),s=n(63655),d=n(39033),c=n(5845),p=n(94315),f=n(95155),v="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[w,g,x]=(0,a.N)(h),[y,b]=(0,u.A)(h,[x]),[C,R]=y(h),M=t.forwardRef((e,r)=>(0,f.jsx)(w.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(w.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,f.jsx)(j,{...e,ref:r})})}));M.displayName=h;var j=t.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:u=!1,dir:i,currentTabStopId:w,defaultCurrentTabStopId:x,onCurrentTabStopIdChange:y,onEntryFocus:b,preventScrollOnEntryFocus:R=!1,...M}=e,j=t.useRef(null),D=(0,l.s)(r,j),_=(0,p.jH)(i),[k,E]=(0,c.i)({prop:w,defaultProp:null!=x?x:null,onChange:y,caller:h}),[P,T]=t.useState(!1),S=(0,d.c)(b),F=g(n),N=t.useRef(!1),[L,A]=t.useState(0);return t.useEffect(()=>{let e=j.current;if(e)return e.addEventListener(v,S),()=>e.removeEventListener(v,S)},[S]),(0,f.jsx)(C,{scope:n,orientation:a,dir:_,loop:u,currentTabStopId:k,onItemFocus:t.useCallback(e=>E(e),[E]),onItemShiftTab:t.useCallback(()=>T(!0),[]),onFocusableItemAdd:t.useCallback(()=>A(e=>e+1),[]),onFocusableItemRemove:t.useCallback(()=>A(e=>e-1),[]),children:(0,f.jsx)(s.sG.div,{tabIndex:P||0===L?-1:0,"data-orientation":a,...M,ref:D,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{N.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let r=!N.current;if(e.target===e.currentTarget&&r&&!P){let r=new CustomEvent(v,m);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=F().filter(e=>e.focusable);I([e.find(e=>e.active),e.find(e=>e.id===k),...e].filter(Boolean).map(e=>e.ref.current),R)}}N.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>T(!1))})})}),D="RovingFocusGroupItem",_=t.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:l=!1,tabStopId:u,children:d,...c}=e,p=(0,i.B)(),v=u||p,m=R(D,n),h=m.currentTabStopId===v,x=g(n),{onFocusableItemAdd:y,onFocusableItemRemove:b,currentTabStopId:C}=m;return t.useEffect(()=>{if(a)return y(),()=>b()},[a,y,b]),(0,f.jsx)(w.ItemSlot,{scope:n,id:v,focusable:a,active:l,children:(0,f.jsx)(s.sG.span,{tabIndex:h?0:-1,"data-orientation":m.orientation,...c,ref:r,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?m.onItemFocus(v):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>m.onItemFocus(v)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void m.onItemShiftTab();if(e.target!==e.currentTarget)return;let r=function(e,r,n){var t;let o=(t=e.key,"rtl"!==n?t:"ArrowLeft"===t?"ArrowRight":"ArrowRight"===t?"ArrowLeft":t);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(o)))return k[o]}(e,m.orientation,m.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=x().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)n.reverse();else if("prev"===r||"next"===r){"prev"===r&&n.reverse();let t=n.indexOf(e.currentTarget);n=m.loop?function(e,r){return e.map((n,t)=>e[(r+t)%e.length])}(n,t+1):n.slice(t+1)}setTimeout(()=>I(n))}}),children:"function"==typeof d?d({isCurrentTabStop:h,hasTabStop:null!=C}):d})})});_.displayName=D;var k={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function I(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let t of e)if(t===n||(t.focus({preventScroll:r}),document.activeElement!==n))return}var E=M,P=_}}]);