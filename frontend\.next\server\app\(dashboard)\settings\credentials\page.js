(()=>{var e={};e.id=2975,e.ids=[2975],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},6211:(e,s,t)=>{"use strict";t.d(s,{A0:()=>n,Table:()=>l,TableBody:()=>i,TableCell:()=>d,TableRow:()=>c,nd:()=>o});var a=t(60687);t(43210);var r=t(4780);function l({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,a.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",e),...s})})}function n({className:e,...s}){return(0,a.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",e),...s})}function i({className:e,...s}){return(0,a.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",e),...s})}function c({className:e,...s}){return(0,a.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...s})}function o({className:e,...s}){return(0,a.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...s})}function d({className:e,...s}){return(0,a.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...s})}},8819:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},14719:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},14952:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},18622:(e,s,t)=>{Promise.resolve().then(t.bind(t,69276))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23428:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.default,__next_app__:()=>d,pages:()=>o,routeModule:()=>u,tree:()=>c});var a=t(65239),r=t(48088),l=t(31369),n=t(30893),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);t.d(s,i);let c={children:["",{children:["(dashboard)",{children:["settings",{children:["credentials",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,93623)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\settings\\credentials\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,81888)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\settings\\credentials\\layout.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,33532)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,88524))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,93595)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,88524))).default(e)],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\settings\\credentials\\page.tsx"],d={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(dashboard)/settings/credentials/page",pathname:"/settings/credentials",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},25541:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35071:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},45583:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},47033:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56085:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},57975:e=>{"use strict";e.exports=require("node:util")},58869:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},64398:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},69259:(e,s,t)=>{"use strict";t.d(s,{z:()=>c});var a=t(60687),r=t(43210),l=t.n(r),n=t(4780);let i=l().memo(function({mainCircleSize:e=210,mainCircleOpacity:s=.24,numCircles:t=8,className:r,...l}){return(0,a.jsx)("div",{className:(0,n.cn)("scale-150 pointer-events-none absolute inset-0 select-none [mask-image:linear-gradient(to_bottom,white,transparent)]",r),...l,children:Array.from({length:t},(t,r)=>{let l=e+70*r,n=s-.03*r,i=`${.06*r}s`;return(0,a.jsx)("div",{className:"absolute animate-ripple rounded-full border bg-foreground/25 shadow-xl",style:{"--i":r,width:`${l}px`,height:`${l}px`,opacity:n,animationDelay:i,borderStyle:"solid",borderWidth:"1px",borderColor:"var(--foreground)",top:"50%",left:"50%",transform:"translate(-50%, -50%) scale(1)"}},r)})})});i.displayName="Ripple";let c=({icon:e,children:s})=>(0,a.jsxs)("div",{className:"relative overflow-hidden rounded-3xl flex items-center justify-center border bg-background",children:[(0,a.jsx)("div",{className:"relative px-8 py-16 text-center",children:(0,a.jsxs)("div",{className:"mx-auto max-w-3xl space-y-6",children:[(0,a.jsx)("div",{className:"inline-flex items-center justify-center rounded-full bg-muted p-3",children:(0,a.jsx)(e,{className:"h-8 w-8 text-primary"})}),(0,a.jsx)("h1",{className:"text-4xl font-semibold tracking-tight text-foreground",children:s})]})}),(0,a.jsx)(i,{})]})},69276:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>F});var a=t(60687),r=t(43210),l=t(45583),n=t(44493),i=t(29523),c=t(96834),o=t(89667),d=t(85726),u=t(91821),p=t(6211),h=t(4780);function m({columns:e,data:s,className:t,emptyMessage:r="No data available",onRowClick:l}){return(0,a.jsx)("div",{className:(0,h.cn)("rounded-md border",t),children:(0,a.jsxs)(p.Table,{children:[(0,a.jsx)(p.A0,{children:(0,a.jsx)(p.TableRow,{children:e.map(e=>(0,a.jsx)(p.nd,{className:(0,h.cn)(e.headerClassName,e.width,"text-muted-foreground font-semibold"),children:e.header},e.id))})}),(0,a.jsx)(p.TableBody,{children:0===s.length?(0,a.jsx)(p.TableRow,{children:(0,a.jsx)(p.TableCell,{colSpan:e.length,className:"text-center py-8 text-muted-foreground",children:r})}):s.map((s,t)=>(0,a.jsx)(p.TableRow,{className:(0,h.cn)(l&&"cursor-pointer hover:bg-muted/50"),onClick:()=>l?.(s),children:e.map(e=>(0,a.jsx)(p.TableCell,{className:(0,h.cn)(e.className,e.width),children:e.cell?e.cell(s):e.accessorKey?String(s[e.accessorKey]||""):""},e.id))},t))})]})})}var x=t(41862),f=t(14719),j=t(11860),g=t(47342),y=t(84027),v=t(63143),b=t(35071),N=t(78122),w=t(88233),k=t(96474),A=t(93613),_=t(58869),C=t(99270),P=t(55701),S=t(56766),q=t(11155),z=t(52556),$=t(8693),E=t(90015),M=t(63503),R=t(21342),L=t(93500),T=t(52581);let D=({appSlug:e,appName:s,profiles:t,appImage:l,onConnect:n,onProfileUpdate:d,onProfileDelete:u,onProfileConnect:p,isUpdating:A,isDeleting:_,isConnecting:C,allAppsData:S})=>{let[q,z]=(0,r.useState)(null),[$,E]=(0,r.useState)(""),[M,D]=(0,r.useState)(null),[O,U]=(0,r.useState)(!1),[G,I]=(0,r.useState)(""),[F,K]=(0,r.useState)(!1),H=(0,P.O4)(),Z=(0,P.wG)(),B=(0,r.useMemo)(()=>S?.apps?.find(t=>t.name_slug===e||t.name.toLowerCase()===s.toLowerCase()),[S,e,s]),V=(0,r.useMemo)(()=>({id:e,name:s,name_slug:e,auth_type:"oauth",description:`Connect to ${s}`,img_src:B?.img_src||"",custom_fields_json:B?.custom_fields_json||"[]",categories:B?.categories||[],featured_weight:0,connect:{allowed_domains:B?.connect?.allowed_domains||null,base_proxy_target_url:B?.connect?.base_proxy_target_url||"",proxy_enabled:B?.connect?.proxy_enabled||!1}}),[e,s,B]),X=async()=>{if(!G.trim())return void T.oR.error("Please enter a profile name");K(!0);try{let a={profile_name:G.trim(),app_slug:e,app_name:s,is_default:0===t.length},r=await H.mutateAsync(a);await Z.mutateAsync({profileId:r.profile_id,app:e}),I(""),U(!1),T.oR.success("Profile created and connected!")}catch(e){console.error("Error creating profile:",e)}finally{K(!1)}},W=e=>{z(e.profile_id),E(e.profile_name)},Q=async e=>{$.trim()&&(await d(e,{profile_name:$.trim()}),z(null),E(""))},J=()=>{z(null),E("")},Y=[{id:"name",header:"Profile Name",width:"w-1/3",cell:e=>(0,a.jsx)("div",{className:"flex items-center gap-2",children:q===e.profile_id?(0,a.jsxs)("div",{className:"flex items-center gap-2 w-full",children:[(0,a.jsx)(o.p,{value:$,onChange:e=>E(e.target.value),onKeyDown:s=>{"Enter"===s.key&&Q(e),"Escape"===s.key&&J()},className:"h-8 text-sm",autoFocus:!0}),(0,a.jsx)(i.$,{size:"sm",onClick:()=>Q(e),disabled:A===e.profile_id,className:"h-8 px-2",children:A===e.profile_id?(0,a.jsx)(x.A,{className:"h-3 w-3 animate-spin"}):(0,a.jsx)(f.A,{className:"h-3 w-3"})}),(0,a.jsx)(i.$,{size:"sm",variant:"outline",onClick:J,className:"h-8 px-2",children:(0,a.jsx)(j.A,{className:"h-3 w-3"})})]}):(0,a.jsx)("span",{className:"font-medium",children:e.profile_name})})},{id:"status",header:"Status",width:"w-1/4",cell:e=>(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[e.is_connected?(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[(0,a.jsx)("div",{className:"h-2 w-2 rounded-full bg-green-500"}),"Connected"]}):(0,a.jsxs)("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[(0,a.jsx)("div",{className:"h-2 w-2 rounded-full bg-destructive"}),"Disconnected"]}),e.is_default&&(0,a.jsx)(c.E,{variant:"outline",className:"text-xs",children:"Default"}),!e.is_active&&(0,a.jsx)(c.E,{variant:"outline",className:"text-xs",children:"Inactive"})]})},{id:"actions",header:"Actions",width:"w-1/3",headerClassName:"text-right",className:"text-right",cell:e=>(0,a.jsxs)("div",{className:"flex items-center justify-end gap-2",children:[!e.is_connected&&(0,a.jsxs)(i.$,{size:"sm",variant:"ghost",onClick:()=>p(e),disabled:C===e.profile_id,className:"h-8 px-2 text-xs",children:[C===e.profile_id?(0,a.jsx)(x.A,{className:"h-3 w-3 animate-spin"}):(0,a.jsx)(g.A,{className:"h-3 w-3"}),"Connect"]}),(0,a.jsxs)(R.rI,{children:[(0,a.jsx)(R.ty,{asChild:!0,children:(0,a.jsx)(i.$,{size:"sm",variant:"ghost",className:"h-8 w-8 p-0",children:(0,a.jsx)(y.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(R.SQ,{align:"end",children:[(0,a.jsxs)(R._2,{onClick:()=>W(e),children:[(0,a.jsx)(v.A,{className:"h-4 w-4"}),"Edit Name"]}),(0,a.jsxs)(R._2,{onClick:()=>d(e,{is_default:!e.is_default}),children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),e.is_default?"Remove Default":"Set as Default"]}),(0,a.jsx)(R._2,{onClick:()=>d(e,{is_active:!e.is_active}),children:e.is_active?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(b.A,{className:"h-4 w-4"}),"Deactivate"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),"Activate"]})}),e.is_connected&&(0,a.jsxs)(R._2,{onClick:()=>p(e),children:[(0,a.jsx)(N.A,{className:"h-4 w-4"}),"Reconnect"]}),(0,a.jsx)(R.mB,{}),(0,a.jsxs)(R._2,{onClick:()=>D(e),className:"text-destructive",children:[(0,a.jsx)(w.A,{className:"h-4 w-4"}),"Delete"]})]})]})]})}];return(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("div",{className:"h-8 w-8 rounded-lg bg-muted/50 flex items-center justify-center overflow-hidden border",children:[l?(0,a.jsx)("img",{src:l,alt:s,className:"h-5 w-5 object-cover",onError:e=>{let s=e.target;s.style.display="none",s.nextElementSibling?.classList.remove("hidden")}}):null,(0,a.jsx)("span",{className:(0,h.cn)("text-sm font-semibold text-muted-foreground",l?"hidden":"block"),children:s.charAt(0).toUpperCase()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-sm",children:s}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[t.length," ",1===t.length?"profile":"profiles"]})]})]}),(0,a.jsx)("div",{className:"flex items-center gap-2",children:O?(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(o.p,{placeholder:"Profile name",value:G,onChange:e=>I(e.target.value),onKeyDown:e=>{"Enter"===e.key&&X(),"Escape"===e.key&&(U(!1),I(""))},className:"h-8 text-sm w-32",autoFocus:!0}),(0,a.jsx)(i.$,{size:"sm",onClick:X,disabled:F,className:"h-8 px-2",children:F?(0,a.jsx)(x.A,{className:"h-3 w-3 animate-spin"}):(0,a.jsx)(f.A,{className:"h-3 w-3"})}),(0,a.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>{U(!1),I("")},className:"h-8 px-2",children:(0,a.jsx)(j.A,{className:"h-3 w-3"})})]}):(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)(i.$,{size:"sm",variant:"link",onClick:()=>n(V),children:[(0,a.jsx)(k.A,{className:"h-3 w-3"}),"New Profile"]})})})]}),(0,a.jsx)(m,{columns:Y,data:t,emptyMessage:`No ${s} profiles found`,className:"bg-card border rounded-lg"}),(0,a.jsx)(L.Lt,{open:!!M,onOpenChange:e=>!e&&D(null),children:(0,a.jsxs)(L.EO,{children:[(0,a.jsxs)(L.wd,{children:[(0,a.jsx)(L.r7,{children:"Delete Profile"}),(0,a.jsxs)(L.$v,{children:['Are you sure you want to delete "',M?.profile_name,'"? This action cannot be undone.']})]}),(0,a.jsxs)(L.ck,{children:[(0,a.jsx)(L.Zr,{children:"Cancel"}),(0,a.jsx)(L.Rx,{onClick:()=>{M&&(u(M),D(null))},className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",children:"Delete"})]})]})})]})},O=({onConnectNewApp:e})=>{let[s,t]=(0,r.useState)(!1),[l,c]=(0,r.useState)(!1),[p,h]=(0,r.useState)(null),[m,x]=(0,r.useState)(""),[f,g]=(0,r.useState)(null),[y,v]=(0,r.useState)(null),[b,N]=(0,r.useState)(null),w=(0,$.jE)(),{data:R,isLoading:L,error:O}=(0,P.h4)(),{data:U}=(0,S.Dy)(void 0,""),G=(0,P.Lh)(),I=(0,P.PA)(),F=(0,P.wG)(),K=e=>{h(e),c(!0)},H=async(e,s)=>{g(e.profile_id);try{await G.mutateAsync({profileId:e.profile_id,request:s}),T.oR.success("Profile updated successfully")}catch(e){console.error("Error updating profile:",e)}finally{g(null)}},Z=async e=>{v(e.profile_id);try{await I.mutateAsync(e.profile_id),T.oR.success("Profile deleted successfully")}catch(e){console.error("Error deleting profile:",e)}finally{v(null)}},B=async e=>{N(e.profile_id);try{await F.mutateAsync({profileId:e.profile_id,app:e.app_slug}),T.oR.success("Profile connected successfully")}catch(e){console.error("Error connecting profile:",e)}finally{N(null)}},V=R?.reduce((e,s)=>{let t=s.app_slug;return e[t]||(e[t]=[]),e[t].push(s),e},{})||{},X=(0,r.useMemo)(()=>{if(!m.trim())return V;let e=m.toLowerCase(),s={};return Object.entries(V).forEach(([t,a])=>{let r=a[0]?.app_name.toLowerCase()||"",l=a.filter(s=>s.profile_name.toLowerCase().includes(e)||s.app_name.toLowerCase().includes(e));(r.includes(e)||l.length>0)&&(s[t]=r.includes(e)?a:l)}),s},[V,m]),W=R?.length||0,Q=R?.filter(e=>e.is_connected).length||0,J=Object.keys(V).length,Y=Object.keys(X).length,ee=Object.values(X).flat().length;return L?(0,a.jsx)("div",{className:"space-y-6",children:Array.from({length:2}).map((e,s)=>(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(d.Skeleton,{className:"h-8 w-8 rounded-lg"}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(d.Skeleton,{className:"h-4 w-32"}),(0,a.jsx)(d.Skeleton,{className:"h-3 w-24"})]})]}),(0,a.jsx)("div",{className:"rounded-md border",children:(0,a.jsxs)("div",{className:"p-4 space-y-3",children:[(0,a.jsx)(d.Skeleton,{className:"h-4 w-full"}),(0,a.jsx)(d.Skeleton,{className:"h-4 w-full"}),(0,a.jsx)(d.Skeleton,{className:"h-4 w-3/4"})]})})]},s))}):O?(0,a.jsxs)(u.Fc,{variant:"destructive",children:[(0,a.jsx)(A.A,{className:"h-4 w-4"}),(0,a.jsx)(u.TN,{children:"Failed to load credential profiles. Please try again."})]}):0===W?(0,a.jsx)(n.Zp,{className:"border-dashed",children:(0,a.jsx)(n.Wu,{className:"p-8 text-center",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-primary/10 w-fit mx-auto",children:(0,a.jsx)(_.A,{className:"h-6 w-6 text-primary"})}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("h3",{className:"font-semibold text-foreground",children:"No credential profiles yet"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground max-w-md mx-auto",children:"Connect your favorite apps to create credential profiles for your agents"})]}),(0,a.jsxs)(i.$,{onClick:()=>t(!0),className:"h-9",children:[(0,a.jsx)(k.A,{className:"h-4 w-4"}),"Connect New App"]})]})})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{children:(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:m?`${Y} ${1===Y?"app":"apps"} with ${ee} ${1===ee?"profile":"profiles"} found`:`${J} ${1===J?"app":"apps"} with ${W} ${1===W?"profile":"profiles"} (${Q} connected)`})}),(0,a.jsxs)(i.$,{onClick:()=>t(!0),size:"sm",children:[(0,a.jsx)(k.A,{className:"h-4 w-4"}),"Connect New App"]})]}),(0,a.jsxs)("div",{className:"relative max-w-sm",children:[(0,a.jsx)(C.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(o.p,{placeholder:"Search apps and profiles...",value:m,onChange:e=>x(e.target.value),className:"pl-12 h-12 rounded-xl bg-muted/50 border-0 focus:bg-background focus:ring-2 focus:ring-primary/20 transition-all"}),m&&(0,a.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>x(""),className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0 hover:bg-muted",children:(0,a.jsx)(j.A,{className:"h-3 w-3"})})]}),0===Object.keys(X).length?(0,a.jsx)(n.Zp,{className:"border-dashed",children:(0,a.jsx)(n.Wu,{className:"p-8 text-center",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"p-3 rounded-full bg-muted/50 w-fit mx-auto",children:(0,a.jsx)(C.A,{className:"h-6 w-6 text-muted-foreground"})}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("h3",{className:"font-semibold text-foreground",children:"No results found"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Try adjusting your search terms or browse all apps"})]}),(0,a.jsx)(i.$,{onClick:()=>x(""),variant:"outline",size:"sm",children:"Clear search"})]})})}):(0,a.jsx)("div",{className:"space-y-6",children:Object.entries(X).sort(([,e],[,s])=>{let t=e.filter(e=>e.is_connected).length,a=s.filter(e=>e.is_connected).length;return t!==a?a-t:s.length-e.length}).map(([e,s])=>{let t=U?.apps?.find(t=>t.name_slug===e||t.name.toLowerCase()===s[0].app_name.toLowerCase());return(0,a.jsx)(D,{appSlug:e,appName:s[0].app_name,profiles:s,appImage:t?.img_src,onConnect:K,onProfileUpdate:H,onProfileDelete:Z,onProfileConnect:B,isUpdating:f,isDeleting:y,isConnecting:b,allAppsData:U},e)})}),(0,a.jsx)(M.lG,{open:s,onOpenChange:t,children:(0,a.jsxs)(M.Cf,{className:"p-0 max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)(M.c7,{className:"sr-only",children:[(0,a.jsx)(M.L3,{children:"Browse Apps"}),(0,a.jsx)(M.rr,{children:"Select an app to create a credential profile"})]}),(0,a.jsx)(q.Z,{mode:"profile-only",onAppSelected:s=>{t(!1),e&&e(s)}})]})}),p&&(0,a.jsx)(z.R,{app:p,open:l,onOpenChange:c,onComplete:(e,s,t,a)=>{c(!1),h(null),T.oR.success(`Connected to ${t}!`),w.invalidateQueries({queryKey:E.M.profiles.all()})},mode:"profile-only"})]})};var U=t(16189),G=t(15945),I=t(69259);function F(){let{enabled:e,loading:s}=(0,G.u)("custom_agents");(0,U.useRouter)();let[t,n]=(0,r.useState)(null);return s?(0,a.jsx)("div",{className:"container mx-auto max-w-4xl px-6 py-6",children:(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,a.jsx)("div",{className:"h-32 bg-muted rounded-3xl"}),(0,a.jsx)("div",{className:"space-y-3",children:Array.from({length:3}).map((e,s)=>(0,a.jsx)("div",{className:"h-32 bg-muted rounded-lg"},s))})]})})}):e?(0,a.jsx)("div",{className:"container mx-auto max-w-4xl px-6 py-6",children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(I.z,{icon:l.A,children:(0,a.jsx)("span",{className:"text-primary",children:"App Credentials"})}),(0,a.jsx)(O,{onConnectNewApp:e=>{n(e)}})]})}):null}},70334:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},70400:(e,s,t)=>{"use strict";t.r(s),t.d(s,{"60120624b62a67d0046abca062aae687cbb5ebc44a":()=>a.vI,"602190608cb4aada86562e5e5997e6bb44fbe95e4e":()=>a.$w,"60836a64bf90333e8dead87703a0c5a32e95fa0f8f":()=>a.gj});var a=t(67834)},71766:(e,s,t)=>{Promise.resolve().then(t.bind(t,93623))},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},81888:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>l,metadata:()=>r});var a=t(37413);let r={title:"App Profiles | Kortix Suna",description:"Manage your connected app integrations",openGraph:{title:"App Profiles | Kortix Suna",description:"Manage your connected app integrations",type:"website"}};async function l({children:e}){return(0,a.jsx)(a.Fragment,{children:e})}},84027:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},84297:e=>{"use strict";e.exports=require("async_hooks")},90270:(e,s,t)=>{"use strict";t.d(s,{bL:()=>N,zi:()=>w});var a=t(43210),r=t(70569),l=t(98599),n=t(11273),i=t(65551),c=t(83721),o=t(18853),d=t(14163),u=t(60687),p="Switch",[h,m]=(0,n.A)(p),[x,f]=h(p),j=a.forwardRef((e,s)=>{let{__scopeSwitch:t,name:n,checked:c,defaultChecked:o,required:h,disabled:m,value:f="on",onCheckedChange:j,form:g,...y}=e,[N,w]=a.useState(null),k=(0,l.s)(s,e=>w(e)),A=a.useRef(!1),_=!N||g||!!N.closest("form"),[C,P]=(0,i.i)({prop:c,defaultProp:o??!1,onChange:j,caller:p});return(0,u.jsxs)(x,{scope:t,checked:C,disabled:m,children:[(0,u.jsx)(d.sG.button,{type:"button",role:"switch","aria-checked":C,"aria-required":h,"data-state":b(C),"data-disabled":m?"":void 0,disabled:m,value:f,...y,ref:k,onClick:(0,r.m)(e.onClick,e=>{P(e=>!e),_&&(A.current=e.isPropagationStopped(),A.current||e.stopPropagation())})}),_&&(0,u.jsx)(v,{control:N,bubbles:!A.current,name:n,value:f,checked:C,required:h,disabled:m,form:g,style:{transform:"translateX(-100%)"}})]})});j.displayName=p;var g="SwitchThumb",y=a.forwardRef((e,s)=>{let{__scopeSwitch:t,...a}=e,r=f(g,t);return(0,u.jsx)(d.sG.span,{"data-state":b(r.checked),"data-disabled":r.disabled?"":void 0,...a,ref:s})});y.displayName=g;var v=a.forwardRef(({__scopeSwitch:e,control:s,checked:t,bubbles:r=!0,...n},i)=>{let d=a.useRef(null),p=(0,l.s)(d,i),h=(0,c.Z)(t),m=(0,o.X)(s);return a.useEffect(()=>{let e=d.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==t&&s){let a=new Event("click",{bubbles:r});s.call(e,t),e.dispatchEvent(a)}},[h,t,r]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:t,...n,tabIndex:-1,ref:p,style:{...n.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function b(e){return e?"checked":"unchecked"}v.displayName="SwitchBubbleInput";var N=j,w=y},91645:e=>{"use strict";e.exports=require("net")},93623:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\settings\\\\credentials\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\settings\\credentials\\page.tsx","default")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},98492:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},99270:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[7719,5193,4267,7096,1265,3530,7156,7976,4097,3667,8188,3806,1841,5966,1155],()=>t(23428));module.exports=a})();