(()=>{var e={};e.id=9645,e.ids=[9645],e.modules={2507:(e,r,t)=>{"use strict";t.d(r,{U:()=>i});var s=t(67218);t(79130);var n=t(45934),o=t(44999),a=t(17478);let i=async()=>{let e=await (0,o.cookies)(),r="";return r&&!r.startsWith("http")&&(r=`http://${r}`),(0,n.createServerClient)(r,"",{cookies:{getAll:()=>e.getAll(),setAll(r){try{r.forEach(({name:r,value:t,options:s})=>e.set({name:r,value:t,...s}))}catch(e){}}}})};(0,a.D)([i]),(0,s.A)(i,"7f8bed79c8654f95685745e61906af37f96a086236",null)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},4992:(e,r,t)=>{Promise.resolve().then(t.bind(t,62478))},5527:(e,r,t)=>{Promise.resolve().then(t.bind(t,34806))},6598:(e,r,t)=>{Promise.resolve().then(t.bind(t,77243)),Promise.resolve().then(t.bind(t,74700))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,r,t)=>{"use strict";t.d(r,{cn:()=>o});var s=t(75986),n=t(8974);function o(...e){return(0,n.QP)((0,s.$)(e))}},11576:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687),n=t(35950),o=t(85814),a=t.n(o),i=t(16189);function d({children:e}){let r=(0,i.usePathname)();return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"space-y-6 w-full",children:[(0,s.jsx)(n.w,{className:"border-subtle dark:border-white/10"}),(0,s.jsxs)("div",{className:"flex flex-col space-y-8 lg:flex-row lg:space-x-12 lg:space-y-0 w-full max-w-7xl mx-auto px-4",children:[(0,s.jsx)("aside",{className:"lg:w-1/4 p-1",children:(0,s.jsx)("nav",{className:"flex flex-col space-y-1",children:[{name:"Billing",href:"/settings/billing"},{name:"Usage Logs",href:"/settings/usage-logs"}].map(e=>(0,s.jsx)(a(),{href:e.href,className:`px-3 py-2 rounded-md text-sm font-medium transition-colors ${r===e.href?"bg-accent text-accent-foreground":"text-muted-foreground hover:bg-accent/50 hover:text-accent-foreground"}`,children:e.name},e.href))})}),(0,s.jsx)("div",{className:"flex-1 bg-card-bg dark:bg-background-secondary p-6 rounded-2xl border border-subtle dark:border-white/10 shadow-custom",children:e})]})]})})}},11997:e=>{"use strict";e.exports=require("punycode")},18601:(e,r,t)=>{"use strict";t.d(r,{n:()=>o});var s=t(67218);t(79130);var n=t(2507);async function o(e,r){let t=r.get("name"),s=r.get("accountId"),o=await (0,n.U)(),{error:a}=await o.rpc("update_account",{name:t,account_id:s});if(a)return{message:a.message}}(0,t(17478).D)([o]),(0,s.A)(o,"600eb0b6a0b193dd120e687c96d846748fe54163a6",null)},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26602:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"600eb0b6a0b193dd120e687c96d846748fe54163a6":()=>s.n,"7f8bed79c8654f95685745e61906af37f96a086236":()=>n.U});var s=t(18601),n=t(2507)},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32496:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.default,__next_app__:()=>c,pages:()=>l,routeModule:()=>u,tree:()=>d});var s=t(65239),n=t(48088),o=t(31369),a=t(30893),i={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>a[e]);t.d(r,i);let d={children:["",{children:["(dashboard)",{children:["(personalAccount)",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,42923)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\settings\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,34806)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\settings\\layout.tsx"]}]},{loading:[()=>Promise.resolve().then(t.bind(t,62478)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\loading.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,33532)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,88524))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,93595)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,88524))).default(e)],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\settings\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(dashboard)/(personalAccount)/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},33252:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>n});var s=t(60687);function n(){return(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsx)("div",{className:"w-12 h-12 border-4 border-primary rounded-full border-t-transparent animate-spin"})})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34806:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\(personalAccount)\\\\settings\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\settings\\layout.tsx","default")},42923:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>u});var s=t(37413);t(61120);var n=t(10974);function o({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}var a=t(74700),i=t(77243),d=t(18601);function l({account:e}){return(0,s.jsxs)("form",{className:"animate-in",children:[(0,s.jsx)("input",{type:"hidden",name:"accountId",value:e.account_id}),(0,s.jsxs)("div",{className:"flex flex-col gap-y-4",children:[(0,s.jsxs)("div",{className:"flex flex-col gap-y-2",children:[(0,s.jsx)(i.Label,{htmlFor:"name",className:"text-sm font-medium text-foreground/90",children:"Name"}),(0,s.jsx)(o,{defaultValue:e.name,name:"name",id:"name",placeholder:"Marty Mcfly",required:!0,className:"h-10 rounded-lg border-subtle dark:border-white/10 bg-white dark:bg-background-secondary"})]}),(0,s.jsx)("div",{className:"flex justify-end mt-2",children:(0,s.jsx)(a.SubmitButton,{formAction:d.n,pendingText:"Updating...",className:"rounded-lg bg-primary hover:bg-primary/90 text-white h-10",children:"Save Changes"})})]})]})}var c=t(2507);async function u(){let e=await (0,c.U)(),{data:r}=await e.rpc("get_personal_account");return(0,s.jsx)("div",{children:(0,s.jsx)(l,{account:r})})}},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},58671:(e,r,t)=>{Promise.resolve().then(t.bind(t,11576))},62478:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\(personalAccount)\\\\loading.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\(personalAccount)\\loading.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68544:(e,r,t)=>{Promise.resolve().then(t.bind(t,33252))},70400:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"60120624b62a67d0046abca062aae687cbb5ebc44a":()=>s.vI,"602190608cb4aada86562e5e5997e6bb44fbe95e4e":()=>s.$w,"60836a64bf90333e8dead87703a0c5a32e95fa0f8f":()=>s.gj});var s=t(67834)},74075:e=>{"use strict";e.exports=require("zlib")},74700:(e,r,t)=>{"use strict";t.d(r,{SubmitButton:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call SubmitButton() from the server but SubmitButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\submit-button.tsx","SubmitButton")},76334:(e,r,t)=>{Promise.resolve().then(t.bind(t,80013)),Promise.resolve().then(t.bind(t,44774))},77243:(e,r,t)=>{"use strict";t.d(r,{Label:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call Label() from the server but Label is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\components\\ui\\label.tsx","Label")},77598:e=>{"use strict";e.exports=require("node:crypto")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84297:e=>{"use strict";e.exports=require("async_hooks")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,5193,4267,7096,1265,3530,7156,7976,4257,4017,3667,8188,3806,1841],()=>t(32496));module.exports=s})();