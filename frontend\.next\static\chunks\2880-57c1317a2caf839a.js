"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2880],{2508:(e,r,t)=>{t.d(r,{A:()=>i});var o=t(99090),a=t(90697),n=t(69947);(0,o.GQ)(n.M.connections(),async()=>await i.getConnections(),{staleTime:6e5,gcTime:9e5,refetchOnWindowFocus:!1,refetchOnMount:!1,refetchInterval:!1});let i={async createConnectionToken(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=await a.Hv.post("/pipedream/connection-token",e,{errorContext:{operation:"create connection token",resource:"Pipedream connection"}});if(!r.success){var t;throw Error((null==(t=r.error)?void 0:t.message)||"Failed to create connection token")}return r.data},async getConnections(){let e=await a.Hv.get("/pipedream/connections",{errorContext:{operation:"load connections",resource:"Pipedream connections"}});if(!e.success){var r;throw Error((null==(r=e.error)?void 0:r.message)||"Failed to get connections")}return e.data},async getApps(e,r){let t=new URLSearchParams;e&&t.append("after",e),r&&t.append("q",r);let o=await a.Hv.get("/pipedream/apps".concat(t.toString()?"?".concat(t.toString()):""),{errorContext:{operation:"load apps",resource:"Pipedream apps"}});if(!o.success){var n;throw Error((null==(n=o.error)?void 0:n.message)||"Failed to get apps")}let i=o.data;if(!i.success&&i.error)throw Error(i.error);return i},async getAvailableTools(){let e=await a.Hv.get("/pipedream/mcp/available-tools",{errorContext:{operation:"load available tools",resource:"Pipedream tools"}});if(!e.success){var r;throw Error((null==(r=e.error)?void 0:r.message)||"Failed to get available tools")}return e.data},async healthCheck(){let e=await a.Hv.get("/pipedream/health",{errorContext:{operation:"health check",resource:"Pipedream service"}});if(!e.success){var r;throw Error((null==(r=e.error)?void 0:r.message)||"Health check failed")}return e.data},async discoverMCPServers(e,r){var t,o;let n=await a.Hv.post("/pipedream/mcp/discover-profile",{external_user_id:e,app_slug:r},{errorContext:{operation:"discover MCP servers",resource:"Pipedream MCP"}});if(!n.success)throw Error((null==(o=n.error)?void 0:o.message)||"Failed to discover MCP servers");return(null==(t=n.data)?void 0:t.mcp_servers)||[]},async createProfile(e){let r=await a.Hv.post("/pipedream/profiles",e,{errorContext:{operation:"create profile",resource:"Pipedream credential profile"}});if(!r.success){var t;throw Error((null==(t=r.error)?void 0:t.message)||"Failed to create profile")}return r.data},async getProfiles(e){let r=new URLSearchParams;(null==e?void 0:e.app_slug)&&r.append("app_slug",e.app_slug),(null==e?void 0:e.is_active)!==void 0&&r.append("is_active",e.is_active.toString());let t=await a.Hv.get("/pipedream/profiles".concat(r.toString()?"?".concat(r.toString()):""),{errorContext:{operation:"get profiles",resource:"Pipedream credential profiles"}});if(!t.success){var o;throw Error((null==(o=t.error)?void 0:o.message)||"Failed to get profiles")}return t.data},async getProfile(e){let r=await a.Hv.get("/pipedream/profiles/".concat(e),{errorContext:{operation:"get profile",resource:"Pipedream credential profile"}});if(!r.success){var t;throw Error((null==(t=r.error)?void 0:t.message)||"Failed to get profile")}return r.data},async updateProfile(e,r){let t=await a.Hv.put("/pipedream/profiles/".concat(e),r,{errorContext:{operation:"update profile",resource:"Pipedream credential profile"}});if(!t.success){var o;throw Error((null==(o=t.error)?void 0:o.message)||"Failed to update profile")}return t.data},async deleteProfile(e){let r=await a.Hv.delete("/pipedream/profiles/".concat(e),{errorContext:{operation:"delete profile",resource:"Pipedream credential profile"}});if(!r.success){var t;throw Error((null==(t=r.error)?void 0:t.message)||"Failed to delete profile")}},async connectProfile(e,r){let t=r?"?app=".concat(encodeURIComponent(r)):"",o=await a.Hv.post("/pipedream/profiles/".concat(e,"/connect").concat(t),{},{errorContext:{operation:"connect profile",resource:"Pipedream credential profile"}});if(!o.success){var n;throw Error((null==(n=o.error)?void 0:n.message)||"Failed to connect profile")}return o.data},async getProfileConnections(e){let r=await a.Hv.get("/pipedream/profiles/".concat(e,"/connections"),{errorContext:{operation:"get profile connections",resource:"Pipedream profile connections"}});if(!r.success){var t;throw Error((null==(t=r.error)?void 0:t.message)||"Failed to get profile connections")}return r.data}}},30285:(e,r,t)=>{t.d(r,{$:()=>l,r:()=>s});var o=t(95155);t(12115);var a=t(99708),n=t(74466),i=t(59434);let s=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",node_outline:"bg-transparent border border-primary/10",node_secondary:"px-0 bg-transparent hover:opacity-60",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-lg gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-lg px-6 has-[>svg]:px-4",icon:"size-9",node_secondary:"px-0"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:r,variant:t,size:n,asChild:l=!1,...c}=e,d=l?a.DX:"button";return(0,o.jsx)(d,{"data-slot":"button",className:(0,i.cn)(s({variant:t,size:n,className:r})),...c})}},59434:(e,r,t)=>{t.d(r,{$3:()=>l,Hz:()=>s,W5:()=>c,cn:()=>i});var o=t(52596),a=t(81949),n=t(39688);function i(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.QP)((0,o.$)(r))}let s=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"rgba(180, 180, 180)";if(!e)return r;try{if("string"==typeof e&&e.startsWith("var(")){let r=document.createElement("div");r.style.color=e,document.body.appendChild(r);let t=window.getComputedStyle(r).color;return document.body.removeChild(r),a.formatRGBA(a.parse(t))}return a.formatRGBA(a.parse(e))}catch(e){return console.error("Color parsing failed:",e),r}},l=(e,r)=>e.startsWith("rgb")?a.formatRGBA(a.alpha(a.parse(e),r)):e;function c(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return e.length<=r?e:e.slice(0,r)+"..."}},68856:(e,r,t)=>{t.d(r,{Skeleton:()=>s});var o=t(95155),a=t(11518),n=t.n(a),i=t(59434);function s(e){let{className:r,...t}=e;return(0,o.jsxs)("div",{...t,className:"jsx-1e687dde282a8b11 "+(t&&null!=t.className&&t.className||(0,i.cn)("relative overflow-hidden rounded-md","bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10","background-animate",r)||""),children:[(0,o.jsx)("div",{className:"jsx-1e687dde282a8b11 shimmer-wrapper",children:(0,o.jsx)("div",{className:"jsx-1e687dde282a8b11 shimmer"})}),(0,o.jsx)(n(),{id:"1e687dde282a8b11",children:".background-animate.jsx-1e687dde282a8b11{-webkit-background-size:200%200%;-moz-background-size:200%200%;-o-background-size:200%200%;background-size:200%200%;-webkit-animation:gradientAnimation 1s ease infinite;-moz-animation:gradientAnimation 1s ease infinite;-o-animation:gradientAnimation 1s ease infinite;animation:gradientAnimation 1s ease infinite}@-webkit-keyframes gradientAnimation{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}@-moz-keyframes gradientAnimation{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}@-o-keyframes gradientAnimation{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}@keyframes gradientAnimation{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}.shimmer-wrapper.jsx-1e687dde282a8b11{position:absolute;top:0;left:0;width:100%;height:100%;overflow:hidden}.shimmer.jsx-1e687dde282a8b11{width:50%;height:100%;background:-webkit-linear-gradient(left,rgba(255,255,255,0)0%,rgba(255,255,255,.3)50%,rgba(255,255,255,0)100%);background:-moz-linear-gradient(left,rgba(255,255,255,0)0%,rgba(255,255,255,.3)50%,rgba(255,255,255,0)100%);background:-o-linear-gradient(left,rgba(255,255,255,0)0%,rgba(255,255,255,.3)50%,rgba(255,255,255,0)100%);background:linear-gradient(90deg,rgba(255,255,255,0)0%,rgba(255,255,255,.3)50%,rgba(255,255,255,0)100%);-webkit-animation:shimmerAnimation 1s infinite;-moz-animation:shimmerAnimation 1s infinite;-o-animation:shimmerAnimation 1s infinite;animation:shimmerAnimation 1s infinite;position:absolute;top:0;left:-150%}@-webkit-keyframes shimmerAnimation{to{left:150%}}@-moz-keyframes shimmerAnimation{to{left:150%}}@-o-keyframes shimmerAnimation{to{left:150%}}@keyframes shimmerAnimation{to{left:150%}}"})]})}t(12115)},69947:(e,r,t)=>{t.d(r,{M:()=>o});let o={all:["pipedream"],connections:()=>[...o.all,"connections"],connectionToken:e=>[...o.all,"connection-token",e||"default"],health:()=>[...o.all,"health"],config:()=>[...o.all,"config"],workflows:()=>[...o.all,"workflows"],workflowRuns:e=>[...o.all,"workflow-runs",e],apps:(e,r,t)=>[...o.all,"apps",e,r||"",t||""],appsSearch:(e,r,t)=>[...o.all,"apps","search",e,r,t||""],availableTools:()=>[...o.all,"available-tools"],mcpDiscovery:e=>[...o.all,"mcp-discovery",null==e?void 0:e.app_slug,null==e?void 0:e.oauth_app_id,null==e?void 0:e.custom],profiles:{all:()=>[...o.all,"profiles"],list:e=>{var r;return[...o.profiles.all(),"list",(null==e?void 0:e.app_slug)||"",null!=(r=null==e?void 0:e.is_active)?r:""]},detail:e=>[...o.profiles.all(),"detail",e],connections:e=>[...o.profiles.all(),"connections",e]}}},89340:(e,r,t)=>{t.d(r,{z:()=>s});var o=t(95155),a=t(12115),n=t(59434);let i=a.memo(function(e){let{mainCircleSize:r=210,mainCircleOpacity:t=.24,numCircles:a=8,className:i,...s}=e;return(0,o.jsx)("div",{className:(0,n.cn)("scale-150 pointer-events-none absolute inset-0 select-none [mask-image:linear-gradient(to_bottom,white,transparent)]",i),...s,children:Array.from({length:a},(e,a)=>{let n=r+70*a,i=t-.03*a,s="".concat(.06*a,"s");return(0,o.jsx)("div",{className:"absolute animate-ripple rounded-full border bg-foreground/25 shadow-xl",style:{"--i":a,width:"".concat(n,"px"),height:"".concat(n,"px"),opacity:i,animationDelay:s,borderStyle:"solid",borderWidth:"1px",borderColor:"var(--foreground)",top:"50%",left:"50%",transform:"translate(-50%, -50%) scale(1)"}},a)})})});i.displayName="Ripple";let s=e=>{let{icon:r,children:t}=e;return(0,o.jsxs)("div",{className:"relative overflow-hidden rounded-3xl flex items-center justify-center border bg-background",children:[(0,o.jsx)("div",{className:"relative px-8 py-16 text-center",children:(0,o.jsxs)("div",{className:"mx-auto max-w-3xl space-y-6",children:[(0,o.jsx)("div",{className:"inline-flex items-center justify-center rounded-full bg-muted p-3",children:(0,o.jsx)(r,{className:"h-8 w-8 text-primary"})}),(0,o.jsx)("h1",{className:"text-4xl font-semibold tracking-tight text-foreground",children:t})]})}),(0,o.jsx)(i,{})]})}},90697:(e,r,t)=>{t.d(r,{Hv:()=>l,mI:()=>s});var o=t(52643),a=t(33356);let n="http://localhost:8000/api",i={async request(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{showErrors:t=!0,errorContext:n,timeout:i=3e4,...s}=r;try{let r,l=new AbortController,c=setTimeout(()=>l.abort(),i),d=(0,o.U)(),{data:{session:u}}=await d.auth.getSession(),p={"Content-Type":"application/json",...s.headers};(null==u?void 0:u.access_token)&&(p.Authorization="Bearer ".concat(u.access_token));let g=await fetch(e,{...s,headers:p,signal:l.signal});if(clearTimeout(c),!g.ok){let e=Error("HTTP ".concat(g.status,": ").concat(g.statusText));e.status=g.status,e.response=g;try{let r=await g.json();e.details=r,r.message&&(e.message=r.message)}catch(e){}return t&&(0,a.hS)(e,n),{error:e,success:!1}}let m=g.headers.get("content-type");return{data:(null==m?void 0:m.includes("application/json"))?await g.json():(null==m?void 0:m.includes("text/"))?await g.text():await g.blob(),success:!0}}catch(r){let e=r instanceof Error?r:Error(String(r));return"AbortError"===r.name&&(e.message="Request timeout",e.code="TIMEOUT"),t&&(0,a.nQ)(e,n),{error:e,success:!1}}},get:async function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return i.request(e,{...r,method:"GET"})},post:async function(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return i.request(e,{...t,method:"POST",body:r?JSON.stringify(r):void 0})},put:async function(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return i.request(e,{...t,method:"PUT",body:r?JSON.stringify(r):void 0})},patch:async function(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return i.request(e,{...t,method:"PATCH",body:r?JSON.stringify(r):void 0})},delete:async function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return i.request(e,{...r,method:"DELETE"})},upload:async function(e,r){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{headers:o,...a}=t,n={...o};return delete n["Content-Type"],i.request(e,{...a,method:"POST",body:r,headers:n})}},s={async execute(e,r){try{let{data:t,error:o}=await e();if(o){let e=Error(o.message||"Database error");return e.code=o.code,e.details=o,(0,a.hS)(e,r),{error:e,success:!1}}return{data:t,success:!0}}catch(t){let e=t instanceof Error?t:Error(String(t));return(0,a.hS)(e,r),{error:e,success:!1}}}},l={get:(e,r)=>i.get("".concat(n).concat(e),r),post:(e,r,t)=>i.post("".concat(n).concat(e),r,t),put:(e,r,t)=>i.put("".concat(n).concat(e),r,t),patch:(e,r,t)=>i.patch("".concat(n).concat(e),r,t),delete:(e,r)=>i.delete("".concat(n).concat(e),r),upload:(e,r,t)=>i.upload("".concat(n).concat(e),r,t)}},99090:(e,r,t)=>{t.d(r,{DY:()=>i,GQ:()=>s,Lx:()=>l});var o=t(28755),a=t(5041),n=t(33356);let i=e=>e;function s(e,r,t){return a=>(0,o.I)({queryKey:e,queryFn:r,...t,...a})}function l(e,r){return t=>{let{errorContext:o,...i}=r||{},{errorContext:s,...l}=t||{};return(0,a.n)({mutationFn:e,onError:(e,r,t)=>{var a,c;(null==l?void 0:l.onError)||(null==i?void 0:i.onError)||(0,n.hS)(e,s||o),null==i||null==(a=i.onError)||a.call(i,e,r,t),null==l||null==(c=l.onError)||c.call(l,e,r,t)},...i,...l})}}}}]);