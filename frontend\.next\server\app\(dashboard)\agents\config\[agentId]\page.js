(()=>{var e={};e.id=2593,e.ids=[2593],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21109:(e,t,a)=>{Promise.resolve().then(a.bind(a,51497))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29051:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.d(t,{i:()=>v});var n=a(60687),s=a(43210),o=a(96834),l=a(52581),i=a(12879),d=a(53060),c=a(96260),u=a(19321),m=a(26408),f=a(56367),p=a(92798),h=a(62185),g=a(12392),x=e([d,c]);[d,c]=x.then?(await x)():x;let v=({agent:e})=>{let[t,a]=(0,s.useState)([]),[r,x]=(0,s.useState)(""),[v,b]=(0,s.useState)(null),[w,y]=(0,s.useState)(null),[j,N]=(0,s.useState)("idle"),[C,S]=(0,s.useState)(!1),[_,k]=(0,s.useState)(!1),E=(0,s.useRef)(null),A=(0,s.useRef)(null),{avatar:R,color:P}=e.avatar&&e.avatar_color?{avatar:e.avatar,color:e.avatar_color}:(0,i.Z)(e.agent_id),T=(0,u.u)(),I=(0,f.U)(),$=(0,p.WZ)(),D=(0,p.CV)(),O=()=>{E.current?.scrollIntoView({behavior:"smooth"})};(0,s.useEffect)(()=>{O()},[t]);let M=(0,s.useCallback)(e=>{console.log(`[PREVIEW STREAM] Received message: ID=${e.message_id}, Type=${e.type}`),a(t=>t.some(t=>t.message_id===e.message_id)?t.map(t=>t.message_id===e.message_id?e:t):[...t,e])},[]),F=(0,s.useCallback)(e=>{switch(console.log(`[PREVIEW] Stream status changed: ${e}`),e){case"idle":case"completed":case"stopped":case"agent_not_running":case"error":case"failed":N("idle"),y(null);break;case"connecting":N("connecting");break;case"streaming":N("running")}},[]),q=(0,s.useCallback)(e=>{console.error(`[PREVIEW] Stream error: ${e}`),e.toLowerCase().includes("not found")||e.toLowerCase().includes("agent run is not running")||l.oR.error(`Stream Error: ${e}`)},[]),U=(0,s.useCallback)(()=>{console.log("[PREVIEW] Stream closed")},[]),{status:L,textContent:B,toolCall:z,error:W,agentRunId:V,startStreaming:H,stopStreaming:G}=(0,m.Z)({onMessage:M,onStatusChange:F,onError:q,onClose:U},v,a);(0,s.useEffect)(()=>{w&&w!==V&&v&&(console.log(`[PREVIEW] Starting stream for agentRunId: ${w}, threadId: ${v}`),H(w))},[w,H,V,v]),(0,s.useEffect)(()=>{console.log("[PREVIEW] State update:",{threadId:v,agentRunId:w,currentHookRunId:V,agentStatus:j,streamHookStatus:L,messagesCount:t.length,hasStartedConversation:_})},[v,w,V,j,L,t.length,_]),(0,s.useEffect)(()=>{B&&O()},[B]);let Y=async(t,r)=>{if(t.trim()||A.current?.getPendingFiles().length){S(!0),k(!0);try{let n=A.current?.getPendingFiles()||[],s=new FormData;s.append("prompt",t),s.append("agent_id",e.agent_id),n.forEach((e,t)=>{let a=(0,g.L)(e.name);s.append("files",e,a)}),r?.model_name&&s.append("model_name",r.model_name),s.append("enable_thinking",String(r?.enable_thinking??!1)),s.append("reasoning_effort",r?.reasoning_effort??"low"),s.append("stream",String(r?.stream??!0)),s.append("enable_context_manager",String(r?.enable_context_manager??!1)),console.log("[PREVIEW] Initiating agent...");let o=await T.mutateAsync(s);if(console.log("[PREVIEW] Agent initiated:",o),o.thread_id){if(b(o.thread_id),o.agent_run_id)console.log("[PREVIEW] Setting agent run ID:",o.agent_run_id),y(o.agent_run_id);else{console.log("[PREVIEW] No agent_run_id in result, starting agent manually...");try{let e=await $.mutateAsync({threadId:o.thread_id,options:r});console.log("[PREVIEW] Agent started manually:",e),y(e.agent_run_id)}catch(e){console.error("[PREVIEW] Error starting agent manually:",e),l.oR.error("Failed to start agent")}}let e={message_id:`user-${Date.now()}`,thread_id:o.thread_id,type:"user",is_llm_message:!1,content:t,metadata:"{}",created_at:new Date().toISOString(),updated_at:new Date().toISOString()};a([e])}A.current?.clearPendingFiles(),x("")}catch(e){console.error("[PREVIEW] Error during initiation:",e),e instanceof h.Ey?l.oR.error("Billing limit reached. Please upgrade your plan."):l.oR.error("Failed to start conversation"),k(!1)}finally{S(!1)}}},X=(0,s.useCallback)(async(e,t)=>{if(!e.trim()||!v)return;S(!0);let r={message_id:`temp-${Date.now()}`,thread_id:v,type:"user",is_llm_message:!1,content:e,metadata:"{}",created_at:new Date().toISOString(),updated_at:new Date().toISOString()};a(e=>[...e,r]),x("");try{let n=I.mutateAsync({threadId:v,message:e}),s=$.mutateAsync({threadId:v,options:t}),o=await Promise.allSettled([n,s]);if("rejected"===o[0].status)throw Error(`Failed to send message: ${o[0].reason?.message||o[0].reason}`);if("rejected"===o[1].status){let e=o[1].reason;if(e instanceof h.Ey){l.oR.error("Billing limit reached. Please upgrade your plan."),a(e=>e.filter(e=>e.message_id!==r.message_id));return}throw Error(`Failed to start agent: ${e?.message||e}`)}let i=o[1].value;y(i.agent_run_id)}catch(e){console.error("[PREVIEW] Error sending message:",e),l.oR.error(e instanceof Error?e.message:"Operation failed"),a(e=>e.filter(e=>e.message_id!==r.message_id))}finally{S(!1)}},[v,I,$]),Z=(0,s.useCallback)(async()=>{if(console.log("[PREVIEW] Stopping agent..."),N("idle"),await G(),w)try{await D.mutateAsync(w)}catch(e){console.error("[PREVIEW] Error stopping agent:",e)}},[G,w,D]),J=(0,s.useCallback)((e,t)=>{console.log("[PREVIEW] Tool clicked:",t),l.oR.info(`Tool: ${t} (Preview mode - tool details not available)`)},[]);return(0,n.jsxs)("div",{className:"h-full flex flex-col bg-muted dark:bg-muted/30",children:[(0,n.jsxs)("div",{className:"flex-shrink-0 flex items-center gap-3 p-8",children:[(0,n.jsx)("div",{className:"h-10 w-10 flex items-center justify-center rounded-lg text-lg",style:{backgroundColor:P},children:R}),(0,n.jsx)("div",{className:"flex-1",children:(0,n.jsx)("h3",{className:"font-semibold",children:e.name||"Unnamed Agent"})}),(0,n.jsx)(o.E,{variant:"highlight",className:"text-sm",children:"Preview Mode"})]}),(0,n.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,n.jsxs)("div",{className:"h-full overflow-y-auto scrollbar-hide",children:[(0,n.jsx)(c.u9,{messages:t,streamingTextContent:B,streamingToolCall:z,agentStatus:j,handleToolClick:J,handleOpenFileViewer:()=>{},streamHookStatus:L,isPreviewMode:!0,agentName:e.name,agentAvatar:R,emptyStateComponent:(0,n.jsxs)("div",{className:"flex flex-col items-center text-center text-muted-foreground/80",children:[(0,n.jsx)("div",{className:"flex w-20 aspect-square items-center justify-center rounded-2xl bg-muted-foreground/10 p-4 mb-4",children:(0,n.jsx)("div",{className:"text-4xl",children:R})}),(0,n.jsxs)("p",{className:"w-[60%] text-2xl mb-3",children:["Start conversation with ",(0,n.jsx)("span",{className:"text-primary/80 font-semibold",children:e.name})]}),(0,n.jsx)("p",{className:"w-[70%] text-sm text-muted-foreground/60",children:"Test your agent's configuration and chat back and forth to see how it performs with your current settings, tools, and knowledge base."})]})}),(0,n.jsx)("div",{ref:E})]})}),(0,n.jsx)("div",{className:"flex-shrink-0",children:(0,n.jsx)("div",{className:"p-0 md:p-4 md:px-10",children:(0,n.jsx)(d.V,{ref:A,onSubmit:v?X:Y,loading:C,placeholder:`Message ${e.name||"agent"}...`,value:r,onChange:x,disabled:C,isAgentRunning:"running"===j||"connecting"===j,onStopAgent:Z,agentName:e.name,hideAttachments:!1,bgColor:"bg-muted-foreground/10",selectedAgentId:e.agent_id,onAgentSelect:()=>{l.oR.info("You can only test the agent you are currently configuring")}})})})]})};r()}catch(e){r(e)}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37675:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});let r=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\agents\\\\config\\\\[agentId]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\config\\[agentId]\\page.tsx","default")},45659:(e,t,a)=>{"use strict";a.d(t,{w:()=>d});var r=a(60687),n=a(43210),s=a(34729),o=a(89667),l=a(4780),i=a(90131);let d=({value:e,onSave:t,className:a="",placeholder:d="Click to edit...",multiline:c=!1,minHeight:u="auto"})=>{let[m,f]=(0,n.useState)(!1),[p,h]=(0,n.useState)(e);(0,n.useEffect)(()=>{h(e)},[e]);let g=()=>{t(p),f(!1)},x=()=>{h(e),f(!1)};if(m){let e=c?s.T:o.p;return(0,r.jsx)("div",{className:"space-y-2",children:(0,r.jsx)(e,{value:p,onChange:e=>h(e.target.value),onKeyDown:e=>{"Enter"!==e.key||c?"Escape"===e.key?x():"Enter"===e.key&&e.metaKey&&c&&g():g()},onBlur:g,autoFocus:!0,className:(0,l.cn)("border-none shadow-none px-0 focus-visible:ring-0 bg-transparent",c?"resize-none":"",c&&u?`min-h-[${u}]`:"",a),style:{fontSize:"inherit",fontWeight:"inherit",lineHeight:"inherit",...c&&u?{minHeight:u}:{}}})})}return(0,r.jsxs)("div",{className:(0,l.cn)("group bg-transparent cursor-pointer relative rounded px-2 py-1 -mx-2 -my-1 transition-colors",a),onClick:()=>f(!0),children:[(0,r.jsx)("div",{className:(0,l.cn)(e?"":"text-muted-foreground italic",c&&u?`min-h-[${u}]`:""),style:c&&u?{minHeight:u}:{},children:e||d}),(0,r.jsx)(i.A,{className:"h-3 w-3 opacity-0 group-hover:opacity-50 absolute top-1 right-1 transition-opacity"})]})}},47242:(e,t,a)=>{"use strict";a.d(t,{g:()=>M});var r=a(60687),n=a(43210),s=a(29523),o=a(45583),l=a(62688);let i=(0,l.A)("Store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12a2 2 0 0 1-2-2V7",key:"6c3vgh"}]]);var d=a(24413),c=a(63503),u=a(44493),m=a(56085),f=a(19959),p=a(43649),h=a(84027),g=a(11860),x=a(12700);let v=({mcp:e,index:t,onEdit:a,onRemove:n,onConfigureTools:o})=>{let{data:l=[]}=(0,x.Gx)(e.qualifiedName),i=e.selectedProfileId||e.config?.profile_id,d=l.find(e=>e.profile_id===i),c=e.config&&Object.keys(e.config).length>0,v=!!i&&!!d,b=!v&&!c&&!e.isCustom;return(0,r.jsx)(u.Zp,{className:"p-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center flex-shrink-0",children:(0,r.jsx)(m.A,{className:"h-4 w-4 text-primary"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("div",{className:"flex items-center gap-2 mb-1",children:(0,r.jsx)("div",{className:"font-medium text-sm truncate",children:e.name})}),(0,r.jsxs)("div",{className:"flex items-center gap-3 text-xs text-muted-foreground",children:[(0,r.jsxs)("span",{children:[e.enabledTools?.length||0," tools enabled"]}),v&&(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(f.A,{className:"h-3 w-3 text-green-600"}),(0,r.jsx)("span",{className:"text-green-600 font-medium truncate max-w-24",children:d.profile_name})]}),c&&!v&&(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(f.A,{className:"h-3 w-3 text-green-600"}),(0,r.jsx)("span",{className:"text-green-600 font-medium",children:"Configured"})]}),b&&(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(p.A,{className:"h-3 w-3 text-amber-600"}),(0,r.jsx)("span",{className:"text-amber-600",children:"Needs config"})]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 flex-shrink-0",children:[o&&(0,r.jsx)(s.$,{size:"sm",variant:"ghost",onClick:()=>o(t),title:"Configure tools",children:(0,r.jsx)(h.A,{className:"h-4 w-4"})}),(0,r.jsx)(s.$,{size:"sm",variant:"ghost",onClick:()=>n(t),title:"Remove integration",children:(0,r.jsx)(g.A,{className:"h-4 w-4"})})]})]})})},b=({configuredMCPs:e,onEdit:t,onRemove:a,onConfigureTools:n})=>0===e.length?null:(0,r.jsx)("div",{className:"space-y-2",children:e.map((e,s)=>(0,r.jsx)(v,{mcp:e,index:s,onEdit:t,onRemove:a,onConfigureTools:n},s))});var w=a(80013),y=a(50812),j=a(91821),N=a(14952);let C=(0,l.A)("Wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]);var S=a(93613),_=a(14719),k=a(41862),E=a(42692),A=a(82978),R=a(4780),P=a(79481),T=a(89667);let I=({open:e,onOpenChange:t,onSave:a})=>{let[l,i]=(0,n.useState)("setup"),[u,f]=(0,n.useState)("sse"),[p,h]=(0,n.useState)(""),[g,x]=(0,n.useState)(""),[v,b]=(0,n.useState)(""),[I,$]=(0,n.useState)(!1),[D,O]=(0,n.useState)(null),[M,F]=(0,n.useState)([]),[q,U]=(0,n.useState)(new Set),[L,B]=(0,n.useState)(null),z=async()=>{$(!0),O(null),F([]);try{let e;if("sse"===u||"http"===u){let t=p.trim();if(!t)throw Error("Please enter the connection URL.");if(!v.trim())throw Error("Please enter a name for this connection.");e={url:t},x(v.trim())}let t=(0,P.U)(),{data:{session:a}}=await t.auth.getSession();if(!a)throw Error("You must be logged in to discover tools");let r=await fetch("http://localhost:8000/api/mcp/discover-custom-tools",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a.access_token}`},body:JSON.stringify({type:u,config:e})});if(!r.ok){let e=await r.json();throw Error(e.message||"Failed to connect to the service. Please check your configuration.")}let n=await r.json();if(!n.tools||0===n.tools.length)throw Error("No tools found. Please check your configuration.");n.serverName&&x(n.serverName),n.processedConfig&&B(n.processedConfig),F(n.tools),U(new Set(n.tools.map(e=>e.name))),i("tools")}catch(e){O(e.message)}finally{$(!1)}},W=()=>{if(0===M.length||0===q.size)return void O("Please select at least one tool to continue.");if(!g.trim())return void O("Please provide a name for this connection.");try{let e={url:p.trim()};a({name:g,type:u,config:e,enabledTools:Array.from(q),selectedProfileId:void 0}),h(""),b(""),F([]),U(new Set),x(""),B(null),O(null),i("setup"),t(!1)}catch(e){O("Invalid configuration format.")}},V=e=>{let t=new Set(q);t.has(e)?t.delete(e):t.add(e),U(t)},H=()=>{h(""),b(""),F([]),U(new Set),x(""),B(null),O(null),i("setup")};return(0,r.jsx)(c.lG,{open:e,onOpenChange:e=>{t(e),e||H()},children:(0,r.jsxs)(c.Cf,{className:"max-w-4xl max-h-[85vh] overflow-hidden flex flex-col",children:[(0,r.jsxs)(c.c7,{children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center",children:(0,r.jsx)(o.A,{className:"h-4 w-4 text-primary"})}),(0,r.jsx)(c.L3,{children:"Connect New Service"})]}),(0,r.jsx)(c.rr,{children:"setup"===l?"Connect to external services to expand your capabilities with new tools and integrations.":"Choose which tools you'd like to enable from this service connection."}),(0,r.jsxs)("div",{className:"flex items-center gap-2 pt-2",children:[(0,r.jsxs)("div",{className:(0,R.cn)("flex items-center gap-2 text-sm font-medium","setup"===l?"text-primary":"text-muted-foreground"),children:[(0,r.jsx)("div",{className:(0,R.cn)("w-6 h-6 rounded-full flex items-center justify-center text-xs","setup"===l?"bg-primary text-primary-foreground":"bg-muted text-muted-foreground"),children:"1"}),"Setup Connection"]}),(0,r.jsx)(N.A,{className:"h-4 w-4 text-muted-foreground"}),(0,r.jsxs)("div",{className:(0,R.cn)("flex items-center gap-2 text-sm font-medium","tools"===l?"text-primary":"text-muted-foreground"),children:[(0,r.jsx)("div",{className:(0,R.cn)("w-6 h-6 rounded-full flex items-center justify-center text-xs","tools"===l?"bg-primary text-primary-foreground":"bg-muted-foreground/20 text-muted-foreground"),children:"2"}),"Select Tools"]})]})]}),(0,r.jsx)("div",{className:"flex-1 overflow-hidden flex flex-col",children:"setup"===l?(0,r.jsxs)("div",{className:"space-y-6 p-1 flex-1",children:[(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(w.Label,{className:"text-base font-medium",children:"How would you like to connect?"}),(0,r.jsxs)(y.z,{value:u,onValueChange:e=>f(e),className:"grid grid-cols-1 gap-3",children:[(0,r.jsxs)("div",{className:(0,R.cn)("flex items-start space-x-3 p-4 rounded-lg border cursor-pointer transition-all hover:bg-muted/50","http"===u?"border-primary bg-primary/5":"border-border"),children:[(0,r.jsx)(y.C,{value:"http",id:"http",className:"mt-1"}),(0,r.jsxs)("div",{className:"flex-1 space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(d.A,{className:"h-4 w-4 text-primary"}),(0,r.jsx)(w.Label,{htmlFor:"http",className:"text-base font-medium cursor-pointer",children:"Streamable HTTP"})]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Standard streamable HTTP connection"})]})]}),(0,r.jsxs)("div",{className:(0,R.cn)("flex items-start space-x-3 p-4 rounded-lg border cursor-pointer transition-all hover:bg-muted/50","sse"===u?"border-primary bg-primary/5":"border-border"),children:[(0,r.jsx)(y.C,{value:"sse",id:"sse",className:"mt-1"}),(0,r.jsxs)("div",{className:"flex-1 space-y-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(C,{className:"h-4 w-4 text-primary"}),(0,r.jsx)(w.Label,{htmlFor:"sse",className:"text-base font-medium cursor-pointer",children:"SSE (Server-Sent Events)"})]}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Real-time connection using Server-Sent Events for streaming updates"})]})]})]})]})}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(w.Label,{htmlFor:"serverName",className:"text-base font-medium",children:"Connection Name"}),(0,r.jsx)("input",{id:"serverName",type:"text",placeholder:"e.g., Gmail, Slack, Customer Support Tools",value:v,onChange:e=>b(e.target.value),className:"w-full px-4 py-3 border border-input bg-background rounded-lg text-base focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Give this connection a memorable name"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(w.Label,{htmlFor:"config",className:"text-base font-medium",children:"Connection URL"}),(0,r.jsx)(T.p,{id:"config",type:"url",placeholder:{http:"https://server.example.com/mcp",sse:"https://mcp.composio.dev/partner/composio/gmail/sse?customerId=YOUR_CUSTOMER_ID"}[u],value:p,onChange:e=>h(e.target.value),className:"w-full px-4 py-3 border border-input bg-muted rounded-lg text-base focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent font-mono"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Paste the complete connection URL provided by your service"})]})]}),D&&(0,r.jsxs)(j.Fc,{variant:"destructive",children:[(0,r.jsx)(S.A,{className:"h-4 w-4"}),(0,r.jsx)(j.TN,{children:D})]})]}):"tools"===l?(0,r.jsxs)("div",{className:"space-y-6 p-1 flex-1 flex flex-col",children:[(0,r.jsxs)(j.Fc,{className:"border-green-200 bg-green-50 text-green-800",children:[(0,r.jsx)(_.A,{className:"h-5 w-5 text-green-600"}),(0,r.jsxs)("div",{className:"ml-2",children:[(0,r.jsx)("h3",{className:"font-medium text-green-900 mb-1",children:"Connection Successful!"}),(0,r.jsxs)("p",{className:"text-sm text-green-700",children:["Found ",M.length," available tools from ",(0,r.jsx)("strong",{children:g})]})]})]}),(0,r.jsxs)("div",{className:"space-y-4 flex-1 flex flex-col",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-base font-medium",children:"Available Tools"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground",children:"Select the tools you want to enable"})]}),(0,r.jsx)(s.$,{variant:"outline",size:"sm",onClick:()=>{q.size===M.length?U(new Set):U(new Set(M.map(e=>e.name)))},children:q.size===M.length?"Deselect All":"Select All"})]}),(0,r.jsx)("div",{className:"flex-1 min-h-0",children:(0,r.jsx)(E.F,{className:"h-[400px] border border-border rounded-lg",children:(0,r.jsx)("div",{className:"space-y-3 p-4",children:M.map(e=>(0,r.jsxs)("div",{className:(0,R.cn)("flex items-start space-x-3 p-4 rounded-lg border transition-all cursor-pointer hover:bg-muted/50",q.has(e.name)?"border-primary bg-primary/5":"border-border"),onClick:()=>V(e.name),children:[(0,r.jsx)(A.S,{id:e.name,checked:q.has(e.name),onCheckedChange:()=>V(e.name),className:"mt-1"}),(0,r.jsxs)("div",{className:"flex-1 space-y-2 min-w-0",children:[(0,r.jsx)(w.Label,{htmlFor:e.name,className:"text-base font-medium cursor-pointer block",children:e.name.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase())}),e.description&&(0,r.jsx)("p",{className:"text-sm text-muted-foreground leading-relaxed",children:e.description})]})]},e.name))})})})]}),D&&(0,r.jsxs)(j.Fc,{variant:"destructive",children:[(0,r.jsx)(S.A,{className:"h-4 w-4"}),(0,r.jsx)(j.TN,{children:D})]})]}):null}),(0,r.jsx)(c.Es,{className:"flex-shrink-0 pt-4",children:"tools"===l?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(s.$,{variant:"outline",onClick:()=>{"tools"===l&&i("setup"),O(null)},children:"Back"}),(0,r.jsx)(s.$,{variant:"outline",onClick:()=>t(!1),children:"Cancel"}),(0,r.jsxs)(s.$,{onClick:()=>{if(0===q.size)return void O("Please select at least one tool to continue.");O(null),W()},disabled:0===q.size,children:["Add Connection (",q.size," tools)"]})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(s.$,{variant:"outline",onClick:()=>t(!1),children:"Cancel"}),(0,r.jsx)(s.$,{onClick:z,disabled:!p.trim()||!v.trim()||I,children:I?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(k.A,{className:"h-5 w-5 animate-spin"}),"Discovering tools..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.A,{className:"h-5 w-5"}),"Connect"]})})]})})]})})};var $=a(11155),D=a(52823);let O=({configuredMCPs:e,onConfigurationChange:t,agentId:a})=>{let[l,u]=(0,n.useState)(!1),[m,f]=(0,n.useState)(!1),[p,h]=(0,n.useState)(null),[g,x]=(0,n.useState)(!1),[v,w]=(0,n.useState)(!1),[y,j]=(0,n.useState)(null),[N,C]=(0,n.useState)(a);return(0,n.useEffect)(()=>{C(a)},[a]),(0,r.jsxs)("div",{className:"h-full flex flex-col",children:[(0,r.jsxs)("div",{className:"flex-1 overflow-y-auto",children:[0===e.length&&(0,r.jsxs)("div",{className:"text-center py-12 px-6 bg-muted/30 rounded-xl border-2 border-dashed border-border",children:[(0,r.jsx)("div",{className:"mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-4",children:(0,r.jsx)(o.A,{className:"h-6 w-6 text-muted-foreground"})}),(0,r.jsx)("h4",{className:"text-sm font-medium text-foreground mb-2",children:"No integrations configured"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mb-6 max-w-sm mx-auto",children:"Browse the app registry to connect your apps through Pipedream or add custom MCP servers"}),(0,r.jsxs)("div",{className:"flex gap-2 justify-center",children:[(0,r.jsxs)(s.$,{onClick:()=>f(!0),variant:"default",children:[(0,r.jsx)(i,{className:"h-4 w-4"}),"Browse Apps"]}),(0,r.jsxs)(s.$,{onClick:()=>u(!0),variant:"outline",children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),"Custom MCP"]})]})]}),e.length>0&&(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsxs)("div",{className:"bg-card rounded-xl border border-border overflow-hidden",children:[(0,r.jsx)("div",{className:"px-6 py-4 border-b border-border bg-muted/30",children:(0,r.jsx)("h4",{className:"text-sm font-medium text-foreground",children:"Configured Integrations"})}),(0,r.jsx)("div",{className:"p-2 divide-y divide-border",children:(0,r.jsx)(b,{configuredMCPs:e,onEdit:t=>{e[t].customType,h(t),u(!0)},onRemove:a=>{let r=[...e];r.splice(a,1),t(r)},onConfigureTools:t=>{let a=e[t];j(a),"pipedream"===a.customType?a.selectedProfileId||a.config?.profile_id?x(!0):console.warn("Pipedream MCP has no profile_id:",a):w(!0)}})})]})})]}),e.length>0&&(0,r.jsx)("div",{className:"flex-shrink-0 pt-4",children:(0,r.jsxs)("div",{className:"flex gap-2 justify-center",children:[(0,r.jsxs)(s.$,{onClick:()=>f(!0),variant:"default",children:[(0,r.jsx)(i,{className:"h-4 w-4"}),"Browse Apps"]}),(0,r.jsxs)(s.$,{onClick:()=>u(!0),variant:"outline",children:[(0,r.jsx)(d.A,{className:"h-4 w-4"}),"Custom MCP"]})]})}),(0,r.jsx)(c.lG,{open:m,onOpenChange:f,children:(0,r.jsxs)(c.Cf,{className:"p-0 max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,r.jsx)(c.c7,{className:"sr-only",children:(0,r.jsx)(c.L3,{children:"Select Integration"})}),(0,r.jsx)($.Z,{showAgentSelector:!1,selectedAgentId:N,onAgentChange:e=>{C(e)},onToolsSelected:(a,r,n,s)=>{let o={name:n,qualifiedName:`pipedream_${s}_${a}`,config:{url:"https://remote.mcp.pipedream.net",headers:{"x-pd-app-slug":s},profile_id:a},enabledTools:r,isCustom:!0,customType:"pipedream",selectedProfileId:a};t([...e.filter(e=>"pipedream"!==e.customType||e.selectedProfileId!==a),o]),f(!1)}})]})}),(0,r.jsx)(I,{open:l,onOpenChange:u,onSave:a=>{t([...e,{name:a.name,qualifiedName:`custom_${a.type}_${Date.now()}`,config:a.config,enabledTools:a.enabledTools,selectedProfileId:a.selectedProfileId,isCustom:!0,customType:a.type}])}}),y&&"pipedream"===y.customType&&(y.selectedProfileId||y.config?.profile_id)&&(0,r.jsx)(D.n,{mode:"pipedream",agentId:N,profileId:y.selectedProfileId||y.config?.profile_id,appName:y.name,open:g,onOpenChange:x,onToolsUpdate:a=>{y&&(t(e.map(e=>e===y?{...e,enabledTools:a}:e)),x(!1),j(null))}}),y&&"pipedream"!==y.customType&&(0,r.jsx)(D.n,{mode:"custom",agentId:N,mcpConfig:y.config,mcpName:y.name,open:v,onOpenChange:w,onToolsUpdate:a=>{y&&(t(e.map(e=>e===y?{...e,enabledTools:a}:e)),w(!1),j(null))}})]})},M=({configuredMCPs:e,customMCPs:t,onMCPChange:a,agentId:n})=>{let s=[...e||[],...(t||[]).map(e=>({name:e.name,qualifiedName:`custom_${e.type||e.customType}_${e.name.replace(" ","_").toLowerCase()}`,config:e.config,enabledTools:e.enabledTools,isCustom:!0,customType:e.type||e.customType}))];return(0,r.jsx)(O,{configuredMCPs:s,onConfigurationChange:e=>{a({configured_mcps:e.filter(e=>!e.isCustom),custom_mcps:e.filter(e=>e.isCustom).map(e=>({name:e.name,type:e.customType,customType:e.customType,config:e.config,enabledTools:e.enabledTools}))})},agentId:n})}},48091:(e,t,a)=>{"use strict";let r;a.d(t,{_s:()=>F,zj:()=>B,BE:()=>z,gk:()=>W,Uz:()=>q});var n=a(60687),s=a(43210),o=a(26134);let l=s.createContext({drawerRef:{current:null},overlayRef:{current:null},onPress:()=>{},onRelease:()=>{},onDrag:()=>{},onNestedDrag:()=>{},onNestedOpenChange:()=>{},onNestedRelease:()=>{},openProp:void 0,dismissible:!1,isOpen:!1,isDragging:!1,keyboardIsOpen:{current:!1},snapPointsOffset:null,snapPoints:null,handleOnly:!1,modal:!1,shouldFade:!1,activeSnapPoint:null,onOpenChange:()=>{},setActiveSnapPoint:()=>{},closeDrawer:()=>{},direction:"bottom",shouldAnimate:{current:!0},shouldScaleBackground:!1,setBackgroundColorOnScale:!0,noBodyStyles:!1,container:null,autoFocus:!1}),i=()=>{let e=s.useContext(l);if(!e)throw Error("useDrawerContext must be used within a Drawer.Root");return e};function d(){return/^((?!chrome|android).)*safari/i.test(navigator.userAgent)}function c(){return u(/^iPhone/)||u(/^iPad/)||u(/^Mac/)&&navigator.maxTouchPoints>1}function u(e){}!function(e){if(!e||"undefined"==typeof document)return;let t=document.head||document.getElementsByTagName("head")[0],a=document.createElement("style");a.type="text/css",t.appendChild(a),a.styleSheet?a.styleSheet.cssText=e:a.appendChild(document.createTextNode(e))}("[data-vaul-drawer]{touch-action:none;will-change:transform;transition:transform .5s cubic-bezier(.32, .72, 0, 1);animation-duration:.5s;animation-timing-function:cubic-bezier(0.32,0.72,0,1)}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=bottom][data-state=open]{animation-name:slideFromBottom}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=bottom][data-state=closed]{animation-name:slideToBottom}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=top][data-state=open]{animation-name:slideFromTop}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=top][data-state=closed]{animation-name:slideToTop}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=left][data-state=open]{animation-name:slideFromLeft}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=left][data-state=closed]{animation-name:slideToLeft}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=right][data-state=open]{animation-name:slideFromRight}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=right][data-state=closed]{animation-name:slideToRight}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=bottom]{transform:translate3d(0,var(--initial-transform,100%),0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=top]{transform:translate3d(0,calc(var(--initial-transform,100%) * -1),0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=left]{transform:translate3d(calc(var(--initial-transform,100%) * -1),0,0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=right]{transform:translate3d(var(--initial-transform,100%),0,0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=top]{transform:translate3d(0,var(--snap-point-height,0),0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=bottom]{transform:translate3d(0,var(--snap-point-height,0),0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=left]{transform:translate3d(var(--snap-point-height,0),0,0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=right]{transform:translate3d(var(--snap-point-height,0),0,0)}[data-vaul-overlay][data-vaul-snap-points=false]{animation-duration:.5s;animation-timing-function:cubic-bezier(0.32,0.72,0,1)}[data-vaul-overlay][data-vaul-snap-points=false][data-state=open]{animation-name:fadeIn}[data-vaul-overlay][data-state=closed]{animation-name:fadeOut}[data-vaul-animate=false]{animation:none!important}[data-vaul-overlay][data-vaul-snap-points=true]{opacity:0;transition:opacity .5s cubic-bezier(.32, .72, 0, 1)}[data-vaul-overlay][data-vaul-snap-points=true]{opacity:1}[data-vaul-drawer]:not([data-vaul-custom-container=true])::after{content:'';position:absolute;background:inherit;background-color:inherit}[data-vaul-drawer][data-vaul-drawer-direction=top]::after{top:initial;bottom:100%;left:0;right:0;height:200%}[data-vaul-drawer][data-vaul-drawer-direction=bottom]::after{top:100%;bottom:initial;left:0;right:0;height:200%}[data-vaul-drawer][data-vaul-drawer-direction=left]::after{left:initial;right:100%;top:0;bottom:0;width:200%}[data-vaul-drawer][data-vaul-drawer-direction=right]::after{left:100%;right:initial;top:0;bottom:0;width:200%}[data-vaul-overlay][data-vaul-snap-points=true]:not([data-vaul-snap-points-overlay=true]):not(\n[data-state=closed]\n){opacity:0}[data-vaul-overlay][data-vaul-snap-points-overlay=true]{opacity:1}[data-vaul-handle]{display:block;position:relative;opacity:.7;background:#e2e2e4;margin-left:auto;margin-right:auto;height:5px;width:32px;border-radius:1rem;touch-action:pan-y}[data-vaul-handle]:active,[data-vaul-handle]:hover{opacity:1}[data-vaul-handle-hitarea]{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:max(100%,2.75rem);height:max(100%,2.75rem);touch-action:inherit}@media (hover:hover) and (pointer:fine){[data-vaul-drawer]{user-select:none}}@media (pointer:fine){[data-vaul-handle-hitarea]:{width:100%;height:100%}}@keyframes fadeIn{from{opacity:0}to{opacity:1}}@keyframes fadeOut{to{opacity:0}}@keyframes slideFromBottom{from{transform:translate3d(0,var(--initial-transform,100%),0)}to{transform:translate3d(0,0,0)}}@keyframes slideToBottom{to{transform:translate3d(0,var(--initial-transform,100%),0)}}@keyframes slideFromTop{from{transform:translate3d(0,calc(var(--initial-transform,100%) * -1),0)}to{transform:translate3d(0,0,0)}}@keyframes slideToTop{to{transform:translate3d(0,calc(var(--initial-transform,100%) * -1),0)}}@keyframes slideFromLeft{from{transform:translate3d(calc(var(--initial-transform,100%) * -1),0,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToLeft{to{transform:translate3d(calc(var(--initial-transform,100%) * -1),0,0)}}@keyframes slideFromRight{from{transform:translate3d(var(--initial-transform,100%),0,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToRight{to{transform:translate3d(var(--initial-transform,100%),0,0)}}");let m=s.useEffect;function f(...e){return(...t)=>{for(let a of e)"function"==typeof a&&a(...t)}}let p="undefined"!=typeof document&&window.visualViewport;function h(e){let t=window.getComputedStyle(e);return/(auto|scroll)/.test(t.overflow+t.overflowX+t.overflowY)}function g(e){for(h(e)&&(e=e.parentElement);e&&!h(e);)e=e.parentElement;return e||document.scrollingElement||document.documentElement}let x=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]),v=0;function b(e,t,a,r){return e.addEventListener(t,a,r),()=>{e.removeEventListener(t,a,r)}}function w(e){let t=document.scrollingElement||document.documentElement;for(;e&&e!==t;){let t=g(e);if(t!==document.documentElement&&t!==document.body&&t!==e){let a=t.getBoundingClientRect().top,r=e.getBoundingClientRect().top;e.getBoundingClientRect().bottom>t.getBoundingClientRect().bottom+24&&(t.scrollTop+=r-a)}e=t.parentElement}}function y(e){return e instanceof HTMLInputElement&&!x.has(e.type)||e instanceof HTMLTextAreaElement||e instanceof HTMLElement&&e.isContentEditable}function j(...e){return s.useCallback(function(...e){return t=>e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}(...e),e)}let N=new WeakMap;function C(e,t,a=!1){if(!e||!(e instanceof HTMLElement))return;let r={};Object.entries(t).forEach(([t,a])=>{if(t.startsWith("--"))return void e.style.setProperty(t,a);r[t]=e.style[t],e.style[t]=a}),a||N.set(e,r)}let S=e=>{switch(e){case"top":case"bottom":return!0;case"left":case"right":return!1;default:return e}};function _(e,t){if(!e)return null;let a=window.getComputedStyle(e),r=a.transform||a.webkitTransform||a.mozTransform,n=r.match(/^matrix3d\((.+)\)$/);return n?parseFloat(n[1].split(", ")[S(t)?13:12]):(n=r.match(/^matrix\((.+)\)$/))?parseFloat(n[1].split(", ")[S(t)?5:4]):null}let k={DURATION:.5,EASE:[.32,.72,0,1]},E="vaul-dragging";function A(e){let t=s.useRef(e);return s.useMemo(()=>(...e)=>null==t.current?void 0:t.current.call(t,...e),[])}function R({prop:e,defaultProp:t,onChange:a=()=>{}}){let[r,n]=function({defaultProp:e,onChange:t}){let a=s.useState(e),[r]=a;return s.useRef(r),A(t),a}({defaultProp:t,onChange:a}),o=void 0!==e,l=o?e:r,i=A(a);return[l,s.useCallback(t=>{if(o){let a="function"==typeof t?t(e):t;a!==e&&i(a)}else n(t)},[o,e,n,i])]}let P=null;function T({open:e,onOpenChange:t,children:a,onDrag:n,onRelease:i,snapPoints:u,shouldScaleBackground:h=!1,setBackgroundColorOnScale:x=!0,closeThreshold:j=.25,scrollLockTimeout:N=100,dismissible:A=!0,handleOnly:T=!1,fadeFromIndex:I=u&&u.length-1,activeSnapPoint:$,setActiveSnapPoint:D,fixed:O,modal:M=!0,onClose:F,nested:q,noBodyStyles:U=!1,direction:L="bottom",defaultOpen:B=!1,disablePreventScroll:z=!0,snapToSequentialPoint:W=!1,preventScrollRestoration:V=!1,repositionInputs:H=!0,onAnimationEnd:G,container:Y,autoFocus:X=!1}){var Z,J;let[K=!1,Q]=R({defaultProp:B,prop:e,onChange:e=>{null==t||t(e),e||q||eE(),setTimeout(()=>{null==G||G(e)},1e3*k.DURATION),e||(document.body.style.pointerEvents="auto")}}),[ee,et]=s.useState(!1),[ea,er]=s.useState(!1),[en,es]=s.useState(!1),eo=s.useRef(null),el=s.useRef(null),ei=s.useRef(null),ed=s.useRef(null),ec=s.useRef(null),eu=s.useRef(!1),em=s.useRef(null),ef=s.useRef(0),ep=s.useRef(!1),eh=s.useRef(!B);s.useRef(0);let eg=s.useRef(null),ex=s.useRef((null==(Z=eg.current)?void 0:Z.getBoundingClientRect().height)||0),ev=s.useRef((null==(J=eg.current)?void 0:J.getBoundingClientRect().width)||0);s.useRef(0);let eb=s.useCallback(e=>{u&&e===eC.length-1&&(el.current=new Date)},[]),{activeSnapPoint:ew,activeSnapPointIndex:ey,setActiveSnapPoint:ej,onRelease:eN,snapPointsOffset:eC,onDrag:eS,shouldFade:e_,getPercentageDragged:ek}=function({activeSnapPointProp:e,setActiveSnapPointProp:t,snapPoints:a,drawerRef:r,overlayRef:n,fadeFromIndex:o,onSnapPointChange:l,direction:i="bottom",container:d,snapToSequentialPoint:c}){let[u,m]=R({prop:e,defaultProp:null==a?void 0:a[0],onChange:t}),[f,p]=s.useState(void 0),h=s.useMemo(()=>u===(null==a?void 0:a[a.length-1])||null,[a,u]),g=s.useMemo(()=>{var e;return null!=(e=null==a?void 0:a.findIndex(e=>e===u))?e:null},[a,u]),x=a&&a.length>0&&(o||0===o)&&!Number.isNaN(o)&&a[o]===u||!a,v=s.useMemo(()=>{var e;let t=d?{width:d.getBoundingClientRect().width,height:d.getBoundingClientRect().height}:{width:0,height:0};return null!=(e=null==a?void 0:a.map(e=>{let a="string"==typeof e,r=0;if(a&&(r=parseInt(e,10)),S(i)){let n=a?r:f?e*t.height:0;return f?"bottom"===i?t.height-n:-t.height+n:n}let n=a?r:f?e*t.width:0;return f?"right"===i?t.width-n:-t.width+n:n}))?e:[]},[a,f,d]),b=s.useMemo(()=>null!==g?null==v?void 0:v[g]:null,[v,g]),w=s.useCallback(e=>{var t;let s=null!=(t=null==v?void 0:v.findIndex(t=>t===e))?t:null;l(s),C(r.current,{transition:`transform ${k.DURATION}s cubic-bezier(${k.EASE.join(",")})`,transform:S(i)?`translate3d(0, ${e}px, 0)`:`translate3d(${e}px, 0, 0)`}),v&&s!==v.length-1&&void 0!==o&&s!==o&&s<o?C(n.current,{transition:`opacity ${k.DURATION}s cubic-bezier(${k.EASE.join(",")})`,opacity:"0"}):C(n.current,{transition:`opacity ${k.DURATION}s cubic-bezier(${k.EASE.join(",")})`,opacity:"1"}),m(null==a?void 0:a[Math.max(s,0)])},[r.current,a,v,o,n,m]);return{isLastSnapPoint:h,activeSnapPoint:u,shouldFade:x,getPercentageDragged:function(e,t){if(!a||"number"!=typeof g||!v||void 0===o)return null;let r=g===o-1;if(g>=o&&t)return 0;if(r&&!t)return 1;if(!x&&!r)return null;let n=r?g+1:g-1,s=e/Math.abs(r?v[n]-v[n-1]:v[n+1]-v[n]);return r?1-s:s},setActiveSnapPoint:m,activeSnapPointIndex:g,onRelease:function({draggedDistance:e,closeDrawer:t,velocity:r,dismissible:s}){if(void 0===o)return;let l="bottom"===i||"right"===i?(null!=b?b:0)-e:(null!=b?b:0)+e,d=g===o-1,u=0===g,m=e>0;if(d&&C(n.current,{transition:`opacity ${k.DURATION}s cubic-bezier(${k.EASE.join(",")})`}),!c&&r>2&&!m)return void(s?t():w(v[0]));if(!c&&r>2&&m&&v&&a)return void w(v[a.length-1]);let f=null==v?void 0:v.reduce((e,t)=>"number"!=typeof e||"number"!=typeof t?e:Math.abs(t-l)<Math.abs(e-l)?t:e),p=S(i)?window.innerHeight:window.innerWidth;if(r>.4&&Math.abs(e)<.4*p){let e=m?1:-1;return e>0&&h&&a?void w(v[a.length-1]):void(u&&e<0&&s&&t(),null===g||w(v[g+e]))}w(f)},onDrag:function({draggedDistance:e}){if(null===b)return;let t="bottom"===i||"right"===i?b-e:b+e;("bottom"!==i&&"right"!==i||!(t<v[v.length-1]))&&(("top"===i||"left"===i)&&t>v[v.length-1]||C(r.current,{transform:S(i)?`translate3d(0, ${t}px, 0)`:`translate3d(${t}px, 0, 0)`}))},snapPointsOffset:v}}({snapPoints:u,activeSnapPointProp:$,setActiveSnapPointProp:D,drawerRef:eg,fadeFromIndex:I,overlayRef:eo,onSnapPointChange:eb,direction:L,container:Y,snapToSequentialPoint:W});!function(e={}){let{isDisabled:t}=e;m(()=>{if(!t){var e,a,n;let t,s,o,l,i,d,u;return 1==++v&&c()&&(o=0,l=window.pageXOffset,i=window.pageYOffset,d=f((e=document.documentElement,a="paddingRight",n=`${window.innerWidth-document.documentElement.clientWidth}px`,t=e.style[a],e.style[a]=n,()=>{e.style[a]=t})),window.scrollTo(0,0),u=f(b(document,"touchstart",e=>{((s=g(e.target))!==document.documentElement||s!==document.body)&&(o=e.changedTouches[0].pageY)},{passive:!1,capture:!0}),b(document,"touchmove",e=>{if(!s||s===document.documentElement||s===document.body)return void e.preventDefault();let t=e.changedTouches[0].pageY,a=s.scrollTop,r=s.scrollHeight-s.clientHeight;0!==r&&((a<=0&&t>o||a>=r&&t<o)&&e.preventDefault(),o=t)},{passive:!1,capture:!0}),b(document,"touchend",e=>{let t=e.target;y(t)&&t!==document.activeElement&&(e.preventDefault(),t.style.transform="translateY(-2000px)",t.focus(),requestAnimationFrame(()=>{t.style.transform=""}))},{passive:!1,capture:!0}),b(document,"focus",e=>{let t=e.target;y(t)&&(t.style.transform="translateY(-2000px)",requestAnimationFrame(()=>{t.style.transform="",p&&(p.height<window.innerHeight?requestAnimationFrame(()=>{w(t)}):p.addEventListener("resize",()=>w(t),{once:!0}))}))},!0),b(window,"scroll",()=>{window.scrollTo(0,0)})),r=()=>{d(),u(),window.scrollTo(l,i)}),()=>{0==--v&&(null==r||r())}}},[t])}({isDisabled:!K||ea||!M||en||!ee||!H||!z});let{restorePositionSetting:eE}=function({isOpen:e,modal:t,nested:a,hasBeenOpened:r,preventScrollRestoration:n,noBodyStyles:o}){let[l,i]=s.useState(()=>""),c=s.useRef(0);return s.useCallback(()=>{if(d()&&null===P&&e&&!o){P={position:document.body.style.position,top:document.body.style.top,left:document.body.style.left,height:document.body.style.height,right:"unset"};let{scrollX:e,innerHeight:t}=window;document.body.style.setProperty("position","fixed","important"),Object.assign(document.body.style,{top:`${-c.current}px`,left:`${-e}px`,right:"0px",height:"auto"}),window.setTimeout(()=>window.requestAnimationFrame(()=>{let e=t-window.innerHeight;e&&c.current>=t&&(document.body.style.top=`${-(c.current+e)}px`)}),300)}},[e]),{restorePositionSetting:s.useCallback(()=>{if(d()&&null!==P&&!o){let e=-parseInt(document.body.style.top,10),t=-parseInt(document.body.style.left,10);Object.assign(document.body.style,P),window.requestAnimationFrame(()=>{if(n&&l!==window.location.href)return void i(window.location.href);window.scrollTo(t,e)}),P=null}},[l])}}({isOpen:K,modal:M,nested:null!=q&&q,hasBeenOpened:ee,preventScrollRestoration:V,noBodyStyles:U});function eA(){return(window.innerWidth-26)/window.innerWidth}function eR(e,t){var a;let r=e,n=null==(a=window.getSelection())?void 0:a.toString(),s=eg.current?_(eg.current,L):null,o=new Date;if("SELECT"===r.tagName||r.hasAttribute("data-vaul-no-drag")||r.closest("[data-vaul-no-drag]"))return!1;if("right"===L||"left"===L)return!0;if(el.current&&o.getTime()-el.current.getTime()<500)return!1;if(null!==s&&("bottom"===L?s>0:s<0))return!0;if(n&&n.length>0)return!1;if(ec.current&&o.getTime()-ec.current.getTime()<N&&0===s||t)return ec.current=o,!1;for(;r;){if(r.scrollHeight>r.clientHeight){if(0!==r.scrollTop)return ec.current=new Date,!1;if("dialog"===r.getAttribute("role"))break}r=r.parentNode}return!0}function eP(e){ea&&eg.current&&(eg.current.classList.remove(E),eu.current=!1,er(!1),ed.current=new Date),null==F||F(),e||Q(!1),setTimeout(()=>{u&&ej(u[0])},1e3*k.DURATION)}function eT(){if(!eg.current)return;let e=document.querySelector("[data-vaul-drawer-wrapper]"),t=_(eg.current,L);C(eg.current,{transform:"translate3d(0, 0, 0)",transition:`transform ${k.DURATION}s cubic-bezier(${k.EASE.join(",")})`}),C(eo.current,{transition:`opacity ${k.DURATION}s cubic-bezier(${k.EASE.join(",")})`,opacity:"1"}),h&&t&&t>0&&K&&C(e,{borderRadius:"8px",overflow:"hidden",...S(L)?{transform:`scale(${eA()}) translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)`,transformOrigin:"top"}:{transform:`scale(${eA()}) translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)`,transformOrigin:"left"},transitionProperty:"transform, border-radius",transitionDuration:`${k.DURATION}s`,transitionTimingFunction:`cubic-bezier(${k.EASE.join(",")})`},!0)}return s.createElement(o.bL,{defaultOpen:B,onOpenChange:e=>{(A||e)&&(e?et(!0):eP(!0),Q(e))},open:K},s.createElement(l.Provider,{value:{activeSnapPoint:ew,snapPoints:u,setActiveSnapPoint:ej,drawerRef:eg,overlayRef:eo,onOpenChange:t,onPress:function(e){var t,a;(A||u)&&(!eg.current||eg.current.contains(e.target))&&(ex.current=(null==(t=eg.current)?void 0:t.getBoundingClientRect().height)||0,ev.current=(null==(a=eg.current)?void 0:a.getBoundingClientRect().width)||0,er(!0),ei.current=new Date,c()&&window.addEventListener("touchend",()=>eu.current=!1,{once:!0}),e.target.setPointerCapture(e.pointerId),ef.current=S(L)?e.pageY:e.pageX)},onRelease:function(e){var t,a;if(!ea||!eg.current)return;eg.current.classList.remove(E),eu.current=!1,er(!1),ed.current=new Date;let r=_(eg.current,L);if(!e||!eR(e.target,!1)||!r||Number.isNaN(r)||null===ei.current)return;let n=ed.current.getTime()-ei.current.getTime(),s=ef.current-(S(L)?e.pageY:e.pageX),o=Math.abs(s)/n;if(o>.05&&(es(!0),setTimeout(()=>{es(!1)},200)),u){eN({draggedDistance:s*("bottom"===L||"right"===L?1:-1),closeDrawer:eP,velocity:o,dismissible:A}),null==i||i(e,!0);return}if("bottom"===L||"right"===L?s>0:s<0){eT(),null==i||i(e,!0);return}if(o>.4){eP(),null==i||i(e,!1);return}let l=Math.min(null!=(t=eg.current.getBoundingClientRect().height)?t:0,window.innerHeight),d=Math.min(null!=(a=eg.current.getBoundingClientRect().width)?a:0,window.innerWidth);if(Math.abs(r)>=("left"===L||"right"===L?d:l)*j){eP(),null==i||i(e,!1);return}null==i||i(e,!0),eT()},onDrag:function(e){if(eg.current&&ea){let t="bottom"===L||"right"===L?1:-1,a=(ef.current-(S(L)?e.pageY:e.pageX))*t,r=a>0,s=u&&!A&&!r;if(s&&0===ey)return;let o=Math.abs(a),l=document.querySelector("[data-vaul-drawer-wrapper]"),i=o/("bottom"===L||"top"===L?ex.current:ev.current),d=ek(o,r);if(null!==d&&(i=d),s&&i>=1||!eu.current&&!eR(e.target,r))return;if(eg.current.classList.add(E),eu.current=!0,C(eg.current,{transition:"none"}),C(eo.current,{transition:"none"}),u&&eS({draggedDistance:a}),r&&!u){let e=Math.min(-(8*(Math.log(a+1)-2)*1),0)*t;C(eg.current,{transform:S(L)?`translate3d(0, ${e}px, 0)`:`translate3d(${e}px, 0, 0)`});return}let c=1-i;if((e_||I&&ey===I-1)&&(null==n||n(e,i),C(eo.current,{opacity:`${c}`,transition:"none"},!0)),l&&eo.current&&h){let e=Math.min(eA()+i*(1-eA()),1),t=8-8*i,a=Math.max(0,14-14*i);C(l,{borderRadius:`${t}px`,transform:S(L)?`scale(${e}) translate3d(0, ${a}px, 0)`:`scale(${e}) translate3d(${a}px, 0, 0)`,transition:"none"},!0)}if(!u){let e=o*t;C(eg.current,{transform:S(L)?`translate3d(0, ${e}px, 0)`:`translate3d(${e}px, 0, 0)`})}}},dismissible:A,shouldAnimate:eh,handleOnly:T,isOpen:K,isDragging:ea,shouldFade:e_,closeDrawer:eP,onNestedDrag:function(e,t){if(t<0)return;let a=(window.innerWidth-16)/window.innerWidth,r=a+t*(1-a),n=-16+16*t;C(eg.current,{transform:S(L)?`scale(${r}) translate3d(0, ${n}px, 0)`:`scale(${r}) translate3d(${n}px, 0, 0)`,transition:"none"})},onNestedOpenChange:function(e){let t=e?(window.innerWidth-16)/window.innerWidth:1,a=e?-16:0;em.current&&window.clearTimeout(em.current),C(eg.current,{transition:`transform ${k.DURATION}s cubic-bezier(${k.EASE.join(",")})`,transform:S(L)?`scale(${t}) translate3d(0, ${a}px, 0)`:`scale(${t}) translate3d(${a}px, 0, 0)`}),!e&&eg.current&&(em.current=setTimeout(()=>{let e=_(eg.current,L);C(eg.current,{transition:"none",transform:S(L)?`translate3d(0, ${e}px, 0)`:`translate3d(${e}px, 0, 0)`})},500))},onNestedRelease:function(e,t){let a=S(L)?window.innerHeight:window.innerWidth,r=t?(a-16)/a:1,n=t?-16:0;t&&C(eg.current,{transition:`transform ${k.DURATION}s cubic-bezier(${k.EASE.join(",")})`,transform:S(L)?`scale(${r}) translate3d(0, ${n}px, 0)`:`scale(${r}) translate3d(${n}px, 0, 0)`})},keyboardIsOpen:ep,modal:M,snapPointsOffset:eC,activeSnapPointIndex:ey,direction:L,shouldScaleBackground:h,setBackgroundColorOnScale:x,noBodyStyles:U,container:Y,autoFocus:X}},a))}let I=s.forwardRef(function({...e},t){let{overlayRef:a,snapPoints:r,onRelease:n,shouldFade:l,isOpen:d,modal:c,shouldAnimate:u}=i(),m=j(t,a),f=r&&r.length>0;if(!c)return null;let p=s.useCallback(e=>n(e),[n]);return s.createElement(o.hJ,{onMouseUp:p,ref:m,"data-vaul-overlay":"","data-vaul-snap-points":d&&f?"true":"false","data-vaul-snap-points-overlay":d&&l?"true":"false","data-vaul-animate":(null==u?void 0:u.current)?"true":"false",...e})});I.displayName="Drawer.Overlay";let $=s.forwardRef(function({onPointerDownOutside:e,style:t,onOpenAutoFocus:a,...r},n){let{drawerRef:l,onPress:d,onRelease:c,onDrag:u,keyboardIsOpen:m,snapPointsOffset:f,activeSnapPointIndex:p,modal:h,isOpen:g,direction:x,snapPoints:v,container:b,handleOnly:w,shouldAnimate:y,autoFocus:N}=i(),[C,S]=s.useState(!1),_=j(n,l),k=s.useRef(null),E=s.useRef(null),A=s.useRef(!1),R=v&&v.length>0,{direction:P,isOpen:T,shouldScaleBackground:I,setBackgroundColorOnScale:$,noBodyStyles:D}=i();s.useRef(null),(0,s.useMemo)(()=>document.body.style.backgroundColor,[]);let O=(e,t,a=0)=>{if(A.current)return!0;let r=Math.abs(e.y),n=Math.abs(e.x),s=n>r,o=["bottom","right"].includes(t)?1:-1;if("left"===t||"right"===t){if(!(e.x*o<0)&&n>=0&&n<=a)return s}else if(!(e.y*o<0)&&r>=0&&r<=a)return!s;return A.current=!0,!0};function M(e){k.current=null,A.current=!1,c(e)}return s.useEffect(()=>{R&&window.requestAnimationFrame(()=>{S(!0)})},[]),s.createElement(o.UC,{"data-vaul-drawer-direction":x,"data-vaul-drawer":"","data-vaul-delayed-snap-points":C?"true":"false","data-vaul-snap-points":g&&R?"true":"false","data-vaul-custom-container":b?"true":"false","data-vaul-animate":(null==y?void 0:y.current)?"true":"false",...r,ref:_,style:f&&f.length>0?{"--snap-point-height":`${f[null!=p?p:0]}px`,...t}:t,onPointerDown:e=>{w||(null==r.onPointerDown||r.onPointerDown.call(r,e),k.current={x:e.pageX,y:e.pageY},d(e))},onOpenAutoFocus:e=>{null==a||a(e),N||e.preventDefault()},onPointerDownOutside:t=>{if(null==e||e(t),!h||t.defaultPrevented)return void t.preventDefault();m.current&&(m.current=!1)},onFocusOutside:e=>{if(!h)return void e.preventDefault()},onPointerMove:e=>{if(E.current=e,w||(null==r.onPointerMove||r.onPointerMove.call(r,e),!k.current))return;let t=e.pageY-k.current.y,a=e.pageX-k.current.x,n="touch"===e.pointerType?10:2;O({x:a,y:t},x,n)?u(e):(Math.abs(a)>n||Math.abs(t)>n)&&(k.current=null)},onPointerUp:e=>{null==r.onPointerUp||r.onPointerUp.call(r,e),k.current=null,A.current=!1,c(e)},onPointerOut:e=>{null==r.onPointerOut||r.onPointerOut.call(r,e),M(E.current)},onContextMenu:e=>{null==r.onContextMenu||r.onContextMenu.call(r,e),E.current&&M(E.current)}})});$.displayName="Drawer.Content";let D=s.forwardRef(function({preventCycle:e=!1,children:t,...a},r){let{closeDrawer:n,isDragging:o,snapPoints:l,activeSnapPoint:d,setActiveSnapPoint:c,dismissible:u,handleOnly:m,isOpen:f,onPress:p,onDrag:h}=i(),g=s.useRef(null),x=s.useRef(!1);function v(){g.current&&window.clearTimeout(g.current),x.current=!1}return s.createElement("div",{onClick:function(){if(x.current)return void v();window.setTimeout(()=>{!function(){if(o||e||x.current)return v();if(v(),!l||0===l.length){u||n();return}if(d===l[l.length-1]&&u)return n();let t=l.findIndex(e=>e===d);-1!==t&&c(l[t+1])}()},120)},onPointerCancel:v,onPointerDown:e=>{m&&p(e),g.current=window.setTimeout(()=>{x.current=!0},250)},onPointerMove:e=>{m&&h(e)},ref:r,"data-vaul-drawer-visible":f?"true":"false","data-vaul-handle":"","aria-hidden":"true",...a},s.createElement("span",{"data-vaul-handle-hitarea":"","aria-hidden":"true"},t))});D.displayName="Drawer.Handle";let O={Root:T,NestedRoot:function({onDrag:e,onOpenChange:t,open:a,...r}){let{onNestedDrag:n,onNestedOpenChange:o,onNestedRelease:l}=i();if(!n)throw Error("Drawer.NestedRoot must be placed in another drawer");return s.createElement(T,{nested:!0,open:a,onClose:()=>{o(!1)},onDrag:(t,a)=>{n(t,a),null==e||e(t,a)},onOpenChange:e=>{e&&o(e),null==t||t(e)},onRelease:l,...r})},Content:$,Overlay:I,Trigger:o.l9,Portal:function(e){let t=i(),{container:a=t.container,...r}=e;return s.createElement(o.ZL,{container:a,...r})},Handle:D,Close:o.bm,Title:o.hE,Description:o.VY};var M=a(4780);function F({...e}){return(0,n.jsx)(O.Root,{"data-slot":"drawer",...e})}function q({...e}){return(0,n.jsx)(O.Trigger,{"data-slot":"drawer-trigger",...e})}function U({...e}){return(0,n.jsx)(O.Portal,{"data-slot":"drawer-portal",...e})}function L({className:e,...t}){return(0,n.jsx)(O.Overlay,{"data-slot":"drawer-overlay",className:(0,M.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function B({className:e,children:t,...a}){return(0,n.jsxs)(U,{"data-slot":"drawer-portal",children:[(0,n.jsx)(L,{}),(0,n.jsxs)(O.Content,{"data-slot":"drawer-content",className:(0,M.cn)("group/drawer-content bg-background fixed z-50 flex h-auto flex-col","data-[vaul-drawer-direction=top]:inset-x-0 data-[vaul-drawer-direction=top]:top-0 data-[vaul-drawer-direction=top]:mb-24 data-[vaul-drawer-direction=top]:max-h-[80vh] data-[vaul-drawer-direction=top]:rounded-b-lg data-[vaul-drawer-direction=top]:border-b","data-[vaul-drawer-direction=bottom]:inset-x-0 data-[vaul-drawer-direction=bottom]:bottom-0 data-[vaul-drawer-direction=bottom]:mt-24 data-[vaul-drawer-direction=bottom]:max-h-[80vh] data-[vaul-drawer-direction=bottom]:rounded-t-lg data-[vaul-drawer-direction=bottom]:border-t","data-[vaul-drawer-direction=right]:inset-y-0 data-[vaul-drawer-direction=right]:right-0 data-[vaul-drawer-direction=right]:w-3/4 data-[vaul-drawer-direction=right]:border-l data-[vaul-drawer-direction=right]:sm:max-w-sm","data-[vaul-drawer-direction=left]:inset-y-0 data-[vaul-drawer-direction=left]:left-0 data-[vaul-drawer-direction=left]:w-3/4 data-[vaul-drawer-direction=left]:border-r data-[vaul-drawer-direction=left]:sm:max-w-sm",e),...a,children:[(0,n.jsx)("div",{className:"bg-muted mx-auto mt-4 hidden h-2 w-[100px] shrink-0 rounded-full group-data-[vaul-drawer-direction=bottom]/drawer-content:block"}),t]})]})}function z({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"drawer-header",className:(0,M.cn)("flex flex-col gap-1.5 p-4",e),...t})}function W({className:e,...t}){return(0,n.jsx)(O.Title,{"data-slot":"drawer-title",className:(0,M.cn)("text-foreground font-semibold",e),...t})}},50173:(e,t,a)=>{"use strict";a.d(t,{U:()=>p});var r=a(60687),n=a(43210),s=a.n(n),o=a(29523),l=a(40988),i=a(44493),d=a(35950),c=a(85763),u=a(42692);let m={smileys:{name:"Smileys & People",icon:"\uD83D\uDE00",emojis:["\uD83D\uDE00","\uD83D\uDE03","\uD83D\uDE04","\uD83D\uDE01","\uD83D\uDE06","\uD83D\uDE05","\uD83D\uDE02","\uD83E\uDD23","\uD83D\uDE0A","\uD83D\uDE07","\uD83D\uDE42","\uD83D\uDE43","\uD83D\uDE09","\uD83D\uDE0C","\uD83D\uDE0D","\uD83E\uDD70","\uD83D\uDE18","\uD83D\uDE17","\uD83D\uDE19","\uD83D\uDE1A","\uD83D\uDE0B","\uD83D\uDE1B","\uD83D\uDE1D","\uD83D\uDE1C","\uD83E\uDD2A","\uD83E\uDD28","\uD83E\uDDD0","\uD83E\uDD13","\uD83D\uDE0E","\uD83E\uDD29","\uD83E\uDD73","\uD83D\uDE0F","\uD83D\uDE12","\uD83D\uDE1E","\uD83D\uDE14","\uD83D\uDE1F","\uD83D\uDE15","\uD83D\uDE41","☹️","\uD83D\uDE23","\uD83D\uDE16","\uD83D\uDE2B","\uD83D\uDE29","\uD83E\uDD7A","\uD83D\uDE22","\uD83D\uDE2D","\uD83D\uDE24","\uD83D\uDE20","\uD83D\uDE21","\uD83E\uDD2C","\uD83E\uDD2F","\uD83D\uDE33","\uD83E\uDD75","\uD83E\uDD76","\uD83D\uDE31","\uD83D\uDE28","\uD83D\uDE30","\uD83D\uDE25","\uD83D\uDE13","\uD83E\uDD17","\uD83E\uDD14","\uD83E\uDD2D","\uD83E\uDD2B","\uD83E\uDD25","\uD83D\uDE36","\uD83D\uDE10","\uD83D\uDE11","\uD83D\uDE2C","\uD83D\uDE44","\uD83D\uDE2F","\uD83D\uDE26","\uD83D\uDE27","\uD83D\uDE2E","\uD83D\uDE32","\uD83E\uDD71","\uD83D\uDE34","\uD83E\uDD24","\uD83D\uDE2A","\uD83D\uDE35","\uD83E\uDD10","\uD83E\uDD74","\uD83E\uDD22","\uD83E\uDD2E","\uD83E\uDD27","\uD83D\uDE37","\uD83E\uDD12","\uD83E\uDD15","\uD83E\uDD11","\uD83E\uDD20","\uD83D\uDE08","\uD83D\uDC7F","\uD83D\uDC79","\uD83D\uDC7A","\uD83E\uDD21","\uD83D\uDCA9","\uD83D\uDC7B","\uD83D\uDC80","☠️","\uD83D\uDC7D","\uD83D\uDC7E","\uD83E\uDD16","\uD83C\uDF83","\uD83D\uDE3A","\uD83D\uDE38","\uD83D\uDE39","\uD83D\uDE3B","\uD83D\uDE3C","\uD83D\uDE3D","\uD83D\uDE40","\uD83D\uDE3F","\uD83D\uDE3E"]},people:{name:"People & Body",icon:"\uD83D\uDC4B",emojis:["\uD83D\uDC4B","\uD83E\uDD1A","\uD83D\uDD90️","✋","\uD83D\uDD96","\uD83D\uDC4C","\uD83E\uDD0C","\uD83E\uDD0F","✌️","\uD83E\uDD1E","\uD83E\uDD1F","\uD83E\uDD18","\uD83E\uDD19","\uD83D\uDC48","\uD83D\uDC49","\uD83D\uDC46","\uD83D\uDD95","\uD83D\uDC47","☝️","\uD83D\uDC4D","\uD83D\uDC4E","\uD83D\uDC4A","✊","\uD83E\uDD1B","\uD83E\uDD1C","\uD83D\uDC4F","\uD83D\uDE4C","\uD83D\uDC50","\uD83E\uDD32","\uD83E\uDD1D","\uD83D\uDE4F","✍️","\uD83D\uDC85","\uD83E\uDD33","\uD83D\uDCAA","\uD83E\uDDBE","\uD83E\uDDBF","\uD83E\uDDB5","\uD83E\uDDB6","\uD83D\uDC42","\uD83E\uDDBB","\uD83D\uDC43","\uD83E\uDDE0","\uD83E\uDEC0","\uD83E\uDEC1","\uD83E\uDDB7","\uD83E\uDDB4","\uD83D\uDC40","\uD83D\uDC41️","\uD83D\uDC45","\uD83D\uDC44","\uD83D\uDC8B","\uD83E\uDE78","\uD83D\uDC76","\uD83E\uDDD2","\uD83D\uDC66","\uD83D\uDC67","\uD83E\uDDD1","\uD83D\uDC71","\uD83D\uDC68","\uD83E\uDDD4","\uD83D\uDC68‍\uD83E\uDDB0","\uD83D\uDC68‍\uD83E\uDDB1","\uD83D\uDC68‍\uD83E\uDDB3","\uD83D\uDC68‍\uD83E\uDDB2","\uD83D\uDC69","\uD83D\uDC69‍\uD83E\uDDB0","\uD83E\uDDD1‍\uD83E\uDDB0","\uD83D\uDC69‍\uD83E\uDDB1","\uD83E\uDDD1‍\uD83E\uDDB1","\uD83D\uDC69‍\uD83E\uDDB3","\uD83E\uDDD1‍\uD83E\uDDB3","\uD83D\uDC69‍\uD83E\uDDB2","\uD83E\uDDD1‍\uD83E\uDDB2","\uD83D\uDC71‍♀️","\uD83D\uDC71‍♂️","\uD83E\uDDD3","\uD83D\uDC74","\uD83D\uDC75","\uD83D\uDE4D","\uD83D\uDE4D‍♂️","\uD83D\uDE4D‍♀️","\uD83D\uDE4E","\uD83D\uDE4E‍♂️","\uD83D\uDE4E‍♀️","\uD83D\uDE45","\uD83D\uDE45‍♂️","\uD83D\uDE45‍♀️","\uD83D\uDE46","\uD83D\uDE46‍♂️","\uD83D\uDE46‍♀️","\uD83D\uDC81","\uD83D\uDC81‍♂️","\uD83D\uDC81‍♀️","\uD83D\uDE4B","\uD83D\uDE4B‍♂️","\uD83D\uDE4B‍♀️","\uD83E\uDDCF","\uD83E\uDDCF‍♂️","\uD83E\uDDCF‍♀️","\uD83D\uDE47","\uD83D\uDE47‍♂️","\uD83D\uDE47‍♀️","\uD83E\uDD26","\uD83E\uDD26‍♂️","\uD83E\uDD26‍♀️","\uD83E\uDD37","\uD83E\uDD37‍♂️","\uD83E\uDD37‍♀️"]},animals:{name:"Animals & Nature",icon:"\uD83D\uDC36",emojis:["\uD83D\uDC36","\uD83D\uDC31","\uD83D\uDC2D","\uD83D\uDC39","\uD83D\uDC30","\uD83E\uDD8A","\uD83D\uDC3B","\uD83D\uDC3C","\uD83D\uDC3B‍❄️","\uD83D\uDC28","\uD83D\uDC2F","\uD83E\uDD81","\uD83D\uDC2E","\uD83D\uDC37","\uD83D\uDC3D","\uD83D\uDC38","\uD83D\uDC35","\uD83D\uDE48","\uD83D\uDE49","\uD83D\uDE4A","\uD83D\uDC12","\uD83D\uDC14","\uD83D\uDC27","\uD83D\uDC26","\uD83D\uDC24","\uD83D\uDC23","\uD83D\uDC25","\uD83E\uDD86","\uD83E\uDD85","\uD83E\uDD89","\uD83E\uDD87","\uD83D\uDC3A","\uD83D\uDC17","\uD83D\uDC34","\uD83E\uDD84","\uD83D\uDC1D","\uD83D\uDC1B","\uD83E\uDD8B","\uD83D\uDC0C","\uD83D\uDC1E","\uD83D\uDC1C","\uD83E\uDD9F","\uD83E\uDD97","\uD83D\uDD77️","\uD83D\uDD78️","\uD83E\uDD82","\uD83D\uDC22","\uD83D\uDC0D","\uD83E\uDD8E","\uD83E\uDD96","\uD83E\uDD95","\uD83D\uDC19","\uD83E\uDD91","\uD83E\uDD90","\uD83E\uDD9E","\uD83E\uDD80","\uD83D\uDC21","\uD83D\uDC20","\uD83D\uDC1F","\uD83D\uDC2C","\uD83D\uDC33","\uD83D\uDC0B","\uD83E\uDD88","\uD83D\uDC0A","\uD83D\uDC05","\uD83D\uDC06","\uD83E\uDD93","\uD83E\uDD8D","\uD83E\uDDA7","\uD83D\uDC18","\uD83E\uDD9B","\uD83E\uDD8F","\uD83D\uDC2A","\uD83D\uDC2B","\uD83E\uDD92","\uD83E\uDD98","\uD83D\uDC03","\uD83D\uDC02","\uD83D\uDC04","\uD83D\uDC0E","\uD83D\uDC16","\uD83D\uDC0F","\uD83D\uDC11","\uD83E\uDD99","\uD83D\uDC10","\uD83E\uDD8C","\uD83D\uDC15","\uD83D\uDC29","\uD83E\uDDAE","\uD83D\uDC15‍\uD83E\uDDBA","\uD83D\uDC08","\uD83D\uDC08‍⬛","\uD83D\uDC13","\uD83E\uDD83","\uD83E\uDD9A","\uD83E\uDD9C","\uD83E\uDDA2","\uD83E\uDDA9","\uD83D\uDD4A️","\uD83D\uDC07","\uD83E\uDD9D","\uD83E\uDDA8","\uD83E\uDDA1","\uD83E\uDDA6","\uD83E\uDDA5","\uD83D\uDC01","\uD83D\uDC00","\uD83D\uDC3F️","\uD83E\uDD94"]},food:{name:"Food & Drink",icon:"\uD83C\uDF4E",emojis:["\uD83C\uDF4E","\uD83C\uDF4F","\uD83C\uDF4A","\uD83C\uDF4B","\uD83C\uDF4C","\uD83C\uDF49","\uD83C\uDF47","\uD83C\uDF53","\uD83E\uDED0","\uD83C\uDF48","\uD83C\uDF52","\uD83C\uDF51","\uD83E\uDD6D","\uD83C\uDF4D","\uD83E\uDD65","\uD83E\uDD5D","\uD83C\uDF45","\uD83C\uDF46","\uD83E\uDD51","\uD83E\uDD66","\uD83E\uDD6C","\uD83E\uDD52","\uD83C\uDF36️","\uD83E\uDED1","\uD83C\uDF3D","\uD83E\uDD55","\uD83E\uDED2","\uD83E\uDDC4","\uD83E\uDDC5","\uD83E\uDD54","\uD83C\uDF60","\uD83E\uDD50","\uD83E\uDD6F","\uD83C\uDF5E","\uD83E\uDD56","\uD83E\uDD68","\uD83E\uDDC0","\uD83E\uDD5A","\uD83C\uDF73","\uD83E\uDDC8","\uD83E\uDD5E","\uD83E\uDDC7","\uD83E\uDD53","\uD83E\uDD69","\uD83C\uDF57","\uD83C\uDF56","\uD83E\uDDB4","\uD83C\uDF2D","\uD83C\uDF54","\uD83C\uDF5F","\uD83C\uDF55","\uD83E\uDED3","\uD83E\uDD6A","\uD83E\uDD59","\uD83E\uDDC6","\uD83C\uDF2E","\uD83C\uDF2F","\uD83E\uDED4","\uD83E\uDD57","\uD83E\uDD58","\uD83E\uDED5","\uD83E\uDD6B","\uD83C\uDF5D","\uD83C\uDF5C","\uD83C\uDF72","\uD83C\uDF5B","\uD83C\uDF63","\uD83C\uDF71","\uD83E\uDD5F","\uD83E\uDDAA","\uD83C\uDF64","\uD83C\uDF59","\uD83C\uDF5A","\uD83C\uDF58","\uD83C\uDF65","\uD83E\uDD60","\uD83E\uDD6E","\uD83C\uDF62","\uD83C\uDF61","\uD83C\uDF67","\uD83C\uDF68","\uD83C\uDF66","\uD83E\uDD67","\uD83E\uDDC1","\uD83C\uDF70","\uD83C\uDF82","\uD83C\uDF6E","\uD83C\uDF6D","\uD83C\uDF6C","\uD83C\uDF6B","\uD83C\uDF7F","\uD83C\uDF69","\uD83C\uDF6A","\uD83C\uDF30","\uD83E\uDD5C","\uD83C\uDF6F","\uD83E\uDD5B","\uD83C\uDF7C","☕","\uD83E\uDED6","\uD83C\uDF75","\uD83E\uDDC3","\uD83E\uDD64","\uD83E\uDDCB","\uD83C\uDF76","\uD83C\uDF7A","\uD83C\uDF7B","\uD83E\uDD42","\uD83C\uDF77","\uD83E\uDD43","\uD83C\uDF78","\uD83C\uDF79","\uD83E\uDDC9","\uD83C\uDF7E"]},activities:{name:"Activities",icon:"⚽",emojis:["⚽","\uD83C\uDFC0","\uD83C\uDFC8","⚾","\uD83E\uDD4E","\uD83C\uDFBE","\uD83C\uDFD0","\uD83C\uDFC9","\uD83E\uDD4F","\uD83C\uDFB1","\uD83E\uDE80","\uD83C\uDFD3","\uD83C\uDFF8","\uD83C\uDFD2","\uD83C\uDFD1","\uD83E\uDD4D","\uD83C\uDFCF","\uD83E\uDE83","\uD83E\uDD45","⛳","\uD83E\uDE81","\uD83C\uDFF9","\uD83C\uDFA3","\uD83E\uDD3F","\uD83E\uDD4A","\uD83E\uDD4B","\uD83C\uDFBD","\uD83D\uDEF9","\uD83D\uDEF7","⛸️","\uD83E\uDD4C","\uD83C\uDFBF","⛷️","\uD83C\uDFC2","\uD83E\uDE82","\uD83C\uDFCB️","\uD83C\uDFCB️‍♂️","\uD83C\uDFCB️‍♀️","\uD83E\uDD3C","\uD83E\uDD3C‍♂️","\uD83E\uDD3C‍♀️","\uD83E\uDD38","\uD83E\uDD38‍♂️","\uD83E\uDD38‍♀️","⛹️","⛹️‍♂️","⛹️‍♀️","\uD83E\uDD3A","\uD83E\uDD3E","\uD83E\uDD3E‍♂️","\uD83E\uDD3E‍♀️","\uD83C\uDFCC️","\uD83C\uDFCC️‍♂️","\uD83C\uDFCC️‍♀️","\uD83C\uDFC7","\uD83E\uDDD8","\uD83E\uDDD8‍♂️","\uD83E\uDDD8‍♀️","\uD83C\uDFC4","\uD83C\uDFC4‍♂️","\uD83C\uDFC4‍♀️","\uD83C\uDFCA","\uD83C\uDFCA‍♂️","\uD83C\uDFCA‍♀️","\uD83E\uDD3D","\uD83E\uDD3D‍♂️","\uD83E\uDD3D‍♀️","\uD83D\uDEA3","\uD83D\uDEA3‍♂️","\uD83D\uDEA3‍♀️","\uD83E\uDDD7","\uD83E\uDDD7‍♂️","\uD83E\uDDD7‍♀️","\uD83D\uDEB5","\uD83D\uDEB5‍♂️","\uD83D\uDEB5‍♀️","\uD83D\uDEB4","\uD83D\uDEB4‍♂️","\uD83D\uDEB4‍♀️","\uD83C\uDFC6","\uD83E\uDD47","\uD83E\uDD48","\uD83E\uDD49","\uD83C\uDFC5","\uD83C\uDF96️","\uD83C\uDFF5️","\uD83C\uDF97️","\uD83C\uDFAB","\uD83C\uDF9F️","\uD83C\uDFAA","\uD83E\uDD39","\uD83E\uDD39‍♂️","\uD83E\uDD39‍♀️","\uD83C\uDFAD","\uD83E\uDE70","\uD83C\uDFA8","\uD83C\uDFAC","\uD83C\uDFA4","\uD83C\uDFA7","\uD83C\uDFBC","\uD83C\uDFB5","\uD83C\uDFB6","\uD83E\uDD41","\uD83E\uDE98","\uD83C\uDFB9","\uD83C\uDFB7","\uD83C\uDFBA","\uD83E\uDE97","\uD83C\uDFB8","\uD83E\uDE95","\uD83C\uDFBB","\uD83C\uDFB2","♟️","\uD83C\uDFAF","\uD83C\uDFB3","\uD83C\uDFAE","\uD83C\uDFB0","\uD83E\uDDE9"]},travel:{name:"Travel & Places",icon:"\uD83D\uDE97",emojis:["\uD83D\uDE97","\uD83D\uDE95","\uD83D\uDE99","\uD83D\uDE8C","\uD83D\uDE8E","\uD83C\uDFCE️","\uD83D\uDE93","\uD83D\uDE91","\uD83D\uDE92","\uD83D\uDE90","\uD83D\uDEFB","\uD83D\uDE9A","\uD83D\uDE9B","\uD83D\uDE9C","\uD83C\uDFCD️","\uD83D\uDEF5","\uD83D\uDEB2","\uD83D\uDEF4","\uD83D\uDEF9","\uD83D\uDEFC","\uD83D\uDE81","\uD83D\uDEF8","✈️","\uD83D\uDEE9️","\uD83D\uDEEB","\uD83D\uDEEC","\uD83E\uDE82","\uD83D\uDCBA","\uD83D\uDE80","\uD83D\uDEF0️","\uD83D\uDE89","\uD83D\uDE8A","\uD83D\uDE9D","\uD83D\uDE9E","\uD83D\uDE8B","\uD83D\uDE83","\uD83D\uDE8B","\uD83D\uDE9E","\uD83D\uDE9D","\uD83D\uDE84","\uD83D\uDE85","\uD83D\uDE88","\uD83D\uDE82","\uD83D\uDE86","\uD83D\uDE87","\uD83D\uDE8A","\uD83D\uDE89","✈️","\uD83D\uDEEB","\uD83D\uDEEC","\uD83D\uDEE9️","\uD83D\uDCBA","\uD83D\uDEF0️","\uD83D\uDE80","\uD83D\uDEF8","\uD83D\uDE81","\uD83D\uDEF6","⛵","\uD83D\uDEA4","\uD83D\uDEE5️","\uD83D\uDEF3️","⛴️","\uD83D\uDEA2","⚓","⛽","\uD83D\uDEA7","\uD83D\uDEA8","\uD83D\uDEA5","\uD83D\uDEA6","\uD83D\uDED1","\uD83D\uDE8F","\uD83D\uDDFA️","\uD83D\uDDFF","\uD83D\uDDFD","\uD83D\uDDFC","\uD83C\uDFF0","\uD83C\uDFEF","\uD83C\uDFDF️","\uD83C\uDFA1","\uD83C\uDFA2","\uD83C\uDFA0","⛲","⛱️","\uD83C\uDFD6️","\uD83C\uDFDD️","\uD83C\uDFDC️","\uD83C\uDF0B","⛰️","\uD83C\uDFD4️","\uD83D\uDDFB","\uD83C\uDFD5️","⛺","\uD83D\uDED6","\uD83C\uDFE0","\uD83C\uDFE1","\uD83C\uDFD8️","\uD83C\uDFDA️","\uD83C\uDFD7️","\uD83C\uDFED","\uD83C\uDFE2","\uD83C\uDFEC","\uD83C\uDFE3","\uD83C\uDFE4","\uD83C\uDFE5","\uD83C\uDFE6","\uD83C\uDFE8","\uD83C\uDFEA","\uD83C\uDFEB","\uD83C\uDFE9","\uD83D\uDC92","\uD83C\uDFDB️","⛪","\uD83D\uDD4C","\uD83D\uDED5","\uD83D\uDD4D","\uD83D\uDD4B","⛩️","\uD83D\uDEE4️","\uD83D\uDEE3️","\uD83D\uDDFE","\uD83C\uDF91","\uD83C\uDFDE️","\uD83C\uDF05","\uD83C\uDF04","\uD83C\uDF20","\uD83C\uDF87","\uD83C\uDF86","\uD83C\uDF07","\uD83C\uDF06","\uD83C\uDFD9️","\uD83C\uDF03","\uD83C\uDF0C","\uD83C\uDF09","\uD83C\uDF01"]},objects:{name:"Objects",icon:"⌚",emojis:["⌚","\uD83D\uDCF1","\uD83D\uDCF2","\uD83D\uDCBB","⌨️","\uD83D\uDDA5️","\uD83D\uDDA8️","\uD83D\uDDB1️","\uD83D\uDDB2️","\uD83D\uDD79️","\uD83D\uDDDC️","\uD83D\uDCBD","\uD83D\uDCBE","\uD83D\uDCBF","\uD83D\uDCC0","\uD83D\uDCFC","\uD83D\uDCF7","\uD83D\uDCF8","\uD83D\uDCF9","\uD83C\uDFA5","\uD83D\uDCFD️","\uD83C\uDF9E️","\uD83D\uDCDE","☎️","\uD83D\uDCDF","\uD83D\uDCE0","\uD83D\uDCFA","\uD83D\uDCFB","\uD83C\uDF99️","\uD83C\uDF9A️","\uD83C\uDF9B️","\uD83E\uDDED","⏱️","⏲️","⏰","\uD83D\uDD70️","⌛","⏳","\uD83D\uDCE1","\uD83D\uDD0B","\uD83D\uDD0C","\uD83D\uDCA1","\uD83D\uDD26","\uD83D\uDD6F️","\uD83E\uDE94","\uD83E\uDDEF","\uD83D\uDEE2️","\uD83D\uDCB8","\uD83D\uDCB5","\uD83D\uDCB4","\uD83D\uDCB6","\uD83D\uDCB7","\uD83E\uDE99","\uD83D\uDCB0","\uD83D\uDCB3","\uD83D\uDC8E","⚖️","\uD83E\uDE9C","\uD83E\uDDF0","\uD83D\uDD27","\uD83D\uDD28","⚒️","\uD83D\uDEE0️","⛏️","\uD83E\uDE93","\uD83E\uDE9A","\uD83D\uDD29","⚙️","\uD83E\uDEA4","\uD83E\uDDF1","⛓️","\uD83E\uDDF2","\uD83D\uDD2B","\uD83D\uDCA3","\uD83E\uDDE8","\uD83E\uDE93","\uD83D\uDD2A","\uD83D\uDDE1️","⚔️","\uD83D\uDEE1️","\uD83D\uDEAC","⚰️","\uD83E\uDEA6","⚱️","\uD83C\uDFFA","\uD83D\uDD2E","\uD83D\uDCFF","\uD83E\uDDFF","\uD83D\uDC88","⚗️","\uD83D\uDD2D","\uD83D\uDD2C","\uD83D\uDD73️","\uD83E\uDE79","\uD83E\uDE7A","\uD83D\uDC8A","\uD83D\uDC89","\uD83E\uDE78","\uD83E\uDDEC","\uD83E\uDDA0","\uD83E\uDDEB","\uD83E\uDDEA","\uD83C\uDF21️","\uD83E\uDDF9","\uD83E\uDEA3","\uD83E\uDDFD","\uD83E\uDDF4","\uD83D\uDECE️","\uD83D\uDD11","\uD83D\uDDDD️","\uD83D\uDEAA","\uD83E\uDE91","\uD83D\uDECB️","\uD83D\uDECF️","\uD83D\uDECC","\uD83E\uDDF8","\uD83E\uDE86","\uD83D\uDDBC️","\uD83E\uDE9E","\uD83E\uDE9F","\uD83D\uDECD️","\uD83D\uDED2","\uD83C\uDF81","\uD83C\uDF88","\uD83C\uDF8F","\uD83C\uDF80","\uD83E\uDE84","\uD83E\uDE85","\uD83C\uDF8A","\uD83C\uDF89","\uD83C\uDF8E","\uD83C\uDFEE","\uD83C\uDF90","\uD83E\uDDE7","✉️","\uD83D\uDCE9","\uD83D\uDCE8","\uD83D\uDCE7","\uD83D\uDC8C","\uD83D\uDCE5","\uD83D\uDCE4","\uD83D\uDCE6","\uD83C\uDFF7️","\uD83E\uDEA7","\uD83D\uDCEA","\uD83D\uDCEB","\uD83D\uDCEC","\uD83D\uDCED","\uD83D\uDCEE","\uD83D\uDCEF","\uD83D\uDCDC","\uD83D\uDCC3","\uD83D\uDCC4","\uD83D\uDCD1","\uD83E\uDDFE","\uD83D\uDCCA","\uD83D\uDCC8","\uD83D\uDCC9","\uD83D\uDDD2️","\uD83D\uDDD3️","\uD83D\uDCC6","\uD83D\uDCC5","\uD83D\uDDD1️","\uD83D\uDCC7","\uD83D\uDDC3️","\uD83D\uDDF3️","\uD83D\uDDC4️","\uD83D\uDCCB","\uD83D\uDCC1","\uD83D\uDCC2","\uD83D\uDDC2️","\uD83D\uDDDE️","\uD83D\uDCF0","\uD83D\uDCD3","\uD83D\uDCD4","\uD83D\uDCD2","\uD83D\uDCD5","\uD83D\uDCD7","\uD83D\uDCD8","\uD83D\uDCD9","\uD83D\uDCDA","\uD83D\uDCD6","\uD83D\uDD16","\uD83E\uDDF7","\uD83D\uDD17","\uD83D\uDCCE","\uD83D\uDD87️","\uD83D\uDCD0","\uD83D\uDCCF","\uD83E\uDDEE","\uD83D\uDCCC","\uD83D\uDCCD","✂️","\uD83D\uDD8A️","\uD83D\uDD8B️","✒️","\uD83D\uDD8C️","\uD83D\uDD8D️","\uD83D\uDCDD","✏️","\uD83D\uDD0D","\uD83D\uDD0E","\uD83D\uDD0F","\uD83D\uDD10","\uD83D\uDD12","\uD83D\uDD13"]},symbols:{name:"Symbols",icon:"❤️",emojis:["❤️","\uD83E\uDDE1","\uD83D\uDC9B","\uD83D\uDC9A","\uD83D\uDC99","\uD83D\uDC9C","\uD83D\uDDA4","\uD83E\uDD0D","\uD83E\uDD0E","\uD83D\uDC94","❣️","\uD83D\uDC95","\uD83D\uDC9E","\uD83D\uDC93","\uD83D\uDC97","\uD83D\uDC96","\uD83D\uDC98","\uD83D\uDC9D","\uD83D\uDC9F","☮️","✝️","☪️","\uD83D\uDD49️","☸️","✡️","\uD83D\uDD2F","\uD83D\uDD4E","☯️","☦️","\uD83D\uDED0","⛎","♈","♉","♊","♋","♌","♍","♎","♏","♐","♑","♒","♓","\uD83C\uDD94","⚛️","\uD83C\uDE51","☢️","☣️","\uD83D\uDCF4","\uD83D\uDCF3","\uD83C\uDE36","\uD83C\uDE1A","\uD83C\uDE38","\uD83C\uDE3A","\uD83C\uDE37️","✴️","\uD83C\uDD9A","\uD83D\uDCAE","\uD83C\uDE50","㊙️","㊗️","\uD83C\uDE34","\uD83C\uDE35","\uD83C\uDE39","\uD83C\uDE32","\uD83C\uDD70️","\uD83C\uDD71️","\uD83C\uDD8E","\uD83C\uDD91","\uD83C\uDD7E️","\uD83C\uDD98","❌","⭕","\uD83D\uDED1","⛔","\uD83D\uDCDB","\uD83D\uDEAB","\uD83D\uDCAF","\uD83D\uDCA2","♨️","\uD83D\uDEB7","\uD83D\uDEAF","\uD83D\uDEB3","\uD83D\uDEB1","\uD83D\uDD1E","\uD83D\uDCF5","\uD83D\uDEAD","❗","❕","❓","❔","‼️","⁉️","\uD83D\uDD05","\uD83D\uDD06","〽️","⚠️","\uD83D\uDEB8","\uD83D\uDD31","⚜️","\uD83D\uDD30","♻️","✅","\uD83C\uDE2F","\uD83D\uDCB9","❇️","✳️","❎","\uD83C\uDF10","\uD83D\uDCA0","Ⓜ️","\uD83C\uDF00","\uD83D\uDCA4","\uD83C\uDFE7","\uD83D\uDEBE","♿","\uD83C\uDD7F️","\uD83D\uDED7","\uD83C\uDE33","\uD83C\uDE02️","\uD83D\uDEC2","\uD83D\uDEC3","\uD83D\uDEC4","\uD83D\uDEC5","\uD83D\uDEB9","\uD83D\uDEBA","\uD83D\uDEBC","⚧️","\uD83D\uDEBB","\uD83D\uDEAE","\uD83C\uDFA6","\uD83D\uDCF6","\uD83C\uDE01","\uD83D\uDD23","ℹ️","\uD83D\uDD24","\uD83D\uDD21","\uD83D\uDD20","\uD83C\uDD96","\uD83C\uDD97","\uD83C\uDD99","\uD83C\uDD92","\uD83C\uDD95","\uD83C\uDD93","0️⃣","1️⃣","2️⃣","3️⃣","4️⃣","5️⃣","6️⃣","7️⃣","8️⃣","9️⃣","\uD83D\uDD1F","\uD83D\uDD22","#️⃣","*️⃣","⏏️","▶️","⏸️","⏯️","⏹️","⏺️","⏭️","⏮️","⏩","⏪","⏫","⏬","◀️","\uD83D\uDD3C","\uD83D\uDD3D","➡️","⬅️","⬆️","⬇️","↗️","↘️","↙️","↖️","↕️","↔️","↪️","↩️","⤴️","⤵️","\uD83D\uDD00","\uD83D\uDD01","\uD83D\uDD02","\uD83D\uDD04","\uD83D\uDD03","\uD83C\uDFB5","\uD83C\uDFB6","➕","➖","➗","✖️","\uD83D\uDFF0","♾️","\uD83D\uDCB2","\uD83D\uDCB1","™️","\xa9️","\xae️","〰️","➰","➿","\uD83D\uDD1A","\uD83D\uDD19","\uD83D\uDD1B","\uD83D\uDD1D","\uD83D\uDD1C","✔️","☑️","\uD83D\uDD18","\uD83D\uDD34","\uD83D\uDFE0","\uD83D\uDFE1","\uD83D\uDFE2","\uD83D\uDD35","\uD83D\uDFE3","⚫","⚪","\uD83D\uDFE4","\uD83D\uDD3A","\uD83D\uDD3B","\uD83D\uDD38","\uD83D\uDD39","\uD83D\uDD36","\uD83D\uDD37","\uD83D\uDD33","\uD83D\uDD32","▪️","▫️","◾","◽","◼️","◻️","\uD83D\uDFE5","\uD83D\uDFE7","\uD83D\uDFE8","\uD83D\uDFE9","\uD83D\uDFE6","\uD83D\uDFEA","⬛","⬜","\uD83D\uDFEB","\uD83D\uDD08","\uD83D\uDD07","\uD83D\uDD09","\uD83D\uDD0A","\uD83D\uDD14","\uD83D\uDD15","\uD83D\uDCE3","\uD83D\uDCE2","\uD83D\uDC41️‍\uD83D\uDDE8️","\uD83D\uDCAC","\uD83D\uDCAD","\uD83D\uDDEF️","♠️","♣️","♥️","♦️","\uD83C\uDCCF","\uD83C\uDFB4","\uD83C\uDC04","\uD83D\uDD50","\uD83D\uDD51","\uD83D\uDD52","\uD83D\uDD53","\uD83D\uDD54","\uD83D\uDD55","\uD83D\uDD56","\uD83D\uDD57","\uD83D\uDD58","\uD83D\uDD59","\uD83D\uDD5A","\uD83D\uDD5B","\uD83D\uDD5C","\uD83D\uDD5D","\uD83D\uDD5E","\uD83D\uDD5F","\uD83D\uDD60","\uD83D\uDD61","\uD83D\uDD62","\uD83D\uDD63","\uD83D\uDD64","\uD83D\uDD65","\uD83D\uDD66","\uD83D\uDD67"]},flags:{name:"Flags",icon:"\uD83C\uDFC1",emojis:["\uD83C\uDFC1","\uD83D\uDEA9","\uD83C\uDF8C","\uD83C\uDFF4","\uD83C\uDFF3️","\uD83C\uDFF3️‍\uD83C\uDF08","\uD83C\uDFF3️‍⚧️","\uD83C\uDFF4‍☠️","\uD83C\uDDE6\uD83C\uDDE8","\uD83C\uDDE6\uD83C\uDDE9","\uD83C\uDDE6\uD83C\uDDEA","\uD83C\uDDE6\uD83C\uDDEB","\uD83C\uDDE6\uD83C\uDDEC","\uD83C\uDDE6\uD83C\uDDEE","\uD83C\uDDE6\uD83C\uDDF1","\uD83C\uDDE6\uD83C\uDDF2","\uD83C\uDDE6\uD83C\uDDF4","\uD83C\uDDE6\uD83C\uDDF6","\uD83C\uDDE6\uD83C\uDDF7","\uD83C\uDDE6\uD83C\uDDF8","\uD83C\uDDE6\uD83C\uDDF9","\uD83C\uDDE6\uD83C\uDDFA","\uD83C\uDDE6\uD83C\uDDFC","\uD83C\uDDE6\uD83C\uDDFD","\uD83C\uDDE6\uD83C\uDDFF","\uD83C\uDDE7\uD83C\uDDE6","\uD83C\uDDE7\uD83C\uDDE7","\uD83C\uDDE7\uD83C\uDDE9","\uD83C\uDDE7\uD83C\uDDEA","\uD83C\uDDE7\uD83C\uDDEB","\uD83C\uDDE7\uD83C\uDDEC","\uD83C\uDDE7\uD83C\uDDED","\uD83C\uDDE7\uD83C\uDDEE","\uD83C\uDDE7\uD83C\uDDEF","\uD83C\uDDE7\uD83C\uDDF1","\uD83C\uDDE7\uD83C\uDDF2","\uD83C\uDDE7\uD83C\uDDF3","\uD83C\uDDE7\uD83C\uDDF4","\uD83C\uDDE7\uD83C\uDDF6","\uD83C\uDDE7\uD83C\uDDF7","\uD83C\uDDE7\uD83C\uDDF8","\uD83C\uDDE7\uD83C\uDDF9","\uD83C\uDDE7\uD83C\uDDFB","\uD83C\uDDE7\uD83C\uDDFC","\uD83C\uDDE7\uD83C\uDDFE","\uD83C\uDDE7\uD83C\uDDFF","\uD83C\uDDE8\uD83C\uDDE6","\uD83C\uDDE8\uD83C\uDDE8","\uD83C\uDDE8\uD83C\uDDE9","\uD83C\uDDE8\uD83C\uDDEB","\uD83C\uDDE8\uD83C\uDDEC","\uD83C\uDDE8\uD83C\uDDED","\uD83C\uDDE8\uD83C\uDDEE","\uD83C\uDDE8\uD83C\uDDF0","\uD83C\uDDE8\uD83C\uDDF1","\uD83C\uDDE8\uD83C\uDDF2","\uD83C\uDDE8\uD83C\uDDF3","\uD83C\uDDE8\uD83C\uDDF4","\uD83C\uDDE8\uD83C\uDDF5","\uD83C\uDDE8\uD83C\uDDF7","\uD83C\uDDE8\uD83C\uDDFA","\uD83C\uDDE8\uD83C\uDDFB","\uD83C\uDDE8\uD83C\uDDFC","\uD83C\uDDE8\uD83C\uDDFD","\uD83C\uDDE8\uD83C\uDDFE","\uD83C\uDDE8\uD83C\uDDFF","\uD83C\uDDE9\uD83C\uDDEA","\uD83C\uDDE9\uD83C\uDDEC","\uD83C\uDDE9\uD83C\uDDEF","\uD83C\uDDE9\uD83C\uDDF0","\uD83C\uDDE9\uD83C\uDDF2","\uD83C\uDDE9\uD83C\uDDF4","\uD83C\uDDE9\uD83C\uDDFF","\uD83C\uDDEA\uD83C\uDDE6","\uD83C\uDDEA\uD83C\uDDE8","\uD83C\uDDEA\uD83C\uDDEA","\uD83C\uDDEA\uD83C\uDDEC","\uD83C\uDDEA\uD83C\uDDED","\uD83C\uDDEA\uD83C\uDDF7","\uD83C\uDDEA\uD83C\uDDF8","\uD83C\uDDEA\uD83C\uDDF9","\uD83C\uDDEA\uD83C\uDDFA","\uD83C\uDDEB\uD83C\uDDEE","\uD83C\uDDEB\uD83C\uDDEF","\uD83C\uDDEB\uD83C\uDDF0","\uD83C\uDDEB\uD83C\uDDF2","\uD83C\uDDEB\uD83C\uDDF4","\uD83C\uDDEB\uD83C\uDDF7","\uD83C\uDDEC\uD83C\uDDE6","\uD83C\uDDEC\uD83C\uDDE7","\uD83C\uDDEC\uD83C\uDDE9","\uD83C\uDDEC\uD83C\uDDEA","\uD83C\uDDEC\uD83C\uDDEB","\uD83C\uDDEC\uD83C\uDDEC","\uD83C\uDDEC\uD83C\uDDED","\uD83C\uDDEC\uD83C\uDDEE","\uD83C\uDDEC\uD83C\uDDF1","\uD83C\uDDEC\uD83C\uDDF2","\uD83C\uDDEC\uD83C\uDDF3","\uD83C\uDDEC\uD83C\uDDF5","\uD83C\uDDEC\uD83C\uDDF6","\uD83C\uDDEC\uD83C\uDDF7","\uD83C\uDDEC\uD83C\uDDF8","\uD83C\uDDEC\uD83C\uDDF9","\uD83C\uDDEC\uD83C\uDDFA","\uD83C\uDDEC\uD83C\uDDFC","\uD83C\uDDEC\uD83C\uDDFE","\uD83C\uDDED\uD83C\uDDF0","\uD83C\uDDED\uD83C\uDDF2","\uD83C\uDDED\uD83C\uDDF3","\uD83C\uDDED\uD83C\uDDF7","\uD83C\uDDED\uD83C\uDDF9","\uD83C\uDDED\uD83C\uDDFA","\uD83C\uDDEE\uD83C\uDDE8","\uD83C\uDDEE\uD83C\uDDE9","\uD83C\uDDEE\uD83C\uDDEA","\uD83C\uDDEE\uD83C\uDDF1","\uD83C\uDDEE\uD83C\uDDF2","\uD83C\uDDEE\uD83C\uDDF3","\uD83C\uDDEE\uD83C\uDDF4","\uD83C\uDDEE\uD83C\uDDF6","\uD83C\uDDEE\uD83C\uDDF7","\uD83C\uDDEE\uD83C\uDDF8","\uD83C\uDDEE\uD83C\uDDF9","\uD83C\uDDEF\uD83C\uDDEA","\uD83C\uDDEF\uD83C\uDDF2","\uD83C\uDDEF\uD83C\uDDF4","\uD83C\uDDEF\uD83C\uDDF5","\uD83C\uDDF0\uD83C\uDDEA","\uD83C\uDDF0\uD83C\uDDEC","\uD83C\uDDF0\uD83C\uDDED","\uD83C\uDDF0\uD83C\uDDEE","\uD83C\uDDF0\uD83C\uDDF2","\uD83C\uDDF0\uD83C\uDDF3","\uD83C\uDDF0\uD83C\uDDF5","\uD83C\uDDF0\uD83C\uDDF7","\uD83C\uDDF0\uD83C\uDDFC","\uD83C\uDDF0\uD83C\uDDFE","\uD83C\uDDF0\uD83C\uDDFF","\uD83C\uDDF1\uD83C\uDDE6","\uD83C\uDDF1\uD83C\uDDE7","\uD83C\uDDF1\uD83C\uDDE8","\uD83C\uDDF1\uD83C\uDDEE","\uD83C\uDDF1\uD83C\uDDF0","\uD83C\uDDF1\uD83C\uDDF7","\uD83C\uDDF1\uD83C\uDDF8","\uD83C\uDDF1\uD83C\uDDF9","\uD83C\uDDF1\uD83C\uDDFA","\uD83C\uDDF1\uD83C\uDDFB","\uD83C\uDDF1\uD83C\uDDFE","\uD83C\uDDF2\uD83C\uDDE6","\uD83C\uDDF2\uD83C\uDDE8","\uD83C\uDDF2\uD83C\uDDE9","\uD83C\uDDF2\uD83C\uDDEA","\uD83C\uDDF2\uD83C\uDDEB","\uD83C\uDDF2\uD83C\uDDEC","\uD83C\uDDF2\uD83C\uDDED","\uD83C\uDDF2\uD83C\uDDF0","\uD83C\uDDF2\uD83C\uDDF1","\uD83C\uDDF2\uD83C\uDDF2","\uD83C\uDDF2\uD83C\uDDF3","\uD83C\uDDF2\uD83C\uDDF4","\uD83C\uDDF2\uD83C\uDDF5","\uD83C\uDDF2\uD83C\uDDF6","\uD83C\uDDF2\uD83C\uDDF7","\uD83C\uDDF2\uD83C\uDDF8","\uD83C\uDDF2\uD83C\uDDF9","\uD83C\uDDF2\uD83C\uDDFA","\uD83C\uDDF2\uD83C\uDDFB","\uD83C\uDDF2\uD83C\uDDFC","\uD83C\uDDF2\uD83C\uDDFD","\uD83C\uDDF2\uD83C\uDDFE","\uD83C\uDDF2\uD83C\uDDFF","\uD83C\uDDF3\uD83C\uDDE6","\uD83C\uDDF3\uD83C\uDDE8","\uD83C\uDDF3\uD83C\uDDEA","\uD83C\uDDF3\uD83C\uDDEB","\uD83C\uDDF3\uD83C\uDDEC","\uD83C\uDDF3\uD83C\uDDEE","\uD83C\uDDF3\uD83C\uDDF1","\uD83C\uDDF3\uD83C\uDDF4","\uD83C\uDDF3\uD83C\uDDF5","\uD83C\uDDF3\uD83C\uDDF7","\uD83C\uDDF3\uD83C\uDDFA","\uD83C\uDDF3\uD83C\uDDFF","\uD83C\uDDF4\uD83C\uDDF2","\uD83C\uDDF5\uD83C\uDDE6","\uD83C\uDDF5\uD83C\uDDEA","\uD83C\uDDF5\uD83C\uDDEB","\uD83C\uDDF5\uD83C\uDDEC","\uD83C\uDDF5\uD83C\uDDED","\uD83C\uDDF5\uD83C\uDDF0","\uD83C\uDDF5\uD83C\uDDF1","\uD83C\uDDF5\uD83C\uDDF2","\uD83C\uDDF5\uD83C\uDDF3","\uD83C\uDDF5\uD83C\uDDF7","\uD83C\uDDF5\uD83C\uDDF8","\uD83C\uDDF5\uD83C\uDDF9","\uD83C\uDDF5\uD83C\uDDFC","\uD83C\uDDF5\uD83C\uDDFE","\uD83C\uDDF6\uD83C\uDDE6","\uD83C\uDDF7\uD83C\uDDEA","\uD83C\uDDF7\uD83C\uDDF4","\uD83C\uDDF7\uD83C\uDDF8","\uD83C\uDDF7\uD83C\uDDFA","\uD83C\uDDF7\uD83C\uDDFC","\uD83C\uDDF8\uD83C\uDDE6","\uD83C\uDDF8\uD83C\uDDE7","\uD83C\uDDF8\uD83C\uDDE8","\uD83C\uDDF8\uD83C\uDDE9","\uD83C\uDDF8\uD83C\uDDEA","\uD83C\uDDF8\uD83C\uDDEC","\uD83C\uDDF8\uD83C\uDDED","\uD83C\uDDF8\uD83C\uDDEE","\uD83C\uDDF8\uD83C\uDDEF","\uD83C\uDDF8\uD83C\uDDF0","\uD83C\uDDF8\uD83C\uDDF1","\uD83C\uDDF8\uD83C\uDDF2","\uD83C\uDDF8\uD83C\uDDF3","\uD83C\uDDF8\uD83C\uDDF4","\uD83C\uDDF8\uD83C\uDDF7","\uD83C\uDDF8\uD83C\uDDF8","\uD83C\uDDF8\uD83C\uDDF9","\uD83C\uDDF8\uD83C\uDDFB","\uD83C\uDDF8\uD83C\uDDFD","\uD83C\uDDF8\uD83C\uDDFE","\uD83C\uDDF8\uD83C\uDDFF","\uD83C\uDDF9\uD83C\uDDE6","\uD83C\uDDF9\uD83C\uDDE8","\uD83C\uDDF9\uD83C\uDDE9","\uD83C\uDDF9\uD83C\uDDEB","\uD83C\uDDF9\uD83C\uDDEC","\uD83C\uDDF9\uD83C\uDDED","\uD83C\uDDF9\uD83C\uDDEF","\uD83C\uDDF9\uD83C\uDDF0","\uD83C\uDDF9\uD83C\uDDF1","\uD83C\uDDF9\uD83C\uDDF2","\uD83C\uDDF9\uD83C\uDDF3","\uD83C\uDDF9\uD83C\uDDF4","\uD83C\uDDF9\uD83C\uDDF7","\uD83C\uDDF9\uD83C\uDDF9","\uD83C\uDDF9\uD83C\uDDFB","\uD83C\uDDF9\uD83C\uDDFC","\uD83C\uDDF9\uD83C\uDDFF","\uD83C\uDDFA\uD83C\uDDE6","\uD83C\uDDFA\uD83C\uDDEC","\uD83C\uDDFA\uD83C\uDDF2","\uD83C\uDDFA\uD83C\uDDF3","\uD83C\uDDFA\uD83C\uDDF8","\uD83C\uDDFA\uD83C\uDDFE","\uD83C\uDDFA\uD83C\uDDFF","\uD83C\uDDFB\uD83C\uDDE6","\uD83C\uDDFB\uD83C\uDDE8","\uD83C\uDDFB\uD83C\uDDEA","\uD83C\uDDFB\uD83C\uDDEC","\uD83C\uDDFB\uD83C\uDDEE","\uD83C\uDDFB\uD83C\uDDF3","\uD83C\uDDFB\uD83C\uDDFA","\uD83C\uDDFC\uD83C\uDDEB","\uD83C\uDDFC\uD83C\uDDF8","\uD83C\uDDFD\uD83C\uDDF0","\uD83C\uDDFE\uD83C\uDDEA","\uD83C\uDDFE\uD83C\uDDF9","\uD83C\uDDFF\uD83C\uDDE6","\uD83C\uDDFF\uD83C\uDDF2","\uD83C\uDDFF\uD83C\uDDFC","\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62\uDB40\uDC65\uDB40\uDC6E\uDB40\uDC67\uDB40\uDC7F","\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62\uDB40\uDC73\uDB40\uDC63\uDB40\uDC74\uDB40\uDC7F","\uD83C\uDFF4\uDB40\uDC67\uDB40\uDC62\uDB40\uDC77\uDB40\uDC6C\uDB40\uDC73\uDB40\uDC7F"]}},f=["#ef4444","#f97316","#f59e0b","#eab308","#84cc16","#22c55e","#10b981","#14b8a6","#06b6d4","#0ea5e9","#3b82f6","#6366f1","#8b5cf6","#a855f7","#d946ef","#ec4899","#f43f5e","#64748b","#6b7280","#374151","#111827","#000000","#ffffff","#f8fafc"],p=({children:e,agentId:t,currentEmoji:a,currentColor:p,onStyleChange:h})=>{let[g,x]=(0,n.useState)(p||"#3b82f6"),[v,b]=(0,n.useState)(a||"\uD83D\uDE00"),[w,y]=(0,n.useState)(!1),[j,N]=(0,n.useState)(""),[C,S]=(0,n.useState)("smileys");s().useEffect(()=>{p&&x(p)},[p]),s().useEffect(()=>{a&&b(a)},[a]);let _=(0,n.useMemo)(()=>j?Object.values(m).flatMap(e=>e.emojis).filter(e=>e.includes(j)):m[C].emojis,[j,C]);return(0,r.jsxs)(l.AM,{open:w,onOpenChange:y,children:[(0,r.jsx)(l.Wv,{asChild:!0,children:e}),(0,r.jsx)(l.hl,{className:"w-96 p-0",align:"start",children:(0,r.jsxs)(i.Zp,{className:"border-0 shadow-none",children:[(0,r.jsxs)(i.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-6 h-6 rounded-full border-2 border-gray-200",style:{backgroundColor:g}}),(0,r.jsx)("span",{className:"font-medium",children:"Color"})]}),(0,r.jsx)("div",{className:"grid grid-cols-8 gap-2",children:f.map(e=>(0,r.jsx)("button",{className:`w-8 h-8 rounded-lg border-2 transition-all hover:scale-110 ${g===e?"border-gray-900 shadow-md":"border-gray-200 hover:border-gray-300"}`,style:{backgroundColor:e},onClick:()=>x(e)},e))}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("input",{type:"color",value:g,onChange:e=>x(e.target.value),className:"w-8 h-8 rounded border cursor-pointer"}),(0,r.jsx)("input",{type:"text",value:g,onChange:e=>x(e.target.value),className:"flex-1 px-2 py-1 text-sm border rounded focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"#000000"})]})]}),(0,r.jsx)(d.w,{}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-xl",children:v}),(0,r.jsx)("span",{className:"font-medium",children:"Emoji"})]}),!j&&(0,r.jsxs)(c.tU,{value:C,onValueChange:S,className:"w-full",children:[(0,r.jsx)(c.j7,{className:"grid w-full grid-cols-4 h-auto p-1",children:Object.entries(m).slice(0,4).map(([e,t])=>(0,r.jsx)(c.Xi,{value:e,className:"text-xs p-1",children:(0,r.jsx)("span",{className:"text-sm",children:t.icon})},e))}),(0,r.jsx)(c.j7,{className:"grid w-full grid-cols-4 h-auto p-1 mt-1",children:Object.entries(m).slice(4,8).map(([e,t])=>(0,r.jsx)(c.Xi,{value:e,className:"text-xs p-1",children:(0,r.jsx)("span",{className:"text-sm",children:t.icon})},e))})]}),(0,r.jsx)(u.F,{className:"h-48 w-full",children:(0,r.jsx)("div",{className:"grid grid-cols-8 gap-1 p-1",children:_.map((e,t)=>(0,r.jsx)("button",{className:`w-8 h-8 text-lg hover:bg-gray-100 rounded transition-colors ${v===e?"bg-blue-100 ring-2 ring-blue-500":""}`,onClick:()=>b(e),children:e},`${e}-${t}`))})}),j&&0===_.length&&(0,r.jsxs)("div",{className:"text-center text-muted-foreground py-4",children:['No emojis found for "',j,'"']})]}),(0,r.jsx)(d.w,{})]}),(0,r.jsxs)(i.wL,{className:"flex justify-end gap-2",children:[(0,r.jsx)(o.$,{variant:"outline",onClick:()=>{let e=["\uD83E\uDD16","\uD83C\uDFAF","⚡","\uD83D\uDE80","\uD83D\uDD2E","\uD83C\uDFA8","\uD83D\uDCCA","\uD83D\uDD27","\uD83D\uDCA1","\uD83C\uDF1F"],a=["#ef4444","#f97316","#f59e0b","#eab308","#84cc16","#22c55e","#10b981","#14b8a6","#06b6d4","#0ea5e9","#3b82f6","#6366f1","#8b5cf6","#a855f7"],r=t.split("").reduce((e,t)=>e+t.charCodeAt(0),0)%e.length,n=t.split("").reduce((e,t)=>e+t.charCodeAt(0),0)%a.length;b(e[r]),x(a[n]),N(""),S("smileys")},children:"Reset"}),(0,r.jsx)(o.$,{onClick:()=>{h(v,g),y(!1)},children:"Save"})]})]})})]})}},51455:e=>{"use strict";e.exports=require("node:fs/promises")},51497:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{default:()=>F});var n=a(60687),s=a(43210),o=a(16189),l=a(48730),i=a(13964),d=a(12941),c=a(13861),u=a(56476),m=a(56085),f=a(45583),p=a(78200),h=a(48563),g=a(41862),x=a(29523),v=a(26110),b=a(96834),w=a(91821),y=a(48091),j=a(85763),N=a(39665),C=a(47242),S=a(52581),_=a(75191),k=a(29051),E=a(12879),A=a(45659),R=a(50173),P=a(55492),T=a(76242),I=a(66183),$=a(37797),D=a(35610),O=a(64371),M=e([k,I]);function F(){let e=(0,o.useParams)();(0,o.useRouter)();let t=e.agentId,{data:a,isLoading:r,error:M}=(0,N.fJ)(t),F=(0,N.Ae)(),{state:q,setOpen:U,setOpenMobile:L}=(0,P.cL)();(0,s.useRef)(!1);let[B,z]=(0,s.useState)({name:"",description:"",system_prompt:"",agentpress_tools:{},configured_mcps:[],custom_mcps:[],is_default:!1,avatar:"",avatar_color:""}),W=(0,s.useRef)(null),V=(0,s.useRef)(B),[H,G]=(0,s.useState)("idle"),Y=(0,s.useRef)(null),[X,Z]=(0,s.useState)(!1),[J,K]=(0,s.useState)("agent-builder"),Q=(0,s.useRef)(null),ee=(0,s.useCallback)((e,t)=>!t||e.name!==t.name||e.description!==t.description||e.system_prompt!==t.system_prompt||e.is_default!==t.is_default||e.avatar!==t.avatar||e.avatar_color!==t.avatar_color||JSON.stringify(e.agentpress_tools)!==JSON.stringify(t.agentpress_tools)||JSON.stringify(e.configured_mcps)!==JSON.stringify(t.configured_mcps)||JSON.stringify(e.custom_mcps)!==JSON.stringify(t.custom_mcps)||!1,[]),et=(0,s.useCallback)(async e=>{try{G("saving"),await F.mutateAsync({agentId:t,...e}),W.current={...e},G("saved"),setTimeout(()=>G("idle"),2e3)}catch(e){console.error("Error updating agent:",e),G("error"),S.oR.error("Failed to update agent"),setTimeout(()=>G("idle"),3e3)}},[t,F]),ea=(0,s.useCallback)(e=>{Y.current&&clearTimeout(Y.current),ee(e,W.current)&&(Y.current=setTimeout(()=>{ee(e,W.current)&&et(e)},500))},[et,ee]),er=(0,s.useCallback)((e,t)=>{let a={...V.current,[e]:t};z(a),ea(a)},[ea]),en=(0,s.useCallback)(e=>{let t={...V.current,configured_mcps:e.configured_mcps,custom_mcps:e.custom_mcps};z(t),ea(t)},[ea]),es=(0,s.useCallback)(()=>{Q.current&&Q.current.scrollIntoView({behavior:"smooth",block:"end"})},[]),eo=(0,s.useCallback)((e,t)=>{let a={...V.current,avatar:e,avatar_color:t};z(a),ea(a)},[ea]),el=(0,s.useMemo)(()=>B.avatar&&B.avatar_color?{avatar:B.avatar,color:B.avatar_color}:(0,E.Z)(t),[B.avatar,B.avatar_color,t]),ei=(0,s.useMemo)(()=>(0,n.jsx)(I.X,{agentId:t,formData:B,handleFieldChange:er,handleStyleChange:eo,currentStyle:el}),[t,B,er,eo,el]),ed=()=>{let e="idle"===H&&!ee(B,W.current);switch(H){case"saving":return(0,n.jsxs)(b.E,{variant:"secondary",className:"flex items-center gap-1 text-amber-700 dark:text-amber-300 bg-amber-600/30 hover:bg-amber-700/40",children:[(0,n.jsx)(l.A,{className:"h-3 w-3 animate-pulse"}),"Saving..."]});case"saved":return(0,n.jsxs)(b.E,{variant:"default",className:"flex items-center gap-1 text-green-700 dark:text-green-300 bg-green-600/30 hover:bg-green-700/40",children:[(0,n.jsx)(i.A,{className:"h-3 w-3"}),"Saved"]});case"error":return(0,n.jsx)(b.E,{variant:"destructive",className:"flex items-center gap-1 text-red-700 dark:text-red-300 bg-red-600/30 hover:bg-red-700/40",children:"Error saving"});default:return e?(0,n.jsxs)(b.E,{variant:"default",className:"flex items-center gap-1 text-green-700 dark:text-green-300 bg-green-600/30 hover:bg-green-700/40",children:[(0,n.jsx)(i.A,{className:"h-3 w-3"}),"Saved"]}):(0,n.jsx)(b.E,{variant:"destructive",className:"flex items-center gap-1 text-red-700 dark:text-red-300 bg-red-600/30 hover:bg-red-700/40",children:"Error saving"})}},ec=(0,s.useMemo)(()=>(0,n.jsxs)("div",{className:"h-full flex flex-col",children:[(0,n.jsxs)("div",{className:"md:hidden flex justify-between items-center mb-4 p-4 pb-0",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)(T.m_,{children:[(0,n.jsx)(T.k$,{asChild:!0,children:(0,n.jsx)("button",{onClick:()=>L(!0),className:"h-8 w-8 flex items-center justify-center rounded-md hover:bg-accent",children:(0,n.jsx)(d.A,{className:"h-4 w-4"})})}),(0,n.jsx)(T.ZI,{children:"Open menu"})]}),(0,n.jsx)("div",{className:"md:hidden flex justify-center",children:ed()})]}),(0,n.jsxs)(y._s,{open:X,onOpenChange:Z,children:[(0,n.jsx)(y.Uz,{asChild:!0,children:(0,n.jsxs)(x.$,{variant:"outline",size:"sm",children:[(0,n.jsx)(c.A,{className:"h-4 w-4"}),"Preview"]})}),(0,n.jsxs)(y.zj,{className:"h-[90vh] bg-muted",children:[(0,n.jsx)(y.BE,{children:(0,n.jsx)(y.gk,{children:"Agent Preview"})}),(0,n.jsx)("div",{className:"flex-1 overflow-y-auto px-4 pb-4",children:(0,n.jsx)(k.i,{agent:{...a,...B}})})]})]})]}),(0,n.jsxs)(j.tU,{value:J,onValueChange:K,className:"flex-1 flex flex-col overflow-hidden",children:[(0,n.jsx)("div",{className:"w-full flex items-center justify-center flex-shrink-0 px-4 md:px-12 md:mt-10",children:(0,n.jsx)("div",{className:"w-auto flex items-center gap-2",children:(0,n.jsxs)(j.j7,{className:"grid h-auto w-full grid-cols-2 bg-muted-foreground/10",children:[(0,n.jsx)(j.Xi,{value:"agent-builder",className:"w-48 flex items-center gap-1.5 px-2",children:(0,n.jsx)("span",{className:"truncate",children:"Prompt to configure"})}),(0,n.jsx)(j.Xi,{value:"manual",children:"Config"})]})})}),(0,n.jsx)(j.av,{value:"manual",className:"mt-0 flex-1 overflow-y-auto overflow-x-hidden px-4 md:px-12 pb-4 md:pb-12 scrollbar-hide",children:(0,n.jsxs)("div",{className:"max-w-full",children:[(0,n.jsx)("div",{className:"hidden md:flex justify-end mb-4 mt-4",children:ed()}),(0,n.jsxs)("div",{className:"flex items-start md:items-center flex-col md:flex-row mt-6",children:[(0,n.jsx)(R.U,{agentId:t,currentEmoji:el.avatar,currentColor:el.color,onStyleChange:eo,children:(0,n.jsx)("div",{className:"flex-shrink-0 h-12 w-12 md:h-16 md:w-16 flex items-center justify-center rounded-2xl text-xl md:text-2xl cursor-pointer hover:opacity-80 transition-opacity mb-3 md:mb-0",style:{backgroundColor:el.color},children:el.avatar})}),(0,n.jsxs)("div",{className:"flex flex-col md:ml-3 w-full min-w-0",children:[(0,n.jsx)(A.w,{value:B.name,onSave:e=>er("name",e),className:"text-lg md:text-xl font-semibold bg-transparent",placeholder:"Click to add agent name..."}),(0,n.jsx)(A.w,{value:B.description,onSave:e=>er("description",e),className:"text-muted-foreground text-sm md:text-base",placeholder:"Click to add description..."})]})]}),(0,n.jsxs)("div",{className:"flex flex-col mt-6 md:mt-8",children:[(0,n.jsx)("div",{className:"text-sm font-semibold text-muted-foreground mb-2",children:"Instructions"}),(0,n.jsx)(A.w,{value:B.system_prompt,onSave:e=>er("system_prompt",e),className:"bg-transparent hover:bg-transparent border-none focus-visible:ring-0 shadow-none text-sm md:text-base",placeholder:"Click to set system instructions...",multiline:!0,minHeight:"150px"})]}),(0,n.jsx)("div",{ref:Q,className:"mt-6 border-t",children:(0,n.jsxs)(v.nD,{type:"multiple",defaultValue:[],className:"space-y-2",onValueChange:es,children:[(0,n.jsxs)(v.As,{value:"tools",className:"border-b",children:[(0,n.jsx)(v.$m,{className:"hover:no-underline text-sm md:text-base",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(u.A,{className:"h-4 w-4"}),"Default Tools"]})}),(0,n.jsx)(v.ub,{className:"pb-4 overflow-x-hidden",children:(0,n.jsx)(_.b,{tools:B.agentpress_tools,onToolsChange:e=>er("agentpress_tools",e)})})]}),(0,n.jsxs)(v.As,{value:"mcp",className:"border-b",children:[(0,n.jsx)(v.$m,{className:"hover:no-underline text-sm md:text-base",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(m.A,{className:"h-4 w-4"}),"Integrations & MCPs",(0,n.jsx)(b.E,{variant:"new",children:"New"})]})}),(0,n.jsx)(v.ub,{className:"pb-4 overflow-x-hidden",children:(0,n.jsx)(C.g,{configuredMCPs:B.configured_mcps,customMCPs:B.custom_mcps,onMCPChange:en,agentId:t})})]}),(0,n.jsxs)(v.As,{value:"triggers",className:"border-b",children:[(0,n.jsx)(v.$m,{className:"hover:no-underline text-sm md:text-base",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(f.A,{className:"h-4 w-4"}),"Triggers",(0,n.jsx)(b.E,{variant:"new",children:"New"})]})}),(0,n.jsx)(v.ub,{className:"pb-4 overflow-x-hidden",children:(0,n.jsx)($.b,{agentId:t})})]}),(0,n.jsxs)(v.As,{value:"knowledge-base",className:"border-b",children:[(0,n.jsx)(v.$m,{className:"hover:no-underline text-sm md:text-base",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(p.A,{className:"h-4 w-4"}),"Knowledge Base",(0,n.jsx)(b.E,{variant:"new",children:"New"})]})}),(0,n.jsx)(v.ub,{className:"pb-4 overflow-x-hidden",children:(0,n.jsx)(D.Y,{agentId:t,agentName:B.name||"Agent"})})]}),(0,n.jsxs)(v.As,{value:"workflows",className:"border-b",children:[(0,n.jsx)(v.$m,{className:"hover:no-underline text-sm md:text-base",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(h.A,{className:"h-4 w-4"}),"Workflows",(0,n.jsx)(b.E,{variant:"new",children:"New"})]})}),(0,n.jsx)(v.ub,{className:"pb-4 overflow-x-hidden",children:(0,n.jsx)(O.E,{agentId:t,agentName:B.name||"Agent"})})]})]})})]})}),(0,n.jsx)(j.av,{value:"agent-builder",className:"mt-0 flex-1 flex flex-col overflow-hidden",children:ei})]})]}),[J,t,a,B,el,X,ei,er,eo,L,Z,K,es,ed,en]);if(r)return(0,n.jsx)("div",{className:"container mx-auto max-w-7xl px-4 py-8",children:(0,n.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(g.A,{className:"h-6 w-6 animate-spin"}),(0,n.jsx)("span",{children:"Loading agent..."})]})})});if(M||!a){let e=M instanceof Error?M.message:String(M),t=e.includes("Access denied")||e.includes("403");return(0,n.jsx)("div",{className:"container mx-auto max-w-7xl px-4 py-8",children:(0,n.jsx)("div",{className:"text-center space-y-4",children:t?(0,n.jsx)(w.Fc,{variant:"destructive",children:(0,n.jsx)(w.TN,{children:"You don't have permission to edit this agent. You can only edit agents that you created."})}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Agent not found"}),(0,n.jsx)("p",{className:"text-muted-foreground mb-4",children:"The agent you're looking for doesn't exist."})]})})})}return(0,n.jsx)("div",{className:"h-screen flex flex-col",children:(0,n.jsxs)("div",{className:"flex-1 flex overflow-hidden",children:[(0,n.jsxs)("div",{className:"hidden md:flex w-full h-full",children:[(0,n.jsx)("div",{className:"w-1/2 border-r bg-background h-full flex flex-col",children:ec}),(0,n.jsx)("div",{className:"w-1/2 overflow-y-auto",children:(0,n.jsx)(k.i,{agent:{...a,...B}})})]}),(0,n.jsx)("div",{className:"md:hidden w-full h-full flex flex-col",children:ec})]})})}[k,I]=M.then?(await M)():M,r()}catch(e){r(e)}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56527:e=>{"use strict";e.exports=import("shiki")},57975:e=>{"use strict";e.exports=require("node:util")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66183:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.d(t,{X:()=>w});var n=a(60687),s=a(43210),o=a.n(s),l=a(53060),i=a(96260),d=a(26408),c=a(56367),u=a(92798),m=a(19321),f=a(39665),p=a(52581),h=a(62185),g=a(8693),x=a(80116),v=a(12392),b=e([l,i]);[l,i]=b.then?(await b)():b;let w=o().memo(function({agentId:e,formData:t,handleFieldChange:a,handleStyleChange:r,currentStyle:o}){let[b,w]=(0,s.useState)(null),[y,j]=(0,s.useState)(null),[N,C]=(0,s.useState)([]),[S,_]=(0,s.useState)(""),[k,E]=(0,s.useState)("idle"),[A,R]=(0,s.useState)("idle"),[P,T]=(0,s.useState)(!1),[I,$]=(0,s.useState)(!1),D=(0,s.useRef)(null),O=(0,s.useRef)(null),M=(0,s.useRef)(0),F=(0,s.useRef)(!1),q=(0,s.useRef)(null);(0,s.useEffect)(()=>(console.log("[AgentBuilderChat] Component mounted"),()=>{console.log("[AgentBuilderChat] Component unmounted")}),[]),(0,s.useEffect)(()=>{null!==q.current&&q.current!==e&&(console.log("[AgentBuilderChat] Agent ID changed, resetting state"),F.current=!1,C([]),w(null),j(null),$(!1),M.current=0),q.current=e},[e]);let U=(0,m.u)(),L=(0,c.U)(),B=(0,u.WZ)(),z=(0,u.CV)(),W=(0,f.gc)(e),V=(0,g.jE)(),H=()=>{D.current?.scrollIntoView({behavior:"smooth"})};(0,s.useEffect)(()=>{N&&N.length>M.current&&H(),M.current=N?.length||0},[N,N?.length]),(0,s.useEffect)(()=>{if(W.data&&"success"===W.status&&!F.current){console.log("[AgentBuilderChat] Loading chat history for agent:",e);let{messages:t,thread_id:a}=W.data;if(t&&t.length>0){let e=t.filter(e=>"status"!==e.type).map(e=>({message_id:e.message_id||`msg-${Date.now()}-${Math.random()}`,thread_id:e.thread_id||a,type:e.type||"system",is_llm_message:!!e.is_llm_message,content:e.content||"",metadata:e.metadata||"{}",created_at:e.created_at||new Date().toISOString(),updated_at:e.updated_at||new Date().toISOString(),sequence:0}));C(e),$(e.length>0),M.current=e.length,a&&w(a)}F.current=!0}else"error"===W.status&&(console.error("[AgentBuilderChat] Error loading chat history:",W.error),F.current=!0)},[W.data,W.status,W.error,e]);let G=(0,s.useCallback)(e=>{C(t=>{if(t||(t=[]),"user"===e.type){let a=t.findIndex(t=>t.message_id.startsWith("temp-user-")&&t.content===e.content&&"user"===t.type);if(-1!==a){console.log("[AGENT BUILDER] Replacing optimistic message with real message");let r=[...t];return r[a]=e,r}}return t.some(t=>t.message_id===e.message_id)?t.map(t=>t.message_id===e.message_id?e:t):[...t,e]})},[]),Y=(0,s.useCallback)(t=>{switch(t){case"idle":case"completed":case"stopped":case"agent_not_running":case"error":case"failed":E("idle"),j(null),"completed"===t&&(R("saved"),V.invalidateQueries({queryKey:x._.lists()}),V.invalidateQueries({queryKey:x._.detail(e)}),V.invalidateQueries({queryKey:x._.builderChatHistory(e)}),setTimeout(()=>R("idle"),2e3));break;case"connecting":E("connecting");break;case"streaming":E("running")}},[]),X=(0,s.useCallback)(e=>{e.toLowerCase().includes("not found")||e.toLowerCase().includes("agent run is not running")||p.oR.error(`Stream Error: ${e}`)},[]),Z=(0,s.useCallback)(()=>{console.log("[AGENT BUILDER] Stream closed")},[]),{status:J,textContent:K,toolCall:Q,error:ee,agentRunId:et,startStreaming:ea,stopStreaming:er}=(0,d.Z)({onMessage:G,onStatusChange:Y,onError:X,onClose:Z},b,C);(0,s.useEffect)(()=>{y&&y!==et&&b&&ea(y)},[y,ea,et,b]);let en=async(t,a)=>{if(t.trim()||O.current?.getPendingFiles().length){T(!0),$(!0),R("saving");try{let r=O.current?.getPendingFiles()||[],n=new FormData;n.append("prompt",t),n.append("is_agent_builder",String(!0)),n.append("target_agent_id",e),r.forEach(e=>{let t=(0,v.L)(e.name);n.append("files",e,t)}),a?.model_name&&n.append("model_name",a.model_name),n.append("enable_thinking",String(a?.enable_thinking??!1)),n.append("reasoning_effort",a?.reasoning_effort??"low"),n.append("stream",String(a?.stream??!0)),n.append("enable_context_manager",String(a?.enable_context_manager??!1));let s=await U.mutateAsync(n);if(s.thread_id){w(s.thread_id),s.agent_run_id&&(console.log("[AGENT BUILDER] Setting agent run ID:",s.agent_run_id),j(s.agent_run_id));let e={message_id:`user-${Date.now()}`,thread_id:s.thread_id,type:"user",is_llm_message:!1,content:t,metadata:"{}",created_at:new Date().toISOString(),updated_at:new Date().toISOString(),sequence:0};C(t=>[...t,e])}O.current?.clearPendingFiles(),_("")}catch(e){e instanceof h.Ey?p.oR.error("Billing limit reached. Please upgrade your plan."):p.oR.error("Failed to start agent builder session"),$(!1),R("idle")}finally{T(!1)}}},es=(0,s.useCallback)(async(e,t)=>{if(!e.trim()||!b)return;T(!0),R("saving");let a={message_id:`temp-user-${Date.now()}-${Math.random()}`,thread_id:b,type:"user",is_llm_message:!1,content:e,metadata:"{}",created_at:new Date().toISOString(),updated_at:new Date().toISOString(),sequence:N.length};C(e=>[...e,a]),_("");try{let r=L.mutateAsync({threadId:b,message:e}),n=B.mutateAsync({threadId:b,options:t}),s=await Promise.allSettled([r,n]);if("rejected"===s[0].status)throw Error(`Failed to send message: ${s[0].reason?.message||s[0].reason}`);if("rejected"===s[1].status){let e=s[1].reason;if(e instanceof h.Ey){p.oR.error("Billing limit reached. Please upgrade your plan."),C(e=>e.filter(e=>e.message_id!==a.message_id));return}throw Error(`Failed to start agent: ${e?.message||e}`)}let o=s[1].value;j(o.agent_run_id)}catch(e){p.oR.error(e instanceof Error?e.message:"Operation failed"),C(e=>e.map(e=>e.message_id===a.message_id?{...e,message_id:`user-error-${Date.now()}`}:e)),R("idle")}finally{T(!1)}},[b,N?.length,L,B]),eo=(0,s.useCallback)(async()=>{if(E("idle"),await er(),y)try{await z.mutateAsync(y)}catch(e){console.error("[AGENT BUILDER] Error stopping agent:",e)}},[er,y,z]),el=(0,s.useCallback)(()=>{},[]);return(0,n.jsxs)("div",{className:"flex flex-col h-full",children:[(0,n.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,n.jsxs)("div",{className:"h-full overflow-y-auto scrollbar-hide",children:[(0,n.jsx)(i.u9,{messages:N||[],streamingTextContent:K,streamingToolCall:Q,agentStatus:k,handleToolClick:()=>{},handleOpenFileViewer:el,streamHookStatus:J,agentName:"Agent Builder",agentAvatar:"\uD83E\uDD16",emptyStateComponent:(0,n.jsxs)("div",{className:"mt-6 flex flex-col items-center text-center text-muted-foreground/80",children:[(0,n.jsx)("div",{className:"flex w-20 aspect-square items-center justify-center rounded-2xl bg-muted-foreground/10 p-4 mb-4",children:(0,n.jsx)("div",{className:"text-4xl",children:"\uD83E\uDD16"})}),(0,n.jsxs)("p",{className:"w-[60%] text-2xl",children:["I'm your ",(0,n.jsx)("span",{className:"text-primary/80 font-semibold",children:"Agent Builder"}),". Describe the exact workflows and tasks you want to automate, and I'll configure your agent to handle them."]})]})}),(0,n.jsx)("div",{ref:D})]})}),(0,n.jsx)("div",{className:"flex-shrink-0 md:pb-4 md:px-12 px-4",children:(0,n.jsx)(l.V,{ref:O,onSubmit:b?es:en,loading:P,placeholder:"Tell me how you'd like to configure your agent...",value:S,onChange:_,disabled:P,isAgentRunning:"running"===k||"connecting"===k,onStopAgent:eo,agentName:"Agent Builder",hideAttachments:!0,bgColor:"bg-muted-foreground/10",hideAgentSelection:!0})})]})},(e,t)=>e.agentId===t.agentId&&JSON.stringify(e.formData)===JSON.stringify(t.formData)&&e.currentStyle.avatar===t.currentStyle.avatar&&e.currentStyle.color===t.currentStyle.color&&e.handleFieldChange===t.handleFieldChange&&e.handleStyleChange===t.handleStyleChange);r()}catch(e){r(e)}})},73136:e=>{"use strict";e.exports=require("node:url")},74075:e=>{"use strict";e.exports=require("zlib")},75140:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s,metadata:()=>n});var r=a(37413);let n={title:"Create Agent | Kortix Suna",description:"Create an agent",openGraph:{title:"Create Agent | Kortix Suna",description:"Create an agent",type:"website"}};async function s({children:e}){return(0,r.jsx)(r.Fragment,{children:e})}},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79253:(e,t,a)=>{Promise.resolve().then(a.bind(a,37675))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82978:(e,t,a)=>{"use strict";a.d(t,{S:()=>R});var r=a(60687),n=a(43210),s=a(98599),o=a(11273),l=a(70569),i=a(65551),d=a(83721),c=a(18853),u=a(46059),m=a(14163),f="Checkbox",[p,h]=(0,o.A)(f),[g,x]=p(f);function v(e){let{__scopeCheckbox:t,checked:a,children:s,defaultChecked:o,disabled:l,form:d,name:c,onCheckedChange:u,required:m,value:p="on",internal_do_not_use_render:h}=e,[x,v]=(0,i.i)({prop:a,defaultProp:o??!1,onChange:u,caller:f}),[b,w]=n.useState(null),[y,j]=n.useState(null),N=n.useRef(!1),C=!b||!!d||!!b.closest("form"),S={checked:x,disabled:l,setChecked:v,control:b,setControl:w,name:c,form:d,value:p,hasConsumerStoppedPropagationRef:N,required:m,defaultChecked:!_(o)&&o,isFormControl:C,bubbleInput:y,setBubbleInput:j};return(0,r.jsx)(g,{scope:t,...S,children:"function"==typeof h?h(S):s})}var b="CheckboxTrigger",w=n.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:a,...o},i)=>{let{control:d,value:c,disabled:u,checked:f,required:p,setControl:h,setChecked:g,hasConsumerStoppedPropagationRef:v,isFormControl:w,bubbleInput:y}=x(b,e),j=(0,s.s)(i,h),N=n.useRef(f);return n.useEffect(()=>{let e=d?.form;if(e){let t=()=>g(N.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[d,g]),(0,r.jsx)(m.sG.button,{type:"button",role:"checkbox","aria-checked":_(f)?"mixed":f,"aria-required":p,"data-state":k(f),"data-disabled":u?"":void 0,disabled:u,value:c,...o,ref:j,onKeyDown:(0,l.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(a,e=>{g(e=>!!_(e)||!e),y&&w&&(v.current=e.isPropagationStopped(),v.current||e.stopPropagation())})})});w.displayName=b;var y=n.forwardRef((e,t)=>{let{__scopeCheckbox:a,name:n,checked:s,defaultChecked:o,required:l,disabled:i,value:d,onCheckedChange:c,form:u,...m}=e;return(0,r.jsx)(v,{__scopeCheckbox:a,checked:s,defaultChecked:o,disabled:i,required:l,onCheckedChange:c,name:n,form:u,value:d,internal_do_not_use_render:({isFormControl:e})=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(w,{...m,ref:t,__scopeCheckbox:a}),e&&(0,r.jsx)(S,{__scopeCheckbox:a})]})})});y.displayName=f;var j="CheckboxIndicator",N=n.forwardRef((e,t)=>{let{__scopeCheckbox:a,forceMount:n,...s}=e,o=x(j,a);return(0,r.jsx)(u.C,{present:n||_(o.checked)||!0===o.checked,children:(0,r.jsx)(m.sG.span,{"data-state":k(o.checked),"data-disabled":o.disabled?"":void 0,...s,ref:t,style:{pointerEvents:"none",...e.style}})})});N.displayName=j;var C="CheckboxBubbleInput",S=n.forwardRef(({__scopeCheckbox:e,...t},a)=>{let{control:o,hasConsumerStoppedPropagationRef:l,checked:i,defaultChecked:u,required:f,disabled:p,name:h,value:g,form:v,bubbleInput:b,setBubbleInput:w}=x(C,e),y=(0,s.s)(a,w),j=(0,d.Z)(i),N=(0,c.X)(o);n.useEffect(()=>{if(!b)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!l.current;if(j!==i&&e){let a=new Event("click",{bubbles:t});b.indeterminate=_(i),e.call(b,!_(i)&&i),b.dispatchEvent(a)}},[b,j,i,l]);let S=n.useRef(!_(i)&&i);return(0,r.jsx)(m.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:u??S.current,required:f,disabled:p,name:h,value:g,form:v,...t,tabIndex:-1,ref:y,style:{...t.style,...N,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function _(e){return"indeterminate"===e}function k(e){return _(e)?"indeterminate":e?"checked":"unchecked"}S.displayName=C;var E=a(13964),A=a(4780);function R({className:e,...t}){return(0,r.jsx)(y,{"data-slot":"checkbox",className:(0,A.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,r.jsx)(N,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,r.jsx)(E.A,{className:"size-3.5"})})})}},84297:e=>{"use strict";e.exports=require("async_hooks")},89422:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(62688).A)("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]])},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},99133:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>s.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>i});var r=a(65239),n=a(48088),s=a(31369),o=a(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(t,l);let i={children:["",{children:["(dashboard)",{children:["agents",{children:["config",{children:["[agentId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,37675)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\config\\[agentId]\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,75140)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\config\\[agentId]\\layout.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,90170)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,33532)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(a.bind(a,88524))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,93595)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(a.bind(a,31369)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(a.bind(a,88524))).default(e)],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\config\\[agentId]\\page.tsx"],c={require:a,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(dashboard)/agents/config/[agentId]/page",pathname:"/agents/config/[agentId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})}};var t=require("../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[7719,5193,4267,7096,1265,3530,7156,7976,4097,6914,9307,5811,9781,9697,3667,8188,3806,1841,5558,6947,5966,1155,9490,3060,8607,4940,6110],()=>a(99133));module.exports=r})();