(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[499],{1954:e=>{e.exports=wasm_77d9faebf7af9e421806970ce10a58e9d83116d7},2094:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ImageResponse:()=>sk,experimental_FigmaImageResponse:()=>sF});var n,o,i,a,s,u,l,c,f,p,h,d,g,v,m,y,D=r(1954),b=r(9317),x=r(5356).<PERSON>uffer,w=Object.create,E=Object.defineProperty,S=Object.getOwnPropertyDescriptor,k=Object.getOwnPropertyNames,F=Object.getPrototypeOf,C=Object.prototype.hasOwnProperty,_=(e,t)=>function(){return t||(0,e[k(e)[0]])((t={exports:{}}).exports,t),t.exports},T=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of k(t))C.call(e,o)||o===r||E(e,o,{get:()=>t[o],enumerable:!(n=S(t,o))||n.enumerable});return e},O=(e,t,r)=>(r=null!=e?w(F(e)):{},T(!t&&e&&e.__esModule?r:E(r,"default",{value:e,enumerable:!0}),e)),A=_({"node_modules/.pnpm/tiny-inflate@1.0.3/node_modules/tiny-inflate/index.js"(e,t){function r(){this.table=new Uint16Array(16),this.trans=new Uint16Array(288)}function n(e,t){this.source=e,this.sourceIndex=0,this.tag=0,this.bitcount=0,this.dest=t,this.destLen=0,this.ltree=new r,this.dtree=new r}var o,i=new r,a=new r,s=new Uint8Array(30),u=new Uint16Array(30),l=new Uint8Array(30),c=new Uint16Array(30),f=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),p=new r,h=new Uint8Array(320);function d(e,t,r,n){var o,i;for(o=0;o<r;++o)e[o]=0;for(o=0;o<30-r;++o)e[o+r]=o/r|0;for(i=n,o=0;o<30;++o)t[o]=i,i+=1<<e[o]}var g=new Uint16Array(16);function v(e,t,r,n){var o,i;for(o=0;o<16;++o)e.table[o]=0;for(o=0;o<n;++o)e.table[t[r+o]]++;for(i=0,e.table[0]=0,o=0;o<16;++o)g[o]=i,i+=e.table[o];for(o=0;o<n;++o)t[r+o]&&(e.trans[g[t[r+o]]++]=o)}function m(e,t,r){if(!t)return r;for(;e.bitcount<24;)e.tag|=e.source[e.sourceIndex++]<<e.bitcount,e.bitcount+=8;var n=e.tag&65535>>>16-t;return e.tag>>>=t,e.bitcount-=t,n+r}function y(e,t){for(;e.bitcount<24;)e.tag|=e.source[e.sourceIndex++]<<e.bitcount,e.bitcount+=8;var r=0,n=0,o=0,i=e.tag;do n=2*n+(1&i),i>>>=1,++o,r+=t.table[o],n-=t.table[o];while(n>=0);return e.tag=i,e.bitcount-=o,t.trans[r+n]}function D(e,t,r){for(;;){var n,o,i,a,f=y(e,t);if(256===f)return 0;if(f<256)e.dest[e.destLen++]=f;else for(f-=257,n=m(e,s[f],u[f]),o=y(e,r),a=i=e.destLen-m(e,l[o],c[o]);a<i+n;++a)e.dest[e.destLen++]=e.dest[a]}}for(o=0;o<7;++o)i.table[o]=0;for(o=0,i.table[7]=24,i.table[8]=152,i.table[9]=112;o<24;++o)i.trans[o]=256+o;for(o=0;o<144;++o)i.trans[24+o]=o;for(o=0;o<8;++o)i.trans[168+o]=280+o;for(o=0;o<112;++o)i.trans[176+o]=144+o;for(o=0;o<5;++o)a.table[o]=0;for(o=0,a.table[5]=32;o<32;++o)a.trans[o]=o;d(s,u,4,3),d(l,c,2,1),s[28]=0,u[28]=258,t.exports=function(e,t){var r,o,s=new n(e,t);do{switch(r=function(e){e.bitcount--||(e.tag=e.source[e.sourceIndex++],e.bitcount=7);var t=1&e.tag;return e.tag>>>=1,t}(s),m(s,2,0)){case 0:o=function(e){for(var t,r,n;e.bitcount>8;)e.sourceIndex--,e.bitcount-=8;if((t=256*(t=e.source[e.sourceIndex+1])+e.source[e.sourceIndex])!==(65535&~(256*e.source[e.sourceIndex+3]+e.source[e.sourceIndex+2])))return -3;for(e.sourceIndex+=4,n=t;n;--n)e.dest[e.destLen++]=e.source[e.sourceIndex++];return e.bitcount=0,0}(s);break;case 1:o=D(s,i,a);break;case 2:!function(e,t,r){for(a=0,n=m(e,5,257),o=m(e,5,1),i=m(e,4,4);a<19;++a)h[a]=0;for(a=0;a<i;++a){var n,o,i,a,s,u,l=m(e,3,0);h[f[a]]=l}for(v(p,h,0,19),s=0;s<n+o;){var c=y(e,p);switch(c){case 16:var d=h[s-1];for(u=m(e,2,3);u;--u)h[s++]=d;break;case 17:for(u=m(e,3,3);u;--u)h[s++]=0;break;case 18:for(u=m(e,7,11);u;--u)h[s++]=0;break;default:h[s++]=c}}v(t,h,0,n),v(r,h,n,o)}(s,s.ltree,s.dtree),o=D(s,s.ltree,s.dtree);break;default:o=-3}if(0!==o)throw Error("Data error")}while(!r);if(s.destLen<s.dest.length)if("function"==typeof s.dest.slice)return s.dest.slice(0,s.destLen);else return s.dest.subarray(0,s.destLen);return s.dest}}}),P=_({"node_modules/.pnpm/unicode-trie@2.0.0/node_modules/unicode-trie/swap.js"(e,t){var r=18===new Uint8Array(new Uint32Array([0x12345678]).buffer)[0],n=(e,t,r)=>{let n=e[t];e[t]=e[r],e[r]=n},o=e=>{let t=e.length;for(let r=0;r<t;r+=4)n(e,r,r+3),n(e,r+1,r+2)};t.exports={swap32LE:e=>{r&&o(e)}}}}),L=_({"node_modules/.pnpm/unicode-trie@2.0.0/node_modules/unicode-trie/index.js"(e,t){var r=A(),{swap32LE:n}=P(),o=2112;t.exports=class{constructor(e){let t="function"==typeof e.readUInt32BE&&"function"==typeof e.slice;if(t||e instanceof Uint8Array){let o;if(t)this.highStart=e.readUInt32LE(0),this.errorValue=e.readUInt32LE(4),o=e.readUInt32LE(8),e=e.slice(12);else{let t=new DataView(e.buffer);this.highStart=t.getUint32(0,!0),this.errorValue=t.getUint32(4,!0),o=t.getUint32(8,!0),e=e.subarray(12)}e=r(e,new Uint8Array(o)),n(e=r(e,new Uint8Array(o))),this.data=new Uint32Array(e.buffer)}else({data:this.data,highStart:this.highStart,errorValue:this.errorValue}=e)}get(e){let t;return e<0||e>1114111?this.errorValue:e<55296||e>56319&&e<=65535?(t=(this.data[e>>5]<<2)+(31&e),this.data[t]):e<=65535?(t=(this.data[2048+(e-55296>>5)]<<2)+(31&e),this.data[t]):e<this.highStart?(t=this.data[o-32+(e>>11)],t=((t=this.data[t+(e>>5&63)])<<2)+(31&e),this.data[t]):this.data[this.data.length-4]}}}}),I=_({"node_modules/.pnpm/base64-js@0.0.8/node_modules/base64-js/lib/b64.js"(e){var t=void 0===e?e.base64js={}:e,r="undefined"!=typeof Uint8Array?Uint8Array:Array;function n(e){var t=e.charCodeAt(0);return 43===t||45===t?62:47===t||95===t?63:t<48?-1:t<58?t-48+26+26:t<91?t-65:t<123?t-97+26:void 0}t.toByteArray=function(e){if(e.length%4>0)throw Error("Invalid string. Length must be a multiple of 4");var t,o,i,a,s,u,l=e.length;s="="===e.charAt(l-2)?2:+("="===e.charAt(l-1)),u=new r(3*e.length/4-s),i=s>0?e.length-4:e.length;var c=0;function f(e){u[c++]=e}for(t=0,o=0;t<i;t+=4,o+=3)f((0xff0000&(a=n(e.charAt(t))<<18|n(e.charAt(t+1))<<12|n(e.charAt(t+2))<<6|n(e.charAt(t+3))))>>16),f((65280&a)>>8),f(255&a);return 2===s?f(255&(a=n(e.charAt(t))<<2|n(e.charAt(t+1))>>4)):1===s&&(f((a=n(e.charAt(t))<<10|n(e.charAt(t+1))<<4|n(e.charAt(t+2))>>2)>>8&255),f(255&a)),u},t.fromByteArray=function(e){var t,r,n,o,i=e.length%3,a="";function s(e){return"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charAt(e)}for(r=0,o=e.length-i;r<o;r+=3)a+=s((t=n=(e[r]<<16)+(e[r+1]<<8)+e[r+2])>>18&63)+s(t>>12&63)+s(t>>6&63)+s(63&t);switch(i){case 1:a+=s((n=e[e.length-1])>>2),a+=s(n<<4&63),a+="==";break;case 2:a+=s((n=(e[e.length-2]<<8)+e[e.length-1])>>10),a+=s(n>>4&63),a+=s(n<<2&63),a+="="}return a}}}),B=_({"node_modules/.pnpm/postcss-value-parser@4.2.0/node_modules/postcss-value-parser/lib/parse.js"(e,t){var r=/^[a-f0-9?-]+$/i;t.exports=function(e){for(var t,n,o,i,a,s,u,l,c,f=[],p=e,h=0,d=p.charCodeAt(h),g=p.length,v=[{nodes:f}],m=0,y="",D="",b="";h<g;)if(d<=32){t=h;do t+=1,d=p.charCodeAt(t);while(d<=32);i=p.slice(h,t),o=f[f.length-1],41===d&&m?b=i:o&&"div"===o.type?(o.after=i,o.sourceEndIndex+=i.length):44===d||58===d||47===d&&42!==p.charCodeAt(t+1)&&(!c||c&&"function"===c.type&&"calc"!==c.value)?D=i:f.push({type:"space",sourceIndex:h,sourceEndIndex:t,value:i}),h=t}else if(39===d||34===d){t=h,i={type:"string",sourceIndex:h,quote:n=39===d?"'":'"'};do if(a=!1,~(t=p.indexOf(n,t+1)))for(s=t;92===p.charCodeAt(s-1);)s-=1,a=!a;else p+=n,t=p.length-1,i.unclosed=!0;while(a);i.value=p.slice(h+1,t),i.sourceEndIndex=i.unclosed?t:t+1,f.push(i),h=t+1,d=p.charCodeAt(h)}else if(47===d&&42===p.charCodeAt(h+1))t=p.indexOf("*/",h),i={type:"comment",sourceIndex:h,sourceEndIndex:t+2},-1===t&&(i.unclosed=!0,t=p.length,i.sourceEndIndex=t),i.value=p.slice(h+2,t),f.push(i),h=t+2,d=p.charCodeAt(h);else if((47===d||42===d)&&c&&"function"===c.type&&"calc"===c.value)i=p[h],f.push({type:"word",sourceIndex:h-D.length,sourceEndIndex:h+i.length,value:i}),h+=1,d=p.charCodeAt(h);else if(47===d||44===d||58===d)i=p[h],f.push({type:"div",sourceIndex:h-D.length,sourceEndIndex:h+i.length,value:i,before:D,after:""}),D="",h+=1,d=p.charCodeAt(h);else if(40===d){t=h;do t+=1,d=p.charCodeAt(t);while(d<=32);if(l=h,i={type:"function",sourceIndex:h-y.length,value:y,before:p.slice(l+1,t)},h=t,"url"===y&&39!==d&&34!==d){t-=1;do if(a=!1,~(t=p.indexOf(")",t+1)))for(s=t;92===p.charCodeAt(s-1);)s-=1,a=!a;else p+=")",t=p.length-1,i.unclosed=!0;while(a);u=t;do u-=1,d=p.charCodeAt(u);while(d<=32);l<u?(h!==u+1?i.nodes=[{type:"word",sourceIndex:h,sourceEndIndex:u+1,value:p.slice(h,u+1)}]:i.nodes=[],i.unclosed&&u+1!==t?(i.after="",i.nodes.push({type:"space",sourceIndex:u+1,sourceEndIndex:t,value:p.slice(u+1,t)})):(i.after=p.slice(u+1,t),i.sourceEndIndex=t)):(i.after="",i.nodes=[]),h=t+1,i.sourceEndIndex=i.unclosed?t:h,d=p.charCodeAt(h),f.push(i)}else m+=1,i.after="",i.sourceEndIndex=h+1,f.push(i),v.push(i),f=i.nodes=[],c=i;y=""}else if(41===d&&m)h+=1,d=p.charCodeAt(h),c.after=b,c.sourceEndIndex+=b.length,b="",m-=1,v[v.length-1].sourceEndIndex=h,v.pop(),f=(c=v[m]).nodes;else{t=h;do 92===d&&(t+=1),t+=1,d=p.charCodeAt(t);while(t<g&&!(d<=32||39===d||34===d||44===d||58===d||47===d||40===d||42===d&&c&&"function"===c.type&&"calc"===c.value||47===d&&"function"===c.type&&"calc"===c.value||41===d&&m));i=p.slice(h,t),40===d?y=i:(117===i.charCodeAt(0)||85===i.charCodeAt(0))&&43===i.charCodeAt(1)&&r.test(i.slice(2))?f.push({type:"unicode-range",sourceIndex:h,sourceEndIndex:t,value:i}):f.push({type:"word",sourceIndex:h,sourceEndIndex:t,value:i}),h=t}for(h=v.length-1;h;h-=1)v[h].unclosed=!0,v[h].sourceEndIndex=p.length;return v[0].nodes}}}),R=_({"node_modules/.pnpm/postcss-value-parser@4.2.0/node_modules/postcss-value-parser/lib/walk.js"(e,t){t.exports=function e(t,r,n){var o,i,a,s;for(o=0,i=t.length;o<i;o+=1)a=t[o],n||(s=r(a,o,t)),!1!==s&&"function"===a.type&&Array.isArray(a.nodes)&&e(a.nodes,r,n),n&&r(a,o,t)}}}),U=_({"node_modules/.pnpm/postcss-value-parser@4.2.0/node_modules/postcss-value-parser/lib/stringify.js"(e,t){function r(e,t){var r,o,i=e.type,a=e.value;if(t&&void 0!==(o=t(e)))return o;if("word"===i||"space"===i);else if("string"===i)return(r=e.quote||"")+a+(e.unclosed?"":r);else if("comment"===i)return"/*"+a+(e.unclosed?"":"*/");else if("div"===i)return(e.before||"")+a+(e.after||"");else if(Array.isArray(e.nodes))return(r=n(e.nodes,t),"function"!==i)?r:a+"("+(e.before||"")+r+(e.after||"")+(e.unclosed?"":")");return a}function n(e,t){var n,o;if(Array.isArray(e)){for(n="",o=e.length-1;~o;o-=1)n=r(e[o],t)+n;return n}return r(e,t)}t.exports=n}}),M=_({"node_modules/.pnpm/postcss-value-parser@4.2.0/node_modules/postcss-value-parser/lib/unit.js"(e,t){t.exports=function(e){var t,r,n,o=0,i=e.length;if(0===i||!function(e){var t,r=e.charCodeAt(0);if(43===r||45===r){if((t=e.charCodeAt(1))>=48&&t<=57)return!0;var n=e.charCodeAt(2);return 46===t&&!!(n>=48)&&!!(n<=57)}return 46===r?!!((t=e.charCodeAt(1))>=48)&&!!(t<=57):!!(r>=48)&&!!(r<=57)}(e))return!1;for((43===(t=e.charCodeAt(o))||45===t)&&o++;o<i&&!((t=e.charCodeAt(o))<48)&&!(t>57);)o+=1;if(t=e.charCodeAt(o),r=e.charCodeAt(o+1),46===t&&r>=48&&r<=57)for(o+=2;o<i&&!((t=e.charCodeAt(o))<48)&&!(t>57);)o+=1;if(t=e.charCodeAt(o),r=e.charCodeAt(o+1),n=e.charCodeAt(o+2),(101===t||69===t)&&(r>=48&&r<=57||(43===r||45===r)&&n>=48&&n<=57))for(o+=43===r||45===r?3:2;o<i&&!((t=e.charCodeAt(o))<48)&&!(t>57);)o+=1;return{number:e.slice(0,o),unit:e.slice(o)}}}}),N=_({"node_modules/.pnpm/postcss-value-parser@4.2.0/node_modules/postcss-value-parser/lib/index.js"(e,t){var r=B(),n=R(),o=U();function i(e){return this instanceof i?(this.nodes=r(e),this):new i(e)}i.prototype.toString=function(){return Array.isArray(this.nodes)?o(this.nodes):""},i.prototype.walk=function(e,t){return n(this.nodes,e,t),this},i.unit=M(),i.walk=n,i.stringify=o,t.exports=i}}),j=_({"node_modules/.pnpm/camelize@1.0.0/node_modules/camelize/index.js"(e,t){function r(e){return e.replace(/[_.-](\w|$)/g,function(e,t){return t.toUpperCase()})}t.exports=function(e){return"string"==typeof e?r(e):function e(t){return!t||"object"!=typeof t||o(t)||i(t)?t:n(t)?function(e,t){if(e.map)return e.map(t);for(var r=[],n=0;n<e.length;n++)r.push(t(e[n],n));return r}(t,e):function(e,t,r){if(e.reduce)return e.reduce(t,r);for(var n=0;n<e.length;n++)r=t(r,e[n],n);return r}(s(t),function(n,o){return n[r(o)]=e(t[o]),n},{})}(e)};var n=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},o=function(e){return"[object Date]"===Object.prototype.toString.call(e)},i=function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},a=Object.prototype.hasOwnProperty,s=Object.keys||function(e){var t=[];for(var r in e)a.call(e,r)&&t.push(r);return t}}}),W=_({"node_modules/.pnpm/css-color-keywords@1.0.0/node_modules/css-color-keywords/colors.json"(e,t){t.exports={black:"#000000",silver:"#c0c0c0",gray:"#808080",white:"#ffffff",maroon:"#800000",red:"#ff0000",purple:"#800080",fuchsia:"#ff00ff",green:"#008000",lime:"#00ff00",olive:"#808000",yellow:"#ffff00",navy:"#000080",blue:"#0000ff",teal:"#008080",aqua:"#00ffff",orange:"#ffa500",aliceblue:"#f0f8ff",antiquewhite:"#faebd7",aquamarine:"#7fffd4",azure:"#f0ffff",beige:"#f5f5dc",bisque:"#ffe4c4",blanchedalmond:"#ffebcd",blueviolet:"#8a2be2",brown:"#a52a2a",burlywood:"#deb887",cadetblue:"#5f9ea0",chartreuse:"#7fff00",chocolate:"#d2691e",coral:"#ff7f50",cornflowerblue:"#6495ed",cornsilk:"#fff8dc",crimson:"#dc143c",darkblue:"#00008b",darkcyan:"#008b8b",darkgoldenrod:"#b8860b",darkgray:"#a9a9a9",darkgreen:"#006400",darkgrey:"#a9a9a9",darkkhaki:"#bdb76b",darkmagenta:"#8b008b",darkolivegreen:"#556b2f",darkorange:"#ff8c00",darkorchid:"#9932cc",darkred:"#8b0000",darksalmon:"#e9967a",darkseagreen:"#8fbc8f",darkslateblue:"#483d8b",darkslategray:"#2f4f4f",darkslategrey:"#2f4f4f",darkturquoise:"#00ced1",darkviolet:"#9400d3",deeppink:"#ff1493",deepskyblue:"#00bfff",dimgray:"#696969",dimgrey:"#696969",dodgerblue:"#1e90ff",firebrick:"#b22222",floralwhite:"#fffaf0",forestgreen:"#228b22",gainsboro:"#dcdcdc",ghostwhite:"#f8f8ff",gold:"#ffd700",goldenrod:"#daa520",greenyellow:"#adff2f",grey:"#808080",honeydew:"#f0fff0",hotpink:"#ff69b4",indianred:"#cd5c5c",indigo:"#4b0082",ivory:"#fffff0",khaki:"#f0e68c",lavender:"#e6e6fa",lavenderblush:"#fff0f5",lawngreen:"#7cfc00",lemonchiffon:"#fffacd",lightblue:"#add8e6",lightcoral:"#f08080",lightcyan:"#e0ffff",lightgoldenrodyellow:"#fafad2",lightgray:"#d3d3d3",lightgreen:"#90ee90",lightgrey:"#d3d3d3",lightpink:"#ffb6c1",lightsalmon:"#ffa07a",lightseagreen:"#20b2aa",lightskyblue:"#87cefa",lightslategray:"#778899",lightslategrey:"#778899",lightsteelblue:"#b0c4de",lightyellow:"#ffffe0",limegreen:"#32cd32",linen:"#faf0e6",mediumaquamarine:"#66cdaa",mediumblue:"#0000cd",mediumorchid:"#ba55d3",mediumpurple:"#9370db",mediumseagreen:"#3cb371",mediumslateblue:"#7b68ee",mediumspringgreen:"#00fa9a",mediumturquoise:"#48d1cc",mediumvioletred:"#c71585",midnightblue:"#191970",mintcream:"#f5fffa",mistyrose:"#ffe4e1",moccasin:"#ffe4b5",navajowhite:"#ffdead",oldlace:"#fdf5e6",olivedrab:"#6b8e23",orangered:"#ff4500",orchid:"#da70d6",palegoldenrod:"#eee8aa",palegreen:"#98fb98",paleturquoise:"#afeeee",palevioletred:"#db7093",papayawhip:"#ffefd5",peachpuff:"#ffdab9",peru:"#cd853f",pink:"#ffc0cb",plum:"#dda0dd",powderblue:"#b0e0e6",rosybrown:"#bc8f8f",royalblue:"#4169e1",saddlebrown:"#8b4513",salmon:"#fa8072",sandybrown:"#f4a460",seagreen:"#2e8b57",seashell:"#fff5ee",sienna:"#a0522d",skyblue:"#87ceeb",slateblue:"#6a5acd",slategray:"#708090",slategrey:"#708090",snow:"#fffafa",springgreen:"#00ff7f",steelblue:"#4682b4",tan:"#d2b48c",thistle:"#d8bfd8",tomato:"#ff6347",turquoise:"#40e0d0",violet:"#ee82ee",wheat:"#f5deb3",whitesmoke:"#f5f5f5",yellowgreen:"#9acd32",rebeccapurple:"#663399"}}}),G=_({"node_modules/.pnpm/css-color-keywords@1.0.0/node_modules/css-color-keywords/index.js"(e,t){t.exports=W()}}),$=_({"node_modules/.pnpm/css-to-react-native@3.0.0/node_modules/css-to-react-native/index.js"(e){function t(e){return e&&"object"==typeof e&&"default"in e?e.default:e}Object.defineProperty(e,"__esModule",{value:!0});var r,n=N(),o=t(n),i=t(j()),a=t(G()),s=/^(#(?:[0-9a-f]{3,4}){1,2})$/i,u=/^(rgba?|hsla?|hwb|lab|lch|gray|color)$/,l=function(e){return function(t){return e(t)?"<token>":null}},c=function(e,t){return void 0===t&&(t=String),function(r){if("word"!==r.type)return null;var n=r.value.match(e);return null===n?null:t(n[1])}},f=l(function(e){return"space"===e.type}),p=l(function(e){return"div"===e.type&&"/"===e.value}),h=l(function(e){return"div"===e.type&&","===e.value}),d=function(e){return"word"===e.type?e.value:null},g=c(/^(none)$/i),v=c(/^(auto)$/i),m=c(/^([+-]?(?:\d*\.)?\d+(?:e[+-]?\d+)?)$/i,Number),y=c(/^(0$|(?:[+-]?(?:\d*\.)?\d+(?:e[+-]?\d+)?)(?=px$))/i,Number),D=c(/^([+-]?(?:\d*\.)?\d+(?:e[+-]?\d+)?(ch|em|ex|rem|vh|vw|vmin|vmax|cm|mm|in|pc|pt))$/i),b=c(/^([+-]?(?:\d*\.)?\d+(?:e[+-]?\d+)?(?:deg|rad))$/i,function(e){return e.toLowerCase()}),x=c(/^([+-]?(?:\d*\.)?\d+(?:e[+-]?\d+)?%)$/i),w=c(/(^-?[_a-z][_a-z0-9-]*$)/i),E=function(e){return"string"!==e.type?null:e.value.replace(/\\([0-9a-f]{1,6})(?:\s|$)/gi,function(e,t){return String.fromCharCode(parseInt(t,16))}).replace(/\\/g,"")},S=function(e){return"word"===e.type&&(s.test(e.value)||e.value in a||"transparent"===e.value)?e.value:"function"===e.type&&u.test(e.value)?n.stringify(e):null},k=c(/^(none|underline|line-through)$/i),F=c(/^(solid|dashed|dotted)$/),C=function(e){var t=e.types,r=void 0===t?[y,D,x]:t,n=e.directions,o=void 0===n?["Top","Right","Bottom","Left"]:n,i=e.prefix,a=void 0===i?"":i,s=e.suffix,u=void 0===s?"":s;return function(e){var t,n=[];for(n.push(e.expect.apply(e,r));n.length<4&&e.hasTokens();)e.expect(f),n.push(e.expect.apply(e,r));e.expectEmpty();var i=n[0],s=n[1],l=void 0===s?i:s,c=n[2],p=n[3],h=void 0===p?l:p,d=function(e){return""+a+o[e]+u};return(t={})[d(0)]=i,t[d(1)]=l,t[d(2)]=void 0===c?i:c,t[d(3)]=h,t}},_=function(e){var t=e.expect(y),r=e.matches(f)?e.expect(y):t;return e.expectEmpty(),{width:t,height:r}},T=function(e){if(e.matches(g))return e.expectEmpty(),{offset:{width:0,height:0},radius:0,color:"black"};for(var t,r,n,o,i=!1;e.hasTokens();)i&&e.expect(f),void 0===t&&e.matches(y,D)?(t=e.lastValue,e.expect(f),r=e.expect(y,D),e.saveRewindPoint(),e.matches(f)&&e.matches(y,D)?n=e.lastValue:e.rewind()):void 0===o&&e.matches(S)?o=e.lastValue:e.throw(),i=!0;return void 0===t&&e.throw(),{offset:{width:t,height:r},radius:void 0!==n?n:0,color:void 0!==o?o:"black"}},O=c(/(nowrap|wrap|wrap-reverse)/),A=c(/(row|row-reverse|column|column-reverse)/),P=function(e){var t;if(e.matches(E))t=e.lastValue;else for(t=e.expect(w);e.hasTokens();)e.expect(f),t+=" "+e.expect(w);return e.expectEmpty(),{fontFamily:t}},L=c(/^(normal)$/),I=c(/^(italic)$/),B=c(/^([1-9]00|bold)$/),R=c(/^(small-caps)$/),U=[],M=c(/(flex-(?:start|end)|center|stretch|space-(?:between|around))/),W=c(/(flex-(?:start|end)|center|space-(?:between|around|evenly))/),$=c(/^(solid|double|dotted|dashed)$/),z=function(e){return function(t){var r=t.expect(e);return t.expectEmpty(),r}},q=z(m),V=z(y),H=z(b),X=function(e){return function(t,r){return function(n){var o,i,a,s=n.expect(e);if(n.hasTokens())n.expect(h),a=n.expect(e);else{if(void 0===r)return s;a=r}return n.expectEmpty(),[((o={})[t+"Y"]=a,o),((i={})[t+"X"]=s,i)]}}},Y=X(m),Z=X(y),Q=X(b),K={perspective:q,scale:Y("scale"),scaleX:q,scaleY:q,translate:Z("translate",0),translateX:V,translateY:V,rotate:H,rotateX:H,rotateY:H,rotateZ:H,skewX:H,skewY:H,skew:Q("skew","0deg")},J=C({types:[S],prefix:"border",suffix:"Color"}),ee=C({directions:["TopLeft","TopRight","BottomRight","BottomLeft"],prefix:"border",suffix:"Radius"}),et=C({prefix:"border",suffix:"Width"}),er=C({types:[y,D,x,v],prefix:"margin"}),en={background:function(e){return{backgroundColor:e.expect(S)}},border:function(e){if(e.matches(g))return e.expectEmpty(),{borderWidth:0,borderColor:"black",borderStyle:"solid"};for(var t,r,n,o=0;o<3&&e.hasTokens();)0!==o&&e.expect(f),void 0===t&&e.matches(y,D)?t=e.lastValue:void 0===r&&e.matches(S)?r=e.lastValue:void 0===n&&e.matches(F)?n=e.lastValue:e.throw(),o+=1;return e.expectEmpty(),void 0===t&&(t=1),void 0===r&&(r="black"),void 0===n&&(n="solid"),{borderWidth:t,borderColor:r,borderStyle:n}},borderColor:J,borderRadius:ee,borderWidth:et,boxShadow:function(e){var t=T(e);return{shadowOffset:t.offset,shadowRadius:t.radius,shadowColor:t.color,shadowOpacity:1}},flex:function(e){if(e.matches(g))return e.expectEmpty(),{flexGrow:0,flexShrink:0,flexBasis:"auto"};if(e.saveRewindPoint(),e.matches(v)&&!e.hasTokens())return{flexGrow:1,flexShrink:1,flexBasis:"auto"};e.rewind();for(var t,r,n,o=0;o<2&&e.hasTokens();)0!==o&&e.expect(f),void 0===t&&e.matches(m)?(t=e.lastValue,e.saveRewindPoint(),e.matches(f)&&e.matches(m)?r=e.lastValue:e.rewind()):void 0===n&&e.matches(y,D,x)?n=e.lastValue:void 0===n&&e.matches(v)?n="auto":e.throw(),o+=1;return e.expectEmpty(),void 0===t&&(t=1),void 0===r&&(r=1),void 0===n&&(n=0),{flexGrow:t,flexShrink:r,flexBasis:n}},flexFlow:function(e){for(var t,r,n=0;n<2&&e.hasTokens();)0!==n&&e.expect(f),void 0===t&&e.matches(O)?t=e.lastValue:void 0===r&&e.matches(A)?r=e.lastValue:e.throw(),n+=1;return e.expectEmpty(),void 0===t&&(t="nowrap"),void 0===r&&(r="row"),{flexWrap:t,flexDirection:r}},font:function(e){for(var t,r,n,o,i=0;i<3&&e.hasTokens();){if(e.matches(L));else if(void 0===t&&e.matches(I))t=e.lastValue;else if(void 0===r&&e.matches(B))r=e.lastValue;else if(void 0===n&&e.matches(R))n=[e.lastValue];else break;e.expect(f),i+=1}var a=e.expect(y,D);e.matches(p)&&(o=e.expect(y,D)),e.expect(f);var s=P(e).fontFamily;void 0===t&&(t="normal"),void 0===r&&(r="normal"),void 0===n&&(n=U);var u={fontStyle:t,fontWeight:r,fontVariant:n,fontSize:a,fontFamily:s};return void 0!==o&&(u.lineHeight=o),u},fontFamily:P,fontVariant:function(e){return{fontVariant:[e.expect(w)]}},fontWeight:function(e){return{fontWeight:e.expect(d)}},margin:er,padding:C({prefix:"padding"}),placeContent:function(e){var t,r=e.expect(M);return e.hasTokens()?(e.expect(f),t=e.expect(W)):t="stretch",e.expectEmpty(),{alignContent:r,justifyContent:t}},shadowOffset:function(e){return{shadowOffset:_(e)}},textShadow:function(e){var t=T(e);return{textShadowOffset:t.offset,textShadowRadius:t.radius,textShadowColor:t.color}},textShadowOffset:function(e){return{textShadowOffset:_(e)}},textDecoration:function(e){for(var t,r,n,o=!1;e.hasTokens();){if(o&&e.expect(f),void 0===t&&e.matches(k)){var i=[e.lastValue.toLowerCase()];e.saveRewindPoint(),"none"!==i[0]&&e.matches(f)&&e.matches(k)?(i.push(e.lastValue.toLowerCase()),i.sort().reverse()):e.rewind(),t=i.join(" ")}else void 0===r&&e.matches($)?r=e.lastValue:void 0===n&&e.matches(S)?n=e.lastValue:e.throw();o=!0}return{textDecorationLine:void 0!==t?t:"none",textDecorationColor:void 0!==n?n:"black",textDecorationStyle:void 0!==r?r:"solid"}},textDecorationLine:function(e){for(var t=[],r=!1;e.hasTokens();)r&&e.expect(f),t.push(e.expect(k).toLowerCase()),r=!0;return t.sort().reverse(),{textDecorationLine:t.join(" ")}},transform:function(e){for(var t=[],r=!1;e.hasTokens();){r&&e.expect(f);var n,o=e.expectFunction(),i=o.functionName,a=K[i](o);Array.isArray(a)||(a=[((n={})[i]=a,n)]),t=a.concat(t),r=!0}return{transform:t}}};null!=r&&new RegExp(r.join("|"));var eo="SYMBOL_MATCH",ei=function(){function e(e,t){this.index=0,this.nodes=e,this.functionName=null!=t?t.value:null,this.lastValue=null,this.rewindIndex=-1}var t=e.prototype;return t.hasTokens=function(){return this.index<=this.nodes.length-1},t[eo]=function(){if(!this.hasTokens())return null;for(var e=this.nodes[this.index],t=0;t<arguments.length;t+=1){var r=t<0||arguments.length<=t?void 0:arguments[t],n=r(e);if(null!==n)return this.index+=1,this.lastValue=n,n}return null},t.matches=function(){return null!==this[eo].apply(this,arguments)},t.expect=function(){var e=this[eo].apply(this,arguments);return null!==e?e:this.throw()},t.matchesFunction=function(){var t=this.nodes[this.index];if("function"!==t.type)return null;var r=new e(t.nodes,t);return this.index+=1,this.lastValue=null,r},t.expectFunction=function(){var e=this.matchesFunction();return null!==e?e:this.throw()},t.expectEmpty=function(){this.hasTokens()&&this.throw()},t.throw=function(){throw Error("Unexpected token type: "+this.nodes[this.index].type)},t.saveRewindPoint=function(){this.rewindIndex=this.index},t.rewind=function(){if(-1===this.rewindIndex)throw Error("Internal error");this.index=this.rewindIndex,this.lastValue=null},e}(),ea=/^([+-]?(?:\d*\.)?\d+(?:e[+-]?\d+)?)(?:px)?$/i,es=/^true|false$/i,eu=/^null$/i,el=/^undefined$/i,ec=function(e,t){var r=t.match(ea);if(null!==r)return Number(r[1]);var n=t.match(es);return null!==n?"true"===n[0].toLowerCase():null!==t.match(eu)?null:null===t.match(el)?t:void 0},ef=function(e,t){var r=new ei(o(t).nodes);return en[e](r)},ep=function(e,t,r){var n,o=!1===r||!(e in en),i=t.trim();return o?((n={})[e]=ec(e,i),n):ef(e,i)},eh=function(e){return/^--\w+/.test(e)?e:i(e)};e.default=function(e,t){return void 0===t&&(t=[]),e.reduce(function(e,r){var n=eh(r[0]),o=r[1],i=-1===t.indexOf(n);return Object.assign(e,ep(n,o,i))},{})},e.getPropertyName=eh,e.getStylesForProperty=ep,e.transformRawValue=ec}}),z=_({"node_modules/.pnpm/css-background-parser@0.1.0/node_modules/css-background-parser/index.js"(e,t){!function(e){function t(e){if(!(this instanceof t))return new t;this.backgrounds=e||[]}function r(e){if(!(this instanceof r))return new r(e);e=e||{};var t=this;function n(r,n){t[r]=r in e?e[r]:n}n("color",""),n("image","none"),n("attachment","scroll"),n("clip","border-box"),n("origin","padding-box"),n("position","0% 0%"),n("repeat","repeat"),n("size","auto")}function n(e){return e.trim()}function o(e){return(e||"").split(",").map(n)}t.prototype.toString=function(){return this.backgrounds.join(", ")},r.prototype.toString=function(){var e=[this.image,this.repeat,this.attachment,this.position+" / "+this.size,this.origin,this.clip];return this.color&&e.unshift(this.color),e.join(" ")},e.BackgroundList=t,e.Background=r,e.parseElementStyle=function(e){var n,i=new t;if(null==e)return i;for(var a=function(e){var t=[],r=/[,\(\)]/,n=0,o="";if(null==e)return t;for(;e.length;){var i=r.exec(e);if(!i)break;var a=i[0],s=!1;switch(a){case",":n||(t.push(o.trim()),o="",s=!0);break;case"(":n++;break;case")":n--}var u=i.index+1;o+=e.slice(0,s?u-1:u),e=e.slice(u)}return(o.length||e.length)&&t.push((o+e).trim()),t}(e.backgroundImage),s=e.backgroundColor,u=o(e.backgroundAttachment),l=o(e.backgroundClip),c=o(e.backgroundOrigin),f=o(e.backgroundPosition),p=o(e.backgroundRepeat),h=o(e.backgroundSize),d=0,g=a.length;d<g;d++)n=new r({image:a[d],attachment:u[d%u.length],clip:l[d%l.length],origin:c[d%c.length],position:f[d%f.length],repeat:p[d%p.length],size:h[d%h.length]}),d===g-1&&(n.color=s),i.backgrounds.push(n);return i}}(void 0!==t&&void 0!==t.exports?t.exports:e.cssBgParser={})}}),q=_({"node_modules/.pnpm/css-box-shadow@1.0.0-3/node_modules/css-box-shadow/index.js"(e,t){var r=/,(?![^\(]*\))/,n=/\s(?![^(]*\))/,o=/^[0-9]+[a-zA-Z%]+?$/,i=e=>{let t=e.split(n),r=t.includes("inset"),o=t.slice(-1)[0],i=s(o)?void 0:o,[a,l,c,f]=t.filter(e=>"inset"!==e).filter(e=>e!==i).map(u);return{inset:r,offsetX:a,offsetY:l,blurRadius:c,spreadRadius:f,color:i}},a=e=>{let{inset:t,offsetX:r=0,offsetY:n=0,blurRadius:o=0,spreadRadius:i,color:a}=e||{};return[t?"inset":null,r,n,o,i,a].filter(e=>null!=e).map(l).map(e=>(""+e).trim()).join(" ")},s=e=>"0"===e||o.test(e),u=e=>{if(!/px$/.test(e)&&"0"!==e)return e;let t=parseFloat(e);return isNaN(t)?e:t},l=e=>"number"==typeof e&&0!==e?e+"px":e;t.exports={parse:e=>e.split(r).map(e=>e.trim()).map(i),stringify:e=>e.map(a).join(", ")}}}),V=_({"node_modules/.pnpm/color-name@1.1.4/node_modules/color-name/index.js"(e,t){t.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}}}),H=_({"node_modules/.pnpm/hex-rgb@4.3.0/node_modules/hex-rgb/index.js"(e,t){var r="a-f\\d",n=`#?[${r}]{3}[${r}]?`,o=`#?[${r}]{6}([${r}]{2})?`,i=RegExp(`[^#${r}]`,"gi"),a=RegExp(`^${n}$|^${o}$`,"i");t.exports=(e,t={})=>{if("string"!=typeof e||i.test(e)||!a.test(e))throw TypeError("Expected a valid hex string");e=e.replace(/^#/,"");let r=1;8===e.length&&(r=Number.parseInt(e.slice(6,8),16)/255,e=e.slice(0,6)),4===e.length&&(r=Number.parseInt(e.slice(3,4).repeat(2),16)/255,e=e.slice(0,3)),3===e.length&&(e=e[0]+e[0]+e[1]+e[1]+e[2]+e[2]);let n=Number.parseInt(e,16),o=n>>16,s=n>>8&255,u=255&n,l="number"==typeof t.alpha?t.alpha:r;if("array"===t.format)return[o,s,u,l];if("css"===t.format){let e=1===l?"":` / ${Number((100*l).toFixed(2))}%`;return`rgb(${o} ${s} ${u}${e})`}return{red:o,green:s,blue:u,alpha:l}}}}),X=_({"node_modules/.pnpm/escape-html@1.0.3/node_modules/escape-html/index.js"(e,t){var r=/["'&<>]/;t.exports=function(e){var t,n=""+e,o=r.exec(n);if(!o)return n;var i="",a=0,s=0;for(a=o.index;a<n.length;a++){switch(n.charCodeAt(a)){case 34:t="&quot;";break;case 38:t="&amp;";break;case 39:t="&#39;";break;case 60:t="&lt;";break;case 62:t="&gt;";break;default:continue}s!==a&&(i+=n.substring(s,a)),s=a+1,i+=t}return s!==a?i+n.substring(s,a):i}}}),Y=O(L(),1),Z=O(I(),1),Q={},K=[[4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,3,4,4,4,4,4,4,4,4,4,4,4],[0,4,4,1,1,4,4,4,4,1,1,0,0,0,0,1,1,1,0,0,4,2,4,0,0,0,0,0,0,0,0,1,0],[0,4,4,1,1,4,4,4,4,1,1,1,1,1,0,1,1,1,0,0,4,2,4,0,0,0,0,0,0,0,0,1,0],[4,4,4,1,1,1,4,4,4,1,1,1,1,1,1,1,1,1,1,1,4,2,4,1,1,1,1,1,1,1,1,1,1],[1,4,4,1,1,1,4,4,4,1,1,1,1,1,1,1,1,1,1,1,4,2,4,1,1,1,1,1,1,1,1,1,1],[0,4,4,1,1,1,4,4,4,0,0,0,0,0,0,1,1,1,0,0,4,2,4,0,0,0,0,0,0,0,0,1,0],[0,4,4,1,1,1,4,4,4,0,0,0,0,0,0,1,1,1,0,0,4,2,4,0,0,0,0,0,0,0,0,1,0],[0,4,4,1,1,1,4,4,4,0,0,1,0,1,0,1,1,1,0,0,4,2,4,0,0,0,0,0,0,0,0,1,0],[0,4,4,1,1,1,4,4,4,0,0,1,1,1,0,1,1,1,0,0,4,2,4,0,0,0,0,0,0,0,0,1,0],[1,4,4,1,1,1,4,4,4,0,0,1,1,1,1,1,1,1,0,0,4,2,4,1,1,1,1,1,0,1,1,1,0],[1,4,4,1,1,1,4,4,4,0,0,1,1,1,0,1,1,1,0,0,4,2,4,0,0,0,0,0,0,0,0,1,0],[1,4,4,1,1,1,4,4,4,1,1,1,1,1,0,1,1,1,0,0,4,2,4,0,0,0,0,0,0,0,0,1,0],[1,4,4,1,1,1,4,4,4,1,1,1,1,1,0,1,1,1,0,0,4,2,4,0,0,0,0,0,0,0,0,1,0],[1,4,4,1,1,1,4,4,4,1,1,1,1,1,0,1,1,1,0,0,4,2,4,0,0,0,0,0,0,0,0,1,0],[0,4,4,1,1,1,4,4,4,0,1,0,0,0,0,1,1,1,0,0,4,2,4,0,0,0,0,0,0,0,0,1,0],[0,4,4,1,1,1,4,4,4,0,0,0,0,0,0,1,1,1,0,0,4,2,4,0,0,0,0,0,0,0,0,1,0],[0,4,4,1,0,1,4,4,4,0,0,1,0,0,0,1,1,1,0,0,4,2,4,0,0,0,0,0,0,0,0,1,0],[0,4,4,1,0,1,4,4,4,0,0,0,0,0,0,1,1,1,0,0,4,2,4,0,0,0,0,0,0,0,0,1,0],[1,4,4,1,1,1,4,4,4,1,1,1,1,1,1,1,1,1,1,1,4,2,4,1,1,1,1,1,1,1,1,1,0],[0,4,4,1,1,1,4,4,4,0,0,0,0,0,0,1,1,1,0,4,4,2,4,0,0,0,0,0,0,0,0,1,0],[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,4,0,0,0,0,0,0,0,0,0,0,0,0],[1,4,4,1,1,1,4,4,4,1,1,1,1,1,0,1,1,1,0,0,4,2,4,0,0,0,0,0,0,0,0,1,0],[1,4,4,1,1,1,4,4,4,1,1,1,1,1,1,1,1,1,1,1,4,2,4,1,1,1,1,1,1,1,1,1,1],[0,4,4,1,1,1,4,4,4,0,1,0,0,0,0,1,1,1,0,0,4,2,4,0,0,0,1,1,0,0,0,1,0],[0,4,4,1,1,1,4,4,4,0,1,0,0,0,0,1,1,1,0,0,4,2,4,0,0,0,0,1,0,0,0,1,0],[0,4,4,1,1,1,4,4,4,0,1,0,0,0,0,1,1,1,0,0,4,2,4,1,1,1,1,0,0,0,0,1,0],[0,4,4,1,1,1,4,4,4,0,1,0,0,0,0,1,1,1,0,0,4,2,4,0,0,0,1,1,0,0,0,1,0],[0,4,4,1,1,1,4,4,4,0,1,0,0,0,0,1,1,1,0,0,4,2,4,0,0,0,0,1,0,0,0,1,0],[0,4,4,1,1,1,4,4,4,0,0,0,0,0,0,1,1,1,0,0,4,2,4,0,0,0,0,0,1,0,0,1,0],[0,4,4,1,1,1,4,4,4,0,1,0,0,0,0,1,1,1,0,0,4,2,4,0,0,0,0,0,0,0,1,1,0],[0,4,4,1,1,1,4,4,4,0,1,0,0,0,0,1,1,1,0,0,4,2,4,0,0,0,0,0,0,0,0,1,0],[1,4,4,1,1,1,4,4,4,1,1,1,1,1,0,1,1,1,0,0,4,2,4,0,0,0,0,0,0,0,0,1,0],[0,4,4,1,1,0,4,4,4,0,0,0,0,0,0,0,0,0,0,0,4,2,4,0,0,0,0,0,0,0,0,1,0]],J=Z.default.toByteArray("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"),ee=new Y.default(J),et=function(e){switch(e){case 33:case 39:case 40:case 42:return 12;case 35:return 5;default:return e}},er=function(e){switch(e){case 37:case 38:return 34;case 41:return 22;default:return e}},en=class{constructor(e,t=!1){this.position=e,this.required=t}};Q=class{nextCodePoint(){let e=this.string.charCodeAt(this.pos++),t=this.string.charCodeAt(this.pos);return 55296<=e&&e<=56319&&56320<=t&&t<=57343?(this.pos++,(e-55296)*1024+(t-56320)+65536):e}nextCharClass(){return et(ee.get(this.nextCodePoint()))}getSimpleBreak(){switch(this.nextClass){case 41:return!1;case 34:case 37:case 38:return this.curClass=34,!1;case 36:return this.curClass=36,!1}return null}getPairTableBreak(e){let t=!1;switch(K[this.curClass][this.nextClass]){case 0:t=!0;break;case 1:t=41===e;break;case 2:if(!(t=41===e))return!1;break;case 3:if(41!==e)return t}return this.LB8a&&(t=!1),this.LB21a&&(16===this.curClass||17===this.curClass)?(t=!1,this.LB21a=!1):this.LB21a=13===this.curClass,28===this.curClass?(this.LB30a++,2==this.LB30a&&28===this.nextClass&&(t=!0,this.LB30a=0)):this.LB30a=0,this.curClass=this.nextClass,t}nextBreak(){if(null==this.curClass){let e=this.nextCharClass();this.curClass=er(e),this.nextClass=e,this.LB8a=31===e,this.LB30a=0}for(;this.pos<this.string.length;){this.lastPos=this.pos;let e=this.nextClass;if(this.nextClass=this.nextCharClass(),34===this.curClass||36===this.curClass&&37!==this.nextClass)return this.curClass=er(et(this.nextClass)),new en(this.lastPos,!0);let t=this.getSimpleBreak();if(null===t&&(t=this.getPairTableBreak(e)),this.LB8a=31===this.nextClass,t)return new en(this.lastPos)}return this.lastPos<this.string.length?(this.lastPos=this.string.length,new en(this.string.length)):null}constructor(e){this.string=e,this.pos=0,this.lastPos=0,this.curClass=null,this.nextClass=null,this.LB8a=!1,this.LB21a=!1,this.LB30a=0}};var eo=O($(),1),ei=O(z(),1),ea=O(q(),1),es=O(V()),eu=O(H()),el=RegExp(/^#([a-f0-9]{3,4}|[a-f0-9]{4}(?:[a-f0-9]{2}){1,2})\b$/,"i"),ec="-?\\d*(?:\\.\\d+)",ef=`(${ec}?)`,ep=`(${ec}?%)`,eh=`(${ec}?%?)`,ed=new RegExp(`^
  hsla?\\(
    \\s*(-?\\d*(?:\\.\\d+)?(?:deg|rad|turn)?)\\s*,
    \\s*${ep}\\s*,
    \\s*${ep}\\s*
    (?:,\\s*${eh}\\s*)?
  \\)
  $
`.replace(/\n|\s/g,"")),eg=new RegExp(`^
  hsla?\\(
    \\s*(-?\\d*(?:\\.\\d+)?(?:deg|rad|turn)?)\\s*
    \\s+${ep}
    \\s+${ep}
    \\s*(?:\\s*\\/\\s*${eh}\\s*)?
  \\)
  $
`.replace(/\n|\s/g,"")),ev=new RegExp(`^
  rgba?\\(
    \\s*${ef}\\s*,
    \\s*${ef}\\s*,
    \\s*${ef}\\s*
    (?:,\\s*${eh}\\s*)?
  \\)
  $
`.replace(/\n|\s/g,"")),em=new RegExp(`^
  rgba?\\(
    \\s*${ep}\\s*,
    \\s*${ep}\\s*,
    \\s*${ep}\\s*
    (?:,\\s*${eh}\\s*)?
  \\)
  $
`.replace(/\n|\s/g,"")),ey=new RegExp(`^
  rgba?\\(
    \\s*${ef}
    \\s+${ef}
    \\s+${ef}
    \\s*(?:\\s*\\/\\s*${eh}\\s*)?
  \\)
$
`.replace(/\n|\s/g,"")),eD=new RegExp(`^
  rgba?\\(
    \\s*${ep}
    \\s+${ep}
    \\s+${ep}
    \\s*(?:\\s*\\/\\s*${eh}\\s*)?
  \\)
$
`.replace(/\n|\s/g,"")),eb=RegExp(/^transparent$/,"i"),ex=(e,t,r)=>Math.min(Math.max(t,e),r),ew=e=>{let t=e;return"number"!=typeof t&&(t=t.endsWith("%")?255*parseFloat(t)/100:parseFloat(t)),ex(Math.round(t),0,255)},eE=e=>ex(parseFloat(e),0,100);function eS(e){let t=e;return"number"!=typeof t&&(t=t.endsWith("%")?parseFloat(t)/100:parseFloat(t)),ex(t,0,1)}function ek([,e,t,r,n=1]){return{type:"rgb",values:[e,t,r].map(ew),alpha:eS(null===n?1:n)}}var eF=e=>{if("string"!=typeof e)return null;let t=el.exec(e);if(t)return function(e){let[t,r,n,o]=(0,eu.default)(e,{format:"array"});return ek([null,t,r,n,o])}(t[0]);let r=eg.exec(e)||ed.exec(e);if(r)return function([,e,t,r,n=1]){let o=e;return{type:"hsl",values:[o=o.endsWith("turn")?360*parseFloat(o)/1:o.endsWith("rad")?Math.round(180*parseFloat(o)/Math.PI):parseFloat(o),eE(t),eE(r)],alpha:eS(null===n?1:n)}}(r);let n=ey.exec(e)||eD.exec(e)||ev.exec(e)||em.exec(e);if(n)return ek(n);if(eb.exec(e))return ek([null,0,0,0,0]);let o=es.default[e.toLowerCase()];return o?ek([null,o[0],o[1],o[2],1]):null},eC=O(N(),1),e_=O($(),1),eT=O(X(),1);function eO(e,t=","){let r=[],n=0,o=0;t=new RegExp(t);for(let i=0;i<e.length;i++)"("===e[i]?o++:")"===e[i]&&o--,0===o&&t.test(e[i])&&(r.push(e.slice(n,i).trim()),n=i+1);return r.push(e.slice(n).trim()),r}function eA(e){let t=[];for(let n=0,o=e.length;n<o;){var r;let[o,i]=eO(e[n],/\s+/);(r=e[n+1],eP.test(r))?(t.push({color:o,offset:eL(i),hint:eL(e[n+1])}),n+=2):(t.push({color:o,offset:eL(i)}),n++)}return t}var eP=/^(-?\d+\.?\d*)(%|vw|vh|px|em|rem|deg|rad|grad|turn)$/;function eL(e){if(!e)return;let[,t,r]=e.trim().match(eP)||[];return{value:t,unit:r}}var eI=new Set(["closest-corner","closest-side","farthest-corner","farthest-side"]),eB=new Set(["center","left","top","right","bottom"]),eR=O($(),1),eU=Uint8Array,eM=Uint16Array,eN=Uint32Array,ej=new eU([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),eW=new eU([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),eG=new eU([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),e$=function(e,t){for(var r=new eM(31),n=0;n<31;++n)r[n]=t+=1<<e[n-1];for(var o=new eN(r[30]),n=1;n<30;++n)for(var i=r[n];i<r[n+1];++i)o[i]=i-r[n]<<5|n;return[r,o]},ez=e$(ej,2),eq=ez[0],eV=ez[1];eq[28]=258,eV[258]=28;var eH=e$(eW,0)[0],eX=new eM(32768);for(o=0;o<32768;++o)n=(61680&(n=(52428&(n=(43690&o)>>>1|(21845&o)<<1))>>>2|(13107&n)<<2))>>>4|(3855&n)<<4,eX[o]=((65280&n)>>>8|(255&n)<<8)>>>1;var eY=function(e,t,r){for(var n,o=e.length,i=0,a=new eM(t);i<o;++i)e[i]&&++a[e[i]-1];var s=new eM(t);for(i=0;i<t;++i)s[i]=s[i-1]+a[i-1]<<1;if(r){n=new eM(1<<t);var u=15-t;for(i=0;i<o;++i)if(e[i])for(var l=i<<4|e[i],c=t-e[i],f=s[e[i]-1]++<<c,p=f|(1<<c)-1;f<=p;++f)n[eX[f]>>>u]=l}else for(i=0,n=new eM(o);i<o;++i)e[i]&&(n[i]=eX[s[e[i]-1]++]>>>15-e[i]);return n},eZ=new eU(288);for(o=0;o<144;++o)eZ[o]=8;for(o=144;o<256;++o)eZ[o]=9;for(o=256;o<280;++o)eZ[o]=7;for(o=280;o<288;++o)eZ[o]=8;var eQ=new eU(32);for(o=0;o<32;++o)eQ[o]=5;var eK=eY(eZ,9,1),eJ=eY(eQ,5,1),e0=function(e){for(var t=e[0],r=1;r<e.length;++r)e[r]>t&&(t=e[r]);return t},e1=function(e,t,r){var n=t/8|0;return(e[n]|e[n+1]<<8)>>(7&t)&r},e2=function(e,t){var r=t/8|0;return(e[r]|e[r+1]<<8|e[r+2]<<16)>>(7&t)},e3=function(e,t,r){(null==t||t<0)&&(t=0),(null==r||r>e.length)&&(r=e.length);var n=new(2==e.BYTES_PER_ELEMENT?eM:4==e.BYTES_PER_ELEMENT?eN:eU)(r-t);return n.set(e.subarray(t,r)),n},e4=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],e5=function(e,t,r){var n=Error(t||e4[e]);if(n.code=e,Error.captureStackTrace&&Error.captureStackTrace(n,e5),!r)throw n;return n},e6=function(e,t,r){var n=e.length;if(!n||r&&r.f&&!r.l)return t||new eU(0);var o=!t||r,i=!r||r.i;r||(r={}),t||(t=new eU(3*n));var a=function(e){var r=t.length;if(e>r){var n=new eU(Math.max(2*r,e));n.set(t),t=n}},s=r.f||0,u=r.p||0,l=r.b||0,c=r.l,f=r.d,p=r.m,h=r.n,d=8*n;do{if(!c){s=e1(e,u,1);var g=e1(e,u+1,3);if(u+=3,g)if(1==g)c=eK,f=eJ,p=9,h=5;else if(2==g){var v=e1(e,u,31)+257,m=e1(e,u+10,15)+4,y=v+e1(e,u+5,31)+1;u+=14;for(var D=new eU(y),b=new eU(19),x=0;x<m;++x)b[eG[x]]=e1(e,u+3*x,7);u+=3*m;for(var w=e0(b),E=(1<<w)-1,S=eY(b,w,1),x=0;x<y;){var k=S[e1(e,u,E)];u+=15&k;var F=k>>>4;if(F<16)D[x++]=F;else{var C=0,_=0;for(16==F?(_=3+e1(e,u,3),u+=2,C=D[x-1]):17==F?(_=3+e1(e,u,7),u+=3):18==F&&(_=11+e1(e,u,127),u+=7);_--;)D[x++]=C}}var T=D.subarray(0,v),O=D.subarray(v);p=e0(T),h=e0(O),c=eY(T,p,1),f=eY(O,h,1)}else e5(1);else{var F=((u+7)/8|0)+4,A=e[F-4]|e[F-3]<<8,P=F+A;if(P>n){i&&e5(0);break}o&&a(l+A),t.set(e.subarray(F,P),l),r.b=l+=A,r.p=u=8*P,r.f=s;continue}if(u>d){i&&e5(0);break}}o&&a(l+131072);for(var L=(1<<p)-1,I=(1<<h)-1,B=u;;B=u){var C=c[e2(e,u)&L],R=C>>>4;if((u+=15&C)>d){i&&e5(0);break}if(C||e5(2),R<256)t[l++]=R;else if(256==R){B=u,c=null;break}else{var U=R-254;if(R>264){var x=R-257,M=ej[x];U=e1(e,u,(1<<M)-1)+eq[x],u+=M}var N=f[e2(e,u)&I],j=N>>>4;N||e5(3),u+=15&N;var O=eH[j];if(j>3){var M=eW[j];O+=e2(e,u)&(1<<M)-1,u+=M}if(u>d){i&&e5(0);break}o&&a(l+131072);for(var W=l+U;l<W;l+=4)t[l]=t[l-O],t[l+1]=t[l+1-O],t[l+2]=t[l+2-O],t[l+3]=t[l+3-O];l=W}}r.l=c,r.p=B,r.b=l,r.f=s,c&&(s=1,r.m=p,r.d=f,r.n=h)}while(!s);return l==t.length?t:e3(t,0,l)},e8=new eU(0),e7="undefined"!=typeof TextDecoder&&new TextDecoder;try{e7.decode(e8,{stream:!0})}catch(e){}function e9(){this.commands=[],this.fill="black",this.stroke=null,this.strokeWidth=1}e9.prototype.moveTo=function(e,t){this.commands.push({type:"M",x:e,y:t})},e9.prototype.lineTo=function(e,t){this.commands.push({type:"L",x:e,y:t})},e9.prototype.curveTo=e9.prototype.bezierCurveTo=function(e,t,r,n,o,i){this.commands.push({type:"C",x1:e,y1:t,x2:r,y2:n,x:o,y:i})},e9.prototype.quadTo=e9.prototype.quadraticCurveTo=function(e,t,r,n){this.commands.push({type:"Q",x1:e,y1:t,x:r,y:n})},e9.prototype.close=e9.prototype.closePath=function(){this.commands.push({type:"Z"})},e9.prototype.extend=function(e){e.commands&&(e=e.commands),Array.prototype.push.apply(this.commands,e)},e9.prototype.toPathData=function(e){function t(){for(var t=arguments,r="",n=0;n<arguments.length;n+=1){var o=t[n];o>=0&&n>0&&(r+=" "),r+=Math.round(o)===o?""+Math.round(o):o.toFixed(e)}return r}e=void 0!==e?e:2;for(var r="",n=0;n<this.commands.length;n+=1){var o=this.commands[n];"M"===o.type?r+="M"+t(o.x,o.y):"L"===o.type?r+="L"+t(o.x,o.y):"C"===o.type?r+="C"+t(o.x1,o.y1,o.x2,o.y2,o.x,o.y):"Q"===o.type?r+="Q"+t(o.x1,o.y1,o.x,o.y):"Z"===o.type&&(r+="Z")}return r};var te=[".notdef","space","exclam","quotedbl","numbersign","dollar","percent","ampersand","quoteright","parenleft","parenright","asterisk","plus","comma","hyphen","period","slash","zero","one","two","three","four","five","six","seven","eight","nine","colon","semicolon","less","equal","greater","question","at","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","bracketleft","backslash","bracketright","asciicircum","underscore","quoteleft","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","braceleft","bar","braceright","asciitilde","exclamdown","cent","sterling","fraction","yen","florin","section","currency","quotesingle","quotedblleft","guillemotleft","guilsinglleft","guilsinglright","fi","fl","endash","dagger","daggerdbl","periodcentered","paragraph","bullet","quotesinglbase","quotedblbase","quotedblright","guillemotright","ellipsis","perthousand","questiondown","grave","acute","circumflex","tilde","macron","breve","dotaccent","dieresis","ring","cedilla","hungarumlaut","ogonek","caron","emdash","AE","ordfeminine","Lslash","Oslash","OE","ordmasculine","ae","dotlessi","lslash","oslash","oe","germandbls","onesuperior","logicalnot","mu","trademark","Eth","onehalf","plusminus","Thorn","onequarter","divide","brokenbar","degree","thorn","threequarters","twosuperior","registered","minus","eth","multiply","threesuperior","copyright","Aacute","Acircumflex","Adieresis","Agrave","Aring","Atilde","Ccedilla","Eacute","Ecircumflex","Edieresis","Egrave","Iacute","Icircumflex","Idieresis","Igrave","Ntilde","Oacute","Ocircumflex","Odieresis","Ograve","Otilde","Scaron","Uacute","Ucircumflex","Udieresis","Ugrave","Yacute","Ydieresis","Zcaron","aacute","acircumflex","adieresis","agrave","aring","atilde","ccedilla","eacute","ecircumflex","edieresis","egrave","iacute","icircumflex","idieresis","igrave","ntilde","oacute","ocircumflex","odieresis","ograve","otilde","scaron","uacute","ucircumflex","udieresis","ugrave","yacute","ydieresis","zcaron","exclamsmall","Hungarumlautsmall","dollaroldstyle","dollarsuperior","ampersandsmall","Acutesmall","parenleftsuperior","parenrightsuperior","266 ff","onedotenleader","zerooldstyle","oneoldstyle","twooldstyle","threeoldstyle","fouroldstyle","fiveoldstyle","sixoldstyle","sevenoldstyle","eightoldstyle","nineoldstyle","commasuperior","threequartersemdash","periodsuperior","questionsmall","asuperior","bsuperior","centsuperior","dsuperior","esuperior","isuperior","lsuperior","msuperior","nsuperior","osuperior","rsuperior","ssuperior","tsuperior","ff","ffi","ffl","parenleftinferior","parenrightinferior","Circumflexsmall","hyphensuperior","Gravesmall","Asmall","Bsmall","Csmall","Dsmall","Esmall","Fsmall","Gsmall","Hsmall","Ismall","Jsmall","Ksmall","Lsmall","Msmall","Nsmall","Osmall","Psmall","Qsmall","Rsmall","Ssmall","Tsmall","Usmall","Vsmall","Wsmall","Xsmall","Ysmall","Zsmall","colonmonetary","onefitted","rupiah","Tildesmall","exclamdownsmall","centoldstyle","Lslashsmall","Scaronsmall","Zcaronsmall","Dieresissmall","Brevesmall","Caronsmall","Dotaccentsmall","Macronsmall","figuredash","hypheninferior","Ogoneksmall","Ringsmall","Cedillasmall","questiondownsmall","oneeighth","threeeighths","fiveeighths","seveneighths","onethird","twothirds","zerosuperior","foursuperior","fivesuperior","sixsuperior","sevensuperior","eightsuperior","ninesuperior","zeroinferior","oneinferior","twoinferior","threeinferior","fourinferior","fiveinferior","sixinferior","seveninferior","eightinferior","nineinferior","centinferior","dollarinferior","periodinferior","commainferior","Agravesmall","Aacutesmall","Acircumflexsmall","Atildesmall","Adieresissmall","Aringsmall","AEsmall","Ccedillasmall","Egravesmall","Eacutesmall","Ecircumflexsmall","Edieresissmall","Igravesmall","Iacutesmall","Icircumflexsmall","Idieresissmall","Ethsmall","Ntildesmall","Ogravesmall","Oacutesmall","Ocircumflexsmall","Otildesmall","Odieresissmall","OEsmall","Oslashsmall","Ugravesmall","Uacutesmall","Ucircumflexsmall","Udieresissmall","Yacutesmall","Thornsmall","Ydieresissmall","001.000","001.001","001.002","001.003","Black","Bold","Book","Light","Medium","Regular","Roman","Semibold"],tt=["","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","space","exclam","quotedbl","numbersign","dollar","percent","ampersand","quoteright","parenleft","parenright","asterisk","plus","comma","hyphen","period","slash","zero","one","two","three","four","five","six","seven","eight","nine","colon","semicolon","less","equal","greater","question","at","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","bracketleft","backslash","bracketright","asciicircum","underscore","quoteleft","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","braceleft","bar","braceright","asciitilde","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","exclamdown","cent","sterling","fraction","yen","florin","section","currency","quotesingle","quotedblleft","guillemotleft","guilsinglleft","guilsinglright","fi","fl","","endash","dagger","daggerdbl","periodcentered","","paragraph","bullet","quotesinglbase","quotedblbase","quotedblright","guillemotright","ellipsis","perthousand","","questiondown","","grave","acute","circumflex","tilde","macron","breve","dotaccent","dieresis","","ring","cedilla","","hungarumlaut","ogonek","caron","emdash","","","","","","","","","","","","","","","","","AE","","ordfeminine","","","","","Lslash","Oslash","OE","ordmasculine","","","","","","ae","","","","dotlessi","","","lslash","oslash","oe","germandbls"],tr=["","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","space","exclamsmall","Hungarumlautsmall","","dollaroldstyle","dollarsuperior","ampersandsmall","Acutesmall","parenleftsuperior","parenrightsuperior","twodotenleader","onedotenleader","comma","hyphen","period","fraction","zerooldstyle","oneoldstyle","twooldstyle","threeoldstyle","fouroldstyle","fiveoldstyle","sixoldstyle","sevenoldstyle","eightoldstyle","nineoldstyle","colon","semicolon","commasuperior","threequartersemdash","periodsuperior","questionsmall","","asuperior","bsuperior","centsuperior","dsuperior","esuperior","","","isuperior","","","lsuperior","msuperior","nsuperior","osuperior","","","rsuperior","ssuperior","tsuperior","","ff","fi","fl","ffi","ffl","parenleftinferior","","parenrightinferior","Circumflexsmall","hyphensuperior","Gravesmall","Asmall","Bsmall","Csmall","Dsmall","Esmall","Fsmall","Gsmall","Hsmall","Ismall","Jsmall","Ksmall","Lsmall","Msmall","Nsmall","Osmall","Psmall","Qsmall","Rsmall","Ssmall","Tsmall","Usmall","Vsmall","Wsmall","Xsmall","Ysmall","Zsmall","colonmonetary","onefitted","rupiah","Tildesmall","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","","exclamdownsmall","centoldstyle","Lslashsmall","","","Scaronsmall","Zcaronsmall","Dieresissmall","Brevesmall","Caronsmall","","Dotaccentsmall","","","Macronsmall","","","figuredash","hypheninferior","","","Ogoneksmall","Ringsmall","Cedillasmall","","","","onequarter","onehalf","threequarters","questiondownsmall","oneeighth","threeeighths","fiveeighths","seveneighths","onethird","twothirds","","","zerosuperior","onesuperior","twosuperior","threesuperior","foursuperior","fivesuperior","sixsuperior","sevensuperior","eightsuperior","ninesuperior","zeroinferior","oneinferior","twoinferior","threeinferior","fourinferior","fiveinferior","sixinferior","seveninferior","eightinferior","nineinferior","centinferior","dollarinferior","periodinferior","commainferior","Agravesmall","Aacutesmall","Acircumflexsmall","Atildesmall","Adieresissmall","Aringsmall","AEsmall","Ccedillasmall","Egravesmall","Eacutesmall","Ecircumflexsmall","Edieresissmall","Igravesmall","Iacutesmall","Icircumflexsmall","Idieresissmall","Ethsmall","Ntildesmall","Ogravesmall","Oacutesmall","Ocircumflexsmall","Otildesmall","Odieresissmall","OEsmall","Oslashsmall","Ugravesmall","Uacutesmall","Ucircumflexsmall","Udieresissmall","Yacutesmall","Thornsmall","Ydieresissmall"];function tn(e){this.font=e}function to(e){this.cmap=e}function ti(e,t){this.encoding=e,this.charset=t}tn.prototype.charToGlyphIndex=function(e){var t=e.codePointAt(0),r=this.font.glyphs;if(r){for(var n=0;n<r.length;n+=1)for(var o=r.get(n),i=0;i<o.unicodes.length;i+=1)if(o.unicodes[i]===t)return n}return null},to.prototype.charToGlyphIndex=function(e){return this.cmap.glyphIndexMap[e.codePointAt(0)]||0},ti.prototype.charToGlyphIndex=function(e){var t=e.codePointAt(0),r=this.encoding[t];return this.charset.indexOf(r)};function ta(e,t){e||function(e){throw Error(e)}(t)}var ts={argument:ta,assert:ta};function tu(e){this.bindConstructorValues(e)}function tl(e,t,r){Object.defineProperty(e,t,{get:function(){return e.path,e[r]},set:function(t){e[r]=t},enumerable:!0,configurable:!0})}function tc(e,t){if(this.font=e,this.glyphs={},Array.isArray(t))for(var r=0;r<t.length;r++){var n=t[r];n.path.unitsPerEm=e.unitsPerEm,this.glyphs[r]=n}this.length=t&&t.length||0}tu.prototype.bindConstructorValues=function(e){var t;this.index=e.index||0,this.name=e.name||null,this.unicode=e.unicode||void 0,this.unicodes=e.unicodes||void 0!==e.unicode?[e.unicode]:[],"xMin"in e&&(this.xMin=e.xMin),"yMin"in e&&(this.yMin=e.yMin),"xMax"in e&&(this.xMax=e.xMax),"yMax"in e&&(this.yMax=e.yMax),"advanceWidth"in e&&(this.advanceWidth=e.advanceWidth),Object.defineProperty(this,"path",(t=e.path||new e9,{configurable:!0,get:function(){return"function"==typeof t&&(t=t()),t},set:function(e){t=e}}))},tu.prototype.addUnicode=function(e){0===this.unicodes.length&&(this.unicode=e),this.unicodes.push(e)},tu.prototype.getPath=function(e,t,r,n,o){e=void 0!==e?e:0,t=void 0!==t?t:0,r=void 0!==r?r:72,n||(n={});var i,a,s=n.xScale,u=n.yScale;if(n.hinting&&o&&o.hinting&&(a=this.path&&o.hinting.exec(this,r)),a)i=o.hinting.getCommands(a),e=Math.round(e),t=Math.round(t),s=u=1;else{i=this.path.commands;var l=1/(this.path.unitsPerEm||1e3)*r;void 0===s&&(s=l),void 0===u&&(u=l)}for(var c=new e9,f=0;f<i.length;f+=1){var p=i[f];"M"===p.type?c.moveTo(e+p.x*s,t+-p.y*u):"L"===p.type?c.lineTo(e+p.x*s,t+-p.y*u):"Q"===p.type?c.quadraticCurveTo(e+p.x1*s,t+-p.y1*u,e+p.x*s,t+-p.y*u):"C"===p.type?c.curveTo(e+p.x1*s,t+-p.y1*u,e+p.x2*s,t+-p.y2*u,e+p.x*s,t+-p.y*u):"Z"===p.type&&c.closePath()}return c},tu.prototype.getContours=function(){if(void 0===this.points)return[];for(var e=[],t=[],r=0;r<this.points.length;r+=1){var n=this.points[r];t.push(n),n.lastPointOfContour&&(e.push(t),t=[])}return ts.argument(0===t.length,"There are still points left in the current contour."),e},tu.prototype.getMetrics=function(){for(var e=this.path.commands,t=[],r=[],n=0;n<e.length;n+=1){var o=e[n];"Z"!==o.type&&(t.push(o.x),r.push(o.y)),("Q"===o.type||"C"===o.type)&&(t.push(o.x1),r.push(o.y1)),"C"===o.type&&(t.push(o.x2),r.push(o.y2))}var i={xMin:Math.min.apply(null,t),yMin:Math.min.apply(null,r),xMax:Math.max.apply(null,t),yMax:Math.max.apply(null,r),leftSideBearing:this.leftSideBearing};return isFinite(i.xMin)||(i.xMin=0),isFinite(i.xMax)||(i.xMax=this.advanceWidth),isFinite(i.yMin)||(i.yMin=0),isFinite(i.yMax)||(i.yMax=0),i.rightSideBearing=this.advanceWidth-i.leftSideBearing-(i.xMax-i.xMin),i},tc.prototype.get=function(e){if(void 0===this.glyphs[e]){this.font._push(e),"function"==typeof this.glyphs[e]&&(this.glyphs[e]=this.glyphs[e]());var t=this.glyphs[e],r=this.font._IndexToUnicodeMap[e];if(r)for(var n=0;n<r.unicodes.length;n++)t.addUnicode(r.unicodes[n]);this.glyphs[e].advanceWidth=this.font._hmtxTableData[e].advanceWidth,this.glyphs[e].leftSideBearing=this.font._hmtxTableData[e].leftSideBearing}else"function"==typeof this.glyphs[e]&&(this.glyphs[e]=this.glyphs[e]());return this.glyphs[e]},tc.prototype.push=function(e,t){this.glyphs[e]=t,this.length++};var tf={GlyphSet:tc,glyphLoader:function(e,t){return new tu({index:t,font:e})},ttfGlyphLoader:function(e,t,r,n,o,i){return function(){var a=new tu({index:t,font:e});return a.path=function(){r(a,n,o);var t=i(e.glyphs,a);return t.unitsPerEm=e.unitsPerEm,t},tl(a,"xMin","_xMin"),tl(a,"xMax","_xMax"),tl(a,"yMin","_yMin"),tl(a,"yMax","_yMax"),a}},cffGlyphLoader:function(e,t,r,n){return function(){var o=new tu({index:t,font:e});return o.path=function(){var t=r(e,o,n);return t.unitsPerEm=e.unitsPerEm,t},o}}};function tp(e,t){for(var r=0,n=e.length-1;r<=n;){var o=r+n>>>1,i=e[o].tag;if(i===t)return o;i<t?r=o+1:n=o-1}return-r-1}function th(e,t){for(var r=0,n=e.length-1;r<=n;){var o=r+n>>>1,i=e[o];if(i===t)return o;i<t?r=o+1:n=o-1}return-r-1}function td(e,t){for(var r,n=0,o=e.length-1;n<=o;){var i=n+o>>>1,a=(r=e[i]).start;if(a===t)return r;a<t?n=i+1:o=i-1}if(n>0)return t>(r=e[n-1]).end?0:r}function tg(e,t){this.font=e,this.tableName=t}function tv(e){tg.call(this,e,"gpos")}function tm(e){tg.call(this,e,"gsub")}function ty(e,t,r){for(var n=e.subtables,o=0;o<n.length;o++){var i=n[o];if(i.substFormat===t)return i}if(r)return n.push(r),r}function tD(e,t){if(!e)throw t}function tb(e,t){return e.getUint8(t)}function tx(e,t){return e.getUint16(t,!1)}function tw(e,t){return e.getUint32(t,!1)}function tE(e,t){return e.getInt16(t,!1)+e.getUint16(t+2,!1)/65535}tg.prototype={searchTag:tp,binSearch:th,getTable:function(e){var t=this.font.tables[this.tableName];return!t&&e&&(t=this.font.tables[this.tableName]=this.createDefaultTable()),t},getDefaultScriptName:function(){var e=this.getTable();if(e){for(var t=!1,r=0;r<e.scripts.length;r++){var n=e.scripts[r].tag;if("DFLT"===n)return n;"latn"===n&&(t=!0)}if(t)return"latn"}},getScriptTable:function(e,t){var r=this.getTable(t);if(r){e=e||"DFLT";var n=r.scripts,o=tp(r.scripts,e);if(o>=0)return n[o].script;if(t){var i={tag:e,script:{defaultLangSys:{reserved:0,reqFeatureIndex:65535,featureIndexes:[]},langSysRecords:[]}};return n.splice(-1-o,0,i),i.script}}},getLangSysTable:function(e,t,r){var n=this.getScriptTable(e,r);if(n){if(!t||"dflt"===t||"DFLT"===t)return n.defaultLangSys;var o=tp(n.langSysRecords,t);if(o>=0)return n.langSysRecords[o].langSys;if(r){var i={tag:t,langSys:{reserved:0,reqFeatureIndex:65535,featureIndexes:[]}};return n.langSysRecords.splice(-1-o,0,i),i.langSys}}},getFeatureTable:function(e,t,r,n){var o=this.getLangSysTable(e,t,n);if(o){for(var i,a=o.featureIndexes,s=this.font.tables[this.tableName].features,u=0;u<a.length;u++)if((i=s[a[u]]).tag===r)return i.feature;if(n){var l=s.length;return ts.assert(0===l||r>=s[l-1].tag,"Features must be added in alphabetical order."),i={tag:r,feature:{params:0,lookupListIndexes:[]}},s.push(i),a.push(l),i.feature}}},getLookupTables:function(e,t,r,n,o){var i=this.getFeatureTable(e,t,r,o),a=[];if(i){for(var s,u=i.lookupListIndexes,l=this.font.tables[this.tableName].lookups,c=0;c<u.length;c++)(s=l[u[c]]).lookupType===n&&a.push(s);if(0===a.length&&o){s={lookupType:n,lookupFlag:0,subtables:[],markFilteringSet:void 0};var f=l.length;return l.push(s),u.push(f),[s]}}return a},getGlyphClass:function(e,t){switch(e.format){case 1:if(e.startGlyph<=t&&t<e.startGlyph+e.classes.length)return e.classes[t-e.startGlyph];return 0;case 2:var r=td(e.ranges,t);return r?r.classId:0}},getCoverageIndex:function(e,t){switch(e.format){case 1:var r=th(e.glyphs,t);return r>=0?r:-1;case 2:var n=td(e.ranges,t);return n?n.index+t-n.start:-1}},expandCoverage:function(e){if(1===e.format)return e.glyphs;for(var t=[],r=e.ranges,n=0;n<r.length;n++)for(var o=r[n],i=o.start,a=o.end,s=i;s<=a;s++)t.push(s);return t}},tv.prototype=tg.prototype,tv.prototype.init=function(){var e=this.getDefaultScriptName();this.defaultKerningTables=this.getKerningTables(e)},tv.prototype.getKerningValue=function(e,t,r){for(var n=0;n<e.length;n++)for(var o=e[n].subtables,i=0;i<o.length;i++){var a=o[i],s=this.getCoverageIndex(a.coverage,t);if(!(s<0))switch(a.posFormat){case 1:for(var u=a.pairSets[s],l=0;l<u.length;l++){var c=u[l];if(c.secondGlyph===r)return c.value1&&c.value1.xAdvance||0}break;case 2:var f=this.getGlyphClass(a.classDef1,t),p=this.getGlyphClass(a.classDef2,r),h=a.classRecords[f][p];return h.value1&&h.value1.xAdvance||0}}return 0},tv.prototype.getKerningTables=function(e,t){if(this.font.tables.gpos)return this.getLookupTables(e,t,"kern",2)},tm.prototype=tg.prototype,tm.prototype.createDefaultTable=function(){return{version:1,scripts:[{tag:"DFLT",script:{defaultLangSys:{reserved:0,reqFeatureIndex:65535,featureIndexes:[]},langSysRecords:[]}}],features:[],lookups:[]}},tm.prototype.getSingle=function(e,t,r){for(var n=[],o=this.getLookupTables(t,r,e,1),i=0;i<o.length;i++)for(var a=o[i].subtables,s=0;s<a.length;s++){var u=a[s],l=this.expandCoverage(u.coverage),c=void 0;if(1===u.substFormat){var f=u.deltaGlyphId;for(c=0;c<l.length;c++){var p=l[c];n.push({sub:p,by:p+f})}}else{var h=u.substitute;for(c=0;c<l.length;c++)n.push({sub:l[c],by:h[c]})}}return n},tm.prototype.getMultiple=function(e,t,r){for(var n=[],o=this.getLookupTables(t,r,e,2),i=0;i<o.length;i++)for(var a=o[i].subtables,s=0;s<a.length;s++){var u=a[s],l=this.expandCoverage(u.coverage),c=void 0;for(c=0;c<l.length;c++){var f=l[c],p=u.sequences[c];n.push({sub:f,by:p})}}return n},tm.prototype.getAlternates=function(e,t,r){for(var n=[],o=this.getLookupTables(t,r,e,3),i=0;i<o.length;i++)for(var a=o[i].subtables,s=0;s<a.length;s++)for(var u=a[s],l=this.expandCoverage(u.coverage),c=u.alternateSets,f=0;f<l.length;f++)n.push({sub:l[f],by:c[f]});return n},tm.prototype.getLigatures=function(e,t,r){for(var n=[],o=this.getLookupTables(t,r,e,4),i=0;i<o.length;i++)for(var a=o[i].subtables,s=0;s<a.length;s++)for(var u=a[s],l=this.expandCoverage(u.coverage),c=u.ligatureSets,f=0;f<l.length;f++)for(var p=l[f],h=c[f],d=0;d<h.length;d++){var g=h[d];n.push({sub:[p].concat(g.components),by:g.ligGlyph})}return n},tm.prototype.addSingle=function(e,t,r,n){var o=ty(this.getLookupTables(r,n,e,1,!0)[0],2,{substFormat:2,coverage:{format:1,glyphs:[]},substitute:[]});ts.assert(1===o.coverage.format,"Single: unable to modify coverage table format "+o.coverage.format);var i=t.sub,a=this.binSearch(o.coverage.glyphs,i);a<0&&(a=-1-a,o.coverage.glyphs.splice(a,0,i),o.substitute.splice(a,0,0)),o.substitute[a]=t.by},tm.prototype.addMultiple=function(e,t,r,n){ts.assert(t.by instanceof Array&&t.by.length>1,'Multiple: "by" must be an array of two or more ids');var o=ty(this.getLookupTables(r,n,e,2,!0)[0],1,{substFormat:1,coverage:{format:1,glyphs:[]},sequences:[]});ts.assert(1===o.coverage.format,"Multiple: unable to modify coverage table format "+o.coverage.format);var i=t.sub,a=this.binSearch(o.coverage.glyphs,i);a<0&&(a=-1-a,o.coverage.glyphs.splice(a,0,i),o.sequences.splice(a,0,0)),o.sequences[a]=t.by},tm.prototype.addAlternate=function(e,t,r,n){var o=ty(this.getLookupTables(r,n,e,3,!0)[0],1,{substFormat:1,coverage:{format:1,glyphs:[]},alternateSets:[]});ts.assert(1===o.coverage.format,"Alternate: unable to modify coverage table format "+o.coverage.format);var i=t.sub,a=this.binSearch(o.coverage.glyphs,i);a<0&&(a=-1-a,o.coverage.glyphs.splice(a,0,i),o.alternateSets.splice(a,0,0)),o.alternateSets[a]=t.by},tm.prototype.addLigature=function(e,t,r,n){var o=this.getLookupTables(r,n,e,4,!0)[0],i=o.subtables[0];i||(i={substFormat:1,coverage:{format:1,glyphs:[]},ligatureSets:[]},o.subtables[0]=i),ts.assert(1===i.coverage.format,"Ligature: unable to modify coverage table format "+i.coverage.format);var a=t.sub[0],s=t.sub.slice(1),u={ligGlyph:t.by,components:s},l=this.binSearch(i.coverage.glyphs,a);if(l>=0){for(var c=i.ligatureSets[l],f=0;f<c.length;f++)if(function(e,t){var r=e.length;if(r!==t.length)return!1;for(var n=0;n<r;n++)if(e[n]!==t[n])return!1;return!0}(c[f].components,s))return;c.push(u)}else l=-1-l,i.coverage.glyphs.splice(l,0,a),i.ligatureSets.splice(l,0,[u])},tm.prototype.getFeature=function(e,t,r){if(/ss\d\d/.test(e))return this.getSingle(e,t,r);switch(e){case"aalt":case"salt":return this.getSingle(e,t,r).concat(this.getAlternates(e,t,r));case"dlig":case"liga":case"rlig":return this.getLigatures(e,t,r);case"ccmp":return this.getMultiple(e,t,r).concat(this.getLigatures(e,t,r));case"stch":return this.getMultiple(e,t,r)}},tm.prototype.add=function(e,t,r,n){if(/ss\d\d/.test(e))return this.addSingle(e,t,r,n);switch(e){case"aalt":case"salt":if("number"==typeof t.by)return this.addSingle(e,t,r,n);return this.addAlternate(e,t,r,n);case"dlig":case"liga":case"rlig":return this.addLigature(e,t,r,n);case"ccmp":if(t.by instanceof Array)return this.addMultiple(e,t,r,n);return this.addLigature(e,t,r,n)}};var tS={byte:1,uShort:2,short:2,uLong:4,fixed:4,longDateTime:8,tag:4};function tk(e,t){this.data=e,this.offset=t,this.relativeOffset=0}tk.prototype.parseByte=function(){var e=this.data.getUint8(this.offset+this.relativeOffset);return this.relativeOffset+=1,e},tk.prototype.parseChar=function(){var e=this.data.getInt8(this.offset+this.relativeOffset);return this.relativeOffset+=1,e},tk.prototype.parseCard8=tk.prototype.parseByte,tk.prototype.parseUShort=function(){var e=this.data.getUint16(this.offset+this.relativeOffset);return this.relativeOffset+=2,e},tk.prototype.parseCard16=tk.prototype.parseUShort,tk.prototype.parseSID=tk.prototype.parseUShort,tk.prototype.parseOffset16=tk.prototype.parseUShort,tk.prototype.parseShort=function(){var e=this.data.getInt16(this.offset+this.relativeOffset);return this.relativeOffset+=2,e},tk.prototype.parseF2Dot14=function(){var e=this.data.getInt16(this.offset+this.relativeOffset)/16384;return this.relativeOffset+=2,e},tk.prototype.parseULong=function(){var e=tw(this.data,this.offset+this.relativeOffset);return this.relativeOffset+=4,e},tk.prototype.parseOffset32=tk.prototype.parseULong,tk.prototype.parseFixed=function(){var e=tE(this.data,this.offset+this.relativeOffset);return this.relativeOffset+=4,e},tk.prototype.parseString=function(e){var t=this.data,r=this.offset+this.relativeOffset,n="";this.relativeOffset+=e;for(var o=0;o<e;o++)n+=String.fromCharCode(t.getUint8(r+o));return n},tk.prototype.parseTag=function(){return this.parseString(4)},tk.prototype.parseLongDateTime=function(){var e=tw(this.data,this.offset+this.relativeOffset+4);return e-=0x7c25b080,this.relativeOffset+=8,e},tk.prototype.parseVersion=function(e){var t=tx(this.data,this.offset+this.relativeOffset),r=tx(this.data,this.offset+this.relativeOffset+2);return this.relativeOffset+=4,void 0===e&&(e=4096),t+r/e/10},tk.prototype.skip=function(e,t){void 0===t&&(t=1),this.relativeOffset+=tS[e]*t},tk.prototype.parseULongList=function(e){void 0===e&&(e=this.parseULong());for(var t=Array(e),r=this.data,n=this.offset+this.relativeOffset,o=0;o<e;o++)t[o]=r.getUint32(n),n+=4;return this.relativeOffset+=4*e,t},tk.prototype.parseOffset16List=tk.prototype.parseUShortList=function(e){void 0===e&&(e=this.parseUShort());for(var t=Array(e),r=this.data,n=this.offset+this.relativeOffset,o=0;o<e;o++)t[o]=r.getUint16(n),n+=2;return this.relativeOffset+=2*e,t},tk.prototype.parseShortList=function(e){for(var t=Array(e),r=this.data,n=this.offset+this.relativeOffset,o=0;o<e;o++)t[o]=r.getInt16(n),n+=2;return this.relativeOffset+=2*e,t},tk.prototype.parseByteList=function(e){for(var t=Array(e),r=this.data,n=this.offset+this.relativeOffset,o=0;o<e;o++)t[o]=r.getUint8(n++);return this.relativeOffset+=e,t},tk.prototype.parseList=function(e,t){t||(t=e,e=this.parseUShort());for(var r=Array(e),n=0;n<e;n++)r[n]=t.call(this);return r},tk.prototype.parseList32=function(e,t){t||(t=e,e=this.parseULong());for(var r=Array(e),n=0;n<e;n++)r[n]=t.call(this);return r},tk.prototype.parseRecordList=function(e,t){t||(t=e,e=this.parseUShort());for(var r=Array(e),n=Object.keys(t),o=0;o<e;o++){for(var i={},a=0;a<n.length;a++){var s=n[a],u=t[s];i[s]=u.call(this)}r[o]=i}return r},tk.prototype.parseRecordList32=function(e,t){t||(t=e,e=this.parseULong());for(var r=Array(e),n=Object.keys(t),o=0;o<e;o++){for(var i={},a=0;a<n.length;a++){var s=n[a],u=t[s];i[s]=u.call(this)}r[o]=i}return r},tk.prototype.parseStruct=function(e){if("function"==typeof e)return e.call(this);for(var t=Object.keys(e),r={},n=0;n<t.length;n++){var o=t[n],i=e[o];r[o]=i.call(this)}return r},tk.prototype.parseValueRecord=function(e){if(void 0===e&&(e=this.parseUShort()),0!==e){var t={};return 1&e&&(t.xPlacement=this.parseShort()),2&e&&(t.yPlacement=this.parseShort()),4&e&&(t.xAdvance=this.parseShort()),8&e&&(t.yAdvance=this.parseShort()),16&e&&(t.xPlaDevice=void 0,this.parseShort()),32&e&&(t.yPlaDevice=void 0,this.parseShort()),64&e&&(t.xAdvDevice=void 0,this.parseShort()),128&e&&(t.yAdvDevice=void 0,this.parseShort()),t}},tk.prototype.parseValueRecordList=function(){for(var e=this.parseUShort(),t=this.parseUShort(),r=Array(t),n=0;n<t;n++)r[n]=this.parseValueRecord(e);return r},tk.prototype.parsePointer=function(e){var t=this.parseOffset16();if(t>0)return new tk(this.data,this.offset+t).parseStruct(e)},tk.prototype.parsePointer32=function(e){var t=this.parseOffset32();if(t>0)return new tk(this.data,this.offset+t).parseStruct(e)},tk.prototype.parseListOfLists=function(e){for(var t=this.parseOffset16List(),r=t.length,n=this.relativeOffset,o=Array(r),i=0;i<r;i++){var a=t[i];if(0===a){o[i]=void 0;continue}if(this.relativeOffset=a,e){for(var s=this.parseOffset16List(),u=Array(s.length),l=0;l<s.length;l++)this.relativeOffset=a+s[l],u[l]=e.call(this);o[i]=u}else o[i]=this.parseUShortList()}return this.relativeOffset=n,o},tk.prototype.parseCoverage=function(){var e=this.offset+this.relativeOffset,t=this.parseUShort(),r=this.parseUShort();if(1===t)return{format:1,glyphs:this.parseUShortList(r)};if(2===t){for(var n=Array(r),o=0;o<r;o++)n[o]={start:this.parseUShort(),end:this.parseUShort(),index:this.parseUShort()};return{format:2,ranges:n}}throw Error("0x"+e.toString(16)+": Coverage format must be 1 or 2.")},tk.prototype.parseClassDef=function(){var e=this.offset+this.relativeOffset,t=this.parseUShort();if(1===t)return{format:1,startGlyph:this.parseUShort(),classes:this.parseUShortList()};if(2===t)return{format:2,ranges:this.parseRecordList({start:tk.uShort,end:tk.uShort,classId:tk.uShort})};throw Error("0x"+e.toString(16)+": ClassDef format must be 1 or 2.")},tk.list=function(e,t){return function(){return this.parseList(e,t)}},tk.list32=function(e,t){return function(){return this.parseList32(e,t)}},tk.recordList=function(e,t){return function(){return this.parseRecordList(e,t)}},tk.recordList32=function(e,t){return function(){return this.parseRecordList32(e,t)}},tk.pointer=function(e){return function(){return this.parsePointer(e)}},tk.pointer32=function(e){return function(){return this.parsePointer32(e)}},tk.tag=tk.prototype.parseTag,tk.byte=tk.prototype.parseByte,tk.uShort=tk.offset16=tk.prototype.parseUShort,tk.uShortList=tk.prototype.parseUShortList,tk.uLong=tk.offset32=tk.prototype.parseULong,tk.uLongList=tk.prototype.parseULongList,tk.struct=tk.prototype.parseStruct,tk.coverage=tk.prototype.parseCoverage,tk.classDef=tk.prototype.parseClassDef;var tF={reserved:tk.uShort,reqFeatureIndex:tk.uShort,featureIndexes:tk.uShortList};tk.prototype.parseScriptList=function(){return this.parsePointer(tk.recordList({tag:tk.tag,script:tk.pointer({defaultLangSys:tk.pointer(tF),langSysRecords:tk.recordList({tag:tk.tag,langSys:tk.pointer(tF)})})}))||[]},tk.prototype.parseFeatureList=function(){return this.parsePointer(tk.recordList({tag:tk.tag,feature:tk.pointer({featureParams:tk.offset16,lookupListIndexes:tk.uShortList})}))||[]},tk.prototype.parseLookupList=function(e){return this.parsePointer(tk.list(tk.pointer(function(){var t=this.parseUShort();ts.argument(1<=t&&t<=9,"GPOS/GSUB lookup type "+t+" unknown.");var r=this.parseUShort();return{lookupType:t,lookupFlag:r,subtables:this.parseList(tk.pointer(e[t])),markFilteringSet:16&r?this.parseUShort():void 0}})))||[]},tk.prototype.parseFeatureVariationsList=function(){return this.parsePointer32(function(){var e=this.parseUShort(),t=this.parseUShort();return ts.argument(1===e&&t<1,"GPOS/GSUB feature variations table unknown."),this.parseRecordList32({conditionSetOffset:tk.offset32,featureTableSubstitutionOffset:tk.offset32})})||[]};var tC={getByte:tb,getCard8:tb,getUShort:tx,getCard16:tx,getShort:function(e,t){return e.getInt16(t,!1)},getULong:tw,getFixed:tE,getTag:function(e,t){for(var r="",n=t;n<t+4;n+=1)r+=String.fromCharCode(e.getInt8(n));return r},getOffset:function(e,t,r){for(var n=0,o=0;o<r;o+=1)n<<=8,n+=e.getUint8(t+o);return n},getBytes:function(e,t,r){for(var n=[],o=t;o<r;o+=1)n.push(e.getUint8(o));return n},bytesToString:function(e){for(var t="",r=0;r<e.length;r+=1)t+=String.fromCharCode(e[r]);return t},Parser:tk};function t_(e,t,r,n,o){var i;return(t&n)>0?(i=e.parseByte(),(t&o)==0&&(i=-i),i=r+i):i=(t&o)>0?r:r+e.parseShort(),i}function tT(e,t,r){var n=new tC.Parser(t,r);if(e.numberOfContours=n.parseShort(),e._xMin=n.parseShort(),e._yMin=n.parseShort(),e._xMax=n.parseShort(),e._yMax=n.parseShort(),e.numberOfContours>0){for(var o=e.endPointIndices=[],i=0;i<e.numberOfContours;i+=1)o.push(n.parseUShort());e.instructionLength=n.parseUShort(),e.instructions=[];for(var a=0;a<e.instructionLength;a+=1)e.instructions.push(n.parseByte());var s=o[o.length-1]+1;f=[];for(var u=0;u<s;u+=1)if(p=n.parseByte(),f.push(p),(8&p)>0)for(var l=n.parseByte(),c=0;c<l;c+=1)f.push(p),u+=1;if(ts.argument(f.length===s,"Bad flags."),o.length>0){var f,p,h,d=[];if(s>0){for(var g=0;g<s;g+=1)p=f[g],(h={}).onCurve=!!(1&p),h.lastPointOfContour=o.indexOf(g)>=0,d.push(h);for(var v=0,m=0;m<s;m+=1)p=f[m],(h=d[m]).x=t_(n,p,v,2,16),v=h.x;for(var y=0,D=0;D<s;D+=1)p=f[D],(h=d[D]).y=t_(n,p,y,4,32),y=h.y}e.points=d}else e.points=[]}else if(0===e.numberOfContours)e.points=[];else{e.isComposite=!0,e.points=[],e.components=[];for(var b=!0;b;){f=n.parseUShort();var x={glyphIndex:n.parseUShort(),xScale:1,scale01:0,scale10:0,yScale:1,dx:0,dy:0};(1&f)>0?(2&f)>0?(x.dx=n.parseShort(),x.dy=n.parseShort()):x.matchedPoints=[n.parseUShort(),n.parseUShort()]:(2&f)>0?(x.dx=n.parseChar(),x.dy=n.parseChar()):x.matchedPoints=[n.parseByte(),n.parseByte()],(8&f)>0?x.xScale=x.yScale=n.parseF2Dot14():(64&f)>0?(x.xScale=n.parseF2Dot14(),x.yScale=n.parseF2Dot14()):(128&f)>0&&(x.xScale=n.parseF2Dot14(),x.scale01=n.parseF2Dot14(),x.scale10=n.parseF2Dot14(),x.yScale=n.parseF2Dot14()),e.components.push(x),b=!!(32&f)}if(256&f){e.instructionLength=n.parseUShort(),e.instructions=[];for(var w=0;w<e.instructionLength;w+=1)e.instructions.push(n.parseByte())}}}function tO(e,t){for(var r=[],n=0;n<e.length;n+=1){var o=e[n],i={x:t.xScale*o.x+t.scale01*o.y+t.dx,y:t.scale10*o.x+t.yScale*o.y+t.dy,onCurve:o.onCurve,lastPointOfContour:o.lastPointOfContour};r.push(i)}return r}function tA(e){var t=new e9;if(!e)return t;for(var r=function(e){for(var t=[],r=[],n=0;n<e.length;n+=1){var o=e[n];r.push(o),o.lastPointOfContour&&(t.push(r),r=[])}return ts.argument(0===r.length,"There are still points left in the current contour."),t}(e),n=0;n<r.length;++n){var o=r[n],i=null,a=o[o.length-1],s=o[0];if(a.onCurve)t.moveTo(a.x,a.y);else if(s.onCurve)t.moveTo(s.x,s.y);else{var u={x:(a.x+s.x)*.5,y:(a.y+s.y)*.5};t.moveTo(u.x,u.y)}for(var l=0;l<o.length;++l)if(i=a,a=s,s=o[(l+1)%o.length],a.onCurve)t.lineTo(a.x,a.y);else{var c=s;i.onCurve||(a.x,i.x,a.y,i.y),s.onCurve||(c={x:(a.x+s.x)*.5,y:(a.y+s.y)*.5}),t.quadraticCurveTo(a.x,a.y,c.x,c.y)}t.closePath()}return t}function tP(e,t){if(t.isComposite)for(var r=0;r<t.components.length;r+=1){var n=t.components[r],o=e.get(n.glyphIndex);if(o.getPath(),o.points){var i=void 0;if(void 0===n.matchedPoints)i=tO(o.points,n);else{if(n.matchedPoints[0]>t.points.length-1||n.matchedPoints[1]>o.points.length-1)throw Error("Matched points out of range in "+t.name);var a=t.points[n.matchedPoints[0]],s=o.points[n.matchedPoints[1]],u={xScale:n.xScale,scale01:n.scale01,scale10:n.scale10,yScale:n.yScale,dx:0,dy:0};s=tO([s],u)[0],u.dx=a.x-s.x,u.dy=a.y-s.y,i=tO(o.points,u)}t.points=t.points.concat(i)}}return tA(t.points)}var tL={getPath:tA,parse:function(e,t,r,n,o){if(o.lowMemory){var i;return i=new tf.GlyphSet(n),n._push=function(o){var a=r[o];a!==r[o+1]?i.push(o,tf.ttfGlyphLoader(n,o,tT,e,t+a,tP)):i.push(o,tf.glyphLoader(n,o))},i}for(var a=new tf.GlyphSet(n),s=0;s<r.length-1;s+=1){var u=r[s];u!==r[s+1]?a.push(s,tf.ttfGlyphLoader(n,s,tT,e,t+u,tP)):a.push(s,tf.glyphLoader(n,s))}return a}};function tI(e){this.font=e,this.getCommands=function(e){return tL.getPath(e).commands},this._fpgmState=this._prepState=void 0,this._errorState=0}function tB(e){return e}function tR(e){return Math.sign(e)*Math.round(Math.abs(e))}function tU(e){return Math.sign(e)*Math.round(Math.abs(2*e))/2}function tM(e){return Math.sign(e)*(Math.round(Math.abs(e)+.5)-.5)}function tN(e){return Math.sign(e)*Math.ceil(Math.abs(e))}function tj(e){return Math.sign(e)*Math.floor(Math.abs(e))}var tW=function(e){var t=this.srPeriod,r=this.srPhase,n=this.srThreshold,o=1;return(e<0&&(e=-e,o=-1),e+=n-r,(e=Math.trunc(e/t)*t+r)<0)?r*o:e*o},tG={x:1,y:0,axis:"x",distance:function(e,t,r,n){return(r?e.xo:e.x)-(n?t.xo:t.x)},interpolate:function(e,t,r,n){var o,i,a,s,u,l,c;if(!n||n===this){if(o=e.xo-t.xo,i=e.xo-r.xo,u=t.x-t.xo,l=r.x-r.xo,0===(c=(a=Math.abs(o))+(s=Math.abs(i)))){e.x=e.xo+(u+l)/2;return}e.x=e.xo+(u*s+l*a)/c;return}if(o=n.distance(e,t,!0,!0),i=n.distance(e,r,!0,!0),u=n.distance(t,t,!1,!0),l=n.distance(r,r,!1,!0),0===(c=(a=Math.abs(o))+(s=Math.abs(i))))return void tG.setRelative(e,e,(u+l)/2,n,!0);tG.setRelative(e,e,(u*s+l*a)/c,n,!0)},normalSlope:Number.NEGATIVE_INFINITY,setRelative:function(e,t,r,n,o){if(!n||n===this){e.x=(o?t.xo:t.x)+r;return}var i=o?t.xo:t.x,a=o?t.yo:t.y,s=i+r*n.x,u=a+r*n.y;e.x=s+(e.y-u)/n.normalSlope},slope:0,touch:function(e){e.xTouched=!0},touched:function(e){return e.xTouched},untouch:function(e){e.xTouched=!1}},t$={x:0,y:1,axis:"y",distance:function(e,t,r,n){return(r?e.yo:e.y)-(n?t.yo:t.y)},interpolate:function(e,t,r,n){var o,i,a,s,u,l,c;if(!n||n===this){if(o=e.yo-t.yo,i=e.yo-r.yo,u=t.y-t.yo,l=r.y-r.yo,0===(c=(a=Math.abs(o))+(s=Math.abs(i)))){e.y=e.yo+(u+l)/2;return}e.y=e.yo+(u*s+l*a)/c;return}if(o=n.distance(e,t,!0,!0),i=n.distance(e,r,!0,!0),u=n.distance(t,t,!1,!0),l=n.distance(r,r,!1,!0),0===(c=(a=Math.abs(o))+(s=Math.abs(i))))return void t$.setRelative(e,e,(u+l)/2,n,!0);t$.setRelative(e,e,(u*s+l*a)/c,n,!0)},normalSlope:0,setRelative:function(e,t,r,n,o){if(!n||n===this){e.y=(o?t.yo:t.y)+r;return}var i=o?t.xo:t.x,a=o?t.yo:t.y,s=i+r*n.x;e.y=a+r*n.y+n.normalSlope*(e.x-s)},slope:Number.POSITIVE_INFINITY,touch:function(e){e.yTouched=!0},touched:function(e){return e.yTouched},untouch:function(e){e.yTouched=!1}};function tz(e,t){this.x=e,this.y=t,this.axis=void 0,this.slope=t/e,this.normalSlope=-e/t,Object.freeze(this)}function tq(e,t){var r=Math.sqrt(e*e+t*t);return(e/=r,t/=r,1===e&&0===t)?tG:0===e&&1===t?t$:new tz(e,t)}function tV(e,t,r,n){this.x=this.xo=Math.round(64*e)/64,this.y=this.yo=Math.round(64*t)/64,this.lastPointOfContour=r,this.onCurve=n,this.prevPointOnContour=void 0,this.nextPointOnContour=void 0,this.xTouched=!1,this.yTouched=!1,Object.preventExtensions(this)}Object.freeze(tG),Object.freeze(t$),tz.prototype.distance=function(e,t,r,n){return this.x*tG.distance(e,t,r,n)+this.y*t$.distance(e,t,r,n)},tz.prototype.interpolate=function(e,t,r,n){var o,i,a,s,u,l,c;if(a=n.distance(e,t,!0,!0),s=n.distance(e,r,!0,!0),o=n.distance(t,t,!1,!0),i=n.distance(r,r,!1,!0),0===(c=(u=Math.abs(a))+(l=Math.abs(s))))return void this.setRelative(e,e,(o+i)/2,n,!0);this.setRelative(e,e,(o*l+i*u)/c,n,!0)},tz.prototype.setRelative=function(e,t,r,n,o){n=n||this;var i=o?t.xo:t.x,a=o?t.yo:t.y,s=i+r*n.x,u=a+r*n.y,l=n.normalSlope,c=this.slope,f=e.x,p=e.y;e.x=(c*f-l*s+u-p)/(c-l),e.y=c*(e.x-f)+p},tz.prototype.touch=function(e){e.xTouched=!0,e.yTouched=!0},tV.prototype.nextTouched=function(e){for(var t=this.nextPointOnContour;!e.touched(t)&&t!==this;)t=t.nextPointOnContour;return t},tV.prototype.prevTouched=function(e){for(var t=this.prevPointOnContour;!e.touched(t)&&t!==this;)t=t.prevPointOnContour;return t};var tH=Object.freeze(new tV(0,0)),tX={cvCutIn:17/16,deltaBase:9,deltaShift:.125,loop:1,minDis:1,autoFlip:!0};function tY(e,t){switch(this.env=e,this.stack=[],this.prog=t,e){case"glyf":this.zp0=this.zp1=this.zp2=1,this.rp0=this.rp1=this.rp2=0;case"prep":this.fv=this.pv=this.dpv=tG,this.round=tR}}function tZ(e){for(var t=e.tZone=Array(e.gZone.length),r=0;r<t.length;r++)t[r]=new tV(0,0)}function tQ(e,t){var r,n=e.prog,o=e.ip,i=1;do if(88===(r=n[++o]))i++;else if(89===r)i--;else if(64===r)o+=n[o+1]+1;else if(65===r)o+=2*n[o+1]+1;else if(r>=176&&r<=183)o+=r-176+1;else if(r>=184&&r<=191)o+=(r-184+1)*2;else if(t&&1===i&&27===r)break;while(i>0);e.ip=o}function tK(e,t){exports.DEBUG&&console.log(t.step,"SVTCA["+e.axis+"]"),t.fv=t.pv=t.dpv=e}function tJ(e,t){exports.DEBUG&&console.log(t.step,"SPVTCA["+e.axis+"]"),t.pv=t.dpv=e}function t0(e,t){exports.DEBUG&&console.log(t.step,"SFVTCA["+e.axis+"]"),t.fv=e}function t1(e,t){var r,n,o=t.stack,i=o.pop(),a=o.pop(),s=t.z2[i],u=t.z1[a];exports.DEBUG&&console.log("SPVTL["+e+"]",i,a),e?(r=s.y-u.y,n=u.x-s.x):(r=u.x-s.x,n=u.y-s.y),t.pv=t.dpv=tq(r,n)}function t2(e,t){var r,n,o=t.stack,i=o.pop(),a=o.pop(),s=t.z2[i],u=t.z1[a];exports.DEBUG&&console.log("SFVTL["+e+"]",i,a),e?(r=s.y-u.y,n=u.x-s.x):(r=u.x-s.x,n=u.y-s.y),t.fv=tq(r,n)}function t3(e){exports.DEBUG&&console.log(e.step,"POP[]"),e.stack.pop()}function t4(e,t){var r=t.stack.pop(),n=t.z0[r],o=t.fv,i=t.pv;exports.DEBUG&&console.log(t.step,"MDAP["+e+"]",r);var a=i.distance(n,tH);e&&(a=t.round(a)),o.setRelative(n,tH,a,i),o.touch(n),t.rp0=t.rp1=r}function t5(e,t){var r,n,o,i=t.z2,a=i.length-2;exports.DEBUG&&console.log(t.step,"IUP["+e.axis+"]");for(var s=0;s<a;s++)r=i[s],e.touched(r)||(n=r.prevTouched(e))!==r&&(n===(o=r.nextTouched(e))&&e.setRelative(r,r,e.distance(n,n,!1,!0),e,!0),e.interpolate(r,n,o,e))}function t6(e,t){for(var r=t.stack,n=e?t.rp1:t.rp2,o=(e?t.z0:t.z1)[n],i=t.fv,a=t.pv,s=t.loop,u=t.z2;s--;){var l=r.pop(),c=u[l],f=a.distance(o,o,!1,!0);i.setRelative(c,c,f,a),i.touch(c),exports.DEBUG&&console.log(t.step,(t.loop>1?"loop "+(t.loop-s)+": ":"")+"SHP["+(e?"rp1":"rp2")+"]",l)}t.loop=1}function t8(e,t){var r=t.stack,n=e?t.rp1:t.rp2,o=(e?t.z0:t.z1)[n],i=t.fv,a=t.pv,s=r.pop(),u=t.z2[t.contours[s]],l=u;exports.DEBUG&&console.log(t.step,"SHC["+e+"]",s);var c=a.distance(o,o,!1,!0);do l!==o&&i.setRelative(l,l,c,a),l=l.nextPointOnContour;while(l!==u)}function t7(e,t){var r,n,o=t.stack,i=e?t.rp1:t.rp2,a=(e?t.z0:t.z1)[i],s=t.fv,u=t.pv,l=o.pop();switch(exports.DEBUG&&console.log(t.step,"SHZ["+e+"]",l),l){case 0:r=t.tZone;break;case 1:r=t.gZone;break;default:throw Error("Invalid zone")}for(var c=u.distance(a,a,!1,!0),f=r.length-2,p=0;p<f;p++)n=r[p],s.setRelative(n,n,c,u)}function t9(e,t){var r=t.stack,n=r.pop()/64,o=r.pop(),i=t.z1[o],a=t.z0[t.rp0],s=t.fv,u=t.pv;s.setRelative(i,a,n,u),s.touch(i),exports.DEBUG&&console.log(t.step,"MSIRP["+e+"]",n,o),t.rp1=t.rp0,t.rp2=o,e&&(t.rp0=o)}function re(e,t){var r=t.stack,n=r.pop(),o=r.pop(),i=t.z0[o],a=t.fv,s=t.pv,u=t.cvt[n];exports.DEBUG&&console.log(t.step,"MIAP["+e+"]",n,"(",u,")",o);var l=s.distance(i,tH);e&&(Math.abs(l-u)<t.cvCutIn&&(l=u),l=t.round(l)),a.setRelative(i,tH,l,s),0===t.zp0&&(i.xo=i.x,i.yo=i.y),a.touch(i),t.rp0=t.rp1=o}function rt(e,t){var r=t.stack,n=r.pop(),o=t.z2[n];exports.DEBUG&&console.log(t.step,"GC["+e+"]",n),r.push(64*t.dpv.distance(o,tH,e,!1))}function rr(e,t){var r=t.stack,n=r.pop(),o=r.pop(),i=t.z1[n],a=t.z0[o],s=t.dpv.distance(a,i,e,e);exports.DEBUG&&console.log(t.step,"MD["+e+"]",n,o,"->",s),t.stack.push(Math.round(64*s))}function rn(e,t){var r=t.stack,n=r.pop(),o=t.fv,i=t.pv,a=t.ppem,s=t.deltaBase+(e-1)*16,u=t.deltaShift,l=t.z0;exports.DEBUG&&console.log(t.step,"DELTAP["+e+"]",n,r);for(var c=0;c<n;c++){var f=r.pop(),p=r.pop();if(s+((240&p)>>4)===a){var h=(15&p)-8;h>=0&&h++,exports.DEBUG&&console.log(t.step,"DELTAPFIX",f,"by",h*u);var d=l[f];o.setRelative(d,d,h*u,i)}}}function ro(e,t){var r=t.stack,n=r.pop();exports.DEBUG&&console.log(t.step,"ROUND[]"),r.push(64*t.round(n/64))}function ri(e,t){var r=t.stack,n=r.pop(),o=t.ppem,i=t.deltaBase+(e-1)*16,a=t.deltaShift;exports.DEBUG&&console.log(t.step,"DELTAC["+e+"]",n,r);for(var s=0;s<n;s++){var u=r.pop(),l=r.pop();if(i+((240&l)>>4)===o){var c=(15&l)-8;c>=0&&c++;var f=c*a;exports.DEBUG&&console.log(t.step,"DELTACFIX",u,"by",f),t.cvt[u]+=f}}}function ra(e,t){var r,n,o=t.stack,i=o.pop(),a=o.pop(),s=t.z2[i],u=t.z1[a];exports.DEBUG&&console.log(t.step,"SDPVTL["+e+"]",i,a),e?(r=s.y-u.y,n=u.x-s.x):(r=u.x-s.x,n=u.y-s.y),t.dpv=tq(r,n)}function rs(e,t){var r=t.stack,n=t.prog,o=t.ip;exports.DEBUG&&console.log(t.step,"PUSHB["+e+"]");for(var i=0;i<e;i++)r.push(n[++o]);t.ip=o}function ru(e,t){var r=t.ip,n=t.prog,o=t.stack;exports.DEBUG&&console.log(t.ip,"PUSHW["+e+"]");for(var i=0;i<e;i++){var a=n[++r]<<8|n[++r];32768&a&&(a=-((65535^a)+1)),o.push(a)}t.ip=r}function rl(e,t,r,n,o,i){var a,s,u,l,c=i.stack,f=e&&c.pop(),p=c.pop(),h=i.rp0,d=i.z0[h],g=i.z1[p],v=i.minDis,m=i.fv,y=i.dpv;u=(s=a=y.distance(g,d,!0,!0))>=0?1:-1,s=Math.abs(s),e&&(l=i.cvt[f],n&&Math.abs(s-l)<i.cvCutIn&&(s=l)),r&&s<v&&(s=v),n&&(s=i.round(s)),m.setRelative(g,d,u*s,y),m.touch(g),exports.DEBUG&&console.log(i.step,(e?"MIRP[":"MDRP[")+(t?"M":"m")+(r?">":"_")+(n?"R":"_")+(0===o?"Gr":1===o?"Bl":2===o?"Wh":"")+"]",e?f+"("+i.cvt[f]+","+l+")":"",p,"(d =",a,"->",u*s,")"),i.rp1=i.rp0,i.rp2=p,t&&(i.rp0=p)}function rc(e){this.char=e,this.state={},this.activeState=null}function rf(e,t,r){this.contextName=r,this.startIndex=e,this.endOffset=t}function rp(e,t,r){this.contextName=e,this.openRange=null,this.ranges=[],this.checkStart=t,this.checkEnd=r}function rh(e,t){this.context=e,this.index=t,this.length=e.length,this.current=e[t],this.backtrack=e.slice(0,t),this.lookahead=e.slice(t+1)}function rd(e){this.eventId=e,this.subscribers=[]}function rg(e){var t=this,r=["start","end","next","newToken","contextStart","contextEnd","insertToken","removeToken","removeRange","replaceToken","replaceRange","composeRUD","updateContextsRanges"];r.forEach(function(e){Object.defineProperty(t.events,e,{value:new rd(e)})}),e&&r.forEach(function(r){var n=e[r];"function"==typeof n&&t.events[r].subscribe(n)}),["insertToken","removeToken","removeRange","replaceToken","replaceRange","composeRUD"].forEach(function(e){t.events[e].subscribe(t.updateContextsRanges)})}function rv(e){this.tokens=[],this.registeredContexts={},this.contextCheckers=[],this.events={},this.registeredModifiers=[],rg.call(this,e)}function rm(e){return/[\u0600-\u065F\u066A-\u06D2\u06FA-\u06FF]/.test(e)}function ry(e){return/[\u0630\u0690\u0621\u0631\u0661\u0671\u0622\u0632\u0672\u0692\u06C2\u0623\u0673\u0693\u06C3\u0624\u0694\u06C4\u0625\u0675\u0695\u06C5\u06E5\u0676\u0696\u06C6\u0627\u0677\u0697\u06C7\u0648\u0688\u0698\u06C8\u0689\u0699\u06C9\u068A\u06CA\u066B\u068B\u06CB\u068C\u068D\u06CD\u06FD\u068E\u06EE\u06FE\u062F\u068F\u06CF\u06EF]/.test(e)}function rD(e){return/[\u0600-\u0605\u060C-\u060E\u0610-\u061B\u061E\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED]/.test(e)}function rb(e){return/[A-z]/.test(e)}function rx(e){this.font=e,this.features={}}function rw(e){this.id=e.id,this.tag=e.tag,this.substitution=e.substitution}function rE(e,t){if(!e)return -1;switch(t.format){case 1:return t.glyphs.indexOf(e);case 2:for(var r=t.ranges,n=0;n<r.length;n++){var o=r[n];if(e>=o.start&&e<=o.end){var i=e-o.start;return o.index+i}}}return -1}function rS(e,t){return -1===rE(e,t.coverage)?null:e+t.deltaGlyphId}function rk(e,t){var r=rE(e,t.coverage);return -1===r?null:t.substitute[r]}function rF(e,t){for(var r=[],n=0;n<e.length;n++){var o=e[n],i=t.current,a=rE(i=Array.isArray(i)?i[0]:i,o);-1!==a&&r.push(a)}return r.length!==e.length?-1:r}function rC(e,t){var r=t.inputCoverage.length+t.lookaheadCoverage.length+t.backtrackCoverage.length;if(e.context.length<r)return[];var n=rF(t.inputCoverage,e);if(-1===n)return[];var o=t.inputCoverage.length-1;if(e.lookahead.length<t.lookaheadCoverage.length)return[];for(var i=e.lookahead.slice(o);i.length&&rD(i[0].char);)i.shift();var a=new rh(i,0),s=rF(t.lookaheadCoverage,a),u=[].concat(e.backtrack);for(u.reverse();u.length&&rD(u[0].char);)u.shift();if(u.length<t.backtrackCoverage.length)return[];var l=new rh(u,0),c=rF(t.backtrackCoverage,l),f=n.length===t.inputCoverage.length&&s.length===t.lookaheadCoverage.length&&c.length===t.backtrackCoverage.length,p=[];if(f)for(var h=0;h<t.lookupRecords.length;h++)for(var d=t.lookupRecords[h].lookupListIndex,g=this.getLookupByIndex(d),v=0;v<g.subtables.length;v++){var m=g.subtables[v],y=this.getLookupMethod(g,m);if("12"===this.getSubstitutionType(g,m))for(var D=0;D<n.length;D++){var b=y(e.get(D));b&&p.push(b)}}return p}function r_(e,t){var r,n=rE(e.current,t.coverage);if(-1===n)return null;for(var o=t.ligatureSets[n],i=0;i<o.length;i++){r=o[i];for(var a=0;a<r.components.length&&e.lookahead[a]===r.components[a];a++)if(a===r.components.length-1)return r}return null}function rT(e,t){var r=rE(e,t.coverage);return -1===r?null:t.sequences[r]}tI.prototype.exec=function(e,t){if("number"!=typeof t)throw Error("Point size is not a number!");if(!(this._errorState>2)){var r=this.font,n=this._prepState;if(!n||n.ppem!==t){var o=this._fpgmState;if(!o){tY.prototype=tX,(o=this._fpgmState=new tY("fpgm",r.tables.fpgm)).funcs=[],o.font=r,exports.DEBUG&&(console.log("---EXEC FPGM---"),o.step=-1);try{a(o)}catch(e){console.log("Hinting error in FPGM:"+e),this._errorState=3;return}}tY.prototype=o,(n=this._prepState=new tY("prep",r.tables.prep)).ppem=t;var i=r.tables.cvt;if(i)for(var u=n.cvt=Array(i.length),l=t/r.unitsPerEm,c=0;c<i.length;c++)u[c]=i[c]*l;else n.cvt=[];exports.DEBUG&&(console.log("---EXEC PREP---"),n.step=-1);try{a(n)}catch(e){this._errorState<2&&console.log("Hinting error in PREP:"+e),this._errorState=2}}if(!(this._errorState>1))try{return s(e,n)}catch(e){this._errorState<1&&(console.log("Hinting error:"+e),console.log("Note: further hinting errors are silenced")),this._errorState=1;return}}},s=function(e,t){var r,n,o,i=t.ppem/t.font.unitsPerEm,s=e.components;if(tY.prototype=t,s){var l=t.font;n=[],r=[];for(var c=0;c<s.length;c++){var f=s[c],p=l.glyphs.get(f.glyphIndex);o=new tY("glyf",p.instructions),exports.DEBUG&&(console.log("---EXEC COMP "+c+"---"),o.step=-1),u(p,o,i,i);for(var h=Math.round(f.dx*i),d=Math.round(f.dy*i),g=o.gZone,v=o.contours,m=0;m<g.length;m++){var y=g[m];y.xTouched=y.yTouched=!1,y.xo=y.x=y.x+h,y.yo=y.y=y.y+d}var D=n.length;n.push.apply(n,g);for(var b=0;b<v.length;b++)r.push(v[b]+D)}e.instructions&&!o.inhibitGridFit&&((o=new tY("glyf",e.instructions)).gZone=o.z0=o.z1=o.z2=n,o.contours=r,n.push(new tV(0,0),new tV(Math.round(e.advanceWidth*i),0)),exports.DEBUG&&(console.log("---EXEC COMPOSITE---"),o.step=-1),a(o),n.length-=2)}else o=new tY("glyf",e.instructions),exports.DEBUG&&(console.log("---EXEC GLYPH---"),o.step=-1),u(e,o,i,i),n=o.gZone;return n},u=function(e,t,r,n){for(var o,i,s,u=e.points||[],l=u.length,c=t.gZone=t.z0=t.z1=t.z2=[],f=t.contours=[],p=0;p<l;p++)o=u[p],c[p]=new tV(o.x*r,o.y*n,o.lastPointOfContour,o.onCurve);for(var h=0;h<l;h++)o=c[h],i||(i=o,f.push(h)),o.lastPointOfContour?(o.nextPointOnContour=i,i.prevPointOnContour=o,i=void 0):(s=c[h+1],o.nextPointOnContour=s,s.prevPointOnContour=o);if(!t.inhibitGridFit){if(exports.DEBUG){console.log("PROCESSING GLYPH",t.stack);for(var d=0;d<l;d++)console.log(d,c[d].x,c[d].y)}if(c.push(new tV(0,0),new tV(Math.round(e.advanceWidth*r),0)),a(t),c.length-=2,exports.DEBUG){console.log("FINISHED GLYPH",t.stack);for(var g=0;g<l;g++)console.log(g,c[g].x,c[g].y)}}},a=function(e){var t,r=e.prog;if(r){var n=r.length;for(e.ip=0;e.ip<n;e.ip++){if(exports.DEBUG&&e.step++,!(t=i[r[e.ip]]))throw Error("unknown instruction: 0x"+Number(r[e.ip]).toString(16));t(e)}}},i=[tK.bind(void 0,t$),tK.bind(void 0,tG),tJ.bind(void 0,t$),tJ.bind(void 0,tG),t0.bind(void 0,t$),t0.bind(void 0,tG),t1.bind(void 0,0),t1.bind(void 0,1),t2.bind(void 0,0),t2.bind(void 0,1),function(e){var t=e.stack,r=t.pop(),n=t.pop();exports.DEBUG&&console.log(e.step,"SPVFS[]",r,n),e.pv=e.dpv=tq(n,r)},function(e){var t=e.stack,r=t.pop(),n=t.pop();exports.DEBUG&&console.log(e.step,"SPVFS[]",r,n),e.fv=tq(n,r)},function(e){var t=e.stack,r=e.pv;exports.DEBUG&&console.log(e.step,"GPV[]"),t.push(16384*r.x),t.push(16384*r.y)},function(e){var t=e.stack,r=e.fv;exports.DEBUG&&console.log(e.step,"GFV[]"),t.push(16384*r.x),t.push(16384*r.y)},function(e){e.fv=e.pv,exports.DEBUG&&console.log(e.step,"SFVTPV[]")},function(e){var t=e.stack,r=t.pop(),n=t.pop(),o=t.pop(),i=t.pop(),a=t.pop(),s=e.z0,u=e.z1,l=s[r],c=s[n],f=u[o],p=u[i],h=e.z2[a];exports.DEBUG&&console.log("ISECT[], ",r,n,o,i,a);var d=l.x,g=l.y,v=c.x,m=c.y,y=f.x,D=f.y,b=p.x,x=p.y,w=(d-v)*(D-x)-(g-m)*(y-b),E=d*m-g*v,S=y*x-D*b;h.x=(E*(y-b)-S*(d-v))/w,h.y=(E*(D-x)-S*(g-m))/w},function(e){e.rp0=e.stack.pop(),exports.DEBUG&&console.log(e.step,"SRP0[]",e.rp0)},function(e){e.rp1=e.stack.pop(),exports.DEBUG&&console.log(e.step,"SRP1[]",e.rp1)},function(e){e.rp2=e.stack.pop(),exports.DEBUG&&console.log(e.step,"SRP2[]",e.rp2)},function(e){var t=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,"SZP0[]",t),e.zp0=t,t){case 0:e.tZone||tZ(e),e.z0=e.tZone;break;case 1:e.z0=e.gZone;break;default:throw Error("Invalid zone pointer")}},function(e){var t=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,"SZP1[]",t),e.zp1=t,t){case 0:e.tZone||tZ(e),e.z1=e.tZone;break;case 1:e.z1=e.gZone;break;default:throw Error("Invalid zone pointer")}},function(e){var t=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,"SZP2[]",t),e.zp2=t,t){case 0:e.tZone||tZ(e),e.z2=e.tZone;break;case 1:e.z2=e.gZone;break;default:throw Error("Invalid zone pointer")}},function(e){var t=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,"SZPS[]",t),e.zp0=e.zp1=e.zp2=t,t){case 0:e.tZone||tZ(e),e.z0=e.z1=e.z2=e.tZone;break;case 1:e.z0=e.z1=e.z2=e.gZone;break;default:throw Error("Invalid zone pointer")}},function(e){e.loop=e.stack.pop(),exports.DEBUG&&console.log(e.step,"SLOOP[]",e.loop)},function(e){exports.DEBUG&&console.log(e.step,"RTG[]"),e.round=tR},function(e){exports.DEBUG&&console.log(e.step,"RTHG[]"),e.round=tM},function(e){var t=e.stack.pop();exports.DEBUG&&console.log(e.step,"SMD[]",t),e.minDis=t/64},function(e){exports.DEBUG&&console.log(e.step,"ELSE[]"),tQ(e,!1)},function(e){var t=e.stack.pop();exports.DEBUG&&console.log(e.step,"JMPR[]",t),e.ip+=t-1},function(e){var t=e.stack.pop();exports.DEBUG&&console.log(e.step,"SCVTCI[]",t),e.cvCutIn=t/64},void 0,void 0,function(e){var t=e.stack;exports.DEBUG&&console.log(e.step,"DUP[]"),t.push(t[t.length-1])},t3,function(e){exports.DEBUG&&console.log(e.step,"CLEAR[]"),e.stack.length=0},function(e){var t=e.stack,r=t.pop(),n=t.pop();exports.DEBUG&&console.log(e.step,"SWAP[]"),t.push(r),t.push(n)},function(e){var t=e.stack;exports.DEBUG&&console.log(e.step,"DEPTH[]"),t.push(t.length)},function(e){var t=e.stack,r=t.pop();exports.DEBUG&&console.log(e.step,"CINDEX[]",r),t.push(t[t.length-r])},function(e){var t=e.stack,r=t.pop();exports.DEBUG&&console.log(e.step,"MINDEX[]",r),t.push(t.splice(t.length-r,1)[0])},void 0,void 0,void 0,function(e){var t=e.stack,r=t.pop(),n=t.pop();exports.DEBUG&&console.log(e.step,"LOOPCALL[]",r,n);var o=e.ip,i=e.prog;e.prog=e.funcs[r];for(var s=0;s<n;s++)a(e),exports.DEBUG&&console.log(++e.step,s+1<n?"next loopcall":"done loopcall",s);e.ip=o,e.prog=i},function(e){var t=e.stack.pop();exports.DEBUG&&console.log(e.step,"CALL[]",t);var r=e.ip,n=e.prog;e.prog=e.funcs[t],a(e),e.ip=r,e.prog=n,exports.DEBUG&&console.log(++e.step,"returning from",t)},function(e){if("fpgm"!==e.env)throw Error("FDEF not allowed here");var t=e.stack,r=e.prog,n=e.ip,o=t.pop(),i=n;for(exports.DEBUG&&console.log(e.step,"FDEF[]",o);45!==r[++n];);e.ip=n,e.funcs[o]=r.slice(i+1,n)},void 0,t4.bind(void 0,0),t4.bind(void 0,1),t5.bind(void 0,t$),t5.bind(void 0,tG),t6.bind(void 0,0),t6.bind(void 0,1),t8.bind(void 0,0),t8.bind(void 0,1),t7.bind(void 0,0),t7.bind(void 0,1),function(e){for(var t=e.stack,r=e.loop,n=e.fv,o=t.pop()/64,i=e.z2;r--;){var a=t.pop(),s=i[a];exports.DEBUG&&console.log(e.step,(e.loop>1?"loop "+(e.loop-r)+": ":"")+"SHPIX[]",a,o),n.setRelative(s,s,o),n.touch(s)}e.loop=1},function(e){for(var t=e.stack,r=e.rp1,n=e.rp2,o=e.loop,i=e.z0[r],a=e.z1[n],s=e.fv,u=e.dpv,l=e.z2;o--;){var c=t.pop(),f=l[c];exports.DEBUG&&console.log(e.step,(e.loop>1?"loop "+(e.loop-o)+": ":"")+"IP[]",c,r,"<->",n),s.interpolate(f,i,a,u),s.touch(f)}e.loop=1},t9.bind(void 0,0),t9.bind(void 0,1),function(e){for(var t=e.stack,r=e.rp0,n=e.z0[r],o=e.loop,i=e.fv,a=e.pv,s=e.z1;o--;){var u=t.pop(),l=s[u];exports.DEBUG&&console.log(e.step,(e.loop>1?"loop "+(e.loop-o)+": ":"")+"ALIGNRP[]",u),i.setRelative(l,n,0,a),i.touch(l)}e.loop=1},function(e){exports.DEBUG&&console.log(e.step,"RTDG[]"),e.round=tU},re.bind(void 0,0),re.bind(void 0,1),function(e){var t=e.prog,r=e.ip,n=e.stack,o=t[++r];exports.DEBUG&&console.log(e.step,"NPUSHB[]",o);for(var i=0;i<o;i++)n.push(t[++r]);e.ip=r},function(e){var t=e.ip,r=e.prog,n=e.stack,o=r[++t];exports.DEBUG&&console.log(e.step,"NPUSHW[]",o);for(var i=0;i<o;i++){var a=r[++t]<<8|r[++t];32768&a&&(a=-((65535^a)+1)),n.push(a)}e.ip=t},function(e){var t=e.stack,r=e.store;r||(r=e.store=[]);var n=t.pop(),o=t.pop();exports.DEBUG&&console.log(e.step,"WS",n,o),r[o]=n},function(e){var t=e.stack,r=e.store,n=t.pop();exports.DEBUG&&console.log(e.step,"RS",n);var o=r&&r[n]||0;t.push(o)},function(e){var t=e.stack,r=t.pop(),n=t.pop();exports.DEBUG&&console.log(e.step,"WCVTP",r,n),e.cvt[n]=r/64},function(e){var t=e.stack,r=t.pop();exports.DEBUG&&console.log(e.step,"RCVT",r),t.push(64*e.cvt[r])},rt.bind(void 0,0),rt.bind(void 0,1),void 0,rr.bind(void 0,0),rr.bind(void 0,1),function(e){exports.DEBUG&&console.log(e.step,"MPPEM[]"),e.stack.push(e.ppem)},void 0,function(e){exports.DEBUG&&console.log(e.step,"FLIPON[]"),e.autoFlip=!0},void 0,void 0,function(e){var t=e.stack,r=t.pop(),n=t.pop();exports.DEBUG&&console.log(e.step,"LT[]",r,n),t.push(+(n<r))},function(e){var t=e.stack,r=t.pop(),n=t.pop();exports.DEBUG&&console.log(e.step,"LTEQ[]",r,n),t.push(+(n<=r))},function(e){var t=e.stack,r=t.pop(),n=t.pop();exports.DEBUG&&console.log(e.step,"GT[]",r,n),t.push(+(n>r))},function(e){var t=e.stack,r=t.pop(),n=t.pop();exports.DEBUG&&console.log(e.step,"GTEQ[]",r,n),t.push(+(n>=r))},function(e){var t=e.stack,r=t.pop(),n=t.pop();exports.DEBUG&&console.log(e.step,"EQ[]",r,n),t.push(+(r===n))},function(e){var t=e.stack,r=t.pop(),n=t.pop();exports.DEBUG&&console.log(e.step,"NEQ[]",r,n),t.push(+(r!==n))},function(e){var t=e.stack,r=t.pop();exports.DEBUG&&console.log(e.step,"ODD[]",r),t.push(Math.trunc(r)%2?1:0)},function(e){var t=e.stack,r=t.pop();exports.DEBUG&&console.log(e.step,"EVEN[]",r),t.push(Math.trunc(r)%2?0:1)},function(e){var t=e.stack.pop();exports.DEBUG&&console.log(e.step,"IF[]",t),!t&&(tQ(e,!0),exports.DEBUG&&console.log(e.step,"EIF[]"))},function(e){exports.DEBUG&&console.log(e.step,"EIF[]")},function(e){var t=e.stack,r=t.pop(),n=t.pop();exports.DEBUG&&console.log(e.step,"AND[]",r,n),t.push(r&&n?1:0)},function(e){var t=e.stack,r=t.pop(),n=t.pop();exports.DEBUG&&console.log(e.step,"OR[]",r,n),t.push(r||n?1:0)},function(e){var t=e.stack,r=t.pop();exports.DEBUG&&console.log(e.step,"NOT[]",r),t.push(+!r)},rn.bind(void 0,1),function(e){var t=e.stack.pop();exports.DEBUG&&console.log(e.step,"SDB[]",t),e.deltaBase=t},function(e){var t=e.stack.pop();exports.DEBUG&&console.log(e.step,"SDS[]",t),e.deltaShift=Math.pow(.5,t)},function(e){var t=e.stack,r=t.pop(),n=t.pop();exports.DEBUG&&console.log(e.step,"ADD[]",r,n),t.push(n+r)},function(e){var t=e.stack,r=t.pop(),n=t.pop();exports.DEBUG&&console.log(e.step,"SUB[]",r,n),t.push(n-r)},function(e){var t=e.stack,r=t.pop(),n=t.pop();exports.DEBUG&&console.log(e.step,"DIV[]",r,n),t.push(64*n/r)},function(e){var t=e.stack,r=t.pop(),n=t.pop();exports.DEBUG&&console.log(e.step,"MUL[]",r,n),t.push(n*r/64)},function(e){var t=e.stack,r=t.pop();exports.DEBUG&&console.log(e.step,"ABS[]",r),t.push(Math.abs(r))},function(e){var t=e.stack,r=t.pop();exports.DEBUG&&console.log(e.step,"NEG[]",r),t.push(-r)},function(e){var t=e.stack,r=t.pop();exports.DEBUG&&console.log(e.step,"FLOOR[]",r),t.push(64*Math.floor(r/64))},function(e){var t=e.stack,r=t.pop();exports.DEBUG&&console.log(e.step,"CEILING[]",r),t.push(64*Math.ceil(r/64))},ro.bind(void 0,0),ro.bind(void 0,1),ro.bind(void 0,2),ro.bind(void 0,3),void 0,void 0,void 0,void 0,function(e){var t=e.stack,r=t.pop(),n=t.pop();exports.DEBUG&&console.log(e.step,"WCVTF[]",r,n),e.cvt[n]=r*e.ppem/e.font.unitsPerEm},rn.bind(void 0,2),rn.bind(void 0,3),ri.bind(void 0,1),ri.bind(void 0,2),ri.bind(void 0,3),function(e){var t,r=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,"SROUND[]",r),e.round=tW,192&r){case 0:t=.5;break;case 64:t=1;break;case 128:t=2;break;default:throw Error("invalid SROUND value")}switch(e.srPeriod=t,48&r){case 0:e.srPhase=0;break;case 16:e.srPhase=.25*t;break;case 32:e.srPhase=.5*t;break;case 48:e.srPhase=.75*t;break;default:throw Error("invalid SROUND value")}0==(r&=15)?e.srThreshold=0:e.srThreshold=(r/8-.5)*t},function(e){var t,r=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,"S45ROUND[]",r),e.round=tW,192&r){case 0:t=Math.sqrt(2)/2;break;case 64:t=Math.sqrt(2);break;case 128:t=2*Math.sqrt(2);break;default:throw Error("invalid S45ROUND value")}switch(e.srPeriod=t,48&r){case 0:e.srPhase=0;break;case 16:e.srPhase=.25*t;break;case 32:e.srPhase=.5*t;break;case 48:e.srPhase=.75*t;break;default:throw Error("invalid S45ROUND value")}0==(r&=15)?e.srThreshold=0:e.srThreshold=(r/8-.5)*t},void 0,void 0,function(e){exports.DEBUG&&console.log(e.step,"ROFF[]"),e.round=tB},void 0,function(e){exports.DEBUG&&console.log(e.step,"RUTG[]"),e.round=tN},function(e){exports.DEBUG&&console.log(e.step,"RDTG[]"),e.round=tj},t3,t3,void 0,void 0,void 0,void 0,void 0,function(e){var t=e.stack.pop();exports.DEBUG&&console.log(e.step,"SCANCTRL[]",t)},ra.bind(void 0,0),ra.bind(void 0,1),function(e){var t=e.stack,r=t.pop(),n=0;exports.DEBUG&&console.log(e.step,"GETINFO[]",r),1&r&&(n=35),32&r&&(n|=4096),t.push(n)},void 0,function(e){var t=e.stack,r=t.pop(),n=t.pop(),o=t.pop();exports.DEBUG&&console.log(e.step,"ROLL[]"),t.push(n),t.push(r),t.push(o)},function(e){var t=e.stack,r=t.pop(),n=t.pop();exports.DEBUG&&console.log(e.step,"MAX[]",r,n),t.push(Math.max(n,r))},function(e){var t=e.stack,r=t.pop(),n=t.pop();exports.DEBUG&&console.log(e.step,"MIN[]",r,n),t.push(Math.min(n,r))},function(e){var t=e.stack.pop();exports.DEBUG&&console.log(e.step,"SCANTYPE[]",t)},function(e){var t=e.stack.pop(),r=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,"INSTCTRL[]",t,r),t){case 1:e.inhibitGridFit=!!r;return;case 2:e.ignoreCvt=!!r;return;default:throw Error("invalid INSTCTRL[] selector")}},void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,rs.bind(void 0,1),rs.bind(void 0,2),rs.bind(void 0,3),rs.bind(void 0,4),rs.bind(void 0,5),rs.bind(void 0,6),rs.bind(void 0,7),rs.bind(void 0,8),ru.bind(void 0,1),ru.bind(void 0,2),ru.bind(void 0,3),ru.bind(void 0,4),ru.bind(void 0,5),ru.bind(void 0,6),ru.bind(void 0,7),ru.bind(void 0,8),rl.bind(void 0,0,0,0,0,0),rl.bind(void 0,0,0,0,0,1),rl.bind(void 0,0,0,0,0,2),rl.bind(void 0,0,0,0,0,3),rl.bind(void 0,0,0,0,1,0),rl.bind(void 0,0,0,0,1,1),rl.bind(void 0,0,0,0,1,2),rl.bind(void 0,0,0,0,1,3),rl.bind(void 0,0,0,1,0,0),rl.bind(void 0,0,0,1,0,1),rl.bind(void 0,0,0,1,0,2),rl.bind(void 0,0,0,1,0,3),rl.bind(void 0,0,0,1,1,0),rl.bind(void 0,0,0,1,1,1),rl.bind(void 0,0,0,1,1,2),rl.bind(void 0,0,0,1,1,3),rl.bind(void 0,0,1,0,0,0),rl.bind(void 0,0,1,0,0,1),rl.bind(void 0,0,1,0,0,2),rl.bind(void 0,0,1,0,0,3),rl.bind(void 0,0,1,0,1,0),rl.bind(void 0,0,1,0,1,1),rl.bind(void 0,0,1,0,1,2),rl.bind(void 0,0,1,0,1,3),rl.bind(void 0,0,1,1,0,0),rl.bind(void 0,0,1,1,0,1),rl.bind(void 0,0,1,1,0,2),rl.bind(void 0,0,1,1,0,3),rl.bind(void 0,0,1,1,1,0),rl.bind(void 0,0,1,1,1,1),rl.bind(void 0,0,1,1,1,2),rl.bind(void 0,0,1,1,1,3),rl.bind(void 0,1,0,0,0,0),rl.bind(void 0,1,0,0,0,1),rl.bind(void 0,1,0,0,0,2),rl.bind(void 0,1,0,0,0,3),rl.bind(void 0,1,0,0,1,0),rl.bind(void 0,1,0,0,1,1),rl.bind(void 0,1,0,0,1,2),rl.bind(void 0,1,0,0,1,3),rl.bind(void 0,1,0,1,0,0),rl.bind(void 0,1,0,1,0,1),rl.bind(void 0,1,0,1,0,2),rl.bind(void 0,1,0,1,0,3),rl.bind(void 0,1,0,1,1,0),rl.bind(void 0,1,0,1,1,1),rl.bind(void 0,1,0,1,1,2),rl.bind(void 0,1,0,1,1,3),rl.bind(void 0,1,1,0,0,0),rl.bind(void 0,1,1,0,0,1),rl.bind(void 0,1,1,0,0,2),rl.bind(void 0,1,1,0,0,3),rl.bind(void 0,1,1,0,1,0),rl.bind(void 0,1,1,0,1,1),rl.bind(void 0,1,1,0,1,2),rl.bind(void 0,1,1,0,1,3),rl.bind(void 0,1,1,1,0,0),rl.bind(void 0,1,1,1,0,1),rl.bind(void 0,1,1,1,0,2),rl.bind(void 0,1,1,1,0,3),rl.bind(void 0,1,1,1,1,0),rl.bind(void 0,1,1,1,1,1),rl.bind(void 0,1,1,1,1,2),rl.bind(void 0,1,1,1,1,3)],rc.prototype.setState=function(e,t){return this.state[e]=t,this.activeState={key:e,value:this.state[e]},this.activeState},rc.prototype.getState=function(e){return this.state[e]||null},rv.prototype.inboundIndex=function(e){return e>=0&&e<this.tokens.length},rv.prototype.composeRUD=function(e){var t=this,r=e.map(function(e){return t[e[0]].apply(t,e.slice(1).concat(!0))}),n=function(e){return"object"==typeof e&&e.hasOwnProperty("FAIL")};if(r.every(n))return{FAIL:"composeRUD: one or more operations hasn't completed successfully",report:r.filter(n)};this.dispatch("composeRUD",[r.filter(function(e){return!n(e)})])},rv.prototype.replaceRange=function(e,t,r,n){t=null!==t?t:this.tokens.length;var o=r.every(function(e){return e instanceof rc});if(!(!isNaN(e)&&this.inboundIndex(e))||!o)return{FAIL:"replaceRange: invalid tokens or startIndex."};var i=this.tokens.splice.apply(this.tokens,[e,t].concat(r));return n||this.dispatch("replaceToken",[e,t,r]),[i,r]},rv.prototype.replaceToken=function(e,t,r){if(!(!isNaN(e)&&this.inboundIndex(e))||!(t instanceof rc))return{FAIL:"replaceToken: invalid token or index."};var n=this.tokens.splice(e,1,t);return r||this.dispatch("replaceToken",[e,t]),[n[0],t]},rv.prototype.removeRange=function(e,t,r){t=isNaN(t)?this.tokens.length:t;var n=this.tokens.splice(e,t);return r||this.dispatch("removeRange",[n,e,t]),n},rv.prototype.removeToken=function(e,t){if(!(!isNaN(e)&&this.inboundIndex(e)))return{FAIL:"removeToken: invalid token index."};var r=this.tokens.splice(e,1);return t||this.dispatch("removeToken",[r,e]),r},rv.prototype.insertToken=function(e,t,r){return e.every(function(e){return e instanceof rc})?(this.tokens.splice.apply(this.tokens,[t,0].concat(e)),r||this.dispatch("insertToken",[e,t]),e):{FAIL:"insertToken: invalid token(s)."}},rv.prototype.registerModifier=function(e,t,r){this.events.newToken.subscribe(function(n,o){var i=[n,o],a=null===t||!0===t.apply(this,i),s=[n,o];if(a){var u=r.apply(this,s);n.setState(e,u)}}),this.registeredModifiers.push(e)},rd.prototype.subscribe=function(e){return"function"==typeof e?this.subscribers.push(e)-1:{FAIL:"invalid '"+this.eventId+"' event handler"}},rd.prototype.unsubscribe=function(e){this.subscribers.splice(e,1)},rh.prototype.setCurrentIndex=function(e){this.index=e,this.current=this.context[e],this.backtrack=this.context.slice(0,e),this.lookahead=this.context.slice(e+1)},rh.prototype.get=function(e){switch(!0){case 0===e:return this.current;case e<0&&Math.abs(e)<=this.backtrack.length:return this.backtrack.slice(e)[0];case e>0&&e<=this.lookahead.length:return this.lookahead[e-1];default:return null}},rv.prototype.rangeToText=function(e){if(e instanceof rf)return this.getRangeTokens(e).map(function(e){return e.char}).join("")},rv.prototype.getText=function(){return this.tokens.map(function(e){return e.char}).join("")},rv.prototype.getContext=function(e){return this.registeredContexts[e]||null},rv.prototype.on=function(e,t){var r=this.events[e];return r?r.subscribe(t):null},rv.prototype.dispatch=function(e,t){var r=this,n=this.events[e];n instanceof rd&&n.subscribers.forEach(function(e){e.apply(r,t||[])})},rv.prototype.registerContextChecker=function(e,t,r){if(this.getContext(e))return{FAIL:"context name '"+e+"' is already registered."};if("function"!=typeof t)return{FAIL:"missing context start check."};if("function"!=typeof r)return{FAIL:"missing context end check."};var n=new rp(e,t,r);return this.registeredContexts[e]=n,this.contextCheckers.push(n),n},rv.prototype.getRangeTokens=function(e){var t=e.startIndex+e.endOffset;return[].concat(this.tokens.slice(e.startIndex,t))},rv.prototype.getContextRanges=function(e){var t=this.getContext(e);return t?t.ranges:{FAIL:"context checker '"+e+"' is not registered."}},rv.prototype.resetContextsRanges=function(){var e=this.registeredContexts;for(var t in e)e.hasOwnProperty(t)&&(e[t].ranges=[])},rv.prototype.updateContextsRanges=function(){this.resetContextsRanges();for(var e=this.tokens.map(function(e){return e.char}),t=0;t<e.length;t++){var r=new rh(e,t);this.runContextCheck(r)}this.dispatch("updateContextsRanges",[this.registeredContexts])},rv.prototype.setEndOffset=function(e,t){var r=new rf(this.getContext(t).openRange.startIndex,e,t),n=this.getContext(t).ranges;return r.rangeId=t+"."+n.length,n.push(r),this.getContext(t).openRange=null,r},rv.prototype.runContextCheck=function(e){var t=this,r=e.index;this.contextCheckers.forEach(function(n){var o=n.contextName,i=t.getContext(o).openRange;if(!i&&n.checkStart(e)&&(i=new rf(r,null,o),t.getContext(o).openRange=i,t.dispatch("contextStart",[o,r])),i&&n.checkEnd(e)){var a=r-i.startIndex+1,s=t.setEndOffset(a,o);t.dispatch("contextEnd",[o,s])}})},rv.prototype.tokenize=function(e){this.tokens=[],this.resetContextsRanges();var t=Array.from(e);this.dispatch("start");for(var r=0;r<t.length;r++){var n=t[r],o=new rh(t,r);this.dispatch("next",[o]),this.runContextCheck(o);var i=new rc(n);this.tokens.push(i),this.dispatch("newToken",[i,o])}return this.dispatch("end",[this.tokens]),this.tokens},rx.prototype.getDefaultScriptFeaturesIndexes=function(){for(var e=this.font.tables.gsub.scripts,t=0;t<e.length;t++){var r=e[t];if("DFLT"===r.tag)return r.script.defaultLangSys.featureIndexes}return[]},rx.prototype.getScriptFeaturesIndexes=function(e){if(!this.font.tables.gsub)return[];if(!e)return this.getDefaultScriptFeaturesIndexes();for(var t=this.font.tables.gsub.scripts,r=0;r<t.length;r++){var n=t[r];if(n.tag===e&&n.script.defaultLangSys)return n.script.defaultLangSys.featureIndexes;var o=n.langSysRecords;if(o)for(var i=0;i<o.length;i++){var a=o[i];if(a.tag===e)return a.langSys.featureIndexes}}return this.getDefaultScriptFeaturesIndexes()},rx.prototype.mapTagsToFeatures=function(e,t){for(var r={},n=0;n<e.length;n++){var o=e[n].tag,i=e[n].feature;r[o]=i}this.features[t].tags=r},rx.prototype.getScriptFeatures=function(e){var t=this.features[e];if(this.features.hasOwnProperty(e))return t;var r=this.getScriptFeaturesIndexes(e);if(!r)return null;var n=this.font.tables.gsub;return t=r.map(function(e){return n.features[e]}),this.features[e]=t,this.mapTagsToFeatures(t,e),t},rx.prototype.getSubstitutionType=function(e,t){return e.lookupType.toString()+t.substFormat.toString()},rx.prototype.getLookupMethod=function(e,t){var r=this;switch(this.getSubstitutionType(e,t)){case"11":return function(e){return rS.apply(r,[e,t])};case"12":return function(e){return rk.apply(r,[e,t])};case"63":return function(e){return rC.apply(r,[e,t])};case"41":return function(e){return r_.apply(r,[e,t])};case"21":return function(e){return rT.apply(r,[e,t])};default:throw Error("lookupType: "+e.lookupType+" - substFormat: "+t.substFormat+" is not yet supported")}},rx.prototype.lookupFeature=function(e){var t=e.contextParams,r=t.index,n=this.getFeature({tag:e.tag,script:e.script});if(!n)return Error("font '"+this.font.names.fullName.en+"' doesn't support feature '"+e.tag+"' for script '"+e.script+"'.");for(var o=this.getFeatureLookups(n),i=[].concat(t.context),a=0;a<o.length;a++)for(var s=o[a],u=this.getLookupSubtables(s),l=0;l<u.length;l++){var c=u[l],f=this.getSubstitutionType(s,c),p=this.getLookupMethod(s,c),h=void 0;switch(f){case"11":(h=p(t.current))&&i.splice(r,1,new rw({id:11,tag:e.tag,substitution:h}));break;case"12":(h=p(t.current))&&i.splice(r,1,new rw({id:12,tag:e.tag,substitution:h}));break;case"63":Array.isArray(h=p(t))&&h.length&&i.splice(r,1,new rw({id:63,tag:e.tag,substitution:h}));break;case"41":(h=p(t))&&i.splice(r,1,new rw({id:41,tag:e.tag,substitution:h}));break;case"21":(h=p(t.current))&&i.splice(r,1,new rw({id:21,tag:e.tag,substitution:h}))}t=new rh(i,r),(!Array.isArray(h)||h.length)&&(h=null)}return i.length?i:null},rx.prototype.supports=function(e){if(!e.script)return!1;this.getScriptFeatures(e.script);var t=this.features.hasOwnProperty(e.script);if(!e.tag)return t;var r=this.features[e.script].some(function(t){return t.tag===e.tag});return t&&r},rx.prototype.getLookupSubtables=function(e){return e.subtables||null},rx.prototype.getLookupByIndex=function(e){return this.font.tables.gsub.lookups[e]||null},rx.prototype.getFeatureLookups=function(e){return e.lookupListIndexes.map(this.getLookupByIndex.bind(this))},rx.prototype.getFeature=function(e){if(!this.font)return{FAIL:"No font was found"};this.features.hasOwnProperty(e.script)||this.getScriptFeatures(e.script);var t=this.features[e.script];return t?t.tags[e.tag]?this.features[e.script].tags[e.tag]:null:{FAIL:"No feature for script "+e.script}};var rO={11:function(e,t,r){t[r].setState(e.tag,e.substitution)},12:function(e,t,r){t[r].setState(e.tag,e.substitution)},63:function(e,t,r){e.substitution.forEach(function(n,o){t[r+o].setState(e.tag,n)})},41:function(e,t,r){var n=t[r];n.setState(e.tag,e.substitution.ligGlyph);for(var o=e.substitution.components.length,i=0;i<o;i++)(n=t[r+i+1]).setState("deleted",!0)}};function rA(e,t,r){e instanceof rw&&rO[e.id]&&rO[e.id](e,t,r)}function rP(e){var t=this,r="arab",n=this.featuresTags[r],o=this.tokenizer.getRangeTokens(e);if(1!==o.length){var i=new rh(o.map(function(e){return e.getState("glyphIndex")}),0),a=new rh(o.map(function(e){return e.char}),0);o.forEach(function(e,s){if(!rD(e.char)){i.setCurrentIndex(s),a.setCurrentIndex(s);var u,l=0;switch(function(e){for(var t=[].concat(e.backtrack),r=t.length-1;r>=0;r--){var n=t[r],o=ry(n),i=rD(n);if(!o&&!i)return!0;if(o)break}return!1}(a)&&(l|=1),function(e){if(ry(e.current))return!1;for(var t=0;t<e.lookahead.length;t++)if(!rD(e.lookahead[t]))return!0;return!1}(a)&&(l|=2),l){case 1:u="fina";break;case 2:u="init";break;case 3:u="medi"}if(-1!==n.indexOf(u)){var c=t.query.lookupFeature({tag:u,script:r,contextParams:i});if(c instanceof Error)return console.info(c.message);c.forEach(function(e,t){e instanceof rw&&(rA(e,o,t),i.context[t]=e.substitution)})}}})}}function rL(e,t){return new rh(e.map(function(e){return e.activeState.value}),t||0)}function rI(e){var t=this,r=this.tokenizer.getRangeTokens(e),n=rL(r);n.context.forEach(function(e,o){n.setCurrentIndex(o);var i=t.query.lookupFeature({tag:"rlig",script:"arab",contextParams:n});i.length&&(i.forEach(function(e){return rA(e,r,o)}),n=rL(r))})}function rB(e,t){return new rh(e.map(function(e){return e.activeState.value}),t||0)}function rR(e){var t=this,r=this.tokenizer.getRangeTokens(e),n=rB(r);n.context.forEach(function(e,o){n.setCurrentIndex(o);var i=t.query.lookupFeature({tag:"liga",script:"latn",contextParams:n});i.length&&(i.forEach(function(e){return rA(e,r,o)}),n=rB(r))})}function rU(e){this.baseDir=e||"ltr",this.tokenizer=new rv,this.featuresTags={}}function rM(e){var t=this.contextChecks[e+"Check"];return this.tokenizer.registerContextChecker(e,t.startCheck,t.endCheck)}function rN(){return rM.call(this,"latinWord"),rM.call(this,"arabicWord"),rM.call(this,"arabicSentence"),this.tokenizer.tokenize(this.text)}function rj(){var e=this;this.tokenizer.getContextRanges("arabicSentence").forEach(function(t){var r=e.tokenizer.getRangeTokens(t);e.tokenizer.replaceRange(t.startIndex,t.endOffset,r.reverse())})}function rW(){if(-1===this.tokenizer.registeredModifiers.indexOf("glyphIndex"))throw Error("glyphIndex modifier is required to apply arabic presentation features.")}function rG(){var e=this;this.featuresTags.hasOwnProperty("arab")&&(rW.call(this),this.tokenizer.getContextRanges("arabicWord").forEach(function(t){rP.call(e,t)}))}function r$(){var e=this,t="arab";this.featuresTags.hasOwnProperty(t)&&-1!==this.featuresTags[t].indexOf("rlig")&&(rW.call(this),this.tokenizer.getContextRanges("arabicWord").forEach(function(t){rI.call(e,t)}))}function rz(){var e=this,t="latn";this.featuresTags.hasOwnProperty(t)&&-1!==this.featuresTags[t].indexOf("liga")&&(rW.call(this),this.tokenizer.getContextRanges("latinWord").forEach(function(t){rR.call(e,t)}))}function rq(e){(e=e||{}).tables=e.tables||{},e.empty||(tD(e.familyName,"When creating a new Font object, familyName is required."),tD(e.styleName,"When creating a new Font object, styleName is required."),tD(e.unitsPerEm,"When creating a new Font object, unitsPerEm is required."),tD(e.ascender,"When creating a new Font object, ascender is required."),tD(e.descender<=0,"When creating a new Font object, negative descender value is required."),this.unitsPerEm=e.unitsPerEm||1e3,this.ascender=e.ascender,this.descender=e.descender,this.createdTimestamp=e.createdTimestamp,this.tables=Object.assign(e.tables,{os2:Object.assign({usWeightClass:e.weightClass||this.usWeightClasses.MEDIUM,usWidthClass:e.widthClass||this.usWidthClasses.MEDIUM,fsSelection:e.fsSelection||this.fsSelectionValues.REGULAR},e.tables.os2)})),this.supported=!0,this.glyphs=new tf.GlyphSet(this,e.glyphs||[]),this.encoding=new tn(this),this.position=new tv(this),this.substitution=new tm(this),this.tables=this.tables||{},this._push=null,this._hmtxTableData={},Object.defineProperty(this,"hinting",{get:function(){return this._hinting?this._hinting:"truetype"===this.outlinesFormat?this._hinting=new tI(this):void 0}})}rU.prototype.setText=function(e){this.text=e},rU.prototype.contextChecks={latinWordCheck:{startCheck:function(e){var t=e.current,r=e.get(-1);return null===r&&rb(t)||!rb(r)&&rb(t)},endCheck:function(e){var t=e.get(1);return null===t||!rb(t)}},arabicWordCheck:{startCheck:function(e){var t=e.current,r=e.get(-1);return null===r&&rm(t)||!rm(r)&&rm(t)},endCheck:function(e){var t=e.get(1);return null===t||!rm(t)}},arabicSentenceCheck:{startCheck:function(e){var t=e.current,r=e.get(-1);return(rm(t)||rD(t))&&!rm(r)},endCheck:function(e){var t=e.get(1);switch(!0){case null===t:return!0;case!rm(t)&&!rD(t):var r=/\s/.test(t);if(!r)return!0;if(r){var n=!1;if(!e.lookahead.some(function(e){return rm(e)||rD(e)}))return!0}break;default:return!1}}}},rU.prototype.registerFeatures=function(e,t){var r=this,n=t.filter(function(t){return r.query.supports({script:e,tag:t})});this.featuresTags.hasOwnProperty(e)?this.featuresTags[e]=this.featuresTags[e].concat(n):this.featuresTags[e]=n},rU.prototype.applyFeatures=function(e,t){if(!e)throw Error("No valid font was provided to apply features");this.query||(this.query=new rx(e));for(var r=0;r<t.length;r++){var n=t[r];this.query.supports({script:n.script})&&this.registerFeatures(n.script,n.tags)}},rU.prototype.registerModifier=function(e,t,r){this.tokenizer.registerModifier(e,t,r)},rU.prototype.checkContextReady=function(e){return!!this.tokenizer.getContext(e)},rU.prototype.applyFeaturesToContexts=function(){this.checkContextReady("arabicWord")&&(rG.call(this),r$.call(this)),this.checkContextReady("latinWord")&&rz.call(this),this.checkContextReady("arabicSentence")&&rj.call(this)},rU.prototype.processText=function(e){this.text&&this.text===e||(this.setText(e),rN.call(this),this.applyFeaturesToContexts())},rU.prototype.getBidiText=function(e){return this.processText(e),this.tokenizer.getText()},rU.prototype.getTextGlyphs=function(e){this.processText(e);for(var t=[],r=0;r<this.tokenizer.tokens.length;r++){var n=this.tokenizer.tokens[r];if(!n.state.deleted){var o=n.activeState.value;t.push(Array.isArray(o)?o[0]:o)}}return t},rq.prototype.hasChar=function(e){return null!==this.encoding.charToGlyphIndex(e)},rq.prototype.charToGlyphIndex=function(e){return this.encoding.charToGlyphIndex(e)},rq.prototype.charToGlyph=function(e){var t=this.charToGlyphIndex(e),r=this.glyphs.get(t);return r||(r=this.glyphs.get(0)),r},rq.prototype.updateFeatures=function(e){return this.defaultRenderOptions.features.map(function(t){return"latn"===t.script?{script:"latn",tags:t.tags.filter(function(t){return e[t]})}:t})},rq.prototype.stringToGlyphs=function(e,t){var r=this,n=new rU;n.registerModifier("glyphIndex",null,function(e){return r.charToGlyphIndex(e.char)});var o=t?this.updateFeatures(t.features):this.defaultRenderOptions.features;n.applyFeatures(this,o);for(var i=n.getTextGlyphs(e),a=i.length,s=Array(a),u=this.glyphs.get(0),l=0;l<a;l+=1)s[l]=this.glyphs.get(i[l])||u;return s},rq.prototype.getKerningValue=function(e,t){e=e.index||e,t=t.index||t;var r=this.position.defaultKerningTables;return r?this.position.getKerningValue(r,e,t):this.kerningPairs[e+","+t]||0},rq.prototype.defaultRenderOptions={kerning:!0,features:[{script:"arab",tags:["init","medi","fina","rlig"]},{script:"latn",tags:["liga","rlig"]}]},rq.prototype.forEachGlyph=function(e,t,r,n,o,i){t=void 0!==t?t:0,r=void 0!==r?r:0,n=void 0!==n?n:72,o=Object.assign({},this.defaultRenderOptions,o);var a,s=1/this.unitsPerEm*n,u=this.stringToGlyphs(e,o);if(o.kerning){var l=o.script||this.position.getDefaultScriptName();a=this.position.getKerningTables(l,o.language)}for(var c=0;c<u.length;c+=1){var f=u[c];i.call(this,f,t,r,n,o),f.advanceWidth&&(t+=f.advanceWidth*s),o.kerning&&c<u.length-1&&(t+=(a?this.position.getKerningValue(a,f.index,u[c+1].index):this.getKerningValue(f,u[c+1]))*s),o.letterSpacing?t+=o.letterSpacing*n:o.tracking&&(t+=o.tracking/1e3*n)}return t},rq.prototype.getPath=function(e,t,r,n,o){var i=new e9;return this.forEachGlyph(e,t,r,n,o,function(e,t,r,n){var a=e.getPath(t,r,n,o,this);i.extend(a)}),i},rq.prototype.getPaths=function(e,t,r,n,o){var i=[];return this.forEachGlyph(e,t,r,n,o,function(e,t,r,n){var a=e.getPath(t,r,n,o,this);i.push(a)}),i},rq.prototype.getAdvanceWidth=function(e,t,r){return this.forEachGlyph(e,0,0,t,r,function(){})},rq.prototype.fsSelectionValues={ITALIC:1,UNDERSCORE:2,NEGATIVE:4,OUTLINED:8,STRIKEOUT:16,BOLD:32,REGULAR:64,USER_TYPO_METRICS:128,WWS:256,OBLIQUE:512},rq.prototype.usWidthClasses={ULTRA_CONDENSED:1,EXTRA_CONDENSED:2,CONDENSED:3,SEMI_CONDENSED:4,MEDIUM:5,SEMI_EXPANDED:6,EXPANDED:7,EXTRA_EXPANDED:8,ULTRA_EXPANDED:9},rq.prototype.usWeightClasses={THIN:100,EXTRA_LIGHT:200,LIGHT:300,NORMAL:400,MEDIUM:500,SEMI_BOLD:600,BOLD:700,EXTRA_BOLD:800,BLACK:900};var rV={parse:function(e,t){var r={};r.version=tC.getUShort(e,t),ts.argument(0===r.version,"cmap table version should be 0."),r.numTables=tC.getUShort(e,t+2);for(var n=-1,o=r.numTables-1;o>=0;o-=1){var i=tC.getUShort(e,t+4+8*o),a=tC.getUShort(e,t+4+8*o+2);if(3===i&&(0===a||1===a||10===a)||0===i&&(0===a||1===a||2===a||3===a||4===a)){n=tC.getULong(e,t+4+8*o+4);break}}if(-1===n)throw Error("No valid cmap sub-tables found.");var s=new tC.Parser(e,t+n);if(r.format=s.parseUShort(),12===r.format)!function(e,t){t.parseUShort(),e.length=t.parseULong(),e.language=t.parseULong(),e.groupCount=r=t.parseULong(),e.glyphIndexMap={};for(var r,n=0;n<r;n+=1)for(var o=t.parseULong(),i=t.parseULong(),a=t.parseULong(),s=o;s<=i;s+=1)e.glyphIndexMap[s]=a,a++}(r,s);else if(4===r.format)!function(e,t,r,n,o){e.length=t.parseUShort(),e.language=t.parseUShort(),e.segCount=i=t.parseUShort()>>1,t.skip("uShort",3),e.glyphIndexMap={};for(var i,a=new tC.Parser(r,n+o+14),s=new tC.Parser(r,n+o+16+2*i),u=new tC.Parser(r,n+o+16+4*i),l=new tC.Parser(r,n+o+16+6*i),c=n+o+16+8*i,f=0;f<i-1;f+=1)for(var p=void 0,h=a.parseUShort(),d=s.parseUShort(),g=u.parseShort(),v=l.parseUShort(),m=d;m<=h;m+=1)0!==v?(c=l.offset+l.relativeOffset-2+v+(m-d)*2,0!==(p=tC.getUShort(r,c))&&(p=p+g&65535)):p=m+g&65535,e.glyphIndexMap[m]=p}(r,s,e,t,n);else throw Error("Only format 4 and 12 cmap tables are supported (found format "+r.format+").");return r}};function rH(e){var t;return e.length<1240?107:e.length<33900?1131:32768}function rX(e,t,r){var n,o,i=[],a=[],s=tC.getCard16(e,t);if(0!==s){var u=tC.getByte(e,t+2);n=t+(s+1)*u+2;for(var l=t+3,c=0;c<s+1;c+=1)i.push(tC.getOffset(e,l,u)),l+=u;o=n+i[s]}else o=t+2;for(var f=0;f<i.length-1;f+=1){var p=tC.getBytes(e,n+i[f],n+i[f+1]);r&&(p=r(p)),a.push(p)}return{objects:a,startOffset:t,endOffset:o}}function rY(e,t,r){t=void 0!==t?t:0;var n=new tC.Parser(e,t),o=[],i=[];for(r=void 0!==r?r:e.length;n.relativeOffset<r;){var a=n.parseByte();a<=21?(12===a&&(a=1200+n.parseByte()),o.push([a,i]),i=[]):i.push(function(e,t){var r,n;if(28===t)return(r=e.parseByte())<<8|(n=e.parseByte());if(29===t)return r=e.parseByte(),n=e.parseByte(),r<<24|n<<16|e.parseByte()<<8|e.parseByte();if(30===t)return function(e){for(var t="",r=["0","1","2","3","4","5","6","7","8","9",".","E","E-",null,"-"];;){var n=e.parseByte(),o=n>>4,i=15&n;if(15===o||(t+=r[o],15===i))break;t+=r[i]}return parseFloat(t)}(e);if(t>=32&&t<=246)return t-139;if(t>=247&&t<=250)return(t-247)*256+(r=e.parseByte())+108;if(t>=251&&t<=254)return-(256*(t-251))-(r=e.parseByte())-108;throw Error("Invalid b0 "+t)}(n,a))}return function(e){for(var t={},r=0;r<e.length;r+=1){var n=e[r][0],o=e[r][1],i=void 0;if(i=1===o.length?o[0]:o,t.hasOwnProperty(n)&&!isNaN(t[n]))throw Error("Object "+t+" already has key "+n);t[n]=i}return t}(o)}function rZ(e,t){return t=t<=390?te[t]:e[t-391]}function rQ(e,t,r){for(var n,o={},i=0;i<t.length;i+=1){var a=t[i];if(Array.isArray(a.type)){var s=[];s.length=a.type.length;for(var u=0;u<a.type.length;u++)void 0===(n=void 0!==e[a.op]?e[a.op][u]:void 0)&&(n=void 0!==a.value&&void 0!==a.value[u]?a.value[u]:null),"SID"===a.type[u]&&(n=rZ(r,n)),s[u]=n;o[a.name]=s}else void 0===(n=e[a.op])&&(n=void 0!==a.value?a.value:null),"SID"===a.type&&(n=rZ(r,n)),o[a.name]=n}return o}var rK=[{name:"version",op:0,type:"SID"},{name:"notice",op:1,type:"SID"},{name:"copyright",op:1200,type:"SID"},{name:"fullName",op:2,type:"SID"},{name:"familyName",op:3,type:"SID"},{name:"weight",op:4,type:"SID"},{name:"isFixedPitch",op:1201,type:"number",value:0},{name:"italicAngle",op:1202,type:"number",value:0},{name:"underlinePosition",op:1203,type:"number",value:-100},{name:"underlineThickness",op:1204,type:"number",value:50},{name:"paintType",op:1205,type:"number",value:0},{name:"charstringType",op:1206,type:"number",value:2},{name:"fontMatrix",op:1207,type:["real","real","real","real","real","real"],value:[.001,0,0,.001,0,0]},{name:"uniqueId",op:13,type:"number"},{name:"fontBBox",op:5,type:["number","number","number","number"],value:[0,0,0,0]},{name:"strokeWidth",op:1208,type:"number",value:0},{name:"xuid",op:14,type:[],value:null},{name:"charset",op:15,type:"offset",value:0},{name:"encoding",op:16,type:"offset",value:0},{name:"charStrings",op:17,type:"offset",value:0},{name:"private",op:18,type:["number","offset"],value:[0,0]},{name:"ros",op:1230,type:["SID","SID","number"]},{name:"cidFontVersion",op:1231,type:"number",value:0},{name:"cidFontRevision",op:1232,type:"number",value:0},{name:"cidFontType",op:1233,type:"number",value:0},{name:"cidCount",op:1234,type:"number",value:8720},{name:"uidBase",op:1235,type:"number"},{name:"fdArray",op:1236,type:"offset"},{name:"fdSelect",op:1237,type:"offset"},{name:"fontName",op:1238,type:"SID"}],rJ=[{name:"subrs",op:19,type:"offset",value:0},{name:"defaultWidthX",op:20,type:"number",value:0},{name:"nominalWidthX",op:21,type:"number",value:0}];function r0(e,t,r,n){for(var o=[],i=0;i<r.length;i+=1){var a=(l=new DataView(new Uint8Array(r[i]).buffer),rQ(rY(l,0,l.byteLength),rK,n));a._subrs=[],a._subrsBias=0,a._defaultWidthX=0,a._nominalWidthX=0;var s=a.private[0],u=a.private[1];if(0!==s&&0!==u){var l,c=rQ(rY(e,u+t,s),rJ,n);a._defaultWidthX=c.defaultWidthX,a._nominalWidthX=c.nominalWidthX,0!==c.subrs&&(a._subrs=rX(e,u+c.subrs+t).objects,a._subrsBias=rH(a._subrs)),a._privateDict=c}o.push(a)}return o}function r1(e,t,r){var n,o,i,a,s,u,l,c,f=new e9,p=[],h=0,d=!1,g=!1,v=0,m=0;if(e.isCIDFont){var y=e.tables.cff.topDict._fdSelect[t.index],D=e.tables.cff.topDict._fdArray[y];s=D._subrs,u=D._subrsBias,l=D._defaultWidthX,c=D._nominalWidthX}else s=e.tables.cff.topDict._subrs,u=e.tables.cff.topDict._subrsBias,l=e.tables.cff.topDict._defaultWidthX,c=e.tables.cff.topDict._nominalWidthX;var b=l;function x(e,t){g&&f.closePath(),f.moveTo(e,t),g=!0}function w(){p.length%2==0||d||(b=p.shift()+c),h+=p.length>>1,p.length=0,d=!0}return!function r(l){for(var y,D,E,S,k,F,C,_,T,O,A,P,L=0;L<l.length;){var I=l[L];switch(L+=1,I){case 1:case 3:case 18:case 23:w();break;case 4:p.length>1&&!d&&(b=p.shift()+c,d=!0),m+=p.pop(),x(v,m);break;case 5:for(;p.length>0;)v+=p.shift(),m+=p.shift(),f.lineTo(v,m);break;case 6:for(;p.length>0&&(v+=p.shift(),f.lineTo(v,m),0!==p.length);)m+=p.shift(),f.lineTo(v,m);break;case 7:for(;p.length>0&&(m+=p.shift(),f.lineTo(v,m),0!==p.length);)v+=p.shift(),f.lineTo(v,m);break;case 8:for(;p.length>0;)n=v+p.shift(),o=m+p.shift(),i=n+p.shift(),a=o+p.shift(),v=i+p.shift(),m=a+p.shift(),f.curveTo(n,o,i,a,v,m);break;case 10:(F=s[k=p.pop()+u])&&r(F);break;case 11:return;case 12:switch(I=l[L],L+=1,I){case 35:n=v+p.shift(),o=m+p.shift(),i=n+p.shift(),a=o+p.shift(),C=i+p.shift(),_=a+p.shift(),T=C+p.shift(),O=_+p.shift(),A=T+p.shift(),P=O+p.shift(),v=A+p.shift(),m=P+p.shift(),p.shift(),f.curveTo(n,o,i,a,C,_),f.curveTo(T,O,A,P,v,m);break;case 34:n=v+p.shift(),o=m,i=n+p.shift(),a=o+p.shift(),C=i+p.shift(),_=a,T=C+p.shift(),O=a,A=T+p.shift(),P=m,v=A+p.shift(),f.curveTo(n,o,i,a,C,_),f.curveTo(T,O,A,P,v,m);break;case 36:n=v+p.shift(),o=m+p.shift(),i=n+p.shift(),a=o+p.shift(),C=i+p.shift(),_=a,T=C+p.shift(),O=a,A=T+p.shift(),P=O+p.shift(),v=A+p.shift(),f.curveTo(n,o,i,a,C,_),f.curveTo(T,O,A,P,v,m);break;case 37:n=v+p.shift(),o=m+p.shift(),i=n+p.shift(),a=o+p.shift(),C=i+p.shift(),_=a+p.shift(),T=C+p.shift(),O=_+p.shift(),A=T+p.shift(),P=O+p.shift(),Math.abs(A-v)>Math.abs(P-m)?v=A+p.shift():m=P+p.shift(),f.curveTo(n,o,i,a,C,_),f.curveTo(T,O,A,P,v,m);break;default:console.log("Glyph "+t.index+": unknown operator 1200"+I),p.length=0}break;case 14:p.length>0&&!d&&(b=p.shift()+c,d=!0),g&&(f.closePath(),g=!1);break;case 19:case 20:w(),L+=h+7>>3;break;case 21:p.length>2&&!d&&(b=p.shift()+c,d=!0),m+=p.pop(),x(v+=p.pop(),m);break;case 22:p.length>1&&!d&&(b=p.shift()+c,d=!0),x(v+=p.pop(),m);break;case 24:for(;p.length>2;)n=v+p.shift(),o=m+p.shift(),i=n+p.shift(),a=o+p.shift(),v=i+p.shift(),m=a+p.shift(),f.curveTo(n,o,i,a,v,m);v+=p.shift(),m+=p.shift(),f.lineTo(v,m);break;case 25:for(;p.length>6;)v+=p.shift(),m+=p.shift(),f.lineTo(v,m);n=v+p.shift(),o=m+p.shift(),i=n+p.shift(),a=o+p.shift(),v=i+p.shift(),m=a+p.shift(),f.curveTo(n,o,i,a,v,m);break;case 26:for(p.length%2&&(v+=p.shift());p.length>0;)n=v,o=m+p.shift(),i=n+p.shift(),a=o+p.shift(),v=i,m=a+p.shift(),f.curveTo(n,o,i,a,v,m);break;case 27:for(p.length%2&&(m+=p.shift());p.length>0;)n=v+p.shift(),o=m,i=n+p.shift(),a=o+p.shift(),v=i+p.shift(),m=a,f.curveTo(n,o,i,a,v,m);break;case 28:y=l[L],D=l[L+1],p.push((y<<24|D<<16)>>16),L+=2;break;case 29:k=p.pop()+e.gsubrsBias,(F=e.gsubrs[k])&&r(F);break;case 30:for(;p.length>0&&(n=v,o=m+p.shift(),i=n+p.shift(),a=o+p.shift(),v=i+p.shift(),m=a+(1===p.length?p.shift():0),f.curveTo(n,o,i,a,v,m),0!==p.length);)n=v+p.shift(),o=m,i=n+p.shift(),m=(a=o+p.shift())+p.shift(),v=i+(1===p.length?p.shift():0),f.curveTo(n,o,i,a,v,m);break;case 31:for(;p.length>0&&(n=v+p.shift(),o=m,i=n+p.shift(),m=(a=o+p.shift())+p.shift(),v=i+(1===p.length?p.shift():0),f.curveTo(n,o,i,a,v,m),0!==p.length);)n=v,o=m+p.shift(),i=n+p.shift(),a=o+p.shift(),v=i+p.shift(),m=a+(1===p.length?p.shift():0),f.curveTo(n,o,i,a,v,m);break;default:I<32?console.log("Glyph "+t.index+": unknown operator "+I):I<247?p.push(I-139):I<251?(y=l[L],L+=1,p.push((I-247)*256+y+108)):I<255?(y=l[L],L+=1,p.push(-(256*(I-251))-y-108)):(y=l[L],D=l[L+1],E=l[L+2],S=l[L+3],L+=4,p.push((y<<24|D<<16|E<<8|S)/65536))}}}(r),t.advanceWidth=b,f}var r2={parse:function(e,t,r,n){r.tables.cff={};var o,i,a,s,u=((a={}).formatMajor=tC.getCard8(e,t),a.formatMinor=tC.getCard8(e,t+1),a.size=tC.getCard8(e,t+2),a.offsetSize=tC.getCard8(e,t+3),a.startOffset=t,a.endOffset=t+4,a),l=rX(e,u.endOffset,tC.bytesToString),c=rX(e,l.endOffset),f=rX(e,c.endOffset,tC.bytesToString);r.gsubrs=rX(e,f.endOffset).objects,r.gsubrsBias=rH(r.gsubrs);var p=r0(e,t,c.objects,f.objects);if(1!==p.length)throw Error("CFF table has too many fonts in 'FontSet' - count of fonts NameIndex.length = "+p.length);var h=p[0];if(r.tables.cff.topDict=h,h._privateDict&&(r.defaultWidthX=h._privateDict.defaultWidthX,r.nominalWidthX=h._privateDict.nominalWidthX),void 0!==h.ros[0]&&void 0!==h.ros[1]&&(r.isCIDFont=!0),r.isCIDFont){var d=h.fdArray,g=h.fdSelect;if(0===d||0===g)throw Error("Font is marked as a CID font, but FDArray and/or FDSelect information is missing");var v=rX(e,d+=t),m=r0(e,t,v.objects,f.objects);h._fdArray=m,h._fdSelect=function(e,t,r,n){var o=[],i=new tC.Parser(e,t),a=i.parseCard8();if(0===a)for(var s=0;s<r;s++){if((u=i.parseCard8())>=n)throw Error("CFF table CID Font FDSelect has bad FD index value "+u+" (FD count "+n+")");o.push(u)}else if(3===a){var u,l,c=i.parseCard16(),f=i.parseCard16();if(0!==f)throw Error("CFF Table CID Font FDSelect format 3 range has bad initial GID "+f);for(var p=0;p<c;p++){if(u=i.parseCard8(),l=i.parseCard16(),u>=n)throw Error("CFF table CID Font FDSelect has bad FD index value "+u+" (FD count "+n+")");if(l>r)throw Error("CFF Table CID Font FDSelect format 3 range has bad GID "+l);for(;f<l;f++)o.push(u);f=l}if(l!==r)throw Error("CFF Table CID Font FDSelect format 3 range has bad final GID "+l)}else throw Error("CFF Table CID Font FDSelect table has unsupported format "+a);return o}(e,g+=t,r.numGlyphs,m.length)}var y=t+h.private[1],D=(o=h.private[0],i=f.objects,rQ(rY(e,y,o),rJ,i));r.defaultWidthX=D.defaultWidthX,r.nominalWidthX=D.nominalWidthX,0!==D.subrs?(r.subrs=rX(e,y+D.subrs).objects,r.subrsBias=rH(r.subrs)):(r.subrs=[],r.subrsBias=0),n.lowMemory?r.nGlyphs=(s=function(e,t){var r,n,o=[],i=tC.getCard16(e,t);if(0!==i){var a=tC.getByte(e,t+2);r=t+(i+1)*a+2;for(var s=t+3,u=0;u<i+1;u+=1)o.push(tC.getOffset(e,s,a)),s+=a;n=r+o[i]}else n=t+2;return{offsets:o,startOffset:t,endOffset:n}}(e,t+h.charStrings)).offsets.length:r.nGlyphs=(s=rX(e,t+h.charStrings)).objects.length;var b=function(e,t,r,n){var o,i,a=new tC.Parser(e,t);r-=1;var s=[".notdef"],u=a.parseCard8();if(0===u)for(var l=0;l<r;l+=1)o=a.parseSID(),s.push(rZ(n,o));else if(1===u)for(;s.length<=r;){o=a.parseSID(),i=a.parseCard8();for(var c=0;c<=i;c+=1)s.push(rZ(n,o)),o+=1}else if(2===u)for(;s.length<=r;){o=a.parseSID(),i=a.parseCard16();for(var f=0;f<=i;f+=1)s.push(rZ(n,o)),o+=1}else throw Error("Unknown charset format "+u);return s}(e,t+h.charset,r.nGlyphs,f.objects);if(0===h.encoding?r.cffEncoding=new ti(tt,b):1===h.encoding?r.cffEncoding=new ti(tr,b):r.cffEncoding=function(e,t,r){var n,o={},i=new tC.Parser(e,t),a=i.parseCard8();if(0===a)for(var s=i.parseCard8(),u=0;u<s;u+=1)o[n=i.parseCard8()]=u;else if(1===a){var l=i.parseCard8();n=1;for(var c=0;c<l;c+=1)for(var f=i.parseCard8(),p=i.parseCard8(),h=f;h<=f+p;h+=1)o[h]=n,n+=1}else throw Error("Unknown encoding format "+a);return new ti(o,r)}(e,t+h.encoding,b),r.encoding=r.encoding||r.cffEncoding,r.glyphs=new tf.GlyphSet(r),n.lowMemory)r._push=function(n){var o=function(e,t,r,n,o){var i=tC.getCard16(r,n),a=0;if(0!==i){var s=tC.getByte(r,n+2);a=n+(i+1)*s+2}return tC.getBytes(r,a+t[e],a+t[e+1])}(n,s.offsets,e,t+h.charStrings);r.glyphs.push(n,tf.cffGlyphLoader(r,n,r1,o))};else for(var x=0;x<r.nGlyphs;x+=1){var w=s.objects[x];r.glyphs.push(x,tf.cffGlyphLoader(r,x,r1,w))}}},r3={parse:function(e,t,r){var n=new tC.Parser(e,t),o=n.parseULong();ts.argument(65536===o,"Unsupported fvar table version.");var i=n.parseOffset16();n.skip("uShort",1);for(var a=n.parseUShort(),s=n.parseUShort(),u=n.parseUShort(),l=n.parseUShort(),c=[],f=0;f<a;f++)c.push(function(e,t,r){var n={},o=new tC.Parser(e,t);return n.tag=o.parseTag(),n.minValue=o.parseFixed(),n.defaultValue=o.parseFixed(),n.maxValue=o.parseFixed(),o.skip("uShort",1),n.name=r[o.parseUShort()]||{},n}(e,t+i+f*s,r));for(var p=[],h=t+i+a*s,d=0;d<u;d++)p.push(function(e,t,r,n){var o={},i=new tC.Parser(e,t);o.name=n[i.parseUShort()]||{},i.skip("uShort",1),o.coordinates={};for(var a=0;a<r.length;++a)o.coordinates[r[a].tag]=i.parseFixed();return o}(e,h+d*l,c,r));return{axes:c,instances:p}}},r4=function(){return{coverage:this.parsePointer(tk.coverage),attachPoints:this.parseList(tk.pointer(tk.uShortList))}},r5=function(){var e=this.parseUShort();return(ts.argument(1===e||2===e||3===e,"Unsupported CaretValue table version."),1===e)?{coordinate:this.parseShort()}:2===e?{pointindex:this.parseShort()}:3===e?{coordinate:this.parseShort()}:void 0},r6=function(){return this.parseList(tk.pointer(r5))},r8=function(){return{coverage:this.parsePointer(tk.coverage),ligGlyphs:this.parseList(tk.pointer(r6))}},r7=function(){return this.parseUShort(),this.parseList(tk.pointer(tk.coverage))},r9={parse:function(e,t){var r=new tk(e,t=t||0),n=r.parseVersion(1);ts.argument(1===n||1.2===n||1.3===n,"Unsupported GDEF table version.");var o={version:n,classDef:r.parsePointer(tk.classDef),attachList:r.parsePointer(r4),ligCaretList:r.parsePointer(r8),markAttachClassDef:r.parsePointer(tk.classDef)};return n>=1.2&&(o.markGlyphSets=r.parsePointer(r7)),o}},ne=Array(10);ne[1]=function(){var e=this.offset+this.relativeOffset,t=this.parseUShort();return 1===t?{posFormat:1,coverage:this.parsePointer(tk.coverage),value:this.parseValueRecord()}:2===t?{posFormat:2,coverage:this.parsePointer(tk.coverage),values:this.parseValueRecordList()}:void ts.assert(!1,"0x"+e.toString(16)+": GPOS lookup type 1 format must be 1 or 2.")},ne[2]=function(){var e=this.offset+this.relativeOffset,t=this.parseUShort();ts.assert(1===t||2===t,"0x"+e.toString(16)+": GPOS lookup type 2 format must be 1 or 2.");var r=this.parsePointer(tk.coverage),n=this.parseUShort(),o=this.parseUShort();if(1===t)return{posFormat:t,coverage:r,valueFormat1:n,valueFormat2:o,pairSets:this.parseList(tk.pointer(tk.list(function(){return{secondGlyph:this.parseUShort(),value1:this.parseValueRecord(n),value2:this.parseValueRecord(o)}})))};if(2===t){var i=this.parsePointer(tk.classDef),a=this.parsePointer(tk.classDef),s=this.parseUShort(),u=this.parseUShort();return{posFormat:t,coverage:r,valueFormat1:n,valueFormat2:o,classDef1:i,classDef2:a,class1Count:s,class2Count:u,classRecords:this.parseList(s,tk.list(u,function(){return{value1:this.parseValueRecord(n),value2:this.parseValueRecord(o)}}))}}},ne[3]=function(){return{error:"GPOS Lookup 3 not supported"}},ne[4]=function(){return{error:"GPOS Lookup 4 not supported"}},ne[5]=function(){return{error:"GPOS Lookup 5 not supported"}},ne[6]=function(){return{error:"GPOS Lookup 6 not supported"}},ne[7]=function(){return{error:"GPOS Lookup 7 not supported"}},ne[8]=function(){return{error:"GPOS Lookup 8 not supported"}},ne[9]=function(){return{error:"GPOS Lookup 9 not supported"}};var nt={parse:function(e,t){var r=new tk(e,t=t||0),n=r.parseVersion(1);return(ts.argument(1===n||1.1===n,"Unsupported GPOS table version "+n),1===n)?{version:n,scripts:r.parseScriptList(),features:r.parseFeatureList(),lookups:r.parseLookupList(ne)}:{version:n,scripts:r.parseScriptList(),features:r.parseFeatureList(),lookups:r.parseLookupList(ne),variations:r.parseFeatureVariationsList()}}},nr=Array(9);nr[1]=function(){var e=this.offset+this.relativeOffset,t=this.parseUShort();return 1===t?{substFormat:1,coverage:this.parsePointer(tk.coverage),deltaGlyphId:this.parseUShort()}:2===t?{substFormat:2,coverage:this.parsePointer(tk.coverage),substitute:this.parseOffset16List()}:void ts.assert(!1,"0x"+e.toString(16)+": lookup type 1 format must be 1 or 2.")},nr[2]=function(){var e=this.parseUShort();return ts.argument(1===e,"GSUB Multiple Substitution Subtable identifier-format must be 1"),{substFormat:e,coverage:this.parsePointer(tk.coverage),sequences:this.parseListOfLists()}},nr[3]=function(){var e=this.parseUShort();return ts.argument(1===e,"GSUB Alternate Substitution Subtable identifier-format must be 1"),{substFormat:e,coverage:this.parsePointer(tk.coverage),alternateSets:this.parseListOfLists()}},nr[4]=function(){var e=this.parseUShort();return ts.argument(1===e,"GSUB ligature table identifier-format must be 1"),{substFormat:e,coverage:this.parsePointer(tk.coverage),ligatureSets:this.parseListOfLists(function(){return{ligGlyph:this.parseUShort(),components:this.parseUShortList(this.parseUShort()-1)}})}};var nn={sequenceIndex:tk.uShort,lookupListIndex:tk.uShort};nr[5]=function(){var e=this.offset+this.relativeOffset,t=this.parseUShort();if(1===t)return{substFormat:t,coverage:this.parsePointer(tk.coverage),ruleSets:this.parseListOfLists(function(){var e=this.parseUShort(),t=this.parseUShort();return{input:this.parseUShortList(e-1),lookupRecords:this.parseRecordList(t,nn)}})};if(2===t)return{substFormat:t,coverage:this.parsePointer(tk.coverage),classDef:this.parsePointer(tk.classDef),classSets:this.parseListOfLists(function(){var e=this.parseUShort(),t=this.parseUShort();return{classes:this.parseUShortList(e-1),lookupRecords:this.parseRecordList(t,nn)}})};if(3===t){var r=this.parseUShort(),n=this.parseUShort();return{substFormat:t,coverages:this.parseList(r,tk.pointer(tk.coverage)),lookupRecords:this.parseRecordList(n,nn)}}ts.assert(!1,"0x"+e.toString(16)+": lookup type 5 format must be 1, 2 or 3.")},nr[6]=function(){var e=this.offset+this.relativeOffset,t=this.parseUShort();return 1===t?{substFormat:1,coverage:this.parsePointer(tk.coverage),chainRuleSets:this.parseListOfLists(function(){return{backtrack:this.parseUShortList(),input:this.parseUShortList(this.parseShort()-1),lookahead:this.parseUShortList(),lookupRecords:this.parseRecordList(nn)}})}:2===t?{substFormat:2,coverage:this.parsePointer(tk.coverage),backtrackClassDef:this.parsePointer(tk.classDef),inputClassDef:this.parsePointer(tk.classDef),lookaheadClassDef:this.parsePointer(tk.classDef),chainClassSet:this.parseListOfLists(function(){return{backtrack:this.parseUShortList(),input:this.parseUShortList(this.parseShort()-1),lookahead:this.parseUShortList(),lookupRecords:this.parseRecordList(nn)}})}:3===t?{substFormat:3,backtrackCoverage:this.parseList(tk.pointer(tk.coverage)),inputCoverage:this.parseList(tk.pointer(tk.coverage)),lookaheadCoverage:this.parseList(tk.pointer(tk.coverage)),lookupRecords:this.parseRecordList(nn)}:void ts.assert(!1,"0x"+e.toString(16)+": lookup type 6 format must be 1, 2 or 3.")},nr[7]=function(){var e=this.parseUShort();ts.argument(1===e,"GSUB Extension Substitution subtable identifier-format must be 1");var t=this.parseUShort(),r=new tk(this.data,this.offset+this.parseULong());return{substFormat:1,lookupType:t,extension:nr[t].call(r)}},nr[8]=function(){var e=this.parseUShort();return ts.argument(1===e,"GSUB Reverse Chaining Contextual Single Substitution Subtable identifier-format must be 1"),{substFormat:e,coverage:this.parsePointer(tk.coverage),backtrackCoverage:this.parseList(tk.pointer(tk.coverage)),lookaheadCoverage:this.parseList(tk.pointer(tk.coverage)),substitutes:this.parseUShortList()}};var no={parse:function(e,t){var r=new tk(e,t=t||0),n=r.parseVersion(1);return(ts.argument(1===n||1.1===n,"Unsupported GSUB table version."),1===n)?{version:n,scripts:r.parseScriptList(),features:r.parseFeatureList(),lookups:r.parseLookupList(nr)}:{version:n,scripts:r.parseScriptList(),features:r.parseFeatureList(),lookups:r.parseLookupList(nr),variations:r.parseFeatureVariationsList()}}},ni={parse:function(e,t){var r={},n=new tC.Parser(e,t);return r.version=n.parseVersion(),r.fontRevision=Math.round(1e3*n.parseFixed())/1e3,r.checkSumAdjustment=n.parseULong(),r.magicNumber=n.parseULong(),ts.argument(0x5f0f3cf5===r.magicNumber,"Font header has wrong magic number."),r.flags=n.parseUShort(),r.unitsPerEm=n.parseUShort(),r.created=n.parseLongDateTime(),r.modified=n.parseLongDateTime(),r.xMin=n.parseShort(),r.yMin=n.parseShort(),r.xMax=n.parseShort(),r.yMax=n.parseShort(),r.macStyle=n.parseUShort(),r.lowestRecPPEM=n.parseUShort(),r.fontDirectionHint=n.parseShort(),r.indexToLocFormat=n.parseShort(),r.glyphDataFormat=n.parseShort(),r}},na={parse:function(e,t){var r={},n=new tC.Parser(e,t);return r.version=n.parseVersion(),r.ascender=n.parseShort(),r.descender=n.parseShort(),r.lineGap=n.parseShort(),r.advanceWidthMax=n.parseUShort(),r.minLeftSideBearing=n.parseShort(),r.minRightSideBearing=n.parseShort(),r.xMaxExtent=n.parseShort(),r.caretSlopeRise=n.parseShort(),r.caretSlopeRun=n.parseShort(),r.caretOffset=n.parseShort(),n.relativeOffset+=8,r.metricDataFormat=n.parseShort(),r.numberOfHMetrics=n.parseUShort(),r}},ns={parse:function(e,t,r,n,o,i,a){if(a.lowMemory){e._hmtxTableData={};for(var s,u,l=new tC.Parser(t,r),c=0;c<o;c+=1)c<n&&(s=l.parseUShort(),u=l.parseShort()),e._hmtxTableData[c]={advanceWidth:s,leftSideBearing:u}}else!function(e,t,r,n,o){for(var i,a,s=new tC.Parser(e,t),u=0;u<n;u+=1){u<r&&(i=s.parseUShort(),a=s.parseShort());var l=o.get(u);l.advanceWidth=i,l.leftSideBearing=a}}(t,r,n,o,i)}},nu={parse:function(e,t){var r=new tC.Parser(e,t),n=r.parseUShort();if(0===n)return function(e){var t={};e.skip("uShort");var r=e.parseUShort();ts.argument(0===r,"Unsupported kern sub-table version."),e.skip("uShort",2);var n=e.parseUShort();e.skip("uShort",3);for(var o=0;o<n;o+=1){var i=e.parseUShort(),a=e.parseUShort(),s=e.parseShort();t[i+","+a]=s}return t}(r);if(1===n)return function(e){var t={};e.skip("uShort"),e.parseULong()>1&&console.warn("Only the first kern subtable is supported."),e.skip("uLong");var r=e.parseUShort();if(e.skip("uShort"),0==(255&r)){var n=e.parseUShort();e.skip("uShort",3);for(var o=0;o<n;o+=1){var i=e.parseUShort(),a=e.parseUShort(),s=e.parseShort();t[i+","+a]=s}}return t}(r);throw Error("Unsupported kern table version ("+n+").")}},nl={parse:function(e,t){var r=new tC.Parser(e,t),n=r.parseULong();ts.argument(1===n,"Unsupported ltag table version."),r.skip("uLong",1);for(var o=r.parseULong(),i=[],a=0;a<o;a++){for(var s="",u=t+r.parseUShort(),l=r.parseUShort(),c=u;c<u+l;++c)s+=String.fromCharCode(e.getInt8(c));i.push(s)}return i}},nc={parse:function(e,t,r,n){for(var o=new tC.Parser(e,t),i=n?o.parseUShort:o.parseULong,a=[],s=0;s<r+1;s+=1){var u=i.call(o);n&&(u*=2),a.push(u)}return a}},nf={parse:function(e,t){var r={},n=new tC.Parser(e,t);return r.version=n.parseVersion(),r.numGlyphs=n.parseUShort(),1===r.version&&(r.maxPoints=n.parseUShort(),r.maxContours=n.parseUShort(),r.maxCompositePoints=n.parseUShort(),r.maxCompositeContours=n.parseUShort(),r.maxZones=n.parseUShort(),r.maxTwilightPoints=n.parseUShort(),r.maxStorage=n.parseUShort(),r.maxFunctionDefs=n.parseUShort(),r.maxInstructionDefs=n.parseUShort(),r.maxStackElements=n.parseUShort(),r.maxSizeOfInstructions=n.parseUShort(),r.maxComponentElements=n.parseUShort(),r.maxComponentDepth=n.parseUShort()),r}},np={parse:function(e,t){var r={},n=new tC.Parser(e,t);r.version=n.parseUShort(),r.xAvgCharWidth=n.parseShort(),r.usWeightClass=n.parseUShort(),r.usWidthClass=n.parseUShort(),r.fsType=n.parseUShort(),r.ySubscriptXSize=n.parseShort(),r.ySubscriptYSize=n.parseShort(),r.ySubscriptXOffset=n.parseShort(),r.ySubscriptYOffset=n.parseShort(),r.ySuperscriptXSize=n.parseShort(),r.ySuperscriptYSize=n.parseShort(),r.ySuperscriptXOffset=n.parseShort(),r.ySuperscriptYOffset=n.parseShort(),r.yStrikeoutSize=n.parseShort(),r.yStrikeoutPosition=n.parseShort(),r.sFamilyClass=n.parseShort(),r.panose=[];for(var o=0;o<10;o++)r.panose[o]=n.parseByte();return r.ulUnicodeRange1=n.parseULong(),r.ulUnicodeRange2=n.parseULong(),r.ulUnicodeRange3=n.parseULong(),r.ulUnicodeRange4=n.parseULong(),r.achVendID=String.fromCharCode(n.parseByte(),n.parseByte(),n.parseByte(),n.parseByte()),r.fsSelection=n.parseUShort(),r.usFirstCharIndex=n.parseUShort(),r.usLastCharIndex=n.parseUShort(),r.sTypoAscender=n.parseShort(),r.sTypoDescender=n.parseShort(),r.sTypoLineGap=n.parseShort(),r.usWinAscent=n.parseUShort(),r.usWinDescent=n.parseUShort(),r.version>=1&&(r.ulCodePageRange1=n.parseULong(),r.ulCodePageRange2=n.parseULong()),r.version>=2&&(r.sxHeight=n.parseShort(),r.sCapHeight=n.parseShort(),r.usDefaultChar=n.parseUShort(),r.usBreakChar=n.parseUShort(),r.usMaxContent=n.parseUShort()),r}},nh={parse:function(e,t){var r={},n=new tC.Parser(e,t);switch(r.version=n.parseVersion(),r.italicAngle=n.parseFixed(),r.underlinePosition=n.parseShort(),r.underlineThickness=n.parseShort(),r.isFixedPitch=n.parseULong(),r.minMemType42=n.parseULong(),r.maxMemType42=n.parseULong(),r.minMemType1=n.parseULong(),r.maxMemType1=n.parseULong(),r.names=[],r.version){case 1:break;case 2:r.numberOfGlyphs=n.parseUShort(),r.glyphNameIndex=Array(r.numberOfGlyphs);for(var o=0;o<r.numberOfGlyphs;o++)r.glyphNameIndex[o]=n.parseUShort();break;case 2.5:r.numberOfGlyphs=n.parseUShort(),r.offset=Array(r.numberOfGlyphs);for(var i=0;i<r.numberOfGlyphs;i++)r.offset[i]=n.parseChar()}return r}},nd={};nd.UTF8=function(e,t,r){for(var n=[],o=0;o<r;o++,t+=1)n[o]=e.getUint8(t);return String.fromCharCode.apply(null,n)},nd.UTF16=function(e,t,r){for(var n=[],o=r/2,i=0;i<o;i++,t+=2)n[i]=e.getUint16(t);return String.fromCharCode.apply(null,n)};var ng={"x-mac-croatian":"\xc4\xc5\xc7\xc9\xd1\xd6\xdc\xe1\xe0\xe2\xe4\xe3\xe5\xe7\xe9\xe8\xea\xeb\xed\xec\xee\xef\xf1\xf3\xf2\xf4\xf6\xf5\xfa\xf9\xfb\xfc†\xb0\xa2\xa3\xa7•\xb6\xdf\xaeŠ™\xb4\xa8≠Ž\xd8∞\xb1≤≥∆\xb5∂∑∏š∫\xaa\xbaΩž\xf8\xbf\xa1\xac√ƒ≈Ć\xabČ…\xa0\xc0\xc3\xd5ŒœĐ—“”‘’\xf7◊\xa9⁄€‹›\xc6\xbb–\xb7‚„‰\xc2ć\xc1č\xc8\xcd\xce\xcf\xcc\xd3\xd4đ\xd2\xda\xdb\xd9ıˆ˜\xafπ\xcb˚\xb8\xca\xe6ˇ","x-mac-cyrillic":"АБВГДЕЖЗИЙКЛМНОПРСТУФХЦЧШЩЪЫЬЭЮЯ†\xb0Ґ\xa3\xa7•\xb6І\xae\xa9™Ђђ≠Ѓѓ∞\xb1≤≥і\xb5ґЈЄєЇїЉљЊњјЅ\xac√ƒ≈∆\xab\xbb…\xa0ЋћЌќѕ–—“”‘’\xf7„ЎўЏџ№Ёёяабвгдежзийклмнопрстуфхцчшщъыьэю","x-mac-gaelic":"\xc4\xc5\xc7\xc9\xd1\xd6\xdc\xe1\xe0\xe2\xe4\xe3\xe5\xe7\xe9\xe8\xea\xeb\xed\xec\xee\xef\xf1\xf3\xf2\xf4\xf6\xf5\xfa\xf9\xfb\xfc†\xb0\xa2\xa3\xa7•\xb6\xdf\xae\xa9™\xb4\xa8≠\xc6\xd8Ḃ\xb1≤≥ḃĊċḊḋḞḟĠġṀ\xe6\xf8ṁṖṗɼƒſṠ\xab\xbb…\xa0\xc0\xc3\xd5Œœ–—“”‘’ṡẛ\xffŸṪ€‹›Ŷŷṫ\xb7Ỳỳ⁊\xc2\xca\xc1\xcb\xc8\xcd\xce\xcf\xcc\xd3\xd4♣\xd2\xda\xdb\xd9ı\xdd\xfdŴŵẄẅẀẁẂẃ","x-mac-greek":"\xc4\xb9\xb2\xc9\xb3\xd6\xdc΅\xe0\xe2\xe4΄\xa8\xe7\xe9\xe8\xea\xeb\xa3™\xee\xef•\xbd‰\xf4\xf6\xa6€\xf9\xfb\xfc†ΓΔΘΛΞΠ\xdf\xae\xa9ΣΪ\xa7≠\xb0\xb7Α\xb1≤≥\xa5ΒΕΖΗΙΚΜΦΫΨΩάΝ\xacΟΡ≈Τ\xab\xbb…\xa0ΥΧΆΈœ–―“”‘’\xf7ΉΊΌΎέήίόΏύαβψδεφγηιξκλμνοπώρστθωςχυζϊϋΐΰ\xad","x-mac-icelandic":"\xc4\xc5\xc7\xc9\xd1\xd6\xdc\xe1\xe0\xe2\xe4\xe3\xe5\xe7\xe9\xe8\xea\xeb\xed\xec\xee\xef\xf1\xf3\xf2\xf4\xf6\xf5\xfa\xf9\xfb\xfc\xdd\xb0\xa2\xa3\xa7•\xb6\xdf\xae\xa9™\xb4\xa8≠\xc6\xd8∞\xb1≤≥\xa5\xb5∂∑∏π∫\xaa\xbaΩ\xe6\xf8\xbf\xa1\xac√ƒ≈∆\xab\xbb…\xa0\xc0\xc3\xd5Œœ–—“”‘’\xf7◊\xffŸ⁄€\xd0\xf0\xde\xfe\xfd\xb7‚„‰\xc2\xca\xc1\xcb\xc8\xcd\xce\xcf\xcc\xd3\xd4\xd2\xda\xdb\xd9ıˆ˜\xaf˘˙˚\xb8˝˛ˇ","x-mac-inuit":"ᐃᐄᐅᐆᐊᐋᐱᐲᐳᐴᐸᐹᑉᑎᑏᑐᑑᑕᑖᑦᑭᑮᑯᑰᑲᑳᒃᒋᒌᒍᒎᒐᒑ\xb0ᒡᒥᒦ•\xb6ᒧ\xae\xa9™ᒨᒪᒫᒻᓂᓃᓄᓅᓇᓈᓐᓯᓰᓱᓲᓴᓵᔅᓕᓖᓗᓘᓚᓛᓪᔨᔩᔪᔫᔭ…\xa0ᔮᔾᕕᕖᕗ–—“”‘’ᕘᕙᕚᕝᕆᕇᕈᕉᕋᕌᕐᕿᖀᖁᖂᖃᖄᖅᖏᖐᖑᖒᖓᖔᖕᙱᙲᙳᙴᙵᙶᖖᖠᖡᖢᖣᖤᖥᖦᕼŁł","x-mac-ce":"\xc4Āā\xc9Ą\xd6\xdc\xe1ąČ\xe4čĆć\xe9ŹźĎ\xedďĒēĖ\xf3ė\xf4\xf6\xf5\xfaĚě\xfc†\xb0Ę\xa3\xa7•\xb6\xdf\xae\xa9™ę\xa8≠ģĮįĪ≤≥īĶ∂∑łĻļĽľĹĺŅņŃ\xac√ńŇ∆\xab\xbb…\xa0ňŐ\xd5őŌ–—“”‘’\xf7◊ōŔŕŘ‹›řŖŗŠ‚„šŚś\xc1Ťť\xcdŽžŪ\xd3\xd4ūŮ\xdaůŰűŲų\xdd\xfdķŻŁżĢˇ",macintosh:"\xc4\xc5\xc7\xc9\xd1\xd6\xdc\xe1\xe0\xe2\xe4\xe3\xe5\xe7\xe9\xe8\xea\xeb\xed\xec\xee\xef\xf1\xf3\xf2\xf4\xf6\xf5\xfa\xf9\xfb\xfc†\xb0\xa2\xa3\xa7•\xb6\xdf\xae\xa9™\xb4\xa8≠\xc6\xd8∞\xb1≤≥\xa5\xb5∂∑∏π∫\xaa\xbaΩ\xe6\xf8\xbf\xa1\xac√ƒ≈∆\xab\xbb…\xa0\xc0\xc3\xd5Œœ–—“”‘’\xf7◊\xffŸ⁄€‹›ﬁﬂ‡\xb7‚„‰\xc2\xca\xc1\xcb\xc8\xcd\xce\xcf\xcc\xd3\xd4\xd2\xda\xdb\xd9ıˆ˜\xaf˘˙˚\xb8˝˛ˇ","x-mac-romanian":"\xc4\xc5\xc7\xc9\xd1\xd6\xdc\xe1\xe0\xe2\xe4\xe3\xe5\xe7\xe9\xe8\xea\xeb\xed\xec\xee\xef\xf1\xf3\xf2\xf4\xf6\xf5\xfa\xf9\xfb\xfc†\xb0\xa2\xa3\xa7•\xb6\xdf\xae\xa9™\xb4\xa8≠ĂȘ∞\xb1≤≥\xa5\xb5∂∑∏π∫\xaa\xbaΩăș\xbf\xa1\xac√ƒ≈∆\xab\xbb…\xa0\xc0\xc3\xd5Œœ–—“”‘’\xf7◊\xffŸ⁄€‹›Țț‡\xb7‚„‰\xc2\xca\xc1\xcb\xc8\xcd\xce\xcf\xcc\xd3\xd4\xd2\xda\xdb\xd9ıˆ˜\xaf˘˙˚\xb8˝˛ˇ","x-mac-turkish":"\xc4\xc5\xc7\xc9\xd1\xd6\xdc\xe1\xe0\xe2\xe4\xe3\xe5\xe7\xe9\xe8\xea\xeb\xed\xec\xee\xef\xf1\xf3\xf2\xf4\xf6\xf5\xfa\xf9\xfb\xfc†\xb0\xa2\xa3\xa7•\xb6\xdf\xae\xa9™\xb4\xa8≠\xc6\xd8∞\xb1≤≥\xa5\xb5∂∑∏π∫\xaa\xbaΩ\xe6\xf8\xbf\xa1\xac√ƒ≈∆\xab\xbb…\xa0\xc0\xc3\xd5Œœ–—“”‘’\xf7◊\xffŸĞğİıŞş‡\xb7‚„‰\xc2\xca\xc1\xcb\xc8\xcd\xce\xcf\xcc\xd3\xd4\xd2\xda\xdb\xd9ˆ˜\xaf˘˙˚\xb8˝˛ˇ"};nd.MACSTRING=function(e,t,r,n){var o=ng[n];if(void 0!==o){for(var i="",a=0;a<r;a++){var s=e.getUint8(t+a);s<=127?i+=String.fromCharCode(s):i+=o[127&s]}return i}};var nv={parse:function(e,t){var r=new tC.Parser(e,t),n=r.parseULong();ts.argument(1===n,"Unsupported META table version."),r.parseULong(),r.parseULong();for(var o=r.parseULong(),i={},a=0;a<o;a++){var s=r.parseTag(),u=r.parseULong(),l=r.parseULong(),c=nd.UTF8(e,t+u,l);i[s]=c}return i}};function nm(e,t){for(var r=[],n=12,o=0;o<t;o+=1){var i=tC.getTag(e,n),a=tC.getULong(e,n+4),s=tC.getULong(e,n+8),u=tC.getULong(e,n+12);r.push({tag:i,checksum:a,offset:s,length:u,compression:!1}),n+=16}return r}function ny(e,t){if("WOFF"!==t.compression)return{data:e,offset:t.offset};var r=new Uint8Array(e.buffer,t.offset+2,t.compressedLength-2),n=new Uint8Array(t.length);if(e6(r,n),n.byteLength!==t.length)throw Error("Decompression error: "+t.tag+" decompressed length doesn't match recorded length");return{data:new DataView(n.buffer,0),offset:0}}var nD=Object.freeze({__proto__:null,Font:rq,Glyph:tu,Path:e9,_parse:tC,parse:function(e,t){t=null==t?{}:t;var r,n,o,i,a,s,u,l,c,f,p,h,d,g=new rq({empty:!0}),v=new DataView(e,0),m=[],y=tC.getTag(v,0);if(y===String.fromCharCode(0,1,0,0)||"true"===y||"typ1"===y)g.outlinesFormat="truetype",n=tC.getUShort(v,4),m=nm(v,n);else if("OTTO"===y)g.outlinesFormat="cff",n=tC.getUShort(v,4),m=nm(v,n);else if("wOFF"===y){var D=tC.getTag(v,4);if(D===String.fromCharCode(0,1,0,0))g.outlinesFormat="truetype";else if("OTTO"===D)g.outlinesFormat="cff";else throw Error("Unsupported OpenType flavor "+y);n=tC.getUShort(v,12),m=function(e,t){for(var r=[],n=44,o=0;o<t;o+=1){var i=tC.getTag(e,n),a=tC.getULong(e,n+4),s=tC.getULong(e,n+8),u=tC.getULong(e,n+12),l=void 0;l=s<u&&"WOFF",r.push({tag:i,offset:a,compression:l,compressedLength:s,length:u}),n+=20}return r}(v,n)}else throw Error("Unsupported OpenType signature "+y);for(var b=0;b<n;b+=1){var x=m[b],w=void 0;switch(x.tag){case"cmap":w=ny(v,x),g.tables.cmap=rV.parse(w.data,w.offset),g.encoding=new to(g.tables.cmap);break;case"cvt ":w=ny(v,x),d=new tC.Parser(w.data,w.offset),g.tables.cvt=d.parseShortList(x.length/2);break;case"fvar":i=x;break;case"fpgm":w=ny(v,x),d=new tC.Parser(w.data,w.offset),g.tables.fpgm=d.parseByteList(x.length);break;case"head":w=ny(v,x),g.tables.head=ni.parse(w.data,w.offset),g.unitsPerEm=g.tables.head.unitsPerEm,r=g.tables.head.indexToLocFormat;break;case"hhea":w=ny(v,x),g.tables.hhea=na.parse(w.data,w.offset),g.ascender=g.tables.hhea.ascender,g.descender=g.tables.hhea.descender,g.numberOfHMetrics=g.tables.hhea.numberOfHMetrics;break;case"hmtx":c=x;break;case"ltag":w=ny(v,x),ltagTable=nl.parse(w.data,w.offset);break;case"maxp":w=ny(v,x),g.tables.maxp=nf.parse(w.data,w.offset),g.numGlyphs=g.tables.maxp.numGlyphs;break;case"OS/2":w=ny(v,x),g.tables.os2=np.parse(w.data,w.offset);break;case"post":w=ny(v,x),g.tables.post=nh.parse(w.data,w.offset);break;case"prep":w=ny(v,x),d=new tC.Parser(w.data,w.offset),g.tables.prep=d.parseByteList(x.length);break;case"glyf":a=x;break;case"loca":p=x;break;case"CFF ":o=x;break;case"kern":f=x;break;case"GDEF":s=x;break;case"GPOS":u=x;break;case"GSUB":l=x;break;case"meta":h=x}}if(a&&p){var E=0===r,S=ny(v,p),k=nc.parse(S.data,S.offset,g.numGlyphs,E),F=ny(v,a);g.glyphs=tL.parse(F.data,F.offset,k,g,t)}else if(o){var C=ny(v,o);r2.parse(C.data,C.offset,g,t)}else throw Error("Font doesn't contain TrueType or CFF outlines.");var _=ny(v,c);if(ns.parse(g,_.data,_.offset,g.numberOfHMetrics,g.numGlyphs,g.glyphs,t),t.lowMemory?function(e){e._IndexToUnicodeMap={};for(var t=e.tables.cmap.glyphIndexMap,r=Object.keys(t),n=0;n<r.length;n+=1){var o=r[n],i=t[o];void 0===e._IndexToUnicodeMap[i]?e._IndexToUnicodeMap[i]={unicodes:[parseInt(o)]}:e._IndexToUnicodeMap[i].unicodes.push(parseInt(o))}}(g):function(e){for(var t=e.tables.cmap.glyphIndexMap,r=Object.keys(t),n=0;n<r.length;n+=1){var o=r[n],i=t[o];e.glyphs.get(i).addUnicode(parseInt(o))}}(g),f){var T=ny(v,f);g.kerningPairs=nu.parse(T.data,T.offset)}else g.kerningPairs={};if(s){var O=ny(v,s);g.tables.gdef=r9.parse(O.data,O.offset)}if(u){var A=ny(v,u);g.tables.gpos=nt.parse(A.data,A.offset),g.position.init()}if(l){var P=ny(v,l);g.tables.gsub=no.parse(P.data,P.offset)}if(i){var L=ny(v,i);g.tables.fvar=r3.parse(L.data,L.offset,g.names)}if(h){var I=ny(v,h);g.tables.meta=nv.parse(I.data,I.offset),g.metas=g.tables.meta}return g},load:function(){},loadSync:function(){}}),nb=Object.create,nx=Object.defineProperty,nw=Object.getOwnPropertyDescriptor,nE=Object.getOwnPropertyNames,nS=Object.getPrototypeOf,nk=Object.prototype.hasOwnProperty,nF=(e,t)=>()=>(e&&(t=e(e=0)),t),nC=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),n_=(e,t)=>{for(var r in t)nx(e,r,{get:t[r],enumerable:!0})},nT=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of nE(t))nk.call(e,o)||o===r||nx(e,o,{get:()=>t[o],enumerable:!(n=nw(t,o))||n.enumerable});return e},nO=e=>nT(nx({},"__esModule",{value:!0}),e),nA={};async function nP(){return{}}n_(nA,{getYogaModule:()=>nP});var nL=nF(()=>{}),nI=nC(e=>{function t(e){return"0"==(e=`${e}`)?"0":/^[+-]?(\d+|\d*\.\d+)(e[+-]?\d+)?(%|\w+)?$/.test(e)?e.replace(/^[+-]?/,e=>"-"===e?"":"-"):e.includes("var(")||e.includes("calc(")?`calc(${e} * -1)`:void 0}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:()=>t})}),nB=nC(e=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:()=>t});var t=["preflight","container","accessibility","pointerEvents","visibility","position","inset","isolation","zIndex","order","gridColumn","gridColumnStart","gridColumnEnd","gridRow","gridRowStart","gridRowEnd","float","clear","margin","boxSizing","display","aspectRatio","height","maxHeight","minHeight","width","minWidth","maxWidth","flex","flexShrink","flexGrow","flexBasis","tableLayout","borderCollapse","borderSpacing","transformOrigin","translate","rotate","skew","scale","transform","animation","cursor","touchAction","userSelect","resize","scrollSnapType","scrollSnapAlign","scrollSnapStop","scrollMargin","scrollPadding","listStylePosition","listStyleType","appearance","columns","breakBefore","breakInside","breakAfter","gridAutoColumns","gridAutoFlow","gridAutoRows","gridTemplateColumns","gridTemplateRows","flexDirection","flexWrap","placeContent","placeItems","alignContent","alignItems","justifyContent","justifyItems","gap","space","divideWidth","divideStyle","divideColor","divideOpacity","placeSelf","alignSelf","justifySelf","overflow","overscrollBehavior","scrollBehavior","textOverflow","whitespace","wordBreak","borderRadius","borderWidth","borderStyle","borderColor","borderOpacity","backgroundColor","backgroundOpacity","backgroundImage","gradientColorStops","boxDecorationBreak","backgroundSize","backgroundAttachment","backgroundClip","backgroundPosition","backgroundRepeat","backgroundOrigin","fill","stroke","strokeWidth","objectFit","objectPosition","padding","textAlign","textIndent","verticalAlign","fontFamily","fontSize","fontWeight","textTransform","fontStyle","fontVariantNumeric","lineHeight","letterSpacing","textColor","textOpacity","textDecoration","textDecorationColor","textDecorationStyle","textDecorationThickness","textUnderlineOffset","fontSmoothing","placeholderColor","placeholderOpacity","caretColor","accentColor","opacity","backgroundBlendMode","mixBlendMode","boxShadow","boxShadowColor","outlineStyle","outlineWidth","outlineOffset","outlineColor","ringWidth","ringColor","ringOpacity","ringOffsetWidth","ringOffsetColor","blur","brightness","contrast","dropShadow","grayscale","hueRotate","invert","saturate","sepia","filter","backdropBlur","backdropBrightness","backdropContrast","backdropGrayscale","backdropHueRotate","backdropInvert","backdropOpacity","backdropSaturate","backdropSepia","backdropFilter","transitionProperty","transitionDelay","transitionDuration","transitionTimingFunction","willChange","content"]}),nR=nC(e=>{function t(e,t){return void 0===e?t:Array.isArray(e)?e:[...new Set(t.filter(t=>!1!==e&&!1!==e[t]).concat(Object.keys(e).filter(t=>!1!==e[t])))]}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:()=>t})}),nU=nC((e,t)=>{t.exports={content:[],presets:[],darkMode:"media",theme:{screens:{sm:"640px",md:"768px",lg:"1024px",xl:"1280px","2xl":"1536px"},colors:({colors:e})=>({inherit:e.inherit,current:e.current,transparent:e.transparent,black:e.black,white:e.white,slate:e.slate,gray:e.gray,zinc:e.zinc,neutral:e.neutral,stone:e.stone,red:e.red,orange:e.orange,amber:e.amber,yellow:e.yellow,lime:e.lime,green:e.green,emerald:e.emerald,teal:e.teal,cyan:e.cyan,sky:e.sky,blue:e.blue,indigo:e.indigo,violet:e.violet,purple:e.purple,fuchsia:e.fuchsia,pink:e.pink,rose:e.rose}),columns:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12","3xs":"16rem","2xs":"18rem",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem"},spacing:{px:"1px",0:"0px",.5:"0.125rem",1:"0.25rem",1.5:"0.375rem",2:"0.5rem",2.5:"0.625rem",3:"0.75rem",3.5:"0.875rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem",11:"2.75rem",12:"3rem",14:"3.5rem",16:"4rem",20:"5rem",24:"6rem",28:"7rem",32:"8rem",36:"9rem",40:"10rem",44:"11rem",48:"12rem",52:"13rem",56:"14rem",60:"15rem",64:"16rem",72:"18rem",80:"20rem",96:"24rem"},animation:{none:"none",spin:"spin 1s linear infinite",ping:"ping 1s cubic-bezier(0, 0, 0.2, 1) infinite",pulse:"pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite",bounce:"bounce 1s infinite"},aspectRatio:{auto:"auto",square:"1 / 1",video:"16 / 9"},backdropBlur:({theme:e})=>e("blur"),backdropBrightness:({theme:e})=>e("brightness"),backdropContrast:({theme:e})=>e("contrast"),backdropGrayscale:({theme:e})=>e("grayscale"),backdropHueRotate:({theme:e})=>e("hueRotate"),backdropInvert:({theme:e})=>e("invert"),backdropOpacity:({theme:e})=>e("opacity"),backdropSaturate:({theme:e})=>e("saturate"),backdropSepia:({theme:e})=>e("sepia"),backgroundColor:({theme:e})=>e("colors"),backgroundImage:{none:"none","gradient-to-t":"linear-gradient(to top, var(--tw-gradient-stops))","gradient-to-tr":"linear-gradient(to top right, var(--tw-gradient-stops))","gradient-to-r":"linear-gradient(to right, var(--tw-gradient-stops))","gradient-to-br":"linear-gradient(to bottom right, var(--tw-gradient-stops))","gradient-to-b":"linear-gradient(to bottom, var(--tw-gradient-stops))","gradient-to-bl":"linear-gradient(to bottom left, var(--tw-gradient-stops))","gradient-to-l":"linear-gradient(to left, var(--tw-gradient-stops))","gradient-to-tl":"linear-gradient(to top left, var(--tw-gradient-stops))"},backgroundOpacity:({theme:e})=>e("opacity"),backgroundPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},backgroundSize:{auto:"auto",cover:"cover",contain:"contain"},blur:{0:"0",none:"0",sm:"4px",DEFAULT:"8px",md:"12px",lg:"16px",xl:"24px","2xl":"40px","3xl":"64px"},brightness:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5",200:"2"},borderColor:({theme:e})=>({...e("colors"),DEFAULT:e("colors.gray.200","currentColor")}),borderOpacity:({theme:e})=>e("opacity"),borderRadius:{none:"0px",sm:"0.125rem",DEFAULT:"0.25rem",md:"0.375rem",lg:"0.5rem",xl:"0.75rem","2xl":"1rem","3xl":"1.5rem",full:"9999px"},borderSpacing:({theme:e})=>({...e("spacing")}),borderWidth:{DEFAULT:"1px",0:"0px",2:"2px",4:"4px",8:"8px"},boxShadow:{sm:"0 1px 2px 0 rgb(0 0 0 / 0.05)",DEFAULT:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)",md:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)",lg:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)",xl:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)","2xl":"0 25px 50px -12px rgb(0 0 0 / 0.25)",inner:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)",none:"none"},boxShadowColor:({theme:e})=>e("colors"),caretColor:({theme:e})=>e("colors"),accentColor:({theme:e})=>({...e("colors"),auto:"auto"}),contrast:{0:"0",50:".5",75:".75",100:"1",125:"1.25",150:"1.5",200:"2"},container:{},content:{none:"none"},cursor:{auto:"auto",default:"default",pointer:"pointer",wait:"wait",text:"text",move:"move",help:"help","not-allowed":"not-allowed",none:"none","context-menu":"context-menu",progress:"progress",cell:"cell",crosshair:"crosshair","vertical-text":"vertical-text",alias:"alias",copy:"copy","no-drop":"no-drop",grab:"grab",grabbing:"grabbing","all-scroll":"all-scroll","col-resize":"col-resize","row-resize":"row-resize","n-resize":"n-resize","e-resize":"e-resize","s-resize":"s-resize","w-resize":"w-resize","ne-resize":"ne-resize","nw-resize":"nw-resize","se-resize":"se-resize","sw-resize":"sw-resize","ew-resize":"ew-resize","ns-resize":"ns-resize","nesw-resize":"nesw-resize","nwse-resize":"nwse-resize","zoom-in":"zoom-in","zoom-out":"zoom-out"},divideColor:({theme:e})=>e("borderColor"),divideOpacity:({theme:e})=>e("borderOpacity"),divideWidth:({theme:e})=>e("borderWidth"),dropShadow:{sm:"0 1px 1px rgb(0 0 0 / 0.05)",DEFAULT:["0 1px 2px rgb(0 0 0 / 0.1)","0 1px 1px rgb(0 0 0 / 0.06)"],md:["0 4px 3px rgb(0 0 0 / 0.07)","0 2px 2px rgb(0 0 0 / 0.06)"],lg:["0 10px 8px rgb(0 0 0 / 0.04)","0 4px 3px rgb(0 0 0 / 0.1)"],xl:["0 20px 13px rgb(0 0 0 / 0.03)","0 8px 5px rgb(0 0 0 / 0.08)"],"2xl":"0 25px 25px rgb(0 0 0 / 0.15)",none:"0 0 #0000"},fill:({theme:e})=>e("colors"),grayscale:{0:"0",DEFAULT:"100%"},hueRotate:{0:"0deg",15:"15deg",30:"30deg",60:"60deg",90:"90deg",180:"180deg"},invert:{0:"0",DEFAULT:"100%"},flex:{1:"1 1 0%",auto:"1 1 auto",initial:"0 1 auto",none:"none"},flexBasis:({theme:e})=>({auto:"auto",...e("spacing"),"1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%"}),flexGrow:{0:"0",DEFAULT:"1"},flexShrink:{0:"0",DEFAULT:"1"},fontFamily:{sans:["ui-sans-serif","system-ui","-apple-system","BlinkMacSystemFont",'"Segoe UI"',"Roboto",'"Helvetica Neue"',"Arial",'"Noto Sans"',"sans-serif",'"Apple Color Emoji"','"Segoe UI Emoji"','"Segoe UI Symbol"','"Noto Color Emoji"'],serif:["ui-serif","Georgia","Cambria",'"Times New Roman"',"Times","serif"],mono:["ui-monospace","SFMono-Regular","Menlo","Monaco","Consolas",'"Liberation Mono"','"Courier New"',"monospace"]},fontSize:{xs:["0.75rem",{lineHeight:"1rem"}],sm:["0.875rem",{lineHeight:"1.25rem"}],base:["1rem",{lineHeight:"1.5rem"}],lg:["1.125rem",{lineHeight:"1.75rem"}],xl:["1.25rem",{lineHeight:"1.75rem"}],"2xl":["1.5rem",{lineHeight:"2rem"}],"3xl":["1.875rem",{lineHeight:"2.25rem"}],"4xl":["2.25rem",{lineHeight:"2.5rem"}],"5xl":["3rem",{lineHeight:"1"}],"6xl":["3.75rem",{lineHeight:"1"}],"7xl":["4.5rem",{lineHeight:"1"}],"8xl":["6rem",{lineHeight:"1"}],"9xl":["8rem",{lineHeight:"1"}]},fontWeight:{thin:"100",extralight:"200",light:"300",normal:"400",medium:"500",semibold:"600",bold:"700",extrabold:"800",black:"900"},gap:({theme:e})=>e("spacing"),gradientColorStops:({theme:e})=>e("colors"),gridAutoColumns:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridAutoRows:{auto:"auto",min:"min-content",max:"max-content",fr:"minmax(0, 1fr)"},gridColumn:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-7":"span 7 / span 7","span-8":"span 8 / span 8","span-9":"span 9 / span 9","span-10":"span 10 / span 10","span-11":"span 11 / span 11","span-12":"span 12 / span 12","span-full":"1 / -1"},gridColumnEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13"},gridColumnStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12",13:"13"},gridRow:{auto:"auto","span-1":"span 1 / span 1","span-2":"span 2 / span 2","span-3":"span 3 / span 3","span-4":"span 4 / span 4","span-5":"span 5 / span 5","span-6":"span 6 / span 6","span-full":"1 / -1"},gridRowStart:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7"},gridRowEnd:{auto:"auto",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7"},gridTemplateColumns:{none:"none",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))",7:"repeat(7, minmax(0, 1fr))",8:"repeat(8, minmax(0, 1fr))",9:"repeat(9, minmax(0, 1fr))",10:"repeat(10, minmax(0, 1fr))",11:"repeat(11, minmax(0, 1fr))",12:"repeat(12, minmax(0, 1fr))"},gridTemplateRows:{none:"none",1:"repeat(1, minmax(0, 1fr))",2:"repeat(2, minmax(0, 1fr))",3:"repeat(3, minmax(0, 1fr))",4:"repeat(4, minmax(0, 1fr))",5:"repeat(5, minmax(0, 1fr))",6:"repeat(6, minmax(0, 1fr))"},height:({theme:e})=>({auto:"auto",...e("spacing"),"1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%",full:"100%",screen:"100vh",min:"min-content",max:"max-content",fit:"fit-content"}),inset:({theme:e})=>({auto:"auto",...e("spacing"),"1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%"}),keyframes:{spin:{to:{transform:"rotate(360deg)"}},ping:{"75%, 100%":{transform:"scale(2)",opacity:"0"}},pulse:{"50%":{opacity:".5"}},bounce:{"0%, 100%":{transform:"translateY(-25%)",animationTimingFunction:"cubic-bezier(0.8,0,1,1)"},"50%":{transform:"none",animationTimingFunction:"cubic-bezier(0,0,0.2,1)"}}},letterSpacing:{tighter:"-0.05em",tight:"-0.025em",normal:"0em",wide:"0.025em",wider:"0.05em",widest:"0.1em"},lineHeight:{none:"1",tight:"1.25",snug:"1.375",normal:"1.5",relaxed:"1.625",loose:"2",3:".75rem",4:"1rem",5:"1.25rem",6:"1.5rem",7:"1.75rem",8:"2rem",9:"2.25rem",10:"2.5rem"},listStyleType:{none:"none",disc:"disc",decimal:"decimal"},margin:({theme:e})=>({auto:"auto",...e("spacing")}),maxHeight:({theme:e})=>({...e("spacing"),full:"100%",screen:"100vh",min:"min-content",max:"max-content",fit:"fit-content"}),maxWidth:({theme:e,breakpoints:t})=>({none:"none",0:"0rem",xs:"20rem",sm:"24rem",md:"28rem",lg:"32rem",xl:"36rem","2xl":"42rem","3xl":"48rem","4xl":"56rem","5xl":"64rem","6xl":"72rem","7xl":"80rem",full:"100%",min:"min-content",max:"max-content",fit:"fit-content",prose:"65ch",...t(e("screens"))}),minHeight:{0:"0px",full:"100%",screen:"100vh",min:"min-content",max:"max-content",fit:"fit-content"},minWidth:{0:"0px",full:"100%",min:"min-content",max:"max-content",fit:"fit-content"},objectPosition:{bottom:"bottom",center:"center",left:"left","left-bottom":"left bottom","left-top":"left top",right:"right","right-bottom":"right bottom","right-top":"right top",top:"top"},opacity:{0:"0",5:"0.05",10:"0.1",20:"0.2",25:"0.25",30:"0.3",40:"0.4",50:"0.5",60:"0.6",70:"0.7",75:"0.75",80:"0.8",90:"0.9",95:"0.95",100:"1"},order:{first:"-9999",last:"9999",none:"0",1:"1",2:"2",3:"3",4:"4",5:"5",6:"6",7:"7",8:"8",9:"9",10:"10",11:"11",12:"12"},padding:({theme:e})=>e("spacing"),placeholderColor:({theme:e})=>e("colors"),placeholderOpacity:({theme:e})=>e("opacity"),outlineColor:({theme:e})=>e("colors"),outlineOffset:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px"},outlineWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px"},ringColor:({theme:e})=>({DEFAULT:e("colors.blue.500","#3b82f6"),...e("colors")}),ringOffsetColor:({theme:e})=>e("colors"),ringOffsetWidth:{0:"0px",1:"1px",2:"2px",4:"4px",8:"8px"},ringOpacity:({theme:e})=>({DEFAULT:"0.5",...e("opacity")}),ringWidth:{DEFAULT:"3px",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px"},rotate:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg",45:"45deg",90:"90deg",180:"180deg"},saturate:{0:"0",50:".5",100:"1",150:"1.5",200:"2"},scale:{0:"0",50:".5",75:".75",90:".9",95:".95",100:"1",105:"1.05",110:"1.1",125:"1.25",150:"1.5"},scrollMargin:({theme:e})=>({...e("spacing")}),scrollPadding:({theme:e})=>e("spacing"),sepia:{0:"0",DEFAULT:"100%"},skew:{0:"0deg",1:"1deg",2:"2deg",3:"3deg",6:"6deg",12:"12deg"},space:({theme:e})=>({...e("spacing")}),stroke:({theme:e})=>e("colors"),strokeWidth:{0:"0",1:"1",2:"2"},textColor:({theme:e})=>e("colors"),textDecorationColor:({theme:e})=>e("colors"),textDecorationThickness:{auto:"auto","from-font":"from-font",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px"},textUnderlineOffset:{auto:"auto",0:"0px",1:"1px",2:"2px",4:"4px",8:"8px"},textIndent:({theme:e})=>({...e("spacing")}),textOpacity:({theme:e})=>e("opacity"),transformOrigin:{center:"center",top:"top","top-right":"top right",right:"right","bottom-right":"bottom right",bottom:"bottom","bottom-left":"bottom left",left:"left","top-left":"top left"},transitionDelay:{75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms"},transitionDuration:{DEFAULT:"150ms",75:"75ms",100:"100ms",150:"150ms",200:"200ms",300:"300ms",500:"500ms",700:"700ms",1e3:"1000ms"},transitionProperty:{none:"none",all:"all",DEFAULT:"color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter",colors:"color, background-color, border-color, text-decoration-color, fill, stroke",opacity:"opacity",shadow:"box-shadow",transform:"transform"},transitionTimingFunction:{DEFAULT:"cubic-bezier(0.4, 0, 0.2, 1)",linear:"linear",in:"cubic-bezier(0.4, 0, 1, 1)",out:"cubic-bezier(0, 0, 0.2, 1)","in-out":"cubic-bezier(0.4, 0, 0.2, 1)"},translate:({theme:e})=>({...e("spacing"),"1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%",full:"100%"}),width:({theme:e})=>({auto:"auto",...e("spacing"),"1/2":"50%","1/3":"33.333333%","2/3":"66.666667%","1/4":"25%","2/4":"50%","3/4":"75%","1/5":"20%","2/5":"40%","3/5":"60%","4/5":"80%","1/6":"16.666667%","2/6":"33.333333%","3/6":"50%","4/6":"66.666667%","5/6":"83.333333%","1/12":"8.333333%","2/12":"16.666667%","3/12":"25%","4/12":"33.333333%","5/12":"41.666667%","6/12":"50%","7/12":"58.333333%","8/12":"66.666667%","9/12":"75%","10/12":"83.333333%","11/12":"91.666667%",full:"100%",screen:"100vw",min:"min-content",max:"max-content",fit:"fit-content"}),willChange:{auto:"auto",scroll:"scroll-position",contents:"contents",transform:"transform"},zIndex:{auto:"auto",0:"0",10:"10",20:"20",30:"30",40:"40",50:"50"}},variantOrder:["first","last","odd","even","visited","checked","empty","read-only","group-hover","group-focus","focus-within","hover","focus","focus-visible","active","disabled"],plugins:[]}}),nM={};n_(nM,{default:()=>l});var nN=nF(()=>{l={info(e,t){console.info(...Array.isArray(e)?[e]:[t,e])},warn(e,t){console.warn(...Array.isArray(e)?[e]:[t,e])},risk(e,t){console.error(...Array.isArray(e)?[e]:[t,e])}}}),nj=nC(e=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:()=>n});var t=function(e){return e&&e.__esModule?e:{default:e}}((nN(),nO(nM)));function r({version:e,from:r,to:n}){t.default.warn(`${r}-color-renamed`,[`As of Tailwind CSS ${e}, \`${r}\` has been renamed to \`${n}\`.`,"Update your configuration file to silence this warning."])}var n={inherit:"inherit",current:"currentColor",transparent:"transparent",black:"#000",white:"#fff",slate:{50:"#f8fafc",100:"#f1f5f9",200:"#e2e8f0",300:"#cbd5e1",400:"#94a3b8",500:"#64748b",600:"#475569",700:"#334155",800:"#1e293b",900:"#0f172a"},gray:{50:"#f9fafb",100:"#f3f4f6",200:"#e5e7eb",300:"#d1d5db",400:"#9ca3af",500:"#6b7280",600:"#4b5563",700:"#374151",800:"#1f2937",900:"#111827"},zinc:{50:"#fafafa",100:"#f4f4f5",200:"#e4e4e7",300:"#d4d4d8",400:"#a1a1aa",500:"#71717a",600:"#52525b",700:"#3f3f46",800:"#27272a",900:"#18181b"},neutral:{50:"#fafafa",100:"#f5f5f5",200:"#e5e5e5",300:"#d4d4d4",400:"#a3a3a3",500:"#737373",600:"#525252",700:"#404040",800:"#262626",900:"#171717"},stone:{50:"#fafaf9",100:"#f5f5f4",200:"#e7e5e4",300:"#d6d3d1",400:"#a8a29e",500:"#78716c",600:"#57534e",700:"#44403c",800:"#292524",900:"#1c1917"},red:{50:"#fef2f2",100:"#fee2e2",200:"#fecaca",300:"#fca5a5",400:"#f87171",500:"#ef4444",600:"#dc2626",700:"#b91c1c",800:"#991b1b",900:"#7f1d1d"},orange:{50:"#fff7ed",100:"#ffedd5",200:"#fed7aa",300:"#fdba74",400:"#fb923c",500:"#f97316",600:"#ea580c",700:"#c2410c",800:"#9a3412",900:"#7c2d12"},amber:{50:"#fffbeb",100:"#fef3c7",200:"#fde68a",300:"#fcd34d",400:"#fbbf24",500:"#f59e0b",600:"#d97706",700:"#b45309",800:"#92400e",900:"#78350f"},yellow:{50:"#fefce8",100:"#fef9c3",200:"#fef08a",300:"#fde047",400:"#facc15",500:"#eab308",600:"#ca8a04",700:"#a16207",800:"#854d0e",900:"#713f12"},lime:{50:"#f7fee7",100:"#ecfccb",200:"#d9f99d",300:"#bef264",400:"#a3e635",500:"#84cc16",600:"#65a30d",700:"#4d7c0f",800:"#3f6212",900:"#365314"},green:{50:"#f0fdf4",100:"#dcfce7",200:"#bbf7d0",300:"#86efac",400:"#4ade80",500:"#22c55e",600:"#16a34a",700:"#15803d",800:"#166534",900:"#14532d"},emerald:{50:"#ecfdf5",100:"#d1fae5",200:"#a7f3d0",300:"#6ee7b7",400:"#34d399",500:"#10b981",600:"#059669",700:"#047857",800:"#065f46",900:"#064e3b"},teal:{50:"#f0fdfa",100:"#ccfbf1",200:"#99f6e4",300:"#5eead4",400:"#2dd4bf",500:"#14b8a6",600:"#0d9488",700:"#0f766e",800:"#115e59",900:"#134e4a"},cyan:{50:"#ecfeff",100:"#cffafe",200:"#a5f3fc",300:"#67e8f9",400:"#22d3ee",500:"#06b6d4",600:"#0891b2",700:"#0e7490",800:"#155e75",900:"#164e63"},sky:{50:"#f0f9ff",100:"#e0f2fe",200:"#bae6fd",300:"#7dd3fc",400:"#38bdf8",500:"#0ea5e9",600:"#0284c7",700:"#0369a1",800:"#075985",900:"#0c4a6e"},blue:{50:"#eff6ff",100:"#dbeafe",200:"#bfdbfe",300:"#93c5fd",400:"#60a5fa",500:"#3b82f6",600:"#2563eb",700:"#1d4ed8",800:"#1e40af",900:"#1e3a8a"},indigo:{50:"#eef2ff",100:"#e0e7ff",200:"#c7d2fe",300:"#a5b4fc",400:"#818cf8",500:"#6366f1",600:"#4f46e5",700:"#4338ca",800:"#3730a3",900:"#312e81"},violet:{50:"#f5f3ff",100:"#ede9fe",200:"#ddd6fe",300:"#c4b5fd",400:"#a78bfa",500:"#8b5cf6",600:"#7c3aed",700:"#6d28d9",800:"#5b21b6",900:"#4c1d95"},purple:{50:"#faf5ff",100:"#f3e8ff",200:"#e9d5ff",300:"#d8b4fe",400:"#c084fc",500:"#a855f7",600:"#9333ea",700:"#7e22ce",800:"#6b21a8",900:"#581c87"},fuchsia:{50:"#fdf4ff",100:"#fae8ff",200:"#f5d0fe",300:"#f0abfc",400:"#e879f9",500:"#d946ef",600:"#c026d3",700:"#a21caf",800:"#86198f",900:"#701a75"},pink:{50:"#fdf2f8",100:"#fce7f3",200:"#fbcfe8",300:"#f9a8d4",400:"#f472b6",500:"#ec4899",600:"#db2777",700:"#be185d",800:"#9d174d",900:"#831843"},rose:{50:"#fff1f2",100:"#ffe4e6",200:"#fecdd3",300:"#fda4af",400:"#fb7185",500:"#f43f5e",600:"#e11d48",700:"#be123c",800:"#9f1239",900:"#881337"},get lightBlue(){return r({version:"v2.2",from:"lightBlue",to:"sky"}),this.sky},get warmGray(){return r({version:"v3.0",from:"warmGray",to:"stone"}),this.stone},get trueGray(){return r({version:"v3.0",from:"trueGray",to:"neutral"}),this.neutral},get coolGray(){return r({version:"v3.0",from:"coolGray",to:"gray"}),this.gray},get blueGray(){return r({version:"v3.0",from:"blueGray",to:"slate"}),this.slate}}}),nW=nC(e=>{function t(e,...r){for(let t of r){var n,o;for(let r in t)null!=e&&null!=(n=e.hasOwnProperty)&&n.call(e,r)||(e[r]=t[r]);for(let r of Object.getOwnPropertySymbols(t))null!=e&&null!=(o=e.hasOwnProperty)&&o.call(e,r)||(e[r]=t[r])}return e}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"defaults",{enumerable:!0,get:()=>t})}),nG=nC(e=>{function t(e){if(Array.isArray(e))return e;if(e.split("[").length-1!=e.split("]").length-1)throw Error(`Path is invalid. Has unbalanced brackets: ${e}`);return e.split(/\.(?![^\[]*\])|[\[\]]/g).filter(Boolean)}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"toPath",{enumerable:!0,get:()=>t})}),n$=nC(e=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"normalizeConfig",{enumerable:!0,get:()=>n});var t=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=r(t);if(n&&n.has(e))return n.get(e);var o={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var s=i?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(o,a,s):o[a]=e[a]}return o.default=e,n&&n.set(e,o),o}((nN(),nO(nM)));function r(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(r=function(e){return e?n:t})(e)}function n(e){var r,n,o,i,a,s,u;let l,c;for(let f of(((()=>{if(e.purge||!e.content||!Array.isArray(e.content)&&("object"!=typeof e.content||null===e.content))return!1;if(Array.isArray(e.content))return e.content.every(e=>"string"==typeof e||!("string"!=typeof(null==e?void 0:e.raw)||null!=e&&e.extension&&"string"!=typeof(null==e?void 0:e.extension)));if("object"==typeof e.content&&null!==e.content){if(Object.keys(e.content).some(e=>!["files","extract","transform"].includes(e)))return!1;if(Array.isArray(e.content.files)){if(!e.content.files.every(e=>"string"==typeof e||!("string"!=typeof(null==e?void 0:e.raw)||null!=e&&e.extension&&"string"!=typeof(null==e?void 0:e.extension))))return!1;if("object"==typeof e.content.extract){for(let t of Object.values(e.content.extract))if("function"!=typeof t)return!1}else if(void 0!==e.content.extract&&"function"!=typeof e.content.extract)return!1;if("object"==typeof e.content.transform){for(let t of Object.values(e.content.transform))if("function"!=typeof t)return!1}else if(void 0!==e.content.transform&&"function"!=typeof e.content.transform)return!1}return!0}return!1})()||t.default.warn("purge-deprecation",["The `purge`/`content` options have changed in Tailwind CSS v3.0.","Update your configuration file to eliminate this warning.","https://tailwindcss.com/docs/upgrade-guide#configure-content-sources"]),e.safelist=(()=>{var t;let{content:r,purge:n,safelist:o}=e;return Array.isArray(o)?o:Array.isArray(null==r?void 0:r.safelist)?r.safelist:Array.isArray(null==n?void 0:n.safelist)?n.safelist:Array.isArray(null==n||null==(t=n.options)?void 0:t.safelist)?n.options.safelist:[]})(),"function"==typeof e.prefix)?(t.default.warn("prefix-function",["As of Tailwind CSS v3.0, `prefix` cannot be a function.","Update `prefix` in your configuration to be a string to eliminate this warning.","https://tailwindcss.com/docs/upgrade-guide#prefix-cannot-be-a-function"]),e.prefix=""):e.prefix=null!=(r=e.prefix)?r:"",e.content={files:(()=>{let{content:t,purge:r}=e;return Array.isArray(r)?r:Array.isArray(null==r?void 0:r.content)?r.content:Array.isArray(t)?t:Array.isArray(null==t?void 0:t.content)?t.content:Array.isArray(null==t?void 0:t.files)?t.files:[]})(),extract:(()=>{var t,r,n,o,i,a,s,u,l,c,f,p,h,d;let g=null!=(t=e.purge)&&t.extract?e.purge.extract:null!=(r=e.content)&&r.extract?e.content.extract:null!=(n=e.purge)&&null!=(o=n.extract)&&o.DEFAULT?e.purge.extract.DEFAULT:null!=(i=e.content)&&null!=(a=i.extract)&&a.DEFAULT?e.content.extract.DEFAULT:null!=(s=e.purge)&&null!=(u=s.options)&&u.extractors?e.purge.options.extractors:null!=(l=e.content)&&null!=(c=l.options)&&c.extractors?e.content.options.extractors:{},v={},m=null!=(f=e.purge)&&null!=(p=f.options)&&p.defaultExtractor?e.purge.options.defaultExtractor:null!=(h=e.content)&&null!=(d=h.options)&&d.defaultExtractor?e.content.options.defaultExtractor:void 0;if(void 0!==m&&(v.DEFAULT=m),"function"==typeof g)v.DEFAULT=g;else if(Array.isArray(g))for(let{extensions:e,extractor:t}of g??[])for(let r of e)v[r]=t;else"object"==typeof g&&null!==g&&Object.assign(v,g);return v})(),transform:(l=null!=(n=e.purge)&&n.transform?e.purge.transform:null!=(o=e.content)&&o.transform?e.content.transform:null!=(i=e.purge)&&null!=(a=i.transform)&&a.DEFAULT?e.purge.transform.DEFAULT:null!=(s=e.content)&&null!=(u=s.transform)&&u.DEFAULT?e.content.transform.DEFAULT:{},c={},"function"==typeof l&&(c.DEFAULT=l),"object"==typeof l&&null!==l&&Object.assign(c,l),c)},e.content.files))if("string"==typeof f&&/{([^,]*?)}/g.test(f)){t.default.warn("invalid-glob-braces",[`The glob pattern ${(0,t.dim)(f)} in your Tailwind CSS configuration is invalid.`,`Update it to ${(0,t.dim)(f.replace(/{([^,]*?)}/g,"$1"))} to silence this warning.`]);break}return e}}),nz=nC(e=>{function t(e){if("[object Object]"!==Object.prototype.toString.call(e))return!1;let t=Object.getPrototypeOf(e);return null===t||t===Object.prototype}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:()=>t})}),nq=nC(e=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"cloneDeep",{enumerable:!0,get:()=>function e(t){return Array.isArray(t)?t.map(t=>e(t)):"object"==typeof t&&null!==t?Object.fromEntries(Object.entries(t).map(([t,r])=>[t,e(r)])):t}})}),nV=nC((e,t)=>{e.__esModule=!0,e.default=function(e){if(!r.test(e))return e;for(var t="",n=0;n<e.length;n++){if("\\"===e[n]){var o=function(e){for(var t=e.toLowerCase(),r="",n=!1,o=0;o<6&&void 0!==t[o];o++){var i=t.charCodeAt(o),a=i>=97&&i<=102||i>=48&&i<=57;if(n=32===i,!a)break;r+=t[o]}if(0!==r.length){var s=parseInt(r,16);return s>=55296&&s<=57343||0===s||s>1114111?["�",r.length+ +!!n]:[String.fromCodePoint(s),r.length+ +!!n]}}(e.slice(n+1,n+7));if(void 0!==o){t+=o[0],n+=o[1];continue}if("\\"===e[n+1]){t+="\\",n++;continue}e.length===n+1&&(t+=e[n]);continue}t+=e[n]}return t};var r=/\\/;t.exports=e.default}),nH=nC((e,t)=>{e.__esModule=!0,e.default=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(;r.length>0;){var o=r.shift();if(!e[o])return;e=e[o]}return e},t.exports=e.default}),nX=nC((e,t)=>{e.__esModule=!0,e.default=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(;r.length>0;){var o=r.shift();e[o]||(e[o]={}),e=e[o]}},t.exports=e.default}),nY=nC((e,t)=>{e.__esModule=!0,e.default=function(e){for(var t="",r=e.indexOf("/*"),n=0;r>=0;){t+=e.slice(n,r);var o=e.indexOf("*/",r+2);if(o<0)return t;n=o+2,r=e.indexOf("/*",n)}return t+e.slice(n)},t.exports=e.default}),nZ=nC(e=>{function t(e){return e&&e.__esModule?e:{default:e}}e.__esModule=!0,e.stripComments=e.ensureObject=e.getProp=e.unesc=void 0,e.unesc=t(nV()).default,e.getProp=t(nH()).default,e.ensureObject=t(nX()).default,e.stripComments=t(nY()).default}),nQ=nC((e,t)=>{e.__esModule=!0,e.default=void 0;var r=nZ(),n=function e(t,r){if("object"!=typeof t||null===t)return t;var n=new t.constructor;for(var o in t)if(t.hasOwnProperty(o)){var i=t[o],a=typeof i;"parent"===o&&"object"===a?r&&(n[o]=r):i instanceof Array?n[o]=i.map(function(t){return e(t,n)}):n[o]=e(i,n)}return n};e.default=function(){function e(e){void 0===e&&(e={}),Object.assign(this,e),this.spaces=this.spaces||{},this.spaces.before=this.spaces.before||"",this.spaces.after=this.spaces.after||""}var t,o=e.prototype;return o.remove=function(){return this.parent&&this.parent.removeChild(this),this.parent=void 0,this},o.replaceWith=function(){if(this.parent){for(var e in arguments)this.parent.insertBefore(this,arguments[e]);this.remove()}return this},o.next=function(){return this.parent.at(this.parent.index(this)+1)},o.prev=function(){return this.parent.at(this.parent.index(this)-1)},o.clone=function(e){void 0===e&&(e={});var t=n(this);for(var r in e)t[r]=e[r];return t},o.appendToPropertyAndEscape=function(e,t,r){this.raws||(this.raws={});var n=this[e],o=this.raws[e];this[e]=n+t,o||r!==t?this.raws[e]=(o||n)+r:delete this.raws[e]},o.setPropertyAndEscape=function(e,t,r){this.raws||(this.raws={}),this[e]=t,this.raws[e]=r},o.setPropertyWithoutEscape=function(e,t){this[e]=t,this.raws&&delete this.raws[e]},o.isAtPosition=function(e,t){if(this.source&&this.source.start&&this.source.end)return!(this.source.start.line>e||this.source.end.line<e||this.source.start.line===e&&this.source.start.column>t||this.source.end.line===e&&this.source.end.column<t)},o.stringifyProperty=function(e){return this.raws&&this.raws[e]||this[e]},o.valueToString=function(){return String(this.stringifyProperty("value"))},o.toString=function(){return[this.rawSpaceBefore,this.valueToString(),this.rawSpaceAfter].join("")},t=[{key:"rawSpaceBefore",get:function(){var e=this.raws&&this.raws.spaces&&this.raws.spaces.before;return void 0===e&&(e=this.spaces&&this.spaces.before),e||""},set:function(e){(0,r.ensureObject)(this,"raws","spaces"),this.raws.spaces.before=e}},{key:"rawSpaceAfter",get:function(){var e=this.raws&&this.raws.spaces&&this.raws.spaces.after;return void 0===e&&(e=this.spaces.after),e||""},set:function(e){(0,r.ensureObject)(this,"raws","spaces"),this.raws.spaces.after=e}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,t),e}(),t.exports=e.default}),nK=nC(e=>{e.__esModule=!0,e.UNIVERSAL=e.ATTRIBUTE=e.CLASS=e.COMBINATOR=e.COMMENT=e.ID=e.NESTING=e.PSEUDO=e.ROOT=e.SELECTOR=e.STRING=e.TAG=void 0,e.TAG="tag",e.STRING="string",e.SELECTOR="selector",e.ROOT="root",e.PSEUDO="pseudo",e.NESTING="nesting",e.ID="id",e.COMMENT="comment",e.COMBINATOR="combinator",e.CLASS="class",e.ATTRIBUTE="attribute",e.UNIVERSAL="universal"}),nJ=nC((e,t)=>{e.__esModule=!0,e.default=void 0;var r=function(e){return e&&e.__esModule?e:{default:e}}(nQ()),n=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=o();if(t&&t.has(e))return t.get(e);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)){var a=n?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(r,i,a):r[i]=e[i]}return r.default=e,t&&t.set(e,r),r}(nK());function o(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return o=function(){return e},e}function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function a(e,t){return(a=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}e.default=function(e){function t(t){var r;return(r=e.call(this,t)||this).nodes||(r.nodes=[]),r}t.prototype=Object.create(e.prototype),t.prototype.constructor=t,a(t,e);var r,o=t.prototype;return o.append=function(e){return e.parent=this,this.nodes.push(e),this},o.prepend=function(e){return e.parent=this,this.nodes.unshift(e),this},o.at=function(e){return this.nodes[e]},o.index=function(e){return"number"==typeof e?e:this.nodes.indexOf(e)},o.removeChild=function(e){var t;for(var r in e=this.index(e),this.at(e).parent=void 0,this.nodes.splice(e,1),this.indexes)(t=this.indexes[r])>=e&&(this.indexes[r]=t-1);return this},o.removeAll=function(){for(var e,t=function(e,t){var r;if(typeof Symbol>"u"||null==e[Symbol.iterator]){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return i(e,void 0);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return i(e,t)}}(e))){r&&(e=r);var n=0;return function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}}}throw TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}return(r=e[Symbol.iterator]()).next.bind(r)}(this.nodes);!(e=t()).done;)e.value.parent=void 0;return this.nodes=[],this},o.empty=function(){return this.removeAll()},o.insertAfter=function(e,t){t.parent=this;var r,n=this.index(e);for(var o in this.nodes.splice(n+1,0,t),t.parent=this,this.indexes)n<=(r=this.indexes[o])&&(this.indexes[o]=r+1);return this},o.insertBefore=function(e,t){t.parent=this;var r,n=this.index(e);for(var o in this.nodes.splice(n,0,t),t.parent=this,this.indexes)(r=this.indexes[o])<=n&&(this.indexes[o]=r+1);return this},o._findChildAtPosition=function(e,t){var r=void 0;return this.each(function(n){if(n.atPosition){var o=n.atPosition(e,t);if(o)return r=o,!1}else if(n.isAtPosition(e,t))return r=n,!1}),r},o.atPosition=function(e,t){if(this.isAtPosition(e,t))return this._findChildAtPosition(e,t)||this},o._inferEndPosition=function(){this.last&&this.last.source&&this.last.source.end&&(this.source=this.source||{},this.source.end=this.source.end||{},Object.assign(this.source.end,this.last.source.end))},o.each=function(e){this.lastEach||(this.lastEach=0),this.indexes||(this.indexes={}),this.lastEach++;var t,r,n=this.lastEach;if(this.indexes[n]=0,this.length){for(;this.indexes[n]<this.length&&(t=this.indexes[n],!1!==(r=e(this.at(t),t)));)this.indexes[n]+=1;if(delete this.indexes[n],!1===r)return!1}},o.walk=function(e){return this.each(function(t,r){var n=e(t,r);if(!1!==n&&t.length&&(n=t.walk(e)),!1===n)return!1})},o.walkAttributes=function(e){var t=this;return this.walk(function(r){if(r.type===n.ATTRIBUTE)return e.call(t,r)})},o.walkClasses=function(e){var t=this;return this.walk(function(r){if(r.type===n.CLASS)return e.call(t,r)})},o.walkCombinators=function(e){var t=this;return this.walk(function(r){if(r.type===n.COMBINATOR)return e.call(t,r)})},o.walkComments=function(e){var t=this;return this.walk(function(r){if(r.type===n.COMMENT)return e.call(t,r)})},o.walkIds=function(e){var t=this;return this.walk(function(r){if(r.type===n.ID)return e.call(t,r)})},o.walkNesting=function(e){var t=this;return this.walk(function(r){if(r.type===n.NESTING)return e.call(t,r)})},o.walkPseudos=function(e){var t=this;return this.walk(function(r){if(r.type===n.PSEUDO)return e.call(t,r)})},o.walkTags=function(e){var t=this;return this.walk(function(r){if(r.type===n.TAG)return e.call(t,r)})},o.walkUniversals=function(e){var t=this;return this.walk(function(r){if(r.type===n.UNIVERSAL)return e.call(t,r)})},o.split=function(e){var t=this,r=[];return this.reduce(function(n,o,i){var a=e.call(t,o);return r.push(o),a?(n.push(r),r=[]):i===t.length-1&&n.push(r),n},[])},o.map=function(e){return this.nodes.map(e)},o.reduce=function(e,t){return this.nodes.reduce(e,t)},o.every=function(e){return this.nodes.every(e)},o.some=function(e){return this.nodes.some(e)},o.filter=function(e){return this.nodes.filter(e)},o.sort=function(e){return this.nodes.sort(e)},o.toString=function(){return this.map(String).join("")},r=[{key:"first",get:function(){return this.at(0)}},{key:"last",get:function(){return this.at(this.length-1)}},{key:"length",get:function(){return this.nodes.length}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(t.prototype,r),t}(r.default),t.exports=e.default}),n0=nC((e,t)=>{e.__esModule=!0,e.default=void 0;var r=function(e){return e&&e.__esModule?e:{default:e}}(nJ()),n=nK();function o(e,t){return(o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}e.default=function(e){function t(t){var r;return(r=e.call(this,t)||this).type=n.ROOT,r}t.prototype=Object.create(e.prototype),t.prototype.constructor=t,o(t,e);var r,i=t.prototype;return i.toString=function(){var e=this.reduce(function(e,t){return e.push(String(t)),e},[]).join(",");return this.trailingComma?e+",":e},i.error=function(e,t){return this._error?this._error(e,t):Error(e)},r=[{key:"errorGenerator",set:function(e){this._error=e}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(t.prototype,r),t}(r.default),t.exports=e.default}),n1=nC((e,t)=>{e.__esModule=!0,e.default=void 0;var r=function(e){return e&&e.__esModule?e:{default:e}}(nJ()),n=nK();function o(e,t){return(o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}e.default=function(e){function t(t){var r;return(r=e.call(this,t)||this).type=n.SELECTOR,r}return t.prototype=Object.create(e.prototype),t.prototype.constructor=t,o(t,e),t}(r.default),t.exports=e.default}),n2=nC((e,t)=>{var r={}.hasOwnProperty,n=function(e,t){if(!e)return t;var n={};for(var o in t)n[o]=r.call(e,o)?e[o]:t[o];return n},o=/[ -,\.\/:-@\[-\^`\{-~]/,i=/[ -,\.\/:-@\[\]\^`\{-~]/,a=/(^|\\+)?(\\[A-F0-9]{1,6})\x20(?![a-fA-F0-9\x20])/g,s=function e(t,r){"single"!=(r=n(r,e.options)).quotes&&"double"!=r.quotes&&(r.quotes="single");for(var s="double"==r.quotes?'"':"'",u=r.isIdentifier,l=t.charAt(0),c="",f=0,p=t.length;f<p;){var h=t.charAt(f++),d=h.charCodeAt(),g=void 0;if(d<32||d>126){if(d>=55296&&d<=56319&&f<p){var v=t.charCodeAt(f++);(64512&v)==56320?d=((1023&d)<<10)+(1023&v)+65536:f--}g="\\"+d.toString(16).toUpperCase()+" "}else g=r.escapeEverything?o.test(h)?"\\"+h:"\\"+d.toString(16).toUpperCase()+" ":/[\t\n\f\r\x0B]/.test(h)?"\\"+d.toString(16).toUpperCase()+" ":"\\"==h||!u&&('"'==h&&s==h||"'"==h&&s==h)||u&&i.test(h)?"\\"+h:h;c+=g}return u&&(/^-[-\d]/.test(c)?c="\\-"+c.slice(1):/\d/.test(l)&&(c="\\3"+l+" "+c.slice(1))),c=c.replace(a,function(e,t,r){return t&&t.length%2?e:(t||"")+r}),!u&&r.wrap?s+c+s:c};s.options={escapeEverything:!1,isIdentifier:!1,quotes:"single",wrap:!1},s.version="3.0.0",t.exports=s}),n3=nC((e,t)=>{e.__esModule=!0,e.default=void 0;var r=a(n2()),n=nZ(),o=a(nQ()),i=nK();function a(e){return e&&e.__esModule?e:{default:e}}function s(e,t){return(s=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}e.default=function(e){var t;function o(t){var r;return(r=e.call(this,t)||this).type=i.CLASS,r._constructed=!0,r}return o.prototype=Object.create(e.prototype),o.prototype.constructor=o,s(o,e),o.prototype.valueToString=function(){return"."+e.prototype.valueToString.call(this)},t=[{key:"value",get:function(){return this._value},set:function(e){if(this._constructed){var t=(0,r.default)(e,{isIdentifier:!0});t!==e?((0,n.ensureObject)(this,"raws"),this.raws.value=t):this.raws&&delete this.raws.value}this._value=e}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(o.prototype,t),o}(o.default),t.exports=e.default}),n4=nC((e,t)=>{e.__esModule=!0,e.default=void 0;var r=function(e){return e&&e.__esModule?e:{default:e}}(nQ()),n=nK();function o(e,t){return(o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}e.default=function(e){function t(t){var r;return(r=e.call(this,t)||this).type=n.COMMENT,r}return t.prototype=Object.create(e.prototype),t.prototype.constructor=t,o(t,e),t}(r.default),t.exports=e.default}),n5=nC((e,t)=>{e.__esModule=!0,e.default=void 0;var r=function(e){return e&&e.__esModule?e:{default:e}}(nQ()),n=nK();function o(e,t){return(o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}e.default=function(e){function t(t){var r;return(r=e.call(this,t)||this).type=n.ID,r}return t.prototype=Object.create(e.prototype),t.prototype.constructor=t,o(t,e),t.prototype.valueToString=function(){return"#"+e.prototype.valueToString.call(this)},t}(r.default),t.exports=e.default}),n6=nC((e,t)=>{e.__esModule=!0,e.default=void 0;var r=i(n2()),n=nZ(),o=i(nQ());function i(e){return e&&e.__esModule?e:{default:e}}function a(e,t){return(a=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}e.default=function(e){function t(){return e.apply(this,arguments)||this}t.prototype=Object.create(e.prototype),t.prototype.constructor=t,a(t,e);var o,i=t.prototype;return i.qualifiedName=function(e){return this.namespace?this.namespaceString+"|"+e:e},i.valueToString=function(){return this.qualifiedName(e.prototype.valueToString.call(this))},o=[{key:"namespace",get:function(){return this._namespace},set:function(e){if(!0===e||"*"===e||"&"===e){this._namespace=e,this.raws&&delete this.raws.namespace;return}var t=(0,r.default)(e,{isIdentifier:!0});this._namespace=e,t!==e?((0,n.ensureObject)(this,"raws"),this.raws.namespace=t):this.raws&&delete this.raws.namespace}},{key:"ns",get:function(){return this._namespace},set:function(e){this.namespace=e}},{key:"namespaceString",get:function(){if(!this.namespace)return"";var e=this.stringifyProperty("namespace");return!0===e?"":e}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(t.prototype,o),t}(o.default),t.exports=e.default}),n8=nC((e,t)=>{e.__esModule=!0,e.default=void 0;var r=function(e){return e&&e.__esModule?e:{default:e}}(n6()),n=nK();function o(e,t){return(o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}e.default=function(e){function t(t){var r;return(r=e.call(this,t)||this).type=n.TAG,r}return t.prototype=Object.create(e.prototype),t.prototype.constructor=t,o(t,e),t}(r.default),t.exports=e.default}),n7=nC((e,t)=>{e.__esModule=!0,e.default=void 0;var r=function(e){return e&&e.__esModule?e:{default:e}}(nQ()),n=nK();function o(e,t){return(o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}e.default=function(e){function t(t){var r;return(r=e.call(this,t)||this).type=n.STRING,r}return t.prototype=Object.create(e.prototype),t.prototype.constructor=t,o(t,e),t}(r.default),t.exports=e.default}),n9=nC((e,t)=>{e.__esModule=!0,e.default=void 0;var r=function(e){return e&&e.__esModule?e:{default:e}}(nJ()),n=nK();function o(e,t){return(o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}e.default=function(e){function t(t){var r;return(r=e.call(this,t)||this).type=n.PSEUDO,r}return t.prototype=Object.create(e.prototype),t.prototype.constructor=t,o(t,e),t.prototype.toString=function(){var e=this.length?"("+this.map(String).join(",")+")":"";return[this.rawSpaceBefore,this.stringifyProperty("value"),e,this.rawSpaceAfter].join("")},t}(r.default),t.exports=e.default}),oe=nC((e,t)=>{t.exports=function(e,t){return function(...r){return console.warn(t),e(...r)}}}),ot=nC(e=>{e.__esModule=!0,e.unescapeValue=h,e.default=void 0;var t,r=a(n2()),n=a(nV()),o=a(n6()),i=nK();function a(e){return e&&e.__esModule?e:{default:e}}function s(e,t){return(s=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}var u=oe(),l=/^('|")([^]*)\1$/,c=u(function(){},"Assigning an attribute a value containing characters that might need to be escaped is deprecated. Call attribute.setValue() instead."),f=u(function(){},"Assigning attr.quoted is deprecated and has no effect. Assign to attr.quoteMark instead."),p=u(function(){},"Constructing an Attribute selector with a value without specifying quoteMark is deprecated. Note: The value should be unescaped now.");function h(e){var t=!1,r=null,o=e,i=o.match(l);return i&&(r=i[1],o=i[2]),(o=(0,n.default)(o))!==e&&(t=!0),{deprecatedUsage:t,unescaped:o,quoteMark:r}}var d=function(e){function t(t){var r;return void 0===t&&(t={}),(r=e.call(this,function(e){if(void 0!==e.quoteMark||void 0===e.value)return e;p();var t=h(e.value),r=t.quoteMark,n=t.unescaped;return e.raws||(e.raws={}),void 0===e.raws.value&&(e.raws.value=e.value),e.value=n,e.quoteMark=r,e}(t))||this).type=i.ATTRIBUTE,r.raws=r.raws||{},Object.defineProperty(r.raws,"unquoted",{get:u(function(){return r.value},"attr.raws.unquoted is deprecated. Call attr.value instead."),set:u(function(){return r.value},"Setting attr.raws.unquoted is deprecated and has no effect. attr.value is unescaped by default now.")}),r._constructed=!0,r}t.prototype=Object.create(e.prototype),t.prototype.constructor=t,s(t,e);var n,o=t.prototype;return o.getQuotedValue=function(e){void 0===e&&(e={});var t=g[this._determineQuoteMark(e)];return(0,r.default)(this._value,t)},o._determineQuoteMark=function(e){return e.smart?this.smartQuoteMark(e):this.preferredQuoteMark(e)},o.setValue=function(e,t){void 0===t&&(t={}),this._value=e,this._quoteMark=this._determineQuoteMark(t),this._syncRawValue()},o.smartQuoteMark=function(e){var n=this.value,o=n.replace(/[^']/g,"").length,i=n.replace(/[^"]/g,"").length;if(o+i!==0)return i===o?this.preferredQuoteMark(e):i<o?t.DOUBLE_QUOTE:t.SINGLE_QUOTE;var a=(0,r.default)(n,{isIdentifier:!0});if(a===n)return t.NO_QUOTE;var s=this.preferredQuoteMark(e);if(s===t.NO_QUOTE){var u=this.quoteMark||e.quoteMark||t.DOUBLE_QUOTE,l=g[u];if((0,r.default)(n,l).length<a.length)return u}return s},o.preferredQuoteMark=function(e){var r=e.preferCurrentQuoteMark?this.quoteMark:e.quoteMark;return void 0===r&&(r=e.preferCurrentQuoteMark?e.quoteMark:this.quoteMark),void 0===r&&(r=t.DOUBLE_QUOTE),r},o._syncRawValue=function(){var e=(0,r.default)(this._value,g[this.quoteMark]);e===this._value?this.raws&&delete this.raws.value:this.raws.value=e},o._handleEscapes=function(e,t){if(this._constructed){var n=(0,r.default)(t,{isIdentifier:!0});n!==t?this.raws[e]=n:delete this.raws[e]}},o._spacesFor=function(e){return Object.assign({before:"",after:""},this.spaces[e]||{},this.raws.spaces&&this.raws.spaces[e]||{})},o._stringFor=function(e,t,r){void 0===t&&(t=e),void 0===r&&(r=v);var n=this._spacesFor(t);return r(this.stringifyProperty(e),n)},o.offsetOf=function(e){var t=1,r=this._spacesFor("attribute");if(t+=r.before.length,"namespace"===e||"ns"===e)return this.namespace?t:-1;if("attributeNS"===e||(t+=this.namespaceString.length,this.namespace&&(t+=1),"attribute"===e))return t;t+=this.stringifyProperty("attribute").length,t+=r.after.length;var n=this._spacesFor("operator");t+=n.before.length;var o=this.stringifyProperty("operator");if("operator"===e)return o?t:-1;t+=o.length,t+=n.after.length;var i=this._spacesFor("value");t+=i.before.length;var a=this.stringifyProperty("value");return"value"===e?a?t:-1:(t+=a.length,t+=i.after.length,t+=this._spacesFor("insensitive").before.length,"insensitive"===e&&this.insensitive?t:-1)},o.toString=function(){var e=this,t=[this.rawSpaceBefore,"["];return t.push(this._stringFor("qualifiedAttribute","attribute")),this.operator&&(this.value||""===this.value)&&(t.push(this._stringFor("operator")),t.push(this._stringFor("value")),t.push(this._stringFor("insensitiveFlag","insensitive",function(t,r){return!(t.length>0)||e.quoted||0!==r.before.length||e.spaces.value&&e.spaces.value.after||(r.before=" "),v(t,r)}))),t.push("]"),t.push(this.rawSpaceAfter),t.join("")},n=[{key:"quoted",get:function(){var e=this.quoteMark;return"'"===e||'"'===e},set:function(e){f()}},{key:"quoteMark",get:function(){return this._quoteMark},set:function(e){if(!this._constructed){this._quoteMark=e;return}this._quoteMark!==e&&(this._quoteMark=e,this._syncRawValue())}},{key:"qualifiedAttribute",get:function(){return this.qualifiedName(this.raws.attribute||this.attribute)}},{key:"insensitiveFlag",get:function(){return this.insensitive?"i":""}},{key:"value",get:function(){return this._value},set:function(e){if(this._constructed){var t=h(e),r=t.deprecatedUsage,n=t.unescaped,o=t.quoteMark;r&&c(),(n!==this._value||o!==this._quoteMark)&&(this._value=n,this._quoteMark=o,this._syncRawValue())}else this._value=e}},{key:"attribute",get:function(){return this._attribute},set:function(e){this._handleEscapes("attribute",e),this._attribute=e}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(t.prototype,n),t}(o.default);e.default=d,d.NO_QUOTE=null,d.SINGLE_QUOTE="'",d.DOUBLE_QUOTE='"';var g=((t={"'":{quotes:"single",wrap:!0},'"':{quotes:"double",wrap:!0}})[null]={isIdentifier:!0},t);function v(e,t){return""+t.before+e+t.after}}),or=nC((e,t)=>{e.__esModule=!0,e.default=void 0;var r=function(e){return e&&e.__esModule?e:{default:e}}(n6()),n=nK();function o(e,t){return(o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}e.default=function(e){function t(t){var r;return(r=e.call(this,t)||this).type=n.UNIVERSAL,r.value="*",r}return t.prototype=Object.create(e.prototype),t.prototype.constructor=t,o(t,e),t}(r.default),t.exports=e.default}),on=nC((e,t)=>{e.__esModule=!0,e.default=void 0;var r=function(e){return e&&e.__esModule?e:{default:e}}(nQ()),n=nK();function o(e,t){return(o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}e.default=function(e){function t(t){var r;return(r=e.call(this,t)||this).type=n.COMBINATOR,r}return t.prototype=Object.create(e.prototype),t.prototype.constructor=t,o(t,e),t}(r.default),t.exports=e.default}),oo=nC((e,t)=>{e.__esModule=!0,e.default=void 0;var r=function(e){return e&&e.__esModule?e:{default:e}}(nQ()),n=nK();function o(e,t){return(o=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e})(e,t)}e.default=function(e){function t(t){var r;return(r=e.call(this,t)||this).type=n.NESTING,r.value="&",r}return t.prototype=Object.create(e.prototype),t.prototype.constructor=t,o(t,e),t}(r.default),t.exports=e.default}),oi=nC((e,t)=>{e.__esModule=!0,e.default=function(e){return e.sort(function(e,t){return e-t})},t.exports=e.default}),oa=nC(e=>{e.__esModule=!0,e.combinator=e.word=e.comment=e.str=e.tab=e.newline=e.feed=e.cr=e.backslash=e.bang=e.slash=e.doubleQuote=e.singleQuote=e.space=e.greaterThan=e.pipe=e.equals=e.plus=e.caret=e.tilde=e.dollar=e.closeSquare=e.openSquare=e.closeParenthesis=e.openParenthesis=e.semicolon=e.colon=e.comma=e.at=e.asterisk=e.ampersand=void 0,e.ampersand=38,e.asterisk=42,e.at=64,e.comma=44,e.colon=58,e.semicolon=59,e.openParenthesis=40,e.closeParenthesis=41,e.openSquare=91,e.closeSquare=93,e.dollar=36,e.tilde=126,e.caret=94,e.plus=43,e.equals=61,e.pipe=124,e.greaterThan=62,e.space=32,e.singleQuote=39,e.doubleQuote=34,e.slash=47,e.bang=33,e.backslash=92,e.cr=13,e.feed=12,e.newline=10,e.tab=9,e.str=39,e.comment=-1,e.word=-2,e.combinator=-3}),os=nC(e=>{e.__esModule=!0,e.default=function(e){var t,r,n,i,l,c,f,p,h,d,g,v,m=[],y=e.css.valueOf(),D=y.length,b=-1,x=1,w=0,E=0;function S(t,r){if(e.safe)y+=r,p=y.length-1;else throw e.error("Unclosed "+t,x,w-b,w)}for(;w<D;){switch((t=y.charCodeAt(w))===o.newline&&(b=w,x+=1),t){case o.space:case o.tab:case o.newline:case o.cr:case o.feed:p=w;do p+=1,(t=y.charCodeAt(p))===o.newline&&(b=p,x+=1);while(t===o.space||t===o.newline||t===o.tab||t===o.cr||t===o.feed);v=o.space,n=x,r=p-b-1,E=p;break;case o.plus:case o.greaterThan:case o.tilde:case o.pipe:p=w;do p+=1,t=y.charCodeAt(p);while(t===o.plus||t===o.greaterThan||t===o.tilde||t===o.pipe);v=o.combinator,n=x,r=w-b,E=p;break;case o.asterisk:case o.ampersand:case o.bang:case o.comma:case o.equals:case o.dollar:case o.caret:case o.openSquare:case o.closeSquare:case o.colon:case o.semicolon:case o.openParenthesis:case o.closeParenthesis:p=w,v=t,n=x,r=w-b,E=p+1;break;case o.singleQuote:case o.doubleQuote:g=t===o.singleQuote?"'":'"',p=w;do for(i=!1,-1===(p=y.indexOf(g,p+1))&&S("quote",g),l=p;y.charCodeAt(l-1)===o.backslash;)l-=1,i=!i;while(i);v=o.str,n=x,r=w-b,E=p+1;break;default:t===o.slash&&y.charCodeAt(w+1)===o.asterisk?(0===(p=y.indexOf("*/",w+2)+1)&&S("comment","*/"),(c=(f=y.slice(w,p+1).split(`
`)).length-1)>0?(h=x+c,d=p-f[c].length):(h=x,d=b),v=o.comment,x=h,n=h,r=p-d):t===o.slash?(p=w,v=t,n=x,r=w-b,E=p+1):(p=function(e,t){var r,n=t;do{if(s[r=e.charCodeAt(n)])break;r===o.backslash?n=function(e,t){var r=t,n=e.charCodeAt(r+1);if(!a[n])if(u[n]){var i=0;do r++,i++,n=e.charCodeAt(r+1);while(u[n]&&i<6);i<6&&n===o.space&&r++}else r++;return r}(e,n)+1:n++}while(n<e.length);return n-1}(y,w),v=o.word,n=x,r=p-b),E=p+1}m.push([v,x,w-b,n,r,w,E]),d&&(b=d,d=null),w=E}return m},e.FIELDS=void 0;var t,r,n,o=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=i();if(t&&t.has(e))return t.get(e);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var a=n?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(r,o,a):r[o]=e[o]}return r.default=e,t&&t.set(e,r),r}(oa());function i(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return i=function(){return e},e}var a=((r={})[o.tab]=!0,r[o.newline]=!0,r[o.cr]=!0,r[o.feed]=!0,r),s=((n={})[o.space]=!0,n[o.tab]=!0,n[o.newline]=!0,n[o.cr]=!0,n[o.feed]=!0,n[o.ampersand]=!0,n[o.asterisk]=!0,n[o.bang]=!0,n[o.comma]=!0,n[o.colon]=!0,n[o.semicolon]=!0,n[o.openParenthesis]=!0,n[o.closeParenthesis]=!0,n[o.openSquare]=!0,n[o.closeSquare]=!0,n[o.singleQuote]=!0,n[o.doubleQuote]=!0,n[o.plus]=!0,n[o.pipe]=!0,n[o.tilde]=!0,n[o.greaterThan]=!0,n[o.equals]=!0,n[o.dollar]=!0,n[o.caret]=!0,n[o.slash]=!0,n),u={},l="0123456789abcdefABCDEF";for(t=0;t<l.length;t++)u[l.charCodeAt(t)]=!0;e.FIELDS={TYPE:0,START_LINE:1,START_COL:2,END_LINE:3,END_COL:4,START_POS:5,END_POS:6}}),ou=nC((e,t)=>{e.__esModule=!0,e.default=void 0;var r,n,o=E(n0()),i=E(n1()),a=E(n3()),s=E(n4()),u=E(n5()),l=E(n8()),c=E(n7()),f=E(n9()),p=w(ot()),h=E(or()),d=E(on()),g=E(oo()),v=E(oi()),m=w(os()),y=w(oa()),D=w(nK()),b=nZ();function x(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return x=function(){return e},e}function w(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=x();if(t&&t.has(e))return t.get(e);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var i=n?Object.getOwnPropertyDescriptor(e,o):null;i&&(i.get||i.set)?Object.defineProperty(r,o,i):r[o]=e[o]}return r.default=e,t&&t.set(e,r),r}function E(e){return e&&e.__esModule?e:{default:e}}var S=((r={})[y.space]=!0,r[y.cr]=!0,r[y.feed]=!0,r[y.newline]=!0,r[y.tab]=!0,r),k=Object.assign({},S,((n={})[y.comment]=!0,n));function F(e){return{line:e[m.FIELDS.START_LINE],column:e[m.FIELDS.START_COL]}}function C(e){return{line:e[m.FIELDS.END_LINE],column:e[m.FIELDS.END_COL]}}function _(e,t,r,n){return{start:{line:e,column:t},end:{line:r,column:n}}}function T(e){return _(e[m.FIELDS.START_LINE],e[m.FIELDS.START_COL],e[m.FIELDS.END_LINE],e[m.FIELDS.END_COL])}function O(e,t){if(e)return _(e[m.FIELDS.START_LINE],e[m.FIELDS.START_COL],t[m.FIELDS.END_LINE],t[m.FIELDS.END_COL])}function A(e,t){var r=e[t];if("string"==typeof r)return -1!==r.indexOf("\\")&&((0,b.ensureObject)(e,"raws"),e[t]=(0,b.unesc)(r),void 0===e.raws[t]&&(e.raws[t]=r)),e}function P(e,t){for(var r=-1,n=[];-1!==(r=e.indexOf(t,r+1));)n.push(r);return n}e.default=function(){function e(e,t){void 0===t&&(t={}),this.rule=e,this.options=Object.assign({lossy:!1,safe:!1},t),this.position=0,this.css="string"==typeof this.rule?this.rule:this.rule.selector,this.tokens=(0,m.default)({css:this.css,error:this._errorGenerator(),safe:this.options.safe});var r=O(this.tokens[0],this.tokens[this.tokens.length-1]);this.root=new o.default({source:r}),this.root.errorGenerator=this._errorGenerator();var n=new i.default({source:{start:{line:1,column:1}}});this.root.append(n),this.current=n,this.loop()}var t,r=e.prototype;return r._errorGenerator=function(){var e=this;return function(t,r){return"string"==typeof e.rule?Error(t):e.rule.error(t,r)}},r.attribute=function(){var e=[],t=this.currToken;for(this.position++;this.position<this.tokens.length&&this.currToken[m.FIELDS.TYPE]!==y.closeSquare;)e.push(this.currToken),this.position++;if(this.currToken[m.FIELDS.TYPE]!==y.closeSquare)return this.expected("closing square bracket",this.currToken[m.FIELDS.START_POS]);var r=e.length,n={source:_(t[1],t[2],this.currToken[3],this.currToken[4]),sourceIndex:t[m.FIELDS.START_POS]};if(1===r&&!~[y.word].indexOf(e[0][m.FIELDS.TYPE]))return this.expected("attribute",e[0][m.FIELDS.START_POS]);for(var o=0,i="",a="",s=null,u=!1;o<r;){var l=e[o],c=this.content(l),f=e[o+1];switch(l[m.FIELDS.TYPE]){case y.space:if(u=!0,this.options.lossy)break;if(s){(0,b.ensureObject)(n,"spaces",s);var h=n.spaces[s].after||"";n.spaces[s].after=h+c;var d=(0,b.getProp)(n,"raws","spaces",s,"after")||null;d&&(n.raws.spaces[s].after=d+c)}else i+=c,a+=c;break;case y.asterisk:f[m.FIELDS.TYPE]===y.equals?(n.operator=c,s="operator"):n.namespace&&("namespace"!==s||u)||!f||(i&&((0,b.ensureObject)(n,"spaces","attribute"),n.spaces.attribute.before=i,i=""),a&&((0,b.ensureObject)(n,"raws","spaces","attribute"),n.raws.spaces.attribute.before=i,a=""),n.namespace=(n.namespace||"")+c,((0,b.getProp)(n,"raws","namespace")||0)&&(n.raws.namespace+=c),s="namespace"),u=!1;break;case y.dollar:if("value"===s){var g=(0,b.getProp)(n,"raws","value");n.value+="$",g&&(n.raws.value=g+"$");break}case y.caret:f[m.FIELDS.TYPE]===y.equals&&(n.operator=c,s="operator"),u=!1;break;case y.combinator:if("~"===c&&f[m.FIELDS.TYPE]===y.equals&&(n.operator=c,s="operator"),"|"!==c){u=!1;break}f[m.FIELDS.TYPE]===y.equals?(n.operator=c,s="operator"):n.namespace||n.attribute||(n.namespace=!0),u=!1;break;case y.word:if(f&&"|"===this.content(f)&&e[o+2]&&e[o+2][m.FIELDS.TYPE]!==y.equals&&!n.operator&&!n.namespace)n.namespace=c,s="namespace";else if(n.attribute&&("attribute"!==s||u))if((n.value||""===n.value)&&("value"!==s||u)){var v="i"===c||"I"===c;(n.value||""===n.value)&&(n.quoteMark||u)?(n.insensitive=v,v&&"I"!==c||((0,b.ensureObject)(n,"raws"),n.raws.insensitiveFlag=c),s="insensitive",i&&((0,b.ensureObject)(n,"spaces","insensitive"),n.spaces.insensitive.before=i,i=""),a&&((0,b.ensureObject)(n,"raws","spaces","insensitive"),n.raws.spaces.insensitive.before=a,a="")):(n.value||""===n.value)&&(s="value",n.value+=c,n.raws.value&&(n.raws.value+=c))}else{var D=(0,b.unesc)(c),x=(0,b.getProp)(n,"raws","value")||"",w=n.value||"";n.value=w+D,n.quoteMark=null,(D!==c||x)&&((0,b.ensureObject)(n,"raws"),n.raws.value=(x||w)+c),s="value"}else i&&((0,b.ensureObject)(n,"spaces","attribute"),n.spaces.attribute.before=i,i=""),a&&((0,b.ensureObject)(n,"raws","spaces","attribute"),n.raws.spaces.attribute.before=a,a=""),n.attribute=(n.attribute||"")+c,((0,b.getProp)(n,"raws","attribute")||0)&&(n.raws.attribute+=c),s="attribute";u=!1;break;case y.str:if(!n.attribute||!n.operator)return this.error("Expected an attribute followed by an operator preceding the string.",{index:l[m.FIELDS.START_POS]});var E=(0,p.unescapeValue)(c),S=E.unescaped,k=E.quoteMark;n.value=S,n.quoteMark=k,s="value",(0,b.ensureObject)(n,"raws"),n.raws.value=c,u=!1;break;case y.equals:if(!n.attribute)return this.expected("attribute",l[m.FIELDS.START_POS],c);if(n.value)return this.error('Unexpected "=" found; an operator was already defined.',{index:l[m.FIELDS.START_POS]});n.operator=n.operator?n.operator+c:c,s="operator",u=!1;break;case y.comment:if(s)if(u||f&&f[m.FIELDS.TYPE]===y.space||"insensitive"===s){var F=(0,b.getProp)(n,"spaces",s,"after")||"",C=(0,b.getProp)(n,"raws","spaces",s,"after")||F;(0,b.ensureObject)(n,"raws","spaces",s),n.raws.spaces[s].after=C+c}else{var T=n[s]||"",O=(0,b.getProp)(n,"raws",s)||T;(0,b.ensureObject)(n,"raws"),n.raws[s]=O+c}else a+=c;break;default:return this.error('Unexpected "'+c+'" found.',{index:l[m.FIELDS.START_POS]})}o++}A(n,"attribute"),A(n,"namespace"),this.newNode(new p.default(n)),this.position++},r.parseWhitespaceEquivalentTokens=function(e){e<0&&(e=this.tokens.length);var t=this.position,r=[],n="",o=void 0;do if(S[this.currToken[m.FIELDS.TYPE]])this.options.lossy||(n+=this.content());else if(this.currToken[m.FIELDS.TYPE]===y.comment){var i={};n&&(i.before=n,n=""),o=new s.default({value:this.content(),source:T(this.currToken),sourceIndex:this.currToken[m.FIELDS.START_POS],spaces:i}),r.push(o)}while(++this.position<e);if(n){if(o)o.spaces.after=n;else if(!this.options.lossy){var a=this.tokens[t],u=this.tokens[this.position-1];r.push(new c.default({value:"",source:_(a[m.FIELDS.START_LINE],a[m.FIELDS.START_COL],u[m.FIELDS.END_LINE],u[m.FIELDS.END_COL]),sourceIndex:a[m.FIELDS.START_POS],spaces:{before:n,after:""}}))}}return r},r.convertWhitespaceNodesToSpace=function(e,t){var r=this;void 0===t&&(t=!1);var n="",o="";return e.forEach(function(e){var i=r.lossySpace(e.spaces.before,t),a=r.lossySpace(e.rawSpaceBefore,t);n+=i+r.lossySpace(e.spaces.after,t&&0===i.length),o+=i+e.value+r.lossySpace(e.rawSpaceAfter,t&&0===a.length)}),o===n&&(o=void 0),{space:n,rawSpace:o}},r.isNamedCombinator=function(e){return void 0===e&&(e=this.position),this.tokens[e+0]&&this.tokens[e+0][m.FIELDS.TYPE]===y.slash&&this.tokens[e+1]&&this.tokens[e+1][m.FIELDS.TYPE]===y.word&&this.tokens[e+2]&&this.tokens[e+2][m.FIELDS.TYPE]===y.slash},r.namedCombinator=function(){if(this.isNamedCombinator()){var e=this.content(this.tokens[this.position+1]),t=(0,b.unesc)(e).toLowerCase(),r={};t!==e&&(r.value="/"+e+"/");var n=new d.default({value:"/"+t+"/",source:_(this.currToken[m.FIELDS.START_LINE],this.currToken[m.FIELDS.START_COL],this.tokens[this.position+2][m.FIELDS.END_LINE],this.tokens[this.position+2][m.FIELDS.END_COL]),sourceIndex:this.currToken[m.FIELDS.START_POS],raws:r});return this.position=this.position+3,n}this.unexpected()},r.combinator=function(){var e,t=this;if("|"===this.content())return this.namespace();var r=this.locateNextMeaningfulToken(this.position);if(r<0||this.tokens[r][m.FIELDS.TYPE]===y.comma){var n=this.parseWhitespaceEquivalentTokens(r);if(n.length>0){var o=this.current.last;if(o){var i=this.convertWhitespaceNodesToSpace(n),a=i.space,s=i.rawSpace;void 0!==s&&(o.rawSpaceAfter+=s),o.spaces.after+=a}else n.forEach(function(e){return t.newNode(e)})}return}var u=this.currToken,l=void 0;if(r>this.position&&(l=this.parseWhitespaceEquivalentTokens(r)),this.isNamedCombinator()?e=this.namedCombinator():this.currToken[m.FIELDS.TYPE]===y.combinator?(e=new d.default({value:this.content(),source:T(this.currToken),sourceIndex:this.currToken[m.FIELDS.START_POS]}),this.position++):S[this.currToken[m.FIELDS.TYPE]]||l||this.unexpected(),e){if(l){var c=this.convertWhitespaceNodesToSpace(l),f=c.space,p=c.rawSpace;e.spaces.before=f,e.rawSpaceBefore=p}}else{var h=this.convertWhitespaceNodesToSpace(l,!0),g=h.space,v=h.rawSpace;v||(v=g);var D={},b={spaces:{}};g.endsWith(" ")&&v.endsWith(" ")?(D.before=g.slice(0,g.length-1),b.spaces.before=v.slice(0,v.length-1)):g.startsWith(" ")&&v.startsWith(" ")?(D.after=g.slice(1),b.spaces.after=v.slice(1)):b.value=v,e=new d.default({value:" ",source:O(u,this.tokens[this.position-1]),sourceIndex:u[m.FIELDS.START_POS],spaces:D,raws:b})}return this.currToken&&this.currToken[m.FIELDS.TYPE]===y.space&&(e.spaces.after=this.optionalSpace(this.content()),this.position++),this.newNode(e)},r.comma=function(){if(this.position===this.tokens.length-1){this.root.trailingComma=!0,this.position++;return}this.current._inferEndPosition();var e=new i.default({source:{start:F(this.tokens[this.position+1])}});this.current.parent.append(e),this.current=e,this.position++},r.comment=function(){var e=this.currToken;this.newNode(new s.default({value:this.content(),source:T(e),sourceIndex:e[m.FIELDS.START_POS]})),this.position++},r.error=function(e,t){throw this.root.error(e,t)},r.missingBackslash=function(){return this.error("Expected a backslash preceding the semicolon.",{index:this.currToken[m.FIELDS.START_POS]})},r.missingParenthesis=function(){return this.expected("opening parenthesis",this.currToken[m.FIELDS.START_POS])},r.missingSquareBracket=function(){return this.expected("opening square bracket",this.currToken[m.FIELDS.START_POS])},r.unexpected=function(){return this.error("Unexpected '"+this.content()+"'. Escaping special characters with \\ may help.",this.currToken[m.FIELDS.START_POS])},r.namespace=function(){var e=this.prevToken&&this.content(this.prevToken)||!0;return this.nextToken[m.FIELDS.TYPE]===y.word?(this.position++,this.word(e)):this.nextToken[m.FIELDS.TYPE]===y.asterisk?(this.position++,this.universal(e)):void 0},r.nesting=function(){if(this.nextToken&&"|"===this.content(this.nextToken))return void this.position++;var e=this.currToken;this.newNode(new g.default({value:this.content(),source:T(e),sourceIndex:e[m.FIELDS.START_POS]})),this.position++},r.parentheses=function(){var e=this.current.last,t=1;if(this.position++,e&&e.type===D.PSEUDO){var r=new i.default({source:{start:F(this.tokens[this.position-1])}}),n=this.current;for(e.append(r),this.current=r;this.position<this.tokens.length&&t;)this.currToken[m.FIELDS.TYPE]===y.openParenthesis&&t++,this.currToken[m.FIELDS.TYPE]===y.closeParenthesis&&t--,t?this.parse():(this.current.source.end=C(this.currToken),this.current.parent.source.end=C(this.currToken),this.position++);this.current=n}else{for(var o,a=this.currToken,s="(";this.position<this.tokens.length&&t;)this.currToken[m.FIELDS.TYPE]===y.openParenthesis&&t++,this.currToken[m.FIELDS.TYPE]===y.closeParenthesis&&t--,o=this.currToken,s+=this.parseParenthesisToken(this.currToken),this.position++;e?e.appendToPropertyAndEscape("value",s,s):this.newNode(new c.default({value:s,source:_(a[m.FIELDS.START_LINE],a[m.FIELDS.START_COL],o[m.FIELDS.END_LINE],o[m.FIELDS.END_COL]),sourceIndex:a[m.FIELDS.START_POS]}))}if(t)return this.expected("closing parenthesis",this.currToken[m.FIELDS.START_POS])},r.pseudo=function(){for(var e=this,t="",r=this.currToken;this.currToken&&this.currToken[m.FIELDS.TYPE]===y.colon;)t+=this.content(),this.position++;return this.currToken?this.currToken[m.FIELDS.TYPE]!==y.word?this.expected(["pseudo-class","pseudo-element"],this.currToken[m.FIELDS.START_POS]):void this.splitWord(!1,function(n,o){t+=n,e.newNode(new f.default({value:t,source:O(r,e.currToken),sourceIndex:r[m.FIELDS.START_POS]})),o>1&&e.nextToken&&e.nextToken[m.FIELDS.TYPE]===y.openParenthesis&&e.error("Misplaced parenthesis.",{index:e.nextToken[m.FIELDS.START_POS]})}):this.expected(["pseudo-class","pseudo-element"],this.position-1)},r.space=function(){var e=this.content();0===this.position||this.prevToken[m.FIELDS.TYPE]===y.comma||this.prevToken[m.FIELDS.TYPE]===y.openParenthesis||this.current.nodes.every(function(e){return"comment"===e.type})?(this.spaces=this.optionalSpace(e),this.position++):this.position===this.tokens.length-1||this.nextToken[m.FIELDS.TYPE]===y.comma||this.nextToken[m.FIELDS.TYPE]===y.closeParenthesis?(this.current.last.spaces.after=this.optionalSpace(e),this.position++):this.combinator()},r.string=function(){var e=this.currToken;this.newNode(new c.default({value:this.content(),source:T(e),sourceIndex:e[m.FIELDS.START_POS]})),this.position++},r.universal=function(e){var t=this.nextToken;if(t&&"|"===this.content(t))return this.position++,this.namespace();var r=this.currToken;this.newNode(new h.default({value:this.content(),source:T(r),sourceIndex:r[m.FIELDS.START_POS]}),e),this.position++},r.splitWord=function(e,t){for(var r=this,n=this.nextToken,o=this.content();n&&~[y.dollar,y.caret,y.equals,y.word].indexOf(n[m.FIELDS.TYPE]);){this.position++;var i=this.content();if(o+=i,i.lastIndexOf("\\")===i.length-1){var s=this.nextToken;s&&s[m.FIELDS.TYPE]===y.space&&(o+=this.requiredSpace(this.content(s)),this.position++)}n=this.nextToken}var c=P(o,".").filter(function(e){var t="\\"===o[e-1],r=/^\d+\.\d+%$/.test(o);return!t&&!r}),f=P(o,"#").filter(function(e){return"\\"!==o[e-1]}),p=P(o,"#{");p.length&&(f=f.filter(function(e){return!~p.indexOf(e)}));var h=(0,v.default)(function(){var e=Array.prototype.concat.apply([],arguments);return e.filter(function(t,r){return r===e.indexOf(t)})}([0].concat(c,f)));h.forEach(function(n,i){var s=h[i+1]||o.length,p=o.slice(n,s);if(0===i&&t)return t.call(r,p,h.length);var d,g=r.currToken,v=g[m.FIELDS.START_POS]+h[i],y=_(g[1],g[2]+n,g[3],g[2]+(s-1));if(~c.indexOf(n)){var D={value:p.slice(1),source:y,sourceIndex:v};d=new a.default(A(D,"value"))}else if(~f.indexOf(n)){var b={value:p.slice(1),source:y,sourceIndex:v};d=new u.default(A(b,"value"))}else{var x={value:p,source:y,sourceIndex:v};A(x,"value"),d=new l.default(x)}r.newNode(d,e),e=null}),this.position++},r.word=function(e){var t=this.nextToken;return t&&"|"===this.content(t)?(this.position++,this.namespace()):this.splitWord(e)},r.loop=function(){for(;this.position<this.tokens.length;)this.parse(!0);return this.current._inferEndPosition(),this.root},r.parse=function(e){switch(this.currToken[m.FIELDS.TYPE]){case y.space:this.space();break;case y.comment:this.comment();break;case y.openParenthesis:this.parentheses();break;case y.closeParenthesis:e&&this.missingParenthesis();break;case y.openSquare:this.attribute();break;case y.dollar:case y.caret:case y.equals:case y.word:this.word();break;case y.colon:this.pseudo();break;case y.comma:this.comma();break;case y.asterisk:this.universal();break;case y.ampersand:this.nesting();break;case y.slash:case y.combinator:this.combinator();break;case y.str:this.string();break;case y.closeSquare:this.missingSquareBracket();case y.semicolon:this.missingBackslash();default:this.unexpected()}},r.expected=function(e,t,r){if(Array.isArray(e)){var n=e.pop();e=e.join(", ")+" or "+n}var o=/^[aeiou]/.test(e[0])?"an":"a";return r?this.error("Expected "+o+" "+e+', found "'+r+'" instead.',{index:t}):this.error("Expected "+o+" "+e+".",{index:t})},r.requiredSpace=function(e){return this.options.lossy?" ":e},r.optionalSpace=function(e){return this.options.lossy?"":e},r.lossySpace=function(e,t){return this.options.lossy?t?" ":"":e},r.parseParenthesisToken=function(e){var t=this.content(e);return e[m.FIELDS.TYPE]===y.space?this.requiredSpace(t):t},r.newNode=function(e,t){return t&&(/^ +$/.test(t)&&(this.options.lossy||(this.spaces=(this.spaces||"")+t),t=!0),e.namespace=t,A(e,"namespace")),this.spaces&&(e.spaces.before=this.spaces,this.spaces=""),this.current.append(e)},r.content=function(e){return void 0===e&&(e=this.currToken),this.css.slice(e[m.FIELDS.START_POS],e[m.FIELDS.END_POS])},r.locateNextMeaningfulToken=function(e){void 0===e&&(e=this.position+1);for(var t=e;t<this.tokens.length;)if(!k[this.tokens[t][m.FIELDS.TYPE]])return t;else{t++;continue}return -1},t=[{key:"currToken",get:function(){return this.tokens[this.position]}},{key:"nextToken",get:function(){return this.tokens[this.position+1]}},{key:"prevToken",get:function(){return this.tokens[this.position-1]}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,t),e}(),t.exports=e.default}),ol=nC((e,t)=>{e.__esModule=!0,e.default=void 0;var r=function(e){return e&&e.__esModule?e:{default:e}}(ou());e.default=function(){function e(e,t){this.func=e||function(){},this.funcRes=null,this.options=t}var t=e.prototype;return t._shouldUpdateSelector=function(e,t){return void 0===t&&(t={}),!1!==Object.assign({},this.options,t).updateSelector&&"string"!=typeof e},t._isLossy=function(e){return void 0===e&&(e={}),!1===Object.assign({},this.options,e).lossless},t._root=function(e,t){return void 0===t&&(t={}),new r.default(e,this._parseOptions(t)).root},t._parseOptions=function(e){return{lossy:this._isLossy(e)}},t._run=function(e,t){var r=this;return void 0===t&&(t={}),new Promise(function(n,o){try{var i=r._root(e,t);Promise.resolve(r.func(i)).then(function(n){var o=void 0;return r._shouldUpdateSelector(e,t)&&(e.selector=o=i.toString()),{transform:n,root:i,string:o}}).then(n,o)}catch(e){o(e);return}})},t._runSync=function(e,t){void 0===t&&(t={});var r=this._root(e,t),n=this.func(r);if(n&&"function"==typeof n.then)throw Error("Selector processor returned a promise to a synchronous call.");var o=void 0;return t.updateSelector&&"string"!=typeof e&&(e.selector=o=r.toString()),{transform:n,root:r,string:o}},t.ast=function(e,t){return this._run(e,t).then(function(e){return e.root})},t.astSync=function(e,t){return this._runSync(e,t).root},t.transform=function(e,t){return this._run(e,t).then(function(e){return e.transform})},t.transformSync=function(e,t){return this._runSync(e,t).transform},t.process=function(e,t){return this._run(e,t).then(function(e){return e.string||e.root.toString()})},t.processSync=function(e,t){var r=this._runSync(e,t);return r.string||r.root.toString()},e}(),t.exports=e.default}),oc=nC(e=>{e.__esModule=!0,e.universal=e.tag=e.string=e.selector=e.root=e.pseudo=e.nesting=e.id=e.comment=e.combinator=e.className=e.attribute=void 0;var t=h(ot()),r=h(n3()),n=h(on()),o=h(n4()),i=h(n5()),a=h(oo()),s=h(n9()),u=h(n0()),l=h(n1()),c=h(n7()),f=h(n8()),p=h(or());function h(e){return e&&e.__esModule?e:{default:e}}e.attribute=function(e){return new t.default(e)},e.className=function(e){return new r.default(e)},e.combinator=function(e){return new n.default(e)},e.comment=function(e){return new o.default(e)},e.id=function(e){return new i.default(e)},e.nesting=function(e){return new a.default(e)},e.pseudo=function(e){return new s.default(e)},e.root=function(e){return new u.default(e)},e.selector=function(e){return new l.default(e)},e.string=function(e){return new c.default(e)},e.tag=function(e){return new f.default(e)},e.universal=function(e){return new p.default(e)}}),of=nC(e=>{e.__esModule=!0,e.isNode=o,e.isPseudoElement=l,e.isPseudoClass=function(e){return s(e)&&!l(e)},e.isContainer=function(e){return!!(o(e)&&e.walk)},e.isNamespace=function(e){return a(e)||u(e)},e.isUniversal=e.isTag=e.isString=e.isSelector=e.isRoot=e.isPseudo=e.isNesting=e.isIdentifier=e.isComment=e.isCombinator=e.isClassName=e.isAttribute=void 0;var t,r=nK(),n=((t={})[r.ATTRIBUTE]=!0,t[r.CLASS]=!0,t[r.COMBINATOR]=!0,t[r.COMMENT]=!0,t[r.ID]=!0,t[r.NESTING]=!0,t[r.PSEUDO]=!0,t[r.ROOT]=!0,t[r.SELECTOR]=!0,t[r.STRING]=!0,t[r.TAG]=!0,t[r.UNIVERSAL]=!0,t);function o(e){return"object"==typeof e&&n[e.type]}function i(e,t){return o(t)&&t.type===e}var a=i.bind(null,r.ATTRIBUTE);e.isAttribute=a,e.isClassName=i.bind(null,r.CLASS),e.isCombinator=i.bind(null,r.COMBINATOR),e.isComment=i.bind(null,r.COMMENT),e.isIdentifier=i.bind(null,r.ID),e.isNesting=i.bind(null,r.NESTING);var s=i.bind(null,r.PSEUDO);e.isPseudo=s,e.isRoot=i.bind(null,r.ROOT),e.isSelector=i.bind(null,r.SELECTOR),e.isString=i.bind(null,r.STRING);var u=i.bind(null,r.TAG);function l(e){return s(e)&&e.value&&(e.value.startsWith("::")||":before"===e.value.toLowerCase()||":after"===e.value.toLowerCase()||":first-letter"===e.value.toLowerCase()||":first-line"===e.value.toLowerCase())}e.isTag=u,e.isUniversal=i.bind(null,r.UNIVERSAL)}),op=nC(e=>{e.__esModule=!0;var t=nK();Object.keys(t).forEach(function(r){"default"===r||"__esModule"===r||r in e&&e[r]===t[r]||(e[r]=t[r])});var r=oc();Object.keys(r).forEach(function(t){"default"===t||"__esModule"===t||t in e&&e[t]===r[t]||(e[t]=r[t])});var n=of();Object.keys(n).forEach(function(t){"default"===t||"__esModule"===t||t in e&&e[t]===n[t]||(e[t]=n[t])})}),oh=nC((e,t)=>{e.__esModule=!0,e.default=void 0;var r=function(e){return e&&e.__esModule?e:{default:e}}(ol()),n=function(e){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=o();if(t&&t.has(e))return t.get(e);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)){var a=n?Object.getOwnPropertyDescriptor(e,i):null;a&&(a.get||a.set)?Object.defineProperty(r,i,a):r[i]=e[i]}return r.default=e,t&&t.set(e,r),r}(op());function o(){if("function"!=typeof WeakMap)return null;var e=new WeakMap;return o=function(){return e},e}var i=function(e){return new r.default(e)};Object.assign(i,n),delete i.__esModule,e.default=i,t.exports=e.default}),od=nC(e=>{function t(e){return e.replace(/\\,/g,"\\2c ")}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:()=>t})}),og=nC((e,t)=>{t.exports={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}}),ov=nC(e=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(e,{parseColor:()=>c,formatColor:()=>f});var t=function(e){return e&&e.__esModule?e:{default:e}}(og()),r=/^#([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})?$/i,n=/^#([a-f\d])([a-f\d])([a-f\d])([a-f\d])?$/i,o=/(?:\d+|\d*\.\d+)%?/,i=/(?:\s*,\s*|\s+)/,a=/\s*[,/]\s*/,s=/var\(--(?:[^ )]*?)\)/,u=RegExp(`^(rgb)a?\\(\\s*(${o.source}|${s.source})(?:${i.source}(${o.source}|${s.source}))?(?:${i.source}(${o.source}|${s.source}))?(?:${a.source}(${o.source}|${s.source}))?\\s*\\)$`),l=RegExp(`^(hsl)a?\\(\\s*((?:${o.source})(?:deg|rad|grad|turn)?|${s.source})(?:${i.source}(${o.source}|${s.source}))?(?:${i.source}(${o.source}|${s.source}))?(?:${a.source}(${o.source}|${s.source}))?\\s*\\)$`);function c(e,{loose:o=!1}={}){var i,a,s;if("string"!=typeof e)return null;if("transparent"===(e=e.trim()))return{mode:"rgb",color:["0","0","0"],alpha:"0"};if(e in t.default)return{mode:"rgb",color:t.default[e].map(e=>e.toString())};let f=e.replace(n,(e,t,r,n,o)=>["#",t,t,r,r,n,n,o?o+o:""].join("")).match(r);if(null!==f)return{mode:"rgb",color:[parseInt(f[1],16),parseInt(f[2],16),parseInt(f[3],16)].map(e=>e.toString()),alpha:f[4]?(parseInt(f[4],16)/255).toString():void 0};let p=null!=(s=e.match(u))?s:e.match(l);if(null===p)return null;let h=[p[2],p[3],p[4]].filter(Boolean).map(e=>e.toString());return(o||3===h.length)&&(!(h.length<3)||h.some(e=>/^var\(.*?\)$/.test(e)))?{mode:p[1],color:h,alpha:null==(i=p[5])||null==(a=i.toString)?void 0:a.call(i)}:null}function f({mode:e,color:t,alpha:r}){return`${e}(${t.join(" ")}${void 0!==r?` / ${r}`:""})`}}),om=nC(e=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(e,{withAlphaValue:()=>r,default:()=>n});var t=ov();function r(e,r,n){if("function"==typeof e)return e({opacityValue:r});let o=(0,t.parseColor)(e,{loose:!0});return null===o?n:(0,t.formatColor)({...o,alpha:r})}function n({color:e,property:r,variable:n}){let o=[].concat(r);if("function"==typeof e)return{[n]:"1",...Object.fromEntries(o.map(t=>[t,e({opacityVariable:n,opacityValue:`var(${n})`})]))};let i=(0,t.parseColor)(e);return null===i?Object.fromEntries(o.map(t=>[t,e])):void 0!==i.alpha?Object.fromEntries(o.map(t=>[t,e])):{[n]:"1",...Object.fromEntries(o.map(e=>[e,(0,t.formatColor)({...i,alpha:`var(${n})`})]))}}}),oy=nC(e=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(e,{pattern:()=>o,withoutCapturing:()=>i,any:()=>a,optional:()=>s,zeroOrMore:()=>u,nestedBrackets:()=>function e(t,r,n=1){return i([l(t),/[^\s]*/,1===n?`[^${l(t)}${l(r)}s]*`:a([`[^${l(t)}${l(r)}s]*`,e(t,r,n-1)]),/[^\s]*/,l(r)])},escape:()=>l});var t=/[\\^$.*+?()[\]{}|]/g,r=RegExp(t.source);function n(e){return(e=(e=Array.isArray(e)?e:[e]).map(e=>e instanceof RegExp?e.source:e)).join("")}function o(e){return RegExp(n(e),"g")}function i(e){return RegExp(`(?:${n(e)})`,"g")}function a(e){return`(?:${e.map(n).join("|")})`}function s(e){return`(?:${n(e)})?`}function u(e){return`(?:${n(e)})*`}function l(e){return e&&r.test(e)?e.replace(t,"\\$&"):e||""}}),oD=nC(e=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"splitAtTopLevelOnly",{enumerable:!0,get:()=>n});var t=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var n=r(t);if(n&&n.has(e))return n.get(e);var o={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var s=i?Object.getOwnPropertyDescriptor(e,a):null;s&&(s.get||s.set)?Object.defineProperty(o,a,s):o[a]=e[a]}return o.default=e,n&&n.set(e,o),o}(oy());function r(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(r=function(e){return e?n:t})(e)}function*n(e,r){let n=RegExp(`[(){}\\[\\]${t.escape(r)}]`,"g"),o=0,i=0,a=!1,s=0,u=0,l=r.length;for(let t of e.matchAll(n)){let n=t[0]===r[s],c=s===l-1,f=n&&c;"("===t[0]&&o++,")"===t[0]&&o--,"["===t[0]&&o++,"]"===t[0]&&o--,"{"===t[0]&&o++,"}"===t[0]&&o--,n&&0===o&&(0===u&&(u=t.index),s++),f&&0===o&&(a=!0,yield e.substring(i,u),i=u+l),s===l&&(s=0,u=0)}a?yield e.substring(i):yield e}}),ob=nC(e=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(e,{parseBoxShadowValue:()=>i,formatBoxShadowValue:()=>a});var t=oD(),r=new Set(["inset","inherit","initial","revert","unset"]),n=/\ +(?![^(]*\))/g,o=/^-?(\d+|\.\d+)(.*?)$/g;function i(e){return Array.from((0,t.splitAtTopLevelOnly)(e,",")).map(e=>{let t=e.trim(),i={raw:t},a=t.split(n),s=new Set;for(let e of a)o.lastIndex=0,!s.has("KEYWORD")&&r.has(e)?(i.keyword=e,s.add("KEYWORD")):o.test(e)?s.has("X")?s.has("Y")?s.has("BLUR")?s.has("SPREAD")||(i.spread=e,s.add("SPREAD")):(i.blur=e,s.add("BLUR")):(i.y=e,s.add("Y")):(i.x=e,s.add("X")):i.color?(i.unknown||(i.unknown=[]),i.unknown.push(e)):i.color=e;return i.valid=void 0!==i.x&&void 0!==i.y,i})}function a(e){return e.map(e=>e.valid?[e.keyword,e.x,e.y,e.blur,e.spread,e.color].filter(Boolean).join(" "):e.raw).join(", ")}}),ox=nC(e=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(e,{normalize:()=>a,url:()=>s,number:()=>u,percentage:()=>l,length:()=>f,lineWidth:()=>h,shadow:()=>d,color:()=>g,image:()=>v,gradient:()=>y,position:()=>b,familyName:()=>x,genericName:()=>E,absoluteSize:()=>k,relativeSize:()=>C});var t=ov(),r=ob(),n=["min","max","clamp","calc"],o=/,(?![^(]*\))/g,i=/_(?![^(]*\))/g;function a(e,t=!0){return e.includes("url(")?e.split(/(url\(.*?\))/g).filter(Boolean).map(e=>/^url\(.*?\)$/.test(e)?e:a(e,!1)).join(""):(e=e.replace(/([^\\])_+/g,(e,t)=>t+" ".repeat(e.length-1)).replace(/^_/g," ").replace(/\\_/g,"_"),t&&(e=e.trim()),e=e.replace(/(calc|min|max|clamp)\(.+\)/g,e=>e.replace(/(-?\d*\.?\d(?!\b-.+[,)](?![^+\-/*])\D)(?:%|[a-z]+)?|\))([+\-/*])/g,"$1 $2 ")))}function s(e){return e.startsWith("url(")}function u(e){return!isNaN(Number(e))||n.some(t=>RegExp(`^${t}\\(.+?`).test(e))}function l(e){return e.split(i).every(e=>/%$/g.test(e)||n.some(t=>RegExp(`^${t}\\(.+?%`).test(e)))}var c="(?:cm|mm|Q|in|pc|pt|px|em|ex|ch|rem|lh|vw|vh|vmin|vmax)";function f(e){return e.split(i).every(e=>"0"===e||RegExp(`${c}$`).test(e)||n.some(t=>RegExp(`^${t}\\(.+?${c}`).test(e)))}var p=new Set(["thin","medium","thick"]);function h(e){return p.has(e)}function d(e){for(let t of(0,r.parseBoxShadowValue)(a(e)))if(!t.valid)return!1;return!0}function g(e){let r=0;return!!e.split(i).every(e=>!!(e=a(e)).startsWith("var(")||null!==(0,t.parseColor)(e,{loose:!0})&&(r++,!0))&&r>0}function v(e){let t=0;return!!e.split(o).every(e=>!!(e=a(e)).startsWith("var(")||!!(s(e)||y(e)||["element(","image(","cross-fade(","image-set("].some(t=>e.startsWith(t)))&&(t++,!0))&&t>0}var m=new Set(["linear-gradient","radial-gradient","repeating-linear-gradient","repeating-radial-gradient","conic-gradient"]);function y(e){for(let t of(e=a(e),m))if(e.startsWith(`${t}(`))return!0;return!1}var D=new Set(["center","top","right","bottom","left"]);function b(e){let t=0;return!!e.split(i).every(e=>!!(e=a(e)).startsWith("var(")||!!(D.has(e)||f(e)||l(e))&&(t++,!0))&&t>0}function x(e){let t=0;return!!e.split(o).every(e=>!!(e=a(e)).startsWith("var(")||!(e.includes(" ")&&!/(['"])([^"']+)\1/g.test(e)||/^\d/g.test(e))&&(t++,!0))&&t>0}var w=new Set(["serif","sans-serif","monospace","cursive","fantasy","system-ui","ui-serif","ui-sans-serif","ui-monospace","ui-rounded","math","emoji","fangsong"]);function E(e){return w.has(e)}var S=new Set(["xx-small","x-small","small","medium","large","x-large","x-large","xxx-large"]);function k(e){return S.has(e)}var F=new Set(["larger","smaller"]);function C(e){return F.has(e)}}),ow=nC(e=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(e,{updateAllClasses:()=>s,asValue:()=>l,parseColorFormat:()=>f,asColor:()=>p,asLookupValue:()=>h,coerceValue:()=>m});var t=a(oh()),r=a(od()),n=om(),o=ox(),i=a(nI());function a(e){return e&&e.__esModule?e:{default:e}}function s(e,n){return(0,t.default)(e=>{e.walkClasses(e=>{let t=n(e.value);e.value=t,e.raws&&e.raws.value&&(e.raws.value=(0,r.default)(e.raws.value))})}).processSync(e)}function u(e,t){if(!c(e))return;let r=e.slice(1,-1);if(t(r))return(0,o.normalize)(r)}function l(e,t={},{validate:r=()=>!0}={}){var n;let o=null==(n=t.values)?void 0:n[e];return void 0!==o?o:t.supportsNegativeValues&&e.startsWith("-")?function(e,t={},r){let n=t[e];if(void 0!==n)return(0,i.default)(n);if(c(e)){let t=u(e,r);return void 0===t?void 0:(0,i.default)(t)}}(e.slice(1),t.values,r):u(e,r)}function c(e){return e.startsWith("[")&&e.endsWith("]")}function f(e){return"string"==typeof e&&e.includes("<alpha-value>")?({opacityValue:t=1})=>e.replace("<alpha-value>",t):e}function p(e,t={},{tailwindConfig:r={}}={}){var i,a,s,u,h,d;let g;if((null==(i=t.values)?void 0:i[e])!==void 0)return f(null==(a=t.values)?void 0:a[e]);let[v,m]=-1===(g=e.lastIndexOf("/"))||g===e.length-1?[e]:[e.slice(0,g),e.slice(g+1)];if(void 0!==m){let e=null!=(d=null==(s=t.values)?void 0:s[v])?d:c(v)?v.slice(1,-1):void 0;return void 0===e?void 0:(e=f(e),c(m)?(0,n.withAlphaValue)(e,m.slice(1,-1)):(null==(u=r.theme)||null==(h=u.opacity)?void 0:h[m])===void 0?void 0:(0,n.withAlphaValue)(e,r.theme.opacity[m]))}return l(e,t,{validate:o.color})}function h(e,t={}){var r;return null==(r=t.values)?void 0:r[e]}function d(e){return(t,r)=>l(t,r,{validate:e})}var g={any:l,color:p,url:d(o.url),image:d(o.image),length:d(o.length),percentage:d(o.percentage),position:d(o.position),lookup:h,"generic-name":d(o.genericName),"family-name":d(o.familyName),number:d(o.number),"line-width":d(o.lineWidth),"absolute-size":d(o.absoluteSize),"relative-size":d(o.relativeSize),shadow:d(o.shadow)},v=Object.keys(g);function m(e,t,r,n){if(c(t)){let e,n=t.slice(1,-1),[o,i]=-1===(e=n.indexOf(":"))?[void 0,n]:[n.slice(0,e),n.slice(e+1)];if(/^[\w-_]+$/g.test(o)){if(void 0!==o&&!v.includes(o))return[]}else i=n;if(i.length>0&&v.includes(o))return[l(`[${i}]`,r),o]}for(let o of[].concat(e)){let e=g[o](t,r,{tailwindConfig:n});if(void 0!==e)return[e,o]}return[]}}),oE=nC(e=>{function t(e){return"function"==typeof e?e({}):e}Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:()=>t})}),oS=nC(e=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:()=>b});var t=d(nI()),r=d(nB()),n=d(nR()),o=d(nU()),i=d(nj()),a=nW(),s=nG(),u=n$(),l=d(nz()),c=nq(),f=ow(),p=om(),h=d(oE());function d(e){return e&&e.__esModule?e:{default:e}}function g(e){return"function"==typeof e}function v(e){return"object"==typeof e&&null!==e}function m(e,...t){let r=t.pop();for(let n of t)for(let t in n){let o=r(e[t],n[t]);void 0===o?v(e[t])&&v(n[t])?e[t]=m(e[t],n[t],r):e[t]=n[t]:e[t]=o}return e}var y={colors:i.default,negative:e=>Object.keys(e).filter(t=>"0"!==e[t]).reduce((r,n)=>{let o=(0,t.default)(e[n]);return void 0!==o&&(r[`-${n}`]=o),r},{}),breakpoints:e=>Object.keys(e).filter(t=>"string"==typeof e[t]).reduce((t,r)=>({...t,[`screen-${r}`]:e[r]}),{})};function D(e,t){return Array.isArray(e)&&v(e[0])?e.concat(t):Array.isArray(t)&&v(t[0])&&v(e)?[e,...t]:Array.isArray(t)?t:void 0}function b(e){var t,i,d,v;let b,x=[...function e(t){let r=[];return t.forEach(t=>{var n;r=[...r,t];let o=null!=(n=null==t?void 0:t.plugins)?n:[];0!==o.length&&o.forEach(t=>{var n;t.__isOptionsFunction&&(t=t()),r=[...r,...e([null!=(n=null==t?void 0:t.config)?n:{}])]})}),r}(e),{prefix:"",important:!1,separator:":",variantOrder:o.default.variantOrder}];return(0,u.normalizeConfig)((0,a.defaults)({theme:(d=function({extend:e,...t}){return m(t,e,(e,t)=>g(e)||t.some(g)?(r,n)=>m({},...[e,...t].map(e=>(function(e,...t){return g(e)?e(...t):e})(e,r,n)),D):m({},e,...t,D))}({...(v=x.map(e=>null!=(t=null==e?void 0:e.theme)?t:{})).reduce((e,t)=>(0,a.defaults)(e,t),{}),extend:v.reduce((e,{extend:t})=>m(e,t,(e,t)=>void 0===e?[t]:Array.isArray(e)?[t,...e]:[t,e]),{})}),Object.assign(b=(e,t)=>{for(let t of function*(e){let t=(0,s.toPath)(e);if(0===t.length||(yield t,Array.isArray(e)))return;let r=e.match(/^(.*?)\s*\/\s*([^/]+)$/);if(null!==r){let[,e,t]=r,n=(0,s.toPath)(e);n.alpha=t,yield n}}(e)){let e=0,r=d;for(;null!=r&&e<t.length;)r=g(r=r[t[e++]])&&(void 0===t.alpha||e<=t.length-1)?r(b,y):r;if(void 0!==r){if(void 0!==t.alpha){let e=(0,f.parseColorFormat)(r);return(0,p.withAlphaValue)(e,t.alpha,(0,h.default)(e))}return(0,l.default)(r)?(0,c.cloneDeep)(r):r}}return t},{theme:b,...y}),Object.keys(d).reduce((e,t)=>(e[t]=g(d[t])?d[t](b,y):d[t],e),{})),corePlugins:[...x.map(e=>e.corePlugins)].reduceRight((e,t)=>g(t)?t({corePlugins:e}):(0,n.default)(t,e),r.default),plugins:[...e.map(e=>null!=(i=null==e?void 0:e.plugins)?i:[])].reduceRight((e,t)=>[...e,...t],[])},...x))}}),ok={};n_(ok,{default:()=>c});var oF=nF(()=>{c={yellow:e=>e}}),oC=nC(e=>{Object.defineProperty(e,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(e,{flagEnabled:()=>a,issueFlagNotices:()=>u,default:()=>l});var t=n((oF(),nO(ok))),r=n((nN(),nO(nM)));function n(e){return e&&e.__esModule?e:{default:e}}var o={optimizeUniversalDefaults:!1},i={future:["hoverOnlyWhenSupported","respectDefaultRingColorOpacity"],experimental:["optimizeUniversalDefaults","matchVariant"]};function a(e,t){var r,n,a,s,u,l;return i.future.includes(t)?"all"===e.future||null!=(a=null!=(n=null==e||null==(r=e.future)?void 0:r[t])?n:o[t])&&a:!!i.experimental.includes(t)&&("all"===e.experimental||null!=(l=null!=(u=null==e||null==(s=e.experimental)?void 0:s[t])?u:o[t])&&l)}function s(e){var t;return"all"===e.experimental?i.experimental:Object.keys(null!=(t=null==e?void 0:e.experimental)?t:{}).filter(t=>i.experimental.includes(t)&&e.experimental[t])}function u(e){if(void 0===process.env.JEST_WORKER_ID&&s(e).length>0){let n=s(e).map(e=>t.default.yellow(e)).join(", ");r.default.warn("experimental-flags-enabled",[`You have enabled experimental features: ${n}`,"Experimental features in Tailwind CSS are not covered by semver, may introduce breaking changes, and can change at any time."])}}var l=i}),o_=nC(e=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:()=>function e(n){var o;let i=(null!=(o=null==n?void 0:n.presets)?o:[t.default]).slice().reverse().flatMap(t=>e("function"==typeof t?t():t)),a={respectDefaultRingColorOpacity:{theme:{ringColor:{DEFAULT:"#3b82f67f"}}}},s=Object.keys(a).filter(e=>(0,r.flagEnabled)(n,e)).map(e=>a[e]);return[n,...s,...i]}});var t=function(e){return e&&e.__esModule?e:{default:e}}(nU()),r=oC()}),oT=nC(e=>{Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"default",{enumerable:!0,get:()=>o});var t=n(oS()),r=n(o_());function n(e){return e&&e.__esModule?e:{default:e}}function o(...e){let[,...n]=(0,r.default)(e[0]);return(0,t.default)([...e,...n])}}),oO=nC((e,t)=>{var r=oT();t.exports=(r.__esModule?r:{default:r}).default}),oA=null;async function oP(){return f||(oA?await oA:(oA=Promise.resolve().then(()=>(nL(),nA)).then(e=>e.getYogaModule()).then(e=>f=e),await oA,oA=null),f)}var oL=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),oI=oL((e,t)=>{t.exports=["em","ex","ch","rem","vh","vw","vmin","vmax","px","mm","cm","in","pt","pc","mozmm"]}),oB=oL((e,t)=>{t.exports=["deg","grad","rad","turn"]}),oR=oL((e,t)=>{t.exports=["dpi","dpcm","dppx"]}),oU=oL((e,t)=>{t.exports=["Hz","kHz"]}),oM=oL((e,t)=>{t.exports=["s","ms"]}),oN=oI(),oj=oB(),oW=oR(),oG=oU(),o$=oM();function oz(e){if(/\.\D?$/.test(e))throw Error("The dot should be followed by a number");if(/^[+-]{2}/.test(e))throw Error("Only one leading +/- is allowed");if(((t=e.match(/\./g))?t.length:0)>1)throw Error("Only one dot is allowed");if(/%$/.test(e)){this.type="percentage",this.value=oV(e),this.unit="%";return}var t,r=function(e){var t=e.match(/\D+$/),r=t&&t[0];if(r&&-1===oH.indexOf(r))throw Error("Invalid unit: "+r);return r}(e);if(!r){this.type="number",this.value=oV(e);return}this.type=oX[r]||"length",this.value=oV(e.substr(0,e.length-r.length)),this.unit=r}function oq(e){return new oz(e)}function oV(e){var t=parseFloat(e);if(isNaN(t))throw Error("Invalid number: "+e);return t}oz.prototype.valueOf=function(){return this.value},oz.prototype.toString=function(){return this.value+(this.unit||"")};var oH=[].concat(oj,oG,oN,oW,o$),oX=Object.assign(oY(oj,"angle"),oY(oG,"frequency"),oY(oW,"resolution"),oY(o$,"time"));function oY(e,t){return Object.fromEntries(e.map(e=>[e,t]))}function oZ(e){let t=typeof e;return"number"!==t&&"bigint"!==t&&"string"!==t&&"boolean"!==t}function oQ(e,t,r,n,o=!1){if("number"==typeof e)return e;try{if(e=e.trim(),/[ /\(,]/.test(e))return;if(e===String(+e))return+e;let i=new oq(e);if("length"===i.type)switch(i.unit){case"em":return i.value*t;case"rem":return 16*i.value;case"vw":return~~(i.value*n._viewportWidth/100);case"vh":return~~(i.value*n._viewportHeight/100);default:return i.value}if("angle"===i.type)return oK(e);if("percentage"===i.type&&o)return i.value/100*r}catch{}}function oK(e){let t=new oq(e);switch(t.unit){case"deg":return t.value;case"rad":return 180*t.value/Math.PI;case"turn":return 360*t.value;case"grad":return .9*t.value}}function oJ(e,t){return[e[0]*t[0]+e[2]*t[1],e[1]*t[0]+e[3]*t[1],e[0]*t[2]+e[2]*t[3],e[1]*t[2]+e[3]*t[3],e[0]*t[4]+e[2]*t[5]+e[4],e[1]*t[4]+e[3]*t[5]+e[5]]}function o0(e,t,r,n){let o=t[e];if(typeof o>"u"){if(n&&"u">typeof e)throw Error(`Invalid value for CSS property "${n}". Allowed values: ${Object.keys(t).map(e=>`"${e}"`).join(" | ")}. Received: "${e}".`);o=r}return o}var o1=[32,160,4961,65792,65793,4153,4241,10].map(e=>String.fromCodePoint(e));function o2(e,t,r){if(!p||!h){if(!("u">typeof Intl&&"Segmenter"in Intl))throw Error("Intl.Segmenter does not exist, please use import a polyfill.");p=new Intl.Segmenter(r,{granularity:"word"}),h=new Intl.Segmenter(r,{granularity:"grapheme"})}if("grapheme"===t)return[...h.segment(e)].map(e=>e.segment);{let t=[...p.segment(e)].map(e=>e.segment),r=[],n=0;for(;n<t.length;){let e=t[n];if("\xa0"==e){let e=0===n?"":r.pop(),o=n===t.length-1?"":t[n+1];r.push(e+"\xa0"+o),n+=2}else r.push(e),n++}return r}}function o3(e,t,r){let n="";for(let[e,r]of Object.entries(t))"u">typeof r&&(n+=` ${e}="${r}"`);return r?`<${e}${n}>${r}</${e}>`:`<${e}${n}/>`}function o4(e){return e?e.split(/[, ]/).filter(Boolean).map(Number):null}function o5(e){return"string"==typeof e}var o6=e=>e.replaceAll(/([A-Z])/g,(e,t)=>`-${t.toLowerCase()}`);function o8(e,t=","){let r=[],n=0,o=0;t=new RegExp(t);for(let i=0;i<e.length;i++)"("===e[i]?o++:")"===e[i]&&o--,0===o&&t.test(e[i])&&(r.push(e.slice(n,i).trim()),n=i+1);return r.push(e.slice(n).trim()),r}var o7="image/apng",o9="image/png",ie="image/jpeg",it="image/gif",ir="image/svg+xml";function io(e){let t=new DataView(e),r=4,n=t.byteLength;for(;r<n;){let e=t.getUint16(r,!1);if(e>n)break;let o=t.getUint8(e+1+r);if(192===o||193===o||194===o)return[t.getUint16(e+7+r,!1),t.getUint16(e+5+r,!1)];r+=e+2}throw TypeError("Invalid JPEG")}function ii(e){let t=new Uint8Array(e.slice(6,10));return[t[0]|t[1]<<8,t[2]|t[3]<<8]}function ia(e){let t=new DataView(e);return[t.getUint16(18,!1),t.getUint16(22,!1)]}var is=function(e=20){let t=new Map;return{set:function(r,n){if(t.size>=e){let e=t.keys().next().value;t.delete(e)}t.set(r,n)},get:function(e){if(!t.has(e))return;let r=t.get(e);return t.delete(e),t.set(e,r),r},clear:function(){t.clear()}}}(100),iu=new Map,il=[o9,o7,ie,it,ir];function ic(e,t){let r=t.match(/<svg[^>]*>/)[0],n=r.match(/viewBox=['"](.+)['"]/),o=n?o4(n[1]):null,i=r.match(/width=['"](\d*\.\d+|\d+)['"]/),a=r.match(/height=['"](\d*\.\d+|\d+)['"]/);if(!o&&(!i||!a))throw Error(`Failed to parse SVG from ${e}: missing "viewBox"`);let s=o?[o[2],o[3]]:[+i[1],+a[1]],u=s[0]/s[1];return i&&a?[+i[1],+a[1]]:i?[+i[1],i[1]/u]:a?[a[1]*u,+a[1]]:[s[0],s[1]]}function ip(e){var t;let r,n=(t=new Uint8Array(e),[255,216,255].every((e,r)=>t[r]===e)?ie:[137,80,78,71,13,10,26,10].every((e,r)=>t[r]===e)?!function(e){let t=new DataView(e.buffer),r,n,o=8,i=!1;for(;!i&&"IEND"!==r&&o<e.length;)n=t.getUint32(o),i="acTL"===(r=String.fromCharCode(...e.subarray(o+4,o+8))),o+=12+n;return i}(t)?o9:o7:[71,73,70,56].every((e,r)=>t[r]===e)?it:[82,73,70,70,0,0,0,0,87,69,66,80].every((e,r)=>!e||t[r]===e)?"image/webp":[60,63,120,109,108].every((e,r)=>t[r]===e)?ir:[0,0,0,0,102,116,121,112,97,118,105,102].every((e,r)=>!e||t[r]===e)?"image/avif":null);switch(n){case o9:case o7:r=ia(e);break;case it:r=ii(e);break;case ie:r=io(e)}if(!il.includes(n))throw Error(`Unsupported image type: ${n||"unknown"}`);return[`data:${n};base64,${function(e){let t="",r=new Uint8Array(e);for(let e=0;e<r.byteLength;e++)t+=String.fromCharCode(r[e]);return btoa(t)}(e)}`,r]}async function ih(e){if(!e)throw Error("Image source is not provided.");if("object"==typeof e){let[t,r]=ip(e);return[t,...r]}if((e.startsWith('"')&&e.endsWith('"')||e.startsWith("'")&&e.endsWith("'"))&&(e=e.slice(1,-1)),typeof window>"u"&&!e.startsWith("http")&&!e.startsWith("data:"))throw Error(`Image source must be an absolute URL: ${e}`);if(e.startsWith("data:")){let t;try{t=/data:(?<imageType>[a-z/+]+)(;[^;=]+=[^;=]+)*?(;(?<encodingType>[^;,]+))?,(?<dataString>.*)/g.exec(e).groups}catch{return console.warn("Image data URI resolved without size:"+e),[e]}let{imageType:r,encodingType:n,dataString:o}=t;if(r===ir){let t="base64"===n?atob(o):decodeURIComponent(o.replace(/ /g,"%20")),r="base64"===n?e:`data:image/svg+xml;base64,${btoa(t)}`,i=ic(e,t);return is.set(e,[r,...i]),[r,...i]}if("base64"!==n)return console.warn("Image data URI resolved without size:"+e),is.set(e,[e]),[e];{let t,n=function(e){let t=atob(e),r=t.length,n=new Uint8Array(r);for(let e=0;e<r;e++)n[e]=t.charCodeAt(e);return n.buffer}(o);switch(r){case o9:case o7:t=ia(n);break;case it:t=ii(n);break;case ie:t=io(n)}return is.set(e,[e,...t]),[e,...t]}}if(!globalThis.fetch)throw Error("`fetch` is required to be polyfilled to load images.");if(iu.has(e))return iu.get(e);let t=is.get(e);if(t)return t;let r=e,n=fetch(r).then(e=>{let t=e.headers.get("content-type");return"image/svg+xml"===t||"application/svg+xml"===t?e.text():e.arrayBuffer()}).then(e=>{if("string"==typeof e)try{let t=`data:image/svg+xml;base64,${btoa(e)}`,n=ic(r,e);return[t,...n]}catch(e){throw Error(`Failed to parse SVG image: ${e.message}`)}let[t,n]=ip(e);return[t,...n]}).then(e=>(is.set(r,e),e)).catch(e=>(console.error(`Can't load image ${r}: `+e.message),is.set(r,[]),[]));return iu.set(r,n),n}var id={accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",href:"href",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDasharray:"stroke-dasharray",strokeDashoffset:"stroke-dashoffset",strokeLinecap:"stroke-linecap",strokeLinejoin:"stroke-linejoin",strokeMiterlimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",xlinkActuate:"xlink:actuate",xlinkArcrole:"xlink:arcrole",xlinkHref:"xlink:href",xlinkRole:"xlink:role",xlinkShow:"xlink:show",xlinkTitle:"xlink:title",xlinkType:"xlink:type",xmlBase:"xml:base",xmlLang:"xml:lang",xmlSpace:"xml:space",xmlnsXlink:"xmlns:xlink"},ig=/[\r\n%#()<>?[\\\]^`{|}"']/g;async function iv(e){let t=new Set,r=e=>{if(e&&oZ(e)){if(Array.isArray(e))return void e.forEach(e=>r(e));"object"==typeof e&&("image"===e.type?t.has(e.props.href)||t.add(e.props.href):"img"===e.type&&(t.has(e.props.src)||t.add(e.props.src))),Array.isArray(e.props.children)?e.props.children.map(e=>r(e)):r(e.props.children)}};return r(e),Promise.all(Array.from(t).map(e=>ih(e)))}async function im(e,t){let{viewBox:r,viewbox:n,width:o,height:i,className:a,style:s,children:u,...l}=e.props||{};r||(r=n),l.xmlns="http://www.w3.org/2000/svg";let c=(null==s?void 0:s.color)||t,f=o4(r),p=f?f[3]/f[2]:null;return o=o||p&&i?i/p:null,i=i||p&&o?o*p:null,l.width=o,l.height=i,r&&(l.viewBox=r),`data:image/svg+xml;utf8,${`<svg ${Object.entries(l).map(([e,t])=>("string"==typeof t&&"currentcolor"===t.toLowerCase()&&(t=c),` ${id[e]||e}="${t}"`)).join("")}>${(function e(t,r){if(!t)return"";if(Array.isArray(t))return t.map(t=>e(t,r)).join("");if("object"!=typeof t)return String(t);let n=t.type;if("text"===n)throw Error("<text> nodes are not currently supported, please convert them to <path>");let{children:o,style:i,...a}=t.props||{},s=(null==i?void 0:i.color)||r,u=`${Object.entries(a).map(([e,t])=>("string"==typeof t&&"currentcolor"===t.toLowerCase()&&(t=s),"href"===e&&"image"===n?` ${id[e]||e}="${is.get(t)[0]}"`:` ${id[e]||e}="${t}"`)).join("")}`,l=i?` style="${Object.entries(i).map(([e,t])=>`${o6(e)}:${t}`).join(";")}"`:"";return`<${n}${u}${l}>${e(o,s)}</${n}>`})(u,c)}</svg>`.replace(ig,encodeURIComponent)}`}var iy="flex",iD={p:{display:iy,marginTop:"1em",marginBottom:"1em"},div:{display:iy},blockquote:{display:iy,marginTop:"1em",marginBottom:"1em",marginLeft:40,marginRight:40},center:{display:iy,textAlign:"center"},hr:{display:iy,marginTop:"0.5em",marginBottom:"0.5em",marginLeft:"auto",marginRight:"auto",borderWidth:1,borderStyle:"solid"},h1:{display:iy,fontSize:"2em",marginTop:"0.67em",marginBottom:"0.67em",marginLeft:0,marginRight:0,fontWeight:"bold"},h2:{display:iy,fontSize:"1.5em",marginTop:"0.83em",marginBottom:"0.83em",marginLeft:0,marginRight:0,fontWeight:"bold"},h3:{display:iy,fontSize:"1.17em",marginTop:"1em",marginBottom:"1em",marginLeft:0,marginRight:0,fontWeight:"bold"},h4:{display:iy,marginTop:"1.33em",marginBottom:"1.33em",marginLeft:0,marginRight:0,fontWeight:"bold"},h5:{display:iy,fontSize:"0.83em",marginTop:"1.67em",marginBottom:"1.67em",marginLeft:0,marginRight:0,fontWeight:"bold"},h6:{display:iy,fontSize:"0.67em",marginTop:"2.33em",marginBottom:"2.33em",marginLeft:0,marginRight:0,fontWeight:"bold"},u:{textDecoration:"underline"},strong:{fontWeight:"bold"},b:{fontWeight:"bold"},i:{fontStyle:"italic"},em:{fontStyle:"italic"},code:{fontFamily:"monospace"},kbd:{fontFamily:"monospace"},pre:{display:iy,fontFamily:"monospace",whiteSpace:"pre",marginTop:"1em",marginBottom:"1em"},mark:{backgroundColor:"yellow",color:"black"},big:{fontSize:"larger"},small:{fontSize:"smaller"},s:{textDecoration:"line-through"}},ib=new Set(["color","font","fontFamily","fontSize","fontStyle","fontWeight","letterSpacing","lineHeight","textAlign","textTransform","textShadowOffset","textShadowColor","textShadowRadius","WebkitTextStrokeWidth","WebkitTextStrokeColor","textDecorationLine","textDecorationStyle","textDecorationColor","whiteSpace","transform","wordBreak","tabSize","opacity","filter","_viewportWidth","_viewportHeight","_inheritedClipPathId","_inheritedMaskId","_inheritedBackgroundClipTextPath"]);function ix(e,t,r){switch(e){case"top":return{yRelative:0};case"left":return{xRelative:0};case"right":return{xRelative:100};case"bottom":return{yRelative:100};case"center":return{};default:{let n=function(e,t){try{let r=new oq(e);switch(r.unit){case"px":return{absolute:r.value};case"em":return{absolute:r.value*t};case"rem":return{absolute:16*r.value};case"%":return{relative:r.value};default:return{}}}catch{return{}}}(e,t);return n.absolute?{[r?"xAbsolute":"yAbsolute"]:n.absolute}:n.relative?{[r?"xRelative":"yRelative"]:n.relative}:{}}}}function iw(e,t){let r=(0,e_.getPropertyName)(`mask-${t}`);return e[r]||e[`WebkitM${r.substring(1)}`]}var iE=new Set(["flex","flexGrow","flexShrink","flexBasis","fontWeight","lineHeight","opacity","scale","scaleX","scaleY"]),iS=new Set(["lineHeight"]);function ik(e,t){let r=Number(t);return isNaN(r)?t:iE.has(e)?iS.has(e)?r:String(t):r+"px"}function iF(e){return"transform"===e?" Only absolute lengths such as `10px` are supported.":""}var iC=/rgb\((\d+)\s+(\d+)\s+(\d+)\s*\/\s*([\.\d]+)\)/;function i_(e,t){var r,n,o;let i,a,s={};if(e){let i=(n=e.color,o=t.color,n&&"currentcolor"!==n.toLowerCase()?iT(n):iT(o));for(let t in s.color=i,e){if(t.startsWith("_")){s[t]=e[t];continue}if("color"===t)continue;let n=(0,eo.getPropertyName)(t),o=function(e,t){return o5(e)&&(e=e.replace(/currentcolor/gi,t)),e}(e[t],i);try{let e=function(e,t,r){if("lineHeight"===e)return{lineHeight:ik(e,t)};if("fontFamily"===e)return{fontFamily:t.split(",").map(e=>e.trim().replace(/(^['"])|(['"]$)/g,"").toLocaleLowerCase())};if("borderRadius"===e){if("string"!=typeof t||!t.includes("/"))return;let[r,n]=t.split("/"),o=(0,eo.getStylesForProperty)(e,r,!0),i=(0,eo.getStylesForProperty)(e,n,!0);for(let t in o)i[t]=ik(e,o[t])+" "+ik(e,i[t]);return i}if(/^border(Top|Right|Bottom|Left)?$/.test(e)){let n=(0,eo.getStylesForProperty)("border",t,!0);1!==n.borderWidth||String(t).includes("1px")||(n.borderWidth=3),"black"!==n.borderColor||String(t).includes("black")||(n.borderColor=r);let o={Width:ik(e+"Width",n.borderWidth),Style:o0(n.borderStyle,{solid:"solid",dashed:"dashed"},"solid",e+"Style"),Color:n.borderColor},i={};for(let t of"border"===e?["Top","Right","Bottom","Left"]:[e.slice(6)])for(let e in o)i["border"+t+e]=o[e];return i}if("boxShadow"===e){if(!t)throw Error('Invalid `boxShadow` value: "'+t+'".');return{[e]:"string"==typeof t?(0,ea.parse)(t):t}}if("transform"===e){if("string"!=typeof t)throw Error("Invalid `transform` value.");let e={},r=t.replace(/(-?[\d.]+%)/g,(t,r)=>{let n=~~(1e9*Math.random());return e[n]=r,n+"px"}),n=(0,eo.getStylesForProperty)("transform",r,!0);for(let t of n.transform)for(let r in t)e[t[r]]&&(t[r]=e[t[r]]);return n}if("background"===e)return t=t.toString().trim(),/^(linear-gradient|radial-gradient|url|repeating-linear-gradient)\(/.test(t)?(0,eo.getStylesForProperty)("backgroundImage",t,!0):(0,eo.getStylesForProperty)("background",t,!0);if("textShadow"===e){t=t.toString().trim();let e={};for(let r of o8(t)){let t=(0,eo.getStylesForProperty)("textShadow",r,!0);for(let r in t)e[r]?e[r].push(t[r]):e[r]=[t[r]]}return e}if("WebkitTextStroke"===e){let r=(t=t.toString().trim()).split(" ");if(2!==r.length)throw Error("Invalid `WebkitTextStroke` value.");return{WebkitTextStrokeWidth:ik(e,r[0]),WebkitTextStrokeColor:ik(e,r[1])}}}(n,o,i)||(r=(0,eo.getStylesForProperty)(n,ik(n,o),!0),"textDecoration"!==n||o.includes(r.textDecorationColor)||(r.textDecorationColor=i),r);Object.assign(s,e)}catch(e){throw Error(e.message+(e.message.includes(o)?`
  `+iF(n):`
  in CSS rule \`${n}: ${o}\`.${iF(n)}`))}}}if(s.backgroundImage){let{backgrounds:e}=(0,ei.parseElementStyle)(s);s.backgroundImage=e}(s.maskImage||s.WebkitMaskImage)&&(i=s.maskImage||s.WebkitMaskImage,a={position:iw(s,"position")||"0% 0%",size:iw(s,"size")||"100% 100%",repeat:iw(s,"repeat")||"repeat",origin:iw(s,"origin")||"border-box",clip:iw(s,"origin")||"border-box"},s.maskImage=o8(i).filter(e=>e&&"none"!==e).reverse().map(e=>({image:e,...a})));let u=function(e,t){if("number"==typeof e)return e;try{let r=new oq(e);switch(r.unit){case"em":return r.value*t;case"rem":return 16*r.value}}catch{return t}}(s.fontSize,t.fontSize);for(let e in"u">typeof s.fontSize&&(s.fontSize=u),s.transformOrigin&&(s.transformOrigin=function(e,t){let r;if("number"==typeof e)return{xAbsolute:e};try{r=(0,eC.default)(e).nodes.filter(e=>"word"===e.type).map(e=>e.value)}catch{return{}}return 1===r.length?ix(r[0],t,!0):2===r.length?(("top"===r[0]||"bottom"===r[0]||"left"===r[1]||"right"===r[1])&&r.reverse(),{...ix(r[0],t,!0),...ix(r[1],t,!1)}):{}}(s.transformOrigin,u)),s){let r=s[e];if("lineHeight"===e)"string"==typeof r&&"normal"!==r&&(r=s[e]=oQ(r,u,u,t,!0)/u);else{if("string"==typeof r){let n=oQ(r,u,u,t);"u">typeof n&&(s[e]=n),r=s[e]}if("string"==typeof r||"object"==typeof r){let t=function e(t){if("string"==typeof t&&iC.test(t.trim()))return t.trim().replace(iC,(e,t,r,n,o)=>`rgba(${t}, ${r}, ${n}, ${o})`);if("object"==typeof t&&null!==t)for(let r in t)t[r]=e(t[r]);return t}(r);t&&(s[e]=t),r=s[e]}}if("opacity"===e&&"number"==typeof r&&(s.opacity=r*t.opacity),"transform"===e)for(let e of r){let r=Object.keys(e)[0],n=e[r],o="string"==typeof n?oQ(n,u,u,t)??n:n;e[r]=o}"textShadowRadius"===e&&(s.textShadowRadius=r.map(e=>oQ(e,u,0,t,!1))),"textShadowOffset"===e&&(s.textShadowOffset=r.map(({height:e,width:r})=>({height:oQ(e,u,0,t,!1),width:oQ(r,u,0,t,!1)})))}return s}function iT(e){if(e.startsWith("hsl")){let t=eF(e),[r,n,o]=t.values;return`hsl(${[r,`${n}%`,`${o}%`].concat(1===t.alpha?[]:[t.alpha]).join(",")})`}return e}async function iO(e,t,r,n,o){let i=await oP(),a={...r,...i_(iD[t],r),...i_(n,r)};if("img"===t){let[t,r,n]=await ih(o.src);if(void 0===r&&void 0===n){if(void 0===o.width||void 0===o.height)throw Error("Image size cannot be determined. Please provide the width and height of the image.");r=parseInt(o.width),n=parseInt(o.height)}let i=n/r,s=(a.borderLeftWidth||0)+(a.borderRightWidth||0)+(a.paddingLeft||0)+(a.paddingRight||0),u=(a.borderTopWidth||0)+(a.borderBottomWidth||0)+(a.paddingTop||0)+(a.paddingBottom||0),l=a.width||o.width,c=a.height||o.height,f="number"==typeof l&&"number"==typeof c;f&&(l-=s,c-=u),void 0===l&&void 0===c?(l="100%",e.setAspectRatio(1/i)):void 0===l?"number"==typeof c?l=c/i:e.setAspectRatio(1/i):void 0===c&&("number"==typeof l?c=l*i:e.setAspectRatio(1/i)),a.width=f?l+s:l,a.height=f?c+u:c,a.__src=t}if("svg"===t){let e=o4(o.viewBox||o.viewbox),t=e?e[3]/e[2]:null,{width:n,height:i}=o;typeof n>"u"&&i?n=null==t?0:"string"==typeof i&&i.endsWith("%")?parseInt(i)/t+"%":(i=oQ(i,r.fontSize,1,r))/t:typeof i>"u"&&n?null==t?n=0:i="string"==typeof n&&n.endsWith("%")?parseInt(n)*t+"%":(n=oQ(n,r.fontSize,1,r))*t:("u">typeof n&&(n=oQ(n,r.fontSize,1,r)||n),"u">typeof i&&(i=oQ(i,r.fontSize,1,r)||i),n||(n=null==e?void 0:e[2]),i||(i=null==e?void 0:e[3])),!a.width&&n&&(a.width=n),!a.height&&i&&(a.height=i)}return e.setDisplay(o0(a.display,{flex:i.DISPLAY_FLEX,block:i.DISPLAY_FLEX,none:i.DISPLAY_NONE,"-webkit-box":i.DISPLAY_FLEX},i.DISPLAY_FLEX,"display")),e.setAlignContent(o0(a.alignContent,{stretch:i.ALIGN_STRETCH,center:i.ALIGN_CENTER,"flex-start":i.ALIGN_FLEX_START,"flex-end":i.ALIGN_FLEX_END,"space-between":i.ALIGN_SPACE_BETWEEN,"space-around":i.ALIGN_SPACE_AROUND,baseline:i.ALIGN_BASELINE,normal:i.ALIGN_AUTO},i.ALIGN_AUTO,"alignContent")),e.setAlignItems(o0(a.alignItems,{stretch:i.ALIGN_STRETCH,center:i.ALIGN_CENTER,"flex-start":i.ALIGN_FLEX_START,"flex-end":i.ALIGN_FLEX_END,baseline:i.ALIGN_BASELINE,normal:i.ALIGN_AUTO},i.ALIGN_STRETCH,"alignItems")),e.setAlignSelf(o0(a.alignSelf,{stretch:i.ALIGN_STRETCH,center:i.ALIGN_CENTER,"flex-start":i.ALIGN_FLEX_START,"flex-end":i.ALIGN_FLEX_END,baseline:i.ALIGN_BASELINE,normal:i.ALIGN_AUTO},i.ALIGN_AUTO,"alignSelf")),e.setJustifyContent(o0(a.justifyContent,{center:i.JUSTIFY_CENTER,"flex-start":i.JUSTIFY_FLEX_START,"flex-end":i.JUSTIFY_FLEX_END,"space-between":i.JUSTIFY_SPACE_BETWEEN,"space-around":i.JUSTIFY_SPACE_AROUND},i.JUSTIFY_FLEX_START,"justifyContent")),e.setFlexDirection(o0(a.flexDirection,{row:i.FLEX_DIRECTION_ROW,column:i.FLEX_DIRECTION_COLUMN,"row-reverse":i.FLEX_DIRECTION_ROW_REVERSE,"column-reverse":i.FLEX_DIRECTION_COLUMN_REVERSE},i.FLEX_DIRECTION_ROW,"flexDirection")),e.setFlexWrap(o0(a.flexWrap,{wrap:i.WRAP_WRAP,nowrap:i.WRAP_NO_WRAP,"wrap-reverse":i.WRAP_WRAP_REVERSE},i.WRAP_NO_WRAP,"flexWrap")),"u">typeof a.gap&&e.setGap(i.GUTTER_ALL,a.gap),"u">typeof a.rowGap&&e.setGap(i.GUTTER_ROW,a.rowGap),"u">typeof a.columnGap&&e.setGap(i.GUTTER_COLUMN,a.columnGap),"u">typeof a.flexBasis&&e.setFlexBasis(a.flexBasis),e.setFlexGrow(typeof a.flexGrow>"u"?0:a.flexGrow),e.setFlexShrink(typeof a.flexShrink>"u"?0:a.flexShrink),"u">typeof a.maxHeight&&e.setMaxHeight(a.maxHeight),"u">typeof a.maxWidth&&e.setMaxWidth(a.maxWidth),"u">typeof a.minHeight&&e.setMinHeight(a.minHeight),"u">typeof a.minWidth&&e.setMinWidth(a.minWidth),e.setOverflow(o0(a.overflow,{visible:i.OVERFLOW_VISIBLE,hidden:i.OVERFLOW_HIDDEN},i.OVERFLOW_VISIBLE,"overflow")),e.setMargin(i.EDGE_TOP,a.marginTop||0),e.setMargin(i.EDGE_BOTTOM,a.marginBottom||0),e.setMargin(i.EDGE_LEFT,a.marginLeft||0),e.setMargin(i.EDGE_RIGHT,a.marginRight||0),e.setBorder(i.EDGE_TOP,a.borderTopWidth||0),e.setBorder(i.EDGE_BOTTOM,a.borderBottomWidth||0),e.setBorder(i.EDGE_LEFT,a.borderLeftWidth||0),e.setBorder(i.EDGE_RIGHT,a.borderRightWidth||0),e.setPadding(i.EDGE_TOP,a.paddingTop||0),e.setPadding(i.EDGE_BOTTOM,a.paddingBottom||0),e.setPadding(i.EDGE_LEFT,a.paddingLeft||0),e.setPadding(i.EDGE_RIGHT,a.paddingRight||0),e.setPositionType(o0(a.position,{absolute:i.POSITION_TYPE_ABSOLUTE,relative:i.POSITION_TYPE_RELATIVE},i.POSITION_TYPE_RELATIVE,"position")),"u">typeof a.top&&e.setPosition(i.EDGE_TOP,a.top),"u">typeof a.bottom&&e.setPosition(i.EDGE_BOTTOM,a.bottom),"u">typeof a.left&&e.setPosition(i.EDGE_LEFT,a.left),"u">typeof a.right&&e.setPosition(i.EDGE_RIGHT,a.right),"u">typeof a.height?e.setHeight(a.height):e.setHeightAuto(),"u">typeof a.width?e.setWidth(a.width):e.setWidthAuto(),[a,function(e){let t={};for(let r in e)ib.has(r)&&(t[r]=e[r]);return t}(a)]}var iA=[1,0,0,1,0,0];function iP({left:e,top:t,width:r,height:n},o,i,a){let s;if(o.__resolved||function(e,t,r){let n=[...iA];for(let o of e){let e=Object.keys(o)[0],i=o[e];if("string"==typeof i)if("translateX"===e)i=parseFloat(i)/100*t,o[e]=i;else if("translateY"===e)i=parseFloat(i)/100*r,o[e]=i;else throw Error(`Invalid transform: "${e}: ${i}".`);let a=i,s=[...iA];switch(e){case"translateX":s[4]=a;break;case"translateY":s[5]=a;break;case"scale":s[0]=a,s[3]=a;break;case"scaleX":s[0]=a;break;case"scaleY":s[3]=a;break;case"rotate":{let e=a*Math.PI/180,t=Math.cos(e),r=Math.sin(e);s[0]=t,s[1]=r,s[2]=-r,s[3]=t;break}case"skewX":s[2]=Math.tan(a*Math.PI/180);break;case"skewY":s[1]=Math.tan(a*Math.PI/180)}n=oJ(s,n)}e.splice(0,e.length),e.push(...n),e.__resolved=!0}(o,r,n),i)s=o;else{let i=(null==a?void 0:a.xAbsolute)??((null==a?void 0:a.xRelative)??50)*r/100,u=(null==a?void 0:a.yAbsolute)??((null==a?void 0:a.yRelative)??50)*n/100,l=e+i,c=t+u;s=oJ([1,0,0,1,l,c],oJ(o,[1,0,0,1,-l,-c])),o.__parent&&(s=oJ(o.__parent,s)),o.splice(0,6,...s)}return`matrix(${s.map(e=>e.toFixed(2)).join(",")})`}function iL(e){return String.fromCodePoint(Number(e=e.replace("U+","0x")))}var iI=iL("U+0020"),iB=iL("U+0009"),iR=iL("U+2026"),iU=new Set([iB]);async function*iM(e,t){var r,n;let o,i=await oP(),{parentStyle:a,inheritedStyle:s,parent:u,font:l,id:c,isInheritingTransform:f,debug:p,embedFont:h,graphemeImages:d,locale:g,canLoadAdditionalAssets:v}=t,{textAlign:m,lineHeight:y,textWrap:D,fontSize:b,filter:x,tabSize:w=8,letterSpacing:E,_inheritedBackgroundClipTextPath:S,flexShrink:k}=a,{words:F,requiredBreaks:C,allowSoftWrap:_,allowBreakWord:T,processedContent:O,shouldCollapseTabsAndSpaces:A,lineLimit:P,blockEllipsis:L}=function(e,t,r){var n,o,i,a,s;let u,l,c,{textTransform:f,whiteSpace:p,wordBreak:h}=t,{content:d,shouldCollapseTabsAndSpaces:g,allowSoftWrap:v}=(a=(n=e,o=f,i=r,"uppercase"===o?n=n.toLocaleUpperCase(i):"lowercase"===o?n=n.toLocaleLowerCase(i):"capitalize"===o&&(n=o2(n,"word",i).map(e=>o2(e,"grapheme",i).map((e,t)=>0===t?e.toLocaleUpperCase(i):e).join("")).join("")),e=n),u=["pre","pre-wrap","pre-line"].includes(s=p),l=["normal","nowrap","pre-line"].includes(s),c=!["pre","nowrap"].includes(s),u||(a=a.replace(/\n/g,iI)),l&&(a=a.replace(/([ ]|\t)+/g,iI).replace(/^[ ]|[ ]$/g,"")),{content:a,shouldCollapseTabsAndSpaces:l,allowSoftWrap:c}),{words:m,requiredBreaks:y,allowBreakWord:D}=function(e,t){let r=["break-all","break-word"].includes(t),{words:n,requiredBreaks:o}=function(e,t){if("break-all"===t)return{words:o2(e,"grapheme"),requiredBreaks:[]};if("keep-all"===t)return{words:o2(e,"word"),requiredBreaks:[]};let r=new Q(e),n=0,o=r.nextBreak(),i=[],a=[!1];for(;o;){let t=e.slice(n,o.position);i.push(t),o.required?a.push(!0):a.push(!1),n=o.position,o=r.nextBreak()}return{words:i,requiredBreaks:a}}(e,t);return{words:n,requiredBreaks:o,allowBreakWord:r}}(d,h),[b,x]=function(e,t){let{textOverflow:r,lineClamp:n,WebkitLineClamp:o,WebkitBoxOrient:i,overflow:a,display:s}=e;if("block"===s&&n){let[e,t=iR]=function(e){if("number"==typeof e)return[e];let t=/^(\d+)\s*"(.*)"$/.exec(e),r=/^(\d+)\s*'(.*)'$/.exec(e);return t?[+t[1],t[2]]:r?[+r[1],r[2]]:[]}(n);if(e)return[e,t]}return"ellipsis"===r&&"-webkit-box"===s&&"vertical"===i&&"number"==typeof o&&o>0?[o,iR]:"ellipsis"!==r||"hidden"!==a||t?[1/0]:[1,iR]}(t,v);return{words:m,requiredBreaks:y,allowSoftWrap:v,allowBreakWord:D,processedContent:d,shouldCollapseTabsAndSpaces:g,lineLimit:b,blockEllipsis:x}}(e,a,g),I=(r=i,n=m,(o=r.Node.create()).setAlignItems(r.ALIGN_BASELINE),o.setJustifyContent(o0(n,{left:r.JUSTIFY_FLEX_START,right:r.JUSTIFY_FLEX_END,center:r.JUSTIFY_CENTER,justify:r.JUSTIFY_SPACE_BETWEEN,start:r.JUSTIFY_FLEX_START,end:r.JUSTIFY_FLEX_END},r.JUSTIFY_FLEX_START,"textAlign")),o);u.insertChild(I,u.getChildCount()),"[object Undefined]"===Object.prototype.toString.call(k)&&u.setFlexShrink(1);let B=l.getEngine(b,y,a,g),R=v?o2(O,"grapheme").filter(e=>!iU.has(e)&&!B.has(e)):[];function U(e){return!!(d&&d[e])}yield R.map(e=>({word:e,locale:g})),R.length&&(B=l.getEngine(b,y,a,g));let{measureGrapheme:M,measureGraphemeArray:N,measureText:j}=function(e,t,r){let{fontSize:n,letterSpacing:o}=r,i=new Map;function a(t){if(i.has(t))return i.get(t);let r=e.measure(t,{fontSize:n,letterSpacing:o});return i.set(t,r),r}function s(e){let r=0;for(let o of e)t(o)?r+=n:r+=a(o);return r}return{measureGrapheme:a,measureGraphemeArray:s,measureText:function(e){return s(o2(e,"grapheme"))}}}(B,U,{fontSize:b,letterSpacing:E}),W=o5(w)?oQ(w,b,1,a):M(iI)*w,G=(e,t)=>{var r;let n;if(0===e.length)return{originWidth:0,endingSpacesWidth:0,text:e};let{index:o,tabCount:i}=(r=e,(n=/(\t)+/.exec(r))?{index:n.index,tabCount:n[0].length}:{index:null,tabCount:0}),a=0;if(i>0){let r=e.slice(0,o),n=e.slice(o+i),s=j(r);a=(0===W?s:(Math.floor((s+t)/W)+i)*W)+j(n)}else a=j(e);let s=e.trimEnd()===e?a:j(e.trimEnd());return{originWidth:a,endingSpacesWidth:a-s,text:e}},$=[],z=[],q=[],V=[],H=[];function X(e){let t=0,r=0,n=-1,o=0,i=0,a=0,s=0;$=[],q=[0],V=[],H=[];let u=0,l=0;for(;u<F.length&&t<P;){let c=F[u],f=C[u],p=0,{originWidth:h,endingSpacesWidth:d,text:g}=G(c,i);c=g,p=h,f&&0===a&&(a=B.height(c));let v=0>",.!?:-@)>]}%#".indexOf(c[0]),m=!i,y=u&&v&&i+p>e+d&&_;if(T&&p>e&&(!i||y||f)){let e=o2(c,"grapheme");F.splice(u,1,...e),i>0&&($.push(i-l),z.push(s),t++,o+=a,i=0,a=0,s=0,q.push(1),n=-1),l=d;continue}if(f||y)A&&c===iI&&(p=0),$.push(i-l),z.push(s),t++,o+=a,i=p,a=p?B.height(c):0,s=p?B.baseline(c):0,q.push(1),n=-1,f||(r=Math.max(r,e));else{i+=p;let e=B.height(c);e>a&&(a=e,s=B.baseline(c)),m&&q[q.length-1]++}m&&n++,r=Math.max(r,i);let D=i-p;if(0===p)H.push({y:o,x:D,width:0,line:t,lineIndex:n,isImage:!1});else{let e=o2(c,"word");for(let r=0;r<e.length;r++){let i=e[r],a=0,s=!1;U(i)?(a=b,s=!0):a=M(i),V.push(i),H.push({y:o,x:D,width:a,line:t,lineIndex:n,isImage:s}),D+=a}}u++,l=d}return i&&(t<P&&(o+=a),t++,$.push(i),z.push(s)),{width:r,height:o}}let Y={width:0,height:0};I.setMeasureFunc(e=>{let{width:t,height:r}=X(e);if("balance"===D){let e=t/2,n=t,o=t;for(;e+1<n;){let{height:t}=X(o=(e+n)/2);t>r?e=o:n=o}X(n);let i=Math.ceil(n);return Y={width:i,height:r},{width:i,height:r}}let n=Math.ceil(t);return Y={width:n,height:r},{width:n,height:r}});let[Z,K]=yield,J="",ee="",et=s._inheritedClipPathId,er=s._inheritedMaskId,{left:en,top:eo,width:ei,height:ea}=I.getComputedLayout(),es=u.getComputedWidth()-u.getComputedPadding(i.EDGE_LEFT)-u.getComputedPadding(i.EDGE_RIGHT)-u.getComputedBorder(i.EDGE_LEFT)-u.getComputedBorder(i.EDGE_RIGHT),eu=Z+en,el=K+eo,{matrix:ec,opacity:ef}=function({left:e,top:t,width:r,height:n,isInheritingTransform:o},i){let a="",s=1;return i.transform&&(a=iP({left:e,top:t,width:r,height:n},i.transform,o,i.transformOrigin)),void 0!==i.opacity&&(s=+i.opacity),{matrix:a,opacity:s}}({left:en,top:eo,width:ei,height:ea,isInheritingTransform:f},a),ep="";if(a.textShadowOffset){let{textShadowColor:e,textShadowOffset:t,textShadowRadius:r}=a;ep=function({id:e,width:t,height:r},n){if(!n.shadowColor||!n.shadowOffset||typeof n.shadowRadius>"u")return"";let o=n.shadowColor.length,i="",a="",s=0,u=t,l=0,c=r;for(let f=0;f<o;f++){let p=n.shadowRadius[f]*n.shadowRadius[f]/4;s=Math.min(n.shadowOffset[f].width-p,s),u=Math.max(n.shadowOffset[f].width+p+t,u),l=Math.min(n.shadowOffset[f].height-p,l),c=Math.max(n.shadowOffset[f].height+p+r,c),i+=o3("feDropShadow",{dx:n.shadowOffset[f].width,dy:n.shadowOffset[f].height,stdDeviation:n.shadowRadius[f]/2,"flood-color":n.shadowColor[f],"flood-opacity":1,...o>1?{in:"SourceGraphic",result:`satori_s-${e}-result-${f}`}:{}}),o>1&&(a=o3("feMergeNode",{in:`satori_s-${e}-result-${f}`})+a)}return o3("filter",{id:`satori_s-${e}`,x:(s/t*110.00000000000001).toFixed(2)+"%",y:(l/r*110.00000000000001).toFixed(2)+"%",width:((u-s)/t*110.00000000000001).toFixed(2)+"%",height:((c-l)/r*110.00000000000001).toFixed(2)+"%"},i+(a?o3("feMerge",{},a):""))}({width:Y.width,height:Y.height,id:c},{shadowColor:e,shadowOffset:t,shadowRadius:r}),ep=o3("defs",{},ep)}let eh="",ed="",eg="",ev=-1,em={},ey=null,eD=0;for(let e=0;e<V.length;e++){let t=H[e],r=H[e+1];if(!t)continue;let n=V[e],o=null,i=!1,s=d?d[n]:null,u=t.y,l=t.x,f=t.width,v=t.line;if(v===ev)continue;let y=!1;if($.length>1){let e=ei-$[v];if("right"===m||"end"===m)l+=e;else if("center"===m)l+=e/2;else if("justify"===m&&v<$.length-1){let r=q[v];l+=(r>1?e/(r-1):0)*t.lineIndex,y=!0}}let D=z[v],x=B.baseline(n),w=B.height(n),k=D-x;if(em[v]||(em[v]=[l,el+u+k,x,y?ei:$[v]]),P!==1/0){let t=function(e,t){let r=o2(t,"grapheme",g),n="",o=0;for(let t of r){let r=e+N([n+t]);if(n&&r+a>es)break;n+=t,o=r}return{subset:n,resolvedWidth:o}},o=L,a=M(L);a>es&&(a=M(o=iR));let s=M(iI),u=v<$.length-1;if(v+1===P&&(u||$[v]>es)){if(l+f+a+s>es){let{subset:e,resolvedWidth:r}=t(l,n);n=e+o,ev=v,em[v][2]=r,i=!0}else if(r&&r.line!==v)if("center"===m){let{subset:e,resolvedWidth:r}=t(l,n);n=e+o,ev=v,em[v][2]=r,i=!0}else{let{subset:r,resolvedWidth:a}=t(f+l,V[e+1]);n=n+r+o,ev=v,em[v][2]=a,i=!0}}}if(s)u+=0;else if(h){if(!n.includes(iB)&&!o1.includes(n)&&V[e+1]&&r&&!r.isImage&&u===r.y&&!i){null===ey&&(eD=l),ey=null===ey?n:ey+n;continue}let a=null===ey?n:ey+n,s=null===ey?l:eD,c=t.width+l-s;o=B.getSVG(a.replace(/(\t)+/g,""),{fontSize:b,left:eu+s,top:el+u+x+k,letterSpacing:E}),ey=null,p&&(eg+=o3("rect",{x:eu+s,y:el+u+k,width:c,height:w,fill:"transparent",stroke:"#575eff","stroke-width":1,transform:ec||void 0,"clip-path":et?`url(#${et})`:void 0})+o3("line",{x1:eu+l,x2:eu+l+t.width,y1:el+u+k+x,y2:el+u+k+x,stroke:"#14c000","stroke-width":1,transform:ec||void 0,"clip-path":et?`url(#${et})`:void 0}))}else u+=x+k;if(a.textDecorationLine){let e=em[v];e&&!e[4]&&(eh+=function({width:e,left:t,top:r,ascender:n,clipPathId:o,matrix:i},a){let{textDecorationColor:s,textDecorationStyle:u,textDecorationLine:l,fontSize:c,color:f}=a;if(!l||"none"===l)return"";let p=Math.max(1,.1*c),h="line-through"===l?r+.7*n:"underline"===l?r+1.1*n:r,d="dashed"===u?`${1.2*p} ${2*p}`:"dotted"===u?`0 ${2*p}`:void 0;return(o?`<g clip-path="url(#${o})">`:"")+o3("line",{x1:t,y1:h,x2:t+e,y2:h,stroke:s||f,"stroke-width":p,"stroke-dasharray":d,"stroke-linecap":"dotted"===u?"round":"square",transform:i})+(o?"</g>":"")}({left:eu+e[0],top:e[1],width:e[3],ascender:e[2],clipPathId:et,matrix:ec},a),e[4]=1)}if(null!==o)ed+=o+" ";else{let[e,t]=function({id:e,content:t,filter:r,left:n,top:o,width:i,height:a,matrix:s,opacity:u,image:l,clipPathId:c,debug:f,shape:p,decorationShape:h},d){let g="";if(f&&(g=o3("rect",{x:n,y:o-a,width:i,height:a,fill:"transparent",stroke:"#575eff","stroke-width":1,transform:s||void 0,"clip-path":c?`url(#${c})`:void 0})),l)return[(r?`${r}<g filter="url(#satori_s-${e})">`:"")+o3("image",{...{href:l,x:n,y:o,width:i,height:a,transform:s||void 0,"clip-path":c?`url(#${c})`:void 0,style:d.filter?`filter:${d.filter}`:void 0},opacity:1!==u?u:void 0})+(h||"")+(r?"</g>":"")+g,""];let v={x:n,y:o,width:i,height:a,"font-weight":d.fontWeight,"font-style":d.fontStyle,"font-size":d.fontSize,"font-family":d.fontFamily,"letter-spacing":d.letterSpacing||void 0,transform:s||void 0,"clip-path":c?`url(#${c})`:void 0,style:d.filter?`filter:${d.filter}`:void 0,"stroke-width":d.WebkitTextStrokeWidth?`${d.WebkitTextStrokeWidth}px`:void 0,stroke:d.WebkitTextStrokeWidth?d.WebkitTextStrokeColor:void 0,"stroke-linejoin":d.WebkitTextStrokeWidth?"round":void 0,"paint-order":d.WebkitTextStrokeWidth?"stroke":void 0};return[(r?`${r}<g filter="url(#satori_s-${e})">`:"")+o3("text",{...v,fill:d.color,opacity:1!==u?u:void 0},(0,eT.default)(t))+(h||"")+(r?"</g>":"")+g,p?o3("text",v,(0,eT.default)(t)):""]}({content:n,filter:ep,id:c,left:eu+l,top:el+u,width:f,height:w,matrix:ec,opacity:ef,image:s,clipPathId:et,debug:p,shape:!!S,decorationShape:eh},a);J+=e,ee+=t,eh=""}if(i)break}if(ed){let e="transparent"!==a.color&&0!==ef?o3("path",{fill:a.color,d:ed,transform:ec||void 0,opacity:1!==ef?ef:void 0,"clip-path":et?`url(#${et})`:void 0,mask:er?`url(#${er})`:void 0,style:x?`filter:${x}`:void 0,"stroke-width":s.WebkitTextStrokeWidth?`${s.WebkitTextStrokeWidth}px`:void 0,stroke:s.WebkitTextStrokeWidth?s.WebkitTextStrokeColor:void 0,"stroke-linejoin":s.WebkitTextStrokeWidth?"round":void 0,"paint-order":s.WebkitTextStrokeWidth?"stroke":void 0}):"";S&&(ee=o3("path",{d:ed,transform:ec||void 0})),J+=(ep?ep+o3("g",{filter:`url(#satori_s-${c})`},e+eh):e+eh)+eg}return ee&&(a._inheritedBackgroundClipTextPath.value+=ee),J}function iN(e,t,r,n,o){let i=[];for(let n of t){let{color:t}=n;if(!i.length&&(i.push({offset:0,color:t}),!n.offset||"0"===n.offset.value))continue;let o=typeof n.offset>"u"?void 0:"%"===n.offset.unit?n.offset.value/100:Number(oQ(`${n.offset.value}${n.offset.unit}`,r.fontSize,e,r,!0))/e;i.push({offset:o,color:t})}i.length||i.push({offset:0,color:"transparent"});let a=i[i.length-1];1!==a.offset&&(typeof a.offset>"u"?a.offset=1:n?i[i.length-1]={offset:1,color:a.color}:i.push({offset:1,color:a.color}));let s=0,u=1;for(let e=0;e<i.length;e++)if(typeof i[e].offset>"u"){for(u<e&&(u=e);typeof i[u].offset>"u";)u++;i[e].offset=(i[u].offset-i[s].offset)/(u-s)*(e-s)+i[s].offset}else s=e;return"mask"===o?i.map(e=>{let t=eF(e.color);return t?0===t.alpha?{...e,color:"rgba(0, 0, 0, 1)"}:{...e,color:`rgba(255, 255, 255, ${t.alpha})`}:e}):i}function ij(e,t,r,n){switch(e){case"center":return{[n]:"x"===n?t/2:r/2};case"left":return{x:0};case"top":return{y:0};case"right":return{x:t};case"bottom":return{y:r}}}function iW(e,{x:t,y:r,defaultX:n,defaultY:o}){return(e?e.split(" ").map(e=>{try{let t=new oq(e);return"length"===t.type||"number"===t.type?t.value:t.value+t.unit}catch{return null}}).filter(e=>null!==e):[n,o]).map((e,n)=>{var o;return o=[t,r][n],"string"==typeof e&&e.endsWith("%")?o*parseFloat(e)/100:+e})}async function iG({id:e,width:t,height:r,left:n,top:o},{image:i,size:a,position:s,repeat:u},l,c){u=u||"repeat",c=c||"background";let f="repeat-x"===u||"repeat"===u,p="repeat-y"===u||"repeat"===u,h=iW(a,{x:t,y:r,defaultX:t,defaultY:r}),d=iW(s,{x:t,y:r,defaultX:0,defaultY:0});if(i.startsWith("linear-gradient(")||i.startsWith("repeating-linear-gradient("))return function({id:e,width:t,height:r,repeatX:n,repeatY:o},i,a,s,u,l){var c,f,p,h;let d=function(e){var t;if(!/^(repeating-)?linear-gradient/.test(e))throw SyntaxError(`could not find syntax for this item: ${e}`);let[,r,n]=e.match(/(repeating-)?linear-gradient\((.+)\)/),o={orientation:{type:"directional",value:"bottom"},repeating:!!r,stops:[]},i=eO(n),a=(t=i[0]).startsWith("to ")?{type:"directional",value:t.replace("to ","")}:["turn","deg","grad","rad"].some(e=>t.endsWith(e))?{type:"angular",value:eL(t)}:null;return a&&(o.orientation=a,i.shift()),{...o,stops:eA(i)}}(i),[g,v]=a,m=i.startsWith("repeating"),y,D,b;if("directional"===d.orientation.type){let e,t,r,n;D=Math.sqrt(Math.pow(((c=d.orientation.value,e=0,t=0,r=0,n=0,c.includes("top")?t=1:c.includes("bottom")&&(n=1),c.includes("left")?e=1:c.includes("right")&&(r=1),e||r||t||n||(t=1),y={x1:e,y1:t,x2:r,y2:n}).x2-y.x1)*g,2)+Math.pow((y.y2-y.y1)*v,2))}else if("angular"===d.orientation.type){let e,t,r,n,o,i,a,s,u,l,{length:c,...m}=(f=oK(`${d.orientation.value.value}${d.orientation.value.unit}`)/180*Math.PI,p=g,e=Math.pow((h=v)/p,2),(l=c=>{if(0===c){t=0,r=h,n=0,o=0,i=h;return}if(c===Math.PI/2){t=0,r=0,n=p,o=0,i=p;return}if(c>0&&c<Math.PI/2){t=(e*p/2/Math.tan(c)-h/2)/(Math.tan(c)+e/Math.tan(c)),r=Math.tan(c)*t+h,i=Math.sqrt(Math.pow((n=Math.abs(p/2-t)+p/2)-t,2)+Math.pow((o=h/2-Math.abs(r-h/2))-r,2)),s=(p/2/Math.tan(c)-h/2)/(Math.tan(c)+1/Math.tan(c)),u=Math.tan(c)*s+h,i=2*Math.sqrt(Math.pow(p/2-s,2)+Math.pow(h/2-u,2));return}if(c>Math.PI/2&&c<Math.PI){t=(h/2+e*p/2/Math.tan(c))/(Math.tan(c)+e/Math.tan(c)),r=Math.tan(c)*t,n=Math.abs(p/2-t)+p/2,o=h/2+Math.abs(r-h/2),s=(p/2/Math.tan(c)+h/2)/(Math.tan(c)+1/Math.tan(c)),u=Math.tan(c)*s,i=2*Math.sqrt(Math.pow(p/2-s,2)+Math.pow(h/2-u,2));return}c>=Math.PI&&(l(c-Math.PI),a=t,t=n,n=a,a=r,r=o,o=a)})(f=(f%(2*Math.PI)+2*Math.PI)%(2*Math.PI)),{x1:t/p,y1:r/h,x2:n/p,y2:o/h,length:i});D=c,y=m}b=m?function(e,t,r,n){let{x1:o,x2:i,y1:a,y2:s}=r,u=e[0].offset?"%"===e[0].offset.unit?Number(e[0].offset.value)/100:oQ(`${e[0].offset.value}${e[0].offset.unit}`,n.fontSize,t,n,!0)/t:0,l=e.at(-1).offset?"%"===e.at(-1).offset.unit?Number(e.at(-1).offset.value)/100:oQ(`${e.at(-1).offset.value}${e.at(-1).offset.unit}`,n.fontSize,t,n,!0)/t:1;return{x1:(i-o)*u+o,y1:(s-a)*u+a,x2:(i-o)*l+o,y2:(s-a)*l+a}}(d.stops,D,y,u):y;let x=iN(m?function(e,t){let{offset:r}=e[e.length-1];return r?"%"===r.unit?Number(r.value)/100*t:Number(r.value):t}(d.stops,D):D,d.stops,u,m,l),w=`satori_bi${e}`,E=`satori_pattern_${e}`,S=o3("pattern",{id:E,x:s[0]/t,y:s[1]/r,width:n?g/t:"1",height:o?v/r:"1",patternUnits:"objectBoundingBox"},o3("linearGradient",{id:w,...b,spreadMethod:m?"repeat":"pad"},x.map(e=>o3("stop",{offset:(e.offset??0)*100+"%","stop-color":e.color})).join(""))+o3("rect",{x:0,y:0,width:g,height:v,fill:`url(#${w})`}));return[E,S]}({id:e,width:t,height:r,repeatX:f,repeatY:p},i,h,d,l,c);if(i.startsWith("radial-gradient("))return function({id:e,width:t,height:r,repeatX:n,repeatY:o},i,a,s,u,l){var c,f,p,h,d,g,v;let m,{shape:y,stops:D,position:b,size:x}=function(e){var t,r,n,o;if(!/(repeating-)?radial-gradient/.test(e))throw SyntaxError(`could not find syntax for this item: ${e}`);let[,i,a]=e.match(/(repeating-)?radial-gradient\((.+)\)/),s={shape:"ellipse",repeating:!!i,size:[{type:"keyword",value:"farthest-corner"}],position:{x:{type:"keyword",value:"center"},y:{type:"keyword",value:"center"}},stops:[]},u=eO(a);if(o=u[0],!/(circle|ellipse|at)/.test(o)&&/^(rgba?|hwb|hsl|lab|lch|oklab|color|#|[a-zA-Z]+)/.test(o))return{...s,stops:eA(u)};let l=u[0].split("at").map(e=>e.trim()),c=((l[0]||"").match(/(circle|ellipse)/)||[])[1],f=(l[0]||"").match(/(-?\d+\.?\d*(vw|vh|px|em|rem|%|rad|grad|turn|deg)?|closest-corner|closest-side|farthest-corner|farthest-side)/g)||[],p=function(e){let t=[,,].fill("");for(let r=0;r<2;r++)e[r]?t[r]=e[r]:t[r]="center";return t}((l[1]||"").split(" "));return c?s.shape=c:1!==f.length||(t=f[0],eI.has(t))?s.shape="ellipse":s.shape="circle",0===f.length&&f.push("farthest-corner"),s.size=f.map(e=>eI.has(e)?{type:"keyword",value:e}:{type:"length",value:eL(e)}),r=p[0],s.position.x=eB.has(r)?{type:"keyword",value:p[0]}:{type:"length",value:eL(p[0])},n=p[1],s.position.y=eB.has(n)?{type:"keyword",value:p[1]}:{type:"length",value:eL(p[1])},(c||f.length>0||l[1])&&u.shift(),{...s,stops:eA(u)}}(i),[w,E]=a,S=w/2,k=E/2,F=(f=b.x,p=b.y,h=w,d=E,g=u.fontSize,v=u,m={x:h/2,y:d/2},"keyword"===f.type?Object.assign(m,ij(f.value,h,d,"x")):m.x=oQ(`${f.value.value}${f.value.unit}`,g,h,v,!0)||h/2,"keyword"===p.type?Object.assign(m,ij(p.value,h,d,"y")):m.y=oQ(`${p.value.value}${p.value.unit}`,g,d,v,!0)||d/2,m);S=F.x,k=F.y;let C=iN(t,D,u,!1,l),_=`satori_radial_${e}`,T=`satori_pattern_${e}`,O=`satori_mask_${e}`,A=function(e,t,r,n,o,i){let[a,s]=o,{x:u,y:l}=n,c={},f=0,p=0;if(!t.some(e=>"keyword"===e.type)){if(t.some(e=>e.value.value.startsWith("-")))throw Error("disallow setting negative values to the size of the shape. Check https://w3c.github.io/csswg-drafts/css-images/#valdef-rg-size-length-0");return"circle"===e?{r:Number(oQ(`${t[0].value.value}${t[0].value.unit}`,r,a,i,!0))}:{rx:Number(oQ(`${t[0].value.value}${t[0].value.unit}`,r,a,i,!0)),ry:Number(oQ(`${t[1].value.value}${t[1].value.unit}`,r,s,i,!0))}}switch(t[0].value){case"farthest-corner":f=Math.max(Math.abs(a-u),Math.abs(u)),p=Math.max(Math.abs(s-l),Math.abs(l));break;case"closest-corner":f=Math.min(Math.abs(a-u),Math.abs(u)),p=Math.min(Math.abs(s-l),Math.abs(l));break;case"farthest-side":return"circle"===e?c.r=Math.max(Math.abs(a-u),Math.abs(u),Math.abs(s-l),Math.abs(l)):(c.rx=Math.max(Math.abs(a-u),Math.abs(u)),c.ry=Math.max(Math.abs(s-l),Math.abs(l))),c;case"closest-side":return"circle"===e?c.r=Math.min(Math.abs(a-u),Math.abs(u),Math.abs(s-l),Math.abs(l)):(c.rx=Math.min(Math.abs(a-u),Math.abs(u)),c.ry=Math.min(Math.abs(s-l),Math.abs(l))),c}if("circle"===e)c.r=Math.sqrt(f*f+p*p);else{let e=0!==p?f/p:1;0===f?(c.rx=0,c.ry=0):(c.ry=Math.sqrt(f*f+p*p*e*e)/e,c.rx=c.ry*e)}return c}(y,x,u.fontSize,{x:S,y:k},[w,E],u),P=o3("pattern",{id:T,x:s[0]/t,y:s[1]/r,width:n?w/t:"1",height:o?E/r:"1",patternUnits:"objectBoundingBox"},o3("radialGradient",{id:_},C.map(e=>o3("stop",{offset:e.offset||0,"stop-color":e.color})).join(""))+o3("mask",{id:O},o3("rect",{x:0,y:0,width:w,height:E,fill:"#fff"}))+o3("rect",{x:0,y:0,width:w,height:E,fill:(null==(c=C.at(-1))?void 0:c.color)||"transparent"})+o3(y,{cx:S,cy:k,width:w,height:E,...A,fill:`url(#${_})`,mask:`url(#${O})`}));return[T,P]}({id:e,width:t,height:r,repeatX:f,repeatY:p},i,h,d,l,c);if(i.startsWith("url(")){let s=iW(a,{x:t,y:r,defaultX:0,defaultY:0}),[u,l,h]=await ih(i.slice(4,-1)),g="mask"===c?l||s[0]:s[0]||l,v="mask"===c?h||s[1]:s[1]||h;return[`satori_bi${e}`,o3("pattern",{id:`satori_bi${e}`,patternContentUnits:"userSpaceOnUse",patternUnits:"userSpaceOnUse",x:d[0]+n,y:d[1]+o,width:f?g:"100%",height:p?v:"100%"},o3("image",{x:0,y:0,width:g,height:v,preserveAspectRatio:"none",href:u}))]}throw Error(`Invalid background image: "${i}"`)}function i$(e,t,r){return r<e+t&&(r/2<e&&r/2<t?e=t=r/2:r/2<e?e=r-t:r/2<t&&(t=r-e)),[e,t]}function iz(e){e[0]=e[1]=Math.min(e[0],e[1])}function iq(e,t,r,n,o){if("string"==typeof e){let i=e.split(" ").map(e=>e.trim()),a=!i[1]&&!i[0].endsWith("%");return i[1]=i[1]||i[0],[a,[Math.min(oQ(i[0],n,t,o,!0),t),Math.min(oQ(i[1],n,r,o,!0),r)]]}return"number"==typeof e?[!0,[Math.min(e,t),Math.min(e,r)]]:[!0,void 0]}var iV=e=>e&&0!==e[0]&&0!==e[1];function iH({left:e,top:t,width:r,height:n},o,i){let{borderTopLeftRadius:a,borderTopRightRadius:s,borderBottomLeftRadius:u,borderBottomRightRadius:l,fontSize:c}=o,f,p,h,d;if([f,a]=iq(a,r,n,c,o),[p,s]=iq(s,r,n,c,o),[h,u]=iq(u,r,n,c,o),[d,l]=iq(l,r,n,c,o),!i&&!iV(a)&&!iV(s)&&!iV(u)&&!iV(l))return"";a||(a=[0,0]),s||(s=[0,0]),u||(u=[0,0]),l||(l=[0,0]),[a[0],s[0]]=i$(a[0],s[0],r),[u[0],l[0]]=i$(u[0],l[0],r),[a[1],u[1]]=i$(a[1],u[1],n),[s[1],l[1]]=i$(s[1],l[1],n),f&&iz(a),p&&iz(s),h&&iz(u),d&&iz(l);let g=[];g[0]=[s,s],g[1]=[l,[-l[0],l[1]]],g[2]=[u,[-u[0],-u[1]]],g[3]=[a,[a[0],-a[1]]];let v=`h${r-a[0]-s[0]} a${g[0][0]} 0 0 1 ${g[0][1]}`,m=`v${n-s[1]-l[1]} a${g[1][0]} 0 0 1 ${g[1][1]}`,y=`h${l[0]+u[0]-r} a${g[2][0]} 0 0 1 ${g[2][1]}`,D=`v${u[1]+a[1]-n} a${g[3][0]} 0 0 1 ${g[3][1]}`;if(i){let o=function(o){let i=function([e,t]){return 0===Math.round(1e3*e)&&0===Math.round(1e3*t)?0:Math.round(e*t/Math.sqrt(e*e+t*t)*1e3)/1e3}([a,s,l,u][o]);return 0===o?[[e+a[0]-i,t+a[1]-i],[e+a[0],t]]:1===o?[[e+r-s[0]+i,t+s[1]-i],[e+r,t+s[1]]]:2===o?[[e+r-l[0]+i,t+n-l[1]+i],[e+r-l[0],t+n]]:[[e+u[0]-i,t+n-u[1]+i],[e,t+n-u[1]]]},c=i.indexOf(!1);if(!i.includes(!0))throw Error("Invalid `partialSides`.");if(-1===c)c=0;else for(;!i[c];)c=(c+1)%4;let f="",p=o(c),h=`M${p[0]} A${g[(c+3)%4][0]} 0 0 1 ${p[1]}`,d=0;for(;d<4&&i[(c+d)%4];d++)f+=h+" ",h=[v,m,y,D][(c+d)%4];let b=(c+d)%4;f+=h.split(" ")[0];let x=o(b);return f+` A${g[(b+3)%4][0]} 0 0 1 ${x[0]}`}return`M${e+a[0]},${t} ${v} ${m} ${y} ${D}`}function iX(e,t,r){return r[e+"Width"]===r[t+"Width"]&&r[e+"Style"]===r[t+"Style"]&&r[e+"Color"]===r[t+"Color"]}function iY({left:e,top:t,width:r,height:n,props:o,asContentMask:i,maskBorderOnly:a},s){let u=["borderTop","borderRight","borderBottom","borderLeft"];if(!i&&!u.some(e=>s[e+"Width"]))return"";let l="",c=0;for(;c>0&&iX(u[c],u[(c+3)%4],s);)c=(c+3)%4;let f=[!1,!1,!1,!1],p=[];for(let h=0;h<4;h++){let d=(c+h)%4,g=(c+h+1)%4,v=u[d],m=u[g];if(f[d]=!0,p=[s[v+"Width"],s[v+"Style"],s[v+"Color"],v],!iX(v,m,s)){let u=(p[0]||0)+(i&&!a&&s[v.replace("border","padding")]||0);u&&(l+=o3("path",{width:r,height:n,...o,fill:"none",stroke:i?"#000":p[2],"stroke-width":2*u,"stroke-dasharray":i||"dashed"!==p[1]?void 0:2*u+" "+u,d:iH({left:e,top:t,width:r,height:n},s,f)})),f=[!1,!1,!1,!1]}}if(f.some(Boolean)){let u=(p[0]||0)+(i&&!a&&s[p[3].replace("border","padding")]||0);u&&(l+=o3("path",{width:r,height:n,...o,fill:"none",stroke:i?"#000":p[2],"stroke-width":2*u,"stroke-dasharray":i||"dashed"!==p[1]?void 0:2*u+" "+u,d:iH({left:e,top:t,width:r,height:n},s,f)}))}return l}var iZ={circle:/circle\((.+)\)/,ellipse:/ellipse\((.+)\)/,path:/path\((.+)\)/,polygon:/polygon\((.+)\)/,inset:/inset\((.+)\)/};function iQ(e){let[,t="nonzero",r]=e.replace(/('|")/g,"").match(/^(nonzero|evenodd)?,?(.+)/)||[];return[t,r]}function iK(e,t,r){let n=e.split(" "),o={x:n[0]||"50%",y:n[1]||"50%"};return n.forEach(e=>{"top"===e?o.y=0:"bottom"===e?o.y=r:"left"===e?o.x=0:"right"===e?o.x=t:"center"===e&&(o.x=t/2,o.y=r/2)}),o}function iJ(e){return`satori_cp-${e}`}var i0=e=>`satori_mi-${e}`;async function i1(e,t,r){if(!t.maskImage)return["",""];let{left:n,top:o,width:i,height:a,id:s}=e,u=t.maskImage,l=u.length;if(!l)return["",""];let c=i0(s),f="";for(let e=0;e<l;e++){let t=u[e],[s,l]=await iG({id:`${c}-${e}`,left:n,top:o,width:i,height:a},t,r,"mask");f+=l+o3("rect",{x:n,y:o,width:i,height:a,fill:`url(#${s})`})}return f=o3("mask",{id:c},f),[c,f]}async function i2({id:e,left:t,top:r,width:n,height:o,isInheritingTransform:i,src:a,debug:s},u,l){if("none"===u.display)return"";let c=!!a,f="rect",p="",h="",d=[],g=1,v="";u.backgroundColor&&d.push(u.backgroundColor),void 0!==u.opacity&&(g=+u.opacity),u.transform&&(p=iP({left:t,top:r,width:n,height:o},u.transform,i,u.transformOrigin));let m="";if(u.backgroundImage){let i=[];for(let a=0;a<u.backgroundImage.length;a++){let s=u.backgroundImage[a],c=await iG({id:e+"_"+a,width:n,height:o,left:t,top:r},s,l);c&&i.unshift(c)}for(let e of i)d.push(`url(#${e[0]})`),h+=e[1],e[2]&&(m+=e[2])}let[y,D]=await i1({id:e,left:t,top:r,width:n,height:o},u,l);h+=D;let b=y?`url(#${y})`:u._inheritedMaskId?`url(#${u._inheritedMaskId})`:void 0,x=iH({left:t,top:r,width:n,height:o},u);x&&(f="path");let w=u._inheritedClipPathId;s&&(v=o3("rect",{x:t,y:r,width:n,height:o,fill:"transparent",stroke:"#ff5757","stroke-width":1,transform:p||void 0,"clip-path":w?`url(#${w})`:void 0}));let{backgroundClip:E,filter:S}=u,k="text"===E?`url(#satori_bct-${e})`:w?`url(#${w})`:u.clipPath?`url(#${iJ(e)})`:void 0,F=function({left:e,top:t,width:r,height:n,path:o,matrix:i,id:a,currentClipPath:s,src:u},l,c){let f="",p=l.clipPath&&"none"!==l.clipPath?function(e,t,r){if("none"===t.clipPath)return"";let n=function({width:e,height:t},r,n){return{parseCircle:function(r){let o=r.match(iZ.circle);if(!o)return null;let[,i]=o,[a,s=""]=i.split("at").map(e=>e.trim()),{x:u,y:l}=iK(s,e,t);return{type:"circle",r:oQ(a,n.fontSize,Math.sqrt(Math.pow(e,2)+Math.pow(t,2))/Math.sqrt(2),n,!0),cx:oQ(u,n.fontSize,e,n,!0),cy:oQ(l,n.fontSize,t,n,!0)}},parseEllipse:function(r){let o=r.match(iZ.ellipse);if(!o)return null;let[,i]=o,[a,s=""]=i.split("at").map(e=>e.trim()),[u,l]=a.split(" "),{x:c,y:f}=iK(s,e,t);return{type:"ellipse",rx:oQ(u||"50%",n.fontSize,e,n,!0),ry:oQ(l||"50%",n.fontSize,t,n,!0),cx:oQ(c,n.fontSize,e,n,!0),cy:oQ(f,n.fontSize,t,n,!0)}},parsePath:function(e){let t=e.match(iZ.path);if(!t)return null;let[r,n]=iQ(t[1]);return{type:"path",d:n,"fill-rule":r}},parsePolygon:function(r){let o=r.match(iZ.polygon);if(!o)return null;let[i,a]=iQ(o[1]);return{type:"polygon","fill-rule":i,points:a.split(",").map(r=>r.split(" ").map((r,o)=>oQ(r,n.fontSize,0===o?e:t,n,!0)).join(" ")).join(",")}},parseInset:function(o){let i=o.match(iZ.inset);if(!i)return null;let[a,s]=(i[1].includes("round")?i[1]:`${i[1].trim()} round 0`).split("round"),u=(0,eR.getStylesForProperty)("borderRadius",s,!0),l=Object.values(u).map(e=>String(e)).map((r,o)=>oQ(r,n.fontSize,0===o||2===o?t:e,n,!0)||0),c=Object.values((0,eR.getStylesForProperty)("margin",a,!0)).map(e=>String(e)).map((r,o)=>oQ(r,n.fontSize,0===o||2===o?t:e,n,!0)||0),f=c[3],p=c[0],h=e-(c[1]+c[3]),d=t-(c[0]+c[2]);return l.some(e=>e>0)?{type:"path",d:iH({left:f,top:p,width:h,height:d},{...r,...u})}:{type:"rect",x:f,y:p,width:h,height:d}}}}(e,t,r),o=t.clipPath,i={type:""};for(let e of Object.keys(n))if(i=n[e](o))break;if(i){let{type:t,...r}=i;return o3("clipPath",{id:iJ(e.id),"clip-path":e.currentClipPath,transform:`translate(${e.left}, ${e.top})`},o3(t,r))}return""}({left:e,top:t,width:r,height:n,path:o,id:a,matrix:i,currentClipPath:s,src:u},l,c):"";return f="hidden"===l.overflow||u?o3("clipPath",{id:p?`satori_ocp-${a}`:iJ(a),"clip-path":s},o3(o?"path":"rect",{x:e,y:t,width:r,height:n,d:o||void 0,transform:"hidden"===l.overflow&&l.transform&&i?i:void 0})):"",p+f+function({id:e,left:t,top:r,width:n,height:o,matrix:i,borderOnly:a},s){let u=(s.borderLeftWidth||0)+(a?0:s.paddingLeft||0),l=(s.borderTopWidth||0)+(a?0:s.paddingTop||0);return o3("mask",{id:e},o3("rect",{...{x:t+u,y:r+l,width:n-u-((s.borderRightWidth||0)+(a?0:s.paddingRight||0)),height:o-l-((s.borderBottomWidth||0)+(a?0:s.paddingBottom||0))},fill:"#fff",transform:"hidden"===s.overflow&&s.transform&&i?i:void 0,mask:s._inheritedMaskId?`url(#${s._inheritedMaskId})`:void 0})+iY({left:t,top:r,width:n,height:o,props:{transform:i||void 0},asContentMask:!0,maskBorderOnly:a},s))}({id:`satori_om-${a}`,left:e,top:t,width:r,height:n,matrix:i,borderOnly:!u},l)}({left:t,top:r,width:n,height:o,path:x,id:e,matrix:p,currentClipPath:k,src:a},u,l),C=d.map(e=>o3(f,{x:t,y:r,width:n,height:o,fill:e,d:x||void 0,transform:p||void 0,"clip-path":u.transform?void 0:k,style:S?`filter:${S}`:void 0,mask:u.transform?void 0:b})).join(""),_=function({id:e,currentClipPathId:t,borderPath:r,borderType:n,left:o,top:i,width:a,height:s},u){if(!(u.borderTopWidth||u.borderRightWidth||u.borderBottomWidth||u.borderLeftWidth))return null;let l=`satori_bc-${e}`;return[o3("clipPath",{id:l,"clip-path":t?`url(#${t})`:void 0},o3(n,{x:o,y:i,width:a,height:s,d:r||void 0})),l]}({id:e,left:t,top:r,width:n,height:o,currentClipPathId:w,borderPath:x,borderType:f},u),T;if(c){let i=(u.borderLeftWidth||0)+(u.paddingLeft||0),s=(u.borderTopWidth||0)+(u.paddingTop||0),l=(u.borderRightWidth||0)+(u.paddingRight||0),c=(u.borderBottomWidth||0)+(u.paddingBottom||0),h="contain"===u.objectFit?"xMidYMid":"cover"===u.objectFit?"xMidYMid slice":"none";u.transform&&(T=function({id:e,borderRadiusPath:t,borderType:r,left:n,top:o,width:i,height:a},s){let u=`satori_brc-${e}`;return[o3("clipPath",{id:u},o3(r,{x:n,y:o,width:i,height:a,d:t||void 0})),u]}({id:e,borderRadiusPath:x,borderType:f,left:t,top:r,width:n,height:o},0)),C+=o3("image",{x:t+i,y:r+s,width:n-i-l,height:o-s-c,href:a,preserveAspectRatio:h,transform:p||void 0,style:S?`filter:${S}`:void 0,"clip-path":u.transform?T?`url(#${T[1]})`:void 0:`url(#satori_cp-${e})`,mask:u.transform?void 0:y?`url(#${y})`:`url(#satori_om-${e})`})}if(_){h+=_[0];let e=_[1];C+=iY({left:t,top:r,width:n,height:o,props:{transform:p||void 0,"clip-path":`url(#${e})`}},u)}let O=function({width:e,height:t,shape:r,opacity:n,id:o},i){if(!i.boxShadow)return null;let a="",s="";for(let u=i.boxShadow.length-1;u>=0;u--){let l="",c=i.boxShadow[u];c.spreadRadius&&c.inset&&(c.spreadRadius=-c.spreadRadius);let f=c.blurRadius*c.blurRadius/4+(c.spreadRadius||0),p=Math.min(-f-(c.inset?c.offsetX:0),0),h=Math.max(f+e-(c.inset?c.offsetX:0),e),d=Math.min(-f-(c.inset?c.offsetY:0),0),g=Math.max(f+t-(c.inset?c.offsetY:0),t),v=`satori_s-${o}-${u}`,m=`satori_ms-${o}-${u}`,y=c.spreadRadius?r.replace('stroke-width="0"',`stroke-width="${2*c.spreadRadius}"`):r;l+=o3("mask",{id:m,maskUnits:"userSpaceOnUse"},o3("rect",{x:0,y:0,width:i._viewportWidth||"100%",height:i._viewportHeight||"100%",fill:c.inset?"#000":"#fff"})+y.replace('fill="#fff"',c.inset?'fill="#fff"':'fill="#000"').replace('stroke="#fff"',""));let D=y.replace(/d="([^"]+)"/,(e,t)=>'d="'+function(e,t,r){return e.replace(/([MA])([0-9.-]+),([0-9.-]+)/g,function(e,n,o,i){return n+(parseFloat(o)+t)+","+(parseFloat(i)+r)})}(t,c.offsetX,c.offsetY)+'"').replace(/x="([^"]+)"/,(e,t)=>'x="'+(parseFloat(t)+c.offsetX)+'"').replace(/y="([^"]+)"/,(e,t)=>'y="'+(parseFloat(t)+c.offsetY)+'"');c.spreadRadius&&c.spreadRadius<0&&(l+=o3("mask",{id:m+"-neg",maskUnits:"userSpaceOnUse"},D.replace('stroke="#fff"','stroke="#000"').replace(/stroke-width="[^"]+"/,`stroke-width="${-(2*c.spreadRadius)}"`))),c.spreadRadius&&c.spreadRadius<0&&(D=o3("g",{mask:`url(#${m}-neg)`},D)),l+=o3("defs",{},o3("filter",{id:v,x:`${p/e*100}%`,y:`${d/t*100}%`,width:`${(h-p)/e*100}%`,height:`${(g-d)/t*100}%`},o3("feGaussianBlur",{stdDeviation:c.blurRadius/2,result:"b"})+o3("feFlood",{"flood-color":c.color,in:"SourceGraphic",result:"f"})+o3("feComposite",{in:"f",in2:"b",operator:c.inset?"out":"in"})))+o3("g",{mask:`url(#${m})`,filter:`url(#${v})`,opacity:n},D),c.inset?s+=l:a+=l}return[a,s]}({width:n,height:o,id:e,opacity:g,shape:o3(f,{x:t,y:r,width:n,height:o,fill:"#fff",stroke:"#fff","stroke-width":0,d:x||void 0,transform:p||void 0,"clip-path":k,mask:b})},u);return(h?o3("defs",{},h):"")+(O?O[0]:"")+(T?T[0]:"")+F+(1!==g?`<g opacity="${g}">`:"")+(u.transform&&(k||b)?`<g${k?` clip-path="${k}"`:""}${b?` mask="${b}"`:""}>`:"")+(m||C)+(u.transform&&(k||b)?"</g>":"")+(1!==g?"</g>":"")+(O?O[1]:"")+v}var i3={emoji:RegExp(/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26F9(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC3\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC08\uDC26](?:\u200D\u2B1B)?|[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE88\uDE90-\uDEBD\uDEBF-\uDEC2\uDECE-\uDEDB\uDEE0-\uDEE8]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g,""),symbol:/\p{Symbol}/u,math:/\p{Math}/u},i4={"ja-JP":/\p{scx=Hira}|\p{scx=Kana}|\p{scx=Han}|[\u3000]|[\uFF00-\uFFEF]/u,"ko-KR":/\p{scx=Hangul}/u,"zh-CN":/\p{scx=Han}/u,"zh-TW":/\p{scx=Han}/u,"zh-HK":/\p{scx=Han}/u,"th-TH":/\p{scx=Thai}/u,"bn-IN":/\p{scx=Bengali}/u,"ar-AR":/\p{scx=Arabic}/u,"ta-IN":/\p{scx=Tamil}/u,"ml-IN":/\p{scx=Malayalam}/u,"he-IL":/\p{scx=Hebrew}/u,"te-IN":/\p{scx=Telugu}/u,devanagari:/\p{scx=Devanagari}/u,kannada:/\p{scx=Kannada}/u},i5=Object.keys({...i4,...i3});async function*i6(e,t){var r,n;let o=await oP(),{id:i,inheritedStyle:a,parent:s,font:u,debug:l,locale:c,embedFont:f=!0,graphemeImages:p,canLoadAdditionalAssets:h,getTwStyles:d}=t;if(null===e||typeof e>"u")return yield,yield,"";if(!oZ(e)||"function"==typeof e.type){let r;if(oZ(e)){if(n=e.type,/^class\s/.test(n.toString()))throw Error("Class component is not supported.");r=i6(e.type(e.props),t),yield(await r.next()).value}else r=iM(String(e),t),yield(await r.next()).value;await r.next();let o=yield;return(await r.next(o)).value}let{type:g,props:v}=e;if(v&&"dangerouslySetInnerHTML"in v)throw Error("dangerouslySetInnerHTML property is not supported. See documentation for more information https://github.com/vercel/satori#jsx.");let{style:m,children:y,tw:D,lang:b=c}=v||{},x=function(e){if(e)return i5.find(t=>t.toLowerCase().startsWith(e.toLowerCase()))}(b);D&&(m=Object.assign(d(D,m),m));let w=o.Node.create();s.insertChild(w,s.getChildCount());let[E,S]=await iO(w,g,a,m,v),k=E.transform===a.transform;if(k||(E.transform.__parent=a.transform),("hidden"===E.overflow||E.clipPath&&"none"!==E.clipPath)&&(S._inheritedClipPathId=`satori_cp-${i}`,S._inheritedMaskId=`satori_om-${i}`),E.maskImage&&(S._inheritedMaskId=`satori_mi-${i}`),"text"===E.backgroundClip){let e={value:""};S._inheritedBackgroundClipTextPath=e,E._inheritedBackgroundClipTextPath=e}let F=function(e){let t=typeof e>"u"?[]:[].concat(e).flat(1/0),r=[];for(let e=0;e<t.length;e++){let n=t[e];typeof n>"u"||"boolean"==typeof n||null===n||("number"==typeof n&&(n=String(n)),"string"==typeof n&&r.length&&"string"==typeof r[r.length-1]?r[r.length-1]+=n:r.push(n))}return r}(y),C=[],_=0,T=[];for(let e of F){let r=i6(e,{id:i+"-"+_++,parentStyle:E,inheritedStyle:S,isInheritingTransform:!0,parent:w,font:u,embedFont:f,debug:l,graphemeImages:p,canLoadAdditionalAssets:h,locale:x,getTwStyles:d,onNodeDetected:t.onNodeDetected});h?T.push(...(await r.next()).value||[]):await r.next(),C.push(r)}for(let e of(yield T,C))await e.next();let[O,A]=yield,{left:P,top:L,width:I,height:B}=w.getComputedLayout();P+=O,L+=A;let R="",U="",M="",{children:N,...j}=v;if(null==(r=t.onNodeDetected)||r.call(t,{left:P,top:L,width:I,height:B,type:g,props:j,key:e.key,textContent:oZ(N)?void 0:N}),"img"===g){let e=E.__src;U=await i2({id:i,left:P,top:L,width:I,height:B,src:e,isInheritingTransform:k,debug:l},E,S)}else if("svg"===g){let t=E.color,r=await im(e,t);U=await i2({id:i,left:P,top:L,width:I,height:B,src:r,isInheritingTransform:k,debug:l},E,S)}else{let e=null==m?void 0:m.display;if("div"===g&&y&&"string"!=typeof y&&"flex"!==e&&"none"!==e)throw Error('Expected <div> to have explicit "display: flex" or "display: none" if it has more than one child node.');U=await i2({id:i,left:P,top:L,width:I,height:B,isInheritingTransform:k,debug:l},E,S)}for(let e of C)R+=(await e.next([P,L])).value;return E._inheritedBackgroundClipTextPath&&(M+=o3("clipPath",{id:`satori_bct-${i}`,"clip-path":E._inheritedClipPathId?`url(#${E._inheritedClipPathId})`:void 0},E._inheritedBackgroundClipTextPath.value)),M+U+R}var i8="unknown",i7=class{defaultFont;fonts=new Map;constructor(e){this.addFonts(e)}get({name:e,weight:t,style:r}){if(!this.fonts.has(e))return null;"normal"===t&&(t=400),"bold"===t&&(t=700),"string"==typeof t&&(t=Number.parseInt(t,10));let n=[...this.fonts.get(e)],o=n[0];for(let e=1;e<n.length;e++){let[,i,a]=o,[,s,u]=n[e];(function(e,t,[r,n],[o,i]){if(r!==o)return r?o&&r!==e?o===e?1:400===e&&500===r||500===e&&400===r?-1:400===e&&500===o||500===e&&400===o?1:e<400?r<e&&o<e?o-r:r<e?-1:o<e?1:r-o:e<r&&e<o?r-o:e<r?-1:e<o?1:o-r:-1:1;if(n!==i){if(n===t)return -1;if(i===t)return 1}return -1})(t,r,[i,a],[s,u])>0&&(o=n[e])}return o[0]}addFonts(e){for(let t of e){let{name:e,data:r,lang:n}=t;if(n&&!i5.includes(n))throw Error(`Invalid value for props \`lang\`: "${n}". The value must be one of the following: ${i5.join(", ")}.`);let o=n??i8,i=nD.parse("buffer"in r?r.buffer.slice(r.byteOffset,r.byteOffset+r.byteLength):r,{lowMemory:!0}),a=i.charToGlyphIndex;i.charToGlyphIndex=e=>{let t=a.call(i,e);return 0===t&&i._trackBrokenChars&&i._trackBrokenChars.push(e),t},this.defaultFont||(this.defaultFont=i);let s=`${e.toLowerCase()}_${o}`;this.fonts.has(s)||this.fonts.set(s,[]),this.fonts.get(s).push([i,t.weight,t.style])}}getEngine(e=16,t="normal",{fontFamily:r="sans-serif",fontWeight:n=400,fontStyle:o="normal"},i){if(!this.fonts.size)throw Error("No fonts are loaded. At least one font is required to calculate the layout.");r=(Array.isArray(r)?r:[r]).map(e=>e.toLowerCase());let a=[];r.forEach(e=>{let t=this.get({name:e,weight:n,style:o});if(t)return void a.push(t);let r=this.get({name:e+"_unknown",weight:n,style:o});if(r)return void a.push(r)});let s=Array.from(this.fonts.keys()),u=[],l=[],c=[];for(let e of s)if(!r.includes(e))if(i){let t=function(e){let t=e.split("_"),r=t[t.length-1];return r===i8?void 0:r}(e);t?t===i?u.push(this.get({name:e,weight:n,style:o})):l.push(this.get({name:e,weight:n,style:o})):c.push(this.get({name:e,weight:n,style:o}))}else c.push(this.get({name:e,weight:n,style:o}));let f=new Map,p=(e,t=!0)=>{let r=[...a,...c,...u,...t?l:[]];if(typeof e>"u")return t?r[r.length-1]:void 0;let n=e.charCodeAt(0);if(f.has(n))return f.get(n);let o=r.find((n,o)=>!!n.charToGlyphIndex(e)||t&&o===r.length-1);return o&&f.set(n,o),o},h=(t,r=!1)=>{var n,o;return((r?null==(o=null==(n=t.tables)?void 0:n.os2)?void 0:o.sTypoAscender:0)||t.ascender)/t.unitsPerEm*e},d=(t,r=!1)=>{var n,o;return((r?null==(o=null==(n=t.tables)?void 0:n.os2)?void 0:o.sTypoDescender:0)||t.descender)/t.unitsPerEm*e},g=(r,n=!1)=>{var o,i;if("string"==typeof t&&"normal"===t){let t=(n?null==(i=null==(o=r.tables)?void 0:o.os2)?void 0:i.sTypoLineGap:0)||0;return h(r,n)-d(r,n)+t/r.unitsPerEm*e}if("number"==typeof t)return e*t},v=e=>p(e,!1);return{has:e=>{if(e===`
`)return!0;let t=v(e);return!!t&&(t._trackBrokenChars=[],t.stringToGlyphs(e),!t._trackBrokenChars.length||(t._trackBrokenChars=void 0,!1))},baseline:(e,t=typeof e>"u"?a[0]:p(e))=>{let r=h(t),n=d(t);return r+(g(t)-(r-n))/2},height:(e,t=typeof e>"u"?a[0]:p(e))=>g(t),measure:(e,t)=>this.measure(p,e,t),getSVG:(e,t)=>this.getSVG(p,e,t)}}patchFontFallbackResolver(e,t){let r=[];e._trackBrokenChars=r;let n=e.stringToGlyphs;return e.stringToGlyphs=(o,...i)=>{let a=n.call(e,o,...i);for(let n=0;n<a.length;n++)if(void 0===a[n].unicode){let o=r.shift(),i=t(o);if(i!==e){let t=i.charToGlyph(o),r=e.unitsPerEm/i.unitsPerEm,s=new nD.Path;s.unitsPerEm=e.unitsPerEm,s.commands=t.path.commands.map(e=>{let t={...e};for(let e in t)"number"==typeof t[e]&&(t[e]*=r);return t});let u=new nD.Glyph({...t,advanceWidth:t.advanceWidth*r,xMin:t.xMin*r,xMax:t.xMax*r,yMin:t.yMin*r,yMax:t.yMax*r,path:s});a[n]=u}}return a},()=>{e.stringToGlyphs=n,e._trackBrokenChars=void 0}}measure(e,t,{fontSize:r,letterSpacing:n=0}){let o=e(t),i=this.patchFontFallbackResolver(o,e);try{return o.getAdvanceWidth(t,r,{letterSpacing:n/r})}finally{i()}}getSVG(e,t,{fontSize:r,top:n,left:o,letterSpacing:i=0}){let a=e(t),s=this.patchFontFallbackResolver(a,e);try{return 0===r?"":a.getPath(t.replace(/\n/g,""),o,n,r,{letterSpacing:i/r}).toPathData(1)}finally{s()}}},i9=((e,t,r)=>(r=null!=e?nb(nS(e)):{},nT(!t&&e&&e.__esModule?r:nx(r,"default",{value:e,enumerable:!0}),e)))(oO()),ae=["ios","android","windows","macos","web"],at=["portrait","landscape"];function ar(e){return"string"==typeof e}function an(e){return"object"==typeof e}function ao(e){return{kind:"complete",style:e}}function ai(e,t={}){let{fractions:r}=t;if(r&&e.includes("/")){let[t="",r=""]=e.split("/",2),n=ai(t),o=ai(r);return n&&o?[n[0]/o[0],o[1]]:null}let n=parseFloat(e);if(Number.isNaN(n))return null;let o=e.match(/(([a-z]{2,}|%))$/);if(!o)return[n,g.none];switch(null==o?void 0:o[1]){case"rem":return[n,g.rem];case"px":return[n,g.px];case"em":return[n,g.em];case"%":return[n,g.percent];case"vw":return[n,g.vw];case"vh":return[n,g.vh];default:return null}}function aa(e,t,r={}){let n=au(t,r);return null===n?null:ao({[e]:n})}function as(e,t,r){let n=au(t);return null!==n&&(r[e]=n),r}function au(e,t={}){if(void 0===e)return null;let r=ai(String(e),t);return r?al(...r,t):null}function al(e,t,r={}){let{isNegative:n,device:o}=r;switch(t){case g.rem:return 16*e*(n?-1:1);case g.px:return e*(n?-1:1);case g.percent:return`${n?"-":""}${e}%`;case g.none:return e*(n?-1:1);case g.vw:return null!=o&&o.windowDimensions?o.windowDimensions.width*(e/100):(am("`vw` CSS unit requires configuration with `useDeviceContext()`"),null);case g.vh:return null!=o&&o.windowDimensions?o.windowDimensions.height*(e/100):(am("`vh` CSS unit requires configuration with `useDeviceContext()`"),null);default:return null}}function ac(e){let t=ai(e);if(!t)return null;let[r,n]=t;switch(n){case g.rem:return 16*r;case g.px:return r;default:return null}}!function(e){e.fontSize="fontSize",e.lineHeight="lineHeight"}(d||(d={})),function(e){e.rem="rem",e.em="em",e.px="px",e.percent="%",e.vw="vw",e.vh="vh",e.none="<no-css-unit>"}(g||(g={}));var af={t:"Top",tr:"TopRight",tl:"TopLeft",b:"Bottom",br:"BottomRight",bl:"BottomLeft",l:"Left",r:"Right",x:"Horizontal",y:"Vertical"};function ap(e){return af[e??""]||"All"}function ah(e){let t="All";return[e.replace(/^-(t|b|r|l|tr|tl|br|bl)(-|$)/,(e,r)=>(t=ap(r),"")),t]}function ad(e,t={}){if(e.includes("/")){let r=av(e,{...t,fractions:!0});if(r)return r}return"["===e[0]&&(e=e.slice(1,-1)),av(e,t)}function ag(e,t,r={}){let n=ad(t,r);return null===n?null:ao({[e]:n})}function av(e,t={}){if("px"===e)return 1;let r=ai(e,t);if(!r)return null;let[n,o]=r;return t.fractions&&(o=g.percent,n*=100),o===g.none&&(n/=4,o=g.rem),al(n,o,t)}var am=typeof process>"u"||(null==(v=null==process?void 0:process.env)?void 0:v.JEST_WORKER_ID)===void 0?function(...e){console.warn(...e)}:function(...e){},ay=[["aspect-square",ao({aspectRatio:1})],["aspect-video",ao({aspectRatio:16/9})],["items-center",ao({alignItems:"center"})],["items-start",ao({alignItems:"flex-start"})],["items-end",ao({alignItems:"flex-end"})],["items-baseline",ao({alignItems:"baseline"})],["items-stretch",ao({alignItems:"stretch"})],["justify-start",ao({justifyContent:"flex-start"})],["justify-end",ao({justifyContent:"flex-end"})],["justify-center",ao({justifyContent:"center"})],["justify-between",ao({justifyContent:"space-between"})],["justify-around",ao({justifyContent:"space-around"})],["justify-evenly",ao({justifyContent:"space-evenly"})],["content-start",ao({alignContent:"flex-start"})],["content-end",ao({alignContent:"flex-end"})],["content-between",ao({alignContent:"space-between"})],["content-around",ao({alignContent:"space-around"})],["content-stretch",ao({alignContent:"stretch"})],["content-center",ao({alignContent:"center"})],["self-auto",ao({alignSelf:"auto"})],["self-start",ao({alignSelf:"flex-start"})],["self-end",ao({alignSelf:"flex-end"})],["self-center",ao({alignSelf:"center"})],["self-stretch",ao({alignSelf:"stretch"})],["self-baseline",ao({alignSelf:"baseline"})],["direction-inherit",ao({direction:"inherit"})],["direction-ltr",ao({direction:"ltr"})],["direction-rtl",ao({direction:"rtl"})],["hidden",ao({display:"none"})],["flex",ao({display:"flex"})],["flex-row",ao({flexDirection:"row"})],["flex-row-reverse",ao({flexDirection:"row-reverse"})],["flex-col",ao({flexDirection:"column"})],["flex-col-reverse",ao({flexDirection:"column-reverse"})],["flex-wrap",ao({flexWrap:"wrap"})],["flex-wrap-reverse",ao({flexWrap:"wrap-reverse"})],["flex-nowrap",ao({flexWrap:"nowrap"})],["flex-auto",ao({flexGrow:1,flexShrink:1,flexBasis:"auto"})],["flex-initial",ao({flexGrow:0,flexShrink:1,flexBasis:"auto"})],["flex-none",ao({flexGrow:0,flexShrink:0,flexBasis:"auto"})],["overflow-hidden",ao({overflow:"hidden"})],["overflow-visible",ao({overflow:"visible"})],["overflow-scroll",ao({overflow:"scroll"})],["absolute",ao({position:"absolute"})],["relative",ao({position:"relative"})],["italic",ao({fontStyle:"italic"})],["not-italic",ao({fontStyle:"normal"})],["oldstyle-nums",aD("oldstyle-nums")],["small-caps",aD("small-caps")],["lining-nums",aD("lining-nums")],["tabular-nums",aD("tabular-nums")],["proportional-nums",aD("proportional-nums")],["font-thin",ao({fontWeight:"100"})],["font-100",ao({fontWeight:"100"})],["font-extralight",ao({fontWeight:"200"})],["font-200",ao({fontWeight:"200"})],["font-light",ao({fontWeight:"300"})],["font-300",ao({fontWeight:"300"})],["font-normal",ao({fontWeight:"normal"})],["font-400",ao({fontWeight:"400"})],["font-medium",ao({fontWeight:"500"})],["font-500",ao({fontWeight:"500"})],["font-semibold",ao({fontWeight:"600"})],["font-600",ao({fontWeight:"600"})],["font-bold",ao({fontWeight:"bold"})],["font-700",ao({fontWeight:"700"})],["font-extrabold",ao({fontWeight:"800"})],["font-800",ao({fontWeight:"800"})],["font-black",ao({fontWeight:"900"})],["font-900",ao({fontWeight:"900"})],["include-font-padding",ao({includeFontPadding:!0})],["remove-font-padding",ao({includeFontPadding:!1})],["max-w-none",ao({maxWidth:"99999%"})],["text-left",ao({textAlign:"left"})],["text-center",ao({textAlign:"center"})],["text-right",ao({textAlign:"right"})],["text-justify",ao({textAlign:"justify"})],["text-auto",ao({textAlign:"auto"})],["underline",ao({textDecorationLine:"underline"})],["line-through",ao({textDecorationLine:"line-through"})],["no-underline",ao({textDecorationLine:"none"})],["uppercase",ao({textTransform:"uppercase"})],["lowercase",ao({textTransform:"lowercase"})],["capitalize",ao({textTransform:"capitalize"})],["normal-case",ao({textTransform:"none"})],["w-auto",ao({width:"auto"})],["h-auto",ao({height:"auto"})],["shadow-sm",ao({shadowOffset:{width:1,height:1},shadowColor:"#000",shadowRadius:1,shadowOpacity:.025,elevation:1})],["shadow",ao({shadowOffset:{width:1,height:1},shadowColor:"#000",shadowRadius:1,shadowOpacity:.075,elevation:2})],["shadow-md",ao({shadowOffset:{width:1,height:1},shadowColor:"#000",shadowRadius:3,shadowOpacity:.125,elevation:3})],["shadow-lg",ao({shadowOffset:{width:1,height:1},shadowColor:"#000",shadowOpacity:.15,shadowRadius:8,elevation:8})],["shadow-xl",ao({shadowOffset:{width:1,height:1},shadowColor:"#000",shadowOpacity:.19,shadowRadius:20,elevation:12})],["shadow-2xl",ao({shadowOffset:{width:1,height:1},shadowColor:"#000",shadowOpacity:.25,shadowRadius:30,elevation:16})],["shadow-none",ao({shadowOffset:{width:0,height:0},shadowColor:"#000",shadowRadius:0,shadowOpacity:0,elevation:0})]];function aD(e){return{kind:"dependent",complete(t){t.fontVariant&&Array.isArray(t.fontVariant)||(t.fontVariant=[]),t.fontVariant.push(e)}}}var ab=class{constructor(e){this.ir=new Map(ay),this.styles=new Map,this.prefixes=new Map,this.ir=new Map([...ay,...e??[]])}getStyle(e){return this.styles.get(e)}setStyle(e,t){this.styles.set(e,t)}getIr(e){return this.ir.get(e)}setIr(e,t){this.ir.set(e,t)}getPrefixMatch(e){return this.prefixes.get(e)}setPrefixMatch(e,t){this.prefixes.set(e,t)}};function ax(e,t){let r=ai(e);if(r){let[e,n]=r;if((n===g.none||n===g.em)&&"number"==typeof t.fontSize)return t.fontSize*e}return e}function aw(e,t,r,n){let o=al(e,t);return null===o?null:aE(r,n,o)}function aE(e,t,r){switch(e){case"All":return{kind:"complete",style:{[`${t}Top`]:r,[`${t}Right`]:r,[`${t}Bottom`]:r,[`${t}Left`]:r}};case"Bottom":case"Top":case"Left":case"Right":return{kind:"complete",style:{[`${t}${e}`]:r}};case"Vertical":return{kind:"complete",style:{[`${t}Top`]:r,[`${t}Bottom`]:r}};case"Horizontal":return{kind:"complete",style:{[`${t}Left`]:r,[`${t}Right`]:r}};default:return null}}function aS(e,t,r){let n;if(!r)return null;t.includes("/")&&([t="",n]=t.split("/",2));let o="";if(!(o=t.startsWith("[#")||t.startsWith("[rgb")?t.slice(1,-1):function e(t,r){let n=r[t];if(ar(n))return n;if(an(n)&&ar(n.DEFAULT))return n.DEFAULT;let[o="",...i]=t.split("-");for(;o!==t;){let t=r[o];if(an(t))return e(i.join("-"),t);if(0===i.length)break;o=`${o}-${i.shift()}`}return""}(t,r)))return null;if(n){let t=Number(n);if(!Number.isNaN(t))return o=aF(o,t/100),ao({[aC[e].color]:o})}return{kind:"dependent",complete(t){let r=t[aC[e].opacity];"number"==typeof r&&(o=aF(o,r)),t[aC[e].color]=o}}}function ak(e,t){let r=parseInt(t,10);return Number.isNaN(r)?null:{kind:"complete",style:{[aC[e].opacity]:r/100}}}function aF(e,t){return e.startsWith("#")?e=function(e){let t=e,r=aT.exec(e=e.replace(a_,(e,t,r,n)=>t+t+r+r+n+n));if(!r)return am(`invalid config hex color value: ${t}`),"rgba(0, 0, 0, 1)";let n=parseInt(r[1],16),o=parseInt(r[2],16),i=parseInt(r[3],16);return`rgba(${n}, ${o}, ${i}, 1)`}(e):e.startsWith("rgb(")&&(e=e.replace(/^rgb\(/,"rgba(").replace(/\)$/,", 1)")),e.replace(/, ?\d*\.?(\d+)\)$/,`, ${t})`)}var aC={bg:{opacity:"__opacity_bg",color:"backgroundColor"},text:{opacity:"__opacity_text",color:"color"},border:{opacity:"__opacity_border",color:"borderColor"},borderTop:{opacity:"__opacity_border",color:"borderTopColor"},borderBottom:{opacity:"__opacity_border",color:"borderBottomColor"},borderLeft:{opacity:"__opacity_border",color:"borderLeftColor"},borderRight:{opacity:"__opacity_border",color:"borderRightColor"},shadow:{opacity:"__opacity_shadow",color:"shadowColor"},tint:{opacity:"__opacity_tint",color:"tintColor"}},a_=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,aT=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i;function aO(e){if((null==e?void 0:e.kind)!=="complete")return e;let t=e.style.borderTopRadius;void 0!==t&&(e.style.borderTopLeftRadius=t,e.style.borderTopRightRadius=t,delete e.style.borderTopRadius);let r=e.style.borderBottomRadius;void 0!==r&&(e.style.borderBottomLeftRadius=r,e.style.borderBottomRightRadius=r,delete e.style.borderBottomRadius);let n=e.style.borderLeftRadius;void 0!==n&&(e.style.borderBottomLeftRadius=n,e.style.borderTopLeftRadius=n,delete e.style.borderLeftRadius);let o=e.style.borderRightRadius;return void 0!==o&&(e.style.borderBottomRightRadius=o,e.style.borderTopRightRadius=o,delete e.style.borderRightRadius),e}function aA(e,t,r,n){let o=null;"inset"===e&&(t=t.replace(/^(x|y)-/,(e,t)=>(o="x"===t?"x":"y","")));let i=null==n?void 0:n[t];if(i){let t=au(i,{isNegative:r});if(null!==t)return aP(e,o,t)}let a=ad(t,{isNegative:r});return null!==a?aP(e,o,a):null}function aP(e,t,r){if("inset"!==e)return ao({[e]:r});switch(t){case null:return ao({top:r,left:r,right:r,bottom:r});case"y":return ao({top:r,bottom:r});case"x":return ao({left:r,right:r})}}function aL(e,t,r){var n;let o=""===(t=t.replace(/^-/,""))?"DEFAULT":t,i=Number(null!=(n=null==r?void 0:r[o])?n:t);return Number.isNaN(i)?null:ao({[`flex${e}`]:i})}function aI(e,t,r={},n){let o=null==n?void 0:n[t];return void 0!==o?aa(e,o,r):ag(e,t,r)}function aB(e,t,r={},n){let o=au(null==n?void 0:n[t],r);return o?ao({[e]:o}):("screen"===t&&(t=e.includes("Width")?"100vw":"100vh"),ag(e,t,r))}function aR(e){let t=ad(e);return"number"==typeof t?t:null}var aU=class{constructor(e,t={},r,n,o){var i,a,s,u,l,c;this.config=t,this.cache=r,this.position=0,this.isNull=!1,this.isNegative=!1,this.context={},this.context.device=n;let f=e.trim().split(":"),p=[];1===f.length?this.string=e:(this.string=null!=(i=f.pop())?i:"",p=f),this.char=this.string[0];let h=function(e){if(!e)return{};let t=Object.entries(e).reduce((e,[t,r])=>{let n=[0,1/0,0],o="string"==typeof r?{min:r}:r,i=o.min?ac(o.min):0;null===i?am(`invalid screen config value: ${t}->min: ${o.min}`):n[0]=i;let a=o.max?ac(o.max):1/0;return null===a?am(`invalid screen config value: ${t}->max: ${o.max}`):n[1]=a,e[t]=n,e},{}),r=Object.values(t);r.sort((e,t)=>{let[r,n]=e,[o,i]=t;return n===1/0||i===1/0?r-o:n-i});let n=0;return r.forEach(e=>e[2]=n++),t}(null==(a=this.config.theme)?void 0:a.screens);for(let e of p)if(h[e]){let t=null==(s=h[e])?void 0:s[2];void 0!==t&&(this.order=(null!=(u=this.order)?u:0)+t);let r=null==(l=n.windowDimensions)?void 0:l.width;if(r){let[t,n]=null!=(c=h[e])?c:[0,0];(r<=t||r>n)&&(this.isNull=!0)}else this.isNull=!0}else ae.includes(e)?this.isNull=e!==o:at.includes(e)?n.windowDimensions?(n.windowDimensions.width>n.windowDimensions.height?"landscape":"portrait")!==e?this.isNull=!0:this.incrementOrder():this.isNull=!0:"retina"===e?2===n.pixelDensity?this.incrementOrder():this.isNull=!0:"dark"===e?"dark"!==n.colorScheme?this.isNull=!0:this.incrementOrder():this.handlePossibleArbitraryBreakpointPrefix(e)||(this.isNull=!0)}parse(){if(this.isNull)return{kind:"null"};let e=this.cache.getIr(this.rest);if(e)return e;this.parseIsNegative();let t=this.parseUtility();return t?void 0!==this.order?{kind:"ordered",order:this.order,styleIr:t}:t:{kind:"null"}}parseUtility(){var e,t,r,n,o;let i,a=this.config.theme,s=null;switch(this.char){case"m":case"p":{let n=this.peekSlice(1,3).match(/^(t|b|r|l|x|y)?-/);if(n){let o="m"===this.char?"margin":"padding";this.advance((null!=(t=null==(e=n[0])?void 0:e.length)?t:0)+1);let i=function(e,t,r,n,o){let i="";if("["===n[0])i=n.slice(1,-1);else{let r=null==o?void 0:o[n];if(r)i=r;else{let r=ad(n);return r&&"number"==typeof r?aw(r,g.px,t,e):null}}if("auto"===i)return aE(t,e,"auto");let a=ai(i);if(!a)return null;let[s,u]=a;return r&&(s=-s),aw(s,u,t,e)}(o,ap(n[1]),this.isNegative,this.rest,null==(r=this.config.theme)?void 0:r[o]);if(i)return i}}}if(this.consumePeeked("h-")&&(s=aI("height",this.rest,this.context,null==a?void 0:a.height))||this.consumePeeked("w-")&&(s=aI("width",this.rest,this.context,null==a?void 0:a.width))||this.consumePeeked("min-w-")&&(s=aB("minWidth",this.rest,this.context,null==a?void 0:a.minWidth))||this.consumePeeked("min-h-")&&(s=aB("minHeight",this.rest,this.context,null==a?void 0:a.minHeight))||this.consumePeeked("max-w-")&&(s=aB("maxWidth",this.rest,this.context,null==a?void 0:a.maxWidth))||this.consumePeeked("max-h-")&&(s=aB("maxHeight",this.rest,this.context,null==a?void 0:a.maxHeight))||this.consumePeeked("leading-")&&(s=function(e,t){var r;let n=ai(null!=(r=null==t?void 0:t[e])?r:e.startsWith("[")?e.slice(1,-1):e);if(!n)return null;let[o,i]=n;if(i===g.none)return{kind:"dependent",complete(e){if("number"!=typeof e.fontSize)return"relative line-height utilities require that font-size be set";e.lineHeight=e.fontSize*o}};let a=al(o,i);return null!==a?ao({lineHeight:a}):null}(this.rest,null==a?void 0:a.lineHeight))||this.consumePeeked("text-")&&((s=function(e,t,r={}){let n,o=null==t?void 0:t[e];if(!o)return ag("fontSize",e,r);if("string"==typeof o)return aa("fontSize",o);let i={},[a,s]=o,u=null===(n=au(a))?null:{fontSize:n};if(u&&(i=u),"string"==typeof s)return ao(as("lineHeight",ax(s,i),i));let{lineHeight:l,letterSpacing:c}=s;return l&&as("lineHeight",ax(l,i),i),c&&as("letterSpacing",c,i),ao(i)}(this.rest,null==a?void 0:a.fontSize,this.context))||(s=aS("text",this.rest,null==a?void 0:a.textColor))||this.consumePeeked("opacity-")&&(s=ak("text",this.rest)))||this.consumePeeked("font-")&&(s=function(e,t){let r=null==t?void 0:t[e];if(!r)return null;if("string"==typeof r)return ao({fontFamily:r});let n=r[0];return n?ao({fontFamily:n}):null}(this.rest,null==a?void 0:a.fontFamily))||this.consumePeeked("aspect-")&&(this.consumePeeked("ratio-")&&am("`aspect-ratio-{ratio}` is deprecated, use `aspect-{ratio}` instead"),s=aa("aspectRatio",this.rest,{fractions:!0}))||this.consumePeeked("tint-")&&(s=aS("tint",this.rest,null==a?void 0:a.colors))||this.consumePeeked("bg-")&&((s=aS("bg",this.rest,null==a?void 0:a.backgroundColor))||this.consumePeeked("opacity-")&&(s=ak("bg",this.rest)))||this.consumePeeked("border")&&((s=function(e,t){let[r,n]=ah(e);if(r.match(/^(-?(\d)+)?$/)){var o=r,i=n,a=null==t?void 0:t.borderWidth;if(!a)return null;let e=a[""===(o=o.replace(/^-/,""))?"DEFAULT":o];return void 0===e?null:aa(`border${"All"===i?"":i}Width`,e)}if(["dashed","solid","dotted"].includes(r=r.replace(/^-/,"")))return ao({borderStyle:r});let s="border";switch(n){case"Bottom":s="borderBottom";break;case"Top":s="borderTop";break;case"Left":s="borderLeft";break;case"Right":s="borderRight"}let u=aS(s,r,null==t?void 0:t.borderColor);if(u)return u;let l=`border${"All"===n?"":n}Width`,c=ag(l,(r=r.replace(/^-/,"")).slice(1,-1));return"number"!=typeof(null==c?void 0:c.style[l])?null:c}(this.rest,a))||this.consumePeeked("-opacity-")&&(s=ak("border",this.rest)))||this.consumePeeked("rounded")&&(s=function(e,t){if(!t)return null;let[r,n]=ah(e);""===(r=r.replace(/^-/,""))&&(r="DEFAULT");let o=`border${"All"===n?"":n}Radius`,i=t[r];if(i)return aO(aa(o,i));let a=ag(o,r);return"number"!=typeof(null==a?void 0:a.style[o])?null:aO(a)}(this.rest,null==a?void 0:a.borderRadius))||this.consumePeeked("bottom-")&&(s=aA("bottom",this.rest,this.isNegative,null==a?void 0:a.inset))||this.consumePeeked("top-")&&(s=aA("top",this.rest,this.isNegative,null==a?void 0:a.inset))||this.consumePeeked("left-")&&(s=aA("left",this.rest,this.isNegative,null==a?void 0:a.inset))||this.consumePeeked("right-")&&(s=aA("right",this.rest,this.isNegative,null==a?void 0:a.inset))||this.consumePeeked("inset-")&&(s=aA("inset",this.rest,this.isNegative,null==a?void 0:a.inset))||this.consumePeeked("flex-")&&(s=this.consumePeeked("grow")?aL("Grow",this.rest,null==a?void 0:a.flexGrow):this.consumePeeked("shrink")?aL("Shrink",this.rest,null==a?void 0:a.flexShrink):function(e,t){var r,n;if(["min-content","revert","unset"].includes(e=(null==t?void 0:t[e])||e))return null;if(e.match(/^\d+(\.\d+)?$/))return ao({flexGrow:Number(e),flexBasis:"0%"});let o=e.match(/^(\d+)\s+(\d+)$/);if(o)return ao({flexGrow:Number(o[1]),flexShrink:Number(o[2])});if(o=e.match(/^(\d+)\s+([^ ]+)$/)){let e=au(null!=(r=o[2])?r:"");return e?ao({flexGrow:Number(o[1]),flexBasis:e}):null}if(o=e.match(/^(\d+)\s+(\d+)\s+(.+)$/)){let e=au(null!=(n=o[3])?n:"");return e?ao({flexGrow:Number(o[1]),flexShrink:Number(o[2]),flexBasis:e}):null}return null}(this.rest,null==a?void 0:a.flex))||this.consumePeeked("grow")&&(s=aL("Grow",this.rest,null==a?void 0:a.flexGrow))||this.consumePeeked("shrink")&&(s=aL("Shrink",this.rest,null==a?void 0:a.flexShrink))||this.consumePeeked("shadow-color-opacity-")&&(s=ak("shadow",this.rest))||this.consumePeeked("shadow-opacity-")&&(s=Number.isNaN(i=parseInt(this.rest,10))?null:{kind:"complete",style:{shadowOpacity:i/100}})||this.consumePeeked("shadow-offset-")&&(s=function(e){if(e.includes("/")){let[t="",r=""]=e.split("/",2),n=aR(t),o=aR(r);return null===n||null===o?null:{kind:"complete",style:{shadowOffset:{width:n,height:o}}}}let t=aR(e);return null===t?null:{kind:"complete",style:{shadowOffset:{width:t,height:t}}}}(this.rest))||this.consumePeeked("shadow-radius-")&&(s=ag("shadowRadius",this.rest))||this.consumePeeked("shadow-")&&(s=aS("shadow",this.rest,null==a?void 0:a.colors)))return s;if(this.consumePeeked("elevation-")){let e=parseInt(this.rest,10);if(!Number.isNaN(e))return ao({elevation:e})}if(this.consumePeeked("opacity-")&&(s=function(e,t){let r=null==t?void 0:t[e];if(r){let e=ai(String(r));if(e)return ao({opacity:e[0]})}let n=ai(e);return n?ao({opacity:n[0]/100}):null}(this.rest,null==a?void 0:a.opacity))||this.consumePeeked("tracking-")&&(s=function(e,t,r){let n=null==r?void 0:r[e];if(n){let e=ai(n,{isNegative:t});if(!e)return null;let[r,i]=e;if(i===g.em){var o;return o=r,{kind:"dependent",complete(e){let t=e.fontSize;if("number"!=typeof t||Number.isNaN(t))return"tracking-X relative letter spacing classes require font-size to be set";e.letterSpacing=Math.round((o*t+Number.EPSILON)*100)/100}}}if(i===g.percent)return am("percentage-based letter-spacing configuration currently unsupported, switch to `em`s, or open an issue if you'd like to see support added."),null;let a=al(r,i,{isNegative:t});return null!==a?ao({letterSpacing:a}):null}return ag("letterSpacing",e,{isNegative:t})}(this.rest,this.isNegative,null==a?void 0:a.letterSpacing)))return s;if(this.consumePeeked("z-")){let e=Number(null!=(o=null==(n=null==a?void 0:a.zIndex)?void 0:n[this.rest])?o:this.rest);if(!Number.isNaN(e))return ao({zIndex:e})}return am(`\`${this.rest}\` unknown or invalid utility`),null}handlePossibleArbitraryBreakpointPrefix(e){var t;if("m"!==e[0])return!1;let r=e.match(/^(min|max)-(w|h)-\[([^\]]+)\]$/);if(!r)return!1;if(!(null!=(t=this.context.device)&&t.windowDimensions))return this.isNull=!0,!0;let n=this.context.device.windowDimensions,[,o="",i="",a=""]=r,s="w"===i?n.width:n.height,u=ai(a,this.context);if(null===u)return this.isNull=!0,!0;let[l,c]=u;return"px"!==c&&(this.isNull=!0),("min"===o?s>=l:s<=l)?this.incrementOrder():this.isNull=!0,!0}advance(e=1){this.position+=e,this.char=this.string[this.position]}get rest(){return this.peekSlice(0,this.string.length)}peekSlice(e,t){return this.string.slice(this.position+e,this.position+t)}consumePeeked(e){return this.peekSlice(0,e.length)===e&&(this.advance(e.length),!0)}parseIsNegative(){"-"===this.char&&(this.advance(),this.isNegative=!0,this.context.isNegative=!0)}incrementOrder(){var e;this.order=(null!=(e=this.order)?e:0)+1}};function aM(e){return e.trim().split(/\s+/)}function aN(e,t,r){return r.indexOf(e)===t}function aj(e){throw Error(`tailwindcss plugin function argument object prop "${e}" not implemented`)}var aW={addComponents:aj,addBase:aj,addVariant:aj,e:aj,prefix:aj,theme:aj,variants:aj,config:aj,corePlugins:aj,matchUtilities:aj,postcss:null},aG={handler:({addUtilities:e})=>{e({"shadow-sm":{boxShadow:"0 1px 2px 0 rgb(0 0 0 / 0.05)"},shadow:{boxShadow:"0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1)"},"shadow-md":{boxShadow:"0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)"},"shadow-lg":{boxShadow:"0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)"},"shadow-xl":{boxShadow:"0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)"},"shadow-2xl":{boxShadow:"0 25px 50px -12px rgb(0 0 0 / 0.25)"},"shadow-inner":{boxShadow:"inset 0 2px 4px 0 rgb(0 0 0 / 0.05)"},"shadow-none":{boxShadow:"0 0 #0000"}})}},a$=new WeakMap;async function az(e,t){let r,n=await oP();if(!n||!n.Node)throw Error("Satori is not initialized: expect `yoga` to be loaded, got "+n);t.fonts=t.fonts||[],a$.has(t.fonts)?r=a$.get(t.fonts):a$.set(t.fonts,r=new i7(t.fonts));let o="width"in t?t.width:void 0,i="height"in t?t.height:void 0,a=n.Node.create();o&&a.setWidth(o),i&&a.setHeight(i),a.setFlexDirection(n.FLEX_DIRECTION_ROW),a.setFlexWrap(n.WRAP_WRAP),a.setAlignContent(n.ALIGN_AUTO),a.setAlignItems(n.ALIGN_FLEX_START),a.setJustifyContent(n.JUSTIFY_FLEX_START),a.setOverflow(n.OVERFLOW_HIDDEN);let s={...t.graphemeImages},u=new Set;is.clear(),iu.clear(),await iv(e);let l=i6(e,{id:"id",parentStyle:{},inheritedStyle:{fontSize:16,fontWeight:"normal",fontFamily:"serif",fontStyle:"normal",lineHeight:"normal",color:"black",opacity:1,whiteSpace:"normal",_viewportWidth:o,_viewportHeight:i},parent:a,font:r,embedFont:t.embedFont,debug:t.debug,graphemeImages:s,canLoadAdditionalAssets:!!t.loadAdditionalAsset,onNodeDetected:t.onNodeDetected,getTwStyles:(e,r)=>{let n={...(function({width:e,height:t,config:r}){return m||(m=function(e,t){var r,n;let o=(0,i9.default)({...e,content:["_no_warnings_please"]}),i={},a=null!=(n=null==(r=o.plugins)?void 0:r.reduce((e,t)=>{var r;let n;return{...e,...(r=t.handler,n={},r({addUtilities:e=>{n=e},...aW}),n)}},{}))?n:{},s={},u=Object.entries(a).map(([e,t])=>"string"==typeof t?(s[e]=t,[e,{kind:"null"}]):[e,ao(t)]).filter(([,e])=>"null"!==e.kind);function l(){return[!!i.windowDimensions&&`w${i.windowDimensions.width}`,!!i.windowDimensions&&`h${i.windowDimensions.height}`,!!i.fontScale&&`fs${i.fontScale}`,"dark"===i.colorScheme&&"dark",2===i.pixelDensity&&"retina"].filter(Boolean).join("--")||"default"}let c=l(),f={};function p(){let e=f[c];if(e)return e;let t=new ab(u);return f[c]=t,t}function h(...e){let t,r,n=p(),a={},u=[],l=[],[c,f]=(t=[],r=null,e.forEach(e=>{if("string"==typeof e)t=[...t,...aM(e)];else if(Array.isArray(e))t=[...t,...e.flatMap(aM)];else if("object"==typeof e&&null!==e)for(let[n,o]of Object.entries(e))"boolean"==typeof o?t=[...t,...o?aM(n):[]]:r?r[n]=o:r={[n]:o}}),[t.filter(Boolean).filter(aN),r]),d=c.join(" "),g=n.getStyle(d);if(g)return{...g,...f||{}};for(let e of c){let t=n.getIr(e);if(!t&&e in s){let t=h(s[e]);n.setIr(e,ao(t)),a={...a,...t};continue}switch((t=new aU(e,o,n,i,"web").parse()).kind){case"complete":a={...a,...t.style},n.setIr(e,t);break;case"dependent":u.push(t);break;case"ordered":l.push(t);break;case"null":n.setIr(e,t)}}if(l.length>0)for(let e of(l.sort((e,t)=>e.order-t.order),l))switch(e.styleIr.kind){case"complete":a={...a,...e.styleIr.style};break;case"dependent":u.push(e.styleIr)}if(u.length>0){for(let e of u){let t=e.complete(a);t&&am(t)}var v=a;for(let e in v)e.startsWith("__opacity_")&&delete v[e]}return""!==d&&n.setStyle(d,a),f&&(a={...a,...f}),a}let d=(e,...t)=>{let r="";return e.forEach((e,n)=>{var o;r+=e+(null!=(o=t[n])?o:"")}),h(r)};return d.style=h,d.color=function(e){let t=h(e.split(/\s+/g).map(e=>e.replace(/^(bg|text|border)-/,"")).map(e=>`bg-${e}`).join(" "));return"string"==typeof t.backgroundColor?t.backgroundColor:void 0},d.prefixMatch=(...e)=>{let t=e.sort().join(":"),r=p(),n=r.getPrefixMatch(t);if(void 0!==n)return n;let a="null"!==new aU(`${t}:flex`,o,r,i,"web").parse().kind;return r.setPrefixMatch(t,a),a},d.setWindowDimensions=e=>{i.windowDimensions=e,c=l()},d.setFontScale=e=>{i.fontScale=e,c=l()},d.setPixelDensity=e=>{i.pixelDensity=e,c=l()},d.setColorScheme=e=>{i.colorScheme=e,c=l()},d}({...r,plugins:[...(null==r?void 0:r.plugins)??[],aG]},"web")),m.setWindowDimensions({width:+e,height:+t}),m})({width:o,height:i,config:t.tailwindConfig})([e])};return"number"==typeof n.lineHeight&&(n.lineHeight=n.lineHeight/(+n.fontSize||r.fontSize||16)),n.shadowColor&&n.boxShadow&&(n.boxShadow=n.boxShadow.replace(/rgba?\([^)]+\)/,n.shadowColor)),n}}),c=(await l.next()).value;if(t.loadAdditionalAsset&&c.length){let e=function(e){let t={},r={};for(let{word:t,locale:n}of e){let e=(function(e,t){for(let t of Object.keys(i3))if(i3[t].test(e))return[t];let r=Object.keys(i4).filter(t=>i4[t].test(e));if(0===r.length)return["unknown"];if(t){let e=r.findIndex(e=>e===t);-1!==e&&(r.splice(e,1),r.unshift(t))}return r})(t,n).join("|");r[e]=r[e]||"",r[e]+=t}return Object.keys(r).forEach(e=>{t[e]=t[e]||[],"emoji"===e?t[e].push(...aq(o2(r[e],"grapheme"))):(t[e][0]=t[e][0]||"",t[e][0]+=aq(o2(r[e],"grapheme","unknown"===e?void 0:e)).join(""))}),t}(c),n=[],o={};await Promise.all(Object.entries(e).flatMap(([e,r])=>r.map(r=>{let i=`${e}_${r}`;return u.has(i)?null:(u.add(i),t.loadAdditionalAsset(e,r).then(e=>{"string"==typeof e?o[r]=e:e&&(Array.isArray(e)?n.push(...e):n.push(e))}))}))),r.addFonts(n),Object.assign(s,o)}await l.next(),a.calculateLayout(o,i,n.DIRECTION_LTR);let f=(await l.next([0,0])).value,p=a.getComputedWidth(),h=a.getComputedHeight();return a.freeRecursive(),function({width:e,height:t,content:r}){return o3("svg",{width:e,height:t,viewBox:`0 0 ${e} ${t}`,xmlns:"http://www.w3.org/2000/svg"},r)}({width:p,height:h,content:f})}function aq(e){return Array.from(new Set(e))}var aV={};aV.ALIGN_AUTO=0,aV.ALIGN_FLEX_START=1,aV.ALIGN_CENTER=2,aV.ALIGN_FLEX_END=3,aV.ALIGN_STRETCH=4,aV.ALIGN_BASELINE=5,aV.ALIGN_SPACE_BETWEEN=6,aV.ALIGN_SPACE_AROUND=7,aV.DIMENSION_WIDTH=0,aV.DIMENSION_HEIGHT=1,aV.DIRECTION_INHERIT=0,aV.DIRECTION_LTR=1,aV.DIRECTION_RTL=2,aV.DISPLAY_FLEX=0,aV.DISPLAY_NONE=1,aV.EDGE_LEFT=0,aV.EDGE_TOP=1,aV.EDGE_RIGHT=2,aV.EDGE_BOTTOM=3,aV.EDGE_START=4,aV.EDGE_END=5,aV.EDGE_HORIZONTAL=6,aV.EDGE_VERTICAL=7,aV.EDGE_ALL=8,aV.EXPERIMENTAL_FEATURE_WEB_FLEX_BASIS=0,aV.EXPERIMENTAL_FEATURE_ABSOLUTE_PERCENTAGE_AGAINST_PADDING_EDGE=1,aV.EXPERIMENTAL_FEATURE_FIX_ABSOLUTE_TRAILING_COLUMN_MARGIN=2,aV.FLEX_DIRECTION_COLUMN=0,aV.FLEX_DIRECTION_COLUMN_REVERSE=1,aV.FLEX_DIRECTION_ROW=2,aV.FLEX_DIRECTION_ROW_REVERSE=3,aV.GUTTER_COLUMN=0,aV.GUTTER_ROW=1,aV.GUTTER_ALL=2,aV.JUSTIFY_FLEX_START=0,aV.JUSTIFY_CENTER=1,aV.JUSTIFY_FLEX_END=2,aV.JUSTIFY_SPACE_BETWEEN=3,aV.JUSTIFY_SPACE_AROUND=4,aV.JUSTIFY_SPACE_EVENLY=5,aV.LOG_LEVEL_ERROR=0,aV.LOG_LEVEL_WARN=1,aV.LOG_LEVEL_INFO=2,aV.LOG_LEVEL_DEBUG=3,aV.LOG_LEVEL_VERBOSE=4,aV.LOG_LEVEL_FATAL=5,aV.MEASURE_MODE_UNDEFINED=0,aV.MEASURE_MODE_EXACTLY=1,aV.MEASURE_MODE_AT_MOST=2,aV.NODE_TYPE_DEFAULT=0,aV.NODE_TYPE_TEXT=1,aV.OVERFLOW_VISIBLE=0,aV.OVERFLOW_HIDDEN=1,aV.OVERFLOW_SCROLL=2,aV.POSITION_TYPE_STATIC=0,aV.POSITION_TYPE_RELATIVE=1,aV.POSITION_TYPE_ABSOLUTE=2,aV.PRINT_OPTIONS_LAYOUT=1,aV.PRINT_OPTIONS_STYLE=2,aV.PRINT_OPTIONS_CHILDREN=4,aV.UNIT_UNDEFINED=0,aV.UNIT_POINT=1,aV.UNIT_PERCENT=2,aV.UNIT_AUTO=3,aV.WRAP_NO_WRAP=0,aV.WRAP_WRAP=1,aV.WRAP_WRAP_REVERSE=2;var aH=e=>{function t(e,t,r){let n=e[t];e[t]=function(...e){return r.call(this,n,...e)}}for(let r of["setPosition","setMargin","setFlexBasis","setWidth","setHeight","setMinWidth","setMinHeight","setMaxWidth","setMaxHeight","setPadding"]){let n={[aV.UNIT_POINT]:e.Node.prototype[r],[aV.UNIT_PERCENT]:e.Node.prototype[`${r}Percent`],[aV.UNIT_AUTO]:e.Node.prototype[`${r}Auto`]};t(e.Node.prototype,r,function(e,...t){let o,i,a=t.pop();if("auto"===a)o=aV.UNIT_AUTO,i=void 0;else if("object"==typeof a)o=a.unit,i=a.valueOf();else if(o="string"==typeof a&&a.endsWith("%")?aV.UNIT_PERCENT:aV.UNIT_POINT,i=parseFloat(a),!Number.isNaN(a)&&Number.isNaN(i))throw Error(`Invalid value ${a} for ${r}`);if(!n[o])throw Error(`Failed to execute "${r}": Unsupported unit '${a}'`);return void 0!==i?n[o].call(this,...t,i):n[o].call(this,...t)})}return t(e.Node.prototype,"setMeasureFunc",function(t,r){return r?t.call(this,e.MeasureCallback.implement({measure:(...e)=>{let{width:t,height:n}=r(...e);return{width:t??NaN,height:n??NaN}}})):this.unsetMeasureFunc()}),t(e.Node.prototype,"setDirtiedFunc",function(t,r){t.call(this,e.DirtiedCallback.implement({dirtied:r}))}),t(e.Config.prototype,"free",function(){e.Config.destroy(this)}),t(e.Node,"create",(t,r)=>r?e.Node.createWithConfig(r):e.Node.createDefault()),t(e.Node.prototype,"free",function(){e.Node.destroy(this)}),t(e.Node.prototype,"freeRecursive",function(){for(let e=0,t=this.getChildCount();e<t;++e)this.getChild(0).freeRecursive();this.free()}),t(e.Node.prototype,"calculateLayout",function(e,t=NaN,r=NaN,n=aV.DIRECTION_LTR){return e.call(this,t,r,n)}),{Config:e.Config,Node:e.Node,...aV}},aX=(()=>{var e="undefined"!=typeof document&&document.currentScript?document.currentScript.src:void 0;return function(t={}){u||(u=void 0!==t?t:{}),u.ready=new Promise(function(e,t){l=e,c=t});var r,n,o=Object.assign({},u),i="";"undefined"!=typeof document&&document.currentScript&&(i=document.currentScript.src),e&&(i=e),i=0!==i.indexOf("blob:")?i.substr(0,i.replace(/[?#].*/,"").lastIndexOf("/")+1):"";var a=console.log.bind(console),s=console.warn.bind(console);Object.assign(u,o),o=null,"object"!=typeof WebAssembly&&T("no native wasm support detected");var u,l,c,f,p=!1;function h(e,t,r){r=t+r;for(var n="";!(t>=r);){var o=e[t++];if(!o)break;if(128&o){var i=63&e[t++];if((224&o)==192)n+=String.fromCharCode((31&o)<<6|i);else{var a=63&e[t++];65536>(o=(240&o)==224?(15&o)<<12|i<<6|a:(7&o)<<18|i<<12|a<<6|63&e[t++])?n+=String.fromCharCode(o):(o-=65536,n+=String.fromCharCode(55296|o>>10,56320|1023&o))}}else n+=String.fromCharCode(o)}return n}function d(){var e=f.buffer;u.HEAP8=g=new Int8Array(e),u.HEAP16=m=new Int16Array(e),u.HEAP32=D=new Int32Array(e),u.HEAPU8=v=new Uint8Array(e),u.HEAPU16=y=new Uint16Array(e),u.HEAPU32=b=new Uint32Array(e),u.HEAPF32=x=new Float32Array(e),u.HEAPF64=w=new Float64Array(e)}var g,v,m,y,D,b,x,w,E,S=[],k=[],F=[],C=0,_=null;function T(e){throw s(e="Aborted("+e+")"),p=!0,c(e=new WebAssembly.RuntimeError(e+". Build with -sASSERTIONS for more info.")),e}function O(){return r.startsWith("data:application/octet-stream;base64,")}function A(){try{throw"both async and sync fetching of the wasm failed"}catch(e){T(e)}}function P(e){for(;0<e.length;)e.shift()(u)}function L(e){if(void 0===e)return"_unknown";var t=(e=e.replace(/[^a-zA-Z0-9_]/g,"$")).charCodeAt(0);return 48<=t&&57>=t?"_"+e:e}function I(e,t){return e=L(e),function(){return t.apply(this,arguments)}}r="yoga.wasm",O()||(r=i+r);var B=[{},{value:void 0},{value:null},{value:!0},{value:!1}],R=[];function U(e){var t=Error,r=I(e,function(t){this.name=e,this.message=t,void 0!==(t=Error(t).stack)&&(this.stack=this.toString()+"\n"+t.replace(/^Error(:[^\n]*)?\n/,""))});return r.prototype=Object.create(t.prototype),r.prototype.constructor=r,r.prototype.toString=function(){return void 0===this.message?this.name:this.name+": "+this.message},r}var M=void 0;function N(e){throw new M(e)}var j=e=>(e||N("Cannot use deleted val. handle = "+e),B[e].value),W=e=>{switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:var t=R.length?R.pop():B.length;return B[t]={fa:1,value:e},t}},G=void 0,$=void 0;function z(e){for(var t="";v[e];)t+=$[v[e++]];return t}var q=[];function V(){for(;q.length;){var e=q.pop();e.L.Z=!1,e.delete()}}var H=void 0,X={};function Y(e,t){for(void 0===t&&N("ptr should not be undefined");e.P;)t=e.aa(t),e=e.P;return t}var Z={};function Q(e){var t=z(e=eH(e));return eY(e),t}function K(e,t){var r=Z[e];return void 0===r&&N(t+" has unknown type "+Q(e)),r}function J(){}var ee=!1;function et(e){--e.count.value,0===e.count.value&&(e.S?e.T.V(e.S):e.O.M.V(e.N))}var er={},en=void 0;function eo(e){throw new en(e)}function ei(e,t){return t.O&&t.N||eo("makeClassHandle requires ptr and ptrType"),!!t.T!=!!t.S&&eo("Both smartPtrType and smartPtr must be specified"),t.count={value:1},ea(Object.create(e,{L:{value:t}}))}function ea(e){return"undefined"==typeof FinalizationRegistry?(ea=e=>e,e):(ee=new FinalizationRegistry(e=>{et(e.L)}),ea=e=>{var t=e.L;return t.S&&ee.register(e,{L:t},e),e},J=e=>{ee.unregister(e)},ea(e))}var es={};function eu(e){for(;e.length;){var t=e.pop();e.pop()(t)}}function el(e){return this.fromWireType(D[e>>2])}var ec={},ef={};function ep(e,t,r){function n(t){(t=r(t)).length!==e.length&&eo("Mismatched type converter count");for(var n=0;n<e.length;++n)ed(e[n],t[n])}e.forEach(function(e){ef[e]=t});var o=Array(t.length),i=[],a=0;t.forEach((e,t)=>{Z.hasOwnProperty(e)?o[t]=Z[e]:(i.push(e),ec.hasOwnProperty(e)||(ec[e]=[]),ec[e].push(()=>{o[t]=Z[e],++a===i.length&&n(o)}))}),0===i.length&&n(o)}function eh(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw TypeError("Unknown type size: "+e)}}function ed(e,t,r={}){if(!("argPackAdvance"in t))throw TypeError("registerType registeredInstance requires argPackAdvance");var n=t.name;if(e||N('type "'+n+'" must have a positive integer typeid pointer'),Z.hasOwnProperty(e)){if(r.ta)return;N("Cannot register type '"+n+"' twice")}Z[e]=t,delete ef[e],ec.hasOwnProperty(e)&&(t=ec[e],delete ec[e],t.forEach(e=>e()))}function eg(e){N(e.L.O.M.name+" instance already deleted")}function ev(){}function em(e,t,r){if(void 0===e[t].R){var n=e[t];e[t]=function(){return e[t].R.hasOwnProperty(arguments.length)||N("Function '"+r+"' called with an invalid number of arguments ("+arguments.length+") - expects one of ("+e[t].R+")!"),e[t].R[arguments.length].apply(this,arguments)},e[t].R=[],e[t].R[n.Y]=n}}function ey(e,t,r,n,o,i,a,s){this.name=e,this.constructor=t,this.W=r,this.V=n,this.P=o,this.oa=i,this.aa=a,this.ma=s,this.ia=[]}function eD(e,t,r){for(;t!==r;)t.aa||N("Expected null or instance of "+r.name+", got an instance of "+t.name),e=t.aa(e),t=t.P;return e}function eb(e,t){return null===t?(this.da&&N("null is not a valid "+this.name),0):(t.L||N('Cannot pass "'+eP(t)+'" as a '+this.name),t.L.N||N("Cannot pass deleted object as a pointer of type "+this.name),eD(t.L.N,t.L.O.M,this.M))}function ex(e,t){if(null===t){if(this.da&&N("null is not a valid "+this.name),this.ca){var r=this.ea();return null!==e&&e.push(this.V,r),r}return 0}if(t.L||N('Cannot pass "'+eP(t)+'" as a '+this.name),t.L.N||N("Cannot pass deleted object as a pointer of type "+this.name),!this.ba&&t.L.O.ba&&N("Cannot convert argument of type "+(t.L.T?t.L.T.name:t.L.O.name)+" to parameter type "+this.name),r=eD(t.L.N,t.L.O.M,this.M),this.ca)switch(void 0===t.L.S&&N("Passing raw pointer to smart pointer is illegal"),this.Aa){case 0:t.L.T===this?r=t.L.S:N("Cannot convert argument of type "+(t.L.T?t.L.T.name:t.L.O.name)+" to parameter type "+this.name);break;case 1:r=t.L.S;break;case 2:if(t.L.T===this)r=t.L.S;else{var n=t.clone();r=this.wa(r,W(function(){n.delete()})),null!==e&&e.push(this.V,r)}break;default:N("Unsupporting sharing policy")}return r}function ew(e,t){return null===t?(this.da&&N("null is not a valid "+this.name),0):(t.L||N('Cannot pass "'+eP(t)+'" as a '+this.name),t.L.N||N("Cannot pass deleted object as a pointer of type "+this.name),t.L.O.ba&&N("Cannot convert argument of type "+t.L.O.name+" to parameter type "+this.name),eD(t.L.N,t.L.O.M,this.M))}function eE(e,t,r,n){this.name=e,this.M=t,this.da=r,this.ba=n,this.ca=!1,this.V=this.wa=this.ea=this.ja=this.Aa=this.va=void 0,void 0!==t.P?this.toWireType=ex:(this.toWireType=n?eb:ew,this.U=null)}var eS=[];function ek(e){var t=eS[e];return t||(e>=eS.length&&(eS.length=e+1),eS[e]=t=E.get(e)),t}function eF(e,t){var r,n,o=(e=z(e)).includes("j")?(r=e,n=[],function(){if(n.length=0,Object.assign(n,arguments),r.includes("j")){var e=u["dynCall_"+r];e=n&&n.length?e.apply(null,[t].concat(n)):e.call(null,t)}else e=ek(t).apply(null,n);return e}):ek(t);return"function"!=typeof o&&N("unknown function pointer with signature "+e+": "+t),o}var eC=void 0;function e_(e,t){var r=[],n={};throw t.forEach(function e(t){n[t]||Z[t]||(ef[t]?ef[t].forEach(e):(r.push(t),n[t]=!0))}),new eC(e+": "+r.map(Q).join([", "]))}function eT(e,t,r,n,o){var i=t.length;2>i&&N("argTypes array size mismatch! Must at least get return value and 'this' types!");var a=null!==t[1]&&null!==r,s=!1;for(r=1;r<t.length;++r)if(null!==t[r]&&void 0===t[r].U){s=!0;break}var u="void"!==t[0].name,l=i-2,c=Array(l),f=[],p=[];return function(){if(arguments.length!==l&&N("function "+e+" called with "+arguments.length+" arguments, expected "+l+" args!"),p.length=0,f.length=a?2:1,f[0]=o,a){var r=t[1].toWireType(p,this);f[1]=r}for(var i=0;i<l;++i)c[i]=t[i+2].toWireType(p,arguments[i]),f.push(c[i]);if(i=n.apply(null,f),s)eu(p);else for(var h=a?1:2;h<t.length;h++){var d=1===h?r:c[h-2];null!==t[h].U&&t[h].U(d)}return u?t[0].fromWireType(i):void 0}}function eO(e,t){for(var r=[],n=0;n<e;n++)r.push(b[t+4*n>>2]);return r}function eA(e){4<e&&0==--B[e].fa&&(B[e]=void 0,R.push(e))}function eP(e){if(null===e)return"null";var t=typeof e;return"object"===t||"array"===t||"function"===t?e.toString():""+e}function eL(e,t){for(var r="",n=0;!(n>=t/2);++n){var o=m[e+2*n>>1];if(0==o)break;r+=String.fromCharCode(o)}return r}function eI(e,t,r){if(void 0===r&&(r=0x7fffffff),2>r)return 0;r-=2;var n=t;r=r<2*e.length?r/2:e.length;for(var o=0;o<r;++o)m[t>>1]=e.charCodeAt(o),t+=2;return m[t>>1]=0,t-n}function eB(e){return 2*e.length}function eR(e,t){for(var r=0,n="";!(r>=t/4);){var o=D[e+4*r>>2];if(0==o)break;++r,65536<=o?(o-=65536,n+=String.fromCharCode(55296|o>>10,56320|1023&o)):n+=String.fromCharCode(o)}return n}function eU(e,t,r){if(void 0===r&&(r=0x7fffffff),4>r)return 0;var n=t;r=n+r-4;for(var o=0;o<e.length;++o){var i=e.charCodeAt(o);if(55296<=i&&57343>=i&&(i=65536+((1023&i)<<10)|1023&e.charCodeAt(++o)),D[t>>2]=i,(t+=4)+4>r)break}return D[t>>2]=0,t-n}function eM(e){for(var t=0,r=0;r<e.length;++r){var n=e.charCodeAt(r);55296<=n&&57343>=n&&++r,t+=4}return t}var eN={};function ej(e){var t=eN[e];return void 0===t?z(e):t}var eW=[],eG=[],e$=[null,[],[]];M=u.BindingError=U("BindingError"),u.count_emval_handles=function(){for(var e=0,t=5;t<B.length;++t)void 0!==B[t]&&++e;return e},u.get_first_emval=function(){for(var e=5;e<B.length;++e)if(void 0!==B[e])return B[e];return null},G=u.PureVirtualError=U("PureVirtualError");for(var ez=Array(256),eq=0;256>eq;++eq)ez[eq]=String.fromCharCode(eq);$=ez,u.getInheritedInstanceCount=function(){return Object.keys(X).length},u.getLiveInheritedInstances=function(){var e,t=[];for(e in X)X.hasOwnProperty(e)&&t.push(X[e]);return t},u.flushPendingDeletes=V,u.setDelayFunction=function(e){H=e,q.length&&H&&H(V)},en=u.InternalError=U("InternalError"),ev.prototype.isAliasOf=function(e){if(!(this instanceof ev&&e instanceof ev))return!1;var t=this.L.O.M,r=this.L.N,n=e.L.O.M;for(e=e.L.N;t.P;)r=t.aa(r),t=t.P;for(;n.P;)e=n.aa(e),n=n.P;return t===n&&r===e},ev.prototype.clone=function(){if(this.L.N||eg(this),this.L.$)return this.L.count.value+=1,this;var e=ea,t=Object,r=t.create,n=Object.getPrototypeOf(this),o=this.L;return e=e(r.call(t,n,{L:{value:{count:o.count,Z:o.Z,$:o.$,N:o.N,O:o.O,S:o.S,T:o.T}}})),e.L.count.value+=1,e.L.Z=!1,e},ev.prototype.delete=function(){this.L.N||eg(this),this.L.Z&&!this.L.$&&N("Object already scheduled for deletion"),J(this),et(this.L),this.L.$||(this.L.S=void 0,this.L.N=void 0)},ev.prototype.isDeleted=function(){return!this.L.N},ev.prototype.deleteLater=function(){return this.L.N||eg(this),this.L.Z&&!this.L.$&&N("Object already scheduled for deletion"),q.push(this),1===q.length&&H&&H(V),this.L.Z=!0,this},eE.prototype.pa=function(e){return this.ja&&(e=this.ja(e)),e},eE.prototype.ga=function(e){this.V&&this.V(e)},eE.prototype.argPackAdvance=8,eE.prototype.readValueFromPointer=el,eE.prototype.deleteObject=function(e){null!==e&&e.delete()},eE.prototype.fromWireType=function(e){function t(){return this.ca?ei(this.M.W,{O:this.va,N:r,T:this,S:e}):ei(this.M.W,{O:this,N:e})}var r=this.pa(e);if(!r)return this.ga(e),null;var n=X[Y(this.M,r)];if(void 0!==n)return 0===n.L.count.value?(n.L.N=r,n.L.S=e,n.clone()):(n=n.clone(),this.ga(e),n);if(!(n=er[n=this.M.oa(r)]))return t.call(this);n=this.ba?n.ka:n.pointerType;var o=function e(t,r,n){return r===n?t:void 0===n.P||null===(t=e(t,r,n.P))?null:n.ma(t)}(r,this.M,n.M);return null===o?t.call(this):this.ca?ei(n.M.W,{O:n,N:o,T:this,S:e}):ei(n.M.W,{O:n,N:o})},eC=u.UnboundTypeError=U("UnboundTypeError");var eV={q:function(e,t,r){e=z(e),t=K(t,"wrapper"),r=j(r);var n=[].slice,o=t.M,i=o.W,a=o.P.W,s=o.P.constructor;for(var u in e=I(e,function(){o.P.ia.forEach((function(e){if(this[e]===a[e])throw new G("Pure virtual function "+e+" must be implemented in JavaScript")}).bind(this)),Object.defineProperty(this,"__parent",{value:i}),this.__construct.apply(this,n.call(arguments))}),i.__construct=function(){this===i&&N("Pass correct 'this' to __construct");var e=s.implement.apply(void 0,[this].concat(n.call(arguments)));J(e);var t=e.L;e.notifyOnDestruction(),t.$=!0,Object.defineProperties(this,{L:{value:t}}),ea(this),e=Y(o,e=t.N),X.hasOwnProperty(e)?N("Tried to register registered instance: "+e):X[e]=this},i.__destruct=function(){this===i&&N("Pass correct 'this' to __destruct"),J(this);var e=this.L.N;e=Y(o,e),X.hasOwnProperty(e)?delete X[e]:N("Tried to unregister unregistered instance: "+e)},e.prototype=Object.create(i),r)e.prototype[u]=r[u];return W(e)},l:function(e){var t=es[e];delete es[e];var r=t.ea,n=t.V,o=t.ha;ep([e],o.map(e=>e.sa).concat(o.map(e=>e.ya)),e=>{var i={};return o.forEach((t,r)=>{var n=e[r],a=t.qa,s=t.ra,u=e[r+o.length],l=t.xa,c=t.za;i[t.na]={read:e=>n.fromWireType(a(s,e)),write:(e,t)=>{var r=[];l(c,e,u.toWireType(r,t)),eu(r)}}}),[{name:t.name,fromWireType:function(e){var t,r={};for(t in i)r[t]=i[t].read(e);return n(e),r},toWireType:function(e,t){for(var o in i)if(!(o in t))throw TypeError('Missing field:  "'+o+'"');var a=r();for(o in i)i[o].write(a,t[o]);return null!==e&&e.push(n,a),a},argPackAdvance:8,readValueFromPointer:el,U:n}]})},v:function(){},B:function(e,t,r,n,o){var i=eh(r);ed(e,{name:t=z(t),fromWireType:function(e){return!!e},toWireType:function(e,t){return t?n:o},argPackAdvance:8,readValueFromPointer:function(e){if(1===r)var n=g;else if(2===r)n=m;else if(4===r)n=D;else throw TypeError("Unknown boolean type size: "+t);return this.fromWireType(n[e>>i])},U:null})},h:function(e,t,r,n,o,i,a,s,l,c,f,p,h){f=z(f),i=eF(o,i),s&&(s=eF(a,s)),c&&(c=eF(l,c)),h=eF(p,h);var d,g=L(f);d=function(){e_("Cannot construct "+f+" due to unbound types",[n])},u.hasOwnProperty(g)?(N("Cannot register public name '"+g+"' twice"),em(u,g,g),u.hasOwnProperty(void 0)&&N("Cannot register multiple overloads of a function with the same number of arguments (undefined)!"),u[g].R[void 0]=d):u[g]=d,ep([e,t,r],n?[n]:[],function(t){if(t=t[0],n)var r,o=t.M,a=o.W;else a=ev.prototype;var l=Object.create(a,{constructor:{value:t=I(g,function(){if(Object.getPrototypeOf(this)!==l)throw new M("Use 'new' to construct "+f);if(void 0===p.X)throw new M(f+" has no accessible constructor");var e=p.X[arguments.length];if(void 0===e)throw new M("Tried to invoke ctor of "+f+" with invalid number of parameters ("+arguments.length+") - expected ("+Object.keys(p.X).toString()+") parameters instead!");return e.apply(this,arguments)})}});t.prototype=l;var p=new ey(f,t,l,h,o,i,s,c);o=new eE(f,p,!0,!1),a=new eE(f+"*",p,!1,!1);var d=new eE(f+" const*",p,!1,!0);return er[e]={pointerType:a,ka:d},r=t,u.hasOwnProperty(g)||eo("Replacing nonexistant public symbol"),u[g]=r,u[g].Y=void 0,[o,a,d]})},d:function(e,t,r,n,o,i,a){var s=eO(r,n);t=z(t),i=eF(o,i),ep([],[e],function(e){function n(){e_("Cannot call "+o+" due to unbound types",s)}var o=(e=e[0]).name+"."+t;t.startsWith("@@")&&(t=Symbol[t.substring(2)]);var u=e.M.constructor;return void 0===u[t]?(n.Y=r-1,u[t]=n):(em(u,t,o),u[t].R[r-1]=n),ep([],s,function(e){return e=eT(o,[e[0],null].concat(e.slice(1)),null,i,a),void 0===u[t].R?(e.Y=r-1,u[t]=e):u[t].R[r-1]=e,[]}),[]})},p:function(e,t,r,n,o,i){0<t||T();var a=eO(t,r);o=eF(n,o),ep([],[e],function(e){var r="constructor "+(e=e[0]).name;if(void 0===e.M.X&&(e.M.X=[]),void 0!==e.M.X[t-1])throw new M("Cannot register multiple constructors with identical number of parameters ("+(t-1)+") for class '"+e.name+"'! Overload resolution is currently only performed using the parameter count, not actual type info!");return e.M.X[t-1]=()=>{e_("Cannot construct "+e.name+" due to unbound types",a)},ep([],a,function(n){return n.splice(1,0,null),e.M.X[t-1]=eT(r,n,null,o,i),[]}),[]})},a:function(e,t,r,n,o,i,a,s){var u=eO(r,n);t=z(t),i=eF(o,i),ep([],[e],function(e){function n(){e_("Cannot call "+o+" due to unbound types",u)}var o=(e=e[0]).name+"."+t;t.startsWith("@@")&&(t=Symbol[t.substring(2)]),s&&e.M.ia.push(t);var l=e.M.W,c=l[t];return void 0===c||void 0===c.R&&c.className!==e.name&&c.Y===r-2?(n.Y=r-2,n.className=e.name,l[t]=n):(em(l,t,o),l[t].R[r-2]=n),ep([],u,function(n){return n=eT(o,n,e,i,a),void 0===l[t].R?(n.Y=r-2,l[t]=n):l[t].R[r-2]=n,[]}),[]})},A:function(e,t){ed(e,{name:t=z(t),fromWireType:function(e){var t=j(e);return eA(e),t},toWireType:function(e,t){return W(t)},argPackAdvance:8,readValueFromPointer:el,U:null})},n:function(e,t,r){r=eh(r),ed(e,{name:t=z(t),fromWireType:function(e){return e},toWireType:function(e,t){return t},argPackAdvance:8,readValueFromPointer:function(e,t){switch(t){case 2:return function(e){return this.fromWireType(x[e>>2])};case 3:return function(e){return this.fromWireType(w[e>>3])};default:throw TypeError("Unknown float type: "+e)}}(t,r),U:null})},e:function(e,t,r,n,o){t=z(t),-1===o&&(o=0xffffffff),o=eh(r);var i=e=>e;if(0===n){var a=32-8*r;i=e=>e<<a>>>a}r=t.includes("unsigned")?function(e,t){return t>>>0}:function(e,t){return t},ed(e,{name:t,fromWireType:i,toWireType:r,argPackAdvance:8,readValueFromPointer:function(e,t,r){switch(t){case 0:return r?function(e){return g[e]}:function(e){return v[e]};case 1:return r?function(e){return m[e>>1]}:function(e){return y[e>>1]};case 2:return r?function(e){return D[e>>2]}:function(e){return b[e>>2]};default:throw TypeError("Unknown integer type: "+e)}}(t,o,0!==n),U:null})},b:function(e,t,r){function n(e){e>>=2;var t=b;return new o(t.buffer,t[e+1],t[e])}var o=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];ed(e,{name:r=z(r),fromWireType:n,argPackAdvance:8,readValueFromPointer:n},{ta:!0})},o:function(e,t){var r="std::string"===(t=z(t));ed(e,{name:t,fromWireType:function(e){var t=b[e>>2],n=e+4;if(r)for(var o=n,i=0;i<=t;++i){var a=n+i;if(i==t||0==v[a]){if(o=o?h(v,o,a-o):"",void 0===s)var s=o;else s+="\0"+o;o=a+1}}else{for(i=0,s=Array(t);i<t;++i)s[i]=String.fromCharCode(v[n+i]);s=s.join("")}return eY(e),s},toWireType:function(e,t){t instanceof ArrayBuffer&&(t=new Uint8Array(t));var n,o="string"==typeof t;if(o||t instanceof Uint8Array||t instanceof Uint8ClampedArray||t instanceof Int8Array||N("Cannot pass non-string to std::string"),r&&o){var i=0;for(n=0;n<t.length;++n){var a=t.charCodeAt(n);127>=a?i++:2047>=a?i+=2:55296<=a&&57343>=a?(i+=4,++n):i+=3}n=i}else n=t.length;if(a=(i=eX(4+n+1))+4,b[i>>2]=n,r&&o){if(o=a,a=n+1,n=v,0<a){a=o+a-1;for(var s=0;s<t.length;++s){var u=t.charCodeAt(s);if(55296<=u&&57343>=u&&(u=65536+((1023&u)<<10)|1023&t.charCodeAt(++s)),127>=u){if(o>=a)break;n[o++]=u}else{if(2047>=u){if(o+1>=a)break;n[o++]=192|u>>6}else{if(65535>=u){if(o+2>=a)break;n[o++]=224|u>>12}else{if(o+3>=a)break;n[o++]=240|u>>18,n[o++]=128|u>>12&63}n[o++]=128|u>>6&63}n[o++]=128|63&u}}n[o]=0}}else if(o)for(o=0;o<n;++o)255<(s=t.charCodeAt(o))&&(eY(a),N("String has UTF-16 code units that do not fit in 8 bits")),v[a+o]=s;else for(o=0;o<n;++o)v[a+o]=t[o];return null!==e&&e.push(eY,i),i},argPackAdvance:8,readValueFromPointer:el,U:function(e){eY(e)}})},k:function(e,t,r){if(r=z(r),2===t)var n=eL,o=eI,i=eB,a=()=>y,s=1;else 4===t&&(n=eR,o=eU,i=eM,a=()=>b,s=2);ed(e,{name:r,fromWireType:function(e){for(var r,o=b[e>>2],i=a(),u=e+4,l=0;l<=o;++l){var c=e+4+l*t;(l==o||0==i[c>>s])&&(u=n(u,c-u),void 0===r?r=u:r+="\0"+u,u=c+t)}return eY(e),r},toWireType:function(e,n){"string"!=typeof n&&N("Cannot pass non-string to C++ string type "+r);var a=i(n),u=eX(4+a+t);return b[u>>2]=a>>s,o(n,u+4,a+t),null!==e&&e.push(eY,u),u},argPackAdvance:8,readValueFromPointer:el,U:function(e){eY(e)}})},m:function(e,t,r,n,o,i){es[e]={name:z(t),ea:eF(r,n),V:eF(o,i),ha:[]}},c:function(e,t,r,n,o,i,a,s,u,l){es[e].ha.push({na:z(t),sa:r,qa:eF(n,o),ra:i,ya:a,xa:eF(s,u),za:l})},C:function(e,t){ed(e,{ua:!0,name:t=z(t),argPackAdvance:0,fromWireType:function(){},toWireType:function(){}})},t:function(e,t,r,n,o){e=eW[e],t=j(t),r=ej(r);var i=[];return b[n>>2]=W(i),e(t,r,i,o)},j:function(e,t,r,n){(e=eW[e])(t=j(t),r=ej(r),null,n)},f:eA,g:function(e,t){var r,n,o=function(e,t){for(var r=Array(e),n=0;n<e;++n)r[n]=K(b[t+4*n>>2],"parameter "+n);return r}(e,t),i=o[0],a=eG[t=i.name+"_$"+o.slice(1).map(function(e){return e.name}).join("_")+"$"];if(void 0!==a)return a;var s=Array(e-1);return r=(t,r,n,a)=>{for(var u=0,l=0;l<e-1;++l)s[l]=o[l+1].readValueFromPointer(a+u),u+=o[l+1].argPackAdvance;for(l=0,t=t[r].apply(t,s);l<e-1;++l)o[l+1].la&&o[l+1].la(s[l]);if(!i.ua)return i.toWireType(n,t)},n=eW.length,eW.push(r),a=n,eG[t]=a},r:function(e){4<e&&(B[e].fa+=1)},s:function(e){eu(j(e)),eA(e)},i:function(){T("")},x:function(e,t,r){v.copyWithin(e,t,t+r)},w:function(e){var t=v.length;if(0x80000000<(e>>>=0))return!1;for(var r=1;4>=r;r*=2){var n=t*(1+.2/r);n=Math.min(n,e+0x6000000);var o=Math,i=o.min;n=Math.max(e,n),n+=(65536-n%65536)%65536;e:{var a=f.buffer;try{f.grow(i.call(o,0x80000000,n)-a.byteLength+65535>>>16),d();var s=1;break e}catch(e){}s=void 0}if(s)return!0}return!1},z:function(){return 52},u:function(){return 70},y:function(e,t,r,n){for(var o=0,i=0;i<r;i++){var u=b[t>>2],l=b[t+4>>2];t+=8;for(var c=0;c<l;c++){var f=v[u+c],p=e$[e];0===f||10===f?((1===e?a:s)(h(p,0)),p.length=0):p.push(f)}o+=l}return b[n>>2]=o,0}};!function(){function e(e){u.asm=e.exports,f=u.asm.D,d(),E=u.asm.I,k.unshift(u.asm.E),0==--C&&_&&(e=_,_=null,e())}function t(t){e(t.instance)}function n(e){return("function"==typeof fetch?fetch(r,{credentials:"same-origin"}).then(function(e){if(!e.ok)throw"failed to load wasm binary file at '"+r+"'";return e.arrayBuffer()}).catch(function(){return A()}):Promise.resolve().then(function(){return A()})).then(function(e){return WebAssembly.instantiate(e,o)}).then(function(e){return e}).then(e,function(e){s("failed to asynchronously prepare wasm: "+e),T(e)})}var o={a:eV};if(C++,u.instantiateWasm)try{return u.instantiateWasm(o,e)}catch(e){s("Module.instantiateWasm callback failed with error: "+e),c(e)}("function"!=typeof WebAssembly.instantiateStreaming||O()||"function"!=typeof fetch?n(t):fetch(r,{credentials:"same-origin"}).then(function(e){return WebAssembly.instantiateStreaming(e,o).then(t,function(e){return s("wasm streaming compile failed: "+e),s("falling back to ArrayBuffer instantiation"),n(t)})})).catch(c)}();var eH=u.___getTypeName=function(){return(eH=u.___getTypeName=u.asm.F).apply(null,arguments)};function eX(){return(eX=u.asm.H).apply(null,arguments)}function eY(){return(eY=u.asm.J).apply(null,arguments)}function eZ(){0<C||(P(S),0<C||n||(n=!0,u.calledRun=!0,p||(P(k),l(u),P(F))))}return u.__embind_initialize_bindings=function(){return(u.__embind_initialize_bindings=u.asm.G).apply(null,arguments)},u.dynCall_jiji=function(){return(u.dynCall_jiji=u.asm.K).apply(null,arguments)},_=function e(){n||eZ(),n||(_=e)},eZ(),t.ready}})();async function aY(e){return aH(await aX({instantiateWasm(t,r){WebAssembly.instantiate(e,t).then(e=>{e instanceof WebAssembly.Instance?r(e):r(e.instance)})}}))}var aZ={};((e,t)=>{for(var r in t)E(e,r,{get:t[r],enumerable:!0})})(aZ,{Resvg:()=>su,initWasm:()=>ss});var aQ=Array(128).fill(void 0);aQ.push(void 0,null,!0,!1);var aK=aQ.length;function aJ(e){aK===aQ.length&&aQ.push(aQ.length+1);let t=aK;return aK=aQ[t],aQ[t]=e,t}function a0(e){let t=aQ[e];return e<132||(aQ[e]=aK,aK=e),t}var a1=0,a2=null;function a3(){return(null===a2||0===a2.byteLength)&&(a2=new Uint8Array(y.memory.buffer)),a2}var a4=new TextEncoder("utf-8"),a5="function"==typeof a4.encodeInto?function(e,t){return a4.encodeInto(e,t)}:function(e,t){let r=a4.encode(e);return t.set(r),{read:e.length,written:r.length}};function a6(e,t,r){if(void 0===r){let r=a4.encode(e),n=t(r.length);return a3().subarray(n,n+r.length).set(r),a1=r.length,n}let n=e.length,o=t(n),i=a3(),a=0;for(;a<n;a++){let t=e.charCodeAt(a);if(t>127)break;i[o+a]=t}if(a!==n){0!==a&&(e=e.slice(a)),o=r(o,n,n=a+3*e.length);let t=a5(e,a3().subarray(o+a,o+n));a+=t.written}return a1=a,o}var a8=null;function a7(){return(null===a8||0===a8.byteLength)&&(a8=new Int32Array(y.memory.buffer)),a8}var a9=new TextDecoder("utf-8",{ignoreBOM:!0,fatal:!0});function se(e,t){return a9.decode(a3().subarray(e,e+t))}a9.decode();var st=class{static __wrap(e){let t=Object.create(st.prototype);return t.ptr=e,t}__destroy_into_raw(){let e=this.ptr;return this.ptr=0,e}free(){let e=this.__destroy_into_raw();y.__wbg_bbox_free(e)}get x(){return y.__wbg_get_bbox_x(this.ptr)}set x(e){y.__wbg_set_bbox_x(this.ptr,e)}get y(){return y.__wbg_get_bbox_y(this.ptr)}set y(e){y.__wbg_set_bbox_y(this.ptr,e)}get width(){return y.__wbg_get_bbox_width(this.ptr)}set width(e){y.__wbg_set_bbox_width(this.ptr,e)}get height(){return y.__wbg_get_bbox_height(this.ptr)}set height(e){y.__wbg_set_bbox_height(this.ptr,e)}},sr=class{static __wrap(e){let t=Object.create(sr.prototype);return t.ptr=e,t}__destroy_into_raw(){let e=this.ptr;return this.ptr=0,e}free(){let e=this.__destroy_into_raw();y.__wbg_renderedimage_free(e)}get width(){return y.renderedimage_width(this.ptr)>>>0}get height(){return y.renderedimage_height(this.ptr)>>>0}asPng(){try{let r=y.__wbindgen_add_to_stack_pointer(-16);y.renderedimage_asPng(r,this.ptr);var e=a7()[r/4+0],t=a7()[r/4+1];if(a7()[r/4+2])throw a0(t);return a0(e)}finally{y.__wbindgen_add_to_stack_pointer(16)}}get pixels(){return a0(y.renderedimage_pixels(this.ptr))}},sn=class{static __wrap(e){let t=Object.create(sn.prototype);return t.ptr=e,t}__destroy_into_raw(){let e=this.ptr;return this.ptr=0,e}free(){let e=this.__destroy_into_raw();y.__wbg_resvg_free(e)}constructor(e,t){try{let a=y.__wbindgen_add_to_stack_pointer(-16);var r=null==t?0:a6(t,y.__wbindgen_malloc,y.__wbindgen_realloc),n=a1;y.resvg_new(a,aJ(e),r,n);var o=a7()[a/4+0],i=a7()[a/4+1];if(a7()[a/4+2])throw a0(i);return sn.__wrap(o)}finally{y.__wbindgen_add_to_stack_pointer(16)}}get width(){return y.resvg_width(this.ptr)}get height(){return y.resvg_height(this.ptr)}render(){try{let r=y.__wbindgen_add_to_stack_pointer(-16);y.resvg_render(r,this.ptr);var e=a7()[r/4+0],t=a7()[r/4+1];if(a7()[r/4+2])throw a0(t);return sr.__wrap(e)}finally{y.__wbindgen_add_to_stack_pointer(16)}}toString(){try{let r=y.__wbindgen_add_to_stack_pointer(-16);y.resvg_toString(r,this.ptr);var e=a7()[r/4+0],t=a7()[r/4+1];return se(e,t)}finally{y.__wbindgen_add_to_stack_pointer(16),y.__wbindgen_free(e,t)}}innerBBox(){let e=y.resvg_innerBBox(this.ptr);return 0===e?void 0:st.__wrap(e)}getBBox(){let e=y.resvg_getBBox(this.ptr);return 0===e?void 0:st.__wrap(e)}cropByBBox(e){!function(e,t){if(!(e instanceof t))throw Error(`expected instance of ${t.name}`);e.ptr}(e,st),y.resvg_cropByBBox(this.ptr,e.ptr)}imagesToResolve(){try{let r=y.__wbindgen_add_to_stack_pointer(-16);y.resvg_imagesToResolve(r,this.ptr);var e=a7()[r/4+0],t=a7()[r/4+1];if(a7()[r/4+2])throw a0(t);return a0(e)}finally{y.__wbindgen_add_to_stack_pointer(16)}}resolveImage(e,t){try{let n=y.__wbindgen_add_to_stack_pointer(-16),o=a6(e,y.__wbindgen_malloc,y.__wbindgen_realloc),i=a1;y.resvg_resolveImage(n,this.ptr,o,i,aJ(t));var r=a7()[n/4+0];if(a7()[n/4+1])throw a0(r)}finally{y.__wbindgen_add_to_stack_pointer(16)}}};async function so(e,t){if("function"==typeof Response&&e instanceof Response){if("function"==typeof WebAssembly.instantiateStreaming)try{return await WebAssembly.instantiateStreaming(e,t)}catch(t){if("application/wasm"!=e.headers.get("Content-Type"))console.warn("`WebAssembly.instantiateStreaming` failed because your server does not serve wasm with `application/wasm` MIME type. Falling back to `WebAssembly.instantiate` which is slower. Original error:\n",t);else throw t}let r=await e.arrayBuffer();return await WebAssembly.instantiate(r,t)}{let r=await WebAssembly.instantiate(e,t);return r instanceof WebAssembly.Instance?{instance:r,module:e}:r}}async function si(e){void 0===e&&(e=new URL("index_bg.wasm",void 0));let t=function(){let e={};return e.wbg={},e.wbg.__wbg_new_15d3966e9981a196=function(e,t){return aJ(Error(se(e,t)))},e.wbg.__wbindgen_memory=function(){return aJ(y.memory)},e.wbg.__wbg_buffer_cf65c07de34b9a08=function(e){return aJ(aQ[e].buffer)},e.wbg.__wbg_newwithbyteoffsetandlength_9fb2f11355ecadf5=function(e,t,r){return aJ(new Uint8Array(aQ[e],t>>>0,r>>>0))},e.wbg.__wbindgen_object_drop_ref=function(e){a0(e)},e.wbg.__wbg_new_537b7341ce90bb31=function(e){return aJ(new Uint8Array(aQ[e]))},e.wbg.__wbg_instanceof_Uint8Array_01cebe79ca606cca=function(e){let t;try{t=aQ[e]instanceof Uint8Array}catch(e){t=!1}return t},e.wbg.__wbindgen_string_get=function(e,t){let r=aQ[t],n="string"==typeof r?r:void 0;var o=null==n?0:a6(n,y.__wbindgen_malloc,y.__wbindgen_realloc),i=a1;a7()[e/4+1]=i,a7()[e/4+0]=o},e.wbg.__wbg_new_b525de17f44a8943=function(){return aJ([])},e.wbg.__wbindgen_string_new=function(e,t){return aJ(se(e,t))},e.wbg.__wbg_push_49c286f04dd3bf59=function(e,t){return aQ[e].push(aQ[t])},e.wbg.__wbg_length_27a2afe8ab42b09f=function(e){return aQ[e].length},e.wbg.__wbg_set_17499e8aa4003ebd=function(e,t,r){aQ[e].set(aQ[t],r>>>0)},e.wbg.__wbindgen_throw=function(e,t){throw Error(se(e,t))},e}();("string"==typeof e||"function"==typeof Request&&e instanceof Request||"function"==typeof URL&&e instanceof URL)&&(e=fetch(e));let{instance:r,module:n}=await so(await e,t);return y=r.exports,si.__wbindgen_wasm_module=n,a8=null,a2=null,y}var sa=!1,ss=async e=>{if(sa)throw Error("Already initialized. The `initWasm()` function can be used only once.");await si(await e),sa=!0},su=class extends sn{constructor(e,t){if(!sa)throw Error("Wasm has not been initialized. Call `initWasm()` function.");super(e,JSON.stringify(t))}},sl=String.fromCharCode(8205),sc=/\uFE0F/g,sf={twemoji:e=>"https://cdn.jsdelivr.net/gh/twitter/twemoji@14.0.2/assets/svg/"+e.toLowerCase()+".svg",openmoji:"https://cdn.jsdelivr.net/npm/@svgmoji/openmoji@2.0.0/svg/",blobmoji:"https://cdn.jsdelivr.net/npm/@svgmoji/blob@2.0.0/svg/",noto:"https://cdn.jsdelivr.net/gh/svgmoji/svgmoji/packages/svgmoji__noto/svg/",fluent:e=>"https://cdn.jsdelivr.net/gh/shuding/fluentui-emoji-unicode/assets/"+e.toLowerCase()+"_color.svg",fluentFlat:e=>"https://cdn.jsdelivr.net/gh/shuding/fluentui-emoji-unicode/assets/"+e.toLowerCase()+"_flat.svg"},sp=class{constructor(){this.rangesByLang={}}async detect(e,t){await this.load(t);let r={};for(let n of e){let e=this.detectSegment(n,t);e&&(r[e]=r[e]||"",r[e]+=n)}return r}detectSegment(e,t){for(let r of t){let t=this.rangesByLang[r];if(t&&function(e,t){let r=e.codePointAt(0);return!!r&&t.some(e=>{if("number"==typeof e)return r===e;{let[t,n]=e;return t<=r&&r<=n}})}(e,t))return r}return null}async load(e){let t="",r=Object.keys(this.rangesByLang),n=e.filter(e=>!r.includes(e));if(0===n.length)return;for(let e of n)t+=`family=${e}&`;t+="display=swap";let o=`https://fonts.googleapis.com/css2?${t}`,i=await (await fetch(o,{headers:{"User-Agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36"}})).text();this.addDetectors(i)}addDetectors(e){for(let[,t,r]of e.matchAll(/font-family:\s*'(.+?)';.+?unicode-range:\s*(.+?);/gms)){let e=t.replaceAll(" ","+");this.rangesByLang[e]||(this.rangesByLang[e]=[]),this.rangesByLang[e].push(...r.split(", ").map(e=>{let[t,r]=(e=e.replaceAll("U+","")).split("-").map(e=>parseInt(e,16));return isNaN(r)?t:[t,r]}))}}},sh={"ja-JP":"Noto+Sans+JP","ko-KR":"Noto+Sans+KR","zh-CN":"Noto+Sans+SC","zh-TW":"Noto+Sans+TC","zh-HK":"Noto+Sans+HK","th-TH":"Noto+Sans+Thai","bn-IN":"Noto+Sans+Bengali","ar-AR":"Noto+Sans+Arabic","ta-IN":"Noto+Sans+Tamil","ml-IN":"Noto+Sans+Malayalam","he-IL":"Noto+Sans+Hebrew","te-IN":"Noto+Sans+Telugu",devanagari:"Noto+Sans+Devanagari",kannada:"Noto+Sans+Kannada",symbol:["Noto+Sans+Symbols","Noto+Sans+Symbols+2"],math:"Noto+Sans+Math",unknown:"Noto+Sans"};async function sd(e,t){if(!e||!t)return;let r=`https://fonts.googleapis.com/css2?family=${e}&text=${encodeURIComponent(t)}`,n=(await (await fetch(r,{headers:{"User-Agent":"Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10_6_8; de-at) AppleWebKit/533.21.1 (KHTML, like Gecko) Version/5.0.5 Safari/533.21.1"}})).text()).match(/src: url\((.+)\) format\('(opentype|truetype)'\)/);if(!n)throw Error("Failed to download dynamic font");let o=await fetch(n[1]);if(!o.ok)throw Error("Failed to download dynamic font. Status: "+o.status);return o.arrayBuffer()}var sg=new sp,sv=new Map,sm=({emoji:e})=>{let t=async(t,r)=>{if("emoji"===t)return"data:image/svg+xml;base64,"+btoa(await (await function(e,t){t&&sf[t]||(t="twemoji");let r=sf[t];return"function"==typeof r?fetch(r(e)):fetch(`${r}${e.toUpperCase()}.svg`)}(function(e){return function(e){for(var t=[],r=0,n=0,o=0;o<e.length;)r=e.charCodeAt(o++),n?(t.push((65536+(n-55296<<10)+(r-56320)).toString(16)),n=0):55296<=r&&r<=56319?n=r:t.push(r.toString(16));return t.join("-")}(0>e.indexOf(sl)?e.replace(sc,""):e)}(r),e)).text());let n=t.split("|"),o=n.map(e=>sh[e]).filter(Boolean).flat();if(0===o.length)return[];try{let e=await sg.detect(r,o),t=Object.keys(e);return(await Promise.all(t.map(t=>sd(t,e[t])))).map((e,t)=>({name:`satori_${n[t]}_fallback_${r}`,data:e,weight:400,style:"normal",lang:"unknown"===n[t]?void 0:n[t]}))}catch(e){console.error("Failed to load dynamic font for",r,". Error:",e)}};return async(...e)=>{let r=JSON.stringify(e),n=sv.get(r);if(n)return n;let o=await t(...e);return sv.set(r,o),o}};async function sy(e,t,r,n,o){let i=Object.assign({width:1200,height:630,debug:!1},r),a=await e(o,{width:i.width,height:i.height,debug:i.debug,fonts:i.fonts||n,loadAdditionalAsset:sm({emoji:i.emoji})}),s=new t.Resvg(a,{fitTo:{mode:"width",value:i.width}}),u=s.render(),l=u.asPng();return u.free(),s.free(),l}var sD=async({url:e,template:t,fonts:r,imageResponseOptions:n,Response:o})=>{var i;let{fileId:a,nodeId:s}=function(e){let t=e.match(/\/file\/([^/]+)\/[^?]+\?[^#]*node-id=([^&#]+)/),r="",n="";return t&&(r=t[1]||"",n=t[2]||""),{fileId:r,nodeId:n}}(e),u=function(e,t){if(void 0===e)throw Error(t);return e}(process.env.FIGMA_PERSONAL_ACCESS_TOKEN,"Missing environment variable: `FIGMA_PERSONAL_ACCESS_TOKEN`. You can get one at https://www.figma.com/developers/api#authentication"),l=await fetch(`https://api.figma.com/v1/images/${a}?ids=${s}&svg_outline_text=false&format=svg&svg_include_id=true`,{method:"GET",headers:{"X-FIGMA-TOKEN":u},cache:"no-store"}),c=(await l.json()).images[s.replace("-",":")],f=await fetch(c,{cache:"no-store"}),p=await f.text(),{width:h,height:d}=function(e){let t=e.match(/width="(\d+)/),r=e.match(/height="(\d+)/);return t&&r?{width:parseInt(t[1],10),height:parseInt(r[1],10)}:{width:0,height:0}}(p),g=(function(e){let t,r=/<text[^>]*>(.*?)<\/text>/g,n=[];for(;null!==(t=r.exec(e));)n.push(t[0]);return n})(p).map(sx);return new o({key:"0",type:"div",props:{style:{display:"flex"},children:[{type:"img",props:{style:{position:"absolute"},alt:"",width:h,height:d,src:(i=p,"data:image/svg+xml;base64,"+x.from(i).toString("base64"))}},{type:"div",props:{style:{display:"flex",position:"relative",width:"100%"},children:g.map(e=>{let r=t[e.id],n="";n=void 0===r?e.content:sb(r)?r.value:r;let o={},i=!1;if(sb(r)&&r.props){let{centerHorizontally:e,...t}=r.props;o=t,i=e||!1}return i?{type:"div",props:{style:{display:"flex",justifyContent:"center",position:"absolute",width:"100%"},children:{type:"div",props:{style:{color:e.fill,marginTop:`${parseInt(e.y)-parseInt(e.fontSize)}px`,fontWeight:e.fontWeight||"400",fontSize:e.fontSize,fontFamily:e.fontFamily,letterSpacing:e.letterSpacing,textAlign:"center",...o},children:n}}}}:{type:"div",props:{style:{position:"absolute",color:e.fill,left:`${e.x}px`,top:`${parseInt(e.y)-parseInt(e.fontSize)}px`,fontWeight:e.fontWeight||"400",fontSize:e.fontSize,fontFamily:e.fontFamily,letterSpacing:e.letterSpacing,...o},children:n}}})}}]}},{width:h,height:d,fonts:r,...n})},sb=e=>"string"!=typeof e&&void 0!==e&&"value"in e;function sx(e){var t,r,n,o,i,a,s,u,l;let c=(null==(t=e.match(/id="([^"]*)"/))?void 0:t[1])||"",f=(null==(r=e.match(/fill="([^"]*)"/))?void 0:r[1])||"",p=(null==(n=e.match(/font-family="([^"]*)"/))?void 0:n[1])||"",h=(null==(o=e.match(/font-size="([^"]*)"/))?void 0:o[1])||"",d=(null==(i=e.match(/font-weight="([^"]*)"/))?void 0:i[1])||"",g=(null==(a=e.match(/letter-spacing="([^"]*)"/))?void 0:a[1])||"",v=(null==(s=e.match(/<tspan[^>]*x="([^"]*)"/))?void 0:s[1])||"";return{id:c,fill:f,fontFamily:p,fontSize:h,fontWeight:d,letterSpacing:g,x:v,y:(null==(u=e.match(/<tspan[^>]*y="([^"]*)"/))?void 0:u[1])||"",content:(null==(l=e.match(/<tspan[^>]*>([^<]*)<\/tspan>/))?void 0:l[1])||""}}var sw=ss(D),sE=aY(b).then(e=>{f=e}),sS=fetch(new r.U(r(3968))).then(e=>e.arrayBuffer()),sk=class extends Response{constructor(e,t={}){super(new ReadableStream({async start(r){await sE,await sw;let n=await sS,o=await sy(az,aZ,t,[{name:"sans serif",data:n,weight:700,style:"normal"}],e);r.enqueue(o),r.close()}}),{headers:{"content-type":"image/png","cache-control":"public, immutable, no-transform, max-age=31536000",...t.headers},status:t.status,statusText:t.statusText})}},sF=async e=>sD({...e,Response:sk})},3968:e=>{e.exports="blob:noto-sans-v27-latin-regular.5dda3fca77107598.ttf"},5356:e=>{"use strict";e.exports=require("node:buffer")},5521:e=>{"use strict";e.exports=require("node:async_hooks")},5607:(e,t,r)=>{"use strict";var n=r(1092),o=Symbol.for("react.transitional.element");if(Symbol.for("react.fragment"),!n.__SERVER_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE)throw Error('The "react" package in this environment is not configured correctly. The "react-server" condition must be enabled in any environment that runs React Server Components.');t.jsx=function(e,t,r){var n=null;if(void 0!==r&&(n=""+r),void 0!==t.key&&(n=""+t.key),"key"in t)for(var i in r={},t)"key"!==i&&(r[i]=t[i]);else r=t;return{$$typeof:o,type:e,key:n,ref:void 0!==(t=r.ref)?t:null,props:r}}},6487:()=>{},8291:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ComponentMod:()=>er,default:()=>en});var n,o={};r.r(o),r.d(o,{alt:()=>W,contentType:()=>$,default:()=>z,runtime:()=>j,size:()=>G});var i={};r.r(i),r.d(i,{GET:()=>X,alt:()=>W,contentType:()=>$,runtime:()=>j,size:()=>G});var a={};r.r(a),r.d(a,{patchFetch:()=>J,routeModule:()=>Y,serverHooks:()=>K,workAsyncStorage:()=>Z,workUnitAsyncStorage:()=>Q});var s=r(8429),u=r(9874),l=r(8294),c=r(6567),f=r(4144),p=r(5421),h=r(974),d=r(8953);r(6237),r(4318);var g=r(7223),v=r(3842),m=r(6937),y=r(4319),D=r(252),b=r(2438),x=r(9604);let w=new WeakMap,E=(0,b.I)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})});function S(){return this.getAll().map(e=>[e.name,e]).values()}function k(e){for(let e of this.getAll())this.delete(e.name);return e}var F=r(6464);let C=new WeakMap;function _(e){let t=C.get(e);if(t)return t;let r=Promise.resolve(e);return C.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function T(e){return"string"==typeof e?`'${e}'`:"..."}let O=(0,b.I)(A);function A(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}function P(){let e=workAsyncStorage.getStore(),t=workUnitAsyncStorage.getStore();switch((!e||!t)&&throwForMissingRequestStore("draftMode"),t.type){case"request":return L(t.draftMode,e);case"cache":case"unstable-cache":let r=getDraftModeProviderForCacheScope(e,t);if(r)return L(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return B(null);default:return t}}function L(e,t){let r,n=I.get(P);return n||(r=B(e),I.set(e,r),r)}r(7);let I=new WeakMap;function B(e){let t=new R(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class R{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){M("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){M("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let U=(0,b.I)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function M(e){let t=workAsyncStorage.getStore(),r=workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});abortAndThrowOnSynchronousRequestDataAccess(t.route,e,n,r)}else if("prerender-ppr"===r.type)postponeWithTracking(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}class N extends Response{static #e=this.displayName="ImageResponse";constructor(...e){let t=new ReadableStream({async start(t){let n=new(await Promise.resolve().then(r.bind(r,2094))).ImageResponse(...e);if(!n.body)return t.close();let o=n.body.getReader();for(;;){let{done:e,value:r}=await o.read();if(e)return t.close();t.enqueue(r)}}}),n=e[1]||{},o=new Headers({"content-type":"image/png","cache-control":"public, immutable, no-transform, max-age=31536000"});n.headers&&new Headers(n.headers).forEach((e,t)=>o.set(t,e)),super(t,{headers:o,status:n.status,statusText:n.statusText})}}let j="edge",W="Kortix Suna",G={width:1200,height:630},$="image/png";async function z(){try{let e=(await function(){let e=g.J.getStore(),t=v.FP.getStore();if(e){if(t&&"after"===t.phase&&!(0,x.iC)())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return _(F.o.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new y.f(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,n=t;let o=C.get(n);if(o)return o;let i=(0,D.W)(n.renderSignal,"`headers()`");return C.set(n,i),Object.defineProperties(i,{append:{value:function(){let e=`\`headers().append(${T(arguments[0])}, ...)\``,t=A(r,e);(0,m.t3)(r,e,t,n)}},delete:{value:function(){let e=`\`headers().delete(${T(arguments[0])})\``,t=A(r,e);(0,m.t3)(r,e,t,n)}},get:{value:function(){let e=`\`headers().get(${T(arguments[0])})\``,t=A(r,e);(0,m.t3)(r,e,t,n)}},has:{value:function(){let e=`\`headers().has(${T(arguments[0])})\``,t=A(r,e);(0,m.t3)(r,e,t,n)}},set:{value:function(){let e=`\`headers().set(${T(arguments[0])}, ...)\``,t=A(r,e);(0,m.t3)(r,e,t,n)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=A(r,e);(0,m.t3)(r,e,t,n)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=A(r,e);(0,m.t3)(r,e,t,n)}},keys:{value:function(){let e="`headers().keys()`",t=A(r,e);(0,m.t3)(r,e,t,n)}},values:{value:function(){let e="`headers().values()`",t=A(r,e);(0,m.t3)(r,e,t,n)}},entries:{value:function(){let e="`headers().entries()`",t=A(r,e);(0,m.t3)(r,e,t,n)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=A(r,e);(0,m.t3)(r,e,t,n)}}}),i}else"prerender-ppr"===t.type?(0,m.Ui)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,m.xI)("headers",e,t);(0,m.Pk)(e,t)}let o=(0,v.XN)("headers");return _(o.headers)}()).get("host")||"",t=`https://${e}`;return new N((0,d.jsx)("div",{style:{height:"100%",width:"100%",display:"flex",alignItems:"center",justifyContent:"center",background:"black"},children:(0,d.jsx)("img",{src:`${t}/meta.png`,alt:W,style:{width:"100%",height:"100%",objectFit:"contain"}})}),{...G})}catch(e){return console.error("Error generating OpenGraph image:",e),new Response("Failed to generate image",{status:500})}}let q={...o},V=q.default,H=q.generateImageMetadata;if("function"!=typeof V)throw Error('Default export is missing in "C:\\Users\\<USER>\\suna\\frontend\\src\\app\\opengraph-image.tsx"');async function X(e,t){let r,n=await t.params,{__metadata_id__:o,...i}=n||{},a=n?i:void 0;if(H){let e=await H({params:a});if(null==(r=e.find(e=>e.id.toString()===o)?.id))return new h.Rp("Not Found",{status:404})}return V({params:a,id:r})}let Y=new c.AppRouteRouteModule({definition:{kind:f.A.APP_ROUTE,page:"/opengraph-image/route",pathname:"/opengraph-image",filename:"opengraph-image",bundlePath:"app/opengraph-image/route"},resolvedPagePath:"next-metadata-route-loader?filePath=C%3A%5CUsers%5Ceenee%5Csuna%5Cfrontend%5Csrc%5Capp%5Copengraph-image.tsx&isDynamicRouteExtension=1!?__next_metadata_route__",nextConfigOutput:"",userland:i}),{workAsyncStorage:Z,workUnitAsyncStorage:Q,serverHooks:K}=Y;function J(){return(0,p.V5)({workAsyncStorage:Z,workUnitAsyncStorage:Q})}let ee=null==(n=self.__RSC_MANIFEST)?void 0:n["/opengraph-image/route"],et=(e=>e?JSON.parse(e):void 0)(self.__RSC_SERVER_MANIFEST);ee&&et&&(0,u.fQ)({page:"/opengraph-image/route",clientReferenceManifest:ee,serverActionsManifest:et,serverModuleMap:(0,s.e)({serverActionsManifest:et})});let er=a,en=l.s.wrap(Y,{nextConfig:{env:{},webpack:null,eslint:{ignoreDuringBuilds:!1},typescript:{ignoreBuildErrors:!1,tsconfigPath:"tsconfig.json"},distDir:".next",cleanDistDir:!0,assetPrefix:"",cacheMaxMemorySize:0x3200000,configOrigin:"next.config.ts",useFileSystemPublicRoutes:!0,generateEtags:!0,pageExtensions:["tsx","ts","jsx","js"],poweredByHeader:!0,compress:!0,images:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"attachment",remotePatterns:[],unoptimized:!1},devIndicators:{position:"bottom-left"},onDemandEntries:{maxInactiveAge:6e4,pagesBufferLength:5},amp:{canonicalBase:""},basePath:"",sassOptions:{},trailingSlash:!1,i18n:null,productionBrowserSourceMaps:!1,excludeDefaultMomentLocales:!0,serverRuntimeConfig:{},publicRuntimeConfig:{},reactProductionProfiling:!1,reactStrictMode:null,reactMaxHeadersLength:6e3,httpAgentOptions:{keepAlive:!0},logging:{},expireTime:31536e3,staticPageGenerationTimeout:60,modularizeImports:{"@mui/icons-material":{transform:"@mui/icons-material/{{member}}"},lodash:{transform:"lodash/{{member}}"}},outputFileTracingRoot:"C:\\Users\\<USER>\\suna\\frontend",experimental:{nodeMiddleware:!1,cacheLife:{default:{stale:300,revalidate:900,expire:0xfffffffe},seconds:{stale:0,revalidate:1,expire:60},minutes:{stale:300,revalidate:60,expire:3600},hours:{stale:300,revalidate:3600,expire:86400},days:{stale:300,revalidate:86400,expire:604800},weeks:{stale:300,revalidate:604800,expire:2592e3},max:{stale:300,revalidate:2592e3,expire:0xfffffffe}},cacheHandlers:{},cssChunking:!0,multiZoneDraftMode:!1,appNavFailHandling:!1,prerenderEarlyExit:!0,serverMinification:!0,serverSourceMaps:!1,linkNoTouchStart:!1,caseSensitiveRoutes:!1,clientSegmentCache:!1,dynamicOnHover:!1,preloadEntriesOnStart:!0,clientRouterFilter:!0,clientRouterFilterRedirects:!1,fetchCacheKeyPrefix:"",middlewarePrefetch:"flexible",optimisticClientCache:!0,manualClientBasePath:!1,cpus:7,memoryBasedWorkersCount:!1,imgOptConcurrency:null,imgOptTimeoutInSeconds:7,imgOptMaxInputPixels:0xfff8001,imgOptSequentialRead:null,isrFlushToDisk:!0,workerThreads:!1,optimizeCss:!1,nextScriptWorkers:!1,scrollRestoration:!1,externalDir:!1,disableOptimizedLoading:!1,gzipSize:!0,craCompat:!1,esmExternals:!0,fullySpecified:!1,swcTraceProfiling:!1,forceSwcTransforms:!1,largePageDataBytes:128e3,typedRoutes:!1,typedEnv:!1,parallelServerCompiles:!1,parallelServerBuildTraces:!1,ppr:!1,authInterrupts:!1,webpackMemoryOptimizations:!1,optimizeServerReact:!0,useEarlyImport:!1,viewTransition:!1,routerBFCache:!1,staleTimes:{dynamic:0,static:300},serverComponentsHmrCache:!0,staticGenerationMaxConcurrency:8,staticGenerationMinPagesPerWorker:25,dynamicIO:!1,inlineCss:!1,useCache:!1,optimizePackageImports:["lucide-react","date-fns","lodash-es","ramda","antd","react-bootstrap","ahooks","@ant-design/icons","@headlessui/react","@headlessui-float/react","@heroicons/react/20/solid","@heroicons/react/24/solid","@heroicons/react/24/outline","@visx/visx","@tremor/react","rxjs","@mui/material","@mui/icons-material","recharts","react-use","effect","@effect/schema","@effect/platform","@effect/platform-node","@effect/platform-browser","@effect/platform-bun","@effect/sql","@effect/sql-mssql","@effect/sql-mysql2","@effect/sql-pg","@effect/sql-squlite-node","@effect/sql-squlite-bun","@effect/sql-squlite-wasm","@effect/sql-squlite-react-native","@effect/rpc","@effect/rpc-http","@effect/typeclass","@effect/experimental","@effect/opentelemetry","@material-ui/core","@material-ui/icons","@tabler/icons-react","mui-core","react-icons/ai","react-icons/bi","react-icons/bs","react-icons/cg","react-icons/ci","react-icons/di","react-icons/fa","react-icons/fa6","react-icons/fc","react-icons/fi","react-icons/gi","react-icons/go","react-icons/gr","react-icons/hi","react-icons/hi2","react-icons/im","react-icons/io","react-icons/io5","react-icons/lia","react-icons/lib","react-icons/lu","react-icons/md","react-icons/pi","react-icons/ri","react-icons/rx","react-icons/si","react-icons/sl","react-icons/tb","react-icons/tfi","react-icons/ti","react-icons/vsc","react-icons/wi"]},htmlLimitedBots:"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti",bundlePagesRouterDependencies:!1,configFile:"C:\\Users\\<USER>\\suna\\frontend\\next.config.ts",configFileName:"next.config.ts",turbopack:{root:"C:\\Users\\<USER>\\suna\\frontend"}}})},8335:()=>{},8953:(e,t,r)=>{"use strict";e.exports=r(5607)},9317:e=>{e.exports=wasm_ef4866ecae192fd87727067cf2c0c0cf9fb8b020}},e=>{var t=t=>e(e.s=t);e.O(0,[580],()=>t(8291));var r=e.O();(_ENTRIES="undefined"==typeof _ENTRIES?{}:_ENTRIES)["middleware_app/opengraph-image/route"]=r}]);
//# sourceMappingURL=route.js.map