(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@shikijs/langs/dist/qmldir.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const lang = Object.freeze(JSON.parse("{\"displayName\":\"QML Directory\",\"name\":\"qmldir\",\"patterns\":[{\"include\":\"#comment\"},{\"include\":\"#keywords\"},{\"include\":\"#version\"},{\"include\":\"#names\"}],\"repository\":{\"comment\":{\"patterns\":[{\"begin\":\"#\",\"end\":\"$\",\"name\":\"comment.line.number-sign.qmldir\"}]},\"file-name\":{\"patterns\":[{\"match\":\"\\\\b\\\\w+\\\\.(qmltypes|qml|js)\\\\b\",\"name\":\"string.unquoted.qmldir\"}]},\"identifier\":{\"patterns\":[{\"match\":\"\\\\b\\\\w+\\\\b\",\"name\":\"variable.parameter.qmldir\"}]},\"keywords\":{\"patterns\":[{\"match\":\"\\\\b(module|singleton|internal|plugin|classname|typeinfo|depends|designersupported)\\\\b\",\"name\":\"keyword.other.qmldir\"}]},\"module-name\":{\"patterns\":[{\"match\":\"\\\\b[A-Z]\\\\w*\\\\b\",\"name\":\"entity.name.type.qmldir\"}]},\"names\":{\"patterns\":[{\"include\":\"#file-name\"},{\"include\":\"#module-name\"},{\"include\":\"#identifier\"}]},\"version\":{\"patterns\":[{\"match\":\"\\\\b\\\\d+\\\\.\\\\d+\\\\b\",\"name\":\"constant.numeric.qml\"}]}},\"scopeName\":\"source.qmldir\"}"));
const __TURBOPACK__default__export__ = [
    lang
];
}}),
}]);

//# sourceMappingURL=node_modules_%40shikijs_langs_dist_qmldir_mjs_27f17ba7._.js.map