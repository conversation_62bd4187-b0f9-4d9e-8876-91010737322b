{"/api/triggers/qstash/webhook/route": "/api/triggers/qstash/webhook", "/api/integrations/[provider]/callback/route": "/api/integrations/[provider]/callback", "/api/webhooks/trigger/[workflowId]/route": "/api/webhooks/trigger/[workflowId]", "/auth/callback/route": "/auth/callback", "/favicon.ico/route": "/favicon.ico", "/monitoring/route": "/monitoring", "/_not-found/page": "/_not-found", "/auth/reset-password/page": "/auth/reset-password", "/auth/github-popup/page": "/auth/github-popup", "/auth/page": "/auth", "/legal/page": "/legal", "/invitation/page": "/invitation", "/(dashboard)/(teamAccount)/[accountSlug]/page": "/[accountSlug]", "/(dashboard)/dashboard/page": "/dashboard", "/(dashboard)/model-pricing/page": "/model-pricing", "/(home)/page": "/", "/share/[threadId]/page": "/share/[threadId]", "/(dashboard)/(personalAccount)/settings/page": "/settings", "/(dashboard)/(teamAccount)/[accountSlug]/settings/billing/page": "/[accountSlug]/settings/billing", "/(dashboard)/(teamAccount)/[accountSlug]/settings/page": "/[accountSlug]/settings", "/(dashboard)/(teamAccount)/[accountSlug]/settings/members/page": "/[accountSlug]/settings/members", "/(dashboard)/(personalAccount)/settings/teams/page": "/settings/teams", "/(dashboard)/(personalAccount)/settings/billing/page": "/settings/billing", "/(dashboard)/projects/[projectId]/thread/[threadId]/page": "/projects/[projectId]/thread/[threadId]", "/(dashboard)/(personalAccount)/settings/usage-logs/page": "/settings/usage-logs", "/(dashboard)/agents/page": "/agents", "/(dashboard)/settings/credentials/page": "/settings/credentials", "/(dashboard)/agents/[threadId]/page": "/agents/[threadId]", "/(dashboard)/agents/config/[agentId]/page": "/agents/config/[agentId]", "/(dashboard)/agents/config/[agentId]/workflow/[workflowId]/page": "/agents/config/[agentId]/workflow/[workflowId]", "/api/share-page/og-image/route": "/api/share-page/og-image", "/opengraph-image/route": "/opengraph-image"}