(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6301],{8168:(e,t,r)=>{"use strict";r.d(t,{K2:()=>o,eP:()=>a});var n=r(99090),s=r(80861),i=r(97270);r(25731);let o=e=>(0,n.GQ)(s.X.details(e),()=>(0,i.fG)(e),{enabled:!!e,retry:1})(),a=()=>(0,n.Lx)(e=>{let{threadId:t,data:r}=e;return(0,i.e4)(t,r)})()},11518:(e,t,r)=>{"use strict";e.exports=r(82269).style},34544:(e,t,r)=>{Promise.resolve().then(r.bind(r,90073))},35695:(e,t,r)=>{"use strict";var n=r(18999);r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}}),r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},52300:(e,t,r)=>{"use strict";r.d(t,{y:()=>o});var n=r(95155),s=r(12115),i=r(68856);function o(e){let{isSidePanelOpen:t=!1,showHeader:r=!0,messageCount:o=3}=e;return(0,n.jsxs)("div",{className:"flex h-screen",children:[(0,n.jsxs)("div",{className:"flex flex-col flex-1 overflow-hidden transition-all duration-200 ease-in-out",children:[r&&(0,n.jsx)("div",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,n.jsxs)("div",{className:"flex h-14 items-center gap-4 px-4",children:[(0,n.jsx)("div",{className:"flex-1",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(i.Skeleton,{className:"h-6 w-6 rounded-full"}),(0,n.jsx)(i.Skeleton,{className:"h-5 w-40"})]})}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(i.Skeleton,{className:"h-8 w-8 rounded-full"}),(0,n.jsx)(i.Skeleton,{className:"h-8 w-8 rounded-full"})]})]})}),(0,n.jsx)("div",{className:"flex-1 overflow-y-auto px-6 py-4 pb-[5.5rem]",children:(0,n.jsxs)("div",{className:"mx-auto max-w-3xl space-y-6",children:[Array.from({length:o}).map((e,t)=>(0,n.jsx)(s.Fragment,{children:t%2==0?(0,n.jsx)("div",{className:"flex justify-end",children:(0,n.jsx)("div",{className:"max-w-[85%] rounded-lg bg-primary/10 px-4 py-3",children:(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(i.Skeleton,{className:"h-4 w-48"}),(0,n.jsx)(i.Skeleton,{className:"h-4 w-32"})]})})}):(0,n.jsx)("div",{children:(0,n.jsxs)("div",{className:"flex items-start gap-3",children:[(0,n.jsx)(i.Skeleton,{className:"flex-shrink-0 w-5 h-5 mt-2 rounded-full"}),(0,n.jsx)("div",{className:"flex-1 space-y-2",children:(0,n.jsx)("div",{className:"max-w-[90%] w-full rounded-lg bg-muted px-4 py-3",children:(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(i.Skeleton,{className:"h-4 w-full max-w-[360px] mb-2"}),(0,n.jsx)(i.Skeleton,{className:"h-4 w-full max-w-[320px] mb-2"}),(0,n.jsx)(i.Skeleton,{className:"h-4 w-full max-w-[290px]"})]}),t%3==1&&(0,n.jsx)("div",{className:"py-1",children:(0,n.jsx)(i.Skeleton,{className:"h-6 w-32 rounded-md"})}),t%3==1&&(0,n.jsxs)("div",{children:[(0,n.jsx)(i.Skeleton,{className:"h-4 w-full max-w-[340px] mb-2"}),(0,n.jsx)(i.Skeleton,{className:"h-4 w-full max-w-[280px]"})]})]})})})]})})},t)),(0,n.jsx)("div",{children:(0,n.jsxs)("div",{className:"flex items-start gap-3",children:[(0,n.jsx)(i.Skeleton,{className:"flex-shrink-0 w-5 h-5 mt-2 rounded-full"}),(0,n.jsx)("div",{className:"flex-1 space-y-2",children:(0,n.jsxs)("div",{className:"flex items-center gap-1.5 py-1",children:[(0,n.jsx)("div",{className:"h-1.5 w-1.5 rounded-full bg-gray-400/50 animate-pulse"}),(0,n.jsx)("div",{className:"h-1.5 w-1.5 rounded-full bg-gray-400/50 animate-pulse delay-150"}),(0,n.jsx)("div",{className:"h-1.5 w-1.5 rounded-full bg-gray-400/50 animate-pulse delay-300"})]})})]})})]})})]}),t&&(0,n.jsx)("div",{className:"hidden sm:block",children:(0,n.jsx)("div",{className:"h-screen w-[450px] border-l",children:(0,n.jsxs)("div",{className:"p-4",children:[(0,n.jsx)(i.Skeleton,{className:"h-8 w-32 mb-4"}),(0,n.jsx)(i.Skeleton,{className:"h-20 w-full rounded-md mb-4"}),(0,n.jsx)(i.Skeleton,{className:"h-40 w-full rounded-md"})]})})})]})}},59434:(e,t,r)=>{"use strict";r.d(t,{$3:()=>l,Hz:()=>a,W5:()=>d,cn:()=>o});var n=r(52596),s=r(81949),i=r(39688);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.QP)((0,n.$)(t))}let a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"rgba(180, 180, 180)";if(!e)return t;try{if("string"==typeof e&&e.startsWith("var(")){let t=document.createElement("div");t.style.color=e,document.body.appendChild(t);let r=window.getComputedStyle(t).color;return document.body.removeChild(t),s.formatRGBA(s.parse(r))}return s.formatRGBA(s.parse(e))}catch(e){return console.error("Color parsing failed:",e),t}},l=(e,t)=>e.startsWith("rgb")?s.formatRGBA(s.alpha(s.parse(e),t)):e;function d(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return e.length<=t?e:e.slice(0,t)+"..."}},68375:()=>{},68856:(e,t,r)=>{"use strict";r.d(t,{Skeleton:()=>a});var n=r(95155),s=r(11518),i=r.n(s),o=r(59434);function a(e){let{className:t,...r}=e;return(0,n.jsxs)("div",{...r,className:"jsx-1e687dde282a8b11 "+(r&&null!=r.className&&r.className||(0,o.cn)("relative overflow-hidden rounded-md","bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10","background-animate",t)||""),children:[(0,n.jsx)("div",{className:"jsx-1e687dde282a8b11 shimmer-wrapper",children:(0,n.jsx)("div",{className:"jsx-1e687dde282a8b11 shimmer"})}),(0,n.jsx)(i(),{id:"1e687dde282a8b11",children:".background-animate.jsx-1e687dde282a8b11{-webkit-background-size:200%200%;-moz-background-size:200%200%;-o-background-size:200%200%;background-size:200%200%;-webkit-animation:gradientAnimation 1s ease infinite;-moz-animation:gradientAnimation 1s ease infinite;-o-animation:gradientAnimation 1s ease infinite;animation:gradientAnimation 1s ease infinite}@-webkit-keyframes gradientAnimation{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}@-moz-keyframes gradientAnimation{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}@-o-keyframes gradientAnimation{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}@keyframes gradientAnimation{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}.shimmer-wrapper.jsx-1e687dde282a8b11{position:absolute;top:0;left:0;width:100%;height:100%;overflow:hidden}.shimmer.jsx-1e687dde282a8b11{width:50%;height:100%;background:-webkit-linear-gradient(left,rgba(255,255,255,0)0%,rgba(255,255,255,.3)50%,rgba(255,255,255,0)100%);background:-moz-linear-gradient(left,rgba(255,255,255,0)0%,rgba(255,255,255,.3)50%,rgba(255,255,255,0)100%);background:-o-linear-gradient(left,rgba(255,255,255,0)0%,rgba(255,255,255,.3)50%,rgba(255,255,255,0)100%);background:linear-gradient(90deg,rgba(255,255,255,0)0%,rgba(255,255,255,.3)50%,rgba(255,255,255,0)100%);-webkit-animation:shimmerAnimation 1s infinite;-moz-animation:shimmerAnimation 1s infinite;-o-animation:shimmerAnimation 1s infinite;animation:shimmerAnimation 1s infinite;position:absolute;top:0;left:-150%}@-webkit-keyframes shimmerAnimation{to{left:150%}}@-moz-keyframes shimmerAnimation{to{left:150%}}@-o-keyframes shimmerAnimation{to{left:150%}}@keyframes shimmerAnimation{to{left:150%}}"})]})}r(12115)},80861:(e,t,r)=>{"use strict";r.d(t,{X:()=>n});let n=(0,r(99090).DY)({all:["threads"],details:e=>["thread",e],messages:e=>["thread",e,"messages"],project:e=>["project",e],publicProjects:()=>["public-projects"],agentRuns:e=>["thread",e,"agent-runs"],billingStatus:["billing","status"],byProject:e=>["project",e,"threads"]})},82269:(e,t,r)=>{"use strict";var n=r(49509);r(68375);var s=r(12115),i=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(s),o=void 0!==n&&n.env&&!0,a=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,r=t.name,n=void 0===r?"stylesheet":r,s=t.optimizeForSpeed,i=void 0===s?o:s;d(a(n),"`name` must be a string"),this._name=n,this._deletedRulePlaceholder="#"+n+"-deleted-rule____{}",d("boolean"==typeof i,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=i,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var l="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=l?l.getAttribute("content"):null}var t,r=e.prototype;return r.setOptimizeForSpeed=function(e){d("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),d(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},r.isOptimizeForSpeed=function(){return this._optimizeForSpeed},r.inject=function(){var e=this;if(d(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(o||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},r.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},r.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},r.insertRule=function(e,t){if(d(a(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var r=this.getSheet();"number"!=typeof t&&(t=r.cssRules.length);try{r.insertRule(e,t)}catch(t){return o||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var n=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,n))}return this._rulesCount++},r.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var r="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(n){o||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}}else{var n=this._tags[e];d(n,"old rule at index `"+e+"` not found"),n.textContent=t}return e},r.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];d(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},r.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},r.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,r){return r?t=t.concat(Array.prototype.map.call(e.getSheetForTag(r).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},r.makeStyleTag=function(e,t,r){t&&d(a(t),"makeStyleTag accepts only strings as second parameter");var n=document.createElement("style");this._nonce&&n.setAttribute("nonce",this._nonce),n.type="text/css",n.setAttribute("data-"+e,""),t&&n.appendChild(document.createTextNode(t));var s=document.head||document.getElementsByTagName("head")[0];return r?s.insertBefore(n,r):s.appendChild(n),n},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,t),e}();function d(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var c=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},u={};function h(e,t){if(!t)return"jsx-"+e;var r=String(t),n=e+r;return u[n]||(u[n]="jsx-"+c(e+"-"+r)),u[n]}function m(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var r=e+t;return u[r]||(u[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),u[r]}var f=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,n=void 0===r?null:r,s=t.optimizeForSpeed,i=void 0!==s&&s;this._sheet=n||new l({name:"styled-jsx",optimizeForSpeed:i}),this._sheet.inject(),n&&"boolean"==typeof i&&(this._sheet.setOptimizeForSpeed(i),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var r=this.getIdAndRules(e),n=r.styleId,s=r.rules;if(n in this._instancesCounts){this._instancesCounts[n]+=1;return}var i=s.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[n]=i,this._instancesCounts[n]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var n=this._fromServer&&this._fromServer[r];n?(n.parentNode.removeChild(n),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],n=e[1];return i.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:n}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,n=e.id;if(r){var s=h(n,r);return{styleId:s,rules:Array.isArray(t)?t.map(function(e){return m(s,e)}):[m(s,t)]}}return{styleId:h(n),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),p=s.createContext(null);p.displayName="StyleSheetContext";var g=i.default.useInsertionEffect||i.default.useLayoutEffect,v="undefined"!=typeof window?new f:void 0;function _(e){var t=v||s.useContext(p);return t&&("undefined"==typeof window?t.add(e):g(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}_.dynamic=function(e){return e.map(function(e){return h(e[0],e[1])}).join(" ")},t.style=_},90073:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var n=r(95155),s=r(12115),i=r(35695),o=r(8168),a=r(52300);function l(e){let{threadId:t}=e,r=(0,i.useRouter)(),l=(0,o.K2)(t);return((0,s.useEffect)(()=>{var e;(null==(e=l.data)?void 0:e.project_id)&&r.replace("/projects/".concat(l.data.project_id,"/thread/").concat(t))},[l.data,t,r]),l.isError)?(r.replace("/dashboard"),null):(0,n.jsx)(a.y,{isSidePanelOpen:!1})}function d(e){let{params:t}=e,r=s.use(t);return(0,n.jsx)(l,{threadId:r.threadId})}},97270:(e,t,r)=>{"use strict";r.d(t,{KX:()=>l,U1:()=>d,e4:()=>o,fG:()=>i});var n=r(52643);let s="http://localhost:8000/api",i=async e=>{let t=(0,n.U)(),{data:r,error:s}=await t.from("threads").select("*").eq("thread_id",e).single();if(s)throw s;return r},o=async(e,t)=>{let r=(0,n.U)(),s={...t},{data:i,error:o}=await r.from("threads").update(s).eq("thread_id",e).select().single();if(o)throw console.error("Error updating thread:",o),Error("Error updating thread: ".concat(o.message));return i},a=async e=>{try{let t=(0,n.U)(),{data:{session:r}}=await t.auth.getSession(),i={"Content-Type":"application/json"};(null==r?void 0:r.access_token)&&(i.Authorization="Bearer ".concat(r.access_token)),(await fetch("".concat(s,"/sandboxes/").concat(e),{method:"DELETE",headers:i})).ok||console.warn("Failed to delete sandbox, continuing with thread deletion")}catch(e){console.warn("Error deleting sandbox, continuing with thread deletion:",e)}},l=async(e,t)=>{try{let s=(0,n.U)();if(t)await a(t);else{let{data:t,error:n}=await s.from("threads").select("project_id").eq("thread_id",e).single();if(n)throw console.error("Error fetching thread:",n),Error("Error fetching thread: ".concat(n.message));if(null==t?void 0:t.project_id){var r;let{data:e}=await s.from("projects").select("sandbox").eq("project_id",t.project_id).single();(null==e||null==(r=e.sandbox)?void 0:r.id)&&await a(e.sandbox.id)}}console.log("Deleting all agent runs for thread ".concat(e));let{error:i}=await s.from("agent_runs").delete().eq("thread_id",e);if(i)throw console.error("Error deleting agent runs:",i),Error("Error deleting agent runs: ".concat(i.message));console.log("Deleting all messages for thread ".concat(e));let{error:o}=await s.from("messages").delete().eq("thread_id",e);if(o)throw console.error("Error deleting messages:",o),Error("Error deleting messages: ".concat(o.message));console.log("Deleting thread ".concat(e));let{error:l}=await s.from("threads").delete().eq("thread_id",e);if(l)throw console.error("Error deleting thread:",l),Error("Error deleting thread: ".concat(l.message));console.log("Thread ".concat(e," successfully deleted with all related items"))}catch(e){throw console.error("Error deleting thread and related items:",e),e}},d=async e=>{let t=(0,n.U)();try{var r;let{data:n,error:i}=await t.from("projects").select("*").eq("project_id",e).single();if(i){if("PGRST116"===i.code)throw Error("Project not found or not accessible: ".concat(e));throw i}return console.log("Raw project data from database:",n),(null==(r=n.sandbox)?void 0:r.id)&&(async()=>{try{let{data:{session:r}}=await t.auth.getSession(),n={"Content-Type":"application/json"};(null==r?void 0:r.access_token)&&(n.Authorization="Bearer ".concat(r.access_token)),console.log("Ensuring sandbox is active for project ".concat(e,"..."));let i=await fetch("".concat(s,"/project/").concat(e,"/sandbox/ensure-active"),{method:"POST",headers:n});if(i.ok)console.log("Sandbox activation successful");else{let e=await i.text().catch(()=>"No error details available");console.warn("Failed to ensure sandbox is active: ".concat(i.status," ").concat(i.statusText),e)}}catch(e){console.warn("Failed to ensure sandbox is active:",e)}})(),{id:n.project_id,name:n.name||"",description:n.description||"",account_id:n.account_id,is_public:n.is_public||!1,created_at:n.created_at,sandbox:n.sandbox||{id:"",pass:"",vnc_preview:"",sandbox_url:""}}}catch(t){throw console.error("Error fetching project ".concat(e,":"),t),t}}},99090:(e,t,r)=>{"use strict";r.d(t,{DY:()=>o,GQ:()=>a,Lx:()=>l});var n=r(28755),s=r(5041),i=r(33356);let o=e=>e;function a(e,t,r){return s=>(0,n.I)({queryKey:e,queryFn:t,...r,...s})}function l(e,t){return r=>{let{errorContext:n,...o}=t||{},{errorContext:a,...l}=r||{};return(0,s.n)({mutationFn:e,onError:(e,t,r)=>{var s,d;(null==l?void 0:l.onError)||(null==o?void 0:o.onError)||(0,i.hS)(e,a||n),null==o||null==(s=o.onError)||s.call(o,e,t,r),null==l||null==(d=l.onError)||d.call(l,e,t,r)},...o,...l})}}}},e=>{var t=t=>e(e.s=t);e.O(0,[2969,1935,6671,3860,1171,6686,8441,1684,7358],()=>t(34544)),_N_E=e.O()}]);