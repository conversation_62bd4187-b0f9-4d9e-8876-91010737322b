"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9001],{15452:(e,n,t)=>{t.d(n,{G$:()=>K,Hs:()=>R,UC:()=>et,VY:()=>eo,ZL:()=>ee,bL:()=>z,bm:()=>ea,hE:()=>er,hJ:()=>en,l9:()=>Q});var r=t(12115),o=t(85185),a=t(6101),i=t(46081),l=t(61285),u=t(5845),s=t(19178),d=t(25519),c=t(34378),f=t(28905),p=t(63655),m=t(92293),g=t(58890),v=t(38168),N=t(99708),h=t(95155),y="Dialog",[D,R]=(0,i.A)(y),[O,b]=D(y),w=e=>{let{__scopeDialog:n,children:t,open:o,defaultOpen:a,onOpenChange:i,modal:s=!0}=e,d=r.useRef(null),c=r.useRef(null),[f,p]=(0,u.i)({prop:o,defaultProp:null!=a&&a,onChange:i,caller:y});return(0,h.jsx)(O,{scope:n,triggerRef:d,contentRef:c,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:s,children:t})};w.displayName=y;var C="DialogTrigger",I=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,i=b(C,t),l=(0,a.s)(n,i.triggerRef);return(0,h.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":H(i.open),...r,ref:l,onClick:(0,o.m)(e.onClick,i.onOpenToggle)})});I.displayName=C;var E="DialogPortal",[j,x]=D(E,{forceMount:void 0}),P=e=>{let{__scopeDialog:n,forceMount:t,children:o,container:a}=e,i=b(E,n);return(0,h.jsx)(j,{scope:n,forceMount:t,children:r.Children.map(o,e=>(0,h.jsx)(f.C,{present:t||i.open,children:(0,h.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};P.displayName=E;var A="DialogOverlay",M=r.forwardRef((e,n)=>{let t=x(A,e.__scopeDialog),{forceMount:r=t.forceMount,...o}=e,a=b(A,e.__scopeDialog);return a.modal?(0,h.jsx)(f.C,{present:r||a.open,children:(0,h.jsx)(_,{...o,ref:n})}):null});M.displayName=A;var T=(0,N.TL)("DialogOverlay.RemoveScroll"),_=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=b(A,t);return(0,h.jsx)(g.A,{as:T,allowPinchZoom:!0,shards:[o.contentRef],children:(0,h.jsx)(p.sG.div,{"data-state":H(o.open),...r,ref:n,style:{pointerEvents:"auto",...r.style}})})}),F="DialogContent",k=r.forwardRef((e,n)=>{let t=x(F,e.__scopeDialog),{forceMount:r=t.forceMount,...o}=e,a=b(F,e.__scopeDialog);return(0,h.jsx)(f.C,{present:r||a.open,children:a.modal?(0,h.jsx)(U,{...o,ref:n}):(0,h.jsx)(S,{...o,ref:n})})});k.displayName=F;var U=r.forwardRef((e,n)=>{let t=b(F,e.__scopeDialog),i=r.useRef(null),l=(0,a.s)(n,t.contentRef,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,v.Eq)(e)},[]),(0,h.jsx)(L,{...e,ref:l,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var n;e.preventDefault(),null==(n=t.triggerRef.current)||n.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let n=e.detail.originalEvent,t=0===n.button&&!0===n.ctrlKey;(2===n.button||t)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),S=r.forwardRef((e,n)=>{let t=b(F,e.__scopeDialog),o=r.useRef(!1),a=r.useRef(!1);return(0,h.jsx)(L,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:n=>{var r,i;null==(r=e.onCloseAutoFocus)||r.call(e,n),n.defaultPrevented||(o.current||null==(i=t.triggerRef.current)||i.focus(),n.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:n=>{var r,i;null==(r=e.onInteractOutside)||r.call(e,n),n.defaultPrevented||(o.current=!0,"pointerdown"===n.detail.originalEvent.type&&(a.current=!0));let l=n.target;(null==(i=t.triggerRef.current)?void 0:i.contains(l))&&n.preventDefault(),"focusin"===n.detail.originalEvent.type&&a.current&&n.preventDefault()}})}),L=r.forwardRef((e,n)=>{let{__scopeDialog:t,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:l,...u}=e,c=b(F,t),f=r.useRef(null),p=(0,a.s)(n,f);return(0,m.Oh)(),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(d.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,h.jsx)(s.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":H(c.open),...u,ref:p,onDismiss:()=>c.onOpenChange(!1)})}),(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(Y,{titleId:c.titleId}),(0,h.jsx)($,{contentRef:f,descriptionId:c.descriptionId})]})]})}),W="DialogTitle",G=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=b(W,t);return(0,h.jsx)(p.sG.h2,{id:o.titleId,...r,ref:n})});G.displayName=W;var B="DialogDescription",q=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=b(B,t);return(0,h.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:n})});q.displayName=B;var V="DialogClose",Z=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,a=b(V,t);return(0,h.jsx)(p.sG.button,{type:"button",...r,ref:n,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function H(e){return e?"open":"closed"}Z.displayName=V;var J="DialogTitleWarning",[K,X]=(0,i.q)(J,{contentName:F,titleName:W,docsSlug:"dialog"}),Y=e=>{let{titleId:n}=e,t=X(J),o="`".concat(t.contentName,"` requires a `").concat(t.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(t.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(t.docsSlug);return r.useEffect(()=>{n&&(document.getElementById(n)||console.error(o))},[o,n]),null},$=e=>{let{contentRef:n,descriptionId:t}=e,o=X("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=n.current)?void 0:e.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(a))},[a,n,t]),null},z=w,Q=I,ee=P,en=M,et=k,er=G,eo=q,ea=Z},28905:(e,n,t)=>{t.d(n,{C:()=>i});var r=t(12115),o=t(6101),a=t(52712),i=e=>{let{present:n,children:t}=e,i=function(e){var n,t;let[o,i]=r.useState(),u=r.useRef(null),s=r.useRef(e),d=r.useRef("none"),[c,f]=(n=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,n)=>{let r=t[e][n];return null!=r?r:e},n));return r.useEffect(()=>{let e=l(u.current);d.current="mounted"===c?e:"none"},[c]),(0,a.N)(()=>{let n=u.current,t=s.current;if(t!==e){let r=d.current,o=l(n);e?f("MOUNT"):"none"===o||(null==n?void 0:n.display)==="none"?f("UNMOUNT"):t&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,a.N)(()=>{if(o){var e;let n,t=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=l(u.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",n=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(d.current=l(u.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(n),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,i(e)},[])}}(n),u="function"==typeof t?t({present:i.isPresent}):r.Children.only(t),s=(0,o.s)(i.ref,function(e){var n,t;let r=null==(n=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:n.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(t=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:t.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof t||i.isPresent?r.cloneElement(u,{ref:s}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},35695:(e,n,t)=>{var r=t(18999);t.o(r,"redirect")&&t.d(n,{redirect:function(){return r.redirect}}),t.o(r,"useParams")&&t.d(n,{useParams:function(){return r.useParams}}),t.o(r,"usePathname")&&t.d(n,{usePathname:function(){return r.usePathname}}),t.o(r,"useRouter")&&t.d(n,{useRouter:function(){return r.useRouter}}),t.o(r,"useSearchParams")&&t.d(n,{useSearchParams:function(){return r.useSearchParams}})},54416:(e,n,t)=>{t.d(n,{A:()=>r});let r=(0,t(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])}}]);