{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/GoogleSignIn.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useCallback, useRef, useState } from 'react';\r\nimport Script from 'next/script';\r\nimport { createClient } from '@/lib/supabase/client';\r\nimport { useTheme } from 'next-themes';\r\n\r\n// Add type declarations for Google One Tap\r\ndeclare global {\r\n  interface Window {\r\n    handleGoogleSignIn?: (response: GoogleSignInResponse) => void;\r\n    google: {\r\n      accounts: {\r\n        id: {\r\n          initialize: (config: GoogleInitializeConfig) => void;\r\n          renderButton: (\r\n            element: HTMLElement,\r\n            options: GoogleButtonOptions,\r\n          ) => void;\r\n          prompt: (\r\n            callback?: (notification: GoogleNotification) => void,\r\n          ) => void;\r\n          cancel: () => void;\r\n        };\r\n      };\r\n    };\r\n  }\r\n}\r\n\r\n// Define types for Google Sign-In\r\ninterface GoogleSignInResponse {\r\n  credential: string;\r\n  clientId?: string;\r\n  select_by?: string;\r\n}\r\n\r\ninterface GoogleInitializeConfig {\r\n  client_id: string | undefined;\r\n  callback: ((response: GoogleSignInResponse) => void) | undefined;\r\n  nonce?: string;\r\n  use_fedcm?: boolean;\r\n  context?: string;\r\n  itp_support?: boolean;\r\n}\r\n\r\ninterface GoogleButtonOptions {\r\n  type?: string;\r\n  theme?: string;\r\n  size?: string;\r\n  text?: string;\r\n  shape?: string;\r\n  logoAlignment?: string;\r\n  width?: number;\r\n}\r\n\r\ninterface GoogleNotification {\r\n  isNotDisplayed: () => boolean;\r\n  getNotDisplayedReason: () => string;\r\n  isSkippedMoment: () => boolean;\r\n  getSkippedReason: () => string;\r\n  isDismissedMoment: () => boolean;\r\n  getDismissedReason: () => string;\r\n}\r\n\r\ninterface GoogleSignInProps {\r\n  returnUrl?: string;\r\n}\r\n\r\nexport default function GoogleSignIn({ returnUrl }: GoogleSignInProps) {\r\n  const googleClientId = process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID;\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const { resolvedTheme } = useTheme();\r\n\r\n  const handleGoogleSignIn = useCallback(\r\n    async (response: GoogleSignInResponse) => {\r\n      try {\r\n        setIsLoading(true);\r\n        const supabase = createClient();\r\n\r\n        console.log('Starting Google sign in process');\r\n\r\n        const { error } = await supabase.auth.signInWithIdToken({\r\n          provider: 'google',\r\n          token: response.credential,\r\n        });\r\n\r\n        if (error) throw error;\r\n\r\n        console.log(\r\n          'Google sign in successful, preparing redirect to:',\r\n          returnUrl || '/dashboard',\r\n        );\r\n\r\n        // Add a longer delay before redirecting to ensure localStorage is properly saved\r\n        setTimeout(() => {\r\n          console.log('Executing redirect now to:', returnUrl || '/dashboard');\r\n          window.location.href = returnUrl || '/dashboard';\r\n        }, 500); // Increased from 100ms to 500ms\r\n      } catch (error) {\r\n        console.error('Error signing in with Google:', error);\r\n        setIsLoading(false);\r\n      }\r\n    },\r\n    [returnUrl],\r\n  );\r\n\r\n  useEffect(() => {\r\n    // Assign the callback to window object so it can be called from the Google button\r\n    window.handleGoogleSignIn = handleGoogleSignIn;\r\n\r\n    if (window.google && googleClientId) {\r\n      window.google.accounts.id.initialize({\r\n        client_id: googleClientId,\r\n        callback: handleGoogleSignIn,\r\n        use_fedcm: true,\r\n        context: 'signin',\r\n        itp_support: true,\r\n      });\r\n    }\r\n\r\n    return () => {\r\n      // Cleanup\r\n      delete window.handleGoogleSignIn;\r\n      if (window.google) {\r\n        window.google.accounts.id.cancel();\r\n      }\r\n    };\r\n  }, [googleClientId, handleGoogleSignIn]);\r\n\r\n  if (!googleClientId) {\r\n    return (\r\n      <button\r\n        disabled\r\n        className=\"w-full h-12 flex items-center justify-center gap-2 text-sm font-medium tracking-wide rounded-full bg-background border border-border opacity-60 cursor-not-allowed\"\r\n      >\r\n        <svg className=\"w-5 h-5\" viewBox=\"0 0 24 24\">\r\n          <path\r\n            d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"\r\n            fill=\"#4285F4\"\r\n          />\r\n          <path\r\n            d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"\r\n            fill=\"#34A853\"\r\n          />\r\n          <path\r\n            d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"\r\n            fill=\"#FBBC05\"\r\n          />\r\n          <path\r\n            d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"\r\n            fill=\"#EA4335\"\r\n          />\r\n        </svg>\r\n        Google Sign-In Not Configured\r\n      </button>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {/* Google One Tap container */}\r\n      <div\r\n        id=\"g_id_onload\"\r\n        data-client_id={googleClientId}\r\n        data-context=\"signin\"\r\n        data-ux_mode=\"popup\"\r\n        data-auto_prompt=\"false\"\r\n        data-itp_support=\"true\"\r\n        data-callback=\"handleGoogleSignIn\"\r\n      />\r\n\r\n      {/* Google Sign-In button container styled to match site design */}\r\n      <div id=\"google-signin-button\" className=\"w-full h-12\">\r\n        {/* The Google button will be rendered here */}\r\n      </div>\r\n\r\n      <Script\r\n        src=\"https://accounts.google.com/gsi/client\"\r\n        strategy=\"afterInteractive\"\r\n        onLoad={() => {\r\n          if (window.google && googleClientId) {\r\n            // Style the button after Google script loads\r\n            const buttonContainer = document.getElementById(\r\n              'google-signin-button',\r\n            );\r\n            if (buttonContainer) {\r\n              window.google.accounts.id.renderButton(buttonContainer, {\r\n                type: 'standard',\r\n                theme: resolvedTheme === 'dark' ? 'filled_black' : 'outline',\r\n                size: 'large',\r\n                text: 'continue_with',\r\n                shape: 'pill',\r\n                logoAlignment: 'left',\r\n                width: buttonContainer.offsetWidth,\r\n              });\r\n\r\n              // Apply custom styles to match site design\r\n              setTimeout(() => {\r\n                const googleButton =\r\n                  buttonContainer.querySelector('div[role=\"button\"]');\r\n                if (googleButton instanceof HTMLElement) {\r\n                  googleButton.style.borderRadius = '9999px';\r\n                  googleButton.style.width = '100%';\r\n                  googleButton.style.height = '56px';\r\n                  googleButton.style.border = '1px solid var(--border)';\r\n                  googleButton.style.background = 'var(--background)';\r\n                  googleButton.style.transition = 'all 0.2s';\r\n                }\r\n              }, 100);\r\n            }\r\n          }\r\n        }}\r\n      />\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAoEe,SAAS,aAAa,EAAE,SAAS,EAAqB;IACnE,MAAM,iBAAiB,QAAQ,GAAG,CAAC,4BAA4B;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEjC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EACnC,OAAO;QACL,IAAI;YACF,aAAa;YACb,MAAM,WAAW,CAAA,GAAA,gIAAA,CAAA,eAAY,AAAD;YAE5B,QAAQ,GAAG,CAAC;YAEZ,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,iBAAiB,CAAC;gBACtD,UAAU;gBACV,OAAO,SAAS,UAAU;YAC5B;YAEA,IAAI,OAAO,MAAM;YAEjB,QAAQ,GAAG,CACT,qDACA,aAAa;YAGf,iFAAiF;YACjF,WAAW;gBACT,QAAQ,GAAG,CAAC,8BAA8B,aAAa;gBACvD,OAAO,QAAQ,CAAC,IAAI,GAAG,aAAa;YACtC,GAAG,MAAM,gCAAgC;QAC3C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,aAAa;QACf;IACF,GACA;QAAC;KAAU;IAGb,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kFAAkF;QAClF,OAAO,kBAAkB,GAAG;QAE5B,IAAI,OAAO,MAAM,IAAI,gBAAgB;YACnC,OAAO,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC;gBACnC,WAAW;gBACX,UAAU;gBACV,WAAW;gBACX,SAAS;gBACT,aAAa;YACf;QACF;QAEA,OAAO;YACL,UAAU;YACV,OAAO,OAAO,kBAAkB;YAChC,IAAI,OAAO,MAAM,EAAE;gBACjB,OAAO,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,MAAM;YAClC;QACF;IACF,GAAG;QAAC;QAAgB;KAAmB;IAEvC,IAAI,CAAC,gBAAgB;QACnB,qBACE,8OAAC;YACC,QAAQ;YACR,WAAU;;8BAEV,8OAAC;oBAAI,WAAU;oBAAU,SAAQ;;sCAC/B,8OAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,8OAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,8OAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,8OAAC;4BACC,GAAE;4BACF,MAAK;;;;;;;;;;;;gBAEH;;;;;;;IAIZ;IAEA,qBACE;;0BAEE,8OAAC;gBACC,IAAG;gBACH,kBAAgB;gBAChB,gBAAa;gBACb,gBAAa;gBACb,oBAAiB;gBACjB,oBAAiB;gBACjB,iBAAc;;;;;;0BAIhB,8OAAC;gBAAI,IAAG;gBAAuB,WAAU;;;;;;0BAIzC,8OAAC,8HAAA,CAAA,UAAM;gBACL,KAAI;gBACJ,UAAS;gBACT,QAAQ;oBACN,IAAI,OAAO,MAAM,IAAI,gBAAgB;wBACnC,6CAA6C;wBAC7C,MAAM,kBAAkB,SAAS,cAAc,CAC7C;wBAEF,IAAI,iBAAiB;4BACnB,OAAO,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,YAAY,CAAC,iBAAiB;gCACtD,MAAM;gCACN,OAAO,kBAAkB,SAAS,iBAAiB;gCACnD,MAAM;gCACN,MAAM;gCACN,OAAO;gCACP,eAAe;gCACf,OAAO,gBAAgB,WAAW;4BACpC;4BAEA,2CAA2C;4BAC3C,WAAW;gCACT,MAAM,eACJ,gBAAgB,aAAa,CAAC;gCAChC,IAAI,wBAAwB,aAAa;oCACvC,aAAa,KAAK,CAAC,YAAY,GAAG;oCAClC,aAAa,KAAK,CAAC,KAAK,GAAG;oCAC3B,aAAa,KAAK,CAAC,MAAM,GAAG;oCAC5B,aAAa,KAAK,CAAC,MAAM,GAAG;oCAC5B,aAAa,KAAK,CAAC,UAAU,GAAG;oCAChC,aAAa,KAAK,CAAC,UAAU,GAAG;gCAClC;4BACF,GAAG;wBACL;oBACF;gBACF;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/billing/usage-limit-alert.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { AlertTriangle, X } from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\nimport { useRouter } from 'next/navigation';\r\nimport { cn } from '@/lib/utils';\r\n\r\ninterface BillingErrorAlertProps {\r\n  message?: string;\r\n  currentUsage?: number;\r\n  limit?: number;\r\n  accountId?: string | null;\r\n  onDismiss: () => void;\r\n  isOpen: boolean;\r\n}\r\n\r\nexport function BillingErrorAlert({\r\n  message,\r\n  currentUsage,\r\n  limit,\r\n  accountId,\r\n  onDismiss,\r\n  isOpen,\r\n}: BillingErrorAlertProps) {\r\n  const router = useRouter();\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed bottom-4 right-4 z-[9999]\">\r\n      <div className=\"bg-destructive/15 backdrop-blur-sm border border-destructive/30 rounded-lg p-5 shadow-lg max-w-md\">\r\n        <div className=\"flex items-start gap-4\">\r\n          <div className=\"flex-shrink-0 bg-destructive/20 p-2 rounded-full\">\r\n            <AlertTriangle className=\"h-5 w-5 text-destructive\" />\r\n          </div>\r\n          <div className=\"flex-1\">\r\n            <div className=\"flex justify-between items-start mb-2\">\r\n              <h3 className=\"text-sm font-semibold text-destructive\">\r\n                Usage Limit Reached\r\n              </h3>\r\n              <Button\r\n                variant=\"ghost\"\r\n                size=\"icon\"\r\n                onClick={onDismiss}\r\n                className=\"h-6 w-6 p-0 text-muted-foreground hover:text-foreground\"\r\n              >\r\n                <X className=\"h-4 w-4\" />\r\n              </Button>\r\n            </div>\r\n            <p className=\"text-sm text-muted-foreground mb-3\">{message}</p>\r\n\r\n            <div className=\"flex gap-2\">\r\n              <Button\r\n                variant=\"outline\"\r\n                size=\"sm\"\r\n                onClick={onDismiss}\r\n                className=\"text-xs\"\r\n              >\r\n                Dismiss\r\n              </Button>\r\n              <Button\r\n                size=\"sm\"\r\n                onClick={() =>\r\n                  router.push(`/settings/billing?accountId=${accountId}`)\r\n                }\r\n                className=\"text-xs bg-destructive hover:bg-destructive/90\"\r\n              >\r\n                Upgrade Plan\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AAJA;;;;;AAgBO,SAAS,kBAAkB,EAChC,OAAO,EACP,YAAY,EACZ,KAAK,EACL,SAAS,EACT,SAAS,EACT,MAAM,EACiB;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;;;;;;kCAE3B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDAGvD,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAGjB,8OAAC;gCAAE,WAAU;0CAAsC;;;;;;0CAEnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAS,IACP,OAAO,IAAI,CAAC,CAAC,4BAA4B,EAAE,WAAW;wCAExD,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 336, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/billing/billing-modal.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport {\r\n    <PERSON><PERSON>,\r\n    <PERSON>alogContent,\r\n    DialogHeader,\r\n    DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport { Button } from '@/components/ui/button';\r\nimport { PricingSection } from '@/components/home/<USER>/pricing-section';\r\nimport { isLocalMode } from '@/lib/config';\r\nimport {\r\n    getSubscription,\r\n    createPortalSession,\r\n    SubscriptionStatus,\r\n} from '@/lib/api';\r\nimport { useAuth } from '@/components/AuthProvider';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { X } from 'lucide-react';\r\n\r\ninterface BillingModalProps {\r\n    open: boolean;\r\n    onOpenChange: (open: boolean) => void;\r\n    returnUrl?: string;\r\n}\r\n\r\nexport function BillingModal({ open, onOpenChange, returnUrl = window?.location?.href || '/' }: BillingModalProps) {\r\n    const { session, isLoading: authLoading } = useAuth();\r\n    const [subscriptionData, setSubscriptionData] = useState<SubscriptionStatus | null>(null);\r\n    const [isLoading, setIsLoading] = useState(true);\r\n    const [error, setError] = useState<string | null>(null);\r\n    const [isManaging, setIsManaging] = useState(false);\r\n\r\n    useEffect(() => {\r\n        async function fetchSubscription() {\r\n            if (!open || authLoading || !session) return;\r\n\r\n            try {\r\n                setIsLoading(true);\r\n                const data = await getSubscription();\r\n                setSubscriptionData(data);\r\n                setError(null);\r\n            } catch (err) {\r\n                console.error('Failed to get subscription:', err);\r\n                setError(err instanceof Error ? err.message : 'Failed to load subscription data');\r\n            } finally {\r\n                setIsLoading(false);\r\n            }\r\n        }\r\n\r\n        fetchSubscription();\r\n    }, [open, session, authLoading]);\r\n\r\n    const handleManageSubscription = async () => {\r\n        try {\r\n            setIsManaging(true);\r\n            const { url } = await createPortalSession({ return_url: returnUrl });\r\n            window.location.href = url;\r\n        } catch (err) {\r\n            console.error('Failed to create portal session:', err);\r\n            setError(err instanceof Error ? err.message : 'Failed to create portal session');\r\n        } finally {\r\n            setIsManaging(false);\r\n        }\r\n    };\r\n\r\n    // Local mode content\r\n    if (isLocalMode()) {\r\n        return (\r\n            <Dialog open={open} onOpenChange={onOpenChange}>\r\n                <DialogContent className=\"max-w-2xl max-h-[80vh] overflow-y-auto\">\r\n                    <DialogHeader>\r\n                        <DialogTitle>Billing & Subscription</DialogTitle>\r\n                    </DialogHeader>\r\n                    <div className=\"p-4 bg-muted/30 border border-border rounded-lg text-center\">\r\n                        <p className=\"text-sm text-muted-foreground\">\r\n                            Running in local development mode - billing features are disabled\r\n                        </p>\r\n                        <p className=\"text-xs text-muted-foreground mt-2\">\r\n                            All premium features are available in this environment\r\n                        </p>\r\n                    </div>\r\n                </DialogContent>\r\n            </Dialog>\r\n        );\r\n    }\r\n\r\n    return (\r\n        <Dialog open={open} onOpenChange={onOpenChange}>\r\n            <DialogContent className=\"max-w-5xl max-h-[80vh] overflow-y-auto\">\r\n                <DialogHeader>\r\n                    <DialogTitle>Upgrade Your Plan</DialogTitle>\r\n                </DialogHeader>\r\n\r\n                {isLoading || authLoading ? (\r\n                    <div className=\"space-y-4\">\r\n                        <Skeleton className=\"h-20 w-full\" />\r\n                        <Skeleton className=\"h-40 w-full\" />\r\n                        <Skeleton className=\"h-10 w-full\" />\r\n                    </div>\r\n                ) : error ? (\r\n                    <div className=\"p-4 bg-destructive/10 border border-destructive/20 rounded-lg text-center\">\r\n                        <p className=\"text-sm text-destructive\">Error loading billing status: {error}</p>\r\n                    </div>\r\n                ) : (\r\n                    <>\r\n                        {subscriptionData && (\r\n                            <div className=\"mb-6\">\r\n                                <div className=\"rounded-lg border bg-background p-4\">\r\n                                    <div className=\"flex justify-between items-center\">\r\n                                        <span className=\"text-sm font-medium text-foreground/90\">\r\n                                            Agent Usage This Month\r\n                                        </span>\r\n                                        <span className=\"text-sm font-medium\">\r\n                                            ${subscriptionData.current_usage?.toFixed(2) || '0'} /{' '}\r\n                                            ${subscriptionData.cost_limit || '0'}\r\n                                        </span>\r\n                                    </div>\r\n                                </div>\r\n                            </div>\r\n                        )}\r\n\r\n                        <PricingSection returnUrl={returnUrl} showTitleAndTabs={false} />\r\n\r\n                        {subscriptionData && (\r\n                            <Button\r\n                                onClick={handleManageSubscription}\r\n                                disabled={isManaging}\r\n                                className=\"max-w-xs mx-auto w-full bg-primary hover:bg-primary/90 shadow-md hover:shadow-lg transition-all mt-4\"\r\n                            >\r\n                                {isManaging ? 'Loading...' : 'Manage Subscription'}\r\n                            </Button>\r\n                        )}\r\n                    </>\r\n                )}\r\n            </DialogContent>\r\n        </Dialog>\r\n    );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAMA;AACA;AACA;AACA;AAKA;AACA;;;;;AAlBA;;;;;;;;;;AA2BO,SAAS,aAAa,EAAE,IAAI,EAAE,YAAY,EAAE,YAAY,QAAQ,UAAU,QAAQ,GAAG,EAAqB;IAC7G,MAAM,EAAE,OAAO,EAAE,WAAW,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAClD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IACpF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACN,eAAe;YACX,IAAI,CAAC,QAAQ,eAAe,CAAC,SAAS;YAEtC,IAAI;gBACA,aAAa;gBACb,MAAM,OAAO,MAAM,CAAA,GAAA,iHAAA,CAAA,kBAAe,AAAD;gBACjC,oBAAoB;gBACpB,SAAS;YACb,EAAE,OAAO,KAAK;gBACV,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAClD,SAAU;gBACN,aAAa;YACjB;QACJ;QAEA;IACJ,GAAG;QAAC;QAAM;QAAS;KAAY;IAE/B,MAAM,2BAA2B;QAC7B,IAAI;YACA,cAAc;YACd,MAAM,EAAE,GAAG,EAAE,GAAG,MAAM,CAAA,GAAA,iHAAA,CAAA,sBAAmB,AAAD,EAAE;gBAAE,YAAY;YAAU;YAClE,OAAO,QAAQ,CAAC,IAAI,GAAG;QAC3B,EAAE,OAAO,KAAK;YACV,QAAQ,KAAK,CAAC,oCAAoC;YAClD,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAClD,SAAU;YACN,cAAc;QAClB;IACJ;IAEA,qBAAqB;IACrB,IAAI,CAAA,GAAA,oHAAA,CAAA,cAAW,AAAD,KAAK;QACf,qBACI,8OAAC,kIAAA,CAAA,SAAM;YAAC,MAAM;YAAM,cAAc;sBAC9B,cAAA,8OAAC,kIAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACrB,8OAAC,kIAAA,CAAA,eAAY;kCACT,cAAA,8OAAC,kIAAA,CAAA,cAAW;sCAAC;;;;;;;;;;;kCAEjB,8OAAC;wBAAI,WAAU;;0CACX,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;0CAG7C,8OAAC;gCAAE,WAAU;0CAAqC;;;;;;;;;;;;;;;;;;;;;;;IAOtE;IAEA,qBACI,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAC9B,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACrB,8OAAC,kIAAA,CAAA,eAAY;8BACT,cAAA,8OAAC,kIAAA,CAAA,cAAW;kCAAC;;;;;;;;;;;gBAGhB,aAAa,4BACV,8OAAC;oBAAI,WAAU;;sCACX,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;2BAExB,sBACA,8OAAC;oBAAI,WAAU;8BACX,cAAA,8OAAC;wBAAE,WAAU;;4BAA2B;4BAA+B;;;;;;;;;;;yCAG3E;;wBACK,kCACG,8OAAC;4BAAI,WAAU;sCACX,cAAA,8OAAC;gCAAI,WAAU;0CACX,cAAA,8OAAC;oCAAI,WAAU;;sDACX,8OAAC;4CAAK,WAAU;sDAAyC;;;;;;sDAGzD,8OAAC;4CAAK,WAAU;;gDAAsB;gDAChC,iBAAiB,aAAa,EAAE,QAAQ,MAAM;gDAAI;gDAAG;gDAAI;gDACzD,iBAAiB,UAAU,IAAI;;;;;;;;;;;;;;;;;;;;;;;sCAOrD,8OAAC,4JAAA,CAAA,iBAAc;4BAAC,WAAW;4BAAW,kBAAkB;;;;;;wBAEvD,kCACG,8OAAC,kIAAA,CAAA,SAAM;4BACH,SAAS;4BACT,UAAU;4BACV,WAAU;sCAET,aAAa,eAAe;;;;;;;;;;;;;;;;;;;AAQ7D", "debugId": null}}, {"offset": {"line": 613, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/billing/payment-required-dialog.tsx"], "sourcesContent": ["import React from 'react';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport { Zap } from 'lucide-react';\r\nimport { useModal } from '@/hooks/use-modal-store';\r\nimport { PricingSection } from '../home/<USER>/pricing-section';\r\n\r\nconst returnUrl = process.env.NEXT_PUBLIC_URL as string;\r\n\r\nexport const PaymentRequiredDialog = () => {\r\n    const { isOpen, type, onClose } = useModal();\r\n    const isModalOpen = isOpen && type === 'paymentRequiredDialog';\r\n    \r\n    return (\r\n      <Dialog open={isModalOpen} onOpenChange={onClose}>\r\n        <DialogContent className=\"w-[95vw] max-w-[750px] max-h-[90vh] overflow-hidden flex flex-col p-0\">\r\n            <DialogHeader className=\"px-4 sm:px-6 pt-4 sm:pt-6 flex-shrink-0\">\r\n              <DialogTitle>\r\n                Upgrade Required\r\n              </DialogTitle>\r\n              <DialogDescription>\r\n                You've reached your plan's usage limit. Upgrade to continue enjoying our premium features.\r\n              </DialogDescription>\r\n            </DialogHeader>\r\n            \r\n            <div className=\"flex-1 pb-2 overflow-y-auto scrollbar-thin scrollbar-thumb-zinc-300 dark:scrollbar-thumb-zinc-700 scrollbar-track-transparent px-4 sm:px-6 min-h-0\">\r\n              <div className=\"space-y-4 sm:space-y-6 pb-4\">\r\n                <div className=\"flex items-start p-3 sm:p-4 bg-destructive/5 border border-destructive/50 rounded-lg\">\r\n                  <div className=\"flex items-start space-x-3\">\r\n                    <div className=\"flex-shrink-0 mt-0.5\">\r\n                      <Zap className=\"w-4 h-4 sm:w-5 sm:h-5 text-destructive\" />\r\n                    </div>\r\n                    <div className=\"text-xs sm:text-sm min-w-0\">\r\n                      <p className=\"font-medium text-destructive\">Usage Limit Reached</p>\r\n                      <p className=\"text-destructive break-words\">\r\n                        Your current plan has been exhausted for this billing period.\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n\r\n                <div className=\"w-full\">\r\n                  <PricingSection \r\n                    insideDialog={true} \r\n                    hideFree={true} \r\n                    returnUrl={`${returnUrl}/dashboard`} \r\n                    showTitleAndTabs={false} \r\n                  />\r\n                </div>\r\n              </div>\r\n            </div>\r\n        </DialogContent>\r\n      </Dialog>\r\n    );\r\n};"], "names": [], "mappings": ";;;;AACA;AAOA;AACA;AACA;;;;;;;;;;AAEA,MAAM;AAEC,MAAM,wBAAwB;IACjC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,WAAQ,AAAD;IACzC,MAAM,cAAc,UAAU,SAAS;IAEvC,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAa,cAAc;kBACvC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACrB,8OAAC,kIAAA,CAAA,eAAY;oBAAC,WAAU;;sCACtB,8OAAC,kIAAA,CAAA,cAAW;sCAAC;;;;;;sCAGb,8OAAC,kIAAA,CAAA,oBAAiB;sCAAC;;;;;;;;;;;;8BAKrB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DAA+B;;;;;;8DAC5C,8OAAC;oDAAE,WAAU;8DAA+B;;;;;;;;;;;;;;;;;;;;;;;0CAOlD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,iBAAc;oCACb,cAAc;oCACd,UAAU;oCACV,WAAW,GAAG,UAAU,UAAU,CAAC;oCACnC,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtC", "debugId": null}}, {"offset": {"line": 770, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/GithubSignIn.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\nimport { useTheme } from 'next-themes';\r\nimport { toast } from 'sonner';\r\nimport { Icons } from './home/<USER>';\r\n\r\ninterface GitHubSignInProps {\r\n  returnUrl?: string;\r\n}\r\n\r\ninterface AuthMessage {\r\n  type: 'github-auth-success' | 'github-auth-error';\r\n  message?: string;\r\n  returnUrl?: string;\r\n}\r\n\r\nexport default function GitHubSignIn({ returnUrl }: GitHubSignInProps) {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const { resolvedTheme } = useTheme();\r\n\r\n  // Cleanup function to handle auth state\r\n  const cleanupAuthState = useCallback(() => {\r\n    sessionStorage.removeItem('isGitHubAuthInProgress');\r\n    setIsLoading(false);\r\n  }, []);\r\n\r\n  // Handle success message\r\n  const handleSuccess = useCallback(\r\n    (data: AuthMessage) => {\r\n      cleanupAuthState();\r\n\r\n      // Add a small delay to ensure state is properly cleared\r\n      setTimeout(() => {\r\n        window.location.href = data.returnUrl || returnUrl || '/dashboard';\r\n      }, 100);\r\n    },\r\n    [cleanupAuthState, returnUrl],\r\n  );\r\n\r\n  // Handle error message\r\n  const handleError = useCallback(\r\n    (data: AuthMessage) => {\r\n      cleanupAuthState();\r\n      toast.error(data.message || 'GitHub sign-in failed. Please try again.');\r\n    },\r\n    [cleanupAuthState],\r\n  );\r\n\r\n  // Message event handler\r\n  useEffect(() => {\r\n    const handleMessage = (event: MessageEvent<AuthMessage>) => {\r\n      // Security: Only accept messages from same origin\r\n      if (event.origin !== window.location.origin) {\r\n        console.warn(\r\n          'Rejected message from unauthorized origin:',\r\n          event.origin,\r\n        );\r\n        return;\r\n      }\r\n\r\n      // Validate message structure\r\n      if (!event.data?.type || typeof event.data.type !== 'string') {\r\n        return;\r\n      }\r\n\r\n      switch (event.data.type) {\r\n        case 'github-auth-success':\r\n          handleSuccess(event.data);\r\n          break;\r\n        case 'github-auth-error':\r\n          handleError(event.data);\r\n          break;\r\n        default:\r\n          // Ignore unknown message types\r\n          break;\r\n      }\r\n    };\r\n\r\n    window.addEventListener('message', handleMessage);\r\n\r\n    return () => {\r\n      window.removeEventListener('message', handleMessage);\r\n    };\r\n  }, [handleSuccess, handleError]);\r\n\r\n  // Cleanup on component unmount\r\n  useEffect(() => {\r\n    return () => {\r\n      cleanupAuthState();\r\n    };\r\n  }, [cleanupAuthState]);\r\n\r\n  const handleGitHubSignIn = async () => {\r\n    if (isLoading) return;\r\n\r\n    let popupInterval: NodeJS.Timeout | null = null;\r\n\r\n    try {\r\n      setIsLoading(true);\r\n\r\n      // Store return URL for the popup\r\n      if (returnUrl) {\r\n        sessionStorage.setItem('github-returnUrl', returnUrl || '/dashboard');\r\n      }\r\n\r\n      // Open popup with proper dimensions and features\r\n      const popup = window.open(\r\n        `${window.location.origin}/auth/github-popup`,\r\n        'GitHubOAuth',\r\n        'width=500,height=600,scrollbars=yes,resizable=yes,status=yes,location=yes',\r\n      );\r\n\r\n      if (!popup) {\r\n        throw new Error(\r\n          'Popup was blocked. Please enable popups and try again.',\r\n        );\r\n      }\r\n\r\n      // Set loading state and track popup\r\n      sessionStorage.setItem('isGitHubAuthInProgress', '1');\r\n\r\n      // Monitor popup closure\r\n      popupInterval = setInterval(() => {\r\n        if (popup.closed) {\r\n          if (popupInterval) {\r\n            clearInterval(popupInterval);\r\n            popupInterval = null;\r\n          }\r\n\r\n          // Small delay to allow postMessage to complete\r\n          setTimeout(() => {\r\n            if (sessionStorage.getItem('isGitHubAuthInProgress')) {\r\n              cleanupAuthState();\r\n              toast.error('GitHub sign-in was cancelled or not completed.');\r\n            }\r\n          }, 500);\r\n        }\r\n      }, 1000);\r\n    } catch (error) {\r\n      console.error('GitHub sign-in error:', error);\r\n      if (popupInterval) {\r\n        clearInterval(popupInterval);\r\n      }\r\n      cleanupAuthState();\r\n      toast.error(\r\n        error instanceof Error\r\n          ? error.message\r\n          : 'Failed to start GitHub sign-in',\r\n      );\r\n    }\r\n  };\r\n\r\n  return (\r\n    // Matched the button with the GoogleSignIn component\r\n    <button\r\n      onClick={handleGitHubSignIn}\r\n      disabled={isLoading}\r\n      className=\"relative w-full h-12 flex items-center justify-center text-sm font-normal tracking-wide rounded-full bg-background text-foreground border border-border hover:bg-accent/30 transition-all duration-200 disabled:opacity-60 disabled:cursor-not-allowed font-sans\"\r\n      aria-label={\r\n        isLoading ? 'Signing in with GitHub...' : 'Sign in with GitHub'\r\n      }\r\n      type=\"button\"\r\n    >\r\n      <div className=\"absolute left-0 inset-y-0 flex items-center pl-1 w-10\">\r\n        <div className=\"w-8 h-8 rounded-full flex items-center justify-center text-foreground dark:bg-foreground dark:text-background\">\r\n          {isLoading ? (\r\n            <div className=\"w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin\" />\r\n          ) : (\r\n            <Icons.github className=\"w-5 h-5\" />\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      <span className=\"ml-9 font-light\">\r\n        {isLoading ? 'Signing in...' : 'Continue with GitHub'}\r\n      </span>\r\n    </button>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAiBe,SAAS,aAAa,EAAE,SAAS,EAAqB;IACnE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEjC,wCAAwC;IACxC,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACnC,eAAe,UAAU,CAAC;QAC1B,aAAa;IACf,GAAG,EAAE;IAEL,yBAAyB;IACzB,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC9B,CAAC;QACC;QAEA,wDAAwD;QACxD,WAAW;YACT,OAAO,QAAQ,CAAC,IAAI,GAAG,KAAK,SAAS,IAAI,aAAa;QACxD,GAAG;IACL,GACA;QAAC;QAAkB;KAAU;IAG/B,uBAAuB;IACvB,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC5B,CAAC;QACC;QACA,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,KAAK,OAAO,IAAI;IAC9B,GACA;QAAC;KAAiB;IAGpB,wBAAwB;IACxB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,kDAAkD;YAClD,IAAI,MAAM,MAAM,KAAK,OAAO,QAAQ,CAAC,MAAM,EAAE;gBAC3C,QAAQ,IAAI,CACV,8CACA,MAAM,MAAM;gBAEd;YACF;YAEA,6BAA6B;YAC7B,IAAI,CAAC,MAAM,IAAI,EAAE,QAAQ,OAAO,MAAM,IAAI,CAAC,IAAI,KAAK,UAAU;gBAC5D;YACF;YAEA,OAAQ,MAAM,IAAI,CAAC,IAAI;gBACrB,KAAK;oBACH,cAAc,MAAM,IAAI;oBACxB;gBACF,KAAK;oBACH,YAAY,MAAM,IAAI;oBACtB;gBACF;oBAEE;YACJ;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QAEnC,OAAO;YACL,OAAO,mBAAmB,CAAC,WAAW;QACxC;IACF,GAAG;QAAC;QAAe;KAAY;IAE/B,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL;QACF;IACF,GAAG;QAAC;KAAiB;IAErB,MAAM,qBAAqB;QACzB,IAAI,WAAW;QAEf,IAAI,gBAAuC;QAE3C,IAAI;YACF,aAAa;YAEb,iCAAiC;YACjC,IAAI,WAAW;gBACb,eAAe,OAAO,CAAC,oBAAoB,aAAa;YAC1D;YAEA,iDAAiD;YACjD,MAAM,QAAQ,OAAO,IAAI,CACvB,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,kBAAkB,CAAC,EAC7C,eACA;YAGF,IAAI,CAAC,OAAO;gBACV,MAAM,IAAI,MACR;YAEJ;YAEA,oCAAoC;YACpC,eAAe,OAAO,CAAC,0BAA0B;YAEjD,wBAAwB;YACxB,gBAAgB,YAAY;gBAC1B,IAAI,MAAM,MAAM,EAAE;oBAChB,IAAI,eAAe;wBACjB,cAAc;wBACd,gBAAgB;oBAClB;oBAEA,+CAA+C;oBAC/C,WAAW;wBACT,IAAI,eAAe,OAAO,CAAC,2BAA2B;4BACpD;4BACA,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACd;oBACF,GAAG;gBACL;YACF,GAAG;QACL,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,IAAI,eAAe;gBACjB,cAAc;YAChB;YACA;YACA,wIAAA,CAAA,QAAK,CAAC,KAAK,CACT,iBAAiB,QACb,MAAM,OAAO,GACb;QAER;IACF;IAEA,OACE,qDAAqD;kBACrD,8OAAC;QACC,SAAS;QACT,UAAU;QACV,WAAU;QACV,cACE,YAAY,8BAA8B;QAE5C,MAAK;;0BAEL,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,0BACC,8OAAC;wBAAI,WAAU;;;;;6CAEf,8OAAC,mIAAA,CAAA,QAAK,CAAC,MAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAK9B,8OAAC;gBAAK,WAAU;0BACb,YAAY,kBAAkB;;;;;;;;;;;;AAIvC", "debugId": null}}, {"offset": {"line": 946, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/payment/paywall-dialog.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport { Rocket } from 'lucide-react';\r\nimport React, { useCallback, useEffect } from 'react';\r\n\r\nexport interface PaywallDialogProps {\r\n  cancelText?: string;\r\n  children?: React.ReactNode;\r\n  ctaText?: string;\r\n  open: boolean;\r\n  description?: string;\r\n  onDialogClose?: () => void;\r\n  onUpgradeClick?: () => void;\r\n  upgradeUrl?: string;\r\n  title?: string;\r\n}\r\n\r\nexport const PaywallDialog: React.FC<PaywallDialogProps> = ({\r\n  cancelText = 'Maybe Later',\r\n  children,\r\n  open = false,\r\n  ctaText = 'Upgrade Now',\r\n  description = 'This feature requires an upgrade to access.',\r\n  onDialogClose,\r\n  onUpgradeClick,\r\n  upgradeUrl = '/settings/billing',\r\n  title = 'Upgrade Required',\r\n}) => {\r\n  const handleUpgrade = useCallback(() => {\r\n    if (onUpgradeClick) {\r\n      onUpgradeClick();\r\n    } else {\r\n      window.location.href = upgradeUrl;\r\n    }\r\n  }, [onUpgradeClick, upgradeUrl]);\r\n\r\n  const handleClose = useCallback(() => {\r\n    if (onDialogClose) {\r\n      onDialogClose();\r\n    }\r\n  }, [onDialogClose]);\r\n\r\n  useEffect(() => {\r\n    return () => {\r\n      document.body.classList.remove('overflow-hidden');\r\n      document.body.style.pointerEvents = 'auto';\r\n      \r\n      const strayBackdrops = document.querySelectorAll('[data-backdrop]');\r\n      strayBackdrops.forEach(element => element.remove());\r\n    };\r\n  }, []);\r\n  \r\n  useEffect(() => {\r\n    if (!open) {\r\n      document.body.classList.remove('overflow-hidden');\r\n      document.body.style.pointerEvents = 'auto';\r\n\r\n      const overlays = document.querySelectorAll('[role=\"dialog\"]');\r\n      overlays.forEach(overlay => {\r\n        if (!overlay.closest('[open=\"true\"]')) {\r\n          overlay.remove();\r\n        }\r\n      });\r\n    }\r\n  }, [open]);\r\n\r\n  return (\r\n    <>\r\n      {children}\r\n\r\n      <Dialog \r\n        open={open} \r\n        onOpenChange={(isOpen) => {\r\n          if (!isOpen) {\r\n            document.body.style.pointerEvents = 'auto';\r\n            document.body.classList.remove('overflow-hidden');\r\n            \r\n            setTimeout(() => {\r\n              if (onDialogClose) {\r\n                onDialogClose();\r\n              }\r\n            }, 0);\r\n          }\r\n        }}\r\n      >\r\n        <DialogContent \r\n          className=\"sm:max-w-md\"\r\n          onEscapeKeyDown={() => {\r\n            handleClose();\r\n            document.body.style.pointerEvents = 'auto';\r\n          }}\r\n        >\r\n          <DialogHeader>\r\n            <DialogTitle>{title}</DialogTitle>\r\n            <DialogDescription>{description}</DialogDescription>\r\n          </DialogHeader>\r\n\r\n          <div className=\"py-6\">\r\n            <div className=\"flex flex-col items-center text-center space-y-4\">\r\n              <div className=\"rounded-full bg-primary/10 p-3\">\r\n                <Rocket className=\"h-6 w-6 text-primary\" />\r\n              </div>\r\n\r\n              <div className=\"space-y-2\">\r\n                <h3 className=\"text-lg font-medium\">Upgrade to unlock</h3>\r\n                <p className=\"text-muted-foreground\">\r\n                  Get access to premium models and features\r\n                </p>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          <DialogFooter className=\"flex items-center gap-2\">\r\n            <Button \r\n              onClick={() => {\r\n                document.body.style.pointerEvents = 'auto';\r\n                document.body.classList.remove('overflow-hidden');\r\n                handleClose();\r\n              }} \r\n              variant=\"outline\"\r\n            >\r\n              {cancelText}\r\n            </Button>\r\n            <Button \r\n              onClick={() => {\r\n                document.body.style.pointerEvents = 'auto';\r\n                document.body.classList.remove('overflow-hidden');\r\n                handleUpgrade();\r\n              }} \r\n              variant=\"default\"\r\n            >\r\n              <Rocket className=\"h-4 w-4\" />\r\n              {ctaText}\r\n            </Button>\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAEA;AACA;AAQA;AACA;AAZA;;;;;;AA0BO,MAAM,gBAA8C,CAAC,EAC1D,aAAa,aAAa,EAC1B,QAAQ,EACR,OAAO,KAAK,EACZ,UAAU,aAAa,EACvB,cAAc,6CAA6C,EAC3D,aAAa,EACb,cAAc,EACd,aAAa,mBAAmB,EAChC,QAAQ,kBAAkB,EAC3B;IACC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,IAAI,gBAAgB;YAClB;QACF,OAAO;YACL,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF,GAAG;QAAC;QAAgB;KAAW;IAE/B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC9B,IAAI,eAAe;YACjB;QACF;IACF,GAAG;QAAC;KAAc;IAElB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;YAC/B,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG;YAEpC,MAAM,iBAAiB,SAAS,gBAAgB,CAAC;YACjD,eAAe,OAAO,CAAC,CAAA,UAAW,QAAQ,MAAM;QAClD;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,MAAM;YACT,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;YAC/B,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG;YAEpC,MAAM,WAAW,SAAS,gBAAgB,CAAC;YAC3C,SAAS,OAAO,CAAC,CAAA;gBACf,IAAI,CAAC,QAAQ,OAAO,CAAC,kBAAkB;oBACrC,QAAQ,MAAM;gBAChB;YACF;QACF;IACF,GAAG;QAAC;KAAK;IAET,qBACE;;YACG;0BAED,8OAAC,kIAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc,CAAC;oBACb,IAAI,CAAC,QAAQ;wBACX,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG;wBACpC,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;wBAE/B,WAAW;4BACT,IAAI,eAAe;gCACjB;4BACF;wBACF,GAAG;oBACL;gBACF;0BAEA,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBACZ,WAAU;oBACV,iBAAiB;wBACf;wBACA,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG;oBACtC;;sCAEA,8OAAC,kIAAA,CAAA,eAAY;;8CACX,8OAAC,kIAAA,CAAA,cAAW;8CAAE;;;;;;8CACd,8OAAC,kIAAA,CAAA,oBAAiB;8CAAE;;;;;;;;;;;;sCAGtB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAGpB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsB;;;;;;0DACpC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAO3C,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;wCACP,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG;wCACpC,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;wCAC/B;oCACF;oCACA,SAAQ;8CAEP;;;;;;8CAEH,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;wCACP,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG;wCACpC,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;wCAC/B;oCACF;oCACA,SAAQ;;sDAER,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCACjB;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}]}