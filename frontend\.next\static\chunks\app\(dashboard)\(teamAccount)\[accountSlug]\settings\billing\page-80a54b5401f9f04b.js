(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5809],{34477:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{callServer:function(){return a.callServer},createServerReference:function(){return c},findSourceMapURL:function(){return s.findSourceMapURL}});let a=r(53806),s=r(31818),c=r(34979).createServerReference},55365:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>l,TN:()=>d,XL:()=>i});var a=r(95155);r(12115);var s=r(74466),c=r(59434);let n=(0,s.F)("relative w-full rounded-xl border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,c.cn)(n({variant:r}),t),...s})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-title",className:(0,c.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,c.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r})}},72305:(e,t,r)=>{"use strict";r.d(t,{U:()=>s});var a=r(34477);let s=(0,a.createServerReference)("7f8bed79c8654f95685745e61906af37f96a086236",a.callServer,void 0,a.findSourceMapURL,"createClient")},75638:(e,t,r)=>{Promise.resolve().then(r.bind(r,77975))},77975:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var a=r(95155),s=r(12115),c=r(72305),n=r(53386),l=r(55365);function i(e){let{params:t}=e,{accountSlug:r}=s.use(t),[i,d]=s.useState(null),[o,u]=s.useState(null);return(s.useEffect(()=>{!async function(){try{let e=await (0,c.U)(),{data:t}=await e.rpc("get_account_by_slug",{slug:r});d(t)}catch(e){u("Failed to load account data"),console.error(e)}}()},[r]),o)?(0,a.jsxs)(l.Fc,{variant:"destructive",className:"border-red-300 dark:border-red-800 rounded-xl",children:[(0,a.jsx)(l.XL,{children:"Error"}),(0,a.jsx)(l.TN,{children:o})]}):i?"owner"!==i.account_role?(0,a.jsxs)(l.Fc,{variant:"destructive",className:"border-red-300 dark:border-red-800 rounded-xl",children:[(0,a.jsx)(l.XL,{children:"Access Denied"}),(0,a.jsx)(l.TN,{children:"You do not have permission to access this page."})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-card-title",children:"Team Billing"}),(0,a.jsx)("p",{className:"text-sm text-foreground/70",children:"Manage your team's subscription and billing details."})]}),(0,a.jsx)(n.default,{accountId:i.account_id,returnUrl:"".concat("http://localhost:3000","/").concat(r,"/settings/billing")})]}):(0,a.jsx)("div",{children:"Loading..."})}}},e=>{var t=t=>e(e.s=t);e.O(0,[5105,2969,1935,6671,3860,6874,1171,7453,9879,5653,6915,6687,6686,937,3685,8222,7379,3386,8441,1684,7358],()=>t(75638)),_N_E=e.O()}]);