(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4089],{1243:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("Triangle<PERSON>lert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},6101:(e,r,t)=>{"use strict";t.d(r,{s:()=>l,t:()=>a});var n=t(12115);function s(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function a(...e){return r=>{let t=!1,n=e.map(e=>{let n=s(e,r);return t||"function"!=typeof n||(t=!0),n});if(t)return()=>{for(let r=0;r<n.length;r++){let t=n[r];"function"==typeof t?t():s(e[r],null)}}}}function l(...e){return n.useCallback(a(...e),e)}},19946:(e,r,t)=>{"use strict";t.d(r,{A:()=>d});var n=t(12115);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),a=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,n.forwardRef)((e,r)=>{let{color:t="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:d,className:o="",children:c,iconNode:u,...f}=e;return(0,n.createElement)("svg",{ref:r,...l,width:s,height:s,stroke:t,strokeWidth:d?24*Number(i)/Number(s):i,className:a("lucide",o),...f},[...u.map(e=>{let[r,t]=e;return(0,n.createElement)(r,t)}),...Array.isArray(c)?c:[c]])}),d=(e,r)=>{let t=(0,n.forwardRef)((t,l)=>{let{className:d,...o}=t;return(0,n.createElement)(i,{ref:l,iconNode:r,className:a("lucide-".concat(s(e)),d),...o})});return t.displayName="".concat(e),t}},30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>d,r:()=>i});var n=t(95155);t(12115);var s=t(99708),a=t(74466),l=t(59434);let i=(0,a.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",node_outline:"bg-transparent border border-primary/10",node_secondary:"px-0 bg-transparent hover:opacity-60",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-lg gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-lg px-6 has-[>svg]:px-4",icon:"size-9",node_secondary:"px-0"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:r,variant:t,size:a,asChild:d=!1,...o}=e,c=d?s.DX:"button";return(0,n.jsx)(c,{"data-slot":"button",className:(0,l.cn)(i({variant:t,size:a,className:r})),...o})}},34477:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{callServer:function(){return n.callServer},createServerReference:function(){return a},findSourceMapURL:function(){return s.findSourceMapURL}});let n=t(53806),s=t(31818),a=t(34979).createServerReference},35169:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,r,t)=>{"use strict";var n=t(18999);t.o(n,"redirect")&&t.d(r,{redirect:function(){return n.redirect}}),t.o(n,"useParams")&&t.d(r,{useParams:function(){return n.useParams}}),t.o(n,"usePathname")&&t.d(r,{usePathname:function(){return n.usePathname}}),t.o(n,"useRouter")&&t.d(r,{useRouter:function(){return n.useRouter}}),t.o(n,"useSearchParams")&&t.d(r,{useSearchParams:function(){return n.useSearchParams}})},35706:(e,r,t)=>{"use strict";t.d(r,{SubmitButton:()=>c});var n=t(95155),s=t(47650),a=t(12115),l=t(30285),i=t(55365),d=t(1243);let o={message:""};function c(e){let{children:r,formAction:t,errorMessage:c,pendingText:u="Submitting...",...f}=e,{pending:m,action:x}=(0,s.useFormStatus)(),[p,g]=(0,a.useActionState)(t,o),h=m&&x===g;return(0,n.jsxs)("div",{className:"flex flex-col gap-y-4 w-full",children:[!!(c||(null==p?void 0:p.message))&&(0,n.jsxs)(i.Fc,{variant:"destructive",className:"w-full",children:[(0,n.jsx)(d.A,{className:"h-4 w-4"}),(0,n.jsx)(i.TN,{children:c||(null==p?void 0:p.message)})]}),(0,n.jsx)("div",{children:(0,n.jsx)(l.$,{...f,type:"submit","aria-disabled":m,formAction:g,children:h?u:r})})]})}},40646:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},43141:(e,r,t)=>{Promise.resolve().then(t.bind(t,92518))},55365:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>i,TN:()=>o,XL:()=>d});var n=t(95155);t(12115);var s=t(74466),a=t(59434);let l=(0,s.F)("relative w-full rounded-xl border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function i(e){let{className:r,variant:t,...s}=e;return(0,n.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,a.cn)(l({variant:t}),r),...s})}function d(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"alert-title",className:(0,a.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",r),...t})}function o(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"alert-description",className:(0,a.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",r),...t})}},59434:(e,r,t)=>{"use strict";t.d(r,{$3:()=>d,Hz:()=>i,W5:()=>o,cn:()=>l});var n=t(52596),s=t(81949),a=t(39688);function l(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,n.$)(r))}let i=function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"rgba(180, 180, 180)";if(!e)return r;try{if("string"==typeof e&&e.startsWith("var(")){let r=document.createElement("div");r.style.color=e,document.body.appendChild(r);let t=window.getComputedStyle(r).color;return document.body.removeChild(r),s.formatRGBA(s.parse(t))}return s.formatRGBA(s.parse(e))}catch(e){return console.error("Color parsing failed:",e),r}},d=(e,r)=>e.startsWith("rgb")?s.formatRGBA(s.alpha(s.parse(e),r)):e;function o(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return e.length<=r?e:e.slice(0,r)+"..."}},62523:(e,r,t)=>{"use strict";t.d(r,{p:()=>a});var n=t(95155);t(12115);var s=t(59434);function a(e){let{className:r,type:t,...a}=e;return(0,n.jsx)("input",{type:t,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...a})}},74466:(e,r,t)=>{"use strict";t.d(r,{F:()=>l});var n=t(52596);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.$,l=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return a(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:l,defaultVariants:i}=r,d=Object.keys(l).map(e=>{let r=null==t?void 0:t[e],n=null==i?void 0:i[e];if(null===r)return null;let a=s(r)||s(n);return l[e][a]}),o=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return a(e,d,null==r||null==(n=r.compoundVariants)?void 0:n.reduce((e,r)=>{let{class:t,className:n,...s}=r;return Object.entries(s).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...i,...o}[r]):({...i,...o})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},85339:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},92518:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var n=t(95155),s=t(6874),a=t.n(s),l=t(35695),i=t(12115),d=t(40646),o=t(35169),c=t(85339),u=t(62523),f=t(35706),m=t(34477);let x=(0,m.createServerReference)("60fcf04f7f92ed069c46b1be2805385ba3ee83149d",m.callServer,void 0,m.findSourceMapURL,"resetPassword");function p(){(0,l.useRouter)();let e=(0,l.useSearchParams)().get("code"),[r,t]=(0,i.useState)(!1),[s,m]=(0,i.useState)(null);(0,i.useEffect)(()=>{e||m("Invalid or missing reset code. Please request a new password reset link.")},[e]);let p=async(r,n)=>{if(!e)return{message:"Invalid reset code"};let s=await x(r,n);return s&&"object"==typeof s&&"success"in s&&s.success&&t(!0),s};return r?(0,n.jsx)("main",{className:"flex flex-col items-center justify-center min-h-screen w-full",children:(0,n.jsx)("div",{className:"w-full divide-y divide-border",children:(0,n.jsx)("section",{className:"w-full relative overflow-hidden",children:(0,n.jsxs)("div",{className:"relative flex flex-col items-center w-full px-6",children:[(0,n.jsx)("div",{className:"absolute inset-x-1/4 top-0 h-[600px] md:h-[800px] -z-20 bg-background rounded-b-xl"}),(0,n.jsx)("div",{className:"relative z-10 pt-24 pb-8 max-w-xl mx-auto h-full w-full flex flex-col gap-2 items-center justify-center",children:(0,n.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,n.jsx)("div",{className:"bg-green-50 dark:bg-green-950/20 rounded-full p-4 mb-6",children:(0,n.jsx)(d.A,{className:"h-12 w-12 text-green-500 dark:text-green-400"})}),(0,n.jsx)("h1",{className:"text-3xl md:text-4xl lg:text-5xl font-medium tracking-tighter text-center text-balance text-primary mb-4",children:"Password Reset Complete"}),(0,n.jsx)("p",{className:"text-base md:text-lg text-center text-muted-foreground font-medium text-balance leading-relaxed tracking-tight max-w-md mb-6",children:"Your password has been successfully updated. You can now sign in with your new password."}),(0,n.jsx)("div",{className:"flex flex-col sm:flex-row gap-4 w-full max-w-sm",children:(0,n.jsx)(a(),{href:"/auth",className:"flex h-12 items-center justify-center w-full text-center rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all shadow-md",children:"Go to sign in"})})]})})]})})})}):(0,n.jsx)("main",{className:"flex flex-col items-center justify-center min-h-screen w-full",children:(0,n.jsx)("div",{className:"w-full divide-y divide-border",children:(0,n.jsxs)("section",{className:"w-full relative overflow-hidden",children:[(0,n.jsxs)("div",{className:"relative flex flex-col items-center w-full px-6",children:[(0,n.jsx)("div",{className:"absolute inset-x-1/4 top-0 h-[600px] md:h-[800px] -z-20 bg-background rounded-b-xl"}),(0,n.jsxs)("div",{className:"relative z-10 pt-24 pb-8 max-w-md mx-auto h-full w-full flex flex-col gap-2 items-center justify-center",children:[(0,n.jsxs)(a(),{href:"/auth",className:"group border border-border/50 bg-background hover:bg-accent/20 rounded-full text-sm h-8 px-3 flex items-center gap-2 transition-all duration-200 shadow-sm mb-6",children:[(0,n.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"}),(0,n.jsx)("span",{className:"font-medium text-muted-foreground text-xs tracking-wide",children:"Back to sign in"})]}),(0,n.jsx)("h1",{className:"text-3xl md:text-4xl lg:text-5xl font-medium tracking-tighter text-center text-balance text-primary",children:"Reset Password"}),(0,n.jsx)("p",{className:"text-base md:text-lg text-center text-muted-foreground font-medium text-balance leading-relaxed tracking-tight mt-2 mb-6",children:"Create a new password for your account"})]})]}),(0,n.jsx)("div",{className:"relative z-10 flex justify-center px-6 pb-24",children:(0,n.jsxs)("div",{className:"w-full max-w-md rounded-xl bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] border border-border p-8",children:[s&&(0,n.jsxs)("div",{className:"mb-6 p-4 rounded-lg flex items-center gap-3 bg-secondary/10 border border-secondary/20 text-secondary",children:[(0,n.jsx)(c.A,{className:"h-5 w-5 flex-shrink-0 text-secondary"}),(0,n.jsx)("span",{className:"text-sm font-medium",children:s})]}),!s&&(0,n.jsxs)("form",{className:"space-y-4",children:[(0,n.jsx)("div",{children:(0,n.jsx)(u.p,{id:"password",name:"password",type:"password",placeholder:"New password",className:"h-12 rounded-full bg-background border-border",required:!0})}),(0,n.jsx)("div",{children:(0,n.jsx)(u.p,{id:"confirmPassword",name:"confirmPassword",type:"password",placeholder:"Confirm new password",className:"h-12 rounded-full bg-background border-border",required:!0})}),(0,n.jsx)("div",{className:"space-y-4 pt-4",children:(0,n.jsx)(f.SubmitButton,{formAction:p,className:"w-full h-12 rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all shadow-md",pendingText:"Updating password...",children:"Reset Password"})})]}),s&&(0,n.jsx)("div",{className:"mt-6 flex justify-center",children:(0,n.jsx)(a(),{href:"/auth",className:"flex h-12 px-6 items-center justify-center text-center rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all shadow-md",children:"Return to sign in"})})]})})]})})})}function g(){return(0,n.jsx)(i.Suspense,{fallback:(0,n.jsx)("main",{className:"flex flex-col items-center justify-center min-h-screen w-full",children:(0,n.jsx)("div",{className:"w-12 h-12 rounded-full border-4 border-primary border-t-transparent animate-spin"})}),children:(0,n.jsx)(p,{})})}},99708:(e,r,t)=>{"use strict";t.d(r,{DX:()=>i,Dc:()=>o,TL:()=>l});var n=t(12115),s=t(6101),a=t(95155);function l(e){let r=function(e){let r=n.forwardRef((e,r)=>{let{children:t,...a}=e;if(n.isValidElement(t)){var l;let e,i,d=(l=t,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),o=function(e,r){let t={...r};for(let n in r){let s=e[n],a=r[n];/^on[A-Z]/.test(n)?s&&a?t[n]=(...e)=>{let r=a(...e);return s(...e),r}:s&&(t[n]=s):"style"===n?t[n]={...s,...a}:"className"===n&&(t[n]=[s,a].filter(Boolean).join(" "))}return{...e,...t}}(a,t.props);return t.type!==n.Fragment&&(o.ref=r?(0,s.t)(r,d):d),n.cloneElement(t,o)}return n.Children.count(t)>1?n.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=n.forwardRef((e,t)=>{let{children:s,...l}=e,i=n.Children.toArray(s),d=i.find(c);if(d){let e=d.props.children,s=i.map(r=>r!==d?r:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(r,{...l,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,s):null})}return(0,a.jsx)(r,{...l,ref:t,children:s})});return t.displayName=`${e}.Slot`,t}var i=l("Slot"),d=Symbol("radix.slottable");function o(e){let r=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});return r.displayName=`${e}.Slottable`,r.__radixId=d,r}function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===d}}},e=>{var r=r=>e(e.s=r);e.O(0,[2969,6874,8441,1684,7358],()=>r(43141)),_N_E=e.O()}]);