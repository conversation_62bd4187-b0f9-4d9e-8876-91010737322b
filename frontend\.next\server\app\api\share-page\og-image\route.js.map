{"version": 3, "file": "app/api/share-page/og-image/route.js", "mappings": "gJEAA,0RFGO,IAAMA,EAAU,OACVC,CADkB,CACL,KAAK,CAAC,cAEVC,EAAIC,CAAO,EAFkB,GAG3C,aAJiE,CAI/DC,CAAY,CAAE,CAAG,IAAIC,IAAIF,EAAQG,GAAG,EACtCC,EAAQH,EAAaI,GAAG,CAAC,SAG/B,GAAI,CAACD,EACH,KADU,EACH,IAAIE,EAAAA,EAAYA,CAAC,0BAA2B,CAAEC,OAAQ,GAAI,GAGnE,GAAI,CACF,IAAMC,EAAW,MAAMC,MAAM,CAAC,uCAAuC,CAAC,CAAE,CACtEC,OAAQ,OACRC,QAAS,CACP,eAAgB,mBAChBC,cAAe,CAAC,OAAO,EAAEC,QAAQC,GAAG,CAACC,cAAc,EAAE,EAEvDC,KAAMC,KAAKC,SAAS,CAAC,CACnBC,WAAY,GACZC,cAAe,OACbhB,CACF,EACAI,SAAU,CACRa,KAAM,QACR,CACF,EACF,GAEA,GAAI,CAACb,EAASc,EAAE,CACd,CADgB,KACNC,MAAM,CAAC,kBAAkB,EAAEf,EAASD,MAAM,EAAE,EAGxD,IAAMiB,EAAO,MAAMhB,EAASgB,IAAI,GAC1BC,EAAc,MAAMD,EAAKC,WAAW,GACpCC,EAAQC,EAAOC,IAADD,CAAMF,GAE1B,OAAO,IAAInB,EAAAA,EAAYA,CAACoB,EAAO,CAC7BnB,OAAQ,IACRI,QAAS,CACP,eAAgB,YAChB,gBAAiB,oDACnB,CACF,EACF,CAAE,MAAOkB,EAAO,CAEd,OADAC,QAAQD,KAAK,CAAC,6BAA8BA,GACrC,IAAIvB,EAAAA,EAAYA,CAAC,yBAA0B,CAAEC,OAAQ,GAAI,EAClE,CACF,CC7CA,UAAwB,qBAAmB,EAC3C,YACA,KAAc,GAAS,WACvB,sCACA,oCACA,iBACA,8CACA,CAAK,CACL,oGACA,iBAVA,GAWA,QAAY,EACZ,CAAC,EAID,kBAAQ,wCAAsD,EAC9D,aACA,MAAW,QAAW,EACtB,mBACA,sBACA,CAAK,CACL,CCpBA,+EACA,EAFA,4BAEA,2BACA,OACI,QAA8B,EAClC,sCACA,0BACA,wBACA,gBAAyB,OAAqB,EAC9C,uBACA,CAAS,CACT,CAAK,EAEE,MAAqB,EAC5B,EAAe,GAAsB,MAAM,EAAkB,CAC7D,QAD6D,GAf7D,CAAoB,MAAQ,OAcM,EAdN,aAA0B,sBAA2B,aAAe,kDAAyD,uOAAsQ,2RAAuT,kBAAkB,QAAQ,uEAAgF,gBAAkB,uBAAyB,kBAAoB,uCAA6C,MAAQ,iBAAmB,2BAA+B,gHAAkI,uBAAyB,8FAA0G,aAAiB,WAAa,sEAA6E,uBAAuB,iCAAmC,QAAQ,EAAE,SAAW,oBAAsB,QAAQ,GAAG,wEAA4E,6BAAoC,SAAW,2CAAiD,UAAY,+BAAqC,UAAY,oCAA0C,QAAU,uCAA6C,OAAS,yCAA+C,QAAU,0CAAiD,MAAQ,+CAAsD,iBAAmB,o/BAAqsC,qBAAyB,k/CAA2gD,waAAqb,yCAiBlnK,CAAC,CAAC,EAAC,sBCvBH,uDCAA", "sources": ["webpack://_N_E/./src/app/api/share-page/og-image/route.tsx", "webpack://_N_E/./src/app/api/share-page/og-image/route.tsx?0b13", "webpack://_N_E/?378c", "webpack://_N_E/external commonjs \"node:buffer\"", "webpack://_N_E/external commonjs \"node:async_hooks\"", "webpack://_N_E/", "webpack://_N_E/?41d4"], "sourcesContent": ["import { NextResponse } from 'next/server';\r\n\r\n// Add route segment config for caching\r\nexport const runtime = 'edge'; // Use edge runtime for better performance\r\nexport const revalidate = 3600; // Cache for 1 hour\r\n\r\nexport async function GET(request) {\r\n  const { searchParams } = new URL(request.url);\r\n  const title = searchParams.get('title');\r\n\r\n  // Add error handling\r\n  if (!title) {\r\n    return new NextResponse('Missing title parameter', { status: 400 });\r\n  }\r\n\r\n  try {\r\n    const response = await fetch(`https://api.orshot.com/v1/studio/render`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        Authorization: `Bearer ${process.env.ORSHOT_API_KEY}`,\r\n      },\r\n      body: JSON.stringify({\r\n        templateId: 10,\r\n        modifications: {\r\n          title,\r\n        },\r\n        response: {\r\n          type: 'binary',\r\n        },\r\n      }),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Orshot API error: ${response.status}`);\r\n    }\r\n\r\n    const blob = await response.blob();\r\n    const arrayBuffer = await blob.arrayBuffer();\r\n    const image = Buffer.from(arrayBuffer);\r\n\r\n    return new NextResponse(image, {\r\n      status: 200,\r\n      headers: {\r\n        'Content-Type': 'image/png',\r\n        'Cache-Control': 'public, max-age=3600, stale-while-revalidate=86400',\r\n      },\r\n    });\r\n  } catch (error) {\r\n    console.error('OG Image generation error:', error);\r\n    return new NextResponse('Error generating image', { status: 500 });\r\n  }\r\n}\r\n", "import { AppRouteRouteModule } from \"next/dist/server/route-modules/app-route/module.compiled\";\nimport { RouteKind } from \"next/dist/server/route-kind\";\nimport { patchFetch as _patchFetch } from \"next/dist/server/lib/patch-fetch\";\nimport * as userland from \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\api\\\\share-page\\\\og-image\\\\route.tsx\";\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new AppRouteRouteModule({\n    definition: {\n        kind: RouteKind.APP_ROUTE,\n        page: \"/api/share-page/og-image/route\",\n        pathname: \"/api/share-page/og-image\",\n        filename: \"route\",\n        bundlePath: \"app/api/share-page/og-image/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\api\\\\share-page\\\\og-image\\\\route.tsx\",\n    nextConfigOutput,\n    userland\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return _patchFetch({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\nexport { routeModule, workAsyncStorage, workUnitAsyncStorage, serverHooks, patchFetch,  };\n\n//# sourceMappingURL=app-route.js.map", "var _self___RSC_MANIFEST;\nimport { createServerModuleMap } from \"next/dist/server/app-render/action-utils\";\nimport { setReferenceManifestsSingleton } from \"next/dist/server/app-render/encryption-utils\";\nimport { EdgeRouteModuleWrapper } from \"next/dist/server/web/edge-route-module-wrapper\";\n// Import the userland code.\nimport * as module from \"next-app-loader?name=app%2Fapi%2Fshare-page%2Fog-image%2Froute&page=%2Fapi%2Fshare-page%2Fog-image%2Froute&pagePath=private-next-app-dir%2Fapi%2Fshare-page%2Fog-image%2Froute.tsx&appDir=C%3A%5CUsers%5Ceenee%5Csuna%5Cfrontend%5Csrc%5Capp&appPaths=%2Fapi%2Fshare-page%2Fog-image%2Froute&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!private-next-app-dir/api/share-page/og-image/route.tsx?__next_edge_ssr_entry__\";\nconst nextConfig = {\"env\":{},\"webpack\":null,\"eslint\":{\"ignoreDuringBuilds\":false},\"typescript\":{\"ignoreBuildErrors\":false,\"tsconfigPath\":\"tsconfig.json\"},\"distDir\":\".next\",\"cleanDistDir\":true,\"assetPrefix\":\"\",\"cacheMaxMemorySize\":52428800,\"configOrigin\":\"next.config.ts\",\"useFileSystemPublicRoutes\":true,\"generateEtags\":true,\"pageExtensions\":[\"tsx\",\"ts\",\"jsx\",\"js\"],\"poweredByHeader\":true,\"compress\":true,\"images\":{\"deviceSizes\":[640,750,828,1080,1200,1920,2048,3840],\"imageSizes\":[16,32,48,64,96,128,256,384],\"path\":\"/_next/image\",\"loader\":\"default\",\"loaderFile\":\"\",\"domains\":[],\"disableStaticImages\":false,\"minimumCacheTTL\":60,\"formats\":[\"image/webp\"],\"dangerouslyAllowSVG\":false,\"contentSecurityPolicy\":\"script-src 'none'; frame-src 'none'; sandbox;\",\"contentDispositionType\":\"attachment\",\"remotePatterns\":[],\"unoptimized\":false},\"devIndicators\":{\"position\":\"bottom-left\"},\"onDemandEntries\":{\"maxInactiveAge\":60000,\"pagesBufferLength\":5},\"amp\":{\"canonicalBase\":\"\"},\"basePath\":\"\",\"sassOptions\":{},\"trailingSlash\":false,\"i18n\":null,\"productionBrowserSourceMaps\":false,\"excludeDefaultMomentLocales\":true,\"serverRuntimeConfig\":{},\"publicRuntimeConfig\":{},\"reactProductionProfiling\":false,\"reactStrictMode\":null,\"reactMaxHeadersLength\":6000,\"httpAgentOptions\":{\"keepAlive\":true},\"logging\":{},\"expireTime\":31536000,\"staticPageGenerationTimeout\":60,\"modularizeImports\":{\"@mui/icons-material\":{\"transform\":\"@mui/icons-material/{{member}}\"},\"lodash\":{\"transform\":\"lodash/{{member}}\"}},\"outputFileTracingRoot\":\"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\",\"experimental\":{\"nodeMiddleware\":false,\"cacheLife\":{\"default\":{\"stale\":300,\"revalidate\":900,\"expire\":4294967294},\"seconds\":{\"stale\":0,\"revalidate\":1,\"expire\":60},\"minutes\":{\"stale\":300,\"revalidate\":60,\"expire\":3600},\"hours\":{\"stale\":300,\"revalidate\":3600,\"expire\":86400},\"days\":{\"stale\":300,\"revalidate\":86400,\"expire\":604800},\"weeks\":{\"stale\":300,\"revalidate\":604800,\"expire\":2592000},\"max\":{\"stale\":300,\"revalidate\":2592000,\"expire\":4294967294}},\"cacheHandlers\":{},\"cssChunking\":true,\"multiZoneDraftMode\":false,\"appNavFailHandling\":false,\"prerenderEarlyExit\":true,\"serverMinification\":true,\"serverSourceMaps\":false,\"linkNoTouchStart\":false,\"caseSensitiveRoutes\":false,\"clientSegmentCache\":false,\"dynamicOnHover\":false,\"preloadEntriesOnStart\":true,\"clientRouterFilter\":true,\"clientRouterFilterRedirects\":false,\"fetchCacheKeyPrefix\":\"\",\"middlewarePrefetch\":\"flexible\",\"optimisticClientCache\":true,\"manualClientBasePath\":false,\"cpus\":7,\"memoryBasedWorkersCount\":false,\"imgOptConcurrency\":null,\"imgOptTimeoutInSeconds\":7,\"imgOptMaxInputPixels\":268402689,\"imgOptSequentialRead\":null,\"isrFlushToDisk\":true,\"workerThreads\":false,\"optimizeCss\":false,\"nextScriptWorkers\":false,\"scrollRestoration\":false,\"externalDir\":false,\"disableOptimizedLoading\":false,\"gzipSize\":true,\"craCompat\":false,\"esmExternals\":true,\"fullySpecified\":false,\"swcTraceProfiling\":false,\"forceSwcTransforms\":false,\"largePageDataBytes\":128000,\"typedRoutes\":false,\"typedEnv\":false,\"parallelServerCompiles\":false,\"parallelServerBuildTraces\":false,\"ppr\":false,\"authInterrupts\":false,\"webpackMemoryOptimizations\":false,\"optimizeServerReact\":true,\"useEarlyImport\":false,\"viewTransition\":false,\"routerBFCache\":false,\"staleTimes\":{\"dynamic\":0,\"static\":300},\"serverComponentsHmrCache\":true,\"staticGenerationMaxConcurrency\":8,\"staticGenerationMinPagesPerWorker\":25,\"dynamicIO\":false,\"inlineCss\":false,\"useCache\":false,\"optimizePackageImports\":[\"lucide-react\",\"date-fns\",\"lodash-es\",\"ramda\",\"antd\",\"react-bootstrap\",\"ahooks\",\"@ant-design/icons\",\"@headlessui/react\",\"@headlessui-float/react\",\"@heroicons/react/20/solid\",\"@heroicons/react/24/solid\",\"@heroicons/react/24/outline\",\"@visx/visx\",\"@tremor/react\",\"rxjs\",\"@mui/material\",\"@mui/icons-material\",\"recharts\",\"react-use\",\"effect\",\"@effect/schema\",\"@effect/platform\",\"@effect/platform-node\",\"@effect/platform-browser\",\"@effect/platform-bun\",\"@effect/sql\",\"@effect/sql-mssql\",\"@effect/sql-mysql2\",\"@effect/sql-pg\",\"@effect/sql-squlite-node\",\"@effect/sql-squlite-bun\",\"@effect/sql-squlite-wasm\",\"@effect/sql-squlite-react-native\",\"@effect/rpc\",\"@effect/rpc-http\",\"@effect/typeclass\",\"@effect/experimental\",\"@effect/opentelemetry\",\"@material-ui/core\",\"@material-ui/icons\",\"@tabler/icons-react\",\"mui-core\",\"react-icons/ai\",\"react-icons/bi\",\"react-icons/bs\",\"react-icons/cg\",\"react-icons/ci\",\"react-icons/di\",\"react-icons/fa\",\"react-icons/fa6\",\"react-icons/fc\",\"react-icons/fi\",\"react-icons/gi\",\"react-icons/go\",\"react-icons/gr\",\"react-icons/hi\",\"react-icons/hi2\",\"react-icons/im\",\"react-icons/io\",\"react-icons/io5\",\"react-icons/lia\",\"react-icons/lib\",\"react-icons/lu\",\"react-icons/md\",\"react-icons/pi\",\"react-icons/ri\",\"react-icons/rx\",\"react-icons/si\",\"react-icons/sl\",\"react-icons/tb\",\"react-icons/tfi\",\"react-icons/ti\",\"react-icons/vsc\",\"react-icons/wi\"]},\"htmlLimitedBots\":\"Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti\",\"bundlePagesRouterDependencies\":false,\"configFile\":\"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\next.config.ts\",\"configFileName\":\"next.config.ts\",\"turbopack\":{\"root\":\"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\"}}\nconst maybeJSONParse = (str)=>str ? JSON.parse(str) : undefined;\nconst rscManifest = (_self___RSC_MANIFEST = self.__RSC_MANIFEST) == null ? void 0 : _self___RSC_MANIFEST[\"/api/share-page/og-image/route\"];\nconst rscServerManifest = maybeJSONParse(self.__RSC_SERVER_MANIFEST);\nif (rscManifest && rscServerManifest) {\n    setReferenceManifestsSingleton({\n        page: \"/api/share-page/og-image/route\",\n        clientReferenceManifest: rscManifest,\n        serverActionsManifest: rscServerManifest,\n        serverModuleMap: createServerModuleMap({\n            serverActionsManifest: rscServerManifest\n        })\n    });\n}\nexport const ComponentMod = module;\nexport default EdgeRouteModuleWrapper.wrap(module.routeModule, {\n    nextConfig\n});\n\n//# sourceMappingURL=edge-app-route.js.map", "module.exports = require(\"node:buffer\");", "module.exports = require(\"node:async_hooks\");"], "names": ["runtime", "revalidate", "GET", "request", "searchParams", "URL", "url", "title", "get", "NextResponse", "status", "response", "fetch", "method", "headers", "Authorization", "process", "env", "ORSHOT_API_KEY", "body", "JSON", "stringify", "templateId", "modifications", "type", "ok", "Error", "blob", "arrayBuffer", "image", "<PERSON><PERSON><PERSON>", "from", "error", "console"], "sourceRoot": "", "ignoreList": []}