{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/node_modules/%40shikijs/langs/dist/hlsl.mjs"], "sourcesContent": ["const lang = Object.freeze(JSON.parse(\"{\\\"displayName\\\":\\\"HLSL\\\",\\\"name\\\":\\\"hlsl\\\",\\\"patterns\\\":[{\\\"begin\\\":\\\"/\\\\\\\\*\\\",\\\"end\\\":\\\"\\\\\\\\*/\\\",\\\"name\\\":\\\"comment.line.block.hlsl\\\"},{\\\"begin\\\":\\\"//\\\",\\\"end\\\":\\\"$\\\",\\\"name\\\":\\\"comment.line.double-slash.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b[0-9]+\\\\\\\\.[0-9]*([Ff])?\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.decimal.hlsl\\\"},{\\\"match\\\":\\\"(\\\\\\\\.([0-9]+)([Ff])?)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.decimal.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b([0-9]+([Ff])?)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.decimal.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(0([Xx])\\\\\\\\h+)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.numeric.hex.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(false|true)\\\\\\\\b\\\",\\\"name\\\":\\\"constant.language.hlsl\\\"},{\\\"match\\\":\\\"^\\\\\\\\s*#\\\\\\\\s*(define|elif|else|endif|ifdef|ifndef|if|undef|include|line|error|pragma)\\\",\\\"name\\\":\\\"keyword.preprocessor.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(break|case|continue|default|discard|do|else|for|if|return|switch|while)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(compile)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.control.fx.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(typedef)\\\\\\\\b\\\",\\\"name\\\":\\\"keyword.typealias.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(bool([1-4](x[1-4])?)?|double([1-4](x[1-4])?)?|dword|float([1-4](x[1-4])?)?|half([1-4](x[1-4])?)?|int([1-4](x[1-4])?)?|matrix|min10float([1-4](x[1-4])?)?|min12int([1-4](x[1-4])?)?|min16float([1-4](x[1-4])?)?|min16int([1-4](x[1-4])?)?|min16uint([1-4](x[1-4])?)?|unsigned|uint([1-4](x[1-4])?)?|vector|void)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.basic.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b([A-Z_a-z][0-9A-Z_a-z]*)(?=\\\\\\\\s*\\\\\\\\()\\\",\\\"name\\\":\\\"support.function.hlsl\\\"},{\\\"match\\\":\\\"(?<=:\\\\\\\\s?)(?i:BINORMAL[0-9]*|BLENDINDICES[0-9]*|BLENDWEIGHT[0-9]*|COLOR[0-9]*|NORMAL[0-9]*|POSITIONT?|PSIZE[0-9]*|TANGENT[0-9]*|TEXCOORD[0-9]*|FOG|TESSFACTOR[0-9]*|VFACE|VPOS|DEPTH[0-9]*)\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.semantic.hlsl\\\"},{\\\"match\\\":\\\"(?<=:\\\\\\\\s?)(?i:SV_(?:ClipDistance[0-9]*|CullDistance[0-9]*|Coverage|Depth|DepthGreaterEqual[0-9]*|DepthLessEqual[0-9]*|InstanceID|IsFrontFace|Position|RenderTargetArrayIndex|SampleIndex|StencilRef|Target[0-7]?|VertexID|ViewportArrayIndex))\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.semantic.sm4.hlsl\\\"},{\\\"match\\\":\\\"(?<=:\\\\\\\\s?)(?i:SV_(?:DispatchThreadID|DomainLocation|GroupID|GroupIndex|GroupThreadID|GSInstanceID|InsideTessFactor|OutputControlPointID|TessFactor))\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.semantic.sm5.hlsl\\\"},{\\\"match\\\":\\\"(?<=:\\\\\\\\s?)(?i:SV_(?:InnerCoverage|StencilRef))\\\\\\\\b\\\",\\\"name\\\":\\\"support.variable.semantic.sm5_1.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(column_major|const|export|extern|globallycoherent|groupshared|inline|inout|in|out|precise|row_major|shared|static|uniform|volatile)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b([su]norm)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.float.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(packoffset|register)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.postfix.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(centroid|linear|nointerpolation|noperspective|sample)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.interpolation.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(lineadj|line|point|triangle|triangleadj)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.modifier.geometryshader.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(string)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.other.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(AppendStructuredBuffer|Buffer|ByteAddressBuffer|ConstantBuffer|ConsumeStructuredBuffer|InputPatch|OutputPatch)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.object.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(RasterizerOrdered(?:Buffer|ByteAddressBuffer|StructuredBuffer|Texture1D|Texture1DArray|Texture2D|Texture2DArray|Texture3D))\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.object.rasterizerordered.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(RW(?:Buffer|ByteAddressBuffer|StructuredBuffer|Texture1D|Texture1DArray|Texture2D|Texture2DArray|Texture3D))\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.object.rw.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b((?:Line|Point|Triangle)Stream)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.object.geometryshader.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(sampler(?:|1D|2D|3D|CUBE|_state))\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.sampler.legacy.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(Sampler(?:|Comparison)State)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.sampler.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(texture(?:2D|CUBE))\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.texture.legacy.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(Texture(?:1D|1DArray|2D|2DArray|2DMS|2DMSArray|3D|Cube|CubeArray))\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.texture.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(cbuffer|class|interface|namespace|struct|tbuffer)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.structured.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(FALSE|TRUE|NULL)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.fx.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b((?:Blend|DepthStencil|Rasterizer)State)\\\\\\\\b\\\",\\\"name\\\":\\\"support.type.fx.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(technique|Technique|technique10|technique11|pass)\\\\\\\\b\\\",\\\"name\\\":\\\"storage.type.fx.technique.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(AlphaToCoverageEnable|BlendEnable|SrcBlend|DestBlend|BlendOp|SrcBlendAlpha|DestBlendAlpha|BlendOpAlpha|RenderTargetWriteMask)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.object-literal.key.fx.blendstate.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(DepthEnable|DepthWriteMask|DepthFunc|StencilEnable|StencilReadMask|StencilWriteMask|FrontFaceStencilFail|FrontFaceStencilZFail|FrontFaceStencilPass|FrontFaceStencilFunc|BackFaceStencilFail|BackFaceStencilZFail|BackFaceStencilPass|BackFaceStencilFunc)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.object-literal.key.fx.depthstencilstate.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(FillMode|CullMode|FrontCounterClockwise|DepthBias|DepthBiasClamp|SlopeScaleDepthBias|ZClipEnable|ScissorEnable|MultiSampleEnable|AntiAliasedLineEnable)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.object-literal.key.fx.rasterizerstate.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(Filter|AddressU|AddressV|AddressW|MipLODBias|MaxAnisotropy|ComparisonFunc|BorderColor|MinLOD|MaxLOD)\\\\\\\\b\\\",\\\"name\\\":\\\"meta.object-literal.key.fx.samplerstate.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:ZERO|ONE|SRC_COLOR|INV_SRC_COLOR|SRC_ALPHA|INV_SRC_ALPHA|DEST_ALPHA|INV_DEST_ALPHA|DEST_COLOR|INV_DEST_COLOR|SRC_ALPHA_SAT|BLEND_FACTOR|INV_BLEND_FACTOR|SRC1_COLOR|INV_SRC1_COLOR|SRC1_ALPHA|INV_SRC1_ALPHA)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.fx.blend.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:ADD|SUBTRACT|REV_SUBTRACT|MIN|MAX)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.fx.blendop.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:ALL)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.fx.depthwritemask.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:NEVER|LESS|EQUAL|LESS_EQUAL|GREATER|NOT_EQUAL|GREATER_EQUAL|ALWAYS)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.fx.comparisonfunc.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:KEEP|REPLACE|INCR_SAT|DECR_SAT|INVERT|INCR|DECR)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.fx.stencilop.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:WIREFRAME|SOLID)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.fx.fillmode.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:NONE|FRONT|BACK)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.fx.cullmode.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:MIN_MAG_MIP_POINT|MIN_MAG_POINT_MIP_LINEAR|MIN_POINT_MAG_LINEAR_MIP_POINT|MIN_POINT_MAG_MIP_LINEAR|MIN_LINEAR_MAG_MIP_POINT|MIN_LINEAR_MAG_POINT_MIP_LINEAR|MIN_MAG_LINEAR_MIP_POINT|MIN_MAG_MIP_LINEAR|ANISOTROPIC|COMPARISON_MIN_MAG_MIP_POINT|COMPARISON_MIN_MAG_POINT_MIP_LINEAR|COMPARISON_MIN_POINT_MAG_LINEAR_MIP_POINT|COMPARISON_MIN_POINT_MAG_MIP_LINEAR|COMPARISON_MIN_LINEAR_MAG_MIP_POINT|COMPARISON_MIN_LINEAR_MAG_POINT_MIP_LINEAR|COMPARISON_MIN_MAG_LINEAR_MIP_POINT|COMPARISON_MIN_MAG_MIP_LINEAR|COMPARISON_ANISOTROPIC|TEXT_1BIT)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.fx.filter.hlsl\\\"},{\\\"match\\\":\\\"\\\\\\\\b(?i:WRAP|MIRROR|CLAMP|BORDER|MIRROR_ONCE)\\\\\\\\b\\\",\\\"name\\\":\\\"support.constant.property-value.fx.textureaddressmode.hlsl\\\"},{\\\"begin\\\":\\\"\\\\\\\"\\\",\\\"end\\\":\\\"\\\\\\\"\\\",\\\"name\\\":\\\"string.quoted.double.hlsl\\\",\\\"patterns\\\":[{\\\"match\\\":\\\"\\\\\\\\\\\\\\\\.\\\",\\\"name\\\":\\\"constant.character.escape.hlsl\\\"}]}],\\\"scopeName\\\":\\\"source.hlsl\\\"}\"))\n\nexport default [\nlang\n]\n"], "names": [], "mappings": ";;;AAAA,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC;uCAEvB;IACf;CACC", "ignoreList": [0], "debugId": null}}]}