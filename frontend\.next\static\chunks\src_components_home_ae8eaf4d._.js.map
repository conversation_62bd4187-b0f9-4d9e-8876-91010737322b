{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>/cta-section.tsx"], "sourcesContent": ["import Image from 'next/image';\r\nimport { siteConfig } from '@/lib/home';\r\nimport Link from 'next/link';\r\nimport { HeroVideoSection } from './hero-video-section';\r\n\r\nexport function CTASection() {\r\n  const { ctaSection } = siteConfig;\r\n\r\n  return (\r\n    <section\r\n      id=\"cta\"\r\n      className=\"flex flex-col items-center justify-center w-full pt-12 pb-12\"\r\n    >\r\n      <div className=\"w-full max-w-6xl mx-auto px-6\">\r\n        <div className=\"h-[400px] md:h-[400px] overflow-hidden shadow-xl w-full border border-border rounded-xl bg-secondary relative z-20\">\r\n          <div className=\"absolute inset-0 -top-32 md:-top-40 flex flex-col items-center justify-center\">\r\n            <h1 className=\"text-white text-4xl md:text-7xl font-medium tracking-tighter max-w-xs md:max-w-xl text-center\">\r\n              {ctaSection.title}\r\n            </h1>\r\n            <div className=\"absolute bottom-10 flex flex-col items-center justify-center gap-2\">\r\n              <Link\r\n                href={ctaSection.button.href}\r\n                className=\"bg-white text-black font-semibold text-sm h-10 w-fit px-4 rounded-full flex items-center justify-center shadow-md\"\r\n              >\r\n                {ctaSection.button.text}\r\n              </Link>\r\n              <span className=\"text-white text-sm\">{ctaSection.subtext}</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AAGO,SAAS;IACd,MAAM,EAAE,UAAU,EAAE,GAAG,sHAAA,CAAA,aAAU;IAEjC,qBACE,6LAAC;QACC,IAAG;QACH,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,WAAW,KAAK;;;;;;sCAEnB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAM,WAAW,MAAM,CAAC,IAAI;oCAC5B,WAAU;8CAET,WAAW,MAAM,CAAC,IAAI;;;;;;8CAEzB,6LAAC;oCAAK,WAAU;8CAAsB,WAAW,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtE;KA5BgB", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>/footer-section.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { FlickeringGrid } from '@/components/home/<USER>/flickering-grid';\r\nimport { useMediaQuery } from '@/hooks/use-media-query';\r\nimport { siteConfig } from '@/lib/home';\r\nimport { ChevronRightIcon } from '@radix-ui/react-icons';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { useTheme } from 'next-themes';\r\nimport { useEffect, useState } from 'react';\r\n\r\nexport function FooterSection() {\r\n  const tablet = useMediaQuery('(max-width: 1024px)');\r\n  const { theme, resolvedTheme } = useTheme();\r\n  const [mounted, setMounted] = useState(false);\r\n\r\n  // After mount, we can access the theme\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  const logoSrc = !mounted\r\n    ? '/kortix-logo.svg'\r\n    : resolvedTheme === 'dark'\r\n      ? '/kortix-logo-white.svg'\r\n      : '/kortix-logo.svg';\r\n\r\n  return (\r\n    <footer id=\"footer\" className=\"w-full pb-0\">\r\n      <div className=\"flex flex-col md:flex-row md:items-center md:justify-between p-10 max-w-6xl mx-auto\">\r\n        <div className=\"flex flex-col items-start justify-start gap-y-5 max-w-xs mx-0\">\r\n          <Link href=\"/\" className=\"flex items-center gap-2\">\r\n            <Image\r\n              src={logoSrc}\r\n              alt=\"Kortix Logo\"\r\n              width={122}\r\n              height={22}\r\n              priority\r\n            />\r\n          </Link>\r\n          <p className=\"tracking-tight text-muted-foreground font-medium\">\r\n            {siteConfig.hero.description}\r\n          </p>\r\n\r\n          <div className=\"flex items-center gap-4\">\r\n            <a\r\n              href=\"https://github.com/kortix-ai/suna\"\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              aria-label=\"GitHub\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                viewBox=\"0 0 24 24\"\r\n                className=\"size-5 text-muted-foreground hover:text-primary transition-colors\"\r\n              >\r\n                <path\r\n                  fill=\"currentColor\"\r\n                  d=\"M12 2C6.477 2 2 6.484 2 12.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0 1 12 6.844a9.59 9.59 0 0 1 2.504.337c1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.202 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.943.359.309.678.92.678 1.855 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.02 10.02 0 0 0 22 12.017C22 6.484 17.522 2 12 2z\"\r\n                />\r\n              </svg>\r\n            </a>\r\n            <a\r\n              href=\"https://x.com/kortixai\"\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              aria-label=\"X (Twitter)\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                viewBox=\"0 0 24 24\"\r\n                className=\"size-5 text-muted-foreground hover:text-primary transition-colors\"\r\n              >\r\n                <path\r\n                  fill=\"currentColor\"\r\n                  d=\"M18.901 1.153h3.68l-8.04 9.19L24 22.846h-7.406l-5.8-7.584-6.638 7.584H.474l8.6-9.83L0 1.154h7.594l5.243 6.932ZM17.61 20.644h2.039L6.486 3.24H4.298Z\"\r\n                />\r\n              </svg>\r\n            </a>\r\n            <a\r\n              href=\"https://www.linkedin.com/company/kortix/\"\r\n              target=\"_blank\"\r\n              rel=\"noopener noreferrer\"\r\n              aria-label=\"LinkedIn\"\r\n            >\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                viewBox=\"0 0 24 24\"\r\n                className=\"size-5 text-muted-foreground hover:text-primary transition-colors\"\r\n              >\r\n                <path\r\n                  fill=\"currentColor\"\r\n                  d=\"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433a2.062 2.062 0 01-2.063-2.065 2.064 2.064 0 112.063 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z\"\r\n                />\r\n              </svg>\r\n            </a>\r\n          </div>\r\n          {/* <div className=\"flex items-center gap-2 dark:hidden\">\r\n            <Icons.soc2 className=\"size-12\" />\r\n            <Icons.hipaa className=\"size-12\" />\r\n            <Icons.gdpr className=\"size-12\" />\r\n          </div>\r\n          <div className=\"dark:flex items-center gap-2 hidden\">\r\n            <Icons.soc2Dark className=\"size-12\" />\r\n            <Icons.hipaaDark className=\"size-12\" />\r\n            <Icons.gdprDark className=\"size-12\" />\r\n          </div> */}\r\n        </div>\r\n        <div className=\"pt-5 md:w-1/2\">\r\n          <div className=\"flex flex-col items-start justify-start md:flex-row md:items-center md:justify-between gap-y-5 lg:pl-10\">\r\n            {siteConfig.footerLinks.map((column, columnIndex) => (\r\n              <ul key={columnIndex} className=\"flex flex-col gap-y-2\">\r\n                <li className=\"mb-2 text-sm font-semibold text-primary\">\r\n                  {column.title}\r\n                </li>\r\n                {column.links.map((link) => (\r\n                  <li\r\n                    key={link.id}\r\n                    className=\"group inline-flex cursor-pointer items-center justify-start gap-1 text-[15px]/snug text-muted-foreground\"\r\n                  >\r\n                    <Link href={link.url}>{link.title}</Link>\r\n                    <div className=\"flex size-4 items-center justify-center border border-border rounded translate-x-0 transform opacity-0 transition-all duration-300 ease-out group-hover:translate-x-1 group-hover:opacity-100\">\r\n                      <ChevronRightIcon className=\"h-4 w-4 \" />\r\n                    </div>\r\n                  </li>\r\n                ))}\r\n              </ul>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <Link\r\n        href=\"https://www.youtube.com/watch?v=nuf5BF1jvjQ\"\r\n        target=\"_blank\"\r\n        rel=\"noopener noreferrer\"\r\n        className=\"block w-full h-48 md:h-64 relative mt-24 z-0 cursor-pointer\"\r\n      >\r\n        <div className=\"absolute inset-0 bg-gradient-to-t from-transparent to-background z-10 from-40%\" />\r\n        <div className=\"absolute inset-0 \">\r\n          <FlickeringGrid\r\n            text={tablet ? 'Agents' : 'Agents Agents Agents'}\r\n            fontSize={tablet ? 60 : 90}\r\n            className=\"h-full w-full\"\r\n            squareSize={2}\r\n            gridGap={tablet ? 2 : 3}\r\n            color=\"#6B7280\"\r\n            maxOpacity={0.3}\r\n            flickerChance={0.1}\r\n          />\r\n        </div>\r\n      </Link>\r\n    </footer>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAWO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAAE;IAC7B,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACxC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,WAAW;QACb;kCAAG,EAAE;IAEL,MAAM,UAAU,CAAC,UACb,qBACA,kBAAkB,SAChB,2BACA;IAEN,qBACE,6LAAC;QAAO,IAAG;QAAS,WAAU;;0BAC5B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAK;oCACL,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;0CAGZ,6LAAC;gCAAE,WAAU;0CACV,sHAAA,CAAA,aAAU,CAAC,IAAI,CAAC,WAAW;;;;;;0CAG9B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,cAAW;kDAEX,cAAA,6LAAC;4CACC,OAAM;4CACN,SAAQ;4CACR,WAAU;sDAEV,cAAA,6LAAC;gDACC,MAAK;gDACL,GAAE;;;;;;;;;;;;;;;;kDAIR,6LAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,cAAW;kDAEX,cAAA,6LAAC;4CACC,OAAM;4CACN,SAAQ;4CACR,WAAU;sDAEV,cAAA,6LAAC;gDACC,MAAK;gDACL,GAAE;;;;;;;;;;;;;;;;kDAIR,6LAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,cAAW;kDAEX,cAAA,6LAAC;4CACC,OAAM;4CACN,SAAQ;4CACR,WAAU;sDAEV,cAAA,6LAAC;gDACC,MAAK;gDACL,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAgBZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,sHAAA,CAAA,aAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,QAAQ,4BACnC,6LAAC;oCAAqB,WAAU;;sDAC9B,6LAAC;4CAAG,WAAU;sDACX,OAAO,KAAK;;;;;;wCAEd,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,qBACjB,6LAAC;gDAEC,WAAU;;kEAEV,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,GAAG;kEAAG,KAAK,KAAK;;;;;;kEACjC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,mLAAA,CAAA,mBAAgB;4DAAC,WAAU;;;;;;;;;;;;+CALzB,KAAK,EAAE;;;;;;mCANT;;;;;;;;;;;;;;;;;;;;;0BAoBjB,6LAAC,+JAAA,CAAA,UAAI;gBACH,MAAK;gBACL,QAAO;gBACP,KAAI;gBACJ,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,yJAAA,CAAA,iBAAc;4BACb,MAAM,SAAS,WAAW;4BAC1B,UAAU,SAAS,KAAK;4BACxB,WAAU;4BACV,YAAY;4BACZ,SAAS,SAAS,IAAI;4BACtB,OAAM;4BACN,YAAY;4BACZ,eAAe;;;;;;;;;;;;;;;;;;;;;;;AAM3B;GA9IgB;;QACC,wIAAA,CAAA,gBAAa;QACK,mJAAA,CAAA,WAAQ;;;KAF3B", "debugId": null}}, {"offset": {"line": 397, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>"], "sourcesContent": ["interface SectionHeaderProps {\r\n  children: React.ReactNode;\r\n}\r\n\r\nexport function SectionHeader({ children }: SectionHeaderProps) {\r\n  return (\r\n    <div className=\"border-b w-full h-full p-10 md:p-14\">\r\n      <div className=\"max-w-xl mx-auto flex flex-col items-center justify-center gap-2\">\r\n        {children}\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAIO,SAAS,cAAc,EAAE,QAAQ,EAAsB;IAC5D,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;KARgB", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>/pricing-section.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { SectionHeader } from '@/components/home/<USER>';\r\nimport type { PricingTier } from '@/lib/home';\r\nimport { siteConfig } from '@/lib/home';\r\nimport { cn } from '@/lib/utils';\r\nimport { motion } from 'motion/react';\r\nimport React, { useState, useEffect } from 'react';\r\nimport { CheckIcon } from 'lucide-react';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  createCheckoutSession,\r\n  SubscriptionStatus,\r\n  CreateCheckoutSessionResponse,\r\n} from '@/lib/api';\r\nimport { toast } from 'sonner';\r\nimport { isLocalMode } from '@/lib/config';\r\nimport { useSubscription } from '@/hooks/react-query';\r\n\r\n// Constants\r\nexport const SUBSCRIPTION_PLANS = {\r\n  FREE: 'free',\r\n  PRO: 'base',\r\n  ENTERPRISE: 'extra',\r\n};\r\n\r\n// Types\r\ntype ButtonVariant =\r\n  | 'default'\r\n  | 'secondary'\r\n  | 'ghost'\r\n  | 'outline'\r\n  | 'link'\r\n  | null;\r\n\r\ninterface PricingTabsProps {\r\n  activeTab: 'cloud' | 'self-hosted';\r\n  setActiveTab: (tab: 'cloud' | 'self-hosted') => void;\r\n  className?: string;\r\n}\r\n\r\ninterface PriceDisplayProps {\r\n  price: string;\r\n  isCompact?: boolean;\r\n}\r\n\r\ninterface PricingTierProps {\r\n  tier: PricingTier;\r\n  isCompact?: boolean;\r\n  currentSubscription: SubscriptionStatus | null;\r\n  isLoading: Record<string, boolean>;\r\n  isFetchingPlan: boolean;\r\n  selectedPlan?: string;\r\n  onPlanSelect?: (planId: string) => void;\r\n  onSubscriptionUpdate?: () => void;\r\n  isAuthenticated?: boolean;\r\n  returnUrl: string;\r\n  insideDialog?: boolean;\r\n  billingPeriod?: 'monthly' | 'yearly';\r\n}\r\n\r\n// Components\r\nfunction PricingTabs({ activeTab, setActiveTab, className }: PricingTabsProps) {\r\n  return (\r\n    <div\r\n      className={cn(\r\n        'relative flex w-fit items-center rounded-full border p-0.5 backdrop-blur-sm cursor-pointer h-9 flex-row bg-muted',\r\n        className,\r\n      )}\r\n    >\r\n      {['cloud', 'self-hosted'].map((tab) => (\r\n        <button\r\n          key={tab}\r\n          onClick={() => setActiveTab(tab as 'cloud' | 'self-hosted')}\r\n          className={cn(\r\n            'relative z-[1] px-3 h-8 flex items-center justify-center cursor-pointer',\r\n            {\r\n              'z-0': activeTab === tab,\r\n            },\r\n          )}\r\n        >\r\n          {activeTab === tab && (\r\n            <motion.div\r\n              layoutId=\"active-tab\"\r\n              className=\"absolute inset-0 rounded-full bg-white dark:bg-[#3F3F46] shadow-md border border-border\"\r\n              transition={{\r\n                duration: 0.2,\r\n                type: 'spring',\r\n                stiffness: 300,\r\n                damping: 25,\r\n                velocity: 2,\r\n              }}\r\n            />\r\n          )}\r\n          <span\r\n            className={cn(\r\n              'relative block text-sm font-medium duration-200 shrink-0',\r\n              activeTab === tab ? 'text-primary' : 'text-muted-foreground',\r\n            )}\r\n          >\r\n            {tab === 'cloud' ? 'Cloud' : 'Self-hosted'}\r\n          </span>\r\n        </button>\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction PriceDisplay({ price, isCompact }: PriceDisplayProps) {\r\n  return (\r\n    <motion.span\r\n      key={price}\r\n      className={isCompact ? 'text-xl font-semibold' : 'text-4xl font-semibold'}\r\n      initial={{\r\n        opacity: 0,\r\n        x: 10,\r\n        filter: 'blur(5px)',\r\n      }}\r\n      animate={{ opacity: 1, x: 0, filter: 'blur(0px)' }}\r\n      transition={{ duration: 0.25, ease: [0.4, 0, 0.2, 1] }}\r\n    >\r\n      {price}\r\n    </motion.span>\r\n  );\r\n}\r\n\r\nfunction BillingPeriodToggle({\r\n  billingPeriod,\r\n  setBillingPeriod\r\n}: {\r\n  billingPeriod: 'monthly' | 'yearly';\r\n  setBillingPeriod: (period: 'monthly' | 'yearly') => void;\r\n}) {\r\n  return (\r\n    <div className=\"flex items-center justify-center gap-3\">\r\n      <div\r\n        className=\"relative bg-muted rounded-full p-1 cursor-pointer\"\r\n        onClick={() => setBillingPeriod(billingPeriod === 'monthly' ? 'yearly' : 'monthly')}\r\n      >\r\n        <div className=\"flex\">\r\n          <div className={cn(\"px-3 py-1 rounded-full text-xs font-medium transition-all duration-200\",\r\n            billingPeriod === 'monthly'\r\n              ? 'bg-background text-foreground shadow-sm'\r\n              : 'text-muted-foreground'\r\n          )}>\r\n            Monthly\r\n          </div>\r\n          <div className={cn(\"px-3 py-1 rounded-full text-xs font-medium transition-all duration-200 flex items-center gap-1\",\r\n            billingPeriod === 'yearly'\r\n              ? 'bg-background text-foreground shadow-sm'\r\n              : 'text-muted-foreground'\r\n          )}>\r\n            Yearly\r\n            <span className=\"bg-green-600 text-green-50 dark:bg-green-500 dark:text-green-50 text-[10px] px-1.5 py-0.5 rounded-full font-semibold whitespace-nowrap\">\r\n              15% off\r\n            </span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nfunction PricingTier({\r\n  tier,\r\n  isCompact = false,\r\n  currentSubscription,\r\n  isLoading,\r\n  isFetchingPlan,\r\n  selectedPlan,\r\n  onPlanSelect,\r\n  onSubscriptionUpdate,\r\n  isAuthenticated = false,\r\n  returnUrl,\r\n  insideDialog = false,\r\n  billingPeriod = 'monthly',\r\n}: PricingTierProps) {\r\n  // Auto-select the correct plan only on initial load - simplified since no more Custom tier\r\n  const handleSubscribe = async (planStripePriceId: string) => {\r\n    if (!isAuthenticated) {\r\n      window.location.href = '/auth?mode=signup';\r\n      return;\r\n    }\r\n\r\n    if (isLoading[planStripePriceId]) {\r\n      return;\r\n    }\r\n\r\n    try {\r\n      onPlanSelect?.(planStripePriceId);\r\n\r\n      const response: CreateCheckoutSessionResponse =\r\n        await createCheckoutSession({\r\n          price_id: planStripePriceId,\r\n          success_url: returnUrl,\r\n          cancel_url: returnUrl,\r\n        });\r\n\r\n      console.log('Subscription action response:', response);\r\n\r\n      switch (response.status) {\r\n        case 'new':\r\n        case 'checkout_created':\r\n          if (response.url) {\r\n            window.location.href = response.url;\r\n          } else {\r\n            console.error(\r\n              \"Error: Received status 'checkout_created' but no checkout URL.\",\r\n            );\r\n            toast.error('Failed to initiate subscription. Please try again.');\r\n          }\r\n          break;\r\n        case 'upgraded':\r\n        case 'updated':\r\n          const upgradeMessage = response.details?.is_upgrade\r\n            ? `Subscription upgraded from $${response.details.current_price} to $${response.details.new_price}`\r\n            : 'Subscription updated successfully';\r\n          toast.success(upgradeMessage);\r\n          if (onSubscriptionUpdate) onSubscriptionUpdate();\r\n          break;\r\n        case 'downgrade_scheduled':\r\n        case 'scheduled':\r\n          const effectiveDate = response.effective_date\r\n            ? new Date(response.effective_date).toLocaleDateString()\r\n            : 'the end of your billing period';\r\n\r\n          const statusChangeMessage = 'Subscription change scheduled';\r\n\r\n          toast.success(\r\n            <div>\r\n              <p>{statusChangeMessage}</p>\r\n              <p className=\"text-sm mt-1\">\r\n                Your plan will change on {effectiveDate}.\r\n              </p>\r\n            </div>,\r\n          );\r\n          if (onSubscriptionUpdate) onSubscriptionUpdate();\r\n          break;\r\n        case 'no_change':\r\n          toast.info(response.message || 'You are already on this plan.');\r\n          break;\r\n        default:\r\n          console.warn(\r\n            'Received unexpected status from createCheckoutSession:',\r\n            response.status,\r\n          );\r\n          toast.error('An unexpected error occurred. Please try again.');\r\n      }\r\n    } catch (error: any) {\r\n      console.error('Error processing subscription:', error);\r\n      const errorMessage =\r\n        error?.response?.data?.detail ||\r\n        error?.message ||\r\n        'Failed to process subscription. Please try again.';\r\n      toast.error(errorMessage);\r\n    }\r\n  };\r\n\r\n  const tierPriceId = billingPeriod === 'yearly' && tier.yearlyStripePriceId\r\n    ? tier.yearlyStripePriceId\r\n    : tier.stripePriceId;\r\n  const displayPrice = billingPeriod === 'yearly' && tier.yearlyPrice\r\n    ? tier.yearlyPrice\r\n    : tier.price;\r\n\r\n  // Find the current tier (moved outside conditional for JSX access)\r\n  const currentTier = siteConfig.cloudPricingItems.find(\r\n    (p) => p.stripePriceId === currentSubscription?.price_id || p.yearlyStripePriceId === currentSubscription?.price_id,\r\n  );\r\n\r\n  const isCurrentActivePlan =\r\n    isAuthenticated && currentSubscription?.price_id === tierPriceId;\r\n  const isScheduled = isAuthenticated && currentSubscription?.has_schedule;\r\n  const isScheduledTargetPlan =\r\n    isScheduled && currentSubscription?.scheduled_price_id === tierPriceId;\r\n  const isPlanLoading = isLoading[tierPriceId];\r\n\r\n  let buttonText = isAuthenticated ? 'Select Plan' : 'Start Free';\r\n  let buttonDisabled = isPlanLoading;\r\n  let buttonVariant: ButtonVariant = null;\r\n  let ringClass = '';\r\n  let statusBadge = null;\r\n  let buttonClassName = '';\r\n\r\n  if (isAuthenticated) {\r\n    if (isCurrentActivePlan) {\r\n      buttonText = 'Current Plan';\r\n      buttonDisabled = true;\r\n      buttonVariant = 'secondary';\r\n      ringClass = isCompact ? 'ring-1 ring-primary' : 'ring-2 ring-primary';\r\n      buttonClassName = 'bg-primary/5 hover:bg-primary/10 text-primary';\r\n      statusBadge = (\r\n        <span className=\"bg-primary/10 text-primary text-[10px] font-medium px-1.5 py-0.5 rounded-full\">\r\n          Current\r\n        </span>\r\n      );\r\n    } else if (isScheduledTargetPlan) {\r\n      buttonText = 'Scheduled';\r\n      buttonDisabled = true;\r\n      buttonVariant = 'outline';\r\n      ringClass = isCompact\r\n        ? 'ring-1 ring-yellow-500'\r\n        : 'ring-2 ring-yellow-500';\r\n      buttonClassName =\r\n        'bg-yellow-500/5 hover:bg-yellow-500/10 text-yellow-600 border-yellow-500/20';\r\n      statusBadge = (\r\n        <span className=\"bg-yellow-500/10 text-yellow-600 text-[10px] font-medium px-1.5 py-0.5 rounded-full\">\r\n          Scheduled\r\n        </span>\r\n      );\r\n    } else if (isScheduled && currentSubscription?.price_id === tierPriceId) {\r\n      buttonText = 'Change Scheduled';\r\n      buttonVariant = 'secondary';\r\n      ringClass = isCompact ? 'ring-1 ring-primary' : 'ring-2 ring-primary';\r\n      buttonClassName = 'bg-primary/5 hover:bg-primary/10 text-primary';\r\n      statusBadge = (\r\n        <span className=\"bg-yellow-500/10 text-yellow-600 text-[10px] font-medium px-1.5 py-0.5 rounded-full\">\r\n          Downgrade Pending\r\n        </span>\r\n      );\r\n    } else {\r\n      const currentPriceString = currentSubscription\r\n        ? currentTier?.price || '$0'\r\n        : '$0';\r\n      const selectedPriceString = tier.price;\r\n      const currentAmount =\r\n        currentPriceString === '$0'\r\n          ? 0\r\n          : parseFloat(currentPriceString.replace(/[^\\d.]/g, '') || '0') * 100;\r\n      const targetAmount =\r\n        selectedPriceString === '$0'\r\n          ? 0\r\n          : parseFloat(selectedPriceString.replace(/[^\\d.]/g, '') || '0') * 100;\r\n\r\n      // Check if current subscription is monthly and target is yearly for same tier\r\n      const currentIsMonthly = currentTier && currentSubscription?.price_id === currentTier.stripePriceId;\r\n      const currentIsYearly = currentTier && currentSubscription?.price_id === currentTier.yearlyStripePriceId;\r\n      const targetIsMonthly = tier.stripePriceId === tierPriceId;\r\n      const targetIsYearly = tier.yearlyStripePriceId === tierPriceId;\r\n      const isSameTierDifferentBilling = currentTier && currentTier.name === tier.name &&\r\n        ((currentIsMonthly && targetIsYearly) || (currentIsYearly && targetIsMonthly));\r\n\r\n      if (\r\n        currentAmount === 0 &&\r\n        targetAmount === 0 &&\r\n        currentSubscription?.status !== 'no_subscription'\r\n      ) {\r\n        buttonText = 'Select Plan';\r\n        buttonDisabled = true;\r\n        buttonVariant = 'secondary';\r\n        buttonClassName = 'bg-primary/5 hover:bg-primary/10 text-primary';\r\n      } else {\r\n        if (targetAmount > currentAmount || (currentIsMonthly && targetIsYearly && targetAmount >= currentAmount)) {\r\n          // Allow upgrade to higher tier OR switch from monthly to yearly at same/higher tier\r\n          // But prevent yearly to monthly switches even if target amount is higher\r\n          if (currentIsYearly && targetIsMonthly) {\r\n            buttonText = '-';\r\n            buttonDisabled = true;\r\n            buttonVariant = 'secondary';\r\n            buttonClassName =\r\n              'opacity-50 cursor-not-allowed bg-muted text-muted-foreground';\r\n          } else if (currentIsMonthly && targetIsYearly && targetAmount === currentAmount) {\r\n            buttonText = 'Switch to Yearly';\r\n            buttonVariant = 'default';\r\n            buttonClassName = 'bg-green-600 hover:bg-green-700 text-white';\r\n          } else {\r\n            buttonText = 'Upgrade';\r\n            buttonVariant = tier.buttonColor as ButtonVariant;\r\n            buttonClassName = 'bg-primary hover:bg-primary/90 text-primary-foreground';\r\n          }\r\n        } else if (targetAmount < currentAmount && !(currentIsYearly && targetIsMonthly && targetAmount === currentAmount)) {\r\n          buttonText = '-';\r\n          buttonDisabled = true;\r\n          buttonVariant = 'secondary';\r\n          buttonClassName =\r\n            'opacity-50 cursor-not-allowed bg-muted text-muted-foreground';\r\n        } else if (isSameTierDifferentBilling) {\r\n          // Allow switching between monthly and yearly for same tier\r\n          if (currentIsMonthly && targetIsYearly) {\r\n            buttonText = 'Switch to Yearly';\r\n            buttonVariant = 'default';\r\n            buttonClassName = 'bg-green-600 hover:bg-green-700 text-white';\r\n          } else if (currentIsYearly && targetIsMonthly) {\r\n            // Prevent downgrade from yearly to monthly\r\n            buttonText = '-';\r\n            buttonDisabled = true;\r\n            buttonVariant = 'secondary';\r\n            buttonClassName =\r\n              'opacity-50 cursor-not-allowed bg-muted text-muted-foreground';\r\n          } else {\r\n            buttonText = 'Select Plan';\r\n            buttonVariant = tier.buttonColor as ButtonVariant;\r\n            buttonClassName =\r\n              'bg-primary hover:bg-primary/90 text-primary-foreground';\r\n          }\r\n        } else {\r\n          buttonText = 'Select Plan';\r\n          buttonVariant = tier.buttonColor as ButtonVariant;\r\n          buttonClassName =\r\n            'bg-primary hover:bg-primary/90 text-primary-foreground';\r\n        }\r\n      }\r\n    }\r\n\r\n    if (isPlanLoading) {\r\n      buttonText = 'Loading...';\r\n      buttonClassName = 'opacity-70 cursor-not-allowed';\r\n    }\r\n  } else {\r\n    // Non-authenticated state styling\r\n    buttonVariant = tier.buttonColor as ButtonVariant;\r\n    buttonClassName =\r\n      tier.buttonColor === 'default'\r\n        ? 'bg-primary hover:bg-primary/90 text-white'\r\n        : 'bg-secondary hover:bg-secondary/90 text-white';\r\n  }\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        'rounded-xl flex flex-col relative',\r\n        insideDialog\r\n          ? 'min-h-[300px]'\r\n          : 'h-full min-h-[300px]',\r\n        tier.isPopular && !insideDialog\r\n          ? 'md:shadow-[0px_61px_24px_-10px_rgba(0,0,0,0.01),0px_34px_20px_-8px_rgba(0,0,0,0.05),0px_15px_15px_-6px_rgba(0,0,0,0.09),0px_4px_8px_-2px_rgba(0,0,0,0.10),0px_0px_0px_1px_rgba(0,0,0,0.08)] bg-accent'\r\n          : 'bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] border border-border',\r\n        !insideDialog && ringClass,\r\n      )}\r\n    >\r\n      <div className={cn(\r\n        \"flex flex-col gap-3\",\r\n        insideDialog ? \"p-3\" : \"p-4\"\r\n      )}>\r\n        <p className=\"text-sm flex items-center gap-2\">\r\n          {tier.name}\r\n          {tier.isPopular && (\r\n            <span className=\"bg-gradient-to-b from-secondary/50 from-[1.92%] to-secondary to-[100%] text-white inline-flex w-fit items-center justify-center px-1.5 py-0.5 rounded-full text-[10px] font-medium shadow-[0px_6px_6px_-3px_rgba(0,0,0,0.08),0px_3px_3px_-1.5px_rgba(0,0,0,0.08),0px_1px_1px_-0.5px_rgba(0,0,0,0.08),0px_0px_0px_1px_rgba(255,255,255,0.12)_inset,0px_1px_0px_0px_rgba(255,255,255,0.12)_inset]\">\r\n              Popular\r\n            </span>\r\n          )}\r\n          {/* Show upgrade badge for yearly plans when user is on monthly */}\r\n          {!tier.isPopular && isAuthenticated && currentSubscription && billingPeriod === 'yearly' &&\r\n            currentTier && currentSubscription.price_id === currentTier.stripePriceId &&\r\n            tier.yearlyStripePriceId && (currentTier.name === tier.name ||\r\n              parseFloat(tier.price.slice(1)) >= parseFloat(currentTier.price.slice(1))) && (\r\n              <span className=\"bg-green-500/10 text-green-700 text-[10px] font-medium px-1.5 py-0.5 rounded-full\">\r\n                Recommended\r\n              </span>\r\n            )}\r\n          {isAuthenticated && statusBadge}\r\n        </p>\r\n        <div className=\"flex items-baseline mt-2\">\r\n          {billingPeriod === 'yearly' && tier.yearlyPrice && displayPrice !== '$0' ? (\r\n            <div className=\"flex flex-col\">\r\n              <div className=\"flex items-baseline gap-2\">\r\n                <PriceDisplay price={`$${Math.round(parseFloat(tier.yearlyPrice.slice(1)) / 12)}`} isCompact={insideDialog} />\r\n                {tier.discountPercentage && (\r\n                  <span className=\"text-xs line-through text-muted-foreground\">\r\n                    ${Math.round(parseFloat(tier.originalYearlyPrice?.slice(1) || '0') / 12)}\r\n                  </span>\r\n                )}\r\n              </div>\r\n              <div className=\"flex items-center gap-2 mt-1\">\r\n                <span className=\"text-xs text-muted-foreground\">/month</span>\r\n                <span className=\"text-xs text-muted-foreground\">billed yearly</span>\r\n              </div>\r\n            </div>\r\n          ) : (\r\n            <div className=\"flex items-baseline\">\r\n              <PriceDisplay price={displayPrice} isCompact={insideDialog} />\r\n              <span className=\"ml-2\">{displayPrice !== '$0' ? '/month' : ''}</span>\r\n            </div>\r\n          )}\r\n        </div>\r\n        <p className=\"hidden text-sm mt-2\">{tier.description}</p>\r\n\r\n        {billingPeriod === 'yearly' && tier.yearlyPrice && tier.discountPercentage ? (\r\n          <div className=\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold bg-green-50 border-green-200 text-green-700 w-fit\">\r\n            Save ${Math.round(parseFloat(tier.originalYearlyPrice?.slice(1) || '0') - parseFloat(tier.yearlyPrice.slice(1)))} per year\r\n          </div>\r\n        ) : (\r\n          <div className=\"hidden items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold bg-primary/10 border-primary/20 text-primary w-fit\">\r\n            {billingPeriod === 'yearly' && tier.yearlyPrice && displayPrice !== '$0'\r\n              ? `$${Math.round(parseFloat(tier.yearlyPrice.slice(1)) / 12)}/month (billed yearly)`\r\n              : `${displayPrice}/month`\r\n            }\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div className={cn(\r\n        \"flex-grow\",\r\n        insideDialog ? \"px-3 pb-2\" : \"px-4 pb-3\"\r\n      )}>\r\n        {tier.features && tier.features.length > 0 && (\r\n          <ul className=\"space-y-3\">\r\n            {tier.features.map((feature) => (\r\n              <li key={feature} className=\"flex items-center gap-2\">\r\n                <div className=\"size-5 min-w-5 rounded-full border border-primary/20 flex items-center justify-center\">\r\n                  <CheckIcon className=\"size-3 text-primary\" />\r\n                </div>\r\n                <span className=\"text-sm\">{feature}</span>\r\n              </li>\r\n            ))}\r\n          </ul>\r\n        )}\r\n      </div>\r\n\r\n      <div className={cn(\r\n        \"mt-auto\",\r\n        insideDialog ? \"px-3 pt-1 pb-3\" : \"px-4 pt-2 pb-4\"\r\n      )}>\r\n        <Button\r\n          onClick={() => handleSubscribe(tierPriceId)}\r\n          disabled={buttonDisabled}\r\n          variant={buttonVariant || 'default'}\r\n          className={cn(\r\n            'w-full font-medium transition-all duration-200',\r\n            isCompact || insideDialog ? 'h-8 rounded-md text-xs' : 'h-10 rounded-full text-sm',\r\n            buttonClassName,\r\n            isPlanLoading && 'animate-pulse',\r\n          )}\r\n        >\r\n          {buttonText}\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\ninterface PricingSectionProps {\r\n  returnUrl?: string;\r\n  showTitleAndTabs?: boolean;\r\n  hideFree?: boolean;\r\n  insideDialog?: boolean;\r\n}\r\n\r\nexport function PricingSection({\r\n  returnUrl = typeof window !== 'undefined' ? window.location.href : '/',\r\n  showTitleAndTabs = true,\r\n  hideFree = false,\r\n  insideDialog = false\r\n}: PricingSectionProps) {\r\n  const [deploymentType, setDeploymentType] = useState<'cloud' | 'self-hosted'>(\r\n    'cloud',\r\n  );\r\n  const { data: subscriptionData, isLoading: isFetchingPlan, error: subscriptionQueryError, refetch: refetchSubscription } = useSubscription();\r\n\r\n  // Derive authentication and subscription status from the hook data\r\n  const isAuthenticated = !!subscriptionData && subscriptionQueryError === null;\r\n  const currentSubscription = subscriptionData || null;\r\n\r\n  // Determine default billing period based on user's current subscription\r\n  const getDefaultBillingPeriod = (): 'monthly' | 'yearly' => {\r\n    if (!isAuthenticated || !currentSubscription) {\r\n      // Default to yearly for non-authenticated users or users without subscription\r\n      return 'yearly';\r\n    }\r\n\r\n    // Find current tier to determine if user is on monthly or yearly plan\r\n    const currentTier = siteConfig.cloudPricingItems.find(\r\n      (p) => p.stripePriceId === currentSubscription.price_id || p.yearlyStripePriceId === currentSubscription.price_id,\r\n    );\r\n\r\n    if (currentTier) {\r\n      // Check if current subscription is yearly\r\n      if (currentTier.yearlyStripePriceId === currentSubscription.price_id) {\r\n        return 'yearly';\r\n      } else if (currentTier.stripePriceId === currentSubscription.price_id) {\r\n        return 'monthly';\r\n      }\r\n    }\r\n\r\n    // Default to yearly if we can't determine current plan type\r\n    return 'yearly';\r\n  };\r\n\r\n  const [billingPeriod, setBillingPeriod] = useState<'monthly' | 'yearly'>(getDefaultBillingPeriod());\r\n  const [planLoadingStates, setPlanLoadingStates] = useState<Record<string, boolean>>({});\r\n\r\n  // Update billing period when subscription data changes\r\n  useEffect(() => {\r\n    setBillingPeriod(getDefaultBillingPeriod());\r\n  }, [isAuthenticated, currentSubscription?.price_id]);\r\n\r\n  const handlePlanSelect = (planId: string) => {\r\n    setPlanLoadingStates((prev) => ({ ...prev, [planId]: true }));\r\n  };\r\n\r\n  const handleSubscriptionUpdate = () => {\r\n    refetchSubscription();\r\n    // The useSubscription hook will automatically refetch, so we just need to clear loading states\r\n    setTimeout(() => {\r\n      setPlanLoadingStates({});\r\n    }, 1000);\r\n  };\r\n\r\n  const handleTabChange = (tab: 'cloud' | 'self-hosted') => {\r\n    if (tab === 'self-hosted') {\r\n      const openSourceSection = document.getElementById('open-source');\r\n      if (openSourceSection) {\r\n        const rect = openSourceSection.getBoundingClientRect();\r\n        const scrollTop =\r\n          window.pageYOffset || document.documentElement.scrollTop;\r\n        const offsetPosition = scrollTop + rect.top - 100;\r\n\r\n        window.scrollTo({\r\n          top: offsetPosition,\r\n          behavior: 'smooth',\r\n        });\r\n      }\r\n    } else {\r\n      setDeploymentType(tab);\r\n    }\r\n  };\r\n\r\n  if (isLocalMode()) {\r\n    return (\r\n      <div className=\"p-4 bg-muted/30 border border-border rounded-lg text-center\">\r\n        <p className=\"text-sm text-muted-foreground\">\r\n          Running in local development mode - billing features are disabled\r\n        </p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <section\r\n      id=\"pricing\"\r\n      className={cn(\"flex flex-col items-center justify-center gap-10 w-full relative pb-12\")}\r\n    >\r\n      {showTitleAndTabs && (\r\n        <>\r\n          <SectionHeader>\r\n            <h2 className=\"text-3xl md:text-4xl font-medium tracking-tighter text-center text-balance\">\r\n              Choose the right plan for your needs\r\n            </h2>\r\n            <p className=\"text-muted-foreground text-center text-balance font-medium\">\r\n              Start with our free plan or upgrade for more AI token credits\r\n            </p>\r\n          </SectionHeader>\r\n          <div className=\"relative w-full h-full\">\r\n            <div className=\"absolute -top-14 left-1/2 -translate-x-1/2\">\r\n              <PricingTabs\r\n                activeTab={deploymentType}\r\n                setActiveTab={handleTabChange}\r\n                className=\"mx-auto\"\r\n              />\r\n            </div>\r\n          </div>\r\n        </>\r\n      )}\r\n\r\n      {deploymentType === 'cloud' && (\r\n        <BillingPeriodToggle\r\n          billingPeriod={billingPeriod}\r\n          setBillingPeriod={setBillingPeriod}\r\n        />\r\n      )}\r\n\r\n      {deploymentType === 'cloud' && (\r\n        <div className={cn(\r\n          \"grid gap-4 w-full mx-auto\",\r\n          {\r\n            \"px-6 max-w-7xl\": !insideDialog,\r\n            \"max-w-7xl\": insideDialog\r\n          },\r\n          insideDialog\r\n            ? \"grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 2xl:grid-cols-4\"\r\n            : \"min-[650px]:grid-cols-2 lg:grid-cols-4\",\r\n          !insideDialog && \"grid-rows-1 items-stretch\"\r\n        )}>\r\n          {siteConfig.cloudPricingItems\r\n            .filter((tier) => !tier.hidden && (!hideFree || tier.price !== '$0'))\r\n            .map((tier) => (\r\n              <PricingTier\r\n                key={tier.name}\r\n                tier={tier}\r\n                currentSubscription={currentSubscription}\r\n                isLoading={planLoadingStates}\r\n                isFetchingPlan={isFetchingPlan}\r\n                onPlanSelect={handlePlanSelect}\r\n                onSubscriptionUpdate={handleSubscriptionUpdate}\r\n                isAuthenticated={isAuthenticated}\r\n                returnUrl={returnUrl}\r\n                insideDialog={insideDialog}\r\n                billingPeriod={billingPeriod}\r\n              />\r\n            ))}\r\n        </div>\r\n      )}\r\n       <div className=\"mt-4 p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg max-w-2xl mx-auto\">\r\n                <p className=\"text-sm text-blue-800 dark:text-blue-200 text-center\">\r\n                  <strong>What are AI tokens?</strong> Tokens are units of text that AI models process. \r\n                  Your plan includes credits to spend on various AI models - the more complex the task, \r\n                  the more tokens used.\r\n                </p>\r\n              </div>\r\n\r\n    </section>\r\n                 \r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAKA;AACA;AACA;AAAA;;;AAjBA;;;;;;;;;;;;AAoBO,MAAM,qBAAqB;IAChC,MAAM;IACN,KAAK;IACL,YAAY;AACd;AAqCA,aAAa;AACb,SAAS,YAAY,EAAE,SAAS,EAAE,YAAY,EAAE,SAAS,EAAoB;IAC3E,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oHACA;kBAGD;YAAC;YAAS;SAAc,CAAC,GAAG,CAAC,CAAC,oBAC7B,6LAAC;gBAEC,SAAS,IAAM,aAAa;gBAC5B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2EACA;oBACE,OAAO,cAAc;gBACvB;;oBAGD,cAAc,qBACb,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAS;wBACT,WAAU;wBACV,YAAY;4BACV,UAAU;4BACV,MAAM;4BACN,WAAW;4BACX,SAAS;4BACT,UAAU;wBACZ;;;;;;kCAGJ,6LAAC;wBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA,cAAc,MAAM,iBAAiB;kCAGtC,QAAQ,UAAU,UAAU;;;;;;;eA5B1B;;;;;;;;;;AAkCf;KA5CS;AA8CT,SAAS,aAAa,EAAE,KAAK,EAAE,SAAS,EAAqB;IAC3D,qBACE,6LAAC,qNAAA,CAAA,SAAM,CAAC,IAAI;QAEV,WAAW,YAAY,0BAA0B;QACjD,SAAS;YACP,SAAS;YACT,GAAG;YACH,QAAQ;QACV;QACA,SAAS;YAAE,SAAS;YAAG,GAAG;YAAG,QAAQ;QAAY;QACjD,YAAY;YAAE,UAAU;YAAM,MAAM;gBAAC;gBAAK;gBAAG;gBAAK;aAAE;QAAC;kBAEpD;OAVI;;;;;AAaX;MAhBS;AAkBT,SAAS,oBAAoB,EAC3B,aAAa,EACb,gBAAgB,EAIjB;IACC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,WAAU;YACV,SAAS,IAAM,iBAAiB,kBAAkB,YAAY,WAAW;sBAEzE,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0EACjB,kBAAkB,YACd,4CACA;kCACH;;;;;;kCAGH,6LAAC;wBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kGACjB,kBAAkB,WACd,4CACA;;4BACH;0CAED,6LAAC;gCAAK,WAAU;0CAAyI;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrK;MAnCS;AAqCT,SAAS,YAAY,EACnB,IAAI,EACJ,YAAY,KAAK,EACjB,mBAAmB,EACnB,SAAS,EACT,cAAc,EACd,YAAY,EACZ,YAAY,EACZ,oBAAoB,EACpB,kBAAkB,KAAK,EACvB,SAAS,EACT,eAAe,KAAK,EACpB,gBAAgB,SAAS,EACR;IACjB,2FAA2F;IAC3F,MAAM,kBAAkB,OAAO;QAC7B,IAAI,CAAC,iBAAiB;YACpB,OAAO,QAAQ,CAAC,IAAI,GAAG;YACvB;QACF;QAEA,IAAI,SAAS,CAAC,kBAAkB,EAAE;YAChC;QACF;QAEA,IAAI;YACF,eAAe;YAEf,MAAM,WACJ,MAAM,CAAA,GAAA,oHAAA,CAAA,wBAAqB,AAAD,EAAE;gBAC1B,UAAU;gBACV,aAAa;gBACb,YAAY;YACd;YAEF,QAAQ,GAAG,CAAC,iCAAiC;YAE7C,OAAQ,SAAS,MAAM;gBACrB,KAAK;gBACL,KAAK;oBACH,IAAI,SAAS,GAAG,EAAE;wBAChB,OAAO,QAAQ,CAAC,IAAI,GAAG,SAAS,GAAG;oBACrC,OAAO;wBACL,QAAQ,KAAK,CACX;wBAEF,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACd;oBACA;gBACF,KAAK;gBACL,KAAK;oBACH,MAAM,iBAAiB,SAAS,OAAO,EAAE,aACrC,CAAC,4BAA4B,EAAE,SAAS,OAAO,CAAC,aAAa,CAAC,KAAK,EAAE,SAAS,OAAO,CAAC,SAAS,EAAE,GACjG;oBACJ,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,IAAI,sBAAsB;oBAC1B;gBACF,KAAK;gBACL,KAAK;oBACH,MAAM,gBAAgB,SAAS,cAAc,GACzC,IAAI,KAAK,SAAS,cAAc,EAAE,kBAAkB,KACpD;oBAEJ,MAAM,sBAAsB;oBAE5B,2IAAA,CAAA,QAAK,CAAC,OAAO,eACX,6LAAC;;0CACC,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;gCAAE,WAAU;;oCAAe;oCACA;oCAAc;;;;;;;;;;;;;oBAI9C,IAAI,sBAAsB;oBAC1B;gBACF,KAAK;oBACH,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC,SAAS,OAAO,IAAI;oBAC/B;gBACF;oBACE,QAAQ,IAAI,CACV,0DACA,SAAS,MAAM;oBAEjB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YAChB;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM,eACJ,OAAO,UAAU,MAAM,UACvB,OAAO,WACP;YACF,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,MAAM,cAAc,kBAAkB,YAAY,KAAK,mBAAmB,GACtE,KAAK,mBAAmB,GACxB,KAAK,aAAa;IACtB,MAAM,eAAe,kBAAkB,YAAY,KAAK,WAAW,GAC/D,KAAK,WAAW,GAChB,KAAK,KAAK;IAEd,mEAAmE;IACnE,MAAM,cAAc,sHAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC,IAAI,CACnD,CAAC,IAAM,EAAE,aAAa,KAAK,qBAAqB,YAAY,EAAE,mBAAmB,KAAK,qBAAqB;IAG7G,MAAM,sBACJ,mBAAmB,qBAAqB,aAAa;IACvD,MAAM,cAAc,mBAAmB,qBAAqB;IAC5D,MAAM,wBACJ,eAAe,qBAAqB,uBAAuB;IAC7D,MAAM,gBAAgB,SAAS,CAAC,YAAY;IAE5C,IAAI,aAAa,kBAAkB,gBAAgB;IACnD,IAAI,iBAAiB;IACrB,IAAI,gBAA+B;IACnC,IAAI,YAAY;IAChB,IAAI,cAAc;IAClB,IAAI,kBAAkB;IAEtB,IAAI,iBAAiB;QACnB,IAAI,qBAAqB;YACvB,aAAa;YACb,iBAAiB;YACjB,gBAAgB;YAChB,YAAY,YAAY,wBAAwB;YAChD,kBAAkB;YAClB,4BACE,6LAAC;gBAAK,WAAU;0BAAgF;;;;;;QAIpG,OAAO,IAAI,uBAAuB;YAChC,aAAa;YACb,iBAAiB;YACjB,gBAAgB;YAChB,YAAY,YACR,2BACA;YACJ,kBACE;YACF,4BACE,6LAAC;gBAAK,WAAU;0BAAsF;;;;;;QAI1G,OAAO,IAAI,eAAe,qBAAqB,aAAa,aAAa;YACvE,aAAa;YACb,gBAAgB;YAChB,YAAY,YAAY,wBAAwB;YAChD,kBAAkB;YAClB,4BACE,6LAAC;gBAAK,WAAU;0BAAsF;;;;;;QAI1G,OAAO;YACL,MAAM,qBAAqB,sBACvB,aAAa,SAAS,OACtB;YACJ,MAAM,sBAAsB,KAAK,KAAK;YACtC,MAAM,gBACJ,uBAAuB,OACnB,IACA,WAAW,mBAAmB,OAAO,CAAC,WAAW,OAAO,OAAO;YACrE,MAAM,eACJ,wBAAwB,OACpB,IACA,WAAW,oBAAoB,OAAO,CAAC,WAAW,OAAO,OAAO;YAEtE,8EAA8E;YAC9E,MAAM,mBAAmB,eAAe,qBAAqB,aAAa,YAAY,aAAa;YACnG,MAAM,kBAAkB,eAAe,qBAAqB,aAAa,YAAY,mBAAmB;YACxG,MAAM,kBAAkB,KAAK,aAAa,KAAK;YAC/C,MAAM,iBAAiB,KAAK,mBAAmB,KAAK;YACpD,MAAM,6BAA6B,eAAe,YAAY,IAAI,KAAK,KAAK,IAAI,IAC9E,CAAC,AAAC,oBAAoB,kBAAoB,mBAAmB,eAAgB;YAE/E,IACE,kBAAkB,KAClB,iBAAiB,KACjB,qBAAqB,WAAW,mBAChC;gBACA,aAAa;gBACb,iBAAiB;gBACjB,gBAAgB;gBAChB,kBAAkB;YACpB,OAAO;gBACL,IAAI,eAAe,iBAAkB,oBAAoB,kBAAkB,gBAAgB,eAAgB;oBACzG,oFAAoF;oBACpF,yEAAyE;oBACzE,IAAI,mBAAmB,iBAAiB;wBACtC,aAAa;wBACb,iBAAiB;wBACjB,gBAAgB;wBAChB,kBACE;oBACJ,OAAO,IAAI,oBAAoB,kBAAkB,iBAAiB,eAAe;wBAC/E,aAAa;wBACb,gBAAgB;wBAChB,kBAAkB;oBACpB,OAAO;wBACL,aAAa;wBACb,gBAAgB,KAAK,WAAW;wBAChC,kBAAkB;oBACpB;gBACF,OAAO,IAAI,eAAe,iBAAiB,CAAC,CAAC,mBAAmB,mBAAmB,iBAAiB,aAAa,GAAG;oBAClH,aAAa;oBACb,iBAAiB;oBACjB,gBAAgB;oBAChB,kBACE;gBACJ,OAAO,IAAI,4BAA4B;oBACrC,2DAA2D;oBAC3D,IAAI,oBAAoB,gBAAgB;wBACtC,aAAa;wBACb,gBAAgB;wBAChB,kBAAkB;oBACpB,OAAO,IAAI,mBAAmB,iBAAiB;wBAC7C,2CAA2C;wBAC3C,aAAa;wBACb,iBAAiB;wBACjB,gBAAgB;wBAChB,kBACE;oBACJ,OAAO;wBACL,aAAa;wBACb,gBAAgB,KAAK,WAAW;wBAChC,kBACE;oBACJ;gBACF,OAAO;oBACL,aAAa;oBACb,gBAAgB,KAAK,WAAW;oBAChC,kBACE;gBACJ;YACF;QACF;QAEA,IAAI,eAAe;YACjB,aAAa;YACb,kBAAkB;QACpB;IACF,OAAO;QACL,kCAAkC;QAClC,gBAAgB,KAAK,WAAW;QAChC,kBACE,KAAK,WAAW,KAAK,YACjB,8CACA;IACR;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qCACA,eACI,kBACA,wBACJ,KAAK,SAAS,IAAI,CAAC,eACf,0MACA,8DACJ,CAAC,gBAAgB;;0BAGnB,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,uBACA,eAAe,QAAQ;;kCAEvB,6LAAC;wBAAE,WAAU;;4BACV,KAAK,IAAI;4BACT,KAAK,SAAS,kBACb,6LAAC;gCAAK,WAAU;0CAAkY;;;;;;4BAKnZ,CAAC,KAAK,SAAS,IAAI,mBAAmB,uBAAuB,kBAAkB,YAC9E,eAAe,oBAAoB,QAAQ,KAAK,YAAY,aAAa,IACzE,KAAK,mBAAmB,IAAI,CAAC,YAAY,IAAI,KAAK,KAAK,IAAI,IACzD,WAAW,KAAK,KAAK,CAAC,KAAK,CAAC,OAAO,WAAW,YAAY,KAAK,CAAC,KAAK,CAAC,GAAG,mBACzE,6LAAC;gCAAK,WAAU;0CAAoF;;;;;;4BAIvG,mBAAmB;;;;;;;kCAEtB,6LAAC;wBAAI,WAAU;kCACZ,kBAAkB,YAAY,KAAK,WAAW,IAAI,iBAAiB,qBAClE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAa,OAAO,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,WAAW,KAAK,WAAW,CAAC,KAAK,CAAC,MAAM,KAAK;4CAAE,WAAW;;;;;;wCAC7F,KAAK,kBAAkB,kBACtB,6LAAC;4CAAK,WAAU;;gDAA6C;gDACzD,KAAK,KAAK,CAAC,WAAW,KAAK,mBAAmB,EAAE,MAAM,MAAM,OAAO;;;;;;;;;;;;;8CAI3E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAgC;;;;;;sDAChD,6LAAC;4CAAK,WAAU;sDAAgC;;;;;;;;;;;;;;;;;iDAIpD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAa,OAAO;oCAAc,WAAW;;;;;;8CAC9C,6LAAC;oCAAK,WAAU;8CAAQ,iBAAiB,OAAO,WAAW;;;;;;;;;;;;;;;;;kCAIjE,6LAAC;wBAAE,WAAU;kCAAuB,KAAK,WAAW;;;;;;oBAEnD,kBAAkB,YAAY,KAAK,WAAW,IAAI,KAAK,kBAAkB,iBACxE,6LAAC;wBAAI,WAAU;;4BAAqI;4BAC3I,KAAK,KAAK,CAAC,WAAW,KAAK,mBAAmB,EAAE,MAAM,MAAM,OAAO,WAAW,KAAK,WAAW,CAAC,KAAK,CAAC;4BAAK;;;;;;6CAGnH,6LAAC;wBAAI,WAAU;kCACZ,kBAAkB,YAAY,KAAK,WAAW,IAAI,iBAAiB,OAChE,CAAC,CAAC,EAAE,KAAK,KAAK,CAAC,WAAW,KAAK,WAAW,CAAC,KAAK,CAAC,MAAM,IAAI,sBAAsB,CAAC,GAClF,GAAG,aAAa,MAAM,CAAC;;;;;;;;;;;;0BAMjC,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,aACA,eAAe,cAAc;0BAE5B,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG,mBACvC,6LAAC;oBAAG,WAAU;8BACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAClB,6LAAC;4BAAiB,WAAU;;8CAC1B,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAEvB,6LAAC;oCAAK,WAAU;8CAAW;;;;;;;2BAJpB;;;;;;;;;;;;;;;0BAWjB,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,WACA,eAAe,mBAAmB;0BAElC,cAAA,6LAAC,qIAAA,CAAA,SAAM;oBACL,SAAS,IAAM,gBAAgB;oBAC/B,UAAU;oBACV,SAAS,iBAAiB;oBAC1B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kDACA,aAAa,eAAe,2BAA2B,6BACvD,iBACA,iBAAiB;8BAGlB;;;;;;;;;;;;;;;;;AAKX;MA9WS;AAuXF,SAAS,eAAe,EAC7B,YAAY,uCAAgC,OAAO,QAAQ,CAAC,IAAI,uCAAM,EACtE,mBAAmB,IAAI,EACvB,WAAW,KAAK,EAChB,eAAe,KAAK,EACA;;IACpB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACjD;IAEF,MAAM,EAAE,MAAM,gBAAgB,EAAE,WAAW,cAAc,EAAE,OAAO,sBAAsB,EAAE,SAAS,mBAAmB,EAAE,GAAG,CAAA,GAAA,0KAAA,CAAA,kBAAe,AAAD;IAEzI,mEAAmE;IACnE,MAAM,kBAAkB,CAAC,CAAC,oBAAoB,2BAA2B;IACzE,MAAM,sBAAsB,oBAAoB;IAEhD,wEAAwE;IACxE,MAAM,0BAA0B;QAC9B,IAAI,CAAC,mBAAmB,CAAC,qBAAqB;YAC5C,8EAA8E;YAC9E,OAAO;QACT;QAEA,sEAAsE;QACtE,MAAM,cAAc,sHAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC,IAAI,CACnD,CAAC,IAAM,EAAE,aAAa,KAAK,oBAAoB,QAAQ,IAAI,EAAE,mBAAmB,KAAK,oBAAoB,QAAQ;QAGnH,IAAI,aAAa;YACf,0CAA0C;YAC1C,IAAI,YAAY,mBAAmB,KAAK,oBAAoB,QAAQ,EAAE;gBACpE,OAAO;YACT,OAAO,IAAI,YAAY,aAAa,KAAK,oBAAoB,QAAQ,EAAE;gBACrE,OAAO;YACT;QACF;QAEA,4DAA4D;QAC5D,OAAO;IACT;IAEA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwB;IACzE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA2B,CAAC;IAErF,uDAAuD;IACvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,iBAAiB;QACnB;mCAAG;QAAC;QAAiB,qBAAqB;KAAS;IAEnD,MAAM,mBAAmB,CAAC;QACxB,qBAAqB,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,OAAO,EAAE;YAAK,CAAC;IAC7D;IAEA,MAAM,2BAA2B;QAC/B;QACA,+FAA+F;QAC/F,WAAW;YACT,qBAAqB,CAAC;QACxB,GAAG;IACL;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,QAAQ,eAAe;YACzB,MAAM,oBAAoB,SAAS,cAAc,CAAC;YAClD,IAAI,mBAAmB;gBACrB,MAAM,OAAO,kBAAkB,qBAAqB;gBACpD,MAAM,YACJ,OAAO,WAAW,IAAI,SAAS,eAAe,CAAC,SAAS;gBAC1D,MAAM,iBAAiB,YAAY,KAAK,GAAG,GAAG;gBAE9C,OAAO,QAAQ,CAAC;oBACd,KAAK;oBACL,UAAU;gBACZ;YACF;QACF,OAAO;YACL,kBAAkB;QACpB;IACF;IAEA,IAAI,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,KAAK;QACjB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAE,WAAU;0BAAgC;;;;;;;;;;;IAKnD;IAEA,qBACE,6LAAC;QACC,IAAG;QACH,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE;;YAEb,kCACC;;kCACE,6LAAC,kJAAA,CAAA,gBAAa;;0CACZ,6LAAC;gCAAG,WAAU;0CAA6E;;;;;;0CAG3F,6LAAC;gCAAE,WAAU;0CAA6D;;;;;;;;;;;;kCAI5E,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAW;gCACX,cAAc;gCACd,WAAU;;;;;;;;;;;;;;;;;;YAOnB,mBAAmB,yBAClB,6LAAC;gBACC,eAAe;gBACf,kBAAkB;;;;;;YAIrB,mBAAmB,yBAClB,6LAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,6BACA;oBACE,kBAAkB,CAAC;oBACnB,aAAa;gBACf,GACA,eACI,8DACA,0CACJ,CAAC,gBAAgB;0BAEhB,sHAAA,CAAA,aAAU,CAAC,iBAAiB,CAC1B,MAAM,CAAC,CAAC,OAAS,CAAC,KAAK,MAAM,IAAI,CAAC,CAAC,YAAY,KAAK,KAAK,KAAK,IAAI,GAClE,GAAG,CAAC,CAAC,qBACJ,6LAAC;wBAEC,MAAM;wBACN,qBAAqB;wBACrB,WAAW;wBACX,gBAAgB;wBAChB,cAAc;wBACd,sBAAsB;wBACtB,iBAAiB;wBACjB,WAAW;wBACX,cAAc;wBACd,eAAe;uBAVV,KAAK,IAAI;;;;;;;;;;0BAevB,6LAAC;gBAAI,WAAU;0BACN,cAAA,6LAAC;oBAAE,WAAU;;sCACX,6LAAC;sCAAO;;;;;;wBAA4B;;;;;;;;;;;;;;;;;;AAStD;GArKgB;;QAS6G,0KAAA,CAAA,kBAAe;;;MAT5H", "debugId": null}}, {"offset": {"line": 1261, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>/hero-section.tsx"], "sourcesContent": ["'use client';\r\nimport { HeroVideoSection } from '@/components/home/<USER>/hero-video-section';\r\nimport { siteConfig } from '@/lib/home';\r\nimport { ArrowRight, Github, X, AlertCircle, Square } from 'lucide-react';\r\nimport { FlickeringGrid } from '@/components/home/<USER>/flickering-grid';\r\nimport { useMediaQuery } from '@/hooks/use-media-query';\r\nimport { useState, useEffect, useRef, FormEvent } from 'react';\r\nimport { useScroll } from 'motion/react';\r\nimport Link from 'next/link';\r\nimport { useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/components/AuthProvider';\r\nimport {\r\n  BillingError,\r\n} from '@/lib/api';\r\nimport { useInitiateAgentMutation } from '@/hooks/react-query/dashboard/use-initiate-agent';\r\nimport { useThreadQuery } from '@/hooks/react-query/threads/use-threads';\r\nimport { generateThreadName } from '@/lib/actions/threads';\r\nimport GoogleSignIn from '@/components/GoogleSignIn';\r\nimport { useAgents } from '@/hooks/react-query/agents/use-agents';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogOverlay,\r\n} from '@/components/ui/dialog';\r\nimport { BillingErrorAlert } from '@/components/billing/usage-limit-alert';\r\nimport { useBillingError } from '@/hooks/useBillingError';\r\nimport { useAccounts } from '@/hooks/use-accounts';\r\nimport { isLocalMode, config } from '@/lib/config';\r\nimport { toast } from 'sonner';\r\nimport { useModal } from '@/hooks/use-modal-store';\r\nimport GitHubSignIn from '@/components/GithubSignIn';\r\nimport { ChatInput, ChatInputHandles } from '@/components/thread/chat-input/chat-input';\r\nimport { normalizeFilenameToNFC } from '@/lib/utils/unicode';\r\nimport { createQueryHook } from '@/hooks/use-query';\r\nimport { agentKeys } from '@/hooks/react-query/agents/keys';\r\nimport { getAgents } from '@/hooks/react-query/agents/utils';\r\n\r\n// Custom dialog overlay with blur effect\r\nconst BlurredDialogOverlay = () => (\r\n  <DialogOverlay className=\"bg-background/40 backdrop-blur-md\" />\r\n);\r\n\r\n// Constant for localStorage key to ensure consistency\r\nconst PENDING_PROMPT_KEY = 'pendingAgentPrompt';\r\n\r\nexport function HeroSection() {\r\n  const { hero } = siteConfig;\r\n  const tablet = useMediaQuery('(max-width: 1024px)');\r\n  const [mounted, setMounted] = useState(false);\r\n  const [isScrolling, setIsScrolling] = useState(false);\r\n  const [isSubmitting, setIsSubmitting] = useState(false);\r\n  const scrollTimeout = useRef<NodeJS.Timeout | null>(null);\r\n  const { scrollY } = useScroll();\r\n  const [inputValue, setInputValue] = useState('');\r\n  const [selectedAgentId, setSelectedAgentId] = useState<string | undefined>();\r\n  const router = useRouter();\r\n  const { user, isLoading } = useAuth();\r\n  const { billingError, handleBillingError, clearBillingError } =\r\n    useBillingError();\r\n  const { data: accounts } = useAccounts();\r\n  const personalAccount = accounts?.find((account) => account.personal_account);\r\n  const { onOpen } = useModal();\r\n  const initiateAgentMutation = useInitiateAgentMutation();\r\n  const [initiatedThreadId, setInitiatedThreadId] = useState<string | null>(null);\r\n  const threadQuery = useThreadQuery(initiatedThreadId || '');\r\n  const chatInputRef = useRef<ChatInputHandles>(null);\r\n\r\n  // Fetch agents for selection\r\n  const { data: agentsResponse } = createQueryHook(\r\n    agentKeys.list({\r\n      limit: 100,\r\n      sort_by: 'name',\r\n      sort_order: 'asc'\r\n    }),\r\n    () => getAgents({\r\n      limit: 100,\r\n      sort_by: 'name',\r\n      sort_order: 'asc'\r\n    }),\r\n    {\r\n      enabled: !!user && !isLoading,\r\n      staleTime: 5 * 60 * 1000,\r\n      gcTime: 10 * 60 * 1000,\r\n    }\r\n  )();\r\n\r\n  const agents = agentsResponse?.agents || [];\r\n\r\n  // Auth dialog state\r\n  const [authDialogOpen, setAuthDialogOpen] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  // Detect when scrolling is active to reduce animation complexity\r\n  useEffect(() => {\r\n    const unsubscribe = scrollY.on('change', () => {\r\n      setIsScrolling(true);\r\n\r\n      // Clear any existing timeout\r\n      if (scrollTimeout.current) {\r\n        clearTimeout(scrollTimeout.current);\r\n      }\r\n\r\n      // Set a new timeout\r\n      scrollTimeout.current = setTimeout(() => {\r\n        setIsScrolling(false);\r\n      }, 300); // Wait 300ms after scroll stops\r\n    });\r\n\r\n    return () => {\r\n      unsubscribe();\r\n      if (scrollTimeout.current) {\r\n        clearTimeout(scrollTimeout.current);\r\n      }\r\n    };\r\n  }, [scrollY]);\r\n\r\n  useEffect(() => {\r\n    if (authDialogOpen && inputValue.trim()) {\r\n      localStorage.setItem(PENDING_PROMPT_KEY, inputValue.trim());\r\n    }\r\n  }, [authDialogOpen, inputValue]);\r\n\r\n  useEffect(() => {\r\n    if (authDialogOpen && user && !isLoading) {\r\n      setAuthDialogOpen(false);\r\n      router.push('/dashboard');\r\n    }\r\n  }, [user, isLoading, authDialogOpen, router]);\r\n\r\n  useEffect(() => {\r\n    if (threadQuery.data && initiatedThreadId) {\r\n      const thread = threadQuery.data;\r\n      if (thread.project_id) {\r\n        router.push(`/projects/${thread.project_id}/thread/${initiatedThreadId}`);\r\n      } else {\r\n        router.push(`/agents/${initiatedThreadId}`);\r\n      }\r\n      setInitiatedThreadId(null);\r\n    }\r\n  }, [threadQuery.data, initiatedThreadId, router]);\r\n\r\n  // Handle ChatInput submission\r\n  const handleChatInputSubmit = async (\r\n    message: string,\r\n    options?: { model_name?: string; enable_thinking?: boolean }\r\n  ) => {\r\n    if ((!message.trim() && !chatInputRef.current?.getPendingFiles().length) || isSubmitting) return;\r\n\r\n    // If user is not logged in, save prompt and show auth dialog\r\n    if (!user && !isLoading) {\r\n      localStorage.setItem(PENDING_PROMPT_KEY, message.trim());\r\n      setAuthDialogOpen(true);\r\n      return;\r\n    }\r\n\r\n    // User is logged in, create the agent with files like dashboard does\r\n    setIsSubmitting(true);\r\n    try {\r\n      const files = chatInputRef.current?.getPendingFiles() || [];\r\n      localStorage.removeItem(PENDING_PROMPT_KEY);\r\n\r\n      const formData = new FormData();\r\n      formData.append('prompt', message);\r\n\r\n      // Add selected agent if one is chosen\r\n      if (selectedAgentId) {\r\n        formData.append('agent_id', selectedAgentId);\r\n      }\r\n\r\n      // Add files if any\r\n      files.forEach((file) => {\r\n        const normalizedName = normalizeFilenameToNFC(file.name);\r\n        formData.append('files', file, normalizedName);\r\n      });\r\n\r\n      if (options?.model_name) formData.append('model_name', options.model_name);\r\n      formData.append('enable_thinking', String(options?.enable_thinking ?? false));\r\n      formData.append('reasoning_effort', 'low');\r\n      formData.append('stream', 'true');\r\n      formData.append('enable_context_manager', 'false');\r\n\r\n      const result = await initiateAgentMutation.mutateAsync(formData);\r\n\r\n      if (result.thread_id) {\r\n        setInitiatedThreadId(result.thread_id);\r\n      } else {\r\n        throw new Error('Agent initiation did not return a thread_id.');\r\n      }\r\n\r\n      chatInputRef.current?.clearPendingFiles();\r\n      setInputValue('');\r\n    } catch (error: any) {\r\n      if (error instanceof BillingError) {\r\n        console.log('Billing error:', error.detail);\r\n        onOpen(\"paymentRequiredDialog\");\r\n      } else {\r\n        const isConnectionError =\r\n          error instanceof TypeError &&\r\n          error.message.includes('Failed to fetch');\r\n        if (!isLocalMode() || isConnectionError) {\r\n          toast.error(\r\n            error.message || 'Failed to create agent. Please try again.',\r\n          );\r\n        }\r\n      }\r\n    } finally {\r\n      setIsSubmitting(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <section id=\"hero\" className=\"w-full relative overflow-hidden\">\r\n      <div className=\"relative flex flex-col items-center w-full px-6\">\r\n        {/* Left side flickering grid with gradient fades */}\r\n        <div className=\"absolute left-0 top-0 h-[600px] md:h-[800px] w-1/3 -z-10 overflow-hidden\">\r\n          {/* Horizontal fade from left to right */}\r\n          <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-background z-10\" />\r\n\r\n          {/* Vertical fade from top */}\r\n          <div className=\"absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10\" />\r\n\r\n          {/* Vertical fade to bottom */}\r\n          <div className=\"absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10\" />\r\n\r\n          <FlickeringGrid\r\n            className=\"h-full w-full\"\r\n            squareSize={mounted && tablet ? 2 : 2.5}\r\n            gridGap={mounted && tablet ? 2 : 2.5}\r\n            color=\"var(--secondary)\"\r\n            maxOpacity={0.4}\r\n            flickerChance={isScrolling ? 0.01 : 0.03} // Low flickering when not scrolling\r\n          />\r\n        </div>\r\n\r\n        {/* Right side flickering grid with gradient fades */}\r\n        <div className=\"absolute right-0 top-0 h-[600px] md:h-[800px] w-1/3 -z-10 overflow-hidden\">\r\n          {/* Horizontal fade from right to left */}\r\n          <div className=\"absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-background z-10\" />\r\n\r\n          {/* Vertical fade from top */}\r\n          <div className=\"absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10\" />\r\n\r\n          {/* Vertical fade to bottom */}\r\n          <div className=\"absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10\" />\r\n\r\n          <FlickeringGrid\r\n            className=\"h-full w-full\"\r\n            squareSize={mounted && tablet ? 2 : 2.5}\r\n            gridGap={mounted && tablet ? 2 : 2.5}\r\n            color=\"var(--secondary)\"\r\n            maxOpacity={0.4}\r\n            flickerChance={isScrolling ? 0.01 : 0.03} // Low flickering when not scrolling\r\n          />\r\n        </div>\r\n\r\n        {/* Center content background with rounded bottom */}\r\n        <div className=\"absolute inset-x-1/4 top-0 h-[600px] md:h-[800px] -z-20 bg-background rounded-b-xl\"></div>\r\n\r\n        <div className=\"relative z-10 pt-32 max-w-3xl mx-auto h-full w-full flex flex-col gap-10 items-center justify-center\">\r\n          {/* <p className=\"border border-border bg-accent rounded-full text-sm h-8 px-3 flex items-center gap-2\">\r\n            {hero.badgeIcon}\r\n            {hero.badge}\r\n          </p> */}\r\n\r\n          {/* <Link\r\n            href={hero.githubUrl}\r\n            target=\"_blank\"\r\n            rel=\"noopener noreferrer\"\r\n            className=\"group border border-border/50 bg-background hover:bg-accent/20 hover:border-secondary/40 rounded-full text-sm h-8 px-3 flex items-center gap-2 transition-all duration-300 shadow-sm hover:shadow-md hover:scale-105 hover:-translate-y-0.5\"\r\n          >\r\n            {hero.badgeIcon}\r\n            <span className=\"font-medium text-muted-foreground text-xs tracking-wide group-hover:text-primary transition-colors duration-300\">\r\n              {hero.badge}\r\n            </span>\r\n            <span className=\"inline-flex items-center justify-center size-3.5 rounded-full bg-muted/30 group-hover:bg-secondary/30 transition-colors duration-300\">\r\n              <svg\r\n                width=\"8\"\r\n                height=\"8\"\r\n                viewBox=\"0 0 24 24\"\r\n                fill=\"none\"\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                className=\"text-muted-foreground group-hover:text-primary\"\r\n              >\r\n                <path\r\n                  d=\"M7 17L17 7M17 7H8M17 7V16\"\r\n                  stroke=\"currentColor\"\r\n                  strokeWidth=\"2\"\r\n                  strokeLinecap=\"round\"\r\n                  strokeLinejoin=\"round\"\r\n                />\r\n              </svg>\r\n            </span>\r\n          </Link> */}\r\n          <div className=\"flex flex-col items-center justify-center gap-5 pt-16\">\r\n            <h1 className=\"text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-medium tracking-tighter text-balance text-center\">\r\n              <span className=\"text-secondary\">Suna</span>\r\n              <span className=\"text-primary\">, your AI Employee.</span>\r\n            </h1>\r\n            <p className=\"text-base md:text-lg text-center text-muted-foreground font-medium text-balance leading-relaxed tracking-tight\">\r\n              {hero.description}\r\n            </p>\r\n          </div>\r\n\r\n          <div className=\"flex items-center w-full max-w-4xl gap-2 flex-wrap justify-center\">\r\n            <div className=\"w-full relative\">\r\n              <div className=\"relative z-10\">\r\n                <ChatInput\r\n                  ref={chatInputRef}\r\n                  onSubmit={handleChatInputSubmit}\r\n                  placeholder=\"Describe what you need help with...\"\r\n                  loading={isSubmitting}\r\n                  disabled={isSubmitting}\r\n                  value={inputValue}\r\n                  onChange={setInputValue}\r\n                  isLoggedIn={!!user}\r\n                  selectedAgentId={selectedAgentId}\r\n                  onAgentSelect={setSelectedAgentId}\r\n                  autoFocus={false}\r\n                />\r\n              </div>\r\n              {/* Subtle glow effect */}\r\n              <div className=\"absolute -bottom-4 inset-x-0 h-6 bg-secondary/20 blur-xl rounded-full -z-10 opacity-70\"></div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"mb-16 sm:mt-52 max-w-4xl mx-auto\"></div>\r\n\r\n      {/* Auth Dialog */}\r\n      <Dialog open={authDialogOpen} onOpenChange={setAuthDialogOpen}>\r\n        <BlurredDialogOverlay />\r\n        <DialogContent className=\"sm:max-w-md rounded-xl bg-background border border-border\">\r\n          <DialogHeader>\r\n            <div className=\"flex items-center justify-between\">\r\n              <DialogTitle className=\"text-xl font-medium\">\r\n                Sign in to continue\r\n              </DialogTitle>\r\n              {/* <button \r\n                onClick={() => setAuthDialogOpen(false)}\r\n                className=\"rounded-full p-1 hover:bg-muted transition-colors\"\r\n              >\r\n                <X className=\"h-4 w-4 text-muted-foreground\" />\r\n              </button> */}\r\n            </div>\r\n            <DialogDescription className=\"text-muted-foreground\">\r\n              Sign in or create an account to talk with Suna\r\n            </DialogDescription>\r\n          </DialogHeader>\r\n\r\n\r\n\r\n          {/* OAuth Sign In */}\r\n          <div className=\"w-full\">\r\n            <GoogleSignIn returnUrl=\"/dashboard\" />\r\n            <GitHubSignIn returnUrl=\"/dashboard\" />\r\n          </div>\r\n\r\n          {/* Divider */}\r\n          <div className=\"relative my-6\">\r\n            <div className=\"absolute inset-0 flex items-center\">\r\n              <div className=\"w-full border-t border-border\"></div>\r\n            </div>\r\n            <div className=\"relative flex justify-center text-sm\">\r\n              <span className=\"px-2 bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] text-muted-foreground\">\r\n                or continue with email\r\n              </span>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Sign in options */}\r\n          <div className=\"space-y-4 pt-4\">\r\n            <Link\r\n              href={`/auth?returnUrl=${encodeURIComponent('/dashboard')}`}\r\n              className=\"flex h-12 items-center justify-center w-full text-center rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all shadow-md\"\r\n              onClick={() => setAuthDialogOpen(false)}\r\n            >\r\n              Sign in with email\r\n            </Link>\r\n\r\n            <Link\r\n              href={`/auth?mode=signup&returnUrl=${encodeURIComponent('/dashboard')}`}\r\n              className=\"flex h-12 items-center justify-center w-full text-center rounded-full border border-border bg-background hover:bg-accent/20 transition-all\"\r\n              onClick={() => setAuthDialogOpen(false)}\r\n            >\r\n              Create new account\r\n            </Link>\r\n          </div>\r\n\r\n          <div className=\"mt-4 text-center text-xs text-muted-foreground\">\r\n            By continuing, you agree to our{' '}\r\n            <Link href=\"/terms\" className=\"text-primary hover:underline\">\r\n              Terms of Service\r\n            </Link>{' '}\r\n            and{' '}\r\n            <Link href=\"/privacy\" className=\"text-primary hover:underline\">\r\n              Privacy Policy\r\n            </Link>\r\n          </div>\r\n        </DialogContent>\r\n      </Dialog>\r\n\r\n      {/* Add Billing Error Alert here */}\r\n      <BillingErrorAlert\r\n        message={billingError?.message}\r\n        currentUsage={billingError?.currentUsage}\r\n        limit={billingError?.limit}\r\n        accountId={personalAccount?.account_id}\r\n        onDismiss={clearBillingError}\r\n        isOpen={!!billingError}\r\n      />\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AAEA;AAEA;AAQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAtCA;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCA,yCAAyC;AACzC,MAAM,uBAAuB,kBAC3B,6LAAC,qIAAA,CAAA,gBAAa;QAAC,WAAU;;;;;;KADrB;AAIN,sDAAsD;AACtD,MAAM,qBAAqB;AAEpB,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,sHAAA,CAAA,aAAU;IAC3B,MAAM,SAAS,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD,EAAE;IAC7B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IACpD,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,oMAAA,CAAA,YAAS,AAAD;IAC5B,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACrD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAClC,MAAM,EAAE,YAAY,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,GAC3D,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAChB,MAAM,EAAE,MAAM,QAAQ,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IACrC,MAAM,kBAAkB,UAAU,KAAK,CAAC,UAAY,QAAQ,gBAAgB;IAC5E,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,WAAQ,AAAD;IAC1B,MAAM,wBAAwB,CAAA,GAAA,0KAAA,CAAA,2BAAwB,AAAD;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1E,MAAM,cAAc,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD,EAAE,qBAAqB;IACxD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,6BAA6B;IAC7B,MAAM,EAAE,MAAM,cAAc,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,kBAAe,AAAD,EAC7C,mJAAA,CAAA,YAAS,CAAC,IAAI,CAAC;QACb,OAAO;QACP,SAAS;QACT,YAAY;IACd,IACA,IAAM,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE;YACd,OAAO;YACP,SAAS;YACT,YAAY;QACd,IACA;QACE,SAAS,CAAC,CAAC,QAAQ,CAAC;QACpB,WAAW,IAAI,KAAK;QACpB,QAAQ,KAAK,KAAK;IACpB;IAGF,MAAM,SAAS,gBAAgB,UAAU,EAAE;IAE3C,oBAAoB;IACpB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,WAAW;QACb;gCAAG,EAAE;IAEL,iEAAiE;IACjE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,cAAc,QAAQ,EAAE,CAAC;qDAAU;oBACvC,eAAe;oBAEf,6BAA6B;oBAC7B,IAAI,cAAc,OAAO,EAAE;wBACzB,aAAa,cAAc,OAAO;oBACpC;oBAEA,oBAAoB;oBACpB,cAAc,OAAO,GAAG;6DAAW;4BACjC,eAAe;wBACjB;4DAAG,MAAM,gCAAgC;gBAC3C;;YAEA;yCAAO;oBACL;oBACA,IAAI,cAAc,OAAO,EAAE;wBACzB,aAAa,cAAc,OAAO;oBACpC;gBACF;;QACF;gCAAG;QAAC;KAAQ;IAEZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,kBAAkB,WAAW,IAAI,IAAI;gBACvC,aAAa,OAAO,CAAC,oBAAoB,WAAW,IAAI;YAC1D;QACF;gCAAG;QAAC;QAAgB;KAAW;IAE/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,kBAAkB,QAAQ,CAAC,WAAW;gBACxC,kBAAkB;gBAClB,OAAO,IAAI,CAAC;YACd;QACF;gCAAG;QAAC;QAAM;QAAW;QAAgB;KAAO;IAE5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,YAAY,IAAI,IAAI,mBAAmB;gBACzC,MAAM,SAAS,YAAY,IAAI;gBAC/B,IAAI,OAAO,UAAU,EAAE;oBACrB,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,OAAO,UAAU,CAAC,QAAQ,EAAE,mBAAmB;gBAC1E,OAAO;oBACL,OAAO,IAAI,CAAC,CAAC,QAAQ,EAAE,mBAAmB;gBAC5C;gBACA,qBAAqB;YACvB;QACF;gCAAG;QAAC,YAAY,IAAI;QAAE;QAAmB;KAAO;IAEhD,8BAA8B;IAC9B,MAAM,wBAAwB,OAC5B,SACA;QAEA,IAAI,AAAC,CAAC,QAAQ,IAAI,MAAM,CAAC,aAAa,OAAO,EAAE,kBAAkB,UAAW,cAAc;QAE1F,6DAA6D;QAC7D,IAAI,CAAC,QAAQ,CAAC,WAAW;YACvB,aAAa,OAAO,CAAC,oBAAoB,QAAQ,IAAI;YACrD,kBAAkB;YAClB;QACF;QAEA,qEAAqE;QACrE,gBAAgB;QAChB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,EAAE,qBAAqB,EAAE;YAC3D,aAAa,UAAU,CAAC;YAExB,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,UAAU;YAE1B,sCAAsC;YACtC,IAAI,iBAAiB;gBACnB,SAAS,MAAM,CAAC,YAAY;YAC9B;YAEA,mBAAmB;YACnB,MAAM,OAAO,CAAC,CAAC;gBACb,MAAM,iBAAiB,CAAA,GAAA,iIAAA,CAAA,yBAAsB,AAAD,EAAE,KAAK,IAAI;gBACvD,SAAS,MAAM,CAAC,SAAS,MAAM;YACjC;YAEA,IAAI,SAAS,YAAY,SAAS,MAAM,CAAC,cAAc,QAAQ,UAAU;YACzE,SAAS,MAAM,CAAC,mBAAmB,OAAO,SAAS,mBAAmB;YACtE,SAAS,MAAM,CAAC,oBAAoB;YACpC,SAAS,MAAM,CAAC,UAAU;YAC1B,SAAS,MAAM,CAAC,0BAA0B;YAE1C,MAAM,SAAS,MAAM,sBAAsB,WAAW,CAAC;YAEvD,IAAI,OAAO,SAAS,EAAE;gBACpB,qBAAqB,OAAO,SAAS;YACvC,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;YAEA,aAAa,OAAO,EAAE;YACtB,cAAc;QAChB,EAAE,OAAO,OAAY;YACnB,IAAI,iBAAiB,oHAAA,CAAA,eAAY,EAAE;gBACjC,QAAQ,GAAG,CAAC,kBAAkB,MAAM,MAAM;gBAC1C,OAAO;YACT,OAAO;gBACL,MAAM,oBACJ,iBAAiB,aACjB,MAAM,OAAO,CAAC,QAAQ,CAAC;gBACzB,IAAI,CAAC,CAAA,GAAA,uHAAA,CAAA,cAAW,AAAD,OAAO,mBAAmB;oBACvC,2IAAA,CAAA,QAAK,CAAC,KAAK,CACT,MAAM,OAAO,IAAI;gBAErB;YACF;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAO,WAAU;;0BAC3B,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;;;;;0CAGf,6LAAC;gCAAI,WAAU;;;;;;0CAGf,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC,yJAAA,CAAA,iBAAc;gCACb,WAAU;gCACV,YAAY,WAAW,SAAS,IAAI;gCACpC,SAAS,WAAW,SAAS,IAAI;gCACjC,OAAM;gCACN,YAAY;gCACZ,eAAe,cAAc,OAAO;;;;;;;;;;;;kCAKxC,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;;;;;0CAGf,6LAAC;gCAAI,WAAU;;;;;;0CAGf,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC,yJAAA,CAAA,iBAAc;gCACb,WAAU;gCACV,YAAY,WAAW,SAAS,IAAI;gCACpC,SAAS,WAAW,SAAS,IAAI;gCACjC,OAAM;gCACN,YAAY;gCACZ,eAAe,cAAc,OAAO;;;;;;;;;;;;kCAKxC,6LAAC;wBAAI,WAAU;;;;;;kCAEf,6LAAC;wBAAI,WAAU;;0CAmCb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAK,WAAU;0DAAiB;;;;;;0DACjC,6LAAC;gDAAK,WAAU;0DAAe;;;;;;;;;;;;kDAEjC,6LAAC;wCAAE,WAAU;kDACV,KAAK,WAAW;;;;;;;;;;;;0CAIrB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,iKAAA,CAAA,YAAS;gDACR,KAAK;gDACL,UAAU;gDACV,aAAY;gDACZ,SAAS;gDACT,UAAU;gDACV,OAAO;gDACP,UAAU;gDACV,YAAY,CAAC,CAAC;gDACd,iBAAiB;gDACjB,eAAe;gDACf,WAAW;;;;;;;;;;;sDAIf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAKvB,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAgB,cAAc;;kCAC1C,6LAAC;;;;;kCACD,6LAAC,qIAAA,CAAA,gBAAa;wBAAC,WAAU;;0CACvB,6LAAC,qIAAA,CAAA,eAAY;;kDACX,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qIAAA,CAAA,cAAW;4CAAC,WAAU;sDAAsB;;;;;;;;;;;kDAU/C,6LAAC,qIAAA,CAAA,oBAAiB;wCAAC,WAAU;kDAAwB;;;;;;;;;;;;0CAQvD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,UAAY;wCAAC,WAAU;;;;;;kDACxB,6LAAC,qIAAA,CAAA,UAAY;wCAAC,WAAU;;;;;;;;;;;;0CAI1B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;kDAEjB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAAmE;;;;;;;;;;;;;;;;;0CAOvF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,gBAAgB,EAAE,mBAAmB,eAAe;wCAC3D,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAClC;;;;;;kDAID,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,4BAA4B,EAAE,mBAAmB,eAAe;wCACvE,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAClC;;;;;;;;;;;;0CAKH,6LAAC;gCAAI,WAAU;;oCAAiD;oCAC9B;kDAChC,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAS,WAAU;kDAA+B;;;;;;oCAErD;oCAAI;oCACR;kDACJ,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAA+B;;;;;;;;;;;;;;;;;;;;;;;;0BAQrE,6LAAC,2JAAA,CAAA,oBAAiB;gBAChB,SAAS,cAAc;gBACvB,cAAc,cAAc;gBAC5B,OAAO,cAAc;gBACrB,WAAW,iBAAiB;gBAC5B,WAAW;gBACX,QAAQ,CAAC,CAAC;;;;;;;;;;;;AAIlB;GAlXgB;;QAEC,wIAAA,CAAA,gBAAa;QAKR,oMAAA,CAAA,YAAS;QAGd,qIAAA,CAAA,YAAS;QACI,qIAAA,CAAA,UAAO;QAEjC,kIAAA,CAAA,kBAAe;QACU,kIAAA,CAAA,cAAW;QAEnB,wIAAA,CAAA,WAAQ;QACG,0KAAA,CAAA,2BAAwB;QAElC,8JAAA,CAAA,iBAAc;;;MAnBpB", "debugId": null}}, {"offset": {"line": 1925, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>/open-source-section.tsx"], "sourcesContent": ["import { SectionHeader } from '@/components/home/<USER>';\r\nimport { siteConfig } from '@/lib/home';\r\nimport { Gith<PERSON> } from 'lucide-react';\r\nimport Link from 'next/link';\r\n\r\nexport function OpenSourceSection() {\r\n  return (\r\n    <section\r\n      id=\"open-source\"\r\n      className=\"flex flex-col items-center justify-center w-full relative pb-18\"\r\n    >\r\n      <div className=\"w-full max-w-6xl mx-auto px-6\">\r\n        <SectionHeader>\r\n          <h2 className=\"text-3xl md:text-4xl font-medium tracking-tighter text-center text-balance pb-1\">\r\n            100% Open Source\r\n          </h2>\r\n          <p className=\"text-muted-foreground text-center text-balance font-medium\">\r\n            Sun<PERSON> is fully open source. Join our community and help shape the\r\n            future of AI.\r\n          </p>\r\n        </SectionHeader>\r\n\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 pt-12\">\r\n          <div className=\"rounded-xl bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] border border-border p-6\">\r\n            <div className=\"flex flex-col gap-6\">\r\n              <div className=\"flex items-center gap-2 text-primary font-medium\">\r\n                <Github className=\"h-5 w-5\" />\r\n                <span>kortix-ai/suna</span>\r\n              </div>\r\n              <div className=\"relative\">\r\n                <h3 className=\"text-2xl font-semibold tracking-tight\">\r\n                  The Generalist AI Agent\r\n                </h3>\r\n                <p className=\"text-muted-foreground mt-2\">\r\n                  Explore, contribute, or fork our repository. Suna is built\r\n                  with transparency and collaboration at its core.\r\n                </p>\r\n              </div>\r\n              <div className=\"flex flex-wrap gap-2\">\r\n                <span className=\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold bg-secondary/10 border-secondary/20 text-secondary\">\r\n                  TypeScript\r\n                </span>\r\n                <span className=\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold bg-secondary/10 border-secondary/20 text-secondary\">\r\n                  Python\r\n                </span>\r\n                <span className=\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold bg-secondary/10 border-secondary/20 text-secondary\">\r\n                  Apache 2.0 License\r\n                </span>\r\n              </div>\r\n              <Link\r\n                href=\"https://github.com/Kortix-ai/Suna\"\r\n                target=\"_blank\"\r\n                rel=\"noopener noreferrer\"\r\n                className=\"group inline-flex h-10 items-center justify-center gap-2 text-sm font-medium tracking-wide rounded-full text-primary-foreground dark:text-black px-6 shadow-[inset_0_1px_2px_rgba(255,255,255,0.25),0_3px_3px_-1.5px_rgba(16,24,40,0.06),0_1px_1px_rgba(16,24,40,0.08)] bg-primary dark:bg-white hover:bg-primary/90 dark:hover:bg-white/90 transition-all duration-200 w-fit\"\r\n              >\r\n                <span>View on GitHub</span>\r\n                <span className=\"inline-flex items-center justify-center size-5 rounded-full bg-white/20 dark:bg-black/10 group-hover:bg-white/30 dark:group-hover:bg-black/20 transition-colors duration-200\">\r\n                  <svg\r\n                    width=\"12\"\r\n                    height=\"12\"\r\n                    viewBox=\"0 0 24 24\"\r\n                    fill=\"none\"\r\n                    xmlns=\"http://www.w3.org/2000/svg\"\r\n                    className=\"text-primary-foreground dark:text-black\"\r\n                  >\r\n                    <path\r\n                      d=\"M7 17L17 7M17 7H8M17 7V16\"\r\n                      stroke=\"currentColor\"\r\n                      strokeWidth=\"2\"\r\n                      strokeLinecap=\"round\"\r\n                      strokeLinejoin=\"round\"\r\n                    />\r\n                  </svg>\r\n                </span>\r\n              </Link>\r\n            </div>\r\n          </div>\r\n\r\n          <div className=\"rounded-xl bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] border border-border p-6\">\r\n            <div className=\"flex flex-col gap-6\">\r\n              <h3 className=\"text-xl md:text-2xl font-medium tracking-tight\">\r\n                Transparency & Trust\r\n              </h3>\r\n              <p className=\"text-muted-foreground\">\r\n                We believe AI should be open and accessible to everyone. Our\r\n                open source approach ensures accountability, innovation, and\r\n                community collaboration.\r\n              </p>\r\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\r\n                <div className=\"flex items-start gap-3\">\r\n                  <div className=\"rounded-full bg-secondary/10 p-2 mt-0.5\">\r\n                    <svg\r\n                      width=\"16\"\r\n                      height=\"16\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      fill=\"none\"\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      className=\"text-secondary\"\r\n                    >\r\n                      <path\r\n                        d=\"M9.75 12.75L11.25 14.25L14.25 9.75\"\r\n                        stroke=\"currentColor\"\r\n                        strokeWidth=\"1.5\"\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                      ></path>\r\n                      <path\r\n                        d=\"M4.75 12C4.75 7.99594 7.99594 4.75 12 4.75C16.0041 4.75 19.25 7.99594 19.25 12C19.25 16.0041 16.0041 19.25 12 19.25C7.99594 19.25 4.75 16.0041 4.75 12Z\"\r\n                        stroke=\"currentColor\"\r\n                        strokeWidth=\"1.5\"\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                      ></path>\r\n                    </svg>\r\n                  </div>\r\n                  <div>\r\n                    <h4 className=\"font-medium\">Transparency</h4>\r\n                    <p className=\"text-muted-foreground text-sm\">\r\n                      Fully auditable codebase\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-start gap-3\">\r\n                  <div className=\"rounded-full bg-secondary/10 p-2 mt-0.5\">\r\n                    <svg\r\n                      width=\"16\"\r\n                      height=\"16\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      fill=\"none\"\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      className=\"text-secondary\"\r\n                    >\r\n                      <path\r\n                        d=\"M9.75 12.75L11.25 14.25L14.25 9.75\"\r\n                        stroke=\"currentColor\"\r\n                        strokeWidth=\"1.5\"\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                      ></path>\r\n                      <path\r\n                        d=\"M4.75 12C4.75 7.99594 7.99594 4.75 12 4.75C16.0041 4.75 19.25 7.99594 19.25 12C19.25 16.0041 16.0041 19.25 12 19.25C7.99594 19.25 4.75 16.0041 4.75 12Z\"\r\n                        stroke=\"currentColor\"\r\n                        strokeWidth=\"1.5\"\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                      ></path>\r\n                    </svg>\r\n                  </div>\r\n                  <div>\r\n                    <h4 className=\"font-medium\">Community</h4>\r\n                    <p className=\"text-muted-foreground text-sm\">\r\n                      Join our developers\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n                <div className=\"flex items-start gap-3\">\r\n                  <div className=\"rounded-full bg-secondary/10 p-2 mt-0.5\">\r\n                    <svg\r\n                      width=\"16\"\r\n                      height=\"16\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      fill=\"none\"\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      className=\"text-secondary\"\r\n                    >\r\n                      <path\r\n                        d=\"M9.75 12.75L11.25 14.25L14.25 9.75\"\r\n                        stroke=\"currentColor\"\r\n                        strokeWidth=\"1.5\"\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                      ></path>\r\n                      <path\r\n                        d=\"M4.75 12C4.75 7.99594 7.99594 4.75 12 4.75C16.0041 4.75 19.25 7.99594 19.25 12C19.25 16.0041 16.0041 19.25 12 19.25C7.99594 19.25 4.75 16.0041 4.75 12Z\"\r\n                        stroke=\"currentColor\"\r\n                        strokeWidth=\"1.5\"\r\n                        strokeLinecap=\"round\"\r\n                        strokeLinejoin=\"round\"\r\n                      ></path>\r\n                    </svg>\r\n                  </div>\r\n                  <div>\r\n                    <h4 className=\"font-medium\">Apache 2.0</h4>\r\n                    <p className=\"text-muted-foreground text-sm\">\r\n                      Free to use and modify\r\n                    </p>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;;;;;AAEO,SAAS;IACd,qBACE,6LAAC;QACC,IAAG;QACH,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,kJAAA,CAAA,gBAAa;;sCACZ,6LAAC;4BAAG,WAAU;sCAAkF;;;;;;sCAGhG,6LAAC;4BAAE,WAAU;sCAA6D;;;;;;;;;;;;8BAM5E,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,yMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAwC;;;;;;0DAGtD,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;kDAK5C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAsI;;;;;;0DAGtJ,6LAAC;gDAAK,WAAU;0DAAsI;;;;;;0DAGtJ,6LAAC;gDAAK,WAAU;0DAAsI;;;;;;;;;;;;kDAIxJ,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;;0DAEV,6LAAC;0DAAK;;;;;;0DACN,6LAAC;gDAAK,WAAU;0DACd,cAAA,6LAAC;oDACC,OAAM;oDACN,QAAO;oDACP,SAAQ;oDACR,MAAK;oDACL,OAAM;oDACN,WAAU;8DAEV,cAAA,6LAAC;wDACC,GAAE;wDACF,QAAO;wDACP,aAAY;wDACZ,eAAc;wDACd,gBAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ3B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAiD;;;;;;kDAG/D,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;kDAKrC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,OAAM;4DACN,QAAO;4DACP,SAAQ;4DACR,MAAK;4DACL,OAAM;4DACN,WAAU;;8EAEV,6LAAC;oEACC,GAAE;oEACF,QAAO;oEACP,aAAY;oEACZ,eAAc;oEACd,gBAAe;;;;;;8EAEjB,6LAAC;oEACC,GAAE;oEACF,QAAO;oEACP,aAAY;oEACZ,eAAc;oEACd,gBAAe;;;;;;;;;;;;;;;;;kEAIrB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAc;;;;;;0EAC5B,6LAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;0DAKjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,OAAM;4DACN,QAAO;4DACP,SAAQ;4DACR,MAAK;4DACL,OAAM;4DACN,WAAU;;8EAEV,6LAAC;oEACC,GAAE;oEACF,QAAO;oEACP,aAAY;oEACZ,eAAc;oEACd,gBAAe;;;;;;8EAEjB,6LAAC;oEACC,GAAE;oEACF,QAAO;oEACP,aAAY;oEACZ,eAAc;oEACd,gBAAe;;;;;;;;;;;;;;;;;kEAIrB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAc;;;;;;0EAC5B,6LAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;0DAKjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,OAAM;4DACN,QAAO;4DACP,SAAQ;4DACR,MAAK;4DACL,OAAM;4DACN,WAAU;;8EAEV,6LAAC;oEACC,GAAE;oEACF,QAAO;oEACP,aAAY;oEACZ,eAAc;oEACd,gBAAe;;;;;;8EAEjB,6LAAC;oEACC,GAAE;oEACF,QAAO;oEACP,aAAY;oEACZ,eAAc;oEACd,gBAAe;;;;;;;;;;;;;;;;;kEAIrB,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAc;;;;;;0EAC5B,6LAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYjE;KA9LgB", "debugId": null}}, {"offset": {"line": 2415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>/use-cases-section.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { SectionHeader } from '@/components/home/<USER>';\r\nimport { siteConfig } from '@/lib/home';\r\nimport { cn } from '@/lib/utils';\r\nimport { motion } from 'motion/react';\r\nimport { ArrowRight } from 'lucide-react';\r\n\r\ninterface UseCase {\r\n  id: string;\r\n  title: string;\r\n  description: string;\r\n  category: string;\r\n  featured: boolean;\r\n  icon: React.ReactNode;\r\n  image: string;\r\n  url: string;\r\n}\r\n\r\nexport function UseCasesSection() {\r\n  // Get featured use cases from siteConfig and limit to 8\r\n  const featuredUseCases: UseCase[] = (siteConfig.useCases || []).filter(\r\n    (useCase: UseCase) => useCase.featured,\r\n  );\r\n\r\n  return (\r\n    <section\r\n      id=\"use-cases\"\r\n      className=\"flex flex-col items-center justify-center gap-10 pb-10 w-full relative\"\r\n    >\r\n      <SectionHeader>\r\n        <h2 className=\"text-3xl md:text-4xl font-medium tracking-tighter text-center text-balance\">\r\n          See <PERSON><PERSON> in action\r\n        </h2>\r\n        <p className=\"text-muted-foreground text-center text-balance font-medium\">\r\n          Explore real-world examples of how <PERSON><PERSON> completes complex tasks\r\n          autonomously\r\n        </p>\r\n      </SectionHeader>\r\n\r\n      <div className=\"relative w-full h-full\">\r\n        <div className=\"grid min-[650px]:grid-cols-2 min-[900px]:grid-cols-3 min-[1200px]:grid-cols-4 gap-4 w-full max-w-6xl mx-auto px-6\">\r\n          {featuredUseCases.map((useCase: UseCase) => (\r\n            <div\r\n              key={useCase.id}\r\n              className=\"rounded-xl overflow-hidden relative h-fit min-[650px]:h-full flex flex-col md:shadow-[0px_61px_24px_-10px_rgba(0,0,0,0.01),0px_34px_20px_-8px_rgba(0,0,0,0.05),0px_15px_15px_-6px_rgba(0,0,0,0.09),0px_4px_8px_-2px_rgba(0,0,0,0.10),0px_0px_0px_1px_rgba(0,0,0,0.08)] bg-accent\"\r\n            >\r\n              <div className=\"flex flex-col gap-4 p-4\">\r\n                <div className=\"flex items-center gap-3\">\r\n                  <div className=\"rounded-full bg-secondary/10 p-2\">\r\n                    <svg\r\n                      width=\"16\"\r\n                      height=\"16\"\r\n                      viewBox=\"0 0 24 24\"\r\n                      fill=\"none\"\r\n                      xmlns=\"http://www.w3.org/2000/svg\"\r\n                      className=\"text-secondary\"\r\n                    >\r\n                      {useCase.icon}\r\n                    </svg>\r\n                  </div>\r\n                  <h3 className=\"text-lg font-medium line-clamp-1\">\r\n                    {useCase.title}\r\n                  </h3>\r\n                </div>\r\n                <p className=\"text-sm text-muted-foreground leading-relaxed line-clamp-3\">\r\n                  {useCase.description}\r\n                </p>\r\n              </div>\r\n\r\n              <div className=\"mt-auto\">\r\n                <hr className=\"border-border dark:border-white/20 m-0\" />\r\n\r\n                <div className=\"w-full h-[160px] bg-accent/10\">\r\n                  <div className=\"relative w-full h-full overflow-hidden\">\r\n                    <img\r\n                      src={\r\n                        useCase.image ||\r\n                        `https://placehold.co/800x400/f5f5f5/666666?text=Suna+${useCase.title.split(' ').join('+')}`\r\n                      }\r\n                      alt={`Suna ${useCase.title}`}\r\n                      className=\"w-full h-full object-cover\"\r\n                    />\r\n                    <a\r\n                      href={useCase.url}\r\n                      target=\"_blank\"\r\n                      rel=\"noopener noreferrer\"\r\n                      className=\"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 hover:opacity-100 transition-opacity flex items-end justify-start p-4 group\"\r\n                    >\r\n                      <span className=\"flex items-center gap-2 text-sm text-white font-medium\">\r\n                        Watch replay\r\n                        <ArrowRight className=\"size-4 transform group-hover:translate-x-1 transition-transform\" />\r\n                      </span>\r\n                    </a>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          ))}\r\n        </div>\r\n\r\n        {featuredUseCases.length === 0 && (\r\n          <div className=\"flex flex-col items-center justify-center py-16 text-center\">\r\n            <p className=\"text-muted-foreground\">No use cases available yet.</p>\r\n          </div>\r\n        )}\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAGA;AANA;;;;;AAmBO,SAAS;IACd,wDAAwD;IACxD,MAAM,mBAA8B,CAAC,sHAAA,CAAA,aAAU,CAAC,QAAQ,IAAI,EAAE,EAAE,MAAM,CACpE,CAAC,UAAqB,QAAQ,QAAQ;IAGxC,qBACE,6LAAC;QACC,IAAG;QACH,WAAU;;0BAEV,6LAAC,kJAAA,CAAA,gBAAa;;kCACZ,6LAAC;wBAAG,WAAU;kCAA6E;;;;;;kCAG3F,6LAAC;wBAAE,WAAU;kCAA6D;;;;;;;;;;;;0BAM5E,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,OAAM;4DACN,QAAO;4DACP,SAAQ;4DACR,MAAK;4DACL,OAAM;4DACN,WAAU;sEAET,QAAQ,IAAI;;;;;;;;;;;kEAGjB,6LAAC;wDAAG,WAAU;kEACX,QAAQ,KAAK;;;;;;;;;;;;0DAGlB,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;;;;;;;kDAIxB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;;;;;0DAEd,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,KACE,QAAQ,KAAK,IACb,CAAC,qDAAqD,EAAE,QAAQ,KAAK,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,MAAM;4DAE9F,KAAK,CAAC,KAAK,EAAE,QAAQ,KAAK,EAAE;4DAC5B,WAAU;;;;;;sEAEZ,6LAAC;4DACC,MAAM,QAAQ,GAAG;4DACjB,QAAO;4DACP,KAAI;4DACJ,WAAU;sEAEV,cAAA,6LAAC;gEAAK,WAAU;;oEAAyD;kFAEvE,6LAAC,qNAAA,CAAA,aAAU;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BA/C3B,QAAQ,EAAE;;;;;;;;;;oBAyDpB,iBAAiB,MAAM,KAAK,mBAC3B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAMjD;KA1FgB", "debugId": null}}, {"offset": {"line": 2639, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>/hero-video-dialog.tsx"], "sourcesContent": ["/* eslint-disable @next/next/no-img-element */\r\n'use client';\r\n\r\nimport { Play, XIcon } from 'lucide-react';\r\nimport { AnimatePresence, motion } from 'motion/react';\r\nimport { useState } from 'react';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\ntype AnimationStyle =\r\n  | 'from-bottom'\r\n  | 'from-center'\r\n  | 'from-top'\r\n  | 'from-left'\r\n  | 'from-right'\r\n  | 'fade'\r\n  | 'top-in-bottom-out'\r\n  | 'left-in-right-out';\r\n\r\ninterface HeroVideoProps {\r\n  animationStyle?: AnimationStyle;\r\n  videoSrc: string;\r\n  thumbnailSrc?: string;\r\n  thumbnailAlt?: string;\r\n  className?: string;\r\n}\r\n\r\nconst animationVariants = {\r\n  'from-bottom': {\r\n    initial: { y: '100%', opacity: 0 },\r\n    animate: { y: 0, opacity: 1 },\r\n    exit: { y: '100%', opacity: 0 },\r\n  },\r\n  'from-center': {\r\n    initial: { scale: 0.5, opacity: 0 },\r\n    animate: { scale: 1, opacity: 1 },\r\n    exit: { scale: 0.5, opacity: 0 },\r\n  },\r\n  'from-top': {\r\n    initial: { y: '-100%', opacity: 0 },\r\n    animate: { y: 0, opacity: 1 },\r\n    exit: { y: '-100%', opacity: 0 },\r\n  },\r\n  'from-left': {\r\n    initial: { x: '-100%', opacity: 0 },\r\n    animate: { x: 0, opacity: 1 },\r\n    exit: { x: '-100%', opacity: 0 },\r\n  },\r\n  'from-right': {\r\n    initial: { x: '100%', opacity: 0 },\r\n    animate: { x: 0, opacity: 1 },\r\n    exit: { x: '100%', opacity: 0 },\r\n  },\r\n  fade: {\r\n    initial: { opacity: 0 },\r\n    animate: { opacity: 1 },\r\n    exit: { opacity: 0 },\r\n  },\r\n  'top-in-bottom-out': {\r\n    initial: { y: '-100%', opacity: 0 },\r\n    animate: { y: 0, opacity: 1 },\r\n    exit: { y: '100%', opacity: 0 },\r\n  },\r\n  'left-in-right-out': {\r\n    initial: { x: '-100%', opacity: 0 },\r\n    animate: { x: 0, opacity: 1 },\r\n    exit: { x: '100%', opacity: 0 },\r\n  },\r\n};\r\n\r\nexport function HeroVideoDialog({\r\n  animationStyle = 'from-center',\r\n  videoSrc,\r\n  thumbnailSrc,\r\n  thumbnailAlt = 'Video thumbnail',\r\n  className,\r\n}: HeroVideoProps) {\r\n  const [isVideoOpen, setIsVideoOpen] = useState(false);\r\n  const selectedAnimation = animationVariants[animationStyle];\r\n\r\n  // Add autoplay parameter to YouTube URL when opened\r\n  const getVideoSrcWithAutoplay = () => {\r\n    const url = new URL(videoSrc);\r\n    // Preserve existing query parameters and add autoplay=1\r\n    url.searchParams.set('autoplay', '1');\r\n    return url.toString();\r\n  };\r\n\r\n  return (\r\n    <div className={cn('relative', className)}>\r\n      <div\r\n        className=\"group relative cursor-pointer\"\r\n        onClick={() => setIsVideoOpen(true)}\r\n      >\r\n        {thumbnailSrc ? (\r\n          <img\r\n            src={thumbnailSrc}\r\n            alt={thumbnailAlt}\r\n            width={1920}\r\n            height={1080}\r\n            className=\"w-full transition-all duration-200 ease-out group-hover:brightness-[0.8] isolate\"\r\n          />\r\n        ) : (\r\n          <div className=\"w-full aspect-video bg-background rounded-2xl\" />\r\n        )}\r\n        <div className=\"absolute isolate inset-0 flex scale-[0.9] items-center justify-center rounded-2xl transition-all duration-200 ease-out group-hover:scale-100\">\r\n          <div className=\"flex size-28 items-center justify-center rounded-full bg-gradient-to-t from-secondary/20 to-[#ACC3F7/15] backdrop-blur-md\">\r\n            <div\r\n              className={`relative flex size-20 scale-100 items-center justify-center rounded-full bg-gradient-to-t from-secondary to-white/10 shadow-md transition-all duration-200 ease-out group-hover:scale-[1.2]`}\r\n            >\r\n              <Play\r\n                className=\"size-8 scale-100 fill-white text-white transition-transform duration-200 ease-out group-hover:scale-105\"\r\n                style={{\r\n                  filter:\r\n                    'drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06))',\r\n                }}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <AnimatePresence>\r\n        {isVideoOpen && (\r\n          <motion.div\r\n            initial={{ opacity: 0 }}\r\n            animate={{ opacity: 1 }}\r\n            onClick={() => setIsVideoOpen(false)}\r\n            exit={{ opacity: 0 }}\r\n            className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-md\"\r\n          >\r\n            <motion.div\r\n              {...selectedAnimation}\r\n              transition={{ type: 'spring', damping: 30, stiffness: 300 }}\r\n              className=\"relative mx-4 aspect-video w-full max-w-4xl md:mx-0\"\r\n            >\r\n              <motion.button\r\n                className=\"absolute cursor-pointer hover:scale-[98%] transition-all duration-200 ease-out -top-16 right-0 rounded-full bg-neutral-900/50 p-2 text-xl text-white ring-1 backdrop-blur-md dark:bg-neutral-100/50 dark:text-black\"\r\n                onClick={() => setIsVideoOpen(false)}\r\n              >\r\n                <XIcon className=\"size-5\" />\r\n              </motion.button>\r\n              <div className=\"relative isolate z-[1] size-full overflow-hidden rounded-2xl border-2 border-white\">\r\n                <iframe\r\n                  src={getVideoSrcWithAutoplay()}\r\n                  className=\"size-full\"\r\n                  allowFullScreen\r\n                  allow=\"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share\"\r\n                ></iframe>\r\n              </div>\r\n            </motion.div>\r\n          </motion.div>\r\n        )}\r\n      </AnimatePresence>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,4CAA4C;;;;AAG5C;AAAA;AACA;AAAA;AACA;AAEA;;;AANA;;;;;AA0BA,MAAM,oBAAoB;IACxB,eAAe;QACb,SAAS;YAAE,GAAG;YAAQ,SAAS;QAAE;QACjC,SAAS;YAAE,GAAG;YAAG,SAAS;QAAE;QAC5B,MAAM;YAAE,GAAG;YAAQ,SAAS;QAAE;IAChC;IACA,eAAe;QACb,SAAS;YAAE,OAAO;YAAK,SAAS;QAAE;QAClC,SAAS;YAAE,OAAO;YAAG,SAAS;QAAE;QAChC,MAAM;YAAE,OAAO;YAAK,SAAS;QAAE;IACjC;IACA,YAAY;QACV,SAAS;YAAE,GAAG;YAAS,SAAS;QAAE;QAClC,SAAS;YAAE,GAAG;YAAG,SAAS;QAAE;QAC5B,MAAM;YAAE,GAAG;YAAS,SAAS;QAAE;IACjC;IACA,aAAa;QACX,SAAS;YAAE,GAAG;YAAS,SAAS;QAAE;QAClC,SAAS;YAAE,GAAG;YAAG,SAAS;QAAE;QAC5B,MAAM;YAAE,GAAG;YAAS,SAAS;QAAE;IACjC;IACA,cAAc;QACZ,SAAS;YAAE,GAAG;YAAQ,SAAS;QAAE;QACjC,SAAS;YAAE,GAAG;YAAG,SAAS;QAAE;QAC5B,MAAM;YAAE,GAAG;YAAQ,SAAS;QAAE;IAChC;IACA,MAAM;QACJ,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,MAAM;YAAE,SAAS;QAAE;IACrB;IACA,qBAAqB;QACnB,SAAS;YAAE,GAAG;YAAS,SAAS;QAAE;QAClC,SAAS;YAAE,GAAG;YAAG,SAAS;QAAE;QAC5B,MAAM;YAAE,GAAG;YAAQ,SAAS;QAAE;IAChC;IACA,qBAAqB;QACnB,SAAS;YAAE,GAAG;YAAS,SAAS;QAAE;QAClC,SAAS;YAAE,GAAG;YAAG,SAAS;QAAE;QAC5B,MAAM;YAAE,GAAG;YAAQ,SAAS;QAAE;IAChC;AACF;AAEO,SAAS,gBAAgB,EAC9B,iBAAiB,aAAa,EAC9B,QAAQ,EACR,YAAY,EACZ,eAAe,iBAAiB,EAChC,SAAS,EACM;;IACf,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,oBAAoB,iBAAiB,CAAC,eAAe;IAE3D,oDAAoD;IACpD,MAAM,0BAA0B;QAC9B,MAAM,MAAM,IAAI,IAAI;QACpB,wDAAwD;QACxD,IAAI,YAAY,CAAC,GAAG,CAAC,YAAY;QACjC,OAAO,IAAI,QAAQ;IACrB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;;0BAC7B,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;oBAE7B,6BACC,6LAAC;wBACC,KAAK;wBACL,KAAK;wBACL,OAAO;wBACP,QAAQ;wBACR,WAAU;;;;;6CAGZ,6LAAC;wBAAI,WAAU;;;;;;kCAEjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAW,CAAC,2LAA2L,CAAC;0CAExM,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCACH,WAAU;oCACV,OAAO;wCACL,QACE;oCACJ;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAMV,6LAAC,oNAAA,CAAA,kBAAe;0BACb,6BACC,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS,IAAM,eAAe;oBAC9B,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;8BAEV,cAAA,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;wBACR,GAAG,iBAAiB;wBACrB,YAAY;4BAAE,MAAM;4BAAU,SAAS;4BAAI,WAAW;wBAAI;wBAC1D,WAAU;;0CAEV,6LAAC,qNAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,WAAU;gCACV,SAAS,IAAM,eAAe;0CAE9B,cAAA,6LAAC,mMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;;;;;;0CAEnB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,KAAK;oCACL,WAAU;oCACV,eAAe;oCACf,OAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;GArFgB;KAAA", "debugId": null}}, {"offset": {"line": 2929, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>/hero-video-section.tsx"], "sourcesContent": ["import { HeroVideoDialog } from '@/components/home/<USER>/hero-video-dialog';\r\nimport { SectionHeader } from '@/components/home/<USER>';\r\n\r\nexport function HeroVideoSection() {\r\n  return (\r\n    <section\r\n      id=\"demo\"\r\n      className=\"flex flex-col items-center justify-center gap-10 w-full relative\"\r\n    >\r\n      <SectionHeader>\r\n        <h2 className=\"text-3xl md:text-4xl font-medium tracking-tighter text-center text-balance pb-1\">\r\n          Watch Intelligence in Motion\r\n        </h2>\r\n        <p className=\"text-muted-foreground text-center text-balance font-medium\">\r\n          Watch how Suna executes complex workflows with precision and autonomy\r\n        </p>\r\n      </SectionHeader>\r\n\r\n      <div className=\"relative px-6\">\r\n        <div className=\"relative w-full max-w-3xl mx-auto shadow-xl rounded-2xl overflow-hidden\">\r\n          <HeroVideoDialog\r\n            className=\"block dark:hidden\"\r\n            animationStyle=\"from-center\"\r\n            videoSrc=\"https://www.youtube.com/embed/Jnxq0osSg2c?si=k8ddEM8h8lver20s\"\r\n            thumbnailSrc=\"/thumbnail-light.png\"\r\n            thumbnailAlt=\"Hero Video\"\r\n          />\r\n          <HeroVideoDialog\r\n            className=\"hidden dark:block\"\r\n            animationStyle=\"from-center\"\r\n            videoSrc=\"https://www.youtube.com/embed/Jnxq0osSg2c?si=k8ddEM8h8lver20s\"\r\n            thumbnailSrc=\"/thumbnail-dark.png\"\r\n            thumbnailAlt=\"Hero Video\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEO,SAAS;IACd,qBACE,6LAAC;QACC,IAAG;QACH,WAAU;;0BAEV,6LAAC,kJAAA,CAAA,gBAAa;;kCACZ,6LAAC;wBAAG,WAAU;kCAAkF;;;;;;kCAGhG,6LAAC;wBAAE,WAAU;kCAA6D;;;;;;;;;;;;0BAK5E,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,8JAAA,CAAA,kBAAe;4BACd,WAAU;4BACV,gBAAe;4BACf,UAAS;4BACT,cAAa;4BACb,cAAa;;;;;;sCAEf,6LAAC,8JAAA,CAAA,kBAAe;4BACd,WAAU;4BACV,gBAAe;4BACf,UAAS;4BACT,cAAa;4BACb,cAAa;;;;;;;;;;;;;;;;;;;;;;;AAMzB;KAnCgB", "debugId": null}}]}