"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8341],{5845:(e,t,r)=>{r.d(t,{i:()=>u});var n,l=r(12115),o=r(52712),i=(n||(n=r.t(l,2)))[" useInsertionEffect ".trim().toString()]||o.N;function u({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[o,u,a]=function({defaultProp:e,onChange:t}){let[r,n]=l.useState(e),o=l.useRef(r),u=l.useRef(t);return i(()=>{u.current=t},[t]),l.useEffect(()=>{o.current!==r&&(u.current?.(r),o.current=r)},[r,o]),[r,n,u]}({defaultProp:t,onChange:r}),c=void 0!==e,s=c?e:o;{let t=l.useRef(void 0!==e);l.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,n])}return[s,l.useCallback(t=>{if(c){let r="function"==typeof t?t(e):t;r!==e&&a.current?.(r)}else u(t)},[c,e,u,a])]}Symbol("RADIX:SYNC_STATE")},6101:(e,t,r)=>{r.d(t,{s:()=>i,t:()=>o});var n=r(12115);function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(...e){return t=>{let r=!1,n=e.map(e=>{let n=l(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():l(e[t],null)}}}}function i(...e){return n.useCallback(o(...e),e)}},19946:(e,t,r)=>{r.d(t,{A:()=>a});var n=r(12115);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let u=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:l=24,strokeWidth:u=2,absoluteStrokeWidth:a,className:c="",children:s,iconNode:d,...f}=e;return(0,n.createElement)("svg",{ref:t,...i,width:l,height:l,stroke:r,strokeWidth:a?24*Number(u)/Number(l):u,className:o("lucide",c),...f},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(s)?s:[s]])}),a=(e,t)=>{let r=(0,n.forwardRef)((r,i)=>{let{className:a,...c}=r;return(0,n.createElement)(u,{ref:i,iconNode:t,className:o("lucide-".concat(l(e)),a),...c})});return r.displayName="".concat(e),r}},46081:(e,t,r)=>{r.d(t,{A:()=>i,q:()=>o});var n=r(12115),l=r(95155);function o(e,t){let r=n.createContext(t),o=e=>{let{children:t,...o}=e,i=n.useMemo(()=>o,Object.values(o));return(0,l.jsx)(r.Provider,{value:i,children:t})};return o.displayName=e+"Provider",[o,function(l){let o=n.useContext(r);if(o)return o;if(void 0!==t)return t;throw Error(`\`${l}\` must be used within \`${e}\``)}]}function i(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let l=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:l}}),[r,l])}};return o.scopeName=e,[function(t,o){let i=n.createContext(o),u=r.length;r=[...r,o];let a=t=>{let{scope:r,children:o,...a}=t,c=r?.[e]?.[u]||i,s=n.useMemo(()=>a,Object.values(a));return(0,l.jsx)(c.Provider,{value:s,children:o})};return a.displayName=t+"Provider",[a,function(r,l){let a=l?.[e]?.[u]||i,c=n.useContext(a);if(c)return c;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let l=r.reduce((t,{useScope:r,scopeName:n})=>{let l=r(e)[`__scope${n}`];return{...t,...l}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:l}),[l])}};return r.scopeName=t.scopeName,r}(o,...t)]}},52712:(e,t,r)=>{r.d(t,{N:()=>l});var n=r(12115),l=globalThis?.document?n.useLayoutEffect:()=>{}},61285:(e,t,r)=>{r.d(t,{B:()=>a});var n,l=r(12115),o=r(52712),i=(n||(n=r.t(l,2)))[" useId ".trim().toString()]||(()=>void 0),u=0;function a(e){let[t,r]=l.useState(i());return(0,o.N)(()=>{e||r(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},63655:(e,t,r)=>{r.d(t,{hO:()=>a,sG:()=>u});var n=r(12115),l=r(47650),o=r(99708),i=r(95155),u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,o.TL)(`Primitive.${t}`),l=n.forwardRef((e,n)=>{let{asChild:l,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(l?r:t,{...o,ref:n})});return l.displayName=`Primitive.${t}`,{...e,[t]:l}},{});function a(e,t){e&&l.flushSync(()=>e.dispatchEvent(t))}},74466:(e,t,r)=>{r.d(t,{F:()=>i});var n=r(52596);let l=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,o=n.$,i=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return o(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:i,defaultVariants:u}=t,a=Object.keys(i).map(e=>{let t=null==r?void 0:r[e],n=null==u?void 0:u[e];if(null===t)return null;let o=l(t)||l(n);return i[e][o]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return o(e,a,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...l}=t;return Object.entries(l).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...u,...c}[t]):({...u,...c})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},85185:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},99708:(e,t,r)=>{r.d(t,{DX:()=>u,Dc:()=>c,TL:()=>i});var n=r(12115),l=r(6101),o=r(95155);function i(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){var i;let e,u,a=(i=r,(u=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(u=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),c=function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=(...e)=>{let t=o(...e);return l(...e),t}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(c.ref=t?(0,l.t)(t,a):a),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:l,...i}=e,u=n.Children.toArray(l),a=u.find(s);if(a){let e=a.props.children,l=u.map(t=>t!==a?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,l):null})}return(0,o.jsx)(t,{...i,ref:r,children:l})});return r.displayName=`${e}.Slot`,r}var u=i("Slot"),a=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=a,t}function s(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===a}}}]);