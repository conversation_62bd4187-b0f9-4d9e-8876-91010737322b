(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1008],{26126:(e,t,a)=>{"use strict";a.d(t,{E:()=>i});var n=a(95155);a(12115);var s=a(99708),r=a(74466),l=a(59434);let o=(0,r.F)("inline-flex items-center justify-center rounded-lg border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground",new:"text-purple-600 dark:text-purple-300 bg-purple-600/30 dark:bg-purple-600/30",beta:"text-blue-600 dark:text-blue-300 bg-blue-600/30 dark:bg-blue-600/30",highlight:"text-green-800 dark:text-green-300 bg-green-600/30 dark:bg-green-600/30"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:a,asChild:r=!1,...i}=e,c=r?s.DX:"span";return(0,n.jsx)(c,{"data-slot":"badge",className:(0,l.cn)(o({variant:a}),t),...i})}},31834:(e,t,a)=>{"use strict";a.d(t,{o:()=>m});var n=a(95155),s=a(59434),r=a(8947),l=a(12115),o=a(25789),i=a(93836),c=a(66722);let d={code:function(e){var t,a,r,l,o,i;let{className:d,children:u,...m}=e;if(!(null==(a=m.node)||null==(t=a.position)?void 0:t.start.line)||(null==(l=m.node)||null==(r=l.position)?void 0:r.start.line)===(null==(i=m.node)||null==(o=i.position)?void 0:o.end.line))return(0,n.jsx)("span",{className:(0,s.cn)("bg-primary-foreground dark:bg-zinc-800 dark:border dark:border-zinc-700 rounded-sm px-1 font-mono text-sm",d),...m,children:u});let g=function(e){if(!e)return"plaintext";let t=e.match(/language-(\w+)/);return t?t[1]:"plaintext"}(d);return(0,n.jsx)(c.NG,{className:"rounded-md overflow-hidden my-4 border border-zinc-200 dark:border-zinc-800 max-w-full min-w-0 w-full",children:(0,n.jsx)(c.sd,{code:u,language:g,className:"text-sm"})})},pre:function(e){let{children:t}=e;return(0,n.jsx)(n.Fragment,{children:t})},ul:function(e){let{children:t,...a}=e;return(0,n.jsx)("ul",{className:"list-disc pl-5 my-2",...a,children:t})},ol:function(e){let{children:t,...a}=e;return(0,n.jsx)("ol",{className:"list-decimal pl-5 my-2",...a,children:t})},li:function(e){let{children:t,...a}=e;return(0,n.jsx)("li",{className:"my-1",...a,children:t})},h1:function(e){let{children:t,...a}=e;return(0,n.jsx)("h1",{className:"text-2xl font-bold my-3",...a,children:t})},h2:function(e){let{children:t,...a}=e;return(0,n.jsx)("h2",{className:"text-xl font-bold my-2",...a,children:t})},h3:function(e){let{children:t,...a}=e;return(0,n.jsx)("h3",{className:"text-lg font-bold my-2",...a,children:t})},blockquote:function(e){let{children:t,...a}=e;return(0,n.jsx)("blockquote",{className:"border-l-4 border-muted pl-4 italic my-2 dark:text-zinc-400 dark:border-zinc-600",...a,children:t})},a:function(e){let{children:t,href:a,...s}=e;return(0,n.jsx)("a",{href:a,className:"text-primary hover:underline dark:text-blue-400",target:"_blank",rel:"noopener noreferrer",...s,children:t})},table:function(e){let{children:t,...a}=e;return(0,n.jsx)("table",{className:"w-full border-collapse my-3 text-sm",...a,children:t})},th:function(e){let{children:t,...a}=e;return(0,n.jsx)("th",{className:"border border-slate-300 dark:border-zinc-700 px-3 py-2 text-left font-semibold bg-slate-100 dark:bg-zinc-800",...a,children:t})},td:function(e){let{children:t,...a}=e;return(0,n.jsx)("td",{className:"border border-slate-300 dark:border-zinc-700 px-3 py-2",...a,children:t})}},u=(0,l.memo)(function(e){let{content:t,components:a=d}=e;return(0,n.jsx)(o.oz,{remarkPlugins:[i.A],components:a,children:t})},function(e,t){return e.content===t.content});u.displayName="MemoizedMarkdownBlock";let m=(0,l.memo)(function(e){let{children:t,id:a,className:o,components:i=d}=e,c=(0,l.useId)(),m=null!=a?a:c,g=(0,l.useMemo)(()=>r.xI.lexer(t).map(e=>e.raw),[t]);return(0,n.jsx)("div",{className:(0,s.cn)("prose-code:before:hidden prose-code:after:hidden",o),children:g.map((e,t)=>(0,n.jsx)(u,{content:e,components:i},"".concat(m,"-block-").concat(t)))})});m.displayName="Markdown"},35076:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>C});var n=a(95155),s=a(12115),r=a(35695),l=a(25731),o=a(56671),i=a(51548),c=a(19363),d=a(67897),u=a(30285),m=a(57434),g=a(82178),f=a(85690),x=a(58832),p=a(81284),h=a(6874),b=a.n(h);let v=new Set(["execute-command","create-file","delete-file","full-file-rewrite","str-replace","browser-click-element","browser-close-tab","browser-drag-drop","browser-get-dropdown-options","browser-go-back","browser-input-text","browser-navigate-to","browser-scroll-down","browser-scroll-to-text","browser-scroll-up","browser-select-dropdown-option","browser-send-keys","browser-switch-tab","browser-wait","deploy","ask","complete","crawl-webpage","web-search"]),w=e=>{let{messages:t,isSidePanelOpen:a,onToggleSidePanel:r,toolCalls:l,setCurrentToolIndex:o,onFileViewerOpen:i,projectName:c="Shared Conversation"}=e,[d,h]=(0,s.useState)({isPlaying:!1,currentMessageIndex:0,visibleMessages:[],streamingText:"",isStreamingText:!1,currentToolCall:null,toolPlaybackIndex:-1}),{isPlaying:w,currentMessageIndex:y,visibleMessages:j,streamingText:k,isStreamingText:N,currentToolCall:_,toolPlaybackIndex:C}=d,S=(0,s.useCallback)(e=>{h(t=>({...t,...e}))},[]),T=(0,s.useCallback)(()=>{S({isPlaying:!w}),w||a||r()},[w,a,r]),z=(0,s.useCallback)(()=>{S({isPlaying:!1,currentMessageIndex:0,visibleMessages:[],streamingText:"",isStreamingText:!1,currentToolCall:null,toolPlaybackIndex:-1})},[S]),E=(0,s.useCallback)(()=>{S({isPlaying:!1,currentMessageIndex:t.length,visibleMessages:t,streamingText:"",isStreamingText:!1,currentToolCall:null,toolPlaybackIndex:l.length-1}),l.length>0&&(o(l.length-1),a||r())},[t,l,a,r,o,S]),A=(0,s.useCallback)((e,n)=>{let s;if(!e||!w)return n(),()=>{};S({isStreamingText:!0,streamingText:""});let l=/<([a-zA-Z\-_]+)(?:\s+[^>]*)?>(?:[\s\S]*?)<\/\1>|<([a-zA-Z\-_]+)(?:\s+[^>]*)?\/>/g,i=[],c=0;for(;null!==(s=l.exec(e));){s.index>c&&i.push({text:e.substring(c,s.index),isTool:!1});let t=s[1]||s[2];i.push({text:s[0],isTool:!0,toolName:t}),c=l.lastIndex}c<e.length&&i.push({text:e.substring(c),isTool:!1});let d=0,u=0,m="",g=!1,f=()=>{if(!w||g)return void setTimeout(f,100);if(u>=i.length){S({isStreamingText:!1});let e=t[y],a=j[j.length-1];(null==a?void 0:a.message_id)===e.message_id?S({visibleMessages:[...j.slice(0,-1),e]}):S({visibleMessages:[...j,e]}),n();return}let e=i[u];if(e.isTool&&0===d&&e.toolName&&v.has(e.toolName)){S({currentToolCall:{name:e.toolName,arguments:e.text,xml_tag_name:e.toolName},toolPlaybackIndex:C+1}),a||r(),o(C+1),g=!0,setTimeout(()=>{g=!1,S({currentToolCall:null}),u++,d=0,f()},500);return}if(d<e.text.length){let t=5,a=e.text[d];t=".!?,;:".includes(a)?5+100*Math.random()+50:5+5*Math.random(),S({streamingText:m+=e.text[d]}),d++,setTimeout(f,t)}else u++,d=0,f()};return f(),()=>{S({isStreamingText:!1,streamingText:""}),g=!0}},[w,t,y,C,o,a,r,S,j]);(0,s.useEffect)(()=>{let e,a;if(w&&0!==t.length)return e=setTimeout(async()=>{if(y>=t.length)return void S({isPlaying:!1});let e=t[y];if(console.log("Playing message ".concat(y,":"),e.type,e.message_id),"assistant"===e.type)try{let t=e.content;try{let e=JSON.parse(t);e.content&&(t=e.content)}catch(e){}await new Promise(e=>{a=A(t,e)})}catch(e){console.error("Error streaming message:",e)}else S({visibleMessages:[...j,e]}),await new Promise(e=>setTimeout(e,500));S({currentMessageIndex:y+1})},500),()=>{clearTimeout(e),a&&a()}},[w,y,t,A,S,j]);let I=a?"left-1/2 -translate-x-1/4 sm:left-[calc(50%-225px)] md:left-[calc(50%-250px)] lg:left-[calc(50%-275px)] xl:left-[calc(50%-325px)]":"left-1/2 -translate-x-1/2",R=(0,s.useCallback)(()=>(0,n.jsx)("div",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 relative z-[50]",children:(0,n.jsxs)("div",{className:"flex h-14 items-center gap-4 px-4",children:[(0,n.jsx)("div",{className:"flex-1",children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("div",{className:"flex items-center justify-center w-6 h-6 rounded-md overflow-hidden bg-primary/10",children:(0,n.jsx)(b(),{href:"/",children:(0,n.jsx)("img",{src:"/kortix-symbol.svg",alt:"Kortix",width:16,height:16,className:"object-contain"})})}),(0,n.jsx)("h1",{children:(0,n.jsx)("span",{className:"font-medium text-foreground",children:c})})]})}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(u.$,{variant:"ghost",size:"icon",onClick:i,className:"h-8 w-8","aria-label":"View Files",children:(0,n.jsx)(m.A,{className:"h-4 w-4"})}),(0,n.jsx)(u.$,{variant:"ghost",size:"icon",onClick:T,className:"h-8 w-8","aria-label":w?"Pause Replay":"Play Replay",children:w?(0,n.jsx)(g.A,{className:"h-4 w-4"}):(0,n.jsx)(f.A,{className:"h-4 w-4"})}),(0,n.jsx)(u.$,{variant:"ghost",size:"icon",onClick:z,className:"h-8 w-8","aria-label":"Restart Replay",children:(0,n.jsx)(x.A,{className:"h-4 w-4 rotate-90"})}),(0,n.jsx)(u.$,{variant:"ghost",size:"icon",onClick:r,className:"h-8 w-8 ".concat(a?"text-primary":""),"aria-label":"Toggle Tool Panel",children:(0,n.jsx)(p.A,{className:"h-4 w-4"})})]})]})}),[w,a,i,r,c,z,T]);return{playbackState:d,updatePlaybackState:S,renderHeader:R,renderFloatingControls:(0,s.useCallback)(()=>(0,n.jsx)(n.Fragment,{children:t.length>0&&(0,n.jsx)("div",{className:"fixed bottom-4 z-10 transform bg-background/90 backdrop-blur rounded-full border shadow-md px-3 py-1.5 transition-all duration-200 ".concat(I),children:(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(u.$,{variant:"ghost",size:"icon",onClick:T,className:"h-8 w-8",children:w?(0,n.jsx)(g.A,{className:"h-4 w-4"}):(0,n.jsx)(f.A,{className:"h-4 w-4"})}),(0,n.jsx)("div",{className:"flex items-center text-xs text-muted-foreground",children:(0,n.jsxs)("span",{children:[Math.min(y+ +!N,t.length),"/",t.length]})}),(0,n.jsx)(u.$,{variant:"ghost",size:"icon",onClick:z,className:"h-8 w-8",children:(0,n.jsx)(x.A,{className:"h-4 w-4 rotate-90"})}),(0,n.jsx)(u.$,{variant:"ghost",size:"sm",onClick:E,className:"text-xs",children:"Skip to end"})]})})}),[I,y,w,N,t.length,z,E,T]),renderWelcomeOverlay:(0,s.useCallback)(()=>(0,n.jsx)(n.Fragment,{children:0===j.length&&!k&&!_&&(0,n.jsxs)("div",{className:"fixed inset-0 flex flex-col items-center justify-center",children:[(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/90 via-black/50 to-transparent dark:from-black/90 dark:via-black/50 dark:to-transparent"}),(0,n.jsxs)("div",{className:"text-center max-w-md mx-auto relative z-10 px-4",children:[(0,n.jsx)("div",{className:"rounded-full bg-primary/10 backdrop-blur-sm w-12 h-12 mx-auto flex items-center justify-center mb-4",children:(0,n.jsx)(f.A,{className:"h-5 w-5 text-primary"})}),(0,n.jsx)("h3",{className:"text-lg font-medium mb-2 text-white",children:"Watch this agent in action"}),(0,n.jsx)("p",{className:"text-sm text-white/80 mb-4",children:"This is a shared view-only agent run. Click play to replay the entire conversation with realistic timing."}),(0,n.jsxs)(u.$,{onClick:T,className:"flex items-center mx-auto bg-white/10 hover:bg-white/20 backdrop-blur-sm text-white border-white/20",size:"lg",variant:"outline",children:[(0,n.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Start Playback"]})]})]})}),[_,k,T,j.length]),togglePlayback:T,resetPlayback:z,skipToEnd:E}};var y=a(18068),j=a(53732),k=a(52300),N=a(99856);let _={PGRST116:"The requested chat does not exist, has been deleted, or you do not have access to it."};function C(e){let{params:t}=e,a=s.use(t).threadId,u=(0,r.useRouter)(),[m,g]=(0,s.useState)([]),[f,x]=(0,s.useState)(!0),[p,h]=(0,s.useState)(null),[b,v]=(0,s.useState)(null),[C,S]=(0,s.useState)("idle"),[T,z]=(0,s.useState)(!1),[E,A]=(0,s.useState)([]),[I,R]=(0,s.useState)(0),[P,M]=(0,s.useState)(!1),[D,O]=(0,s.useState)(!1),[G,L]=(0,s.useState)(0),[F,H]=(0,s.useState)(""),[$,V]=(0,s.useState)(null),[Z,B]=s.useState(void 0),U=(0,s.useRef)(null),q=(0,s.useRef)(null),W=(0,s.useRef)(null),[J,Y]=(0,s.useState)(!1),[K,Q]=(0,s.useState)(!1);(0,s.useRef)(!1);let[X,ee]=(0,s.useState)(null),[et,ea]=(0,s.useState)(null),[en,es]=(0,s.useState)(!1),[er,el]=(0,s.useState)(""),[eo,ei]=(0,s.useState)(null),ec=(0,s.useRef)(!1);(0,s.useRef)(!1),(0,s.useRef)(!1);let[ed,eu]=(0,s.useState)(""),em=(0,s.useRef)(!1);(0,s.useEffect)(()=>{em.current=!0,z(!1)},[]);let eg=(0,s.useCallback)(()=>{z(e=>!e)},[]),ef=(0,s.useCallback)(e=>{R(e),console.log("Tool panel manually set to index ".concat(e))},[]),ex=(0,s.useCallback)(e=>{if(console.log("[STREAM HANDLER] Received message: ID=".concat(e.message_id,", Type=").concat(e.type)),!e.message_id){var t;console.warn("[STREAM HANDLER] Received message is missing ID: Type=".concat(e.type,", Content=").concat(null==(t=e.content)?void 0:t.substring(0,50),"..."))}g(t=>t.some(t=>t.message_id===e.message_id)?t.map(t=>t.message_id===e.message_id?e:t):[...t,e]),"tool"===e.type&&M(!1)},[]),ep=(0,s.useCallback)(e=>{switch(console.log("[PAGE] Hook status changed: ".concat(e)),e){case"idle":case"completed":case"stopped":case"agent_not_running":S("idle"),v(null),M(!1);break;case"connecting":S("connecting");break;case"streaming":S("running");break;case"error":S("error"),setTimeout(()=>{S("idle"),v(null)},3e3)}},[a]),eh=(0,s.useCallback)(e=>{console.error("[PAGE] Stream hook error: ".concat(e)),o.oR.error(e,{duration:15e3})},[]),eb=(0,s.useCallback)(()=>{console.log("[PAGE] Stream hook closed with final status: ".concat(C))},[C]),ev=(0,s.useCallback)(e=>{if(!e)return;let t=e.name||e.xml_tag_name||"Unknown Tool",a=t.replace(/_/g,"-").toLowerCase();if(console.log("[STREAM] Received tool call:",a,"(raw:",t,")"),em.current)return;let n=e.arguments||"",s=n;if(a.includes("command")&&!n.includes("<execute-command>"))s="<execute-command>".concat(n,"</execute-command>");else if(a.includes("file")||"create-file"===a||"delete-file"===a||"full-file-rewrite"===a){let e=["create-file","delete-file","full-file-rewrite"].find(e=>a===e);if(e)if(n.includes("<".concat(e,">"))||n.includes("file_path="))s=n;else{let t=n.trim();s=t&&!t.startsWith("<")?"<".concat(e,' file_path="').concat(t,'">'):"<".concat(e,">").concat(n,"</").concat(e,">")}}let r={assistantCall:{name:a,content:s,timestamp:new Date().toISOString()},toolResult:{content:"STREAMING",isSuccess:!0,timestamp:new Date().toISOString()}};A(e=>e.length>0&&e[0].assistantCall.name===a?[{...e[0],assistantCall:{...e[0].assistantCall,content:s}}]:[r]),R(0),z(!0)},[]);(0,s.useEffect)(()=>{let e;if(D&&0!==m.length)return e=setTimeout(async()=>{if(G>=m.length)return void O(!1);let e=m[G];console.log("Playing message ".concat(G,":"),e.type,e.message_id),L(e=>e+1)},500),()=>{clearTimeout(e)}},[D,G,m]);let{status:ew,toolCall:ey,error:ej,agentRunId:ek,startStreaming:eN,stopStreaming:e_}=(0,j.Z)({onMessage:ex,onStatusChange:ep,onError:eh,onClose:eb},a,g);(0,s.useEffect)(()=>{b&&b!==ek&&(console.log("[PAGE] Target agentRunId set to ".concat(b,", initiating stream...")),eN(b))},[b,eN,ek]),(0,s.useEffect)(()=>{ey&&ev(ey)},[ey,ev]),(0,s.useEffect)(()=>{let e=!0;return async function(){ec.current||x(!0),h(null);try{if(!a)throw Error("Thread ID is required");let[n,s]=await Promise.all([(0,l.fG)(a).catch(e=>{if(_[e.code])h(_[e.code]);else throw Error(e.message);return null}),(0,l.VL)(a).catch(e=>(console.warn("Failed to load messages:",e),[]))]);if(!e)return;let r=(null==n?void 0:n.project_id)?await (0,l.U1)(n.project_id).catch(e=>(console.warn("[SHARE] Could not load project data:",e),null)):null;if(e){if(r){var t;ee(r),"string"==typeof r.sandbox?ea(r.sandbox):(null==(t=r.sandbox)?void 0:t.id)&&ea(r.sandbox.id),el(r.name||"")}else el("Shared Conversation");let e=(s||[]).filter(e=>"status"!==e.type).map(e=>({message_id:e.message_id||null,thread_id:e.thread_id||a,type:e.type||"system",is_llm_message:!!e.is_llm_message,content:e.content||"",metadata:e.metadata||"{}",created_at:e.created_at||new Date().toISOString(),updated_at:e.updated_at||new Date().toISOString(),agent_id:e.agent_id,agents:e.agents}));g(e);let n=[];e.filter(e=>"assistant"===e.type&&e.message_id).forEach(t=>{let a=e.find(e=>{if("tool"!==e.type||!e.metadata||!t.message_id)return!1;try{return(0,y.jD)(e.metadata,{}).assistant_message_id===t.message_id}catch(e){return!1}});if(a){let e="unknown";try{let a=(()=>{try{return(0,y.jD)(t.content,{}).content||t.content}catch(e){return t.content}})(),n=(0,N.bi)(a);if(n)e=n;else{let a=(0,y.jD)(t.content,{});if(a.tool_calls&&a.tool_calls.length>0){var s;let t=a.tool_calls[0];e=((null==(s=t.function)?void 0:s.name)||t.name||"unknown").replace(/_/g,"-").toLowerCase()}}}catch(e){}let r=!0;try{let e=(()=>{try{return(0,y.jD)(a.content,{}).content||a.content}catch(e){return a.content}})();if(e&&"string"==typeof e){let t=e.match(/ToolResult\s*\(\s*success\s*=\s*(True|False|true|false)/i);if(t)r="true"===t[1].toLowerCase();else{let t=e.toLowerCase();r=!(t.includes("failed")||t.includes("error")||t.includes("failure"))}}}catch(e){}n.push({assistantCall:{name:e,content:t.content,timestamp:t.created_at},toolResult:{content:a.content,isSuccess:r,timestamp:a.created_at}})}}),n.sort((e,t)=>{let a=new Date(e.assistantCall.timestamp||"").getTime(),n=new Date(t.assistantCall.timestamp||"").getTime();return a-n}),A(n),ec.current=!0}}catch(t){if(console.error("Error loading thread data:",t),e){let e=t instanceof Error?t.message:"Failed to load thread";h(e),o.oR.error(e)}}finally{e&&x(!1)}}(),()=>{e=!1}},[a]);let eC=function(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"smooth";null==(e=U.current)||e.scrollIntoView({behavior:t})},eS=(0,s.useCallback)((e,t)=>{if(!e){console.warn("Clicked assistant message ID is null. Cannot open side panel."),o.oR.warning("Cannot view details: Assistant message ID is missing.");return}em.current=!1,console.log("[PAGE] Tool Click Triggered. Assistant Message ID:",e,"Tool Name:",t);let a=E.findIndex(t=>{var a,n,s;if(!(null==(a=t.toolResult)?void 0:a.content)||"STREAMING"===t.toolResult.content)return!1;let r=m.find(t=>t.message_id===e&&"assistant"===t.type);if(!r)return!1;let l=m.find(e=>{if("tool"!==e.type||!e.metadata)return!1;try{return(0,y.jD)(e.metadata,{}).assistant_message_id===r.message_id}catch(e){return!1}});return(null==(n=t.assistantCall)?void 0:n.content)===r.content&&(null==(s=t.toolResult)?void 0:s.content)===(null==l?void 0:l.content)});-1!==a?(console.log("[PAGE] Found tool call at index ".concat(a," for assistant message ").concat(e)),B(a),R(a),z(!0),setTimeout(()=>B(void 0),100)):(console.warn("[PAGE] Could not find matching tool call in toolCalls array for assistant message ID: ".concat(e)),o.oR.info("Could not find details for this tool call."))},[m,E]),eT=(0,s.useCallback)((e,t)=>{e?ei(e):ei(null),es(!0)},[]),{playbackState:ez,renderHeader:eE,renderFloatingControls:eA,renderWelcomeOverlay:eI,togglePlayback:eR,resetPlayback:eP,skipToEnd:eM}=w({messages:m,isSidePanelOpen:T,onToggleSidePanel:eg,toolCalls:E,setCurrentToolIndex:R,onFileViewerOpen:eT,projectName:er||"Shared Conversation"});return((0,s.useEffect)(()=>{O(ez.isPlaying),L(ez.currentMessageIndex)},[ez.isPlaying,ez.currentMessageIndex]),(0,s.useEffect)(()=>{ez.visibleMessages.length>0&&!K&&eC("smooth")},[ez.visibleMessages,K]),(0,s.useEffect)(()=>{if(!W.current||0===ez.visibleMessages.length)return;let e=new IntersectionObserver(e=>{let[t]=e;return Y(!(null==t?void 0:t.isIntersecting))},{root:q.current,threshold:.1});return e.observe(W.current),()=>e.disconnect()},[ez.visibleMessages,F,$]),(0,s.useEffect)(()=>{console.log("[PAGE] \uD83D\uDD04 Page AgentStatus: ".concat(C,", Hook Status: ").concat(ew,", Target RunID: ").concat(b||"none",", Hook RunID: ").concat(ek||"none")),("completed"===ew||"stopped"===ew||"agent_not_running"===ew||"error"===ew)&&("running"===C||"connecting"===C)&&(console.log("[PAGE] Detected hook completed but UI still shows running, updating status"),S("idle"),v(null),M(!1))},[C,ew,b,ek]),(0,s.useCallback)(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"smooth";!K&&U.current&&U.current.scrollIntoView({behavior:e})},[K]),(0,s.useEffect)(()=>{if(!D||G<=0||!m.length)return;let e=m[G-1];if((null==e?void 0:e.type)==="tool"&&e.metadata)try{let t=(0,y.jD)(e.metadata,{}).assistant_message_id;if(t){let e=E.findIndex(e=>{var a;let n=m.find(e=>e.message_id===t&&"assistant"===e.type);return!!n&&(null==(a=e.assistantCall)?void 0:a.content)===n.content});-1!==e&&(console.log("Direct mapping: Setting tool index to ".concat(e," for message ").concat(t)),R(e))}}catch(e){console.error("Error in direct tool mapping:",e)}},[G,D,m,E]),(0,s.useEffect)(()=>{if(!D||0===m.length||G<=0)return;let e=m.slice(0,G);for(let a=e.length-1;a>=0;a--){let n=e[a];if("tool"===n.type&&n.metadata)try{let e=(0,y.jD)(n.metadata,{}).assistant_message_id;if(e){console.log("Looking for tool panel for assistant message ".concat(e));for(let a=0;a<E.length;a++){var t;if(((null==(t=E[a].assistantCall)?void 0:t.content)||"").includes(e)){console.log("Found matching tool call at index ".concat(a,", updating panel")),R(a);return}}}}catch(e){console.error("Error parsing tool message metadata:",e)}}},[G,D,m,E]),f&&!ec.current)?(0,n.jsx)(k.y,{isSidePanelOpen:T,showHeader:!0}):p?(0,n.jsx)("div",{className:"flex h-screen",children:(0,n.jsxs)("div",{className:"flex flex-col flex-1 overflow-hidden transition-all duration-200 ease-in-out ".concat(T?"mr-[90%] sm:mr-[450px] md:mr-[500px] lg:mr-[550px] xl:mr-[650px]":""),children:[(0,n.jsx)("div",{className:"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 relative z-[100]",children:(0,n.jsx)("div",{className:"flex h-14 items-center gap-4 px-4",children:(0,n.jsx)("div",{className:"flex-1",children:(0,n.jsx)("span",{className:"text-foreground font-medium",children:"Shared Conversation"})})})}),(0,n.jsx)("div",{className:"flex flex-1 items-center justify-center p-4",children:(0,n.jsxs)("div",{className:"flex w-full max-w-md flex-col items-center gap-4 rounded-lg border bg-card p-6 text-center",children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-destructive",children:"Error"}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:p}),(0,n.jsx)("button",{className:"rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground",onClick:()=>u.push("/"),children:"Back to Home"})]})})]})}):(0,n.jsxs)("div",{className:"flex h-screen",children:[(0,n.jsxs)("div",{className:"flex flex-col flex-1 overflow-hidden transition-all duration-200 ease-in-out ".concat(T?"mr-[90%] sm:mr-[450px] md:mr-[500px] lg:mr-[550px] xl:mr-[650px]":""),children:[eE(),(0,n.jsx)(d.u9,{messages:m,agentStatus:C,handleToolClick:eS,handleOpenFileViewer:eT,readOnly:!0,visibleMessages:ez.visibleMessages,streamingText:ez.streamingText,isStreamingText:ez.isStreamingText,currentToolCall:ez.currentToolCall,sandboxId:et||"",project:X}),eI(),eA()]}),(0,n.jsx)(c.s,{isOpen:T,onClose:()=>{z(!1),em.current=!0},toolCalls:E,messages:m,agentStatus:"idle",currentIndex:I,onNavigate:ef,externalNavigateToIndex:Z,project:X,onFileClick:eT}),(0,n.jsx)(i.S,{open:en,onOpenChange:es,sandboxId:et||"",initialFilePath:eo,project:X})]})}},37156:(e,t,a)=>{Promise.resolve().then(a.bind(a,35076))},44838:(e,t,a)=>{"use strict";a.d(t,{I:()=>d,SQ:()=>c,V0:()=>x,_2:()=>u,hO:()=>m,lp:()=>g,mB:()=>f,rI:()=>o,ty:()=>i});var n=a(95155);a(12115);var s=a(48698),r=a(5196),l=a(59434);function o(e){let{...t}=e;return(0,n.jsx)(s.bL,{"data-slot":"dropdown-menu",...t})}function i(e){let{...t}=e;return(0,n.jsx)(s.l9,{"data-slot":"dropdown-menu-trigger",...t})}function c(e){let{className:t,sideOffset:a=4,...r}=e;return(0,n.jsx)(s.ZL,{children:(0,n.jsx)(s.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-xl border p-1 shadow-md",t),...r})})}function d(e){let{...t}=e;return(0,n.jsx)(s.YJ,{"data-slot":"dropdown-menu-group",...t})}function u(e){let{className:t,inset:a,variant:r="default",...o}=e;return(0,n.jsx)(s.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":r,className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...o})}function m(e){let{className:t,children:a,checked:o,...i}=e;return(0,n.jsxs)(s.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),checked:o,...i,children:[(0,n.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,n.jsx)(s.VF,{children:(0,n.jsx)(r.A,{className:"size-4"})})}),a]})}function g(e){let{className:t,inset:a,...r}=e;return(0,n.jsx)(s.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,l.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...r})}function f(e){let{className:t,...a}=e;return(0,n.jsx)(s.wv,{"data-slot":"dropdown-menu-separator",className:(0,l.cn)("bg-border -mx-1 my-1 h-px",t),...a})}function x(e){let{className:t,...a}=e;return(0,n.jsx)("span",{"data-slot":"dropdown-menu-shortcut",className:(0,l.cn)("text-muted-foreground ml-auto text-xs tracking-widest",t),...a})}},45503:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});var n=a(12115);function s(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},46102:(e,t,a)=>{"use strict";a.d(t,{Bc:()=>l,ZI:()=>c,k$:()=>i,m_:()=>o});var n=a(95155);a(12115);var s=a(89613),r=a(59434);function l(e){let{delayDuration:t=0,...a}=e;return(0,n.jsx)(s.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...a})}function o(e){let{...t}=e;return(0,n.jsx)(l,{children:(0,n.jsx)(s.bL,{"data-slot":"tooltip",...t})})}function i(e){let{...t}=e;return(0,n.jsx)(s.l9,{"data-slot":"tooltip-trigger",...t})}function c(e){let{className:t,sideOffset:a=0,children:l,...o}=e;return(0,n.jsx)(s.ZL,{children:(0,n.jsxs)(s.UC,{"data-slot":"tooltip-content",sideOffset:a,className:(0,r.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",t),...o,children:[l,(0,n.jsx)(s.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},47863:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(19946).A)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},54165:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>u,Es:()=>g,L3:()=>f,LC:()=>d,c7:()=>m,lG:()=>o,rr:()=>x,zM:()=>i});var n=a(95155);a(12115);var s=a(15452),r=a(54416),l=a(59434);function o(e){let{...t}=e;return(0,n.jsx)(s.bL,{"data-slot":"dialog",...t})}function i(e){let{...t}=e;return(0,n.jsx)(s.l9,{"data-slot":"dialog-trigger",...t})}function c(e){let{...t}=e;return(0,n.jsx)(s.ZL,{"data-slot":"dialog-portal",...t})}function d(e){let{className:t,...a}=e;return(0,n.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50 backdrop-blur-xs",t),...a})}function u(e){let{className:t,children:a,...o}=e;return(0,n.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,n.jsx)(d,{}),(0,n.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-2xl border p-6 shadow-lg duration-200",t),...o,children:[a,(0,n.jsxs)(s.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,n.jsx)(r.A,{}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function g(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function f(e){let{className:t,...a}=e;return(0,n.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",t),...a})}function x(e){let{className:t,...a}=e;return(0,n.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",t),...a})}},59434:(e,t,a)=>{"use strict";a.d(t,{$3:()=>i,Hz:()=>o,W5:()=>c,cn:()=>l});var n=a(52596),s=a(81949),r=a(39688);function l(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,n.$)(t))}let o=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"rgba(180, 180, 180)";if(!e)return t;try{if("string"==typeof e&&e.startsWith("var(")){let t=document.createElement("div");t.style.color=e,document.body.appendChild(t);let a=window.getComputedStyle(t).color;return document.body.removeChild(t),s.formatRGBA(s.parse(a))}return s.formatRGBA(s.parse(e))}catch(e){return console.error("Color parsing failed:",e),t}},i=(e,t)=>e.startsWith("rgb")?s.formatRGBA(s.alpha(s.parse(e),t)):e;function c(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return e.length<=t?e:e.slice(0,t)+"..."}},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>r});var n=a(95155);a(12115);var s=a(59434);function r(e){let{className:t,type:a,...r}=e;return(0,n.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...r})}},64541:(e,t,a)=>{"use strict";a.d(t,{A:()=>i,O:()=>o});var n=a(95155),s=a(12115),r=a(52643);let l=(0,s.createContext)(void 0),o=e=>{let{children:t}=e,a=(0,r.U)(),[o,i]=(0,s.useState)(null),[c,d]=(0,s.useState)(null),[u,m]=(0,s.useState)(!0);(0,s.useEffect)(()=>{(async()=>{var e;let{data:{session:t}}=await a.auth.getSession();i(t),d(null!=(e=null==t?void 0:t.user)?e:null),m(!1)})();let{data:e}=a.auth.onAuthStateChange((e,t)=>{var a;i(t),d(null!=(a=null==t?void 0:t.user)?a:null),u&&m(!1)});return()=>{null==e||e.subscription.unsubscribe()}},[a,u]);let g=async()=>{await a.auth.signOut()};return(0,n.jsx)(l.Provider,{value:{supabase:a,session:o,user:c,isLoading:u,signOut:g},children:t})},i=()=>{let e=(0,s.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},66474:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(19946).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>i,Wu:()=>c,ZB:()=>o,Zp:()=>r,aR:()=>l,wL:()=>d});var n=a(95155);a(12115);var s=a(59434);function r(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function o(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function i(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},66722:(e,t,a)=>{"use strict";a.d(t,{NG:()=>i,sd:()=>c});var n=a(95155),s=a(59434),r=a(12115),l=a(475),o=a(51362);function i(e){let{children:t,className:a,...r}=e;return(0,n.jsx)("div",{className:(0,s.cn)("w-px flex-grow min-w-0 overflow-hidden flex",a),...r,children:t})}function c(e){let{code:t,language:a="tsx",theme:i,className:c,...d}=e,{resolvedTheme:u}=(0,o.D)(),[m,g]=(0,r.useState)(null),f=i||("dark"===u?"github-dark":"github-light");(0,r.useEffect)(()=>{!async function(){if(!t||"string"!=typeof t)return g(null);g(await (0,l.Yz)(t,{lang:a,theme:f,transformers:[{pre(e){e.properties.style&&(e.properties.style=e.properties.style.replace(/background-color:[^;]+;?/g,""))}}]}))}()},[t,a,f]);let x=(0,s.cn)("[&_pre]:!bg-background/95 [&_pre]:rounded-lg [&_pre]:p-4 [&_pre]:!overflow-x-auto [&_pre]:!w-px [&_pre]:!flex-grow [&_pre]:!min-w-0 [&_pre]:!box-border [&_.shiki]:!overflow-x-auto [&_.shiki]:!w-px [&_.shiki]:!flex-grow [&_.shiki]:!min-w-0 [&_code]:!min-w-0 [&_code]:!whitespace-pre","w-px flex-grow min-w-0 overflow-hidden flex w-full",c);return m?(0,n.jsx)("div",{className:x,dangerouslySetInnerHTML:{__html:m},...d}):(0,n.jsx)("div",{className:x,...d,children:(0,n.jsx)("pre",{className:"!overflow-x-auto !w-px !flex-grow !min-w-0 !box-border",children:(0,n.jsx)("code",{children:t})})})}},89367:(e,t,a)=>{"use strict";function n(e,[t,a]){return Math.min(a,Math.max(t,e))}a.d(t,{q:()=>n})}},e=>{var t=t=>e(e.s=t);e.O(0,[825,2084,6079,3558,2969,1935,6671,3860,6874,1171,8341,7201,5061,6165,9001,2103,9879,6766,2473,9613,539,5571,6686,3685,9713,5039,6955,8441,1684,7358],()=>t(37156)),_N_E=e.O()}]);