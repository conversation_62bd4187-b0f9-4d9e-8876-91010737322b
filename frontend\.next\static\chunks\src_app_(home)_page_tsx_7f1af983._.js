(globalThis.TURBOPACK_CHUNK_LISTS = globalThis.TURBOPACK_CHUNK_LISTS || []).push({
    script: typeof document === "object" ? document.currentScript : undefined,
    chunks: [
  "static/chunks/src_components_home_ae8eaf4d._.js",
  "static/chunks/src_components_ui_d1b396e7._.js",
  "static/chunks/src_components_thread_d28876db._.js",
  "static/chunks/src_components_agents_b5e39bef._.js",
  "static/chunks/src_components_58e36523._.js",
  "static/chunks/src_hooks_3d87a5e5._.js",
  "static/chunks/src_87391ac5._.js",
  "static/chunks/node_modules_@radix-ui_react-icons_dist_react-icons_esm_ecd85ef9.js",
  "static/chunks/node_modules_lucide-react_dist_esm_icons_88572c29._.js",
  "static/chunks/node_modules_next_dist_compiled_42e3a7b9._.js",
  "static/chunks/node_modules_framer-motion_dist_es_5e916064._.js",
  "static/chunks/node_modules_motion-dom_dist_es_0da7774d._.js",
  "static/chunks/node_modules_react-icons_fa_index_mjs_d2e2d7f5._.js",
  "static/chunks/node_modules_react-icons_si_index_mjs_4ff16b17._.js",
  "static/chunks/node_modules_react-icons_lib_74ccc930._.js",
  "static/chunks/node_modules_date-fns_5e7b5adc._.js",
  "static/chunks/node_modules_react-day-picker_dist_index_esm_9fc30424.js",
  "static/chunks/node_modules_jszip_lib_e13ccca1._.js",
  "static/chunks/node_modules_pako_e267515d._.js",
  "static/chunks/node_modules_5424652e._.js"
],
    source: "dynamic"
});
