(()=>{var e={};e.id=2771,e.ids=[2771],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8719:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{isRequestAPICallableInsideAfter:function(){return c},throwForSearchParamsAccessInUseCache:function(){return o},throwWithStaticGenerationBailoutError:function(){return s},throwWithStaticGenerationBailoutErrorWithDynamicError:function(){return i}});let n=t(80023),a=t(3295);function s(e,r){throw Object.defineProperty(new n.StaticGenBailoutError(`Route ${e} couldn't be rendered statically because it used ${r}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E576",enumerable:!1,configurable:!0})}function i(e,r){throw Object.defineProperty(new n.StaticGenBailoutError(`Route ${e} with \`dynamic = "error"\` couldn't be rendered statically because it used ${r}. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E543",enumerable:!1,configurable:!0})}function o(e){let r=Object.defineProperty(Error(`Route ${e.route} used "searchParams" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "searchParams" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E634",enumerable:!1,configurable:!0});throw e.invalidUsageError??=r,r}function c(){let e=a.afterTaskAsyncStorage.getStore();return(null==e?void 0:e.rootTaskSpawnPhase)==="action"}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},43763:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"ReflectAdapter",{enumerable:!0,get:function(){return t}});class t{static get(e,r,t){let n=Reflect.get(e,r,t);return"function"==typeof n?n.bind(e):n}static set(e,r,t,n){return Reflect.set(e,r,t,n)}static has(e,r){return Reflect.has(e,r)}static deleteProperty(e,r){return Reflect.deleteProperty(e,r)}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72609:(e,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{describeHasCheckingStringProperty:function(){return a},describeStringPropertyAccess:function(){return n},wellKnownProperties:function(){return s}});let t=/^[A-Za-z_$][A-Za-z0-9_$]*$/;function n(e,r){return t.test(r)?"`"+e+"."+r+"`":"`"+e+"["+JSON.stringify(r)+"]`"}function a(e,r){let t=JSON.stringify(r);return"`Reflect.has("+e+", "+t+")`, `"+t+" in "+e+"`, or similar"}let s=new Set(["hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toString","valueOf","toLocaleString","then","catch","finally","status","displayName","toJSON","$$typeof","__esModule"])},78335:()=>{},87063:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>l,serverHooks:()=>f,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>p});var n={};t.r(n),t.d(n,{GET:()=>u});var a=t(96559),s=t(48088),i=t(37719),o=t(32190);let c=process.env.BACKEND_URL||"http://localhost:8000";async function u(e,{params:r}){try{let{provider:t}=await r,n=e.nextUrl.searchParams,a=n.get("code"),s=n.get("state"),i=n.get("error");if(!["slack","discord","teams"].includes(t))return console.error(`Invalid OAuth provider: ${t}`),o.NextResponse.redirect(new URL(`/agents?${t}_error=invalid_provider`,e.url));if(i)return console.error(`${t} OAuth error:`,i),o.NextResponse.redirect(new URL(`/agents?${t}_error=${i}`,e.url));if(!a||!s)return console.error(`Missing required OAuth parameters for ${t}`),o.NextResponse.redirect(new URL(`/agents?${t}_error=missing_parameters`,e.url));let u=new URL(`/api/integrations/${t}/callback`,c);u.searchParams.set("code",a),u.searchParams.set("state",s);let l=await fetch(u.toString(),{method:"GET",headers:{"Content-Type":"application/json"}});if(l.redirected){let r=new URL(l.url).searchParams,t=new URL("/agents",e.url);return r.forEach((e,r)=>{t.searchParams.set(r,e)}),o.NextResponse.redirect(t)}if(!l.ok){let r=await l.text().catch(()=>"Unknown error");return console.error(`Backend OAuth callback failed for ${t}:`,r),o.NextResponse.redirect(new URL(`/agents?${t}_error=backend_error`,e.url))}return o.NextResponse.redirect(new URL(`/agents?${t}_success=true`,e.url))}catch(n){console.error("OAuth callback error:",n);let{provider:t}=await r;return o.NextResponse.redirect(new URL(`/agents?${t}_error=callback_failed`,e.url))}}let l=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/integrations/[provider]/callback/route",pathname:"/api/integrations/[provider]/callback",filename:"route",bundlePath:"app/api/integrations/[provider]/callback/route"},resolvedPagePath:"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\api\\integrations\\[provider]\\callback\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:d,workUnitAsyncStorage:p,serverHooks:f}=l;function h(){return(0,i.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:p})}},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[7719,580],()=>t(87063));module.exports=n})();