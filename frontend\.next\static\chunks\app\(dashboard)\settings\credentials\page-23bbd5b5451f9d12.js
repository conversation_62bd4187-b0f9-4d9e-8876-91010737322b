(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2975],{381:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(19946).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1482:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(19946).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},4229:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(19946).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},4884:(e,s,a)=>{"use strict";a.d(s,{bL:()=>w,zi:()=>b});var l=a(12115),t=a(85185),r=a(6101),n=a(46081),c=a(5845),i=a(45503),o=a(11275),d=a(63655),u=a(95155),h="Switch",[m,p]=(0,n.A)(h),[x,f]=m(h),j=l.forwardRef((e,s)=>{let{__scopeSwitch:a,name:n,checked:i,defaultChecked:o,required:m,disabled:p,value:f="on",onCheckedChange:j,form:y,...v}=e,[w,b]=l.useState(null),k=(0,r.s)(s,e=>b(e)),A=l.useRef(!1),_=!w||y||!!w.closest("form"),[C,S]=(0,c.i)({prop:i,defaultProp:null!=o&&o,onChange:j,caller:h});return(0,u.jsxs)(x,{scope:a,checked:C,disabled:p,children:[(0,u.jsx)(d.sG.button,{type:"button",role:"switch","aria-checked":C,"aria-required":m,"data-state":N(C),"data-disabled":p?"":void 0,disabled:p,value:f,...v,ref:k,onClick:(0,t.m)(e.onClick,e=>{S(e=>!e),_&&(A.current=e.isPropagationStopped(),A.current||e.stopPropagation())})}),_&&(0,u.jsx)(g,{control:w,bubbles:!A.current,name:n,value:f,checked:C,required:m,disabled:p,form:y,style:{transform:"translateX(-100%)"}})]})});j.displayName=h;var y="SwitchThumb",v=l.forwardRef((e,s)=>{let{__scopeSwitch:a,...l}=e,t=f(y,a);return(0,u.jsx)(d.sG.span,{"data-state":N(t.checked),"data-disabled":t.disabled?"":void 0,...l,ref:s})});v.displayName=y;var g=l.forwardRef((e,s)=>{let{__scopeSwitch:a,control:t,checked:n,bubbles:c=!0,...d}=e,h=l.useRef(null),m=(0,r.s)(h,s),p=(0,i.Z)(n),x=(0,o.X)(t);return l.useEffect(()=>{let e=h.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==n&&s){let a=new Event("click",{bubbles:c});s.call(e,n),e.dispatchEvent(a)}},[p,n,c]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...d,tabIndex:-1,ref:m,style:{...d.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function N(e){return e?"checked":"unchecked"}g.displayName="SwitchBubbleInput";var w=j,b=v},6654:(e,s,a)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"useMergedRef",{enumerable:!0,get:function(){return t}});let l=a(12115);function t(e,s){let a=(0,l.useRef)(null),t=(0,l.useRef)(null);return(0,l.useCallback)(l=>{if(null===l){let e=a.current;e&&(a.current=null,e());let s=t.current;s&&(t.current=null,s())}else e&&(a.current=r(e,l)),s&&(t.current=r(s,l))},[e,s])}function r(e,s){if("function"!=typeof e)return e.current=s,()=>{e.current=null};{let a=e(s);return"function"==typeof a?a:()=>e(null)}}("function"==typeof s.default||"object"==typeof s.default&&null!==s.default)&&void 0===s.default.__esModule&&(Object.defineProperty(s.default,"__esModule",{value:!0}),Object.assign(s.default,s),e.exports=s.default)},13052:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(19946).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},13717:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(19946).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},24561:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>G});var l=a(95155),t=a(12115),r=a(71539),n=a(66695),c=a(30285),i=a(26126),o=a(62523),d=a(68856),u=a(55365),h=a(85127),m=a(59434);function p(e){let{columns:s,data:a,className:t,emptyMessage:r="No data available",onRowClick:n}=e;return(0,l.jsx)("div",{className:(0,m.cn)("rounded-md border",t),children:(0,l.jsxs)(h.Table,{children:[(0,l.jsx)(h.A0,{children:(0,l.jsx)(h.TableRow,{children:s.map(e=>(0,l.jsx)(h.nd,{className:(0,m.cn)(e.headerClassName,e.width,"text-muted-foreground font-semibold"),children:e.header},e.id))})}),(0,l.jsx)(h.TableBody,{children:0===a.length?(0,l.jsx)(h.TableRow,{children:(0,l.jsx)(h.TableCell,{colSpan:s.length,className:"text-center py-8 text-muted-foreground",children:r})}):a.map((e,a)=>(0,l.jsx)(h.TableRow,{className:(0,m.cn)(n&&"cursor-pointer hover:bg-muted/50"),onClick:()=>null==n?void 0:n(e),children:s.map(s=>(0,l.jsx)(h.TableCell,{className:(0,m.cn)(s.className,s.width),children:s.cell?s.cell(e):s.accessorKey?String(e[s.accessorKey]||""):""},s.id))},a))})]})})}var x=a(51154),f=a(43453),j=a(54416),y=a(38164),v=a(381),g=a(13717),N=a(54861),w=a(53904),b=a(62525),k=a(84616),A=a(85339),_=a(71007),C=a(47924),S=a(2417),M=a(26104),E=a(26528),R=a(78948),z=a(26715),P=a(69947),L=a(54165),O=a(44838),T=a(90010),D=a(56671);let q=e=>{let{appSlug:s,appName:a,profiles:r,appImage:n,onConnect:d,onProfileUpdate:u,onProfileDelete:h,onProfileConnect:A,isUpdating:_,isDeleting:C,isConnecting:M,allAppsData:E}=e,[R,z]=(0,t.useState)(null),[P,L]=(0,t.useState)(""),[q,F]=(0,t.useState)(null),[$,I]=(0,t.useState)(!1),[H,G]=(0,t.useState)(""),[K,U]=(0,t.useState)(!1),Z=(0,S.O4)(),B=(0,S.wG)(),V=(0,t.useMemo)(()=>{var e;return null==E||null==(e=E.apps)?void 0:e.find(e=>e.name_slug===s||e.name.toLowerCase()===a.toLowerCase())},[E,s,a]),Q=(0,t.useMemo)(()=>{var e,l,t;return{id:s,name:a,name_slug:s,auth_type:"oauth",description:"Connect to ".concat(a),img_src:(null==V?void 0:V.img_src)||"",custom_fields_json:(null==V?void 0:V.custom_fields_json)||"[]",categories:(null==V?void 0:V.categories)||[],featured_weight:0,connect:{allowed_domains:(null==V||null==(e=V.connect)?void 0:e.allowed_domains)||null,base_proxy_target_url:(null==V||null==(l=V.connect)?void 0:l.base_proxy_target_url)||"",proxy_enabled:(null==V||null==(t=V.connect)?void 0:t.proxy_enabled)||!1}}},[s,a,V]),W=async()=>{if(!H.trim())return void D.oR.error("Please enter a profile name");U(!0);try{let e={profile_name:H.trim(),app_slug:s,app_name:a,is_default:0===r.length},l=await Z.mutateAsync(e);await B.mutateAsync({profileId:l.profile_id,app:s}),G(""),I(!1),D.oR.success("Profile created and connected!")}catch(e){console.error("Error creating profile:",e)}finally{U(!1)}},X=e=>{z(e.profile_id),L(e.profile_name)},J=async e=>{P.trim()&&(await u(e,{profile_name:P.trim()}),z(null),L(""))},Y=()=>{z(null),L("")},ee=[{id:"name",header:"Profile Name",width:"w-1/3",cell:e=>(0,l.jsx)("div",{className:"flex items-center gap-2",children:R===e.profile_id?(0,l.jsxs)("div",{className:"flex items-center gap-2 w-full",children:[(0,l.jsx)(o.p,{value:P,onChange:e=>L(e.target.value),onKeyDown:s=>{"Enter"===s.key&&J(e),"Escape"===s.key&&Y()},className:"h-8 text-sm",autoFocus:!0}),(0,l.jsx)(c.$,{size:"sm",onClick:()=>J(e),disabled:_===e.profile_id,className:"h-8 px-2",children:_===e.profile_id?(0,l.jsx)(x.A,{className:"h-3 w-3 animate-spin"}):(0,l.jsx)(f.A,{className:"h-3 w-3"})}),(0,l.jsx)(c.$,{size:"sm",variant:"outline",onClick:Y,className:"h-8 px-2",children:(0,l.jsx)(j.A,{className:"h-3 w-3"})})]}):(0,l.jsx)("span",{className:"font-medium",children:e.profile_name})})},{id:"status",header:"Status",width:"w-1/4",cell:e=>(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[e.is_connected?(0,l.jsxs)("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[(0,l.jsx)("div",{className:"h-2 w-2 rounded-full bg-green-500"}),"Connected"]}):(0,l.jsxs)("div",{className:"flex items-center gap-2 text-xs text-muted-foreground",children:[(0,l.jsx)("div",{className:"h-2 w-2 rounded-full bg-destructive"}),"Disconnected"]}),e.is_default&&(0,l.jsx)(i.E,{variant:"outline",className:"text-xs",children:"Default"}),!e.is_active&&(0,l.jsx)(i.E,{variant:"outline",className:"text-xs",children:"Inactive"})]})},{id:"actions",header:"Actions",width:"w-1/3",headerClassName:"text-right",className:"text-right",cell:e=>(0,l.jsxs)("div",{className:"flex items-center justify-end gap-2",children:[!e.is_connected&&(0,l.jsxs)(c.$,{size:"sm",variant:"ghost",onClick:()=>A(e),disabled:M===e.profile_id,className:"h-8 px-2 text-xs",children:[M===e.profile_id?(0,l.jsx)(x.A,{className:"h-3 w-3 animate-spin"}):(0,l.jsx)(y.A,{className:"h-3 w-3"}),"Connect"]}),(0,l.jsxs)(O.rI,{children:[(0,l.jsx)(O.ty,{asChild:!0,children:(0,l.jsx)(c.$,{size:"sm",variant:"ghost",className:"h-8 w-8 p-0",children:(0,l.jsx)(v.A,{className:"h-4 w-4"})})}),(0,l.jsxs)(O.SQ,{align:"end",children:[(0,l.jsxs)(O._2,{onClick:()=>X(e),children:[(0,l.jsx)(g.A,{className:"h-4 w-4"}),"Edit Name"]}),(0,l.jsxs)(O._2,{onClick:()=>u(e,{is_default:!e.is_default}),children:[(0,l.jsx)(f.A,{className:"h-4 w-4"}),e.is_default?"Remove Default":"Set as Default"]}),(0,l.jsx)(O._2,{onClick:()=>u(e,{is_active:!e.is_active}),children:e.is_active?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(N.A,{className:"h-4 w-4"}),"Deactivate"]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(f.A,{className:"h-4 w-4"}),"Activate"]})}),e.is_connected&&(0,l.jsxs)(O._2,{onClick:()=>A(e),children:[(0,l.jsx)(w.A,{className:"h-4 w-4"}),"Reconnect"]}),(0,l.jsx)(O.mB,{}),(0,l.jsxs)(O._2,{onClick:()=>F(e),className:"text-destructive",children:[(0,l.jsx)(b.A,{className:"h-4 w-4"}),"Delete"]})]})]})]})}];return(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsxs)("div",{className:"h-8 w-8 rounded-lg bg-muted/50 flex items-center justify-center overflow-hidden border",children:[n?(0,l.jsx)("img",{src:n,alt:a,className:"h-5 w-5 object-cover",onError:e=>{var s;let a=e.target;a.style.display="none",null==(s=a.nextElementSibling)||s.classList.remove("hidden")}}):null,(0,l.jsx)("span",{className:(0,m.cn)("text-sm font-semibold text-muted-foreground",n?"hidden":"block"),children:a.charAt(0).toUpperCase()})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-semibold text-sm",children:a}),(0,l.jsxs)("p",{className:"text-xs text-muted-foreground",children:[r.length," ",1===r.length?"profile":"profiles"]})]})]}),(0,l.jsx)("div",{className:"flex items-center gap-2",children:$?(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(o.p,{placeholder:"Profile name",value:H,onChange:e=>G(e.target.value),onKeyDown:e=>{"Enter"===e.key&&W(),"Escape"===e.key&&(I(!1),G(""))},className:"h-8 text-sm w-32",autoFocus:!0}),(0,l.jsx)(c.$,{size:"sm",onClick:W,disabled:K,className:"h-8 px-2",children:K?(0,l.jsx)(x.A,{className:"h-3 w-3 animate-spin"}):(0,l.jsx)(f.A,{className:"h-3 w-3"})}),(0,l.jsx)(c.$,{size:"sm",variant:"outline",onClick:()=>{I(!1),G("")},className:"h-8 px-2",children:(0,l.jsx)(j.A,{className:"h-3 w-3"})})]}):(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)(c.$,{size:"sm",variant:"link",onClick:()=>d(Q),children:[(0,l.jsx)(k.A,{className:"h-3 w-3"}),"New Profile"]})})})]}),(0,l.jsx)(p,{columns:ee,data:r,emptyMessage:"No ".concat(a," profiles found"),className:"bg-card border rounded-lg"}),(0,l.jsx)(T.Lt,{open:!!q,onOpenChange:e=>!e&&F(null),children:(0,l.jsxs)(T.EO,{children:[(0,l.jsxs)(T.wd,{children:[(0,l.jsx)(T.r7,{children:"Delete Profile"}),(0,l.jsxs)(T.$v,{children:['Are you sure you want to delete "',null==q?void 0:q.profile_name,'"? This action cannot be undone.']})]}),(0,l.jsxs)(T.ck,{children:[(0,l.jsx)(T.Zr,{children:"Cancel"}),(0,l.jsx)(T.Rx,{onClick:()=>{q&&(h(q),F(null))},className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",children:"Delete"})]})]})})]})},F=e=>{let{onConnectNewApp:s}=e,[a,r]=(0,t.useState)(!1),[i,h]=(0,t.useState)(!1),[m,p]=(0,t.useState)(null),[x,f]=(0,t.useState)(""),[y,v]=(0,t.useState)(null),[g,N]=(0,t.useState)(null),[w,b]=(0,t.useState)(null),O=(0,z.jE)(),{data:T,isLoading:F,error:$}=(0,S.h4)(),{data:I}=(0,M.Dy)(void 0,""),H=(0,S.Lh)(),G=(0,S.PA)(),K=(0,S.wG)(),U=e=>{p(e),h(!0)},Z=async(e,s)=>{v(e.profile_id);try{await H.mutateAsync({profileId:e.profile_id,request:s}),D.oR.success("Profile updated successfully")}catch(e){console.error("Error updating profile:",e)}finally{v(null)}},B=async e=>{N(e.profile_id);try{await G.mutateAsync(e.profile_id),D.oR.success("Profile deleted successfully")}catch(e){console.error("Error deleting profile:",e)}finally{N(null)}},V=async e=>{b(e.profile_id);try{await K.mutateAsync({profileId:e.profile_id,app:e.app_slug}),D.oR.success("Profile connected successfully")}catch(e){console.error("Error connecting profile:",e)}finally{b(null)}},Q=(null==T?void 0:T.reduce((e,s)=>{let a=s.app_slug;return e[a]||(e[a]=[]),e[a].push(s),e},{}))||{},W=(0,t.useMemo)(()=>{if(!x.trim())return Q;let e=x.toLowerCase(),s={};return Object.entries(Q).forEach(a=>{var l;let[t,r]=a,n=(null==(l=r[0])?void 0:l.app_name.toLowerCase())||"",c=r.filter(s=>s.profile_name.toLowerCase().includes(e)||s.app_name.toLowerCase().includes(e));(n.includes(e)||c.length>0)&&(s[t]=n.includes(e)?r:c)}),s},[Q,x]),X=(null==T?void 0:T.length)||0,J=(null==T?void 0:T.filter(e=>e.is_connected).length)||0,Y=Object.keys(Q).length,ee=Object.keys(W).length,es=Object.values(W).flat().length;return F?(0,l.jsx)("div",{className:"space-y-6",children:Array.from({length:2}).map((e,s)=>(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)(d.Skeleton,{className:"h-8 w-8 rounded-lg"}),(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)(d.Skeleton,{className:"h-4 w-32"}),(0,l.jsx)(d.Skeleton,{className:"h-3 w-24"})]})]}),(0,l.jsx)("div",{className:"rounded-md border",children:(0,l.jsxs)("div",{className:"p-4 space-y-3",children:[(0,l.jsx)(d.Skeleton,{className:"h-4 w-full"}),(0,l.jsx)(d.Skeleton,{className:"h-4 w-full"}),(0,l.jsx)(d.Skeleton,{className:"h-4 w-3/4"})]})})]},s))}):$?(0,l.jsxs)(u.Fc,{variant:"destructive",children:[(0,l.jsx)(A.A,{className:"h-4 w-4"}),(0,l.jsx)(u.TN,{children:"Failed to load credential profiles. Please try again."})]}):0===X?(0,l.jsx)(n.Zp,{className:"border-dashed",children:(0,l.jsx)(n.Wu,{className:"p-8 text-center",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("div",{className:"p-3 rounded-full bg-primary/10 w-fit mx-auto",children:(0,l.jsx)(_.A,{className:"h-6 w-6 text-primary"})}),(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)("h3",{className:"font-semibold text-foreground",children:"No credential profiles yet"}),(0,l.jsx)("p",{className:"text-sm text-muted-foreground max-w-md mx-auto",children:"Connect your favorite apps to create credential profiles for your agents"})]}),(0,l.jsxs)(c.$,{onClick:()=>r(!0),className:"h-9",children:[(0,l.jsx)(k.A,{className:"h-4 w-4"}),"Connect New App"]})]})})}):(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("div",{children:(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:x?"".concat(ee," ").concat(1===ee?"app":"apps"," with ").concat(es," ").concat(1===es?"profile":"profiles"," found"):"".concat(Y," ").concat(1===Y?"app":"apps"," with ").concat(X," ").concat(1===X?"profile":"profiles"," (").concat(J," connected)")})}),(0,l.jsxs)(c.$,{onClick:()=>r(!0),size:"sm",children:[(0,l.jsx)(k.A,{className:"h-4 w-4"}),"Connect New App"]})]}),(0,l.jsxs)("div",{className:"relative max-w-sm",children:[(0,l.jsx)(C.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,l.jsx)(o.p,{placeholder:"Search apps and profiles...",value:x,onChange:e=>f(e.target.value),className:"pl-12 h-12 rounded-xl bg-muted/50 border-0 focus:bg-background focus:ring-2 focus:ring-primary/20 transition-all"}),x&&(0,l.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>f(""),className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0 hover:bg-muted",children:(0,l.jsx)(j.A,{className:"h-3 w-3"})})]}),0===Object.keys(W).length?(0,l.jsx)(n.Zp,{className:"border-dashed",children:(0,l.jsx)(n.Wu,{className:"p-8 text-center",children:(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("div",{className:"p-3 rounded-full bg-muted/50 w-fit mx-auto",children:(0,l.jsx)(C.A,{className:"h-6 w-6 text-muted-foreground"})}),(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)("h3",{className:"font-semibold text-foreground",children:"No results found"}),(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:"Try adjusting your search terms or browse all apps"})]}),(0,l.jsx)(c.$,{onClick:()=>f(""),variant:"outline",size:"sm",children:"Clear search"})]})})}):(0,l.jsx)("div",{className:"space-y-6",children:Object.entries(W).sort((e,s)=>{let[,a]=e,[,l]=s,t=a.filter(e=>e.is_connected).length,r=l.filter(e=>e.is_connected).length;return t!==r?r-t:l.length-a.length}).map(e=>{var s;let[a,t]=e,r=null==I||null==(s=I.apps)?void 0:s.find(e=>e.name_slug===a||e.name.toLowerCase()===t[0].app_name.toLowerCase());return(0,l.jsx)(q,{appSlug:a,appName:t[0].app_name,profiles:t,appImage:null==r?void 0:r.img_src,onConnect:U,onProfileUpdate:Z,onProfileDelete:B,onProfileConnect:V,isUpdating:y,isDeleting:g,isConnecting:w,allAppsData:I},a)})}),(0,l.jsx)(L.lG,{open:a,onOpenChange:r,children:(0,l.jsxs)(L.Cf,{className:"p-0 max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,l.jsxs)(L.c7,{className:"sr-only",children:[(0,l.jsx)(L.L3,{children:"Browse Apps"}),(0,l.jsx)(L.rr,{children:"Select an app to create a credential profile"})]}),(0,l.jsx)(E.Z,{mode:"profile-only",onAppSelected:e=>{r(!1),s&&s(e)}})]})}),m&&(0,l.jsx)(R.R,{app:m,open:i,onOpenChange:h,onComplete:(e,s,a,l)=>{h(!1),p(null),D.oR.success("Connected to ".concat(a,"!")),O.invalidateQueries({queryKey:P.M.profiles.all()})},mode:"profile-only"})]})};var $=a(35695),I=a(17711),H=a(89340);function G(){let{enabled:e,loading:s}=(0,I.u)("custom_agents"),a=(0,$.useRouter)(),[n,c]=(0,t.useState)(null);return((0,t.useEffect)(()=>{s||e||a.replace("/dashboard")},[s,e,a]),s)?(0,l.jsx)("div",{className:"container mx-auto max-w-4xl px-6 py-6",children:(0,l.jsx)("div",{className:"space-y-6",children:(0,l.jsxs)("div",{className:"animate-pulse space-y-4",children:[(0,l.jsx)("div",{className:"h-32 bg-muted rounded-3xl"}),(0,l.jsx)("div",{className:"space-y-3",children:Array.from({length:3}).map((e,s)=>(0,l.jsx)("div",{className:"h-32 bg-muted rounded-lg"},s))})]})})}):e?(0,l.jsx)("div",{className:"container mx-auto max-w-4xl px-6 py-6",children:(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsx)(H.z,{icon:r.A,children:(0,l.jsx)("span",{className:"text-primary",children:"App Credentials"})}),(0,l.jsx)(F,{onConnectNewApp:e=>{c(e)}})]})}):null}},26104:(e,s,a)=>{"use strict";a.d(s,{Dy:()=>i});var l=a(28755),t=a(99090),r=a(90697),n=a(2508),c=a(69947);let i=(e,s)=>(0,l.I)({queryKey:["pipedream","apps",e,s],queryFn:async()=>await n.A.getApps(e,s),staleTime:3e5,retry:2});(0,t.GQ)(c.M.availableTools(),async function(){var e;let s=arguments.length>0&&void 0!==arguments[0]&&arguments[0],a=new URLSearchParams;s&&a.append("force_refresh","true");let l="/pipedream/mcp/available-tools".concat(a.toString()?"?".concat(a.toString()):""),t=await r.Hv.get(l,{errorContext:{operation:"load available tools",resource:"Pipedream tools"}});if(t.success&&t.data)if(t.data.success)return t.data;else throw Error(t.data.error||"Failed to get available tools");throw Error((null==(e=t.error)?void 0:e.message)||"Failed to get available tools")},{staleTime:3e5,gcTime:6e5,refetchOnWindowFocus:!1,refetchOnMount:!0,retry:(e,s)=>{if(e<2){var a;let e=(null==s||null==(a=s.message)?void 0:a.toLowerCase())||"";return!e.includes("unauthorized")&&!e.includes("forbidden")}return!1},retryDelay:e=>Math.min(1e3*2**e,3e4)})},31418:(e,s,a)=>{Promise.resolve().then(a.bind(a,24561))},33109:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(19946).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},38164:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(19946).A)("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},38564:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(19946).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},42355:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(19946).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},43453:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(19946).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},47924:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},53311:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(19946).A)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},54861:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(19946).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},71007:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(19946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},71539:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(19946).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},85127:(e,s,a)=>{"use strict";a.d(s,{A0:()=>n,Table:()=>r,TableBody:()=>c,TableCell:()=>d,TableRow:()=>i,nd:()=>o});var l=a(95155);a(12115);var t=a(59434);function r(e){let{className:s,...a}=e;return(0,l.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,l.jsx)("table",{"data-slot":"table",className:(0,t.cn)("w-full caption-bottom text-sm",s),...a})})}function n(e){let{className:s,...a}=e;return(0,l.jsx)("thead",{"data-slot":"table-header",className:(0,t.cn)("[&_tr]:border-b",s),...a})}function c(e){let{className:s,...a}=e;return(0,l.jsx)("tbody",{"data-slot":"table-body",className:(0,t.cn)("[&_tr:last-child]:border-0",s),...a})}function i(e){let{className:s,...a}=e;return(0,l.jsx)("tr",{"data-slot":"table-row",className:(0,t.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",s),...a})}function o(e){let{className:s,...a}=e;return(0,l.jsx)("th",{"data-slot":"table-head",className:(0,t.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...a})}function d(e){let{className:s,...a}=e;return(0,l.jsx)("td",{"data-slot":"table-cell",className:(0,t.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...a})}},92138:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(19946).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[2969,1935,6671,3860,1171,8341,7201,5061,6165,9001,2103,6766,9855,2473,2566,9613,6686,4962,6528,2880,8441,1684,7358],()=>s(31418)),_N_E=e.O()}]);