(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/home/<USER>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ThemeProvider": (()=>ThemeProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)");
'use client';
;
;
function ThemeProvider({ children, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ThemeProvider"], {
        ...props,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/home/<USER>",
        lineNumber: 10,
        columnNumber: 10
    }, this);
}
_c = ThemeProvider;
var _c;
__turbopack_context__.k.register(_c, "ThemeProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/supabase/client.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "createClient": (()=>createClient)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createBrowserClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@supabase/ssr/dist/module/createBrowserClient.js [app-client] (ecmascript)");
;
const createClient = ()=>{
    // Get URL and key from environment variables
    let supabaseUrl = ("TURBOPACK compile-time value", "");
    const supabaseAnonKey = ("TURBOPACK compile-time value", "");
    // Ensure the URL is in the proper format with http/https protocol
    if (supabaseUrl && !supabaseUrl.startsWith('http')) {
        // If it's just a hostname without protocol, add http://
        supabaseUrl = `http://${supabaseUrl}`;
    }
    // console.log('Supabase URL:', supabaseUrl);
    // console.log('Supabase Anon Key:', supabaseAnonKey);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$ssr$2f$dist$2f$module$2f$createBrowserClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createBrowserClient"])(supabaseUrl, supabaseAnonKey);
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/AuthProvider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuthProvider": (()=>AuthProvider),
    "useAuth": (()=>useAuth)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase/client.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
'use client';
;
;
const AuthContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const AuthProvider = ({ children })=>{
    _s();
    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
    const [session, setSession] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [user, setUser] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthProvider.useEffect": ()=>{
            const getInitialSession = {
                "AuthProvider.useEffect.getInitialSession": async ()=>{
                    const { data: { session: currentSession } } = await supabase.auth.getSession();
                    setSession(currentSession);
                    setUser(currentSession?.user ?? null);
                    setIsLoading(false);
                }
            }["AuthProvider.useEffect.getInitialSession"];
            getInitialSession();
            const { data: authListener } = supabase.auth.onAuthStateChange({
                "AuthProvider.useEffect": (_event, newSession)=>{
                    setSession(newSession);
                    setUser(newSession?.user ?? null);
                    // No need to set loading state here as initial load is done
                    // and subsequent changes shouldn't show a loading state for the whole app
                    if (isLoading) setIsLoading(false);
                }
            }["AuthProvider.useEffect"]);
            return ({
                "AuthProvider.useEffect": ()=>{
                    authListener?.subscription.unsubscribe();
                }
            })["AuthProvider.useEffect"];
        }
    }["AuthProvider.useEffect"], [
        supabase,
        isLoading
    ]); // Added isLoading to dependencies to ensure it runs once after initial load completes
    const signOut = async ()=>{
        await supabase.auth.signOut();
    // State updates will be handled by onAuthStateChange
    };
    const value = {
        supabase,
        session,
        user,
        isLoading,
        signOut
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(AuthContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/components/AuthProvider.tsx",
        lineNumber: 70,
        columnNumber: 10
    }, this);
};
_s(AuthProvider, "DXVOhXI98GuIhHj+f2l1eK0qIfM=");
_c = AuthProvider;
const useAuth = ()=>{
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(AuthContext);
    if (context === undefined) {
        throw new Error('useAuth must be used within an AuthProvider');
    }
    return context;
};
_s1(useAuth, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "AuthProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/api.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "BillingError": (()=>BillingError),
    "NoAccessTokenAvailableError": (()=>NoAccessTokenAvailableError),
    "addUserMessage": (()=>addUserMessage),
    "checkApiHealth": (()=>checkApiHealth),
    "checkBillingStatus": (()=>checkBillingStatus),
    "createCheckoutSession": (()=>createCheckoutSession),
    "createPortalSession": (()=>createPortalSession),
    "createProject": (()=>createProject),
    "createSandboxFile": (()=>createSandboxFile),
    "createSandboxFileJson": (()=>createSandboxFileJson),
    "createThread": (()=>createThread),
    "deleteProject": (()=>deleteProject),
    "getAgentBuilderChatHistory": (()=>getAgentBuilderChatHistory),
    "getAgentRuns": (()=>getAgentRuns),
    "getAgentStatus": (()=>getAgentStatus),
    "getAvailableModels": (()=>getAvailableModels),
    "getMessages": (()=>getMessages),
    "getProject": (()=>getProject),
    "getProjects": (()=>getProjects),
    "getPublicProjects": (()=>getPublicProjects),
    "getSandboxFileContent": (()=>getSandboxFileContent),
    "getSubscription": (()=>getSubscription),
    "getThread": (()=>getThread),
    "getThreads": (()=>getThreads),
    "initiateAgent": (()=>initiateAgent),
    "listSandboxFiles": (()=>listSandboxFiles),
    "startAgent": (()=>startAgent),
    "stopAgent": (()=>stopAgent),
    "streamAgent": (()=>streamAgent),
    "transcribeAudio": (()=>transcribeAudio),
    "updateProject": (()=>updateProject)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase/client.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/error-handler.ts [app-client] (ecmascript)");
;
;
// Get backend URL from environment variables
const API_URL = ("TURBOPACK compile-time value", "http://localhost:8000/api") || '';
// Set to keep track of agent runs that are known to be non-running
const nonRunningAgentRuns = new Set();
// Map to keep track of active EventSource streams
const activeStreams = new Map();
class BillingError extends Error {
    status;
    detail;
    constructor(status, detail, message){
        super(message || detail.message || `Billing Error: ${status}`);
        this.name = 'BillingError';
        this.status = status;
        this.detail = detail;
        // Set the prototype explicitly.
        Object.setPrototypeOf(this, BillingError.prototype);
    }
}
class NoAccessTokenAvailableError extends Error {
    constructor(message, options){
        super(message || 'No access token available', options);
    }
    name = 'NoAccessTokenAvailableError';
}
const getProjects = async ()=>{
    try {
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
        // Get the current user's ID to filter projects
        const { data: userData, error: userError } = await supabase.auth.getUser();
        if (userError) {
            console.error('Error getting current user:', userError);
            return [];
        }
        // If no user is logged in, return an empty array
        if (!userData.user) {
            console.log('[API] No user logged in, returning empty projects array');
            return [];
        }
        // Query only projects where account_id matches the current user's ID
        const { data, error } = await supabase.from('projects').select('*').eq('account_id', userData.user.id);
        if (error) {
            // Handle permission errors specifically
            if (error.code === '42501' && error.message.includes('has_role_on_account')) {
                console.error('Permission error: User does not have proper account access');
                return []; // Return empty array instead of throwing
            }
            throw error;
        }
        console.log('[API] Raw projects from DB:', data?.length, data);
        // Map database fields to our Project type
        const mappedProjects = (data || []).map((project)=>({
                id: project.project_id,
                name: project.name || '',
                description: project.description || '',
                account_id: project.account_id,
                created_at: project.created_at,
                updated_at: project.updated_at,
                sandbox: project.sandbox || {
                    id: '',
                    pass: '',
                    vnc_preview: '',
                    sandbox_url: ''
                }
            }));
        console.log('[API] Mapped projects for frontend:', mappedProjects.length);
        return mappedProjects;
    } catch (err) {
        console.error('Error fetching projects:', err);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(err, {
            operation: 'load projects',
            resource: 'projects'
        });
        // Return empty array for permission errors to avoid crashing the UI
        return [];
    }
};
const getProject = async (projectId)=>{
    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
    try {
        const { data, error } = await supabase.from('projects').select('*').eq('project_id', projectId).single();
        if (error) {
            // Handle the specific "no rows returned" error from Supabase
            if (error.code === 'PGRST116') {
                throw new Error(`Project not found or not accessible: ${projectId}`);
            }
            throw error;
        }
        console.log('Raw project data from database:', data);
        // If project has a sandbox, ensure it's started
        if (data.sandbox?.id) {
            // Fire off sandbox activation without blocking
            const ensureSandboxActive = async ()=>{
                try {
                    const { data: { session } } = await supabase.auth.getSession();
                    // For public projects, we don't need authentication
                    const headers = {
                        'Content-Type': 'application/json'
                    };
                    if (session?.access_token) {
                        headers['Authorization'] = `Bearer ${session.access_token}`;
                    }
                    console.log(`Ensuring sandbox is active for project ${projectId}...`);
                    const response = await fetch(`${API_URL}/project/${projectId}/sandbox/ensure-active`, {
                        method: 'POST',
                        headers
                    });
                    if (!response.ok) {
                        const errorText = await response.text().catch(()=>'No error details available');
                        console.warn(`Failed to ensure sandbox is active: ${response.status} ${response.statusText}`, errorText);
                    } else {
                        console.log('Sandbox activation successful');
                    }
                } catch (sandboxError) {
                    console.warn('Failed to ensure sandbox is active:', sandboxError);
                }
            };
            // Start the sandbox activation without awaiting
            ensureSandboxActive();
        }
        // Map database fields to our Project type
        const mappedProject = {
            id: data.project_id,
            name: data.name || '',
            description: data.description || '',
            account_id: data.account_id,
            is_public: data.is_public || false,
            created_at: data.created_at,
            sandbox: data.sandbox || {
                id: '',
                pass: '',
                vnc_preview: '',
                sandbox_url: ''
            }
        };
        // console.log('Mapped project data for frontend:', mappedProject);
        return mappedProject;
    } catch (error) {
        console.error(`Error fetching project ${projectId}:`, error);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(error, {
            operation: 'load project',
            resource: `project ${projectId}`
        });
        throw error;
    }
};
const createProject = async (projectData, accountId)=>{
    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
    // If accountId is not provided, we'll need to get the user's ID
    if (!accountId) {
        const { data: userData, error: userError } = await supabase.auth.getUser();
        if (userError) throw userError;
        if (!userData.user) throw new Error('You must be logged in to create a project');
        // In Basejump, the personal account ID is the same as the user ID
        accountId = userData.user.id;
    }
    const { data, error } = await supabase.from('projects').insert({
        name: projectData.name,
        description: projectData.description || null,
        account_id: accountId
    }).select().single();
    if (error) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(error, {
            operation: 'create project',
            resource: 'project'
        });
        throw error;
    }
    const project = {
        id: data.project_id,
        name: data.name,
        description: data.description || '',
        account_id: data.account_id,
        created_at: data.created_at,
        sandbox: {
            id: '',
            pass: '',
            vnc_preview: ''
        }
    };
    return project;
};
const updateProject = async (projectId, data)=>{
    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
    console.log('Updating project with ID:', projectId);
    console.log('Update data:', data);
    // Sanity check to avoid update errors
    if (!projectId || projectId === '') {
        console.error('Attempted to update project with invalid ID:', projectId);
        throw new Error('Cannot update project: Invalid project ID');
    }
    const { data: updatedData, error } = await supabase.from('projects').update(data).eq('project_id', projectId).select().single();
    if (error) {
        console.error('Error updating project:', error);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(error, {
            operation: 'update project',
            resource: `project ${projectId}`
        });
        throw error;
    }
    if (!updatedData) {
        const noDataError = new Error('No data returned from update');
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(noDataError, {
            operation: 'update project',
            resource: `project ${projectId}`
        });
        throw noDataError;
    }
    // Dispatch a custom event to notify components about the project change
    if ("TURBOPACK compile-time truthy", 1) {
        window.dispatchEvent(new CustomEvent('project-updated', {
            detail: {
                projectId,
                updatedData: {
                    id: updatedData.project_id,
                    name: updatedData.name,
                    description: updatedData.description
                }
            }
        }));
    }
    // Return formatted project data - use same mapping as getProject
    const project = {
        id: updatedData.project_id,
        name: updatedData.name,
        description: updatedData.description || '',
        account_id: updatedData.account_id,
        created_at: updatedData.created_at,
        sandbox: updatedData.sandbox || {
            id: '',
            pass: '',
            vnc_preview: '',
            sandbox_url: ''
        }
    };
    return project;
};
const deleteProject = async (projectId)=>{
    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
    const { error } = await supabase.from('projects').delete().eq('project_id', projectId);
    if (error) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(error, {
            operation: 'delete project',
            resource: `project ${projectId}`
        });
        throw error;
    }
};
const getThreads = async (projectId)=>{
    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
    // Get the current user's ID to filter threads
    const { data: userData, error: userError } = await supabase.auth.getUser();
    if (userError) {
        console.error('Error getting current user:', userError);
        return [];
    }
    // If no user is logged in, return an empty array
    if (!userData.user) {
        console.log('[API] No user logged in, returning empty threads array');
        return [];
    }
    let query = supabase.from('threads').select('*');
    // Always filter by the current user's account ID
    query = query.eq('account_id', userData.user.id);
    if (projectId) {
        console.log('[API] Filtering threads by project_id:', projectId);
        query = query.eq('project_id', projectId);
    }
    const { data, error } = await query;
    if (error) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(error, {
            operation: 'load threads',
            resource: projectId ? `threads for project ${projectId}` : 'threads'
        });
        throw error;
    }
    const mappedThreads = (data || []).filter((thread)=>{
        const metadata = thread.metadata || {};
        return !metadata.is_agent_builder;
    }).map((thread)=>({
            thread_id: thread.thread_id,
            account_id: thread.account_id,
            project_id: thread.project_id,
            created_at: thread.created_at,
            updated_at: thread.updated_at,
            metadata: thread.metadata
        }));
    return mappedThreads;
};
const getThread = async (threadId)=>{
    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
    const { data, error } = await supabase.from('threads').select('*').eq('thread_id', threadId).single();
    if (error) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(error, {
            operation: 'load thread',
            resource: `thread ${threadId}`
        });
        throw error;
    }
    return data;
};
const createThread = async (projectId)=>{
    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
    // If user is not logged in, redirect to login
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
        throw new Error('You must be logged in to create a thread');
    }
    const { data, error } = await supabase.from('threads').insert({
        project_id: projectId,
        account_id: user.id
    }).select().single();
    if (error) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(error, {
            operation: 'create thread',
            resource: 'thread'
        });
        throw error;
    }
    return data;
};
const addUserMessage = async (threadId, content)=>{
    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
    // Format the message in the format the LLM expects - keep it simple with only required fields
    const message = {
        role: 'user',
        content: content
    };
    // Insert the message into the messages table
    const { error } = await supabase.from('messages').insert({
        thread_id: threadId,
        type: 'user',
        is_llm_message: true,
        content: JSON.stringify(message)
    });
    if (error) {
        console.error('Error adding user message:', error);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(error, {
            operation: 'add message',
            resource: 'message'
        });
        throw new Error(`Error adding message: ${error.message}`);
    }
};
const getMessages = async (threadId)=>{
    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
    let allMessages = [];
    let from = 0;
    const batchSize = 1000;
    let hasMore = true;
    while(hasMore){
        const { data, error } = await supabase.from('messages').select(`
        *,
        agents:agent_id (
          name,
          avatar,
          avatar_color
        )
      `).eq('thread_id', threadId).neq('type', 'cost').neq('type', 'summary').order('created_at', {
            ascending: true
        }).range(from, from + batchSize - 1);
        if (error) {
            console.error('Error fetching messages:', error);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(error, {
                operation: 'load messages',
                resource: `messages for thread ${threadId}`
            });
            throw new Error(`Error getting messages: ${error.message}`);
        }
        if (data && data.length > 0) {
            allMessages = allMessages.concat(data);
            from += batchSize;
            hasMore = data.length === batchSize;
        } else {
            hasMore = false;
        }
    }
    console.log('[API] Messages fetched count:', allMessages.length);
    return allMessages;
};
const startAgent = async (threadId, options)=>{
    try {
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.access_token) {
            throw new NoAccessTokenAvailableError();
        }
        // Check if backend URL is configured
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        console.log(`[API] Starting agent for thread ${threadId} using ${API_URL}/thread/${threadId}/agent/start`);
        const defaultOptions = {
            model_name: 'claude-3-7-sonnet-latest',
            enable_thinking: false,
            reasoning_effort: 'low',
            stream: true,
            agent_id: undefined
        };
        const finalOptions = {
            ...defaultOptions,
            ...options
        };
        const body = {
            model_name: finalOptions.model_name,
            enable_thinking: finalOptions.enable_thinking,
            reasoning_effort: finalOptions.reasoning_effort,
            stream: finalOptions.stream
        };
        // Only include agent_id if it's provided
        if (finalOptions.agent_id) {
            body.agent_id = finalOptions.agent_id;
        }
        const response = await fetch(`${API_URL}/thread/${threadId}/agent/start`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${session.access_token}`
            },
            body: JSON.stringify(body)
        });
        if (!response.ok) {
            // Check for 402 Payment Required first
            if (response.status === 402) {
                try {
                    const errorData = await response.json();
                    console.error(`[API] Billing error starting agent (402):`, errorData);
                    // Ensure detail exists and has a message property
                    const detail = errorData?.detail || {
                        message: 'Payment Required'
                    };
                    if (typeof detail.message !== 'string') {
                        detail.message = 'Payment Required'; // Default message if missing
                    }
                    throw new BillingError(response.status, detail);
                } catch (parseError) {
                    // Handle cases where parsing fails or the structure isn't as expected
                    console.error('[API] Could not parse 402 error response body:', parseError);
                    throw new BillingError(response.status, {
                        message: 'Payment Required'
                    }, `Error starting agent: ${response.statusText} (402)`);
                }
            }
            // Handle other errors
            const errorText = await response.text().catch(()=>'No error details available');
            console.error(`[API] Error starting agent: ${response.status} ${response.statusText}`, errorText);
            throw new Error(`Error starting agent: ${response.statusText} (${response.status})`);
        }
        const result = await response.json();
        return result;
    } catch (error) {
        // Rethrow BillingError instances directly
        if (error instanceof BillingError) {
            throw error;
        }
        if (error instanceof NoAccessTokenAvailableError) {
            throw error;
        }
        console.error('[API] Failed to start agent:', error);
        // Handle different error types with appropriate user messages
        if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
            const networkError = new Error(`Cannot connect to backend server. Please check your internet connection and make sure the backend is running.`);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(networkError, {
                operation: 'start agent',
                resource: 'AI assistant'
            });
            throw networkError;
        }
        // For other errors, add context and rethrow
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(error, {
            operation: 'start agent',
            resource: 'AI assistant'
        });
        throw error;
    }
};
const stopAgent = async (agentRunId)=>{
    // Add to non-running set immediately to prevent reconnection attempts
    nonRunningAgentRuns.add(agentRunId);
    // Close any existing stream
    const existingStream = activeStreams.get(agentRunId);
    if (existingStream) {
        console.log(`[API] Closing existing stream for ${agentRunId} before stopping agent`);
        existingStream.close();
        activeStreams.delete(agentRunId);
    }
    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) {
        const authError = new NoAccessTokenAvailableError();
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(authError, {
            operation: 'stop agent',
            resource: 'AI assistant'
        });
        throw authError;
    }
    const response = await fetch(`${API_URL}/agent-run/${agentRunId}/stop`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${session.access_token}`
        },
        // Add cache: 'no-store' to prevent caching
        cache: 'no-store'
    });
    if (!response.ok) {
        const stopError = new Error(`Error stopping agent: ${response.statusText}`);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(stopError, {
            operation: 'stop agent',
            resource: 'AI assistant'
        });
        throw stopError;
    }
};
const getAgentStatus = async (agentRunId)=>{
    console.log(`[API] Requesting agent status for ${agentRunId}`);
    // If we already know this agent is not running, throw an error
    if (nonRunningAgentRuns.has(agentRunId)) {
        console.log(`[API] Agent run ${agentRunId} is known to be non-running, returning error`);
        throw new Error(`Agent run ${agentRunId} is not running`);
    }
    try {
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.access_token) {
            console.error('[API] No access token available for getAgentStatus');
            throw new NoAccessTokenAvailableError();
        }
        const url = `${API_URL}/agent-run/${agentRunId}`;
        console.log(`[API] Fetching from: ${url}`);
        const response = await fetch(url, {
            headers: {
                Authorization: `Bearer ${session.access_token}`
            },
            // Add cache: 'no-store' to prevent caching
            cache: 'no-store'
        });
        if (!response.ok) {
            const errorText = await response.text().catch(()=>'No error details available');
            console.error(`[API] Error getting agent status: ${response.status} ${response.statusText}`, errorText);
            // If we get a 404, add to non-running set
            if (response.status === 404) {
                nonRunningAgentRuns.add(agentRunId);
            }
            throw new Error(`Error getting agent status: ${response.statusText} (${response.status})`);
        }
        const data = await response.json();
        console.log(`[API] Successfully got agent status:`, data);
        // If agent is not running, add to non-running set
        if (data.status !== 'running') {
            nonRunningAgentRuns.add(agentRunId);
        }
        return data;
    } catch (error) {
        console.error('[API] Failed to get agent status:', error);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(error, {
            operation: 'get agent status',
            resource: 'AI assistant status',
            silent: true
        });
        throw error;
    }
};
const getAgentRuns = async (threadId)=>{
    try {
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.access_token) {
            throw new NoAccessTokenAvailableError();
        }
        const response = await fetch(`${API_URL}/thread/${threadId}/agent-runs`, {
            headers: {
                Authorization: `Bearer ${session.access_token}`
            },
            // Add cache: 'no-store' to prevent caching
            cache: 'no-store'
        });
        if (!response.ok) {
            throw new Error(`Error getting agent runs: ${response.statusText}`);
        }
        const data = await response.json();
        return data.agent_runs || [];
    } catch (error) {
        if (error instanceof NoAccessTokenAvailableError) {
            throw error;
        }
        console.error('Failed to get agent runs:', error);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(error, {
            operation: 'load agent runs',
            resource: 'conversation history'
        });
        throw error;
    }
};
const streamAgent = (agentRunId, callbacks)=>{
    console.log(`[STREAM] streamAgent called for ${agentRunId}`);
    // Check if this agent run is known to be non-running
    if (nonRunningAgentRuns.has(agentRunId)) {
        console.log(`[STREAM] Agent run ${agentRunId} is known to be non-running, not creating stream`);
        // Notify the caller immediately
        setTimeout(()=>{
            callbacks.onError(`Agent run ${agentRunId} is not running`);
            callbacks.onClose();
        }, 0);
        // Return a no-op cleanup function
        return ()=>{};
    }
    // Check if there's already an active stream for this agent run
    const existingStream = activeStreams.get(agentRunId);
    if (existingStream) {
        console.log(`[STREAM] Stream already exists for ${agentRunId}, closing it first`);
        existingStream.close();
        activeStreams.delete(agentRunId);
    }
    // Set up a new stream
    try {
        const setupStream = async ()=>{
            // First verify the agent is actually running
            try {
                const status = await getAgentStatus(agentRunId);
                if (status.status !== 'running') {
                    console.log(`[STREAM] Agent run ${agentRunId} is not running (status: ${status.status}), not creating stream`);
                    nonRunningAgentRuns.add(agentRunId);
                    callbacks.onError(`Agent run ${agentRunId} is not running (status: ${status.status})`);
                    callbacks.onClose();
                    return;
                }
            } catch (err) {
                console.error(`[STREAM] Error verifying agent run ${agentRunId}:`, err);
                // Check if this is a "not found" error
                const errorMessage = err instanceof Error ? err.message : String(err);
                const isNotFoundError = errorMessage.includes('not found') || errorMessage.includes('404') || errorMessage.includes('does not exist');
                if (isNotFoundError) {
                    console.log(`[STREAM] Agent run ${agentRunId} not found, not creating stream`);
                    nonRunningAgentRuns.add(agentRunId);
                }
                callbacks.onError(errorMessage);
                callbacks.onClose();
                return;
            }
            const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
            const { data: { session } } = await supabase.auth.getSession();
            if (!session?.access_token) {
                const authError = new NoAccessTokenAvailableError();
                console.error('[STREAM] No auth token available');
                callbacks.onError(authError);
                callbacks.onClose();
                return;
            }
            const url = new URL(`${API_URL}/agent-run/${agentRunId}/stream`);
            url.searchParams.append('token', session.access_token);
            console.log(`[STREAM] Creating EventSource for ${agentRunId}`);
            const eventSource = new EventSource(url.toString());
            // Store the EventSource in the active streams map
            activeStreams.set(agentRunId, eventSource);
            eventSource.onopen = ()=>{
                console.log(`[STREAM] Connection opened for ${agentRunId}`);
            };
            eventSource.onmessage = (event)=>{
                try {
                    const rawData = event.data;
                    if (rawData.includes('"type":"ping"')) return;
                    // Log raw data for debugging (truncated for readability)
                    console.log(`[STREAM] Received data for ${agentRunId}: ${rawData.substring(0, 100)}${rawData.length > 100 ? '...' : ''}`);
                    // Skip empty messages
                    if (!rawData || rawData.trim() === '') {
                        console.debug('[STREAM] Received empty message, skipping');
                        return;
                    }
                    // Check for error status messages
                    try {
                        const jsonData = JSON.parse(rawData);
                        if (jsonData.status === 'error') {
                            console.error(`[STREAM] Error status received for ${agentRunId}:`, jsonData);
                            // Pass the error message to the callback
                            callbacks.onError(jsonData.message || 'Unknown error occurred');
                            // Don't close the stream for error status messages as they may continue
                            return;
                        }
                    } catch (jsonError) {
                    // Not JSON or invalid JSON, continue with normal processing
                    }
                    // Check for "Agent run not found" error
                    if (rawData.includes('Agent run') && rawData.includes('not found in active runs')) {
                        console.log(`[STREAM] Agent run ${agentRunId} not found in active runs, closing stream`);
                        // Add to non-running set to prevent future reconnection attempts
                        nonRunningAgentRuns.add(agentRunId);
                        // Notify about the error
                        callbacks.onError('Agent run not found in active runs');
                        // Clean up
                        eventSource.close();
                        activeStreams.delete(agentRunId);
                        callbacks.onClose();
                        return;
                    }
                    // Check for completion messages
                    if (rawData.includes('"type":"status"') && rawData.includes('"status":"completed"')) {
                        console.log(`[STREAM] Detected completion status message for ${agentRunId}`);
                        // Check for specific completion messages that indicate we should stop checking
                        if (rawData.includes('Run data not available for streaming') || rawData.includes('Stream ended with status: completed')) {
                            console.log(`[STREAM] Detected final completion message for ${agentRunId}, adding to non-running set`);
                            // Add to non-running set to prevent future reconnection attempts
                            nonRunningAgentRuns.add(agentRunId);
                        }
                        // Notify about the message
                        callbacks.onMessage(rawData);
                        // Clean up
                        eventSource.close();
                        activeStreams.delete(agentRunId);
                        callbacks.onClose();
                        return;
                    }
                    // Check for thread run end message
                    if (rawData.includes('"type":"status"') && rawData.includes('"status_type":"thread_run_end"')) {
                        console.log(`[STREAM] Detected thread run end message for ${agentRunId}`);
                        // Add to non-running set
                        nonRunningAgentRuns.add(agentRunId);
                        // Notify about the message
                        callbacks.onMessage(rawData);
                        // Clean up
                        eventSource.close();
                        activeStreams.delete(agentRunId);
                        callbacks.onClose();
                        return;
                    }
                    // For all other messages, just pass them through
                    callbacks.onMessage(rawData);
                } catch (error) {
                    console.error(`[STREAM] Error handling message:`, error);
                    callbacks.onError(error instanceof Error ? error : String(error));
                }
            };
            eventSource.onerror = (event)=>{
                console.log(`[STREAM] EventSource error for ${agentRunId}:`, event);
                // Check if the agent is still running
                getAgentStatus(agentRunId).then((status)=>{
                    if (status.status !== 'running') {
                        console.log(`[STREAM] Agent run ${agentRunId} is not running after error, closing stream`);
                        nonRunningAgentRuns.add(agentRunId);
                        eventSource.close();
                        activeStreams.delete(agentRunId);
                        callbacks.onClose();
                    } else {
                        console.log(`[STREAM] Agent run ${agentRunId} is still running after error, keeping stream open`);
                    // Let the browser handle reconnection for non-fatal errors
                    }
                }).catch((err)=>{
                    console.error(`[STREAM] Error checking agent status after stream error:`, err);
                    // Check if this is a "not found" error
                    const errMsg = err instanceof Error ? err.message : String(err);
                    const isNotFoundErr = errMsg.includes('not found') || errMsg.includes('404') || errMsg.includes('does not exist');
                    if (isNotFoundErr) {
                        console.log(`[STREAM] Agent run ${agentRunId} not found after error, closing stream`);
                        nonRunningAgentRuns.add(agentRunId);
                        eventSource.close();
                        activeStreams.delete(agentRunId);
                        callbacks.onClose();
                    }
                    // For other errors, notify but don't close the stream
                    callbacks.onError(errMsg);
                });
            };
        };
        // Start the stream setup
        setupStream();
        // Return a cleanup function
        return ()=>{
            console.log(`[STREAM] Cleanup called for ${agentRunId}`);
            const stream = activeStreams.get(agentRunId);
            if (stream) {
                console.log(`[STREAM] Closing stream for ${agentRunId}`);
                stream.close();
                activeStreams.delete(agentRunId);
            }
        };
    } catch (error) {
        console.error(`[STREAM] Error setting up stream for ${agentRunId}:`, error);
        callbacks.onError(error instanceof Error ? error : String(error));
        callbacks.onClose();
        return ()=>{};
    }
};
const createSandboxFile = async (sandboxId, filePath, content)=>{
    try {
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { session } } = await supabase.auth.getSession();
        // Use FormData to handle both text and binary content more reliably
        const formData = new FormData();
        formData.append('path', filePath);
        // Create a Blob from the content string and append as a file
        const blob = new Blob([
            content
        ], {
            type: 'application/octet-stream'
        });
        formData.append('file', blob, filePath.split('/').pop() || 'file');
        const headers = {};
        if (session?.access_token) {
            headers['Authorization'] = `Bearer ${session.access_token}`;
        }
        const response = await fetch(`${API_URL}/sandboxes/${sandboxId}/files`, {
            method: 'POST',
            headers,
            body: formData
        });
        if (!response.ok) {
            const errorText = await response.text().catch(()=>'No error details available');
            console.error(`Error creating sandbox file: ${response.status} ${response.statusText}`, errorText);
            throw new Error(`Error creating sandbox file: ${response.statusText} (${response.status})`);
        }
        const result = await response.json();
        return result;
    } catch (error) {
        console.error('Failed to create sandbox file:', error);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(error, {
            operation: 'create file',
            resource: `file ${filePath}`
        });
        throw error;
    }
};
const createSandboxFileJson = async (sandboxId, filePath, content)=>{
    try {
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { session } } = await supabase.auth.getSession();
        const headers = {
            'Content-Type': 'application/json'
        };
        if (session?.access_token) {
            headers['Authorization'] = `Bearer ${session.access_token}`;
        }
        const response = await fetch(`${API_URL}/sandboxes/${sandboxId}/files/json`, {
            method: 'POST',
            headers,
            body: JSON.stringify({
                path: filePath,
                content: content
            })
        });
        if (!response.ok) {
            const errorText = await response.text().catch(()=>'No error details available');
            console.error(`Error creating sandbox file (JSON): ${response.status} ${response.statusText}`, errorText);
            throw new Error(`Error creating sandbox file: ${response.statusText} (${response.status})`);
        }
        const result = await response.json();
        return result;
    } catch (error) {
        console.error('Failed to create sandbox file with JSON:', error);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(error, {
            operation: 'create file',
            resource: `file ${filePath}`
        });
        throw error;
    }
};
// Helper function to normalize file paths with Unicode characters
function normalizePathWithUnicode(path) {
    try {
        // Replace escaped Unicode sequences with actual characters
        return path.replace(/\\u([0-9a-fA-F]{4})/g, (_, hexCode)=>{
            return String.fromCharCode(parseInt(hexCode, 16));
        });
    } catch (e) {
        console.error('Error processing Unicode escapes in path:', e);
        return path;
    }
}
const listSandboxFiles = async (sandboxId, path)=>{
    try {
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { session } } = await supabase.auth.getSession();
        const url = new URL(`${API_URL}/sandboxes/${sandboxId}/files`);
        // Normalize the path to handle Unicode escape sequences
        const normalizedPath = normalizePathWithUnicode(path);
        // Properly encode the path parameter for UTF-8 support
        url.searchParams.append('path', normalizedPath);
        const headers = {};
        if (session?.access_token) {
            headers['Authorization'] = `Bearer ${session.access_token}`;
        }
        const response = await fetch(url.toString(), {
            headers
        });
        if (!response.ok) {
            const errorText = await response.text().catch(()=>'No error details available');
            console.error(`Error listing sandbox files: ${response.status} ${response.statusText}`, errorText);
            throw new Error(`Error listing sandbox files: ${response.statusText} (${response.status})`);
        }
        const data = await response.json();
        return data.files || [];
    } catch (error) {
        console.error('Failed to list sandbox files:', error);
        // handleApiError(error, { operation: 'list files', resource: `directory ${path}` });
        throw error;
    }
};
const getSandboxFileContent = async (sandboxId, path)=>{
    try {
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { session } } = await supabase.auth.getSession();
        const url = new URL(`${API_URL}/sandboxes/${sandboxId}/files/content`);
        // Normalize the path to handle Unicode escape sequences
        const normalizedPath = normalizePathWithUnicode(path);
        // Properly encode the path parameter for UTF-8 support
        url.searchParams.append('path', normalizedPath);
        const headers = {};
        if (session?.access_token) {
            headers['Authorization'] = `Bearer ${session.access_token}`;
        }
        const response = await fetch(url.toString(), {
            headers
        });
        if (!response.ok) {
            const errorText = await response.text().catch(()=>'No error details available');
            console.error(`Error getting sandbox file content: ${response.status} ${response.statusText}`, errorText);
            throw new Error(`Error getting sandbox file content: ${response.statusText} (${response.status})`);
        }
        // Check if it's a text file or binary file based on content-type
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('text') || contentType?.includes('application/json')) {
            return await response.text();
        } else {
            return await response.blob();
        }
    } catch (error) {
        console.error('Failed to get sandbox file content:', error);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(error, {
            operation: 'load file content',
            resource: `file ${path}`
        });
        throw error;
    }
};
const getPublicProjects = async ()=>{
    try {
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
        // Query for threads that are marked as public
        const { data: publicThreads, error: threadsError } = await supabase.from('threads').select('project_id').eq('is_public', true);
        if (threadsError) {
            console.error('Error fetching public threads:', threadsError);
            return [];
        }
        // If no public threads found, return empty array
        if (!publicThreads?.length) {
            return [];
        }
        // Extract unique project IDs from public threads
        const publicProjectIds = [
            ...new Set(publicThreads.map((thread)=>thread.project_id))
        ].filter(Boolean);
        // If no valid project IDs, return empty array
        if (!publicProjectIds.length) {
            return [];
        }
        // Get the projects that have public threads
        const { data: projects, error: projectsError } = await supabase.from('projects').select('*').in('project_id', publicProjectIds);
        if (projectsError) {
            console.error('Error fetching public projects:', projectsError);
            return [];
        }
        console.log('[API] Raw public projects from DB:', projects?.length, projects);
        // Map database fields to our Project type
        const mappedProjects = (projects || []).map((project)=>({
                id: project.project_id,
                name: project.name || '',
                description: project.description || '',
                account_id: project.account_id,
                created_at: project.created_at,
                updated_at: project.updated_at,
                sandbox: project.sandbox || {
                    id: '',
                    pass: '',
                    vnc_preview: '',
                    sandbox_url: ''
                },
                is_public: true
            }));
        console.log('[API] Mapped public projects for frontend:', mappedProjects.length);
        return mappedProjects;
    } catch (err) {
        console.error('Error fetching public projects:', err);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(err, {
            operation: 'load public projects',
            resource: 'public projects'
        });
        return [];
    }
};
const initiateAgent = async (formData)=>{
    try {
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.access_token) {
            throw new NoAccessTokenAvailableError();
        }
        if ("TURBOPACK compile-time falsy", 0) {
            "TURBOPACK unreachable";
        }
        console.log(`[API] Initiating agent with files using ${API_URL}/agent/initiate`);
        const response = await fetch(`${API_URL}/agent/initiate`, {
            method: 'POST',
            headers: {
                Authorization: `Bearer ${session.access_token}`
            },
            body: formData,
            cache: 'no-store'
        });
        if (!response.ok) {
            const errorText = await response.text().catch(()=>'No error details available');
            console.error(`[API] Error initiating agent: ${response.status} ${response.statusText}`, errorText);
            if (response.status === 402) {
                throw new Error('Payment Required');
            } else if (response.status === 401) {
                throw new Error('Authentication error: Please sign in again');
            } else if (response.status >= 500) {
                throw new Error('Server error: Please try again later');
            }
            throw new Error(`Error initiating agent: ${response.statusText} (${response.status})`);
        }
        const result = await response.json();
        return result;
    } catch (error) {
        console.error('[API] Failed to initiate agent:', error);
        if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
            const networkError = new Error(`Cannot connect to backend server. Please check your internet connection and make sure the backend is running.`);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(networkError, {
                operation: 'initiate agent',
                resource: 'AI assistant'
            });
            throw networkError;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(error, {
            operation: 'initiate agent'
        });
        throw error;
    }
};
const checkApiHealth = async ()=>{
    try {
        const response = await fetch(`${API_URL}/health`, {
            cache: 'no-store'
        });
        if (!response.ok) {
            throw new Error(`API health check failed: ${response.statusText}`);
        }
        return response.json();
    } catch (error) {
        throw error;
    }
};
const createCheckoutSession = async (request)=>{
    try {
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.access_token) {
            throw new NoAccessTokenAvailableError();
        }
        const requestBody = {
            ...request,
            tolt_referral: window.tolt_referral
        };
        console.log('Tolt Referral ID:', requestBody.tolt_referral);
        const response = await fetch(`${API_URL}/billing/create-checkout-session`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${session.access_token}`
            },
            body: JSON.stringify(requestBody)
        });
        if (!response.ok) {
            const errorText = await response.text().catch(()=>'No error details available');
            console.error(`Error creating checkout session: ${response.status} ${response.statusText}`, errorText);
            throw new Error(`Error creating checkout session: ${response.statusText} (${response.status})`);
        }
        const data = await response.json();
        console.log('Checkout session response:', data);
        // Handle all possible statuses
        switch(data.status){
            case 'upgraded':
            case 'updated':
            case 'downgrade_scheduled':
            case 'scheduled':
            case 'no_change':
                return data;
            case 'new':
            case 'checkout_created':
                if (!data.url) {
                    throw new Error('No checkout URL provided');
                }
                return data;
            default:
                console.warn('Unexpected status from createCheckoutSession:', data.status);
                return data;
        }
    } catch (error) {
        console.error('Failed to create checkout session:', error);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(error, {
            operation: 'create checkout session',
            resource: 'billing'
        });
        throw error;
    }
};
const createPortalSession = async (request)=>{
    try {
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.access_token) {
            throw new NoAccessTokenAvailableError();
        }
        const response = await fetch(`${API_URL}/billing/create-portal-session`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                Authorization: `Bearer ${session.access_token}`
            },
            body: JSON.stringify(request)
        });
        if (!response.ok) {
            const errorText = await response.text().catch(()=>'No error details available');
            console.error(`Error creating portal session: ${response.status} ${response.statusText}`, errorText);
            throw new Error(`Error creating portal session: ${response.statusText} (${response.status})`);
        }
        return response.json();
    } catch (error) {
        console.error('Failed to create portal session:', error);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(error, {
            operation: 'create portal session',
            resource: 'billing portal'
        });
        throw error;
    }
};
const getSubscription = async ()=>{
    try {
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.access_token) {
            throw new NoAccessTokenAvailableError();
        }
        const response = await fetch(`${API_URL}/billing/subscription`, {
            headers: {
                Authorization: `Bearer ${session.access_token}`
            }
        });
        if (!response.ok) {
            const errorText = await response.text().catch(()=>'No error details available');
            console.error(`Error getting subscription: ${response.status} ${response.statusText}`, errorText);
            throw new Error(`Error getting subscription: ${response.statusText} (${response.status})`);
        }
        return response.json();
    } catch (error) {
        if (error instanceof NoAccessTokenAvailableError) {
            throw error;
        }
        console.error('Failed to get subscription:', error);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(error, {
            operation: 'load subscription',
            resource: 'billing information'
        });
        throw error;
    }
};
const getAvailableModels = async ()=>{
    try {
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.access_token) {
            throw new NoAccessTokenAvailableError();
        }
        const response = await fetch(`${API_URL}/billing/available-models`, {
            headers: {
                Authorization: `Bearer ${session.access_token}`
            }
        });
        if (!response.ok) {
            const errorText = await response.text().catch(()=>'No error details available');
            console.error(`Error getting available models: ${response.status} ${response.statusText}`, errorText);
            throw new Error(`Error getting available models: ${response.statusText} (${response.status})`);
        }
        return response.json();
    } catch (error) {
        if (error instanceof NoAccessTokenAvailableError) {
            throw error;
        }
        console.error('Failed to get available models:', error);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(error, {
            operation: 'load available models',
            resource: 'AI models'
        });
        throw error;
    }
};
const checkBillingStatus = async ()=>{
    try {
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.access_token) {
            throw new NoAccessTokenAvailableError();
        }
        const response = await fetch(`${API_URL}/billing/check-status`, {
            headers: {
                Authorization: `Bearer ${session.access_token}`
            }
        });
        if (!response.ok) {
            const errorText = await response.text().catch(()=>'No error details available');
            console.error(`Error checking billing status: ${response.status} ${response.statusText}`, errorText);
            throw new Error(`Error checking billing status: ${response.statusText} (${response.status})`);
        }
        return response.json();
    } catch (error) {
        if (error instanceof NoAccessTokenAvailableError) {
            throw error;
        }
        console.error('Failed to check billing status:', error);
        throw error;
    }
};
const transcribeAudio = async (audioFile)=>{
    try {
        const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
        const { data: { session } } = await supabase.auth.getSession();
        if (!session?.access_token) {
            throw new NoAccessTokenAvailableError();
        }
        const formData = new FormData();
        formData.append('audio_file', audioFile);
        const response = await fetch(`${API_URL}/transcription`, {
            method: 'POST',
            headers: {
                Authorization: `Bearer ${session.access_token}`
            },
            body: formData
        });
        if (!response.ok) {
            const errorText = await response.text().catch(()=>'No error details available');
            console.error(`Error transcribing audio: ${response.status} ${response.statusText}`, errorText);
            throw new Error(`Error transcribing audio: ${response.statusText} (${response.status})`);
        }
        return response.json();
    } catch (error) {
        if (error instanceof NoAccessTokenAvailableError) {
            throw error;
        }
        console.error('Failed to transcribe audio:', error);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(error, {
            operation: 'transcribe audio',
            resource: 'speech-to-text'
        });
        throw error;
    }
};
const getAgentBuilderChatHistory = async (agentId)=>{
    const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2f$client$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createClient"])();
    const { data: { session } } = await supabase.auth.getSession();
    if (!session?.access_token) {
        throw new NoAccessTokenAvailableError();
    }
    const response = await fetch(`${API_URL}/agents/${agentId}/builder-chat-history`, {
        headers: {
            Authorization: `Bearer ${session.access_token}`
        }
    });
    if (!response.ok) {
        const errorText = await response.text().catch(()=>'No error details available');
        console.error(`Error getting agent builder chat history: ${response.status} ${response.statusText}`, errorText);
        throw new Error(`Error getting agent builder chat history: ${response.statusText}`);
    }
    const data = await response.json();
    console.log('[API] Agent builder chat history fetched:', data);
    return data;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/error-handler.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "handleApiError": (()=>handleApiError),
    "handleApiInfo": (()=>handleApiInfo),
    "handleApiSuccess": (()=>handleApiSuccess),
    "handleApiWarning": (()=>handleApiWarning),
    "handleNetworkError": (()=>handleNetworkError)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api.ts [app-client] (ecmascript)");
;
;
const getStatusMessage = (status)=>{
    switch(status){
        case 400:
            return 'Invalid request. Please check your input and try again.';
        case 401:
            return 'Authentication required. Please sign in again.';
        case 403:
            return 'Access denied. You don\'t have permission to perform this action.';
        case 404:
            return 'The requested resource was not found.';
        case 408:
            return 'Request timeout. Please try again.';
        case 409:
            return 'Conflict detected. The resource may have been modified by another user.';
        case 422:
            return 'Invalid data provided. Please check your input.';
        case 429:
            return 'Too many requests. Please wait a moment and try again.';
        case 500:
            return 'Server error. Our team has been notified.';
        case 502:
            return 'Service temporarily unavailable. Please try again in a moment.';
        case 503:
            return 'Service maintenance in progress. Please try again later.';
        case 504:
            return 'Request timeout. The server took too long to respond.';
        default:
            return 'An unexpected error occurred. Please try again.';
    }
};
const extractErrorMessage = (error)=>{
    if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BillingError"]) {
        return error.detail?.message || error.message || 'Billing issue detected';
    }
    if (error instanceof Error) {
        return error.message;
    }
    if (error?.response) {
        const status = error.response.status;
        return getStatusMessage(status);
    }
    if (error?.status) {
        return getStatusMessage(error.status);
    }
    if (typeof error === 'string') {
        return error;
    }
    if (error?.message) {
        return error.message;
    }
    if (error?.error) {
        return typeof error.error === 'string' ? error.error : error.error.message || 'Unknown error';
    }
    return 'An unexpected error occurred';
};
const shouldShowError = (error, context)=>{
    if (context?.silent) {
        return false;
    }
    if (error instanceof __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BillingError"]) {
        return false;
    }
    if (error?.status === 404 && context?.resource) {
        return false;
    }
    return true;
};
const formatErrorMessage = (message, context)=>{
    if (!context?.operation && !context?.resource) {
        return message;
    }
    const parts = [];
    if (context.operation) {
        parts.push(`Failed to ${context.operation}`);
    }
    if (context.resource) {
        parts.push(context.resource);
    }
    const prefix = parts.join(' ');
    if (message.toLowerCase().includes(context.operation?.toLowerCase() || '')) {
        return message;
    }
    return `${prefix}: ${message}`;
};
const handleApiError = (error, context)=>{
    console.error('API Error:', error, context);
    if (!shouldShowError(error, context)) {
        return;
    }
    const rawMessage = extractErrorMessage(error);
    const formattedMessage = formatErrorMessage(rawMessage, context);
    if (error?.status >= 500) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(formattedMessage, {
            description: 'Our team has been notified and is working on a fix.',
            duration: 6000
        });
    } else if (error?.status === 401) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(formattedMessage, {
            description: 'Please refresh the page and sign in again.',
            duration: 8000
        });
    } else if (error?.status === 403) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(formattedMessage, {
            description: 'Contact support if you believe this is an error.',
            duration: 6000
        });
    } else if (error?.status === 429) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].warning(formattedMessage, {
            description: 'Please wait a moment before trying again.',
            duration: 5000
        });
    } else {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(formattedMessage, {
            duration: 5000
        });
    }
};
const handleNetworkError = (error, context)=>{
    const isNetworkError = error?.message?.includes('fetch') || error?.message?.includes('network') || error?.message?.includes('connection') || error?.code === 'NETWORK_ERROR' || !navigator.onLine;
    if (isNetworkError) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error('Connection error', {
            description: 'Please check your internet connection and try again.',
            duration: 6000
        });
    } else {
        handleApiError(error, context);
    }
};
const handleApiSuccess = (message, description)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(message, {
        description,
        duration: 3000
    });
};
const handleApiWarning = (message, description)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].warning(message, {
        description,
        duration: 4000
    });
};
const handleApiInfo = (message, description)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].info(message, {
        description,
        duration: 3000
    });
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/providers/react-query-provider.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ReactQueryProvider": (()=>ReactQueryProvider)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$HydrationBoundary$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/HydrationBoundary.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/error-handler.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function ReactQueryProvider({ children, dehydratedState }) {
    _s();
    const [queryClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "ReactQueryProvider.useState": ()=>new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClient"]({
                defaultOptions: {
                    queries: {
                        staleTime: 20 * 1000,
                        gcTime: 5 * 60 * 1000,
                        retry: {
                            "ReactQueryProvider.useState": (failureCount, error)=>{
                                if (error?.status >= 400 && error?.status < 500) return false;
                                if (error?.status === 404) return false;
                                return failureCount < 3;
                            }
                        }["ReactQueryProvider.useState"],
                        refetchOnMount: true,
                        refetchOnWindowFocus: true,
                        refetchOnReconnect: 'always'
                    },
                    mutations: {
                        retry: {
                            "ReactQueryProvider.useState": (failureCount, error)=>{
                                if (error?.status >= 400 && error?.status < 500) return false;
                                return failureCount < 1;
                            }
                        }["ReactQueryProvider.useState"],
                        onError: {
                            "ReactQueryProvider.useState": (error, variables, context)=>{
                                (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$error$2d$handler$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["handleApiError"])(error, {
                                    operation: 'perform action',
                                    silent: false
                                });
                            }
                        }["ReactQueryProvider.useState"]
                    }
                }
            })
    }["ReactQueryProvider.useState"]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClientProvider"], {
        client: queryClient,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$HydrationBoundary$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["HydrationBoundary"], {
            state: dehydratedState,
            children: children
        }, void 0, false, {
            fileName: "[project]/src/providers/react-query-provider.tsx",
            lineNumber: 53,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/providers/react-query-provider.tsx",
        lineNumber: 52,
        columnNumber: 5
    }, this);
}
_s(ReactQueryProvider, "7n23jyqGy7kYn3q0pXmljMoswFE=");
_c = ReactQueryProvider;
var _c;
__turbopack_context__.k.register(_c, "ReactQueryProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/providers.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Providers": (()=>Providers),
    "ToolCallsContext": (()=>ToolCallsContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$AuthProvider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/AuthProvider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$react$2d$query$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/providers/react-query-provider.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$hydration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/hydration.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/query-core/build/modern/queryClient.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
const ToolCallsContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])({
    toolCalls: [],
    setToolCalls: ()=>{}
});
function Providers({ children }) {
    _s();
    // Shared state for tool calls across the app
    const [toolCalls, setToolCalls] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const queryClient = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$queryClient$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["QueryClient"]();
    const dehydratedState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$query$2d$core$2f$build$2f$modern$2f$hydration$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dehydrate"])(queryClient);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$AuthProvider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AuthProvider"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ToolCallsContext.Provider, {
            value: {
                toolCalls,
                setToolCalls
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ThemeProvider"], {
                attribute: "class",
                defaultTheme: "system",
                enableSystem: true,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$providers$2f$react$2d$query$2d$provider$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ReactQueryProvider"], {
                    dehydratedState: dehydratedState,
                    children: children
                }, void 0, false, {
                    fileName: "[project]/src/app/providers.tsx",
                    lineNumber: 47,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/providers.tsx",
                lineNumber: 46,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/providers.tsx",
            lineNumber: 45,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/providers.tsx",
        lineNumber: 44,
        columnNumber: 5
    }, this);
}
_s(Providers, "wDU7cGkKloOmLYf97AR0DILHKlw=");
_c = Providers;
var _c;
__turbopack_context__.k.register(_c, "Providers");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/ui/sonner.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "Toaster": (()=>Toaster)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next-themes/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
const Toaster = ({ ...props })=>{
    _s();
    const { theme = 'system' } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Toaster"], {
        theme: theme,
        className: "toaster group",
        style: {
            '--normal-bg': 'var(--popover)',
            '--normal-text': 'var(--popover-foreground)',
            '--normal-border': 'var(--border)',
            '--border-radius': '1rem'
        },
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/sonner.tsx",
        lineNumber: 10,
        columnNumber: 5
    }, this);
};
_s(Toaster, "bbCbBsvL7+LiaR8ofHlkcwveh/Y=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$themes$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTheme"]
    ];
});
_c = Toaster;
;
var _c;
__turbopack_context__.k.register(_c, "Toaster");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_f229f92d._.js.map