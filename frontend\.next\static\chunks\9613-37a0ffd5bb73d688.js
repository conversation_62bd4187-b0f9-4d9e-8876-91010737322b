"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9613],{89613:(e,t,r)=>{r.d(t,{Bc:()=>E,Kq:()=>Y,UC:()=>K,ZL:()=>G,bL:()=>Z,i3:()=>W,k$:()=>P,l9:()=>z});var n=r(12115),o=r(85185),l=r(6101),i=r(46081),a=r(19178),s=r(61285),u=r(35152),c=r(34378),p=r(28905),d=r(63655),f=r(99708),x=r(5845),h=r(2564),v=r(95155),[g,y]=(0,i.A)("Tooltip",[u.Bk]),b=(0,u.Bk)(),m="TooltipProvider",w="tooltip.open",[C,T]=g(m),E=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:l=!1,children:i}=e,a=n.useRef(!0),s=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,v.jsx)(C,{scope:t,isOpenDelayedRef:a,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),a.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>a.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:n.useCallback(e=>{s.current=e},[]),disableHoverableContent:l,children:i})};E.displayName=m;var k="Tooltip",[L,R]=g(k),j=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:l,onOpenChange:i,disableHoverableContent:a,delayDuration:c}=e,p=T(k,e.__scopeTooltip),d=b(t),[f,h]=n.useState(null),g=(0,s.B)(),y=n.useRef(0),m=null!=a?a:p.disableHoverableContent,C=null!=c?c:p.delayDuration,E=n.useRef(!1),[R,j]=(0,x.i)({prop:o,defaultProp:null!=l&&l,onChange:e=>{e?(p.onOpen(),document.dispatchEvent(new CustomEvent(w))):p.onClose(),null==i||i(e)},caller:k}),_=n.useMemo(()=>R?E.current?"delayed-open":"instant-open":"closed",[R]),P=n.useCallback(()=>{window.clearTimeout(y.current),y.current=0,E.current=!1,j(!0)},[j]),M=n.useCallback(()=>{window.clearTimeout(y.current),y.current=0,j(!1)},[j]),D=n.useCallback(()=>{window.clearTimeout(y.current),y.current=window.setTimeout(()=>{E.current=!0,j(!0),y.current=0},C)},[C,j]);return n.useEffect(()=>()=>{y.current&&(window.clearTimeout(y.current),y.current=0)},[]),(0,v.jsx)(u.bL,{...d,children:(0,v.jsx)(L,{scope:t,contentId:g,open:R,stateAttribute:_,trigger:f,onTriggerChange:h,onTriggerEnter:n.useCallback(()=>{p.isOpenDelayedRef.current?D():P()},[p.isOpenDelayedRef,D,P]),onTriggerLeave:n.useCallback(()=>{m?M():(window.clearTimeout(y.current),y.current=0)},[M,m]),onOpen:P,onClose:M,disableHoverableContent:m,children:r})})};j.displayName=k;var _="TooltipTrigger",P=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...i}=e,a=R(_,r),s=T(_,r),c=b(r),p=n.useRef(null),f=(0,l.s)(t,p,a.onTriggerChange),x=n.useRef(!1),h=n.useRef(!1),g=n.useCallback(()=>x.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),(0,v.jsx)(u.Mz,{asChild:!0,...c,children:(0,v.jsx)(d.sG.button,{"aria-describedby":a.open?a.contentId:void 0,"data-state":a.stateAttribute,...i,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(h.current||s.isPointerInTransitRef.current||(a.onTriggerEnter(),h.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{a.onTriggerLeave(),h.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{a.open&&a.onClose(),x.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{x.current||a.onOpen()}),onBlur:(0,o.m)(e.onBlur,a.onClose),onClick:(0,o.m)(e.onClick,a.onClose)})})});P.displayName=_;var M="TooltipPortal",[D,B]=g(M,{forceMount:void 0}),N=e=>{let{__scopeTooltip:t,forceMount:r,children:n,container:o}=e,l=R(M,t);return(0,v.jsx)(D,{scope:t,forceMount:r,children:(0,v.jsx)(p.C,{present:r||l.open,children:(0,v.jsx)(c.Z,{asChild:!0,container:o,children:n})})})};N.displayName=M;var O="TooltipContent",I=n.forwardRef((e,t)=>{let r=B(O,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...l}=e,i=R(O,e.__scopeTooltip);return(0,v.jsx)(p.C,{present:n||i.open,children:i.disableHoverableContent?(0,v.jsx)(S,{side:o,...l,ref:t}):(0,v.jsx)(A,{side:o,...l,ref:t})})}),A=n.forwardRef((e,t)=>{let r=R(O,e.__scopeTooltip),o=T(O,e.__scopeTooltip),i=n.useRef(null),a=(0,l.s)(t,i),[s,u]=n.useState(null),{trigger:c,onClose:p}=r,d=i.current,{onPointerInTransitChange:f}=o,x=n.useCallback(()=>{u(null),f(!1)},[f]),h=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),l=Math.abs(t.left-e.x);switch(Math.min(r,n,o,l)){case l:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),f(!0)},[f]);return n.useEffect(()=>()=>x(),[x]),n.useEffect(()=>{if(c&&d){let e=e=>h(e,d),t=e=>h(e,c);return c.addEventListener("pointerleave",e),d.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),d.removeEventListener("pointerleave",t)}}},[c,d,h,x]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=(null==c?void 0:c.contains(t))||(null==d?void 0:d.contains(t)),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,l=t.length-1;e<t.length;l=e++){let i=t[e],a=t[l],s=i.x,u=i.y,c=a.x,p=a.y;u>n!=p>n&&r<(c-s)*(n-u)/(p-u)+s&&(o=!o)}return o}(r,s);n?x():o&&(x(),p())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,d,s,p,x]),(0,v.jsx)(S,{...e,ref:a})}),[F,H]=g(k,{isInside:!1}),q=(0,f.Dc)("TooltipContent"),S=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":l,onEscapeKeyDown:i,onPointerDownOutside:s,...c}=e,p=R(O,r),d=b(r),{onClose:f}=p;return n.useEffect(()=>(document.addEventListener(w,f),()=>document.removeEventListener(w,f)),[f]),n.useEffect(()=>{if(p.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(p.trigger))&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[p.trigger,f]),(0,v.jsx)(a.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,v.jsxs)(u.UC,{"data-state":p.stateAttribute,...d,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,v.jsx)(q,{children:o}),(0,v.jsx)(F,{scope:r,isInside:!0,children:(0,v.jsx)(h.bL,{id:p.contentId,role:"tooltip",children:l||o})})]})})});I.displayName=O;var U="TooltipArrow",X=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=b(r);return H(U,r).isInside?null:(0,v.jsx)(u.i3,{...o,...n,ref:t})});X.displayName=U;var Y=E,Z=j,z=P,G=N,K=I,W=X}}]);