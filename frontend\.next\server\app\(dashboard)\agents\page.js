(()=>{var e={};e.id=797,e.ids=[797],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4573:e=>{"use strict";e.exports=require("node:buffer")},10022:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13861:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14719:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},14952:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20572:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.default,__next_app__:()=>o,pages:()=>d,routeModule:()=>m,tree:()=>c});var t=a(65239),r=a(48088),l=a(31369),i=a(30893),n={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);a.d(s,n);let c={children:["",{children:["(dashboard)",{children:["agents",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,40481)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,90170)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,33532)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(a.bind(a,88524))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,93595)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(a.bind(a,31369)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(a.bind(a,54413)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(a.bind(a,88524))).default(e)],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\page.tsx"],o={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(dashboard)/agents/page",pathname:"/agents",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},27910:e=>{"use strict";e.exports=require("stream")},28933:(e,s,a)=>{Promise.resolve().then(a.bind(a,34199))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},33872:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},33873:e=>{"use strict";e.exports=require("path")},34199:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>e0});var t=a(60687),r=a(43210),l=a.n(r),i=a(52581),n=a(16189),c=a(39665),d=a(43612),o=a(8693),m=a(54050),u=a(79481);let x="http://localhost:8000/api";function h(){let e=(0,o.jE)();return(0,m.n)({mutationFn:async e=>{let s=(0,u.U)(),{data:{session:a}}=await s.auth.getSession();if(!a)throw Error("You must be logged in to unpublish templates");let t=await fetch(`${x}/templates/${e}/unpublish`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a.access_token}`}});if(!t.ok)throw Error((await t.json().catch(()=>({message:"Unknown error"}))).message||`HTTP ${t.status}: ${t.statusText}`);return t.json()},onSuccess:()=>{e.invalidateQueries({queryKey:["secure-mcp","marketplace-templates"]}),e.invalidateQueries({queryKey:["secure-mcp","my-templates"]})}})}var p=a(15945),g=a(29523),f=a(89667),j=a(80013),v=a(63503),y=a(41862),N=a(5336),b=a(31158),_=a(70334),w=a(4780),k=a(12879),C=a(35950),A=a(91821),P=a(99891),S=a(96474),q=a(84027),$=a(15079),T=a(96834),M=a(44493),F=a(19959),E=a(93613),R=a(64398),L=a(12700);let z=(e,s=!0)=>{let a=(0,u.U)();return(0,d.I)({queryKey:["mcp-server-details",e],queryFn:async()=>{let{data:{session:s}}=await a.auth.getSession();if(!s)throw Error("No session");let t=await fetch(`http://localhost:8000/api/mcp/servers/${e}`,{headers:{Authorization:`Bearer ${s.access_token}`}});if(!t.ok)throw Error("Failed to fetch MCP server details");return t.json()},enabled:s&&!!e,staleTime:6e5})},I=({open:e,onOpenChange:s,mcpQualifiedName:a,mcpDisplayName:l,onSuccess:n})=>{let[c,d]=(0,r.useState)({profile_name:`${l} Profile`,display_name:l,config:{},is_default:!1}),{data:o,isLoading:m}=z(a),u=(0,L.Ak)(),x=()=>{let e=o?.connections?.[0]?.configSchema;return e?.required||[]},h=e=>x().includes(e),p=(e,s)=>{d(a=>({...a,config:{...a.config,[e]:s}}))},N=async()=>{try{let e={mcp_qualified_name:a,profile_name:c.profile_name,display_name:c.display_name,config:c.config,is_default:c.is_default},t=await u.mutateAsync(e);i.oR.success("Credential profile created successfully!");let r={profile_id:t.profile_id||"new-profile",mcp_qualified_name:a,profile_name:c.profile_name,display_name:c.display_name,config_keys:Object.keys(c.config),is_active:!0,is_default:c.is_default,last_used_at:null,created_at:new Date().toISOString(),updated_at:new Date().toISOString()};n(r),s(!1),d({profile_name:`${l} Profile`,display_name:l,config:{},is_default:!1})}catch(e){i.oR.error(e.message||"Failed to create credential profile")}},b=(()=>{let e=o?.connections?.[0]?.configSchema;return e?.properties||{}})(),_=Object.keys(b).length>0;return(0,t.jsx)(v.lG,{open:e,onOpenChange:s,children:(0,t.jsxs)(v.Cf,{className:"max-w-2xl max-h-[85vh] overflow-hidden flex flex-col",children:[(0,t.jsxs)(v.c7,{children:[(0,t.jsxs)(v.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(F.A,{className:"h-5 w-5 text-primary"}),"Create Credential Profile"]}),(0,t.jsxs)(v.rr,{children:["Create a new credential profile for ",(0,t.jsx)("strong",{children:l})]})]}),m?(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(y.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,t.jsx)("span",{children:"Loading server configuration..."})]}):(0,t.jsx)("div",{className:"space-y-6 flex-1 overflow-y-auto",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"grid grid-cols-1 gap-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(j.Label,{htmlFor:"profile_name",children:"Profile Name *"}),(0,t.jsx)(f.p,{id:"profile_name",value:c.profile_name,onChange:e=>d(s=>({...s,profile_name:e.target.value})),placeholder:"Enter a name for this profile"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"This helps you identify different configurations for the same MCP server"})]})}),_?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h3",{className:"text-sm font-semibold flex items-center gap-2",children:[(0,t.jsx)(q.A,{className:"h-4 w-4"}),"Connection Settings"]}),Object.entries(b).map(([e,s])=>(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(j.Label,{htmlFor:e,children:[s.title||e,h(e)&&(0,t.jsx)("span",{className:"text-destructive ml-1",children:"*"})]}),(0,t.jsx)(f.p,{id:e,type:"password"===s.format?"password":"text",placeholder:s.description||`Enter ${e}`,value:c.config[e]||"",onChange:s=>p(e,s.target.value)}),s.description&&(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:s.description})]},e))]}):(0,t.jsxs)(A.Fc,{children:[(0,t.jsx)(F.A,{className:"h-4 w-4"}),(0,t.jsx)(A.TN,{children:"This MCP server doesn't require any API credentials to use."})]}),(0,t.jsxs)(A.Fc,{children:[(0,t.jsx)(P.A,{className:"h-4 w-4"}),(0,t.jsx)(A.TN,{children:"Your credentials will be encrypted and stored securely. You can create multiple profiles for the same MCP server to handle different use cases."})]})]})}),(0,t.jsxs)(v.Es,{children:[(0,t.jsx)(g.$,{variant:"outline",onClick:()=>s(!1),children:"Cancel"}),(0,t.jsx)(g.$,{onClick:N,disabled:!c.profile_name.trim()||u.isPending,children:u.isPending?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y.A,{className:"h-4 w-4 animate-spin"}),"Creating..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),"Create Profile"]})})]})]})})};function O({mcpQualifiedName:e,mcpDisplayName:s,selectedProfileId:a,onProfileSelect:l,disabled:n=!1}){let[c,d]=(0,r.useState)(!1),{data:o=[],isLoading:m,error:u,refetch:x}=(0,L.Gx)(e),h=(0,L.M4)(),p=o.find(e=>e.profile_id===a),f=async e=>{try{await h.mutateAsync(e)}catch(e){console.error("Failed to set default profile:",e)}};return m?(0,t.jsx)("div",{className:"flex items-center justify-center p-4",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary"})}):u?(0,t.jsx)(M.Zp,{className:"border-destructive/50",children:(0,t.jsx)(M.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 text-destructive",children:[(0,t.jsx)(E.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm",children:"Failed to load credential profiles"})]})})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsx)(j.Label,{className:"text-sm font-medium",children:"Credential Profile"})}),0===o.length?(0,t.jsx)(M.Zp,{className:"border-dashed",children:(0,t.jsxs)(M.Wu,{className:"p-6 text-center",children:[(0,t.jsx)(q.A,{className:"h-8 w-8 mx-auto mb-2 text-muted-foreground"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground mb-3",children:["No credential profiles found for ",s]}),(0,t.jsxs)(g.$,{variant:"outline",size:"sm",onClick:()=>{d(!0)},disabled:n,children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),"Create Profile"]})]})}):(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)($.l6,{value:a||"",onValueChange:e=>{if(e&&""!==e.trim()){let s=o.find(s=>s.profile_id===e);s?l(e,s):(console.error("Selected profile not found:",e),i.oR.error("Selected profile not found. Please refresh and try again."))}else l(null,null)},disabled:n,children:[(0,t.jsx)($.bq,{children:(0,t.jsx)($.yv,{placeholder:"Select a credential profile..."})}),(0,t.jsx)($.gC,{children:o.map(e=>(0,t.jsx)($.eb,{value:e.profile_id,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{children:e.profile_name}),e.is_default&&(0,t.jsx)(T.E,{variant:"outline",className:"text-xs",children:"Default"})]})},e.profile_id))})]}),p&&(0,t.jsx)(M.Zp,{className:"bg-muted/30 py-0",children:(0,t.jsx)(M.Wu,{className:"p-3",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("h4",{className:"text-sm font-medium",children:p.profile_name}),p.is_default&&(0,t.jsx)(T.E,{variant:"outline",className:"text-xs",children:"Default"})]}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:p.display_name})]}),!p.is_default&&(0,t.jsx)(g.$,{variant:"ghost",size:"sm",onClick:()=>f(p.profile_id),disabled:h.isPending,children:(0,t.jsx)(R.A,{className:"h-3 w-3"})})]})})})]})]}),(0,t.jsx)(I,{open:c,onOpenChange:d,mcpQualifiedName:e,mcpDisplayName:s,onSuccess:e=>{x(),l(e.profile_id,e),i.oR.success(`Profile "${e.profile_name}" created and selected!`)}})]})}var D=a(58869),U=a(14719),G=a(35071),Z=a(55701),K=a(54987),V=a(21342),H=a(93500),Q=a(62688);let B=(0,Q.A)("Link2",[["path",{d:"M9 17H7A5 5 0 0 1 7 7h2",key:"8i5ue5"}],["path",{d:"M15 7h2a5 5 0 1 1 0 10h-2",key:"1b9ql8"}],["line",{x1:"8",x2:"16",y1:"12",y2:"12",key:"1jonct"}]]);var Y=a(81904),W=a(78122);let X=(0,Q.A)("StarOff",[["path",{d:"M8.34 8.34 2 9.27l5 4.87L5.82 21 12 17.77 18.18 21l-.59-3.43",key:"16m0ql"}],["path",{d:"M18.42 12.76 22 9.27l-6.91-1L12 2l-1.44 2.91",key:"1vt8nq"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);var J=a(88233);let ee=({appSlug:e,appName:s,onProfileSelect:a,className:l})=>{let[i,n]=(0,r.useState)(!1),[c,d]=(0,r.useState)(null),[o,m]=(0,r.useState)(null),[u,x]=(0,r.useState)(""),[h,p]=(0,r.useState)(!1),{data:N,isLoading:b,refetch:_}=(0,Z.h4)({app_slug:e}),w=(0,Z.O4)(),k=(0,Z.Lh)(),C=(0,Z.PA)(),A=(0,Z.wG)(),P=async()=>{if(!u.trim())return;let a={profile_name:u.trim(),app_slug:e||"",app_name:s||e||"",is_default:h};try{await w.mutateAsync(a),n(!1),x(""),p(!1)}catch(e){}},$=async(e,s)=>{try{await k.mutateAsync({profileId:e.profile_id,request:s}),d(null)}catch(e){}},F=async e=>{try{await C.mutateAsync(e.profile_id),m(null)}catch(e){}},E=async s=>{try{await A.mutateAsync({profileId:s.profile_id,app:e})}catch(e){}};if(b)return(0,t.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,t.jsx)(y.A,{className:"h-6 w-6 animate-spin"})});let L=N?.filter(s=>!e||s.app_slug===e)||[];return(0,t.jsxs)("div",{className:l,children:[0===L.length?(0,t.jsx)(M.Zp,{children:(0,t.jsxs)(M.Wu,{className:"text-center py-12",children:[(0,t.jsx)(D.A,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),(0,t.jsx)("h4",{className:"font-medium mb-2",children:"No credential profiles yet"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground mb-6 max-w-sm mx-auto",children:["Create credential profiles to manage multiple accounts or configurations for ",s||"your apps","."]}),(0,t.jsxs)(g.$,{onClick:()=>n(!0),variant:"outline",children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),"Create First Profile"]})]})}):(0,t.jsxs)("div",{className:"space-y-4",children:[L.map(e=>(0,t.jsx)(M.Zp,{children:(0,t.jsx)(M.Wu,{children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)("h4",{className:"font-medium",children:e.profile_name}),e.is_default&&(0,t.jsxs)(T.E,{variant:"secondary",className:"text-xs",children:[(0,t.jsx)(R.A,{className:"h-3 w-3"}),"Default"]}),e.is_connected?(0,t.jsxs)(T.E,{variant:"default",className:"text-xs",children:[(0,t.jsx)(U.A,{className:"h-3 w-3"}),"Connected"]}):(0,t.jsxs)(T.E,{variant:"outline",className:"text-xs",children:[(0,t.jsx)(G.A,{className:"h-3 w-3"}),"Not Connected"]}),!e.is_active&&(0,t.jsx)(T.E,{variant:"destructive",className:"text-xs",children:"Inactive"})]})}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[!e.is_connected&&(0,t.jsxs)(g.$,{size:"sm",variant:"outline",onClick:()=>E(e),disabled:A.isPending,children:[(0,t.jsx)(B,{className:"h-4 w-4"}),"Connect"]}),(0,t.jsxs)(V.rI,{children:[(0,t.jsx)(V.ty,{asChild:!0,children:(0,t.jsx)(g.$,{size:"sm",variant:"ghost",children:(0,t.jsx)(Y.A,{className:"h-4 w-4"})})}),(0,t.jsxs)(V.SQ,{align:"end",children:[(0,t.jsxs)(V._2,{onClick:()=>d(e),children:[(0,t.jsx)(q.A,{className:"h-4 w-4"}),"Edit Profile"]}),e.is_connected&&(0,t.jsxs)(V._2,{onClick:()=>E(e),children:[(0,t.jsx)(W.A,{className:"h-4 w-4"}),"Reconnect"]}),!e.is_default&&(0,t.jsxs)(V._2,{onClick:()=>$(e,{is_default:!0}),children:[(0,t.jsx)(R.A,{className:"h-4 w-4"}),"Set as Default"]}),e.is_default&&(0,t.jsxs)(V._2,{onClick:()=>$(e,{is_default:!1}),children:[(0,t.jsx)(X,{className:"h-4 w-4"}),"Remove Default"]}),(0,t.jsxs)(V._2,{className:"text-destructive",onClick:()=>m(e),children:[(0,t.jsx)(J.A,{className:"h-4 w-4"}),"Delete Profile"]})]})]})]})]})})},e.profile_id)),(0,t.jsx)("div",{className:"w-full rounded-lg h-24 bg-muted border-dashed border-muted flex items-center justify-center",children:(0,t.jsx)(g.$,{onClick:()=>n(!0),size:"sm",variant:"outline",className:"w-full h-full",children:(0,t.jsx)("div",{className:"flex bg-primary/10 items-center justify-center h-10 w-10 rounded-full",children:(0,t.jsx)(S.A,{className:"h-4 w-4"})})})})]}),(0,t.jsx)(v.lG,{open:i,onOpenChange:n,children:(0,t.jsxs)(v.Cf,{className:"max-w-md",children:[(0,t.jsxs)(v.c7,{children:[(0,t.jsx)(v.L3,{children:"Create Credential Profile"}),(0,t.jsxs)(v.rr,{children:["Create a new credential profile for ",s||"your app","."]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(j.Label,{htmlFor:"profile-name",children:"Profile Name"}),(0,t.jsx)(f.p,{id:"profile-name",placeholder:"e.g., Personal Account, Work Account",value:u,onChange:e=>x(e.target.value)})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(K.d,{id:"is-default",checked:h,onCheckedChange:p}),(0,t.jsx)(j.Label,{htmlFor:"is-default",children:"Set as default profile"})]})]}),(0,t.jsxs)(v.Es,{children:[(0,t.jsx)(g.$,{variant:"outline",onClick:()=>n(!1),children:"Cancel"}),(0,t.jsx)(g.$,{onClick:P,disabled:!u.trim()||w.isPending,children:w.isPending?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Creating..."]}):"Create Profile"})]})]})}),c&&(0,t.jsx)(v.lG,{open:!!c,onOpenChange:()=>d(null),children:(0,t.jsxs)(v.Cf,{className:"max-w-md",children:[(0,t.jsxs)(v.c7,{children:[(0,t.jsx)(v.L3,{children:"Edit Profile"}),(0,t.jsxs)(v.rr,{children:["Update the settings for ",c.profile_name,"."]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(j.Label,{htmlFor:"edit-profile-name",children:"Profile Name"}),(0,t.jsx)(f.p,{id:"edit-profile-name",defaultValue:c.profile_name,onChange:e=>{let s=e.target.value;d({...c,profile_name:s})}})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(K.d,{id:"edit-is-active",checked:c.is_active,onCheckedChange:e=>{d({...c,is_active:e})}}),(0,t.jsx)(j.Label,{htmlFor:"edit-is-active",children:"Active"})]})]}),(0,t.jsxs)(v.Es,{children:[(0,t.jsx)(g.$,{variant:"outline",onClick:()=>d(null),children:"Cancel"}),(0,t.jsx)(g.$,{onClick:()=>$(c,{profile_name:c.profile_name,is_active:c.is_active}),disabled:k.isPending,children:k.isPending?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Saving..."]}):"Save Changes"})]})]})}),(0,t.jsx)(H.Lt,{open:!!o,onOpenChange:()=>m(null),children:(0,t.jsxs)(H.EO,{children:[(0,t.jsxs)(H.wd,{children:[(0,t.jsx)(H.r7,{children:"Delete Profile"}),(0,t.jsxs)(H.$v,{children:['Are you sure you want to delete the profile "',o?.profile_name,'"? This action cannot be undone.']})]}),(0,t.jsxs)(H.ck,{children:[(0,t.jsx)(H.Zr,{children:"Cancel"}),(0,t.jsx)(H.Rx,{onClick:()=>o&&F(o),className:"bg-destructive text-destructive-foreground hover:bg-destructive/90",children:"Delete Profile"})]})]})})]})},es=({appSlug:e,appName:s,selectedProfileId:a,onProfileSelect:l,className:i,showCreateOption:n=!0})=>{let[c,d]=(0,r.useState)(!1),{data:o,isLoading:m}=(0,Z.h4)({app_slug:e,is_active:!0}),u=o?.find(e=>e.profile_id===a),x=o?.filter(e=>e.is_connected)||[];return m?(0,t.jsx)("div",{className:i,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 animate-spin"}),(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Loading profiles..."})]})}):o&&0!==o.length?0===x.length?(0,t.jsxs)("div",{className:i,children:[(0,t.jsx)(M.Zp,{children:(0,t.jsx)(M.Wu,{className:"py-6",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(E.A,{className:"h-8 w-8 text-warning mx-auto mb-3"}),(0,t.jsx)("p",{className:"text-sm font-medium mb-1",children:"No Connected Profiles"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground mb-3",children:["You have profiles but none are connected to ",s]}),(0,t.jsxs)(g.$,{size:"sm",variant:"outline",onClick:()=>d(!0),children:[(0,t.jsx)(q.A,{className:"h-4 w-4 mr-2"}),"Manage Profiles"]})]})})}),c&&(0,t.jsx)(v.lG,{open:c,onOpenChange:d,children:(0,t.jsxs)(v.Cf,{className:"max-w-3xl",children:[(0,t.jsxs)(v.c7,{children:[(0,t.jsxs)(v.L3,{children:["Manage ",s," Profiles"]}),(0,t.jsx)(v.rr,{children:"Connect your profiles to use them with agents."})]}),(0,t.jsx)(ee,{appSlug:e,appName:s,onProfileSelect:e=>{l(e.profile_id),d(!1)}})]})})]}):(0,t.jsxs)("div",{className:i,children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)($.l6,{value:a||"",onValueChange:e=>l(e||null),children:[(0,t.jsx)($.bq,{className:"flex-1",children:(0,t.jsx)($.yv,{placeholder:"Select a profile",children:u&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{children:u.profile_name}),u.is_connected?(0,t.jsx)(U.A,{className:"h-3 w-3 text-green-500"}):(0,t.jsx)(G.A,{className:"h-3 w-3 text-red-500"})]})})}),(0,t.jsx)($.gC,{children:x.map(e=>(0,t.jsx)($.eb,{value:e.profile_id,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("span",{children:e.profile_name}),(0,t.jsx)("div",{className:"text-xs flex items-center gap-2",children:(0,t.jsx)("div",{className:"h-2 w-2 bg-green-500 rounded-full"})})]})},e.profile_id))})]}),n&&(0,t.jsx)(g.$,{size:"icon",variant:"outline",onClick:()=>d(!0),title:"Add new profile",children:(0,t.jsx)(S.A,{className:"h-4 w-4"})})]}),u&&!u.is_connected&&(0,t.jsxs)("p",{className:"text-xs text-destructive flex items-center gap-1",children:[(0,t.jsx)(G.A,{className:"h-3 w-3"}),"This profile is not connected. Please connect it first."]})]}),c&&(0,t.jsx)(v.lG,{open:c,onOpenChange:d,children:(0,t.jsxs)(v.Cf,{className:"max-w-3xl",children:[(0,t.jsxs)(v.c7,{children:[(0,t.jsxs)(v.L3,{children:["Manage ",s," Profiles"]}),(0,t.jsxs)(v.rr,{children:["Create and manage credential profiles for ",s,"."]})]}),(0,t.jsx)(ee,{appSlug:e,appName:s,onProfileSelect:e=>{l(e.profile_id),d(!1)}})]})})]}):(0,t.jsxs)("div",{className:i,children:[(0,t.jsx)(M.Zp,{children:(0,t.jsx)(M.Wu,{className:"py-6",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(D.A,{className:"h-8 w-8 text-muted-foreground mx-auto mb-3"}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground mb-3",children:["No credential profiles found for ",s]}),n&&(0,t.jsxs)(g.$,{size:"sm",variant:"outline",onClick:()=>d(!0),children:[(0,t.jsx)(S.A,{className:"h-4 w-4 mr-2"}),"Create Profile"]})]})})}),c&&(0,t.jsx)(v.lG,{open:c,onOpenChange:d,children:(0,t.jsxs)(v.Cf,{className:"max-w-3xl",children:[(0,t.jsxs)(v.c7,{children:[(0,t.jsxs)(v.L3,{children:["Manage ",s," Profiles"]}),(0,t.jsxs)(v.rr,{children:["Create and manage credential profiles for ",s,"."]})]}),(0,t.jsx)(ee,{appSlug:e,appName:s,onProfileSelect:e=>{l(e.profile_id),d(!1)}})]})})]})};var ea=a(52556);let et=({step:e,selectedProfileId:s,onProfileSelect:a,onComplete:l})=>{let[n,c]=(0,r.useState)("select"),[d,o]=(0,r.useState)(!1),[m,u]=(0,r.useState)(""),[x,h]=(0,r.useState)({}),[p,v]=(0,r.useState)(!1),N=(0,L.Ak)(),{data:b}=z(e.qualified_name),{data:_}=(0,Z.h4)(),w="pipedream_profile"===e.type,k=b?.connections?.[0]?.configSchema?.properties||{},$=b?.connections?.[0]?.configSchema?.required||[],T=_?.some(s=>s.app_slug===e.app_slug&&s.is_connected);(0,r.useEffect)(()=>{c("select"),o(!1),u(""),h({}),v(!1)},[e.qualified_name]);let M=(0,r.useMemo)(()=>({id:e.qualified_name,name_slug:e.app_slug||e.qualified_name,name:e.service_name,description:`Connect your ${e.service_name} account to use its tools`,img_src:"",custom_fields_json:"[]",categories:[],featured_weight:0,auth_type:"keys",connect:{allowed_domains:null,base_proxy_target_url:"",proxy_enabled:!1}}),[e.app_slug,e.qualified_name,e.service_name]),F=(0,r.useCallback)(async()=>{if(!m.trim())return void i.oR.error("Please enter a profile name");o(!0);try{let s={mcp_qualified_name:e.qualified_name,profile_name:m.trim(),display_name:e.service_name,config:x,is_default:!1},t=await N.mutateAsync(s);i.oR.success("Profile created successfully!"),a(e.qualified_name,t.profile_id||"new-profile"),c("select"),u(""),h({}),l?.()}catch(e){i.oR.error(e.message||"Failed to create profile")}finally{o(!1)}},[m,x,e.qualified_name,e.service_name,N,a,l]),E=(0,r.useCallback)((e,s)=>{h(a=>({...a,[e]:s}))},[]),R=(0,r.useCallback)(e=>{u(e.target.value)},[]),I=(0,r.useCallback)(e=>{"Enter"===e.key&&"create"===n&&F()},[F,n]),D=e=>$.includes(e),U=(0,r.useMemo)(()=>(0,t.jsx)("div",{className:"space-y-4",children:w?(0,t.jsxs)("div",{className:"space-y-4",children:[T?(0,t.jsx)(es,{appSlug:e.app_slug||"",appName:e.service_name,selectedProfileId:s,onProfileSelect:s=>{a(e.qualified_name,s)}}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(A.Fc,{className:"border-primary/20 bg-primary/5",children:[(0,t.jsx)(P.A,{className:"h-4 w-4"}),(0,t.jsxs)(A.TN,{children:["No connected ",e.service_name," profiles found. Create and connect one to continue."]})]}),(0,t.jsxs)(g.$,{onClick:()=>v(!0),className:"w-full",children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),"Connect ",e.service_name]})]}),T&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(C.w,{className:"flex-1"}),(0,t.jsx)("span",{className:"text-xs text-muted-foreground",children:"OR"}),(0,t.jsx)(C.w,{className:"flex-1"})]}),(0,t.jsxs)(g.$,{variant:"outline",onClick:()=>v(!0),className:"w-full",children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),"Connect Different Account"]})]})]}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(O,{mcpQualifiedName:e.qualified_name,mcpDisplayName:e.service_name,selectedProfileId:s,onProfileSelect:s=>{a(e.qualified_name,s)}}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(C.w,{className:"flex-1"}),(0,t.jsx)("span",{className:"text-xs text-muted-foreground",children:"OR"}),(0,t.jsx)(C.w,{className:"flex-1"})]}),(0,t.jsxs)(g.$,{variant:"outline",onClick:()=>{u(`${e.service_name} Profile`),c("create")},className:"w-full",children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),"Create New Profile"]})]})}),[e.service_name,e.qualified_name,e.app_slug,w,s,T,a]),G=(0,r.useMemo)(()=>(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"flex items-center gap-2",children:(0,t.jsx)(g.$,{variant:"link",size:"sm",onClick:()=>c("select"),className:"mb-4 p-0 h-auto font-normal text-muted-foreground hover:text-foreground",children:"← Back to Selection"})}),(0,t.jsxs)("h3",{className:"font-semibold",children:["Create ",e.service_name," Profile"]}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Set up a new credential profile for ",e.service_name]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(j.Label,{htmlFor:"profile-name",children:"Profile Name"}),(0,t.jsx)(f.p,{id:"profile-name",placeholder:"e.g., Personal Account, Work Account",value:m,onChange:R,onKeyDown:I,autoFocus:!0,className:"h-11"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"This helps you identify different configurations"})]}),Object.keys(k).length>0?(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(q.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:"Connection Settings"})]}),Object.entries(k).map(([e,s])=>(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(j.Label,{htmlFor:e,children:[s.title||e,D(e)&&(0,t.jsx)("span",{className:"text-destructive ml-1",children:"*"})]}),(0,t.jsx)(f.p,{id:e,type:"password"===s.format?"password":"text",placeholder:s.description||`Enter ${e}`,value:x[e]||"",onChange:s=>E(e,s.target.value),onKeyDown:I,className:"h-11"}),s.description&&(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:s.description})]},e))]}):(0,t.jsxs)(A.Fc,{className:"border-primary/20 bg-primary/5",children:[(0,t.jsx)(P.A,{className:"h-4 w-4"}),(0,t.jsx)(A.TN,{children:"This service doesn't require any credentials to connect."})]})]}),(0,t.jsx)("div",{className:"pt-4 border-t",children:(0,t.jsx)(g.$,{onClick:F,disabled:!m.trim()||d,className:"w-full",children:d?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y.A,{className:"h-4 w-4 animate-spin"}),"Creating..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),"Create & Continue"]})})})]}),[e.service_name,m,x,k,d,R,I,E,F,D]);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"space-y-6",children:"select"===n?U:G}),w&&(0,t.jsx)(ea.R,{app:M,open:p,onOpenChange:v,mode:"profile-only",onComplete:(s,t,r,n)=>{a(e.qualified_name,s),v(!1),i.oR.success(`Connected to ${r} successfully!`),l?.()}})]})};var er=a(24413),el=a(96882);let ei=({step:e,config:s,onConfigUpdate:a})=>{let l=(0,r.useCallback)((t,r)=>{let l={...s,[t]:r};a(e.qualified_name,l)},[s,a,e.qualified_name]);return(0,t.jsxs)("div",{className:"space-y-4",children:[e.custom_type&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(T.E,{variant:"outline",className:"bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800",children:e.custom_type.toUpperCase()}),(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Custom Server"})]}),(0,t.jsxs)(A.Fc,{className:"border-primary/20 bg-primary/5",children:[(0,t.jsx)(er.A,{className:"h-4 w-4"}),(0,t.jsxs)(A.TN,{children:["Configure your personal ",e.service_name," server connection details."]})]}),(0,t.jsx)("div",{className:"space-y-4",children:e.required_fields?.map(e=>(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(j.Label,{htmlFor:e.key,className:"text-sm font-medium",children:e.label}),(0,t.jsx)(f.p,{id:e.key,type:e.type,placeholder:e.placeholder,value:s[e.key]||"",onChange:s=>l(e.key,s.target.value),className:"h-11"}),e.description&&(0,t.jsxs)("div",{className:"flex items-start gap-2",children:[(0,t.jsx)(el.A,{className:"h-3 w-3 text-muted-foreground mt-0.5 flex-shrink-0"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]})]},e.key))})]})},en=({item:e,open:s,onOpenChange:a,onInstall:l,isInstalling:i})=>{let[n,c]=(0,r.useState)(0),[d,o]=(0,r.useState)(""),[m,u]=(0,r.useState)({}),[x,h]=(0,r.useState)({}),[p,C]=(0,r.useState)([]),[A,P]=(0,r.useState)(!1),S=(0,r.useCallback)(()=>{if(!e?.mcp_requirements)return[];let s=[];return e.mcp_requirements.filter(e=>"pipedream"===e.custom_type).forEach(e=>{s.push({id:e.qualified_name,title:`Connect ${e.display_name}`,description:`Select an existing ${e.display_name} profile or create a new one`,type:"pipedream_profile",service_name:e.display_name,qualified_name:e.qualified_name,app_slug:e.qualified_name})}),e.mcp_requirements.filter(e=>!e.custom_type).forEach(e=>{s.push({id:e.qualified_name,title:`Connect ${e.display_name}`,description:`Select or create a credential profile for ${e.display_name}`,type:"credential_profile",service_name:e.display_name,qualified_name:e.qualified_name})}),e.mcp_requirements.filter(e=>e.custom_type&&"pipedream"!==e.custom_type).forEach(e=>{s.push({id:e.qualified_name,title:`Configure ${e.display_name}`,description:`Enter your ${e.display_name} server details`,type:"custom_server",service_name:e.display_name,qualified_name:e.qualified_name,custom_type:e.custom_type,required_fields:e.required_config?.map(s=>({key:s,label:"url"===s?`${e.display_name} Server URL`:s,type:"url"===s?"url":"text",placeholder:"url"===s?`https://your-${e.display_name.toLowerCase()}-server.com`:`Enter your ${s}`,description:"url"===s?`Your personal ${e.display_name} server endpoint`:void 0}))||[]})}),s},[e]);(0,r.useEffect)(()=>{s&&e&&(c(0),o(e.name),u({}),h({}),P(!0),C(S()),P(!1))},[s,e,S]);let q=(0,r.useCallback)(e=>{o(e.target.value)},[]),$=(0,r.useCallback)((e,s)=>{u(a=>({...a,[e]:s||""}))},[]),T=(0,r.useCallback)((e,s)=>{h(a=>({...a,[e]:s}))},[]),M=(0,r.useCallback)(()=>{if(0===p.length)return!0;if(n>=p.length)return!!d.trim();let e=p[n];switch(e.type){case"credential_profile":case"pipedream_profile":return!!m[e.qualified_name];case"custom_server":let s=x[e.qualified_name]||{};return e.required_fields?.every(e=>{let a=s[e.key];return a&&a.toString().trim().length>0})||!1;default:return!1}},[n,p,m,x,d]),F=(0,r.useCallback)(()=>{n<p.length&&c(n+1)},[n,p.length]),E=(0,r.useCallback)(()=>{n>0&&c(n-1)},[n]),R=(0,r.useCallback)(async()=>{if(!e||!d.trim())return;let s={...x};p.forEach(e=>{if("pipedream_profile"===e.type){let a=m[e.qualified_name];a&&(s[e.qualified_name]={url:"https://remote.mcp.pipedream.net",headers:{"x-pd-app-slug":e.app_slug},profile_id:a})}}),await l(e,d,m,s)},[e,d,m,x,p,l]),L=p[n],z=n>=p.length,{avatar:I,color:O}=(0,r.useMemo)(()=>e?e.avatar&&e.avatar_color?{avatar:e.avatar,color:e.avatar_color}:(0,k.Z)(e.id):{avatar:"\uD83E\uDD16",color:"#000"},[e]);return e?(0,t.jsx)(v.lG,{open:s,onOpenChange:a,children:(0,t.jsxs)(v.Cf,{className:"sm:max-w-lg max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(v.c7,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"h-12 w-12 flex-shrink-0 rounded-lg flex items-center justify-center",style:{backgroundColor:O,boxShadow:`0 16px 48px -8px ${O}70, 0 8px 24px -4px ${O}50`},children:(0,t.jsx)("span",{className:"text-lg",children:I})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)(v.L3,{className:"text-left flex items-center gap-2",children:["Install ",e.name]}),(0,t.jsx)(v.rr,{className:"text-left",children:e.description})]})]}),p.length>0&&(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[p.map((e,s)=>(0,t.jsx)("div",{className:(0,w.cn)("h-2 w-2 rounded-full",s<=n?"bg-primary":"bg-muted")},s)),(0,t.jsx)("div",{className:"h-px bg-muted flex-1"}),(0,t.jsx)("div",{className:(0,w.cn)("h-2 w-2 rounded-full",z?"bg-primary":"bg-muted")})]})]}),(0,t.jsx)("div",{className:"mt-6",children:(0,t.jsx)(()=>A?(0,t.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(y.A,{className:"h-5 w-5 animate-spin"}),(0,t.jsx)("span",{className:"text-sm",children:"Preparing installation..."})]})}):0===p.length||z?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"h-8 w-8 rounded-full bg-green-100 dark:bg-green-900/20 flex items-center justify-center",children:(0,t.jsx)(N.A,{className:"h-5 w-5 text-green-600 dark:text-green-400"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold",children:"Ready to install!"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Give your agent a name and we'll set everything up."})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(j.Label,{htmlFor:"instance-name",children:"Agent Name"}),(0,t.jsx)(f.p,{id:"instance-name",placeholder:"Enter a name for this agent",value:d,onChange:q,className:"h-11"})]})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"h-4 w-4 rounded-full bg-primary text-primary-foreground text-sm font-semibold flex items-center justify-center",children:n+1}),(0,t.jsx)("h3",{className:"font-semibold",children:L.title})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:L.description})]}),(0,t.jsxs)("div",{children:[("credential_profile"===L.type||"pipedream_profile"===L.type)&&(0,t.jsx)(et,{step:L,selectedProfileId:m[L.qualified_name],onProfileSelect:$,onComplete:()=>{n<p.length-1&&setTimeout(()=>c(n+1),500)}}),"custom_server"===L.type&&(0,t.jsx)(ei,{step:L,config:x[L.qualified_name]||{},onConfigUpdate:T})]}),p.length>1&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"flex items-center gap-2",children:p.map((e,s)=>(0,t.jsx)("div",{className:(0,w.cn)("h-1 flex-1 rounded-full transition-colors",s<=n?"bg-primary":"bg-muted")},s))}),(0,t.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Step ",n+1," of ",p.length]})]})]}),{})}),(0,t.jsxs)("div",{className:"flex gap-3 pt-6 border-t",children:[n>0&&(0,t.jsx)(g.$,{variant:"outline",onClick:E,className:"flex-1",children:"Back"}),z||0===p.length?(0,t.jsx)(g.$,{onClick:R,disabled:i||!d.trim(),className:"flex-1",children:i?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y.A,{className:"h-4 w-4 animate-spin"}),"Installing..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(b.A,{className:"h-4 w-4"}),"Install Agent"]})}):(0,t.jsxs)(g.$,{onClick:F,disabled:!M(),className:"flex-1",children:["Continue",(0,t.jsx)(_.A,{className:"h-4 w-4"})]})]})]})}):null};var ec=a(83753),ed=a(69259);let eo=()=>(0,t.jsxs)(ed.z,{icon:ec.A,children:[(0,t.jsx)("span",{className:"text-primary",children:"Custom agents"})," that",(0,t.jsx)("br",{}),(0,t.jsx)("span",{className:"text-muted-foreground",children:"automate"})," your ",(0,t.jsx)("span",{className:"text-muted-foreground",children:"workflows"}),"."]});var em=a(71057),eu=a(10022),ex=a(10218);let eh=({value:e,isActive:s,onClick:a,children:r})=>{let{theme:l}=(0,ex.D)(),i="dark"===l;return(0,t.jsxs)("button",{onClick:a,className:(0,w.cn)("relative flex items-center justify-center gap-2 rounded-2xl px-4 py-2.5 text-sm font-medium transition-all duration-300",i?"hover:bg-white/5":"hover:bg-muted/80",s?i?"text-white":"text-foreground bg-background border border-border/50":i?"text-white/50 hover:text-white/70":"text-muted-foreground hover:text-foreground"),style:s&&i?{background:"linear-gradient(135deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.08))",backdropFilter:"blur(12px)",boxShadow:`
          0 4px 8px rgba(0, 0, 0, 0.1),
          0 0 20px rgba(255, 255, 255, 0.1),
          0 0 40px rgba(255, 255, 255, 0.1),
          inset 0 1px 0 rgba(255, 255, 255, 0.2)
        `}:void 0,children:[s&&i&&(0,t.jsx)("div",{className:"absolute inset-0 rounded-2xl opacity-40 blur-sm",style:{background:"linear-gradient(45deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1))",zIndex:-1}}),r]})},ep=({tabs:e,activeTab:s,onTabChange:a,className:r})=>{let{theme:l}=(0,ex.D)(),i="dark"===l;return(0,t.jsx)("div",{className:(0,w.cn)("overflow-hidden grid w-full max-w-lg mx-auto rounded-3xl p-1.5",i?"border-white/5":"border-border/20 bg-muted",r),style:{gridTemplateColumns:`repeat(${e.length}, 1fr)`,...i?{background:"rgba(255, 255, 255, 0.05)",backdropFilter:"blur(20px)"}:{}},children:e.map(e=>{let r=e.icon;return(0,t.jsxs)(eh,{value:e.value,isActive:s===e.value,onClick:()=>a(e.value),children:[(0,t.jsx)(r,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"hidden sm:inline",children:e.label}),e.shortLabel&&(0,t.jsx)("span",{className:"sm:hidden",children:e.shortLabel})]},e.value)})})},eg=[{value:"my-agents",icon:ec.A,label:"My Agents"},{value:"marketplace",icon:em.A,label:"Marketplace",shortLabel:"Market"},{value:"my-templates",icon:eu.A,label:"My Templates",shortLabel:"Templates"}],ef=({activeTab:e,onTabChange:s})=>(0,t.jsx)(ep,{tabs:eg,activeTab:e,onTabChange:s});var ej=a(99270);let ev=({placeholder:e,value:s,onChange:a,className:r=""})=>(0,t.jsxs)("div",{className:`relative flex-1 max-w-2xl ${r}`,children:[(0,t.jsx)(ej.A,{className:"absolute left-4 top-1/2 h-5 w-5 -translate-y-1/2 text-muted-foreground"}),(0,t.jsx)(f.p,{placeholder:e,value:s,onChange:e=>a(e.target.value),className:"pl-12 h-12 rounded-xl bg-muted/50 border-0 focus:bg-background focus:ring-2 focus:ring-primary/20 transition-all"})]}),ey=({hasAgents:e,onCreateAgent:s,onClearFilters:a})=>(0,t.jsx)("div",{className:"flex flex-col items-center justify-center py-16 px-4",children:(0,t.jsxs)("div",{className:"flex flex-col items-center text-center max-w-md space-y-6",children:[(0,t.jsx)("div",{className:"rounded-full bg-muted p-6",children:e?(0,t.jsx)(ej.A,{className:"h-12 w-12 text-muted-foreground"}):(0,t.jsx)(ec.A,{className:"h-12 w-12 text-muted-foreground"})}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h2",{className:"text-2xl font-semibold text-foreground",children:e?"No agents found":"No agents yet"}),(0,t.jsx)("p",{className:"text-muted-foreground leading-relaxed",children:e?"No agents match your current search and filter criteria. Try adjusting your filters or search terms.":"Create your first agent to start automating tasks with custom instructions and tools. Configure custom AgentPress capabilities to fine tune agent according to your needs."})]}),e?(0,t.jsx)(g.$,{variant:"outline",onClick:a,className:"mt-4",children:"Clear filters"}):(0,t.jsxs)(g.$,{size:"lg",onClick:s,className:"mt-4",children:[(0,t.jsx)(S.A,{className:"h-5 w-5"}),"Create your first agent"]})]})});var eN=a(67821),eb=a(82679),e_=a(33872);let ew=(0,Q.A)("GlobeLock",[["path",{d:"M15.686 15A14.5 14.5 0 0 1 12 22a14.5 14.5 0 0 1 0-20 10 10 0 1 0 9.542 13",key:"qkt0x6"}],["path",{d:"M2 12h8.5",key:"ovaggd"}],["path",{d:"M20 6V4a2 2 0 1 0-4 0v2",key:"1of5e8"}],["rect",{width:"8",height:"5",x:"14",y:"6",rx:"1",key:"1fmf51"}]]);var ek=a(11437),eC=a(40228),eA=a(13861);let eP=({isKortixTeam:e})=>e?(0,t.jsxs)(T.E,{variant:"secondary",className:"bg-blue-100 text-blue-700 border-0 dark:bg-blue-950 dark:text-blue-300",children:[(0,t.jsx)(N.A,{className:"h-3 w-3 mr-1"}),"Kortix"]}):(0,t.jsxs)(T.E,{variant:"secondary",className:"bg-green-100 text-green-700 border-0 dark:bg-green-950 dark:text-green-300",children:[(0,t.jsx)(D.A,{className:"h-3 w-3 mr-1"}),"Community"]}),eS=({isPublic:e})=>e?(0,t.jsxs)(T.E,{variant:"default",className:"bg-green-100 text-green-700 border-0 dark:bg-green-950 dark:text-green-300",children:[(0,t.jsx)(ek.A,{className:"h-3 w-3 mr-1"}),"Public"]}):(0,t.jsxs)(T.E,{variant:"secondary",className:"bg-gray-100 text-gray-700 border-0 dark:bg-gray-800 dark:text-gray-300",children:[(0,t.jsx)(ew,{className:"h-3 w-3 mr-1"}),"Private"]}),eq=({agent:e})=>(0,t.jsxs)("div",{className:"flex gap-1",children:[e.current_version&&(0,t.jsxs)(T.E,{variant:"outline",className:"text-xs",children:[(0,t.jsx)(eN.A,{className:"h-3 w-3 mr-1"}),e.current_version.version_name]}),e.is_public&&(0,t.jsxs)(T.E,{variant:"outline",className:"text-xs",children:[(0,t.jsx)(P.A,{className:"h-3 w-3 mr-1"}),"Published"]})]}),e$=({data:e})=>(0,t.jsxs)("div",{className:"flex items-center justify-between text-xs text-muted-foreground",children:[(0,t.jsx)("div",{className:"flex items-center gap-1",children:e.is_kortix_team?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(b.A,{className:"h-3 w-3"}),(0,t.jsxs)("span",{children:[e.download_count," installs"]})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(D.A,{className:"h-3 w-3"}),(0,t.jsxs)("span",{children:["by ",e.creator_name]})]})}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(eC.A,{className:"h-3 w-3"}),(0,t.jsx)("span",{children:new Date(e.marketplace_published_at||e.created_at).toLocaleDateString()})]})]}),eT=({data:e})=>(0,t.jsxs)("div",{className:"space-y-1 text-xs text-muted-foreground",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(eC.A,{className:"h-3 w-3"}),(0,t.jsxs)("span",{children:["Created ",new Date(e.created_at).toLocaleDateString()]})]}),e.is_public&&(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(b.A,{className:"h-3 w-3"}),(0,t.jsxs)("span",{children:[e.download_count," downloads"]})]})]}),eM=()=>(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsx)("span",{className:"text-muted-foreground text-xs",children:"By me"})}),eF=({onAction:e,isActioning:s,data:a})=>(0,t.jsx)(g.$,{onClick:s=>e?.(a,s),disabled:s,className:"w-full",size:"sm",children:s?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y.A,{className:"h-4 w-4 animate-spin "}),"Installing..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(b.A,{className:"h-4 w-4 "}),"Install Agent"]})}),eE=({data:e,onPrimaryAction:s,onSecondaryAction:a,isActioning:r})=>(0,t.jsx)("div",{className:"space-y-2",children:e.is_public?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(g.$,{onClick:a=>s?.(e,a),disabled:r,variant:"outline",className:"w-full",size:"sm",children:r?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y.A,{className:"h-3 w-3 animate-spin "}),"Unpublishing..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(ew,{className:"h-3 w-3 "}),"Make Private"]})}),(0,t.jsxs)(g.$,{onClick:s=>a?.(e,s),variant:"ghost",size:"sm",className:"w-full",children:[(0,t.jsx)(eA.A,{className:"h-3 w-3 "}),"View in Marketplace"]})]}):(0,t.jsx)(g.$,{onClick:a=>s?.(e,a),disabled:r,variant:"default",className:"w-full",size:"sm",children:r?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y.A,{className:"h-3 w-3 animate-spin "}),"Publishing..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(ek.A,{className:"h-3 w-3 "}),"Publish to Marketplace"]})})}),eR=({avatar:e,color:s})=>(0,t.jsxs)("div",{className:"relative h-14 w-14 flex items-center justify-center rounded-2xl",style:{backgroundColor:s},children:[(0,t.jsx)("div",{className:"text-2xl",children:e}),(0,t.jsx)("div",{className:"absolute inset-0 rounded-2xl pointer-events-none opacity-0 dark:opacity-100 transition-opacity",style:{boxShadow:`0 16px 48px -8px ${s}70, 0 8px 24px -4px ${s}50`}})]}),eL=({tags:e})=>e&&0!==e.length?(0,t.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.slice(0,2).map(e=>(0,t.jsx)(T.E,{variant:"outline",className:"text-xs border-border/50",children:e},e)),e.length>2&&(0,t.jsxs)(T.E,{variant:"outline",className:"text-xs border-border/50",children:["+",e.length-2]})]}):null,ez=({mode:e,data:s,styling:a,isActioning:r=!1,onPrimaryAction:l,onSecondaryAction:i,onClick:n})=>{let{avatar:c,color:d}=a;return(0,t.jsxs)("div",{className:"group relative bg-card rounded-2xl overflow-hidden shadow-sm transition-all duration-300 border border-border/50 hover:border-primary/20 cursor-pointer",onClick:()=>n?.(s),children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"}),(0,t.jsxs)("div",{className:"relative p-6",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,t.jsx)(eR,{avatar:c,color:d}),(()=>{switch(e){case"marketplace":return(0,t.jsx)(eP,{isKortixTeam:s.is_kortix_team});case"template":return(0,t.jsx)(eS,{isPublic:s.is_public});case"agent":return(0,t.jsx)(eq,{agent:s});default:return null}})()]}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-foreground mb-2 line-clamp-1",children:s.name}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-4 line-clamp-2 min-h-[2.5rem]",children:s.description||"No description available"}),(0,t.jsx)(eL,{tags:s.tags}),(0,t.jsx)("div",{className:"mt-4 mb-4",children:(()=>{switch(e){case"marketplace":return(0,t.jsx)(e$,{data:s});case"template":return(0,t.jsx)(eT,{data:s});case"agent":return(0,t.jsx)(eM,{});default:return null}})()}),(()=>{switch(e){case"marketplace":return(0,t.jsx)(eF,{onAction:l,isActioning:r,data:s});case"template":return(0,t.jsx)(eE,{data:s,onPrimaryAction:l,onSecondaryAction:i,isActioning:r});default:return null}})()]})]})},eI=({agent:e,isOpen:s,onClose:a,onCustomize:r,onChat:l,onPublish:i,onUnpublish:n,isPublishing:c,isUnpublishing:d})=>{if(!e)return null;let{avatar:o,color:m}=(e=>e.avatar&&e.avatar_color?{avatar:e.avatar,color:e.avatar_color}:(0,k.Z)(e.agent_id))(e);return(0,t.jsx)(v.lG,{open:s,onOpenChange:a,children:(0,t.jsxs)(v.Cf,{className:"max-w-md p-0 overflow-hidden border-none",children:[(0,t.jsx)(v.L3,{className:"sr-only",children:"Agent actions"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)("div",{className:"h-32 flex items-center justify-center relative bg-gradient-to-br from-opacity-90 to-opacity-100",style:{backgroundColor:m},children:[(0,t.jsx)("div",{className:"text-6xl drop-shadow-sm",children:o}),(0,t.jsx)("div",{className:"absolute top-4 right-4 flex gap-2",children:e.is_default&&(0,t.jsx)(R.A,{className:"h-5 w-5 text-white fill-white drop-shadow-sm"})})]}),(0,t.jsxs)("div",{className:"p-4 space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-foreground",children:e.name}),e.current_version&&(0,t.jsxs)(T.E,{variant:"outline",className:"text-xs",children:[(0,t.jsx)(eN.A,{className:"h-3 w-3 mr-1"}),e.current_version.version_name]}),e.is_public&&(0,t.jsxs)(T.E,{variant:"outline",className:"text-xs",children:[(0,t.jsx)(P.A,{className:"h-3 w-3 mr-1"}),"Published"]})]}),(0,t.jsx)("p",{className:"text-muted-foreground text-sm leading-relaxed",children:((e,s=120)=>!e||e.length<=s?e||"Try out this agent":e.substring(0,s)+"...")(e.description)})]}),(0,t.jsxs)("div",{className:"flex gap-3 pt-2",children:[(0,t.jsxs)(g.$,{onClick:()=>r(e.agent_id),variant:"outline",className:"flex-1 gap-2",children:[(0,t.jsx)(eb.A,{className:"h-4 w-4"}),"Customize"]}),(0,t.jsxs)(g.$,{onClick:()=>l(e.agent_id),className:"flex-1 gap-2 bg-primary hover:bg-primary/90",children:[(0,t.jsx)(e_.A,{className:"h-4 w-4"}),"Chat"]})]}),(0,t.jsx)("div",{className:"pt-2",children:e.is_public?(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm text-muted-foreground",children:[(0,t.jsx)("span",{children:"Published as secure template"}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(b.A,{className:"h-3 w-3"}),e.download_count||0," downloads"]})]}),(0,t.jsx)(g.$,{onClick:()=>n(e.agent_id),disabled:d,variant:"outline",className:"w-full gap-2",children:d?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"}),"Making Private..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(ew,{className:"h-4 w-4"}),"Make Private"]})})]}):(0,t.jsx)(g.$,{onClick:()=>i(e.agent_id),disabled:c,variant:"outline",className:"w-full gap-2",children:c?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent"}),"Publishing..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(P.A,{className:"h-4 w-4"}),"Publish as Template"]})})})]})]})]})})},eO=({agents:e,onEditAgent:s,onDeleteAgent:a,onToggleDefault:l,deleteAgentMutation:c})=>{let[d,p]=(0,r.useState)(null),[f,j]=(0,r.useState)(null),[v,y]=(0,r.useState)(null),N=(0,n.useRouter)(),b=h(),_=function(){let e=(0,o.jE)();return(0,m.n)({mutationFn:async e=>{let s=(0,u.U)(),{data:{session:a}}=await s.auth.getSession();if(!a)throw Error("You must be logged in to create templates");let t=await fetch(`${x}/templates`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a.access_token}`},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json().catch(()=>({message:"Unknown error"}))).message||`HTTP ${t.status}: ${t.statusText}`);return t.json()},onSuccess:()=>{e.invalidateQueries({queryKey:["secure-mcp","marketplace-templates"]}),e.invalidateQueries({queryKey:["secure-mcp","my-templates"]})}})}(),w=e=>{p(e)},C=async e=>{try{j(e),await _.mutateAsync({agent_id:e,make_public:!0,tags:[]}),i.oR.success("Agent published!"),p(null)}catch(e){i.oR.error("Failed to create secure template")}finally{j(null)}},A=async e=>{try{y(e),await b.mutateAsync(e),i.oR.success("Agent made private"),p(null)}catch(e){i.oR.error("Failed to make agent private")}finally{y(null)}},P=e=>e.avatar&&e.avatar_color?{avatar:e.avatar,color:e.avatar_color}:(0,k.Z)(e.agent_id);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"grid gap-4 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:e.map(e=>{let s={...e,id:e.agent_id};return(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)(ez,{mode:"agent",data:s,styling:P(e),onClick:()=>w(e)}),(0,t.jsx)("div",{className:"absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity",children:!e.is_default&&(0,t.jsxs)(H.Lt,{children:[(0,t.jsx)(H.tv,{asChild:!0,children:(0,t.jsx)(g.$,{variant:"ghost",size:"sm",className:"h-7 w-7 p-0 hover:bg-destructive/10 hover:text-destructive text-muted-foreground",disabled:c.isPending,title:"Delete agent",onClick:e=>e.stopPropagation(),children:(0,t.jsx)(J.A,{className:"h-3.5 w-3.5"})})}),(0,t.jsxs)(H.EO,{className:"max-w-md",children:[(0,t.jsxs)(H.wd,{children:[(0,t.jsx)(H.r7,{className:"text-xl",children:"Delete Agent"}),(0,t.jsxs)(H.$v,{children:['Are you sure you want to delete "',e.name,'"? This action cannot be undone.',e.is_public&&(0,t.jsx)("span",{className:"block mt-2 text-amber-600 dark:text-amber-400",children:"Note: This agent is currently published to the marketplace and will be removed from there as well."})]})]}),(0,t.jsxs)(H.ck,{children:[(0,t.jsx)(H.Zr,{onClick:e=>e.stopPropagation(),children:"Cancel"}),(0,t.jsx)(H.Rx,{onClick:s=>{s.stopPropagation(),a(e.agent_id)},disabled:c.isPending,className:"bg-destructive hover:bg-destructive/90 text-white",children:c.isPending?"Deleting...":"Delete"})]})]})]})})]},e.agent_id)})}),(0,t.jsx)(eI,{agent:d,isOpen:!!d,onClose:()=>p(null),onCustomize:e=>{N.push(`/agents/config/${e}`),p(null)},onChat:e=>{N.push(`/dashboard?agent_id=${e}`),p(null)},onPublish:C,onUnpublish:A,isPublishing:f===d?.agent_id,isUnpublishing:v===d?.agent_id})]})};var eD=a(85726);let eU=({viewMode:e})=>(0,t.jsx)("div",{className:"grid"===e?"grid gap-6 sm:grid-cols-2 lg:grid-cols-4":"space-y-4",children:Array.from({length:"grid"===e?4:8},(e,s)=>(0,t.jsxs)("div",{className:"bg-neutral-100 dark:bg-sidebar border border-border rounded-2xl overflow-hidden",children:[(0,t.jsx)(eD.Skeleton,{className:"h-50"}),(0,t.jsxs)("div",{className:"p-4 space-y-3",children:[(0,t.jsx)(eD.Skeleton,{className:"h-5 rounded"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eD.Skeleton,{className:"h-4 rounded"}),(0,t.jsx)(eD.Skeleton,{className:"h-4 rounded w-3/4"})]})]})]},s))});var eG=a(47033),eZ=a(93661),eK=a(14952);let eV=({currentPage:e,totalPages:s,onPageChange:a,isLoading:r=!1})=>{if(s<=1)return null;let i=(()=>{let a=[],t=[];for(let t=Math.max(2,e-2);t<=Math.min(s-1,e+2);t++)a.push(t);return e-2>2?t.push(1,"..."):t.push(1),t.push(...a),e+2<s-1?t.push("...",s):s>1&&t.push(s),t})();return(0,t.jsxs)("div",{className:"flex items-center justify-center space-x-2",children:[(0,t.jsx)(g.$,{variant:"outline",size:"sm",onClick:()=>a(e-1),disabled:e<=1||r,className:"h-8 w-8 p-0",children:(0,t.jsx)(eG.A,{className:"h-4 w-4"})}),i.map((s,i)=>(0,t.jsx)(l().Fragment,{children:"..."===s?(0,t.jsx)("div",{className:"flex h-8 w-8 items-center justify-center",children:(0,t.jsx)(eZ.A,{className:"h-4 w-4"})}):(0,t.jsx)(g.$,{variant:e===s?"default":"outline",size:"sm",onClick:()=>a(s),disabled:r,className:"h-8 w-8 p-0",children:s})},i)),(0,t.jsx)(g.$,{variant:"outline",size:"sm",onClick:()=>a(e+1),disabled:e>=s||r,className:"h-8 w-8 p-0",children:(0,t.jsx)(eK.A,{className:"h-4 w-4"})})]})},eH=({agentsSearchQuery:e,setAgentsSearchQuery:s,agentsLoading:a,agents:r,agentsPagination:l,viewMode:i,onCreateAgent:n,onEditAgent:c,onDeleteAgent:d,onToggleDefault:o,onClearFilters:m,deleteAgentMutation:u,setAgentsPage:x})=>(0,t.jsxs)("div",{className:"space-y-6 mt-8",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between mb-6",children:[(0,t.jsx)(ev,{placeholder:"Search your agents...",value:e,onChange:s}),(0,t.jsxs)(g.$,{onClick:n,className:"rounded-xl",children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),"Create Agent"]})]}),a?(0,t.jsx)(eU,{viewMode:i}):0===r.length?(0,t.jsx)(ey,{hasAgents:(l?.total||0)>0,onCreateAgent:n,onClearFilters:m}):(0,t.jsx)(eO,{agents:r,onEditAgent:c,onDeleteAgent:d,onToggleDefault:o,deleteAgentMutation:u}),l&&l.pages>1&&(0,t.jsx)(eV,{currentPage:l.page,totalPages:l.pages,onPageChange:x,isLoading:a})]}),eQ=({title:e,subtitle:s,icon:a=(0,t.jsx)(P.A,{className:"h-5 w-5 text-white"})})=>(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"flex h-10 w-10 items-center justify-center rounded-2xl bg-gradient-to-br from-blue-500 to-blue-600 shadow-lg",children:a}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-foreground",children:e}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:s})]})]}),eB=({marketplaceSearchQuery:e,setMarketplaceSearchQuery:s,marketplaceFilter:a,setMarketplaceFilter:r,marketplaceLoading:l,allMarketplaceItems:i,kortixTeamItems:n,communityItems:c,installingItemId:d,onInstallClick:o,getItemStyling:m})=>(0,t.jsxs)("div",{className:"space-y-6 mt-8",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[(0,t.jsx)(ev,{placeholder:"Search agents...",value:e,onChange:s}),(0,t.jsxs)($.l6,{value:a,onValueChange:e=>r(e),children:[(0,t.jsx)($.bq,{className:"w-[180px] h-12 rounded-xl",children:(0,t.jsx)($.yv,{placeholder:"Filter agents"})}),(0,t.jsxs)($.gC,{children:[(0,t.jsx)($.eb,{value:"all",children:"All Agents"}),(0,t.jsx)($.eb,{value:"kortix",children:"Kortix Verified"}),(0,t.jsx)($.eb,{value:"community",children:"Community"})]})]})]}),l?(0,t.jsx)("div",{className:"grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:Array.from({length:8}).map((e,s)=>(0,t.jsxs)("div",{className:"bg-card rounded-2xl overflow-hidden shadow-sm",children:[(0,t.jsx)(eD.Skeleton,{className:"h-48"}),(0,t.jsxs)("div",{className:"p-6 space-y-3",children:[(0,t.jsx)(eD.Skeleton,{className:"h-5 rounded"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(eD.Skeleton,{className:"h-4 rounded"}),(0,t.jsx)(eD.Skeleton,{className:"h-4 rounded w-3/4"})]}),(0,t.jsx)(eD.Skeleton,{className:"h-10 rounded-full"})]})]},s))}):0===i.length?(0,t.jsx)("div",{className:"text-center py-12",children:(0,t.jsx)("p",{className:"text-muted-foreground",children:e?"No templates found matching your criteria. Try adjusting your search or filters.":"No agent templates are currently available in the marketplace."})}):(0,t.jsx)("div",{className:"space-y-12",children:"all"===a?(0,t.jsxs)(t.Fragment,{children:[n.length>0&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(eQ,{title:"Verified by Kortix",subtitle:"Official agents, maintained and supported"}),(0,t.jsx)("div",{className:"grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:n.map(e=>(0,t.jsx)(ez,{mode:"marketplace",data:e,styling:m(e),isActioning:d===e.id,onPrimaryAction:o,onClick:()=>o(e)},e.id))})]}),c.length>0&&(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsx)("div",{className:"grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:c.map(e=>(0,t.jsx)(ez,{mode:"marketplace",data:e,styling:m(e),isActioning:d===e.id,onPrimaryAction:o,onClick:()=>o(e)},e.id))})})]}):(0,t.jsx)("div",{className:"grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:i.map(e=>(0,t.jsx)(ez,{mode:"marketplace",data:e,styling:m(e),isActioning:d===e.id,onPrimaryAction:o,onClick:()=>o(e)},e.id))})})]});var eY=a(43649);let eW=({templatesError:e,templatesLoading:s,myTemplates:a,templatesActioningId:r,onPublish:l,onUnpublish:i,onViewInMarketplace:n,onSwitchToMyAgents:c,getTemplateStyling:d})=>(0,t.jsx)("div",{className:"space-y-6 mt-8",children:e?(0,t.jsxs)(A.Fc,{variant:"destructive",className:"rounded-2xl",children:[(0,t.jsx)(eY.A,{className:"h-4 w-4"}),(0,t.jsx)(A.TN,{children:"Failed to load your templates. Please try again later."})]}):s?(0,t.jsx)("div",{className:"grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:Array.from({length:8}).map((e,s)=>(0,t.jsxs)("div",{className:"bg-card rounded-2xl overflow-hidden shadow-sm",children:[(0,t.jsx)(eD.Skeleton,{className:"h-48"}),(0,t.jsxs)("div",{className:"p-6 space-y-3",children:[(0,t.jsx)(eD.Skeleton,{className:"h-5 rounded"}),(0,t.jsx)(eD.Skeleton,{className:"h-4 rounded w-3/4"}),(0,t.jsx)("div",{className:"flex gap-2",children:(0,t.jsx)(eD.Skeleton,{className:"h-10 rounded-full flex-1"})})]})]},s))}):a?.length===0?(0,t.jsxs)("div",{className:"text-center py-16",children:[(0,t.jsx)("div",{className:"mx-auto w-20 h-20 bg-gradient-to-br from-primary/20 to-primary/10 rounded-3xl flex items-center justify-center mb-6",children:(0,t.jsx)(ek.A,{className:"h-10 w-10 text-primary"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold mb-3",children:"No templates yet"}),(0,t.jsx)("p",{className:"text-muted-foreground mb-8 max-w-md mx-auto",children:"Create your first secure agent template to share with the community while keeping your credentials safe."}),(0,t.jsxs)(g.$,{onClick:c,size:"lg",children:[(0,t.jsx)(S.A,{className:"h-4 w-4 mr-2"}),"Create Your First Agent"]})]}):(0,t.jsx)("div",{className:"grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",children:a?.map(e=>{let s=r===e.template_id;return(0,t.jsx)(ez,{mode:"template",data:e,styling:d(e),isActioning:s,onPrimaryAction:e.is_public?()=>i(e.template_id,e.name):()=>l(e),onSecondaryAction:e.is_public?n:void 0},e.template_id)})})}),eX=({publishDialog:e,publishTags:s,templatesActioningId:a,onClose:r,onPublishTagsChange:l,onPublish:i})=>(0,t.jsx)(v.lG,{open:!!e,onOpenChange:r,children:(0,t.jsxs)(v.Cf,{className:"sm:max-w-md",children:[(0,t.jsxs)(v.c7,{children:[(0,t.jsx)(v.L3,{children:"Publish Template to Marketplace"}),(0,t.jsxs)(v.rr,{children:['Make "',e?.templateName,'" available for the community to discover and install.']})]}),(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{children:[(0,t.jsx)(j.Label,{htmlFor:"tags",children:"Tags (optional)"}),(0,t.jsx)(f.p,{id:"tags",placeholder:"automation, productivity, data-analysis",value:s,onChange:e=>l(e.target.value),className:"mt-1"}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Separate tags with commas to help users discover your template"})]})}),(0,t.jsxs)(v.Es,{children:[(0,t.jsx)(g.$,{variant:"outline",onClick:r,disabled:!!a,children:"Cancel"}),(0,t.jsx)(g.$,{onClick:i,disabled:!!a,children:a?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(y.A,{className:"h-4 w-4 animate-spin mr-2"}),"Publishing..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(ek.A,{className:"h-4 w-4 mr-2"}),"Publish Template"]})})]})]})}),eJ=({count:e=6})=>(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:Array.from({length:e}).map((e,s)=>(0,t.jsxs)("div",{className:"p-2 bg-neutral-100 dark:bg-sidebar rounded-2xl overflow-hidden group",children:[(0,t.jsx)("div",{className:"h-24 flex items-center justify-center relative bg-gradient-to-br from-opacity-90 to-opacity-100",children:(0,t.jsx)(eD.Skeleton,{className:"h-24 w-full rounded-xl"})}),(0,t.jsxs)("div",{className:"space-y-2 mt-4 mb-4",children:[(0,t.jsx)(eD.Skeleton,{className:"h-6 w-32 rounded"}),(0,t.jsx)(eD.Skeleton,{className:"h-4 w-24 rounded"})]})]},s))});function e0(){let{enabled:e,loading:s}=(0,p.u)("custom_agents"),{enabled:a,loading:l}=(0,p.u)("agent_marketplace");(0,n.useRouter)();let[g,f]=(0,r.useState)("my-agents"),[j,v]=(0,r.useState)(!1),[y,N]=(0,r.useState)(null),[b,_]=(0,r.useState)("grid"),[w,C]=(0,r.useState)(1),[A,P]=(0,r.useState)(""),[S,q]=(0,r.useState)("created_at"),[$,T]=(0,r.useState)("desc"),[M,F]=(0,r.useState)({hasDefaultAgent:!1,hasMcpTools:!1,hasAgentpressTools:!1,selectedTools:[]}),[E,R]=(0,r.useState)(1),[L,z]=(0,r.useState)(""),[I,O]=(0,r.useState)([]),[D,U]=(0,r.useState)("newest"),[G,Z]=(0,r.useState)(null),[K,V]=(0,r.useState)(null),[H,Q]=(0,r.useState)(!1),[B,Y]=(0,r.useState)("all"),[W,X]=(0,r.useState)(null),[J,ee]=(0,r.useState)(null),[es,ea]=(0,r.useState)(""),et=(0,r.useMemo)(()=>{let e={page:w,limit:20,search:A||void 0,sort_by:S,sort_order:$};return M.hasDefaultAgent&&(e.has_default=!0),M.hasMcpTools&&(e.has_mcp_tools=!0),M.hasAgentpressTools&&(e.has_agentpress_tools=!0),M.selectedTools.length>0&&(e.tools=M.selectedTools.join(",")),e},[w,A,S,$,M]),er=(0,r.useMemo)(()=>({limit:20,offset:(E-1)*20,search:L||void 0,tags:I.length>0?I.join(","):void 0}),[E,L,I]),{data:el,isLoading:ei,error:ec,refetch:ed}=(0,c._F)(et),{data:em,isLoading:eu}=(0,d.I)({queryKey:["secure-mcp","marketplace-templates",er],queryFn:async()=>{let e=(0,u.U)(),{data:{session:s}}=await e.auth.getSession();if(!s)throw Error("You must be logged in to view marketplace templates");let a=new URLSearchParams;er?.limit&&a.set("limit",er.limit.toString()),er?.offset&&a.set("offset",er.offset.toString()),er?.search&&a.set("search",er.search),er?.tags&&a.set("tags",er.tags);let t=await fetch(`${x}/templates/marketplace?${a}`,{headers:{Authorization:`Bearer ${s.access_token}`}});if(!t.ok)throw Error((await t.json().catch(()=>({message:"Unknown error"}))).message||`HTTP ${t.status}: ${t.statusText}`);return t.json()}}),{data:ex,isLoading:eh,error:ep}=(0,d.I)({queryKey:["secure-mcp","my-templates"],queryFn:async()=>{let e=(0,u.U)(),{data:{session:s}}=await e.auth.getSession();if(!s)throw Error("You must be logged in to view your templates");let a=await fetch(`${x}/templates/my`,{headers:{Authorization:`Bearer ${s.access_token}`}});if(!a.ok)throw Error((await a.json().catch(()=>({message:"Unknown error"}))).message||`HTTP ${a.status}: ${a.statusText}`);return a.json()}}),eg=(0,c.Ae)(),ej=(0,c.gX)(),ev=(0,c.h3)(),{optimisticallyUpdateAgent:ey,revertOptimisticUpdate:eN}=(0,c.XK)(),eb=function(){let e=(0,o.jE)();return(0,m.n)({mutationFn:async e=>{let s=(0,u.U)(),{data:{session:a}}=await s.auth.getSession();if(!a)throw Error("You must be logged in to install templates");let t=await fetch(`${x}/templates/install`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a.access_token}`},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json().catch(()=>({message:"Unknown error"}))).message||`HTTP ${t.status}: ${t.statusText}`);return t.json()},onSuccess:()=>{e.invalidateQueries({queryKey:["agents"]})}})}(),e_=h(),ew=function(){let e=(0,o.jE)();return(0,m.n)({mutationFn:async({template_id:e,tags:s})=>{let a=(0,u.U)(),{data:{session:t}}=await a.auth.getSession();if(!t)throw Error("You must be logged in to publish templates");let r=await fetch(`${x}/templates/${e}/publish`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t.access_token}`},body:JSON.stringify({tags:s})});if(!r.ok)throw Error((await r.json().catch(()=>({message:"Unknown error"}))).message||`HTTP ${r.status}: ${r.statusText}`);return r.json()},onSuccess:()=>{e.invalidateQueries({queryKey:["secure-mcp","marketplace-templates"]}),e.invalidateQueries({queryKey:["secure-mcp","my-templates"]})}})}(),ek=el?.agents||[],eC=el?.pagination,{kortixTeamItems:eA,communityItems:eP}=(0,r.useMemo)(()=>{let e=[],s=[];em&&em.forEach(a=>{let t={id:a.template_id,name:a.name,description:a.description,tags:a.tags||[],download_count:a.download_count||0,creator_name:a.creator_name||"Anonymous",created_at:a.created_at,marketplace_published_at:a.marketplace_published_at,avatar:a.avatar,avatar_color:a.avatar_color,template_id:a.template_id,is_kortix_team:a.is_kortix_team,mcp_requirements:a.mcp_requirements,metadata:a.metadata};a.is_kortix_team?e.push(t):s.push(t)});let a=e=>e.sort((e,s)=>{switch(D){case"newest":return new Date(s.marketplace_published_at||s.created_at).getTime()-new Date(e.marketplace_published_at||e.created_at).getTime();case"popular":case"most_downloaded":return s.download_count-e.download_count;case"name":return e.name.localeCompare(s.name);default:return 0}});return{kortixTeamItems:a(e),communityItems:a(s)}},[em,D]),eS=(0,r.useMemo)(()=>"kortix"===B?eA:"community"===B?eP:[...eA,...eP],[eA,eP,B]),eq=e=>{f(e)},e$=async e=>{try{await ej.mutateAsync(e)}catch(e){console.error("Error deleting agent:",e)}},eT=async(e,s)=>{ey(e,{is_default:!s});try{await eg.mutateAsync({agentId:e,is_default:!s})}catch(s){eN(e),console.error("Error updating agent:",s)}},eM=async(e,s,a,t)=>{Z(e.id);try{if(!s||""===s.trim())return void i.oR.error("Please provide a name for the agent");let r=(e.mcp_requirements?.filter(e=>!e.custom_type)||[]).filter(e=>!a||!a[e.qualified_name]||""===a[e.qualified_name].trim());if(r.length>0){let e=r.map(e=>e.display_name).join(", ");i.oR.error(`Please select credential profiles for: ${e}`);return}let l=(e.mcp_requirements?.filter(e=>e.custom_type&&"pipedream"!==e.custom_type)||[]).filter(e=>!t||!t[e.qualified_name]||e.required_config.some(s=>!t[e.qualified_name][s]?.trim()));if(l.length>0){let e=l.map(e=>e.display_name).join(", ");i.oR.error(`Please provide all required configuration for: ${e}`);return}let n=(e.mcp_requirements?.filter(e=>"pipedream"===e.custom_type)||[]).filter(e=>!t||!t[e.qualified_name]||!t[e.qualified_name].profile_id);if(n.length>0){let e=n.map(e=>e.display_name).join(", ");i.oR.error(`Please select Pipedream profiles for: ${e}`);return}let c=await eb.mutateAsync({template_id:e.template_id,instance_name:s,profile_mappings:a,custom_mcp_configs:t});if("installed"===c.status)i.oR.success(`Agent "${s}" installed successfully!`),Q(!1),eq("my-agents");else if("configs_required"===c.status)return void i.oR.error("Please provide all required configurations");else return void i.oR.error("Unexpected response from server. Please try again.")}catch(e){console.error("Installation error:",e),e.message?.includes("already in your library")?i.oR.error("This agent is already in your library"):e.message?.includes("Credential profile not found")?i.oR.error("One or more selected credential profiles could not be found. Please refresh and try again."):e.message?.includes("Missing credential profile")?i.oR.error("Please select credential profiles for all required services"):e.message?.includes("Invalid credential profile")?i.oR.error("One or more selected credential profiles are invalid. Please select valid profiles."):e.message?.includes("inactive")?i.oR.error("One or more selected credential profiles are inactive. Please select active profiles."):e.message?.includes("Template not found")?i.oR.error("This agent template is no longer available"):e.message?.includes("Access denied")?i.oR.error("You do not have permission to install this agent"):i.oR.error(e.message||"Failed to install agent. Please try again.")}finally{Z(null)}},eF=async(e,s)=>{try{X(e),await e_.mutateAsync(e),i.oR.success(`${s} has been unpublished from the marketplace`)}catch(e){i.oR.error(e.message||"Failed to unpublish template")}finally{X(null)}},eE=async()=>{if(J)try{X(J.templateId);let e=es.split(",").map(e=>e.trim()).filter(e=>e.length>0);await ew.mutateAsync({template_id:J.templateId,tags:e.length>0?e:void 0}),i.oR.success(`${J.templateName} has been published to the marketplace`),ee(null),ea("")}catch(e){i.oR.error(e.message||"Failed to publish template")}finally{X(null)}};return s||l?(0,t.jsxs)("div",{className:"min-h-screen",children:[(0,t.jsx)("div",{className:"container max-w-7xl mx-auto px-4 py-8",children:(0,t.jsx)(eo,{})}),(0,t.jsx)("div",{className:"sticky top-0 z-50 bg-background/95 backdrop-blur-md border-b border-border/40 shadow-sm",children:(0,t.jsx)("div",{className:"container max-w-7xl mx-auto px-4 py-4",children:(0,t.jsx)(ef,{activeTab:g,onTabChange:eq})})}),(0,t.jsx)("div",{className:"container max-w-7xl mx-auto px-4 py-8",children:(0,t.jsx)(eJ,{})})]}):e?(0,t.jsxs)("div",{className:"min-h-screen",children:[(0,t.jsx)("div",{className:"container mx-auto max-w-7xl px-4 py-8",children:(0,t.jsx)(eo,{})}),(0,t.jsxs)("div",{className:"sticky top-0 z-50 relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 backdrop-blur-md",style:{maskImage:"linear-gradient(to bottom, black 0%, black 60%, transparent 100%)",WebkitMaskImage:"linear-gradient(to bottom, black 0%, black 60%, transparent 100%)"}}),(0,t.jsx)("div",{className:"relative bg-gradient-to-b from-background/95 via-background/70 to-transparent",children:(0,t.jsx)("div",{className:"container mx-auto max-w-7xl px-4 py-4",children:(0,t.jsx)(ef,{activeTab:g,onTabChange:eq})})})]}),(0,t.jsxs)("div",{className:"container mx-auto max-w-7xl px-4 py-2",children:[(0,t.jsxs)("div",{className:"w-full",children:["my-agents"===g&&(0,t.jsx)(eH,{agentsSearchQuery:A,setAgentsSearchQuery:P,agentsLoading:ei,agents:ek,agentsPagination:eC,viewMode:b,onCreateAgent:()=>{ev.mutate()},onEditAgent:e=>{N(e),v(!0)},onDeleteAgent:e$,onToggleDefault:eT,onClearFilters:()=>{P(""),F({hasDefaultAgent:!1,hasMcpTools:!1,hasAgentpressTools:!1,selectedTools:[]}),C(1)},deleteAgentMutation:ej,setAgentsPage:C}),"marketplace"===g&&(0,t.jsx)(eB,{marketplaceSearchQuery:L,setMarketplaceSearchQuery:z,marketplaceFilter:B,setMarketplaceFilter:Y,marketplaceLoading:eu,allMarketplaceItems:eS,kortixTeamItems:eA,communityItems:eP,installingItemId:G,onInstallClick:(e,s)=>{s&&s.stopPropagation(),V(e),Q(!0)},getItemStyling:e=>e.avatar&&e.avatar_color?{avatar:e.avatar,color:e.avatar_color}:(0,k.Z)(e.id)}),"my-templates"===g&&(0,t.jsx)(eW,{templatesError:ep,templatesLoading:eh,myTemplates:ex,templatesActioningId:W,onPublish:e=>{ee({templateId:e.template_id,templateName:e.name,currentTags:e.tags||[]}),ea((e.tags||[]).join(", "))},onUnpublish:eF,onViewInMarketplace:()=>eq("marketplace"),onSwitchToMyAgents:()=>eq("my-agents"),getTemplateStyling:e=>e.avatar&&e.avatar_color?{avatar:e.avatar,color:e.avatar_color}:(0,k.Z)(e.template_id)})]}),(0,t.jsx)(eX,{publishDialog:J,publishTags:es,templatesActioningId:W,onClose:()=>ee(null),onPublishTagsChange:ea,onPublish:eE}),(0,t.jsx)(en,{item:K,open:H,onOpenChange:Q,onInstall:eM,isInstalling:G===K?.id})]})]}):null}},34631:e=>{"use strict";e.exports=require("tls")},35071:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},40228:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},40481:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\(dashboard)\\\\agents\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\(dashboard)\\agents\\page.tsx","default")},45583:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},47033:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},51455:e=>{"use strict";e.exports=require("node:fs/promises")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57975:e=>{"use strict";e.exports=require("node:util")},58869:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64398:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},67821:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("GitBranch",[["line",{x1:"6",x2:"6",y1:"3",y2:"15",key:"17qcm7"}],["circle",{cx:"18",cy:"6",r:"3",key:"1h7g24"}],["circle",{cx:"6",cy:"18",r:"3",key:"fqmcym"}],["path",{d:"M18 9a9 9 0 0 1-9 9",key:"n2h4wq"}]])},69259:(e,s,a)=>{"use strict";a.d(s,{z:()=>c});var t=a(60687),r=a(43210),l=a.n(r),i=a(4780);let n=l().memo(function({mainCircleSize:e=210,mainCircleOpacity:s=.24,numCircles:a=8,className:r,...l}){return(0,t.jsx)("div",{className:(0,i.cn)("scale-150 pointer-events-none absolute inset-0 select-none [mask-image:linear-gradient(to_bottom,white,transparent)]",r),...l,children:Array.from({length:a},(a,r)=>{let l=e+70*r,i=s-.03*r,n=`${.06*r}s`;return(0,t.jsx)("div",{className:"absolute animate-ripple rounded-full border bg-foreground/25 shadow-xl",style:{"--i":r,width:`${l}px`,height:`${l}px`,opacity:i,animationDelay:n,borderStyle:"solid",borderWidth:"1px",borderColor:"var(--foreground)",top:"50%",left:"50%",transform:"translate(-50%, -50%) scale(1)"}},r)})})});n.displayName="Ripple";let c=({icon:e,children:s})=>(0,t.jsxs)("div",{className:"relative overflow-hidden rounded-3xl flex items-center justify-center border bg-background",children:[(0,t.jsx)("div",{className:"relative px-8 py-16 text-center",children:(0,t.jsxs)("div",{className:"mx-auto max-w-3xl space-y-6",children:[(0,t.jsx)("div",{className:"inline-flex items-center justify-center rounded-full bg-muted p-3",children:(0,t.jsx)(e,{className:"h-8 w-8 text-primary"})}),(0,t.jsx)("h1",{className:"text-4xl font-semibold tracking-tight text-foreground",children:s})]})}),(0,t.jsx)(n,{})]})},70334:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},71057:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},74075:e=>{"use strict";e.exports=require("zlib")},77598:e=>{"use strict";e.exports=require("node:crypto")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},81904:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},84027:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},84297:e=>{"use strict";e.exports=require("async_hooks")},90270:(e,s,a)=>{"use strict";a.d(s,{bL:()=>b,zi:()=>_});var t=a(43210),r=a(70569),l=a(98599),i=a(11273),n=a(65551),c=a(83721),d=a(18853),o=a(14163),m=a(60687),u="Switch",[x,h]=(0,i.A)(u),[p,g]=x(u),f=t.forwardRef((e,s)=>{let{__scopeSwitch:a,name:i,checked:c,defaultChecked:d,required:x,disabled:h,value:g="on",onCheckedChange:f,form:j,...v}=e,[b,_]=t.useState(null),w=(0,l.s)(s,e=>_(e)),k=t.useRef(!1),C=!b||j||!!b.closest("form"),[A,P]=(0,n.i)({prop:c,defaultProp:d??!1,onChange:f,caller:u});return(0,m.jsxs)(p,{scope:a,checked:A,disabled:h,children:[(0,m.jsx)(o.sG.button,{type:"button",role:"switch","aria-checked":A,"aria-required":x,"data-state":N(A),"data-disabled":h?"":void 0,disabled:h,value:g,...v,ref:w,onClick:(0,r.m)(e.onClick,e=>{P(e=>!e),C&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),C&&(0,m.jsx)(y,{control:b,bubbles:!k.current,name:i,value:g,checked:A,required:x,disabled:h,form:j,style:{transform:"translateX(-100%)"}})]})});f.displayName=u;var j="SwitchThumb",v=t.forwardRef((e,s)=>{let{__scopeSwitch:a,...t}=e,r=g(j,a);return(0,m.jsx)(o.sG.span,{"data-state":N(r.checked),"data-disabled":r.disabled?"":void 0,...t,ref:s})});v.displayName=j;var y=t.forwardRef(({__scopeSwitch:e,control:s,checked:a,bubbles:r=!0,...i},n)=>{let o=t.useRef(null),u=(0,l.s)(o,n),x=(0,c.Z)(a),h=(0,d.X)(s);return t.useEffect(()=>{let e=o.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(x!==a&&s){let t=new Event("click",{bubbles:r});s.call(e,a),e.dispatchEvent(t)}},[x,a,r]),(0,m.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...i,tabIndex:-1,ref:u,style:{...i.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function N(e){return e?"checked":"unchecked"}y.displayName="SwitchBubbleInput";var b=f,_=v},91645:e=>{"use strict";e.exports=require("net")},92485:(e,s,a)=>{Promise.resolve().then(a.bind(a,40481))},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{},99270:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},99891:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(62688).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[7719,5193,4267,7096,1265,3530,7156,7976,4097,3667,8188,3806,1841,5966,4940],()=>a(20572));module.exports=t})();