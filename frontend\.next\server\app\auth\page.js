(()=>{var e={};e.id=8365,e.ids=[8365],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5876:(e,t,r)=>{"use strict";r.d(t,{U:()=>o});var s=r(91199);r(42087);var a=r(30445),n=r(74208),i=r(33331);let o=async()=>{let e=await (0,n.UL)(),t="";return t&&!t.startsWith("http")&&(t=`http://${t}`),(0,a.createServerClient)(t,"",{cookies:{getAll:()=>e.getAll(),setAll(t){try{t.forEach(({name:t,value:r,options:s})=>e.set({name:t,value:r,...s}))}catch(e){}}}})};(0,i.D)([o]),(0,s.A)(o,"7f8bed79c8654f95685745e61906af37f96a086236",null)},9828:(e,t,r)=>{Promise.resolve().then(r.bind(r,26284))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26284:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>A});var s=r(60687),a=r(85814),n=r.n(a),i=r(44774),o=r(89667),l=r(92819),d=r(13488),c=r(63977),u=r(43210),m=r(92211),g=r(6475);let x=(0,g.createServerReference)("604991a10ed242e0f6ea33f223b3055ecb96bfab5b",g.callServer,void 0,g.findSourceMapURL,"signIn"),f=(0,g.createServerReference)("609c31419be1d42bf9c9b8ca0643e758692237b99d",g.callServer,void 0,g.findSourceMapURL,"signUp"),b=(0,g.createServerReference)("60a6205911ff9573610eaa1755309d0261975c088b",g.callServer,void 0,g.findSourceMapURL,"forgotPassword");var p=r(16189),h=r(41862);let v=(0,r(62688).A)("MailCheck",[["path",{d:"M22 13V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v12c0 1.1.9 2 2 2h8",key:"12jkf8"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}],["path",{d:"m16 19 2 2 4-4",key:"1b14m6"}]]);var w=r(50940),y=r(93613),j=r(11860),k=r(5336),N=r(58297),C=r(63503),S=r(26351);function P(){let e=(0,p.useRouter)(),t=(0,p.useSearchParams)(),{user:r,isLoading:a}=(0,N.A)(),g=t.get("mode"),P=t.get("returnUrl"),A=t.get("message"),z="signup"===g,_=(0,c.U)("(max-width: 1024px)"),[U,F]=(0,u.useState)(!1),[q,I]=(0,u.useState)(!1);(0,u.useRef)(null);let{scrollY:G}=(0,m.L)(),E=A&&(A.includes("Check your email")||A.includes("Account created")||A.includes("success")),[T,R]=(0,u.useState)(!!E),[L,H]=(0,u.useState)(""),[M,B]=(0,u.useState)(!1),[$,D]=(0,u.useState)(""),[O,W]=(0,u.useState)({}),V=async(e,t)=>{P?t.append("returnUrl",P):t.append("returnUrl","/dashboard");let r=await x(e,t);return r&&"object"==typeof r&&"success"in r&&r.success&&"redirectTo"in r?(window.location.href=r.redirectTo,null):r},X=async(e,t)=>{H(t.get("email")),P&&t.append("returnUrl",P),t.append("origin",window.location.origin);let r=await f(e,t);if(r&&"object"==typeof r&&"success"in r&&r.success&&"redirectTo"in r)return window.location.href=r.redirectTo,null;if(r&&"object"==typeof r&&"message"in r){let e=r.message;if(e.includes("Check your email")){R(!0);let t=new URLSearchParams(window.location.search);t.set("message",e);let r=window.location.pathname+(t.toString()?"?"+t.toString():"");window.history.pushState({path:r},"",r)}}return r},K=async e=>{if(e.preventDefault(),W({}),!$||!$.includes("@"))return void W({success:!1,message:"Please enter a valid email address"});let t=new FormData;t.append("email",$),t.append("origin",window.location.origin),W(await b(null,t))};return a?(0,s.jsx)("main",{className:"flex flex-col items-center justify-center min-h-screen w-full",children:(0,s.jsx)(h.A,{className:"h-12 w-12 animate-spin text-primary"})}):T?(0,s.jsx)("main",{className:"flex flex-col items-center justify-center min-h-screen w-full",children:(0,s.jsx)("div",{className:"w-full divide-y divide-border",children:(0,s.jsx)("section",{className:"w-full relative overflow-hidden",children:(0,s.jsxs)("div",{className:"relative flex flex-col items-center w-full px-6",children:[(0,s.jsxs)("div",{className:"absolute left-0 top-0 h-[600px] md:h-[800px] w-1/3 -z-10 overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-background z-10"}),(0,s.jsx)("div",{className:"absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10"}),(0,s.jsx)("div",{className:"absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10"})]}),(0,s.jsxs)("div",{className:"absolute right-0 top-0 h-[600px] md:h-[800px] w-1/3 -z-10 overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-background z-10"}),(0,s.jsx)("div",{className:"absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10"}),(0,s.jsx)("div",{className:"absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10"})]}),(0,s.jsx)("div",{className:"absolute inset-x-1/4 top-0 h-[600px] md:h-[800px] -z-20 bg-background rounded-b-xl"}),(0,s.jsx)("div",{className:"relative z-10 pt-24 pb-8 max-w-xl mx-auto h-full w-full flex flex-col gap-2 items-center justify-center",children:(0,s.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,s.jsx)("div",{className:"bg-green-50 dark:bg-green-950/20 rounded-full p-4 mb-6",children:(0,s.jsx)(v,{className:"h-12 w-12 text-green-500 dark:text-green-400"})}),(0,s.jsx)("h1",{className:"text-3xl md:text-4xl lg:text-5xl font-medium tracking-tighter text-center text-balance text-primary mb-4",children:"Check your email"}),(0,s.jsx)("p",{className:"text-base md:text-lg text-center text-muted-foreground font-medium text-balance leading-relaxed tracking-tight max-w-md mb-2",children:"We've sent a confirmation link to:"}),(0,s.jsx)("p",{className:"text-lg font-medium mb-6",children:L||"your email address"}),(0,s.jsx)("div",{className:"bg-green-50 dark:bg-green-950/20 border border-green-100 dark:border-green-900/50 rounded-lg p-6 mb-8 max-w-md w-full",children:(0,s.jsx)("p",{className:"text-sm text-green-800 dark:text-green-400 leading-relaxed",children:"Click the link in the email to activate your account. If you don't see the email, check your spam folder."})}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 w-full max-w-sm",children:[(0,s.jsx)(n(),{href:"/",className:"flex h-12 items-center justify-center w-full text-center rounded-full border border-border bg-background hover:bg-accent/20 transition-all",children:"Return to home"}),(0,s.jsx)("button",{onClick:()=>{R(!1);let t=new URLSearchParams(window.location.search);t.delete("message"),t.set("mode","signin");let r=window.location.pathname+(t.toString()?"?"+t.toString():"");window.history.pushState({path:r},"",r),e.refresh()},className:"flex h-12 items-center justify-center w-full text-center rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all shadow-md",children:"Back to sign in"})]})]})})]})})})}):(0,s.jsxs)("main",{className:"flex flex-col items-center justify-center min-h-screen w-full",children:[(0,s.jsx)("div",{className:"w-full divide-y divide-border",children:(0,s.jsxs)("section",{className:"w-full relative overflow-hidden",children:[(0,s.jsxs)("div",{className:"relative flex flex-col items-center w-full px-6",children:[(0,s.jsxs)("div",{className:"absolute left-0 top-0 h-[600px] md:h-[800px] w-1/3 -z-10 overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-background z-10"}),(0,s.jsx)("div",{className:"absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10"}),(0,s.jsx)("div",{className:"absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10"}),(0,s.jsx)("div",{className:"h-full w-full",children:(0,s.jsx)(d.b,{className:"h-full w-full",squareSize:U&&_?2:2.5,gridGap:U&&_?2:2.5,color:"var(--secondary)",maxOpacity:.4,flickerChance:q?.01:.03})})]}),(0,s.jsxs)("div",{className:"absolute right-0 top-0 h-[600px] md:h-[800px] w-1/3 -z-10 overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-background z-10"}),(0,s.jsx)("div",{className:"absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10"}),(0,s.jsx)("div",{className:"absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10"}),(0,s.jsx)("div",{className:"h-full w-full",children:(0,s.jsx)(d.b,{className:"h-full w-full",squareSize:U&&_?2:2.5,gridGap:U&&_?2:2.5,color:"var(--secondary)",maxOpacity:.4,flickerChance:q?.01:.03})})]}),(0,s.jsx)("div",{className:"absolute inset-x-1/4 top-0 h-[600px] md:h-[800px] -z-20 bg-background rounded-b-xl"}),(0,s.jsxs)("div",{className:"relative z-10 pt-24 pb-8 max-w-md mx-auto h-full w-full flex flex-col gap-2 items-center justify-center",children:[(0,s.jsxs)(n(),{href:"/",className:"group border border-border/50 bg-background hover:bg-accent/20 rounded-full text-sm h-8 px-3 flex items-center gap-2 transition-all duration-200 shadow-sm mb-6",children:[(0,s.jsx)(w.A,{className:"h-4 w-4 text-muted-foreground"}),(0,s.jsx)("span",{className:"font-medium text-muted-foreground text-xs tracking-wide",children:"Back to home"})]}),(0,s.jsx)("h1",{className:"text-3xl md:text-4xl lg:text-5xl font-medium tracking-tighter text-center text-balance text-primary",children:z?"Join Suna":"Welcome back"}),(0,s.jsx)("p",{className:"text-base md:text-lg text-center text-muted-foreground font-medium text-balance leading-relaxed tracking-tight mt-2 mb-6",children:z?"Create your account and start building with AI":"Sign in to your account to continue"})]})]}),(0,s.jsx)("div",{className:"relative z-10 flex justify-center px-6 pb-24",children:(0,s.jsxs)("div",{className:"w-full max-w-md rounded-xl bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] border border-border p-8",children:[A&&!E&&(0,s.jsxs)("div",{className:"mb-6 p-4 rounded-lg flex items-center gap-3 bg-secondary/10 border border-secondary/20 text-secondary",children:[(0,s.jsx)(y.A,{className:"h-5 w-5 flex-shrink-0 text-secondary"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:A})]}),(0,s.jsxs)("div",{className:"w-full flex flex-col gap-3 mb-6",children:[(0,s.jsx)("div",{className:"w-full",children:(0,s.jsx)(l.A,{returnUrl:P||void 0})}),(0,s.jsx)("div",{className:"w-full",children:(0,s.jsx)(S.A,{returnUrl:P||void 0})})]}),(0,s.jsxs)("div",{className:"relative my-8",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-border"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] text-muted-foreground",children:"or continue with email"})})]}),(0,s.jsxs)("form",{className:"space-y-4",children:[(0,s.jsx)("div",{children:(0,s.jsx)(o.p,{id:"email",name:"email",type:"email",placeholder:"Email address",className:"h-12 rounded-full bg-background border-border",required:!0})}),(0,s.jsx)("div",{children:(0,s.jsx)(o.p,{id:"password",name:"password",type:"password",placeholder:"Password",className:"h-12 rounded-full bg-background border-border",required:!0})}),z&&(0,s.jsx)("div",{children:(0,s.jsx)(o.p,{id:"confirmPassword",name:"confirmPassword",type:"password",placeholder:"Confirm password",className:"h-12 rounded-full bg-background border-border",required:!0})}),(0,s.jsx)("div",{className:"space-y-4 pt-4",children:z?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.SubmitButton,{formAction:X,className:"w-full h-12 rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all shadow-md",pendingText:"Creating account...",children:"Sign up"}),(0,s.jsx)(n(),{href:`/auth${P?`?returnUrl=${P}`:""}`,className:"flex h-12 items-center justify-center w-full text-center rounded-full border border-border bg-background hover:bg-accent/20 transition-all",children:"Back to sign in"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.SubmitButton,{formAction:V,className:"w-full h-12 rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all shadow-md",pendingText:"Signing in...",children:"Sign in"}),(0,s.jsx)(n(),{href:`/auth?mode=signup${P?`&returnUrl=${P}`:""}`,className:"flex h-12 items-center justify-center w-full text-center rounded-full border border-border bg-background hover:bg-accent/20 transition-all",children:"Create new account"})]})}),!z&&(0,s.jsx)("div",{className:"text-center pt-2",children:(0,s.jsx)("button",{type:"button",onClick:()=>B(!0),className:"text-sm text-primary hover:underline",children:"Forgot password?"})})]}),(0,s.jsxs)("div",{className:"mt-8 text-center text-xs text-muted-foreground",children:["By continuing, you agree to our"," ",(0,s.jsx)(n(),{href:"/terms",className:"text-primary hover:underline",children:"Terms of Service"})," ","and"," ",(0,s.jsx)(n(),{href:"/privacy",className:"text-primary hover:underline",children:"Privacy Policy"})]})]})})]})}),(0,s.jsx)(C.lG,{open:M,onOpenChange:B,children:(0,s.jsxs)(C.Cf,{className:"sm:max-w-md rounded-xl bg-[#F3F4F6] dark:bg-[#17171A] border border-border [&>button]:hidden",children:[(0,s.jsxs)(C.c7,{children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(C.L3,{className:"text-xl font-medium",children:"Reset Password"}),(0,s.jsx)("button",{onClick:()=>B(!1),className:"rounded-full p-1 hover:bg-muted transition-colors",children:(0,s.jsx)(j.A,{className:"h-4 w-4 text-muted-foreground"})})]}),(0,s.jsx)(C.rr,{className:"text-muted-foreground",children:"Enter your email address and we'll send you a link to reset your password."})]}),(0,s.jsxs)("form",{onSubmit:K,className:"space-y-4 py-4",children:[(0,s.jsx)(o.p,{id:"forgot-password-email",type:"email",placeholder:"Email address",value:$,onChange:e=>D(e.target.value),className:"h-12 rounded-full bg-background border-border",required:!0}),O.message&&(0,s.jsxs)("div",{className:`p-4 rounded-lg flex items-center gap-3 ${O.success?"bg-green-50 dark:bg-green-950/30 border border-green-200 dark:border-green-900/50 text-green-800 dark:text-green-400":"bg-secondary/10 border border-secondary/20 text-secondary"}`,children:[O.success?(0,s.jsx)(k.A,{className:"h-5 w-5 flex-shrink-0 text-green-500 dark:text-green-400"}):(0,s.jsx)(y.A,{className:"h-5 w-5 flex-shrink-0 text-secondary"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:O.message})]}),(0,s.jsxs)(C.Es,{className:"flex sm:justify-start gap-3 pt-2",children:[(0,s.jsx)("button",{type:"submit",className:"h-12 px-6 rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all shadow-md",children:"Send Reset Link"}),(0,s.jsx)("button",{type:"button",onClick:()=>B(!1),className:"h-12 px-6 rounded-full border border-border bg-background hover:bg-accent/20 transition-all",children:"Cancel"})]})]})]})})]})}function A(){return(0,s.jsx)(u.Suspense,{fallback:(0,s.jsx)("main",{className:"flex flex-col items-center justify-center min-h-screen w-full",children:(0,s.jsx)("div",{className:"w-12 h-12 rounded-full border-4 border-primary border-t-transparent animate-spin"})}),children:(0,s.jsx)(P,{})})}},26351:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(60687),a=r(43210),n=r(10218),i=r(52581),o=r(75558);function l({returnUrl:e}){let[t,r]=(0,a.useState)(!1),{resolvedTheme:l}=(0,n.D)(),d=(0,a.useCallback)(()=>{sessionStorage.removeItem("isGitHubAuthInProgress"),r(!1)},[]);(0,a.useCallback)(t=>{d(),setTimeout(()=>{window.location.href=t.returnUrl||e||"/dashboard"},100)},[d,e]),(0,a.useCallback)(e=>{d(),i.oR.error(e.message||"GitHub sign-in failed. Please try again.")},[d]);let c=async()=>{if(t)return;let s=null;try{r(!0),e&&sessionStorage.setItem("github-returnUrl",e||"/dashboard");let t=window.open(`${window.location.origin}/auth/github-popup`,"GitHubOAuth","width=500,height=600,scrollbars=yes,resizable=yes,status=yes,location=yes");if(!t)throw Error("Popup was blocked. Please enable popups and try again.");sessionStorage.setItem("isGitHubAuthInProgress","1"),s=setInterval(()=>{t.closed&&(s&&(clearInterval(s),s=null),setTimeout(()=>{sessionStorage.getItem("isGitHubAuthInProgress")&&(d(),i.oR.error("GitHub sign-in was cancelled or not completed."))},500))},1e3)}catch(e){console.error("GitHub sign-in error:",e),s&&clearInterval(s),d(),i.oR.error(e instanceof Error?e.message:"Failed to start GitHub sign-in")}};return(0,s.jsxs)("button",{onClick:c,disabled:t,className:"relative w-full h-12 flex items-center justify-center text-sm font-normal tracking-wide rounded-full bg-background text-foreground border border-border hover:bg-accent/30 transition-all duration-200 disabled:opacity-60 disabled:cursor-not-allowed font-sans","aria-label":t?"Signing in with GitHub...":"Sign in with GitHub",type:"button",children:[(0,s.jsx)("div",{className:"absolute left-0 inset-y-0 flex items-center pl-1 w-10",children:(0,s.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-foreground dark:bg-foreground dark:text-background",children:t?(0,s.jsx)("div",{className:"w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin"}):(0,s.jsx)(o.F.github,{className:"w-5 h-5"})})}),(0,s.jsx)("span",{className:"ml-9 font-light",children:t?"Signing in...":"Continue with GitHub"})]})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,r:()=>o});var s=r(60687);r(43210);var a=r(8730),n=r(24224),i=r(4780);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",node_outline:"bg-transparent border border-primary/10",node_secondary:"px-0 bg-transparent hover:opacity-60",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-lg gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-lg px-6 has-[>svg]:px-4",icon:"size-9",node_secondary:"px-0"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:n=!1,...l}){let d=n?a.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:r,className:e})),...l})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44774:(e,t,r)=>{"use strict";r.d(t,{SubmitButton:()=>c});var s=r(60687),a=r(51215),n=r(43210),i=r(29523),o=r(91821),l=r(43649);let d={message:""};function c({children:e,formAction:t,errorMessage:r,pendingText:c="Submitting...",...u}){let{pending:m,action:g}=(0,a.useFormStatus)(),[x,f]=(0,n.useActionState)(t,d),b=m&&g===f;return(0,s.jsxs)("div",{className:"flex flex-col gap-y-4 w-full",children:[!!(r||x?.message)&&(0,s.jsxs)(o.Fc,{variant:"destructive",className:"w-full",children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)(o.TN,{children:r||x?.message})]}),(0,s.jsx)("div",{children:(0,s.jsx)(i.$,{...u,type:"submit","aria-disabled":m,formAction:f,children:b?c:e})})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>u,Es:()=>g,L3:()=>x,LC:()=>c,c7:()=>m,lG:()=>o,rr:()=>f,zM:()=>l});var s=r(60687);r(43210);var a=r(26134),n=r(11860),i=r(4780);function o({...e}){return(0,s.jsx)(a.bL,{"data-slot":"dialog",...e})}function l({...e}){return(0,s.jsx)(a.l9,{"data-slot":"dialog-trigger",...e})}function d({...e}){return(0,s.jsx)(a.ZL,{"data-slot":"dialog-portal",...e})}function c({className:e,...t}){return(0,s.jsx)(a.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50 backdrop-blur-xs",e),...t})}function u({className:e,children:t,...r}){return(0,s.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,s.jsx)(c,{}),(0,s.jsxs)(a.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-2xl border p-6 shadow-lg duration-200",e),...r,children:[t,(0,s.jsxs)(a.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(n.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function g({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function x({className:e,...t}){return(0,s.jsx)(a.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...t})}function f({className:e,...t}){return(0,s.jsx)(a.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}},68305:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\auth\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\auth\\page.tsx","default")},73028:(e,t,r)=>{Promise.resolve().then(r.bind(r,68305))},74075:e=>{"use strict";e.exports=require("zlib")},74372:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>u,tree:()=>l});var s=r(65239),a=r(48088),n=r(31369),i=r(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let l={children:["",{children:["auth",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,68305)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\auth\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,88524))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,93595)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(r.bind(r,31369)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(r.bind(r,88524))).default(e)],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\suna\\frontend\\src\\app\\auth\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/page",pathname:"/auth",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83112:(e,t,r)=>{"use strict";r.r(t),r.d(t,{"001bd528d1e6d74e3d1c0a606898cfa923e7bbe9fd":()=>u,"604991a10ed242e0f6ea33f223b3055ecb96bfab5b":()=>o,"609c31419be1d42bf9c9b8ca0643e758692237b99d":()=>l,"60a6205911ff9573610eaa1755309d0261975c088b":()=>d,"60fcf04f7f92ed069c46b1be2805385ba3ee83149d":()=>c});var s=r(91199);r(42087);var a=r(5876),n=r(90141);async function i(e,t){try{let r=process.env.ADMIN_API_KEY;if(!r)return void console.error("ADMIN_API_KEY not configured");let s=await fetch("http://localhost:8000/api/api/send-welcome-email",{method:"POST",headers:{"Content-Type":"application/json","X-Admin-Api-Key":r},body:JSON.stringify({email:e,name:t})});if(s.ok)console.log(`Welcome email queued for ${e}`);else{let t=await s.json().catch(()=>({}));console.error(`Failed to queue welcome email for ${e}:`,t)}}catch(e){console.error("Error sending welcome email:",e)}}async function o(e,t){let r=t.get("email"),s=t.get("password"),n=t.get("returnUrl");if(!r||!r.includes("@"))return{message:"Please enter a valid email address"};if(!s||s.length<6)return{message:"Password must be at least 6 characters"};let i=await (0,a.U)(),{error:o}=await i.auth.signInWithPassword({email:r,password:s});return o?{message:o.message||"Could not authenticate user"}:{success:!0,redirectTo:n||"/dashboard"}}async function l(e,t){let r=t.get("origin"),s=t.get("email"),n=t.get("password"),o=t.get("confirmPassword"),l=t.get("returnUrl");if(!s||!s.includes("@"))return{message:"Please enter a valid email address"};if(!n||n.length<6)return{message:"Password must be at least 6 characters"};if(n!==o)return{message:"Passwords do not match"};let d=await (0,a.U)(),{error:c}=await d.auth.signUp({email:s,password:n,options:{emailRedirectTo:`${r}/auth/callback?returnUrl=${l}`}});if(c)return{message:c.message||"Could not create account"};let u=s.split("@")[0].replace(/[._-]/g," ").replace(/\b\w/g,e=>e.toUpperCase()),{error:m,data:g}=await d.auth.signInWithPassword({email:s,password:n});return(g&&i(s,u),m)?{message:"Account created! Check your email to confirm your registration."}:{success:!0,redirectTo:l||"/dashboard"}}async function d(e,t){let r=t.get("email"),s=t.get("origin");if(!r||!r.includes("@"))return{message:"Please enter a valid email address"};let n=await (0,a.U)(),{error:i}=await n.auth.resetPasswordForEmail(r,{redirectTo:`${s}/auth/reset-password`});return i?{message:i.message||"Could not send password reset email"}:{success:!0,message:"Check your email for a password reset link"}}async function c(e,t){let r=t.get("password"),s=t.get("confirmPassword");if(!r||r.length<6)return{message:"Password must be at least 6 characters"};if(r!==s)return{message:"Passwords do not match"};let n=await (0,a.U)(),{error:i}=await n.auth.updateUser({password:r});return i?{message:i.message||"Could not update password"}:{success:!0,message:"Password updated successfully"}}async function u(){let e=await (0,a.U)(),{error:t}=await e.auth.signOut();return t?{message:t.message||"Could not sign out"}:(0,n.redirect)("/")}(0,r(33331).D)([o,l,d,c,u]),(0,s.A)(o,"604991a10ed242e0f6ea33f223b3055ecb96bfab5b",null),(0,s.A)(l,"609c31419be1d42bf9c9b8ca0643e758692237b99d",null),(0,s.A)(d,"60a6205911ff9573610eaa1755309d0261975c088b",null),(0,s.A)(c,"60fcf04f7f92ed069c46b1be2805385ba3ee83149d",null),(0,s.A)(u,"001bd528d1e6d74e3d1c0a606898cfa923e7bbe9fd",null)},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(60687);r(43210);var a=r(4780);function n({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},91645:e=>{"use strict";e.exports=require("net")},91821:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>o,TN:()=>d,XL:()=>l});var s=r(60687);r(43210);var a=r(24224),n=r(4780);let i=(0,a.F)("relative w-full rounded-xl border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(i({variant:t}),e),...r})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"alert-title",className:(0,n.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}},92819:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(60687),a=r(43210),n=r(72600),i=r(79481),o=r(10218);function l({returnUrl:e}){let t=process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,[r,l]=(0,a.useState)(!1),{resolvedTheme:d}=(0,o.D)();return((0,a.useCallback)(async t=>{try{l(!0);let r=(0,i.U)();console.log("Starting Google sign in process");let{error:s}=await r.auth.signInWithIdToken({provider:"google",token:t.credential});if(s)throw s;console.log("Google sign in successful, preparing redirect to:",e||"/dashboard"),setTimeout(()=>{console.log("Executing redirect now to:",e||"/dashboard"),window.location.href=e||"/dashboard"},500)}catch(e){console.error("Error signing in with Google:",e),l(!1)}},[e]),t)?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{id:"g_id_onload","data-client_id":t,"data-context":"signin","data-ux_mode":"popup","data-auto_prompt":"false","data-itp_support":"true","data-callback":"handleGoogleSignIn"}),(0,s.jsx)("div",{id:"google-signin-button",className:"w-full h-12"}),(0,s.jsx)(n.default,{src:"https://accounts.google.com/gsi/client",strategy:"afterInteractive",onLoad:()=>{if(window.google&&t){let e=document.getElementById("google-signin-button");e&&(window.google.accounts.id.renderButton(e,{type:"standard",theme:"dark"===d?"filled_black":"outline",size:"large",text:"continue_with",shape:"pill",logoAlignment:"left",width:e.offsetWidth}),setTimeout(()=>{let t=e.querySelector('div[role="button"]');t instanceof HTMLElement&&(t.style.borderRadius="9999px",t.style.width="100%",t.style.height="56px",t.style.border="1px solid var(--border)",t.style.background="var(--background)",t.style.transition="all 0.2s")},100))}}})]}):(0,s.jsxs)("button",{disabled:!0,className:"w-full h-12 flex items-center justify-center gap-2 text-sm font-medium tracking-wide rounded-full bg-background border border-border opacity-60 cursor-not-allowed",children:[(0,s.jsxs)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z",fill:"#4285F4"}),(0,s.jsx)("path",{d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z",fill:"#34A853"}),(0,s.jsx)("path",{d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z",fill:"#FBBC05"}),(0,s.jsx)("path",{d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z",fill:"#EA4335"})]}),"Google Sign-In Not Configured"]})}},93613:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,5193,4267,7096,1265,3667,5558],()=>r(74372));module.exports=s})();