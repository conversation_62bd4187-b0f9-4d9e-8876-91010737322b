"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3386],{7601:(e,r,t)=>{t.d(r,{X:()=>l});var s=t(95155);function l(e){let{children:r}=e;return(0,s.jsx)("div",{className:"border-b w-full h-full p-10 md:p-14",children:(0,s.jsx)("div",{className:"max-w-xl mx-auto flex flex-col items-center justify-center gap-2",children:r})})}},53386:(e,r,t)=>{t.d(r,{default:()=>g});var s=t(95155),l=t(12115),i=t(30285),a=t(77633),n=t(23843),d=t(25731),o=t(64541),c=t(68856),u=t(27379),m=t(6874),x=t.n(m),p=t(33096);function g(e){var r,t;let{accountId:m,returnUrl:g}=e,{session:h,isLoading:b}=(0,o.A)(),[f,y]=(0,l.useState)(null),[v,j]=(0,l.useState)(!1),{data:w,isLoading:N,error:_}=(0,u.Rs)(),P=async()=>{try{j(!0);let{url:e}=await (0,d.VK)({return_url:g});window.location.href=e}catch(e){console.error("Failed to create portal session:",e),y(e instanceof Error?e.message:"Failed to create portal session")}finally{j(!1)}};if((0,n.Jn)())return(0,s.jsxs)("div",{className:"rounded-xl border shadow-sm bg-card p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Billing Status"}),(0,s.jsxs)("div",{className:"p-4 mb-4 bg-muted/30 border border-border rounded-lg text-center",children:[(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Running in local development mode - billing features are disabled"}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground mt-2",children:"Agent usage limits are not enforced in this environment"})]})]});if(N||b)return(0,s.jsxs)("div",{className:"rounded-xl border shadow-sm bg-card p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Billing Status"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(c.Skeleton,{className:"h-20 w-full"}),(0,s.jsx)(c.Skeleton,{className:"h-40 w-full"}),(0,s.jsx)(c.Skeleton,{className:"h-10 w-full"})]})]});if(f||_)return(0,s.jsxs)("div",{className:"rounded-xl border shadow-sm bg-card p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Billing Status"}),(0,s.jsx)("div",{className:"p-4 mb-4 bg-destructive/10 border border-destructive/20 rounded-lg text-center",children:(0,s.jsxs)("p",{className:"text-sm text-destructive",children:["Error loading billing status:"," ",f||_.message]})})]});let S=e=>(null==w?void 0:w.plan_name)===e;return S("free")||S("base")||S("extra"),(0,s.jsxs)("div",{className:"rounded-xl border shadow-sm bg-card p-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold mb-4",children:"Billing Status"}),w?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsx)("div",{className:"rounded-lg border bg-background p-4",children:(0,s.jsxs)("div",{className:"flex justify-between items-center gap-4",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-foreground/90",children:"Agent Usage This Month"}),(0,s.jsxs)("span",{className:"text-sm font-medium text-card-title",children:["$",(null==(r=w.current_usage)?void 0:r.toFixed(2))||"0"," /"," ","$",w.cost_limit||"0"]}),(0,s.jsx)(i.$,{variant:"outline",asChild:!0,className:"text-sm",children:(0,s.jsx)(x(),{href:"/settings/usage-logs",children:"Usage logs"})})]})})}),(0,s.jsx)(a.c,{returnUrl:g,showTitleAndTabs:!1,insideDialog:!0}),(0,s.jsx)("div",{className:"mt-20"}),(0,s.jsxs)("div",{className:"flex justify-center items-center gap-4",children:[(0,s.jsx)(i.$,{variant:"outline",className:"border-border hover:bg-muted/50 shadow-sm hover:shadow-md transition-all whitespace-nowrap flex items-center",children:(0,s.jsxs)(x(),{href:"/model-pricing",children:["View Model Pricing ",(0,s.jsx)(p.Ngv,{className:"w-4 h-4 inline ml-2"})]})}),(0,s.jsx)(i.$,{onClick:P,disabled:v,className:"bg-primary hover:bg-primary/90 shadow-md hover:shadow-lg transition-all",children:v?"Loading...":"Manage Subscription"})]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("div",{className:"rounded-lg border bg-background p-4 gap-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-foreground/90",children:"Current Plan"}),(0,s.jsx)("span",{className:"text-sm font-medium text-card-title",children:"Free"})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-foreground/90",children:"Agent Usage This Month"}),(0,s.jsxs)("span",{className:"text-sm font-medium text-card-title",children:["$",(null==w||null==(t=w.current_usage)?void 0:t.toFixed(2))||"0"," /"," ","$",(null==w?void 0:w.cost_limit)||"0"]})]})]})}),(0,s.jsx)(a.c,{returnUrl:g,showTitleAndTabs:!1,insideDialog:!0}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)(i.$,{onClick:()=>window.open("/model-pricing","_blank"),variant:"outline",className:"w-full border-border hover:bg-muted/50 shadow-sm hover:shadow-md transition-all",children:"View Model Pricing"}),(0,s.jsx)(i.$,{onClick:P,disabled:v,className:"w-full bg-primary text-white hover:bg-primary/90 shadow-md hover:shadow-lg transition-all",children:v?"Loading...":"Manage Subscription"})]})]})]})}},77633:(e,r,t)=>{t.d(r,{c:()=>y});var s=t(95155),l=t(7601),i=t(68222),a=t(59434),n=t(95653),d=t(12115),o=t(5196),c=t(30285),u=t(25731),m=t(56671),x=t(23843),p=t(27379);function g(e){let{activeTab:r,setActiveTab:t,className:l}=e;return(0,s.jsx)("div",{className:(0,a.cn)("relative flex w-fit items-center rounded-full border p-0.5 backdrop-blur-sm cursor-pointer h-9 flex-row bg-muted",l),children:["cloud","self-hosted"].map(e=>(0,s.jsxs)("button",{onClick:()=>t(e),className:(0,a.cn)("relative z-[1] px-3 h-8 flex items-center justify-center cursor-pointer",{"z-0":r===e}),children:[r===e&&(0,s.jsx)(n.P.div,{layoutId:"active-tab",className:"absolute inset-0 rounded-full bg-white dark:bg-[#3F3F46] shadow-md border border-border",transition:{duration:.2,type:"spring",stiffness:300,damping:25,velocity:2}}),(0,s.jsx)("span",{className:(0,a.cn)("relative block text-sm font-medium duration-200 shrink-0",r===e?"text-primary":"text-muted-foreground"),children:"cloud"===e?"Cloud":"Self-hosted"})]},e))})}function h(e){let{price:r,isCompact:t}=e;return(0,s.jsx)(n.P.span,{className:t?"text-xl font-semibold":"text-4xl font-semibold",initial:{opacity:0,x:10,filter:"blur(5px)"},animate:{opacity:1,x:0,filter:"blur(0px)"},transition:{duration:.25,ease:[.4,0,.2,1]},children:r},r)}function b(e){let{billingPeriod:r,setBillingPeriod:t}=e;return(0,s.jsx)("div",{className:"flex items-center justify-center gap-3",children:(0,s.jsx)("div",{className:"relative bg-muted rounded-full p-1 cursor-pointer",onClick:()=>t("monthly"===r?"yearly":"monthly"),children:(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("div",{className:(0,a.cn)("px-3 py-1 rounded-full text-xs font-medium transition-all duration-200","monthly"===r?"bg-background text-foreground shadow-sm":"text-muted-foreground"),children:"Monthly"}),(0,s.jsxs)("div",{className:(0,a.cn)("px-3 py-1 rounded-full text-xs font-medium transition-all duration-200 flex items-center gap-1","yearly"===r?"bg-background text-foreground shadow-sm":"text-muted-foreground"),children:["Yearly",(0,s.jsx)("span",{className:"bg-green-600 text-green-50 dark:bg-green-500 dark:text-green-50 text-[10px] px-1.5 py-0.5 rounded-full font-semibold whitespace-nowrap",children:"15% off"})]})]})})})}function f(e){var r,t;let{tier:l,isCompact:n=!1,currentSubscription:d,isLoading:x,isFetchingPlan:p,selectedPlan:g,onPlanSelect:b,onSubscriptionUpdate:f,isAuthenticated:y=!1,returnUrl:v,insideDialog:j=!1,billingPeriod:w="monthly"}=e,N=async e=>{var r,t,l;if(!y){window.location.href="/auth?mode=signup";return}if(!x[e])try{null==b||b(e);let t=await (0,u.fw)({price_id:e,success_url:v,cancel_url:v});switch(console.log("Subscription action response:",t),t.status){case"new":case"checkout_created":t.url?window.location.href=t.url:(console.error("Error: Received status 'checkout_created' but no checkout URL."),m.oR.error("Failed to initiate subscription. Please try again."));break;case"upgraded":case"updated":let l=(null==(r=t.details)?void 0:r.is_upgrade)?"Subscription upgraded from $".concat(t.details.current_price," to $").concat(t.details.new_price):"Subscription updated successfully";m.oR.success(l),f&&f();break;case"downgrade_scheduled":case"scheduled":let i=t.effective_date?new Date(t.effective_date).toLocaleDateString():"the end of your billing period";m.oR.success((0,s.jsxs)("div",{children:[(0,s.jsx)("p",{children:"Subscription change scheduled"}),(0,s.jsxs)("p",{className:"text-sm mt-1",children:["Your plan will change on ",i,"."]})]})),f&&f();break;case"no_change":m.oR.info(t.message||"You are already on this plan.");break;default:console.warn("Received unexpected status from createCheckoutSession:",t.status),m.oR.error("An unexpected error occurred. Please try again.")}}catch(r){console.error("Error processing subscription:",r);let e=(null==r||null==(l=r.response)||null==(t=l.data)?void 0:t.detail)||(null==r?void 0:r.message)||"Failed to process subscription. Please try again.";m.oR.error(e)}},_="yearly"===w&&l.yearlyStripePriceId?l.yearlyStripePriceId:l.stripePriceId,P="yearly"===w&&l.yearlyPrice?l.yearlyPrice:l.price,S=i.CQ.cloudPricingItems.find(e=>e.stripePriceId===(null==d?void 0:d.price_id)||e.yearlyStripePriceId===(null==d?void 0:d.price_id)),k=y&&(null==d?void 0:d.price_id)===_,F=y&&(null==d?void 0:d.has_schedule),C=F&&(null==d?void 0:d.scheduled_price_id)===_,I=x[_],$=y?"Select Plan":"Start Free",A=I,R=null,T="",M=null,E="";if(y){if(k)$="Current Plan",A=!0,R="secondary",T=n?"ring-1 ring-primary":"ring-2 ring-primary",E="bg-primary/5 hover:bg-primary/10 text-primary",M=(0,s.jsx)("span",{className:"bg-primary/10 text-primary text-[10px] font-medium px-1.5 py-0.5 rounded-full",children:"Current"});else if(C)$="Scheduled",A=!0,R="outline",T=n?"ring-1 ring-yellow-500":"ring-2 ring-yellow-500",E="bg-yellow-500/5 hover:bg-yellow-500/10 text-yellow-600 border-yellow-500/20",M=(0,s.jsx)("span",{className:"bg-yellow-500/10 text-yellow-600 text-[10px] font-medium px-1.5 py-0.5 rounded-full",children:"Scheduled"});else if(F&&(null==d?void 0:d.price_id)===_)$="Change Scheduled",R="secondary",T=n?"ring-1 ring-primary":"ring-2 ring-primary",E="bg-primary/5 hover:bg-primary/10 text-primary",M=(0,s.jsx)("span",{className:"bg-yellow-500/10 text-yellow-600 text-[10px] font-medium px-1.5 py-0.5 rounded-full",children:"Downgrade Pending"});else{let e=d&&(null==S?void 0:S.price)||"$0",r=l.price,t="$0"===e?0:100*parseFloat(e.replace(/[^\d.]/g,"")||"0"),s="$0"===r?0:100*parseFloat(r.replace(/[^\d.]/g,"")||"0"),i=S&&(null==d?void 0:d.price_id)===S.stripePriceId,a=S&&(null==d?void 0:d.price_id)===S.yearlyStripePriceId,n=l.stripePriceId===_,o=l.yearlyStripePriceId===_,c=S&&S.name===l.name&&(i&&o||a&&n);0===t&&0===s&&(null==d?void 0:d.status)!=="no_subscription"?($="Select Plan",A=!0,R="secondary",E="bg-primary/5 hover:bg-primary/10 text-primary"):s>t||i&&o&&s>=t?a&&n?($="-",A=!0,R="secondary",E="opacity-50 cursor-not-allowed bg-muted text-muted-foreground"):i&&o&&s===t?($="Switch to Yearly",R="default",E="bg-green-600 hover:bg-green-700 text-white"):($="Upgrade",R=l.buttonColor,E="bg-primary hover:bg-primary/90 text-primary-foreground"):s<t&&!(a&&n&&s===t)?($="-",A=!0,R="secondary",E="opacity-50 cursor-not-allowed bg-muted text-muted-foreground"):c?i&&o?($="Switch to Yearly",R="default",E="bg-green-600 hover:bg-green-700 text-white"):a&&n?($="-",A=!0,R="secondary",E="opacity-50 cursor-not-allowed bg-muted text-muted-foreground"):($="Select Plan",R=l.buttonColor,E="bg-primary hover:bg-primary/90 text-primary-foreground"):($="Select Plan",R=l.buttonColor,E="bg-primary hover:bg-primary/90 text-primary-foreground")}I&&($="Loading...",E="opacity-70 cursor-not-allowed")}else R=l.buttonColor,E="default"===l.buttonColor?"bg-primary hover:bg-primary/90 text-white":"bg-secondary hover:bg-secondary/90 text-white";return(0,s.jsxs)("div",{className:(0,a.cn)("rounded-xl flex flex-col relative",j?"min-h-[300px]":"h-full min-h-[300px]",l.isPopular&&!j?"md:shadow-[0px_61px_24px_-10px_rgba(0,0,0,0.01),0px_34px_20px_-8px_rgba(0,0,0,0.05),0px_15px_15px_-6px_rgba(0,0,0,0.09),0px_4px_8px_-2px_rgba(0,0,0,0.10),0px_0px_0px_1px_rgba(0,0,0,0.08)] bg-accent":"bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] border border-border",!j&&T),children:[(0,s.jsxs)("div",{className:(0,a.cn)("flex flex-col gap-3",j?"p-3":"p-4"),children:[(0,s.jsxs)("p",{className:"text-sm flex items-center gap-2",children:[l.name,l.isPopular&&(0,s.jsx)("span",{className:"bg-gradient-to-b from-secondary/50 from-[1.92%] to-secondary to-[100%] text-white inline-flex w-fit items-center justify-center px-1.5 py-0.5 rounded-full text-[10px] font-medium shadow-[0px_6px_6px_-3px_rgba(0,0,0,0.08),0px_3px_3px_-1.5px_rgba(0,0,0,0.08),0px_1px_1px_-0.5px_rgba(0,0,0,0.08),0px_0px_0px_1px_rgba(255,255,255,0.12)_inset,0px_1px_0px_0px_rgba(255,255,255,0.12)_inset]",children:"Popular"}),!l.isPopular&&y&&d&&"yearly"===w&&S&&d.price_id===S.stripePriceId&&l.yearlyStripePriceId&&(S.name===l.name||parseFloat(l.price.slice(1))>=parseFloat(S.price.slice(1)))&&(0,s.jsx)("span",{className:"bg-green-500/10 text-green-700 text-[10px] font-medium px-1.5 py-0.5 rounded-full",children:"Recommended"}),y&&M]}),(0,s.jsx)("div",{className:"flex items-baseline mt-2",children:"yearly"===w&&l.yearlyPrice&&"$0"!==P?(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsxs)("div",{className:"flex items-baseline gap-2",children:[(0,s.jsx)(h,{price:"$".concat(Math.round(parseFloat(l.yearlyPrice.slice(1))/12)),isCompact:j}),l.discountPercentage&&(0,s.jsxs)("span",{className:"text-xs line-through text-muted-foreground",children:["$",Math.round(parseFloat((null==(r=l.originalYearlyPrice)?void 0:r.slice(1))||"0")/12)]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"/month"}),(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"billed yearly"})]})]}):(0,s.jsxs)("div",{className:"flex items-baseline",children:[(0,s.jsx)(h,{price:P,isCompact:j}),(0,s.jsx)("span",{className:"ml-2",children:"$0"!==P?"/month":""})]})}),(0,s.jsx)("p",{className:"hidden text-sm mt-2",children:l.description}),"yearly"===w&&l.yearlyPrice&&l.discountPercentage?(0,s.jsxs)("div",{className:"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold bg-green-50 border-green-200 text-green-700 w-fit",children:["Save $",Math.round(parseFloat((null==(t=l.originalYearlyPrice)?void 0:t.slice(1))||"0")-parseFloat(l.yearlyPrice.slice(1)))," per year"]}):(0,s.jsx)("div",{className:"hidden items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold bg-primary/10 border-primary/20 text-primary w-fit",children:"yearly"===w&&l.yearlyPrice&&"$0"!==P?"$".concat(Math.round(parseFloat(l.yearlyPrice.slice(1))/12),"/month (billed yearly)"):"".concat(P,"/month")})]}),(0,s.jsx)("div",{className:(0,a.cn)("flex-grow",j?"px-3 pb-2":"px-4 pb-3"),children:l.features&&l.features.length>0&&(0,s.jsx)("ul",{className:"space-y-3",children:l.features.map(e=>(0,s.jsxs)("li",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"size-5 min-w-5 rounded-full border border-primary/20 flex items-center justify-center",children:(0,s.jsx)(o.A,{className:"size-3 text-primary"})}),(0,s.jsx)("span",{className:"text-sm",children:e})]},e))})}),(0,s.jsx)("div",{className:(0,a.cn)("mt-auto",j?"px-3 pt-1 pb-3":"px-4 pt-2 pb-4"),children:(0,s.jsx)(c.$,{onClick:()=>N(_),disabled:A,variant:R||"default",className:(0,a.cn)("w-full font-medium transition-all duration-200",n||j?"h-8 rounded-md text-xs":"h-10 rounded-full text-sm",E,I&&"animate-pulse"),children:$})})]})}function y(e){let{returnUrl:r=window.location.href,showTitleAndTabs:t=!0,hideFree:n=!1,insideDialog:o=!1}=e,[c,u]=(0,d.useState)("cloud"),{data:m,isLoading:h,error:y,refetch:v}=(0,p.Rs)(),j=!!m&&null===y,w=m||null,N=()=>{if(!j||!w)return"yearly";let e=i.CQ.cloudPricingItems.find(e=>e.stripePriceId===w.price_id||e.yearlyStripePriceId===w.price_id);if(e){if(e.yearlyStripePriceId===w.price_id);else if(e.stripePriceId===w.price_id)return"monthly"}return"yearly"},[_,P]=(0,d.useState)(N()),[S,k]=(0,d.useState)({});(0,d.useEffect)(()=>{P(N())},[j,null==w?void 0:w.price_id]);let F=e=>{k(r=>({...r,[e]:!0}))},C=()=>{v(),setTimeout(()=>{k({})},1e3)};return(0,x.Jn)()?(0,s.jsx)("div",{className:"p-4 bg-muted/30 border border-border rounded-lg text-center",children:(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Running in local development mode - billing features are disabled"})}):(0,s.jsxs)("section",{id:"pricing",className:(0,a.cn)("flex flex-col items-center justify-center gap-10 w-full relative pb-12"),children:[t&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(l.X,{children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-medium tracking-tighter text-center text-balance",children:"Choose the right plan for your needs"}),(0,s.jsx)("p",{className:"text-muted-foreground text-center text-balance font-medium",children:"Start with our free plan or upgrade for more AI token credits"})]}),(0,s.jsx)("div",{className:"relative w-full h-full",children:(0,s.jsx)("div",{className:"absolute -top-14 left-1/2 -translate-x-1/2",children:(0,s.jsx)(g,{activeTab:c,setActiveTab:e=>{if("self-hosted"===e){let e=document.getElementById("open-source");if(e){let r=e.getBoundingClientRect(),t=(window.pageYOffset||document.documentElement.scrollTop)+r.top-100;window.scrollTo({top:t,behavior:"smooth"})}}else u(e)},className:"mx-auto"})})})]}),"cloud"===c&&(0,s.jsx)(b,{billingPeriod:_,setBillingPeriod:P}),"cloud"===c&&(0,s.jsx)("div",{className:(0,a.cn)("grid gap-4 w-full mx-auto",{"px-6 max-w-7xl":!o,"max-w-7xl":o},o?"grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 2xl:grid-cols-4":"min-[650px]:grid-cols-2 lg:grid-cols-4",!o&&"grid-rows-1 items-stretch"),children:i.CQ.cloudPricingItems.filter(e=>!e.hidden&&(!n||"$0"!==e.price)).map(e=>(0,s.jsx)(f,{tier:e,currentSubscription:w,isLoading:S,isFetchingPlan:h,onPlanSelect:F,onSubscriptionUpdate:C,isAuthenticated:j,returnUrl:r,insideDialog:o,billingPeriod:_},e.name))}),(0,s.jsx)("div",{className:"mt-4 p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg max-w-2xl mx-auto",children:(0,s.jsxs)("p",{className:"text-sm text-blue-800 dark:text-blue-200 text-center",children:[(0,s.jsx)("strong",{children:"What are AI tokens?"})," Tokens are units of text that AI models process. Your plan includes credits to spend on various AI models - the more complex the task, the more tokens used."]})})]})}}}]);