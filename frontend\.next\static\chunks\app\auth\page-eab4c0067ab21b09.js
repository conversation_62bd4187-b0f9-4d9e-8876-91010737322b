(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8365],{7259:(e,t,r)=>{"use strict";r.d(t,{A:()=>d});var a=r(95155),s=r(12115),n=r(63554),i=r(52643),o=r(51362),l=r(49509);function d(e){let{returnUrl:t}=e,r=l.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID,[d,c]=(0,s.useState)(!1),{resolvedTheme:u}=(0,o.D)(),g=(0,s.useCallback)(async e=>{try{c(!0);let r=(0,i.U)();console.log("Starting Google sign in process");let{error:a}=await r.auth.signInWithIdToken({provider:"google",token:e.credential});if(a)throw a;console.log("Google sign in successful, preparing redirect to:",t||"/dashboard"),setTimeout(()=>{console.log("Executing redirect now to:",t||"/dashboard"),window.location.href=t||"/dashboard"},500)}catch(e){console.error("Error signing in with Google:",e),c(!1)}},[t]);return((0,s.useEffect)(()=>(window.handleGoogleSignIn=g,window.google&&r&&window.google.accounts.id.initialize({client_id:r,callback:g,use_fedcm:!0,context:"signin",itp_support:!0}),()=>{delete window.handleGoogleSignIn,window.google&&window.google.accounts.id.cancel()}),[r,g]),r)?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{id:"g_id_onload","data-client_id":r,"data-context":"signin","data-ux_mode":"popup","data-auto_prompt":"false","data-itp_support":"true","data-callback":"handleGoogleSignIn"}),(0,a.jsx)("div",{id:"google-signin-button",className:"w-full h-12"}),(0,a.jsx)(n.default,{src:"https://accounts.google.com/gsi/client",strategy:"afterInteractive",onLoad:()=>{if(window.google&&r){let e=document.getElementById("google-signin-button");e&&(window.google.accounts.id.renderButton(e,{type:"standard",theme:"dark"===u?"filled_black":"outline",size:"large",text:"continue_with",shape:"pill",logoAlignment:"left",width:e.offsetWidth}),setTimeout(()=>{let t=e.querySelector('div[role="button"]');t instanceof HTMLElement&&(t.style.borderRadius="9999px",t.style.width="100%",t.style.height="56px",t.style.border="1px solid var(--border)",t.style.background="var(--background)",t.style.transition="all 0.2s")},100))}}})]}):(0,a.jsxs)("button",{disabled:!0,className:"w-full h-12 flex items-center justify-center gap-2 text-sm font-medium tracking-wide rounded-full bg-background border border-border opacity-60 cursor-not-allowed",children:[(0,a.jsxs)("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",children:[(0,a.jsx)("path",{d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z",fill:"#4285F4"}),(0,a.jsx)("path",{d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z",fill:"#34A853"}),(0,a.jsx)("path",{d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z",fill:"#FBBC05"}),(0,a.jsx)("path",{d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z",fill:"#EA4335"})]}),"Google Sign-In Not Configured"]})}},8348:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>_});var a=r(95155),s=r(6874),n=r.n(s),i=r(35706),o=r(62523),l=r(7259),d=r(4654),c=r(38095),u=r(12115),g=r(95360),m=r(34477);let x=(0,m.createServerReference)("604991a10ed242e0f6ea33f223b3055ecb96bfab5b",m.callServer,void 0,m.findSourceMapURL,"signIn"),f=(0,m.createServerReference)("609c31419be1d42bf9c9b8ca0643e758692237b99d",m.callServer,void 0,m.findSourceMapURL,"signUp"),b=(0,m.createServerReference)("60a6205911ff9573610eaa1755309d0261975c088b",m.callServer,void 0,m.findSourceMapURL,"forgotPassword");var h=r(35695),p=r(51154),v=r(12874),w=r(35169),j=r(85339),y=r(54416),N=r(40646),k=r(64541),S=r(54165),z=r(60643);function C(){let e=(0,h.useRouter)(),t=(0,h.useSearchParams)(),{user:r,isLoading:s}=(0,k.A)(),m=t.get("mode"),C=t.get("returnUrl"),_=t.get("message"),E="signup"===m,A=(0,c.U)("(max-width: 1024px)"),[F,G]=(0,u.useState)(!1),[I,L]=(0,u.useState)(!1),U=(0,u.useRef)(null),{scrollY:T}=(0,g.L)();(0,u.useEffect)(()=>{!s&&r&&e.push(C||"/dashboard")},[r,s,e,C]);let P=_&&(_.includes("Check your email")||_.includes("Account created")||_.includes("success")),[R,B]=(0,u.useState)(!!P),[H,M]=(0,u.useState)(""),[O,q]=(0,u.useState)(!1),[D,W]=(0,u.useState)(""),[V,X]=(0,u.useState)({});(0,u.useEffect)(()=>{G(!0)},[]),(0,u.useEffect)(()=>{P&&B(!0)},[P]),(0,u.useEffect)(()=>{let e=T.on("change",()=>{L(!0),U.current&&clearTimeout(U.current),U.current=setTimeout(()=>{L(!1)},300)});return()=>{e(),U.current&&clearTimeout(U.current)}},[T]);let J=async(e,t)=>{C?t.append("returnUrl",C):t.append("returnUrl","/dashboard");let r=await x(e,t);return r&&"object"==typeof r&&"success"in r&&r.success&&"redirectTo"in r?(window.location.href=r.redirectTo,null):r},$=async(e,t)=>{M(t.get("email")),C&&t.append("returnUrl",C),t.append("origin",window.location.origin);let r=await f(e,t);if(r&&"object"==typeof r&&"success"in r&&r.success&&"redirectTo"in r)return window.location.href=r.redirectTo,null;if(r&&"object"==typeof r&&"message"in r){let e=r.message;if(e.includes("Check your email")){B(!0);let t=new URLSearchParams(window.location.search);t.set("message",e);let r=window.location.pathname+(t.toString()?"?"+t.toString():"");window.history.pushState({path:r},"",r)}}return r},Y=async e=>{if(e.preventDefault(),X({}),!D||!D.includes("@"))return void X({success:!1,message:"Please enter a valid email address"});let t=new FormData;t.append("email",D),t.append("origin",window.location.origin),X(await b(null,t))};return s?(0,a.jsx)("main",{className:"flex flex-col items-center justify-center min-h-screen w-full",children:(0,a.jsx)(p.A,{className:"h-12 w-12 animate-spin text-primary"})}):R?(0,a.jsx)("main",{className:"flex flex-col items-center justify-center min-h-screen w-full",children:(0,a.jsx)("div",{className:"w-full divide-y divide-border",children:(0,a.jsx)("section",{className:"w-full relative overflow-hidden",children:(0,a.jsxs)("div",{className:"relative flex flex-col items-center w-full px-6",children:[(0,a.jsxs)("div",{className:"absolute left-0 top-0 h-[600px] md:h-[800px] w-1/3 -z-10 overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-background z-10"}),(0,a.jsx)("div",{className:"absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10"}),(0,a.jsx)("div",{className:"absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10"})]}),(0,a.jsxs)("div",{className:"absolute right-0 top-0 h-[600px] md:h-[800px] w-1/3 -z-10 overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-background z-10"}),(0,a.jsx)("div",{className:"absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10"}),(0,a.jsx)("div",{className:"absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10"})]}),(0,a.jsx)("div",{className:"absolute inset-x-1/4 top-0 h-[600px] md:h-[800px] -z-20 bg-background rounded-b-xl"}),(0,a.jsx)("div",{className:"relative z-10 pt-24 pb-8 max-w-xl mx-auto h-full w-full flex flex-col gap-2 items-center justify-center",children:(0,a.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,a.jsx)("div",{className:"bg-green-50 dark:bg-green-950/20 rounded-full p-4 mb-6",children:(0,a.jsx)(v.A,{className:"h-12 w-12 text-green-500 dark:text-green-400"})}),(0,a.jsx)("h1",{className:"text-3xl md:text-4xl lg:text-5xl font-medium tracking-tighter text-center text-balance text-primary mb-4",children:"Check your email"}),(0,a.jsx)("p",{className:"text-base md:text-lg text-center text-muted-foreground font-medium text-balance leading-relaxed tracking-tight max-w-md mb-2",children:"We've sent a confirmation link to:"}),(0,a.jsx)("p",{className:"text-lg font-medium mb-6",children:H||"your email address"}),(0,a.jsx)("div",{className:"bg-green-50 dark:bg-green-950/20 border border-green-100 dark:border-green-900/50 rounded-lg p-6 mb-8 max-w-md w-full",children:(0,a.jsx)("p",{className:"text-sm text-green-800 dark:text-green-400 leading-relaxed",children:"Click the link in the email to activate your account. If you don't see the email, check your spam folder."})}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 w-full max-w-sm",children:[(0,a.jsx)(n(),{href:"/",className:"flex h-12 items-center justify-center w-full text-center rounded-full border border-border bg-background hover:bg-accent/20 transition-all",children:"Return to home"}),(0,a.jsx)("button",{onClick:()=>{B(!1);let t=new URLSearchParams(window.location.search);t.delete("message"),t.set("mode","signin");let r=window.location.pathname+(t.toString()?"?"+t.toString():"");window.history.pushState({path:r},"",r),e.refresh()},className:"flex h-12 items-center justify-center w-full text-center rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all shadow-md",children:"Back to sign in"})]})]})})]})})})}):(0,a.jsxs)("main",{className:"flex flex-col items-center justify-center min-h-screen w-full",children:[(0,a.jsx)("div",{className:"w-full divide-y divide-border",children:(0,a.jsxs)("section",{className:"w-full relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"relative flex flex-col items-center w-full px-6",children:[(0,a.jsxs)("div",{className:"absolute left-0 top-0 h-[600px] md:h-[800px] w-1/3 -z-10 overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-background z-10"}),(0,a.jsx)("div",{className:"absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10"}),(0,a.jsx)("div",{className:"absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10"}),(0,a.jsx)("div",{className:"h-full w-full",children:(0,a.jsx)(d.b,{className:"h-full w-full",squareSize:F&&A?2:2.5,gridGap:F&&A?2:2.5,color:"var(--secondary)",maxOpacity:.4,flickerChance:I?.01:.03})})]}),(0,a.jsxs)("div",{className:"absolute right-0 top-0 h-[600px] md:h-[800px] w-1/3 -z-10 overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-background z-10"}),(0,a.jsx)("div",{className:"absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10"}),(0,a.jsx)("div",{className:"absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10"}),(0,a.jsx)("div",{className:"h-full w-full",children:(0,a.jsx)(d.b,{className:"h-full w-full",squareSize:F&&A?2:2.5,gridGap:F&&A?2:2.5,color:"var(--secondary)",maxOpacity:.4,flickerChance:I?.01:.03})})]}),(0,a.jsx)("div",{className:"absolute inset-x-1/4 top-0 h-[600px] md:h-[800px] -z-20 bg-background rounded-b-xl"}),(0,a.jsxs)("div",{className:"relative z-10 pt-24 pb-8 max-w-md mx-auto h-full w-full flex flex-col gap-2 items-center justify-center",children:[(0,a.jsxs)(n(),{href:"/",className:"group border border-border/50 bg-background hover:bg-accent/20 rounded-full text-sm h-8 px-3 flex items-center gap-2 transition-all duration-200 shadow-sm mb-6",children:[(0,a.jsx)(w.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"font-medium text-muted-foreground text-xs tracking-wide",children:"Back to home"})]}),(0,a.jsx)("h1",{className:"text-3xl md:text-4xl lg:text-5xl font-medium tracking-tighter text-center text-balance text-primary",children:E?"Join Suna":"Welcome back"}),(0,a.jsx)("p",{className:"text-base md:text-lg text-center text-muted-foreground font-medium text-balance leading-relaxed tracking-tight mt-2 mb-6",children:E?"Create your account and start building with AI":"Sign in to your account to continue"})]})]}),(0,a.jsx)("div",{className:"relative z-10 flex justify-center px-6 pb-24",children:(0,a.jsxs)("div",{className:"w-full max-w-md rounded-xl bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] border border-border p-8",children:[_&&!P&&(0,a.jsxs)("div",{className:"mb-6 p-4 rounded-lg flex items-center gap-3 bg-secondary/10 border border-secondary/20 text-secondary",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 flex-shrink-0 text-secondary"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:_})]}),(0,a.jsxs)("div",{className:"w-full flex flex-col gap-3 mb-6",children:[(0,a.jsx)("div",{className:"w-full",children:(0,a.jsx)(l.A,{returnUrl:C||void 0})}),(0,a.jsx)("div",{className:"w-full",children:(0,a.jsx)(z.A,{returnUrl:C||void 0})})]}),(0,a.jsxs)("div",{className:"relative my-8",children:[(0,a.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,a.jsx)("div",{className:"w-full border-t border-border"})}),(0,a.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,a.jsx)("span",{className:"px-2 bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] text-muted-foreground",children:"or continue with email"})})]}),(0,a.jsxs)("form",{className:"space-y-4",children:[(0,a.jsx)("div",{children:(0,a.jsx)(o.p,{id:"email",name:"email",type:"email",placeholder:"Email address",className:"h-12 rounded-full bg-background border-border",required:!0})}),(0,a.jsx)("div",{children:(0,a.jsx)(o.p,{id:"password",name:"password",type:"password",placeholder:"Password",className:"h-12 rounded-full bg-background border-border",required:!0})}),E&&(0,a.jsx)("div",{children:(0,a.jsx)(o.p,{id:"confirmPassword",name:"confirmPassword",type:"password",placeholder:"Confirm password",className:"h-12 rounded-full bg-background border-border",required:!0})}),(0,a.jsx)("div",{className:"space-y-4 pt-4",children:E?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.SubmitButton,{formAction:$,className:"w-full h-12 rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all shadow-md",pendingText:"Creating account...",children:"Sign up"}),(0,a.jsx)(n(),{href:"/auth".concat(C?"?returnUrl=".concat(C):""),className:"flex h-12 items-center justify-center w-full text-center rounded-full border border-border bg-background hover:bg-accent/20 transition-all",children:"Back to sign in"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.SubmitButton,{formAction:J,className:"w-full h-12 rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all shadow-md",pendingText:"Signing in...",children:"Sign in"}),(0,a.jsx)(n(),{href:"/auth?mode=signup".concat(C?"&returnUrl=".concat(C):""),className:"flex h-12 items-center justify-center w-full text-center rounded-full border border-border bg-background hover:bg-accent/20 transition-all",children:"Create new account"})]})}),!E&&(0,a.jsx)("div",{className:"text-center pt-2",children:(0,a.jsx)("button",{type:"button",onClick:()=>q(!0),className:"text-sm text-primary hover:underline",children:"Forgot password?"})})]}),(0,a.jsxs)("div",{className:"mt-8 text-center text-xs text-muted-foreground",children:["By continuing, you agree to our"," ",(0,a.jsx)(n(),{href:"/terms",className:"text-primary hover:underline",children:"Terms of Service"})," ","and"," ",(0,a.jsx)(n(),{href:"/privacy",className:"text-primary hover:underline",children:"Privacy Policy"})]})]})})]})}),(0,a.jsx)(S.lG,{open:O,onOpenChange:q,children:(0,a.jsxs)(S.Cf,{className:"sm:max-w-md rounded-xl bg-[#F3F4F6] dark:bg-[#17171A] border border-border [&>button]:hidden",children:[(0,a.jsxs)(S.c7,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(S.L3,{className:"text-xl font-medium",children:"Reset Password"}),(0,a.jsx)("button",{onClick:()=>q(!1),className:"rounded-full p-1 hover:bg-muted transition-colors",children:(0,a.jsx)(y.A,{className:"h-4 w-4 text-muted-foreground"})})]}),(0,a.jsx)(S.rr,{className:"text-muted-foreground",children:"Enter your email address and we'll send you a link to reset your password."})]}),(0,a.jsxs)("form",{onSubmit:Y,className:"space-y-4 py-4",children:[(0,a.jsx)(o.p,{id:"forgot-password-email",type:"email",placeholder:"Email address",value:D,onChange:e=>W(e.target.value),className:"h-12 rounded-full bg-background border-border",required:!0}),V.message&&(0,a.jsxs)("div",{className:"p-4 rounded-lg flex items-center gap-3 ".concat(V.success?"bg-green-50 dark:bg-green-950/30 border border-green-200 dark:border-green-900/50 text-green-800 dark:text-green-400":"bg-secondary/10 border border-secondary/20 text-secondary"),children:[V.success?(0,a.jsx)(N.A,{className:"h-5 w-5 flex-shrink-0 text-green-500 dark:text-green-400"}):(0,a.jsx)(j.A,{className:"h-5 w-5 flex-shrink-0 text-secondary"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:V.message})]}),(0,a.jsxs)(S.Es,{className:"flex sm:justify-start gap-3 pt-2",children:[(0,a.jsx)("button",{type:"submit",className:"h-12 px-6 rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all shadow-md",children:"Send Reset Link"}),(0,a.jsx)("button",{type:"button",onClick:()=>q(!1),className:"h-12 px-6 rounded-full border border-border bg-background hover:bg-accent/20 transition-all",children:"Cancel"})]})]})]})})]})}function _(){return(0,a.jsx)(u.Suspense,{fallback:(0,a.jsx)("main",{className:"flex flex-col items-center justify-center min-h-screen w-full",children:(0,a.jsx)("div",{className:"w-12 h-12 rounded-full border-4 border-primary border-t-transparent animate-spin"})}),children:(0,a.jsx)(C,{})})}},30285:(e,t,r)=>{"use strict";r.d(t,{$:()=>l,r:()=>o});var a=r(95155);r(12115);var s=r(99708),n=r(74466),i=r(59434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",node_outline:"bg-transparent border border-primary/10",node_secondary:"px-0 bg-transparent hover:opacity-60",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-lg gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-lg px-6 has-[>svg]:px-4",icon:"size-9",node_secondary:"px-0"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:r,size:n,asChild:l=!1,...d}=e,c=l?s.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:r,size:n,className:t})),...d})}},35706:(e,t,r)=>{"use strict";r.d(t,{SubmitButton:()=>c});var a=r(95155),s=r(47650),n=r(12115),i=r(30285),o=r(55365),l=r(1243);let d={message:""};function c(e){let{children:t,formAction:r,errorMessage:c,pendingText:u="Submitting...",...g}=e,{pending:m,action:x}=(0,s.useFormStatus)(),[f,b]=(0,n.useActionState)(r,d),h=m&&x===b;return(0,a.jsxs)("div",{className:"flex flex-col gap-y-4 w-full",children:[!!(c||(null==f?void 0:f.message))&&(0,a.jsxs)(o.Fc,{variant:"destructive",className:"w-full",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),(0,a.jsx)(o.TN,{children:c||(null==f?void 0:f.message)})]}),(0,a.jsx)("div",{children:(0,a.jsx)(i.$,{...g,type:"submit","aria-disabled":m,formAction:b,children:h?u:t})})]})}},38095:(e,t,r)=>{"use strict";r.d(t,{U:()=>s});var a=r(12115);function s(e){let[t,r]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{let a=window.matchMedia(e);a.matches!==t&&r(a.matches);let s=()=>r(a.matches);return a.addEventListener("change",s),()=>a.removeEventListener("change",s)},[t,e]),t}},52643:(e,t,r)=>{"use strict";r.d(t,{U:()=>s});var a=r(81935);let s=()=>{let e="";return e&&!e.startsWith("http")&&(e="http://".concat(e)),(0,a.createBrowserClient)(e,"")}},53868:(e,t,r)=>{Promise.resolve().then(r.bind(r,8348))},54165:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>u,Es:()=>m,L3:()=>x,LC:()=>c,c7:()=>g,lG:()=>o,rr:()=>f,zM:()=>l});var a=r(95155);r(12115);var s=r(15452),n=r(54416),i=r(59434);function o(e){let{...t}=e;return(0,a.jsx)(s.bL,{"data-slot":"dialog",...t})}function l(e){let{...t}=e;return(0,a.jsx)(s.l9,{"data-slot":"dialog-trigger",...t})}function d(e){let{...t}=e;return(0,a.jsx)(s.ZL,{"data-slot":"dialog-portal",...t})}function c(e){let{className:t,...r}=e;return(0,a.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50 backdrop-blur-xs",t),...r})}function u(e){let{className:t,children:r,...o}=e;return(0,a.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,a.jsx)(c,{}),(0,a.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-2xl border p-6 shadow-lg duration-200",t),...o,children:[r,(0,a.jsxs)(s.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,a.jsx)(n.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function g(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",t),...r})}function m(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...r})}function x(e){let{className:t,...r}=e;return(0,a.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",t),...r})}function f(e){let{className:t,...r}=e;return(0,a.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...r})}},55365:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>o,TN:()=>d,XL:()=>l});var a=r(95155);r(12115);var s=r(74466),n=r(59434);let i=(0,s.F)("relative w-full rounded-xl border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(i({variant:r}),t),...s})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-title",className:(0,n.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r})}},60643:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var a=r(95155),s=r(12115),n=r(51362),i=r(56671),o=r(87962);function l(e){let{returnUrl:t}=e,[r,l]=(0,s.useState)(!1),{resolvedTheme:d}=(0,n.D)(),c=(0,s.useCallback)(()=>{sessionStorage.removeItem("isGitHubAuthInProgress"),l(!1)},[]),u=(0,s.useCallback)(e=>{c(),setTimeout(()=>{window.location.href=e.returnUrl||t||"/dashboard"},100)},[c,t]),g=(0,s.useCallback)(e=>{c(),i.oR.error(e.message||"GitHub sign-in failed. Please try again.")},[c]);(0,s.useEffect)(()=>{let e=e=>{var t;if(e.origin!==window.location.origin)return void console.warn("Rejected message from unauthorized origin:",e.origin);if((null==(t=e.data)?void 0:t.type)&&"string"==typeof e.data.type)switch(e.data.type){case"github-auth-success":u(e.data);break;case"github-auth-error":g(e.data)}};return window.addEventListener("message",e),()=>{window.removeEventListener("message",e)}},[u,g]),(0,s.useEffect)(()=>()=>{c()},[c]);let m=async()=>{if(r)return;let e=null;try{l(!0),t&&sessionStorage.setItem("github-returnUrl",t||"/dashboard");let r=window.open("".concat(window.location.origin,"/auth/github-popup"),"GitHubOAuth","width=500,height=600,scrollbars=yes,resizable=yes,status=yes,location=yes");if(!r)throw Error("Popup was blocked. Please enable popups and try again.");sessionStorage.setItem("isGitHubAuthInProgress","1"),e=setInterval(()=>{r.closed&&(e&&(clearInterval(e),e=null),setTimeout(()=>{sessionStorage.getItem("isGitHubAuthInProgress")&&(c(),i.oR.error("GitHub sign-in was cancelled or not completed."))},500))},1e3)}catch(t){console.error("GitHub sign-in error:",t),e&&clearInterval(e),c(),i.oR.error(t instanceof Error?t.message:"Failed to start GitHub sign-in")}};return(0,a.jsxs)("button",{onClick:m,disabled:r,className:"relative w-full h-12 flex items-center justify-center text-sm font-normal tracking-wide rounded-full bg-background text-foreground border border-border hover:bg-accent/30 transition-all duration-200 disabled:opacity-60 disabled:cursor-not-allowed font-sans","aria-label":r?"Signing in with GitHub...":"Sign in with GitHub",type:"button",children:[(0,a.jsx)("div",{className:"absolute left-0 inset-y-0 flex items-center pl-1 w-10",children:(0,a.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-foreground dark:bg-foreground dark:text-background",children:r?(0,a.jsx)("div",{className:"w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin"}):(0,a.jsx)(o.F.github,{className:"w-5 h-5"})})}),(0,a.jsx)("span",{className:"ml-9 font-light",children:r?"Signing in...":"Continue with GitHub"})]})}},62523:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var a=r(95155);r(12115);var s=r(59434);function n(e){let{className:t,type:r,...n}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2969,1935,6671,6874,8341,7201,9001,7453,5360,1552,937,8441,1684,7358],()=>t(53868)),_N_E=e.O()}]);