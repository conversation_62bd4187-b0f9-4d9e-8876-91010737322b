"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8926],{18926:(a,e,n)=>{n.r(e),n.d(e,{default:()=>t});let t=[Object.freeze(JSON.parse('{"displayName":"MATLAB","fileTypes":["m"],"name":"matlab","patterns":[{"include":"#all_before_command_dual"},{"include":"#command_dual"},{"include":"#all_after_command_dual"}],"repository":{"all_after_command_dual":{"patterns":[{"include":"#string"},{"include":"#line_continuation"},{"include":"#comments"},{"include":"#conjugate_transpose"},{"include":"#transpose"},{"include":"#constants"},{"include":"#variables"},{"include":"#numbers"},{"include":"#operators"}]},"all_before_command_dual":{"patterns":[{"include":"#classdef"},{"include":"#function"},{"include":"#blocks"},{"include":"#control_statements"},{"include":"#global_persistent"},{"include":"#parens"},{"include":"#square_brackets"},{"include":"#indexing_curly_brackets"},{"include":"#curly_brackets"}]},"blocks":{"patterns":[{"begin":"\\\\s*(?:^|[,;\\\\s])(for)\\\\b","beginCaptures":{"1":{"name":"keyword.control.for.matlab"}},"end":"\\\\s*(?:^|[,;\\\\s])(end)\\\\b","endCaptures":{"1":{"name":"keyword.control.end.for.matlab"}},"name":"meta.for.matlab","patterns":[{"include":"$self"}]},{"begin":"\\\\s*(?:^|[,;\\\\s])(if)\\\\b","beginCaptures":{"1":{"name":"keyword.control.if.matlab"}},"end":"\\\\s*(?:^|[,;\\\\s])(end)\\\\b","endCaptures":{"1":{"name":"keyword.control.end.if.matlab"},"2":{"patterns":[{"include":"$self"}]}},"name":"meta.if.matlab","patterns":[{"captures":{"2":{"name":"keyword.control.elseif.matlab"},"3":{"patterns":[{"include":"$self"}]}},"end":"^","match":"(\\\\s*)(?:^|[,;\\\\s])(elseif)\\\\b(.*)$\\\\n?","name":"meta.elseif.matlab"},{"captures":{"2":{"name":"keyword.control.else.matlab"},"3":{"patterns":[{"include":"$self"}]}},"end":"^","match":"(\\\\s*)(?:^|[,;\\\\s])(else)\\\\b(.*)?$\\\\n?","name":"meta.else.matlab"},{"include":"$self"}]},{"begin":"\\\\s*(?:^|[,;\\\\s])(parfor)\\\\b","beginCaptures":{"1":{"name":"keyword.control.for.matlab"}},"end":"\\\\s*(?:^|[,;\\\\s])(end)\\\\b","endCaptures":{"1":{"name":"keyword.control.end.for.matlab"}},"name":"meta.parfor.matlab","patterns":[{"begin":"\\\\G(?!$)","end":"$\\\\n?","name":"meta.parfor-quantity.matlab","patterns":[{"include":"$self"}]},{"include":"$self"}]},{"begin":"\\\\s*(?:^|[,;\\\\s])(spmd)\\\\b","beginCaptures":{"1":{"name":"keyword.control.spmd.matlab"}},"end":"\\\\s*(?:^|[,;\\\\s])(end)\\\\b","endCaptures":{"1":{"name":"keyword.control.end.spmd.matlab"}},"name":"meta.spmd.matlab","patterns":[{"begin":"\\\\G(?!$)","end":"$\\\\n?","name":"meta.spmd-statement.matlab","patterns":[{"include":"$self"}]},{"include":"$self"}]},{"begin":"\\\\s*(?:^|[,;\\\\s])(switch)\\\\b","beginCaptures":{"1":{"name":"keyword.control.switch.matlab"}},"end":"\\\\s*(?:^|[,;\\\\s])(end)\\\\b","endCaptures":{"1":{"name":"keyword.control.end.switch.matlab"}},"name":"meta.switch.matlab","patterns":[{"captures":{"2":{"name":"keyword.control.case.matlab"},"3":{"patterns":[{"include":"$self"}]}},"end":"^","match":"(\\\\s*)(?:^|[,;\\\\s])(case)\\\\b(.*)$\\\\n?","name":"meta.case.matlab"},{"captures":{"2":{"name":"keyword.control.otherwise.matlab"},"3":{"patterns":[{"include":"$self"}]}},"end":"^","match":"(\\\\s*)(?:^|[,;\\\\s])(otherwise)\\\\b(.*)?$\\\\n?","name":"meta.otherwise.matlab"},{"include":"$self"}]},{"begin":"\\\\s*(?:^|[,;\\\\s])(try)\\\\b","beginCaptures":{"1":{"name":"keyword.control.try.matlab"}},"end":"\\\\s*(?:^|[,;\\\\s])(end)\\\\b","endCaptures":{"1":{"name":"keyword.control.end.try.matlab"}},"name":"meta.try.matlab","patterns":[{"captures":{"2":{"name":"keyword.control.catch.matlab"},"3":{"patterns":[{"include":"$self"}]}},"end":"^","match":"(\\\\s*)(?:^|[,;\\\\s])(catch)\\\\b(.*)?$\\\\n?","name":"meta.catch.matlab"},{"include":"$self"}]},{"begin":"\\\\s*(?:^|[,;\\\\s])(while)\\\\b","beginCaptures":{"1":{"name":"keyword.control.while.matlab"}},"end":"\\\\s*(?:^|[,;\\\\s])(end)\\\\b","endCaptures":{"1":{"name":"keyword.control.end.while.matlab"}},"name":"meta.while.matlab","patterns":[{"include":"$self"}]}]},"braced_validator_list":{"begin":"\\\\s*(\\\\{)\\\\s*","beginCaptures":{"1":{"name":"storage.type.matlab"}},"end":"(})","endCaptures":{"1":{"name":"storage.type.matlab"}},"patterns":[{"include":"#braced_validator_list"},{"include":"#validator_strings"},{"include":"#line_continuation"},{"captures":{"1":{"name":"storage.type.matlab"}},"match":"([^\\"\'.{}]+)"},{"match":"\\\\.","name":"storage.type.matlab"}]},"classdef":{"patterns":[{"begin":"^(\\\\s*)(classdef)\\\\b\\\\s*(.*)","beginCaptures":{"2":{"name":"storage.type.class.matlab"},"3":{"patterns":[{"captures":{"1":{"patterns":[{"match":"[A-Za-z][0-9A-Z_a-z]*","name":"variable.parameter.class.matlab"},{"begin":"=\\\\s*","end":",|(?=\\\\))","patterns":[{"match":"true|false","name":"constant.language.boolean.matlab"},{"include":"#string"}]}]},"2":{"name":"meta.class-declaration.matlab"},"3":{"name":"entity.name.section.class.matlab"},"4":{"name":"keyword.operator.other.matlab"},"5":{"patterns":[{"match":"[A-Za-z][0-9A-Z_a-z]*(\\\\.[A-Za-z][0-9A-Z_a-z]*)*","name":"entity.other.inherited-class.matlab"},{"match":"&","name":"keyword.operator.other.matlab"}]},"6":{"patterns":[{"include":"$self"}]}},"match":"(\\\\([^)]*\\\\))?\\\\s*(([A-Za-z][0-9A-Z_a-z]*)(?:\\\\s*(<)\\\\s*([^%]*))?)\\\\s*($|(?=(%|...)).*)"}]}},"end":"\\\\s*(?:^|[,;\\\\s])(end)\\\\b","endCaptures":{"1":{"name":"keyword.control.end.class.matlab"}},"name":"meta.class.matlab","patterns":[{"begin":"^(\\\\s*)(properties)\\\\b([^%]*)\\\\s*(\\\\([^)]*\\\\))?\\\\s*($|(?=%))","beginCaptures":{"2":{"name":"keyword.control.properties.matlab"},"3":{"patterns":[{"match":"[A-Za-z][0-9A-Z_a-z]*","name":"variable.parameter.properties.matlab"},{"begin":"=\\\\s*","end":",|(?=\\\\))","patterns":[{"match":"true|false","name":"constant.language.boolean.matlab"},{"match":"p(?:ublic|rotected|rivate)","name":"constant.language.access.matlab"}]}]}},"end":"\\\\s*(?:^|[,;\\\\s])(end)\\\\b","endCaptures":{"1":{"name":"keyword.control.end.properties.matlab"}},"name":"meta.properties.matlab","patterns":[{"include":"#validators"},{"include":"$self"}]},{"begin":"^(\\\\s*)(methods)\\\\b([^%]*)\\\\s*(\\\\([^)]*\\\\))?\\\\s*($|(?=%))","beginCaptures":{"2":{"name":"keyword.control.methods.matlab"},"3":{"patterns":[{"match":"[A-Za-z][0-9A-Z_a-z]*","name":"variable.parameter.methods.matlab"},{"begin":"=\\\\s*","end":",|(?=\\\\))","patterns":[{"match":"true|false","name":"constant.language.boolean.matlab"},{"match":"p(?:ublic|rotected|rivate)","name":"constant.language.access.matlab"}]}]}},"end":"\\\\s*(?:^|[,;\\\\s])(end)\\\\b","endCaptures":{"1":{"name":"keyword.control.end.methods.matlab"}},"name":"meta.methods.matlab","patterns":[{"include":"$self"}]},{"begin":"^(\\\\s*)(events)\\\\b([^%]*)\\\\s*(\\\\([^)]*\\\\))?\\\\s*($|(?=%))","beginCaptures":{"2":{"name":"keyword.control.events.matlab"},"3":{"patterns":[{"match":"[A-Za-z][0-9A-Z_a-z]*","name":"variable.parameter.events.matlab"},{"begin":"=\\\\s*","end":",|(?=\\\\))","patterns":[{"match":"true|false","name":"constant.language.boolean.matlab"},{"match":"p(?:ublic|rotected|rivate)","name":"constant.language.access.matlab"}]}]}},"end":"\\\\s*(?:^|[,;\\\\s])(end)\\\\b","endCaptures":{"1":{"name":"keyword.control.end.events.matlab"}},"name":"meta.events.matlab","patterns":[{"include":"$self"}]},{"begin":"^(\\\\s*)(enumeration)\\\\b([^%]*)\\\\s*($|(?=%))","beginCaptures":{"2":{"name":"keyword.control.enumeration.matlab"}},"end":"\\\\s*(?:^|[,;\\\\s])(end)\\\\b","endCaptures":{"1":{"name":"keyword.control.end.enumeration.matlab"}},"name":"meta.enumeration.matlab","patterns":[{"include":"$self"}]},{"include":"$self"}]}]},"command_dual":{"captures":{"1":{"name":"string.interpolated.matlab"},"2":{"name":"variable.other.command.matlab"},"28":{"name":"comment.line.percentage.matlab"}},"match":"^\\\\s*(([A-HJ-MO-Zbcdfghklmoq-z]\\\\w*|an??|a([0-9A-Z_a-mo-z]\\\\w*|n[0-9A-Z_a-rt-z]\\\\w*|ns\\\\w+)|ep??|e([0-9A-Z_a-oq-z]\\\\w*|p[0-9A-Z_a-rt-z]\\\\w*|ps\\\\w+)|in|i([0-9A-Z_a-mo-z]\\\\w*|n[0-9A-Z_a-eg-z]\\\\w*|nf\\\\w+)|In??|I([0-9A-Z_a-mo-z]\\\\w*|n[0-9A-Z_a-eg-z]\\\\w*|nf\\\\w+)|j\\\\w+|Na??|N([0-9A-Z_b-z]\\\\w*|a[0-9A-MO-Z_a-z]\\\\w*|aN\\\\w+)|na??|narg??|nargi|nargou??|n([0-9A-Z_b-z]\\\\w*|a([0-9A-Z_a-mopqs-z]\\\\w*|n\\\\w+|r([0-9A-Z_a-fh-z]\\\\w*|g([0-9A-Z_a-hj-nq-z]\\\\w*|i([0-9A-Z_a-mo-z]\\\\w*|n\\\\w+)|o([0-9A-Z_a-tv-z]\\\\w*|u([A-Za-su-z]\\\\w*|t\\\\w+))))))|p|p[0-9A-Z_a-hj-z]\\\\w*|pi\\\\w+)\\\\s+((([^\\"%-/:->@\\\\\\\\^{|~\\\\s]|(?=\')|(?=\\"))|(\\\\.\\\\^|\\\\.\\\\*|\\\\./|\\\\.\\\\\\\\|\\\\.\'|\\\\.\\\\(|&&|==|\\\\|\\\\||&(?=[^\\\\&])|\\\\|(?=[^|])|~=|<=|>=|~(?!=)|<(?!=)|>(?!=)|[-*+/:@\\\\\\\\^])(\\\\S|\\\\s*(?=%)|\\\\s+$|\\\\s+([]\\\\&)*,/:->@\\\\\\\\^|}]|(\\\\.(?:[^.\\\\d]|\\\\.[^.]))))|(\\\\.[^\'(*/A-Z\\\\\\\\^a-z\\\\s]))([^%]|\'[^\']*\'|\\"[^\\"]*\\")*|(\\\\.(?=\\\\s)|\\\\.[A-Za-z]|(?=\\\\{))([^\\"%\'(=]|==|\'[^\']*\'|\\"[^\\"]*\\"|\\\\(|\\\\([^%)]*\\\\)|\\\\[|\\\\[[^]%]*]|\\\\{|\\\\{[^%}]*})*(\\\\.\\\\.\\\\.[^%]*)?((?=%)|$)))(%.*)?$"},"comment_block":{"begin":"^(\\\\s*)%\\\\{[^\\\\n\\\\S]*+\\\\n","beginCaptures":{"1":{"name":"punctuation.definition.comment.matlab"}},"end":"^\\\\s*%}[^\\\\n\\\\S]*+(?:\\\\n|$)","name":"comment.block.percentage.matlab","patterns":[{"include":"#comment_block"},{"match":"^[^\\\\n]*\\\\n"}]},"comments":{"patterns":[{"begin":"(^[\\\\t ]+)?(?=%%\\\\s)","beginCaptures":{"1":{"name":"punctuation.whitespace.comment.leading.matlab"}},"end":"(?!\\\\G)","patterns":[{"begin":"%%","beginCaptures":{"0":{"name":"punctuation.definition.comment.matlab"}},"end":"\\\\n","name":"comment.line.double-percentage.matlab","patterns":[{"begin":"\\\\G[^\\\\n\\\\S]*(?![\\\\n\\\\s])","contentName":"meta.cell.matlab","end":"(?=\\\\n)"}]}]},{"include":"#comment_block"},{"begin":"(^[\\\\t ]+)?(?=%)","beginCaptures":{"1":{"name":"punctuation.whitespace.comment.leading.matlab"}},"end":"(?!\\\\G)","patterns":[{"begin":"%","beginCaptures":{"0":{"name":"punctuation.definition.comment.matlab"}},"end":"\\\\n","name":"comment.line.percentage.matlab"}]}]},"conjugate_transpose":{"match":"((?<=\\\\S)|(?<=])|(?<=\\\\))|(?<=}))\'","name":"keyword.operator.transpose.matlab"},"constants":{"match":"(?<!\\\\.)\\\\b(eps|false|Inf|inf|intmax|intmin|namelengthmax|NaN|nan|on|off|realmax|realmin|true|pi)\\\\b","name":"constant.language.matlab"},"control_statements":{"captures":{"1":{"name":"keyword.control.matlab"}},"match":"\\\\s*(?:^|[,;\\\\s])(break|continue|return)\\\\b","name":"meta.control.matlab"},"curly_brackets":{"begin":"\\\\{","end":"}","patterns":[{"include":"#end_in_parens"},{"include":"#all_before_command_dual"},{"include":"#all_after_command_dual"},{"include":"#end_in_parens"},{"include":"#block_keywords"}]},"end_in_parens":{"match":"\\\\bend\\\\b","name":"keyword.operator.symbols.matlab"},"function":{"patterns":[{"begin":"^(\\\\s*)(function)\\\\s+(?:(?:(\\\\[)([^]]*)(])|([A-Za-z][0-9A-Z_a-z]*))\\\\s*=\\\\s*)?([A-Za-z][0-9A-Z_a-z]*(\\\\.[A-Za-z][0-9A-Z_a-z]*)*)\\\\s*","beginCaptures":{"2":{"name":"storage.type.function.matlab"},"3":{"name":"punctuation.definition.arguments.begin.matlab"},"4":{"patterns":[{"match":"\\\\w+","name":"variable.parameter.output.matlab"}]},"5":{"name":"punctuation.definition.arguments.end.matlab"},"6":{"name":"variable.parameter.output.function.matlab"},"7":{"name":"entity.name.function.matlab"}},"end":"\\\\s*(?:^|[,;\\\\s])(end)\\\\b(\\\\s*\\\\n)?","endCaptures":{"1":{"name":"keyword.control.end.function.matlab"}},"name":"meta.function.matlab","patterns":[{"begin":"\\\\G\\\\(","end":"\\\\)","name":"meta.arguments.function.matlab","patterns":[{"include":"#line_continuation"},{"match":"\\\\w+","name":"variable.parameter.input.matlab"}]},{"begin":"^(\\\\s*)(arguments)\\\\b([^%]*)\\\\s*(\\\\([^)]*\\\\))?\\\\s*($|(?=%))","beginCaptures":{"2":{"name":"keyword.control.arguments.matlab"},"3":{"patterns":[{"match":"[A-Za-z][0-9A-Z_a-z]*","name":"variable.parameter.arguments.matlab"}]}},"end":"\\\\s*(?:^|[,;\\\\s])(end)\\\\b","endCaptures":{"1":{"name":"keyword.control.end.arguments.matlab"}},"name":"meta.arguments.matlab","patterns":[{"include":"#validators"},{"include":"$self"}]},{"include":"$self"}]}]},"global_persistent":{"captures":{"1":{"name":"keyword.control.globalpersistent.matlab"}},"match":"^\\\\s*(global|persistent)\\\\b","name":"meta.globalpersistent.matlab"},"indexing_curly_brackets":{"Comment":"Match identifier{idx, idx, } and stop at newline without ... This helps with partially written code like x{idx ","begin":"([A-Za-z][.0-9A-Z_a-z]*\\\\s*)\\\\{","beginCaptures":{"1":{"patterns":[{"include":"$self"}]}},"end":"(}|(?<!\\\\.\\\\.\\\\.).\\\\n)","patterns":[{"include":"#end_in_parens"},{"include":"#all_before_command_dual"},{"include":"#all_after_command_dual"},{"include":"#end_in_parens"},{"include":"#block_keywords"}]},"line_continuation":{"captures":{"1":{"name":"keyword.operator.symbols.matlab"},"2":{"name":"comment.line.continuation.matlab"}},"match":"(\\\\.\\\\.\\\\.)(.*)$","name":"meta.linecontinuation.matlab"},"numbers":{"match":"(?<=[(*-\\\\-/:=\\\\[\\\\\\\\{\\\\s]|^)\\\\d*\\\\.?\\\\d+([Ee][-+]?\\\\d)?([0-9&&[^.]])*([ij])?\\\\b","name":"constant.numeric.matlab"},"operators":{"match":"(?<=\\\\s)(==|~=|>=??|<=??|&&??|[:|]|\\\\|\\\\||[-*+]|\\\\.\\\\*|/|\\\\./|\\\\\\\\|\\\\.\\\\\\\\|\\\\^|\\\\.\\\\^)(?=\\\\s)","name":"keyword.operator.symbols.matlab"},"parens":{"begin":"\\\\(","end":"(\\\\)|(?<!\\\\.\\\\.\\\\.).\\\\n)","patterns":[{"include":"#end_in_parens"},{"include":"#all_before_command_dual"},{"include":"#all_after_command_dual"},{"include":"#block_keywords"}]},"square_brackets":{"begin":"\\\\[","end":"]","patterns":[{"include":"#all_before_command_dual"},{"include":"#all_after_command_dual"},{"include":"#block_keywords"}]},"string":{"patterns":[{"captures":{"1":{"name":"string.interpolated.matlab"},"2":{"name":"punctuation.definition.string.begin.matlab"}},"match":"^\\\\s*((!).*$\\\\n?)"},{"begin":"((?<=([\\\\&(*-/:->\\\\[\\\\\\\\^{|~\\\\s]))|^)\'","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.matlab"}},"end":"\'(?=([\\\\&(-/:->\\\\[-^{-~\\\\s]))","endCaptures":{"0":{"name":"punctuation.definition.string.end.matlab"}},"name":"string.quoted.single.matlab","patterns":[{"match":"\'\'","name":"constant.character.escape.matlab"},{"match":"\'(?=.)","name":"invalid.illegal.unescaped-quote.matlab"},{"match":"((%([-+0]?\\\\d{0,3}(\\\\.\\\\d{1,3})?)([EGc-gs]|(([bt])?([Xoux]))))|%%|\\\\\\\\([\\\\\\\\bfnrt]))","name":"constant.character.escape.matlab"}]},{"begin":"((?<=([\\\\&(*-/:->\\\\[\\\\\\\\^{|~\\\\s]))|^)\\"","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.matlab"}},"end":"\\"(?=([\\\\&(-/:->\\\\[-^{-~\\\\s]))","endCaptures":{"0":{"name":"punctuation.definition.string.end.matlab"}},"name":"string.quoted.double.matlab","patterns":[{"match":"\\"\\"","name":"constant.character.escape.matlab"},{"match":"\\"(?=.)","name":"invalid.illegal.unescaped-quote.matlab"}]}]},"transpose":{"match":"\\\\.\'","name":"keyword.operator.transpose.matlab"},"validator_strings":{"patterns":[{"patterns":[{"begin":"((?<=([\\\\&(*-/:->\\\\[\\\\\\\\^{|~\\\\s]))|^)\'","end":"\'(?=([\\\\&(-/:->\\\\[-^{-~\\\\s]))","name":"storage.type.matlab","patterns":[{"match":"\'\'"},{"match":"\'(?=.)"},{"match":"([^\']+)"}]},{"begin":"((?<=([\\\\&(*-/:->\\\\[\\\\\\\\^{|~\\\\s]))|^)\\"","end":"\\"(?=([\\\\&(-/:->\\\\[-^{-~\\\\s]))","name":"storage.type.matlab","patterns":[{"match":"\\"\\""},{"match":"\\"(?=.)"},{"match":"[^\\"]+"}]}]}]},"validators":{"begin":"\\\\s*;?\\\\s*([A-Za-z][.0-9?A-Z_a-z]*)","end":"([\\\\n%;=].*)","endCaptures":{"1":{"patterns":[{"captures":{"1":{"patterns":[{"include":"$self"}]}},"match":"(%.*)"},{"captures":{"1":{"patterns":[{"include":"$self"}]}},"match":"(=[^;]*)"},{"captures":{"1":{"patterns":[{"include":"#validators"}]}},"match":"([\\\\n;]\\\\s*[A-Za-z].*)"},{"include":"$self"}]}},"patterns":[{"include":"#line_continuation"},{"match":"\\\\s*(\\\\([^)]*\\\\))","name":"storage.type.matlab"},{"match":"([A-Za-z][.0-9A-Z_a-z]*)","name":"storage.type.matlab"},{"include":"#braced_validator_list"}]},"variables":{"match":"(?<!\\\\.)\\\\b(nargin|nargout|varargin|varargout)\\\\b","name":"variable.other.function.matlab"}},"scopeName":"source.matlab"}'))]}}]);