(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@shikijs/langs/dist/css.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const lang = Object.freeze(JSON.parse("{\"displayName\":\"CSS\",\"name\":\"css\",\"patterns\":[{\"include\":\"#comment-block\"},{\"include\":\"#escapes\"},{\"include\":\"#combinators\"},{\"include\":\"#selector\"},{\"include\":\"#at-rules\"},{\"include\":\"#rule-list\"}],\"repository\":{\"at-rules\":{\"patterns\":[{\"begin\":\"\\\\A\\\\uFEFF?(?i:(?=\\\\s*@charset\\\\b))\",\"end\":\";|(?=$)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.terminator.rule.css\"}},\"name\":\"meta.at-rule.charset.css\",\"patterns\":[{\"captures\":{\"1\":{\"name\":\"invalid.illegal.not-lowercase.charset.css\"},\"2\":{\"name\":\"invalid.illegal.leading-whitespace.charset.css\"},\"3\":{\"name\":\"invalid.illegal.no-whitespace.charset.css\"},\"4\":{\"name\":\"invalid.illegal.whitespace.charset.css\"},\"5\":{\"name\":\"invalid.illegal.not-double-quoted.charset.css\"},\"6\":{\"name\":\"invalid.illegal.unclosed-string.charset.css\"},\"7\":{\"name\":\"invalid.illegal.unexpected-characters.charset.css\"}},\"match\":\"\\\\G((?!@charset)@\\\\w+)|\\\\G(\\\\s+)|(@charset\\\\S[^;]*)|(?<=@charset)( {2,}|\\\\t+)|(?<=@charset )([^\\\";]+)|(\\\"[^\\\"]+)$|(?<=\\\")([^;]+)\"},{\"captures\":{\"1\":{\"name\":\"keyword.control.at-rule.charset.css\"},\"2\":{\"name\":\"punctuation.definition.keyword.css\"}},\"match\":\"((@)charset)(?=\\\\s)\"},{\"begin\":\"\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.css\"}},\"end\":\"\\\"|$\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.css\"}},\"name\":\"string.quoted.double.css\",\"patterns\":[{\"begin\":\"(?:\\\\G|^)(?=[^\\\"]+$)\",\"end\":\"$\",\"name\":\"invalid.illegal.unclosed.string.css\"}]}]},{\"begin\":\"(?i)((@)import)(?:\\\\s+|$|(?=[\\\"']|/\\\\*))\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.at-rule.import.css\"},\"2\":{\"name\":\"punctuation.definition.keyword.css\"}},\"end\":\";\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.terminator.rule.css\"}},\"name\":\"meta.at-rule.import.css\",\"patterns\":[{\"begin\":\"\\\\G\\\\s*(?=/\\\\*)\",\"end\":\"(?<=\\\\*/)\\\\s*\",\"patterns\":[{\"include\":\"#comment-block\"}]},{\"include\":\"#string\"},{\"include\":\"#url\"},{\"include\":\"#media-query-list\"}]},{\"begin\":\"(?i)((@)font-face)(?=\\\\s*|\\\\{|/\\\\*|$)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.at-rule.font-face.css\"},\"2\":{\"name\":\"punctuation.definition.keyword.css\"}},\"end\":\"(?!\\\\G)\",\"name\":\"meta.at-rule.font-face.css\",\"patterns\":[{\"include\":\"#comment-block\"},{\"include\":\"#escapes\"},{\"include\":\"#rule-list\"}]},{\"begin\":\"(?i)(@)page(?=[:{\\\\s]|/\\\\*|$)\",\"captures\":{\"0\":{\"name\":\"keyword.control.at-rule.page.css\"},\"1\":{\"name\":\"punctuation.definition.keyword.css\"}},\"end\":\"(?=\\\\s*($|[:;{]))\",\"name\":\"meta.at-rule.page.css\",\"patterns\":[{\"include\":\"#rule-list\"}]},{\"begin\":\"(?i)(?=@media([(\\\\s]|/\\\\*|$))\",\"end\":\"(?<=})(?!\\\\G)\",\"patterns\":[{\"begin\":\"(?i)\\\\G(@)media\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.control.at-rule.media.css\"},\"1\":{\"name\":\"punctuation.definition.keyword.css\"}},\"end\":\"(?=\\\\s*[;{])\",\"name\":\"meta.at-rule.media.header.css\",\"patterns\":[{\"include\":\"#media-query-list\"}]},{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.media.begin.bracket.curly.css\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.media.end.bracket.curly.css\"}},\"name\":\"meta.at-rule.media.body.css\",\"patterns\":[{\"include\":\"$self\"}]}]},{\"begin\":\"(?i)(?=@counter-style([\\\"';{\\\\s]|/\\\\*|$))\",\"end\":\"(?<=})(?!\\\\G)\",\"patterns\":[{\"begin\":\"(?i)\\\\G(@)counter-style\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.control.at-rule.counter-style.css\"},\"1\":{\"name\":\"punctuation.definition.keyword.css\"}},\"end\":\"(?=\\\\s*\\\\{)\",\"name\":\"meta.at-rule.counter-style.header.css\",\"patterns\":[{\"include\":\"#comment-block\"},{\"include\":\"#escapes\"},{\"captures\":{\"0\":{\"patterns\":[{\"include\":\"#escapes\"}]}},\"match\":\"[-A-Z_a-z[^\\\\x00-\\\\x7F]](?:[-0-9A-Z_a-z[^\\\\x00-\\\\x7F]]|\\\\\\\\(?:\\\\h{1,6}|.))*\",\"name\":\"variable.parameter.style-name.css\"}]},{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.property-list.begin.bracket.curly.css\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.property-list.end.bracket.curly.css\"}},\"name\":\"meta.at-rule.counter-style.body.css\",\"patterns\":[{\"include\":\"#comment-block\"},{\"include\":\"#escapes\"},{\"include\":\"#rule-list-innards\"}]}]},{\"begin\":\"(?i)(?=@document([\\\"';{\\\\s]|/\\\\*|$))\",\"end\":\"(?<=})(?!\\\\G)\",\"patterns\":[{\"begin\":\"(?i)\\\\G(@)document\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.control.at-rule.document.css\"},\"1\":{\"name\":\"punctuation.definition.keyword.css\"}},\"end\":\"(?=\\\\s*[;{])\",\"name\":\"meta.at-rule.document.header.css\",\"patterns\":[{\"begin\":\"(?i)(?<![-\\\\w])(url-prefix|domain|regexp)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.document-rule.css\"},\"2\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"name\":\"meta.function.document-rule.css\",\"patterns\":[{\"include\":\"#string\"},{\"include\":\"#comment-block\"},{\"include\":\"#escapes\"},{\"match\":\"[^\\\"')\\\\s]+\",\"name\":\"variable.parameter.document-rule.css\"}]},{\"include\":\"#url\"},{\"include\":\"#commas\"},{\"include\":\"#comment-block\"},{\"include\":\"#escapes\"}]},{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.document.begin.bracket.curly.css\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.document.end.bracket.curly.css\"}},\"name\":\"meta.at-rule.document.body.css\",\"patterns\":[{\"include\":\"$self\"}]}]},{\"begin\":\"(?i)(?=@(?:-(?:webkit|moz|o|ms)-)?keyframes([\\\"';{\\\\s]|/\\\\*|$))\",\"end\":\"(?<=})(?!\\\\G)\",\"patterns\":[{\"begin\":\"(?i)\\\\G(@)(?:-(?:webkit|moz|o|ms)-)?keyframes\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.control.at-rule.keyframes.css\"},\"1\":{\"name\":\"punctuation.definition.keyword.css\"}},\"end\":\"(?=\\\\s*\\\\{)\",\"name\":\"meta.at-rule.keyframes.header.css\",\"patterns\":[{\"include\":\"#comment-block\"},{\"include\":\"#escapes\"},{\"captures\":{\"0\":{\"patterns\":[{\"include\":\"#escapes\"}]}},\"match\":\"[-A-Z_a-z[^\\\\x00-\\\\x7F]](?:[-0-9A-Z_a-z[^\\\\x00-\\\\x7F]]|\\\\\\\\(?:\\\\h{1,6}|.))*\",\"name\":\"variable.parameter.keyframe-list.css\"}]},{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.keyframes.begin.bracket.curly.css\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.keyframes.end.bracket.curly.css\"}},\"name\":\"meta.at-rule.keyframes.body.css\",\"patterns\":[{\"include\":\"#comment-block\"},{\"include\":\"#escapes\"},{\"captures\":{\"1\":{\"name\":\"entity.other.keyframe-offset.css\"},\"2\":{\"name\":\"entity.other.keyframe-offset.percentage.css\"}},\"match\":\"(?i)(?<![-\\\\w])(from|to)(?![-\\\\w])|([-+]?(?:\\\\d+(?:\\\\.\\\\d+)?|\\\\.\\\\d+)%)\"},{\"include\":\"#rule-list\"}]}]},{\"begin\":\"(?i)(?=@supports([(\\\\s]|/\\\\*|$))\",\"end\":\"(?<=})(?!\\\\G)|(?=;)\",\"patterns\":[{\"begin\":\"(?i)\\\\G(@)supports\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.control.at-rule.supports.css\"},\"1\":{\"name\":\"punctuation.definition.keyword.css\"}},\"end\":\"(?=\\\\s*[;{])\",\"name\":\"meta.at-rule.supports.header.css\",\"patterns\":[{\"include\":\"#feature-query-operators\"},{\"include\":\"#feature-query\"},{\"include\":\"#comment-block\"},{\"include\":\"#escapes\"}]},{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.supports.begin.bracket.curly.css\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.supports.end.bracket.curly.css\"}},\"name\":\"meta.at-rule.supports.body.css\",\"patterns\":[{\"include\":\"$self\"}]}]},{\"begin\":\"(?i)((@)(-(ms|o)-)?viewport)(?=[\\\"';{\\\\s]|/\\\\*|$)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.at-rule.viewport.css\"},\"2\":{\"name\":\"punctuation.definition.keyword.css\"}},\"end\":\"(?=\\\\s*[;@{])\",\"name\":\"meta.at-rule.viewport.css\",\"patterns\":[{\"include\":\"#comment-block\"},{\"include\":\"#escapes\"}]},{\"begin\":\"(?i)((@)font-feature-values)(?=[\\\"';{\\\\s]|/\\\\*|$)\\\\s*\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.at-rule.font-feature-values.css\"},\"2\":{\"name\":\"punctuation.definition.keyword.css\"}},\"contentName\":\"variable.parameter.font-name.css\",\"end\":\"(?=\\\\s*[;@{])\",\"name\":\"meta.at-rule.font-features.css\",\"patterns\":[{\"include\":\"#comment-block\"},{\"include\":\"#escapes\"}]},{\"include\":\"#font-features\"},{\"begin\":\"(?i)((@)namespace)(?=[\\\"';\\\\s]|/\\\\*|$)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.at-rule.namespace.css\"},\"2\":{\"name\":\"punctuation.definition.keyword.css\"}},\"end\":\";|(?=[@{])\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.terminator.rule.css\"}},\"name\":\"meta.at-rule.namespace.css\",\"patterns\":[{\"include\":\"#url\"},{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#comment-block\"}]},\"2\":{\"name\":\"entity.name.function.namespace-prefix.css\",\"patterns\":[{\"include\":\"#escapes\"}]}},\"match\":\"(?i)(?:\\\\G|^|(?<=\\\\s))(?=(?<=\\\\s|^)[-A-Z_a-z[^\\\\x00-\\\\x7F]]|\\\\s*/\\\\*(?:[^*]|\\\\*[^/])*\\\\*/)(.*?)([-A-Z_a-z[^\\\\x00-\\\\x7F]](?:[-0-9A-Z_a-z[^\\\\x00-\\\\x7F]]|\\\\\\\\(?:\\\\h{1,6}|.))*)\"},{\"include\":\"#comment-block\"},{\"include\":\"#escapes\"},{\"include\":\"#string\"}]},{\"begin\":\"(?i)(?=@[-\\\\w]+[^;]+;s*$)\",\"end\":\"(?<=;)(?!\\\\G)\",\"patterns\":[{\"begin\":\"(?i)\\\\G(@)[-\\\\w]+\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.control.at-rule.css\"},\"1\":{\"name\":\"punctuation.definition.keyword.css\"}},\"end\":\";\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.terminator.rule.css\"}},\"name\":\"meta.at-rule.header.css\"}]},{\"begin\":\"(?i)(?=@[-\\\\w]+([({\\\\s]|/\\\\*|$))\",\"end\":\"(?<=})(?!\\\\G)\",\"patterns\":[{\"begin\":\"(?i)\\\\G(@)[-\\\\w]+\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.control.at-rule.css\"},\"1\":{\"name\":\"punctuation.definition.keyword.css\"}},\"end\":\"(?=\\\\s*[;{])\",\"name\":\"meta.at-rule.header.css\"},{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.begin.bracket.curly.css\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.end.bracket.curly.css\"}},\"name\":\"meta.at-rule.body.css\",\"patterns\":[{\"include\":\"$self\"}]}]}]},\"color-keywords\":{\"patterns\":[{\"match\":\"(?i)(?<![-\\\\w])(aqua|black|blue|fuchsia|gray|green|lime|maroon|navy|olive|orange|purple|red|silver|teal|white|yellow)(?![-\\\\w])\",\"name\":\"support.constant.color.w3c-standard-color-name.css\"},{\"match\":\"(?i)(?<![-\\\\w])(aliceblue|antiquewhite|aquamarine|azure|beige|bisque|blanchedalmond|blueviolet|brown|burlywood|cadetblue|chartreuse|chocolate|coral|cornflowerblue|cornsilk|crimson|cyan|darkblue|darkcyan|darkgoldenrod|darkgray|darkgreen|darkgrey|darkkhaki|darkmagenta|darkolivegreen|darkorange|darkorchid|darkred|darksalmon|darkseagreen|darkslateblue|darkslategray|darkslategrey|darkturquoise|darkviolet|deeppink|deepskyblue|dimgray|dimgrey|dodgerblue|firebrick|floralwhite|forestgreen|gainsboro|ghostwhite|gold|goldenrod|greenyellow|grey|honeydew|hotpink|indianred|indigo|ivory|khaki|lavender|lavenderblush|lawngreen|lemonchiffon|lightblue|lightcoral|lightcyan|lightgoldenrodyellow|lightgray|lightgreen|lightgrey|lightpink|lightsalmon|lightseagreen|lightskyblue|lightslategray|lightslategrey|lightsteelblue|lightyellow|limegreen|linen|magenta|mediumaquamarine|mediumblue|mediumorchid|mediumpurple|mediumseagreen|mediumslateblue|mediumspringgreen|mediumturquoise|mediumvioletred|midnightblue|mintcream|mistyrose|moccasin|navajowhite|oldlace|olivedrab|orangered|orchid|palegoldenrod|palegreen|paleturquoise|palevioletred|papayawhip|peachpuff|peru|pink|plum|powderblue|rebeccapurple|rosybrown|royalblue|saddlebrown|salmon|sandybrown|seagreen|seashell|sienna|skyblue|slateblue|slategray|slategrey|snow|springgreen|steelblue|tan|thistle|tomato|transparent|turquoise|violet|wheat|whitesmoke|yellowgreen)(?![-\\\\w])\",\"name\":\"support.constant.color.w3c-extended-color-name.css\"},{\"match\":\"(?i)(?<![-\\\\w])currentColor(?![-\\\\w])\",\"name\":\"support.constant.color.current.css\"},{\"match\":\"(?i)(?<![-\\\\w])(ActiveBorder|ActiveCaption|AppWorkspace|Background|ButtonFace|ButtonHighlight|ButtonShadow|ButtonText|CaptionText|GrayText|Highlight|HighlightText|InactiveBorder|InactiveCaption|InactiveCaptionText|InfoBackground|InfoText|Menu|MenuText|Scrollbar|ThreeDDarkShadow|ThreeDFace|ThreeDHighlight|ThreeDLightShadow|ThreeDShadow|Window|WindowFrame|WindowText)(?![-\\\\w])\",\"name\":\"invalid.deprecated.color.system.css\"}]},\"combinators\":{\"patterns\":[{\"match\":\"/deep/|>>>\",\"name\":\"invalid.deprecated.combinator.css\"},{\"match\":\">>|[+>~]\",\"name\":\"keyword.operator.combinator.css\"}]},\"commas\":{\"match\":\",\",\"name\":\"punctuation.separator.list.comma.css\"},\"comment-block\":{\"begin\":\"/\\\\*\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.begin.css\"}},\"end\":\"\\\\*/\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.end.css\"}},\"name\":\"comment.block.css\"},\"escapes\":{\"patterns\":[{\"match\":\"\\\\\\\\\\\\h{1,6}\",\"name\":\"constant.character.escape.codepoint.css\"},{\"begin\":\"\\\\\\\\$\\\\s*\",\"end\":\"^(?<!\\\\G)\",\"name\":\"constant.character.escape.newline.css\"},{\"match\":\"\\\\\\\\.\",\"name\":\"constant.character.escape.css\"}]},\"feature-query\":{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.condition.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.condition.end.bracket.round.css\"}},\"name\":\"meta.feature-query.css\",\"patterns\":[{\"include\":\"#feature-query-operators\"},{\"include\":\"#feature-query\"}]},\"feature-query-operators\":{\"patterns\":[{\"match\":\"(?i)(?<=[()\\\\s]|^|\\\\*/)(and|not|or)(?=[()\\\\s]|/\\\\*|$)\",\"name\":\"keyword.operator.logical.feature.$1.css\"},{\"include\":\"#rule-list-innards\"}]},\"font-features\":{\"begin\":\"(?i)((@)(annotation|character-variant|ornaments|styleset|stylistic|swash))(?=[\\\"';@{\\\\s]|/\\\\*|$)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.at-rule.${3:/downcase}.css\"},\"2\":{\"name\":\"punctuation.definition.keyword.css\"}},\"end\":\"(?<=})\",\"name\":\"meta.at-rule.${3:/downcase}.css\",\"patterns\":[{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.property-list.begin.bracket.curly.css\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.property-list.end.bracket.curly.css\"}},\"name\":\"meta.property-list.font-feature.css\",\"patterns\":[{\"captures\":{\"0\":{\"patterns\":[{\"include\":\"#escapes\"}]}},\"match\":\"[-A-Z_a-z[^\\\\x00-\\\\x7F]](?:[-0-9A-Z_a-z[^\\\\x00-\\\\x7F]]|\\\\\\\\(?:\\\\h{1,6}|.))*\",\"name\":\"variable.font-feature.css\"},{\"include\":\"#rule-list-innards\"}]}]},\"functional-pseudo-classes\":{\"patterns\":[{\"begin\":\"(?i)((:)dir)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"entity.other.attribute-name.pseudo-class.css\"},\"2\":{\"name\":\"punctuation.definition.entity.css\"},\"3\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"patterns\":[{\"include\":\"#comment-block\"},{\"include\":\"#escapes\"},{\"match\":\"(?i)(?<![-\\\\w])(ltr|rtl)(?![-\\\\w])\",\"name\":\"support.constant.text-direction.css\"},{\"include\":\"#property-values\"}]},{\"begin\":\"(?i)((:)lang)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"entity.other.attribute-name.pseudo-class.css\"},\"2\":{\"name\":\"punctuation.definition.entity.css\"},\"3\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"patterns\":[{\"match\":\"(?<=[(,\\\\s])[A-Za-z]+(-[0-9A-Za-z]*|\\\\\\\\(?:\\\\h{1,6}|.))*(?=[),\\\\s])\",\"name\":\"support.constant.language-range.css\"},{\"begin\":\"\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.css\"}},\"end\":\"\\\"\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.css\"}},\"name\":\"string.quoted.double.css\",\"patterns\":[{\"include\":\"#escapes\"},{\"match\":\"(?<=[\\\"\\\\s])[*A-Za-z]+(-[*0-9A-Za-z]*)*(?=[\\\"\\\\s])\",\"name\":\"support.constant.language-range.css\"}]},{\"begin\":\"'\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.css\"}},\"end\":\"'\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.css\"}},\"name\":\"string.quoted.single.css\",\"patterns\":[{\"include\":\"#escapes\"},{\"match\":\"(?<=['\\\\s])[*A-Za-z]+(-[*0-9A-Za-z]*)*(?=['\\\\s])\",\"name\":\"support.constant.language-range.css\"}]},{\"include\":\"#commas\"}]},{\"begin\":\"(?i)((:)(?:not|has|matches|where|is))(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"entity.other.attribute-name.pseudo-class.css\"},\"2\":{\"name\":\"punctuation.definition.entity.css\"},\"3\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"patterns\":[{\"include\":\"#selector-innards\"}]},{\"begin\":\"(?i)((:)nth-(?:last-)?(?:child|of-type))(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"entity.other.attribute-name.pseudo-class.css\"},\"2\":{\"name\":\"punctuation.definition.entity.css\"},\"3\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"patterns\":[{\"match\":\"(?i)[-+]?(\\\\d+n?|n)(\\\\s*[-+]\\\\s*\\\\d+)?\",\"name\":\"constant.numeric.css\"},{\"match\":\"(?i)even|odd\",\"name\":\"support.constant.parity.css\"}]}]},\"functions\":{\"patterns\":[{\"begin\":\"(?i)(?<![-\\\\w])(calc)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.calc.css\"},\"2\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"name\":\"meta.function.calc.css\",\"patterns\":[{\"match\":\"[*/]|(?<=\\\\s|^)[-+](?=\\\\s|$)\",\"name\":\"keyword.operator.arithmetic.css\"},{\"include\":\"#property-values\"}]},{\"begin\":\"(?i)(?<![-\\\\w])(rgba?|hsla?|hwb|lab|oklab|lch|oklch|color)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.misc.css\"},\"2\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"name\":\"meta.function.color.css\",\"patterns\":[{\"include\":\"#property-values\"}]},{\"begin\":\"(?i)(?<![-\\\\w])((?:-(?:webkit-|moz-|o-))?(?:repeating-)?(?:linear|radial|conic)-gradient)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.gradient.css\"},\"2\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"name\":\"meta.function.gradient.css\",\"patterns\":[{\"match\":\"(?i)(?<![-\\\\w])(from|to|at|in|hue)(?![-\\\\w])\",\"name\":\"keyword.operator.gradient.css\"},{\"include\":\"#property-values\"}]},{\"begin\":\"(?i)(?<![-\\\\w])(-webkit-gradient)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"invalid.deprecated.gradient.function.css\"},\"2\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"name\":\"meta.function.gradient.invalid.deprecated.gradient.css\",\"patterns\":[{\"begin\":\"(?i)(?<![-\\\\w])(from|to|color-stop)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"invalid.deprecated.function.css\"},\"2\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"patterns\":[{\"include\":\"#property-values\"}]},{\"include\":\"#property-values\"}]},{\"begin\":\"(?i)(?<![-\\\\w])(annotation|attr|blur|brightness|character-variant|clamp|contrast|counters?|cross-fade|drop-shadow|element|fit-content|format|grayscale|hue-rotate|color-mix|image-set|invert|local|max|min|minmax|opacity|ornaments|repeat|saturate|sepia|styleset|stylistic|swash|symbols|cos|sin|tan|acos|asin|atan2??|hypot|sqrt|pow|log|exp|abs|sign)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.misc.css\"},\"2\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"name\":\"meta.function.misc.css\",\"patterns\":[{\"match\":\"(?i)(?<=[\\\",\\\\s]|\\\\*/|^)\\\\d+x(?=[\\\"'),\\\\s]|/\\\\*|$)\",\"name\":\"constant.numeric.other.density.css\"},{\"include\":\"#property-values\"},{\"match\":\"[^\\\"'),\\\\s]+\",\"name\":\"variable.parameter.misc.css\"}]},{\"begin\":\"(?i)(?<![-\\\\w])(circle|ellipse|inset|polygon|rect)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.shape.css\"},\"2\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"name\":\"meta.function.shape.css\",\"patterns\":[{\"match\":\"(?i)(?<=\\\\s|^|\\\\*/)(at|round)(?=\\\\s|/\\\\*|$)\",\"name\":\"keyword.operator.shape.css\"},{\"include\":\"#property-values\"}]},{\"begin\":\"(?i)(?<![-\\\\w])(cubic-bezier|steps)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.timing-function.css\"},\"2\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"name\":\"meta.function.timing-function.css\",\"patterns\":[{\"match\":\"(?i)(?<![-\\\\w])(start|end)(?=\\\\s*\\\\)|$)\",\"name\":\"support.constant.step-direction.css\"},{\"include\":\"#property-values\"}]},{\"begin\":\"(?i)(?<![-\\\\w])((?:translate|scale|rotate)(?:[XYZ]|3D)?|matrix(?:3D)?|skew[XY]?|perspective)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.transform.css\"},\"2\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"patterns\":[{\"include\":\"#property-values\"}]},{\"include\":\"#url\"},{\"begin\":\"(?i)(?<![-\\\\w])(var)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.misc.css\"},\"2\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"name\":\"meta.function.variable.css\",\"patterns\":[{\"match\":\"--[-A-Z_a-z[^\\\\x00-\\\\x7F]](?:[-0-9A-Z_a-z[^\\\\x00-\\\\x7F]]|\\\\\\\\(?:\\\\h{1,6}|.))*\",\"name\":\"variable.argument.css\"},{\"include\":\"#property-values\"}]}]},\"media-feature-keywords\":{\"match\":\"(?i)(?<=^|[:\\\\s]|\\\\*/)(?:portrait|landscape|progressive|interlace|fullscreen|standalone|minimal-ui|browser|hover)(?=[)\\\\s]|$)\",\"name\":\"support.constant.property-value.css\"},\"media-features\":{\"captures\":{\"1\":{\"name\":\"support.type.property-name.media.css\"},\"2\":{\"name\":\"support.type.property-name.media.css\"},\"3\":{\"name\":\"support.type.vendored.property-name.media.css\"}},\"match\":\"(?i)(?<=^|[(\\\\s]|\\\\*/)(?:((?:m(?:in-|ax-))?(?:height|width|aspect-ratio|color|color-index|monochrome|resolution)|grid|scan|orientation|display-mode|hover)|((?:m(?:in-|ax-))?device-(?:height|width|aspect-ratio))|((?:[-_](?:webkit|apple|khtml|epub|moz|ms|o|xv|ah|rim|atsc|hp|tc|wap|ro)|(?:mso|prince))-[-\\\\w]+(?=\\\\s*(?:/\\\\*(?:[^*]|\\\\*[^/])*\\\\*/)?\\\\s*[):])))(?=\\\\s|$|[):<=>]|/\\\\*)\"},\"media-query\":{\"begin\":\"\\\\G\",\"end\":\"(?=\\\\s*[;{])\",\"patterns\":[{\"include\":\"#comment-block\"},{\"include\":\"#escapes\"},{\"include\":\"#media-types\"},{\"match\":\"(?i)(?<=\\\\s|^|,|\\\\*/)(only|not)(?=[{\\\\s]|/\\\\*|$)\",\"name\":\"keyword.operator.logical.$1.media.css\"},{\"match\":\"(?i)(?<=\\\\s|^|\\\\*/|\\\\))and(?=\\\\s|/\\\\*|$)\",\"name\":\"keyword.operator.logical.and.media.css\"},{\"match\":\",(?:(?:\\\\s*,)+|(?=\\\\s*[);{]))\",\"name\":\"invalid.illegal.comma.css\"},{\"include\":\"#commas\"},{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.parameters.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.parameters.end.bracket.round.css\"}},\"patterns\":[{\"include\":\"#media-features\"},{\"include\":\"#media-feature-keywords\"},{\"match\":\":\",\"name\":\"punctuation.separator.key-value.css\"},{\"match\":\">=|<=|[<=>]\",\"name\":\"keyword.operator.comparison.css\"},{\"captures\":{\"1\":{\"name\":\"constant.numeric.css\"},\"2\":{\"name\":\"keyword.operator.arithmetic.css\"},\"3\":{\"name\":\"constant.numeric.css\"}},\"match\":\"(\\\\d+)\\\\s*(/)\\\\s*(\\\\d+)\",\"name\":\"meta.ratio.css\"},{\"include\":\"#numeric-values\"},{\"include\":\"#comment-block\"}]}]},\"media-query-list\":{\"begin\":\"(?=\\\\s*[^;{])\",\"end\":\"(?=\\\\s*[;{])\",\"patterns\":[{\"include\":\"#media-query\"}]},\"media-types\":{\"captures\":{\"1\":{\"name\":\"support.constant.media.css\"},\"2\":{\"name\":\"invalid.deprecated.constant.media.css\"}},\"match\":\"(?i)(?<=^|[,\\\\s]|\\\\*/)(?:(all|print|screen|speech)|(aural|braille|embossed|handheld|projection|tty|tv))(?=$|[,;{\\\\s]|/\\\\*)\"},\"numeric-values\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"punctuation.definition.constant.css\"}},\"match\":\"(#)(?:\\\\h{3,4}|\\\\h{6}|\\\\h{8})\\\\b\",\"name\":\"constant.other.color.rgb-value.hex.css\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.unit.percentage.css\"},\"2\":{\"name\":\"keyword.other.unit.${2:/downcase}.css\"}},\"match\":\"(?i)(?<![-\\\\w])[-+]?(?:[0-9]+(?:\\\\.[0-9]+)?|\\\\.[0-9]+)(?:(?<=[0-9])E[-+]?[0-9]+)?(?:(%)|(deg|grad|rad|turn|Hz|kHz|ch|cm|em|ex|fr|in|mm|mozmm|pc|pt|px|q|rem|rch|rex|rlh|ic|ric|rcap|vh|vw|vb|vi|svh|svw|svb|svi|dvh|dvw|dvb|dvi|lvh|lvw|lvb|lvi|vmax|vmin|cqw|cqi|cqh|cqb|cqmin|cqmax|dpi|dpcm|dppx|s|ms)\\\\b)?\",\"name\":\"constant.numeric.css\"}]},\"property-keywords\":{\"patterns\":[{\"match\":\"(?i)(?<![-\\\\w])(above|absolute|active|add|additive|after-edge|alias|all|all-petite-caps|all-scroll|all-small-caps|alpha|alphabetic|alternate|alternate-reverse|always|antialiased|auto|auto-fill|auto-fit|auto-pos|available|avoid|avoid-column|avoid-page|avoid-region|backwards|balance|baseline|before-edge|below|bevel|bidi-override|blink|block|block-axis|block-start|block-end|bold|bolder|border|border-box|both|bottom|bottom-outside|break-all|break-word|bullets|butt|capitalize|caption|cell|center|central|char|circle|clip|clone|close-quote|closest-corner|closest-side|col-resize|collapse|color|color-burn|color-dodge|column|column-reverse|common-ligatures|compact|condensed|contain|content|content-box|contents|context-menu|contextual|copy|cover|crisp-edges|crispEdges|crosshair|cyclic|dark|darken|dashed|decimal|default|dense|diagonal-fractions|difference|digits|disabled|disc|discretionary-ligatures|distribute|distribute-all-lines|distribute-letter|distribute-space|dot|dotted|double|double-circle|downleft|downright|e-resize|each-line|ease|ease-in|ease-in-out|ease-out|economy|ellipse|ellipsis|embed|end|evenodd|ew-resize|exact|exclude|exclusion|expanded|extends|extra-condensed|extra-expanded|fallback|farthest-corner|farthest-side|fill|fill-available|fill-box|filled|fit-content|fixed|flat|flex|flex-end|flex-start|flip|flow-root|forwards|freeze|from-image|full-width|geometricPrecision|georgian|grab|grabbing|grayscale|grid|groove|hand|hanging|hard-light|help|hidden|hide|historical-forms|historical-ligatures|horizontal|horizontal-tb|hue|icon|ideograph-alpha|ideograph-numeric|ideograph-parenthesis|ideograph-space|ideographic|inactive|infinite|inherit|initial|inline|inline-axis|inline-block|inline-end|inline-flex|inline-grid|inline-list-item|inline-start|inline-table|inset|inside|inter-character|inter-ideograph|inter-word|intersect|invert|isolate|isolate-override|italic|jis04|jis78|jis83|jis90|justify|justify-all|kannada|keep-all|landscape|larger??|left|light|lighten|lighter|line|line-edge|line-through|linear|linearRGB|lining-nums|list-item|local|loose|lowercase|lr|lr-tb|ltr|luminance|luminosity|main-size|mandatory|manipulation|manual|margin-box|match-parent|match-source|mathematical|max-content|medium|menu|message-box|middle|min-content|miter|mixed|move|multiply|n-resize|narrower|ne-resize|nearest-neighbor|nesw-resize|newspaper|no-change|no-clip|no-close-quote|no-common-ligatures|no-contextual|no-discretionary-ligatures|no-drop|no-historical-ligatures|no-open-quote|no-repeat|none|nonzero|normal|not-allowed|nowrap|ns-resize|numbers|numeric|nw-resize|nwse-resize|oblique|oldstyle-nums|open|open-quote|optimizeLegibility|optimizeQuality|optimizeSpeed|optional|ordinal|outset|outside|over|overlay|overline|padding|padding-box|page|painted|pan-down|pan-left|pan-right|pan-up|pan-x|pan-y|paused|petite-caps|pixelated|plaintext|pointer|portrait|pre|pre-line|pre-wrap|preserve-3d|progress|progressive|proportional-nums|proportional-width|proximity|radial|recto|region|relative|remove|repeat|repeat-[xy]|reset-size|reverse|revert|ridge|right|rl|rl-tb|round|row|row-resize|row-reverse|row-severse|rtl|ruby|ruby-base|ruby-base-container|ruby-text|ruby-text-container|run-in|running|s-resize|saturation|scale-down|screen|scroll|scroll-position|se-resize|semi-condensed|semi-expanded|separate|sesame|show|sideways|sideways-left|sideways-lr|sideways-right|sideways-rl|simplified|slashed-zero|slice|small|small-caps|small-caption|smaller|smooth|soft-light|solid|space|space-around|space-between|space-evenly|spell-out|square|sRGB|stacked-fractions|start|static|status-bar|swap|step-end|step-start|sticky|stretch|strict|stroke|stroke-box|style|sub|subgrid|subpixel-antialiased|subtract|super|sw-resize|symbolic|table|table-caption|table-cell|table-column|table-column-group|table-footer-group|table-header-group|table-row|table-row-group|tabular-nums|tb|tb-rl|text|text-after-edge|text-before-edge|text-bottom|text-top|thick|thin|titling-caps|top|top-outside|touch|traditional|transparent|triangle|ultra-condensed|ultra-expanded|under|underline|unicase|unset|upleft|uppercase|upright|use-glyph-orientation|use-script|verso|vertical|vertical-ideographic|vertical-lr|vertical-rl|vertical-text|view-box|visible|visibleFill|visiblePainted|visibleStroke|w-resize|wait|wavy|weight|whitespace|wider|words|wrap|wrap-reverse|x|x-large|x-small|xx-large|xx-small|y|zero|zoom-in|zoom-out)(?![-\\\\w])\",\"name\":\"support.constant.property-value.css\"},{\"match\":\"(?i)(?<![-\\\\w])(arabic-indic|armenian|bengali|cambodian|circle|cjk-decimal|cjk-earthly-branch|cjk-heavenly-stem|cjk-ideographic|decimal|decimal-leading-zero|devanagari|disc|disclosure-closed|disclosure-open|ethiopic-halehame-am|ethiopic-halehame-ti-e[rt]|ethiopic-numeric|georgian|gujarati|gurmukhi|hangul|hangul-consonant|hebrew|hiragana|hiragana-iroha|japanese-formal|japanese-informal|kannada|katakana|katakana-iroha|khmer|korean-hangul-formal|korean-hanja-formal|korean-hanja-informal|lao|lower-alpha|lower-armenian|lower-greek|lower-latin|lower-roman|malayalam|mongolian|myanmar|oriya|persian|simp-chinese-formal|simp-chinese-informal|square|tamil|telugu|thai|tibetan|trad-chinese-formal|trad-chinese-informal|upper-alpha|upper-armenian|upper-latin|upper-roman|urdu)(?![-\\\\w])\",\"name\":\"support.constant.property-value.list-style-type.css\"},{\"match\":\"(?<![-\\\\w])(?i:-(?:ah|apple|atsc|epub|hp|khtml|moz|ms|o|rim|ro|tc|wap|webkit|xv)|(?:mso|prince))-[-A-Za-z]+\",\"name\":\"support.constant.vendored.property-value.css\"},{\"match\":\"(?<![-\\\\w])(?i:arial|century|comic|courier|garamond|georgia|helvetica|impact|lucida|symbol|system-ui|system|tahoma|times|trebuchet|ui-monospace|ui-rounded|ui-sans-serif|ui-serif|utopia|verdana|webdings|sans-serif|serif|monospace)(?![-\\\\w])\",\"name\":\"support.constant.font-name.css\"}]},\"property-names\":{\"patterns\":[{\"match\":\"(?i)(?<![-\\\\w])(?:accent-color|additive-symbols|align-content|align-items|align-self|all|animation|animation-delay|animation-direction|animation-duration|animation-fill-mode|animation-iteration-count|animation-name|animation-play-state|animation-timing-function|aspect-ratio|backdrop-filter|backface-visibility|background|background-attachment|background-blend-mode|background-clip|background-color|background-image|background-origin|background-position|background-position-[xy]|background-repeat|background-size|bleed|block-size|border|border-block-end|border-block-end-color|border-block-end-style|border-block-end-width|border-block-start|border-block-start-color|border-block-start-style|border-block-start-width|border-bottom|border-bottom-color|border-bottom-left-radius|border-bottom-right-radius|border-bottom-style|border-bottom-width|border-collapse|border-color|border-end-end-radius|border-end-start-radius|border-image|border-image-outset|border-image-repeat|border-image-slice|border-image-source|border-image-width|border-inline-end|border-inline-end-color|border-inline-end-style|border-inline-end-width|border-inline-start|border-inline-start-color|border-inline-start-style|border-inline-start-width|border-left|border-left-color|border-left-style|border-left-width|border-radius|border-right|border-right-color|border-right-style|border-right-width|border-spacing|border-start-end-radius|border-start-start-radius|border-style|border-top|border-top-color|border-top-left-radius|border-top-right-radius|border-top-style|border-top-width|border-width|bottom|box-decoration-break|box-shadow|box-sizing|break-after|break-before|break-inside|caption-side|caret-color|clear|clip|clip-path|clip-rule|color|color-adjust|color-interpolation-filters|color-scheme|column-count|column-fill|column-gap|column-rule|column-rule-color|column-rule-style|column-rule-width|column-span|column-width|columns|contain|container|container-name|container-type|content|counter-increment|counter-reset|cursor|direction|display|empty-cells|enable-background|fallback|fill|fill-opacity|fill-rule|filter|flex|flex-basis|flex-direction|flex-flow|flex-grow|flex-shrink|flex-wrap|float|flood-color|flood-opacity|font|font-display|font-family|font-feature-settings|font-kerning|font-language-override|font-optical-sizing|font-size|font-size-adjust|font-stretch|font-style|font-synthesis|font-variant|font-variant-alternates|font-variant-caps|font-variant-east-asian|font-variant-ligatures|font-variant-numeric|font-variant-position|font-variation-settings|font-weight|gap|glyph-orientation-horizontal|glyph-orientation-vertical|grid|grid-area|grid-auto-columns|grid-auto-flow|grid-auto-rows|grid-column|grid-column-end|grid-column-gap|grid-column-start|grid-gap|grid-row|grid-row-end|grid-row-gap|grid-row-start|grid-template|grid-template-areas|grid-template-columns|grid-template-rows|hanging-punctuation|height|hyphens|image-orientation|image-rendering|image-resolution|ime-mode|initial-letter|initial-letter-align|inline-size|inset|inset-block|inset-block-end|inset-block-start|inset-inline|inset-inline-end|inset-inline-start|isolation|justify-content|justify-items|justify-self|kerning|left|letter-spacing|lighting-color|line-break|line-clamp|line-height|list-style|list-style-image|list-style-position|list-style-type|margin|margin-block|margin-block-end|margin-block-start|margin-bottom|margin-inline|margin-inline-end|margin-inline-start|margin-left|margin-right|margin-top|marker-end|marker-mid|marker-start|marks|mask|mask-border|mask-border-mode|mask-border-outset|mask-border-repeat|mask-border-slice|mask-border-source|mask-border-width|mask-clip|mask-composite|mask-image|mask-mode|mask-origin|mask-position|mask-repeat|mask-size|mask-type|max-block-size|max-height|max-inline-size|max-lines|max-width|max-zoom|min-block-size|min-height|min-inline-size|min-width|min-zoom|mix-blend-mode|negative|object-fit|object-position|offset|offset-anchor|offset-distance|offset-path|offset-position|offset-rotation|opacity|order|orientation|orphans|outline|outline-color|outline-offset|outline-style|outline-width|overflow|overflow-anchor|overflow-block|overflow-inline|overflow-wrap|overflow-[xy]|overscroll-behavior|overscroll-behavior-block|overscroll-behavior-inline|overscroll-behavior-[xy]|pad|padding|padding-block|padding-block-end|padding-block-start|padding-bottom|padding-inline|padding-inline-end|padding-inline-start|padding-left|padding-right|padding-top|page-break-after|page-break-before|page-break-inside|paint-order|perspective|perspective-origin|place-content|place-items|place-self|pointer-events|position|prefix|quotes|range|resize|right|rotate|row-gap|ruby-align|ruby-merge|ruby-position|scale|scroll-behavior|scroll-margin|scroll-margin-block|scroll-margin-block-end|scroll-margin-block-start|scroll-margin-bottom|scroll-margin-inline|scroll-margin-inline-end|scroll-margin-inline-start|scroll-margin-left|scroll-margin-right|scroll-margin-top|scroll-padding|scroll-padding-block|scroll-padding-block-end|scroll-padding-block-start|scroll-padding-bottom|scroll-padding-inline|scroll-padding-inline-end|scroll-padding-inline-start|scroll-padding-left|scroll-padding-right|scroll-padding-top|scroll-snap-align|scroll-snap-coordinate|scroll-snap-destination|scroll-snap-stop|scroll-snap-type|scrollbar-color|scrollbar-gutter|scrollbar-width|shape-image-threshold|shape-margin|shape-outside|shape-rendering|size|speak-as|src|stop-color|stop-opacity|stroke|stroke-dasharray|stroke-dashoffset|stroke-linecap|stroke-linejoin|stroke-miterlimit|stroke-opacity|stroke-width|suffix|symbols|system|tab-size|table-layout|text-align|text-align-last|text-anchor|text-combine-upright|text-decoration|text-decoration-color|text-decoration-line|text-decoration-skip|text-decoration-skip-ink|text-decoration-style|text-decoration-thickness|text-emphasis|text-emphasis-color|text-emphasis-position|text-emphasis-style|text-indent|text-justify|text-orientation|text-overflow|text-rendering|text-shadow|text-size-adjust|text-transform|text-underline-offset|text-underline-position|top|touch-action|transform|transform-box|transform-origin|transform-style|transition|transition-delay|transition-duration|transition-property|transition-timing-function|translate|unicode-bidi|unicode-range|user-select|user-zoom|vertical-align|visibility|white-space|widows|width|will-change|word-break|word-spacing|word-wrap|writing-mode|z-index|zoom|alignment-baseline|baseline-shift|clip-rule|color-interpolation|color-interpolation-filters|color-profile|color-rendering|cx|cy|dominant-baseline|enable-background|fill|fill-opacity|fill-rule|flood-color|flood-opacity|glyph-orientation-horizontal|glyph-orientation-vertical|height|kerning|lighting-color|marker-end|marker-mid|marker-start|rx??|ry|shape-rendering|stop-color|stop-opacity|stroke|stroke-dasharray|stroke-dashoffset|stroke-linecap|stroke-linejoin|stroke-miterlimit|stroke-opacity|stroke-width|text-anchor|width|[xy]|adjust|after|align|align-last|alignment|alignment-adjust|appearance|attachment|azimuth|background-break|balance|baseline|before|bidi|binding|bookmark|bookmark-label|bookmark-level|bookmark-target|border-length|bottom-color|bottom-left-radius|bottom-right-radius|bottom-style|bottom-width|box|box-align|box-direction|box-flex|box-flex-group|box-lines|box-ordinal-group|box-orient|box-pack|break|character|collapse|column|column-break-after|column-break-before|count|counter|crop|cue|cue-after|cue-before|decoration|decoration-break|delay|display-model|display-role|down|drop|drop-initial-after-adjust|drop-initial-after-align|drop-initial-before-adjust|drop-initial-before-align|drop-initial-size|drop-initial-value|duration|elevation|emphasis|family|fit|fit-position|flex-group|float-offset|gap|grid-columns|grid-rows|hanging-punctuation|header|hyphenate|hyphenate-after|hyphenate-before|hyphenate-character|hyphenate-lines|hyphenate-resource|icon|image|increment|indent|index|initial-after-adjust|initial-after-align|initial-before-adjust|initial-before-align|initial-size|initial-value|inline-box-align|iteration-count|justify|label|left-color|left-style|left-width|length|level|line|line-stacking|line-stacking-ruby|line-stacking-shift|line-stacking-strategy|lines|list|mark|mark-after|mark-before|marks|marquee|marquee-direction|marquee-play-count|marquee-speed|marquee-style|max|min|model|move-to|name|nav|nav-down|nav-index|nav-left|nav-right|nav-up|new|numeral|offset|ordinal-group|orient|origin|overflow-style|overhang|pack|page|page-policy|pause|pause-after|pause-before|phonemes|pitch|pitch-range|play-count|play-during|play-state|point|presentation|presentation-level|profile|property|punctuation|punctuation-trim|radius|rate|rendering-intent|repeat|replace|reset|resolution|resource|respond-to|rest|rest-after|rest-before|richness|right-color|right-style|right-width|role|rotation|rotation-point|rows|ruby|ruby-overhang|ruby-span|rule|rule-color|rule-style|rule-width|shadow|size|size-adjust|sizing|space|space-collapse|spacing|span|speak|speak-header|speak-numeral|speak-punctuation|speech|speech-rate|speed|stacking|stacking-ruby|stacking-shift|stacking-strategy|stress|stretch|string-set|style|style-image|style-position|style-type|target|target-name|target-new|target-position|text|text-height|text-justify|text-outline|text-replace|text-wrap|timing-function|top-color|top-left-radius|top-right-radius|top-style|top-width|trim|unicode|up|user-select|variant|voice|voice-balance|voice-duration|voice-family|voice-pitch|voice-pitch-range|voice-rate|voice-stress|voice-volume|volume|weight|white|white-space-collapse|word|wrap)(?![-\\\\w])\",\"name\":\"support.type.property-name.css\"},{\"match\":\"(?<![-\\\\w])(?i:-(?:ah|apple|atsc|epub|hp|khtml|moz|ms|o|rim|ro|tc|wap|webkit|xv)|(?:mso|prince))-[-A-Za-z]+\",\"name\":\"support.type.vendored.property-name.css\"}]},\"property-values\":{\"patterns\":[{\"include\":\"#commas\"},{\"include\":\"#comment-block\"},{\"include\":\"#escapes\"},{\"include\":\"#functions\"},{\"include\":\"#property-keywords\"},{\"include\":\"#unicode-range\"},{\"include\":\"#numeric-values\"},{\"include\":\"#color-keywords\"},{\"include\":\"#string\"},{\"match\":\"!\\\\s*important(?![-\\\\w])\",\"name\":\"keyword.other.important.css\"}]},\"pseudo-classes\":{\"captures\":{\"1\":{\"name\":\"punctuation.definition.entity.css\"},\"2\":{\"name\":\"invalid.illegal.colon.css\"}},\"match\":\"(?i)(:)(:*)(?:active|any-link|checked|default|disabled|empty|enabled|first|(?:first|last|only)-(?:child|of-type)|focus|focus-visible|focus-within|fullscreen|host|hover|in-range|indeterminate|invalid|left|link|optional|out-of-range|read-only|read-write|required|right|root|scope|target|unresolved|valid|visited)(?![-\\\\w]|\\\\s*[;}])\",\"name\":\"entity.other.attribute-name.pseudo-class.css\"},\"pseudo-elements\":{\"captures\":{\"1\":{\"name\":\"punctuation.definition.entity.css\"},\"2\":{\"name\":\"punctuation.definition.entity.css\"}},\"match\":\"(?i)(?:(::?)(?:after|before|first-letter|first-line|(?:-(?:ah|apple|atsc|epub|hp|khtml|moz|ms|o|rim|ro|tc|wap|webkit|xv)|(?:mso|prince))-[-a-z]+)|(::)(?:backdrop|content|grammar-error|marker|placeholder|selection|shadow|spelling-error))(?![-\\\\w]|\\\\s*[;}])\",\"name\":\"entity.other.attribute-name.pseudo-element.css\"},\"rule-list\":{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.property-list.begin.bracket.curly.css\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.property-list.end.bracket.curly.css\"}},\"name\":\"meta.property-list.css\",\"patterns\":[{\"include\":\"#rule-list-innards\"}]},\"rule-list-innards\":{\"patterns\":[{\"include\":\"#comment-block\"},{\"include\":\"#escapes\"},{\"include\":\"#font-features\"},{\"match\":\"(?<![-\\\\w])--[-A-Z_a-z[^\\\\x00-\\\\x7F]](?:[-0-9A-Z_a-z[^\\\\x00-\\\\x7F]]|\\\\\\\\(?:\\\\h{1,6}|.))*\",\"name\":\"variable.css\"},{\"begin\":\"(?<![-A-Za-z])(?=[-A-Za-z])\",\"end\":\"$|(?![-A-Za-z])\",\"name\":\"meta.property-name.css\",\"patterns\":[{\"include\":\"#property-names\"}]},{\"begin\":\"(:)\\\\s*\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.separator.key-value.css\"}},\"contentName\":\"meta.property-value.css\",\"end\":\"\\\\s*(;)|\\\\s*(?=[)}])\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.terminator.rule.css\"}},\"patterns\":[{\"include\":\"#comment-block\"},{\"include\":\"#property-values\"}]},{\"match\":\";\",\"name\":\"punctuation.terminator.rule.css\"}]},\"selector\":{\"begin\":\"(?=\\\\|?(?:[-#*.:A-\\\\[_a-z[^\\\\x00-\\\\x7F]]|\\\\\\\\(?:\\\\h{1,6}|.)))\",\"end\":\"(?=\\\\s*[)/@{])\",\"name\":\"meta.selector.css\",\"patterns\":[{\"include\":\"#selector-innards\"}]},\"selector-innards\":{\"patterns\":[{\"include\":\"#comment-block\"},{\"include\":\"#commas\"},{\"include\":\"#escapes\"},{\"include\":\"#combinators\"},{\"captures\":{\"1\":{\"name\":\"entity.other.namespace-prefix.css\"},\"2\":{\"name\":\"punctuation.separator.css\"}},\"match\":\"(?:^|(?<=[(,;}\\\\s]))(?![-*\\\\w]+\\\\|(?![-#*.:A-\\\\[_a-z[^\\\\x00-\\\\x7F]]))([-A-Z_a-z[^\\\\x00-\\\\x7F]](?:[-0-9A-Z_a-z[^\\\\x00-\\\\x7F]]|\\\\\\\\(?:\\\\h{1,6}|.))*|\\\\*)?(\\\\|)\"},{\"include\":\"#tag-names\"},{\"match\":\"\\\\*\",\"name\":\"entity.name.tag.wildcard.css\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.entity.css\"},\"2\":{\"patterns\":[{\"include\":\"#escapes\"}]}},\"match\":\"(?<![-@\\\\w])([#.])((?:-?[0-9]|-(?=$|[#)+,.:>\\\\[{|~\\\\s]|/\\\\*)|(?:[-0-9A-Z_a-z[^\\\\x00-\\\\x7F]]|\\\\\\\\(?:\\\\h{1,6}|.))*(?:[]!\\\"%-(*;<?@^`|}]|/(?!\\\\*))+)(?:[-0-9A-Z_a-z[^\\\\x00-\\\\x7F]]|\\\\\\\\(?:\\\\h{1,6}|.))*)\",\"name\":\"invalid.illegal.bad-identifier.css\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.entity.css\"},\"2\":{\"patterns\":[{\"include\":\"#escapes\"}]}},\"match\":\"(\\\\.)((?:[-0-9A-Z_a-z[^\\\\x00-\\\\x7F]]|\\\\\\\\(?:\\\\h{1,6}|.))+)(?=$|[#)+,.:>\\\\[{|~\\\\s]|/\\\\*)\",\"name\":\"entity.other.attribute-name.class.css\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.entity.css\"},\"2\":{\"patterns\":[{\"include\":\"#escapes\"}]}},\"match\":\"(#)(-?(?![0-9])(?:[-0-9A-Z_a-z[^\\\\x00-\\\\x7F]]|\\\\\\\\(?:\\\\h{1,6}|.))+)(?=$|[#)+,.:>\\\\[{|~\\\\s]|/\\\\*)\",\"name\":\"entity.other.attribute-name.id.css\"},{\"begin\":\"\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.entity.begin.bracket.square.css\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.entity.end.bracket.square.css\"}},\"name\":\"meta.attribute-selector.css\",\"patterns\":[{\"include\":\"#comment-block\"},{\"include\":\"#string\"},{\"captures\":{\"1\":{\"name\":\"storage.modifier.ignore-case.css\"}},\"match\":\"(?<=[\\\"'\\\\s]|^|\\\\*/)\\\\s*([Ii])\\\\s*(?=[]\\\\s]|/\\\\*|$)\"},{\"captures\":{\"1\":{\"name\":\"string.unquoted.attribute-value.css\",\"patterns\":[{\"include\":\"#escapes\"}]}},\"match\":\"(?<==)\\\\s*((?!/\\\\*)(?:[^]\\\"'\\\\\\\\\\\\s]|\\\\\\\\.)+)\"},{\"include\":\"#escapes\"},{\"match\":\"[$*^|~]?=\",\"name\":\"keyword.operator.pattern.css\"},{\"match\":\"\\\\|\",\"name\":\"punctuation.separator.css\"},{\"captures\":{\"1\":{\"name\":\"entity.other.namespace-prefix.css\",\"patterns\":[{\"include\":\"#escapes\"}]}},\"match\":\"(-?(?!\\\\d)(?:[-\\\\w[^\\\\x00-\\\\x7F]]|\\\\\\\\(?:\\\\h{1,6}|.))+|\\\\*)(?=\\\\|(?![=\\\\s]|$|])(?:-?(?!\\\\d)|[-\\\\\\\\\\\\w[^\\\\x00-\\\\x7F]]))\"},{\"captures\":{\"1\":{\"name\":\"entity.other.attribute-name.css\",\"patterns\":[{\"include\":\"#escapes\"}]}},\"match\":\"(-?(?!\\\\d)(?>[-\\\\w[^\\\\x00-\\\\x7F]]|\\\\\\\\(?:\\\\h{1,6}|.))+)\\\\s*(?=[]$*=^|~]|/\\\\*)\"}]},{\"include\":\"#pseudo-classes\"},{\"include\":\"#pseudo-elements\"},{\"include\":\"#functional-pseudo-classes\"},{\"match\":\"(?<![-@\\\\w])(?=[a-z]\\\\w*-)(?:(?![A-Z])[-\\\\w])+(?![-(\\\\w])\",\"name\":\"entity.name.tag.custom.css\"}]},\"string\":{\"patterns\":[{\"begin\":\"\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.css\"}},\"end\":\"\\\"|(?<!\\\\\\\\)(?=$|\\\\n)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.css\"}},\"name\":\"string.quoted.double.css\",\"patterns\":[{\"begin\":\"(?:\\\\G|^)(?=(?:[^\\\"\\\\\\\\]|\\\\\\\\.)+$)\",\"end\":\"$\",\"name\":\"invalid.illegal.unclosed.string.css\",\"patterns\":[{\"include\":\"#escapes\"}]},{\"include\":\"#escapes\"}]},{\"begin\":\"'\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.css\"}},\"end\":\"'|(?<!\\\\\\\\)(?=$|\\\\n)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.css\"}},\"name\":\"string.quoted.single.css\",\"patterns\":[{\"begin\":\"(?:\\\\G|^)(?=(?:[^'\\\\\\\\]|\\\\\\\\.)+$)\",\"end\":\"$\",\"name\":\"invalid.illegal.unclosed.string.css\",\"patterns\":[{\"include\":\"#escapes\"}]},{\"include\":\"#escapes\"}]}]},\"tag-names\":{\"match\":\"(?i)(?<![-:\\\\w])(?:a|abbr|acronym|address|applet|area|article|aside|audio|b|base|basefont|bdi|bdo|bgsound|big|blink|blockquote|body|br|button|canvas|caption|center|cite|code|col|colgroup|command|content|data|datalist|dd|del|details|dfn|dialog|dir|div|dl|dt|element|em|embed|fieldset|figcaption|figure|font|footer|form|frame|frameset|h[1-6]|head|header|hgroup|hr|html|i|iframe|image|img|input|ins|isindex|kbd|keygen|label|legend|li|link|listing|main|map|mark|marquee|math|menu|menuitem|meta|meter|multicol|nav|nextid|nobr|noembed|noframes|noscript|object|ol|optgroup|option|output|p|param|picture|plaintext|pre|progress|q|rb|rp|rtc??|ruby|s|samp|script|section|select|shadow|slot|small|source|spacer|span|strike|strong|style|sub|summary|sup|table|tbody|td|template|textarea|tfoot|th|thead|time|title|tr|track|tt|ul??|var|video|wbr|xmp|altGlyph|altGlyphDef|altGlyphItem|animate|animateColor|animateMotion|animateTransform|circle|clipPath|color-profile|cursor|defs|desc|discard|ellipse|feBlend|feColorMatrix|feComponentTransfer|feComposite|feConvolveMatrix|feDiffuseLighting|feDisplacementMap|feDistantLight|feDropShadow|feFlood|feFuncA|feFuncB|feFuncG|feFuncR|feGaussianBlur|feImage|feMerge|feMergeNode|feMorphology|feOffset|fePointLight|feSpecularLighting|feSpotLight|feTile|feTurbulence|filter|font-face|font-face-format|font-face-name|font-face-src|font-face-uri|foreignObject|g|glyph|glyphRef|hatch|hatchpath|hkern|line|linearGradient|marker|mask|mesh|meshgradient|meshpatch|meshrow|metadata|missing-glyph|mpath|path|pattern|polygon|polyline|radialGradient|rect|set|solidcolor|stop|svg|switch|symbol|text|textPath|tref|tspan|use|view|vkern|annotation|annotation-xml|maction|maligngroup|malignmark|math|menclose|merror|mfenced|mfrac|mglyph|mi|mlabeledtr|mlongdiv|mmultiscripts|mn|mo|mover|mpadded|mphantom|mroot|mrow|ms|mscarries|mscarry|msgroup|msline|mspace|msqrt|msrow|mstack|mstyle|msub|msubsup|msup|mtable|mtd|mtext|mtr|munder|munderover|semantics)(?=[#)+,.:>\\\\[{|~\\\\s]|/\\\\*|$)\",\"name\":\"entity.name.tag.css\"},\"unicode-range\":{\"captures\":{\"0\":{\"name\":\"constant.other.unicode-range.css\"},\"1\":{\"name\":\"punctuation.separator.dash.unicode-range.css\"}},\"match\":\"(?<![-\\\\w])[Uu]\\\\+[?\\\\h]{1,6}(?:(-)\\\\h{1,6})?(?![-\\\\w])\"},\"url\":{\"begin\":\"(?i)(?<![-@\\\\w])(url)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.url.css\"},\"2\":{\"name\":\"punctuation.section.function.begin.bracket.round.css\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.bracket.round.css\"}},\"name\":\"meta.function.url.css\",\"patterns\":[{\"match\":\"[^\\\"')\\\\s]+\",\"name\":\"variable.parameter.url.css\"},{\"include\":\"#string\"},{\"include\":\"#comment-block\"},{\"include\":\"#escapes\"}]}},\"scopeName\":\"source.css\"}"));
const __TURBOPACK__default__export__ = [
    lang
];
}}),
"[project]/node_modules/@shikijs/langs/dist/html.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$javascript$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/javascript.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$css$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/css.mjs [app-client] (ecmascript)");
;
;
const lang = Object.freeze(JSON.parse("{\"displayName\":\"HTML\",\"injections\":{\"R:text.html - (comment.block, text.html meta.embedded, meta.tag.*.*.html, meta.tag.*.*.*.html, meta.tag.*.*.*.*.html)\":{\"patterns\":[{\"match\":\"<\",\"name\":\"invalid.illegal.bad-angle-bracket.html\"}]}},\"name\":\"html\",\"patterns\":[{\"include\":\"#xml-processing\"},{\"include\":\"#comment\"},{\"include\":\"#doctype\"},{\"include\":\"#cdata\"},{\"include\":\"#tags-valid\"},{\"include\":\"#tags-invalid\"},{\"include\":\"#entities\"}],\"repository\":{\"attribute\":{\"patterns\":[{\"begin\":\"(s(hape|cope|t(ep|art)|ize(s)?|p(ellcheck|an)|elected|lot|andbox|rc(set|doc|lang)?)|h(ttp-equiv|i(dden|gh)|e(ight|aders)|ref(lang)?)|n(o(nce|validate|module)|ame)|c(h(ecked|arset)|ite|o(nt(ent(editable)?|rols)|ords|l(s(pan)?|or))|lass|rossorigin)|t(ype(mustmatch)?|itle|a(rget|bindex)|ranslate)|i(s(map)?|n(tegrity|putmode)|tem(scope|type|id|prop|ref)|d)|op(timum|en)|d(i(sabled|r(name)?)|ownload|e(coding|f(er|ault))|at(etime|a)|raggable)|usemap|p(ing|oster|la(ysinline|ceholder)|attern|reload)|enctype|value|kind|for(m(novalidate|target|enctype|action|method)?)?|w(idth|rap)|l(ist|o(op|w)|a(ng|bel))|a(s(ync)?|c(ce(sskey|pt(-charset)?)|tion)|uto(c(omplete|apitalize)|play|focus)|l(t|low(usermedia|paymentrequest|fullscreen))|bbr)|r(ows(pan)?|e(versed|quired|ferrerpolicy|l|adonly))|m(in(length)?|u(ted|ltiple)|e(thod|dia)|a(nifest|x(length)?)))(?![-:\\\\w])\",\"beginCaptures\":{\"0\":{\"name\":\"entity.other.attribute-name.html\"}},\"end\":\"(?=\\\\s*+[^=\\\\s])\",\"name\":\"meta.attribute.$1.html\",\"patterns\":[{\"include\":\"#attribute-interior\"}]},{\"begin\":\"style(?![-:\\\\w])\",\"beginCaptures\":{\"0\":{\"name\":\"entity.other.attribute-name.html\"}},\"end\":\"(?=\\\\s*+[^=\\\\s])\",\"name\":\"meta.attribute.style.html\",\"patterns\":[{\"begin\":\"=\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.separator.key-value.html\"}},\"end\":\"(?<=[^=\\\\s])(?!\\\\s*=)|(?=/?>)\",\"patterns\":[{\"begin\":\"(?=[^/<=>`\\\\s]|/(?!>))\",\"end\":\"(?!\\\\G)\",\"name\":\"meta.embedded.line.css\",\"patterns\":[{\"captures\":{\"0\":{\"name\":\"source.css\"}},\"match\":\"([^\\\"'/<=>`\\\\s]|/(?!>))+\",\"name\":\"string.unquoted.html\"},{\"begin\":\"\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.html\"}},\"contentName\":\"source.css\",\"end\":\"(\\\")\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.html\"},\"1\":{\"name\":\"source.css\"}},\"name\":\"string.quoted.double.html\",\"patterns\":[{\"include\":\"#entities\"}]},{\"begin\":\"'\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.html\"}},\"contentName\":\"source.css\",\"end\":\"(')\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.html\"},\"1\":{\"name\":\"source.css\"}},\"name\":\"string.quoted.single.html\",\"patterns\":[{\"include\":\"#entities\"}]}]},{\"match\":\"=\",\"name\":\"invalid.illegal.unexpected-equals-sign.html\"}]}]},{\"begin\":\"on(s(croll|t(orage|alled)|u(spend|bmit)|e(curitypolicyviolation|ek(ing|ed)|lect))|hashchange|c(hange|o(ntextmenu|py)|u(t|echange)|l(ick|ose)|an(cel|play(through)?))|t(imeupdate|oggle)|in(put|valid)|o((?:n|ff)line)|d(urationchange|r(op|ag(start|over|e(n(ter|d)|xit)|leave)?)|blclick)|un(handledrejection|load)|p(opstate|lay(ing)?|a(ste|use|ge(show|hide))|rogress)|e(nded|rror|mptied)|volumechange|key(down|up|press)|focus|w(heel|aiting)|l(oad(start|e(nd|d((?:|meta)data)))?|anguagechange)|a(uxclick|fterprint|bort)|r(e(s(ize|et)|jectionhandled)|atechange)|m(ouse(o(ut|ver)|down|up|enter|leave|move)|essage(error)?)|b(efore(unload|print)|lur))(?![-:\\\\w])\",\"beginCaptures\":{\"0\":{\"name\":\"entity.other.attribute-name.html\"}},\"end\":\"(?=\\\\s*+[^=\\\\s])\",\"name\":\"meta.attribute.event-handler.$1.html\",\"patterns\":[{\"begin\":\"=\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.separator.key-value.html\"}},\"end\":\"(?<=[^=\\\\s])(?!\\\\s*=)|(?=/?>)\",\"patterns\":[{\"begin\":\"(?=[^/<=>`\\\\s]|/(?!>))\",\"end\":\"(?!\\\\G)\",\"name\":\"meta.embedded.line.js\",\"patterns\":[{\"captures\":{\"0\":{\"name\":\"source.js\"},\"1\":{\"patterns\":[{\"include\":\"source.js\"}]}},\"match\":\"(([^\\\"'/<=>`\\\\s]|/(?!>))+)\",\"name\":\"string.unquoted.html\"},{\"begin\":\"\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.html\"}},\"contentName\":\"source.js\",\"end\":\"(\\\")\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.html\"},\"1\":{\"name\":\"source.js\"}},\"name\":\"string.quoted.double.html\",\"patterns\":[{\"captures\":{\"0\":{\"patterns\":[{\"include\":\"source.js\"}]}},\"match\":\"([^\\\\n\\\"/]|/(?![*/]))+\"},{\"begin\":\"//\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.js\"}},\"end\":\"(?=\\\")|\\\\n\",\"name\":\"comment.line.double-slash.js\"},{\"begin\":\"/\\\\*\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.begin.js\"}},\"end\":\"(?=\\\")|\\\\*/\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.end.js\"}},\"name\":\"comment.block.js\"}]},{\"begin\":\"'\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.html\"}},\"contentName\":\"source.js\",\"end\":\"(')\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.html\"},\"1\":{\"name\":\"source.js\"}},\"name\":\"string.quoted.single.html\",\"patterns\":[{\"captures\":{\"0\":{\"patterns\":[{\"include\":\"source.js\"}]}},\"match\":\"([^\\\\n'/]|/(?![*/]))+\"},{\"begin\":\"//\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.js\"}},\"end\":\"(?=')|\\\\n\",\"name\":\"comment.line.double-slash.js\"},{\"begin\":\"/\\\\*\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.begin.js\"}},\"end\":\"(?=')|\\\\*/\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.end.js\"}},\"name\":\"comment.block.js\"}]}]},{\"match\":\"=\",\"name\":\"invalid.illegal.unexpected-equals-sign.html\"}]}]},{\"begin\":\"(data-[-a-z]+)(?![-:\\\\w])\",\"beginCaptures\":{\"0\":{\"name\":\"entity.other.attribute-name.html\"}},\"end\":\"(?=\\\\s*+[^=\\\\s])\",\"name\":\"meta.attribute.data-x.$1.html\",\"patterns\":[{\"include\":\"#attribute-interior\"}]},{\"begin\":\"(align|bgcolor|border)(?![-:\\\\w])\",\"beginCaptures\":{\"0\":{\"name\":\"invalid.deprecated.entity.other.attribute-name.html\"}},\"end\":\"(?=\\\\s*+[^=\\\\s])\",\"name\":\"meta.attribute.$1.html\",\"patterns\":[{\"include\":\"#attribute-interior\"}]},{\"begin\":\"([^\\\\x00- \\\"'/<=>\\\\x7F-\\\\x{9F}﷐-﷯￾￿🿾🿿𯿾𯿿𿿾𿿿\\\\x{4FFFE}\\\\x{4FFFF}\\\\x{5FFFE}\\\\x{5FFFF}\\\\x{6FFFE}\\\\x{6FFFF}\\\\x{7FFFE}\\\\x{7FFFF}\\\\x{8FFFE}\\\\x{8FFFF}\\\\x{9FFFE}\\\\x{9FFFF}\\\\x{AFFFE}\\\\x{AFFFF}\\\\x{BFFFE}\\\\x{BFFFF}\\\\x{CFFFE}\\\\x{CFFFF}\\\\x{DFFFE}\\\\x{DFFFF}\\\\x{EFFFE}\\\\x{EFFFF}\\\\x{FFFFE}\\\\x{FFFFF}\\\\x{10FFFE}\\\\x{10FFFF}]+)\",\"beginCaptures\":{\"0\":{\"name\":\"entity.other.attribute-name.html\"}},\"end\":\"(?=\\\\s*+[^=\\\\s])\",\"name\":\"meta.attribute.unrecognized.$1.html\",\"patterns\":[{\"include\":\"#attribute-interior\"}]},{\"match\":\"[^>\\\\s]+\",\"name\":\"invalid.illegal.character-not-allowed-here.html\"}]},\"attribute-interior\":{\"patterns\":[{\"begin\":\"=\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.separator.key-value.html\"}},\"end\":\"(?<=[^=\\\\s])(?!\\\\s*=)|(?=/?>)\",\"patterns\":[{\"match\":\"([^\\\"'/<=>`\\\\s]|/(?!>))+\",\"name\":\"string.unquoted.html\"},{\"begin\":\"\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.html\"}},\"end\":\"\\\"\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.html\"}},\"name\":\"string.quoted.double.html\",\"patterns\":[{\"include\":\"#entities\"}]},{\"begin\":\"'\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.html\"}},\"end\":\"'\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.html\"}},\"name\":\"string.quoted.single.html\",\"patterns\":[{\"include\":\"#entities\"}]},{\"match\":\"=\",\"name\":\"invalid.illegal.unexpected-equals-sign.html\"}]}]},\"cdata\":{\"begin\":\"<!\\\\[CDATA\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.begin.html\"}},\"contentName\":\"string.other.inline-data.html\",\"end\":\"]]>\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.metadata.cdata.html\"},\"comment\":{\"begin\":\"<!--\",\"captures\":{\"0\":{\"name\":\"punctuation.definition.comment.html\"}},\"end\":\"-->\",\"name\":\"comment.block.html\",\"patterns\":[{\"match\":\"\\\\G-?>\",\"name\":\"invalid.illegal.characters-not-allowed-here.html\"},{\"match\":\"<!-(?:-(?!>)|(?=-->))\",\"name\":\"invalid.illegal.characters-not-allowed-here.html\"},{\"match\":\"--!>\",\"name\":\"invalid.illegal.characters-not-allowed-here.html\"}]},\"core-minus-invalid\":{\"patterns\":[{\"include\":\"#xml-processing\"},{\"include\":\"#comment\"},{\"include\":\"#doctype\"},{\"include\":\"#cdata\"},{\"include\":\"#tags-valid\"},{\"include\":\"#entities\"}]},\"doctype\":{\"begin\":\"<!(?=(?i:DOCTYPE\\\\s))\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.begin.html\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.metadata.doctype.html\",\"patterns\":[{\"match\":\"\\\\G(?i:DOCTYPE)\",\"name\":\"entity.name.tag.html\"},{\"begin\":\"\\\"\",\"end\":\"\\\"\",\"name\":\"string.quoted.double.html\"},{\"match\":\"[^>\\\\s]+\",\"name\":\"entity.other.attribute-name.html\"}]},\"entities\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"punctuation.definition.entity.html\"},\"912\":{\"name\":\"punctuation.definition.entity.html\"}},\"match\":\"(&)(?=[A-Za-z])((a(s(ymp(eq)?|cr|t)|n(d(slope|[dv]|and)?|g(s(t|ph)|zarr|e|le|rt(vb(d)?)?|msd(a([a-h]))?)?)|c(y|irc|d|ute|E)?|tilde|o(pf|gon)|uml|p(id|os|prox(eq)?|[Ee]|acir)?|elig|f(r)?|w((?:con|)int)|l(pha|e(ph|fsym))|acute|ring|grave|m(p|a(cr|lg))|breve)|A(s(sign|cr)|nd|MP|c(y|irc)|tilde|o(pf|gon)|uml|pplyFunction|fr|Elig|lpha|acute|ring|grave|macr|breve))|(B(scr|cy|opf|umpeq|e(cause|ta|rnoullis)|fr|a(ckslash|r(v|wed))|reve)|b(s(cr|im(e)?|ol(hsub|b)?|emi)|n(ot|e(quiv)?)|c(y|ong)|ig(s(tar|qcup)|c(irc|up|ap)|triangle(down|up)|o(times|dot|plus)|uplus|vee|wedge)|o(t(tom)?|pf|wtie|x(h([DUdu])?|times|H([DUdu])?|d([LRlr])|u([LRlr])|plus|D([LRlr])|v([HLRhlr])?|U([LRlr])|V([HLRhlr])?|minus|box))|Not|dquo|u(ll(et)?|mp(e(q)?|E)?)|prime|e(caus(e)?|t(h|ween|a)|psi|rnou|mptyv)|karow|fr|l(ock|k(1([24])|34)|a(nk|ck(square|triangle(down|left|right)?|lozenge)))|a(ck(sim(eq)?|cong|prime|epsilon)|r(vee|wed(ge)?))|r(eve|vbar)|brk(tbrk)?))|(c(s(cr|u(p(e)?|b(e)?))|h(cy|i|eck(mark)?)|ylcty|c(irc|ups(sm)?|edil|a(ps|ron))|tdot|ir(scir|c(eq|le(d(R|circ|S|dash|ast)|arrow(left|right)))?|e|fnint|E|mid)?|o(n(int|g(dot)?)|p(y(sr)?|f|rod)|lon(e(q)?)?|m(p(fn|le(xes|ment))?|ma(t)?))|dot|u(darr([lr])|p(s|c([au]p)|or|dot|brcap)?|e(sc|pr)|vee|wed|larr(p)?|r(vearrow(left|right)|ly(eq(succ|prec)|vee|wedge)|arr(m)?|ren))|e(nt(erdot)?|dil|mptyv)|fr|w((?:con|)int)|lubs(uit)?|a(cute|p(s|c([au]p)|dot|and|brcup)?|r(on|et))|r(oss|arr))|C(scr|hi|c(irc|onint|edil|aron)|ircle(Minus|Times|Dot|Plus)|Hcy|o(n(tourIntegral|int|gruent)|unterClockwiseContourIntegral|p(f|roduct)|lon(e)?)|dot|up(Cap)?|OPY|e(nterDot|dilla)|fr|lo(seCurly((?:Double|)Quote)|ckwiseContourIntegral)|a(yleys|cute|p(italDifferentialD)?)|ross))|(d(s(c([ry])|trok|ol)|har([lr])|c(y|aron)|t(dot|ri(f)?)|i(sin|e|v(ide(ontimes)?|onx)?|am(s|ond(suit)?)?|gamma)|Har|z(cy|igrarr)|o(t(square|plus|eq(dot)?|minus)?|ublebarwedge|pf|wn(harpoon(left|right)|downarrows|arrow)|llar)|d(otseq|a(rr|gger))?|u(har|arr)|jcy|e(lta|g|mptyv)|f(isht|r)|wangle|lc(orn|rop)|a(sh(v)?|leth|rr|gger)|r(c(orn|rop)|bkarow)|b(karow|lac)|Arr)|D(s(cr|trok)|c(y|aron)|Scy|i(fferentialD|a(critical(Grave|Tilde|Do(t|ubleAcute)|Acute)|mond))|o(t(Dot|Equal)?|uble(Right(Tee|Arrow)|ContourIntegral|Do(t|wnArrow)|Up((?:Down|)Arrow)|VerticalBar|L(ong(RightArrow|Left((?:Right|)Arrow))|eft(RightArrow|Tee|Arrow)))|pf|wn(Right(TeeVector|Vector(Bar)?)|Breve|Tee(Arrow)?|arrow|Left(RightVector|TeeVector|Vector(Bar)?)|Arrow(Bar|UpArrow)?))|Zcy|el(ta)?|D(otrahd)?|Jcy|fr|a(shv|rr|gger)))|(e(s(cr|im|dot)|n(sp|g)|c(y|ir(c)?|olon|aron)|t([ah])|o(pf|gon)|dot|u(ro|ml)|p(si(v|lon)?|lus|ar(sl)?)|e|D(D??ot)|q(s(im|lant(less|gtr))|c(irc|olon)|u(iv(DD)?|est|als)|vparsl)|f(Dot|r)|l(s(dot)?|inters|l)?|a(ster|cute)|r(Dot|arr)|g(s(dot)?|rave)?|x(cl|ist|p(onentiale|ectation))|m(sp(1([34]))?|pty(set|v)?|acr))|E(s(cr|im)|c(y|irc|aron)|ta|o(pf|gon)|NG|dot|uml|TH|psilon|qu(ilibrium|al(Tilde)?)|fr|lement|acute|grave|x(ists|ponentialE)|m(pty((?:|Very)SmallSquare)|acr)))|(f(scr|nof|cy|ilig|o(pf|r(k(v)?|all))|jlig|partint|emale|f(ilig|l(l??ig)|r)|l(tns|lig|at)|allingdotseq|r(own|a(sl|c(1([2-68])|78|2([35])|3([458])|45|5([68])))))|F(scr|cy|illed((?:|Very)SmallSquare)|o(uriertrf|pf|rAll)|fr))|(G(scr|c(y|irc|edil)|t|opf|dot|T|Jcy|fr|amma(d)?|reater(Greater|SlantEqual|Tilde|Equal(Less)?|FullEqual|Less)|g|breve)|g(s(cr|im([el])?)|n(sim|e(q(q)?)?|E|ap(prox)?)|c(y|irc)|t(c(c|ir)|dot|quest|lPar|r(sim|dot|eq(q?less)|less|a(pprox|rr)))?|imel|opf|dot|jcy|e(s(cc|dot(o(l)?)?|l(es)?)?|q(slant|q)?|l)?|v(nE|ertneqq)|fr|E(l)?|l([Eaj])?|a(cute|p|mma(d)?)|rave|g(g)?|breve))|(h(s(cr|trok|lash)|y(phen|bull)|circ|o(ok((?:lef|righ)tarrow)|pf|arr|rbar|mtht)|e(llip|arts(uit)?|rcon)|ks([ew]arow)|fr|a(irsp|lf|r(dcy|r(cir|w)?)|milt)|bar|Arr)|H(s(cr|trok)|circ|ilbertSpace|o(pf|rizontalLine)|ump(DownHump|Equal)|fr|a(cek|t)|ARDcy))|(i(s(cr|in(s(v)?|dot|[Ev])?)|n(care|t(cal|prod|e(rcal|gers)|larhk)?|odot|fin(tie)?)?|c(y|irc)?|t(ilde)?|i(nfin|i(i??nt)|ota)?|o(cy|ta|pf|gon)|u(kcy|ml)|jlig|prod|e(cy|xcl)|quest|f([fr])|acute|grave|m(of|ped|a(cr|th|g(part|e|line))))|I(scr|n(t(e(rsection|gral))?|visible(Comma|Times))|c(y|irc)|tilde|o(ta|pf|gon)|dot|u(kcy|ml)|Ocy|Jlig|fr|Ecy|acute|grave|m(plies|a(cr|ginaryI))?))|(j(s(cr|ercy)|c(y|irc)|opf|ukcy|fr|math)|J(s(cr|ercy)|c(y|irc)|opf|ukcy|fr))|(k(scr|hcy|c(y|edil)|opf|jcy|fr|appa(v)?|green)|K(scr|c(y|edil)|Hcy|opf|Jcy|fr|appa))|(l(s(h|cr|trok|im([eg])?|q(uo(r)?|b)|aquo)|h(ar(d|u(l)?)|blk)|n(sim|e(q(q)?)?|E|ap(prox)?)|c(y|ub|e(d??il)|aron)|Barr|t(hree|c(c|ir)|imes|dot|quest|larr|r(i([ef])?|Par))?|Har|o(ng(left((?:|right)arrow)|rightarrow|mapsto)|times|z(enge|f)?|oparrow(left|right)|p(f|lus|ar)|w(ast|bar)|a(ng|rr)|brk)|d(sh|ca|quo(r)?|r((?:d|us)har))|ur((?:ds|u)har)|jcy|par(lt)?|e(s(s(sim|dot|eq(q?gtr)|approx|gtr)|cc|dot(o(r)?)?|g(es)?)?|q(slant|q)?|ft(harpoon(down|up)|threetimes|leftarrows|arrow(tail)?|right(squigarrow|harpoons|arrow(s)?))|g)?|v(nE|ertneqq)|f(isht|loor|r)|E(g)?|l(hard|corner|tri|arr)?|a(ng(d|le)?|cute|t(e(s)?|ail)?|p|emptyv|quo|rr(sim|hk|tl|pl|fs|lp|b(fs)?)?|gran|mbda)|r(har(d)?|corner|tri|arr|m)|g(E)?|m(idot|oust(ache)?)|b(arr|r(k(sl([du])|e)|ac([ek]))|brk)|A(tail|arr|rr))|L(s(h|cr|trok)|c(y|edil|aron)|t|o(ng(RightArrow|left((?:|right)arrow)|rightarrow|Left((?:Right|)Arrow))|pf|wer((?:Righ|Lef)tArrow))|T|e(ss(Greater|SlantEqual|Tilde|EqualGreater|FullEqual|Less)|ft(Right(Vector|Arrow)|Ceiling|T(ee(Vector|Arrow)?|riangle(Bar|Equal)?)|Do(ubleBracket|wn(TeeVector|Vector(Bar)?))|Up(TeeVector|DownVector|Vector(Bar)?)|Vector(Bar)?|arrow|rightarrow|Floor|A(ngleBracket|rrow(RightArrow|Bar)?)))|Jcy|fr|l(eftarrow)?|a(ng|cute|placetrf|rr|mbda)|midot))|(M(scr|cy|inusPlus|opf|u|e(diumSpace|llintrf)|fr|ap)|m(s(cr|tpos)|ho|nplus|c(y|omma)|i(nus(d(u)?|b)?|cro|d(cir|dot|ast)?)|o(dels|pf)|dash|u((?:lti|)map)?|p|easuredangle|DDot|fr|l(cp|dr)|a(cr|p(sto(down|up|left)?)?|l(t(ese)?|e)|rker)))|(n(s(hort(parallel|mid)|c(cue|[er])?|im(e(q)?)?|u(cc(eq)?|p(set(eq(q)?)?|[Ee])?|b(set(eq(q)?)?|[Ee])?)|par|qsu([bp]e)|mid)|Rightarrow|h(par|arr|Arr)|G(t(v)?|g)|c(y|ong(dot)?|up|edil|a(p|ron))|t(ilde|lg|riangle(left(eq)?|right(eq)?)|gl)|i(s(d)?|v)?|o(t(ni(v([abc]))?|in(dot|v([abc])|E)?)?|pf)|dash|u(m(sp|ero)?)?|jcy|p(olint|ar(sl|t|allel)?|r(cue|e(c(eq)?)?)?)|e(s(im|ear)|dot|quiv|ar(hk|r(ow)?)|xist(s)?|Arr)?|v(sim|infin|Harr|dash|Dash|l(t(rie)?|e|Arr)|ap|r(trie|Arr)|g([et]))|fr|w(near|ar(hk|r(ow)?)|Arr)|V([Dd]ash)|l(sim|t(ri(e)?)?|dr|e(s(s)?|q(slant|q)?|ft((?:|right)arrow))?|E|arr|Arr)|a(ng|cute|tur(al(s)?)?|p(id|os|prox|E)?|bla)|r(tri(e)?|ightarrow|arr([cw])?|Arr)|g(sim|t(r)?|e(s|q(slant|q)?)?|E)|mid|L(t(v)?|eft((?:|right)arrow)|l)|b(sp|ump(e)?))|N(scr|c(y|edil|aron)|tilde|o(nBreakingSpace|Break|t(R(ightTriangle(Bar|Equal)?|everseElement)|Greater(Greater|SlantEqual|Tilde|Equal|FullEqual|Less)?|S(u(cceeds(SlantEqual|Tilde|Equal)?|perset(Equal)?|bset(Equal)?)|quareSu(perset(Equal)?|bset(Equal)?))|Hump(DownHump|Equal)|Nested(GreaterGreater|LessLess)|C(ongruent|upCap)|Tilde(Tilde|Equal|FullEqual)?|DoubleVerticalBar|Precedes((?:Slant|)Equal)?|E(qual(Tilde)?|lement|xists)|VerticalBar|Le(ss(Greater|SlantEqual|Tilde|Equal|Less)?|ftTriangle(Bar|Equal)?))?|pf)|u|e(sted(GreaterGreater|LessLess)|wLine|gative(MediumSpace|Thi((?:n|ck)Space)|VeryThinSpace))|Jcy|fr|acute))|(o(s(cr|ol|lash)|h(m|bar)|c(y|ir(c)?)|ti(lde|mes(as)?)|S|int|opf|d(sold|iv|ot|ash|blac)|uml|p(erp|lus|ar)|elig|vbar|f(cir|r)|l(c(ir|ross)|t|ine|arr)|a(st|cute)|r(slope|igof|or|d(er(of)?|[fm])?|v|arr)?|g(t|on|rave)|m(i(nus|cron|d)|ega|acr))|O(s(cr|lash)|c(y|irc)|ti(lde|mes)|opf|dblac|uml|penCurly((?:Double|)Quote)|ver(B(ar|rac(e|ket))|Parenthesis)|fr|Elig|acute|r|grave|m(icron|ega|acr)))|(p(s(cr|i)|h(i(v)?|one|mmat)|cy|i(tchfork|v)?|o(intint|und|pf)|uncsp|er(cnt|tenk|iod|p|mil)|fr|l(us(sim|cir|two|d([ou])|e|acir|mn|b)?|an(ck(h)?|kv))|ar(s(im|l)|t|a(llel)?)?|r(sim|n(sim|E|ap)|cue|ime(s)?|o(d|p(to)?|f(surf|line|alar))|urel|e(c(sim|n(sim|eqq|approx)|curlyeq|eq|approx)?)?|E|ap)?|m)|P(s(cr|i)|hi|cy|i|o(incareplane|pf)|fr|lusMinus|artialD|r(ime|o(duct|portion(al)?)|ecedes(SlantEqual|Tilde|Equal)?)?))|(q(scr|int|opf|u(ot|est(eq)?|at(int|ernions))|prime|fr)|Q(scr|opf|UOT|fr))|(R(s(h|cr)|ho|c(y|edil|aron)|Barr|ight(Ceiling|T(ee(Vector|Arrow)?|riangle(Bar|Equal)?)|Do(ubleBracket|wn(TeeVector|Vector(Bar)?))|Up(TeeVector|DownVector|Vector(Bar)?)|Vector(Bar)?|arrow|Floor|A(ngleBracket|rrow(Bar|LeftArrow)?))|o(undImplies|pf)|uleDelayed|e(verse(UpEquilibrium|E(quilibrium|lement)))?|fr|EG|a(ng|cute|rr(tl)?)|rightarrow)|r(s(h|cr|q(uo(r)?|b)|aquo)|h(o(v)?|ar(d|u(l)?))|nmid|c(y|ub|e(d??il)|aron)|Barr|t(hree|imes|ri([ef]|ltri)?)|i(singdotseq|ng|ght(squigarrow|harpoon(down|up)|threetimes|left(harpoons|arrows)|arrow(tail)?|rightarrows))|Har|o(times|p(f|lus|ar)|a(ng|rr)|brk)|d(sh|ca|quo(r)?|ldhar)|uluhar|p(polint|ar(gt)?)|e(ct|al(s|ine|part)?|g)|f(isht|loor|r)|l(har|arr|m)|a(ng([de]|le)?|c(ute|e)|t(io(nals)?|ail)|dic|emptyv|quo|rr(sim|hk|c|tl|pl|fs|w|lp|ap|b(fs)?)?)|rarr|x|moust(ache)?|b(arr|r(k(sl([du])|e)|ac([ek]))|brk)|A(tail|arr|rr)))|(s(s(cr|tarf|etmn|mile)|h(y|c(hcy|y)|ort(parallel|mid)|arp)|c(sim|y|n(sim|E|ap)|cue|irc|polint|e(dil)?|E|a(p|ron))?|t(ar(f)?|r(ns|aight(phi|epsilon)))|i(gma([fv])?|m(ne|dot|plus|e(q)?|l(E)?|rarr|g(E)?)?)|zlig|o(pf|ftcy|l(b(ar)?)?)|dot([be])?|u(ng|cc(sim|n(sim|eqq|approx)|curlyeq|eq|approx)?|p(s(im|u([bp])|et(neq(q)?|eq(q)?)?)|hs(ol|ub)|1|n([Ee])|2|d(sub|ot)|3|plus|e(dot)?|E|larr|mult)?|m|b(s(im|u([bp])|et(neq(q)?|eq(q)?)?)|n([Ee])|dot|plus|e(dot)?|E|rarr|mult)?)|pa(des(uit)?|r)|e(swar|ct|tm(n|inus)|ar(hk|r(ow)?)|xt|mi|Arr)|q(su(p(set(eq)?|e)?|b(set(eq)?|e)?)|c(up(s)?|ap(s)?)|u(f|ar([ef]))?)|fr(own)?|w(nwar|ar(hk|r(ow)?)|Arr)|larr|acute|rarr|m(t(e(s)?)?|i(d|le)|eparsl|a(shp|llsetminus))|bquo)|S(scr|hort((?:Right|Down|Up|Left)Arrow)|c(y|irc|edil|aron)?|tar|igma|H(cy|CHcy)|opf|u(c(hThat|ceeds(SlantEqual|Tilde|Equal)?)|p(set|erset(Equal)?)?|m|b(set(Equal)?)?)|OFTcy|q(uare(Su(perset(Equal)?|bset(Equal)?)|Intersection|Union)?|rt)|fr|acute|mallCircle))|(t(s(hcy|c([ry])|trok)|h(i(nsp|ck(sim|approx))|orn|e(ta(sym|v)?|re(4|fore))|k(sim|ap))|c(y|edil|aron)|i(nt|lde|mes(d|b(ar)?)?)|o(sa|p(cir|f(ork)?|bot)?|ea)|dot|prime|elrec|fr|w(ixt|ohead((?:lef|righ)tarrow))|a(u|rget)|r(i(sb|time|dot|plus|e|angle(down|q|left(eq)?|right(eq)?)?|minus)|pezium|ade)|brk)|T(s(cr|trok)|RADE|h(i((?:n|ck)Space)|e(ta|refore))|c(y|edil|aron)|S(H??cy)|ilde(Tilde|Equal|FullEqual)?|HORN|opf|fr|a([bu])|ripleDot))|(u(scr|h(ar([lr])|blk)|c(y|irc)|t(ilde|dot|ri(f)?)|Har|o(pf|gon)|d(har|arr|blac)|u(arr|ml)|p(si(h|lon)?|harpoon(left|right)|downarrow|uparrows|lus|arrow)|f(isht|r)|wangle|l(c(orn(er)?|rop)|tri)|a(cute|rr)|r(c(orn(er)?|rop)|tri|ing)|grave|m(l|acr)|br(cy|eve)|Arr)|U(scr|n(ion(Plus)?|der(B(ar|rac(e|ket))|Parenthesis))|c(y|irc)|tilde|o(pf|gon)|dblac|uml|p(si(lon)?|downarrow|Tee(Arrow)?|per((?:Righ|Lef)tArrow)|DownArrow|Equilibrium|arrow|Arrow(Bar|DownArrow)?)|fr|a(cute|rr(ocir)?)|ring|grave|macr|br(cy|eve)))|(v(s(cr|u(pn([Ee])|bn([Ee])))|nsu([bp])|cy|Bar(v)?|zigzag|opf|dash|prop|e(e(eq|bar)?|llip|r(t|bar))|Dash|fr|ltri|a(ngrt|r(s(igma|u(psetneq(q)?|bsetneq(q)?))|nothing|t(heta|riangle(left|right))|p(hi|i|ropto)|epsilon|kappa|r(ho)?))|rtri|Arr)|V(scr|cy|opf|dash(l)?|e(e|r(yThinSpace|t(ical(Bar|Separator|Tilde|Line))?|bar))|Dash|vdash|fr|bar))|(w(scr|circ|opf|p|e(ierp|d(ge(q)?|bar))|fr|r(eath)?)|W(scr|circ|opf|edge|fr))|(X(scr|i|opf|fr)|x(s(cr|qcup)|h([Aa]rr)|nis|c(irc|up|ap)|i|o(time|dot|p(f|lus))|dtri|u(tri|plus)|vee|fr|wedge|l([Aa]rr)|r([Aa]rr)|map))|(y(scr|c(y|irc)|icy|opf|u(cy|ml)|en|fr|ac(y|ute))|Y(scr|c(y|irc)|opf|uml|Icy|Ucy|fr|acute|Acy))|(z(scr|hcy|c(y|aron)|igrarr|opf|dot|e(ta|etrf)|fr|w(n?j)|acute)|Z(scr|c(y|aron)|Hcy|opf|dot|e(ta|roWidthSpace)|fr|acute)))(;)\",\"name\":\"constant.character.entity.named.$2.html\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.entity.html\"},\"3\":{\"name\":\"punctuation.definition.entity.html\"}},\"match\":\"(&)#[0-9]+(;)\",\"name\":\"constant.character.entity.numeric.decimal.html\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.entity.html\"},\"3\":{\"name\":\"punctuation.definition.entity.html\"}},\"match\":\"(&)#[Xx]\\\\h+(;)\",\"name\":\"constant.character.entity.numeric.hexadecimal.html\"},{\"match\":\"&(?=[0-9A-Za-z]+;)\",\"name\":\"invalid.illegal.ambiguous-ampersand.html\"}]},\"math\":{\"patterns\":[{\"begin\":\"(?i)(<)(math)(?=\\\\s|/?>)(?:(([^\\\"'>]|\\\"[^\\\"]*\\\"|'[^']*')*)(>))?\",\"beginCaptures\":{\"0\":{\"name\":\"meta.tag.structure.$2.start.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"patterns\":[{\"include\":\"#attribute\"}]},\"5\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"end\":\"(?i)(</)(\\\\2)\\\\s*(>)\",\"endCaptures\":{\"0\":{\"name\":\"meta.tag.structure.$2.end.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.element.structure.$2.html\",\"patterns\":[{\"begin\":\"(?<!>)\\\\G\",\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.structure.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"include\":\"#tags\"}]}],\"repository\":{\"attribute\":{\"patterns\":[{\"begin\":\"(s(hift|ymmetric|cript(sizemultiplier|level|minsize)|t(ackalign|retchy)|ide|u([bp]scriptshift)|e(parator(s)?|lection)|rc)|h(eight|ref)|n(otation|umalign)|c(haralign|olumn(spa(n|cing)|width|lines|align)|lose|rossout)|i(n(dent(shift(first|last)?|target|align(first|last)?)|fixlinebreakstyle)|d)|o(pen|verflow)|d(i(splay(style)?|r)|e(nomalign|cimalpoint|pth))|position|e(dge|qual(columns|rows))|voffset|f(orm|ence|rame(spacing)?)|width|l(space|ine(thickness|leading|break(style|multchar)?)|o(ngdivstyle|cation)|ength|quote|argeop)|a(c(cent(under)?|tiontype)|l(t(text|img(-(height|valign|width))?)|ign(mentscope)?))|r(space|ow(spa(n|cing)|lines|align)|quote)|groupalign|x(link:href|mlns)|m(in(size|labelspacing)|ovablelimits|a(th(size|color|variant|background)|xsize))|bevelled)(?![-:\\\\w])\",\"beginCaptures\":{\"0\":{\"name\":\"entity.other.attribute-name.html\"}},\"end\":\"(?=\\\\s*+[^=\\\\s])\",\"name\":\"meta.attribute.$1.html\",\"patterns\":[{\"include\":\"#attribute-interior\"}]},{\"begin\":\"([^\\\\x00- \\\"'/<=>\\\\x7F-\\\\x{9F}﷐-﷯￾￿🿾🿿𯿾𯿿𿿾𿿿\\\\x{4FFFE}\\\\x{4FFFF}\\\\x{5FFFE}\\\\x{5FFFF}\\\\x{6FFFE}\\\\x{6FFFF}\\\\x{7FFFE}\\\\x{7FFFF}\\\\x{8FFFE}\\\\x{8FFFF}\\\\x{9FFFE}\\\\x{9FFFF}\\\\x{AFFFE}\\\\x{AFFFF}\\\\x{BFFFE}\\\\x{BFFFF}\\\\x{CFFFE}\\\\x{CFFFF}\\\\x{DFFFE}\\\\x{DFFFF}\\\\x{EFFFE}\\\\x{EFFFF}\\\\x{FFFFE}\\\\x{FFFFF}\\\\x{10FFFE}\\\\x{10FFFF}]+)\",\"beginCaptures\":{\"0\":{\"name\":\"entity.other.attribute-name.html\"}},\"end\":\"(?=\\\\s*+[^=\\\\s])\",\"name\":\"meta.attribute.unrecognized.$1.html\",\"patterns\":[{\"include\":\"#attribute-interior\"}]},{\"match\":\"[^>\\\\s]+\",\"name\":\"invalid.illegal.character-not-allowed-here.html\"}]},\"tags\":{\"patterns\":[{\"include\":\"#comment\"},{\"include\":\"#cdata\"},{\"captures\":{\"0\":{\"name\":\"meta.tag.structure.math.$2.void.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"patterns\":[{\"include\":\"#attribute\"}]},\"5\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"match\":\"(?i)(<)(annotation|annotation-xml|semantics|menclose|merror|mfenced|mfrac|mpadded|mphantom|mroot|mrow|msqrt|mstyle|mmultiscripts|mover|mprescripts|msub|msubsup|msup|munder|munderover|none|mlabeledtr|mtable|mtd|mtr|mlongdiv|mscarries|mscarry|msgroup|msline|msrow|mstack|maction)(?=\\\\s|/?>)(([^\\\"'>]|\\\"[^\\\"]*\\\"|'[^']*')*)(/>)\",\"name\":\"meta.element.structure.math.$2.html\"},{\"begin\":\"(?i)(<)(annotation|annotation-xml|semantics|menclose|merror|mfenced|mfrac|mpadded|mphantom|mroot|mrow|msqrt|mstyle|mmultiscripts|mover|mprescripts|msub|msubsup|msup|munder|munderover|none|mlabeledtr|mtable|mtd|mtr|mlongdiv|mscarries|mscarry|msgroup|msline|msrow|mstack|maction)(?=\\\\s|/?>)(?:(([^\\\"'>]|\\\"[^\\\"]*\\\"|'[^']*')*)(>))?\",\"beginCaptures\":{\"0\":{\"name\":\"meta.tag.structure.math.$2.start.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"patterns\":[{\"include\":\"#attribute\"}]},\"5\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"end\":\"(?i)(</)(\\\\2)\\\\s*(>)|(/>)|(?=</\\\\w+)\",\"endCaptures\":{\"0\":{\"name\":\"meta.tag.structure.math.$2.end.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"punctuation.definition.tag.end.html\"},\"4\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.element.structure.math.$2.html\",\"patterns\":[{\"begin\":\"(?<!>)\\\\G\",\"end\":\"(?=/>)|>\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.structure.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"include\":\"#tags\"}]},{\"captures\":{\"0\":{\"name\":\"meta.tag.inline.math.$2.void.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"patterns\":[{\"include\":\"#attribute\"}]},\"5\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"match\":\"(?i)(<)(m(?:[inos]|space|text|aligngroup|alignmark))(?=\\\\s|/?>)(([^\\\"'>]|\\\"[^\\\"]*\\\"|'[^']*')*)(/>)\",\"name\":\"meta.element.inline.math.$2.html\"},{\"begin\":\"(?i)(<)(m(?:[inos]|space|text|aligngroup|alignmark))(?=\\\\s|/?>)(?:(([^\\\"'>]|\\\"[^\\\"]*\\\"|'[^']*')*)(>))?\",\"beginCaptures\":{\"0\":{\"name\":\"meta.tag.inline.math.$2.start.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"patterns\":[{\"include\":\"#attribute\"}]},\"5\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"end\":\"(?i)(</)(\\\\2)\\\\s*(>)|(/>)|(?=</\\\\w+)\",\"endCaptures\":{\"0\":{\"name\":\"meta.tag.inline.math.$2.end.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"punctuation.definition.tag.end.html\"},\"4\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.element.inline.math.$2.html\",\"patterns\":[{\"begin\":\"(?<!>)\\\\G\",\"end\":\"(?=/>)|>\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.inline.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"include\":\"#tags\"}]},{\"captures\":{\"0\":{\"name\":\"meta.tag.object.math.$2.void.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"patterns\":[{\"include\":\"#attribute\"}]},\"5\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"match\":\"(?i)(<)(mglyph)(?=\\\\s|/?>)(([^\\\"'>]|\\\"[^\\\"]*\\\"|'[^']*')*)(/>)\",\"name\":\"meta.element.object.math.$2.html\"},{\"begin\":\"(?i)(<)(mglyph)(?=\\\\s|/?>)(?:(([^\\\"'>]|\\\"[^\\\"]*\\\"|'[^']*')*)(>))?\",\"beginCaptures\":{\"0\":{\"name\":\"meta.tag.object.math.$2.start.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"patterns\":[{\"include\":\"#attribute\"}]},\"5\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"end\":\"(?i)(</)(\\\\2)\\\\s*(>)|(/>)|(?=</\\\\w+)\",\"endCaptures\":{\"0\":{\"name\":\"meta.tag.object.math.$2.end.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"punctuation.definition.tag.end.html\"},\"4\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.element.object.math.$2.html\",\"patterns\":[{\"begin\":\"(?<!>)\\\\G\",\"end\":\"(?=/>)|>\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.object.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"include\":\"#tags\"}]},{\"captures\":{\"0\":{\"name\":\"meta.tag.other.invalid.void.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"invalid.illegal.unrecognized-tag.html\"},\"4\":{\"patterns\":[{\"include\":\"#attribute\"}]},\"6\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"match\":\"(?i)(<)(([:\\\\w]+))(?=\\\\s|/?>)(([^\\\"'>]|\\\"[^\\\"]*\\\"|'[^']*')*)(/>)\",\"name\":\"meta.element.other.invalid.html\"},{\"begin\":\"(?i)(<)((\\\\w[^>\\\\s]*))(?=\\\\s|/?>)(?:(([^\\\"'>]|\\\"[^\\\"]*\\\"|'[^']*')*)(>))?\",\"beginCaptures\":{\"0\":{\"name\":\"meta.tag.other.invalid.start.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"invalid.illegal.unrecognized-tag.html\"},\"4\":{\"patterns\":[{\"include\":\"#attribute\"}]},\"6\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"end\":\"(?i)(</)((\\\\2))\\\\s*(>)|(/>)|(?=</\\\\w+)\",\"endCaptures\":{\"0\":{\"name\":\"meta.tag.other.invalid.end.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"invalid.illegal.unrecognized-tag.html\"},\"4\":{\"name\":\"punctuation.definition.tag.end.html\"},\"5\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.element.other.invalid.html\",\"patterns\":[{\"begin\":\"(?<!>)\\\\G\",\"end\":\"(?=/>)|>\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.other.invalid.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"include\":\"#tags\"}]},{\"include\":\"#tags-invalid\"}]}}},\"svg\":{\"patterns\":[{\"begin\":\"(?i)(<)(svg)(?=\\\\s|/?>)(?:(([^\\\"'>]|\\\"[^\\\"]*\\\"|'[^']*')*)(>))?\",\"beginCaptures\":{\"0\":{\"name\":\"meta.tag.structure.$2.start.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"patterns\":[{\"include\":\"#attribute\"}]},\"5\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"end\":\"(?i)(</)(\\\\2)\\\\s*(>)\",\"endCaptures\":{\"0\":{\"name\":\"meta.tag.structure.$2.end.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.element.structure.$2.html\",\"patterns\":[{\"begin\":\"(?<!>)\\\\G\",\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.structure.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"include\":\"#tags\"}]}],\"repository\":{\"attribute\":{\"patterns\":[{\"begin\":\"(s(hape-rendering|ystemLanguage|cale|t(yle|itchTiles|op-(color|opacity)|dDeviation|em([hv])|artOffset|r(i(ng|kethrough-(thickness|position))|oke(-(opacity|dash(offset|array)|width|line(cap|join)|miterlimit))?))|urfaceScale|p(e(cular(Constant|Exponent)|ed)|acing|readMethod)|eed|lope)|h(oriz-(origin-x|adv-x)|eight|anging|ref(lang)?)|y([12]|ChannelSelector)?|n(umOctaves|ame)|c(y|o(ntentS((?:cript|tyle)Type)|lor(-(interpolation(-filters)?|profile|rendering))?)|ursor|l(ip(-(path|rule)|PathUnits)?|ass)|a(p-height|lcMode)|x)|t(ype|o|ext(-(decoration|anchor|rendering)|Length)|a(rget([XY])?|b(index|leValues))|ransform)|i(n(tercept|2)?|d(eographic)?|mage-rendering)|z(oomAndPan)?|o(p(erator|acity)|ver(flow|line-(thickness|position))|ffset|r(i(ent(ation)?|gin)|der))|d(y|i(splay|visor|ffuseConstant|rection)|ominant-baseline|ur|e(scent|celerate)|x)?|u(1|n(i(code(-(range|bidi))?|ts-per-em)|derline-(thickness|position))|2)|p(ing|oint(s(At([XYZ]))?|er-events)|a(nose-1|t(h(Length)?|tern(ContentUnits|Transform|Units))|int-order)|r(imitiveUnits|eserveA(spectRatio|lpha)))|e(n(d|able-background)|dgeMode|levation|x(ternalResourcesRequired|ponent))|v(i(sibility|ew(Box|Target))|-(hanging|ideographic|alphabetic|mathematical)|e(ctor-effect|r(sion|t-(origin-([xy])|adv-y)))|alues)|k([123]|e(y(Splines|Times|Points)|rn(ing|el(Matrix|UnitLength)))|4)?|f(y|il(ter(Res|Units)?|l(-(opacity|rule))?)|o(nt-(s(t(yle|retch)|ize(-adjust)?)|variant|family|weight)|rmat)|lood-(color|opacity)|r(om)?|x)|w(idth(s)?|ord-spacing|riting-mode)|l(i(ghting-color|mitingConeAngle)|ocal|e(ngthAdjust|tter-spacing)|ang)|a(scent|cc(umulate|ent-height)|ttribute(Name|Type)|zimuth|dditive|utoReverse|l(ignment-baseline|phabetic|lowReorder)|rabic-form|mplitude)|r(y|otate|e(s(tart|ult)|ndering-intent|peat(Count|Dur)|quired(Extensions|Features)|f([XY]|errerPolicy)|l)|adius|x)?|g([12]|lyph(Ref|-(name|orientation-(horizontal|vertical)))|radient(Transform|Units))|x([12]|ChannelSelector|-height|link:(show|href|t(ype|itle)|a(ctuate|rcrole)|role)|ml:(space|lang|base))?|m(in|ode|e(thod|dia)|a(sk((?:Content|)Units)?|thematical|rker(Height|-(start|end|mid)|Units|Width)|x))|b(y|ias|egin|ase(Profile|line-shift|Frequency)|box))(?![-:\\\\w])\",\"beginCaptures\":{\"0\":{\"name\":\"entity.other.attribute-name.html\"}},\"end\":\"(?=\\\\s*+[^=\\\\s])\",\"name\":\"meta.attribute.$1.html\",\"patterns\":[{\"include\":\"#attribute-interior\"}]},{\"begin\":\"([^\\\\x00- \\\"'/<=>\\\\x7F-\\\\x{9F}﷐-﷯￾￿🿾🿿𯿾𯿿𿿾𿿿\\\\x{4FFFE}\\\\x{4FFFF}\\\\x{5FFFE}\\\\x{5FFFF}\\\\x{6FFFE}\\\\x{6FFFF}\\\\x{7FFFE}\\\\x{7FFFF}\\\\x{8FFFE}\\\\x{8FFFF}\\\\x{9FFFE}\\\\x{9FFFF}\\\\x{AFFFE}\\\\x{AFFFF}\\\\x{BFFFE}\\\\x{BFFFF}\\\\x{CFFFE}\\\\x{CFFFF}\\\\x{DFFFE}\\\\x{DFFFF}\\\\x{EFFFE}\\\\x{EFFFF}\\\\x{FFFFE}\\\\x{FFFFF}\\\\x{10FFFE}\\\\x{10FFFF}]+)\",\"beginCaptures\":{\"0\":{\"name\":\"entity.other.attribute-name.html\"}},\"end\":\"(?=\\\\s*+[^=\\\\s])\",\"name\":\"meta.attribute.unrecognized.$1.html\",\"patterns\":[{\"include\":\"#attribute-interior\"}]},{\"match\":\"[^>\\\\s]+\",\"name\":\"invalid.illegal.character-not-allowed-here.html\"}]},\"tags\":{\"patterns\":[{\"include\":\"#comment\"},{\"include\":\"#cdata\"},{\"captures\":{\"0\":{\"name\":\"meta.tag.metadata.svg.$2.void.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"patterns\":[{\"include\":\"#attribute\"}]},\"5\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"match\":\"(?i)(<)(color-profile|desc|metadata|script|style|title)(?=\\\\s|/?>)(([^\\\"'>]|\\\"[^\\\"]*\\\"|'[^']*')*)(/>)\",\"name\":\"meta.element.metadata.svg.$2.html\"},{\"begin\":\"(?i)(<)(color-profile|desc|metadata|script|style|title)(?=\\\\s|/?>)(?:(([^\\\"'>]|\\\"[^\\\"]*\\\"|'[^']*')*)(>))?\",\"beginCaptures\":{\"0\":{\"name\":\"meta.tag.metadata.svg.$2.start.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"patterns\":[{\"include\":\"#attribute\"}]},\"5\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"end\":\"(?i)(</)(\\\\2)\\\\s*(>)|(/>)|(?=</\\\\w+)\",\"endCaptures\":{\"0\":{\"name\":\"meta.tag.metadata.svg.$2.end.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"punctuation.definition.tag.end.html\"},\"4\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.element.metadata.svg.$2.html\",\"patterns\":[{\"begin\":\"(?<!>)\\\\G\",\"end\":\"(?=/>)|>\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.metadata.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"include\":\"#tags\"}]},{\"captures\":{\"0\":{\"name\":\"meta.tag.structure.svg.$2.void.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"patterns\":[{\"include\":\"#attribute\"}]},\"5\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"match\":\"(?i)(<)(animateMotion|clipPath|defs|feComponentTransfer|feDiffuseLighting|feMerge|feSpecularLighting|filter|g|hatch|linearGradient|marker|mask|mesh|meshgradient|meshpatch|meshrow|pattern|radialGradient|switch|text|textPath)(?=\\\\s|/?>)(([^\\\"'>]|\\\"[^\\\"]*\\\"|'[^']*')*)(/>)\",\"name\":\"meta.element.structure.svg.$2.html\"},{\"begin\":\"(?i)(<)(animateMotion|clipPath|defs|feComponentTransfer|feDiffuseLighting|feMerge|feSpecularLighting|filter|g|hatch|linearGradient|marker|mask|mesh|meshgradient|meshpatch|meshrow|pattern|radialGradient|switch|text|textPath)(?=\\\\s|/?>)(?:(([^\\\"'>]|\\\"[^\\\"]*\\\"|'[^']*')*)(>))?\",\"beginCaptures\":{\"0\":{\"name\":\"meta.tag.structure.svg.$2.start.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"patterns\":[{\"include\":\"#attribute\"}]},\"5\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"end\":\"(?i)(</)(\\\\2)\\\\s*(>)|(/>)|(?=</\\\\w+)\",\"endCaptures\":{\"0\":{\"name\":\"meta.tag.structure.svg.$2.end.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"punctuation.definition.tag.end.html\"},\"4\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.element.structure.svg.$2.html\",\"patterns\":[{\"begin\":\"(?<!>)\\\\G\",\"end\":\"(?=/>)|>\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.structure.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"include\":\"#tags\"}]},{\"captures\":{\"0\":{\"name\":\"meta.tag.inline.svg.$2.void.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"patterns\":[{\"include\":\"#attribute\"}]},\"5\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"match\":\"(?i)(<)(a|animate|discard|feBlend|feColorMatrix|feComposite|feConvolveMatrix|feDisplacementMap|feDistantLight|feDropShadow|feFlood|feFuncA|feFuncB|feFuncG|feFuncR|feGaussianBlur|feMergeNode|feMorphology|feOffset|fePointLight|feSpotLight|feTile|feTurbulence|hatchPath|mpath|set|solidcolor|stop|tspan)(?=\\\\s|/?>)(([^\\\"'>]|\\\"[^\\\"]*\\\"|'[^']*')*)(/>)\",\"name\":\"meta.element.inline.svg.$2.html\"},{\"begin\":\"(?i)(<)(a|animate|discard|feBlend|feColorMatrix|feComposite|feConvolveMatrix|feDisplacementMap|feDistantLight|feDropShadow|feFlood|feFuncA|feFuncB|feFuncG|feFuncR|feGaussianBlur|feMergeNode|feMorphology|feOffset|fePointLight|feSpotLight|feTile|feTurbulence|hatchPath|mpath|set|solidcolor|stop|tspan)(?=\\\\s|/?>)(?:(([^\\\"'>]|\\\"[^\\\"]*\\\"|'[^']*')*)(>))?\",\"beginCaptures\":{\"0\":{\"name\":\"meta.tag.inline.svg.$2.start.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"patterns\":[{\"include\":\"#attribute\"}]},\"5\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"end\":\"(?i)(</)(\\\\2)\\\\s*(>)|(/>)|(?=</\\\\w+)\",\"endCaptures\":{\"0\":{\"name\":\"meta.tag.inline.svg.$2.end.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"punctuation.definition.tag.end.html\"},\"4\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.element.inline.svg.$2.html\",\"patterns\":[{\"begin\":\"(?<!>)\\\\G\",\"end\":\"(?=/>)|>\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.inline.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"include\":\"#tags\"}]},{\"captures\":{\"0\":{\"name\":\"meta.tag.object.svg.$2.void.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"patterns\":[{\"include\":\"#attribute\"}]},\"5\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"match\":\"(?i)(<)(circle|ellipse|feImage|foreignObject|image|line|path|polygon|polyline|rect|symbol|use|view)(?=\\\\s|/?>)(([^\\\"'>]|\\\"[^\\\"]*\\\"|'[^']*')*)(/>)\",\"name\":\"meta.element.object.svg.$2.html\"},{\"begin\":\"(?i)(<)(a|circle|ellipse|feImage|foreignObject|image|line|path|polygon|polyline|rect|symbol|use|view)(?=\\\\s|/?>)(?:(([^\\\"'>]|\\\"[^\\\"]*\\\"|'[^']*')*)(>))?\",\"beginCaptures\":{\"0\":{\"name\":\"meta.tag.object.svg.$2.start.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"patterns\":[{\"include\":\"#attribute\"}]},\"5\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"end\":\"(?i)(</)(\\\\2)\\\\s*(>)|(/>)|(?=</\\\\w+)\",\"endCaptures\":{\"0\":{\"name\":\"meta.tag.object.svg.$2.end.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"punctuation.definition.tag.end.html\"},\"4\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.element.object.svg.$2.html\",\"patterns\":[{\"begin\":\"(?<!>)\\\\G\",\"end\":\"(?=/>)|>\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.object.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"include\":\"#tags\"}]},{\"captures\":{\"0\":{\"name\":\"meta.tag.other.svg.$2.void.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"invalid.deprecated.html\"},\"4\":{\"patterns\":[{\"include\":\"#attribute\"}]},\"6\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"match\":\"(?i)(<)((altGlyph|altGlyphDef|altGlyphItem|animateColor|animateTransform|cursor|font|font-face|font-face-format|font-face-name|font-face-src|font-face-uri|glyph|glyphRef|hkern|missing-glyph|tref|vkern))(?=\\\\s|/?>)(([^\\\"'>]|\\\"[^\\\"]*\\\"|'[^']*')*)(/>)\",\"name\":\"meta.element.other.svg.$2.html\"},{\"begin\":\"(?i)(<)((altGlyph|altGlyphDef|altGlyphItem|animateColor|animateTransform|cursor|font|font-face|font-face-format|font-face-name|font-face-src|font-face-uri|glyph|glyphRef|hkern|missing-glyph|tref|vkern))(?=\\\\s|/?>)(?:(([^\\\"'>]|\\\"[^\\\"]*\\\"|'[^']*')*)(>))?\",\"beginCaptures\":{\"0\":{\"name\":\"meta.tag.other.svg.$2.start.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"invalid.deprecated.html\"},\"4\":{\"patterns\":[{\"include\":\"#attribute\"}]},\"6\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"end\":\"(?i)(</)((\\\\2))\\\\s*(>)|(/>)|(?=</\\\\w+)\",\"endCaptures\":{\"0\":{\"name\":\"meta.tag.other.svg.$2.end.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"invalid.deprecated.html\"},\"4\":{\"name\":\"punctuation.definition.tag.end.html\"},\"5\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.element.other.svg.$2.html\",\"patterns\":[{\"begin\":\"(?<!>)\\\\G\",\"end\":\"(?=/>)|>\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.other.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"include\":\"#tags\"}]},{\"captures\":{\"0\":{\"name\":\"meta.tag.other.invalid.void.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"invalid.illegal.unrecognized-tag.html\"},\"4\":{\"patterns\":[{\"include\":\"#attribute\"}]},\"6\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"match\":\"(?i)(<)(([:\\\\w]+))(?=\\\\s|/?>)(([^\\\"'>]|\\\"[^\\\"]*\\\"|'[^']*')*)(/>)\",\"name\":\"meta.element.other.invalid.html\"},{\"begin\":\"(?i)(<)((\\\\w[^>\\\\s]*))(?=\\\\s|/?>)(?:(([^\\\"'>]|\\\"[^\\\"]*\\\"|'[^']*')*)(>))?\",\"beginCaptures\":{\"0\":{\"name\":\"meta.tag.other.invalid.start.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"invalid.illegal.unrecognized-tag.html\"},\"4\":{\"patterns\":[{\"include\":\"#attribute\"}]},\"6\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"end\":\"(?i)(</)((\\\\2))\\\\s*(>)|(/>)|(?=</\\\\w+)\",\"endCaptures\":{\"0\":{\"name\":\"meta.tag.other.invalid.end.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"invalid.illegal.unrecognized-tag.html\"},\"4\":{\"name\":\"punctuation.definition.tag.end.html\"},\"5\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.element.other.invalid.html\",\"patterns\":[{\"begin\":\"(?<!>)\\\\G\",\"end\":\"(?=/>)|>\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.other.invalid.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"include\":\"#tags\"}]},{\"include\":\"#tags-invalid\"}]}}},\"tags-invalid\":{\"patterns\":[{\"begin\":\"(</?)((\\\\w[^>\\\\s]*))(?<!/)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"invalid.illegal.unrecognized-tag.html\"}},\"end\":\"((?: ?/)?>)\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.other.$2.html\",\"patterns\":[{\"include\":\"#attribute\"}]}]},\"tags-valid\":{\"patterns\":[{\"begin\":\"(^[\\\\t ]+)?(?=<(?i:style)\\\\b(?!-))\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.whitespace.embedded.leading.html\"}},\"end\":\"(?!\\\\G)([\\\\t ]*$\\\\n?)?\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.whitespace.embedded.trailing.html\"}},\"patterns\":[{\"begin\":\"(?i)(<)(style)(?=\\\\s|/?>)\",\"beginCaptures\":{\"0\":{\"name\":\"meta.tag.metadata.style.start.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"}},\"end\":\"(?i)((<)/)(style)\\\\s*(>)\",\"endCaptures\":{\"0\":{\"name\":\"meta.tag.metadata.style.end.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"source.css-ignored-vscode\"},\"3\":{\"name\":\"entity.name.tag.html\"},\"4\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.embedded.block.html\",\"patterns\":[{\"begin\":\"\\\\G\",\"captures\":{\"1\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"end\":\"(>)\",\"name\":\"meta.tag.metadata.style.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?!\\\\G)\",\"end\":\"(?=</(?i:style))\",\"name\":\"source.css\",\"patterns\":[{\"include\":\"source.css\"}]}]}]},{\"begin\":\"(^[\\\\t ]+)?(?=<(?i:script)\\\\b(?!-))\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.whitespace.embedded.leading.html\"}},\"end\":\"(?!\\\\G)([\\\\t ]*$\\\\n?)?\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.whitespace.embedded.trailing.html\"}},\"patterns\":[{\"begin\":\"(<)((?i:script))\\\\b\",\"beginCaptures\":{\"0\":{\"name\":\"meta.tag.metadata.script.start.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"}},\"end\":\"(/)((?i:script))(>)\",\"endCaptures\":{\"0\":{\"name\":\"meta.tag.metadata.script.end.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.embedded.block.html\",\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(?=/)\",\"patterns\":[{\"begin\":\"(>)\",\"beginCaptures\":{\"0\":{\"name\":\"meta.tag.metadata.script.start.html\"},\"1\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"end\":\"((<))(?=/(?i:script))\",\"endCaptures\":{\"0\":{\"name\":\"meta.tag.metadata.script.end.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"source.js-ignored-vscode\"}},\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(?=</(?i:script))\",\"name\":\"source.js\",\"patterns\":[{\"begin\":\"(^[\\\\t ]+)?(?=//)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.whitespace.comment.leading.js\"}},\"end\":\"(?!\\\\G)\",\"patterns\":[{\"begin\":\"//\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.js\"}},\"end\":\"(?=</script)|\\\\n\",\"name\":\"comment.line.double-slash.js\"}]},{\"begin\":\"/\\\\*\",\"captures\":{\"0\":{\"name\":\"punctuation.definition.comment.js\"}},\"end\":\"\\\\*/|(?=</script)\",\"name\":\"comment.block.js\"},{\"include\":\"source.js\"}]}]},{\"begin\":\"\\\\G\",\"end\":\"(?i:(?=>|type(?=[=\\\\s])(?!\\\\s*=\\\\s*(''|\\\"\\\"|([\\\"']?)(text/(javascript(1\\\\.[0-5])?|x-javascript|jscript|livescript|(x-)?ecmascript|babel)|application/((?:(x-)?jav|(x-)?ecm)ascript)|module)[\\\"'>\\\\s]))))\",\"name\":\"meta.tag.metadata.script.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?i:(?=type\\\\s*=\\\\s*([\\\"']?)text/(x-handlebars|(x-(handlebars-)?|ng-)?template|html)[\\\"'>\\\\s]))\",\"end\":\"((<))(?=/(?i:script))\",\"endCaptures\":{\"0\":{\"name\":\"meta.tag.metadata.script.end.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"text.html.basic\"}},\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(>)\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.metadata.script.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?!\\\\G)\",\"end\":\"(?=</(?i:script))\",\"name\":\"text.html.basic\",\"patterns\":[{\"include\":\"text.html.basic\"}]}]},{\"begin\":\"(?=(?i:type))\",\"end\":\"(<)(?=/(?i:script))\",\"endCaptures\":{\"0\":{\"name\":\"meta.tag.metadata.script.end.html\"},\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"}},\"patterns\":[{\"begin\":\"\\\\G\",\"end\":\"(>)\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.metadata.script.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?!\\\\G)\",\"end\":\"(?=</(?i:script))\",\"name\":\"source.unknown\"}]}]}]}]},{\"begin\":\"(?i)(<)(base|link|meta)(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"}},\"end\":\"/?>\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.metadata.$2.void.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?i)(<)(noscript|title)(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.metadata.$2.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?i)(</)(noscript|title)(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.metadata.$2.end.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?i)(<)(col|hr|input)(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"}},\"end\":\"/?>\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.structure.$2.void.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?i)(<)(address|article|aside|blockquote|body|button|caption|colgroup|datalist|dd|details|dialog|div|dl|dt|fieldset|figcaption|figure|footer|form|head|header|hgroup|html|h[1-6]|label|legend|li|main|map|menu|meter|nav|ol|optgroup|option|output|p|pre|progress|section|select|slot|summary|table|tbody|td|template|textarea|tfoot|th|thead|tr|ul)(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.structure.$2.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?i)(</)(address|article|aside|blockquote|body|button|caption|colgroup|datalist|dd|details|dialog|div|dl|dt|fieldset|figcaption|figure|footer|form|head|header|hgroup|html|h[1-6]|label|legend|li|main|map|menu|meter|nav|ol|optgroup|option|output|p|pre|progress|section|select|slot|summary|table|tbody|td|template|textarea|tfoot|th|thead|tr|ul)(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.structure.$2.end.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?i)(<)(area|br|wbr)(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"}},\"end\":\"/?>\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.inline.$2.void.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?i)(<)(a|abbr|b|bdi|bdo|cite|code|data|del|dfn|em|i|ins|kbd|mark|q|rp|rt|ruby|s|samp|small|span|strong|sub|sup|time|u|var)(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.inline.$2.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?i)(</)(a|abbr|b|bdi|bdo|cite|code|data|del|dfn|em|i|ins|kbd|mark|q|rp|rt|ruby|s|samp|small|span|strong|sub|sup|time|u|var)(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.inline.$2.end.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?i)(<)(embed|img|param|source|track)(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"}},\"end\":\"/?>\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.object.$2.void.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?i)(<)(audio|canvas|iframe|object|picture|video)(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.object.$2.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?i)(</)(audio|canvas|iframe|object|picture|video)(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.object.$2.end.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?i)(<)((basefont|isindex))(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"invalid.deprecated.html\"}},\"end\":\"/?>\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.metadata.$2.void.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?i)(<)((center|frameset|noembed|noframes))(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"invalid.deprecated.html\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.structure.$2.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?i)(</)((center|frameset|noembed|noframes))(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"invalid.deprecated.html\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.structure.$2.end.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?i)(<)((acronym|big|blink|font|strike|tt|xmp))(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"invalid.deprecated.html\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.inline.$2.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?i)(</)((acronym|big|blink|font|strike|tt|xmp))(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"invalid.deprecated.html\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.inline.$2.end.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?i)(<)((frame))(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"invalid.deprecated.html\"}},\"end\":\"/?>\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.object.$2.void.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?i)(<)((applet))(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"invalid.deprecated.html\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.object.$2.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?i)(</)((applet))(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"invalid.deprecated.html\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.object.$2.end.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?i)(<)((dir|keygen|listing|menuitem|plaintext|spacer))(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"invalid.illegal.no-longer-supported.html\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.other.$2.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(?i)(</)((dir|keygen|listing|menuitem|plaintext|spacer))(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"},\"3\":{\"name\":\"invalid.illegal.no-longer-supported.html\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.other.$2.end.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"include\":\"#math\"},{\"include\":\"#svg\"},{\"begin\":\"(<)([A-Za-z][.0-9A-Z_a-z·À-ÖØ-öø-ͽͿ-῿‌‍‿⁀⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�𐀀-\\\\x{EFFFF}]*-[-.0-9A-Z_a-z·À-ÖØ-öø-ͽͿ-῿‌‍‿⁀⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�𐀀-\\\\x{EFFFF}]*)(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"}},\"end\":\"/?>\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.custom.start.html\",\"patterns\":[{\"include\":\"#attribute\"}]},{\"begin\":\"(</)([A-Za-z][.0-9A-Z_a-z·À-ÖØ-öø-ͽͿ-῿‌‍‿⁀⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�𐀀-\\\\x{EFFFF}]*-[-.0-9A-Z_a-z·À-ÖØ-öø-ͽͿ-῿‌‍‿⁀⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�𐀀-\\\\x{EFFFF}]*)(?=\\\\s|/?>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.begin.html\"},\"2\":{\"name\":\"entity.name.tag.html\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.tag.end.html\"}},\"name\":\"meta.tag.custom.end.html\",\"patterns\":[{\"include\":\"#attribute\"}]}]},\"xml-processing\":{\"begin\":\"(<\\\\?)(xml)\",\"captures\":{\"1\":{\"name\":\"punctuation.definition.tag.html\"},\"2\":{\"name\":\"entity.name.tag.html\"}},\"end\":\"(\\\\?>)\",\"name\":\"meta.tag.metadata.processing.xml.html\",\"patterns\":[{\"include\":\"#attribute\"}]}},\"scopeName\":\"text.html.basic\",\"embeddedLangs\":[\"javascript\",\"css\"]}"));
const __TURBOPACK__default__export__ = [
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$javascript$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$css$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    lang
];
}}),
"[project]/node_modules/@shikijs/langs/dist/haml.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$javascript$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/javascript.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$css$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/css.mjs [app-client] (ecmascript)");
;
;
const lang = Object.freeze(JSON.parse("{\"displayName\":\"Ruby Haml\",\"fileTypes\":[\"haml\",\"html.haml\"],\"foldingStartMarker\":\"^\\\\s*([-#%.:=\\\\w].*)\\\\s$\",\"foldingStopMarker\":\"^\\\\s*$\",\"name\":\"haml\",\"patterns\":[{\"begin\":\"^(\\\\s*)==\",\"contentName\":\"string.quoted.double.ruby\",\"end\":\"$\\\\n*\",\"patterns\":[{\"include\":\"#interpolated_ruby\"}]},{\"begin\":\"^(\\\\s*):ruby\",\"end\":\"^(?!\\\\1\\\\s+|$\\\\n*)\",\"name\":\"source.ruby.embedded.filter.haml\",\"patterns\":[{\"include\":\"source.ruby\"}]},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.prolog.haml\"}},\"match\":\"^(!!!)($|\\\\s.*)\",\"name\":\"meta.prolog.haml\"},{\"begin\":\"^(\\\\s*):javascript\",\"end\":\"^(?!\\\\1\\\\s+|$\\\\n*)\",\"name\":\"js.haml\",\"patterns\":[{\"include\":\"source.js\"}]},{\"begin\":\"^(\\\\s*)%script\",\"end\":\"^(?!\\\\1\\\\s+|$\\\\n*)\",\"name\":\"js.inline.haml\",\"patterns\":[{\"include\":\"source.js\"}]},{\"begin\":\"^(\\\\s*):ruby$\",\"end\":\"^(?!\\\\1\\\\s+|$\\\\n*)\",\"name\":\"source.ruby.embedded.filter.haml\",\"patterns\":[{\"include\":\"source.ruby\"}]},{\"captures\":{\"1\":{\"name\":\"punctuation.section.comment.haml\"}},\"match\":\"^(\\\\s*)(/\\\\[[^]].*?$\\\\n?)\",\"name\":\"comment.line.slash.haml\"},{\"begin\":\"^(\\\\s*)(-#|/|-\\\\s*/\\\\*+)\",\"beginCaptures\":{\"2\":{\"name\":\"punctuation.section.comment.haml\"}},\"end\":\"^(?!\\\\1\\\\s+|\\\\n)\",\"name\":\"comment.block.haml\",\"patterns\":[{\"include\":\"text.haml\"}]},{\"begin\":\"^\\\\s*(?:((%)([-:\\\\w]+))|(?=[#.]))\",\"captures\":{\"1\":{\"name\":\"meta.tag.haml\"},\"2\":{\"name\":\"punctuation.definition.tag.haml\"},\"3\":{\"name\":\"entity.name.tag.haml\"}},\"end\":\"$|(?![#(.\\\\[{]|&amp;|[-=~]|!=|&=|/)\",\"patterns\":[{\"begin\":\"==\",\"contentName\":\"string.quoted.double.ruby\",\"end\":\"$\\\\n?\",\"patterns\":[{\"include\":\"#interpolated_ruby\"}]},{\"captures\":{\"1\":{\"name\":\"entity.other.attribute-name.class\"}},\"match\":\"(\\\\.[-:\\\\w]+)\",\"name\":\"meta.selector.css\"},{\"captures\":{\"1\":{\"name\":\"entity.other.attribute-name.id\"}},\"match\":\"(#[-\\\\w]+)\",\"name\":\"meta.selector.css\"},{\"begin\":\"(?<!#)\\\\{(?=.*(,|(do)|[{|}]|(#.*)|\\\\R)\\\\s*)\",\"end\":\"\\\\s*}(?!\\\\s*,)(?!\\\\s*\\\\|)(?!#\\\\{.*})\",\"name\":\"meta.section.attributes.haml\",\"patterns\":[{\"include\":\"source.ruby\"},{\"include\":\"#continuation\"},{\"include\":\"#rubyline\"}]},{\"begin\":\"\\\\(\",\"end\":\"\\\\)\",\"name\":\"meta.section.attributes.plain.haml\",\"patterns\":[{\"match\":\"([-\\\\w]+)\",\"name\":\"constant.other.symbol.ruby\"},{\"match\":\"=\",\"name\":\"punctuation\"},{\"include\":\"#variables\"},{\"begin\":\"\\\"\",\"end\":\"\\\"\",\"name\":\"string.quoted.double.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\(x\\\\h{2}|[012][0-7]{0,2}|3[0-6][0-7]?|37[0-7]?|[4-7][0-7]?|.)\",\"name\":\"constant.character.escape.ruby\"},{\"include\":\"#interpolated_ruby\"}]},{\"include\":\"#interpolated_ruby\"}]},{\"begin\":\"\\\\[(?=.+([],\\\\[|]|(#.*))\\\\s*)\",\"end\":\"\\\\s*](?!.*(?!#\\\\[)])\",\"name\":\"meta.section.object.haml\",\"patterns\":[{\"include\":\"source.ruby\"},{\"include\":\"#continuation\"},{\"include\":\"#rubyline\"}]},{\"include\":\"#interpolated_ruby_line\"},{\"include\":\"#rubyline\"},{\"match\":\"/\",\"name\":\"punctuation.terminator.tag.haml\"}]},{\"begin\":\"^(\\\\s*):(ruby|opal)$\",\"end\":\"^(?!\\\\1\\\\s+|$\\\\n*)\",\"name\":\"source.ruby.embedded.filter.haml\",\"patterns\":[{\"include\":\"source.ruby\"}]},{\"begin\":\"^(\\\\s*):ruby$\",\"end\":\"^(?!\\\\1\\\\s+|$\\\\n*)\",\"name\":\"source.ruby.embedded.filter.haml\",\"patterns\":[{\"include\":\"source.ruby\"}]},{\"begin\":\"^(\\\\s*):(s(?:tyle|ass))$\",\"end\":\"^(?=\\\\1\\\\s+|$\\\\n*)\",\"name\":\"source.sass.embedded.filter.haml\",\"patterns\":[{\"include\":\"source.sass\"}]},{\"begin\":\"^(\\\\s*):coffee(script)?\",\"end\":\"^(?!\\\\1\\\\s+|$\\\\n*)\",\"name\":\"source.coffee.embedded.filter.haml\",\"patterns\":[{\"include\":\"source.coffee\"}]},{\"begin\":\"^(\\\\s*):plain$\",\"end\":\"^(?=\\\\1\\\\s+|$\\\\n*)\",\"name\":\"text.plain.embedded.filter.haml\",\"patterns\":[{\"include\":\"text.plain\"}]},{\"begin\":\"^(\\\\s*)(:ruby)\",\"beginCaptures\":{\"2\":{\"name\":\"keyword.control.filter.haml\"}},\"end\":\"(?m:(?<=\\\\n)(?!\\\\1\\\\s+|$\\\\n*))\",\"name\":\"source.ruby.embedded.filter.haml\",\"patterns\":[{\"include\":\"source.ruby\"}]},{\"begin\":\"^(\\\\s*)(:sass)\",\"beginCaptures\":{\"2\":{\"name\":\"keyword.control.filter.haml\"}},\"end\":\"^(?!\\\\1\\\\s+|$\\\\n*)\",\"name\":\"source.embedded.filter.sass\",\"patterns\":[{\"include\":\"source.sass\"}]},{\"begin\":\"^(\\\\s*):(s(?:tyles|ass))$\",\"end\":\"^(?=\\\\1\\\\s+|$\\\\n*)\",\"name\":\"source.sass.embedded.filter.haml\",\"patterns\":[{\"include\":\"source.sass\"}]},{\"begin\":\"^(\\\\s*):plain$\",\"end\":\"^(?=\\\\1\\\\s+|$\\\\n*)\",\"name\":\"text.plain.embedded.filter.haml\",\"patterns\":[{\"include\":\"text.plain\"}]},{\"captures\":{\"1\":{\"name\":\"meta.escape.haml\"}},\"match\":\"^\\\\s*(\\\\.)\"},{\"begin\":\"^\\\\s*(?=[-=~]|!=|&=)\",\"end\":\"$\",\"patterns\":[{\"include\":\"#interpolated_ruby_line\"},{\"include\":\"#rubyline\"}]},{\"begin\":\"^(\\\\s*)(:php)\",\"captures\":{\"2\":{\"name\":\"entity.name.tag.haml\"}},\"end\":\"^(?!\\\\1\\\\s+|$\\\\n*)\",\"name\":\"meta.embedded.php\",\"patterns\":[{\"include\":\"text.html.php#language\"}]},{\"begin\":\"^(\\\\s*)(:markdown)\",\"captures\":{\"2\":{\"name\":\"entity.name.tag.haml\"}},\"end\":\"^(?!\\\\1\\\\s+|$\\\\n*)\",\"name\":\"meta.embedded.markdown\",\"patterns\":[{\"include\":\"text.html.markdown\"}]},{\"begin\":\"^(\\\\s*)(:(css|styles?))$\",\"captures\":{\"2\":{\"name\":\"entity.name.tag.haml\"}},\"end\":\"^(?!\\\\1\\\\s+|$\\\\n*)\",\"name\":\"meta.embedded.css\",\"patterns\":[{\"include\":\"source.css\"}]},{\"begin\":\"^(\\\\s*)(:sass)$\",\"captures\":{\"2\":{\"name\":\"entity.name.tag.haml\"}},\"end\":\"^(?!\\\\1\\\\s+|$\\\\n*)\",\"name\":\"meta.embedded.sass\",\"patterns\":[{\"include\":\"source.sass\"}]},{\"begin\":\"^(\\\\s*)(:scss)$\",\"captures\":{\"2\":{\"name\":\"entity.name.tag.haml\"}},\"end\":\"^(?!\\\\1\\\\s+|$\\\\n*)\",\"name\":\"meta.embedded.scss\",\"patterns\":[{\"include\":\"source.scss\"}]}],\"repository\":{\"continuation\":{\"captures\":{\"1\":{\"name\":\"punctuation.separator.continuation.haml\"}},\"match\":\"(\\\\|)\\\\s*\\\\n\"},\"interpolated_ruby\":{\"patterns\":[{\"captures\":{\"0\":{\"name\":\"punctuation.section.embedded.ruby\"},\"1\":{\"name\":\"source.ruby.embedded.source.empty\"}},\"match\":\"#\\\\{(})\",\"name\":\"source.ruby.embedded.source\"},{\"begin\":\"#\\\\{\",\"captures\":{\"0\":{\"name\":\"punctuation.section.embedded.ruby\"}},\"end\":\"(})\",\"name\":\"source.ruby.embedded.source\",\"patterns\":[{\"include\":\"#nest_curly_and_self\"},{\"include\":\"source.ruby\"}]},{\"include\":\"#variables\"}]},\"interpolated_ruby_line\":{\"begin\":\"!?==\",\"contentName\":\"string.source.ruby.embedded.haml\",\"end\":\"$\",\"name\":\"meta.line.ruby.interpolated.haml\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"source.ruby#escaped_char\"}]},\"nest_curly_and_self\":{\"patterns\":[{\"begin\":\"\\\\{\",\"captures\":{\"0\":{\"name\":\"punctuation.section.scope.ruby\"}},\"end\":\"}\",\"patterns\":[{\"include\":\"#nest_curly_and_self\"},{\"include\":\"source.ruby\"}]}]},\"rubyline\":{\"begin\":\"(&amp|!)?([-=~])\",\"contentName\":\"source.ruby.embedded.haml\",\"end\":\"((do|\\\\{)( \\\\|[*.]+\\\\|)?)$|$|^(?!.*\\\\|\\\\s*)$\\\\n?\",\"endCaptures\":{\"1\":{\"name\":\"source.ruby.embedded.html\"},\"2\":{\"name\":\"keyword.control.ruby.start-block\"}},\"name\":\"meta.line.ruby.haml\",\"patterns\":[{\"captures\":{\"1\":{\"name\":\"keyword.control.php\"}},\"match\":\"\\\\s+((elseif|foreach|switch|declare|default|use))(?=[(\\\\s])\"},{\"captures\":{\"1\":{\"name\":\"keyword.control.import.include.php\"}},\"match\":\"\\\\s+((?:requir|includ)e_once)(?=[(\\\\s])\"},{\"match\":\"\\\\s+(catch|try|throw|exception|finally|die)(?=[(\\\\s]|\\\\n*)\",\"name\":\"keyword.control.exception.php\"},{\"captures\":{\"1\":{\"name\":\"storage.type.function.php\"}},\"match\":\"\\\\s+(function\\\\s*)((?=\\\\())\"},{\"captures\":{\"1\":{\"name\":\"keyword.control.php\"}},\"match\":\"\\\\s+(use\\\\s*)((?=\\\\())\"},{\"match\":\"([,<|]|do|\\\\{)\\\\s*(#.*)?$\\\\n*\",\"name\":\"source.ruby\",\"patterns\":[{\"include\":\"#rubyline\"}]},{\"match\":\"#.*$\",\"name\":\"comment.line.number-sign.ruby\"},{\"include\":\"source.ruby\"},{\"include\":\"#continuation\"}]},\"variables\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"punctuation.definition.variable.ruby\"}},\"match\":\"(#@)[A-Z_a-z]\\\\w*\",\"name\":\"variable.other.readwrite.instance.ruby\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.variable.ruby\"}},\"match\":\"(#@@)[A-Z_a-z]\\\\w*\",\"name\":\"variable.other.readwrite.class.ruby\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.variable.ruby\"}},\"match\":\"(#\\\\$)[A-Z_a-z]\\\\w*\",\"name\":\"variable.other.readwrite.global.ruby\"}]}},\"scopeName\":\"text.haml\",\"embeddedLangs\":[\"javascript\",\"css\"],\"embeddedLangsLazy\":[\"ruby\",\"sass\",\"coffee\",\"markdown\"]}"));
const __TURBOPACK__default__export__ = [
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$javascript$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$css$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    lang
];
}}),
"[project]/node_modules/@shikijs/langs/dist/java.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const lang = Object.freeze(JSON.parse("{\"displayName\":\"Java\",\"name\":\"java\",\"patterns\":[{\"begin\":\"\\\\b(package)\\\\b\\\\s*\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.other.package.java\"}},\"contentName\":\"storage.modifier.package.java\",\"end\":\"\\\\s*(;)\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.terminator.java\"}},\"name\":\"meta.package.java\",\"patterns\":[{\"include\":\"#comments\"},{\"match\":\"(?<=\\\\.)\\\\s*\\\\.|\\\\.(?=\\\\s*;)\",\"name\":\"invalid.illegal.character_not_allowed_here.java\"},{\"match\":\"(?<!_)_(?=\\\\s*([.;]))|\\\\b\\\\d+|-+\",\"name\":\"invalid.illegal.character_not_allowed_here.java\"},{\"match\":\"[A-Z]+\",\"name\":\"invalid.deprecated.package_name_not_lowercase.java\"},{\"match\":\"\\\\b(?<!\\\\$)(abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|native|new|non-sealed|package|permits|private|protected|public|return|sealed|short|static|strictfp|super|switch|syncronized|this|throws??|transient|try|void|volatile|while|yield|true|false|null)\\\\b\",\"name\":\"invalid.illegal.character_not_allowed_here.java\"},{\"match\":\"\\\\.\",\"name\":\"punctuation.separator.java\"}]},{\"begin\":\"\\\\b(import)\\\\b\\\\s*\\\\b(static)?\\\\b\\\\s\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.other.import.java\"},\"2\":{\"name\":\"storage.modifier.java\"}},\"contentName\":\"storage.modifier.import.java\",\"end\":\"\\\\s*(;)\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.terminator.java\"}},\"name\":\"meta.import.java\",\"patterns\":[{\"include\":\"#comments\"},{\"match\":\"(?<=\\\\.)\\\\s*\\\\.|\\\\.(?=\\\\s*;)\",\"name\":\"invalid.illegal.character_not_allowed_here.java\"},{\"match\":\"(?<!\\\\.)\\\\s*\\\\*\",\"name\":\"invalid.illegal.character_not_allowed_here.java\"},{\"match\":\"(?<!_)_(?=\\\\s*([.;]))|\\\\b\\\\d+|-+\",\"name\":\"invalid.illegal.character_not_allowed_here.java\"},{\"match\":\"\\\\b(?<!\\\\$)(abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|native|new|non-sealed|package|permits|private|protected|public|return|sealed|short|static|strictfp|super|switch|syncronized|this|throws??|transient|try|void|volatile|while|yield|true|false|null)\\\\b\",\"name\":\"invalid.illegal.character_not_allowed_here.java\"},{\"match\":\"\\\\.\",\"name\":\"punctuation.separator.java\"},{\"match\":\"\\\\*\",\"name\":\"variable.language.wildcard.java\"}]},{\"include\":\"#comments-javadoc\"},{\"include\":\"#code\"},{\"include\":\"#module\"}],\"repository\":{\"all-types\":{\"patterns\":[{\"include\":\"#primitive-arrays\"},{\"include\":\"#primitive-types\"},{\"include\":\"#object-types\"}]},\"annotations\":{\"patterns\":[{\"begin\":\"((@)\\\\s*([^(\\\\s]+))(\\\\()\",\"beginCaptures\":{\"2\":{\"name\":\"punctuation.definition.annotation.java\"},\"3\":{\"name\":\"storage.type.annotation.java\"},\"4\":{\"name\":\"punctuation.definition.annotation-arguments.begin.bracket.round.java\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.annotation-arguments.end.bracket.round.java\"}},\"name\":\"meta.declaration.annotation.java\",\"patterns\":[{\"captures\":{\"1\":{\"name\":\"constant.other.key.java\"},\"2\":{\"name\":\"keyword.operator.assignment.java\"}},\"match\":\"(\\\\w*)\\\\s*(=)\"},{\"include\":\"#code\"}]},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.annotation.java\"},\"2\":{\"name\":\"storage.modifier.java\"},\"3\":{\"name\":\"storage.type.annotation.java\"},\"5\":{\"name\":\"punctuation.definition.annotation.java\"},\"6\":{\"name\":\"storage.type.annotation.java\"}},\"match\":\"(@)(interface)\\\\s+(\\\\w*)|((@)\\\\s*(\\\\w+))\",\"name\":\"meta.declaration.annotation.java\"}]},\"anonymous-block-and-instance-initializer\":{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.block.begin.bracket.curly.java\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.block.end.bracket.curly.java\"}},\"patterns\":[{\"include\":\"#code\"}]},\"anonymous-classes-and-new\":{\"begin\":\"\\\\bnew\\\\b\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.control.new.java\"}},\"end\":\"(?=[])-.:;?}]|/(?![*/])|[!%\\\\&=^|])\",\"patterns\":[{\"include\":\"#comments\"},{\"include\":\"#function-call\"},{\"include\":\"#all-types\"},{\"begin\":\"(?<=\\\\))\",\"end\":\"(?=[])-.:;?}]|/(?![*/])|[!%\\\\&=^|])\",\"patterns\":[{\"include\":\"#comments\"},{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.inner-class.begin.bracket.curly.java\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.inner-class.end.bracket.curly.java\"}},\"name\":\"meta.inner-class.java\",\"patterns\":[{\"include\":\"#class-body\"}]}]},{\"begin\":\"(?<=])\",\"end\":\"(?=[])-.:;?}]|/(?![*/])|[!%\\\\&=^|])\",\"patterns\":[{\"include\":\"#comments\"},{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.array-initializer.begin.bracket.curly.java\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.array-initializer.end.bracket.curly.java\"}},\"name\":\"meta.array-initializer.java\",\"patterns\":[{\"include\":\"#code\"}]}]},{\"include\":\"#parens\"}]},\"assertions\":{\"patterns\":[{\"begin\":\"\\\\b(assert)\\\\s\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.assert.java\"}},\"end\":\"$\",\"name\":\"meta.declaration.assertion.java\",\"patterns\":[{\"match\":\":\",\"name\":\"keyword.operator.assert.expression-separator.java\"},{\"include\":\"#code\"}]}]},\"class\":{\"begin\":\"(?=\\\\w?[-\\\\w\\\\s]*\\\\b(?:class|(?<!@)interface|enum)\\\\s+[$\\\\w]+)\",\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.class.end.bracket.curly.java\"}},\"name\":\"meta.class.java\",\"patterns\":[{\"include\":\"#storage-modifiers\"},{\"include\":\"#generics\"},{\"include\":\"#comments\"},{\"captures\":{\"1\":{\"name\":\"storage.modifier.java\"},\"2\":{\"name\":\"entity.name.type.class.java\"}},\"match\":\"(class|(?<!@)interface|enum)\\\\s+([$\\\\w]+)\",\"name\":\"meta.class.identifier.java\"},{\"begin\":\"extends\",\"beginCaptures\":{\"0\":{\"name\":\"storage.modifier.extends.java\"}},\"end\":\"(?=\\\\{|implements|permits)\",\"name\":\"meta.definition.class.inherited.classes.java\",\"patterns\":[{\"include\":\"#object-types-inherited\"},{\"include\":\"#comments\"}]},{\"begin\":\"(implements)\\\\s\",\"beginCaptures\":{\"1\":{\"name\":\"storage.modifier.implements.java\"}},\"end\":\"(?=\\\\s*extends|permits|\\\\{)\",\"name\":\"meta.definition.class.implemented.interfaces.java\",\"patterns\":[{\"include\":\"#object-types-inherited\"},{\"include\":\"#comments\"}]},{\"begin\":\"(permits)\\\\s\",\"beginCaptures\":{\"1\":{\"name\":\"storage.modifier.permits.java\"}},\"end\":\"(?=\\\\s*extends|implements|\\\\{)\",\"name\":\"meta.definition.class.permits.classes.java\",\"patterns\":[{\"include\":\"#object-types-inherited\"},{\"include\":\"#comments\"}]},{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.class.begin.bracket.curly.java\"}},\"contentName\":\"meta.class.body.java\",\"end\":\"(?=})\",\"patterns\":[{\"include\":\"#class-body\"}]}]},\"class-body\":{\"patterns\":[{\"include\":\"#comments-javadoc\"},{\"include\":\"#comments\"},{\"include\":\"#enums\"},{\"include\":\"#class\"},{\"include\":\"#generics\"},{\"include\":\"#static-initializer\"},{\"include\":\"#class-fields-and-methods\"},{\"include\":\"#annotations\"},{\"include\":\"#storage-modifiers\"},{\"include\":\"#member-variables\"},{\"include\":\"#code\"}]},\"class-fields-and-methods\":{\"patterns\":[{\"begin\":\"(?==)\",\"end\":\"(?=;)\",\"patterns\":[{\"include\":\"#code\"}]},{\"include\":\"#methods\"}]},\"code\":{\"patterns\":[{\"include\":\"#annotations\"},{\"include\":\"#comments\"},{\"include\":\"#enums\"},{\"include\":\"#class\"},{\"include\":\"#record\"},{\"include\":\"#anonymous-block-and-instance-initializer\"},{\"include\":\"#try-catch-finally\"},{\"include\":\"#assertions\"},{\"include\":\"#parens\"},{\"include\":\"#constants-and-special-vars\"},{\"include\":\"#numbers\"},{\"include\":\"#anonymous-classes-and-new\"},{\"include\":\"#lambda-expression\"},{\"include\":\"#keywords\"},{\"include\":\"#storage-modifiers\"},{\"include\":\"#method-call\"},{\"include\":\"#function-call\"},{\"include\":\"#variables\"},{\"include\":\"#variables-local\"},{\"include\":\"#objects\"},{\"include\":\"#properties\"},{\"include\":\"#strings\"},{\"include\":\"#all-types\"},{\"match\":\",\",\"name\":\"punctuation.separator.delimiter.java\"},{\"match\":\"\\\\.\",\"name\":\"punctuation.separator.period.java\"},{\"match\":\";\",\"name\":\"punctuation.terminator.java\"}]},\"comments\":{\"patterns\":[{\"captures\":{\"0\":{\"name\":\"punctuation.definition.comment.java\"}},\"match\":\"/\\\\*\\\\*/\",\"name\":\"comment.block.empty.java\"},{\"include\":\"#comments-inline\"}]},\"comments-inline\":{\"patterns\":[{\"begin\":\"/\\\\*\",\"captures\":{\"0\":{\"name\":\"punctuation.definition.comment.java\"}},\"end\":\"\\\\*/\",\"name\":\"comment.block.java\"},{\"begin\":\"(^[\\\\t ]+)?(?=//)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.whitespace.comment.leading.java\"}},\"end\":\"(?!\\\\G)\",\"patterns\":[{\"begin\":\"//\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.java\"}},\"end\":\"\\\\n\",\"name\":\"comment.line.double-slash.java\"}]}]},\"comments-javadoc\":{\"patterns\":[{\"begin\":\"^\\\\s*(/\\\\*\\\\*)(?!/)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.comment.java\"}},\"end\":\"\\\\*/\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.java\"}},\"name\":\"comment.block.javadoc.java\",\"patterns\":[{\"match\":\"@(author|deprecated|return|see|serial|since|version)\\\\b\",\"name\":\"keyword.other.documentation.javadoc.java\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.documentation.javadoc.java\"},\"2\":{\"name\":\"variable.parameter.java\"}},\"match\":\"(@param)\\\\s+(\\\\S+)\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.documentation.javadoc.java\"},\"2\":{\"name\":\"entity.name.type.class.java\"}},\"match\":\"(@(?:exception|throws))\\\\s+(\\\\S+)\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.documentation.javadoc.java\"},\"2\":{\"name\":\"entity.name.type.class.java\"},\"3\":{\"name\":\"variable.parameter.java\"}},\"match\":\"\\\\{(@link)\\\\s+(\\\\S+)?#([$\\\\w]+\\\\s*\\\\([^()]*\\\\)).*?}\"}]}]},\"constants-and-special-vars\":{\"patterns\":[{\"match\":\"\\\\b(true|false|null)\\\\b\",\"name\":\"constant.language.java\"},{\"match\":\"\\\\bthis\\\\b\",\"name\":\"variable.language.this.java\"},{\"match\":\"\\\\bsuper\\\\b\",\"name\":\"variable.language.java\"}]},\"enums\":{\"begin\":\"^\\\\s*([\\\\w\\\\s]*)(enum)\\\\s+(\\\\w+)\",\"beginCaptures\":{\"1\":{\"patterns\":[{\"include\":\"#storage-modifiers\"}]},\"2\":{\"name\":\"storage.modifier.java\"},\"3\":{\"name\":\"entity.name.type.enum.java\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.enum.end.bracket.curly.java\"}},\"name\":\"meta.enum.java\",\"patterns\":[{\"begin\":\"\\\\b(extends)\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"storage.modifier.extends.java\"}},\"end\":\"(?=\\\\{|\\\\bimplements\\\\b)\",\"name\":\"meta.definition.class.inherited.classes.java\",\"patterns\":[{\"include\":\"#object-types-inherited\"},{\"include\":\"#comments\"}]},{\"begin\":\"\\\\b(implements)\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"storage.modifier.implements.java\"}},\"end\":\"(?=\\\\{|\\\\bextends\\\\b)\",\"name\":\"meta.definition.class.implemented.interfaces.java\",\"patterns\":[{\"include\":\"#object-types-inherited\"},{\"include\":\"#comments\"}]},{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.enum.begin.bracket.curly.java\"}},\"end\":\"(?=})\",\"patterns\":[{\"begin\":\"(?<=\\\\{)\",\"end\":\"(?=[;}])\",\"patterns\":[{\"include\":\"#comments-javadoc\"},{\"include\":\"#comments\"},{\"begin\":\"\\\\b(\\\\w+)\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"constant.other.enum.java\"}},\"end\":\"(,)|(?=[;}])\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.separator.delimiter.java\"}},\"patterns\":[{\"include\":\"#comments-javadoc\"},{\"include\":\"#comments\"},{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.bracket.round.java\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.bracket.round.java\"}},\"patterns\":[{\"include\":\"#code\"}]},{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.bracket.curly.java\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.bracket.curly.java\"}},\"patterns\":[{\"include\":\"#class-body\"}]}]}]},{\"include\":\"#class-body\"}]}]},\"function-call\":{\"begin\":\"([$A-Z_a-z][$\\\\w]*)\\\\s*(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"entity.name.function.java\"},\"2\":{\"name\":\"punctuation.definition.parameters.begin.bracket.round.java\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.parameters.end.bracket.round.java\"}},\"name\":\"meta.function-call.java\",\"patterns\":[{\"include\":\"#code\"}]},\"generics\":{\"begin\":\"<\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.bracket.angle.java\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.bracket.angle.java\"}},\"patterns\":[{\"match\":\"\\\\b(extends|super)\\\\b\",\"name\":\"storage.modifier.$1.java\"},{\"captures\":{\"1\":{\"name\":\"storage.type.java\"}},\"match\":\"(?<!\\\\.)([$A-Z_a-z][$0-9A-Z_a-z]*)(?=\\\\s*<)\"},{\"include\":\"#primitive-arrays\"},{\"match\":\"[$A-Z_a-z][$0-9A-Z_a-z]*\",\"name\":\"storage.type.generic.java\"},{\"match\":\"\\\\?\",\"name\":\"storage.type.generic.wildcard.java\"},{\"match\":\"&\",\"name\":\"punctuation.separator.types.java\"},{\"match\":\",\",\"name\":\"punctuation.separator.delimiter.java\"},{\"match\":\"\\\\.\",\"name\":\"punctuation.separator.period.java\"},{\"include\":\"#parens\"},{\"include\":\"#generics\"},{\"include\":\"#comments\"}]},\"keywords\":{\"patterns\":[{\"match\":\"\\\\bthrow\\\\b\",\"name\":\"keyword.control.throw.java\"},{\"match\":\"[:?]\",\"name\":\"keyword.control.ternary.java\"},{\"match\":\"\\\\b(return|yield|break|case|continue|default|do|while|for|switch|if|else)\\\\b\",\"name\":\"keyword.control.java\"},{\"match\":\"\\\\b(instanceof)\\\\b\",\"name\":\"keyword.operator.instanceof.java\"},{\"match\":\"(<<|>>>?|[\\\\^~])\",\"name\":\"keyword.operator.bitwise.java\"},{\"match\":\"(([\\\\&^|]|<<|>>>?)=)\",\"name\":\"keyword.operator.assignment.bitwise.java\"},{\"match\":\"(===?|!=|<=|>=|<>|[<>])\",\"name\":\"keyword.operator.comparison.java\"},{\"match\":\"([-%*+/]=)\",\"name\":\"keyword.operator.assignment.arithmetic.java\"},{\"match\":\"(=)\",\"name\":\"keyword.operator.assignment.java\"},{\"match\":\"(--|\\\\+\\\\+)\",\"name\":\"keyword.operator.increment-decrement.java\"},{\"match\":\"([-%*+/])\",\"name\":\"keyword.operator.arithmetic.java\"},{\"match\":\"(!|&&|\\\\|\\\\|)\",\"name\":\"keyword.operator.logical.java\"},{\"match\":\"([\\\\&|])\",\"name\":\"keyword.operator.bitwise.java\"},{\"match\":\"\\\\b(const|goto)\\\\b\",\"name\":\"keyword.reserved.java\"}]},\"lambda-expression\":{\"patterns\":[{\"match\":\"->\",\"name\":\"storage.type.function.arrow.java\"}]},\"member-variables\":{\"begin\":\"(?=private|protected|public|native|synchronized|abstract|threadsafe|transient|static|final)\",\"end\":\"(?=[;=])\",\"patterns\":[{\"include\":\"#storage-modifiers\"},{\"include\":\"#variables\"},{\"include\":\"#primitive-arrays\"},{\"include\":\"#object-types\"}]},\"method-call\":{\"begin\":\"(\\\\.)\\\\s*([$A-Z_a-z][$\\\\w]*)\\\\s*(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.separator.period.java\"},\"2\":{\"name\":\"entity.name.function.java\"},\"3\":{\"name\":\"punctuation.definition.parameters.begin.bracket.round.java\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.parameters.end.bracket.round.java\"}},\"name\":\"meta.method-call.java\",\"patterns\":[{\"include\":\"#code\"}]},\"methods\":{\"begin\":\"(?!new)(?=[<\\\\w].*\\\\s+)(?=([^/=]|/(?!/))+\\\\()\",\"end\":\"(})|(?=;)\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.section.method.end.bracket.curly.java\"}},\"name\":\"meta.method.java\",\"patterns\":[{\"include\":\"#storage-modifiers\"},{\"begin\":\"(\\\\w+)\\\\s*(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"entity.name.function.java\"},\"2\":{\"name\":\"punctuation.definition.parameters.begin.bracket.round.java\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.parameters.end.bracket.round.java\"}},\"name\":\"meta.method.identifier.java\",\"patterns\":[{\"include\":\"#parameters\"},{\"include\":\"#parens\"},{\"include\":\"#comments\"}]},{\"include\":\"#generics\"},{\"begin\":\"(?=\\\\w.*\\\\s+\\\\w+\\\\s*\\\\()\",\"end\":\"(?=\\\\s+\\\\w+\\\\s*\\\\()\",\"name\":\"meta.method.return-type.java\",\"patterns\":[{\"include\":\"#all-types\"},{\"include\":\"#parens\"},{\"include\":\"#comments\"}]},{\"include\":\"#throws\"},{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.method.begin.bracket.curly.java\"}},\"contentName\":\"meta.method.body.java\",\"end\":\"(?=})\",\"patterns\":[{\"include\":\"#code\"}]},{\"include\":\"#comments\"}]},\"module\":{\"begin\":\"((open)\\\\s)?(module)\\\\s+(\\\\w+)\",\"beginCaptures\":{\"1\":{\"name\":\"storage.modifier.java\"},\"3\":{\"name\":\"storage.modifier.java\"},\"4\":{\"name\":\"entity.name.type.module.java\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.module.end.bracket.curly.java\"}},\"name\":\"meta.module.java\",\"patterns\":[{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.module.begin.bracket.curly.java\"}},\"contentName\":\"meta.module.body.java\",\"end\":\"(?=})\",\"patterns\":[{\"include\":\"#comments\"},{\"include\":\"#comments-javadoc\"},{\"match\":\"\\\\b(requires|transitive|exports|opens|to|uses|provides|with)\\\\b\",\"name\":\"keyword.module.java\"}]}]},\"numbers\":{\"patterns\":[{\"match\":\"\\\\b(?<!\\\\$)0([Xx])((?<!\\\\.)\\\\h([_\\\\h]*\\\\h)?[Ll]?(?!\\\\.)|(\\\\h([_\\\\h]*\\\\h)?\\\\.?|(\\\\h([_\\\\h]*\\\\h)?)?\\\\.\\\\h([_\\\\h]*\\\\h)?)[Pp][-+]?[0-9]([0-9_]*[0-9])?[DFdf]?)\\\\b(?!\\\\$)\",\"name\":\"constant.numeric.hex.java\"},{\"match\":\"\\\\b(?<!\\\\$)0([Bb])[01]([01_]*[01])?[Ll]?\\\\b(?!\\\\$)\",\"name\":\"constant.numeric.binary.java\"},{\"match\":\"\\\\b(?<!\\\\$)0[0-7]([0-7_]*[0-7])?[Ll]?\\\\b(?!\\\\$)\",\"name\":\"constant.numeric.octal.java\"},{\"match\":\"(?<!\\\\$)(\\\\b[0-9]([0-9_]*[0-9])?\\\\.\\\\B(?!\\\\.)|\\\\b[0-9]([0-9_]*[0-9])?\\\\.([Ee][-+]?[0-9]([0-9_]*[0-9])?)[DFdf]?\\\\b|\\\\b[0-9]([0-9_]*[0-9])?\\\\.([Ee][-+]?[0-9]([0-9_]*[0-9])?)?[DFdf]\\\\b|\\\\b[0-9]([0-9_]*[0-9])?\\\\.([0-9]([0-9_]*[0-9])?)([Ee][-+]?[0-9]([0-9_]*[0-9])?)?[DFdf]?\\\\b|(?<!\\\\.)\\\\B\\\\.[0-9]([0-9_]*[0-9])?([Ee][-+]?[0-9]([0-9_]*[0-9])?)?[DFdf]?\\\\b|\\\\b[0-9]([0-9_]*[0-9])?([Ee][-+]?[0-9]([0-9_]*[0-9])?)[DFdf]?\\\\b|\\\\b[0-9]([0-9_]*[0-9])?([Ee][-+]?[0-9]([0-9_]*[0-9])?)?[DFdf]\\\\b|\\\\b(0|[1-9]([0-9_]*[0-9])?)(?!\\\\.)[Ll]?\\\\b)(?!\\\\$)\",\"name\":\"constant.numeric.decimal.java\"}]},\"object-types\":{\"patterns\":[{\"include\":\"#generics\"},{\"begin\":\"\\\\b((?:[A-Z_a-z]\\\\w*\\\\s*\\\\.\\\\s*)*)([A-Z_]\\\\w*)\\\\s*(?=\\\\[)\",\"beginCaptures\":{\"1\":{\"patterns\":[{\"match\":\"[A-Z_a-z]\\\\w*\",\"name\":\"storage.type.java\"},{\"match\":\"\\\\.\",\"name\":\"punctuation.separator.period.java\"}]},\"2\":{\"name\":\"storage.type.object.array.java\"}},\"end\":\"(?!\\\\s*\\\\[)\",\"patterns\":[{\"include\":\"#comments\"},{\"include\":\"#parens\"}]},{\"captures\":{\"1\":{\"patterns\":[{\"match\":\"[A-Z_a-z]\\\\w*\",\"name\":\"storage.type.java\"},{\"match\":\"\\\\.\",\"name\":\"punctuation.separator.period.java\"}]}},\"match\":\"\\\\b((?:[A-Z_a-z]\\\\w*\\\\s*\\\\.\\\\s*)*[A-Z_]\\\\w*)\\\\s*(?=<)\"},{\"captures\":{\"1\":{\"patterns\":[{\"match\":\"[A-Z_a-z]\\\\w*\",\"name\":\"storage.type.java\"},{\"match\":\"\\\\.\",\"name\":\"punctuation.separator.period.java\"}]}},\"match\":\"\\\\b((?:[A-Z_a-z]\\\\w*\\\\s*\\\\.\\\\s*)*[A-Z_]\\\\w*)\\\\b((?=\\\\s*[\\\\n$A-Z_a-z])|(?=\\\\s*\\\\.\\\\.\\\\.))\"}]},\"object-types-inherited\":{\"patterns\":[{\"include\":\"#generics\"},{\"captures\":{\"1\":{\"name\":\"punctuation.separator.period.java\"}},\"match\":\"\\\\b(?:[A-Z]\\\\w*\\\\s*(\\\\.)\\\\s*)*[A-Z]\\\\w*\\\\b\",\"name\":\"entity.other.inherited-class.java\"},{\"match\":\",\",\"name\":\"punctuation.separator.delimiter.java\"}]},\"objects\":{\"match\":\"(?<![$\\\\w])[$A-Z_a-z][$\\\\w]*(?=\\\\s*\\\\.\\\\s*[$\\\\w]+)\",\"name\":\"variable.other.object.java\"},\"parameters\":{\"patterns\":[{\"match\":\"\\\\bfinal\\\\b\",\"name\":\"storage.modifier.java\"},{\"include\":\"#annotations\"},{\"include\":\"#all-types\"},{\"include\":\"#strings\"},{\"match\":\"\\\\w+\",\"name\":\"variable.parameter.java\"},{\"match\":\",\",\"name\":\"punctuation.separator.delimiter.java\"},{\"match\":\"\\\\.\\\\.\\\\.\",\"name\":\"punctuation.definition.parameters.varargs.java\"}]},\"parens\":{\"patterns\":[{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.bracket.round.java\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.bracket.round.java\"}},\"patterns\":[{\"include\":\"#code\"}]},{\"begin\":\"\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.bracket.square.java\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.bracket.square.java\"}},\"patterns\":[{\"include\":\"#code\"}]},{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.bracket.curly.java\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.bracket.curly.java\"}},\"patterns\":[{\"include\":\"#code\"}]}]},\"primitive-arrays\":{\"patterns\":[{\"begin\":\"\\\\b(void|boolean|byte|char|short|int|float|long|double)\\\\b\\\\s*(?=\\\\[)\",\"beginCaptures\":{\"1\":{\"name\":\"storage.type.primitive.array.java\"}},\"end\":\"(?!\\\\s*\\\\[)\",\"patterns\":[{\"include\":\"#comments\"},{\"include\":\"#parens\"}]}]},\"primitive-types\":{\"match\":\"\\\\b(void|boolean|byte|char|short|int|float|long|double)\\\\b\",\"name\":\"storage.type.primitive.java\"},\"properties\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"punctuation.separator.period.java\"},\"2\":{\"name\":\"keyword.control.new.java\"}},\"match\":\"(\\\\.)\\\\s*(new)\"},{\"captures\":{\"1\":{\"name\":\"punctuation.separator.period.java\"},\"2\":{\"name\":\"variable.other.object.property.java\"}},\"match\":\"(\\\\.)\\\\s*([$A-Z_a-z][$\\\\w]*)(?=\\\\s*\\\\.\\\\s*[$A-Z_a-z][$\\\\w]*)\"},{\"captures\":{\"1\":{\"name\":\"punctuation.separator.period.java\"},\"2\":{\"name\":\"variable.other.object.property.java\"}},\"match\":\"(\\\\.)\\\\s*([$A-Z_a-z][$\\\\w]*)\"},{\"captures\":{\"1\":{\"name\":\"punctuation.separator.period.java\"},\"2\":{\"name\":\"invalid.illegal.identifier.java\"}},\"match\":\"(\\\\.)\\\\s*([0-9][$\\\\w]*)\"}]},\"record\":{\"begin\":\"(?=\\\\w?[\\\\w\\\\s]*\\\\brecord\\\\s+[$\\\\w]+)\",\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.class.end.bracket.curly.java\"}},\"name\":\"meta.record.java\",\"patterns\":[{\"include\":\"#storage-modifiers\"},{\"include\":\"#generics\"},{\"include\":\"#comments\"},{\"begin\":\"(record)\\\\s+([$\\\\w]+)(<[$\\\\w]+>)?(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"storage.modifier.java\"},\"2\":{\"name\":\"entity.name.type.record.java\"},\"3\":{\"patterns\":[{\"include\":\"#generics\"}]},\"4\":{\"name\":\"punctuation.definition.parameters.begin.bracket.round.java\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.parameters.end.bracket.round.java\"}},\"name\":\"meta.record.identifier.java\",\"patterns\":[{\"include\":\"#code\"}]},{\"begin\":\"(implements)\\\\s\",\"beginCaptures\":{\"1\":{\"name\":\"storage.modifier.implements.java\"}},\"end\":\"(?=\\\\s*\\\\{)\",\"name\":\"meta.definition.class.implemented.interfaces.java\",\"patterns\":[{\"include\":\"#object-types-inherited\"},{\"include\":\"#comments\"}]},{\"include\":\"#record-body\"}]},\"record-body\":{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.class.begin.bracket.curly.java\"}},\"end\":\"(?=})\",\"name\":\"meta.record.body.java\",\"patterns\":[{\"include\":\"#record-constructor\"},{\"include\":\"#class-body\"}]},\"record-constructor\":{\"begin\":\"(?!new)(?=[<\\\\w].*\\\\s+)(?=([^(/=]|/(?!/))+(?=\\\\{))\",\"end\":\"(})|(?=;)\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.section.method.end.bracket.curly.java\"}},\"name\":\"meta.method.java\",\"patterns\":[{\"include\":\"#storage-modifiers\"},{\"begin\":\"(\\\\w+)\",\"beginCaptures\":{\"1\":{\"name\":\"entity.name.function.java\"}},\"end\":\"(?=\\\\s*\\\\{)\",\"name\":\"meta.method.identifier.java\",\"patterns\":[{\"include\":\"#comments\"}]},{\"include\":\"#comments\"},{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.method.begin.bracket.curly.java\"}},\"contentName\":\"meta.method.body.java\",\"end\":\"(?=})\",\"patterns\":[{\"include\":\"#code\"}]}]},\"static-initializer\":{\"patterns\":[{\"include\":\"#anonymous-block-and-instance-initializer\"},{\"match\":\"static\",\"name\":\"storage.modifier.java\"}]},\"storage-modifiers\":{\"match\":\"\\\\b(public|private|protected|static|final|native|synchronized|abstract|threadsafe|transient|volatile|default|strictfp|sealed|non-sealed)\\\\b\",\"name\":\"storage.modifier.java\"},\"strings\":{\"patterns\":[{\"begin\":\"\\\"\\\"\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.java\"}},\"end\":\"\\\"\\\"\\\"\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.java\"}},\"name\":\"string.quoted.triple.java\",\"patterns\":[{\"match\":\"(\\\\\\\\\\\"\\\"\\\")(?!\\\")|(\\\\\\\\.)\",\"name\":\"constant.character.escape.java\"}]},{\"begin\":\"\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.java\"}},\"end\":\"\\\"\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.java\"}},\"name\":\"string.quoted.double.java\",\"patterns\":[{\"match\":\"\\\\\\\\.\",\"name\":\"constant.character.escape.java\"}]},{\"begin\":\"'\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.java\"}},\"end\":\"'\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.java\"}},\"name\":\"string.quoted.single.java\",\"patterns\":[{\"match\":\"\\\\\\\\.\",\"name\":\"constant.character.escape.java\"}]}]},\"throws\":{\"begin\":\"throws\",\"beginCaptures\":{\"0\":{\"name\":\"storage.modifier.java\"}},\"end\":\"(?=[;{])\",\"name\":\"meta.throwables.java\",\"patterns\":[{\"match\":\",\",\"name\":\"punctuation.separator.delimiter.java\"},{\"match\":\"[$A-Z_a-z][$.0-9A-Z_a-z]*\",\"name\":\"storage.type.java\"},{\"include\":\"#comments\"}]},\"try-catch-finally\":{\"patterns\":[{\"begin\":\"\\\\btry\\\\b\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.control.try.java\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.try.end.bracket.curly.java\"}},\"name\":\"meta.try.java\",\"patterns\":[{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.try.resources.begin.bracket.round.java\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.try.resources.end.bracket.round.java\"}},\"name\":\"meta.try.resources.java\",\"patterns\":[{\"include\":\"#code\"}]},{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.try.begin.bracket.curly.java\"}},\"contentName\":\"meta.try.body.java\",\"end\":\"(?=})\",\"patterns\":[{\"include\":\"#code\"}]}]},{\"begin\":\"\\\\b(catch)\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.catch.java\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.catch.end.bracket.curly.java\"}},\"name\":\"meta.catch.java\",\"patterns\":[{\"include\":\"#comments\"},{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.parameters.begin.bracket.round.java\"}},\"contentName\":\"meta.catch.parameters.java\",\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.parameters.end.bracket.round.java\"}},\"patterns\":[{\"include\":\"#comments\"},{\"include\":\"#storage-modifiers\"},{\"begin\":\"[$A-Z_a-z][$.0-9A-Z_a-z]*\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.java\"}},\"end\":\"(\\\\|)|(?=\\\\))\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.catch.separator.java\"}},\"patterns\":[{\"include\":\"#comments\"},{\"captures\":{\"0\":{\"name\":\"variable.parameter.java\"}},\"match\":\"\\\\w+\"}]}]},{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.catch.begin.bracket.curly.java\"}},\"contentName\":\"meta.catch.body.java\",\"end\":\"(?=})\",\"patterns\":[{\"include\":\"#code\"}]}]},{\"begin\":\"\\\\bfinally\\\\b\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.control.finally.java\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.finally.end.bracket.curly.java\"}},\"name\":\"meta.finally.java\",\"patterns\":[{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.finally.begin.bracket.curly.java\"}},\"contentName\":\"meta.finally.body.java\",\"end\":\"(?=})\",\"patterns\":[{\"include\":\"#code\"}]}]}]},\"variables\":{\"begin\":\"(?=\\\\b((void|boolean|byte|char|short|int|float|long|double)|(?>(\\\\w+\\\\.)*[A-Z_]+\\\\w*))\\\\b\\\\s*(<[],.<>?\\\\[\\\\w\\\\s]*>)?\\\\s*((\\\\[])*)?\\\\s+[$A-Z_a-z][$\\\\w]*([]$,\\\\[\\\\w][],\\\\[\\\\w\\\\s]*)?\\\\s*([:;=]))\",\"end\":\"(?=[:;=])\",\"name\":\"meta.definition.variable.java\",\"patterns\":[{\"captures\":{\"1\":{\"name\":\"variable.other.definition.java\"}},\"match\":\"([$A-Z_a-z][$\\\\w]*)(?=\\\\s*(\\\\[])*\\\\s*([,:;=]))\"},{\"include\":\"#all-types\"},{\"include\":\"#code\"}]},\"variables-local\":{\"begin\":\"(?=\\\\b(var)\\\\b\\\\s+[$A-Z_a-z][$\\\\w]*\\\\s*([:;=]))\",\"end\":\"(?=[:;=])\",\"name\":\"meta.definition.variable.local.java\",\"patterns\":[{\"match\":\"\\\\bvar\\\\b\",\"name\":\"storage.type.local.java\"},{\"captures\":{\"1\":{\"name\":\"variable.other.definition.java\"}},\"match\":\"([$A-Z_a-z][$\\\\w]*)(?=\\\\s*(\\\\[])*\\\\s*([:;=]))\"},{\"include\":\"#code\"}]}},\"scopeName\":\"source.java\"}"));
const __TURBOPACK__default__export__ = [
    lang
];
}}),
"[project]/node_modules/@shikijs/langs/dist/xml.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$java$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/java.mjs [app-client] (ecmascript)");
;
const lang = Object.freeze(JSON.parse("{\"displayName\":\"XML\",\"name\":\"xml\",\"patterns\":[{\"begin\":\"(<\\\\?)\\\\s*([-0-9A-Z_a-z]+)\",\"captures\":{\"1\":{\"name\":\"punctuation.definition.tag.xml\"},\"2\":{\"name\":\"entity.name.tag.xml\"}},\"end\":\"(\\\\?>)\",\"name\":\"meta.tag.preprocessor.xml\",\"patterns\":[{\"match\":\" ([-A-Za-z]+)\",\"name\":\"entity.other.attribute-name.xml\"},{\"include\":\"#doublequotedString\"},{\"include\":\"#singlequotedString\"}]},{\"begin\":\"(<!)(DOCTYPE)\\\\s+([:A-Z_a-z][-.0-:A-Z_a-z]*)\",\"captures\":{\"1\":{\"name\":\"punctuation.definition.tag.xml\"},\"2\":{\"name\":\"keyword.other.doctype.xml\"},\"3\":{\"name\":\"variable.language.documentroot.xml\"}},\"end\":\"\\\\s*(>)\",\"name\":\"meta.tag.sgml.doctype.xml\",\"patterns\":[{\"include\":\"#internalSubset\"}]},{\"include\":\"#comments\"},{\"begin\":\"(<)((?:([-0-9A-Z_a-z]+)(:))?([-0-:A-Z_a-z]+))(?=(\\\\s[^>]*)?></\\\\2>)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.xml\"},\"2\":{\"name\":\"entity.name.tag.xml\"},\"3\":{\"name\":\"entity.name.tag.namespace.xml\"},\"4\":{\"name\":\"punctuation.separator.namespace.xml\"},\"5\":{\"name\":\"entity.name.tag.localname.xml\"}},\"end\":\"(>)(</)((?:([-0-9A-Z_a-z]+)(:))?([-0-:A-Z_a-z]+))(>)\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.tag.xml\"},\"2\":{\"name\":\"punctuation.definition.tag.xml\"},\"3\":{\"name\":\"entity.name.tag.xml\"},\"4\":{\"name\":\"entity.name.tag.namespace.xml\"},\"5\":{\"name\":\"punctuation.separator.namespace.xml\"},\"6\":{\"name\":\"entity.name.tag.localname.xml\"},\"7\":{\"name\":\"punctuation.definition.tag.xml\"}},\"name\":\"meta.tag.no-content.xml\",\"patterns\":[{\"include\":\"#tagStuff\"}]},{\"begin\":\"(</?)(?:([-.\\\\w]+)((:)))?([-.:\\\\w]+)\",\"captures\":{\"1\":{\"name\":\"punctuation.definition.tag.xml\"},\"2\":{\"name\":\"entity.name.tag.namespace.xml\"},\"3\":{\"name\":\"entity.name.tag.xml\"},\"4\":{\"name\":\"punctuation.separator.namespace.xml\"},\"5\":{\"name\":\"entity.name.tag.localname.xml\"}},\"end\":\"(/?>)\",\"name\":\"meta.tag.xml\",\"patterns\":[{\"include\":\"#tagStuff\"}]},{\"include\":\"#entity\"},{\"include\":\"#bare-ampersand\"},{\"begin\":\"<%@\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.embedded.begin.xml\"}},\"end\":\"%>\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.embedded.end.xml\"}},\"name\":\"source.java-props.embedded.xml\",\"patterns\":[{\"match\":\"page|include|taglib\",\"name\":\"keyword.other.page-props.xml\"}]},{\"begin\":\"<%[!=]?(?!--)\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.embedded.begin.xml\"}},\"end\":\"(?!--)%>\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.embedded.end.xml\"}},\"name\":\"source.java.embedded.xml\",\"patterns\":[{\"include\":\"source.java\"}]},{\"begin\":\"<!\\\\[CDATA\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.xml\"}},\"end\":\"]]>\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.xml\"}},\"name\":\"string.unquoted.cdata.xml\"}],\"repository\":{\"EntityDecl\":{\"begin\":\"(<!)(ENTITY)\\\\s+(%\\\\s+)?([:A-Z_a-z][-.0-:A-Z_a-z]*)(\\\\s+(?:SYSTEM|PUBLIC)\\\\s+)?\",\"captures\":{\"1\":{\"name\":\"punctuation.definition.tag.xml\"},\"2\":{\"name\":\"keyword.other.entity.xml\"},\"3\":{\"name\":\"punctuation.definition.entity.xml\"},\"4\":{\"name\":\"variable.language.entity.xml\"},\"5\":{\"name\":\"keyword.other.entitytype.xml\"}},\"end\":\"(>)\",\"patterns\":[{\"include\":\"#doublequotedString\"},{\"include\":\"#singlequotedString\"}]},\"bare-ampersand\":{\"match\":\"&\",\"name\":\"invalid.illegal.bad-ampersand.xml\"},\"comments\":{\"patterns\":[{\"begin\":\"<%--\",\"captures\":{\"0\":{\"name\":\"punctuation.definition.comment.xml\"},\"end\":\"--%>\",\"name\":\"comment.block.xml\"}},{\"begin\":\"<!--\",\"captures\":{\"0\":{\"name\":\"punctuation.definition.comment.xml\"}},\"end\":\"-->\",\"name\":\"comment.block.xml\",\"patterns\":[{\"begin\":\"--(?!>)\",\"captures\":{\"0\":{\"name\":\"invalid.illegal.bad-comments-or-CDATA.xml\"}}}]}]},\"doublequotedString\":{\"begin\":\"\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.xml\"}},\"end\":\"\\\"\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.xml\"}},\"name\":\"string.quoted.double.xml\",\"patterns\":[{\"include\":\"#entity\"},{\"include\":\"#bare-ampersand\"}]},\"entity\":{\"captures\":{\"1\":{\"name\":\"punctuation.definition.constant.xml\"},\"3\":{\"name\":\"punctuation.definition.constant.xml\"}},\"match\":\"(&)([:A-Z_a-z][-.0-:A-Z_a-z]*|#[0-9]+|#x\\\\h+)(;)\",\"name\":\"constant.character.entity.xml\"},\"internalSubset\":{\"begin\":\"(\\\\[)\",\"captures\":{\"1\":{\"name\":\"punctuation.definition.constant.xml\"}},\"end\":\"(])\",\"name\":\"meta.internalsubset.xml\",\"patterns\":[{\"include\":\"#EntityDecl\"},{\"include\":\"#parameterEntity\"},{\"include\":\"#comments\"}]},\"parameterEntity\":{\"captures\":{\"1\":{\"name\":\"punctuation.definition.constant.xml\"},\"3\":{\"name\":\"punctuation.definition.constant.xml\"}},\"match\":\"(%)([:A-Z_a-z][-.0-:A-Z_a-z]*)(;)\",\"name\":\"constant.character.parameter-entity.xml\"},\"singlequotedString\":{\"begin\":\"'\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.xml\"}},\"end\":\"'\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.xml\"}},\"name\":\"string.quoted.single.xml\",\"patterns\":[{\"include\":\"#entity\"},{\"include\":\"#bare-ampersand\"}]},\"tagStuff\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"entity.other.attribute-name.namespace.xml\"},\"2\":{\"name\":\"entity.other.attribute-name.xml\"},\"3\":{\"name\":\"punctuation.separator.namespace.xml\"},\"4\":{\"name\":\"entity.other.attribute-name.localname.xml\"}},\"match\":\"(?:^|\\\\s+)(?:([-.\\\\w]+)((:)))?([-.:\\\\w]+)\\\\s*=\"},{\"include\":\"#doublequotedString\"},{\"include\":\"#singlequotedString\"}]}},\"scopeName\":\"text.xml\",\"embeddedLangs\":[\"java\"]}"));
const __TURBOPACK__default__export__ = [
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$java$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    lang
];
}}),
"[project]/node_modules/@shikijs/langs/dist/sql.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const lang = Object.freeze(JSON.parse("{\"displayName\":\"SQL\",\"name\":\"sql\",\"patterns\":[{\"match\":\"((?<!@)@)\\\\b(\\\\w+)\\\\b\",\"name\":\"text.variable\"},{\"match\":\"(\\\\[)[^]]*(])\",\"name\":\"text.bracketed\"},{\"include\":\"#comments\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.create.sql\"},\"2\":{\"name\":\"keyword.other.sql\"},\"5\":{\"name\":\"entity.name.function.sql\"}},\"match\":\"(?i:^\\\\s*(create(?:\\\\s+or\\\\s+replace)?)\\\\s+(aggregate|conversion|database|domain|function|group|(unique\\\\s+)?index|language|operator class|operator|rule|schema|sequence|table|tablespace|trigger|type|user|view)\\\\s+)([\\\"'`]?)(\\\\w+)\\\\4\",\"name\":\"meta.create.sql\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.create.sql\"},\"2\":{\"name\":\"keyword.other.sql\"}},\"match\":\"(?i:^\\\\s*(drop)\\\\s+(aggregate|conversion|database|domain|function|group|index|language|operator class|operator|rule|schema|sequence|table|tablespace|trigger|type|user|view))\",\"name\":\"meta.drop.sql\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.create.sql\"},\"2\":{\"name\":\"keyword.other.table.sql\"},\"3\":{\"name\":\"entity.name.function.sql\"},\"4\":{\"name\":\"keyword.other.cascade.sql\"}},\"match\":\"(?i:\\\\s*(drop)\\\\s+(table)\\\\s+(\\\\w+)(\\\\s+cascade)?\\\\b)\",\"name\":\"meta.drop.sql\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.create.sql\"},\"2\":{\"name\":\"keyword.other.table.sql\"}},\"match\":\"(?i:^\\\\s*(alter)\\\\s+(aggregate|conversion|database|domain|function|group|index|language|operator class|operator|proc(edure)?|rule|schema|sequence|table|tablespace|trigger|type|user|view)\\\\s+)\",\"name\":\"meta.alter.sql\"},{\"captures\":{\"1\":{\"name\":\"storage.type.sql\"},\"2\":{\"name\":\"storage.type.sql\"},\"3\":{\"name\":\"constant.numeric.sql\"},\"4\":{\"name\":\"storage.type.sql\"},\"5\":{\"name\":\"constant.numeric.sql\"},\"6\":{\"name\":\"storage.type.sql\"},\"7\":{\"name\":\"constant.numeric.sql\"},\"8\":{\"name\":\"constant.numeric.sql\"},\"9\":{\"name\":\"storage.type.sql\"},\"10\":{\"name\":\"constant.numeric.sql\"},\"11\":{\"name\":\"storage.type.sql\"},\"12\":{\"name\":\"storage.type.sql\"},\"13\":{\"name\":\"storage.type.sql\"},\"14\":{\"name\":\"constant.numeric.sql\"},\"15\":{\"name\":\"storage.type.sql\"}},\"match\":\"(?i)\\\\b(bigint|bigserial|bit|boolean|box|bytea|cidr|circle|date|double\\\\sprecision|inet|int|integer|line|lseg|macaddr|money|oid|path|point|polygon|real|serial|smallint|sysdate|text)\\\\b|\\\\b(bit\\\\svarying|character\\\\s(?:varying)?|tinyint|var\\\\schar|float|interval)\\\\((\\\\d+)\\\\)|\\\\b(char|number|varchar\\\\d?)\\\\b(?:\\\\((\\\\d+)\\\\))?|\\\\b(numeric|decimal)\\\\b(?:\\\\((\\\\d+),(\\\\d+)\\\\))?|\\\\b(times?)\\\\b(?:\\\\((\\\\d+)\\\\))?(\\\\swith(?:out)?\\\\stime\\\\szone\\\\b)?|\\\\b(timestamp)(s|tz)?\\\\b(?:\\\\((\\\\d+)\\\\))?(\\\\s(with(?:|out))\\\\stime\\\\szone\\\\b)?\"},{\"match\":\"(?i:\\\\b((?:primary|foreign)\\\\s+key|references|on\\\\sdelete(\\\\s+cascade)?|nocheck|check|constraint|collate|default)\\\\b)\",\"name\":\"storage.modifier.sql\"},{\"match\":\"\\\\b\\\\d+\\\\b\",\"name\":\"constant.numeric.sql\"},{\"match\":\"(?i:\\\\b(select(\\\\s+(all|distinct))?|insert\\\\s+(ignore\\\\s+)?into|update|delete|from|set|where|group\\\\s+by|or|like|and|union(\\\\s+all)?|having|order\\\\s+by|limit|cross\\\\s+join|join|straight_join|(inner|(left|right|full)(\\\\s+outer)?)\\\\s+join|natural(\\\\s+(inner|(left|right|full)(\\\\s+outer)?))?\\\\s+join)\\\\b)\",\"name\":\"keyword.other.DML.sql\"},{\"match\":\"(?i:\\\\b(on|off|((is\\\\s+)?not\\\\s+)?null)\\\\b)\",\"name\":\"keyword.other.DDL.create.II.sql\"},{\"match\":\"(?i:\\\\bvalues\\\\b)\",\"name\":\"keyword.other.DML.II.sql\"},{\"match\":\"(?i:\\\\b(begin(\\\\s+work)?|start\\\\s+transaction|commit(\\\\s+work)?|rollback(\\\\s+work)?)\\\\b)\",\"name\":\"keyword.other.LUW.sql\"},{\"match\":\"(?i:\\\\b(grant(\\\\swith\\\\sgrant\\\\soption)?|revoke)\\\\b)\",\"name\":\"keyword.other.authorization.sql\"},{\"match\":\"(?i:\\\\bin\\\\b)\",\"name\":\"keyword.other.data-integrity.sql\"},{\"match\":\"(?i:^\\\\s*(comment\\\\s+on\\\\s+(table|column|aggregate|constraint|database|domain|function|index|operator|rule|schema|sequence|trigger|type|view))\\\\s+.*?\\\\s+(is)\\\\s+)\",\"name\":\"keyword.other.object-comments.sql\"},{\"match\":\"(?i)\\\\bAS\\\\b\",\"name\":\"keyword.other.alias.sql\"},{\"match\":\"(?i)\\\\b(DESC|ASC)\\\\b\",\"name\":\"keyword.other.order.sql\"},{\"match\":\"\\\\*\",\"name\":\"keyword.operator.star.sql\"},{\"match\":\"[!<>]?=|<>|[<>]\",\"name\":\"keyword.operator.comparison.sql\"},{\"match\":\"[-+/]\",\"name\":\"keyword.operator.math.sql\"},{\"match\":\"\\\\|\\\\|\",\"name\":\"keyword.operator.concatenator.sql\"},{\"captures\":{\"1\":{\"name\":\"support.function.aggregate.sql\"}},\"match\":\"(?i)\\\\b(approx_count_distinct|approx_percentile_cont|approx_percentile_disc|avg|checksum_agg|count|count_big|group|grouping|grouping_id|max|min|sum|stdevp??|varp??)\\\\b\\\\s*\\\\(\"},{\"captures\":{\"1\":{\"name\":\"support.function.analytic.sql\"}},\"match\":\"(?i)\\\\b(cume_dist|first_value|lag|last_value|lead|percent_rank|percentile_cont|percentile_disc)\\\\b\\\\s*\\\\(\"},{\"captures\":{\"1\":{\"name\":\"support.function.bitmanipulation.sql\"}},\"match\":\"(?i)\\\\b((?:bit_coun|get_bi|left_shif|right_shif|set_bi)t)\\\\b\\\\s*\\\\(\"},{\"captures\":{\"1\":{\"name\":\"support.function.conversion.sql\"}},\"match\":\"(?i)\\\\b(cast|convert|parse|try_cast|try_convert|try_parse)\\\\b\\\\s*\\\\(\"},{\"captures\":{\"1\":{\"name\":\"support.function.collation.sql\"}},\"match\":\"(?i)\\\\b(collationproperty|tertiary_weights)\\\\b\\\\s*\\\\(\"},{\"captures\":{\"1\":{\"name\":\"support.function.cryptographic.sql\"}},\"match\":\"(?i)\\\\b(asymkey_id|asymkeyproperty|certproperty|cert_id|crypt_gen_random|decryptbyasymkey|decryptbycert|decryptbykey|decryptbykeyautoasymkey|decryptbykeyautocert|decryptbypassphrase|encryptbyasymkey|encryptbycert|encryptbykey|encryptbypassphrase|hashbytes|is_objectsigned|key_guid|key_id|key_name|signbyasymkey|signbycert|symkeyproperty|verifysignedbycert|verifysignedbyasymkey)\\\\b\\\\s*\\\\(\"},{\"captures\":{\"1\":{\"name\":\"support.function.cursor.sql\"}},\"match\":\"(?i)\\\\b(cursor_status)\\\\b\\\\s*\\\\(\"},{\"captures\":{\"1\":{\"name\":\"support.function.datetime.sql\"}},\"match\":\"(?i)\\\\b(sysdatetime|sysdatetimeoffset|sysutcdatetime|current_time(stamp)?|getdate|getutcdate|datename|datepart|day|month|year|datefromparts|datetime2fromparts|datetimefromparts|datetimeoffsetfromparts|smalldatetimefromparts|timefromparts|datediff|dateadd|datetrunc|eomonth|switchoffset|todatetimeoffset|isdate|date_bucket)\\\\b\\\\s*\\\\(\"},{\"captures\":{\"1\":{\"name\":\"support.function.datatype.sql\"}},\"match\":\"(?i)\\\\b(datalength|ident_current|ident_incr|ident_seed|identity|sql_variant_property)\\\\b\\\\s*\\\\(\"},{\"captures\":{\"1\":{\"name\":\"support.function.expression.sql\"}},\"match\":\"(?i)\\\\b(coalesce|nullif)\\\\b\\\\s*\\\\(\"},{\"captures\":{\"1\":{\"name\":\"support.function.globalvar.sql\"}},\"match\":\"(?<!@)@@(?i)\\\\b(cursor_rows|connections|cpu_busy|datefirst|dbts|error|fetch_status|identity|idle|io_busy|langid|language|lock_timeout|max_connections|max_precision|nestlevel|options|packet_errors|pack_received|pack_sent|procid|remserver|rowcount|servername|servicename|spid|textsize|timeticks|total_errors|total_read|total_write|trancount|version)\\\\b\\\\s*\\\\(\"},{\"captures\":{\"1\":{\"name\":\"support.function.json.sql\"}},\"match\":\"(?i)\\\\b(json|isjson|json_object|json_array|json_value|json_query|json_modify|json_path_exists)\\\\b\\\\s*\\\\(\"},{\"captures\":{\"1\":{\"name\":\"support.function.logical.sql\"}},\"match\":\"(?i)\\\\b(choose|iif|greatest|least)\\\\b\\\\s*\\\\(\"},{\"captures\":{\"1\":{\"name\":\"support.function.mathematical.sql\"}},\"match\":\"(?i)\\\\b(abs|acos|asin|atan|atn2|ceiling|cos|cot|degrees|exp|floor|log|log10|pi|power|radians|rand|round|sign|sin|sqrt|square|tan)\\\\b\\\\s*\\\\(\"},{\"captures\":{\"1\":{\"name\":\"support.function.metadata.sql\"}},\"match\":\"(?i)\\\\b(app_name|applock_mode|applock_test|assemblyproperty|col_length|col_name|columnproperty|database_principal_id|databasepropertyex|db_id|db_name|file_id|file_idex|file_name|filegroup_id|filegroup_name|filegroupproperty|fileproperty|fulltextcatalogproperty|fulltextserviceproperty|index_col|indexkey_property|indexproperty|object_definition|object_id|object_name|object_schema_name|objectproperty|objectpropertyex|original_db_name|parsename|schema_id|schema_name|scope_identity|serverproperty|stats_date|type_id|type_name|typeproperty)\\\\b\\\\s*\\\\(\"},{\"captures\":{\"1\":{\"name\":\"support.function.ranking.sql\"}},\"match\":\"(?i)\\\\b(rank|dense_rank|ntile|row_number)\\\\b\\\\s*\\\\(\"},{\"captures\":{\"1\":{\"name\":\"support.function.rowset.sql\"}},\"match\":\"(?i)\\\\b(generate_series|opendatasource|openjson|openrowset|openquery|openxml|predict|string_split)\\\\b\\\\s*\\\\(\"},{\"captures\":{\"1\":{\"name\":\"support.function.security.sql\"}},\"match\":\"(?i)\\\\b(certencoded|certprivatekey|current_user|database_principal_id|has_perms_by_name|is_member|is_rolemember|is_srvrolemember|original_login|permissions|pwdcompare|pwdencrypt|schema_id|schema_name|session_user|suser_id|suser_sid|suser_sname|system_user|suser_name|user_id|user_name)\\\\b\\\\s*\\\\(\"},{\"captures\":{\"1\":{\"name\":\"support.function.string.sql\"}},\"match\":\"(?i)\\\\b(ascii|char|charindex|concat|difference|format|left|len|lower|ltrim|nchar|nodes|patindex|quotename|replace|replicate|reverse|right|rtrim|soundex|space|str|string_agg|string_escape|string_split|stuff|substring|translate|trim|unicode|upper)\\\\b\\\\s*\\\\(\"},{\"captures\":{\"1\":{\"name\":\"support.function.system.sql\"}},\"match\":\"(?i)\\\\b(binary_checksum|checksum|compress|connectionproperty|context_info|current_request_id|current_transaction_id|decompress|error_line|error_message|error_number|error_procedure|error_severity|error_state|formatmessage|get_filestream_transaction_context|getansinull|host_id|host_name|isnull|isnumeric|min_active_rowversion|newid|newsequentialid|rowcount_big|session_context|session_id|xact_state)\\\\b\\\\s*\\\\(\"},{\"captures\":{\"1\":{\"name\":\"support.function.textimage.sql\"}},\"match\":\"(?i)\\\\b(patindex|textptr|textvalid)\\\\b\\\\s*\\\\(\"},{\"captures\":{\"1\":{\"name\":\"support.function.vector.sql\"}},\"match\":\"(?i)\\\\b(vector_(?:distance|norm|normalize))\\\\b\\\\s*\\\\(\"},{\"captures\":{\"1\":{\"name\":\"constant.other.database-name.sql\"},\"2\":{\"name\":\"constant.other.table-name.sql\"}},\"match\":\"(\\\\w+?)\\\\.(\\\\w+)\"},{\"include\":\"#strings\"},{\"include\":\"#regexps\"},{\"match\":\"\\\\b(?i)(abort|abort_after_wait|absent|absolute|accent_sensitivity|acceptable_cursopt|acp|action|activation|add|address|admin|aes_128|aes_192|aes_256|affinity|after|aggregate|algorithm|all_constraints|all_errormsgs|all_indexes|all_levels|all_results|allow_connections|allow_dup_row|allow_encrypted_value_modifications|allow_page_locks|allow_row_locks|allow_snapshot_isolation|alter|altercolumn|always|anonymous|ansi_defaults|ansi_null_default|ansi_null_dflt_off|ansi_null_dflt_on|ansi_nulls|ansi_padding|ansi_warnings|appdomain|append|application|apply|arithabort|arithignore|array|assembly|asymmetric|asynchronous_commit|at|atan2|atomic|attach|attach_force_rebuild_log|attach_rebuild_log|audit|auth_realm|authentication|auto|auto_cleanup|auto_close|auto_create_statistics|auto_drop|auto_shrink|auto_update_statistics|auto_update_statistics_async|automated_backup_preference|automatic|autopilot|availability|availability_mode|backup|backup_priority|base64|basic|batches|batchsize|before|between|bigint|binary|binding|bit|block|blockers|blocksize|bmk|both|break|broker|broker_instance|bucket_count|buffer|buffercount|bulk_logged|by|call|caller|card|case|catalog|catch|cert|certificate|change_retention|change_tracking|change_tracking_context|changes|char|character|character_set|check_expiration|check_policy|checkconstraints|checkindex|checkpoint|checksum|cleanup_policy|clear|clear_port|close|clustered|codepage|collection|column_encryption_key|column_master_key|columnstore|columnstore_archive|colv_80_to_100|colv_100_to_80|commit_differential_base|committed|compatibility_level|compress_all_row_groups|compression|compression_delay|concat_null_yields_null|concatenate|configuration|connect|connection|containment|continue|continue_after_error|contract|contract_name|control|conversation|conversation_group_id|conversation_handle|copy|copy_only|count_rows|counter|create(\\\\\\\\s+or\\\\\\\\s+alter)?|credential|cross|cryptographic|cryptographic_provider|cube|cursor|cursor_close_on_commit|cursor_default|data|data_compression|data_flush_interval_seconds|data_mirroring|data_purity|data_source|database|database_name|database_snapshot|datafiletype|date_correlation_optimization|date|datefirst|dateformat|date_format|datetime2??|datetimeoffset|day(s)?|db_chaining|dbid|dbidexec|dbo_only|deadlock_priority|deallocate|dec|decimal|declare|decrypt|decrypt_a|decryption|default_database|default_fulltext_language|default_language|default_logon_domain|default_schema|definition|delay|delayed_durability|delimitedtext|density_vector|dependent|des|description|desired_state|desx|differential|digest|disable|disable_broker|disable_def_cnst_chk|disabled|disk|distinct|distributed|distribution|drop|drop_existing|dts_buffers|dump|durability|dynamic|edition|elements|else|emergency|empty|enable|enable_broker|enabled|encoding|encrypted|encrypted_value|encryption|encryption_type|end|endpoint|endpoint_url|enhancedintegrity|entry|error_broker_conversations|errorfile|estimateonly|event|except|exec|executable|execute|exists|expand|expiredate|expiry_date|explicit|external|external_access|failover|failover_mode|failure_condition_level|fast|fast_forward|fastfirstrow|federated_service_account|fetch|field_terminator|fieldterminator|file|filelistonly|filegroup|filegrowth|filename|filestream|filestream_log|filestream_on|filetable|file_format|filter|first_row|fips_flagger|fire_triggers|first|firstrow|float|flush_interval_seconds|fmtonly|following|for|force|force_failover_allow_data_loss|force_service_allow_data_loss|forced|forceplan|formatfile|format_options|format_type|formsof|forward_only|free_cursors|free_exec_context|fullscan|fulltext|fulltextall|fulltextkey|function|generated|get|geography|geometry|global|go|goto|governor|guid|hadoop|hardening|hash|hashed|header_limit|headeronly|health_check_timeout|hidden|hierarchyid|histogram|histogram_steps|hits_cursors|hits_exec_context|hour(s)?|http|identity|identity_value|if|ifnull|ignore|ignore_constraints|ignore_dup_key|ignore_dup_row|ignore_triggers|image|immediate|implicit_transactions|include|include_null_values|incremental|index|inflectional|init|initiator|insensitive|insert|instead|int|integer|integrated|intersect|intermediate|interval_length_minutes|into|inuse_cursors|inuse_exec_context|io|is|isabout|iso_week|isolation|job_tracker_location|json|keep|keep_nulls|keep_replication|keepdefaults|keepfixed|keepidentity|keepnulls|kerberos|key|key_path|key_source|key_store_provider_name|keyset|kill|kilobytes_per_batch|labelonly|langid|language|last|lastrow|leading|legacy_cardinality_estimation|length|level|lifetime|lineage_80_to_100|lineage_100_to_80|listener_ip|listener_port|load|loadhistory|lob_compaction|local|local_service_name|locate|location|lock_escalation|lock_timeout|lockres|log|login|login_type|loop|manual|mark_in_use_for_removal|masked|master|match|matched|max_queue_readers|max_duration|max_outstanding_io_per_volume|maxdop|maxerrors|maxlength|maxtransfersize|max_plans_per_query|max_storage_size_mb|mediadescription|medianame|mediapassword|memogroup|memory_optimized|merge|message|message_forward_size|message_forwarding|microsecond|millisecond|minute(s)?|mirror_address|misses_cursors|misses_exec_context|mixed|modify|money|month|move|multi_user|must_change|name|namespace|nanosecond|native|native_compilation|nchar|ncharacter|nested_triggers|never|new_account|new_broker|newname|next|no|no_browsetable|no_checksum|no_compression|no_infomsgs|no_triggers|no_truncate|nocount|noexec|noexpand|noformat|noinit|nolock|nonatomic|nonclustered|nondurable|none|norecompute|norecovery|noreset|norewind|noskip|not|notification|nounload|now|nowait|ntext|ntlm|nulls|numeric|numeric_roundabort|nvarchar|object|objid|oem|offline|old_account|online|operation_mode|open|openjson|optimistic|option|orc|out|outer|output|over|override|owner|ownership|pad_index|page|page_checksum|page_verify|pagecount|paglock|param|parameter_sniffing|parameter_type_expansion|parameterization|parquet|parseonly|partial|partition|partner|password|path|pause|percentage|permission_set|persisted|period|physical_only|plan_forcing_mode|policy|pool|population|ports|preceding|precision|predicate|presume_abort|primary|primary_role|print|prior|priority |priority_level|private|proc(edure)?|procedure_name|profile|provider|quarter|query_capture_mode|query_governor_cost_limit|query_optimizer_hotfixes|query_store|queue|quoted_identifier|raiserror|range|raw|rcfile|rc2|rc4|rc4_128|rdbms|read_committed_snapshot|read|read_only|read_write|readcommitted|readcommittedlock|readonly|readpast|readuncommitted|readwrite|real|rebuild|receive|recmodel_70backcomp|recompile|reconfigure|recovery|recursive|recursive_triggers|redo_queue|reject_sample_value|reject_type|reject_value|relative|remote|remote_data_archive|remote_proc_transactions|remote_service_name|remove|removed_cursors|removed_exec_context|reorganize|repeat|repeatable|repeatableread|replace|replica|replicated|replnick_100_to_80|replnickarray_80_to_100|replnickarray_100_to_80|required|required_cursopt|resample|reset|resource|resource_manager_location|respect|restart|restore|restricted_user|resume|retaindays|retention|return|revert|rewind|rewindonly|returns|robust|role|rollup|root|round_robin|route|row|rowdump|rowguidcol|rowlock|row_terminator|rows|rows_per_batch|rowsets_only|rowterminator|rowversion|rsa_1024|rsa_2048|rsa_3072|rsa_4096|rsa_512|safe|safety|sample|save|scalar|schema|schemabinding|scoped|scroll|scroll_locks|sddl|second|secexpr|seconds|secondary|secondary_only|secondary_role|secret|security|securityaudit|selective|self|send|sent|sequence|serde_method|serializable|server|service|service_broker|service_name|service_objective|session_timeout|sessions??|seterror|setopts|sets|shard_map_manager|shard_map_name|sharded|shared_memory|shortest_path|show_statistics|showplan_all|showplan_text|showplan_xml|showplan_xml_with_recompile|shrinkdb|shutdown|sid|signature|simple|single_blob|single_clob|single_nclob|single_user|singleton|site|size|size_based_cleanup_mode|skip|smalldatetime|smallint|smallmoney|snapshot|snapshot_import|snapshotrestorephase|soap|softnuma|sort_in_tempdb|sorted_data|sorted_data_reorg|spatial|sql|sql_bigint|sql_binary|sql_bit|sql_char|sql_date|sql_decimal|sql_double|sql_float|sql_guid|sql_handle|sql_longvarbinary|sql_longvarchar|sql_numeric|sql_real|sql_smallint|sql_time|sql_timestamp|sql_tinyint|sql_tsi_day|sql_tsi_frac_second|sql_tsi_hour|sql_tsi_minute|sql_tsi_month|sql_tsi_quarter|sql_tsi_second|sql_tsi_week|sql_tsi_year|sql_type_date|sql_type_time|sql_type_timestamp|sql_varbinary|sql_varchar|sql_variant|sql_wchar|sql_wlongvarchar|ssl|ssl_port|standard|standby|start|start_date|started|stat_header|state|statement|static|statistics|statistics_incremental|statistics_norecompute|statistics_only|statman|stats|stats_stream|status|stop|stop_on_error|stopat|stopatmark|stopbeforemark|stoplist|stopped|string_delimiter|subject|supplemental_logging|supported|suspend|symmetric|synchronous_commit|synonym|sysname|system|system_time|system_versioning|table|tableresults|tablockx??|take|tape|target|target_index|target_partition|target_recovery_time|tcp|temporal_history_retention|text|textimage_on|then|thesaurus|throw|time|timeout|timestamp|tinyint|top??|torn_page_detection|track_columns_updated|trailing|tran|transaction|transfer|transform_noise_words|triple_des|triple_des_3key|truncate|trustworthy|try|tsql|two_digit_year_cutoff|type|type_desc|type_warning|tzoffset|uid|unbounded|uncommitted|unique|uniqueidentifier|unlimited|unload|unlock|unsafe|updlock|url|use|useplan|useroptions|use_type_default|using|utcdatetime|valid_xml|validation|values??|varbinary|varchar|vector|verbose|verifyonly|version|view_metadata|virtual_device|visiblity|wait_at_low_priority|waitfor|webmethod|week|weekday|weight|well_formed_xml|when|while|widechar|widechar_ansi|widenative|windows??|with|within|within group|witness|without|without_array_wrapper|workload|wsdl|xact_abort|xlock|xml|xmlschema|xquery|xsinil|year|zone)\\\\b\",\"name\":\"keyword.other.sql\"},{\"captures\":{\"1\":{\"name\":\"punctuation.section.scope.begin.sql\"},\"2\":{\"name\":\"punctuation.section.scope.end.sql\"}},\"match\":\"(\\\\()(\\\\))\",\"name\":\"meta.block.sql\"}],\"repository\":{\"comment-block\":{\"begin\":\"/\\\\*\",\"captures\":{\"0\":{\"name\":\"punctuation.definition.comment.sql\"}},\"end\":\"\\\\*/\",\"name\":\"comment.block\",\"patterns\":[{\"include\":\"#comment-block\"}]},\"comments\":{\"patterns\":[{\"begin\":\"(^[\\\\t ]+)?(?=--)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.whitespace.comment.leading.sql\"}},\"end\":\"(?!\\\\G)\",\"patterns\":[{\"begin\":\"--\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.sql\"}},\"end\":\"\\\\n\",\"name\":\"comment.line.double-dash.sql\"}]},{\"begin\":\"(^[\\\\t ]+)?(?=#)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.whitespace.comment.leading.sql\"}},\"end\":\"(?!\\\\G)\",\"patterns\":[]},{\"include\":\"#comment-block\"}]},\"regexps\":{\"patterns\":[{\"begin\":\"/(?=\\\\S.*/)\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.sql\"}},\"end\":\"/\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.sql\"}},\"name\":\"string.regexp.sql\",\"patterns\":[{\"include\":\"#string_interpolation\"},{\"match\":\"\\\\\\\\/\",\"name\":\"constant.character.escape.slash.sql\"}]},{\"begin\":\"%r\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.sql\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.sql\"}},\"name\":\"string.regexp.modr.sql\",\"patterns\":[{\"include\":\"#string_interpolation\"}]}]},\"string_escape\":{\"match\":\"\\\\\\\\.\",\"name\":\"constant.character.escape.sql\"},\"string_interpolation\":{\"captures\":{\"1\":{\"name\":\"punctuation.definition.string.begin.sql\"},\"3\":{\"name\":\"punctuation.definition.string.end.sql\"}},\"match\":\"(#\\\\{)([^}]*)(})\",\"name\":\"string.interpolated.sql\"},\"strings\":{\"patterns\":[{\"captures\":{\"2\":{\"name\":\"punctuation.definition.string.begin.sql\"},\"3\":{\"name\":\"punctuation.definition.string.end.sql\"}},\"match\":\"(N)?(')[^']*(')\",\"name\":\"string.quoted.single.sql\"},{\"begin\":\"'\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.sql\"}},\"end\":\"'\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.sql\"}},\"name\":\"string.quoted.single.sql\",\"patterns\":[{\"include\":\"#string_escape\"}]},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.string.begin.sql\"},\"2\":{\"name\":\"punctuation.definition.string.end.sql\"}},\"match\":\"(`)[^\\\\\\\\`]*(`)\",\"name\":\"string.quoted.other.backtick.sql\"},{\"begin\":\"`\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.sql\"}},\"end\":\"`\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.sql\"}},\"name\":\"string.quoted.other.backtick.sql\",\"patterns\":[{\"include\":\"#string_escape\"}]},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.string.begin.sql\"},\"2\":{\"name\":\"punctuation.definition.string.end.sql\"}},\"match\":\"(\\\")[^\\\"#]*(\\\")\",\"name\":\"string.quoted.double.sql\"},{\"begin\":\"\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.sql\"}},\"end\":\"\\\"\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.sql\"}},\"name\":\"string.quoted.double.sql\",\"patterns\":[{\"include\":\"#string_interpolation\"}]},{\"begin\":\"%\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.sql\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.sql\"}},\"name\":\"string.other.quoted.brackets.sql\",\"patterns\":[{\"include\":\"#string_interpolation\"}]}]}},\"scopeName\":\"source.sql\"}"));
const __TURBOPACK__default__export__ = [
    lang
];
}}),
"[project]/node_modules/@shikijs/langs/dist/graphql.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$javascript$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/javascript.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$typescript$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/typescript.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$jsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/jsx.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$tsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/tsx.mjs [app-client] (ecmascript)");
;
;
;
;
const lang = Object.freeze(JSON.parse("{\"displayName\":\"GraphQL\",\"fileTypes\":[\"graphql\",\"graphqls\",\"gql\",\"graphcool\"],\"name\":\"graphql\",\"patterns\":[{\"include\":\"#graphql\"}],\"repository\":{\"graphql\":{\"patterns\":[{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-description-singleline\"},{\"include\":\"#graphql-fragment-definition\"},{\"include\":\"#graphql-directive-definition\"},{\"include\":\"#graphql-type-interface\"},{\"include\":\"#graphql-enum\"},{\"include\":\"#graphql-scalar\"},{\"include\":\"#graphql-union\"},{\"include\":\"#graphql-schema\"},{\"include\":\"#graphql-operation-def\"},{\"include\":\"#literal-quasi-embedded\"}]},\"graphql-ampersand\":{\"captures\":{\"1\":{\"name\":\"keyword.operator.logical.graphql\"}},\"match\":\"\\\\s*(&)\"},\"graphql-arguments\":{\"begin\":\"\\\\s*(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"meta.brace.round.directive.graphql\"}},\"end\":\"\\\\s*(\\\\))\",\"endCaptures\":{\"1\":{\"name\":\"meta.brace.round.directive.graphql\"}},\"name\":\"meta.arguments.graphql\",\"patterns\":[{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-description-singleline\"},{\"begin\":\"\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)\\\\s*(:)\",\"beginCaptures\":{\"1\":{\"name\":\"variable.parameter.graphql\"},\"2\":{\"name\":\"punctuation.colon.graphql\"}},\"end\":\"(?=\\\\s*(?:([A-Z_a-z][0-9A-Z_a-z]*)\\\\s*(:)|\\\\)))|\\\\s*(,)\",\"endCaptures\":{\"3\":{\"name\":\"punctuation.comma.graphql\"}},\"patterns\":[{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-description-singleline\"},{\"include\":\"#graphql-directive\"},{\"include\":\"#graphql-value\"},{\"include\":\"#graphql-skip-newlines\"}]},{\"include\":\"#literal-quasi-embedded\"}]},\"graphql-boolean-value\":{\"captures\":{\"1\":{\"name\":\"constant.language.boolean.graphql\"}},\"match\":\"\\\\s*\\\\b(true|false)\\\\b\"},\"graphql-colon\":{\"captures\":{\"1\":{\"name\":\"punctuation.colon.graphql\"}},\"match\":\"\\\\s*(:)\"},\"graphql-comma\":{\"captures\":{\"1\":{\"name\":\"punctuation.comma.graphql\"}},\"match\":\"\\\\s*(,)\"},\"graphql-comment\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"punctuation.whitespace.comment.leading.graphql\"}},\"match\":\"(\\\\s*)(#).*\",\"name\":\"comment.line.graphql.js\"},{\"begin\":\"(\\\"\\\"\\\")\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.whitespace.comment.leading.graphql\"}},\"end\":\"(\\\"\\\"\\\")\",\"name\":\"comment.line.graphql.js\"},{\"begin\":\"(\\\")\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.whitespace.comment.leading.graphql\"}},\"end\":\"(\\\")\",\"name\":\"comment.line.graphql.js\"}]},\"graphql-description-docstring\":{\"begin\":\"\\\"\\\"\\\"\",\"end\":\"\\\"\\\"\\\"\",\"name\":\"comment.block.graphql\"},\"graphql-description-singleline\":{\"match\":\"#(?=([^\\\"]*\\\"[^\\\"]*\\\")*[^\\\"]*$).*$\",\"name\":\"comment.line.number-sign.graphql\"},\"graphql-directive\":{\"applyEndPatternLast\":1,\"begin\":\"\\\\s*((@)\\\\s*([A-Z_a-z][0-9A-Z_a-z]*))\",\"beginCaptures\":{\"1\":{\"name\":\"entity.name.function.directive.graphql\"}},\"end\":\"(?=.)\",\"patterns\":[{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-description-singleline\"},{\"include\":\"#graphql-arguments\"},{\"include\":\"#literal-quasi-embedded\"},{\"include\":\"#graphql-skip-newlines\"}]},\"graphql-directive-definition\":{\"applyEndPatternLast\":1,\"begin\":\"\\\\s*\\\\b(directive)\\\\b\\\\s*(@[A-Z_a-z][0-9A-Z_a-z]*)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.directive.graphql\"},\"2\":{\"name\":\"entity.name.function.directive.graphql\"},\"3\":{\"name\":\"keyword.on.graphql\"},\"4\":{\"name\":\"support.type.graphql\"}},\"end\":\"(?=.)\",\"patterns\":[{\"include\":\"#graphql-variable-definitions\"},{\"applyEndPatternLast\":1,\"begin\":\"\\\\s*\\\\b(on)\\\\b\\\\s*([A-Z_a-z]*)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.on.graphql\"},\"2\":{\"name\":\"support.type.location.graphql\"}},\"end\":\"(?=.)\",\"patterns\":[{\"include\":\"#graphql-skip-newlines\"},{\"include\":\"#graphql-comment\"},{\"include\":\"#literal-quasi-embedded\"},{\"captures\":{\"2\":{\"name\":\"support.type.location.graphql\"}},\"match\":\"\\\\s*(\\\\|)\\\\s*([A-Z_a-z]*)\"}]},{\"include\":\"#graphql-skip-newlines\"},{\"include\":\"#graphql-comment\"},{\"include\":\"#literal-quasi-embedded\"}]},\"graphql-enum\":{\"begin\":\"\\\\s*+\\\\b(enum)\\\\b\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.enum.graphql\"},\"2\":{\"name\":\"support.type.enum.graphql\"}},\"end\":\"(?<=})\",\"name\":\"meta.enum.graphql\",\"patterns\":[{\"begin\":\"\\\\s*(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.operation.graphql\"}},\"end\":\"\\\\s*(})\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.operation.graphql\"}},\"name\":\"meta.type.object.graphql\",\"patterns\":[{\"include\":\"#graphql-object-type\"},{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-description-singleline\"},{\"include\":\"#graphql-directive\"},{\"include\":\"#graphql-enum-value\"},{\"include\":\"#literal-quasi-embedded\"}]},{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-description-singleline\"},{\"include\":\"#graphql-directive\"}]},\"graphql-enum-value\":{\"match\":\"\\\\s*(?!=\\\\b(true|false|null)\\\\b)([A-Z_a-z][0-9A-Z_a-z]*)\",\"name\":\"constant.character.enum.graphql\"},\"graphql-field\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"string.unquoted.alias.graphql\"},\"2\":{\"name\":\"punctuation.colon.graphql\"}},\"match\":\"\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)\\\\s*(:)\"},{\"captures\":{\"1\":{\"name\":\"variable.graphql\"}},\"match\":\"\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)\"},{\"include\":\"#graphql-arguments\"},{\"include\":\"#graphql-directive\"},{\"include\":\"#graphql-selection-set\"},{\"include\":\"#literal-quasi-embedded\"},{\"include\":\"#graphql-skip-newlines\"}]},\"graphql-float-value\":{\"captures\":{\"1\":{\"name\":\"constant.numeric.float.graphql\"}},\"match\":\"\\\\s*(-?(0|[1-9][0-9]*)(\\\\.[0-9]+)?(([Ee])([-+])?[0-9]+)?)\"},\"graphql-fragment-definition\":{\"begin\":\"\\\\s*\\\\b(fragment)\\\\b\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)?\\\\s*\\\\b(on)\\\\b\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)\",\"captures\":{\"1\":{\"name\":\"keyword.fragment.graphql\"},\"2\":{\"name\":\"entity.name.fragment.graphql\"},\"3\":{\"name\":\"keyword.on.graphql\"},\"4\":{\"name\":\"support.type.graphql\"}},\"end\":\"(?<=})\",\"name\":\"meta.fragment.graphql\",\"patterns\":[{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-description-singleline\"},{\"include\":\"#graphql-selection-set\"},{\"include\":\"#graphql-directive\"},{\"include\":\"#graphql-skip-newlines\"},{\"include\":\"#literal-quasi-embedded\"}]},\"graphql-fragment-spread\":{\"applyEndPatternLast\":1,\"begin\":\"\\\\s*(\\\\.\\\\.\\\\.)\\\\s*(?!\\\\bon\\\\b)([A-Z_a-z][0-9A-Z_a-z]*)\",\"captures\":{\"1\":{\"name\":\"keyword.operator.spread.graphql\"},\"2\":{\"name\":\"variable.fragment.graphql\"}},\"end\":\"(?=.)\",\"patterns\":[{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-description-singleline\"},{\"include\":\"#graphql-selection-set\"},{\"include\":\"#graphql-directive\"},{\"include\":\"#literal-quasi-embedded\"},{\"include\":\"#graphql-skip-newlines\"}]},\"graphql-ignore-spaces\":{\"match\":\"\\\\s*\"},\"graphql-inline-fragment\":{\"applyEndPatternLast\":1,\"begin\":\"\\\\s*(\\\\.\\\\.\\\\.)\\\\s*(?:\\\\b(on)\\\\b\\\\s*([A-Z_a-z][0-9A-Z_a-z]*))?\",\"captures\":{\"1\":{\"name\":\"keyword.operator.spread.graphql\"},\"2\":{\"name\":\"keyword.on.graphql\"},\"3\":{\"name\":\"support.type.graphql\"}},\"end\":\"(?=.)\",\"patterns\":[{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-description-singleline\"},{\"include\":\"#graphql-selection-set\"},{\"include\":\"#graphql-directive\"},{\"include\":\"#graphql-skip-newlines\"},{\"include\":\"#literal-quasi-embedded\"}]},\"graphql-input-types\":{\"patterns\":[{\"include\":\"#graphql-scalar-type\"},{\"captures\":{\"1\":{\"name\":\"support.type.graphql\"},\"2\":{\"name\":\"keyword.operator.nulltype.graphql\"}},\"match\":\"\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)(?:\\\\s*(!))?\"},{\"begin\":\"\\\\s*(\\\\[)\",\"captures\":{\"1\":{\"name\":\"meta.brace.square.graphql\"},\"2\":{\"name\":\"keyword.operator.nulltype.graphql\"}},\"end\":\"\\\\s*(])(?:\\\\s*(!))?\",\"name\":\"meta.type.list.graphql\",\"patterns\":[{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-description-singleline\"},{\"include\":\"#graphql-input-types\"},{\"include\":\"#graphql-comma\"},{\"include\":\"#literal-quasi-embedded\"}]}]},\"graphql-list-value\":{\"patterns\":[{\"begin\":\"\\\\s*+(\\\\[)\",\"beginCaptures\":{\"1\":{\"name\":\"meta.brace.square.graphql\"}},\"end\":\"\\\\s*(])\",\"endCaptures\":{\"1\":{\"name\":\"meta.brace.square.graphql\"}},\"name\":\"meta.listvalues.graphql\",\"patterns\":[{\"include\":\"#graphql-value\"}]}]},\"graphql-name\":{\"captures\":{\"1\":{\"name\":\"entity.name.function.graphql\"}},\"match\":\"\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)\"},\"graphql-null-value\":{\"captures\":{\"1\":{\"name\":\"constant.language.null.graphql\"}},\"match\":\"\\\\s*\\\\b(null)\\\\b\"},\"graphql-object-field\":{\"captures\":{\"1\":{\"name\":\"constant.object.key.graphql\"},\"2\":{\"name\":\"string.unquoted.graphql\"},\"3\":{\"name\":\"punctuation.graphql\"}},\"match\":\"\\\\s*(([A-Z_a-z][0-9A-Z_a-z]*))\\\\s*(:)\"},\"graphql-object-value\":{\"patterns\":[{\"begin\":\"\\\\s*+(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"meta.brace.curly.graphql\"}},\"end\":\"\\\\s*(})\",\"endCaptures\":{\"1\":{\"name\":\"meta.brace.curly.graphql\"}},\"name\":\"meta.objectvalues.graphql\",\"patterns\":[{\"include\":\"#graphql-object-field\"},{\"include\":\"#graphql-value\"}]}]},\"graphql-operation-def\":{\"patterns\":[{\"include\":\"#graphql-query-mutation\"},{\"include\":\"#graphql-name\"},{\"include\":\"#graphql-variable-definitions\"},{\"include\":\"#graphql-directive\"},{\"include\":\"#graphql-selection-set\"}]},\"graphql-query-mutation\":{\"captures\":{\"1\":{\"name\":\"keyword.operation.graphql\"}},\"match\":\"\\\\s*\\\\b(query|mutation)\\\\b\"},\"graphql-scalar\":{\"captures\":{\"1\":{\"name\":\"keyword.scalar.graphql\"},\"2\":{\"name\":\"entity.scalar.graphql\"}},\"match\":\"\\\\s*\\\\b(scalar)\\\\b\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)\"},\"graphql-scalar-type\":{\"captures\":{\"1\":{\"name\":\"support.type.builtin.graphql\"},\"2\":{\"name\":\"keyword.operator.nulltype.graphql\"}},\"match\":\"\\\\s*\\\\b(Int|Float|String|Boolean|ID)\\\\b(?:\\\\s*(!))?\"},\"graphql-schema\":{\"begin\":\"\\\\s*\\\\b(schema)\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.schema.graphql\"}},\"end\":\"(?<=})\",\"patterns\":[{\"begin\":\"\\\\s*(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.operation.graphql\"}},\"end\":\"\\\\s*(})\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.operation.graphql\"}},\"patterns\":[{\"begin\":\"\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)(?=\\\\s*\\\\(|:)\",\"beginCaptures\":{\"1\":{\"name\":\"variable.arguments.graphql\"}},\"end\":\"(?=\\\\s*(([A-Z_a-z][0-9A-Z_a-z]*)\\\\s*([(:])|(})))|\\\\s*(,)\",\"endCaptures\":{\"5\":{\"name\":\"punctuation.comma.graphql\"}},\"patterns\":[{\"captures\":{\"1\":{\"name\":\"support.type.graphql\"}},\"match\":\"\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)\"},{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-description-singleline\"},{\"include\":\"#graphql-colon\"},{\"include\":\"#graphql-skip-newlines\"}]},{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-description-singleline\"},{\"include\":\"#graphql-skip-newlines\"}]},{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-description-singleline\"},{\"include\":\"#graphql-directive\"},{\"include\":\"#graphql-skip-newlines\"}]},\"graphql-selection-set\":{\"begin\":\"\\\\s*(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.operation.graphql\"}},\"end\":\"\\\\s*(})\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.operation.graphql\"}},\"name\":\"meta.selectionset.graphql\",\"patterns\":[{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-description-singleline\"},{\"include\":\"#graphql-field\"},{\"include\":\"#graphql-fragment-spread\"},{\"include\":\"#graphql-inline-fragment\"},{\"include\":\"#graphql-comma\"},{\"include\":\"#native-interpolation\"},{\"include\":\"#literal-quasi-embedded\"}]},\"graphql-skip-newlines\":{\"match\":\"\\\\s*\\\\n\"},\"graphql-string-content\":{\"patterns\":[{\"match\":\"\\\\\\\\[\\\"'/\\\\\\\\bfnrt]\",\"name\":\"constant.character.escape.graphql\"},{\"match\":\"\\\\\\\\u(\\\\h{4})\",\"name\":\"constant.character.escape.graphql\"}]},\"graphql-string-value\":{\"begin\":\"\\\\s*+((\\\"))\",\"beginCaptures\":{\"1\":{\"name\":\"string.quoted.double.graphql\"},\"2\":{\"name\":\"punctuation.definition.string.begin.graphql\"}},\"contentName\":\"string.quoted.double.graphql\",\"end\":\"\\\\s*+(?:((\\\"))|(\\\\n))\",\"endCaptures\":{\"1\":{\"name\":\"string.quoted.double.graphql\"},\"2\":{\"name\":\"punctuation.definition.string.end.graphql\"},\"3\":{\"name\":\"invalid.illegal.newline.graphql\"}},\"patterns\":[{\"include\":\"#graphql-string-content\"},{\"include\":\"#literal-quasi-embedded\"}]},\"graphql-type-definition\":{\"begin\":\"\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)(?=\\\\s*\\\\(|:)\",\"beginCaptures\":{\"1\":{\"name\":\"variable.graphql\"}},\"end\":\"(?=\\\\s*(([A-Z_a-z][0-9A-Z_a-z]*)\\\\s*([(:])|(})))|\\\\s*(,)\",\"endCaptures\":{\"5\":{\"name\":\"punctuation.comma.graphql\"}},\"patterns\":[{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-description-singleline\"},{\"include\":\"#graphql-directive\"},{\"include\":\"#graphql-variable-definitions\"},{\"include\":\"#graphql-type-object\"},{\"include\":\"#graphql-colon\"},{\"include\":\"#graphql-input-types\"},{\"include\":\"#literal-quasi-embedded\"}]},\"graphql-type-interface\":{\"applyEndPatternLast\":1,\"begin\":\"\\\\s*\\\\b(?:(extends?)?\\\\b\\\\s*\\\\b(type)|(interface)|(input))\\\\b\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)?\",\"captures\":{\"1\":{\"name\":\"keyword.type.graphql\"},\"2\":{\"name\":\"keyword.type.graphql\"},\"3\":{\"name\":\"keyword.interface.graphql\"},\"4\":{\"name\":\"keyword.input.graphql\"},\"5\":{\"name\":\"support.type.graphql\"}},\"end\":\"(?=.)\",\"name\":\"meta.type.interface.graphql\",\"patterns\":[{\"begin\":\"\\\\s*\\\\b(implements)\\\\b\\\\s*\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.implements.graphql\"}},\"end\":\"\\\\s*(?=\\\\{)\",\"patterns\":[{\"captures\":{\"1\":{\"name\":\"support.type.graphql\"}},\"match\":\"\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)\"},{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-description-singleline\"},{\"include\":\"#graphql-directive\"},{\"include\":\"#graphql-ampersand\"},{\"include\":\"#graphql-comma\"}]},{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-description-singleline\"},{\"include\":\"#graphql-directive\"},{\"include\":\"#graphql-type-object\"},{\"include\":\"#literal-quasi-embedded\"},{\"include\":\"#graphql-ignore-spaces\"}]},\"graphql-type-object\":{\"begin\":\"\\\\s*(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.operation.graphql\"}},\"end\":\"\\\\s*(})\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.operation.graphql\"}},\"name\":\"meta.type.object.graphql\",\"patterns\":[{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-description-singleline\"},{\"include\":\"#graphql-object-type\"},{\"include\":\"#graphql-type-definition\"},{\"include\":\"#literal-quasi-embedded\"}]},\"graphql-union\":{\"applyEndPatternLast\":1,\"begin\":\"\\\\s*\\\\b(union)\\\\b\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)\",\"captures\":{\"1\":{\"name\":\"keyword.union.graphql\"},\"2\":{\"name\":\"support.type.graphql\"}},\"end\":\"(?=.)\",\"patterns\":[{\"applyEndPatternLast\":1,\"begin\":\"\\\\s*(=)\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)\",\"captures\":{\"1\":{\"name\":\"punctuation.assignment.graphql\"},\"2\":{\"name\":\"support.type.graphql\"}},\"end\":\"(?=.)\",\"patterns\":[{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-description-singleline\"},{\"include\":\"#graphql-skip-newlines\"},{\"include\":\"#literal-quasi-embedded\"},{\"captures\":{\"1\":{\"name\":\"punctuation.or.graphql\"},\"2\":{\"name\":\"support.type.graphql\"}},\"match\":\"\\\\s*(\\\\|)\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)\"}]},{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-description-singleline\"},{\"include\":\"#graphql-skip-newlines\"},{\"include\":\"#literal-quasi-embedded\"}]},\"graphql-union-mark\":{\"captures\":{\"1\":{\"name\":\"punctuation.union.graphql\"}},\"match\":\"\\\\s*(\\\\|)\"},\"graphql-value\":{\"patterns\":[{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-variable-name\"},{\"include\":\"#graphql-float-value\"},{\"include\":\"#graphql-string-value\"},{\"include\":\"#graphql-boolean-value\"},{\"include\":\"#graphql-null-value\"},{\"include\":\"#graphql-enum-value\"},{\"include\":\"#graphql-list-value\"},{\"include\":\"#graphql-object-value\"},{\"include\":\"#literal-quasi-embedded\"}]},\"graphql-variable-assignment\":{\"applyEndPatternLast\":1,\"begin\":\"\\\\s(=)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.assignment.graphql\"}},\"end\":\"(?=[\\\\n),])\",\"patterns\":[{\"include\":\"#graphql-value\"}]},\"graphql-variable-definition\":{\"begin\":\"\\\\s*(\\\\$?[A-Z_a-z][0-9A-Z_a-z]*)(?=\\\\s*\\\\(|:)\",\"beginCaptures\":{\"1\":{\"name\":\"variable.parameter.graphql\"}},\"end\":\"(?=\\\\s*((\\\\$?[A-Z_a-z][0-9A-Z_a-z]*)\\\\s*([(:])|([)}])))|\\\\s*(,)\",\"endCaptures\":{\"5\":{\"name\":\"punctuation.comma.graphql\"}},\"name\":\"meta.variables.graphql\",\"patterns\":[{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-description-singleline\"},{\"include\":\"#graphql-directive\"},{\"include\":\"#graphql-colon\"},{\"include\":\"#graphql-input-types\"},{\"include\":\"#graphql-variable-assignment\"},{\"include\":\"#literal-quasi-embedded\"},{\"include\":\"#graphql-skip-newlines\"}]},\"graphql-variable-definitions\":{\"begin\":\"\\\\s*(\\\\()\",\"captures\":{\"1\":{\"name\":\"meta.brace.round.graphql\"}},\"end\":\"\\\\s*(\\\\))\",\"patterns\":[{\"include\":\"#graphql-comment\"},{\"include\":\"#graphql-description-docstring\"},{\"include\":\"#graphql-description-singleline\"},{\"include\":\"#graphql-variable-definition\"},{\"include\":\"#literal-quasi-embedded\"}]},\"graphql-variable-name\":{\"captures\":{\"1\":{\"name\":\"variable.graphql\"}},\"match\":\"\\\\s*(\\\\$[A-Z_a-z][0-9A-Z_a-z]*)\"},\"native-interpolation\":{\"begin\":\"\\\\s*(\\\\$\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.other.substitution.begin\"}},\"end\":\"(})\",\"endCaptures\":{\"1\":{\"name\":\"keyword.other.substitution.end\"}},\"name\":\"native.interpolation\",\"patterns\":[{\"include\":\"source.js\"},{\"include\":\"source.ts\"},{\"include\":\"source.js.jsx\"},{\"include\":\"source.tsx\"}]}},\"scopeName\":\"source.graphql\",\"embeddedLangs\":[\"javascript\",\"typescript\",\"jsx\",\"tsx\"],\"aliases\":[\"gql\"]}"));
const __TURBOPACK__default__export__ = [
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$javascript$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$typescript$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$jsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$tsx$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    lang
];
}}),
"[project]/node_modules/@shikijs/langs/dist/regexp.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const lang = Object.freeze(JSON.parse("{\"displayName\":\"RegExp\",\"fileTypes\":[\"re\"],\"name\":\"regexp\",\"patterns\":[{\"include\":\"#regexp-expression\"}],\"repository\":{\"codetags\":{\"captures\":{\"1\":{\"name\":\"keyword.codetag.notation.python\"}},\"match\":\"\\\\b(NOTE|XXX|HACK|FIXME|BUG|TODO)\\\\b\"},\"fregexp-base-expression\":{\"patterns\":[{\"include\":\"#fregexp-quantifier\"},{\"include\":\"#fstring-formatting-braces\"},{\"match\":\"\\\\{.*?}\"},{\"include\":\"#regexp-base-common\"}]},\"fregexp-quantifier\":{\"match\":\"\\\\{\\\\{(\\\\d+|\\\\d+,(\\\\d+)?|,\\\\d+)}}\",\"name\":\"keyword.operator.quantifier.regexp\"},\"fstring-formatting-braces\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"constant.character.format.placeholder.other.python\"},\"2\":{\"name\":\"invalid.illegal.brace.python\"},\"3\":{\"name\":\"constant.character.format.placeholder.other.python\"}},\"match\":\"(\\\\{)(\\\\s*?)(})\"},{\"match\":\"(\\\\{\\\\{|}})\",\"name\":\"constant.character.escape.python\"}]},\"regexp-backreference\":{\"captures\":{\"1\":{\"name\":\"support.other.parenthesis.regexp punctuation.parenthesis.backreference.named.begin.regexp\"},\"2\":{\"name\":\"entity.name.tag.named.backreference.regexp\"},\"3\":{\"name\":\"support.other.parenthesis.regexp punctuation.parenthesis.backreference.named.end.regexp\"}},\"match\":\"(\\\\()(\\\\?P=\\\\w+(?:\\\\s+\\\\p{alnum}+)?)(\\\\))\",\"name\":\"meta.backreference.named.regexp\"},\"regexp-backreference-number\":{\"captures\":{\"1\":{\"name\":\"entity.name.tag.backreference.regexp\"}},\"match\":\"(\\\\\\\\[1-9]\\\\d?)\",\"name\":\"meta.backreference.regexp\"},\"regexp-base-common\":{\"patterns\":[{\"match\":\"\\\\.\",\"name\":\"support.other.match.any.regexp\"},{\"match\":\"\\\\^\",\"name\":\"support.other.match.begin.regexp\"},{\"match\":\"\\\\$\",\"name\":\"support.other.match.end.regexp\"},{\"match\":\"[*+?]\\\\??\",\"name\":\"keyword.operator.quantifier.regexp\"},{\"match\":\"\\\\|\",\"name\":\"keyword.operator.disjunction.regexp\"},{\"include\":\"#regexp-escape-sequence\"}]},\"regexp-base-expression\":{\"patterns\":[{\"include\":\"#regexp-quantifier\"},{\"include\":\"#regexp-base-common\"}]},\"regexp-character-set\":{\"patterns\":[{\"match\":\"\\\\[\\\\^?](?!.*?])\"},{\"begin\":\"(\\\\[)(\\\\^)?(])?\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.character.set.begin.regexp constant.other.set.regexp\"},\"2\":{\"name\":\"keyword.operator.negation.regexp\"},\"3\":{\"name\":\"constant.character.set.regexp\"}},\"end\":\"(])\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.character.set.end.regexp constant.other.set.regexp\"},\"2\":{\"name\":\"invalid.illegal.newline.python\"}},\"name\":\"meta.character.set.regexp\",\"patterns\":[{\"include\":\"#regexp-charecter-set-escapes\"},{\"match\":\"\\\\N\",\"name\":\"constant.character.set.regexp\"}]}]},\"regexp-charecter-set-escapes\":{\"patterns\":[{\"match\":\"\\\\\\\\[\\\\\\\\abfnrtv]\",\"name\":\"constant.character.escape.regexp\"},{\"include\":\"#regexp-escape-special\"},{\"match\":\"\\\\\\\\([0-7]{1,3})\",\"name\":\"constant.character.escape.regexp\"},{\"include\":\"#regexp-escape-character\"},{\"include\":\"#regexp-escape-unicode\"},{\"include\":\"#regexp-escape-catchall\"}]},\"regexp-comments\":{\"begin\":\"\\\\(\\\\?#\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.comment.begin.regexp\"}},\"end\":\"(\\\\))\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.comment.end.regexp\"},\"2\":{\"name\":\"invalid.illegal.newline.python\"}},\"name\":\"comment.regexp\",\"patterns\":[{\"include\":\"#codetags\"}]},\"regexp-conditional\":{\"begin\":\"(\\\\()\\\\?\\\\((\\\\w+(?:\\\\s+\\\\p{alnum}+)?|\\\\d+)\\\\)\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.operator.conditional.regexp\"},\"1\":{\"name\":\"punctuation.parenthesis.conditional.begin.regexp\"}},\"end\":\"(\\\\))\",\"endCaptures\":{\"1\":{\"name\":\"keyword.operator.conditional.negative.regexp punctuation.parenthesis.conditional.end.regexp\"},\"2\":{\"name\":\"invalid.illegal.newline.python\"}},\"patterns\":[{\"include\":\"#regexp-expression\"}]},\"regexp-escape-catchall\":{\"match\":\"\\\\\\\\(.|\\\\n)\",\"name\":\"constant.character.escape.regexp\"},\"regexp-escape-character\":{\"match\":\"\\\\\\\\(x\\\\h{2}|0[0-7]{1,2}|[0-7]{3})\",\"name\":\"constant.character.escape.regexp\"},\"regexp-escape-sequence\":{\"patterns\":[{\"include\":\"#regexp-escape-special\"},{\"include\":\"#regexp-escape-character\"},{\"include\":\"#regexp-escape-unicode\"},{\"include\":\"#regexp-backreference-number\"},{\"include\":\"#regexp-escape-catchall\"}]},\"regexp-escape-special\":{\"match\":\"\\\\\\\\([ABDSWZbdsw])\",\"name\":\"support.other.escape.special.regexp\"},\"regexp-escape-unicode\":{\"match\":\"\\\\\\\\(u\\\\h{4}|U\\\\h{8})\",\"name\":\"constant.character.unicode.regexp\"},\"regexp-expression\":{\"patterns\":[{\"include\":\"#regexp-base-expression\"},{\"include\":\"#regexp-character-set\"},{\"include\":\"#regexp-comments\"},{\"include\":\"#regexp-flags\"},{\"include\":\"#regexp-named-group\"},{\"include\":\"#regexp-backreference\"},{\"include\":\"#regexp-lookahead\"},{\"include\":\"#regexp-lookahead-negative\"},{\"include\":\"#regexp-lookbehind\"},{\"include\":\"#regexp-lookbehind-negative\"},{\"include\":\"#regexp-conditional\"},{\"include\":\"#regexp-parentheses-non-capturing\"},{\"include\":\"#regexp-parentheses\"}]},\"regexp-flags\":{\"match\":\"\\\\(\\\\?[Laimsux]+\\\\)\",\"name\":\"storage.modifier.flag.regexp\"},\"regexp-lookahead\":{\"begin\":\"(\\\\()\\\\?=\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.operator.lookahead.regexp\"},\"1\":{\"name\":\"punctuation.parenthesis.lookahead.begin.regexp\"}},\"end\":\"(\\\\))\",\"endCaptures\":{\"1\":{\"name\":\"keyword.operator.lookahead.regexp punctuation.parenthesis.lookahead.end.regexp\"},\"2\":{\"name\":\"invalid.illegal.newline.python\"}},\"patterns\":[{\"include\":\"#regexp-expression\"}]},\"regexp-lookahead-negative\":{\"begin\":\"(\\\\()\\\\?!\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.operator.lookahead.negative.regexp\"},\"1\":{\"name\":\"punctuation.parenthesis.lookahead.begin.regexp\"}},\"end\":\"(\\\\))\",\"endCaptures\":{\"1\":{\"name\":\"keyword.operator.lookahead.negative.regexp punctuation.parenthesis.lookahead.end.regexp\"},\"2\":{\"name\":\"invalid.illegal.newline.python\"}},\"patterns\":[{\"include\":\"#regexp-expression\"}]},\"regexp-lookbehind\":{\"begin\":\"(\\\\()\\\\?<=\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.operator.lookbehind.regexp\"},\"1\":{\"name\":\"punctuation.parenthesis.lookbehind.begin.regexp\"}},\"end\":\"(\\\\))\",\"endCaptures\":{\"1\":{\"name\":\"keyword.operator.lookbehind.regexp punctuation.parenthesis.lookbehind.end.regexp\"},\"2\":{\"name\":\"invalid.illegal.newline.python\"}},\"patterns\":[{\"include\":\"#regexp-expression\"}]},\"regexp-lookbehind-negative\":{\"begin\":\"(\\\\()\\\\?<!\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.operator.lookbehind.negative.regexp\"},\"1\":{\"name\":\"punctuation.parenthesis.lookbehind.begin.regexp\"}},\"end\":\"(\\\\))\",\"endCaptures\":{\"1\":{\"name\":\"keyword.operator.lookbehind.negative.regexp punctuation.parenthesis.lookbehind.end.regexp\"},\"2\":{\"name\":\"invalid.illegal.newline.python\"}},\"patterns\":[{\"include\":\"#regexp-expression\"}]},\"regexp-named-group\":{\"begin\":\"(\\\\()(\\\\?P<\\\\w+(?:\\\\s+\\\\p{alnum}+)?>)\",\"beginCaptures\":{\"1\":{\"name\":\"support.other.parenthesis.regexp punctuation.parenthesis.named.begin.regexp\"},\"2\":{\"name\":\"entity.name.tag.named.group.regexp\"}},\"end\":\"(\\\\))\",\"endCaptures\":{\"1\":{\"name\":\"support.other.parenthesis.regexp punctuation.parenthesis.named.end.regexp\"},\"2\":{\"name\":\"invalid.illegal.newline.python\"}},\"name\":\"meta.named.regexp\",\"patterns\":[{\"include\":\"#regexp-expression\"}]},\"regexp-parentheses\":{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"support.other.parenthesis.regexp punctuation.parenthesis.begin.regexp\"}},\"end\":\"(\\\\))\",\"endCaptures\":{\"1\":{\"name\":\"support.other.parenthesis.regexp punctuation.parenthesis.end.regexp\"},\"2\":{\"name\":\"invalid.illegal.newline.python\"}},\"patterns\":[{\"include\":\"#regexp-expression\"}]},\"regexp-parentheses-non-capturing\":{\"begin\":\"\\\\(\\\\?:\",\"beginCaptures\":{\"0\":{\"name\":\"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.begin.regexp\"}},\"end\":\"(\\\\))\",\"endCaptures\":{\"1\":{\"name\":\"support.other.parenthesis.regexp punctuation.parenthesis.non-capturing.end.regexp\"},\"2\":{\"name\":\"invalid.illegal.newline.python\"}},\"patterns\":[{\"include\":\"#regexp-expression\"}]},\"regexp-quantifier\":{\"match\":\"\\\\{(\\\\d+|\\\\d+,(\\\\d+)?|,\\\\d+)}\",\"name\":\"keyword.operator.quantifier.regexp\"}},\"scopeName\":\"source.regexp.python\",\"aliases\":[\"regex\"]}"));
const __TURBOPACK__default__export__ = [
    lang
];
}}),
"[project]/node_modules/@shikijs/langs/dist/c.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const lang = Object.freeze(JSON.parse("{\"displayName\":\"C\",\"name\":\"c\",\"patterns\":[{\"include\":\"#preprocessor-rule-enabled\"},{\"include\":\"#preprocessor-rule-disabled\"},{\"include\":\"#preprocessor-rule-conditional\"},{\"include\":\"#predefined_macros\"},{\"include\":\"#comments\"},{\"include\":\"#switch_statement\"},{\"include\":\"#anon_pattern_1\"},{\"include\":\"#storage_types\"},{\"include\":\"#anon_pattern_2\"},{\"include\":\"#anon_pattern_3\"},{\"include\":\"#anon_pattern_4\"},{\"include\":\"#anon_pattern_5\"},{\"include\":\"#anon_pattern_6\"},{\"include\":\"#anon_pattern_7\"},{\"include\":\"#operators\"},{\"include\":\"#numbers\"},{\"include\":\"#strings\"},{\"include\":\"#anon_pattern_range_1\"},{\"include\":\"#anon_pattern_range_2\"},{\"include\":\"#anon_pattern_range_3\"},{\"include\":\"#pragma-mark\"},{\"include\":\"#anon_pattern_range_4\"},{\"include\":\"#anon_pattern_range_5\"},{\"include\":\"#anon_pattern_range_6\"},{\"include\":\"#anon_pattern_8\"},{\"include\":\"#anon_pattern_9\"},{\"include\":\"#anon_pattern_10\"},{\"include\":\"#anon_pattern_11\"},{\"include\":\"#anon_pattern_12\"},{\"include\":\"#anon_pattern_13\"},{\"include\":\"#block\"},{\"include\":\"#parens\"},{\"include\":\"#anon_pattern_range_7\"},{\"include\":\"#line_continuation_character\"},{\"include\":\"#anon_pattern_range_8\"},{\"include\":\"#anon_pattern_range_9\"},{\"include\":\"#anon_pattern_14\"},{\"include\":\"#anon_pattern_15\"}],\"repository\":{\"access-method\":{\"begin\":\"([A-Z_a-z][0-9A-Z_a-z]*|(?<=[])]))\\\\s*(?:(\\\\.)|(->))((?:[A-Z_a-z][0-9A-Z_a-z]*\\\\s*(?:\\\\.|->))*)\\\\s*([A-Z_a-z][0-9A-Z_a-z]*)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"variable.object.c\"},\"2\":{\"name\":\"punctuation.separator.dot-access.c\"},\"3\":{\"name\":\"punctuation.separator.pointer-access.c\"},\"4\":{\"patterns\":[{\"match\":\"\\\\.\",\"name\":\"punctuation.separator.dot-access.c\"},{\"match\":\"->\",\"name\":\"punctuation.separator.pointer-access.c\"},{\"match\":\"[A-Z_a-z][0-9A-Z_a-z]*\",\"name\":\"variable.object.c\"},{\"match\":\".+\",\"name\":\"everything.else.c\"}]},\"5\":{\"name\":\"entity.name.function.member.c\"},\"6\":{\"name\":\"punctuation.section.arguments.begin.bracket.round.function.member.c\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.arguments.end.bracket.round.function.member.c\"}},\"name\":\"meta.function-call.member.c\",\"patterns\":[{\"include\":\"#function-call-innards\"}]},\"anon_pattern_1\":{\"match\":\"\\\\b(break|continue|do|else|for|goto|if|_Pragma|return|while)\\\\b\",\"name\":\"keyword.control.c\"},\"anon_pattern_10\":{\"match\":\"\\\\b((?:int8|int16|int32|int64|uint8|uint16|uint32|uint64|int_least8|int_least16|int_least32|int_least64|uint_least8|uint_least16|uint_least32|uint_least64|int_fast8|int_fast16|int_fast32|int_fast64|uint_fast8|uint_fast16|uint_fast32|uint_fast64|intptr|uintptr|intmax|uintmax)_t)\\\\b\",\"name\":\"support.type.stdint.c\"},\"anon_pattern_11\":{\"match\":\"\\\\b(noErr|kNilOptions|kInvalidID|kVariableLengthArray)\\\\b\",\"name\":\"support.constant.mac-classic.c\"},\"anon_pattern_12\":{\"match\":\"\\\\b(AbsoluteTime|Boolean|Byte|ByteCount|ByteOffset|BytePtr|CompTimeValue|ConstLogicalAddress|ConstStrFileNameParam|ConstStringPtr|Duration|Fixed|FixedPtr|Float32|Float32Point|Float64|Float80|Float96|FourCharCode|Fract|FractPtr|Handle|ItemCount|LogicalAddress|OptionBits|OSErr|OSStatus|OSType|OSTypePtr|PhysicalAddress|ProcessSerialNumber|ProcessSerialNumberPtr|ProcHandle|Ptr|ResType|ResTypePtr|ShortFixed|ShortFixedPtr|SignedByte|SInt16|SInt32|SInt64|SInt8|Size|StrFileName|StringHandle|StringPtr|TimeBase|TimeRecord|TimeScale|TimeValue|TimeValue64|UInt16|UInt32|UInt64|UInt8|UniChar|UniCharCount|UniCharCountPtr|UniCharPtr|UnicodeScalarValue|UniversalProcHandle|UniversalProcPtr|UnsignedFixed|UnsignedFixedPtr|UnsignedWide|UTF16Char|UTF32Char|UTF8Char)\\\\b\",\"name\":\"support.type.mac-classic.c\"},\"anon_pattern_13\":{\"match\":\"\\\\b([0-9A-Z_a-z]+_t)\\\\b\",\"name\":\"support.type.posix-reserved.c\"},\"anon_pattern_14\":{\"match\":\";\",\"name\":\"punctuation.terminator.statement.c\"},\"anon_pattern_15\":{\"match\":\",\",\"name\":\"punctuation.separator.delimiter.c\"},\"anon_pattern_2\":{\"match\":\"typedef\",\"name\":\"keyword.other.typedef.c\"},\"anon_pattern_3\":{\"match\":\"\\\\b(const|extern|register|restrict|static|volatile|inline)\\\\b\",\"name\":\"storage.modifier.c\"},\"anon_pattern_4\":{\"match\":\"\\\\bk[A-Z]\\\\w*\\\\b\",\"name\":\"constant.other.variable.mac-classic.c\"},\"anon_pattern_5\":{\"match\":\"\\\\bg[A-Z]\\\\w*\\\\b\",\"name\":\"variable.other.readwrite.global.mac-classic.c\"},\"anon_pattern_6\":{\"match\":\"\\\\bs[A-Z]\\\\w*\\\\b\",\"name\":\"variable.other.readwrite.static.mac-classic.c\"},\"anon_pattern_7\":{\"match\":\"\\\\b(NULL|true|false|TRUE|FALSE)\\\\b\",\"name\":\"constant.language.c\"},\"anon_pattern_8\":{\"match\":\"\\\\b(u_char|u_short|u_int|u_long|ushort|uint|u_quad_t|quad_t|qaddr_t|caddr_t|daddr_t|div_t|dev_t|fixpt_t|blkcnt_t|blksize_t|gid_t|in_addr_t|in_port_t|ino_t|key_t|mode_t|nlink_t|id_t|pid_t|off_t|segsz_t|swblk_t|uid_t|id_t|clock_t|size_t|ssize_t|time_t|useconds_t|suseconds_t)\\\\b\",\"name\":\"support.type.sys-types.c\"},\"anon_pattern_9\":{\"match\":\"\\\\b(pthread_(?:attr_|cond_|condattr_|mutex_|mutexattr_|once_|rwlock_|rwlockattr_||key_)t)\\\\b\",\"name\":\"support.type.pthread.c\"},\"anon_pattern_range_1\":{\"begin\":\"((?:(?>\\\\s+)|(/\\\\*)((?>(?:[^*]|(?>\\\\*+)[^/])*)((?>\\\\*+)/)))+?|(?:(?:(?:(?:\\\\b|(?<=\\\\W))|(?=\\\\W))|\\\\A)|\\\\Z))((#)\\\\s*define)\\\\b\\\\s+((?<!\\\\w)[A-Z_a-z]\\\\w*(?!\\\\w))(?:(\\\\()([^()\\\\\\\\]+)(\\\\)))?\",\"beginCaptures\":{\"1\":{\"patterns\":[{\"include\":\"#inline_comment\"}]},\"2\":{\"name\":\"comment.block.c punctuation.definition.comment.begin.c\"},\"3\":{\"name\":\"comment.block.c\"},\"4\":{\"patterns\":[{\"match\":\"\\\\*/\",\"name\":\"comment.block.c punctuation.definition.comment.end.c\"},{\"match\":\"\\\\*\",\"name\":\"comment.block.c\"}]},\"5\":{\"name\":\"keyword.control.directive.define.c\"},\"6\":{\"name\":\"punctuation.definition.directive.c\"},\"7\":{\"name\":\"entity.name.function.preprocessor.c\"},\"8\":{\"name\":\"punctuation.definition.parameters.begin.c\"},\"9\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"variable.parameter.preprocessor.c\"}},\"match\":\"(?<=[(,])\\\\s*((?<!\\\\w)[A-Z_a-z]\\\\w*(?!\\\\w))\\\\s*\"},{\"match\":\",\",\"name\":\"punctuation.separator.parameters.c\"},{\"match\":\"\\\\.\\\\.\\\\.\",\"name\":\"ellipses.c punctuation.vararg-ellipses.variable.parameter.preprocessor.c\"}]},\"10\":{\"name\":\"punctuation.definition.parameters.end.c\"}},\"end\":\"(?<!\\\\\\\\)(?=\\\\n)\",\"name\":\"meta.preprocessor.macro.c\",\"patterns\":[{\"include\":\"#preprocessor-rule-define-line-contents\"}]},\"anon_pattern_range_2\":{\"begin\":\"^\\\\s*((#)\\\\s*(error|warning))\\\\b\\\\s*\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.directive.diagnostic.$3.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"end\":\"(?<!\\\\\\\\)(?=\\\\n)\",\"name\":\"meta.preprocessor.diagnostic.c\",\"patterns\":[{\"begin\":\"\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.c\"}},\"end\":\"\\\"|(?<!\\\\\\\\)(?=\\\\s*\\\\n)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.c\"}},\"name\":\"string.quoted.double.c\",\"patterns\":[{\"include\":\"#line_continuation_character\"}]},{\"begin\":\"'\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.c\"}},\"end\":\"'|(?<!\\\\\\\\)(?=\\\\s*\\\\n)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.c\"}},\"name\":\"string.quoted.single.c\",\"patterns\":[{\"include\":\"#line_continuation_character\"}]},{\"begin\":\"[^\\\"']\",\"end\":\"(?<!\\\\\\\\)(?=\\\\s*\\\\n)\",\"name\":\"string.unquoted.single.c\",\"patterns\":[{\"include\":\"#line_continuation_character\"},{\"include\":\"#comments\"}]}]},\"anon_pattern_range_3\":{\"begin\":\"^\\\\s*((#)\\\\s*(i(?:nclude(?:_next)?|mport)))\\\\b\\\\s*\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.directive.$3.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"end\":\"(?=/[*/])|(?<!\\\\\\\\)(?=\\\\n)\",\"name\":\"meta.preprocessor.include.c\",\"patterns\":[{\"include\":\"#line_continuation_character\"},{\"begin\":\"\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.c\"}},\"end\":\"\\\"\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.c\"}},\"name\":\"string.quoted.double.include.c\"},{\"begin\":\"<\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.c\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.c\"}},\"name\":\"string.quoted.other.lt-gt.include.c\"}]},\"anon_pattern_range_4\":{\"begin\":\"^\\\\s*((#)\\\\s*line)\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.directive.line.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"end\":\"(?=/[*/])|(?<!\\\\\\\\)(?=\\\\n)\",\"name\":\"meta.preprocessor.c\",\"patterns\":[{\"include\":\"#strings\"},{\"include\":\"#numbers\"},{\"include\":\"#line_continuation_character\"}]},\"anon_pattern_range_5\":{\"begin\":\"^\\\\s*((#)\\\\s*undef)\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.directive.undef.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"end\":\"(?=/[*/])|(?<!\\\\\\\\)(?=\\\\n)\",\"name\":\"meta.preprocessor.c\",\"patterns\":[{\"match\":\"[$A-Z_a-z][$\\\\w]*\",\"name\":\"entity.name.function.preprocessor.c\"},{\"include\":\"#line_continuation_character\"}]},\"anon_pattern_range_6\":{\"begin\":\"^\\\\s*((#)\\\\s*pragma)\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.directive.pragma.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"end\":\"(?=/[*/])|(?<!\\\\\\\\)(?=\\\\n)\",\"name\":\"meta.preprocessor.pragma.c\",\"patterns\":[{\"include\":\"#strings\"},{\"match\":\"[$A-Z_a-z][-$\\\\w]*\",\"name\":\"entity.other.attribute-name.pragma.preprocessor.c\"},{\"include\":\"#numbers\"},{\"include\":\"#line_continuation_character\"}]},\"anon_pattern_range_7\":{\"begin\":\"(?<!\\\\w)(?!\\\\s*(?:atomic_uint_least64_t|atomic_uint_least16_t|atomic_uint_least32_t|atomic_uint_least8_t|atomic_int_least16_t|atomic_uint_fast64_t|atomic_uint_fast32_t|atomic_int_least64_t|atomic_int_least32_t|pthread_rwlockattr_t|atomic_uint_fast16_t|pthread_mutexattr_t|atomic_int_fast16_t|atomic_uint_fast8_t|atomic_int_fast64_t|atomic_int_least8_t|atomic_int_fast32_t|atomic_int_fast8_t|pthread_condattr_t|pthread_rwlock_t|atomic_uintptr_t|atomic_ptrdiff_t|atomic_uintmax_t|atomic_intmax_t|atomic_char32_t|atomic_intptr_t|atomic_char16_t|pthread_mutex_t|pthread_cond_t|atomic_wchar_t|uint_least64_t|uint_least32_t|uint_least16_t|pthread_once_t|pthread_attr_t|uint_least8_t|int_least32_t|int_least16_t|pthread_key_t|uint_fast32_t|uint_fast64_t|uint_fast16_t|atomic_size_t|atomic_ushort|atomic_ullong|int_least64_t|atomic_ulong|int_least8_t|int_fast16_t|int_fast32_t|int_fast64_t|uint_fast8_t|memory_order|atomic_schar|atomic_uchar|atomic_short|atomic_llong|thread_local|atomic_bool|atomic_uint|atomic_long|int_fast8_t|suseconds_t|atomic_char|atomic_int|useconds_t|_Imaginary|uintmax_t|in_addr_t|in_port_t|_Noreturn|blksize_t|pthread_t|uintptr_t|volatile|u_quad_t|blkcnt_t|intmax_t|intptr_t|_Complex|uint16_t|uint32_t|uint64_t|_Alignof|_Alignas|continue|unsigned|restrict|intmax_t|register|int64_t|qaddr_t|segsz_t|_Atomic|alignas|default|caddr_t|nlink_t|typedef|u_short|fixpt_t|clock_t|swblk_t|ssize_t|alignof|daddr_t|int16_t|int32_t|uint8_t|struct|mode_t|size_t|time_t|ushort|u_long|u_char|int8_t|double|signed|static|extern|inline|return|switch|xor_eq|and_eq|bitand|not_eq|sizeof|quad_t|uid_t|bitor|union|off_t|key_t|ino_t|compl|u_int|short|const|false|while|float|pid_t|break|_Bool|or_eq|div_t|dev_t|gid_t|id_t|long|case|goto|else|bool|auto|id_t|enum|uint|true|NULL|void|char|for|not|int|and|xor|do|or|if)\\\\s*\\\\()(?=[A-Z_a-z]\\\\w*\\\\s*\\\\()\",\"end\":\"(?!\\\\G)(?<=\\\\))\",\"name\":\"meta.function.c\",\"patterns\":[{\"include\":\"#function-innards\"}]},\"anon_pattern_range_8\":{\"begin\":\"([A-Z_a-z][0-9A-Z_a-z]*|(?<=[])]))?(\\\\[)(?!])\",\"beginCaptures\":{\"1\":{\"name\":\"variable.object.c\"},\"2\":{\"name\":\"punctuation.definition.begin.bracket.square.c\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.end.bracket.square.c\"}},\"name\":\"meta.bracket.square.access.c\",\"patterns\":[{\"include\":\"#function-call-innards\"}]},\"anon_pattern_range_9\":{\"match\":\"\\\\[\\\\s*]\",\"name\":\"storage.modifier.array.bracket.square.c\"},\"backslash_escapes\":{\"match\":\"\\\\\\\\([\\\"'?\\\\\\\\abefnprtv]|[0-3][0-7]{0,2}|[4-7]\\\\d?|x\\\\h{0,2}|u\\\\h{0,4}|U\\\\h{0,8})\",\"name\":\"constant.character.escape.c\"},\"block\":{\"patterns\":[{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.block.begin.bracket.curly.c\"}},\"end\":\"}|(?=\\\\s*#\\\\s*e(?:lif|lse|ndif)\\\\b)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.block.end.bracket.curly.c\"}},\"name\":\"meta.block.c\",\"patterns\":[{\"include\":\"#block_innards\"}]}]},\"block_comment\":{\"patterns\":[{\"begin\":\"\\\\s*+(/\\\\*)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.comment.begin.c\"}},\"end\":\"\\\\*/\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.end.c\"}},\"name\":\"comment.block.c\"},{\"begin\":\"\\\\s*+(/\\\\*)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.comment.begin.c\"}},\"end\":\"\\\\*/\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.end.c\"}},\"name\":\"comment.block.c\"}]},\"block_innards\":{\"patterns\":[{\"include\":\"#preprocessor-rule-enabled-block\"},{\"include\":\"#preprocessor-rule-disabled-block\"},{\"include\":\"#preprocessor-rule-conditional-block\"},{\"include\":\"#method_access\"},{\"include\":\"#member_access\"},{\"include\":\"#c_function_call\"},{\"begin\":\"(?=\\\\s)(?<!else|new|return)(?<=\\\\w)\\\\s+(and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)((?:[A-Z_a-z][0-9A-Z_a-z]*+|::)++|(?<=operator)(?:[-!\\\\&*+<=>]+|\\\\(\\\\)|\\\\[]))\\\\s*(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"variable.other.c\"},\"2\":{\"name\":\"punctuation.section.parens.begin.bracket.round.initialization.c\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.parens.end.bracket.round.initialization.c\"}},\"name\":\"meta.initialization.c\",\"patterns\":[{\"include\":\"#function-call-innards\"}]},{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.block.begin.bracket.curly.c\"}},\"end\":\"}|(?=\\\\s*#\\\\s*e(?:lif|lse|ndif)\\\\b)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.block.end.bracket.curly.c\"}},\"patterns\":[{\"include\":\"#block_innards\"}]},{\"include\":\"#parens-block\"},{\"include\":\"$self\"}]},\"c_conditional_context\":{\"patterns\":[{\"include\":\"$self\"},{\"include\":\"#block_innards\"}]},\"c_function_call\":{\"begin\":\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)\\\\s*\\\\()(?=(?:[A-Z_a-z][0-9A-Z_a-z]*+|::)++\\\\s*\\\\(|(?<=operator)(?:[-!\\\\&*+<=>]+|\\\\(\\\\)|\\\\[])\\\\s*\\\\()\",\"end\":\"(?<=\\\\))(?!\\\\w)\",\"name\":\"meta.function-call.c\",\"patterns\":[{\"include\":\"#function-call-innards\"}]},\"case_statement\":{\"begin\":\"((?>(?:(?>(?<!\\\\s)\\\\s+)|(/\\\\*)((?>(?:[^*]|(?>\\\\*+)[^/])*)((?>\\\\*+)/)))+|(?:(?:(?:(?:\\\\b|(?<=\\\\W))|(?=\\\\W))|\\\\A)|\\\\Z)))((?<!\\\\w)case(?!\\\\w))\",\"beginCaptures\":{\"1\":{\"patterns\":[{\"include\":\"#inline_comment\"}]},\"2\":{\"name\":\"comment.block.c punctuation.definition.comment.begin.c\"},\"3\":{\"name\":\"comment.block.c\"},\"4\":{\"patterns\":[{\"match\":\"\\\\*/\",\"name\":\"comment.block.c punctuation.definition.comment.end.c\"},{\"match\":\"\\\\*\",\"name\":\"comment.block.c\"}]},\"5\":{\"name\":\"keyword.control.case.c\"}},\"end\":\"(:)\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.separator.colon.case.c\"}},\"name\":\"meta.conditional.case.c\",\"patterns\":[{\"include\":\"#evaluation_context\"},{\"include\":\"#c_conditional_context\"}]},\"comments\":{\"patterns\":[{\"patterns\":[{\"patterns\":[{\"begin\":\"^(?>\\\\s*)(//[!/]+)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.comment.documentation.c\"}},\"end\":\"(?<=\\\\n)(?<!\\\\\\\\\\\\n)\",\"name\":\"comment.line.double-slash.documentation.c\",\"patterns\":[{\"include\":\"#line_continuation_character\"},{\"match\":\"(?<=[!*/\\\\s])[@\\\\\\\\](?:callergraph|callgraph|else|endif|f\\\\$|f\\\\[|f]|hidecallergraph|hidecallgraph|hiderefby|hiderefs|hideinitializer|htmlinclude|n|nosubgrouping|private|privatesection|protected|protectedsection|public|publicsection|pure|showinitializer|showrefby|showrefs|tableofcontents|[\\\"-%.<=>]|::|\\\\||---??)\\\\b(?:\\\\{[^}]*})?\",\"name\":\"storage.type.class.doxygen.c\"},{\"captures\":{\"1\":{\"name\":\"storage.type.class.doxygen.c\"},\"2\":{\"name\":\"markup.italic.doxygen.c\"}},\"match\":\"((?<=[!*/\\\\s])[@\\\\\\\\](?:a|em?))\\\\s+(\\\\S+)\"},{\"captures\":{\"1\":{\"name\":\"storage.type.class.doxygen.c\"},\"2\":{\"name\":\"markup.bold.doxygen.c\"}},\"match\":\"((?<=[!*/\\\\s])[@\\\\\\\\]b)\\\\s+(\\\\S+)\"},{\"captures\":{\"1\":{\"name\":\"storage.type.class.doxygen.c\"},\"2\":{\"name\":\"markup.inline.raw.string.c\"}},\"match\":\"((?<=[!*/\\\\s])[@\\\\\\\\][cp])\\\\s+(\\\\S+)\"},{\"match\":\"(?<=[!*/\\\\s])[@\\\\\\\\](?:a|anchor|[bc]|cite|copybrief|copydetail|copydoc|def|dir|dontinclude|em??|emoji|enum|example|extends|file|idlexcept|implements|include|includedoc|includelineno|latexinclude|link|memberof|namespace|p|package|ref|refitem|related|relates|relatedalso|relatesalso|verbinclude)\\\\b(?:\\\\{[^}]*})?\",\"name\":\"storage.type.class.doxygen.c\"},{\"match\":\"(?<=[!*/\\\\s])[@\\\\\\\\](?:addindex|addtogroup|category|class|defgroup|diafile|dotfile|elseif|fn|headerfile|if|ifnot|image|ingroup|interface|line|mainpage|mscfile|name|overload|page|property|protocol|section|skip|skipline|snippet|snippetdoc|snippetlineno|struct|subpage|subsection|subsubsection|typedef|union|until|vhdlflow|weakgroup)\\\\b(?:\\\\{[^}]*})?\",\"name\":\"storage.type.class.doxygen.c\"},{\"captures\":{\"1\":{\"name\":\"storage.type.class.doxygen.c\"},\"2\":{\"patterns\":[{\"match\":\"in|out\",\"name\":\"keyword.other.parameter.direction.$0.c\"}]},\"3\":{\"name\":\"variable.parameter.c\"}},\"match\":\"((?<=[!*/\\\\s])[@\\\\\\\\]param)(?:\\\\s*\\\\[((?:,?\\\\s*(?:in|out)\\\\s*)+)])?\\\\s+\\\\b(\\\\w+)\\\\b\"},{\"match\":\"(?<=[!*/\\\\s])[@\\\\\\\\](?:arg|attention|authors??|brief|bug|copyright|date|deprecated|details|exception|invariant|li|note|par|paragraph|param|post|pre|remarks??|result|returns??|retval|sa|see|short|since|test|throw|todo|tparam|version|warning|xrefitem)\\\\b(?:\\\\{[^}]*})?\",\"name\":\"storage.type.class.doxygen.c\"},{\"match\":\"(?<=[!*/\\\\s])[@\\\\\\\\](?:code|cond|docbookonly|dot|htmlonly|internal|latexonly|link|manonly|msc|parblock|rtfonly|secreflist|uml|verbatim|xmlonly|endcode|endcond|enddocbookonly|enddot|endhtmlonly|endinternal|endlatexonly|endlink|endmanonly|endmsc|endparblock|endrtfonly|endsecreflist|enduml|endverbatim|endxmlonly)\\\\b(?:\\\\{[^}]*})?\",\"name\":\"storage.type.class.doxygen.c\"},{\"match\":\"\\\\b[A-Z]+:|@[_a-z]+:\",\"name\":\"storage.type.class.gtkdoc\"}]},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.comment.begin.documentation.c\"},\"2\":{\"patterns\":[{\"match\":\"(?<=[!*/\\\\s])[@\\\\\\\\](?:callergraph|callgraph|else|endif|f\\\\$|f\\\\[|f]|hidecallergraph|hidecallgraph|hiderefby|hiderefs|hideinitializer|htmlinclude|n|nosubgrouping|private|privatesection|protected|protectedsection|public|publicsection|pure|showinitializer|showrefby|showrefs|tableofcontents|[\\\"-%.<=>]|::|\\\\||---??)\\\\b(?:\\\\{[^}]*})?\",\"name\":\"storage.type.class.doxygen.c\"},{\"captures\":{\"1\":{\"name\":\"storage.type.class.doxygen.c\"},\"2\":{\"name\":\"markup.italic.doxygen.c\"}},\"match\":\"((?<=[!*/\\\\s])[@\\\\\\\\](?:a|em?))\\\\s+(\\\\S+)\"},{\"captures\":{\"1\":{\"name\":\"storage.type.class.doxygen.c\"},\"2\":{\"name\":\"markup.bold.doxygen.c\"}},\"match\":\"((?<=[!*/\\\\s])[@\\\\\\\\]b)\\\\s+(\\\\S+)\"},{\"captures\":{\"1\":{\"name\":\"storage.type.class.doxygen.c\"},\"2\":{\"name\":\"markup.inline.raw.string.c\"}},\"match\":\"((?<=[!*/\\\\s])[@\\\\\\\\][cp])\\\\s+(\\\\S+)\"},{\"match\":\"(?<=[!*/\\\\s])[@\\\\\\\\](?:a|anchor|[bc]|cite|copybrief|copydetail|copydoc|def|dir|dontinclude|em??|emoji|enum|example|extends|file|idlexcept|implements|include|includedoc|includelineno|latexinclude|link|memberof|namespace|p|package|ref|refitem|related|relates|relatedalso|relatesalso|verbinclude)\\\\b(?:\\\\{[^}]*})?\",\"name\":\"storage.type.class.doxygen.c\"},{\"match\":\"(?<=[!*/\\\\s])[@\\\\\\\\](?:addindex|addtogroup|category|class|defgroup|diafile|dotfile|elseif|fn|headerfile|if|ifnot|image|ingroup|interface|line|mainpage|mscfile|name|overload|page|property|protocol|section|skip|skipline|snippet|snippetdoc|snippetlineno|struct|subpage|subsection|subsubsection|typedef|union|until|vhdlflow|weakgroup)\\\\b(?:\\\\{[^}]*})?\",\"name\":\"storage.type.class.doxygen.c\"},{\"captures\":{\"1\":{\"name\":\"storage.type.class.doxygen.c\"},\"2\":{\"patterns\":[{\"match\":\"in|out\",\"name\":\"keyword.other.parameter.direction.$0.c\"}]},\"3\":{\"name\":\"variable.parameter.c\"}},\"match\":\"((?<=[!*/\\\\s])[@\\\\\\\\]param)(?:\\\\s*\\\\[((?:,?\\\\s*(?:in|out)\\\\s*)+)])?\\\\s+\\\\b(\\\\w+)\\\\b\"},{\"match\":\"(?<=[!*/\\\\s])[@\\\\\\\\](?:arg|attention|authors??|brief|bug|copyright|date|deprecated|details|exception|invariant|li|note|par|paragraph|param|post|pre|remarks??|result|returns??|retval|sa|see|short|since|test|throw|todo|tparam|version|warning|xrefitem)\\\\b(?:\\\\{[^}]*})?\",\"name\":\"storage.type.class.doxygen.c\"},{\"match\":\"(?<=[!*/\\\\s])[@\\\\\\\\](?:code|cond|docbookonly|dot|htmlonly|internal|latexonly|link|manonly|msc|parblock|rtfonly|secreflist|uml|verbatim|xmlonly|endcode|endcond|enddocbookonly|enddot|endhtmlonly|endinternal|endlatexonly|endlink|endmanonly|endmsc|endparblock|endrtfonly|endsecreflist|enduml|endverbatim|endxmlonly)\\\\b(?:\\\\{[^}]*})?\",\"name\":\"storage.type.class.doxygen.c\"},{\"match\":\"\\\\b[A-Z]+:|@[_a-z]+:\",\"name\":\"storage.type.class.gtkdoc\"}]},\"3\":{\"name\":\"punctuation.definition.comment.end.documentation.c\"}},\"match\":\"(/\\\\*[!*]+(?=\\\\s))(.+)([!*]*\\\\*/)\",\"name\":\"comment.block.documentation.c\"},{\"begin\":\"((?>\\\\s*)/\\\\*[!*]+(?:(?:\\\\n|$)|(?=\\\\s)))\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.comment.begin.documentation.c\"}},\"end\":\"([!*]*\\\\*/)\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.comment.end.documentation.c\"}},\"name\":\"comment.block.documentation.c\",\"patterns\":[{\"match\":\"(?<=[!*/\\\\s])[@\\\\\\\\](?:callergraph|callgraph|else|endif|f\\\\$|f\\\\[|f]|hidecallergraph|hidecallgraph|hiderefby|hiderefs|hideinitializer|htmlinclude|n|nosubgrouping|private|privatesection|protected|protectedsection|public|publicsection|pure|showinitializer|showrefby|showrefs|tableofcontents|[\\\"-%.<=>]|::|\\\\||---??)\\\\b(?:\\\\{[^}]*})?\",\"name\":\"storage.type.class.doxygen.c\"},{\"captures\":{\"1\":{\"name\":\"storage.type.class.doxygen.c\"},\"2\":{\"name\":\"markup.italic.doxygen.c\"}},\"match\":\"((?<=[!*/\\\\s])[@\\\\\\\\](?:a|em?))\\\\s+(\\\\S+)\"},{\"captures\":{\"1\":{\"name\":\"storage.type.class.doxygen.c\"},\"2\":{\"name\":\"markup.bold.doxygen.c\"}},\"match\":\"((?<=[!*/\\\\s])[@\\\\\\\\]b)\\\\s+(\\\\S+)\"},{\"captures\":{\"1\":{\"name\":\"storage.type.class.doxygen.c\"},\"2\":{\"name\":\"markup.inline.raw.string.c\"}},\"match\":\"((?<=[!*/\\\\s])[@\\\\\\\\][cp])\\\\s+(\\\\S+)\"},{\"match\":\"(?<=[!*/\\\\s])[@\\\\\\\\](?:a|anchor|[bc]|cite|copybrief|copydetail|copydoc|def|dir|dontinclude|em??|emoji|enum|example|extends|file|idlexcept|implements|include|includedoc|includelineno|latexinclude|link|memberof|namespace|p|package|ref|refitem|related|relates|relatedalso|relatesalso|verbinclude)\\\\b(?:\\\\{[^}]*})?\",\"name\":\"storage.type.class.doxygen.c\"},{\"match\":\"(?<=[!*/\\\\s])[@\\\\\\\\](?:addindex|addtogroup|category|class|defgroup|diafile|dotfile|elseif|fn|headerfile|if|ifnot|image|ingroup|interface|line|mainpage|mscfile|name|overload|page|property|protocol|section|skip|skipline|snippet|snippetdoc|snippetlineno|struct|subpage|subsection|subsubsection|typedef|union|until|vhdlflow|weakgroup)\\\\b(?:\\\\{[^}]*})?\",\"name\":\"storage.type.class.doxygen.c\"},{\"captures\":{\"1\":{\"name\":\"storage.type.class.doxygen.c\"},\"2\":{\"patterns\":[{\"match\":\"in|out\",\"name\":\"keyword.other.parameter.direction.$0.c\"}]},\"3\":{\"name\":\"variable.parameter.c\"}},\"match\":\"((?<=[!*/\\\\s])[@\\\\\\\\]param)(?:\\\\s*\\\\[((?:,?\\\\s*(?:in|out)\\\\s*)+)])?\\\\s+\\\\b(\\\\w+)\\\\b\"},{\"match\":\"(?<=[!*/\\\\s])[@\\\\\\\\](?:arg|attention|authors??|brief|bug|copyright|date|deprecated|details|exception|invariant|li|note|par|paragraph|param|post|pre|remarks??|result|returns??|retval|sa|see|short|since|test|throw|todo|tparam|version|warning|xrefitem)\\\\b(?:\\\\{[^}]*})?\",\"name\":\"storage.type.class.doxygen.c\"},{\"match\":\"(?<=[!*/\\\\s])[@\\\\\\\\](?:code|cond|docbookonly|dot|htmlonly|internal|latexonly|link|manonly|msc|parblock|rtfonly|secreflist|uml|verbatim|xmlonly|endcode|endcond|enddocbookonly|enddot|endhtmlonly|endinternal|endlatexonly|endlink|endmanonly|endmsc|endparblock|endrtfonly|endsecreflist|enduml|endverbatim|endxmlonly)\\\\b(?:\\\\{[^}]*})?\",\"name\":\"storage.type.class.doxygen.c\"},{\"match\":\"\\\\b[A-Z]+:|@[_a-z]+:\",\"name\":\"storage.type.class.gtkdoc\"}]},{\"captures\":{\"1\":{\"name\":\"meta.toc-list.banner.block.c\"}},\"match\":\"^/\\\\* =(\\\\s*.*?)\\\\s*= \\\\*/$\\\\n?\",\"name\":\"comment.block.banner.c\"},{\"begin\":\"(/\\\\*)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.comment.begin.c\"}},\"end\":\"(\\\\*/)\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.comment.end.c\"}},\"name\":\"comment.block.c\"},{\"captures\":{\"1\":{\"name\":\"meta.toc-list.banner.line.c\"}},\"match\":\"^// =(\\\\s*.*?)\\\\s*=$\\\\n?\",\"name\":\"comment.line.banner.c\"},{\"begin\":\"((?:^[\\\\t ]+)?)(?=//)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.whitespace.comment.leading.c\"}},\"end\":\"(?!\\\\G)\",\"patterns\":[{\"begin\":\"(//)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.comment.c\"}},\"end\":\"(?=\\\\n)\",\"name\":\"comment.line.double-slash.c\",\"patterns\":[{\"include\":\"#line_continuation_character\"}]}]}]},{\"include\":\"#block_comment\"},{\"include\":\"#line_comment\"}]},{\"include\":\"#block_comment\"},{\"include\":\"#line_comment\"}]},\"default_statement\":{\"begin\":\"((?>(?:(?>(?<!\\\\s)\\\\s+)|(/\\\\*)((?>(?:[^*]|(?>\\\\*+)[^/])*)((?>\\\\*+)/)))+|(?:(?:(?:(?:\\\\b|(?<=\\\\W))|(?=\\\\W))|\\\\A)|\\\\Z)))((?<!\\\\w)default(?!\\\\w))\",\"beginCaptures\":{\"1\":{\"patterns\":[{\"include\":\"#inline_comment\"}]},\"2\":{\"name\":\"comment.block.c punctuation.definition.comment.begin.c\"},\"3\":{\"name\":\"comment.block.c\"},\"4\":{\"patterns\":[{\"match\":\"\\\\*/\",\"name\":\"comment.block.c punctuation.definition.comment.end.c\"},{\"match\":\"\\\\*\",\"name\":\"comment.block.c\"}]},\"5\":{\"name\":\"keyword.control.default.c\"}},\"end\":\"(:)\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.separator.colon.case.default.c\"}},\"name\":\"meta.conditional.case.c\",\"patterns\":[{\"include\":\"#evaluation_context\"},{\"include\":\"#c_conditional_context\"}]},\"disabled\":{\"begin\":\"^\\\\s*#\\\\s*if(n?def)?\\\\b.*$\",\"end\":\"^\\\\s*#\\\\s*endif\\\\b\",\"patterns\":[{\"include\":\"#disabled\"},{\"include\":\"#pragma-mark\"}]},\"evaluation_context\":{\"patterns\":[{\"include\":\"#function-call-innards\"},{\"include\":\"$self\"}]},\"function-call-innards\":{\"patterns\":[{\"include\":\"#comments\"},{\"include\":\"#storage_types\"},{\"include\":\"#method_access\"},{\"include\":\"#member_access\"},{\"include\":\"#operators\"},{\"begin\":\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)\\\\s*\\\\()((?:[A-Z_a-z][0-9A-Z_a-z]*+|::)++|(?<=operator)(?:[-!\\\\&*+<=>]+|\\\\(\\\\)|\\\\[]))\\\\s*(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"entity.name.function.c\"},\"2\":{\"name\":\"punctuation.section.arguments.begin.bracket.round.c\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.arguments.end.bracket.round.c\"}},\"patterns\":[{\"include\":\"#function-call-innards\"}]},{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.parens.begin.bracket.round.c\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.parens.end.bracket.round.c\"}},\"patterns\":[{\"include\":\"#function-call-innards\"}]},{\"include\":\"#block_innards\"}]},\"function-innards\":{\"patterns\":[{\"include\":\"#comments\"},{\"include\":\"#storage_types\"},{\"include\":\"#operators\"},{\"include\":\"#vararg_ellipses\"},{\"begin\":\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)\\\\s*\\\\()((?:[A-Z_a-z][0-9A-Z_a-z]*+|::)++|(?<=operator)(?:[-!\\\\&*+<=>]+|\\\\(\\\\)|\\\\[]))\\\\s*(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"entity.name.function.c\"},\"2\":{\"name\":\"punctuation.section.parameters.begin.bracket.round.c\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.parameters.end.bracket.round.c\"}},\"name\":\"meta.function.definition.parameters.c\",\"patterns\":[{\"include\":\"#probably_a_parameter\"},{\"include\":\"#function-innards\"}]},{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.parens.begin.bracket.round.c\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.parens.end.bracket.round.c\"}},\"patterns\":[{\"include\":\"#function-innards\"}]},{\"include\":\"$self\"}]},\"inline_comment\":{\"patterns\":[{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"comment.block.c punctuation.definition.comment.begin.c\"},\"2\":{\"name\":\"comment.block.c\"},\"3\":{\"patterns\":[{\"match\":\"\\\\*/\",\"name\":\"comment.block.c punctuation.definition.comment.end.c\"},{\"match\":\"\\\\*\",\"name\":\"comment.block.c\"}]}},\"match\":\"(/\\\\*)((?>(?:[^*]|(?>\\\\*+)[^/])*)((?>\\\\*+)/))\"},{\"captures\":{\"1\":{\"name\":\"comment.block.c punctuation.definition.comment.begin.c\"},\"2\":{\"name\":\"comment.block.c\"},\"3\":{\"patterns\":[{\"match\":\"\\\\*/\",\"name\":\"comment.block.c punctuation.definition.comment.end.c\"},{\"match\":\"\\\\*\",\"name\":\"comment.block.c\"}]}},\"match\":\"(/\\\\*)((?:[^*]|\\\\*++[^/])*+(\\\\*++/))\"}]},{\"captures\":{\"1\":{\"name\":\"comment.block.c punctuation.definition.comment.begin.c\"},\"2\":{\"name\":\"comment.block.c\"},\"3\":{\"patterns\":[{\"match\":\"\\\\*/\",\"name\":\"comment.block.c punctuation.definition.comment.end.c\"},{\"match\":\"\\\\*\",\"name\":\"comment.block.c\"}]}},\"match\":\"(/\\\\*)((?:[^*]|\\\\*++[^/])*+(\\\\*++/))\"}]},\"line_comment\":{\"patterns\":[{\"begin\":\"\\\\s*+(//)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.comment.c\"}},\"end\":\"(?<=\\\\n)(?<!\\\\\\\\\\\\n)\",\"endCaptures\":{},\"name\":\"comment.line.double-slash.c\",\"patterns\":[{\"include\":\"#line_continuation_character\"}]},{\"begin\":\"\\\\s*+(//)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.comment.c\"}},\"end\":\"(?<=\\\\n)(?<!\\\\\\\\\\\\n)\",\"endCaptures\":{},\"name\":\"comment.line.double-slash.c\",\"patterns\":[{\"include\":\"#line_continuation_character\"}]}]},\"line_continuation_character\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"constant.character.escape.line-continuation.c\"}},\"match\":\"(\\\\\\\\)\\\\n\"}]},\"member_access\":{\"captures\":{\"1\":{\"name\":\"variable.other.object.access.c\"},\"2\":{\"name\":\"punctuation.separator.dot-access.c\"},\"3\":{\"name\":\"punctuation.separator.pointer-access.c\"},\"4\":{\"patterns\":[{\"include\":\"#member_access\"},{\"include\":\"#method_access\"},{\"captures\":{\"1\":{\"name\":\"variable.other.object.access.c\"},\"2\":{\"name\":\"punctuation.separator.dot-access.c\"},\"3\":{\"name\":\"punctuation.separator.pointer-access.c\"}},\"match\":\"((?:[A-Z_a-z]\\\\w*|(?<=[])]))\\\\s*)(?:(\\\\.\\\\*?)|(->\\\\*?))\"}]},\"5\":{\"name\":\"variable.other.member.c\"}},\"match\":\"((?:[A-Z_a-z]\\\\w*|(?<=[])]))\\\\s*)(?:(\\\\.\\\\*?)|(->\\\\*?))((?:[A-Z_a-z]\\\\w*\\\\s*(?:\\\\.\\\\*?|->\\\\*?)\\\\s*)*)\\\\s*\\\\b((?!(?:atomic_uint_least64_t|atomic_uint_least16_t|atomic_uint_least32_t|atomic_uint_least8_t|atomic_int_least16_t|atomic_uint_fast64_t|atomic_uint_fast32_t|atomic_int_least64_t|atomic_int_least32_t|pthread_rwlockattr_t|atomic_uint_fast16_t|pthread_mutexattr_t|atomic_int_fast16_t|atomic_uint_fast8_t|atomic_int_fast64_t|atomic_int_least8_t|atomic_int_fast32_t|atomic_int_fast8_t|pthread_condattr_t|atomic_uintptr_t|atomic_ptrdiff_t|pthread_rwlock_t|atomic_uintmax_t|pthread_mutex_t|atomic_intmax_t|atomic_intptr_t|atomic_char32_t|atomic_char16_t|pthread_attr_t|atomic_wchar_t|uint_least64_t|uint_least32_t|uint_least16_t|pthread_cond_t|pthread_once_t|uint_fast64_t|uint_fast16_t|atomic_size_t|uint_least8_t|int_least64_t|int_least32_t|int_least16_t|pthread_key_t|atomic_ullong|atomic_ushort|uint_fast32_t|atomic_schar|atomic_short|uint_fast8_t|int_fast64_t|int_fast32_t|int_fast16_t|atomic_ulong|atomic_llong|int_least8_t|atomic_uchar|memory_order|suseconds_t|int_fast8_t|atomic_bool|atomic_char|atomic_uint|atomic_long|atomic_int|useconds_t|_Imaginary|blksize_t|pthread_t|in_addr_t|uintptr_t|in_port_t|uintmax_t|blkcnt_t|uint16_t|unsigned|_Complex|uint32_t|intptr_t|intmax_t|uint64_t|u_quad_t|int64_t|int32_t|ssize_t|caddr_t|clock_t|uint8_t|u_short|swblk_t|segsz_t|int16_t|fixpt_t|daddr_t|nlink_t|qaddr_t|size_t|time_t|mode_t|signed|quad_t|ushort|u_long|u_char|double|int8_t|ino_t|uid_t|pid_t|_Bool|float|dev_t|div_t|short|gid_t|off_t|u_int|key_t|id_t|uint|long|void|char|bool|id_t|int)\\\\b)[A-Z_a-z]\\\\w*\\\\b(?!\\\\())\"},\"method_access\":{\"begin\":\"((?:[A-Z_a-z]\\\\w*|(?<=[])]))\\\\s*)(?:(\\\\.\\\\*?)|(->\\\\*?))((?:[A-Z_a-z]\\\\w*\\\\s*(?:\\\\.\\\\*?|->\\\\*?)\\\\s*)*)\\\\s*([A-Z_a-z]\\\\w*)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"variable.other.object.access.c\"},\"2\":{\"name\":\"punctuation.separator.dot-access.c\"},\"3\":{\"name\":\"punctuation.separator.pointer-access.c\"},\"4\":{\"patterns\":[{\"include\":\"#member_access\"},{\"include\":\"#method_access\"},{\"captures\":{\"1\":{\"name\":\"variable.other.object.access.c\"},\"2\":{\"name\":\"punctuation.separator.dot-access.c\"},\"3\":{\"name\":\"punctuation.separator.pointer-access.c\"}},\"match\":\"((?:[A-Z_a-z]\\\\w*|(?<=[])]))\\\\s*)(?:(\\\\.\\\\*?)|(->\\\\*?))\"}]},\"5\":{\"name\":\"entity.name.function.member.c\"},\"6\":{\"name\":\"punctuation.section.arguments.begin.bracket.round.function.member.c\"}},\"contentName\":\"meta.function-call.member.c\",\"end\":\"(\\\\))\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.section.arguments.end.bracket.round.function.member.c\"}},\"patterns\":[{\"include\":\"#function-call-innards\"}]},\"numbers\":{\"captures\":{\"0\":{\"patterns\":[{\"begin\":\"(?=.)\",\"end\":\"$\",\"patterns\":[{\"captures\":{\"1\":{\"name\":\"keyword.other.unit.hexadecimal.c\"},\"2\":{\"name\":\"constant.numeric.hexadecimal.c\",\"patterns\":[{\"match\":\"(?<=\\\\h)'(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric\"}]},\"3\":{\"name\":\"punctuation.separator.constant.numeric\"},\"4\":{\"name\":\"constant.numeric.hexadecimal.c\"},\"5\":{\"name\":\"constant.numeric.hexadecimal.c\",\"patterns\":[{\"match\":\"(?<=\\\\h)'(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric\"}]},\"6\":{\"name\":\"punctuation.separator.constant.numeric\"},\"8\":{\"name\":\"keyword.other.unit.exponent.hexadecimal.c\"},\"9\":{\"name\":\"keyword.operator.plus.exponent.hexadecimal.c\"},\"10\":{\"name\":\"keyword.operator.minus.exponent.hexadecimal.c\"},\"11\":{\"name\":\"constant.numeric.exponent.hexadecimal.c\",\"patterns\":[{\"match\":\"(?<=\\\\h)'(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric\"}]},\"12\":{\"name\":\"keyword.other.unit.suffix.floating-point.c\"}},\"match\":\"\\\\G(0[Xx])(\\\\h(?:\\\\h|((?<=\\\\h)'(?=\\\\h)))*)?((?<=\\\\h)\\\\.|\\\\.(?=\\\\h))(\\\\h(?:\\\\h|((?<=\\\\h)'(?=\\\\h)))*)?((?<!')([Pp])(\\\\+?)(-?)([0-9](?:[0-9]|(?<=\\\\h)'(?=\\\\h))*))?([FLfl](?!\\\\w))?$\"},{\"captures\":{\"2\":{\"name\":\"constant.numeric.decimal.c\",\"patterns\":[{\"match\":\"(?<=\\\\h)'(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric\"}]},\"3\":{\"name\":\"punctuation.separator.constant.numeric\"},\"4\":{\"name\":\"constant.numeric.decimal.point.c\"},\"5\":{\"name\":\"constant.numeric.decimal.c\",\"patterns\":[{\"match\":\"(?<=\\\\h)'(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric\"}]},\"6\":{\"name\":\"punctuation.separator.constant.numeric\"},\"8\":{\"name\":\"keyword.other.unit.exponent.decimal.c\"},\"9\":{\"name\":\"keyword.operator.plus.exponent.decimal.c\"},\"10\":{\"name\":\"keyword.operator.minus.exponent.decimal.c\"},\"11\":{\"name\":\"constant.numeric.exponent.decimal.c\",\"patterns\":[{\"match\":\"(?<=\\\\h)'(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric\"}]},\"12\":{\"name\":\"keyword.other.unit.suffix.floating-point.c\"}},\"match\":\"\\\\G((?=[.0-9])(?!0[BXbx]))([0-9](?:[0-9]|((?<=\\\\h)'(?=\\\\h)))*)?((?<=[0-9])\\\\.|\\\\.(?=[0-9]))([0-9](?:[0-9]|((?<=\\\\h)'(?=\\\\h)))*)?((?<!')([Ee])(\\\\+?)(-?)([0-9](?:[0-9]|(?<=\\\\h)'(?=\\\\h))*))?([FLfl](?!\\\\w))?$\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.unit.binary.c\"},\"2\":{\"name\":\"constant.numeric.binary.c\",\"patterns\":[{\"match\":\"(?<=\\\\h)'(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric\"}]},\"3\":{\"name\":\"punctuation.separator.constant.numeric\"},\"4\":{\"name\":\"keyword.other.unit.suffix.integer.c\"}},\"match\":\"\\\\G(0[Bb])([01](?:[01]|((?<=\\\\h)'(?=\\\\h)))*)((?:(?:(?:(?:(?:[Uu]|[Uu]ll?)|[Uu]LL?)|ll?[Uu]?)|LL?[Uu]?)|[Ff])(?!\\\\w))?$\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.unit.octal.c\"},\"2\":{\"name\":\"constant.numeric.octal.c\",\"patterns\":[{\"match\":\"(?<=\\\\h)'(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric\"}]},\"3\":{\"name\":\"punctuation.separator.constant.numeric\"},\"4\":{\"name\":\"keyword.other.unit.suffix.integer.c\"}},\"match\":\"\\\\G(0)((?:[0-7]|((?<=\\\\h)'(?=\\\\h)))+)((?:(?:(?:(?:(?:[Uu]|[Uu]ll?)|[Uu]LL?)|ll?[Uu]?)|LL?[Uu]?)|[Ff])(?!\\\\w))?$\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.unit.hexadecimal.c\"},\"2\":{\"name\":\"constant.numeric.hexadecimal.c\",\"patterns\":[{\"match\":\"(?<=\\\\h)'(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric\"}]},\"3\":{\"name\":\"punctuation.separator.constant.numeric\"},\"5\":{\"name\":\"keyword.other.unit.exponent.hexadecimal.c\"},\"6\":{\"name\":\"keyword.operator.plus.exponent.hexadecimal.c\"},\"7\":{\"name\":\"keyword.operator.minus.exponent.hexadecimal.c\"},\"8\":{\"name\":\"constant.numeric.exponent.hexadecimal.c\",\"patterns\":[{\"match\":\"(?<=\\\\h)'(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric\"}]},\"9\":{\"name\":\"keyword.other.unit.suffix.integer.c\"}},\"match\":\"\\\\G(0[Xx])(\\\\h(?:\\\\h|((?<=\\\\h)'(?=\\\\h)))*)((?<!')([Pp])(\\\\+?)(-?)([0-9](?:[0-9]|(?<=\\\\h)'(?=\\\\h))*))?((?:(?:(?:(?:(?:[Uu]|[Uu]ll?)|[Uu]LL?)|ll?[Uu]?)|LL?[Uu]?)|[Ff])(?!\\\\w))?$\"},{\"captures\":{\"2\":{\"name\":\"constant.numeric.decimal.c\",\"patterns\":[{\"match\":\"(?<=\\\\h)'(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric\"}]},\"3\":{\"name\":\"punctuation.separator.constant.numeric\"},\"5\":{\"name\":\"keyword.other.unit.exponent.decimal.c\"},\"6\":{\"name\":\"keyword.operator.plus.exponent.decimal.c\"},\"7\":{\"name\":\"keyword.operator.minus.exponent.decimal.c\"},\"8\":{\"name\":\"constant.numeric.exponent.decimal.c\",\"patterns\":[{\"match\":\"(?<=\\\\h)'(?=\\\\h)\",\"name\":\"punctuation.separator.constant.numeric\"}]},\"9\":{\"name\":\"keyword.other.unit.suffix.integer.c\"}},\"match\":\"\\\\G((?=[.0-9])(?!0[BXbx]))([0-9](?:[0-9]|((?<=\\\\h)'(?=\\\\h)))*)((?<!')([Ee])(\\\\+?)(-?)([0-9](?:[0-9]|(?<=\\\\h)'(?=\\\\h))*))?((?:(?:(?:(?:(?:[Uu]|[Uu]ll?)|[Uu]LL?)|ll?[Uu]?)|LL?[Uu]?)|[Ff])(?!\\\\w))?$\"},{\"match\":\"(?:['.0-9A-Z_a-z]|(?<=[EPep])[-+])+\",\"name\":\"invalid.illegal.constant.numeric\"}]}]}},\"match\":\"(?<!\\\\w)\\\\.?\\\\d(?:['.0-9A-Z_a-z]|(?<=[EPep])[-+])*\"},\"operators\":{\"patterns\":[{\"match\":\"(?<![$\\\\w])(sizeof)(?![$\\\\w])\",\"name\":\"keyword.operator.sizeof.c\"},{\"match\":\"--\",\"name\":\"keyword.operator.decrement.c\"},{\"match\":\"\\\\+\\\\+\",\"name\":\"keyword.operator.increment.c\"},{\"match\":\"(?:[-%*+]|(?<!\\\\()/)=\",\"name\":\"keyword.operator.assignment.compound.c\"},{\"match\":\"(?:[\\\\&^]|<<|>>|\\\\|)=\",\"name\":\"keyword.operator.assignment.compound.bitwise.c\"},{\"match\":\"<<|>>\",\"name\":\"keyword.operator.bitwise.shift.c\"},{\"match\":\"!=|<=|>=|==|[<>]\",\"name\":\"keyword.operator.comparison.c\"},{\"match\":\"&&|!|\\\\|\\\\|\",\"name\":\"keyword.operator.logical.c\"},{\"match\":\"[\\\\&^|~]\",\"name\":\"keyword.operator.c\"},{\"match\":\"=\",\"name\":\"keyword.operator.assignment.c\"},{\"match\":\"[-%*+/]\",\"name\":\"keyword.operator.c\"},{\"begin\":\"(\\\\?)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.operator.ternary.c\"}},\"end\":\"(:)\",\"endCaptures\":{\"1\":{\"name\":\"keyword.operator.ternary.c\"}},\"patterns\":[{\"include\":\"#function-call-innards\"},{\"include\":\"$self\"}]}]},\"parens\":{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.parens.begin.bracket.round.c\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.parens.end.bracket.round.c\"}},\"name\":\"meta.parens.c\",\"patterns\":[{\"include\":\"$self\"}]},\"parens-block\":{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.parens.begin.bracket.round.c\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.parens.end.bracket.round.c\"}},\"name\":\"meta.parens.block.c\",\"patterns\":[{\"include\":\"#block_innards\"},{\"match\":\"(?-im:(?<!:):(?!:))\",\"name\":\"punctuation.range-based.c\"}]},\"pragma-mark\":{\"captures\":{\"1\":{\"name\":\"meta.preprocessor.pragma.c\"},\"2\":{\"name\":\"keyword.control.directive.pragma.pragma-mark.c\"},\"3\":{\"name\":\"punctuation.definition.directive.c\"},\"4\":{\"name\":\"entity.name.tag.pragma-mark.c\"}},\"match\":\"^\\\\s*(((#)\\\\s*pragma\\\\s+mark)\\\\s+(.*))\",\"name\":\"meta.section.c\"},\"predefined_macros\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"entity.name.other.preprocessor.macro.predefined.$1.c\"}},\"match\":\"\\\\b(__cplusplus|__DATE__|__FILE__|__LINE__|__STDC__|__STDC_HOSTED__|__STDC_NO_COMPLEX__|__STDC_VERSION__|__STDCPP_THREADS__|__TIME__|NDEBUG|__OBJC__|__ASSEMBLER__|__ATOM__|__AVX__|__AVX2__|_CHAR_UNSIGNED|__CLR_VER|_CONTROL_FLOW_GUARD|__COUNTER__|__cplusplus_cli|__cplusplus_winrt|_CPPRTTI|_CPPUNWIND|_DEBUG|_DLL|__FUNCDNAME__|__FUNCSIG__|__FUNCTION__|_INTEGRAL_MAX_BITS|__INTELLISENSE__|_ISO_VOLATILE|_KERNEL_MODE|_M_AMD64|_M_ARM|_M_ARM_ARMV7VE|_M_ARM_FP|_M_ARM64|_M_CEE|_M_CEE_PURE|_M_CEE_SAFE|_M_FP_EXCEPT|_M_FP_FAST|_M_FP_PRECISE|_M_FP_STRICT|_M_IX86|_M_IX86_FP|_M_X64|_MANAGED|_MSC_BUILD|_MSC_EXTENSIONS|_MSC_FULL_VER|_MSC_VER|_MSVC_LANG|__MSVC_RUNTIME_CHECKS|_MT|_NATIVE_WCHAR_T_DEFINED|_OPENMP|_PREFAST|__TIMESTAMP__|_VC_NO_DEFAULTLIB|_WCHAR_T_DEFINED|_WIN32|_WIN64|_WINRT_DLL|_ATL_VER|_MFC_VER|__GFORTRAN__|__GNUC__|__GNUC_MINOR__|__GNUC_PATCHLEVEL__|__GNUG__|__STRICT_ANSI__|__BASE_FILE__|__INCLUDE_LEVEL__|__ELF__|__VERSION__|__OPTIMIZE__|__OPTIMIZE_SIZE__|__NO_INLINE__|__GNUC_STDC_INLINE__|__CHAR_UNSIGNED__|__WCHAR_UNSIGNED__|__REGISTER_PREFIX__|__SIZE_TYPE__|__PTRDIFF_TYPE__|__WCHAR_TYPE__|__WINT_TYPE__|__INTMAX_TYPE__|__UINTMAX_TYPE__|__SIG_ATOMIC_TYPE__|__INT8_TYPE__|__INT16_TYPE__|__INT32_TYPE__|__INT64_TYPE__|__UINT8_TYPE__|__UINT16_TYPE__|__UINT32_TYPE__|__UINT64_TYPE__|__INT_LEAST8_TYPE__|__INT_LEAST16_TYPE__|__INT_LEAST32_TYPE__|__INT_LEAST64_TYPE__|__UINT_LEAST8_TYPE__|__UINT_LEAST16_TYPE__|__UINT_LEAST32_TYPE__|__UINT_LEAST64_TYPE__|__INT_FAST8_TYPE__|__INT_FAST16_TYPE__|__INT_FAST32_TYPE__|__INT_FAST64_TYPE__|__UINT_FAST8_TYPE__|__UINT_FAST16_TYPE__|__UINT_FAST32_TYPE__|__UINT_FAST64_TYPE__|__INTPTR_TYPE__|__UINTPTR_TYPE__|__CHAR_BIT__|__SCHAR_MAX__|__WCHAR_MAX__|__SHRT_MAX__|__INT_MAX__|__LONG_MAX__|__LONG_LONG_MAX__|__WINT_MAX__|__SIZE_MAX__|__PTRDIFF_MAX__|__INTMAX_MAX__|__UINTMAX_MAX__|__SIG_ATOMIC_MAX__|__INT8_MAX__|__INT16_MAX__|__INT32_MAX__|__INT64_MAX__|__UINT8_MAX__|__UINT16_MAX__|__UINT32_MAX__|__UINT64_MAX__|__INT_LEAST8_MAX__|__INT_LEAST16_MAX__|__INT_LEAST32_MAX__|__INT_LEAST64_MAX__|__UINT_LEAST8_MAX__|__UINT_LEAST16_MAX__|__UINT_LEAST32_MAX__|__UINT_LEAST64_MAX__|__INT_FAST8_MAX__|__INT_FAST16_MAX__|__INT_FAST32_MAX__|__INT_FAST64_MAX__|__UINT_FAST8_MAX__|__UINT_FAST16_MAX__|__UINT_FAST32_MAX__|__UINT_FAST64_MAX__|__INTPTR_MAX__|__UINTPTR_MAX__|__WCHAR_MIN__|__WINT_MIN__|__SIG_ATOMIC_MIN__|__SCHAR_WIDTH__|__SHRT_WIDTH__|__INT_WIDTH__|__LONG_WIDTH__|__LONG_LONG_WIDTH__|__PTRDIFF_WIDTH__|__SIG_ATOMIC_WIDTH__|__SIZE_WIDTH__|__WCHAR_WIDTH__|__WINT_WIDTH__|__INT_LEAST8_WIDTH__|__INT_LEAST16_WIDTH__|__INT_LEAST32_WIDTH__|__INT_LEAST64_WIDTH__|__INT_FAST8_WIDTH__|__INT_FAST16_WIDTH__|__INT_FAST32_WIDTH__|__INT_FAST64_WIDTH__|__INTPTR_WIDTH__|__INTMAX_WIDTH__|__SIZEOF_INT__|__SIZEOF_LONG__|__SIZEOF_LONG_LONG__|__SIZEOF_SHORT__|__SIZEOF_POINTER__|__SIZEOF_FLOAT__|__SIZEOF_DOUBLE__|__SIZEOF_LONG_DOUBLE__|__SIZEOF_SIZE_T__|__SIZEOF_WCHAR_T__|__SIZEOF_WINT_T__|__SIZEOF_PTRDIFF_T__|__BYTE_ORDER__|__ORDER_LITTLE_ENDIAN__|__ORDER_BIG_ENDIAN__|__ORDER_PDP_ENDIAN__|__FLOAT_WORD_ORDER__|__DEPRECATED|__EXCEPTIONS|__GXX_RTTI|__USING_SJLJ_EXCEPTIONS__|__GXX_EXPERIMENTAL_CXX0X__|__GXX_WEAK__|__NEXT_RUNTIME__|__LP64__|_LP64|__SSP__|__SSP_ALL__|__SSP_STRONG__|__SSP_EXPLICIT__|__SANITIZE_ADDRESS__|__SANITIZE_THREAD__|__GCC_HAVE_SYNC_COMPARE_AND_SWAP_1|__GCC_HAVE_SYNC_COMPARE_AND_SWAP_2|__GCC_HAVE_SYNC_COMPARE_AND_SWAP_4|__GCC_HAVE_SYNC_COMPARE_AND_SWAP_8|__GCC_HAVE_SYNC_COMPARE_AND_SWAP_16|__HAVE_SPECULATION_SAFE_VALUE|__GCC_HAVE_DWARF2_CFI_ASM|__FP_FAST_FMAF??|__FP_FAST_FMAL|__FP_FAST_FMAF16|__FP_FAST_FMAF32|__FP_FAST_FMAF64|__FP_FAST_FMAF128|__FP_FAST_FMAF32X|__FP_FAST_FMAF64X|__FP_FAST_FMAF128X|__GCC_IEC_559|__GCC_IEC_559_COMPLEX|__NO_MATH_ERRNO__|__has_builtin|__has_feature|__has_extension|__has_cpp_attribute|__has_c_attribute|__has_attribute|__has_declspec_attribute|__is_identifier|__has_include|__has_include_next|__has_warning|__BASE_FILE__|__FILE_NAME__|__clang__|__clang_major__|__clang_minor__|__clang_patchlevel__|__clang_version__|__fp16|_Float16)\\\\b\"},{\"match\":\"\\\\b__([A-Z_]+)__\\\\b\",\"name\":\"entity.name.other.preprocessor.macro.predefined.probably.$1.c\"}]},\"preprocessor-rule-conditional\":{\"patterns\":[{\"begin\":\"^\\\\s*((#)\\\\s*if(?:n?def)?)\\\\b\",\"beginCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"end\":\"^\\\\s*((#)\\\\s*endif)\\\\b\",\"endCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"patterns\":[{\"begin\":\"\\\\G(?=.)(?!/(?:/|\\\\*(?!.*\\\\\\\\\\\\s*\\\\n)))\",\"end\":\"(?=//)|(?=/\\\\*(?!.*\\\\\\\\\\\\s*\\\\n))|(?<!\\\\\\\\)(?=\\\\n)\",\"name\":\"meta.preprocessor.c\",\"patterns\":[{\"include\":\"#preprocessor-rule-conditional-line\"}]},{\"include\":\"#preprocessor-rule-enabled-elif\"},{\"include\":\"#preprocessor-rule-enabled-else\"},{\"include\":\"#preprocessor-rule-disabled-elif\"},{\"begin\":\"^\\\\s*((#)\\\\s*elif)\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"end\":\"(?=//)|(?=/\\\\*(?!.*\\\\\\\\\\\\s*\\\\n))|(?<!\\\\\\\\)(?=\\\\n)\",\"name\":\"meta.preprocessor.c\",\"patterns\":[{\"include\":\"#preprocessor-rule-conditional-line\"}]},{\"include\":\"$self\"}]},{\"captures\":{\"0\":{\"name\":\"invalid.illegal.stray-$1.c\"}},\"match\":\"^\\\\s*#\\\\s*(e(?:lse|lif|ndif))\\\\b\"}]},\"preprocessor-rule-conditional-block\":{\"patterns\":[{\"begin\":\"^\\\\s*((#)\\\\s*if(?:n?def)?)\\\\b\",\"beginCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"end\":\"^\\\\s*((#)\\\\s*endif)\\\\b\",\"endCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"patterns\":[{\"begin\":\"\\\\G(?=.)(?!/(?:/|\\\\*(?!.*\\\\\\\\\\\\s*\\\\n)))\",\"end\":\"(?=//)|(?=/\\\\*(?!.*\\\\\\\\\\\\s*\\\\n))|(?<!\\\\\\\\)(?=\\\\n)\",\"name\":\"meta.preprocessor.c\",\"patterns\":[{\"include\":\"#preprocessor-rule-conditional-line\"}]},{\"include\":\"#preprocessor-rule-enabled-elif-block\"},{\"include\":\"#preprocessor-rule-enabled-else-block\"},{\"include\":\"#preprocessor-rule-disabled-elif\"},{\"begin\":\"^\\\\s*((#)\\\\s*elif)\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"end\":\"(?=//)|(?=/\\\\*(?!.*\\\\\\\\\\\\s*\\\\n))|(?<!\\\\\\\\)(?=\\\\n)\",\"name\":\"meta.preprocessor.c\",\"patterns\":[{\"include\":\"#preprocessor-rule-conditional-line\"}]},{\"include\":\"#block_innards\"}]},{\"captures\":{\"0\":{\"name\":\"invalid.illegal.stray-$1.c\"}},\"match\":\"^\\\\s*#\\\\s*(e(?:lse|lif|ndif))\\\\b\"}]},\"preprocessor-rule-conditional-line\":{\"patterns\":[{\"match\":\"\\\\bdefined\\\\b(?:\\\\s*$|(?=\\\\s*\\\\(*\\\\s*(?!defined\\\\b)[$A-Z_a-z][$\\\\w]*\\\\b\\\\s*\\\\)*\\\\s*(?:\\\\n|//|/\\\\*|[:?]|&&|\\\\|\\\\||\\\\\\\\\\\\s*\\\\n)))\",\"name\":\"keyword.control.directive.conditional.c\"},{\"match\":\"\\\\bdefined\\\\b\",\"name\":\"invalid.illegal.macro-name.c\"},{\"include\":\"#comments\"},{\"include\":\"#strings\"},{\"include\":\"#numbers\"},{\"begin\":\"\\\\?\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.operator.ternary.c\"}},\"end\":\":\",\"endCaptures\":{\"0\":{\"name\":\"keyword.operator.ternary.c\"}},\"patterns\":[{\"include\":\"#preprocessor-rule-conditional-line\"}]},{\"include\":\"#operators\"},{\"match\":\"\\\\b(NULL|true|false|TRUE|FALSE)\\\\b\",\"name\":\"constant.language.c\"},{\"match\":\"[$A-Z_a-z][$\\\\w]*\",\"name\":\"entity.name.function.preprocessor.c\"},{\"include\":\"#line_continuation_character\"},{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.parens.begin.bracket.round.c\"}},\"end\":\"\\\\)|(?=//)|(?=/\\\\*(?!.*\\\\\\\\\\\\s*\\\\n))|(?<!\\\\\\\\)(?=\\\\n)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.parens.end.bracket.round.c\"}},\"patterns\":[{\"include\":\"#preprocessor-rule-conditional-line\"}]}]},\"preprocessor-rule-define-line-blocks\":{\"patterns\":[{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.block.begin.bracket.curly.c\"}},\"end\":\"}|(?=\\\\s*#\\\\s*e(?:lif|lse|ndif)\\\\b)|(?<!\\\\\\\\)(?=\\\\s*\\\\n)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.block.end.bracket.curly.c\"}},\"patterns\":[{\"include\":\"#preprocessor-rule-define-line-blocks\"},{\"include\":\"#preprocessor-rule-define-line-contents\"}]},{\"include\":\"#preprocessor-rule-define-line-contents\"}]},\"preprocessor-rule-define-line-contents\":{\"patterns\":[{\"include\":\"#vararg_ellipses\"},{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.block.begin.bracket.curly.c\"}},\"end\":\"}|(?=\\\\s*#\\\\s*e(?:lif|lse|ndif)\\\\b)|(?<!\\\\\\\\)(?=\\\\s*\\\\n)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.block.end.bracket.curly.c\"}},\"name\":\"meta.block.c\",\"patterns\":[{\"include\":\"#preprocessor-rule-define-line-blocks\"}]},{\"match\":\"\\\\(\",\"name\":\"punctuation.section.parens.begin.bracket.round.c\"},{\"match\":\"\\\\)\",\"name\":\"punctuation.section.parens.end.bracket.round.c\"},{\"begin\":\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas|asm|__asm__|auto|bool|_Bool|char|_Complex|double|enum|float|_Imaginary|int|long|short|signed|struct|typedef|union|unsigned|void)\\\\s*\\\\()(?=(?:[A-Z_a-z][0-9A-Z_a-z]*+|::)++\\\\s*\\\\(|(?<=operator)(?:[-!\\\\&*+<=>]+|\\\\(\\\\)|\\\\[])\\\\s*\\\\()\",\"end\":\"(?<=\\\\))(?!\\\\w)|(?<!\\\\\\\\)(?=\\\\s*\\\\n)\",\"name\":\"meta.function.c\",\"patterns\":[{\"include\":\"#preprocessor-rule-define-line-functions\"}]},{\"begin\":\"\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.c\"}},\"end\":\"\\\"|(?<!\\\\\\\\)(?=\\\\s*\\\\n)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.c\"}},\"name\":\"string.quoted.double.c\",\"patterns\":[{\"include\":\"#string_escaped_char\"},{\"include\":\"#string_placeholder\"},{\"include\":\"#line_continuation_character\"}]},{\"begin\":\"'\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.c\"}},\"end\":\"'|(?<!\\\\\\\\)(?=\\\\s*\\\\n)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.c\"}},\"name\":\"string.quoted.single.c\",\"patterns\":[{\"include\":\"#string_escaped_char\"},{\"include\":\"#line_continuation_character\"}]},{\"include\":\"#method_access\"},{\"include\":\"#member_access\"},{\"include\":\"$self\"}]},\"preprocessor-rule-define-line-functions\":{\"patterns\":[{\"include\":\"#comments\"},{\"include\":\"#storage_types\"},{\"include\":\"#vararg_ellipses\"},{\"include\":\"#method_access\"},{\"include\":\"#member_access\"},{\"include\":\"#operators\"},{\"begin\":\"(?!(?:while|for|do|if|else|switch|catch|enumerate|return|typeid|alignof|alignas|sizeof|[cr]?iterate|and|and_eq|bitand|bitor|compl|not|not_eq|or|or_eq|typeid|xor|xor_eq|alignof|alignas)\\\\s*\\\\()((?:[A-Z_a-z][0-9A-Z_a-z]*+|::)++|(?<=operator)(?:[-!\\\\&*+<=>]+|\\\\(\\\\)|\\\\[]))\\\\s*(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"entity.name.function.c\"},\"2\":{\"name\":\"punctuation.section.arguments.begin.bracket.round.c\"}},\"end\":\"(\\\\))|(?<!\\\\\\\\)(?=\\\\s*\\\\n)\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.section.arguments.end.bracket.round.c\"}},\"patterns\":[{\"include\":\"#preprocessor-rule-define-line-functions\"}]},{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.parens.begin.bracket.round.c\"}},\"end\":\"(\\\\))|(?<!\\\\\\\\)(?=\\\\s*\\\\n)\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.section.parens.end.bracket.round.c\"}},\"patterns\":[{\"include\":\"#preprocessor-rule-define-line-functions\"}]},{\"include\":\"#preprocessor-rule-define-line-contents\"}]},\"preprocessor-rule-disabled\":{\"patterns\":[{\"begin\":\"^\\\\s*((#)\\\\s*if)\\\\b(?=\\\\s*\\\\(*\\\\b0+\\\\b\\\\)*\\\\s*(?:$|//|/\\\\*))\",\"beginCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"end\":\"^\\\\s*((#)\\\\s*endif)\\\\b\",\"endCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"patterns\":[{\"begin\":\"\\\\G(?=.)(?!/(?:/|\\\\*(?!.*\\\\\\\\\\\\s*\\\\n)))\",\"end\":\"(?=//)|(?=/\\\\*(?!.*\\\\\\\\\\\\s*\\\\n))|(?=\\\\n)\",\"name\":\"meta.preprocessor.c\",\"patterns\":[{\"include\":\"#preprocessor-rule-conditional-line\"}]},{\"include\":\"#comments\"},{\"include\":\"#preprocessor-rule-enabled-elif\"},{\"include\":\"#preprocessor-rule-enabled-else\"},{\"include\":\"#preprocessor-rule-disabled-elif\"},{\"begin\":\"^\\\\s*((#)\\\\s*elif)\\\\b\",\"beginCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"end\":\"(?=^\\\\s*((#)\\\\s*e(?:lif|lse|ndif))\\\\b)\",\"patterns\":[{\"begin\":\"\\\\G(?=.)(?!/(?:/|\\\\*(?!.*\\\\\\\\\\\\s*\\\\n)))\",\"end\":\"(?=//)|(?=/\\\\*(?!.*\\\\\\\\\\\\s*\\\\n))|(?<!\\\\\\\\)(?=\\\\n)\",\"name\":\"meta.preprocessor.c\",\"patterns\":[{\"include\":\"#preprocessor-rule-conditional-line\"}]},{\"include\":\"$self\"}]},{\"begin\":\"\\\\n\",\"contentName\":\"comment.block.preprocessor.if-branch.c\",\"end\":\"(?=^\\\\s*((#)\\\\s*e(?:lse|lif|ndif))\\\\b)\",\"patterns\":[{\"include\":\"#disabled\"},{\"include\":\"#pragma-mark\"}]}]}]},\"preprocessor-rule-disabled-block\":{\"patterns\":[{\"begin\":\"^\\\\s*((#)\\\\s*if)\\\\b(?=\\\\s*\\\\(*\\\\b0+\\\\b\\\\)*\\\\s*(?:$|//|/\\\\*))\",\"beginCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"end\":\"^\\\\s*((#)\\\\s*endif)\\\\b\",\"endCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"patterns\":[{\"begin\":\"\\\\G(?=.)(?!/(?:/|\\\\*(?!.*\\\\\\\\\\\\s*\\\\n)))\",\"end\":\"(?=//)|(?=/\\\\*(?!.*\\\\\\\\\\\\s*\\\\n))|(?=\\\\n)\",\"name\":\"meta.preprocessor.c\",\"patterns\":[{\"include\":\"#preprocessor-rule-conditional-line\"}]},{\"include\":\"#comments\"},{\"include\":\"#preprocessor-rule-enabled-elif-block\"},{\"include\":\"#preprocessor-rule-enabled-else-block\"},{\"include\":\"#preprocessor-rule-disabled-elif\"},{\"begin\":\"^\\\\s*((#)\\\\s*elif)\\\\b\",\"beginCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"end\":\"(?=^\\\\s*((#)\\\\s*e(?:lif|lse|ndif))\\\\b)\",\"patterns\":[{\"begin\":\"\\\\G(?=.)(?!/(?:/|\\\\*(?!.*\\\\\\\\\\\\s*\\\\n)))\",\"end\":\"(?=//)|(?=/\\\\*(?!.*\\\\\\\\\\\\s*\\\\n))|(?<!\\\\\\\\)(?=\\\\n)\",\"name\":\"meta.preprocessor.c\",\"patterns\":[{\"include\":\"#preprocessor-rule-conditional-line\"}]},{\"include\":\"#block_innards\"}]},{\"begin\":\"\\\\n\",\"contentName\":\"comment.block.preprocessor.if-branch.in-block.c\",\"end\":\"(?=^\\\\s*((#)\\\\s*e(?:lse|lif|ndif))\\\\b)\",\"patterns\":[{\"include\":\"#disabled\"},{\"include\":\"#pragma-mark\"}]}]}]},\"preprocessor-rule-disabled-elif\":{\"begin\":\"^\\\\s*((#)\\\\s*elif)\\\\b(?=\\\\s*\\\\(*\\\\b0+\\\\b\\\\)*\\\\s*(?:$|//|/\\\\*))\",\"beginCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"end\":\"(?=^\\\\s*((#)\\\\s*e(?:lif|lse|ndif))\\\\b)\",\"patterns\":[{\"begin\":\"\\\\G(?=.)(?!/(?:/|\\\\*(?!.*\\\\\\\\\\\\s*\\\\n)))\",\"end\":\"(?=//)|(?=/\\\\*(?!.*\\\\\\\\\\\\s*\\\\n))|(?<!\\\\\\\\)(?=\\\\n)\",\"name\":\"meta.preprocessor.c\",\"patterns\":[{\"include\":\"#preprocessor-rule-conditional-line\"}]},{\"include\":\"#comments\"},{\"begin\":\"\\\\n\",\"contentName\":\"comment.block.preprocessor.elif-branch.c\",\"end\":\"(?=^\\\\s*((#)\\\\s*e(?:lse|lif|ndif))\\\\b)\",\"patterns\":[{\"include\":\"#disabled\"},{\"include\":\"#pragma-mark\"}]}]},\"preprocessor-rule-enabled\":{\"patterns\":[{\"begin\":\"^\\\\s*((#)\\\\s*if)\\\\b(?=\\\\s*\\\\(*\\\\b0*1\\\\b\\\\)*\\\\s*(?:$|//|/\\\\*))\",\"beginCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"},\"3\":{\"name\":\"constant.numeric.preprocessor.c\"}},\"end\":\"^\\\\s*((#)\\\\s*endif)\\\\b\",\"endCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"patterns\":[{\"begin\":\"\\\\G(?=.)(?!/(?:/|\\\\*(?!.*\\\\\\\\\\\\s*\\\\n)))\",\"end\":\"(?=//)|(?=/\\\\*(?!.*\\\\\\\\\\\\s*\\\\n))|(?=\\\\n)\",\"name\":\"meta.preprocessor.c\",\"patterns\":[{\"include\":\"#preprocessor-rule-conditional-line\"}]},{\"include\":\"#comments\"},{\"begin\":\"^\\\\s*((#)\\\\s*else)\\\\b\",\"beginCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"contentName\":\"comment.block.preprocessor.else-branch.c\",\"end\":\"(?=^\\\\s*((#)\\\\s*endif)\\\\b)\",\"patterns\":[{\"include\":\"#disabled\"},{\"include\":\"#pragma-mark\"}]},{\"begin\":\"^\\\\s*((#)\\\\s*elif)\\\\b\",\"beginCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"contentName\":\"comment.block.preprocessor.if-branch.c\",\"end\":\"(?=^\\\\s*((#)\\\\s*e(?:lse|lif|ndif))\\\\b)\",\"patterns\":[{\"include\":\"#disabled\"},{\"include\":\"#pragma-mark\"}]},{\"begin\":\"\\\\n\",\"end\":\"(?=^\\\\s*((#)\\\\s*e(?:lse|lif|ndif))\\\\b)\",\"patterns\":[{\"include\":\"$self\"}]}]}]},\"preprocessor-rule-enabled-block\":{\"patterns\":[{\"begin\":\"^\\\\s*((#)\\\\s*if)\\\\b(?=\\\\s*\\\\(*\\\\b0*1\\\\b\\\\)*\\\\s*(?:$|//|/\\\\*))\",\"beginCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"end\":\"^\\\\s*((#)\\\\s*endif)\\\\b\",\"endCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"patterns\":[{\"begin\":\"\\\\G(?=.)(?!/(?:/|\\\\*(?!.*\\\\\\\\\\\\s*\\\\n)))\",\"end\":\"(?=//)|(?=/\\\\*(?!.*\\\\\\\\\\\\s*\\\\n))|(?=\\\\n)\",\"name\":\"meta.preprocessor.c\",\"patterns\":[{\"include\":\"#preprocessor-rule-conditional-line\"}]},{\"include\":\"#comments\"},{\"begin\":\"^\\\\s*((#)\\\\s*else)\\\\b\",\"beginCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"contentName\":\"comment.block.preprocessor.else-branch.in-block.c\",\"end\":\"(?=^\\\\s*((#)\\\\s*endif)\\\\b)\",\"patterns\":[{\"include\":\"#disabled\"},{\"include\":\"#pragma-mark\"}]},{\"begin\":\"^\\\\s*((#)\\\\s*elif)\\\\b\",\"beginCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"contentName\":\"comment.block.preprocessor.if-branch.in-block.c\",\"end\":\"(?=^\\\\s*((#)\\\\s*e(?:lse|lif|ndif))\\\\b)\",\"patterns\":[{\"include\":\"#disabled\"},{\"include\":\"#pragma-mark\"}]},{\"begin\":\"\\\\n\",\"end\":\"(?=^\\\\s*((#)\\\\s*e(?:lse|lif|ndif))\\\\b)\",\"patterns\":[{\"include\":\"#block_innards\"}]}]}]},\"preprocessor-rule-enabled-elif\":{\"begin\":\"^\\\\s*((#)\\\\s*elif)\\\\b(?=\\\\s*\\\\(*\\\\b0*1\\\\b\\\\)*\\\\s*(?:$|//|/\\\\*))\",\"beginCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"end\":\"(?=^\\\\s*((#)\\\\s*endif)\\\\b)\",\"patterns\":[{\"begin\":\"\\\\G(?=.)(?!/(?:/|\\\\*(?!.*\\\\\\\\\\\\s*\\\\n)))\",\"end\":\"(?=//)|(?=/\\\\*(?!.*\\\\\\\\\\\\s*\\\\n))|(?<!\\\\\\\\)(?=\\\\n)\",\"name\":\"meta.preprocessor.c\",\"patterns\":[{\"include\":\"#preprocessor-rule-conditional-line\"}]},{\"include\":\"#comments\"},{\"begin\":\"\\\\n\",\"end\":\"(?=^\\\\s*((#)\\\\s*endif)\\\\b)\",\"patterns\":[{\"begin\":\"^\\\\s*((#)\\\\s*(else))\\\\b\",\"beginCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"contentName\":\"comment.block.preprocessor.elif-branch.c\",\"end\":\"(?=^\\\\s*((#)\\\\s*endif)\\\\b)\",\"patterns\":[{\"include\":\"#disabled\"},{\"include\":\"#pragma-mark\"}]},{\"begin\":\"^\\\\s*((#)\\\\s*(elif))\\\\b\",\"beginCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"contentName\":\"comment.block.preprocessor.elif-branch.c\",\"end\":\"(?=^\\\\s*((#)\\\\s*e(?:lse|lif|ndif))\\\\b)\",\"patterns\":[{\"include\":\"#disabled\"},{\"include\":\"#pragma-mark\"}]},{\"include\":\"$self\"}]}]},\"preprocessor-rule-enabled-elif-block\":{\"begin\":\"^\\\\s*((#)\\\\s*elif)\\\\b(?=\\\\s*\\\\(*\\\\b0*1\\\\b\\\\)*\\\\s*(?:$|//|/\\\\*))\",\"beginCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"end\":\"(?=^\\\\s*((#)\\\\s*endif)\\\\b)\",\"patterns\":[{\"begin\":\"\\\\G(?=.)(?!/(?:/|\\\\*(?!.*\\\\\\\\\\\\s*\\\\n)))\",\"end\":\"(?=//)|(?=/\\\\*(?!.*\\\\\\\\\\\\s*\\\\n))|(?<!\\\\\\\\)(?=\\\\n)\",\"name\":\"meta.preprocessor.c\",\"patterns\":[{\"include\":\"#preprocessor-rule-conditional-line\"}]},{\"include\":\"#comments\"},{\"begin\":\"\\\\n\",\"end\":\"(?=^\\\\s*((#)\\\\s*endif)\\\\b)\",\"patterns\":[{\"begin\":\"^\\\\s*((#)\\\\s*(else))\\\\b\",\"beginCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"contentName\":\"comment.block.preprocessor.elif-branch.in-block.c\",\"end\":\"(?=^\\\\s*((#)\\\\s*endif)\\\\b)\",\"patterns\":[{\"include\":\"#disabled\"},{\"include\":\"#pragma-mark\"}]},{\"begin\":\"^\\\\s*((#)\\\\s*(elif))\\\\b\",\"beginCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"contentName\":\"comment.block.preprocessor.elif-branch.c\",\"end\":\"(?=^\\\\s*((#)\\\\s*e(?:lse|lif|ndif))\\\\b)\",\"patterns\":[{\"include\":\"#disabled\"},{\"include\":\"#pragma-mark\"}]},{\"include\":\"#block_innards\"}]}]},\"preprocessor-rule-enabled-else\":{\"begin\":\"^\\\\s*((#)\\\\s*else)\\\\b\",\"beginCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"end\":\"(?=^\\\\s*((#)\\\\s*endif)\\\\b)\",\"patterns\":[{\"include\":\"$self\"}]},\"preprocessor-rule-enabled-else-block\":{\"begin\":\"^\\\\s*((#)\\\\s*else)\\\\b\",\"beginCaptures\":{\"0\":{\"name\":\"meta.preprocessor.c\"},\"1\":{\"name\":\"keyword.control.directive.conditional.c\"},\"2\":{\"name\":\"punctuation.definition.directive.c\"}},\"end\":\"(?=^\\\\s*((#)\\\\s*endif)\\\\b)\",\"patterns\":[{\"include\":\"#block_innards\"}]},\"probably_a_parameter\":{\"captures\":{\"1\":{\"name\":\"variable.parameter.probably.c\"}},\"match\":\"(?<=[0-9A-Z_a-z] |[]\\\\&)*>])\\\\s*([A-Z_a-z]\\\\w*)\\\\s*(?=(?:\\\\[]\\\\s*)?[),])\"},\"static_assert\":{\"begin\":\"((?>(?:(?>(?<!\\\\s)\\\\s+)|(/\\\\*)((?>(?:[^*]|(?>\\\\*+)[^/])*)((?>\\\\*+)/)))+|(?:(?:(?:(?:\\\\b|(?<=\\\\W))|(?=\\\\W))|\\\\A)|\\\\Z)))((?<!\\\\w)static_assert|_Static_assert(?!\\\\w))((?>(?:(?>(?<!\\\\s)\\\\s+)|(/\\\\*)((?>(?:[^*]|(?>\\\\*+)[^/])*)((?>\\\\*+)/)))+|(?:(?:(?:(?:\\\\b|(?<=\\\\W))|(?=\\\\W))|\\\\A)|\\\\Z)))(\\\\()\",\"beginCaptures\":{\"1\":{\"patterns\":[{\"include\":\"#inline_comment\"}]},\"2\":{\"name\":\"comment.block.c punctuation.definition.comment.begin.c\"},\"3\":{\"name\":\"comment.block.c\"},\"4\":{\"patterns\":[{\"match\":\"\\\\*/\",\"name\":\"comment.block.c punctuation.definition.comment.end.c\"},{\"match\":\"\\\\*\",\"name\":\"comment.block.c\"}]},\"5\":{\"name\":\"keyword.other.static_assert.c\"},\"6\":{\"patterns\":[{\"include\":\"#inline_comment\"}]},\"7\":{\"name\":\"comment.block.c punctuation.definition.comment.begin.c\"},\"8\":{\"name\":\"comment.block.c\"},\"9\":{\"patterns\":[{\"match\":\"\\\\*/\",\"name\":\"comment.block.c punctuation.definition.comment.end.c\"},{\"match\":\"\\\\*\",\"name\":\"comment.block.c\"}]},\"10\":{\"name\":\"punctuation.section.arguments.begin.bracket.round.static_assert.c\"}},\"end\":\"(\\\\))\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.section.arguments.end.bracket.round.static_assert.c\"}},\"patterns\":[{\"begin\":\"(,)\\\\s*(?=(?:L|u8?|U\\\\s*\\\")?)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.separator.delimiter.comma.c\"}},\"end\":\"(?=\\\\))\",\"name\":\"meta.static_assert.message.c\",\"patterns\":[{\"include\":\"#string_context\"}]},{\"include\":\"#evaluation_context\"}]},\"storage_types\":{\"patterns\":[{\"match\":\"(?-im:(?<!\\\\w)(?:unsigned|signed|double|_Bool|short|float|long|void|char|bool|int)(?!\\\\w))\",\"name\":\"storage.type.built-in.primitive.c\"},{\"match\":\"(?-im:(?<!\\\\w)(?:atomic_uint_least64_t|atomic_uint_least16_t|atomic_uint_least32_t|pthread_rwlockattr_t|atomic_uint_fast64_t|atomic_uint_fast32_t|atomic_uint_fast16_t|atomic_int_least64_t|atomic_int_least32_t|atomic_int_least16_t|atomic_uint_least8_t|atomic_uint_fast8_t|atomic_int_least8_t|atomic_int_fast16_t|pthread_mutexattr_t|atomic_int_fast32_t|atomic_int_fast64_t|atomic_int_fast8_t|pthread_condattr_t|atomic_ptrdiff_t|pthread_rwlock_t|atomic_uintptr_t|atomic_uintmax_t|atomic_intmax_t|atomic_intptr_t|atomic_char32_t|atomic_char16_t|pthread_mutex_t|pthread_cond_t|atomic_wchar_t|uint_least64_t|uint_least32_t|uint_least16_t|pthread_once_t|pthread_attr_t|int_least32_t|pthread_key_t|int_least16_t|int_least64_t|uint_least8_t|uint_fast16_t|uint_fast32_t|uint_fast64_t|atomic_ushort|atomic_ullong|atomic_size_t|int_fast16_t|int_fast64_t|uint_fast8_t|atomic_short|atomic_uchar|atomic_schar|int_least8_t|memory_order|atomic_llong|atomic_ulong|int_fast32_t|atomic_long|atomic_uint|atomic_char|int_fast8_t|suseconds_t|atomic_bool|atomic_int|_Imaginary|useconds_t|in_port_t|uintmax_t|pthread_t|blksize_t|in_addr_t|uintptr_t|blkcnt_t|uint16_t|uint32_t|uint64_t|u_quad_t|_Complex|intptr_t|intmax_t|segsz_t|u_short|nlink_t|uint8_t|int64_t|int32_t|int16_t|fixpt_t|daddr_t|caddr_t|qaddr_t|ssize_t|clock_t|swblk_t|u_long|mode_t|int8_t|time_t|ushort|u_char|quad_t|size_t|pid_t|gid_t|uid_t|dev_t|div_t|off_t|u_int|key_t|ino_t|uint|id_t)(?!\\\\w))\",\"name\":\"storage.type.built-in.c\"},{\"match\":\"(?-im:\\\\b(enum|struct|union)\\\\b)\",\"name\":\"storage.type.$1.c\"},{\"begin\":\"\\\\b(__asm__|asm)\\\\b\\\\s*((?:volatile)?)\",\"beginCaptures\":{\"1\":{\"name\":\"storage.type.asm.c\"},\"2\":{\"name\":\"storage.modifier.c\"}},\"end\":\"(?!\\\\G)\",\"name\":\"meta.asm.c\",\"patterns\":[{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#inline_comment\"}]},\"2\":{\"name\":\"comment.block.c punctuation.definition.comment.begin.c\"},\"3\":{\"name\":\"comment.block.c\"},\"4\":{\"patterns\":[{\"match\":\"\\\\*/\",\"name\":\"comment.block.c punctuation.definition.comment.end.c\"},{\"match\":\"\\\\*\",\"name\":\"comment.block.c\"}]}},\"match\":\"^((?:(?>\\\\s+)|(/\\\\*)((?>(?:[^*]|(?>\\\\*+)[^/])*)((?>\\\\*+)/)))+?|(?:(?:(?:(?:\\\\b|(?<=\\\\W))|(?=\\\\W))|\\\\A)|\\\\Z))(?:\\\\n|$)\"},{\"include\":\"#comments\"},{\"begin\":\"(((?:(?>\\\\s+)|(/\\\\*)((?>(?:[^*]|(?>\\\\*+)[^/])*)((?>\\\\*+)/)))+?|(?:(?:(?:(?:\\\\b|(?<=\\\\W))|(?=\\\\W))|\\\\A)|\\\\Z))\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.section.parens.begin.bracket.round.assembly.c\"},\"2\":{\"patterns\":[{\"include\":\"#inline_comment\"}]},\"3\":{\"name\":\"comment.block.c punctuation.definition.comment.begin.c\"},\"4\":{\"name\":\"comment.block.c\"},\"5\":{\"patterns\":[{\"match\":\"\\\\*/\",\"name\":\"comment.block.c punctuation.definition.comment.end.c\"},{\"match\":\"\\\\*\",\"name\":\"comment.block.c\"}]}},\"end\":\"(\\\\))\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.section.parens.end.bracket.round.assembly.c\"}},\"patterns\":[{\"begin\":\"(R?)(\\\")\",\"beginCaptures\":{\"1\":{\"name\":\"meta.encoding.c\"},\"2\":{\"name\":\"punctuation.definition.string.begin.assembly.c\"}},\"contentName\":\"meta.embedded.assembly.c\",\"end\":\"(\\\")\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.string.end.assembly.c\"}},\"name\":\"string.quoted.double.c\",\"patterns\":[{\"include\":\"source.asm\"},{\"include\":\"source.x86\"},{\"include\":\"source.x86_64\"},{\"include\":\"source.arm\"},{\"include\":\"#backslash_escapes\"},{\"include\":\"#string_escaped_char\"}]},{\"begin\":\"(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.section.parens.begin.bracket.round.assembly.inner.c\"}},\"end\":\"(\\\\))\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.section.parens.end.bracket.round.assembly.inner.c\"}},\"patterns\":[{\"include\":\"#evaluation_context\"}]},{\"captures\":{\"1\":{\"patterns\":[{\"include\":\"#inline_comment\"}]},\"2\":{\"name\":\"comment.block.c punctuation.definition.comment.begin.c\"},\"3\":{\"name\":\"comment.block.c\"},\"4\":{\"patterns\":[{\"match\":\"\\\\*/\",\"name\":\"comment.block.c punctuation.definition.comment.end.c\"},{\"match\":\"\\\\*\",\"name\":\"comment.block.c\"}]},\"5\":{\"name\":\"variable.other.asm.label.c\"},\"6\":{\"patterns\":[{\"include\":\"#inline_comment\"}]},\"7\":{\"name\":\"comment.block.c punctuation.definition.comment.begin.c\"},\"8\":{\"name\":\"comment.block.c\"},\"9\":{\"patterns\":[{\"match\":\"\\\\*/\",\"name\":\"comment.block.c punctuation.definition.comment.end.c\"},{\"match\":\"\\\\*\",\"name\":\"comment.block.c\"}]}},\"match\":\"\\\\[((?:(?>\\\\s+)|(/\\\\*)((?>(?:[^*]|(?>\\\\*+)[^/])*)((?>\\\\*+)/)))+?|(?:(?:(?:(?:\\\\b|(?<=\\\\W))|(?=\\\\W))|\\\\A)|\\\\Z))([A-Z_a-z]\\\\w*)((?:(?>\\\\s+)|(/\\\\*)((?>(?:[^*]|(?>\\\\*+)[^/])*)((?>\\\\*+)/)))+?|(?:(?:(?:(?:\\\\b|(?<=\\\\W))|(?=\\\\W))|\\\\A)|\\\\Z))]\"},{\"match\":\":\",\"name\":\"punctuation.separator.delimiter.colon.assembly.c\"},{\"include\":\"#comments\"}]}]}]},\"string_escaped_char\":{\"patterns\":[{\"match\":\"\\\\\\\\([\\\"'?\\\\\\\\abefnprtv]|[0-3]\\\\d{0,2}|[4-7]\\\\d?|x\\\\h{0,2}|u\\\\h{0,4}|U\\\\h{0,8})\",\"name\":\"constant.character.escape.c\"},{\"match\":\"\\\\\\\\.\",\"name\":\"invalid.illegal.unknown-escape.c\"}]},\"string_placeholder\":{\"patterns\":[{\"match\":\"%(\\\\d+\\\\$)?[- #'+0]*[,:;_]?((-?\\\\d+)|\\\\*(-?\\\\d+\\\\$)?)?(\\\\.((-?\\\\d+)|\\\\*(-?\\\\d+\\\\$)?)?)?(hh?|ll|[Ljlqtz]|vh|vl?|hv|hl)?[%AC-GOSUXac-ginopsux]\",\"name\":\"constant.other.placeholder.c\"},{\"captures\":{\"1\":{\"name\":\"invalid.illegal.placeholder.c\"}},\"match\":\"(%)(?!\\\"\\\\s*(PRI|SCN))\"}]},\"strings\":{\"patterns\":[{\"begin\":\"\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.c\"}},\"end\":\"\\\"\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.c\"}},\"name\":\"string.quoted.double.c\",\"patterns\":[{\"include\":\"#string_escaped_char\"},{\"include\":\"#string_placeholder\"},{\"include\":\"#line_continuation_character\"}]},{\"begin\":\"'\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.c\"}},\"end\":\"'\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.c\"}},\"name\":\"string.quoted.single.c\",\"patterns\":[{\"include\":\"#string_escaped_char\"},{\"include\":\"#line_continuation_character\"}]}]},\"switch_conditional_parentheses\":{\"begin\":\"((?>(?:(?>(?<!\\\\s)\\\\s+)|(/\\\\*)((?>(?:[^*]|(?>\\\\*+)[^/])*)((?>\\\\*+)/)))+|(?:(?:(?:(?:\\\\b|(?<=\\\\W))|(?=\\\\W))|\\\\A)|\\\\Z)))(\\\\()\",\"beginCaptures\":{\"1\":{\"patterns\":[{\"include\":\"#inline_comment\"}]},\"2\":{\"name\":\"comment.block.c punctuation.definition.comment.begin.c\"},\"3\":{\"name\":\"comment.block.c\"},\"4\":{\"patterns\":[{\"match\":\"\\\\*/\",\"name\":\"comment.block.c punctuation.definition.comment.end.c\"},{\"match\":\"\\\\*\",\"name\":\"comment.block.c\"}]},\"5\":{\"name\":\"punctuation.section.parens.begin.bracket.round.conditional.switch.c\"}},\"end\":\"(\\\\))\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.section.parens.end.bracket.round.conditional.switch.c\"}},\"name\":\"meta.conditional.switch.c\",\"patterns\":[{\"include\":\"#evaluation_context\"},{\"include\":\"#c_conditional_context\"}]},\"switch_statement\":{\"begin\":\"(((?>(?:(?>(?<!\\\\s)\\\\s+)|(/\\\\*)((?>(?:[^*]|(?>\\\\*+)[^/])*)((?>\\\\*+)/)))+|(?:(?:(?:(?:\\\\b|(?<=\\\\W))|(?=\\\\W))|\\\\A)|\\\\Z)))((?<!\\\\w)switch(?!\\\\w)))\",\"beginCaptures\":{\"1\":{\"name\":\"meta.head.switch.c\"},\"2\":{\"patterns\":[{\"include\":\"#inline_comment\"}]},\"3\":{\"name\":\"comment.block.c punctuation.definition.comment.begin.c\"},\"4\":{\"name\":\"comment.block.c\"},\"5\":{\"patterns\":[{\"match\":\"\\\\*/\",\"name\":\"comment.block.c punctuation.definition.comment.end.c\"},{\"match\":\"\\\\*\",\"name\":\"comment.block.c\"}]},\"6\":{\"name\":\"keyword.control.switch.c\"}},\"end\":\"(?<=}|%>|\\\\?\\\\?>)|(?=[];=>\\\\[])\",\"name\":\"meta.block.switch.c\",\"patterns\":[{\"begin\":\"\\\\G ?\",\"end\":\"(\\\\{|<%|\\\\?\\\\?<|(?=;))\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.section.block.begin.bracket.curly.switch.c\"}},\"name\":\"meta.head.switch.c\",\"patterns\":[{\"include\":\"#switch_conditional_parentheses\"},{\"include\":\"$self\"}]},{\"begin\":\"(?<=\\\\{|<%|\\\\?\\\\?<)\",\"end\":\"(}|%>|\\\\?\\\\?>)\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.section.block.end.bracket.curly.switch.c\"}},\"name\":\"meta.body.switch.c\",\"patterns\":[{\"include\":\"#default_statement\"},{\"include\":\"#case_statement\"},{\"include\":\"$self\"},{\"include\":\"#block_innards\"}]},{\"begin\":\"(?<=}|%>|\\\\?\\\\?>)[\\\\n\\\\s]*\",\"end\":\"[\\\\n\\\\s]*(?=;)\",\"name\":\"meta.tail.switch.c\",\"patterns\":[{\"include\":\"$self\"}]}]},\"vararg_ellipses\":{\"match\":\"(?<!\\\\.)\\\\.\\\\.\\\\.(?!\\\\.)\",\"name\":\"punctuation.vararg-ellipses.c\"}},\"scopeName\":\"source.c\"}"));
const __TURBOPACK__default__export__ = [
    lang
];
}}),
"[project]/node_modules/@shikijs/langs/dist/glsl.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$c$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/c.mjs [app-client] (ecmascript)");
;
const lang = Object.freeze(JSON.parse("{\"displayName\":\"GLSL\",\"fileTypes\":[\"vs\",\"fs\",\"gs\",\"vsh\",\"fsh\",\"gsh\",\"vshader\",\"fshader\",\"gshader\",\"vert\",\"frag\",\"geom\",\"f.glsl\",\"v.glsl\",\"g.glsl\"],\"foldingStartMarker\":\"/\\\\*\\\\*|\\\\{\\\\s*$\",\"foldingStopMarker\":\"\\\\*\\\\*/|^\\\\s*}\",\"name\":\"glsl\",\"patterns\":[{\"match\":\"\\\\b(break|case|continue|default|discard|do|else|for|if|return|switch|while)\\\\b\",\"name\":\"keyword.control.glsl\"},{\"match\":\"\\\\b(void|bool|int|uint|float|vec2|vec3|vec4|bvec2|bvec3|bvec4|ivec2|ivec3|uvec2|uvec3|mat2|mat3|mat4|mat2x2|mat2x3|mat2x4|mat3x2|mat3x3|mat3x4|mat4x2|mat4x3|mat4x4|sampler[123|]D|samplerCube|sampler2DRect|sampler[12|]DShadow|sampler2DRectShadow|sampler[12|]DArray|sampler[12|]DArrayShadow|samplerBuffer|sampler2DMS|sampler2DMSArray|struct|isampler[123|]D|isamplerCube|isampler2DRect|isampler[12|]DArray|isamplerBuffer|isampler2DMS|isampler2DMSArray|usampler[123|]D|usamplerCube|usampler2DRect|usampler[12|]DArray|usamplerBuffer|usampler2DMS|usampler2DMSArray)\\\\b\",\"name\":\"storage.type.glsl\"},{\"match\":\"\\\\b(attribute|centroid|const|flat|in|inout|invariant|noperspective|out|smooth|uniform|varying)\\\\b\",\"name\":\"storage.modifier.glsl\"},{\"match\":\"\\\\b(gl_(?:BackColor|BackLightModelProduct|BackLightProduct|BackMaterial|BackSecondaryColor|ClipDistance|ClipPlane|ClipVertex|Color|DepthRange|DepthRangeParameters|EyePlaneQ|EyePlaneR|EyePlaneS|EyePlaneT|Fog|FogCoord|FogFragCoord|FogParameters|FragColor|FragCoord|FragDat|FragDept|FrontColor|FrontFacing|FrontLightModelProduct|FrontLightProduct|FrontMaterial|FrontSecondaryColor|InstanceID|Layer|LightModel|LightModelParameters|LightModelProducts|LightProducts|LightSource|LightSourceParameters|MaterialParameters|ModelViewMatrix|ModelViewMatrixInverse|ModelViewMatrixInverseTranspose|ModelViewMatrixTranspose|ModelViewProjectionMatrix|ModelViewProjectionMatrixInverse|ModelViewProjectionMatrixInverseTranspose|ModelViewProjectionMatrixTranspose|MultiTexCoord[0-7]|Normal|NormalMatrix|NormalScale|ObjectPlaneQ|ObjectPlaneR|ObjectPlaneS|ObjectPlaneT|Point|PointCoord|PointParameters|PointSize|Position|PrimitiveIDIn|ProjectionMatrix|ProjectionMatrixInverse|ProjectionMatrixInverseTranspose|ProjectionMatrixTranspose|SecondaryColor|TexCoord|TextureEnvColor|TextureMatrix|TextureMatrixInverse|TextureMatrixInverseTranspose|TextureMatrixTranspose|Vertex|VertexIDh))\\\\b\",\"name\":\"support.variable.glsl\"},{\"match\":\"\\\\b(gl_Max(?:ClipPlane|CombinedTextureImageUnit|DrawBuffer|FragmentUniformComponent|Light|TextureCoord|TextureImageUnit|TextureUnit|VaryingFloat|VertexAttrib|VertexTextureImageUnit|VertexUniformComponent)s)\\\\b\",\"name\":\"support.constant.glsl\"},{\"match\":\"\\\\b(abs|acos|all|any|asin|atan|ceil|clamp|cos|cross|degrees|dFdx|dFdy|distance|dot|equal|exp2??|faceforward|floor|fract|ftransform|fwidth|greaterThan|greaterThanEqual|inversesqrt|length|lessThan|lessThanEqual|log2??|matrixCompMult|max|min|mix|mod|noise[1-4]|normalize|not|notEqual|outerProduct|pow|radians|reflect|refract|shadow1D|shadow1DLod|shadow1DProj|shadow1DProjLod|shadow2D|shadow2DLod|shadow2DProj|shadow2DProjLod|sign|sin|smoothstep|sqrt|step|tan|texture1D|texture1DLod|texture1DProj|texture1DProjLod|texture2D|texture2DLod|texture2DProj|texture2DProjLod|texture3D|texture3DLod|texture3DProj|texture3DProjLod|textureCube|textureCubeLod|transpose)\\\\b\",\"name\":\"support.function.glsl\"},{\"match\":\"\\\\b(asm|double|enum|extern|goto|inline|long|short|sizeof|static|typedef|union|unsigned|volatile)\\\\b\",\"name\":\"invalid.illegal.glsl\"},{\"include\":\"source.c\"}],\"scopeName\":\"source.glsl\",\"embeddedLangs\":[\"c\"]}"));
const __TURBOPACK__default__export__ = [
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$c$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    lang
];
}}),
"[project]/node_modules/@shikijs/langs/dist/shellscript.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const lang = Object.freeze(JSON.parse("{\"displayName\":\"Shell\",\"name\":\"shellscript\",\"patterns\":[{\"include\":\"#initial_context\"}],\"repository\":{\"alias_statement\":{\"begin\":\"[\\\\t ]*+(alias)[\\\\t ]*+((?:((?<!\\\\w)-\\\\w+)\\\\b[\\\\t ]*+)*)[\\\\t ]*+((?<!\\\\w)[-0-9A-Z_a-z]+(?!\\\\w))(?:(\\\\[)((?:(?:\\\\$?(?<!\\\\w)[-0-9A-Z_a-z]+(?!\\\\w)|@)|\\\\*)|(-?\\\\d+))(]))?(?:(?:(=)|(\\\\+=))|(-=))\",\"beginCaptures\":{\"1\":{\"name\":\"storage.type.alias.shell\"},\"2\":{\"patterns\":[{\"match\":\"(?<!\\\\w)-\\\\w+\\\\b\",\"name\":\"string.unquoted.argument.shell constant.other.option.shell\"}]},\"3\":{\"name\":\"string.unquoted.argument.shell constant.other.option.shell\"},\"4\":{\"name\":\"variable.other.assignment.shell\"},\"5\":{\"name\":\"punctuation.definition.array.access.shell\"},\"6\":{\"name\":\"variable.other.assignment.shell\"},\"7\":{\"name\":\"constant.numeric.shell constant.numeric.integer.shell\"},\"8\":{\"name\":\"punctuation.definition.array.access.shell\"},\"9\":{\"name\":\"keyword.operator.assignment.shell\"},\"10\":{\"name\":\"keyword.operator.assignment.compound.shell\"},\"11\":{\"name\":\"keyword.operator.assignment.compound.shell\"}},\"end\":\"(?=[\\\\t ]|$)|(?:(?:(?:(;)|(&&))|(\\\\|\\\\|))|(&))\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.terminator.statement.semicolon.shell\"},\"2\":{\"name\":\"punctuation.separator.statement.and.shell\"},\"3\":{\"name\":\"punctuation.separator.statement.or.shell\"},\"4\":{\"name\":\"punctuation.separator.statement.background.shell\"}},\"name\":\"meta.expression.assignment.alias.shell\",\"patterns\":[{\"include\":\"#normal_context\"}]},\"argument\":{\"begin\":\"[\\\\t ]++(?![\\\\n#\\\\&(\\\\[|]|$|;)\",\"beginCaptures\":{},\"end\":\"(?=[\\\\t \\\\&;|]|$|[\\\\n)`])\",\"endCaptures\":{},\"name\":\"meta.argument.shell\",\"patterns\":[{\"include\":\"#argument_context\"},{\"include\":\"#line_continuation\"}]},\"argument_context\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"string.unquoted.argument.shell\",\"patterns\":[{\"match\":\"\\\\*\",\"name\":\"variable.language.special.wildcard.shell\"},{\"include\":\"#variable\"},{\"include\":\"#numeric_literal\"},{\"captures\":{\"1\":{\"name\":\"constant.language.$1.shell\"}},\"match\":\"(?<!\\\\w)\\\\b(true|false)\\\\b(?!\\\\w)\"}]}},\"match\":\"[\\\\t ]*+([^\\\\t\\\\n \\\"$\\\\&-);<>\\\\\\\\`|]+(?!>))\"},{\"include\":\"#normal_context\"}]},\"arithmetic_double\":{\"patterns\":[{\"begin\":\"\\\\(\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.arithmetic.double.shell\"}},\"end\":\"\\\\)\\\\s*\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.arithmetic.double.shell\"}},\"name\":\"meta.arithmetic.shell\",\"patterns\":[{\"include\":\"#math\"},{\"include\":\"#string\"}]}]},\"arithmetic_no_dollar\":{\"patterns\":[{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.arithmetic.single.shell\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.arithmetic.single.shell\"}},\"name\":\"meta.arithmetic.shell\",\"patterns\":[{\"include\":\"#math\"},{\"include\":\"#string\"}]}]},\"array_access_inline\":{\"captures\":{\"1\":{\"name\":\"punctuation.section.array.shell\"},\"2\":{\"patterns\":[{\"include\":\"#special_expansion\"},{\"include\":\"#string\"},{\"include\":\"#variable\"}]},\"3\":{\"name\":\"punctuation.section.array.shell\"}},\"match\":\"(\\\\[)([^]\\\\[]+)(])\"},\"array_value\":{\"begin\":\"[\\\\t ]*+((?<!\\\\w)[-0-9A-Z_a-z]+(?!\\\\w))(?:(\\\\[)((?:(?:\\\\$?(?<!\\\\w)[-0-9A-Z_a-z]+(?!\\\\w)|@)|\\\\*)|(-?\\\\d+))(]))?(?:(?:(=)|(\\\\+=))|(-=))[\\\\t ]*+(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"variable.other.assignment.shell\"},\"2\":{\"name\":\"punctuation.definition.array.access.shell\"},\"3\":{\"name\":\"variable.other.assignment.shell\"},\"4\":{\"name\":\"constant.numeric.shell constant.numeric.integer.shell\"},\"5\":{\"name\":\"punctuation.definition.array.access.shell\"},\"6\":{\"name\":\"keyword.operator.assignment.shell\"},\"7\":{\"name\":\"keyword.operator.assignment.compound.shell\"},\"8\":{\"name\":\"keyword.operator.assignment.compound.shell\"},\"9\":{\"name\":\"punctuation.definition.array.shell\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.array.shell\"}},\"patterns\":[{\"include\":\"#comment\"},{\"captures\":{\"1\":{\"name\":\"variable.other.assignment.array.shell entity.other.attribute-name.shell\"},\"2\":{\"name\":\"keyword.operator.assignment.shell punctuation.definition.assignment.shell\"}},\"match\":\"((?<!\\\\w)[-0-9A-Z_a-z]+(?!\\\\w))(=)\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.bracket.named-array.shell\"},\"2\":{\"name\":\"string.unquoted.shell entity.other.attribute-name.bracket.shell\"},\"3\":{\"name\":\"punctuation.definition.bracket.named-array.shell\"},\"4\":{\"name\":\"punctuation.definition.assignment.shell\"}},\"match\":\"(\\\\[)(.+?)(])(=)\"},{\"include\":\"#normal_context\"},{\"include\":\"#simple_unquoted\"}]},\"assignment_statement\":{\"patterns\":[{\"include\":\"#array_value\"},{\"include\":\"#modified_assignment_statement\"},{\"include\":\"#normal_assignment_statement\"}]},\"basic_command_name\":{\"captures\":{\"1\":{\"name\":\"storage.modifier.$1.shell\"},\"2\":{\"name\":\"entity.name.function.call.shell entity.name.command.shell\",\"patterns\":[{\"match\":\"(?<!\\\\w)(?:continue|return|break)(?!\\\\w)\",\"name\":\"keyword.control.$0.shell\"},{\"match\":\"(?<!\\\\w)(?:unfunction|continue|autoload|unsetopt|bindkey|builtin|getopts|command|declare|unalias|history|unlimit|typeset|suspend|source|printf|unhash|disown|ulimit|return|which|alias|break|false|print|shift|times|umask|unset|read|type|exec|eval|wait|echo|dirs|jobs|kill|hash|stat|exit|test|trap|true|let|set|pwd|cd|fg|bg|fc|[.:])(?!/)(?!\\\\w)(?!-)\",\"name\":\"support.function.builtin.shell\"},{\"include\":\"#variable\"}]}},\"match\":\"(?![\\\\n!#\\\\&()<>\\\\[{|]|$|[\\\\t ;])(?!nocorrect |nocorrect\\\\t|nocorrect$|readonly |readonly\\\\t|readonly$|function |function\\\\t|function$|foreach |foreach\\\\t|foreach$|coproc |coproc\\\\t|coproc$|logout |logout\\\\t|logout$|export |export\\\\t|export$|select |select\\\\t|select$|repeat |repeat\\\\t|repeat$|pushd |pushd\\\\t|pushd$|until |until\\\\t|until$|while |while\\\\t|while$|local |local\\\\t|local$|case |case\\\\t|case$|done |done\\\\t|done$|elif |elif\\\\t|elif$|else |else\\\\t|else$|esac |esac\\\\t|esac$|popd |popd\\\\t|popd$|then |then\\\\t|then$|time |time\\\\t|time$|for |for\\\\t|for$|end |end\\\\t|end$|fi |fi\\\\t|fi$|do |do\\\\t|do$|in |in\\\\t|in$|if |if\\\\t|if$)(?:((?<=^|[\\\\t \\\\&;])(?:readonly|declare|typeset|export|local)(?=[\\\\t \\\\&;]|$))|((?![\\\"']|\\\\\\\\\\\\n?$)[^\\\\t\\\\n\\\\r !\\\"'<>]+?))(?:(?=[\\\\t ])|(?=[\\\\n\\\\&);`{|}]|[\\\\t ]*#|])(?<!\\\\\\\\))\",\"name\":\"meta.statement.command.name.basic.shell\"},\"block_comment\":{\"begin\":\"\\\\s*+(/\\\\*)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.comment.begin.shell\"}},\"end\":\"\\\\*/\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.end.shell\"}},\"name\":\"comment.block.shell\"},\"boolean\":{\"match\":\"\\\\b(?:true|false)\\\\b\",\"name\":\"constant.language.$0.shell\"},\"case_statement\":{\"begin\":\"\\\\b(case)\\\\b[\\\\t ]*+(.+?)[\\\\t ]*+\\\\b(in)\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.case.shell\"},\"2\":{\"patterns\":[{\"include\":\"#initial_context\"}]},\"3\":{\"name\":\"keyword.control.in.shell\"}},\"end\":\"\\\\besac\\\\b\",\"endCaptures\":{\"0\":{\"name\":\"keyword.control.esac.shell\"}},\"name\":\"meta.case.shell\",\"patterns\":[{\"include\":\"#comment\"},{\"captures\":{\"1\":{\"name\":\"keyword.operator.pattern.case.default.shell\"}},\"match\":\"[\\\\t ]*+(\\\\* *\\\\))\"},{\"begin\":\"(?<!\\\\))(?![\\\\t ]*+(?:esac\\\\b|$))\",\"beginCaptures\":{},\"end\":\"(?=\\\\besac\\\\b)|(\\\\))\",\"endCaptures\":{\"1\":{\"name\":\"keyword.operator.pattern.case.shell\"}},\"name\":\"meta.case.entry.pattern.shell\",\"patterns\":[{\"include\":\"#case_statement_context\"}]},{\"begin\":\"(?<=\\\\))\",\"beginCaptures\":{},\"end\":\"(;;)|(?=\\\\besac\\\\b)\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.terminator.statement.case.shell\"}},\"name\":\"meta.case.entry.body.shell\",\"patterns\":[{\"include\":\"#typical_statements\"},{\"include\":\"#initial_context\"}]}]},\"case_statement_context\":{\"patterns\":[{\"match\":\"\\\\*\",\"name\":\"variable.language.special.quantifier.star.shell keyword.operator.quantifier.star.shell punctuation.definition.arbitrary-repetition.shell punctuation.definition.regex.arbitrary-repetition.shell\"},{\"match\":\"\\\\+\",\"name\":\"variable.language.special.quantifier.plus.shell keyword.operator.quantifier.plus.shell punctuation.definition.arbitrary-repetition.shell punctuation.definition.regex.arbitrary-repetition.shell\"},{\"match\":\"\\\\?\",\"name\":\"variable.language.special.quantifier.question.shell keyword.operator.quantifier.question.shell punctuation.definition.arbitrary-repetition.shell punctuation.definition.regex.arbitrary-repetition.shell\"},{\"match\":\"@\",\"name\":\"variable.language.special.at.shell keyword.operator.at.shell punctuation.definition.regex.at.shell\"},{\"match\":\"\\\\|\",\"name\":\"keyword.operator.orvariable.language.special.or.shell keyword.operator.alternation.ruby.shell punctuation.definition.regex.alternation.shell punctuation.separator.regex.alternation.shell\"},{\"match\":\"\\\\\\\\.\",\"name\":\"constant.character.escape.shell\"},{\"match\":\"(?<=\\\\tin| in|[\\\\t ]|;;)\\\\(\",\"name\":\"keyword.operator.pattern.case.shell\"},{\"begin\":\"(?<=\\\\S)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.group.shell punctuation.definition.regex.group.shell\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.group.shell punctuation.definition.regex.group.shell\"}},\"name\":\"meta.parenthese.shell\",\"patterns\":[{\"include\":\"#case_statement_context\"}]},{\"begin\":\"\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.character-class.shell\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.character-class.shell\"}},\"name\":\"string.regexp.character-class.shell\",\"patterns\":[{\"match\":\"\\\\\\\\.\",\"name\":\"constant.character.escape.shell\"}]},{\"include\":\"#string\"},{\"match\":\"[^\\\\t\\\\n )*?@\\\\[|]\",\"name\":\"string.unquoted.pattern.shell string.regexp.unquoted.shell\"}]},\"command_name_range\":{\"begin\":\"\\\\G\",\"beginCaptures\":{},\"end\":\"(?=[\\\\t \\\\&;|]|$|[\\\\n)`])|(?=<)\",\"endCaptures\":{},\"name\":\"meta.statement.command.name.shell\",\"patterns\":[{\"match\":\"(?<!\\\\w)(?:continue|return|break)(?!\\\\w)\",\"name\":\"entity.name.function.call.shell entity.name.command.shell keyword.control.$0.shell\"},{\"match\":\"(?<!\\\\w)(?:unfunction|continue|autoload|unsetopt|bindkey|builtin|getopts|command|declare|unalias|history|unlimit|typeset|suspend|source|printf|unhash|disown|ulimit|return|which|alias|break|false|print|shift|times|umask|unset|read|type|exec|eval|wait|echo|dirs|jobs|kill|hash|stat|exit|test|trap|true|let|set|pwd|cd|fg|bg|fc|[.:])(?!/)(?!\\\\w)(?!-)\",\"name\":\"entity.name.function.call.shell entity.name.command.shell support.function.builtin.shell\"},{\"include\":\"#variable\"},{\"captures\":{\"1\":{\"name\":\"entity.name.function.call.shell entity.name.command.shell\"}},\"match\":\"(?<!\\\\w)(?<=\\\\G|[\\\"')}])([^\\\\t\\\\n\\\\r \\\"\\\\&');->`{|]+)\"},{\"begin\":\"(?:\\\\G|(?<![\\\\t\\\\n #\\\\&;{|]))(\\\\$?)((\\\")|('))\",\"beginCaptures\":{\"1\":{\"name\":\"meta.statement.command.name.quoted.shell punctuation.definition.string.shell entity.name.function.call.shell entity.name.command.shell\"},\"2\":{},\"3\":{\"name\":\"meta.statement.command.name.quoted.shell string.quoted.double.shell punctuation.definition.string.begin.shell entity.name.function.call.shell entity.name.command.shell\"},\"4\":{\"name\":\"meta.statement.command.name.quoted.shell string.quoted.single.shell punctuation.definition.string.begin.shell entity.name.function.call.shell entity.name.command.shell\"}},\"end\":\"(?<!\\\\G)(?<=\\\\2)\",\"endCaptures\":{},\"patterns\":[{\"include\":\"#continuation_of_single_quoted_command_name\"},{\"include\":\"#continuation_of_double_quoted_command_name\"}]},{\"include\":\"#line_continuation\"},{\"include\":\"#simple_unquoted\"}]},\"command_statement\":{\"begin\":\"[\\\\t ]*+(?![\\\\n!#\\\\&()<>\\\\[{|]|$|[\\\\t ;])(?!nocorrect |nocorrect\\\\t|nocorrect$|readonly |readonly\\\\t|readonly$|function |function\\\\t|function$|foreach |foreach\\\\t|foreach$|coproc |coproc\\\\t|coproc$|logout |logout\\\\t|logout$|export |export\\\\t|export$|select |select\\\\t|select$|repeat |repeat\\\\t|repeat$|pushd |pushd\\\\t|pushd$|until |until\\\\t|until$|while |while\\\\t|while$|local |local\\\\t|local$|case |case\\\\t|case$|done |done\\\\t|done$|elif |elif\\\\t|elif$|else |else\\\\t|else$|esac |esac\\\\t|esac$|popd |popd\\\\t|popd$|then |then\\\\t|then$|time |time\\\\t|time$|for |for\\\\t|for$|end |end\\\\t|end$|fi |fi\\\\t|fi$|do |do\\\\t|do$|in |in\\\\t|in$|if |if\\\\t|if$)(?!\\\\\\\\\\\\n?$)\",\"beginCaptures\":{},\"end\":\"(?=[\\\\n\\\\&);`{|}]|[\\\\t ]*#|])(?<!\\\\\\\\)\",\"endCaptures\":{},\"name\":\"meta.statement.command.shell\",\"patterns\":[{\"include\":\"#command_name_range\"},{\"include\":\"#line_continuation\"},{\"include\":\"#option\"},{\"include\":\"#argument\"},{\"include\":\"#string\"},{\"include\":\"#heredoc\"}]},\"comment\":{\"captures\":{\"1\":{\"name\":\"comment.line.number-sign.shell meta.shebang.shell\"},\"2\":{\"name\":\"punctuation.definition.comment.shebang.shell\"},\"3\":{\"name\":\"comment.line.number-sign.shell\"},\"4\":{\"name\":\"punctuation.definition.comment.shell\"}},\"match\":\"(?:^|[\\\\t ]++)(?:((#!).*)|((#).*))\"},\"comments\":{\"patterns\":[{\"include\":\"#block_comment\"},{\"include\":\"#line_comment\"}]},\"compound-command\":{\"patterns\":[{\"begin\":\"\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.logical-expression.shell\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.logical-expression.shell\"}},\"name\":\"meta.scope.logical-expression.shell\",\"patterns\":[{\"include\":\"#logical-expression\"},{\"include\":\"#initial_context\"}]},{\"begin\":\"(?<=\\\\s|^)\\\\{(?=\\\\s|$)\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.group.shell\"}},\"end\":\"(?<=^|;)\\\\s*(})\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.group.shell\"}},\"name\":\"meta.scope.group.shell\",\"patterns\":[{\"include\":\"#initial_context\"}]}]},\"continuation_of_double_quoted_command_name\":{\"begin\":\"\\\\G(?<=\\\")\",\"beginCaptures\":{},\"contentName\":\"meta.statement.command.name.continuation string.quoted.double entity.name.function.call entity.name.command\",\"end\":\"\\\"\",\"endCaptures\":{\"0\":{\"name\":\"string.quoted.double.shell punctuation.definition.string.end.shell entity.name.function.call.shell entity.name.command.shell\"}},\"patterns\":[{\"match\":\"\\\\\\\\[\\\\n\\\"$\\\\\\\\`]\",\"name\":\"constant.character.escape.shell\"},{\"include\":\"#variable\"},{\"include\":\"#interpolation\"}]},\"continuation_of_single_quoted_command_name\":{\"begin\":\"\\\\G(?<=')\",\"beginCaptures\":{},\"contentName\":\"meta.statement.command.name.continuation string.quoted.single entity.name.function.call entity.name.command\",\"end\":\"'\",\"endCaptures\":{\"0\":{\"name\":\"string.quoted.single.shell punctuation.definition.string.end.shell entity.name.function.call.shell entity.name.command.shell\"}}},\"custom_command_names\":{\"patterns\":[]},\"custom_commands\":{\"patterns\":[]},\"double_quote_context\":{\"patterns\":[{\"match\":\"\\\\\\\\[\\\\n\\\"$\\\\\\\\`]\",\"name\":\"constant.character.escape.shell\"},{\"include\":\"#variable\"},{\"include\":\"#interpolation\"}]},\"double_quote_escape_char\":{\"match\":\"\\\\\\\\[\\\\n\\\"$\\\\\\\\`]\",\"name\":\"constant.character.escape.shell\"},\"floating_keyword\":{\"patterns\":[{\"match\":\"(?<=^|[\\\\t \\\\&;])(?:then|elif|else|done|end|do|if|fi)(?=[\\\\t \\\\&;]|$)\",\"name\":\"keyword.control.$0.shell\"}]},\"for_statement\":{\"patterns\":[{\"begin\":\"\\\\b(for)\\\\b[\\\\t ]*+((?<!\\\\w)[-0-9A-Z_a-z]+(?!\\\\w))[\\\\t ]*+\\\\b(in)\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.for.shell\"},\"2\":{\"name\":\"variable.other.for.shell\"},\"3\":{\"name\":\"keyword.control.in.shell\"}},\"end\":\"(?=[\\\\n\\\\&);`{|}]|[\\\\t ]*#|])(?<!\\\\\\\\)\",\"endCaptures\":{},\"name\":\"meta.for.in.shell\",\"patterns\":[{\"include\":\"#string\"},{\"include\":\"#simple_unquoted\"},{\"include\":\"#normal_context\"}]},{\"begin\":\"\\\\b(for)\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.for.shell\"}},\"end\":\"(?=[\\\\n\\\\&);`{|}]|[\\\\t ]*#|])(?<!\\\\\\\\)\",\"endCaptures\":{},\"name\":\"meta.for.shell\",\"patterns\":[{\"include\":\"#arithmetic_double\"},{\"include\":\"#normal_context\"}]}]},\"function_definition\":{\"applyEndPatternLast\":1,\"begin\":\"[\\\\t ]*+(?:\\\\b(function)\\\\b[\\\\t ]*+([^\\\\t\\\\n\\\\r \\\"'()=]+)(?:(\\\\()[\\\\t ]*+(\\\\)))?|([^\\\\t\\\\n\\\\r \\\"'()=]+)[\\\\t ]*+(\\\\()[\\\\t ]*+(\\\\)))\",\"beginCaptures\":{\"1\":{\"name\":\"storage.type.function.shell\"},\"2\":{\"name\":\"entity.name.function.shell\"},\"3\":{\"name\":\"punctuation.definition.arguments.shell\"},\"4\":{\"name\":\"punctuation.definition.arguments.shell\"},\"5\":{\"name\":\"entity.name.function.shell\"},\"6\":{\"name\":\"punctuation.definition.arguments.shell\"},\"7\":{\"name\":\"punctuation.definition.arguments.shell\"}},\"end\":\"(?<=[)}])\",\"endCaptures\":{},\"name\":\"meta.function.shell\",\"patterns\":[{\"match\":\"\\\\G[\\\\t\\\\n ]\"},{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.group.shell punctuation.section.function.definition.shell\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.group.shell punctuation.section.function.definition.shell\"}},\"name\":\"meta.function.body.shell\",\"patterns\":[{\"include\":\"#initial_context\"}]},{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.group.shell punctuation.section.function.definition.shell\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.group.shell punctuation.section.function.definition.shell\"}},\"name\":\"meta.function.body.shell\",\"patterns\":[{\"include\":\"#initial_context\"}]},{\"include\":\"#initial_context\"}]},\"heredoc\":{\"patterns\":[{\"begin\":\"((?<!<)<<-)[\\\\t ]*+([\\\"'])[\\\\t ]*+([^\\\"']+?)(?=[\\\"\\\\&';<\\\\s])(\\\\2)(.*)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.operator.heredoc.shell\"},\"2\":{\"name\":\"punctuation.definition.string.heredoc.quote.shell\"},\"3\":{\"name\":\"punctuation.definition.string.heredoc.delimiter.shell\"},\"4\":{\"name\":\"punctuation.definition.string.heredoc.quote.shell\"},\"5\":{\"patterns\":[{\"include\":\"#redirect_fix\"},{\"include\":\"#typical_statements\"}]}},\"contentName\":\"string.quoted.heredoc.indent.$3\",\"end\":\"^\\\\t*\\\\3(?=[\\\\&;\\\\s]|$)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.heredoc.$0.shell\"}},\"patterns\":[]},{\"begin\":\"((?<!<)<<(?!<))[\\\\t ]*+([\\\"'])[\\\\t ]*+([^\\\"']+?)(?=[\\\"\\\\&';<\\\\s])(\\\\2)(.*)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.operator.heredoc.shell\"},\"2\":{\"name\":\"punctuation.definition.string.heredoc.quote.shell\"},\"3\":{\"name\":\"punctuation.definition.string.heredoc.delimiter.shell\"},\"4\":{\"name\":\"punctuation.definition.string.heredoc.quote.shell\"},\"5\":{\"patterns\":[{\"include\":\"#redirect_fix\"},{\"include\":\"#typical_statements\"}]}},\"contentName\":\"string.quoted.heredoc.no-indent.$3\",\"end\":\"^\\\\3(?=[\\\\&;\\\\s]|$)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.heredoc.delimiter.shell\"}},\"patterns\":[]},{\"begin\":\"((?<!<)<<-)[\\\\t ]*+([^\\\\t \\\"']+)(?=[\\\"\\\\&';<\\\\s])(.*)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.operator.heredoc.shell\"},\"2\":{\"name\":\"punctuation.definition.string.heredoc.delimiter.shell\"},\"3\":{\"patterns\":[{\"include\":\"#redirect_fix\"},{\"include\":\"#typical_statements\"}]}},\"contentName\":\"string.unquoted.heredoc.indent.$2\",\"end\":\"^\\\\t*\\\\2(?=[\\\\&;\\\\s]|$)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.heredoc.delimiter.shell\"}},\"patterns\":[{\"include\":\"#double_quote_escape_char\"},{\"include\":\"#variable\"},{\"include\":\"#interpolation\"}]},{\"begin\":\"((?<!<)<<(?!<))[\\\\t ]*+([^\\\\t \\\"']+)(?=[\\\"\\\\&';<\\\\s])(.*)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.operator.heredoc.shell\"},\"2\":{\"name\":\"punctuation.definition.string.heredoc.delimiter.shell\"},\"3\":{\"patterns\":[{\"include\":\"#redirect_fix\"},{\"include\":\"#typical_statements\"}]}},\"contentName\":\"string.unquoted.heredoc.no-indent.$2\",\"end\":\"^\\\\2(?=[\\\\&;\\\\s]|$)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.heredoc.delimiter.shell\"}},\"patterns\":[{\"include\":\"#double_quote_escape_char\"},{\"include\":\"#variable\"},{\"include\":\"#interpolation\"}]}]},\"herestring\":{\"patterns\":[{\"begin\":\"(<<<)\\\\s*(('))\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.operator.herestring.shell\"},\"2\":{\"name\":\"string.quoted.single.shell\"},\"3\":{\"name\":\"punctuation.definition.string.begin.shell\"}},\"contentName\":\"string.quoted.single.shell\",\"end\":\"(')\",\"endCaptures\":{\"0\":{\"name\":\"string.quoted.single.shell\"},\"1\":{\"name\":\"punctuation.definition.string.end.shell\"}},\"name\":\"meta.herestring.shell\"},{\"begin\":\"(<<<)\\\\s*((\\\"))\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.operator.herestring.shell\"},\"2\":{\"name\":\"string.quoted.double.shell\"},\"3\":{\"name\":\"punctuation.definition.string.begin.shell\"}},\"contentName\":\"string.quoted.double.shell\",\"end\":\"(\\\")\",\"endCaptures\":{\"0\":{\"name\":\"string.quoted.double.shell\"},\"1\":{\"name\":\"punctuation.definition.string.end.shell\"}},\"name\":\"meta.herestring.shell\",\"patterns\":[{\"include\":\"#double_quote_context\"}]},{\"captures\":{\"1\":{\"name\":\"keyword.operator.herestring.shell\"},\"2\":{\"name\":\"string.unquoted.herestring.shell\",\"patterns\":[{\"include\":\"#initial_context\"}]}},\"match\":\"(<<<)\\\\s*(([^)\\\\\\\\\\\\s]|\\\\\\\\.)+)\",\"name\":\"meta.herestring.shell\"}]},\"initial_context\":{\"patterns\":[{\"include\":\"#comment\"},{\"include\":\"#pipeline\"},{\"include\":\"#normal_statement_seperator\"},{\"include\":\"#logical_expression_double\"},{\"include\":\"#logical_expression_single\"},{\"include\":\"#assignment_statement\"},{\"include\":\"#case_statement\"},{\"include\":\"#for_statement\"},{\"include\":\"#loop\"},{\"include\":\"#function_definition\"},{\"include\":\"#line_continuation\"},{\"include\":\"#arithmetic_double\"},{\"include\":\"#misc_ranges\"},{\"include\":\"#variable\"},{\"include\":\"#interpolation\"},{\"include\":\"#heredoc\"},{\"include\":\"#herestring\"},{\"include\":\"#redirection\"},{\"include\":\"#pathname\"},{\"include\":\"#floating_keyword\"},{\"include\":\"#alias_statement\"},{\"include\":\"#normal_statement\"},{\"include\":\"#string\"},{\"include\":\"#support\"}]},\"inline_comment\":{\"captures\":{\"1\":{\"name\":\"comment.block.shell punctuation.definition.comment.begin.shell\"},\"2\":{\"name\":\"comment.block.shell\"},\"3\":{\"patterns\":[{\"match\":\"\\\\*/\",\"name\":\"comment.block.shell punctuation.definition.comment.end.shell\"},{\"match\":\"\\\\*\",\"name\":\"comment.block.shell\"}]}},\"match\":\"(/\\\\*)((?:[^*]|\\\\*++[^/])*+(\\\\*++/))\"},\"interpolation\":{\"patterns\":[{\"include\":\"#arithmetic_dollar\"},{\"include\":\"#subshell_dollar\"},{\"begin\":\"`\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.evaluation.backticks.shell\"}},\"end\":\"`\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.evaluation.backticks.shell\"}},\"name\":\"string.interpolated.backtick.shell\",\"patterns\":[{\"match\":\"\\\\\\\\[$\\\\\\\\`]\",\"name\":\"constant.character.escape.shell\"},{\"begin\":\"(?<=\\\\W)(?=#)(?!#\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.whitespace.comment.leading.shell\"}},\"end\":\"(?!\\\\G)\",\"patterns\":[{\"begin\":\"#\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.shell\"}},\"end\":\"(?=`)\",\"name\":\"comment.line.number-sign.shell\"}]},{\"include\":\"#initial_context\"}]}]},\"keyword\":{\"patterns\":[{\"match\":\"(?<=^|[\\\\&;\\\\s])(then|else|elif|fi|for|in|do|done|select|continue|esac|while|until|return)(?=[\\\\&;\\\\s]|$)\",\"name\":\"keyword.control.shell\"},{\"match\":\"(?<=^|[\\\\&;\\\\s])(?:export|declare|typeset|local|readonly)(?=[\\\\&;\\\\s]|$)\",\"name\":\"storage.modifier.shell\"}]},\"line_comment\":{\"begin\":\"\\\\s*+(//)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.comment.shell\"}},\"end\":\"(?<=\\\\n)(?<!\\\\\\\\\\\\n)\",\"endCaptures\":{},\"name\":\"comment.line.double-slash.shell\",\"patterns\":[{\"include\":\"#line_continuation_character\"}]},\"line_continuation\":{\"match\":\"\\\\\\\\(?=\\\\n)\",\"name\":\"constant.character.escape.line-continuation.shell\"},\"logical-expression\":{\"patterns\":[{\"include\":\"#arithmetic_no_dollar\"},{\"match\":\"=[=~]?|!=?|[<>]|&&|\\\\|\\\\|\",\"name\":\"keyword.operator.logical.shell\"},{\"match\":\"(?<!\\\\S)-(nt|ot|ef|eq|ne|l[et]|g[et]|[GLNOSa-hknopr-uwxz])\\\\b\",\"name\":\"keyword.operator.logical.shell\"}]},\"logical_expression_context\":{\"patterns\":[{\"include\":\"#regex_comparison\"},{\"include\":\"#arithmetic_no_dollar\"},{\"include\":\"#logical-expression\"},{\"include\":\"#logical_expression_single\"},{\"include\":\"#logical_expression_double\"},{\"include\":\"#comment\"},{\"include\":\"#boolean\"},{\"include\":\"#redirect_number\"},{\"include\":\"#numeric_literal\"},{\"include\":\"#pipeline\"},{\"include\":\"#normal_statement_seperator\"},{\"include\":\"#string\"},{\"include\":\"#variable\"},{\"include\":\"#interpolation\"},{\"include\":\"#heredoc\"},{\"include\":\"#herestring\"},{\"include\":\"#pathname\"},{\"include\":\"#floating_keyword\"},{\"include\":\"#support\"}]},\"logical_expression_double\":{\"begin\":\"\\\\[\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.logical-expression.shell\"}},\"end\":\"]]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.logical-expression.shell\"}},\"name\":\"meta.scope.logical-expression.shell\",\"patterns\":[{\"include\":\"#logical_expression_context\"}]},\"logical_expression_single\":{\"begin\":\"\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.logical-expression.shell\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.logical-expression.shell\"}},\"name\":\"meta.scope.logical-expression.shell\",\"patterns\":[{\"include\":\"#logical_expression_context\"}]},\"loop\":{\"patterns\":[{\"begin\":\"(?<=^|[\\\\&;\\\\s])(for)\\\\s+(.+?)\\\\s+(in)(?=[\\\\&;\\\\s]|$)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.shell\"},\"2\":{\"name\":\"variable.other.loop.shell\",\"patterns\":[{\"include\":\"#string\"}]},\"3\":{\"name\":\"keyword.control.shell\"}},\"end\":\"(?<=^|[\\\\&;\\\\s])done(?=[\\\\&;\\\\s]|$|\\\\))\",\"endCaptures\":{\"0\":{\"name\":\"keyword.control.shell\"}},\"name\":\"meta.scope.for-in-loop.shell\",\"patterns\":[{\"include\":\"#initial_context\"}]},{\"begin\":\"(?<=^|[\\\\&;\\\\s])(while|until)(?=[\\\\&;\\\\s]|$)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.shell\"}},\"end\":\"(?<=^|[\\\\&;\\\\s])done(?=[\\\\&;\\\\s]|$|\\\\))\",\"endCaptures\":{\"0\":{\"name\":\"keyword.control.shell\"}},\"name\":\"meta.scope.while-loop.shell\",\"patterns\":[{\"include\":\"#initial_context\"}]},{\"begin\":\"(?<=^|[\\\\&;\\\\s])(select)\\\\s+((?:[^\\\\\\\\\\\\s]|\\\\\\\\.)+)(?=[\\\\&;\\\\s]|$)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.shell\"},\"2\":{\"name\":\"variable.other.loop.shell\"}},\"end\":\"(?<=^|[\\\\&;\\\\s])(done)(?=[\\\\&;\\\\s]|$|\\\\))\",\"endCaptures\":{\"1\":{\"name\":\"keyword.control.shell\"}},\"name\":\"meta.scope.select-block.shell\",\"patterns\":[{\"include\":\"#initial_context\"}]},{\"begin\":\"(?<=^|[\\\\&;\\\\s])if(?=[\\\\&;\\\\s]|$)\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.control.if.shell\"}},\"end\":\"(?<=^|[\\\\&;\\\\s])fi(?=[\\\\&;\\\\s]|$)\",\"endCaptures\":{\"0\":{\"name\":\"keyword.control.fi.shell\"}},\"name\":\"meta.scope.if-block.shell\",\"patterns\":[{\"include\":\"#initial_context\"}]}]},\"math\":{\"patterns\":[{\"include\":\"#variable\"},{\"match\":\"\\\\+{1,2}|-{1,2}|[!~]|\\\\*{1,2}|[%/]|<[<=]?|>[=>]?|==|!=|^|\\\\|{1,2}|&{1,2}|[,:=?]|[-%\\\\&*+/^|]=|<<=|>>=\",\"name\":\"keyword.operator.arithmetic.shell\"},{\"match\":\"0[Xx]\\\\h+\",\"name\":\"constant.numeric.hex.shell\"},{\"match\":\";\",\"name\":\"punctuation.separator.semicolon.range\"},{\"match\":\"0\\\\d+\",\"name\":\"constant.numeric.octal.shell\"},{\"match\":\"\\\\d{1,2}#[0-9@-Z_a-z]+\",\"name\":\"constant.numeric.other.shell\"},{\"match\":\"\\\\d+\",\"name\":\"constant.numeric.integer.shell\"},{\"match\":\"(?<!\\\\w)[0-9A-Z_a-z]+(?!\\\\w)\",\"name\":\"variable.other.normal.shell\"}]},\"math_operators\":{\"patterns\":[{\"match\":\"\\\\+{1,2}|-{1,2}|[!~]|\\\\*{1,2}|[%/]|<[<=]?|>[=>]?|==|!=|^|\\\\|{1,2}|&{1,2}|[,:=?]|[-%\\\\&*+/^|]=|<<=|>>=\",\"name\":\"keyword.operator.arithmetic.shell\"},{\"match\":\"0[Xx]\\\\h+\",\"name\":\"constant.numeric.hex.shell\"},{\"match\":\"0\\\\d+\",\"name\":\"constant.numeric.octal.shell\"},{\"match\":\"\\\\d{1,2}#[0-9@-Z_a-z]+\",\"name\":\"constant.numeric.other.shell\"},{\"match\":\"\\\\d+\",\"name\":\"constant.numeric.integer.shell\"}]},\"misc_ranges\":{\"patterns\":[{\"include\":\"#logical_expression_single\"},{\"include\":\"#logical_expression_double\"},{\"include\":\"#subshell_dollar\"},{\"begin\":\"(?<![^\\\\t ])(\\\\{)(?![$\\\\w])\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.group.shell\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.group.shell\"}},\"name\":\"meta.scope.group.shell\",\"patterns\":[{\"include\":\"#initial_context\"}]}]},\"modified_assignment_statement\":{\"begin\":\"(?<=^|[\\\\t \\\\&;])(?:readonly|declare|typeset|export|local)(?=[\\\\t \\\\&;]|$)\",\"beginCaptures\":{\"0\":{\"name\":\"storage.modifier.$0.shell\"}},\"end\":\"(?=[\\\\n\\\\&);`{|}]|[\\\\t ]*#|])(?<!\\\\\\\\)\",\"endCaptures\":{},\"name\":\"meta.statement.shell meta.expression.assignment.modified.shell\",\"patterns\":[{\"match\":\"(?<!\\\\w)-\\\\w+\\\\b\",\"name\":\"string.unquoted.argument.shell constant.other.option.shell\"},{\"include\":\"#array_value\"},{\"captures\":{\"1\":{\"name\":\"variable.other.assignment.shell\"},\"2\":{\"name\":\"punctuation.definition.array.access.shell\"},\"3\":{\"name\":\"variable.other.assignment.shell\"},\"4\":{\"name\":\"constant.numeric.shell constant.numeric.integer.shell\"},\"5\":{\"name\":\"punctuation.definition.array.access.shell\"},\"6\":{\"name\":\"keyword.operator.assignment.shell\"},\"7\":{\"name\":\"keyword.operator.assignment.compound.shell\"},\"8\":{\"name\":\"keyword.operator.assignment.compound.shell\"},\"9\":{\"name\":\"constant.numeric.shell constant.numeric.hex.shell\"},\"10\":{\"name\":\"constant.numeric.shell constant.numeric.octal.shell\"},\"11\":{\"name\":\"constant.numeric.shell constant.numeric.other.shell\"},\"12\":{\"name\":\"constant.numeric.shell constant.numeric.decimal.shell\"},\"13\":{\"name\":\"constant.numeric.shell constant.numeric.version.shell\"},\"14\":{\"name\":\"constant.numeric.shell constant.numeric.integer.shell\"}},\"match\":\"((?<!\\\\w)[-0-9A-Z_a-z]+(?!\\\\w))(?:(\\\\[)((?:(?:\\\\$?(?<!\\\\w)[-0-9A-Z_a-z]+(?!\\\\w)|@)|\\\\*)|(-?\\\\d+))(]))?(?:(?:(=)|(\\\\+=))|(-=))?(?:(?<=[\\\\t =]|^|[(\\\\[{])(?:(?:(?:(?:(?:(0[Xx]\\\\h+)|(0\\\\d+))|(\\\\d{1,2}#[0-9@-Z_a-z]+))|(-?\\\\d+\\\\.\\\\d+))|(-?\\\\d+(?:\\\\.\\\\d+)+))|(-?\\\\d+))(?=[\\\\t ]|$|[);}]))?\"},{\"include\":\"#normal_context\"}]},\"modifiers\":{\"match\":\"(?<=^|[\\\\t \\\\&;])(?:readonly|declare|typeset|export|local)(?=[\\\\t \\\\&;]|$)\",\"name\":\"storage.modifier.$0.shell\"},\"normal_assignment_statement\":{\"begin\":\"[\\\\t ]*+((?<!\\\\w)[-0-9A-Z_a-z]+(?!\\\\w))(?:(\\\\[)((?:(?:\\\\$?(?<!\\\\w)[-0-9A-Z_a-z]+(?!\\\\w)|@)|\\\\*)|(-?\\\\d+))(]))?(?:(?:(=)|(\\\\+=))|(-=))\",\"beginCaptures\":{\"1\":{\"name\":\"variable.other.assignment.shell\"},\"2\":{\"name\":\"punctuation.definition.array.access.shell\"},\"3\":{\"name\":\"variable.other.assignment.shell\"},\"4\":{\"name\":\"constant.numeric.shell constant.numeric.integer.shell\"},\"5\":{\"name\":\"punctuation.definition.array.access.shell\"},\"6\":{\"name\":\"keyword.operator.assignment.shell\"},\"7\":{\"name\":\"keyword.operator.assignment.compound.shell\"},\"8\":{\"name\":\"keyword.operator.assignment.compound.shell\"}},\"end\":\"(?=[\\\\n\\\\&);`{|}]|[\\\\t ]*#|])(?<!\\\\\\\\)\",\"endCaptures\":{},\"name\":\"meta.expression.assignment.shell\",\"patterns\":[{\"include\":\"#comment\"},{\"include\":\"#string\"},{\"include\":\"#normal_assignment_statement\"},{\"begin\":\"(?<=[\\\\t ])(?![\\\\t ]|\\\\w+=)\",\"beginCaptures\":{},\"end\":\"(?=[\\\\n\\\\&);`{|}]|[\\\\t ]*#|])(?<!\\\\\\\\)\",\"endCaptures\":{},\"name\":\"meta.statement.command.env.shell\",\"patterns\":[{\"include\":\"#command_name_range\"},{\"include\":\"#line_continuation\"},{\"include\":\"#option\"},{\"include\":\"#argument\"},{\"include\":\"#string\"}]},{\"include\":\"#simple_unquoted\"},{\"include\":\"#normal_context\"}]},\"normal_context\":{\"patterns\":[{\"include\":\"#comment\"},{\"include\":\"#pipeline\"},{\"include\":\"#normal_statement_seperator\"},{\"include\":\"#misc_ranges\"},{\"include\":\"#boolean\"},{\"include\":\"#redirect_number\"},{\"include\":\"#numeric_literal\"},{\"include\":\"#string\"},{\"include\":\"#variable\"},{\"include\":\"#interpolation\"},{\"include\":\"#heredoc\"},{\"include\":\"#herestring\"},{\"include\":\"#redirection\"},{\"include\":\"#pathname\"},{\"include\":\"#floating_keyword\"},{\"include\":\"#support\"},{\"include\":\"#parenthese\"}]},\"normal_statement\":{\"begin\":\"(?!^[\\\\t ]*+$)(?:(?<=(?:^until| until|\\\\tuntil|^while| while|\\\\twhile|^elif| elif|\\\\telif|^else| else|\\\\telse|^then| then|\\\\tthen|^do| do|\\\\tdo|^if| if|\\\\tif) )|(?<=^|[!\\\\&(;`{|]))[\\\\t ]*+(?!nocorrect\\\\W|nocorrect\\\\$|function\\\\W|function\\\\$|foreach\\\\W|foreach\\\\$|repeat\\\\W|repeat\\\\$|logout\\\\W|logout\\\\$|coproc\\\\W|coproc\\\\$|select\\\\W|select\\\\$|while\\\\W|while\\\\$|pushd\\\\W|pushd\\\\$|until\\\\W|until\\\\$|case\\\\W|case\\\\$|done\\\\W|done\\\\$|elif\\\\W|elif\\\\$|else\\\\W|else\\\\$|esac\\\\W|esac\\\\$|popd\\\\W|popd\\\\$|then\\\\W|then\\\\$|time\\\\W|time\\\\$|for\\\\W|for\\\\$|end\\\\W|end\\\\$|fi\\\\W|fi\\\\$|do\\\\W|do\\\\$|in\\\\W|in\\\\$|if\\\\W|if\\\\$)\",\"beginCaptures\":{},\"end\":\"(?=[\\\\n\\\\&);`{|}]|[\\\\t ]*#|])(?<!\\\\\\\\)\",\"endCaptures\":{},\"name\":\"meta.statement.shell\",\"patterns\":[{\"include\":\"#typical_statements\"}]},\"normal_statement_seperator\":{\"captures\":{\"1\":{\"name\":\"punctuation.terminator.statement.semicolon.shell\"},\"2\":{\"name\":\"punctuation.separator.statement.and.shell\"},\"3\":{\"name\":\"punctuation.separator.statement.or.shell\"},\"4\":{\"name\":\"punctuation.separator.statement.background.shell\"}},\"match\":\"(?:(?:(;)|(&&))|(\\\\|\\\\|))|(&)\"},\"numeric_literal\":{\"captures\":{\"1\":{\"name\":\"constant.numeric.shell constant.numeric.hex.shell\"},\"2\":{\"name\":\"constant.numeric.shell constant.numeric.octal.shell\"},\"3\":{\"name\":\"constant.numeric.shell constant.numeric.other.shell\"},\"4\":{\"name\":\"constant.numeric.shell constant.numeric.decimal.shell\"},\"5\":{\"name\":\"constant.numeric.shell constant.numeric.version.shell\"},\"6\":{\"name\":\"constant.numeric.shell constant.numeric.integer.shell\"}},\"match\":\"(?<=[\\\\t =]|^|[(\\\\[{])(?:(?:(?:(?:(?:(0[Xx]\\\\h+)|(0\\\\d+))|(\\\\d{1,2}#[0-9@-Z_a-z]+))|(-?\\\\d+\\\\.\\\\d+))|(-?\\\\d+(?:\\\\.\\\\d+)+))|(-?\\\\d+))(?=[\\\\t ]|$|[);}])\"},\"option\":{\"begin\":\"[\\\\t ]++(-)((?![\\\\n!#\\\\&()<>\\\\[{|]|$|[\\\\t ;]))\",\"beginCaptures\":{\"1\":{\"name\":\"string.unquoted.argument.shell constant.other.option.dash.shell\"},\"2\":{\"name\":\"string.unquoted.argument.shell constant.other.option.shell\"}},\"contentName\":\"string.unquoted.argument constant.other.option\",\"end\":\"(?=[\\\\t ])|(?=[\\\\n\\\\&);`{|}]|[\\\\t ]*#|])(?<!\\\\\\\\)\",\"endCaptures\":{},\"patterns\":[{\"include\":\"#option_context\"}]},\"option_context\":{\"patterns\":[{\"include\":\"#misc_ranges\"},{\"include\":\"#string\"},{\"include\":\"#variable\"},{\"include\":\"#interpolation\"},{\"include\":\"#heredoc\"},{\"include\":\"#herestring\"},{\"include\":\"#redirection\"},{\"include\":\"#pathname\"},{\"include\":\"#floating_keyword\"},{\"include\":\"#support\"}]},\"parenthese\":{\"patterns\":[{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.parenthese.shell\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.parenthese.shell\"}},\"name\":\"meta.parenthese.group.shell\",\"patterns\":[{\"include\":\"#initial_context\"}]}]},\"pathname\":{\"patterns\":[{\"match\":\"(?<=[:=\\\\s]|^)~\",\"name\":\"keyword.operator.tilde.shell\"},{\"match\":\"[*?]\",\"name\":\"keyword.operator.glob.shell\"},{\"begin\":\"([!*+?@])(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.operator.extglob.shell\"},\"2\":{\"name\":\"punctuation.definition.extglob.shell\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.extglob.shell\"}},\"name\":\"meta.structure.extglob.shell\",\"patterns\":[{\"include\":\"#initial_context\"}]}]},\"pipeline\":{\"patterns\":[{\"match\":\"(?<=^|[\\\\&;\\\\s])(time)(?=[\\\\&;\\\\s]|$)\",\"name\":\"keyword.other.shell\"},{\"match\":\"[!|]\",\"name\":\"keyword.operator.pipe.shell\"}]},\"redirect_fix\":{\"captures\":{\"1\":{\"name\":\"keyword.operator.redirect.shell\"},\"2\":{\"name\":\"string.unquoted.argument.shell\"}},\"match\":\"(>>?)[\\\\t ]*+([^\\\\t\\\\n \\\"$\\\\&-);<>\\\\\\\\`|]+)\"},\"redirect_number\":{\"captures\":{\"1\":{\"name\":\"keyword.operator.redirect.stdout.shell\"},\"2\":{\"name\":\"keyword.operator.redirect.stderr.shell\"},\"3\":{\"name\":\"keyword.operator.redirect.$3.shell\"}},\"match\":\"(?<=[\\\\t ])(?:(1)|(2)|(\\\\d+))(?=>)\"},\"redirection\":{\"patterns\":[{\"begin\":\"[<>]\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.shell\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.shell\"}},\"name\":\"string.interpolated.process-substitution.shell\",\"patterns\":[{\"include\":\"#initial_context\"}]},{\"match\":\"(?<![<>])(&>|\\\\d*>&\\\\d*|\\\\d*(>>|[<>])|\\\\d*<&|\\\\d*<>)(?![<>])\",\"name\":\"keyword.operator.redirect.shell\"}]},\"regex_comparison\":{\"match\":\"=~\",\"name\":\"keyword.operator.logical.regex.shell\"},\"regexp\":{\"patterns\":[{\"match\":\".+\"}]},\"simple_options\":{\"captures\":{\"0\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"string.unquoted.argument.shell constant.other.option.dash.shell\"},\"2\":{\"name\":\"string.unquoted.argument.shell constant.other.option.shell\"}},\"match\":\"[\\\\t ]++(-)(\\\\w+)\"}]}},\"match\":\"(?:[\\\\t ]++-\\\\w+)*\"},\"simple_unquoted\":{\"match\":\"[^\\\\t\\\\n \\\"$\\\\&-);<>\\\\\\\\`|]\",\"name\":\"string.unquoted.shell\"},\"special_expansion\":{\"match\":\"!|:[-=?]?|[*@]|##?|%%|[%/]\",\"name\":\"keyword.operator.expansion.shell\"},\"start_of_command\":{\"match\":\"[\\\\t ]*+(?![\\\\n!#\\\\&()<>\\\\[{|]|$|[\\\\t ;])(?!nocorrect |nocorrect\\\\t|nocorrect$|readonly |readonly\\\\t|readonly$|function |function\\\\t|function$|foreach |foreach\\\\t|foreach$|coproc |coproc\\\\t|coproc$|logout |logout\\\\t|logout$|export |export\\\\t|export$|select |select\\\\t|select$|repeat |repeat\\\\t|repeat$|pushd |pushd\\\\t|pushd$|until |until\\\\t|until$|while |while\\\\t|while$|local |local\\\\t|local$|case |case\\\\t|case$|done |done\\\\t|done$|elif |elif\\\\t|elif$|else |else\\\\t|else$|esac |esac\\\\t|esac$|popd |popd\\\\t|popd$|then |then\\\\t|then$|time |time\\\\t|time$|for |for\\\\t|for$|end |end\\\\t|end$|fi |fi\\\\t|fi$|do |do\\\\t|do$|in |in\\\\t|in$|if |if\\\\t|if$)(?!\\\\\\\\\\\\n?$)\"},\"string\":{\"patterns\":[{\"match\":\"\\\\\\\\.\",\"name\":\"constant.character.escape.shell\"},{\"begin\":\"'\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.shell\"}},\"end\":\"'\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.shell\"}},\"name\":\"string.quoted.single.shell\"},{\"begin\":\"\\\\$?\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.shell\"}},\"end\":\"\\\"\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.shell\"}},\"name\":\"string.quoted.double.shell\",\"patterns\":[{\"match\":\"\\\\\\\\[\\\\n\\\"$\\\\\\\\`]\",\"name\":\"constant.character.escape.shell\"},{\"include\":\"#variable\"},{\"include\":\"#interpolation\"}]},{\"begin\":\"\\\\$'\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.shell\"}},\"end\":\"'\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.shell\"}},\"name\":\"string.quoted.single.dollar.shell\",\"patterns\":[{\"match\":\"\\\\\\\\['\\\\\\\\abefnrtv]\",\"name\":\"constant.character.escape.ansi-c.shell\"},{\"match\":\"\\\\\\\\[0-9]{3}\\\"\",\"name\":\"constant.character.escape.octal.shell\"},{\"match\":\"\\\\\\\\x\\\\h{2}\\\"\",\"name\":\"constant.character.escape.hex.shell\"},{\"match\":\"\\\\\\\\c.\\\"\",\"name\":\"constant.character.escape.control-char.shell\"}]}]},\"subshell_dollar\":{\"patterns\":[{\"begin\":\"\\\\$\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.subshell.single.shell\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.subshell.single.shell\"}},\"name\":\"meta.scope.subshell\",\"patterns\":[{\"include\":\"#parenthese\"},{\"include\":\"#initial_context\"}]}]},\"support\":{\"patterns\":[{\"match\":\"(?<=^|[\\\\&;\\\\s])[.:](?=[\\\\&;\\\\s]|$)\",\"name\":\"support.function.builtin.shell\"}]},\"typical_statements\":{\"patterns\":[{\"include\":\"#assignment_statement\"},{\"include\":\"#case_statement\"},{\"include\":\"#for_statement\"},{\"include\":\"#while_statement\"},{\"include\":\"#function_definition\"},{\"include\":\"#command_statement\"},{\"include\":\"#line_continuation\"},{\"include\":\"#arithmetic_double\"},{\"include\":\"#normal_context\"}]},\"variable\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"punctuation.definition.variable.shell variable.parameter.positional.all.shell\"},\"2\":{\"name\":\"variable.parameter.positional.all.shell\"}},\"match\":\"(\\\\$)(@(?!\\\\w))\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.variable.shell variable.parameter.positional.shell\"},\"2\":{\"name\":\"variable.parameter.positional.shell\"}},\"match\":\"(\\\\$)([0-9](?!\\\\w))\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.variable.shell variable.language.special.shell\"},\"2\":{\"name\":\"variable.language.special.shell\"}},\"match\":\"(\\\\$)([-!#$*0?_](?!\\\\w))\"},{\"begin\":\"(\\\\$)(\\\\{)[\\\\t ]*+(?=\\\\d)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.variable.shell variable.parameter.positional.shell\"},\"2\":{\"name\":\"punctuation.section.bracket.curly.variable.begin.shell punctuation.definition.variable.shell variable.parameter.positional.shell\"}},\"contentName\":\"meta.parameter-expansion\",\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.bracket.curly.variable.end.shell punctuation.definition.variable.shell variable.parameter.positional.shell\"}},\"patterns\":[{\"include\":\"#special_expansion\"},{\"include\":\"#array_access_inline\"},{\"match\":\"[0-9]+\",\"name\":\"variable.parameter.positional.shell\"},{\"match\":\"(?<!\\\\w)[-0-9A-Z_a-z]+(?!\\\\w)\",\"name\":\"variable.other.normal.shell\"},{\"include\":\"#variable\"},{\"include\":\"#string\"}]},{\"begin\":\"(\\\\$)(\\\\{)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.variable.shell\"},\"2\":{\"name\":\"punctuation.section.bracket.curly.variable.begin.shell punctuation.definition.variable.shell\"}},\"contentName\":\"meta.parameter-expansion\",\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.bracket.curly.variable.end.shell punctuation.definition.variable.shell\"}},\"patterns\":[{\"include\":\"#special_expansion\"},{\"include\":\"#array_access_inline\"},{\"match\":\"(?<!\\\\w)[-0-9A-Z_a-z]+(?!\\\\w)\",\"name\":\"variable.other.normal.shell\"},{\"include\":\"#variable\"},{\"include\":\"#string\"}]},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.variable.shell variable.other.normal.shell\"},\"2\":{\"name\":\"variable.other.normal.shell\"}},\"match\":\"(\\\\$)(\\\\w+(?!\\\\w))\"}]},\"while_statement\":{\"patterns\":[{\"begin\":\"\\\\b(while)\\\\b\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.while.shell\"}},\"end\":\"(?=[\\\\n\\\\&);`{|}]|[\\\\t ]*#|])(?<!\\\\\\\\)\",\"endCaptures\":{},\"name\":\"meta.while.shell\",\"patterns\":[{\"include\":\"#line_continuation\"},{\"include\":\"#math_operators\"},{\"include\":\"#option\"},{\"include\":\"#simple_unquoted\"},{\"include\":\"#normal_context\"},{\"include\":\"#string\"}]}]}},\"scopeName\":\"source.shell\",\"aliases\":[\"bash\",\"sh\",\"shell\",\"zsh\"]}"));
const __TURBOPACK__default__export__ = [
    lang
];
}}),
"[project]/node_modules/@shikijs/langs/dist/lua.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$c$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/c.mjs [app-client] (ecmascript)");
;
const lang = Object.freeze(JSON.parse("{\"displayName\":\"Lua\",\"name\":\"lua\",\"patterns\":[{\"begin\":\"\\\\b(?:(local)\\\\s+)?(function)\\\\b(?![,:])\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.local.lua\"},\"2\":{\"name\":\"keyword.control.lua\"}},\"end\":\"(?<=[-\\\\]\\\"')\\\\[{}])\",\"name\":\"meta.function.lua\",\"patterns\":[{\"include\":\"#comment\"},{\"begin\":\"(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.parameters.begin.lua\"}},\"end\":\"(\\\\))|(?=[-\\\\]\\\"'.\\\\[{}])\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.definition.parameters.finish.lua\"}},\"name\":\"meta.parameter.lua\",\"patterns\":[{\"include\":\"#comment\"},{\"match\":\"[A-Z_a-z][0-9A-Z_a-z]*\",\"name\":\"variable.parameter.function.lua\"},{\"match\":\",\",\"name\":\"punctuation.separator.arguments.lua\"},{\"begin\":\":\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.separator.arguments.lua\"}},\"end\":\"(?=[),])\",\"patterns\":[{\"include\":\"#emmydoc.type\"}]}]},{\"match\":\"\\\\b([A-Z_a-z][0-9A-Z_a-z]*)\\\\b\\\\s*(?=:)\",\"name\":\"entity.name.class.lua\"},{\"match\":\"\\\\b([A-Z_a-z][0-9A-Z_a-z]*)\\\\b\",\"name\":\"entity.name.function.lua\"}]},{\"match\":\"(?<![.\\\\w\\\\d])0[Xx]\\\\h+(\\\\.\\\\h*)?([Ee]-?\\\\d*)?([Pp][-+]\\\\d+)?\",\"name\":\"constant.numeric.float.hexadecimal.lua\"},{\"match\":\"(?<![.\\\\w\\\\d])0[Xx]\\\\.\\\\h+([Ee]-?\\\\d*)?([Pp][-+]\\\\d+)?\",\"name\":\"constant.numeric.float.hexadecimal.lua\"},{\"match\":\"(?<![.\\\\w\\\\d])0[Xx]\\\\h+(?![.0-9EPep])\",\"name\":\"constant.numeric.integer.hexadecimal.lua\"},{\"match\":\"(?<![.\\\\w\\\\d])\\\\d+(\\\\.\\\\d*)?([Ee]-?\\\\d*)?\",\"name\":\"constant.numeric.float.lua\"},{\"match\":\"(?<![.\\\\w\\\\d])\\\\.\\\\d+([Ee]-?\\\\d*)?\",\"name\":\"constant.numeric.float.lua\"},{\"match\":\"(?<![.\\\\w\\\\d])\\\\d+(?![.0-9EPep])\",\"name\":\"constant.numeric.integer.lua\"},{\"include\":\"#string\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.comment.lua\"}},\"match\":\"\\\\A(#!).*$\\\\n?\",\"name\":\"comment.line.shebang.lua\"},{\"include\":\"#comment\"},{\"captures\":{\"1\":{\"name\":\"keyword.control.goto.lua\"},\"2\":{\"name\":\"string.tag.lua\"}},\"match\":\"\\\\b(goto)\\\\s+([A-Z_a-z][0-9A-Z_a-z]*)\"},{\"captures\":{\"1\":{\"name\":\"punctuation.section.embedded.begin.lua\"},\"2\":{\"name\":\"punctuation.section.embedded.end.lua\"}},\"match\":\"(::)\\\\s*[A-Z_a-z][0-9A-Z_a-z]*\\\\s*(::)\",\"name\":\"string.tag.lua\"},{\"captures\":{\"0\":{\"name\":\"storage.type.attribute.lua\"}},\"match\":\"<\\\\s*(c(?:onst|lose))\\\\s*>\"},{\"match\":\"<[*A-Z_a-z][-*.0-9A-Z_a-z]*>\",\"name\":\"storage.type.generic.lua\"},{\"match\":\"\\\\b(break|do|else|for|if|elseif|goto|return|then|repeat|while|until|end|in)\\\\b\",\"name\":\"keyword.control.lua\"},{\"match\":\"\\\\b(local)\\\\b\",\"name\":\"keyword.local.lua\"},{\"match\":\"\\\\b(function)\\\\b(?![,:])\",\"name\":\"keyword.control.lua\"},{\"match\":\"(?<![^.]\\\\.|:)\\\\b(false|nil(?!:)|true|_ENV|_G|_VERSION|math\\\\.(pi|huge|maxinteger|mininteger)|utf8\\\\.charpattern|io\\\\.(std(?:in|out|err))|package\\\\.(config|cpath|loaded|loaders|path|preload|searchers))\\\\b|(?<!\\\\.)\\\\.{3}(?!\\\\.)\",\"name\":\"constant.language.lua\"},{\"match\":\"(?<![^.]\\\\.|:)\\\\b(self)\\\\b\",\"name\":\"variable.language.self.lua\"},{\"match\":\"(?<![^.]\\\\.|:)\\\\b(assert|collectgarbage|dofile|error|getfenv|getmetatable|ipairs|load|loadfile|loadstring|module|next|pairs|pcall|print|rawequal|rawget|rawlen|rawset|require|select|setfenv|setmetatable|tonumber|tostring|type|unpack|xpcall)\\\\b(?!\\\\s*=(?!=))\",\"name\":\"support.function.lua\"},{\"match\":\"(?<![^.]\\\\.|:)\\\\b(async)\\\\b(?!\\\\s*=(?!=))\",\"name\":\"entity.name.tag.lua\"},{\"match\":\"(?<![^.]\\\\.|:)\\\\b(coroutine\\\\.(create|isyieldable|close|resume|running|status|wrap|yield)|string\\\\.(byte|char|dump|find|format|gmatch|gsub|len|lower|match|pack|packsize|rep|reverse|sub|unpack|upper)|table\\\\.(concat|insert|maxn|move|pack|remove|sort|unpack)|math\\\\.(abs|acos|asin|atan2?|ceil|cosh?|deg|exp|floor|fmod|frexp|ldexp|log|log10|max|min|modf|pow|rad|random|randomseed|sinh?|sqrt|tanh?|tointeger|type)|io\\\\.(close|flush|input|lines|open|output|popen|read|tmpfile|type|write)|os\\\\.(clock|date|difftime|execute|exit|getenv|remove|rename|setlocale|time|tmpname)|package\\\\.(loadlib|seeall|searchpath)|debug\\\\.(debug|[gs]etfenv|[gs]ethook|getinfo|[gs]etlocal|[gs]etmetatable|getregistry|[gs]etupvalue|[gs]etuservalue|set[Cc]stacklimit|traceback|upvalueid|upvaluejoin)|bit32\\\\.(arshift|band|bnot|bor|btest|bxor|extract|replace|lrotate|lshift|rrotate|rshift)|utf8\\\\.(char|codes|codepoint|len|offset))\\\\b(?!\\\\s*=(?!=))\",\"name\":\"support.function.library.lua\"},{\"match\":\"\\\\b(and|or|not|\\\\|\\\\||&&|!)\\\\b\",\"name\":\"keyword.operator.lua\"},{\"match\":\"\\\\b([A-Z_a-z][0-9A-Z_a-z]*)\\\\b(?=\\\\s*(?:[\\\"'({]|\\\\[\\\\[))\",\"name\":\"support.function.any-method.lua\"},{\"match\":\"\\\\b([A-Z_a-z][0-9A-Z_a-z]*)\\\\b(?=\\\\s*\\\\??:)\",\"name\":\"entity.name.class.lua\"},{\"match\":\"(?<=[^.]\\\\.|:)\\\\b([A-Z_a-z][0-9A-Z_a-z]*)\\\\b(?!\\\\s*=\\\\s*\\\\b(function)\\\\b)\",\"name\":\"entity.other.attribute.lua\"},{\"match\":\"\\\\b([A-Z_a-z][0-9A-Z_a-z]*)\\\\b(?!\\\\s*=\\\\s*\\\\b(function)\\\\b)\",\"name\":\"variable.other.lua\"},{\"match\":\"\\\\b([A-Z_a-z][0-9A-Z_a-z]*)\\\\b(?=\\\\s*=\\\\s*\\\\b(function)\\\\b)\",\"name\":\"entity.name.function.lua\"},{\"match\":\"[-#%*+/^]|==?|~=|!=|<=?|>=?|(?<!\\\\.)\\\\.{2}(?!\\\\.)\",\"name\":\"keyword.operator.lua\"}],\"repository\":{\"comment\":{\"patterns\":[{\"begin\":\"(^[\\\\t ]+)?(?=--)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.whitespace.comment.leading.lua\"}},\"end\":\"(?!\\\\G)((?!^)[\\\\t ]+\\\\n)?\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.whitespace.comment.trailing.lua\"}},\"patterns\":[{\"begin\":\"--\\\\[(=*)\\\\[@@@\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.begin.lua\"}},\"end\":\"(--)?]\\\\1]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.end.lua\"}},\"name\":\"\",\"patterns\":[{\"include\":\"source.lua\"}]},{\"begin\":\"--\\\\[(=*)\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.begin.lua\"}},\"end\":\"(--)?]\\\\1]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.end.lua\"}},\"name\":\"comment.block.lua\",\"patterns\":[{\"include\":\"#emmydoc\"},{\"include\":\"#ldoc_tag\"}]},{\"begin\":\"----\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.lua\"}},\"end\":\"\\\\n\",\"name\":\"comment.line.double-dash.lua\"},{\"begin\":\"---\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.lua\"}},\"end\":\"\\\\n\",\"name\":\"comment.line.double-dash.documentation.lua\",\"patterns\":[{\"include\":\"#emmydoc\"},{\"include\":\"#ldoc_tag\"}]},{\"begin\":\"--\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.lua\"}},\"end\":\"\\\\n\",\"name\":\"comment.line.double-dash.lua\",\"patterns\":[{\"include\":\"#ldoc_tag\"}]}]},{\"begin\":\"/\\\\*\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.begin.lua\"}},\"end\":\"\\\\*/\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.end.lua\"}},\"name\":\"comment.block.lua\",\"patterns\":[{\"include\":\"#emmydoc\"},{\"include\":\"#ldoc_tag\"}]}]},\"emmydoc\":{\"patterns\":[{\"begin\":\"(?<=---)[\\\\t ]*@class\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.annotation.lua\"}},\"end\":\"(?=[\\\\n#@])\",\"patterns\":[{\"match\":\"\\\\b([*A-Z_a-z][-*.0-9A-Z_a-z]*)\",\"name\":\"support.class.lua\"},{\"match\":\"[,:]\",\"name\":\"keyword.operator.lua\"}]},{\"begin\":\"(?<=---)[\\\\t ]*@enum\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.annotation.lua\"}},\"end\":\"(?=[\\\\n#@])\",\"patterns\":[{\"begin\":\"\\\\b([*A-Z_a-z][-*.0-9A-Z_a-z]*)\",\"beginCaptures\":{\"0\":{\"name\":\"variable.lua\"}},\"end\":\"(?=\\\\n)\"}]},{\"begin\":\"(?<=---)[\\\\t ]*@type\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.annotation.lua\"}},\"end\":\"(?=[\\\\n#@])\",\"patterns\":[{\"include\":\"#emmydoc.type\"}]},{\"begin\":\"(?<=---)[\\\\t ]*@alias\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.annotation.lua\"}},\"end\":\"(?=[\\\\n#@])\",\"patterns\":[{\"begin\":\"\\\\b([*A-Z_a-z][-*.0-9A-Z_a-z]*)\",\"beginCaptures\":{\"0\":{\"name\":\"variable.lua\"}},\"end\":\"(?=[\\\\n#])\",\"patterns\":[{\"include\":\"#emmydoc.type\"}]}]},{\"begin\":\"(?<=---)[\\\\t ]*(@operator)\\\\s*(\\\\b[a-z]+)?\",\"beginCaptures\":{\"1\":{\"name\":\"storage.type.annotation.lua\"},\"2\":{\"name\":\"support.function.library.lua\"}},\"end\":\"(?=[\\\\n#@])\",\"patterns\":[{\"include\":\"#emmydoc.type\"}]},{\"begin\":\"(?<=---)[\\\\t ]*@cast\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.annotation.lua\"}},\"end\":\"(?=[\\\\n#@])\",\"patterns\":[{\"begin\":\"\\\\b([*A-Z_a-z][-*.0-9A-Z_a-z]*)\",\"beginCaptures\":{\"0\":{\"name\":\"variable.other.lua\"}},\"end\":\"(?=\\\\n)\",\"patterns\":[{\"include\":\"#emmydoc.type\"},{\"match\":\"([+-|])\",\"name\":\"keyword.operator.lua\"}]}]},{\"begin\":\"(?<=---)[\\\\t ]*@param\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.annotation.lua\"}},\"end\":\"(?=[\\\\n#@])\",\"patterns\":[{\"begin\":\"\\\\b([A-Z_a-z][0-9A-Z_a-z]*)\\\\b(\\\\??)\",\"beginCaptures\":{\"1\":{\"name\":\"entity.name.variable.lua\"},\"2\":{\"name\":\"keyword.operator.lua\"}},\"end\":\"(?=[\\\\n#])\",\"patterns\":[{\"include\":\"#emmydoc.type\"}]}]},{\"begin\":\"(?<=---)[\\\\t ]*@return\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.annotation.lua\"}},\"end\":\"(?=[\\\\n#@])\",\"patterns\":[{\"match\":\"\\\\?\",\"name\":\"keyword.operator.lua\"},{\"include\":\"#emmydoc.type\"}]},{\"begin\":\"(?<=---)[\\\\t ]*@field\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.annotation.lua\"}},\"end\":\"(?=[\\\\n#@])\",\"patterns\":[{\"begin\":\"(\\\\b([A-Z_a-z][0-9A-Z_a-z]*)\\\\b|(\\\\[))(\\\\??)\",\"beginCaptures\":{\"2\":{\"name\":\"entity.name.variable.lua\"},\"3\":{\"name\":\"keyword.operator.lua\"}},\"end\":\"(?=[\\\\n#])\",\"patterns\":[{\"include\":\"#string\"},{\"include\":\"#emmydoc.type\"},{\"match\":\"]\",\"name\":\"keyword.operator.lua\"}]}]},{\"begin\":\"(?<=---)[\\\\t ]*@generic\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.annotation.lua\"}},\"end\":\"(?=[\\\\n#@])\",\"patterns\":[{\"begin\":\"\\\\b([A-Z_a-z][0-9A-Z_a-z]*)\\\\b\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.generic.lua\"}},\"end\":\"(?=\\\\n)|(,)\",\"endCaptures\":{\"0\":{\"name\":\"keyword.operator.lua\"}},\"patterns\":[{\"match\":\":\",\"name\":\"keyword.operator.lua\"},{\"include\":\"#emmydoc.type\"}]}]},{\"begin\":\"(?<=---)[\\\\t ]*@vararg\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.annotation.lua\"}},\"end\":\"(?=[\\\\n#@])\",\"patterns\":[{\"include\":\"#emmydoc.type\"}]},{\"begin\":\"(?<=---)[\\\\t ]*@overload\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.annotation.lua\"}},\"end\":\"(?=[\\\\n#@])\",\"patterns\":[{\"include\":\"#emmydoc.type\"}]},{\"begin\":\"(?<=---)[\\\\t ]*@deprecated\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.annotation.lua\"}},\"end\":\"(?=[\\\\n#@])\"},{\"begin\":\"(?<=---)[\\\\t ]*@meta\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.annotation.lua\"}},\"end\":\"(?=[\\\\n#@])\"},{\"begin\":\"(?<=---)[\\\\t ]*@private\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.annotation.lua\"}},\"end\":\"(?=[\\\\n#@])\"},{\"begin\":\"(?<=---)[\\\\t ]*@protected\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.annotation.lua\"}},\"end\":\"(?=[\\\\n#@])\"},{\"begin\":\"(?<=---)[\\\\t ]*@package\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.annotation.lua\"}},\"end\":\"(?=[\\\\n#@])\"},{\"begin\":\"(?<=---)[\\\\t ]*@version\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.annotation.lua\"}},\"end\":\"(?=[\\\\n#@])\",\"patterns\":[{\"match\":\"\\\\b(5\\\\.1|5\\\\.2|5\\\\.3|5\\\\.4|JIT)\\\\b\",\"name\":\"support.class.lua\"},{\"match\":\"[,<>]\",\"name\":\"keyword.operator.lua\"}]},{\"begin\":\"(?<=---)[\\\\t ]*@see\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.annotation.lua\"}},\"end\":\"(?=[\\\\n#@])\",\"patterns\":[{\"match\":\"\\\\b([*A-Z_a-z][-*.0-9A-Z_a-z]*)\",\"name\":\"support.class.lua\"},{\"match\":\"#\",\"name\":\"keyword.operator.lua\"}]},{\"begin\":\"(?<=---)[\\\\t ]*@diagnostic\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.annotation.lua\"}},\"end\":\"(?=[\\\\n#@])\",\"patterns\":[{\"begin\":\"([-0-9A-Z_a-z]+)[\\\\t ]*(:)?\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.other.unit\"},\"2\":{\"name\":\"keyword.operator.unit\"}},\"end\":\"(?=\\\\n)\",\"patterns\":[{\"match\":\"\\\\b([*A-Z_a-z][-0-9A-Z_a-z]*)\",\"name\":\"support.class.lua\"},{\"match\":\",\",\"name\":\"keyword.operator.lua\"}]}]},{\"begin\":\"(?<=---)[\\\\t ]*@module\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.annotation.lua\"}},\"end\":\"(?=[\\\\n#@])\",\"patterns\":[{\"include\":\"#string\"}]},{\"match\":\"(?<=---)[\\\\t ]*@(async|nodiscard)\",\"name\":\"storage.type.annotation.lua\"},{\"begin\":\"(?<=---)\\\\|\\\\s*[+>]?\",\"beginCaptures\":{\"0\":{\"name\":\"storage.type.annotation.lua\"}},\"end\":\"(?=[\\\\n#@])\",\"patterns\":[{\"include\":\"#string\"}]}]},\"emmydoc.type\":{\"patterns\":[{\"begin\":\"\\\\bfun\\\\b\",\"beginCaptures\":{\"0\":{\"name\":\"keyword.control.lua\"}},\"end\":\"(?=[#\\\\s])\",\"patterns\":[{\"match\":\"[(),:?][\\\\t ]*\",\"name\":\"keyword.operator.lua\"},{\"match\":\"([A-Z_a-z][-\\\\]*,.0-9<>A-\\\\[_a-z]*)(?<!,)[\\\\t ]*(?=\\\\??:)\",\"name\":\"entity.name.variable.lua\"},{\"include\":\"#emmydoc.type\"},{\"include\":\"#string\"}]},{\"match\":\"<[*A-Z_a-z][-*.0-9A-Z_a-z]*>\",\"name\":\"storage.type.generic.lua\"},{\"match\":\"\\\\basync\\\\b\",\"name\":\"entity.name.tag.lua\"},{\"match\":\"[,:?`{|}][\\\\t ]*\",\"name\":\"keyword.operator.lua\"},{\"begin\":\"(?=[\\\"'*.A-\\\\[_a-z])\",\"end\":\"(?=[#),:?|}\\\\s])\",\"patterns\":[{\"match\":\"([-\\\\]*,.0-9<>A-\\\\[_a-z]+)(?<!,)[\\\\t ]*\",\"name\":\"support.type.lua\"},{\"match\":\"(\\\\.\\\\.\\\\.)[\\\\t ]*\",\"name\":\"constant.language.lua\"},{\"include\":\"#string\"}]}]},\"escaped_char\":{\"patterns\":[{\"match\":\"\\\\\\\\[\\\\n\\\"'\\\\\\\\abfnrtv]\",\"name\":\"constant.character.escape.lua\"},{\"match\":\"\\\\\\\\z[\\\\t\\\\n ]*\",\"name\":\"constant.character.escape.lua\"},{\"match\":\"\\\\\\\\\\\\d{1,3}\",\"name\":\"constant.character.escape.byte.lua\"},{\"match\":\"\\\\\\\\x\\\\h\\\\h\",\"name\":\"constant.character.escape.byte.lua\"},{\"match\":\"\\\\\\\\u\\\\{\\\\h+}\",\"name\":\"constant.character.escape.unicode.lua\"},{\"match\":\"\\\\\\\\.\",\"name\":\"invalid.illegal.character.escape.lua\"}]},\"ldoc_tag\":{\"captures\":{\"1\":{\"name\":\"punctuation.definition.block.tag.ldoc\"},\"2\":{\"name\":\"storage.type.class.ldoc\"}},\"match\":\"\\\\G[\\\\t ]*(@)(alias|annotation|author|charset|class|classmod|comment|constructor|copyright|description|example|export|factory|field|file|fixme|function|include|lfunction|license|local|module|name|param|pragma|private|raise|release|return|script|section|see|set|static|submodule|summary|tfield|thread|tparam|treturn|todo|topic|type|usage|warning|within)\\\\b\"},\"string\":{\"patterns\":[{\"begin\":\"'\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.lua\"}},\"end\":\"'[\\\\t ]*|(?=\\\\n)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.lua\"}},\"name\":\"string.quoted.single.lua\",\"patterns\":[{\"include\":\"#escaped_char\"}]},{\"begin\":\"\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.lua\"}},\"end\":\"\\\"[\\\\t ]*|(?=\\\\n)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.lua\"}},\"name\":\"string.quoted.double.lua\",\"patterns\":[{\"include\":\"#escaped_char\"}]},{\"begin\":\"`\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.lua\"}},\"end\":\"`[\\\\t ]*|(?=\\\\n)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.lua\"}},\"name\":\"string.quoted.double.lua\"},{\"begin\":\"(?<=\\\\.cdef)\\\\s*(\\\\[(=*)\\\\[)\",\"beginCaptures\":{\"0\":{\"name\":\"string.quoted.other.multiline.lua\"},\"1\":{\"name\":\"punctuation.definition.string.begin.lua\"}},\"contentName\":\"meta.embedded.lua\",\"end\":\"(]\\\\2])[\\\\t ]*\",\"endCaptures\":{\"0\":{\"name\":\"string.quoted.other.multiline.lua\"},\"1\":{\"name\":\"punctuation.definition.string.end.lua\"}},\"patterns\":[{\"include\":\"source.c\"}]},{\"begin\":\"(?<!--)\\\\[(=*)\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.lua\"}},\"end\":\"]\\\\1][\\\\t ]*\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.lua\"}},\"name\":\"string.quoted.other.multiline.lua\"}]}},\"scopeName\":\"source.lua\",\"embeddedLangs\":[\"c\"]}"));
const __TURBOPACK__default__export__ = [
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$c$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    lang
];
}}),
"[project]/node_modules/@shikijs/langs/dist/yaml.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const lang = Object.freeze(JSON.parse("{\"displayName\":\"YAML\",\"fileTypes\":[\"yaml\",\"yml\",\"rviz\",\"reek\",\"clang-format\",\"yaml-tmlanguage\",\"syntax\",\"sublime-syntax\"],\"firstLineMatch\":\"^%YAML( ?1.\\\\d+)?\",\"name\":\"yaml\",\"patterns\":[{\"include\":\"#comment\"},{\"include\":\"#property\"},{\"include\":\"#directive\"},{\"match\":\"^---\",\"name\":\"entity.other.document.begin.yaml\"},{\"match\":\"^\\\\.{3}\",\"name\":\"entity.other.document.end.yaml\"},{\"include\":\"#node\"}],\"repository\":{\"block-collection\":{\"patterns\":[{\"include\":\"#block-sequence\"},{\"include\":\"#block-mapping\"}]},\"block-mapping\":{\"patterns\":[{\"include\":\"#block-pair\"}]},\"block-node\":{\"patterns\":[{\"include\":\"#prototype\"},{\"include\":\"#block-scalar\"},{\"include\":\"#block-collection\"},{\"include\":\"#flow-scalar-plain-out\"},{\"include\":\"#flow-node\"}]},\"block-pair\":{\"patterns\":[{\"begin\":\"\\\\?\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.key-value.begin.yaml\"}},\"end\":\"(?=\\\\?)|^ *(:)|(:)\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.separator.key-value.mapping.yaml\"},\"2\":{\"name\":\"invalid.illegal.expected-newline.yaml\"}},\"name\":\"meta.block-mapping.yaml\",\"patterns\":[{\"include\":\"#block-node\"}]},{\"begin\":\"(?=(?:[^-\\\\]!\\\"#%\\\\&'*,:>?@\\\\[`{|}\\\\s]|[-:?]\\\\S)([^:\\\\s]|:\\\\S|\\\\s+(?![#\\\\s]))*\\\\s*:(\\\\s|$))\",\"end\":\"(?=\\\\s*$|\\\\s+#|\\\\s*:(\\\\s|$))\",\"patterns\":[{\"include\":\"#flow-scalar-plain-out-implicit-type\"},{\"begin\":\"[^-\\\\]!\\\"#%\\\\&'*,:>?@\\\\[`{|}\\\\s]|[-:?]\\\\S\",\"beginCaptures\":{\"0\":{\"name\":\"entity.name.tag.yaml\"}},\"contentName\":\"entity.name.tag.yaml\",\"end\":\"(?=\\\\s*$|\\\\s+#|\\\\s*:(\\\\s|$))\",\"name\":\"string.unquoted.plain.out.yaml\"}]},{\"match\":\":(?=\\\\s|$)\",\"name\":\"punctuation.separator.key-value.mapping.yaml\"}]},\"block-scalar\":{\"begin\":\"(?:(\\\\|)|(>))([1-9])?([-+])?(.*\\\\n?)\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.flow.block-scalar.literal.yaml\"},\"2\":{\"name\":\"keyword.control.flow.block-scalar.folded.yaml\"},\"3\":{\"name\":\"constant.numeric.indentation-indicator.yaml\"},\"4\":{\"name\":\"storage.modifier.chomping-indicator.yaml\"},\"5\":{\"patterns\":[{\"include\":\"#comment\"},{\"match\":\".+\",\"name\":\"invalid.illegal.expected-comment-or-newline.yaml\"}]}},\"end\":\"^(?=\\\\S)|(?!\\\\G)\",\"patterns\":[{\"begin\":\"^( +)(?! )\",\"end\":\"^(?!\\\\1|\\\\s*$)\",\"name\":\"string.unquoted.block.yaml\"}]},\"block-sequence\":{\"match\":\"(-)(?!\\\\S)\",\"name\":\"punctuation.definition.block.sequence.item.yaml\"},\"comment\":{\"begin\":\"(?:^([\\\\t ]*)|[\\\\t ]+)(?=#\\\\p{print}*$)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.whitespace.comment.leading.yaml\"}},\"end\":\"(?!\\\\G)\",\"patterns\":[{\"begin\":\"#\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.yaml\"}},\"end\":\"\\\\n\",\"name\":\"comment.line.number-sign.yaml\"}]},\"directive\":{\"begin\":\"^%\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.directive.begin.yaml\"}},\"end\":\"(?=$|[\\\\t ]+($|#))\",\"name\":\"meta.directive.yaml\",\"patterns\":[{\"captures\":{\"1\":{\"name\":\"keyword.other.directive.yaml.yaml\"},\"2\":{\"name\":\"constant.numeric.yaml-version.yaml\"}},\"match\":\"\\\\G(YAML)[\\\\t ]+(\\\\d+\\\\.\\\\d+)\"},{\"captures\":{\"1\":{\"name\":\"keyword.other.directive.tag.yaml\"},\"2\":{\"name\":\"storage.type.tag-handle.yaml\"},\"3\":{\"name\":\"support.type.tag-prefix.yaml\"}},\"match\":\"\\\\G(TAG)(?:[\\\\t ]+(!(?:[-0-9A-Za-z]*!)?)(?:[\\\\t ]+(!(?:%\\\\h{2}|[]!#$\\\\&-;=?-\\\\[_a-z~])*|(?![]!,\\\\[{}])(?:%\\\\h{2}|[]!#$\\\\&-;=?-\\\\[_a-z~])+))?)?\"},{\"captures\":{\"1\":{\"name\":\"support.other.directive.reserved.yaml\"},\"2\":{\"name\":\"string.unquoted.directive-name.yaml\"},\"3\":{\"name\":\"string.unquoted.directive-parameter.yaml\"}},\"match\":\"\\\\G(\\\\w+)(?:[\\\\t ]+(\\\\w+)(?:[\\\\t ]+(\\\\w+))?)?\"},{\"match\":\"\\\\S+\",\"name\":\"invalid.illegal.unrecognized.yaml\"}]},\"flow-alias\":{\"captures\":{\"1\":{\"name\":\"keyword.control.flow.alias.yaml\"},\"2\":{\"name\":\"punctuation.definition.alias.yaml\"},\"3\":{\"name\":\"variable.other.alias.yaml\"},\"4\":{\"name\":\"invalid.illegal.character.anchor.yaml\"}},\"match\":\"((\\\\*))([^],/\\\\[{}\\\\s]+)([^],}\\\\s]\\\\S*)?\"},\"flow-collection\":{\"patterns\":[{\"include\":\"#flow-sequence\"},{\"include\":\"#flow-mapping\"}]},\"flow-mapping\":{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.mapping.begin.yaml\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.mapping.end.yaml\"}},\"name\":\"meta.flow-mapping.yaml\",\"patterns\":[{\"include\":\"#prototype\"},{\"match\":\",\",\"name\":\"punctuation.separator.mapping.yaml\"},{\"include\":\"#flow-pair\"}]},\"flow-node\":{\"patterns\":[{\"include\":\"#prototype\"},{\"include\":\"#flow-alias\"},{\"include\":\"#flow-collection\"},{\"include\":\"#flow-scalar\"}]},\"flow-pair\":{\"patterns\":[{\"begin\":\"\\\\?\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.key-value.begin.yaml\"}},\"end\":\"(?=[],}])\",\"name\":\"meta.flow-pair.explicit.yaml\",\"patterns\":[{\"include\":\"#prototype\"},{\"include\":\"#flow-pair\"},{\"include\":\"#flow-node\"},{\"begin\":\":(?=\\\\s|$|[],\\\\[{}])\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.separator.key-value.mapping.yaml\"}},\"end\":\"(?=[],}])\",\"patterns\":[{\"include\":\"#flow-value\"}]}]},{\"begin\":\"(?=(?:[^-\\\\]!\\\"#%\\\\&'*,:>?@\\\\[`{|}\\\\s]|[-:?][^],\\\\[{}\\\\s])([^],:\\\\[{}\\\\s]|:[^],\\\\[{}\\\\s]|\\\\s+(?![#\\\\s]))*\\\\s*:(\\\\s|$))\",\"end\":\"(?=\\\\s*$|\\\\s+#|\\\\s*:(\\\\s|$)|\\\\s*:[],\\\\[{}]|\\\\s*[],\\\\[{}])\",\"name\":\"meta.flow-pair.key.yaml\",\"patterns\":[{\"include\":\"#flow-scalar-plain-in-implicit-type\"},{\"begin\":\"[^-\\\\]!\\\"#%\\\\&'*,:>?@\\\\[`{|}\\\\s]|[-:?][^],\\\\[{}\\\\s]\",\"beginCaptures\":{\"0\":{\"name\":\"entity.name.tag.yaml\"}},\"contentName\":\"entity.name.tag.yaml\",\"end\":\"(?=\\\\s*$|\\\\s+#|\\\\s*:(\\\\s|$)|\\\\s*:[],\\\\[{}]|\\\\s*[],\\\\[{}])\",\"name\":\"string.unquoted.plain.in.yaml\"}]},{\"include\":\"#flow-node\"},{\"begin\":\":(?=\\\\s|$|[],\\\\[{}])\",\"captures\":{\"0\":{\"name\":\"punctuation.separator.key-value.mapping.yaml\"}},\"end\":\"(?=[],}])\",\"name\":\"meta.flow-pair.yaml\",\"patterns\":[{\"include\":\"#flow-value\"}]}]},\"flow-scalar\":{\"patterns\":[{\"include\":\"#flow-scalar-double-quoted\"},{\"include\":\"#flow-scalar-single-quoted\"},{\"include\":\"#flow-scalar-plain-in\"}]},\"flow-scalar-double-quoted\":{\"begin\":\"\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.yaml\"}},\"end\":\"\\\"\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.yaml\"}},\"name\":\"string.quoted.double.yaml\",\"patterns\":[{\"match\":\"\\\\\\\\([ \\\"/0LN\\\\\\\\_abefnprtv]|x\\\\d\\\\d|u\\\\d{4}|U\\\\d{8})\",\"name\":\"constant.character.escape.yaml\"},{\"match\":\"\\\\\\\\\\\\n\",\"name\":\"constant.character.escape.double-quoted.newline.yaml\"}]},\"flow-scalar-plain-in\":{\"patterns\":[{\"include\":\"#flow-scalar-plain-in-implicit-type\"},{\"begin\":\"[^-\\\\]!\\\"#%\\\\&'*,:>?@\\\\[`{|}\\\\s]|[-:?][^],\\\\[{}\\\\s]\",\"end\":\"(?=\\\\s*$|\\\\s+#|\\\\s*:(\\\\s|$)|\\\\s*:[],\\\\[{}]|\\\\s*[],\\\\[{}])\",\"name\":\"string.unquoted.plain.in.yaml\"}]},\"flow-scalar-plain-in-implicit-type\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"constant.language.null.yaml\"},\"2\":{\"name\":\"constant.language.boolean.yaml\"},\"3\":{\"name\":\"constant.numeric.integer.yaml\"},\"4\":{\"name\":\"constant.numeric.float.yaml\"},\"5\":{\"name\":\"constant.other.timestamp.yaml\"},\"6\":{\"name\":\"constant.language.value.yaml\"},\"7\":{\"name\":\"constant.language.merge.yaml\"}},\"match\":\"(?:(null|Null|NULL|~)|([Yy]|yes|Yes|YES|[Nn]|no|No|NO|true|True|TRUE|false|False|FALSE|on|On|ON|off|Off|OFF)|([-+]?0b[01_]+|[-+]?0[0-7_]+|[-+]?(?:0|[1-9][0-9_]*)|[-+]?0x[_\\\\h]+|[-+]?[1-9][0-9_]*(?::[0-5]?[0-9])+)|([-+]?(?:[0-9][0-9_]*)?\\\\.[.0-9]*(?:[Ee][-+][0-9]+)?|[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\\\\.[0-9_]*|[-+]?\\\\.(?:inf|Inf|INF)|\\\\.(?:nan|NaN|NAN))|(\\\\d{4}-\\\\d{2}-\\\\d{2}|\\\\d{4}-\\\\d{1,2}-\\\\d{1,2}(?:[Tt]|[\\\\t ]+)\\\\d{1,2}:\\\\d{2}:\\\\d{2}(?:\\\\.\\\\d*)?(?:[\\\\t ]*Z|[-+]\\\\d{1,2}(?::\\\\d{1,2})?)?)|(=)|(<<))(?=\\\\s*$|\\\\s+#|\\\\s*:(\\\\s|$)|\\\\s*:[],\\\\[{}]|\\\\s*[],\\\\[{}])\"}]},\"flow-scalar-plain-out\":{\"patterns\":[{\"include\":\"#flow-scalar-plain-out-implicit-type\"},{\"begin\":\"[^-\\\\]!\\\"#%\\\\&'*,:>?@\\\\[`{|}\\\\s]|[-:?]\\\\S\",\"end\":\"(?=\\\\s*$|\\\\s+#|\\\\s*:(\\\\s|$))\",\"name\":\"string.unquoted.plain.out.yaml\"}]},\"flow-scalar-plain-out-implicit-type\":{\"patterns\":[{\"captures\":{\"1\":{\"name\":\"constant.language.null.yaml\"},\"2\":{\"name\":\"constant.language.boolean.yaml\"},\"3\":{\"name\":\"constant.numeric.integer.yaml\"},\"4\":{\"name\":\"constant.numeric.float.yaml\"},\"5\":{\"name\":\"constant.other.timestamp.yaml\"},\"6\":{\"name\":\"constant.language.value.yaml\"},\"7\":{\"name\":\"constant.language.merge.yaml\"}},\"match\":\"(?:(null|Null|NULL|~)|([Yy]|yes|Yes|YES|[Nn]|no|No|NO|true|True|TRUE|false|False|FALSE|on|On|ON|off|Off|OFF)|([-+]?0b[01_]+|[-+]?0[0-7_]+|[-+]?(?:0|[1-9][0-9_]*)|[-+]?0x[_\\\\h]+|[-+]?[1-9][0-9_]*(?::[0-5]?[0-9])+)|([-+]?(?:[0-9][0-9_]*)?\\\\.[.0-9]*(?:[Ee][-+][0-9]+)?|[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\\\\.[0-9_]*|[-+]?\\\\.(?:inf|Inf|INF)|\\\\.(?:nan|NaN|NAN))|(\\\\d{4}-\\\\d{2}-\\\\d{2}|\\\\d{4}-\\\\d{1,2}-\\\\d{1,2}(?:[Tt]|[\\\\t ]+)\\\\d{1,2}:\\\\d{2}:\\\\d{2}(?:\\\\.\\\\d*)?(?:[\\\\t ]*Z|[-+]\\\\d{1,2}(?::\\\\d{1,2})?)?)|(=)|(<<))(?=\\\\s*$|\\\\s+#|\\\\s*:(\\\\s|$))\"}]},\"flow-scalar-single-quoted\":{\"begin\":\"'\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.yaml\"}},\"end\":\"'(?!')\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.yaml\"}},\"name\":\"string.quoted.single.yaml\",\"patterns\":[{\"match\":\"''\",\"name\":\"constant.character.escape.single-quoted.yaml\"}]},\"flow-sequence\":{\"begin\":\"\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.sequence.begin.yaml\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.sequence.end.yaml\"}},\"name\":\"meta.flow-sequence.yaml\",\"patterns\":[{\"include\":\"#prototype\"},{\"match\":\",\",\"name\":\"punctuation.separator.sequence.yaml\"},{\"include\":\"#flow-pair\"},{\"include\":\"#flow-node\"}]},\"flow-value\":{\"patterns\":[{\"begin\":\"\\\\G(?![],}])\",\"end\":\"(?=[],}])\",\"name\":\"meta.flow-pair.value.yaml\",\"patterns\":[{\"include\":\"#flow-node\"}]}]},\"node\":{\"patterns\":[{\"include\":\"#block-node\"}]},\"property\":{\"begin\":\"(?=[!\\\\&])\",\"end\":\"(?!\\\\G)\",\"name\":\"meta.property.yaml\",\"patterns\":[{\"captures\":{\"1\":{\"name\":\"keyword.control.property.anchor.yaml\"},\"2\":{\"name\":\"punctuation.definition.anchor.yaml\"},\"3\":{\"name\":\"entity.name.type.anchor.yaml\"},\"4\":{\"name\":\"invalid.illegal.character.anchor.yaml\"}},\"match\":\"\\\\G((&))([^],/\\\\[{}\\\\s]+)(\\\\S+)?\"},{\"match\":\"\\\\G!(?:<(?:%\\\\h{2}|[]!#$\\\\&-;=?-\\\\[_a-z~])+>|(?:[-0-9A-Za-z]*!)?(?:%\\\\h{2}|[#$\\\\&-+\\\\--;=?-Z_a-z~])+|)(?=[\\\\t ]|$)\",\"name\":\"storage.type.tag-handle.yaml\"},{\"match\":\"\\\\S+\",\"name\":\"invalid.illegal.tag-handle.yaml\"}]},\"prototype\":{\"patterns\":[{\"include\":\"#comment\"},{\"include\":\"#property\"}]}},\"scopeName\":\"source.yaml\",\"aliases\":[\"yml\"]}"));
const __TURBOPACK__default__export__ = [
    lang
];
}}),
"[project]/node_modules/@shikijs/langs/dist/ruby.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$html$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/html.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$haml$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/haml.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$xml$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/xml.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$sql$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/sql.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$graphql$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/graphql.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$css$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/css.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$cpp$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/cpp.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$c$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/c.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$javascript$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/javascript.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$shellscript$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/shellscript.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$lua$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/lua.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$yaml$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@shikijs/langs/dist/yaml.mjs [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
const lang = Object.freeze(JSON.parse("{\"displayName\":\"Ruby\",\"name\":\"ruby\",\"patterns\":[{\"captures\":{\"1\":{\"name\":\"keyword.control.class.ruby\"},\"2\":{\"name\":\"entity.name.type.class.ruby\"},\"5\":{\"name\":\"punctuation.separator.namespace.ruby\"},\"7\":{\"name\":\"punctuation.separator.inheritance.ruby\"},\"8\":{\"name\":\"entity.other.inherited-class.ruby\"},\"11\":{\"name\":\"punctuation.separator.namespace.ruby\"}},\"match\":\"\\\\b(class)\\\\s+(([0-9A-Z_a-z]+)((::)[0-9A-Z_a-z]+)*)\\\\s*((<)\\\\s*(([0-9A-Z_a-z]+)((::)[0-9A-Z_a-z]+)*))?\",\"name\":\"meta.class.ruby\"},{\"captures\":{\"1\":{\"name\":\"keyword.control.module.ruby\"},\"2\":{\"name\":\"entity.name.type.module.ruby\"},\"5\":{\"name\":\"punctuation.separator.namespace.ruby\"}},\"match\":\"\\\\b(module)\\\\s+(([0-9A-Z_a-z]+)((::)[0-9A-Z_a-z]+)*)\",\"name\":\"meta.module.ruby\"},{\"captures\":{\"1\":{\"name\":\"keyword.control.class.ruby\"},\"2\":{\"name\":\"punctuation.separator.inheritance.ruby\"}},\"match\":\"\\\\b(class)\\\\s*(<<)\\\\s*\",\"name\":\"meta.class.ruby\"},{\"match\":\"(?<!\\\\.)\\\\belse(\\\\s)+if\\\\b\",\"name\":\"invalid.deprecated.ruby\"},{\"captures\":{\"1\":{\"name\":\"variable.ruby\"},\"2\":{\"name\":\"keyword.operator.assignment.augmented.ruby\"}},\"match\":\"^\\\\s*([_a-z][0-9A-Z_a-z]*)\\\\s*((&&|\\\\|\\\\|)=)\"},{\"captures\":{\"1\":{\"name\":\"keyword.control.ruby\"},\"3\":{\"name\":\"variable.ruby\"},\"4\":{\"name\":\"keyword.operator.assignment.augmented.ruby\"}},\"match\":\"(?<!\\\\.)\\\\b(case|if|elsif|unless|until|while)\\\\b\\\\s*(\\\\()*?\\\\s*([_a-z][0-9A-Z_a-z]*)\\\\s*((&&|\\\\|\\\\|)=)\"},{\"captures\":{\"1\":{\"name\":\"variable.ruby\"},\"2\":{\"name\":\"keyword.operator.assignment.augmented.ruby\"}},\"match\":\"^\\\\s*([_a-z][0-9A-Z_a-z]*)\\\\s*(([-%*+/]|\\\\*\\\\*|[\\\\&^|]|<<|>>)=)\"},{\"captures\":{\"1\":{\"name\":\"keyword.control.ruby\"},\"3\":{\"name\":\"variable.ruby\"},\"4\":{\"name\":\"keyword.operator.assignment.augmented.ruby\"}},\"match\":\"(?<!\\\\.)\\\\b(case|if|elsif|unless|until|while)\\\\b\\\\s*(\\\\()*?\\\\s*([_a-z][0-9A-Z_a-z]*)\\\\s*(([-%*+/]|\\\\*\\\\*|[\\\\&^|]|<<|>>)=)\"},{\"captures\":{\"1\":{\"name\":\"variable.ruby\"}},\"match\":\"^\\\\s*([_a-z][0-9A-Z_a-z]*)\\\\s*(?==[^=>])\"},{\"captures\":{\"1\":{\"name\":\"keyword.control.ruby\"},\"3\":{\"name\":\"variable.ruby\"}},\"match\":\"(?<!\\\\.)\\\\b(case|if|elsif|unless|until|while)\\\\b\\\\s*(\\\\()*?\\\\s*([_a-z][0-9A-Z_a-z]*)\\\\s*=[^=>]\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.constant.hashkey.ruby\"}},\"match\":\"(?>[A-Z_a-z]\\\\w*[!?]?)(:)(?!:)\",\"name\":\"constant.language.symbol.hashkey.ruby\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.constant.ruby\"}},\"match\":\"(?<!:)(:)(?>[A-Z_a-z]\\\\w*[!?]?)(?=\\\\s*=>)\",\"name\":\"constant.language.symbol.hashkey.ruby\"},{\"match\":\"(?<!\\\\.)\\\\b(BEGIN|begin|case|class|else|elsif|END|end|ensure|for|if|in|module|rescue|then|unless|until|when|while)\\\\b(?![!?])\",\"name\":\"keyword.control.ruby\"},{\"match\":\"(?<!\\\\.)\\\\bdo\\\\b\",\"name\":\"keyword.control.start-block.ruby\"},{\"match\":\"(?<=\\\\{)(\\\\s+)\",\"name\":\"meta.syntax.ruby.start-block\"},{\"match\":\"(?<!\\\\.)\\\\b(alias|alias_method|break|next|redo|retry|return|super|undef|yield)\\\\b(?![!?])|\\\\bdefined\\\\?|\\\\b(block_given|iterator)\\\\?\",\"name\":\"keyword.control.pseudo-method.ruby\"},{\"match\":\"\\\\bnil\\\\b(?![!?])\",\"name\":\"constant.language.nil.ruby\"},{\"match\":\"\\\\b(true|false)\\\\b(?![!?])\",\"name\":\"constant.language.boolean.ruby\"},{\"match\":\"\\\\b(__(FILE|LINE)__)\\\\b(?![!?])\",\"name\":\"variable.language.ruby\"},{\"match\":\"\\\\bself\\\\b(?![!?])\",\"name\":\"variable.language.self.ruby\"},{\"match\":\"\\\\b(initialize|new|loop|include|extend|prepend|raise|fail|attr_reader|attr_writer|attr_accessor|attr|catch|throw|private|private_class_method|module_function|public|public_class_method|protected|refine|using)\\\\b(?![!?])\",\"name\":\"keyword.other.special-method.ruby\"},{\"begin\":\"\\\\b(?<!\\\\.|::)(require(?:|_relative))\\\\b(?![!?])\",\"captures\":{\"1\":{\"name\":\"keyword.other.special-method.ruby\"}},\"end\":\"$|(?=[#}])\",\"name\":\"meta.require.ruby\",\"patterns\":[{\"include\":\"$self\"}]},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.variable.ruby\"}},\"match\":\"(@)[A-Z_a-z]\\\\w*\",\"name\":\"variable.other.readwrite.instance.ruby\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.variable.ruby\"}},\"match\":\"(@@)[A-Z_a-z]\\\\w*\",\"name\":\"variable.other.readwrite.class.ruby\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.variable.ruby\"}},\"match\":\"(\\\\$)[A-Z_a-z]\\\\w*\",\"name\":\"variable.other.readwrite.global.ruby\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.variable.ruby\"}},\"match\":\"(\\\\$)([!\\\\&'+@`]|\\\\d+|[\\\"$*,./:-?\\\\\\\\_~]|-[0FIadilpv])\",\"name\":\"variable.other.readwrite.global.pre-defined.ruby\"},{\"begin\":\"\\\\b(ENV)\\\\[\",\"beginCaptures\":{\"1\":{\"name\":\"variable.other.constant.ruby\"}},\"end\":\"]\",\"name\":\"meta.environment-variable.ruby\",\"patterns\":[{\"include\":\"$self\"}]},{\"match\":\"\\\\b[A-Z]\\\\w*(?=((\\\\.|::)[A-Za-z]|\\\\[))\",\"name\":\"support.class.ruby\"},{\"match\":\"\\\\b((abort|at_exit|autoload|binding|callcc|caller|caller_locations|chomp|chop|eval|exec|exit|fork|format|gets|global_variables|gsub|lambda|load|local_variables|open|p|printf??|proc|putc|puts|rand|readlines??|select|set_trace_func|sleep|spawn|sprintf|srand|sub|syscall|system|test|trace_var|trap|untrace_var|warn)\\\\b(?![!?])|autoload\\\\?|exit!)\",\"name\":\"support.function.kernel.ruby\"},{\"match\":\"\\\\b[A-Z_]\\\\w*\\\\b\",\"name\":\"variable.other.constant.ruby\"},{\"begin\":\"(->)\\\\(\",\"beginCaptures\":{\"1\":{\"name\":\"support.function.kernel.ruby\"}},\"end\":\"\\\\)\",\"patterns\":[{\"begin\":\"(?=[\\\\&*A-Z_a-z])\",\"end\":\"(?=[),])\",\"patterns\":[{\"include\":\"#method_parameters\"}]},{\"include\":\"#method_parameters\"}]},{\"begin\":\"(?=def\\\\b)(?<=^|\\\\s)(def)\\\\s+((?>[A-Z_a-z]\\\\w*(?>\\\\.|::))?(?>[A-Z_a-z]\\\\w*(?>[!?]|=(?!>))?|===?|!=|>[=>]?|<=>|<[<=]?|[%\\\\&/`|]|\\\\*\\\\*?|=?~|[-+]@?|\\\\[]=?))\\\\s*(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.def.ruby\"},\"2\":{\"name\":\"entity.name.function.ruby\"},\"3\":{\"name\":\"punctuation.definition.parameters.ruby\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.parameters.ruby\"}},\"name\":\"meta.function.method.with-arguments.ruby\",\"patterns\":[{\"begin\":\"(?=[\\\\&*A-Z_a-z])\",\"end\":\"(?=[),])\",\"patterns\":[{\"include\":\"#method_parameters\"}]},{\"include\":\"#method_parameters\"}]},{\"begin\":\"(?=def\\\\b)(?<=^|\\\\s)(def)\\\\s+((?>[A-Z_a-z]\\\\w*(?>\\\\.|::))?(?>[A-Z_a-z]\\\\w*(?>[!?]|=(?!>))?|===?|!=|>[=>]?|<=>|<[<=]?|[%\\\\&/`|]|\\\\*\\\\*?|=?~|[-+]@?|\\\\[]=?))[\\\\t ](?=[\\\\t ]*[^#;\\\\s])\",\"beginCaptures\":{\"1\":{\"name\":\"keyword.control.def.ruby\"},\"2\":{\"name\":\"entity.name.function.ruby\"}},\"end\":\"(?=;)|(?<=[]!\\\"')?`}\\\\w])(?=\\\\s*#|\\\\s*$)\",\"name\":\"meta.function.method.with-arguments.ruby\",\"patterns\":[{\"begin\":\"(?=[\\\\&*A-Z_a-z])\",\"end\":\"(?=[,;]|\\\\s*#|\\\\s*$)\",\"patterns\":[{\"include\":\"#method_parameters\"}]},{\"include\":\"#method_parameters\"}]},{\"captures\":{\"1\":{\"name\":\"keyword.control.def.ruby\"},\"3\":{\"name\":\"entity.name.function.ruby\"}},\"match\":\"(?=def\\\\b)(?<=^|\\\\s)(def)\\\\b(\\\\s+((?>[A-Z_a-z]\\\\w*(?>\\\\.|::))?(?>[A-Z_a-z]\\\\w*(?>[!?]|=(?!>))?|===?|!=|>[=>]?|<=>|<[<=]?|[%\\\\&/`|]|\\\\*\\\\*?|=?~|[-+]@?|\\\\[]=?)))?\",\"name\":\"meta.function.method.without-arguments.ruby\"},{\"match\":\"\\\\b(\\\\d(?>_?\\\\d)*(\\\\.(?![^\\\\s\\\\d])(?>_?\\\\d)*)?([Ee][-+]?\\\\d(?>_?\\\\d)*)?|0(?:[Xx]\\\\h(?>_?\\\\h)*|[Oo]?[0-7](?>_?[0-7])*|[Bb][01](?>_?[01])*|[Dd]\\\\d(?>_?\\\\d)*))\\\\b\",\"name\":\"constant.numeric.ruby\"},{\"begin\":\":'\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.symbol.begin.ruby\"}},\"end\":\"'\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.symbol.end.ruby\"}},\"name\":\"constant.language.symbol.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\['\\\\\\\\]\",\"name\":\"constant.character.escape.ruby\"}]},{\"begin\":\":\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.symbol.begin.ruby\"}},\"end\":\"\\\"\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.symbol.end.ruby\"}},\"name\":\"constant.language.symbol.interpolated.ruby\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"}]},{\"match\":\"(?<!\\\\()/=\",\"name\":\"keyword.operator.assignment.augmented.ruby\"},{\"begin\":\"'\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.ruby\"}},\"end\":\"'\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.ruby\"}},\"name\":\"string.quoted.single.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\['\\\\\\\\]\",\"name\":\"constant.character.escape.ruby\"}]},{\"begin\":\"\\\"\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.ruby\"}},\"end\":\"\\\"\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.ruby\"}},\"name\":\"string.quoted.double.interpolated.ruby\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"}]},{\"begin\":\"(?<!\\\\.)`\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.ruby\"}},\"end\":\"`\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.ruby\"}},\"name\":\"string.interpolated.ruby\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"}]},{\"begin\":\"(?<![)\\\\w])((/))(?![*+?])(?=(?:\\\\\\\\/|[^/])*+/[eimnosux]*\\\\s*(?:[]#),.:?}]|\\\\|\\\\||&&|<=>|=>|==|=~|!~|!=|;|$|if|else|elsif|then|do|end|unless|while|until|or|and)|$)\",\"captures\":{\"1\":{\"name\":\"string.regexp.interpolated.ruby\"},\"2\":{\"name\":\"punctuation.section.regexp.ruby\"}},\"contentName\":\"string.regexp.interpolated.ruby\",\"end\":\"((/[eimnosux]*))\",\"patterns\":[{\"include\":\"#regex_sub\"}]},{\"begin\":\"%r\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.regexp.begin.ruby\"}},\"end\":\"}[eimnosux]*\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.regexp.end.ruby\"}},\"name\":\"string.regexp.interpolated.ruby\",\"patterns\":[{\"include\":\"#regex_sub\"},{\"include\":\"#nest_curly_r\"}]},{\"begin\":\"%r\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.regexp.begin.ruby\"}},\"end\":\"][eimnosux]*\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.regexp.end.ruby\"}},\"name\":\"string.regexp.interpolated.ruby\",\"patterns\":[{\"include\":\"#regex_sub\"},{\"include\":\"#nest_brackets_r\"}]},{\"begin\":\"%r\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.regexp.begin.ruby\"}},\"end\":\"\\\\)[eimnosux]*\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.regexp.end.ruby\"}},\"name\":\"string.regexp.interpolated.ruby\",\"patterns\":[{\"include\":\"#regex_sub\"},{\"include\":\"#nest_parens_r\"}]},{\"begin\":\"%r<\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.regexp.begin.ruby\"}},\"end\":\">[eimnosux]*\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.regexp.end.ruby\"}},\"name\":\"string.regexp.interpolated.ruby\",\"patterns\":[{\"include\":\"#regex_sub\"},{\"include\":\"#nest_ltgt_r\"}]},{\"begin\":\"%r(\\\\W)\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.regexp.begin.ruby\"}},\"end\":\"\\\\1[eimnosux]*\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.regexp.end.ruby\"}},\"name\":\"string.regexp.interpolated.ruby\",\"patterns\":[{\"include\":\"#regex_sub\"}]},{\"begin\":\"%I\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.array.begin.ruby\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.array.end.ruby\"}},\"name\":\"constant.language.symbol.interpolated.ruby\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"},{\"include\":\"#nest_brackets_i\"}]},{\"begin\":\"%I\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.array.begin.ruby\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.array.end.ruby\"}},\"name\":\"constant.language.symbol.interpolated.ruby\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"},{\"include\":\"#nest_parens_i\"}]},{\"begin\":\"%I<\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.array.begin.ruby\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.array.end.ruby\"}},\"name\":\"constant.language.symbol.interpolated.ruby\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"},{\"include\":\"#nest_ltgt_i\"}]},{\"begin\":\"%I\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.array.begin.ruby\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.array.end.ruby\"}},\"name\":\"constant.language.symbol.interpolated.ruby\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"},{\"include\":\"#nest_curly_i\"}]},{\"begin\":\"%I(\\\\W)\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.array.begin.ruby\"}},\"end\":\"\\\\1\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.array.end.ruby\"}},\"name\":\"constant.language.symbol.interpolated.ruby\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"}]},{\"begin\":\"%i\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.array.begin.ruby\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.array.end.ruby\"}},\"name\":\"constant.language.symbol.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\[]\\\\\\\\]\",\"name\":\"constant.character.escape.ruby\"},{\"include\":\"#nest_brackets\"}]},{\"begin\":\"%i\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.array.begin.ruby\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.array.end.ruby\"}},\"name\":\"constant.language.symbol.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\[)\\\\\\\\]\",\"name\":\"constant.character.escape.ruby\"},{\"include\":\"#nest_parens\"}]},{\"begin\":\"%i<\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.array.begin.ruby\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.array.end.ruby\"}},\"name\":\"constant.language.symbol.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\[>\\\\\\\\]\",\"name\":\"constant.character.escape.ruby\"},{\"include\":\"#nest_ltgt\"}]},{\"begin\":\"%i\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.array.begin.ruby\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.array.end.ruby\"}},\"name\":\"constant.language.symbol.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\[\\\\\\\\}]\",\"name\":\"constant.character.escape.ruby\"},{\"include\":\"#nest_curly\"}]},{\"begin\":\"%i(\\\\W)\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.array.begin.ruby\"}},\"end\":\"\\\\1\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.array.end.ruby\"}},\"name\":\"constant.language.symbol.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\.\"}]},{\"begin\":\"%W\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.array.begin.ruby\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.array.end.ruby\"}},\"name\":\"string.quoted.other.interpolated.ruby\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"},{\"include\":\"#nest_brackets_i\"}]},{\"begin\":\"%W\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.array.begin.ruby\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.array.end.ruby\"}},\"name\":\"string.quoted.other.interpolated.ruby\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"},{\"include\":\"#nest_parens_i\"}]},{\"begin\":\"%W<\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.array.begin.ruby\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.array.end.ruby\"}},\"name\":\"string.quoted.other.interpolated.ruby\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"},{\"include\":\"#nest_ltgt_i\"}]},{\"begin\":\"%W\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.array.begin.ruby\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.array.end.ruby\"}},\"name\":\"string.quoted.other.interpolated.ruby\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"},{\"include\":\"#nest_curly_i\"}]},{\"begin\":\"%W(\\\\W)\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.array.begin.ruby\"}},\"end\":\"\\\\1\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.array.end.ruby\"}},\"name\":\"string.quoted.other.interpolated.ruby\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"}]},{\"begin\":\"%w\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.array.begin.ruby\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.array.end.ruby\"}},\"name\":\"string.quoted.other.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\[]\\\\\\\\]\",\"name\":\"constant.character.escape.ruby\"},{\"include\":\"#nest_brackets\"}]},{\"begin\":\"%w\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.array.begin.ruby\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.array.end.ruby\"}},\"name\":\"string.quoted.other.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\[)\\\\\\\\]\",\"name\":\"constant.character.escape.ruby\"},{\"include\":\"#nest_parens\"}]},{\"begin\":\"%w<\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.array.begin.ruby\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.array.end.ruby\"}},\"name\":\"string.quoted.other.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\[>\\\\\\\\]\",\"name\":\"constant.character.escape.ruby\"},{\"include\":\"#nest_ltgt\"}]},{\"begin\":\"%w\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.array.begin.ruby\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.array.end.ruby\"}},\"name\":\"string.quoted.other.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\[\\\\\\\\}]\",\"name\":\"constant.character.escape.ruby\"},{\"include\":\"#nest_curly\"}]},{\"begin\":\"%w(\\\\W)\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.array.begin.ruby\"}},\"end\":\"\\\\1\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.array.end.ruby\"}},\"name\":\"string.quoted.other.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\.\"}]},{\"begin\":\"%[Qx]?\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.ruby\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.ruby\"}},\"name\":\"string.quoted.other.interpolated.ruby\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"},{\"include\":\"#nest_parens_i\"}]},{\"begin\":\"%[Qx]?\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.ruby\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.ruby\"}},\"name\":\"string.quoted.other.interpolated.ruby\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"},{\"include\":\"#nest_brackets_i\"}]},{\"begin\":\"%[Qx]?\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.ruby\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.ruby\"}},\"name\":\"string.quoted.other.interpolated.ruby\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"},{\"include\":\"#nest_curly_i\"}]},{\"begin\":\"%[Qx]?<\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.ruby\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.ruby\"}},\"name\":\"string.quoted.other.interpolated.ruby\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"},{\"include\":\"#nest_ltgt_i\"}]},{\"begin\":\"%[Qx](\\\\W)\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.ruby\"}},\"end\":\"\\\\1\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.ruby\"}},\"name\":\"string.quoted.other.interpolated.ruby\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"}]},{\"begin\":\"%([^=\\\\w\\\\s])\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.ruby\"}},\"end\":\"\\\\1\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.ruby\"}},\"name\":\"string.quoted.other.interpolated.ruby\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"}]},{\"begin\":\"%q\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.ruby\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.ruby\"}},\"name\":\"string.quoted.other.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\[)\\\\\\\\]\",\"name\":\"constant.character.escape.ruby\"},{\"include\":\"#nest_parens\"}]},{\"begin\":\"%q<\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.ruby\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.ruby\"}},\"name\":\"string.quoted.other.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\[>\\\\\\\\]\",\"name\":\"constant.character.escape.ruby\"},{\"include\":\"#nest_ltgt\"}]},{\"begin\":\"%q\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.ruby\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.ruby\"}},\"name\":\"string.quoted.other.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\[]\\\\\\\\]\",\"name\":\"constant.character.escape.ruby\"},{\"include\":\"#nest_brackets\"}]},{\"begin\":\"%q\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.ruby\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.ruby\"}},\"name\":\"string.quoted.other.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\[\\\\\\\\}]\",\"name\":\"constant.character.escape.ruby\"},{\"include\":\"#nest_curly\"}]},{\"begin\":\"%q(\\\\W)\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.begin.ruby\"}},\"end\":\"\\\\1\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.string.end.ruby\"}},\"name\":\"string.quoted.other.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\.\"}]},{\"begin\":\"%s\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.symbol.begin.ruby\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.symbol.end.ruby\"}},\"name\":\"constant.language.symbol.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\[)\\\\\\\\]\",\"name\":\"constant.character.escape.ruby\"},{\"include\":\"#nest_parens\"}]},{\"begin\":\"%s<\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.symbol.begin.ruby\"}},\"end\":\">\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.symbol.end.ruby\"}},\"name\":\"constant.language.symbol.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\[>\\\\\\\\]\",\"name\":\"constant.character.escape.ruby\"},{\"include\":\"#nest_ltgt\"}]},{\"begin\":\"%s\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.symbol.begin.ruby\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.symbol.end.ruby\"}},\"name\":\"constant.language.symbol.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\[]\\\\\\\\]\",\"name\":\"constant.character.escape.ruby\"},{\"include\":\"#nest_brackets\"}]},{\"begin\":\"%s\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.symbol.begin.ruby\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.symbol.end.ruby\"}},\"name\":\"constant.language.symbol.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\[\\\\\\\\}]\",\"name\":\"constant.character.escape.ruby\"},{\"include\":\"#nest_curly\"}]},{\"begin\":\"%s(\\\\W)\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.symbol.begin.ruby\"}},\"end\":\"\\\\1\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.symbol.end.ruby\"}},\"name\":\"constant.language.symbol.ruby\",\"patterns\":[{\"match\":\"\\\\\\\\.\"}]},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.constant.ruby\"}},\"match\":\"(?<!:)(:)(?>[$A-Z_a-z]\\\\w*(?>[!?]|=(?![=>]))?|===?|<=>|>[=>]?|<[<=]?|[%\\\\&/`|]|\\\\*\\\\*?|=?~|[-+]@?|\\\\[]=?|@@?[A-Z_a-z]\\\\w*)\",\"name\":\"constant.language.symbol.ruby\"},{\"begin\":\"^=begin\",\"captures\":{\"0\":{\"name\":\"punctuation.definition.comment.ruby\"}},\"end\":\"^=end\",\"name\":\"comment.block.documentation.ruby\"},{\"include\":\"#yard\"},{\"begin\":\"(^[\\\\t ]+)?(?=#)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.whitespace.comment.leading.ruby\"}},\"end\":\"(?!\\\\G)\",\"patterns\":[{\"begin\":\"#\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.ruby\"}},\"end\":\"\\\\n\",\"name\":\"comment.line.number-sign.ruby\"}]},{\"match\":\"(?<!\\\\w)\\\\?(\\\\\\\\(x\\\\h{1,2}(?!\\\\h)\\\\b|0[0-7]{0,2}(?![0-7])\\\\b|[^0CMx])|(\\\\\\\\[CM]-)+\\\\w|[^\\\\\\\\\\\\s])\",\"name\":\"constant.numeric.ruby\"},{\"begin\":\"^__END__\\\\n\",\"captures\":{\"0\":{\"name\":\"string.unquoted.program-block.ruby\"}},\"contentName\":\"text.plain\",\"end\":\"(?=not)impossible\",\"patterns\":[{\"begin\":\"(?=<?xml|<(?i:html\\\\b)|!DOCTYPE (?i:html\\\\b))\",\"end\":\"(?=not)impossible\",\"name\":\"text.html.embedded.ruby\",\"patterns\":[{\"include\":\"text.html.basic\"}]}]},{\"begin\":\"(?=(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)HTML)\\\\b\\\\1))\",\"end\":\"(?!\\\\G)\",\"name\":\"meta.embedded.block.html\",\"patterns\":[{\"begin\":\"(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)HTML)\\\\b\\\\1)\",\"beginCaptures\":{\"0\":{\"name\":\"string.definition.begin.ruby\"}},\"contentName\":\"text.html\",\"end\":\"^\\\\s*\\\\2$\\\\n?\",\"endCaptures\":{\"0\":{\"name\":\"string.definition.end.ruby\"}},\"patterns\":[{\"include\":\"#heredoc\"},{\"include\":\"#interpolated_ruby\"},{\"include\":\"text.html.basic\"},{\"include\":\"#escaped_char\"}]}]},{\"begin\":\"(?=(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)HAML)\\\\b\\\\1))\",\"end\":\"(?!\\\\G)\",\"name\":\"meta.embedded.block.haml\",\"patterns\":[{\"begin\":\"(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)HAML)\\\\b\\\\1)\",\"beginCaptures\":{\"0\":{\"name\":\"string.definition.begin.ruby\"}},\"contentName\":\"text.haml\",\"end\":\"^\\\\s*\\\\2$\\\\n?\",\"endCaptures\":{\"0\":{\"name\":\"string.definition.end.ruby\"}},\"patterns\":[{\"include\":\"#heredoc\"},{\"include\":\"#interpolated_ruby\"},{\"include\":\"text.haml\"},{\"include\":\"#escaped_char\"}]}]},{\"begin\":\"(?=(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)XML)\\\\b\\\\1))\",\"end\":\"(?!\\\\G)\",\"name\":\"meta.embedded.block.xml\",\"patterns\":[{\"begin\":\"(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)XML)\\\\b\\\\1)\",\"beginCaptures\":{\"0\":{\"name\":\"string.definition.begin.ruby\"}},\"contentName\":\"text.xml\",\"end\":\"^\\\\s*\\\\2$\\\\n?\",\"endCaptures\":{\"0\":{\"name\":\"string.definition.end.ruby\"}},\"patterns\":[{\"include\":\"#heredoc\"},{\"include\":\"#interpolated_ruby\"},{\"include\":\"text.xml\"},{\"include\":\"#escaped_char\"}]}]},{\"begin\":\"(?=(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)SQL)\\\\b\\\\1))\",\"end\":\"(?!\\\\G)\",\"name\":\"meta.embedded.block.sql\",\"patterns\":[{\"begin\":\"(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)SQL)\\\\b\\\\1)\",\"beginCaptures\":{\"0\":{\"name\":\"string.definition.begin.ruby\"}},\"contentName\":\"source.sql\",\"end\":\"^\\\\s*\\\\2$\\\\n?\",\"endCaptures\":{\"0\":{\"name\":\"string.definition.end.ruby\"}},\"patterns\":[{\"include\":\"#heredoc\"},{\"include\":\"#interpolated_ruby\"},{\"include\":\"source.sql\"},{\"include\":\"#escaped_char\"}]}]},{\"begin\":\"(?=(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)G(?:RAPHQL|QL))\\\\b\\\\1))\",\"end\":\"(?!\\\\G)\",\"name\":\"meta.embedded.block.graphql\",\"patterns\":[{\"begin\":\"(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)G(?:RAPHQL|QL))\\\\b\\\\1)\",\"beginCaptures\":{\"0\":{\"name\":\"string.definition.begin.ruby\"}},\"contentName\":\"source.graphql\",\"end\":\"^\\\\s*\\\\2$\\\\n?\",\"endCaptures\":{\"0\":{\"name\":\"string.definition.end.ruby\"}},\"patterns\":[{\"include\":\"#heredoc\"},{\"include\":\"#interpolated_ruby\"},{\"include\":\"source.graphql\"},{\"include\":\"#escaped_char\"}]}]},{\"begin\":\"(?=(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)CSS)\\\\b\\\\1))\",\"end\":\"(?!\\\\G)\",\"name\":\"meta.embedded.block.css\",\"patterns\":[{\"begin\":\"(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)CSS)\\\\b\\\\1)\",\"beginCaptures\":{\"0\":{\"name\":\"string.definition.begin.ruby\"}},\"contentName\":\"source.css\",\"end\":\"^\\\\s*\\\\2$\\\\n?\",\"endCaptures\":{\"0\":{\"name\":\"string.definition.end.ruby\"}},\"patterns\":[{\"include\":\"#heredoc\"},{\"include\":\"#interpolated_ruby\"},{\"include\":\"source.css\"},{\"include\":\"#escaped_char\"}]}]},{\"begin\":\"(?=(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)CPP)\\\\b\\\\1))\",\"end\":\"(?!\\\\G)\",\"name\":\"meta.embedded.block.cpp\",\"patterns\":[{\"begin\":\"(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)CPP)\\\\b\\\\1)\",\"beginCaptures\":{\"0\":{\"name\":\"string.definition.begin.ruby\"}},\"contentName\":\"source.cpp\",\"end\":\"^\\\\s*\\\\2$\\\\n?\",\"endCaptures\":{\"0\":{\"name\":\"string.definition.end.ruby\"}},\"patterns\":[{\"include\":\"#heredoc\"},{\"include\":\"#interpolated_ruby\"},{\"include\":\"source.cpp\"},{\"include\":\"#escaped_char\"}]}]},{\"begin\":\"(?=(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)C)\\\\b\\\\1))\",\"end\":\"(?!\\\\G)\",\"name\":\"meta.embedded.block.c\",\"patterns\":[{\"begin\":\"(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)C)\\\\b\\\\1)\",\"beginCaptures\":{\"0\":{\"name\":\"string.definition.begin.ruby\"}},\"contentName\":\"source.c\",\"end\":\"^\\\\s*\\\\2$\\\\n?\",\"endCaptures\":{\"0\":{\"name\":\"string.definition.end.ruby\"}},\"patterns\":[{\"include\":\"#heredoc\"},{\"include\":\"#interpolated_ruby\"},{\"include\":\"source.c\"},{\"include\":\"#escaped_char\"}]}]},{\"begin\":\"(?=(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)J(?:S|AVASCRIPT))\\\\b\\\\1))\",\"end\":\"(?!\\\\G)\",\"name\":\"meta.embedded.block.js\",\"patterns\":[{\"begin\":\"(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)J(?:S|AVASCRIPT))\\\\b\\\\1)\",\"beginCaptures\":{\"0\":{\"name\":\"string.definition.begin.ruby\"}},\"contentName\":\"source.js\",\"end\":\"^\\\\s*\\\\2$\\\\n?\",\"endCaptures\":{\"0\":{\"name\":\"string.definition.end.ruby\"}},\"patterns\":[{\"include\":\"#heredoc\"},{\"include\":\"#interpolated_ruby\"},{\"include\":\"source.js\"},{\"include\":\"#escaped_char\"}]}]},{\"begin\":\"(?=(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)JQUERY)\\\\b\\\\1))\",\"end\":\"(?!\\\\G)\",\"name\":\"meta.embedded.block.js.jquery\",\"patterns\":[{\"begin\":\"(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)JQUERY)\\\\b\\\\1)\",\"beginCaptures\":{\"0\":{\"name\":\"string.definition.begin.ruby\"}},\"contentName\":\"source.js.jquery\",\"end\":\"^\\\\s*\\\\2$\\\\n?\",\"endCaptures\":{\"0\":{\"name\":\"string.definition.end.ruby\"}},\"patterns\":[{\"include\":\"#heredoc\"},{\"include\":\"#interpolated_ruby\"},{\"include\":\"source.js.jquery\"},{\"include\":\"#escaped_char\"}]}]},{\"begin\":\"(?=(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)SH(?:|ELL))\\\\b\\\\1))\",\"end\":\"(?!\\\\G)\",\"name\":\"meta.embedded.block.shell\",\"patterns\":[{\"begin\":\"(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)SH(?:|ELL))\\\\b\\\\1)\",\"beginCaptures\":{\"0\":{\"name\":\"string.definition.begin.ruby\"}},\"contentName\":\"source.shell\",\"end\":\"^\\\\s*\\\\2$\\\\n?\",\"endCaptures\":{\"0\":{\"name\":\"string.definition.end.ruby\"}},\"patterns\":[{\"include\":\"#heredoc\"},{\"include\":\"#interpolated_ruby\"},{\"include\":\"source.shell\"},{\"include\":\"#escaped_char\"}]}]},{\"begin\":\"(?=(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)LUA)\\\\b\\\\1))\",\"end\":\"(?!\\\\G)\",\"name\":\"meta.embedded.block.lua\",\"patterns\":[{\"begin\":\"(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)LUA)\\\\b\\\\1)\",\"beginCaptures\":{\"0\":{\"name\":\"string.definition.begin.ruby\"}},\"contentName\":\"source.lua\",\"end\":\"^\\\\s*\\\\2$\\\\n?\",\"endCaptures\":{\"0\":{\"name\":\"string.definition.end.ruby\"}},\"patterns\":[{\"include\":\"#heredoc\"},{\"include\":\"#interpolated_ruby\"},{\"include\":\"source.lua\"},{\"include\":\"#escaped_char\"}]}]},{\"begin\":\"(?=(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)RUBY)\\\\b\\\\1))\",\"end\":\"(?!\\\\G)\",\"name\":\"meta.embedded.block.ruby\",\"patterns\":[{\"begin\":\"(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)RUBY)\\\\b\\\\1)\",\"beginCaptures\":{\"0\":{\"name\":\"string.definition.begin.ruby\"}},\"contentName\":\"source.ruby\",\"end\":\"^\\\\s*\\\\2$\\\\n?\",\"endCaptures\":{\"0\":{\"name\":\"string.definition.end.ruby\"}},\"patterns\":[{\"include\":\"#heredoc\"},{\"include\":\"#interpolated_ruby\"},{\"include\":\"source.ruby\"},{\"include\":\"#escaped_char\"}]}]},{\"begin\":\"(?=(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)YA?ML)\\\\b\\\\1))\",\"end\":\"(?!\\\\G)\",\"name\":\"meta.embedded.block.yaml\",\"patterns\":[{\"begin\":\"(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)YA?ML)\\\\b\\\\1)\",\"beginCaptures\":{\"0\":{\"name\":\"string.definition.begin.ruby\"}},\"contentName\":\"source.yaml\",\"end\":\"^\\\\s*\\\\2$\\\\n?\",\"endCaptures\":{\"0\":{\"name\":\"string.definition.end.ruby\"}},\"patterns\":[{\"include\":\"#heredoc\"},{\"include\":\"#interpolated_ruby\"},{\"include\":\"source.yaml\"},{\"include\":\"#escaped_char\"}]}]},{\"begin\":\"(?=(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)SLIM)\\\\b\\\\1))\",\"end\":\"(?!\\\\G)\",\"name\":\"meta.embedded.block.slim\",\"patterns\":[{\"begin\":\"(?><<[-~]([\\\"'`]?)((?:[_\\\\w]+_|)SLIM)\\\\b\\\\1)\",\"beginCaptures\":{\"0\":{\"name\":\"string.definition.begin.ruby\"}},\"contentName\":\"text.slim\",\"end\":\"^\\\\s*\\\\2$\\\\n?\",\"endCaptures\":{\"0\":{\"name\":\"string.definition.end.ruby\"}},\"patterns\":[{\"include\":\"#heredoc\"},{\"include\":\"#interpolated_ruby\"},{\"include\":\"text.slim\"},{\"include\":\"#escaped_char\"}]}]},{\"begin\":\"(?>=\\\\s*<<([\\\"'`]?)(\\\\w+)\\\\1)\",\"beginCaptures\":{\"0\":{\"name\":\"string.definition.begin.ruby\"}},\"contentName\":\"string.unquoted.heredoc.ruby\",\"end\":\"^\\\\2$\",\"endCaptures\":{\"0\":{\"name\":\"string.definition.end.ruby\"}},\"patterns\":[{\"include\":\"#heredoc\"},{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"}]},{\"begin\":\"(?>((<<[-~]([\\\"'`]?)(\\\\w+)\\\\3,\\\\s?)*<<[-~]([\\\"'`]?)(\\\\w+)\\\\5))(.*)\",\"beginCaptures\":{\"1\":{\"name\":\"string.definition.begin.ruby\"},\"7\":{\"patterns\":[{\"include\":\"source.ruby\"}]}},\"contentName\":\"string.unquoted.heredoc.ruby\",\"end\":\"^\\\\s*\\\\6$\",\"endCaptures\":{\"0\":{\"name\":\"string.definition.end.ruby\"}},\"patterns\":[{\"include\":\"#heredoc\"},{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"}]},{\"begin\":\"(?<=\\\\{|\\\\{\\\\s+|[^$0-:@-Z_a-z]do|^do|[^$0-:@-Z_a-z]do\\\\s+|^do\\\\s+)(\\\\|)\",\"captures\":{\"1\":{\"name\":\"punctuation.separator.variable.ruby\"}},\"end\":\"(?<!\\\\|)(\\\\|)(?!\\\\|)\",\"name\":\"meta.block.parameters.ruby\",\"patterns\":[{\"begin\":\"(?![(,|\\\\s])\",\"end\":\"(?=,|\\\\|\\\\s*)\",\"patterns\":[{\"captures\":{\"1\":{\"name\":\"storage.type.variable.ruby\"},\"2\":{\"name\":\"variable.other.block.ruby\"}},\"match\":\"\\\\G((?:&|\\\\*\\\\*?)?)([A-Z_a-z][_\\\\w]*)\"}]},{\"match\":\",\",\"name\":\"punctuation.separator.variable.ruby\"}]},{\"match\":\"=>\",\"name\":\"punctuation.separator.key-value\"},{\"match\":\"->\",\"name\":\"support.function.kernel.ruby\"},{\"match\":\"<<=|%=|&{1,2}=|\\\\*=|\\\\*\\\\*=|\\\\+=|-=|\\\\^=|\\\\|{1,2}=|<<\",\"name\":\"keyword.operator.assignment.augmented.ruby\"},{\"match\":\"<=>|<(?![<=])|>(?![<=>])|<=|>=|===?|=~|!=|!~|(?<=[\\\\t ])\\\\?\",\"name\":\"keyword.operator.comparison.ruby\"},{\"match\":\"(?<!\\\\.)\\\\b(and|not|or)\\\\b(?![!?])\",\"name\":\"keyword.operator.logical.ruby\"},{\"match\":\"(?<=^|[\\\\t !])!|&&|\\\\|\\\\||\\\\^\",\"name\":\"keyword.operator.logical.ruby\"},{\"captures\":{\"1\":{\"name\":\"keyword.operator.logical.ruby\"}},\"match\":\"(&\\\\.)\\\\s*(?![A-Z])\"},{\"match\":\"([%\\\\&]|\\\\*\\\\*|[-*+/])\",\"name\":\"keyword.operator.arithmetic.ruby\"},{\"match\":\"=\",\"name\":\"keyword.operator.assignment.ruby\"},{\"match\":\"[|~]|>>\",\"name\":\"keyword.operator.other.ruby\"},{\"match\":\";\",\"name\":\"punctuation.separator.statement.ruby\"},{\"match\":\",\",\"name\":\"punctuation.separator.object.ruby\"},{\"captures\":{\"1\":{\"name\":\"punctuation.separator.namespace.ruby\"}},\"match\":\"(::)\\\\s*(?=[A-Z])\"},{\"captures\":{\"1\":{\"name\":\"punctuation.separator.method.ruby\"}},\"match\":\"(\\\\.|::)\\\\s*(?![A-Z])\"},{\"match\":\":\",\"name\":\"punctuation.separator.other.ruby\"},{\"match\":\"\\\\{\",\"name\":\"punctuation.section.scope.begin.ruby\"},{\"match\":\"}\",\"name\":\"punctuation.section.scope.end.ruby\"},{\"match\":\"\\\\[\",\"name\":\"punctuation.section.array.begin.ruby\"},{\"match\":\"]\",\"name\":\"punctuation.section.array.end.ruby\"},{\"match\":\"[()]\",\"name\":\"punctuation.section.function.ruby\"},{\"begin\":\"(?<=[^.]\\\\.|::)(?=[A-Za-z][!0-9?A-Z_a-z]*[^!0-9?A-Z_a-z])\",\"end\":\"(?<=[!0-9?A-Z_a-z])(?=[^!0-9?A-Z_a-z])\",\"name\":\"meta.function-call.ruby\",\"patterns\":[{\"match\":\"([A-Za-z][!0-9?A-Z_a-z]*)(?=[^!0-9?A-Z_a-z])\",\"name\":\"entity.name.function.ruby\"}]},{\"begin\":\"([A-Za-z]\\\\w*[!?]?)(\\\\()\",\"beginCaptures\":{\"1\":{\"name\":\"entity.name.function.ruby\"},\"2\":{\"name\":\"punctuation.section.function.ruby\"}},\"end\":\"(\\\\))\",\"endCaptures\":{\"1\":{\"name\":\"punctuation.section.function.ruby\"}},\"name\":\"meta.function-call.ruby\",\"patterns\":[{\"include\":\"$self\"}]}],\"repository\":{\"escaped_char\":{\"match\":\"\\\\\\\\(?:[0-7]{1,3}|x[A-Fa-f\\\\d]{1,2}|.)\",\"name\":\"constant.character.escape.ruby\"},\"heredoc\":{\"begin\":\"^<<[-~]?\\\\w+\",\"end\":\"$\",\"patterns\":[{\"include\":\"$self\"}]},\"interpolated_ruby\":{\"patterns\":[{\"begin\":\"#\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.embedded.begin.ruby\"}},\"contentName\":\"source.ruby\",\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.embedded.end.ruby\"}},\"name\":\"meta.embedded.line.ruby\",\"patterns\":[{\"include\":\"#nest_curly_and_self\"},{\"include\":\"$self\"}]},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.variable.ruby\"}},\"match\":\"(#@)[A-Z_a-z]\\\\w*\",\"name\":\"variable.other.readwrite.instance.ruby\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.variable.ruby\"}},\"match\":\"(#@@)[A-Z_a-z]\\\\w*\",\"name\":\"variable.other.readwrite.class.ruby\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.variable.ruby\"}},\"match\":\"(#\\\\$)[A-Z_a-z]\\\\w*\",\"name\":\"variable.other.readwrite.global.ruby\"}]},\"method_parameters\":{\"patterns\":[{\"include\":\"#parens\"},{\"include\":\"#braces\"},{\"include\":\"#brackets\"},{\"include\":\"#params\"},{\"include\":\"$self\"}],\"repository\":{\"braces\":{\"begin\":\"\\\\{\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.scope.begin.ruby\"}},\"end\":\"}\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.scope.end.ruby\"}},\"patterns\":[{\"include\":\"#parens\"},{\"include\":\"#braces\"},{\"include\":\"#brackets\"},{\"include\":\"$self\"}]},\"brackets\":{\"begin\":\"\\\\[\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.array.begin.ruby\"}},\"end\":\"]\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.array.end.ruby\"}},\"patterns\":[{\"include\":\"#parens\"},{\"include\":\"#braces\"},{\"include\":\"#brackets\"},{\"include\":\"$self\"}]},\"params\":{\"captures\":{\"1\":{\"name\":\"storage.type.variable.ruby\"},\"2\":{\"name\":\"constant.other.symbol.hashkey.parameter.function.ruby\"},\"3\":{\"name\":\"punctuation.definition.constant.ruby\"},\"4\":{\"name\":\"variable.parameter.function.ruby\"}},\"match\":\"\\\\G(&|\\\\*\\\\*?)?(?:([A-Z_a-z]\\\\w*[!?]?(:))|([A-Z_a-z]\\\\w*))\"},\"parens\":{\"begin\":\"\\\\(\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.section.function.begin.ruby\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.section.function.end.ruby\"}},\"patterns\":[{\"include\":\"#parens\"},{\"include\":\"#braces\"},{\"include\":\"#brackets\"},{\"include\":\"$self\"}]}}},\"nest_brackets\":{\"begin\":\"\\\\[\",\"captures\":{\"0\":{\"name\":\"punctuation.section.scope.ruby\"}},\"end\":\"]\",\"patterns\":[{\"include\":\"#nest_brackets\"}]},\"nest_brackets_i\":{\"begin\":\"\\\\[\",\"captures\":{\"0\":{\"name\":\"punctuation.section.scope.ruby\"}},\"end\":\"]\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"},{\"include\":\"#nest_brackets_i\"}]},\"nest_brackets_r\":{\"begin\":\"\\\\[\",\"captures\":{\"0\":{\"name\":\"punctuation.section.scope.ruby\"}},\"end\":\"]\",\"patterns\":[{\"include\":\"#regex_sub\"},{\"include\":\"#nest_brackets_r\"}]},\"nest_curly\":{\"begin\":\"\\\\{\",\"captures\":{\"0\":{\"name\":\"punctuation.section.scope.ruby\"}},\"end\":\"}\",\"patterns\":[{\"include\":\"#nest_curly\"}]},\"nest_curly_and_self\":{\"patterns\":[{\"begin\":\"\\\\{\",\"captures\":{\"0\":{\"name\":\"punctuation.section.scope.ruby\"}},\"end\":\"}\",\"patterns\":[{\"include\":\"#nest_curly_and_self\"}]},{\"include\":\"$self\"}]},\"nest_curly_i\":{\"begin\":\"\\\\{\",\"captures\":{\"0\":{\"name\":\"punctuation.section.scope.ruby\"}},\"end\":\"}\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"},{\"include\":\"#nest_curly_i\"}]},\"nest_curly_r\":{\"begin\":\"\\\\{\",\"captures\":{\"0\":{\"name\":\"punctuation.section.scope.ruby\"}},\"end\":\"}\",\"patterns\":[{\"include\":\"#regex_sub\"},{\"include\":\"#nest_curly_r\"}]},\"nest_ltgt\":{\"begin\":\"<\",\"captures\":{\"0\":{\"name\":\"punctuation.section.scope.ruby\"}},\"end\":\">\",\"patterns\":[{\"include\":\"#nest_ltgt\"}]},\"nest_ltgt_i\":{\"begin\":\"<\",\"captures\":{\"0\":{\"name\":\"punctuation.section.scope.ruby\"}},\"end\":\">\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"},{\"include\":\"#nest_ltgt_i\"}]},\"nest_ltgt_r\":{\"begin\":\"<\",\"captures\":{\"0\":{\"name\":\"punctuation.section.scope.ruby\"}},\"end\":\">\",\"patterns\":[{\"include\":\"#regex_sub\"},{\"include\":\"#nest_ltgt_r\"}]},\"nest_parens\":{\"begin\":\"\\\\(\",\"captures\":{\"0\":{\"name\":\"punctuation.section.scope.ruby\"}},\"end\":\"\\\\)\",\"patterns\":[{\"include\":\"#nest_parens\"}]},\"nest_parens_i\":{\"begin\":\"\\\\(\",\"captures\":{\"0\":{\"name\":\"punctuation.section.scope.ruby\"}},\"end\":\"\\\\)\",\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"},{\"include\":\"#nest_parens_i\"}]},\"nest_parens_r\":{\"begin\":\"\\\\(\",\"captures\":{\"0\":{\"name\":\"punctuation.section.scope.ruby\"}},\"end\":\"\\\\)\",\"patterns\":[{\"include\":\"#regex_sub\"},{\"include\":\"#nest_parens_r\"}]},\"regex_sub\":{\"patterns\":[{\"include\":\"#interpolated_ruby\"},{\"include\":\"#escaped_char\"},{\"captures\":{\"1\":{\"name\":\"punctuation.definition.arbitrary-repetition.ruby\"},\"3\":{\"name\":\"punctuation.definition.arbitrary-repetition.ruby\"}},\"match\":\"(\\\\{)\\\\d+(,\\\\d+)?(})\",\"name\":\"string.regexp.arbitrary-repetition.ruby\"},{\"begin\":\"\\\\[(?:\\\\^?])?\",\"captures\":{\"0\":{\"name\":\"punctuation.definition.character-class.ruby\"}},\"end\":\"]\",\"name\":\"string.regexp.character-class.ruby\",\"patterns\":[{\"include\":\"#escaped_char\"}]},{\"begin\":\"\\\\(\\\\?#\",\"beginCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.begin.ruby\"}},\"end\":\"\\\\)\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.end.ruby\"}},\"name\":\"comment.line.number-sign.ruby\",\"patterns\":[{\"include\":\"#escaped_char\"}]},{\"begin\":\"\\\\(\",\"captures\":{\"0\":{\"name\":\"punctuation.definition.group.ruby\"}},\"end\":\"\\\\)\",\"name\":\"string.regexp.group.ruby\",\"patterns\":[{\"include\":\"#regex_sub\"}]},{\"begin\":\"(?<=^|\\\\s)(#)\\\\s(?=[-\\\\t !,.0-9?A-Za-z[^\\\\x00-\\\\x7F]]*$)\",\"beginCaptures\":{\"1\":{\"name\":\"punctuation.definition.comment.ruby\"}},\"end\":\"$\\\\n?\",\"endCaptures\":{\"0\":{\"name\":\"punctuation.definition.comment.ruby\"}},\"name\":\"comment.line.number-sign.ruby\"}]},\"yard\":{\"patterns\":[{\"include\":\"#yard_comment\"},{\"include\":\"#yard_param_types\"},{\"include\":\"#yard_option\"},{\"include\":\"#yard_tag\"},{\"include\":\"#yard_types\"},{\"include\":\"#yard_directive\"},{\"include\":\"#yard_see\"},{\"include\":\"#yard_macro_attribute\"}]},\"yard_comment\":{\"begin\":\"^(\\\\s*)(#)(\\\\s*)(@)(abstract|api|author|deprecated|example|macro|note|overload|since|todo|version)(?=\\\\s|$)\",\"beginCaptures\":{\"2\":{\"name\":\"punctuation.definition.comment.ruby\"},\"4\":{\"name\":\"comment.line.keyword.punctuation.yard.ruby\"},\"5\":{\"name\":\"comment.line.keyword.yard.ruby\"}},\"contentName\":\"comment.line.string.yard.ruby\",\"end\":\"^(?!\\\\s*#\\\\3\\\\s{2,}|\\\\s*#\\\\s*$)\",\"name\":\"comment.line.number-sign.ruby\",\"patterns\":[{\"include\":\"#yard\"},{\"include\":\"#yard_continuation\"}]},\"yard_continuation\":{\"match\":\"^\\\\s*#\",\"name\":\"punctuation.definition.comment.ruby\"},\"yard_directive\":{\"begin\":\"^(\\\\s*)(#)(\\\\s*)(@!)(endgroup|group|method|parse|scope|visibility)(\\\\s+((\\\\[).+(])))?(?=\\\\s)\",\"beginCaptures\":{\"2\":{\"name\":\"punctuation.definition.comment.ruby\"},\"4\":{\"name\":\"comment.line.keyword.punctuation.yard.ruby\"},\"5\":{\"name\":\"comment.line.keyword.yard.ruby\"},\"7\":{\"name\":\"comment.line.type.yard.ruby\"},\"8\":{\"name\":\"comment.line.punctuation.yard.ruby\"},\"9\":{\"name\":\"comment.line.punctuation.yard.ruby\"}},\"contentName\":\"comment.line.string.yard.ruby\",\"end\":\"^(?!\\\\s*#\\\\3\\\\s{2,}|\\\\s*#\\\\s*$)\",\"name\":\"comment.line.number-sign.ruby\",\"patterns\":[{\"include\":\"#yard\"},{\"include\":\"#yard_continuation\"}]},\"yard_macro_attribute\":{\"begin\":\"^(\\\\s*)(#)(\\\\s*)(@!)(attribute|macro)(\\\\s+((\\\\[).+(])))?(?=\\\\s)(\\\\s+([_a-z]\\\\w*:?))?\",\"beginCaptures\":{\"2\":{\"name\":\"punctuation.definition.comment.ruby\"},\"4\":{\"name\":\"comment.line.keyword.punctuation.yard.ruby\"},\"5\":{\"name\":\"comment.line.keyword.yard.ruby\"},\"7\":{\"name\":\"comment.line.type.yard.ruby\"},\"8\":{\"name\":\"comment.line.punctuation.yard.ruby\"},\"9\":{\"name\":\"comment.line.punctuation.yard.ruby\"},\"11\":{\"name\":\"comment.line.parameter.yard.ruby\"}},\"contentName\":\"comment.line.string.yard.ruby\",\"end\":\"^(?!\\\\s*#\\\\3\\\\s{2,}|\\\\s*#\\\\s*$)\",\"name\":\"comment.line.number-sign.ruby\",\"patterns\":[{\"include\":\"#yard\"},{\"include\":\"#yard_continuation\"}]},\"yard_option\":{\"begin\":\"^(\\\\s*)(#)(\\\\s*)(@)(option)(?=\\\\s)(?>\\\\s+([_a-z]\\\\w*:?))?(?>\\\\s+((\\\\[).+(])))?(?>\\\\s+((\\\\S*)))?(?>\\\\s+((\\\\().+(\\\\))))?\",\"beginCaptures\":{\"2\":{\"name\":\"punctuation.definition.comment.ruby\"},\"4\":{\"name\":\"comment.line.keyword.punctuation.yard.ruby\"},\"5\":{\"name\":\"comment.line.keyword.yard.ruby\"},\"6\":{\"name\":\"comment.line.parameter.yard.ruby\"},\"7\":{\"name\":\"comment.line.type.yard.ruby\"},\"8\":{\"name\":\"comment.line.punctuation.yard.ruby\"},\"9\":{\"name\":\"comment.line.punctuation.yard.ruby\"},\"10\":{\"name\":\"comment.line.keyword.yard.ruby\"},\"11\":{\"name\":\"comment.line.hashkey.yard.ruby\"},\"12\":{\"name\":\"comment.line.defaultvalue.yard.ruby\"},\"13\":{\"name\":\"comment.line.punctuation.yard.ruby\"},\"14\":{\"name\":\"comment.line.punctuation.yard.ruby\"}},\"contentName\":\"comment.line.string.yard.ruby\",\"end\":\"^(?!\\\\s*#\\\\3\\\\s{2,}|\\\\s*#\\\\s*$)\",\"name\":\"comment.line.number-sign.ruby\",\"patterns\":[{\"include\":\"#yard\"},{\"include\":\"#yard_continuation\"}]},\"yard_param_types\":{\"begin\":\"^(\\\\s*)(#)(\\\\s*)(@)(attr|attr_reader|attr_writer|yieldparam|param)(?=\\\\s)(?>\\\\s+(?>([_a-z]\\\\w*:?)|((\\\\[).+(]))))?(?>\\\\s+(?>((\\\\[).+(]))|([_a-z]\\\\w*:?)))?\",\"beginCaptures\":{\"2\":{\"name\":\"punctuation.definition.comment.ruby\"},\"4\":{\"name\":\"comment.line.keyword.punctuation.yard.ruby\"},\"5\":{\"name\":\"comment.line.keyword.yard.ruby\"},\"6\":{\"name\":\"comment.line.parameter.yard.ruby\"},\"7\":{\"name\":\"comment.line.type.yard.ruby\"},\"8\":{\"name\":\"comment.line.punctuation.yard.ruby\"},\"9\":{\"name\":\"comment.line.punctuation.yard.ruby\"},\"10\":{\"name\":\"comment.line.type.yard.ruby\"},\"11\":{\"name\":\"comment.line.punctuation.yard.ruby\"},\"12\":{\"name\":\"comment.line.punctuation.yard.ruby\"},\"13\":{\"name\":\"comment.line.parameter.yard.ruby\"}},\"contentName\":\"comment.line.string.yard.ruby\",\"end\":\"^(?!\\\\s*#\\\\3\\\\s{2,}|\\\\s*#\\\\s*$)\",\"name\":\"comment.line.number-sign.ruby\",\"patterns\":[{\"include\":\"#yard\"},{\"include\":\"#yard_continuation\"}]},\"yard_see\":{\"begin\":\"^(\\\\s*)(#)(\\\\s*)(@)(see)(?=\\\\s)(\\\\s+(.+?))?(?=\\\\s|$)\",\"beginCaptures\":{\"2\":{\"name\":\"punctuation.definition.comment.ruby\"},\"4\":{\"name\":\"comment.line.keyword.punctuation.yard.ruby\"},\"5\":{\"name\":\"comment.line.keyword.yard.ruby\"},\"7\":{\"name\":\"comment.line.parameter.yard.ruby\"}},\"contentName\":\"comment.line.string.yard.ruby\",\"end\":\"^(?!\\\\s*#\\\\3\\\\s{2,}|\\\\s*#\\\\s*$)\",\"name\":\"comment.line.number-sign.ruby\",\"patterns\":[{\"include\":\"#yard\"},{\"include\":\"#yard_continuation\"}]},\"yard_tag\":{\"captures\":{\"2\":{\"name\":\"punctuation.definition.comment.ruby\"},\"4\":{\"name\":\"comment.line.keyword.punctuation.yard.ruby\"},\"5\":{\"name\":\"comment.line.keyword.yard.ruby\"}},\"match\":\"^(\\\\s*)(#)(\\\\s*)(@)(private)$\",\"name\":\"comment.line.number-sign.ruby\"},\"yard_types\":{\"begin\":\"^(\\\\s*)(#)(\\\\s*)(@)(raise|return|yield(?:return)?)(?=\\\\s)(\\\\s+((\\\\[).+(])))?\",\"beginCaptures\":{\"2\":{\"name\":\"punctuation.definition.comment.ruby\"},\"4\":{\"name\":\"comment.line.keyword.punctuation.yard.ruby\"},\"5\":{\"name\":\"comment.line.keyword.yard.ruby\"},\"7\":{\"name\":\"comment.line.type.yard.ruby\"},\"8\":{\"name\":\"comment.line.punctuation.yard.ruby\"},\"9\":{\"name\":\"comment.line.punctuation.yard.ruby\"}},\"contentName\":\"comment.line.string.yard.ruby\",\"end\":\"^(?!\\\\s*#\\\\3\\\\s{2,}|\\\\s*#\\\\s*$)\",\"name\":\"comment.line.number-sign.ruby\",\"patterns\":[{\"include\":\"#yard\"},{\"include\":\"#yard_continuation\"}]}},\"scopeName\":\"source.ruby\",\"embeddedLangs\":[\"html\",\"haml\",\"xml\",\"sql\",\"graphql\",\"css\",\"cpp\",\"c\",\"javascript\",\"shellscript\",\"lua\",\"yaml\"],\"aliases\":[\"rb\"]}"));
const __TURBOPACK__default__export__ = [
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$html$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$haml$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$xml$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$sql$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$graphql$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$css$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$cpp$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$c$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$javascript$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$shellscript$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$lua$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    ...__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$shikijs$2f$langs$2f$dist$2f$yaml$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    lang
];
}}),
}]);

//# sourceMappingURL=node_modules_%40shikijs_langs_dist_83e26cd6._.js.map