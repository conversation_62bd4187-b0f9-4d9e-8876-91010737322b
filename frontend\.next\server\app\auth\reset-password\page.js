(()=>{var e={};e.id=4089,e.ids=[4089],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},5876:(e,r,t)=>{"use strict";t.d(r,{U:()=>d});var s=t(91199);t(42087);var a=t(30445),n=t(74208),i=t(33331);let d=async()=>{let e=await (0,n.UL)(),r="";return r&&!r.startsWith("http")&&(r=`http://${r}`),(0,a.createServerClient)(r,"",{cookies:{getAll:()=>e.getAll(),setAll(r){try{r.forEach(({name:r,value:t,options:s})=>e.set({name:r,value:t,...s}))}catch(e){}}}})};(0,i.D)([d]),(0,s.A)(d,"7f8bed79c8654f95685745e61906af37f96a086236",null)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21787:(e,r,t)=>{Promise.resolve().then(t.bind(t,23678))},23678:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var s=t(60687),a=t(85814),n=t.n(a),i=t(16189),d=t(43210),l=t(5336),o=t(50940),c=t(93613),u=t(89667),m=t(44774),p=t(6475);let f=(0,p.createServerReference)("60fcf04f7f92ed069c46b1be2805385ba3ee83149d",p.callServer,void 0,p.findSourceMapURL,"resetPassword");function x(){(0,i.useRouter)();let e=(0,i.useSearchParams)().get("code"),[r,t]=(0,d.useState)(!1),[a,p]=(0,d.useState)(null),x=async(r,s)=>{if(!e)return{message:"Invalid reset code"};let a=await f(r,s);return a&&"object"==typeof a&&"success"in a&&a.success&&t(!0),a};return r?(0,s.jsx)("main",{className:"flex flex-col items-center justify-center min-h-screen w-full",children:(0,s.jsx)("div",{className:"w-full divide-y divide-border",children:(0,s.jsx)("section",{className:"w-full relative overflow-hidden",children:(0,s.jsxs)("div",{className:"relative flex flex-col items-center w-full px-6",children:[(0,s.jsx)("div",{className:"absolute inset-x-1/4 top-0 h-[600px] md:h-[800px] -z-20 bg-background rounded-b-xl"}),(0,s.jsx)("div",{className:"relative z-10 pt-24 pb-8 max-w-xl mx-auto h-full w-full flex flex-col gap-2 items-center justify-center",children:(0,s.jsxs)("div",{className:"flex flex-col items-center text-center",children:[(0,s.jsx)("div",{className:"bg-green-50 dark:bg-green-950/20 rounded-full p-4 mb-6",children:(0,s.jsx)(l.A,{className:"h-12 w-12 text-green-500 dark:text-green-400"})}),(0,s.jsx)("h1",{className:"text-3xl md:text-4xl lg:text-5xl font-medium tracking-tighter text-center text-balance text-primary mb-4",children:"Password Reset Complete"}),(0,s.jsx)("p",{className:"text-base md:text-lg text-center text-muted-foreground font-medium text-balance leading-relaxed tracking-tight max-w-md mb-6",children:"Your password has been successfully updated. You can now sign in with your new password."}),(0,s.jsx)("div",{className:"flex flex-col sm:flex-row gap-4 w-full max-w-sm",children:(0,s.jsx)(n(),{href:"/auth",className:"flex h-12 items-center justify-center w-full text-center rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all shadow-md",children:"Go to sign in"})})]})})]})})})}):(0,s.jsx)("main",{className:"flex flex-col items-center justify-center min-h-screen w-full",children:(0,s.jsx)("div",{className:"w-full divide-y divide-border",children:(0,s.jsxs)("section",{className:"w-full relative overflow-hidden",children:[(0,s.jsxs)("div",{className:"relative flex flex-col items-center w-full px-6",children:[(0,s.jsx)("div",{className:"absolute inset-x-1/4 top-0 h-[600px] md:h-[800px] -z-20 bg-background rounded-b-xl"}),(0,s.jsxs)("div",{className:"relative z-10 pt-24 pb-8 max-w-md mx-auto h-full w-full flex flex-col gap-2 items-center justify-center",children:[(0,s.jsxs)(n(),{href:"/auth",className:"group border border-border/50 bg-background hover:bg-accent/20 rounded-full text-sm h-8 px-3 flex items-center gap-2 transition-all duration-200 shadow-sm mb-6",children:[(0,s.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"}),(0,s.jsx)("span",{className:"font-medium text-muted-foreground text-xs tracking-wide",children:"Back to sign in"})]}),(0,s.jsx)("h1",{className:"text-3xl md:text-4xl lg:text-5xl font-medium tracking-tighter text-center text-balance text-primary",children:"Reset Password"}),(0,s.jsx)("p",{className:"text-base md:text-lg text-center text-muted-foreground font-medium text-balance leading-relaxed tracking-tight mt-2 mb-6",children:"Create a new password for your account"})]})]}),(0,s.jsx)("div",{className:"relative z-10 flex justify-center px-6 pb-24",children:(0,s.jsxs)("div",{className:"w-full max-w-md rounded-xl bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] border border-border p-8",children:[a&&(0,s.jsxs)("div",{className:"mb-6 p-4 rounded-lg flex items-center gap-3 bg-secondary/10 border border-secondary/20 text-secondary",children:[(0,s.jsx)(c.A,{className:"h-5 w-5 flex-shrink-0 text-secondary"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:a})]}),!a&&(0,s.jsxs)("form",{className:"space-y-4",children:[(0,s.jsx)("div",{children:(0,s.jsx)(u.p,{id:"password",name:"password",type:"password",placeholder:"New password",className:"h-12 rounded-full bg-background border-border",required:!0})}),(0,s.jsx)("div",{children:(0,s.jsx)(u.p,{id:"confirmPassword",name:"confirmPassword",type:"password",placeholder:"Confirm new password",className:"h-12 rounded-full bg-background border-border",required:!0})}),(0,s.jsx)("div",{className:"space-y-4 pt-4",children:(0,s.jsx)(m.SubmitButton,{formAction:x,className:"w-full h-12 rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all shadow-md",pendingText:"Updating password...",children:"Reset Password"})})]}),a&&(0,s.jsx)("div",{className:"mt-6 flex justify-center",children:(0,s.jsx)(n(),{href:"/auth",className:"flex h-12 px-6 items-center justify-center text-center rounded-full bg-primary text-primary-foreground hover:bg-primary/90 transition-all shadow-md",children:"Return to sign in"})})]})})]})})})}function g(){return(0,s.jsx)(d.Suspense,{fallback:(0,s.jsx)("main",{className:"flex flex-col items-center justify-center min-h-screen w-full",children:(0,s.jsx)("div",{className:"w-12 h-12 rounded-full border-4 border-primary border-t-transparent animate-spin"})}),children:(0,s.jsx)(x,{})})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>l,r:()=>d});var s=t(60687);t(43210);var a=t(8730),n=t(24224),i=t(4780);let d=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",node_outline:"bg-transparent border border-primary/10",node_secondary:"px-0 bg-transparent hover:opacity-60",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-lg gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-lg px-6 has-[>svg]:px-4",icon:"size-9",node_secondary:"px-0"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:r,size:t,asChild:n=!1,...l}){let o=n?a.DX:"button";return(0,s.jsx)(o,{"data-slot":"button",className:(0,i.cn)(d({variant:r,size:t,className:e})),...l})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44774:(e,r,t)=>{"use strict";t.d(r,{SubmitButton:()=>c});var s=t(60687),a=t(51215),n=t(43210),i=t(29523),d=t(91821),l=t(43649);let o={message:""};function c({children:e,formAction:r,errorMessage:t,pendingText:c="Submitting...",...u}){let{pending:m,action:p}=(0,a.useFormStatus)(),[f,x]=(0,n.useActionState)(r,o),g=m&&p===x;return(0,s.jsxs)("div",{className:"flex flex-col gap-y-4 w-full",children:[!!(t||f?.message)&&(0,s.jsxs)(d.Fc,{variant:"destructive",className:"w-full",children:[(0,s.jsx)(l.A,{className:"h-4 w-4"}),(0,s.jsx)(d.TN,{children:t||f?.message})]}),(0,s.jsx)("div",{children:(0,s.jsx)(i.$,{...u,type:"submit","aria-disabled":m,formAction:x,children:g?c:e})})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56131:(e,r,t)=>{Promise.resolve().then(t.bind(t,59379))},59379:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\suna\\\\frontend\\\\src\\\\app\\\\auth\\\\reset-password\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\auth\\reset-password\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80354:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.default,__next_app__:()=>c,pages:()=>o,routeModule:()=>u,tree:()=>l});var s=t(65239),a=t(48088),n=t(31369),i=t(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let l={children:["",{children:["auth",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,59379)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\auth\\reset-password\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,88524))).default(e)],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,93595)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[async e=>(await Promise.resolve().then(t.bind(t,88524))).default(e)],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\suna\\frontend\\src\\app\\auth\\reset-password\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/reset-password/page",pathname:"/auth/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},81630:e=>{"use strict";e.exports=require("http")},83112:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"001bd528d1e6d74e3d1c0a606898cfa923e7bbe9fd":()=>u,"604991a10ed242e0f6ea33f223b3055ecb96bfab5b":()=>d,"609c31419be1d42bf9c9b8ca0643e758692237b99d":()=>l,"60a6205911ff9573610eaa1755309d0261975c088b":()=>o,"60fcf04f7f92ed069c46b1be2805385ba3ee83149d":()=>c});var s=t(91199);t(42087);var a=t(5876),n=t(90141);async function i(e,r){try{let t=process.env.ADMIN_API_KEY;if(!t)return void console.error("ADMIN_API_KEY not configured");let s=await fetch("http://localhost:8000/api/api/send-welcome-email",{method:"POST",headers:{"Content-Type":"application/json","X-Admin-Api-Key":t},body:JSON.stringify({email:e,name:r})});if(s.ok)console.log(`Welcome email queued for ${e}`);else{let r=await s.json().catch(()=>({}));console.error(`Failed to queue welcome email for ${e}:`,r)}}catch(e){console.error("Error sending welcome email:",e)}}async function d(e,r){let t=r.get("email"),s=r.get("password"),n=r.get("returnUrl");if(!t||!t.includes("@"))return{message:"Please enter a valid email address"};if(!s||s.length<6)return{message:"Password must be at least 6 characters"};let i=await (0,a.U)(),{error:d}=await i.auth.signInWithPassword({email:t,password:s});return d?{message:d.message||"Could not authenticate user"}:{success:!0,redirectTo:n||"/dashboard"}}async function l(e,r){let t=r.get("origin"),s=r.get("email"),n=r.get("password"),d=r.get("confirmPassword"),l=r.get("returnUrl");if(!s||!s.includes("@"))return{message:"Please enter a valid email address"};if(!n||n.length<6)return{message:"Password must be at least 6 characters"};if(n!==d)return{message:"Passwords do not match"};let o=await (0,a.U)(),{error:c}=await o.auth.signUp({email:s,password:n,options:{emailRedirectTo:`${t}/auth/callback?returnUrl=${l}`}});if(c)return{message:c.message||"Could not create account"};let u=s.split("@")[0].replace(/[._-]/g," ").replace(/\b\w/g,e=>e.toUpperCase()),{error:m,data:p}=await o.auth.signInWithPassword({email:s,password:n});return(p&&i(s,u),m)?{message:"Account created! Check your email to confirm your registration."}:{success:!0,redirectTo:l||"/dashboard"}}async function o(e,r){let t=r.get("email"),s=r.get("origin");if(!t||!t.includes("@"))return{message:"Please enter a valid email address"};let n=await (0,a.U)(),{error:i}=await n.auth.resetPasswordForEmail(t,{redirectTo:`${s}/auth/reset-password`});return i?{message:i.message||"Could not send password reset email"}:{success:!0,message:"Check your email for a password reset link"}}async function c(e,r){let t=r.get("password"),s=r.get("confirmPassword");if(!t||t.length<6)return{message:"Password must be at least 6 characters"};if(t!==s)return{message:"Passwords do not match"};let n=await (0,a.U)(),{error:i}=await n.auth.updateUser({password:t});return i?{message:i.message||"Could not update password"}:{success:!0,message:"Password updated successfully"}}async function u(){let e=await (0,a.U)(),{error:r}=await e.auth.signOut();return r?{message:r.message||"Could not sign out"}:(0,n.redirect)("/")}(0,t(33331).D)([d,l,o,c,u]),(0,s.A)(d,"604991a10ed242e0f6ea33f223b3055ecb96bfab5b",null),(0,s.A)(l,"609c31419be1d42bf9c9b8ca0643e758692237b99d",null),(0,s.A)(o,"60a6205911ff9573610eaa1755309d0261975c088b",null),(0,s.A)(c,"60fcf04f7f92ed069c46b1be2805385ba3ee83149d",null),(0,s.A)(u,"001bd528d1e6d74e3d1c0a606898cfa923e7bbe9fd",null)},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>n});var s=t(60687);t(43210);var a=t(4780);function n({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},91645:e=>{"use strict";e.exports=require("net")},91821:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>d,TN:()=>o,XL:()=>l});var s=t(60687);t(43210);var a=t(24224),n=t(4780);let i=(0,a.F)("relative w-full rounded-xl border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function d({className:e,variant:r,...t}){return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(i({variant:r}),e),...t})}function l({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"alert-title",className:(0,n.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",e),...r})}function o({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...r})}},93613:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,5193,4267,7096,3667],()=>t(80354));module.exports=s})();