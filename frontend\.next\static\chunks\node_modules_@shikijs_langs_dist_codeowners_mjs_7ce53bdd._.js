(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/@shikijs/langs/dist/codeowners.mjs [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
const lang = Object.freeze(JSON.parse("{\"displayName\":\"CODEOWNERS\",\"name\":\"codeowners\",\"patterns\":[{\"include\":\"#comment\"},{\"include\":\"#pattern\"},{\"include\":\"#owner\"}],\"repository\":{\"comment\":{\"patterns\":[{\"begin\":\"^\\\\s*#\",\"captures\":{\"0\":{\"name\":\"punctuation.definition.comment.codeowners\"}},\"end\":\"$\",\"name\":\"comment.line.codeowners\"}]},\"owner\":{\"match\":\"\\\\S*@\\\\S+\",\"name\":\"storage.type.function.codeowners\"},\"pattern\":{\"match\":\"^\\\\s*(\\\\S+)\",\"name\":\"variable.other.codeowners\"}},\"scopeName\":\"text.codeowners\"}"));
const __TURBOPACK__default__export__ = [
    lang
];
}}),
}]);

//# sourceMappingURL=node_modules_%40shikijs_langs_dist_codeowners_mjs_7ce53bdd._.js.map