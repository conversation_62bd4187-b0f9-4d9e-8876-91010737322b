(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4345],{4654:(e,t,r)=>{"use strict";r.d(t,{b:()=>s});var n=r(95155),a=r(59434),l=r(12115);let s=e=>{let{squareSize:t=3,gridGap:r=3,flickerChance:s=.2,color:i="#B4B4B4",width:c,height:o,className:u,maxOpacity:d=.15,text:h="",fontSize:f=140,fontWeight:m=600,...x}=e,g=(0,l.useRef)(null),b=(0,l.useRef)(null),p=(0,l.useRef)(0),v=(0,l.useRef)(0),w=(0,l.useRef)(0),y=(0,l.useRef)(null),[k,j]=(0,l.useState)(!1),[N,z]=(0,l.useState)({width:0,height:0}),A=(0,l.useMemo)(()=>(0,a.Hz)(i),[i]),R=(0,l.useCallback)((e,n,l,s,i,c,o)=>{e.clearRect(0,0,n,l);let u=null,d=null;h&&((u=document.createElement("canvas")).width=n,u.height=l,(d=u.getContext("2d",{willReadFrequently:!0}))&&(d.save(),d.scale(o,o),d.fillStyle="white",d.font="".concat(m," ").concat(f,'px "Geist", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif'),d.textAlign="center",d.textBaseline="middle",d.fillText(h,n/(2*o),l/(2*o)),d.restore()));let x=new Map;for(let e=0;e<s;e++)for(let n=0;n<i;n++){var g;let a=e*(t+r)*o,l=n*(t+r)*o,s=t*o,h=t*o,f=!1;d&&u&&(f=d.getImageData(a,l,s,h).data.some((e,t)=>t%4==0&&e>0));let m=c[e*i+n],b=Math.round(100*(f?Math.min(1,3*m+.4):m))/100;x.has(b)||x.set(b,[]),null==(g=x.get(b))||g.push({x:a,y:l})}for(let[r,n]of x.entries())for(let{x:l,y:s}of(e.fillStyle=(0,a.$3)(A,r),n))e.fillRect(l,s,t*o,t*o)},[A,t,r,h,f,m]),M=(0,l.useCallback)((e,n,a)=>{let l=window.devicePixelRatio||1;e.width=n*l,e.height=a*l,e.style.width="".concat(n,"px"),e.style.height="".concat(a,"px");let s=Math.ceil(n/(t+r)),i=Math.ceil(a/(t+r));if(y.current&&y.current.cols===s&&y.current.rows===i)return{cols:s,rows:i,squares:y.current.squares,dpr:l};let c=new Float32Array(s*i);for(let e=0;e<c.length;e++)c[e]=Math.random()*d;return{cols:s,rows:i,squares:c,dpr:l}},[t,r,d]),C=(0,l.useCallback)((e,t)=>{if(!(s<=0))for(let r=0;r<e.length;r++)Math.random()<s*t&&(e[r]=Math.random()*d)},[s,d]);return(0,l.useEffect)(()=>{let e=g.current,n=b.current;if(!e||!n)return;let a=e.getContext("2d",{alpha:!0});if(!a)return;let l=()=>{let a=performance.now();if(a-w.current<200)return;w.current=a;let l=c||n.clientWidth,s=o||n.clientHeight;if(N.width!==l||N.height!==s)if(z({width:l,height:s}),y.current&&10>Math.abs(y.current.cols*(t+r)-l)&&10>Math.abs(y.current.rows*(t+r)-s)){let t=window.devicePixelRatio||1;e.width=l*t,e.height=s*t,e.style.width="".concat(l,"px"),e.style.height="".concat(s,"px")}else y.current=M(e,l,s)};y.current||l();let s=0,i=t=>{if(!k||t-v.current<50){p.current=requestAnimationFrame(i);return}if(!y.current||!y.current.squares){l(),p.current=requestAnimationFrame(i);return}v.current=t;let r=(t-s)/1e3;s=t,C(y.current.squares,r),R(a,e.width,e.height,y.current.cols,y.current.rows,y.current.squares,y.current.dpr),p.current=requestAnimationFrame(i)},u=new ResizeObserver(()=>{if(performance.now()-w.current<200)return;let e=c||n.clientWidth,t=o||n.clientHeight;(Math.abs(N.width-e)>5||Math.abs(N.height-t)>5)&&l()});u.observe(n);let d=new IntersectionObserver(e=>{let[t]=e;j(t.isIntersecting)},{threshold:.1,rootMargin:"50px"});return d.observe(e),k&&(p.current=requestAnimationFrame(i)),()=>{cancelAnimationFrame(p.current),u.disconnect(),d.disconnect()}},[M,C,R,c,o,k,t,r]),(0,n.jsx)("div",{ref:b,className:(0,a.cn)("h-full w-full ".concat(u)),...x,children:(0,n.jsx)("canvas",{ref:g,className:"pointer-events-none",style:{width:N.width,height:N.height}})})}},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var n=r(12115);let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:a=24,strokeWidth:i=2,absoluteStrokeWidth:c,className:o="",children:u,iconNode:d,...h}=e;return(0,n.createElement)("svg",{ref:t,...s,width:a,height:a,stroke:r,strokeWidth:c?24*Number(i)/Number(a):i,className:l("lucide",o),...h},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),c=(e,t)=>{let r=(0,n.forwardRef)((r,s)=>{let{className:c,...o}=r;return(0,n.createElement)(i,{ref:s,iconNode:t,className:l("lucide-".concat(a(e)),c),...o})});return r.displayName="".concat(e),r}},33237:(e,t,r)=>{Promise.resolve().then(r.bind(r,99543))},35169:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},38095:(e,t,r)=>{"use strict";r.d(t,{U:()=>a});var n=r(12115);function a(e){let[t,r]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{let n=window.matchMedia(e);n.matches!==t&&r(n.matches);let a=()=>r(n.matches);return n.addEventListener("change",a),()=>n.removeEventListener("change",a)},[t,e]),t}},59434:(e,t,r)=>{"use strict";r.d(t,{$3:()=>c,Hz:()=>i,W5:()=>o,cn:()=>s});var n=r(52596),a=r(81949),l=r(39688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,l.QP)((0,n.$)(t))}let i=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"rgba(180, 180, 180)";if(!e)return t;try{if("string"==typeof e&&e.startsWith("var(")){let t=document.createElement("div");t.style.color=e,document.body.appendChild(t);let r=window.getComputedStyle(t).color;return document.body.removeChild(t),a.formatRGBA(a.parse(r))}return a.formatRGBA(a.parse(e))}catch(e){return console.error("Color parsing failed:",e),t}},c=(e,t)=>e.startsWith("rgb")?a.formatRGBA(a.alpha(a.parse(e),t)):e;function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50;return e.length<=t?e:e.slice(0,t)+"..."}},99543:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var n=r(95155),a=r(6874),l=r.n(a),s=r(12115),i=r(35169),c=r(95360),o=r(4654),u=r(38095);function d(){let e=(0,u.U)("(max-width: 1024px)"),[t,r]=(0,s.useState)(!1),[a,d]=(0,s.useState)(!1),h=(0,s.useRef)(null),{scrollY:f}=(0,c.L)();return(0,s.useEffect)(()=>{r(!0)},[]),(0,s.useEffect)(()=>{let e=f.on("change",()=>{d(!0),h.current&&clearTimeout(h.current),h.current=setTimeout(()=>{d(!1)},300)});return()=>{e(),h.current&&clearTimeout(h.current)}},[f]),(0,n.jsx)("section",{className:"w-full relative overflow-hidden min-h-screen flex items-center justify-center",children:(0,n.jsxs)("div",{className:"relative flex flex-col items-center w-full px-6",children:[(0,n.jsxs)("div",{className:"absolute left-0 top-0 h-full w-1/3 -z-10 overflow-hidden",children:[(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-transparent to-background z-10"}),(0,n.jsx)("div",{className:"absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10"}),(0,n.jsx)("div",{className:"absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10"}),(0,n.jsx)(o.b,{className:"h-full w-full",squareSize:t&&e?2:2.5,gridGap:t&&e?2:2.5,color:"var(--secondary)",maxOpacity:.4,flickerChance:a?.01:.03})]}),(0,n.jsxs)("div",{className:"absolute right-0 top-0 h-full w-1/3 -z-10 overflow-hidden",children:[(0,n.jsx)("div",{className:"absolute inset-0 bg-gradient-to-l from-transparent via-transparent to-background z-10"}),(0,n.jsx)("div",{className:"absolute inset-x-0 top-0 h-32 bg-gradient-to-b from-background via-background/90 to-transparent z-10"}),(0,n.jsx)("div",{className:"absolute inset-x-0 bottom-0 h-48 bg-gradient-to-t from-background via-background/90 to-transparent z-10"}),(0,n.jsx)(o.b,{className:"h-full w-full",squareSize:t&&e?2:2.5,gridGap:t&&e?2:2.5,color:"var(--secondary)",maxOpacity:.4,flickerChance:a?.01:.03})]}),(0,n.jsx)("div",{className:"absolute inset-x-1/4 top-0 h-full -z-20 bg-background rounded-b-xl"}),(0,n.jsxs)("div",{className:"relative z-10 max-w-3xl mx-auto h-full w-full flex flex-col gap-10 items-center justify-center",children:[(0,n.jsx)("div",{className:"inline-flex h-10 w-fit items-center justify-center gap-2 rounded-full bg-secondary/10 text-secondary px-4",children:(0,n.jsx)("span",{className:"text-sm font-medium",children:"404 Error"})}),(0,n.jsxs)("div",{className:"flex flex-col items-center justify-center gap-5",children:[(0,n.jsx)("h1",{className:"text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-medium tracking-tighter text-balance text-center text-primary",children:"Page not found"}),(0,n.jsx)("p",{className:"text-base md:text-lg text-center text-muted-foreground font-medium text-balance leading-relaxed tracking-tight",children:"The page you're looking for doesn't exist or has been moved."})]}),(0,n.jsx)("div",{className:"flex items-center w-full max-w-xl gap-2 flex-wrap justify-center",children:(0,n.jsxs)(l(),{href:"/",className:"inline-flex h-12 md:h-14 items-center justify-center gap-2 rounded-full bg-primary text-white px-6 shadow-md hover:bg-primary/90 transition-all duration-200",children:[(0,n.jsx)(i.A,{className:"size-4 md:size-5 dark:text-black"}),(0,n.jsx)("span",{className:"font-medium dark:text-black",children:"Return Home"})]})}),(0,n.jsx)("div",{className:"absolute -bottom-4 inset-x-0 h-6 bg-secondary/20 blur-xl rounded-full -z-10 opacity-70"})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[2969,6874,7453,5360,8441,1684,7358],()=>t(33237)),_N_E=e.O()}]);