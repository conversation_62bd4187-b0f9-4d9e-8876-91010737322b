(()=>{var e={};e.id=8299,e.ids=[8299],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34296:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var n={};t.r(n),t.d(n,{POST:()=>d});var s=t(96559),o=t(48088),a=t(37719);let i=new URL(process.env.NEXT_PUBLIC_SENTRY_DSN??"https://example.com/abc"),p=i.hostname,u=i.pathname.split("/").pop(),d=async e=>{try{if(!process.env.NEXT_PUBLIC_SENTRY_DSN)return Response.json({error:"Sentry is not configured"},{status:500});let r=await e.arrayBuffer(),t=new TextDecoder().decode(r).split("\n")[0],n=JSON.parse(t),s=new URL(n.dsn),o=s.pathname.replace("/","");if(s.hostname!==p)throw Error(`Invalid sentry hostname: ${s.hostname}`);if(o!==u)throw Error(`Invalid sentry project id: ${o}`);let a=`https://${p}/api/${o}/envelope/`;return await fetch(a,{body:r,method:"POST"})}catch(e){return console.error("error tunneling to sentry",e),Response.json({error:"error tunneling to sentry"},{status:500})}},c=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/monitoring/route",pathname:"/monitoring",filename:"route",bundlePath:"app/monitoring/route"},resolvedPagePath:"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\monitoring\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:h}=c;function x(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{},96559:(e,r,t)=>{"use strict";e.exports=t(44870)}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[7719],()=>t(34296));module.exports=n})();