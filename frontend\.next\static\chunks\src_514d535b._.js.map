{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from 'clsx';\r\nimport * as Color from 'color-bits';\r\nimport { twMerge } from 'tailwind-merge';\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs));\r\n}\r\n\r\n// Helper function to convert any CSS color to rgba\r\nexport const getRGBA = (\r\n  cssColor: React.CSSProperties['color'],\r\n  fallback: string = 'rgba(180, 180, 180)',\r\n): string => {\r\n  if (typeof window === 'undefined') return fallback;\r\n  if (!cssColor) return fallback;\r\n\r\n  try {\r\n    // Handle CSS variables\r\n    if (typeof cssColor === 'string' && cssColor.startsWith('var(')) {\r\n      const element = document.createElement('div');\r\n      element.style.color = cssColor;\r\n      document.body.appendChild(element);\r\n      const computedColor = window.getComputedStyle(element).color;\r\n      document.body.removeChild(element);\r\n      return Color.formatRGBA(Color.parse(computedColor));\r\n    }\r\n\r\n    return Color.formatRGBA(Color.parse(cssColor));\r\n  } catch (e) {\r\n    console.error('Color parsing failed:', e);\r\n    return fallback;\r\n  }\r\n};\r\n\r\n// Helper function to add opacity to an RGB color string\r\nexport const colorWithOpacity = (color: string, opacity: number): string => {\r\n  if (!color.startsWith('rgb')) return color;\r\n  return Color.formatRGBA(Color.alpha(Color.parse(color), opacity));\r\n};\r\n\r\n// Tremor Raw focusInput [v0.0.1]\r\n\r\nexport const focusInput = [\r\n  // base\r\n  'focus:ring-2',\r\n  // ring color\r\n  'focus:ring-blue-200 focus:dark:ring-blue-700/30',\r\n  // border color\r\n  'focus:border-blue-500 focus:dark:border-blue-700',\r\n];\r\n\r\n// Tremor Raw focusRing [v0.0.1]\r\n\r\nexport const focusRing = [\r\n  // base\r\n  'outline outline-offset-2 outline-0 focus-visible:outline-2',\r\n  // outline color\r\n  'outline-blue-500 dark:outline-blue-500',\r\n];\r\n\r\n// Tremor Raw hasErrorInput [v0.0.1]\r\n\r\nexport const hasErrorInput = [\r\n  // base\r\n  'ring-2',\r\n  // border color\r\n  'border-red-500 dark:border-red-700',\r\n  // ring color\r\n  'ring-red-200 dark:ring-red-700/30',\r\n];\r\n\r\nexport function truncateString(str: string, maxLength = 50) {\r\n  if (str.length <= maxLength) return str;\r\n  return str.slice(0, maxLength) + '...';\r\n}\r\n\r\n\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;AACA;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,MAAM,UAAU,CACrB,UACA,WAAmB,qBAAqB;IAExC,uCAAmC;;IAAe;IAClD,IAAI,CAAC,UAAU,OAAO;IAEtB,IAAI;QACF,uBAAuB;QACvB,IAAI,OAAO,aAAa,YAAY,SAAS,UAAU,CAAC,SAAS;YAC/D,MAAM,UAAU,SAAS,aAAa,CAAC;YACvC,QAAQ,KAAK,CAAC,KAAK,GAAG;YACtB,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,MAAM,gBAAgB,OAAO,gBAAgB,CAAC,SAAS,KAAK;YAC5D,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO,CAAA,GAAA,kJAAA,CAAA,aAAgB,AAAD,EAAE,CAAA,GAAA,kJAAA,CAAA,QAAW,AAAD,EAAE;QACtC;QAEA,OAAO,CAAA,GAAA,kJAAA,CAAA,aAAgB,AAAD,EAAE,CAAA,GAAA,kJAAA,CAAA,QAAW,AAAD,EAAE;IACtC,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO;IACT;AACF;AAGO,MAAM,mBAAmB,CAAC,OAAe;IAC9C,IAAI,CAAC,MAAM,UAAU,CAAC,QAAQ,OAAO;IACrC,OAAO,CAAA,GAAA,kJAAA,CAAA,aAAgB,AAAD,EAAE,CAAA,GAAA,kJAAA,CAAA,QAAW,AAAD,EAAE,CAAA,GAAA,kJAAA,CAAA,QAAW,AAAD,EAAE,QAAQ;AAC1D;AAIO,MAAM,aAAa;IACxB,OAAO;IACP;IACA,aAAa;IACb;IACA,eAAe;IACf;CACD;AAIM,MAAM,YAAY;IACvB,OAAO;IACP;IACA,gBAAgB;IAChB;CACD;AAIM,MAAM,gBAAgB;IAC3B,OAAO;IACP;IACA,eAAe;IACf;IACA,aAAa;IACb;CACD;AAEM,SAAS,eAAe,GAAW,EAAE,YAAY,EAAE;IACxD,IAAI,IAAI,MAAM,IAAI,WAAW,OAAO;IACpC,OAAO,IAAI,KAAK,CAAC,GAAG,aAAa;AACnC", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>"], "sourcesContent": ["import { cn } from '@/lib/utils';\r\n\r\nexport const Icons = {\r\n  logo: ({ className }: { className?: string }) => (\r\n    <svg\r\n      width=\"42\"\r\n      height=\"24\"\r\n      viewBox=\"0 0 42 24\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      className={cn('size-4 fill-[var(--secondary)]', className)}\r\n    >\r\n      <g clipPath=\"url(#clip0_322_9172)\">\r\n        <path\r\n          d=\"M22.3546 0.96832C22.9097 0.390834 23.6636 0.0664062 24.4487 0.0664062C27.9806 0.0664062 31.3091 0.066408 34.587 0.0664146C41.1797 0.0664284 44.481 8.35854 39.8193 13.2082L29.6649 23.7718C29.1987 24.2568 28.4016 23.9133 28.4016 23.2274V13.9234L29.5751 12.7025C30.5075 11.7326 29.8472 10.0742 28.5286 10.0742H13.6016L22.3546 0.96832Z\"\r\n          fill=\"current\"\r\n        />\r\n        <path\r\n          d=\"M19.6469 23.0305C19.0919 23.608 18.338 23.9324 17.5529 23.9324C14.021 23.9324 10.6925 23.9324 7.41462 23.9324C0.821896 23.9324 -2.47942 15.6403 2.18232 10.7906L12.3367 0.227022C12.8029 -0.257945 13.6 0.0855283 13.6 0.771372L13.6 10.0754L12.4265 11.2963C11.4941 12.2662 12.1544 13.9246 13.473 13.9246L28.4001 13.9246L19.6469 23.0305Z\"\r\n          fill=\"current\"\r\n        />\r\n      </g>\r\n      <defs>\r\n        <clipPath id=\"clip0_322_9172\">\r\n          <rect width=\"42\" height=\"24\" fill=\"white\" />\r\n        </clipPath>\r\n      </defs>\r\n    </svg>\r\n  ),\r\n  soc2: ({ className }: { className?: string }) => (\r\n    <svg\r\n      width=\"46\"\r\n      height=\"45\"\r\n      viewBox=\"0 0 46 45\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      className={cn('size-4', className)}\r\n    >\r\n      <g filter=\"url(#filter0_ddd_1_4900)\">\r\n        <rect\r\n          x=\"3\"\r\n          y=\"0.863281\"\r\n          width=\"40\"\r\n          height=\"40\"\r\n          rx=\"20\"\r\n          fill=\"url(#paint0_linear_1_4900)\"\r\n        />\r\n        <g filter=\"url(#filter1_d_1_4900)\">\r\n          <rect\r\n            x=\"6.15784\"\r\n            y=\"4.021\"\r\n            width=\"33.6842\"\r\n            height=\"33.6842\"\r\n            rx=\"16.8421\"\r\n            fill=\"url(#paint1_linear_1_4900)\"\r\n          />\r\n          <path\r\n            d=\"M15.0475 29.6233C13.7506 29.6233 12.9548 28.8938 12.8738 27.8033L13.8464 27.7443C13.9348 28.4222 14.3401 28.798 15.0622 28.798C15.6812 28.798 16.0348 28.5696 16.0348 28.1201C16.0348 27.7148 15.8285 27.4717 14.7601 27.2212C13.4633 26.9264 12.977 26.558 12.977 25.7033C12.977 24.7896 13.6917 24.1559 14.8633 24.1559C16.1159 24.1559 16.8012 24.8854 16.9191 25.8948L15.9538 25.9391C15.8875 25.3717 15.5117 24.9812 14.8485 24.9812C14.2959 24.9812 13.957 25.2612 13.957 25.6664C13.957 26.0938 14.2001 26.2559 15.1359 26.4696C16.5433 26.7717 17.0148 27.2875 17.0148 28.0685C17.0148 29.0264 16.2338 29.6233 15.0475 29.6233ZM19.9915 29.6233C18.4367 29.6233 17.5009 28.5843 17.5009 26.897C17.5009 25.2096 18.4367 24.1559 19.9915 24.1559C21.5536 24.1559 22.4894 25.2096 22.4894 26.897C22.4894 28.5843 21.5536 29.6233 19.9915 29.6233ZM19.9915 28.7906C20.942 28.7906 21.502 28.0906 21.502 26.897C21.502 25.7033 20.942 24.9885 19.9915 24.9885C19.0557 24.9885 18.4883 25.7033 18.4883 26.897C18.4883 28.0906 19.0557 28.7906 19.9915 28.7906ZM25.324 29.6233C23.8945 29.6233 22.8997 28.6064 22.8997 26.897C22.8997 25.2169 23.865 24.1559 25.3313 24.1559C26.665 24.1559 27.3797 24.8559 27.6082 26.0422L26.6061 26.0938C26.4734 25.4085 26.0534 24.9885 25.3313 24.9885C24.4397 24.9885 23.8871 25.7327 23.8871 26.897C23.8871 28.0759 24.4545 28.7906 25.324 28.7906C26.105 28.7906 26.5176 28.3412 26.6355 27.5896L27.6376 27.6412C27.4313 28.8717 26.6429 29.6233 25.324 29.6233ZM29.6489 29.5054C29.6489 28.238 30.1205 27.5085 31.5573 26.7569C32.2721 26.3812 32.53 26.1748 32.53 25.7327C32.53 25.298 32.2426 24.9885 31.6826 24.9885C31.0858 24.9885 30.7321 25.3348 30.651 25.9685L29.6637 25.9096C29.7668 24.8191 30.4889 24.1559 31.6826 24.1559C32.8395 24.1559 33.5173 24.7896 33.5173 25.718C33.5173 26.5212 33.1416 26.897 32.1173 27.4422C31.2479 27.9064 30.8279 28.3485 30.7984 28.6727H33.5173V29.5054H29.6489Z\"\r\n            fill=\"#101828\"\r\n          />\r\n          <path\r\n            d=\"M13.0537 17.8882L14.9621 12.6566H15.6253L17.5263 17.8882H16.9811L16.4211 16.3187H14.159L13.599 17.8882H13.0537ZM14.3285 15.8324H16.2516L15.2937 13.1061L14.3285 15.8324ZM18.026 17.8882V12.6566H18.5271V17.8882H18.026ZM21.5495 18.0061C20.1642 18.0061 19.2506 16.9745 19.2506 15.2798C19.2506 13.585 20.1642 12.5387 21.5495 12.5387C22.7727 12.5387 23.4506 13.2387 23.6642 14.3292L23.1337 14.3661C22.9863 13.5482 22.4632 13.0324 21.5495 13.0324C20.4811 13.0324 19.7737 13.8798 19.7737 15.2798C19.7737 16.6798 20.4811 17.5124 21.5495 17.5124C22.5074 17.5124 23.0453 16.9598 23.1779 16.0608L23.7085 16.0977C23.5242 17.2471 22.7727 18.0061 21.5495 18.0061ZM24.5062 17.8882V12.6566H26.3409C27.4904 12.6566 28.1683 13.2461 28.1683 14.2187C28.1683 15.1913 27.4904 15.7808 26.3409 15.7808H25.0072V17.8882H24.5062ZM25.0072 15.2945H26.3409C27.1957 15.2945 27.6378 14.9187 27.6378 14.2187C27.6378 13.5113 27.1957 13.1429 26.3409 13.1429H25.0072V15.2945ZM27.9425 17.8882L29.851 12.6566H30.5141L32.4152 17.8882H31.8699L31.3099 16.3187H29.0478L28.4878 17.8882H27.9425ZM29.2173 15.8324H31.1404L30.1825 13.1061L29.2173 15.8324Z\"\r\n            fill=\"#6A7282\"\r\n          />\r\n          <line\r\n            x1=\"10.4938\"\r\n            y1=\"21.2488\"\r\n            x2=\"34.988\"\r\n            y2=\"21.2488\"\r\n            stroke=\"#E5E7EB\"\r\n            strokeWidth=\"0.263158\"\r\n          />\r\n        </g>\r\n      </g>\r\n      <defs>\r\n        <filter\r\n          id=\"filter0_ddd_1_4900\"\r\n          x=\"0.857143\"\r\n          y=\"0.148996\"\r\n          width=\"44.2857\"\r\n          height=\"44.2857\"\r\n          filterUnits=\"userSpaceOnUse\"\r\n          colorInterpolationFilters=\"sRGB\"\r\n        >\r\n          <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feMorphology\r\n            radius=\"0.714286\"\r\n            operator=\"erode\"\r\n            in=\"SourceAlpha\"\r\n            result=\"effect1_dropShadow_1_4900\"\r\n          />\r\n          <feOffset dy=\"0.714286\" />\r\n          <feGaussianBlur stdDeviation=\"0.357143\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"BackgroundImageFix\"\r\n            result=\"effect1_dropShadow_1_4900\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feMorphology\r\n            radius=\"0.714286\"\r\n            operator=\"erode\"\r\n            in=\"SourceAlpha\"\r\n            result=\"effect2_dropShadow_1_4900\"\r\n          />\r\n          <feOffset dy=\"1.42857\" />\r\n          <feGaussianBlur stdDeviation=\"1.42857\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect1_dropShadow_1_4900\"\r\n            result=\"effect2_dropShadow_1_4900\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feMorphology\r\n            radius=\"0.714286\"\r\n            operator=\"dilate\"\r\n            in=\"SourceAlpha\"\r\n            result=\"effect3_dropShadow_1_4900\"\r\n          />\r\n          <feOffset />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.08 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect2_dropShadow_1_4900\"\r\n            result=\"effect3_dropShadow_1_4900\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in=\"SourceGraphic\"\r\n            in2=\"effect3_dropShadow_1_4900\"\r\n            result=\"shape\"\r\n          />\r\n        </filter>\r\n        <filter\r\n          id=\"filter1_d_1_4900\"\r\n          x=\"5.44355\"\r\n          y=\"3.30671\"\r\n          width=\"35.1128\"\r\n          height=\"35.1127\"\r\n          filterUnits=\"userSpaceOnUse\"\r\n          colorInterpolationFilters=\"sRGB\"\r\n        >\r\n          <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feMorphology\r\n            radius=\"0.714286\"\r\n            operator=\"dilate\"\r\n            in=\"SourceAlpha\"\r\n            result=\"effect1_dropShadow_1_4900\"\r\n          />\r\n          <feOffset />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.08 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"BackgroundImageFix\"\r\n            result=\"effect1_dropShadow_1_4900\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in=\"SourceGraphic\"\r\n            in2=\"effect1_dropShadow_1_4900\"\r\n            result=\"shape\"\r\n          />\r\n        </filter>\r\n        <linearGradient\r\n          id=\"paint0_linear_1_4900\"\r\n          x1=\"9.88803\"\r\n          y1=\"6.55415\"\r\n          x2=\"36.0447\"\r\n          y2=\"35.5773\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop stopColor=\"#F9FAFB\" />\r\n          <stop offset=\"1\" stopColor=\"#E5E7EB\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint1_linear_1_4900\"\r\n          x1=\"11.9583\"\r\n          y1=\"8.8133\"\r\n          x2=\"33.9849\"\r\n          y2=\"33.2538\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop stopColor=\"#E5E7EB\" />\r\n          <stop offset=\"1\" stopColor=\"#F9FAFB\" />\r\n        </linearGradient>\r\n      </defs>\r\n    </svg>\r\n  ),\r\n  soc2Dark: ({ className }: { className?: string }) => (\r\n    <svg\r\n      width=\"46\"\r\n      height=\"45\"\r\n      viewBox=\"0 0 46 45\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      className={cn('size-4', className)}\r\n    >\r\n      <g filter=\"url(#filter0_ddd_1_2018)\">\r\n        <rect\r\n          x=\"3\"\r\n          y=\"0.863281\"\r\n          width=\"40\"\r\n          height=\"40\"\r\n          rx=\"20\"\r\n          fill=\"url(#paint0_linear_1_2018)\"\r\n        />\r\n        <g filter=\"url(#filter1_d_1_2018)\">\r\n          <rect\r\n            x=\"6.1579\"\r\n            y=\"4.021\"\r\n            width=\"33.6842\"\r\n            height=\"33.6842\"\r\n            rx=\"16.8421\"\r\n            fill=\"url(#paint1_linear_1_2018)\"\r\n          />\r\n          <path\r\n            d=\"M15.0441 29.6233C14.6118 29.6233 14.2385 29.5496 13.9241 29.4022C13.6097 29.2499 13.3617 29.0362 13.1799 28.7612C12.9982 28.4861 12.8925 28.1668 12.8631 27.8033L13.8357 27.7443C13.8701 27.9752 13.9364 28.1692 14.0346 28.3264C14.1329 28.4787 14.2655 28.5966 14.4325 28.6801C14.6045 28.7587 14.8132 28.798 15.0589 28.798C15.3683 28.798 15.6066 28.7415 15.7736 28.6285C15.9455 28.5106 16.0315 28.3412 16.0315 28.1201C16.0315 27.9777 15.9971 27.8573 15.9283 27.7591C15.8596 27.6559 15.7343 27.5626 15.5525 27.4791C15.3708 27.3906 15.1055 27.3047 14.7567 27.2212C14.3097 27.118 13.9585 27.005 13.7031 26.8822C13.4476 26.7545 13.261 26.5973 13.1431 26.4106C13.0301 26.224 12.9736 25.9882 12.9736 25.7033C12.9736 25.3987 13.0473 25.131 13.1946 24.9001C13.3469 24.6643 13.5655 24.4826 13.8504 24.3548C14.1353 24.2222 14.4718 24.1559 14.8599 24.1559C15.2627 24.1559 15.6115 24.2296 15.9062 24.3769C16.201 24.5243 16.4318 24.7282 16.5989 24.9885C16.7659 25.2489 16.869 25.551 16.9083 25.8948L15.9431 25.9391C15.9234 25.7475 15.8669 25.5805 15.7736 25.438C15.6803 25.2906 15.555 25.1777 15.3978 25.0991C15.2455 25.0205 15.0613 24.9812 14.8452 24.9812C14.5701 24.9812 14.3515 25.045 14.1894 25.1727C14.0273 25.2955 13.9462 25.4601 13.9462 25.6664C13.9462 25.8089 13.9806 25.9268 14.0494 26.0201C14.1182 26.1134 14.2336 26.1945 14.3957 26.2633C14.5627 26.3271 14.8059 26.3959 15.1252 26.4696C15.5869 26.5678 15.9553 26.6931 16.2304 26.8454C16.5055 26.9927 16.702 27.1671 16.8199 27.3685C16.9427 27.565 17.0041 27.7984 17.0041 28.0685C17.0041 28.3829 16.9231 28.658 16.761 28.8938C16.5989 29.1247 16.368 29.304 16.0683 29.4317C15.7736 29.5594 15.4322 29.6233 15.0441 29.6233ZM19.9881 29.6233C19.4723 29.6233 19.0277 29.5152 18.6544 29.2991C18.2811 29.078 17.9937 28.7636 17.7923 28.3559C17.5909 27.9433 17.4902 27.4569 17.4902 26.897C17.4902 26.3369 17.5909 25.8506 17.7923 25.438C17.9937 25.0254 18.2811 24.7085 18.6544 24.4875C19.0277 24.2664 19.4723 24.1559 19.9881 24.1559C20.5039 24.1559 20.9484 24.2664 21.3218 24.4875C21.7 24.7085 21.9874 25.0254 22.1839 25.438C22.3853 25.8506 22.486 26.3369 22.486 26.897C22.486 27.4569 22.3853 27.9433 22.1839 28.3559C21.9874 28.7636 21.7 29.078 21.3218 29.2991C20.9484 29.5152 20.5039 29.6233 19.9881 29.6233ZM19.9881 28.7906C20.3025 28.7906 20.5727 28.717 20.7986 28.5696C21.0246 28.4173 21.1965 28.1987 21.3144 27.9138C21.4323 27.6289 21.4913 27.2899 21.4913 26.897C21.4913 26.4991 21.4323 26.1577 21.3144 25.8727C21.1965 25.5878 21.0246 25.3692 20.7986 25.2169C20.5727 25.0647 20.3025 24.9885 19.9881 24.9885C19.6737 24.9885 19.4035 25.0647 19.1776 25.2169C18.9565 25.3692 18.7846 25.5878 18.6618 25.8727C18.5439 26.1577 18.4849 26.4991 18.4849 26.897C18.4849 27.2899 18.5439 27.6289 18.6618 27.9138C18.7846 28.1987 18.9565 28.4173 19.1776 28.5696C19.4035 28.717 19.6737 28.7906 19.9881 28.7906ZM25.3276 29.6233C24.8511 29.6233 24.4311 29.5152 24.0676 29.2991C23.7041 29.078 23.4192 28.7612 23.2129 28.3485C23.0066 27.9359 22.9034 27.452 22.9034 26.897C22.9034 26.3468 23.0041 25.8654 23.2055 25.4527C23.4069 25.0352 23.6894 24.7159 24.0529 24.4948C24.4213 24.2689 24.8511 24.1559 25.3423 24.1559C25.9908 24.1559 26.5016 24.318 26.875 24.6422C27.2532 24.9664 27.4988 25.4331 27.6118 26.0422L26.6097 26.0938C26.5459 25.745 26.4059 25.4748 26.1897 25.2833C25.9785 25.0868 25.696 24.9885 25.3423 24.9885C25.0476 24.9885 24.7897 25.0671 24.5687 25.2243C24.3525 25.3766 24.1855 25.5977 24.0676 25.8875C23.9546 26.1724 23.8981 26.5089 23.8981 26.897C23.8981 27.285 23.9571 27.6215 24.075 27.9064C24.1929 28.1913 24.3599 28.4099 24.576 28.5622C24.7922 28.7145 25.0452 28.7906 25.335 28.7906C25.7132 28.7906 26.0104 28.6875 26.2266 28.4812C26.4427 28.2699 26.5802 27.9727 26.6392 27.5896L27.6413 27.6412C27.5381 28.2699 27.2876 28.7587 26.8897 29.1075C26.4967 29.4513 25.976 29.6233 25.3276 29.6233ZM29.6598 29.5054C29.6598 29.078 29.7187 28.7071 29.8366 28.3927C29.9594 28.0734 30.1584 27.7836 30.4335 27.5233C30.7086 27.2629 31.0868 27.0075 31.5682 26.7569C31.8236 26.6194 32.0177 26.504 32.1503 26.4106C32.2879 26.3124 32.3861 26.2117 32.445 26.1085C32.5089 26.0054 32.5408 25.8801 32.5408 25.7327C32.5408 25.5068 32.4671 25.3275 32.3198 25.1948C32.1724 25.0573 31.9636 24.9885 31.6935 24.9885C31.3987 24.9885 31.1629 25.072 30.9861 25.2391C30.8093 25.4061 30.7012 25.6492 30.6619 25.9685L29.6745 25.9096C29.7236 25.3594 29.9226 24.9296 30.2714 24.6201C30.625 24.3106 31.0991 24.1559 31.6935 24.1559C32.0717 24.1559 32.3984 24.2222 32.6735 24.3548C32.9535 24.4826 33.1647 24.6643 33.3071 24.9001C33.4545 25.1359 33.5282 25.4085 33.5282 25.718C33.5282 25.9882 33.4815 26.2166 33.3882 26.4033C33.2998 26.5899 33.1573 26.7619 32.9608 26.9191C32.7693 27.0762 32.4917 27.2506 32.1282 27.4422C31.7057 27.6682 31.384 27.8892 31.1629 28.1054C30.9419 28.3166 30.824 28.5057 30.8093 28.6727H33.5282V29.5054H29.6598Z\"\r\n            fill=\"#F4F4F5\"\r\n          />\r\n          <path\r\n            d=\"M14.883 12.6566H15.5462L17.4546 17.8882H16.9094L16.3494 16.3187H14.0873L13.5273 17.8882H12.982L14.883 12.6566ZM16.1799 15.8324L15.2146 13.1061L14.2567 15.8324H16.1799ZM18.0764 12.6566H18.5775V17.8882H18.0764V12.6566ZM21.6147 18.0061C21.1578 18.0061 20.755 17.898 20.4062 17.6819C20.0624 17.4608 19.7947 17.144 19.6031 16.7313C19.4115 16.3187 19.3157 15.8349 19.3157 15.2798C19.3157 14.7247 19.4115 14.2408 19.6031 13.8282C19.7947 13.4106 20.0624 13.0913 20.4062 12.8703C20.755 12.6492 21.1578 12.5387 21.6147 12.5387C22.2091 12.5387 22.6831 12.6959 23.0368 13.0103C23.3905 13.3247 23.6238 13.7643 23.7368 14.3292L23.2062 14.3661C23.1277 13.9485 22.9533 13.6219 22.6831 13.3861C22.4178 13.1503 22.0617 13.0324 21.6147 13.0324C21.261 13.0324 20.9491 13.1233 20.6789 13.305C20.4136 13.4819 20.2073 13.7398 20.0599 14.0787C19.9175 14.4177 19.8462 14.818 19.8462 15.2798C19.8462 15.7415 19.9175 16.1419 20.0599 16.4808C20.2073 16.8149 20.4136 17.0703 20.6789 17.2471C20.9491 17.424 21.261 17.5124 21.6147 17.5124C22.0862 17.5124 22.4596 17.3871 22.7347 17.1366C23.0098 16.8812 23.1817 16.5226 23.2505 16.0608L23.781 16.0977C23.6877 16.6871 23.4519 17.1538 23.0736 17.4977C22.7003 17.8366 22.214 18.0061 21.6147 18.0061ZM24.571 12.6566H26.4058C26.784 12.6566 27.1082 12.7205 27.3784 12.8482C27.6535 12.971 27.8647 13.1503 28.0121 13.3861C28.1594 13.617 28.2331 13.8945 28.2331 14.2187C28.2331 14.538 28.1594 14.8156 28.0121 15.0513C27.8647 15.2871 27.6535 15.4689 27.3784 15.5966C27.1082 15.7194 26.784 15.7808 26.4058 15.7808H25.0721V17.8882H24.571V12.6566ZM26.4058 15.2945C26.8331 15.2945 27.1549 15.2036 27.371 15.0219C27.5921 14.8401 27.7026 14.5724 27.7026 14.2187C27.7026 13.865 27.5921 13.5973 27.371 13.4156C27.1549 13.2338 26.8331 13.1429 26.4058 13.1429H25.0721V15.2945H26.4058ZM29.923 12.6566H30.5861L32.4945 17.8882H31.9493L31.3893 16.3187H29.1272L28.5672 17.8882H28.0219L29.923 12.6566ZM31.2198 15.8324L30.2545 13.1061L29.2967 15.8324H31.2198Z\"\r\n            fill=\"#D4D4D8\"\r\n          />\r\n          <line\r\n            x1=\"10.4938\"\r\n            y1=\"21.2488\"\r\n            x2=\"34.9881\"\r\n            y2=\"21.2488\"\r\n            stroke=\"#E4E4E7\"\r\n            strokeWidth=\"0.263158\"\r\n          />\r\n        </g>\r\n      </g>\r\n      <defs>\r\n        <filter\r\n          id=\"filter0_ddd_1_2018\"\r\n          x=\"0.857143\"\r\n          y=\"0.148996\"\r\n          width=\"44.2857\"\r\n          height=\"44.2857\"\r\n          filterUnits=\"userSpaceOnUse\"\r\n          colorInterpolationFilters=\"sRGB\"\r\n        >\r\n          <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feMorphology\r\n            radius=\"0.714286\"\r\n            operator=\"erode\"\r\n            in=\"SourceAlpha\"\r\n            result=\"effect1_dropShadow_1_2018\"\r\n          />\r\n          <feOffset dy=\"0.714286\" />\r\n          <feGaussianBlur stdDeviation=\"0.357143\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"BackgroundImageFix\"\r\n            result=\"effect1_dropShadow_1_2018\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feMorphology\r\n            radius=\"0.714286\"\r\n            operator=\"erode\"\r\n            in=\"SourceAlpha\"\r\n            result=\"effect2_dropShadow_1_2018\"\r\n          />\r\n          <feOffset dy=\"1.42857\" />\r\n          <feGaussianBlur stdDeviation=\"1.42857\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect1_dropShadow_1_2018\"\r\n            result=\"effect2_dropShadow_1_2018\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feMorphology\r\n            radius=\"0.714286\"\r\n            operator=\"dilate\"\r\n            in=\"SourceAlpha\"\r\n            result=\"effect3_dropShadow_1_2018\"\r\n          />\r\n          <feOffset />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.08 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect2_dropShadow_1_2018\"\r\n            result=\"effect3_dropShadow_1_2018\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in=\"SourceGraphic\"\r\n            in2=\"effect3_dropShadow_1_2018\"\r\n            result=\"shape\"\r\n          />\r\n        </filter>\r\n        <filter\r\n          id=\"filter1_d_1_2018\"\r\n          x=\"5.44361\"\r\n          y=\"3.30671\"\r\n          width=\"35.1128\"\r\n          height=\"35.1127\"\r\n          filterUnits=\"userSpaceOnUse\"\r\n          colorInterpolationFilters=\"sRGB\"\r\n        >\r\n          <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feMorphology\r\n            radius=\"0.714286\"\r\n            operator=\"dilate\"\r\n            in=\"SourceAlpha\"\r\n            result=\"effect1_dropShadow_1_2018\"\r\n          />\r\n          <feOffset />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.08 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"BackgroundImageFix\"\r\n            result=\"effect1_dropShadow_1_2018\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in=\"SourceGraphic\"\r\n            in2=\"effect1_dropShadow_1_2018\"\r\n            result=\"shape\"\r\n          />\r\n        </filter>\r\n        <linearGradient\r\n          id=\"paint0_linear_1_2018\"\r\n          x1=\"9.88803\"\r\n          y1=\"6.55415\"\r\n          x2=\"36.0447\"\r\n          y2=\"35.5773\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop stopColor=\"#27272A\" />\r\n          <stop offset=\"1\" stopColor=\"#52525C\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint1_linear_1_2018\"\r\n          x1=\"11.9583\"\r\n          y1=\"8.8133\"\r\n          x2=\"33.985\"\r\n          y2=\"33.2538\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop stopColor=\"#52525C\" />\r\n          <stop offset=\"1\" stopColor=\"#27272A\" />\r\n        </linearGradient>\r\n      </defs>\r\n    </svg>\r\n  ),\r\n  hipaa: ({ className }: { className?: string }) => (\r\n    <svg\r\n      width=\"46\"\r\n      height=\"45\"\r\n      viewBox=\"0 0 46 45\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      className={className}\r\n    >\r\n      <g filter=\"url(#filter0_ddd_1_4905)\">\r\n        <rect\r\n          x=\"3\"\r\n          y=\"0.863281\"\r\n          width=\"40\"\r\n          height=\"40\"\r\n          rx=\"20\"\r\n          fill=\"url(#paint0_linear_1_4905)\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M19.0736 7.30078H18.5513C17.4523 7.40753 16.5515 7.91698 15.6382 8.43349L15.6382 8.4335C15.3095 8.61938 14.9792 8.80617 14.6375 8.97544C13.1797 9.69771 11.6905 10.3538 10.1701 10.9436C9.31299 11.2764 8.4347 11.5409 7.5352 11.7372C7.4488 11.7559 7.36935 11.7893 7.29688 11.8372V11.8727C7.5102 11.9479 7.76245 11.9888 8.05363 11.9953C8.84267 12.0125 9.51349 12.0095 10.0661 11.9865C10.1506 11.9828 10.2321 11.9633 10.3106 11.928C12.4837 10.9519 14.6612 9.98527 16.8432 9.02797C16.8476 9.02613 16.852 9.02536 16.8565 9.02567C16.8777 9.02843 16.879 9.03427 16.8603 9.04318C14.6862 10.108 12.5139 11.176 10.3434 12.2473C10.2138 12.3114 9.41474 12.7999 9.89472 12.9132C10.6648 13.0953 11.6052 13.0639 12.3886 12.9939C12.4952 12.9843 12.6 12.9582 12.7029 12.9155C14.1157 12.3278 15.0315 11.9343 15.4503 11.7349C15.5082 11.7069 15.5663 11.6793 15.6245 11.6519C16.9222 11.0378 16.9367 11.0651 15.6682 11.734C14.7886 12.1972 13.894 12.6323 12.9844 13.039C12.7769 13.1321 11.6328 13.7584 12.3364 13.9538C12.4725 13.9916 12.6016 14.0121 12.7238 14.0155C13.3976 14.0333 14.0708 13.9567 14.7434 13.7856C14.8605 13.7558 14.9747 13.7083 15.0861 13.6432C15.7916 13.2327 16.4885 12.8092 17.1769 12.3727C17.1826 12.3694 17.2268 12.3621 17.1921 12.3879C16.5905 12.8389 15.9774 13.2745 15.3529 13.6948C15.2118 13.7897 15.1129 13.8702 15.0562 13.9363C14.891 14.1289 14.5539 14.61 15.0282 14.733C15.5751 14.8745 16.5412 14.457 17.0397 14.2275C17.4891 14.0207 17.9114 13.6074 18.2623 13.264L18.2623 13.264L18.2623 13.264L18.2817 13.245C18.3135 13.2141 18.3187 13.2542 18.3011 13.2736C18.0812 13.5218 17.8525 13.7621 17.6151 13.9943C17.442 14.1642 17.3197 14.3066 17.2481 14.4215C16.2811 15.9736 18.2741 15.2192 18.7317 14.9459C19.0251 14.7708 19.3157 14.3372 19.4771 14.0773C19.4847 14.065 19.4923 14.055 19.4999 14.0473C19.5306 14.016 19.5374 14.0203 19.5203 14.0602C19.395 14.3536 19.2423 14.6318 19.0622 14.8948C18.8756 15.1671 18.9696 15.539 19.3333 15.6045C19.9746 15.7197 20.5823 15.1432 20.8306 14.6275C20.8349 14.6186 20.8427 14.6117 20.8525 14.6082C20.917 14.5857 20.7513 14.9203 20.694 15.036L20.694 15.036L20.694 15.036C20.6842 15.0557 20.6777 15.069 20.6759 15.0731C20.6145 15.2129 20.5891 15.3212 20.5999 15.398C20.6298 15.6155 20.8211 15.675 21.0262 15.6307C21.5992 15.5072 22.0821 15.0501 22.375 14.569C22.3793 14.5618 22.3816 14.5535 22.3816 14.545C22.3658 13.4218 22.3524 12.6862 22.3413 12.3381C22.3296 11.975 22.3313 11.6482 22.3465 11.3579C22.0265 11.3502 21.7496 11.3301 21.5157 11.2976C21.0295 11.2303 20.8074 10.6556 20.7551 10.2381C20.7421 10.1328 20.7309 10.0254 20.7197 9.91727C20.6412 9.1606 20.5585 8.36393 19.7843 7.94825C18.9876 7.52014 17.6982 7.77544 16.9177 8.14733C16.6867 8.25724 16.4617 8.36722 16.2377 8.47668C15.6119 8.78252 14.9945 9.08425 14.2795 9.36898C14.2774 9.3698 14.2738 9.37189 14.2694 9.37443C14.2515 9.3848 14.2205 9.40273 14.2216 9.37313C14.2217 9.36881 14.2231 9.3646 14.2257 9.36094C14.2282 9.35728 14.2317 9.35432 14.2358 9.35239C15.1708 8.93273 16.0943 8.49064 17.0065 8.02613C17.1644 7.94594 17.3811 7.86514 17.6564 7.78373C18.1853 7.62751 18.7113 7.58097 19.2407 7.65332C20.2115 7.7865 20.6664 8.52659 20.7936 9.39571C20.8053 9.47557 20.8144 9.57357 20.8245 9.68219C20.8751 10.2286 20.9506 11.044 21.5138 11.1708C21.7195 11.2172 21.9931 11.24 22.3346 11.239L22.319 10.9648C22.3183 10.9535 22.3134 10.9428 22.3051 10.9348C22.2969 10.9268 22.2859 10.9221 22.2743 10.9215C22.0977 10.9123 21.9254 10.8988 21.7573 10.881C21.118 10.8134 21.057 9.96774 21.0224 9.48751L21.0186 9.43534C20.9317 8.26069 20.3701 7.38696 19.0736 7.30078ZM23.952 11.3634C23.8584 11.3678 23.7589 11.3724 23.653 11.38C23.6381 12.3764 23.6176 13.3716 23.5913 14.3658C23.5897 14.4355 23.6046 14.4991 23.6359 14.5565C23.8796 14.9977 24.2298 15.3257 24.6866 15.5404C24.9543 15.6667 25.3977 15.7916 25.411 15.3441C25.4132 15.2722 25.3913 15.19 25.345 15.0976C25.309 15.0257 25.2745 14.953 25.2415 14.8796C25.12 14.6105 25.1422 14.5988 25.308 14.8446C25.32 14.8627 25.3322 14.8808 25.3446 14.8989C25.5682 15.2289 26.004 15.6077 26.4317 15.6307C26.8196 15.6515 27.0755 15.5183 27.0323 15.0999C27.0253 15.0332 26.9961 14.9624 26.9445 14.8874C26.7672 14.6318 26.6174 14.3613 26.4949 14.0759L26.4943 14.0738L26.4941 14.0718L26.4944 14.0696L26.495 14.0679C26.4956 14.0667 26.4965 14.0657 26.4975 14.0649C26.4986 14.0641 26.4998 14.0636 26.5011 14.0635L26.5672 14.1485L26.5673 14.1486L26.5674 14.1488L26.5674 14.1488L26.5677 14.1492C26.6999 14.3195 26.9863 14.6884 27.0266 14.7353C27.3186 15.0754 27.8997 15.2694 28.3327 15.357C28.7319 15.4376 29.1806 15.3736 28.9683 14.8455C28.8421 14.5317 28.6996 14.2814 28.4347 14.0395C28.3382 13.951 28.2445 13.8599 28.1537 13.7662C27.6951 13.2931 27.7171 13.2711 28.2197 13.7003C28.3102 13.7774 28.4013 13.8539 28.4931 13.9298C28.9793 14.3317 30.2502 14.833 30.8636 14.7685C31.6346 14.6883 31.016 13.9418 30.7302 13.751C30.1035 13.3332 29.4873 12.9014 28.8815 12.4556C28.8549 12.436 28.8321 12.4183 28.8131 12.4026C28.6852 12.2979 28.6914 12.2899 28.8316 12.3787C29.5276 12.8189 30.2219 13.2412 30.9144 13.6455C31.0305 13.7134 31.1484 13.7628 31.268 13.7939C31.9693 13.9759 32.7478 14.0644 33.4742 14.0114C33.7054 13.9943 34.1014 13.8662 33.7975 13.5685C33.6041 13.379 33.3827 13.2274 33.1333 13.1137C31.7854 12.4989 30.4668 11.8295 29.1777 11.1054C29.1562 11.0931 29.1556 11.0919 29.1758 11.1017C30.5406 11.7493 31.9234 12.3584 33.3242 12.9289C33.4299 12.9719 33.5377 12.9983 33.6475 13.0082C34.366 13.0708 35.0843 13.0631 35.8024 12.9851C35.9493 12.9691 36.092 12.936 36.2306 12.8856C36.3123 12.8561 36.3254 12.8069 36.27 12.7381C36.1149 12.5449 35.9246 12.3923 35.6989 12.2805C33.5565 11.2166 31.4098 10.1608 29.2589 9.11322C29.2543 9.11085 29.2508 9.10674 29.2489 9.1017C29.2432 9.08358 29.2551 9.08097 29.2845 9.09387C31.4317 10.0321 33.5736 10.9814 35.7103 11.9418C35.7891 11.9771 35.8711 11.9968 35.9562 12.0008C36.7203 12.0376 37.4841 12.0329 38.2478 11.9865C38.3953 11.9776 38.5416 11.9545 38.6865 11.9174C38.6968 11.9146 38.7059 11.9086 38.7122 11.9003C38.7186 11.8921 38.7219 11.882 38.7216 11.8717C38.7193 11.7834 38.1676 11.6695 37.9373 11.622C37.889 11.612 37.8548 11.605 37.8429 11.6017C36.9206 11.3479 35.9675 11.0175 34.9835 10.6105C33.3147 9.92014 31.7233 9.21553 30.2369 8.37728L30.1894 8.3505C29.2617 7.82668 28.4334 7.35904 27.32 7.31599C26.1103 7.26898 25.3579 7.74594 25.0849 8.91783C25.0582 9.03241 25.0418 9.19421 25.0233 9.37641C24.962 9.98194 24.8778 10.8128 24.3172 10.8851C24.1165 10.9106 23.9148 10.9249 23.7119 10.928C23.6995 10.9281 23.6877 10.933 23.679 10.9415C23.6703 10.95 23.6654 10.9616 23.6654 10.9736L23.6639 11.239C23.9436 11.2635 24.5864 11.2575 24.7991 11.004C24.9732 10.7963 25.0822 10.5565 25.1262 10.2847C25.1525 10.1221 25.1712 9.96165 25.1897 9.8035L25.1897 9.80346L25.1897 9.80343L25.1898 9.80339L25.1898 9.80335C25.2475 9.31007 25.3026 8.83918 25.5743 8.39709C25.8844 7.89295 26.4303 7.68143 27.0394 7.63903C27.8626 7.58143 28.5885 7.82244 29.3168 8.19341C30.1191 8.60232 30.9333 8.98772 31.7594 9.34963C31.7987 9.36683 31.7968 9.37636 31.7537 9.3782C31.7442 9.37881 31.7347 9.37728 31.7252 9.37359C31.0228 9.10275 30.3289 8.7621 29.6401 8.42402L29.6401 8.42401C29.4476 8.32952 29.2555 8.23523 29.0638 8.14272C28.4684 7.85577 27.8161 7.72428 27.1068 7.74825C25.5838 7.79986 25.3327 8.98235 25.2781 10.204C25.2737 10.3063 25.243 10.4341 25.186 10.5874C24.9148 11.3186 24.5549 11.3354 23.952 11.3634ZM23.9444 8.72196C23.9444 9.22639 23.5231 9.63532 23.0035 9.63532C22.4838 9.63532 22.0625 9.22639 22.0625 8.72196C22.0625 8.21752 22.4838 7.80859 23.0035 7.80859C23.5231 7.80859 23.9444 8.21752 23.9444 8.72196ZM23.4354 19.2164C23.9105 19.0149 24.3602 18.7703 24.7846 18.4828C24.8251 18.4553 24.8743 18.4248 24.9283 18.3913L24.9284 18.3913C25.222 18.2092 25.6559 17.9402 25.5713 17.6136C25.5245 17.4321 25.4047 17.3439 25.2119 17.3491C24.7837 17.3602 24.2111 16.9814 23.9239 16.7012C23.8521 16.6308 23.8443 16.5689 23.9007 16.5155C23.9051 16.5112 23.9087 16.5062 23.9112 16.5006C23.9136 16.495 23.9149 16.4889 23.9149 16.4829C23.915 16.4768 23.9138 16.4707 23.9114 16.4651C23.909 16.4595 23.9055 16.4544 23.9011 16.45C23.8622 16.4116 23.8575 16.3616 23.8869 16.2998C24.017 16.0255 24.2377 15.8758 24.5492 15.851C25.1037 15.8072 25.9763 15.9758 26.1329 16.5869C26.1354 16.5965 26.1411 16.6052 26.1491 16.6118C26.5773 16.9473 26.9367 17.5408 26.8095 18.0874C26.5745 19.0961 25.2513 19.6298 24.364 19.9284C24.3582 19.9304 24.352 19.9313 24.3458 19.931C24.3396 19.9307 24.3335 19.9292 24.3279 19.9265L22.5918 19.121C22.5839 19.1173 22.5772 19.1114 22.5725 19.1042C22.5679 19.097 22.5653 19.0886 22.5652 19.08L22.4123 9.7846C22.4122 9.77796 22.4106 9.77142 22.4077 9.76543C22.4047 9.75945 22.4004 9.75416 22.395 9.74995C22.3897 9.74574 22.3835 9.7427 22.3768 9.74104C22.3701 9.73938 22.3631 9.73915 22.3563 9.74036C22.1819 9.77078 22.0944 9.69121 22.0938 9.50165C22.0938 9.46325 22.1096 9.42731 22.1412 9.39382C22.1455 9.3892 22.1508 9.38549 22.1566 9.38293C22.1625 9.38036 22.1688 9.379 22.1753 9.37891C22.1817 9.37882 22.1882 9.38002 22.1941 9.38243C22.2001 9.38483 22.2055 9.3884 22.2101 9.3929C22.3506 9.53238 22.4834 9.62193 22.6084 9.66156C23.0654 9.80595 23.4566 9.71671 23.782 9.39382C23.7906 9.38522 23.8023 9.38025 23.8147 9.37999C23.827 9.37973 23.839 9.3842 23.848 9.39244C23.8923 9.43238 23.9086 9.48829 23.8969 9.56018C23.8709 9.71901 23.7862 9.77769 23.6429 9.73622C23.6359 9.73416 23.6285 9.73372 23.6213 9.73491C23.6141 9.7361 23.6073 9.7389 23.6014 9.74309C23.5954 9.74728 23.5906 9.75274 23.5872 9.75906C23.5837 9.76538 23.5818 9.77239 23.5816 9.77953C23.5332 11.8247 23.484 13.8507 23.434 15.8574C23.4068 16.9628 23.3851 18.0682 23.3689 19.1735C23.3688 19.1812 23.3707 19.1889 23.3744 19.1957C23.3781 19.2025 23.3835 19.2083 23.3901 19.2126C23.3967 19.2168 23.4044 19.2194 23.4123 19.2201C23.4202 19.2207 23.4281 19.2195 23.4354 19.2164ZM24.835 16.2264C24.8351 16.2149 24.8306 16.2036 24.8218 16.1929C24.8131 16.1823 24.8002 16.1726 24.784 16.1644C24.7677 16.1562 24.7484 16.1497 24.7271 16.1451C24.7058 16.1406 24.683 16.1382 24.6599 16.1381C24.6133 16.1377 24.5686 16.1466 24.5355 16.1627C24.5025 16.1788 24.4838 16.2009 24.4837 16.224C24.4836 16.2354 24.488 16.2468 24.4968 16.2574C24.5055 16.268 24.5184 16.2777 24.5347 16.2859C24.5509 16.2941 24.5702 16.3006 24.5915 16.3052C24.6128 16.3097 24.6356 16.3121 24.6587 16.3123C24.7053 16.3126 24.75 16.3037 24.7831 16.2876C24.8161 16.2715 24.8348 16.2495 24.835 16.2264ZM22.5395 27.688C22.7974 27.91 23.0477 28.1255 23.2629 28.3449C23.4832 28.5698 23.616 28.7142 23.6613 28.7781C24.089 29.3808 23.9048 29.8444 23.4595 30.3583C22.7303 31.1997 21.8197 32.4689 22.5352 33.6071C22.5412 33.6166 22.5477 33.6232 22.5546 33.6269C22.5822 33.6423 22.5917 33.6352 22.5831 33.6057C22.5701 33.5611 22.5561 33.516 22.542 33.4706L22.5419 33.4705C22.478 33.2647 22.4119 33.0515 22.4212 32.8458C22.4667 31.8631 23.2106 31.1761 23.8923 30.5466L23.8977 30.5417C24.3421 30.1311 24.7114 29.4154 24.4147 28.8343C24.2678 28.5464 24.0542 28.2661 23.7738 27.9933C23.6245 27.8483 23.4709 27.704 23.3163 27.5588L23.3158 27.5583L23.3156 27.5581C22.8037 27.0774 22.2819 26.5872 21.8762 26.0324C21.4185 25.4066 21.8681 24.8541 22.3733 24.4758C22.8926 24.0867 23.4595 23.7381 24.0261 23.3896C24.3085 23.216 24.5908 23.0423 24.8671 22.8638C25.4539 22.4845 26.0217 21.8458 25.65 21.1149C25.5538 20.9254 25.4164 20.7662 25.2379 20.6375C24.5536 20.1439 23.7116 19.77 22.9035 19.411L22.9035 19.411L22.9035 19.411C22.721 19.3299 22.5402 19.2496 22.3633 19.1689C21.7793 18.902 21.3535 18.6384 20.8303 18.2924L20.8066 18.2767C20.6006 18.141 20.3765 17.9934 20.3285 17.7583C20.2899 17.5678 20.3812 17.4277 20.6024 17.338C20.6123 17.3341 20.6231 17.3334 20.6333 17.3361C20.9841 17.4292 21.8952 16.9131 22.0371 16.5887C22.0414 16.579 22.0422 16.5682 22.0392 16.558C22.0363 16.5479 22.0299 16.539 22.021 16.5329L22.0001 16.5186C21.9942 16.5146 21.9893 16.5092 21.9858 16.5031C21.9823 16.4969 21.9804 16.49 21.9801 16.483C21.9797 16.476 21.9811 16.469 21.984 16.4625C21.9869 16.4561 21.9913 16.4504 21.9968 16.4458L22.0248 16.4228C22.0318 16.417 22.037 16.4094 22.0396 16.4009C22.0422 16.3924 22.0422 16.3833 22.0395 16.3748C21.7775 15.5076 20.282 15.8191 19.877 16.3426C19.8384 16.3924 19.82 16.4381 19.802 16.483C19.7768 16.5457 19.7523 16.6067 19.6743 16.6744C19.3616 16.9457 19.1643 17.2791 19.0823 17.6748C18.9133 18.4924 19.8647 19.2108 20.5056 19.5251C20.6183 19.5801 20.9115 19.6953 21.3853 19.8707C22.0094 20.1014 22.6347 20.3295 23.2611 20.555C23.7097 20.7163 25.3281 21.4896 24.4038 22.1163C24.0499 22.356 23.2622 22.9088 22.0405 23.7748C21.6914 24.0222 21.384 24.3295 21.1185 24.697C20.7273 25.2389 20.8094 25.7191 21.1707 26.2716C21.5263 26.8156 22.0467 27.2636 22.5395 27.688ZM21.4001 16.1957C21.4087 16.2063 21.413 16.2176 21.4128 16.2289H21.4128C21.4126 16.2403 21.4079 16.2514 21.3989 16.2617C21.39 16.272 21.3769 16.2813 21.3606 16.289C21.3442 16.2967 21.3248 16.3028 21.3035 16.3067C21.2823 16.3107 21.2595 16.3126 21.2366 16.3122C21.1902 16.3114 21.146 16.3015 21.1135 16.2848C21.0811 16.2681 21.0631 16.2459 21.0635 16.223C21.0637 16.2117 21.0684 16.2006 21.0773 16.1903C21.0863 16.18 21.0993 16.1707 21.1157 16.163C21.1321 16.1552 21.1514 16.1492 21.1727 16.1453C21.194 16.1413 21.2168 16.1394 21.2397 16.1398C21.2626 16.1402 21.2853 16.1428 21.3064 16.1475C21.3276 16.1522 21.3467 16.1589 21.3628 16.1672C21.3788 16.1754 21.3915 16.1851 21.4001 16.1957ZM21.7927 23.3472L21.7925 23.3472C21.1373 22.9721 20.2923 22.4884 20.1631 21.7698C20.0231 20.9905 20.9151 20.3854 21.566 20.1057C21.5773 20.1007 21.5901 20.1002 21.6016 20.1043L23.2984 20.7135C23.3077 20.7169 23.3156 20.723 23.3212 20.7309C23.3267 20.7389 23.3295 20.7483 23.3292 20.7578L23.2813 22.6997C23.2811 22.7069 23.2793 22.7139 23.2758 22.7202C23.2723 22.7265 23.2674 22.732 23.2613 22.7361L22.1371 23.5149C22.1295 23.5203 22.1205 23.5233 22.1111 23.5236C22.1017 23.5239 22.0924 23.5216 22.0844 23.5168C21.9942 23.4626 21.8958 23.4063 21.7927 23.3472ZM21.4544 22.0735C21.8146 22.3398 22.181 22.5982 22.5535 22.8486C22.6044 22.8827 22.6293 22.8698 22.628 22.8080L22.5919 20.6407C22.5918 20.6333 22.5897 20.626 22.5861 20.6195C22.5824 20.6129 22.5771 20.6073 22.5707 20.6032C22.5644 20.599 22.5571 20.5964 22.5494 20.5956C22.5418 20.5947 22.5341 20.5957 22.5269 20.5983C22.1238 20.7509 20.664 21.49 21.4544 22.0735ZM23.4793 27.5085C23.2219 27.2836 22.8996 26.9619 22.7045 26.7573C22.6963 26.7491 22.6917 26.7382 22.6916 26.7269L22.6556 24.4527C22.6554 24.445 22.6573 24.4373 22.6609 24.4305C22.6646 24.4236 22.6699 24.4178 22.6765 24.4135L23.748 23.7191C23.7562 23.7138 23.7659 23.7111 23.7757 23.7113C23.7856 23.7116 23.7951 23.7148 23.803 23.7205C24.4834 24.2168 25.4096 25.1016 24.8992 26.0099C24.5751 26.5868 24.1219 27.0871 23.5396 27.5108C23.5307 27.5173 23.5199 27.5206 23.5089 27.5202C23.4979 27.5198 23.4874 27.5156 23.4793 27.5085ZM23.5903 24.5269C23.5033 24.4529 23.4126 24.383 23.3183 24.3172C23.2696 24.2831 23.2444 24.2953 23.2428 24.3536L23.1859 26.89C23.1843 26.9527 23.206 26.9616 23.2509 26.9168C23.4218 26.746 23.5881 26.5712 23.7499 26.3923C23.9322 26.1905 24.0642 26.0071 24.1458 25.8421C24.4121 25.3034 23.9773 24.8546 23.5903 24.5269ZM22.4608 30.9952C22.5107 31.0416 22.5602 31.0877 22.609 31.1338C22.6139 31.1384 22.6197 31.142 22.6262 31.1442C22.6326 31.1464 22.6394 31.1473 22.6462 31.1467C22.6531 31.1462 22.6597 31.1442 22.6656 31.1409C22.6715 31.1376 22.6767 31.1332 22.6807 31.1278L23.0899 30.5812C23.0958 30.5734 23.0989 30.5642 23.0989 30.555L23.1459 28.4448C23.1461 28.4383 23.1449 28.4319 23.1423 28.4259C23.1398 28.42 23.136 28.4146 23.1312 28.4103L22.4286 27.7665C22.4197 27.7583 22.4079 27.7537 22.3955 27.7539C22.3832 27.7541 22.3714 27.7589 22.3626 27.7674L22.3463 27.7833C21.9152 28.2035 21.2952 28.8078 21.3931 29.4485C21.4915 30.0925 21.9887 30.5555 22.4608 30.9952ZM22.1684 28.8803C22.3045 28.6963 22.4615 28.5192 22.6394 28.349C22.6922 28.2986 22.7191 28.3094 22.7201 28.3812L22.7552 30.6407C22.7562 30.7141 22.7334 30.7218 22.6868 30.6637C22.589 30.5411 22.4841 30.4115 22.3721 30.2748C22.1433 29.9964 21.932 29.6541 22.0151 29.2918C22.0603 29.0946 22.1114 28.9574 22.1684 28.8803ZM23.0125 34.0863C23.0309 33.3287 23.0502 32.5756 23.0704 31.8269C23.0733 31.7243 23.1026 31.7166 23.1583 31.8038C23.4086 32.1952 23.4276 32.5966 23.2152 33.008C23.2121 33.0141 23.2105 33.0201 23.2105 33.0259C23.2105 33.0309 23.2108 33.0355 23.2114 33.0398C23.2122 33.043 23.2137 33.0459 23.216 33.0484C23.2182 33.0509 23.221 33.0527 23.2242 33.0538C23.2274 33.0549 23.2308 33.0552 23.2341 33.0547C23.2375 33.0542 23.2406 33.0529 23.2433 33.0508C23.5414 32.813 23.5599 32.2232 23.4555 31.8988C23.408 31.751 23.342 31.6098 23.2575 31.4752C23.2536 31.4691 23.2484 31.464 23.2422 31.4601C23.2359 31.4563 23.2289 31.4539 23.2216 31.4532C23.2143 31.4524 23.2069 31.4533 23.2 31.4558C23.1932 31.4582 23.187 31.4622 23.182 31.4674C23.044 31.6112 22.9269 31.7702 22.8307 31.9444C22.7981 32.0034 22.7823 32.0691 22.7832 32.1416C22.7937 32.7539 22.803 33.3662 22.8112 33.9785C22.8125 34.0694 22.8185 34.1424 22.8293 34.1974C22.8407 34.2554 22.8557 34.3124 22.8744 34.3683C22.9006 34.4454 22.925 34.4448 22.9475 34.3665C22.9586 34.3266 22.9706 34.2869 22.9836 34.2476C23.0019 34.1926 23.0116 34.1388 23.0125 34.0863Z\"\r\n          fill=\"url(#paint1_linear_1_4905)\"\r\n        />\r\n        <path\r\n          d=\"M10.4578 29.8555V26.8126H11.1092V28.0512H12.2621V26.8126H12.9135V29.8555H12.2621V28.5998H11.1092V29.8555H10.4578ZM13.4879 29.8555V26.8126H14.1393V29.8555H13.4879ZM14.7142 29.8555V26.8126H15.9313C16.6599 26.8126 17.1013 27.1898 17.1013 27.8069C17.1013 28.424 16.6599 28.8055 15.9313 28.8055H15.3656V29.8555H14.7142ZM15.3656 28.2569H15.8928C16.2356 28.2569 16.4328 28.1026 16.4328 27.8069C16.4328 27.5112 16.2356 27.3612 15.8928 27.3612H15.3656V28.2569ZM17.0022 29.8555L18.0993 26.8126H18.8708L19.9679 29.8555H19.2908L19.0679 29.2083H17.8979L17.675 29.8555H17.0022ZM18.0822 28.6726H18.8879L18.485 27.4983L18.0822 28.6726ZM20.1328 29.8555L21.2299 26.8126H22.0013L23.0985 29.8555H22.4213L22.1985 29.2083H21.0285L20.8056 29.8555H20.1328ZM21.2128 28.6726H22.0185L21.6156 27.4983L21.2128 28.6726ZM11.7478 33.924C10.9163 33.924 10.3206 33.3326 10.3206 32.3383C10.3206 31.3698 10.8821 30.744 11.7606 30.744C12.5578 30.744 12.9863 31.1512 13.1149 31.8755L12.4378 31.9012C12.3692 31.5198 12.1378 31.2926 11.7606 31.2926C11.2763 31.2926 10.9935 31.704 10.9935 32.3383C10.9935 32.9812 11.2892 33.3755 11.7563 33.3755C12.1635 33.3755 12.3863 33.1312 12.4463 32.7198L13.1278 32.7455C13.0035 33.4869 12.5406 33.924 11.7478 33.924ZM14.9508 33.924C14.0251 33.924 13.4679 33.3198 13.4679 32.3383C13.4679 31.3569 14.0251 30.744 14.9508 30.744C15.8765 30.744 16.4337 31.3569 16.4337 32.3383C16.4337 33.3198 15.8765 33.924 14.9508 33.924ZM14.9508 33.3755C15.4565 33.3755 15.7608 32.9983 15.7608 32.3383C15.7608 31.6783 15.4565 31.2926 14.9508 31.2926C14.4408 31.2926 14.1408 31.6783 14.1408 32.3383C14.1408 32.9983 14.4408 33.3755 14.9508 33.3755ZM16.8654 33.8555V30.8126H17.7354L18.5111 33.0283L19.2826 30.8126H20.1526V33.8555H19.5011V31.884L18.7854 33.8469H18.2283L17.5168 31.884V33.8555H16.8654ZM20.7284 33.8555V30.8126H21.9456C22.6741 30.8126 23.1156 31.1898 23.1156 31.8069C23.1156 32.424 22.6741 32.8055 21.9456 32.8055H21.3798V33.8555H20.7284ZM21.3798 32.2569H21.907C22.2498 32.2569 22.447 32.1026 22.447 31.8069C22.447 31.5112 22.2498 31.3612 21.907 31.3612H21.3798V32.2569ZM23.6079 33.8555V30.8126H24.2593V33.3069H25.6607V33.8555H23.6079ZM25.9935 33.8555V30.8126H26.6449V33.8555H25.9935ZM27.0008 33.8555L28.0979 30.8126H28.8694L29.9665 33.8555H29.2894L29.0665 33.2083H27.8965L27.6736 33.8555H27.0008ZM28.0808 32.6726H28.8865L28.4836 31.4983L28.0808 32.6726ZM30.2541 33.8555V30.8126H30.9741L32.1827 32.9126V30.8126H32.8341V33.8555H32.1055L30.9055 31.8283V33.8555H30.2541ZM33.9856 33.8555V31.3612H33.077V30.8126H35.5541V31.3612H34.6413V33.8555H33.9856Z\"\r\n          fill=\"#101828\"\r\n        />\r\n      </g>\r\n      <defs>\r\n        <filter\r\n          id=\"filter0_ddd_1_4905\"\r\n          x=\"0.857143\"\r\n          y=\"0.148996\"\r\n          width=\"44.2857\"\r\n          height=\"44.2857\"\r\n          filterUnits=\"userSpaceOnUse\"\r\n          colorInterpolationFilters=\"sRGB\"\r\n        >\r\n          <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feMorphology\r\n            radius=\"0.714286\"\r\n            operator=\"erode\"\r\n            in=\"SourceAlpha\"\r\n            result=\"effect1_dropShadow_1_4905\"\r\n          />\r\n          <feOffset dy=\"0.714286\" />\r\n          <feGaussianBlur stdDeviation=\"0.357143\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"BackgroundImageFix\"\r\n            result=\"effect1_dropShadow_1_4905\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feMorphology\r\n            radius=\"0.714286\"\r\n            operator=\"erode\"\r\n            in=\"SourceAlpha\"\r\n            result=\"effect2_dropShadow_1_4905\"\r\n          />\r\n          <feOffset dy=\"1.42857\" />\r\n          <feGaussianBlur stdDeviation=\"1.42857\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect1_dropShadow_1_4905\"\r\n            result=\"effect2_dropShadow_1_4905\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feMorphology\r\n            radius=\"0.714286\"\r\n            operator=\"dilate\"\r\n            in=\"SourceAlpha\"\r\n            result=\"effect3_dropShadow_1_4905\"\r\n          />\r\n          <feOffset />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.08 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect2_dropShadow_1_4905\"\r\n            result=\"effect3_dropShadow_1_4905\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in=\"SourceGraphic\"\r\n            in2=\"effect3_dropShadow_1_4905\"\r\n            result=\"shape\"\r\n          />\r\n        </filter>\r\n        <linearGradient\r\n          id=\"paint0_linear_1_4905\"\r\n          x1=\"9.88803\"\r\n          y1=\"6.55415\"\r\n          x2=\"36.0447\"\r\n          y2=\"35.5773\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop stopColor=\"#E5E7EB\" />\r\n          <stop offset=\"1\" stopColor=\"#F9FAFB\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint1_linear_1_4905\"\r\n          x1=\"30.5498\"\r\n          y1=\"10.0698\"\r\n          x2=\"20.9753\"\r\n          y2=\"31.2119\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.473541\" stopColor=\"#364153\" stopOpacity=\"0.7\" />\r\n          <stop offset=\"0.811446\" stopColor=\"#364153\" stopOpacity=\"0\" />\r\n        </linearGradient>\r\n      </defs>\r\n    </svg>\r\n  ),\r\n  hipaaDark: ({ className }: { className?: string }) => (\r\n    <svg\r\n      width=\"46\"\r\n      height=\"45\"\r\n      viewBox=\"0 0 46 45\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      className={className}\r\n    >\r\n      <g filter=\"url(#filter0_ddd_1_2028)\">\r\n        <rect\r\n          x=\"3\"\r\n          y=\"0.863281\"\r\n          width=\"40\"\r\n          height=\"40\"\r\n          rx=\"20\"\r\n          fill=\"url(#paint0_linear_1_2028)\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M19.0736 7.30078H18.5513C17.4523 7.40753 16.5515 7.91698 15.6382 8.43349L15.6382 8.4335C15.3095 8.61938 14.9792 8.80617 14.6375 8.97544C13.1797 9.69771 11.6905 10.3538 10.1701 10.9436C9.31299 11.2764 8.4347 11.5409 7.5352 11.7372C7.4488 11.7559 7.36935 11.7893 7.29688 11.8372V11.8727C7.5102 11.9479 7.76245 11.9888 8.05363 11.9953C8.84267 12.0125 9.51349 12.0095 10.0661 11.9865C10.1506 11.9828 10.2321 11.9633 10.3106 11.928C12.4837 10.9519 14.6612 9.98527 16.8432 9.02797C16.8476 9.02613 16.852 9.02536 16.8565 9.02567C16.8777 9.02843 16.879 9.03427 16.8603 9.04318C14.6862 10.108 12.5139 11.176 10.3434 12.2473C10.2138 12.3114 9.41474 12.7999 9.89472 12.9132C10.6648 13.0953 11.6052 13.0639 12.3886 12.9939C12.4952 12.9843 12.6 12.9582 12.7029 12.9155C14.1157 12.3278 15.0315 11.9343 15.4503 11.7349C15.5082 11.7069 15.5663 11.6793 15.6245 11.6519C16.9222 11.0378 16.9367 11.0651 15.6682 11.734C14.7886 12.1972 13.894 12.6323 12.9844 13.039C12.7769 13.1321 11.6328 13.7584 12.3364 13.9538C12.4725 13.9916 12.6016 14.0121 12.7238 14.0155C13.3976 14.0333 14.0708 13.9567 14.7434 13.7856C14.8605 13.7558 14.9747 13.7083 15.0861 13.6432C15.7916 13.2327 16.4885 12.8092 17.1769 12.3727C17.1826 12.3694 17.2268 12.3621 17.1921 12.3879C16.5905 12.8389 15.9774 13.2745 15.3529 13.6948C15.2118 13.7897 15.1129 13.8702 15.0562 13.9363C14.891 14.1289 14.5539 14.61 15.0282 14.733C15.5751 14.8745 16.5412 14.457 17.0397 14.2275C17.4891 14.0207 17.9114 13.6074 18.2623 13.264L18.2623 13.264L18.2623 13.264L18.2817 13.245C18.3135 13.2141 18.3187 13.2542 18.3011 13.2736C18.0812 13.5218 17.8525 13.7621 17.6151 13.9943C17.442 14.1642 17.3197 14.3066 17.2481 14.4215C16.2811 15.9736 18.2741 15.2192 18.7317 14.9459C19.0251 14.7708 19.3157 14.3372 19.4771 14.0773C19.4847 14.065 19.4923 14.055 19.4999 14.0473C19.5306 14.016 19.5374 14.0203 19.5203 14.0602C19.395 14.3536 19.2423 14.6318 19.0622 14.8948C18.8756 15.1671 18.9696 15.539 19.3333 15.6045C19.9746 15.7197 20.5823 15.1432 20.8306 14.6275C20.8349 14.6186 20.8427 14.6117 20.8525 14.6082C20.917 14.5857 20.7513 14.9203 20.694 15.036L20.694 15.036L20.694 15.036C20.6842 15.0557 20.6777 15.069 20.6759 15.0731C20.6145 15.2129 20.5891 15.3212 20.5999 15.398C20.6298 15.6155 20.8211 15.675 21.0262 15.6307C21.5992 15.5072 22.0821 15.0501 22.375 14.569C22.3793 14.5618 22.3816 14.5535 22.3816 14.545C22.3658 13.4218 22.3524 12.6862 22.3413 12.3381C22.3296 11.975 22.3313 11.6482 22.3465 11.3579C22.0265 11.3502 21.7496 11.3301 21.5157 11.2976C21.0295 11.2303 20.8074 10.6556 20.7551 10.2381C20.7421 10.1328 20.7309 10.0254 20.7197 9.91727C20.6412 9.1606 20.5585 8.36393 19.7843 7.94825C18.9876 7.52014 17.6982 7.77544 16.9177 8.14733C16.6867 8.25724 16.4617 8.36722 16.2377 8.47668C15.6119 8.78252 14.9945 9.08425 14.2795 9.36898C14.2774 9.3698 14.2738 9.37189 14.2694 9.37443C14.2515 9.3848 14.2205 9.40273 14.2216 9.37313C14.2217 9.36881 14.2231 9.3646 14.2257 9.36094C14.2282 9.35728 14.2317 9.35432 14.2358 9.35239C15.1708 8.93273 16.0943 8.49064 17.0065 8.02613C17.1644 7.94594 17.3811 7.86514 17.6564 7.78373C18.1853 7.62751 18.7113 7.58097 19.2407 7.65332C20.2115 7.7865 20.6664 8.52659 20.7936 9.39571C20.8053 9.47557 20.8144 9.57357 20.8245 9.68219C20.8751 10.2286 20.9506 11.044 21.5138 11.1708C21.7195 11.2172 21.9931 11.24 22.3346 11.239L22.319 10.9648C22.3183 10.9535 22.3134 10.9428 22.3051 10.9348C22.2969 10.9268 22.2859 10.9221 22.2743 10.9215C22.0977 10.9123 21.9254 10.8988 21.7573 10.881C21.118 10.8134 21.057 9.96774 21.0224 9.48751L21.0186 9.43534C20.9317 8.26069 20.3701 7.38696 19.0736 7.30078ZM23.952 11.3634C23.8584 11.3678 23.7589 11.3724 23.653 11.38C23.6381 12.3764 23.6176 13.3716 23.5913 14.3658C23.5897 14.4355 23.6046 14.4991 23.6359 14.5565C23.8796 14.9977 24.2298 15.3257 24.6866 15.5404C24.9543 15.6667 25.3977 15.7916 25.411 15.3441C25.4132 15.2722 25.3913 15.19 25.345 15.0976C25.309 15.0257 25.2745 14.953 25.2415 14.8796C25.12 14.6105 25.1422 14.5988 25.308 14.8446C25.32 14.8627 25.3322 14.8808 25.3446 14.8989C25.5682 15.2289 26.004 15.6077 26.4317 15.6307C26.8196 15.6515 27.0755 15.5183 27.0323 15.0999C27.0253 15.0332 26.9961 14.9624 26.9445 14.8874C26.7672 14.6318 26.6174 14.3613 26.4949 14.0759L26.4943 14.0738L26.4941 14.0718L26.4944 14.0696L26.495 14.0679C26.4956 14.0667 26.4965 14.0657 26.4975 14.0649C26.4986 14.0641 26.4998 14.0636 26.5011 14.0635L26.5672 14.1485L26.5673 14.1486L26.5674 14.1488L26.5674 14.1488L26.5677 14.1492C26.6999 14.3195 26.9863 14.6884 27.0266 14.7353C27.3186 15.0754 27.8997 15.2694 28.3327 15.357C28.7319 15.4376 29.1806 15.3736 28.9683 14.8455C28.8421 14.5317 28.6996 14.2814 28.4347 14.0395C28.3382 13.951 28.2445 13.8599 28.1537 13.7662C27.6951 13.2931 27.7171 13.2711 28.2197 13.7003C28.3102 13.7774 28.4013 13.8539 28.4931 13.9298C28.9793 14.3317 30.2502 14.833 30.8636 14.7685C31.6346 14.6883 31.016 13.9418 30.7302 13.751C30.1035 13.3332 29.4873 12.9014 28.8815 12.4556C28.8549 12.436 28.8321 12.4183 28.8131 12.4026C28.6852 12.2979 28.6914 12.2899 28.8316 12.3787C29.5276 12.8189 30.2219 13.2412 30.9144 13.6455C31.0305 13.7134 31.1484 13.7628 31.268 13.7939C31.9693 13.9759 32.7478 14.0644 33.4742 14.0114C33.7054 13.9943 34.1014 13.8662 33.7975 13.5685C33.6041 13.379 33.3827 13.2274 33.1333 13.1137C31.7854 12.4989 30.4668 11.8295 29.1777 11.1054C29.1562 11.0931 29.1556 11.0919 29.1758 11.1017C30.5406 11.7493 31.9234 12.3584 33.3242 12.9289C33.4299 12.9719 33.5377 12.9983 33.6475 13.0082C34.366 13.0708 35.0843 13.0631 35.8024 12.9851C35.9493 12.9691 36.092 12.936 36.2306 12.8856C36.3123 12.8561 36.3254 12.8069 36.27 12.7381C36.1149 12.5449 35.9246 12.3923 35.6989 12.2805C33.5565 11.2166 31.4098 10.1608 29.2589 9.11322C29.2543 9.11085 29.2508 9.10674 29.2489 9.1017C29.2432 9.08358 29.2551 9.08097 29.2845 9.09387C31.4317 10.0321 33.5736 10.9814 35.7103 11.9418C35.7891 11.9771 35.8711 11.9968 35.9562 12.0008C36.7203 12.0376 37.4841 12.0329 38.2478 11.9865C38.3953 11.9776 38.5416 11.9545 38.6865 11.9174C38.6968 11.9146 38.7059 11.9086 38.7122 11.9003C38.7186 11.8921 38.7219 11.882 38.7216 11.8717C38.7193 11.7834 38.1676 11.6695 37.9373 11.622C37.889 11.612 37.8548 11.605 37.8429 11.6017C36.9206 11.3479 35.9675 11.0175 34.9835 10.6105C33.3147 9.92014 31.7233 9.21553 30.2369 8.37728L30.1894 8.3505C29.2617 7.82668 28.4334 7.35904 27.32 7.31599C26.1103 7.26898 25.3579 7.74594 25.0849 8.91783C25.0582 9.03241 25.0418 9.19421 25.0233 9.37641C24.962 9.98194 24.8778 10.8128 24.3172 10.8851C24.1165 10.9106 23.9148 10.9249 23.7119 10.928C23.6995 10.9281 23.6877 10.933 23.679 10.9415C23.6703 10.95 23.6654 10.9616 23.6654 10.9736L23.6639 11.239C23.9436 11.2635 24.5864 11.2575 24.7991 11.004C24.9732 10.7963 25.0822 10.5565 25.1262 10.2847C25.1525 10.1221 25.1712 9.96165 25.1897 9.8035L25.1897 9.80346L25.1897 9.80343L25.1898 9.80339L25.1898 9.80335C25.2475 9.31007 25.3026 8.83918 25.5743 8.39709C25.8844 7.89295 26.4303 7.68143 27.0394 7.63903C27.8626 7.58143 28.5885 7.82244 29.3168 8.19341C30.1191 8.60232 30.9333 8.98772 31.7594 9.34963C31.7987 9.36683 31.7968 9.37636 31.7537 9.3782C31.7442 9.37881 31.7347 9.37728 31.7252 9.37359C31.0228 9.10275 30.3289 8.7621 29.6401 8.42402L29.6401 8.42401C29.4476 8.32952 29.2555 8.23523 29.0638 8.14272C28.4684 7.85577 27.8161 7.72428 27.1068 7.74825C25.5838 7.79986 25.3327 8.98235 25.2781 10.204C25.2737 10.3063 25.243 10.4341 25.186 10.5874C24.9148 11.3186 24.5549 11.3354 23.952 11.3634ZM23.9444 8.72196C23.9444 9.22639 23.5231 9.63532 23.0035 9.63532C22.4838 9.63532 22.0625 9.22639 22.0625 8.72196C22.0625 8.21752 22.4838 7.80859 23.0035 7.80859C23.5231 7.80859 23.9444 8.21752 23.9444 8.72196ZM23.4354 19.2164C23.9105 19.0149 24.3602 18.7703 24.7846 18.4828C24.8251 18.4553 24.8743 18.4248 24.9283 18.3913L24.9284 18.3913C25.222 18.2092 25.6559 17.9402 25.5713 17.6136C25.5245 17.4321 25.4047 17.3439 25.2119 17.3491C24.7837 17.3602 24.2111 16.9814 23.9239 16.7012C23.8521 16.6308 23.8443 16.5689 23.9007 16.5155C23.9051 16.5112 23.9087 16.5062 23.9112 16.5006C23.9136 16.495 23.9149 16.4889 23.9149 16.4829C23.915 16.4768 23.9138 16.4707 23.9114 16.4651C23.909 16.4595 23.9055 16.4544 23.9011 16.45C23.8622 16.4116 23.8575 16.3616 23.8869 16.2998C24.017 16.0255 24.2377 15.8758 24.5492 15.851C25.1037 15.8072 25.9763 15.9758 26.1329 16.5869C26.1354 16.5965 26.1411 16.6052 26.1491 16.6118C26.5773 16.9473 26.9367 17.5408 26.8095 18.0874C26.5745 19.0961 25.2513 19.6298 24.364 19.9284C24.3582 19.9304 24.352 19.9313 24.3458 19.931C24.3396 19.9307 24.3335 19.9292 24.3279 19.9265L22.5918 19.121C22.5839 19.1173 22.5772 19.1114 22.5725 19.1042C22.5679 19.097 22.5653 19.0886 22.5652 19.08L22.4123 9.7846C22.4122 9.77796 22.4106 9.77142 22.4077 9.76543C22.4047 9.75945 22.4004 9.75416 22.395 9.74995C22.3897 9.74574 22.3835 9.7427 22.3768 9.74104C22.3701 9.73938 22.3631 9.73915 22.3563 9.74036C22.1819 9.77078 22.0944 9.69121 22.0938 9.50165C22.0938 9.46325 22.1096 9.42731 22.1412 9.39382C22.1455 9.3892 22.1508 9.38549 22.1566 9.38293C22.1625 9.38036 22.1688 9.379 22.1753 9.37891C22.1817 9.37882 22.1882 9.38002 22.1941 9.38243C22.2001 9.38483 22.2055 9.3884 22.2101 9.3929C22.3506 9.53238 22.4834 9.62193 22.6084 9.66156C23.0654 9.80595 23.4566 9.71671 23.782 9.39382C23.7906 9.38522 23.8023 9.38025 23.8147 9.37999C23.827 9.37973 23.839 9.3842 23.848 9.39244C23.8923 9.43238 23.9086 9.48829 23.8969 9.56018C23.8709 9.71901 23.7862 9.77769 23.6429 9.73622C23.6359 9.73416 23.6285 9.73372 23.6213 9.73491C23.6141 9.7361 23.6073 9.7389 23.6014 9.74309C23.5954 9.74728 23.5906 9.75274 23.5872 9.75906C23.5837 9.76538 23.5818 9.77239 23.5816 9.77953C23.5332 11.8247 23.484 13.8507 23.434 15.8574C23.4068 16.9628 23.3851 18.0682 23.3689 19.1735C23.3688 19.1812 23.3707 19.1889 23.3744 19.1957C23.3781 19.2025 23.3835 19.2083 23.3901 19.2126C23.3967 19.2168 23.4044 19.2194 23.4123 19.2201C23.4202 19.2207 23.4281 19.2195 23.4354 19.2164ZM24.835 16.2264C24.8351 16.2149 24.8306 16.2036 24.8218 16.1929C24.8131 16.1823 24.8002 16.1726 24.784 16.1644C24.7677 16.1562 24.7484 16.1497 24.7271 16.1451C24.7058 16.1406 24.683 16.1382 24.6599 16.1381C24.6133 16.1377 24.5686 16.1466 24.5355 16.1627C24.5025 16.1788 24.4838 16.2009 24.4837 16.224C24.4836 16.2354 24.488 16.2468 24.4968 16.2574C24.5055 16.268 24.5184 16.2777 24.5347 16.2859C24.5509 16.2941 24.5702 16.3006 24.5915 16.3052C24.6128 16.3097 24.6356 16.3121 24.6587 16.3123C24.7053 16.3126 24.75 16.3037 24.7831 16.2876C24.8161 16.2715 24.8348 16.2495 24.835 16.2264ZM22.5395 27.688C22.7974 27.91 23.0477 28.1255 23.2629 28.3449C23.4832 28.5698 23.616 28.7142 23.6613 28.7781C24.089 29.3808 23.9048 29.8444 23.4595 30.3583C22.7303 31.1997 21.8197 32.4689 22.5352 33.6071C22.5412 33.6166 22.5477 33.6232 22.5546 33.6269C22.5822 33.6423 22.5917 33.6352 22.5831 33.6057C22.5701 33.5611 22.5561 33.516 22.542 33.4706L22.5419 33.4705C22.478 33.2647 22.4119 33.0515 22.4212 32.8458C22.4667 31.8631 23.2106 31.1761 23.8923 30.5466L23.8977 30.5417C24.3421 30.1311 24.7114 29.4154 24.4147 28.8343C24.2678 28.5464 24.0542 28.2661 23.7738 27.9933C23.6245 27.8483 23.4709 27.704 23.3163 27.5588L23.3158 27.5583L23.3156 27.5581C22.8037 27.0774 22.2819 26.5872 21.8762 26.0324C21.4185 25.4066 21.8681 24.8541 22.3733 24.4758C22.8926 24.0867 23.4595 23.7381 24.0261 23.3896C24.3085 23.216 24.5908 23.0423 24.8671 22.8638C25.4539 22.4845 26.0217 21.8458 25.65 21.1149C25.5538 20.9254 25.4164 20.7662 25.2379 20.6375C24.5536 20.1439 23.7116 19.77 22.9035 19.411L22.9035 19.411L22.9035 19.411C22.721 19.3299 22.5402 19.2496 22.3633 19.1689C21.7793 18.902 21.3535 18.6384 20.8303 18.2924L20.8066 18.2767C20.6006 18.141 20.3765 17.9934 20.3285 17.7583C20.2899 17.5678 20.3812 17.4277 20.6024 17.338C20.6123 17.3341 20.6231 17.3334 20.6333 17.3361C20.9841 17.4292 21.8952 16.9131 22.0371 16.5887C22.0414 16.579 22.0422 16.5682 22.0392 16.558C22.0363 16.5479 22.0299 16.539 22.021 16.5329L22.0001 16.5186C21.9942 16.5146 21.9893 16.5092 21.9858 16.5031C21.9823 16.4969 21.9804 16.49 21.9801 16.483C21.9797 16.476 21.9811 16.469 21.984 16.4625C21.9869 16.4561 21.9913 16.4504 21.9968 16.4458L22.0248 16.4228C22.0318 16.417 22.037 16.4094 22.0396 16.4009C22.0422 16.3924 22.0422 16.3833 22.0395 16.3748C21.7775 15.5076 20.282 15.8191 19.877 16.3426C19.8384 16.3924 19.82 16.4381 19.802 16.483C19.7768 16.5457 19.7523 16.6067 19.6743 16.6744C19.3616 16.9457 19.1643 17.2791 19.0823 17.6748C18.9133 18.4924 19.8647 19.2108 20.5056 19.5251C20.6183 19.5801 20.9115 19.6953 21.3853 19.8707C22.0094 20.1014 22.6347 20.3295 23.2611 20.555C23.7097 20.7163 25.3281 21.4896 24.4038 22.1163C24.0499 22.356 23.2622 22.9088 22.0405 23.7748C21.6914 24.0222 21.384 24.3295 21.1185 24.697C20.7273 25.2389 20.8094 25.7191 21.1707 26.2716C21.5263 26.8156 22.0467 27.2636 22.5395 27.688ZM21.4001 16.1957C21.4087 16.2063 21.413 16.2176 21.4128 16.2289H21.4128C21.4126 16.2403 21.4079 16.2514 21.3989 16.2617C21.39 16.272 21.3769 16.2813 21.3606 16.289C21.3442 16.2967 21.3248 16.3028 21.3035 16.3067C21.2823 16.3107 21.2595 16.3126 21.2366 16.3122C21.1902 16.3114 21.146 16.3015 21.1135 16.2848C21.0811 16.2681 21.0631 16.2459 21.0635 16.223C21.0637 16.2117 21.0684 16.2006 21.0773 16.1903C21.0863 16.18 21.0993 16.1707 21.1157 16.163C21.1321 16.1552 21.1514 16.1492 21.1727 16.1453C21.194 16.1413 21.2168 16.1394 21.2397 16.1398C21.2626 16.1402 21.2853 16.1428 21.3064 16.1475C21.3276 16.1522 21.3467 16.1589 21.3628 16.1672C21.3788 16.1754 21.3915 16.1851 21.4001 16.1957ZM21.7927 23.3472L21.7925 23.3472C21.1373 22.9721 20.2923 22.4884 20.1631 21.7698C20.0231 20.9905 20.9151 20.3854 21.566 20.1057C21.5773 20.1007 21.5901 20.1002 21.6016 20.1043L23.2984 20.7135C23.3077 20.7169 23.3156 20.723 23.3212 20.7309C23.3267 20.7389 23.3295 20.7483 23.3292 20.7578L23.2813 22.6997C23.2811 22.7069 23.2793 22.7139 23.2758 22.7202C23.2723 22.7265 23.2674 22.732 23.2613 22.7361L22.1371 23.5149C22.1295 23.5203 22.1205 23.5233 22.1111 23.5236C22.1017 23.5239 22.0924 23.5216 22.0844 23.5168C21.9942 23.4626 21.8958 23.4063 21.7927 23.3472ZM21.4544 22.0735C21.8146 22.3398 22.181 22.5982 22.5535 22.8486C22.6044 22.8827 22.6293 22.8698 22.628 22.8080L22.5919 20.6407C22.5918 20.6333 22.5897 20.626 22.5861 20.6195C22.5824 20.6129 22.5771 20.6073 22.5707 20.6032C22.5644 20.599 22.5571 20.5964 22.5494 20.5956C22.5418 20.5947 22.5341 20.5957 22.5269 20.5983C22.1238 20.7509 20.664 21.49 21.4544 22.0735ZM23.4793 27.5085C23.2219 27.2836 22.8996 26.9619 22.7045 26.7573C22.6963 26.7491 22.6917 26.7382 22.6916 26.7269L22.6556 24.4527C22.6554 24.445 22.6573 24.4373 22.6609 24.4305C22.6646 24.4236 22.6699 24.4178 22.6765 24.4135L23.748 23.7191C23.7562 23.7138 23.7659 23.7111 23.7757 23.7113C23.7856 23.7116 23.7951 23.7148 23.803 23.7205C24.4834 24.2168 25.4096 25.1016 24.8992 26.0099C24.5751 26.5868 24.1219 27.0871 23.5396 27.5108C23.5307 27.5173 23.5199 27.5206 23.5089 27.5202C23.4979 27.5198 23.4874 27.5156 23.4793 27.5085ZM23.5903 24.5269C23.5033 24.4529 23.4126 24.383 23.3183 24.3172C23.2696 24.2831 23.2444 24.2953 23.2428 24.3536L23.1859 26.89C23.1843 26.9527 23.206 26.9616 23.2509 26.9168C23.4218 26.746 23.5881 26.5712 23.7499 26.3923C23.9322 26.1905 24.0642 26.0071 24.1458 25.8421C24.4121 25.3034 23.9773 24.8546 23.5903 24.5269ZM22.4608 30.9952C22.5107 31.0416 22.5602 31.0877 22.609 31.1338C22.6139 31.1384 22.6197 31.142 22.6262 31.1442C22.6326 31.1464 22.6394 31.1473 22.6462 31.1467C22.6531 31.1462 22.6597 31.1442 22.6656 31.1409C22.6715 31.1376 22.6767 31.1332 22.6807 31.1278L23.0899 30.5812C23.0958 30.5734 23.0989 30.5642 23.0989 30.555L23.1459 28.4448C23.1461 28.4383 23.1449 28.4319 23.1423 28.4259C23.1398 28.42 23.136 28.4146 23.1312 28.4103L22.4286 27.7665C22.4197 27.7583 22.4079 27.7537 22.3955 27.7539C22.3832 27.7541 22.3714 27.7589 22.3626 27.7674L22.3463 27.7833C21.9152 28.2035 21.2952 28.8078 21.3931 29.4485C21.4915 30.0925 21.9887 30.5555 22.4608 30.9952ZM22.1684 28.8803C22.3045 28.6963 22.4615 28.5192 22.6394 28.349C22.6922 28.2986 22.7191 28.3094 22.7201 28.3812L22.7552 30.6407C22.7562 30.7141 22.7334 30.7218 22.6868 30.6637C22.589 30.5411 22.4841 30.4115 22.3721 30.2748C22.1433 29.9964 21.932 29.6541 22.0151 29.2918C22.0603 29.0946 22.1114 28.9574 22.1684 28.8803ZM23.0125 34.0863C23.0309 33.3287 23.0502 32.5756 23.0704 31.8269C23.0733 31.7243 23.1026 31.7166 23.1583 31.8038C23.4086 32.1952 23.4276 32.5966 23.2152 33.008C23.2121 33.0141 23.2105 33.0201 23.2105 33.0259C23.2105 33.0309 23.2108 33.0355 23.2114 33.0398C23.2122 33.043 23.2137 33.0459 23.216 33.0484C23.2182 33.0509 23.221 33.0527 23.2242 33.0538C23.2274 33.0549 23.2308 33.0552 23.2341 33.0547C23.2375 33.0542 23.2406 33.0529 23.2433 33.0508C23.5414 32.813 23.5599 32.2232 23.4555 31.8988C23.408 31.751 23.342 31.6098 23.2575 31.4752C23.2536 31.4691 23.2484 31.464 23.2422 31.4601C23.2359 31.4563 23.2289 31.4539 23.2216 31.4532C23.2143 31.4524 23.2069 31.4533 23.2 31.4558C23.1932 31.4582 23.187 31.4622 23.182 31.4674C23.044 31.6112 22.9269 31.7702 22.8307 31.9444C22.7981 32.0034 22.7823 32.0691 22.7832 32.1416C22.7937 32.7539 22.803 33.3662 22.8112 33.9785C22.8125 34.0694 22.8185 34.1424 22.8293 34.1974C22.8407 34.2554 22.8557 34.3124 22.8744 34.3683C22.9006 34.4454 22.925 34.4448 22.9475 34.3665C22.9586 34.3266 22.9706 34.2869 22.9836 34.2476C23.0019 34.1926 23.0116 34.1388 23.0125 34.0863Z\"\r\n          fill=\"url(#paint1_linear_1_2028)\"\r\n        />\r\n        <path\r\n          d=\"M10.4578 26.8126H11.1092V28.2569L10.8221 28.0512H12.5535L12.2621 28.2569V26.8126H12.9135V29.8555H12.2621V28.394L12.5535 28.5998H10.8221L11.1092 28.394V29.8555H10.4578V26.8126ZM13.4544 26.8126H14.1058V29.8555H13.4544V26.8126ZM14.643 26.8126H15.8602C16.1002 26.8126 16.3087 26.8526 16.4859 26.9326C16.663 27.0126 16.7973 27.1283 16.8887 27.2798C16.983 27.4283 17.0302 27.604 17.0302 27.8069C17.0302 28.0098 16.983 28.1869 16.8887 28.3383C16.7973 28.4869 16.663 28.6026 16.4859 28.6855C16.3087 28.7655 16.1002 28.8055 15.8602 28.8055H15.2945V29.8555H14.643V26.8126ZM15.8216 28.2569C15.9959 28.2569 16.1287 28.2183 16.2202 28.1412C16.3145 28.064 16.3616 27.9526 16.3616 27.8069C16.3616 27.6612 16.3145 27.5512 16.2202 27.4769C16.1287 27.3998 15.9959 27.3612 15.8216 27.3612H15.2945V28.2569H15.8216ZM18.0497 26.8126H18.8211L19.9183 29.8555H19.2411L19.0183 29.2083H17.8483L17.6254 29.8555H16.9526L18.0497 26.8126ZM18.8383 28.6726L18.4354 27.4983L18.0326 28.6726H18.8383ZM21.2263 26.8126H21.9978L23.0949 29.8555H22.4178L22.1949 29.2083H21.0249L20.802 29.8555H20.1292L21.2263 26.8126ZM22.0149 28.6726L21.612 27.4983L21.2092 28.6726H22.0149ZM11.7435 33.924C11.4663 33.924 11.2192 33.8612 11.0021 33.7355C10.7849 33.6098 10.6163 33.4269 10.4963 33.1869C10.3763 32.9469 10.3163 32.664 10.3163 32.3383C10.3163 32.0183 10.3749 31.7383 10.4921 31.4983C10.6092 31.2555 10.7763 31.0698 10.9935 30.9412C11.2106 30.8098 11.4635 30.744 11.7521 30.744C12.1435 30.744 12.4506 30.8398 12.6735 31.0312C12.8963 31.2226 13.0421 31.504 13.1106 31.8755L12.4335 31.9012C12.3963 31.7069 12.3192 31.5569 12.2021 31.4512C12.0878 31.3455 11.9378 31.2926 11.7521 31.2926C11.5921 31.2926 11.4549 31.3355 11.3406 31.4212C11.2263 31.5069 11.1378 31.6283 11.0749 31.7855C11.0149 31.9426 10.9849 32.1269 10.9849 32.3383C10.9849 32.5526 11.0149 32.7383 11.0749 32.8955C11.1378 33.0498 11.2263 33.1683 11.3406 33.2512C11.4578 33.334 11.5935 33.3755 11.7478 33.3755C11.9506 33.3755 12.1092 33.3198 12.2235 33.2083C12.3378 33.094 12.4106 32.9312 12.4421 32.7198L13.1235 32.7455C13.0606 33.1226 12.9121 33.414 12.6778 33.6198C12.4435 33.8226 12.1321 33.924 11.7435 33.924ZM14.9297 33.924C14.6268 33.924 14.3625 33.8598 14.1368 33.7312C13.914 33.6026 13.744 33.4198 13.6268 33.1826C13.5097 32.9426 13.4511 32.6612 13.4511 32.3383C13.4511 32.0155 13.5097 31.734 13.6268 31.494C13.744 31.254 13.914 31.0698 14.1368 30.9412C14.3625 30.8098 14.6268 30.744 14.9297 30.744C15.2354 30.744 15.4997 30.8098 15.7225 30.9412C15.9454 31.0698 16.1154 31.254 16.2325 31.494C16.3525 31.734 16.4125 32.0155 16.4125 32.3383C16.4125 32.6612 16.3525 32.9426 16.2325 33.1826C16.1154 33.4198 15.9454 33.6026 15.7225 33.7312C15.4997 33.8598 15.2354 33.924 14.9297 33.924ZM14.9297 33.3755C15.0982 33.3755 15.2425 33.3355 15.3625 33.2555C15.4854 33.1726 15.5797 33.054 15.6454 32.8998C15.7111 32.7426 15.744 32.5555 15.744 32.3383C15.744 32.1212 15.7111 31.934 15.6454 31.7769C15.5797 31.6198 15.4854 31.4998 15.3625 31.4169C15.2425 31.334 15.0982 31.2926 14.9297 31.2926C14.7611 31.2926 14.6154 31.334 14.4925 31.4169C14.3725 31.4998 14.2797 31.6198 14.214 31.7769C14.1511 31.934 14.1197 32.1212 14.1197 32.3383C14.1197 32.5555 14.1511 32.7426 14.214 32.8998C14.2797 33.054 14.3725 33.1726 14.4925 33.2555C14.6154 33.3355 14.7611 33.3755 14.9297 33.3755ZM16.811 30.8126H17.681L18.4567 33.0283L19.2281 30.8126H20.0981V33.8555H19.4467V31.884L18.731 33.8469H18.1739L17.4624 31.884V33.8555H16.811V30.8126ZM20.6363 30.8126H21.8535C22.0935 30.8126 22.3021 30.8526 22.4792 30.9326C22.6563 31.0126 22.7906 31.1283 22.8821 31.2798C22.9763 31.4283 23.0235 31.604 23.0235 31.8069C23.0235 32.0098 22.9763 32.1869 22.8821 32.3383C22.7906 32.4869 22.6563 32.6026 22.4792 32.6855C22.3021 32.7655 22.0935 32.8055 21.8535 32.8055H21.2878V33.8555H20.6363V30.8126ZM21.8149 32.2569C21.9892 32.2569 22.1221 32.2183 22.2135 32.1412C22.3078 32.064 22.3549 31.9526 22.3549 31.8069C22.3549 31.6612 22.3078 31.5512 22.2135 31.4769C22.1221 31.3998 21.9892 31.3612 21.8149 31.3612H21.2878V32.2569H21.8149ZM23.5158 30.8126H24.1672V33.6798L23.8072 33.3069H25.5687V33.8555H23.5158V30.8126ZM26.0395 30.8126H26.6909V33.8555H26.0395V30.8126ZM28.2115 30.8126H28.983L30.0801 33.8555H29.403L29.1801 33.2083H28.0101L27.7873 33.8555H27.1144L28.2115 30.8126ZM29.0001 32.6726L28.5973 31.4983L28.1944 32.6726H29.0001ZM30.501 30.8126H31.221L32.4296 32.9126V30.8126H33.081V33.8555H32.3524L31.1524 31.8283V33.8555H30.501V30.8126ZM34.4267 31.3612H33.5139V30.8126H35.991V31.3612H35.0824V33.8555H34.4267V31.3612Z\"\r\n          fill=\"#E4E4E7\"\r\n        />\r\n      </g>\r\n      <defs>\r\n        <filter\r\n          id=\"filter0_ddd_1_2028\"\r\n          x=\"0.857143\"\r\n          y=\"0.148996\"\r\n          width=\"44.2857\"\r\n          height=\"44.2857\"\r\n          filterUnits=\"userSpaceOnUse\"\r\n          colorInterpolationFilters=\"sRGB\"\r\n        >\r\n          <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feMorphology\r\n            radius=\"0.714286\"\r\n            operator=\"erode\"\r\n            in=\"SourceAlpha\"\r\n            result=\"effect1_dropShadow_1_2028\"\r\n          />\r\n          <feOffset dy=\"0.714286\" />\r\n          <feGaussianBlur stdDeviation=\"0.357143\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"BackgroundImageFix\"\r\n            result=\"effect1_dropShadow_1_2028\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feMorphology\r\n            radius=\"0.714286\"\r\n            operator=\"erode\"\r\n            in=\"SourceAlpha\"\r\n            result=\"effect2_dropShadow_1_2028\"\r\n          />\r\n          <feOffset dy=\"1.42857\" />\r\n          <feGaussianBlur stdDeviation=\"1.42857\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect1_dropShadow_1_2028\"\r\n            result=\"effect2_dropShadow_1_2028\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feMorphology\r\n            radius=\"0.714286\"\r\n            operator=\"dilate\"\r\n            in=\"SourceAlpha\"\r\n            result=\"effect3_dropShadow_1_2028\"\r\n          />\r\n          <feOffset />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.08 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect2_dropShadow_1_2028\"\r\n            result=\"effect3_dropShadow_1_2028\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in=\"SourceGraphic\"\r\n            in2=\"effect3_dropShadow_1_2028\"\r\n            result=\"shape\"\r\n          />\r\n        </filter>\r\n        <linearGradient\r\n          id=\"paint0_linear_1_2028\"\r\n          x1=\"9.88803\"\r\n          y1=\"6.55415\"\r\n          x2=\"36.0447\"\r\n          y2=\"35.5773\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop stopColor=\"#27272A\" />\r\n          <stop offset=\"1\" stopColor=\"#52525C\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint1_linear_1_2028\"\r\n          x1=\"30.5498\"\r\n          y1=\"10.0698\"\r\n          x2=\"20.9753\"\r\n          y2=\"31.2119\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.473541\" stopColor=\"#FAFAFA\" stopOpacity=\"0.7\" />\r\n          <stop offset=\"0.811446\" stopColor=\"#FAFAFA\" stopOpacity=\"0\" />\r\n        </linearGradient>\r\n      </defs>\r\n    </svg>\r\n  ),\r\n  gdpr: ({ className }: { className?: string }) => (\r\n    <svg\r\n      width=\"46\"\r\n      height=\"45\"\r\n      viewBox=\"0 0 46 45\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      className={cn('size-4', className)}\r\n    >\r\n      <g filter=\"url(#filter0_ddd_1_4914)\">\r\n        <rect\r\n          x=\"3\"\r\n          y=\"0.863281\"\r\n          width=\"40\"\r\n          height=\"40\"\r\n          rx=\"20\"\r\n          fill=\"url(#paint0_linear_1_4914)\"\r\n        />\r\n        <path\r\n          d=\"M30.6146 31.3062L31.2991 33.4127L33.5139 33.4127L31.7221 34.7145L32.4065 36.821L30.6146 35.5191L28.8228 36.821L29.5072 34.7145L27.7153 33.4127L29.9302 33.4127L30.6146 31.3062Z\"\r\n          fill=\"url(#paint1_linear_1_4914)\"\r\n        />\r\n        <path\r\n          d=\"M23.6857 35.4549L23.0013 33.3485L22.3168 35.4549H20.102L21.8938 36.7568L21.2094 38.8633L23.0013 37.5614L24.7932 38.8633L24.1087 36.7568L25.9006 35.4549H23.6857Z\"\r\n          fill=\"url(#paint2_linear_1_4914)\"\r\n        />\r\n        <path\r\n          d=\"M23.6857 4.96976L23.0013 2.86328L22.3168 4.96975L20.102 4.96975L21.8938 6.27163L21.2094 8.3781L23.0013 7.07623L24.7932 8.3781L24.1087 6.27163L25.9006 4.96975L23.6857 4.96976Z\"\r\n          fill=\"url(#paint3_linear_1_4914)\"\r\n        />\r\n        <path\r\n          d=\"M30.6226 4.90555L31.307 7.01202L33.5219 7.01202L31.73 8.3139L32.4144 10.4204L30.6226 9.1185L28.8307 10.4204L29.5151 8.3139L27.7233 7.01202L29.9381 7.01202L30.6226 4.90555Z\"\r\n          fill=\"url(#paint4_linear_1_4914)\"\r\n        />\r\n        <path\r\n          d=\"M16.0644 33.4127L15.38 31.3062L14.6955 33.4127H12.4807L14.2725 34.7145L13.5881 36.821L15.38 35.5191L17.1719 36.821L16.4874 34.7145L18.2793 33.4127L16.0644 33.4127Z\"\r\n          fill=\"url(#paint5_linear_1_4914)\"\r\n        />\r\n        <path\r\n          d=\"M36.1956 10.4846L36.8801 12.5911L39.095 12.5911L37.3031 13.8929L37.9875 15.9994L36.1956 14.6975L34.4038 15.9994L35.0882 13.8929L33.2963 12.5911L35.5112 12.5911L36.1956 10.4846Z\"\r\n          fill=\"url(#paint6_linear_1_4914)\"\r\n        />\r\n        <path\r\n          d=\"M10.4755 27.8336L9.79104 25.7272L9.1066 27.8336H6.89172L8.6836 29.1355L7.99916 31.242L9.79104 29.9401L11.5829 31.242L10.8985 29.1355L12.6903 27.8336L10.4755 27.8336Z\"\r\n          fill=\"url(#paint7_linear_1_4914)\"\r\n        />\r\n        <path\r\n          d=\"M38.2439 18.1059L38.9283 20.2123L41.1432 20.2123L39.3513 21.5142L40.0357 23.6207L38.2439 22.3188L36.452 23.6207L37.1364 21.5142L35.3446 20.2123H37.5594L38.2439 18.1059Z\"\r\n          fill=\"url(#paint8_linear_1_4914)\"\r\n        />\r\n        <path\r\n          d=\"M8.44313 20.2123L7.75869 18.1059L7.07425 20.2123H4.85938L6.65125 21.5142L5.96682 23.6207L7.75869 22.3188L9.55056 23.6207L8.86613 21.5142L10.658 20.2123H8.44313Z\"\r\n          fill=\"url(#paint9_linear_1_4914)\"\r\n        />\r\n        <path\r\n          d=\"M36.1956 25.7272L36.8801 27.8336H39.095L37.3031 29.1355L37.9875 31.242L36.1956 29.9401L34.4038 31.242L35.0882 29.1355L33.2963 27.8336L35.5112 27.8336L36.1956 25.7272Z\"\r\n          fill=\"url(#paint10_linear_1_4914)\"\r\n        />\r\n        <path\r\n          d=\"M10.4755 12.591L9.79103 10.4846L9.1066 12.591H6.89172L8.6836 13.8929L7.99916 15.9994L9.79103 14.6975L11.5829 15.9994L10.8985 13.8929L12.6903 12.591H10.4755Z\"\r\n          fill=\"url(#paint11_linear_1_4914)\"\r\n        />\r\n        <path\r\n          d=\"M16.0565 7.01202L15.372 4.90555L14.6876 7.01202H12.4727L14.2646 8.3139L13.5802 10.4204L15.372 9.1185L17.1639 10.4204L16.4795 8.3139L18.2714 7.01203L16.0565 7.01202Z\"\r\n          fill=\"url(#paint12_linear_1_4914)\"\r\n        />\r\n        <path\r\n          fillRule=\"evenodd\"\r\n          clipRule=\"evenodd\"\r\n          d=\"M15.2383 20.8682C15.2383 22.1882 15.9883 23.0882 17.2243 23.0882C17.8363 23.0882 18.3403 22.8302 18.5623 22.4162L18.5983 22.9922H19.2223V20.7542H17.3023V21.4802H18.1843C18.1363 21.9362 17.8243 22.2362 17.3263 22.2362C16.6243 22.2362 16.3123 21.6782 16.3123 20.8682C16.3123 20.0642 16.6483 19.4882 17.3083 19.4882C17.8063 19.4882 18.0523 19.7582 18.1303 20.2082L19.2103 20.1602C19.0123 19.2122 18.4303 18.6362 17.2963 18.6362C16.0003 18.6362 15.2383 19.5842 15.2383 20.8682ZM19.7261 18.7322V22.9922H21.2801C22.6601 22.9922 23.4341 22.2302 23.4341 20.8682C23.4341 19.5002 22.6481 18.7322 21.2441 18.7322H19.7261ZM21.2441 22.1402H20.7701V19.5842H21.2441C22.0121 19.5842 22.3601 19.9922 22.3601 20.8622C22.3601 21.7322 22.0121 22.1402 21.2441 22.1402ZM23.8059 22.6543V22.9922H24.8499V21.5822H25.5699C26.6139 21.5822 27.2499 21.0362 27.2499 20.1542C27.2499 19.2722 26.6139 18.7322 25.5699 18.7322H23.8059V22.1211H23.6798V22.6543H23.8059ZM25.4979 20.7242H24.8499V19.5842H25.4979C25.9239 19.5842 26.1819 19.7822 26.1819 20.1542C26.1819 20.5322 25.9179 20.7242 25.4979 20.7242ZM29.531 18.7322H27.581V22.9922H28.625V21.4862H29.423C29.831 21.4862 29.969 21.6362 29.993 21.9542L30.059 22.9922H31.127L31.025 21.7802C30.989 21.3542 30.767 21.1082 30.329 21.0482C30.797 20.9042 31.091 20.5202 31.091 19.9982C31.091 19.2302 30.479 18.7322 29.531 18.7322ZM29.369 20.6342H28.625V19.5842H29.357C29.789 19.5842 30.023 19.7642 30.023 20.1062C30.023 20.4422 29.789 20.6342 29.369 20.6342Z\"\r\n          fill=\"#101828\"\r\n        />\r\n      </g>\r\n      <defs>\r\n        <filter\r\n          id=\"filter0_ddd_1_4914\"\r\n          x=\"0.857143\"\r\n          y=\"0.148996\"\r\n          width=\"44.2857\"\r\n          height=\"44.2857\"\r\n          filterUnits=\"userSpaceOnUse\"\r\n          colorInterpolationFilters=\"sRGB\"\r\n        >\r\n          <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feMorphology\r\n            radius=\"0.714286\"\r\n            operator=\"erode\"\r\n            in=\"SourceAlpha\"\r\n            result=\"effect1_dropShadow_1_4914\"\r\n          />\r\n          <feOffset dy=\"0.714286\" />\r\n          <feGaussianBlur stdDeviation=\"0.357143\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"BackgroundImageFix\"\r\n            result=\"effect1_dropShadow_1_4914\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feMorphology\r\n            radius=\"0.714286\"\r\n            operator=\"erode\"\r\n            in=\"SourceAlpha\"\r\n            result=\"effect2_dropShadow_1_4914\"\r\n          />\r\n          <feOffset dy=\"1.42857\" />\r\n          <feGaussianBlur stdDeviation=\"1.42857\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect1_dropShadow_1_4914\"\r\n            result=\"effect2_dropShadow_1_4914\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feMorphology\r\n            radius=\"0.714286\"\r\n            operator=\"dilate\"\r\n            in=\"SourceAlpha\"\r\n            result=\"effect3_dropShadow_1_4914\"\r\n          />\r\n          <feOffset />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.08 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect2_dropShadow_1_4914\"\r\n            result=\"effect3_dropShadow_1_4914\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in=\"SourceGraphic\"\r\n            in2=\"effect3_dropShadow_1_4914\"\r\n            result=\"shape\"\r\n          />\r\n        </filter>\r\n        <linearGradient\r\n          id=\"paint0_linear_1_4914\"\r\n          x1=\"9.88803\"\r\n          y1=\"6.55415\"\r\n          x2=\"36.0447\"\r\n          y2=\"35.5773\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop stopColor=\"#E5E7EB\" />\r\n          <stop offset=\"1\" stopColor=\"#F9FAFB\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint1_linear_1_4914\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#364153\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#364153\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint2_linear_1_4914\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#364153\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#364153\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint3_linear_1_4914\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#364153\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#364153\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint4_linear_1_4914\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#364153\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#364153\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint5_linear_1_4914\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#364153\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#364153\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint6_linear_1_4914\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#364153\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#364153\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint7_linear_1_4914\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#364153\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#364153\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint8_linear_1_4914\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#364153\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#364153\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint9_linear_1_4914\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#364153\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#364153\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint10_linear_1_4914\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#364153\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#364153\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint11_linear_1_4914\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#364153\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#364153\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint12_linear_1_4914\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#364153\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#364153\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n      </defs>\r\n    </svg>\r\n  ),\r\n  gdprDark: ({ className }: { className?: string }) => (\r\n    <svg\r\n      width=\"46\"\r\n      height=\"45\"\r\n      viewBox=\"0 0 46 45\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      className={cn('size-4', className)}\r\n    >\r\n      <g filter=\"url(#filter0_ddd_1_2046)\">\r\n        <rect\r\n          x=\"3\"\r\n          y=\"0.863281\"\r\n          width=\"40\"\r\n          height=\"40\"\r\n          rx=\"20\"\r\n          fill=\"url(#paint0_linear_1_2046)\"\r\n        />\r\n        <path\r\n          d=\"M30.6146 31.3062L31.2991 33.4127L33.5139 33.4127L31.7221 34.7145L32.4065 36.821L30.6146 35.5191L28.8228 36.821L29.5072 34.7145L27.7153 33.4127L29.9302 33.4127L30.6146 31.3062Z\"\r\n          fill=\"url(#paint1_linear_1_2046)\"\r\n        />\r\n        <path\r\n          d=\"M23.6857 35.4549L23.0013 33.3485L22.3168 35.4549H20.102L21.8938 36.7568L21.2094 38.8633L23.0013 37.5614L24.7932 38.8633L24.1087 36.7568L25.9006 35.4549H23.6857Z\"\r\n          fill=\"url(#paint2_linear_1_2046)\"\r\n        />\r\n        <path\r\n          d=\"M23.6857 4.96976L23.0013 2.86328L22.3168 4.96975L20.102 4.96975L21.8938 6.27163L21.2094 8.3781L23.0013 7.07623L24.7932 8.3781L24.1087 6.27163L25.9006 4.96975L23.6857 4.96976Z\"\r\n          fill=\"url(#paint3_linear_1_2046)\"\r\n        />\r\n        <path\r\n          d=\"M30.6226 4.90555L31.307 7.01202L33.5219 7.01202L31.73 8.3139L32.4144 10.4204L30.6226 9.1185L28.8307 10.4204L29.5151 8.3139L27.7233 7.01202L29.9381 7.01202L30.6226 4.90555Z\"\r\n          fill=\"url(#paint4_linear_1_2046)\"\r\n        />\r\n        <path\r\n          d=\"M16.0644 33.4127L15.38 31.3062L14.6955 33.4127H12.4807L14.2725 34.7145L13.5881 36.821L15.38 35.5191L17.1719 36.821L16.4874 34.7145L18.2793 33.4127L16.0644 33.4127Z\"\r\n          fill=\"url(#paint5_linear_1_2046)\"\r\n        />\r\n        <path\r\n          d=\"M36.1956 10.4846L36.8801 12.5911L39.095 12.5911L37.3031 13.8929L37.9875 15.9994L36.1956 14.6975L34.4038 15.9994L35.0882 13.8929L33.2963 12.5911L35.5112 12.5911L36.1956 10.4846Z\"\r\n          fill=\"url(#paint6_linear_1_2046)\"\r\n        />\r\n        <path\r\n          d=\"M10.4755 27.8336L9.79104 25.7272L9.1066 27.8336H6.89172L8.6836 29.1355L7.99916 31.242L9.79104 29.9401L11.5829 31.242L10.8985 29.1355L12.6903 27.8336L10.4755 27.8336Z\"\r\n          fill=\"url(#paint7_linear_1_2046)\"\r\n        />\r\n        <path\r\n          d=\"M38.2439 18.1059L38.9283 20.2123L41.1432 20.2123L39.3513 21.5142L40.0357 23.6207L38.2439 22.3188L36.452 23.6207L37.1364 21.5142L35.3446 20.2123H37.5594L38.2439 18.1059Z\"\r\n          fill=\"url(#paint8_linear_1_2046)\"\r\n        />\r\n        <path\r\n          d=\"M8.44313 20.2123L7.75869 18.1059L7.07425 20.2123H4.85938L6.65125 21.5142L5.96682 23.6207L7.75869 22.3188L9.55056 23.6207L8.86613 21.5142L10.658 20.2123H8.44313Z\"\r\n          fill=\"url(#paint9_linear_1_2046)\"\r\n        />\r\n        <path\r\n          d=\"M36.1956 25.7272L36.8801 27.8336H39.095L37.3031 29.1355L37.9875 31.242L36.1956 29.9401L34.4038 31.242L35.0882 29.1355L33.2963 27.8336L35.5112 27.8336L36.1956 25.7272Z\"\r\n          fill=\"url(#paint10_linear_1_2046)\"\r\n        />\r\n        <path\r\n          d=\"M10.4755 12.591L9.79103 10.4846L9.1066 12.591H6.89172L8.6836 13.8929L7.99916 15.9994L9.79103 14.6975L11.5829 15.9994L10.8985 13.8929L12.6903 12.591H10.4755Z\"\r\n          fill=\"url(#paint11_linear_1_2046)\"\r\n        />\r\n        <path\r\n          d=\"M16.0565 7.01202L15.372 4.90555L14.6876 7.01202H12.4727L14.2646 8.3139L13.5802 10.4204L15.372 9.1185L17.1639 10.4204L16.4795 8.3139L18.2714 7.01203L16.0565 7.01202Z\"\r\n          fill=\"url(#paint12_linear_1_2046)\"\r\n        />\r\n        <path\r\n          d=\"M17.2242 23.0882C16.8122 23.0882 16.4582 22.9942 16.1622 22.8062C15.8662 22.6182 15.6382 22.3582 15.4782 22.0262C15.3182 21.6942 15.2382 21.3082 15.2382 20.8682C15.2382 20.4402 15.3202 20.0582 15.4842 19.7222C15.6482 19.3862 15.8822 19.1222 16.1862 18.9302C16.4942 18.7342 16.8642 18.6362 17.2962 18.6362C17.6762 18.6362 17.9942 18.6982 18.2502 18.8222C18.5102 18.9422 18.7182 19.1162 18.8742 19.3442C19.0342 19.5722 19.1462 19.8442 19.2102 20.1602L18.1302 20.2082C18.0902 19.9802 18.0062 19.8042 17.8782 19.6802C17.7502 19.5522 17.5602 19.4882 17.3082 19.4882C17.0882 19.4882 16.9042 19.5482 16.7562 19.6682C16.6082 19.7882 16.4962 19.9522 16.4202 20.1602C16.3482 20.3642 16.3122 20.6002 16.3122 20.8682C16.3122 21.1362 16.3482 21.3742 16.4202 21.5822C16.4922 21.7862 16.6022 21.9462 16.7502 22.0622C16.9022 22.1782 17.0942 22.2362 17.3262 22.2362C17.4942 22.2362 17.6382 22.2042 17.7582 22.1402C17.8822 22.0762 17.9802 21.9882 18.0522 21.8762C18.1242 21.7602 18.1682 21.6282 18.1842 21.4802H17.3022V20.7542H19.2222V22.9922H18.5982L18.5442 22.1042L18.6642 22.1402C18.6242 22.3282 18.5362 22.4942 18.4002 22.6382C18.2682 22.7782 18.1002 22.8882 17.8962 22.9682C17.6962 23.0482 17.4722 23.0882 17.2242 23.0882ZM19.726 22.9922V18.7322H21.244C21.948 18.7322 22.488 18.9182 22.864 19.2902C23.244 19.6582 23.434 20.1842 23.434 20.8682C23.434 21.5482 23.248 22.0722 22.876 22.4402C22.504 22.8082 21.972 22.9922 21.28 22.9922H19.726ZM20.77 22.1402H21.244C21.628 22.1402 21.91 22.0362 22.09 21.8282C22.27 21.6202 22.36 21.2982 22.36 20.8622C22.36 20.4262 22.27 20.1042 22.09 19.8962C21.91 19.6882 21.628 19.5842 21.244 19.5842H20.77V22.1402ZM23.8058 22.9922V18.7322H25.5698C26.0938 18.7322 26.5038 18.8602 26.7998 19.1162C27.0998 19.3682 27.2498 19.7142 27.2498 20.1542C27.2498 20.4462 27.1818 20.7002 27.0458 20.9162C26.9138 21.1282 26.7218 21.2922 26.4698 21.4082C26.2178 21.5242 25.9178 21.5822 25.5698 21.5822H24.8498V22.9922H23.8058ZM24.8498 20.7242H25.4978C25.7098 20.7242 25.8758 20.6762 25.9958 20.5802C26.1198 20.4842 26.1818 20.3422 26.1818 20.1542C26.1818 19.9702 26.1218 19.8302 26.0018 19.7342C25.8818 19.6342 25.7138 19.5842 25.4978 19.5842H24.8498V20.7242ZM27.5809 22.9922V18.7322H29.5309C29.8469 18.7322 30.1209 18.7842 30.3529 18.8882C30.5889 18.9922 30.7709 19.1402 30.8989 19.3322C31.0269 19.5202 31.0909 19.7422 31.0909 19.9982C31.0909 20.1982 31.0509 20.3742 30.9709 20.5262C30.8909 20.6782 30.7789 20.8042 30.6349 20.9042C30.4949 21.0002 30.3269 21.0642 30.1309 21.0962L30.1129 21.0362C30.4049 21.0362 30.6249 21.0982 30.7729 21.2222C30.9249 21.3462 31.0089 21.5322 31.0249 21.7802L31.1269 22.9922H30.0589L29.9929 21.9542C29.9809 21.7942 29.9329 21.6762 29.8489 21.6002C29.7689 21.5242 29.6269 21.4862 29.4229 21.4862H28.6249V22.9922H27.5809ZM28.6249 20.6342H29.3689C29.5809 20.6342 29.7429 20.5882 29.8549 20.4962C29.9669 20.4042 30.0229 20.2742 30.0229 20.1062C30.0229 19.9342 29.9649 19.8042 29.8489 19.7162C29.7369 19.6282 29.5729 19.5842 29.3569 19.5842H28.6249V20.6342Z\"\r\n          fill=\"#D4D4D8\"\r\n        />\r\n      </g>\r\n      <defs>\r\n        <filter\r\n          id=\"filter0_ddd_1_2046\"\r\n          x=\"0.857143\"\r\n          y=\"0.148996\"\r\n          width=\"44.2857\"\r\n          height=\"44.2857\"\r\n          filterUnits=\"userSpaceOnUse\"\r\n          colorInterpolationFilters=\"sRGB\"\r\n        >\r\n          <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feMorphology\r\n            radius=\"0.714286\"\r\n            operator=\"erode\"\r\n            in=\"SourceAlpha\"\r\n            result=\"effect1_dropShadow_1_2046\"\r\n          />\r\n          <feOffset dy=\"0.714286\" />\r\n          <feGaussianBlur stdDeviation=\"0.357143\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"BackgroundImageFix\"\r\n            result=\"effect1_dropShadow_1_2046\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feMorphology\r\n            radius=\"0.714286\"\r\n            operator=\"erode\"\r\n            in=\"SourceAlpha\"\r\n            result=\"effect2_dropShadow_1_2046\"\r\n          />\r\n          <feOffset dy=\"1.42857\" />\r\n          <feGaussianBlur stdDeviation=\"1.42857\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect1_dropShadow_1_2046\"\r\n            result=\"effect2_dropShadow_1_2046\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feMorphology\r\n            radius=\"0.714286\"\r\n            operator=\"dilate\"\r\n            in=\"SourceAlpha\"\r\n            result=\"effect3_dropShadow_1_2046\"\r\n          />\r\n          <feOffset />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.08 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect2_dropShadow_1_2046\"\r\n            result=\"effect3_dropShadow_1_2046\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in=\"SourceGraphic\"\r\n            in2=\"effect3_dropShadow_1_2046\"\r\n            result=\"shape\"\r\n          />\r\n        </filter>\r\n        <linearGradient\r\n          id=\"paint0_linear_1_2046\"\r\n          x1=\"9.88803\"\r\n          y1=\"6.55415\"\r\n          x2=\"36.0447\"\r\n          y2=\"35.5773\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop stopColor=\"#27272A\" />\r\n          <stop offset=\"1\" stopColor=\"#52525C\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint1_linear_1_2046\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#FAFAFA\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#FAFAFA\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint2_linear_1_2046\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#FAFAFA\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#FAFAFA\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint3_linear_1_2046\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#FAFAFA\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#FAFAFA\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint4_linear_1_2046\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#FAFAFA\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#FAFAFA\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint5_linear_1_2046\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#FAFAFA\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#FAFAFA\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint6_linear_1_2046\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#FAFAFA\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#FAFAFA\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint7_linear_1_2046\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#FAFAFA\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#FAFAFA\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint8_linear_1_2046\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#FAFAFA\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#FAFAFA\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint9_linear_1_2046\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#FAFAFA\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#FAFAFA\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint10_linear_1_2046\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#FAFAFA\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#FAFAFA\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint11_linear_1_2046\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#FAFAFA\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#FAFAFA\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint12_linear_1_2046\"\r\n          x1=\"15.8864\"\r\n          y1=\"51.1315\"\r\n          x2=\"29.5116\"\r\n          y2=\"5.36433\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop offset=\"0.188554\" stopColor=\"#FAFAFA\" stopOpacity=\"0\" />\r\n          <stop offset=\"0.526459\" stopColor=\"#FAFAFA\" stopOpacity=\"0.7\" />\r\n        </linearGradient>\r\n      </defs>\r\n    </svg>\r\n  ),\r\n  vercel: () => (\r\n    <svg\r\n      width=\"52\"\r\n      height=\"60\"\r\n      viewBox=\"0 0 52 60\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <g filter=\"url(#filter0_dddd_1_4108)\">\r\n        <path\r\n          d=\"M5 22C5 10.402 14.402 1 26 1C37.598 1 47 10.402 47 22C47 33.598 37.598 43 26 43C14.402 43 5 33.598 5 22Z\"\r\n          fill=\"black\"\r\n        />\r\n        <g clipPath=\"url(#clip0_1_4108)\">\r\n          <path d=\"M26 8L39.5 31.3829H12.5L26 8Z\" fill=\"white\" />\r\n        </g>\r\n      </g>\r\n      <defs>\r\n        <filter\r\n          id=\"filter0_dddd_1_4108\"\r\n          x=\"0\"\r\n          y=\"0\"\r\n          width=\"52\"\r\n          height=\"60\"\r\n          filterUnits=\"userSpaceOnUse\"\r\n          colorInterpolationFilters=\"sRGB\"\r\n        >\r\n          <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"1\" />\r\n          <feGaussianBlur stdDeviation=\"1\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"BackgroundImageFix\"\r\n            result=\"effect1_dropShadow_1_4108\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"3\" />\r\n          <feGaussianBlur stdDeviation=\"1.5\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.09 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect1_dropShadow_1_4108\"\r\n            result=\"effect2_dropShadow_1_4108\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"7\" />\r\n          <feGaussianBlur stdDeviation=\"2\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect2_dropShadow_1_4108\"\r\n            result=\"effect3_dropShadow_1_4108\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"12\" />\r\n          <feGaussianBlur stdDeviation=\"2.5\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.01 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect3_dropShadow_1_4108\"\r\n            result=\"effect4_dropShadow_1_4108\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in=\"SourceGraphic\"\r\n            in2=\"effect4_dropShadow_1_4108\"\r\n            result=\"shape\"\r\n          />\r\n        </filter>\r\n        <clipPath id=\"clip0_1_4108\">\r\n          <rect\r\n            width=\"27\"\r\n            height=\"23.4141\"\r\n            fill=\"white\"\r\n            transform=\"translate(12.5 8)\"\r\n          />\r\n        </clipPath>\r\n      </defs>\r\n    </svg>\r\n  ),\r\n  replit: () => (\r\n    <svg\r\n      width=\"52\"\r\n      height=\"60\"\r\n      viewBox=\"0 0 52 60\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <g filter=\"url(#filter0_dddd_1_4111)\">\r\n        <path\r\n          d=\"M5 22C5 10.402 14.402 1 26 1C37.598 1 47 10.402 47 22C47 33.598 37.598 43 26 43C14.402 43 5 33.598 5 22Z\"\r\n          fill=\"white\"\r\n        />\r\n        <g clipPath=\"url(#clip0_1_4111)\">\r\n          <path\r\n            d=\"M15.5 8.875C15.5 7.83947 16.3395 7 17.375 7H26.125C27.1605 7 28 7.83947 28 8.875V17H17.375C16.3395 17 15.5 16.1605 15.5 15.125V8.875Z\"\r\n            fill=\"#F26207\"\r\n          />\r\n          <path\r\n            d=\"M28 17H38.625C39.6605 17 40.5 17.8395 40.5 18.875V25.125C40.5 26.1605 39.6605 27 38.625 27H28V17Z\"\r\n            fill=\"#F26207\"\r\n          />\r\n          <path\r\n            d=\"M15.5 28.875C15.5 27.8395 16.3395 27 17.375 27H28V35.125C28 36.1605 27.1605 37 26.125 37H17.375C16.3395 37 15.5 36.1605 15.5 35.125V28.875Z\"\r\n            fill=\"#F26207\"\r\n          />\r\n        </g>\r\n      </g>\r\n      <defs>\r\n        <filter\r\n          id=\"filter0_dddd_1_4111\"\r\n          x=\"0\"\r\n          y=\"0\"\r\n          width=\"52\"\r\n          height=\"60\"\r\n          filterUnits=\"userSpaceOnUse\"\r\n          colorInterpolationFilters=\"sRGB\"\r\n        >\r\n          <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"1\" />\r\n          <feGaussianBlur stdDeviation=\"1\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"BackgroundImageFix\"\r\n            result=\"effect1_dropShadow_1_4111\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"3\" />\r\n          <feGaussianBlur stdDeviation=\"1.5\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.09 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect1_dropShadow_1_4111\"\r\n            result=\"effect2_dropShadow_1_4111\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"7\" />\r\n          <feGaussianBlur stdDeviation=\"2\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect2_dropShadow_1_4111\"\r\n            result=\"effect3_dropShadow_1_4111\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"12\" />\r\n          <feGaussianBlur stdDeviation=\"2.5\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.01 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect3_dropShadow_1_4111\"\r\n            result=\"effect4_dropShadow_1_4111\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in=\"SourceGraphic\"\r\n            in2=\"effect4_dropShadow_1_4111\"\r\n            result=\"shape\"\r\n          />\r\n        </filter>\r\n        <clipPath id=\"clip0_1_4111\">\r\n          <rect\r\n            width=\"25\"\r\n            height=\"30\"\r\n            fill=\"white\"\r\n            transform=\"translate(15.5 7)\"\r\n          />\r\n        </clipPath>\r\n      </defs>\r\n    </svg>\r\n  ),\r\n  posthog: () => (\r\n    <svg\r\n      width=\"52\"\r\n      height=\"60\"\r\n      viewBox=\"0 0 52 60\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <g filter=\"url(#filter0_dddd_1_4116)\">\r\n        <path\r\n          d=\"M5 22C5 10.402 14.402 1 26 1C37.598 1 47 10.402 47 22C47 33.598 37.598 43 26 43C14.402 43 5 33.598 5 22Z\"\r\n          fill=\"#EEEFE8\"\r\n        />\r\n        <path\r\n          d=\"M18.2508 23.0069C18.1951 23.118 18.1096 23.2115 18.0038 23.2768C17.898 23.3421 17.7762 23.3767 17.6518 23.3767C17.5275 23.3767 17.4057 23.3421 17.2999 23.2768C17.1941 23.2115 17.1086 23.118 17.0529 23.0069L16.462 21.8257C16.4155 21.7327 16.3913 21.6302 16.3913 21.5262C16.3913 21.4223 16.4155 21.3197 16.462 21.2267L17.0529 20.0456C17.1086 19.9344 17.1941 19.841 17.2999 19.7757C17.4057 19.7104 17.5275 19.6758 17.6518 19.6758C17.7762 19.6758 17.898 19.7104 18.0038 19.7757C18.1096 19.841 18.1951 19.9344 18.2508 20.0456L18.8417 21.2267C18.8882 21.3197 18.9124 21.4223 18.9124 21.5262C18.9124 21.6302 18.8882 21.7327 18.8417 21.8257L18.2508 23.0069ZM18.2508 29.7047C18.1951 29.8158 18.1096 29.9093 18.0038 29.9746C17.898 30.0399 17.7762 30.0745 17.6518 30.0745C17.5275 30.0745 17.4057 30.0399 17.2999 29.9746C17.1941 29.9093 17.1086 29.8158 17.0529 29.7047L16.4613 28.5235C16.4148 28.4305 16.3906 28.328 16.3906 28.224C16.3906 28.1201 16.4148 28.0176 16.4613 27.9246L17.0522 26.7434C17.1079 26.6322 17.1934 26.5388 17.2992 26.4735C17.405 26.4082 17.5269 26.3736 17.6512 26.3736C17.7755 26.3736 17.8974 26.4082 18.0031 26.4735C18.1089 26.5388 18.1944 26.6322 18.2501 26.7434L18.8411 27.9246C18.8875 28.0176 18.9117 28.1201 18.9117 28.224C18.9117 28.328 18.8875 28.4305 18.8411 28.5235L18.2508 29.7047Z\"\r\n          fill=\"#1D4AFF\"\r\n        />\r\n        <path\r\n          d=\"M10.9531 27.1607C10.9531 26.5644 11.6747 26.2649 12.0968 26.687L15.1673 29.7575C15.5894 30.1796 15.2906 30.9019 14.6936 30.9019H11.6231C11.4454 30.9019 11.275 30.8313 11.1494 30.7056C11.0237 30.58 10.9531 30.4096 10.9531 30.2319V27.1607ZM10.9531 23.926C10.9531 24.0141 10.9704 24.1013 11.004 24.1827C11.0377 24.2641 11.0871 24.3381 11.1494 24.4004L17.4546 30.7049C17.5168 30.7672 17.5906 30.8167 17.6719 30.8505C17.7532 30.8843 17.8403 30.9018 17.9283 30.9019H21.3921C21.9884 30.9019 22.2879 30.1803 21.8658 29.7582L12.0975 19.9899C11.6747 19.5671 10.9531 19.8659 10.9531 20.4622V23.926ZM10.9531 17.2282C10.9532 17.4059 11.0238 17.5762 11.1494 17.7019L24.1518 30.7062C24.2774 30.8319 24.4478 30.9025 24.6255 30.9025H28.0893C28.6856 30.9025 28.985 30.1803 28.563 29.7582L12.0968 13.2914C11.6747 12.8693 10.9531 13.1681 10.9531 13.765V17.2282ZM17.6509 17.2282C17.651 17.4059 17.7216 17.5762 17.8472 17.7019L29.9029 29.7582C30.325 30.1803 31.0466 29.8808 31.0466 29.2839V25.8207C31.0465 25.643 30.9759 25.4727 30.8503 25.347L18.7946 13.2914C18.3725 12.8693 17.6509 13.1681 17.6509 13.765V17.2282ZM25.4924 13.2914C25.0703 12.8693 24.3488 13.1681 24.3488 13.765V17.2289C24.349 17.4063 24.4196 17.5764 24.5451 17.7019L29.9029 23.0604C30.325 23.4825 31.0466 23.183 31.0466 22.586V19.1229C31.0465 18.9452 30.9759 18.7748 30.8503 18.6492L25.4924 13.2914Z\"\r\n          fill=\"#F9BD2B\"\r\n        />\r\n        <path\r\n          d=\"M39.4424 27.2418L33.1358 20.9359C32.7138 20.5138 31.9922 20.8126 31.9922 21.4096V30.2312C31.9922 30.4089 32.0628 30.5793 32.1884 30.705C32.3141 30.8306 32.4845 30.9012 32.6622 30.9012H42.4305C42.6082 30.9012 42.7786 30.8306 42.9043 30.705C43.0299 30.5793 43.1005 30.4089 43.1005 30.2312V29.4273C43.1005 29.0574 42.7997 28.762 42.4325 28.7144C41.3008 28.567 40.2495 28.0488 39.4424 27.2418ZM35.2074 28.7579C34.9231 28.7579 34.6505 28.645 34.4494 28.444C34.2484 28.2429 34.1355 27.9703 34.1355 27.686C34.1355 27.4017 34.2484 27.129 34.4494 26.928C34.6505 26.7269 34.9231 26.614 35.2074 26.614C35.4917 26.614 35.7644 26.7269 35.9654 26.928C36.1665 27.129 36.2794 27.4017 36.2794 27.686C36.2794 27.9703 36.1665 28.2429 35.9654 28.444C35.7644 28.645 35.4917 28.7579 35.2074 28.7579Z\"\r\n          fill=\"black\"\r\n        />\r\n        <path\r\n          d=\"M10.9531 30.2319C10.9531 30.4096 11.0237 30.58 11.1494 30.7056C11.275 30.8313 11.4454 30.9019 11.6231 30.9019H14.6936C15.2906 30.9019 15.5894 30.1796 15.1673 29.7575L12.0968 26.687C11.6747 26.2649 10.9531 26.5637 10.9531 27.1607V30.2319ZM17.6509 18.8455L12.0968 13.2914C11.6747 12.8693 10.9531 13.1681 10.9531 13.765V17.2289C10.9533 17.4063 11.0239 17.5764 11.1494 17.7019L17.6509 24.204V18.8455ZM12.0968 19.9892C11.6747 19.5671 10.9531 19.8659 10.9531 20.4629V23.9267C10.9533 24.1041 11.0239 24.2742 11.1494 24.3997L17.6509 30.9019V25.5433L12.0968 19.9892Z\"\r\n          fill=\"#1D4AFF\"\r\n        />\r\n        <path\r\n          d=\"M24.3463 19.1229C24.3462 18.9452 24.2756 18.7748 24.15 18.6492L18.7921 13.2914C18.37 12.8693 17.6484 13.1681 17.6484 13.765V17.2289C17.6487 17.4063 17.7193 17.5764 17.8447 17.7019L24.3463 24.204V19.1222V19.1229ZM17.6484 30.9012H21.389C21.9859 30.9012 22.2847 30.1796 21.8626 29.7575L17.6484 25.5433V30.9019V30.9012ZM17.6484 18.8455V23.926C17.6484 24.0141 17.6657 24.1013 17.6994 24.1827C17.733 24.2641 17.7824 24.3381 17.8447 24.4004L24.3463 30.9019V25.8207C24.3462 25.643 24.2756 25.4727 24.15 25.347L17.6484 18.8455Z\"\r\n          fill=\"#F54E00\"\r\n        />\r\n      </g>\r\n      <defs>\r\n        <filter\r\n          id=\"filter0_dddd_1_4116\"\r\n          x=\"0\"\r\n          y=\"0\"\r\n          width=\"52\"\r\n          height=\"60\"\r\n          filterUnits=\"userSpaceOnUse\"\r\n          colorInterpolationFilters=\"sRGB\"\r\n        >\r\n          <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"1\" />\r\n          <feGaussianBlur stdDeviation=\"1\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"BackgroundImageFix\"\r\n            result=\"effect1_dropShadow_1_4116\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"3\" />\r\n          <feGaussianBlur stdDeviation=\"1.5\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.09 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect1_dropShadow_1_4116\"\r\n            result=\"effect2_dropShadow_1_4116\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"7\" />\r\n          <feGaussianBlur stdDeviation=\"2\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect2_dropShadow_1_4116\"\r\n            result=\"effect3_dropShadow_1_4116\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"12\" />\r\n          <feGaussianBlur stdDeviation=\"2.5\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.01 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect3_dropShadow_1_4116\"\r\n            result=\"effect4_dropShadow_1_4116\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in=\"SourceGraphic\"\r\n            in2=\"effect4_dropShadow_1_4116\"\r\n            result=\"shape\"\r\n          />\r\n        </filter>\r\n      </defs>\r\n    </svg>\r\n  ),\r\n  googleDrive: () => (\r\n    <svg\r\n      width=\"100\"\r\n      height=\"100\"\r\n      viewBox=\"0 0 87.3 78\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <path\r\n        d=\"m6.6 66.85 3.85 6.65c.8 1.4 1.95 2.5 3.3 3.3l13.75-23.8h-27.5c0 1.55.4 3.1 1.2 4.5z\"\r\n        fill=\"#0066da\"\r\n      />\r\n      <path\r\n        d=\"m43.65 25-13.75-23.8c-1.35.8-2.5 1.9-3.3 3.3l-25.4 44a9.06 9.06 0 0 0 -1.2 4.5h27.5z\"\r\n        fill=\"#00ac47\"\r\n      />\r\n      <path\r\n        d=\"m73.55 76.8c1.35-.8 2.5-1.9 3.3-3.3l1.6-2.75 7.65-13.25c.8-1.4 1.2-2.95 1.2-4.5h-27.502l5.852 11.5z\"\r\n        fill=\"#ea4335\"\r\n      />\r\n      <path\r\n        d=\"m43.65 25 13.75-23.8c-1.35-.8-2.9-1.2-4.5-1.2h-18.5c-1.6 0-3.15.45-4.5 1.2z\"\r\n        fill=\"#00832d\"\r\n      />\r\n      <path\r\n        d=\"m59.8 53h-32.3l-13.75 23.8c1.35.8 2.9 1.2 4.5 1.2h50.8c1.6 0 3.15-.45 4.5-1.2z\"\r\n        fill=\"#2684fc\"\r\n      />\r\n      <path\r\n        d=\"m73.4 26.5-12.7-22c-.8-1.4-1.95-2.5-3.3-3.3l-13.75 23.8 16.15 28h27.45c0-1.55-.4-3.1-1.2-4.5z\"\r\n        fill=\"#ffba00\"\r\n      />\r\n    </svg>\r\n  ),\r\n  workos: () => (\r\n    <svg\r\n      width=\"52\"\r\n      height=\"60\"\r\n      viewBox=\"0 0 52 60\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <g filter=\"url(#filter0_dddd_1_4101)\">\r\n        <path\r\n          d=\"M5 22C5 10.402 14.402 1 26 1C37.598 1 47 10.402 47 22C47 33.598 37.598 43 26 43C14.402 43 5 33.598 5 22Z\"\r\n          fill=\"#6363F1\"\r\n        />\r\n        <g clipPath=\"url(#clip0_1_4101)\">\r\n          <path\r\n            d=\"M12 22.0011C12 22.557 12.1516 23.0624 12.4043 23.5173L17.3069 32.0083C17.8123 32.8675 18.5704 33.5751 19.5307 33.8783C21.3502 34.4848 23.3213 33.7267 24.2816 32.1094L25.444 30.0372L20.7942 21.9505L25.7473 13.409L26.9097 11.3873C27.2635 10.7808 27.7184 10.2754 28.2744 9.87109H20.6931C19.3791 9.87109 18.1155 10.5787 17.4585 11.7411L12.4043 20.4848C12.1516 20.9397 12 21.4451 12 22.0011Z\"\r\n            fill=\"white\"\r\n          />\r\n          <path\r\n            d=\"M40.0009 22.0019C40.0009 21.446 39.8493 20.9405 39.5966 20.4857L34.6435 11.8936C33.6832 10.2257 31.7121 9.51816 29.8926 10.1247C28.9323 10.4279 28.1742 11.1355 27.6688 11.9947L26.5063 13.9153L31.2067 22.0019L26.2536 30.5434L25.0912 32.6156C24.7374 33.2221 24.2825 33.7275 23.7266 34.1319H31.3583C32.6724 34.1319 33.9359 33.4243 34.593 32.2618L39.6471 23.5182C39.8493 23.0633 40.0009 22.5579 40.0009 22.0019Z\"\r\n            fill=\"white\"\r\n          />\r\n        </g>\r\n      </g>\r\n      <defs>\r\n        <filter\r\n          id=\"filter0_dddd_1_4101\"\r\n          x=\"0\"\r\n          y=\"0\"\r\n          width=\"52\"\r\n          height=\"60\"\r\n          filterUnits=\"userSpaceOnUse\"\r\n          colorInterpolationFilters=\"sRGB\"\r\n        >\r\n          <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"1\" />\r\n          <feGaussianBlur stdDeviation=\"1\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"BackgroundImageFix\"\r\n            result=\"effect1_dropShadow_1_4101\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"3\" />\r\n          <feGaussianBlur stdDeviation=\"1.5\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.09 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect1_dropShadow_1_4101\"\r\n            result=\"effect2_dropShadow_1_4101\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"7\" />\r\n          <feGaussianBlur stdDeviation=\"2\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect2_dropShadow_1_4101\"\r\n            result=\"effect3_dropShadow_1_4101\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"12\" />\r\n          <feGaussianBlur stdDeviation=\"2.5\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.01 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect3_dropShadow_1_4101\"\r\n            result=\"effect4_dropShadow_1_4101\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in=\"SourceGraphic\"\r\n            in2=\"effect4_dropShadow_1_4101\"\r\n            result=\"shape\"\r\n          />\r\n        </filter>\r\n        <clipPath id=\"clip0_1_4101\">\r\n          <rect\r\n            width=\"28\"\r\n            height=\"28\"\r\n            fill=\"white\"\r\n            transform=\"translate(12 8)\"\r\n          />\r\n        </clipPath>\r\n      </defs>\r\n    </svg>\r\n  ),\r\n  runwayml: () => (\r\n    <svg\r\n      width=\"52\"\r\n      height=\"60\"\r\n      viewBox=\"0 0 52 60\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <g filter=\"url(#filter0_dddd_1_4106)\">\r\n        <path\r\n          d=\"M5 22C5 10.402 14.402 1 26 1C37.598 1 47 10.402 47 22C47 33.598 37.598 43 26 43C14.402 43 5 33.598 5 22Z\"\r\n          fill=\"#101828\"\r\n        />\r\n        <path\r\n          d=\"M37.678 13.2664C38.3954 15.4976 38.2063 17.5358 36.7194 19.377C36.0513 20.2045 35.105 20.7428 33.2308 21.2226C33.6954 21.6556 34.1217 22.0294 34.521 22.4302C35.2929 23.2052 36.0595 23.986 36.8108 24.781C38.5668 26.639 38.5001 29.3228 37.3076 31.4453C35.7329 34.2479 31.7191 35.1571 29.2989 33.0723C28.1452 32.0786 27.0621 31.0026 25.9522 29.9584C25.7293 29.7485 25.529 29.5145 25.2864 29.2579C25.0948 29.8493 24.9711 30.4074 24.7399 30.9169C23.4524 33.7547 19.82 34.9039 16.8601 33.4455C14.8504 32.4552 13.9101 30.7587 13.8784 28.6156C13.8134 24.2092 13.8339 19.8011 13.8679 15.3939C13.8907 12.4321 16.3064 9.88977 19.2617 9.83918C23.7709 9.762 28.2832 9.77292 32.793 9.83779C35.1105 9.87113 36.753 11.063 37.678 13.2664ZM21.0218 13.8121C20.2809 13.2306 19.4728 13.0218 18.6063 13.4803C17.7302 13.9439 17.3212 14.6789 17.3266 15.7004C17.3488 19.9172 17.3308 24.134 17.3447 28.3508C17.346 28.7318 17.3968 29.1459 17.5522 29.4864C17.9764 30.415 19.1143 30.8701 20.1817 30.6052C21.2025 30.3518 21.8008 29.5747 21.8013 28.4783C21.804 24.1995 21.7934 19.9206 21.8116 15.6418C21.8147 14.9289 21.594 14.3552 21.0218 13.8121ZM25.8643 21.1924C25.6845 21.2009 25.5046 21.2095 25.2832 21.22C25.2832 22.2251 25.2729 23.1939 25.2932 24.1622C25.2967 24.3236 25.3961 24.5182 25.513 24.6367C27.31 26.4581 29.1012 28.2862 30.9369 30.0681C31.2679 30.3895 31.7728 30.6269 32.2308 30.7023C33.164 30.8562 34.1069 30.2305 34.5663 29.2999C34.9496 28.5235 34.7691 27.5689 34.0724 26.8589C32.4945 25.2511 30.8899 23.6692 29.3299 22.0444C28.7194 21.4085 28.0705 21.0385 27.1623 21.1823C26.7788 21.243 26.3776 21.1924 25.8643 21.1924ZM29.4434 13.2325C27.9264 13.2325 26.4094 13.2325 25.0086 13.2325C25.1354 14.7766 25.2569 16.2585 25.3812 17.7726C27.7177 17.7726 30.0693 17.7785 32.4208 17.7705C33.8172 17.7656 34.7019 16.8615 34.6935 15.4759C34.6855 14.157 33.7615 13.2426 32.4172 13.2339C31.4674 13.2276 30.5175 13.2325 29.4434 13.2325Z\"\r\n          fill=\"#FEFEFE\"\r\n        />\r\n      </g>\r\n      <defs>\r\n        <filter\r\n          id=\"filter0_dddd_1_4106\"\r\n          x=\"0\"\r\n          y=\"0\"\r\n          width=\"52\"\r\n          height=\"60\"\r\n          filterUnits=\"userSpaceOnUse\"\r\n          colorInterpolationFilters=\"sRGB\"\r\n        >\r\n          <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"1\" />\r\n          <feGaussianBlur stdDeviation=\"1\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"BackgroundImageFix\"\r\n            result=\"effect1_dropShadow_1_4106\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"3\" />\r\n          <feGaussianBlur stdDeviation=\"1.5\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.09 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect1_dropShadow_1_4106\"\r\n            result=\"effect2_dropShadow_1_4106\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"7\" />\r\n          <feGaussianBlur stdDeviation=\"2\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect2_dropShadow_1_4106\"\r\n            result=\"effect3_dropShadow_1_4106\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"12\" />\r\n          <feGaussianBlur stdDeviation=\"2.5\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.01 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect3_dropShadow_1_4106\"\r\n            result=\"effect4_dropShadow_1_4106\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in=\"SourceGraphic\"\r\n            in2=\"effect4_dropShadow_1_4106\"\r\n            result=\"shape\"\r\n          />\r\n        </filter>\r\n      </defs>\r\n    </svg>\r\n  ),\r\n  gemini: () => (\r\n    <svg\r\n      width=\"52\"\r\n      height=\"60\"\r\n      viewBox=\"0 0 52 60\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <g filter=\"url(#filter0_dddd_1_4143)\">\r\n        <path\r\n          d=\"M5 22C5 10.402 14.402 1 26 1C37.598 1 47 10.402 47 22C47 33.598 37.598 43 26 43C14.402 43 5 33.598 5 22Z\"\r\n          fill=\"white\"\r\n        />\r\n        <g clipPath=\"url(#clip0_1_4143)\">\r\n          <path\r\n            d=\"M26 34C25.5426 30.9809 24.131 28.1874 21.9718 26.0282C19.8126 23.869 17.0191 22.4574 14 22C17.0191 21.5426 19.8126 20.131 21.9718 17.9718C24.131 15.8126 25.5426 13.0191 26 10C26.4575 13.0191 27.8692 15.8125 30.0283 17.9717C32.1875 20.1308 34.9809 21.5425 38 22C34.9809 22.4575 32.1875 23.8692 30.0283 26.0283C27.8692 28.1875 26.4575 30.9809 26 34Z\"\r\n            fill=\"url(#paint0_linear_1_4143)\"\r\n          />\r\n        </g>\r\n      </g>\r\n      <defs>\r\n        <filter\r\n          id=\"filter0_dddd_1_4143\"\r\n          x=\"0\"\r\n          y=\"0\"\r\n          width=\"52\"\r\n          height=\"60\"\r\n          filterUnits=\"userSpaceOnUse\"\r\n          colorInterpolationFilters=\"sRGB\"\r\n        >\r\n          <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"1\" />\r\n          <feGaussianBlur stdDeviation=\"1\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"BackgroundImageFix\"\r\n            result=\"effect1_dropShadow_1_4143\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"3\" />\r\n          <feGaussianBlur stdDeviation=\"1.5\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.09 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect1_dropShadow_1_4143\"\r\n            result=\"effect2_dropShadow_1_4143\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"7\" />\r\n          <feGaussianBlur stdDeviation=\"2\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect2_dropShadow_1_4143\"\r\n            result=\"effect3_dropShadow_1_4143\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"12\" />\r\n          <feGaussianBlur stdDeviation=\"2.5\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.01 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect3_dropShadow_1_4143\"\r\n            result=\"effect4_dropShadow_1_4143\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in=\"SourceGraphic\"\r\n            in2=\"effect4_dropShadow_1_4143\"\r\n            result=\"shape\"\r\n          />\r\n        </filter>\r\n        <linearGradient\r\n          id=\"paint0_linear_1_4143\"\r\n          x1=\"13.9999\"\r\n          y1=\"2410\"\r\n          x2=\"1663.52\"\r\n          y2=\"739.48\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop stopColor=\"#1C7DFF\" />\r\n          <stop offset=\"0.52021\" stopColor=\"#1C69FF\" />\r\n          <stop offset=\"1\" stopColor=\"#F0DCD6\" />\r\n        </linearGradient>\r\n        <clipPath id=\"clip0_1_4143\">\r\n          <rect\r\n            width=\"24\"\r\n            height=\"24\"\r\n            fill=\"white\"\r\n            transform=\"translate(14 10)\"\r\n          />\r\n        </clipPath>\r\n      </defs>\r\n    </svg>\r\n  ),\r\n  boat: () => (\r\n    <svg\r\n      width=\"52\"\r\n      height=\"60\"\r\n      viewBox=\"0 0 52 60\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <g filter=\"url(#filter0_dddd_1_4139)\">\r\n        <path\r\n          d=\"M5 22C5 10.402 14.402 1 26 1C37.598 1 47 10.402 47 22C47 33.598 37.598 43 26 43C14.402 43 5 33.598 5 22Z\"\r\n          fill=\"#0086FF\"\r\n        />\r\n        <path\r\n          d=\"M34.4067 25.7667C33.678 26.7432 32.145 28.3644 31.2924 28.3644H27.3297V25.7646H30.486C30.9375 25.7646 31.368 25.5819 31.6788 25.2606C33.3 23.5806 34.2366 21.5814 34.2366 19.4373C34.2366 15.7791 31.5024 12.5451 27.3276 10.5963V8.84908C27.3276 8.09728 26.7186 7.48828 25.9668 7.48828C25.215 7.48828 24.606 8.09728 24.606 8.84908V9.57358C23.4363 9.23128 22.1973 8.97088 20.9037 8.82178C23.0457 11.1486 24.354 14.2608 24.354 17.6733C24.354 20.733 23.3082 23.5428 21.5505 25.7688H24.606V28.3728H20.133C19.5282 28.3728 19.0368 27.8835 19.0368 27.2766V26.1384C19.0368 25.9389 18.8751 25.7751 18.6735 25.7751H12.7767C12.6612 25.7751 12.5625 25.8696 12.5625 25.9851C12.5583 30.6429 16.2438 34.1394 20.6517 34.1394H33.0942C36.0762 34.1394 37.4097 30.3174 39.1569 27.8856C39.8352 26.9448 41.4627 26.1888 41.9562 25.9788C42.0465 25.941 42.099 25.857 42.099 25.7583V24.2463C42.099 24.093 41.9478 23.9775 41.7987 24.0195C41.7987 24.0195 34.6062 25.6722 34.5222 25.6953C34.4382 25.7205 34.4067 25.7688 34.4067 25.7688V25.7667Z\"\r\n          fill=\"white\"\r\n        />\r\n        <path\r\n          d=\"M22.0494 17.6649C22.0494 15.3192 21.2451 13.1604 19.9011 11.4531L13.1328 23.1648H20.4135C21.4488 21.5856 22.0515 19.6956 22.0515 17.667L22.0494 17.6649Z\"\r\n          fill=\"white\"\r\n        />\r\n      </g>\r\n      <defs>\r\n        <filter\r\n          id=\"filter0_dddd_1_4139\"\r\n          x=\"0\"\r\n          y=\"0\"\r\n          width=\"52\"\r\n          height=\"60\"\r\n          filterUnits=\"userSpaceOnUse\"\r\n          colorInterpolationFilters=\"sRGB\"\r\n        >\r\n          <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"1\" />\r\n          <feGaussianBlur stdDeviation=\"1\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"BackgroundImageFix\"\r\n            result=\"effect1_dropShadow_1_4139\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"3\" />\r\n          <feGaussianBlur stdDeviation=\"1.5\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.09 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect1_dropShadow_1_4139\"\r\n            result=\"effect2_dropShadow_1_4139\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"7\" />\r\n          <feGaussianBlur stdDeviation=\"2\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect2_dropShadow_1_4139\"\r\n            result=\"effect3_dropShadow_1_4139\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"12\" />\r\n          <feGaussianBlur stdDeviation=\"2.5\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.01 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect3_dropShadow_1_4139\"\r\n            result=\"effect4_dropShadow_1_4139\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in=\"SourceGraphic\"\r\n            in2=\"effect4_dropShadow_1_4139\"\r\n            result=\"shape\"\r\n          />\r\n        </filter>\r\n      </defs>\r\n    </svg>\r\n  ),\r\n  supabase: () => (\r\n    <svg\r\n      width=\"52\"\r\n      height=\"60\"\r\n      viewBox=\"0 0 52 60\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <g filter=\"url(#filter0_dddd_1_4134)\">\r\n        <path\r\n          d=\"M5 22C5 10.402 14.402 1 26 1C37.598 1 47 10.402 47 22C47 33.598 37.598 43 26 43C14.402 43 5 33.598 5 22Z\"\r\n          fill=\"#121212\"\r\n        />\r\n        <g clipPath=\"url(#clip0_1_4134)\">\r\n          <path\r\n            d=\"M28.5219 36.7564C27.7374 37.7443 26.1469 37.2031 26.128 35.9417L25.8516 17.4922H38.257C40.5039 17.4922 41.7571 20.0874 40.3599 21.8472L28.5219 36.7564Z\"\r\n            fill=\"url(#paint0_linear_1_4134)\"\r\n          />\r\n          <path\r\n            d=\"M28.5219 36.7564C27.7374 37.7443 26.1469 37.2031 26.128 35.9417L25.8516 17.4922H38.257C40.5039 17.4922 41.7571 20.0874 40.3599 21.8472L28.5219 36.7564Z\"\r\n            fill=\"url(#paint1_linear_1_4134)\"\r\n            fillOpacity=\"0.2\"\r\n          />\r\n          <path\r\n            d=\"M23.48 7.06882C24.2645 6.08082 25.8551 6.62217 25.874 7.88359L25.9951 26.333H13.745C11.4979 26.333 10.2447 23.7378 11.642 21.978L23.48 7.06882Z\"\r\n            fill=\"#3ECF8E\"\r\n          />\r\n        </g>\r\n      </g>\r\n      <defs>\r\n        <filter\r\n          id=\"filter0_dddd_1_4134\"\r\n          x=\"0\"\r\n          y=\"0\"\r\n          width=\"52\"\r\n          height=\"60\"\r\n          filterUnits=\"userSpaceOnUse\"\r\n          colorInterpolationFilters=\"sRGB\"\r\n        >\r\n          <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"1\" />\r\n          <feGaussianBlur stdDeviation=\"1\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"BackgroundImageFix\"\r\n            result=\"effect1_dropShadow_1_4134\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"3\" />\r\n          <feGaussianBlur stdDeviation=\"1.5\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.09 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect1_dropShadow_1_4134\"\r\n            result=\"effect2_dropShadow_1_4134\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"7\" />\r\n          <feGaussianBlur stdDeviation=\"2\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect2_dropShadow_1_4134\"\r\n            result=\"effect3_dropShadow_1_4134\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"12\" />\r\n          <feGaussianBlur stdDeviation=\"2.5\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.01 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect3_dropShadow_1_4134\"\r\n            result=\"effect4_dropShadow_1_4134\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in=\"SourceGraphic\"\r\n            in2=\"effect4_dropShadow_1_4134\"\r\n            result=\"shape\"\r\n          />\r\n        </filter>\r\n        <linearGradient\r\n          id=\"paint0_linear_1_4134\"\r\n          x1=\"25.8516\"\r\n          y1=\"21.5829\"\r\n          x2=\"36.8771\"\r\n          y2=\"26.207\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop stopColor=\"#249361\" />\r\n          <stop offset=\"1\" stopColor=\"#3ECF8E\" />\r\n        </linearGradient>\r\n        <linearGradient\r\n          id=\"paint1_linear_1_4134\"\r\n          x1=\"20.9634\"\r\n          y1=\"14.8902\"\r\n          x2=\"25.9916\"\r\n          y2=\"24.3555\"\r\n          gradientUnits=\"userSpaceOnUse\"\r\n        >\r\n          <stop />\r\n          <stop offset=\"1\" stopOpacity=\"0\" />\r\n        </linearGradient>\r\n        <clipPath id=\"clip0_1_4134\">\r\n          <rect\r\n            width=\"29.9027\"\r\n            height=\"31\"\r\n            fill=\"white\"\r\n            transform=\"translate(11.0469 6.5)\"\r\n          />\r\n        </clipPath>\r\n      </defs>\r\n    </svg>\r\n  ),\r\n  figma: () => (\r\n    <svg\r\n      width=\"52\"\r\n      height=\"60\"\r\n      viewBox=\"0 0 52 60\"\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      <g filter=\"url(#filter0_dddd_1_4123)\">\r\n        <path\r\n          d=\"M5 22C5 10.402 14.402 1 26 1C37.598 1 47 10.402 47 22C47 33.598 37.598 43 26 43C14.402 43 5 33.598 5 22Z\"\r\n          fill=\"white\"\r\n        />\r\n        <g clipPath=\"url(#clip0_1_4123)\">\r\n          <mask\r\n            id=\"mask0_1_4123\"\r\n            maskUnits=\"userSpaceOnUse\"\r\n            x=\"15\"\r\n            y=\"7\"\r\n            width=\"21\"\r\n            height=\"30\"\r\n          >\r\n            <path d=\"M35.875 7H15.875V37H35.875V7Z\" fill=\"white\" />\r\n          </mask>\r\n          <g mask=\"url(#mask0_1_4123)\">\r\n            <path\r\n              d=\"M20.875 37.0001C23.635 37.0001 25.875 34.7601 25.875 32.0001V27.0001H20.875C18.115 27.0001 15.875 29.2401 15.875 32.0001C15.875 34.7601 18.115 37.0001 20.875 37.0001Z\"\r\n              fill=\"#0ACF83\"\r\n            />\r\n            <path\r\n              d=\"M15.875 21.9999C15.875 19.2399 18.115 16.9999 20.875 16.9999H25.875V26.9999H20.875C18.115 26.9999 15.875 24.7599 15.875 21.9999Z\"\r\n              fill=\"#A259FF\"\r\n            />\r\n            <path\r\n              d=\"M15.875 12C15.875 9.24 18.115 7 20.875 7H25.875V17H20.875C18.115 17 15.875 14.76 15.875 12Z\"\r\n              fill=\"#F24E1E\"\r\n            />\r\n            <path\r\n              d=\"M25.875 7H30.875C33.635 7 35.875 9.24 35.875 12C35.875 14.76 33.635 17 30.875 17H25.875V7Z\"\r\n              fill=\"#FF7262\"\r\n            />\r\n            <path\r\n              d=\"M35.875 21.9999C35.875 24.7599 33.635 26.9999 30.875 26.9999C28.115 26.9999 25.875 24.7599 25.875 21.9999C25.875 19.2399 28.115 16.9999 30.875 16.9999C33.635 16.9999 35.875 19.2399 35.875 21.9999Z\"\r\n              fill=\"#1ABCFE\"\r\n            />\r\n          </g>\r\n        </g>\r\n      </g>\r\n      <defs>\r\n        <filter\r\n          id=\"filter0_dddd_1_4123\"\r\n          x=\"0\"\r\n          y=\"0\"\r\n          width=\"52\"\r\n          height=\"60\"\r\n          filterUnits=\"userSpaceOnUse\"\r\n          colorInterpolationFilters=\"sRGB\"\r\n        >\r\n          <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"1\" />\r\n          <feGaussianBlur stdDeviation=\"1\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"BackgroundImageFix\"\r\n            result=\"effect1_dropShadow_1_4123\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"3\" />\r\n          <feGaussianBlur stdDeviation=\"1.5\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.09 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect1_dropShadow_1_4123\"\r\n            result=\"effect2_dropShadow_1_4123\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"7\" />\r\n          <feGaussianBlur stdDeviation=\"2\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect2_dropShadow_1_4123\"\r\n            result=\"effect3_dropShadow_1_4123\"\r\n          />\r\n          <feColorMatrix\r\n            in=\"SourceAlpha\"\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n            result=\"hardAlpha\"\r\n          />\r\n          <feOffset dy=\"12\" />\r\n          <feGaussianBlur stdDeviation=\"2.5\" />\r\n          <feColorMatrix\r\n            type=\"matrix\"\r\n            values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.01 0\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in2=\"effect3_dropShadow_1_4123\"\r\n            result=\"effect4_dropShadow_1_4123\"\r\n          />\r\n          <feBlend\r\n            mode=\"normal\"\r\n            in=\"SourceGraphic\"\r\n            in2=\"effect4_dropShadow_1_4123\"\r\n            result=\"shape\"\r\n          />\r\n        </filter>\r\n        <clipPath id=\"clip0_1_4123\">\r\n          <rect\r\n            width=\"20.25\"\r\n            height=\"30\"\r\n            fill=\"white\"\r\n            transform=\"translate(15.875 7)\"\r\n          />\r\n        </clipPath>\r\n      </defs>\r\n    </svg>\r\n  ),\r\n  github: ({\r\n    className,\r\n    color = 'currentColor',\r\n  }: {\r\n    className?: string;\r\n    color?: string;\r\n  }) => (\r\n    <svg\r\n      className={cn('w-9 h-9', className)}\r\n      viewBox=\"0 0 24 24\"\r\n      aria-hidden=\"true\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n      fill={color}\r\n    >\r\n      <path\r\n        fillRule=\"evenodd\"\r\n        clipRule=\"evenodd\"\r\n        d=\"M12 0C5.37 0 0 5.373 0 12.01c0 5.303 3.438 9.8 8.207 11.387.6.113.82-.26.82-.577v-2.256c-3.338.727-4.033-1.416-4.033-1.416-.546-1.39-1.333-1.76-1.333-1.76-1.09-.745.082-.729.082-.729 1.205.085 1.84 1.26 1.84 1.26 1.07 1.836 2.807 1.306 3.492.998.108-.775.42-1.307.763-1.606-2.665-.307-5.466-1.34-5.466-5.968 0-1.318.47-2.396 1.24-3.24-.125-.307-.537-1.545.116-3.22 0 0 1.008-.324 3.3 1.23a11.44 11.44 0 013.006-.404c1.02.005 2.047.137 3.006.404 2.29-1.554 3.297-1.23 3.297-1.23.655 1.675.243 2.913.12 3.22.77.844 1.237 1.922 1.237 3.24 0 4.64-2.805 5.658-5.48 **********.814 1.103.814 2.222v3.293c0 .32.216.694.825.577C20.565 21.807 24 17.31 24 12.01 24 5.373 18.627 0 12 0z\"\r\n      />\r\n    </svg>\r\n  ),\r\n};"], "names": [], "mappings": ";;;;AAAA;;;AAEO,MAAM,QAAQ;IACnB,MAAM,CAAC,EAAE,SAAS,EAA0B,iBAC1C,6LAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC;;8BAEhD,6LAAC;oBAAE,UAAS;;sCACV,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;;;;;;;8BAGT,6LAAC;8BACC,cAAA,6LAAC;wBAAS,IAAG;kCACX,cAAA,6LAAC;4BAAK,OAAM;4BAAK,QAAO;4BAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;IAK1C,MAAM,CAAC,EAAE,SAAS,EAA0B,iBAC1C,6LAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;8BAExB,6LAAC;oBAAE,QAAO;;sCACR,6LAAC;4BACC,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,IAAG;4BACH,MAAK;;;;;;sCAEP,6LAAC;4BAAE,QAAO;;8CACR,6LAAC;oCACC,GAAE;oCACF,GAAE;oCACF,OAAM;oCACN,QAAO;oCACP,IAAG;oCACH,MAAK;;;;;;8CAEP,6LAAC;oCACC,GAAE;oCACF,MAAK;;;;;;8CAEP,6LAAC;oCACC,GAAE;oCACF,MAAK;;;;;;8CAEP,6LAAC;oCACC,IAAG;oCACH,IAAG;oCACH,IAAG;oCACH,IAAG;oCACH,QAAO;oCACP,aAAY;;;;;;;;;;;;;;;;;;8BAIlB,6LAAC;;sCACC,6LAAC;4BACC,IAAG;4BACH,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,aAAY;4BACZ,2BAA0B;;8CAE1B,6LAAC;oCAAQ,cAAa;oCAAI,QAAO;;;;;;8CACjC,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCACC,QAAO;oCACP,UAAS;oCACT,IAAG;oCACH,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCACC,QAAO;oCACP,UAAS;oCACT,IAAG;oCACH,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCACC,QAAO;oCACP,UAAS;oCACT,IAAG;oCACH,QAAO;;;;;;8CAET,6LAAC;;;;;8CACD,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,KAAI;oCACJ,QAAO;;;;;;;;;;;;sCAGX,6LAAC;4BACC,IAAG;4BACH,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,aAAY;4BACZ,2BAA0B;;8CAE1B,6LAAC;oCAAQ,cAAa;oCAAI,QAAO;;;;;;8CACjC,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCACC,QAAO;oCACP,UAAS;oCACT,IAAG;oCACH,QAAO;;;;;;8CAET,6LAAC;;;;;8CACD,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,KAAI;oCACJ,QAAO;;;;;;;;;;;;sCAGX,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,QAAO;oCAAI,WAAU;;;;;;;;;;;;sCAE7B,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,QAAO;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;IAKnC,UAAU,CAAC,EAAE,SAAS,EAA0B,iBAC9C,6LAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;8BAExB,6LAAC;oBAAE,QAAO;;sCACR,6LAAC;4BACC,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,IAAG;4BACH,MAAK;;;;;;sCAEP,6LAAC;4BAAE,QAAO;;8CACR,6LAAC;oCACC,GAAE;oCACF,GAAE;oCACF,OAAM;oCACN,QAAO;oCACP,IAAG;oCACH,MAAK;;;;;;8CAEP,6LAAC;oCACC,GAAE;oCACF,MAAK;;;;;;8CAEP,6LAAC;oCACC,GAAE;oCACF,MAAK;;;;;;8CAEP,6LAAC;oCACC,IAAG;oCACH,IAAG;oCACH,IAAG;oCACH,IAAG;oCACH,QAAO;oCACP,aAAY;;;;;;;;;;;;;;;;;;8BAIlB,6LAAC;;sCACC,6LAAC;4BACC,IAAG;4BACH,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,aAAY;4BACZ,2BAA0B;;8CAE1B,6LAAC;oCAAQ,cAAa;oCAAI,QAAO;;;;;;8CACjC,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCACC,QAAO;oCACP,UAAS;oCACT,IAAG;oCACH,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCACC,QAAO;oCACP,UAAS;oCACT,IAAG;oCACH,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCACC,QAAO;oCACP,UAAS;oCACT,IAAG;oCACH,QAAO;;;;;;8CAET,6LAAC;;;;;8CACD,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,KAAI;oCACJ,QAAO;;;;;;;;;;;;sCAGX,6LAAC;4BACC,IAAG;4BACH,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,aAAY;4BACZ,2BAA0B;;8CAE1B,6LAAC;oCAAQ,cAAa;oCAAI,QAAO;;;;;;8CACjC,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCACC,QAAO;oCACP,UAAS;oCACT,IAAG;oCACH,QAAO;;;;;;8CAET,6LAAC;;;;;8CACD,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,KAAI;oCACJ,QAAO;;;;;;;;;;;;sCAGX,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,QAAO;oCAAI,WAAU;;;;;;;;;;;;sCAE7B,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,QAAO;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;IAKnC,OAAO,CAAC,EAAE,SAAS,EAA0B,iBAC3C,6LAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;;8BAEX,6LAAC;oBAAE,QAAO;;sCACR,6LAAC;4BACC,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,IAAG;4BACH,MAAK;;;;;;sCAEP,6LAAC;4BACC,UAAS;4BACT,UAAS;4BACT,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;;;;;;;8BAGT,6LAAC;;sCACC,6LAAC;4BACC,IAAG;4BACH,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,aAAY;4BACZ,2BAA0B;;8CAE1B,6LAAC;oCAAQ,cAAa;oCAAI,QAAO;;;;;;8CACjC,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCACC,QAAO;oCACP,UAAS;oCACT,IAAG;oCACH,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCACC,QAAO;oCACP,UAAS;oCACT,IAAG;oCACH,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCACC,QAAO;oCACP,UAAS;oCACT,IAAG;oCACH,QAAO;;;;;;8CAET,6LAAC;;;;;8CACD,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,KAAI;oCACJ,QAAO;;;;;;;;;;;;sCAGX,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,QAAO;oCAAI,WAAU;;;;;;;;;;;;sCAE7B,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;;;;;;;;;;;;;IAKhE,WAAW,CAAC,EAAE,SAAS,EAA0B,iBAC/C,6LAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW;;8BAEX,6LAAC;oBAAE,QAAO;;sCACR,6LAAC;4BACC,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,IAAG;4BACH,MAAK;;;;;;sCAEP,6LAAC;4BACC,UAAS;4BACT,UAAS;4BACT,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;;;;;;;8BAGT,6LAAC;;sCACC,6LAAC;4BACC,IAAG;4BACH,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,aAAY;4BACZ,2BAA0B;;8CAE1B,6LAAC;oCAAQ,cAAa;oCAAI,QAAO;;;;;;8CACjC,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCACC,QAAO;oCACP,UAAS;oCACT,IAAG;oCACH,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCACC,QAAO;oCACP,UAAS;oCACT,IAAG;oCACH,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCACC,QAAO;oCACP,UAAS;oCACT,IAAG;oCACH,QAAO;;;;;;8CAET,6LAAC;;;;;8CACD,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,KAAI;oCACJ,QAAO;;;;;;;;;;;;sCAGX,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,QAAO;oCAAI,WAAU;;;;;;;;;;;;sCAE7B,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;;;;;;;;;;;;;IAKhE,MAAM,CAAC,EAAE,SAAS,EAA0B,iBAC1C,6LAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;8BAExB,6LAAC;oBAAE,QAAO;;sCACR,6LAAC;4BACC,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,IAAG;4BACH,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,UAAS;4BACT,UAAS;4BACT,GAAE;4BACF,MAAK;;;;;;;;;;;;8BAGT,6LAAC;;sCACC,6LAAC;4BACC,IAAG;4BACH,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,aAAY;4BACZ,2BAA0B;;8CAE1B,6LAAC;oCAAQ,cAAa;oCAAI,QAAO;;;;;;8CACjC,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCACC,QAAO;oCACP,UAAS;oCACT,IAAG;oCACH,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCACC,QAAO;oCACP,UAAS;oCACT,IAAG;oCACH,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCACC,QAAO;oCACP,UAAS;oCACT,IAAG;oCACH,QAAO;;;;;;8CAET,6LAAC;;;;;8CACD,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,KAAI;oCACJ,QAAO;;;;;;;;;;;;sCAGX,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,QAAO;oCAAI,WAAU;;;;;;;;;;;;sCAE7B,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;sCAE1D,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;sCAE1D,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;sCAE1D,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;sCAE1D,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;sCAE1D,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;sCAE1D,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;sCAE1D,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;sCAE1D,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;sCAE1D,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;sCAE1D,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;sCAE1D,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;;;;;;;;;;;;;IAKhE,UAAU,CAAC,EAAE,SAAS,EAA0B,iBAC9C,6LAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,UAAU;;8BAExB,6LAAC;oBAAE,QAAO;;sCACR,6LAAC;4BACC,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,IAAG;4BACH,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;;;;;;;8BAGT,6LAAC;;sCACC,6LAAC;4BACC,IAAG;4BACH,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,aAAY;4BACZ,2BAA0B;;8CAE1B,6LAAC;oCAAQ,cAAa;oCAAI,QAAO;;;;;;8CACjC,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCACC,QAAO;oCACP,UAAS;oCACT,IAAG;oCACH,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCACC,QAAO;oCACP,UAAS;oCACT,IAAG;oCACH,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCACC,QAAO;oCACP,UAAS;oCACT,IAAG;oCACH,QAAO;;;;;;8CAET,6LAAC;;;;;8CACD,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,KAAI;oCACJ,QAAO;;;;;;;;;;;;sCAGX,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,QAAO;oCAAI,WAAU;;;;;;;;;;;;sCAE7B,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;sCAE1D,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;sCAE1D,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;sCAE1D,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;sCAE1D,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;sCAE1D,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;sCAE1D,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;sCAE1D,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;sCAE1D,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;sCAE1D,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;sCAE1D,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;sCAE1D,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;8CACxD,6LAAC;oCAAK,QAAO;oCAAW,WAAU;oCAAU,aAAY;;;;;;;;;;;;;;;;;;;;;;;;IAKhE,QAAQ,kBACN,6LAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;;8BAEN,6LAAC;oBAAE,QAAO;;sCACR,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BAAE,UAAS;sCACV,cAAA,6LAAC;gCAAK,GAAE;gCAAgC,MAAK;;;;;;;;;;;;;;;;;8BAGjD,6LAAC;;sCACC,6LAAC;4BACC,IAAG;4BACH,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,aAAY;4BACZ,2BAA0B;;8CAE1B,6LAAC;oCAAQ,cAAa;oCAAI,QAAO;;;;;;8CACjC,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,KAAI;oCACJ,QAAO;;;;;;;;;;;;sCAGX,6LAAC;4BAAS,IAAG;sCACX,cAAA,6LAAC;gCACC,OAAM;gCACN,QAAO;gCACP,MAAK;gCACL,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMpB,QAAQ,kBACN,6LAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;;8BAEN,6LAAC;oBAAE,QAAO;;sCACR,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BAAE,UAAS;;8CACV,6LAAC;oCACC,GAAE;oCACF,MAAK;;;;;;8CAEP,6LAAC;oCACC,GAAE;oCACF,MAAK;;;;;;8CAEP,6LAAC;oCACC,GAAE;oCACF,MAAK;;;;;;;;;;;;;;;;;;8BAIX,6LAAC;;sCACC,6LAAC;4BACC,IAAG;4BACH,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,aAAY;4BACZ,2BAA0B;;8CAE1B,6LAAC;oCAAQ,cAAa;oCAAI,QAAO;;;;;;8CACjC,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,KAAI;oCACJ,QAAO;;;;;;;;;;;;sCAGX,6LAAC;4BAAS,IAAG;sCACX,cAAA,6LAAC;gCACC,OAAM;gCACN,QAAO;gCACP,MAAK;gCACL,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMpB,SAAS,kBACP,6LAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;;8BAEN,6LAAC;oBAAE,QAAO;;sCACR,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;;;;;;;8BAGT,6LAAC;8BACC,cAAA,6LAAC;wBACC,IAAG;wBACH,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,aAAY;wBACZ,2BAA0B;;0CAE1B,6LAAC;gCAAQ,cAAa;gCAAI,QAAO;;;;;;0CACjC,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,QAAO;gCACP,QAAO;;;;;;0CAET,6LAAC;gCAAS,IAAG;;;;;;0CACb,6LAAC;gCAAe,cAAa;;;;;;0CAC7B,6LAAC;gCACC,MAAK;gCACL,QAAO;;;;;;0CAET,6LAAC;gCACC,MAAK;gCACL,KAAI;gCACJ,QAAO;;;;;;0CAET,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,QAAO;gCACP,QAAO;;;;;;0CAET,6LAAC;gCAAS,IAAG;;;;;;0CACb,6LAAC;gCAAe,cAAa;;;;;;0CAC7B,6LAAC;gCACC,MAAK;gCACL,QAAO;;;;;;0CAET,6LAAC;gCACC,MAAK;gCACL,KAAI;gCACJ,QAAO;;;;;;0CAET,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,QAAO;gCACP,QAAO;;;;;;0CAET,6LAAC;gCAAS,IAAG;;;;;;0CACb,6LAAC;gCAAe,cAAa;;;;;;0CAC7B,6LAAC;gCACC,MAAK;gCACL,QAAO;;;;;;0CAET,6LAAC;gCACC,MAAK;gCACL,KAAI;gCACJ,QAAO;;;;;;0CAET,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,QAAO;gCACP,QAAO;;;;;;0CAET,6LAAC;gCAAS,IAAG;;;;;;0CACb,6LAAC;gCAAe,cAAa;;;;;;0CAC7B,6LAAC;gCACC,MAAK;gCACL,QAAO;;;;;;0CAET,6LAAC;gCACC,MAAK;gCACL,KAAI;gCACJ,QAAO;;;;;;0CAET,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,KAAI;gCACJ,QAAO;;;;;;;;;;;;;;;;;;;;;;;IAMjB,aAAa,kBACX,6LAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,OAAM;;8BAEN,6LAAC;oBACC,GAAE;oBACF,MAAK;;;;;;8BAEP,6LAAC;oBACC,GAAE;oBACF,MAAK;;;;;;8BAEP,6LAAC;oBACC,GAAE;oBACF,MAAK;;;;;;8BAEP,6LAAC;oBACC,GAAE;oBACF,MAAK;;;;;;8BAEP,6LAAC;oBACC,GAAE;oBACF,MAAK;;;;;;8BAEP,6LAAC;oBACC,GAAE;oBACF,MAAK;;;;;;;;;;;;IAIX,QAAQ,kBACN,6LAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;;8BAEN,6LAAC;oBAAE,QAAO;;sCACR,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BAAE,UAAS;;8CACV,6LAAC;oCACC,GAAE;oCACF,MAAK;;;;;;8CAEP,6LAAC;oCACC,GAAE;oCACF,MAAK;;;;;;;;;;;;;;;;;;8BAIX,6LAAC;;sCACC,6LAAC;4BACC,IAAG;4BACH,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,aAAY;4BACZ,2BAA0B;;8CAE1B,6LAAC;oCAAQ,cAAa;oCAAI,QAAO;;;;;;8CACjC,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,KAAI;oCACJ,QAAO;;;;;;;;;;;;sCAGX,6LAAC;4BAAS,IAAG;sCACX,cAAA,6LAAC;gCACC,OAAM;gCACN,QAAO;gCACP,MAAK;gCACL,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMpB,UAAU,kBACR,6LAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;;8BAEN,6LAAC;oBAAE,QAAO;;sCACR,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;;;;;;;8BAGT,6LAAC;8BACC,cAAA,6LAAC;wBACC,IAAG;wBACH,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,aAAY;wBACZ,2BAA0B;;0CAE1B,6LAAC;gCAAQ,cAAa;gCAAI,QAAO;;;;;;0CACjC,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,QAAO;gCACP,QAAO;;;;;;0CAET,6LAAC;gCAAS,IAAG;;;;;;0CACb,6LAAC;gCAAe,cAAa;;;;;;0CAC7B,6LAAC;gCACC,MAAK;gCACL,QAAO;;;;;;0CAET,6LAAC;gCACC,MAAK;gCACL,KAAI;gCACJ,QAAO;;;;;;0CAET,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,QAAO;gCACP,QAAO;;;;;;0CAET,6LAAC;gCAAS,IAAG;;;;;;0CACb,6LAAC;gCAAe,cAAa;;;;;;0CAC7B,6LAAC;gCACC,MAAK;gCACL,QAAO;;;;;;0CAET,6LAAC;gCACC,MAAK;gCACL,KAAI;gCACJ,QAAO;;;;;;0CAET,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,QAAO;gCACP,QAAO;;;;;;0CAET,6LAAC;gCAAS,IAAG;;;;;;0CACb,6LAAC;gCAAe,cAAa;;;;;;0CAC7B,6LAAC;gCACC,MAAK;gCACL,QAAO;;;;;;0CAET,6LAAC;gCACC,MAAK;gCACL,KAAI;gCACJ,QAAO;;;;;;0CAET,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,QAAO;gCACP,QAAO;;;;;;0CAET,6LAAC;gCAAS,IAAG;;;;;;0CACb,6LAAC;gCAAe,cAAa;;;;;;0CAC7B,6LAAC;gCACC,MAAK;gCACL,QAAO;;;;;;0CAET,6LAAC;gCACC,MAAK;gCACL,KAAI;gCACJ,QAAO;;;;;;0CAET,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,KAAI;gCACJ,QAAO;;;;;;;;;;;;;;;;;;;;;;;IAMjB,QAAQ,kBACN,6LAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;;8BAEN,6LAAC;oBAAE,QAAO;;sCACR,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BAAE,UAAS;sCACV,cAAA,6LAAC;gCACC,GAAE;gCACF,MAAK;;;;;;;;;;;;;;;;;8BAIX,6LAAC;;sCACC,6LAAC;4BACC,IAAG;4BACH,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,aAAY;4BACZ,2BAA0B;;8CAE1B,6LAAC;oCAAQ,cAAa;oCAAI,QAAO;;;;;;8CACjC,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,KAAI;oCACJ,QAAO;;;;;;;;;;;;sCAGX,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,QAAO;oCAAU,WAAU;;;;;;8CACjC,6LAAC;oCAAK,QAAO;oCAAI,WAAU;;;;;;;;;;;;sCAE7B,6LAAC;4BAAS,IAAG;sCACX,cAAA,6LAAC;gCACC,OAAM;gCACN,QAAO;gCACP,MAAK;gCACL,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMpB,MAAM,kBACJ,6LAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;;8BAEN,6LAAC;oBAAE,QAAO;;sCACR,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;;;;;;;8BAGT,6LAAC;8BACC,cAAA,6LAAC;wBACC,IAAG;wBACH,GAAE;wBACF,GAAE;wBACF,OAAM;wBACN,QAAO;wBACP,aAAY;wBACZ,2BAA0B;;0CAE1B,6LAAC;gCAAQ,cAAa;gCAAI,QAAO;;;;;;0CACjC,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,QAAO;gCACP,QAAO;;;;;;0CAET,6LAAC;gCAAS,IAAG;;;;;;0CACb,6LAAC;gCAAe,cAAa;;;;;;0CAC7B,6LAAC;gCACC,MAAK;gCACL,QAAO;;;;;;0CAET,6LAAC;gCACC,MAAK;gCACL,KAAI;gCACJ,QAAO;;;;;;0CAET,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,QAAO;gCACP,QAAO;;;;;;0CAET,6LAAC;gCAAS,IAAG;;;;;;0CACb,6LAAC;gCAAe,cAAa;;;;;;0CAC7B,6LAAC;gCACC,MAAK;gCACL,QAAO;;;;;;0CAET,6LAAC;gCACC,MAAK;gCACL,KAAI;gCACJ,QAAO;;;;;;0CAET,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,QAAO;gCACP,QAAO;;;;;;0CAET,6LAAC;gCAAS,IAAG;;;;;;0CACb,6LAAC;gCAAe,cAAa;;;;;;0CAC7B,6LAAC;gCACC,MAAK;gCACL,QAAO;;;;;;0CAET,6LAAC;gCACC,MAAK;gCACL,KAAI;gCACJ,QAAO;;;;;;0CAET,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,QAAO;gCACP,QAAO;;;;;;0CAET,6LAAC;gCAAS,IAAG;;;;;;0CACb,6LAAC;gCAAe,cAAa;;;;;;0CAC7B,6LAAC;gCACC,MAAK;gCACL,QAAO;;;;;;0CAET,6LAAC;gCACC,MAAK;gCACL,KAAI;gCACJ,QAAO;;;;;;0CAET,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,KAAI;gCACJ,QAAO;;;;;;;;;;;;;;;;;;;;;;;IAMjB,UAAU,kBACR,6LAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;;8BAEN,6LAAC;oBAAE,QAAO;;sCACR,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BAAE,UAAS;;8CACV,6LAAC;oCACC,GAAE;oCACF,MAAK;;;;;;8CAEP,6LAAC;oCACC,GAAE;oCACF,MAAK;oCACL,aAAY;;;;;;8CAEd,6LAAC;oCACC,GAAE;oCACF,MAAK;;;;;;;;;;;;;;;;;;8BAIX,6LAAC;;sCACC,6LAAC;4BACC,IAAG;4BACH,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,aAAY;4BACZ,2BAA0B;;8CAE1B,6LAAC;oCAAQ,cAAa;oCAAI,QAAO;;;;;;8CACjC,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,KAAI;oCACJ,QAAO;;;;;;;;;;;;sCAGX,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;oCAAK,WAAU;;;;;;8CAChB,6LAAC;oCAAK,QAAO;oCAAI,WAAU;;;;;;;;;;;;sCAE7B,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,IAAG;4BACH,eAAc;;8CAEd,6LAAC;;;;;8CACD,6LAAC;oCAAK,QAAO;oCAAI,aAAY;;;;;;;;;;;;sCAE/B,6LAAC;4BAAS,IAAG;sCACX,cAAA,6LAAC;gCACC,OAAM;gCACN,QAAO;gCACP,MAAK;gCACL,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMpB,OAAO,kBACL,6LAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;;8BAEN,6LAAC;oBAAE,QAAO;;sCACR,6LAAC;4BACC,GAAE;4BACF,MAAK;;;;;;sCAEP,6LAAC;4BAAE,UAAS;;8CACV,6LAAC;oCACC,IAAG;oCACH,WAAU;oCACV,GAAE;oCACF,GAAE;oCACF,OAAM;oCACN,QAAO;8CAEP,cAAA,6LAAC;wCAAK,GAAE;wCAAgC,MAAK;;;;;;;;;;;8CAE/C,6LAAC;oCAAE,MAAK;;sDACN,6LAAC;4CACC,GAAE;4CACF,MAAK;;;;;;sDAEP,6LAAC;4CACC,GAAE;4CACF,MAAK;;;;;;sDAEP,6LAAC;4CACC,GAAE;4CACF,MAAK;;;;;;sDAEP,6LAAC;4CACC,GAAE;4CACF,MAAK;;;;;;sDAEP,6LAAC;4CACC,GAAE;4CACF,MAAK;;;;;;;;;;;;;;;;;;;;;;;;8BAKb,6LAAC;;sCACC,6LAAC;4BACC,IAAG;4BACH,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;4BACP,aAAY;4BACZ,2BAA0B;;8CAE1B,6LAAC;oCAAQ,cAAa;oCAAI,QAAO;;;;;;8CACjC,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,QAAO;oCACP,QAAO;;;;;;8CAET,6LAAC;oCAAS,IAAG;;;;;;8CACb,6LAAC;oCAAe,cAAa;;;;;;8CAC7B,6LAAC;oCACC,MAAK;oCACL,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,QAAO;;;;;;8CAET,6LAAC;oCACC,MAAK;oCACL,IAAG;oCACH,KAAI;oCACJ,QAAO;;;;;;;;;;;;sCAGX,6LAAC;4BAAS,IAAG;sCACX,cAAA,6LAAC;gCACC,OAAM;gCACN,QAAO;gCACP,MAAK;gCACL,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMpB,QAAQ,CAAC,EACP,SAAS,EACT,QAAQ,cAAc,EAIvB,iBACC,6LAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,WAAW;YACzB,SAAQ;YACR,eAAY;YACZ,OAAM;YACN,MAAM;sBAEN,cAAA,6LAAC;gBACC,UAAS;gBACT,UAAS;gBACT,GAAE;;;;;;;;;;;AAIV", "debugId": null}}, {"offset": {"line": 5690, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/ui/code-block.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport React, { useEffect, useState } from 'react';\r\nimport { codeToHtml } from 'shiki';\r\nimport { useTheme } from 'next-themes';\r\n\r\nexport type CodeBlockProps = {\r\n  children?: React.ReactNode;\r\n  className?: string;\r\n} & React.HTMLProps<HTMLDivElement>;\r\n\r\nfunction CodeBlock({ children, className, ...props }: CodeBlockProps) {\r\n  return (\r\n    <div className={cn('w-px flex-grow min-w-0 overflow-hidden flex', className)} {...props}>\r\n      {children}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport type CodeBlockCodeProps = {\r\n  code: string;\r\n  language?: string;\r\n  theme?: string;\r\n  className?: string;\r\n} & React.HTMLProps<HTMLDivElement>;\r\n\r\nfunction CodeBlockCode({\r\n  code,\r\n  language = 'tsx',\r\n  theme: propTheme,\r\n  className,\r\n  ...props\r\n}: CodeBlockCodeProps) {\r\n  const { resolvedTheme } = useTheme();\r\n  const [highlightedHtml, setHighlightedHtml] = useState<string | null>(null);\r\n\r\n  // Use github-dark when in dark mode, github-light when in light mode\r\n  const theme =\r\n    propTheme || (resolvedTheme === 'dark' ? 'github-dark' : 'github-light');\r\n\r\n  useEffect(() => {\r\n    async function highlight() {\r\n      if (!code || typeof code !== 'string') {\r\n        setHighlightedHtml(null);\r\n        return;\r\n      }\r\n      const html = await codeToHtml(code, {\r\n        lang: language,\r\n        theme,\r\n        transformers: [\r\n          {\r\n            pre(node) {\r\n              if (node.properties.style) {\r\n                node.properties.style = (node.properties.style as string)\r\n                  .replace(/background-color:[^;]+;?/g, '');\r\n              }\r\n            }\r\n          }\r\n        ]\r\n      });\r\n      setHighlightedHtml(html);\r\n    }\r\n    highlight();\r\n  }, [code, language, theme]);\r\n\r\n  const classNames = cn('[&_pre]:!bg-background/95 [&_pre]:rounded-lg [&_pre]:p-4 [&_pre]:!overflow-x-auto [&_pre]:!w-px [&_pre]:!flex-grow [&_pre]:!min-w-0 [&_pre]:!box-border [&_.shiki]:!overflow-x-auto [&_.shiki]:!w-px [&_.shiki]:!flex-grow [&_.shiki]:!min-w-0 [&_code]:!min-w-0 [&_code]:!whitespace-pre', 'w-px flex-grow min-w-0 overflow-hidden flex w-full', className);\r\n\r\n  // SSR fallback: render plain code if not hydrated yet\r\n  return highlightedHtml ? (\r\n    <div\r\n      className={classNames}\r\n      dangerouslySetInnerHTML={{ __html: highlightedHtml }}\r\n      {...props}\r\n    />\r\n  ) : (\r\n    <div className={classNames} {...props}>\r\n      <pre className=\"!overflow-x-auto !w-px !flex-grow !min-w-0 !box-border\">\r\n        <code>{code}</code>\r\n      </pre>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport type CodeBlockGroupProps = React.HTMLAttributes<HTMLDivElement>;\r\n\r\nfunction CodeBlockGroup({\r\n  children,\r\n  className,\r\n  ...props\r\n}: CodeBlockGroupProps) {\r\n  return (\r\n    <div className={cn('', className)} {...props}>\r\n      {children}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport { CodeBlockGroup, CodeBlockCode, CodeBlock };\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAYA,SAAS,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAuB;IAClE,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+CAA+C;QAAa,GAAG,KAAK;kBACpF;;;;;;AAGP;KANS;AAeT,SAAS,cAAc,EACrB,IAAI,EACJ,WAAW,KAAK,EAChB,OAAO,SAAS,EAChB,SAAS,EACT,GAAG,OACgB;;IACnB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtE,qEAAqE;IACrE,MAAM,QACJ,aAAa,CAAC,kBAAkB,SAAS,gBAAgB,cAAc;IAEzE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,eAAe;gBACb,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;oBACrC,mBAAmB;oBACnB;gBACF;gBACA,MAAM,OAAO,MAAM,CAAA,GAAA,mKAAA,CAAA,aAAU,AAAD,EAAE,MAAM;oBAClC,MAAM;oBACN;oBACA,cAAc;wBACZ;4BACE,KAAI,IAAI;gCACN,IAAI,KAAK,UAAU,CAAC,KAAK,EAAE;oCACzB,KAAK,UAAU,CAAC,KAAK,GAAG,AAAC,KAAK,UAAU,CAAC,KAAK,CAC3C,OAAO,CAAC,6BAA6B;gCAC1C;4BACF;wBACF;qBACD;gBACH;gBACA,mBAAmB;YACrB;YACA;QACF;kCAAG;QAAC;QAAM;QAAU;KAAM;IAE1B,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6RAA6R,sDAAsD;IAEzW,sDAAsD;IACtD,OAAO,gCACL,6LAAC;QACC,WAAW;QACX,yBAAyB;YAAE,QAAQ;QAAgB;QAClD,GAAG,KAAK;;;;;6BAGX,6LAAC;QAAI,WAAW;QAAa,GAAG,KAAK;kBACnC,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;0BAAM;;;;;;;;;;;;;;;;AAIf;GAvDS;;QAOmB,mJAAA,CAAA,WAAQ;;;MAP3B;AA2DT,SAAS,eAAe,EACtB,QAAQ,EACR,SAAS,EACT,GAAG,OACiB;IACpB,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,IAAI;QAAa,GAAG,KAAK;kBACzC;;;;;;AAGP;MAVS", "debugId": null}}, {"offset": {"line": 5821, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/ui/markdown.tsx"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\r\nimport { cn } from '@/lib/utils';\r\nimport { marked } from 'marked';\r\nimport { memo, useId, useMemo } from 'react';\r\nimport ReactMarkdown, { Components } from 'react-markdown';\r\nimport remarkGfm from 'remark-gfm';\r\nimport { CodeBlock, CodeBlockCode } from '@/components/ui/code-block';\r\n\r\nexport type MarkdownProps = {\r\n  children: string;\r\n  id?: string;\r\n  className?: string;\r\n  components?: Partial<Components>;\r\n};\r\n\r\nfunction parseMarkdownIntoBlocks(markdown: string): string[] {\r\n  const tokens = marked.lexer(markdown);\r\n  return tokens.map((token: any) => token.raw);\r\n}\r\n\r\nfunction extractLanguage(className?: string): string {\r\n  if (!className) return 'plaintext';\r\n  const match = className.match(/language-(\\w+)/);\r\n  return match ? match[1] : 'plaintext';\r\n}\r\n\r\nconst INITIAL_COMPONENTS: Partial<Components> = {\r\n  code: function CodeComponent({ className, children, ...props }: any) {\r\n    const isInline =\r\n      !props.node?.position?.start.line ||\r\n      props.node?.position?.start.line === props.node?.position?.end.line;\r\n\r\n    if (isInline) {\r\n      return (\r\n        <span\r\n          className={cn(\r\n            'bg-primary-foreground dark:bg-zinc-800 dark:border dark:border-zinc-700 rounded-sm px-1 font-mono text-sm',\r\n            className,\r\n          )}\r\n          {...props}\r\n        >\r\n          {children}\r\n        </span>\r\n      );\r\n    }\r\n\r\n    const language = extractLanguage(className);\r\n\r\n    return (\r\n      <CodeBlock className=\"rounded-md overflow-hidden my-4 border border-zinc-200 dark:border-zinc-800 max-w-full min-w-0 w-full\">\r\n        <CodeBlockCode\r\n          code={children as string}\r\n          language={language}\r\n          className=\"text-sm\"\r\n        />\r\n      </CodeBlock>\r\n    );\r\n  },\r\n  pre: function PreComponent({ children }: any) {\r\n    return <>{children}</>;\r\n  },\r\n  ul: function UnorderedList({ children, ...props }: any) {\r\n    return (\r\n      <ul className=\"list-disc pl-5 my-2\" {...props}>\r\n        {children}\r\n      </ul>\r\n    );\r\n  },\r\n  ol: function OrderedList({ children, ...props }: any) {\r\n    return (\r\n      <ol className=\"list-decimal pl-5 my-2\" {...props}>\r\n        {children}\r\n      </ol>\r\n    );\r\n  },\r\n  li: function ListItem({ children, ...props }: any) {\r\n    return (\r\n      <li className=\"my-1\" {...props}>\r\n        {children}\r\n      </li>\r\n    );\r\n  },\r\n  h1: function H1({ children, ...props }: any) {\r\n    return (\r\n      <h1 className=\"text-2xl font-bold my-3\" {...props}>\r\n        {children}\r\n      </h1>\r\n    );\r\n  },\r\n  h2: function H2({ children, ...props }: any) {\r\n    return (\r\n      <h2 className=\"text-xl font-bold my-2\" {...props}>\r\n        {children}\r\n      </h2>\r\n    );\r\n  },\r\n  h3: function H3({ children, ...props }: any) {\r\n    return (\r\n      <h3 className=\"text-lg font-bold my-2\" {...props}>\r\n        {children}\r\n      </h3>\r\n    );\r\n  },\r\n  blockquote: function Blockquote({ children, ...props }: any) {\r\n    return (\r\n      <blockquote\r\n        className=\"border-l-4 border-muted pl-4 italic my-2 dark:text-zinc-400 dark:border-zinc-600\"\r\n        {...props}\r\n      >\r\n        {children}\r\n      </blockquote>\r\n    );\r\n  },\r\n  a: function Anchor({ children, href, ...props }: any) {\r\n    return (\r\n      <a\r\n        href={href}\r\n        className=\"text-primary hover:underline dark:text-blue-400\"\r\n        target=\"_blank\"\r\n        rel=\"noopener noreferrer\"\r\n        {...props}\r\n      >\r\n        {children}\r\n      </a>\r\n    );\r\n  },\r\n  table: function Table({ children, ...props }: any) {\r\n    return (\r\n      <table className=\"w-full border-collapse my-3 text-sm\" {...props}>\r\n        {children}\r\n      </table>\r\n    );\r\n  },\r\n  th: function TableHeader({ children, ...props }: any) {\r\n    return (\r\n      <th\r\n        className=\"border border-slate-300 dark:border-zinc-700 px-3 py-2 text-left font-semibold bg-slate-100 dark:bg-zinc-800\"\r\n        {...props}\r\n      >\r\n        {children}\r\n      </th>\r\n    );\r\n  },\r\n  td: function TableCell({ children, ...props }: any) {\r\n    return (\r\n      <td\r\n        className=\"border border-slate-300 dark:border-zinc-700 px-3 py-2\"\r\n        {...props}\r\n      >\r\n        {children}\r\n      </td>\r\n    );\r\n  },\r\n};\r\n\r\nconst MemoizedMarkdownBlock = memo(\r\n  function MarkdownBlock({\r\n    content,\r\n    components = INITIAL_COMPONENTS,\r\n  }: {\r\n    content: string;\r\n    components?: Partial<Components>;\r\n  }) {\r\n    return (\r\n      <ReactMarkdown remarkPlugins={[remarkGfm]} components={components}>\r\n        {content}\r\n      </ReactMarkdown>\r\n    );\r\n  },\r\n  function propsAreEqual(prevProps: any, nextProps: any) {\r\n    return prevProps.content === nextProps.content;\r\n  },\r\n);\r\n\r\nMemoizedMarkdownBlock.displayName = 'MemoizedMarkdownBlock';\r\n\r\nfunction MarkdownComponent({\r\n  children,\r\n  id,\r\n  className,\r\n  components = INITIAL_COMPONENTS,\r\n}: MarkdownProps) {\r\n  const generatedId = useId();\r\n  const blockId = id ?? generatedId;\r\n  const blocks = useMemo(() => parseMarkdownIntoBlocks(children), [children]);\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        'prose-code:before:hidden prose-code:after:hidden',\r\n        className,\r\n      )}\r\n    >\r\n      {blocks.map((block, index) => (\r\n        <MemoizedMarkdownBlock\r\n          key={`${blockId}-block-${index}`}\r\n          content={block}\r\n          components={components}\r\n        />\r\n      ))}\r\n    </div>\r\n  );\r\n}\r\n\r\nconst Markdown = memo(MarkdownComponent);\r\nMarkdown.displayName = 'Markdown';\r\n\r\nexport { Markdown };\r\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AACrD;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AASA,SAAS,wBAAwB,QAAgB;IAC/C,MAAM,SAAS,iJAAA,CAAA,SAAM,CAAC,KAAK,CAAC;IAC5B,OAAO,OAAO,GAAG,CAAC,CAAC,QAAe,MAAM,GAAG;AAC7C;AAEA,SAAS,gBAAgB,SAAkB;IACzC,IAAI,CAAC,WAAW,OAAO;IACvB,MAAM,QAAQ,UAAU,KAAK,CAAC;IAC9B,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;AAC5B;AAEA,MAAM,qBAA0C;IAC9C,MAAM,SAAS,cAAc,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAY;QACjE,MAAM,WACJ,CAAC,MAAM,IAAI,EAAE,UAAU,MAAM,QAC7B,MAAM,IAAI,EAAE,UAAU,MAAM,SAAS,MAAM,IAAI,EAAE,UAAU,IAAI;QAEjE,IAAI,UAAU;YACZ,qBACE,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6GACA;gBAED,GAAG,KAAK;0BAER;;;;;;QAGP;QAEA,MAAM,WAAW,gBAAgB;QAEjC,qBACE,6LAAC,4IAAA,CAAA,YAAS;YAAC,WAAU;sBACnB,cAAA,6LAAC,4IAAA,CAAA,gBAAa;gBACZ,MAAM;gBACN,UAAU;gBACV,WAAU;;;;;;;;;;;IAIlB;IACA,KAAK,SAAS,aAAa,EAAE,QAAQ,EAAO;QAC1C,qBAAO;sBAAG;;IACZ;IACA,IAAI,SAAS,cAAc,EAAE,QAAQ,EAAE,GAAG,OAAY;QACpD,qBACE,6LAAC;YAAG,WAAU;YAAuB,GAAG,KAAK;sBAC1C;;;;;;IAGP;IACA,IAAI,SAAS,YAAY,EAAE,QAAQ,EAAE,GAAG,OAAY;QAClD,qBACE,6LAAC;YAAG,WAAU;YAA0B,GAAG,KAAK;sBAC7C;;;;;;IAGP;IACA,IAAI,SAAS,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAY;QAC/C,qBACE,6LAAC;YAAG,WAAU;YAAQ,GAAG,KAAK;sBAC3B;;;;;;IAGP;IACA,IAAI,SAAS,GAAG,EAAE,QAAQ,EAAE,GAAG,OAAY;QACzC,qBACE,6LAAC;YAAG,WAAU;YAA2B,GAAG,KAAK;sBAC9C;;;;;;IAGP;IACA,IAAI,SAAS,GAAG,EAAE,QAAQ,EAAE,GAAG,OAAY;QACzC,qBACE,6LAAC;YAAG,WAAU;YAA0B,GAAG,KAAK;sBAC7C;;;;;;IAGP;IACA,IAAI,SAAS,GAAG,EAAE,QAAQ,EAAE,GAAG,OAAY;QACzC,qBACE,6LAAC;YAAG,WAAU;YAA0B,GAAG,KAAK;sBAC7C;;;;;;IAGP;IACA,YAAY,SAAS,WAAW,EAAE,QAAQ,EAAE,GAAG,OAAY;QACzD,qBACE,6LAAC;YACC,WAAU;YACT,GAAG,KAAK;sBAER;;;;;;IAGP;IACA,GAAG,SAAS,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,GAAG,OAAY;QAClD,qBACE,6LAAC;YACC,MAAM;YACN,WAAU;YACV,QAAO;YACP,KAAI;YACH,GAAG,KAAK;sBAER;;;;;;IAGP;IACA,OAAO,SAAS,MAAM,EAAE,QAAQ,EAAE,GAAG,OAAY;QAC/C,qBACE,6LAAC;YAAM,WAAU;YAAuC,GAAG,KAAK;sBAC7D;;;;;;IAGP;IACA,IAAI,SAAS,YAAY,EAAE,QAAQ,EAAE,GAAG,OAAY;QAClD,qBACE,6LAAC;YACC,WAAU;YACT,GAAG,KAAK;sBAER;;;;;;IAGP;IACA,IAAI,SAAS,UAAU,EAAE,QAAQ,EAAE,GAAG,OAAY;QAChD,qBACE,6LAAC;YACC,WAAU;YACT,GAAG,KAAK;sBAER;;;;;;IAGP;AACF;AAEA,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAC/B,SAAS,cAAc,EACrB,OAAO,EACP,aAAa,kBAAkB,EAIhC;IACC,qBACE,6LAAC,2LAAA,CAAA,UAAa;QAAC,eAAe;YAAC,gJAAA,CAAA,UAAS;SAAC;QAAE,YAAY;kBACpD;;;;;;AAGP,GACA,SAAS,cAAc,SAAc,EAAE,SAAc;IACnD,OAAO,UAAU,OAAO,KAAK,UAAU,OAAO;AAChD;KAhBI;AAmBN,sBAAsB,WAAW,GAAG;AAEpC,SAAS,kBAAkB,EACzB,QAAQ,EACR,EAAE,EACF,SAAS,EACT,aAAa,kBAAkB,EACjB;;IACd,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,QAAK,AAAD;IACxB,MAAM,UAAU,MAAM;IACtB,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6CAAE,IAAM,wBAAwB;4CAAW;QAAC;KAAS;IAE1E,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oDACA;kBAGD,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,6LAAC;gBAEC,SAAS;gBACT,YAAY;eAFP,GAAG,QAAQ,OAAO,EAAE,OAAO;;;;;;;;;;AAO1C;GA1BS;;QAMa,6JAAA,CAAA,QAAK;;;MANlB;AA4BT,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE;;AACtB,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 6075, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>/response-stream.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport React, { useCallback, useEffect, useRef, useState } from 'react';\r\n\r\nexport type Mode = 'typewriter' | 'fade';\r\n\r\nexport type UseTextStreamOptions = {\r\n  textStream: string | AsyncIterable<string>;\r\n  speed?: number;\r\n  mode?: Mode;\r\n  onComplete?: () => void;\r\n  fadeDuration?: number;\r\n  segmentDelay?: number;\r\n  characterChunkSize?: number;\r\n  onError?: (error: unknown) => void;\r\n};\r\n\r\nexport type UseTextStreamResult = {\r\n  displayedText: string;\r\n  isComplete: boolean;\r\n  segments: { text: string; index: number }[];\r\n  getFadeDuration: () => number;\r\n  getSegmentDelay: () => number;\r\n  reset: () => void;\r\n  startStreaming: () => void;\r\n  pause: () => void;\r\n  resume: () => void;\r\n};\r\n\r\nfunction useTextStream({\r\n  textStream,\r\n  speed = 20,\r\n  mode = 'typewriter',\r\n  onComplete,\r\n  fadeDuration,\r\n  segmentDelay,\r\n  characterChunkSize,\r\n  onError,\r\n}: UseTextStreamOptions): UseTextStreamResult {\r\n  const [displayedText, setDisplayedText] = useState('');\r\n  const [isComplete, setIsComplete] = useState(false);\r\n  const [segments, setSegments] = useState<{ text: string; index: number }[]>(\r\n    [],\r\n  );\r\n\r\n  const speedRef = useRef(speed);\r\n  const modeRef = useRef(mode);\r\n  const currentIndexRef = useRef(0);\r\n  const animationRef = useRef<number | null>(null);\r\n  const fadeDurationRef = useRef(fadeDuration);\r\n  const segmentDelayRef = useRef(segmentDelay);\r\n  const characterChunkSizeRef = useRef(characterChunkSize);\r\n  const streamRef = useRef<AbortController | null>(null);\r\n  const completedRef = useRef(false);\r\n  const onCompleteRef = useRef(onComplete);\r\n\r\n  useEffect(() => {\r\n    speedRef.current = speed;\r\n    modeRef.current = mode;\r\n    fadeDurationRef.current = fadeDuration;\r\n    segmentDelayRef.current = segmentDelay;\r\n    characterChunkSizeRef.current = characterChunkSize;\r\n  }, [speed, mode, fadeDuration, segmentDelay, characterChunkSize]);\r\n\r\n  useEffect(() => {\r\n    onCompleteRef.current = onComplete;\r\n  }, [onComplete]);\r\n\r\n  const getChunkSize = useCallback(() => {\r\n    if (typeof characterChunkSizeRef.current === 'number') {\r\n      return Math.max(1, characterChunkSizeRef.current);\r\n    }\r\n\r\n    const normalizedSpeed = Math.min(100, Math.max(1, speedRef.current));\r\n\r\n    if (modeRef.current === 'typewriter') {\r\n      if (normalizedSpeed < 25) return 1;\r\n      return Math.max(1, Math.round((normalizedSpeed - 25) / 10));\r\n    } else if (modeRef.current === 'fade') {\r\n      return 1;\r\n    }\r\n\r\n    return 1;\r\n  }, []);\r\n\r\n  const getProcessingDelay = useCallback(() => {\r\n    if (typeof segmentDelayRef.current === 'number') {\r\n      return Math.max(0, segmentDelayRef.current);\r\n    }\r\n\r\n    const normalizedSpeed = Math.min(100, Math.max(1, speedRef.current));\r\n    return Math.max(1, Math.round(100 / Math.sqrt(normalizedSpeed)));\r\n  }, []);\r\n\r\n  const getFadeDuration = useCallback(() => {\r\n    if (typeof fadeDurationRef.current === 'number')\r\n      return Math.max(10, fadeDurationRef.current);\r\n\r\n    const normalizedSpeed = Math.min(100, Math.max(1, speedRef.current));\r\n    return Math.round(1000 / Math.sqrt(normalizedSpeed));\r\n  }, []);\r\n\r\n  const getSegmentDelay = useCallback(() => {\r\n    if (typeof segmentDelayRef.current === 'number')\r\n      return Math.max(0, segmentDelayRef.current);\r\n\r\n    const normalizedSpeed = Math.min(100, Math.max(1, speedRef.current));\r\n    return Math.max(1, Math.round(100 / Math.sqrt(normalizedSpeed)));\r\n  }, []);\r\n\r\n  const updateSegments = useCallback(\r\n    (text: string) => {\r\n      if (modeRef.current === 'fade') {\r\n        try {\r\n          const segmenter = new Intl.Segmenter(navigator.language, {\r\n            granularity: 'word',\r\n          });\r\n          const segmentIterator = segmenter.segment(text);\r\n          const newSegments = Array.from(segmentIterator).map(\r\n            (segment, index) => ({\r\n              text: segment.segment,\r\n              index,\r\n            }),\r\n          );\r\n          setSegments(newSegments);\r\n        } catch (error) {\r\n          const newSegments = text\r\n            .split(/(\\s+)/)\r\n            .filter(Boolean)\r\n            .map((word, index) => ({\r\n              text: word,\r\n              index,\r\n            }));\r\n          setSegments(newSegments);\r\n          onError?.(error);\r\n        }\r\n      }\r\n    },\r\n    [onError],\r\n  );\r\n\r\n  const markComplete = useCallback(() => {\r\n    if (!completedRef.current) {\r\n      completedRef.current = true;\r\n      setIsComplete(true);\r\n      onCompleteRef.current?.();\r\n    }\r\n  }, []);\r\n\r\n  const reset = useCallback(() => {\r\n    currentIndexRef.current = 0;\r\n    setDisplayedText('');\r\n    setSegments([]);\r\n    setIsComplete(false);\r\n    completedRef.current = false;\r\n\r\n    if (animationRef.current) {\r\n      cancelAnimationFrame(animationRef.current);\r\n      animationRef.current = null;\r\n    }\r\n  }, []);\r\n\r\n  const processStringTypewriter = useCallback(\r\n    (text: string) => {\r\n      let lastFrameTime = 0;\r\n\r\n      const streamContent = (timestamp: number) => {\r\n        const delay = getProcessingDelay();\r\n        if (delay > 0 && timestamp - lastFrameTime < delay) {\r\n          animationRef.current = requestAnimationFrame(streamContent);\r\n          return;\r\n        }\r\n        lastFrameTime = timestamp;\r\n\r\n        if (currentIndexRef.current >= text.length) {\r\n          markComplete();\r\n          return;\r\n        }\r\n\r\n        const chunkSize = getChunkSize();\r\n        const endIndex = Math.min(\r\n          currentIndexRef.current + chunkSize,\r\n          text.length,\r\n        );\r\n        const newDisplayedText = text.slice(0, endIndex);\r\n\r\n        setDisplayedText(newDisplayedText);\r\n        if (modeRef.current === 'fade') {\r\n          updateSegments(newDisplayedText);\r\n        }\r\n\r\n        currentIndexRef.current = endIndex;\r\n\r\n        if (endIndex < text.length) {\r\n          animationRef.current = requestAnimationFrame(streamContent);\r\n        } else {\r\n          markComplete();\r\n        }\r\n      };\r\n\r\n      animationRef.current = requestAnimationFrame(streamContent);\r\n    },\r\n    [getProcessingDelay, getChunkSize, updateSegments, markComplete],\r\n  );\r\n\r\n  const processAsyncIterable = useCallback(\r\n    async (stream: AsyncIterable<string>) => {\r\n      const controller = new AbortController();\r\n      streamRef.current = controller;\r\n\r\n      let displayed = '';\r\n\r\n      try {\r\n        for await (const chunk of stream) {\r\n          if (controller.signal.aborted) return;\r\n\r\n          displayed += chunk;\r\n          setDisplayedText(displayed);\r\n          updateSegments(displayed);\r\n        }\r\n\r\n        markComplete();\r\n      } catch (error) {\r\n        console.error('Error processing text stream:', error);\r\n        markComplete();\r\n        onError?.(error);\r\n      }\r\n    },\r\n    [updateSegments, markComplete, onError],\r\n  );\r\n\r\n  const startStreaming = useCallback(() => {\r\n    reset();\r\n\r\n    if (typeof textStream === 'string') {\r\n      processStringTypewriter(textStream);\r\n    } else if (textStream) {\r\n      processAsyncIterable(textStream);\r\n    }\r\n  }, [textStream, reset, processStringTypewriter, processAsyncIterable]);\r\n\r\n  const pause = useCallback(() => {\r\n    if (animationRef.current) {\r\n      cancelAnimationFrame(animationRef.current);\r\n      animationRef.current = null;\r\n    }\r\n  }, []);\r\n\r\n  const resume = useCallback(() => {\r\n    if (typeof textStream === 'string' && !isComplete) {\r\n      processStringTypewriter(textStream);\r\n    }\r\n  }, [textStream, isComplete, processStringTypewriter]);\r\n\r\n  useEffect(() => {\r\n    startStreaming();\r\n\r\n    return () => {\r\n      if (animationRef.current) {\r\n        cancelAnimationFrame(animationRef.current);\r\n      }\r\n      if (streamRef.current) {\r\n        streamRef.current.abort();\r\n      }\r\n    };\r\n  }, [textStream, startStreaming]);\r\n\r\n  return {\r\n    displayedText,\r\n    isComplete,\r\n    segments,\r\n    getFadeDuration,\r\n    getSegmentDelay,\r\n    reset,\r\n    startStreaming,\r\n    pause,\r\n    resume,\r\n  };\r\n}\r\n\r\nexport type ResponseStreamProps = {\r\n  textStream: string | AsyncIterable<string>;\r\n  mode?: Mode;\r\n  speed?: number; // 1-100, where 1 is slowest and 100 is fastest\r\n  className?: string;\r\n  onComplete?: () => void;\r\n  as?: keyof React.JSX.IntrinsicElements; // Element type to render\r\n  fadeDuration?: number; // Custom fade duration in ms (overrides speed)\r\n  segmentDelay?: number; // Custom delay between segments in ms (overrides speed)\r\n  characterChunkSize?: number; // Custom characters per frame for typewriter mode (overrides speed)\r\n};\r\n\r\nfunction ResponseStream({\r\n  textStream,\r\n  mode = 'typewriter',\r\n  speed = 20,\r\n  className = '',\r\n  onComplete,\r\n  as = 'div',\r\n  fadeDuration,\r\n  segmentDelay,\r\n  characterChunkSize,\r\n}: ResponseStreamProps) {\r\n  const animationEndRef = useRef<(() => void) | null>(null);\r\n\r\n  const {\r\n    displayedText,\r\n    isComplete,\r\n    segments,\r\n    getFadeDuration,\r\n    getSegmentDelay,\r\n  } = useTextStream({\r\n    textStream,\r\n    speed,\r\n    mode,\r\n    onComplete,\r\n    fadeDuration,\r\n    segmentDelay,\r\n    characterChunkSize,\r\n  });\r\n\r\n  useEffect(() => {\r\n    animationEndRef.current = onComplete ?? null;\r\n  }, [onComplete]);\r\n\r\n  const handleLastSegmentAnimationEnd = useCallback(() => {\r\n    if (animationEndRef.current && isComplete) {\r\n      animationEndRef.current();\r\n    }\r\n  }, [isComplete]);\r\n\r\n  // fadeStyle is the style for the fade animation\r\n  const fadeStyle = `\r\n    @keyframes fadeIn {\r\n      from { opacity: 0; }\r\n      to { opacity: 1; }\r\n    }\r\n    \r\n    .fade-segment {\r\n      display: inline-block;\r\n      opacity: 0;\r\n      animation: fadeIn ${getFadeDuration()}ms ease-out forwards;\r\n    }\r\n\r\n    .fade-segment-space {\r\n      white-space: pre;\r\n    }\r\n  `;\r\n\r\n  const renderContent = () => {\r\n    switch (mode) {\r\n      case 'typewriter':\r\n        return <>{displayedText}</>;\r\n\r\n      case 'fade':\r\n        return (\r\n          <>\r\n            <style>{fadeStyle}</style>\r\n            <div className=\"relative\">\r\n              {segments.map((segment, idx) => {\r\n                const isWhitespace = /^\\s+$/.test(segment.text);\r\n                const isLastSegment = idx === segments.length - 1;\r\n\r\n                return (\r\n                  <span\r\n                    key={`${segment.text}-${idx}`}\r\n                    className={cn(\r\n                      'fade-segment',\r\n                      isWhitespace && 'fade-segment-space',\r\n                    )}\r\n                    style={{\r\n                      animationDelay: `${idx * getSegmentDelay()}ms`,\r\n                    }}\r\n                    onAnimationEnd={\r\n                      isLastSegment ? handleLastSegmentAnimationEnd : undefined\r\n                    }\r\n                  >\r\n                    {segment.text}\r\n                  </span>\r\n                );\r\n              })}\r\n            </div>\r\n          </>\r\n        );\r\n\r\n      default:\r\n        return <>{displayedText}</>;\r\n    }\r\n  };\r\n\r\n  const Container = as as keyof React.JSX.IntrinsicElements;\r\n\r\n  return <Container className={className}>{renderContent()}</Container>;\r\n}\r\n\r\nexport { useTextStream, ResponseStream };\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AA8BA,SAAS,cAAc,EACrB,UAAU,EACV,QAAQ,EAAE,EACV,OAAO,YAAY,EACnB,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,kBAAkB,EAClB,OAAO,EACc;;IACrB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EACrC,EAAE;IAGJ,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;IAC3C,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACrC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAA0B;IACjD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,SAAS,OAAO,GAAG;YACnB,QAAQ,OAAO,GAAG;YAClB,gBAAgB,OAAO,GAAG;YAC1B,gBAAgB,OAAO,GAAG;YAC1B,sBAAsB,OAAO,GAAG;QAClC;kCAAG;QAAC;QAAO;QAAM;QAAc;QAAc;KAAmB;IAEhE,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,cAAc,OAAO,GAAG;QAC1B;kCAAG;QAAC;KAAW;IAEf,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YAC/B,IAAI,OAAO,sBAAsB,OAAO,KAAK,UAAU;gBACrD,OAAO,KAAK,GAAG,CAAC,GAAG,sBAAsB,OAAO;YAClD;YAEA,MAAM,kBAAkB,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,SAAS,OAAO;YAElE,IAAI,QAAQ,OAAO,KAAK,cAAc;gBACpC,IAAI,kBAAkB,IAAI,OAAO;gBACjC,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,CAAC,kBAAkB,EAAE,IAAI;YACzD,OAAO,IAAI,QAAQ,OAAO,KAAK,QAAQ;gBACrC,OAAO;YACT;YAEA,OAAO;QACT;kDAAG,EAAE;IAEL,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;YACrC,IAAI,OAAO,gBAAgB,OAAO,KAAK,UAAU;gBAC/C,OAAO,KAAK,GAAG,CAAC,GAAG,gBAAgB,OAAO;YAC5C;YAEA,MAAM,kBAAkB,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,SAAS,OAAO;YAClE,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC;QAChD;wDAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAClC,IAAI,OAAO,gBAAgB,OAAO,KAAK,UACrC,OAAO,KAAK,GAAG,CAAC,IAAI,gBAAgB,OAAO;YAE7C,MAAM,kBAAkB,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,SAAS,OAAO;YAClE,OAAO,KAAK,KAAK,CAAC,OAAO,KAAK,IAAI,CAAC;QACrC;qDAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAClC,IAAI,OAAO,gBAAgB,OAAO,KAAK,UACrC,OAAO,KAAK,GAAG,CAAC,GAAG,gBAAgB,OAAO;YAE5C,MAAM,kBAAkB,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,GAAG,SAAS,OAAO;YAClE,OAAO,KAAK,GAAG,CAAC,GAAG,KAAK,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC;QAChD;qDAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAC/B,CAAC;YACC,IAAI,QAAQ,OAAO,KAAK,QAAQ;gBAC9B,IAAI;oBACF,MAAM,YAAY,IAAI,KAAK,SAAS,CAAC,UAAU,QAAQ,EAAE;wBACvD,aAAa;oBACf;oBACA,MAAM,kBAAkB,UAAU,OAAO,CAAC;oBAC1C,MAAM,cAAc,MAAM,IAAI,CAAC,iBAAiB,GAAG;iFACjD,CAAC,SAAS,QAAU,CAAC;gCACnB,MAAM,QAAQ,OAAO;gCACrB;4BACF,CAAC;;oBAEH,YAAY;gBACd,EAAE,OAAO,OAAO;oBACd,MAAM,cAAc,KACjB,KAAK,CAAC,SACN,MAAM,CAAC,SACP,GAAG;iFAAC,CAAC,MAAM,QAAU,CAAC;gCACrB,MAAM;gCACN;4BACF,CAAC;;oBACH,YAAY;oBACZ,UAAU;gBACZ;YACF;QACF;oDACA;QAAC;KAAQ;IAGX,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YAC/B,IAAI,CAAC,aAAa,OAAO,EAAE;gBACzB,aAAa,OAAO,GAAG;gBACvB,cAAc;gBACd,cAAc,OAAO;YACvB;QACF;kDAAG,EAAE;IAEL,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE;YACxB,gBAAgB,OAAO,GAAG;YAC1B,iBAAiB;YACjB,YAAY,EAAE;YACd,cAAc;YACd,aAAa,OAAO,GAAG;YAEvB,IAAI,aAAa,OAAO,EAAE;gBACxB,qBAAqB,aAAa,OAAO;gBACzC,aAAa,OAAO,GAAG;YACzB;QACF;2CAAG,EAAE;IAEL,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DACxC,CAAC;YACC,IAAI,gBAAgB;YAEpB,MAAM;oFAAgB,CAAC;oBACrB,MAAM,QAAQ;oBACd,IAAI,QAAQ,KAAK,YAAY,gBAAgB,OAAO;wBAClD,aAAa,OAAO,GAAG,sBAAsB;wBAC7C;oBACF;oBACA,gBAAgB;oBAEhB,IAAI,gBAAgB,OAAO,IAAI,KAAK,MAAM,EAAE;wBAC1C;wBACA;oBACF;oBAEA,MAAM,YAAY;oBAClB,MAAM,WAAW,KAAK,GAAG,CACvB,gBAAgB,OAAO,GAAG,WAC1B,KAAK,MAAM;oBAEb,MAAM,mBAAmB,KAAK,KAAK,CAAC,GAAG;oBAEvC,iBAAiB;oBACjB,IAAI,QAAQ,OAAO,KAAK,QAAQ;wBAC9B,eAAe;oBACjB;oBAEA,gBAAgB,OAAO,GAAG;oBAE1B,IAAI,WAAW,KAAK,MAAM,EAAE;wBAC1B,aAAa,OAAO,GAAG,sBAAsB;oBAC/C,OAAO;wBACL;oBACF;gBACF;;YAEA,aAAa,OAAO,GAAG,sBAAsB;QAC/C;6DACA;QAAC;QAAoB;QAAc;QAAgB;KAAa;IAGlE,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DACrC,OAAO;YACL,MAAM,aAAa,IAAI;YACvB,UAAU,OAAO,GAAG;YAEpB,IAAI,YAAY;YAEhB,IAAI;gBACF,WAAW,MAAM,SAAS,OAAQ;oBAChC,IAAI,WAAW,MAAM,CAAC,OAAO,EAAE;oBAE/B,aAAa;oBACb,iBAAiB;oBACjB,eAAe;gBACjB;gBAEA;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;gBAC/C;gBACA,UAAU;YACZ;QACF;0DACA;QAAC;QAAgB;QAAc;KAAQ;IAGzC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAAE;YACjC;YAEA,IAAI,OAAO,eAAe,UAAU;gBAClC,wBAAwB;YAC1B,OAAO,IAAI,YAAY;gBACrB,qBAAqB;YACvB;QACF;oDAAG;QAAC;QAAY;QAAO;QAAyB;KAAqB;IAErE,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;4CAAE;YACxB,IAAI,aAAa,OAAO,EAAE;gBACxB,qBAAqB,aAAa,OAAO;gBACzC,aAAa,OAAO,GAAG;YACzB;QACF;2CAAG,EAAE;IAEL,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE;YACzB,IAAI,OAAO,eAAe,YAAY,CAAC,YAAY;gBACjD,wBAAwB;YAC1B;QACF;4CAAG;QAAC;QAAY;QAAY;KAAwB;IAEpD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;YAEA;2CAAO;oBACL,IAAI,aAAa,OAAO,EAAE;wBACxB,qBAAqB,aAAa,OAAO;oBAC3C;oBACA,IAAI,UAAU,OAAO,EAAE;wBACrB,UAAU,OAAO,CAAC,KAAK;oBACzB;gBACF;;QACF;kCAAG;QAAC;QAAY;KAAe;IAE/B,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAzPS;AAuQT,SAAS,eAAe,EACtB,UAAU,EACV,OAAO,YAAY,EACnB,QAAQ,EAAE,EACV,YAAY,EAAE,EACd,UAAU,EACV,KAAK,KAAK,EACV,YAAY,EACZ,YAAY,EACZ,kBAAkB,EACE;;IACpB,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAuB;IAEpD,MAAM,EACJ,aAAa,EACb,UAAU,EACV,QAAQ,EACR,eAAe,EACf,eAAe,EAChB,GAAG,cAAc;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,gBAAgB,OAAO,GAAG,cAAc;QAC1C;mCAAG;QAAC;KAAW;IAEf,MAAM,gCAAgC,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qEAAE;YAChD,IAAI,gBAAgB,OAAO,IAAI,YAAY;gBACzC,gBAAgB,OAAO;YACzB;QACF;oEAAG;QAAC;KAAW;IAEf,gDAAgD;IAChD,MAAM,YAAY,CAAC;;;;;;;;;wBASG,EAAE,kBAAkB;;;;;;EAM1C,CAAC;IAED,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,qBAAO;8BAAG;;YAEZ,KAAK;gBACH,qBACE;;sCACE,6LAAC;sCAAO;;;;;;sCACR,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS;gCACtB,MAAM,eAAe,QAAQ,IAAI,CAAC,QAAQ,IAAI;gCAC9C,MAAM,gBAAgB,QAAQ,SAAS,MAAM,GAAG;gCAEhD,qBACE,6LAAC;oCAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBACA,gBAAgB;oCAElB,OAAO;wCACL,gBAAgB,GAAG,MAAM,kBAAkB,EAAE,CAAC;oCAChD;oCACA,gBACE,gBAAgB,gCAAgC;8CAGjD,QAAQ,IAAI;mCAZR,GAAG,QAAQ,IAAI,CAAC,CAAC,EAAE,KAAK;;;;;4BAenC;;;;;;;;YAKR;gBACE,qBAAO;8BAAG;;QACd;IACF;IAEA,MAAM,YAAY;IAElB,qBAAO,6LAAC;QAAU,WAAW;kBAAY;;;;;;AAC3C;IArGS;;QAmBH;;;KAnBG", "debugId": null}}, {"offset": {"line": 6461, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>/reasoning.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { ChevronDownIcon } from 'lucide-react';\r\nimport React, {\r\n  createContext,\r\n  useContext,\r\n  useEffect,\r\n  useRef,\r\n  useState,\r\n} from 'react';\r\nimport { Markdown } from '../../ui/markdown';\r\nimport { useTextStream, type Mode } from './response-stream';\r\n\r\ntype ReasoningContextType = {\r\n  isOpen: boolean;\r\n  onOpenChange: (open: boolean) => void;\r\n};\r\n\r\nconst ReasoningContext = createContext<ReasoningContextType | undefined>(\r\n  undefined,\r\n);\r\n\r\nfunction useReasoningContext() {\r\n  const context = useContext(ReasoningContext);\r\n  if (!context) {\r\n    throw new Error(\r\n      'useReasoningContext must be used within a Reasoning provider',\r\n    );\r\n  }\r\n  return context;\r\n}\r\n\r\nexport type ReasoningProps = {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n  open?: boolean;\r\n  onOpenChange?: (open: boolean) => void;\r\n};\r\n\r\nfunction Reasoning({\r\n  children,\r\n  className,\r\n  open,\r\n  onOpenChange,\r\n}: ReasoningProps) {\r\n  const [internalOpen, setInternalOpen] = useState(true);\r\n  const isControlled = open !== undefined;\r\n  const isOpen = isControlled ? open : internalOpen;\r\n\r\n  const handleOpenChange = (newOpen: boolean) => {\r\n    if (!isControlled) {\r\n      setInternalOpen(newOpen);\r\n    }\r\n    onOpenChange?.(newOpen);\r\n  };\r\n\r\n  return (\r\n    <ReasoningContext.Provider\r\n      value={{\r\n        isOpen,\r\n        onOpenChange: handleOpenChange,\r\n      }}\r\n    >\r\n      <div className={className}>{children}</div>\r\n    </ReasoningContext.Provider>\r\n  );\r\n}\r\n\r\nexport type ReasoningTriggerProps = {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n} & React.HTMLAttributes<HTMLButtonElement>;\r\n\r\nfunction ReasoningTrigger({\r\n  children,\r\n  className,\r\n  ...props\r\n}: ReasoningTriggerProps) {\r\n  const { isOpen, onOpenChange } = useReasoningContext();\r\n\r\n  return (\r\n    <button\r\n      className={cn('flex cursor-pointer items-center gap-2', className)}\r\n      onClick={() => onOpenChange(!isOpen)}\r\n      {...props}\r\n    >\r\n      <span className=\"text-primary\">{children}</span>\r\n      <div\r\n        className={cn(\r\n          'transform transition-transform',\r\n          isOpen ? 'rotate-180' : '',\r\n        )}\r\n      >\r\n        <ChevronDownIcon className=\"size-4\" />\r\n      </div>\r\n    </button>\r\n  );\r\n}\r\n\r\nexport type ReasoningContentProps = {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n} & React.HTMLAttributes<HTMLDivElement>;\r\n\r\nfunction ReasoningContent({\r\n  children,\r\n  className,\r\n  ...props\r\n}: ReasoningContentProps) {\r\n  const contentRef = useRef<HTMLDivElement>(null);\r\n  const innerRef = useRef<HTMLDivElement>(null);\r\n  const { isOpen } = useReasoningContext();\r\n\r\n  useEffect(() => {\r\n    if (!contentRef.current || !innerRef.current) return;\r\n\r\n    const observer = new ResizeObserver(() => {\r\n      if (contentRef.current && innerRef.current && isOpen) {\r\n        contentRef.current.style.maxHeight = `${innerRef.current.scrollHeight}px`;\r\n      }\r\n    });\r\n\r\n    observer.observe(innerRef.current);\r\n\r\n    if (isOpen) {\r\n      contentRef.current.style.maxHeight = `${innerRef.current.scrollHeight}px`;\r\n    }\r\n\r\n    return () => observer.disconnect();\r\n  }, [isOpen]);\r\n\r\n  return (\r\n    <div\r\n      ref={contentRef}\r\n      className={cn(\r\n        'overflow-hidden transition-[max-height] duration-300 ease-out',\r\n        className,\r\n      )}\r\n      style={{\r\n        maxHeight: isOpen ? contentRef.current?.scrollHeight : '0px',\r\n      }}\r\n      {...props}\r\n    >\r\n      <div ref={innerRef}>{children}</div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport type ReasoningResponseProps = {\r\n  text: string | AsyncIterable<string>;\r\n  className?: string;\r\n  speed?: number;\r\n  mode?: Mode;\r\n  onComplete?: () => void;\r\n  fadeDuration?: number;\r\n  segmentDelay?: number;\r\n  characterChunkSize?: number;\r\n};\r\n\r\nfunction ReasoningResponse({\r\n  text,\r\n  className,\r\n  speed = 20,\r\n  mode = 'typewriter',\r\n  onComplete,\r\n  fadeDuration,\r\n  segmentDelay,\r\n  characterChunkSize,\r\n}: ReasoningResponseProps) {\r\n  const { isOpen } = useReasoningContext();\r\n  const { displayedText } = useTextStream({\r\n    textStream: text,\r\n    speed,\r\n    mode,\r\n    onComplete,\r\n    fadeDuration,\r\n    segmentDelay,\r\n    characterChunkSize,\r\n  });\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        'text-muted-foreground prose prose-sm dark:prose-invert text-sm transition-opacity duration-300 ease-out',\r\n        className,\r\n      )}\r\n      style={{\r\n        opacity: isOpen ? 1 : 0,\r\n      }}\r\n    >\r\n      <Markdown>{displayedText}</Markdown>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport { Reasoning, ReasoningTrigger, ReasoningContent, ReasoningResponse };\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAOA;AACA;;;AAZA;;;;;;AAmBA,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EACnC;AAGF,SAAS;;IACP,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MACR;IAEJ;IACA,OAAO;AACT;GARS;AAiBT,SAAS,UAAU,EACjB,QAAQ,EACR,SAAS,EACT,IAAI,EACJ,YAAY,EACG;;IACf,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,eAAe,SAAS;IAC9B,MAAM,SAAS,eAAe,OAAO;IAErC,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,cAAc;YACjB,gBAAgB;QAClB;QACA,eAAe;IACjB;IAEA,qBACE,6LAAC,iBAAiB,QAAQ;QACxB,OAAO;YACL;YACA,cAAc;QAChB;kBAEA,cAAA,6LAAC;YAAI,WAAW;sBAAY;;;;;;;;;;;AAGlC;IA3BS;KAAA;AAkCT,SAAS,iBAAiB,EACxB,QAAQ,EACR,SAAS,EACT,GAAG,OACmB;;IACtB,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG;IAEjC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACxD,SAAS,IAAM,aAAa,CAAC;QAC5B,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BAAgB;;;;;;0BAChC,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kCACA,SAAS,eAAe;0BAG1B,cAAA,6LAAC,2NAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;IAxBS;;QAK0B;;;MAL1B;AA+BT,SAAS,iBAAiB,EACxB,QAAQ,EACR,SAAS,EACT,GAAG,OACmB;;IACtB,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACxC,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,WAAW,OAAO,IAAI,CAAC,SAAS,OAAO,EAAE;YAE9C,MAAM,WAAW,IAAI;8CAAe;oBAClC,IAAI,WAAW,OAAO,IAAI,SAAS,OAAO,IAAI,QAAQ;wBACpD,WAAW,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,SAAS,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;oBAC3E;gBACF;;YAEA,SAAS,OAAO,CAAC,SAAS,OAAO;YAEjC,IAAI,QAAQ;gBACV,WAAW,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,SAAS,OAAO,CAAC,YAAY,CAAC,EAAE,CAAC;YAC3E;YAEA;8CAAO,IAAM,SAAS,UAAU;;QAClC;qCAAG;QAAC;KAAO;IAEX,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAEF,OAAO;YACL,WAAW,SAAS,WAAW,OAAO,EAAE,eAAe;QACzD;QACC,GAAG,KAAK;kBAET,cAAA,6LAAC;YAAI,KAAK;sBAAW;;;;;;;;;;;AAG3B;IA1CS;;QAOY;;;MAPZ;AAuDT,SAAS,kBAAkB,EACzB,IAAI,EACJ,SAAS,EACT,QAAQ,EAAE,EACV,OAAO,YAAY,EACnB,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,kBAAkB,EACK;;IACvB,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,gBAAa,AAAD,EAAE;QACtC,YAAY;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2GACA;QAEF,OAAO;YACL,SAAS,SAAS,IAAI;QACxB;kBAEA,cAAA,6LAAC,uIAAA,CAAA,WAAQ;sBAAE;;;;;;;;;;;AAGjB;IAlCS;;QAUY;QACO,yJAAA,CAAA,gBAAa;;;MAXhC", "debugId": null}}, {"offset": {"line": 6671, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>"], "sourcesContent": ["/* eslint-disable @next/next/no-img-element */\r\n'use client';\r\n\r\nimport { Icons } from '@/components/home/<USER>';\r\nimport {\r\n  Reasoning,\r\n  ReasoningContent,\r\n  ReasoningResponse,\r\n} from '@/components/home/<USER>/reasoning';\r\nimport { AnimatePresence, motion, useInView } from 'motion/react';\r\nimport { useEffect, useRef, useState } from 'react';\r\n\r\nexport function ReasoningBasic() {\r\n  const reasoningText = `Based on your calendar patterns and preferences, I recommend scheduling the team meeting for Tuesday at 2pm. This time slot has historically had the highest attendance rate, and it avoids conflicts with other recurring meetings.`;\r\n\r\n  return (\r\n    <Reasoning>\r\n      <ReasoningContent className=\"\">\r\n        <ReasoningResponse text={reasoningText} />\r\n      </ReasoningContent>\r\n    </Reasoning>\r\n  );\r\n}\r\n\r\nexport function FirstBentoAnimation() {\r\n  const ref = useRef(null);\r\n  const isInView = useInView(ref);\r\n  const [shouldAnimate, setShouldAnimate] = useState(false);\r\n\r\n  useEffect(() => {\r\n    let timeoutId: NodeJS.Timeout;\r\n    if (isInView) {\r\n      timeoutId = setTimeout(() => {\r\n        setShouldAnimate(true);\r\n      }, 1000);\r\n    } else {\r\n      setShouldAnimate(false);\r\n    }\r\n\r\n    return () => {\r\n      if (timeoutId) clearTimeout(timeoutId);\r\n    };\r\n  }, [isInView]);\r\n\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      className=\"w-full h-full p-4 flex flex-col items-center justify-center gap-5\"\r\n    >\r\n      <div className=\"pointer-events-none absolute bottom-0 left-0 h-20 w-full bg-gradient-to-t from-background to-transparent z-20\"></div>\r\n      <motion.div\r\n        className=\"max-w-md mx-auto w-full flex flex-col gap-2\"\r\n        animate={{\r\n          y: shouldAnimate ? -75 : 0,\r\n        }}\r\n        transition={{\r\n          type: 'spring',\r\n          stiffness: 300,\r\n          damping: 20,\r\n        }}\r\n      >\r\n        <div className=\"flex items-end justify-end gap-3\">\r\n          <motion.div\r\n            className=\"max-w-[280px] bg-secondary text-white p-4 rounded-2xl ml-auto shadow-[0_0_10px_rgba(0,0,0,0.05)]\"\r\n            initial={{ opacity: 0, x: 20 }}\r\n            animate={{ opacity: 1, x: 0 }}\r\n            transition={{\r\n              duration: 0.3,\r\n              ease: 'easeOut',\r\n            }}\r\n          >\r\n            <p className=\"text-sm\">\r\n              Hey, I need help scheduling a team meeting that works well for\r\n              everyone. Any suggestions for finding an optimal time slot?\r\n            </p>\r\n          </motion.div>\r\n          <div className=\"flex items-center bg-background rounded-full w-fit border border-border flex-shrink-0\">\r\n            <img\r\n              src=\"https://randomuser.me/api/portraits/women/79.jpg\"\r\n              alt=\"User Avatar\"\r\n              className=\"size-8 rounded-full flex-shrink-0\"\r\n            />\r\n          </div>\r\n        </div>\r\n        <div className=\"flex items-start gap-2\">\r\n          <div className=\"flex items-center bg-background rounded-full size-10 flex-shrink-0 justify-center shadow-[0_0_10px_rgba(0,0,0,0.05)] border border-border\">\r\n            <Icons.logo className=\"size-4\" />\r\n          </div>\r\n\r\n          <div className=\"relative\">\r\n            <AnimatePresence mode=\"wait\">\r\n              {!shouldAnimate ? (\r\n                <motion.div\r\n                  key=\"dots\"\r\n                  className=\"absolute left-0 top-0 bg-background p-4 rounded-2xl border border-border\"\r\n                  initial={{ opacity: 0, x: -20 }}\r\n                  animate={{ opacity: 1, x: 0 }}\r\n                  exit={{ opacity: 0, x: -10 }}\r\n                  transition={{\r\n                    duration: 0.2,\r\n                    ease: 'easeOut',\r\n                  }}\r\n                >\r\n                  <div className=\"flex gap-1\">\r\n                    {[0, 1, 2].map((index) => (\r\n                      <motion.div\r\n                        key={index}\r\n                        className=\"w-2 h-2 bg-primary/50 rounded-full\"\r\n                        animate={{ y: [0, -5, 0] }}\r\n                        transition={{\r\n                          duration: 0.6,\r\n                          repeat: Infinity,\r\n                          delay: index * 0.2,\r\n                          ease: 'easeInOut',\r\n                        }}\r\n                      />\r\n                    ))}\r\n                  </div>\r\n                </motion.div>\r\n              ) : (\r\n                <motion.div\r\n                  key=\"response\"\r\n                  layout\r\n                  className=\"absolute left-0 top-0 md:min-w-[300px] min-w-[220px] p-4 bg-accent border border-border rounded-xl shadow-[0_0_10px_rgba(0,0,0,0.05)]\"\r\n                  initial={{ opacity: 0, x: 10 }}\r\n                  animate={{\r\n                    opacity: 1,\r\n                    x: 0,\r\n                  }}\r\n                  exit={{ opacity: 0, x: 20 }}\r\n                  transition={{\r\n                    duration: 0.3,\r\n                    ease: 'easeOut',\r\n                  }}\r\n                >\r\n                  <ReasoningBasic />\r\n                </motion.div>\r\n              )}\r\n            </AnimatePresence>\r\n          </div>\r\n        </div>\r\n      </motion.div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": "AAAA,4CAA4C;;;;;AAG5C;AACA;AAKA;AAAA;AAAA;AACA;;;AATA;;;;;AAWO,SAAS;IACd,MAAM,gBAAgB,CAAC,oOAAoO,CAAC;IAE5P,qBACE,6LAAC,gJAAA,CAAA,YAAS;kBACR,cAAA,6LAAC,gJAAA,CAAA,mBAAgB;YAAC,WAAU;sBAC1B,cAAA,6LAAC,gJAAA,CAAA,oBAAiB;gBAAC,MAAM;;;;;;;;;;;;;;;;AAIjC;KAVgB;AAYT,SAAS;;IACd,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,wMAAA,CAAA,YAAS,AAAD,EAAE;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI;YACJ,IAAI,UAAU;gBACZ,YAAY;qDAAW;wBACrB,iBAAiB;oBACnB;oDAAG;YACL,OAAO;gBACL,iBAAiB;YACnB;YAEA;iDAAO;oBACL,IAAI,WAAW,aAAa;gBAC9B;;QACF;wCAAG;QAAC;KAAS;IAEb,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG,gBAAgB,CAAC,KAAK;gBAC3B;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;gBACX;;kCAEA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCACV,UAAU;oCACV,MAAM;gCACR;0CAEA,cAAA,6LAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;0CAKzB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,KAAI;oCACJ,KAAI;oCACJ,WAAU;;;;;;;;;;;;;;;;;kCAIhB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,sIAAA,CAAA,QAAK,CAAC,IAAI;oCAAC,WAAU;;;;;;;;;;;0CAGxB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,oNAAA,CAAA,kBAAe;oCAAC,MAAK;8CACnB,CAAC,8BACA,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC3B,YAAY;4CACV,UAAU;4CACV,MAAM;wCACR;kDAEA,cAAA,6LAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAG;gDAAG;6CAAE,CAAC,GAAG,CAAC,CAAC,sBACd,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;oDAET,WAAU;oDACV,SAAS;wDAAE,GAAG;4DAAC;4DAAG,CAAC;4DAAG;yDAAE;oDAAC;oDACzB,YAAY;wDACV,UAAU;wDACV,QAAQ;wDACR,OAAO,QAAQ;wDACf,MAAM;oDACR;mDARK;;;;;;;;;;uCAbP;;;;6DA2BN,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;wCAET,MAAM;wCACN,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CACP,SAAS;4CACT,GAAG;wCACL;wCACA,MAAM;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC1B,YAAY;4CACV,UAAU;4CACV,MAAM;wCACR;kDAEA,cAAA,6LAAC;;;;;uCAdG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBtB;GAxHgB;;QAEG,wMAAA,CAAA,YAAS;;;MAFZ", "debugId": null}}, {"offset": {"line": 6963, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>"], "sourcesContent": ["'use client';\r\n\r\nimport {\r\n  AnimatePresence,\r\n  motion,\r\n  useInView,\r\n  useMotionValue,\r\n  useSpring,\r\n} from 'motion/react';\r\nimport { useEffect, useRef, useState } from 'react';\r\n\r\ninterface BoxConfig {\r\n  title: string;\r\n  className: string;\r\n}\r\n\r\nconst boxConfigs: BoxConfig[] = [\r\n  {\r\n    title: 'Bento grid',\r\n    className: 'bg-secondary text-white',\r\n  },\r\n  {\r\n    title: 'Landing Page',\r\n    className: 'bg-secondary/40 text-white',\r\n  },\r\n  {\r\n    title: 'Add Task',\r\n    className:\r\n      'bg-secondary/20 border border-secondary border-dashed text-secondary',\r\n  },\r\n];\r\n\r\ninterface FourthBentoAnimationProps {\r\n  startAnimationDelay?: number;\r\n  once?: boolean;\r\n}\r\n\r\nexport function FourthBentoAnimation({\r\n  once = false,\r\n  startAnimationDelay = 0,\r\n}: FourthBentoAnimationProps) {\r\n  const containerRef = useRef<HTMLDivElement | null>(null);\r\n  const isInView = useInView(containerRef, { once });\r\n  const [translateXValues, setTranslateXValues] = useState<number[]>([]);\r\n\r\n  const mouseX = useMotionValue(0);\r\n  const smoothX = useSpring(mouseX, {\r\n    damping: 50,\r\n    stiffness: 400,\r\n  });\r\n\r\n  useEffect(() => {\r\n    if (containerRef.current) {\r\n      const rect = containerRef.current.getBoundingClientRect();\r\n      mouseX.set(rect.width / 2);\r\n    }\r\n  }, [mouseX]);\r\n\r\n  const handleMouseMove = (e: React.MouseEvent) => {\r\n    if (containerRef.current) {\r\n      const rect = containerRef.current.getBoundingClientRect();\r\n      const adjustedX = e.clientX - rect.left + 100;\r\n      mouseX.set(adjustedX);\r\n    }\r\n  };\r\n\r\n  const handleMouseLeave = () => {\r\n    if (containerRef.current) {\r\n      const rect = containerRef.current.getBoundingClientRect();\r\n      mouseX.set(rect.width / 2);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const updateWidth = () => {\r\n      if (containerRef.current) {\r\n        const containerWidth =\r\n          containerRef.current.getBoundingClientRect().width;\r\n        const itemWidth = 250;\r\n        const numberOfItems = 3;\r\n        const totalItemsWidth = itemWidth * numberOfItems;\r\n        const availableSpace = containerWidth - totalItemsWidth;\r\n        const gap = availableSpace / (numberOfItems - 1);\r\n\r\n        const newTranslateXValues = Array.from(\r\n          { length: numberOfItems },\r\n          (_, index) => {\r\n            return ((itemWidth + gap) * index) / 2;\r\n          },\r\n        );\r\n        setTranslateXValues(newTranslateXValues);\r\n      }\r\n    };\r\n\r\n    updateWidth();\r\n\r\n    window.addEventListener('resize', updateWidth);\r\n\r\n    // Cleanup event listener on component unmount\r\n    return () => {\r\n      window.removeEventListener('resize', updateWidth);\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <div\r\n      className=\"w-full h-full flex flex-col relative\"\r\n      onMouseMove={handleMouseMove}\r\n      onMouseLeave={handleMouseLeave}\r\n    >\r\n      <div className=\"absolute inset-0 flex -z-10 [mask:linear-gradient(180deg,transparent,black_40%,black_40%,transparent)] \">\r\n        <div className=\" w-1/2 h-full flex items-start justify-between\">\r\n          {Array.from({ length: 5 }).map((_, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"w-px h-5 bg-primary first:bg-transparent\"\r\n            ></div>\r\n          ))}\r\n        </div>\r\n        <div className=\"w-1/2 h-full border-x border-border/70 border-dashed flex items-start justify-between\">\r\n          {Array.from({ length: 5 }).map((_, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"w-px h-5 bg-primary first:bg-transparent\"\r\n            ></div>\r\n          ))}\r\n        </div>\r\n        <div className=\" w-1/2 h-full flex items-start justify-between\">\r\n          {Array.from({ length: 5 }).map((_, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"w-px h-5 bg-primary first:bg-transparent\"\r\n            ></div>\r\n          ))}\r\n        </div>\r\n        <div className=\"w-1/2 h-full border-x border-border/70 border-dashed flex items-start justify-between\">\r\n          {Array.from({ length: 5 }).map((_, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"w-px h-5 bg-primary first:bg-transparent \"\r\n            ></div>\r\n          ))}\r\n        </div>\r\n        <div className=\" w-1/2 h-full flex items-start justify-between\">\r\n          {Array.from({ length: 5 }).map((_, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"w-px h-5 bg-primary first:bg-transparent\"\r\n            ></div>\r\n          ))}\r\n        </div>\r\n        <div className=\"w-1/2 h-full border-x border-border/70 border-dashed flex items-start justify-between\">\r\n          {Array.from({ length: 5 }).map((_, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"w-px h-5 bg-primary first:bg-transparent\"\r\n            ></div>\r\n          ))}\r\n        </div>\r\n        <div className=\" w-1/2 h-full flex items-start justify-between\">\r\n          {Array.from({ length: 5 }).map((_, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"w-px h-5 bg-primary first:bg-transparent\"\r\n            ></div>\r\n          ))}\r\n        </div>\r\n        <div className=\"w-1/2 h-full border-x border-border/70 border-dashed flex items-start justify-between\">\r\n          {Array.from({ length: 5 }).map((_, index) => (\r\n            <div\r\n              key={index}\r\n              className=\"w-px h-5 bg-primary first:bg-transparent\"\r\n            ></div>\r\n          ))}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Days of the week */}\r\n      <div className=\"absolute top-4 left-0 right-0 flex justify-between max-w-md mx-auto px-8 text-sm text-gray-500\">\r\n        <span>Tue</span>\r\n        <span>Wed</span>\r\n        <span>Thu</span>\r\n        <span>Fri</span>\r\n        <span>Sat</span>\r\n      </div>\r\n\r\n      <motion.div\r\n        className=\"absolute top-10 w-[2px] h-[calc(100%-80px)] bg-gradient-to-b from-black dark:from-accent to-transparent z-10\"\r\n        style={{\r\n          x: smoothX,\r\n          translateX: '-50%',\r\n        }}\r\n        initial={{ opacity: 0 }}\r\n        animate={{\r\n          opacity: 1,\r\n        }}\r\n        transition={{\r\n          opacity: { duration: 0.2 },\r\n          default: { duration: 0 }, // Makes position update instant\r\n        }}\r\n      />\r\n      <motion.div\r\n        className=\"absolute top-14 bg-black dark:bg-accent h-6 z-20 flex items-center justify-center text-xs p-2 rounded-md shadow-[0px_2.2px_6.6px_0px_rgba(18,43,105,0.04),0px_1.1px_2.2px_0px_rgba(18,43,105,0.08),0px_0px_0px_1.1px_rgba(18,43,105,0.08),0px_1.1px_0px_0px_rgba(255,255,255,0.20)_inset,0px_4.4px_6.6px_0px_rgba(255,255,255,0.01)_inset]\"\r\n        style={{\r\n          x: smoothX,\r\n          translateX: '-50%',\r\n        }}\r\n        initial={{ opacity: 0 }}\r\n        animate={{\r\n          opacity: 1,\r\n        }}\r\n        transition={{\r\n          opacity: { duration: 0.2 },\r\n          default: { duration: 0 }, // Makes position update instant\r\n        }}\r\n      >\r\n        <span className=\"text-white\">12:00 AM</span>\r\n      </motion.div>\r\n\r\n      <div\r\n        className=\"w-full absolute grid gap-10 top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/3\"\r\n        ref={containerRef}\r\n      >\r\n        <AnimatePresence>\r\n          {translateXValues.map((translateX, index) => (\r\n            <motion.div\r\n              key={index}\r\n              initial={{\r\n                opacity: 0,\r\n                x:\r\n                  index % 2 === 0\r\n                    ? -50\r\n                    : containerRef.current?.getBoundingClientRect().width || 0,\r\n              }}\r\n              animate={\r\n                isInView\r\n                  ? {\r\n                      opacity: 1,\r\n                      x: translateX,\r\n                    }\r\n                  : {\r\n                      opacity: 0,\r\n                      x:\r\n                        index % 2 === 0\r\n                          ? -50\r\n                          : containerRef.current?.getBoundingClientRect()\r\n                              .width || 0,\r\n                    }\r\n              }\r\n              exit={{ opacity: 0, x: index % 2 === 0 ? 50 : -50 }}\r\n              transition={{\r\n                type: 'spring',\r\n                stiffness: 220,\r\n                damping: 18,\r\n                duration: 0.3,\r\n                delay: startAnimationDelay + index * 0.2,\r\n              }}\r\n              className={`flex items-center h-8 justify-center gap-2 rounded-lg w-[250px] p-2 shadow-[0px_9px_5px_0px_#00000005,0px_4px_4px_0px_#00000009,0px_1px_2px_0px_#00000010] ${boxConfigs[index].className}`}\r\n            >\r\n              <p className=\"font-medium text-sm\">{boxConfigs[index].title}</p>\r\n            </motion.div>\r\n          ))}\r\n        </AnimatePresence>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAOA;;;AATA;;;AAgBA,MAAM,aAA0B;IAC9B;QACE,OAAO;QACP,WAAW;IACb;IACA;QACE,OAAO;QACP,WAAW;IACb;IACA;QACE,OAAO;QACP,WACE;IACJ;CACD;AAOM,SAAS,qBAAqB,EACnC,OAAO,KAAK,EACZ,sBAAsB,CAAC,EACG;;IAC1B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAyB;IACnD,MAAM,WAAW,CAAA,GAAA,wMAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QAAE;IAAK;IAChD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAErE,MAAM,SAAS,CAAA,GAAA,6MAAA,CAAA,iBAAc,AAAD,EAAE;IAC9B,MAAM,UAAU,CAAA,GAAA,oMAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;QAChC,SAAS;QACT,WAAW;IACb;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,IAAI,aAAa,OAAO,EAAE;gBACxB,MAAM,OAAO,aAAa,OAAO,CAAC,qBAAqB;gBACvD,OAAO,GAAG,CAAC,KAAK,KAAK,GAAG;YAC1B;QACF;yCAAG;QAAC;KAAO;IAEX,MAAM,kBAAkB,CAAC;QACvB,IAAI,aAAa,OAAO,EAAE;YACxB,MAAM,OAAO,aAAa,OAAO,CAAC,qBAAqB;YACvD,MAAM,YAAY,EAAE,OAAO,GAAG,KAAK,IAAI,GAAG;YAC1C,OAAO,GAAG,CAAC;QACb;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,aAAa,OAAO,EAAE;YACxB,MAAM,OAAO,aAAa,OAAO,CAAC,qBAAqB;YACvD,OAAO,GAAG,CAAC,KAAK,KAAK,GAAG;QAC1B;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR,MAAM;8DAAc;oBAClB,IAAI,aAAa,OAAO,EAAE;wBACxB,MAAM,iBACJ,aAAa,OAAO,CAAC,qBAAqB,GAAG,KAAK;wBACpD,MAAM,YAAY;wBAClB,MAAM,gBAAgB;wBACtB,MAAM,kBAAkB,YAAY;wBACpC,MAAM,iBAAiB,iBAAiB;wBACxC,MAAM,MAAM,iBAAiB,CAAC,gBAAgB,CAAC;wBAE/C,MAAM,sBAAsB,MAAM,IAAI,CACpC;4BAAE,QAAQ;wBAAc;8FACxB,CAAC,GAAG;gCACF,OAAO,AAAC,CAAC,YAAY,GAAG,IAAI,QAAS;4BACvC;;wBAEF,oBAAoB;oBACtB;gBACF;;YAEA;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAElC,8CAA8C;YAC9C;kDAAO;oBACL,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACF;yCAAG,EAAE;IAEL,qBACE,6LAAC;QACC,WAAU;QACV,aAAa;QACb,cAAc;;0BAEd,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC;gCAEC,WAAU;+BADL;;;;;;;;;;kCAKX,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC;gCAEC,WAAU;+BADL;;;;;;;;;;kCAKX,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC;gCAEC,WAAU;+BADL;;;;;;;;;;kCAKX,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC;gCAEC,WAAU;+BADL;;;;;;;;;;kCAKX,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC;gCAEC,WAAU;+BADL;;;;;;;;;;kCAKX,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC;gCAEC,WAAU;+BADL;;;;;;;;;;kCAKX,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC;gCAEC,WAAU;+BADL;;;;;;;;;;kCAKX,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC;gCAEC,WAAU;+BADL;;;;;;;;;;;;;;;;0BAQb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAK;;;;;;kCACN,6LAAC;kCAAK;;;;;;kCACN,6LAAC;kCAAK;;;;;;kCACN,6LAAC;kCAAK;;;;;;kCACN,6LAAC;kCAAK;;;;;;;;;;;;0BAGR,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,GAAG;oBACH,YAAY;gBACd;gBACA,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBACP,SAAS;gBACX;gBACA,YAAY;oBACV,SAAS;wBAAE,UAAU;oBAAI;oBACzB,SAAS;wBAAE,UAAU;oBAAE;gBACzB;;;;;;0BAEF,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,GAAG;oBACH,YAAY;gBACd;gBACA,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBACP,SAAS;gBACX;gBACA,YAAY;oBACV,SAAS;wBAAE,UAAU;oBAAI;oBACzB,SAAS;wBAAE,UAAU;oBAAE;gBACzB;0BAEA,cAAA,6LAAC;oBAAK,WAAU;8BAAa;;;;;;;;;;;0BAG/B,6LAAC;gBACC,WAAU;gBACV,KAAK;0BAEL,cAAA,6LAAC,oNAAA,CAAA,kBAAe;8BACb,iBAAiB,GAAG,CAAC,CAAC,YAAY,sBACjC,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCACP,SAAS;gCACT,GACE,QAAQ,MAAM,IACV,CAAC,KACD,aAAa,OAAO,EAAE,wBAAwB,SAAS;4BAC/D;4BACA,SACE,WACI;gCACE,SAAS;gCACT,GAAG;4BACL,IACA;gCACE,SAAS;gCACT,GACE,QAAQ,MAAM,IACV,CAAC,KACD,aAAa,OAAO,EAAE,wBACnB,SAAS;4BACpB;4BAEN,MAAM;gCAAE,SAAS;gCAAG,GAAG,QAAQ,MAAM,IAAI,KAAK,CAAC;4BAAG;4BAClD,YAAY;gCACV,MAAM;gCACN,WAAW;gCACX,SAAS;gCACT,UAAU;gCACV,OAAO,sBAAsB,QAAQ;4BACvC;4BACA,WAAW,CAAC,2JAA2J,EAAE,UAAU,CAAC,MAAM,CAAC,SAAS,EAAE;sCAEtM,cAAA,6LAAC;gCAAE,WAAU;0CAAuB,UAAU,CAAC,MAAM,CAAC,KAAK;;;;;;2BAjCtD;;;;;;;;;;;;;;;;;;;;;AAwCnB;GArOgB;;QAKG,wMAAA,CAAA,YAAS;QAGX,6MAAA,CAAA,iBAAc;QACb,oMAAA,CAAA,YAAS;;;KATX", "debugId": null}}, {"offset": {"line": 7380, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>/orbiting-circle.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { cn } from '@/lib/utils';\r\nimport { cubicBezier, HTMLMotionProps, motion, useInView } from 'motion/react';\r\nimport React, { useEffect, useRef, useState } from 'react';\r\n\r\nexport interface OrbitingCirclesProps extends HTMLMotionProps<'div'> {\r\n  className?: string;\r\n  children?: React.ReactNode;\r\n  reverse?: boolean;\r\n  duration?: number;\r\n  delay?: number;\r\n  radius?: number;\r\n  path?: boolean;\r\n  iconSize?: number;\r\n  speed?: number;\r\n  index?: number;\r\n  startAnimationDelay?: number;\r\n  once?: boolean;\r\n}\r\n\r\nexport function OrbitingCircles({\r\n  className,\r\n  children,\r\n  reverse,\r\n  duration = 20,\r\n  radius = 160,\r\n  path = true,\r\n  iconSize = 30,\r\n  speed = 1,\r\n  index = 0,\r\n  startAnimationDelay = 0,\r\n  once = false,\r\n  ...props\r\n}: OrbitingCirclesProps) {\r\n  const calculatedDuration = duration / speed;\r\n\r\n  const ref = useRef(null);\r\n  const isInView = useInView(ref, { once });\r\n  const [shouldAnimate, setShouldAnimate] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (isInView) {\r\n      setShouldAnimate(true);\r\n    } else {\r\n      setShouldAnimate(false);\r\n    }\r\n  }, [isInView]);\r\n  return (\r\n    <>\r\n      {path && (\r\n        <motion.div ref={ref}>\r\n          {shouldAnimate && (\r\n            <motion.div\r\n              initial={{ scale: 0, opacity: 0 }}\r\n              animate={{ scale: 1, opacity: 1 }}\r\n              transition={{\r\n                duration: 0.8,\r\n                ease: [0.23, 1, 0.32, 1],\r\n                delay: index * 0.2 + startAnimationDelay,\r\n                type: 'spring',\r\n                stiffness: 120,\r\n                damping: 18,\r\n                mass: 1,\r\n              }}\r\n              className=\"pointer-events-none absolute inset-0\"\r\n              style={{\r\n                width: radius * 2,\r\n                height: radius * 2,\r\n                left: `calc(50% - ${radius}px)`,\r\n                top: `calc(50% - ${radius}px)`,\r\n              }}\r\n            >\r\n              <div\r\n                className={cn(\r\n                  'size-full rounded-full',\r\n                  'border border-[0,0,0,0.07] dark:border-[rgba(249,250,251,0.07)]',\r\n                  'bg-gradient-to-b from-[rgba(0,0,0,0.05)] from-0% via-[rgba(249,250,251,0.00)] via-54.76%',\r\n                  'dark:bg-gradient-to-b dark:from-[rgba(249,250,251,0.03)] dark:from-0% dark:via-[rgba(249,250,251,0.00)] dark:via-54.76%',\r\n                  className,\r\n                )}\r\n              />\r\n            </motion.div>\r\n          )}\r\n        </motion.div>\r\n      )}\r\n      {shouldAnimate &&\r\n        React.Children.map(children, (child, index) => {\r\n          const angle = (360 / React.Children.count(children)) * index;\r\n          return (\r\n            <div\r\n              style={\r\n                {\r\n                  '--duration': calculatedDuration,\r\n                  '--radius': radius * 0.98,\r\n                  '--angle': angle,\r\n                  '--icon-size': `${iconSize}px`,\r\n                } as React.CSSProperties\r\n              }\r\n              className={cn(\r\n                'absolute flex size-[var(--icon-size)] z-20 p-1 transform-gpu animate-orbit items-center justify-center rounded-full',\r\n                { '[animation-direction:reverse]': reverse },\r\n              )}\r\n            >\r\n              <motion.div\r\n                key={`orbit-child-${index}`}\r\n                initial={{ scale: 0, opacity: 0 }}\r\n                animate={{ scale: 1, opacity: 1 }}\r\n                transition={{\r\n                  duration: 0.5,\r\n                  delay: 0.6 + index * 0.2 + startAnimationDelay,\r\n                  ease: cubicBezier(0, 0, 0.58, 1),\r\n                  type: 'spring',\r\n                  stiffness: 120,\r\n                  damping: 18,\r\n                  mass: 1,\r\n                }}\r\n                {...props}\r\n              >\r\n                {child}\r\n              </motion.div>\r\n            </div>\r\n          );\r\n        })}\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;;;AAJA;;;;AAqBO,SAAS,gBAAgB,EAC9B,SAAS,EACT,QAAQ,EACR,OAAO,EACP,WAAW,EAAE,EACb,SAAS,GAAG,EACZ,OAAO,IAAI,EACX,WAAW,EAAE,EACb,QAAQ,CAAC,EACT,QAAQ,CAAC,EACT,sBAAsB,CAAC,EACvB,OAAO,KAAK,EACZ,GAAG,OACkB;;IACrB,MAAM,qBAAqB,WAAW;IAEtC,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,wMAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE;IAAK;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,UAAU;gBACZ,iBAAiB;YACnB,OAAO;gBACL,iBAAiB;YACnB;QACF;oCAAG;QAAC;KAAS;IACb,qBACE;;YACG,sBACC,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,KAAK;0BACd,+BACC,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,OAAO;wBAAG,SAAS;oBAAE;oBAChC,SAAS;wBAAE,OAAO;wBAAG,SAAS;oBAAE;oBAChC,YAAY;wBACV,UAAU;wBACV,MAAM;4BAAC;4BAAM;4BAAG;4BAAM;yBAAE;wBACxB,OAAO,QAAQ,MAAM;wBACrB,MAAM;wBACN,WAAW;wBACX,SAAS;wBACT,MAAM;oBACR;oBACA,WAAU;oBACV,OAAO;wBACL,OAAO,SAAS;wBAChB,QAAQ,SAAS;wBACjB,MAAM,CAAC,WAAW,EAAE,OAAO,GAAG,CAAC;wBAC/B,KAAK,CAAC,WAAW,EAAE,OAAO,GAAG,CAAC;oBAChC;8BAEA,cAAA,6LAAC;wBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0BACA,mEACA,4FACA,2HACA;;;;;;;;;;;;;;;;YAOX,iBACC,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,OAAO;gBACnC,MAAM,QAAQ,AAAC,MAAM,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,YAAa;gBACvD,qBACE,6LAAC;oBACC,OACE;wBACE,cAAc;wBACd,YAAY,SAAS;wBACrB,WAAW;wBACX,eAAe,GAAG,SAAS,EAAE,CAAC;oBAChC;oBAEF,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uHACA;wBAAE,iCAAiC;oBAAQ;8BAG7C,cAAA,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBAChC,SAAS;4BAAE,OAAO;4BAAG,SAAS;wBAAE;wBAChC,YAAY;4BACV,UAAU;4BACV,OAAO,MAAM,QAAQ,MAAM;4BAC3B,MAAM,CAAA,GAAA,sMAAA,CAAA,cAAW,AAAD,EAAE,GAAG,GAAG,MAAM;4BAC9B,MAAM;4BACN,WAAW;4BACX,SAAS;4BACT,MAAM;wBACR;wBACC,GAAG,KAAK;kCAER;uBAdI,CAAC,YAAY,EAAE,OAAO;;;;;;;;;;YAkBnC;;;AAGR;GAzGgB;;QAiBG,wMAAA,CAAA,YAAS;;;KAjBZ", "debugId": null}}, {"offset": {"line": 7528, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>"], "sourcesContent": ["import { Icons } from '@/components/home/<USER>';\r\nimport { OrbitingCircles } from '@/components/home/<USER>/orbiting-circle';\r\n\r\nexport function SecondBentoAnimation() {\r\n  return (\r\n    <div className=\"relative flex h-full w-full items-center justify-center overflow-hidden\">\r\n      <div className=\"pointer-events-none absolute bottom-0 left-0 h-20 w-full bg-gradient-to-t from-background to-transparent z-20\"></div>\r\n      <div className=\"pointer-events-none absolute top-0 left-0 h-20 w-full bg-gradient-to-b from-background to-transparent z-20\"></div>\r\n\r\n      <div className=\"absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2 flex items-center justify-center gap-2 size-16 bg-secondary p-2 rounded-full z-30 md:bottom-0 md:top-auto\">\r\n        <Icons.logo className=\"fill-white size-10\" />\r\n      </div>\r\n      <div className=\"relative flex h-full w-full items-center justify-center overflow-hidden\">\r\n        <div className=\"relative flex h-full w-full items-center justify-center translate-y-0 md:translate-y-32\">\r\n          <OrbitingCircles\r\n            index={0}\r\n            iconSize={60}\r\n            radius={100}\r\n            reverse\r\n            speed={1}\r\n          >\r\n            <Icons.boat />\r\n            <Icons.supabase />\r\n            <Icons.figma />\r\n          </OrbitingCircles>\r\n\r\n          <OrbitingCircles index={1} iconSize={60} speed={0.5}>\r\n            <Icons.workos />\r\n            <Icons.runwayml />\r\n            <Icons.gemini />\r\n          </OrbitingCircles>\r\n\r\n          <OrbitingCircles\r\n            index={2}\r\n            iconSize={60}\r\n            radius={230}\r\n            reverse\r\n            speed={0.5}\r\n          >\r\n            <Icons.vercel />\r\n            <Icons.replit />\r\n            <Icons.posthog />\r\n          </OrbitingCircles>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEO,SAAS;IACd,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;;;;;0BAEf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,sIAAA,CAAA,QAAK,CAAC,IAAI;oBAAC,WAAU;;;;;;;;;;;0BAExB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yJAAA,CAAA,kBAAe;4BACd,OAAO;4BACP,UAAU;4BACV,QAAQ;4BACR,OAAO;4BACP,OAAO;;8CAEP,6LAAC,sIAAA,CAAA,QAAK,CAAC,IAAI;;;;;8CACX,6LAAC,sIAAA,CAAA,QAAK,CAAC,QAAQ;;;;;8CACf,6LAAC,sIAAA,CAAA,QAAK,CAAC,KAAK;;;;;;;;;;;sCAGd,6LAAC,yJAAA,CAAA,kBAAe;4BAAC,OAAO;4BAAG,UAAU;4BAAI,OAAO;;8CAC9C,6LAAC,sIAAA,CAAA,QAAK,CAAC,MAAM;;;;;8CACb,6LAAC,sIAAA,CAAA,QAAK,CAAC,QAAQ;;;;;8CACf,6LAAC,sIAAA,CAAA,QAAK,CAAC,MAAM;;;;;;;;;;;sCAGf,6LAAC,yJAAA,CAAA,kBAAe;4BACd,OAAO;4BACP,UAAU;4BACV,QAAQ;4BACR,OAAO;4BACP,OAAO;;8CAEP,6LAAC,sIAAA,CAAA,QAAK,CAAC,MAAM;;;;;8CACb,6LAAC,sIAAA,CAAA,QAAK,CAAC,MAAM;;;;;8CACb,6LAAC,sIAAA,CAAA,QAAK,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1B;KA5CgB", "debugId": null}}, {"offset": {"line": 7686, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>"], "sourcesContent": ["'use client';\r\n\r\nimport { colorWithOpacity, getRGBA } from '@/lib/utils';\r\nimport NumberFlow from '@number-flow/react';\r\nimport { motion, useInView } from 'motion/react';\r\nimport { CSSProperties, useCallback, useEffect, useRef, useState } from 'react';\r\n\r\ninterface LineChartProps {\r\n  data: number[];\r\n  height?: number;\r\n  width?: number;\r\n  color: string;\r\n  shouldAnimate: boolean;\r\n  startAnimationDelay?: number;\r\n}\r\n\r\nexport function LineChart({\r\n  data,\r\n  height = 200,\r\n  width = 600,\r\n  color,\r\n  shouldAnimate,\r\n  startAnimationDelay,\r\n}: LineChartProps) {\r\n  const svgRef = useRef<SVGSVGElement>(null);\r\n\r\n  // Create smooth curve points using bezier curves\r\n  const createSmoothPath = (points: { x: number; y: number }[]) => {\r\n    if (points.length < 2) return '';\r\n\r\n    const path = points.reduce((acc, point, i, arr) => {\r\n      if (i === 0) {\r\n        // Move to the first point\r\n        return `M ${point.x} ${point.y}`;\r\n      }\r\n\r\n      // Calculate control points for smooth curve\r\n      const prev = arr[i - 1];\r\n      const next = arr[i + 1];\r\n      const smoothing = 0.2;\r\n\r\n      // If it's the last point, we don't need a curve\r\n      if (i === arr.length - 1) {\r\n        return `${acc} L ${point.x} ${point.y}`;\r\n      }\r\n\r\n      // Calculate control points\r\n      const cp1x = prev.x + (point.x - prev.x) * smoothing;\r\n      const cp1y = prev.y + (point.y - prev.y) * smoothing;\r\n      const cp2x = point.x - (next.x - prev.x) * smoothing;\r\n      const cp2y = point.y - (next.y - prev.y) * smoothing;\r\n\r\n      return `${acc} C ${cp1x},${cp1y} ${cp2x},${cp2y} ${point.x},${point.y}`;\r\n    }, '');\r\n\r\n    return path;\r\n  };\r\n\r\n  // Convert data points to coordinates\r\n  const coordinates = data.map((value, index) => ({\r\n    x: (index / (data.length - 1)) * width,\r\n    y: height - (value / Math.max(...data)) * height * 0.8, // Add some padding at top\r\n  }));\r\n\r\n  // Create smooth path\r\n  const smoothPath = createSmoothPath(coordinates);\r\n\r\n  // Find the middle point coordinates\r\n  const middleIndex = Math.floor(data.length / 2);\r\n  const middlePoint = coordinates[middleIndex];\r\n\r\n  const [showPulse, setShowPulse] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (!shouldAnimate) {\r\n      setShowPulse(false);\r\n      return;\r\n    }\r\n\r\n    const timeoutId = setTimeout(\r\n      () => {\r\n        setShowPulse(true);\r\n      },\r\n      (startAnimationDelay || 0) * 1000,\r\n    );\r\n\r\n    return () => clearTimeout(timeoutId);\r\n  }, [shouldAnimate, startAnimationDelay]);\r\n\r\n  const [computedColor, setComputedColor] = useState(color);\r\n\r\n  useEffect(() => {\r\n    setComputedColor(getRGBA(color));\r\n  }, [color]);\r\n\r\n  const getColorWithOpacity = useCallback(\r\n    (opacity: number) => colorWithOpacity(computedColor, opacity),\r\n    [computedColor],\r\n  );\r\n\r\n  return (\r\n    <svg\r\n      ref={svgRef}\r\n      width={width}\r\n      height={height}\r\n      viewBox={`0 0 ${width} ${height}`}\r\n      fill=\"none\"\r\n      xmlns=\"http://www.w3.org/2000/svg\"\r\n    >\r\n      {/* Gradient Definition */}\r\n      <defs>\r\n        <linearGradient id=\"lineGradient\" x1=\"0\" y1=\"0\" x2=\"0\" y2=\"1\">\r\n          <stop offset=\"0%\" stopColor={getColorWithOpacity(0.3)} />\r\n          <stop offset=\"100%\" stopColor={getColorWithOpacity(0)} />\r\n        </linearGradient>\r\n      </defs>\r\n\r\n      {/* Animated Area Fill */}\r\n      <motion.path\r\n        initial={{ opacity: 0, scale: 0.95 }}\r\n        animate={{\r\n          opacity: shouldAnimate ? 1 : 0,\r\n          scale: shouldAnimate ? 1 : 0.95,\r\n        }}\r\n        transition={{\r\n          duration: 0.8,\r\n          ease: 'easeOut',\r\n          delay: startAnimationDelay,\r\n        }}\r\n        d={`${smoothPath} L ${width},${height} L 0,${height} Z`}\r\n        fill=\"url(#lineGradient)\"\r\n      />\r\n\r\n      {/* Animated Line */}\r\n      <motion.path\r\n        initial={{ pathLength: 0 }}\r\n        animate={{ pathLength: shouldAnimate ? 1 : 0 }}\r\n        transition={{\r\n          duration: 1.5,\r\n          ease: 'easeInOut',\r\n          delay: startAnimationDelay,\r\n        }}\r\n        d={smoothPath}\r\n        stroke={color}\r\n        strokeWidth=\"2\"\r\n        fill=\"none\"\r\n        strokeLinecap=\"round\"\r\n      />\r\n\r\n      {/* Center dot with scale animation */}\r\n      <motion.circle\r\n        cx={middlePoint.x}\r\n        cy={middlePoint.y}\r\n        r=\"4\"\r\n        fill={color}\r\n        initial={{ scale: 0, opacity: 0 }}\r\n        animate={{\r\n          scale: shouldAnimate ? 1 : 0,\r\n          opacity: shouldAnimate ? 1 : 0,\r\n        }}\r\n        transition={{\r\n          delay: startAnimationDelay ? startAnimationDelay + 0.3 : 0.3,\r\n          duration: 0.4,\r\n          ease: 'backOut',\r\n        }}\r\n      />\r\n\r\n      {/* Multiple pulsing waves */}\r\n      {showPulse && (\r\n        <>\r\n          {[0, 1, 2].map((index) => (\r\n            <motion.circle\r\n              key={index}\r\n              cx={middlePoint.x}\r\n              cy={middlePoint.y}\r\n              r=\"10\"\r\n              stroke={color}\r\n              strokeWidth=\"2\"\r\n              fill=\"none\"\r\n              initial={{ scale: 0.5, opacity: 0 }}\r\n              animate={{\r\n                scale: [0.5, 2],\r\n                opacity: [0.8, 0],\r\n              }}\r\n              transition={{\r\n                duration: 2,\r\n                repeat: Infinity,\r\n                delay: index * 0.67,\r\n                ease: 'easeOut',\r\n                times: [0, 1],\r\n                repeatDelay: 0,\r\n              }}\r\n            />\r\n          ))}\r\n        </>\r\n      )}\r\n    </svg>\r\n  );\r\n}\r\n\r\nexport function NumberFlowCounter({\r\n  toolTipValues,\r\n  shouldAnimate,\r\n  startAnimationDelay,\r\n}: {\r\n  toolTipValues: number[];\r\n  shouldAnimate: boolean;\r\n  startAnimationDelay?: number;\r\n}) {\r\n  const [currentIndex, setCurrentIndex] = useState(0);\r\n  const currentValue = toolTipValues[currentIndex];\r\n  const [showCounter, setShowCounter] = useState(false);\r\n\r\n  useEffect(() => {\r\n    if (!shouldAnimate) {\r\n      setShowCounter(false);\r\n      return;\r\n    }\r\n\r\n    const timeoutId = setTimeout(\r\n      () => {\r\n        setShowCounter(true);\r\n      },\r\n      (startAnimationDelay || 0) * 1000,\r\n    );\r\n\r\n    return () => clearTimeout(timeoutId);\r\n  }, [shouldAnimate, startAnimationDelay]);\r\n\r\n  useEffect(() => {\r\n    if (!showCounter) return;\r\n\r\n    const intervalId = setInterval(() => {\r\n      setCurrentIndex((prev) => (prev + 1) % toolTipValues.length);\r\n    }, 2000);\r\n\r\n    return () => {\r\n      if (intervalId) clearInterval(intervalId);\r\n    };\r\n  }, [showCounter, toolTipValues.length]);\r\n\r\n  return (\r\n    <div\r\n      className={`${\r\n        showCounter ? 'opacity-100' : 'opacity-0'\r\n      } transition-opacity duration-300 ease-in-out absolute top-32 left-[42%] -translate-x-1/2 text-sm bg-[#1A1B25] border border-white/[0.07] text-white px-4 py-1 rounded-full h-8 flex items-center justify-center font-mono shadow-[0px_1.1px_0px_0px_rgba(255,255,255,0.20)_inset,0px_4.4px_6.6px_0px_rgba(255,255,255,0.01)_inset,0px_2.2px_6.6px_0px_rgba(18,43,105,0.04),0px_1.1px_2.2px_0px_rgba(18,43,105,0.08),0px_0px_0px_1.1px_rgba(18,43,105,0.08)]`}\r\n    >\r\n      <NumberFlow\r\n        value={currentValue}\r\n        className=\"font-mono\"\r\n        transformTiming={{\r\n          duration: 700,\r\n          easing: 'ease-out',\r\n        }}\r\n        format={{\r\n          useGrouping: true,\r\n          minimumIntegerDigits: 1,\r\n        }}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport function ThirdBentoAnimation({\r\n  data,\r\n  toolTipValues,\r\n  color = 'var(--secondary)',\r\n  startAnimationDelay = 0,\r\n  once = false,\r\n}: {\r\n  data: number[];\r\n  toolTipValues: number[];\r\n  color?: string;\r\n  startAnimationDelay?: number;\r\n  once?: boolean;\r\n}) {\r\n  const ref = useRef(null);\r\n  const isInView = useInView(ref, { once });\r\n  const [shouldAnimate, setShouldAnimate] = useState(false);\r\n  const [computedColor, setComputedColor] = useState(color);\r\n\r\n  useEffect(() => {\r\n    setComputedColor(getRGBA(color));\r\n  }, [color]);\r\n\r\n  useEffect(() => {\r\n    if (isInView) {\r\n      setShouldAnimate(true);\r\n    } else {\r\n      setShouldAnimate(false);\r\n    }\r\n  }, [isInView]);\r\n\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      className=\"relative flex size-full items-center justify-center h-[300px] pt-10 overflow-hidden\"\r\n      style={\r\n        {\r\n          '--color': computedColor,\r\n        } as CSSProperties\r\n      }\r\n    >\r\n      <motion.div\r\n        initial={{ opacity: 0 }}\r\n        animate={{ opacity: shouldAnimate ? 1 : 0 }}\r\n        transition={{\r\n          duration: 0.5,\r\n          delay: startAnimationDelay ? startAnimationDelay + 0.3 : 0.3,\r\n          ease: 'easeOut',\r\n        }}\r\n        className=\"absolute top-[60%] left-1/2 -translate-x-1/2 w-[2px] h-32 bg-gradient-to-b from-[var(--color)] to-[var(--color-transparent)]\"\r\n      ></motion.div>\r\n      <NumberFlowCounter\r\n        toolTipValues={toolTipValues}\r\n        shouldAnimate={shouldAnimate}\r\n        startAnimationDelay={startAnimationDelay}\r\n      />\r\n      <LineChart\r\n        data={data}\r\n        height={200}\r\n        width={600}\r\n        color={computedColor}\r\n        shouldAnimate={shouldAnimate}\r\n        startAnimationDelay={startAnimationDelay}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;;;AALA;;;;;AAgBO,SAAS,UAAU,EACxB,IAAI,EACJ,SAAS,GAAG,EACZ,QAAQ,GAAG,EACX,KAAK,EACL,aAAa,EACb,mBAAmB,EACJ;;IACf,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;IAErC,iDAAiD;IACjD,MAAM,mBAAmB,CAAC;QACxB,IAAI,OAAO,MAAM,GAAG,GAAG,OAAO;QAE9B,MAAM,OAAO,OAAO,MAAM,CAAC,CAAC,KAAK,OAAO,GAAG;YACzC,IAAI,MAAM,GAAG;gBACX,0BAA0B;gBAC1B,OAAO,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE;YAClC;YAEA,4CAA4C;YAC5C,MAAM,OAAO,GAAG,CAAC,IAAI,EAAE;YACvB,MAAM,OAAO,GAAG,CAAC,IAAI,EAAE;YACvB,MAAM,YAAY;YAElB,gDAAgD;YAChD,IAAI,MAAM,IAAI,MAAM,GAAG,GAAG;gBACxB,OAAO,GAAG,IAAI,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE;YACzC;YAEA,2BAA2B;YAC3B,MAAM,OAAO,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI;YAC3C,MAAM,OAAO,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,IAAI;YAC3C,MAAM,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI;YAC3C,MAAM,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI;YAE3C,OAAO,GAAG,IAAI,GAAG,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE;QACzE,GAAG;QAEH,OAAO;IACT;IAEA,qCAAqC;IACrC,MAAM,cAAc,KAAK,GAAG,CAAC,CAAC,OAAO,QAAU,CAAC;YAC9C,GAAG,AAAC,QAAQ,CAAC,KAAK,MAAM,GAAG,CAAC,IAAK;YACjC,GAAG,SAAS,AAAC,QAAQ,KAAK,GAAG,IAAI,QAAS,SAAS;QACrD,CAAC;IAED,qBAAqB;IACrB,MAAM,aAAa,iBAAiB;IAEpC,oCAAoC;IACpC,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,GAAG;IAC7C,MAAM,cAAc,WAAW,CAAC,YAAY;IAE5C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,eAAe;gBAClB,aAAa;gBACb;YACF;YAEA,MAAM,YAAY;iDAChB;oBACE,aAAa;gBACf;gDACA,CAAC,uBAAuB,CAAC,IAAI;YAG/B;uCAAO,IAAM,aAAa;;QAC5B;8BAAG;QAAC;QAAe;KAAoB;IAEvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,iBAAiB,CAAA,GAAA,sHAAA,CAAA,UAAO,AAAD,EAAE;QAC3B;8BAAG;QAAC;KAAM;IAEV,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDACpC,CAAC,UAAoB,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,eAAe;qDACrD;QAAC;KAAc;IAGjB,qBACE,6LAAC;QACC,KAAK;QACL,OAAO;QACP,QAAQ;QACR,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,QAAQ;QACjC,MAAK;QACL,OAAM;;0BAGN,6LAAC;0BACC,cAAA,6LAAC;oBAAe,IAAG;oBAAe,IAAG;oBAAI,IAAG;oBAAI,IAAG;oBAAI,IAAG;;sCACxD,6LAAC;4BAAK,QAAO;4BAAK,WAAW,oBAAoB;;;;;;sCACjD,6LAAC;4BAAK,QAAO;4BAAO,WAAW,oBAAoB;;;;;;;;;;;;;;;;;0BAKvD,6LAAC,qNAAA,CAAA,SAAM,CAAC,IAAI;gBACV,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAK;gBACnC,SAAS;oBACP,SAAS,gBAAgB,IAAI;oBAC7B,OAAO,gBAAgB,IAAI;gBAC7B;gBACA,YAAY;oBACV,UAAU;oBACV,MAAM;oBACN,OAAO;gBACT;gBACA,GAAG,GAAG,WAAW,GAAG,EAAE,MAAM,CAAC,EAAE,OAAO,KAAK,EAAE,OAAO,EAAE,CAAC;gBACvD,MAAK;;;;;;0BAIP,6LAAC,qNAAA,CAAA,SAAM,CAAC,IAAI;gBACV,SAAS;oBAAE,YAAY;gBAAE;gBACzB,SAAS;oBAAE,YAAY,gBAAgB,IAAI;gBAAE;gBAC7C,YAAY;oBACV,UAAU;oBACV,MAAM;oBACN,OAAO;gBACT;gBACA,GAAG;gBACH,QAAQ;gBACR,aAAY;gBACZ,MAAK;gBACL,eAAc;;;;;;0BAIhB,6LAAC,qNAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,IAAI,YAAY,CAAC;gBACjB,IAAI,YAAY,CAAC;gBACjB,GAAE;gBACF,MAAM;gBACN,SAAS;oBAAE,OAAO;oBAAG,SAAS;gBAAE;gBAChC,SAAS;oBACP,OAAO,gBAAgB,IAAI;oBAC3B,SAAS,gBAAgB,IAAI;gBAC/B;gBACA,YAAY;oBACV,OAAO,sBAAsB,sBAAsB,MAAM;oBACzD,UAAU;oBACV,MAAM;gBACR;;;;;;YAID,2BACC;0BACG;oBAAC;oBAAG;oBAAG;iBAAE,CAAC,GAAG,CAAC,CAAC,sBACd,6LAAC,qNAAA,CAAA,SAAM,CAAC,MAAM;wBAEZ,IAAI,YAAY,CAAC;wBACjB,IAAI,YAAY,CAAC;wBACjB,GAAE;wBACF,QAAQ;wBACR,aAAY;wBACZ,MAAK;wBACL,SAAS;4BAAE,OAAO;4BAAK,SAAS;wBAAE;wBAClC,SAAS;4BACP,OAAO;gCAAC;gCAAK;6BAAE;4BACf,SAAS;gCAAC;gCAAK;6BAAE;wBACnB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,OAAO,QAAQ;4BACf,MAAM;4BACN,OAAO;gCAAC;gCAAG;6BAAE;4BACb,aAAa;wBACf;uBAnBK;;;;;;;;;;;;AA0BnB;GAtLgB;KAAA;AAwLT,SAAS,kBAAkB,EAChC,aAAa,EACb,aAAa,EACb,mBAAmB,EAKpB;;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,eAAe,aAAa,CAAC,aAAa;IAChD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,CAAC,eAAe;gBAClB,eAAe;gBACf;YACF;YAEA,MAAM,YAAY;yDAChB;oBACE,eAAe;gBACjB;wDACA,CAAC,uBAAuB,CAAC,IAAI;YAG/B;+CAAO,IAAM,aAAa;;QAC5B;sCAAG;QAAC;QAAe;KAAoB;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,CAAC,aAAa;YAElB,MAAM,aAAa;0DAAY;oBAC7B;kEAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,cAAc,MAAM;;gBAC7D;yDAAG;YAEH;+CAAO;oBACL,IAAI,YAAY,cAAc;gBAChC;;QACF;sCAAG;QAAC;QAAa,cAAc,MAAM;KAAC;IAEtC,qBACE,6LAAC;QACC,WAAW,GACT,cAAc,gBAAgB,YAC/B,2bAA2b,CAAC;kBAE7b,cAAA,6LAAC,yNAAA,CAAA,UAAU;YACT,OAAO;YACP,WAAU;YACV,iBAAiB;gBACf,UAAU;gBACV,QAAQ;YACV;YACA,QAAQ;gBACN,aAAa;gBACb,sBAAsB;YACxB;;;;;;;;;;;AAIR;IA7DgB;MAAA;AA+DT,SAAS,oBAAoB,EAClC,IAAI,EACJ,aAAa,EACb,QAAQ,kBAAkB,EAC1B,sBAAsB,CAAC,EACvB,OAAO,KAAK,EAOb;;IACC,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACnB,MAAM,WAAW,CAAA,GAAA,wMAAA,CAAA,YAAS,AAAD,EAAE,KAAK;QAAE;IAAK;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,iBAAiB,CAAA,GAAA,sHAAA,CAAA,UAAO,AAAD,EAAE;QAC3B;wCAAG;QAAC;KAAM;IAEV,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,UAAU;gBACZ,iBAAiB;YACnB,OAAO;gBACL,iBAAiB;YACnB;QACF;wCAAG;QAAC;KAAS;IAEb,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;QACV,OACE;YACE,WAAW;QACb;;0BAGF,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS,gBAAgB,IAAI;gBAAE;gBAC1C,YAAY;oBACV,UAAU;oBACV,OAAO,sBAAsB,sBAAsB,MAAM;oBACzD,MAAM;gBACR;gBACA,WAAU;;;;;;0BAEZ,6LAAC;gBACC,eAAe;gBACf,eAAe;gBACf,qBAAqB;;;;;;0BAEvB,6LAAC;gBACC,MAAM;gBACN,QAAQ;gBACR,OAAO;gBACP,OAAO;gBACP,eAAe;gBACf,qBAAqB;;;;;;;;;;;;AAI7B;IAjEgB;;QAcG,wMAAA,CAAA,YAAS;;;MAdZ", "debugId": null}}, {"offset": {"line": 8105, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>/flickering-grid.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { cn, colorWithOpacity, getRGBA } from '@/lib/utils';\r\nimport React, {\r\n  useCallback,\r\n  useEffect,\r\n  useMemo,\r\n  useRef,\r\n  useState,\r\n} from 'react';\r\n\r\ninterface FlickeringGridProps extends React.HTMLAttributes<HTMLDivElement> {\r\n  squareSize?: number;\r\n  gridGap?: number;\r\n  flickerChance?: number;\r\n  color?: string; // Can be any valid CSS color including hex, rgb, rgba, hsl, var(--color)\r\n  width?: number;\r\n  height?: number;\r\n  className?: string;\r\n  maxOpacity?: number;\r\n  text?: string;\r\n  textColor?: string;\r\n  fontSize?: number;\r\n  fontWeight?: number | string;\r\n}\r\n\r\nexport const FlickeringGrid: React.FC<FlickeringGridProps> = ({\r\n  squareSize = 3,\r\n  gridGap = 3,\r\n  flickerChance = 0.2,\r\n  color = '#B4B4B4',\r\n  width,\r\n  height,\r\n  className,\r\n  maxOpacity = 0.15,\r\n  text = '',\r\n  fontSize = 140,\r\n  fontWeight = 600,\r\n  ...props\r\n}) => {\r\n  const canvasRef = useRef<HTMLCanvasElement>(null);\r\n  const containerRef = useRef<HTMLDivElement>(null);\r\n  const animationRef = useRef<number>(0);\r\n  const lastRenderTimeRef = useRef<number>(0);\r\n  const lastResizeTimeRef = useRef<number>(0);\r\n  const gridParamsRef = useRef<any>(null);\r\n  const [isInView, setIsInView] = useState(false);\r\n  const [canvasSize, setCanvasSize] = useState({ width: 0, height: 0 });\r\n\r\n  // Throttle rendering to improve performance - adjust ms as needed\r\n  const FRAME_THROTTLE = 50; // Only render every ~50ms (20fps instead of 60fps)\r\n  const RESIZE_THROTTLE = 200; // Throttle resize events\r\n\r\n  // Convert any CSS color to rgba for optimal canvas performance\r\n  const memoizedColor = useMemo(() => {\r\n    return getRGBA(color);\r\n  }, [color]);\r\n\r\n  const drawGrid = useCallback(\r\n    (\r\n      ctx: CanvasRenderingContext2D,\r\n      width: number,\r\n      height: number,\r\n      cols: number,\r\n      rows: number,\r\n      squares: Float32Array,\r\n      dpr: number,\r\n    ) => {\r\n      ctx.clearRect(0, 0, width, height);\r\n\r\n      // Create a separate canvas for the text mask if needed\r\n      let maskCanvas: HTMLCanvasElement | null = null;\r\n      let maskCtx: CanvasRenderingContext2D | null = null;\r\n\r\n      if (text) {\r\n        maskCanvas = document.createElement('canvas');\r\n        maskCanvas.width = width;\r\n        maskCanvas.height = height;\r\n        maskCtx = maskCanvas.getContext('2d', { willReadFrequently: true });\r\n\r\n        if (maskCtx) {\r\n          // Draw text on mask canvas\r\n          maskCtx.save();\r\n          maskCtx.scale(dpr, dpr);\r\n          maskCtx.fillStyle = 'white';\r\n          maskCtx.font = `${fontWeight} ${fontSize}px \"Geist\", -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, sans-serif`;\r\n          maskCtx.textAlign = 'center';\r\n          maskCtx.textBaseline = 'middle';\r\n          maskCtx.fillText(text, width / (2 * dpr), height / (2 * dpr));\r\n          maskCtx.restore();\r\n        }\r\n      }\r\n\r\n      // Batch squares by opacity for better performance\r\n      const opacityMap = new Map<number, { x: number; y: number }[]>();\r\n\r\n      for (let i = 0; i < cols; i++) {\r\n        for (let j = 0; j < rows; j++) {\r\n          const x = i * (squareSize + gridGap) * dpr;\r\n          const y = j * (squareSize + gridGap) * dpr;\r\n          const squareWidth = squareSize * dpr;\r\n          const squareHeight = squareSize * dpr;\r\n\r\n          let hasText = false;\r\n\r\n          if (maskCtx && maskCanvas) {\r\n            const maskData = maskCtx.getImageData(\r\n              x,\r\n              y,\r\n              squareWidth,\r\n              squareHeight,\r\n            ).data;\r\n\r\n            hasText = maskData.some(\r\n              (value, index) => index % 4 === 0 && value > 0,\r\n            );\r\n          }\r\n\r\n          const opacity = squares[i * rows + j];\r\n          const finalOpacity = hasText\r\n            ? Math.min(1, opacity * 3 + 0.4)\r\n            : opacity;\r\n\r\n          // Round opacity to 2 decimal places for batching\r\n          const roundedOpacity = Math.round(finalOpacity * 100) / 100;\r\n\r\n          if (!opacityMap.has(roundedOpacity)) {\r\n            opacityMap.set(roundedOpacity, []);\r\n          }\r\n\r\n          opacityMap.get(roundedOpacity)?.push({ x, y });\r\n        }\r\n      }\r\n\r\n      // Draw squares by opacity batch\r\n      for (const [opacity, squares] of opacityMap.entries()) {\r\n        ctx.fillStyle = colorWithOpacity(memoizedColor, opacity);\r\n\r\n        for (const { x, y } of squares) {\r\n          ctx.fillRect(x, y, squareSize * dpr, squareSize * dpr);\r\n        }\r\n      }\r\n    },\r\n    [memoizedColor, squareSize, gridGap, text, fontSize, fontWeight],\r\n  );\r\n\r\n  const setupCanvas = useCallback(\r\n    (canvas: HTMLCanvasElement, width: number, height: number) => {\r\n      const dpr = window.devicePixelRatio || 1;\r\n      canvas.width = width * dpr;\r\n      canvas.height = height * dpr;\r\n      canvas.style.width = `${width}px`;\r\n      canvas.style.height = `${height}px`;\r\n      const cols = Math.ceil(width / (squareSize + gridGap));\r\n      const rows = Math.ceil(height / (squareSize + gridGap));\r\n\r\n      // Check if we should preserve the existing grid state\r\n      if (\r\n        gridParamsRef.current &&\r\n        gridParamsRef.current.cols === cols &&\r\n        gridParamsRef.current.rows === rows\r\n      ) {\r\n        // Use existing squares array to maintain state\r\n        return {\r\n          cols,\r\n          rows,\r\n          squares: gridParamsRef.current.squares,\r\n          dpr,\r\n        };\r\n      }\r\n\r\n      // Create new squares array only if needed\r\n      const squares = new Float32Array(cols * rows);\r\n      for (let i = 0; i < squares.length; i++) {\r\n        squares[i] = Math.random() * maxOpacity;\r\n      }\r\n\r\n      return { cols, rows, squares, dpr };\r\n    },\r\n    [squareSize, gridGap, maxOpacity],\r\n  );\r\n\r\n  const updateSquares = useCallback(\r\n    (squares: Float32Array, deltaTime: number) => {\r\n      // Only update if flickerChance is greater than 0\r\n      if (flickerChance <= 0) return;\r\n\r\n      for (let i = 0; i < squares.length; i++) {\r\n        if (Math.random() < flickerChance * deltaTime) {\r\n          squares[i] = Math.random() * maxOpacity;\r\n        }\r\n      }\r\n    },\r\n    [flickerChance, maxOpacity],\r\n  );\r\n\r\n  useEffect(() => {\r\n    const canvas = canvasRef.current;\r\n    const container = containerRef.current;\r\n    if (!canvas || !container) return;\r\n\r\n    const ctx = canvas.getContext('2d', { alpha: true });\r\n    if (!ctx) return;\r\n\r\n    const updateCanvasSize = () => {\r\n      const now = performance.now();\r\n      if (now - lastResizeTimeRef.current < RESIZE_THROTTLE) return;\r\n\r\n      lastResizeTimeRef.current = now;\r\n      const newWidth = width || container.clientWidth;\r\n      const newHeight = height || container.clientHeight;\r\n\r\n      // Only update if size changed to prevent unnecessary redraws\r\n      if (canvasSize.width !== newWidth || canvasSize.height !== newHeight) {\r\n        setCanvasSize({ width: newWidth, height: newHeight });\r\n\r\n        // Don't recreate grid if sizes are similar (within 10px)\r\n        const shouldPreserveGrid =\r\n          gridParamsRef.current &&\r\n          Math.abs(\r\n            gridParamsRef.current.cols * (squareSize + gridGap) - newWidth,\r\n          ) < 10 &&\r\n          Math.abs(\r\n            gridParamsRef.current.rows * (squareSize + gridGap) - newHeight,\r\n          ) < 10;\r\n\r\n        if (!shouldPreserveGrid) {\r\n          gridParamsRef.current = setupCanvas(canvas, newWidth, newHeight);\r\n        } else {\r\n          // Just update canvas dimensions without recreating grid\r\n          const dpr = window.devicePixelRatio || 1;\r\n          canvas.width = newWidth * dpr;\r\n          canvas.height = newHeight * dpr;\r\n          canvas.style.width = `${newWidth}px`;\r\n          canvas.style.height = `${newHeight}px`;\r\n        }\r\n      }\r\n    };\r\n\r\n    // Initialize canvas size and grid params if needed\r\n    if (!gridParamsRef.current) {\r\n      updateCanvasSize();\r\n    }\r\n\r\n    let lastTime = 0;\r\n    const animate = (time: number) => {\r\n      if (!isInView) {\r\n        animationRef.current = requestAnimationFrame(animate);\r\n        return;\r\n      }\r\n\r\n      // Throttle to improve performance\r\n      if (time - lastRenderTimeRef.current < FRAME_THROTTLE) {\r\n        animationRef.current = requestAnimationFrame(animate);\r\n        return;\r\n      }\r\n\r\n      // Safety check\r\n      if (!gridParamsRef.current || !gridParamsRef.current.squares) {\r\n        updateCanvasSize();\r\n        animationRef.current = requestAnimationFrame(animate);\r\n        return;\r\n      }\r\n\r\n      lastRenderTimeRef.current = time;\r\n      const deltaTime = (time - lastTime) / 1000;\r\n      lastTime = time;\r\n\r\n      updateSquares(gridParamsRef.current.squares, deltaTime);\r\n      drawGrid(\r\n        ctx,\r\n        canvas.width,\r\n        canvas.height,\r\n        gridParamsRef.current.cols,\r\n        gridParamsRef.current.rows,\r\n        gridParamsRef.current.squares,\r\n        gridParamsRef.current.dpr,\r\n      );\r\n      animationRef.current = requestAnimationFrame(animate);\r\n    };\r\n\r\n    // Use a gentle resize observer that doesn't completely redraw everything\r\n    const resizeObserver = new ResizeObserver(() => {\r\n      const now = performance.now();\r\n      if (now - lastResizeTimeRef.current < RESIZE_THROTTLE) return;\r\n\r\n      const newWidth = width || container.clientWidth;\r\n      const newHeight = height || container.clientHeight;\r\n\r\n      // Only update if dimensions actually changed significantly (at least 5px difference)\r\n      if (\r\n        Math.abs(canvasSize.width - newWidth) > 5 ||\r\n        Math.abs(canvasSize.height - newHeight) > 5\r\n      ) {\r\n        updateCanvasSize();\r\n      }\r\n    });\r\n\r\n    resizeObserver.observe(container);\r\n\r\n    const intersectionObserver = new IntersectionObserver(\r\n      ([entry]) => {\r\n        setIsInView(entry.isIntersecting);\r\n      },\r\n      { threshold: 0.1, rootMargin: '50px' }, // Only activate when 10% visible with margin\r\n    );\r\n\r\n    intersectionObserver.observe(canvas);\r\n\r\n    // Start animation if in view\r\n    if (isInView) {\r\n      animationRef.current = requestAnimationFrame(animate);\r\n    }\r\n\r\n    return () => {\r\n      cancelAnimationFrame(animationRef.current);\r\n      resizeObserver.disconnect();\r\n      intersectionObserver.disconnect();\r\n    };\r\n  }, [\r\n    setupCanvas,\r\n    updateSquares,\r\n    drawGrid,\r\n    width,\r\n    height,\r\n    isInView,\r\n    squareSize,\r\n    gridGap,\r\n  ]);\r\n\r\n  return (\r\n    <div\r\n      ref={containerRef}\r\n      className={cn(`h-full w-full ${className}`)}\r\n      {...props}\r\n    >\r\n      <canvas\r\n        ref={canvasRef}\r\n        className=\"pointer-events-none\"\r\n        style={{\r\n          width: canvasSize.width,\r\n          height: canvasSize.height,\r\n        }}\r\n      />\r\n    </div>\r\n  );\r\n};\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AA0BO,MAAM,iBAAgD,CAAC,EAC5D,aAAa,CAAC,EACd,UAAU,CAAC,EACX,gBAAgB,GAAG,EACnB,QAAQ,SAAS,EACjB,KAAK,EACL,MAAM,EACN,SAAS,EACT,aAAa,IAAI,EACjB,OAAO,EAAE,EACT,WAAW,GAAG,EACd,aAAa,GAAG,EAChB,GAAG,OACJ;;IACC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU;IACpC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU;IACzC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU;IACzC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAO;IAClC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,OAAO;QAAG,QAAQ;IAAE;IAEnE,kEAAkE;IAClE,MAAM,iBAAiB,IAAI,mDAAmD;IAC9E,MAAM,kBAAkB,KAAK,yBAAyB;IAEtD,+DAA+D;IAC/D,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iDAAE;YAC5B,OAAO,CAAA,GAAA,sHAAA,CAAA,UAAO,AAAD,EAAE;QACjB;gDAAG;QAAC;KAAM;IAEV,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDACzB,CACE,KACA,OACA,QACA,MACA,MACA,SACA;YAEA,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO;YAE3B,uDAAuD;YACvD,IAAI,aAAuC;YAC3C,IAAI,UAA2C;YAE/C,IAAI,MAAM;gBACR,aAAa,SAAS,aAAa,CAAC;gBACpC,WAAW,KAAK,GAAG;gBACnB,WAAW,MAAM,GAAG;gBACpB,UAAU,WAAW,UAAU,CAAC,MAAM;oBAAE,oBAAoB;gBAAK;gBAEjE,IAAI,SAAS;oBACX,2BAA2B;oBAC3B,QAAQ,IAAI;oBACZ,QAAQ,KAAK,CAAC,KAAK;oBACnB,QAAQ,SAAS,GAAG;oBACpB,QAAQ,IAAI,GAAG,GAAG,WAAW,CAAC,EAAE,SAAS,6EAA6E,CAAC;oBACvH,QAAQ,SAAS,GAAG;oBACpB,QAAQ,YAAY,GAAG;oBACvB,QAAQ,QAAQ,CAAC,MAAM,QAAQ,CAAC,IAAI,GAAG,GAAG,SAAS,CAAC,IAAI,GAAG;oBAC3D,QAAQ,OAAO;gBACjB;YACF;YAEA,kDAAkD;YAClD,MAAM,aAAa,IAAI;YAEvB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;gBAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,IAAK;oBAC7B,MAAM,IAAI,IAAI,CAAC,aAAa,OAAO,IAAI;oBACvC,MAAM,IAAI,IAAI,CAAC,aAAa,OAAO,IAAI;oBACvC,MAAM,cAAc,aAAa;oBACjC,MAAM,eAAe,aAAa;oBAElC,IAAI,UAAU;oBAEd,IAAI,WAAW,YAAY;wBACzB,MAAM,WAAW,QAAQ,YAAY,CACnC,GACA,GACA,aACA,cACA,IAAI;wBAEN,UAAU,SAAS,IAAI;oEACrB,CAAC,OAAO,QAAU,QAAQ,MAAM,KAAK,QAAQ;;oBAEjD;oBAEA,MAAM,UAAU,OAAO,CAAC,IAAI,OAAO,EAAE;oBACrC,MAAM,eAAe,UACjB,KAAK,GAAG,CAAC,GAAG,UAAU,IAAI,OAC1B;oBAEJ,iDAAiD;oBACjD,MAAM,iBAAiB,KAAK,KAAK,CAAC,eAAe,OAAO;oBAExD,IAAI,CAAC,WAAW,GAAG,CAAC,iBAAiB;wBACnC,WAAW,GAAG,CAAC,gBAAgB,EAAE;oBACnC;oBAEA,WAAW,GAAG,CAAC,iBAAiB,KAAK;wBAAE;wBAAG;oBAAE;gBAC9C;YACF;YAEA,gCAAgC;YAChC,KAAK,MAAM,CAAC,SAAS,QAAQ,IAAI,WAAW,OAAO,GAAI;gBACrD,IAAI,SAAS,GAAG,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,eAAe;gBAEhD,KAAK,MAAM,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,QAAS;oBAC9B,IAAI,QAAQ,CAAC,GAAG,GAAG,aAAa,KAAK,aAAa;gBACpD;YACF;QACF;+CACA;QAAC;QAAe;QAAY;QAAS;QAAM;QAAU;KAAW;IAGlE,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAC5B,CAAC,QAA2B,OAAe;YACzC,MAAM,MAAM,OAAO,gBAAgB,IAAI;YACvC,OAAO,KAAK,GAAG,QAAQ;YACvB,OAAO,MAAM,GAAG,SAAS;YACzB,OAAO,KAAK,CAAC,KAAK,GAAG,GAAG,MAAM,EAAE,CAAC;YACjC,OAAO,KAAK,CAAC,MAAM,GAAG,GAAG,OAAO,EAAE,CAAC;YACnC,MAAM,OAAO,KAAK,IAAI,CAAC,QAAQ,CAAC,aAAa,OAAO;YACpD,MAAM,OAAO,KAAK,IAAI,CAAC,SAAS,CAAC,aAAa,OAAO;YAErD,sDAAsD;YACtD,IACE,cAAc,OAAO,IACrB,cAAc,OAAO,CAAC,IAAI,KAAK,QAC/B,cAAc,OAAO,CAAC,IAAI,KAAK,MAC/B;gBACA,+CAA+C;gBAC/C,OAAO;oBACL;oBACA;oBACA,SAAS,cAAc,OAAO,CAAC,OAAO;oBACtC;gBACF;YACF;YAEA,0CAA0C;YAC1C,MAAM,UAAU,IAAI,aAAa,OAAO;YACxC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;gBACvC,OAAO,CAAC,EAAE,GAAG,KAAK,MAAM,KAAK;YAC/B;YAEA,OAAO;gBAAE;gBAAM;gBAAM;gBAAS;YAAI;QACpC;kDACA;QAAC;QAAY;QAAS;KAAW;IAGnC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;qDAC9B,CAAC,SAAuB;YACtB,iDAAiD;YACjD,IAAI,iBAAiB,GAAG;YAExB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;gBACvC,IAAI,KAAK,MAAM,KAAK,gBAAgB,WAAW;oBAC7C,OAAO,CAAC,EAAE,GAAG,KAAK,MAAM,KAAK;gBAC/B;YACF;QACF;oDACA;QAAC;QAAe;KAAW;IAG7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,SAAS,UAAU,OAAO;YAChC,MAAM,YAAY,aAAa,OAAO;YACtC,IAAI,CAAC,UAAU,CAAC,WAAW;YAE3B,MAAM,MAAM,OAAO,UAAU,CAAC,MAAM;gBAAE,OAAO;YAAK;YAClD,IAAI,CAAC,KAAK;YAEV,MAAM;6DAAmB;oBACvB,MAAM,MAAM,YAAY,GAAG;oBAC3B,IAAI,MAAM,kBAAkB,OAAO,GAAG,iBAAiB;oBAEvD,kBAAkB,OAAO,GAAG;oBAC5B,MAAM,WAAW,SAAS,UAAU,WAAW;oBAC/C,MAAM,YAAY,UAAU,UAAU,YAAY;oBAElD,6DAA6D;oBAC7D,IAAI,WAAW,KAAK,KAAK,YAAY,WAAW,MAAM,KAAK,WAAW;wBACpE,cAAc;4BAAE,OAAO;4BAAU,QAAQ;wBAAU;wBAEnD,yDAAyD;wBACzD,MAAM,qBACJ,cAAc,OAAO,IACrB,KAAK,GAAG,CACN,cAAc,OAAO,CAAC,IAAI,GAAG,CAAC,aAAa,OAAO,IAAI,YACpD,MACJ,KAAK,GAAG,CACN,cAAc,OAAO,CAAC,IAAI,GAAG,CAAC,aAAa,OAAO,IAAI,aACpD;wBAEN,IAAI,CAAC,oBAAoB;4BACvB,cAAc,OAAO,GAAG,YAAY,QAAQ,UAAU;wBACxD,OAAO;4BACL,wDAAwD;4BACxD,MAAM,MAAM,OAAO,gBAAgB,IAAI;4BACvC,OAAO,KAAK,GAAG,WAAW;4BAC1B,OAAO,MAAM,GAAG,YAAY;4BAC5B,OAAO,KAAK,CAAC,KAAK,GAAG,GAAG,SAAS,EAAE,CAAC;4BACpC,OAAO,KAAK,CAAC,MAAM,GAAG,GAAG,UAAU,EAAE,CAAC;wBACxC;oBACF;gBACF;;YAEA,mDAAmD;YACnD,IAAI,CAAC,cAAc,OAAO,EAAE;gBAC1B;YACF;YAEA,IAAI,WAAW;YACf,MAAM;oDAAU,CAAC;oBACf,IAAI,CAAC,UAAU;wBACb,aAAa,OAAO,GAAG,sBAAsB;wBAC7C;oBACF;oBAEA,kCAAkC;oBAClC,IAAI,OAAO,kBAAkB,OAAO,GAAG,gBAAgB;wBACrD,aAAa,OAAO,GAAG,sBAAsB;wBAC7C;oBACF;oBAEA,eAAe;oBACf,IAAI,CAAC,cAAc,OAAO,IAAI,CAAC,cAAc,OAAO,CAAC,OAAO,EAAE;wBAC5D;wBACA,aAAa,OAAO,GAAG,sBAAsB;wBAC7C;oBACF;oBAEA,kBAAkB,OAAO,GAAG;oBAC5B,MAAM,YAAY,CAAC,OAAO,QAAQ,IAAI;oBACtC,WAAW;oBAEX,cAAc,cAAc,OAAO,CAAC,OAAO,EAAE;oBAC7C,SACE,KACA,OAAO,KAAK,EACZ,OAAO,MAAM,EACb,cAAc,OAAO,CAAC,IAAI,EAC1B,cAAc,OAAO,CAAC,IAAI,EAC1B,cAAc,OAAO,CAAC,OAAO,EAC7B,cAAc,OAAO,CAAC,GAAG;oBAE3B,aAAa,OAAO,GAAG,sBAAsB;gBAC/C;;YAEA,yEAAyE;YACzE,MAAM,iBAAiB,IAAI;4CAAe;oBACxC,MAAM,MAAM,YAAY,GAAG;oBAC3B,IAAI,MAAM,kBAAkB,OAAO,GAAG,iBAAiB;oBAEvD,MAAM,WAAW,SAAS,UAAU,WAAW;oBAC/C,MAAM,YAAY,UAAU,UAAU,YAAY;oBAElD,qFAAqF;oBACrF,IACE,KAAK,GAAG,CAAC,WAAW,KAAK,GAAG,YAAY,KACxC,KAAK,GAAG,CAAC,WAAW,MAAM,GAAG,aAAa,GAC1C;wBACA;oBACF;gBACF;;YAEA,eAAe,OAAO,CAAC;YAEvB,MAAM,uBAAuB,IAAI;4CAC/B,CAAC,CAAC,MAAM;oBACN,YAAY,MAAM,cAAc;gBAClC;2CACA;gBAAE,WAAW;gBAAK,YAAY;YAAO;YAGvC,qBAAqB,OAAO,CAAC;YAE7B,6BAA6B;YAC7B,IAAI,UAAU;gBACZ,aAAa,OAAO,GAAG,sBAAsB;YAC/C;YAEA;4CAAO;oBACL,qBAAqB,aAAa,OAAO;oBACzC,eAAe,UAAU;oBACzB,qBAAqB,UAAU;gBACjC;;QACF;mCAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,CAAC,cAAc,EAAE,WAAW;QACzC,GAAG,KAAK;kBAET,cAAA,6LAAC;YACC,KAAK;YACL,WAAU;YACV,OAAO;gBACL,OAAO,WAAW,KAAK;gBACvB,QAAQ,WAAW,MAAM;YAC3B;;;;;;;;;;;AAIR;GAhUa;KAAA", "debugId": null}}, {"offset": {"line": 8406, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>/globe.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport createGlobe, { COBEOptions } from 'cobe';\r\nimport { useMotionValue, useSpring } from 'motion/react';\r\nimport { useEffect, useRef, useMemo } from 'react';\r\nimport { useTheme } from 'next-themes';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst MOVEMENT_DAMPING = 1400;\r\n\r\nconst GLOBE_CONFIG: COBEOptions = {\r\n  width: 800,\r\n  height: 800,\r\n  onRender: () => {},\r\n  devicePixelRatio: 2,\r\n  phi: 0,\r\n  theta: 0.3,\r\n  dark: 0,\r\n  diffuse: 0.4,\r\n  mapSamples: 16000,\r\n  mapBrightness: 1.2,\r\n  baseColor: [1, 1, 1],\r\n  markerColor: [251 / 255, 100 / 255, 21 / 255],\r\n  glowColor: [1, 1, 1],\r\n  markers: [\r\n    { location: [14.5995, 120.9842], size: 0.03 },\r\n    { location: [19.076, 72.8777], size: 0.1 },\r\n    { location: [23.8103, 90.4125], size: 0.05 },\r\n    { location: [30.0444, 31.2357], size: 0.07 },\r\n    { location: [39.9042, 116.4074], size: 0.08 },\r\n    { location: [-23.5505, -46.6333], size: 0.1 },\r\n    { location: [19.4326, -99.1332], size: 0.1 },\r\n    { location: [40.7128, -74.006], size: 0.1 },\r\n    { location: [34.6937, 135.5022], size: 0.05 },\r\n    { location: [41.0082, 28.9784], size: 0.06 },\r\n  ],\r\n};\r\n\r\n// Define color configurations for light and dark modes\r\nconst COLORS = {\r\n  light: {\r\n    base: [1, 1, 1] as [number, number, number],\r\n    glow: [1, 1, 1] as [number, number, number],\r\n    marker: [251 / 255, 100 / 255, 21 / 255] as [number, number, number],\r\n  },\r\n  dark: {\r\n    base: [0.4, 0.4, 0.4] as [number, number, number],\r\n    glow: [0.24, 0.24, 0.27] as [number, number, number],\r\n    marker: [251 / 255, 100 / 255, 21 / 255] as [number, number, number],\r\n  },\r\n};\r\n\r\nexport function Globe({\r\n  className,\r\n  config = GLOBE_CONFIG,\r\n}: {\r\n  className?: string;\r\n  config?: COBEOptions;\r\n}) {\r\n  const { theme } = useTheme();\r\n  const isDarkMode = theme === 'dark';\r\n\r\n  const phiRef = useRef(0);\r\n  const widthRef = useRef(0);\r\n  const canvasRef = useRef<HTMLCanvasElement>(null);\r\n  const pointerInteracting = useRef<number | null>(null);\r\n  const pointerInteractionMovement = useRef(0);\r\n\r\n  const r = useMotionValue(0);\r\n  const rs = useSpring(r, {\r\n    mass: 1,\r\n    damping: 30,\r\n    stiffness: 100,\r\n  });\r\n\r\n  const finalConfig = useMemo(\r\n    () => ({\r\n      ...config,\r\n      baseColor: isDarkMode ? COLORS.dark.base : COLORS.light.base,\r\n      glowColor: isDarkMode ? COLORS.dark.glow : COLORS.light.glow,\r\n      markerColor: COLORS.light.marker,\r\n      dark: isDarkMode ? 1 : 0,\r\n      diffuse: isDarkMode ? 0.5 : 0.4,\r\n      mapBrightness: isDarkMode ? 1.4 : 1.2,\r\n    }),\r\n    [config, isDarkMode],\r\n  );\r\n\r\n  const updatePointerInteraction = (value: number | null) => {\r\n    pointerInteracting.current = value;\r\n    if (canvasRef.current) {\r\n      canvasRef.current.style.cursor = value !== null ? 'grabbing' : 'grab';\r\n    }\r\n  };\r\n\r\n  const updateMovement = (clientX: number) => {\r\n    if (pointerInteracting.current !== null) {\r\n      const delta = clientX - pointerInteracting.current;\r\n      pointerInteractionMovement.current = delta;\r\n      r.set(r.get() + delta / MOVEMENT_DAMPING);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    const onResize = () => {\r\n      if (canvasRef.current) {\r\n        widthRef.current = canvasRef.current.offsetWidth;\r\n      }\r\n    };\r\n\r\n    window.addEventListener('resize', onResize);\r\n    onResize();\r\n\r\n    const globe = createGlobe(canvasRef.current!, {\r\n      ...finalConfig,\r\n      width: widthRef.current * 2,\r\n      height: widthRef.current * 2,\r\n      onRender: (state) => {\r\n        if (!pointerInteracting.current) phiRef.current += 0.005;\r\n        state.phi = phiRef.current + rs.get();\r\n        state.width = widthRef.current * 2;\r\n        state.height = widthRef.current * 2;\r\n      },\r\n    });\r\n\r\n    setTimeout(() => (canvasRef.current!.style.opacity = '1'), 0);\r\n    return () => {\r\n      globe.destroy();\r\n      window.removeEventListener('resize', onResize);\r\n    };\r\n  }, [rs, finalConfig]);\r\n\r\n  return (\r\n    <div\r\n      className={cn(\r\n        'absolute inset-0 mx-auto aspect-[1/1] w-full max-w-[600px]',\r\n        className,\r\n      )}\r\n    >\r\n      <canvas\r\n        className={cn(\r\n          'size-full opacity-0 transition-opacity duration-500 [contain:layout_paint_size]',\r\n        )}\r\n        ref={canvasRef}\r\n        onPointerDown={(e) => {\r\n          pointerInteracting.current = e.clientX;\r\n          updatePointerInteraction(e.clientX);\r\n        }}\r\n        onPointerUp={() => updatePointerInteraction(null)}\r\n        onPointerOut={() => updatePointerInteraction(null)}\r\n        onMouseMove={(e) => updateMovement(e.clientX)}\r\n        onTouchMove={(e) =>\r\n          e.touches[0] && updateMovement(e.touches[0].clientX)\r\n        }\r\n      />\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAEA;;;AAPA;;;;;;AASA,MAAM,mBAAmB;AAEzB,MAAM,eAA4B;IAChC,OAAO;IACP,QAAQ;IACR,UAAU,KAAO;IACjB,kBAAkB;IAClB,KAAK;IACL,OAAO;IACP,MAAM;IACN,SAAS;IACT,YAAY;IACZ,eAAe;IACf,WAAW;QAAC;QAAG;QAAG;KAAE;IACpB,aAAa;QAAC,MAAM;QAAK,MAAM;QAAK,KAAK;KAAI;IAC7C,WAAW;QAAC;QAAG;QAAG;KAAE;IACpB,SAAS;QACP;YAAE,UAAU;gBAAC;gBAAS;aAAS;YAAE,MAAM;QAAK;QAC5C;YAAE,UAAU;gBAAC;gBAAQ;aAAQ;YAAE,MAAM;QAAI;QACzC;YAAE,UAAU;gBAAC;gBAAS;aAAQ;YAAE,MAAM;QAAK;QAC3C;YAAE,UAAU;gBAAC;gBAAS;aAAQ;YAAE,MAAM;QAAK;QAC3C;YAAE,UAAU;gBAAC;gBAAS;aAAS;YAAE,MAAM;QAAK;QAC5C;YAAE,UAAU;gBAAC,CAAC;gBAAS,CAAC;aAAQ;YAAE,MAAM;QAAI;QAC5C;YAAE,UAAU;gBAAC;gBAAS,CAAC;aAAQ;YAAE,MAAM;QAAI;QAC3C;YAAE,UAAU;gBAAC;gBAAS,CAAC;aAAO;YAAE,MAAM;QAAI;QAC1C;YAAE,UAAU;gBAAC;gBAAS;aAAS;YAAE,MAAM;QAAK;QAC5C;YAAE,UAAU;gBAAC;gBAAS;aAAQ;YAAE,MAAM;QAAK;KAC5C;AACH;AAEA,uDAAuD;AACvD,MAAM,SAAS;IACb,OAAO;QACL,MAAM;YAAC;YAAG;YAAG;SAAE;QACf,MAAM;YAAC;YAAG;YAAG;SAAE;QACf,QAAQ;YAAC,MAAM;YAAK,MAAM;YAAK,KAAK;SAAI;IAC1C;IACA,MAAM;QACJ,MAAM;YAAC;YAAK;YAAK;SAAI;QACrB,MAAM;YAAC;YAAM;YAAM;SAAK;QACxB,QAAQ;YAAC,MAAM;YAAK,MAAM;YAAK,KAAK;SAAI;IAC1C;AACF;AAEO,SAAS,MAAM,EACpB,SAAS,EACT,SAAS,YAAY,EAItB;;IACC,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,aAAa,UAAU;IAE7B,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACtB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;IACjD,MAAM,6BAA6B,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE1C,MAAM,IAAI,CAAA,GAAA,6MAAA,CAAA,iBAAc,AAAD,EAAE;IACzB,MAAM,KAAK,CAAA,GAAA,oMAAA,CAAA,YAAS,AAAD,EAAE,GAAG;QACtB,MAAM;QACN,SAAS;QACT,WAAW;IACb;IAEA,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sCACxB,IAAM,CAAC;gBACL,GAAG,MAAM;gBACT,WAAW,aAAa,OAAO,IAAI,CAAC,IAAI,GAAG,OAAO,KAAK,CAAC,IAAI;gBAC5D,WAAW,aAAa,OAAO,IAAI,CAAC,IAAI,GAAG,OAAO,KAAK,CAAC,IAAI;gBAC5D,aAAa,OAAO,KAAK,CAAC,MAAM;gBAChC,MAAM,aAAa,IAAI;gBACvB,SAAS,aAAa,MAAM;gBAC5B,eAAe,aAAa,MAAM;YACpC,CAAC;qCACD;QAAC;QAAQ;KAAW;IAGtB,MAAM,2BAA2B,CAAC;QAChC,mBAAmB,OAAO,GAAG;QAC7B,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,UAAU,OAAO,aAAa;QACjE;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,mBAAmB,OAAO,KAAK,MAAM;YACvC,MAAM,QAAQ,UAAU,mBAAmB,OAAO;YAClD,2BAA2B,OAAO,GAAG;YACrC,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,QAAQ;QAC1B;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,MAAM;4CAAW;oBACf,IAAI,UAAU,OAAO,EAAE;wBACrB,SAAS,OAAO,GAAG,UAAU,OAAO,CAAC,WAAW;oBAClD;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;YAEA,MAAM,QAAQ,CAAA,GAAA,+IAAA,CAAA,UAAW,AAAD,EAAE,UAAU,OAAO,EAAG;gBAC5C,GAAG,WAAW;gBACd,OAAO,SAAS,OAAO,GAAG;gBAC1B,QAAQ,SAAS,OAAO,GAAG;gBAC3B,QAAQ;6CAAE,CAAC;wBACT,IAAI,CAAC,mBAAmB,OAAO,EAAE,OAAO,OAAO,IAAI;wBACnD,MAAM,GAAG,GAAG,OAAO,OAAO,GAAG,GAAG,GAAG;wBACnC,MAAM,KAAK,GAAG,SAAS,OAAO,GAAG;wBACjC,MAAM,MAAM,GAAG,SAAS,OAAO,GAAG;oBACpC;;YACF;YAEA;mCAAW,IAAO,UAAU,OAAO,CAAE,KAAK,CAAC,OAAO,GAAG;kCAAM;YAC3D;mCAAO;oBACL,MAAM,OAAO;oBACb,OAAO,mBAAmB,CAAC,UAAU;gBACvC;;QACF;0BAAG;QAAC;QAAI;KAAY;IAEpB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8DACA;kBAGF,cAAA,6LAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;YAEF,KAAK;YACL,eAAe,CAAC;gBACd,mBAAmB,OAAO,GAAG,EAAE,OAAO;gBACtC,yBAAyB,EAAE,OAAO;YACpC;YACA,aAAa,IAAM,yBAAyB;YAC5C,cAAc,IAAM,yBAAyB;YAC7C,aAAa,CAAC,IAAM,eAAe,EAAE,OAAO;YAC5C,aAAa,CAAC,IACZ,EAAE,OAAO,CAAC,EAAE,IAAI,eAAe,EAAE,OAAO,CAAC,EAAE,CAAC,OAAO;;;;;;;;;;;AAK7D;GAzGgB;;QAOI,mJAAA,CAAA,WAAQ;QAShB,6MAAA,CAAA,iBAAc;QACb,oMAAA,CAAA,YAAS;;;KAjBN", "debugId": null}}, {"offset": {"line": 8684, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/lib/config.ts"], "sourcesContent": ["// Environment mode types\r\nexport enum EnvMode {\r\n  LOCAL = 'local',\r\n  STAGING = 'staging',\r\n  PRODUCTION = 'production',\r\n}\r\n\r\n// Subscription tier structure\r\nexport interface SubscriptionTierData {\r\n  priceId: string;\r\n  name: string;\r\n}\r\n\r\n// Subscription tiers structure\r\nexport interface SubscriptionTiers {\r\n  FREE: SubscriptionTierData;\r\n  TIER_2_20: SubscriptionTierData;\r\n  TIER_6_50: SubscriptionTierData;\r\n  TIER_12_100: SubscriptionTierData;\r\n  TIER_25_200: SubscriptionTierData;\r\n  TIER_50_400: SubscriptionTierData;\r\n  TIER_125_800: SubscriptionTierData;\r\n  TIER_200_1000: SubscriptionTierData;\r\n  // Yearly plans with 15% discount\r\n  TIER_2_20_YEARLY: SubscriptionTierData;\r\n  TIER_6_50_YEARLY: SubscriptionTierData;\r\n  TIER_12_100_YEARLY: SubscriptionTierData;\r\n  TIER_25_200_YEARLY: SubscriptionTierData;\r\n  TIER_50_400_YEARLY: SubscriptionTierData;\r\n  TIER_125_800_YEARLY: SubscriptionTierData;\r\n  TIER_200_1000_YEARLY: SubscriptionTierData;\r\n}\r\n\r\n// Configuration object\r\ninterface Config {\r\n  ENV_MODE: EnvMode;\r\n  IS_LOCAL: boolean;\r\n  SUBSCRIPTION_TIERS: SubscriptionTiers;\r\n}\r\n\r\n// Production tier IDs\r\nconst PROD_TIERS: SubscriptionTiers = {\r\n  FREE: {\r\n    priceId: 'price_1RILb4G6l1KZGqIrK4QLrx9i',\r\n    name: 'Free',\r\n  },\r\n  TIER_2_20: {\r\n    priceId: 'price_1RILb4G6l1KZGqIrhomjgDnO',\r\n    name: '2h/$20',\r\n  },\r\n  TIER_6_50: {\r\n    priceId: 'price_1RILb4G6l1KZGqIr5q0sybWn',\r\n    name: '6h/$50',\r\n  },\r\n  TIER_12_100: {\r\n    priceId: 'price_1RILb4G6l1KZGqIr5Y20ZLHm',\r\n    name: '12h/$100',\r\n  },\r\n  TIER_25_200: {\r\n    priceId: 'price_1RILb4G6l1KZGqIrGAD8rNjb',\r\n    name: '25h/$200',\r\n  },\r\n  TIER_50_400: {\r\n    priceId: 'price_1RILb4G6l1KZGqIruNBUMTF1',\r\n    name: '50h/$400',\r\n  },\r\n  TIER_125_800: {\r\n    priceId: 'price_1RILb3G6l1KZGqIrbJA766tN',\r\n    name: '125h/$800',\r\n  },\r\n  TIER_200_1000: {\r\n    priceId: 'price_1RILb3G6l1KZGqIrmauYPOiN',\r\n    name: '200h/$1000',\r\n  },\r\n  // Yearly plans with 15% discount (12x monthly price with 15% off)\r\n  TIER_2_20_YEARLY: {\r\n    priceId: 'price_1ReHB5G6l1KZGqIrD70I1xqM',\r\n    name: '2h/$204/year',\r\n  },\r\n  TIER_6_50_YEARLY: {\r\n    priceId: 'price_1ReHAsG6l1KZGqIrlAog487C',\r\n    name: '6h/$510/year',\r\n  },\r\n  TIER_12_100_YEARLY: {\r\n    priceId: 'price_1ReHAWG6l1KZGqIrBHer2PQc',\r\n    name: '12h/$1020/year',\r\n  },\r\n  TIER_25_200_YEARLY: {\r\n    priceId: 'price_1ReH9uG6l1KZGqIrsvMLHViC',\r\n    name: '25h/$2040/year',\r\n  },\r\n  TIER_50_400_YEARLY: {\r\n    priceId: 'price_1ReH9fG6l1KZGqIrsPtu5KIA',\r\n    name: '50h/$4080/year',\r\n  },\r\n  TIER_125_800_YEARLY: {\r\n    priceId: 'price_1ReH9GG6l1KZGqIrfgqaJyat',\r\n    name: '125h/$8160/year',\r\n  },\r\n  TIER_200_1000_YEARLY: {\r\n    priceId: 'price_1ReH8qG6l1KZGqIrK1akY90q',\r\n    name: '200h/$10200/year',\r\n  },\r\n} as const;\r\n\r\n// Staging tier IDs\r\nconst STAGING_TIERS: SubscriptionTiers = {\r\n  FREE: {\r\n    priceId: 'price_1RIGvuG6l1KZGqIrw14abxeL',\r\n    name: 'Free',\r\n  },\r\n  TIER_2_20: {\r\n    priceId: 'price_1RIGvuG6l1KZGqIrCRu0E4Gi',\r\n    name: '2h/$20',\r\n  },\r\n  TIER_6_50: {\r\n    priceId: 'price_1RIGvuG6l1KZGqIrvjlz5p5V',\r\n    name: '6h/$50',\r\n  },\r\n  TIER_12_100: {\r\n    priceId: 'price_1RIGvuG6l1KZGqIrT6UfgblC',\r\n    name: '12h/$100',\r\n  },\r\n  TIER_25_200: {\r\n    priceId: 'price_1RIGvuG6l1KZGqIrOVLKlOMj',\r\n    name: '25h/$200',\r\n  },\r\n  TIER_50_400: {\r\n    priceId: 'price_1RIKNgG6l1KZGqIrvsat5PW7',\r\n    name: '50h/$400',\r\n  },\r\n  TIER_125_800: {\r\n    priceId: 'price_1RIKNrG6l1KZGqIrjKT0yGvI',\r\n    name: '125h/$800',\r\n  },\r\n  TIER_200_1000: {\r\n    priceId: 'price_1RIKQ2G6l1KZGqIrum9n8SI7',\r\n    name: '200h/$1000',\r\n  },\r\n  // Yearly plans with 15% discount (12x monthly price with 15% off)\r\n  TIER_2_20_YEARLY: {\r\n    priceId: 'price_1ReGogG6l1KZGqIrEyBTmtPk',\r\n    name: '2h/$204/year',\r\n  },\r\n  TIER_6_50_YEARLY: {\r\n    priceId: 'price_1ReGoJG6l1KZGqIr0DJWtoOc',\r\n    name: '6h/$510/year',\r\n  },\r\n  TIER_12_100_YEARLY: {\r\n    priceId: 'price_1ReGnZG6l1KZGqIr0ThLEl5S',\r\n    name: '12h/$1020/year',\r\n  },\r\n  TIER_25_200_YEARLY: {\r\n    priceId: 'price_1ReGmzG6l1KZGqIre31mqoEJ',\r\n    name: '25h/$2040/year',\r\n  },\r\n  TIER_50_400_YEARLY: {\r\n    priceId: 'price_1ReGmgG6l1KZGqIrn5nBc7e5',\r\n    name: '50h/$4080/year',\r\n  },\r\n  TIER_125_800_YEARLY: {\r\n    priceId: 'price_1ReGmMG6l1KZGqIrvE2ycrAX',\r\n    name: '125h/$8160/year',\r\n  },\r\n  TIER_200_1000_YEARLY: {\r\n    priceId: 'price_1ReGlXG6l1KZGqIrlgurP5GU',\r\n    name: '200h/$10200/year',\r\n  },\r\n} as const;\r\n\r\n// Determine the environment mode from environment variables\r\nconst getEnvironmentMode = (): EnvMode => {\r\n  // Get the environment mode from the environment variable, if set\r\n  const envMode = process.env.NEXT_PUBLIC_ENV_MODE?.toLowerCase();\r\n\r\n  // First check if the environment variable is explicitly set\r\n  if (envMode) {\r\n    if (envMode === EnvMode.LOCAL) {\r\n      console.log('Using explicitly set LOCAL environment mode');\r\n      return EnvMode.LOCAL;\r\n    } else if (envMode === EnvMode.STAGING) {\r\n      console.log('Using explicitly set STAGING environment mode');\r\n      return EnvMode.STAGING;\r\n    } else if (envMode === EnvMode.PRODUCTION) {\r\n      console.log('Using explicitly set PRODUCTION environment mode');\r\n      return EnvMode.PRODUCTION;\r\n    }\r\n  }\r\n\r\n  // If no valid environment mode is set, fall back to defaults based on NODE_ENV\r\n  if (process.env.NODE_ENV === 'development') {\r\n    console.log('Defaulting to LOCAL environment mode in development');\r\n    return EnvMode.LOCAL;\r\n  } else {\r\n    console.log('Defaulting to PRODUCTION environment mode');\r\n    return EnvMode.PRODUCTION;\r\n  }\r\n};\r\n\r\n// Get the environment mode once to ensure consistency\r\nconst currentEnvMode = getEnvironmentMode();\r\n\r\n// Create the config object\r\nexport const config: Config = {\r\n  ENV_MODE: currentEnvMode,\r\n  IS_LOCAL: currentEnvMode === EnvMode.LOCAL,\r\n  SUBSCRIPTION_TIERS:\r\n    currentEnvMode === EnvMode.STAGING ? STAGING_TIERS : PROD_TIERS,\r\n};\r\n\r\n// Helper function to check if we're in local mode (for component conditionals)\r\nexport const isLocalMode = (): boolean => {\r\n  return config.IS_LOCAL;\r\n};\r\n\r\n// Export subscription tier type for typing elsewhere\r\nexport type SubscriptionTier = keyof typeof PROD_TIERS;\r\n"], "names": [], "mappings": "AAAA,yBAAyB;;;;;;AA6KP;AA5KX,IAAA,AAAK,iCAAA;;;;WAAA;;AAuCZ,sBAAsB;AACtB,MAAM,aAAgC;IACpC,MAAM;QACJ,SAAS;QACT,MAAM;IACR;IACA,WAAW;QACT,SAAS;QACT,MAAM;IACR;IACA,WAAW;QACT,SAAS;QACT,MAAM;IACR;IACA,aAAa;QACX,SAAS;QACT,MAAM;IACR;IACA,aAAa;QACX,SAAS;QACT,MAAM;IACR;IACA,aAAa;QACX,SAAS;QACT,MAAM;IACR;IACA,cAAc;QACZ,SAAS;QACT,MAAM;IACR;IACA,eAAe;QACb,SAAS;QACT,MAAM;IACR;IACA,kEAAkE;IAClE,kBAAkB;QAChB,SAAS;QACT,MAAM;IACR;IACA,kBAAkB;QAChB,SAAS;QACT,MAAM;IACR;IACA,oBAAoB;QAClB,SAAS;QACT,MAAM;IACR;IACA,oBAAoB;QAClB,SAAS;QACT,MAAM;IACR;IACA,oBAAoB;QAClB,SAAS;QACT,MAAM;IACR;IACA,qBAAqB;QACnB,SAAS;QACT,MAAM;IACR;IACA,sBAAsB;QACpB,SAAS;QACT,MAAM;IACR;AACF;AAEA,mBAAmB;AACnB,MAAM,gBAAmC;IACvC,MAAM;QACJ,SAAS;QACT,MAAM;IACR;IACA,WAAW;QACT,SAAS;QACT,MAAM;IACR;IACA,WAAW;QACT,SAAS;QACT,MAAM;IACR;IACA,aAAa;QACX,SAAS;QACT,MAAM;IACR;IACA,aAAa;QACX,SAAS;QACT,MAAM;IACR;IACA,aAAa;QACX,SAAS;QACT,MAAM;IACR;IACA,cAAc;QACZ,SAAS;QACT,MAAM;IACR;IACA,eAAe;QACb,SAAS;QACT,MAAM;IACR;IACA,kEAAkE;IAClE,kBAAkB;QAChB,SAAS;QACT,MAAM;IACR;IACA,kBAAkB;QAChB,SAAS;QACT,MAAM;IACR;IACA,oBAAoB;QAClB,SAAS;QACT,MAAM;IACR;IACA,oBAAoB;QAClB,SAAS;QACT,MAAM;IACR;IACA,oBAAoB;QAClB,SAAS;QACT,MAAM;IACR;IACA,qBAAqB;QACnB,SAAS;QACT,MAAM;IACR;IACA,sBAAsB;QACpB,SAAS;QACT,MAAM;IACR;AACF;AAEA,4DAA4D;AAC5D,MAAM,qBAAqB;IACzB,iEAAiE;IACjE,MAAM,qDAA4C;IAElD,4DAA4D;IAC5D,IAAI,SAAS;QACX,IAAI,qBAA2B;YAC7B,QAAQ,GAAG,CAAC;YACZ;QACF,OAAO,IAAI,uBAA6B;YACtC,QAAQ,GAAG,CAAC;YACZ;QACF,OAAO,IAAI,0BAAgC;YACzC,QAAQ,GAAG,CAAC;YACZ;QACF;IACF;IAEA,+EAA+E;IAC/E,wCAA4C;QAC1C,QAAQ,GAAG,CAAC;QACZ;IACF,OAAO;;IAGP;AACF;AAEA,sDAAsD;AACtD,MAAM,iBAAiB;AAGhB,MAAM,SAAiB;IAC5B,UAAU;IACV,UAAU;IACV,oBACE,+BAAqC,gBAAgB;AACzD;AAGO,MAAM,cAAc;IACzB,OAAO,OAAO,QAAQ;AACxB", "debugId": null}}, {"offset": {"line": 8869, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/lib/home.tsx"], "sourcesContent": ["import { FirstBentoAnimation } from '@/components/home/<USER>';\r\nimport { FourthBentoAnimation } from '@/components/home/<USER>';\r\nimport { SecondBentoAnimation } from '@/components/home/<USER>';\r\nimport { ThirdBentoAnimation } from '@/components/home/<USER>';\r\nimport { FlickeringGrid } from '@/components/home/<USER>/flickering-grid';\r\nimport { Globe } from '@/components/home/<USER>/globe';\r\nimport { cn } from '@/lib/utils';\r\nimport { motion } from 'motion/react';\r\nimport { config } from '@/lib/config';\r\n\r\nexport const Highlight = ({\r\n  children,\r\n  className,\r\n}: {\r\n  children: React.ReactNode;\r\n  className?: string;\r\n}) => {\r\n  return (\r\n    <span\r\n      className={cn(\r\n        'p-1 py-0.5 font-medium dark:font-semibold text-secondary',\r\n        className,\r\n      )}\r\n    >\r\n      {children}\r\n    </span>\r\n  );\r\n};\r\n\r\nexport const BLUR_FADE_DELAY = 0.15;\r\n\r\ninterface UpgradePlan {\r\n  /** @deprecated */\r\n  hours: string;\r\n  price: string;\r\n  stripePriceId: string;\r\n}\r\n\r\nexport interface PricingTier {\r\n  name: string;\r\n  price: string;\r\n  yearlyPrice?: string; // Add yearly price support\r\n  description: string;\r\n  buttonText: string;\r\n  buttonColor: string;\r\n  isPopular: boolean;\r\n  /** @deprecated */\r\n  hours: string;\r\n  features: string[];\r\n  stripePriceId: string;\r\n  yearlyStripePriceId?: string; // Add yearly price ID support\r\n  upgradePlans: UpgradePlan[];\r\n  hidden?: boolean; // Optional property to hide plans from display while keeping them in code\r\n  billingPeriod?: 'monthly' | 'yearly'; // Add billing period support\r\n  originalYearlyPrice?: string; // For showing crossed-out price\r\n  discountPercentage?: number; // For showing discount badge\r\n}\r\n\r\nexport const siteConfig = {\r\n  name: 'Kortix Suna',\r\n  description: 'The Generalist AI Agent that can act on your behalf.',\r\n  cta: 'Start Free',\r\n  url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',\r\n  keywords: ['AI Agent', 'Generalist AI', 'Open Source AI', 'Autonomous Agent'],\r\n  links: {\r\n    email: '<EMAIL>',\r\n    twitter: 'https://x.com/kortixai',\r\n    discord: 'https://discord.gg/kortixai',\r\n    github: 'https://github.com/Kortix-ai/Suna',\r\n    instagram: 'https://instagram.com/kortixai',\r\n  },\r\n  nav: {\r\n    links: [\r\n      { id: 1, name: 'Home', href: '#hero' },\r\n      { id: 2, name: 'Use Cases', href: '#use-cases' },\r\n      { id: 3, name: 'Open Source', href: '#open-source' },\r\n      { id: 4, name: 'Pricing', href: '#pricing' },\r\n    ],\r\n  },\r\n  hero: {\r\n    badgeIcon: (\r\n      <svg\r\n        width=\"14\"\r\n        height=\"14\"\r\n        viewBox=\"0 0 16 16\"\r\n        fill=\"none\"\r\n        xmlns=\"http://www.w3.org/2000/svg\"\r\n        className=\"text-muted-foreground\"\r\n      >\r\n        <path\r\n          d=\"M7.62758 1.09876C7.74088 1.03404 7.8691 1 7.99958 1C8.13006 1 8.25828 1.03404 8.37158 1.09876L13.6216 4.09876C13.7363 4.16438 13.8316 4.25915 13.8979 4.37347C13.9642 4.48779 13.9992 4.6176 13.9992 4.74976C13.9992 4.88191 13.9642 5.01172 13.8979 5.12604C13.8316 5.24036 13.7363 5.33513 13.6216 5.40076L8.37158 8.40076C8.25828 8.46548 8.13006 8.49952 7.99958 8.49952C7.8691 8.49952 7.74088 8.46548 7.62758 8.40076L2.37758 5.40076C2.26287 5.33513 2.16753 5.24036 2.10123 5.12604C2.03492 5.01172 2 4.88191 2 4.74976C2 4.6176 2.03492 4.48779 2.10123 4.37347C2.16753 4.25915 2.26287 4.16438 2.37758 4.09876L7.62758 1.09876Z\"\r\n          stroke=\"currentColor\"\r\n          strokeWidth=\"1.25\"\r\n        />\r\n        <path\r\n          d=\"M2.56958 7.23928L2.37758 7.34928C2.26287 7.41491 2.16753 7.50968 2.10123 7.624C2.03492 7.73831 2 7.86813 2 8.00028C2 8.13244 2.03492 8.26225 2.10123 8.37657C2.16753 8.49089 2.26287 8.58566 2.37758 8.65128L7.62758 11.6513C7.74088 11.716 7.8691 11.75 7.99958 11.75C8.13006 11.75 8.25828 11.716 8.37158 11.6513L13.6216 8.65128C13.7365 8.58573 13.8321 8.49093 13.8986 8.3765C13.965 8.26208 14 8.13211 14 7.99978C14 7.86745 13.965 7.73748 13.8986 7.62306C13.8321 7.50864 13.7365 7.41384 13.6216 7.34828L13.4296 7.23828L9.11558 9.70328C8.77568 9.89744 8.39102 9.99956 7.99958 9.99956C7.60814 9.99956 7.22347 9.89744 6.88358 9.70328L2.56958 7.23928Z\"\r\n          stroke=\"currentColor\"\r\n          strokeWidth=\"1.25\"\r\n        />\r\n        <path\r\n          d=\"M2.37845 10.5993L2.57045 10.4893L6.88445 12.9533C7.22435 13.1474 7.60901 13.2496 8.00045 13.2496C8.39189 13.2496 8.77656 13.1474 9.11645 12.9533L13.4305 10.4883L13.6225 10.5983C13.7374 10.6638 13.833 10.7586 13.8994 10.8731C13.9659 10.9875 14.0009 11.1175 14.0009 11.2498C14.0009 11.3821 13.9659 11.5121 13.8994 11.6265C13.833 11.7409 13.7374 11.8357 13.6225 11.9013L8.37245 14.9013C8.25915 14.966 8.13093 15 8.00045 15C7.86997 15 7.74175 14.966 7.62845 14.9013L2.37845 11.9013C2.2635 11.8357 2.16795 11.7409 2.10148 11.6265C2.03501 11.5121 2 11.3821 2 11.2498C2 11.1175 2.03501 10.9875 2.10148 10.8731C2.16795 10.7586 2.2635 10.6638 2.37845 10.5983V10.5993Z\"\r\n          stroke=\"currentColor\"\r\n          strokeWidth=\"1.25\"\r\n        />\r\n      </svg>\r\n    ),\r\n    badge: '100% OPEN SOURCE',\r\n    githubUrl: 'https://github.com/kortix-ai/suna',\r\n    title: 'Suna, the AI Employee.',\r\n    description:\r\n      'Suna by Kortix – is a generalist AI Agent that acts on your behalf.',\r\n    inputPlaceholder: 'Ask Suna to...',\r\n  },\r\n  cloudPricingItems: [\r\n    {\r\n      name: 'Free',\r\n      price: '$0',\r\n      description: 'Perfect for getting started',\r\n      buttonText: 'Start Free',\r\n      buttonColor: 'bg-secondary text-white',\r\n      isPopular: false,\r\n      /** @deprecated */\r\n      hours: '60 min',\r\n      features: [\r\n        '$5 free AI tokens included',\r\n        'Public projects',\r\n        'Basic Models',\r\n        'Community support',\r\n      ],\r\n      stripePriceId: config.SUBSCRIPTION_TIERS.FREE.priceId,\r\n      upgradePlans: [],\r\n    },\r\n    {\r\n      name: 'Plus',\r\n      price: '$20',\r\n      yearlyPrice: '$204',\r\n      originalYearlyPrice: '$240',\r\n      discountPercentage: 15,\r\n      description: 'Best for individuals and small teams',\r\n      buttonText: 'Start Free',\r\n      buttonColor: 'bg-primary text-white dark:text-black',\r\n      isPopular: true,\r\n      /** @deprecated */\r\n      hours: '2 hours',\r\n      features: [\r\n        '$20 AI token credits/month',\r\n        'Private projects',\r\n        'Premium AI Models',\r\n        'Community support',\r\n      ],\r\n      stripePriceId: config.SUBSCRIPTION_TIERS.TIER_2_20.priceId,\r\n      yearlyStripePriceId: config.SUBSCRIPTION_TIERS.TIER_2_20_YEARLY.priceId,\r\n      upgradePlans: [],\r\n    },\r\n    {\r\n      name: 'Pro',\r\n      price: '$50',\r\n      yearlyPrice: '$510',\r\n      originalYearlyPrice: '$600',\r\n      discountPercentage: 15,\r\n      description: 'Ideal for growing businesses',\r\n      buttonText: 'Start Free',\r\n      buttonColor: 'bg-secondary text-white',\r\n      isPopular: false,\r\n      /** @deprecated */\r\n      hours: '6 hours',\r\n      features: [\r\n        '$50 AI token credits/month',\r\n        'Private projects',\r\n        'Premium AI Models',\r\n        'Community support',\r\n      ],\r\n      stripePriceId: config.SUBSCRIPTION_TIERS.TIER_6_50.priceId,\r\n      yearlyStripePriceId: config.SUBSCRIPTION_TIERS.TIER_6_50_YEARLY.priceId,\r\n      upgradePlans: [],\r\n    },\r\n    {\r\n      name: 'Business',\r\n      price: '$100',\r\n      yearlyPrice: '$1020',\r\n      originalYearlyPrice: '$1200',\r\n      discountPercentage: 15,\r\n      description: 'For established businesses',\r\n      buttonText: 'Start Free',\r\n      buttonColor: 'bg-secondary text-white',\r\n      isPopular: false,\r\n      hours: '12 hours',\r\n      features: [\r\n        '$100 AI token credits/month',\r\n        'Private projects',\r\n        'Premium AI Models',\r\n        'Community support',\r\n      ],\r\n      stripePriceId: config.SUBSCRIPTION_TIERS.TIER_12_100.priceId,\r\n      yearlyStripePriceId: config.SUBSCRIPTION_TIERS.TIER_12_100_YEARLY.priceId,\r\n      upgradePlans: [],\r\n      hidden: true,\r\n    },\r\n    {\r\n      name: 'Ultra',\r\n      price: '$200',\r\n      yearlyPrice: '$2040',\r\n      originalYearlyPrice: '$2400',\r\n      discountPercentage: 15,\r\n      description: 'For power users and teams',\r\n      buttonText: 'Start Free',\r\n      buttonColor: 'bg-primary text-white dark:text-black',\r\n      isPopular: false,\r\n      hours: '25 hours',\r\n      features: [\r\n        '$200 AI token credits/month',\r\n        'Private projects',\r\n        'Premium AI Models',\r\n        'Priority support',\r\n      ],\r\n      stripePriceId: config.SUBSCRIPTION_TIERS.TIER_25_200.priceId,\r\n      yearlyStripePriceId: config.SUBSCRIPTION_TIERS.TIER_25_200_YEARLY.priceId,\r\n      upgradePlans: [],\r\n    },\r\n    {\r\n      name: 'Enterprise',\r\n      price: '$400',\r\n      yearlyPrice: '$4080',\r\n      originalYearlyPrice: '$4800',\r\n      discountPercentage: 15,\r\n      description: 'For large organizations',\r\n      buttonText: 'Start Free',\r\n      buttonColor: 'bg-secondary text-white',\r\n      isPopular: false,\r\n      hours: '50 hours',\r\n      features: [\r\n        '$400 AI token credits/month',\r\n        'Private projects',\r\n        'Premium AI Models',\r\n        'Full Suna AI access',\r\n        'Community support',\r\n        'Custom integrations',\r\n        'Dedicated account manager',\r\n      ],\r\n      stripePriceId: config.SUBSCRIPTION_TIERS.TIER_50_400.priceId,\r\n      yearlyStripePriceId: config.SUBSCRIPTION_TIERS.TIER_50_400_YEARLY.priceId,\r\n      upgradePlans: [],\r\n      hidden: true,\r\n    },\r\n    {\r\n      name: 'Scale',\r\n      price: '$800',\r\n      yearlyPrice: '$8160',\r\n      originalYearlyPrice: '$9600',\r\n      discountPercentage: 15,\r\n      description: 'For scaling enterprises',\r\n      buttonText: 'Start Free',\r\n      buttonColor: 'bg-secondary text-white',\r\n      isPopular: false,\r\n      hours: '125 hours',\r\n      features: [\r\n        '$800 AI token credits/month',\r\n        'Private projects',\r\n        'Premium AI Models',\r\n        'Full Suna AI access',\r\n        'Community support',\r\n        'Custom integrations',\r\n        'Dedicated account manager',\r\n        'Custom SLA',\r\n      ],\r\n      stripePriceId: config.SUBSCRIPTION_TIERS.TIER_125_800.priceId,\r\n      yearlyStripePriceId: config.SUBSCRIPTION_TIERS.TIER_125_800_YEARLY.priceId,\r\n      upgradePlans: [],\r\n      hidden: true,\r\n    },\r\n    {\r\n      name: 'Premium',\r\n      price: '$1000',\r\n      yearlyPrice: '$10200',\r\n      originalYearlyPrice: '$12000',\r\n      discountPercentage: 15,\r\n      description: 'For maximum scale and performance',\r\n      buttonText: 'Start Free',\r\n      buttonColor: 'bg-secondary text-white',\r\n      isPopular: false,\r\n      hours: '200 hours',\r\n      features: [\r\n        '$1000 AI token credits/month',\r\n        'Private projects',\r\n        'Premium AI Models',\r\n        'Full Suna AI access',\r\n        'Priority support',\r\n        'Custom integrations',\r\n        'Dedicated account manager',\r\n        'Custom SLA',\r\n        'White-label options',\r\n      ],\r\n      stripePriceId: config.SUBSCRIPTION_TIERS.TIER_200_1000.priceId,\r\n      yearlyStripePriceId: config.SUBSCRIPTION_TIERS.TIER_200_1000_YEARLY.priceId,\r\n      upgradePlans: [],\r\n      hidden: true,\r\n    },\r\n  ],\r\n  companyShowcase: {\r\n    companyLogos: [\r\n      {\r\n        id: 1,\r\n        name: 'Company 1',\r\n        logo: (\r\n          <svg\r\n            width=\"110\"\r\n            height=\"31\"\r\n            viewBox=\"0 0 110 31\"\r\n            fill=\"none\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            className=\"dark:fill-white fill-black\"\r\n          >\r\n            <path d=\"M34.5469 14.5155C34.5469 19.4338 37.7054 22.8631 42.0822 22.8631C46.4591 22.8631 49.6176 19.4338 49.6176 14.5155C49.6176 9.59721 46.4591 6.16797 42.0822 6.16797C37.7054 6.16797 34.5469 9.59721 34.5469 14.5155ZM46.7298 14.5155C46.7298 18.035 44.8121 20.3137 42.0822 20.3137C39.3524 20.3137 37.4347 18.035 37.4347 14.5155C37.4347 10.996 39.3524 8.71736 42.0822 8.71736C44.8121 8.71736 46.7298 10.996 46.7298 14.5155Z\" />\r\n            <path d=\"M57.7468 22.8652C61.0633 22.8652 62.9584 20.0676 62.9584 16.706C62.9584 13.3444 61.0633 10.5469 57.7468 10.5469C56.2127 10.5469 55.0846 11.156 54.3401 12.0359V10.7725H51.6328V26.678H54.3401V21.3761C55.0846 22.256 56.2127 22.8652 57.7468 22.8652ZM54.2724 16.3676C54.2724 14.1341 55.5359 12.9158 57.2054 12.9158C59.1682 12.9158 60.2285 14.4499 60.2285 16.706C60.2285 18.9621 59.1682 20.4963 57.2054 20.4963C55.5359 20.4963 54.2724 19.2554 54.2724 17.067V16.3676Z\" />\r\n            <path d=\"M70.2843 22.8652C72.6532 22.8652 74.5258 21.6243 75.3605 19.5487L73.0367 18.6688C72.6758 19.8871 71.6154 20.5639 70.2843 20.5639C68.5471 20.5639 67.3288 19.3231 67.1258 17.2926H75.4282V16.3902C75.4282 13.1414 73.6008 10.5469 70.1715 10.5469C66.7422 10.5469 64.5312 13.2316 64.5312 16.706C64.5312 20.3609 66.9002 22.8652 70.2843 22.8652ZM70.1489 12.8255C71.8636 12.8255 72.6758 13.9536 72.6983 15.2621H67.2611C67.6672 13.6603 68.7501 12.8255 70.1489 12.8255Z\" />\r\n            <path d=\"M77.4609 22.617H80.1683V15.6682C80.1683 13.9761 81.4091 13.0737 82.6274 13.0737C84.1164 13.0737 84.703 14.1341 84.703 15.6005V22.617H87.4103V14.8109C87.4103 12.2615 85.9213 10.5469 83.4396 10.5469C81.9054 10.5469 80.8451 11.2463 80.1683 12.0359V10.7725H77.4609V22.617Z\" />\r\n            <path d=\"M95.3397 6.41797L89.2031 22.6167H92.0684L93.4446 18.9167H100.438L101.837 22.6167H104.748L98.611 6.41797H95.3397ZM96.919 9.62163L99.4909 16.3899H94.3921L96.919 9.62163Z\" />\r\n            <path d=\"M109.396 6.46484H106.508V22.6636H109.396V6.46484Z\" />\r\n            <path d=\"M27.9278 12.3665C28.6102 10.3182 28.3752 8.07433 27.2838 6.21115C25.6425 3.35343 22.343 1.88321 19.1205 2.57508C17.687 0.960086 15.6273 0.0416664 13.4681 0.054827C10.1742 0.0473067 7.25158 2.16804 6.2382 5.30213C4.12219 5.73551 2.29568 7.06002 1.22685 8.93727C-0.426682 11.7875 -0.0497272 15.3803 2.15937 17.8244C1.4769 19.8728 1.71191 22.1166 2.8033 23.9798C4.4446 26.8375 7.74416 28.3078 10.9666 27.6159C12.3992 29.2309 14.4598 30.1494 16.6191 30.1352C19.9149 30.1437 22.8384 28.021 23.8518 24.8841C25.9678 24.4508 27.7943 23.1263 28.8631 21.249C30.5148 18.3988 30.137 14.8088 27.9287 12.3646L27.9278 12.3665ZM16.621 28.1696C15.3021 28.1714 14.0246 27.7099 13.0121 26.8648C13.0582 26.8403 13.1381 26.7962 13.1898 26.7642L19.1797 23.3049C19.4862 23.131 19.6742 22.8048 19.6723 22.4522V14.0078L22.2038 15.4696C22.2311 15.4828 22.249 15.5091 22.2527 15.5392V22.5321C22.249 25.6418 19.7306 28.163 16.621 28.1696ZM4.50945 22.9965C3.84863 21.8553 3.61081 20.5176 3.83735 19.2194C3.88154 19.2457 3.95954 19.2937 4.01501 19.3257L10.0049 22.785C10.3086 22.9627 10.6846 22.9627 10.9892 22.785L18.3018 18.5624V21.4859C18.3036 21.5159 18.2895 21.5451 18.266 21.5639L12.2112 25.0599C9.51423 26.6129 6.06995 25.6897 4.51042 22.9965H4.50945ZM2.93302 9.9215C3.59104 8.77841 4.62981 7.90416 5.8669 7.45014C5.8669 7.50182 5.86408 7.59303 5.86408 7.65695V14.5766C5.86218 14.9281 6.05019 15.2543 6.35572 15.4282L13.6683 19.65L11.1368 21.1117C11.1114 21.1287 11.0794 21.1315 11.0512 21.1193L4.99548 17.6204C2.30413 16.0618 1.38101 12.6185 2.93208 9.92243L2.93302 9.9215ZM23.7324 14.7618L16.4198 10.5391L18.9513 9.07829C18.9767 9.06136 19.0087 9.05853 19.0369 9.07077L25.0926 12.5668C27.7887 14.1244 28.7127 17.5734 27.155 20.2695C26.4961 21.4107 25.4583 22.2849 24.2221 22.7399V15.6134C24.2249 15.2619 24.0379 14.9366 23.7333 14.7618H23.7324ZM26.2517 10.9697C26.2075 10.9424 26.1295 10.8954 26.074 10.8634L20.0841 7.40406C19.7804 7.2264 19.4044 7.2264 19.0998 7.40406L11.7873 11.6267V8.70321C11.7854 8.67313 11.7995 8.64398 11.823 8.62518L17.8778 5.13199C20.5748 3.57621 24.0228 4.50217 25.5777 7.20008C26.2347 8.33941 26.4726 9.67333 26.2498 10.9697H26.2517ZM10.411 16.1803L7.87856 14.7185C7.85131 14.7054 7.83347 14.679 7.82971 14.649V7.65599C7.83157 4.54257 10.3575 2.01951 13.4709 2.02139C14.7879 2.02139 16.0626 2.48389 17.075 3.32618C17.0289 3.3506 16.95 3.39479 16.8973 3.42677L10.9074 6.88612C10.6009 7.06002 10.4129 7.38526 10.4148 7.73778L10.411 16.1784V16.1803ZM11.7863 13.2154L15.0436 11.3344L18.3008 13.2145V16.9756L15.0436 18.8556L11.7863 16.9756V13.2154Z\" />\r\n          </svg>\r\n        ),\r\n      },\r\n      {\r\n        id: 2,\r\n        name: 'Company 2',\r\n        logo: (\r\n          <svg\r\n            width=\"113\"\r\n            height=\"25\"\r\n            viewBox=\"0 0 113 25\"\r\n            fill=\"none\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            className=\"dark:fill-white fill-black\"\r\n          >\r\n            <path d=\"M0.75 2.69908C0.75 1.48458 1.73458 0.5 2.94908 0.5H11.551C12.7655 0.5 13.75 1.48458 13.75 2.69908V4.4005C13.75 5.00775 13.2577 5.50004 12.6505 5.50004H1.84954C1.24229 5.50004 0.75 5.00775 0.75 4.4005V2.69908Z\" />\r\n            <path d=\"M0.75 9.59954C0.75 8.99224 1.24229 8.5 1.84954 8.5H22.551C23.7655 8.5 24.7501 9.48453 24.7501 10.6991V16.4005C24.7501 17.0077 24.2578 17.5 23.6506 17.5H2.94908C1.73458 17.5 0.75 16.5154 0.75 15.3009V9.59954Z\" />\r\n            <path d=\"M11.75 21.5995C11.75 20.9923 12.2423 20.5 12.8495 20.5H23.6505C24.2577 20.5 24.75 20.9923 24.75 21.5995V22.3009C24.75 23.5154 23.7654 24.5 22.5509 24.5H13.9491C12.7346 24.5 11.75 23.5154 11.75 22.3009V21.5995Z\" />\r\n            <path d=\"M38.3455 15.241H40.6572L44.9499 22.2582H50.2059L45.4452 14.7456C48.0872 13.8375 49.628 11.8012 49.628 8.85671C49.628 4.72899 46.7389 2.5 42.0881 2.5H33.75V22.2582H38.3455V15.241ZM38.3455 11.4985V6.38009H41.8404C44.0144 6.38009 45.115 7.31571 45.115 8.93927C45.115 10.5353 44.0144 11.4985 41.8404 11.4985H38.3455Z\" />\r\n            <path d=\"M58.113 22.5607C61.0026 22.5607 63.6446 21.1023 64.7178 18.3229L61.0305 17.1396C60.6177 18.4055 59.5444 19.0659 58.1682 19.0659C56.4896 19.0659 55.3064 17.9377 55.0036 15.9563H64.8278V14.8006C64.8278 10.7002 62.4335 7.45312 58.0309 7.45312C53.8477 7.45312 50.7656 10.7002 50.7656 14.9932C50.7656 19.5062 53.7378 22.5607 58.113 22.5607ZM57.9757 10.8655C59.5991 10.8655 60.4525 11.9662 60.4799 13.2595H55.1413C55.5815 11.6635 56.6274 10.8655 57.9757 10.8655Z\" />\r\n            <path d=\"M67.3281 18.378C67.3281 21.35 68.8967 22.3957 72.0335 22.3957C73.0794 22.3957 73.9051 22.3133 74.6755 22.2031V18.5982C74.1801 18.6532 73.9324 18.6807 73.4097 18.6807C72.309 18.6807 71.6759 18.4606 71.6759 17.2498V11.3884H74.5103V7.75595H71.6759V3.73828H67.3281V7.75595H65.4844V11.3884H67.3281V18.378Z\" />\r\n            <path d=\"M90.4203 15.0207C90.4203 10.5077 87.4212 7.45312 83.1555 7.45312C78.8628 7.45312 75.8906 10.5077 75.8906 15.0207C75.8906 19.5337 78.8628 22.5607 83.1555 22.5607C87.4212 22.5607 90.4203 19.5337 90.4203 15.0207ZM80.2663 15.0207C80.2663 12.489 81.3943 11.0581 83.1555 11.0581C84.9171 11.0581 86.0451 12.489 86.0451 15.0207C86.0451 17.5524 84.9171 18.9833 83.1555 18.9833C81.3943 18.9833 80.2663 17.5524 80.2663 15.0207Z\" />\r\n            <path d=\"M106.28 15.0207C106.28 10.5077 103.281 7.45312 99.0148 7.45312C94.7222 7.45312 91.75 10.5077 91.75 15.0207C91.75 19.5337 94.7222 22.5607 99.0148 22.5607C103.281 22.5607 106.28 19.5337 106.28 15.0207ZM96.1257 15.0207C96.1257 12.489 97.2537 11.0581 99.0148 11.0581C100.776 11.0581 101.904 12.489 101.904 15.0207C101.904 17.5524 100.776 18.9833 99.0148 18.9833C97.2537 18.9833 96.1257 17.5524 96.1257 15.0207Z\" />\r\n            <path d=\"M112.747 2.5H108.344V22.2582H112.747V2.5Z\" />\r\n          </svg>\r\n        ),\r\n      },\r\n      {\r\n        id: 3,\r\n        name: 'Company 3',\r\n        logo: (\r\n          <svg\r\n            width=\"73\"\r\n            height=\"31\"\r\n            viewBox=\"0 0 73 31\"\r\n            fill=\"none\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            className=\"dark:fill-white fill-black\"\r\n          >\r\n            <path\r\n              fillRule=\"evenodd\"\r\n              clipRule=\"evenodd\"\r\n              d=\"M72.2313 15.5944C72.2313 10.4676 69.7478 6.4222 65.0013 6.4222C60.2348 6.4222 57.3508 10.4676 57.3508 15.5543C57.3508 21.5824 60.7558 24.6264 65.6423 24.6264C68.0253 24.6264 69.8278 24.0857 71.1898 23.3247V19.3194C69.8278 20.0003 68.2658 20.4209 66.2833 20.4209C64.3403 20.4209 62.6183 19.74 62.3978 17.3768H72.1913C72.1913 17.1165 72.2313 16.0751 72.2313 15.5944ZM62.3378 13.6919C62.3378 11.4289 63.7198 10.4876 64.9813 10.4876C66.2028 10.4876 67.5048 11.4289 67.5048 13.6919H62.3378ZM49.6203 6.4222C47.6578 6.4222 46.3958 7.3434 45.6948 7.98425L45.4348 6.7426H41.0288V30.0938L46.0353 29.0323L46.0553 23.3648C46.7763 23.8855 47.8378 24.6264 49.6003 24.6264C53.1853 24.6264 56.4498 21.7426 56.4498 15.3941C56.4298 9.5864 53.1253 6.4222 49.6203 6.4222ZM48.4188 20.2206C47.2373 20.2206 46.5363 19.8001 46.0553 19.2794L46.0353 11.8494C46.5563 11.2687 47.2773 10.8681 48.4188 10.8681C50.2413 10.8681 51.5028 12.9108 51.5028 15.5343C51.5028 18.2179 50.2613 20.2206 48.4188 20.2206ZM34.1393 5.2406L39.1663 4.15915V0.09375L34.1393 1.15515V5.2406ZM34.1393 6.76265H39.1663V24.286H34.1393V6.76265ZM28.7518 8.2446L28.4313 6.76265H24.1053V24.286H29.1123V12.4102C30.2938 10.8681 32.2968 11.1485 32.9178 11.3688V6.76265C32.2768 6.5223 29.9333 6.08175 28.7518 8.2446ZM18.7383 2.41685L13.8513 3.45825L13.8313 19.4996C13.8313 22.4636 16.0543 24.6465 19.0188 24.6465C20.6608 24.6465 21.8623 24.3461 22.5233 23.9856V19.9202C21.8823 20.1805 18.7183 21.1017 18.7183 18.1378V11.0284H22.5233V6.76265H18.7183L18.7383 2.41685ZM5.19971 11.8494C5.19971 11.0684 5.84061 10.768 6.90206 10.768C8.42411 10.768 10.3468 11.2286 11.8688 12.0497V7.3434C10.2066 6.68255 8.56431 6.4222 6.90206 6.4222C2.83651 6.4222 0.132812 8.545 0.132812 12.0898C0.132812 17.6171 7.74321 16.736 7.74321 19.1191C7.74321 20.0403 6.94211 20.3407 5.82056 20.3407C4.15831 20.3407 2.03541 19.6598 0.353111 18.7386V23.505C2.21566 24.3061 4.09821 24.6465 5.82056 24.6465C9.98626 24.6465 12.8503 22.5837 12.8503 18.9989C12.8303 13.031 5.19971 14.0924 5.19971 11.8494Z\"\r\n            />\r\n          </svg>\r\n        ),\r\n      },\r\n      {\r\n        id: 4,\r\n        name: 'Company 4',\r\n        logo: (\r\n          <svg\r\n            width=\"96\"\r\n            height=\"23\"\r\n            viewBox=\"0 0 96 23\"\r\n            fill=\"none\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            className=\"dark:fill-white fill-black\"\r\n          >\r\n            <path d=\"M53.6896 0.965339H59.5709L56.6122 22.0721H50.7313L53.6896 0.965339ZM46.2752 0.965339L42.3065 13.1242L40.5746 0.965339H36.4617L31.2658 13.0881L30.6168 0.965339H24.9158L26.9005 22.0721H31.6268L37.4717 8.72251L39.5286 22.0721H44.1827L51.8677 0.965339H46.2752ZM95.6509 13.2324H81.6879C81.76 15.9747 83.4016 17.7786 85.8191 17.7786C87.641 17.7786 89.0845 16.8044 90.203 14.9463L94.9167 17.0895C93.2968 20.2807 89.8833 22.3606 85.6749 22.3606C79.9382 22.3606 76.1315 18.5001 76.1315 12.2945C76.1315 5.47532 80.6054 0.640625 86.9194 0.640625C92.4758 0.640625 95.9754 4.39293 95.9754 10.2378C95.9754 11.2121 95.8672 12.1862 95.6509 13.2324ZM90.4194 9.19153C90.4194 6.73812 89.0484 5.18671 86.8473 5.18671C84.6466 5.18671 82.6979 6.81028 82.1932 9.19153H90.4194ZM6.53496 7.32622L0.53125 14.342H11.2506L12.4557 11.0334H7.8627L10.6697 7.78807L10.6787 7.70145L8.85309 4.56074H17.0631L10.6986 22.0721H15.0536L22.7386 0.965339H2.88366L6.53314 7.32622H6.53496ZM69.132 5.18671C71.2067 5.18671 73.0251 6.30155 74.6124 8.21378L75.446 2.26424C73.9665 1.26303 71.9643 0.640625 69.3123 0.640625C64.0448 0.640625 61.0861 3.72547 61.0861 7.64011C61.0861 10.3552 62.6017 12.0148 65.0912 13.0881L66.2819 13.6294C68.5004 14.5765 69.096 15.0455 69.096 16.0468C69.096 17.048 68.0947 17.7425 66.5704 17.7425C64.0535 17.7514 62.0153 16.4617 60.4818 14.2607L59.6322 20.324C61.3784 21.6555 63.6173 22.3606 66.5704 22.3606C71.5764 22.3606 74.6524 19.4743 74.6524 15.4694C74.6524 12.7454 73.4435 10.9956 70.3948 9.62454L69.096 9.01117C67.2919 8.20839 66.6786 7.76639 66.6786 6.88243C66.6786 5.92632 67.5174 5.18671 69.132 5.18671Z\" />\r\n          </svg>\r\n        ),\r\n      },\r\n      {\r\n        id: 5,\r\n        name: 'Company 5',\r\n        logo: (\r\n          <svg\r\n            width=\"99\"\r\n            height=\"31\"\r\n            viewBox=\"0 0 99 31\"\r\n            fill=\"none\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            className=\"dark:fill-white fill-black\"\r\n          >\r\n            <path d=\"M38.0781 26.5517V4.44531H42.0332V26.5517H38.0781Z\" />\r\n            <path d=\"M76.0156 11.5289H79.7927V13.3616C80.5953 11.8961 82.4697 11.1016 84.075 11.1016C86.0686 11.1016 87.6738 11.9875 88.4172 13.6052C89.5763 11.7725 91.124 11.1016 93.0571 11.1016C95.763 11.1016 98.3503 12.7811 98.3503 16.811V26.5515H94.5139V17.6361C94.5139 16.0175 93.7402 14.7965 91.926 14.7965C90.2305 14.7965 89.2189 16.1402 89.2189 17.7588V26.5525H85.2946V17.6361C85.2946 16.0175 84.4919 14.7965 82.7067 14.7965C80.9813 14.7965 79.9698 16.1097 79.9698 17.7588V26.5525H76.0156V11.5289Z\" />\r\n            <path d=\"M50.971 26.9864C46.5155 26.9864 43.2891 23.5951 43.2891 19.0402C43.2891 14.5579 46.5052 11.0859 50.971 11.0859C55.459 11.0859 58.6533 14.5901 58.6533 19.0402C58.6533 23.562 55.425 26.9864 50.971 26.9864ZM50.971 14.763C48.6732 14.763 46.8038 16.6816 46.8038 19.0412C46.8038 21.4006 48.6732 23.3193 50.971 23.3193C53.2691 23.3193 55.1376 21.4006 55.1376 19.0412C55.1376 16.6816 53.2691 14.763 50.971 14.763Z\" />\r\n            <path d=\"M67.1041 26.9864C62.6483 26.9864 59.4219 23.5951 59.4219 19.0402C59.4219 14.5579 62.638 11.0859 67.1041 11.0859C71.5918 11.0859 74.7861 14.5901 74.7861 19.0402C74.7861 23.562 71.5561 26.9864 67.1041 26.9864ZM67.1041 14.7119C64.7797 14.7119 62.8885 16.6539 62.8885 19.0393C62.8885 21.4248 64.7797 23.3667 67.1041 23.3667C69.4282 23.3667 71.3194 21.4248 71.3194 19.0393C71.3185 16.6539 69.4273 14.7119 67.1041 14.7119Z\" />\r\n            <path d=\"M30.1043 13.8198H21.4933L28.9509 9.40149L27.3131 6.48975L19.8556 10.9081L24.1602 3.25587L21.3228 1.57426L17.0181 9.22646V0.390625H13.7425V9.22731L9.43615 1.57426L6.59959 3.25502L10.9051 10.9072L3.44749 6.48975L1.80966 9.40064L9.26724 13.819H0.65625V17.1805H9.26641L1.80966 21.5989L3.44749 24.5106L10.9043 20.0931L6.59876 27.7453L9.43615 29.426L13.7416 21.7731V30.6097H17.0173V21.7739L21.322 29.426L24.1593 27.7453L19.8539 20.0923L27.3114 24.5106L28.9493 21.5989L21.4925 17.1813H30.1026V13.8198H30.1043ZM15.3803 20.0719C12.9104 20.0719 10.9084 18.0176 10.9084 15.4828C10.9084 12.9482 12.9104 10.8937 15.3803 10.8937C17.8502 10.8937 19.8521 12.9482 19.8521 15.4828C19.8521 18.0176 17.8502 20.0719 15.3803 20.0719Z\" />\r\n          </svg>\r\n        ),\r\n      },\r\n      {\r\n        id: 6,\r\n        name: 'Company 6',\r\n        logo: (\r\n          <svg\r\n            width=\"132\"\r\n            height=\"21\"\r\n            viewBox=\"0 0 132 21\"\r\n            fill=\"none\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            className=\"dark:fill-white fill-black\"\r\n          >\r\n            <path d=\"M62.1915 1.56344L62.1641 1.56944C61.1444 1.79968 60.627 2.14298 60.627 3.3812H60.6224L60.627 18.2354C60.714 19.2134 61.2396 19.5153 62.1641 19.7239L62.1915 19.7302V19.9404H55.0749V19.7302L55.1023 19.7239C56.0258 19.5153 56.5392 19.2134 56.6262 18.2354V3.78459L49.767 19.907H49.3889L42.7236 4.22197V17.8774C42.7236 19.1156 43.2415 19.4589 44.2617 19.6892L44.2886 19.6952V19.907H40.2031V19.6945L40.2299 19.6884C41.25 19.4583 41.7701 19.1149 41.7701 17.8767V3.3812C41.7701 2.14298 41.2521 1.79968 40.2319 1.56944L40.2051 1.56344V1.35156H46.2267L51.415 13.5466L56.6038 1.35156H62.1915V1.56344Z\" />\r\n            <path\r\n              fillRule=\"evenodd\"\r\n              clipRule=\"evenodd\"\r\n              d=\"M73.6869 11.9082H65.346L65.345 11.9089C65.1832 14.7676 66.9661 17.1276 69.2933 17.3017C70.9984 17.4292 72.4968 16.3842 73.3307 14.6598L73.5704 14.7442C73.0957 17.9527 71.1917 20.343 67.9221 20.343C64.1966 20.343 62.0993 17.24 62.0093 13.3573C62.0067 13.2072 62.0077 13.058 62.0108 12.9098C62.0128 12.8174 62.0164 12.7253 62.021 12.6336C62.0235 12.5845 62.026 12.5385 62.0296 12.4871C62.0332 12.4357 62.0367 12.3843 62.0413 12.3329C62.1751 10.6186 62.7272 9.09318 63.6379 7.96441C64.2037 7.26377 64.884 6.72991 65.6549 6.37292C66.3321 6.03191 67.2841 5.84375 68.06 5.84375H68.0936C69.589 5.84375 71.0126 6.34957 71.9742 7.26875C73.0865 8.33244 73.6635 9.8816 73.6869 11.8732V11.9082ZM68.0376 6.65751C66.4679 6.68987 65.5327 8.56971 65.4162 11.1742H70.1802C70.229 9.14122 69.9141 7.78093 69.243 7.12995C68.9275 6.82435 68.5215 6.66555 68.0376 6.65751Z\"\r\n            />\r\n            <path\r\n              fillRule=\"evenodd\"\r\n              clipRule=\"evenodd\"\r\n              d=\"M87.1166 17.8132C87.1166 19.1145 87.6346 19.4738 88.6542 19.7151L88.6812 19.7204V19.9426H83.121V17.7972C82.4844 19.3954 81.0928 20.343 79.3634 20.343C76.4077 20.343 74.4214 17.6311 74.4219 13.5962C74.4219 11.337 75.0711 9.38915 76.2999 7.96379C77.4788 6.59618 79.1136 5.84313 80.9015 5.84313C81.7833 5.84313 82.5104 6.03297 83.121 6.42298V4.04938C83.121 3.52357 82.9927 3.14984 82.7292 2.90632C82.3923 2.59532 81.8128 2.47723 80.958 2.54627L80.9198 2.54928V2.32575L87.1166 0.5V17.8132ZM81.5116 18.1759C82.2768 18.1759 82.8477 17.8189 83.121 17.17V7.60945C82.8508 6.98655 82.2845 6.62951 81.5645 6.62951C79.8463 6.62951 78.7355 8.93874 78.7355 12.5088C78.7355 14.2241 79.0164 15.6722 79.5507 16.6965C80.0472 17.6508 80.7463 18.1759 81.5116 18.1759Z\"\r\n            />\r\n            <path d=\"M91.7591 0.644531C93.0189 0.644531 93.9689 1.59481 93.9689 2.85465C93.9689 4.11454 93.0189 5.06476 91.7591 5.06476C90.5059 5.06476 89.5234 4.09383 89.5234 2.85465C89.5234 1.61546 90.5049 0.644531 91.7591 0.644531Z\" />\r\n            <path d=\"M93.8929 17.8119C93.8929 19.1132 94.4104 19.4725 95.4306 19.7137L95.4591 19.7191V19.9413H89.8937V9.62677C89.8937 8.22074 89.5645 7.80631 88.3612 7.69925L88.3281 7.69625V7.47934L93.8884 5.88281V17.8119H93.8929Z\" />\r\n            <path d=\"M108.142 17.8119C108.142 19.1132 108.66 19.4725 109.68 19.7137L109.706 19.7227V19.9449H104.146V17.7429C103.514 19.3284 102.079 20.3417 100.442 20.3417C98.5098 20.3417 97.2764 19.4595 96.8053 17.7725L96.7056 17.3371C96.6257 16.8988 96.587 16.454 96.5895 16.0085V9.62677C96.5895 8.22039 96.2598 7.806 95.057 7.69925L95.0234 7.69625V7.4797L100.584 5.88317V15.9034C100.584 17.3858 101.25 18.2023 102.459 18.2023C103.204 18.1763 103.819 17.7909 104.147 17.1419V9.60138C104.147 8.18904 103.863 7.75095 102.879 7.64751L102.848 7.6442V7.42663L108.142 5.88281V17.8119Z\" />\r\n            <path d=\"M131.286 19.7207V19.9432H125.726V10.0431C125.726 8.44255 125.157 7.66482 123.984 7.66482C123.016 7.66482 122.518 8.36649 122.271 8.95936C122.321 9.26455 122.346 9.57356 122.344 9.88292V18.1575C122.344 19.1285 122.629 19.5068 123.512 19.7144L123.539 19.721V19.9432H118.35V10.0431C118.35 8.4656 117.764 7.66482 116.608 7.66482C115.677 7.66482 115.206 8.31107 114.973 8.85663V18.1575C114.973 19.1285 115.258 19.5068 116.141 19.7144L116.168 19.721V19.9432H110.98V9.53993C110.98 8.16998 110.636 7.74353 109.448 7.63811L109.414 7.63511V7.41692L114.975 5.88578V8.21699C115.537 6.72625 116.847 5.84375 118.52 5.84375C120.424 5.84375 121.711 6.75327 122.158 8.40821C122.721 6.84638 124.185 5.84375 125.92 5.84375C128.3 5.84375 129.721 7.35383 129.721 9.88394V17.9984C129.721 18.978 130.152 19.4591 131.259 19.7144L131.286 19.7207Z\" />\r\n            <path d=\"M10.0364 20.5015C15.4631 20.5015 19.8622 16.0719 19.8622 10.608C19.8622 5.14408 15.4628 0.714844 10.0364 0.714844C4.61003 0.714844 0.210938 5.14276 0.210938 10.608C0.210938 16.0732 4.60973 20.5015 10.0364 20.5015Z\" />\r\n            <path d=\"M25.7254 19.9227C28.4386 19.9227 30.6383 15.7534 30.6383 10.6098C30.6383 5.46753 28.4386 1.29688 25.7254 1.29688C23.0122 1.29688 20.8125 5.46753 20.8125 10.6098C20.8125 15.7521 23.0122 19.9227 25.7254 19.9227Z\" />\r\n            <path d=\"M35.0496 10.6057C35.0496 15.2142 34.2759 18.9497 33.3217 18.9497C32.3675 18.9497 31.5938 15.2128 31.5938 10.6057C31.5938 5.99865 32.3675 2.26172 33.322 2.26172C34.2766 2.26172 35.0496 5.99763 35.0496 10.6057Z\" />\r\n          </svg>\r\n        ),\r\n      },\r\n      {\r\n        id: 7,\r\n        name: 'Company 7',\r\n        logo: (\r\n          <svg\r\n            width=\"134\"\r\n            height=\"31\"\r\n            viewBox=\"0 0 134 31\"\r\n            fill=\"none\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            className=\"dark:fill-white fill-black\"\r\n          >\r\n            <mask\r\n              id=\"mask0_342_6736\"\r\n              maskUnits=\"userSpaceOnUse\"\r\n              x=\"42\"\r\n              y=\"7\"\r\n              width=\"92\"\r\n              height=\"20\"\r\n            >\r\n              <path\r\n                d=\"M133.02 7.77734H42.4375V26.5404H133.02V7.77734Z\"\r\n                fill=\"white\"\r\n              />\r\n            </mask>\r\n            <g mask=\"url(#mask0_342_6736)\">\r\n              <path d=\"M53.9534 12.179C53.8381 12.2504 53.7006 12.2758 53.5672 12.2501C53.4343 12.2243 53.3155 12.1494 53.2353 12.0401C52.8379 11.4719 52.3053 11.0113 51.6861 10.6998C51.0663 10.3882 50.3791 10.2355 49.6856 10.2552C46.9763 10.2552 45.3095 12.3991 45.3095 15.4608C45.3095 18.5225 47.0033 20.707 49.7131 20.707C50.4172 20.7231 51.1137 20.5597 51.737 20.232C52.3603 19.9043 52.89 19.4232 53.2763 18.8341C53.3535 18.723 53.47 18.645 53.6023 18.6155C53.7345 18.586 53.8733 18.6072 53.9909 18.6749L55.2744 19.4199C55.34 19.4582 55.3973 19.5096 55.4418 19.5711C55.4863 19.6326 55.5179 19.7028 55.5337 19.7771C55.5495 19.8514 55.5501 19.9282 55.5343 20.0026C55.5191 20.077 55.488 20.1473 55.4436 20.2091C54.8144 21.1522 53.9564 21.9201 52.9491 22.4407C51.9418 22.9614 50.8193 23.2179 49.6856 23.1861C45.3738 23.1861 42.4375 20.0432 42.4375 15.4608C42.4375 10.8784 45.3738 7.77607 49.6417 7.77607C50.7561 7.74511 51.8611 7.98763 52.8601 8.4824C53.8592 8.97716 54.7219 9.70913 55.3727 10.6142C55.4172 10.6749 55.4488 10.7441 55.4658 10.8176C55.4822 10.8911 55.4839 10.9672 55.4699 11.0413C55.4558 11.1153 55.4266 11.1856 55.3844 11.2479C55.3423 11.3102 55.2873 11.3631 55.2235 11.4033L53.9534 12.179Z\" />\r\n              <path d=\"M63.4416 22.3742V21.751C62.7638 22.6249 61.8157 23.1668 60.2951 23.1668C58.0459 23.1668 56.4844 21.9373 56.4844 19.7799C56.4844 17.0942 58.8793 16.6336 60.3595 16.427C61.9409 16.1967 63.2958 16.0883 63.2958 15.0519C63.2958 14.1341 62.2324 13.8868 61.3346 13.8868C60.3056 13.9043 59.2943 14.1606 58.3813 14.6353C58.3175 14.6703 58.2472 14.692 58.1752 14.699C58.1027 14.7062 58.0301 14.6986 57.9604 14.6767C57.8914 14.6549 57.827 14.6193 57.772 14.572C57.717 14.5248 57.6719 14.4668 57.6397 14.4016L57.1651 13.4398C57.1024 13.3144 57.0907 13.1696 57.1323 13.0357C57.1738 12.9018 57.2657 12.7893 57.3886 12.7218C58.6657 12.0363 60.092 11.6757 61.5412 11.6719C64.1223 11.6719 65.9442 12.7319 65.9442 15.2958V22.3742C65.9442 22.518 65.8875 22.6558 65.7856 22.7574C65.6844 22.859 65.5462 22.9161 65.4028 22.9161H63.98C63.8366 22.9152 63.6997 22.8577 63.599 22.7562C63.4983 22.6547 63.4416 22.5174 63.4416 22.3742ZM63.2958 17.4802C62.7948 17.8562 61.8765 17.9815 60.898 18.1577C59.9188 18.3338 59.1093 18.5945 59.1093 19.658C59.1093 20.6368 59.8175 21.094 60.8371 21.094C62.1071 21.094 63.2958 20.3861 63.2958 18.7401V17.4802Z\" />\r\n              <path d=\"M68.2854 20.1116C68.3767 20.0042 68.5061 19.9367 68.6465 19.9234C68.787 19.9102 68.9269 19.9522 69.0369 20.0404C69.9412 20.7245 71.0503 21.0826 72.1834 21.0565C73.3522 21.0565 74.2161 20.6806 74.2161 19.7661C74.2161 18.933 73.423 18.7501 71.5062 18.4317C69.4741 18.093 67.5263 17.4157 67.5263 15.0991C67.5263 12.6843 69.6093 11.6648 71.9838 11.6648C73.4728 11.6423 74.9266 12.1193 76.1124 13.0195C76.1709 13.0646 76.2195 13.1211 76.2546 13.1856C76.2903 13.2501 76.3125 13.3212 76.3195 13.3946C76.3266 13.4679 76.3184 13.5419 76.2956 13.612C76.2727 13.682 76.2359 13.7467 76.1873 13.8019L75.5095 14.5978C75.42 14.7046 75.2924 14.7728 75.1537 14.7886C75.0155 14.8043 74.8763 14.7664 74.7645 14.6825C73.9375 14.074 72.9361 13.7486 71.9095 13.7545C70.8934 13.7545 70.036 14.0458 70.036 14.8349C70.036 15.7121 71.2657 15.8781 72.4953 16.0846C75.1168 16.5452 76.7831 17.2938 76.7831 19.4714C76.7831 21.7644 74.9506 23.18 72.0412 23.18C70.4504 23.2285 68.893 22.7193 67.6381 21.7406C67.5819 21.6948 67.5357 21.6382 67.5012 21.5742C67.4672 21.5102 67.4461 21.44 67.4397 21.3678C67.4333 21.2955 67.4409 21.2227 67.4631 21.1536C67.4848 21.0844 67.5199 21.0203 67.5673 20.965L68.2854 20.1116Z\" />\r\n              <path d=\"M78.3594 22.3732V8.58875C78.3594 8.44502 78.4162 8.30719 78.518 8.20559C78.6198 8.10394 78.7574 8.04688 78.9013 8.04688H80.4629C80.6063 8.04688 80.7444 8.10394 80.8456 8.20559C80.9475 8.30719 81.0042 8.44502 81.0042 8.58875V13.2931C81.3923 12.7737 81.8997 12.3552 82.4832 12.0729C83.0668 11.7906 83.7094 11.6526 84.3579 11.6708C86.6709 11.6708 88.0223 13.3168 88.0223 15.6672V22.3732C88.0223 22.5168 87.9655 22.6547 87.8637 22.7563C87.7619 22.8579 87.6243 22.915 87.4804 22.915H85.9188C85.7754 22.915 85.6373 22.8579 85.5361 22.7563C85.4342 22.6547 85.3774 22.5168 85.3774 22.3732V16.2769C85.3774 15.1084 85.0046 14.0483 83.504 14.0483C82.0882 14.0483 81.0042 15.0474 81.0042 16.7137V22.3732C81.0042 22.5168 80.9475 22.6547 80.8456 22.7563C80.7444 22.8579 80.6063 22.915 80.4629 22.915H78.9013C78.7574 22.915 78.6198 22.8579 78.518 22.7563C78.4162 22.6547 78.3594 22.5168 78.3594 22.3732Z\" />\r\n              <path d=\"M105.217 22.5479L104.086 19.2322H98.409L97.2847 22.5479C97.2484 22.6557 97.1793 22.7493 97.0868 22.8156C96.9944 22.8819 96.8832 22.9173 96.7696 22.9171H94.9406C94.8534 22.9172 94.7668 22.896 94.6895 22.8555C94.6117 22.815 94.545 22.7562 94.4952 22.6843C94.4455 22.6124 94.4133 22.5295 94.4027 22.4427C94.3916 22.3558 94.4022 22.2676 94.4326 22.1856L99.5474 8.40121C99.5877 8.30026 99.658 8.21394 99.7487 8.15348C99.8394 8.09308 99.9465 8.06136 100.055 8.06253H102.571C102.68 8.06136 102.787 8.09308 102.878 8.15348C102.969 8.21394 103.039 8.30026 103.08 8.40121L108.177 22.189C108.207 22.2708 108.217 22.3586 108.206 22.445C108.194 22.5314 108.163 22.6138 108.113 22.6853C108.063 22.7568 107.996 22.8151 107.919 22.8555C107.842 22.8959 107.756 22.917 107.669 22.9171H105.732C105.618 22.9173 105.507 22.8819 105.415 22.8156C105.322 22.7493 105.253 22.6557 105.217 22.5479ZM101.282 10.4638L99.0967 16.9225H103.384L101.282 10.4638Z\" />\r\n              <path d=\"M112.082 12.464V13.3174C112.467 12.7842 112.977 12.3546 113.568 12.0673C114.16 11.78 114.813 11.644 115.469 11.6714C118.471 11.6714 120.408 14.1506 120.408 17.3986C120.408 20.6465 118.471 23.1562 115.469 23.1562C114.811 23.1811 114.157 23.0418 113.565 22.751C112.974 22.46 112.465 22.0267 112.082 21.4899V25.9977C112.082 26.1414 112.026 26.2793 111.924 26.3809C111.822 26.4825 111.684 26.5396 111.54 26.5396H109.979C109.835 26.5396 109.697 26.4825 109.596 26.3809C109.494 26.2793 109.438 26.1414 109.438 25.9977V12.4504C109.438 12.3067 109.494 12.1689 109.596 12.0672C109.697 11.9656 109.835 11.9085 109.979 11.9085H111.527C111.601 11.9067 111.673 11.9198 111.742 11.947C111.81 11.9743 111.872 12.0151 111.924 12.0671C111.976 12.119 112.017 12.181 112.044 12.2492C112.071 12.3175 112.084 12.3905 112.082 12.464ZM114.914 20.8531C116.746 20.8531 117.644 19.2918 117.644 17.3986C117.644 15.5054 116.746 13.9813 114.914 13.9813C113.081 13.9813 112.082 15.502 112.082 17.3986C112.082 19.2952 113.062 20.8531 114.914 20.8531Z\" />\r\n              <path d=\"M124.689 12.4637V13.3172C125.073 12.7846 125.584 12.3555 126.175 12.0683C126.766 11.7811 127.419 11.6447 128.076 11.6712C131.073 11.6712 133.01 14.1504 133.01 17.3984C133.01 20.6463 131.073 23.1559 128.076 23.1559C127.417 23.1799 126.763 23.0403 126.172 22.7495C125.581 22.4587 125.071 22.0258 124.689 21.4896V25.9974C124.689 26.1412 124.631 26.279 124.53 26.3807C124.428 26.4823 124.29 26.5394 124.147 26.5394H122.589C122.445 26.5394 122.307 26.4823 122.206 26.3807C122.104 26.279 122.047 26.1412 122.047 25.9974V12.4502C122.046 12.3789 122.06 12.3082 122.087 12.2423C122.114 12.1763 122.154 12.1164 122.204 12.066C122.255 12.0156 122.315 11.9757 122.38 11.9486C122.447 11.9215 122.517 11.9078 122.589 11.9083H124.147C124.219 11.9078 124.291 11.9219 124.358 11.9499C124.425 11.9778 124.485 12.0189 124.536 12.0707C124.586 12.1226 124.626 12.1841 124.652 12.2517C124.678 12.3192 124.691 12.3913 124.689 12.4637ZM127.523 20.8529C129.356 20.8529 130.233 19.2916 130.233 17.3984C130.233 15.5051 129.339 13.981 127.523 13.981C125.708 13.981 124.689 15.5017 124.689 17.3984C124.689 19.295 125.671 20.8529 127.523 20.8529Z\" />\r\n            </g>\r\n            <path\r\n              fillRule=\"evenodd\"\r\n              clipRule=\"evenodd\"\r\n              d=\"M26.0362 1.36815C24.4417 0.867188 22.9893 0.867188 20.0373 0.867188H11.1812C8.24598 0.867188 6.77676 0.867188 5.20932 1.35464C4.35365 1.66446 3.57651 2.15864 2.93294 2.80218C2.28943 3.44565 1.79528 4.22274 1.48543 5.07842C0.984375 6.66273 0.984375 8.12853 0.984375 11.0635V19.9194C0.984375 22.8713 0.984375 24.3236 1.47185 25.9045C1.7817 26.7602 2.27591 27.5373 2.91942 28.1808C3.56294 28.8243 4.34007 29.3184 5.19581 29.6283C6.78015 30.1293 8.24598 30.1293 11.1812 30.1293H20.0508C22.9893 30.1293 24.4552 30.1293 26.0362 29.6283C26.8919 29.3184 27.669 28.8243 28.3125 28.1808C28.9561 27.5373 29.4503 26.7602 29.7601 25.9045C30.2612 24.3202 30.2612 22.8544 30.2612 19.9194V11.0771C30.2612 8.13871 30.2612 6.67286 29.7601 5.09194C29.4503 4.23631 28.9561 3.45917 28.3125 2.8157C27.669 2.17216 26.8919 1.67804 26.0362 1.36815ZM20.2896 11.8979C19.1918 10.9688 17.8008 10.458 16.3626 10.4558C15.1777 10.4558 13.9928 10.8485 13.9928 11.9385C13.9928 12.9322 15.0477 13.321 16.3473 13.8C16.4733 13.8465 16.6017 13.8938 16.7316 13.9426C19.3011 14.8092 21.4169 15.8721 21.4169 18.3908C21.4169 21.1261 19.2943 22.9947 15.8209 23.208L15.5061 24.6738C15.4773 24.8067 15.4038 24.9256 15.2978 25.0106C15.1917 25.0957 15.0597 25.1417 14.9238 25.141H12.7402C12.652 25.1389 12.5653 25.1173 12.4863 25.078C12.4072 25.0387 12.3378 24.9825 12.2829 24.9134C12.228 24.8443 12.189 24.764 12.1686 24.6781C12.1481 24.5922 12.1468 24.5029 12.1647 24.4166L12.5032 22.8695C11.1922 22.5352 9.98689 21.8749 8.99935 20.95C8.94275 20.8954 8.89769 20.83 8.86696 20.7575C8.83617 20.6851 8.82031 20.6073 8.82031 20.5286C8.82031 20.4499 8.83617 20.3721 8.86696 20.2996C8.89769 20.2272 8.94275 20.1617 8.99935 20.1071L10.2113 18.9223C10.3215 18.8139 10.4698 18.7532 10.6243 18.7532C10.7788 18.7532 10.9272 18.8139 11.0373 18.9223C12.1476 19.9815 13.6299 20.5615 15.1641 20.537C16.7451 20.537 17.8183 19.8668 17.8183 18.8038C17.8183 17.8428 16.9383 17.5209 15.2686 16.9102C15.0914 16.8454 14.9053 16.7773 14.7105 16.7049C12.5642 15.9398 10.533 14.8464 10.533 12.3041C10.533 9.36229 12.9907 7.92699 15.8886 7.78477L16.1933 6.29187C16.2226 6.16025 16.2959 6.0425 16.4012 5.95817C16.5064 5.87384 16.6373 5.82795 16.7722 5.82813H18.949C19.0366 5.82784 19.1231 5.84709 19.2023 5.88443C19.2815 5.92182 19.3513 5.97637 19.4068 6.04414C19.4623 6.11191 19.5019 6.19115 19.5228 6.27619C19.5438 6.36123 19.5455 6.44983 19.5279 6.53563L19.1893 8.20457C20.2976 8.56683 21.3244 9.14178 22.2125 9.89721C22.2731 9.94923 22.3222 10.0133 22.3568 10.0852C22.3914 10.1572 22.4107 10.2355 22.4136 10.3154C22.4164 10.3952 22.4028 10.4747 22.3734 10.549C22.344 10.6232 22.2996 10.6906 22.243 10.7469L21.1123 11.8809C21.0032 11.9882 20.8573 12.0498 20.7043 12.0529C20.5513 12.0561 20.403 12.0006 20.2896 11.8979Z\"\r\n            />\r\n          </svg>\r\n        ),\r\n      },\r\n      {\r\n        id: 8,\r\n        name: 'Company 8',\r\n        logo: (\r\n          <svg\r\n            width=\"122\"\r\n            height=\"31\"\r\n            viewBox=\"0 0 122 31\"\r\n            fill=\"none\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n            className=\"dark:fill-white fill-black\"\r\n          >\r\n            <path\r\n              fillRule=\"evenodd\"\r\n              clipRule=\"evenodd\"\r\n              d=\"M4.65498 5.4192C4.49517 5.59338 4.50576 5.86062 4.67364 6.02715L25.4235 26.6055C25.5914 26.772 25.8609 26.7825 26.0365 26.624C29.079 23.8784 30.9888 19.9197 30.9888 15.5187C30.9888 7.22841 24.2123 0.507812 15.8529 0.507812C11.4152 0.507812 7.42352 2.40182 4.65498 5.4192ZM2.06157 9.32463C1.98489 9.4923 2.02355 9.68889 2.15481 9.81906L21.6 29.1035C21.7313 29.2337 21.9295 29.2721 22.0986 29.196C22.5478 28.994 22.9852 28.7708 23.4097 28.5277C23.662 28.3833 23.7007 28.0419 23.4946 27.8376L3.43132 7.94007C3.22533 7.73577 2.88109 7.77411 2.73544 8.02431C2.49037 8.44527 2.26532 8.87913 2.06157 9.32463ZM0.88189 14.8466C0.790844 14.7563 0.742401 14.6319 0.750973 14.5045C0.791115 13.9077 0.866419 13.3206 0.974847 12.745C1.03986 12.3998 1.46512 12.2801 1.71546 12.5283L18.8682 29.5393C19.1185 29.7875 18.9977 30.2093 18.6497 30.2738C18.0693 30.3813 17.4773 30.456 16.8756 30.4958C16.7471 30.5043 16.6216 30.4562 16.5306 30.3659L0.88189 14.8466ZM1.93451 19.0355C1.622 18.7256 1.11767 18.9926 1.23213 19.416C2.62655 24.5739 6.7222 28.6357 11.9231 30.0186C12.35 30.1321 12.6193 29.632 12.3068 29.322L1.93451 19.0355ZM61.7293 8.76831C62.8144 8.76831 63.6937 7.89291 63.6937 6.81306C63.6937 5.73321 62.8144 4.85781 61.7293 4.85781C60.6445 4.85781 59.7652 5.73321 59.7652 6.81306C59.7652 7.89291 60.6445 8.76831 61.7293 8.76831ZM44.9039 25.8202V4.85898H48.3948V22.7534H57.8071V25.8202H44.9039ZM69.7501 17.2669V25.8202H66.3724V10.9645H69.7077V13.5107L69.7501 13.4826C70.0892 12.6854 70.6358 12.0196 71.3896 11.485C72.1432 10.941 73.1042 10.669 74.2728 10.669C75.3091 10.669 76.2511 10.8988 77.0993 11.3584C77.9472 11.8085 78.6254 12.4697 79.1342 13.3419C79.643 14.2142 79.8974 15.2833 79.8974 16.5494V25.8202H76.5197V17.0137C76.5197 15.8882 76.2181 15.0348 75.6152 14.4533C75.0217 13.8625 74.2256 13.567 73.2267 13.567C72.586 13.567 72.0019 13.6983 71.4743 13.9609C70.9468 14.2235 70.5275 14.6268 70.2165 15.1708C69.9056 15.7147 69.7501 16.4134 69.7501 17.2669ZM100.334 25.567C101.107 25.8952 101.993 26.0594 102.991 26.0594C103.811 26.0594 104.513 25.9562 105.097 25.7498C105.681 25.5341 106.162 25.2481 106.539 24.8917C106.925 24.5353 107.231 24.1461 107.457 23.7241H107.514V25.8202H110.75V15.5928C110.75 14.8706 110.609 14.2095 110.326 13.6092C110.044 13.009 109.634 12.4885 109.097 12.0477C108.569 11.6069 107.928 11.2693 107.174 11.0348C106.421 10.7909 105.573 10.669 104.631 10.669C103.34 10.669 102.223 10.8894 101.281 11.3302C100.348 11.7617 99.6183 12.3431 99.0905 13.0747C98.5629 13.8062 98.2755 14.6315 98.2283 15.5506H101.493C101.531 15.1192 101.682 14.7347 101.945 14.397C102.209 14.0594 102.567 13.7968 103.02 13.6092C103.472 13.4123 103.995 13.3138 104.588 13.3138C105.182 13.3138 105.686 13.4123 106.101 13.6092C106.524 13.8062 106.85 14.0735 107.076 14.4111C107.302 14.7487 107.415 15.1426 107.415 15.5928V15.7054C107.415 16.043 107.297 16.2915 107.062 16.451C106.835 16.6104 106.449 16.7276 105.903 16.8026C105.366 16.8777 104.631 16.9668 103.698 17.0699C102.935 17.1543 102.2 17.281 101.493 17.4498C100.787 17.6186 100.155 17.8671 99.5993 18.1954C99.0529 18.5236 98.6195 18.9597 98.2991 19.5037C97.9788 20.0477 97.8188 20.7464 97.8188 21.5998C97.8188 22.5846 98.0447 23.4099 98.497 24.0758C98.9492 24.7323 99.5618 25.2293 100.334 25.567ZM105.804 23.0769C105.257 23.3677 104.584 23.513 103.783 23.513C102.972 23.513 102.327 23.3442 101.847 23.0066C101.366 22.6596 101.126 22.186 101.126 21.5858C101.126 21.1168 101.258 20.737 101.521 20.4462C101.795 20.1555 102.153 19.9257 102.596 19.7569C103.038 19.5881 103.519 19.4709 104.037 19.4052C104.414 19.349 104.781 19.2927 105.14 19.2364C105.497 19.1708 105.832 19.1098 106.143 19.0535C106.454 18.9879 106.718 18.9222 106.934 18.8566C107.161 18.7909 107.325 18.7206 107.429 18.6455V20.3056C107.429 20.887 107.292 21.4216 107.019 21.9093C106.755 22.3876 106.35 22.7768 105.804 23.0769ZM113.697 25.8202V10.9645H116.948V13.4123H116.99C117.263 12.5682 117.692 11.9258 118.276 11.485C118.87 11.0348 119.647 10.8097 120.608 10.8097C120.844 10.8097 121.056 10.8191 121.244 10.8379C121.442 10.8472 121.607 10.8566 121.739 10.866V13.8906C121.616 13.8718 121.4 13.8484 121.089 13.8203C120.778 13.7921 120.448 13.778 120.099 13.778C119.543 13.778 119.035 13.9046 118.573 14.1579C118.111 14.4111 117.744 14.8003 117.471 15.3255C117.207 15.8414 117.075 16.4932 117.075 17.281V25.8202H113.697ZM60.0335 25.8202V10.9645H63.4112V25.8202H60.0335ZM85.5155 25.159C86.599 25.8249 87.8853 26.1578 89.3739 26.1578C90.5234 26.1578 91.5691 25.9515 92.5114 25.5388C93.4631 25.1168 94.2544 24.54 94.8857 23.8085C95.517 23.0676 95.9269 22.2141 96.1154 21.2481H92.9355C92.7939 21.6889 92.5631 22.0781 92.2428 22.4157C91.9321 22.744 91.541 23.0019 91.07 23.1895C90.5987 23.3771 90.0524 23.4708 89.4304 23.4708C88.5919 23.4708 87.871 23.2833 87.2682 22.9081C86.6747 22.533 86.2224 22.0125 85.9114 21.3466C85.6292 20.7337 85.4749 20.0397 85.4489 19.2645H96.3132V18.3642C96.3132 17.22 96.1435 16.179 95.8044 15.2411C95.4653 14.2939 94.9846 13.4779 94.3627 12.7933C93.741 12.0993 92.9921 11.5647 92.1157 11.1896C91.2488 10.8144 90.2832 10.6268 89.2184 10.6268C87.8335 10.6268 86.6087 10.9598 85.5439 11.6257C84.4791 12.2915 83.6454 13.2107 83.0422 14.383C82.4394 15.5553 82.1378 16.8964 82.1378 18.4064C82.1378 19.907 82.43 21.2434 83.0141 22.4157C83.5982 23.5787 84.4322 24.4931 85.5155 25.159ZM92.6245 15.3255C92.3135 14.6878 91.8707 14.1954 91.2959 13.8484C90.7212 13.5014 90.043 13.3279 89.2607 13.3279C88.4881 13.3279 87.8145 13.5014 87.2397 13.8484C86.6746 14.1954 86.2318 14.6878 85.9114 15.3255C85.6828 15.7874 85.5382 16.3126 85.4777 16.9011H93.058C92.9975 16.3126 92.8529 15.7874 92.6245 15.3255Z\"\r\n            />\r\n          </svg>\r\n        ),\r\n      },\r\n    ],\r\n  },\r\n  featureSection: {\r\n    title: 'How Kortix Suna Works',\r\n    description:\r\n      'Discover how Kortix Suna transforms your commands into action in four easy steps',\r\n    items: [\r\n      {\r\n        id: 1,\r\n        title: 'Request an Action',\r\n        content:\r\n          'Speak or type your command—let Kortix Suna capture your intent. Your request instantly sets the process in motion.',\r\n        image:\r\n          'https://images.unsplash.com/photo-1720371300677-ba4838fa0678?q=80&w=2070&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D',\r\n      },\r\n      {\r\n        id: 2,\r\n        title: 'AI Understanding & Planning',\r\n        content:\r\n          'Suna analyzes your request, understands the context, and develops a structured plan to complete the task efficiently.',\r\n        image:\r\n          'https://images.unsplash.com/photo-1686170287433-c95faf6d3608?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxmZWF0dXJlZC1waG90b3MtZmVlZHwzfHx8ZW58MHx8fHx8fA%3D%3D',\r\n      },\r\n      {\r\n        id: 3,\r\n        title: 'Autonomous Execution',\r\n        content:\r\n          'Using its capabilities and integrations, Suna executes the task independently, handling any complexities along the way.',\r\n        image:\r\n          'https://images.unsplash.com/photo-1720378042271-60aff1e1c538?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxmZWF0dXJlZC1waG90b3MtZmVlZHwxMHx8fGVufDB8fHx8fA%3D%3D',\r\n      },\r\n      {\r\n        id: 4,\r\n        title: 'Results & Learning',\r\n        content:\r\n          'Suna delivers results and learns from each interaction, continuously improving its performance to better serve your needs.',\r\n        image:\r\n          'https://images.unsplash.com/photo-1666882990322-e7f3b8df4f75?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1yZWxhdGVkfDF8fHxlbnwwfHx8fHw%3D',\r\n      },\r\n    ],\r\n  },\r\n  bentoSection: {\r\n    title: 'Empower Your Workflow with Kortix Suna',\r\n    description:\r\n      'Let Kortix Suna act on your behalf with advanced AI capabilities, seamless integrations, and autonomous task execution.',\r\n    items: [\r\n      {\r\n        id: 1,\r\n        content: <FirstBentoAnimation />,\r\n        title: 'Autonomous Task Execution',\r\n        description:\r\n          'Experience true automation with Suna. Ask your AI Agent to complete tasks, research information, and handle complex workflows with minimal supervision.',\r\n      },\r\n      {\r\n        id: 2,\r\n        content: <SecondBentoAnimation />,\r\n        title: 'Seamless Integrations',\r\n        description:\r\n          'Connect Suna to your existing tools for a unified workflow. Boost productivity through AI-powered interconnected systems.',\r\n      },\r\n      {\r\n        id: 3,\r\n        content: (\r\n          <ThirdBentoAnimation\r\n            data={[20, 30, 25, 45, 40, 55, 75]}\r\n            toolTipValues={[\r\n              1234, 1678, 2101, 2534, 2967, 3400, 3833, 4266, 4700, 5133,\r\n            ]}\r\n          />\r\n        ),\r\n        title: 'Intelligent Data Analysis',\r\n        description:\r\n          \"Transform raw data into actionable insights in seconds. Make better decisions with Suna's real-time, adaptive intelligence.\",\r\n      },\r\n      {\r\n        id: 4,\r\n        content: <FourthBentoAnimation once={false} />,\r\n        title: 'Complete Customization',\r\n        description:\r\n          'Tailor Suna to your specific needs. As an open source solution, you have full control over its capabilities, integrations, and implementation.',\r\n      },\r\n    ],\r\n  },\r\n  benefits: [\r\n    {\r\n      id: 1,\r\n      text: \"Automate everyday tasks with Suna's powerful AI capabilities.\",\r\n      image: '/Device-6.png',\r\n    },\r\n    {\r\n      id: 2,\r\n      text: 'Increase productivity with autonomous task completion.',\r\n      image: '/Device-7.png',\r\n    },\r\n    {\r\n      id: 3,\r\n      text: 'Improve focus on high-value work as Suna handles the routine.',\r\n      image: '/Device-8.png',\r\n    },\r\n    {\r\n      id: 4,\r\n      text: 'Access cutting-edge AI as an open source, transparent solution.',\r\n      image: '/Device-1.png',\r\n    },\r\n  ],\r\n  growthSection: {\r\n    title: 'Open Source & Secure',\r\n    description:\r\n      'Where advanced security meets complete transparency—designed to protect your data while providing full access to the code.',\r\n    items: [\r\n      {\r\n        id: 1,\r\n        content: (\r\n          <div\r\n            className=\"relative flex size-full items-center justify-center overflow-hidden transition-all duration-300 hover:[mask-image:none] hover:[webkit-mask-image:none]\"\r\n            style={{\r\n              WebkitMaskImage: `url(\"data:image/svg+xml,%3Csvg width='265' height='268' viewBox='0 0 265 268' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fillRule='evenodd' clipRule='evenodd' d='M121.384 4.5393C124.406 1.99342 128.319 0.585938 132.374 0.585938C136.429 0.585938 140.342 1.99342 143.365 4.5393C173.074 29.6304 210.174 45.6338 249.754 50.4314C253.64 50.9018 257.221 52.6601 259.855 55.3912C262.489 58.1223 264.005 61.6477 264.13 65.3354C265.616 106.338 254.748 146.9 232.782 182.329C210.816 217.759 178.649 246.61 140.002 265.547C137.645 266.701 135.028 267.301 132.371 267.298C129.715 267.294 127.1 266.686 124.747 265.526C86.0991 246.59 53.9325 217.739 31.9665 182.309C10.0005 146.879 -0.867679 106.317 0.618784 65.3147C0.748654 61.6306 2.26627 58.1102 4.9001 55.3833C7.53394 52.6565 11.1121 50.9012 14.9945 50.4314C54.572 45.6396 91.6716 29.6435 121.384 4.56V4.5393Z' fill='black'/%3E%3C/svg%3E\")`,\r\n              maskImage: `url(\"data:image/svg+xml,%3Csvg width='265' height='268' viewBox='0 0 265 268' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fillRule='evenodd' clipRule='evenodd' d='M121.384 4.5393C124.406 1.99342 128.319 0.585938 132.374 0.585938C136.429 0.585938 140.342 1.99342 143.365 4.5393C173.074 29.6304 210.174 45.6338 249.754 50.4314C253.64 50.9018 257.221 52.6601 259.855 55.3912C262.489 58.1223 264.005 61.6477 264.13 65.3354C265.616 106.338 254.748 146.9 232.782 182.329C210.816 217.759 178.649 246.61 140.002 265.547C137.645 266.701 135.028 267.301 132.371 267.298C129.715 267.294 127.1 266.686 124.747 265.526C86.0991 246.59 53.9325 217.739 31.9665 182.309C10.0005 146.879 -0.867679 106.317 0.618784 65.3147C0.748654 61.6306 2.26627 58.1102 4.9001 55.3833C7.53394 52.6565 11.1121 50.9012 14.9945 50.4314C54.572 45.6396 91.6716 29.6435 121.384 4.56V4.5393Z' fill='black'/%3E%3C/svg%3E\")`,\r\n              WebkitMaskSize: 'contain',\r\n              maskSize: 'contain',\r\n              WebkitMaskRepeat: 'no-repeat',\r\n              maskPosition: 'center',\r\n            }}\r\n          >\r\n            <div className=\"absolute top-[55%] md:top-[58%] left-[55%] md:left-[57%] -translate-x-1/2 -translate-y-1/2  size-full z-10\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"227\"\r\n                height=\"244\"\r\n                viewBox=\"0 0 227 244\"\r\n                fill=\"none\"\r\n                className=\"size-[90%] md:size-[85%] object-contain fill-background\"\r\n              >\r\n                <path\r\n                  fillRule=\"evenodd\"\r\n                  clipRule=\"evenodd\"\r\n                  d=\"M104.06 3.61671C106.656 1.28763 110.017 0 113.5 0C116.983 0 120.344 1.28763 122.94 3.61671C148.459 26.5711 180.325 41.2118 214.322 45.6008C217.66 46.0312 220.736 47.6398 222.999 50.1383C225.262 52.6369 226.563 55.862 226.67 59.2357C227.947 96.7468 218.612 133.854 199.744 166.267C180.877 198.68 153.248 225.074 120.052 242.398C118.028 243.454 115.779 244.003 113.498 244C111.216 243.997 108.969 243.441 106.948 242.379C73.7524 225.055 46.1231 198.661 27.2556 166.248C8.38807 133.835 -0.947042 96.7279 0.329744 59.2168C0.441295 55.8464 1.74484 52.6258 4.00715 50.1311C6.26946 47.6365 9.34293 46.0306 12.6777 45.6008C46.6725 41.2171 78.5389 26.5832 104.06 3.63565V3.61671Z\"\r\n                />\r\n              </svg>\r\n            </div>\r\n            <div className=\"absolute top-[58%] md:top-[60%] left-1/2 -translate-x-1/2 -translate-y-1/2  size-full z-20\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"245\"\r\n                height=\"282\"\r\n                viewBox=\"0 0 245 282\"\r\n                className=\"size-full object-contain fill-accent\"\r\n              >\r\n                <g filter=\"url(#filter0_dddd_2_33)\">\r\n                  <path\r\n                    fillRule=\"evenodd\"\r\n                    clipRule=\"evenodd\"\r\n                    d=\"M113.664 7.33065C116.025 5.21236 119.082 4.04126 122.25 4.04126C125.418 4.04126 128.475 5.21236 130.836 7.33065C154.045 28.2076 183.028 41.5233 213.948 45.5151C216.984 45.9065 219.781 47.3695 221.839 49.6419C223.897 51.9144 225.081 54.8476 225.178 57.916C226.339 92.0322 217.849 125.781 200.689 155.261C183.529 184.74 158.4 208.746 128.209 224.501C126.368 225.462 124.323 225.962 122.248 225.959C120.173 225.956 118.13 225.45 116.291 224.484C86.0997 208.728 60.971 184.723 43.811 155.244C26.6511 125.764 18.1608 92.015 19.322 57.8988C19.4235 54.8334 20.6091 51.9043 22.6666 49.6354C24.7242 47.3665 27.5195 45.906 30.5524 45.5151C61.4706 41.5281 90.4531 28.2186 113.664 7.34787V7.33065Z\"\r\n                  />\r\n                </g>\r\n                <defs>\r\n                  <filter\r\n                    id=\"filter0_dddd_2_33\"\r\n                    x=\"0.217041\"\r\n                    y=\"0.0412598\"\r\n                    width=\"244.066\"\r\n                    height=\"292.917\"\r\n                    filterUnits=\"userSpaceOnUse\"\r\n                    colorInterpolationFilters=\"sRGB\"\r\n                  >\r\n                    <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\r\n                    <feColorMatrix\r\n                      in=\"SourceAlpha\"\r\n                      type=\"matrix\"\r\n                      values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n                      result=\"hardAlpha\"\r\n                    />\r\n                    <feOffset dy=\"3\" />\r\n                    <feGaussianBlur stdDeviation=\"3.5\" />\r\n                    <feColorMatrix\r\n                      type=\"matrix\"\r\n                      values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0\"\r\n                    />\r\n                    <feBlend\r\n                      mode=\"normal\"\r\n                      in2=\"BackgroundImageFix\"\r\n                      result=\"effect1_dropShadow_2_33\"\r\n                    />\r\n                    <feColorMatrix\r\n                      in=\"SourceAlpha\"\r\n                      type=\"matrix\"\r\n                      values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n                      result=\"hardAlpha\"\r\n                    />\r\n                    <feOffset dy=\"12\" />\r\n                    <feGaussianBlur stdDeviation=\"6\" />\r\n                    <feColorMatrix\r\n                      type=\"matrix\"\r\n                      values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0\"\r\n                    />\r\n                    <feBlend\r\n                      mode=\"normal\"\r\n                      in2=\"effect1_dropShadow_2_33\"\r\n                      result=\"effect2_dropShadow_2_33\"\r\n                    />\r\n                    <feColorMatrix\r\n                      in=\"SourceAlpha\"\r\n                      type=\"matrix\"\r\n                      values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n                      result=\"hardAlpha\"\r\n                    />\r\n                    <feOffset dy=\"27\" />\r\n                    <feGaussianBlur stdDeviation=\"8\" />\r\n                    <feColorMatrix\r\n                      type=\"matrix\"\r\n                      values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.02 0\"\r\n                    />\r\n                    <feBlend\r\n                      mode=\"normal\"\r\n                      in2=\"effect2_dropShadow_2_33\"\r\n                      result=\"effect3_dropShadow_2_33\"\r\n                    />\r\n                    <feColorMatrix\r\n                      in=\"SourceAlpha\"\r\n                      type=\"matrix\"\r\n                      values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n                      result=\"hardAlpha\"\r\n                    />\r\n                    <feOffset dy=\"48\" />\r\n                    <feGaussianBlur stdDeviation=\"9.5\" />\r\n                    <feColorMatrix\r\n                      type=\"matrix\"\r\n                      values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.01 0\"\r\n                    />\r\n                    <feBlend\r\n                      mode=\"normal\"\r\n                      in2=\"effect3_dropShadow_2_33\"\r\n                      result=\"effect4_dropShadow_2_33\"\r\n                    />\r\n                    <feBlend\r\n                      mode=\"normal\"\r\n                      in=\"SourceGraphic\"\r\n                      in2=\"effect4_dropShadow_2_33\"\r\n                      result=\"shape\"\r\n                    />\r\n                  </filter>\r\n                </defs>\r\n              </svg>\r\n            </div>\r\n            <div className=\"absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-30\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"81\"\r\n                height=\"80\"\r\n                viewBox=\"0 0 81 80\"\r\n                className=\"fill-background\"\r\n              >\r\n                <g filter=\"url(#filter0_iiii_2_34)\">\r\n                  <path\r\n                    fillRule=\"evenodd\"\r\n                    clipRule=\"evenodd\"\r\n                    d=\"M20.5 36V28C20.5 22.6957 22.6071 17.6086 26.3579 13.8579C30.1086 10.1071 35.1957 8 40.5 8C45.8043 8 50.8914 10.1071 54.6421 13.8579C58.3929 17.6086 60.5 22.6957 60.5 28V36C62.6217 36 64.6566 36.8429 66.1569 38.3431C67.6571 39.8434 68.5 41.8783 68.5 44V64C68.5 66.1217 67.6571 68.1566 66.1569 69.6569C64.6566 71.1571 62.6217 72 60.5 72H20.5C18.3783 72 16.3434 71.1571 14.8431 69.6569C13.3429 68.1566 12.5 66.1217 12.5 64V44C12.5 41.8783 13.3429 39.8434 14.8431 38.3431C16.3434 36.8429 18.3783 36 20.5 36ZM52.5 28V36H28.5V28C28.5 24.8174 29.7643 21.7652 32.0147 19.5147C34.2652 17.2643 37.3174 16 40.5 16C43.6826 16 46.7348 17.2643 48.9853 19.5147C51.2357 21.7652 52.5 24.8174 52.5 28Z\"\r\n                  />\r\n                </g>\r\n                <defs>\r\n                  <filter\r\n                    id=\"filter0_iiii_2_34\"\r\n                    x=\"12.5\"\r\n                    y=\"8\"\r\n                    width=\"56\"\r\n                    height=\"70\"\r\n                    filterUnits=\"userSpaceOnUse\"\r\n                    colorInterpolationFilters=\"sRGB\"\r\n                  >\r\n                    <feFlood floodOpacity=\"0\" result=\"BackgroundImageFix\" />\r\n                    <feBlend\r\n                      mode=\"normal\"\r\n                      in=\"SourceGraphic\"\r\n                      in2=\"BackgroundImageFix\"\r\n                      result=\"shape\"\r\n                    />\r\n                    <feColorMatrix\r\n                      in=\"SourceAlpha\"\r\n                      type=\"matrix\"\r\n                      values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n                      result=\"hardAlpha\"\r\n                    />\r\n                    <feOffset dy=\"1\" />\r\n                    <feGaussianBlur stdDeviation=\"1\" />\r\n                    <feComposite\r\n                      in2=\"hardAlpha\"\r\n                      operator=\"arithmetic\"\r\n                      k2=\"-1\"\r\n                      k3=\"1\"\r\n                    />\r\n                    <feColorMatrix\r\n                      type=\"matrix\"\r\n                      values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.1 0\"\r\n                    />\r\n                    <feBlend\r\n                      mode=\"normal\"\r\n                      in2=\"shape\"\r\n                      result=\"effect1_innerShadow_2_34\"\r\n                    />\r\n                    <feColorMatrix\r\n                      in=\"SourceAlpha\"\r\n                      type=\"matrix\"\r\n                      values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n                      result=\"hardAlpha\"\r\n                    />\r\n                    <feOffset dy=\"3\" />\r\n                    <feGaussianBlur stdDeviation=\"1.5\" />\r\n                    <feComposite\r\n                      in2=\"hardAlpha\"\r\n                      operator=\"arithmetic\"\r\n                      k2=\"-1\"\r\n                      k3=\"1\"\r\n                    />\r\n                    <feColorMatrix\r\n                      type=\"matrix\"\r\n                      values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.09 0\"\r\n                    />\r\n                    <feBlend\r\n                      mode=\"normal\"\r\n                      in2=\"effect1_innerShadow_2_34\"\r\n                      result=\"effect2_innerShadow_2_34\"\r\n                    />\r\n                    <feColorMatrix\r\n                      in=\"SourceAlpha\"\r\n                      type=\"matrix\"\r\n                      values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n                      result=\"hardAlpha\"\r\n                    />\r\n                    <feOffset dy=\"8\" />\r\n                    <feGaussianBlur stdDeviation=\"2.5\" />\r\n                    <feComposite\r\n                      in2=\"hardAlpha\"\r\n                      operator=\"arithmetic\"\r\n                      k2=\"-1\"\r\n                      k3=\"1\"\r\n                    />\r\n                    <feColorMatrix\r\n                      type=\"matrix\"\r\n                      values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.05 0\"\r\n                    />\r\n                    <feBlend\r\n                      mode=\"normal\"\r\n                      in2=\"effect2_innerShadow_2_34\"\r\n                      result=\"effect3_innerShadow_2_34\"\r\n                    />\r\n                    <feColorMatrix\r\n                      in=\"SourceAlpha\"\r\n                      type=\"matrix\"\r\n                      values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0\"\r\n                      result=\"hardAlpha\"\r\n                    />\r\n                    <feOffset dy=\"14\" />\r\n                    <feGaussianBlur stdDeviation=\"3\" />\r\n                    <feComposite\r\n                      in2=\"hardAlpha\"\r\n                      operator=\"arithmetic\"\r\n                      k2=\"-1\"\r\n                      k3=\"1\"\r\n                    />\r\n                    <feColorMatrix\r\n                      type=\"matrix\"\r\n                      values=\"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.01 0\"\r\n                    />\r\n                    <feBlend\r\n                      mode=\"normal\"\r\n                      in2=\"effect3_innerShadow_2_34\"\r\n                      result=\"effect4_innerShadow_2_34\"\r\n                    />\r\n                  </filter>\r\n                </defs>\r\n              </svg>\r\n            </div>\r\n            <motion.div\r\n              initial={{ opacity: 0 }}\r\n              animate={{ opacity: 1 }}\r\n              transition={{ duration: 0.3, ease: 'easeOut' }}\r\n              className=\"size-full\"\r\n            >\r\n              <FlickeringGrid\r\n                className=\"size-full\"\r\n                gridGap={4}\r\n                squareSize={2}\r\n                maxOpacity={0.5}\r\n              />\r\n            </motion.div>\r\n          </div>\r\n        ),\r\n\r\n        title: 'Open Source Security',\r\n        description:\r\n          'Benefit from the security of open source code that thousands of eyes can review, audit, and improve.',\r\n      },\r\n      {\r\n        id: 2,\r\n        content: (\r\n          <div className=\"relative flex size-full max-w-lg items-center justify-center overflow-hidden [mask-image:linear-gradient(to_top,transparent,black_50%)] -translate-y-20\">\r\n            <Globe className=\"top-28\" />\r\n          </div>\r\n        ),\r\n\r\n        title: 'Community Powered',\r\n        description:\r\n          \"Join a thriving community of developers and users continuously enhancing and expanding Suna's capabilities.\",\r\n      },\r\n    ],\r\n  },\r\n  quoteSection: {\r\n    quote:\r\n      'Kortix Suna has transformed how we approach everyday tasks. The level of automation it provides, combined with its open source nature, makes it an invaluable tool for our entire organization.',\r\n    author: {\r\n      name: 'Alex Johnson',\r\n      role: 'CTO, Innovatech',\r\n      image: 'https://randomuser.me/api/portraits/men/91.jpg',\r\n    },\r\n  },\r\n  pricing: {\r\n    title: 'Open Source & Free Forever',\r\n    description:\r\n      'Kortix Suna is 100% open source and free to use. No hidden fees, no premium features locked behind paywalls.',\r\n    pricingItems: [\r\n      {\r\n        name: 'Community',\r\n        href: '#',\r\n        price: 'Free',\r\n        period: 'forever',\r\n        yearlyPrice: 'Free',\r\n        features: [\r\n          'Full agent capabilities',\r\n          'Unlimited usage',\r\n          'Full source code access',\r\n          'Community support',\r\n        ],\r\n        description: 'Perfect for individual users and developers',\r\n        buttonText: 'Hire Suna',\r\n        buttonColor: 'bg-accent text-primary',\r\n        isPopular: false,\r\n      },\r\n      {\r\n        name: 'Self-Hosted',\r\n        href: '#',\r\n        price: 'Free',\r\n        period: 'forever',\r\n        yearlyPrice: 'Free',\r\n        features: [\r\n          'Full agent capabilities',\r\n          'Unlimited usage',\r\n          'Full source code access',\r\n          'Custom deployment',\r\n          'Local data storage',\r\n          'Integration with your tools',\r\n          'Full customization',\r\n          'Community support',\r\n        ],\r\n        description: 'Ideal for organizations with specific requirements',\r\n        buttonText: 'View Docs',\r\n        buttonColor: 'bg-secondary text-white',\r\n        isPopular: true,\r\n      },\r\n      {\r\n        name: 'Enterprise',\r\n        href: '#',\r\n        price: 'Custom',\r\n        period: '',\r\n        yearlyPrice: 'Custom',\r\n        features: [\r\n          'Everything in Self-Hosted',\r\n          'Priority support',\r\n          'Custom development',\r\n          'Dedicated hosting',\r\n          'SLA guarantees',\r\n        ],\r\n        description: 'For large teams needing custom implementations',\r\n        buttonText: 'Contact Us',\r\n        buttonColor: 'bg-primary text-primary-foreground',\r\n        isPopular: false,\r\n      },\r\n    ],\r\n  },\r\n  testimonials: [\r\n    {\r\n      id: '1',\r\n      name: 'Alex Rivera',\r\n      role: 'CTO at InnovateTech',\r\n      img: 'https://randomuser.me/api/portraits/men/91.jpg',\r\n      description: (\r\n        <p>\r\n          The AI-driven analytics from #QuantumInsights have revolutionized our\r\n          product development cycle.\r\n          <Highlight>\r\n            Insights are now more accurate and faster than ever.\r\n          </Highlight>{' '}\r\n          A game-changer for tech companies.\r\n        </p>\r\n      ),\r\n    },\r\n    {\r\n      id: '2',\r\n      name: 'Samantha Lee',\r\n      role: 'Marketing Director at NextGen Solutions',\r\n      img: 'https://randomuser.me/api/portraits/women/12.jpg',\r\n      description: (\r\n        <p>\r\n          Implementing #AIStream&apos;s customer prediction model has\r\n          drastically improved our targeting strategy.\r\n          <Highlight>Seeing a 50% increase in conversion rates!</Highlight>{' '}\r\n          Highly recommend their solutions.\r\n        </p>\r\n      ),\r\n    },\r\n    {\r\n      id: '3',\r\n      name: 'Raj Patel',\r\n      role: 'Founder & CEO at StartUp Grid',\r\n      img: 'https://randomuser.me/api/portraits/men/45.jpg',\r\n      description: (\r\n        <p>\r\n          As a startup, we need to move fast and stay ahead. #CodeAI&apos;s\r\n          automated coding assistant helps us do just that.\r\n          <Highlight>Our development speed has doubled.</Highlight> Essential\r\n          tool for any startup.\r\n        </p>\r\n      ),\r\n    },\r\n    {\r\n      id: '4',\r\n      name: 'Emily Chen',\r\n      role: 'Product Manager at Digital Wave',\r\n      img: 'https://randomuser.me/api/portraits/women/83.jpg',\r\n      description: (\r\n        <p>\r\n          #VoiceGen&apos;s AI-driven voice synthesis has made creating global\r\n          products a breeze.\r\n          <Highlight>Localization is now seamless and efficient.</Highlight> A\r\n          must-have for global product teams.\r\n        </p>\r\n      ),\r\n    },\r\n    {\r\n      id: '5',\r\n      name: 'Michael Brown',\r\n      role: 'Data Scientist at FinTech Innovations',\r\n      img: 'https://randomuser.me/api/portraits/men/1.jpg',\r\n      description: (\r\n        <p>\r\n          Leveraging #DataCrunch&apos;s AI for our financial models has given us\r\n          an edge in predictive accuracy.\r\n          <Highlight>\r\n            Our investment strategies are now powered by real-time data\r\n            analytics.\r\n          </Highlight>{' '}\r\n          Transformative for the finance industry.\r\n        </p>\r\n      ),\r\n    },\r\n    {\r\n      id: '6',\r\n      name: 'Linda Wu',\r\n      role: 'VP of Operations at LogiChain Solutions',\r\n      img: 'https://randomuser.me/api/portraits/women/5.jpg',\r\n      description: (\r\n        <p>\r\n          #LogiTech&apos;s supply chain optimization tools have drastically\r\n          reduced our operational costs.\r\n          <Highlight>\r\n            Efficiency and accuracy in logistics have never been better.\r\n          </Highlight>{' '}\r\n        </p>\r\n      ),\r\n    },\r\n    {\r\n      id: '7',\r\n      name: 'Carlos Gomez',\r\n      role: 'Head of R&D at EcoInnovate',\r\n      img: 'https://randomuser.me/api/portraits/men/14.jpg',\r\n      description: (\r\n        <p>\r\n          By integrating #GreenTech&apos;s sustainable energy solutions,\r\n          we&apos;ve seen a significant reduction in carbon footprint.\r\n          <Highlight>\r\n            Leading the way in eco-friendly business practices.\r\n          </Highlight>{' '}\r\n          Pioneering change in the industry.\r\n        </p>\r\n      ),\r\n    },\r\n    {\r\n      id: '8',\r\n      name: 'Aisha Khan',\r\n      role: 'Chief Marketing Officer at Fashion Forward',\r\n      img: 'https://randomuser.me/api/portraits/women/56.jpg',\r\n      description: (\r\n        <p>\r\n          #TrendSetter&apos;s market analysis AI has transformed how we approach\r\n          fashion trends.\r\n          <Highlight>\r\n            Our campaigns are now data-driven with higher customer engagement.\r\n          </Highlight>{' '}\r\n          Revolutionizing fashion marketing.\r\n        </p>\r\n      ),\r\n    },\r\n    {\r\n      id: '9',\r\n      name: 'Tom Chen',\r\n      role: 'Director of IT at HealthTech Solutions',\r\n      img: 'https://randomuser.me/api/portraits/men/18.jpg',\r\n      description: (\r\n        <p>\r\n          Implementing #MediCareAI in our patient care systems has improved\r\n          patient outcomes significantly.\r\n          <Highlight>\r\n            Technology and healthcare working hand in hand for better health.\r\n          </Highlight>{' '}\r\n          A milestone in medical technology.\r\n        </p>\r\n      ),\r\n    },\r\n    {\r\n      id: '10',\r\n      name: 'Sofia Patel',\r\n      role: 'CEO at EduTech Innovations',\r\n      img: 'https://randomuser.me/api/portraits/women/73.jpg',\r\n      description: (\r\n        <p>\r\n          #LearnSmart&apos;s AI-driven personalized learning plans have doubled\r\n          student performance metrics.\r\n          <Highlight>\r\n            Education tailored to every learner&apos;s needs.\r\n          </Highlight>{' '}\r\n          Transforming the educational landscape.\r\n        </p>\r\n      ),\r\n    },\r\n    {\r\n      id: '11',\r\n      name: 'Jake Morrison',\r\n      role: 'CTO at SecureNet Tech',\r\n      img: 'https://randomuser.me/api/portraits/men/25.jpg',\r\n      description: (\r\n        <p>\r\n          With #CyberShield&apos;s AI-powered security systems, our data\r\n          protection levels are unmatched.\r\n          <Highlight>\r\n            Ensuring safety and trust in digital spaces.\r\n          </Highlight>{' '}\r\n          Redefining cybersecurity standards.\r\n        </p>\r\n      ),\r\n    },\r\n    {\r\n      id: '12',\r\n      name: 'Nadia Ali',\r\n      role: 'Product Manager at Creative Solutions',\r\n      img: 'https://randomuser.me/api/portraits/women/78.jpg',\r\n      description: (\r\n        <p>\r\n          #DesignPro&apos;s AI has streamlined our creative process, enhancing\r\n          productivity and innovation.\r\n          <Highlight>Bringing creativity and technology together.</Highlight> A\r\n          game-changer for creative industries.\r\n        </p>\r\n      ),\r\n    },\r\n    {\r\n      id: '13',\r\n      name: 'Omar Farooq',\r\n      role: 'Founder at Startup Hub',\r\n      img: 'https://randomuser.me/api/portraits/men/54.jpg',\r\n      description: (\r\n        <p>\r\n          #VentureAI&apos;s insights into startup ecosystems have been\r\n          invaluable for our growth and funding strategies.\r\n          <Highlight>\r\n            Empowering startups with data-driven decisions.\r\n          </Highlight>{' '}\r\n          A catalyst for startup success.\r\n        </p>\r\n      ),\r\n    },\r\n  ],\r\n  faqSection: {\r\n    title: 'Frequently Asked Questions',\r\n    description:\r\n      \"Answers to common questions about Kortix Suna and its capabilities. If you have any other questions, please don't hesitate to contact us.\",\r\n    faQitems: [\r\n      {\r\n        id: 1,\r\n        question: 'What is an AI Agent?',\r\n        answer:\r\n          'An AI Agent is an intelligent software program that can perform tasks autonomously, learn from interactions, and make decisions to help achieve specific goals. It combines artificial intelligence and machine learning to provide personalized assistance and automation.',\r\n      },\r\n      {\r\n        id: 2,\r\n        question: 'How does Kortix Suna work?',\r\n        answer:\r\n          'Kortix Suna works by analyzing your requirements, leveraging advanced AI algorithms to understand context, and executing tasks based on your instructions. It can integrate with your workflow, learn from feedback, and continuously improve its performance.',\r\n      },\r\n      {\r\n        id: 3,\r\n        question: 'Is Kortix Suna really free?',\r\n        answer:\r\n          'Yes, Kortix Suna is completely free and open source. We believe in democratizing AI technology and making it accessible to everyone. You can use it, modify it, and contribute to its development without any cost.',\r\n      },\r\n      {\r\n        id: 4,\r\n        question: 'Can I integrate Suna with my existing tools?',\r\n        answer:\r\n          'Yes, Kortix Suna is designed to be highly compatible with popular tools and platforms. We offer APIs and pre-built integrations for seamless connection with your existing workflow tools and systems.',\r\n      },\r\n      {\r\n        id: 5,\r\n        question: 'How can I contribute to Kortix Suna?',\r\n        answer:\r\n          'You can contribute to Kortix Suna by submitting pull requests on GitHub, reporting bugs, suggesting new features, or helping with documentation. Join our Discord community to connect with other contributors and Hire Suna.',\r\n      },\r\n      {\r\n        id: 6,\r\n        question: 'How does Kortix Suna save me time?',\r\n        answer:\r\n          'Kortix Suna automates repetitive tasks, streamlines workflows, and provides quick solutions to common challenges. This automation and efficiency can save hours of manual work, allowing you to focus on more strategic activities.',\r\n      },\r\n    ],\r\n  },\r\n  ctaSection: {\r\n    id: 'cta',\r\n    title: 'Start Using Kortix Suna Today',\r\n    backgroundImage: '/holo.png',\r\n    button: {\r\n      text: 'Get Started for free',\r\n      href: '/auth',\r\n    },\r\n    subtext: 'The generalist AI Agent that acts on your behalf',\r\n  },\r\n  footerLinks: [\r\n    {\r\n      title: 'Kortix',\r\n      links: [\r\n        { id: 1, title: 'About', url: 'https://kortix.ai' },\r\n        { id: 3, title: 'Contact', url: 'mailto:<EMAIL>' },\r\n        { id: 4, title: 'Careers', url: 'https://kortix.ai/careers' },\r\n      ],\r\n    },\r\n    {\r\n      title: 'Resources',\r\n      links: [\r\n        {\r\n          id: 5,\r\n          title: 'Documentation',\r\n          url: 'https://github.com/Kortix-ai/Suna',\r\n        },\r\n        { id: 7, title: 'Discord', url: 'https://discord.gg/Py6pCBUUPw' },\r\n        { id: 8, title: 'GitHub', url: 'https://github.com/Kortix-ai/Suna' },\r\n      ],\r\n    },\r\n    {\r\n      title: 'Legal',\r\n      links: [\r\n        {\r\n          id: 9,\r\n          title: 'Privacy Policy',\r\n          url: 'https://suna.so/legal?tab=privacy',\r\n        },\r\n        {\r\n          id: 10,\r\n          title: 'Terms of Service',\r\n          url: 'https://suna.so/legal?tab=terms',\r\n        },\r\n        {\r\n          id: 11,\r\n          title: 'License Apache 2.0',\r\n          url: 'https://github.com/Kortix-ai/Suna/blob/main/LICENSE',\r\n        },\r\n      ],\r\n    },\r\n  ],\r\n  useCases: [\r\n    {\r\n      id: 'competitor-analysis',\r\n      title: 'Competitor Analysis',\r\n      description:\r\n        'Analyze the market for my next company in the healthcare industry, located in the UK. Give me the major players, their market size, strengths, and weaknesses, and add their website URLs. Once done, generate a PDF report.',\r\n      category: 'research',\r\n      featured: true,\r\n      icon: (\r\n        <svg\r\n          width=\"24\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 24 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M7.75 19.25H16.25C17.3546 19.25 18.25 18.3546 18.25 17.25V8.75L13.75 4.25H7.75C6.64543 4.25 5.75 5.14543 5.75 6.25V17.25C5.75 18.3546 6.64543 19.25 7.75 19.25Z\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M18 9L14 9C13.4477 9 13 8.55228 13 8L13 4\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M9.5 14.5L11 13L12.5 14.5L14.5 12.5\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n        </svg>\r\n      ),\r\n      image:\r\n        'https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',\r\n      url: 'https://suna.so/share/2fbf0552-87d6-4d12-be25-d54f435bc493',\r\n    },\r\n    {\r\n      id: 'vc-list',\r\n      title: 'VC List',\r\n      description:\r\n        'Give me the list of the most important VC Funds in the United States based on Assets Under Management. Give me website URLs, and if possible an email to reach them out.',\r\n      category: 'finance',\r\n      featured: true,\r\n      icon: (\r\n        <svg\r\n          width=\"24\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 24 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M12 4.75L19.25 9L12 13.25L4.75 9L12 4.75Z\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M9.25 11.5L4.75 14L12 18.25L19.25 14L14.6722 11.5\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n        </svg>\r\n      ),\r\n      image:\r\n        'https://images.unsplash.com/photo-1444653614773-995cb1ef9efa?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',\r\n      url: 'https://suna.so/share/a172382b-aa77-42a2-a3e1-46f32a0f9c37',\r\n    },\r\n    {\r\n      id: 'candidate-search',\r\n      title: 'Looking for Candidates',\r\n      description:\r\n        \"Go on LinkedIn, and find 10 profiles available - they are not working right now - for a junior software engineer position, who are located in Munich, Germany. They should have at least one bachelor's degree in Computer Science or anything related to it, and 1-year of experience in any field/role.\",\r\n      category: 'recruitment',\r\n      featured: true,\r\n      icon: (\r\n        <svg\r\n          width=\"24\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 24 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M17.25 10C17.25 12.8995 14.8995 15.25 12 15.25C9.10051 15.25 6.75 12.8995 6.75 10C6.75 7.10051 9.10051 4.75 12 4.75C14.8995 4.75 17.25 7.10051 17.25 10Z\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M8.25 14.75L5.25 19.25\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M15.75 14.75L18.75 19.25\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n        </svg>\r\n      ),\r\n      image:\r\n        'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',\r\n      url: 'https://suna.so/share/d9e39c94-4f6f-4b5a-b1a0-b681bfe0dee8',\r\n    },\r\n    {\r\n      id: 'company-trip',\r\n      title: 'Planning Company Trip',\r\n      description:\r\n        \"Generate a route plan for my company. We should go to California. We'll be 8 people. Compose the trip from the departure (Paris, France) to the activities we can do considering that the trip will be 7 days long - departure on the 21st of Jun 2025.\",\r\n      category: 'travel',\r\n      featured: true,\r\n      icon: (\r\n        <svg\r\n          width=\"24\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 24 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M4.75 8.75C4.75 7.64543 5.64543 6.75 6.75 6.75H17.25C18.3546 6.75 19.25 7.64543 19.25 8.75V17.25C19.25 18.3546 18.3546 19.25 17.25 19.25H6.75C5.64543 19.25 4.75 18.3546 4.75 17.25V8.75Z\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M8 4.75V8.25\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M16 4.75V8.25\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M7.75 10.75H16.25\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n        </svg>\r\n      ),\r\n      image:\r\n        'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',\r\n      url: 'https://suna.so/share/23f7d904-eb66-4a9c-9247-b9704ddfd233',\r\n    },\r\n    {\r\n      id: 'excel-spreadsheet',\r\n      title: 'Working on Excel',\r\n      description:\r\n        'My company asked to set up an Excel spreadsheet with all the information about Italian lottery games (Lotto, 10eLotto, and Million Day). Based on that, generate and send me a spreadsheet with all the basic information (public ones).',\r\n      category: 'data',\r\n      featured: true,\r\n      icon: (\r\n        <svg\r\n          width=\"24\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 24 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M4.75 6.75C4.75 5.64543 5.64543 4.75 6.75 4.75H17.25C18.3546 4.75 19.25 5.64543 19.25 6.75V17.25C19.25 18.3546 18.3546 19.25 17.25 19.25H6.75C5.64543 19.25 4.75 18.3546 4.75 17.25V6.75Z\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M9.75 8.75V19\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M5 8.25H19\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n        </svg>\r\n      ),\r\n      image:\r\n        'https://images.unsplash.com/photo-1532153975070-2e9ab71f1b14?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',\r\n      url: 'https://suna.so/share/2a147a3a-3778-4624-8285-42474c8c1c9c',\r\n    },\r\n    {\r\n      id: 'speaker-prospecting',\r\n      title: 'Automate Event Speaker Prospecting',\r\n      description:\r\n        \"Find 20 AI ethics speakers from Europe who've spoken at conferences in the past year. Scrapes conference sites, cross-references LinkedIn and YouTube, and outputs contact info + talk summaries.\",\r\n      category: 'research',\r\n      featured: true,\r\n      icon: (\r\n        <svg\r\n          width=\"24\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 24 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M5.75 19.2502H18.25C18.8023 19.2502 19.25 18.8025 19.25 18.2502V5.75C19.25 5.19772 18.8023 4.75 18.25 4.75H5.75C5.19772 4.75 4.75 5.19772 4.75 5.75V18.2502C4.75 18.8025 5.19772 19.2502 5.75 19.2502Z\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M9.75 8.75C9.75 9.44036 9.19036 10 8.5 10C7.80964 10 7.25 9.44036 7.25 8.75C7.25 8.05964 7.80964 7.5 8.5 7.5C9.19036 7.5 9.75 8.05964 9.75 8.75Z\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M19.25 13.75L14.75 9.25L7.25 16.75\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n        </svg>\r\n      ),\r\n      image:\r\n        'https://images.unsplash.com/photo-1523580494863-6f3031224c94?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',\r\n      url: 'https://suna.so/share/6830cc6d-3fbd-492a-93f8-510a5f48ce50',\r\n    },\r\n    {\r\n      id: 'scientific-papers',\r\n      title: 'Summarize and Cross-Reference Scientific Papers',\r\n      description:\r\n        'Research and compare scientific papers talking about Alcohol effects on our bodies during the last 5 years. Generate a report about the most important scientific papers talking about the topic I wrote before.',\r\n      category: 'research',\r\n      featured: true,\r\n      icon: (\r\n        <svg\r\n          width=\"24\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 24 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M4.75 6.75C4.75 5.64543 5.64543 4.75 6.75 4.75H17.25C18.3546 4.75 19.25 5.64543 19.25 6.75V17.25C19.25 18.3546 18.3546 19.25 17.25 19.25H6.75C5.64543 19.25 4.75 18.3546 4.75 17.25V6.75Z\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M9.75 8.75V19\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M5 8.25H19\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n        </svg>\r\n      ),\r\n      image:\r\n        'https://images.unsplash.com/photo-1532153975070-2e9ab71f1b14?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',\r\n      url: 'https://suna.so/share/a106ef9f-ed97-46ee-8e51-7bfaf2ac3c29',\r\n    },\r\n    {\r\n      id: 'lead-generation',\r\n      title: 'Research + First Contact Draft',\r\n      description:\r\n        'Research my potential customers (B2B) on LinkedIn. They should be in the clean tech industry. Find their websites and their email addresses. After that, based on the company profile, generate a personalized first contact email.',\r\n      category: 'sales',\r\n      featured: true,\r\n      icon: (\r\n        <svg\r\n          width=\"24\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 24 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M4.75 11.75L10.25 6.25L14.75 10.75L19.25 6.25\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M5.75 19.25H18.25\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M12 11.25V19.25\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n        </svg>\r\n      ),\r\n      image:\r\n        'https://images.unsplash.com/photo-1552581234-26160f608093?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',\r\n      url: 'https://suna.so/share/c3472df7-adc1-4d5f-9927-4f8f513ec2fe',\r\n    },\r\n    {\r\n      id: 'seo-analysis',\r\n      title: 'SEO Analysis',\r\n      description:\r\n        \"Based on my website suna.so, generate an SEO report analysis, find top-ranking pages by keyword clusters, and identify topics I'm missing.\",\r\n      category: 'marketing',\r\n      featured: true,\r\n      icon: (\r\n        <svg\r\n          width=\"24\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 24 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M4.75 11.75L10.25 6.25L14.75 10.75L19.25 6.25\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M19.25 6.25V19.25\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M4.75 6.25V19.25\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M4.75 19.25H19.25\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n        </svg>\r\n      ),\r\n      image:\r\n        'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',\r\n      url: 'https://suna.so/share/cf756e02-fee9-4281-a0e4-76ac850f1ac9',\r\n    },\r\n    {\r\n      id: 'personal-trip',\r\n      title: 'Generate a Personal Trip',\r\n      description:\r\n        'Generate a personal trip to London, with departure from Bangkok on the 1st of May. The trip will last 10 days. Find an accommodation in the center of London, with a rating on Google reviews of at least 4.5.',\r\n      category: 'travel',\r\n      featured: true,\r\n      icon: (\r\n        <svg\r\n          width=\"24\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 24 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M4.75 8.75C4.75 7.64543 5.64543 6.75 6.75 6.75H17.25C18.3546 6.75 19.25 7.64543 19.25 8.75V17.25C19.25 18.3546 18.3546 19.25 17.25 19.25H6.75C5.64543 19.25 4.75 18.3546 4.75 17.25V8.75Z\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M8 4.75V8.25\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M16 4.75V8.25\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M7.75 10.75H16.25\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n        </svg>\r\n      ),\r\n      image:\r\n        'https://images.unsplash.com/photo-1507525428034-b723cf961d3e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',\r\n      url: 'https://suna.so/share/8442cc76-ac8b-438c-b539-4b93909a2218',\r\n    },\r\n    {\r\n      id: 'funded-startups',\r\n      title: 'Recently Funded Startups',\r\n      description:\r\n        'Go on Crunchbase, Dealroom, and TechCrunch, filter by Series A funding rounds in the SaaS Finance Space, and build a report with company data, founders, and contact info for outbound sales.',\r\n      category: 'finance',\r\n      featured: true,\r\n      icon: (\r\n        <svg\r\n          width=\"24\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 24 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M12 4.75L19.25 9L12 13.25L4.75 9L12 4.75Z\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M9.25 11.5L4.75 14L12 18.25L19.25 14L14.6722 11.5\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n        </svg>\r\n      ),\r\n      image:\r\n        'https://images.unsplash.com/photo-1444653614773-995cb1ef9efa?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',\r\n      url: 'https://suna.so/share/f04c871c-6bf5-4464-8e9c-5351c9cf5a60',\r\n    },\r\n    {\r\n      id: 'scrape-forums',\r\n      title: 'Scrape Forum Discussions',\r\n      description:\r\n        'I need to find the best beauty centers in Rome, but I want to find them by using open forums that speak about this topic. Go on Google, and scrape the forums by looking for beauty center discussions located in Rome.',\r\n      category: 'research',\r\n      featured: true,\r\n      icon: (\r\n        <svg\r\n          width=\"24\"\r\n          height=\"24\"\r\n          viewBox=\"0 0 24 24\"\r\n          fill=\"none\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            d=\"M5.75 19.2502H18.25C18.8023 19.2502 19.25 18.8025 19.25 18.2502V5.75C19.25 5.19772 18.8023 4.75 18.25 4.75H5.75C5.19772 4.75 4.75 5.19772 4.75 5.75V18.2502C4.75 18.8025 5.19772 19.2502 5.75 19.2502Z\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M9.75 8.75C9.75 9.44036 9.19036 10 8.5 10C7.80964 10 7.25 9.44036 7.25 8.75C7.25 8.05964 7.80964 7.5 8.5 7.5C9.19036 7.5 9.75 8.05964 9.75 8.75Z\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n          <path\r\n            d=\"M19.25 13.75L14.75 9.25L7.25 16.75\"\r\n            stroke=\"currentColor\"\r\n            strokeWidth=\"1.5\"\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n          />\r\n        </svg>\r\n      ),\r\n      image:\r\n        'https://images.unsplash.com/photo-1523580494863-6f3031224c94?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2400&q=80',\r\n      url: 'https://suna.so/share/53bcd4c7-40d6-4293-9f69-e2638ddcfad8',\r\n    },\r\n  ],\r\n};\r\n\r\nexport type SiteConfig = typeof siteConfig;\r\n"], "names": [], "mappings": ";;;;;AA8DO;;AA9DP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEO,MAAM,YAAY,CAAC,EACxB,QAAQ,EACR,SAAS,EAIV;IACC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;kBAGD;;;;;;AAGP;KAjBa;AAmBN,MAAM,kBAAkB;AA6BxB,MAAM,aAAa;IACxB,MAAM;IACN,aAAa;IACb,KAAK;IACL,KAAK,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;IACxC,UAAU;QAAC;QAAY;QAAiB;QAAkB;KAAmB;IAC7E,OAAO;QACL,OAAO;QACP,SAAS;QACT,SAAS;QACT,QAAQ;QACR,WAAW;IACb;IACA,KAAK;QACH,OAAO;YACL;gBAAE,IAAI;gBAAG,MAAM;gBAAQ,MAAM;YAAQ;YACrC;gBAAE,IAAI;gBAAG,MAAM;gBAAa,MAAM;YAAa;YAC/C;gBAAE,IAAI;gBAAG,MAAM;gBAAe,MAAM;YAAe;YACnD;gBAAE,IAAI;gBAAG,MAAM;gBAAW,MAAM;YAAW;SAC5C;IACH;IACA,MAAM;QACJ,yBACE,6LAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,MAAK;YACL,OAAM;YACN,WAAU;;8BAEV,6LAAC;oBACC,GAAE;oBACF,QAAO;oBACP,aAAY;;;;;;8BAEd,6LAAC;oBACC,GAAE;oBACF,QAAO;oBACP,aAAY;;;;;;8BAEd,6LAAC;oBACC,GAAE;oBACF,QAAO;oBACP,aAAY;;;;;;;;;;;;QAIlB,OAAO;QACP,WAAW;QACX,OAAO;QACP,aACE;QACF,kBAAkB;IACpB;IACA,mBAAmB;QACjB;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,YAAY;YACZ,aAAa;YACb,WAAW;YACX,gBAAgB,GAChB,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,eAAe,uHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,IAAI,CAAC,OAAO;YACrD,cAAc,EAAE;QAClB;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,qBAAqB;YACrB,oBAAoB;YACpB,aAAa;YACb,YAAY;YACZ,aAAa;YACb,WAAW;YACX,gBAAgB,GAChB,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,eAAe,uHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC,OAAO;YAC1D,qBAAqB,uHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,OAAO;YACvE,cAAc,EAAE;QAClB;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,qBAAqB;YACrB,oBAAoB;YACpB,aAAa;YACb,YAAY;YACZ,aAAa;YACb,WAAW;YACX,gBAAgB,GAChB,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,eAAe,uHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,SAAS,CAAC,OAAO;YAC1D,qBAAqB,uHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,OAAO;YACvE,cAAc,EAAE;QAClB;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,qBAAqB;YACrB,oBAAoB;YACpB,aAAa;YACb,YAAY;YACZ,aAAa;YACb,WAAW;YACX,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,eAAe,uHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,WAAW,CAAC,OAAO;YAC5D,qBAAqB,uHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,OAAO;YACzE,cAAc,EAAE;YAChB,QAAQ;QACV;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,qBAAqB;YACrB,oBAAoB;YACpB,aAAa;YACb,YAAY;YACZ,aAAa;YACb,WAAW;YACX,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;YACD,eAAe,uHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,WAAW,CAAC,OAAO;YAC5D,qBAAqB,uHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,OAAO;YACzE,cAAc,EAAE;QAClB;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,qBAAqB;YACrB,oBAAoB;YACpB,aAAa;YACb,YAAY;YACZ,aAAa;YACb,WAAW;YACX,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,eAAe,uHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,WAAW,CAAC,OAAO;YAC5D,qBAAqB,uHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,OAAO;YACzE,cAAc,EAAE;YAChB,QAAQ;QACV;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,qBAAqB;YACrB,oBAAoB;YACpB,aAAa;YACb,YAAY;YACZ,aAAa;YACb,WAAW;YACX,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,eAAe,uHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,YAAY,CAAC,OAAO;YAC7D,qBAAqB,uHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,OAAO;YAC1E,cAAc,EAAE;YAChB,QAAQ;QACV;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;YACb,qBAAqB;YACrB,oBAAoB;YACpB,aAAa;YACb,YAAY;YACZ,aAAa;YACb,WAAW;YACX,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,eAAe,uHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,aAAa,CAAC,OAAO;YAC9D,qBAAqB,uHAAA,CAAA,SAAM,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,OAAO;YAC3E,cAAc,EAAE;YAChB,QAAQ;QACV;KACD;IACD,iBAAiB;QACf,cAAc;YACZ;gBACE,IAAI;gBACJ,MAAM;gBACN,oBACE,6LAAC;oBACC,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,OAAM;oBACN,WAAU;;sCAEV,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;;;;;;;YAGd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,oBACE,6LAAC;oBACC,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,OAAM;oBACN,WAAU;;sCAEV,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;;;;;;;YAGd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,oBACE,6LAAC;oBACC,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,OAAM;oBACN,WAAU;8BAEV,cAAA,6LAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;;;;;;;;;;;YAIV;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,oBACE,6LAAC;oBACC,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,OAAM;oBACN,WAAU;8BAEV,cAAA,6LAAC;wBAAK,GAAE;;;;;;;;;;;YAGd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,oBACE,6LAAC;oBACC,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,OAAM;oBACN,WAAU;;sCAEV,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;;;;;;;YAGd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,oBACE,6LAAC;oBACC,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,OAAM;oBACN,WAAU;;sCAEV,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BACC,UAAS;4BACT,UAAS;4BACT,GAAE;;;;;;sCAEJ,6LAAC;4BACC,UAAS;4BACT,UAAS;4BACT,GAAE;;;;;;sCAEJ,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;sCACR,6LAAC;4BAAK,GAAE;;;;;;;;;;;;YAGd;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,oBACE,6LAAC;oBACC,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,OAAM;oBACN,WAAU;;sCAEV,6LAAC;4BACC,IAAG;4BACH,WAAU;4BACV,GAAE;4BACF,GAAE;4BACF,OAAM;4BACN,QAAO;sCAEP,cAAA,6LAAC;gCACC,GAAE;gCACF,MAAK;;;;;;;;;;;sCAGT,6LAAC;4BAAE,MAAK;;8CACN,6LAAC;oCAAK,GAAE;;;;;;8CACR,6LAAC;oCAAK,GAAE;;;;;;8CACR,6LAAC;oCAAK,GAAE;;;;;;8CACR,6LAAC;oCAAK,GAAE;;;;;;8CACR,6LAAC;oCAAK,GAAE;;;;;;8CACR,6LAAC;oCAAK,GAAE;;;;;;8CACR,6LAAC;oCAAK,GAAE;;;;;;;;;;;;sCAEV,6LAAC;4BACC,UAAS;4BACT,UAAS;4BACT,GAAE;;;;;;;;;;;;YAIV;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,oBACE,6LAAC;oBACC,OAAM;oBACN,QAAO;oBACP,SAAQ;oBACR,MAAK;oBACL,OAAM;oBACN,WAAU;8BAEV,cAAA,6LAAC;wBACC,UAAS;wBACT,UAAS;wBACT,GAAE;;;;;;;;;;;YAIV;SACD;IACH;IACA,gBAAgB;QACd,OAAO;QACP,aACE;QACF,OAAO;YACL;gBACE,IAAI;gBACJ,OAAO;gBACP,SACE;gBACF,OACE;YACJ;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SACE;gBACF,OACE;YACJ;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SACE;gBACF,OACE;YACJ;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,SACE;gBACF,OACE;YACJ;SACD;IACH;IACA,cAAc;QACZ,OAAO;QACP,aACE;QACF,OAAO;YACL;gBACE,IAAI;gBACJ,uBAAS,6LAAC,4JAAA,CAAA,sBAAmB;;;;;gBAC7B,OAAO;gBACP,aACE;YACJ;YACA;gBACE,IAAI;gBACJ,uBAAS,6LAAC,6JAAA,CAAA,uBAAoB;;;;;gBAC9B,OAAO;gBACP,aACE;YACJ;YACA;gBACE,IAAI;gBACJ,uBACE,6LAAC,4JAAA,CAAA,sBAAmB;oBAClB,MAAM;wBAAC;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;wBAAI;qBAAG;oBAClC,eAAe;wBACb;wBAAM;wBAAM;wBAAM;wBAAM;wBAAM;wBAAM;wBAAM;wBAAM;wBAAM;qBACvD;;;;;;gBAGL,OAAO;gBACP,aACE;YACJ;YACA;gBACE,IAAI;gBACJ,uBAAS,6LAAC,6JAAA,CAAA,uBAAoB;oBAAC,MAAM;;;;;;gBACrC,OAAO;gBACP,aACE;YACJ;SACD;IACH;IACA,UAAU;QACR;YACE,IAAI;YACJ,MAAM;YACN,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;QACT;KACD;IACD,eAAe;QACb,OAAO;QACP,aACE;QACF,OAAO;YACL;gBACE,IAAI;gBACJ,uBACE,6LAAC;oBACC,WAAU;oBACV,OAAO;wBACL,iBAAiB,CAAC,k3BAAk3B,CAAC;wBACr4B,WAAW,CAAC,k3BAAk3B,CAAC;wBAC/3B,gBAAgB;wBAChB,UAAU;wBACV,kBAAkB;wBAClB,cAAc;oBAChB;;sCAEA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,OAAM;gCACN,OAAM;gCACN,QAAO;gCACP,SAAQ;gCACR,MAAK;gCACL,WAAU;0CAEV,cAAA,6LAAC;oCACC,UAAS;oCACT,UAAS;oCACT,GAAE;;;;;;;;;;;;;;;;sCAIR,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,OAAM;gCACN,OAAM;gCACN,QAAO;gCACP,SAAQ;gCACR,WAAU;;kDAEV,6LAAC;wCAAE,QAAO;kDACR,cAAA,6LAAC;4CACC,UAAS;4CACT,UAAS;4CACT,GAAE;;;;;;;;;;;kDAGN,6LAAC;kDACC,cAAA,6LAAC;4CACC,IAAG;4CACH,GAAE;4CACF,GAAE;4CACF,OAAM;4CACN,QAAO;4CACP,aAAY;4CACZ,2BAA0B;;8DAE1B,6LAAC;oDAAQ,cAAa;oDAAI,QAAO;;;;;;8DACjC,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,QAAO;oDACP,QAAO;;;;;;8DAET,6LAAC;oDAAS,IAAG;;;;;;8DACb,6LAAC;oDAAe,cAAa;;;;;;8DAC7B,6LAAC;oDACC,MAAK;oDACL,QAAO;;;;;;8DAET,6LAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,QAAO;;;;;;8DAET,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,QAAO;oDACP,QAAO;;;;;;8DAET,6LAAC;oDAAS,IAAG;;;;;;8DACb,6LAAC;oDAAe,cAAa;;;;;;8DAC7B,6LAAC;oDACC,MAAK;oDACL,QAAO;;;;;;8DAET,6LAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,QAAO;;;;;;8DAET,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,QAAO;oDACP,QAAO;;;;;;8DAET,6LAAC;oDAAS,IAAG;;;;;;8DACb,6LAAC;oDAAe,cAAa;;;;;;8DAC7B,6LAAC;oDACC,MAAK;oDACL,QAAO;;;;;;8DAET,6LAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,QAAO;;;;;;8DAET,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,QAAO;oDACP,QAAO;;;;;;8DAET,6LAAC;oDAAS,IAAG;;;;;;8DACb,6LAAC;oDAAe,cAAa;;;;;;8DAC7B,6LAAC;oDACC,MAAK;oDACL,QAAO;;;;;;8DAET,6LAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,QAAO;;;;;;8DAET,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,KAAI;oDACJ,QAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,OAAM;gCACN,OAAM;gCACN,QAAO;gCACP,SAAQ;gCACR,WAAU;;kDAEV,6LAAC;wCAAE,QAAO;kDACR,cAAA,6LAAC;4CACC,UAAS;4CACT,UAAS;4CACT,GAAE;;;;;;;;;;;kDAGN,6LAAC;kDACC,cAAA,6LAAC;4CACC,IAAG;4CACH,GAAE;4CACF,GAAE;4CACF,OAAM;4CACN,QAAO;4CACP,aAAY;4CACZ,2BAA0B;;8DAE1B,6LAAC;oDAAQ,cAAa;oDAAI,QAAO;;;;;;8DACjC,6LAAC;oDACC,MAAK;oDACL,IAAG;oDACH,KAAI;oDACJ,QAAO;;;;;;8DAET,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,QAAO;oDACP,QAAO;;;;;;8DAET,6LAAC;oDAAS,IAAG;;;;;;8DACb,6LAAC;oDAAe,cAAa;;;;;;8DAC7B,6LAAC;oDACC,KAAI;oDACJ,UAAS;oDACT,IAAG;oDACH,IAAG;;;;;;8DAEL,6LAAC;oDACC,MAAK;oDACL,QAAO;;;;;;8DAET,6LAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,QAAO;;;;;;8DAET,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,QAAO;oDACP,QAAO;;;;;;8DAET,6LAAC;oDAAS,IAAG;;;;;;8DACb,6LAAC;oDAAe,cAAa;;;;;;8DAC7B,6LAAC;oDACC,KAAI;oDACJ,UAAS;oDACT,IAAG;oDACH,IAAG;;;;;;8DAEL,6LAAC;oDACC,MAAK;oDACL,QAAO;;;;;;8DAET,6LAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,QAAO;;;;;;8DAET,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,QAAO;oDACP,QAAO;;;;;;8DAET,6LAAC;oDAAS,IAAG;;;;;;8DACb,6LAAC;oDAAe,cAAa;;;;;;8DAC7B,6LAAC;oDACC,KAAI;oDACJ,UAAS;oDACT,IAAG;oDACH,IAAG;;;;;;8DAEL,6LAAC;oDACC,MAAK;oDACL,QAAO;;;;;;8DAET,6LAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,QAAO;;;;;;8DAET,6LAAC;oDACC,IAAG;oDACH,MAAK;oDACL,QAAO;oDACP,QAAO;;;;;;8DAET,6LAAC;oDAAS,IAAG;;;;;;8DACb,6LAAC;oDAAe,cAAa;;;;;;8DAC7B,6LAAC;oDACC,KAAI;oDACJ,UAAS;oDACT,IAAG;oDACH,IAAG;;;;;;8DAEL,6LAAC;oDACC,MAAK;oDACL,QAAO;;;;;;8DAET,6LAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,QAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMjB,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,YAAY;gCAAE,UAAU;gCAAK,MAAM;4BAAU;4BAC7C,WAAU;sCAEV,cAAA,6LAAC,yJAAA,CAAA,iBAAc;gCACb,WAAU;gCACV,SAAS;gCACT,YAAY;gCACZ,YAAY;;;;;;;;;;;;;;;;;gBAMpB,OAAO;gBACP,aACE;YACJ;YACA;gBACE,IAAI;gBACJ,uBACE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,4IAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;gBAIrB,OAAO;gBACP,aACE;YACJ;SACD;IACH;IACA,cAAc;QACZ,OACE;QACF,QAAQ;YACN,MAAM;YACN,MAAM;YACN,OAAO;QACT;IACF;IACA,SAAS;QACP,OAAO;QACP,aACE;QACF,cAAc;YACZ;gBACE,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,aAAa;gBACb,UAAU;oBACR;oBACA;oBACA;oBACA;iBACD;gBACD,aAAa;gBACb,YAAY;gBACZ,aAAa;gBACb,WAAW;YACb;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,aAAa;gBACb,UAAU;oBACR;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,aAAa;gBACb,YAAY;gBACZ,aAAa;gBACb,WAAW;YACb;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,OAAO;gBACP,QAAQ;gBACR,aAAa;gBACb,UAAU;oBACR;oBACA;oBACA;oBACA;oBACA;iBACD;gBACD,aAAa;gBACb,YAAY;gBACZ,aAAa;gBACb,WAAW;YACb;SACD;IACH;IACA,cAAc;QACZ;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,KAAK;YACL,2BACE,6LAAC;;oBAAE;kCAGD,6LAAC;kCAAU;;;;;;oBAEE;oBAAI;;;;;;;QAIvB;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,KAAK;YACL,2BACE,6LAAC;;oBAAE;kCAGD,6LAAC;kCAAU;;;;;;oBAAuD;oBAAI;;;;;;;QAI5E;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,KAAK;YACL,2BACE,6LAAC;;oBAAE;kCAGD,6LAAC;kCAAU;;;;;;oBAA8C;;;;;;;QAI/D;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,KAAK;YACL,2BACE,6LAAC;;oBAAE;kCAGD,6LAAC;kCAAU;;;;;;oBAAuD;;;;;;;QAIxE;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,KAAK;YACL,2BACE,6LAAC;;oBAAE;kCAGD,6LAAC;kCAAU;;;;;;oBAGE;oBAAI;;;;;;;QAIvB;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,KAAK;YACL,2BACE,6LAAC;;oBAAE;kCAGD,6LAAC;kCAAU;;;;;;oBAEE;;;;;;;QAGnB;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,KAAK;YACL,2BACE,6LAAC;;oBAAE;kCAGD,6LAAC;kCAAU;;;;;;oBAEE;oBAAI;;;;;;;QAIvB;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,KAAK;YACL,2BACE,6LAAC;;oBAAE;kCAGD,6LAAC;kCAAU;;;;;;oBAEE;oBAAI;;;;;;;QAIvB;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,KAAK;YACL,2BACE,6LAAC;;oBAAE;kCAGD,6LAAC;kCAAU;;;;;;oBAEE;oBAAI;;;;;;;QAIvB;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,KAAK;YACL,2BACE,6LAAC;;oBAAE;kCAGD,6LAAC;kCAAU;;;;;;oBAEE;oBAAI;;;;;;;QAIvB;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,KAAK;YACL,2BACE,6LAAC;;oBAAE;kCAGD,6LAAC;kCAAU;;;;;;oBAEE;oBAAI;;;;;;;QAIvB;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,KAAK;YACL,2BACE,6LAAC;;oBAAE;kCAGD,6LAAC;kCAAU;;;;;;oBAAwD;;;;;;;QAIzE;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,KAAK;YACL,2BACE,6LAAC;;oBAAE;kCAGD,6LAAC;kCAAU;;;;;;oBAEE;oBAAI;;;;;;;QAIvB;KACD;IACD,YAAY;QACV,OAAO;QACP,aACE;QACF,UAAU;YACR;gBACE,IAAI;gBACJ,UAAU;gBACV,QACE;YACJ;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,QACE;YACJ;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,QACE;YACJ;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,QACE;YACJ;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,QACE;YACJ;YACA;gBACE,IAAI;gBACJ,UAAU;gBACV,QACE;YACJ;SACD;IACH;IACA,YAAY;QACV,IAAI;QACJ,OAAO;QACP,iBAAiB;QACjB,QAAQ;YACN,MAAM;YACN,MAAM;QACR;QACA,SAAS;IACX;IACA,aAAa;QACX;YACE,OAAO;YACP,OAAO;gBACL;oBAAE,IAAI;oBAAG,OAAO;oBAAS,KAAK;gBAAoB;gBAClD;oBAAE,IAAI;oBAAG,OAAO;oBAAW,KAAK;gBAAuB;gBACvD;oBAAE,IAAI;oBAAG,OAAO;oBAAW,KAAK;gBAA4B;aAC7D;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,KAAK;gBACP;gBACA;oBAAE,IAAI;oBAAG,OAAO;oBAAW,KAAK;gBAAgC;gBAChE;oBAAE,IAAI;oBAAG,OAAO;oBAAU,KAAK;gBAAoC;aACpE;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,KAAK;gBACP;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,KAAK;gBACP;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,KAAK;gBACP;aACD;QACH;KACD;IACD,UAAU;QACR;YACE,IAAI;YACJ,OAAO;YACP,aACE;YACF,UAAU;YACV,UAAU;YACV,oBACE,6LAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;;;;;;;YAIrB,OACE;YACF,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,aACE;YACF,UAAU;YACV,UAAU;YACV,oBACE,6LAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;;;;;;;YAIrB,OACE;YACF,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,aACE;YACF,UAAU;YACV,UAAU;YACV,oBACE,6LAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;;;;;;;YAIrB,OACE;YACF,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,aACE;YACF,UAAU;YACV,UAAU;YACV,oBACE,6LAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;;;;;;;YAIrB,OACE;YACF,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,aACE;YACF,UAAU;YACV,UAAU;YACV,oBACE,6LAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;;;;;;;YAIrB,OACE;YACF,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,aACE;YACF,UAAU;YACV,UAAU;YACV,oBACE,6LAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;;;;;;;YAIrB,OACE;YACF,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,aACE;YACF,UAAU;YACV,UAAU;YACV,oBACE,6LAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;;;;;;;YAIrB,OACE;YACF,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,aACE;YACF,UAAU;YACV,UAAU;YACV,oBACE,6LAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;;;;;;;YAIrB,OACE;YACF,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,aACE;YACF,UAAU;YACV,UAAU;YACV,oBACE,6LAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;;;;;;;YAIrB,OACE;YACF,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,aACE;YACF,UAAU;YACV,UAAU;YACV,oBACE,6LAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;;;;;;;YAIrB,OACE;YACF,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,aACE;YACF,UAAU;YACV,UAAU;YACV,oBACE,6LAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;;;;;;;YAIrB,OACE;YACF,KAAK;QACP;QACA;YACE,IAAI;YACJ,OAAO;YACP,aACE;YACF,UAAU;YACV,UAAU;YACV,oBACE,6LAAC;gBACC,OAAM;gBACN,QAAO;gBACP,SAAQ;gBACR,MAAK;gBACL,OAAM;;kCAEN,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;kCAEjB,6LAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,eAAc;wBACd,gBAAe;;;;;;;;;;;;YAIrB,OACE;YACF,KAAK;QACP;KACD;AACH", "debugId": null}}, {"offset": {"line": 11569, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>"], "sourcesContent": ["'use client';\r\n\r\nimport { siteConfig } from '@/lib/home';\r\nimport { motion } from 'motion/react';\r\nimport React, { useRef, useState } from 'react';\r\n\r\ninterface NavItem {\r\n  name: string;\r\n  href: string;\r\n}\r\n\r\nconst navs: NavItem[] = siteConfig.nav.links;\r\n\r\nexport function NavMenu() {\r\n  const ref = useRef<HTMLUListElement>(null);\r\n  const [left, setLeft] = useState(0);\r\n  const [width, setWidth] = useState(0);\r\n  const [isReady, setIsReady] = useState(false);\r\n  const [activeSection, setActiveSection] = useState('hero');\r\n  const [isManualScroll, setIsManualScroll] = useState(false);\r\n\r\n  React.useEffect(() => {\r\n    // Initialize with first nav item\r\n    const firstItem = ref.current?.querySelector(\r\n      `[href=\"#${navs[0].href.substring(1)}\"]`,\r\n    )?.parentElement;\r\n    if (firstItem) {\r\n      const rect = firstItem.getBoundingClientRect();\r\n      setLeft(firstItem.offsetLeft);\r\n      setWidth(rect.width);\r\n      setIsReady(true);\r\n    }\r\n  }, []);\r\n\r\n  React.useEffect(() => {\r\n    const handleScroll = () => {\r\n      // Skip scroll handling during manual click scrolling\r\n      if (isManualScroll) return;\r\n\r\n      const sections = navs.map((item) => item.href.substring(1));\r\n\r\n      // Find the section closest to viewport top\r\n      let closestSection = sections[0];\r\n      let minDistance = Infinity;\r\n\r\n      for (const section of sections) {\r\n        const element = document.getElementById(section);\r\n        if (element) {\r\n          const rect = element.getBoundingClientRect();\r\n          const distance = Math.abs(rect.top - 100); // Offset by 100px to trigger earlier\r\n          if (distance < minDistance) {\r\n            minDistance = distance;\r\n            closestSection = section;\r\n          }\r\n        }\r\n      }\r\n\r\n      // Update active section and nav indicator\r\n      setActiveSection(closestSection);\r\n      const navItem = ref.current?.querySelector(\r\n        `[href=\"#${closestSection}\"]`,\r\n      )?.parentElement;\r\n      if (navItem) {\r\n        const rect = navItem.getBoundingClientRect();\r\n        setLeft(navItem.offsetLeft);\r\n        setWidth(rect.width);\r\n      }\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll);\r\n    handleScroll(); // Initial check\r\n    return () => window.removeEventListener('scroll', handleScroll);\r\n  }, [isManualScroll]);\r\n\r\n  const handleClick = (\r\n    e: React.MouseEvent<HTMLAnchorElement>,\r\n    item: NavItem,\r\n  ) => {\r\n    e.preventDefault();\r\n\r\n    const targetId = item.href.substring(1);\r\n    const element = document.getElementById(targetId);\r\n\r\n    if (element) {\r\n      // Set manual scroll flag\r\n      setIsManualScroll(true);\r\n\r\n      // Immediately update nav state\r\n      setActiveSection(targetId);\r\n      const navItem = e.currentTarget.parentElement;\r\n      if (navItem) {\r\n        const rect = navItem.getBoundingClientRect();\r\n        setLeft(navItem.offsetLeft);\r\n        setWidth(rect.width);\r\n      }\r\n\r\n      // Calculate exact scroll position\r\n      const elementPosition = element.getBoundingClientRect().top;\r\n      const offsetPosition = elementPosition + window.pageYOffset - 100; // 100px offset\r\n\r\n      // Smooth scroll to exact position\r\n      window.scrollTo({\r\n        top: offsetPosition,\r\n        behavior: 'smooth',\r\n      });\r\n\r\n      // Reset manual scroll flag after animation completes\r\n      setTimeout(() => {\r\n        setIsManualScroll(false);\r\n      }, 500); // Adjust timing to match scroll animation duration\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full hidden md:block\">\r\n      <ul\r\n        className=\"relative mx-auto flex w-fit rounded-full h-11 px-2 items-center justify-center\"\r\n        ref={ref}\r\n      >\r\n        {navs.map((item) => (\r\n          <li\r\n            key={item.name}\r\n            className={`z-10 cursor-pointer h-full flex items-center justify-center px-4 py-2 text-sm font-medium transition-colors duration-200 ${\r\n              activeSection === item.href.substring(1)\r\n                ? 'text-primary'\r\n                : 'text-primary/60 hover:text-primary'\r\n            } tracking-tight`}\r\n          >\r\n            <a href={item.href} onClick={(e) => handleClick(e, item)}>\r\n              {item.name}\r\n            </a>\r\n          </li>\r\n        ))}\r\n        {isReady && (\r\n          <motion.li\r\n            animate={{ left, width }}\r\n            transition={{ type: 'spring', stiffness: 400, damping: 30 }}\r\n            className=\"absolute inset-0 my-1.5 rounded-full bg-accent/60 border border-border\"\r\n          />\r\n        )}\r\n      </ul>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAWA,MAAM,OAAkB,sHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,KAAK;AAErC,SAAS;;IACd,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IACrC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,6JAAA,CAAA,UAAK,CAAC,SAAS;6BAAC;YACd,iCAAiC;YACjC,MAAM,YAAY,IAAI,OAAO,EAAE,cAC7B,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,GACvC;YACH,IAAI,WAAW;gBACb,MAAM,OAAO,UAAU,qBAAqB;gBAC5C,QAAQ,UAAU,UAAU;gBAC5B,SAAS,KAAK,KAAK;gBACnB,WAAW;YACb;QACF;4BAAG,EAAE;IAEL,6JAAA,CAAA,UAAK,CAAC,SAAS;6BAAC;YACd,MAAM;kDAAe;oBACnB,qDAAqD;oBACrD,IAAI,gBAAgB;oBAEpB,MAAM,WAAW,KAAK,GAAG;mEAAC,CAAC,OAAS,KAAK,IAAI,CAAC,SAAS,CAAC;;oBAExD,2CAA2C;oBAC3C,IAAI,iBAAiB,QAAQ,CAAC,EAAE;oBAChC,IAAI,cAAc;oBAElB,KAAK,MAAM,WAAW,SAAU;wBAC9B,MAAM,UAAU,SAAS,cAAc,CAAC;wBACxC,IAAI,SAAS;4BACX,MAAM,OAAO,QAAQ,qBAAqB;4BAC1C,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,GAAG,GAAG,MAAM,qCAAqC;4BAChF,IAAI,WAAW,aAAa;gCAC1B,cAAc;gCACd,iBAAiB;4BACnB;wBACF;oBACF;oBAEA,0CAA0C;oBAC1C,iBAAiB;oBACjB,MAAM,UAAU,IAAI,OAAO,EAAE,cAC3B,CAAC,QAAQ,EAAE,eAAe,EAAE,CAAC,GAC5B;oBACH,IAAI,SAAS;wBACX,MAAM,OAAO,QAAQ,qBAAqB;wBAC1C,QAAQ,QAAQ,UAAU;wBAC1B,SAAS,KAAK,KAAK;oBACrB;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC,gBAAgB,gBAAgB;YAChC;qCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;4BAAG;QAAC;KAAe;IAEnB,MAAM,cAAc,CAClB,GACA;QAEA,EAAE,cAAc;QAEhB,MAAM,WAAW,KAAK,IAAI,CAAC,SAAS,CAAC;QACrC,MAAM,UAAU,SAAS,cAAc,CAAC;QAExC,IAAI,SAAS;YACX,yBAAyB;YACzB,kBAAkB;YAElB,+BAA+B;YAC/B,iBAAiB;YACjB,MAAM,UAAU,EAAE,aAAa,CAAC,aAAa;YAC7C,IAAI,SAAS;gBACX,MAAM,OAAO,QAAQ,qBAAqB;gBAC1C,QAAQ,QAAQ,UAAU;gBAC1B,SAAS,KAAK,KAAK;YACrB;YAEA,kCAAkC;YAClC,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;YAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG,KAAK,eAAe;YAElF,kCAAkC;YAClC,OAAO,QAAQ,CAAC;gBACd,KAAK;gBACL,UAAU;YACZ;YAEA,qDAAqD;YACrD,WAAW;gBACT,kBAAkB;YACpB,GAAG,MAAM,mDAAmD;QAC9D;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,WAAU;YACV,KAAK;;gBAEJ,KAAK,GAAG,CAAC,CAAC,qBACT,6LAAC;wBAEC,WAAW,CAAC,yHAAyH,EACnI,kBAAkB,KAAK,IAAI,CAAC,SAAS,CAAC,KAClC,iBACA,qCACL,eAAe,CAAC;kCAEjB,cAAA,6LAAC;4BAAE,MAAM,KAAK,IAAI;4BAAE,SAAS,CAAC,IAAM,YAAY,GAAG;sCAChD,KAAK,IAAI;;;;;;uBARP,KAAK,IAAI;;;;;gBAYjB,yBACC,6LAAC,qNAAA,CAAA,SAAM,CAAC,EAAE;oBACR,SAAS;wBAAE;wBAAM;oBAAM;oBACvB,YAAY;wBAAE,MAAM;wBAAU,WAAW;wBAAK,SAAS;oBAAG;oBAC1D,WAAU;;;;;;;;;;;;;;;;;AAMtB;GAlIgB;KAAA", "debugId": null}}, {"offset": {"line": 11737, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>/button.tsx"], "sourcesContent": ["import * as React from 'react';\r\nimport { Slot } from '@radix-ui/react-slot';\r\nimport { cva, type VariantProps } from 'class-variance-authority';\r\n\r\nimport { cn } from '@/lib/utils';\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40',\r\n        outline:\r\n          'border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground',\r\n        secondary:\r\n          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  },\r\n);\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean;\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button';\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  );\r\n}\r\n\r\nexport { Button, buttonVariants };\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,8cACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 11800, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport { Moon, Sun } from 'lucide-react';\r\nimport { useTheme } from 'next-themes';\r\nimport { Button } from '@/components/home/<USER>/button';\r\n\r\nexport function ThemeToggle() {\r\n  const { theme, setTheme } = useTheme();\r\n\r\n  return (\r\n    <Button\r\n      variant=\"outline\"\r\n      size=\"icon\"\r\n      onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}\r\n      className=\"cursor-pointer rounded-full h-8 w-8\"\r\n    >\r\n      <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0 text-primary\" />\r\n      <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100 text-primary\" />\r\n      <span className=\"sr-only\">Toggle theme</span>\r\n    </Button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;;;AALA;;;;AAOO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEnC,qBACE,6LAAC,6IAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS,IAAM,SAAS,UAAU,UAAU,SAAS;QACrD,WAAU;;0BAEV,6LAAC,mMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;0BACf,6LAAC,qMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;0BAChB,6LAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;GAfgB;;QACc,mJAAA,CAAA,WAAQ;;;KADtB", "debugId": null}}, {"offset": {"line": 11869, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>/navbar.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Icons } from '@/components/home/<USER>';\r\nimport { NavMenu } from '@/components/home/<USER>';\r\nimport { ThemeToggle } from '@/components/home/<USER>';\r\nimport { siteConfig } from '@/lib/home';\r\nimport { cn } from '@/lib/utils';\r\nimport { Menu, X, Github } from 'lucide-react';\r\nimport { AnimatePresence, motion, useScroll } from 'motion/react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { useEffect, useState } from 'react';\r\nimport { useTheme } from 'next-themes';\r\nimport { useAuth } from '@/components/AuthProvider';\r\n\r\nconst INITIAL_WIDTH = '70rem';\r\nconst MAX_WIDTH = '800px';\r\n\r\nconst overlayVariants = {\r\n  hidden: { opacity: 0 },\r\n  visible: { opacity: 1 },\r\n  exit: { opacity: 0 },\r\n};\r\n\r\nconst drawerVariants = {\r\n  hidden: { opacity: 0, y: 100 },\r\n  visible: {\r\n    opacity: 1,\r\n    y: 0,\r\n    rotate: 0,\r\n    transition: {\r\n      type: 'spring',\r\n      damping: 15,\r\n      stiffness: 200,\r\n      staggerChildren: 0.03,\r\n    },\r\n  },\r\n  exit: {\r\n    opacity: 0,\r\n    y: 100,\r\n    transition: { duration: 0.1 },\r\n  },\r\n};\r\n\r\nconst drawerMenuContainerVariants = {\r\n  hidden: { opacity: 0 },\r\n  visible: { opacity: 1 },\r\n};\r\n\r\nconst drawerMenuVariants = {\r\n  hidden: { opacity: 0 },\r\n  visible: { opacity: 1 },\r\n};\r\n\r\nexport function Navbar() {\r\n  const { scrollY } = useScroll();\r\n  const [hasScrolled, setHasScrolled] = useState(false);\r\n  const [isDrawerOpen, setIsDrawerOpen] = useState(false);\r\n  const [activeSection, setActiveSection] = useState('hero');\r\n  const { theme, resolvedTheme } = useTheme();\r\n  const [mounted, setMounted] = useState(false);\r\n  const { user } = useAuth();\r\n\r\n  useEffect(() => {\r\n    setMounted(true);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const handleScroll = () => {\r\n      const sections = siteConfig.nav.links.map((item) =>\r\n        item.href.substring(1),\r\n      );\r\n\r\n      for (const section of sections) {\r\n        const element = document.getElementById(section);\r\n        if (element) {\r\n          const rect = element.getBoundingClientRect();\r\n          if (rect.top <= 150 && rect.bottom >= 150) {\r\n            setActiveSection(section);\r\n            break;\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    window.addEventListener('scroll', handleScroll);\r\n    handleScroll();\r\n\r\n    return () => window.removeEventListener('scroll', handleScroll);\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    const unsubscribe = scrollY.on('change', (latest) => {\r\n      setHasScrolled(latest > 10);\r\n    });\r\n    return unsubscribe;\r\n  }, [scrollY]);\r\n\r\n  const toggleDrawer = () => setIsDrawerOpen((prev) => !prev);\r\n  const handleOverlayClick = () => setIsDrawerOpen(false);\r\n\r\n  const logoSrc = !mounted\r\n    ? '/kortix-logo.svg'\r\n    : resolvedTheme === 'dark'\r\n      ? '/kortix-logo-white.svg'\r\n      : '/kortix-logo.svg';\r\n\r\n  return (\r\n    <header\r\n      className={cn(\r\n        'sticky z-50 mx-4 flex justify-center transition-all duration-300 md:mx-0',\r\n        hasScrolled ? 'top-6' : 'top-4 mx-0',\r\n      )}\r\n    >\r\n      <motion.div\r\n        initial={{ width: INITIAL_WIDTH }}\r\n        animate={{ width: hasScrolled ? MAX_WIDTH : INITIAL_WIDTH }}\r\n        transition={{ duration: 0.3, ease: [0.25, 0.1, 0.25, 1] }}\r\n      >\r\n        <div\r\n          className={cn(\r\n            'mx-auto max-w-7xl rounded-2xl transition-all duration-300  xl:px-0',\r\n            hasScrolled\r\n              ? 'px-2 border border-border backdrop-blur-lg bg-background/75'\r\n              : 'shadow-none px-7',\r\n          )}\r\n        >\r\n          <div className=\"flex h-[56px] items-center justify-between p-4\">\r\n            <Link href=\"/\" className=\"flex items-center gap-3\">\r\n              <Image\r\n                src={logoSrc}\r\n                alt=\"Kortix Logo\"\r\n                width={140}\r\n                height={22}\r\n                priority\r\n              /> \r\n            </Link>\r\n\r\n            <NavMenu />\r\n\r\n            <div className=\"flex flex-row items-center gap-1 md:gap-3 shrink-0\">\r\n              <div className=\"flex items-center space-x-3\">\r\n                {/* <Link\r\n                  href=\"https://github.com/kortix-ai/suna\"\r\n                  target=\"_blank\"\r\n                  rel=\"noopener noreferrer\"\r\n                  className=\"hidden md:flex items-center justify-center h-8 px-3 text-sm font-normal tracking-wide rounded-full text-primary hover:text-primary/80 transition-colors\"\r\n                  aria-label=\"GitHub\"\r\n                >\r\n                  <Github className=\"size-[18px]\" />\r\n                </Link> */}\r\n                {user ? (\r\n                  <Link\r\n                    className=\"bg-secondary h-8 hidden md:flex items-center justify-center text-sm font-normal tracking-wide rounded-full text-primary-foreground dark:text-secondary-foreground w-fit px-4 shadow-[inset_0_1px_2px_rgba(255,255,255,0.25),0_3px_3px_-1.5px_rgba(16,24,40,0.06),0_1px_1px_rgba(16,24,40,0.08)] border border-white/[0.12]\"\r\n                    href=\"/dashboard\"\r\n                  >\r\n                    Dashboard\r\n                  </Link>\r\n                ) : (\r\n                  <Link\r\n                    className=\"bg-secondary h-8 hidden md:flex items-center justify-center text-sm font-normal tracking-wide rounded-full text-primary-foreground dark:text-secondary-foreground w-fit px-4 shadow-[inset_0_1px_2px_rgba(255,255,255,0.25),0_3px_3px_-1.5px_rgba(16,24,40,0.06),0_1px_1px_rgba(16,24,40,0.08)] border border-white/[0.12]\"\r\n                    href=\"/auth\"\r\n                  >\r\n                    Get started\r\n                  </Link>\r\n                )}\r\n              </div>\r\n              <ThemeToggle />\r\n              <button\r\n                className=\"md:hidden border border-border size-8 rounded-md cursor-pointer flex items-center justify-center\"\r\n                onClick={toggleDrawer}\r\n              >\r\n                {isDrawerOpen ? (\r\n                  <X className=\"size-5\" />\r\n                ) : (\r\n                  <Menu className=\"size-5\" />\r\n                )}\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </motion.div>\r\n\r\n      {/* Mobile Drawer */}\r\n      <AnimatePresence>\r\n        {isDrawerOpen && (\r\n          <>\r\n            <motion.div\r\n              className=\"fixed inset-0 bg-black/50 backdrop-blur-sm\"\r\n              initial=\"hidden\"\r\n              animate=\"visible\"\r\n              exit=\"exit\"\r\n              variants={overlayVariants}\r\n              transition={{ duration: 0.2 }}\r\n              onClick={handleOverlayClick}\r\n            />\r\n\r\n            <motion.div\r\n              className=\"fixed inset-x-0 w-[95%] mx-auto bottom-3 bg-background border border-border p-4 rounded-xl shadow-lg\"\r\n              initial=\"hidden\"\r\n              animate=\"visible\"\r\n              exit=\"exit\"\r\n              variants={drawerVariants}\r\n            >\r\n              {/* Mobile menu content */}\r\n              <div className=\"flex flex-col gap-4\">\r\n                <div className=\"flex items-center justify-between\">\r\n                  <Link href=\"/\" className=\"flex items-center gap-3\">\r\n                    <Image\r\n                      src={logoSrc}\r\n                      alt=\"Kortix Logo\"\r\n                      width={120}\r\n                      height={22}\r\n                      priority\r\n                    />\r\n                    <span className=\"font-medium text-primary text-sm\">\r\n                      / Suna\r\n                    </span>\r\n                  </Link>\r\n                  <button\r\n                    onClick={toggleDrawer}\r\n                    className=\"border border-border rounded-md p-1 cursor-pointer\"\r\n                  >\r\n                    <X className=\"size-5\" />\r\n                  </button>\r\n                </div>\r\n\r\n                <motion.ul\r\n                  className=\"flex flex-col text-sm mb-4 border border-border rounded-md\"\r\n                  variants={drawerMenuContainerVariants}\r\n                >\r\n                  <AnimatePresence>\r\n                    {siteConfig.nav.links.map((item) => (\r\n                      <motion.li\r\n                        key={item.id}\r\n                        className=\"p-2.5 border-b border-border last:border-b-0\"\r\n                        variants={drawerMenuVariants}\r\n                      >\r\n                        <a\r\n                          href={item.href}\r\n                          onClick={(e) => {\r\n                            e.preventDefault();\r\n                            const element = document.getElementById(\r\n                              item.href.substring(1),\r\n                            );\r\n                            element?.scrollIntoView({ behavior: 'smooth' });\r\n                            setIsDrawerOpen(false);\r\n                          }}\r\n                          className={`underline-offset-4 hover:text-primary/80 transition-colors ${\r\n                            activeSection === item.href.substring(1)\r\n                              ? 'text-primary font-medium'\r\n                              : 'text-primary/60'\r\n                          }`}\r\n                        >\r\n                          {item.name}\r\n                        </a>\r\n                      </motion.li>\r\n                    ))}\r\n                  </AnimatePresence>\r\n                </motion.ul>\r\n\r\n                {/* Action buttons */}\r\n                <div className=\"flex flex-col gap-2\">\r\n                  {user ? (\r\n                    <Link\r\n                      href=\"/dashboard\"\r\n                      className=\"bg-secondary h-8 flex items-center justify-center text-sm font-normal tracking-wide rounded-full text-primary-foreground dark:text-secondary-foreground w-full px-4 shadow-[inset_0_1px_2px_rgba(255,255,255,0.25),0_3px_3px_-1.5px_rgba(16,24,40,0.06),0_1px_1px_rgba(16,24,40,0.08)] border border-white/[0.12] hover:bg-secondary/80 transition-all ease-out active:scale-95\"\r\n                    >\r\n                      Dashboard\r\n                    </Link>\r\n                  ) : (\r\n                    <Link\r\n                      href=\"/auth\"\r\n                      className=\"bg-secondary h-8 flex items-center justify-center text-sm font-normal tracking-wide rounded-full text-primary-foreground dark:text-secondary-foreground w-full px-4 shadow-[inset_0_1px_2px_rgba(255,255,255,0.25),0_3px_3px_-1.5px_rgba(16,24,40,0.06),0_1px_1px_rgba(16,24,40,0.08)] border border-white/[0.12] hover:bg-secondary/80 transition-all ease-out active:scale-95\"\r\n                    >\r\n                      Get Started\r\n                    </Link>\r\n                  )}\r\n                  <div className=\"flex justify-between\">\r\n                    <ThemeToggle />\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </motion.div>\r\n          </>\r\n        )}\r\n      </AnimatePresence>\r\n    </header>\r\n  ); \r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;AAbA;;;;;;;;;;;;AAeA,MAAM,gBAAgB;AACtB,MAAM,YAAY;AAElB,MAAM,kBAAkB;IACtB,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QAAE,SAAS;IAAE;IACtB,MAAM;QAAE,SAAS;IAAE;AACrB;AAEA,MAAM,iBAAiB;IACrB,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAI;IAC7B,SAAS;QACP,SAAS;QACT,GAAG;QACH,QAAQ;QACR,YAAY;YACV,MAAM;YACN,SAAS;YACT,WAAW;YACX,iBAAiB;QACnB;IACF;IACA,MAAM;QACJ,SAAS;QACT,GAAG;QACH,YAAY;YAAE,UAAU;QAAI;IAC9B;AACF;AAEA,MAAM,8BAA8B;IAClC,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QAAE,SAAS;IAAE;AACxB;AAEA,MAAM,qBAAqB;IACzB,QAAQ;QAAE,SAAS;IAAE;IACrB,SAAS;QAAE,SAAS;IAAE;AACxB;AAEO,SAAS;;IACd,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,oMAAA,CAAA,YAAS,AAAD;IAC5B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IACxC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,UAAO,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,WAAW;QACb;2BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,MAAM,WAAW,sHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG;kEAAC,CAAC,OACzC,KAAK,IAAI,CAAC,SAAS,CAAC;;oBAGtB,KAAK,MAAM,WAAW,SAAU;wBAC9B,MAAM,UAAU,SAAS,cAAc,CAAC;wBACxC,IAAI,SAAS;4BACX,MAAM,OAAO,QAAQ,qBAAqB;4BAC1C,IAAI,KAAK,GAAG,IAAI,OAAO,KAAK,MAAM,IAAI,KAAK;gCACzC,iBAAiB;gCACjB;4BACF;wBACF;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;YAEA;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM,cAAc,QAAQ,EAAE,CAAC;gDAAU,CAAC;oBACxC,eAAe,SAAS;gBAC1B;;YACA,OAAO;QACT;2BAAG;QAAC;KAAQ;IAEZ,MAAM,eAAe,IAAM,gBAAgB,CAAC,OAAS,CAAC;IACtD,MAAM,qBAAqB,IAAM,gBAAgB;IAEjD,MAAM,UAAU,CAAC,UACb,qBACA,kBAAkB,SAChB,2BACA;IAEN,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4EACA,cAAc,UAAU;;0BAG1B,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,OAAO;gBAAc;gBAChC,SAAS;oBAAE,OAAO,cAAc,YAAY;gBAAc;gBAC1D,YAAY;oBAAE,UAAU;oBAAK,MAAM;wBAAC;wBAAM;wBAAK;wBAAM;qBAAE;gBAAC;0BAExD,cAAA,6LAAC;oBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sEACA,cACI,gEACA;8BAGN,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAK;oCACL,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;0CAIZ,6LAAC,4IAAA,CAAA,UAAO;;;;;0CAER,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAUZ,qBACC,6LAAC,+JAAA,CAAA,UAAI;4CACH,WAAU;4CACV,MAAK;sDACN;;;;;iEAID,6LAAC,+JAAA,CAAA,UAAI;4CACH,WAAU;4CACV,MAAK;sDACN;;;;;;;;;;;kDAKL,6LAAC,gJAAA,CAAA,cAAW;;;;;kDACZ,6LAAC;wCACC,WAAU;wCACV,SAAS;kDAER,6BACC,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;iEAEb,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS5B,6LAAC,oNAAA,CAAA,kBAAe;0BACb,8BACC;;sCACE,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,MAAK;4BACL,UAAU;4BACV,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,SAAS;;;;;;sCAGX,6LAAC,qNAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAQ;4BACR,SAAQ;4BACR,MAAK;4BACL,UAAU;sCAGV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAI,WAAU;;kEACvB,6LAAC,gIAAA,CAAA,UAAK;wDACJ,KAAK;wDACL,KAAI;wDACJ,OAAO;wDACP,QAAQ;wDACR,QAAQ;;;;;;kEAEV,6LAAC;wDAAK,WAAU;kEAAmC;;;;;;;;;;;;0DAIrD,6LAAC;gDACC,SAAS;gDACT,WAAU;0DAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAIjB,6LAAC,qNAAA,CAAA,SAAM,CAAC,EAAE;wCACR,WAAU;wCACV,UAAU;kDAEV,cAAA,6LAAC,oNAAA,CAAA,kBAAe;sDACb,sHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,qBACzB,6LAAC,qNAAA,CAAA,SAAM,CAAC,EAAE;oDAER,WAAU;oDACV,UAAU;8DAEV,cAAA,6LAAC;wDACC,MAAM,KAAK,IAAI;wDACf,SAAS,CAAC;4DACR,EAAE,cAAc;4DAChB,MAAM,UAAU,SAAS,cAAc,CACrC,KAAK,IAAI,CAAC,SAAS,CAAC;4DAEtB,SAAS,eAAe;gEAAE,UAAU;4DAAS;4DAC7C,gBAAgB;wDAClB;wDACA,WAAW,CAAC,2DAA2D,EACrE,kBAAkB,KAAK,IAAI,CAAC,SAAS,CAAC,KAClC,6BACA,mBACJ;kEAED,KAAK,IAAI;;;;;;mDApBP,KAAK,EAAE;;;;;;;;;;;;;;;kDA4BpB,6LAAC;wCAAI,WAAU;;4CACZ,qBACC,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;qEAID,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;0DAIH,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,gJAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUhC;GA3OgB;;QACM,oMAAA,CAAA,YAAS;QAII,mJAAA,CAAA,WAAQ;QAExB,qIAAA,CAAA,UAAO;;;KAPV", "debugId": null}}]}