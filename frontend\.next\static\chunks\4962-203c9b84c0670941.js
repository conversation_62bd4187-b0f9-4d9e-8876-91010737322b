"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4962],{2417:(e,t,a)=>{a.d(t,{Lh:()=>u,O4:()=>d,PA:()=>g,h4:()=>c,wG:()=>m});var s=a(28755),r=a(26715),n=a(5041),o=a(2508),l=a(69947),i=a(56671);let c=e=>(0,s.I)({queryKey:l.M.profiles.list(e),queryFn:()=>o.A.getProfiles(e),staleTime:3e5}),d=()=>{let e=(0,r.jE)();return(0,n.n)({mutationFn:e=>o.A.createProfile(e),onSuccess:t=>{e.invalidateQueries({queryKey:l.M.profiles.all()}),i.oR.success('Profile "'.concat(t.profile_name,'" created successfully'))},onError:e=>{i.oR.error(e.message||"Failed to create profile")}})},u=()=>{let e=(0,r.jE)();return(0,n.n)({mutationFn:e=>{let{profileId:t,request:a}=e;return o.A.updateProfile(t,a)},onSuccess:t=>{e.invalidateQueries({queryKey:l.M.profiles.all()}),e.invalidateQueries({queryKey:l.M.profiles.detail(t.profile_id)}),i.oR.success("Profile updated successfully")},onError:e=>{i.oR.error(e.message||"Failed to update profile")}})},g=()=>{let e=(0,r.jE)();return(0,n.n)({mutationFn:e=>o.A.deleteProfile(e),onSuccess:(t,a)=>{e.invalidateQueries({queryKey:l.M.profiles.all()}),e.removeQueries({queryKey:l.M.profiles.detail(a)}),i.oR.success("Profile deleted successfully")},onError:e=>{i.oR.error(e.message||"Failed to delete profile")}})},m=()=>{let e=(0,r.jE)();return(0,n.n)({mutationFn:e=>{let{profileId:t,app:a}=e;return o.A.connectProfile(t,a)},onSuccess:t=>{if(e.invalidateQueries({queryKey:l.M.profiles.all()}),e.invalidateQueries({queryKey:l.M.profiles.detail(t.profile_id)}),e.invalidateQueries({queryKey:l.M.profiles.connections(t.profile_id)}),t.link){let a=window.open(t.link,"_blank","width=600,height=700");if(a){let s=setInterval(()=>{a.closed&&(clearInterval(s),e.invalidateQueries({queryKey:l.M.profiles.all()}),e.invalidateQueries({queryKey:l.M.profiles.detail(t.profile_id)}),e.invalidateQueries({queryKey:l.M.profiles.connections(t.profile_id)}),i.oR.success("Connection process completed"))},1e3);setTimeout(()=>{clearInterval(s)},3e5)}else i.oR.error("Failed to open connection window. Please check your popup blocker.")}},onError:e=>{i.oR.error(e.message||"Failed to connect profile")}})}},5416:(e,t,a)=>{a.d(t,{_:()=>r,n:()=>s});let s={sb_shell_tool:{enabled:!0,description:"Execute shell commands in tmux sessions for terminal operations, CLI tools, and system management",icon:"\uD83D\uDCBB",color:"bg-slate-100 dark:bg-slate-800"},sb_files_tool:{enabled:!0,description:"Create, read, update, and delete files in the workspace with comprehensive file management",icon:"\uD83D\uDCC1",color:"bg-blue-100 dark:bg-blue-800/50"},sb_browser_tool:{enabled:!0,description:"Browser automation for web navigation, clicking, form filling, and page interaction",icon:"\uD83C\uDF10",color:"bg-indigo-100 dark:bg-indigo-800/50"},sb_deploy_tool:{enabled:!0,description:"Deploy applications and services with automated deployment capabilities",icon:"\uD83D\uDE80",color:"bg-green-100 dark:bg-green-800/50"},sb_expose_tool:{enabled:!0,description:"Expose services and manage ports for application accessibility",icon:"\uD83D\uDD0C",color:"bg-orange-100 dark:bg-orange-800/20"},web_search_tool:{enabled:!0,description:"Search the web using Tavily API and scrape webpages with Firecrawl for research",icon:"\uD83D\uDD0D",color:"bg-yellow-100 dark:bg-yellow-800/50"},sb_vision_tool:{enabled:!0,description:"Vision and image processing capabilities for visual content analysis",icon:"\uD83D\uDC41️",color:"bg-pink-100 dark:bg-pink-800/50"},data_providers_tool:{enabled:!0,description:"Access to data providers and external APIs (requires RapidAPI key)",icon:"\uD83D\uDD17",color:"bg-cyan-100 dark:bg-cyan-800/50"}},r=e=>({sb_shell_tool:"Terminal",sb_files_tool:"File Manager",sb_browser_tool:"Browser Automation",sb_deploy_tool:"Deploy Tool",sb_expose_tool:"Port Exposure",web_search_tool:"Web Search",sb_vision_tool:"Image Processing",data_providers_tool:"Data Providers"})[e]||e.replace(/_/g," ").replace(/\b\w/g,e=>e.toUpperCase())},17711:(e,t,a)=>{a.d(t,{Ej:()=>u,h:()=>p,u:()=>f});var s=a(12115),r=a(28755),n=a(71610);let o="http://localhost:8000/api",l=new Map,i=null;class c{static getInstance(){return c.instance||(c.instance=new c),c.instance}async isEnabled(e){try{let t=l.get(e);if(t&&Date.now()-t.timestamp<3e5)return t.value;let a=await fetch("".concat(o,"/feature-flags/").concat(e),{method:"GET",headers:{"Content-Type":"application/json"}});if(!a.ok)return console.warn("Failed to fetch feature flag ".concat(e,": ").concat(a.status)),!1;let s=await a.json();return l.set(e,{value:s.enabled,timestamp:Date.now()}),s.enabled}catch(t){return console.error("Error checking feature flag ".concat(e,":"),t),!1}}async getFlagDetails(e){try{let t=await fetch("".concat(o,"/feature-flags/").concat(e),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)return console.warn("Failed to fetch feature flag details for ".concat(e,": ").concat(t.status)),null;return await t.json()}catch(t){return console.error("Error fetching feature flag details for ".concat(e,":"),t),null}}async getAllFlags(){try{if(i&&Date.now()-i.timestamp<3e5)return i.flags;let e=await fetch("".concat(o,"/feature-flags"),{method:"GET",headers:{"Content-Type":"application/json"}});if(!e.ok)return console.warn("Failed to fetch all feature flags: ".concat(e.status)),{};let t=await e.json();return i={flags:t.flags,timestamp:Date.now()},Object.entries(t.flags).forEach(e=>{let[t,a]=e;l.set(t,{value:a,timestamp:Date.now()})}),t.flags}catch(e){return console.error("Error fetching all feature flags:",e),{}}}clearCache(){l.clear(),i=null}async preloadFlags(e){try{let t=e.map(e=>this.isEnabled(e));await Promise.all(t)}catch(e){console.error("Error preloading feature flags:",e)}}constructor(){}}let d=c.getInstance(),u=e=>d.isEnabled(e),g={all:["feature-flags"],flag:e=>[...g.all,"flag",e],flagDetails:e=>[...g.all,"details",e],allFlags:()=>[...g.all,"allFlags"]},m=async e=>{let t=await fetch("".concat(o,"/feature-flags/").concat(e),{method:"GET",headers:{"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to fetch feature flag ".concat(e,": ").concat(t.status));return(await t.json()).enabled},f=(e,t)=>{var a,s,n,o,l;let i=(0,r.I)({queryKey:g.flag(e),queryFn:()=>m(e),staleTime:null!=(a=null==t?void 0:t.staleTime)?a:3e5,gcTime:null!=(s=null==t?void 0:t.gcTime)?s:6e5,refetchOnWindowFocus:null!=(n=null==t?void 0:t.refetchOnWindowFocus)&&n,enabled:null==(o=null==t?void 0:t.enabled)||o,retry:(e,t)=>!(t instanceof Error&&t.message.includes("4"))&&e<3,meta:{errorMessage:"Failed to fetch feature flag: ".concat(e)}});return{enabled:null!=(l=i.data)&&l,loading:i.isLoading,...i}},p=(e,t)=>{var a,r,o;let l=(0,n.E)({queries:e.map(e=>{var a,s,r;return{queryKey:g.flag(e),queryFn:()=>m(e),staleTime:null!=(a=null==t?void 0:t.staleTime)?a:3e5,gcTime:null!=(s=null==t?void 0:t.gcTime)?s:6e5,enabled:null==(r=null==t?void 0:t.enabled)||r,retry:(e,t)=>!t.message.includes("4")&&e<3}})}),i=s.useMemo(()=>{let t={};return e.forEach((e,a)=>{var s;let r=l[a];t[e]=null!=(s=r.data)&&s}),t},[l,e]);return{flags:i,loading:l.some(e=>e.isLoading),error:null!=(o=null==(r=l.find(e=>e.error))||null==(a=r.error)?void 0:a.message)?o:null}}},22346:(e,t,a)=>{a.d(t,{w:()=>o});var s=a(95155);a(12115);var r=a(87489),n=a(59434);function o(e){let{className:t,orientation:a="horizontal",decorative:o=!0,...l}=e;return(0,s.jsx)(r.b,{"data-slot":"separator-root",decorative:o,orientation:a,className:(0,n.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...l})}},26126:(e,t,a)=>{a.d(t,{E:()=>i});var s=a(95155);a(12115);var r=a(99708),n=a(74466),o=a(59434);let l=(0,n.F)("inline-flex items-center justify-center rounded-lg border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground",new:"text-purple-600 dark:text-purple-300 bg-purple-600/30 dark:bg-purple-600/30",beta:"text-blue-600 dark:text-blue-300 bg-blue-600/30 dark:bg-blue-600/30",highlight:"text-green-800 dark:text-green-300 bg-green-600/30 dark:bg-green-600/30"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:a,asChild:n=!1,...i}=e,c=n?r.DX:"span";return(0,s.jsx)(c,{"data-slot":"badge",className:(0,o.cn)(l({variant:a}),t),...i})}},38198:(e,t,a)=>{a.d(t,{fJ:()=>m,gc:()=>y,_F:()=>g,h3:()=>p,gX:()=>x,XK:()=>v,jz:()=>b,Ae:()=>h});var s=a(99090),r=a(26715),n=a(56671),o=a(58350),l=a(43319);a(12115);var i=a(35695);let c=["\uD83E\uDD16","\uD83E\uDDE0","\uD83D\uDCA1","\uD83D\uDE80","⚡","\uD83D\uDD2E","\uD83C\uDFAF","\uD83D\uDEE1️","\uD83D\uDD27","\uD83C\uDFA8","\uD83D\uDCCA","\uD83D\uDCC8","\uD83D\uDD0D","\uD83C\uDF1F","✨","\uD83C\uDFAA","\uD83C\uDFAD","\uD83C\uDFA8","\uD83C\uDFAF","\uD83C\uDFB2","\uD83E\uDDE9","\uD83D\uDD2C","\uD83D\uDD2D","\uD83D\uDDFA️","\uD83E\uDDED","⚙️","\uD83D\uDEE0️","\uD83D\uDD29","\uD83D\uDD17","\uD83D\uDCE1","\uD83C\uDF10","\uD83D\uDCBB","\uD83D\uDDA5️","\uD83D\uDCF1","⌨️","\uD83D\uDDB1️","\uD83D\uDCBE","\uD83D\uDCBF","\uD83D\uDCC0","\uD83D\uDDC4️","\uD83D\uDCC2","\uD83D\uDCC1","\uD83D\uDDC2️","\uD83D\uDCCB","\uD83D\uDCCC","\uD83D\uDCCD","\uD83D\uDCCE","\uD83D\uDD87️","\uD83D\uDCCF","\uD83D\uDCD0","✂️","\uD83D\uDD8A️","\uD83D\uDD8B️","✒️","\uD83D\uDD8C️","\uD83D\uDD8D️","\uD83D\uDCDD","✏️","\uD83D\uDD10","\uD83D\uDD12","\uD83D\uDD13","\uD83D\uDD0F","\uD83D\uDD11","\uD83D\uDDDD️","\uD83D\uDD28","⛏️","⚒️","\uD83D\uDEE1️","\uD83C\uDFF9","\uD83C\uDFAF","\uD83C\uDFB0","\uD83C\uDFAE","\uD83D\uDD79️","\uD83C\uDFB2","♠️","♥️","♦️","♣️","\uD83C\uDCCF","\uD83C\uDC04","\uD83C\uDFB4","\uD83C\uDFAD","\uD83D\uDDBC️","\uD83C\uDFA8","\uD83E\uDDF5","\uD83E\uDDF6","\uD83C\uDFB8","\uD83C\uDFB9","\uD83C\uDFBA","\uD83C\uDFBB","\uD83E\uDD41","\uD83C\uDFAC","\uD83C\uDFA4","\uD83C\uDFA7","\uD83C\uDFBC","\uD83C\uDFB5","\uD83C\uDFB6","\uD83C\uDF99️","\uD83C\uDF9A️","\uD83C\uDF9B️","\uD83D\uDCFB","\uD83D\uDCFA","\uD83D\uDCF7","\uD83D\uDCF9","\uD83D\uDCFD️","\uD83C\uDF9E️","\uD83D\uDCDE","☎️","\uD83D\uDCDF","\uD83D\uDCE0","\uD83D\uDC8E","\uD83D\uDC8D","\uD83C\uDFC6","\uD83E\uDD47","\uD83E\uDD48","\uD83E\uDD49","\uD83C\uDFC5","\uD83C\uDF96️","\uD83C\uDFF5️","\uD83C\uDF97️","\uD83C\uDFAB","\uD83C\uDF9F️","\uD83C\uDFAA","\uD83C\uDFAD","\uD83C\uDFA8","\uD83C\uDFAC","\uD83C\uDFA4","\uD83C\uDFA7","\uD83C\uDFBC","\uD83C\uDFB9","\uD83E\uDDBE","\uD83E\uDDBF","\uD83E\uDDB4","\uD83D\uDC41️","\uD83E\uDDE0","\uD83E\uDEC0","\uD83E\uDEC1","\uD83E\uDDB7","\uD83E\uDDB4","\uD83D\uDC40"],d=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FECA57","#FF9FF3","#54A0FF","#48DBFB","#1DD1A1","#00D2D3","#5F27CD","#341F97","#EE5A24","#F368E0","#FF6348","#7BED9F","#70A1FF","#5352ED","#3742FA","#2ED573","#1E90FF","#FF1744","#D500F9","#00E676","#FF6D00","#00B8D4","#6C5CE7","#A29BFE","#FD79A8","#FDCB6E","#6C5CE7","#00B894","#00CEC9","#0984E3","#6C5CE7","#A29BFE","#74B9FF","#81ECEC","#55A3FF","#FD79A8","#FDCB6E","#FF7675","#E17055","#FAB1A0","#74B9FF","#A29BFE","#DFE6E9","#B2BEC3","#636E72"];var u=a(5416);let g=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,s.GQ)(o._.list(e),()=>(0,l.vt)(e),{staleTime:3e5,gcTime:6e5})()},m=e=>(0,s.GQ)(o._.detail(e),()=>(0,l.WU)(e),{enabled:!!e,staleTime:3e5,gcTime:6e5})(),f=()=>{let e=(0,r.jE)();return(0,s.Lx)(l.Sn,{onSuccess:t=>{e.invalidateQueries({queryKey:o._.lists()}),e.setQueryData(o._.detail(t.agent_id),t),n.oR.success("Agent created successfully")}})()},p=()=>{let e=(0,i.useRouter)(),t=f();return(0,s.Lx)(async e=>{let{avatar:a,avatar_color:s}={avatar:function(){let e=Math.floor(Math.random()*c.length);return c[e]}(),avatar_color:function(){let e=Math.floor(Math.random()*d.length);return d[e]}()},r={name:"New Agent",description:"",system_prompt:"You are a helpful assistant. Provide clear, accurate, and helpful responses to user queries.",avatar:a,avatar_color:s,configured_mcps:[],agentpress_tools:Object.fromEntries(Object.entries(u.n).map(e=>{let[t,a]=e;return[t,{enabled:a.enabled,description:a.description}]})),is_default:!1};return await t.mutateAsync(r)},{onSuccess:t=>{e.push("/agents/config/".concat(t.agent_id))},onError:e=>{console.error("Error creating agent:",e),n.oR.error("Failed to create agent. Please try again.")}})()},h=()=>{let e=(0,r.jE)();return(0,s.Lx)(e=>{let{agentId:t,...a}=e;return(0,l.x4)(t,a)},{onSuccess:(t,a)=>{e.setQueryData(o._.detail(a.agentId),t),e.invalidateQueries({queryKey:o._.lists()}),(void 0!==a.configured_mcps||void 0!==a.custom_mcps)&&(e.invalidateQueries({queryKey:["agent-tools",a.agentId]}),e.invalidateQueries({queryKey:["pipedream-tools",a.agentId]}),e.invalidateQueries({queryKey:["custom-mcp-tools",a.agentId]}),e.invalidateQueries({queryKey:["pipedream","available-tools"]}))}})()},x=()=>{let e=(0,r.jE)();return(0,s.Lx)(l.RB,{onSuccess:(t,a)=>{e.removeQueries({queryKey:o._.detail(a)}),e.invalidateQueries({queryKey:o._.lists()}),n.oR.success("Agent deleted successfully")}})()},v=()=>{let e=(0,r.jE)();return{optimisticallyUpdateAgent:(t,a)=>{e.setQueryData(o._.detail(t),e=>e?{...e,...a}:e)},revertOptimisticUpdate:t=>{e.invalidateQueries({queryKey:o._.detail(t)})}}},b=e=>(0,s.GQ)(o._.threadAgent(e),()=>(0,l.qU)(e),{enabled:!!e,staleTime:3e5,gcTime:6e5})(),y=e=>(0,s.GQ)(o._.builderChatHistory(e),()=>(0,l.Lm)(e),{enabled:!!e,retry:1})()},43319:(e,t,a)=>{a.d(t,{Lm:()=>g,RB:()=>d,Sn:()=>i,WU:()=>l,qU:()=>u,vt:()=>o,x4:()=>c});var s=a(52643),r=a(17711);let n="http://localhost:8000/api",o=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};try{var t,a;if(!await (0,r.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let o=(0,s.U)(),{data:{session:l}}=await o.auth.getSession();if(!l)throw Error("You must be logged in to get agents");let i=new URLSearchParams;e.page&&i.append("page",e.page.toString()),e.limit&&i.append("limit",e.limit.toString()),e.search&&i.append("search",e.search),e.sort_by&&i.append("sort_by",e.sort_by),e.sort_order&&i.append("sort_order",e.sort_order),void 0!==e.has_default&&i.append("has_default",e.has_default.toString()),void 0!==e.has_mcp_tools&&i.append("has_mcp_tools",e.has_mcp_tools.toString()),void 0!==e.has_agentpress_tools&&i.append("has_agentpress_tools",e.has_agentpress_tools.toString()),e.tools&&i.append("tools",e.tools);let c="".concat(n,"/agents").concat(i.toString()?"?".concat(i.toString()):""),d=await fetch(c,{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(l.access_token)}});if(!d.ok){let e=await d.json().catch(()=>({message:"Unknown error"}));throw Error(e.message||"HTTP ".concat(d.status,": ").concat(d.statusText))}let u=await d.json();return console.log("[API] Fetched agents:",(null==(t=u.agents)?void 0:t.length)||0,"total:",(null==(a=u.pagination)?void 0:a.total)||0),u}catch(e){throw console.error("Error fetching agents:",e),e}},l=async e=>{try{if(!await (0,r.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let t=(0,s.U)(),{data:{session:a}}=await t.auth.getSession();if(!a)throw Error("You must be logged in to get agent details");let o=await fetch("".concat(n,"/agents/").concat(e),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a.access_token)}});if(!o.ok){let e=await o.json().catch(()=>({message:"Unknown error"}));throw Error(e.message||"HTTP ".concat(o.status,": ").concat(o.statusText))}let l=await o.json();return console.log("[API] Fetched agent:",l.agent_id),l}catch(e){throw console.error("Error fetching agent:",e),e}},i=async e=>{try{if(!await (0,r.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let t=(0,s.U)(),{data:{session:a}}=await t.auth.getSession();if(!a)throw Error("You must be logged in to create an agent");let o=await fetch("".concat(n,"/agents"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a.access_token)},body:JSON.stringify(e)});if(!o.ok){let e=await o.json().catch(()=>({message:"Unknown error"}));throw Error(e.message||"HTTP ".concat(o.status,": ").concat(o.statusText))}let l=await o.json();return console.log("[API] Created agent:",l.agent_id),l}catch(e){throw console.error("Error creating agent:",e),e}},c=async(e,t)=>{try{if(!await (0,r.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let a=(0,s.U)(),{data:{session:o}}=await a.auth.getSession();if(!o)throw Error("You must be logged in to update an agent");let l=await fetch("".concat(n,"/agents/").concat(e),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(o.access_token)},body:JSON.stringify(t)});if(!l.ok){let e=await l.json().catch(()=>({message:"Unknown error"}));throw Error(e.message||"HTTP ".concat(l.status,": {response.statusText}"))}let i=await l.json();return console.log("[API] Updated agent:",i.agent_id),i}catch(e){throw console.error("Error updating agent:",e),e}},d=async e=>{try{if(!await (0,r.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let t=(0,s.U)(),{data:{session:a}}=await t.auth.getSession();if(!a)throw Error("You must be logged in to delete an agent");let o=await fetch("".concat(n,"/agents/").concat(e),{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a.access_token)}});if(!o.ok){let e=await o.json().catch(()=>({message:"Unknown error"}));throw Error(e.message||"HTTP ".concat(o.status,": ").concat(o.statusText))}console.log("[API] Deleted agent:",e)}catch(e){throw console.error("Error deleting agent:",e),e}},u=async e=>{try{if(!await (0,r.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let t=(0,s.U)(),{data:{session:a}}=await t.auth.getSession();if(!a)throw Error("You must be logged in to get thread agent");let o=await fetch("".concat(n,"/thread/").concat(e,"/agent"),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a.access_token)}});if(!o.ok){let e=await o.json().catch(()=>({message:"Unknown error"}));throw Error(e.message||"HTTP ".concat(o.status,": ").concat(o.statusText))}let l=await o.json();return console.log("[API] Fetched thread agent:",e),l}catch(e){throw console.error("Error fetching thread agent:",e),e}},g=async e=>{try{if(!await (0,r.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let t=(0,s.U)(),{data:{session:a}}=await t.auth.getSession();if(!a)throw Error("You must be logged in to get agent builder chat history");let o=await fetch("".concat(n,"/agents/").concat(e,"/builder-chat-history"),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a.access_token)}});if(!o.ok){let e=await o.json().catch(()=>({message:"Unknown error"}));throw Error(e.message||"HTTP ".concat(o.status,": ").concat(o.statusText))}let l=await o.json();return console.log("[API] Fetched agent builder chat history:",e,l.messages.length),l}catch(e){throw console.error("Error fetching agent builder chat history:",e),e}}},44838:(e,t,a)=>{a.d(t,{I:()=>d,SQ:()=>c,V0:()=>p,_2:()=>u,hO:()=>g,lp:()=>m,mB:()=>f,rI:()=>l,ty:()=>i});var s=a(95155);a(12115);var r=a(48698),n=a(5196),o=a(59434);function l(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"dropdown-menu",...t})}function i(e){let{...t}=e;return(0,s.jsx)(r.l9,{"data-slot":"dropdown-menu-trigger",...t})}function c(e){let{className:t,sideOffset:a=4,...n}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsx)(r.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-xl border p-1 shadow-md",t),...n})})}function d(e){let{...t}=e;return(0,s.jsx)(r.YJ,{"data-slot":"dropdown-menu-group",...t})}function u(e){let{className:t,inset:a,variant:n="default",...l}=e;return(0,s.jsx)(r.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":n,className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l})}function g(e){let{className:t,children:a,checked:l,...i}=e;return(0,s.jsxs)(r.H_,{"data-slot":"dropdown-menu-checkbox-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),checked:l,...i,children:[(0,s.jsx)("span",{className:"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),a]})}function m(e){let{className:t,inset:a,...n}=e;return(0,s.jsx)(r.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,o.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...n})}function f(e){let{className:t,...a}=e;return(0,s.jsx)(r.wv,{"data-slot":"dropdown-menu-separator",className:(0,o.cn)("bg-border -mx-1 my-1 h-px",t),...a})}function p(e){let{className:t,...a}=e;return(0,s.jsx)("span",{"data-slot":"dropdown-menu-shortcut",className:(0,o.cn)("text-muted-foreground ml-auto text-xs tracking-widest",t),...a})}},54165:(e,t,a)=>{a.d(t,{Cf:()=>u,Es:()=>m,L3:()=>f,LC:()=>d,c7:()=>g,lG:()=>l,rr:()=>p,zM:()=>i});var s=a(95155);a(12115);var r=a(15452),n=a(54416),o=a(59434);function l(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"dialog",...t})}function i(e){let{...t}=e;return(0,s.jsx)(r.l9,{"data-slot":"dialog-trigger",...t})}function c(e){let{...t}=e;return(0,s.jsx)(r.ZL,{"data-slot":"dialog-portal",...t})}function d(e){let{className:t,...a}=e;return(0,s.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50 backdrop-blur-xs",t),...a})}function u(e){let{className:t,children:a,...l}=e;return(0,s.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,s.jsx)(d,{}),(0,s.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-2xl border p-6 shadow-lg duration-200",t),...l,children:[a,(0,s.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(n.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function g(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function m(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function f(e){let{className:t,...a}=e;return(0,s.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg leading-none font-semibold",t),...a})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",t),...a})}},55365:(e,t,a)=>{a.d(t,{Fc:()=>l,TN:()=>c,XL:()=>i});var s=a(95155);a(12115);var r=a(74466),n=a(59434);let o=(0,r.F)("relative w-full rounded-xl border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-card text-card-foreground",destructive:"text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:a,...r}=e;return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(o({variant:a}),t),...r})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-title",className:(0,n.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...a})}},58350:(e,t,a)=>{a.d(t,{_:()=>n});var s=a(99090);let r=["agents"],n=(0,s.DY)({all:r,lists:()=>[...r,"list"],list:e=>[...r,"list",e],details:()=>[...r,"detail"],detail:e=>[...r,"detail",e],threadAgents:()=>[...r,"thread-agent"],threadAgent:e=>[...r,"thread-agent",e],builderChatHistory:e=>[...r,"builderChatHistory",e]})},59409:(e,t,a)=>{a.d(t,{bq:()=>u,eb:()=>m,gC:()=>g,l6:()=>c,yv:()=>d});var s=a(95155);a(12115);var r=a(31992),n=a(66474),o=a(5196),l=a(47863),i=a(59434);function c(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...t})}function d(e){let{...t}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:o,...l}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger","data-size":a,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...l,children:[o,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function g(e){let{className:t,children:a,position:n="popper",...o}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...o,children:[(0,s.jsx)(f,{}),(0,s.jsx)(r.LM,{className:(0,i.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(p,{})]})})}function m(e){let{className:t,children:a,...n}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(o.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:a})]})}function f(e){let{className:t,...a}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(l.A,{className:"size-4"})})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(n.A,{className:"size-4"})})}},62523:(e,t,a)=>{a.d(t,{p:()=>n});var s=a(95155);a(12115);var r=a(59434);function n(e){let{className:t,type:a,...n}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},66695:(e,t,a)=>{a.d(t,{BT:()=>i,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>o,wL:()=>d});var s=a(95155);a(12115);var r=a(59434);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6 [.border-t]:pt-6",t),...a})}},78948:(e,t,a)=>{a.d(t,{R:()=>_});var s=a(95155),r=a(12115),n=a(30285),o=a(62523),l=a(85057),i=a(80333),c=a(66695),d=a(22346),u=a(54165),g=a(59409),m=a(43453),f=a(84616),p=a(51154),h=a(92138),x=a(53904),v=a(71539),b=a(56671),y=a(59434),w=a(2417),j=a(2508);let _=e=>{let{app:t,open:a,onOpenChange:_,onComplete:N,mode:E="full"}=e,[k,C]=(0,r.useState)("profile"),[F,A]=(0,r.useState)(""),[T,z]=(0,r.useState)(!1),[S,P]=(0,r.useState)(""),[D,q]=(0,r.useState)(new Set),[B,L]=(0,r.useState)([]),[U,Q]=(0,r.useState)(!1),[I,M]=(0,r.useState)(!1),[R,K]=(0,r.useState)(!1),{data:G,refetch:O}=(0,w.h4)({app_slug:t.name_slug}),Y=(0,w.O4)(),H=(0,w.wG)(),V=(0,r.useMemo)(()=>(null==G?void 0:G.filter(e=>e.is_connected))||[],[G]),W=(0,r.useMemo)(()=>null==G?void 0:G.find(e=>e.profile_id===F),[G,F]);(0,r.useEffect)(()=>{a&&(C("profile"),A(""),z(!1),P(""),q(new Set),L([]))},[a]),(0,r.useEffect)(()=>{a&&1===V.length&&!F&&A(V[0].profile_id)},[a,V,F]);let Z=(0,r.useCallback)(async()=>{if(!S.trim())return void b.oR.error("Please enter a profile name");M(!0);try{let e={profile_name:S.trim(),app_slug:t.name_slug,app_name:t.name,is_default:0===V.length},a=await Y.mutateAsync(e);await H.mutateAsync({profileId:a.profile_id,app:t.name_slug}),await O(),A(a.profile_id),z(!1),P(""),b.oR.success("Profile created and connected successfully!"),"profile-only"===E?(N(a.profile_id,[],t.name,t.name_slug),_(!1)):$()}catch(e){console.error("Error creating profile:",e)}finally{M(!1)}},[S,t.name_slug,t.name,V.length,Y,H,O,E,N,_]),$=(0,r.useCallback)(async()=>{if(F&&W){Q(!0),C("tools");try{let e=(await j.A.discoverMCPServers(W.external_user_id,t.name_slug)).find(e=>e.app_slug===t.name_slug);(null==e?void 0:e.available_tools)&&(L(e.available_tools),q(new Set(e.available_tools.map(e=>e.name))))}catch(e){console.error("Error fetching tools:",e),b.oR.error("Failed to load tools")}finally{Q(!1)}}},[F,W,t.name_slug]),J=(0,r.useCallback)(async()=>{if(!F||0===D.size)return void b.oR.error("Please select at least one tool");K(!0);try{N(F,Array.from(D),t.name,t.name_slug),_(!1)}catch(e){console.error("Error completing connection:",e)}finally{K(!1)}},[F,D,N,t.name,t.name_slug,_]),X=(0,r.useCallback)(async()=>{if(!F)return void b.oR.error("Please select a profile");K(!0);try{N(F,[],t.name,t.name_slug),_(!1)}catch(e){console.error("Error completing connection:",e)}finally{K(!1)}},[F,N,t.name,t.name_slug,_]),ee=(0,r.useCallback)(e=>{q(t=>{let a=new Set(t);return a.has(e)?a.delete(e):a.add(e),a})},[]),et=(0,r.useCallback)(e=>{P(e.target.value)},[]),ea=(0,r.useCallback)(e=>{"Enter"===e.key&&Z()},[Z]),es=(0,r.useMemo)(()=>(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"text-lg font-semibold",children:["Connect to ",t.name]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"profile-only"===E?"Create a new profile to connect your account":V.length>0?"Select a profile or create a new one to connect different accounts":"Create your first profile to get started"})]}),"profile-only"!==E&&V.length>0&&!T&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.Label,{htmlFor:"profile-select",children:"Select Profile"}),(0,s.jsxs)(g.l6,{value:F,onValueChange:A,children:[(0,s.jsx)(g.bq,{children:(0,s.jsx)(g.yv,{placeholder:"Choose a profile",children:W&&(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{children:W.profile_name}),(0,s.jsx)(m.A,{className:"h-3 w-3 text-green-500"})]})})}),(0,s.jsx)(g.gC,{children:V.map(e=>(0,s.jsx)(g.eb,{value:e.profile_id,children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{children:e.profile_name}),(0,s.jsx)("div",{className:"h-2 w-2 bg-green-500 rounded-full"})]})},e.profile_id))})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(d.w,{className:"flex-1"}),(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:"OR"}),(0,s.jsx)(d.w,{className:"flex-1"})]}),(0,s.jsxs)(n.$,{variant:"outline",onClick:()=>z(!0),className:"w-full",children:[(0,s.jsx)(f.A,{className:"h-4 w-4"}),"Create New Profile"]})]}),("profile-only"===E||0===V.length||T)&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.Label,{htmlFor:"profile-name",children:"Profile Name"}),(0,s.jsx)(o.p,{id:"profile-name",placeholder:"e.g., Personal Account, Work Account",value:S,onChange:et,onKeyDown:ea,autoFocus:"profile-only"===E||T})]}),(0,s.jsxs)("div",{className:"flex gap-3",children:["profile-only"!==E&&T&&(0,s.jsx)(n.$,{variant:"outline",onClick:()=>{z(!1),P("")},className:"flex-1",children:"Cancel"}),(0,s.jsx)(n.$,{onClick:Z,disabled:!S.trim()||I,className:"profile-only"===E?"w-full":"flex-1",children:I?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Creating..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(f.A,{className:"h-4 w-4"}),"Create & Connect"]})})]})]}),"profile-only"!==E&&F&&!T&&(0,s.jsx)("div",{className:"pt-4 border-t",children:(0,s.jsx)(n.$,{onClick:$,disabled:!F||R,className:"w-full",children:R?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Connecting..."]}):(0,s.jsxs)(s.Fragment,{children:["Continue to Tools",(0,s.jsx)(h.A,{className:"h-4 w-4"})]})})})]}),[t.name,V,T,F,W,S,I,et,ea,Z,$,E,X,R]),er=(0,r.useMemo)(()=>(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)("div",{children:(0,s.jsx)("div",{className:"flex items-center gap-2",children:(0,s.jsx)(n.$,{variant:"link",size:"sm",onClick:()=>C("profile"),className:"mb-4 p-0 h-auto font-normal text-muted-foreground hover:text-foreground",children:"← Back to Profile"})})}),U?(0,s.jsx)("div",{className:"flex items-center justify-center py-8",children:(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(p.A,{className:"h-5 w-5 animate-spin"}),(0,s.jsx)("span",{className:"text-sm",children:"Loading tools..."})]})}):0===B.length?(0,s.jsxs)("div",{className:"text-center py-8",children:[(0,s.jsx)("div",{className:"text-4xl mb-3",children:"\uD83D\uDD27"}),(0,s.jsx)("h4",{className:"font-medium mb-2",children:"No tools available"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"This app doesn't have any tools available yet."}),(0,s.jsxs)(n.$,{variant:"outline",onClick:$,children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Refresh"]})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("span",{className:"text-sm font-medium",children:[D.size," of ",B.length," tools selected"]}),(0,s.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>{D.size===B.length?q(new Set):q(new Set(B.map(e=>e.name)))},children:D.size===B.length?"Deselect All":"Select All"})]}),(0,s.jsx)("div",{className:"space-y-2 max-h-64 overflow-y-auto",children:B.map(e=>{let t=D.has(e.name);return(0,s.jsx)(c.Zp,{className:(0,y.cn)("p-0 border cursor-pointer transition-colors",t?"bg-muted/50":"hover:bg-muted/20"),onClick:()=>ee(e.name),children:(0,s.jsx)(c.Wu,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,s.jsx)("div",{className:"flex-1 min-w-0",children:(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,s.jsx)("h4",{className:"font-medium text-sm",children:e.name}),t&&(0,s.jsx)(m.A,{className:"h-4 w-4 text-green-500"})]})}),(0,s.jsx)(i.d,{checked:t,onCheckedChange:()=>ee(e.name),onClick:e=>e.stopPropagation()})]})})},e.name)})})]}),(0,s.jsx)("div",{className:"pt-4 border-t",children:(0,s.jsx)(n.$,{onClick:J,disabled:0===D.size||R,className:"w-full",children:R?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Connecting..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(v.A,{className:"h-4 w-4"}),"Connect with ",D.size," Tool",1!==D.size?"s":""]})})})]})]}),[t.name,U,B,D,ee,J,R,$]);return(0,s.jsx)(u.lG,{open:a,onOpenChange:_,children:(0,s.jsxs)(u.Cf,{className:"sm:max-w-lg max-h-[90vh] overflow-y-auto",children:[(0,s.jsxs)(u.c7,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"h-10 w-10 flex-shrink-0 rounded-lg bg-muted flex items-center justify-center",children:t.img_src?(0,s.jsx)("img",{src:t.img_src,alt:t.name,className:"h-6 w-6 object-cover rounded"}):(0,s.jsx)("span",{className:"text-sm font-semibold",children:t.name.charAt(0)})}),(0,s.jsxs)("div",{children:[(0,s.jsx)(u.L3,{className:"text-left",children:t.name}),(0,s.jsx)(u.rr,{className:"text-left",children:"profile-only"===E?"Connect your account to continue with installation":t.description})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:(0,y.cn)("h-2 w-2 rounded-full","profile"===k?"bg-primary":"bg-muted")}),"full"===E&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"h-px bg-muted flex-1"}),(0,s.jsx)("div",{className:(0,y.cn)("h-2 w-2 rounded-full","tools"===k?"bg-primary":"bg-muted")})]})]})]}),(0,s.jsx)("div",{className:"mt-6",children:"profile"===k?es:er})]})})}},80333:(e,t,a)=>{a.d(t,{d:()=>o});var s=a(95155);a(12115);var r=a(4884),n=a(59434);function o(e){let{className:t,...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"switch",className:(0,n.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 dark:data-[state=unchecked]:bg-input/80 inline-flex h-[1.15rem] w-8 shrink-0 items-center rounded-full border border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,s.jsx)(r.zi,{"data-slot":"switch-thumb",className:(0,n.cn)("bg-background dark:data-[state=unchecked]:bg-foreground dark:data-[state=checked]:bg-primary-foreground pointer-events-none block size-4 rounded-full ring-0 transition-transform data-[state=checked]:translate-x-[calc(100%-2px)] data-[state=unchecked]:translate-x-0")})})}},85057:(e,t,a)=>{a.d(t,{Label:()=>o});var s=a(95155);a(12115);var r=a(40968),n=a(59434);function o(e){let{className:t,...a}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},90010:(e,t,a)=>{a.d(t,{$v:()=>p,EO:()=>u,Lt:()=>l,Rx:()=>h,Zr:()=>x,ck:()=>m,r7:()=>f,tv:()=>i,wd:()=>g});var s=a(95155);a(12115);var r=a(17649),n=a(59434),o=a(30285);function l(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"alert-dialog",...t})}function i(e){let{...t}=e;return(0,s.jsx)(r.l9,{"data-slot":"alert-dialog-trigger",...t})}function c(e){let{...t}=e;return(0,s.jsx)(r.ZL,{"data-slot":"alert-dialog-portal",...t})}function d(e){let{className:t,...a}=e;return(0,s.jsx)(r.hJ,{"data-slot":"alert-dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function u(e){let{className:t,...a}=e;return(0,s.jsxs)(c,{children:[(0,s.jsx)(d,{}),(0,s.jsx)(r.UC,{"data-slot":"alert-dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-2xl border p-6 shadow-lg duration-200 sm:max-w-lg",t),...a})]})}function g(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function m(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function f(e){let{className:t,...a}=e;return(0,s.jsx)(r.hE,{"data-slot":"alert-dialog-title",className:(0,n.cn)("text-lg font-semibold",t),...a})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(r.VY,{"data-slot":"alert-dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...a})}function h(e){let{className:t,...a}=e;return(0,s.jsx)(r.rc,{className:(0,n.cn)((0,o.r)(),t),...a})}function x(e){let{className:t,...a}=e;return(0,s.jsx)(r.ZD,{className:(0,n.cn)((0,o.r)({variant:"outline"}),t),...a})}}}]);