{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/lib/utils/unicode.ts"], "sourcesContent": ["/**\r\n * Normalize filename to NFC (Normalized Form Composed) to ensure consistent\r\n * Unicode representation across different systems, especially macOS which\r\n * can use NFD (Normalized Form Decomposed).\r\n * \r\n * @param filename The filename to normalize\r\n * @returns The filename normalized to NFC form\r\n */\r\nexport const normalizeFilenameToNFC = (filename: string): string => {\r\n  try {\r\n    // Normalize to NFC (Normalized Form Composed)\r\n    return filename.normalize('NFC');\r\n  } catch (error) {\r\n    console.warn('Failed to normalize filename to NFC:', filename, error);\r\n    return filename;\r\n  }\r\n};\r\n\r\n/**\r\n * Normalize file path to NFC (Normalized Form Composed) to ensure consistent\r\n * Unicode representation across different systems.\r\n * \r\n * @param path The file path to normalize\r\n * @returns The path with all components normalized to NFC form\r\n */\r\nexport const normalizePathToNFC = (path: string): string => {\r\n  try {\r\n    // Normalize to NFC (Normalized Form Composed)\r\n    return path.normalize('NFC');\r\n  } catch (error) {\r\n    console.warn('Failed to normalize path to NFC:', path, error);\r\n    return path;\r\n  }\r\n}; "], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACM,MAAM,yBAAyB,CAAC;IACrC,IAAI;QACF,8CAA8C;QAC9C,OAAO,SAAS,SAAS,CAAC;IAC5B,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,wCAAwC,UAAU;QAC/D,OAAO;IACT;AACF;AASO,MAAM,qBAAqB,CAAC;IACjC,IAAI;QACF,8CAA8C;QAC9C,OAAO,KAAK,SAAS,CAAC;IACxB,EAAE,OAAO,OAAO;QACd,QAAQ,IAAI,CAAC,oCAAoC,MAAM;QACvD,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/lib/feature-flags.ts"], "sourcesContent": ["import React from 'react';\r\nimport { useQuery, useQueries } from '@tanstack/react-query';\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || '';\r\n\r\nexport interface FeatureFlag {\r\n  flag_name: string;\r\n  enabled: boolean;\r\n  details?: {\r\n    description?: string;\r\n    updated_at?: string;\r\n  } | null;\r\n}\r\n\r\nexport interface FeatureFlagsResponse {\r\n  flags: Record<string, boolean>;\r\n}\r\n\r\nconst flagCache = new Map<string, { value: boolean; timestamp: number }>();\r\nconst CACHE_DURATION = 5 * 60 * 1000;\r\n\r\nlet globalFlagsCache: { flags: Record<string, boolean>; timestamp: number } | null = null;\r\n\r\nexport class FeatureFlagManager {\r\n  private static instance: FeatureFlagManager;\r\n  \r\n  private constructor() {}\r\n  \r\n  static getInstance(): FeatureFlagManager {\r\n    if (!FeatureFlagManager.instance) {\r\n      FeatureFlagManager.instance = new FeatureFlagManager();\r\n    }\r\n    return FeatureFlagManager.instance;\r\n  }\r\n\r\n  async isEnabled(flagName: string): Promise<boolean> {\r\n    try {\r\n      const cached = flagCache.get(flagName);\r\n      if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {\r\n        return cached.value;\r\n      }\r\n      const response = await fetch(`${API_URL}/feature-flags/${flagName}`, {\r\n        method: 'GET',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n      });\r\n      if (!response.ok) {\r\n        console.warn(`Failed to fetch feature flag ${flagName}: ${response.status}`);\r\n        return false;\r\n      }\r\n      \r\n      const data: FeatureFlag = await response.json();\r\n      \r\n      flagCache.set(flagName, {\r\n        value: data.enabled,\r\n        timestamp: Date.now(),\r\n      });\r\n      \r\n      return data.enabled;\r\n    } catch (error) {\r\n      console.error(`Error checking feature flag ${flagName}:`, error);\r\n      return false;\r\n    }\r\n  }\r\n  \r\n  async getFlagDetails(flagName: string): Promise<FeatureFlag | null> {\r\n    try {\r\n      const response = await fetch(`${API_URL}/feature-flags/${flagName}`, {\r\n        method: 'GET',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n      });\r\n      \r\n      if (!response.ok) {\r\n        console.warn(`Failed to fetch feature flag details for ${flagName}: ${response.status}`);\r\n        return null;\r\n      }\r\n      \r\n      const data: FeatureFlag = await response.json();\r\n      return data;\r\n    } catch (error) {\r\n      console.error(`Error fetching feature flag details for ${flagName}:`, error);\r\n      return null;\r\n    }\r\n  }\r\n  \r\n  async getAllFlags(): Promise<Record<string, boolean>> {\r\n    try {\r\n      if (globalFlagsCache && Date.now() - globalFlagsCache.timestamp < CACHE_DURATION) {\r\n        return globalFlagsCache.flags;\r\n      }\r\n      \r\n      const response = await fetch(`${API_URL}/feature-flags`, {\r\n        method: 'GET',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n      });\r\n      \r\n      if (!response.ok) {\r\n        console.warn(`Failed to fetch all feature flags: ${response.status}`);\r\n        return {};\r\n      }\r\n      \r\n      const data: FeatureFlagsResponse = await response.json();\r\n      globalFlagsCache = {\r\n        flags: data.flags,\r\n        timestamp: Date.now(),\r\n      };\r\n      \r\n      Object.entries(data.flags).forEach(([flagName, enabled]) => {\r\n        flagCache.set(flagName, {\r\n          value: enabled,\r\n          timestamp: Date.now(),\r\n        });\r\n      });\r\n      \r\n      return data.flags;\r\n    } catch (error) {\r\n      console.error('Error fetching all feature flags:', error);\r\n      return {};\r\n    }\r\n  }\r\n\r\n  clearCache(): void {\r\n    flagCache.clear();\r\n    globalFlagsCache = null;\r\n  }\r\n\r\n  async preloadFlags(flagNames: string[]): Promise<void> {\r\n    try {\r\n      const promises = flagNames.map(flagName => this.isEnabled(flagName));\r\n      await Promise.all(promises);\r\n    } catch (error) {\r\n      console.error('Error preloading feature flags:', error);\r\n    }\r\n  }\r\n}\r\n\r\nconst featureFlagManager = FeatureFlagManager.getInstance();\r\n\r\nexport const isEnabled = (flagName: string): Promise<boolean> => {\r\n  return featureFlagManager.isEnabled(flagName);\r\n};\r\n\r\nexport const isFlagEnabled = isEnabled;\r\n\r\nexport const getFlagDetails = (flagName: string): Promise<FeatureFlag | null> => {\r\n  return featureFlagManager.getFlagDetails(flagName);\r\n};\r\n\r\nexport const getAllFlags = (): Promise<Record<string, boolean>> => {\r\n  return featureFlagManager.getAllFlags();\r\n};\r\n\r\nexport const clearFlagCache = (): void => {\r\n  featureFlagManager.clearCache();\r\n};\r\n\r\nexport const preloadFlags = (flagNames: string[]): Promise<void> => {\r\n  return featureFlagManager.preloadFlags(flagNames);\r\n};\r\n\r\n// React Query key factories\r\nexport const featureFlagKeys = {\r\n  all: ['feature-flags'] as const,\r\n  flag: (flagName: string) => [...featureFlagKeys.all, 'flag', flagName] as const,\r\n  flagDetails: (flagName: string) => [...featureFlagKeys.all, 'details', flagName] as const,\r\n  allFlags: () => [...featureFlagKeys.all, 'allFlags'] as const,\r\n};\r\n\r\n// Query functions\r\nconst fetchFeatureFlag = async (flagName: string): Promise<boolean> => {\r\n  const response = await fetch(`${API_URL}/feature-flags/${flagName}`, {\r\n    method: 'GET',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n  });\r\n  \r\n  if (!response.ok) {\r\n    throw new Error(`Failed to fetch feature flag ${flagName}: ${response.status}`);\r\n  }\r\n  \r\n  const data: FeatureFlag = await response.json();\r\n  return data.enabled;\r\n};\r\n\r\nconst fetchFeatureFlagDetails = async (flagName: string): Promise<FeatureFlag> => {\r\n  const response = await fetch(`${API_URL}/feature-flags/${flagName}`, {\r\n    method: 'GET',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n  });\r\n  \r\n  if (!response.ok) {\r\n    throw new Error(`Failed to fetch feature flag details for ${flagName}: ${response.status}`);\r\n  }\r\n  \r\n  const data: FeatureFlag = await response.json();\r\n  return data;\r\n};\r\n\r\nconst fetchAllFeatureFlags = async (): Promise<Record<string, boolean>> => {\r\n  const response = await fetch(`${API_URL}/feature-flags`, {\r\n    method: 'GET',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n    },\r\n  });\r\n  \r\n  if (!response.ok) {\r\n    throw new Error(`Failed to fetch all feature flags: ${response.status}`);\r\n  }\r\n  \r\n  const data: FeatureFlagsResponse = await response.json();\r\n  return data.flags;\r\n};\r\n\r\n// React Query Hooks\r\nexport const useFeatureFlag = (flagName: string, options?: {\r\n  enabled?: boolean;\r\n  staleTime?: number;\r\n  gcTime?: number;\r\n  refetchOnWindowFocus?: boolean;\r\n}) => {\r\n  const query = useQuery({\r\n    queryKey: featureFlagKeys.flag(flagName),\r\n    queryFn: () => fetchFeatureFlag(flagName),\r\n    staleTime: options?.staleTime ?? 5 * 60 * 1000, // 5 minutes\r\n    gcTime: options?.gcTime ?? 10 * 60 * 1000, // 10 minutes\r\n    refetchOnWindowFocus: options?.refetchOnWindowFocus ?? false,\r\n    enabled: options?.enabled ?? true,\r\n    retry: (failureCount, error) => {\r\n      // Don't retry on 4xx errors, but retry on network errors\r\n      if (error instanceof Error && error.message.includes('4')) {\r\n        return false;\r\n      }\r\n      return failureCount < 3;\r\n    },\r\n    meta: {\r\n      errorMessage: `Failed to fetch feature flag: ${flagName}`,\r\n    },\r\n  });\r\n\r\n  // Return backward-compatible interface\r\n  return {\r\n    enabled: query.data ?? false,\r\n    loading: query.isLoading,\r\n    // Also expose React Query properties for advanced usage\r\n    ...query,\r\n  };\r\n};\r\n\r\nexport const useFeatureFlagDetails = (flagName: string, options?: {\r\n  enabled?: boolean;\r\n  staleTime?: number;\r\n  gcTime?: number;\r\n}) => {\r\n  return useQuery({\r\n    queryKey: featureFlagKeys.flagDetails(flagName),\r\n    queryFn: () => fetchFeatureFlagDetails(flagName),\r\n    staleTime: options?.staleTime ?? 5 * 60 * 1000, // 5 minutes\r\n    gcTime: options?.gcTime ?? 10 * 60 * 1000, // 10 minutes\r\n    enabled: options?.enabled ?? true,\r\n    retry: (failureCount, error) => {\r\n      if (error instanceof Error && error.message.includes('4')) {\r\n        return false;\r\n      }\r\n      return failureCount < 3;\r\n    },\r\n  });\r\n};\r\n\r\nexport const useAllFeatureFlags = (options?: {\r\n  enabled?: boolean;\r\n  staleTime?: number;\r\n  gcTime?: number;\r\n}) => {\r\n  return useQuery({\r\n    queryKey: featureFlagKeys.allFlags(),\r\n    queryFn: fetchAllFeatureFlags,\r\n    staleTime: options?.staleTime ?? 5 * 60 * 1000, // 5 minutes\r\n    gcTime: options?.gcTime ?? 10 * 60 * 1000, // 10 minutes\r\n    enabled: options?.enabled ?? true,\r\n    retry: (failureCount, error) => {\r\n      if (error instanceof Error && error.message.includes('4')) {\r\n        return false;\r\n      }\r\n      return failureCount < 3;\r\n    },\r\n  });\r\n};\r\n\r\nexport const useFeatureFlags = (flagNames: string[], options?: {\r\n  enabled?: boolean;\r\n  staleTime?: number;\r\n  gcTime?: number;\r\n}) => {\r\n  const queries = useQueries({\r\n    queries: flagNames.map((flagName) => ({\r\n      queryKey: featureFlagKeys.flag(flagName),\r\n      queryFn: () => fetchFeatureFlag(flagName),\r\n      staleTime: options?.staleTime ?? 5 * 60 * 1000, // 5 minutes\r\n      gcTime: options?.gcTime ?? 10 * 60 * 1000, // 10 minutes\r\n      enabled: options?.enabled ?? true,\r\n      retry: (failureCount: number, error: Error) => {\r\n        if (error.message.includes('4')) {\r\n          return false;\r\n        }\r\n        return failureCount < 3;\r\n      },\r\n    })),\r\n  });\r\n\r\n  // Transform the results into a more convenient format\r\n  const flags = React.useMemo(() => {\r\n    const result: Record<string, boolean> = {};\r\n    flagNames.forEach((flagName, index) => {\r\n      const query = queries[index];\r\n      result[flagName] = query.data ?? false;\r\n    });\r\n    return result;\r\n  }, [queries, flagNames]);\r\n\r\n  const loading = queries.some(query => query.isLoading);\r\n  const error = queries.find(query => query.error)?.error?.message ?? null;\r\n\r\n  return { flags, loading, error };\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAGgB;AAHhB;AACA;AAAA;;;;AAEA,MAAM,UAAU,iEAAuC;AAevD,MAAM,YAAY,IAAI;AACtB,MAAM,iBAAiB,IAAI,KAAK;AAEhC,IAAI,mBAAiF;AAE9E,MAAM;IACX,OAAe,SAA6B;IAE5C,aAAsB,CAAC;IAEvB,OAAO,cAAkC;QACvC,IAAI,CAAC,mBAAmB,QAAQ,EAAE;YAChC,mBAAmB,QAAQ,GAAG,IAAI;QACpC;QACA,OAAO,mBAAmB,QAAQ;IACpC;IAEA,MAAM,UAAU,QAAgB,EAAoB;QAClD,IAAI;YACF,MAAM,SAAS,UAAU,GAAG,CAAC;YAC7B,IAAI,UAAU,KAAK,GAAG,KAAK,OAAO,SAAS,GAAG,gBAAgB;gBAC5D,OAAO,OAAO,KAAK;YACrB;YACA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,eAAe,EAAE,UAAU,EAAE;gBACnE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YACA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,QAAQ,IAAI,CAAC,CAAC,6BAA6B,EAAE,SAAS,EAAE,EAAE,SAAS,MAAM,EAAE;gBAC3E,OAAO;YACT;YAEA,MAAM,OAAoB,MAAM,SAAS,IAAI;YAE7C,UAAU,GAAG,CAAC,UAAU;gBACtB,OAAO,KAAK,OAAO;gBACnB,WAAW,KAAK,GAAG;YACrB;YAEA,OAAO,KAAK,OAAO;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,SAAS,CAAC,CAAC,EAAE;YAC1D,OAAO;QACT;IACF;IAEA,MAAM,eAAe,QAAgB,EAA+B;QAClE,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,eAAe,EAAE,UAAU,EAAE;gBACnE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,QAAQ,IAAI,CAAC,CAAC,yCAAyC,EAAE,SAAS,EAAE,EAAE,SAAS,MAAM,EAAE;gBACvF,OAAO;YACT;YAEA,MAAM,OAAoB,MAAM,SAAS,IAAI;YAC7C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,wCAAwC,EAAE,SAAS,CAAC,CAAC,EAAE;YACtE,OAAO;QACT;IACF;IAEA,MAAM,cAAgD;QACpD,IAAI;YACF,IAAI,oBAAoB,KAAK,GAAG,KAAK,iBAAiB,SAAS,GAAG,gBAAgB;gBAChF,OAAO,iBAAiB,KAAK;YAC/B;YAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,cAAc,CAAC,EAAE;gBACvD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,QAAQ,IAAI,CAAC,CAAC,mCAAmC,EAAE,SAAS,MAAM,EAAE;gBACpE,OAAO,CAAC;YACV;YAEA,MAAM,OAA6B,MAAM,SAAS,IAAI;YACtD,mBAAmB;gBACjB,OAAO,KAAK,KAAK;gBACjB,WAAW,KAAK,GAAG;YACrB;YAEA,OAAO,OAAO,CAAC,KAAK,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC,UAAU,QAAQ;gBACrD,UAAU,GAAG,CAAC,UAAU;oBACtB,OAAO;oBACP,WAAW,KAAK,GAAG;gBACrB;YACF;YAEA,OAAO,KAAK,KAAK;QACnB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO,CAAC;QACV;IACF;IAEA,aAAmB;QACjB,UAAU,KAAK;QACf,mBAAmB;IACrB;IAEA,MAAM,aAAa,SAAmB,EAAiB;QACrD,IAAI;YACF,MAAM,WAAW,UAAU,GAAG,CAAC,CAAA,WAAY,IAAI,CAAC,SAAS,CAAC;YAC1D,MAAM,QAAQ,GAAG,CAAC;QACpB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;AACF;AAEA,MAAM,qBAAqB,mBAAmB,WAAW;AAElD,MAAM,YAAY,CAAC;IACxB,OAAO,mBAAmB,SAAS,CAAC;AACtC;AAEO,MAAM,gBAAgB;AAEtB,MAAM,iBAAiB,CAAC;IAC7B,OAAO,mBAAmB,cAAc,CAAC;AAC3C;AAEO,MAAM,cAAc;IACzB,OAAO,mBAAmB,WAAW;AACvC;AAEO,MAAM,iBAAiB;IAC5B,mBAAmB,UAAU;AAC/B;AAEO,MAAM,eAAe,CAAC;IAC3B,OAAO,mBAAmB,YAAY,CAAC;AACzC;AAGO,MAAM,kBAAkB;IAC7B,KAAK;QAAC;KAAgB;IACtB,MAAM,CAAC,WAAqB;eAAI,gBAAgB,GAAG;YAAE;YAAQ;SAAS;IACtE,aAAa,CAAC,WAAqB;eAAI,gBAAgB,GAAG;YAAE;YAAW;SAAS;IAChF,UAAU,IAAM;eAAI,gBAAgB,GAAG;YAAE;SAAW;AACtD;AAEA,kBAAkB;AAClB,MAAM,mBAAmB,OAAO;IAC9B,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,eAAe,EAAE,UAAU,EAAE;QACnE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,6BAA6B,EAAE,SAAS,EAAE,EAAE,SAAS,MAAM,EAAE;IAChF;IAEA,MAAM,OAAoB,MAAM,SAAS,IAAI;IAC7C,OAAO,KAAK,OAAO;AACrB;AAEA,MAAM,0BAA0B,OAAO;IACrC,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,eAAe,EAAE,UAAU,EAAE;QACnE,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,yCAAyC,EAAE,SAAS,EAAE,EAAE,SAAS,MAAM,EAAE;IAC5F;IAEA,MAAM,OAAoB,MAAM,SAAS,IAAI;IAC7C,OAAO;AACT;AAEA,MAAM,uBAAuB;IAC3B,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,cAAc,CAAC,EAAE;QACvD,QAAQ;QACR,SAAS;YACP,gBAAgB;QAClB;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,SAAS,MAAM,EAAE;IACzE;IAEA,MAAM,OAA6B,MAAM,SAAS,IAAI;IACtD,OAAO,KAAK,KAAK;AACnB;AAGO,MAAM,iBAAiB,CAAC,UAAkB;;IAM/C,MAAM,QAAQ,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACrB,UAAU,gBAAgB,IAAI,CAAC;QAC/B,OAAO;8CAAE,IAAM,iBAAiB;;QAChC,WAAW,SAAS,aAAa,IAAI,KAAK;QAC1C,QAAQ,SAAS,UAAU,KAAK,KAAK;QACrC,sBAAsB,SAAS,wBAAwB;QACvD,SAAS,SAAS,WAAW;QAC7B,KAAK;8CAAE,CAAC,cAAc;gBACpB,yDAAyD;gBACzD,IAAI,iBAAiB,SAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,MAAM;oBACzD,OAAO;gBACT;gBACA,OAAO,eAAe;YACxB;;QACA,MAAM;YACJ,cAAc,CAAC,8BAA8B,EAAE,UAAU;QAC3D;IACF;IAEA,uCAAuC;IACvC,OAAO;QACL,SAAS,MAAM,IAAI,IAAI;QACvB,SAAS,MAAM,SAAS;QACxB,wDAAwD;QACxD,GAAG,KAAK;IACV;AACF;GAhCa;;QAMG,8KAAA,CAAA,WAAQ;;;AA4BjB,MAAM,wBAAwB,CAAC,UAAkB;;IAKtD,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,gBAAgB,WAAW,CAAC;QACtC,OAAO;8CAAE,IAAM,wBAAwB;;QACvC,WAAW,SAAS,aAAa,IAAI,KAAK;QAC1C,QAAQ,SAAS,UAAU,KAAK,KAAK;QACrC,SAAS,SAAS,WAAW;QAC7B,KAAK;8CAAE,CAAC,cAAc;gBACpB,IAAI,iBAAiB,SAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,MAAM;oBACzD,OAAO;gBACT;gBACA,OAAO,eAAe;YACxB;;IACF;AACF;IAlBa;;QAKJ,8KAAA,CAAA,WAAQ;;;AAeV,MAAM,qBAAqB,CAAC;;IAKjC,OAAO,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE;QACd,UAAU,gBAAgB,QAAQ;QAClC,SAAS;QACT,WAAW,SAAS,aAAa,IAAI,KAAK;QAC1C,QAAQ,SAAS,UAAU,KAAK,KAAK;QACrC,SAAS,SAAS,WAAW;QAC7B,KAAK;2CAAE,CAAC,cAAc;gBACpB,IAAI,iBAAiB,SAAS,MAAM,OAAO,CAAC,QAAQ,CAAC,MAAM;oBACzD,OAAO;gBACT;gBACA,OAAO,eAAe;YACxB;;IACF;AACF;IAlBa;;QAKJ,8KAAA,CAAA,WAAQ;;;AAeV,MAAM,kBAAkB,CAAC,WAAqB;;IAKnD,MAAM,UAAU,CAAA,GAAA,gLAAA,CAAA,aAAU,AAAD,EAAE;QACzB,SAAS,UAAU,GAAG;mDAAC,CAAC,WAAa,CAAC;oBACpC,UAAU,gBAAgB,IAAI,CAAC;oBAC/B,OAAO;+DAAE,IAAM,iBAAiB;;oBAChC,WAAW,SAAS,aAAa,IAAI,KAAK;oBAC1C,QAAQ,SAAS,UAAU,KAAK,KAAK;oBACrC,SAAS,SAAS,WAAW;oBAC7B,KAAK;+DAAE,CAAC,cAAsB;4BAC5B,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,MAAM;gCAC/B,OAAO;4BACT;4BACA,OAAO,eAAe;wBACxB;;gBACF,CAAC;;IACH;IAEA,sDAAsD;IACtD,MAAM,QAAQ,6JAAA,CAAA,UAAK,CAAC,OAAO;0CAAC;YAC1B,MAAM,SAAkC,CAAC;YACzC,UAAU,OAAO;kDAAC,CAAC,UAAU;oBAC3B,MAAM,QAAQ,OAAO,CAAC,MAAM;oBAC5B,MAAM,CAAC,SAAS,GAAG,MAAM,IAAI,IAAI;gBACnC;;YACA,OAAO;QACT;yCAAG;QAAC;QAAS;KAAU;IAEvB,MAAM,UAAU,QAAQ,IAAI,CAAC,CAAA,QAAS,MAAM,SAAS;IACrD,MAAM,QAAQ,QAAQ,IAAI,CAAC,CAAA,QAAS,MAAM,KAAK,GAAG,OAAO,WAAW;IAEpE,OAAO;QAAE;QAAO;QAAS;IAAM;AACjC;IAnCa;;QAKK,gLAAA,CAAA,aAAU", "debugId": null}}, {"offset": {"line": 392, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/lib/utils/_avatar-generator.ts"], "sourcesContent": ["const AGENT_EMOJIS = [\r\n  '🤖', '🧠', '💡', '🚀', '⚡', '🔮', '🎯', '🛡️', '🔧', '🎨',\r\n  '📊', '📈', '🔍', '🌟', '✨', '🎪', '🎭', '🎨', '🎯', '🎲',\r\n  '🧩', '🔬', '🔭', '🗺️', '🧭', '⚙️', '🛠️', '🔩', '🔗', '📡',\r\n  '🌐', '💻', '🖥️', '📱', '⌨️', '🖱️', '💾', '💿', '📀', '🗄️',\r\n  '📂', '📁', '🗂️', '📋', '📌', '📍', '📎', '🖇️', '📏', '📐',\r\n  '✂️', '🖊️', '🖋️', '✒️', '🖌️', '🖍️', '📝', '✏️', '🔐', '🔒',\r\n  '🔓', '🔏', '🔑', '🗝️', '🔨', '⛏️', '⚒️', '🛡️', '🏹', '🎯',\r\n  '🎰', '🎮', '🕹️', '🎲', '♠️', '♥️', '♦️', '♣️', '🃏', '🀄',\r\n  '🎴', '🎭', '🖼️', '🎨', '🧵', '🧶', '🎸', '🎹', '🎺', '🎻',\r\n  '🥁', '🎬', '🎤', '🎧', '🎼', '🎵', '🎶', '🎙️', '🎚️', '🎛️',\r\n  '📻', '📺', '📷', '📹', '📽️', '🎞️', '📞', '☎️', '📟', '📠',\r\n  '💎', '💍', '🏆', '🥇', '🥈', '🥉', '🏅', '🎖️', '🏵️', '🎗️',\r\n  '🎫', '🎟️', '🎪', '🎭', '🎨', '🎬', '🎤', '🎧', '🎼', '🎹',\r\n  '🦾', '🦿', '🦴', '👁️', '🧠', '🫀', '🫁', '🦷', '🦴', '👀'\r\n];\r\n\r\nconst AVATAR_COLORS = [\r\n  '#FF6B6B', // Red\r\n  '#4ECDC4', // Teal\r\n  '#45B7D1', // Sky Blue\r\n  '#96CEB4', // Mint Green\r\n  '#FECA57', // Yellow\r\n  '#FF9FF3', // Pink\r\n  '#54A0FF', // Blue\r\n  '#48DBFB', // Light Blue\r\n  '#1DD1A1', // Emerald\r\n  '#00D2D3', // Cyan\r\n  '#5F27CD', // Purple\r\n  '#341F97', // Dark Purple\r\n  '#EE5A24', // Orange\r\n  '#F368E0', // Magenta\r\n  '#FF6348', // Coral\r\n  '#7BED9F', // Light Green\r\n  '#70A1FF', // Periwinkle\r\n  '#5352ED', // Indigo\r\n  '#3742FA', // Royal Blue\r\n  '#2ED573', // Green\r\n  '#1E90FF', // Dodger Blue\r\n  '#FF1744', // Red Accent\r\n  '#D500F9', // Purple Accent\r\n  '#00E676', // Green Accent\r\n  '#FF6D00', // Orange Accent\r\n  '#00B8D4', // Cyan Accent\r\n  '#6C5CE7', // Soft Purple\r\n  '#A29BFE', // Lavender\r\n  '#FD79A8', // Rose\r\n  '#FDCB6E', // Mustard\r\n  '#6C5CE7', // Violet\r\n  '#00B894', // Mint\r\n  '#00CEC9', // Turquoise\r\n  '#0984E3', // Blue\r\n  '#6C5CE7', // Purple\r\n  '#A29BFE', // Light Purple\r\n  '#74B9FF', // Light Blue\r\n  '#81ECEC', // Light Cyan\r\n  '#55A3FF', // Sky\r\n  '#FD79A8', // Pink\r\n  '#FDCB6E', // Yellow\r\n  '#FF7675', // Light Red\r\n  '#E17055', // Terra Cotta\r\n  '#FAB1A0', // Peach\r\n  '#74B9FF', // Powder Blue\r\n  '#A29BFE', // Periwinkle\r\n  '#DFE6E9', // Light Gray\r\n  '#B2BEC3', // Gray\r\n  '#636E72', // Dark Gray\r\n];\r\n\r\nexport function generateRandomEmoji(): string {\r\n  const randomIndex = Math.floor(Math.random() * AGENT_EMOJIS.length);\r\n  return AGENT_EMOJIS[randomIndex];\r\n}\r\n\r\nexport function generateRandomAvatarColor(): string {\r\n  const randomIndex = Math.floor(Math.random() * AVATAR_COLORS.length);\r\n  return AVATAR_COLORS[randomIndex];\r\n}\r\n\r\nexport function generateRandomAvatar(): { avatar: string; avatar_color: string } {\r\n  return {\r\n    avatar: generateRandomEmoji(),\r\n    avatar_color: generateRandomAvatarColor(),\r\n  };\r\n}\r\n\r\nexport function generateAvatarFromSeed(seed: string): { avatar: string; avatar_color: string } {\r\n  let hash = 0;\r\n  for (let i = 0; i < seed.length; i++) {\r\n    const char = seed.charCodeAt(i);\r\n    hash = ((hash << 5) - hash) + char;\r\n    hash = hash & hash;\r\n  }\r\n  const emojiIndex = Math.abs(hash) % AGENT_EMOJIS.length;\r\n  const colorIndex = Math.abs(hash >> 8) % AVATAR_COLORS.length;\r\n  return {\r\n    avatar: AGENT_EMOJIS[emojiIndex],\r\n    avatar_color: AVATAR_COLORS[colorIndex],\r\n  };\r\n} "], "names": [], "mappings": ";;;;;;AAAA,MAAM,eAAe;IACnB;IAAM;IAAM;IAAM;IAAM;IAAK;IAAM;IAAM;IAAO;IAAM;IACtD;IAAM;IAAM;IAAM;IAAM;IAAK;IAAM;IAAM;IAAM;IAAM;IACrD;IAAM;IAAM;IAAM;IAAO;IAAM;IAAM;IAAO;IAAM;IAAM;IACxD;IAAM;IAAM;IAAO;IAAM;IAAM;IAAO;IAAM;IAAM;IAAM;IACxD;IAAM;IAAM;IAAO;IAAM;IAAM;IAAM;IAAM;IAAO;IAAM;IACxD;IAAM;IAAO;IAAO;IAAM;IAAO;IAAO;IAAM;IAAM;IAAM;IAC1D;IAAM;IAAM;IAAM;IAAO;IAAM;IAAM;IAAM;IAAO;IAAM;IACxD;IAAM;IAAM;IAAO;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACvD;IAAM;IAAM;IAAO;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACvD;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAO;IAAO;IACxD;IAAM;IAAM;IAAM;IAAM;IAAO;IAAO;IAAM;IAAM;IAAM;IACxD;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAO;IAAO;IACxD;IAAM;IAAO;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IAAM;IACvD;IAAM;IAAM;IAAM;IAAO;IAAM;IAAM;IAAM;IAAM;IAAM;CACxD;AAED,MAAM,gBAAgB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,SAAS;IACd,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,aAAa,MAAM;IAClE,OAAO,YAAY,CAAC,YAAY;AAClC;AAEO,SAAS;IACd,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,cAAc,MAAM;IACnE,OAAO,aAAa,CAAC,YAAY;AACnC;AAEO,SAAS;IACd,OAAO;QACL,QAAQ;QACR,cAAc;IAChB;AACF;AAEO,SAAS,uBAAuB,IAAY;IACjD,IAAI,OAAO;IACX,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;QACpC,MAAM,OAAO,KAAK,UAAU,CAAC;QAC7B,OAAO,AAAC,CAAC,QAAQ,CAAC,IAAI,OAAQ;QAC9B,OAAO,OAAO;IAChB;IACA,MAAM,aAAa,KAAK,GAAG,CAAC,QAAQ,aAAa,MAAM;IACvD,MAAM,aAAa,KAAK,GAAG,CAAC,QAAQ,KAAK,cAAc,MAAM;IAC7D,OAAO;QACL,QAAQ,YAAY,CAAC,WAAW;QAChC,cAAc,aAAa,CAAC,WAAW;IACzC;AACF", "debugId": null}}, {"offset": {"line": 628, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/lib/api-client.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/client';\r\nimport { handleApiError, handleNetworkError, ErrorContext, ApiError } from './error-handler';\r\n\r\nconst API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || '';\r\n\r\nexport interface ApiClientOptions {\r\n  showErrors?: boolean;\r\n  errorContext?: ErrorContext;\r\n  timeout?: number;\r\n}\r\n\r\nexport interface ApiResponse<T = any> {\r\n  data?: T;\r\n  error?: ApiError;\r\n  success: boolean;\r\n}\r\n\r\nexport const apiClient = {\r\n  async request<T = any>(\r\n    url: string,\r\n    options: RequestInit & ApiClientOptions = {}\r\n  ): Promise<ApiResponse<T>> {\r\n    const {\r\n      showErrors = true,\r\n      errorContext,\r\n      timeout = 30000,\r\n      ...fetchOptions\r\n    } = options;\r\n\r\n    try {\r\n      const controller = new AbortController();\r\n      const timeoutId = setTimeout(() => controller.abort(), timeout);\r\n\r\n      const supabase = createClient();\r\n      const { data: { session } } = await supabase.auth.getSession();\r\n\r\n      const headers: Record<string, string> = {\r\n        'Content-Type': 'application/json',\r\n        ...fetchOptions.headers as Record<string, string>,\r\n      };\r\n\r\n      if (session?.access_token) {\r\n        headers['Authorization'] = `Bearer ${session.access_token}`;\r\n      }\r\n\r\n      const response = await fetch(url, {\r\n        ...fetchOptions,\r\n        headers,\r\n        signal: controller.signal,\r\n      });\r\n\r\n      clearTimeout(timeoutId);\r\n\r\n      if (!response.ok) {\r\n        const error: ApiError = new Error(`HTTP ${response.status}: ${response.statusText}`);\r\n        error.status = response.status;\r\n        error.response = response;\r\n\r\n        try {\r\n          const errorData = await response.json();\r\n          error.details = errorData;\r\n          if (errorData.message) {\r\n            error.message = errorData.message;\r\n          }\r\n        } catch {\r\n        }\r\n\r\n        if (showErrors) {\r\n          handleApiError(error, errorContext);\r\n        }\r\n\r\n        return {\r\n          error,\r\n          success: false,\r\n        };\r\n      }\r\n\r\n      let data: T;\r\n      const contentType = response.headers.get('content-type');\r\n      \r\n      if (contentType?.includes('application/json')) {\r\n        data = await response.json();\r\n      } else if (contentType?.includes('text/')) {\r\n        data = await response.text() as T;\r\n      } else {\r\n        data = await response.blob() as T;\r\n      }\r\n\r\n      return {\r\n        data,\r\n        success: true,\r\n      };\r\n\r\n    } catch (error: any) {\r\n      const apiError: ApiError = error instanceof Error ? error : new Error(String(error));\r\n      \r\n      if (error.name === 'AbortError') {\r\n        apiError.message = 'Request timeout';\r\n        apiError.code = 'TIMEOUT';\r\n      }\r\n\r\n      if (showErrors) {\r\n        handleNetworkError(apiError, errorContext);\r\n      }\r\n\r\n      return {\r\n        error: apiError,\r\n        success: false,\r\n      };\r\n    }\r\n  },\r\n\r\n  get: async <T = any>(\r\n    url: string,\r\n    options: Omit<RequestInit & ApiClientOptions, 'method' | 'body'> = {}\r\n  ): Promise<ApiResponse<T>> => {\r\n    return apiClient.request<T>(url, {\r\n      ...options,\r\n      method: 'GET',\r\n    });\r\n  },\r\n\r\n  post: async <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    options: Omit<RequestInit & ApiClientOptions, 'method'> = {}\r\n  ): Promise<ApiResponse<T>> => {\r\n    return apiClient.request<T>(url, {\r\n      ...options,\r\n      method: 'POST',\r\n      body: data ? JSON.stringify(data) : undefined,\r\n    });\r\n  },\r\n\r\n  put: async <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    options: Omit<RequestInit & ApiClientOptions, 'method'> = {}\r\n  ): Promise<ApiResponse<T>> => {\r\n    return apiClient.request<T>(url, {\r\n      ...options,\r\n      method: 'PUT',\r\n      body: data ? JSON.stringify(data) : undefined,\r\n    });\r\n  },\r\n\r\n  patch: async <T = any>(\r\n    url: string,\r\n    data?: any,\r\n    options: Omit<RequestInit & ApiClientOptions, 'method'> = {}\r\n  ): Promise<ApiResponse<T>> => {\r\n    return apiClient.request<T>(url, {\r\n      ...options,\r\n      method: 'PATCH',\r\n      body: data ? JSON.stringify(data) : undefined,\r\n    });\r\n  },\r\n\r\n  delete: async <T = any>(\r\n    url: string,\r\n    options: Omit<RequestInit & ApiClientOptions, 'method' | 'body'> = {}\r\n  ): Promise<ApiResponse<T>> => {\r\n    return apiClient.request<T>(url, {\r\n      ...options,\r\n      method: 'DELETE',\r\n    });\r\n  },\r\n\r\n  upload: async <T = any>(\r\n    url: string,\r\n    formData: FormData,\r\n    options: Omit<RequestInit & ApiClientOptions, 'method' | 'body'> = {}\r\n  ): Promise<ApiResponse<T>> => {\r\n    const { headers, ...restOptions } = options;\r\n    \r\n    const uploadHeaders = { ...headers as Record<string, string> };\r\n    delete uploadHeaders['Content-Type'];\r\n\r\n    return apiClient.request<T>(url, {\r\n      ...restOptions,\r\n      method: 'POST',\r\n      body: formData,\r\n      headers: uploadHeaders,\r\n    });\r\n  },\r\n};\r\n\r\nexport const supabaseClient = {\r\n  async execute<T = any>(\r\n    queryFn: () => Promise<{ data: T | null; error: any }>,\r\n    errorContext?: ErrorContext\r\n  ): Promise<ApiResponse<T>> {\r\n    try {\r\n      const { data, error } = await queryFn();\r\n\r\n      if (error) {\r\n        const apiError: ApiError = new Error(error.message || 'Database error');\r\n        apiError.code = error.code;\r\n        apiError.details = error;\r\n\r\n        handleApiError(apiError, errorContext);\r\n\r\n        return {\r\n          error: apiError,\r\n          success: false,\r\n        };\r\n      }\r\n\r\n      return {\r\n        data: data as T,\r\n        success: true,\r\n      };\r\n    } catch (error: any) {\r\n      const apiError: ApiError = error instanceof Error ? error : new Error(String(error));\r\n      handleApiError(apiError, errorContext);\r\n\r\n      return {\r\n        error: apiError,\r\n        success: false,\r\n      };\r\n    }\r\n  },\r\n};\r\n\r\nexport const backendApi = {\r\n  get: <T = any>(endpoint: string, options?: Omit<RequestInit & ApiClientOptions, 'method' | 'body'>) =>\r\n    apiClient.get<T>(`${API_URL}${endpoint}`, options),\r\n\r\n  post: <T = any>(endpoint: string, data?: any, options?: Omit<RequestInit & ApiClientOptions, 'method'>) =>\r\n    apiClient.post<T>(`${API_URL}${endpoint}`, data, options),\r\n\r\n  put: <T = any>(endpoint: string, data?: any, options?: Omit<RequestInit & ApiClientOptions, 'method'>) =>\r\n    apiClient.put<T>(`${API_URL}${endpoint}`, data, options),\r\n\r\n  patch: <T = any>(endpoint: string, data?: any, options?: Omit<RequestInit & ApiClientOptions, 'method'>) =>\r\n    apiClient.patch<T>(`${API_URL}${endpoint}`, data, options),\r\n\r\n  delete: <T = any>(endpoint: string, options?: Omit<RequestInit & ApiClientOptions, 'method' | 'body'>) =>\r\n    apiClient.delete<T>(`${API_URL}${endpoint}`, options),\r\n\r\n  upload: <T = any>(endpoint: string, formData: FormData, options?: Omit<RequestInit & ApiClientOptions, 'method' | 'body'>) =>\r\n    apiClient.upload<T>(`${API_URL}${endpoint}`, formData, options),\r\n}; "], "names": [], "mappings": ";;;;;AAGgB;AAHhB;AACA;;;AAEA,MAAM,UAAU,iEAAuC;AAchD,MAAM,YAAY;IACvB,MAAM,SACJ,GAAW,EACX,UAA0C,CAAC,CAAC;QAE5C,MAAM,EACJ,aAAa,IAAI,EACjB,YAAY,EACZ,UAAU,KAAK,EACf,GAAG,cACJ,GAAG;QAEJ,IAAI;YACF,MAAM,aAAa,IAAI;YACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI;YAEvD,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;YAC5B,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;YAE5D,MAAM,UAAkC;gBACtC,gBAAgB;gBAChB,GAAG,aAAa,OAAO;YACzB;YAEA,IAAI,SAAS,cAAc;gBACzB,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YAC7D;YAEA,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,GAAG,YAAY;gBACf;gBACA,QAAQ,WAAW,MAAM;YAC3B;YAEA,aAAa;YAEb,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,QAAkB,IAAI,MAAM,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;gBACnF,MAAM,MAAM,GAAG,SAAS,MAAM;gBAC9B,MAAM,QAAQ,GAAG;gBAEjB,IAAI;oBACF,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,OAAO,GAAG;oBAChB,IAAI,UAAU,OAAO,EAAE;wBACrB,MAAM,OAAO,GAAG,UAAU,OAAO;oBACnC;gBACF,EAAE,OAAM,CACR;gBAEA,IAAI,YAAY;oBACd,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;gBACxB;gBAEA,OAAO;oBACL;oBACA,SAAS;gBACX;YACF;YAEA,IAAI;YACJ,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;YAEzC,IAAI,aAAa,SAAS,qBAAqB;gBAC7C,OAAO,MAAM,SAAS,IAAI;YAC5B,OAAO,IAAI,aAAa,SAAS,UAAU;gBACzC,OAAO,MAAM,SAAS,IAAI;YAC5B,OAAO;gBACL,OAAO,MAAM,SAAS,IAAI;YAC5B;YAEA,OAAO;gBACL;gBACA,SAAS;YACX;QAEF,EAAE,OAAO,OAAY;YACnB,MAAM,WAAqB,iBAAiB,QAAQ,QAAQ,IAAI,MAAM,OAAO;YAE7E,IAAI,MAAM,IAAI,KAAK,cAAc;gBAC/B,SAAS,OAAO,GAAG;gBACnB,SAAS,IAAI,GAAG;YAClB;YAEA,IAAI,YAAY;gBACd,CAAA,GAAA,iIAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU;YAC/B;YAEA,OAAO;gBACL,OAAO;gBACP,SAAS;YACX;QACF;IACF;IAEA,KAAK,OACH,KACA,UAAmE,CAAC,CAAC;QAErE,OAAO,UAAU,OAAO,CAAI,KAAK;YAC/B,GAAG,OAAO;YACV,QAAQ;QACV;IACF;IAEA,MAAM,OACJ,KACA,MACA,UAA0D,CAAC,CAAC;QAE5D,OAAO,UAAU,OAAO,CAAI,KAAK;YAC/B,GAAG,OAAO;YACV,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,KAAK,OACH,KACA,MACA,UAA0D,CAAC,CAAC;QAE5D,OAAO,UAAU,OAAO,CAAI,KAAK;YAC/B,GAAG,OAAO;YACV,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,OAAO,OACL,KACA,MACA,UAA0D,CAAC,CAAC;QAE5D,OAAO,UAAU,OAAO,CAAI,KAAK;YAC/B,GAAG,OAAO;YACV,QAAQ;YACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;QACtC;IACF;IAEA,QAAQ,OACN,KACA,UAAmE,CAAC,CAAC;QAErE,OAAO,UAAU,OAAO,CAAI,KAAK;YAC/B,GAAG,OAAO;YACV,QAAQ;QACV;IACF;IAEA,QAAQ,OACN,KACA,UACA,UAAmE,CAAC,CAAC;QAErE,MAAM,EAAE,OAAO,EAAE,GAAG,aAAa,GAAG;QAEpC,MAAM,gBAAgB;YAAE,GAAG,OAAO;QAA2B;QAC7D,OAAO,aAAa,CAAC,eAAe;QAEpC,OAAO,UAAU,OAAO,CAAI,KAAK;YAC/B,GAAG,WAAW;YACd,QAAQ;YACR,MAAM;YACN,SAAS;QACX;IACF;AACF;AAEO,MAAM,iBAAiB;IAC5B,MAAM,SACJ,OAAsD,EACtD,YAA2B;QAE3B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;YAE9B,IAAI,OAAO;gBACT,MAAM,WAAqB,IAAI,MAAM,MAAM,OAAO,IAAI;gBACtD,SAAS,IAAI,GAAG,MAAM,IAAI;gBAC1B,SAAS,OAAO,GAAG;gBAEnB,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;gBAEzB,OAAO;oBACL,OAAO;oBACP,SAAS;gBACX;YACF;YAEA,OAAO;gBACL,MAAM;gBACN,SAAS;YACX;QACF,EAAE,OAAO,OAAY;YACnB,MAAM,WAAqB,iBAAiB,QAAQ,QAAQ,IAAI,MAAM,OAAO;YAC7E,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;YAEzB,OAAO;gBACL,OAAO;gBACP,SAAS;YACX;QACF;IACF;AACF;AAEO,MAAM,aAAa;IACxB,KAAK,CAAU,UAAkB,UAC/B,UAAU,GAAG,CAAI,GAAG,UAAU,UAAU,EAAE;IAE5C,MAAM,CAAU,UAAkB,MAAY,UAC5C,UAAU,IAAI,CAAI,GAAG,UAAU,UAAU,EAAE,MAAM;IAEnD,KAAK,CAAU,UAAkB,MAAY,UAC3C,UAAU,GAAG,CAAI,GAAG,UAAU,UAAU,EAAE,MAAM;IAElD,OAAO,CAAU,UAAkB,MAAY,UAC7C,UAAU,KAAK,CAAI,GAAG,UAAU,UAAU,EAAE,MAAM;IAEpD,QAAQ,CAAU,UAAkB,UAClC,UAAU,MAAM,CAAI,GAAG,UAAU,UAAU,EAAE;IAE/C,QAAQ,CAAU,UAAkB,UAAoB,UACtD,UAAU,MAAM,CAAI,GAAG,UAAU,UAAU,EAAE,UAAU;AAC3D", "debugId": null}}, {"offset": {"line": 799, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/lib/api-enhanced.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/client';\r\nimport { backendApi, supabaseClient } from './api-client';\r\nimport { handleApiSuccess } from './error-handler';\r\nimport { \r\n  Project, \r\n  Thread, \r\n  Message, \r\n  AgentRun, \r\n  InitiateAgentResponse,\r\n  HealthCheckResponse,\r\n  FileInfo,\r\n  CreateCheckoutSessionRequest,\r\n  CreateCheckoutSessionResponse,\r\n  CreatePortalSessionRequest,\r\n  SubscriptionStatus,\r\n  AvailableModelsResponse,\r\n  BillingStatusResponse,\r\n  BillingError,\r\n  UsageLogsResponse\r\n} from './api';\r\n\r\nexport * from './api';\r\n\r\nexport const projectsApi = {\r\n  async getAll(): Promise<Project[]> {\r\n    const result = await supabaseClient.execute(\r\n      async () => {\r\n        const supabase = createClient();\r\n        const { data: userData, error: userError } = await supabase.auth.getUser();\r\n        \r\n        if (userError) {\r\n          return { data: null, error: userError };\r\n        }\r\n\r\n        if (!userData.user) {\r\n          return { data: [], error: null };\r\n        }\r\n\r\n        const { data, error } = await supabase\r\n          .from('projects')\r\n          .select('*')\r\n          .eq('account_id', userData.user.id);\r\n\r\n        if (error) {\r\n          if (error.code === '42501' && error.message.includes('has_role_on_account')) {\r\n            return { data: [], error: null };\r\n          }\r\n          return { data: null, error };\r\n        }\r\n\r\n        const mappedProjects: Project[] = (data || []).map((project) => ({\r\n          id: project.project_id,\r\n          name: project.name || '',\r\n          description: project.description || '',\r\n          account_id: project.account_id,\r\n          created_at: project.created_at,\r\n          updated_at: project.updated_at,\r\n          sandbox: project.sandbox || {\r\n            id: '',\r\n            pass: '',\r\n            vnc_preview: '',\r\n            sandbox_url: '',\r\n          },\r\n        }));\r\n\r\n        return { data: mappedProjects, error: null };\r\n      },\r\n      { operation: 'load projects', resource: 'projects' }\r\n    );\r\n\r\n    return result.data || [];\r\n  },\r\n\r\n  async getById(projectId: string): Promise<Project | null> {\r\n    const result = await supabaseClient.execute(\r\n      async () => {\r\n        const supabase = createClient();\r\n        const { data, error } = await supabase\r\n          .from('projects')\r\n          .select('*')\r\n          .eq('project_id', projectId)\r\n          .single();\r\n\r\n        if (error) {\r\n          if (error.code === 'PGRST116') {\r\n            return { data: null, error: new Error(`Project not found: ${projectId}`) };\r\n          }\r\n          return { data: null, error };\r\n        }\r\n\r\n        // Ensure sandbox is active if it exists\r\n        if (data.sandbox?.id) {\r\n          backendApi.post(`/project/${projectId}/sandbox/ensure-active`, undefined, {\r\n            showErrors: false,\r\n            errorContext: { silent: true }\r\n          });\r\n        }\r\n\r\n        const mappedProject: Project = {\r\n          id: data.project_id,\r\n          name: data.name || '',\r\n          description: data.description || '',\r\n          account_id: data.account_id,\r\n          is_public: data.is_public || false,\r\n          created_at: data.created_at,\r\n          sandbox: data.sandbox || {\r\n            id: '',\r\n            pass: '',\r\n            vnc_preview: '',\r\n            sandbox_url: '',\r\n          },\r\n        };\r\n\r\n        return { data: mappedProject, error: null };\r\n      },\r\n      { operation: 'load project', resource: `project ${projectId}` }\r\n    );\r\n\r\n    return result.data || null;\r\n  },\r\n\r\n  async create(projectData: { name: string; description: string }, accountId?: string): Promise<Project | null> {\r\n    const result = await supabaseClient.execute(\r\n      async () => {\r\n        const supabase = createClient();\r\n        \r\n        if (!accountId) {\r\n          const { data: userData, error: userError } = await supabase.auth.getUser();\r\n          if (userError) return { data: null, error: userError };\r\n          if (!userData.user) return { data: null, error: new Error('You must be logged in to create a project') };\r\n          accountId = userData.user.id;\r\n        }\r\n\r\n        const { data, error } = await supabase\r\n          .from('projects')\r\n          .insert({\r\n            name: projectData.name,\r\n            description: projectData.description || null,\r\n            account_id: accountId,\r\n          })\r\n          .select()\r\n          .single();\r\n\r\n        if (error) return { data: null, error };\r\n\r\n        const project: Project = {\r\n          id: data.project_id,\r\n          name: data.name,\r\n          description: data.description || '',\r\n          account_id: data.account_id,\r\n          created_at: data.created_at,\r\n          sandbox: { id: '', pass: '', vnc_preview: '' },\r\n        };\r\n\r\n        return { data: project, error: null };\r\n      },\r\n      { operation: 'create project', resource: 'project' }\r\n    );\r\n\r\n    return result.data || null;\r\n  },\r\n\r\n  async update(projectId: string, data: Partial<Project>): Promise<Project | null> {\r\n    if (!projectId || projectId === '') {\r\n      throw new Error('Cannot update project: Invalid project ID');\r\n    }\r\n\r\n    const result = await supabaseClient.execute(\r\n      async () => {\r\n        const supabase = createClient();\r\n        const { data: updatedData, error } = await supabase\r\n          .from('projects')\r\n          .update(data)\r\n          .eq('project_id', projectId)\r\n          .select()\r\n          .single();\r\n\r\n        if (error) return { data: null, error };\r\n        if (!updatedData) return { data: null, error: new Error('No data returned from update') };\r\n\r\n        // Dispatch custom event for project updates\r\n        if (typeof window !== 'undefined') {\r\n          window.dispatchEvent(\r\n            new CustomEvent('project-updated', {\r\n              detail: {\r\n                projectId,\r\n                updatedData: {\r\n                  id: updatedData.project_id,\r\n                  name: updatedData.name,\r\n                  description: updatedData.description,\r\n                },\r\n              },\r\n            }),\r\n          );\r\n        }\r\n\r\n        const project: Project = {\r\n          id: updatedData.project_id,\r\n          name: updatedData.name,\r\n          description: updatedData.description || '',\r\n          account_id: updatedData.account_id,\r\n          created_at: updatedData.created_at,\r\n          sandbox: updatedData.sandbox || {\r\n            id: '',\r\n            pass: '',\r\n            vnc_preview: '',\r\n            sandbox_url: '',\r\n          },\r\n        };\r\n\r\n        return { data: project, error: null };\r\n      },\r\n      { operation: 'update project', resource: `project ${projectId}` }\r\n    );\r\n    return result.data || null;\r\n  },\r\n\r\n  async delete(projectId: string): Promise<boolean> {\r\n    const result = await supabaseClient.execute(\r\n      async () => {\r\n        const supabase = createClient();\r\n        const { error } = await supabase\r\n          .from('projects')\r\n          .delete()\r\n          .eq('project_id', projectId);\r\n\r\n        return { data: !error, error };\r\n      },\r\n      { operation: 'delete project', resource: `project ${projectId}` }\r\n    );\r\n    return result.success;\r\n  },\r\n};\r\n\r\nexport const threadsApi = {\r\n  async getAll(projectId?: string): Promise<Thread[]> {\r\n    const result = await supabaseClient.execute(\r\n      async () => {\r\n        const supabase = createClient();\r\n        const { data: userData, error: userError } = await supabase.auth.getUser();\r\n        \r\n        if (userError) return { data: null, error: userError };\r\n        if (!userData.user) return { data: [], error: null };\r\n\r\n        let query = supabase.from('threads').select('*').eq('account_id', userData.user.id);\r\n        \r\n        if (projectId) {\r\n          query = query.eq('project_id', projectId);\r\n        }\r\n\r\n        const { data, error } = await query;\r\n        if (error) return { data: null, error };\r\n\r\n        const mappedThreads: Thread[] = (data || []).map((thread) => ({\r\n          thread_id: thread.thread_id,\r\n          account_id: thread.account_id,\r\n          project_id: thread.project_id,\r\n          created_at: thread.created_at,\r\n          updated_at: thread.updated_at,\r\n          metadata: thread.metadata,\r\n        }));\r\n\r\n        return { data: mappedThreads, error: null };\r\n      },\r\n      { operation: 'load threads', resource: projectId ? `threads for project ${projectId}` : 'threads' }\r\n    );\r\n\r\n    return result.data || [];\r\n  },\r\n\r\n  async getById(threadId: string): Promise<Thread | null> {\r\n    const result = await supabaseClient.execute(\r\n      async () => {\r\n        const supabase = createClient();\r\n        const { data, error } = await supabase\r\n          .from('threads')\r\n          .select('*')\r\n          .eq('thread_id', threadId)\r\n          .single();\r\n\r\n        return { data, error };\r\n      },\r\n      { operation: 'load thread', resource: `thread ${threadId}` }\r\n    );\r\n\r\n    return result.data || null;\r\n  },\r\n\r\n  async create(projectId: string): Promise<Thread | null> {\r\n    const result = await supabaseClient.execute(\r\n      async () => {\r\n        const supabase = createClient();\r\n        const { data: { user } } = await supabase.auth.getUser();\r\n        \r\n        if (!user) {\r\n          return { data: null, error: new Error('You must be logged in to create a thread') };\r\n        }\r\n\r\n        const { data, error } = await supabase\r\n          .from('threads')\r\n          .insert({\r\n            project_id: projectId,\r\n            account_id: user.id,\r\n          })\r\n          .select()\r\n          .single();\r\n\r\n        return { data, error };\r\n      },\r\n      { operation: 'create thread', resource: 'thread' }\r\n    );\r\n    return result.data || null;\r\n  },\r\n};\r\n\r\nexport const agentApi = {\r\n  async start(\r\n    threadId: string,\r\n    options?: {\r\n      model_name?: string;\r\n      enable_thinking?: boolean;\r\n      reasoning_effort?: string;\r\n      stream?: boolean;\r\n    }\r\n  ): Promise<{ agent_run_id: string } | null> {\r\n    const result = await backendApi.post(\r\n      `/thread/${threadId}/agent/start`,\r\n      options,\r\n      {\r\n        errorContext: { operation: 'start agent', resource: 'AI assistant' },\r\n        timeout: 60000,\r\n      }\r\n    );\r\n    return result.data || null;\r\n  },\r\n\r\n  async stop(agentRunId: string): Promise<boolean> {\r\n    const result = await backendApi.post(\r\n      `/agent/${agentRunId}/stop`,\r\n      undefined,\r\n      {\r\n        errorContext: { operation: 'stop agent', resource: 'AI assistant' },\r\n      }\r\n    );\r\n\r\n    if (result.success) {\r\n      handleApiSuccess('AI assistant stopped');\r\n    }\r\n\r\n    return result.success;\r\n  },\r\n\r\n  async getStatus(agentRunId: string): Promise<AgentRun | null> {\r\n    const result = await backendApi.get(\r\n      `/agent/${agentRunId}/status`,\r\n      {\r\n        errorContext: { operation: 'get agent status', resource: 'AI assistant status' },\r\n        showErrors: false,\r\n      }\r\n    );\r\n\r\n    return result.data || null;\r\n  },\r\n\r\n  async getRuns(threadId: string): Promise<AgentRun[]> {\r\n    const result = await backendApi.get(\r\n      `/thread/${threadId}/agent/runs`,\r\n      {\r\n        errorContext: { operation: 'load agent runs', resource: 'conversation history' },\r\n      }\r\n    );\r\n\r\n    return result.data || [];\r\n  },\r\n};\r\n\r\nexport const billingApi = {\r\n  async getSubscription(): Promise<SubscriptionStatus | null> {\r\n    const result = await backendApi.get(\r\n      '/billing/subscription',\r\n      {\r\n        errorContext: { operation: 'load subscription', resource: 'billing information' },\r\n      }\r\n    );\r\n\r\n    return result.data || null;\r\n  },\r\n\r\n  async checkStatus(): Promise<BillingStatusResponse | null> {\r\n    const result = await backendApi.get(\r\n      '/billing/status',\r\n      {\r\n        errorContext: { operation: 'check billing status', resource: 'account status' },\r\n      }\r\n    );\r\n\r\n    return result.data || null;\r\n  },\r\n\r\n  async createCheckoutSession(request: CreateCheckoutSessionRequest): Promise<CreateCheckoutSessionResponse | null> {\r\n    const result = await backendApi.post(\r\n      '/billing/create-checkout-session',\r\n      request,\r\n      {\r\n        errorContext: { operation: 'create checkout session', resource: 'billing' },\r\n      }\r\n    );\r\n\r\n    return result.data || null;\r\n  },\r\n\r\n  async createPortalSession(request: CreatePortalSessionRequest): Promise<{ url: string } | null> {\r\n    const result = await backendApi.post(\r\n      '/billing/create-portal-session',\r\n      request,\r\n      {\r\n        errorContext: { operation: 'create portal session', resource: 'billing portal' },\r\n      }\r\n    );\r\n\r\n    return result.data || null;\r\n  },\r\n\r\n  async getAvailableModels(): Promise<AvailableModelsResponse | null> {\r\n    const result = await backendApi.get(\r\n      '/billing/available-models',\r\n      {\r\n        errorContext: { operation: 'load available models', resource: 'AI models' },\r\n      }\r\n    );\r\n\r\n    return result.data || null;\r\n  },\r\n\r\n  async getUsageLogs(page: number = 0, itemsPerPage: number = 1000): Promise<UsageLogsResponse | null> {\r\n    const result = await backendApi.get(\r\n      `/billing/usage-logs?page=${page}&items_per_page=${itemsPerPage}`,\r\n      {\r\n        errorContext: { operation: 'load usage logs', resource: 'usage history' },\r\n      }\r\n    );\r\n\r\n    return result.data || null;\r\n  },\r\n};\r\n\r\nexport const healthApi = {\r\n  async check(): Promise<HealthCheckResponse | null> {\r\n    const result = await backendApi.get(\r\n      '/health',\r\n      {\r\n        errorContext: { operation: 'check system health', resource: 'system status' },\r\n        timeout: 10000,\r\n      }\r\n    );\r\n\r\n    return result.data || null;\r\n  },\r\n}; "], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AAmBA;;;;;AAEO,MAAM,cAAc;IACzB,MAAM;QACJ,MAAM,SAAS,MAAM,8HAAA,CAAA,iBAAc,CAAC,OAAO,CACzC;YACE,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;YAC5B,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YAExE,IAAI,WAAW;gBACb,OAAO;oBAAE,MAAM;oBAAM,OAAO;gBAAU;YACxC;YAEA,IAAI,CAAC,SAAS,IAAI,EAAE;gBAClB,OAAO;oBAAE,MAAM,EAAE;oBAAE,OAAO;gBAAK;YACjC;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,cAAc,SAAS,IAAI,CAAC,EAAE;YAEpC,IAAI,OAAO;gBACT,IAAI,MAAM,IAAI,KAAK,WAAW,MAAM,OAAO,CAAC,QAAQ,CAAC,wBAAwB;oBAC3E,OAAO;wBAAE,MAAM,EAAE;wBAAE,OAAO;oBAAK;gBACjC;gBACA,OAAO;oBAAE,MAAM;oBAAM;gBAAM;YAC7B;YAEA,MAAM,iBAA4B,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAC,UAAY,CAAC;oBAC/D,IAAI,QAAQ,UAAU;oBACtB,MAAM,QAAQ,IAAI,IAAI;oBACtB,aAAa,QAAQ,WAAW,IAAI;oBACpC,YAAY,QAAQ,UAAU;oBAC9B,YAAY,QAAQ,UAAU;oBAC9B,YAAY,QAAQ,UAAU;oBAC9B,SAAS,QAAQ,OAAO,IAAI;wBAC1B,IAAI;wBACJ,MAAM;wBACN,aAAa;wBACb,aAAa;oBACf;gBACF,CAAC;YAED,OAAO;gBAAE,MAAM;gBAAgB,OAAO;YAAK;QAC7C,GACA;YAAE,WAAW;YAAiB,UAAU;QAAW;QAGrD,OAAO,OAAO,IAAI,IAAI,EAAE;IAC1B;IAEA,MAAM,SAAQ,SAAiB;QAC7B,MAAM,SAAS,MAAM,8HAAA,CAAA,iBAAc,CAAC,OAAO,CACzC;YACE,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;YAC5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,cAAc,WACjB,MAAM;YAET,IAAI,OAAO;gBACT,IAAI,MAAM,IAAI,KAAK,YAAY;oBAC7B,OAAO;wBAAE,MAAM;wBAAM,OAAO,IAAI,MAAM,CAAC,mBAAmB,EAAE,WAAW;oBAAE;gBAC3E;gBACA,OAAO;oBAAE,MAAM;oBAAM;gBAAM;YAC7B;YAEA,wCAAwC;YACxC,IAAI,KAAK,OAAO,EAAE,IAAI;gBACpB,8HAAA,CAAA,aAAU,CAAC,IAAI,CAAC,CAAC,SAAS,EAAE,UAAU,sBAAsB,CAAC,EAAE,WAAW;oBACxE,YAAY;oBACZ,cAAc;wBAAE,QAAQ;oBAAK;gBAC/B;YACF;YAEA,MAAM,gBAAyB;gBAC7B,IAAI,KAAK,UAAU;gBACnB,MAAM,KAAK,IAAI,IAAI;gBACnB,aAAa,KAAK,WAAW,IAAI;gBACjC,YAAY,KAAK,UAAU;gBAC3B,WAAW,KAAK,SAAS,IAAI;gBAC7B,YAAY,KAAK,UAAU;gBAC3B,SAAS,KAAK,OAAO,IAAI;oBACvB,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,aAAa;gBACf;YACF;YAEA,OAAO;gBAAE,MAAM;gBAAe,OAAO;YAAK;QAC5C,GACA;YAAE,WAAW;YAAgB,UAAU,CAAC,QAAQ,EAAE,WAAW;QAAC;QAGhE,OAAO,OAAO,IAAI,IAAI;IACxB;IAEA,MAAM,QAAO,WAAkD,EAAE,SAAkB;QACjF,MAAM,SAAS,MAAM,8HAAA,CAAA,iBAAc,CAAC,OAAO,CACzC;YACE,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;YAE5B,IAAI,CAAC,WAAW;gBACd,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;gBACxE,IAAI,WAAW,OAAO;oBAAE,MAAM;oBAAM,OAAO;gBAAU;gBACrD,IAAI,CAAC,SAAS,IAAI,EAAE,OAAO;oBAAE,MAAM;oBAAM,OAAO,IAAI,MAAM;gBAA6C;gBACvG,YAAY,SAAS,IAAI,CAAC,EAAE;YAC9B;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;gBACN,MAAM,YAAY,IAAI;gBACtB,aAAa,YAAY,WAAW,IAAI;gBACxC,YAAY;YACd,GACC,MAAM,GACN,MAAM;YAET,IAAI,OAAO,OAAO;gBAAE,MAAM;gBAAM;YAAM;YAEtC,MAAM,UAAmB;gBACvB,IAAI,KAAK,UAAU;gBACnB,MAAM,KAAK,IAAI;gBACf,aAAa,KAAK,WAAW,IAAI;gBACjC,YAAY,KAAK,UAAU;gBAC3B,YAAY,KAAK,UAAU;gBAC3B,SAAS;oBAAE,IAAI;oBAAI,MAAM;oBAAI,aAAa;gBAAG;YAC/C;YAEA,OAAO;gBAAE,MAAM;gBAAS,OAAO;YAAK;QACtC,GACA;YAAE,WAAW;YAAkB,UAAU;QAAU;QAGrD,OAAO,OAAO,IAAI,IAAI;IACxB;IAEA,MAAM,QAAO,SAAiB,EAAE,IAAsB;QACpD,IAAI,CAAC,aAAa,cAAc,IAAI;YAClC,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,MAAM,8HAAA,CAAA,iBAAc,CAAC,OAAO,CACzC;YACE,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;YAC5B,MAAM,EAAE,MAAM,WAAW,EAAE,KAAK,EAAE,GAAG,MAAM,SACxC,IAAI,CAAC,YACL,MAAM,CAAC,MACP,EAAE,CAAC,cAAc,WACjB,MAAM,GACN,MAAM;YAET,IAAI,OAAO,OAAO;gBAAE,MAAM;gBAAM;YAAM;YACtC,IAAI,CAAC,aAAa,OAAO;gBAAE,MAAM;gBAAM,OAAO,IAAI,MAAM;YAAgC;YAExF,4CAA4C;YAC5C,wCAAmC;gBACjC,OAAO,aAAa,CAClB,IAAI,YAAY,mBAAmB;oBACjC,QAAQ;wBACN;wBACA,aAAa;4BACX,IAAI,YAAY,UAAU;4BAC1B,MAAM,YAAY,IAAI;4BACtB,aAAa,YAAY,WAAW;wBACtC;oBACF;gBACF;YAEJ;YAEA,MAAM,UAAmB;gBACvB,IAAI,YAAY,UAAU;gBAC1B,MAAM,YAAY,IAAI;gBACtB,aAAa,YAAY,WAAW,IAAI;gBACxC,YAAY,YAAY,UAAU;gBAClC,YAAY,YAAY,UAAU;gBAClC,SAAS,YAAY,OAAO,IAAI;oBAC9B,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,aAAa;gBACf;YACF;YAEA,OAAO;gBAAE,MAAM;gBAAS,OAAO;YAAK;QACtC,GACA;YAAE,WAAW;YAAkB,UAAU,CAAC,QAAQ,EAAE,WAAW;QAAC;QAElE,OAAO,OAAO,IAAI,IAAI;IACxB;IAEA,MAAM,QAAO,SAAiB;QAC5B,MAAM,SAAS,MAAM,8HAAA,CAAA,iBAAc,CAAC,OAAO,CACzC;YACE,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;YAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,cAAc;YAEpB,OAAO;gBAAE,MAAM,CAAC;gBAAO;YAAM;QAC/B,GACA;YAAE,WAAW;YAAkB,UAAU,CAAC,QAAQ,EAAE,WAAW;QAAC;QAElE,OAAO,OAAO,OAAO;IACvB;AACF;AAEO,MAAM,aAAa;IACxB,MAAM,QAAO,SAAkB;QAC7B,MAAM,SAAS,MAAM,8HAAA,CAAA,iBAAc,CAAC,OAAO,CACzC;YACE,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;YAC5B,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YAExE,IAAI,WAAW,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAU;YACrD,IAAI,CAAC,SAAS,IAAI,EAAE,OAAO;gBAAE,MAAM,EAAE;gBAAE,OAAO;YAAK;YAEnD,IAAI,QAAQ,SAAS,IAAI,CAAC,WAAW,MAAM,CAAC,KAAK,EAAE,CAAC,cAAc,SAAS,IAAI,CAAC,EAAE;YAElF,IAAI,WAAW;gBACb,QAAQ,MAAM,EAAE,CAAC,cAAc;YACjC;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;YAC9B,IAAI,OAAO,OAAO;gBAAE,MAAM;gBAAM;YAAM;YAEtC,MAAM,gBAA0B,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAC,SAAW,CAAC;oBAC5D,WAAW,OAAO,SAAS;oBAC3B,YAAY,OAAO,UAAU;oBAC7B,YAAY,OAAO,UAAU;oBAC7B,YAAY,OAAO,UAAU;oBAC7B,YAAY,OAAO,UAAU;oBAC7B,UAAU,OAAO,QAAQ;gBAC3B,CAAC;YAED,OAAO;gBAAE,MAAM;gBAAe,OAAO;YAAK;QAC5C,GACA;YAAE,WAAW;YAAgB,UAAU,YAAY,CAAC,oBAAoB,EAAE,WAAW,GAAG;QAAU;QAGpG,OAAO,OAAO,IAAI,IAAI,EAAE;IAC1B;IAEA,MAAM,SAAQ,QAAgB;QAC5B,MAAM,SAAS,MAAM,8HAAA,CAAA,iBAAc,CAAC,OAAO,CACzC;YACE,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;YAC5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,UAChB,MAAM;YAET,OAAO;gBAAE;gBAAM;YAAM;QACvB,GACA;YAAE,WAAW;YAAe,UAAU,CAAC,OAAO,EAAE,UAAU;QAAC;QAG7D,OAAO,OAAO,IAAI,IAAI;IACxB;IAEA,MAAM,QAAO,SAAiB;QAC5B,MAAM,SAAS,MAAM,8HAAA,CAAA,iBAAc,CAAC,OAAO,CACzC;YACE,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;YAC5B,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;YAEtD,IAAI,CAAC,MAAM;gBACT,OAAO;oBAAE,MAAM;oBAAM,OAAO,IAAI,MAAM;gBAA4C;YACpF;YAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,WACL,MAAM,CAAC;gBACN,YAAY;gBACZ,YAAY,KAAK,EAAE;YACrB,GACC,MAAM,GACN,MAAM;YAET,OAAO;gBAAE;gBAAM;YAAM;QACvB,GACA;YAAE,WAAW;YAAiB,UAAU;QAAS;QAEnD,OAAO,OAAO,IAAI,IAAI;IACxB;AACF;AAEO,MAAM,WAAW;IACtB,MAAM,OACJ,QAAgB,EAChB,OAKC;QAED,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAClC,CAAC,QAAQ,EAAE,SAAS,YAAY,CAAC,EACjC,SACA;YACE,cAAc;gBAAE,WAAW;gBAAe,UAAU;YAAe;YACnE,SAAS;QACX;QAEF,OAAO,OAAO,IAAI,IAAI;IACxB;IAEA,MAAM,MAAK,UAAkB;QAC3B,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAClC,CAAC,OAAO,EAAE,WAAW,KAAK,CAAC,EAC3B,WACA;YACE,cAAc;gBAAE,WAAW;gBAAc,UAAU;YAAe;QACpE;QAGF,IAAI,OAAO,OAAO,EAAE;YAClB,CAAA,GAAA,iIAAA,CAAA,mBAAgB,AAAD,EAAE;QACnB;QAEA,OAAO,OAAO,OAAO;IACvB;IAEA,MAAM,WAAU,UAAkB;QAChC,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CACjC,CAAC,OAAO,EAAE,WAAW,OAAO,CAAC,EAC7B;YACE,cAAc;gBAAE,WAAW;gBAAoB,UAAU;YAAsB;YAC/E,YAAY;QACd;QAGF,OAAO,OAAO,IAAI,IAAI;IACxB;IAEA,MAAM,SAAQ,QAAgB;QAC5B,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CACjC,CAAC,QAAQ,EAAE,SAAS,WAAW,CAAC,EAChC;YACE,cAAc;gBAAE,WAAW;gBAAmB,UAAU;YAAuB;QACjF;QAGF,OAAO,OAAO,IAAI,IAAI,EAAE;IAC1B;AACF;AAEO,MAAM,aAAa;IACxB,MAAM;QACJ,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CACjC,yBACA;YACE,cAAc;gBAAE,WAAW;gBAAqB,UAAU;YAAsB;QAClF;QAGF,OAAO,OAAO,IAAI,IAAI;IACxB;IAEA,MAAM;QACJ,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CACjC,mBACA;YACE,cAAc;gBAAE,WAAW;gBAAwB,UAAU;YAAiB;QAChF;QAGF,OAAO,OAAO,IAAI,IAAI;IACxB;IAEA,MAAM,uBAAsB,OAAqC;QAC/D,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAClC,oCACA,SACA;YACE,cAAc;gBAAE,WAAW;gBAA2B,UAAU;YAAU;QAC5E;QAGF,OAAO,OAAO,IAAI,IAAI;IACxB;IAEA,MAAM,qBAAoB,OAAmC;QAC3D,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,IAAI,CAClC,kCACA,SACA;YACE,cAAc;gBAAE,WAAW;gBAAyB,UAAU;YAAiB;QACjF;QAGF,OAAO,OAAO,IAAI,IAAI;IACxB;IAEA,MAAM;QACJ,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CACjC,6BACA;YACE,cAAc;gBAAE,WAAW;gBAAyB,UAAU;YAAY;QAC5E;QAGF,OAAO,OAAO,IAAI,IAAI;IACxB;IAEA,MAAM,cAAa,OAAe,CAAC,EAAE,eAAuB,IAAI;QAC9D,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CACjC,CAAC,yBAAyB,EAAE,KAAK,gBAAgB,EAAE,cAAc,EACjE;YACE,cAAc;gBAAE,WAAW;gBAAmB,UAAU;YAAgB;QAC1E;QAGF,OAAO,OAAO,IAAI,IAAI;IACxB;AACF;AAEO,MAAM,YAAY;IACvB,MAAM;QACJ,MAAM,SAAS,MAAM,8HAAA,CAAA,aAAU,CAAC,GAAG,CACjC,WACA;YACE,cAAc;gBAAE,WAAW;gBAAuB,UAAU;YAAgB;YAC5E,SAAS;QACX;QAGF,OAAO,OAAO,IAAI,IAAI;IACxB;AACF", "debugId": null}}, {"offset": {"line": 1240, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/lib/utils/url.ts"], "sourcesContent": ["/**\r\n * Constructs a preview URL for HTML files in the sandbox environment.\r\n * <PERSON><PERSON><PERSON> handles URL encoding of file paths by encoding each path segment individually.\r\n *\r\n * @param sandboxUrl - The base URL of the sandbox\r\n * @param filePath - The path to the HTML file (can include /workspace/ prefix)\r\n * @returns The properly encoded preview URL, or undefined if inputs are invalid\r\n */\r\nexport function constructHtmlPreviewUrl(\r\n  sandboxUrl: string | undefined,\r\n  filePath: string | undefined,\r\n): string | undefined {\r\n  if (!sandboxUrl || !filePath) {\r\n    return undefined;\r\n  }\r\n\r\n  // Remove /workspace/ prefix if present\r\n  const processedPath = filePath.replace(/^\\/workspace\\//, '');\r\n\r\n  // Split the path into segments and encode each segment individually\r\n  const pathSegments = processedPath\r\n    .split('/')\r\n    .map((segment) => encodeURIComponent(segment));\r\n\r\n  // Join the segments back together with forward slashes\r\n  const encodedPath = pathSegments.join('/');\r\n\r\n  return `${sandboxUrl}/${encodedPath}`;\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACM,SAAS,wBACd,UAA8B,EAC9B,QAA4B;IAE5B,IAAI,CAAC,cAAc,CAAC,UAAU;QAC5B,OAAO;IACT;IAEA,uCAAuC;IACvC,MAAM,gBAAgB,SAAS,OAAO,CAAC,kBAAkB;IAEzD,oEAAoE;IACpE,MAAM,eAAe,cAClB,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,UAAY,mBAAmB;IAEvC,uDAAuD;IACvD,MAAM,cAAc,aAAa,IAAI,CAAC;IAEtC,OAAO,GAAG,WAAW,CAAC,EAAE,aAAa;AACvC", "debugId": null}}, {"offset": {"line": 1271, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/providers/modal-providers.tsx"], "sourcesContent": ["import { PaymentRequiredDialog } from \"@/components/billing/payment-required-dialog\"\r\n\r\nexport const ModalProviders = () => {\r\n  return (\r\n    <>\r\n      <PaymentRequiredDialog />\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;;AAEO,MAAM,iBAAiB;IAC5B,qBACE;kBACE,cAAA,6LAAC,iKAAA,CAAA,wBAAqB;;;;;;AAG5B;KANa", "debugId": null}}, {"offset": {"line": 1299, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/app/%28home%29/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useEffect, useState } from 'react';\r\nimport { CTASection } from '@/components/home/<USER>/cta-section';\r\n// import { FAQSection } from \"@/components/sections/faq-section\";\r\nimport { FooterSection } from '@/components/home/<USER>/footer-section';\r\nimport { HeroSection } from '@/components/home/<USER>/hero-section';\r\nimport { OpenSourceSection } from '@/components/home/<USER>/open-source-section';\r\nimport { PricingSection } from '@/components/home/<USER>/pricing-section';\r\nimport { UseCasesSection } from '@/components/home/<USER>/use-cases-section';\r\nimport { ModalProviders } from '@/providers/modal-providers';\r\nimport { HeroVideoSection } from '@/components/home/<USER>/hero-video-section';\r\n\r\nexport default function Home() {\r\n  return (\r\n    <>\r\n      <ModalProviders />\r\n      <main className=\"flex flex-col items-center justify-center min-h-screen w-full\">\r\n        <div className=\"w-full divide-y divide-border\">\r\n          <HeroSection />\r\n          <UseCasesSection />\r\n          {/* <CompanyShowcase /> */}\r\n          {/* <BentoSection /> */}\r\n          {/* <QuoteSection /> */}\r\n          {/* <FeatureSection /> */}\r\n          {/* <GrowthSection /> */}\r\n          <OpenSourceSection />\r\n          <div className='flex flex-col items-center px-4'>\r\n            <PricingSection />\r\n          </div>\r\n          <div className=\"pb-10 mx-auto\">\r\n            <HeroVideoSection />\r\n          </div>\r\n          {/* <TestimonialSection /> */}\r\n          {/* <FAQSection /> */}\r\n          <CTASection />\r\n          <FooterSection />\r\n        </div>\r\n      </main>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA,kEAAkE;AAClE;AACA;AACA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;AAae,SAAS;IACtB,qBACE;;0BACE,6LAAC,0IAAA,CAAA,iBAAc;;;;;0BACf,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,4JAAA,CAAA,cAAW;;;;;sCACZ,6LAAC,oKAAA,CAAA,kBAAe;;;;;sCAMhB,6LAAC,sKAAA,CAAA,oBAAiB;;;;;sCAClB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,iBAAc;;;;;;;;;;sCAEjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qKAAA,CAAA,mBAAgB;;;;;;;;;;sCAInB,6LAAC,2JAAA,CAAA,aAAU;;;;;sCACX,6LAAC,8JAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;;;AAKxB;KA5BwB", "debugId": null}}]}