"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2566],{17649:(e,t,r)=>{r.d(t,{UC:()=>H,VY:()=>P,ZD:()=>U,ZL:()=>q,bL:()=>N,hE:()=>G,hJ:()=>L,l9:()=>Q,rc:()=>S});var s=r(12115),i=r(46081),a=r(6101),n=r(15452),o=r(85185),l=r(99708),u=r(95155),h="AlertDialog",[c,d]=(0,i.A)(h,[n.Hs]),p=(0,n.Hs)(),f=e=>{let{__scopeAlertDialog:t,...r}=e,s=p(t);return(0,u.jsx)(n.bL,{...s,...r,modal:!0})};f.displayName=h;var b=s.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...s}=e,i=p(r);return(0,u.jsx)(n.l9,{...i,...s,ref:t})});b.displayName="AlertDialogTrigger";var v=e=>{let{__scopeAlertDialog:t,...r}=e,s=p(t);return(0,u.jsx)(n.ZL,{...s,...r})};v.displayName="AlertDialogPortal";var y=s.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...s}=e,i=p(r);return(0,u.jsx)(n.hJ,{...i,...s,ref:t})});y.displayName="AlertDialogOverlay";var m="AlertDialogContent",[g,R]=c(m),w=(0,l.Dc)("AlertDialogContent"),x=s.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:i,...l}=e,h=p(r),c=s.useRef(null),d=(0,a.s)(t,c),f=s.useRef(null);return(0,u.jsx)(n.G$,{contentName:m,titleName:k,docsSlug:"alert-dialog",children:(0,u.jsx)(g,{scope:r,cancelRef:f,children:(0,u.jsxs)(n.UC,{role:"alertdialog",...h,...l,ref:d,onOpenAutoFocus:(0,o.m)(l.onOpenAutoFocus,e=>{var t;e.preventDefault(),null==(t=f.current)||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,u.jsx)(w,{children:i}),(0,u.jsx)(M,{contentRef:c})]})})})});x.displayName=m;var k="AlertDialogTitle",E=s.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...s}=e,i=p(r);return(0,u.jsx)(n.hE,{...i,...s,ref:t})});E.displayName=k;var O="AlertDialogDescription",A=s.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...s}=e,i=p(r);return(0,u.jsx)(n.VY,{...i,...s,ref:t})});A.displayName=O;var C=s.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...s}=e,i=p(r);return(0,u.jsx)(n.bm,{...i,...s,ref:t})});C.displayName="AlertDialogAction";var j="AlertDialogCancel",D=s.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...s}=e,{cancelRef:i}=R(j,r),o=p(r),l=(0,a.s)(t,i);return(0,u.jsx)(n.bm,{...o,...s,ref:l})});D.displayName=j;var M=e=>{let{contentRef:t}=e,r="`".concat(m,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(m,"` by passing a `").concat(O,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(m,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return s.useEffect(()=>{var e;document.getElementById(null==(e=t.current)?void 0:e.getAttribute("aria-describedby"))||console.warn(r)},[r,t]),null},N=f,Q=b,q=v,L=y,H=x,S=C,U=D,G=E,P=A},25657:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(19946).A)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},40968:(e,t,r)=>{r.d(t,{b:()=>o});var s=r(12115),i=r(63655),a=r(95155),n=s.forwardRef((e,t)=>(0,a.jsx)(i.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var o=n},53904:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(19946).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},71610:(e,t,r)=>{r.d(t,{E:()=>b});var s=r(12115),i=r(7165),a=r(76347),n=r(25910),o=r(52020);function l(e,t){return e.filter(e=>!t.includes(e))}var u=class extends n.Q{#e;#t;#r;#s;#i;#a;#n;#o;#l=[];constructor(e,t,r){super(),this.#e=e,this.#s=r,this.#r=[],this.#i=[],this.#t=[],this.setQueries(t)}onSubscribe(){1===this.listeners.size&&this.#i.forEach(e=>{e.subscribe(t=>{this.#u(e,t)})})}onUnsubscribe(){this.listeners.size||this.destroy()}destroy(){this.listeners=new Set,this.#i.forEach(e=>{e.destroy()})}setQueries(e,t){this.#r=e,this.#s=t,i.jG.batch(()=>{let e=this.#i,t=this.#h(this.#r);this.#l=t,t.forEach(e=>e.observer.setOptions(e.defaultedQueryOptions));let r=t.map(e=>e.observer),s=r.map(e=>e.getCurrentResult()),i=r.some((t,r)=>t!==e[r]);(e.length!==r.length||i)&&(this.#i=r,this.#t=s,this.hasListeners()&&(l(e,r).forEach(e=>{e.destroy()}),l(r,e).forEach(e=>{e.subscribe(t=>{this.#u(e,t)})}),this.#c()))})}getCurrentResult(){return this.#t}getQueries(){return this.#i.map(e=>e.getCurrentQuery())}getObservers(){return this.#i}getOptimisticResult(e,t){let r=this.#h(e),s=r.map(e=>e.observer.getOptimisticResult(e.defaultedQueryOptions));return[s,e=>this.#d(e??s,t),()=>this.#p(s,r)]}#p(e,t){return t.map((r,s)=>{let i=e[s];return r.defaultedQueryOptions.notifyOnChangeProps?i:r.observer.trackResult(i,e=>{t.forEach(t=>{t.observer.trackProp(e)})})})}#d(e,t){return t?(this.#a&&this.#t===this.#o&&t===this.#n||(this.#n=t,this.#o=this.#t,this.#a=(0,o.BH)(this.#a,t(e))),this.#a):e}#h(e){let t=new Map(this.#i.map(e=>[e.options.queryHash,e])),r=[];return e.forEach(e=>{let s=this.#e.defaultQueryOptions(e),i=t.get(s.queryHash);i?r.push({defaultedQueryOptions:s,observer:i}):r.push({defaultedQueryOptions:s,observer:new a.$(this.#e,s)})}),r}#u(e,t){let r=this.#i.indexOf(e);-1!==r&&(this.#t=function(e,t,r){let s=e.slice(0);return s[t]=r,s}(this.#t,r,t),this.#c())}#c(){if(this.hasListeners()){let e=this.#a,t=this.#p(this.#t,this.#l);e!==this.#d(t,this.#s?.combine)&&i.jG.batch(()=>{this.listeners.forEach(e=>{e(this.#t)})})}}},h=r(26715),c=r(61581),d=r(80382),p=r(22450),f=r(4791);function b(e,t){let{queries:r,...n}=e,l=(0,h.jE)(t),b=(0,c.w)(),v=(0,d.h)(),y=s.useMemo(()=>r.map(e=>{let t=l.defaultQueryOptions(e);return t._optimisticResults=b?"isRestoring":"optimistic",t}),[r,l,b]);y.forEach(e=>{(0,f.jv)(e),(0,p.LJ)(e,v)}),(0,p.wZ)(v);let[m]=s.useState(()=>new u(l,y,n)),[g,R,w]=m.getOptimisticResult(y,n.combine),x=!b&&!1!==n.subscribed;s.useSyncExternalStore(s.useCallback(e=>x?m.subscribe(i.jG.batchCalls(e)):o.lQ,[m,x]),()=>m.getCurrentResult(),()=>m.getCurrentResult()),s.useEffect(()=>{m.setQueries(y,n)},[y,n,m]);let k=g.some((e,t)=>(0,f.EU)(y[t],e))?g.flatMap((e,t)=>{let r=y[t];if(r){let t=new a.$(l,r);if((0,f.EU)(r,e))return(0,f.iL)(r,t,v);(0,f.nE)(e,b)&&(0,f.iL)(r,t,v)}return[]}):[];if(k.length>0)throw Promise.all(k);let E=g.find((e,t)=>{let r=y[t];return r&&(0,p.$1)({result:e,errorResetBoundary:v,throwOnError:r.throwOnError,query:l.getQueryCache().get(r.queryHash),suspense:r.suspense})});if(null==E?void 0:E.error)throw E.error;return R(w())}},85339:(e,t,r)=>{r.d(t,{A:()=>s});let s=(0,r(19946).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},87489:(e,t,r)=>{r.d(t,{b:()=>u});var s=r(12115),i=r(63655),a=r(95155),n="horizontal",o=["horizontal","vertical"],l=s.forwardRef((e,t)=>{var r;let{decorative:s,orientation:l=n,...u}=e,h=(r=l,o.includes(r))?l:n;return(0,a.jsx)(i.sG.div,{"data-orientation":h,...s?{role:"none"}:{"aria-orientation":"vertical"===h?h:void 0,role:"separator"},...u,ref:t})});l.displayName="Separator";var u=l}}]);