"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[359,3065],{63065:(e,n,t)=>{t.r(n),t.d(n,{default:()=>r});var a=t(90359);let i=Object.freeze(JSON.parse('{"displayName":"Nginx","fileTypes":["conf.erb","conf","ngx","nginx.conf","mime.types","fastcgi_params","scgi_params","uwsgi_params"],"foldingStartMarker":"\\\\{\\\\s*$","foldingStopMarker":"^\\\\s*}","name":"nginx","patterns":[{"match":"#.*","name":"comment.line.number-sign"},{"begin":"\\\\b((?:content|rewrite|access|init_worker|init|set|log|balancer|ssl_(?:client_hello|session_fetch|certificate))_by_lua(?:_block)?)\\\\s*\\\\{","beginCaptures":{"1":{"name":"storage.type.directive.context.nginx"}},"contentName":"meta.embedded.block.lua","end":"}","name":"meta.context.lua.nginx","patterns":[{"include":"source.lua"}]},{"begin":"\\\\b((?:content|rewrite|access|init_worker|init|set|log|balancer|ssl_(?:client_hello|session_fetch|certificate))_by_lua)\\\\s*\'","beginCaptures":{"1":{"name":"storage.type.directive.context.nginx"}},"contentName":"meta.embedded.block.lua","end":"\'","name":"meta.context.lua.nginx","patterns":[{"include":"source.lua"}]},{"begin":"\\\\b(events) +\\\\{","beginCaptures":{"1":{"name":"storage.type.directive.context.nginx"}},"end":"}","name":"meta.context.events.nginx","patterns":[{"include":"$self"}]},{"begin":"\\\\b(http) +\\\\{","beginCaptures":{"1":{"name":"storage.type.directive.context.nginx"}},"end":"}","name":"meta.context.http.nginx","patterns":[{"include":"$self"}]},{"begin":"\\\\b(mail) +\\\\{","beginCaptures":{"1":{"name":"storage.type.directive.context.nginx"}},"end":"}","name":"meta.context.mail.nginx","patterns":[{"include":"$self"}]},{"begin":"\\\\b(stream) +\\\\{","beginCaptures":{"1":{"name":"storage.type.directive.context.nginx"}},"end":"}","name":"meta.context.stream.nginx","patterns":[{"include":"$self"}]},{"begin":"\\\\b(server) +\\\\{","beginCaptures":{"1":{"name":"storage.type.directive.context.nginx"}},"end":"}","name":"meta.context.server.nginx","patterns":[{"include":"$self"}]},{"begin":"\\\\b(location) +(\\\\^?~\\\\*?|=) +(.*?)\\\\{","beginCaptures":{"1":{"name":"storage.type.directive.context.nginx"},"2":{"name":"keyword.operator.nginx"},"3":{"name":"string.regexp.nginx"}},"end":"}","name":"meta.context.location.nginx","patterns":[{"include":"$self"}]},{"begin":"\\\\b(location) +(.*?)\\\\{","beginCaptures":{"1":{"name":"storage.type.directive.context.nginx"},"2":{"name":"entity.name.context.location.nginx"}},"end":"}","name":"meta.context.location.nginx","patterns":[{"include":"$self"}]},{"begin":"\\\\b(limit_except) +\\\\{","beginCaptures":{"1":{"name":"storage.type.directive.context.nginx"}},"end":"}","name":"meta.context.limit_except.nginx","patterns":[{"include":"$self"}]},{"begin":"\\\\b(if) +\\\\(","beginCaptures":{"1":{"name":"keyword.control.nginx"}},"end":"\\\\)","name":"meta.context.if.nginx","patterns":[{"include":"#if_condition"}]},{"begin":"\\\\b(upstream) +(.*?)\\\\{","beginCaptures":{"1":{"name":"storage.type.directive.context.nginx"},"2":{"name":"entity.name.context.location.nginx"}},"end":"}","name":"meta.context.upstream.nginx","patterns":[{"include":"$self"}]},{"begin":"\\\\b(types) +\\\\{","beginCaptures":{"1":{"name":"storage.type.directive.context.nginx"}},"end":"}","name":"meta.context.types.nginx","patterns":[{"include":"$self"}]},{"begin":"\\\\b(map) +(\\\\$)([0-9A-Z_a-z]+) +(\\\\$)([0-9A-Z_a-z]+) *\\\\{","beginCaptures":{"1":{"name":"storage.type.directive.context.nginx"},"2":{"name":"punctuation.definition.variable.nginx"},"3":{"name":"variable.parameter.nginx"},"4":{"name":"punctuation.definition.variable.nginx"},"5":{"name":"variable.other.nginx"}},"end":"}","name":"meta.context.map.nginx","patterns":[{"include":"#values"},{"match":";","name":"punctuation.terminator.nginx"},{"match":"#.*","name":"comment.line.number-sign"}]},{"begin":"\\\\{","end":"}","name":"meta.block.nginx","patterns":[{"include":"$self"}]},{"begin":"\\\\b(return)\\\\b","beginCaptures":{"1":{"name":"keyword.control.nginx"}},"end":";","patterns":[{"include":"#values"}]},{"begin":"\\\\b(rewrite)\\\\s+","beginCaptures":{"1":{"name":"keyword.directive.nginx"}},"end":"(last|break|redirect|permanent)?(;)","endCaptures":{"1":{"name":"keyword.other.nginx"},"2":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"\\\\b(server)\\\\s+","beginCaptures":{"1":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"1":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#server_parameters"}]},{"begin":"\\\\b(internal|empty_gif|f4f|flv|hls|mp4|break|status|stub_status|ip_hash|ntlm|least_conn|upstream_conf|least_conn|zone_sync)\\\\b","beginCaptures":{"1":{"name":"keyword.directive.nginx"}},"end":"(;|$)","endCaptures":{"1":{"name":"punctuation.terminator.nginx"}}},{"begin":"([\\"\'\\\\s]|^)(accept_)(mutex(?:|_delay))([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(debug_)(connection|points)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(error_)(log|page)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(ssl_)(engine|buffer_size|certificate|certificate_key|ciphers|client_certificate|conf_command|crl|dhparam|early_data|ecdh_curve|ocsp|ocsp_cache|ocsp_responder|password_file|prefer_server_ciphers|protocols|reject_handshake|session_cache|session_ticket_key|session_tickets|session_timeout|stapling|stapling_file|stapling_responder|stapling_verify|trusted_certificate|verify_client|verify_depth|alpn|handshake_timeout|preread)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(worker_)(aio_requests|connections|cpu_affinity|priority|processes|rlimit_core|rlimit_nofile|shutdown_timeout)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(auth_)(delay|basic|basic_user_file|jwt|jwt_claim_set|jwt_header_set|jwt_key_cache|jwt_key_file|jwt_key_request|jwt_leeway|jwt_type|jwt_require|request|request_set|http|http_header|http_pass_client_cert|http_timeout)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(client_)(body_buffer_size|body_in_file_only|body_in_single_buffer|body_temp_path|body_timeout|header_buffer_size|header_timeout|max_body_size)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(keepalive_)(disable|requests|time|timeout)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(limit_)(rate|rate_after|conn|conn_dry_run|conn_log_level|conn_status|conn_zone|zone|req|req_dry_run|req_log_level|req_status|req_zone)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(lingering_)(close|time|timeout)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(log_)(not_found|subrequest|format)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(max_)(ranges|errors)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(msie_)(padding|refresh)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(open_)(file_cache|file_cache_errors|file_cache_min_uses|file_cache_valid|log_file_cache)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(send_)(lowat|timeout)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(server_)(name|name_in_redirect|names_hash_bucket_size|names_hash_max_size|tokens)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(tcp_)(no(?:delay|push))([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(types_)(hash_(?:bucket|max)_size)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(variables_)(hash_(?:bucket|max)_size)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(add_)(before_body|after_body|header|trailer)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(status_)(zone|format)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(autoindex_)(exact_size|format|localtime)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(ancient_)(browser(?:|_value))([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(modern_)(browser(?:|_value))([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(charset_)(map|types)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(dav_)(access|methods)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(fastcgi_)(bind|buffer_size|buffering|buffers|busy_buffers_size|cache|cache_background_update|cache_bypass|cache_key|cache_lock|cache_lock_age|cache_lock_timeout|cache_max_range_offset|cache_methods|cache_min_uses|cache_path|cache_purge|cache_revalidate|cache_use_stale|cache_valid|catch_stderr|connect_timeout|force_ranges|hide_header|ignore_client_abort|ignore_headers|index|intercept_errors|keep_conn|limit_rate|max_temp_file_size|next_upstream|next_upstream_timeout|next_upstream_tries|no_cache|param|pass|pass_header|pass_request_body|pass_request_headers|read_timeout|request_buffering|send_lowat|send_timeout|socket_keepalive|split_path_info|store|store_access|temp_file_write_size|temp_path)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(geoip_)(country|city|org|proxy|proxy_recursive)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(grpc_)(bind|buffer_size|connect_timeout|hide_header|ignore_headers|intercept_errors|next_upstream|next_upstream_timeout|next_upstream_tries|pass|pass_header|read_timeout|send_timeout|set_header|socket_keepalive|ssl_certificate|ssl_certificate_key|ssl_ciphers|ssl_conf_command|ssl_crl|ssl_name|ssl_password_file|ssl_protocols|ssl_server_name|ssl_session_reuse|ssl_trusted_certificate|ssl_verify|ssl_verify_depth)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(gzip_)(buffers|comp_level|disable|http_version|min_length|proxied|types|vary|static)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(hls_)(buffers|forward_args|fragment|mp4_buffer_size|mp4_max_buffer_size)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(image_)(filter(?:|_buffer|_interlace|_jpeg_quality|_sharpen|_transparency|_webp_quality))([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(map_)(hash_(?:bucket|max)_size)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(memcached_)(bind|buffer_size|connect_timeout|gzip_flag|next_upstream|next_upstream_timeout|next_upstream_tries|pass|read_timeout|send_timeout|socket_keepalive)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(mp4_)(buffer_size|max_buffer_size|limit_rate|limit_rate_after|start_key_frame)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(perl_)(modules|require|set)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(proxy_)(bind|buffer_size|buffering|buffers|busy_buffers_size|cache|cache_background_update|cache_bypass|cache_convert_head|cache_key|cache_lock|cache_lock_age|cache_lock_timeout|cache_max_range_offset|cache_methods|cache_min_uses|cache_path|cache_purge|cache_revalidate|cache_use_stale|cache_valid|connect_timeout|cookie_domain|cookie_flags|cookie_path|force_ranges|headers_hash_bucket_size|headers_hash_max_size|hide_header|http_version|ignore_client_abort|ignore_headers|intercept_errors|limit_rate|max_temp_file_size|method|next_upstream|next_upstream_timeout|next_upstream_tries|no_cache|pass|pass_header|pass_request_body|pass_request_headers|read_timeout|redirect|request_buffering|send_lowat|send_timeout|set_body|set_header|socket_keepalive|ssl_certificate|ssl_certificate_key|ssl_ciphers|ssl_conf_command|ssl_crl|ssl_name|ssl_password_file|ssl_protocols|ssl_server_name|ssl_session_reuse|ssl_trusted_certificate|ssl_verify|ssl_verify_depth|store|store_access|temp_file_write_size|temp_path|buffer|pass_error_message|protocol|smtp_auth|timeout|protocol_timeout|download_rate|half_close|requests|responses|session_drop|ssl|upload_rate)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(real_)(ip_(?:header|recursive))([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(referer_)(hash_(?:bucket|max)_size)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(scgi_)(bind|buffer_size|buffering|buffers|busy_buffers_size|cache|cache_background_update|cache_bypass|cache_key|cache_lock|cache_lock_age|cache_lock_timeout|cache_max_range_offset|cache_methods|cache_min_uses|cache_path|cache_purge|cache_revalidate|cache_use_stale|cache_valid|connect_timeout|force_ranges|hide_header|ignore_client_abort|ignore_headers|intercept_errors|limit_rate|max_temp_file_size|next_upstream|next_upstream_timeout|next_upstream_tries|no_cache|param|pass|pass_header|pass_request_body|pass_request_headers|read_timeout|request_buffering|send_timeout|socket_keepalive|store|store_access|temp_file_write_size|temp_path)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(secure_)(link(?:|_md5|_secret))([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(session_)(log(?:|_format|_zone))([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(ssi_)(last_modified|min_file_chunk|silent_errors|types|value_length)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(sub_)(filter(?:|_last_modified|_once|_types))([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(health_)(check(?:|_timeout))([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(userid_)(domain|expires|flags|mark|name|p3p|path|service)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(uwsgi_)(bind|buffer_size|buffering|buffers|busy_buffers_size|cache|cache_background_update|cache_bypass|cache_key|cache_lock|cache_lock_age|cache_lock_timeout|cache_max_range_offset|cache_methods|cache_min_uses|cache_path|cache_purge|cache_revalidate|cache_use_stale|cache_valid|connect_timeout|force_ranges|hide_header|ignore_client_abort|ignore_headers|intercept_errors|limit_rate|max_temp_file_size|modifier1|modifier2|next_upstream|next_upstream_timeout|next_upstream_tries|no_cache|param|pass|pass_header|pass_request_body|pass_request_headers|read_timeout|request_buffering|send_timeout|socket_keepalive|ssl_certificate|ssl_certificate_key|ssl_ciphers|ssl_conf_command|ssl_crl|ssl_name|ssl_password_file|ssl_protocols|ssl_server_name|ssl_session_reuse|ssl_trusted_certificate|ssl_verify|ssl_verify_depth|store|store_access|temp_file_write_size|temp_path)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(http2_)(body_preread_size|chunk_size|idle_timeout|max_concurrent_pushes|max_concurrent_streams|max_field_size|max_header_size|max_requests|push|push_preload|recv_buffer_size|recv_timeout)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(http3_)(hq|max_concurrent_streams|stream_buffer_size)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(quic_)(active_connection_id_limit|bpf|gso|host_key|retry)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(xslt_)(last_modified|param|string_param|stylesheet|types)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(imap_)(auth|capabilities|client_buffer)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(pop3_)(auth|capabilities)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(smtp_)(auth|capabilities|client_buffer|greeting_delay)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(preread_)(buffer_size|timeout)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(mqtt_)(preread|buffers|rewrite_buffer_size|set_connect)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(zone_)(sync_(?:buffers|connect_retry_interval|connect_timeout|interval|recv_buffer_size|server|ssl|ssl_certificate|ssl_certificate_key|ssl_ciphers|ssl_conf_command|ssl_crl|ssl_name|ssl_password_file|ssl_protocols|ssl_server_name|ssl_trusted_certificate|ssl_verify|ssl_verify_depth|timeout))([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(otel_)(exporter|service_name|trace|trace_context|span_name|span_attr)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(js_)(body_filter|content|fetch_buffer_size|fetch_ciphers|fetch_max_response_buffer_size|fetch_protocols|fetch_timeout|fetch_trusted_certificate|fetch_verify|fetch_verify_depth|header_filter|import|include|path|periodic|preload_object|set|shared_dict_zone|var|access|filter|preread)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"},"4":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"([\\"\'\\\\s]|^)(daemon|env|include|pid|user??|aio|alias|directio|etag|listen|resolver|root|satisfy|sendfile|allow|deny|api|autoindex|charset|geo|gunzip|gzip|expires|index|keyval|mirror|perl|set|slice|ssi|ssl|zone|state|hash|keepalive|queue|random|sticky|match|userid|http2|http3|protocol|timeout|xclient|starttls|mqtt|load_module|lock_file|master_process|multi_accept|pcre_jit|thread_pool|timer_resolution|working_directory|absolute_redirect|aio_write|chunked_transfer_encoding|connection_pool_size|default_type|directio_alignment|disable_symlinks|if_modified_since|ignore_invalid_headers|large_client_header_buffers|merge_slashes|output_buffers|port_in_redirect|postpone_output|read_ahead|recursive_error_pages|request_pool_size|reset_timedout_connection|resolver_timeout|sendfile_max_chunk|subrequest_output_buffer_size|try_files|underscores_in_headers|addition_types|override_charset|source_charset|create_full_put_path|min_delete_depth|f4f_buffer_size|gunzip_buffers|internal_redirect|keyval_zone|access_log|mirror_request_body|random_index|set_real_ip_from|valid_referers|rewrite_log|uninitialized_variable_warn|split_clients|least_time|sticky_cookie_insert|xml_entities|google_perftools_profiles)([\\"\'\\\\s]|$)","beginCaptures":{"1":{"name":"keyword.directive.nginx"},"2":{"name":"keyword.directive.nginx"},"3":{"name":"keyword.directive.nginx"}},"end":";","endCaptures":{"0":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"\\\\b([0-9A-Z_a-z]+)\\\\s+","beginCaptures":{"1":{"name":"keyword.directive.unknown.nginx"}},"end":"(;|$)","endCaptures":{"1":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]},{"begin":"\\\\b([a-z]+/[-+.0-9A-Za-z]+)\\\\b","beginCaptures":{"1":{"name":"constant.other.mediatype.nginx"}},"end":"(;)","endCaptures":{"1":{"name":"punctuation.terminator.nginx"}},"patterns":[{"include":"#values"}]}],"repository":{"if_condition":{"patterns":[{"include":"#variables"},{"match":"!?~\\\\*?\\\\s","name":"keyword.operator.nginx"},{"match":"!?-[defx]\\\\s","name":"keyword.operator.nginx"},{"match":"!?=[^=]","name":"keyword.operator.nginx"},{"include":"#regexp_and_string"}]},"regexp_and_string":{"patterns":[{"match":"\\\\^.*?\\\\$","name":"string.regexp.nginx"},{"begin":"\\"","end":"\\"","name":"string.quoted.double.nginx","patterns":[{"match":"\\\\\\\\[\\"\'\\\\\\\\nt]","name":"constant.character.escape.nginx"},{"include":"#variables"}]},{"begin":"\'","end":"\'","name":"string.quoted.single.nginx","patterns":[{"match":"\\\\\\\\[\\"\'\\\\\\\\nt]","name":"constant.character.escape.nginx"},{"include":"#variables"}]}]},"server_parameters":{"patterns":[{"captures":{"1":{"name":"variable.parameter.nginx"},"2":{"name":"keyword.operator.nginx"},"3":{"name":"constant.numeric.nginx"}},"match":"(?:^|\\\\s)(weight|max_conn|max_fails|fail_timeout|slow_start)(=)(\\\\d[.\\\\d]*[BDGHKMSTbdghkmst]?)(?:[;\\\\s]|$)"},{"include":"#values"}]},"values":{"patterns":[{"include":"#variables"},{"match":"#.*","name":"comment.line.number-sign"},{"captures":{"1":{"name":"constant.numeric.nginx"}},"match":"(?<=\\\\G|\\\\s)(=?[0-9][.0-9]*[BDGHKMSTbdghkmst]?)(?=[\\\\t ;])"},{"match":"(?<=\\\\G|\\\\s)(on|off|true|false)(?=[\\\\t ;])","name":"constant.language.nginx"},{"match":"(?<=\\\\G|\\\\s)(kqueue|rtsig|epoll|/dev/poll|select|poll|eventport|max|all|default_server|default|main|crit|error|debug|warn|notice|last)(?=[\\\\t ;])","name":"constant.language.nginx"},{"match":"\\\\\\\\.* |~\\\\*?|!~\\\\*?","name":"keyword.operator.nginx"},{"include":"#regexp_and_string"}]},"variables":{"patterns":[{"captures":{"1":{"name":"punctuation.definition.variable.nginx"},"2":{"name":"variable.other.nginx"}},"match":"(\\\\$)([0-9A-Z_a-z]+)\\\\b"},{"captures":{"1":{"name":"punctuation.definition.variable.nginx"},"2":{"name":"variable.other.nginx"},"3":{"name":"punctuation.definition.variable.nginx"}},"match":"(\\\\$\\\\{)([0-9A-Z_a-z]+)(})"}]}},"scopeName":"source.nginx","embeddedLangs":["lua"]}')),r=[...a.default,i]},90359:(e,n,t)=>{t.r(n),t.d(n,{default:()=>r});var a=t(1442);let i=Object.freeze(JSON.parse('{"displayName":"Lua","name":"lua","patterns":[{"begin":"\\\\b(?:(local)\\\\s+)?(function)\\\\b(?![,:])","beginCaptures":{"1":{"name":"keyword.local.lua"},"2":{"name":"keyword.control.lua"}},"end":"(?<=[-\\\\]\\"\')\\\\[{}])","name":"meta.function.lua","patterns":[{"include":"#comment"},{"begin":"(\\\\()","beginCaptures":{"1":{"name":"punctuation.definition.parameters.begin.lua"}},"end":"(\\\\))|(?=[-\\\\]\\"\'.\\\\[{}])","endCaptures":{"1":{"name":"punctuation.definition.parameters.finish.lua"}},"name":"meta.parameter.lua","patterns":[{"include":"#comment"},{"match":"[A-Z_a-z][0-9A-Z_a-z]*","name":"variable.parameter.function.lua"},{"match":",","name":"punctuation.separator.arguments.lua"},{"begin":":","beginCaptures":{"0":{"name":"punctuation.separator.arguments.lua"}},"end":"(?=[),])","patterns":[{"include":"#emmydoc.type"}]}]},{"match":"\\\\b([A-Z_a-z][0-9A-Z_a-z]*)\\\\b\\\\s*(?=:)","name":"entity.name.class.lua"},{"match":"\\\\b([A-Z_a-z][0-9A-Z_a-z]*)\\\\b","name":"entity.name.function.lua"}]},{"match":"(?<![.\\\\w\\\\d])0[Xx]\\\\h+(\\\\.\\\\h*)?([Ee]-?\\\\d*)?([Pp][-+]\\\\d+)?","name":"constant.numeric.float.hexadecimal.lua"},{"match":"(?<![.\\\\w\\\\d])0[Xx]\\\\.\\\\h+([Ee]-?\\\\d*)?([Pp][-+]\\\\d+)?","name":"constant.numeric.float.hexadecimal.lua"},{"match":"(?<![.\\\\w\\\\d])0[Xx]\\\\h+(?![.0-9EPep])","name":"constant.numeric.integer.hexadecimal.lua"},{"match":"(?<![.\\\\w\\\\d])\\\\d+(\\\\.\\\\d*)?([Ee]-?\\\\d*)?","name":"constant.numeric.float.lua"},{"match":"(?<![.\\\\w\\\\d])\\\\.\\\\d+([Ee]-?\\\\d*)?","name":"constant.numeric.float.lua"},{"match":"(?<![.\\\\w\\\\d])\\\\d+(?![.0-9EPep])","name":"constant.numeric.integer.lua"},{"include":"#string"},{"captures":{"1":{"name":"punctuation.definition.comment.lua"}},"match":"\\\\A(#!).*$\\\\n?","name":"comment.line.shebang.lua"},{"include":"#comment"},{"captures":{"1":{"name":"keyword.control.goto.lua"},"2":{"name":"string.tag.lua"}},"match":"\\\\b(goto)\\\\s+([A-Z_a-z][0-9A-Z_a-z]*)"},{"captures":{"1":{"name":"punctuation.section.embedded.begin.lua"},"2":{"name":"punctuation.section.embedded.end.lua"}},"match":"(::)\\\\s*[A-Z_a-z][0-9A-Z_a-z]*\\\\s*(::)","name":"string.tag.lua"},{"captures":{"0":{"name":"storage.type.attribute.lua"}},"match":"<\\\\s*(c(?:onst|lose))\\\\s*>"},{"match":"<[*A-Z_a-z][-*.0-9A-Z_a-z]*>","name":"storage.type.generic.lua"},{"match":"\\\\b(break|do|else|for|if|elseif|goto|return|then|repeat|while|until|end|in)\\\\b","name":"keyword.control.lua"},{"match":"\\\\b(local)\\\\b","name":"keyword.local.lua"},{"match":"\\\\b(function)\\\\b(?![,:])","name":"keyword.control.lua"},{"match":"(?<![^.]\\\\.|:)\\\\b(false|nil(?!:)|true|_ENV|_G|_VERSION|math\\\\.(pi|huge|maxinteger|mininteger)|utf8\\\\.charpattern|io\\\\.(std(?:in|out|err))|package\\\\.(config|cpath|loaded|loaders|path|preload|searchers))\\\\b|(?<!\\\\.)\\\\.{3}(?!\\\\.)","name":"constant.language.lua"},{"match":"(?<![^.]\\\\.|:)\\\\b(self)\\\\b","name":"variable.language.self.lua"},{"match":"(?<![^.]\\\\.|:)\\\\b(assert|collectgarbage|dofile|error|getfenv|getmetatable|ipairs|load|loadfile|loadstring|module|next|pairs|pcall|print|rawequal|rawget|rawlen|rawset|require|select|setfenv|setmetatable|tonumber|tostring|type|unpack|xpcall)\\\\b(?!\\\\s*=(?!=))","name":"support.function.lua"},{"match":"(?<![^.]\\\\.|:)\\\\b(async)\\\\b(?!\\\\s*=(?!=))","name":"entity.name.tag.lua"},{"match":"(?<![^.]\\\\.|:)\\\\b(coroutine\\\\.(create|isyieldable|close|resume|running|status|wrap|yield)|string\\\\.(byte|char|dump|find|format|gmatch|gsub|len|lower|match|pack|packsize|rep|reverse|sub|unpack|upper)|table\\\\.(concat|insert|maxn|move|pack|remove|sort|unpack)|math\\\\.(abs|acos|asin|atan2?|ceil|cosh?|deg|exp|floor|fmod|frexp|ldexp|log|log10|max|min|modf|pow|rad|random|randomseed|sinh?|sqrt|tanh?|tointeger|type)|io\\\\.(close|flush|input|lines|open|output|popen|read|tmpfile|type|write)|os\\\\.(clock|date|difftime|execute|exit|getenv|remove|rename|setlocale|time|tmpname)|package\\\\.(loadlib|seeall|searchpath)|debug\\\\.(debug|[gs]etfenv|[gs]ethook|getinfo|[gs]etlocal|[gs]etmetatable|getregistry|[gs]etupvalue|[gs]etuservalue|set[Cc]stacklimit|traceback|upvalueid|upvaluejoin)|bit32\\\\.(arshift|band|bnot|bor|btest|bxor|extract|replace|lrotate|lshift|rrotate|rshift)|utf8\\\\.(char|codes|codepoint|len|offset))\\\\b(?!\\\\s*=(?!=))","name":"support.function.library.lua"},{"match":"\\\\b(and|or|not|\\\\|\\\\||&&|!)\\\\b","name":"keyword.operator.lua"},{"match":"\\\\b([A-Z_a-z][0-9A-Z_a-z]*)\\\\b(?=\\\\s*(?:[\\"\'({]|\\\\[\\\\[))","name":"support.function.any-method.lua"},{"match":"\\\\b([A-Z_a-z][0-9A-Z_a-z]*)\\\\b(?=\\\\s*\\\\??:)","name":"entity.name.class.lua"},{"match":"(?<=[^.]\\\\.|:)\\\\b([A-Z_a-z][0-9A-Z_a-z]*)\\\\b(?!\\\\s*=\\\\s*\\\\b(function)\\\\b)","name":"entity.other.attribute.lua"},{"match":"\\\\b([A-Z_a-z][0-9A-Z_a-z]*)\\\\b(?!\\\\s*=\\\\s*\\\\b(function)\\\\b)","name":"variable.other.lua"},{"match":"\\\\b([A-Z_a-z][0-9A-Z_a-z]*)\\\\b(?=\\\\s*=\\\\s*\\\\b(function)\\\\b)","name":"entity.name.function.lua"},{"match":"[-#%*+/^]|==?|~=|!=|<=?|>=?|(?<!\\\\.)\\\\.{2}(?!\\\\.)","name":"keyword.operator.lua"}],"repository":{"comment":{"patterns":[{"begin":"(^[\\\\t ]+)?(?=--)","beginCaptures":{"1":{"name":"punctuation.whitespace.comment.leading.lua"}},"end":"(?!\\\\G)((?!^)[\\\\t ]+\\\\n)?","endCaptures":{"1":{"name":"punctuation.whitespace.comment.trailing.lua"}},"patterns":[{"begin":"--\\\\[(=*)\\\\[@@@","beginCaptures":{"0":{"name":"punctuation.definition.comment.begin.lua"}},"end":"(--)?]\\\\1]","endCaptures":{"0":{"name":"punctuation.definition.comment.end.lua"}},"name":"","patterns":[{"include":"source.lua"}]},{"begin":"--\\\\[(=*)\\\\[","beginCaptures":{"0":{"name":"punctuation.definition.comment.begin.lua"}},"end":"(--)?]\\\\1]","endCaptures":{"0":{"name":"punctuation.definition.comment.end.lua"}},"name":"comment.block.lua","patterns":[{"include":"#emmydoc"},{"include":"#ldoc_tag"}]},{"begin":"----","beginCaptures":{"0":{"name":"punctuation.definition.comment.lua"}},"end":"\\\\n","name":"comment.line.double-dash.lua"},{"begin":"---","beginCaptures":{"0":{"name":"punctuation.definition.comment.lua"}},"end":"\\\\n","name":"comment.line.double-dash.documentation.lua","patterns":[{"include":"#emmydoc"},{"include":"#ldoc_tag"}]},{"begin":"--","beginCaptures":{"0":{"name":"punctuation.definition.comment.lua"}},"end":"\\\\n","name":"comment.line.double-dash.lua","patterns":[{"include":"#ldoc_tag"}]}]},{"begin":"/\\\\*","beginCaptures":{"0":{"name":"punctuation.definition.comment.begin.lua"}},"end":"\\\\*/","endCaptures":{"0":{"name":"punctuation.definition.comment.end.lua"}},"name":"comment.block.lua","patterns":[{"include":"#emmydoc"},{"include":"#ldoc_tag"}]}]},"emmydoc":{"patterns":[{"begin":"(?<=---)[\\\\t ]*@class","beginCaptures":{"0":{"name":"storage.type.annotation.lua"}},"end":"(?=[\\\\n#@])","patterns":[{"match":"\\\\b([*A-Z_a-z][-*.0-9A-Z_a-z]*)","name":"support.class.lua"},{"match":"[,:]","name":"keyword.operator.lua"}]},{"begin":"(?<=---)[\\\\t ]*@enum","beginCaptures":{"0":{"name":"storage.type.annotation.lua"}},"end":"(?=[\\\\n#@])","patterns":[{"begin":"\\\\b([*A-Z_a-z][-*.0-9A-Z_a-z]*)","beginCaptures":{"0":{"name":"variable.lua"}},"end":"(?=\\\\n)"}]},{"begin":"(?<=---)[\\\\t ]*@type","beginCaptures":{"0":{"name":"storage.type.annotation.lua"}},"end":"(?=[\\\\n#@])","patterns":[{"include":"#emmydoc.type"}]},{"begin":"(?<=---)[\\\\t ]*@alias","beginCaptures":{"0":{"name":"storage.type.annotation.lua"}},"end":"(?=[\\\\n#@])","patterns":[{"begin":"\\\\b([*A-Z_a-z][-*.0-9A-Z_a-z]*)","beginCaptures":{"0":{"name":"variable.lua"}},"end":"(?=[\\\\n#])","patterns":[{"include":"#emmydoc.type"}]}]},{"begin":"(?<=---)[\\\\t ]*(@operator)\\\\s*(\\\\b[a-z]+)?","beginCaptures":{"1":{"name":"storage.type.annotation.lua"},"2":{"name":"support.function.library.lua"}},"end":"(?=[\\\\n#@])","patterns":[{"include":"#emmydoc.type"}]},{"begin":"(?<=---)[\\\\t ]*@cast","beginCaptures":{"0":{"name":"storage.type.annotation.lua"}},"end":"(?=[\\\\n#@])","patterns":[{"begin":"\\\\b([*A-Z_a-z][-*.0-9A-Z_a-z]*)","beginCaptures":{"0":{"name":"variable.other.lua"}},"end":"(?=\\\\n)","patterns":[{"include":"#emmydoc.type"},{"match":"([+-|])","name":"keyword.operator.lua"}]}]},{"begin":"(?<=---)[\\\\t ]*@param","beginCaptures":{"0":{"name":"storage.type.annotation.lua"}},"end":"(?=[\\\\n#@])","patterns":[{"begin":"\\\\b([A-Z_a-z][0-9A-Z_a-z]*)\\\\b(\\\\??)","beginCaptures":{"1":{"name":"entity.name.variable.lua"},"2":{"name":"keyword.operator.lua"}},"end":"(?=[\\\\n#])","patterns":[{"include":"#emmydoc.type"}]}]},{"begin":"(?<=---)[\\\\t ]*@return","beginCaptures":{"0":{"name":"storage.type.annotation.lua"}},"end":"(?=[\\\\n#@])","patterns":[{"match":"\\\\?","name":"keyword.operator.lua"},{"include":"#emmydoc.type"}]},{"begin":"(?<=---)[\\\\t ]*@field","beginCaptures":{"0":{"name":"storage.type.annotation.lua"}},"end":"(?=[\\\\n#@])","patterns":[{"begin":"(\\\\b([A-Z_a-z][0-9A-Z_a-z]*)\\\\b|(\\\\[))(\\\\??)","beginCaptures":{"2":{"name":"entity.name.variable.lua"},"3":{"name":"keyword.operator.lua"}},"end":"(?=[\\\\n#])","patterns":[{"include":"#string"},{"include":"#emmydoc.type"},{"match":"]","name":"keyword.operator.lua"}]}]},{"begin":"(?<=---)[\\\\t ]*@generic","beginCaptures":{"0":{"name":"storage.type.annotation.lua"}},"end":"(?=[\\\\n#@])","patterns":[{"begin":"\\\\b([A-Z_a-z][0-9A-Z_a-z]*)\\\\b","beginCaptures":{"0":{"name":"storage.type.generic.lua"}},"end":"(?=\\\\n)|(,)","endCaptures":{"0":{"name":"keyword.operator.lua"}},"patterns":[{"match":":","name":"keyword.operator.lua"},{"include":"#emmydoc.type"}]}]},{"begin":"(?<=---)[\\\\t ]*@vararg","beginCaptures":{"0":{"name":"storage.type.annotation.lua"}},"end":"(?=[\\\\n#@])","patterns":[{"include":"#emmydoc.type"}]},{"begin":"(?<=---)[\\\\t ]*@overload","beginCaptures":{"0":{"name":"storage.type.annotation.lua"}},"end":"(?=[\\\\n#@])","patterns":[{"include":"#emmydoc.type"}]},{"begin":"(?<=---)[\\\\t ]*@deprecated","beginCaptures":{"0":{"name":"storage.type.annotation.lua"}},"end":"(?=[\\\\n#@])"},{"begin":"(?<=---)[\\\\t ]*@meta","beginCaptures":{"0":{"name":"storage.type.annotation.lua"}},"end":"(?=[\\\\n#@])"},{"begin":"(?<=---)[\\\\t ]*@private","beginCaptures":{"0":{"name":"storage.type.annotation.lua"}},"end":"(?=[\\\\n#@])"},{"begin":"(?<=---)[\\\\t ]*@protected","beginCaptures":{"0":{"name":"storage.type.annotation.lua"}},"end":"(?=[\\\\n#@])"},{"begin":"(?<=---)[\\\\t ]*@package","beginCaptures":{"0":{"name":"storage.type.annotation.lua"}},"end":"(?=[\\\\n#@])"},{"begin":"(?<=---)[\\\\t ]*@version","beginCaptures":{"0":{"name":"storage.type.annotation.lua"}},"end":"(?=[\\\\n#@])","patterns":[{"match":"\\\\b(5\\\\.1|5\\\\.2|5\\\\.3|5\\\\.4|JIT)\\\\b","name":"support.class.lua"},{"match":"[,<>]","name":"keyword.operator.lua"}]},{"begin":"(?<=---)[\\\\t ]*@see","beginCaptures":{"0":{"name":"storage.type.annotation.lua"}},"end":"(?=[\\\\n#@])","patterns":[{"match":"\\\\b([*A-Z_a-z][-*.0-9A-Z_a-z]*)","name":"support.class.lua"},{"match":"#","name":"keyword.operator.lua"}]},{"begin":"(?<=---)[\\\\t ]*@diagnostic","beginCaptures":{"0":{"name":"storage.type.annotation.lua"}},"end":"(?=[\\\\n#@])","patterns":[{"begin":"([-0-9A-Z_a-z]+)[\\\\t ]*(:)?","beginCaptures":{"1":{"name":"keyword.other.unit"},"2":{"name":"keyword.operator.unit"}},"end":"(?=\\\\n)","patterns":[{"match":"\\\\b([*A-Z_a-z][-0-9A-Z_a-z]*)","name":"support.class.lua"},{"match":",","name":"keyword.operator.lua"}]}]},{"begin":"(?<=---)[\\\\t ]*@module","beginCaptures":{"0":{"name":"storage.type.annotation.lua"}},"end":"(?=[\\\\n#@])","patterns":[{"include":"#string"}]},{"match":"(?<=---)[\\\\t ]*@(async|nodiscard)","name":"storage.type.annotation.lua"},{"begin":"(?<=---)\\\\|\\\\s*[+>]?","beginCaptures":{"0":{"name":"storage.type.annotation.lua"}},"end":"(?=[\\\\n#@])","patterns":[{"include":"#string"}]}]},"emmydoc.type":{"patterns":[{"begin":"\\\\bfun\\\\b","beginCaptures":{"0":{"name":"keyword.control.lua"}},"end":"(?=[#\\\\s])","patterns":[{"match":"[(),:?][\\\\t ]*","name":"keyword.operator.lua"},{"match":"([A-Z_a-z][-\\\\]*,.0-9<>A-\\\\[_a-z]*)(?<!,)[\\\\t ]*(?=\\\\??:)","name":"entity.name.variable.lua"},{"include":"#emmydoc.type"},{"include":"#string"}]},{"match":"<[*A-Z_a-z][-*.0-9A-Z_a-z]*>","name":"storage.type.generic.lua"},{"match":"\\\\basync\\\\b","name":"entity.name.tag.lua"},{"match":"[,:?`{|}][\\\\t ]*","name":"keyword.operator.lua"},{"begin":"(?=[\\"\'*.A-\\\\[_a-z])","end":"(?=[#),:?|}\\\\s])","patterns":[{"match":"([-\\\\]*,.0-9<>A-\\\\[_a-z]+)(?<!,)[\\\\t ]*","name":"support.type.lua"},{"match":"(\\\\.\\\\.\\\\.)[\\\\t ]*","name":"constant.language.lua"},{"include":"#string"}]}]},"escaped_char":{"patterns":[{"match":"\\\\\\\\[\\\\n\\"\'\\\\\\\\abfnrtv]","name":"constant.character.escape.lua"},{"match":"\\\\\\\\z[\\\\t\\\\n ]*","name":"constant.character.escape.lua"},{"match":"\\\\\\\\\\\\d{1,3}","name":"constant.character.escape.byte.lua"},{"match":"\\\\\\\\x\\\\h\\\\h","name":"constant.character.escape.byte.lua"},{"match":"\\\\\\\\u\\\\{\\\\h+}","name":"constant.character.escape.unicode.lua"},{"match":"\\\\\\\\.","name":"invalid.illegal.character.escape.lua"}]},"ldoc_tag":{"captures":{"1":{"name":"punctuation.definition.block.tag.ldoc"},"2":{"name":"storage.type.class.ldoc"}},"match":"\\\\G[\\\\t ]*(@)(alias|annotation|author|charset|class|classmod|comment|constructor|copyright|description|example|export|factory|field|file|fixme|function|include|lfunction|license|local|module|name|param|pragma|private|raise|release|return|script|section|see|set|static|submodule|summary|tfield|thread|tparam|treturn|todo|topic|type|usage|warning|within)\\\\b"},"string":{"patterns":[{"begin":"\'","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.lua"}},"end":"\'[\\\\t ]*|(?=\\\\n)","endCaptures":{"0":{"name":"punctuation.definition.string.end.lua"}},"name":"string.quoted.single.lua","patterns":[{"include":"#escaped_char"}]},{"begin":"\\"","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.lua"}},"end":"\\"[\\\\t ]*|(?=\\\\n)","endCaptures":{"0":{"name":"punctuation.definition.string.end.lua"}},"name":"string.quoted.double.lua","patterns":[{"include":"#escaped_char"}]},{"begin":"`","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.lua"}},"end":"`[\\\\t ]*|(?=\\\\n)","endCaptures":{"0":{"name":"punctuation.definition.string.end.lua"}},"name":"string.quoted.double.lua"},{"begin":"(?<=\\\\.cdef)\\\\s*(\\\\[(=*)\\\\[)","beginCaptures":{"0":{"name":"string.quoted.other.multiline.lua"},"1":{"name":"punctuation.definition.string.begin.lua"}},"contentName":"meta.embedded.lua","end":"(]\\\\2])[\\\\t ]*","endCaptures":{"0":{"name":"string.quoted.other.multiline.lua"},"1":{"name":"punctuation.definition.string.end.lua"}},"patterns":[{"include":"source.c"}]},{"begin":"(?\x3c!--)\\\\[(=*)\\\\[","beginCaptures":{"0":{"name":"punctuation.definition.string.begin.lua"}},"end":"]\\\\1][\\\\t ]*","endCaptures":{"0":{"name":"punctuation.definition.string.end.lua"}},"name":"string.quoted.other.multiline.lua"}]}},"scopeName":"source.lua","embeddedLangs":["c"]}')),r=[...a.default,i]}}]);