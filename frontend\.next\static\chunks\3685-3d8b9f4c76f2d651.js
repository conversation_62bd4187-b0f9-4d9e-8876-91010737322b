"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3685],{30285:(e,t,n)=>{n.d(t,{$:()=>c,r:()=>s});var o=n(95155);n(12115);var a=n(99708),r=n(74466),i=n(59434);let s=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",node_outline:"bg-transparent border border-primary/10",node_secondary:"px-0 bg-transparent hover:opacity-60",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-lg gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-lg px-6 has-[>svg]:px-4",icon:"size-9",node_secondary:"px-0"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:n,size:r,asChild:c=!1,...l}=e,d=c?a.DX:"button";return(0,o.jsx)(d,{"data-slot":"button",className:(0,i.cn)(s({variant:n,size:r,className:t})),...l})}},34986:(e,t,n)=>{n.d(t,{Be:()=>d,OG:()=>b,R2:()=>f,pH:()=>c.pH,x2:()=>m});var o=n(12115),a=n(28755),r=n(26715),i=n(64541),s=n(25731),c=n(43089);function l(e){if(!e)return"/workspace";e.startsWith("/workspace")||(e="/workspace/".concat(e.startsWith("/")?e.substring(1):e));try{e=e.replace(/\\u([0-9a-fA-F]{4})/g,(e,t)=>String.fromCharCode(parseInt(t,16)))}catch(e){console.error("Error processing Unicode escapes in path:",e)}return e}let d={all:["files"],contents:()=>[...d.all,"content"],content:(e,t,n)=>[...d.contents(),e,l(t),n],directories:()=>[...d.all,"directory"],directory:(e,t)=>[...d.directories(),e,l(t)]};function p(e){if(!e)return"text";let t=e.toLowerCase().split(".").pop()||"";return/^(xlsx|xls|docx|doc|pptx|ppt|pdf|png|jpg|jpeg|gif|bmp|webp|svg|ico|zip|exe|dll|bin|dat|obj|o|so|dylib|mp3|mp4|avi|mov|wmv|flv|wav|ogg)$/.test(t)?"blob":"json"===t?"json":"text"}function u(e){var t;return["png","jpg","jpeg","gif","svg","webp","bmp","ico"].includes((null==(t=e.split(".").pop())?void 0:t.toLowerCase())||"")}async function g(e,t,n,o){let a=l(t),r=new URL("".concat("http://localhost:8000/api","/sandboxes/").concat(e,"/files/content"));r.searchParams.append("path",a),console.log("[FILE QUERY] Fetching ".concat(n," content for: ").concat(a));let i={};o&&(i.Authorization="Bearer ".concat(o));let s=await fetch(r.toString(),{headers:i});if(!s.ok){let e=await s.text();throw Error("Failed to fetch file: ".concat(s.status," ").concat(e))}switch(n){case"json":return await s.json();case"blob":{let e=await s.blob(),n=function(e){var t;switch((null==(t=e.split(".").pop())?void 0:t.toLowerCase())||""){case"xlsx":return"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";case"xls":return"application/vnd.ms-excel";case"docx":return"application/vnd.openxmlformats-officedocument.wordprocessingml.document";case"doc":return"application/msword";case"pptx":return"application/vnd.openxmlformats-officedocument.presentationml.presentation";case"ppt":return"application/vnd.ms-powerpoint";case"pdf":return"application/pdf";case"png":return"image/png";case"jpg":case"jpeg":return"image/jpeg";case"gif":return"image/gif";case"svg":return"image/svg+xml";case"zip":return"application/zip";default:return"application/octet-stream"}}(t);if(n!==e.type&&"application/octet-stream"!==n){console.log("[FILE QUERY] Correcting MIME type for ".concat(t,": ").concat(e.type," → ").concat(n));let o=new Blob([e],{type:n});return u(t)&&console.log("[FILE QUERY] Created image blob:",{originalType:e.type,correctedType:o.type,size:o.size,filePath:t}),o}return u(t)&&console.log("[FILE QUERY] Image blob details:",{type:e.type,size:e.size,filePath:t}),e}default:return await s.text()}}function m(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{session:s}=(0,i.A)(),c=t?l(t):null,u=t?p(t):"text",m=n.contentType||u,f=(0,a.I)({queryKey:e&&c?d.content(e,c,m):[],queryFn:async()=>{if(!e||!c)throw Error("Missing required parameters");return g(e,c,m,(null==s?void 0:s.access_token)||"")},enabled:!!(e&&c&&!1!==n.enabled),staleTime:n.staleTime||("blob"===m?3e5:12e4),gcTime:n.gcTime||6e5,retry:(e,t)=>{var n,o;return!((null==t||null==(n=t.message)?void 0:n.includes("401"))||(null==t||null==(o=t.message)?void 0:o.includes("403")))&&e<3}}),b=(0,r.jE)(),h=o.useCallback(async()=>{if(!e||!t)return null;let n=l(t),o=d.content(e,n,m);return await b.invalidateQueries({queryKey:o}),b.getQueryData(o)||null},[e,t,m,b]);return{...f,refreshCache:h,getCachedFile:()=>Promise.resolve(f.data),getFromCache:()=>f.data,cache:new Map}}function f(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},{session:o}=(0,i.A)(),r=t?l(t):null;return(0,a.I)({queryKey:e&&r?d.directory(e,r):[],queryFn:async()=>{if(!e||!r)throw Error("Missing required parameters");return console.log("[FILE QUERY] Fetching directory listing for: ".concat(r)),await (0,s.XY)(e,r)},enabled:!!(e&&r&&!1!==n.enabled),staleTime:n.staleTime||3e4,gcTime:3e5,retry:2})}function b(){let e=(0,r.jE)(),{session:t}=(0,i.A)();return{preloadFiles:o.useCallback(async(n,o)=>{if(!(null==t?void 0:t.access_token))return void console.warn("Cannot preload files: No authentication token available");let a=[...new Set(o)];console.log("[FILE QUERY] Preloading ".concat(a.length," files for sandbox ").concat(n));let r=a.map(async o=>{let a=l(o),r=p(o),i=d.content(n,a,r),s=e.getQueryData(i);return s?(console.log("[FILE QUERY] Already cached: ".concat(a)),s):e.prefetchQuery({queryKey:i,queryFn:()=>g(n,a,r,t.access_token),staleTime:"blob"===r?3e5:12e4})});await Promise.all(r),console.log("[FILE QUERY] Completed preloading ".concat(a.length," files"))},[e,null==t?void 0:t.access_token])}}},43089:(e,t,n)=>{n.d(t,{pH:()=>i}),n(12115),n(64541);let o=new Map,a=new Map;function r(e){if(!e)return"/workspace";e.startsWith("/workspace")||(e="/workspace/".concat(e.startsWith("/")?e.substring(1):e));try{e=e.replace(/\\u([0-9a-fA-F]{4})/g,(e,t)=>String.fromCharCode(parseInt(t,16)))}catch(e){console.error("Error processing Unicode escapes in path:",e)}return e}let i={get:e=>{var t;return(null==(t=o.get(e))?void 0:t.content)||null},set:(e,t)=>{e&&null!=t&&o.set(e,{content:t,timestamp:Date.now(),type:"string"==typeof t&&t.startsWith("blob:")?"url":"content"})},has:e=>o.has(e),clear:()=>o.clear(),delete:e=>o.delete(e),getContentTypeFromPath:e=>{if(!e)return"text";let t=e.toLowerCase().split(".").pop()||"";return/^(xlsx|xls|docx|doc|pptx|ppt|pdf|png|jpg|jpeg|gif|bmp|webp|svg|ico|zip|exe|dll|bin|dat|obj|o|so|dylib|mp3|mp4|avi|mov|wmv|flv|wav|ogg)$/.test(t)?"blob":"json"===t?"json":"text"},isImageFile:e=>{var t;return["png","jpg","jpeg","gif","svg","webp","bmp","ico"].includes((null==(t=e.split(".").pop())?void 0:t.toLowerCase())||"")},isPdfFile:e=>e.toLowerCase().endsWith(".pdf"),isBlob:e=>e instanceof Blob,getContentType:(e,t)=>t||i.getContentTypeFromPath(e),getMimeTypeFromPath:e=>{var t;switch((null==(t=e.split(".").pop())?void 0:t.toLowerCase())||""){case"xlsx":return"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";case"xls":return"application/vnd.ms-excel";case"docx":return"application/vnd.openxmlformats-officedocument.wordprocessingml.document";case"doc":return"application/msword";case"pptx":return"application/vnd.openxmlformats-officedocument.presentationml.presentation";case"ppt":return"application/vnd.ms-powerpoint";case"pdf":return"application/pdf";case"png":return"image/png";case"jpg":case"jpeg":return"image/jpeg";case"gif":return"image/gif";case"svg":return"image/svg+xml";case"zip":return"application/zip";default:return"application/octet-stream"}},preload:async(e,t,n)=>{if(!n)try{let e=localStorage.getItem("auth");e&&(n=JSON.parse(e).access_token)}catch(e){console.error("Failed to get auth token from localStorage:",e)}if(!n)return console.warn("Cannot preload files: No authentication token available"),[];let s=[...new Set(t)];return s.length<t.length&&console.log("[FILE CACHE] Removed ".concat(t.length-s.length," duplicate file paths")),console.log("[FILE CACHE] Preloading ".concat(s.length," files for sandbox ").concat(e)),Promise.all(s.map(async t=>{let s=r(t=t.replace(/\\u([0-9a-fA-F]{4})/g,(e,t)=>String.fromCharCode(parseInt(t,16)))),c=function(e,t){let n=r(t);return"".concat(e,":").concat(n)}(e,s);if(o.has(c)){var l;return console.log("[FILE CACHE] Already cached: ".concat(s)),null==(l=o.get(c))?void 0:l.content}let d="".concat(e,":").concat(s);if(a.has(d))return console.log("[FILE CACHE] Preload already in progress for: ".concat(s)),a.get(d);console.log("[FILE CACHE] Preloading file: ".concat(s));let p=(async()=>{try{var r;let a,l=new URL("".concat("http://localhost:8000/api","/sandboxes/").concat(e,"/files/content"));l.searchParams.append("path",s);let d=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=await fetch(l.toString(),{headers:{Authorization:"Bearer ".concat(n)}});if(!t.ok){let n=await t.text(),o="Failed to preload file: ".concat(t.status);if(n.includes("Workspace is not running")&&!e)return console.log("[FILE CACHE] Workspace not ready during preload, retrying in 2s for ".concat(s)),await new Promise(e=>setTimeout(e,2e3)),d(!0);throw Error(o)}return t},p=await d(),u=null==(r=t.split(".").pop())?void 0:r.toLowerCase(),g="content";if(["png","jpg","jpeg","gif","pdf","mp3","mp4","xlsx","xls","docx","doc","pptx","ppt","zip","exe","bin"].includes(u||"")){let e=await p.blob();if(i.isImageFile(t))a=e,g="content",console.log("[FILE CACHE] Successfully preloaded image blob: ".concat(s," (").concat(e.size," bytes)"));else if("pdf"===u||["xlsx","xls","docx","doc","pptx","ppt"].includes(u||"")){let n=i.getMimeTypeFromPath(t);a=new Blob([e],{type:n}),g="content",console.log("[FILE CACHE] Successfully preloaded binary blob for ".concat(u," file: ").concat(s," (").concat(e.size," bytes)"))}else a=URL.createObjectURL(e),g="url",console.log("[FILE CACHE] Successfully preloaded blob URL: ".concat(s," (").concat(e.size," bytes)"))}else"json"===u?(a=await p.json(),console.log("[FILE CACHE] Successfully preloaded JSON: ".concat(s))):(a=await p.text(),console.log("[FILE CACHE] Successfully preloaded text: ".concat(s," (").concat(a.length," bytes)")));return o.set(c,{content:a,timestamp:Date.now(),type:g}),a}catch(e){return console.error("[FILE CACHE] Failed to preload ".concat(s,":"),e),o.set(c,{content:null,timestamp:Date.now(),type:"error"}),null}finally{a.delete(d)}})();return a.set(d,p),p}))},getMimeType:e=>i.getMimeTypeFromPath(e)}},68856:(e,t,n)=>{n.d(t,{Skeleton:()=>s});var o=n(95155),a=n(11518),r=n.n(a),i=n(59434);function s(e){let{className:t,...n}=e;return(0,o.jsxs)("div",{...n,className:"jsx-1e687dde282a8b11 "+(n&&null!=n.className&&n.className||(0,i.cn)("relative overflow-hidden rounded-md","bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10","background-animate",t)||""),children:[(0,o.jsx)("div",{className:"jsx-1e687dde282a8b11 shimmer-wrapper",children:(0,o.jsx)("div",{className:"jsx-1e687dde282a8b11 shimmer"})}),(0,o.jsx)(r(),{id:"1e687dde282a8b11",children:".background-animate.jsx-1e687dde282a8b11{-webkit-background-size:200%200%;-moz-background-size:200%200%;-o-background-size:200%200%;background-size:200%200%;-webkit-animation:gradientAnimation 1s ease infinite;-moz-animation:gradientAnimation 1s ease infinite;-o-animation:gradientAnimation 1s ease infinite;animation:gradientAnimation 1s ease infinite}@-webkit-keyframes gradientAnimation{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}@-moz-keyframes gradientAnimation{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}@-o-keyframes gradientAnimation{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}@keyframes gradientAnimation{0%{background-position:0%50%}50%{background-position:100%50%}100%{background-position:0%50%}}.shimmer-wrapper.jsx-1e687dde282a8b11{position:absolute;top:0;left:0;width:100%;height:100%;overflow:hidden}.shimmer.jsx-1e687dde282a8b11{width:50%;height:100%;background:-webkit-linear-gradient(left,rgba(255,255,255,0)0%,rgba(255,255,255,.3)50%,rgba(255,255,255,0)100%);background:-moz-linear-gradient(left,rgba(255,255,255,0)0%,rgba(255,255,255,.3)50%,rgba(255,255,255,0)100%);background:-o-linear-gradient(left,rgba(255,255,255,0)0%,rgba(255,255,255,.3)50%,rgba(255,255,255,0)100%);background:linear-gradient(90deg,rgba(255,255,255,0)0%,rgba(255,255,255,.3)50%,rgba(255,255,255,0)100%);-webkit-animation:shimmerAnimation 1s infinite;-moz-animation:shimmerAnimation 1s infinite;-o-animation:shimmerAnimation 1s infinite;animation:shimmerAnimation 1s infinite;position:absolute;top:0;left:-150%}@-webkit-keyframes shimmerAnimation{to{left:150%}}@-moz-keyframes shimmerAnimation{to{left:150%}}@-o-keyframes shimmerAnimation{to{left:150%}}@keyframes shimmerAnimation{to{left:150%}}"})]})}n(12115)},88362:(e,t,n)=>{n.d(t,{np:()=>l});var o=n(26715),a=n(5041),r=n(64541),i=n(34986),s=n(43089),c=n(56671);function l(){let{session:e}=(0,r.A)(),t=(0,o.jE)();return(0,a.n)({mutationFn:async t=>{let{sandboxId:n,filePath:o}=t;if(!(null==e?void 0:e.access_token))throw Error("No access token available");let a=await fetch("".concat("http://localhost:8000/api","/sandboxes/").concat(n,"/files?path=").concat(encodeURIComponent(o)),{method:"DELETE",headers:{Authorization:"Bearer ".concat(e.access_token)}});if(!a.ok)throw Error(await a.text()||"Delete failed");return await a.json()},onSuccess:(e,n)=>{var o;let a=n.filePath.substring(0,n.filePath.lastIndexOf("/"));t.invalidateQueries({queryKey:i.Be.directory(n.sandboxId,a)}),t.invalidateQueries({queryKey:i.Be.directories()}),t.invalidateQueries({predicate:e=>{let t=e.queryKey;return t.length>=4&&"files"===t[0]&&"content"===t[1]&&t[2]===n.sandboxId&&t[3]===n.filePath}}),["text","blob","json"].forEach(e=>{let o=i.Be.content(n.sandboxId,n.filePath,e);t.removeQueries({queryKey:o})});let r=(o=n.filePath)?((o=o.trim()).startsWith("/")||(o="/"+o),(o=o.replace(/\/+/g,"/")).length>1&&o.endsWith("/")&&(o=o.slice(0,-1)),o):"/";["".concat(n.sandboxId,":").concat(r,":blob"),"".concat(n.sandboxId,":").concat(r,":text"),"".concat(n.sandboxId,":").concat(r,":json"),"".concat(n.sandboxId,":").concat(r),"".concat(n.sandboxId,":").concat(r.substring(1),":blob"),"".concat(n.sandboxId,":").concat(r.substring(1),":text"),"".concat(n.sandboxId,":").concat(r.substring(1),":json"),"".concat(n.sandboxId,":").concat(r.substring(1))].forEach(e=>{var t;let n=null==(t=s.pH.cache)?void 0:t.get(e);n&&("url"===n.type&&"string"==typeof n.content&&n.content.startsWith("blob:")&&(console.log("[FILE DELETE] Revoking blob URL for deleted file: ".concat(n.content)),URL.revokeObjectURL(n.content)),s.pH.delete(e))})},onError:e=>{let t=e instanceof Error?e.message:String(e);c.oR.error("Delete failed: ".concat(t))}})}}}]);