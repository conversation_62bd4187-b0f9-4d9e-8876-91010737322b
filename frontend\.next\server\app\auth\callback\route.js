(()=>{var e={};e.id=5015,e.ids=[5015],e.modules={2507:(e,r,t)=>{"use strict";t.d(r,{U:()=>o});var s=t(67218);t(79130);var a=t(45934),u=t(44999),i=t(17478);let o=async()=>{let e=await (0,u.cookies)(),r="";return r&&!r.startsWith("http")&&(r=`http://${r}`),(0,a.createServerClient)(r,"",{cookies:{getAll:()=>e.getAll(),setAll(r){try{r.forEach(({name:r,value:t,options:s})=>e.set({name:r,value:t,...s}))}catch(e){}}}})};(0,i.D)([o]),(0,s.A)(o,"7f8bed79c8654f95685745e61906af37f96a086236",null)},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},16745:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"7f8bed79c8654f95685745e61906af37f96a086236":()=>s.U});var s=t(2507)},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},34631:e=>{"use strict";e.exports=require("tls")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},59218:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>n});var a=t(96559),u=t(48088),i=t(37719),o=t(2507),c=t(32190);async function n(e){let r=new URL(e.url),t=r.searchParams.get("code"),s=r.searchParams.get("returnUrl"),a=r.origin;if(t){let e=await (0,o.U)();await e.auth.exchangeCodeForSession(t)}let u=s&&"null"!==s?s:"/dashboard";return c.NextResponse.redirect(`${a}${u.startsWith("/")?"":"/"}${u}`)}let p=new a.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/auth/callback/route",pathname:"/auth/callback",filename:"route",bundlePath:"app/auth/callback/route"},resolvedPagePath:"C:\\Users\\<USER>\\suna\\frontend\\src\\app\\auth\\callback\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:x}=p;function h(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,5193,4257,580],()=>t(59218));module.exports=s})();