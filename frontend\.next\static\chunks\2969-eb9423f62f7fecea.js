"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2969],{38990:function(e,r,t){var o=this&&this.__createBinding||(Object.create?function(e,r,t,o){void 0===o&&(o=t);var n=Object.getOwnPropertyDescriptor(r,t);(!n||("get"in n?!r.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return r[t]}}),Object.defineProperty(e,o,n)}:function(e,r,t,o){void 0===o&&(o=t),e[o]=r[t]}),n=this&&this.__setModuleDefault||(Object.create?function(e,r){Object.defineProperty(e,"default",{enumerable:!0,value:r})}:function(e,r){e.default=r}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var r={};if(null!=e)for(var t in e)"default"!==t&&Object.prototype.hasOwnProperty.call(e,t)&&o(r,e,t);return n(r,e),r};Object.defineProperty(r,"__esModule",{value:!0}),r.parse=function(e){return 35===e.charCodeAt(0)?c(e):u(e)},r.parseHex=c,r.parseColor=u;let l=t(74772),s=a(t(94210)),i=(()=>{let e="[\\s,\\/]",r="([^\\s,\\/]+)",t=`(?:${e}+${r})`;return new RegExp(`(\\w+)\\(
      ${e}*
      ${r}
      ${t}
      ${t}
      ${t}?
      ${t}?
      ${e}*
    \\)`.replace(/\s/g,""))})();function c(e){let r=0,t=0,o=0,n=255;switch(e.length){case 4:r=(d(e.charCodeAt(1))<<4)+d(e.charCodeAt(1)),t=(d(e.charCodeAt(2))<<4)+d(e.charCodeAt(2)),o=(d(e.charCodeAt(3))<<4)+d(e.charCodeAt(3));break;case 7:r=(d(e.charCodeAt(1))<<4)+d(e.charCodeAt(2)),t=(d(e.charCodeAt(3))<<4)+d(e.charCodeAt(4)),o=(d(e.charCodeAt(5))<<4)+d(e.charCodeAt(6));break;case 9:r=(d(e.charCodeAt(1))<<4)+d(e.charCodeAt(2)),t=(d(e.charCodeAt(3))<<4)+d(e.charCodeAt(4)),o=(d(e.charCodeAt(5))<<4)+d(e.charCodeAt(6)),n=(d(e.charCodeAt(7))<<4)+d(e.charCodeAt(8))}return(0,l.newColor)(r,t,o,n)}function d(e){return(15&e)+9*(e>>6)}function u(e){let r=i.exec(e);if(null===r)throw Error(`Color.parse(): invalid CSS color: "${e}"`);let t=r[1],o=r[2],n=r[3],a=r[4],c=r[5],d=r[6];switch(t){case"rgb":case"rgba":{let e=f(o),r=f(n),t=f(a),s=c?p(c):255;return(0,l.newColor)(e,r,t,s)}case"hsl":case"hsla":{let e,r,t,s=m(o),i=b(n),d=b(a),u=c?p(c):255;if(0===i)e=r=t=Math.round(255*d);else{let o=d<.5?d*(1+i):d+i-d*i,n=2*d-o;e=Math.round(255*y(n,o,s+1/3)),r=Math.round(255*y(n,o,s)),t=Math.round(255*y(n,o,s-1/3))}return(0,l.newColor)(e,r,t,u)}case"hwb":{let e=m(o),r=b(n),t=b(a),s=c?p(c):255,i=0,d=Math.round(255*y(0,1,e+1/3)),u=Math.round(255*y(i,1,e)),f=Math.round(255*y(i,1,e-1/3));return d=w(d,r,t),u=w(u,r,t),f=w(f,r,t),(0,l.newColor)(d,u,f,s)}case"lab":{let e=g(o,100),r=g(n,125),t=g(a,125);return k(c?p(c):255,s.xyzd50ToSrgb(...s.labToXyzd50(e,r,t)))}case"lch":{let e=g(o,100),r=g(n,150),t=360*m(a);return k(c?p(c):255,s.xyzd50ToSrgb(...s.labToXyzd50(...s.lchToLab(e,r,t))))}case"oklab":{let e=g(o,1),r=g(n,.4),t=g(a,.4);return k(c?p(c):255,s.xyzd50ToSrgb(...s.xyzd65ToD50(...s.oklabToXyzd65(e,r,t))))}case"oklch":{let e=h(o),r=h(n),t=h(a);return k(c?p(c):255,s.xyzd50ToSrgb(...s.oklchToXyzd50(e,r,t)))}case"color":{let e=h(n),r=h(a),t=h(c),l=d?p(d):255;switch(o){case"srgb":return k(l,[e,r,t]);case"srgb-linear":return k(l,s.xyzd50ToSrgb(...s.srgbLinearToXyzd50(e,r,t)));case"display-p3":return k(l,s.xyzd50ToSrgb(...s.displayP3ToXyzd50(e,r,t)));case"a98-rgb":return k(l,s.xyzd50ToSrgb(...s.adobeRGBToXyzd50(e,r,t)));case"prophoto-rgb":return k(l,s.xyzd50ToSrgb(...s.proPhotoToXyzd50(e,r,t)));case"rec2020":return k(l,s.xyzd50ToSrgb(...s.rec2020ToXyzd50(e,r,t)));case"xyz":case"xyz-d65":return k(l,s.xyzd50ToSrgb(...s.xyzd65ToD50(e,r,t)));case"xyz-d50":return k(l,s.xyzd50ToSrgb(e,r,t))}}}throw Error(`Color.parse(): invalid CSS color: "${e}"`)}function f(e){return 37===e.charCodeAt(e.length-1)?Math.round(parseFloat(e)/100*255):Math.round(parseFloat(e))}function p(e){var r;return Math.round(255*(110===(r=e).charCodeAt(0)?0:37===r.charCodeAt(r.length-1)?parseFloat(r)/100:parseFloat(r)))}function m(e){let r=1;switch(e.charCodeAt(e.length-1)){case 101:return 0;case 100:r=103===e.charCodeAt(Math.max(0,e.length-4))?400:2*Math.PI;break;case 110:r=1;break;default:r=360}return parseFloat(e)/r}function b(e){return 110===e.charCodeAt(0)?0:parseFloat(e)/100}function h(e){return 110===e.charCodeAt(0)?0:37===e.charCodeAt(e.length-1)?parseFloat(e)/100:parseFloat(e)}function g(e,r){return 110===e.charCodeAt(0)?0:37===e.charCodeAt(e.length-1)?parseFloat(e)/100*r:parseFloat(e)}function y(e,r,t){return(t<0&&(t+=1),t>1&&(t-=1),t<1/6)?e+(r-e)*6*t:t<.5?r:t<2/3?e+(r-e)*(2/3-t)*6:e}function w(e,r,t){let o=e/255;return o*=1-r-t,Math.round(255*(o+=r))}function x(e){return Math.max(0,Math.min(255,e))}function k(e,r){let t=x(Math.round(255*r[0])),o=x(Math.round(255*r[1])),n=x(Math.round(255*r[2]));return(0,l.newColor)(t,o,n,e)}},39688:(e,r,t)=>{t.d(r,{QP:()=>ec});let o=e=>{let r=s(e),{conflictingClassGroups:t,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let t=e.split("-");return""===t[0]&&1!==t.length&&t.shift(),n(t,r)||l(e)},getConflictingClassGroupIds:(e,r)=>{let n=t[e]||[];return r&&o[e]?[...n,...o[e]]:n}}},n=(e,r)=>{if(0===e.length)return r.classGroupId;let t=e[0],o=r.nextPart.get(t),a=o?n(e.slice(1),o):void 0;if(a)return a;if(0===r.validators.length)return;let l=e.join("-");return r.validators.find(({validator:e})=>e(l))?.classGroupId},a=/^\[(.+)\]$/,l=e=>{if(a.test(e)){let r=a.exec(e)[1],t=r?.substring(0,r.indexOf(":"));if(t)return"arbitrary.."+t}},s=e=>{let{theme:r,classGroups:t}=e,o={nextPart:new Map,validators:[]};for(let e in t)i(t[e],o,e,r);return o},i=(e,r,t,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?r:c(r,e)).classGroupId=t;return}if("function"==typeof e)return d(e)?void i(e(o),r,t,o):void r.validators.push({validator:e,classGroupId:t});Object.entries(e).forEach(([e,n])=>{i(n,c(r,e),t,o)})})},c=(e,r)=>{let t=e;return r.split("-").forEach(e=>{t.nextPart.has(e)||t.nextPart.set(e,{nextPart:new Map,validators:[]}),t=t.nextPart.get(e)}),t},d=e=>e.isThemeGetter,u=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let r=0,t=new Map,o=new Map,n=(n,a)=>{t.set(n,a),++r>e&&(r=0,o=t,t=new Map)};return{get(e){let r=t.get(e);return void 0!==r?r:void 0!==(r=o.get(e))?(n(e,r),r):void 0},set(e,r){t.has(e)?t.set(e,r):n(e,r)}}},f=e=>{let{prefix:r,experimentalParseClassName:t}=e,o=e=>{let r,t=[],o=0,n=0,a=0;for(let l=0;l<e.length;l++){let s=e[l];if(0===o&&0===n){if(":"===s){t.push(e.slice(a,l)),a=l+1;continue}if("/"===s){r=l;continue}}"["===s?o++:"]"===s?o--:"("===s?n++:")"===s&&n--}let l=0===t.length?e:e.substring(a),s=p(l);return{modifiers:t,hasImportantModifier:s!==l,baseClassName:s,maybePostfixModifierPosition:r&&r>a?r-a:void 0}};if(r){let e=r+":",t=o;o=r=>r.startsWith(e)?t(r.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:r,maybePostfixModifierPosition:void 0}}if(t){let e=o;o=r=>t({className:r,parseClassName:e})}return o},p=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,m=e=>{let r=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let t=[],o=[];return e.forEach(e=>{"["===e[0]||r[e]?(t.push(...o.sort(),e),o=[]):o.push(e)}),t.push(...o.sort()),t}},b=e=>({cache:u(e.cacheSize),parseClassName:f(e),sortModifiers:m(e),...o(e)}),h=/\s+/,g=(e,r)=>{let{parseClassName:t,getClassGroupId:o,getConflictingClassGroupIds:n,sortModifiers:a}=r,l=[],s=e.trim().split(h),i="";for(let e=s.length-1;e>=0;e-=1){let r=s[e],{isExternal:c,modifiers:d,hasImportantModifier:u,baseClassName:f,maybePostfixModifierPosition:p}=t(r);if(c){i=r+(i.length>0?" "+i:i);continue}let m=!!p,b=o(m?f.substring(0,p):f);if(!b){if(!m||!(b=o(f))){i=r+(i.length>0?" "+i:i);continue}m=!1}let h=a(d).join(":"),g=u?h+"!":h,y=g+b;if(l.includes(y))continue;l.push(y);let w=n(b,m);for(let e=0;e<w.length;++e){let r=w[e];l.push(g+r)}i=r+(i.length>0?" "+i:i)}return i};function y(){let e,r,t=0,o="";for(;t<arguments.length;)(e=arguments[t++])&&(r=w(e))&&(o&&(o+=" "),o+=r);return o}let w=e=>{let r;if("string"==typeof e)return e;let t="";for(let o=0;o<e.length;o++)e[o]&&(r=w(e[o]))&&(t&&(t+=" "),t+=r);return t},x=e=>{let r=r=>r[e]||[];return r.isThemeGetter=!0,r},k=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,v=/^\((?:(\w[\w-]*):)?(.+)\)$/i,_=/^\d+\/\d+$/,z=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,O=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,E=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,S=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,T=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,M=e=>_.test(e),F=e=>!!e&&!Number.isNaN(Number(e)),P=e=>!!e&&Number.isInteger(Number(e)),R=e=>e.endsWith("%")&&F(e.slice(0,-1)),j=e=>z.test(e),C=()=>!0,A=e=>O.test(e)&&!E.test(e),G=()=>!1,B=e=>S.test(e),$=e=>T.test(e),N=e=>!X(e)&&!q(e),I=e=>ee(e,en,G),X=e=>k.test(e),D=e=>ee(e,ea,A),V=e=>ee(e,el,F),L=e=>ee(e,et,G),H=e=>ee(e,eo,$),W=e=>ee(e,ei,B),q=e=>v.test(e),Q=e=>er(e,ea),J=e=>er(e,es),K=e=>er(e,et),U=e=>er(e,en),Y=e=>er(e,eo),Z=e=>er(e,ei,!0),ee=(e,r,t)=>{let o=k.exec(e);return!!o&&(o[1]?r(o[1]):t(o[2]))},er=(e,r,t=!1)=>{let o=v.exec(e);return!!o&&(o[1]?r(o[1]):t)},et=e=>"position"===e||"percentage"===e,eo=e=>"image"===e||"url"===e,en=e=>"length"===e||"size"===e||"bg-size"===e,ea=e=>"length"===e,el=e=>"number"===e,es=e=>"family-name"===e,ei=e=>"shadow"===e;Symbol.toStringTag;let ec=function(e,...r){let t,o,n,a=function(s){return o=(t=b(r.reduce((e,r)=>r(e),e()))).cache.get,n=t.cache.set,a=l,l(s)};function l(e){let r=o(e);if(r)return r;let a=g(e,t);return n(e,a),a}return function(){return a(y.apply(null,arguments))}}(()=>{let e=x("color"),r=x("font"),t=x("text"),o=x("font-weight"),n=x("tracking"),a=x("leading"),l=x("breakpoint"),s=x("container"),i=x("spacing"),c=x("radius"),d=x("shadow"),u=x("inset-shadow"),f=x("text-shadow"),p=x("drop-shadow"),m=x("blur"),b=x("perspective"),h=x("aspect"),g=x("ease"),y=x("animate"),w=()=>["auto","avoid","all","avoid-page","page","left","right","column"],k=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],v=()=>[...k(),q,X],_=()=>["auto","hidden","clip","visible","scroll"],z=()=>["auto","contain","none"],O=()=>[q,X,i],E=()=>[M,"full","auto",...O()],S=()=>[P,"none","subgrid",q,X],T=()=>["auto",{span:["full",P,q,X]},P,q,X],A=()=>[P,"auto",q,X],G=()=>["auto","min","max","fr",q,X],B=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],$=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...O()],er=()=>[M,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...O()],et=()=>[e,q,X],eo=()=>[...k(),K,L,{position:[q,X]}],en=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ea=()=>["auto","cover","contain",U,I,{size:[q,X]}],el=()=>[R,Q,D],es=()=>["","none","full",c,q,X],ei=()=>["",F,Q,D],ec=()=>["solid","dashed","dotted","double"],ed=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eu=()=>[F,R,K,L],ef=()=>["","none",m,q,X],ep=()=>["none",F,q,X],em=()=>["none",F,q,X],eb=()=>[F,q,X],eh=()=>[M,"full",...O()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[j],breakpoint:[j],color:[C],container:[j],"drop-shadow":[j],ease:["in","out","in-out"],font:[N],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[j],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[j],shadow:[j],spacing:["px",F],text:[j],"text-shadow":[j],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",M,X,q,h]}],container:["container"],columns:[{columns:[F,X,q,s]}],"break-after":[{"break-after":w()}],"break-before":[{"break-before":w()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:v()}],overflow:[{overflow:_()}],"overflow-x":[{"overflow-x":_()}],"overflow-y":[{"overflow-y":_()}],overscroll:[{overscroll:z()}],"overscroll-x":[{"overscroll-x":z()}],"overscroll-y":[{"overscroll-y":z()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:E()}],"inset-x":[{"inset-x":E()}],"inset-y":[{"inset-y":E()}],start:[{start:E()}],end:[{end:E()}],top:[{top:E()}],right:[{right:E()}],bottom:[{bottom:E()}],left:[{left:E()}],visibility:["visible","invisible","collapse"],z:[{z:[P,"auto",q,X]}],basis:[{basis:[M,"full","auto",s,...O()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[F,M,"auto","initial","none",X]}],grow:[{grow:["",F,q,X]}],shrink:[{shrink:["",F,q,X]}],order:[{order:[P,"first","last","none",q,X]}],"grid-cols":[{"grid-cols":S()}],"col-start-end":[{col:T()}],"col-start":[{"col-start":A()}],"col-end":[{"col-end":A()}],"grid-rows":[{"grid-rows":S()}],"row-start-end":[{row:T()}],"row-start":[{"row-start":A()}],"row-end":[{"row-end":A()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":G()}],"auto-rows":[{"auto-rows":G()}],gap:[{gap:O()}],"gap-x":[{"gap-x":O()}],"gap-y":[{"gap-y":O()}],"justify-content":[{justify:[...B(),"normal"]}],"justify-items":[{"justify-items":[...$(),"normal"]}],"justify-self":[{"justify-self":["auto",...$()]}],"align-content":[{content:["normal",...B()]}],"align-items":[{items:[...$(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...$(),{baseline:["","last"]}]}],"place-content":[{"place-content":B()}],"place-items":[{"place-items":[...$(),"baseline"]}],"place-self":[{"place-self":["auto",...$()]}],p:[{p:O()}],px:[{px:O()}],py:[{py:O()}],ps:[{ps:O()}],pe:[{pe:O()}],pt:[{pt:O()}],pr:[{pr:O()}],pb:[{pb:O()}],pl:[{pl:O()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":O()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":O()}],"space-y-reverse":["space-y-reverse"],size:[{size:er()}],w:[{w:[s,"screen",...er()]}],"min-w":[{"min-w":[s,"screen","none",...er()]}],"max-w":[{"max-w":[s,"screen","none","prose",{screen:[l]},...er()]}],h:[{h:["screen","lh",...er()]}],"min-h":[{"min-h":["screen","lh","none",...er()]}],"max-h":[{"max-h":["screen","lh",...er()]}],"font-size":[{text:["base",t,Q,D]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,q,V]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",R,X]}],"font-family":[{font:[J,X,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,q,X]}],"line-clamp":[{"line-clamp":[F,"none",q,V]}],leading:[{leading:[a,...O()]}],"list-image":[{"list-image":["none",q,X]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",q,X]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:et()}],"text-color":[{text:et()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ec(),"wavy"]}],"text-decoration-thickness":[{decoration:[F,"from-font","auto",q,D]}],"text-decoration-color":[{decoration:et()}],"underline-offset":[{"underline-offset":[F,"auto",q,X]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:O()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",q,X]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",q,X]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:eo()}],"bg-repeat":[{bg:en()}],"bg-size":[{bg:ea()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},P,q,X],radial:["",q,X],conic:[P,q,X]},Y,H]}],"bg-color":[{bg:et()}],"gradient-from-pos":[{from:el()}],"gradient-via-pos":[{via:el()}],"gradient-to-pos":[{to:el()}],"gradient-from":[{from:et()}],"gradient-via":[{via:et()}],"gradient-to":[{to:et()}],rounded:[{rounded:es()}],"rounded-s":[{"rounded-s":es()}],"rounded-e":[{"rounded-e":es()}],"rounded-t":[{"rounded-t":es()}],"rounded-r":[{"rounded-r":es()}],"rounded-b":[{"rounded-b":es()}],"rounded-l":[{"rounded-l":es()}],"rounded-ss":[{"rounded-ss":es()}],"rounded-se":[{"rounded-se":es()}],"rounded-ee":[{"rounded-ee":es()}],"rounded-es":[{"rounded-es":es()}],"rounded-tl":[{"rounded-tl":es()}],"rounded-tr":[{"rounded-tr":es()}],"rounded-br":[{"rounded-br":es()}],"rounded-bl":[{"rounded-bl":es()}],"border-w":[{border:ei()}],"border-w-x":[{"border-x":ei()}],"border-w-y":[{"border-y":ei()}],"border-w-s":[{"border-s":ei()}],"border-w-e":[{"border-e":ei()}],"border-w-t":[{"border-t":ei()}],"border-w-r":[{"border-r":ei()}],"border-w-b":[{"border-b":ei()}],"border-w-l":[{"border-l":ei()}],"divide-x":[{"divide-x":ei()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ei()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ec(),"hidden","none"]}],"divide-style":[{divide:[...ec(),"hidden","none"]}],"border-color":[{border:et()}],"border-color-x":[{"border-x":et()}],"border-color-y":[{"border-y":et()}],"border-color-s":[{"border-s":et()}],"border-color-e":[{"border-e":et()}],"border-color-t":[{"border-t":et()}],"border-color-r":[{"border-r":et()}],"border-color-b":[{"border-b":et()}],"border-color-l":[{"border-l":et()}],"divide-color":[{divide:et()}],"outline-style":[{outline:[...ec(),"none","hidden"]}],"outline-offset":[{"outline-offset":[F,q,X]}],"outline-w":[{outline:["",F,Q,D]}],"outline-color":[{outline:et()}],shadow:[{shadow:["","none",d,Z,W]}],"shadow-color":[{shadow:et()}],"inset-shadow":[{"inset-shadow":["none",u,Z,W]}],"inset-shadow-color":[{"inset-shadow":et()}],"ring-w":[{ring:ei()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:et()}],"ring-offset-w":[{"ring-offset":[F,D]}],"ring-offset-color":[{"ring-offset":et()}],"inset-ring-w":[{"inset-ring":ei()}],"inset-ring-color":[{"inset-ring":et()}],"text-shadow":[{"text-shadow":["none",f,Z,W]}],"text-shadow-color":[{"text-shadow":et()}],opacity:[{opacity:[F,q,X]}],"mix-blend":[{"mix-blend":[...ed(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ed()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[F]}],"mask-image-linear-from-pos":[{"mask-linear-from":eu()}],"mask-image-linear-to-pos":[{"mask-linear-to":eu()}],"mask-image-linear-from-color":[{"mask-linear-from":et()}],"mask-image-linear-to-color":[{"mask-linear-to":et()}],"mask-image-t-from-pos":[{"mask-t-from":eu()}],"mask-image-t-to-pos":[{"mask-t-to":eu()}],"mask-image-t-from-color":[{"mask-t-from":et()}],"mask-image-t-to-color":[{"mask-t-to":et()}],"mask-image-r-from-pos":[{"mask-r-from":eu()}],"mask-image-r-to-pos":[{"mask-r-to":eu()}],"mask-image-r-from-color":[{"mask-r-from":et()}],"mask-image-r-to-color":[{"mask-r-to":et()}],"mask-image-b-from-pos":[{"mask-b-from":eu()}],"mask-image-b-to-pos":[{"mask-b-to":eu()}],"mask-image-b-from-color":[{"mask-b-from":et()}],"mask-image-b-to-color":[{"mask-b-to":et()}],"mask-image-l-from-pos":[{"mask-l-from":eu()}],"mask-image-l-to-pos":[{"mask-l-to":eu()}],"mask-image-l-from-color":[{"mask-l-from":et()}],"mask-image-l-to-color":[{"mask-l-to":et()}],"mask-image-x-from-pos":[{"mask-x-from":eu()}],"mask-image-x-to-pos":[{"mask-x-to":eu()}],"mask-image-x-from-color":[{"mask-x-from":et()}],"mask-image-x-to-color":[{"mask-x-to":et()}],"mask-image-y-from-pos":[{"mask-y-from":eu()}],"mask-image-y-to-pos":[{"mask-y-to":eu()}],"mask-image-y-from-color":[{"mask-y-from":et()}],"mask-image-y-to-color":[{"mask-y-to":et()}],"mask-image-radial":[{"mask-radial":[q,X]}],"mask-image-radial-from-pos":[{"mask-radial-from":eu()}],"mask-image-radial-to-pos":[{"mask-radial-to":eu()}],"mask-image-radial-from-color":[{"mask-radial-from":et()}],"mask-image-radial-to-color":[{"mask-radial-to":et()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":k()}],"mask-image-conic-pos":[{"mask-conic":[F]}],"mask-image-conic-from-pos":[{"mask-conic-from":eu()}],"mask-image-conic-to-pos":[{"mask-conic-to":eu()}],"mask-image-conic-from-color":[{"mask-conic-from":et()}],"mask-image-conic-to-color":[{"mask-conic-to":et()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:eo()}],"mask-repeat":[{mask:en()}],"mask-size":[{mask:ea()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",q,X]}],filter:[{filter:["","none",q,X]}],blur:[{blur:ef()}],brightness:[{brightness:[F,q,X]}],contrast:[{contrast:[F,q,X]}],"drop-shadow":[{"drop-shadow":["","none",p,Z,W]}],"drop-shadow-color":[{"drop-shadow":et()}],grayscale:[{grayscale:["",F,q,X]}],"hue-rotate":[{"hue-rotate":[F,q,X]}],invert:[{invert:["",F,q,X]}],saturate:[{saturate:[F,q,X]}],sepia:[{sepia:["",F,q,X]}],"backdrop-filter":[{"backdrop-filter":["","none",q,X]}],"backdrop-blur":[{"backdrop-blur":ef()}],"backdrop-brightness":[{"backdrop-brightness":[F,q,X]}],"backdrop-contrast":[{"backdrop-contrast":[F,q,X]}],"backdrop-grayscale":[{"backdrop-grayscale":["",F,q,X]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[F,q,X]}],"backdrop-invert":[{"backdrop-invert":["",F,q,X]}],"backdrop-opacity":[{"backdrop-opacity":[F,q,X]}],"backdrop-saturate":[{"backdrop-saturate":[F,q,X]}],"backdrop-sepia":[{"backdrop-sepia":["",F,q,X]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":O()}],"border-spacing-x":[{"border-spacing-x":O()}],"border-spacing-y":[{"border-spacing-y":O()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",q,X]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[F,"initial",q,X]}],ease:[{ease:["linear","initial",g,q,X]}],delay:[{delay:[F,q,X]}],animate:[{animate:["none",y,q,X]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[b,q,X]}],"perspective-origin":[{"perspective-origin":v()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:em()}],"scale-x":[{"scale-x":em()}],"scale-y":[{"scale-y":em()}],"scale-z":[{"scale-z":em()}],"scale-3d":["scale-3d"],skew:[{skew:eb()}],"skew-x":[{"skew-x":eb()}],"skew-y":[{"skew-y":eb()}],transform:[{transform:[q,X,"","none","gpu","cpu"]}],"transform-origin":[{origin:v()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eh()}],"translate-x":[{"translate-x":eh()}],"translate-y":[{"translate-y":eh()}],"translate-z":[{"translate-z":eh()}],"translate-none":["translate-none"],accent:[{accent:et()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:et()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",q,X]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":O()}],"scroll-mx":[{"scroll-mx":O()}],"scroll-my":[{"scroll-my":O()}],"scroll-ms":[{"scroll-ms":O()}],"scroll-me":[{"scroll-me":O()}],"scroll-mt":[{"scroll-mt":O()}],"scroll-mr":[{"scroll-mr":O()}],"scroll-mb":[{"scroll-mb":O()}],"scroll-ml":[{"scroll-ml":O()}],"scroll-p":[{"scroll-p":O()}],"scroll-px":[{"scroll-px":O()}],"scroll-py":[{"scroll-py":O()}],"scroll-ps":[{"scroll-ps":O()}],"scroll-pe":[{"scroll-pe":O()}],"scroll-pt":[{"scroll-pt":O()}],"scroll-pr":[{"scroll-pr":O()}],"scroll-pb":[{"scroll-pb":O()}],"scroll-pl":[{"scroll-pl":O()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",q,X]}],fill:[{fill:["none",...et()]}],"stroke-w":[{stroke:[F,Q,D,V]}],stroke:[{stroke:["none",...et()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},52596:(e,r,t)=>{function o(){for(var e,r,t=0,o="",n=arguments.length;t<n;t++)(e=arguments[t])&&(r=function e(r){var t,o,n="";if("string"==typeof r||"number"==typeof r)n+=r;else if("object"==typeof r)if(Array.isArray(r)){var a=r.length;for(t=0;t<a;t++)r[t]&&(o=e(r[t]))&&(n&&(n+=" "),n+=o)}else for(o in r)r[o]&&(n&&(n+=" "),n+=o);return n}(e))&&(o&&(o+=" "),o+=r);return o}t.d(r,{$:()=>o,A:()=>n});let n=o},53584:function(e,r,t){var o=this&&this.__createBinding||(Object.create?function(e,r,t,o){void 0===o&&(o=t);var n=Object.getOwnPropertyDescriptor(r,t);(!n||("get"in n?!r.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return r[t]}}),Object.defineProperty(e,o,n)}:function(e,r,t,o){void 0===o&&(o=t),e[o]=r[t]}),n=this&&this.__setModuleDefault||(Object.create?function(e,r){Object.defineProperty(e,"default",{enumerable:!0,value:r})}:function(e,r){e.default=r}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var r={};if(null!=e)for(var t in e)"default"!==t&&Object.prototype.hasOwnProperty.call(e,t)&&o(r,e,t);return n(r,e),r};Object.defineProperty(r,"__esModule",{value:!0}),r.format=void 0,r.formatHEXA=f,r.formatHEX=function(e){return"#"+u[l(e)]+u[s(e)]+u[i(e)]},r.formatRGBA=function(e){return`rgba(${l(e)} ${s(e)} ${i(e)} / ${c(e)/255})`},r.toRGBA=function(e){return{r:l(e),g:s(e),b:i(e),a:c(e)}},r.formatHSLA=function(e){p(l(e),s(e),i(e));let r=d[0],t=d[1],o=d[2],n=c(e)/255;return`hsla(${r} ${t}% ${o}% / ${n})`},r.toHSLA=function(e){p(l(e),s(e),i(e));let r=d[0],t=d[1];return{h:r,s:t,l:d[2],a:c(e)/255}},r.formatHWBA=function(e){m(l(e),s(e),i(e));let r=d[0],t=d[1],o=d[2],n=c(e)/255;return`hsla(${r} ${t}% ${o}% / ${n})`},r.toHWBA=function(e){m(l(e),s(e),i(e));let r=d[0],t=d[1];return{h:r,w:t,b:d[2],a:c(e)/255}};let{getRed:l,getGreen:s,getBlue:i,getAlpha:c}=a(t(74772)),d=[0,0,0],u=Array.from({length:256}).map((e,r)=>r.toString(16).padStart(2,"0"));function f(e){return"#"+u[l(e)]+u[s(e)]+u[i(e)]+u[c(e)]}function p(e,r,t){let o=Math.max(e/=255,r/=255,t/=255),n=o-Math.min(e,r,t),a=n?o===e?(r-t)/n:o===r?2+(t-e)/n:4+(e-r)/n:0;d[0]=60*a<0?60*a+360:60*a,d[1]=100*(n?o<=.5?n/(2*o-n):n/(2-(2*o-n)):0),d[2]=100*(2*o-n)/2}function m(e,r,t){let o=Math.min(e/=255,r/=255,t/=255),n=Math.max(e,r,t),a=1-n;if(n===o){d[0]=0,d[1]=o,d[2]=a;return}let l=e===o?r-t:r===o?t-e:e-r,s=e===o?3:r===o?5:1;d[0]=(s-l/(n-o))/6,d[1]=o,d[2]=a}r.format=f},61084:function(e,r,t){var o=this&&this.__createBinding||(Object.create?function(e,r,t,o){void 0===o&&(o=t);var n=Object.getOwnPropertyDescriptor(r,t);(!n||("get"in n?!r.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return r[t]}}),Object.defineProperty(e,o,n)}:function(e,r,t,o){void 0===o&&(o=t),e[o]=r[t]}),n=this&&this.__setModuleDefault||(Object.create?function(e,r){Object.defineProperty(e,"default",{enumerable:!0,value:r})}:function(e,r){e.default=r}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var r={};if(null!=e)for(var t in e)"default"!==t&&Object.prototype.hasOwnProperty.call(e,t)&&o(r,e,t);return n(r,e),r};Object.defineProperty(r,"__esModule",{value:!0}),r.alpha=function(e,r){return d(e,Math.round(255*r))},r.darken=function(e,r){let t=l(e),o=s(e),n=i(e),a=c(e),d=1-r;return u(t*d,o*d,n*d,a)},r.lighten=function(e,r){let t=l(e),o=s(e),n=i(e);return u(t+(255-t)*r,o+(255-o)*r,n+(255-n)*r,c(e))},r.blend=function(e,r,t,o=1){let n=(e,r)=>Math.round((e**(1/o)*(1-t)+r**(1/o)*t)**o),a=n(l(e),l(r));return u(a,n(s(e),s(r)),n(i(e),i(r)),255)},r.getLuminance=function(e){let r=l(e)/255,t=s(e)/255,o=i(e)/255,n=e=>e<=.03928?e/12.92:((e+.055)/1.055)**2.4,a=n(r);return Math.round((.2126*a+.7152*n(t)+.0722*n(o))*1e3)/1e3};let{getRed:l,getGreen:s,getBlue:i,getAlpha:c,setAlpha:d,newColor:u}=a(t(74772))},67626:(e,r)=>{Object.defineProperty(r,"__esModule",{value:!0}),r.cast=function(e){return e<0?e+0x100000000:e},r.get=function(e,r){return e>>r&255},r.set=function(e,r,t){return e^(e^t<<r)&255<<r}},74772:function(e,r,t){var o=this&&this.__createBinding||(Object.create?function(e,r,t,o){void 0===o&&(o=t);var n=Object.getOwnPropertyDescriptor(r,t);(!n||("get"in n?!r.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return r[t]}}),Object.defineProperty(e,o,n)}:function(e,r,t,o){void 0===o&&(o=t),e[o]=r[t]}),n=this&&this.__setModuleDefault||(Object.create?function(e,r){Object.defineProperty(e,"default",{enumerable:!0,value:r})}:function(e,r){e.default=r}),a=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var r={};if(null!=e)for(var t in e)"default"!==t&&Object.prototype.hasOwnProperty.call(e,t)&&o(r,e,t);return n(r,e),r};Object.defineProperty(r,"__esModule",{value:!0}),r.OFFSET_A=r.OFFSET_B=r.OFFSET_G=r.OFFSET_R=void 0,r.newColor=c,r.from=function(e){return c(s(e,r.OFFSET_R),s(e,r.OFFSET_G),s(e,r.OFFSET_B),s(e,r.OFFSET_A))},r.toNumber=function(e){return l(e)},r.getRed=function(e){return s(e,r.OFFSET_R)},r.getGreen=function(e){return s(e,r.OFFSET_G)},r.getBlue=function(e){return s(e,r.OFFSET_B)},r.getAlpha=function(e){return s(e,r.OFFSET_A)},r.setRed=function(e,t){return i(e,r.OFFSET_R,t)},r.setGreen=function(e,t){return i(e,r.OFFSET_G,t)},r.setBlue=function(e,t){return i(e,r.OFFSET_B,t)},r.setAlpha=function(e,t){return i(e,r.OFFSET_A,t)};let{cast:l,get:s,set:i}=a(t(67626));function c(e,t,o,n){return(e<<r.OFFSET_R)+(t<<r.OFFSET_G)+(o<<r.OFFSET_B)+(n<<r.OFFSET_A)}r.OFFSET_R=24,r.OFFSET_G=16,r.OFFSET_B=8,r.OFFSET_A=0},81949:function(e,r,t){var o=this&&this.__createBinding||(Object.create?function(e,r,t,o){void 0===o&&(o=t);var n=Object.getOwnPropertyDescriptor(r,t);(!n||("get"in n?!r.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return r[t]}}),Object.defineProperty(e,o,n)}:function(e,r,t,o){void 0===o&&(o=t),e[o]=r[t]}),n=this&&this.__exportStar||function(e,r){for(var t in e)"default"===t||Object.prototype.hasOwnProperty.call(r,t)||o(r,e,t)};Object.defineProperty(r,"__esModule",{value:!0}),n(t(74772),r),n(t(38990),r),n(t(53584),r),n(t(61084),r)},94210:(e,r)=>{function t(e,r){let t=[0,0,0];for(let o=0;o<3;++o)t[o]=e[o][0]*r[0]+e[o][1]*r[1]+e[o][2]*r[2];return t}Object.defineProperty(r,"__esModule",{value:!0}),r.labToXyzd50=function(e,r,t){let o=(e+16)/116,n=o+r/500,a=o-t/200;function l(e){return e<=24/116?108/841*(e-16/116):e*e*e}return n=.9642*l(n),[n,o=+l(o),a=.8251*l(a)]},r.xyzd50ToLab=function(e,r,t){function o(e){return e<=24/116*(24/116)*(24/116)?841/108*e+16/116:Math.pow(e,1/3)}return e=o(e/.9642),r=o(r/1),[116*r-16,500*(e-r),200*(r-(t=o(t/.8251)))]},r.oklabToXyzd65=g,r.xyzd65ToOklab=y,r.lchToLab=w,r.labToLch=x,r.displayP3ToXyzd50=function(e,r,o){let[l,i,c]=s(n.sRGB,e,r,o);return t(a.displayP3,[l,i,c])},r.xyzd50ToDisplayP3=function(e,r,o){let l=t(a.displayP3_INVERSE,[e,r,o]);return s(n.sRGB_INVERSE,l[0],l[1],l[2])},r.proPhotoToXyzd50=function(e,r,o){let[a,l,i]=s(n.proPhotoRGB,e,r,o);return t(f,[a,l,i])},r.xyzd50ToProPhoto=function(e,r,o){let a=t(p,[e,r,o]);return s(n.proPhotoRGB_INVERSE,a[0],a[1],a[2])},r.adobeRGBToXyzd50=function(e,r,o){let[l,i,c]=s(n.k2Dot2,e,r,o);return t(a.adobeRGB,[l,i,c])},r.xyzd50ToAdobeRGB=function(e,r,o){let l=t(a.adobeRGB_INVERSE,[e,r,o]);return s(n.k2Dot2_INVERSE,l[0],l[1],l[2])},r.rec2020ToXyzd50=function(e,r,o){let[l,i,c]=s(n.rec2020,e,r,o);return t(a.rec2020,[l,i,c])},r.xyzd50ToRec2020=function(e,r,o){let l=t(a.rec2020_INVERSE,[e,r,o]);return s(n.rec2020_INVERSE,l[0],l[1],l[2])},r.xyzd50ToD65=k,r.xyzd65ToD50=v,r.xyzd65TosRGBLinear=function(e,r,o){return t(h,[e,r,o])},r.xyzd50TosRGBLinear=function(e,r,o){return t(a.sRGB_INVERSE,[e,r,o])},r.srgbLinearToXyzd50=function(e,r,o){return t(a.sRGB,[e,r,o])},r.srgbToXyzd50=function(e,r,o){let[l,i,c]=s(n.sRGB,e,r,o);return t(a.sRGB,[l,i,c])},r.xyzd50ToSrgb=function(e,r,o){let l=t(a.sRGB_INVERSE,[e,r,o]);return s(n.sRGB_INVERSE,l[0],l[1],l[2])},r.oklchToXyzd50=function(e,r,t){let[o,n,a]=w(e,r,t),[l,s,i]=g(o,n,a);return v(l,s,i)},r.xyzd50ToOklch=function(e,r,t){let[o,n,a]=k(e,r,t),[l,s,i]=y(o,n,a);return x(l,s,i)};class o{g;a;b;c;d;e;f;constructor(e,r,t=0,o=0,n=0,a=0,l=0){this.g=e,this.a=r,this.b=t,this.c=o,this.d=n,this.e=a,this.f=l}eval(e){let r=e<0?-1:1,t=e*r;return t<this.d?r*(this.c*t+this.f):r*(Math.pow(this.a*t+this.b,this.g)+this.e)}}let n={sRGB:new o(2.4,1/1.055,.055/1.055,1/12.92,.04045,0,0),sRGB_INVERSE:new o(.416667,1.13728,-0,12.92,.0031308,-.0549698,-0),proPhotoRGB:new o(1.8,1),proPhotoRGB_INVERSE:new o(.555556,1,-0,0,0,0,0),k2Dot2:new o(2.2,1),k2Dot2_INVERSE:new o(.454545,1),rec2020:new o(2.22222,.909672,.0903276,.222222,.0812429,0,0),rec2020_INVERSE:new o(.45,1.23439,-0,4.5,.018054,-.0993195,-0)},a={sRGB:[[.436065674,.385147095,.143066406],[.222488403,.716873169,.06060791],[.013916016,.097076416,.714096069]],sRGB_INVERSE:[[3.134112151374599,-1.6173924597114966,-.4906334036481285],[-.9787872938826594,1.9162795854799963,.0334547139520088],[.07198304248352326,-.2289858493321844,1.4053851325241447]],displayP3:[[.515102,.291965,.157153],[.241182,.692236,.0665819],[-.00104941,.0418818,.784378]],displayP3_INVERSE:[[2.404045155982687,-.9898986932663839,-.3976317191366333],[-.8422283799266768,1.7988505115115485,.016048170293157416],[.04818705979712955,-.09737385156228891,1.2735066448052303]],adobeRGB:[[.60974,.20528,.14919],[.31111,.62567,.06322],[.01947,.06087,.74457]],adobeRGB_INVERSE:[[1.9625385510109137,-.6106892546501431,-.3413827467482388],[-.9787580455521,1.9161624707082339,.03341676594241408],[.028696263137883395,-.1406807819331586,1.349252109991369]],rec2020:[[.673459,.165661,.1251],[.279033,.675338,.0456288],[-.00193139,.0299794,.797162]],rec2020_INVERSE:[[1.647275201661012,-.3936024771460771,-.23598028884792507],[-.6826176165196962,1.647617775014935,.01281626807852422],[.029662725298529837,-.06291668721366285,1.2533964313435522]]};function l(e){return Math.PI/180*e}function s(e,r,t,o){return[e.eval(r),e.eval(t),e.eval(o)]}let i=[[.9999999984505198,.39633779217376786,.2158037580607588],[1.0000000088817609,-.10556134232365635,-.06385417477170591],[1.0000000546724108,-.08948418209496575,-1.2914855378640917]],c=[[.2104542553,.7936177849999999,-.0040720468],[1.9779984951000003,-2.4285922049999997,.4505937099000001],[.025904037099999982,.7827717662,-.8086757660000001]],d=[[.8190224432164319,.3619062562801221,-.12887378261216414],[.0329836671980271,.9292868468965546,.03614466816999844],[.048177199566046255,.26423952494422764,.6335478258136937]],u=[[1.226879873374156,-.5578149965554814,.2813910501772159],[-.040575762624313734,1.1122868293970596,-.07171106666151703],[-.07637294974672144,-.4214933239627915,1.586924024427242]],f=[[.7976700747153241,.13519395152800417,.03135596341127167],[.28803902352472205,.7118744007923554,8661179538844252e-20],[2739876695467402e-22,-14405226518969991e-22,.825211112593861]],p=[[1.3459533710138858,-.25561367037652133,-.051116041522131374],[-.544600415668951,1.5081687311475767,.020535163968720935],[-13975622054109725e-22,2717590904589903e-21,1.2118111696814942]],m=[[1.0478573189120088,.022907374491829943,-.050162247377152525],[.029570500050499514,.9904755577034089,-.017061518194840468],[-.00924047197558879,.015052921526981566,.7519708530777581]],b=[[.9555366447632887,-.02306009252137888,.06321844147263304],[-.028315378228764922,1.009951351591575,.021026001591792402],[.012308773293784308,-.02050053471777469,1.3301947294775631]],h=[[3.2408089365140573,-1.5375788839307314,-.4985609572551541],[-.9692732213205414,1.876110235238969,.041560501141251774],[.05567030990267439,-.2040007921971802,1.0571046720577026]];function g(e,r,o){let n=t(i,[e,r,o]);return n[0]=n[0]*n[0]*n[0],n[1]=n[1]*n[1]*n[1],n[2]=n[2]*n[2]*n[2],t(u,n)}function y(e,r,o){let n=t(d,[e,r,o]);n[0]=Math.pow(n[0],1/3),n[1]=Math.pow(n[1],1/3),n[2]=Math.pow(n[2],1/3);let a=t(c,n);return[a[0],a[1],a[2]]}function w(e,r,t){return void 0===t?[e,0,0]:[e,r*Math.cos(l(t)),r*Math.sin(l(t))]}function x(e,r,t){return[e,Math.sqrt(r*r+t*t),180/Math.PI*Math.atan2(t,r)]}function k(e,r,o){return t(b,[e,r,o])}function v(e,r,o){return t(m,[e,r,o])}}}]);