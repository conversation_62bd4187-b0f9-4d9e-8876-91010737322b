(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7169],{25377:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>eN});var a=s(95155),n=s(12115),l=s(35695),r=s(25731),i=s(56671),o=s(82814),c=s(52695),d=s(53732),u=s(59434),m=s(40416),x=s(23843),h=s(67897),g=s(52300),f=s(58883),p=s(46768),v=s(48988),j=s(8168),b=s(79232),w=s(18068),N=s(99856),y=s(16879),k=s(1243);function C(e){let{error:t}=e;return(0,a.jsx)("div",{className:"flex flex-1 items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"flex w-full max-w-md flex-col items-center gap-4 rounded-lg border bg-card p-6 text-center",children:[(0,a.jsx)("div",{className:"rounded-full bg-destructive/10 p-3",children:(0,a.jsx)(k.A,{className:"h-6 w-6 text-destructive"})}),(0,a.jsx)("h2",{className:"text-lg font-semibold text-destructive",children:"Thread Not Found"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:t.includes("JSON object requested, multiple (or no) rows returned")?"This thread either does not exist or you do not have access to it.":t})]})})}var _=s(17951),S=s(49376),A=s(71539),E=s(14186),R=s(53311),I=s(54165),D=s(30285);function L(e){let{open:t,onOpenChange:s,onDismiss:n}=e,r=(0,l.useRouter)();return(0,a.jsx)(I.lG,{open:t,onOpenChange:s,children:(0,a.jsxs)(I.Cf,{onPointerDownOutside:e=>e.preventDefault(),className:"sm:max-w-md",children:[(0,a.jsxs)(I.c7,{children:[(0,a.jsxs)(I.L3,{className:"flex items-center",children:[(0,a.jsx)(_.A,{className:"h-5 w-5 mr-2 text-primary"}),"Unlock the Full Suna Experience"]}),(0,a.jsx)(I.rr,{children:"You're currently using Suna's free tier with limited capabilities. Upgrade now to access our most powerful AI model."})]}),(0,a.jsxs)("div",{className:"py-4",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-slate-700 dark:text-slate-300 mb-3",children:"Pro Benefits"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"rounded-full bg-secondary/10 p-2 flex-shrink-0 mt-0.5",children:(0,a.jsx)(S.A,{className:"h-4 w-4 text-secondary"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-slate-900 dark:text-slate-100",children:"Advanced AI Models"}),(0,a.jsx)("p",{className:"text-xs text-slate-500 dark:text-slate-400",children:"Get access to advanced models suited for complex tasks"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"rounded-full bg-secondary/10 p-2 flex-shrink-0 mt-0.5",children:(0,a.jsx)(A.A,{className:"h-4 w-4 text-secondary"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-slate-900 dark:text-slate-100",children:"Faster Responses"}),(0,a.jsx)("p",{className:"text-xs text-slate-500 dark:text-slate-400",children:"Get access to faster models that breeze through your tasks"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"rounded-full bg-secondary/10 p-2 flex-shrink-0 mt-0.5",children:(0,a.jsx)(E.A,{className:"h-4 w-4 text-secondary"})}),(0,a.jsxs)("div",{className:"ml-3",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-slate-900 dark:text-slate-100",children:"Higher Usage Limits"}),(0,a.jsx)("p",{className:"text-xs text-slate-500 dark:text-slate-400",children:"Enjoy more conversations and longer run durations"})]})]})]})]}),(0,a.jsxs)(I.Es,{className:"flex gap-2",children:[(0,a.jsx)(D.$,{variant:"outline",onClick:n,children:"Maybe Later"}),(0,a.jsxs)(D.$,{onClick:()=>{r.push("/settings/billing"),s(!1),localStorage.setItem("suna_upgrade_dialog_displayed","true")},children:[(0,a.jsx)(R.A,{className:"h-4 w-4"}),"Upgrade Now"]})]})]})})}var P=s(74783),T=s(5196),O=s(54416),F=s(87004),z=s(14395),M=s(99245),V=s(66516),G=s(46102),$=s(62523),U=s(27379),K=s(68856),B=s(45132),Z=s(26715),H=s(80861),q=s(85057),X=s(88539),J=s(59409),Y=s(26126),Q=s(34869),W=s(85339),ee=s(47924),et=s(84616),es=s(57434),ea=s(78749),en=s(44020),el=s(56287),er=s(92657),ei=s(62525),eo=s(51154),ec=s(44838),ed=s(90010),eu=s(69794);let em=[{value:"always",label:"Always Active",icon:Q.A,color:"bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800"}],ex=()=>(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"relative w-full",children:(0,a.jsx)(K.Skeleton,{className:"h-10 w-full"})}),(0,a.jsx)(K.Skeleton,{className:"h-10 w-32 ml-4"})]}),(0,a.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,a.jsx)("div",{className:"border rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0 space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(K.Skeleton,{className:"h-4 w-4"}),(0,a.jsx)(K.Skeleton,{className:"h-5 w-48"}),(0,a.jsx)(K.Skeleton,{className:"h-5 w-20"})]}),(0,a.jsx)(K.Skeleton,{className:"h-4 w-64"}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(K.Skeleton,{className:"h-4 w-full"}),(0,a.jsx)(K.Skeleton,{className:"h-4 w-3/4"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(K.Skeleton,{className:"h-5 w-24"}),(0,a.jsx)(K.Skeleton,{className:"h-4 w-20"})]}),(0,a.jsx)(K.Skeleton,{className:"h-4 w-16"})]})]}),(0,a.jsx)(K.Skeleton,{className:"h-8 w-8"})]})},e))})]}),eh=e=>{let{threadId:t}=e,[s,l]=(0,n.useState)({isOpen:!1}),[r,i]=(0,n.useState)(null),[o,c]=(0,n.useState)(""),[d,m]=(0,n.useState)({name:"",description:"",content:"",usage_context:"always"}),{data:x,isLoading:h,error:g}=(0,eu.Xe)(t),f=(0,eu.v0)(),p=(0,eu.kq)(),v=(0,eu.QJ)(),j=()=>{m({name:"",description:"",content:"",usage_context:"always"}),l({isOpen:!0})},b=e=>{m({name:e.name,description:e.description||"",content:e.content,usage_context:e.usage_context}),l({entry:e,isOpen:!0})},w=()=>{l({isOpen:!1}),m({name:"",description:"",content:"",usage_context:"always"})},N=async e=>{if(e.preventDefault(),d.name.trim()&&d.content.trim())try{if(s.entry){let e={name:d.name!==s.entry.name?d.name:void 0,description:d.description!==s.entry.description?d.description:void 0,content:d.content!==s.entry.content?d.content:void 0,usage_context:d.usage_context!==s.entry.usage_context?d.usage_context:void 0};Object.values(e).some(e=>void 0!==e)&&await p.mutateAsync({entryId:s.entry.entry_id,data:e})}else await f.mutateAsync({threadId:t,data:d});w()}catch(e){console.error("Error saving knowledge base entry:",e)}},y=async e=>{try{await v.mutateAsync(e),i(null)}catch(e){console.error("Error deleting knowledge base entry:",e)}},k=async e=>{try{await p.mutateAsync({entryId:e.entry_id,data:{is_active:!e.is_active}})}catch(e){console.error("Error toggling entry status:",e)}},C=e=>em.find(t=>t.value===e)||em[0];if(h)return(0,a.jsx)(ex,{});if(g)return(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(W.A,{className:"h-8 w-8 text-red-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:"Failed to load knowledge base"})]})});let _=(null==x?void 0:x.entries)||[],S=_.filter(e=>e.name.toLowerCase().includes(o.toLowerCase())||e.content.toLowerCase().includes(o.toLowerCase())||e.description&&e.description.toLowerCase().includes(o.toLowerCase()));return(0,a.jsxs)("div",{className:"space-y-6",children:[_.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"relative w-full",children:[(0,a.jsx)(ee.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)($.p,{placeholder:"Search knowledge entries...",value:o,onChange:e=>c(e.target.value),className:"pl-9"})]}),(0,a.jsxs)(D.$,{onClick:j,className:"gap-2 ml-4",children:[(0,a.jsx)(et.A,{className:"h-4 w-4"}),"Add Knowledge"]})]}),(0,a.jsx)("div",{className:"space-y-3",children:0===S.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(ee.A,{className:"h-8 w-8 mx-auto text-muted-foreground/50 mb-2"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No entries match your search"})]}):S.map(e=>{let t=C(e.usage_context),s=t.icon;return(0,a.jsx)("div",{className:(0,u.cn)("group border rounded-lg p-4 transition-all",e.is_active?"border-border bg-card hover:border-border/80":"border-border/50 bg-muted/30 opacity-70"),children:(0,a.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0 space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(es.A,{className:"h-4 w-4 text-muted-foreground flex-shrink-0"}),(0,a.jsx)("h3",{className:"font-medium truncate",children:e.name}),!e.is_active&&(0,a.jsxs)(Y.E,{variant:"secondary",className:"text-xs",children:[(0,a.jsx)(ea.A,{className:"h-3 w-3 mr-1"}),"Disabled"]})]}),e.description&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-1",children:e.description}),(0,a.jsx)("p",{className:"text-sm text-foreground/80 line-clamp-2 leading-relaxed",children:e.content}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(Y.E,{variant:"outline",className:(0,u.cn)("text-xs gap-1",t.color),children:[(0,a.jsx)(s,{className:"h-3 w-3"}),t.label]}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground flex items-center gap-1",children:[(0,a.jsx)(E.A,{className:"h-3 w-3"}),new Date(e.created_at).toLocaleDateString()]})]}),e.content_tokens&&(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:["~",e.content_tokens.toLocaleString()," tokens"]})]})]}),(0,a.jsxs)(ec.rI,{children:[(0,a.jsx)(ec.ty,{asChild:!0,children:(0,a.jsx)(D.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,a.jsx)(en.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(ec.SQ,{align:"end",className:"w-36",children:[(0,a.jsxs)(ec._2,{onClick:()=>b(e),children:[(0,a.jsx)(el.A,{className:"h-4 w-4"}),"Edit"]}),(0,a.jsx)(ec._2,{onClick:()=>k(e),children:e.is_active?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(ea.A,{className:"h-4 w-4"}),"Disable"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(er.A,{className:"h-4 w-4"}),"Enable"]})}),(0,a.jsx)(ec.mB,{}),(0,a.jsxs)(ec._2,{onClick:()=>i(e.entry_id),className:"text-destructive focus:bg-destructive/10 focus:text-destructive",children:[(0,a.jsx)(ei.A,{className:"h-4 w-4 text-destructive"}),"Delete"]})]})]})]})},e.entry_id)})})]}),0===_.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"mx-auto w-24 h-24 bg-muted/50 rounded-full flex items-center justify-center mb-4",children:(0,a.jsx)(es.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-2",children:"No Knowledge Entries"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-6 max-w-md mx-auto",children:"Add knowledge entries to provide your agent with context, guidelines, and information it should always remember."}),(0,a.jsxs)(D.$,{onClick:j,className:"gap-2",children:[(0,a.jsx)(et.A,{className:"h-4 w-4"}),"Create Your First Entry"]})]}),(0,a.jsx)(I.lG,{open:s.isOpen,onOpenChange:w,children:(0,a.jsxs)(I.Cf,{className:"max-w-2xl max-h-[85vh] overflow-hidden flex flex-col",children:[(0,a.jsx)(I.c7,{className:"flex-shrink-0",children:(0,a.jsxs)(I.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(es.A,{className:"h-5 w-5"}),s.entry?"Edit Knowledge Entry":"Add Knowledge Entry"]})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,a.jsxs)("form",{onSubmit:N,className:"space-y-6 p-1",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(q.Label,{htmlFor:"name",className:"text-sm font-medium",children:"Name *"}),(0,a.jsx)($.p,{id:"name",value:d.name,onChange:e=>m(t=>({...t,name:e.target.value})),placeholder:"e.g., Company Guidelines, API Documentation",required:!0,className:"w-full"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(q.Label,{htmlFor:"usage_context",className:"text-sm font-medium",children:"Usage Context"}),(0,a.jsxs)(J.l6,{value:d.usage_context,onValueChange:e=>m(t=>({...t,usage_context:e})),children:[(0,a.jsx)(J.bq,{children:(0,a.jsx)(J.yv,{})}),(0,a.jsx)(J.gC,{children:em.map(e=>{let t=e.icon;return(0,a.jsx)(J.eb,{value:e.value,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(t,{className:"h-4 w-4"}),(0,a.jsx)("div",{children:(0,a.jsx)("div",{className:"font-medium",children:e.label})})]})},e.value)})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(q.Label,{htmlFor:"description",className:"text-sm font-medium",children:"Description"}),(0,a.jsx)($.p,{id:"description",value:d.description,onChange:e=>m(t=>({...t,description:e.target.value})),placeholder:"Brief description of this knowledge (optional)"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(q.Label,{htmlFor:"content",className:"text-sm font-medium",children:"Content *"}),(0,a.jsx)(X.T,{id:"content",value:d.content,onChange:e=>m(t=>({...t,content:e.target.value})),placeholder:"Enter the knowledge content that your agent should know...",className:"min-h-[200px] resize-y",required:!0}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Approximately ",Math.ceil(d.content.length/4).toLocaleString()," tokens"]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-3 pt-4 border-t",children:[(0,a.jsx)(D.$,{type:"button",variant:"outline",onClick:w,children:"Cancel"}),(0,a.jsxs)(D.$,{type:"submit",disabled:!d.name.trim()||!d.content.trim()||f.isPending||p.isPending,className:"gap-2",children:[f.isPending||p.isPending?(0,a.jsx)(eo.A,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(et.A,{className:"h-4 w-4"}),s.entry?"Save Changes":"Add Knowledge"]})]})]})})]})}),(0,a.jsx)(ed.Lt,{open:!!r,onOpenChange:()=>i(null),children:(0,a.jsxs)(ed.EO,{children:[(0,a.jsxs)(ed.wd,{children:[(0,a.jsxs)(ed.r7,{className:"flex items-center gap-2",children:[(0,a.jsx)(W.A,{className:"h-5 w-5 text-destructive"}),"Delete Knowledge Entry"]}),(0,a.jsx)(ed.$v,{children:"This will permanently delete this knowledge entry. Your agent will no longer have access to this information."})]}),(0,a.jsxs)(ed.ck,{children:[(0,a.jsx)(ed.Zr,{children:"Cancel"}),(0,a.jsxs)(ed.Rx,{onClick:()=>r&&y(r),className:"bg-destructive hover:bg-destructive/90",disabled:v.isPending,children:[v.isPending?(0,a.jsx)(eo.A,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(ei.A,{className:"h-4 w-4"}),"Delete Entry"]})]})]})})]})};var eg=s(17711);function ef(e){let{threadId:t,projectId:s,projectName:r,onViewFiles:o,onToggleSidePanel:d,onProjectRenamed:x,isMobileView:h,debugMode:g}=e;(0,l.usePathname)();let[f,p]=(0,n.useState)(!1),[v,j]=(0,n.useState)(r),b=(0,n.useRef)(null),[w,N]=(0,n.useState)(!1),[y,k]=(0,n.useState)(!1),C=(0,Z.jE)(),{flags:_,loading:S}=(0,eg.h)(["knowledge_base"]),A=_.knowledge_base,E=(0,m.a)()||h,{setOpenMobile:R}=(0,c.cL)(),L=(0,U.sS)(),q=()=>{p(!1),j(r)},X=async()=>{if(""===v.trim()){j(r),p(!1);return}if(v!==r)try{if(!s){i.oR.error("Cannot rename: Project ID is missing"),j(r),p(!1);return}if(await L.mutateAsync({projectId:s,data:{name:v}}))null==x||x(v),C.invalidateQueries({queryKey:H.X.project(s)});else throw Error("Failed to update project")}catch(t){let e=t instanceof Error?t.message:"Failed to rename project";console.error("Failed to rename project:",e),i.oR.error(e),j(r)}p(!1)};return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("header",{className:(0,u.cn)("bg-background sticky top-0 flex h-14 shrink-0 items-center gap-2 z-20 w-full",E&&"px-2"),children:[E&&(0,a.jsx)(D.$,{variant:"ghost",size:"icon",onClick:()=>R(!0),className:"h-9 w-9 mr-1","aria-label":"Open sidebar",children:(0,a.jsx)(P.A,{className:"h-4 w-4"})}),(0,a.jsx)("div",{className:"flex flex-1 items-center gap-2 px-3",children:f?(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)($.p,{ref:b,value:v,onChange:e=>j(e.target.value),onKeyDown:e=>{"Enter"===e.key?X():"Escape"===e.key&&q()},onBlur:X,className:"h-8 w-auto min-w-[180px] text-base font-medium",maxLength:50}),(0,a.jsx)(D.$,{variant:"ghost",size:"icon",className:"h-7 w-7",onClick:X,children:(0,a.jsx)(T.A,{className:"h-3.5 w-3.5"})}),(0,a.jsx)(D.$,{variant:"ghost",size:"icon",className:"h-7 w-7",onClick:q,children:(0,a.jsx)(O.A,{className:"h-3.5 w-3.5"})})]}):r&&"Project"!==r?(0,a.jsx)("div",{className:"text-base font-medium text-muted-foreground hover:text-foreground cursor-pointer flex items-center",onClick:()=>{j(r),p(!0),setTimeout(()=>{var e,t;null==(e=b.current)||e.focus(),null==(t=b.current)||t.select()},0)},title:"Click to rename project",children:r}):(0,a.jsx)(K.Skeleton,{className:"h-5 w-32"})}),(0,a.jsxs)("div",{className:"flex items-center gap-1 pr-4",children:[g&&(0,a.jsx)("div",{className:"bg-amber-500 text-black text-xs px-2 py-0.5 rounded-md mr-2",children:"Debug"}),E?(0,a.jsx)(D.$,{variant:"ghost",size:"icon",onClick:d,className:"h-9 w-9 cursor-pointer","aria-label":"Toggle computer panel",children:(0,a.jsx)(F.A,{className:"h-4 w-4"})}):(0,a.jsxs)(G.Bc,{children:[(0,a.jsxs)(G.m_,{children:[(0,a.jsx)(G.k$,{asChild:!0,children:(0,a.jsx)(D.$,{variant:"ghost",size:"icon",onClick:o,className:"h-9 w-9 cursor-pointer",children:(0,a.jsx)(z.A,{className:"h-4 w-4"})})}),(0,a.jsx)(G.ZI,{children:(0,a.jsx)("p",{children:"View Files in Task"})})]}),A&&(0,a.jsxs)(G.m_,{children:[(0,a.jsx)(G.k$,{asChild:!0,children:(0,a.jsx)(D.$,{variant:"ghost",size:"icon",onClick:()=>{k(!0)},className:"h-9 w-9 cursor-pointer",children:(0,a.jsx)(M.A,{className:"h-4 w-4"})})}),(0,a.jsx)(G.ZI,{children:(0,a.jsx)("p",{children:"Knowledge Base"})})]}),(0,a.jsxs)(G.m_,{children:[(0,a.jsx)(G.k$,{asChild:!0,children:(0,a.jsx)(D.$,{variant:"ghost",size:"icon",onClick:()=>{N(!0)},className:"h-9 w-9 cursor-pointer",children:(0,a.jsx)(V.A,{className:"h-4 w-4"})})}),(0,a.jsx)(G.ZI,{children:(0,a.jsx)("p",{children:"Share Chat"})})]})]})]})]}),(0,a.jsx)(B.Z,{isOpen:w,onClose:()=>N(!1),threadId:t,projectId:s}),(0,a.jsx)(I.lG,{open:y,onOpenChange:k,children:(0,a.jsx)(I.Cf,{className:"max-w-2xl max-h-[90vh] overflow-hidden p-0",children:(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsx)(I.c7,{className:"px-6 py-4",children:(0,a.jsxs)(I.L3,{className:"flex items-center gap-2 text-lg",children:[(0,a.jsx)(M.A,{className:"h-5 w-5"}),"Knowledge Base"]})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto p-6",children:(0,a.jsx)(eh,{threadId:t})})]})})})]})}var ep=s(51548),ev=s(19363),ej=s(61446);function eb(e){let{children:t,threadId:s,projectName:n,projectId:l,project:r,sandboxId:i,isSidePanelOpen:o,onToggleSidePanel:c,onProjectRenamed:d,onViewFiles:u,fileViewerOpen:m,setFileViewerOpen:x,fileToView:h,filePathList:g,toolCalls:f,messages:p,externalNavIndex:v,agentStatus:j,currentToolIndex:b,onSidePanelNavigate:w,onSidePanelClose:N,renderAssistantMessage:y,renderToolResult:k,isLoading:C,showBillingAlert:_,billingData:S,onDismissBilling:A,debugMode:E,isMobile:R,initialLoadCompleted:I,agentName:D}=e;return(0,a.jsxs)("div",{className:"flex h-screen",children:[E&&(0,a.jsx)("div",{className:"fixed top-16 right-4 bg-amber-500 text-black text-xs px-2 py-1 rounded-md shadow-md z-50",children:"Debug Mode"}),(0,a.jsxs)("div",{className:"flex flex-col flex-1 overflow-hidden transition-all duration-200 ease-in-out ".concat(!I||o?"mr-[90%] sm:mr-[450px] md:mr-[500px] lg:mr-[550px] xl:mr-[650px]":""),children:[(0,a.jsx)(ef,{threadId:s,projectName:n,projectId:l,onViewFiles:u,onToggleSidePanel:c,onProjectRenamed:d,isMobileView:R,debugMode:E}),t]}),(0,a.jsx)(ev.s,{isOpen:o&&I,onClose:N,toolCalls:f,messages:p,externalNavigateToIndex:v,agentStatus:j,currentIndex:b,onNavigate:w,project:r||void 0,renderAssistantMessage:y,renderToolResult:k,isLoading:!I||C,onFileClick:u,agentName:D}),i&&(0,a.jsx)(ep.S,{open:m,onOpenChange:x,sandboxId:i,initialFilePath:h,project:r||void 0,filePathList:g}),(0,a.jsx)(ej.i,{message:S.message,currentUsage:S.currentUsage,limit:S.limit,accountId:S.accountId,onDismiss:A,isOpen:_})]})}var ew=s(38198);function eN(e){var t,s,k,_;let{params:S}=e,{projectId:A,threadId:E}=n.use(S),R=(0,m.a)(),I=(0,l.useSearchParams)(),[D,P]=(0,n.useState)(""),[T,O]=(0,n.useState)(!1),[F,z]=(0,n.useState)(!1),[M,V]=(0,n.useState)(null),[G,$]=(0,n.useState)(void 0),[U,K]=(0,n.useState)(!1),[B,Z]=(0,n.useState)(!1),[H,q]=(0,n.useState)(!1),[X,J]=(0,n.useState)(void 0),Y=(0,n.useRef)(null),Q=(0,n.useRef)(null),W=(0,n.useRef)(null),[ee,et]=(0,n.useState)(!1),[es,ea]=(0,n.useState)(!1);(0,n.useRef)(!1);let en=(0,n.useRef)(!1),{state:el,setOpen:er}=(0,c.cL)(),{messages:ei,setMessages:eo,project:ec,sandboxId:ed,projectName:eu,agentRunId:em,setAgentRunId:ex,agentStatus:eh,setAgentStatus:eg,isLoading:ef,error:ep,initialLoadCompleted:ev,threadQuery:ej,messagesQuery:eN,projectQuery:ey,agentRunsQuery:ek}=function(e,t){let[s,a]=(0,n.useState)([]),[l,r]=(0,n.useState)(null),[o,c]=(0,n.useState)(null),[d,u]=(0,n.useState)(""),[m,x]=(0,n.useState)(null),[h,g]=(0,n.useState)("idle"),[v,w]=(0,n.useState)(!0),[N,y]=(0,n.useState)(null),k=(0,n.useRef)(!1),C=(0,n.useRef)(!1),_=(0,n.useRef)(!1),S=(0,n.useRef)(!1),A=(0,j.K2)(e),E=(0,f.C)(e),R=(0,b.BR)(t),I=(0,p.P3)(e);return(0,n.useEffect)(()=>{let t=!0;async function s(){k.current||w(!0),y(null);try{if(!e)throw Error("Thread ID is required");if(A.isError)throw Error("Failed to load thread data: "+A.error);if(!t)return;if(R.data){var s;r(R.data),"string"==typeof R.data.sandbox?c(R.data.sandbox):(null==(s=R.data.sandbox)?void 0:s.id)&&c(R.data.sandbox.id),u(R.data.name||"")}if(E.data&&!C.current){let t=(E.data||[]).filter(e=>"status"!==e.type).map(t=>({message_id:t.message_id||null,thread_id:t.thread_id||e,type:t.type||"system",is_llm_message:!!t.is_llm_message,content:t.content||"",metadata:t.metadata||"{}",created_at:t.created_at||new Date().toISOString(),updated_at:t.updated_at||new Date().toISOString()}));a(t),console.log("[PAGE] Loaded Messages (excluding status, keeping browser_state):",t.length),C.current=!0,S.current||(S.current=!0)}if(I.data&&!_.current&&t){console.log("[PAGE] Checking for active agent runs..."),_.current=!0;let e=I.data.find(e=>"running"===e.status);e&&t?(console.log("[PAGE] Found active run on load:",e.id),x(e.id)):(console.log("[PAGE] No active agent runs found"),t&&g("idle"))}A.data&&E.data&&I.data&&(k.current=!0,w(!1))}catch(e){if(console.error("Error loading thread data:",e),t){let t=e instanceof Error?e.message:"Failed to load thread";y(t),i.oR.error(t),w(!1)}}}return e&&s(),()=>{t=!1}},[e,A.data,A.isError,A.error,R.data,E.data,I.data]),(0,n.useEffect)(()=>{E.data&&"success"===E.status&&!v&&"running"!==h&&"connecting"!==h&&a((E.data||[]).filter(e=>"status"!==e.type).map(t=>({message_id:t.message_id||null,thread_id:t.thread_id||e,type:t.type||"system",is_llm_message:!!t.is_llm_message,content:t.content||"",metadata:t.metadata||"{}",created_at:t.created_at||new Date().toISOString(),updated_at:t.updated_at||new Date().toISOString(),agent_id:t.agent_id,agents:t.agents})))},[E.data,E.status,v,h,e]),{messages:s,setMessages:a,project:l,sandboxId:o,projectName:d,agentRunId:m,setAgentRunId:x,agentStatus:h,setAgentStatus:g,isLoading:v,error:N,initialLoadCompleted:k.current,threadQuery:A,messagesQuery:E,projectQuery:R,agentRunsQuery:I}}(E,A),{toolCalls:eC,setToolCalls:e_,currentToolIndex:eS,setCurrentToolIndex:eA,isSidePanelOpen:eE,setIsSidePanelOpen:eR,autoOpenedPanel:eI,setAutoOpenedPanel:eD,externalNavIndex:eL,setExternalNavIndex:eP,handleToolClick:eT,handleStreamingToolCall:eO,toggleSidePanel:eF,handleSidePanelNavigate:ez,userClosedPanelRef:eM}=function(e,t,s){let[a,l]=(0,n.useState)([]),[r,o]=(0,n.useState)(0),[c,d]=(0,n.useState)(!1),[u,m]=(0,n.useState)(!1),[x,h]=(0,n.useState)(void 0),g=(0,n.useRef)(!1),f=(0,n.useRef)(!1),p=(0,n.useCallback)(()=>{d(e=>{let s=!e;return s||(g.current=!0),s&&t(!1),s})},[t]),v=(0,n.useCallback)(e=>{o(e),f.current=!0},[]),j=(0,n.useRef)(new Map);(0,n.useEffect)(()=>{let t=[],a=new Map;e.filter(e=>"assistant"===e.type&&e.message_id).forEach(s=>{let n=e.find(e=>{if("tool"!==e.type||!e.metadata||!s.message_id)return!1;try{return(0,w.jD)(e.metadata,{}).assistant_message_id===s.message_id}catch(e){return!1}});if(n){let e="unknown",r=!0,i=function(e){try{let t="string"==typeof e?(0,w.jD)(e,e):e;if(t&&"object"==typeof t){if("tool_name"in t||"xml_tag_name"in t)return{toolName:t.tool_name||t.xml_tag_name||"unknown",parameters:t.parameters||{},result:t.result||null};if("content"in t&&"object"==typeof t.content){let e=t.content;if("tool_name"in e||"xml_tag_name"in e)return{toolName:e.tool_name||e.xml_tag_name||"unknown",parameters:e.parameters||{},result:e.result||null}}}}catch(e){}return null}(n.content);if(i)e=i.toolName.replace(/_/g,"-").toLowerCase(),i.result&&"object"==typeof i.result&&(r=!1!==i.result.success);else{try{let t=(()=>{try{return(0,w.jD)(s.content,{}).content||s.content}catch(e){return s.content}})(),a=(0,N.bi)(t);if(a)e=a;else{let t=(0,w.jD)(s.content,{});if(t.tool_calls&&t.tool_calls.length>0){var l;let s=t.tool_calls[0];e=((null==(l=s.function)?void 0:l.name)||s.name||"unknown").replace(/_/g,"-").toLowerCase()}}}catch(e){}try{let e=(()=>{try{return(0,w.jD)(n.content,{}).content||n.content}catch(e){return n.content}})();if(e&&"string"==typeof e){let t=e.match(/ToolResult\s*\(\s*success\s*=\s*(True|False|true|false)/i);if(t)r="true"===t[1].toLowerCase();else{let t=e.toLowerCase();r=!(t.includes("failed")||t.includes("error")||t.includes("failure"))}}}catch(e){}}let o=t.length;t.push({assistantCall:{name:e,content:s.content,timestamp:s.created_at},toolResult:{content:n.content,isSuccess:r,timestamp:n.created_at}}),s.message_id&&a.set(s.message_id,o)}}),j.current=a,l(t),t.length>0&&(("running"!==s||f.current)&&(!c||g.current||f.current)?c||u||g.current||(o(t.length-1),d(!0),m(!0)):o(t.length-1))},[e,c,u,s]),(0,n.useEffect)(()=>{"idle"===s&&(f.current=!1)},[s]),(0,n.useEffect)(()=>{c||m(!1)},[c]);let b=(0,n.useCallback)((t,s)=>{if(!t){console.warn("Clicked assistant message ID is null. Cannot open side panel."),i.oR.warning("Cannot view details: Assistant message ID is missing.");return}g.current=!1,f.current=!0,console.log("[PAGE] Tool Click Triggered. Assistant Message ID:",t,"Tool Name:",s);let n=j.current.get(t);if(void 0!==n)console.log("[PAGE] Found tool call at index ".concat(n," for assistant message ").concat(t)),h(n),o(n),d(!0),setTimeout(()=>h(void 0),100);else{if(console.warn("[PAGE] Could not find matching tool call in toolCalls array for assistant message ID: ".concat(t)),e.find(e=>e.message_id===t&&"assistant"===e.type)){let s=e.filter(e=>"assistant"===e.type&&e.message_id).findIndex(e=>e.message_id===t);if(-1!==s&&s<a.length){console.log("[PAGE] Using fallback: found tool at index ".concat(s)),h(s),o(s),d(!0),setTimeout(()=>h(void 0),100);return}}i.oR.info("Could not find details for this tool call.")}},[e,a]),y=(0,n.useCallback)(e=>{if(!e)return;let t=e.name||e.xml_tag_name||"Unknown Tool",s=t.replace(/_/g,"-").toLowerCase();if(console.log("[STREAM] Received tool call:",s,"(raw:",t,")"),g.current)return;let n=e.arguments||"",r=n;if(s.includes("command")&&!n.includes("<execute-command>"))r="<execute-command>".concat(n,"</execute-command>");else if(s.includes("file")||"create-file"===s||"delete-file"===s||"full-file-rewrite"===s){let e=["create-file","delete-file","full-file-rewrite"].find(e=>s===e);if(e)if(n.includes("<".concat(e,">"))||n.includes("file_path="))r=n;else{let t=n.trim();r=t&&!t.startsWith("<")?"<".concat(e,' file_path="').concat(t,'">'):"<".concat(e,">").concat(n,"</").concat(e,">")}}let i={assistantCall:{name:s,content:r,timestamp:new Date().toISOString()},toolResult:{content:"STREAMING",isSuccess:!0,timestamp:new Date().toISOString()}};l(e=>{let t=e.findIndex(e=>{var t;return(null==(t=e.toolResult)?void 0:t.content)==="STREAMING"});if(-1===t||e[t].assistantCall.name!==s)return[...e,i];{let s=[...e];return s[t]={...s[t],assistantCall:{...s[t].assistantCall,content:r}},s}}),f.current||o(e=>a.length+1-1),d(!0)},[a.length]);return{toolCalls:a,setToolCalls:l,currentToolIndex:r,setCurrentToolIndex:o,isSidePanelOpen:c,setIsSidePanelOpen:d,autoOpenedPanel:u,setAutoOpenedPanel:m,externalNavIndex:x,setExternalNavIndex:h,handleToolClick:b,handleStreamingToolCall:y,toggleSidePanel:p,handleSidePanelNavigate:v,userClosedPanelRef:g}}(ei,er,eh),{showBillingAlert:eV,setShowBillingAlert:eG,billingData:e$,setBillingData:eU,checkBillingLimits:eK,billingStatusQuery:eB}=function(e,t,s){let[a,l]=(0,n.useState)(!1),[r,i]=(0,n.useState)({}),o=(0,n.useRef)("idle"),c=(0,y.p)(),d=(0,n.useCallback)(async()=>{if((0,x.Jn)())return console.log("Running in local development mode - billing checks are disabled"),!1;try{await c.refetch();let a=c.data;if(a&&!a.can_run){var t,s;return i({currentUsage:(null==(t=a.subscription)?void 0:t.minutes_limit)||0,limit:(null==(s=a.subscription)?void 0:s.minutes_limit)||0,message:a.message||"Usage limit reached",accountId:e||null}),l(!0),!0}return!1}catch(e){return console.error("Error checking billing status:",e),!1}},[e,c]);return(0,n.useEffect)(()=>{"running"===o.current&&"idle"===t&&d(),o.current=t},[t,d]),(0,n.useEffect)(()=>{e&&s&&!c.data&&(console.log("Checking billing status on initial load"),d())},[e,d,s,c.data]),{showBillingAlert:a,setShowBillingAlert:l,billingData:r,setBillingData:i,checkBillingLimits:d,billingStatusQuery:c}}(null==ec?void 0:ec.account_id,eh,ev);!function(e){let{isSidePanelOpen:t,setIsSidePanelOpen:s,leftSidebarState:a,setLeftSidebarOpen:l,userClosedPanelRef:r}=e;(0,n.useEffect)(()=>{let e=e=>{(e.metaKey||e.ctrlKey)&&"i"===e.key&&(e.preventDefault(),t?(s(!1),r.current=!0):(s(!0),l(!1))),(e.metaKey||e.ctrlKey)&&"b"===e.key&&(e.preventDefault(),"expanded"===a?l(!1):(l(!0),t&&(s(!1),r.current=!0))),"Escape"===e.key&&t&&(s(!1),r.current=!0)};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[t,a,l,s,r])}({isSidePanelOpen:eE,setIsSidePanelOpen:eR,leftSidebarState:el,setLeftSidebarOpen:er,userClosedPanelRef:eM});let eZ=(0,f.U)(),eH=(0,p.WZ)(),eq=(0,p.CV)(),{data:eX}=(0,ew.jz)(E),eJ=null==eX?void 0:eX.agent;null==(s=ej.data)||null==(t=s.metadata)||t.workflow_id,(0,n.useEffect)(()=>{(null==eX?void 0:eX.agent)&&!X&&J(eX.agent.agent_id)},[eX,X]);let{data:eY}=(0,v.Rs)(),eQ=(null==eY?void 0:eY.status)==="active"?"active":"no_subscription";!function(e){var t,s;let a=(0,n.useRef)(null),l=(0,n.useRef)(!1),r=(0,n.useRef)(null),i=(0,n.useRef)(0),o=(0,n.useRef)(!1),c=(0,n.useCallback)(e=>{if(o.current||l.current)return;o.current=!0,console.log("[VNC PRELOADER] Attempt ".concat(i.current+1,"/10 - Starting VNC preload:"),e);let t=document.createElement("iframe");t.src=e,t.style.position="absolute",t.style.left="-9999px",t.style.top="-9999px",t.style.width="1024px",t.style.height="768px",t.style.border="0",t.title="VNC Preloader";let s=setTimeout(()=>{if(console.log("[VNC PRELOADER] Load timeout - VNC service likely not ready"),t.parentNode&&t.parentNode.removeChild(t),i.current<10){i.current++,o.current=!1;let t=Math.min(2e3*Math.pow(1.5,i.current-1),15e3);console.log("[VNC PRELOADER] Retrying in ".concat(t,"ms (attempt ").concat(i.current+1,"/10)")),r.current=setTimeout(()=>{c(e)},t)}else console.log("[VNC PRELOADER] Max retries reached, giving up on preloading"),o.current=!1},5e3);t.onload=()=>{clearTimeout(s),console.log("[VNC PRELOADER] ✅ VNC iframe preloaded successfully!"),l.current=!0,o.current=!1,a.current=t},t.onerror=()=>{if(clearTimeout(s),console.log("[VNC PRELOADER] VNC iframe failed to load (onerror)"),t.parentNode&&t.parentNode.removeChild(t),i.current<10){i.current++,o.current=!1;let t=Math.min(2e3*Math.pow(1.5,i.current-1),15e3);console.log("[VNC PRELOADER] Retrying in ".concat(t,"ms (attempt ").concat(i.current+1,"/10)")),r.current=setTimeout(()=>{c(e)},t)}else console.log("[VNC PRELOADER] Max retries reached, giving up on preloading"),o.current=!1},document.body.appendChild(t),console.log("[VNC PRELOADER] VNC iframe added to DOM, waiting for load...")},[]);(0,n.useEffect)(()=>{var t,s;if(!(null==e||null==(t=e.sandbox)?void 0:t.vnc_preview)||!(null==e||null==(s=e.sandbox)?void 0:s.pass)||l.current||o.current)return;let n="".concat(e.sandbox.vnc_preview,"/vnc_lite.html?password=").concat(e.sandbox.pass,"&autoconnect=true&scale=local&width=1024&height=768");i.current=0,o.current=!1;let d=setTimeout(()=>{c(n)},1e3);return()=>{clearTimeout(d),r.current&&(clearTimeout(r.current),r.current=null),a.current&&a.current.parentNode&&(a.current.parentNode.removeChild(a.current),a.current=null),l.current=!1,o.current=!1,i.current=0}},[null==e||null==(t=e.sandbox)?void 0:t.vnc_preview,null==e||null==(s=e.sandbox)?void 0:s.pass,c]),l.current,a.current}((0,n.useMemo)(()=>ec,[null==ec?void 0:ec.id,null==ec||null==(k=ec.sandbox)?void 0:k.vnc_preview,null==ec||null==(_=ec.sandbox)?void 0:_.pass]));let eW=(0,n.useCallback)(e=>{},[]),e0=function(){var e;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"smooth";null==(e=Y.current)||e.scrollIntoView({behavior:t})},e1=(0,n.useCallback)(e=>{if(console.log("[STREAM HANDLER] Received message: ID=".concat(e.message_id,", Type=").concat(e.type)),!e.message_id){var t;console.warn("[STREAM HANDLER] Received message is missing ID: Type=".concat(e.type,", Content=").concat(null==(t=e.content)?void 0:t.substring(0,50),"..."))}eo(t=>t.some(t=>t.message_id===e.message_id)?t.map(t=>t.message_id===e.message_id?e:t):[...t,e]),"tool"===e.type&&eD(!1)},[eo,eD]),e4=(0,n.useCallback)(e=>{switch(console.log("[PAGE] Hook status changed: ".concat(e)),e){case"idle":case"completed":case"stopped":case"agent_not_running":case"error":case"failed":eg("idle"),ex(null),eD(!1),["completed","stopped","agent_not_running","error","failed"].includes(e)&&e0("smooth");break;case"connecting":eg("connecting");break;case"streaming":eg("running")}},[eg,ex,eD]),e2=(0,n.useCallback)(e=>{console.error("[PAGE] Stream hook error: ".concat(e)),e.toLowerCase().includes("not found")||e.toLowerCase().includes("agent run is not running")||i.oR.error("Stream Error: ".concat(e))},[]),e5=(0,n.useCallback)(()=>{console.log("[PAGE] Stream hook closed with final status: ".concat(eh))},[eh]),{status:e3,textContent:e6,toolCall:e9,error:e7,agentRunId:e8,startStreaming:te,stopStreaming:tt}=(0,d.Z)({onMessage:e1,onStatusChange:e4,onError:e2,onClose:e5},E,eo),ts=(0,n.useCallback)(async(e,t)=>{if(!e.trim())return;O(!0);let s={message_id:"temp-".concat(Date.now()),thread_id:E,type:"user",is_llm_message:!1,content:e,metadata:"{}",created_at:new Date().toISOString(),updated_at:new Date().toISOString()};eo(e=>[...e,s]),P(""),e0("smooth");try{let a=eZ.mutateAsync({threadId:E,message:e}),n=eH.mutateAsync({threadId:E,options:{...t,agent_id:X}}),l=await Promise.allSettled([a,n]);if("rejected"===l[0].status){let e=l[0].reason;throw console.error("Failed to send message:",e),Error("Failed to send message: ".concat((null==e?void 0:e.message)||e))}if("rejected"===l[1].status){let e=l[1].reason;if(console.error("Failed to start agent:",e),e instanceof r.Ey){console.log("Caught BillingError:",e.detail),eU({currentUsage:e.detail.currentUsage,limit:e.detail.limit,message:e.detail.message||"Monthly usage limit reached. Please upgrade.",accountId:(null==ec?void 0:ec.account_id)||null}),eG(!0),eo(e=>e.filter(e=>e.message_id!==s.message_id));return}throw Error("Failed to start agent: ".concat((null==e?void 0:e.message)||e))}let i=l[1].value;ex(i.agent_run_id),eN.refetch(),ek.refetch()}catch(e){console.error("Error sending message or starting agent:",e),e instanceof r.Ey||i.oR.error(e instanceof Error?e.message:"Operation failed"),eo(e=>e.filter(e=>e.message_id!==s.message_id))}finally{O(!1)}},[E,null==ec?void 0:ec.account_id,eZ,eH,eN,ek,eo,eU,eG,ex]),ta=(0,n.useCallback)(async()=>{if(console.log("[PAGE] Requesting agent stop via hook."),eg("idle"),await tt(),em)try{await eq.mutateAsync(em),ek.refetch()}catch(e){console.error("Error stopping agent:",e)}},[tt,em,eq,ek,eg]),tn=(0,n.useCallback)((e,t)=>{e?V(e):V(null),$(t),z(!0)},[]),tl=(0,n.useCallback)((e,t)=>e?(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{className:"text-xs font-medium text-muted-foreground",children:"Assistant Message"}),(0,a.jsx)("div",{className:"rounded-md border bg-muted/50 p-3",children:(0,a.jsx)("div",{className:"text-xs prose prose-xs dark:prose-invert chat-markdown max-w-none",children:e})})]}):null,[]),tr=(0,n.useCallback)((e,t)=>e?(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("div",{className:"text-xs font-medium text-muted-foreground",children:"Tool Result"}),(0,a.jsx)("div",{className:"px-2 py-0.5 rounded-full text-xs ".concat(t?"bg-green-50 text-green-700 dark:bg-green-900 dark:text-green-300":"bg-red-50 text-red-700 dark:bg-red-900 dark:text-red-300"),children:t?"Success":"Failed"})]}),(0,a.jsx)("div",{className:"rounded-md border bg-muted/50 p-3",children:(0,a.jsx)("div",{className:"text-xs prose prose-xs dark:prose-invert chat-markdown max-w-none",children:e})})]}):null,[]);return((0,n.useEffect)(()=>{en.current||(er(!1),en.current=!0)},[er]),(0,n.useEffect)(()=>{ev&&!H&&(q(!0),eC.length>0?(eR(!0),eA(eC.length-1)):ei.length>0&&eR(!0))},[H,ei,eC,ev,eR,eA]),(0,n.useEffect)(()=>{em&&em!==e8&&(console.log("[PAGE] Target agentRunId set to ".concat(em,", initiating stream...")),te(em))},[em,te,e8]),(0,n.useEffect)(()=>{let e=ei[ei.length-1];(null==e?void 0:e.type)!=="user"&&"running"!==eh||es||e0("smooth")},[ei,eh,es]),(0,n.useEffect)(()=>{if(!W.current||0===ei.length)return;let e=new IntersectionObserver(e=>{let[t]=e;return et(!(null==t?void 0:t.isIntersecting))},{root:Q.current,threshold:.1});return e.observe(W.current),()=>e.disconnect()},[ei,e6,e9]),(0,n.useEffect)(()=>{console.log("[PAGE] \uD83D\uDD04 Page AgentStatus: ".concat(eh,", Hook Status: ").concat(e3,", Target RunID: ").concat(em||"none",", Hook RunID: ").concat(e8||"none")),("completed"===e3||"stopped"===e3||"agent_not_running"===e3||"error"===e3)&&("running"===eh||"connecting"===eh)&&(console.log("[PAGE] Detected hook completed but UI still shows running, updating status"),eg("idle"),ex(null),eD(!1))},[eh,e3,em,e8,eg,ex,eD]),(0,n.useEffect)(()=>{if(eu){document.title="".concat(eu," | Kortix Suna");let e=document.querySelector('meta[name="description"]');e&&e.setAttribute("content","".concat(eu," - Interactive agent conversation powered by Kortix Suna"));let t=document.querySelector('meta[property="og:title"]');t&&t.setAttribute("content","".concat(eu," | Kortix Suna"));let s=document.querySelector('meta[property="og:description"]');s&&s.setAttribute("content","Interactive AI conversation for ".concat(eu))}},[eu]),(0,n.useEffect)(()=>{Z("true"===I.get("debug"))},[I]),(0,n.useEffect)(()=>{if(ev&&eY){let e=localStorage.getItem("suna_upgrade_dialog_displayed"),t="no_subscription"===eQ;e||!t||(0,x.Jn)()||K(!0)}},[eY,eQ,ev]),(0,n.useEffect)(()=>{e9&&eO(e9)},[e9,eO]),!ev||ef)?(0,a.jsx)(g.y,{isSidePanelOpen:eE}):ep?(0,a.jsx)(eb,{threadId:E,projectName:eu,projectId:(null==ec?void 0:ec.id)||"",project:ec,sandboxId:ed,isSidePanelOpen:eE,onToggleSidePanel:eF,onViewFiles:tn,fileViewerOpen:F,setFileViewerOpen:z,fileToView:M,filePathList:G,toolCalls:eC,messages:ei,externalNavIndex:eL,agentStatus:eh,currentToolIndex:eS,onSidePanelNavigate:ez,onSidePanelClose:()=>{eR(!1),eM.current=!0,eD(!0)},renderAssistantMessage:tl,renderToolResult:tr,isLoading:!ev||ef,showBillingAlert:eV,billingData:e$,onDismissBilling:()=>eG(!1),debugMode:B,isMobile:R,initialLoadCompleted:ev,agentName:eJ&&eJ.name,children:(0,a.jsx)(C,{error:ep})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(eb,{threadId:E,projectName:eu,projectId:(null==ec?void 0:ec.id)||"",project:ec,sandboxId:ed,isSidePanelOpen:eE,onToggleSidePanel:eF,onProjectRenamed:eW,onViewFiles:tn,fileViewerOpen:F,setFileViewerOpen:z,fileToView:M,filePathList:G,toolCalls:eC,messages:ei,externalNavIndex:eL,agentStatus:eh,currentToolIndex:eS,onSidePanelNavigate:ez,onSidePanelClose:()=>{eR(!1),eM.current=!0,eD(!0)},renderAssistantMessage:tl,renderToolResult:tr,isLoading:!ev||ef,showBillingAlert:eV,billingData:e$,onDismissBilling:()=>eG(!1),debugMode:B,isMobile:R,initialLoadCompleted:ev,agentName:eJ&&eJ.name,children:[(0,a.jsx)(h.u9,{messages:ei,streamingTextContent:e6,streamingToolCall:e9,agentStatus:eh,handleToolClick:eT,handleOpenFileViewer:tn,readOnly:!1,streamHookStatus:e3,sandboxId:ed,project:ec,debugMode:B,agentName:eJ&&eJ.name,agentAvatar:eJ&&eJ.avatar}),(0,a.jsx)("div",{className:(0,u.cn)("fixed bottom-0 z-10 bg-gradient-to-t from-background via-background/90 to-transparent px-4 pt-8 transition-all duration-200 ease-in-out","expanded"===el?"left-[72px] md:left-[256px]":"left-[72px]",eE?"right-[90%] sm:right-[450px] md:right-[500px] lg:right-[550px] xl:right-[650px]":"right-0",R?"left-0 right-0":""),children:(0,a.jsx)("div",{className:(0,u.cn)("mx-auto",R?"w-full":"max-w-3xl"),children:(0,a.jsx)(o.V,{value:D,onChange:P,onSubmit:ts,placeholder:"Describe what you need help with...",loading:T,disabled:T||"running"===eh||"connecting"===eh,isAgentRunning:"running"===eh||"connecting"===eh,onStopAgent:ta,autoFocus:!ef,onFileBrowse:tn,sandboxId:ed||void 0,messages:ei,agentName:eJ&&eJ.name,selectedAgentId:X,onAgentSelect:J,toolCalls:eC,toolCallIndex:eS,showToolPreview:!eE&&eC.length>0,onExpandToolPreview:()=>{eR(!0),eM.current=!1}})})})]}),(0,a.jsx)(L,{open:U,onOpenChange:K,onDismiss:()=>{K(!1),localStorage.setItem("suna_upgrade_dialog_displayed","true")}})]})}},45132:(e,t,s)=>{"use strict";s.d(t,{Z:()=>w});var a=s(95155),n=s(12115),l=s(30285),r=s(54165),i=s(62523),o=s(85057),c=s(55365),d=s(66516),u=s(34869),m=s(5196),x=s(24357),h=s(77639),g=s(51154),f=s(38164),p=s(56671),v=s(27379),j=s(68856);let b=()=>(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"rounded-lg border p-4",children:(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)(j.Skeleton,{className:"h-4 w-4"}),(0,a.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,a.jsx)(j.Skeleton,{className:"h-4 w-3/4"}),(0,a.jsx)(j.Skeleton,{className:"h-4 w-full"})]})]})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(j.Skeleton,{className:"h-4 w-20"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(j.Skeleton,{className:"h-9 flex-1"}),(0,a.jsx)(j.Skeleton,{className:"h-9 w-9"})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(j.Skeleton,{className:"h-4 w-24"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(j.Skeleton,{className:"h-8 w-20"}),(0,a.jsx)(j.Skeleton,{className:"h-8 w-16"})]})]}),(0,a.jsx)(j.Skeleton,{className:"h-9 w-full"})]});function w(e){let{isOpen:t,onClose:s,threadId:j,projectId:w}=e,[N,y]=(0,n.useState)(null),[k,C]=(0,n.useState)(!1),[_,S]=(0,n.useState)(!1);(0,n.useEffect)(()=>{t&&(document.body.style.pointerEvents="auto")},[t]);let A=(0,v.eP)(),E=(0,v.sS)(),{data:R,isLoading:I}=(0,v.K2)(j||"");(0,n.useEffect)(()=>{(null==R?void 0:R.is_public)?y(D()):y(null)},[R]);let D=()=>j?"".concat("http://localhost:3000","/share/").concat(j):"",L=async()=>{if(j){C(!0);try{await T(!0);let e=D();y(e),p.oR.success("Shareable link created successfully")}catch(e){console.error("Error creating share link:",e),p.oR.error("Failed to create shareable link")}finally{C(!1)}}},P=async()=>{if(j){C(!0);try{await T(!1),y(null),p.oR.success("Shareable link removed")}catch(e){console.error("Error removing share link:",e),p.oR.error("Failed to remove shareable link")}finally{C(!1)}}},T=async e=>{console.log("Updating public status for thread:",j,"and project:",w,"to",e),j&&w&&(await E.mutateAsync({projectId:w,data:{is_public:e}}),await A.mutateAsync({threadId:j,data:{is_public:e}}))},O=[{name:"LinkedIn",icon:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"h-4 w-4",children:(0,a.jsx)("path",{fill:"currentColor",d:"M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433a2.062 2.062 0 01-2.063-2.065 2.064 2.064 0 112.063 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"})}),onClick:()=>{N&&window.open("https://www.linkedin.com/shareArticle?url=".concat(encodeURIComponent(N),"&text=Shared conversation"),"_blank")}},{name:"X",icon:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",className:"h-4 w-4",children:(0,a.jsx)("path",{fill:"currentColor",d:"M18.901 1.153h3.68l-8.04 9.19L24 22.846h-7.406l-5.8-7.584-6.638 7.584H.474l8.6-9.83L0 1.154h7.594l5.243 6.932ZM17.61 20.644h2.039L6.486 3.24H4.298Z"})}),onClick:()=>{N&&window.open("https://twitter.com/intent/tweet?url=".concat(encodeURIComponent(N),"&text=Shared conversation"),"_blank")}}];return(0,a.jsx)(r.lG,{open:t,onOpenChange:s,children:(0,a.jsxs)(r.Cf,{className:"sm:max-w-md",children:[(0,a.jsx)(r.c7,{children:(0,a.jsxs)(r.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(d.A,{className:"h-5 w-5"}),"Share Chat"]})}),(0,a.jsx)("div",{className:"space-y-6",children:I?(0,a.jsx)(b,{}):N?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(c.Fc,{children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsx)(c.TN,{children:"This chat is publicly accessible. Anyone with the link can view this conversation."})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.Label,{htmlFor:"share-link",children:"Share link"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(i.p,{id:"share-link",value:N,readOnly:!0,className:"font-mono text-sm"}),(0,a.jsxs)(l.$,{variant:"outline",size:"icon",onClick:()=>{N&&(S(!0),navigator.clipboard.writeText(N),p.oR.success("Link copied to clipboard"),setTimeout(()=>{S(!1)},500))},disabled:_,children:[_?(0,a.jsx)(m.A,{className:"h-4 w-4"}):(0,a.jsx)(x.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Copy link"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(o.Label,{children:"Share on social"}),(0,a.jsx)("div",{className:"flex space-x-2",children:O.map((e,t)=>(0,a.jsxs)(l.$,{variant:"outline",size:"sm",onClick:e.onClick,className:"flex items-center",children:[e.icon,(0,a.jsx)("span",{children:e.name})]},t))})]}),(0,a.jsxs)(l.$,{variant:"outline",className:"w-full text-destructive hover:bg-destructive hover:text-muted",onClick:P,disabled:k,children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),k?"Removing...":"Remove link"]})]}):(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)("div",{className:"mx-auto w-12 h-12 bg-muted-foreground/20 rounded-full flex items-center justify-center",children:(0,a.jsx)(d.A,{className:"h-6 w-6"})}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold",children:"Share this chat"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Create a shareable link that allows others to view this conversation publicly."})]}),(0,a.jsx)(l.$,{onClick:L,disabled:k,className:"w-full",children:k?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"h-4 w-4 animate-spin"}),"Creating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"h-4 w-4"}),"Create shareable link"]})})]})})]})})}},52695:(e,t,s)=>{"use strict";s.d(t,{Bx:()=>k,Yv:()=>R,CG:()=>E,Cn:()=>I,jj:()=>D,Gh:()=>A,sF:()=>S,wZ:()=>L,Uj:()=>O,FX:()=>P,GB:()=>y,jM:()=>_,x2:()=>C,cL:()=>N});var a=s(95155),n=s(12115),l=s(99708),r=s(74466),i=s(22432),o=s(40416),c=s(59434),d=s(30285);s(62523),s(22346);var u=s(15452),m=s(54416);function x(e){let{...t}=e;return(0,a.jsx)(u.bL,{"data-slot":"sheet",...t})}function h(e){let{...t}=e;return(0,a.jsx)(u.ZL,{"data-slot":"sheet-portal",...t})}function g(e){let{className:t,...s}=e;return(0,a.jsx)(u.hJ,{"data-slot":"sheet-overlay",className:(0,c.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...s})}function f(e){let{className:t,children:s,side:n="right",...l}=e;return(0,a.jsxs)(h,{children:[(0,a.jsx)(g,{}),(0,a.jsxs)(u.UC,{"data-slot":"sheet-content",className:(0,c.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===n&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===n&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===n&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===n&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...l,children:[s,(0,a.jsxs)(u.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,a.jsx)(m.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function p(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"sheet-header",className:(0,c.cn)("flex flex-col gap-1.5 p-4",t),...s})}function v(e){let{className:t,...s}=e;return(0,a.jsx)(u.hE,{"data-slot":"sheet-title",className:(0,c.cn)("text-foreground font-semibold",t),...s})}function j(e){let{className:t,...s}=e;return(0,a.jsx)(u.VY,{"data-slot":"sheet-description",className:(0,c.cn)("text-muted-foreground text-sm",t),...s})}s(68856);var b=s(46102);let w=n.createContext(null);function N(){let e=n.useContext(w);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}function y(e){let{defaultOpen:t=!0,open:s,onOpenChange:l,className:r,style:i,children:d,...u}=e,m=(0,o.a)(),[x,h]=n.useState(!1),[g,f]=n.useState(t),p=null!=s?s:g,v=n.useCallback(e=>{let t="function"==typeof e?e(p):e;l?l(t):f(t),document.cookie="".concat("sidebar_state","=").concat(t,"; path=/; max-age=").concat(604800)},[l,p]),j=n.useCallback(()=>m?h(e=>!e):v(e=>!e),[m,v,h]);n.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),j())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[j]);let N=p?"expanded":"collapsed",y=n.useMemo(()=>({state:N,open:p,setOpen:v,isMobile:m,openMobile:x,setOpenMobile:h,toggleSidebar:j}),[N,p,v,m,x,h,j]);return(0,a.jsx)(w.Provider,{value:y,children:(0,a.jsx)(b.Bc,{delayDuration:0,children:(0,a.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...i},className:(0,c.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",r),...u,children:d})})})}function k(e){let{side:t="left",variant:s="sidebar",collapsible:n="offcanvas",className:l,children:r,...i}=e,{isMobile:o,state:d,openMobile:u,setOpenMobile:m}=N();return"none"===n?(0,a.jsx)("div",{"data-slot":"sidebar",className:(0,c.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",l),...i,children:r}):o?(0,a.jsx)(x,{open:u,onOpenChange:m,...i,children:(0,a.jsxs)(f,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:t,children:[(0,a.jsxs)(p,{className:"sr-only",children:[(0,a.jsx)(v,{children:"Sidebar"}),(0,a.jsx)(j,{children:"Displays the mobile sidebar."})]}),(0,a.jsx)("div",{className:"flex h-full w-full flex-col",children:r})]})}):(0,a.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":d,"data-collapsible":"collapsed"===d?n:"","data-variant":s,"data-side":t,"data-slot":"sidebar",children:[(0,a.jsx)("div",{"data-slot":"sidebar-gap",className:(0,c.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===s||"inset"===s?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,a.jsx)("div",{"data-slot":"sidebar-container",className:(0,c.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===t?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===s||"inset"===s?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",l),...i,children:(0,a.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:r})})]})}function C(e){let{className:t,onClick:s,...n}=e,{toggleSidebar:l}=N();return(0,a.jsxs)(d.$,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:(0,c.cn)("size-7",t),onClick:e=>{null==s||s(e),l()},...n,children:[(0,a.jsx)(i.A,{}),(0,a.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function _(e){let{className:t,...s}=e,{toggleSidebar:n}=N();return(0,a.jsx)("button",{"data-sidebar":"rail","data-slot":"sidebar-rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:n,title:"Toggle Sidebar",className:(0,c.cn)("hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex","in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full","[[data-side=left][data-collapsible=offcanvas]_&]:-right-2","[[data-side=right][data-collapsible=offcanvas]_&]:-left-2",t),...s})}function S(e){let{className:t,...s}=e;return(0,a.jsx)("main",{"data-slot":"sidebar-inset",className:(0,c.cn)("bg-background relative flex w-full flex-1 flex-col","md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2",t),...s})}function A(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:(0,c.cn)("flex flex-col gap-2 p-2",t),...s})}function E(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:(0,c.cn)("flex flex-col gap-2 p-2",t),...s})}function R(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,c.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",t),...s})}function I(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,c.cn)("relative flex w-full min-w-0 flex-col p-2",t),...s})}function D(e){let{className:t,asChild:s=!1,...n}=e,r=s?l.DX:"div";return(0,a.jsx)(r,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,c.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",t),...n})}function L(e){let{className:t,...s}=e;return(0,a.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,c.cn)("flex w-full min-w-0 flex-col gap-1",t),...s})}function P(e){let{className:t,...s}=e;return(0,a.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,c.cn)("group/menu-item relative",t),...s})}let T=(0,r.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function O(e){let{asChild:t=!1,isActive:s=!1,variant:n="default",size:r="default",tooltip:i,className:o,...d}=e,u=t?l.DX:"button",{isMobile:m,state:x}=N(),h=(0,a.jsx)(u,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":r,"data-active":s,className:(0,c.cn)(T({variant:n,size:r}),o),...d});return i?("string"==typeof i&&(i={children:i}),(0,a.jsxs)(b.m_,{children:[(0,a.jsx)(b.k$,{asChild:!0,children:h}),(0,a.jsx)(b.ZI,{side:"right",align:"center",hidden:"collapsed"!==x||m,...i})]})):h}},61446:(e,t,s)=>{"use strict";s.d(t,{i:()=>o});var a=s(95155),n=s(1243),l=s(54416),r=s(30285),i=s(35695);function o(e){let{message:t,currentUsage:s,limit:o,accountId:c,onDismiss:d,isOpen:u}=e,m=(0,i.useRouter)();return u?(0,a.jsx)("div",{className:"fixed bottom-4 right-4 z-[9999]",children:(0,a.jsx)("div",{className:"bg-destructive/15 backdrop-blur-sm border border-destructive/30 rounded-lg p-5 shadow-lg max-w-md",children:(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[(0,a.jsx)("div",{className:"flex-shrink-0 bg-destructive/20 p-2 rounded-full",children:(0,a.jsx)(n.A,{className:"h-5 w-5 text-destructive"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsx)("h3",{className:"text-sm font-semibold text-destructive",children:"Usage Limit Reached"}),(0,a.jsx)(r.$,{variant:"ghost",size:"icon",onClick:d,className:"h-6 w-6 p-0 text-muted-foreground hover:text-foreground",children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-3",children:t}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(r.$,{variant:"outline",size:"sm",onClick:d,className:"text-xs",children:"Dismiss"}),(0,a.jsx)(r.$,{size:"sm",onClick:()=>m.push("/settings/billing?accountId=".concat(c)),className:"text-xs bg-destructive hover:bg-destructive/90",children:"Upgrade Plan"})]})]})]})})}):null}},90114:(e,t,s)=>{Promise.resolve().then(s.bind(s,25377))}},e=>{var t=t=>e(e.s=t);e.O(0,[825,2362,6711,2084,6079,3558,2969,1935,6671,3860,1171,8341,7201,5061,6165,9001,7453,2103,9879,5653,6766,9855,2473,6915,539,4612,5571,7351,6686,937,3685,8222,7379,4962,6528,9713,2814,5039,6955,8441,1684,7358],()=>t(90114)),_N_E=e.O()}]);