(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8844],{14636:(e,t,s)=>{"use strict";s.d(t,{AM:()=>a,Wv:()=>i,hl:()=>l});var n=s(95155);s(12115);var o=s(20547),r=s(59434);function a(e){let{...t}=e;return(0,n.jsx)(o.bL,{"data-slot":"popover",...t})}function i(e){let{...t}=e;return(0,n.jsx)(o.l9,{"data-slot":"popover-trigger",...t})}function l(e){let{className:t,align:s="center",sideOffset:a=4,...i}=e;return(0,n.jsx)(o.ZL,{children:(0,n.jsx)(o.UC,{"data-slot":"popover-content",align:s,sideOffset:a,className:(0,r.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",t),...i})})}},30285:(e,t,s)=>{"use strict";s.d(t,{$:()=>l,r:()=>i});var n=s(95155);s(12115);var o=s(99708),r=s(74466),a=s(59434);let i=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-xl text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",node_outline:"bg-transparent border border-primary/10",node_secondary:"px-0 bg-transparent hover:opacity-60",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-lg gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-lg px-6 has-[>svg]:px-4",icon:"size-9",node_secondary:"px-0"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:s,size:r,asChild:l=!1,...c}=e,d=l?o.DX:"button";return(0,n.jsx)(d,{"data-slot":"button",className:(0,a.cn)(i({variant:s,size:r,className:t})),...c})}},33423:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>M});var n=s(95155),o=s(12115),r=s(35695),a=s(35169),i=s(2775),l=s(381),c=s(4229),d=s(30285),u=s(62523),h=s(85057),m=s(88539),p=s(14636),f=s(56671),g=s(36742),x=s(28755),v=s(52643),w=s(17711);let b=async e=>{let t,s,n;if(!await (0,w.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let o=(0,v.U)(),{data:{session:r}}=await o.auth.getSession();if(!r)throw Error("You must be logged in to get agent tools");let a=await fetch("".concat("http://localhost:8000/api","/agents/").concat(e),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r.access_token)}});if(!a.ok)throw Error((await a.json().catch(()=>({detail:"Unknown error"}))).detail||"HTTP ".concat(a.status,": ").concat(a.statusText));let i=await a.json();i.current_version?(t=i.current_version.agentpress_tools||{},s=i.current_version.configured_mcps||[],n=i.current_version.custom_mcps||[]):(t=i.agentpress_tools||{},s=i.configured_mcps||[],n=i.custom_mcps||[]);let l=[],c={sb_shell_tool:{description:"Execute shell commands in tmux sessions",icon:"\uD83D\uDCBB",color:"bg-slate-100"},sb_files_tool:{description:"Create, read, update, and delete files",icon:"\uD83D\uDCC1",color:"bg-blue-100"},sb_browser_tool:{description:"Browser automation and web navigation",icon:"\uD83C\uDF10",color:"bg-indigo-100"},sb_deploy_tool:{description:"Deploy applications and services",icon:"\uD83D\uDE80",color:"bg-green-100"},sb_expose_tool:{description:"Expose services and manage ports",icon:"\uD83D\uDD0C",color:"bg-orange-100"},web_search_tool:{description:"Search the web using Tavily API",icon:"\uD83D\uDD0D",color:"bg-yellow-100"},sb_vision_tool:{description:"Vision and image processing capabilities",icon:"\uD83D\uDC41️",color:"bg-pink-100"},data_providers_tool:{description:"Access to data providers and external APIs",icon:"\uD83D\uDD17",color:"bg-cyan-100"}};for(let[e,s]of Object.entries(t)){let t=c[e];t&&s&&"object"==typeof s&&"enabled"in s&&l.push({name:e,description:t.description,type:"agentpress",enabled:!!s.enabled,icon:t.icon,color:t.color})}let d=[];for(let e of s)if(e.enabledTools&&Array.isArray(e.enabledTools))for(let t of e.enabledTools)d.push({name:t,description:"".concat(t," from ").concat(e.name),type:"mcp",server:e.name,enabled:!0,icon:"\uD83D\uDD27",color:"bg-purple-100"});for(let e of n)if(e.enabledTools&&Array.isArray(e.enabledTools))for(let t of e.enabledTools){let s=e.customType||e.type||"sse";d.push({name:t,description:"".concat(t," from ").concat(e.name," (").concat(s,")"),type:"mcp",server:e.name,enabled:!0,icon:"sse"===s?"\uD83C\uDF10":"http"===s?"\uD83D\uDCE1":"⚙️",color:"bg-indigo-100"})}return{agentpress_tools:l,mcp_tools:d}},j=e=>(0,x.I)({queryKey:["agent-tools",e],queryFn:()=>b(e),staleTime:3e5,enabled:!!e});var y=s(1243),k=s(84616),N=s(10081),_=s(5196),E=s(5623),C=s(62525),A=s(56542),T=s(47924),S=s(59434);function I(e){let{className:t,...s}=e;return(0,n.jsx)(A.uB,{"data-slot":"command",className:(0,S.cn)("bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md",t),...s})}function z(e){let{className:t,...s}=e;return(0,n.jsxs)("div",{"data-slot":"command-input-wrapper",className:"flex h-9 items-center gap-2 border-b px-3",children:[(0,n.jsx)(T.A,{className:"size-4 shrink-0 opacity-50"}),(0,n.jsx)(A.uB.Input,{"data-slot":"command-input",className:(0,S.cn)("placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50",t),...s})]})}function P(e){let{className:t,...s}=e;return(0,n.jsx)(A.uB.List,{"data-slot":"command-list",className:(0,S.cn)("max-h-[300px] scroll-py-1 overflow-x-hidden overflow-y-auto",t),...s})}function L(e){let{...t}=e;return(0,n.jsx)(A.uB.Empty,{"data-slot":"command-empty",className:"py-6 text-center text-sm",...t})}function U(e){let{className:t,...s}=e;return(0,n.jsx)(A.uB.Group,{"data-slot":"command-group",className:(0,S.cn)("text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium",t),...s})}function B(e){let{className:t,...s}=e;return(0,n.jsx)(A.uB.Item,{"data-slot":"command-item",className:(0,S.cn)("data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...s})}s(54165);let D=(e,t)=>"agentpress"===t?({sb_shell_tool:"Shell Tool",sb_files_tool:"Files Tool",sb_browser_tool:"Browser Tool",sb_deploy_tool:"Deploy Tool",sb_expose_tool:"Expose Tool",web_search_tool:"Web Search",sb_vision_tool:"Vision Tool",data_providers_tool:"Data Providers"})[e]||e:e.split("_").map(e=>e.charAt(0).toUpperCase()+e.slice(1).toLowerCase()).join(" ");function W(e){let{steps:t,onStepsChange:s,agentTools:r,isLoadingTools:a}=e,[i,l]=(0,o.useState)({}),[c,m]=(0,o.useState)({});t.forEach((e,t)=>{var s,n;console.log("Step ".concat(t,":"),{name:e.name,type:e.type,hasChildren:!!e.children,childrenCount:(null==(s=e.children)?void 0:s.length)||0,children:null==(n=e.children)?void 0:n.map(e=>({name:e.name,type:e.type}))})});let f=()=>Math.random().toString(36).substr(2,9),g=(0,o.useCallback)((e,n)=>{let o={id:f(),name:"Step",description:"",type:"instruction",config:{},order:0,enabled:!0},r=t=>{if(!e){if(n){let e=t.findIndex(e=>e.id===n);return[...t.slice(0,e+1),o,...t.slice(e+1)]}return[...t,o]}return t.map(t=>t.id===e?{...t,children:[...t.children||[],o]}:t.children?{...t,children:r(t.children)}:t)};s(r(t))},[t,s]),x=(0,o.useCallback)(e=>{let n={id:f(),name:"If",description:"",type:"condition",config:{},conditions:{type:"if",expression:""},children:[],order:0,enabled:!0,hasIssues:!0},o=t=>{let s=t.findIndex(t=>t.id===e);return -1!==s?[...t.slice(0,s+1),n,...t.slice(s+1)]:t.map(e=>e.children?{...e,children:o(e.children)}:e)};s(o(t))},[t,s]),v=(0,o.useCallback)(e=>{let n={id:f(),name:"Else If",description:"",type:"condition",config:{},conditions:{type:"elseif",expression:""},children:[],order:0,enabled:!0,hasIssues:!0},o=t=>{let s=t.findIndex(t=>t.id===e);return -1!==s?[...t.slice(0,s+1),n,...t.slice(s+1)]:t.map(e=>e.children?{...e,children:o(e.children)}:e)};s(o(t))},[t,s]),w=(0,o.useCallback)(e=>{let n={id:f(),name:"Else",description:"",type:"condition",config:{},conditions:{type:"else"},children:[],order:0,enabled:!0,hasIssues:!1},o=t=>{let s=t.findIndex(t=>t.id===e);return -1!==s?[...t.slice(0,s+1),n,...t.slice(s+1)]:t.map(e=>e.children?{...e,children:o(e.children)}:e)};s(o(t))},[t,s]),b=(0,o.useCallback)((e,n)=>{let o=t=>t.map(t=>{if(t.id===e){var s,r,a,i;let e={...t,...n};return"instruction"===e.type&&e.name&&"New Step"!==e.name||"condition"===e.type&&((null==(s=e.conditions)?void 0:s.type)==="if"||(null==(r=e.conditions)?void 0:r.type)==="elseif")&&(null==(a=e.conditions)?void 0:a.expression)?e.hasIssues=!1:"condition"===e.type&&(null==(i=e.conditions)?void 0:i.type)==="else"&&(e.hasIssues=!1),e}return t.children?{...t,children:o(t.children)}:t});s(o(t))},[t,s]),j=(0,o.useCallback)(e=>{let n=t=>t.filter(t=>t.id!==e).map(e=>e.children?{...e,children:n(e.children)}:e);s(n(t))},[t,s]),A=(0,o.useCallback)(function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:t,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{value:0};for(let t of s){if(n.value++,t.id===e)return n.value;if(t.children&&t.children.length>0){let s=A(e,t.children,n);if(s>0)return s}}return 0},[t]),T=e=>String.fromCharCode(65+e),W=(e,t)=>{var s,o,r,a;let i=c[t]||(null==(s=e[0])?void 0:s.id),l=e.find(e=>e.id===i)||e[0],p=e.some(e=>{var t;return(null==(t=e.conditions)?void 0:t.type)==="else"}),f=(s,n)=>{if("Backspace"===s.key||"Delete"===s.key){var o;if(s.preventDefault(),e.length>1&&(1!==e.length||(null==(o=n.conditions)?void 0:o.type)!=="if")){j(n.id);let s=e.filter(e=>e.id!==n.id);s.length>0&&m(e=>({...e,[t]:s[0].id}))}}};return(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[e.map((e,s)=>{var o,r,a;let l=T(s),c=e.id===i,d=(null==(o=e.conditions)?void 0:o.type)==="if"?"If":(null==(r=e.conditions)?void 0:r.type)==="elseif"?"Else If":(null==(a=e.conditions)?void 0:a.type)==="else"?"Else":"If";return(0,n.jsxs)("button",{onClick:()=>m(s=>({...s,[t]:e.id})),onKeyDown:t=>f(t,e),tabIndex:0,className:(0,S.cn)("flex items-center gap-2 px-3 py-2 rounded-md border text-sm font-medium transition-all",c?"bg-primary text-primary-foreground border-primary shadow-sm":"bg-background border-border text-foreground hover:bg-accent hover:text-accent-foreground"),children:[(0,n.jsx)("span",{className:"font-mono text-xs",children:l}),(0,n.jsx)("span",{children:"•"}),(0,n.jsx)("span",{children:d}),e.hasIssues&&(0,n.jsx)(y.A,{className:"h-3 w-3 text-destructive"})]},e.id)}),!p&&(0,n.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>v(e[e.length-1].id),className:"h-9 px-3 border-dashed text-xs",children:[(0,n.jsx)(k.A,{className:"h-3 w-3 mr-1"}),"Else If"]}),!p&&(0,n.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>w(e[e.length-1].id),className:"h-9 px-3 border-dashed text-xs",children:[(0,n.jsx)(k.A,{className:"h-3 w-3 mr-1"}),"Else"]})]}),l&&(0,n.jsxs)("div",{className:"bg-muted/50 rounded-lg p-4 border",children:[(null==(o=l.conditions)?void 0:o.type)==="if"||(null==(r=l.conditions)?void 0:r.type)==="elseif"?(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsx)(h.Label,{className:"text-sm font-medium",children:(null==(a=l.conditions)?void 0:a.type)==="if"?"Condition":"Else If Condition"}),(0,n.jsx)(u.p,{type:"text",value:l.conditions.expression||"",onChange:e=>b(l.id,{conditions:{...l.conditions,expression:e.target.value}}),placeholder:"e.g., user asks about pricing",className:"w-full bg-transparent text-sm px-3 py-2 rounded-md"})]}):(0,n.jsx)("div",{className:"text-sm text-muted-foreground font-medium",children:"Otherwise (fallback condition)"}),(0,n.jsxs)("div",{className:"mt-4 space-y-3",children:[l.children&&l.children.length>0&&(0,n.jsx)(n.Fragment,{children:l.children.map((e,t)=>O(e,t+1,!0,l.id))}),(0,n.jsx)("div",{className:"flex justify-center pt-2",children:(0,n.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>g(l.id),className:"border-dashed text-xs",children:[(0,n.jsx)(k.A,{className:"h-3 w-3"}),"Add step"]})})]})]})]})},O=function(e,t){arguments.length>2&&void 0!==arguments[2]&&arguments[2],arguments.length>3&&arguments[3];let s="condition"===e.type,o="sequence"===e.type;return s?null:(0,n.jsx)("div",{className:"group",children:(0,n.jsx)("div",{className:"bg-card rounded-lg border shadow-sm p-4 transition-shadow",children:(0,n.jsxs)("div",{className:"flex items-start gap-4",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 shrink-0",children:[e.hasIssues&&(0,n.jsx)(y.A,{className:"h-4 w-4 text-destructive"}),(0,n.jsx)("div",{className:"w-6 h-6 rounded-full bg-muted flex items-center justify-center text-sm font-medium text-muted-foreground",children:t})]}),(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsx)("div",{className:"flex items-center justify-between mb-3",children:o?(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("div",{className:"w-5 h-5 rounded bg-primary/10 flex items-center justify-center",children:(0,n.jsx)("div",{className:"w-2 h-2 rounded-full bg-primary"})}),(0,n.jsx)("span",{className:"text-base font-medium",children:e.description})]}):(0,n.jsx)("input",{type:"text",value:e.name+" "+t,onChange:t=>b(e.id,{name:t.target.value}),placeholder:"Step name",className:"w-full bg-transparent border-0 outline-none text-base font-medium placeholder:text-muted-foreground"})}),!o&&void 0!==e.description&&(0,n.jsx)("input",{type:"text",value:e.description,onChange:t=>b(e.id,{description:t.target.value}),placeholder:"Add a description",className:"-mt-2 w-full bg-transparent border-0 outline-none text-sm text-muted-foreground placeholder:text-muted-foreground mb-3"}),!o&&(0,n.jsxs)(p.AM,{open:i[e.id]||!1,onOpenChange:t=>l(s=>({...s,[e.id]:t})),children:[(0,n.jsx)(p.Wv,{asChild:!0,children:(0,n.jsxs)(d.$,{variant:"outline",role:"combobox","aria-expanded":i[e.id]||!1,className:"h-9 w-full justify-between text-sm",children:[e.config.tool_name?(0,n.jsx)("span",{className:"flex items-center gap-2 text-sm",children:(()=>{let t=null==r?void 0:r.agentpress_tools.find(t=>t.name===e.config.tool_name);if(t)return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("span",{children:t.icon||"\uD83D\uDD27"}),(0,n.jsx)("span",{children:D(t.name,"agentpress")})]});let s=null==r?void 0:r.mcp_tools.find(t=>"".concat(t.server,":").concat(t.name)===e.config.tool_name);return s?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("span",{children:s.icon||"\uD83D\uDD27"}),(0,n.jsx)("span",{children:D(s.name,"mcp")})]}):e.config.tool_name})()}):(0,n.jsx)("span",{className:"text-muted-foreground",children:"Select tool (optional)"}),(0,n.jsx)(N.A,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),(0,n.jsx)(p.hl,{className:"w-[320px] p-0",align:"start",children:(0,n.jsxs)(I,{children:[(0,n.jsx)(z,{placeholder:"Search tools...",className:"h-9"}),(0,n.jsx)(L,{children:"No tools found."}),(0,n.jsx)(P,{children:a?(0,n.jsx)(B,{disabled:!0,children:"Loading tools..."}):r?(0,n.jsxs)(n.Fragment,{children:[r.agentpress_tools.filter(e=>e.enabled).length>0&&(0,n.jsx)(U,{heading:"Default Tools",children:r.agentpress_tools.filter(e=>e.enabled).map(t=>(0,n.jsxs)(B,{value:"".concat(D(t.name,"agentpress")," ").concat(t.name),onSelect:()=>{b(e.id,{config:{...e.config,tool_name:t.name}}),l(t=>({...t,[e.id]:!1}))},children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{children:t.icon||"\uD83D\uDD27"}),(0,n.jsx)("span",{children:D(t.name,"agentpress")})]}),(0,n.jsx)(_.A,{className:(0,S.cn)("ml-auto h-4 w-4",e.config.tool_name===t.name?"opacity-100":"opacity-0")})]},t.name))}),r.mcp_tools.length>0&&(0,n.jsx)(U,{heading:"External Tools",children:r.mcp_tools.map(t=>(0,n.jsxs)(B,{value:"".concat(D(t.name,"mcp")," ").concat(t.name," ").concat(t.server||""),onSelect:()=>{b(e.id,{config:{...e.config,tool_name:t.server?"".concat(t.server,":").concat(t.name):t.name}}),l(t=>({...t,[e.id]:!1}))},children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)("span",{children:t.icon||"\uD83D\uDD27"}),(0,n.jsx)("span",{children:D(t.name,"mcp")})]}),(0,n.jsx)(_.A,{className:(0,S.cn)("ml-auto h-4 w-4",e.config.tool_name===(t.server?"".concat(t.server,":").concat(t.name):t.name)?"opacity-100":"opacity-0")})]},"".concat(t.server||"default","-").concat(t.name)))})]}):(0,n.jsx)(B,{disabled:!0,children:"Failed to load tools"})})]})})]}),e.children&&e.children.length>0&&(0,n.jsx)("div",{className:"mt-4 space-y-4",children:e.children.map((t,s)=>O(t,s+1,!0,e.id))})]}),(0,n.jsxs)(p.AM,{children:[(0,n.jsx)(p.Wv,{asChild:!0,children:(0,n.jsx)(d.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,n.jsx)(E.A,{className:"h-4 w-4"})})}),(0,n.jsx)(p.hl,{className:"w-48 p-1",align:"end",children:(0,n.jsxs)(d.$,{variant:"ghost",size:"sm",onClick:()=>j(e.id),className:"w-full justify-start text-destructive hover:text-destructive hover:bg-destructive/10",children:[(0,n.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"Delete step"]})})]})]})})},e.id)};return(0,n.jsx)("div",{className:"space-y-6 max-w-4xl",children:0===t.length?(0,n.jsxs)("div",{className:"text-center py-16",children:[(0,n.jsx)("div",{className:"w-16 h-16 bg-muted rounded-2xl flex items-center justify-center mx-auto mb-4",children:(0,n.jsx)(k.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,n.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Start building your workflow"}),(0,n.jsx)("p",{className:"text-muted-foreground mb-6 max-w-md mx-auto",children:"Add steps and conditions to create a smart workflow that adapts to different scenarios."}),(0,n.jsxs)(d.$,{onClick:()=>g(),children:[(0,n.jsx)(k.A,{className:"h-4 w-4"}),"Add step"]})]}):(0,n.jsxs)("div",{className:"space-y-6",children:[(()=>{let e=[],s=0,o=0;for(;o<t.length;){let r=t[o];if("condition"===r.type){let r=[];for(;o<t.length&&"condition"===t[o].type;)r.push(t[o]),o++;s++,e.push((0,n.jsx)("div",{className:"bg-card rounded-lg border shadow-sm p-4 transition-shadow",children:(0,n.jsxs)("div",{className:"flex items-start gap-4",children:[(0,n.jsx)("div",{className:"flex items-center gap-2 shrink-0",children:(0,n.jsx)("div",{className:"w-6 h-6 rounded-full bg-muted flex items-center justify-center text-sm font-medium text-muted-foreground",children:s})}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsx)("div",{className:"text-base font-medium mb-4",children:"Add rule"}),W(r,r[0].id)]})]})},r[0].id))}else s++,e.push(O(r,s,!1)),o++}return e})(),(0,n.jsx)("div",{className:"flex justify-center pt-4",children:(0,n.jsxs)("div",{className:"flex gap-3",children:[(0,n.jsxs)(d.$,{variant:"outline",onClick:()=>g(),className:"border-dashed",children:[(0,n.jsx)(k.A,{className:"h-4 w-4"}),"Add step"]}),(0,n.jsxs)(d.$,{variant:"outline",onClick:()=>{var e;return x((null==(e=t[t.length-1])?void 0:e.id)||"")},className:"border-dashed",children:[(0,n.jsx)(k.A,{className:"h-4 w-4"}),"Add rule"]})]})})]})})}let O=e=>{let t=1,s=e=>e.map(e=>{let n={id:e.id,name:e.name,description:e.description,type:e.type,config:e.config,order:t++};return"condition"===e.type&&e.conditions&&(n.conditions=e.conditions),e.children&&e.children.length>0&&(n.children=s(e.children)),n});return s(e)},$=e=>{if(!e||0===e.length)return[];let t=e=>e.map(e=>{let s={id:e.id||Math.random().toString(36).substr(2,9),name:e.name,description:e.description||"",type:e.type||"instruction",config:e.config||{},order:e.order||e.step_order||0,enabled:!1!==e.enabled,hasIssues:e.hasIssues||!1};return"condition"===e.type&&e.conditions&&(s.conditions=e.conditions),e.children&&Array.isArray(e.children)&&e.children.length>0?s.children=t(e.children):s.children=[],s});return t(e)},F=e=>{if(!e||0===e.length)return[];let t=[],s=new Map;for(let t of e)if("condition"===t.type){let e={id:t.id||Math.random().toString(36).substr(2,9),name:t.name,description:t.description||"",type:"condition",config:t.config||{},conditions:t.conditions,order:t.order||t.step_order,children:[]};s.set(e.id,e)}for(let t of e)if("condition"!==t.type&&t.conditions){for(let[e,n]of s)if(JSON.stringify(n.conditions)===JSON.stringify(t.conditions)){let e={id:t.id||Math.random().toString(36).substr(2,9),name:t.name,description:t.description||"",type:t.type||"instruction",config:t.config||{},order:t.order||t.step_order,children:[]};n.children.push(e);break}}let n=[...e].sort((e,t)=>(e.order||e.step_order||0)-(t.order||t.step_order||0)),o=0;for(;o<n.length;){let e=n[o];if("condition"===e.type){let e=[];for(;o<n.length&&"condition"===n[o].type;){let t=s.get(n[o].id);t&&e.push(t),o++}e.sort((e,t)=>{var s,n;let o={if:0,elseif:1,else:2};return(o[null==(s=e.conditions)?void 0:s.type]||0)-(o[null==(n=t.conditions)?void 0:n.type]||0)}),t.push(...e)}else if(e.conditions)o++;else{let s={id:e.id||Math.random().toString(36).substr(2,9),name:e.name,description:e.description||"",type:e.type||"instruction",config:e.config||{},order:e.order||e.step_order,children:[]};t.push(s),o++}}return t};function M(){let e=(0,r.useParams)(),t=(0,r.useRouter)(),s=e.agentId,x=e.workflowId,{data:v=[],isLoading:w}=(0,g.X3)(s),b=(0,g.Ci)(),y=(0,g.Vb)(),{data:k,isLoading:N}=j(s),_=!!x,[E,C]=(0,o.useState)(""),[A,T]=(0,o.useState)(""),[S,I]=(0,o.useState)(""),[z,P]=(0,o.useState)(!1),[L,U]=(0,o.useState)([]),[B,D]=(0,o.useState)(!1),[M,R]=(0,o.useState)(_);(0,o.useEffect)(()=>{if(_&&v.length>0){let e=v.find(e=>e.id===x);if(e){let t;C(e.name),T(e.description||""),I(e.trigger_phrase||""),P(e.is_default);try{t=e.steps.some(e=>e.children&&Array.isArray(e.children))?$(e.steps):F(e.steps)}catch(s){console.warn("Error reconstructing workflow steps, using fallback:",s),t=F(e.steps)}U(t),R(!1)}else w||f.oR.error("Workflow not found")}else _||R(!1)},[_,v,x,w,t,s]);let q=(0,o.useCallback)(async()=>{if(!E.trim())return void f.oR.error("Please enter a workflow name");let e=O(L);try{_?(await y.mutateAsync({agentId:s,workflowId:x,workflow:{name:E,description:A,trigger_phrase:S||void 0,is_default:z,steps:e}}),f.oR.success("Workflow updated successfully")):(await b.mutateAsync({agentId:s,workflow:{name:E,description:A,trigger_phrase:S||void 0,is_default:z,steps:e}}),f.oR.success("Workflow created successfully"))}catch(e){f.oR.error("Failed to ".concat(_?"update":"create"," workflow"))}},[E,A,S,z,L,s,x,_,b,y,t]);return M||w?(0,n.jsx)("div",{className:"h-screen flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"}),(0,n.jsx)("p",{children:"Loading workflow..."})]})}):(0,n.jsxs)("div",{className:"h-screen flex flex-col bg-background",children:[(0,n.jsxs)("div",{className:"border-b bg-card px-2 py-4 flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-4",children:[(0,n.jsx)(d.$,{variant:"ghost",size:"icon",onClick:()=>t.back(),className:"h-8 w-8",children:(0,n.jsx)(a.A,{className:"h-4 w-4"})}),(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsx)("div",{className:"w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center",children:(0,n.jsx)(i.A,{className:"h-4 w-4 text-primary"})}),(0,n.jsx)("div",{children:(0,n.jsx)("h1",{className:"text-lg font-semibold",children:E||"Untitled Workflow"})}),(0,n.jsxs)(p.AM,{open:B,onOpenChange:D,children:[(0,n.jsx)(p.Wv,{asChild:!0,children:(0,n.jsx)(d.$,{variant:"ghost",size:"icon",className:"h-8 w-8 ml-2",children:(0,n.jsx)(l.A,{className:"h-4 w-4"})})}),(0,n.jsx)(p.hl,{className:"w-96",align:"start",children:(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsx)("div",{children:(0,n.jsx)("h3",{className:"font-medium text-sm mb-3",children:"Workflow Settings"})}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(h.Label,{className:"text-xs font-medium",children:"Name"}),(0,n.jsx)(u.p,{value:E,onChange:e=>C(e.target.value),placeholder:"Enter workflow name",className:"h-8"})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)(h.Label,{className:"text-xs font-medium",children:"Description"}),(0,n.jsx)(m.T,{value:A,onChange:e=>T(e.target.value),placeholder:"Describe what this workflow does",rows:3,className:"resize-none text-sm"})]}),(0,n.jsx)(d.$,{onClick:()=>D(!1),className:"w-full h-8",size:"sm",children:"Done"})]})})]})]})]}),(0,n.jsxs)(d.$,{onClick:q,disabled:b.isPending||y.isPending,size:"sm",className:"h-8",children:[(0,n.jsx)(c.A,{className:"h-3.5 w-3.5"}),b.isPending||y.isPending?"Saving...":"Save Workflow"]})]}),(0,n.jsx)("div",{className:"flex-1 overflow-auto",children:(0,n.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,n.jsx)("div",{className:"p-6",children:(0,n.jsx)(W,{steps:L,onStepsChange:U,agentTools:k,isLoadingTools:N})})})})]})}},36742:(e,t,s)=>{"use strict";s.d(t,{X3:()=>f,Ci:()=>g,Fo:()=>v,AM:()=>w,Vb:()=>x});var n=s(99090),o=s(26715),r=s(56671);let a={all:["agent-workflows"],agent:e=>[...a.all,e],workflow:(e,t)=>[...a.agent(e),t],executions:(e,t)=>[...a.workflow(e,t),"executions"]};var i=s(52643),l=s(17711);let c="http://localhost:8000/api",d=async e=>{try{if(!await (0,l.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let t=(0,i.U)(),{data:{session:s}}=await t.auth.getSession();if(!s)throw Error("You must be logged in to get workflows");let n=await fetch("".concat(c,"/agents/").concat(e,"/workflows"),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s.access_token)}});if(!n.ok){let e=await n.json().catch(()=>({detail:"Unknown error"}));throw Error(e.detail||"HTTP ".concat(n.status,": ").concat(n.statusText))}let o=await n.json();return console.log("[API] Fetched workflows for agent:",e,o.length),o}catch(e){throw console.error("Error fetching workflows:",e),e}},u=async(e,t)=>{try{if(!await (0,l.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let s=(0,i.U)(),{data:{session:n}}=await s.auth.getSession();if(!n)throw Error("You must be logged in to create a workflow");let o=await fetch("".concat(c,"/agents/").concat(e,"/workflows"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(n.access_token)},body:JSON.stringify(t)});if(!o.ok){let e=await o.json().catch(()=>({detail:"Unknown error"}));throw Error(e.detail||"HTTP ".concat(o.status,": ").concat(o.statusText))}let r=await o.json();return console.log("[API] Created workflow:",r.id),r}catch(e){throw console.error("Error creating workflow:",e),e}},h=async(e,t,s)=>{try{if(console.log("[API] Updating workflow:",s),!await (0,l.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let n=(0,i.U)(),{data:{session:o}}=await n.auth.getSession();if(!o)throw Error("You must be logged in to update a workflow");let r=await fetch("".concat(c,"/agents/").concat(e,"/workflows/").concat(t),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(o.access_token)},body:JSON.stringify(s)});if(!r.ok){let e=await r.json().catch(()=>({detail:"Unknown error"}));throw Error(e.detail||"HTTP ".concat(r.status,": ").concat(r.statusText))}let a=await r.json();return console.log("[API] Updated workflow:",a.id),a}catch(e){throw console.error("Error updating workflow:",e),e}},m=async(e,t)=>{try{if(!await (0,l.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let s=(0,i.U)(),{data:{session:n}}=await s.auth.getSession();if(!n)throw Error("You must be logged in to delete a workflow");let o=await fetch("".concat(c,"/agents/").concat(e,"/workflows/").concat(t),{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(n.access_token)}});if(!o.ok){let e=await o.json().catch(()=>({detail:"Unknown error"}));throw Error(e.detail||"HTTP ".concat(o.status,": ").concat(o.statusText))}console.log("[API] Deleted workflow:",t)}catch(e){throw console.error("Error deleting workflow:",e),e}},p=async(e,t,s)=>{try{if(!await (0,l.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let n=(0,i.U)(),{data:{session:o}}=await n.auth.getSession();if(!o)throw Error("You must be logged in to execute a workflow");let r=await fetch("".concat(c,"/agents/").concat(e,"/workflows/").concat(t,"/execute"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(o.access_token)},body:JSON.stringify(s)});if(!r.ok){let e=await r.json().catch(()=>({detail:"Unknown error"}));throw Error(e.detail||"HTTP ".concat(r.status,": ").concat(r.statusText))}let a=await r.json();return console.log("[API] Executed workflow:",t,"execution:",a.execution_id),a}catch(e){throw console.error("Error executing workflow:",e),e}},f=e=>(0,n.GQ)(a.agent(e),()=>d(e),{enabled:!!e,staleTime:3e4})(),g=()=>{let e=(0,o.jE)();return(0,n.Lx)(e=>{let{agentId:t,workflow:s}=e;return u(t,s)},{onSuccess:(t,s)=>{e.invalidateQueries({queryKey:a.agent(s.agentId)}),r.oR.success("Workflow created successfully")}})()},x=()=>{let e=(0,o.jE)();return(0,n.Lx)(e=>{let{agentId:t,workflowId:s,workflow:n}=e;return h(t,s,n)},{onSuccess:(t,s)=>{e.invalidateQueries({queryKey:a.agent(s.agentId)}),r.oR.success("Workflow updated successfully")}})()},v=()=>{let e=(0,o.jE)();return(0,n.Lx)(e=>{let{agentId:t,workflowId:s}=e;return m(t,s)},{onSuccess:(t,s)=>{e.invalidateQueries({queryKey:a.agent(s.agentId)}),r.oR.success("Workflow deleted successfully")}})()},w=()=>{let e=(0,o.jE)();return(0,n.Lx)(e=>{let{agentId:t,workflowId:s,execution:n}=e;return p(t,s,n)},{onSuccess:(t,s)=>{e.invalidateQueries({queryKey:a.executions(s.agentId,s.workflowId)}),r.oR.success("Workflow execution started")}})()}},62964:(e,t,s)=>{Promise.resolve().then(s.bind(s,33423))},88539:(e,t,s)=>{"use strict";s.d(t,{T:()=>r});var n=s(95155);s(12115);var o=s(59434);function r(e){let{className:t,...s}=e;return(0,n.jsx)("textarea",{"data-slot":"textarea",className:(0,o.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...s})}},99090:(e,t,s)=>{"use strict";s.d(t,{DY:()=>a,GQ:()=>i,Lx:()=>l});var n=s(28755),o=s(5041),r=s(33356);let a=e=>e;function i(e,t,s){return o=>(0,n.I)({queryKey:e,queryFn:t,...s,...o})}function l(e,t){return s=>{let{errorContext:n,...a}=t||{},{errorContext:i,...l}=s||{};return(0,o.n)({mutationFn:e,onError:(e,t,s)=>{var o,c;(null==l?void 0:l.onError)||(null==a?void 0:a.onError)||(0,r.hS)(e,i||n),null==a||null==(o=a.onError)||o.call(a,e,t,s),null==l||null==(c=l.onError)||c.call(l,e,t,s)},...a,...l})}}}},e=>{var t=t=>e(e.s=t);e.O(0,[2969,1935,6671,3860,1171,8341,7201,5061,9001,5106,6686,4969,8441,1684,7358],()=>t(62964)),_N_E=e.O()}]);