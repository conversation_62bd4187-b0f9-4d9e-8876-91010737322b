(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4790],{6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>a,t:()=>s});var n=r(12115);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let r=!1,n=e.map(e=>{let n=i(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():i(e[t],null)}}}}function a(...e){return n.useCallback(s(...e),e)}},19946:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(12115);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),s=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:i=24,strokeWidth:l=2,absoluteStrokeWidth:o,className:d="",children:c,iconNode:u,...f}=e;return(0,n.createElement)("svg",{ref:t,...a,width:i,height:i,stroke:r,strokeWidth:o?24*Number(l)/Number(i):l,className:s("lucide",d),...f},[...u.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),o=(e,t)=>{let r=(0,n.forwardRef)((r,a)=>{let{className:o,...d}=r;return(0,n.createElement)(l,{ref:a,iconNode:t,className:s("lucide-".concat(i(e)),o),...d})});return r.displayName="".concat(e),r}},39076:(e,t,r)=>{"use strict";r.d(t,{Navbar:()=>R});var n=r(95155),i=r(68222),s=r(95653),a=r(12115);let l=i.CQ.nav.links;function o(){let e=(0,a.useRef)(null),[t,r]=(0,a.useState)(0),[i,o]=(0,a.useState)(0),[d,c]=(0,a.useState)(!1),[u,f]=(0,a.useState)("hero"),[x,m]=(0,a.useState)(!1);a.useEffect(()=>{var t,n;let i=null==(n=e.current)||null==(t=n.querySelector('[href="#'.concat(l[0].href.substring(1),'"]')))?void 0:t.parentElement;if(i){let e=i.getBoundingClientRect();r(i.offsetLeft),o(e.width),c(!0)}},[]),a.useEffect(()=>{let t=()=>{var t,n;if(x)return;let i=l.map(e=>e.href.substring(1)),s=i[0],a=1/0;for(let e of i){let t=document.getElementById(e);if(t){let r=Math.abs(t.getBoundingClientRect().top-100);r<a&&(a=r,s=e)}}f(s);let d=null==(n=e.current)||null==(t=n.querySelector('[href="#'.concat(s,'"]')))?void 0:t.parentElement;if(d){let e=d.getBoundingClientRect();r(d.offsetLeft),o(e.width)}};return window.addEventListener("scroll",t),t(),()=>window.removeEventListener("scroll",t)},[x]);let h=(e,t)=>{e.preventDefault();let n=t.href.substring(1),i=document.getElementById(n);if(i){m(!0),f(n);let t=e.currentTarget.parentElement;if(t){let e=t.getBoundingClientRect();r(t.offsetLeft),o(e.width)}let s=i.getBoundingClientRect().top+window.pageYOffset-100;window.scrollTo({top:s,behavior:"smooth"}),setTimeout(()=>{m(!1)},500)}};return(0,n.jsx)("div",{className:"w-full hidden md:block",children:(0,n.jsxs)("ul",{className:"relative mx-auto flex w-fit rounded-full h-11 px-2 items-center justify-center",ref:e,children:[l.map(e=>(0,n.jsx)("li",{className:"z-10 cursor-pointer h-full flex items-center justify-center px-4 py-2 text-sm font-medium transition-colors duration-200 ".concat(u===e.href.substring(1)?"text-primary":"text-primary/60 hover:text-primary"," tracking-tight"),children:(0,n.jsx)("a",{href:e.href,onClick:t=>h(t,e),children:e.name})},e.name)),d&&(0,n.jsx)(s.P.li,{animate:{left:t,width:i},transition:{type:"spring",stiffness:400,damping:30},className:"absolute inset-0 my-1.5 rounded-full bg-accent/60 border border-border"})]})})}var d=r(62098),c=r(93509),u=r(51362),f=r(99708),x=r(74466),m=r(59434);let h=(0,x.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-[color,box-shadow] disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"border border-input bg-background shadow-xs hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function p(e){let{className:t,variant:r,size:i,asChild:s=!1,...a}=e,l=s?f.DX:"button";return(0,n.jsx)(l,{"data-slot":"button",className:(0,m.cn)(h({variant:r,size:i,className:t})),...a})}function g(){let{theme:e,setTheme:t}=(0,u.D)();return(0,n.jsxs)(p,{variant:"outline",size:"icon",onClick:()=>t("light"===e?"dark":"light"),className:"cursor-pointer rounded-full h-8 w-8",children:[(0,n.jsx)(d.A,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0 text-primary"}),(0,n.jsx)(c.A,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100 text-primary"}),(0,n.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}var b=r(54416),y=r(74783),v=r(95360),w=r(45084),j=r(6874),k=r.n(j),_=r(66766),N=r(64541);let C="70rem",E={hidden:{opacity:0},visible:{opacity:1},exit:{opacity:0}},A={hidden:{opacity:0,y:100},visible:{opacity:1,y:0,rotate:0,transition:{type:"spring",damping:15,stiffness:200,staggerChildren:.03}},exit:{opacity:0,y:100,transition:{duration:.1}}},S={hidden:{opacity:0},visible:{opacity:1}},z={hidden:{opacity:0},visible:{opacity:1}};function R(){let{scrollY:e}=(0,v.L)(),[t,r]=(0,a.useState)(!1),[l,d]=(0,a.useState)(!1),[c,f]=(0,a.useState)("hero"),{theme:x,resolvedTheme:h}=(0,u.D)(),[p,j]=(0,a.useState)(!1),{user:R}=(0,N.A)();(0,a.useEffect)(()=>{j(!0)},[]),(0,a.useEffect)(()=>{let e=()=>{for(let e of i.CQ.nav.links.map(e=>e.href.substring(1))){let t=document.getElementById(e);if(t){let r=t.getBoundingClientRect();if(r.top<=150&&r.bottom>=150){f(e);break}}}};return window.addEventListener("scroll",e),e(),()=>window.removeEventListener("scroll",e)},[]),(0,a.useEffect)(()=>e.on("change",e=>{r(e>10)}),[e]);let L=()=>d(e=>!e),B=p&&"dark"===h?"/kortix-logo-white.svg":"/kortix-logo.svg";return(0,n.jsxs)("header",{className:(0,m.cn)("sticky z-50 mx-4 flex justify-center transition-all duration-300 md:mx-0",t?"top-6":"top-4 mx-0"),children:[(0,n.jsx)(s.P.div,{initial:{width:C},animate:{width:t?"800px":C},transition:{duration:.3,ease:[.25,.1,.25,1]},children:(0,n.jsx)("div",{className:(0,m.cn)("mx-auto max-w-7xl rounded-2xl transition-all duration-300  xl:px-0",t?"px-2 border border-border backdrop-blur-lg bg-background/75":"shadow-none px-7"),children:(0,n.jsxs)("div",{className:"flex h-[56px] items-center justify-between p-4",children:[(0,n.jsx)(k(),{href:"/",className:"flex items-center gap-3",children:(0,n.jsx)(_.default,{src:B,alt:"Kortix Logo",width:140,height:22,priority:!0})}),(0,n.jsx)(o,{}),(0,n.jsxs)("div",{className:"flex flex-row items-center gap-1 md:gap-3 shrink-0",children:[(0,n.jsx)("div",{className:"flex items-center space-x-3",children:R?(0,n.jsx)(k(),{className:"bg-secondary h-8 hidden md:flex items-center justify-center text-sm font-normal tracking-wide rounded-full text-primary-foreground dark:text-secondary-foreground w-fit px-4 shadow-[inset_0_1px_2px_rgba(255,255,255,0.25),0_3px_3px_-1.5px_rgba(16,24,40,0.06),0_1px_1px_rgba(16,24,40,0.08)] border border-white/[0.12]",href:"/dashboard",children:"Dashboard"}):(0,n.jsx)(k(),{className:"bg-secondary h-8 hidden md:flex items-center justify-center text-sm font-normal tracking-wide rounded-full text-primary-foreground dark:text-secondary-foreground w-fit px-4 shadow-[inset_0_1px_2px_rgba(255,255,255,0.25),0_3px_3px_-1.5px_rgba(16,24,40,0.06),0_1px_1px_rgba(16,24,40,0.08)] border border-white/[0.12]",href:"/auth",children:"Get started"})}),(0,n.jsx)(g,{}),(0,n.jsx)("button",{className:"md:hidden border border-border size-8 rounded-md cursor-pointer flex items-center justify-center",onClick:L,children:l?(0,n.jsx)(b.A,{className:"size-5"}):(0,n.jsx)(y.A,{className:"size-5"})})]})]})})}),(0,n.jsx)(w.N,{children:l&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(s.P.div,{className:"fixed inset-0 bg-black/50 backdrop-blur-sm",initial:"hidden",animate:"visible",exit:"exit",variants:E,transition:{duration:.2},onClick:()=>d(!1)}),(0,n.jsx)(s.P.div,{className:"fixed inset-x-0 w-[95%] mx-auto bottom-3 bg-background border border-border p-4 rounded-xl shadow-lg",initial:"hidden",animate:"visible",exit:"exit",variants:A,children:(0,n.jsxs)("div",{className:"flex flex-col gap-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)(k(),{href:"/",className:"flex items-center gap-3",children:[(0,n.jsx)(_.default,{src:B,alt:"Kortix Logo",width:120,height:22,priority:!0}),(0,n.jsx)("span",{className:"font-medium text-primary text-sm",children:"/ Suna"})]}),(0,n.jsx)("button",{onClick:L,className:"border border-border rounded-md p-1 cursor-pointer",children:(0,n.jsx)(b.A,{className:"size-5"})})]}),(0,n.jsx)(s.P.ul,{className:"flex flex-col text-sm mb-4 border border-border rounded-md",variants:S,children:(0,n.jsx)(w.N,{children:i.CQ.nav.links.map(e=>(0,n.jsx)(s.P.li,{className:"p-2.5 border-b border-border last:border-b-0",variants:z,children:(0,n.jsx)("a",{href:e.href,onClick:t=>{t.preventDefault();let r=document.getElementById(e.href.substring(1));null==r||r.scrollIntoView({behavior:"smooth"}),d(!1)},className:"underline-offset-4 hover:text-primary/80 transition-colors ".concat(c===e.href.substring(1)?"text-primary font-medium":"text-primary/60"),children:e.name})},e.id))})}),(0,n.jsxs)("div",{className:"flex flex-col gap-2",children:[R?(0,n.jsx)(k(),{href:"/dashboard",className:"bg-secondary h-8 flex items-center justify-center text-sm font-normal tracking-wide rounded-full text-primary-foreground dark:text-secondary-foreground w-full px-4 shadow-[inset_0_1px_2px_rgba(255,255,255,0.25),0_3px_3px_-1.5px_rgba(16,24,40,0.06),0_1px_1px_rgba(16,24,40,0.08)] border border-white/[0.12] hover:bg-secondary/80 transition-all ease-out active:scale-95",children:"Dashboard"}):(0,n.jsx)(k(),{href:"/auth",className:"bg-secondary h-8 flex items-center justify-center text-sm font-normal tracking-wide rounded-full text-primary-foreground dark:text-secondary-foreground w-full px-4 shadow-[inset_0_1px_2px_rgba(255,255,255,0.25),0_3px_3px_-1.5px_rgba(16,24,40,0.06),0_1px_1px_rgba(16,24,40,0.08)] border border-white/[0.12] hover:bg-secondary/80 transition-all ease-out active:scale-95",children:"Get Started"}),(0,n.jsx)("div",{className:"flex justify-between",children:(0,n.jsx)(g,{})})]})]})})]})})]})}},43533:(e,t,r)=>{Promise.resolve().then(r.bind(r,39076))},52643:(e,t,r)=>{"use strict";r.d(t,{U:()=>i});var n=r(81935);let i=()=>{let e="";return e&&!e.startsWith("http")&&(e="http://".concat(e)),(0,n.createBrowserClient)(e,"")}},54416:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},62098:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},74466:(e,t,r)=>{"use strict";r.d(t,{F:()=>a});var n=r(52596);let i=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,s=n.$,a=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return s(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:a,defaultVariants:l}=t,o=Object.keys(a).map(e=>{let t=null==r?void 0:r[e],n=null==l?void 0:l[e];if(null===t)return null;let s=i(t)||i(n);return a[e][s]}),d=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return s(e,o,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...i}=t;return Object.entries(i).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...d}[t]):({...l,...d})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},74783:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},93509:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(19946).A)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},99708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>l,Dc:()=>d,TL:()=>a});var n=r(12115),i=r(6101),s=r(95155);function a(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...s}=e;if(n.isValidElement(r)){var a;let e,l,o=(a=r,(l=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(l=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),d=function(e,t){let r={...t};for(let n in t){let i=e[n],s=t[n];/^on[A-Z]/.test(n)?i&&s?r[n]=(...e)=>{let t=s(...e);return i(...e),t}:i&&(r[n]=i):"style"===n?r[n]={...i,...s}:"className"===n&&(r[n]=[i,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==n.Fragment&&(d.ref=t?(0,i.t)(t,o):o),n.cloneElement(r,d)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:i,...a}=e,l=n.Children.toArray(i),o=l.find(c);if(o){let e=o.props.children,i=l.map(t=>t!==o?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...a,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,s.jsx)(t,{...a,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}var l=a("Slot"),o=Symbol("radix.slottable");function d(e){let t=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=o,t}function c(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}}},e=>{var t=t=>e(e.s=t);e.O(0,[2969,1935,6874,7453,9879,5653,6766,6915,5360,937,8222,8441,1684,7358],()=>t(43533)),_N_E=e.O()}]);