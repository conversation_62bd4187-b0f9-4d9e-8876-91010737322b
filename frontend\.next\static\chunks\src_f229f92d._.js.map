{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/home/<USER>"], "sourcesContent": ["'use client';\r\n\r\nimport * as React from 'react';\r\nimport { ThemeProvider as NextThemesProvider } from 'next-themes';\r\n\r\nexport function ThemeProvider({\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof NextThemesProvider>) {\r\n  return <NextThemesProvider {...props}>{children}</NextThemesProvider>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKO,SAAS,cAAc,EAC5B,QAAQ,EACR,GAAG,OAC6C;IAChD,qBAAO,6LAAC,mJAAA,CAAA,gBAAkB;QAAE,GAAG,KAAK;kBAAG;;;;;;AACzC;KALgB", "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/lib/supabase/client.ts"], "sourcesContent": ["import { createBrowserClient } from '@supabase/ssr';\r\n\r\nexport const createClient = () => {\r\n  // Get URL and key from environment variables\r\n  let supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;\r\n  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;\r\n\r\n  // Ensure the URL is in the proper format with http/https protocol\r\n  if (supabaseUrl && !supabaseUrl.startsWith('http')) {\r\n    // If it's just a hostname without protocol, add http://\r\n    supabaseUrl = `http://${supabaseUrl}`;\r\n  }\r\n\r\n  // console.log('Supabase URL:', supabaseUrl);\r\n  // console.log('Supabase Anon Key:', supabaseAnonKey);\r\n\r\n  return createBrowserClient(supabaseUrl, supabaseAnonKey);\r\n};\r\n"], "names": [], "mappings": ";;;AAIoB;AAJpB;AAAA;;AAEO,MAAM,eAAe;IAC1B,6CAA6C;IAC7C,IAAI;IACJ,MAAM;IAEN,kEAAkE;IAClE,IAAI,eAAe,CAAC,YAAY,UAAU,CAAC,SAAS;QAClD,wDAAwD;QACxD,cAAc,CAAC,OAAO,EAAE,aAAa;IACvC;IAEA,6CAA6C;IAC7C,sDAAsD;IAEtD,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;AAC1C", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/AuthProvider.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, {\r\n  createContext,\r\n  useContext,\r\n  useState,\r\n  useEffect,\r\n  ReactNode,\r\n} from 'react';\r\nimport { createClient } from '@/lib/supabase/client';\r\nimport { User, Session } from '@supabase/supabase-js';\r\nimport { SupabaseClient } from '@supabase/supabase-js';\r\n\r\ntype AuthContextType = {\r\n  supabase: SupabaseClient;\r\n  session: Session | null;\r\n  user: User | null;\r\n  isLoading: boolean;\r\n  signOut: () => Promise<void>;\r\n};\r\n\r\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\r\n\r\nexport const AuthProvider = ({ children }: { children: ReactNode }) => {\r\n  const supabase = createClient();\r\n  const [session, setSession] = useState<Session | null>(null);\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [isLoading, setIsLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    const getInitialSession = async () => {\r\n      const {\r\n        data: { session: currentSession },\r\n      } = await supabase.auth.getSession();\r\n      setSession(currentSession);\r\n      setUser(currentSession?.user ?? null);\r\n      setIsLoading(false);\r\n    };\r\n\r\n    getInitialSession();\r\n\r\n    const { data: authListener } = supabase.auth.onAuthStateChange(\r\n      (_event, newSession) => {\r\n        setSession(newSession);\r\n        setUser(newSession?.user ?? null);\r\n        // No need to set loading state here as initial load is done\r\n        // and subsequent changes shouldn't show a loading state for the whole app\r\n        if (isLoading) setIsLoading(false);\r\n      },\r\n    );\r\n\r\n    return () => {\r\n      authListener?.subscription.unsubscribe();\r\n    };\r\n  }, [supabase, isLoading]); // Added isLoading to dependencies to ensure it runs once after initial load completes\r\n\r\n  const signOut = async () => {\r\n    await supabase.auth.signOut();\r\n    // State updates will be handled by onAuthStateChange\r\n  };\r\n\r\n  const value = {\r\n    supabase,\r\n    session,\r\n    user,\r\n    isLoading,\r\n    signOut,\r\n  };\r\n\r\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\r\n};\r\n\r\nexport const useAuth = (): AuthContextType => {\r\n  const context = useContext(AuthContext);\r\n  if (context === undefined) {\r\n    throw new Error('useAuth must be used within an AuthProvider');\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AAEA;AAOA;;;AATA;;;AAqBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,eAAe,CAAC,EAAE,QAAQ,EAA2B;;IAChE,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM;4DAAoB;oBACxB,MAAM,EACJ,MAAM,EAAE,SAAS,cAAc,EAAE,EAClC,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;oBAClC,WAAW;oBACX,QAAQ,gBAAgB,QAAQ;oBAChC,aAAa;gBACf;;YAEA;YAEA,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB;0CAC5D,CAAC,QAAQ;oBACP,WAAW;oBACX,QAAQ,YAAY,QAAQ;oBAC5B,4DAA4D;oBAC5D,0EAA0E;oBAC1E,IAAI,WAAW,aAAa;gBAC9B;;YAGF;0CAAO;oBACL,cAAc,aAAa;gBAC7B;;QACF;iCAAG;QAAC;QAAU;KAAU,GAAG,sFAAsF;IAEjH,MAAM,UAAU;QACd,MAAM,SAAS,IAAI,CAAC,OAAO;IAC3B,qDAAqD;IACvD;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;GA/Ca;KAAA;AAiDN,MAAM,UAAU;;IACrB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANa", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/lib/api.ts"], "sourcesContent": ["import { createClient } from '@/lib/supabase/client';\r\nimport { handleApiError } from './error-handler';\r\n\r\n// Get backend URL from environment variables\r\nconst API_URL = process.env.NEXT_PUBLIC_BACKEND_URL || '';\r\n\r\n// Set to keep track of agent runs that are known to be non-running\r\nconst nonRunningAgentRuns = new Set<string>();\r\n// Map to keep track of active EventSource streams\r\nconst activeStreams = new Map<string, EventSource>();\r\n\r\n// Custom error for billing issues\r\nexport class BillingError extends Error {\r\n  status: number;\r\n  detail: { message: string; [key: string]: any }; // Allow other properties in detail\r\n\r\n  constructor(\r\n    status: number,\r\n    detail: { message: string; [key: string]: any },\r\n    message?: string,\r\n  ) {\r\n    super(message || detail.message || `Billing Error: ${status}`);\r\n    this.name = 'BillingError';\r\n    this.status = status;\r\n    this.detail = detail;\r\n\r\n    // Set the prototype explicitly.\r\n    Object.setPrototypeOf(this, BillingError.prototype);\r\n  }\r\n}\r\n\r\nexport class NoAccessTokenAvailableError extends Error {\r\n  constructor(message?: string, options?: { cause?: Error }) {\r\n    super(message || 'No access token available', options);\r\n  }\r\n  name = 'NoAccessTokenAvailableError';\r\n}\r\n\r\n// Type Definitions (moved from potential separate file for clarity)\r\nexport type Project = {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  account_id: string;\r\n  created_at: string;\r\n  updated_at?: string;\r\n  sandbox: {\r\n    vnc_preview?: string;\r\n    sandbox_url?: string;\r\n    id?: string;\r\n    pass?: string;\r\n  };\r\n  is_public?: boolean; // Flag to indicate if the project is public\r\n  [key: string]: any; // Allow additional properties to handle database fields\r\n};\r\n\r\nexport type Thread = {\r\n  thread_id: string;\r\n  account_id: string | null;\r\n  project_id?: string | null;\r\n  is_public?: boolean;\r\n  created_at: string;\r\n  updated_at: string;\r\n  [key: string]: any; // Allow additional properties to handle database fields\r\n};\r\n\r\nexport type Message = {\r\n  role: string;\r\n  content: string;\r\n  type: string;\r\n  agent_id?: string;\r\n  agents?: {\r\n    name: string;\r\n    avatar?: string;\r\n    avatar_color?: string;\r\n  };\r\n};\r\n\r\nexport type AgentRun = {\r\n  id: string;\r\n  thread_id: string;\r\n  status: 'running' | 'completed' | 'stopped' | 'error';\r\n  started_at: string;\r\n  completed_at: string | null;\r\n  responses: Message[];\r\n  error: string | null;\r\n};\r\n\r\nexport type ToolCall = {\r\n  name: string;\r\n  arguments: Record<string, unknown>;\r\n};\r\n\r\nexport interface InitiateAgentResponse {\r\n  thread_id: string;\r\n  agent_run_id: string;\r\n}\r\n\r\nexport interface HealthCheckResponse {\r\n  status: string;\r\n  timestamp: string;\r\n  instance_id: string;\r\n}\r\n\r\nexport interface FileInfo {\r\n  name: string;\r\n  path: string;\r\n  is_dir: boolean;\r\n  size: number;\r\n  mod_time: string;\r\n  permissions?: string;\r\n}\r\n\r\nexport type WorkflowExecution = {\r\n  id: string;\r\n  workflow_id: string;\r\n  workflow_name: string;\r\n  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';\r\n  started_at: string | null;\r\n  completed_at: string | null;\r\n  result: any;\r\n  error: string | null;\r\n};\r\n\r\nexport type WorkflowExecutionLog = {\r\n  id: string;\r\n  execution_id: string;\r\n  node_id: string;\r\n  node_name: string;\r\n  node_type: string;\r\n  started_at: string;\r\n  completed_at: string | null;\r\n  status: 'running' | 'completed' | 'failed';\r\n  input_data: any;\r\n  output_data: any;\r\n  error: string | null;\r\n};\r\n\r\n// Workflow Types\r\nexport type Workflow = {\r\n  id: string;\r\n  name: string;\r\n  description: string;\r\n  status: 'draft' | 'active' | 'paused' | 'disabled' | 'archived';\r\n  project_id: string;\r\n  account_id: string;\r\n  definition: {\r\n    name: string;\r\n    description: string;\r\n    nodes: any[];\r\n    edges: any[];\r\n    variables?: Record<string, any>;\r\n  };\r\n  created_at: string;\r\n  updated_at: string;\r\n};\r\n\r\nexport type WorkflowNode = {\r\n  id: string;\r\n  type: string;\r\n  position: { x: number; y: number };\r\n  data: any;\r\n};\r\n\r\nexport type WorkflowEdge = {\r\n  id: string;\r\n  source: string;\r\n  target: string;\r\n  sourceHandle?: string;\r\n  targetHandle?: string;\r\n};\r\n\r\n// Project APIs\r\nexport const getProjects = async (): Promise<Project[]> => {\r\n  try {\r\n    const supabase = createClient();\r\n\r\n    // Get the current user's ID to filter projects\r\n    const { data: userData, error: userError } = await supabase.auth.getUser();\r\n    if (userError) {\r\n      console.error('Error getting current user:', userError);\r\n      return [];\r\n    }\r\n\r\n    // If no user is logged in, return an empty array\r\n    if (!userData.user) {\r\n      console.log('[API] No user logged in, returning empty projects array');\r\n      return [];\r\n    }\r\n\r\n    // Query only projects where account_id matches the current user's ID\r\n    const { data, error } = await supabase\r\n      .from('projects')\r\n      .select('*')\r\n      .eq('account_id', userData.user.id);\r\n\r\n    if (error) {\r\n      // Handle permission errors specifically\r\n      if (\r\n        error.code === '42501' &&\r\n        error.message.includes('has_role_on_account')\r\n      ) {\r\n        console.error(\r\n          'Permission error: User does not have proper account access',\r\n        );\r\n        return []; // Return empty array instead of throwing\r\n      }\r\n      throw error;\r\n    }\r\n\r\n    console.log('[API] Raw projects from DB:', data?.length, data);\r\n\r\n    // Map database fields to our Project type\r\n    const mappedProjects: Project[] = (data || []).map((project) => ({\r\n      id: project.project_id,\r\n      name: project.name || '',\r\n      description: project.description || '',\r\n      account_id: project.account_id,\r\n      created_at: project.created_at,\r\n      updated_at: project.updated_at,\r\n      sandbox: project.sandbox || {\r\n        id: '',\r\n        pass: '',\r\n        vnc_preview: '',\r\n        sandbox_url: '',\r\n      },\r\n    }));\r\n\r\n    console.log('[API] Mapped projects for frontend:', mappedProjects.length);\r\n\r\n    return mappedProjects;\r\n  } catch (err) {\r\n    console.error('Error fetching projects:', err);\r\n    handleApiError(err, { operation: 'load projects', resource: 'projects' });\r\n    // Return empty array for permission errors to avoid crashing the UI\r\n    return [];\r\n  }\r\n};\r\n\r\nexport const getProject = async (projectId: string): Promise<Project> => {\r\n  const supabase = createClient();\r\n\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from('projects')\r\n      .select('*')\r\n      .eq('project_id', projectId)\r\n      .single();\r\n\r\n    if (error) {\r\n      // Handle the specific \"no rows returned\" error from Supabase\r\n      if (error.code === 'PGRST116') {\r\n        throw new Error(`Project not found or not accessible: ${projectId}`);\r\n      }\r\n      throw error;\r\n    }\r\n\r\n    console.log('Raw project data from database:', data);\r\n\r\n    // If project has a sandbox, ensure it's started\r\n    if (data.sandbox?.id) {\r\n      // Fire off sandbox activation without blocking\r\n      const ensureSandboxActive = async () => {\r\n        try {\r\n          const {\r\n            data: { session },\r\n          } = await supabase.auth.getSession();\r\n\r\n          // For public projects, we don't need authentication\r\n          const headers: Record<string, string> = {\r\n            'Content-Type': 'application/json',\r\n          };\r\n\r\n          if (session?.access_token) {\r\n            headers['Authorization'] = `Bearer ${session.access_token}`;\r\n          }\r\n\r\n          console.log(`Ensuring sandbox is active for project ${projectId}...`);\r\n          const response = await fetch(\r\n            `${API_URL}/project/${projectId}/sandbox/ensure-active`,\r\n            {\r\n              method: 'POST',\r\n              headers,\r\n            },\r\n          );\r\n\r\n          if (!response.ok) {\r\n            const errorText = await response\r\n              .text()\r\n              .catch(() => 'No error details available');\r\n            console.warn(\r\n              `Failed to ensure sandbox is active: ${response.status} ${response.statusText}`,\r\n              errorText,\r\n            );\r\n          } else {\r\n            console.log('Sandbox activation successful');\r\n          }\r\n        } catch (sandboxError) {\r\n          console.warn('Failed to ensure sandbox is active:', sandboxError);\r\n        }\r\n      };\r\n\r\n      // Start the sandbox activation without awaiting\r\n      ensureSandboxActive();\r\n    }\r\n\r\n    // Map database fields to our Project type\r\n    const mappedProject: Project = {\r\n      id: data.project_id,\r\n      name: data.name || '',\r\n      description: data.description || '',\r\n      account_id: data.account_id,\r\n      is_public: data.is_public || false,\r\n      created_at: data.created_at,\r\n      sandbox: data.sandbox || {\r\n        id: '',\r\n        pass: '',\r\n        vnc_preview: '',\r\n        sandbox_url: '',\r\n      },\r\n    };\r\n\r\n    // console.log('Mapped project data for frontend:', mappedProject);\r\n\r\n    return mappedProject;\r\n  } catch (error) {\r\n    console.error(`Error fetching project ${projectId}:`, error);\r\n    handleApiError(error, { operation: 'load project', resource: `project ${projectId}` });\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const createProject = async (\r\n  projectData: { name: string; description: string },\r\n  accountId?: string,\r\n): Promise<Project> => {\r\n  const supabase = createClient();\r\n\r\n  // If accountId is not provided, we'll need to get the user's ID\r\n  if (!accountId) {\r\n    const { data: userData, error: userError } = await supabase.auth.getUser();\r\n\r\n    if (userError) throw userError;\r\n    if (!userData.user)\r\n      throw new Error('You must be logged in to create a project');\r\n\r\n    // In Basejump, the personal account ID is the same as the user ID\r\n    accountId = userData.user.id;\r\n  }\r\n\r\n  const { data, error } = await supabase\r\n    .from('projects')\r\n    .insert({\r\n      name: projectData.name,\r\n      description: projectData.description || null,\r\n      account_id: accountId,\r\n    })\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    handleApiError(error, { operation: 'create project', resource: 'project' });\r\n    throw error;\r\n  }\r\n\r\n  const project = {\r\n    id: data.project_id,\r\n    name: data.name,\r\n    description: data.description || '',\r\n    account_id: data.account_id,\r\n    created_at: data.created_at,\r\n    sandbox: { id: '', pass: '', vnc_preview: '' },\r\n  };\r\n  return project;\r\n};\r\n\r\nexport const updateProject = async (\r\n  projectId: string,\r\n  data: Partial<Project>,\r\n): Promise<Project> => {\r\n  const supabase = createClient();\r\n\r\n  console.log('Updating project with ID:', projectId);\r\n  console.log('Update data:', data);\r\n\r\n  // Sanity check to avoid update errors\r\n  if (!projectId || projectId === '') {\r\n    console.error('Attempted to update project with invalid ID:', projectId);\r\n    throw new Error('Cannot update project: Invalid project ID');\r\n  }\r\n\r\n  const { data: updatedData, error } = await supabase\r\n    .from('projects')\r\n    .update(data)\r\n    .eq('project_id', projectId)\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    console.error('Error updating project:', error);\r\n    handleApiError(error, { operation: 'update project', resource: `project ${projectId}` });\r\n    throw error;\r\n  }\r\n\r\n  if (!updatedData) {\r\n    const noDataError = new Error('No data returned from update');\r\n    handleApiError(noDataError, { operation: 'update project', resource: `project ${projectId}` });\r\n    throw noDataError;\r\n  }\r\n\r\n  // Dispatch a custom event to notify components about the project change\r\n  if (typeof window !== 'undefined') {\r\n    window.dispatchEvent(\r\n      new CustomEvent('project-updated', {\r\n        detail: {\r\n          projectId,\r\n          updatedData: {\r\n            id: updatedData.project_id,\r\n            name: updatedData.name,\r\n            description: updatedData.description,\r\n          },\r\n        },\r\n      }),\r\n    );\r\n  }\r\n\r\n  // Return formatted project data - use same mapping as getProject\r\n  const project = {\r\n    id: updatedData.project_id,\r\n    name: updatedData.name,\r\n    description: updatedData.description || '',\r\n    account_id: updatedData.account_id,\r\n    created_at: updatedData.created_at,\r\n    sandbox: updatedData.sandbox || {\r\n      id: '',\r\n      pass: '',\r\n      vnc_preview: '',\r\n      sandbox_url: '',\r\n    },\r\n  };\r\n  return project;\r\n};\r\n\r\nexport const deleteProject = async (projectId: string): Promise<void> => {\r\n  const supabase = createClient();\r\n  const { error } = await supabase\r\n    .from('projects')\r\n    .delete()\r\n    .eq('project_id', projectId);\r\n\r\n  if (error) {\r\n    handleApiError(error, { operation: 'delete project', resource: `project ${projectId}` });\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Thread APIs\r\nexport const getThreads = async (projectId?: string): Promise<Thread[]> => {\r\n  const supabase = createClient();\r\n\r\n  // Get the current user's ID to filter threads\r\n  const { data: userData, error: userError } = await supabase.auth.getUser();\r\n  if (userError) {\r\n    console.error('Error getting current user:', userError);\r\n    return [];\r\n  }\r\n\r\n  // If no user is logged in, return an empty array\r\n  if (!userData.user) {\r\n    console.log('[API] No user logged in, returning empty threads array');\r\n    return [];\r\n  }\r\n\r\n  let query = supabase.from('threads').select('*');\r\n\r\n  // Always filter by the current user's account ID\r\n  query = query.eq('account_id', userData.user.id);\r\n\r\n  if (projectId) {\r\n    console.log('[API] Filtering threads by project_id:', projectId);\r\n    query = query.eq('project_id', projectId);\r\n  }\r\n\r\n  const { data, error } = await query;\r\n\r\n  if (error) {\r\n    handleApiError(error, { operation: 'load threads', resource: projectId ? `threads for project ${projectId}` : 'threads' });\r\n    throw error;\r\n  }\r\n\r\n  const mappedThreads: Thread[] = (data || [])\r\n    .filter((thread) => {\r\n      const metadata = thread.metadata || {};\r\n      return !metadata.is_agent_builder;\r\n    })\r\n    .map((thread) => ({\r\n      thread_id: thread.thread_id,\r\n      account_id: thread.account_id,\r\n      project_id: thread.project_id,\r\n      created_at: thread.created_at,\r\n      updated_at: thread.updated_at,\r\n      metadata: thread.metadata,\r\n    }));\r\n  return mappedThreads;\r\n};\r\n\r\nexport const getThread = async (threadId: string): Promise<Thread> => {\r\n  const supabase = createClient();\r\n  const { data, error } = await supabase\r\n    .from('threads')\r\n    .select('*')\r\n    .eq('thread_id', threadId)\r\n    .single();\r\n\r\n  if (error) {\r\n    handleApiError(error, { operation: 'load thread', resource: `thread ${threadId}` });\r\n    throw error;\r\n  }\r\n\r\n  return data;\r\n};\r\n\r\nexport const createThread = async (projectId: string): Promise<Thread> => {\r\n  const supabase = createClient();\r\n\r\n  // If user is not logged in, redirect to login\r\n  const {\r\n    data: { user },\r\n  } = await supabase.auth.getUser();\r\n  if (!user) {\r\n    throw new Error('You must be logged in to create a thread');\r\n  }\r\n\r\n  const { data, error } = await supabase\r\n    .from('threads')\r\n    .insert({\r\n      project_id: projectId,\r\n      account_id: user.id, // Use the current user's ID as the account ID\r\n    })\r\n    .select()\r\n    .single();\r\n\r\n  if (error) {\r\n    handleApiError(error, { operation: 'create thread', resource: 'thread' });\r\n    throw error;\r\n  }\r\n  return data;\r\n};\r\n\r\nexport const addUserMessage = async (\r\n  threadId: string,\r\n  content: string,\r\n): Promise<void> => {\r\n  const supabase = createClient();\r\n\r\n  // Format the message in the format the LLM expects - keep it simple with only required fields\r\n  const message = {\r\n    role: 'user',\r\n    content: content,\r\n  };\r\n\r\n  // Insert the message into the messages table\r\n  const { error } = await supabase.from('messages').insert({\r\n    thread_id: threadId,\r\n    type: 'user',\r\n    is_llm_message: true,\r\n    content: JSON.stringify(message),\r\n  });\r\n\r\n  if (error) {\r\n    console.error('Error adding user message:', error);\r\n    handleApiError(error, { operation: 'add message', resource: 'message' });\r\n    throw new Error(`Error adding message: ${error.message}`);\r\n  }\r\n};\r\n\r\nexport const getMessages = async (threadId: string): Promise<Message[]> => {\r\n  const supabase = createClient();\r\n\r\n  let allMessages: Message[] = [];\r\n  let from = 0;\r\n  const batchSize = 1000;\r\n  let hasMore = true;\r\n\r\n  while (hasMore) {\r\n    const { data, error } = await supabase\r\n      .from('messages')\r\n      .select(`\r\n        *,\r\n        agents:agent_id (\r\n          name,\r\n          avatar,\r\n          avatar_color\r\n        )\r\n      `)\r\n      .eq('thread_id', threadId)\r\n      .neq('type', 'cost')\r\n      .neq('type', 'summary')\r\n      .order('created_at', { ascending: true })\r\n      .range(from, from + batchSize - 1);\r\n\r\n    if (error) {\r\n      console.error('Error fetching messages:', error);\r\n      handleApiError(error, { operation: 'load messages', resource: `messages for thread ${threadId}` });\r\n      throw new Error(`Error getting messages: ${error.message}`);\r\n    }\r\n\r\n    if (data && data.length > 0) {\r\n      allMessages = allMessages.concat(data);\r\n      from += batchSize;\r\n      hasMore = data.length === batchSize;\r\n    } else {\r\n      hasMore = false;\r\n    }\r\n  }\r\n\r\n  console.log('[API] Messages fetched count:', allMessages.length);\r\n\r\n  return allMessages;\r\n};\r\n\r\n// Agent APIs\r\nexport const startAgent = async (\r\n  threadId: string,\r\n  options?: {\r\n    model_name?: string;\r\n    enable_thinking?: boolean;\r\n    reasoning_effort?: string;\r\n    stream?: boolean;\r\n    agent_id?: string;\r\n  },\r\n): Promise<{ agent_run_id: string }> => {\r\n  try {\r\n    const supabase = createClient();\r\n    const {\r\n      data: { session },\r\n    } = await supabase.auth.getSession();\r\n\r\n    if (!session?.access_token) {\r\n      throw new NoAccessTokenAvailableError();\r\n    }\r\n\r\n    // Check if backend URL is configured\r\n    if (!API_URL) {\r\n      throw new Error(\r\n        'Backend URL is not configured. Set NEXT_PUBLIC_BACKEND_URL in your environment.',\r\n      );\r\n    }\r\n\r\n    console.log(\r\n      `[API] Starting agent for thread ${threadId} using ${API_URL}/thread/${threadId}/agent/start`,\r\n    );\r\n\r\n    const defaultOptions = {\r\n      model_name: 'claude-3-7-sonnet-latest',\r\n      enable_thinking: false,\r\n      reasoning_effort: 'low',\r\n      stream: true,\r\n      agent_id: undefined,\r\n    };\r\n\r\n    const finalOptions = { ...defaultOptions, ...options };\r\n\r\n    const body: any = {\r\n      model_name: finalOptions.model_name,\r\n      enable_thinking: finalOptions.enable_thinking,\r\n      reasoning_effort: finalOptions.reasoning_effort,\r\n      stream: finalOptions.stream,\r\n    };\r\n    \r\n    // Only include agent_id if it's provided\r\n    if (finalOptions.agent_id) {\r\n      body.agent_id = finalOptions.agent_id;\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/thread/${threadId}/agent/start`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        Authorization: `Bearer ${session.access_token}`,\r\n      },\r\n      body: JSON.stringify(body),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      // Check for 402 Payment Required first\r\n      if (response.status === 402) {\r\n        try {\r\n          const errorData = await response.json();\r\n          console.error(`[API] Billing error starting agent (402):`, errorData);\r\n          // Ensure detail exists and has a message property\r\n          const detail = errorData?.detail || { message: 'Payment Required' };\r\n          if (typeof detail.message !== 'string') {\r\n            detail.message = 'Payment Required'; // Default message if missing\r\n          }\r\n          throw new BillingError(response.status, detail);\r\n        } catch (parseError) {\r\n          // Handle cases where parsing fails or the structure isn't as expected\r\n          console.error(\r\n            '[API] Could not parse 402 error response body:',\r\n            parseError,\r\n          );\r\n          throw new BillingError(\r\n            response.status,\r\n            { message: 'Payment Required' },\r\n            `Error starting agent: ${response.statusText} (402)`,\r\n          );\r\n        }\r\n      }\r\n\r\n      // Handle other errors\r\n      const errorText = await response\r\n        .text()\r\n        .catch(() => 'No error details available');\r\n      console.error(\r\n        `[API] Error starting agent: ${response.status} ${response.statusText}`,\r\n        errorText,\r\n      );\r\n      throw new Error(\r\n        `Error starting agent: ${response.statusText} (${response.status})`,\r\n      );\r\n    }\r\n\r\n    const result = await response.json();\r\n    return result;\r\n  } catch (error) {\r\n    // Rethrow BillingError instances directly\r\n    if (error instanceof BillingError) {\r\n      throw error;\r\n    }\r\n\r\n    if (error instanceof NoAccessTokenAvailableError) {\r\n      throw error;\r\n    }\r\n\r\n    console.error('[API] Failed to start agent:', error);\r\n    \r\n    // Handle different error types with appropriate user messages\r\n    if (\r\n      error instanceof TypeError &&\r\n      error.message.includes('Failed to fetch')\r\n    ) {\r\n      const networkError = new Error(\r\n        `Cannot connect to backend server. Please check your internet connection and make sure the backend is running.`,\r\n      );\r\n      handleApiError(networkError, { operation: 'start agent', resource: 'AI assistant' });\r\n      throw networkError;\r\n    }\r\n\r\n    // For other errors, add context and rethrow\r\n    handleApiError(error, { operation: 'start agent', resource: 'AI assistant' });\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const stopAgent = async (agentRunId: string): Promise<void> => {\r\n  // Add to non-running set immediately to prevent reconnection attempts\r\n  nonRunningAgentRuns.add(agentRunId);\r\n\r\n  // Close any existing stream\r\n  const existingStream = activeStreams.get(agentRunId);\r\n  if (existingStream) {\r\n    console.log(\r\n      `[API] Closing existing stream for ${agentRunId} before stopping agent`,\r\n    );\r\n    existingStream.close();\r\n    activeStreams.delete(agentRunId);\r\n  }\r\n\r\n  const supabase = createClient();\r\n  const {\r\n    data: { session },\r\n  } = await supabase.auth.getSession();\r\n\r\n  if (!session?.access_token) {\r\n    const authError = new NoAccessTokenAvailableError();\r\n    handleApiError(authError, { operation: 'stop agent', resource: 'AI assistant' });\r\n    throw authError;\r\n  }\r\n\r\n  const response = await fetch(`${API_URL}/agent-run/${agentRunId}/stop`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      Authorization: `Bearer ${session.access_token}`,\r\n    },\r\n    // Add cache: 'no-store' to prevent caching\r\n    cache: 'no-store',\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const stopError = new Error(`Error stopping agent: ${response.statusText}`);\r\n    handleApiError(stopError, { operation: 'stop agent', resource: 'AI assistant' });\r\n    throw stopError;\r\n  }\r\n};\r\n\r\nexport const getAgentStatus = async (agentRunId: string): Promise<AgentRun> => {\r\n  console.log(`[API] Requesting agent status for ${agentRunId}`);\r\n\r\n  // If we already know this agent is not running, throw an error\r\n  if (nonRunningAgentRuns.has(agentRunId)) {\r\n    console.log(\r\n      `[API] Agent run ${agentRunId} is known to be non-running, returning error`,\r\n    );\r\n    throw new Error(`Agent run ${agentRunId} is not running`);\r\n  }\r\n\r\n  try {\r\n    const supabase = createClient();\r\n    const {\r\n      data: { session },\r\n    } = await supabase.auth.getSession();\r\n\r\n    if (!session?.access_token) {\r\n      console.error('[API] No access token available for getAgentStatus');\r\n      throw new NoAccessTokenAvailableError();\r\n    }\r\n\r\n    const url = `${API_URL}/agent-run/${agentRunId}`;\r\n    console.log(`[API] Fetching from: ${url}`);\r\n\r\n    const response = await fetch(url, {\r\n      headers: {\r\n        Authorization: `Bearer ${session.access_token}`,\r\n      },\r\n      // Add cache: 'no-store' to prevent caching\r\n      cache: 'no-store',\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response\r\n        .text()\r\n        .catch(() => 'No error details available');\r\n      console.error(\r\n        `[API] Error getting agent status: ${response.status} ${response.statusText}`,\r\n        errorText,\r\n      );\r\n\r\n      // If we get a 404, add to non-running set\r\n      if (response.status === 404) {\r\n        nonRunningAgentRuns.add(agentRunId);\r\n      }\r\n\r\n      throw new Error(\r\n        `Error getting agent status: ${response.statusText} (${response.status})`,\r\n      );\r\n    }\r\n\r\n    const data = await response.json();\r\n    console.log(`[API] Successfully got agent status:`, data);\r\n\r\n    // If agent is not running, add to non-running set\r\n    if (data.status !== 'running') {\r\n      nonRunningAgentRuns.add(agentRunId);\r\n    }\r\n\r\n    return data;\r\n  } catch (error) {\r\n    console.error('[API] Failed to get agent status:', error);\r\n    handleApiError(error, { operation: 'get agent status', resource: 'AI assistant status', silent: true });\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const getAgentRuns = async (threadId: string): Promise<AgentRun[]> => {\r\n  try {\r\n    const supabase = createClient();\r\n    const {\r\n      data: { session },\r\n    } = await supabase.auth.getSession();\r\n\r\n    if (!session?.access_token) {\r\n      throw new NoAccessTokenAvailableError();\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/thread/${threadId}/agent-runs`, {\r\n      headers: {\r\n        Authorization: `Bearer ${session.access_token}`,\r\n      },\r\n      // Add cache: 'no-store' to prevent caching\r\n      cache: 'no-store',\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`Error getting agent runs: ${response.statusText}`);\r\n    }\r\n\r\n    const data = await response.json();\r\n    return data.agent_runs || [];\r\n  } catch (error) {\r\n    if (error instanceof NoAccessTokenAvailableError) {\r\n      throw error;\r\n    }\r\n\r\n    console.error('Failed to get agent runs:', error);\r\n    handleApiError(error, { operation: 'load agent runs', resource: 'conversation history' });\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const streamAgent = (\r\n  agentRunId: string,\r\n  callbacks: {\r\n    onMessage: (content: string) => void;\r\n    onError: (error: Error | string) => void;\r\n    onClose: () => void;\r\n  },\r\n): (() => void) => {\r\n  console.log(`[STREAM] streamAgent called for ${agentRunId}`);\r\n\r\n  // Check if this agent run is known to be non-running\r\n  if (nonRunningAgentRuns.has(agentRunId)) {\r\n    console.log(\r\n      `[STREAM] Agent run ${agentRunId} is known to be non-running, not creating stream`,\r\n    );\r\n    // Notify the caller immediately\r\n    setTimeout(() => {\r\n      callbacks.onError(`Agent run ${agentRunId} is not running`);\r\n      callbacks.onClose();\r\n    }, 0);\r\n\r\n    // Return a no-op cleanup function\r\n    return () => {};\r\n  }\r\n\r\n  // Check if there's already an active stream for this agent run\r\n  const existingStream = activeStreams.get(agentRunId);\r\n  if (existingStream) {\r\n    console.log(\r\n      `[STREAM] Stream already exists for ${agentRunId}, closing it first`,\r\n    );\r\n    existingStream.close();\r\n    activeStreams.delete(agentRunId);\r\n  }\r\n\r\n  // Set up a new stream\r\n  try {\r\n    const setupStream = async () => {\r\n      // First verify the agent is actually running\r\n      try {\r\n        const status = await getAgentStatus(agentRunId);\r\n        if (status.status !== 'running') {\r\n          console.log(\r\n            `[STREAM] Agent run ${agentRunId} is not running (status: ${status.status}), not creating stream`,\r\n          );\r\n          nonRunningAgentRuns.add(agentRunId);\r\n          callbacks.onError(\r\n            `Agent run ${agentRunId} is not running (status: ${status.status})`,\r\n          );\r\n          callbacks.onClose();\r\n          return;\r\n        }\r\n      } catch (err) {\r\n        console.error(`[STREAM] Error verifying agent run ${agentRunId}:`, err);\r\n\r\n        // Check if this is a \"not found\" error\r\n        const errorMessage = err instanceof Error ? err.message : String(err);\r\n        const isNotFoundError =\r\n          errorMessage.includes('not found') ||\r\n          errorMessage.includes('404') ||\r\n          errorMessage.includes('does not exist');\r\n\r\n        if (isNotFoundError) {\r\n          console.log(\r\n            `[STREAM] Agent run ${agentRunId} not found, not creating stream`,\r\n          );\r\n          nonRunningAgentRuns.add(agentRunId);\r\n        }\r\n\r\n        callbacks.onError(errorMessage);\r\n        callbacks.onClose();\r\n        return;\r\n      }\r\n\r\n      const supabase = createClient();\r\n      const {\r\n        data: { session },\r\n      } = await supabase.auth.getSession();\r\n\r\n      if (!session?.access_token) {\r\n        const authError = new NoAccessTokenAvailableError();\r\n        console.error('[STREAM] No auth token available');\r\n        callbacks.onError(authError);\r\n        callbacks.onClose();\r\n        return;\r\n      }\r\n\r\n      const url = new URL(`${API_URL}/agent-run/${agentRunId}/stream`);\r\n      url.searchParams.append('token', session.access_token);\r\n\r\n      console.log(`[STREAM] Creating EventSource for ${agentRunId}`);\r\n      const eventSource = new EventSource(url.toString());\r\n\r\n      // Store the EventSource in the active streams map\r\n      activeStreams.set(agentRunId, eventSource);\r\n\r\n      eventSource.onopen = () => {\r\n        console.log(`[STREAM] Connection opened for ${agentRunId}`);\r\n      };\r\n\r\n      eventSource.onmessage = (event) => {\r\n        try {\r\n          const rawData = event.data;\r\n          if (rawData.includes('\"type\":\"ping\"')) return;\r\n\r\n          // Log raw data for debugging (truncated for readability)\r\n          console.log(\r\n            `[STREAM] Received data for ${agentRunId}: ${rawData.substring(0, 100)}${rawData.length > 100 ? '...' : ''}`,\r\n          );\r\n\r\n          // Skip empty messages\r\n          if (!rawData || rawData.trim() === '') {\r\n            console.debug('[STREAM] Received empty message, skipping');\r\n            return;\r\n          }\r\n\r\n          // Check for error status messages\r\n          try {\r\n            const jsonData = JSON.parse(rawData);\r\n            if (jsonData.status === 'error') {\r\n              console.error(`[STREAM] Error status received for ${agentRunId}:`, jsonData);\r\n              \r\n              // Pass the error message to the callback\r\n              callbacks.onError(jsonData.message || 'Unknown error occurred');\r\n              \r\n              // Don't close the stream for error status messages as they may continue\r\n              return;\r\n            }\r\n          } catch (jsonError) {\r\n            // Not JSON or invalid JSON, continue with normal processing\r\n          }\r\n\r\n          // Check for \"Agent run not found\" error\r\n          if (\r\n            rawData.includes('Agent run') &&\r\n            rawData.includes('not found in active runs')\r\n          ) {\r\n            console.log(\r\n              `[STREAM] Agent run ${agentRunId} not found in active runs, closing stream`,\r\n            );\r\n\r\n            // Add to non-running set to prevent future reconnection attempts\r\n            nonRunningAgentRuns.add(agentRunId);\r\n\r\n            // Notify about the error\r\n            callbacks.onError('Agent run not found in active runs');\r\n\r\n            // Clean up\r\n            eventSource.close();\r\n            activeStreams.delete(agentRunId);\r\n            callbacks.onClose();\r\n\r\n            return;\r\n          }\r\n\r\n          // Check for completion messages\r\n          if (\r\n            rawData.includes('\"type\":\"status\"') &&\r\n            rawData.includes('\"status\":\"completed\"')\r\n          ) {\r\n            console.log(\r\n              `[STREAM] Detected completion status message for ${agentRunId}`,\r\n            );\r\n\r\n            // Check for specific completion messages that indicate we should stop checking\r\n            if (\r\n              rawData.includes('Run data not available for streaming') ||\r\n              rawData.includes('Stream ended with status: completed')\r\n            ) {\r\n              console.log(\r\n                `[STREAM] Detected final completion message for ${agentRunId}, adding to non-running set`,\r\n              );\r\n              // Add to non-running set to prevent future reconnection attempts\r\n              nonRunningAgentRuns.add(agentRunId);\r\n            }\r\n\r\n            // Notify about the message\r\n            callbacks.onMessage(rawData);\r\n\r\n            // Clean up\r\n            eventSource.close();\r\n            activeStreams.delete(agentRunId);\r\n            callbacks.onClose();\r\n\r\n            return;\r\n          }\r\n\r\n          // Check for thread run end message\r\n          if (\r\n            rawData.includes('\"type\":\"status\"') &&\r\n            rawData.includes('\"status_type\":\"thread_run_end\"')\r\n          ) {\r\n            console.log(\r\n              `[STREAM] Detected thread run end message for ${agentRunId}`,\r\n            );\r\n\r\n            // Add to non-running set\r\n            nonRunningAgentRuns.add(agentRunId);\r\n\r\n            // Notify about the message\r\n            callbacks.onMessage(rawData);\r\n\r\n            // Clean up\r\n            eventSource.close();\r\n            activeStreams.delete(agentRunId);\r\n            callbacks.onClose();\r\n\r\n            return;\r\n          }\r\n\r\n          // For all other messages, just pass them through\r\n          callbacks.onMessage(rawData);\r\n        } catch (error) {\r\n          console.error(`[STREAM] Error handling message:`, error);\r\n          callbacks.onError(error instanceof Error ? error : String(error));\r\n        }\r\n      };\r\n\r\n      eventSource.onerror = (event) => {\r\n        console.log(`[STREAM] EventSource error for ${agentRunId}:`, event);\r\n\r\n        // Check if the agent is still running\r\n        getAgentStatus(agentRunId)\r\n          .then((status) => {\r\n            if (status.status !== 'running') {\r\n              console.log(\r\n                `[STREAM] Agent run ${agentRunId} is not running after error, closing stream`,\r\n              );\r\n              nonRunningAgentRuns.add(agentRunId);\r\n              eventSource.close();\r\n              activeStreams.delete(agentRunId);\r\n              callbacks.onClose();\r\n            } else {\r\n              console.log(\r\n                `[STREAM] Agent run ${agentRunId} is still running after error, keeping stream open`,\r\n              );\r\n              // Let the browser handle reconnection for non-fatal errors\r\n            }\r\n          })\r\n          .catch((err) => {\r\n            console.error(\r\n              `[STREAM] Error checking agent status after stream error:`,\r\n              err,\r\n            );\r\n\r\n            // Check if this is a \"not found\" error\r\n            const errMsg = err instanceof Error ? err.message : String(err);\r\n            const isNotFoundErr =\r\n              errMsg.includes('not found') ||\r\n              errMsg.includes('404') ||\r\n              errMsg.includes('does not exist');\r\n\r\n            if (isNotFoundErr) {\r\n              console.log(\r\n                `[STREAM] Agent run ${agentRunId} not found after error, closing stream`,\r\n              );\r\n              nonRunningAgentRuns.add(agentRunId);\r\n              eventSource.close();\r\n              activeStreams.delete(agentRunId);\r\n              callbacks.onClose();\r\n            }\r\n\r\n            // For other errors, notify but don't close the stream\r\n            callbacks.onError(errMsg);\r\n          });\r\n      };\r\n    };\r\n\r\n    // Start the stream setup\r\n    setupStream();\r\n\r\n    // Return a cleanup function\r\n    return () => {\r\n      console.log(`[STREAM] Cleanup called for ${agentRunId}`);\r\n      const stream = activeStreams.get(agentRunId);\r\n      if (stream) {\r\n        console.log(`[STREAM] Closing stream for ${agentRunId}`);\r\n        stream.close();\r\n        activeStreams.delete(agentRunId);\r\n      }\r\n    };\r\n  } catch (error) {\r\n    console.error(`[STREAM] Error setting up stream for ${agentRunId}:`, error);\r\n    callbacks.onError(error instanceof Error ? error : String(error));\r\n    callbacks.onClose();\r\n    return () => {};\r\n  }\r\n};\r\n\r\n// Sandbox API Functions\r\nexport const createSandboxFile = async (\r\n  sandboxId: string,\r\n  filePath: string,\r\n  content: string,\r\n): Promise<void> => {\r\n  try {\r\n    const supabase = createClient();\r\n    const {\r\n      data: { session },\r\n    } = await supabase.auth.getSession();\r\n\r\n    // Use FormData to handle both text and binary content more reliably\r\n    const formData = new FormData();\r\n    formData.append('path', filePath);\r\n\r\n    // Create a Blob from the content string and append as a file\r\n    const blob = new Blob([content], { type: 'application/octet-stream' });\r\n    formData.append('file', blob, filePath.split('/').pop() || 'file');\r\n\r\n    const headers: Record<string, string> = {};\r\n    if (session?.access_token) {\r\n      headers['Authorization'] = `Bearer ${session.access_token}`;\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/sandboxes/${sandboxId}/files`, {\r\n      method: 'POST',\r\n      headers,\r\n      body: formData,\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response\r\n        .text()\r\n        .catch(() => 'No error details available');\r\n      console.error(\r\n        `Error creating sandbox file: ${response.status} ${response.statusText}`,\r\n        errorText,\r\n      );\r\n      throw new Error(\r\n        `Error creating sandbox file: ${response.statusText} (${response.status})`,\r\n      );\r\n    }\r\n\r\n    const result = await response.json();\r\n    return result;\r\n  } catch (error) {\r\n    console.error('Failed to create sandbox file:', error);\r\n    handleApiError(error, { operation: 'create file', resource: `file ${filePath}` });\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Fallback method for legacy support using JSON\r\nexport const createSandboxFileJson = async (\r\n  sandboxId: string,\r\n  filePath: string,\r\n  content: string,\r\n): Promise<void> => {\r\n  try {\r\n    const supabase = createClient();\r\n    const {\r\n      data: { session },\r\n    } = await supabase.auth.getSession();\r\n\r\n    const headers: Record<string, string> = {\r\n      'Content-Type': 'application/json',\r\n    };\r\n\r\n    if (session?.access_token) {\r\n      headers['Authorization'] = `Bearer ${session.access_token}`;\r\n    }\r\n\r\n    const response = await fetch(\r\n      `${API_URL}/sandboxes/${sandboxId}/files/json`,\r\n      {\r\n        method: 'POST',\r\n        headers,\r\n        body: JSON.stringify({\r\n          path: filePath,\r\n          content: content,\r\n        }),\r\n      },\r\n    );\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response\r\n        .text()\r\n        .catch(() => 'No error details available');\r\n      console.error(\r\n        `Error creating sandbox file (JSON): ${response.status} ${response.statusText}`,\r\n        errorText,\r\n      );\r\n      throw new Error(\r\n        `Error creating sandbox file: ${response.statusText} (${response.status})`,\r\n      );\r\n    }\r\n\r\n    const result = await response.json();\r\n    return result;\r\n  } catch (error) {\r\n    console.error('Failed to create sandbox file with JSON:', error);\r\n    handleApiError(error, { operation: 'create file', resource: `file ${filePath}` });\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Helper function to normalize file paths with Unicode characters\r\nfunction normalizePathWithUnicode(path: string): string {\r\n  try {\r\n    // Replace escaped Unicode sequences with actual characters\r\n    return path.replace(/\\\\u([0-9a-fA-F]{4})/g, (_, hexCode) => {\r\n      return String.fromCharCode(parseInt(hexCode, 16));\r\n    });\r\n  } catch (e) {\r\n    console.error('Error processing Unicode escapes in path:', e);\r\n    return path;\r\n  }\r\n}\r\n\r\nexport const listSandboxFiles = async (\r\n  sandboxId: string,\r\n  path: string,\r\n): Promise<FileInfo[]> => {\r\n  try {\r\n    const supabase = createClient();\r\n    const {\r\n      data: { session },\r\n    } = await supabase.auth.getSession();\r\n\r\n    const url = new URL(`${API_URL}/sandboxes/${sandboxId}/files`);\r\n    \r\n    // Normalize the path to handle Unicode escape sequences\r\n    const normalizedPath = normalizePathWithUnicode(path);\r\n    \r\n    // Properly encode the path parameter for UTF-8 support\r\n    url.searchParams.append('path', normalizedPath);\r\n\r\n    const headers: Record<string, string> = {};\r\n    if (session?.access_token) {\r\n      headers['Authorization'] = `Bearer ${session.access_token}`;\r\n    }\r\n\r\n    const response = await fetch(url.toString(), {\r\n      headers,\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response\r\n        .text()\r\n        .catch(() => 'No error details available');\r\n      console.error(\r\n        `Error listing sandbox files: ${response.status} ${response.statusText}`,\r\n        errorText,\r\n      );\r\n      throw new Error(\r\n        `Error listing sandbox files: ${response.statusText} (${response.status})`,\r\n      );\r\n    }\r\n\r\n    const data = await response.json();\r\n    return data.files || [];\r\n  } catch (error) {\r\n    console.error('Failed to list sandbox files:', error);\r\n    // handleApiError(error, { operation: 'list files', resource: `directory ${path}` });\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const getSandboxFileContent = async (\r\n  sandboxId: string,\r\n  path: string,\r\n): Promise<string | Blob> => {\r\n  try {\r\n    const supabase = createClient();\r\n    const {\r\n      data: { session },\r\n    } = await supabase.auth.getSession();\r\n\r\n    const url = new URL(`${API_URL}/sandboxes/${sandboxId}/files/content`);\r\n    \r\n    // Normalize the path to handle Unicode escape sequences\r\n    const normalizedPath = normalizePathWithUnicode(path);\r\n    \r\n    // Properly encode the path parameter for UTF-8 support\r\n    url.searchParams.append('path', normalizedPath);\r\n\r\n    const headers: Record<string, string> = {};\r\n    if (session?.access_token) {\r\n      headers['Authorization'] = `Bearer ${session.access_token}`;\r\n    }\r\n\r\n    const response = await fetch(url.toString(), {\r\n      headers,\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response\r\n        .text()\r\n        .catch(() => 'No error details available');\r\n      console.error(\r\n        `Error getting sandbox file content: ${response.status} ${response.statusText}`,\r\n        errorText,\r\n      );\r\n      throw new Error(\r\n        `Error getting sandbox file content: ${response.statusText} (${response.status})`,\r\n      );\r\n    }\r\n\r\n    // Check if it's a text file or binary file based on content-type\r\n    const contentType = response.headers.get('content-type');\r\n    if (\r\n      (contentType && contentType.includes('text')) ||\r\n      contentType?.includes('application/json')\r\n    ) {\r\n      return await response.text();\r\n    } else {\r\n      return await response.blob();\r\n    }\r\n  } catch (error) {\r\n    console.error('Failed to get sandbox file content:', error);\r\n    handleApiError(error, { operation: 'load file content', resource: `file ${path}` });\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Function to get public projects\r\nexport const getPublicProjects = async (): Promise<Project[]> => {\r\n  try {\r\n    const supabase = createClient();\r\n\r\n    // Query for threads that are marked as public\r\n    const { data: publicThreads, error: threadsError } = await supabase\r\n      .from('threads')\r\n      .select('project_id')\r\n      .eq('is_public', true);\r\n\r\n    if (threadsError) {\r\n      console.error('Error fetching public threads:', threadsError);\r\n      return [];\r\n    }\r\n\r\n    // If no public threads found, return empty array\r\n    if (!publicThreads?.length) {\r\n      return [];\r\n    }\r\n\r\n    // Extract unique project IDs from public threads\r\n    const publicProjectIds = [\r\n      ...new Set(publicThreads.map((thread) => thread.project_id)),\r\n    ].filter(Boolean);\r\n\r\n    // If no valid project IDs, return empty array\r\n    if (!publicProjectIds.length) {\r\n      return [];\r\n    }\r\n\r\n    // Get the projects that have public threads\r\n    const { data: projects, error: projectsError } = await supabase\r\n      .from('projects')\r\n      .select('*')\r\n      .in('project_id', publicProjectIds);\r\n\r\n    if (projectsError) {\r\n      console.error('Error fetching public projects:', projectsError);\r\n      return [];\r\n    }\r\n\r\n    console.log(\r\n      '[API] Raw public projects from DB:',\r\n      projects?.length,\r\n      projects,\r\n    );\r\n\r\n    // Map database fields to our Project type\r\n    const mappedProjects: Project[] = (projects || []).map((project) => ({\r\n      id: project.project_id,\r\n      name: project.name || '',\r\n      description: project.description || '',\r\n      account_id: project.account_id,\r\n      created_at: project.created_at,\r\n      updated_at: project.updated_at,\r\n      sandbox: project.sandbox || {\r\n        id: '',\r\n        pass: '',\r\n        vnc_preview: '',\r\n        sandbox_url: '',\r\n      },\r\n      is_public: true, // Mark these as public projects\r\n    }));\r\n\r\n    console.log(\r\n      '[API] Mapped public projects for frontend:',\r\n      mappedProjects.length,\r\n    );\r\n\r\n    return mappedProjects;\r\n  } catch (err) {\r\n    console.error('Error fetching public projects:', err);\r\n    handleApiError(err, { operation: 'load public projects', resource: 'public projects' });\r\n    return [];\r\n  }\r\n};\r\n\r\n\r\nexport const initiateAgent = async (\r\n  formData: FormData,\r\n): Promise<InitiateAgentResponse> => {\r\n  try {\r\n    const supabase = createClient();\r\n    const {\r\n      data: { session },\r\n    } = await supabase.auth.getSession();\r\n\r\n    if (!session?.access_token) {\r\n      throw new NoAccessTokenAvailableError();\r\n    }\r\n\r\n    if (!API_URL) {\r\n      throw new Error(\r\n        'Backend URL is not configured. Set NEXT_PUBLIC_BACKEND_URL in your environment.',\r\n      );\r\n    }\r\n\r\n    console.log(\r\n      `[API] Initiating agent with files using ${API_URL}/agent/initiate`,\r\n    );\r\n\r\n    const response = await fetch(`${API_URL}/agent/initiate`, {\r\n      method: 'POST',\r\n      headers: {\r\n        Authorization: `Bearer ${session.access_token}`,\r\n      },\r\n      body: formData,\r\n      cache: 'no-store',\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response\r\n        .text()\r\n        .catch(() => 'No error details available');\r\n      \r\n      console.error(\r\n        `[API] Error initiating agent: ${response.status} ${response.statusText}`,\r\n        errorText,\r\n      );\r\n    \r\n      if (response.status === 402) {\r\n        throw new Error('Payment Required');\r\n      } else if (response.status === 401) {\r\n        throw new Error('Authentication error: Please sign in again');\r\n      } else if (response.status >= 500) {\r\n        throw new Error('Server error: Please try again later');\r\n      }\r\n    \r\n      throw new Error(\r\n        `Error initiating agent: ${response.statusText} (${response.status})`,\r\n      );\r\n    }\r\n\r\n    const result = await response.json();\r\n    return result;\r\n  } catch (error) {\r\n    console.error('[API] Failed to initiate agent:', error);\r\n\r\n    if (\r\n      error instanceof TypeError &&\r\n      error.message.includes('Failed to fetch')\r\n    ) {\r\n      const networkError = new Error(\r\n        `Cannot connect to backend server. Please check your internet connection and make sure the backend is running.`,\r\n      );\r\n      handleApiError(networkError, { operation: 'initiate agent', resource: 'AI assistant' });\r\n      throw networkError;\r\n    }\r\n    handleApiError(error, { operation: 'initiate agent' });\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const checkApiHealth = async (): Promise<HealthCheckResponse> => {\r\n  try {\r\n    const response = await fetch(`${API_URL}/health`, {\r\n      cache: 'no-store',\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(`API health check failed: ${response.statusText}`);\r\n    }\r\n\r\n    return response.json();\r\n  } catch (error) {\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Billing API Types\r\nexport interface CreateCheckoutSessionRequest {\r\n  price_id: string;\r\n  success_url: string;\r\n  cancel_url: string;\r\n  referral_id?: string;\r\n}\r\n\r\nexport interface CreatePortalSessionRequest {\r\n  return_url: string;\r\n}\r\n\r\nexport interface SubscriptionStatus {\r\n  status: string; // Includes 'active', 'trialing', 'past_due', 'scheduled_downgrade', 'no_subscription'\r\n  plan_name?: string;\r\n  price_id?: string; // Added\r\n  current_period_end?: string; // ISO Date string\r\n  cancel_at_period_end: boolean;\r\n  trial_end?: string; // ISO Date string\r\n  minutes_limit?: number;\r\n  cost_limit?: number;\r\n  current_usage?: number;\r\n  // Fields for scheduled changes\r\n  has_schedule: boolean;\r\n  scheduled_plan_name?: string;\r\n  scheduled_price_id?: string; // Added\r\n  scheduled_change_date?: string; // ISO Date string - Deprecate? Check backend usage\r\n  schedule_effective_date?: string; // ISO Date string - Added for consistency\r\n}\r\n\r\nexport interface BillingStatusResponse {\r\n  can_run: boolean;\r\n  message: string;\r\n  subscription: {\r\n    price_id: string;\r\n    plan_name: string;\r\n    minutes_limit?: number;\r\n  };\r\n}\r\n\r\nexport interface Model {\r\n  id: string;\r\n  display_name: string;\r\n  short_name?: string;\r\n  requires_subscription?: boolean;\r\n  is_available?: boolean;\r\n  input_cost_per_million_tokens?: number | null;\r\n  output_cost_per_million_tokens?: number | null;\r\n  max_tokens?: number | null;\r\n}\r\n\r\nexport interface AvailableModelsResponse {\r\n  models: Model[];\r\n  subscription_tier: string;\r\n  total_models: number;\r\n}\r\n\r\nexport interface UsageLogEntry {\r\n  message_id: string;\r\n  thread_id: string;\r\n  created_at: string;\r\n  content: {\r\n    usage: {\r\n      prompt_tokens: number;\r\n      completion_tokens: number;\r\n    };\r\n    model: string;\r\n  };\r\n  total_tokens: number;\r\n  estimated_cost: number;\r\n  project_id: string;\r\n}\r\n\r\nexport interface UsageLogsResponse {\r\n  logs: UsageLogEntry[];\r\n  has_more: boolean;\r\n  message?: string;\r\n}\r\n\r\nexport interface CreateCheckoutSessionResponse {\r\n  status:\r\n    | 'upgraded'\r\n    | 'downgrade_scheduled'\r\n    | 'checkout_created'\r\n    | 'no_change'\r\n    | 'new'\r\n    | 'updated'\r\n    | 'scheduled';\r\n  subscription_id?: string;\r\n  schedule_id?: string;\r\n  session_id?: string;\r\n  url?: string;\r\n  effective_date?: string;\r\n  message?: string;\r\n  details?: {\r\n    is_upgrade?: boolean;\r\n    effective_date?: string;\r\n    current_price?: number;\r\n    new_price?: number;\r\n    invoice?: {\r\n      id: string;\r\n      status: string;\r\n      amount_due: number;\r\n      amount_paid: number;\r\n    };\r\n  };\r\n}\r\n\r\n// Billing API Functions\r\nexport const createCheckoutSession = async (\r\n  request: CreateCheckoutSessionRequest,\r\n): Promise<CreateCheckoutSessionResponse> => {\r\n  try {\r\n    const supabase = createClient();\r\n    const {\r\n      data: { session },\r\n    } = await supabase.auth.getSession();\r\n\r\n    if (!session?.access_token) {\r\n      throw new NoAccessTokenAvailableError();\r\n    }\r\n    \r\n    \r\n    const requestBody = { ...request, tolt_referral: window.tolt_referral };\r\n    console.log('Tolt Referral ID:', requestBody.tolt_referral);\r\n    \r\n    const response = await fetch(`${API_URL}/billing/create-checkout-session`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        Authorization: `Bearer ${session.access_token}`,\r\n      },\r\n      body: JSON.stringify(requestBody),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response\r\n        .text()\r\n        .catch(() => 'No error details available');\r\n      console.error(\r\n        `Error creating checkout session: ${response.status} ${response.statusText}`,\r\n        errorText,\r\n      );\r\n      throw new Error(\r\n        `Error creating checkout session: ${response.statusText} (${response.status})`,\r\n      );\r\n    }\r\n\r\n    const data = await response.json();\r\n    console.log('Checkout session response:', data);\r\n\r\n    // Handle all possible statuses\r\n    switch (data.status) {\r\n      case 'upgraded':\r\n      case 'updated':\r\n      case 'downgrade_scheduled':\r\n      case 'scheduled':\r\n      case 'no_change':\r\n        return data;\r\n      case 'new':\r\n      case 'checkout_created':\r\n        if (!data.url) {\r\n          throw new Error('No checkout URL provided');\r\n        }\r\n        return data;\r\n      default:\r\n        console.warn(\r\n          'Unexpected status from createCheckoutSession:',\r\n          data.status,\r\n        );\r\n        return data;\r\n    }\r\n  } catch (error) {\r\n    console.error('Failed to create checkout session:', error);\r\n    handleApiError(error, { operation: 'create checkout session', resource: 'billing' });\r\n    throw error;\r\n  }\r\n};\r\n\r\n\r\nexport const createPortalSession = async (\r\n  request: CreatePortalSessionRequest,\r\n): Promise<{ url: string }> => {\r\n  try {\r\n    const supabase = createClient();\r\n    const {\r\n      data: { session },\r\n    } = await supabase.auth.getSession();\r\n\r\n    if (!session?.access_token) {\r\n      throw new NoAccessTokenAvailableError();\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/billing/create-portal-session`, {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n        Authorization: `Bearer ${session.access_token}`,\r\n      },\r\n      body: JSON.stringify(request),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response\r\n        .text()\r\n        .catch(() => 'No error details available');\r\n      console.error(\r\n        `Error creating portal session: ${response.status} ${response.statusText}`,\r\n        errorText,\r\n      );\r\n      throw new Error(\r\n        `Error creating portal session: ${response.statusText} (${response.status})`,\r\n      );\r\n    }\r\n\r\n    return response.json();\r\n  } catch (error) {\r\n    console.error('Failed to create portal session:', error);\r\n    handleApiError(error, { operation: 'create portal session', resource: 'billing portal' });\r\n    throw error;\r\n  }\r\n};\r\n\r\n\r\nexport const getSubscription = async (): Promise<SubscriptionStatus> => {\r\n  try {\r\n    const supabase = createClient();\r\n    const {\r\n      data: { session },\r\n    } = await supabase.auth.getSession();\r\n\r\n    if (!session?.access_token) {\r\n      throw new NoAccessTokenAvailableError();\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/billing/subscription`, {\r\n      headers: {\r\n        Authorization: `Bearer ${session.access_token}`,\r\n      },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response\r\n        .text()\r\n        .catch(() => 'No error details available');\r\n      console.error(\r\n        `Error getting subscription: ${response.status} ${response.statusText}`,\r\n        errorText,\r\n      );\r\n      throw new Error(\r\n        `Error getting subscription: ${response.statusText} (${response.status})`,\r\n      );\r\n    }\r\n\r\n    return response.json();\r\n  } catch (error) {\r\n    if (error instanceof NoAccessTokenAvailableError) {\r\n      throw error;\r\n    }\r\n\r\n    console.error('Failed to get subscription:', error);\r\n    handleApiError(error, { operation: 'load subscription', resource: 'billing information' });\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const getAvailableModels = async (): Promise<AvailableModelsResponse> => {\r\n  try {\r\n    const supabase = createClient();\r\n    const {\r\n      data: { session },\r\n    } = await supabase.auth.getSession();\r\n\r\n    if (!session?.access_token) {\r\n      throw new NoAccessTokenAvailableError();\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/billing/available-models`, {\r\n      headers: {\r\n        Authorization: `Bearer ${session.access_token}`,\r\n      },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response\r\n        .text()\r\n        .catch(() => 'No error details available');\r\n      console.error(\r\n        `Error getting available models: ${response.status} ${response.statusText}`,\r\n        errorText,\r\n      );\r\n      throw new Error(\r\n        `Error getting available models: ${response.statusText} (${response.status})`,\r\n      );\r\n    }\r\n\r\n    return response.json();\r\n  } catch (error) {\r\n    if (error instanceof NoAccessTokenAvailableError) {\r\n      throw error;\r\n    }\r\n\r\n    console.error('Failed to get available models:', error);\r\n    handleApiError(error, { operation: 'load available models', resource: 'AI models' });\r\n    throw error;\r\n  }\r\n};\r\n\r\n\r\nexport const checkBillingStatus = async (): Promise<BillingStatusResponse> => {\r\n  try {\r\n    const supabase = createClient();\r\n    const {\r\n      data: { session },\r\n    } = await supabase.auth.getSession();\r\n\r\n    if (!session?.access_token) {\r\n      throw new NoAccessTokenAvailableError();\r\n    }\r\n\r\n    const response = await fetch(`${API_URL}/billing/check-status`, {\r\n      headers: {\r\n        Authorization: `Bearer ${session.access_token}`,\r\n      },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response\r\n        .text()\r\n        .catch(() => 'No error details available');\r\n      console.error(\r\n        `Error checking billing status: ${response.status} ${response.statusText}`,\r\n        errorText,\r\n      );\r\n      throw new Error(\r\n        `Error checking billing status: ${response.statusText} (${response.status})`,\r\n      );\r\n    }\r\n\r\n    return response.json();\r\n  } catch (error) {\r\n    if (error instanceof NoAccessTokenAvailableError) {\r\n      throw error;\r\n    }\r\n\r\n    console.error('Failed to check billing status:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\n// Transcription API Types\r\nexport interface TranscriptionResponse {\r\n  text: string;\r\n}\r\n\r\n// Transcription API Functions\r\nexport const transcribeAudio = async (audioFile: File): Promise<TranscriptionResponse> => {\r\n  try {\r\n    const supabase = createClient();\r\n    const {\r\n      data: { session },\r\n    } = await supabase.auth.getSession();\r\n\r\n    if (!session?.access_token) {\r\n      throw new NoAccessTokenAvailableError();\r\n    }\r\n\r\n    const formData = new FormData();\r\n    formData.append('audio_file', audioFile);\r\n\r\n    const response = await fetch(`${API_URL}/transcription`, {\r\n      method: 'POST',\r\n      headers: {\r\n        Authorization: `Bearer ${session.access_token}`,\r\n      },\r\n      body: formData,\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response\r\n        .text()\r\n        .catch(() => 'No error details available');\r\n      console.error(\r\n        `Error transcribing audio: ${response.status} ${response.statusText}`,\r\n        errorText,\r\n      );\r\n      throw new Error(\r\n        `Error transcribing audio: ${response.statusText} (${response.status})`,\r\n      );\r\n    }\r\n\r\n    return response.json();\r\n  } catch (error) {\r\n    if (error instanceof NoAccessTokenAvailableError) {\r\n      throw error;\r\n    }\r\n\r\n    console.error('Failed to transcribe audio:', error);\r\n    handleApiError(error, { operation: 'transcribe audio', resource: 'speech-to-text' });\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const getAgentBuilderChatHistory = async (agentId: string): Promise<{messages: Message[], thread_id: string | null}> => {\r\n  const supabase = createClient();\r\n  const {\r\n    data: { session },\r\n  } = await supabase.auth.getSession();\r\n\r\n  if (!session?.access_token) {\r\n    throw new NoAccessTokenAvailableError();\r\n  }\r\n\r\n  const response = await fetch(`${API_URL}/agents/${agentId}/builder-chat-history`, {\r\n    headers: {\r\n      Authorization: `Bearer ${session.access_token}`,\r\n    },\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const errorText = await response.text().catch(() => 'No error details available');\r\n    console.error(`Error getting agent builder chat history: ${response.status} ${response.statusText}`, errorText);\r\n    throw new Error(`Error getting agent builder chat history: ${response.statusText}`);\r\n  }\r\n\r\n  const data = await response.json();\r\n  console.log('[API] Agent builder chat history fetched:', data);\r\n\r\n  return data;\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIgB;AAJhB;AACA;;;AAEA,6CAA6C;AAC7C,MAAM,UAAU,iEAAuC;AAEvD,mEAAmE;AACnE,MAAM,sBAAsB,IAAI;AAChC,kDAAkD;AAClD,MAAM,gBAAgB,IAAI;AAGnB,MAAM,qBAAqB;IAChC,OAAe;IACf,OAAgD;IAEhD,YACE,MAAc,EACd,MAA+C,EAC/C,OAAgB,CAChB;QACA,KAAK,CAAC,WAAW,OAAO,OAAO,IAAI,CAAC,eAAe,EAAE,QAAQ;QAC7D,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG;QAEd,gCAAgC;QAChC,OAAO,cAAc,CAAC,IAAI,EAAE,aAAa,SAAS;IACpD;AACF;AAEO,MAAM,oCAAoC;IAC/C,YAAY,OAAgB,EAAE,OAA2B,CAAE;QACzD,KAAK,CAAC,WAAW,6BAA6B;IAChD;IACA,OAAO,8BAA8B;AACvC;AAyIO,MAAM,cAAc;IACzB,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAE5B,+CAA+C;QAC/C,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QACxE,IAAI,WAAW;YACb,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO,EAAE;QACX;QAEA,iDAAiD;QACjD,IAAI,CAAC,SAAS,IAAI,EAAE;YAClB,QAAQ,GAAG,CAAC;YACZ,OAAO,EAAE;QACX;QAEA,qEAAqE;QACrE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,cAAc,SAAS,IAAI,CAAC,EAAE;QAEpC,IAAI,OAAO;YACT,wCAAwC;YACxC,IACE,MAAM,IAAI,KAAK,WACf,MAAM,OAAO,CAAC,QAAQ,CAAC,wBACvB;gBACA,QAAQ,KAAK,CACX;gBAEF,OAAO,EAAE,EAAE,yCAAyC;YACtD;YACA,MAAM;QACR;QAEA,QAAQ,GAAG,CAAC,+BAA+B,MAAM,QAAQ;QAEzD,0CAA0C;QAC1C,MAAM,iBAA4B,CAAC,QAAQ,EAAE,EAAE,GAAG,CAAC,CAAC,UAAY,CAAC;gBAC/D,IAAI,QAAQ,UAAU;gBACtB,MAAM,QAAQ,IAAI,IAAI;gBACtB,aAAa,QAAQ,WAAW,IAAI;gBACpC,YAAY,QAAQ,UAAU;gBAC9B,YAAY,QAAQ,UAAU;gBAC9B,YAAY,QAAQ,UAAU;gBAC9B,SAAS,QAAQ,OAAO,IAAI;oBAC1B,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,aAAa;gBACf;YACF,CAAC;QAED,QAAQ,GAAG,CAAC,uCAAuC,eAAe,MAAM;QAExE,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,KAAK;YAAE,WAAW;YAAiB,UAAU;QAAW;QACvE,oEAAoE;QACpE,OAAO,EAAE;IACX;AACF;AAEO,MAAM,aAAa,OAAO;IAC/B,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,cAAc,WACjB,MAAM;QAET,IAAI,OAAO;YACT,6DAA6D;YAC7D,IAAI,MAAM,IAAI,KAAK,YAAY;gBAC7B,MAAM,IAAI,MAAM,CAAC,qCAAqC,EAAE,WAAW;YACrE;YACA,MAAM;QACR;QAEA,QAAQ,GAAG,CAAC,mCAAmC;QAE/C,gDAAgD;QAChD,IAAI,KAAK,OAAO,EAAE,IAAI;YACpB,+CAA+C;YAC/C,MAAM,sBAAsB;gBAC1B,IAAI;oBACF,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;oBAElC,oDAAoD;oBACpD,MAAM,UAAkC;wBACtC,gBAAgB;oBAClB;oBAEA,IAAI,SAAS,cAAc;wBACzB,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;oBAC7D;oBAEA,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,UAAU,GAAG,CAAC;oBACpE,MAAM,WAAW,MAAM,MACrB,GAAG,QAAQ,SAAS,EAAE,UAAU,sBAAsB,CAAC,EACvD;wBACE,QAAQ;wBACR;oBACF;oBAGF,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,YAAY,MAAM,SACrB,IAAI,GACJ,KAAK,CAAC,IAAM;wBACf,QAAQ,IAAI,CACV,CAAC,oCAAoC,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,EAC/E;oBAEJ,OAAO;wBACL,QAAQ,GAAG,CAAC;oBACd;gBACF,EAAE,OAAO,cAAc;oBACrB,QAAQ,IAAI,CAAC,uCAAuC;gBACtD;YACF;YAEA,gDAAgD;YAChD;QACF;QAEA,0CAA0C;QAC1C,MAAM,gBAAyB;YAC7B,IAAI,KAAK,UAAU;YACnB,MAAM,KAAK,IAAI,IAAI;YACnB,aAAa,KAAK,WAAW,IAAI;YACjC,YAAY,KAAK,UAAU;YAC3B,WAAW,KAAK,SAAS,IAAI;YAC7B,YAAY,KAAK,UAAU;YAC3B,SAAS,KAAK,OAAO,IAAI;gBACvB,IAAI;gBACJ,MAAM;gBACN,aAAa;gBACb,aAAa;YACf;QACF;QAEA,mEAAmE;QAEnE,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,UAAU,CAAC,CAAC,EAAE;QACtD,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAAE,WAAW;YAAgB,UAAU,CAAC,QAAQ,EAAE,WAAW;QAAC;QACpF,MAAM;IACR;AACF;AAEO,MAAM,gBAAgB,OAC3B,aACA;IAEA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,gEAAgE;IAChE,IAAI,CAAC,WAAW;QACd,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;QAExE,IAAI,WAAW,MAAM;QACrB,IAAI,CAAC,SAAS,IAAI,EAChB,MAAM,IAAI,MAAM;QAElB,kEAAkE;QAClE,YAAY,SAAS,IAAI,CAAC,EAAE;IAC9B;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC;QACN,MAAM,YAAY,IAAI;QACtB,aAAa,YAAY,WAAW,IAAI;QACxC,YAAY;IACd,GACC,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAAE,WAAW;YAAkB,UAAU;QAAU;QACzE,MAAM;IACR;IAEA,MAAM,UAAU;QACd,IAAI,KAAK,UAAU;QACnB,MAAM,KAAK,IAAI;QACf,aAAa,KAAK,WAAW,IAAI;QACjC,YAAY,KAAK,UAAU;QAC3B,YAAY,KAAK,UAAU;QAC3B,SAAS;YAAE,IAAI;YAAI,MAAM;YAAI,aAAa;QAAG;IAC/C;IACA,OAAO;AACT;AAEO,MAAM,gBAAgB,OAC3B,WACA;IAEA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,QAAQ,GAAG,CAAC,6BAA6B;IACzC,QAAQ,GAAG,CAAC,gBAAgB;IAE5B,sCAAsC;IACtC,IAAI,CAAC,aAAa,cAAc,IAAI;QAClC,QAAQ,KAAK,CAAC,gDAAgD;QAC9D,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,MAAM,WAAW,EAAE,KAAK,EAAE,GAAG,MAAM,SACxC,IAAI,CAAC,YACL,MAAM,CAAC,MACP,EAAE,CAAC,cAAc,WACjB,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,2BAA2B;QACzC,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAAE,WAAW;YAAkB,UAAU,CAAC,QAAQ,EAAE,WAAW;QAAC;QACtF,MAAM;IACR;IAEA,IAAI,CAAC,aAAa;QAChB,MAAM,cAAc,IAAI,MAAM;QAC9B,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,aAAa;YAAE,WAAW;YAAkB,UAAU,CAAC,QAAQ,EAAE,WAAW;QAAC;QAC5F,MAAM;IACR;IAEA,wEAAwE;IACxE,wCAAmC;QACjC,OAAO,aAAa,CAClB,IAAI,YAAY,mBAAmB;YACjC,QAAQ;gBACN;gBACA,aAAa;oBACX,IAAI,YAAY,UAAU;oBAC1B,MAAM,YAAY,IAAI;oBACtB,aAAa,YAAY,WAAW;gBACtC;YACF;QACF;IAEJ;IAEA,iEAAiE;IACjE,MAAM,UAAU;QACd,IAAI,YAAY,UAAU;QAC1B,MAAM,YAAY,IAAI;QACtB,aAAa,YAAY,WAAW,IAAI;QACxC,YAAY,YAAY,UAAU;QAClC,YAAY,YAAY,UAAU;QAClC,SAAS,YAAY,OAAO,IAAI;YAC9B,IAAI;YACJ,MAAM;YACN,aAAa;YACb,aAAa;QACf;IACF;IACA,OAAO;AACT;AAEO,MAAM,gBAAgB,OAAO;IAClC,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SACrB,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,cAAc;IAEpB,IAAI,OAAO;QACT,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAAE,WAAW;YAAkB,UAAU,CAAC,QAAQ,EAAE,WAAW;QAAC;QACtF,MAAM;IACR;AACF;AAGO,MAAM,aAAa,OAAO;IAC/B,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,8CAA8C;IAC9C,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IACxE,IAAI,WAAW;QACb,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO,EAAE;IACX;IAEA,iDAAiD;IACjD,IAAI,CAAC,SAAS,IAAI,EAAE;QAClB,QAAQ,GAAG,CAAC;QACZ,OAAO,EAAE;IACX;IAEA,IAAI,QAAQ,SAAS,IAAI,CAAC,WAAW,MAAM,CAAC;IAE5C,iDAAiD;IACjD,QAAQ,MAAM,EAAE,CAAC,cAAc,SAAS,IAAI,CAAC,EAAE;IAE/C,IAAI,WAAW;QACb,QAAQ,GAAG,CAAC,0CAA0C;QACtD,QAAQ,MAAM,EAAE,CAAC,cAAc;IACjC;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;IAE9B,IAAI,OAAO;QACT,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAAE,WAAW;YAAgB,UAAU,YAAY,CAAC,oBAAoB,EAAE,WAAW,GAAG;QAAU;QACxH,MAAM;IACR;IAEA,MAAM,gBAA0B,CAAC,QAAQ,EAAE,EACxC,MAAM,CAAC,CAAC;QACP,MAAM,WAAW,OAAO,QAAQ,IAAI,CAAC;QACrC,OAAO,CAAC,SAAS,gBAAgB;IACnC,GACC,GAAG,CAAC,CAAC,SAAW,CAAC;YAChB,WAAW,OAAO,SAAS;YAC3B,YAAY,OAAO,UAAU;YAC7B,YAAY,OAAO,UAAU;YAC7B,YAAY,OAAO,UAAU;YAC7B,YAAY,OAAO,UAAU;YAC7B,UAAU,OAAO,QAAQ;QAC3B,CAAC;IACH,OAAO;AACT;AAEO,MAAM,YAAY,OAAO;IAC9B,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,UAChB,MAAM;IAET,IAAI,OAAO;QACT,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAAE,WAAW;YAAe,UAAU,CAAC,OAAO,EAAE,UAAU;QAAC;QACjF,MAAM;IACR;IAEA,OAAO;AACT;AAEO,MAAM,eAAe,OAAO;IACjC,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,8CAA8C;IAC9C,MAAM,EACJ,MAAM,EAAE,IAAI,EAAE,EACf,GAAG,MAAM,SAAS,IAAI,CAAC,OAAO;IAC/B,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,WACL,MAAM,CAAC;QACN,YAAY;QACZ,YAAY,KAAK,EAAE;IACrB,GACC,MAAM,GACN,MAAM;IAET,IAAI,OAAO;QACT,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAAE,WAAW;YAAiB,UAAU;QAAS;QACvE,MAAM;IACR;IACA,OAAO;AACT;AAEO,MAAM,iBAAiB,OAC5B,UACA;IAEA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,8FAA8F;IAC9F,MAAM,UAAU;QACd,MAAM;QACN,SAAS;IACX;IAEA,6CAA6C;IAC7C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,YAAY,MAAM,CAAC;QACvD,WAAW;QACX,MAAM;QACN,gBAAgB;QAChB,SAAS,KAAK,SAAS,CAAC;IAC1B;IAEA,IAAI,OAAO;QACT,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAAE,WAAW;YAAe,UAAU;QAAU;QACtE,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,MAAM,OAAO,EAAE;IAC1D;AACF;AAEO,MAAM,cAAc,OAAO;IAChC,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAE5B,IAAI,cAAyB,EAAE;IAC/B,IAAI,OAAO;IACX,MAAM,YAAY;IAClB,IAAI,UAAU;IAEd,MAAO,QAAS;QACd,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAC3B,IAAI,CAAC,YACL,MAAM,CAAC,CAAC;;;;;;;MAOT,CAAC,EACA,EAAE,CAAC,aAAa,UAChB,GAAG,CAAC,QAAQ,QACZ,GAAG,CAAC,QAAQ,WACZ,KAAK,CAAC,cAAc;YAAE,WAAW;QAAK,GACtC,KAAK,CAAC,MAAM,OAAO,YAAY;QAElC,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;gBAAE,WAAW;gBAAiB,UAAU,CAAC,oBAAoB,EAAE,UAAU;YAAC;YAChG,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,MAAM,OAAO,EAAE;QAC5D;QAEA,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;YAC3B,cAAc,YAAY,MAAM,CAAC;YACjC,QAAQ;YACR,UAAU,KAAK,MAAM,KAAK;QAC5B,OAAO;YACL,UAAU;QACZ;IACF;IAEA,QAAQ,GAAG,CAAC,iCAAiC,YAAY,MAAM;IAE/D,OAAO;AACT;AAGO,MAAM,aAAa,OACxB,UACA;IAQA,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAElC,IAAI,CAAC,SAAS,cAAc;YAC1B,MAAM,IAAI;QACZ;QAEA,qCAAqC;QACrC,uCAAc;;QAId;QAEA,QAAQ,GAAG,CACT,CAAC,gCAAgC,EAAE,SAAS,OAAO,EAAE,QAAQ,QAAQ,EAAE,SAAS,YAAY,CAAC;QAG/F,MAAM,iBAAiB;YACrB,YAAY;YACZ,iBAAiB;YACjB,kBAAkB;YAClB,QAAQ;YACR,UAAU;QACZ;QAEA,MAAM,eAAe;YAAE,GAAG,cAAc;YAAE,GAAG,OAAO;QAAC;QAErD,MAAM,OAAY;YAChB,YAAY,aAAa,UAAU;YACnC,iBAAiB,aAAa,eAAe;YAC7C,kBAAkB,aAAa,gBAAgB;YAC/C,QAAQ,aAAa,MAAM;QAC7B;QAEA,yCAAyC;QACzC,IAAI,aAAa,QAAQ,EAAE;YACzB,KAAK,QAAQ,GAAG,aAAa,QAAQ;QACvC;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,QAAQ,EAAE,SAAS,YAAY,CAAC,EAAE;YACxE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,eAAe,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACjD;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,uCAAuC;YACvC,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,IAAI;oBACF,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,QAAQ,KAAK,CAAC,CAAC,yCAAyC,CAAC,EAAE;oBAC3D,kDAAkD;oBAClD,MAAM,SAAS,WAAW,UAAU;wBAAE,SAAS;oBAAmB;oBAClE,IAAI,OAAO,OAAO,OAAO,KAAK,UAAU;wBACtC,OAAO,OAAO,GAAG,oBAAoB,6BAA6B;oBACpE;oBACA,MAAM,IAAI,aAAa,SAAS,MAAM,EAAE;gBAC1C,EAAE,OAAO,YAAY;oBACnB,sEAAsE;oBACtE,QAAQ,KAAK,CACX,kDACA;oBAEF,MAAM,IAAI,aACR,SAAS,MAAM,EACf;wBAAE,SAAS;oBAAmB,GAC9B,CAAC,sBAAsB,EAAE,SAAS,UAAU,CAAC,MAAM,CAAC;gBAExD;YACF;YAEA,sBAAsB;YACtB,MAAM,YAAY,MAAM,SACrB,IAAI,GACJ,KAAK,CAAC,IAAM;YACf,QAAQ,KAAK,CACX,CAAC,4BAA4B,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,EACvE;YAEF,MAAM,IAAI,MACR,CAAC,sBAAsB,EAAE,SAAS,UAAU,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC;QAEvE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,0CAA0C;QAC1C,IAAI,iBAAiB,cAAc;YACjC,MAAM;QACR;QAEA,IAAI,iBAAiB,6BAA6B;YAChD,MAAM;QACR;QAEA,QAAQ,KAAK,CAAC,gCAAgC;QAE9C,8DAA8D;QAC9D,IACE,iBAAiB,aACjB,MAAM,OAAO,CAAC,QAAQ,CAAC,oBACvB;YACA,MAAM,eAAe,IAAI,MACvB,CAAC,6GAA6G,CAAC;YAEjH,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,cAAc;gBAAE,WAAW;gBAAe,UAAU;YAAe;YAClF,MAAM;QACR;QAEA,4CAA4C;QAC5C,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAAE,WAAW;YAAe,UAAU;QAAe;QAC3E,MAAM;IACR;AACF;AAEO,MAAM,YAAY,OAAO;IAC9B,sEAAsE;IACtE,oBAAoB,GAAG,CAAC;IAExB,4BAA4B;IAC5B,MAAM,iBAAiB,cAAc,GAAG,CAAC;IACzC,IAAI,gBAAgB;QAClB,QAAQ,GAAG,CACT,CAAC,kCAAkC,EAAE,WAAW,sBAAsB,CAAC;QAEzE,eAAe,KAAK;QACpB,cAAc,MAAM,CAAC;IACvB;IAEA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;IAElC,IAAI,CAAC,SAAS,cAAc;QAC1B,MAAM,YAAY,IAAI;QACtB,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;YAAE,WAAW;YAAc,UAAU;QAAe;QAC9E,MAAM;IACR;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,WAAW,EAAE,WAAW,KAAK,CAAC,EAAE;QACtE,QAAQ;QACR,SAAS;YACP,gBAAgB;YAChB,eAAe,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;QACjD;QACA,2CAA2C;QAC3C,OAAO;IACT;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,IAAI,MAAM,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE;QAC1E,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;YAAE,WAAW;YAAc,UAAU;QAAe;QAC9E,MAAM;IACR;AACF;AAEO,MAAM,iBAAiB,OAAO;IACnC,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,YAAY;IAE7D,+DAA+D;IAC/D,IAAI,oBAAoB,GAAG,CAAC,aAAa;QACvC,QAAQ,GAAG,CACT,CAAC,gBAAgB,EAAE,WAAW,4CAA4C,CAAC;QAE7E,MAAM,IAAI,MAAM,CAAC,UAAU,EAAE,WAAW,eAAe,CAAC;IAC1D;IAEA,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAElC,IAAI,CAAC,SAAS,cAAc;YAC1B,QAAQ,KAAK,CAAC;YACd,MAAM,IAAI;QACZ;QAEA,MAAM,MAAM,GAAG,QAAQ,WAAW,EAAE,YAAY;QAChD,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,KAAK;QAEzC,MAAM,WAAW,MAAM,MAAM,KAAK;YAChC,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACjD;YACA,2CAA2C;YAC3C,OAAO;QACT;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SACrB,IAAI,GACJ,KAAK,CAAC,IAAM;YACf,QAAQ,KAAK,CACX,CAAC,kCAAkC,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,EAC7E;YAGF,0CAA0C;YAC1C,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,oBAAoB,GAAG,CAAC;YAC1B;YAEA,MAAM,IAAI,MACR,CAAC,4BAA4B,EAAE,SAAS,UAAU,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC;QAE7E;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,CAAC,oCAAoC,CAAC,EAAE;QAEpD,kDAAkD;QAClD,IAAI,KAAK,MAAM,KAAK,WAAW;YAC7B,oBAAoB,GAAG,CAAC;QAC1B;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,qCAAqC;QACnD,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAAE,WAAW;YAAoB,UAAU;YAAuB,QAAQ;QAAK;QACrG,MAAM;IACR;AACF;AAEO,MAAM,eAAe,OAAO;IACjC,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAElC,IAAI,CAAC,SAAS,cAAc;YAC1B,MAAM,IAAI;QACZ;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,QAAQ,EAAE,SAAS,WAAW,CAAC,EAAE;YACvE,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACjD;YACA,2CAA2C;YAC3C,OAAO;QACT;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,0BAA0B,EAAE,SAAS,UAAU,EAAE;QACpE;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,UAAU,IAAI,EAAE;IAC9B,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,6BAA6B;YAChD,MAAM;QACR;QAEA,QAAQ,KAAK,CAAC,6BAA6B;QAC3C,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAAE,WAAW;YAAmB,UAAU;QAAuB;QACvF,MAAM;IACR;AACF;AAEO,MAAM,cAAc,CACzB,YACA;IAMA,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,YAAY;IAE3D,qDAAqD;IACrD,IAAI,oBAAoB,GAAG,CAAC,aAAa;QACvC,QAAQ,GAAG,CACT,CAAC,mBAAmB,EAAE,WAAW,gDAAgD,CAAC;QAEpF,gCAAgC;QAChC,WAAW;YACT,UAAU,OAAO,CAAC,CAAC,UAAU,EAAE,WAAW,eAAe,CAAC;YAC1D,UAAU,OAAO;QACnB,GAAG;QAEH,kCAAkC;QAClC,OAAO,KAAO;IAChB;IAEA,+DAA+D;IAC/D,MAAM,iBAAiB,cAAc,GAAG,CAAC;IACzC,IAAI,gBAAgB;QAClB,QAAQ,GAAG,CACT,CAAC,mCAAmC,EAAE,WAAW,kBAAkB,CAAC;QAEtE,eAAe,KAAK;QACpB,cAAc,MAAM,CAAC;IACvB;IAEA,sBAAsB;IACtB,IAAI;QACF,MAAM,cAAc;YAClB,6CAA6C;YAC7C,IAAI;gBACF,MAAM,SAAS,MAAM,eAAe;gBACpC,IAAI,OAAO,MAAM,KAAK,WAAW;oBAC/B,QAAQ,GAAG,CACT,CAAC,mBAAmB,EAAE,WAAW,yBAAyB,EAAE,OAAO,MAAM,CAAC,sBAAsB,CAAC;oBAEnG,oBAAoB,GAAG,CAAC;oBACxB,UAAU,OAAO,CACf,CAAC,UAAU,EAAE,WAAW,yBAAyB,EAAE,OAAO,MAAM,CAAC,CAAC,CAAC;oBAErE,UAAU,OAAO;oBACjB;gBACF;YACF,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,WAAW,CAAC,CAAC,EAAE;gBAEnE,uCAAuC;gBACvC,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG,OAAO;gBACjE,MAAM,kBACJ,aAAa,QAAQ,CAAC,gBACtB,aAAa,QAAQ,CAAC,UACtB,aAAa,QAAQ,CAAC;gBAExB,IAAI,iBAAiB;oBACnB,QAAQ,GAAG,CACT,CAAC,mBAAmB,EAAE,WAAW,+BAA+B,CAAC;oBAEnE,oBAAoB,GAAG,CAAC;gBAC1B;gBAEA,UAAU,OAAO,CAAC;gBAClB,UAAU,OAAO;gBACjB;YACF;YAEA,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;YAC5B,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;YAElC,IAAI,CAAC,SAAS,cAAc;gBAC1B,MAAM,YAAY,IAAI;gBACtB,QAAQ,KAAK,CAAC;gBACd,UAAU,OAAO,CAAC;gBAClB,UAAU,OAAO;gBACjB;YACF;YAEA,MAAM,MAAM,IAAI,IAAI,GAAG,QAAQ,WAAW,EAAE,WAAW,OAAO,CAAC;YAC/D,IAAI,YAAY,CAAC,MAAM,CAAC,SAAS,QAAQ,YAAY;YAErD,QAAQ,GAAG,CAAC,CAAC,kCAAkC,EAAE,YAAY;YAC7D,MAAM,cAAc,IAAI,YAAY,IAAI,QAAQ;YAEhD,kDAAkD;YAClD,cAAc,GAAG,CAAC,YAAY;YAE9B,YAAY,MAAM,GAAG;gBACnB,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,YAAY;YAC5D;YAEA,YAAY,SAAS,GAAG,CAAC;gBACvB,IAAI;oBACF,MAAM,UAAU,MAAM,IAAI;oBAC1B,IAAI,QAAQ,QAAQ,CAAC,kBAAkB;oBAEvC,yDAAyD;oBACzD,QAAQ,GAAG,CACT,CAAC,2BAA2B,EAAE,WAAW,EAAE,EAAE,QAAQ,SAAS,CAAC,GAAG,OAAO,QAAQ,MAAM,GAAG,MAAM,QAAQ,IAAI;oBAG9G,sBAAsB;oBACtB,IAAI,CAAC,WAAW,QAAQ,IAAI,OAAO,IAAI;wBACrC,QAAQ,KAAK,CAAC;wBACd;oBACF;oBAEA,kCAAkC;oBAClC,IAAI;wBACF,MAAM,WAAW,KAAK,KAAK,CAAC;wBAC5B,IAAI,SAAS,MAAM,KAAK,SAAS;4BAC/B,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,WAAW,CAAC,CAAC,EAAE;4BAEnE,yCAAyC;4BACzC,UAAU,OAAO,CAAC,SAAS,OAAO,IAAI;4BAEtC,wEAAwE;4BACxE;wBACF;oBACF,EAAE,OAAO,WAAW;oBAClB,4DAA4D;oBAC9D;oBAEA,wCAAwC;oBACxC,IACE,QAAQ,QAAQ,CAAC,gBACjB,QAAQ,QAAQ,CAAC,6BACjB;wBACA,QAAQ,GAAG,CACT,CAAC,mBAAmB,EAAE,WAAW,yCAAyC,CAAC;wBAG7E,iEAAiE;wBACjE,oBAAoB,GAAG,CAAC;wBAExB,yBAAyB;wBACzB,UAAU,OAAO,CAAC;wBAElB,WAAW;wBACX,YAAY,KAAK;wBACjB,cAAc,MAAM,CAAC;wBACrB,UAAU,OAAO;wBAEjB;oBACF;oBAEA,gCAAgC;oBAChC,IACE,QAAQ,QAAQ,CAAC,sBACjB,QAAQ,QAAQ,CAAC,yBACjB;wBACA,QAAQ,GAAG,CACT,CAAC,gDAAgD,EAAE,YAAY;wBAGjE,+EAA+E;wBAC/E,IACE,QAAQ,QAAQ,CAAC,2CACjB,QAAQ,QAAQ,CAAC,wCACjB;4BACA,QAAQ,GAAG,CACT,CAAC,+CAA+C,EAAE,WAAW,2BAA2B,CAAC;4BAE3F,iEAAiE;4BACjE,oBAAoB,GAAG,CAAC;wBAC1B;wBAEA,2BAA2B;wBAC3B,UAAU,SAAS,CAAC;wBAEpB,WAAW;wBACX,YAAY,KAAK;wBACjB,cAAc,MAAM,CAAC;wBACrB,UAAU,OAAO;wBAEjB;oBACF;oBAEA,mCAAmC;oBACnC,IACE,QAAQ,QAAQ,CAAC,sBACjB,QAAQ,QAAQ,CAAC,mCACjB;wBACA,QAAQ,GAAG,CACT,CAAC,6CAA6C,EAAE,YAAY;wBAG9D,yBAAyB;wBACzB,oBAAoB,GAAG,CAAC;wBAExB,2BAA2B;wBAC3B,UAAU,SAAS,CAAC;wBAEpB,WAAW;wBACX,YAAY,KAAK;wBACjB,cAAc,MAAM,CAAC;wBACrB,UAAU,OAAO;wBAEjB;oBACF;oBAEA,iDAAiD;oBACjD,UAAU,SAAS,CAAC;gBACtB,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,CAAC,EAAE;oBAClD,UAAU,OAAO,CAAC,iBAAiB,QAAQ,QAAQ,OAAO;gBAC5D;YACF;YAEA,YAAY,OAAO,GAAG,CAAC;gBACrB,QAAQ,GAAG,CAAC,CAAC,+BAA+B,EAAE,WAAW,CAAC,CAAC,EAAE;gBAE7D,sCAAsC;gBACtC,eAAe,YACZ,IAAI,CAAC,CAAC;oBACL,IAAI,OAAO,MAAM,KAAK,WAAW;wBAC/B,QAAQ,GAAG,CACT,CAAC,mBAAmB,EAAE,WAAW,2CAA2C,CAAC;wBAE/E,oBAAoB,GAAG,CAAC;wBACxB,YAAY,KAAK;wBACjB,cAAc,MAAM,CAAC;wBACrB,UAAU,OAAO;oBACnB,OAAO;wBACL,QAAQ,GAAG,CACT,CAAC,mBAAmB,EAAE,WAAW,kDAAkD,CAAC;oBAEtF,2DAA2D;oBAC7D;gBACF,GACC,KAAK,CAAC,CAAC;oBACN,QAAQ,KAAK,CACX,CAAC,wDAAwD,CAAC,EAC1D;oBAGF,uCAAuC;oBACvC,MAAM,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG,OAAO;oBAC3D,MAAM,gBACJ,OAAO,QAAQ,CAAC,gBAChB,OAAO,QAAQ,CAAC,UAChB,OAAO,QAAQ,CAAC;oBAElB,IAAI,eAAe;wBACjB,QAAQ,GAAG,CACT,CAAC,mBAAmB,EAAE,WAAW,sCAAsC,CAAC;wBAE1E,oBAAoB,GAAG,CAAC;wBACxB,YAAY,KAAK;wBACjB,cAAc,MAAM,CAAC;wBACrB,UAAU,OAAO;oBACnB;oBAEA,sDAAsD;oBACtD,UAAU,OAAO,CAAC;gBACpB;YACJ;QACF;QAEA,yBAAyB;QACzB;QAEA,4BAA4B;QAC5B,OAAO;YACL,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,YAAY;YACvD,MAAM,SAAS,cAAc,GAAG,CAAC;YACjC,IAAI,QAAQ;gBACV,QAAQ,GAAG,CAAC,CAAC,4BAA4B,EAAE,YAAY;gBACvD,OAAO,KAAK;gBACZ,cAAc,MAAM,CAAC;YACvB;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,qCAAqC,EAAE,WAAW,CAAC,CAAC,EAAE;QACrE,UAAU,OAAO,CAAC,iBAAiB,QAAQ,QAAQ,OAAO;QAC1D,UAAU,OAAO;QACjB,OAAO,KAAO;IAChB;AACF;AAGO,MAAM,oBAAoB,OAC/B,WACA,UACA;IAEA,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAElC,oEAAoE;QACpE,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,6DAA6D;QAC7D,MAAM,OAAO,IAAI,KAAK;YAAC;SAAQ,EAAE;YAAE,MAAM;QAA2B;QACpE,SAAS,MAAM,CAAC,QAAQ,MAAM,SAAS,KAAK,CAAC,KAAK,GAAG,MAAM;QAE3D,MAAM,UAAkC,CAAC;QACzC,IAAI,SAAS,cAAc;YACzB,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;QAC7D;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,WAAW,EAAE,UAAU,MAAM,CAAC,EAAE;YACtE,QAAQ;YACR;YACA,MAAM;QACR;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SACrB,IAAI,GACJ,KAAK,CAAC,IAAM;YACf,QAAQ,KAAK,CACX,CAAC,6BAA6B,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,EACxE;YAEF,MAAM,IAAI,MACR,CAAC,6BAA6B,EAAE,SAAS,UAAU,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC;QAE9E;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;QAChD,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAAE,WAAW;YAAe,UAAU,CAAC,KAAK,EAAE,UAAU;QAAC;QAC/E,MAAM;IACR;AACF;AAGO,MAAM,wBAAwB,OACnC,WACA,UACA;IAEA,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAElC,MAAM,UAAkC;YACtC,gBAAgB;QAClB;QAEA,IAAI,SAAS,cAAc;YACzB,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;QAC7D;QAEA,MAAM,WAAW,MAAM,MACrB,GAAG,QAAQ,WAAW,EAAE,UAAU,WAAW,CAAC,EAC9C;YACE,QAAQ;YACR;YACA,MAAM,KAAK,SAAS,CAAC;gBACnB,MAAM;gBACN,SAAS;YACX;QACF;QAGF,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SACrB,IAAI,GACJ,KAAK,CAAC,IAAM;YACf,QAAQ,KAAK,CACX,CAAC,oCAAoC,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,EAC/E;YAEF,MAAM,IAAI,MACR,CAAC,6BAA6B,EAAE,SAAS,UAAU,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC;QAE9E;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAAE,WAAW;YAAe,UAAU,CAAC,KAAK,EAAE,UAAU;QAAC;QAC/E,MAAM;IACR;AACF;AAEA,kEAAkE;AAClE,SAAS,yBAAyB,IAAY;IAC5C,IAAI;QACF,2DAA2D;QAC3D,OAAO,KAAK,OAAO,CAAC,wBAAwB,CAAC,GAAG;YAC9C,OAAO,OAAO,YAAY,CAAC,SAAS,SAAS;QAC/C;IACF,EAAE,OAAO,GAAG;QACV,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,OAAO;IACT;AACF;AAEO,MAAM,mBAAmB,OAC9B,WACA;IAEA,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAElC,MAAM,MAAM,IAAI,IAAI,GAAG,QAAQ,WAAW,EAAE,UAAU,MAAM,CAAC;QAE7D,wDAAwD;QACxD,MAAM,iBAAiB,yBAAyB;QAEhD,uDAAuD;QACvD,IAAI,YAAY,CAAC,MAAM,CAAC,QAAQ;QAEhC,MAAM,UAAkC,CAAC;QACzC,IAAI,SAAS,cAAc;YACzB,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;QAC7D;QAEA,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ,IAAI;YAC3C;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SACrB,IAAI,GACJ,KAAK,CAAC,IAAM;YACf,QAAQ,KAAK,CACX,CAAC,6BAA6B,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,EACxE;YAEF,MAAM,IAAI,MACR,CAAC,6BAA6B,EAAE,SAAS,UAAU,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC;QAE9E;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,KAAK,IAAI,EAAE;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,qFAAqF;QACrF,MAAM;IACR;AACF;AAEO,MAAM,wBAAwB,OACnC,WACA;IAEA,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAElC,MAAM,MAAM,IAAI,IAAI,GAAG,QAAQ,WAAW,EAAE,UAAU,cAAc,CAAC;QAErE,wDAAwD;QACxD,MAAM,iBAAiB,yBAAyB;QAEhD,uDAAuD;QACvD,IAAI,YAAY,CAAC,MAAM,CAAC,QAAQ;QAEhC,MAAM,UAAkC,CAAC;QACzC,IAAI,SAAS,cAAc;YACzB,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;QAC7D;QAEA,MAAM,WAAW,MAAM,MAAM,IAAI,QAAQ,IAAI;YAC3C;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SACrB,IAAI,GACJ,KAAK,CAAC,IAAM;YACf,QAAQ,KAAK,CACX,CAAC,oCAAoC,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,EAC/E;YAEF,MAAM,IAAI,MACR,CAAC,oCAAoC,EAAE,SAAS,UAAU,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC;QAErF;QAEA,iEAAiE;QACjE,MAAM,cAAc,SAAS,OAAO,CAAC,GAAG,CAAC;QACzC,IACE,AAAC,eAAe,YAAY,QAAQ,CAAC,WACrC,aAAa,SAAS,qBACtB;YACA,OAAO,MAAM,SAAS,IAAI;QAC5B,OAAO;YACL,OAAO,MAAM,SAAS,IAAI;QAC5B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAAE,WAAW;YAAqB,UAAU,CAAC,KAAK,EAAE,MAAM;QAAC;QACjF,MAAM;IACR;AACF;AAGO,MAAM,oBAAoB;IAC/B,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAE5B,8CAA8C;QAC9C,MAAM,EAAE,MAAM,aAAa,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,SACxD,IAAI,CAAC,WACL,MAAM,CAAC,cACP,EAAE,CAAC,aAAa;QAEnB,IAAI,cAAc;YAChB,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO,EAAE;QACX;QAEA,iDAAiD;QACjD,IAAI,CAAC,eAAe,QAAQ;YAC1B,OAAO,EAAE;QACX;QAEA,iDAAiD;QACjD,MAAM,mBAAmB;eACpB,IAAI,IAAI,cAAc,GAAG,CAAC,CAAC,SAAW,OAAO,UAAU;SAC3D,CAAC,MAAM,CAAC;QAET,8CAA8C;QAC9C,IAAI,CAAC,iBAAiB,MAAM,EAAE;YAC5B,OAAO,EAAE;QACX;QAEA,4CAA4C;QAC5C,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,SACpD,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,cAAc;QAEpB,IAAI,eAAe;YACjB,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO,EAAE;QACX;QAEA,QAAQ,GAAG,CACT,sCACA,UAAU,QACV;QAGF,0CAA0C;QAC1C,MAAM,iBAA4B,CAAC,YAAY,EAAE,EAAE,GAAG,CAAC,CAAC,UAAY,CAAC;gBACnE,IAAI,QAAQ,UAAU;gBACtB,MAAM,QAAQ,IAAI,IAAI;gBACtB,aAAa,QAAQ,WAAW,IAAI;gBACpC,YAAY,QAAQ,UAAU;gBAC9B,YAAY,QAAQ,UAAU;gBAC9B,YAAY,QAAQ,UAAU;gBAC9B,SAAS,QAAQ,OAAO,IAAI;oBAC1B,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,aAAa;gBACf;gBACA,WAAW;YACb,CAAC;QAED,QAAQ,GAAG,CACT,8CACA,eAAe,MAAM;QAGvB,OAAO;IACT,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,mCAAmC;QACjD,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,KAAK;YAAE,WAAW;YAAwB,UAAU;QAAkB;QACrF,OAAO,EAAE;IACX;AACF;AAGO,MAAM,gBAAgB,OAC3B;IAEA,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAElC,IAAI,CAAC,SAAS,cAAc;YAC1B,MAAM,IAAI;QACZ;QAEA,uCAAc;;QAId;QAEA,QAAQ,GAAG,CACT,CAAC,wCAAwC,EAAE,QAAQ,eAAe,CAAC;QAGrE,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,eAAe,CAAC,EAAE;YACxD,QAAQ;YACR,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACjD;YACA,MAAM;YACN,OAAO;QACT;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SACrB,IAAI,GACJ,KAAK,CAAC,IAAM;YAEf,QAAQ,KAAK,CACX,CAAC,8BAA8B,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,EACzE;YAGF,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,MAAM,IAAI,MAAM;YAClB,OAAO,IAAI,SAAS,MAAM,KAAK,KAAK;gBAClC,MAAM,IAAI,MAAM;YAClB,OAAO,IAAI,SAAS,MAAM,IAAI,KAAK;gBACjC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,IAAI,MACR,CAAC,wBAAwB,EAAE,SAAS,UAAU,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC;QAEzE;QAEA,MAAM,SAAS,MAAM,SAAS,IAAI;QAClC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QAEjD,IACE,iBAAiB,aACjB,MAAM,OAAO,CAAC,QAAQ,CAAC,oBACvB;YACA,MAAM,eAAe,IAAI,MACvB,CAAC,6GAA6G,CAAC;YAEjH,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,cAAc;gBAAE,WAAW;gBAAkB,UAAU;YAAe;YACrF,MAAM;QACR;QACA,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAAE,WAAW;QAAiB;QACpD,MAAM;IACR;AACF;AAEO,MAAM,iBAAiB;IAC5B,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,OAAO,CAAC,EAAE;YAChD,OAAO;QACT;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE;QACnE;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,MAAM;IACR;AACF;AA+GO,MAAM,wBAAwB,OACnC;IAEA,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAElC,IAAI,CAAC,SAAS,cAAc;YAC1B,MAAM,IAAI;QACZ;QAGA,MAAM,cAAc;YAAE,GAAG,OAAO;YAAE,eAAe,OAAO,aAAa;QAAC;QACtE,QAAQ,GAAG,CAAC,qBAAqB,YAAY,aAAa;QAE1D,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,gCAAgC,CAAC,EAAE;YACzE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,eAAe,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACjD;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SACrB,IAAI,GACJ,KAAK,CAAC,IAAM;YACf,QAAQ,KAAK,CACX,CAAC,iCAAiC,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,EAC5E;YAEF,MAAM,IAAI,MACR,CAAC,iCAAiC,EAAE,SAAS,UAAU,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC;QAElF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,8BAA8B;QAE1C,+BAA+B;QAC/B,OAAQ,KAAK,MAAM;YACjB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,IAAI,CAAC,KAAK,GAAG,EAAE;oBACb,MAAM,IAAI,MAAM;gBAClB;gBACA,OAAO;YACT;gBACE,QAAQ,IAAI,CACV,iDACA,KAAK,MAAM;gBAEb,OAAO;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAAE,WAAW;YAA2B,UAAU;QAAU;QAClF,MAAM;IACR;AACF;AAGO,MAAM,sBAAsB,OACjC;IAEA,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAElC,IAAI,CAAC,SAAS,cAAc;YAC1B,MAAM,IAAI;QACZ;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,8BAA8B,CAAC,EAAE;YACvE,QAAQ;YACR,SAAS;gBACP,gBAAgB;gBAChB,eAAe,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACjD;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SACrB,IAAI,GACJ,KAAK,CAAC,IAAM;YACf,QAAQ,KAAK,CACX,CAAC,+BAA+B,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,EAC1E;YAEF,MAAM,IAAI,MACR,CAAC,+BAA+B,EAAE,SAAS,UAAU,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC;QAEhF;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAAE,WAAW;YAAyB,UAAU;QAAiB;QACvF,MAAM;IACR;AACF;AAGO,MAAM,kBAAkB;IAC7B,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAElC,IAAI,CAAC,SAAS,cAAc;YAC1B,MAAM,IAAI;QACZ;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,qBAAqB,CAAC,EAAE;YAC9D,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACjD;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SACrB,IAAI,GACJ,KAAK,CAAC,IAAM;YACf,QAAQ,KAAK,CACX,CAAC,4BAA4B,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,EACvE;YAEF,MAAM,IAAI,MACR,CAAC,4BAA4B,EAAE,SAAS,UAAU,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC;QAE7E;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,6BAA6B;YAChD,MAAM;QACR;QAEA,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAAE,WAAW;YAAqB,UAAU;QAAsB;QACxF,MAAM;IACR;AACF;AAEO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAElC,IAAI,CAAC,SAAS,cAAc;YAC1B,MAAM,IAAI;QACZ;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,yBAAyB,CAAC,EAAE;YAClE,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACjD;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SACrB,IAAI,GACJ,KAAK,CAAC,IAAM;YACf,QAAQ,KAAK,CACX,CAAC,gCAAgC,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,EAC3E;YAEF,MAAM,IAAI,MACR,CAAC,gCAAgC,EAAE,SAAS,UAAU,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC;QAEjF;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,6BAA6B;YAChD,MAAM;QACR;QAEA,QAAQ,KAAK,CAAC,mCAAmC;QACjD,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAAE,WAAW;YAAyB,UAAU;QAAY;QAClF,MAAM;IACR;AACF;AAGO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAElC,IAAI,CAAC,SAAS,cAAc;YAC1B,MAAM,IAAI;QACZ;QAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,qBAAqB,CAAC,EAAE;YAC9D,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACjD;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SACrB,IAAI,GACJ,KAAK,CAAC,IAAM;YACf,QAAQ,KAAK,CACX,CAAC,+BAA+B,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,EAC1E;YAEF,MAAM,IAAI,MACR,CAAC,+BAA+B,EAAE,SAAS,UAAU,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC;QAEhF;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,6BAA6B;YAChD,MAAM;QACR;QAEA,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAQO,MAAM,kBAAkB,OAAO;IACpC,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;QAC5B,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAElC,IAAI,CAAC,SAAS,cAAc;YAC1B,MAAM,IAAI;QACZ;QAEA,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,cAAc;QAE9B,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,cAAc,CAAC,EAAE;YACvD,QAAQ;YACR,SAAS;gBACP,eAAe,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;YACjD;YACA,MAAM;QACR;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SACrB,IAAI,GACJ,KAAK,CAAC,IAAM;YACf,QAAQ,KAAK,CACX,CAAC,0BAA0B,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,EACrE;YAEF,MAAM,IAAI,MACR,CAAC,0BAA0B,EAAE,SAAS,UAAU,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC,CAAC,CAAC;QAE3E;QAEA,OAAO,SAAS,IAAI;IACtB,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,6BAA6B;YAChD,MAAM;QACR;QAEA,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;YAAE,WAAW;YAAoB,UAAU;QAAiB;QAClF,MAAM;IACR;AACF;AAEO,MAAM,6BAA6B,OAAO;IAC/C,MAAM,WAAW,CAAA,GAAA,mIAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;IAElC,IAAI,CAAC,SAAS,cAAc;QAC1B,MAAM,IAAI;IACZ;IAEA,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,QAAQ,EAAE,QAAQ,qBAAqB,CAAC,EAAE;QAChF,SAAS;YACP,eAAe,CAAC,OAAO,EAAE,QAAQ,YAAY,EAAE;QACjD;IACF;IAEA,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM;QACpD,QAAQ,KAAK,CAAC,CAAC,0CAA0C,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,EAAE;QACrG,MAAM,IAAI,MAAM,CAAC,0CAA0C,EAAE,SAAS,UAAU,EAAE;IACpF;IAEA,MAAM,OAAO,MAAM,SAAS,IAAI;IAChC,QAAQ,GAAG,CAAC,6CAA6C;IAEzD,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1477, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/lib/error-handler.ts"], "sourcesContent": ["import { toast } from 'sonner';\r\nimport { BillingError } from './api';\r\n\r\nexport interface ApiError extends Error {\r\n  status?: number;\r\n  code?: string;\r\n  details?: any;\r\n  response?: Response;\r\n}\r\n\r\nexport interface ErrorContext {\r\n  operation?: string;\r\n  resource?: string;\r\n  silent?: boolean;\r\n}\r\n\r\nconst getStatusMessage = (status: number): string => {\r\n  switch (status) {\r\n    case 400:\r\n      return 'Invalid request. Please check your input and try again.';\r\n    case 401:\r\n      return 'Authentication required. Please sign in again.';\r\n    case 403:\r\n      return 'Access denied. You don\\'t have permission to perform this action.';\r\n    case 404:\r\n      return 'The requested resource was not found.';\r\n    case 408:\r\n      return 'Request timeout. Please try again.';\r\n    case 409:\r\n      return 'Conflict detected. The resource may have been modified by another user.';\r\n    case 422:\r\n      return 'Invalid data provided. Please check your input.';\r\n    case 429:\r\n      return 'Too many requests. Please wait a moment and try again.';\r\n    case 500:\r\n      return 'Server error. Our team has been notified.';\r\n    case 502:\r\n      return 'Service temporarily unavailable. Please try again in a moment.';\r\n    case 503:\r\n      return 'Service maintenance in progress. Please try again later.';\r\n    case 504:\r\n      return 'Request timeout. The server took too long to respond.';\r\n    default:\r\n      return 'An unexpected error occurred. Please try again.';\r\n  }\r\n};\r\n\r\nconst extractErrorMessage = (error: any): string => {\r\n  if (error instanceof BillingError) {\r\n    return error.detail?.message || error.message || 'Billing issue detected';\r\n  }\r\n\r\n  if (error instanceof Error) {\r\n    return error.message;\r\n  }\r\n\r\n  if (error?.response) {\r\n    const status = error.response.status;\r\n    return getStatusMessage(status);\r\n  }\r\n\r\n  if (error?.status) {\r\n    return getStatusMessage(error.status);\r\n  }\r\n\r\n  if (typeof error === 'string') {\r\n    return error;\r\n  }\r\n\r\n  if (error?.message) {\r\n    return error.message;\r\n  }\r\n\r\n  if (error?.error) {\r\n    return typeof error.error === 'string' ? error.error : error.error.message || 'Unknown error';\r\n  }\r\n\r\n  return 'An unexpected error occurred';\r\n};\r\n\r\nconst shouldShowError = (error: any, context?: ErrorContext): boolean => {\r\n  if (context?.silent) {\r\n    return false;\r\n  }\r\n  if (error instanceof BillingError) {\r\n    return false;\r\n  }\r\n\r\n  if (error?.status === 404 && context?.resource) {\r\n    return false;\r\n  }\r\n\r\n  return true;\r\n};\r\n\r\nconst formatErrorMessage = (message: string, context?: ErrorContext): string => {\r\n  if (!context?.operation && !context?.resource) {\r\n    return message;\r\n  }\r\n\r\n  const parts = [];\r\n  \r\n  if (context.operation) {\r\n    parts.push(`Failed to ${context.operation}`);\r\n  }\r\n  \r\n  if (context.resource) {\r\n    parts.push(context.resource);\r\n  }\r\n\r\n  const prefix = parts.join(' ');\r\n  \r\n  if (message.toLowerCase().includes(context.operation?.toLowerCase() || '')) {\r\n    return message;\r\n  }\r\n\r\n  return `${prefix}: ${message}`;\r\n};\r\n\r\n\r\nexport const handleApiError = (error: any, context?: ErrorContext): void => {\r\n  console.error('API Error:', error, context);\r\n\r\n  if (!shouldShowError(error, context)) {\r\n    return;\r\n  }\r\n\r\n  const rawMessage = extractErrorMessage(error);\r\n  const formattedMessage = formatErrorMessage(rawMessage, context);\r\n\r\n  if (error?.status >= 500) {\r\n    toast.error(formattedMessage, {\r\n      description: 'Our team has been notified and is working on a fix.',\r\n      duration: 6000,\r\n    });\r\n  } else if (error?.status === 401) {\r\n    toast.error(formattedMessage, {\r\n      description: 'Please refresh the page and sign in again.',\r\n      duration: 8000,\r\n    });\r\n  } else if (error?.status === 403) {\r\n    toast.error(formattedMessage, {\r\n      description: 'Contact support if you believe this is an error.',\r\n      duration: 6000,\r\n    });\r\n  } else if (error?.status === 429) {\r\n    toast.warning(formattedMessage, {\r\n      description: 'Please wait a moment before trying again.',\r\n      duration: 5000,\r\n    });\r\n  } else {\r\n    toast.error(formattedMessage, {\r\n      duration: 5000,\r\n    });\r\n  }\r\n};\r\n\r\nexport const handleNetworkError = (error: any, context?: ErrorContext): void => {\r\n  const isNetworkError = \r\n    error?.message?.includes('fetch') ||\r\n    error?.message?.includes('network') ||\r\n    error?.message?.includes('connection') ||\r\n    error?.code === 'NETWORK_ERROR' ||\r\n    !navigator.onLine;\r\n\r\n  if (isNetworkError) {\r\n    toast.error('Connection error', {\r\n      description: 'Please check your internet connection and try again.',\r\n      duration: 6000,\r\n    });\r\n  } else {\r\n    handleApiError(error, context);\r\n  }\r\n};\r\n\r\nexport const handleApiSuccess = (message: string, description?: string): void => {\r\n  toast.success(message, {\r\n    description,\r\n    duration: 3000,\r\n  });\r\n};\r\n\r\nexport const handleApiWarning = (message: string, description?: string): void => {\r\n  toast.warning(message, {\r\n    description,\r\n    duration: 4000,\r\n  });\r\n};\r\n\r\nexport const handleApiInfo = (message: string, description?: string): void => {\r\n  toast.info(message, {\r\n    description,\r\n    duration: 3000,\r\n  });\r\n}; "], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAeA,MAAM,mBAAmB,CAAC;IACxB,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,MAAM,sBAAsB,CAAC;IAC3B,IAAI,iBAAiB,oHAAA,CAAA,eAAY,EAAE;QACjC,OAAO,MAAM,MAAM,EAAE,WAAW,MAAM,OAAO,IAAI;IACnD;IAEA,IAAI,iBAAiB,OAAO;QAC1B,OAAO,MAAM,OAAO;IACtB;IAEA,IAAI,OAAO,UAAU;QACnB,MAAM,SAAS,MAAM,QAAQ,CAAC,MAAM;QACpC,OAAO,iBAAiB;IAC1B;IAEA,IAAI,OAAO,QAAQ;QACjB,OAAO,iBAAiB,MAAM,MAAM;IACtC;IAEA,IAAI,OAAO,UAAU,UAAU;QAC7B,OAAO;IACT;IAEA,IAAI,OAAO,SAAS;QAClB,OAAO,MAAM,OAAO;IACtB;IAEA,IAAI,OAAO,OAAO;QAChB,OAAO,OAAO,MAAM,KAAK,KAAK,WAAW,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,OAAO,IAAI;IAChF;IAEA,OAAO;AACT;AAEA,MAAM,kBAAkB,CAAC,OAAY;IACnC,IAAI,SAAS,QAAQ;QACnB,OAAO;IACT;IACA,IAAI,iBAAiB,oHAAA,CAAA,eAAY,EAAE;QACjC,OAAO;IACT;IAEA,IAAI,OAAO,WAAW,OAAO,SAAS,UAAU;QAC9C,OAAO;IACT;IAEA,OAAO;AACT;AAEA,MAAM,qBAAqB,CAAC,SAAiB;IAC3C,IAAI,CAAC,SAAS,aAAa,CAAC,SAAS,UAAU;QAC7C,OAAO;IACT;IAEA,MAAM,QAAQ,EAAE;IAEhB,IAAI,QAAQ,SAAS,EAAE;QACrB,MAAM,IAAI,CAAC,CAAC,UAAU,EAAE,QAAQ,SAAS,EAAE;IAC7C;IAEA,IAAI,QAAQ,QAAQ,EAAE;QACpB,MAAM,IAAI,CAAC,QAAQ,QAAQ;IAC7B;IAEA,MAAM,SAAS,MAAM,IAAI,CAAC;IAE1B,IAAI,QAAQ,WAAW,GAAG,QAAQ,CAAC,QAAQ,SAAS,EAAE,iBAAiB,KAAK;QAC1E,OAAO;IACT;IAEA,OAAO,GAAG,OAAO,EAAE,EAAE,SAAS;AAChC;AAGO,MAAM,iBAAiB,CAAC,OAAY;IACzC,QAAQ,KAAK,CAAC,cAAc,OAAO;IAEnC,IAAI,CAAC,gBAAgB,OAAO,UAAU;QACpC;IACF;IAEA,MAAM,aAAa,oBAAoB;IACvC,MAAM,mBAAmB,mBAAmB,YAAY;IAExD,IAAI,OAAO,UAAU,KAAK;QACxB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,kBAAkB;YAC5B,aAAa;YACb,UAAU;QACZ;IACF,OAAO,IAAI,OAAO,WAAW,KAAK;QAChC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,kBAAkB;YAC5B,aAAa;YACb,UAAU;QACZ;IACF,OAAO,IAAI,OAAO,WAAW,KAAK;QAChC,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,kBAAkB;YAC5B,aAAa;YACb,UAAU;QACZ;IACF,OAAO,IAAI,OAAO,WAAW,KAAK;QAChC,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB;YAC9B,aAAa;YACb,UAAU;QACZ;IACF,OAAO;QACL,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,kBAAkB;YAC5B,UAAU;QACZ;IACF;AACF;AAEO,MAAM,qBAAqB,CAAC,OAAY;IAC7C,MAAM,iBACJ,OAAO,SAAS,SAAS,YACzB,OAAO,SAAS,SAAS,cACzB,OAAO,SAAS,SAAS,iBACzB,OAAO,SAAS,mBAChB,CAAC,UAAU,MAAM;IAEnB,IAAI,gBAAgB;QAClB,2IAAA,CAAA,QAAK,CAAC,KAAK,CAAC,oBAAoB;YAC9B,aAAa;YACb,UAAU;QACZ;IACF,OAAO;QACL,eAAe,OAAO;IACxB;AACF;AAEO,MAAM,mBAAmB,CAAC,SAAiB;IAChD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS;QACrB;QACA,UAAU;IACZ;AACF;AAEO,MAAM,mBAAmB,CAAC,SAAiB;IAChD,2IAAA,CAAA,QAAK,CAAC,OAAO,CAAC,SAAS;QACrB;QACA,UAAU;IACZ;AACF;AAEO,MAAM,gBAAgB,CAAC,SAAiB;IAC7C,2IAAA,CAAA,QAAK,CAAC,IAAI,CAAC,SAAS;QAClB;QACA,UAAU;IACZ;AACF", "debugId": null}}, {"offset": {"line": 1643, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/providers/react-query-provider.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport {\r\n  HydrationBoundary,\r\n  QueryClient,\r\n  QueryClientProvider,\r\n} from '@tanstack/react-query';\r\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools';\r\nimport { handleApiError } from '@/lib/error-handler';\r\n\r\nexport function ReactQueryProvider({\r\n  children,\r\n  dehydratedState,\r\n}: {\r\n  children: React.ReactNode;\r\n  dehydratedState?: unknown;\r\n}) {\r\n  const [queryClient] = useState(\r\n    () =>\r\n      new QueryClient({\r\n        defaultOptions: {\r\n          queries: {\r\n            staleTime: 20 * 1000,\r\n            gcTime: 5 * 60 * 1000,\r\n            retry: (failureCount, error: any) => {\r\n              if (error?.status >= 400 && error?.status < 500) return false;\r\n              if (error?.status === 404) return false;\r\n              return failureCount < 3;\r\n            },\r\n            refetchOnMount: true,\r\n            refetchOnWindowFocus: true,\r\n            refetchOnReconnect: 'always',\r\n          },\r\n          mutations: {\r\n            retry: (failureCount, error: any) => {\r\n              if (error?.status >= 400 && error?.status < 500) return false;\r\n              return failureCount < 1;\r\n            },\r\n            onError: (error: any, variables: any, context: any) => {\r\n              handleApiError(error, {\r\n                operation: 'perform action',\r\n                silent: false,\r\n              });\r\n            },\r\n          },\r\n        },\r\n      }),\r\n  );\r\n\r\n  return (\r\n    <QueryClientProvider client={queryClient}>\r\n      <HydrationBoundary state={dehydratedState}>\r\n        {children}\r\n        {/* {process.env.NODE_ENV !== 'production' && (\r\n          <ReactQueryDevtools initialIsOpen={false} />\r\n        )} */}\r\n      </HydrationBoundary>\r\n    </QueryClientProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAMA;;;AATA;;;;AAWO,SAAS,mBAAmB,EACjC,QAAQ,EACR,eAAe,EAIhB;;IACC,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;uCAC3B,IACE,IAAI,gLAAA,CAAA,cAAW,CAAC;gBACd,gBAAgB;oBACd,SAAS;wBACP,WAAW,KAAK;wBAChB,QAAQ,IAAI,KAAK;wBACjB,KAAK;2DAAE,CAAC,cAAc;gCACpB,IAAI,OAAO,UAAU,OAAO,OAAO,SAAS,KAAK,OAAO;gCACxD,IAAI,OAAO,WAAW,KAAK,OAAO;gCAClC,OAAO,eAAe;4BACxB;;wBACA,gBAAgB;wBAChB,sBAAsB;wBACtB,oBAAoB;oBACtB;oBACA,WAAW;wBACT,KAAK;2DAAE,CAAC,cAAc;gCACpB,IAAI,OAAO,UAAU,OAAO,OAAO,SAAS,KAAK,OAAO;gCACxD,OAAO,eAAe;4BACxB;;wBACA,OAAO;2DAAE,CAAC,OAAY,WAAgB;gCACpC,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,OAAO;oCACpB,WAAW;oCACX,QAAQ;gCACV;4BACF;;oBACF;gBACF;YACF;;IAGJ,qBACE,6LAAC,yLAAA,CAAA,sBAAmB;QAAC,QAAQ;kBAC3B,cAAA,6LAAC,uLAAA,CAAA,oBAAiB;YAAC,OAAO;sBACvB;;;;;;;;;;;AAOT;GAjDgB;KAAA", "debugId": null}}, {"offset": {"line": 1725, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/app/providers.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { ThemeProvider } from 'next-themes';\r\nimport { useState, createContext, useEffect } from 'react';\r\nimport { AuthProvider } from '@/components/AuthProvider';\r\nimport { ReactQueryProvider } from '@/providers/react-query-provider';\r\nimport { dehydrate, QueryClient } from '@tanstack/react-query';\r\n\r\nexport interface ParsedTag {\r\n  tagName: string;\r\n  attributes: Record<string, string>;\r\n  content: string;\r\n  isClosing: boolean;\r\n  id: string; // Unique ID for each tool call instance\r\n  rawMatch?: string; // Raw XML match for deduplication\r\n  timestamp?: number; // Timestamp when the tag was created\r\n\r\n  // Pairing and completion status\r\n  resultTag?: ParsedTag; // Reference to the result tag if this is a tool call\r\n  isToolCall?: boolean; // Whether this is a tool call (vs a result)\r\n  isPaired?: boolean; // Whether this tag has been paired with its call/result\r\n  status?: 'running' | 'completed' | 'error'; // Status of the tool call\r\n\r\n  // VNC preview for browser-related tools\r\n  vncPreview?: string; // VNC preview image URL\r\n}\r\n\r\n// Create the context here instead of importing it\r\nexport const ToolCallsContext = createContext<{\r\n  toolCalls: ParsedTag[];\r\n  setToolCalls: React.Dispatch<React.SetStateAction<ParsedTag[]>>;\r\n}>({\r\n  toolCalls: [],\r\n  setToolCalls: () => { },\r\n});\r\n\r\nexport function Providers({ children }: { children: React.ReactNode }) {\r\n  // Shared state for tool calls across the app\r\n  const [toolCalls, setToolCalls] = useState<ParsedTag[]>([]);\r\n  const queryClient = new QueryClient();\r\n  const dehydratedState = dehydrate(queryClient);\r\n\r\n  return (\r\n    <AuthProvider>\r\n      <ToolCallsContext.Provider value={{ toolCalls, setToolCalls }}>\r\n        <ThemeProvider attribute=\"class\" defaultTheme=\"system\" enableSystem>\r\n          <ReactQueryProvider dehydratedState={dehydratedState}>\r\n            {children}\r\n          </ReactQueryProvider>\r\n        </ThemeProvider>\r\n      </ToolCallsContext.Provider>\r\n    </AuthProvider>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;;;AANA;;;;;;AA4BO,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAGzC;IACD,WAAW,EAAE;IACb,cAAc,KAAQ;AACxB;AAEO,SAAS,UAAU,EAAE,QAAQ,EAAiC;;IACnE,6CAA6C;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC1D,MAAM,cAAc,IAAI,gLAAA,CAAA,cAAW;IACnC,MAAM,kBAAkB,CAAA,GAAA,8KAAA,CAAA,YAAS,AAAD,EAAE;IAElC,qBACE,6LAAC,qIAAA,CAAA,eAAY;kBACX,cAAA,6LAAC,iBAAiB,QAAQ;YAAC,OAAO;gBAAE;gBAAW;YAAa;sBAC1D,cAAA,6LAAC,mJAAA,CAAA,gBAAa;gBAAC,WAAU;gBAAQ,cAAa;gBAAS,YAAY;0BACjE,cAAA,6LAAC,kJAAA,CAAA,qBAAkB;oBAAC,iBAAiB;8BAClC;;;;;;;;;;;;;;;;;;;;;AAMb;GAjBgB;KAAA", "debugId": null}}, {"offset": {"line": 1801, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/suna/frontend/src/components/ui/sonner.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useTheme } from 'next-themes';\r\nimport { Toaster as Sonner, ToasterProps } from 'sonner';\r\n\r\nconst Toaster = ({ ...props }: ToasterProps) => {\r\n  const { theme = 'system' } = useTheme();\r\n\r\n  return (\r\n    <Sonner\r\n      theme={theme as ToasterProps['theme']}\r\n      className=\"toaster group\"\r\n      style={\r\n        {\r\n          '--normal-bg': 'var(--popover)',\r\n          '--normal-text': 'var(--popover-foreground)',\r\n          '--normal-border': 'var(--border)',\r\n          '--border-radius': '1rem',\r\n        } as React.CSSProperties\r\n      }\r\n      {...props}\r\n    />\r\n  );\r\n};\r\n\r\nexport { Toaster };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,6LAAC,2IAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;YACnB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf;GAlBM;;QACyB,mJAAA,CAAA,WAAQ;;;KADjC", "debugId": null}}]}