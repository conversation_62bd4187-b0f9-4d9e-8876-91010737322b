"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2814],{7601:(e,s,t)=>{t.d(s,{X:()=>r});var a=t(95155);function r(e){let{children:s}=e;return(0,a.jsx)("div",{className:"border-b w-full h-full p-10 md:p-14",children:(0,a.jsx)("div",{className:"max-w-xl mx-auto flex flex-col items-center justify-center gap-2",children:s})})}},10401:(e,s,t)=>{t.d(s,{Zo:()=>x,un:()=>u,L6:()=>h,ve:()=>m,WY:()=>d,Il:()=>g,uv:()=>f,xE:()=>v});var a=t(48988),r=t(12115),l=t(23843),n=t(99090),i=t(25731),o=t(16183);let c=(0,n.GQ)(o._1.available,i.cL,{staleTime:3e5,refetchOnWindowFocus:!1,retry:2,select:e=>({...e,models:[...e.models].sort((e,s)=>e.display_name.localeCompare(s.display_name))})}),d="suna-preferred-model-v2",m="customModels",u="claude-sonnet-4",x="claude-sonnet-4",h={"claude-sonnet-4":{tier:"free",priority:100,recommended:!0,lowQuality:!1},"gemini-flash-2.5":{tier:"free",priority:70,recommended:!1,lowQuality:!1},qwen3:{tier:"free",priority:60,recommended:!1,lowQuality:!1},"sonnet-3.7":{tier:"premium",priority:99,recommended:!1,lowQuality:!1},"grok-4":{tier:"premium",priority:98,recommended:!1,lowQuality:!1},"google/gemini-2.5-pro":{tier:"premium",priority:97,recommended:!1,lowQuality:!1},"gpt-4.1":{tier:"premium",priority:96,recommended:!1,lowQuality:!1},"sonnet-3.5":{tier:"premium",priority:90,recommended:!1,lowQuality:!1},"gpt-4o":{tier:"premium",priority:88,recommended:!1,lowQuality:!1},"gemini-2.5-flash:thinking":{tier:"premium",priority:84,recommended:!1,lowQuality:!1},"deepseek/deepseek-chat-v3-0324":{tier:"premium",priority:75,recommended:!1,lowQuality:!1}},p=(e,s)=>!!(0,l.Jn)()||"active"===e||!s,g=e=>e.split("-").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" "),f=()=>{if(!(0,l.Jn)())return[];try{let e=localStorage.getItem(m);if(!e)return[];let s=JSON.parse(e);if(!Array.isArray(s))return[];return s.filter(e=>e&&"object"==typeof e&&"string"==typeof e.id&&"string"==typeof e.label)}catch(e){return console.error("Error parsing custom models:",e),[]}},j=e=>{try{localStorage.setItem(d,e)}catch(e){console.warn("Failed to save model preference to localStorage:",e)}},v=()=>{let[e,s]=(0,r.useState)(x),[t,n]=(0,r.useState)([]),[i,o]=(0,r.useState)(!1),{data:m}=(0,a.Rs)(),{data:v,isLoading:b}=c({refetchOnMount:!1}),w=(null==m?void 0:m.status)==="active"?"active":"no_subscription",y=()=>{(0,l.Jn)()&&n(f())};(0,r.useEffect)(()=>{y()},[]);let N=(0,r.useMemo)(()=>{let e=[];if((null==v?void 0:v.models)&&!b)e=v.models.map(e=>{let s=e.short_name||e.id,t=e.display_name||s;t.includes("/")&&(t=t.split("/").pop()||t),t=t.replace(/-/g," ").split(" ").map(e=>e.charAt(0).toUpperCase()+e.slice(1)).join(" ");let a=h[s]||{};return{id:s,label:t,requiresSubscription:(null==e?void 0:e.requires_subscription)||"premium"===a.tier||!1,top:a.priority>=90,priority:a.priority||0,lowQuality:a.lowQuality||!1,recommended:a.recommended||!1}});else{var s,a;e=[{id:x,label:"DeepSeek",requiresSubscription:!1,priority:(null==(s=h[x])?void 0:s.priority)||50},{id:u,label:"Sonnet 4",requiresSubscription:!0,priority:(null==(a=h[u])?void 0:a.priority)||100}]}return(0,l.Jn)()&&t.length>0&&(e=[...e,...t.map(e=>({id:e.id,label:e.label||g(e.id),requiresSubscription:!1,top:!1,isCustom:!0,priority:30,lowQuality:!1,recommended:!1}))]),e.sort((e,s)=>e.recommended!==s.recommended?e.recommended?-1:1:e.priority!==s.priority?s.priority-e.priority:e.label.localeCompare(s.label))},[v,b,t]),k=(0,r.useMemo)(()=>(0,l.Jn)()?N:N.filter(e=>p(w,e.requiresSubscription)),[N,w]);(0,r.useEffect)(()=>{if(!i){console.log("Initializing model selection from localStorage...");try{let a=localStorage.getItem(d);if(console.log("Saved model from localStorage:",a),a){if(b)return void console.log("Models still loading, waiting...");let r=N.find(e=>e.id===a),n=(0,l.Jn)()&&t.some(e=>e.id===a);if(r||n){var e;if((0,l.Jn)()||p(w,null!=(e=null==r?void 0:r.requiresSubscription)&&e)){console.log("Using saved model:",a),s(a),o(!0);return}console.log("Saved model not accessible, falling back to default")}else console.log("Saved model not found in available models, falling back to default")}let r="active"===w?u:x;console.log("Using default model:",r),s(r),j(r),o(!0)}catch(t){console.warn("Failed to load preferences from localStorage:",t);let e="active"===w?u:x;s(e),j(e),o(!0)}}},[w,N,b,t,i]);let _=e=>{var a;console.log("handleModelChange called with:",e),(0,l.Jn)()&&y();let r=(0,l.Jn)()&&t.some(s=>s.id===e),n=N.find(s=>s.id===e);if(!n&&!r){console.warn("Model not found in options:",e,N,r,t);let a=(0,l.Jn)()?u:x;s(a),j(a);return}if(!r&&!(0,l.Jn)()&&!p(w,null!=(a=null==n?void 0:n.requiresSubscription)&&a))return void console.warn("Model not accessible:",e);console.log("Setting selected model and saving to localStorage:",e),s(e),j(e)};return{selectedModel:e,setSelectedModel:e=>{_(e)},subscriptionStatus:w,availableModels:k,allModels:N,customModels:t,getActualModelId:e=>e,refreshCustomModels:y,canAccessModel:e=>{if((0,l.Jn)())return!0;let s=N.find(s=>s.id===e);return!!s&&p(w,s.requiresSubscription)},isSubscriptionRequired:e=>{var s;return(null==(s=N.find(s=>s.id===e))?void 0:s.requiresSubscription)||!1}}}},14636:(e,s,t)=>{t.d(s,{AM:()=>n,Wv:()=>i,hl:()=>o});var a=t(95155);t(12115);var r=t(20547),l=t(59434);function n(e){let{...s}=e;return(0,a.jsx)(r.bL,{"data-slot":"popover",...s})}function i(e){let{...s}=e;return(0,a.jsx)(r.l9,{"data-slot":"popover-trigger",...s})}function o(e){let{className:s,align:t="center",sideOffset:n=4,...i}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsx)(r.UC,{"data-slot":"popover-content",align:t,sideOffset:n,className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",s),...i})})}},30356:(e,s,t)=>{t.d(s,{C:()=>o,z:()=>i});var a=t(95155);t(12115);var r=t(54059),l=t(9428),n=t(59434);function i(e){let{className:s,...t}=e;return(0,a.jsx)(r.bL,{"data-slot":"radio-group",className:(0,n.cn)("grid gap-3",s),...t})}function o(e){let{className:s,...t}=e;return(0,a.jsx)(r.q7,{"data-slot":"radio-group-item",className:(0,n.cn)("border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",s),...t,children:(0,a.jsx)(r.C1,{"data-slot":"radio-group-indicator",className:"relative flex items-center justify-center",children:(0,a.jsx)(l.A,{className:"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2"})})})}},36742:(e,s,t)=>{t.d(s,{X3:()=>p,Ci:()=>g,Fo:()=>j,AM:()=>v,Vb:()=>f});var a=t(99090),r=t(26715),l=t(56671);let n={all:["agent-workflows"],agent:e=>[...n.all,e],workflow:(e,s)=>[...n.agent(e),s],executions:(e,s)=>[...n.workflow(e,s),"executions"]};var i=t(52643),o=t(17711);let c="http://localhost:8000/api",d=async e=>{try{if(!await (0,o.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let s=(0,i.U)(),{data:{session:t}}=await s.auth.getSession();if(!t)throw Error("You must be logged in to get workflows");let a=await fetch("".concat(c,"/agents/").concat(e,"/workflows"),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t.access_token)}});if(!a.ok){let e=await a.json().catch(()=>({detail:"Unknown error"}));throw Error(e.detail||"HTTP ".concat(a.status,": ").concat(a.statusText))}let r=await a.json();return console.log("[API] Fetched workflows for agent:",e,r.length),r}catch(e){throw console.error("Error fetching workflows:",e),e}},m=async(e,s)=>{try{if(!await (0,o.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let t=(0,i.U)(),{data:{session:a}}=await t.auth.getSession();if(!a)throw Error("You must be logged in to create a workflow");let r=await fetch("".concat(c,"/agents/").concat(e,"/workflows"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a.access_token)},body:JSON.stringify(s)});if(!r.ok){let e=await r.json().catch(()=>({detail:"Unknown error"}));throw Error(e.detail||"HTTP ".concat(r.status,": ").concat(r.statusText))}let l=await r.json();return console.log("[API] Created workflow:",l.id),l}catch(e){throw console.error("Error creating workflow:",e),e}},u=async(e,s,t)=>{try{if(console.log("[API] Updating workflow:",t),!await (0,o.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let a=(0,i.U)(),{data:{session:r}}=await a.auth.getSession();if(!r)throw Error("You must be logged in to update a workflow");let l=await fetch("".concat(c,"/agents/").concat(e,"/workflows/").concat(s),{method:"PUT",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r.access_token)},body:JSON.stringify(t)});if(!l.ok){let e=await l.json().catch(()=>({detail:"Unknown error"}));throw Error(e.detail||"HTTP ".concat(l.status,": ").concat(l.statusText))}let n=await l.json();return console.log("[API] Updated workflow:",n.id),n}catch(e){throw console.error("Error updating workflow:",e),e}},x=async(e,s)=>{try{if(!await (0,o.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let t=(0,i.U)(),{data:{session:a}}=await t.auth.getSession();if(!a)throw Error("You must be logged in to delete a workflow");let r=await fetch("".concat(c,"/agents/").concat(e,"/workflows/").concat(s),{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a.access_token)}});if(!r.ok){let e=await r.json().catch(()=>({detail:"Unknown error"}));throw Error(e.detail||"HTTP ".concat(r.status,": ").concat(r.statusText))}console.log("[API] Deleted workflow:",s)}catch(e){throw console.error("Error deleting workflow:",e),e}},h=async(e,s,t)=>{try{if(!await (0,o.Ej)("custom_agents"))throw Error("Custom agents is not enabled");let a=(0,i.U)(),{data:{session:r}}=await a.auth.getSession();if(!r)throw Error("You must be logged in to execute a workflow");let l=await fetch("".concat(c,"/agents/").concat(e,"/workflows/").concat(s,"/execute"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r.access_token)},body:JSON.stringify(t)});if(!l.ok){let e=await l.json().catch(()=>({detail:"Unknown error"}));throw Error(e.detail||"HTTP ".concat(l.status,": ").concat(l.statusText))}let n=await l.json();return console.log("[API] Executed workflow:",s,"execution:",n.execution_id),n}catch(e){throw console.error("Error executing workflow:",e),e}},p=e=>(0,a.GQ)(n.agent(e),()=>d(e),{enabled:!!e,staleTime:3e4})(),g=()=>{let e=(0,r.jE)();return(0,a.Lx)(e=>{let{agentId:s,workflow:t}=e;return m(s,t)},{onSuccess:(s,t)=>{e.invalidateQueries({queryKey:n.agent(t.agentId)}),l.oR.success("Workflow created successfully")}})()},f=()=>{let e=(0,r.jE)();return(0,a.Lx)(e=>{let{agentId:s,workflowId:t,workflow:a}=e;return u(s,t,a)},{onSuccess:(s,t)=>{e.invalidateQueries({queryKey:n.agent(t.agentId)}),l.oR.success("Workflow updated successfully")}})()},j=()=>{let e=(0,r.jE)();return(0,a.Lx)(e=>{let{agentId:s,workflowId:t}=e;return x(s,t)},{onSuccess:(s,t)=>{e.invalidateQueries({queryKey:n.agent(t.agentId)}),l.oR.success("Workflow deleted successfully")}})()},v=()=>{let e=(0,r.jE)();return(0,a.Lx)(e=>{let{agentId:s,workflowId:t,execution:a}=e;return h(s,t,a)},{onSuccess:(s,t)=>{e.invalidateQueries({queryKey:n.executions(t.agentId,t.workflowId)}),l.oR.success("Workflow execution started")}})()}},40538:(e,s,t)=>{t.d(s,{Y:()=>X});var a=t(95155),r=t(12115),l=t(30285),n=t(62523),i=t(85057),o=t(88539),c=t(59409),d=t(26126),m=t(68856),u=t(17313),x=t(66695),h=t(34869),p=t(99890),g=t(2775),f=t(39022),j=t(57434),v=t(40646),b=t(54861),w=t(53904),y=t(14186),N=t(85339),k=t(29869),_=t(47924),C=t(84616),S=t(25657),A=t(78749),E=t(44020),L=t(56287),P=t(92657),F=t(62525),I=t(5040),T=t(57100),z=t(51154),R=t(54416),M=t(44838),D=t(90010),U=t(54165),$=t(69794),O=t(59434),q=t(56671),W=t(59311),Z=t.n(W),B=t(59432),J=t(29911);let Q=[{value:"always",label:"Always Active",icon:h.A,color:"bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800"}],K=(e,s)=>{var t;let a=null==(t=e.split(".").pop())?void 0:t.toLowerCase();switch(a){case"js":return B.AeH;case"ts":return B.cyb;case"jsx":case"tsx":return B.GWR;case"py":return B.ptC;case"html":return B.$gB;case"css":return B.jrh;case"json":return B.zUx;case"md":return B.S_6;case"yaml":case"yml":return B.WDB;case"xml":return B.Q_I;case"pdf":return J.kl1;case"doc":case"docx":return J.WLb;case"xls":case"xlsx":case"csv":return J.Ru;case"png":case"jpg":case"jpeg":case"gif":case"svg":case"webp":case"ico":return J.R1W;case"zip":case"rar":case"7z":case"tar":case"gz":return J.aw1;default:if(["java","cpp","c","cs","php","rb","go","rs","swift","kt","scala"].includes(a||""))return J.reA;if(["txt","rtf","log"].includes(a||""))return J.t69;return J.Gp9}},G=e=>{var s;switch(null==(s=e.split(".").pop())?void 0:s.toLowerCase()){case"js":return"text-yellow-500";case"ts":case"tsx":return"text-blue-500";case"jsx":return"text-cyan-500";case"py":return"text-green-600";case"html":return"text-orange-600";case"css":return"text-blue-600";case"json":return"text-yellow-600";case"md":return"text-gray-700 dark:text-gray-300";case"yaml":case"yml":return"text-red-500";case"xml":return"text-orange-500";case"pdf":return"text-red-600";case"doc":case"docx":return"text-blue-700";case"xls":case"xlsx":case"csv":return"text-green-700";case"png":case"jpg":case"jpeg":case"gif":case"svg":case"webp":case"ico":return"text-purple-500";case"zip":case"rar":case"7z":case"tar":case"gz":return"text-yellow-700";default:return"text-gray-500"}},Y=(e,s)=>{switch(e){case"file":return s?K(s):p.A;case"git_repo":return g.A;case"zip_extracted":return f.A;default:return j.A}},V=()=>(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"relative w-full",children:(0,a.jsx)(m.Skeleton,{className:"h-10 w-full"})}),(0,a.jsx)(m.Skeleton,{className:"h-10 w-32 ml-4"})]}),(0,a.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,a.jsx)("div",{className:"border rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0 space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(m.Skeleton,{className:"h-4 w-4"}),(0,a.jsx)(m.Skeleton,{className:"h-5 w-48"}),(0,a.jsx)(m.Skeleton,{className:"h-5 w-20"})]}),(0,a.jsx)(m.Skeleton,{className:"h-4 w-64"}),(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)(m.Skeleton,{className:"h-4 w-full"}),(0,a.jsx)(m.Skeleton,{className:"h-4 w-3/4"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(m.Skeleton,{className:"h-5 w-24"}),(0,a.jsx)(m.Skeleton,{className:"h-4 w-20"})]}),(0,a.jsx)(m.Skeleton,{className:"h-4 w-16"})]})]}),(0,a.jsx)(m.Skeleton,{className:"h-8 w-8"})]})},e))})]}),X=e=>{let{agentId:s,agentName:t}=e,[m,h]=(0,r.useState)({isOpen:!1}),[p,g]=(0,r.useState)(null),[f,j]=(0,r.useState)(""),[W,B]=(0,r.useState)(!1),[J,X]=(0,r.useState)("manual"),[H,ee]=(0,r.useState)(!1),[es,et]=(0,r.useState)([]),ea=(0,r.useRef)(null),[er,el]=(0,r.useState)({name:"",description:"",content:"",usage_context:"always"}),{data:en,isLoading:ei,error:eo}=(0,$.Cv)(s),{data:ec}=(0,$.ku)(s),ed=(0,$.CR)(),em=(0,$.kq)(),eu=(0,$.QJ)(),ex=(0,$.Dv)();(0,$.rW)();let eh=(0,r.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?ee(!0):"dragleave"===e.type&&ee(!1)},[]),ep=(0,r.useCallback)(e=>{e.preventDefault(),e.stopPropagation(),ee(!1),e.dataTransfer.files&&e.dataTransfer.files[0]&&eN(e.dataTransfer.files)},[]),eg=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"manual";X(e),B(!0),el({name:"",description:"",content:"",usage_context:"always"}),et([])},ef=e=>{el({name:e.name,description:e.description||"",content:e.content,usage_context:e.usage_context}),h({entry:e,isOpen:!0})},ej=()=>{h({isOpen:!1}),B(!1),el({name:"",description:"",content:"",usage_context:"always"}),et([])},ev=async e=>{if(e.preventDefault(),er.name.trim()&&er.content.trim())try{if(m.entry){let e={name:er.name!==m.entry.name?er.name:void 0,description:er.description!==m.entry.description?er.description:void 0,content:er.content!==m.entry.content?er.content:void 0,usage_context:er.usage_context!==m.entry.usage_context?er.usage_context:void 0};Object.values(e).some(e=>void 0!==e)&&await em.mutateAsync({entryId:m.entry.entry_id,data:e})}else await ed.mutateAsync({agentId:s,data:er});ej()}catch(e){console.error("Error saving agent knowledge base entry:",e)}},eb=async e=>{try{await eu.mutateAsync(e),g(null)}catch(e){console.error("Error deleting agent knowledge base entry:",e)}},ew=async e=>{try{await em.mutateAsync({entryId:e.entry_id,data:{is_active:!e.is_active}})}catch(e){console.error("Error toggling entry status:",e)}},ey=async(e,s)=>{try{et(e=>e.map(e=>e.id===s?{...e,status:"extracting"}:e));let t=new(Z()),a=await t.loadAsync(e),r=[];for(let[e,t]of Object.entries(a.files))if(!t.dir&&!e.startsWith("__MACOSX/")&&!e.includes("/."))try{let a=await t.async("blob"),l=e.split("/").pop()||e,n=new File([a],l);r.push({file:n,id:Math.random().toString(36).substr(2,9),status:"pending",isFromZip:!0,zipParentId:s,originalPath:e})}catch(s){console.warn("Failed to extract ".concat(e,":"),s)}et(e=>[...e.map(e=>e.id===s?{...e,status:"success"}:e),...r]),q.oR.success("Extracted ".concat(r.length," files from ").concat(e.name))}catch(e){console.error("Error extracting ZIP:",e),et(e=>e.map(e=>e.id===s?{...e,status:"error",error:"Failed to extract ZIP file"}:e)),q.oR.error("Failed to extract ZIP file")}},eN=async e=>{if(!e||0===e.length)return;let s=[];for(let t of Array.from(e)){let e=Math.random().toString(36).substr(2,9),a={file:t,id:e,status:"pending"};s.push(a),t.name.toLowerCase().endsWith(".zip")&&setTimeout(()=>ey(t,e),100)}et(e=>[...e,...s]),W||(X("files"),B(!0))},ek=async()=>{for(let e of es.filter(e=>"pending"===e.status&&(e.isFromZip||!e.file.name.toLowerCase().endsWith(".zip"))))try{et(s=>s.map(s=>s.id===e.id?{...s,status:"uploading"}:s)),await ex.mutateAsync({agentId:s,file:e.file}),et(s=>s.map(s=>s.id===e.id?{...s,status:"success"}:s))}catch(s){et(t=>t.map(t=>t.id===e.id?{...t,status:"error",error:s instanceof Error?s.message:"Upload failed"}:t))}setTimeout(()=>{es.filter(e=>!e.file.name.toLowerCase().endsWith(".zip")||e.isFromZip).every(e=>"success"===e.status)&&ej()},1e3)},e_=e=>{et(s=>s.filter(s=>s.id!==e))},eC=e=>Q.find(s=>s.value===e)||Q[0],eS=e=>{switch(e){case"completed":return v.A;case"failed":return b.A;case"processing":return w.A;default:return y.A}},eA=e=>{switch(e){case"completed":return"text-green-600";case"failed":return"text-red-600";case"processing":return"text-blue-600";default:return"text-yellow-600"}};if(ei)return(0,a.jsx)(V,{});if(eo)return(0,a.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(N.A,{className:"h-8 w-8 text-red-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:"Failed to load agent knowledge base"})]})});let eE=(null==en?void 0:en.entries)||[],eL=(null==ec?void 0:ec.jobs)||[],eP=eE.filter(e=>e.name.toLowerCase().includes(f.toLowerCase())||e.content.toLowerCase().includes(f.toLowerCase())||e.description&&e.description.toLowerCase().includes(f.toLowerCase()));return(0,a.jsxs)("div",{className:"space-y-6",onDragEnter:eh,onDragLeave:eh,onDragOver:eh,onDrop:ep,children:[H&&(0,a.jsx)("div",{className:"fixed inset-0 bg-blue-500/20 backdrop-blur-sm z-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-900 rounded-lg p-8 shadow-lg border-2 border-dashed border-blue-500",children:[(0,a.jsx)(k.A,{className:"h-12 w-12 text-blue-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-lg font-medium text-center",children:"Drop files here to upload"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground text-center mt-2",children:"Supports documents, images, code files, and ZIP archives"})]})}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"relative flex-1 max-w-md",children:[(0,a.jsx)(_.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(n.p,{placeholder:"Search knowledge entries...",value:f,onChange:e=>j(e.target.value),className:"pl-9"})]}),(0,a.jsxs)(l.$,{onClick:()=>eg(),className:"gap-2",children:[(0,a.jsx)(C.A,{className:"h-4 w-4"}),"Add Knowledge"]})]}),0===eE.length?(0,a.jsxs)("div",{className:"text-center py-12 px-6 bg-muted/30 rounded-xl border-2 border-dashed border-border",children:[(0,a.jsx)("div",{className:"mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-4 border",children:(0,a.jsx)(S.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,a.jsx)("h3",{className:"text-sm font-semibold mb-2",children:"No Agent Knowledge Entries"}),(0,a.jsxs)("p",{className:"text-muted-foreground mb-6 max-w-sm mx-auto",children:["Add knowledge entries to provide ",(0,a.jsx)("span",{className:"font-medium",children:t})," with specialized context, guidelines, and information it should always remember."]})]}):(0,a.jsx)("div",{className:"space-y-3",children:0===eP.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(_.A,{className:"h-8 w-8 mx-auto text-muted-foreground/50 mb-2"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"No entries match your search"})]}):eP.map(e=>{var s;let t=eC(e.usage_context),r=t.icon,n=Y(e.source_type||"manual",null==(s=e.source_metadata)?void 0:s.filename);return(0,a.jsx)(x.Zp,{className:(0,O.cn)("group transition-all p-0",e.is_active?"bg-card":"bg-muted/30 opacity-70"),children:(0,a.jsx)(x.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-start justify-between gap-3",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0 space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(n,{className:"h-4 w-4 text-muted-foreground flex-shrink-0"}),(0,a.jsx)("h3",{className:"font-medium truncate",children:e.name}),!e.is_active&&(0,a.jsxs)(d.E,{variant:"outline",className:"text-xs",children:[(0,a.jsx)(A.A,{className:"h-3 w-3 mr-1"}),"Disabled"]}),e.source_type&&"manual"!==e.source_type&&(0,a.jsx)(d.E,{variant:"outline",className:"text-xs",children:"git_repo"===e.source_type?"Git":"zip_extracted"===e.source_type?"ZIP":"File"})]}),e.description&&(0,a.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-1",children:e.description}),(0,a.jsx)("p",{className:"text-sm text-foreground/80 line-clamp-2 leading-relaxed",children:e.content}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)(d.E,{variant:"outline",className:(0,O.cn)("text-xs gap-1",t.color),children:[(0,a.jsx)(r,{className:"h-3 w-3"}),t.label]}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground flex items-center gap-1",children:[(0,a.jsx)(y.A,{className:"h-3 w-3"}),new Date(e.created_at).toLocaleDateString()]}),e.file_size&&(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:[(e.file_size/1024).toFixed(1),"KB"]})]}),e.content_tokens&&(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:["~",e.content_tokens.toLocaleString()," tokens"]})]})]}),(0,a.jsxs)(M.rI,{children:[(0,a.jsx)(M.ty,{asChild:!0,children:(0,a.jsx)(l.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,a.jsx)(E.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(M.SQ,{align:"end",className:"w-36",children:[(0,a.jsxs)(M._2,{onClick:()=>ef(e),children:[(0,a.jsx)(L.A,{className:"h-4 w-4"}),"Edit"]}),(0,a.jsx)(M._2,{onClick:()=>ew(e),children:e.is_active?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(A.A,{className:"h-4 w-4"}),"Disable"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(P.A,{className:"h-4 w-4"}),"Enable"]})}),(0,a.jsx)(M.mB,{}),(0,a.jsxs)(M._2,{onClick:()=>g(e.entry_id),className:"text-destructive focus:bg-destructive/10 focus:text-destructive",children:[(0,a.jsx)(F.A,{className:"h-4 w-4 text-destructive"}),"Delete"]})]})]})]})})},e.entry_id)})}),eL.length>0&&(0,a.jsxs)(x.Zp,{children:[(0,a.jsx)(x.aR,{children:(0,a.jsxs)(x.ZB,{className:"text-sm font-medium flex items-center gap-2",children:[(0,a.jsx)(w.A,{className:"h-4 w-4"}),"Processing Status"]})}),(0,a.jsx)(x.Wu,{className:"space-y-3",children:eL.map(e=>{let s=eS(e.status),t=eA(e.status);return(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-muted/50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(s,{className:(0,O.cn)("h-4 w-4",t,"processing"===e.status&&"animate-spin")}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"file_upload"===e.job_type?"File Upload":"git_clone"===e.job_type?"Git Repository":"Processing"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.source_info.filename||e.source_info.git_url||"Unknown source"})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)(d.E,{variant:"completed"===e.status?"default":"failed"===e.status?"destructive":"secondary",className:"text-xs",children:e.status}),"completed"===e.status&&(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:[e.entries_created," entries created"]})]})]},e.job_id)})})]}),(0,a.jsx)("input",{ref:ea,type:"file",multiple:!0,onChange:e=>eN(e.target.files),className:"hidden",accept:".txt,.md,.py,.js,.ts,.html,.css,.json,.yaml,.yml,.xml,.csv,.pdf,.docx,.xlsx,.png,.jpg,.jpeg,.gif,.zip"}),(0,a.jsx)(U.lG,{open:W,onOpenChange:B,children:(0,a.jsxs)(U.Cf,{className:"max-w-4xl max-h-[90vh] overflow-hidden flex flex-col",children:[(0,a.jsx)(U.c7,{className:"flex-shrink-0",children:(0,a.jsxs)(U.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(I.A,{className:"h-5 w-5 text-blue-600"}),"Add Knowledge to ",t]})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,a.jsxs)(u.tU,{value:J,onValueChange:e=>X(e),className:"w-full",children:[(0,a.jsxs)(u.j7,{className:"grid w-80 grid-cols-2",children:[(0,a.jsxs)(u.Xi,{value:"manual",className:"gap-2",children:[(0,a.jsx)(T.A,{className:"h-4 w-4"}),"Write Knowledge"]}),(0,a.jsxs)(u.Xi,{value:"files",className:"gap-2",children:[(0,a.jsx)(k.A,{className:"h-4 w-4"}),"Upload Files",es.length>0&&(0,a.jsx)(d.E,{variant:"outline",className:"ml-1",children:es.length})]})]}),(0,a.jsx)(u.av,{value:"manual",className:"space-y-6 mt-6",children:(0,a.jsxs)("form",{onSubmit:ev,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.Label,{htmlFor:"name",className:"text-sm font-medium",children:"Name *"}),(0,a.jsx)(n.p,{id:"name",value:er.name,onChange:e=>el(s=>({...s,name:e.target.value})),placeholder:"e.g., Coding Standards, Domain Knowledge, API Guidelines",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.Label,{htmlFor:"usage_context",className:"text-sm font-medium",children:"Usage Context"}),(0,a.jsxs)(c.l6,{value:er.usage_context,onValueChange:e=>el(s=>({...s,usage_context:e})),children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{})}),(0,a.jsx)(c.gC,{children:Q.map(e=>{let s=e.icon;return(0,a.jsx)(c.eb,{value:e.value,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(s,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:e.label})]})},e.value)})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.Label,{htmlFor:"description",className:"text-sm font-medium",children:"Description"}),(0,a.jsx)(n.p,{id:"description",value:er.description,onChange:e=>el(s=>({...s,description:e.target.value})),placeholder:"Brief description of this knowledge (optional)"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.Label,{htmlFor:"content",className:"text-sm font-medium",children:"Content *"}),(0,a.jsx)(o.T,{id:"content",value:er.content,onChange:e=>el(s=>({...s,content:e.target.value})),placeholder:"Enter the specialized knowledge that ".concat(t," should know..."),className:"min-h-[200px] resize-y",required:!0}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Approximately ",Math.ceil(er.content.length/4).toLocaleString()," tokens"]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-3 pt-4 border-t",children:[(0,a.jsx)(l.$,{type:"button",variant:"outline",onClick:ej,children:"Cancel"}),(0,a.jsxs)(l.$,{type:"submit",disabled:!er.name.trim()||!er.content.trim()||ed.isPending,className:"gap-2",children:[ed.isPending?(0,a.jsx)(z.A,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(C.A,{className:"h-4 w-4"}),"Add Knowledge"]})]})]})}),(0,a.jsx)(u.av,{value:"files",className:"space-y-6 mt-6",children:(0,a.jsxs)("div",{className:"space-y-4",children:[0===es.length&&(0,a.jsxs)("div",{className:"border-2 border-dashed border-border rounded-lg p-8 text-center",children:[(0,a.jsx)(k.A,{className:"h-12 w-12 mx-auto mb-4 text-muted-foreground"}),(0,a.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Upload Files"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground mb-4",children:["Drag and drop files here or click to browse.",(0,a.jsx)("br",{}),"Supports: Documents, Code, ZIP archives"]}),(0,a.jsxs)(l.$,{onClick:()=>{var e;return null==(e=ea.current)?void 0:e.click()},variant:"outline",className:"gap-2",children:[(0,a.jsx)(k.A,{className:"h-4 w-4"}),"Choose Files"]})]}),es.length>0&&(0,a.jsxs)("div",{className:"space-y-6",children:[es.filter(e=>e.file.name.toLowerCase().endsWith(".zip")&&!e.isFromZip).map(e=>{let s=es.filter(s=>s.zipParentId===e.id);return(0,a.jsx)("div",{className:"space-y-3",children:s.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-sm font-medium text-muted-foreground mb-3",children:["Extracted Files (",s.length,"):"]}),(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3",children:s.map(e=>{let s=K(e.file.name),t=G(e.file.name);return(0,a.jsx)("div",{className:"group relative p-2 pb-0 rounded-lg border bg-muted flex items-center",children:(0,a.jsxs)("div",{className:"flex items-center text-center space-y-2",children:[(0,a.jsx)(s,{className:(0,O.cn)("h-8 w-8",t)}),(0,a.jsxs)("div",{className:"w-full flex flex-col items-start ml-2",children:[(0,a.jsx)("p",{className:"text-xs font-medium truncate",title:e.file.name,children:e.file.name}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[(e.file.size/1024).toFixed(1),"KB"]})]}),(0,a.jsxs)("div",{className:"absolute top-1 right-1",children:["uploading"===e.status&&(0,a.jsx)(z.A,{className:"h-3 w-3 animate-spin text-blue-600"}),"success"===e.status&&(0,a.jsx)(v.A,{className:"h-3 w-3 text-green-600"}),"error"===e.status&&(0,a.jsx)(b.A,{className:"h-3 w-3 text-red-600"}),"pending"===e.status&&(0,a.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>e_(e.id),className:"h-4 w-4 p-0 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,a.jsx)(R.A,{className:"h-3 w-3"})})]})]})},e.id)})})]})},e.id)}),es.filter(e=>!e.isFromZip&&!e.file.name.toLowerCase().endsWith(".zip")).length>0&&(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("p",{className:"text-sm font-medium text-muted-foreground",children:["Individual Files (",es.filter(e=>!e.isFromZip&&!e.file.name.toLowerCase().endsWith(".zip")).length,"):"]}),(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3",children:es.filter(e=>!e.isFromZip&&!e.file.name.toLowerCase().endsWith(".zip")).map(e=>{let s=K(e.file.name),t=G(e.file.name);return(0,a.jsx)("div",{className:"group relative p-2 pb-0 rounded-lg border bg-muted flex items-center",children:(0,a.jsxs)("div",{className:"flex items-center text-center space-y-2",children:[(0,a.jsx)(s,{className:(0,O.cn)("h-8 w-8",t)}),(0,a.jsxs)("div",{className:"w-full flex flex-col items-start ml-2",children:[(0,a.jsx)("p",{className:"text-xs font-medium truncate",title:e.file.name,children:(0,O.W5)(e.file.name,20)}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[(e.file.size/1024).toFixed(1),"KB"]})]}),(0,a.jsxs)("div",{className:"absolute top-1 right-1",children:["uploading"===e.status&&(0,a.jsx)(z.A,{className:"h-3 w-3 animate-spin text-blue-600"}),"success"===e.status&&(0,a.jsx)(v.A,{className:"h-3 w-3 text-green-600"}),"error"===e.status&&(0,a.jsx)(b.A,{className:"h-3 w-3 text-red-600"}),"pending"===e.status&&(0,a.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>e_(e.id),className:"h-4 w-4 p-0 opacity-0 group-hover:opacity-100 transition-opacity",children:(0,a.jsx)(R.A,{className:"h-3 w-3"})})]})]})},e.id)})})]})]}),es.length>0&&(0,a.jsxs)("div",{className:"flex justify-end gap-3 pt-4 border-t",children:[(0,a.jsx)(l.$,{type:"button",variant:"outline",onClick:ej,children:"Cancel"}),(0,a.jsxs)(l.$,{onClick:ek,disabled:ex.isPending||0===es.filter(e=>"pending"===e.status&&(e.isFromZip||!e.file.name.toLowerCase().endsWith(".zip"))).length,className:"gap-2",children:[ex.isPending?(0,a.jsx)(z.A,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(k.A,{className:"h-4 w-4"}),"Upload Files (",es.filter(e=>"pending"===e.status&&(e.isFromZip||!e.file.name.toLowerCase().endsWith(".zip"))).length,")"]})]})]})})]})})]})}),(0,a.jsx)(U.lG,{open:m.isOpen,onOpenChange:ej,children:(0,a.jsxs)(U.Cf,{className:"max-w-2xl max-h-[85vh] overflow-hidden flex flex-col",children:[(0,a.jsx)(U.c7,{className:"flex-shrink-0",children:(0,a.jsxs)(U.L3,{className:"flex items-center gap-2",children:[(0,a.jsx)(L.A,{className:"h-5 w-5 text-blue-600"}),"Edit Knowledge Entry"]})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,a.jsxs)("form",{onSubmit:ev,className:"space-y-6 p-1",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.Label,{htmlFor:"edit-name",className:"text-sm font-medium",children:"Name *"}),(0,a.jsx)(n.p,{id:"edit-name",value:er.name,onChange:e=>el(s=>({...s,name:e.target.value})),placeholder:"e.g., Coding Standards, Domain Knowledge, API Guidelines",required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.Label,{htmlFor:"edit-usage_context",className:"text-sm font-medium",children:"Usage Context"}),(0,a.jsxs)(c.l6,{value:er.usage_context,onValueChange:e=>el(s=>({...s,usage_context:e})),children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{})}),(0,a.jsx)(c.gC,{children:Q.map(e=>{let s=e.icon;return(0,a.jsx)(c.eb,{value:e.value,children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(s,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:e.label})]})},e.value)})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.Label,{htmlFor:"edit-description",className:"text-sm font-medium",children:"Description"}),(0,a.jsx)(n.p,{id:"edit-description",value:er.description,onChange:e=>el(s=>({...s,description:e.target.value})),placeholder:"Brief description of this knowledge (optional)"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(i.Label,{htmlFor:"edit-content",className:"text-sm font-medium",children:"Content *"}),(0,a.jsx)(o.T,{id:"edit-content",value:er.content,onChange:e=>el(s=>({...s,content:e.target.value})),placeholder:"Enter the specialized knowledge that ".concat(t," should know..."),className:"min-h-[200px] resize-y",required:!0}),(0,a.jsxs)("div",{className:"text-xs text-muted-foreground",children:["Approximately ",Math.ceil(er.content.length/4).toLocaleString()," tokens"]})]}),(0,a.jsxs)("div",{className:"flex justify-end gap-3 pt-4 border-t",children:[(0,a.jsx)(l.$,{type:"button",variant:"outline",onClick:ej,children:"Cancel"}),(0,a.jsxs)(l.$,{type:"submit",disabled:!er.name.trim()||!er.content.trim()||em.isPending,className:"gap-2",children:[em.isPending?(0,a.jsx)(z.A,{className:"h-4 w-4 animate-spin"}):(0,a.jsx)(L.A,{className:"h-4 w-4"}),"Save Changes"]})]})]})})]})}),(0,a.jsx)(D.Lt,{open:!!p,onOpenChange:()=>g(null),children:(0,a.jsxs)(D.EO,{children:[(0,a.jsxs)(D.wd,{children:[(0,a.jsxs)(D.r7,{className:"flex items-center gap-2",children:[(0,a.jsx)(N.A,{className:"h-5 w-5 text-destructive"}),"Delete Knowledge Entry"]}),(0,a.jsxs)(D.$v,{children:["This will permanently delete this knowledge entry. ",t," will no longer have access to this information."]})]}),(0,a.jsxs)(D.ck,{children:[(0,a.jsx)(D.Zr,{children:"Cancel"}),(0,a.jsx)(D.Rx,{onClick:()=>p&&eb(p),className:"bg-destructive hover:bg-destructive/90",children:"Delete Entry"})]})]})})]})}},58351:(e,s,t)=>{t.d(s,{b:()=>c});var a=t(95155),r=t(12115),l=t(47924),n=t(62523),i=t(80333),o=t(5416);let c=e=>{let{tools:s,onToolsChange:t}=e,[c,d]=(0,r.useState)(""),m=(e,a)=>{t({...s,[e]:{...s[e],enabled:a}})},u=()=>{let e=Object.entries(o.n);return c&&(e=e.filter(e=>{let[s,t]=e;return(0,o._)(s).toLowerCase().includes(c.toLowerCase())||t.description.toLowerCase().includes(c.toLowerCase())})),e};return(0,a.jsxs)("div",{className:"h-full flex flex-col",children:[(0,a.jsxs)("div",{className:"flex-shrink-0 mb-4",children:[(0,a.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,a.jsxs)("span",{className:"text-sm text-muted-foreground",children:[Object.values(s).filter(e=>e.enabled).length," selected"]})}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(l.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(n.p,{placeholder:"Search tools...",value:c,onChange:e=>d(e.target.value),className:"pl-10"})]})]}),(0,a.jsxs)("div",{className:"flex-1 overflow-y-auto",children:[(0,a.jsx)("div",{className:"gap-4 grid grid-cols-1 md:grid-cols-2",children:u().map(e=>{var t;let[r,l]=e;return(0,a.jsxs)("div",{className:"flex items-center gap-3 p-3 bg-muted/50 rounded-lg border hover:border-border/80 transition-colors",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-lg ".concat(l.color," flex items-center justify-center flex-shrink-0"),children:(0,a.jsx)("span",{className:"text-lg",children:l.icon})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:(0,o._)(r)}),(0,a.jsx)(i.d,{checked:(null==(t=s[r])?void 0:t.enabled)||!1,onCheckedChange:e=>m(r,e),className:"flex-shrink-0"})]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground leading-relaxed",children:l.description})]})]},r)})}),0===u().length&&(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"text-4xl mb-3",children:"\uD83D\uDD0D"}),(0,a.jsx)("h3",{className:"text-sm font-medium mb-1",children:"No tools found"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Try adjusting your search criteria"})]})]})]})}},70567:(e,s,t)=>{t.d(s,{b:()=>ex});var a=t(95155),r=t(12115),l=t(71539),n=t(381),i=t(54165),o=t(30285),c=t(26126),d=t(80333),m=t(24357),u=t(33786),x=t(13717),h=t(62525),p=t(46102),g=t(29911);let f=e=>{let{className:s}=e;return(0,a.jsxs)("svg",{className:s,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("path",{d:"M5.042 15.165a2.528 2.528 0 0 1-2.52 2.523A2.528 2.528 0 0 1 0 15.165a2.527 2.527 0 0 1 2.522-2.52h2.52v2.52zM6.313 15.165a2.527 2.527 0 0 1 2.521-2.52 2.527 2.527 0 0 1 2.521 2.52v6.313A2.528 2.528 0 0 1 8.834 24a2.528 2.528 0 0 1-2.521-2.522v-6.313z",fill:"#E01E5A"}),(0,a.jsx)("path",{d:"M8.834 5.042a2.528 2.528 0 0 1-2.521-2.52A2.528 2.528 0 0 1 8.834 0a2.527 2.527 0 0 1 2.521 2.522v2.52H8.834zM8.834 6.313a2.527 2.527 0 0 1 2.521 2.521 2.527 2.527 0 0 1-2.521 2.521H2.522A2.528 2.528 0 0 1 0 8.834a2.528 2.528 0 0 1 2.522-2.521h6.312z",fill:"#36C5F0"}),(0,a.jsx)("path",{d:"M18.956 8.834a2.528 2.528 0 0 1 2.521-2.521A2.528 2.528 0 0 1 24 8.834a2.527 2.527 0 0 1-2.523 2.521h-2.521V8.834zM17.688 8.834a2.527 2.527 0 0 1-2.523 2.521 2.527 2.527 0 0 1-2.52-2.521V2.522A2.528 2.528 0 0 1 15.165 0a2.528 2.528 0 0 1 2.523 2.522v6.312z",fill:"#2EB67D"}),(0,a.jsx)("path",{d:"M15.165 18.956a2.528 2.528 0 0 1 2.523 2.521A2.528 2.528 0 0 1 15.165 24a2.527 2.527 0 0 1-2.52-2.523v-2.521h2.52zM15.165 17.688a2.527 2.527 0 0 1-2.52-2.523 2.527 2.527 0 0 1 2.52-2.52h6.313A2.528 2.528 0 0 1 24 15.165a2.528 2.528 0 0 1-2.522 2.523h-6.313z",fill:"#ECB22E"})]})};var j=t(96753),v=t(14186);let b=e=>{switch(e){case"telegram":return(0,a.jsx)(g.hFS,{className:"h-5 w-5",color:"#0088cc"});case"slack":return(0,a.jsx)(f,{className:"h-5 w-5"});case"webhook":return(0,a.jsx)(j.A,{className:"h-5 w-5"});case"schedule":return(0,a.jsx)(v.A,{className:"h-5 w-5",color:"#10b981"});default:return(0,a.jsx)(l.A,{className:"h-5 w-5"})}},w=e=>{switch(e){case"telegram":return(0,a.jsx)(g.hFS,{className:"h-6 w-6",color:"#0088cc"});case"slack":return(0,a.jsx)(f,{className:"h-6 w-6"});case"webhook":return(0,a.jsx)(j.A,{className:"h-6 w-6"});case"schedule":return(0,a.jsx)(v.A,{className:"h-6 w-6",color:"#10b981"});default:return(0,a.jsx)(l.A,{className:"h-5 w-5"})}},y=async e=>{try{await navigator.clipboard.writeText(e)}catch(e){console.error("Failed to copy text: ",e)}},N=e=>{let{triggers:s,onEdit:t,onRemove:r,onToggle:l,isLoading:n=!1}=e;return(0,a.jsx)(p.Bc,{children:(0,a.jsx)("div",{className:"space-y-2",children:s.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 rounded-lg border bg-card hover:bg-muted/50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 flex-1",children:[(0,a.jsx)("div",{className:"p-2 rounded-lg bg-muted border",children:b(e.trigger_type)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsx)("h4",{className:"text-sm font-medium truncate",children:e.name}),(0,a.jsx)(c.E,{variant:e.is_active?"default":"secondary",className:"text-xs",children:e.is_active?"Active":"Inactive"})]}),e.description&&(0,a.jsx)("p",{className:"text-xs text-muted-foreground truncate",children:e.description}),e.webhook_url&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,a.jsx)("code",{className:"text-xs bg-muted px-2 py-1 rounded font-mono max-w-xs truncate",children:e.webhook_url}),(0,a.jsxs)(p.m_,{children:[(0,a.jsx)(p.k$,{asChild:!0,children:(0,a.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0",onClick:()=>y(e.webhook_url),children:(0,a.jsx)(m.A,{className:"h-3 w-3"})})}),(0,a.jsx)(p.ZI,{children:(0,a.jsx)("p",{children:"Copy webhook URL"})})]}),e.webhook_url.startsWith("http")&&(0,a.jsxs)(p.m_,{children:[(0,a.jsx)(p.k$,{asChild:!0,children:(0,a.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0",onClick:()=>window.open(e.webhook_url,"_blank"),children:(0,a.jsx)(u.A,{className:"h-3 w-3"})})}),(0,a.jsx)(p.ZI,{children:(0,a.jsx)("p",{children:"Open webhook URL"})})]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(p.m_,{children:[(0,a.jsx)(p.k$,{asChild:!0,children:(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(d.d,{checked:e.is_active,onCheckedChange:()=>l(e),disabled:n})})}),(0,a.jsx)(p.ZI,{children:(0,a.jsxs)("p",{children:[e.is_active?"Disable":"Enable"," trigger"]})})]}),(0,a.jsxs)(p.m_,{children:[(0,a.jsx)(p.k$,{asChild:!0,children:(0,a.jsx)(o.$,{size:"sm",variant:"ghost",onClick:()=>t(e),className:"h-8 w-8 p-0",disabled:n,children:(0,a.jsx)(x.A,{className:"h-4 w-4"})})}),(0,a.jsx)(p.ZI,{children:(0,a.jsx)("p",{children:"Edit trigger"})})]}),(0,a.jsxs)(p.m_,{children:[(0,a.jsx)(p.k$,{asChild:!0,children:(0,a.jsx)(o.$,{size:"sm",variant:"ghost",onClick:()=>r(e),className:"h-8 w-8 p-0 text-destructive hover:text-destructive",disabled:n,children:(0,a.jsx)(h.A,{className:"h-4 w-4"})})}),(0,a.jsx)(p.ZI,{children:(0,a.jsx)("p",{children:"Delete trigger"})})]})]})]},e.trigger_id))})})};var k=t(62523),_=t(85057),C=t(88539),S=t(79397),A=t(51154),E=t(59409),L=t(17313),P=t(66695),F=t(30356),I=t(42355),T=t(13052),z=t(29746),R=t(59434);function M(e){let{className:s,classNames:t,showOutsideDays:r=!0,...l}=e;return(0,a.jsx)(z.hv,{showOutsideDays:r,className:(0,R.cn)("p-3",s),classNames:{months:"flex flex-col sm:flex-row gap-2",month:"flex flex-col gap-4",caption:"flex justify-center pt-1 relative items-center w-full",caption_label:"text-sm font-medium",nav:"flex items-center gap-1",nav_button:(0,R.cn)((0,o.r)({variant:"outline"}),"size-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-x-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:(0,R.cn)("relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md","range"===l.mode?"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md":"[&:has([aria-selected])]:rounded-md"),day:(0,R.cn)((0,o.r)({variant:"ghost"}),"size-8 p-0 font-normal aria-selected:opacity-100"),day_range_start:"day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground",day_range_end:"day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...t},components:{IconLeft:e=>{let{className:s,...t}=e;return(0,a.jsx)(I.A,{className:(0,R.cn)("size-4",s),...t})},IconRight:e=>{let{className:s,...t}=e;return(0,a.jsx)(T.A,{className:(0,R.cn)("size-4",s),...t})}},...l})}var D=t(14636),U=t(46561),$=t(16785),O=t(41335),q=t(69074),W=t(81284),Z=t(83013),B=t(80644),J=t(36742);let Q=[{name:"Every minute",cron:"* * * * *",description:"Every minute",icon:(0,a.jsx)(l.A,{className:"h-4 w-4"}),category:"frequent"},{name:"Every 5 minutes",cron:"*/5 * * * *",description:"Every 5 minutes",icon:(0,a.jsx)(U.A,{className:"h-4 w-4"}),category:"frequent"},{name:"Every 15 minutes",cron:"*/15 * * * *",description:"Every 15 minutes",icon:(0,a.jsx)(U.A,{className:"h-4 w-4"}),category:"frequent"},{name:"Every 30 minutes",cron:"*/30 * * * *",description:"Every 30 minutes",icon:(0,a.jsx)(U.A,{className:"h-4 w-4"}),category:"frequent"},{name:"Every hour",cron:"0 * * * *",description:"At the start of every hour",icon:(0,a.jsx)(v.A,{className:"h-4 w-4"}),category:"frequent"},{name:"Daily at 9 AM",cron:"0 9 * * *",description:"Every day at 9:00 AM",icon:(0,a.jsx)($.A,{className:"h-4 w-4"}),category:"daily"},{name:"Daily at 12 PM",cron:"0 12 * * *",description:"Every day at 12:00 PM",icon:(0,a.jsx)($.A,{className:"h-4 w-4"}),category:"daily"},{name:"Daily at 6 PM",cron:"0 18 * * *",description:"Every day at 6:00 PM",icon:(0,a.jsx)($.A,{className:"h-4 w-4"}),category:"daily"},{name:"Twice daily",cron:"0 9,17 * * *",description:"Every day at 9 AM and 5 PM",icon:(0,a.jsx)(O.A,{className:"h-4 w-4"}),category:"daily"},{name:"Weekdays at 9 AM",cron:"0 9 * * 1-5",description:"Monday-Friday at 9:00 AM",icon:(0,a.jsx)($.A,{className:"h-4 w-4"}),category:"weekly"},{name:"Monday mornings",cron:"0 9 * * 1",description:"Every Monday at 9:00 AM",icon:(0,a.jsx)(q.A,{className:"h-4 w-4"}),category:"weekly"},{name:"Friday evenings",cron:"0 17 * * 5",description:"Every Friday at 5:00 PM",icon:(0,a.jsx)(q.A,{className:"h-4 w-4"}),category:"weekly"},{name:"Weekend mornings",cron:"0 10 * * 0,6",description:"Saturday & Sunday at 10:00 AM",icon:(0,a.jsx)(q.A,{className:"h-4 w-4"}),category:"weekly"},{name:"Monthly on 1st",cron:"0 9 1 * *",description:"First day of month at 9:00 AM",icon:(0,a.jsx)(q.A,{className:"h-4 w-4"}),category:"monthly"},{name:"Monthly on 15th",cron:"0 9 15 * *",description:"15th of month at 9:00 AM",icon:(0,a.jsx)(q.A,{className:"h-4 w-4"}),category:"monthly"},{name:"End of month",cron:"0 9 28-31 * *",description:"Last few days of month at 9:00 AM",icon:(0,a.jsx)(q.A,{className:"h-4 w-4"}),category:"monthly"}],K=[{value:"UTC",label:"UTC (Coordinated Universal Time)"},{value:"America/New_York",label:"Eastern Time (ET)"},{value:"America/Chicago",label:"Central Time (CT)"},{value:"America/Denver",label:"Mountain Time (MT)"},{value:"America/Los_Angeles",label:"Pacific Time (PT)"},{value:"Europe/London",label:"Greenwich Mean Time (GMT)"},{value:"Europe/Paris",label:"Central European Time (CET)"},{value:"Europe/Berlin",label:"Central European Time (CET)"},{value:"Asia/Tokyo",label:"Japan Standard Time (JST)"},{value:"Asia/Shanghai",label:"China Standard Time (CST)"},{value:"Australia/Sydney",label:"Australian Eastern Time (AET)"}],G=[{value:"1",label:"Monday",short:"Mon"},{value:"2",label:"Tuesday",short:"Tue"},{value:"3",label:"Wednesday",short:"Wed"},{value:"4",label:"Thursday",short:"Thu"},{value:"5",label:"Friday",short:"Fri"},{value:"6",label:"Saturday",short:"Sat"},{value:"0",label:"Sunday",short:"Sun"}],Y=[{value:"1",label:"January"},{value:"2",label:"February"},{value:"3",label:"March"},{value:"4",label:"April"},{value:"5",label:"May"},{value:"6",label:"June"},{value:"7",label:"July"},{value:"8",label:"August"},{value:"9",label:"September"},{value:"10",label:"October"},{value:"11",label:"November"},{value:"12",label:"December"}],V=e=>{var s,t;let{provider:n,config:i,onChange:c,errors:d,agentId:m}=e,{data:u=[],isLoading:x}=(0,J.X3)(m),[h,p]=(0,r.useState)("quick"),[g,f]=(0,r.useState)(""),[j,v]=(0,r.useState)("daily"),[b,w]=(0,r.useState)(["1","2","3","4","5"]),[y,N]=(0,r.useState)(["*"]),[S,A]=(0,r.useState)("1"),[I,T]=(0,r.useState)({hour:"09",minute:"00"}),[z,U]=(0,r.useState)(),[V,X]=(0,r.useState)({hour:"09",minute:"00"}),H=()=>{if("quick"===h&&g)return g;if("recurring"===h){let{hour:e,minute:s}=I;switch(j){case"daily":default:return"".concat(s," ").concat(e," * * *");case"weekly":let t=b.join(",");return"".concat(s," ").concat(e," * * ").concat(t);case"monthly":let a=y.includes("*")?"*":y.join(",");return"".concat(s," ").concat(e," ").concat(S," ").concat(a," *")}}if("one-time"===h&&z){let{hour:e,minute:s}=V,t=z.getDate(),a=z.getMonth()+1;return z.getFullYear(),"".concat(s," ").concat(e," ").concat(t," ").concat(a," *")}return i.cron_expression||""};(0,r.useEffect)(()=>{let e=H();e&&e!==i.cron_expression&&c({...i,cron_expression:e})},[h,g,j,b,y,S,I,z,V]);let ee=e=>{f(e.cron),c({...i,cron_expression:e.cron})},es=e=>{c({...i,agent_prompt:e})},et=e=>{w(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e].sort())},ea=e=>{"*"===e?N(["*"]):N(s=>{let t=s.filter(e=>"*"!==e);return t.includes(e)?t.filter(s=>s!==e):[...t,e].sort((e,s)=>parseInt(e)-parseInt(s))})},er=Q.reduce((e,s)=>(e[s.category]||(e[s.category]=[]),e[s.category].push(s),e),{});return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(P.Zp,{className:"border-none bg-transparent shadow-none p-0",children:[(0,a.jsx)(P.aR,{className:"p-0 -mt-2",children:(0,a.jsx)(P.BT,{children:"Configure when your agent should be triggered automatically. Choose from quick presets, recurring schedules, or set up advanced cron expressions."})}),(0,a.jsx)(P.Wu,{className:"p-0",children:(0,a.jsxs)(L.tU,{value:h,onValueChange:e=>p(e),className:"w-full",children:[(0,a.jsxs)(L.j7,{className:"grid w-full grid-cols-4",children:[(0,a.jsxs)(L.Xi,{value:"quick",className:"flex items-center gap-2",children:[(0,a.jsx)(l.A,{className:"h-4 w-4"}),"Quick"]}),(0,a.jsxs)(L.Xi,{value:"recurring",className:"flex items-center gap-2",children:[(0,a.jsx)(O.A,{className:"h-4 w-4"}),"Recurring"]}),(0,a.jsxs)(L.Xi,{value:"one-time",className:"flex items-center gap-2",children:[(0,a.jsx)(q.A,{className:"h-4 w-4"}),"One-time"]}),(0,a.jsxs)(L.Xi,{value:"advanced",className:"flex items-center gap-2",children:[(0,a.jsx)($.A,{className:"h-4 w-4"}),"Advanced"]})]}),(0,a.jsx)(L.av,{value:"quick",className:"space-y-4 mt-6",children:(0,a.jsx)("div",{className:"space-y-4",children:Object.entries(er).map(e=>{let[s,t]=e;return(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"text-sm font-medium mb-3 capitalize",children:[s," Schedules"]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:t.map(e=>(0,a.jsx)(P.Zp,{className:(0,R.cn)("p-0 cursor-pointer transition-colors hover:bg-accent",g===e.cron&&"ring-2 ring-primary bg-accent"),onClick:()=>ee(e),children:(0,a.jsx)(P.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"text-primary",children:e.icon}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"font-medium text-sm",children:e.name}),(0,a.jsx)("div",{className:"text-xs text-muted-foreground",children:e.description})]})]})})},e.cron))})]},s)})})}),(0,a.jsx)(L.av,{value:"recurring",className:"space-y-6 mt-6",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(_.Label,{className:"text-sm font-medium mb-3 block",children:"Schedule Type"}),(0,a.jsxs)(F.z,{value:j,onValueChange:e=>v(e),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(F.C,{value:"daily",id:"daily"}),(0,a.jsx)(_.Label,{htmlFor:"daily",children:"Daily"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(F.C,{value:"weekly",id:"weekly"}),(0,a.jsx)(_.Label,{htmlFor:"weekly",children:"Weekly"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(F.C,{value:"monthly",id:"monthly"}),(0,a.jsx)(_.Label,{htmlFor:"monthly",children:"Monthly"})]})]})]}),"weekly"===j&&(0,a.jsxs)("div",{children:[(0,a.jsx)(_.Label,{className:"text-sm font-medium mb-3 block",children:"Days of Week"}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:G.map(e=>(0,a.jsx)(o.$,{variant:b.includes(e.value)?"default":"outline",size:"sm",onClick:()=>et(e.value),children:e.short},e.value))})]}),"monthly"===j&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(_.Label,{className:"text-sm font-medium mb-3 block",children:"Day of Month"}),(0,a.jsxs)(E.l6,{value:S,onValueChange:A,children:[(0,a.jsx)(E.bq,{className:"w-full",children:(0,a.jsx)(E.yv,{})}),(0,a.jsx)(E.gC,{children:Array.from({length:31},(e,s)=>(0,a.jsx)(E.eb,{value:(s+1).toString(),children:s+1},s+1))})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(_.Label,{className:"text-sm font-medium mb-3 block",children:"Months"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.$,{variant:y.includes("*")?"default":"outline",size:"sm",onClick:()=>ea("*"),children:"All Months"}),(0,a.jsx)("div",{className:"grid grid-cols-3 gap-2",children:Y.map(e=>(0,a.jsx)(o.$,{variant:y.includes(e.value)?"default":"outline",size:"sm",onClick:()=>ea(e.value),disabled:y.includes("*"),children:e.label.slice(0,3)},e.value))})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(_.Label,{className:"text-sm font-medium mb-3 block",children:"Time"}),(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsxs)(E.l6,{value:I.hour,onValueChange:e=>T(s=>({...s,hour:e})),children:[(0,a.jsx)(E.bq,{className:"w-20",children:(0,a.jsx)(E.yv,{})}),(0,a.jsx)(E.gC,{children:Array.from({length:24},(e,s)=>(0,a.jsx)(E.eb,{value:s.toString().padStart(2,"0"),children:s.toString().padStart(2,"0")},s))})]}),(0,a.jsx)("span",{children:":"}),(0,a.jsxs)(E.l6,{value:I.minute,onValueChange:e=>T(s=>({...s,minute:e})),children:[(0,a.jsx)(E.bq,{className:"w-20",children:(0,a.jsx)(E.yv,{})}),(0,a.jsx)(E.gC,{children:Array.from({length:60},(e,s)=>(0,a.jsx)(E.eb,{value:s.toString().padStart(2,"0"),children:s.toString().padStart(2,"0")},s))})]})]})]})]})}),(0,a.jsx)(L.av,{value:"one-time",className:"space-y-6 mt-6",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(_.Label,{className:"text-sm font-medium mb-3 block",children:"Date"}),(0,a.jsxs)(D.AM,{children:[(0,a.jsx)(D.Wv,{asChild:!0,children:(0,a.jsxs)(o.$,{variant:"outline",className:(0,R.cn)("w-full justify-start text-left font-normal",!z&&"text-muted-foreground"),children:[(0,a.jsx)(q.A,{className:"h-4 w-4"}),z?(0,Z.GP)(z,"PPP"):"Pick a date"]})}),(0,a.jsx)(D.hl,{className:"w-auto p-0",align:"start",children:(0,a.jsx)(M,{mode:"single",selected:z,onSelect:U,disabled:e=>e<(0,B.o)(new Date),initialFocus:!0})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(_.Label,{className:"text-sm font-medium mb-3 block",children:"Time"}),(0,a.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,a.jsxs)(E.l6,{value:V.hour,onValueChange:e=>X(s=>({...s,hour:e})),children:[(0,a.jsx)(E.bq,{className:"w-20",children:(0,a.jsx)(E.yv,{})}),(0,a.jsx)(E.gC,{children:Array.from({length:24},(e,s)=>(0,a.jsx)(E.eb,{value:s.toString().padStart(2,"0"),children:s.toString().padStart(2,"0")},s))})]}),(0,a.jsx)("span",{children:":"}),(0,a.jsxs)(E.l6,{value:V.minute,onValueChange:e=>X(s=>({...s,minute:e})),children:[(0,a.jsx)(E.bq,{className:"w-20",children:(0,a.jsx)(E.yv,{})}),(0,a.jsx)(E.gC,{children:Array.from({length:60},(e,s)=>(0,a.jsx)(E.eb,{value:s.toString().padStart(2,"0"),children:s.toString().padStart(2,"0")},s))})]})]})]})]})}),(0,a.jsx)(L.av,{value:"advanced",className:"space-y-4 mt-6",children:(0,a.jsxs)("div",{children:[(0,a.jsx)(_.Label,{htmlFor:"cron_expression",className:"text-sm font-medium",children:"Cron Expression *"}),(0,a.jsx)(k.p,{id:"cron_expression",type:"text",value:i.cron_expression||"",onChange:e=>c({...i,cron_expression:e.target.value}),placeholder:"0 9 * * 1-5",className:d.cron_expression?"border-destructive":""}),d.cron_expression&&(0,a.jsx)("p",{className:"text-xs text-destructive mt-1",children:d.cron_expression}),(0,a.jsx)(P.Zp,{className:"mt-3 p-0 py-4",children:(0,a.jsxs)(P.Wu,{children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,a.jsx)(W.A,{className:"h-4 w-4 text-muted-foreground"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Cron Format"})]}),(0,a.jsxs)("div",{className:"text-sm text-muted-foreground space-y-1",children:[(0,a.jsxs)("div",{children:["Format: ",(0,a.jsx)("code",{className:"bg-muted px-1 rounded text-xs",children:"minute hour day month weekday"})]}),(0,a.jsxs)("div",{children:["Example: ",(0,a.jsx)("code",{className:"bg-muted px-1 rounded text-xs",children:"0 9 * * 1-5"})," = Weekdays at 9 AM"]}),(0,a.jsxs)("div",{children:["Use ",(0,a.jsx)("code",{className:"bg-muted px-1 rounded text-xs",children:"*"})," for any value, ",(0,a.jsx)("code",{className:"bg-muted px-1 rounded text-xs",children:"*/5"})," for every 5 units"]})]})]})})]})})]})})]}),(0,a.jsx)(P.Zp,{className:"border-none bg-transparent shadow-none p-0",children:(0,a.jsxs)(P.Wu,{className:"space-y-4 p-0",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(_.Label,{htmlFor:"timezone",className:"text-sm font-medium",children:"Timezone"}),(0,a.jsxs)(E.l6,{value:i.timezone||"UTC",onValueChange:e=>{c({...i,timezone:e})},children:[(0,a.jsx)(E.bq,{children:(0,a.jsx)(E.yv,{placeholder:"Select timezone"})}),(0,a.jsx)(E.gC,{children:K.map(e=>(0,a.jsx)(E.eb,{value:e.value,children:e.label},e.value))})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(_.Label,{className:"text-sm font-medium mb-3 block",children:"Execution Type *"}),(0,a.jsxs)(F.z,{value:i.execution_type||"agent",onValueChange:e=>{let s={...i,execution_type:e};"agent"===e?(delete s.workflow_id,delete s.workflow_input):(delete s.agent_prompt,s.workflow_input||(s.workflow_input={prompt:""})),c(s)},children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(F.C,{value:"agent",id:"execution-agent"}),(0,a.jsx)(_.Label,{htmlFor:"execution-agent",children:"Execute Agent"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(F.C,{value:"workflow",id:"execution-workflow"}),(0,a.jsx)(_.Label,{htmlFor:"execution-workflow",children:"Execute Workflow"})]})]}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Choose whether to execute the agent directly or run a specific workflow."})]}),"workflow"===i.execution_type?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(_.Label,{htmlFor:"workflow_id",className:"text-sm font-medium",children:"Workflow *"}),(0,a.jsxs)(E.l6,{value:i.workflow_id||"",onValueChange:e=>{e.startsWith("__")||c({...i,workflow_id:e})},children:[(0,a.jsx)(E.bq,{className:d.workflow_id?"border-destructive":"",children:(0,a.jsx)(E.yv,{placeholder:"Select a workflow"})}),(0,a.jsx)(E.gC,{children:x?(0,a.jsx)(E.eb,{value:"__loading__",disabled:!0,children:"Loading workflows..."}):0===u.length?(0,a.jsx)(E.eb,{value:"__no_workflows__",disabled:!0,children:"No workflows available"}):u.filter(e=>"active"===e.status).map(e=>(0,a.jsx)(E.eb,{value:e.id,children:e.name},e.id))})]}),d.workflow_id&&(0,a.jsx)("p",{className:"text-xs text-destructive mt-1",children:d.workflow_id}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Select the workflow to execute when triggered."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(_.Label,{htmlFor:"workflow_input",className:"text-sm font-medium",children:"Instructions for Workflow"}),(0,a.jsx)(C.T,{id:"workflow_input",value:(null==(s=i.workflow_input)?void 0:s.prompt)||(null==(t=i.workflow_input)?void 0:t.message)||"",onChange:e=>{c({...i,workflow_input:{prompt:e.target.value}})},placeholder:"Write what you want the workflow to do...",rows:3,className:d.workflow_input?"border-destructive":""}),d.workflow_input&&(0,a.jsx)("p",{className:"text-xs text-destructive mt-1",children:d.workflow_input}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"Simply describe what you want the workflow to accomplish. The workflow will interpret your instructions naturally."})]})]}):(0,a.jsxs)("div",{children:[(0,a.jsx)(_.Label,{htmlFor:"agent_prompt",className:"text-sm font-medium",children:"Agent Prompt *"}),(0,a.jsx)(C.T,{id:"agent_prompt",value:i.agent_prompt||"",onChange:e=>es(e.target.value),placeholder:"Enter the prompt that will be sent to your agent when triggered...",rows:4,className:d.agent_prompt?"border-destructive":""}),d.agent_prompt&&(0,a.jsx)("p",{className:"text-xs text-destructive mt-1",children:d.agent_prompt}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground mt-1",children:"This prompt will be sent to your agent each time the schedule triggers."})]})]})})]})},X=e=>{var s;let{provider:t,existingConfig:l,onSave:n,onCancel:c,isLoading:x=!1,agentId:h}=e,[p,g]=(0,r.useState)((null==l?void 0:l.name)||""),[f,j]=(0,r.useState)((null==l?void 0:l.description)||""),[v,b]=(0,r.useState)(null==(s=null==l?void 0:l.is_active)||s),[y,N]=(0,r.useState)((null==l?void 0:l.config)||{}),[E,L]=(0,r.useState)({});(0,r.useEffect)(()=>{p||l||g("".concat(t.name," Trigger"))},[t,l,p]);let P=()=>{let e={};return p.trim()||(e.name="Name is required"),"telegram"===t.provider_id?y.bot_token||(e.bot_token="Bot token is required"):"slack"===t.provider_id?y.signing_secret||(e.signing_secret="Signing secret is required"):"schedule"===t.provider_id&&(y.cron_expression||(e.cron_expression="Cron expression is required"),"workflow"===y.execution_type?y.workflow_id||(e.workflow_id="Workflow selection is required"):y.agent_prompt||(e.agent_prompt="Agent prompt is required")),L(e),0===Object.keys(e).length};return(0,a.jsxs)(i.Cf,{className:"max-w-2xl max-h-[80vh] overflow-hidden flex flex-col",children:[(0,a.jsxs)(i.c7,{children:[(0,a.jsxs)(i.L3,{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-2 rounded-lg bg-muted border",children:w(t.trigger_type)}),(0,a.jsx)("div",{children:(0,a.jsxs)("span",{children:["Configure ",t.name]})})]}),(0,a.jsx)(i.rr,{children:t.description})]}),(0,a.jsxs)("div",{className:"flex-1 overflow-y-auto space-y-6 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(_.Label,{htmlFor:"trigger-name",children:"Name *"}),(0,a.jsx)(k.p,{id:"trigger-name",value:p,onChange:e=>g(e.target.value),placeholder:"Enter a name for this trigger",className:E.name?"border-destructive":""}),E.name&&(0,a.jsx)("p",{className:"text-sm text-destructive",children:E.name})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(_.Label,{htmlFor:"trigger-description",children:"Description"}),(0,a.jsx)(C.T,{id:"trigger-description",value:f,onChange:e=>j(e.target.value),placeholder:"Optional description for this trigger",rows:2})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(d.d,{id:"trigger-active",checked:v,onCheckedChange:b}),(0,a.jsx)(_.Label,{htmlFor:"trigger-active",children:"Enable trigger immediately"})]})]}),(0,a.jsxs)("div",{className:"border-t pt-6",children:[(0,a.jsxs)("h3",{className:"text-sm font-medium mb-4",children:[t.name," Configuration"]}),"schedule"===t.provider_id?(0,a.jsx)(V,{provider:t,config:y,onChange:N,errors:E,agentId:h}):(0,a.jsxs)("div",{className:"text-center py-8 text-muted-foreground",children:[(0,a.jsx)(S.A,{className:"h-12 w-12 mx-auto mb-4"}),(0,a.jsxs)("p",{children:["Configuration form for ",t.name," is not yet implemented."]})]})]}),t.webhook_enabled&&(null==l?void 0:l.webhook_url)&&(0,a.jsxs)("div",{className:"border-t pt-6",children:[(0,a.jsx)("h3",{className:"text-sm font-medium mb-4",children:"Webhook Information"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(_.Label,{children:"Webhook URL"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(k.p,{value:l.webhook_url,readOnly:!0,className:"font-mono text-sm"}),(0,a.jsx)(o.$,{size:"sm",variant:"outline",onClick:()=>navigator.clipboard.writeText(l.webhook_url),children:(0,a.jsx)(m.A,{className:"h-4 w-4"})}),(0,a.jsx)(o.$,{size:"sm",variant:"outline",onClick:()=>window.open(l.webhook_url,"_blank"),children:(0,a.jsx)(u.A,{className:"h-4 w-4"})})]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Use this URL to configure the webhook in ",t.name]})]})]})]}),(0,a.jsxs)(i.Es,{children:[(0,a.jsx)(o.$,{variant:"outline",onClick:c,disabled:x,children:"Cancel"}),(0,a.jsx)(o.$,{onClick:()=>{P()&&n({name:p.trim(),description:f.trim(),is_active:v,config:y})},disabled:x,children:x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(A.A,{className:"animate-spin rounded-full h-4 w-4"}),l?"Updating...":"Creating..."]}):"".concat(l?"Update":"Create"," Trigger")})]})]})};var H=t(58332),ee=t(56671),es=t(26715),et=t(5041),ea=t(52643);let er="http://localhost:8000/api",el=async()=>{let e=(0,ea.U)(),{data:{session:s}}=await e.auth.getSession();if(!s)throw Error("You must be logged in to manage integrations");return s.access_token},en=async e=>{let s=await el(),t=await fetch("".concat(er,"/integrations/install"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json().catch(()=>({}))).detail||"Failed to initiate installation");return t.json()},ei=async e=>{let s=await el(),t=await fetch("".concat(er,"/integrations/uninstall/").concat(e),{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)}});if(!t.ok)throw Error((await t.json().catch(()=>({}))).detail||"Failed to uninstall integration")},eo=()=>((0,es.jE)(),(0,et.n)({mutationFn:en,onSuccess:(e,s)=>{sessionStorage.setItem("oauth_agent_id",s.agent_id),sessionStorage.setItem("oauth_provider",s.provider),window.location.href=e.install_url},onError:e=>{ee.oR.error(e.message||"Failed to start OAuth installation")}})),ec=()=>{let e=(0,es.jE)();return(0,et.n)({mutationFn:ei,onSuccess:(s,t)=>{ee.oR.success("Integration uninstalled successfully"),e.invalidateQueries({queryKey:["oauth-integrations"]}),e.invalidateQueries({queryKey:["agent-triggers"]})},onError:e=>{ee.oR.error(e.message||"Failed to uninstall integration")}})},ed=()=>{let e=(0,es.jE)();return{handleCallback:()=>{let s=new URLSearchParams(window.location.search),t=sessionStorage.getItem("oauth_agent_id");sessionStorage.getItem("oauth_provider");let a=s.get("slack_success"),r=s.get("discord_success"),l=s.get("teams_success");s.get("trigger_id"),s.get("workspace"),s.get("bot_name");let n=s.get("slack_error"),i=s.get("discord_error"),o=s.get("teams_error");if(a||r||l){ee.oR.success("".concat(a?"Slack":r?"Discord":"Teams"," integration installed successfully!")),t&&e.invalidateQueries({queryKey:["oauth-integrations",t]}),sessionStorage.removeItem("oauth_agent_id"),sessionStorage.removeItem("oauth_provider");let s=window.location.pathname;window.history.replaceState({},document.title,s)}else if(n||i||o){let e=n?"Slack":i?"Discord":"Teams";ee.oR.error("Failed to install ".concat(e," integration: ").concat(n||i||o)),sessionStorage.removeItem("oauth_agent_id"),sessionStorage.removeItem("oauth_provider");let s=window.location.pathname;window.history.replaceState({},document.title,s)}}}},em={schedule:{name:"Schedule",icon:(0,a.jsx)(v.A,{className:"h-4 w-4",color:"#10b981"}),isOAuth:!1}},eu=e=>{let{agentId:s}=e,[t,l]=(0,r.useState)(!1),{data:n=[]}=(0,H.aC)(s),c=eo(),d=ec(),m=(0,H.A$)(),u=(0,H.Dz)(),{handleCallback:x}=ed();(0,r.useEffect)(()=>{x()},[]);let h=async e=>{if("schedule"===e)return void l(!0);try{await c.mutateAsync({agent_id:s,provider:e})}catch(s){console.error("Error installing ".concat(e,":"),s)}},p=async(e,s)=>{if("schedule"===e&&s){try{await u.mutateAsync(s),ee.oR.success("Schedule trigger removed successfully")}catch(e){ee.oR.error("Failed to remove schedule trigger"),console.error("Error removing schedule trigger:",e)}return}try{await d.mutateAsync(s)}catch(e){console.error("Error uninstalling integration:",e)}},g=async e=>{try{await m.mutateAsync({agentId:s,provider_id:"schedule",name:e.name||"Scheduled Trigger",description:e.description||"Automatically scheduled trigger",config:e.config}),ee.oR.success("Schedule trigger created successfully"),l(!1)}catch(e){ee.oR.error(e.message||"Failed to create schedule trigger"),console.error("Error creating schedule trigger:",e)}},f=e=>{if("schedule"===e)return n.find(e=>"schedule"===e.trigger_type)},j=e=>!!f(e),v=e=>{let s=f(e);return null==s?void 0:s.trigger_id};return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"flex flex-wrap gap-3",children:Object.entries(em).map(e=>{let[s,t]=e,r=j(s),l=c.isPending||d.isPending||"schedule"===s&&(m.isPending||u.isPending),n=v(s),i="schedule"===s?t.name:r?"Disconnect ".concat(t.name):"Connect ".concat(t.name);return(0,a.jsxs)(o.$,{variant:"outline",size:"sm",onClick:()=>{"schedule"===s?h(s):r?p(s,n):h(s)},disabled:l,className:"flex items-center",children:[l?(0,a.jsx)(A.A,{className:"h-4 w-4 mr-2 animate-spin"}):t.icon,i]},s)})}),t&&(0,a.jsx)(i.lG,{open:t,onOpenChange:l,children:(0,a.jsx)(X,{provider:{provider_id:"schedule",name:"Schedule",description:"Schedule agent execution using cron expressions",trigger_type:"schedule",webhook_enabled:!0,config_schema:{}},existingConfig:null,onSave:g,onCancel:()=>l(!1),isLoading:m.isPending,agentId:s})})]})},ex=e=>{let{agentId:s}=e,[t,o]=(0,r.useState)(null),[c,d]=(0,r.useState)(null),{data:m=[],isLoading:u,error:x}=(0,H.aC)(s),h=(0,H.A$)(),p=(0,H.vg)(),g=(0,H.Dz)(),f=(0,H.ct)(),j=async e=>{try{await g.mutateAsync(e.trigger_id),ee.oR.success("Trigger deleted successfully")}catch(e){ee.oR.error("Failed to delete trigger"),console.error("Error deleting trigger:",e)}},v=async e=>{try{c?(await p.mutateAsync({triggerId:c.trigger_id,name:e.name,description:e.description,config:e.config,is_active:e.is_active}),ee.oR.success("Trigger updated successfully")):(await h.mutateAsync({agentId:s,provider_id:t.provider_id,name:e.name,description:e.description,config:e.config}),ee.oR.success("Trigger created successfully"))}catch(e){ee.oR.error(e.message||"Failed to save trigger"),console.error("Error saving trigger:",e)}o(null),d(null)},b=async e=>{try{await f.mutateAsync({triggerId:e.trigger_id,isActive:!e.is_active}),ee.oR.success("Trigger ".concat(e.is_active?"disabled":"enabled"))}catch(e){ee.oR.error("Failed to toggle trigger"),console.error("Error toggling trigger:",e)}};return x?(0,a.jsx)("div",{className:"rounded-xl p-6 border border-destructive/20 bg-destructive/5",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-2 bg-destructive/10 rounded-lg",children:(0,a.jsx)(l.A,{className:"h-5 w-5 text-destructive"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-destructive",children:"Error Loading Triggers"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:x instanceof Error?x.message:"Failed to load triggers"})]})]})}):(0,a.jsxs)("div",{className:"h-full flex flex-col",children:[(0,a.jsxs)("div",{className:"flex-1 overflow-y-auto space-y-4",children:[(0,a.jsx)(eu,{agentId:s}),m.length>0&&(0,a.jsxs)("div",{className:"bg-card rounded-xl border border-border overflow-hidden",children:[(0,a.jsx)("div",{className:"px-6 py-4 border-b border-border bg-muted/30",children:(0,a.jsxs)("h4",{className:"text-sm font-medium text-foreground flex items-center gap-2",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),"Configured Triggers"]})}),(0,a.jsx)("div",{className:"p-2 divide-y divide-border",children:(0,a.jsx)(N,{triggers:m,onEdit:e=>{d(e),o({provider_id:e.provider_id,name:e.trigger_type,description:"",trigger_type:e.trigger_type,webhook_enabled:!!e.webhook_url,config_schema:{}})},onRemove:j,onToggle:b,isLoading:g.isPending||f.isPending})})]}),!u&&0===m.length&&(0,a.jsxs)("div",{className:"text-center py-12 px-6 bg-muted/30 rounded-xl border-2 border-dashed border-border",children:[(0,a.jsx)("div",{className:"mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-4 border",children:(0,a.jsx)(l.A,{className:"h-6 w-6 text-muted-foreground"})}),(0,a.jsx)("h4",{className:"text-sm font-semibold text-foreground",children:"No triggers configured"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mb-6 max-w-sm mx-auto",children:"Click on a trigger provider above to get started"})]})]}),t&&(0,a.jsx)(i.lG,{open:!!t,onOpenChange:()=>o(null),children:(0,a.jsx)(X,{provider:t,existingConfig:c,onSave:v,onCancel:()=>o(null),isLoading:h.isPending||p.isPending,agentId:s})})]})}},77633:(e,s,t)=>{t.d(s,{c:()=>v});var a=t(95155),r=t(7601),l=t(68222),n=t(59434),i=t(95653),o=t(12115),c=t(5196),d=t(30285),m=t(25731),u=t(56671),x=t(23843),h=t(27379);function p(e){let{activeTab:s,setActiveTab:t,className:r}=e;return(0,a.jsx)("div",{className:(0,n.cn)("relative flex w-fit items-center rounded-full border p-0.5 backdrop-blur-sm cursor-pointer h-9 flex-row bg-muted",r),children:["cloud","self-hosted"].map(e=>(0,a.jsxs)("button",{onClick:()=>t(e),className:(0,n.cn)("relative z-[1] px-3 h-8 flex items-center justify-center cursor-pointer",{"z-0":s===e}),children:[s===e&&(0,a.jsx)(i.P.div,{layoutId:"active-tab",className:"absolute inset-0 rounded-full bg-white dark:bg-[#3F3F46] shadow-md border border-border",transition:{duration:.2,type:"spring",stiffness:300,damping:25,velocity:2}}),(0,a.jsx)("span",{className:(0,n.cn)("relative block text-sm font-medium duration-200 shrink-0",s===e?"text-primary":"text-muted-foreground"),children:"cloud"===e?"Cloud":"Self-hosted"})]},e))})}function g(e){let{price:s,isCompact:t}=e;return(0,a.jsx)(i.P.span,{className:t?"text-xl font-semibold":"text-4xl font-semibold",initial:{opacity:0,x:10,filter:"blur(5px)"},animate:{opacity:1,x:0,filter:"blur(0px)"},transition:{duration:.25,ease:[.4,0,.2,1]},children:s},s)}function f(e){let{billingPeriod:s,setBillingPeriod:t}=e;return(0,a.jsx)("div",{className:"flex items-center justify-center gap-3",children:(0,a.jsx)("div",{className:"relative bg-muted rounded-full p-1 cursor-pointer",onClick:()=>t("monthly"===s?"yearly":"monthly"),children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:(0,n.cn)("px-3 py-1 rounded-full text-xs font-medium transition-all duration-200","monthly"===s?"bg-background text-foreground shadow-sm":"text-muted-foreground"),children:"Monthly"}),(0,a.jsxs)("div",{className:(0,n.cn)("px-3 py-1 rounded-full text-xs font-medium transition-all duration-200 flex items-center gap-1","yearly"===s?"bg-background text-foreground shadow-sm":"text-muted-foreground"),children:["Yearly",(0,a.jsx)("span",{className:"bg-green-600 text-green-50 dark:bg-green-500 dark:text-green-50 text-[10px] px-1.5 py-0.5 rounded-full font-semibold whitespace-nowrap",children:"15% off"})]})]})})})}function j(e){var s,t;let{tier:r,isCompact:i=!1,currentSubscription:o,isLoading:x,isFetchingPlan:h,selectedPlan:p,onPlanSelect:f,onSubscriptionUpdate:j,isAuthenticated:v=!1,returnUrl:b,insideDialog:w=!1,billingPeriod:y="monthly"}=e,N=async e=>{var s,t,r;if(!v){window.location.href="/auth?mode=signup";return}if(!x[e])try{null==f||f(e);let t=await (0,m.fw)({price_id:e,success_url:b,cancel_url:b});switch(console.log("Subscription action response:",t),t.status){case"new":case"checkout_created":t.url?window.location.href=t.url:(console.error("Error: Received status 'checkout_created' but no checkout URL."),u.oR.error("Failed to initiate subscription. Please try again."));break;case"upgraded":case"updated":let r=(null==(s=t.details)?void 0:s.is_upgrade)?"Subscription upgraded from $".concat(t.details.current_price," to $").concat(t.details.new_price):"Subscription updated successfully";u.oR.success(r),j&&j();break;case"downgrade_scheduled":case"scheduled":let l=t.effective_date?new Date(t.effective_date).toLocaleDateString():"the end of your billing period";u.oR.success((0,a.jsxs)("div",{children:[(0,a.jsx)("p",{children:"Subscription change scheduled"}),(0,a.jsxs)("p",{className:"text-sm mt-1",children:["Your plan will change on ",l,"."]})]})),j&&j();break;case"no_change":u.oR.info(t.message||"You are already on this plan.");break;default:console.warn("Received unexpected status from createCheckoutSession:",t.status),u.oR.error("An unexpected error occurred. Please try again.")}}catch(s){console.error("Error processing subscription:",s);let e=(null==s||null==(r=s.response)||null==(t=r.data)?void 0:t.detail)||(null==s?void 0:s.message)||"Failed to process subscription. Please try again.";u.oR.error(e)}},k="yearly"===y&&r.yearlyStripePriceId?r.yearlyStripePriceId:r.stripePriceId,_="yearly"===y&&r.yearlyPrice?r.yearlyPrice:r.price,C=l.CQ.cloudPricingItems.find(e=>e.stripePriceId===(null==o?void 0:o.price_id)||e.yearlyStripePriceId===(null==o?void 0:o.price_id)),S=v&&(null==o?void 0:o.price_id)===k,A=v&&(null==o?void 0:o.has_schedule),E=A&&(null==o?void 0:o.scheduled_price_id)===k,L=x[k],P=v?"Select Plan":"Start Free",F=L,I=null,T="",z=null,R="";if(v){if(S)P="Current Plan",F=!0,I="secondary",T=i?"ring-1 ring-primary":"ring-2 ring-primary",R="bg-primary/5 hover:bg-primary/10 text-primary",z=(0,a.jsx)("span",{className:"bg-primary/10 text-primary text-[10px] font-medium px-1.5 py-0.5 rounded-full",children:"Current"});else if(E)P="Scheduled",F=!0,I="outline",T=i?"ring-1 ring-yellow-500":"ring-2 ring-yellow-500",R="bg-yellow-500/5 hover:bg-yellow-500/10 text-yellow-600 border-yellow-500/20",z=(0,a.jsx)("span",{className:"bg-yellow-500/10 text-yellow-600 text-[10px] font-medium px-1.5 py-0.5 rounded-full",children:"Scheduled"});else if(A&&(null==o?void 0:o.price_id)===k)P="Change Scheduled",I="secondary",T=i?"ring-1 ring-primary":"ring-2 ring-primary",R="bg-primary/5 hover:bg-primary/10 text-primary",z=(0,a.jsx)("span",{className:"bg-yellow-500/10 text-yellow-600 text-[10px] font-medium px-1.5 py-0.5 rounded-full",children:"Downgrade Pending"});else{let e=o&&(null==C?void 0:C.price)||"$0",s=r.price,t="$0"===e?0:100*parseFloat(e.replace(/[^\d.]/g,"")||"0"),a="$0"===s?0:100*parseFloat(s.replace(/[^\d.]/g,"")||"0"),l=C&&(null==o?void 0:o.price_id)===C.stripePriceId,n=C&&(null==o?void 0:o.price_id)===C.yearlyStripePriceId,i=r.stripePriceId===k,c=r.yearlyStripePriceId===k,d=C&&C.name===r.name&&(l&&c||n&&i);0===t&&0===a&&(null==o?void 0:o.status)!=="no_subscription"?(P="Select Plan",F=!0,I="secondary",R="bg-primary/5 hover:bg-primary/10 text-primary"):a>t||l&&c&&a>=t?n&&i?(P="-",F=!0,I="secondary",R="opacity-50 cursor-not-allowed bg-muted text-muted-foreground"):l&&c&&a===t?(P="Switch to Yearly",I="default",R="bg-green-600 hover:bg-green-700 text-white"):(P="Upgrade",I=r.buttonColor,R="bg-primary hover:bg-primary/90 text-primary-foreground"):a<t&&!(n&&i&&a===t)?(P="-",F=!0,I="secondary",R="opacity-50 cursor-not-allowed bg-muted text-muted-foreground"):d?l&&c?(P="Switch to Yearly",I="default",R="bg-green-600 hover:bg-green-700 text-white"):n&&i?(P="-",F=!0,I="secondary",R="opacity-50 cursor-not-allowed bg-muted text-muted-foreground"):(P="Select Plan",I=r.buttonColor,R="bg-primary hover:bg-primary/90 text-primary-foreground"):(P="Select Plan",I=r.buttonColor,R="bg-primary hover:bg-primary/90 text-primary-foreground")}L&&(P="Loading...",R="opacity-70 cursor-not-allowed")}else I=r.buttonColor,R="default"===r.buttonColor?"bg-primary hover:bg-primary/90 text-white":"bg-secondary hover:bg-secondary/90 text-white";return(0,a.jsxs)("div",{className:(0,n.cn)("rounded-xl flex flex-col relative",w?"min-h-[300px]":"h-full min-h-[300px]",r.isPopular&&!w?"md:shadow-[0px_61px_24px_-10px_rgba(0,0,0,0.01),0px_34px_20px_-8px_rgba(0,0,0,0.05),0px_15px_15px_-6px_rgba(0,0,0,0.09),0px_4px_8px_-2px_rgba(0,0,0,0.10),0px_0px_0px_1px_rgba(0,0,0,0.08)] bg-accent":"bg-[#F3F4F6] dark:bg-[#F9FAFB]/[0.02] border border-border",!w&&T),children:[(0,a.jsxs)("div",{className:(0,n.cn)("flex flex-col gap-3",w?"p-3":"p-4"),children:[(0,a.jsxs)("p",{className:"text-sm flex items-center gap-2",children:[r.name,r.isPopular&&(0,a.jsx)("span",{className:"bg-gradient-to-b from-secondary/50 from-[1.92%] to-secondary to-[100%] text-white inline-flex w-fit items-center justify-center px-1.5 py-0.5 rounded-full text-[10px] font-medium shadow-[0px_6px_6px_-3px_rgba(0,0,0,0.08),0px_3px_3px_-1.5px_rgba(0,0,0,0.08),0px_1px_1px_-0.5px_rgba(0,0,0,0.08),0px_0px_0px_1px_rgba(255,255,255,0.12)_inset,0px_1px_0px_0px_rgba(255,255,255,0.12)_inset]",children:"Popular"}),!r.isPopular&&v&&o&&"yearly"===y&&C&&o.price_id===C.stripePriceId&&r.yearlyStripePriceId&&(C.name===r.name||parseFloat(r.price.slice(1))>=parseFloat(C.price.slice(1)))&&(0,a.jsx)("span",{className:"bg-green-500/10 text-green-700 text-[10px] font-medium px-1.5 py-0.5 rounded-full",children:"Recommended"}),v&&z]}),(0,a.jsx)("div",{className:"flex items-baseline mt-2",children:"yearly"===y&&r.yearlyPrice&&"$0"!==_?(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-baseline gap-2",children:[(0,a.jsx)(g,{price:"$".concat(Math.round(parseFloat(r.yearlyPrice.slice(1))/12)),isCompact:w}),r.discountPercentage&&(0,a.jsxs)("span",{className:"text-xs line-through text-muted-foreground",children:["$",Math.round(parseFloat((null==(s=r.originalYearlyPrice)?void 0:s.slice(1))||"0")/12)]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:"/month"}),(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:"billed yearly"})]})]}):(0,a.jsxs)("div",{className:"flex items-baseline",children:[(0,a.jsx)(g,{price:_,isCompact:w}),(0,a.jsx)("span",{className:"ml-2",children:"$0"!==_?"/month":""})]})}),(0,a.jsx)("p",{className:"hidden text-sm mt-2",children:r.description}),"yearly"===y&&r.yearlyPrice&&r.discountPercentage?(0,a.jsxs)("div",{className:"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold bg-green-50 border-green-200 text-green-700 w-fit",children:["Save $",Math.round(parseFloat((null==(t=r.originalYearlyPrice)?void 0:t.slice(1))||"0")-parseFloat(r.yearlyPrice.slice(1)))," per year"]}):(0,a.jsx)("div",{className:"hidden items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold bg-primary/10 border-primary/20 text-primary w-fit",children:"yearly"===y&&r.yearlyPrice&&"$0"!==_?"$".concat(Math.round(parseFloat(r.yearlyPrice.slice(1))/12),"/month (billed yearly)"):"".concat(_,"/month")})]}),(0,a.jsx)("div",{className:(0,n.cn)("flex-grow",w?"px-3 pb-2":"px-4 pb-3"),children:r.features&&r.features.length>0&&(0,a.jsx)("ul",{className:"space-y-3",children:r.features.map(e=>(0,a.jsxs)("li",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"size-5 min-w-5 rounded-full border border-primary/20 flex items-center justify-center",children:(0,a.jsx)(c.A,{className:"size-3 text-primary"})}),(0,a.jsx)("span",{className:"text-sm",children:e})]},e))})}),(0,a.jsx)("div",{className:(0,n.cn)("mt-auto",w?"px-3 pt-1 pb-3":"px-4 pt-2 pb-4"),children:(0,a.jsx)(d.$,{onClick:()=>N(k),disabled:F,variant:I||"default",className:(0,n.cn)("w-full font-medium transition-all duration-200",i||w?"h-8 rounded-md text-xs":"h-10 rounded-full text-sm",R,L&&"animate-pulse"),children:P})})]})}function v(e){let{returnUrl:s=window.location.href,showTitleAndTabs:t=!0,hideFree:i=!1,insideDialog:c=!1}=e,[d,m]=(0,o.useState)("cloud"),{data:u,isLoading:g,error:v,refetch:b}=(0,h.Rs)(),w=!!u&&null===v,y=u||null,N=()=>{if(!w||!y)return"yearly";let e=l.CQ.cloudPricingItems.find(e=>e.stripePriceId===y.price_id||e.yearlyStripePriceId===y.price_id);if(e){if(e.yearlyStripePriceId===y.price_id);else if(e.stripePriceId===y.price_id)return"monthly"}return"yearly"},[k,_]=(0,o.useState)(N()),[C,S]=(0,o.useState)({});(0,o.useEffect)(()=>{_(N())},[w,null==y?void 0:y.price_id]);let A=e=>{S(s=>({...s,[e]:!0}))},E=()=>{b(),setTimeout(()=>{S({})},1e3)};return(0,x.Jn)()?(0,a.jsx)("div",{className:"p-4 bg-muted/30 border border-border rounded-lg text-center",children:(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Running in local development mode - billing features are disabled"})}):(0,a.jsxs)("section",{id:"pricing",className:(0,n.cn)("flex flex-col items-center justify-center gap-10 w-full relative pb-12"),children:[t&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(r.X,{children:[(0,a.jsx)("h2",{className:"text-3xl md:text-4xl font-medium tracking-tighter text-center text-balance",children:"Choose the right plan for your needs"}),(0,a.jsx)("p",{className:"text-muted-foreground text-center text-balance font-medium",children:"Start with our free plan or upgrade for more AI token credits"})]}),(0,a.jsx)("div",{className:"relative w-full h-full",children:(0,a.jsx)("div",{className:"absolute -top-14 left-1/2 -translate-x-1/2",children:(0,a.jsx)(p,{activeTab:d,setActiveTab:e=>{if("self-hosted"===e){let e=document.getElementById("open-source");if(e){let s=e.getBoundingClientRect(),t=(window.pageYOffset||document.documentElement.scrollTop)+s.top-100;window.scrollTo({top:t,behavior:"smooth"})}}else m(e)},className:"mx-auto"})})})]}),"cloud"===d&&(0,a.jsx)(f,{billingPeriod:k,setBillingPeriod:_}),"cloud"===d&&(0,a.jsx)("div",{className:(0,n.cn)("grid gap-4 w-full mx-auto",{"px-6 max-w-7xl":!c,"max-w-7xl":c},c?"grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 2xl:grid-cols-4":"min-[650px]:grid-cols-2 lg:grid-cols-4",!c&&"grid-rows-1 items-stretch"),children:l.CQ.cloudPricingItems.filter(e=>!e.hidden&&(!i||"$0"!==e.price)).map(e=>(0,a.jsx)(j,{tier:e,currentSubscription:y,isLoading:C,isFetchingPlan:g,onPlanSelect:A,onSubscriptionUpdate:E,isAuthenticated:w,returnUrl:s,insideDialog:c,billingPeriod:k},e.name))}),(0,a.jsx)("div",{className:"mt-4 p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg max-w-2xl mx-auto",children:(0,a.jsxs)("p",{className:"text-sm text-blue-800 dark:text-blue-200 text-center",children:[(0,a.jsx)("strong",{children:"What are AI tokens?"})," Tokens are units of text that AI models process. Your plan includes credits to spend on various AI models - the more complex the task, the more tokens used."]})})]})}},82814:(e,s,t)=>{t.d(s,{V:()=>eT});var a,r,l=t(95155),n=t(12115),i=t(66695),o=t(30285),c=t(51154),d=t(50492),m=t(56671),u=t(52643),x=t(26715),h=t(34986),p=t(46102),g=t(90066);let f=(e,s,t)=>{let a=e.filter(e=>!(e.size>0x3200000)||(m.oR.error("File size exceeds 50MB limit: ".concat(e.name)),!1));s(e=>[...e,...a]);let r=a.map(e=>{let s=(0,g.L)(e.name);return{name:s,path:"/workspace/".concat(s),size:e.size,type:e.type||"application/octet-stream",localUrl:URL.createObjectURL(e)}});t(e=>[...e,...r]),a.forEach(e=>{let s=(0,g.L)(e.name);m.oR.success("File attached: ".concat(s))})},j=async function(e,s,t,a){let r=arguments.length>4&&void 0!==arguments[4]?arguments[4]:[],l=arguments.length>5?arguments[5]:void 0;try{a(!0);let n=[];for(let t of e){if(t.size>0x3200000){m.oR.error("File size exceeds 50MB limit: ".concat(t.name));continue}let e=(0,g.L)(t.name),a="/workspace/".concat(e),i=r.some(e=>("string"==typeof e.content?e.content:"").includes("[Uploaded File: ".concat(a,"]"))),o=new FormData;o.append("file",t,e),o.append("path",a);let c=(0,u.U)(),{data:{session:d}}=await c.auth.getSession();if(!(null==d?void 0:d.access_token))throw Error("No access token available");let x=await fetch("".concat("http://localhost:8000/api","/sandboxes/").concat(s,"/files"),{method:"POST",headers:{Authorization:"Bearer ".concat(d.access_token)},body:o});if(!x.ok)throw Error("Upload failed: ".concat(x.statusText));if(i&&l){console.log("Invalidating cache for existing file: ".concat(a)),["text","blob","json"].forEach(e=>{let t=h.Be.content(s,a,e);l.removeQueries({queryKey:t})});let e=a.substring(0,a.lastIndexOf("/"));l.invalidateQueries({queryKey:h.Be.directory(s,e)})}n.push({name:e,path:a,size:t.size,type:t.type||"application/octet-stream"}),m.oR.success("File uploaded: ".concat(e))}t(e=>[...e,...n])}catch(e){console.error("File upload failed:",e),m.oR.error("string"==typeof e?e:e instanceof Error?e.message:"Failed to upload file")}finally{a(!1)}},v=async function(e,s,t,a,r){let l=arguments.length>5&&void 0!==arguments[5]?arguments[5]:[],n=arguments.length>6?arguments[6]:void 0;s?await j(e,s,a,r,l,n):f(e,t,a)},b=(0,n.forwardRef)((e,s)=>{let{loading:t,disabled:a,isAgentRunning:r,isUploading:i,sandboxId:m,setPendingFiles:u,setUploadedFiles:h,setIsUploading:g,messages:f=[],isLoggedIn:j=!0}=e,b=(0,x.jE)();(0,n.useEffect)(()=>()=>{h(e=>(e.forEach(e=>{e.localUrl&&URL.revokeObjectURL(e.localUrl)}),e))},[]);let w=async e=>{e.target.files&&0!==e.target.files.length&&(v(Array.from(e.target.files),m,u,h,g,f,b),e.target.value="")};return(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(p.Bc,{children:(0,l.jsxs)(p.m_,{children:[(0,l.jsx)(p.k$,{asChild:!0,children:(0,l.jsx)("span",{className:"inline-block",children:(0,l.jsxs)(o.$,{type:"button",onClick:()=>{s&&"current"in s&&s.current&&s.current.click()},variant:"outline",size:"sm",className:"h-8 px-3 py-2 bg-transparent border border-border rounded-xl text-muted-foreground hover:text-foreground hover:bg-accent/50 flex items-center gap-2",disabled:!j||t||a&&!r||i,children:[i?(0,l.jsx)(c.A,{className:"h-4 w-4 animate-spin"}):(0,l.jsx)(d.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{className:"text-sm",children:"Attach"})]})})}),(0,l.jsx)(p.ZI,{side:"top",children:(0,l.jsx)("p",{children:j?"Attach files":"Please login to attach files"})})]})}),(0,l.jsx)("input",{type:"file",ref:s,className:"hidden",onChange:w,multiple:!0})]})});b.displayName="FileUploadHandler";var w=t(88539),y=t(39881),N=t(59434),k=t(18979),_=t(97207),C=t(99090),S=t(25731);let A=(0,C.Lx)(S.Ks,{errorContext:{operation:"transcribe audio",resource:"speech-to-text"}}),E=e=>{let{onTranscription:s,disabled:t=!1}=e,[a,r]=(0,n.useState)("idle"),i=(0,n.useRef)(null),d=(0,n.useRef)([]),m=(0,n.useRef)(null),u=(0,n.useRef)(null),x=(0,n.useRef)(null),h=A();(0,n.useEffect)(()=>("recording"===a?(u.current=Date.now(),x.current=setTimeout(()=>{console.log("Auto-stopping recording after 15 minutes"),f()},9e5)):(u.current=null,x.current&&(clearTimeout(x.current),x.current=null)),()=>{x.current&&clearTimeout(x.current)}),[a]);let g=async()=>{try{let e=await navigator.mediaDevices.getUserMedia({audio:!0});m.current=e;let t=new MediaRecorder(e,{mimeType:"audio/webm"});i.current=t,d.current=[],t.ondataavailable=e=>{e.data.size>0&&d.current.push(e.data)},t.onstop=async()=>{if(0===d.current.length){v(),r("idle");return}r("processing");let e=new Blob(d.current,{type:"audio/webm"}),t=new File([e],"recording.webm",{type:"audio/webm"});h.mutate(t,{onSuccess:e=>{s(e.text),r("idle")},onError:e=>{console.error("Transcription failed:",e),r("idle")}}),v()},t.start(),r("recording")}catch(e){console.error("Error starting recording:",e),r("idle")}},f=()=>{i.current&&"recording"===a&&i.current.stop()},j=()=>{i.current&&"recording"===a&&(d.current=[],i.current.stop(),v(),r("idle"))},v=()=>{m.current&&(m.current.getTracks().forEach(e=>e.stop()),m.current=null)};return(0,l.jsx)(p.Bc,{children:(0,l.jsxs)(p.m_,{children:[(0,l.jsx)(p.k$,{asChild:!0,children:(0,l.jsx)(o.$,{type:"button",variant:"ghost",size:"sm",onClick:()=>{"idle"===a?g():"recording"===a&&f()},onContextMenu:e=>{e.preventDefault(),"recording"===a&&j()},disabled:t||"processing"===a,className:"h-8 px-2 py-2 bg-transparent border-0 rounded-xl text-muted-foreground hover:text-foreground hover:bg-accent/50 flex items-center gap-2 transition-colors ".concat("recording"===a?"text-red-500 hover:bg-red-50 hover:text-red-600":""),children:(()=>{switch(a){case"recording":return(0,l.jsx)(k.A,{className:"h-4 w-4"});case"processing":return(0,l.jsx)(c.A,{className:"h-4 w-4 animate-spin"});default:return(0,l.jsx)(_.A,{className:"h-4 w-4"})}})()})}),(0,l.jsx)(p.ZI,{side:"top",className:"text-xs",children:(0,l.jsx)("p",{children:"recording"===a?"Click to stop recording":"processing"===a?"Processing...":"Record voice message"})})]})})};var L=t(44838),P=t(1243),F=t(17951),I=t(13717),T=t(74126),z=t(5196),R=t(73314),M=t(84616),D=t(47924),U=t(10401),$=t(54165),O=t(59964);let q=e=>{let{cancelText:s="Maybe Later",children:t,open:a=!1,ctaText:r="Upgrade Now",description:i="This feature requires an upgrade to access.",onDialogClose:c,onUpgradeClick:d,upgradeUrl:m="/settings/billing",title:u="Upgrade Required"}=e,x=(0,n.useCallback)(()=>{d?d():window.location.href=m},[d,m]),h=(0,n.useCallback)(()=>{c&&c()},[c]);return(0,n.useEffect)(()=>()=>{document.body.classList.remove("overflow-hidden"),document.body.style.pointerEvents="auto",document.querySelectorAll("[data-backdrop]").forEach(e=>e.remove())},[]),(0,n.useEffect)(()=>{a||(document.body.classList.remove("overflow-hidden"),document.body.style.pointerEvents="auto",document.querySelectorAll('[role="dialog"]').forEach(e=>{e.closest('[open="true"]')||e.remove()}))},[a]),(0,l.jsxs)(l.Fragment,{children:[t,(0,l.jsx)($.lG,{open:a,onOpenChange:e=>{e||(document.body.style.pointerEvents="auto",document.body.classList.remove("overflow-hidden"),setTimeout(()=>{c&&c()},0))},children:(0,l.jsxs)($.Cf,{className:"sm:max-w-md",onEscapeKeyDown:()=>{h(),document.body.style.pointerEvents="auto"},children:[(0,l.jsxs)($.c7,{children:[(0,l.jsx)($.L3,{children:u}),(0,l.jsx)($.rr,{children:i})]}),(0,l.jsx)("div",{className:"py-6",children:(0,l.jsxs)("div",{className:"flex flex-col items-center text-center space-y-4",children:[(0,l.jsx)("div",{className:"rounded-full bg-primary/10 p-3",children:(0,l.jsx)(O.A,{className:"h-6 w-6 text-primary"})}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("h3",{className:"text-lg font-medium",children:"Upgrade to unlock"}),(0,l.jsx)("p",{className:"text-muted-foreground",children:"Get access to premium models and features"})]})]})}),(0,l.jsxs)($.Es,{className:"flex items-center gap-2",children:[(0,l.jsx)(o.$,{onClick:()=>{document.body.style.pointerEvents="auto",document.body.classList.remove("overflow-hidden"),h()},variant:"outline",children:s}),(0,l.jsxs)(o.$,{onClick:()=>{document.body.style.pointerEvents="auto",document.body.classList.remove("overflow-hidden"),x()},variant:"default",children:[(0,l.jsx)(O.A,{className:"h-4 w-4"}),r]})]})]})})]})};var W=t(35695),Z=t(23843),B=t(62523),J=t(85057);let Q=e=>{let{isOpen:s,onClose:t,onSave:a,initialData:r,mode:i}=e,[c,d]=n.useState(r);n.useEffect(()=>{d(r)},[r,s]);let m=()=>{t()};return(0,l.jsx)($.lG,{open:s,onOpenChange:e=>{e||m()},children:(0,l.jsxs)($.Cf,{className:"sm:max-w-[430px]",onEscapeKeyDown:m,onPointerDownOutside:m,children:[(0,l.jsxs)($.c7,{children:[(0,l.jsx)($.L3,{children:"add"===i?"Add Custom Model":"Edit Custom Model"}),(0,l.jsxs)($.rr,{children:["Kortix Suna uses ",(0,l.jsx)("b",{children:"LiteLLM"})," under the hood, which makes it compatible with over 100 models. You can easily choose any ",(0,l.jsx)("a",{href:"https://openrouter.ai/models",target:"_blank",rel:"noopener noreferrer",className:"text-blue-500 hover:text-blue-600 underline",children:"OpenRouter model"})," by prefixing it with ",(0,l.jsx)("code",{className:"bg-muted px-1.5 py-0.5 rounded text-sm font-mono",children:"openrouter/"})," and it should work out of the box. If you want to use other models besides OpenRouter, you might have to modify the ",(0,l.jsx)("a",{href:"https://github.com/kortix-ai/suna/blob/main/backend/services/llm.py",target:"_blank",rel:"noopener noreferrer",className:"text-blue-500 hover:text-blue-600 underline",children:"llm.py"}),", set the correct environment variables, and rebuild your self-hosted Docker container."]})]}),(0,l.jsx)("div",{className:"flex flex-col gap-4 py-4",children:(0,l.jsxs)("div",{className:"flex flex-col items-start gap-4",children:[(0,l.jsx)(J.Label,{htmlFor:"modelId",className:"text-right",children:"Model ID"}),(0,l.jsx)(B.p,{id:"modelId",placeholder:"e.g. openrouter/meta-llama/llama-4-maverick",value:c.id,onChange:e=>{let{id:s,value:t}=e.target;d(e=>({...e,["modelId"===s?"id":"label"]:t}))},className:"col-span-3",onClick:e=>e.stopPropagation()})]})}),(0,l.jsxs)($.Es,{children:[(0,l.jsx)(o.$,{variant:"outline",onClick:e=>{e.stopPropagation(),m()},children:"Cancel"}),(0,l.jsx)(o.$,{onClick:e=>{e.stopPropagation(),c.id.trim()&&a(c)},disabled:!c.id.trim(),children:"add"===i?"Add Model":"Save Changes"})]})]})})},K=e=>{var s,t,a;let{selectedModel:r,onModelChange:i,modelOptions:c,canAccessModel:d,subscriptionStatus:m,refreshCustomModels:u,billingModalOpen:x,setBillingModalOpen:h,hasBorder:g=!1}=e,[f,j]=(0,n.useState)(!1),[v,b]=(0,n.useState)(null),[w,y]=(0,n.useState)(!1),[k,_]=(0,n.useState)(""),[C,S]=(0,n.useState)(-1),A=(0,n.useRef)(null);(0,W.useRouter)();let[E,$]=(0,n.useState)([]),[O,B]=(0,n.useState)(!1),[J,K]=(0,n.useState)({id:"",label:""}),[G,Y]=(0,n.useState)("add"),[V,X]=(0,n.useState)(null);(0,n.useEffect)(()=>{(0,Z.Jn)()&&$((0,U.uv)())},[]),(0,n.useEffect)(()=>{(0,Z.Jn)()&&E.length>0&&localStorage.setItem(U.ve,JSON.stringify(E))},[E]);let H=new Map;c.forEach(e=>{H.set(e.id,{...e,isCustom:!1})}),(0,Z.Jn)()&&E.forEach(e=>{if(H.has(e.id)){let s=H.get(e.id);H.set(e.id,{...s,isCustom:!0})}else H.set(e.id,{id:e.id,label:e.label||(0,U.Il)(e.id),requiresSubscription:!1,top:!1,isCustom:!0})});let ee=Array.from(H.values()),es=ee.filter(e=>e.label.toLowerCase().includes(k.toLowerCase())||e.id.toLowerCase().includes(k.toLowerCase())),et=()=>c.filter(e=>!e.requiresSubscription).map(e=>e.id),ea=(e,s)=>"model-".concat(e.id,"-").concat(s),er=es.map((e,s)=>({...e,uniqueKey:ea(e,s)}));(0,n.useEffect)(()=>{w&&A.current?setTimeout(()=>{var e;null==(e=A.current)||e.focus()},50):(_(""),S(-1))},[w]),null==(s=ee.find(e=>e.id===r))||s.label;let el=e=>{if(E.some(s=>s.id===e)&&(0,Z.Jn)()){i(e),y(!1);return}d(e)?(i(e),y(!1)):(b(e),j(!0))},en=es.filter(e=>!et().some(s=>e.id.includes(s))),ei=!(0,Z.Jn)()&&"no_subscription"===m&&en.length>0,eo=e=>{null==e||e.stopPropagation(),K({id:"",label:""}),Y("add"),B(!0),y(!1)},ec=(e,s)=>{null==s||s.stopPropagation(),K({id:e.id,label:e.label}),X(e.id),Y("edit"),B(!0),y(!1)},ed=()=>{B(!1),K({id:"",label:""}),X(null),document.body.classList.remove("overflow-hidden");let e=document.body.style;setTimeout(()=>{e.pointerEvents="",e.removeProperty("pointer-events")},150)},em=(e,s)=>{null==s||s.stopPropagation(),null==s||s.preventDefault();let t=E.filter(s=>s.id!==e);if((0,Z.Jn)()&&1)try{localStorage.setItem(U.ve,JSON.stringify(t))}catch(e){console.error("Failed to update custom models in localStorage:",e)}if($(t),u&&u(),r===e){let e=(0,Z.Jn)()?U.un:U.Zo;i(e);try{localStorage.setItem(U.WY,e)}catch(e){console.warn("Failed to update selected model in localStorage:",e)}}y(!1),setTimeout(()=>{S(-1),w&&(y(!1),setTimeout(()=>y(!0),50))},10)},eu=(e,s)=>{var t,a;let n=!!e.isCustom||(0,Z.Jn)()&&E.some(s=>s.id===e.id),i=!!n||d(e.id),o=s===C,c=e.requiresSubscription,m=(null==(t=U.L6[e.id])?void 0:t.lowQuality)||!1,u=(null==(a=U.L6[e.id])?void 0:a.recommended)||!1;return(0,l.jsx)(p.Bc,{children:(0,l.jsxs)(p.m_,{children:[(0,l.jsx)(p.k$,{asChild:!0,children:(0,l.jsx)("div",{className:"w-full",children:(0,l.jsxs)(L._2,{className:(0,N.cn)("text-sm px-3 py-2 mx-2 my-0.5 flex items-center justify-between cursor-pointer",o&&"bg-accent",!i&&"opacity-70"),onClick:()=>el(e.id),onMouseEnter:()=>S(s),children:[(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{className:"font-medium",children:e.label})}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[m&&(0,l.jsx)(P.A,{className:"h-3.5 w-3.5 text-amber-500"}),u&&(0,l.jsx)("span",{className:"text-xs px-1.5 py-0.5 rounded-sm bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 font-medium",children:"Recommended"}),c&&!i&&(0,l.jsx)(F.A,{className:"h-3.5 w-3.5 text-blue-500"}),(0,Z.Jn)()&&n&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)("button",{onClick:s=>{s.stopPropagation(),ec(e,s)},className:"text-muted-foreground hover:text-foreground",children:(0,l.jsx)(I.A,{className:"h-3.5 w-3.5"})}),(0,l.jsx)("button",{onClick:s=>{s.stopPropagation(),em(e.id,s)},className:"text-muted-foreground hover:text-red-500",children:(0,l.jsx)(T.A,{className:"h-3.5 w-3.5"})})]}),r===e.id&&(0,l.jsx)(z.A,{className:"h-4 w-4 text-blue-500"})]})]})})}),i?m?(0,l.jsx)(p.ZI,{side:"left",className:"text-xs max-w-xs",children:(0,l.jsx)("p",{children:"Not recommended for complex tasks"})}):u?(0,l.jsx)(p.ZI,{side:"left",className:"text-xs max-w-xs",children:(0,l.jsx)("p",{children:"Recommended for optimal performance"})}):n?(0,l.jsx)(p.ZI,{side:"left",className:"text-xs max-w-xs",children:(0,l.jsx)("p",{children:"Custom model"})}):null:(0,l.jsx)(p.ZI,{side:"left",className:"text-xs max-w-xs",children:(0,l.jsx)("p",{children:"Requires subscription to access premium model"})})]})},e.uniqueKey||"model-".concat(e.id,"-").concat(s))};return(0,n.useEffect)(()=>{S(-1),_(""),w&&(y(!1),setTimeout(()=>y(!0),10))},[E,c]),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsxs)(L.rI,{open:w,onOpenChange:y,children:[(0,l.jsx)(p.Bc,{children:(0,l.jsxs)(p.m_,{children:[(0,l.jsx)(p.k$,{asChild:!0,children:(0,l.jsx)(L.ty,{asChild:!0,children:(0,l.jsx)(o.$,{variant:"ghost",size:"sm",className:"h-8 px-2 py-2 bg-transparent border-0 rounded-xl text-muted-foreground hover:text-foreground hover:bg-accent/50 flex items-center gap-2",children:(0,l.jsxs)("div",{className:"relative flex items-center justify-center",children:[(0,l.jsx)(R.A,{className:"h-4 w-4"}),(null==(t=U.L6[r])?void 0:t.lowQuality)&&(0,l.jsx)(P.A,{className:"h-2.5 w-2.5 text-amber-500 absolute -top-1 -right-1"})]})})})}),(0,l.jsx)(p.ZI,{side:"top",className:"text-xs",children:(0,l.jsx)("p",{children:"Choose a model"})})]})}),(0,l.jsxs)(L.SQ,{align:"end",className:"w-72 p-0 overflow-hidden",sideOffset:4,children:[(0,l.jsx)("div",{className:"overflow-y-auto w-full scrollbar-hide relative",children:ei?(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"px-3 py-3 text-xs font-medium text-muted-foreground",children:"Available Models"}),er.filter(e=>!e.requiresSubscription&&(e.label.toLowerCase().includes(k.toLowerCase())||e.id.toLowerCase().includes(k.toLowerCase()))).map((e,s)=>{var t,a,n;return(0,l.jsx)(p.Bc,{children:(0,l.jsxs)(p.m_,{children:[(0,l.jsx)(p.k$,{asChild:!0,children:(0,l.jsx)("div",{className:"w-full",children:(0,l.jsxs)(L._2,{className:(0,N.cn)("text-sm mx-2 my-0.5 px-3 py-2 flex items-center justify-between cursor-pointer",r===e.id&&"bg-accent"),onClick:()=>i(e.id),onMouseEnter:()=>S(es.indexOf(e)),children:[(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{className:"font-medium",children:e.label})}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[((null==(t=U.L6[e.id])?void 0:t.lowQuality)||!1)&&(0,l.jsx)(P.A,{className:"h-3.5 w-3.5 text-amber-500"}),((null==(a=U.L6[e.id])?void 0:a.recommended)||!1)&&(0,l.jsx)("span",{className:"text-xs px-1.5 py-0.5 rounded-sm bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 font-medium",children:"Recommended"}),r===e.id&&(0,l.jsx)(z.A,{className:"h-4 w-4 text-blue-500"})]})]})})}),(null==(n=U.L6[e.id])?void 0:n.lowQuality)&&(0,l.jsx)(p.ZI,{side:"left",className:"text-xs max-w-xs",children:(0,l.jsx)("p",{children:"Basic model with limited capabilities"})})]})},e.uniqueKey||"model-".concat(e.id,"-").concat(s))}),(0,l.jsxs)("div",{className:"mt-4 border-t border-border pt-2",children:[(0,l.jsxs)("div",{className:"px-3 py-1.5 text-xs font-medium text-blue-500 flex items-center",children:[(0,l.jsx)(F.A,{className:"h-3.5 w-3.5 mr-1.5"}),"Premium Models"]}),(0,l.jsxs)("div",{className:"relative h-40 overflow-hidden px-2",children:[c.filter(e=>e.requiresSubscription).map((e,s)=>({...e,uniqueKey:ea(e,s)})).filter(e=>e.requiresSubscription&&(e.label.toLowerCase().includes(k.toLowerCase())||e.id.toLowerCase().includes(k.toLowerCase()))).slice(0,3).map((e,s)=>{var t;return(0,l.jsx)(p.Bc,{children:(0,l.jsxs)(p.m_,{children:[(0,l.jsx)(p.k$,{asChild:!0,children:(0,l.jsx)("div",{className:"w-full",children:(0,l.jsxs)(L._2,{className:"text-sm px-3 py-2 flex items-center justify-between opacity-70 cursor-pointer pointer-events-none",children:[(0,l.jsx)("div",{className:"flex items-center",children:(0,l.jsx)("span",{className:"font-medium",children:e.label})}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(null==(t=U.L6[e.id])?void 0:t.recommended)&&(0,l.jsx)("span",{className:"text-xs px-1.5 py-0.5 rounded-sm bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 font-medium whitespace-nowrap",children:"Recommended"}),(0,l.jsx)(F.A,{className:"h-3.5 w-3.5 text-blue-500"})]})]})})}),(0,l.jsx)(p.ZI,{side:"left",className:"text-xs max-w-xs",children:(0,l.jsx)("p",{children:"Requires subscription to access premium model"})})]})},e.uniqueKey||"model-".concat(e.id,"-").concat(s))}),(0,l.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-background via-background/95 to-transparent flex items-end justify-center",children:(0,l.jsx)("div",{className:"w-full p-3",children:(0,l.jsx)("div",{className:"rounded-xl bg-gradient-to-br from-blue-50/80 to-blue-200/70 dark:from-blue-950/40 dark:to-blue-900/30 shadow-sm border border-blue-200/50 dark:border-blue-800/50 p-3",children:(0,l.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)(F.A,{className:"h-4 w-4 text-blue-500 mr-2 flex-shrink-0"}),(0,l.jsx)("div",{children:(0,l.jsx)("p",{className:"text-sm font-medium",children:"Unlock all models + higher limits"})})]}),(0,l.jsx)(o.$,{size:"sm",className:"w-full h-8 font-medium",onClick:()=>{h(!0)},children:"Upgrade now"})]})})})})]})]})]}):(0,l.jsxs)("div",{className:"max-h-[320px] overflow-y-auto w-full",children:[(0,l.jsxs)("div",{className:"px-3 py-3 flex justify-between items-center",children:[(0,l.jsx)("span",{className:"text-xs font-medium text-muted-foreground",children:"All Models"}),(0,Z.Jn)()&&(0,l.jsx)(p.Bc,{children:(0,l.jsxs)(p.m_,{children:[(0,l.jsx)(p.k$,{asChild:!0,children:(0,l.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-6 w-6 p-0",onClick:e=>{e.stopPropagation(),eo(e)},children:(0,l.jsx)(M.A,{className:"h-3.5 w-3.5"})})}),(0,l.jsx)(p.ZI,{side:"bottom",className:"text-xs",children:"Add a custom model"})]})})]}),er.filter(e=>e.label.toLowerCase().includes(k.toLowerCase())||e.id.toLowerCase().includes(k.toLowerCase())).map((e,s)=>eu(e,s)),0===er.length&&(0,l.jsx)("div",{className:"text-sm text-center py-4 text-muted-foreground",children:"No models match your search"})]})}),!ei&&(0,l.jsx)("div",{className:"px-3 py-2 border-t border-border",children:(0,l.jsxs)("div",{className:"relative flex items-center",children:[(0,l.jsx)(D.A,{className:"absolute left-2.5 h-3.5 w-3.5 text-muted-foreground pointer-events-none"}),(0,l.jsx)("input",{ref:A,type:"text",placeholder:"Search models...",value:k,onChange:e=>_(e.target.value),onKeyDown:e=>{if(e.stopPropagation(),"ArrowDown"===e.key)e.preventDefault(),S(e=>e<es.length-1?e+1:0);else if("ArrowUp"===e.key)e.preventDefault(),S(e=>e>0?e-1:es.length-1);else if("Enter"===e.key&&C>=0){e.preventDefault();let s=es[C];s&&el(s.id)}},className:"w-full h-8 px-8 py-1 rounded-lg text-sm focus:outline-none bg-muted"})]})})]})]}),(0,l.jsx)(Q,{isOpen:O,onClose:ed,onSave:e=>{let s=e.id.trim(),t=s.startsWith("openrouter/")?s.replace("openrouter/",""):s,a=e.label.trim()||(0,U.Il)(t);if(!s)return;if(E.some(e=>e.id===s&&("add"===G||e.id!==V)))return void console.error("A model with this ID already exists");ed();let l={id:s,label:a},n="add"===G?[...E,l]:E.map(e=>e.id===V?l:e);try{localStorage.setItem(U.ve,JSON.stringify(n))}catch(e){console.error("Failed to save custom models to localStorage:",e)}if($(n),u&&u(),"add"===G){i(s);try{localStorage.setItem(U.WY,s)}catch(e){console.warn("Failed to save selected model to localStorage:",e)}}else if(r===V){i(s);try{localStorage.setItem(U.WY,s)}catch(e){console.warn("Failed to save selected model to localStorage:",e)}}y(!1),setTimeout(()=>{S(-1)},0)},initialData:J,mode:G}),f&&(0,l.jsx)(q,{open:!0,onDialogClose:()=>{j(!1),b(null)},title:"Premium Model",description:v?"Subscribe to access ".concat(null==(a=c.find(e=>e.id===v))?void 0:a.label):"Subscribe to access premium models with enhanced capabilities",ctaText:"Subscribe Now",cancelText:"Maybe Later"})]})};var G=t(48470),Y=t(17711),V=t(89613),X=t(77633),H=t(64541),ee=t(68856);function es(e){var s;let{open:t,onOpenChange:i,returnUrl:c=(null==(r=window)||null==(a=r.location)?void 0:a.href)||"/"}=e,{session:d,isLoading:m}=(0,H.A)(),[u,x]=(0,n.useState)(null),[h,p]=(0,n.useState)(!0),[g,f]=(0,n.useState)(null),[j,v]=(0,n.useState)(!1);(0,n.useEffect)(()=>{!async function(){if(t&&!m&&d)try{p(!0);let e=await (0,S.uV)();x(e),f(null)}catch(e){console.error("Failed to get subscription:",e),f(e instanceof Error?e.message:"Failed to load subscription data")}finally{p(!1)}}()},[t,d,m]);let b=async()=>{try{v(!0);let{url:e}=await (0,S.VK)({return_url:c});window.location.href=e}catch(e){console.error("Failed to create portal session:",e),f(e instanceof Error?e.message:"Failed to create portal session")}finally{v(!1)}};return(0,Z.Jn)()?(0,l.jsx)($.lG,{open:t,onOpenChange:i,children:(0,l.jsxs)($.Cf,{className:"max-w-2xl max-h-[80vh] overflow-y-auto",children:[(0,l.jsx)($.c7,{children:(0,l.jsx)($.L3,{children:"Billing & Subscription"})}),(0,l.jsxs)("div",{className:"p-4 bg-muted/30 border border-border rounded-lg text-center",children:[(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:"Running in local development mode - billing features are disabled"}),(0,l.jsx)("p",{className:"text-xs text-muted-foreground mt-2",children:"All premium features are available in this environment"})]})]})}):(0,l.jsx)($.lG,{open:t,onOpenChange:i,children:(0,l.jsxs)($.Cf,{className:"max-w-5xl max-h-[80vh] overflow-y-auto",children:[(0,l.jsx)($.c7,{children:(0,l.jsx)($.L3,{children:"Upgrade Your Plan"})}),h||m?(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)(ee.Skeleton,{className:"h-20 w-full"}),(0,l.jsx)(ee.Skeleton,{className:"h-40 w-full"}),(0,l.jsx)(ee.Skeleton,{className:"h-10 w-full"})]}):g?(0,l.jsx)("div",{className:"p-4 bg-destructive/10 border border-destructive/20 rounded-lg text-center",children:(0,l.jsxs)("p",{className:"text-sm text-destructive",children:["Error loading billing status: ",g]})}):(0,l.jsxs)(l.Fragment,{children:[u&&(0,l.jsx)("div",{className:"mb-6",children:(0,l.jsx)("div",{className:"rounded-lg border bg-background p-4",children:(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsx)("span",{className:"text-sm font-medium text-foreground/90",children:"Agent Usage This Month"}),(0,l.jsxs)("span",{className:"text-sm font-medium",children:["$",(null==(s=u.current_usage)?void 0:s.toFixed(2))||"0"," /"," ","$",u.cost_limit||"0"]})]})})}),(0,l.jsx)(X.c,{returnUrl:c,showTitleAndTabs:!1}),u&&(0,l.jsx)(o.$,{onClick:b,disabled:j,className:"max-w-xs mx-auto w-full bg-primary hover:bg-primary/90 shadow-md hover:shadow-lg transition-all mt-4",children:j?"Loading...":"Manage Subscription"})]})]})})}var et=t(66474),ea=t(71007),er=t(25657),el=t(71539),en=t(57434),ei=t(32919),eo=t(66766);let ec=()=>{let[e,s]=(0,n.useState)(!1);return(0,l.jsxs)(L.rI,{open:e,onOpenChange:s,children:[(0,l.jsx)(L.ty,{asChild:!0,children:(0,l.jsx)(o.$,{variant:"ghost",size:"sm",className:"px-3 py-2 text-sm font-medium hover:bg-accent",style:{borderRadius:"12px"},children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(eo.default,{src:"/kortix-symbol.svg",alt:"Suna",width:16,height:16,className:"h-4 w-4 dark:invert"}),(0,l.jsx)("span",{children:"Suna"}),(0,l.jsx)(et.A,{size:14,className:"opacity-50"})]})})}),(0,l.jsxs)(L.SQ,{align:"end",className:"w-64 p-0 border",sideOffset:4,children:[(0,l.jsxs)(L._2,{className:"flex items-center gap-3 px-4 py-3 cursor-pointer hover:bg-accent border-b m-0",style:{borderRadius:"0"},children:[(0,l.jsx)(ea.A,{size:18}),(0,l.jsxs)("div",{className:"flex flex-col",children:[(0,l.jsx)("span",{className:"font-semibold text-sm",children:"Suna"}),(0,l.jsx)("span",{className:"text-xs text-muted-foreground",children:"Default"})]})]}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsxs)(L._2,{className:"flex items-center gap-3 px-4 py-3 cursor-pointer hover:bg-accent border-b m-0",children:[(0,l.jsx)(er.A,{size:18}),(0,l.jsxs)("div",{className:"flex flex-col",children:[(0,l.jsx)("span",{className:"font-semibold text-sm",children:"Code Assistant"}),(0,l.jsx)("span",{className:"text-xs text-muted-foreground",children:"Help with coding"})]})]}),(0,l.jsxs)(L._2,{className:"flex items-center gap-3 px-4 py-3 cursor-pointer hover:bg-accent border-b m-0",children:[(0,l.jsx)(el.A,{size:18}),(0,l.jsxs)("div",{className:"flex flex-col",children:[(0,l.jsx)("span",{className:"font-semibold text-sm",children:"Quick Writer"}),(0,l.jsx)("span",{className:"text-xs text-muted-foreground",children:"Fast content creation"})]})]}),(0,l.jsxs)(L._2,{className:"flex items-center gap-3 px-4 py-3 cursor-pointer hover:bg-accent m-0",children:[(0,l.jsx)(en.A,{size:18}),(0,l.jsxs)("div",{className:"flex flex-col",children:[(0,l.jsx)("span",{className:"font-semibold text-sm",children:"Document Helper"}),(0,l.jsx)("span",{className:"text-xs text-muted-foreground",children:"Analyze documents"})]})]}),(0,l.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-background via-background/95 to-transparent flex items-end justify-center",children:(0,l.jsx)("div",{className:"w-full p-3",children:(0,l.jsx)("div",{className:"rounded-xl bg-gradient-to-br from-slate-50/80 to-slate-100/70 dark:from-slate-900/40 dark:to-slate-800/30 shadow-sm border border-slate-200/50 dark:border-slate-700/50 p-3",children:(0,l.jsxs)("div",{className:"flex items-center justify-center",children:[(0,l.jsx)(ei.A,{className:"h-4 w-4 text-slate-500 mr-2 flex-shrink-0"}),(0,l.jsx)("p",{className:"text-sm font-medium",children:"Login to explore all agents"})]})})})})]})]})]})},ed=(0,n.forwardRef)((e,s)=>{let{value:t,onChange:a,onSubmit:r,onTranscription:i,placeholder:d,loading:m,disabled:u,isAgentRunning:x,onStopAgent:h,isDraggingOver:g,uploadedFiles:f,fileInputRef:j,isUploading:v,sandboxId:k,setPendingFiles:_,setUploadedFiles:C,setIsUploading:S,hideAttachments:A=!1,messages:L=[],isLoggedIn:P=!0,selectedModel:F,onModelChange:I,modelOptions:T,subscriptionStatus:z,canAccessModel:R,refreshCustomModels:M,selectedAgentId:D,onAgentSelect:U,enableAdvancedConfig:$=!1,hideAgentSelection:O=!1}=e,[q,W]=(0,n.useState)(!1),{enabled:B,loading:J}=(0,Y.u)("custom_agents");return(0,n.useEffect)(()=>{if(!s.current)return;let e=()=>{s.current.style.height="auto";let e=Math.min(Math.max(s.current.scrollHeight,24),200);s.current.style.height="".concat(e,"px")};return e(),e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[t,s]),(0,l.jsxs)("div",{className:"relative flex flex-col w-full h-full gap-2 justify-between",children:[(0,l.jsx)("div",{className:"flex flex-col gap-1 px-2",children:(0,l.jsx)(w.T,{ref:s,value:t,onChange:a,onKeyDown:e=>{"Enter"!==e.key||e.shiftKey||e.nativeEvent.isComposing||(e.preventDefault(),(t.trim()||f.length>0)&&!m&&(!u||x)&&r(e))},placeholder:d,className:(0,N.cn)("w-full bg-transparent dark:bg-transparent border-none shadow-none focus-visible:ring-0 px-0.5 pb-6 pt-4 !text-[15px] min-h-[36px] max-h-[200px] overflow-y-auto resize-none",g?"opacity-40":""),disabled:m||u&&!x,rows:1})}),(0,l.jsxs)("div",{className:"flex items-center justify-between mt-0 mb-1 px-2",children:[(0,l.jsx)("div",{className:"flex items-center gap-3",children:!A&&(0,l.jsx)(b,{ref:j,loading:m,disabled:u,isAgentRunning:x,isUploading:v,sandboxId:k,setPendingFiles:_,setUploadedFiles:C,setIsUploading:S,messages:L,isLoggedIn:P})}),"no_subscription"===z&&!(0,Z.Jn)()&&(0,l.jsx)(V.Bc,{children:(0,l.jsxs)(p.m_,{children:[(0,l.jsx)(V.k$,{children:(0,l.jsx)("p",{role:"button",className:"text-sm text-amber-500 hidden sm:block cursor-pointer",onClick:()=>W(!0),children:"Upgrade for more usage"})}),(0,l.jsx)(p.ZI,{children:(0,l.jsx)("p",{children:"The free tier is severely limited by the amount of usage. Upgrade to experience the full power of Suna."})})]})}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[P?(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[($||B&&!J)&&!O&&(0,l.jsx)(G.b,{selectedAgentId:D,onAgentSelect:U,disabled:m||u&&!x}),(0,l.jsx)(K,{selectedModel:F,onModelChange:I,modelOptions:T,subscriptionStatus:z,canAccessModel:R,refreshCustomModels:M,billingModalOpen:q,setBillingModalOpen:W})]}):(0,l.jsx)(ec,{}),(0,l.jsx)(es,{open:q,onOpenChange:W,returnUrl:window.location.href}),P&&(0,l.jsx)(E,{onTranscription:i,disabled:m||u&&!x}),(0,l.jsx)(o.$,{type:"submit",onClick:x&&h?h:r,size:"sm",className:(0,N.cn)("w-8 h-8 flex-shrink-0 self-end rounded-xl",(t.trim()||0!==f.length||x)&&!m&&(!u||x)?"":"opacity-50"),disabled:!t.trim()&&0===f.length&&!x||m||u&&!x,children:m?(0,l.jsx)(c.A,{className:"h-5 w-5 animate-spin"}):x?(0,l.jsx)("div",{className:"min-h-[14px] min-w-[14px] w-[14px] h-[14px] rounded-sm bg-current"}):(0,l.jsx)(y.A,{className:"h-5 w-5"})})]})]}),"no_subscription"===z&&!(0,Z.Jn)()&&(0,l.jsx)("div",{className:"sm:hidden absolute -bottom-8 left-0 right-0 flex justify-center",children:(0,l.jsx)("p",{className:"text-xs text-amber-500 px-2 py-1",children:"Upgrade for better performance"})})]})});ed.displayName="MessageInput";var em=t(86618),eu=t(30257),ex=t(60760),eh=t(76408),ep=t(66538),eg=t(91981),ef=t(18068);let ej=e=>{var s,t,a,r,l,n,i,o,c,d,m,u,x,h;let p=null==e||null==(s=e.toolResult)?void 0:s.content;if(!p)return null==(o=null==e||null==(t=e.toolResult)?void 0:t.isSuccess)||o;let g=e=>{try{return"string"==typeof e?JSON.parse(e):e}catch(e){return null}},f=g(p);if(!f)return null==(c=null==e||null==(a=e.toolResult)?void 0:a.isSuccess)||c;if(f.content){let e=g(f.content);if((null==e||null==(m=e.tool_execution)||null==(d=m.result)?void 0:d.success)!==void 0)return e.tool_execution.result.success}let j=null!=(x=null!=(u=null==(l=f.tool_execution)||null==(r=l.result)?void 0:r.success)?u:null==(n=f.result)?void 0:n.success)?x:f.success;return void 0!==j?j:null==(h=null==e||null==(i=e.toolResult)?void 0:i.isSuccess)||h},ev=e=>{var s,t;let{toolCalls:a,currentIndex:r,onExpand:i,agentName:c,isVisible:d}=e,[m,u]=n.useState(!1),x=a[r],h=a.length;if(n.useEffect(()=>{d&&u(!1)},[d]),!x||0===h)return null;let p=(null==(s=x.assistantCall)?void 0:s.name)||"Tool Call",g=(0,ef.S8)(p),f=(null==(t=x.toolResult)?void 0:t.content)==="STREAMING",j=!!f||ej(x);return(0,l.jsx)(ex.N,{children:d&&(0,l.jsx)(eh.P.div,{layoutId:"tool-panel-float",layout:!0,transition:{layout:{type:"spring",stiffness:300,damping:30}},className:"-mb-4 w-full",style:{pointerEvents:"auto"},children:(0,l.jsx)(eh.P.div,{layoutId:"tool-panel-content",whileHover:{scale:1.01},whileTap:{scale:.99},className:"bg-card border border-border rounded-3xl p-2 w-full cursor-pointer group",onClick:()=>{u(!0),requestAnimationFrame(()=>{i()})},style:{opacity:+!m},children:(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"flex-shrink-0",children:(0,l.jsx)(eh.P.div,{layoutId:"tool-icon",className:(0,N.cn)("w-10 h-10 rounded-2xl flex items-center justify-center",f?"bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800":j?"bg-green-50 dark:bg-green-900/20 border border-green-300 dark:border-green-800":"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800"),style:{opacity:+!m},children:f?(0,l.jsx)(ep.A,{className:"h-5 w-5 text-blue-500 dark:text-blue-400 animate-spin",style:{opacity:+!m}}):(0,l.jsx)(g,{className:"h-5 w-5 text-foreground",style:{opacity:+!m}})})}),(0,l.jsxs)("div",{className:"flex-1 min-w-0",style:{opacity:+!m},children:[(0,l.jsxs)(eh.P.div,{layoutId:"tool-title",className:"flex items-center gap-2 mb-1",children:[(0,l.jsx)("h4",{className:"text-sm font-medium text-foreground truncate",children:(0,ef.qR)(p)}),(0,l.jsx)("div",{className:"flex items-center gap-1",children:(0,l.jsxs)("span",{className:"text-xs text-muted-foreground",children:[r+1,"/",h]})})]}),(0,l.jsxs)(eh.P.div,{layoutId:"tool-status",className:"flex items-center gap-2",children:[(0,l.jsx)("div",{className:(0,N.cn)("w-2 h-2 rounded-full",f?"bg-blue-500 animate-pulse":j?"bg-green-500":"bg-red-500")}),(0,l.jsx)("span",{className:"text-xs text-muted-foreground truncate",children:f?"".concat(c||"Suna"," is working..."):j?"Success":"Failed"})]})]}),(0,l.jsx)(o.$,{value:"ghost",className:"bg-transparent hover:bg-transparent flex-shrink-0",style:{opacity:+!m},children:(0,l.jsx)(eg.A,{className:"h-4 w-4 text-muted-foreground group-hover:text-foreground transition-colors"})})]})})})})};var eb=t(49376),ew=t(54213),ey=t(40081),eN=t(29911),ek=t(59432),e_=t(17313),eC=t(47330),eS=t(70567),eA=t(84779),eE=t(40538),eL=t(58351),eP=t(38198);let eF=e=>{let{isOpen:s,onOpenChange:t,selectedAgentId:a,onAgentSelect:r,initialTab:i="tools"}=e,[c,d]=(0,n.useState)(i),[u,x]=(0,n.useState)(!1),[h,p]=(0,n.useState)(""),[g,f]=(0,n.useState)(""),[j,v]=(0,n.useState)(""),{data:b,isLoading:y}=(0,eP.fJ)(a||""),N=(0,eP.Ae)(),k=(0,W.useRouter)();n.useEffect(()=>{s&&i&&d(i)},[i,s]),n.useEffect(()=>{b&&(f(b.name||""),v(b.description||""),p(b.system_prompt||""))},[b]);let _=async()=>{if(a)try{await N.mutateAsync({agentId:a,name:g,description:j,system_prompt:h}),m.oR.success("Agent updated successfully"),x(!1)}catch(e){m.oR.error("Failed to update agent")}},C=async e=>{if(a)try{await N.mutateAsync({agentId:a,agentpress_tools:e}),m.oR.success("Tools updated successfully")}catch(e){m.oR.error("Failed to update tools")}};return null==b||b.name,(0,l.jsx)($.lG,{open:s,onOpenChange:t,children:(0,l.jsxs)($.Cf,{className:"max-w-4xl h-[85vh] p-0 flex flex-col",children:[(0,l.jsx)($.c7,{className:"flex-shrink-0 border-b px-6 py-4",children:(0,l.jsxs)($.L3,{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(er.A,{className:"h-4 w-4"}),"Agent Configuration"]}),a&&(0,l.jsxs)(o.$,{variant:"ghost",size:"sm",onClick:()=>k.push("/agents/config/".concat(a)),className:"text-xs",children:[(0,l.jsx)(eC.A,{className:"h-3 w-3 mr-1"}),"Advanced"]})]})}),(0,l.jsx)("div",{className:"flex-shrink-0 px-6 py-3 border-b",children:(0,l.jsx)(G.b,{selectedAgentId:a,onAgentSelect:r})}),(0,l.jsx)("div",{className:"flex-1 min-h-0 px-6 py-4",children:(0,l.jsxs)(e_.tU,{value:c,onValueChange:d,className:"h-full flex flex-col",children:[(0,l.jsxs)(e_.j7,{className:"grid w-full grid-cols-5 flex-shrink-0 h-9 mb-4",children:[(0,l.jsxs)(e_.Xi,{value:"tools",className:"text-xs",children:[(0,l.jsx)(eC.A,{className:"h-3 w-3 mr-1"}),"Tools"]}),(0,l.jsxs)(e_.Xi,{value:"instructions",className:"text-xs",children:[(0,l.jsx)(eb.A,{className:"h-3 w-3 mr-1"}),"Instructions"]}),(0,l.jsxs)(e_.Xi,{value:"knowledge",className:"text-xs",children:[(0,l.jsx)(ew.A,{className:"h-3 w-3 mr-1"}),"Knowledge"]}),(0,l.jsxs)(e_.Xi,{value:"triggers",className:"text-xs",children:[(0,l.jsx)(el.A,{className:"h-3 w-3 mr-1"}),"Triggers"]}),(0,l.jsxs)(e_.Xi,{value:"workflows",className:"text-xs",children:[(0,l.jsx)(ey.A,{className:"h-3 w-3 mr-1"}),"Workflows"]})]}),(0,l.jsx)(e_.av,{value:"tools",className:"flex-1 m-0 mt-0 overflow-y-auto overflow-hidden",children:(0,l.jsx)("div",{className:"h-full",children:a?(0,l.jsx)(eL.b,{tools:(null==b?void 0:b.agentpress_tools)||{},onToolsChange:C}):(0,l.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:"Select an agent to configure tools"})})})}),(0,l.jsx)(e_.av,{value:"instructions",className:"flex-1 m-0 mt-0 overflow-y-auto overflow-hidden",children:(0,l.jsx)("div",{className:"h-full flex flex-col",children:a?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(J.Label,{htmlFor:"agent-name",className:"text-sm",children:"Name"}),(0,l.jsx)(B.p,{id:"agent-name",value:g,onChange:e=>f(e.target.value),placeholder:"Agent name",className:"h-8"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(J.Label,{htmlFor:"agent-description",className:"text-sm",children:"Description"}),(0,l.jsx)(B.p,{id:"agent-description",value:j,onChange:e=>v(e.target.value),placeholder:"Brief description",className:"h-8"})]})]}),(0,l.jsxs)("div",{className:"space-y-2 flex-1 flex flex-col",children:[(0,l.jsx)(J.Label,{htmlFor:"system-instructions",className:"text-sm",children:"System Instructions"}),(0,l.jsx)(w.T,{id:"system-instructions",value:h,onChange:e=>p(e.target.value),placeholder:"Define the agent's role, behavior, and expertise...",className:"flex-1 resize-none"})]}),(0,l.jsxs)("div",{className:"flex gap-2 pt-4",children:[(0,l.jsx)(o.$,{onClick:_,disabled:N.isPending,size:"sm",children:N.isPending?"Saving...":"Save"}),(0,l.jsx)(o.$,{variant:"outline",size:"sm",onClick:()=>{f((null==b?void 0:b.name)||""),v((null==b?void 0:b.description)||""),p((null==b?void 0:b.system_prompt)||"")},children:"Reset"})]})]}):(0,l.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:"Select an agent to configure instructions"})})})}),(0,l.jsx)(e_.av,{value:"knowledge",className:"flex-1 m-0 mt-0 overflow-y-auto overflow-hidden",children:(0,l.jsx)("div",{className:"h-full",children:a?(0,l.jsx)(eE.Y,{agentId:a,agentName:g}):(0,l.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:"Select an agent to manage knowledge base"})})})}),(0,l.jsx)(e_.av,{value:"triggers",className:"flex-1 m-0 mt-0 overflow-y-auto overflow-hidden",children:(0,l.jsx)("div",{className:"h-full",children:a?(0,l.jsx)(eS.b,{agentId:a}):(0,l.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:"Select an agent to configure triggers"})})})}),(0,l.jsx)(e_.av,{value:"workflows",className:"flex-1 m-0 mt-0 overflow-y-auto overflow-hidden",children:(0,l.jsx)("div",{className:"h-full",children:a?(0,l.jsx)(eA.E,{agentId:a,agentName:g}):(0,l.jsx)("div",{className:"flex items-center justify-center h-32",children:(0,l.jsx)("p",{className:"text-sm text-muted-foreground",children:"Select an agent to configure workflows"})})})})]})})]})})};var eI=t(26528);let eT=(0,n.forwardRef)((e,s)=>{let{onSubmit:t,placeholder:a="Describe what you need help with...",loading:r=!1,disabled:o=!1,isAgentRunning:c=!1,onStopAgent:d,autoFocus:m=!0,value:u,onChange:h,onFileBrowse:p,sandboxId:g,hideAttachments:f=!1,selectedAgentId:j,onAgentSelect:b,agentName:w,messages:y=[],bgColor:N="bg-card",toolCalls:k=[],toolCallIndex:_=0,showToolPreview:C=!1,onExpandToolPreview:S,isLoggedIn:A=!0,enableAdvancedConfig:E=!1,onConfigureAgent:L,hideAgentSelection:P=!1}=e,F=void 0!==u&&void 0!==h,[I,T]=(0,n.useState)(""),z=F?u:I,[R,M]=(0,n.useState)([]),[D,O]=(0,n.useState)([]),[q,W]=(0,n.useState)(!1),[Z,B]=(0,n.useState)(!1),[J,Q]=(0,n.useState)(!1),[K,G]=(0,n.useState)("integrations"),[Y,V]=(0,n.useState)(!1),{selectedModel:X,setSelectedModel:H,subscriptionStatus:ee,allModels:es,canAccessModel:et,getActualModelId:ea,refreshCustomModels:er}=(0,U.xE)(),en=(0,eu.np)(),ei=(0,x.jE)(),eo=(0,n.useRef)(null),ec=(0,n.useRef)(null),ex=(0,n.useRef)(!1);(0,n.useImperativeHandle)(s,()=>({getPendingFiles:()=>D,clearPendingFiles:()=>O([])})),(0,n.useEffect)(()=>{if(b&&!ex.current){let e=new URLSearchParams(window.location.search).has("agent_id");if(j||e)console.log("Skipping localStorage load:",{hasSelectedAgent:!!j,hasAgentIdInUrl:e,selectedAgentId:j});else{let e=localStorage.getItem("lastSelectedAgentId");e?(console.log("Loading saved agent from localStorage:",e),b("suna"===e?void 0:e)):console.log("No saved agent found in localStorage")}ex.current=!0}},[b,j]),(0,n.useEffect)(()=>{{let e=void 0===j?"suna":j;console.log("Saving selected agent to localStorage:",e),localStorage.setItem("lastSelectedAgentId",e)}},[j]),(0,n.useEffect)(()=>{m&&eo.current&&eo.current.focus()},[m]);let eh=async e=>{if(e.preventDefault(),!z.trim()&&0===R.length||r||o&&!c)return;if(c&&d)return void d();let s=z;if(R.length>0){let e=R.map(e=>"[Uploaded File: ".concat(e.path,"]")).join("\n");s=s?"".concat(s,"\n\n").concat(e):e}let a=ea(X),l=!1;X.endsWith("-thinking")&&(a=ea(X.replace(/-thinking$/,"")),l=!0),t(s,{model_name:a,enable_thinking:l}),F||T(""),M([])};return(0,l.jsxs)("div",{className:"mx-auto w-full max-w-4xl",children:[(0,l.jsx)(ev,{toolCalls:k,currentIndex:_,onExpand:S||(()=>{}),agentName:w,isVisible:C}),(0,l.jsx)(i.Zp,{className:"-mb-2 shadow-none w-full max-w-4xl mx-auto bg-transparent border-none overflow-hidden ".concat(E&&j?"":"rounded-3xl"),onDragOver:e=>{e.preventDefault(),e.stopPropagation(),B(!0)},onDragLeave:e=>{e.preventDefault(),e.stopPropagation(),B(!1)},onDrop:e=>{e.preventDefault(),e.stopPropagation(),B(!1),ec.current&&e.dataTransfer.files.length>0&&v(Array.from(e.dataTransfer.files),g,O,M,W,y,ei)},children:(0,l.jsxs)("div",{className:"w-full text-sm flex flex-col justify-between items-start rounded-lg",children:[(0,l.jsxs)(i.Wu,{className:"w-full p-1.5 ".concat(E&&j?"pb-1":"pb-2"," ").concat(N," border ").concat(E&&j?"rounded-t-3xl":"rounded-3xl"),children:[(0,l.jsx)(em.X,{files:R||[],sandboxId:g,onRemove:e=>{let s=R[e];s.localUrl&&URL.revokeObjectURL(s.localUrl),M(s=>s.filter((s,t)=>t!==e)),!g&&D.length>e&&O(s=>s.filter((s,t)=>t!==e));let t=y.some(e=>("string"==typeof e.content?e.content:"").includes("[Uploaded File: ".concat(s.path,"]")));g&&s.path&&!t?en.mutate({sandboxId:g,filePath:s.path},{onError:e=>{console.error("Failed to delete file from server:",e)}}):t&&console.log("Skipping server deletion for ".concat(s.path," - file is referenced in chat history"))},layout:"inline",maxHeight:"216px",showPreviews:!0}),(0,l.jsx)(ed,{ref:eo,value:z,onChange:e=>{let s=e.target.value;F?h(s):T(s)},onSubmit:eh,onTranscription:e=>{let s=F?u:I,t=s?"".concat(s," ").concat(e):e;F?h(t):T(t)},placeholder:a,loading:r,disabled:o,isAgentRunning:c,onStopAgent:d,isDraggingOver:Z,uploadedFiles:R,fileInputRef:ec,isUploading:q,sandboxId:g,setPendingFiles:O,setUploadedFiles:M,setIsUploading:W,hideAttachments:f,messages:y,selectedModel:X,onModelChange:H,modelOptions:es,subscriptionStatus:ee,canAccessModel:et,refreshCustomModels:er,isLoggedIn:A,selectedAgentId:j,onAgentSelect:b,hideAgentSelection:P})]}),E&&j&&(0,l.jsx)("div",{className:"w-full border-t border-border/30 bg-muted/20 px-4 py-1.5 rounded-b-3xl border-l border-r border-b border-border",children:(0,l.jsx)("div",{className:"flex items-center justify-center",children:(0,l.jsxs)("div",{className:"flex items-center gap-1 sm:gap-2 overflow-x-auto scrollbar-none",children:[(0,l.jsxs)("button",{onClick:()=>V(!0),className:"flex items-center gap-1.5 text-muted-foreground hover:text-foreground transition-all duration-200 px-2.5 py-1.5 rounded-md hover:bg-muted/50 border border-transparent hover:border-border/30 flex-shrink-0",children:[(0,l.jsxs)("div",{className:"flex items-center -space-x-0.5",children:[(0,l.jsx)("div",{className:"w-5 h-5 bg-white dark:bg-muted border border-border rounded-full flex items-center justify-center shadow-sm",children:(0,l.jsx)(eN.DSS,{className:"w-3 h-3"})}),(0,l.jsx)("div",{className:"w-5 h-5 bg-white dark:bg-muted border border-border rounded-full flex items-center justify-center shadow-sm",children:(0,l.jsx)(eN.O4U,{className:"w-3 h-3"})}),(0,l.jsx)("div",{className:"w-5 h-5 bg-white dark:bg-muted border border-border rounded-full flex items-center justify-center shadow-sm",children:(0,l.jsx)(ek.i4$,{className:"w-3 h-3"})})]}),(0,l.jsx)("span",{className:"text-xs font-medium",children:"Integrations"})]}),(0,l.jsx)("div",{className:"w-px h-4 bg-border/60"}),(0,l.jsxs)("button",{onClick:()=>{G("instructions"),Q(!0)},className:"flex items-center gap-1.5 text-muted-foreground hover:text-foreground transition-all duration-200 px-2.5 py-1.5 rounded-md hover:bg-muted/50 border border-transparent hover:border-border/30 flex-shrink-0",children:[(0,l.jsx)(eb.A,{className:"h-3.5 w-3.5 flex-shrink-0"}),(0,l.jsx)("span",{className:"text-xs font-medium",children:"Instructions"})]}),(0,l.jsx)("div",{className:"w-px h-4 bg-border/60"}),(0,l.jsxs)("button",{onClick:()=>{G("knowledge"),Q(!0)},className:"flex items-center gap-1.5 text-muted-foreground hover:text-foreground transition-all duration-200 px-2.5 py-1.5 rounded-md hover:bg-muted/50 border border-transparent hover:border-border/30 flex-shrink-0",children:[(0,l.jsx)(ew.A,{className:"h-3.5 w-3.5 flex-shrink-0"}),(0,l.jsx)("span",{className:"text-xs font-medium",children:"Knowledge"})]}),(0,l.jsx)("div",{className:"w-px h-4 bg-border/60"}),(0,l.jsxs)("button",{onClick:()=>{G("triggers"),Q(!0)},className:"flex items-center gap-1.5 text-muted-foreground hover:text-foreground transition-all duration-200 px-2.5 py-1.5 rounded-md hover:bg-muted/50 border border-transparent hover:border-border/30 flex-shrink-0",children:[(0,l.jsx)(el.A,{className:"h-3.5 w-3.5 flex-shrink-0"}),(0,l.jsx)("span",{className:"text-xs font-medium",children:"Triggers"})]}),(0,l.jsx)("div",{className:"w-px h-4 bg-border/60"}),(0,l.jsxs)("button",{onClick:()=>{G("workflows"),Q(!0)},className:"flex items-center gap-1.5 text-muted-foreground hover:text-foreground transition-all duration-200 px-2.5 py-1.5 rounded-md hover:bg-muted/50 border border-transparent hover:border-border/30 flex-shrink-0",children:[(0,l.jsx)(ey.A,{className:"h-3.5 w-3.5 flex-shrink-0"}),(0,l.jsx)("span",{className:"text-xs font-medium",children:"Workflows"})]})]})})})]})}),(0,l.jsx)(eF,{isOpen:J,onOpenChange:Q,selectedAgentId:j,onAgentSelect:b,initialTab:K}),(0,l.jsx)($.lG,{open:Y,onOpenChange:V,children:(0,l.jsxs)($.Cf,{className:"p-0 max-w-6xl max-h-[90vh] overflow-y-auto",children:[(0,l.jsx)($.c7,{className:"sr-only",children:(0,l.jsx)($.L3,{children:"Integrations"})}),(0,l.jsx)(eI.Z,{showAgentSelector:!0,selectedAgentId:j,onAgentChange:b,onToolsSelected:(e,s,t,a)=>{console.log("Tools selected:",{profileId:e,selectedTools:s,appName:t,appSlug:a})}})]})})]})});eT.displayName="ChatInput"},84779:(e,s,t)=>{t.d(s,{E:()=>y});var a=t(95155),r=t(12115),l=t(35695),n=t(84616),i=t(85339),o=t(40081),c=t(85690),d=t(82178),m=t(62525),u=t(30285),x=t(66695),h=t(26126),p=t(54165),g=t(90010),f=t(85057),j=t(88539),v=t(17313),b=t(56671),w=t(36742);function y(e){let{agentId:s,agentName:t}=e,y=(0,l.useRouter)(),{data:N=[],isLoading:k}=(0,w.X3)(s),_=(0,w.Ci)(),C=(0,w.Vb)(),S=(0,w.Fo)(),A=(0,w.AM)(),[E,L]=(0,r.useState)(!1),[P,F]=(0,r.useState)(null),[I,T]=(0,r.useState)(!1),[z,R]=(0,r.useState)(null),[M,D]=(0,r.useState)("workflows"),[U,$]=(0,r.useState)(""),O=(0,r.useCallback)(async()=>{try{let e=await _.mutateAsync({agentId:s,workflow:{name:"Untitled Workflow",description:"A new workflow",steps:[]}});y.push("/agents/config/".concat(s,"/workflow/").concat(e.id))}catch(e){b.oR.error("Failed to create workflow")}},[s,y,_]),q=(0,r.useCallback)(async(e,t)=>{await C.mutateAsync({agentId:s,workflowId:e,workflow:{status:t}})},[s,C]),W=(0,r.useCallback)(e=>{F(e),L(!0)},[]),Z=(0,r.useCallback)(e=>{y.push("/agents/config/".concat(s,"/workflow/").concat(e))},[s,y]),B=(0,r.useCallback)(e=>{R(e),T(!0)},[]),J=(0,r.useCallback)(async()=>{if(z)try{await S.mutateAsync({agentId:s,workflowId:z.id}),b.oR.success("Workflow deleted successfully"),T(!1),R(null)}catch(e){b.oR.error("Failed to delete workflow")}},[s,z,S]),Q=(0,r.useCallback)(async()=>{if(P)try{let e=await A.mutateAsync({agentId:s,workflowId:P.id,execution:{input_data:U.trim()?{prompt:U}:void 0}});L(!1),F(null),$(""),b.oR.success("".concat(e.message,". Thread ID: ").concat(e.thread_id),{action:e.thread_id?{label:"View Execution",onClick:()=>{window.open("/thread/".concat(e.thread_id),"_blank")}}:void 0,duration:1e4})}catch(e){b.oR.error("Failed to execute workflow")}},[s,P,U,A]),K=e=>(0,a.jsx)(h.E,{className:{draft:"text-gray-700 bg-gray-100",active:"text-green-700 bg-green-100",paused:"text-yellow-700 bg-yellow-100",archived:"text-red-700 bg-red-100"}[e],children:e.charAt(0).toUpperCase()+e.slice(1)});return(0,a.jsxs)("div",{className:"h-full flex flex-col",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mb-4",children:(0,a.jsxs)(u.$,{size:"sm",variant:"outline",className:"flex items-center gap-2",onClick:O,disabled:_.isPending,children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),_.isPending?"Creating...":"Create Workflow"]})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto",children:(0,a.jsx)(v.tU,{value:M,onValueChange:D,className:"w-full",children:(0,a.jsx)(v.av,{value:"workflows",className:"space-y-4",children:k?(0,a.jsx)("div",{className:"flex items-center justify-center p-8",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(i.A,{className:"h-5 w-5 animate-spin"}),(0,a.jsx)("span",{children:"Loading workflows..."})]})}):0===N.length?(0,a.jsxs)("div",{className:"text-center py-12 px-6 bg-muted/30 rounded-xl border-2 border-dashed border-border",children:[(0,a.jsx)("div",{className:"mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-4 border",children:(0,a.jsx)(o.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,a.jsx)("h3",{className:"text-sm font-semibold mb-2",children:"No Agent Workflows"}),(0,a.jsx)("p",{className:"text-muted-foreground mb-6 max-w-sm mx-auto",children:"Create workflows to automate tasks and streamline your agent's operations."})]}):(0,a.jsx)("div",{className:"grid gap-4",children:N.map(e=>(0,a.jsx)(x.Zp,{className:"p-4 cursor-pointer hover:opacity-80 transition-colors",onClick:()=>Z(e.id),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("h4",{className:"font-semibold",children:e.name}),K(e.status),e.is_default&&(0,a.jsx)(h.E,{variant:"outline",children:"Default"})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:e.description}),e.trigger_phrase&&(0,a.jsxs)("p",{className:"text-xs text-muted-foreground mt-1",children:['Trigger: "',e.trigger_phrase,'"']}),(0,a.jsxs)("div",{className:"flex items-center gap-4 mt-2",children:[(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:[e.steps.length," steps"]}),(0,a.jsxs)("span",{className:"text-xs text-muted-foreground",children:["Created ",new Date(e.created_at).toLocaleDateString()]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)(u.$,{variant:"ghost",size:"sm",onClick:s=>{s.stopPropagation(),W(e)},disabled:"active"!==e.status||A.isPending,children:[(0,a.jsx)(c.A,{className:"h-4 w-4"}),"Execute"]}),(0,a.jsx)(u.$,{variant:"ghost",size:"sm",onClick:s=>{s.stopPropagation(),q(e.id,"active"===e.status?"paused":"active")},disabled:C.isPending,children:"active"===e.status?(0,a.jsx)(d.A,{className:"h-4 w-4"}):(0,a.jsx)(c.A,{className:"h-4 w-4"})}),(0,a.jsx)(u.$,{variant:"ghost",size:"sm",onClick:s=>{s.stopPropagation(),B(e)},disabled:S.isPending,className:"text-red-600 hover:text-red-700 hover:bg-red-50",children:(0,a.jsx)(m.A,{className:"h-4 w-4"})})]})]})},e.id))})})})}),(0,a.jsx)(p.lG,{open:E,onOpenChange:L,children:(0,a.jsxs)(p.Cf,{className:"max-w-md",children:[(0,a.jsxs)(p.c7,{children:[(0,a.jsx)(p.L3,{children:"Execute Workflow"}),(0,a.jsxs)(p.rr,{children:['Provide input data for "',null==P?void 0:P.name,'" workflow']})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(f.Label,{children:"What would you like the workflow to work on?"}),(0,a.jsx)(j.T,{value:U,onChange:e=>$(e.target.value),placeholder:"Enter your request...",rows:4,className:"resize-none",required:!0})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between pt-4",children:[(0,a.jsx)(u.$,{variant:"outline",onClick:()=>L(!1),children:"Cancel"}),(0,a.jsx)(u.$,{onClick:Q,disabled:A.isPending,children:A.isPending?"Executing...":"Execute Workflow"})]})]})]})}),(0,a.jsx)(g.Lt,{open:I,onOpenChange:T,children:(0,a.jsxs)(g.EO,{children:[(0,a.jsxs)(g.wd,{children:[(0,a.jsx)(g.r7,{children:"Delete Workflow"}),(0,a.jsxs)(g.$v,{children:["Are you sure you want to delete workflow ",null==z?void 0:z.name,"? This action cannot be undone."]})]}),(0,a.jsxs)(g.ck,{children:[(0,a.jsx)(g.Zr,{children:"Cancel"}),(0,a.jsx)(g.Rx,{onClick:J,className:"bg-red-600 hover:bg-red-700",disabled:S.isPending,children:S.isPending?"Deleting...":"Delete"})]})]})})]})}},88539:(e,s,t)=>{t.d(s,{T:()=>l});var a=t(95155);t(12115);var r=t(59434);function l(e){let{className:s,...t}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",s),...t})}}}]);